# # Protect the site by password (basic HTTP authentication)
# # Predefined user: test, password: test
# #
# # To create other users see http://www.htaccesstools.com/htpasswd-generator/ 
# # or use command line: htpasswd -b .htpasswd test test (supposing .htpasswd file is on actual path)
# #
# AuthName "Authentication"
# # !!! Update the path here (search for SCRIPT_FILENAME in phpinfo):
# AuthUserFile /var/www/clients/client18/web197/web/.htpasswd
# AuthType Basic
# # The word 'user' here below is important. Authentication does not work without it
# require user test

# Serve resources with the proper media types (f.k.a. MIME types).
#
# https://www.iana.org/assignments/media-types/media-types.xhtml
# https://httpd.apache.org/docs/current/mod/mod_mime.html#addtype 
<IfModule mod_mime.c>
  # Data interchange
    AddType application/atom+xml                        atom
    AddType application/json                            json map topojson
    AddType application/ld+json                         jsonld
    AddType application/rss+xml                         rss
    AddType application/vnd.geo+json                    geojson
    AddType application/xml                             rdf xml

  # JavaScript
    # Normalize to standard type.
    # https://tools.ietf.org/html/rfc4329#section-7.2
    AddType application/javascript                      js

  # Manifest files
    AddType application/manifest+json                   webmanifest
    AddType application/x-web-app-manifest+json         webapp
    AddType text/cache-manifest                         appcache

  # Media files
    AddType audio/mp4                                   f4a f4b m4a
    AddType audio/ogg                                   oga ogg opus
    AddType image/bmp                                   bmp
    AddType image/svg+xml                               svg svgz
    AddType image/webp                                  webp
    AddType video/mp4                                   f4v f4p m4v mp4
    AddType video/ogg                                   ogv
    AddType video/webm                                  webm
    AddType video/x-flv                                 flv

    # Serving `.ico` image files with a different media type
    # prevents Internet Explorer from displaying then as images:
    # https://github.com/h5bp/html5-boilerplate/commit/37b5fec090d00f38de64b591bcddcb205aadf8ee
    AddType image/x-icon                                cur ico


  # Web fonts
    AddType application/font-woff                       woff
    AddType application/font-woff2                      woff2
    AddType application/vnd.ms-fontobject               eot

    # Browsers usually ignore the font media types and simply sniff
    # the bytes to figure out the font type.
    # https://mimesniff.spec.whatwg.org/#matching-a-font-type-pattern
    #
    # However, Blink and WebKit based browsers will show a warning
    # in the console if the following font types are served with any
    # other media types.
    AddType application/x-font-ttf                      ttc ttf
    AddType font/opentype                               otf

  # Other
    AddType application/octet-stream                    safariextz
    AddType application/x-bb-appworld                   bbaw
    AddType application/x-chrome-extension              crx
    AddType application/x-opera-extension               oex
    AddType application/x-xpinstall                     xpi
    AddType text/vcard                                  vcard vcf
    AddType text/vnd.rim.location.xloc                  xloc
    AddType text/vtt                                    vtt
    AddType text/x-component                            htc
</IfModule>

<IfModule mod_rewrite.c>
    # see:
    # - http://php.net/manual/en/reserved.variables.server.php
    # - http://www.askapache.com/htaccess/mod_rewrite-variables-cheatsheet.html
    # - http://httpd.apache.org/docs/2.4/rewrite/flags.html#flag_end

    RewriteEngine on
    RewriteBase /

#    # In case of following subdomains ignore rewrite rules defined in the rest of this file.
#    # (If the subdomain host matches condition then this is the last [L] rule to be done
#    # and it does nothing => there is no rewrite in this file applied to URLs of subdomain)
#    RewriteCond %{HTTP_HOST} ^sub\.mysite\.com [NC]
#    RewriteRule ^ - [L]

    # Set %{ENV:ON_LOCALHOST} variable, to allow identify localhost request by single condition
    # `RewriteCond %{ENV:ON_LOCALHOST} 1` or `RewriteCond %{ENV:ON_LOCALHOST} !1` in rewrite rules here below
    RewriteCond %{REMOTE_ADDR} ^127\. [OR]
    RewriteCond %{ENV:IN_DOCKER} ^onLocalhost$ [NC]
    RewriteRule ^ - [ENV=ON_LOCALHOST:1]

    # Set %{ENV:PROTO} variable, to allow rewrites to redirect with the appropriate 
    # schema automatically (http or https).
    RewriteCond %{HTTPS} =on
    RewriteRule ^ - [env=proto:https]
    RewriteCond %{HTTPS} !=on
    RewriteRule ^ - [env=proto:http]

    # Out of service (if site is e.g. under maintenance, then return for any request maintenance.php)
    #
    #RewriteRule    ^$ maintenance.php    [L]
    #RewriteRule    (.*) maintenance.php [L]

    # !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    # !!! ATTENTION: Redirects remove POST data so use them only for GET requests !!!
    # !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    # !!! ATTENTION: There is a BUG in Apache 2.2 -2.3 so it is not possible      !!!
    # !!! to turn off query parameters (re)encoding (which should be normally done!!!
    # !!! by NE parameter). What is why if the QUERY_STRING contains % (this is   !!!
    # !!! the most critical character) the redirection rules below are not        !!!
    # !!! applied. This problem occures also in nonredirectional rewrites but     !!!
    # !!! where is no chance to skip them so they are processed despite this bug. !!!
    # !!! See: http://serverfault.com/questions/331899                            !!!
    # !!! See: https://bz.apache.org/bugzilla/show_bug.cgi?id=34602               !!!
    # !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

    # NOTE: If URL is 'http://www.my-site.com/my-slug1/my-slug2/?q=2#my-hash' then the rule RewriteRule ^(.*)$ $1
    # will grab 'my-slug1/my-slug2/' (and provide it as $1). It means that LEADING SLASH IS OMITTED
    # and trailing slash (if any in URL) is included.

    # NOTE: REQUEST_FILENAME contains only existing part of path, so on request my.sk/x/y/file.txt?p=2
    # you can get REQUEST_FILENAME = /var/www/my_sk/x if y/file.txt does not exist.

    # NOTE: REQUEST_URI does not contain absolute server path BUT always contain the 
    # the required file, on request my.sk/x/y/file.txt?p=2 you get REQUEST_URI = /x/y/file.txt

    # FORCE HTTPS: Redirect http://... to https://... but not on localhost
    # ATTENTION: If you decide to use HTTPS site-widely by this rewrite rule 
    # then be sure that application HTTP/HTTPS redirecting is off, it means that 
    # config 'allowSwitchBetweenHttpAndHttps' is set to FALSE
    RewriteCond %{HTTPS} !=on
    RewriteCond %{REQUEST_METHOD} ^GET$ [NC]
    # remove line below on >= Apache 2.4 (if the bug described here above is resolved)
    RewriteCond %{QUERY_STRING} !\% [NC]
    RewriteCond %{ENV:ON_LOCALHOST} !1
    RewriteRule ^(.*)$ https://%{HTTP_HOST}/$1 [QSA,NC,L,NE,R=301]

#    # URL CANONICALIZATION: Redirect secondary domains pointing to this project
#    # to the primary project domain. Use already the proper form (www/nonwww) to avoid 
#    # consecutive redirections
#    # !!! UPDATE 'primary-domain.com' here
#    RewriteCond %{HTTP_HOST} !primary-domain.com$ [NC] 
#    RewriteCond %{ENV:ON_LOCALHOST} !1
#    # !!! UPDATE 'www.primary-domain.com' here
#    RewriteRule ^(.*)$ %{ENV:PROTO}://www.primary-domain.com/$1 [R=301,L]

     # URL CANONICALIZATION: Redirect http(s)://my-site.com to http(s)://www.my-site.com but not on localhost or subdomain
     # The reasons to use www. see here: http://www.yes-www.org/why-use-www/
     RewriteCond %{REQUEST_METHOD} ^GET$ [NC]
     # remove line below on >= Apache 2.4 (if the bug described here above is resolved)
     RewriteCond %{QUERY_STRING} !\% [NC]
     RewriteCond %{HTTP_HOST} !^www\. [NC]
     RewriteCond %{ENV:ON_LOCALHOST} !1
     RewriteCond %{HTTP_HOST} !\.[^\.]*\. [NC]
     RewriteRule ^(.*)$ %{ENV:PROTO}://www.%{HTTP_HOST}/$1 [QSA,NC,L,NE,R=301]

#   # URL CANONICALIZATION: Redirect http(s)://www.my-site.com to http(s)://my-site.com
#   RewriteCond %{REQUEST_METHOD} ^GET$ [NC]
#   # remove line below on >= Apache 2.4 (if the bug described here above is resolved)
#   RewriteCond %{QUERY_STRING} !\% [NC]
#   RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
#   RewriteRule ^(.*)$ %{ENV:PROTO}://%1/$1 [QSA,NC,L,NE,R=301]

    # URL CANONICALIZATION: Redirect /index.php[/x/y/z] to /[x/y/z]
    RewriteCond %{REQUEST_METHOD} ^GET$ [NC]
    RewriteCond %{QUERY_STRING} !_urlPath_= [NC]
    # remove line below on >= Apache 2.4 (if the bug described here above is resolved)
    RewriteCond %{QUERY_STRING} !\% [NC]
    RewriteRule ^index.php$ / [QSA,NC,L,NE,R=301]
    RewriteCond %{REQUEST_METHOD} ^GET$ [NC]
    RewriteCond %{QUERY_STRING} !_urlPath_= [NC]
    # remove line below on >= Apache 2.4 (if the bug described here above is resolved)
    RewriteCond %{QUERY_STRING} !\% [NC]
    RewriteRule ^index.php/(.+)$ /$1 [QSA,NC,L,NE,R=301]

    # URL CANONICALIZATION: Remove trailing slash from URL paths, http://my-site.com/my-slug/?q=2 is redirected to http://my-site.com/my-slug?q=2
    # NOTE: This is BETTER alternative comparing to adding trailing slash because for Google both possibilities equals
    # if you keep unique approach (see http://googlewebmastercentral.blogspot.sk/2010/04/to-slash-or-not-to-slash.html)
    # and for development it is simpler to not add trailing slashes
    # ATTENTION: If you change this then update in app config 'urlPathTrailingSlash' accordingly
    RewriteCond %{REQUEST_METHOD} ^GET$ [NC]
    # remove line below on >= Apache 2.4 (if the bug described here above is resolved)
    RewriteCond %{QUERY_STRING} !\% [NC]
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} /$ [NC]
    RewriteRule ^(.*)/$ /$1 [QSA,NC,L,NE,R=301]

#    # URL CANONICALIZATION: Add trailing slash to URL paths, http://my-site.com/my-slug?q=2 is redirected to http://my-site.com/my-slug/?q=2
#    # ATTENTION: If you change this then update in app config 'urlPathTrailingSlash' accordingly
#    RewriteCond %{REQUEST_METHOD} ^GET$ [NC]
#    # remove line below on >= Apache 2.4 (if the bug described here above is resolved)
#    RewriteCond %{QUERY_STRING} !\% [NC]
#    RewriteCond %{REQUEST_FILENAME} !-d
#    RewriteCond %{REQUEST_FILENAME} !-f
#    RewriteCond %{REQUEST_URI} !/$ [NC]
#    RewriteCond %{REQUEST_URI} !\.[a-z]{2,4}$ [NC]
#    RewriteRule ^(.*)$ /$1/ [QSA,NC,L,NE,R=301]

#    # SEO REDIRECTS 1: Use trailing/notrailing slash according the above settings
#    # To convert list of urls use for search this regex: http://[^/]+/(\S+)\s+http://[^/]+(\S+)$
#    # and for replace this this expression: RewriteRule ^\1$ \2 [QSA,NC,L,NE,R=301]

    # SEO REDIRECTS 2: requests for unexisting .php files redirect to home (applies 
    # mostly after replacing old site by new one)
    #
    # if not request for existing file
    RewriteCond %{REQUEST_FILENAME} !-f
    # if not request launched by restrictions below
    RewriteCond %{REQUEST_URI} !^/_htaccess_has_restricted_access_to_ [NC]
    # with .php extension
    RewriteRule \.php$ / [QSA,NC,L,NE,R=301]

    # SPECIAL URLS: Treat some special cases:
    #
    # call robots screen
    RewriteRule ^robots.txt$ index.php?_urlPath_=/_robots [QSA,NC,L,NE]
    #
    # call pwa screens
    RewriteRule ^pwa-manifest.json$ index.php?_urlPath_=/_pwaManifest [QSA,NC,L,NE]
    RewriteRule ^pwa-service-worker.js$ index.php?_urlPath_=/_pwaServiceWorker [QSA,NC,L,NE]
    #
    # call sitemap screen
#    RewriteRule ^sitemap.xml$ index.php?_urlPath_=/_sitemap [QSA,NC,L,NE]
    #
    # call image sitemap screen
    # RewriteRule ^image-sitemap.xml$ index.php?_urlPath_=/_imageSitemap [QSA,NC,L,NE]

    # RESTRICT PHP FILES DIRECT ACCESS:
    # 
    # if existing file
    RewriteCond %{REQUEST_FILENAME} -f
    # with valid php extension (.php, .php3, .php4, .php5, .phtml, ...?) 
    # or hidden php files (badScript.php.123, see Case 4 on http://www.acunetix.com/websitesecurity/upload-forms-threat/)
    RewriteCond %{REQUEST_URI} \.(?:phtml|php.*)$ [NC]
    # and not one of folowing EXCEPTIONS:
    # - file index.php
    RewriteCond %{REQUEST_URI} !^/index\.php$ [NC]
    # - file under /app/vendors/
    RewriteCond %{REQUEST_URI} !^/app/vendors/.+?\.php$ [NC]
    # - file under /app/modules/???/vendors/ (e.g. ckfinder access directly connector.php or phpthumb.php)
    RewriteCond %{REQUEST_URI} !^/app/modules/[^/]+/vendors/.+?\.php$ [NC]
    # - or one of following files:
#    RewriteCond %{REQUEST_URI} !^/app/config/install\.php$ [NC]
#    RewriteCond %{REQUEST_URI} !^/phpinfo\.php$ [NC]
    # then let server to return 404 error by requesting nonexisting file
    RewriteRule ^(.+)$ _htaccess_has_restricted_access_to_file_$1 [QSA,NC,L,NE]

    # RESTRICT DIRETORIES ACCESS:
    # 
    # if a directory
    RewriteCond %{REQUEST_FILENAME} -d
    # then let server to return 404 error by requesting nonexisting file
    RewriteRule ^(.+)$ _htaccess_has_restricted_access_to_directory_$1 [QSA,NC,L,NE]

    # USE REMOTE USERFILES
    # ATTENTION: !!!This breaks functionality of section TREAT LAZY VARIANT FILES on localhost!!!
    # ATTENTION: To make this work create at least empty userfiles/ folder!
    # ATTENTION: If access to remote site is protected by password then you must be logged there!
    # 
    # if not request for existing file
    RewriteCond %{REQUEST_FILENAME} !-f
    # if on localhost
    RewriteCond %{ENV:ON_LOCALHOST} 1
    # if request for file in userfiles
    RewriteCond %{REQUEST_URI} ^/userfiles/ [NC]
    # then use remote file. Use http in URL (not https) to make remote address 
    # functional even if https is not yet available!
    RewriteRule ^(.*)$ https://unisport-kovac.run.sk/$1 [L]

    # TREAT LAZY VARIANT FILES
    # ATTENTION: !!!To make this work on localhost the section USE REMOTE USERFILES must be commented out!!!
    #
    # if not existing file
    RewriteCond %{REQUEST_FILENAME} !-f
    # but seems to be request for a picture
    RewriteCond %{REQUEST_URI} \.(?:jpg|jpeg|png|gif|bmp)$ [NC]
    # and it has awaited path structure - see phpDoc for Model::getFileFieldOptions()
    RewriteCond %{REQUEST_URI} /[^/]+/[^/]+/[^/]+/[^/]+/[^/]+ [NC]
    # then grab url and send it on input of _getImage screen
    RewriteRule ^(.+)$ index.php?_urlPath_=/_getImage&_i_=/$1 [QSA,NC,L,NE] 

    # Grab the url and set it as GET param ($GET['_urlPath_']) of index.php
    # NOTE: $_SERVER['PATH_INFO'] does not work on all servers
    #
    # if not request for directory
    RewriteCond %{REQUEST_FILENAME} !-d
    # if not request for existing file
    RewriteCond %{REQUEST_FILENAME} !-f
    # if not request for any kind of file (= sometning with extension) even nonexisting
    # but ignore this conditions if it is mvc request similar to file (e.g. http://my-site.com/mvc/App/Users/<USER>/<EMAIL>)
    # NOTE: The OR is applied locally so the overal condition looks like: !d AND !-f AND (!\.[a-z]{2,4}$ OR ^/mvc/[^\/]+/[^\/]+/[^\/]+/) AND !^/_htaccess_has_restricted_access_to_
    RewriteCond %{REQUEST_URI} !\.[a-z]{2,4}$ [NC,OR]
    RewriteCond %{REQUEST_URI} ^/mvc/[^/]+/[^/]+/[^/]+/ [NC]
    # if not request launched by restrictions above
    RewriteCond %{REQUEST_URI} !^/_htaccess_has_restricted_access_to_ [NC]
    # then grab url and go to index.php
    RewriteRule ^(.+)$ index.php?_urlPath_=/$1 [QSA,NC,L,NE] 
    # debug (see http://httpd.apache.org/docs/current/mod/mod_rewrite.html for all possible variables):
#    RewriteRule ^(.+)$ index.php?_urlPath_=/$1&_htaccess[REQUEST_FILENAME]=%{REQUEST_FILENAME}&_htaccess[REQUEST_URI]=%{REQUEST_URI}&_htaccess[DOCUMENT_ROOT]=%{DOCUMENT_ROOT}&_htaccess[PATH_INFO]=%{PATH_INFO}&_htaccess[HTTPS]=%{HTTPS}&_htaccess[PROTO]=%{ENV:PROTO} [QSA,NC,L,NE]

</IfModule>

# Prevent Apache from adding a trailing footer line containing
# information about the server to the server-generated documents
# (e.g.: error messages, directory listings, etc.)
#
# https://httpd.apache.org/docs/current/mod/core.html#serversignature

ServerSignature Off

# deflate css and js files
<IfModule mod_deflate.c>
    <FilesMatch "\.(js|css)$">
        SetOutputFilter DEFLATE
    </FilesMatch>
</IfModule>

# ----------------------------------------------------------------------
# | ETags                                                              |
# ----------------------------------------------------------------------

# Remove `ETags` as resources are sent with far-future expires headers.
#
# https://developer.yahoo.com/performance/rules.html#etags
# https://tools.ietf.org/html/rfc7232#section-2.3

# `FileETag None` doesn't work in all cases.
<IfModule mod_headers.c>
    Header unset ETag
</IfModule>

FileETag None

# ----------------------------------------------------------------------
# | Expires headers                                                    |
# ----------------------------------------------------------------------

# Serve resources with far-future expires headers.
#
# (!) If you don't control versioning with filename-based
# cache busting, you should consider lowering the cache times
# to something like one week.
#
# https://httpd.apache.org/docs/current/mod/mod_expires.html

<IfModule mod_expires.c>

    ExpiresActive on
    ExpiresDefault                                      "access plus 1 month"

  # CSS
    ExpiresByType text/css                              "access plus 1 year"

  # Data interchange
    ExpiresByType application/atom+xml                  "access plus 1 hour"
    ExpiresByType application/rdf+xml                   "access plus 1 hour"
    ExpiresByType application/rss+xml                   "access plus 1 hour"

    ExpiresByType application/json                      "access plus 0 seconds"
    ExpiresByType application/ld+json                   "access plus 0 seconds"
    ExpiresByType application/schema+json               "access plus 0 seconds"
    ExpiresByType application/vnd.geo+json              "access plus 0 seconds"
    ExpiresByType application/xml                       "access plus 0 seconds"
    ExpiresByType text/xml                              "access plus 0 seconds"

  # Favicon (cannot be renamed!) and cursor images
    ExpiresByType image/vnd.microsoft.icon              "access plus 1 week"
    ExpiresByType image/x-icon                          "access plus 1 week"

  # HTML
    ExpiresByType text/html                             "access plus 0 seconds"

  # JavaScript
    ExpiresByType application/javascript                "access plus 1 year"
    ExpiresByType application/x-javascript              "access plus 1 year"
    ExpiresByType text/javascript                       "access plus 1 year"

  # Manifest files
    ExpiresByType application/manifest+json             "access plus 1 week"
    ExpiresByType application/x-web-app-manifest+json   "access plus 0 seconds"
    ExpiresByType text/cache-manifest                   "access plus 0 seconds"

  # Media files
    ExpiresByType audio/ogg                             "access plus 1 month"
    ExpiresByType image/bmp                             "access plus 1 month"
    ExpiresByType image/gif                             "access plus 1 month"
    ExpiresByType image/jpeg                            "access plus 1 month"
    ExpiresByType image/png                             "access plus 1 month"
    ExpiresByType image/svg+xml                         "access plus 1 month"
    ExpiresByType image/webp                            "access plus 1 month"
    ExpiresByType video/mp4                             "access plus 1 month"
    ExpiresByType video/ogg                             "access plus 1 month"
    ExpiresByType video/webm                            "access plus 1 month"

  # Web fonts
    # Embedded OpenType (EOT)
    ExpiresByType application/vnd.ms-fontobject         "access plus 1 month"
    ExpiresByType font/eot                              "access plus 1 month"
    # OpenType
    ExpiresByType font/opentype                         "access plus 1 month"
    # TrueType
    ExpiresByType application/x-font-ttf                "access plus 1 month"
    # Web Open Font Format (WOFF) 1.0
    ExpiresByType application/font-woff                 "access plus 1 month"
    ExpiresByType application/x-font-woff               "access plus 1 month"
    ExpiresByType font/woff                             "access plus 1 month"
    # Web Open Font Format (WOFF) 2.0
    ExpiresByType application/font-woff2                "access plus 1 month"


  # Other
    ExpiresByType text/x-cross-domain-policy            "access plus 1 week"

</IfModule>
