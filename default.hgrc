# To ensure proper hg behaviour for the project
# include this file into .hg/hgrc file like:
#
#   %include ../default.hgrc
#
# ATTENTION: Ensure that 'msgcat' command is available on your system. It is used
# to merge .po files. On Linux it is part of gettext package. On Windows do following:
#   1] Download and install gettext (http://gnuwin32.sourceforge.net/packages/gettext.htm)
#   2] Gettext install path (C:\Program Files (x86)\GnuWin32\bin) add to sytem PATH variable
#
# ATTENTION: Ensure that 'gulp' command is available on your system. It is used to
# auto-compile .less files after each merge, update and commit. For installation
# details see comment in gulpfile.js
#
# ATTENTION: To set correctly project rights after cloning it on LINUX use:
#   sudo chmod -R a+rw my_project_folder/
# It is important to not change X rights as these are the only rights whose change
# is considered by Mercurial as file change.

[paths]
core = ssh://hgpuller@91.148.0.111//home/<USER>/Documents/core_v2
parent = ssh://hgpuller@91.148.0.111//home/<USER>/Documents/allexpress

[merge-tools]
pomerge.priority = -100
pomerge.premerge = False
pomerge.args = --use-first $local $other -o $output
pomerge.executable = msgcat

[merge-patterns]
app/css/*.css = internal:local
app/css/libs/*.css = internal:local
app/modules/*/css/*.css = internal:local
app/modules/*/css/libs/*.css = internal:local
app/css/*.map = internal:local
app/css/libs/*.map = internal:local
app/modules/*/css/*.map = internal:local
app/modules/*/css/libs/*.map = internal:local
app/js/*.js = internal:local
app/js/libs/*.js = internal:local
app/modules/*/js/*.js = internal:local
app/modules/*/js/libs/*.js = internal:local
**.po = pomerge

[hooks]
# applies for merge and update commands
update.less = gulp compile-less --recompile --quiet
update.scss = gulp compile-scss --recompile --quiet
update.js = gulp compile-js --recompile --quiet
# applies for commit commands. Do not work correctly, as compiling is async and
# project files are commited sooner than compiled.
#precommit.css = gulp compile-less --recompile --quiet
#precommit.js = gulp compile-js --recompile --quiet

# for more info see:
# - https://www.mercurial-scm.org/wiki/MergeToolConfiguration
# - https://www.selenic.com/hg/help/merge-tools
# - http://stackoverflow.com/questions/4962810/mercurial-merge-strategy-per-file-type
# - http://stackoverflow.com/questions/4770018/checking-hgrc-file-into-mercurial
# - Complete list of hooks: https://www.mercurial-scm.org/repo/hg/help/hgrc > "hooks"
