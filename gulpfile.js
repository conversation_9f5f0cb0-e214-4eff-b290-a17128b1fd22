/**
 * To use gulp for tasks automation (e.g. for compiling .less files) do the following:
 * 
 * 1] install nodejs:
 * 
 *      https://nodejs.org/en/download/
 * 
 * 2] install gulp package globally:
 * 
 *      $ sudo npm install -g gulp
 *      
 *      NOTE: On Windows add installed gulp path (C:\Users\<USER>\AppData\Roaming\npm) into system PATH variable
 * 
 * 3] being in project root (where package.json is located) launch to install project gulp dependencies:
 *      
 *      $ npm install
 *      
 *      !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 *      !! If you get an error "Unhandled rejection Error: EACCES: permission denied" then run         !!
 *      !! following commands on linux:                                                                !!
 *      !!      sudo chown -R $USER:$GROUP ~/.npm                                                      !!
 *      !!      sudo chown -R $USER:$GROUP ~/.config                                                   !!
 *      !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 *      
 *      !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 *      !! NOTE: Once you install gulp dependencies you can move them one level up to parent directory !!
 *      !! and so share them for all projects. Means instead of e.g. /var/www/myproject/node_modules   !!
 *      !! it will be /var/www/node_modules. If IDE (Netbeans) bother you that there are missing nmp   !!
 *      !! dependencies in project then resolve it by creating symlink in project root folder:         !!
 *      !! ln -s ../node_modules node_modules                                                          !!
 *      !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 *      
 * 4] being in project root (where gulpfile.js is located) launch default task (compile-less + watch-less):
 * 
 *      $ gulp
 *      
 *    or any other of defined tasks in gulpfile.js:
 *    
 *      $ gulp compile-less
 *      $ gulp watch-less 
 *      $ gulp serve-less
 *      
 *   to recompile all .less files at the launch of task use --recompile switch
 *   
 *      $ gulp compile-less --recompile (for the moment this is defaut behaviour)
 *      
 *   to compile just specified sources use --source argument (paths must be relative to project root)
 *    
 *      $ gulp compile-less --source app/css/less/grid.less                     # compiles just app/css/less/grid.less
 *      $ gulp watch-less --source app/css/less                                 # compiles .less files directly under app/css/less/ 
 *      $ gulp serve-less --source "app/css/**"                                 # compiles .less files under app/css/ and all its subdirectories 
 *      $ gulp compile-less --source "app/css/less app/css/libs/less"           # compiles .less files in many provided sources
 *      
 *   to avoid minification of generated .css files use --nonminified switch
 *   to not display log of resolved globs and processed files use --quiet switch
 *   
 * 5] If you get an error "Error: ENOSPC: System limit for number of file watchers reached ..." 
 *      then you need to ncrease number of watch handles for Inotify facility:
 *      
 *      a) Add the following line to /etc/sysctl.conf file:
 *      
 *          fs.inotify.max_user_watches = 524288
 *          
 *      b) Then run this command to apply the change:
 *      
 *          sudo sysctl -p --system
 *    
 * NOTE: To add new gulp/nodejs packages use: 
 * 
 *      $ npm install --save-dev {package} 
 * 
 * @todo: For further posibilities of gulp uses (livereload/browsersync, ...) see:
 *      - http://www.smashingmagazine.com/2014/06/building-with-gulp/
 *      - http://www.browsersync.io/docs/gulp/
 *      - http://hazmi.id/building-with-gulp-1-compile-less-watch-changes-and-minify-css/ - see how the tasks are broken here into small pieces and then joined together
 *      
 *      - do sync browsing also if any view php file changes, maybe do it only on some flag or dedicated task (serve-php)
 *      - !!!if you would like to use changed() then imports/dependencies must be resolved
 *      
 *      For editing less files directly from Chrome see 
 *              http://code.tutsplus.com/tutorials/working-with-less-and-the-chrome-devtools--net-36636
 *              https://developer.chrome.com/devtools/docs/css-preprocessors
 */ 
(function(){
    
    //
    // load used gulp modules
    //
    const gulp = require('gulp'),
        gutil = require('gulp-util'),
        argv = require('yargs').argv,
        globby = require('globby'),
        //inquirer = require('inquirer'),
        //sourcemaps = require('gulp-sourcemaps'),
        path = require('path');

    //
    // define custom functions, variables and do some preprocessing
    //
    const getCssDest = function (file) {
        // for file object properties see https://github.com/gulpjs/vinyl
        //gutil.log(file.path);
        //gutil.log(file.base);
        //gutil.log(path.dirname(path.dirname(file.path)));
        let regex = new RegExp('\\' + path.sep + '(less|scss)(' + '\\' + path.sep + '|$)');
        return path.dirname(file.path).replace(regex, '$2');
    };
    
    const getJsDest = function (file) {
        // for file object properties see https://github.com/gulpjs/vinyl
        //gutil.log(file.path);
        //gutil.log(file.base);
        //gutil.log(path.dirname(path.dirname(file.path)));
        let regex = new RegExp(path.sep + 'sources(' + path.sep + '|$)');
        return path.dirname(file.path).replace(regex, '$1');
    };
    
    let task = argv._[0] || 'default';
    let recompile = true && argv.recompile;
    let nonminified = true && argv.nonminified;
    let quiet = true && argv.quiet;
    let doSourcemaps = true && argv.sourcemaps;
    let gulpVersion = gulp.parallel ? 4 : 3;

    //
    // LESS FILES TASKS
    //
    if (
        task === 'default'
        || task === 'less'
        || task === 'debug-less'
        || task === 'compile-less'
        || task === 'watch-less'
        || task === 'serve-less'
    ) {
        const gulpif = require('gulp-if'),
            //changed = require('gulp-changed'),
            less = require('gulp-less'),
            autoprefixer = require('gulp-autoprefixer'),
            minifyCSS = require('gulp-minify-css'),
            sourcemaps = require('gulp-sourcemaps');

        let ignoredFiles = [
            '!./**/vendors/**',
            '!./**/node_modules/**'
        ];

        let notCompiledFiles = [
            '!./app/**/_*.less',
            '!./app/**/mixins.less',
            '!./app/**/variables.less'
        ].concat(ignoredFiles);

        // prepare source files
        let sourceFiles = [], sources;
        if (argv.source) {
            let i, source;
            sources = argv.source.split(' ');
            for (i = 0; i < sources.length; i++) {
                source = '.' + path.sep + sources[i];
                if (source.substr(-5) !== '.less') {
                    if (source.substr(-1) !== path.sep) {
                        source += path.sep;
                    } 
                    source += '*.less';
                }
                sources[i] = source;
            }
        }
        else {
            sources = ['./app/**/*.less'];
        }
        if (!quiet) {
            gutil.log('Resolved source globs are:');
            gutil.log(sources.concat(ignoredFiles));
        }
        // Gulp takes everything that's a wildcard or a globstar into its virtual file name. 
        // That is whay we must provide relative fullpaths (without wildcards) 
        // to make gulp.dest(getCssDest) work properly 
        sourceFiles = globby.sync(sources.concat(ignoredFiles));
        if (sourceFiles.length === 0) {
            if (!quiet) {
                gutil.log('No .less files has been found for provided source');
            }
            process.exit(1);
        }
        if (!quiet) {
            gutil.log('Following files will be procesed:');
            gutil.log(sourceFiles);
        }
        //@todo - some promt here, inquirer is assync and the below code cannot be placed into callback (gulp will fail)
        // some kind of sync promt is needed here

        //
        // define gulp tasks
        //
        function debugLess (cb) {
            if (typeof gutil === 'undefined') {
                var gutil = require('gulp-util');
            }
            if (typeof argv === 'undefined') {
                var argv = require('yargs').argv;
            }
            switch (argv.case) {

                case 'list_of_files':
                    gutil.log(argv.source);
                    gutil.log(argv.source.split(' '));
                    var fs = require('fs');
                    gutil.log(fs.readdirSync(__dirname));
                    gutil.log(fs.lstatSync(__dirname).isDirectory());
                    gutil.log(fs.lstatSync(__dirname).isFile());
                    globby(['./app/**/*.less', '!./app/**/vendors/**']).then(function(paths) {
                        gutil.log(paths);
                    });            
                    break;

                case 'array_concat':
                    gutil.log(sourceFiles.concat(ignoredFiles));
                    break;

                case 'test':
                    gutil.log(typeof argv.recompile);   // gulp debug --case test -> undefined  // gulp debug --case test --recompile -> bool
                    gutil.log(!argv.recompile);         // gulp debug --case test -> TRUE       // gulp debug --case test --recompile -> FALSE
                    gutil.log(gulpVersion);
                    gutil.log(process.cwd());
                    if (argv.recompile) {
                        gutil.log('Yes recompile');
                    }
                    else {
                        gutil.log('No, do not recompile');
                    }
                    break;

            }
            return cb();
        };
        gulp.task('debug-less', debugLess);

        /**
         * Use this task to launch single compilation of changed .less files.
         * 
         * Switches:
         *      --recompile - all .less files are recompiled even if they were not changed.
         *      --source (string) - to compile just specified sources use --source argument 
         *          (paths must be relative to project root):
         *              app/css/less/grid.less                     # compiles just app/css/less/grid.less
         *              app/css/less                               # compiles .less files directly under app/css/less/ 
         *              "app/css/**"                               # compiles .less files under app/css/ and all its subdirectories 
         *              "app/css/less app/css/libs/less"           # compiles .less files in many provided sources
         *              
         *      --nonminified - to avoid minification of generated .css files
         *      --quiet - to not display log of resolved globs and processed files
         *      --sourcemaps - to generate source maps
         */
        function compileLess () {
            let files = globby.sync(sourceFiles.concat(notCompiledFiles));
            return gulp.src(files)
                .pipe(gulpif(doSourcemaps, sourcemaps.init()))
                .pipe(less())
                .pipe(autoprefixer({
//                    // for options see https://github.com/postcss/autoprefixer#options
//                    browsers: ['last 2 versions'],
//                    cascade: false
                }))
                .pipe(gulpif(!nonminified, minifyCSS()))
                .pipe(gulpif(doSourcemaps, sourcemaps.write('./')))
                // see: https://gulpjs.com/docs/en/api/dest
                .pipe(gulp.dest(getCssDest));
        };
        gulp.task('compile-less', compileLess);

        /**
         * Use this task to continuous compilation of changed .less files.
         * 
         * Switches:
         *      --recompile - all .less files are recompiled even if they were not changed.
         *      --source (string) - to compile just specified sources use --source argument 
         *          (paths must be relative to project root):
         *              app/css/less/grid.less                     # compiles just app/css/less/grid.less
         *              app/css/less                               # compiles .less files directly under app/css/less/ 
         *              "app/css/**"                               # compiles .less files under app/css/ and all its subdirectories 
         *              "app/css/less app/css/libs/less"           # compiles .less files in many provided sources
         *              
         *      --nonminified - to avoid minification of generated .css files
         *      --quiet - to not display log of resolved globs and processed files
         *      --sourcemaps - to generate source maps
         */
        function watchLess () {
            if (gulpVersion === 4) {
                return gulp.watch(sourceFiles, gulp.series(compileLess));
            }
            else {
                return gulp.watch(sourceFiles, ['compile-less']);
            }
        };
        gulp.task('watch-less', watchLess);

        if (gulpVersion === 4) {
            gulp.task('less', gulp.series(compileLess, watchLess));
            gulp.task('default', gulp.series(compileLess, watchLess));
        }
        else {
            gulp.task('less', ['compile-less' , 'watch-less']);
            gulp.task('default', ['compile-less' , 'watch-less']);
        }
    }
    //
    // SCSS FILES TASKS
    //
    else if (
        task === 'scss'
        || task === 'compile-scss'
        || task === 'watch-scss'
    ) {
        const gulpif = require('gulp-if'),
            sass = require('gulp-sass'),
            autoprefixer = require('gulp-autoprefixer'),
            minifyCSS = require('gulp-minify-css'),
            sourcemaps = require('gulp-sourcemaps');
        sass.compiler = require('node-sass');
        
        let ignoredFiles = [
            '!./**/vendors/**',
            '!./**/node_modules/**'
        ];

        let notCompiledFiles = [
            '!./app/**/_*.scss',
            '!./app/**/mixins.scss',
            '!./app/**/variables.scss'
        ].concat(ignoredFiles);

        // prepare source files
        let sourceFiles = [], sources;
        if (argv.source) {
            let i, source;
            sources = argv.source.split(' ');
            for (i = 0; i < sources.length; i++) {
                source = '.' + path.sep + sources[i];
                if (source.substr(-5) !== '.scss') {
                    if (source.substr(-1) !== path.sep) {
                        source += path.sep;
                    } 
                    source += '*.scss';
                }
                sources[i] = source;
            }
        }
        else {
            sources = ['./app/**/*.scss'];
        }
        if (!quiet) {
            gutil.log('Resolved source globs are:');
            gutil.log(sources.concat(ignoredFiles));
        }
        // Gulp takes everything that's a wildcard or a globstar into its virtual file name. 
        // That is whay we must provide relative fullpaths (without wildcards) 
        // to make gulp.dest(getCssDest) work properly 
        sourceFiles = globby.sync(sources.concat(ignoredFiles));
        if (sourceFiles.length === 0) {
            if (!quiet) {
                gutil.log('No .scss files has been found for provided source');
            }
            process.exit(1);
        }
        if (!quiet) {
            gutil.log('Following files will be procesed:');
            gutil.log(sourceFiles);
        }

        /**
         * Use this task to launch single compilation of changed .scss files.
         * 
         * Switches:
         *      --recompile - all .scss files are recompiled even if they were not changed.
         *      --source (string) - to compile just specified sources use --source argument 
         *          (paths must be relative to project root):
         *              app/css/scss/grid.scss                     # compiles just app/css/scss/grid.scss
         *              app/css/scss                               # compiles .scss files directly under app/css/scss/ 
         *              "app/css/**"                               # compiles .scss files under app/css/ and all its subdirectories 
         *              "app/css/scss app/css/libs/scss"           # compiles .scss files in many provided sources
         *              
         *      --nonminified - to avoid minification of generated .css files
         *      --quiet - to not display log of resolved globs and processed files
         *      --sourcemaps - to generate source maps
         */
        function compileScss () {
            let files = globby.sync(sourceFiles.concat(notCompiledFiles));
            return gulp.src(files)
                .pipe(gulpif(doSourcemaps, sourcemaps.init()))
                .pipe(sass.sync().on('error', sass.logError))
                .pipe(autoprefixer({
//                    // for options see https://github.com/postcss/autoprefixer#options
//                    browsers: ['last 2 versions'],
//                    cascade: false
                }))
                .pipe(gulpif(!nonminified, minifyCSS()))
                .pipe(gulpif(doSourcemaps, sourcemaps.write('./')))
                .pipe(gulp.dest(getCssDest));
        }
        gulp.task('compile-scss', compileScss);
        
        /**
         * Use this task to continuous compilation of changed .scss files.
         * 
         * Switches:
         *      --recompile - all .scss files are recompiled even if they were not changed.
         *      --source (string) - to compile just specified sources use --source argument 
         *          (paths must be relative to project root):
         *              app/css/scss/grid.scss                     # compiles just app/css/scss/grid.scss
         *              app/css/scss                               # compiles .scss files directly under app/css/scss/ 
         *              "app/css/**"                               # compiles .scss files under app/css/ and all its subdirectories 
         *              "app/css/scss app/css/libs/scss"           # compiles .scss files in many provided sources
         *              
         *      --nonminified - to avoid minification of generated .css files
         *      --quiet - to not display log of resolved globs and processed files
         *      --sourcemaps - to generate source maps
         */
        function watchScss () {
            if (gulpVersion === 4) {
                return gulp.watch(sourceFiles, gulp.series(compileScss));
            }
            else {
                return gulp.watch(sourceFiles, ['compile-scss']);
            }
        }
        gulp.task('watch-scss', watchScss);

        if (gulpVersion === 4) {
            gulp.task('scss', gulp.series(compileScss, watchScss));
        }
        else {
            gulp.task('scss', ['compile-scss' , 'watch-scss']);
        }
    }
    //
    // JS FILES TASKS
    //
    else if (
        task === 'js'
        || task === 'compile-js'
        || task === 'watch-js'
    ) {
        /**
         * See:
         * https://www.npmjs.com/package/gulp-uglify
         * https://github.com/mishoo/UglifyJS2#minify-options
         * https://github.com/mafintosh/pump
         */
        const ts = require('gulp-typescript'),
            tsProject = ts.createProject('tsconfig.json', {
                'allowJs': true,
                'noEmit': false
            }),
            babel = require('gulp-babel'),
            uglify = require('gulp-uglify'), 
            pump = require('pump');

        let ignoredFiles = [
            '!./**/vendors/**',
            '!./**/node_modules/**'
        ];

        // prepare source files
        let sourceFiles = [], sourceParts, sources = [];
        if (argv.source) {
            let i, source, pathRegex = new RegExp(path.sep + 'sources' + path.sep);
            sourceParts = argv.source.split(' ');
            for (i = 0; i < sourceParts.length; i++) {
                source = '.' + path.sep + sourceParts[i];
                if (
                    source.substr(-3) !== '.js'
                    && source.substr(-3) !== '.ts'
                ) {
                    if (source.substr(-1) !== path.sep) {
                        source += path.sep;
                    } 
                    sources.push(source + 'sources/**/*.js');
                    sources.push(source + 'sources/**/*.ts');
                }
                else if (!pathRegex.test(source)) {
                    if (!quiet) {
                        gutil.log('Only .js files placed in sources/ directories are compiled. This is not the case of :source:'.replace(':source:', source));
                    }
                    process.exit(1);
                }
                else {
                    sources.push(source);
                }
            }
        }
        else {
            sources = [
                './app/**/sources/**/*.js',
                './app/**/sources/**/*.ts'
            ];
        }
        if (!quiet) {
            gutil.log('Resolved source globs are:');
            gutil.log(sources.concat(ignoredFiles));
        }
        sourceFiles = globby.sync(sources.concat(ignoredFiles));
        if (sourceFiles.length === 0) {
            if (!quiet) {
                gutil.log('No .js and/or .ts files has been found for provided source');
            }
            process.exit(1);
        }
        if (!quiet) {
            gutil.log('Following files will be procesed:');
            gutil.log(sourceFiles);
        }

        /**
         * Use this task to launch single compilation of changed .js files.
         * 
         * Switches:
         *      --source (string) - to compile just specified sources use --source argument 
         *          (paths must be relative to project root):
         *              app/js/sources/main.js                  # compiles just app/js/sources/main.js
         *              app/js                                  # compiles sources/*.js files directly under app/js/ 
         *              "app/js/**"                             # compiles sources/*.js files under app/js/ and all its subdirectories 
         *              "app/js/sources app/js/libs/sources"    # compiles .js files in many provided sources
         *              
         *      --nonminified - to avoid minification of generated .js files
         *      --quiet - to not display log of resolved globs and processed files
         */
        function compileJs (cb) {
            let pipe, minifyOptions;
    //// pipe version        
    //        if (!nonminified) {            
    //            pipe = gulp.src(sourceFiles)
    //            .pipe(tsProject())
    //            .pipe(uglify(minifyOptions))
    //            .pipe(gulp.dest(getJsDest));
    //        }
    //        else {
    //            pipe = gulp.src(sourceFiles)
    //            .pipe(tsProject())
    //            .pipe(gulp.dest(getJsDest));            
    //        }

    // pump version - When using standard source.pipe(dest) source will not be destroyed
    // if dest emits close or an error. You are also not able to provide a callback 
    // to tell when then pipe has finished. pump does these two things for you.
            if (!nonminified) {
                pipe = pump([
                    gulp.src(sourceFiles),
                    tsProject(),
                    babel({
                        presets: ['@babel/env'],
                        // avoid automatic "use strict" addition by providing
                        // source type (see https://babeljs.io/docs/en/options#sourcetype)
                        sourceType: 'unambiguous'
                    }),
                    uglify(minifyOptions),
                    gulp.dest(getJsDest)
                ], cb);  
            }
            else {
                pipe = pump([
                    gulp.src(sourceFiles),
                    tsProject(),
                    gulp.dest(getJsDest)
                ], cb);  
            }
            if (gulpVersion === 4) {
                return pipe;
            }
        };
        gulp.task('compile-js', compileJs);
        
        /**
         * Use this task to continuous compilation of changed .js files.
         * 
         * Switches:
         *      --source (string) - to compile just specified sources use --source argument 
         *          (paths must be relative to project root):
         *              app/js/sources/main.js                  # compiles just app/js/sources/main.js
         *              app/js                                  # compiles sources/*.js files directly under app/js/ 
         *              "app/js/**"                             # compiles sources/*.js files under app/js/ and all its subdirectories 
         *              "app/js/sources app/js/libs/sources"    # compiles .js files in many provided sources
         *              
         *      --nonminified - to avoid minification of generated .js files
         *      --quiet - to not display log of resolved globs and processed files
         */
        function watchJs () {
            if (gulpVersion === 4) {
                return gulp.watch(sourceFiles, gulp.series(compileJs));
            }
            else {
                return gulp.watch(sourceFiles, ['compile-js']);
            }
        };
        gulp.task('watch-js', watchJs);

        if (gulpVersion === 4) {
            gulp.task('js', gulp.series(compileJs, watchJs));
        }
        else {
            gulp.task('js', ['compile-js' , 'watch-js']);
        }
    } 
})();
