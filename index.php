<?php
// load application class and init it
require_once dirname(__FILE__) . DIRECTORY_SEPARATOR 
    . 'app' . DIRECTORY_SEPARATOR 
    . 'libs' . DIRECTORY_SEPARATOR 
    . 'default' . DIRECTORY_SEPARATOR 
    . 'App.php';
App::init();

/*/
// debug .htaccess
App::debug(@$_GET['_htaccess'], '$_GET[_htaccess]'); //debug
App::debug(@$_SERVER, '$_SERVER'); //debug
return; //debug
/*/

/*/
// frontend-only maintenance screen 
if (
    !ON_LOCALHOST
    && App::$requestType === 'slug'
    && !App::hasScreen(App::$slug)
    && (
        // allow frontend for selected IPs
        !in_array(Sanitize::value($_SERVER['REMOTE_ADDR']), array('************', '**************'))
        // allow to test maintenance screen if there is a testMaintenance GET param
        || isset($_GET['testMaintenance'])
    )
) {
    include ROOT . DS . 'maintenance.php';
    exit;
}
/*/

if (App::$langIsInvalid) {
    App::setErrorMessage(__(__FILE__, 'Požadovaná jazyková verzia stránky nie je dostupná'));
    // set main html output
    $output = App::loadScreen('_404');
    $_contents['main']['text'] = $output;
    // seo index and follow set to FALSE for screens
    App::setSeoIndex(false);
    App::setSeoFollow(false);
}
elseif (App::$langIsNotPublished) {
    App::setErrorMessage(__(__FILE__, 'Požadovaná jazyková verzia stránky zatiaľ nie je dostupná'));
    // set main html output
    $output = App::loadScreen('_404');
    $_contents['main']['text'] = $output;
    // seo index and follow set to FALSE for screens
    App::setSeoIndex(false);
    App::setSeoFollow(false);
}
else {
    // in case of admin-only application
    if (
        App::$adminOnly
        && App::$requestType === 'slug'
        && !App::hasScreen(App::$slug)
    ) {
        // redirect to homepage if not yet
        if (App::$slug !== App::$homeSlug) {
            App::redirect('/');
        }
        // replace any content slug by admin slug
        App::$slug = App::$adminSlug;
    }

    // prepare contents array which will be passed to layout
    // For the moment there is only one region content passed to layout
    $_contents = array(
        // regions
        'main' => array(),
    );

    // dispatch slug request
    if (App::$requestType === 'slug') {
        $screenOutput = false;
        $contentOutput = false;
        // try to get a screen first if it exists
        if (App::hasScreen(App::$slug)) {
            App::authenticate('App', 'screen', App::$slug);
            $screenOutput = App::loadScreen(App::$slug, App::$params, true, App::$args);
        }
        // if no such screen then try for a content
        else {
            $contentOutput = App::getContentByLocator(App::$slug);
            // use content layout if specified
            if (!empty($contentOutput['layout'])) {
                $layout = explode('.', $contentOutput['layout']);
                if (count($layout) === 2) {
                    App::setLayout($layout[0], $layout[1]);
                } 
                else {
                    App::setLayout('App', $layout[0]);
                }
            }
        }
        // if neither screen nor content is found then return 404 screen
        if ($screenOutput === false && $contentOutput === false) {
            $screenOutput = App::loadScreen('_404');
        }
        // prepare variables for layout
        if ($screenOutput !== false) {
            // set main html output
            $output = $screenOutput;
            $_contents['main']['text'] = $output;
            // seo index and follow set to FALSE for screens
            App::setSeoIndex(false);
            App::setSeoFollow(false);
        }
        // if no screen (even 404) then a content MUST be retrieved
        else {
            App::setPropertyContent($contentOutput);
            $output = $contentOutput['text'];
            $_contents['main'] = $contentOutput;
            // apply contet seo params if seo params are not set yet
            if (!App::getSeoTitle()) {
                App::setSeoTitle($contentOutput['seo_title']);
            }
            if (!App::getSeoKeywords()) {
                App::setSeoKeywords($contentOutput['seo_keywords']);
            }
            if (!App::getSeoDescription()) {
                App::setSeoDescription($contentOutput['seo_description']);
            }
            // seo index and follow are acumulated by logical AND so just set them
            App::setSeoIndex($contentOutput['seo_index']);
            App::setSeoFollow($contentOutput['seo_follow']);
        }
    }
    // dispatch mvc request
    else {
        App::authenticate(App::$module, 'controlleraction', App::$controller . '.' . App::$action);
        $output = App::loadControllerAction(App::$module, App::$controller, App::$action, App::$params, true, App::$args);
        $_contents['main']['text'] = $output;
        // seo index and follow set to FALSE for mvc requests
        App::setSeoIndex(false);
        App::setSeoFollow(false);
    }
}
    
// apply default seo params if seo params are not set yet
if (!App::getSeoTitle()) {
    App::setSeoTitle(App::getSetting('App', 'seo.defaultTitle'));
}
if (!App::getSeoKeywords()) {
    App::setSeoKeywords(App::getSetting('App', 'seo.defaultKeywords'));
}
if (!App::getSeoDescription()) {
    App::setSeoDescription(App::getSetting('App', 'seo.defaultDescription'));
}

// render layout if defined
list($layoutModule, $layoutName) = App::getLayout();
if ($layoutModule && $layoutName) {
    // merge regions data from $_contents retrieved here above with regions data from 
    // layoutParams['_contents'] set dynamically in application logic. 
    // Dynamic contents region data have higher priority.
    $layoutParams = App::getPropertyLayoutParams();
    if (!empty($layoutParams['_contents'])) {
        foreach ($layoutParams['_contents'] as $region => &$content) {
            if (!empty($_contents[$region])) {
                $content = array_merge((array)$_contents[$region], (array)$content);
            }
        }
    }
    // if there are no $layoutParams['_contents'] data defined dynamically then 
    // set them to the ones retrieved here above
    else {
        $layoutParams['_contents'] = $_contents;
    }
    // load layout
    $output = App::loadLayout($layoutModule, $layoutName, $layoutParams, App::$data);
    
    // render doctype if defined
    list($doctypeModule, $doctypeName) = App::getDoctype();
    if ($doctypeModule && $doctypeName) {
        $layoutParams['head'] = App::loadElement('App', 'htmlHead');
        $layoutParams['body'] = $output;
        // load doctype
        $output = App::loadDoctype($doctypeModule, $doctypeName, $layoutParams, App::$data);
    }
}

// in the case that actual request is on https but it does not requires it then
// switch to http (all this logic is included in following method)
App::switchToHttp();

// clear session data which should not be preserved on common app termination
// - application messages which were not displayed till now
App::clearMessages();
// - redirect counter
App::clearRedirectCounter();

// place this before echo to allow write to headers if App::$debugOutput is FB
App::getElapsedMicrotime(true);
DB::debugSqlLog();

// catch php generated errors before the generated page html is echoed
App::endPhpErrorsHtmlCapture();

// echo the generated output
echo $output;

//// do you need to find out if you update the right/live FTP?
//// ATTENTION: This brokes all nonHTML output, e.g. JSONs for AJAX calls
//// so do it only for home page request
//if (SLUG === HOME_SLUG) {
//    echo '<!-- yes this is the right ftp -->';
//}