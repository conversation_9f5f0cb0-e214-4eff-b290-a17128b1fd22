There is a programmer manual placed in directory misc/fajnbook/. It is a gitbook 
and to read it you must do following (both windows & linux):

    1] Instal npm module gitbook-cli: 

        sudo npm install gitbook-cli -g

    2] Go to project root folder and run:

        gitbook serve
    
    or (to save system resources):

        gitbook serve --no-watch --no-live

    3] Open URL http://localhost:4000/ in your browser

    4] To make it available on http://fajnbook.run.sk/ do following:

        a] Compile gitbook with command:
            
            gitbook serve --no-watch --no-live

        b] Copy the content of _book/ folder to run.sk FTP on Webglobe > run.sk > SubDomains > fajnbook.
            Do not delete the existing content (to preserve http password) but just
            rewrite it by new one.
            
The 'gitbook serve' will create directory _book with compiled/built book into 
html/css/js sources. This directory is ignored by mercurial and can be deleted anytime 
when you do not read the book in browser. It will be recreated/rewrited after each 
call of 'gitbook serve'.

It is possible also to export it as .pdf or .epub. In case of any problem or for 
more details see http://toolchain.gitbook.com/ . File book.json defines the book root 
and some other config properties.

Some example of the gitbook use: https://github.com/GitbookIO/gitbook/tree/master/docs 
or https://github.com/SamyPesse/How-to-Make-a-Computer-Operating-System

How to write comments in .md files: http://stackoverflow.com/questions/4823468/comments-in-markdown
    