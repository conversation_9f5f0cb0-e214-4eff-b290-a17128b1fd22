# Všeobecné pokyny na pridávanie novej funkcionality

1] Žiadnu existujúcu funkcionalitu nemeň! Len pridávaj nový kód.
Výnimkou sú prípady keď je priamo v zadaní požadovaná zmena nejakej existujúcej metódy.
Nevymazavaj ani medzery ani nic  nepotrebne, iba pridavaj novy kod alebo zmen iba kod ktory je potrebny zmenit pre funkcnost novej implentacie....

2] Vyhýbaj sa vytváraniu inštancií modelov použitím `App::loadModel()` a `$this->loadModel()` s posledným parametrom `true`.
T.j. namiesto nesprávneho:

```php
$MyModel = $this->loadModel('MyModel', true);
```

použi správne:

```php
$this->loadModel('MyModel');
$MyModel = new MyModel();
```
