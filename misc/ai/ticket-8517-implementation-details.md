# Porovnanie názvov produktov v MRP a v e-shope

1] Uprav metódu `MrpStockReader::readRecord()` tak aby vrátený záznam obsahoval aj pole `'mrp_name'`. Špecifikáciu pre  prenos udajov z MRP cez API viď tu: https://faq.mrp.cz/faqcz/FaqAnswer.aspx?cislo=483#EXPEO0 alebo https://mrp.sk/component/content/article/87-podpora/91-autonomny-rezim#EXPEO0

2] Modelu `EshopProduct` pridaj pole `mrp_name` a zapracuj ho do admin formulara (read only input, súbor `app/modules/Eshop/views/EshopProducts/admin_productForm.php`) a admin zoznamu (`EshopProducts::admin_index()`).

3] Pridaj import poľa `mrp_name` do aktualizačného importu `EshopProductImport::importUpdate()`​ > `'mrpStockUpdate'​`:

- pridaj kód potrebný na import poľa `mrp_name​`
- uprav načítanie pair values tak aby sa načítali okrem `EshopProduct.id` aj `EshopProduct.mrp_name` (`$this->loadPairFieldValues(array('fields' => array('EshopProduct.id', 'EshopProduct.mrp_name')))​`)
- v prípade importu `'mrpStockUpdate'` vytváraj zoznam `mrp_name`-s,​ ktoré sa líšia a ak sa také nájdu, tak na konci importu odošli interný upozorňujúci mail obsahujúci zoznam pôvodných a nových názvov v MRP. Tento mail odošli pomocou `App::sendEmail()` na adresu uloženú v nastavení `'email.cc'` (modul `'Eshop'`).

## Všeobecné pokyny

Nový kód pridávaj na základe pokynov v `misc/ai/coding-general-instructions.md`.
