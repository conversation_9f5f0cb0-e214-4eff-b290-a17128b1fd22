**********
IMPORT KATEGORII
**********
Pred impoortom kategorie skontrolujte ci sa v csv nenachadza kategoria s id/parent_id 1, ak ano zmente ju na kategoriu ktora este neexistuje

**********************************************************
********************* KARAVANDOPLNKY *********************
**********************************************************

**********
PREDPREDAJ
**********
Dostupnosť (availibility aj disponibility) "Predpredaj" (enum_presale, presale) sa na žiadosť klienta
premenovala na "Momentálne nedostupné". Toto premenovanie sa uskutočnilo len na úrovni prekladov
v .po súboroch. Viď zmeny v revízii tejto poznámky.

***************************
ÚVODNÉ NASADENIE (REDIZAJN)
***************************

***********
MEILISEARCH
***********

Ohľadom inštalácie viď app/updates/2024/2024-02-29_svec_meilisearch.txt
Ohľadom úpravy/zmien vo vyhľadávaní viď inštrukcie v uvodnom phpDoce triedy EshopProductMeiliSearch
Ak sa používa MeiliSearch vo verzi 0.29 (a viac, 0.29 je momentálna inštalácia MS na web9) a web beží na PHP 7.3 tak viď app/updates/2024/2024-02-29_svec_meilisearch.txt > 2a)

Nasadená implementácia je odladená voči MeiliSearch 0.29
Dokumentáciu k tejto verzii nájdeš na https://github.com/meilisearch/documentation/tree/v0.29
Dokumentáciu si môžeš rozbehať aj lokálne - viď návod na úvodnej github stránke danej verzie.
Ak yarn nie je kompatibilny s tvojou verziou node tak viď tento návod: https://stackoverflow.com/a/19584407/1245149

**********************************************************
************************ ALTEREGO ************************
**********************************************************

***************
DAROVACIA KARTA
***************

Popis funkcionality a implementácie darovacej karty je popísaný tu: http://projects.run.sk/projects/82/comments/52770

Revízie: 399f82f631ba, 5c4eb22083ff, e24532559655, d1a00e994181, 12bd312ebe86 (hg log -k darova)


**************************************************
ZĽAVOVÉ KÓDY S ABSOLÚTNOU ZĽAVOU (PEŇAŽNÉ POUKAZY)
**************************************************

Zľavové kódy s absolútnou zľavou (peňažné poukazy, PP) sú implementované ako položky košíka s mínusovou cenou.

Legislatíva:
- https://www.financnasprava.sk/_img/pfsedit/Dokumenty_PFS/Zverejnovanie_dok/Dane/Metodicke_pokyny/Nepriame_dane/2019/2019.09.27_16_DPH_2019_MP.pdf > príklad 24
- https://www.podnikajte.sk/dan-z-pridanej-hodnoty/zdanovanie-poukazov-voucherov-dph-2019
- https://www.easystart.sk/poukazy-a-ich-zdanovanie/

# Jednoúčelové PP

Pokiaľ sú na eshope len produkty s jednou sadzbou DPH, tak všetko funguje ako má.
T.j. od cien s/bez DPH bežnýchproduktov sa odčíta cena s/bez DPH PP a všetky súčty sedia.
Viď krasnasprava.sk. V legislatíve je tento druh PP nazvaný ako "Jednoúčelový" (JPP) a 
naša implementácia by legislatíve mala vyhovovať - pri JPP sa DPH vopred pozná a platí 
sa už pri jeho nákupe (napríklad formou darovacej karty). Následne pri uplatnení JPP sa
zaplatené DPH odpočítava.

# Viacúčelové PP

Ak sú však na eshope produkty s rôznou sadzbou DPH (alterego.sk) tak horeuvedený postup 
nefunguje (napr. pri knihe s cenou 21€ / DPH 10% a PP 20€ / DPH 20% by ostala výsledná 
cena s DPH 1€ / DPH -1.9€ (2.1€ - 4€) !!!). Podľa legislatívy sa takéto PP nazývajú
"Viacúčelové" (VPP) a DPH sa pre VPP nepočíta. Súma VPP sa odpočíta len od celkovej sumy
produktov ktorú treba zaplatiť. V podstate ide len o určitú formu úhrady (alternetívne platidlo).

Ak mám v košíku knihu za 21€ / 10% DPH, mapu za 9€ / 20% DPH a VPP 20€ tak 20€ VPP sa odpočíta len 
od výslednej 30€ s DPH aby som vedel koľko mám zaplatiť (cena k zaplateniu). 
Na cenu produktov s DPH a bez DPH to nemá žiadný dopad (t.j. DPH sa korektne zaplatí).
Na konci objednávkového procesu to zobrazujeme takto:

    Zľava spolu:                       -20€             (VPP)
    Cena celkom za tovar:               10€             (kniha + mapa)          //**
    Poštovné:                           2.50€

    Celková cena objednávky s DPH:      32.50€          (kniha + mapa)          //**
    Celková cena objednávky bez DPH:    28.67€          (kniha + mapa)
    
    Súma na zaplatenie:                 12,50€

Riadky označené '//**' sú trochu mätúce - zvlášť ak je poštovné 0 tak by sme očakávali,
že tam bude rovnaká suma a ono nie. Tento problém by sa vyriešil keby sa "Zľava spolu" odčitávala
až úplne na konci (t.j. uplatnila by sa aj na dopravu) - viď aj priklad nižšie od Martinusu.

Do tabuľky run_eshop_orders sa pridali stĺpce:
    - products_price_to_pay (10€)
    - vouchers_discount     (20€)
    - order_price_to_pay    (12.5€)

Stĺpce products_price_to_pay a order_price_to_pay sa používaju všade tak kde sa 
predtým zobrazovala/používala výsledná/aktuálna cena s DPH.

Podobným spôsobom (ako VPP) sa na alterego.sk uplatňuje aj bonus 10€ (keď ho zákazník získa).

Na Martinuse sú VPP v objednávkovom procese zadávané v časti "Spôsob platby" - t.j. je to použité
ako alternatívne platidlo (v súlade s pochopením legislatívy). Je s nimi pokryté aj poštovné
(prirodzene ako s každým iným platidlom). Vo faktúre je to tiež uvedené ako súčasť platby:

    {produkt}                       5.99 €
    {poštovné}                      0.99 €

    Suma bez DPH                    5.82 €
    Suma DPH                        1.16 €
    Celková suma                    6.98 €   // tu konči sumár objednávky, ďalej sú už len zhrnutia platieb

    Darčeková poukážka             -5.00 €
    Celková faktúrovaná suma        1.98 €
    Uhradené zálohou (29.4.2021)   -1.98 €   // = platba kartou vopred
    K úhrade                        0.00 €


***************************************
SUPLIER PRODUCTS (ZJEDNOTENIE IMPORTOV)
***************************************

Funkcionalita nie je používaná/zverejnená hoci klient má o ňu potencionálne záujem. 
Je zakomentovaná len na pár kľúčových miestach aby bola nedostupná a zvyšok je plne funkčný.
Zakomentované miesta (vyhľadaj aj "@todo-redesign" a "//unused on alterego"):
- v nastaveniach Eshopu som zakomentoval tiež 'Eshop/EshopProduct/importedProductsMinMarkupRate'
- vo formuláre produktu som zakomentoval synchronize_price_with_suppliers a supplier_pid a 'if' => !empty($supplierProductsHtml)
- po potiahnutí z vydavateľa je potrebné v admin indexe objednávok zakomentovať odporúčania dodávateľov (teraz sa už nepozerá na EshopProduct.supplier_pid ale sa hľadajú najlacnejši dodávatelia.


************************************
DOPLNKOVÉ SLUŽBY (DARČEKOVÉ BALENIE)
************************************

Darčekové balenie sa implementovalo pomocou všeobecnejšieho prístupu (na rozdiel od na mieru šitej implementácie pre jazykovú korektúru na krasnasprava.sk, rev 9f5cf02648a1, b40023689c9c). Tento všeobecnejší prístup je pridanie produktov "Doplnkové služby" (DS).
Úvodná implementácia je urobená v rozsahu a zapracovaní súvislostí pre "Darčekové balenie".
Detaily zapracovania viď v revízii 0fb2987f6032 a 9bd34226210f.

Produktom sa pridal príznak `is_additional_service` typu enum. Jednotlivé hodnoty sú pre rôzne typy DS (momentálne len enum_gift_package). Ak je hodnota NULL, tak produkt nie je DS. 

Produkty typu DS su vynechané z bežného frontendového zobrazovania - viď EshopProduct::getFilterFindOptions() a ::getSearchFindOptions().
Metóde EshopProduct::getDetails() pribudlo option 'getAdditionalServiceProducts'. Ak TRUE tak sú pod kľúčom 'EshopAdditionalServiceProduct' detaily prduktov DS.

V košíku je možné ich pridať jednotlivým položkám košíka - viď EshopCarts::addChildProduct(). Za týmto účelom sa pridali položkám košíka kľúče 'parent_index', 'child_indexes' a 'child_ids'. Tieto je potencionáolne možné použiť aj pre iné budúce závislosti v košíku typu rodič-dieťa.
Na vydavatel.sk/alterego.sk sú v košíku podobne spravované závislosti medzi produktami špeciálnych ponuk (promovanými a zľavnenými) no používajú sa iné kľúče v poli položiek košíka takže tam nevzniká konflikt.

Ak nie je vhodné aby produkty typu DS boli na projekte dostupné, tak stačí v admin formulári zakomentovať pole kde sa volí o aký typ DS ide.
Ak v systéme neexistujú žiadne produkty typu DS, tak ostatná zapracovaná logika sa jednoducho len nepoužije.

Todo
----
-! pri súčastnom použití spolu so špeciálnymi ponukami (vydavatel.sk, alterego.sk) je potrebné ešte odladiť metódu EshopCart::applySpecialOffers(), ktorá dosť "mieša" produktami v košíku... t.j. treba pri tom miešani udržať náväznosti dané v parent_index a child_indexes (najmä keď vskutku nejaké ŠP existujú). Najjednoduchší spôsob je asi sledovať konverzie indexov a nakoniec ich len prekonvertovať - viď už zapracované konverzia na konci metódy ("// actualize parent_index and child_indexes"). Toto čiastočné zapracovanie sa dá pozrieť v revízii cb5925b91f69 (vydavatel.sk, alterego.sk). Pri tomto čiastočnom zapracovaní som to odladil no bez existujúcich ŠP. Treba to odladiť ešte aj s existujúcimi ŠP a či aj funkcionalita ŠP ostala ok.
- na projektoch kde sa používate typ produktu je možné skryť jednotlivé DS do tohoto komba typov produktov a to tak, že sa vytvorí napr. typ produktu "Darčekové balenie" s pid-om 'enum_gift_package'. Viď predpríprava v EshopProduct::normalize() > "// resolve some product properties according to product type" a zapracovanie na alterego.sk > rev d9eeb32f6b6d
- priradenie DS len určitým produktom (HABTM). Momentálne každá DS je dopstpná pre každý produkt, ktorý nie je DS - viď EshopProduct::getAdditionalServiceProducts()
- (200526a) dal by sa ešte pridať príznak či sa má služba (jej cena) uplatňovať podľa počtu produktov alebo jednorázovo/paušálne bez ohľadu na počet produktov (vyhľadaj "@implementationDetails > 200526a" v EshopCart.php)
- pridávanie doplnkových služieb je implementované cez košík ale dalo by sa urobiť aj z detailu produktu (viď krásnaspráva.sk, rev 9f5cf02648a1, b40023689c9c, vyhľadaj regex "languageCorrectionProduct|language_correction"). No namiesto príznaku "langualge_correction" by sa dal všeobecne nazvaný príznak "child_product" (v súlade s ostatnou logikou zapracovanie v košíku)


******
BONUSY
******
Fungujú tak že užívateľ si z každho nákupu pripočíta cenu zakúpených produktov
do UserProfile.bonus_points. Keď táto súma prekročí nastavenie Eshop.EshopOrder.applicableBonusPoints,
tak sa z nasledujúcej objednávky odpočíta bonus podľa nastavenia Eshop.EshopOrder.bonusDiscount
a UserProfile.bonus_points sa vynuluje.

Cena produktov sa započíta do UserProfile.bonus_points až pozaplatení danej objednávky.

Ak sa objednávka na ktorej sa uplatnil bonus (a vynulovalo sa UserProfile.bonus_points) zruší,
tak užívateľovi sa do UserProfile.bonus_points naspäť pripočíta Eshop.EshopOrder.applicableBonusPoints.

Ak má nastavenie Eshop.EshopOrder.applicableBonusPoints prázdnu hodnotu tak je logika zbierania bonusov vypnutá.


***********************************************************
************************ VYDAVATEL ************************
***********************************************************

Projekt bol naklonovaný z vydavatel.sk a nasledovné implementačné detaily boli napísané ešte na projekte vydavatel.sk

*************
HEUREKA KOŠÍK
*************

Dokumentácia: https://sluzby.heureka.sk/napoveda/kosik-api
Kontakt: <EMAIL>

x? čo obnáša administrácia / zriadenie tejto služby?
    >> Už to majú zriadrené

1] Metódy API na strane obchodníka (Heuréka si pýta / posiela Obchodníkovi):
- GET products/availability - Heuréka si pýta info o cene a dostupnosti produktu od obchodníka [8h]
- GET payment/delivery - Heuréka si pýta dostupné metódy doručenia a platby a ich kombinácie od obchodníka [8h]
- GET order/status - Heuréka si pýta stav objednávky od ochbodníka [3h]
- POST order/send - Heuréka odošle novú objednávku do obchodu [10h]
- PUT order/cancel - Heuréka odošle storno objednávky do obchodu [5h]
    - je potrebné to aj pridať do logu zmien v objednávke
- PUT payment/status - Heuréka pošle stav platby za objednávku do obchodu [3h]
    - je potrebné to aj pridať do logu zmien v objednávke

Všeobecná forma URL: https://www.example.com/api/:verzia_api:/:oblasť:/:akcia:/

Tieto akcie na strane obchodníka obsluhuje screen api.php, ktorý na základe argumentov (:verzia_api:/:oblasť:/:akcia:/)
a HTTP metódy zavolá metódu Eshop::processApiRequest() a tá zavolá prislušnú akciu kontrolera EshopHeurekaCarts.

2] Metódy API na strane Heuréky (Obchodník si pýta / posiela Heuréke):
- GET payment/status - Obchodník si pýta stav platby z Heuréky. [3h]
    -? kedy a ako by sa malo spúšťať toto?
- PUT order/status - Obchodník odošle stav objednávky do Heuréky [3h]
    - odošle sa pri zmene stavu objednávky
- PUT payment/status - Obchodník odošle stav platby do Heuréky [2h]
    - odošle sa pri zmene stavu platby objednávky
- GET order/status - Obchodník si pýta informácie o stave objednávky a internom čísle objednávky na Heureke [3h]
    -? kedy a ako by sa malo spúšťať toto?
- GET stores - Obchodník si pýta informácie o pobočkách / výdajných miestach, ktoré má obchod uložené na Heureke
- GET shop/status - Obchodník si pýta informácie o aktivácii obchodu v Košíku (kontrola funkčnosti API košíka) [4h]
    - toto by sa mohlo spúšťať formou cronu a v prípade problému by to posielalo informáčný mail nám aj obchodníkovi
- POST order/note - Obchodník odošle poznámku k objednávke do Heuréky
    - odošle sa pri vytvorení poznámky k objednávke [3h]
- POST order/invoice - Obchodník odošle faktúru k objednávke do Heuréky
    - odošle sa pri vytvorení faktúry k objednávke [6h]

Všeobecná forma URL: https://ssl.heureka.sk/api/cart/:API_ID_obchodu:/:verzia_api:/:metoda:/:funkcia:

3] Všetka business logika je obsiahnutá v modeli EshopHeurekaCart


Zápis novej objednávky:
-----------------------

Štruktúru došlých dat viď tu:
http://vydavatel/_debug/getParams?products[0][id]=ABC123&products[0][count]=1&products[0][price]=100&products[0][totalPrice]=100&products[0][gifts][0][name]=darek&products[0][gifts][0][shopGiftId]=drk1&customer[firstname]=Jan&customer[lastname]=Novak&customer[street]=Jiraskova%209&customer[phone]=*********&customer[city]=Jablonec&customer[company]=&customer[postCode]=46601&customer[state]=CZ&customer[email]=<EMAIL>&deliveryAddress[firstname]=Jan&deliveryAddress[lastname]=Kos&deliveryAddress[street]=Liberecka%20999&deliveryAddress[city]=Jablonec&deliveryAddress[company]=&deliveryAddress[postCode]=46601&deliveryAddress[state]=CZ&deliveryAddress[note]=Pozn%C3%A1mka%20TEST%20Heureka&deliveryId=100&paymentId=203&productsTotalPrice=500&paymentOnlineType[title]=Testovac%C3%AD%20online%20platba&paymentOnlineType[id]=1&deliveryPrice=100&paymentPrice=30.20&heureka_id=7864287

V run_eshop_orders.heureka_id sa zapíše id objednávky na stráne Heuréky.
Je otázne či osobitne ukladať aj stav objednávky a platby(?)


**************
SLOVART IMPORT
**************

Odpovede na moje otázky:
------------------------
Veronika Parobková <<EMAIL>>:

1] aký je rozdiel medzi tagmi PRODUCT a PRODUCTNAME - je to len spätná kompatibilita a niektorý z tagov bude časom odstránený?
Áno sú to rovnaké hodnoty

2] aký je rozdiel medzi tagmi EAN a PRODUCTNO - je to len spätná kompatibilita a niektorý z tagov bude časom odstránený?
Áno sú to rovnaké hodnoty

3] čo znamená B2B_AVAILABILITY = 4  (na objednávku)? Je produkt dostupný? V akom čase?
Na objednávku. Titul nie je v čase vytvorenia záväznej objednávky skladom, ale po potvrdení záväznej objednávky ho vieme objednať u zahraničného dodávateľa a dodať. Dodacia lehota je garantovaná maximálne 6 týždňov.

4] čo znamená B2B_AVAILABILITY = 2 (dodanie oneskorené)? V akom čase sa produkt dodá?
Nie je v čase vytvorenia záväznej objednávky skladom, ale sme schopní tento tovar do 21 dní od potvrdenia objednávky expedovať.

5] čo znamená B2B_AVAILABILITY = 3 (novinka)? Je to predpredaj? Aký to má vzťah voči STATE  = 1 (novinka) a STATE = 8 (pripravujeme)
Ako sa dajú jednoznačne rozpoznať knihy v predpredaji?
Stav NOVINKA je príznak pre nové tituly naskladnené po prvý raz pred maximálne 60 dňami. Nie je to predpredaj.
Pripravujeme sú tituly, ktoré pripravujeme ale nemáme ich ešte naskladnené. Tituly sa dajú na stránke rezervovať nie objednať.
Knihy v predpredaji sú : STATE = 8 (pripravujeme)

6] aký je rozdiel medzi B2B_AVAILABILITY = -1 (zakázané) a B2B_AVAILABILITY = 0 (vypredané)? Zrejme ani v jednom prípade nie je možné knihu zakúpiť.
Stav -1 znamená , že položka je pre B2B zákazníka zakázaná.  

7] ktoré XML tagy sú vždy uvedené a ktoré záznam nemusí mať? Napríklad niektorým záznamom chýbajú tagy AUTHORS, PUBLISHERS, LANGUAGES, IMGURL.
Toto sú všetky možné parametre produktu. Ak ich produkt nemá v našej databáze vyplnené tak sú hodnoty rovné nule.

8] Ak má produkt skladový stav 0 tak je vypredaný/nedostupný?
Áno titul je vypredaný/ nedostupný alebo novinka v predpredaji.

Užitočné REGEX:
---------------
Overenie či STATE 8 ma aj iné B2B_AVAILABILITY ako 3: [8\]\]><\/STATE>.+[012456789]<\/B2B_AVAILABILITY>
Overenie či B2B_AVAILABILITY 3 ma aj iné STATE ako 8: [12345679]\]\]><\/STATE>.+[3]<\/B2B_AVAILABILITY>
Overenie či STOCK_QUANTITY 0 ma aj iné B2B_AVAILABILITY ako 0: >[0]<\/STOCK_QUANTITY>.+>[123456789]<\/B2B_AVAILABILITY>

********************
ZJEDNOTENIE IMPORTOV
********************
Bez ohľadu na úvodný import produktu a na importnú skupinu výrobcu sa budú ku každému produktu importovať data od všetkých dodavateľov (t.j. zo všetkých importov).

Pri importe nových sa naimportujú nové produkty a vytvoria sa záznamy pre daneho dodávateľa. Po importe nebude potrebné spúšťať aktualizačnú/synchronizačnú logiku lebo vytvorené produkty budú v súlade z datami dodávateľov (t.j. zatial jediného dodávateľa)

Pri akomkoľvek aktualizačnom importe (okrem interného) sa vyberú z db párovacie údaje všetkých produktov a aktualizované údaje (cena, dostupnosť, ...) sa zapíšu do dat dodávateľa. Po zbehnutí importu sa musí spustiť synchronizačná logika ktorá aktualizuje stavy dostupnosti naimportovaných produktov. Budú sa aktualizovať len tie, ktoré sa zmenili od poslednej synchronizácie (na základe modified dátumu, čas poslednej synchronizácie bude uložený v nastavení. Synchronizácia sa môže spúšťať ako cron alebo sa spustí na konci samotného aktualizačného importu.

Aktualizačný import bude aktualizovať len dostupnosti. Ceny budú len informátívne, zobrazia sa v detaile produktu v administrácii aby vedeli odkiaľ je najvýhodnejšie produkt objednať. !!! Väčšina aktualizačných importov (pemicPriceUpdate, ikarUpdate, albatrosUpdate, partnertechnicUpdate, informUpdate) importuje aj cenu !!! - Aj napriek tomu sme sa s pani Zjavkovou dohodli, že aktualiovať sa budú len dostupnosti

Po zapracovani horeuvedeného stratia importné skupiny (EshopProduct.manufacturer_group a EshopManufacturer.import_group) význam. Prever to u klienta a v admine ich zakomentuj. V DB nechaj údaje ešte nejaký čas ak by náhodou zistili že predsa... Prípdane tieto údaje zálohuj (id, udaj) aby sa dali v prípade potreby znovú nahrať.

Užitočné SQL dopyty:
--------------------
# zoznam produktov ktore majú viac ako jedneho dodavatela
SELECT b.slug, b.availability, a.* 
FROM `run_eshop_supplier_products` a 
LEFT JOIN run_eshop_products b ON(b.id = a.run_eshop_products_id) 
WHERE 
    a.run_eshop_products_id IN (
        SELECT c.run_eshop_products_id 
        FROM `run_eshop_supplier_products` c 
        GROUP BY c.run_eshop_products_id HAVING COUNT(c.id) > 1
    ) 
ORDER BY a.run_eshop_products_id

# zoznam aktivnych dostupných produktov ktore ma niektory dodavatel skladom
SELECT b.id, b.ean, b.slug, GROUP_CONCAT(a.supplier_pid) AS dodavatelia, GROUP_CONCAT(a.stock) AS skladove_stavy, b.created
FROM `run_eshop_supplier_products` a 
LEFT JOIN run_eshop_products b ON(b.id = a.run_eshop_products_id) 
WHERE 
    a.stock > 0
    AND b.active = 1
    AND b.availability = 'enum_available'
GROUP BY a.run_eshop_products_id
ORDER BY a.run_eshop_products_id

# zoznam aktivnych dostupných produktov ktore nemá žiadny dodavatel skladom
SELECT b.id, b.ean, b.slug, GROUP_CONCAT(a.supplier_pid) AS dodavatelia, GROUP_CONCAT(a.stock) AS skladove_stavy, b.created
FROM `run_eshop_supplier_products` a 
LEFT JOIN run_eshop_products b ON(b.id = a.run_eshop_products_id) 
WHERE 
    b.active = 1
    AND b.availability = 'enum_available'
GROUP BY a.run_eshop_products_id
HAVING
    SUM(a.stock) = 0
ORDER BY a.run_eshop_products_id

# zoznam aktivnych dostupných produktov ktore majú nastavené shipment_time_off_stock na 21 (t.j. ktoré nemá žiadny dodavatel skladom)
SELECT  id, ean, slug, shipment_time_off_stock, created
FROM `run_eshop_products` 
WHERE 
    `shipment_time_off_stock` = 21 
    AND `availability` = 'enum_available' 
    AND `active` = 1
ORDER BY id

SELECT * FROM `run_eshop_supplier_products`  
ORDER BY `run_eshop_products_id`  DESC, (image_import_source IS NULL) ASC


SELECT CONCAT('https://www.vydavatel.sk/kniha/', slug) AS url, created, exported 
FROM run_eshop_products 
WHERE created > '2019-04-18 00:00:00'


SELECT b.slug, b.availability, b.price, b.created, b.exported, a.created 
FROM `run_eshop_supplier_products` a 
LEFT JOIN run_eshop_products b ON(b.id = a.run_eshop_products_id) 
WHERE 
    b.created > '2019-04-18 00:00:00' 
ORDER BY a.run_eshop_products_id

SELECT * 
FROM run_eshop_products 
WHERE ean IN(978801800016,9788081402784,9788097315092)

SELECT id FROM `run_eshop_products` 
WHERE 
    price = 0
    AND created > '2019-04-15 00:00:00'

SELECT * FROM `run_eshop_products` 
WHERE 
    id IN (255476, 255482, 255484, 255485, 255486, 255490, 255491, 255513, 255514, 255516, 255517, 255518, 255521, 255523, 255524, 255525, 255526, 255527, 255528, 255529, 255530, 255531, 255532, 255533, 255534, 255535, 255536, 255537, 255538, 255539, 255540, 255541, 255542, 255543, 255564, 255570, 255574, 255579, 255598, 255599, 255600, 255601, 255602, 255603, 255604, 255605, 255606, 255607, 255608, 255609, 255610, 255619, 255620, 255621)


Návrhy:
-------
- Mohla by sa urobiť funkcionalita na nahranie konkrétneho obrázku (ktorý sa klientovi páči najviac) - v admine by bol link pri každom zo supllier obrazkov a kliknutím naň by sa zmazal starý a nahral nový obrázok

****************
ŠPECIÁLNE PONUKY
****************
- Analýza: Viď https://docs.google.com/spreadsheets/d/1MzsgB1pBV5kFBy5dK3RedR1bR5LgXWxNrIarGMJTxcU/edit?usp=sharing > 180427 - Akcia "Kniha za 1€"

Zobrazenie ŠP pri produktoch
----------------------------
(Nasledovné pravidlá zobrazenia som spísal v 02/2019 a sú mierne odlišné od úvodných
pravidiel uvedených v analýze)

Špeciálna ponuka sa zobrazí pri produkte (v zozname alebo detaile) v týchto prípadoch:

1] Aktívna ŠP uplatnená na promovaných produktov (ŠPA):
     a) sa zobrazí ako promovacia:
         i) len pri jej promovaných produktoch    
         ii) ako aplikovateľná sa zobrazí ak:
             - je v košíku voľný niektorý z jej zľavnených produktov  
     b) sa zobrazí ako diskontná:
         i) len pri jej zľavnených produktoch
         ii) ako aplikovaná sa zobrazí ak:
             - je v košíku voľný niektorý z jej promovaných produktov
          
2] Aktívna ŠP uplatnená na základe ceny košíka (ŠPB):
     a) sa zobrazí ako promovacia:
         i) pri všetkých produktoch    
         ii) ako aplikovateľná sa zobrazí ak (sučasne):
             - je v košíku voľný niektorý z jej zľavnených produktov  
             - cena produktu, pri ktorom sa zobrazuje ("promovaného"), je vyššia ako hraničná cena ŠP
                 @todo: alebo je vyššia rovná ako chýbajúca časť ceny v košíku
     b) sa zobrazí ako diskontná:
         i) len pri jej zľavnených produktoch
         ii) ako aplikovaná sa zobrazí ak:
             - je voľná cena košíku výššia/rovná ako cena zľavneného produktu
          
3] Aktívna ŠP uplatnená na cenu promovaných produktov v košíku (ŠPC):
     a) sa zobrazí ako promovacia:
         i) len pri jej promovaných produktoch    
         ii) ako aplikovateľná sa zobrazí ak (sučasne):
             - je v košíku voľný niektorý z jej zľavnených produktov  
             - cena promovaného produktu je vyššia ako hraničná cena ŠP
                 @todo: alebo je vyššia rovná ako chýbajúca časť ceny v košíku pre danú ŠP
     b) sa zobrazí ako diskontná:
         i) len pri jej zľavnených produktoch
         ii) ako aplikovaná sa zobrazí ak (sučasne):
             - voľná cena promovaných produktov v košíku pre danú ŠP je výššia/rovná ako cena zľavneného produktu

ŠPC je správaním bližšie k ŠPB ako k ŠPA. Ide vlastne o verziu ŠPB.


Pravidlá zaraďovania produktov do ŠP
------------------------------------

Viď app/modules/Eshop/models/EshopSpecialOffer.php -> __construct() > phpDoc k $this->validations


Popis párovania promovaných a zľavnených produktov v košíku a aplikovania zliav
-------------------------------------------------------------------------------

Viď app/modules/Eshop/models/EshopCart.php > blok komentára začínajúci "// SPECIAL OFFERS"
Viď popis v EshopCart::applySpecialOffers() > "find optimal combinations of promoted and discounted products"

V prípade ŠP uplatnených na základe ceny promovaných produktov v košíku sú produkty párované "volne", t.j. všetky zľavnené produkty ponuky majú v poli 'promoted_cart_index' nastavené id-čka všetkých promovaných produktov ponuky nachádzajúcich sa v košíku a naopak. Vníma sa to ako "Všetky tieto produkty sú zlavnené na základe celkovej ceny týchto promovaných produktov". Je to veľmi podobné s ŠP aplikovanou na základe ceny celého košíka. ŠP aplikované na základe ceny promovaných produltov v košíku si všíma len cenu podskupiny produktov ("podkošík"). Výhodné je tiež (z pohľadu výpočtov/programovania), že zľavnené produkty sa na tejto cene "podkošíka" nepodieľajú (čo neplatí pre ŠP na základe ceny celého košíka).

Sú nasledovné možnosti párovania:

// ŠP aplikovaná na základe promovaných produktov v košíku (ŠPA)
0: P1 (special_offer_id = X, discounted_cart_index = 1)
1:  D1 (special_offer_id = X, promoted_cart_index = 0)

// ŠP aplikovaná na základe cený košíka (ŠPB)
2:  D2 (special_offer_id = Y, cart_price_threshold = 10)

// ŠP aplikovaná na základe ceny promovaných produktov v košíku (ŠPC)
3: P2 (special_offer_id = Z, discounted_cart_index = [6, 7])
4: P3 (special_offer_id = Z, discounted_cart_index = [6, 7])
5: P4 (special_offer_id = Z, discounted_cart_index = [6, 7])
6:  D3 (special_offer_id = Z, promoted_cart_index = [3, 4, 5], cart_price_threshold = 15)
7:  D4 (special_offer_id = Z, promoted_cart_index = [3, 4, 5], cart_price_threshold = 15)


Základné kroky aplikácie ŠP na produkty v košíku sú nasledovné (viď EshopCart::applySpecialOffers()):
- overenie platnosti momentálneho stavu (odstránenie úplatnenia ŠP, ktoré už nie su aktuálne kvôli ich aktívnosti, zmene ich apply_by alebo kvôli zmenám v košíku)
    - nájdu sa všetky aktívne ŠP a produkty ktorých special_offer_id nie je medzi aktívymi alebo ktorých apply_by sa zmenilo sa odznačia
    - pre ŠPA sa overí či je spárovaný produkt v košíku
    - pre ŠPC sa overí či sú spárované produkty v košíku
    - pre ŠPB a ŠPC sa overí či je voľná cena košíka rovná a väčšia ako 0 (a v poradí pridania sa odznačuje pokiaľ to nie je ok)
- nájdenie nespárovaných (voľných) produktov v košíku
    - sú to všetky tie ktoré nemajú nastavené ani jedno z promoted_cart_index, discounted_cart_index a cart_price_threshold (special_offer_id môže byť nastavené v prípade zľavnených produktov ako navrhnuté a malo by sa dodržať)
    - pre ŠPC sú to aj všetky promované produkty (majúce nastavené discounted_cart_index) pretože v tomto prípade sú promované produkty zviazané so zľavnenými volne a na základe voľnej/dostupnej ceny promovaných môžu pribudnúť ďalšie zľavnené
- vyhľladanie aplikovateľných ŠP pre voľné produkty v košíku
- aplikácia zliav na voľné produkty
    - Algoritmus optimalizácie: Z pohľadu zákazníka je zaujímavá čo najvyššia (absolútna) úspora na kupovanom zlavnenom produkte a aby to získal za čo najnižšiu cenu promovaného produktu (alebo cenu košíka v prípade ŠP na základe ceny košíka). Výsledná cena zľavneného nie je až tak dôležitá lebo tak či tak si ho chcel zákazník kúpiť:
    P1 alebo cena košíka: 15€
        D1: 10€ -> 5€ => 15 - (10 - 5) = 10
        D2:  5€ -> 2€ => 15 - (5 - 2) = 12
        D3: 100€ -> 30€ => 15 - (100 - 30) = -55
    - Dostupná cena košíka (bez aplikovaných cart_price_threshold-s) sa vypočítava z aktuálnej ceny produktov s DPH. Je to trochu HARDCOED a je to na nasledovných miestach:
        - v metóde EshopCart::getPriceWithoutAppliedSpecialOffer()
        - v metóde EshopProduct::getDetails() > porovnanie "if ($product['price_actual_taxed'] > $offer['cart_price_threshold']) {"
    - celková dostupná/voľná cena sa priebežne upravuje pri vytvorení každého zľavneného produktu
    - po spárovaní produktov sa skontroluje celková dostupná cena košíka a ak je menšia ako 0 tak sa iteruje zlavnenými produktami naspäť a odoberajú sa pokiaľ nie je celková dostupná cena košíka väčšia alebo rovna 0
- spojenie rovnako spárovaných promovaných a zľavnených produktov
- umiestnenie spárovaných produktov vedľa seba (najprv promované, za nimi zľavnené) 

********************************************************
POUŽITIE ŠPECIÁLNYCH PONUK NA PREDAJ SKLADOVÝCH LEŽIAKOV
********************************************************
- pridalo sa zaškrtávatko "Zobraziť v menu"
- banner, štítky, slug a popis sa nastavili ako nepovinné (bez ohľadu na typ ŠP). Je na klientovi čo si vyplní aby sa to mohlo zobraziť (a predať produkty)

***************************************
VÝDAJNÉ MIESTA ZASIELKOVŇA A GEIS POINT
***************************************
- pri zoraďovaní položiek v zozname vydajných miest Zásielkovne a GeisPointu (EshopOrder::getPickupPlacesList(), Zásielkovňa by to ani nepotrebova, GeisPoint určite) som potreboval použiť klasu Collator. Ta je obsiahnutá v php rozšírení php5-intl. Inštalácia na Linuxe:

    sudo apt-get install php5-intl (aktualizuj podľa verzie php: php5.6-intl, php7.0-intl, ...)
    sudo service apache2 reload


***************************************************
PRENOS FAKTUR Z MRP A ODOSLANIE FAKTURY ZÁKAZNÍKOVI
***************************************************
- na generovanie pdf a jpg súborov z html som použil PHP knižnicu https://github.com/mikehaertl/phpwkhtmltopdf ktorá interne používa wkhtmltopdf a wkhtmltoimage (https://wkhtmltopdf.org). Je potrebné má nainštalovanú s "patched Qt" a pokiaľ možno najnovšiu. Možnosti sú nasledovné (Linux):
    1] Inštalácia stiahnutého .deb súboru (PHP bude vidieť priamo príkaz wkhtmltopdf, nebude potrebné zadávať cestu):
        - stiahni najnovšiu verziu z https://wkhtmltopdf.org/downloads.html
        - spusti inštaláciu stiahnutého balíčka: sudo dpkg -i {downloadedPackage}.deb
        - ak ti to hodí chybu počas inštalácie (chýbajúce dependencies) tak spusti: sudo apt-get -f install

    2] Ak by predošlá inštalácia nefungovala tak skús (v PHP bude potrebné zadávať cestu):
        - download last version from https://wkhtmltopdf.org/downloads.html
        - unpack it
        - sudo mv ~/Downloads/wkhtmltox /usr/bin/wkhtmltox
        - sudo chown root:root /usr/bin/wkhtmltox
        - sudo chmod +x /usr/bin/wkhtmltox
        - export PATH=$PATH":/usr/bin/wkhtmltox/bin"
        - and it worked, if not then check https://github.com/mikehaertl/phpwkhtmltopdf or http://blog.hemantthorat.com/install-wkhtmltopdf-on-ubuntu-12-04/

Inštalácia je možná aj cez sudo apt-get install wkhtmltopdf ale verzia tam je staršia (viď apt-cache policy wkhtmltopdf) a nainštaluje sa vo verzii "unpatched Qt" ktorá nepozná options --disable-smart-shrinking.

Samotnú PHP knižnicu https://github.com/mikehaertl/phpwkhtmltopdf som nainštaloval ako vendora cez composer (nie je zahrnutý v repozitári) a po naklonovaníé projektu už stačí len spustiť (súc v projektovom priečinku):
    - composer install

- Pre prípad testovania je v metóde EshopOrder::getInvoiceFile() pripravené kešovanie odpovedí z MRP aby to frčalo rýchlejšie. Stačí to tam odkometovať (sú to 2 miesta - pred a po dopyte do MRP)

