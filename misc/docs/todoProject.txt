**********************************************************
********************* UNISPORT-KOVAC *********************
**********************************************************

******************
VARIANTY PRODUKTOV
******************
x keď máme produkt (ktorý reprezentuje varianty produktu) v zozname produktov a nieje dostupný 
(ale niektorý jeho variant môže byť), tak po zvolení možnosti 'len skladom' sa nezobrazuje
    >> vyriešené - viď EshopProduct::normalizeFindOptions()

********************************
ÚVODNÉ NASADENIE (PRENOS Z ODOO)
********************************

- Doladenie štýlov:
    - cookie banner - farba
    - košík > farby pre zľavový kód
    - posledný krok objednávky > farby tlačidiel "zmeniť"
    - nekonečný skrol produktov (pri refreši stránky > 1) - treba vymeniť logo a farbu
- nastav spôsoby doručenia a platobné metódy podľa odoo verzie:
    - Slovenská pošta (4.50 €)
    - Osobný odber
    - toto by malo isť aj cez .../_debug/addEshopShipmentMethod

    - Bankový prevod
    - Platba na predajni
- Nech si aktualizujú obchodné podmienky
    - v odoo nemali žiadne
x! produkty ktore nie sú v MRP sú vypredané
    x over či sa to tak správa pri importe skladových stavov z MRP (http://unisport/mvc/Eshop/EshopImports/autoimportProductsUpdate/mrpStockUpdate)
        - napr "Bicykel Kenzel Camping 20" nexus hnedý", mrp code 35045
x! Vypredané produkty nie je možné zakúpiť
- zriadiť crony

-? sú prenesené produkty ok?
-? skontrolujú si MRP kódy ručne?
    -? chcú pripraviť nejakú tabuľku obsahujúcu: Názov, MRP kód - aby sa im kontrola uľačila?
-? aké chcú mať spôsoby doručenia?
    -? chcú doručovať len na Slovensko alebo aj do iných krajín?

x? chcú pridať nejakú online platobnú metódu?
    >> zatiaľ nie
x? chcú Meilisearch?
    >> zatiaľ nie


**********************************************************
********************* KARAVANDOPLNKY *********************
**********************************************************

*****
GOPAY
*****
- dopíš návod na rozbehanie platobnej metódy v úvodnom phpDoc-e triedy PaymentMethodGoPay a prenes to na vydavatel, core-v2, chyzbet, alterego, ....

*************
SELFCANONICAL
*************
- skáče warning na adrese http://karavanydoplnky.sk:50013/kategoria/voda/material:umel%C3%A1%20hmota?filter%5Bstock%5D=

**************************
ZLAVOVY KOD PRE PRODUKTY
**************************
x treba upravit itemselector a treeselector nech nesatava pretecenie najdenych poloziek pod spodny okraj.
x pri pridávaní kódu do košíka treba doladiť hlášku "Zľavový kód nie je platný" pre prípad kódov naviazaných na produkty.

**************************
REFACTORING MENU KATEGÓRIÍ
**************************
- opraviť rozbalovanie katégórii v tretej urovni (event na <li class="level-03-button-more">Ďalšie kategórie</li>)
- opraviť slide efekt na kategoriách po rokliknutí hamburgerového menu pri kliknutí na expand button pri šírke < 1000px
- opraviť pozíciu obrázka kategórie v menu keď som prihlásený ako admin


*****************
301 PRESMEROVANIA
*****************

- ked sa vytvoria nejaké presmerovania naplnia sa aj sledovacie data na nich tak dokonči overovanie googlebota v UrlRedirection::isCrawledBy() pomocou gethostbyaddr()
x skontroluj administráciu a zapracovania pridávaní presmerovaní
x po pretestovaní:
    x nastav východziu hodnotu 'addRedirection' na !ON_LOCALHOST
    x oprav podmienku if (true || $redirection['user_ip'] !== $ip) {

*************************
IMPORT PRODUKTOV FRANKANA
*************************
- kategórie sa momentálne neimportujú. Všetky nové produkty sa zaradia do kategórie nezaradené.

x? v API sa nenachádza tax rate pre produkt. Momentálne je všade nastavený na 20. Môže to tak ostať?
    >> áno
-? chcú robiť preklady pomocou chatGPT?
x? Ako často chcú spúšťať import nových? Stačí raz za deň?
    >> nastavil som na každé dve hodiny

x? má sa v /mvc/Eshop/EshopImports/autoimportProductsUpdate/frankanaStockUpdate iportovať aj 'stock'? - toto sa predsa importuje z MRP
    >> nie, stock sa importuje len z MRP

x skontroluj zapracovanie nasledovných metód v projekte a refaktoruj ich novou prekladovou funkcionalitou:
    x EshopProducts::admin_translate()
        >> nikde sa nepoužíva
    x EshopProductCategories::admin_translate()
        >> nikde sa nepoužíva

x ostáva ešte
    x app/modules/Eshop/config/rights.php
    x app/modules/Eshop/controllers/EshopProducts.php > insertRelatedProducts() 
        x EshopProduct.related_products
        x EshopProduct.related_products_done
        x? mohlo by sa to urobiť vrámci úvodného importu?
            >> nie, až po ňom
        x porovnaj EshopProducts::insertRelatedProducts() s .../_debug/convertRelatedProductsToHabtm
    x app/modules/Eshop/models/EshopProductImport.php

-? EshopProduct.tech_data_html je dostupné aj v novom importe cez API?
x? Chcú pridať import obrázkov?
    >> áno
-? Je reálne že by sa niekedy napárovani kategórie
-? Chcú v aktualizačnom importe aktualizovať aj niečo iné ako ceny a skladovú dostupnosť?
x? Chcú aktualizačný import obrázkov a obrázkov fotogalérie?
    >> áno
    
************************
komunikácia z 18.3.2022:
************************
- je potrebné rozbehať aj frankana import nových a nasledne preklady textov z DE do SK
    - treba pridať frankana import nových a preniesť App settings: googleTranslate, EshopProducts::translateTechDataHtml(), cron /mvc/Eshop/EshopProducts/translateTechDataHtml
        - Model::saveBatch() - pridanie option 'includeImportFields' alebo treba preniest saveBatch() z core_v2 (alebo zamerdžovať)
    - chýbajú tam niektore produkty (napr. 206/431)
    - občas sa nestiahnú obrázky - pošlú nám 
    - vyhľadaj aj "frankana" tu nižšie
    x ceny nad 1000+€ sa zobrazujú ako 1+€
- Odladenie MRP (spojenie s MRP)
    - pripojenie
    - párovanie produktov s kartami v MRP a prenos skladových stavov 
- letter-spacing:
    - bezný text: 0
    - detail roduktu nadpis: -0.5px
    - nadpisy: -0.2px

- užší centrálny blok na texty (podľa detailu blogu)

pred spustením:
- nastaviť odosielanie <NAME_EMAIL>
    - Miňo, Bohuš ani Peťo M. heslo nemajú. Spýtaj sa ešte v karavanoch. Potom ho resetni
- tie zvuky doplň
- pri malej výšle obrazovky sa owlSlajder zúži - napr keď si otvoríš konzolu (treba načítať stránku znovu) - odstráň to
- doladenie h1
- odznaky na úvodnej stránke. Pani Gilanová napísala:
    - Doručenie zadarmo nad 200,00 €  - nevieme, či link je úplne vhodným riešením, lebo jediné čo k tomu potrebujeme dodať je to, že sa to nevzťahuje na nadrozmerný tovar, lebo pri nadrozmernom tovare je doprava vyššia. Možno by sa to dalo dať ako poznámka? Alebo to môžeme dať aj ako link, ale nemáme tam moc veľa k tomu ďalšieho povedať.
    - 20 rokov skúseností (Turčan Auto je na trhu už dvadsať rokov – tu by sme mohli my napísať nejaký článok o našej firme, že sme na trhu dvadsať rokov a dali by sme to zverejniť do blogu a link by smeroval teda tam)
    - Vernostný program (tu určite budeme dávať link, kde bude vysvetlené aké výhody dostanú ľudia, keď sa zaregistrujú, toto ale ešte musíme doriešiť s kolegami, aby sme to dobre nastavili a dohodnúť aké výhody budú vyplývať z členstva, na tomto ešte robíme) – toto je vlastne aj tá Vaša otázka smerovaná k bonusu.
    - Tovar skladom odosielame do 24 hodín (sem tiež buď link alebo poznámku, ak sa bude dať, že pri víkendových objednávkach (od piatku poobedia) a sviatkoch odosielame tovar nasledujúci pracovný deň)
    x Prosíme z odznakov vymazať „Vrátenie zadarmo“ – alebo respektíve to dať len neaktívne (ak sa dá), ak by sme sa niekedy do budúcna rozhodli to zmeniť, nech to tam zase nemusíte dávať
    - A ešte máme dotaz k odznaku „Najlepšia kvalita“ – tuším ste nám to už aj vysvetľovali, ale v rýchlosti sme si to zabudli zapísať a už si nespomíname, že čo to bolo (ale tento by sme tiež dali neaktívny zatiaľ).
-?! EshopProduct.seo_description - prečo je v seo_description html? 
    -- SELECT LENGTH(seo_description), seo_description FROM `run_eshop_products` ORDER BY LENGTH(seo_description) DESC;
-?! EshopProduct.seo_keywords - prečo majú keywords dĺžku až 6000 znakov, zdá sa že sú tam duplicity
    -- SELECT LENGTH(seo_keywords), seo_keywords FROM `run_eshop_products` ORDER BY LENGTH(seo_keywords) DESC;
x vymazať opustené obrázky
- menu kategórií v hlavičke sa na začiatku správa divne - urob to rovnako ako na alterego.sk
- MRP - prever export / import do / z MRP
x pošli Peti Kalmarovej nový link na google feed (prever či je zriadený cron)
x aktualizuj crony podľa aktuálnych URL
    - prenes crony z web7 na web8 a zmeň nasledovne adresy cronov:
        - /mvc/Eshop/EshopProducts/exportPriceEnginesXml/googleSK -> /mvc/Eshop/EshopExports/exporPriceEnginesXml/google
        - /mvc/Eshop/EshopProducts/exportPriceEnginesXml/heureka -> /mvc/Eshop/EshopExports/exporPriceEnginesXml/heureka
        - /mvc/Eshop/EshopProducts/importImages -> /mvc/Eshop/EshopImports/autoimportProductFieldsFromSource/image
    -- pôvodné cronjobs na web7:
        10 * * * * https://www.karavandoplnky.sk/mvc/Eshop/EshopProducts/importImages
        10 2 * * * https://www.karavandoplnky.sk/mvc/Eshop/EshopImports/autoimportUpdateStockAndPriceFromFrankana
        */2 * * * * https://www.karavandoplnky.sk/mvc/Eshop/EshopProducts/translateTechDataHtml
        20 2 * * * https://www.karavandoplnky.sk/mvc/Eshop/EshopProducts/exportPriceEnginesXml/googleSK
        15 2 * * * https://www.karavandoplnky.sk/mvc/Eshop/EshopProducts/exportPriceEnginesXml/heureka   
        */20 * * * * https://www.karavandoplnky.sk/mvc/Eshop/EshopOrders/autoexportMrpOrders/1
        */10 * * * * https://www.karavandoplnky.sk/mvc/Eshop/EshopImports/autoimportProductsUpdate/mrpStockUpdate
    x vytvorili sa nasledovné crony na web8:
        00  5   *   *   *   https://www.karavandoplnky.sk/_sitemap/write
        */10    3-23,0-1   *   *   *   https://www.karavandoplnky.sk/mvc/Eshop/EshopImports/autoimportProductFieldsFromSource/image  
        */10    3-23,0-1   *   *   *   https://www.karavandoplnky.sk/mvc/Eshop/EshopImports/autoimportProductFieldsFromSource/description
        00  6   *   *   *   https://www.karavandoplnky.sk/mvc/Eshop/EshopImports/autoimportProductFieldsFromSource/image/0/1  
        15  6   *   *   *   https://www.karavandoplnky.sk/mvc/Eshop/EshopImports/autoimportProductFieldsFromSource/description/0/1
        00  3   *   *   *   https://www.karavandoplnky.sk/mvc/Eshop/EshopExports/exportPriceEnginesXml/heureka 
        20	2	*	*	*	https://www.karavandoplnky.sk/mvc/Eshop/EshopExports/exportPriceEnginesXml/google
        10	2	*	*	*	https://www.karavandoplnky.sk/mvc/Eshop/EshopImports/autoimportProductsUpdate/frankanaStockUpdate
        */10	*	*	*	*	https://www.karavandoplnky.sk/mvc/Eshop/EshopImports/autoimportProductsUpdate/mrpStockUpdate
        */10 *   *   *   *   https://www.karavandoplnky.sk/mvc/Eshop/EshopOrderProducts/updateReservedAmount
        */20 *   *   *   *   https://www.karavandoplnky.sk/mvc/Eshop/EshopExports/autoexportMrpOrders/1
        00  07  *   *   *    https://www.karavandoplnky.sk/mvc/Eshop/EshopWishlists/checkWatchdog
        *    *   *   *   *   https://www.karavandoplnky.sk/mvc/Eshop/EshopOrderProducts/createGiftCardVouchers
        */10 *   *   *   *   https://www.karavandoplnky.sk/mvc/Eshop/EshopOrderProducts/sendExpiringGiftCardVoucherEmails
        *   *   *   *   *   https://www.karavandoplnky.sk/mvc/Mailer/MailerCampaigns/send 
    -! prever zmysluplnosť cronu:
        */2 * * * * https://www.karavandoplnky.sk/mvc/Eshop/EshopProducts/translateTechDataHtml
- skontroluj funkčnosť google api key (viď 2019-01-07_durik.sql)
- Vytvor ručne aspoň jednu vzorovú darovaciu kartu v admine (viď 2021-03-321) - podľa vzoru alterego.sk
- Admin > Eshop > vytvor produkt "Zľavový kód", cena: 1 (je to jedno), aktívny a nastav jeho id v Eshop > Nastavenia > Id produktu "Zľavový kód" (viď 2021-04-16)
    - dohodi sa s Bohušom či im dáme zľavové kódy a dorovacie kraty "len tak" (zadarmo)
- Admin > Eshop > Nastavenia > E-maily objednávky > Nová objednávka - pridaj vsuvku :electronicGiftCardInfo:
x! prever nasadenie microsoft clarity
x .htaccess - použitie vzdialených súborov je momentálne povolené aj na produkčnom projekte (demo). Zakomentuj to
x!!! ceny - preskúmaj prečo má napr. /produkt/vega-375 v novej DB cenu 1.1€ a v produktčnej 1099€
    >> problem bol v chybnom oddeľovači tisícov vo frankana importe
    -- momentálne je to ok lebo som to preniesol z DB starého projektu: UPDATE karavandoplnky_2021_refactor.run_eshop_products a LEFT JOIN karavandoplnky.run_eshop_products b ON (a.id = b.id) SET a.price = b.price;
    -- horeuvedený dopyt aktualizoal len 1086 produktov zo všetkých 17000
        -  možno sa väčšie časť cien už opravila pri skúšaní frankana aktualizačného importu (alebo naopak)
    -- či to náhodou nevznikne pri synchronizácii
x zmaž lazy varianty obrázkov a nechaj ich pregenerovať
    x! EshopProduct.image - pri niektorých produktoch chýbajú original varianty obrázkov, treba ich nahradiť large variantmi (napr. produkt id 9008)
x <NAME_EMAIL>
    >> chcú <EMAIL>
x prever hodnoty nastavení
x Admin > Eshop > Nastavenia > Emaily - všetky vsuvky :userName: nahraď za :userFirstName: (V maili objednávky je omnoho krajšie oslovenie "Dobrý deň Peter, ..." ako "Dobrý deň Peter Novák, ...")
- V textoch kampaní Mailera a textoch nastavení Mailera aktualizuj vsuvky nasledovne:
    x ':view:' -> ':viewUrl:'
    x ':unsubscribe:' -> ':unsubscribeUrl:'
    - ':firstname:' -> ':firstName:'
    - ':lastname:' -> ':lastName:'
    - ':company:' -> ':companyName:'
x V nastaveniach Mailera aktualizuj "Zakázané domény kontaktov" nasledovne:
    0815.ru,126.com,163.com,21cn.com,aol.com,banhim.com,believesex.com,buytwitterfollowersreviews.org,dispostable.com,dressesw.com,eyou.com,fast-email.com,loginadulttoys.com,mailcatch.com,mailmetrash.com,mailnesia.com,mailtothis.com,mmmmail.com,naturalvitiligotreatmentsystemreview.com,onsaleadult.com,pickadulttoys.com,qip.ru,qq.com,reallymymail.com,sina.com,sogou.com,sohu.com,spamavert.com,spambog.com,star.com,tea-tins.com,tom.com,trash-mail.com,truthaboutcellulitereviews.com,visionwithoutglassesscam.org
x! treba doriešiť veľkosť obrázkov produktov a ich zobrazenie v detaile
x EshopProductCategory.image má na starom webe veľa variantov no tie sú zrejme ešte z autolekar.sk - zmaž ich (t.j. zmaž /web/userfiles/Eshop/EshopProductCategory)
x Skontroluj vyhľadávanie
x oprav logo
x pridaj na stránku výsledkov vyhľadávania breadcrumbs
x ckeditor - skús nastaviť inportovaným .css timestamps aby sa načítali nové
X Zoznam produktiv (cb) - dorob možnosť aby sa najpredávanejšie dali zobrazovať aj bez výberu 
    >> toto nefungovalo ani na pôvodnej stránke, zobrazovali sa produkty so skupiny id 1
x prečo sa nezobrazuje blogový článok
x WebContent.locator - normalizácia absolutnej lokálnej na relatívnu URL

- na záver:
    - prenes cb obrázkových bannerov a ContentBlockWebContentsAccordion do core
    - odlaď v core .htacces:
            # Out of service (if site is e.g. under maintenance, then return for any request maintenance.php)
            # BUT allow access to following URLs    
        #     # - if request for existing file
        #     RewriteCond %{REQUEST_FILENAME} !-f
        #     # - if request for file in userfiles
        #     RewriteCond %{REQUEST_URI} !^/userfiles/ [NC]
        #     # - if URL starting by /_debug/
        #     RewriteCond %{REQUEST_URI} !^/_debug/ [NC]
            RewriteRule ^(.*)$ maintenance.php [L]
    -x do Tool::deleteEshopProductOrphanFiles() pridaj aj možnosť zmazať aj obrázky deleted produktov (momentálne sa to vlastne udeje lebo tam nie je 'ignoreSoftDeleted' => false,)
    x doplň normalizáciu jpeg/jpe na jpg aj pri ukladaní obrázkov

prezenácia klientovi:
- "teraz to treba ponaplňať"
- spusti nahrávanie videa
- ukáž  dizajn úvodnej stránky vs navrh (https://xd.adobe.com/view/69ee44f5-4f43-433a-9e8a-e45caf7c0248-3cbd/specs/). Ukáž aj o ako to máš cvične na lokálhoste
- dosť záleží na obrázkoch
- "pôjdeme of vrchu"
- ukáž naplňanie hlavného menu
- ukáž vytvárane obsahových blokov 
    - upozorni ich na použitie compressor.io pre obrázky
- ukáž vytváranie tabov
- ukáž vytváranie partnerov
- ukáž použitie iných CB
- ukáž ako sa testuje responzivita
- otázky
- ich úlohy

otázky:
-? ako nastaviť bonus (na akú výškuzaplatených produktov a akú výšku bonusu)?
x? chcú všetky obrázky rovnakej výšky alebo môžu výšky skákať? Prípadne sa to bude dať prepínať.
    >> chcú rovnakú výšku
x? vytvorí<NAME_EMAIL> - dohodli sme sa tak
    >> vytvorili sme eshop@...
x? čo sú to za podkategórie v "Markízy Fiamma"? (https://www.karavandoplnky.sk/kategoria/fiamma)
    >> už ich prehodili
-? ako je to s použitím MRP - stále nám chodia logy o zlyhaní pripojenia
-? Detail produktu - používajú sa niekde tieto oznámy?:
    -- .oversized-warning (style="padding-left: 32px; display: inline-block; margin-top: -20px;")
    -- .covid-warning (style="color: red; padding-left: 32px; display: inline-block; margin-top: -20px;")
    - ak áno tak im tieto štýly pridaj
x? môžeme zmazať opustené obrázky produktov (aj tých čo boli zmazané)?
    >> áno, zmazané
x? preniesť aj presmerovania z htaccess?
    >> áno - hotovo
- Synchronizácia supplier produktov 
    -? aký shipmentTimeOffStock chcú nastaviť ak sú produkty u všetkých Frankana skladoch offstock ($allSuppliersOffStock === true)?
    -? aký shipmentTimeOffStock chcú nastaviť ak sú produkty na niektorom z Frankana skladov?
x? Chcú mať "Platba v hotovosti alebo kartou na predajni" alebo "Platba v hotovosti"?
    >> dal som to prvé

klientové úlohy:
- nech si napíšu text k bonusu
- nech si vytvoria blogové články
- nech si upravia slajd s oznamom pre pandemiu
- nech si vyplnia odporúčané a výpredajové produkty v taboch (a potom ich aktivujú)

Naše úlohy (po google meetingu):
- pošli klientovi link na dizajn a rozmery obrázkov pre jednotlivé obsahové bloky (alevo skontroluj hinty a daj im vedieť že tam to nájdu)
- vyrob skupinu 'bloggers' (pozri na IPE ake je to tam) a nastav jej práva na administráciu blogových článkov (najlepšie len tých ktoré patria danému úžívateľovi)
- CB zoznam produktov - odlaď bez stránovania, 4 produkty
    - kde je filter?
- odlaď stránkovanie produktov (počet produktov - viď posledný vložený CB na homestránke na localhoste)
- odlaď nahľad na CB slajder produktov
x premiestni poskupiny kategórie "Markízy Fiamma" do nošičov na bicykle - spýtaj sa ich kam presne. Urb to v DB, je toho veľmi veľa.
x pätičkové menu urobiť a premiestniť obchodné podmienky
x odladiť responzívne medzery v CB
x multiselecty produkov - nech sa da hľadať aj podľa kódu, kód nech sa pripojí pred názov
x vyhľadávanie produktov na fronte aj podľa kódov (pozri ako to bolo na starej stránke)

tieto otázky sa zatiaľ pýtať nebudeme:
-? chcú mať v pätičke copyright
-? Chcú používať EshopProduct.new alebo EshopProduct::IS_NEW_SQL?
    >> momentálne as používa (tak ako na starom projekte) EshopProduct.new. Vyhľadaj IS_NEW_SQL - je vždy zakomentované a nahradené za EshopProduct.new
-? môžeme kontaktovať frankanu napriamo? Pána Markus Quasigroch, IT department, <EMAIL>
-? aké chcú kategórie blogových článkov?
    - nastav to vo WebContent::getSmartFormForArticle() > aux_01
-? chcú mať aj nejaký filter blogových článkov podľa kategórie?
-? chcú kategóriu blogového článku zobrazovať aj v zozname článkov?
-!!! Zdá sa že filtrovateľné parametre (zvolené v EshopProductCategory.product_parameters a zadané v admin formulári produktu) sa nakoniec nikde nepoužívajú (je otázne či vôbec vo filtri)
    -? zobraziť ich v detaile produktu?
        -! niektoré z nich môžu mať duplicitu v tech_data_html
-! prever ako je to s prepravou oversized produktov na starom webe vs ako sa to podarilo preniesť na nový (vyhládaj "oversized"/i vo všetkých *.php súboroch )
    - možno by to chcelo v druhom kroku objednávky zobraziť možnosti ako pri doprave do zahraničia 
    - skontroluj štýly .oversized-warning v detaile košíka
-? ako sa budú objednávať produkty "Na dopyt"? Urobiť tam nejaký button "Odoslať dopyt"?

požiadavky klienta:
- združenie niektorých produktov do jedného (napríklad markízy s rôznymi farbami)
    - v MRP si vyberú veľné pole a budú do neho vpisovať id/kat.č. hlavného produku
    -! dohodli sme sa že po vybraní poľa to najprv otestujeme či sa dané pole prenáša cez MRP API aj na web
- platobná metóda

návrhy / vylepšenia:
-! skontroluj onsite SEO podľa svetnapojov, martinus, procamp.cz (tie konkurenčné stránky)
- obrázkovým bannerom by sa mohlo pridať aj horizontalne a vertikalne zarovnanie obrázkov (viď Vlastnosti bloku > obrázok pozadia)
- možno by sa mohli resume v zoznamoch produktov orezávať aj pri window resize (?)
- ak sa detail blogového článku zobrazí z odfitrovaného zoznamu článkov danej kategórie tak v sekcii "Dalšie články" by mohli byť len články z danej kategórie
- zoradenie produktov podľa odporúčaných - treba pridať pole EshopProduct.recommendation_index a produkty zoraďovať zostupne podľa najväčšieho odporúčania (buď sa to obmedzí na hodnoty 0-10 alebo sa to nechá na kleintovi ale 0-10 je asi lepšie)
- hlavičku urob na mobilnej verzii zásúvaciu
- užívateľské menu - pre užívateľky by mohla byť "she-icon" s copom
- Zo stránky zoznam produktov som odstránil snippet <object _snippet="Eshop.EshopProductCategories.index" _snippet_generic="1" _snippet_name="Kategorie"></object>. Jeho náhrada je #subcategories menu ktoré sa generuje priamo v hlavičke zoznamu produktov. Ak by to malo byť nezávislé tak treba naštýlovať Eshop.EshopProductCategories.index
- MRP - ak by bolo potrebné aj párovanie podľa 'code' (nie len 'mrp_code') tak pozri karavandoplnky > rev a11c1a02d297
- iné nápady: https://docs.google.com/document/d/1H-oZLkeewCtQ7kJt-RR0S0EvT3QQaylek86l8V1iBNk/edit

nedokončené:
- keď naštýluješ blog (tam by mal byť použitý hlavný a bočný obsah) tak prekontroluj štýly zoznamov produktov ak by sa vložili do hlavného obsahu
- /mvc/Eshop/EshopProducts/importImages treba migrovať na /mvc/Eshop/EshopImports/autoimportProductFieldsFromSource/image 
- prenes EshopImports::admin_importFrankanaCategories()
    -? používajú to?
- import produktov z Frankany 
    - textové info o rozmeroch importuj do 'dimensions' namiesto do 'length'
    - prever potrebu poli EshopProduct > import_ z ohľadom na SupplierProduct
    - urob popis frankana importu
    - ::loadPairFieldValues() - odstráň zapracovanie 'language'
    x vyhodnocovanie frankana_stock_status (vyhľadaj na starom webe) prenes do FrankanaAvailabilityCsvReader-a
- preniesť opravu horného menu (vravel o tom Peťo) do alterego.sk
- Produkt - zosúlaď typy v DB a v scheme pre polia weight ... dimensions, 

x blog
    x premiestni ho z footra do hlavného obsahu aby tam bolo tiež to pozadie s ikonkami. Buď to urob napevno v layoute alebo cez snippet
    x po dokončení responzivity nastav veľkosti variantov
        x nastav v admin formulári min veľkosti obrázku pre zoznam a pre detail článku
    x oprav margim na dátume (daj ho na obrázok alebo na .article-text)
x bonus by sa mohol napĺňať odspodu (veci sa napĺňajú odspodu)
x zoznam produktov:
    x doladiť farby stavov dostupnosti (čakám na Igora)
    x? technické údaje pod názvom produktu zobrať odkiaľ? Alebo na to urobiť nejaké osobitné pole s výberom podstatných parametrov?
        >> urobil som to cez subtitle (Dodatok názvu)
            x treba ho ešte v zozname produktov naštýlovať
x detail produktu:
    x nastavil som šírky ľavej a pravej časti (.images a .texts). Treba ísť rad za radom podľa dizajnu (a pôvodných alterego css) a  doštýlovať to
x Mail Igorovi:
    x png/svg pre ikony termosky, mapy a karavanu
    x image placeholder (pre zoznamy aj detail produktu)
x breadcrimbs a filter produktov - vymeniť ikonku ">" (čakám na Igora)
x!!! zdá sa že dostupnosť predpredaj e na starom webe zobrazená ako "na dopyt" - viď napr. /produkt/cestovna-markiza-capri-north-12 (áno, tento produkt má availability enum_presale
    x poľu availability pridaj hodnotu enum_on_demand, touto nahraď všetky enum_presale a pridaj disponibility, zapracuj v indexoch a detaile produktu a uprav
x Detail produktu - naštýluj tech_data_html v detaile produktu
x Filter produktov (Eshop/views/EshopProducts/filter.php) - treba ešte preniesť / doladiť štýly

x! nastav _placeholder.jpg pre App a Eshop userfiles !!!Momentálne je tam alterego placeholder!!!
x Comment.email - skontroluj zapracovanie do pridávania komentárov
x Prever či najeké obsahy majú mať bočný obsah. Ak nie tak spusti:
    UPDATE `run_web_contents` SET `has_side_content` = 0;
x breadcrumbs - namiesto home ikony by mohla byť ikona karavanu (ako je v košíku) (?) - prípadne pozri ikony v /media/Windows/Users/<USER>/Documents/Práca/Projekty/Karavandoplnky.sk/Redizajn 2021/Dizajn/obrázky/ , tie ...-red čo sú tam  

x spoj .po súbory
    x msgcat --use-first EshopAdmin_sk_SK.po ../../../../../karavandoplnky/app/modules/Eshop/locale/EshopAdmin_sk_SK.po -o EshopAdmin_sk_SK.po
    x msgcat --use-first EshopValidations_sk_SK.po ../../../../../karavandoplnky/app/modules/Eshop/locale/EshopValidations_sk_SK.po -o EshopValidations_sk_SK.po
    x msgcat --use-first Eshop_sk_SK.po ../../../../../karavandoplnky/app/modules/Eshop/locale/Eshop_sk_SK.po -o Eshop_sk_SK.po
x?! EshopProductImport::importUpdate() > 'pairMrp' option a MrpStockReader > 'itemCodes' option - čo to znamená?
    >> používa sa to len na úvodné spárovanie kódov
        - nenašiel som všek miesto kde sa tie kódy zadajú
    x treba tam ešte zapracovať použitie mrp_code (to sa práve nastavuje na základe 'pairMrp')
x? FrankanaStockCsvReader > frankana_stock_1 / frankana_stock_2 / freiko_stock - čo znamenajú tieto údaje? Je to stav skladu? Alebo to je nejaky status kód?
    >> je to stav
x prenes export do heuréky > `// zaraďuje len produkty v  tých kategóriách ktoré majú zadané heureka_name`
x prenes export do googlu (prenes to ale z drinkcentra a porovnaj) a zriaď cron
    x?! používajú to? >> áno
x import produktov z Frankany 
    x?! odkiaľ (z akého katalógu) importujú nové produkty? >> z SFTP - no medzičasom sa zmenil
x dorieš / optimalizuj bg image stránky (viď main.less > body > #page > background-image)
x nastav šírku @main-content-width a @side-content-width
    -- vzorový blogový článok má sirku len 1170px rozdelených na 870 + 300 px ...? Možno urobíme aj o 20px užší center

Hlavička:
x responzivita: search box skry trochu skôr (aby sa nedotýkalo loga, nech tam vždy ostane aspoň taká medera ako na druhej strane k ikone užívateľa)
x pozadie oznamu (hore nad hlavičkou) nastav na @color-dark-green
x responzivita: doštýluj otvorené hamburger menu:
    x farba pozadia "Top kategórie" nech je @color-dark-green alebo červená (čo bude krajšie)
    x treba to posunúť trochu nižšie nech to nezasahuje do loga, nech je to presne pod svetlošedým pásom ako má byť
x bonus > progres - z toho koliečka to treba prerobiť na zapĺňanie srdiečka
x searchbox > číslo 20000 v placeholderi je napevno - nech sa vytiahne skutočný počet z DB - 
    findCount() + conditions EshopProduct::getPublishedConditions()
x telefónne čísla v hlavičke - prečo pomlčka ako oddeľovač? Nie je čiarka prirodzenejšia? Vhodé je pridať príklad cez <code>...</code>
x? less premenná @indent sa použila len na jednom mieste?
    >> zrušil som ju
x jedny kontakty sa generujú v /app/views/Settings/mainContacts.php druhé v /app/modules/Eshop/views/elements/contacts.php. Väčší poriadok by v tom bol (kedže .../Settings/mainContacts.php už existovali) keby sa tie druhé vytvorili ako .../Settings/footerContacts.php
x košík > počet položiek - dopixlovať nech je to presne podľa dizajnu
x hlavné menu:
    x príblížiť položky (asi padding <a> na cca 10px)
    x sub menu rozbaľovača nech má vždy aspoň výšku rozbaľovača
    x ľavý padding podľa dizajnu

x do hlavného menu pridaj položku "Kategórie" tak ako je na alterego.sk
X položky v hlavnom menu zarovnaj do ľava (nie rozprestrené cez celú sírku)

Pätička:
x horný padding 54px
x dolaď aj horný padding položiek v stĺpcoch
x skry platobné metódy a copyright
x menu odznakov

Zoznam produktov:
x prever použitý variant obrázkov (aj s možným navýšením rozmeru pri responzivite)
x? chcú mať možnosť aj prepnúť na detailný zoznam?
    >> áno, nechal som to
x? nechať tam aj tie podkategórie (pod hlavičkou) ako sú na alterego.sk?
    >> áno, nechal som to

Slajder produktov:
X treba nastaviť štýly (a js breakpoint) ak je zobrazený v hlavnom obsahu (vedľa bočného) tak aby to vyzeralo ako keď je na plnú šírku. V takomto prípade bude max počet viditeľných položiek 3.

Detail produktu: 
x opravit zbalenie popisu
x dotiahnuť údaj Doprava v tabuľke pod cenou


---------
Migrácia:
---------

x screen sideBanners a kontroler SideBanners sa neprenáša!

http://localhost:8000/rev/e07b564ff7f2:ac2bc7e61640:
----------------------------------------------------
(!!! Všetky tie "+" sú vykopírované z diffov - nemá to žiadny iný význam !!!)

X livechat a jeho naštýlovanie (/app/css/less/liveChat.css)
    >> toto sa na karavanoch nepoužíva
x? boli tam nejaké obsahové bloky?
    >> len tie základné
x eshop crons:
    +X 20  2   *   *   *   /mvc/Eshop/EshopExports/exporPriceEnginesXml/najnakup
    +  50  2   *   *   *   /mvc/Eshop/EshopExports/exporPriceEnginesXml/heureka
    +X 30  2   *   *   *   /mvc/Eshop/EshopExports/exporPriceEnginesXml/pricemania
    +X 40  2   *   *   *   /mvc/Eshop/EshopExports/exporPriceEnginesXml/tovarsk
    +x */10	*	*	*	*	/mvc/Eshop/EshopImports/autoimportProductsUpdate/mrpStockUpdate
    +x */20	*	*	*	*	/mvc/Eshop/EshopOrders/autoexportMrpOrders/1
- eshop rights:
    - použitie jednotlivých metód treba skontrolovať vyhľadáním v kóde, ak sa nenájde žiadne volanie tak to zatiaľ neprenášaj. Crony sú označené

    +            'EshopProducts.importImages' => true,                          // cron
    +X            'EshopProducts.insertionRelatedProducts' => true,              // nepoužité
    +X            'EshopProducts.translateAll' => true,                          // nepoužité
    +            'EshopProducts.translateTechDataHtml' => true,                 // cron
    +X            'EshopProducts.translateGermanNames' => true,                  // nepoužité
    +X            'EshopProducts.translateGermanDescription' => true,            // nepoužité
    +X            'EshopProducts.translateGermanShortDescription' => true,       // nepoužité
    +X            'EshopImports.pairMrpNumbers' => true,                         // nepoužité
    +x            'EshopImports.autoimportUpdateStockAndPriceFromFrankana' => true,  // cron
    +x            'EshopOrders.autoexportMrpOrders' => true,                         // cron
    +x            'EshopProducts.admin_bulkActions' => true,                     // POUŽITÉ
    +x            'EshopProducts.relatedProducts' => true,                       // nepoužité - slúži na prepísanie related_products do HABTM >> už som to migroval
    +X            'EshopProducts.fillEmptySlugs' => true,                        // nepoužité
    +X            'EshopProducts.removeDuplicityProducts' => true,               // nepoužité
    +X            'EshopProducts.repairFrankanaImport' => true,                  // nepoužité
    +X            'EshopProducts.repairManuallyAddedProducts' => true,            // nepoužité
    +x            'EshopOrders.admin_exportMrpOrders' => true,                   // nepoužité ale mohlo by sa použiť (toto zrejme na novom už aj bude fungovať)
    +x            'EshopOrders.admin_exportMrpOrdersXml' => true,                // nepoužité ale mohlo by sa použiť (toto zrejme na novom už aj bude fungovať)
    +x            'EshopOrders.admin_downloadMrpFile' => true,                   // nepoužité (toto zrejme na novom už aj bude fungovať)
    +            'EshopImports.admin_importFrankanaCategories' => true,         // POUŽITÉ

x eshop settings:
    +    // MRP server IP for automatic imports/exports
    +    'mrp.serverIpAddress' => array(
    +        'value' => '',
    +    ),
    +    // MRP server port for automatic imports/exports
    +    'mrp.serverPort' => array(
    +        'value' => '',
    +    ),
    +    // MRP server private key for encryption of automatic imports/exports
    +    'mrp.privateKey' => array(
    +        'value' => '',
    +    ),
    +    // MRP last number
    +    'mrp.lastCardNumber' => array(
    +        'value' => null
    +    ),
    +    // Eshop Cart oversized products
    +    'cart.oversizedProductMessage' => array(
    +        'value' => 'Cena za dopravu pri tomto tovare bude vzhľadom na nadštandardný rozmer/váhu vypočítaná individuálne. Cena Vám bude oznámená na odsúhlasenie nasledujúci pracovný deň.'
    +    )

x EshopCarts::view():
    
    +        $containOversized = $Cart->containOversized();

X EshopImports::pairMrpNumbers()
    x? potrebujú to ešte? Nebolo to len úvodné spárovanie?
        >> zdá sa že nie, že sa to použilo len na nejaké jednorázové čosi
x EshopImports::autoimportUpdateStockAndPriceFromFrankana()
x EshopOrders::admin_printDetail() - pridanie 'EshopProduct.code', do findu (r. 760)
x EshopOrders::checkout():
    +        $wp['containOversized'] = $Cart->containOversized();

    +            // zľava pri objednávke nad 300eur
    +            if($wp['EshopCart']['products_price_actual_taxed'] > 300 || $wp['containOversized']){
    +                $shipment['price'] = 0;
    +                $payment['price'] = 0;
    +            }

    +            if($wp['containOversized']){
    +                $payment['price'] = 0;
    +            }
x EshopOrders::admin_exportMrpOrders()
x EshopOrders::admin_exportMrpOrdersXml()
x EshopOrders::autoexportMrpOrders()
x EshopOrders::admin_downloadMrpFile()
x EshopProductCategories::admin_edit():
    
    +                array('row', 'columns' => array(3,6)),
    +                    'code' => array('label' => __a($this, 'Category Code')),
    +                    'product_parameters' => array(
    +                        'label' => __a($this, 'Viewed product parameters'),
    +                        'hint' => __a($this, 'Viewed parameters for products classified in this category'),
    +                    ),
    +                array('/row'),
x EshopProducts::index()/indexSearch():
    + 'showDisponibility' => true,
x EshopProducts::view()
    +                'filterFields' => $Product->filterFields,
x EshopProducts::admin_index() - pozri si celý horeuvedený diff, je tam množstvo zmien
x EshopProducts::admin_edit() - pozri si celý horeuvedený diff, je tam množstvo zmien
x EshopProducts::filter() - pozri si celý horeuvedený diff, je tam množstvo zmien
    -! treba ešte preniesť štýly
x EshopProducts::relatedProducts() - konverzia 'related_products' na HABTM
    x odlaď a spusti to nad existujúcimi a potom to prerob tak že 'related_products' odstániš 
        - a HABTM sa bude vytvárať priamo pri importe
X EshopProducts::insertionRelatedProducts() >> nepoužité
x EshopProducts::admin_bulkActions()
X EshopProducts::translateAll() >> nepoužité
X EshopProducts::translateGermanNames() >> nepoužité
X EshopProducts::translateGermanDescription() >> nepoužité
X EshopProducts::translateGermanShortDescription() >> nepoužité
X EshopProducts::fillEmptySlugs() >> nepoužité
X EshopProducts::removeDuplicityProducts() >> nepoužité
X EshopProducts::repairFrankanaImport() >> nepoužité
X EshopProducts::repairManuallyAddedProducts() >> nepoužité
x EshopSettings::admin_edit():
    +                array(
    +                    'h2' => __a(__FILE__, 'MRP - autonómny režim'),
    +                    'hint' => __a(__FILE__, 'Slúži na automatický prenos skladových stavov z MRP na web a prenos objednávok z webu do MRP')
    +                ),
    +                array('row', 'columns' => array(2, 2, 6, 2)),
    +                'Eshop/mrp/serverIpAddress' => array(
    +                    'label' => __a(__FILE__, 'Verejná IP adresa servera'),
    +                    'hint' => __a(__FILE__, 'Server musí mať pevnú verejnú IP adresu. Táto sa dá zistiť zavolaním <code>http://whatismyip.cz/</code>. V routeri lokalnej siete musí byť urobený forwarding z vonkajšieho portu verejnej IP adresy na MRP port lokálnej IP adresy servera. Lokálnu IP adresu servera je možné zistiť pomocou konzolového príkazu <kbd>ipconfig</kbd>. Server musí mať tiež nastavenú pevnú lokálnu IP asresu (cez nastavenia jeho sieťovej karty)'),
    +                ),
    +                'Eshop/mrp/serverPort' => array(
    +                    'label' => __a(__FILE__, 'Port'),
    +                    'hint' => __a(__FILE__, 'Číslo portu, na ktorom MRP očakáva požiadavky autonómneho režimu'),
    +                ),
    +                'Eshop/mrp/privateKey' => array(
    +                    'label' => __a(__FILE__, 'Kryptovací kľúč'),
    +                    'hint' => __a(__FILE__, 'Kľúč služiaci na zakryptovanie komunikácie autonómneho režimu. Skopírujte ho sem tak ako je vygenerovaný v MRP.'),
    +                ),
    +                'Eshop/mrp/lastCardNumber' => array(
    +                    'label' => __a(__FILE__, 'Posledné číslo karty'),
    +                    'hint' => __a(__FILE__, 'Číslo poslednej karty, ktorá bola importovaná do MRP'),
    +                ),
    +                array('/row'),
    +                array(
    +                    'h2' => __a(__FILE__, 'Nákupný košík'),
    +                    'hint' => __a(__FILE__, 'Nastavenia košíka')
    +                ),
    +                array('row'),
    +                'Eshop/cart/oversizedProductMessage' => array(
    +                    'label' => __a(__FILE__, 'Správa v košíku s nadrozmerným produktom'),
    +                    'hint' => __a(__FILE__, 'Správa bude zobrazovaná v košíku s nadrozmerným produktom na miesto informácií o doprave zdarma'),
    +                ),
    +                array('/row'),
x Eshop/libs/EshopMrpQuickOrderAddressUniqueId.php
x Eshop/libs/FrankanaCsvReader.php
    x toto by mal byť Frankana availability reader
- Eshop/libs/InternalCatalogueCsvReader.php
    - toto by mal byť Frankana Catalogue reader
x Eshop/libs/MrpDataEncoding.php + Eshop/libs/MrpRequest.php + Eshop/libs/MrpResponse.php + Eshop/libs/MrpStockReader.php
x EshopCart::containOversized()
x EshopExport::exportMrpOrdersXml() + outputMrpFile() + sendNewItemsEmail() + getNewMrpNumber()
- EshopOrder:
    x pridaj pole exported
    x ::normalizePhone()
    x ::add():

        +        // set Shipment and Payment prices to 0 if order contain oversized product
        +        if ($Cart->containOversized()) {
        +            $shipment['shipment_price_actual_taxless'] = $shipment['shipment_tax_actual'] = $shipment['shipment_price_actual_taxed'] = 0.0;
        +            $payment['payment_price_actual_taxless'] = $payment['payment_tax_actual'] = $payment['payment_price_actual_taxed'] = 0.0;
        +        }
            - neprenášal som ale toto by malo byť v poriadku na základe toho že order sa vyhodnocuje ako specific, prever to 

    x ::getInserts():

        +x        $Cart = $this->loadModel('EshopCart', true);
        +x        $containOversized = $Cart->containOversized($order['id']);

        +x            'oversizedMessage' => $containOversized ? $this->getSetting('cart.oversizedProductMessage') : ''
    
    x ::getProductsDetails() - pridaj option 'cleanCode'
        x premenoval som ho na 'normalizeCode'
    x ::getProductsInfoString():

        +            // disponsibility
        +            if($product['disponibility'] == 'enum_presale') {
        +                $infoString .=  ' | <span style="color:red">' . __($this, 'o termíne dodania tohto tovaru Vás budeme informovať') . '</span>';
        +            }

x EshopOrderProduct::getDetails() - pridaj option 'cleanCode'
x EshopProduct:
    x aktualizuj schemu
    x pozri si celý horeuvedený diff, je tam množstvo zmien
- EshopProductCategory
    x aktualizuj schému:
        x 'code' => ...
        X 'fontawesome_icon' => array('type' => 'varchar', 'length' => '20', 'default' => null),
            >> toto sa nikde nepoužíva, takže to neprenášam
        x 'product_parameters' => array('type' => 'text', 'default' => null, 'comment' => 'Viewed parameters for products classified in this category'),
            -? nebolo by vhodné zvolené parametre kontrolovať nie len na strane admin formularu produktu ale aj na frontendovej strane (zrejme vo filtri)?
        x 'import_source' => array('type' => 'varchar', 'default' => null),
            - premenuj na supplier_pid tu a pri prenose EshopProductImport::importFrankanaCategories() 
                - nikde inde sa to nepoužíva a tak je otázne či to má vôbec zmysel nechávať (spýtaj sa Peťa)

- EshopProductImport
    +    protected $ignoreProductsWithoutCategory = true;
    
    +            'batchSize' => 200,
    - pozri si celý horeuvedený diff, je tam množstvo zmien
x EshopSetting - validácie:

    +            'Eshop/mrp/serverIpAddress' => array(
    +                array(
    +                    'rule' => 'notEmptyIfMrp',
    +                    'force' => true,
    +                    'message' => __v(__FILE__, 'Zadajte'),
    +                ),
    +                array(
    +                    'rule' => '/^[0-2]?[0-9]{1,2}\.[0-2]?[0-9]{1,2}\.[0-2]?[0-9]{1,2}\.[0-2]?[0-9]{1,2}$/',
    +                    'message' => __v(__FILE__, 'Zadajte platnú IP adresu'),
    +                ),
    +            ),
    +            'Eshop/mrp/serverPort' => array(
    +                array(
    +                    'rule' => 'notEmptyIfMrp',
    +                    'force' => true,
    +                    'message' => __v(__FILE__, 'Zadajte'),
    +                ),
    +                array(
    +                    'rule' => 'intNumber',
    +                    'message' => __v(__FILE__, 'Zadajte platné číslo portu od 0 do 65536'),
    +                ),
    +                array(
    +                    'rule' => array('between', 0, 65536),
    +                    'message' => __v(__FILE__, 'Zadajte platné číslo portu od 0 do 65536'),
    +                ),
    +            ),
    +            'Eshop/mrp/privateKey' => array(
    +                array(
    +                    'rule' => 'notEmptyIfMrp',
    +                    'force' => true,
    +                    'message' => __v(__FILE__, 'Zadajte'),
    +                ),
    +            ),

    +    public function validate_notEmptyIfMrp($fieldValue, $fieldName, $data) {
    +        if (
    +            (
    +                !empty($data['Eshop/mrp/serverIpAddress'])
    +                || !empty($data['Eshop/mrp/serverPort'])
    +                || !empty($data['Eshop/mrp/privateKey'])
    +            ) && empty($fieldValue)
    +        ) {
    +            return false;
    +        }
    +        return true;
    +    }

x Eshop/views/EshopCarts/view.php - provnaj to na diffe
x Eshop/views/EshopExport/mrpOrdersXmlFooter.php + mrpOrdersXmlHeader.php + mrpOrdersXmlProducts.php + mrpOrdersXmlRecord.php + admin_exportMrpItems.php
x Eshop/views/EshopOrders/admin_printDetail.php
x Eshop/views/EshopOrders/checkoutStep02.php - zapracovanie oversized (viď diff)
x Eshop/views/EshopOrders/checkoutStep03.php - zapracovanie oversized (viď diff)
x Eshop/views/EshopProducts/filter.php
x screens/run.php

Aktualizácia DB:
----------------
- porovnaj schemy starého a nového projektu a prenes polia ktoré sú na starom projekte do nového
    x EshopProduct - pokracuj od width, height, ...
    x zisti kde sú tieto polia použité v starom projekte a prenes to do nového
x! skôr ako spustis update 2021-12-06_durik_suhrn-160427-211110.sql nad ostrou DB tak prenes opravu Tools::updateTableFields() aby sa polia vytvárali na správnej pozícii v tabuľke (alebo urob merge s core - cez vydavateľa a alterego)
    - použitie polí na starom projekte (regex napr: `['"`.]viscosity[ '"`]`):
        x nasledovné som zmazal:
            x viscosity - podľa všetkého sa nepoužíva, v admine formulari je zakomentovené, v detaile produktu ošetrené cez empty()
            x package - podľa všetkého sa nepoužíva, v admine formulari je zakomentovené, v detaile produktu ošetrené cez empty()
            x import_supplier - nastavuje sa len pri importe nových a je zobrazené v admine. Z ohľadom na EshopSupplierProduct by bolo fajn ho odstrániť
            x import_foreign_key - je zobrazené v admine, inak sa nepoužíva. Z ohľadom na EshopSupplierProduct by bolo fajn ho odstrániť
            x image_import_source - je zobrazené v admine, inak sa nepoužíva. Z ohľadom na EshopSupplierProduct by bolo fajn ho odstrániť. Bolo by zaujímavé zisť čo sa importuje v EshopProducts.importImages
            x image_import_result - nikde sa nepoužíva
            x import_source|import_source_number obsahujú len hodnoty 'frankana' (import_source) a 'frankana3' (import_source_number) a používajú sa len v neprenesených akciách kontrolera EshopProducts
            x frankana_stock_status - nikde sa nepoužíva, je to len virtuálne pole použíte lokálne pri aktualizačnom importe dostupností, môže sa odstrániť
            x translated - sa používa len v EshopProducts::translateAll(), ktoré sa nepoužíva, tak sa môže odstániť
            x related_products|related_products_done - pomocou EshopProducts::relatedProducts() / ::insertionRelatedProducts() sa konvertujú na HABTM (raz sa ako oddeľovač používa , ;). Treba to prerobiť tak aby sa to rovno takto imortovalo
                - related_products_done sa používa len v EshopProducts::insertionRelatedProducts()
            x manufacturer - je nepoužité, môže sa odstrániť. Používa sa len pri importe ale tam sa korektne premieňa ma EshopManufacturer-a
            x polia groups|children|axis|categories|documents|hersteller_nummer|images_de|images_en|info|logos|marke|search_words|tech_data|upsell_products|zolltarif_nummer|tech_data_values|speditions_artikel
        x new
        x polia color|material_color|diameter|marquee_height|extension|material|sail_width|in_series|carrying_capacity|pressure|power|electric_current|inner_frame|outer_frame|roof_thickness|inner_dimension|volume|voltage|capacity a variant - sú zobrazené v admine a používajú  sa vo filtri (podľa výberu EshopProductCategory.product_parameters)
            - je otázne či má význam aby sa vyberali v getDetails() ak sa používajú len na filtrovanie
        x polia groups|children|axis|categories|documents|hersteller_nummer|images_de|images_en|info|logos|marke|search_words|tech_data|upsell_products|zolltarif_nummer|tech_data_html|tech_data_values|speditions_artikel
            - používa sa len tech_data_html (v detaile produktu v spojení s translated), ostatné sa môžu zmazať
                X? nechcú tech_data_values? >> toto pole je prázdne
            - z uvedených polí môžu byť pre nich zaujímavé (SELECT `id`, `slug`, `groups`, `children`, `axis`, `categories`, `documents`, `hersteller_nummer`, `images_de`, `images_en`, `info`, `logos`, `marke`, `search_words`, `tech_data`, `upsell_products`, `zolltarif_nummer`, `tech_data_html`, `tech_data_values`, `speditions_artikel` FROM run_eshop_products WHERE `documents` != '[]' AND `documents` IS NOT NULL;):
                -- documents: [["http://extern-cdn.frankana.de/fd/b328b9db56abff9fb65ad2d62151a1/Seitenverdunkelung SP300_Bedienungsanleitung.pdf","Seitenverdunkelung SP300_Bedienungsanleitung.pdf",827512]]
                -- images_en: [["http://extern-cdn.frankana.de/5f/decfa6658a5ddeb3b7ed88b88e4617/1024x1024.jpeg","Side Window Blind SP300"],["http://extern-cdn.frankana.de/97/e345b849827a02a7da770908f1fea1/1024x1024.jpeg","More Privacy"],["http://extern-cdn.frankana.de/f3/a0b70149c19db4a62d5b70862838f1/1024x1024.jpeg","Ultra Flat Handle"],["http://extern-cdn.frankana.de/7b/86f8b0d9b579d9b5e86e0a39618635/1024x1024.jpeg","Better visibility of the small mirror"],["http://extern-cdn.frankana.de/d1/ead49bfab09f633595df38d16597fc/1024x1024.jpeg","New Guide Panel"]]
                -- logos
                -- tech_data_html
                -- marke (výrobca, značka)
x Comment.email 
    x oprav na varchar(100)
X chcú v detaile produktu používať obsahové bloky? Ak áno tak skontroluj 2019-01-17_durik_products-content-blocks.sql)
    >> nie
X spusti .../mvc/App/Tools/checkContentBlocksDefinitions a oprav nájdené chyby
X Na homestránke doplň obom snippetom na zobrazenie produktov nasledovné parametre:

    index-type=brief
    show-index-type-select=0

    t.j. obsah home stránky bude:

    <h2 class="section-title"><a href="/novinky">Novinky</a></h2>
    <object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" _snippet_name="Novinky" has-image="1" index-type="brief" limit="4" paginate="0" show-index-type-select="0" show-sort-select="0"></object>

    <h2 class="section-title"><a href="/nase-knihy">Naše knihy</a></h2>
    <object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" _snippet_name="Nase knihy" filter_group="9" index-type="brief" limit="4" paginate="0" show-index-type-select="0" show-sort-select="0"></object>

- prever či spustiť tento dopyt (2017-02-06):
    -- SELECT id, `name`, created FROM `run_eshop_products` WHERE `availability` = 'enum_presale' AND (`available_from` IS NULL OR `available_from` = '') AND created < NOW() - INTERVAL 14 DAY;
    UPDATE `run_eshop_products` SET `availability` = 'enum_soldout' WHERE `availability` = 'enum_presale' AND (`available_from` IS NULL OR `available_from` = '') AND created < NOW() - INTERVAL 14 DAY;
X ak používajú zásielkovňu tak nastav crony (aurob úvodné spustenie):
    --      10   0   *   *   *   /mvc/Eshop/EshopShipmentMethods/loadPickupPlaces/geisPoint   
    --      15   0   *   *   *   /mvc/Eshop/EshopShipmentMethods/loadPickupPlaces/zasielkovna  
x prenes frankana import a odlaď v náväznosti na pridanie EshopSupplierProduct (2019-04-11):
    x zmaž pole EshopProduct.import_source ak nie je použité prípadne premenuj na supplier_pid
    x zmaž pole EshopProduct.import_source_number ak nie je použité

Zmeny v prenesenom:
- EshopProduct:
    x ::$disponibilityClasses, 'disponibility_class', 'disponibility_name' - nepreniesol som, prever ako je o v novom projekte
    x skontroluj použitie zmazaných polí:
            'EshopProduct.ebook_url',
            'EshopProduct.ebook_url_2',
            'EshopProduct.binding',
            'EshopProduct.language',
            'EshopProduct.pages',
            'EshopProduct.isbn',
            'EshopProduct.subtitle',
            'EshopProduct.original_name',
            'EshopProduct.year',
            'EshopProduct.time_length',
            'EshopProduct.starring',
            'EshopProduct.dubbing',
            'EshopProduct.subtitles',
            'EshopProduct.media_type',
            'EshopProduct.pdf_overview',
            'EshopProduct.reprint', - toto pole som nakoneic nechal
    X zmaž pole EshopProduct.exported
        >> ponechal som ho ako informatívne pole
    x pole EshopProduct.manufacturer by malo byť konvertované na záznamy v tabuľke EshopManufacturers
        >> zmazal som ho
    x zdá sa že pole 'viscosity' a 'package' sa nikde nepoužíva (sú zakomentované v admin formulári) - mohli by sa odstrániť
x nastavenie mrp.lastCardNumber premenuj na mrp.lastCode (vyhľadaj regex `(last\s*)?car[dt]\s*number` a zmeň to na mrp code
x nastavenie 'cart.oversizedProductMessage' premenuj na 'EshopCart.oversizedProductMessage'

x? EshopProducts::orderForm() + EshopProduct::submitOrderForm() sa zrejme nepoužíva(?)
    >> asi nie, je to z autolekar.sk
x /app/modules/Eshop/views/EshopProducts/indexRecords.php - pre názornú ukážku sa všetky produkty zobrazujú ako novinky


**********************************************************
************************ ALTEREGO ************************
**********************************************************

***************
DAROVACIA KARTA
***************

- súhrn DB UPDATE merge core_v2:
    ALTER TABLE `run_eshop_product_categories` ADD `sort_products` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'If 1 then default products order in indexes is the order in which they are added to the category' AFTER `show_description`;
    -- .../mvc/App/Tools/updateSettings?data[module]=Eshop
    -- .../mvc/App/Tools/updateContents?data[module]=Eshop&data[pid]=Eshop.EshopProducts.indexSearch&data[force]=1
    -- .../mvc/App/Tools/updateContents?data[module]=App&data[pid]=search&data[force]=

    -- .../mvc/App/Tools/updateSettings?data[module]=App
    UPDATE `run_countries` SET `name` = 'Maldivy' WHERE `name` = 'Maledivy';

x EshopProductType.variant - doplň komentár k poľu podľa konečného spôsobu použitia tohto poľa (skopíruje sa jeho obsah do EshopProduct.variant na začiatku, pri vytvorení produktu? Alebo vždy pri ukladaní produktu alebo ukladaní typu produktu?
x v košíku a emaile novej objednávky by sa nemusel zobrazovať skladový stav ak is_gift_cart = true alebo ide o voucher
x abs voucher by sa mal dať pridať len raz
x abs voucheru nech sa nastaví počet použití vždy aspoň na 1
x additional_services a zľavový kód by nemal mať v košíku link na detail
x preklady hlášok ku zľavovým kódom
x darovacie karty by sa nemali zobrazovať ako:
    X novinka(?) >> a možno aj hej
    x zľava 0%, 
    x v detaile by nemalo byť "doručime do"
x špeciálne produkty (zabalenie, DK, ZK, ...) by mali byť vynechané z "Kúpene spolu s", viď:
    x http://alterego/kniha/darovacia-karta-20#odporucane-produkty
    x http://alterego/kniha/posledny-bohem#odporucane-produkty
x Notice: Undefined offset: -1 in /var/www/core-v2/app/modules/Eshop/models/EshopProduct.php on line 4393
x generovanie a odosielanie zľavových kódov (DK)
x vsúvka do mailu novej objednávky pre elektronickú DK: "Po zaplatení objednávky vám pošleme kód darovacej karty aj s pokynmi ako ju môžete použiť"
x admin objenávky - zľavový kód nech je pri použitom voucheri (s linkom na detail kódu)
x stránka na overenie platnosti ZK (DK)
x odosielani upozorňujúcich mailov o blížiacej sa expírácii DK
    x pridaj nastavenie EshopOrderProduct.sendExpiringGiftCardVoucherEmails.lastBatchTimestamp
    x pridaj pole 'EshopOrderProduct.expiring_email_success'
x prever aký bude mať vplyv discount_rate voucher na cenu DK a jej následné použitie - cena by sa mala odvíjať od plnej ceny DK (nie od ceny za ktorú bola kúpená).
x zľavové kódy by sa mohli pozovnavat bez ohľadu na veľkosť písmen >> takto to už funguje (pretože DB charset je utf8_general_ci)

- odlaď úvodne načítanie nekonečného skrollu v malých zoznamoch

-? Ako upraviť hlášku v košíku "Ak ste od nás dostali zľavový kód po jeho zadaní môžete získať výslednú zľavu až do 20%"
X? Ak by náhodou niekto zakúpil v jednej objednávke viac elektronických DK tak posielať ich naraz alebo každú osobitne?
    >> Momentálne sa posielajú osobitne.
-? ako sa má nastaviť platnosť ZK od - do (pri kartičke trvá pár dní kým sa doručí) - ak bude platnosť dostatočne dlhá (napr. 1 rok) tak je to jedno
-? je dĺžka kódu 10 znakov ok?
-? Ak má objednávka nastavený iný email doručenia, tak poslať elektronickú darovaciu kartu tam?

x darovacie karty vo vyhľadávaní
- cena bez DPH v košíku 
    - https://www.google.com/search?q=zda%C5%88ovanie+pe%C5%88a%C5%BEn%C3%BDch+poukazov
        - https://www.financnasprava.sk/_img/pfsedit/Dokumenty_PFS/Zverejnovanie_dok/Dane/Metodicke_pokyny/Nepriame_dane/2019/2019.09.27_16_DPH_2019_MP.pdf > príklad 24
        - https://www.podnikajte.sk/dan-z-pridanej-hodnoty/zdanovanie-poukazov-voucherov-dph-2019
        - https://www.easystart.sk/poukazy-a-ich-zdanovanie/
    - http://uctuj.sk/uctovnictvo/uctovanie-zliav-rabaty-skonta-a-bonusy-v-podvojnom-uctovnictve/8739/

- skús namiesto farby #DCAF0E nájsť nejakú lepšiu (už tam sú zakomentované nejaké návrhy)

*****************
SÚHRN 2020-06-05:
*****************
Toto je súhrn/výber todo z nasledovných nižšie uvedených toto (až po sekciu VYDAVATEL).
Nie sú tu úplne všetky nedokončené, tie najmenej závažné som do súhrnu nepreniesol.
Všetky tu uvedené todo som nechal aj nižšie ešte ako nevyriešené. 
Preto ich po vyriešení tu označ aj nižšie.

Všeobecne:
x! nastaviť heureka kódy pre spôsoby dopravy
x otestuj watchdog a vouchers
- mail vsuvky: bonus a či je registrovaný (?)
x chcú aj podrobný search ako bol na starej stránke - toto je mozné zapracovať do filtra v zozname produktov
    >> volal som s p. Navrátilom a je to v poriadku, tak ako sme to spravili (bez podrobného searchu)
-? na starom webe mali aj zoznamy autorov a vydavateľov. Chcú to aj na novom?
-? chcú mať aj stránky (viď starý web hlavička)?:
    - Novinky
    - Pripravované knihy
    - Vydavateľstvá

Produkty:
- premigruj funkcionalitu pdf_overview (vyhľadaj pdf_overview na oboch projektoch). Zdá sa že to nepoužívajú. Na novom projekte je to len premigrované v tabulke produktov. Momentálne to ani nezobrazujem v admin formulári produktu
    - formulár produktu - pridaj pdf_overview - možno by sa to mohlo spojiť s niektorým s poli ebook_url / ebook_url_2
x v zozname nájdených zobraz prepínač typu zoznamu
X! ked do obsahového bloku produktov (alebo aj do slajdra) vyberieš produkty a súčasne aj skupiny (t.j. použiješ viacero itemselectorov) tak to nefunguje (vracia 404 - no toto sa mi na localhoste nepodarili nasimulovať, videl som to len na ostrom webe) - odlaď to prípadne zobraz validačnú chybu že len jeden item selector môže byť vyplnený. čo je zvláštne že na localhoste mi to šlo (možno ešte záleží ako je nastavené stránkovanie alebo iné parametre...) 
    >> nepodarilo sa mi to zopakovať
- obsahový blok zoznamu produktov (alebo slajder) - pri výbere z viacerých kritérií sa robí prienik (AND) t.j. zužuje. Asi by bolo lepšie robiž to ako spojnie (OR) alebo na to dať aspoň nejaký príznak.
x detail produktu: odľad js chybu "Cannot read property 'top' of undefined"
    
Filter produktov:
x! http://alterego/novinky?filter%5Bstock%5D=&filter%5Blanguage%5D= 
   - ked sa klikne vo filtry na akcie vyhodi Fatal error: Allowed memory size exhausted
x! Odlaď nefunkcné http://alterego/mvc/Eshop/EshopOrders/admin_index?_requestSource_=frame&sort%5BEshopProduct.name%5D=ASC&filter%5BEshopProduct.name%5D=ma%C4%8Diatko%20tinka vs funkčné http://alterego/mvc/Eshop/EshopProducts/admin_index?sort%5BEshopProduct.name%5D=ASC&filter%5BEshopProduct.name%5D=V%C3%A1le%C4%8Dn%C3%ADk%C5%AFv%20slib%204.kniha
    
Objednávky:
x Zostava objednávok (zoznam objednávok typu "Zostava") je prázdna
X odlaď notice v zozname objednávok: "Notice: Undefined variable: disponibility in /var/www/alterego-new/app/modules/Eshop/models/EshopProduct.php on line 2547". Prečo sa to nezobrazuje na vydavateľovi?
    >> nepadarilo sa mi zopakovať

Bónusy:
x! Admin > formular objednávky - do js výpočtu zahrň aj bonus

Importy:
-! Konverzie kategórií pre Albatros (na inych projektoch ich tiež nemáme, asi v tom ma Albatros neporiadok)
x celoplošné nastavenie "vypredané"
    x? chcú nastaviť úplne všetky? Aj tie čo sú v predpredaji?
        >> tie čo sú v predpredaji nie
X! Importy > "Duplicate entry '5740' for key 'PRIMARY'" - vzniká to tak že v aktualizačnej dávke (Model::saveBatch() > 'update' => ...) je dvakrát aktualizovaný ten istý záznam. T.j. napr pri importe knih sa daný EAN nachádza 2x v importnom katalógu. Pri náhrávaní dávky do tmp tabuľky vznikne uvedená chyba. Treba si preto viesť zoznam $processedPairCodes a všetky ktoré už boli spracované preskočiť. Viď premenná $processed v MailerContact::import().
    >> toto už je ošetrené napriek tomu to občas hádže túto chybu


Požiadavky klienta:
x v niektorých kategóriach chcú prednostné radenie slovenčiny (detské knižky, ...)
x naimportovať kontakty do mailera

Wishlist:
- oprav text pri pokuse odoslať wishlist na email ktorý je nevalídny (ide vlastne o text validačnej chyby)
x odlaď mail na zdieľanie wishlistu a watchdogu:
    x nevypĺňajú sa vsuvky
    x je to akoby spojeine mailu na zdieľanie wishlistu a upozornenia watchdogu

Otázky:
x? texty milov pre zdieľanie wishlistu a pre watchdog sú zrejme tie isté len nedopatrením? >> Opravil som
-? príznak 'new' si nastavujú sami interným importom?
-? môžeme starý web zmazať?

Info:
- Kategorie produktov - pridanie priznaku "Slovenske produkty ako prve" a zapracovanie do vychodzieho radenia zoznamu produktov kategorie
- Export produktov - zmena formatu z .xlsx na .csv
- Pridanie moznosti spustat PEMIC aktualizaciu popisov z administracie
- Bonus - doladenie infa v hlavicke, zobrazenie v admin zozname objednavok, zapracovanie do vypoctu ceny objednavky v admin detaile objednavky
- Sučasné filtrovanie a radenie v zoname objednávok
- Uzivatelsky profil - nastavenie tel. cisel na medzinarodny format a normalizacia telefonnych cisel
- Zrusenie odosielania emailov o uspesnej platbe v pripade prepnutia stavu platby objednavky v admine
- Automatické sťahovanie konverzného kurzu CZK pre Kosmas import
- Filter v admine - ukáž im CTRL+ENTER a SHIFT+CLICK na sort, CTRL + smartAdminTtrigger

Návrhy:
x "Úhrada platobnou kartou" by mohli premenovať na "Online úhrada platobnou kartou" a "Platba v hotovosti" na "Platba v hotovosti alebo kartou"
x dorieš s nimi dizajn stránky https://alterego.sk/kontakt
x! dorieš s nimi ešte aj dizajn nadpisu sekcie Home > Výpredaj
x rozmery jednotlivých slajdov nech si nastavia na identické rozmery
- vyhodnocovanie cien a dostupností rôzných dodávateľov
- Inform import
- celoplošné nastavenie "vypredané" by to mohlo byť súčasťou interného aktualizačného importu ktorý okamžite nahrá nové stavy (bol by tam príznak "nastaviť Vypredané", kontrolovala by sa prítomnosť poľa availability v importnom súbore a tiež počet položiek v súbore (aspoň nejaké % z celkového stavu).
- do filtra pridať zoradenie podľa najpredávanejších (.../produkty/skolska-literatura/sortBestsellers:1)
- watchdog - možno by bolo fajn keby niekde videli o aké knihy majú klienti záujem (ktoré dali strážiť) 
x export produktov - kvôli množstvu produktov zlyháva export všetkých ale zrejme nie je potrebne aby sa exportovali všetky stĺpce. PO doladení stĺpcov a formátu by mohol byť možný aj export všetkýh na jeden raz.

*************************************
MIGRACIA CAKEPHP DB NA FAJNWORKOVU DB
*************************************
- Migration::migrateAppUsers():
    x je potrebné premigrovať aj eshop_profiles.bonus_base (a napojenú funkcionalitu)
        x? používajú to ešte? >> áno
    x prever aké práva mal skupina Editors na starom projekte a nastav ich rovnako
        - pri úprave produktu boli ešte tieto polianastavené ako disabled (app/plugins/eshop/controllers/eshop_products_controller.php > 1281): 'price', 'price_discounted', 'discount_rate', 'price_discounted_end', 'eshop_tax_id', 'stock', 'sold_out', 'available', 'active', 'slug'
    x over funkčnosť hesla pri premigrovaní užívateľov (nastav ten istý hashovací algoritmus)
x Migration::migrateEshopAuthors():
    x! urob dynamické presmerovanie zo starej URL .../autor-produkty/115546 na novú (id autorov sa zachovávajú)
        Stačílo urobiť len presmerovanie na aktuálne URL + id no lepšie bude ak sa to presmeruje na aktuálne URL + slug
x Migration::migrateEshopProductAuthors()
    x je potrebné premigrovať aj eshop_products_eshop_authors.author_unified
        x? používajú to ešte? >> áno
        x a napojenú funkcionalitu, viď /p/eshop/eshop_authors/unify
x Migration::migrateEshopManufacturers()
    x EshopManufacturer.import_group sa zrejme nepoužije (zakomentuj ho v admin formulári výrobcu)
x Migration::migrateEshopShipmentMethods()
    x pole 'delivery_time' - potiahni core_v2
    x pridaj pole 'list_color' (v admine, normalizacia, validacia) a zapracuj v objednávkovom procese
        x? chcú to ešte aj v novom dizajne? >> nie
x Migration::migrateEshopOrders()
    x je potrebné premigrovať aj eshop_orders.bonus a eshop_orders.in_bonus_base (a napojenú funkcionalitu)
    x! pošli im prevody stavov nech to schvália >> poslal som
    x? pole 'paid_amount' ešte používajú? (pôvodne ako log pre Truspay, teraz tam zrejme len občas poznačia ručne skutočne zaplatenú sumu ale to sa dá urobiť v novom admine cez poznámku) >> nie
    x? TrustPay ešte používajú? >> nie, ani cardpay, ani csob, ani uniplatba
    x pole 'gift_package_price' je možné premigrovať ako špeciálny produkt "Darčekové balenie". Ide vlastne o službu objednávky - takto by sa to dalo zovšeobecniť - všetky produkty takto označené by sa zobrazovali na konci objednávkového procesu (alebo v detaile produktu cez nejaký prepínač perProductBasis/perOrderBasis) a boli by vylúčené z akéhokoľvek iného frontendového zobrazenia
        x zapracuj to aj do EshopOrder::getProductsInfoString() > @todo-redesign
    x je potrebné premigrovať aj eshop_orders.provide_email_and_phone_to_slovak_post_agreement (a napojenú funkcionalitu)
        x? používajú to ešte? >> ??? nevedia >> podľa kódu to nepoužívajú
    x pri zrušení objednávky sa na starom webe vrátili počty produktov (app/plugins/eshop/controllers/eshop_orders_controller.php > line 700 vs app/modules/Eshop/models/EshopOrder.php > // solve product reservations, vyhľadaj tiež "->updateStock"). Prezisti či je to tak aj na novom a či to je nutné (skladové stavy sa importujú zrejme každý deň) >> skladové počty uvádzajú len fiktívne takže to zatiaľ nutné nie je.
- Migration::migrateEshopOrderProducts()
    - prever zapracovane 'reserved_amount' do nového projektu, či nebude prekážať keď tam nebude nič. Tiež prever či má defaultna hodnota '' zmysel. Prečo tam nie je 0 ako východzia hodnota?
    x je potrebné premigrovať aj eshop_carts_eshop_products.gift_package_price - viď poznámku pri migrácii objednávok
- Migration::migrateEshopProducts()
    x over že je správne nastavená nerovnosť pri použití Date::getDiff()
    x? má produkt aj nejakú intertnú poznámku? ('note') >> asi nie
    x je potrebné premigrovať aj eshop_products.pdf_overview
        - a napojenú funkcionalitu
    x obrázky (tie najväčšie dostupné) je potrebné preniesť ručne (do priečinka /userfiles/Eshop/EshopProduct/image/original), menšie (lazy) varianty sa vytvoria sami. Po prenesení je potrebné spustiť Migration::moveEshopProductImages() na oddelenie obrázkov modelov EshopProduct a EshopProductImage
- Migrate::migrateEshopWhishlists()
    x je potrebné premigrovať aj eshop_wishlists.cart_wishlist >> nazval som ho EshopWishlist.from_cart
        - a napojenú funkcionalitu. Používalo sa to na to aby si užívateľ mohol uložiť košík a nathiahnúť si to naspäť do košíka na inom zariadení. Riešením by mohlo byť keby sa košík ukladal do DB a premášal sa utomaticky medzi zariadeniami. Viď todo 191220. Toto by sa mohlo preniesť do core
- Migrate::migrateEshopWhishlistProducts()
    x je potrebné premigrovať aj eshop_products_eshop_wishlists.priority, .amount, .attributes >> priority sa nikde nepoužíva
        - a napojenú funkcionalitu - súvisí to s ukladaním produktov do košíka (viď poznámkui pri migrácii wishlistov)
x Migrate::migrateEshopVouchers()
    x v admine zakomentuj pole pre 'special_discount_rate'
X Migrate::migrateMailerContacts
    X na odhlásenie z newslettera zachovaj aj nasledovnú pôvodnú URL: https://www.alterego.sk/newsletter-unsubscribe/502cb105-158c-4276-81a8-4993d43921e0
- Migrate::migrateSettings()
    x dokonči konverzie vsuviek a následne konverzie jednotlivých nastavení
    x konverzie vsuviek:
        x ':orderBonus' - prenes funkcionalitu bonusov a vytvor vsuvku (email novej objednávky)
        x ':giftPackageCost' - implementuj balenie ako produkt a táto vsuvka nebude potrebná (viď aj nastavenie Eshop.EshopOrder.giftPackageUnitPrice)
        x? ':suhlasSoZaslanimReklamnychMaterialov' - chcú to ešte? Ak áno tak to prenes (email novej objednávky) >> nie, zruší sa to.
        x ':deliveryDate' - odkomentuj generovanie vsuvky v EshopOrder::getInserts()
        x ':linkOrder' - buď vytvor vsuvku ':ordersLink:' (viď existujúce ':orderUrl:')
    x konverzie nastavení:
        x 'Core.All.emailBcc' 
            x vytvor 'App.email.bcc' a 'Eshop.email.bcc' 
            x zpracuj do odosielania emailov (všade tam kde je nastavené 'Core.All.emailBcc') a prenes to aj do core-v2
        x! 'Eshop.EshopOrder.maxVydavatelDiscountRate' (EshopProduct.additionalDiscountRateLimitForVydavatel) - zakomentuj použitie
        x over migraciu týchto nastavení:
            X Eshop.EshopOrder.giftPackageUnitPrice !!! - toto treba prerobiť na produkt
            x Eshop.EshopCategory.imageHeight
            x Eshop.EshopCategory.imageWidth
            x Core.All.gridPageLimit
            x Core.User.passwordResetCodeTimeout
            x Core.User.msgSubjectPasswordReset
            x Core.User.msgBodyPasswordReset
            x Core.All.extDisplayComponentId
            x Eshop.EshopOrder.msgSubjectNewComment
            x Eshop.EshopOrder.msgBodyNewComment
            x Core.All.primaryPlugin
            x Core.All.massEmailLimit
            x Core.All.smtpHostMass
            x Core.All.smtpUsernameMass
            x Core.All.smtpPasswordMass
            x Core.All.smtpPortMass
            x Eshop.EshopOrder.msgBodyNewOrderIncomplete
            x Newsletter.NewsletterLetter.emailFooter
            x Core.CmsTree.menuExpandLevel
            x Core.All.extjsTheme
            x Core.Content.useHeadingField
            x Core.All.speechToText
            x Eshop.EshopShippingMethod.variableForeignShippingRate
            x Eshop.EshopShippingMethod.homeCountry !!!
            x Eshop.EshopOrder.subjectPaymentInProcessing
            x Eshop.EshopOrder.bodyPaymentInProcessing
            x Eshop.EshopOrder.bankAccount
            x Eshop.EshopOrder.bankTransferConstantSymbol
            x Eshop.EshopProductAttribute.allowMultipleValues
            x Newsletter.NewsletterLetter.mode
            x Core.All.logs
            x Core.All.logsLifeDays
            x Core.All.speaker
            x Eshop.EshopOrder.msgBodyStatusChange
            x Core.Content.homeSlug
            x Core.Content.useContentGroups
            x Core.Content.allowedFields
            x Orator.OratorSpeech.variant
            x Orator.OratorSpeech.pitch
            x Orator.OratorSpeech.speed
            x Orator.OratorSpeech.gap
            x Orator.OratorSpeech.limit
            x Orator.OratorSpeech.cacheTime
            x Orator.OratorSpeech.synthesizerAdapter
            x Alterego.All.infotext
            x Eshop.EshopOrder.smartsmsMessageInStore !!!
            x Eshop.EshopOrder.smartsmsMessageShipped !!!
            x Eshop.EshopOrder.bonusAmount !!!
            x Eshop.EshopOrder.bonusableTotal !!!
            x Core.All.emailBcc !!!
            x Alterego.All.infotextSlugs
            x Alterego.PemicImport.ignoredPublishers !!!
            x Core.All.facebookAppId !!
            x Eshop.EshopWishlist.msgBodyWishlistWatchdog !!!
            x Eshop.EshopWishlist.msgSubjectWishlistWatchdog !!!
            x Eshop.EshopShipmentMethod.zasielkovnaApiKey !!!
            x Eshop.EshopShipmentMethod.zasielkovnaPickupPlaces !!!

x Migrované tabuľky:
    -- users - Migration::migrateAppUsers()
    -- eshop_profiles - Migration::migrateAppUsers()
    -- eshop_categories - Migration::migrateEshopProductCategories()
    -- eshop_eshop_products_eshop_categories - Migration::migrateEshopProductCategoryProducts()
    -- eshop_authors - Migration::migrateEshopAuthors()
    -- eshop_products_eshop_authors - Migration::migrateEshopProductAuthors()
    -- eshop_manufacturers - Migration::migrateEshopManufacturers()
    -- eshop_product_series - Migration::migrateEshopManufacturerRanges()
    -- cashier_payment_methods - Migration::migratePaymentMethods()
    -- eshop_shipping_methods - Migration::migrateEshopShipmentMethods()
    -- eshop_shipping_methods_cashier_payment_methods - Migration::migrateEshopShipmentMethodPaymentMethods()
    -- eshop_sales - Migration::migrateEshopProductGroups()
    -- eshop_product_groups - Migration::migrateEshopProductGroups()
    -- eshop_products_eshop_product_groups - Migration::migrateEshopProductGroups()
    -- eshop_products_eshop_sales - Migration::migrateEshopProductGroups()
    -- eshop_orders - Migration::migrateEshopOrders()
    -- eshop_carts - Migration::migrateEshopOrders()
    -- eshop_carts_eshop_products - Migration::migrateEshopOrderProducts()
    -- eshop_product_types - Migration::migrateEshopProductTypes()
    -- eshop_products - Migration::migrateEshopProducts()
    -- eshop_product_i18ns - Migration::migrateEshopProducts()
    -- eshop_product_attribute_types - Migration::migrateEshopProducts()
    -- eshop_product_attributes - Migration::migrateEshopProducts()
    -- eshop_product_attribute_i18ns - Migration::migrateEshopProducts()
    -- eshop_taxes - Migration::migrateEshopProducts()
    -- attachments - Migration::migrateEshopProducts(), Migration::migrateEshopProductImages()
    -- eshop_wishlists - Migration::migrateEshopWhishlists()
    -- eshop_products_eshop_wishlists - Migration::migrateEshopWhishlistProducts()
    -- eshop_vouchers - Migration::migrateEshopVouchers()
    -- newsletter_recipients - Migration::migrateMailerContacts()
    -- settings_settings - Migration::migrateSettings()


# Migracia bonusov
x? EshopOrder.order_price_taxless - má toto byť už s odpočítaným bonusom alebo nie? Treba to zohľadniť aj pri vytváraní objednávky aj pri migrácii objednávok >> dal som už s odpočítaným bonusom
x Admin > formulár užívateľa - pridaj pole bonus points
x Zobraz bonus points aj v hlavičke, možno ako ???/160 a  daj tam nápovedu o čo ide
    x pridaj bonus info do užívateľského menu (ako link na stránku vysvetľujúcu ako to funguje)
- Admin > formular objednávky - do js výpočtu zahrň aj bonus

# Importy
x!? Aké východzie zľavy nastaviť jednotlivým importom? >> podľa starého webu
x? ponúkneme im vyhodnocovanie cien a dostupností rôzných dodávateľov alebo to necháme po starom (a udaje dodávateľov v admine zakomentujeme)
    >> predbežný záujem o to majú, dohodneme sakeď to prenesieme
    >> takisto mali záujem o ostatné importy
x zriaď crons:
- skontroluj či sa media_type zobrazuje v detaile produktu
- skontroluj media_type (Pemic import) vs binding (Kosmas import) - rovnako to však bolo rozlýšené na starov webe (media_type vs format)
x! zakomentuj importy ktoré sme im nepredali!
x pridaj celoplošné nastavenie "vypredané".

## Pemic import nových
-? nastavenie Alterego.PemicImport.ignoredPublishers ... EshopSupplierProduct.ignoredPublishers
x? môžu sa súbory sťahovať automaticky alebo to chcú ručne ako na starom webe?
    >> automaticky

## Ikar import nových
-? na starom webe sa shipment_time_off_stock nastavoval na 7 (aj pre všetky ostatné ktoré používali Ikar). Treba to nastaviť rovnako alebo stačí globálne nastanie v nastaveniach Eshopu ("Doba odoslania produktu, ktorý nie je sklade")?
x? ktoré sortimenty chcú importovať?

## Albatros / Partner technic / Inform import nových
x ak tieto importy objednáju:
    x nastav konverzie kategórií

## Alterego import nových
x? ktorý z katalǵov je dôležitejší? >> Knihy
x? C - je to novinka? >> áno ale nepoužívajú to
x? V/W - ??? >> vypredajový produkt, t.j. dostupný ale nepoužívajú to
x? majú skladové stavy alebo nastaviť tých 10000 ako na starom webe? >> na toto majú import skladových stavov
x? používajú to aj na vytváranie alebo len na aktualizáciu? >> Väčšinou na vytváranie ale občas aj na aktualizáciu
x? majú všetky produkty kód? (t.j. má sa párovať podľa kódu alebo EANu?) >> áno
x? vytvárajú produkty aj ručne v admine alebo len cez importy >> len cez importy
x! uplne kľudne by sem mohli pridať ešte stĺpec s reálnym skladovým stavom >> telefonoval som im, p. Uhrín povedel že OK
x konverzia kategórií
x nastavenie $record['code'] nemôže byť implicitné keď je pair field 'code'
x product type má byť kniha? >> vydavatel to tieťž nemá vyplnené
- obrázky
    x? nahrávajú produktu viacere obrázky alebo vždy len jeden >> aj viacere (poďla toho čo som videl v súbore s importnými obrázkami)
x avoidEans - zapracuj alebo odstráň
x SupplierProduct - skontroluj zapracovanie 'INTERNAL'
- pridaj tam link na spustenie importu obrázkov (pri zobrazení výsledkov alebo normnálne do bočného menu)
x! prepni na párovanie cez EAN namiesto cez  CODE - aby nevznikali duplicity eanov (keď sa importuje s katalógov a páruje sa cez ean vs keď sa importuje interne a páruje sa cez kód). Možno sa môže ponechať párovanie cez kód len overiť že daný ena ešte neexsituje (kód je aj tak predpísaný pri vytváraní produktu z tohto importu), SQL na zizstenie duplicít: SELECT CONCAT("'", ean, "',") FROM `run_eshop_products` GROUP BY ean HAVING COUNT(id) > 1 LIMIT 500. Príklad duplicitnej knihy: https://alterego.sk/kniha/obhliadka-bytu-1 vs https://alterego.sk/kniha/obhliadka-bytu

## Alterego import aktualizačný
-! pridaj aj image_import_source a gallery_image_import_sources - na starom webe bolo možné aktualizovať aj obrázky produktov (interným importom) (viď /var/www/alterego/app/plugins/vydavatel/models/vydavatel_eshop_product.php > 3204 - 3229). V EshopProductCsvReader to už je zapracované, ešte to treba zapracovať do EshopProductImport::updateImport() > 'internalProductUpdate'. Po zapracovaní odkomentuj v EshopProductCsvReader @todo200520

## Kosmas
x? importovať produkty bez kategórie? Alebo počkajú až sa im kategória vyplní? >> áno
x? aké východzie % zľavy a rabatu? 10%
x? týmto importom len pridávali? Alebo aj aktualizovali? >> iba nové
x? chcú čítať všetky formáty?: >> áno
    BA - kniha, vazba není určena
    BB - kniha, šitá vazba
    BC - kniha, brožovaná vazba
    BD - kniha, vázaná vazba
    BE - kniha, kroužková vazba
    BF - kniha, lístková vazba
    BO - kniha, sešitová vazba
    AB - magnetofonová kazeta
    AC - CD
    AI - DVD
    AE - LP, SP
    CA - mapa
    DB - CD-ROM
    PC - kalendář
    PD - hrací karty
    VJ - VHS kazeta
    VO - Blu-ray
    ZE - hry
x? chcú nejak zohľadniť to <InvoiceOnly>? že by brali len do komisu? >> nie
x? chcú dokódiť aj import dostupnosti? >> zatial nie (to má význam iba ak chcú vyhodnocovať dostupniosti všetkých dodávateľov)
x? aký im kosmas dáva rabat? >> 28-30% rabat
x? chcú automaticky naťahovať konverzný kurz? (https://www.ecb.europa.eu/stats/eurofxref/eurofxref-daily.xml) Daj to do modelu EshopCurrency.

# Exporty

- export produktov zlyhal na maxExecutionTime - nastav ho buď globálne v ISP na 600 alebo urob app config 'maxExecutionTime' a 'memoryLimit' a v App::init uplatni tieto configs a tiež v database.php. Prenes to aj do core

## Cenové porovnávače
x ak majú tak zriaď crony a nastav cesty k súborom

# Po migrácii
x otestuj všetkú migrovanú funkcionalitu !!!
x! znefunkčni spúšťanie migrácií
x! odlaď chyby prichádzajúce do mailu

******************************** 
Požiadavky klienta na novom webe
********************************
x Na odporúčanie SAEC v procese objednávky prosím zrušte zaškrtávacie políčko "Súhlasím s jednorazovým zaslaním informačných marketingových materiálov spolu s tovarom " v kroku ODOSLANIE
- v niektorých kategóriach chcú prednostné radenie slovenčiny (detské knižky, ...)
- chcú naimportovať kontakty do mailera


********************
Nasadzovanie dizajnu
********************

# Všeobecne
x! buttonu nekonečného scrollu by ma mohol dať príznak "loading" a zobraziť nejaký loading gif...
x! odlaď ukladanie úžívateľského profilu (na ostrom projekte nešlo nastaviť tel. č. mojmu profilu)
-! dorieš s nimi ešte aj dizajn nadpisu sekcie Home > Výpredaj
-? na starom webe mali aj zoznamy autorov a vydavateľov. Chcú to aj na novom?
x "PO-PIA 8:00-18:30" v hlavičke daj ako link na stránku kontakt
x stiahni súbory fontov a začisti app/css/less/_fonts.less (zmaž nepoužité súbory)
- nastav fontawesome na verziu 5:
    - vyhľadaj vo *.php: font-awesome.
    - https://fontawesome.com/how-to-use/on-the-web/setup/hosting-font-awesome-yourself
x vyhľadaj všetky @todo-redesign a dokonči
x pridaj oznam o starých prehliadačoch (ak napr. nefunguje flexbox)
x! zakomentuj "Heuréka - overené zákazníkmi"
x! skontroluj a vymaž (prípadne aj skry) nastavenia ktoré sa preniesli z vydavatela a
na alteregu nemajú pár
    x! nastav SMTP a emailové adresy
x! nastav rovnaké url adresy stránkam eshopu (najmä zoznamom produktov)
x! favicon
x zamerdžuj core-v2 do vydavatela a potom to potiahni do alterega (2 muchy 1 ranou)
x všetkým obsahom okrem home vypni bočný obsah
x? nechcú dať niekde zrozumitené info o bonuse - ako to funguje.... Link na túto stránku by sa mohol zobrazovať všade kde sa zobrazuje bonus (menu užívateľa, košík, súhrn objednávky, detail mojej objednávky)
    >> hotovo
x? nechcú zmeniť nulovanie bonusov tak, že sa po uplatnení bonusu od bonusovýyh bodov odčíta 160 (namiesto úplného vynulovania)?
    >> nie
x na stránku onformujúcu o bonuse vytvor snippet na vloženie zmysluplnej informáciu o stave užívateľovho bonusu, napr. "Vás aktuálny stav bodov na získanie bonusu je 27 zo 166 potrebných. Po diasihnutí 166 bodov získate bonus v hodnote 10€"
x? majú aj vouchers >> áno
x! skry:
    x špeciálne ponuky
    x importné udaje dodavateľov
x a.button:active/:focus ... nastav farbu písma na bielu
x v užívateľskom menu pridaj aj link "Moje objednávky"
-? chcú mať aj stránky (viď starý web hlavička)?:
    - Novinky
    - Pripravované knihy
    - Vydavateľstvá
-? aký je rozdiel medzi (starý web): https://alterego.sk/knihy/beletria-a-poezia/ a https://alterego.sk/produkty/beletria-a-poezia/ - lebo to vracia rozne zoznamy
x? používajú MRP? Lebo v nastaveniach ISP mali extension=dbase.so >> Nie
x (Mojo) preniesť LuigisBox script zo starého webu (do elementov htmlBodyEnd alebo htmlHead)
x? vymeň obrázky v obsahových blokoch
x prenes crony
x aplikačnú správu zatvor pre danú session (do zatvorenia tabu / prehliadača). Prenes to aj do core.
-!! >> 2020-04-29 14:36:19 ************** Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.122 Safari/537.36 (/mvc/Eshop/EshopOrders/admin_index?_requestSource_=frame&sort%5BEshopProduct.name%5D=ASC&filter%5BEshopProduct.name%5D=ma%C4%8Diatko%20tinka)
    [EXCEPTION: Invalid definition of join 'Eshop.EshopProduct', it is not possible to generate 'conditions'. Please specify them manually.] /var/www/clients/client18/web197/web/app/libs/default/Model.php (line 1523): 
    Exception: Invalid definition of join 'Eshop.EshopProduct', it is not possible to generate 'conditions'. Please specify them manually. in /var/www/clients/client18/web197/web/app/libs/default/Model.php:1523
    Stack trace:
    #0 /var/www/clients/client18/web197/web/app/libs/default/Model.php(1994): Model->normalizeJoins(Array, Array)
    #1 /var/www/clients/client18/web197/web/app/libs/default/Model.php(2332): Model->find(Array)
    #2 /var/www/clients/client18/web197/web/app/libs/Paginator.php(978): Model->findCount(Array)
    #3 /var/www/clients/client18/web197/web/app/libs/default/Model.php(1978): Paginator->getCount(Array)
    #4 /var/www/clients/client18/web197/web/app/modules/Eshop/controllers/EshopOrders.php(33): Model->find(Array)
    #5 /var/www/clients/client18/web197/web/app/libs/default/Controller.php(149): EshopOrders->admin_index()
    #6 /var/www/clients/client18/web197/web/app/libs/default/App.php(7682): Controller->loadAction('admin_index', Array, Array, Array, false)
    #7 /var/www/clients/client18/web197/web/index.php(124): App::loadControllerAction('Eshop', 'EshopOrders', 'admin_index', Array, Array, Array)
    #8 {main}

## Zakomentované na doriešenie
x novinky blogu v pätičke
- Admin
    x Blog (použije sa tento jednoduchý alebo sa prenesie plný blog z ipaslovakia)
         >> jednoduchý
    x Exporty (treba preniesť to čo používajú)

x! vyčisti tabuľky
    x! payment_methods a priradenie k doprave - csobwebpay -> gpwebpay + prenes kľúče

# Klient
x! 200323: Okrem iného by sme potrebovali momentálne tieto úpravy:
    x Newsletter – zatiaľ nám nefunguje, testový e-mail áno, ale skupinový nie. Bude to zrejme nejaké drobné povolenie od vás.
    x Možnosť platby – bol by funkčný osobný odber s platbou kartou (vopred)? Dočasne by sa nám táto varianta hodila, viete prečo. Vytvorili by sme ďalší spôsob platby Osob. odber s touto formou úhrady. – môžeme to urobiť my, alebo to musíte doprogramovať?
    x Iné – zatiaľ nie tak dôležité – pri vyhľadávaní podľa textového reťazca nefunguje triedenie podľa jazyka, ani podľa dostupnosti (triedenia fungujú pri výbere podľa kategórií) 
- nastavenia
    - E-shop:
        - formulár odstúpenia od zmluvy (doplniť kde je ???)
        - obchodné podmienky
        - text emailu novej špeciálnej objednávky
x položky hlavného menu
x položky pätičkového menu
    - treba vyplniť takmer všetky stránky (obchodné podmienky sa vyplnia po vyplnení textu v nastaveniach)
x produkty na home stránke
x? obrázky (preniesť staršie ako rok?) >> všetky
x! google analytics
-i pri zmene stavov sa odosielajú maily
xi nech si nastavia slajdre na homestránke
xi nech si vyplnia stránku bonus info
xi v zozname produktov sa dá prepínať typ stručný/detailný/zvolený...
xi kosmas import je hotový
-i horný padding obsahu pre prípad html obsahu si môžu nastaviť vo Vlastnostiach bloku (napr. http://alterego-new/obmedzenie-ponuky)
- 200427: dokončenie stránky:
    1 X vyhľadávanie podľa edície (Luigi´s Box?) 
        >> toto na starom webe nebolo
    2 x ujednotenie autora meno/priezvisko
    3 x darčekové balenie
    4 x v prípade zmenenej doručovacej adresy prenos aj zmeneného telefónneho čísla (telefónne číslo konečného príjemcu zásielky)
    5 x chýbajúce importy:
        x ikar,
        x albatros,
        X inform,
        x partner...
    6 x v importe pemic sporadicky sa nedohrá časť textu anotácie – napríklad pri dohrávaní posledných 10 položiek sú tieto tovarové karty nekompletné.
    7 x pri vyhľadávaní podľa reťazca nie sú funkčné filtre DOSTUPNOSŤ a JAZYK, zoraďovania sú funkčné  (skúšam to napríklad na HARRY POTTER)
    8 x vysielanie alterego aktualizačný import: medzi poliami chýba EDÍCIA, VYDAVATEĽ, NÁZOV POLOŽKY (je tu seo_title). Sú vynechané zámerne?
    9 x v informačnom e-maili o vytvorení objednávky pri FIREMNEJ OBJEDNÁVKE sa vyobrazí len DIČ zákazníka – je to taká drobnosť, uvítali by sme to v tvare ako bolo pôvodné “Názov firmy;IČO...;DIČ....;IČ DPH....”, aby sme to neprehliadli. 
    Toľko sme zatiaľ našli.
    S pozdravom Miro Navrátil, Marek Uhrin

## Dohodnuté s klientom
x! pri platbe v hotovosti neodosielaj email o úspešnej platbe (ak to v admine označia - oni to robia kvôli bonusom)
x normalizácia tel. č. v DB (pridá sa +421 všade kde nie je medzinárodný tvar)
x zrušiť logo vydavateľa v dodacom liste
x do stĺpca Top 10 dať nadpis: <h2 class="section-title">Top 10</h2>
x Zmeň text o poštovnom zadarmo (platí len pre prípad kuriera a zásielkovne, nie slovenskej pošty)
x Spôsoby doručenia:
    x pridaj aj záznam 'abroadDelivery' (podľa vydavatela alebo core)
    x doručenia do Českej republiky nastav pre CZ
- mail vsuvky: bonus a či je registrovaný
x zobraziť info o bonuse v hlavičke (ako prasiatko niekde pri košíku)
- chcú aj podrobný search ako bol na starej stránke - toto je mozné zapracovať do filtra v zozname produktov
x do horného menu pridaj smart admin launcher

# Dôležité
x prenes GA (preniesol sa až po 3 dńoch, t.j. od 27.2.2020 22h do 2.3. 8:20 to bolo bez GA)
x! aj objednávky s osobným odberom sú ozančené ako špoeciálne. Prečo?

# Hlavička
x urob kvalitnejšiu verziu loga
x dorieš stýly appAnnouncement (aplikáčného oznamu)
x Settings::mainContacts() - dokonči (hardcoded)
    X toto by mohlo byť aj v eshope (už keď je tam adresa), t.j. presuň to do EshopSettings::mainContacts()
x over že vstup vyhľadávania má oranžový okraj (zmenil som vychodziu farbu okrajov pre .text-input)
x zfunkčniť topkategórie - zatiaľ sú nakódené "natvrdo" so statickými dátami
- skusit optimalizovat kod pri zobrazovani a skrývani vsetkych menu v hlavicke (dala by sa z toho spravit jedna funkcia),
  v kode zacina riadkami
  button.click(function(e){
      e.stopPropagation();
      jQuery(this).toggleClass('active');
- v hlavnom slideri v administrácii dať možnosť zadať aj obrázok pre menšie obrazovky
x hlavné menu - treba tam nastaviť justify-content: flex-start a podľa toho doladiť štýly
X ikony sú z FA4 treba tam dať FA5
- v hlavičke sa na malých zariadenia prekrývajú otvorené menu (niektoré sú dokonca skryté pod)
- slajder nech je pomocou fadeIn efektu
    - no pri fadein to skáče

# Pätička
- nahraď všetko hardcoded za editovateľnú verziu:
    - footer-badges - urob nejaké menu v Obsah webu > Menu v pätičke (?)
        - .badges-menu - urob ako menu, prinajmenšom by bolo fajn zobrazovať dopravu zadarmo podľa skutočnej nastavenej hodnoty a url linkov urobiť cez pidy obsahov
        -! zriaď stránku bezpecny-nakup
    - footer-announcement - urob nastavenie (text aj s linkom)
    - footer-payment-methods - urob nejaké menu v Obsah webu > Menu v pätičke (?)
    - "Odporúčame" - urob ako menu
X prenes blog z ipaslovakia a zobraz v pätičke 2 najnovšie články
- app/modules/Eshop/views/elements/address.php - prerob to na EshopSettings::address() a prenes do core
x responzivita pätičky (odznaky a platobné metódy)
x najviac vyhľadávané - keď tam bude Blog tak uprav farbu .title podľa dizajnu

# Home stránka
x! horný slajder prenes z ipaslovakia
x! zoznamy produktov zobraz cez Eshop.ContentBlockEshopProductsSlider
X contents.php - pridaj tam zoznamy produktov podľa dizajnu a DB aktualizaciu
x prozatímne je vypnutý bočný obsah aj tu - dolaď to a zobraz v ňom Top 10
x ContentBlockQuotation - citáty zobrazené na homestránke (ale potencionálne aj na iných stránkach)

# Generovanie faktúr
x nahraj pečiatku Alterega (app/modules/Eshop/img/stamp.png)

# Eshop
x! skry nasledovnú funkcionalitu (? - dohodnisa s Bohušom):
    x Admin > špeciálne akcie / ponuky
    x Admin > fromulár produktu > Importné údaje dodavatelov
x! odstráň všetku funkcionalitu ohľadom show_link_dobroty_z_kuchyne
- Admin
    - Zostava objednávok (zoznam objednávok typu "Zostava") je prázdna
    - formulár produktu - pridaj pdf_overview - možno by sa to mohlo spojiť s niektorým s poli ebook_url / ebook_url_2
    - odlaď notice v zozname objednávok: "Notice: Undefined variable: disponibility in /var/www/alterego-new/app/modules/Eshop/models/EshopProduct.php on line 2547". Prečo sa to nezobrazuje na vydavateľovi?
x prenes cron https://alterego.sk/p/eshop/eshop_authors/unify
- urob skript na vyhľadanie všetkých obrázkov produktov ktoré neexistujú (napr https://alterego.sk/kniha/black-echo , https://alterego.sk/kniha/drop ,  https://alterego.sk/kniha/gods-of-guilt-0) a bu nech si to doimportujú alebo tie obrázky zmaž aj v DB a bude tam aspoň placeholder (to sa môže urobiť v každom prípade a oni si to potom môžu stále doimportovať)

## Filter produktu
X? môže ostať filtrovanie dostupnosti tak ako je alebo ju viac prevziať zo starého webu (všetky | iba na sklade | bez vypredaných)?
    >> tak ako je
- pridaj zoradenie podľa najpredávanejších (.../produkty/skolska-literatura/sortBestsellers:1)
X ako caret ikonu použi FA5 ikony (https://fontawesome.com/icons/angle-down?style=solid, https://fontawesome.com/icons/angle-up?style=solid)
X aj zoradenie by sa mohlo otvárať na hover (?)
x Pri výbere sortovania (napr podľa názvu) a filtrovania (napr. len slovenský jazyk) súčastne sa vytvorí nesprávne URL, napr:
    /produkty/beletria-a-poezia?data[_target][]=Eshop.EshopProducts.index&data[_target][]=Eshop.EshopProducts.miniSearch&products-index-type-select-5e5914ea5ea43=brief&sort=name:ASC&filter[stock]=&filter[language]=*sloven*+NULL&filter[discounted]=1
Chybné je "sort=name:ASC" malo by byť "sort[name]=asc". Momentálne je to ošetrené presmerovaním v App::redirectOldToNewUrl() > //@todo-redesign. Treba to opraviť priamo vo filtri a kód App::redirectOldToNewUrl() > //@todo-redesign odstrániť.
x! Keď odfiltruješ slovenské knihy (*sloven*+NULL) a potom vypneš filtrovanie slovenských tak to NULL tam ostane (lebo sa to exploduje cez + ...)
x dolaď tam pekné checkboxy a radiobuttony
x keď sa už raz filter otvorí tak nie je možné ho zavrieť. Pridaj tam tlačidlo "Reset" / "Zrušiť" s linkom obsahujúcim GET parameter reset=1. (toto by sa mohlo preniesť aj do core/vydavatel)
- ! http://alterego-new/novinky?filter%5Bstock%5D=&filter%5Blanguage%5D= 
   - ked sa klikne vo filtry na akcie vyhodi Fatal error: Allowed memory size exhausted

## Zoznam produktov
x! je tam veľmi dlhý scroll nadol (asi len niečo priebežné)
x! zapni nekonečný scroll
x! skontroluj nastavenia veľkosti obrázkov
- zapracuj tam card-mixin();
- zislo by sa zoznam produktov zobrazovat ako ul > li a top 10 ako ol > li, google bude krochkat blahom
x treba tam nastaviť justify-content: flex-start a podľa toho doladiť štýly
x? mali na starom webe nejaké placeholder obrázky? >> áno
    x Pridaj placeholder obrázky!
x! tie čo sú na sklade nech majú označenie dostupnosti
- v zozname nájdených zobraz prepínač typu zoznamu
-? ako nastaviť label "Novinka" - lebo aj vypredané sa zobrazujú ako novinka
x opraviť prepínanie detailného výpisu - potom sa na homestránke vypisusú hlúposti
x lištu s filtrom na malých zariadeniach od kraja do kraja
x zoznam podkategórií nad filtrom
x v zozname nájdených sa nezobrazuje label "Novinka"
- ak zavoláš zoznam všetkých produktov (http://alterego-new/produkty) tak filter je nalepený na hlavičku (chýba tam breadcrumbs)
x Prelož text buttonu "All items are loaded" - stačí aj prepísať na mieste
x (Igor) "Načítať ďalšie" je naschval na kraji? Nemalo byť uprostred?
-! ked do obsahového bloku produktov (alebo aj do slajdra) vyberieš produkty a súčasne aj skupiny (t.j. použiješ viacero itemselectorov) tak to nefunguje (vracia 404 - no toto sa mi na localhoste nepodarili nasimulovať, videl som to len na ostrom webe) - odlaď to prípadne zobraz validačnú chybu že len jeden item selector môže byť vyplnený. čo je zvláštne že na localhoste mi to šlo (možno ešte záleží ako je nastavené stránkovanie alebo iné parametre...)
    - pri výbere z viacerých kritérií sa robí prienik (AND) t.j. zužuje. Asi by bolo lepšie robiž to ako spojnie (OR) alebo na to dať aspoň nejaký príznak.
x (Igor) Naštýluj vstup pre watchdog (nastav si nejaký produkt nedostupný...)

## Slajder produktov
x nech sa posúva nie po jednom produkte ale po celých stránkach
x pridaj linky v hlavičke na načítanie ďalších produktov (zobrazenie zoznamu všetkých) - s Igorom s me to už začali implementovať na úrovni parametrov obsahového bloku a ich porenosu do EshopProducts::index() - možno stačí keď budú len v obsahovom bloku, do EshopProducts::index() nemusia isť... ?

## Detail produktu
x? bude tam aj fotogaléria? >> áno, samé odseba sa to celkom v pohode zobrazuje, treba len skontrolovať / doladiť štýly
    x keď sa vyplní tak sa aj zobrazuje - skontroluj štýly
x aj rok vydania by mohol byť klikateľný - zobrazili by sa produkty daného vydávateľa z daného roku (a staršie, no zoradené podľa rokov)
x! uprav hlášky o dostupnosti podľa aktuálneho webu
x! dorob interné komentáre
x! dorob breadcrumbs
x prever funkcionalitu facebook comments - na locahoste sa nezobrazujú, možno to pôjde až na ostrom serveri (asi overujú či je daná url verejne dostupná)
x dorob rozbaľovací popis
x dorob slajder podobných titulov (prípadne tam pridaj aj iné odporúčané tituly)
-! Admin > formulár produktu - edície by sa mali vyberať len tie ktoré sú dostupné pre daného výrobcu (pridaj k poľu hint aby najprv vybrali výrobcu) - oprav to aj v core.
- .availability-alert / watchdog - možno by bolo fajn keby niekde videli o aké knihy majú klienti záujem (ktoré dali strážiť) - prenes aj do core
- doladenie responzivity na malých obrazovkách (zmenšiť bočný padding)
x! vypredané majú nejaký formulár
- odľad js chybu "Cannot read property 'top' of undefined"
x linky na autora, vydavateľstvo a rok nech sa podčiarknú pri hoveri
x dolaď farebnosti infa o dostupnosti - nech sú použité rovnaké farby ako v zoznamoch produktov
x (Igor) Naštýluj vstup pre watchdog (nastav si nejaký produkt nedostupný...)

## Objednávkový proces
x prázdnemu košíku nezobrazovať cenu ani položky >> hlúposť, necháme to tak ako je
x vyskakovacie upozornenia o tom produkt nie je skladom vypni
    - pokiaľ možno urob to tak aby sa to dalo vypnúť/zapnúť cez nastavenie (hľadaj "$Cart->setOffStockProductsAppConfirmationMessage();")
x urob tam pekné checkboxy a radiobuttony
x responzivita: okrem košíka sa v ostaných krokoch na obrazovkách < 790px zobrazuje na pravej strane tenký pásik. Prever to keď bude hlavička definitívne hotová - možno niečo odtiaľ presahuje.
x štýly platobného linku:
    x pri hovery je text oranžový = zmizne
    x pridaj padding hore
- Mail novej objednávky:
    x? Chcú tam mať presne tie isté dĺžky orezaní textov? >> je to v pohode
    x? chcú tam mať aj počet dní dodania? ($product['total_shipment_time']) - pri predpredaji to napríklad nemajú
    x! pridaj tam info o aktuálnom stave bonusu a o zľavovom kóde (pridaj aj popis vsuviek do adminu)

## Wishlist
x skontrolovať štýly wishlistu (zoznam a správa wishlistov)
x oprav náhľad na zdieľaný wishlist (hodí to NOTICE ohľadom toho že recordsView je nedefinované)
- oprav text pri pokuse odoslať wishlist na email ktorý je nevalídny (ide vlastne o text validačnej chyby)

## Vouchers
- prever funkcionalitu

******
SERVER
******
- spýtaj sa Michala či tam môžu byť tie isté nastavenia ako na starom alteregu (extension=dbase.so asi nie - ani som nedal)
x po zverejnení zapni https

***********************************************************
************************ VYDAVATEL ************************
***********************************************************

Projekt bol naklonovaný z vydavatel.sk a nasledovné todo boli napísané ešte na projekte vydavatel.sk

***
INÉ
***
- EshopProductImport::importAddition() - oprav "'alternative' => $this->supplier," na "'alternative' => $this->importPid,"
- Do objednávkového procesu pridaj aj krajinu Srbsko (kvôli slovákom vo Vojdine).
- Formulár na prihlásenie do mailing listu pôsobí metúco (najme výrazné tlačítko "Odoslať") - bu ho v objednávkovom procese skyry alebo daj výraznejšie tlačítko "Pokračovať" a "Objednať s povinnosťou platby"

****************************************
ZJEDNOTENIE IMPORTOV - AKTUALIZÁCIA CIEN
****************************************

Návrhy:
    x pridanie možnosti danému produktu vypnúť aktualizáciu cien. Pridal by sa stĺpec EshopProduct.synchonize_price_with_suppliers a v EshopSupplierProduct::synchonize() > saveBatch() > 'ignoreNewValueIf' - nastav pre polia cien (price, discount_rate, tax_rate) aj podiemnku 'EshopProduct.synchonize_price_with_suppliers' => true, 

**************************************
PARTNER TECHNIC - AKTUALIZÁCIA IMPORTU
**************************************
x export.xml - v novej dokumentácii tam chýba tag DPH
    

********************
ZJEDNOTENIE IMPORTOV
********************
- problematické produkty
    - 2019-05-02: id 192522, kč. 296821 - objednany (56899) napriek tomu že bol vypredaný, v čase objednávky bol "dostupný", keď som kontroloval tak už bol správny stav "vypredaný", upravil som EshopSupplierProduct::getAvailability(), v čase kontroly bol supplier len PT import

x produktom ktoré nie su na sklade u nikoho nastav dlhú dobu dodania alebo shippment_time_offstock aspoň 3 týždňe
x EshopProductImport - pridaj properties podľa upravenej metódy ::copyMissingFields()
x Do importov ktoré sa skončia ako 'interrupted' pridaj odoslanie <NAME_EMAIL> (zalogovanie chyby)
x importné polia ktoré sú zhodné medzi tabuľkami run_eshop_products a run_eshop_supplier_products odstráň z tabuľky run_eshop_products, t.j. import_supplier, supplier_availability, supplier_long_delivery_time, supplier_reprint, import_foreign_key, image_import_source, image_import_result, description_import_source, description_import_result. Polia supplier_... odstráň aj z importov (zrejme to vždy budú len kopie zodpovedajúcich polí bez supplier_ - prever to)
x Uprav import obrázkov a popisov (zdroje ťahaj z EshopSupplierProduct)
x zapracuj Model::$tableIndexes do Model::saveBatch() - tam kde sa odstraňujú. Zrejme to ani nie je treba - Model::$tableIndexes sa už teraz zrejme nevytvoria, takže icn nie je potrebné odstraňovať. Prever to.
x pemicUpdate: 'description_import_source', 'image_import_source' importuj do supplier product a nie priamo do produktu. Skontroluj to aj pri ných aktualizačných importoch a aj importoch nových
x na začiatku je potrebné naimportovať supplier products (SP) z plných katalógov (ceny a dostupnosti)
x aktualizačný import - načítaj aj ostatné parametre dostupnosti (available_from, reprint, long_delivery_time)
x do prehľadu supplier products v admin forme produktu pridaj aj polia created (Dátum vytvorenia) a modified (Dátum zmeny)
- EshopSupplierProduct::synchronize() - if there is more than one best-availability-product then there can be used other criterias to get optimal product (considering price, supplier, ...)
- ak by sa raz aktualizavali aj ceny cez EshopSupplierProduct tak tam tiež presuň funkcionalitu EshopProductImport::setInactiveWithoutPrice()
x preskúmaj či má stale vyznam že PemicAvailabilityCsvReader::readRecord() vytvára fiktívne záznamy - Nie, zrušil som to (tak že som nenastavil option 'processEans')
x po pár dňoch skontroluj:
    x neaktívne produkty s cenou 0 a premysli ako ich znovu aktivovať - zmaž ich (vytvoria sa znovu) - prever či sa už náhodou nepreniesli do MRP (asi hej  - potom radšej nejak jednorázovo aktualizuj ceny). Najlepšia varianta - jednorázovo aktivuj ceny ze EshopSupplierProdut::synchronize() ale len tam kde stará cena bola 0.
    x či sa spustili všetky synchronizačné dopyty (v debug.log) a potom zakomentuj debugovacie logy

Otázky:
-------
x Tiež sa klienta spýtaj či sa nejak zmení použitie percent zliav podľa výrobcu (EshopProductImport::loadManufacturerDiscountRates()) >> Nechal som to tak ako to fungovalo doteraz, nik nenamietal
- možno by bolo fajn doplniť niektoré importy (napríklad PT aktualizácia obrázkov sa nerobí)
x Majú sa v datach suppliera ukladať ceny upravené (so zľavou podľa výrobcu) alebo pôvodné ceny? >> Ceny sa zatiaľ neimportujú, takže som to neriešil. Ale nastavil som rovnaké ceny ako sa nastavujú produktu (t.j. upravené).
x Možno by bolo fajn urobiť začistenie produktov v DB? >> Dohodli sme sa že niekedy do konca roka 2019.
x Ako sa vypočítava EshopSupplierProduct.discount_price? >> Rovnako ako EshopProduct.discount_price
x Ako sa určí cena ak je produkt dostupný u viacerých dodávateľov no u niektorých je skladom a u iných nie? >> Cena sa zatial neimportuje (na základe dohody s p. Zjavkovou)
x PEMIC aktualizacia cien - má sa robiť rovno nad produktami a ak áno tak nad všetkými? (ideálne to bude cez supplier product). Treba preskúmať všetky aktualizácie cien. >> Už ju nerobíme (respektíve sa robí len nad poliami EshopSupplierProduct.price a ostáva to zatiaľ nepoužité). Importujú sa len produkty s nastavenou cenou a preto nie je potrebné ju aktualizovať

x Ako sa budú vyhodnocovať produkty ktoré nebudú mať žiadny záznam od dodávateľa? - Jednoducho u ních nebude prebiehať žiadna aktualizácia stavu dostupnosti a stav sa bude nastavovať len ručne (väčšinou ide o produkty VMS)
x Čo ak produkt z importného katalógu dodávateľa zmizne? - Záleží od importu, v prípade ikar a albatros importu sa pre všetky položky chýbajúce v katalógu nastavia EshopSupplierProduct.availability na enum_soldout.
x Je potrebné EshopSupplierProduct.unavailable? - Nie, už som ho odstánil z tabuľky

**************
SLOVART IMPORT
**************
- rabat 25%

Otázky:
-------
- aký je rozdiel medzi PRODUCT a PRODUCTNAME - je to len spätná kompatibilita a bude niektorý odstránený?
- aký je rozdiel medzi EAN a PRODUCTNO - je to len spätná kompatibilita a bude niektorý odstránený?
- čo znamená B2B_AVAILABILITY > 4 - na objednávku? - je produkt dostupný? V akom čase?
- čo znamená B2B_AVAILABILITY > 2 - dodanie oneskorené? - V akom čase sa produkt dodá?
- čo znamená B2B_AVAILABILITY > 3 - novinka? - Je to predpredaj? Aký to má vzťah voči STATE  > 1 - novinka a STATE > 8 - pripravujeme
- ako sú označené knihy v predpredaji?
- aký je rozdiel medzi B2B_AVAILABILITY > -1 (zakázané) a B2B_AVAILABILITY > 0 (vypredané)
- majú nejaký úplný zoznam kategórií?
- ktoré XML tagy sú vždy uvedené a ktoré záznam nemusí mať (napríklad prvým záznamom chýba AUTHORS, PUBLISHERS, LANGUAGES, IMGURL)

- aký shipment_time_off_stock nastaviť keď SLOVART stock je 0? >> Vtedy je produkt vypredaný


****************
ŠPECIÁLNE PONUKY
****************
- (190214) - dorob odznačovanie zľavnených produktov ak je celková dostupná cena košíka menšia ako 0
- V prípade ŠP na základe ceny promovaných produktov v košíku je pomerne naročné neustále udržiavať aktuálny zoznam indexov v poliach promoted_cart_index a disconted_cart_index. Keďže sú pre všetky spárované produkty vždy rovnaké tak riešením by mohlo byť že budú mať len hodnotu TRUE (ktorú nie je potrebné ďalej aktualizovať). Nevýhodou tohoto riešenia je že pri vzájomné spárované zoznamy produktov sa musia vždy vyhľadať preiterovaním na celým polom produktov košíka (a vybarním tých ktore majú nastavené promoted_cart_index a disconted_cart_index na TRUE)
- EshopCart::applySpecialOffers() - začisťovanie "// 2] for special offers applied by cart price or by promoted products cart price" je urobené spôsobom odznačenia všetkých - možno je to úplne ok (a najlepšie) skús však zvážiť či by nebolo lepšie odstrániť len požadovaný nevyhnutný počet produktov v poradí pridania od konca
- EshopCart::applySpecialOffers() - v prípade ŠP na základe ceny košíka alebo ceny promovaných produktov v košíku by sa pri pridávaní produktu do košíka by sa mohli prepočítať optmimálne priradenia (zvlášť ak nemá odporúčanú special_offer_id) tak ako sa to deje pri zmene pomerov v košíku pri týchto ponukách
- Ponuky aplikované podľa ceny košíka som dal zobrazovať pri každom produkte. Uprav preto hlášky v banneroch produktov v takomto prípade. Nemôže tam byť "Pri kúpe tohoto produktu máme pre Vás pripravené nasledovné špeciálne ponuky:" - alebo môže? Uvedená hláška by bola vhodná už pre aplikovateľnú ŠP. Ak nie je aplikovateľná tak daj hlášku "Pri kúpe tohoto produktu máme pre Vás pripravené nasledovné špeciálne ponuky:" - lenže ono sa to zobrazuje pre viacero ŠP naraz a v prípade ŠP pre promované produkty tiež nemusí isť o aplikovateľné ŠP takže JE TO OK TAK AKO TO JE
- Oprav hlášku pri uplatnení ŠP na obsah košíka - namiesto "Cena produktu XY sa zmenila s ohľadom na dostupné ŠP" zmeň na "Cena produktu XY sa zmenila s ohľadom na uplatnené ŠP"
- EshopSpecialOffer::getActive() - mohlo by sa tam pridať nejaké option na základe ktorého by sa spúšťalo generovanie virtuálnych polí promoted_product_ids, discounted_product_ids, matched_promoted_product_ids and matched_discounted_product_ids - pretože nie všade je to potrebné. Mohlo by to byť option 'setVirtualFields'. Option 'getDetails' by sa mohlo premenovať na 'setFileFieldsUrlPaths'
- EshopProduct::getDiscountingSpecialOffers() a ::getPromotingSpecialOffers() - skús tu ukladať do vráteného poľa špeciálnych ponuk namiesto kópií $offer (vo foreach cykle) referenciu na $offer (na konci je aj zakomentované unset($offer) - skús či to pôjde a či to aspoň trochu zrýchli načítanie homestránky. Porovnaj rozdiel s/bez a keď sa v EshopProducts::index() zakomentuje "'getSpecialOffers' => true,"
- Pri výpočte ceny PB v EshopProduct::getActualPrice() sa vyberie špeciálna ponuka s najnižšou cenou. Ak by však dve rôzne ponuky vracali tú istú cenu, tak medzi nimi je možné ešte rozhodnúť na základe ceny ich promovaných produktov (možno len tých v košíku). Výhodnejšia by bola tá u ktorej je priemerná cena promo produktov nižšia. Zistenie priemernej ceny by bolo možno vhodné robiť už pri ukladaní špeciálnej ponuky v admine alebo pre konkrétne produkty v EshopSpecialOffer::getActive()

OTÁZKY:
-------
- ak je zľavnená cena zadaná percentami (25%) tak z ktorej ceny sa vypočítava zľava? Z plnej 25% alebo zo zľavnenej 25%?
- EshopProduct::getSpecialOffers() - keď hľadáš ŠO podľa ceny košíka tak ku cene košíka sa pridať aj cenu produktu, ktorý by sa vložil do košíka? V prípade discounted_products_price_adjustment zadaných absolútnou hodnotou (nie %) by to bolo možné.
- ako sa budú ponuky zobrazovať v zoznamoch (v detaile produktu alebo v košíku)? - podľa dátumu konca vzostupne (najbližšie = nastaršie dátumy ako prvé) alebo sa bude mocť určiť poradie ponúk (sort pole) - sort sa bude buď vyplňať automaticky (zoradený zoznam) alebo sa bude dať ručne vypísať a uplatní sa len pri vybraných ostatné budu zoradené podľa koncového dátumu

NÁVRHY:
-------
- keď je štítok odfajknutý tak ho prekri polopriesvitnou bielou s tým checkmarkom
- Produktom PB s cenou 0 € a zľavou 100% zobraz namiesto ceny nápis "Darček zadarmo"
- V košíku by sa mohli zobrazovať upozornenia že priradeni produktov môže byť urobené aj optimálnejšie a že či to chce klient optimalizovať (momentálna úspora je X po optimalizácii to bude Y)
- Produktom PB s aplikovanou špeciálnou cenou v detaile zobraz pri cene štítok speciálnej zľavy "Keďže v košíku máte PA tak vrámci špeciálnej zľavy ŠP je cena produktu teraz pre Vás nasledovná..."


***************************************************
PRENOS FAKTUR Z MRP A ODOSLANIE FAKTURY ZÁKAZNÍKOVI
***************************************************
- EshopOrder::getInvoiceFile() > generovanie hlavičky na ďalších stránkach: Parameter header-html musí byť URL (nie HTML) a preto by bolo potrebné vytvoriť verejnú akciu EshopOrders::getInvoiceHtml($token, $part). Pomocou tokenu by sa vytiahli všetky invoice data uložené v tmp súbore ($invoice, $eans, $format, ...). Tento súbor by sa vytvoril v EshopOrder::getInvoiceFile() pred volaním wkhtmltopdf a zmazal by sa po ňom. Ochranené by to bolo tým že akcia EshopOrders::getInvoiceHtml($token, $part) by sama o sebe bez pomoci EshopOrder::getInvoiceFile() nevygenerovala nič)
x Keď p. Kozubík zapracuje vyhľadávanie faktúr podľa čísla objednávky tak zruš pole invoice_number (vymaž všetky jeho výskyty) a dopyty na faktúry rob jednoducho cez číslo objednávky.
- Keď p. Kozubík zapracuje export pdf faktúry tak uprav metódu EshopOrder::getInvoiceFile() tak že nebude generovať pdf same ale len uloží obdržané pdf data.
- Generovanie faktúr prenes aj do core_v1, t.j. EshopOrder::getInvoiceFile() a EshopOrder::createInvoicePdf() - poznač aspoň do todo číslo revízie.
- Mohlo by sa pridať aj mazanie faktúr v admine

************************
PRENOS OBJEDNÁVOK DO MRP
************************
- objednávky 45640 a 45639 sa preniesli do MRP 2x. Asi k tomu došlo tak že MRP na požiadavku neodpovedalo dostatočne rýchlo (php skript skončil), tieto objednávky ostali označené ako neprenesené a preniesli sa ešte raz. Treba premyslieť spôsob ako tomuta zabrániť:
    - navýšenie exec time pre prenos objednávok
    - prípadne pred každým exportom overiť či objednávky s daným číslom už náhodou neexistuje, prípadne overiť či ide o tú istú ako je na webe (porovnaním ceny a produktov)
    - nastaviť volanie cronu na interval väčší ako je max exec time (*/max exec time + 1)

**************************************
REDIZAJN 2016 A PRENESENIE NA FAJNWORK
**************************************
- Dizajn admina:
    - v bočnom menu pridaj js scroll bar
    - zoznam objednávok - "Akcia" je čiernym, oprav na bielu
    - EshopProducts::edit_many() - zoznam produktov je mimo plochy
    - loading gif "run" urob tiež v jasne modrej farbe
    - Spravca súborov - dolaď dizajn hlavičky (výška, hrúbka fontu), Skontroluj v novom admine aj farebnosť neaktívnych polí (užitočné najmä v prípade prekladov)
    - dokončiť admin (univerzálny filter)
    - upraviť šírku hlavičky pri prvom načítaní tak aby sa zobrazil celý nadpis (názov stránky)
    - stiahni Font Awesome 4.7.0 a doplň nové ikonydo mailera aj na vymazanie obrázku a zrušenie tabu (je tam delete v stvorci)
    - pridaj tam aj responzinvu verziu - namalých obrazovkách bčnú navigáciu zbaľ a umiestni nad hlavné okno (tak ako napr menu kategórií)
- Aktualizuj export do Pricemanie (nad rámec)
- Otázka p. Zjavkovej - bolo by možné pripojiť čítačku ean kódu k webu? - http://www.synet.sk/php/en/280-barcode-reader-scanner-in-php
- Zmeniť výber odporúčaných produktov (buď nech sú to najlepšie hodnotené, alebo nech ich zadávajú ručne) Nemá význam aby tam bolo takmer to isté ako v novinkách (toto ešte dohodni s pani Zjavkovou cez telefón)
- vodoznask na obrázky (nad rámec, ale už asi nechcú)
- Pozri všetky výskyty HARDCODED a VYDAVATEL SPECIFIC a premysli ako z toho urobiť generickú funkcionalitu aby sa dal projekt vydavateľ plynule ďalej klonovať. Napr v EshopCart::getAdditionalDiscountRates() je natvrdo id vydavatela 3 pre $voucher['special_discount_rate']; Urob to tak že bude nastavenie v ktorom vyberieš výrobcov pre specialnu zľavu, ...
- Čo sa týka  dostupností bolo by fajn násť nejaké definitívne riešenie pre možné situácie stavov knižného eshopu. 22.12.2016 napr p. Zjavková potrebovala prednastaviť stav "predpredaj - reprin" pre knihu ktorej ešte mali posledné kusy na sklade ale reprint už bol naplánovaný na 22.02.2017. No keďže na sklade ešte boli kusy tak DB TRIGGER zmenil vždy stav "presale" na "available" ... - momentálne riešenie v tejto situácii bolo nechať nech sa produkt vypredá, a potom mu nastaviť nový stav

- ZAPRACOVAŤ IBA AK TO BUDÚ ŽIADAŤ:
    - Zisti prečo p. Zjavkovej nejde nahrať v Správcovi súborov na server EXPORTKARIET.csv - píše že súbor je príliš veľky. Zatiaľ som jej poslal FTP prístup
    - Admin > indexy: pridaj kombo na zmenu počtu položiek na stránke (požiadavka klienta, bolo to možné predtým)
    - zoznam vydavateľstiev (link v ľavom stlpci na starom webe)
    - zoznam autorov (link v ľavom stlpci na starom webe)
    - EshopProduct.ebook_url - zobraz vo view produktu. !!!používaju vôbec ebook_url? viď napr http://vydavatel.sk/kniha/povesti-z-liptova alebo http://vydavatel.sk/kniha/torty-a-mucniky - nikam to nesmeruje (forbidden)
    - http://www.vydavatel.sk/akcie/knihy-za-symbolicke-ceny - pridaj sem breadcrumbs alebo aspoň padding na vrchu stránky
    - Admin > zoznam produktov - vstup na hromadné vyhľadávanie a pridaj sem všetky stĺpce ktoré sú v starom admine.
    - prenes funkcionalitu ohľadom nastavenia 'Eshop.EshopProduct.reprintTransitionalDays' - spýtaj sa či to vlastne vôbec má oddôvodnenie.
    - chcú preniesť aj podrobný vyhľadávací formulár na frontende (http://vydavatel/hladat)?
    - detail produktu - ak sú nejaké komentáre tak zobraz len button z komentároveho formuláru (tak je to v dizajne)
    - Detail produktu - odpurúčané knihy: ide vlastne o produkty, ktoré si uzívazelia kúpili spolu s aktuálnym produktom. My síce zobrazujeme súvisiace knihy ale to sú zobrazené priradené RelatedProducts
    - Import produktov - na koniec pridaj spustenie importu images a description cez App::request() aby sa obrázky začali importovať okamžite po ukončení importu produktov
    - spýtaj sa či partnertechnic poskytuje katalógy na stiahnutie aj v zip. Spýtaj sa tiež či export.xml obsahuje aktuálne ceny
    - Študentskú zľavu (vydavatel: 216:bf688efe0702 - 217:168da5c7531d) som zatiaľ neprenášal, nemajú  licenciu - ak ju raz dostanú tak im to zapracujeme
    - Migration::migrateEshopOrders() - pri prenose študentských zliav prenes v objednávke aj polia student_card_name a student_card_discount_rate (je toto vôbec funkčné?)
    - Ako zobraziť heureka odznak na mobilných zariadeniach? - poslal som mail - momentálne sa to ešte nedá, pracujú na tom
    - Hlavičkové produkty - dolaď výpočet výšky produktu pri zalomení do riadkov (aby sa vypočet obmedzil len na položky riadku)
    - skupiny top-10 a odporúčame sa zrejme môžu zmazať (?)
    - Chcú preniesť aj link doprava a platba v detaile produktu?
    - Namiesto kniha mesiaca zobraz bočné bannery (nad rámec - už to nejak používajú, chcú to ešte zmeniť?)

- INTERNÉ SVEDOMIE:
    - filter jazyka trvá fest dlho
    - /var/www/vydavatel/app/plugins/vydavatel/models/vydavatel_eshop_product.php ::importDvd() som neprenášal lebo v DB nie je ani 1 produkt označený created_by = 'D', t.j. zdá sa že to vôbec nepoužívajú. ::importDvd() sa používa aj na import BLUERAY.
    - Keď bude všetko hotovo tak odstráň stĺpce EshopProduct.for_beginners/for_advanced/for_professionals
    - Zapracuj Nastavenia EshopProduct.weightUnits a EshopProduct.dimensionsUnits do administrácie produktu
    - EshopProduct.weight - pri importoch skontroluj že sa importuje float a nie string s jednotkami
    - Zoznam duplicit v mailoch užívateľov:
        SELECT * FROM `users` WHERE email IN (
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        )
    - EshopProductGroups - prever zapracovanie tag pola do formularu
    - (Toto už nepoužívajú) Pozri ako zavrieť okno po GEIS exporte - dalo by sa to tak že:
    - (nefunguje - lebo tab sa zavrie pomocou novo načítaného js, no v tomto prípade sa js nenačíta) by východzí buton bol uložit a zavrieť (aspoň jeho funkcionalita na pozadí, napísané by mohlo byť čokoľvek)
    - by sa dopyt na server urobil len cez ajax a po odpovedi by sa tab zavrel pomocou pôvodného js
    - EshopProductImport::importFromSource() - tiež to ukladaj cez batch
    - Spoj views pre EshopProducts::index() a ::indexRecommended() do view/EshopProducts/index.php
    - /app/views/elements/htmlBodyHeader.php - indexRecommended daj do showcase elementu
    - optimalizuj vyhľadávanie produktov, skús urobiť query len na id a potom najsť detaily nastránkovaných produktov
    - nasledovná query trvá 19s:
        SELECT COUNT(DISTINCT `EshopProduct`.`id`) AS `_count` FROM `run_eshop_products` AS `EshopProduct` LEFT JOIN `run_eshop_product_category_products` AS `EshopProductCategoryProduct` ON (`EshopProductCategoryProduct`.`run_eshop_products_id` = `EshopProduct`.`id`) WHERE `EshopProduct`.`active` = '1' AND `EshopProduct`.`deleted` IS NULL AND `EshopProductCategoryProduct`.`run_eshop_product_categories_id` IN ('2','22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37','38','39','41','42','43','44','45','46','47','48','49','50','51','52','53','54','55','56','57','58','59','60','61','62','63','64','65','66','67','68','69','70','71','72','74','75','76','77','78','80','81','82','83','84','85','86','87','88','89','90','91','92','93','94','95','96','97','98','99','100','101','102','103','104','105','106','107','108','109','110','112','113','114','115','116','117','118','119','120','121','122','123','124','125','126','127','128','129','130','131','132','133','134','135','136','137','138','139','140','141','142','143','144','145','146','147','148','149','150','151','152','153','154','155','156','157','158','159','160','161','162','163','164','165','166','167','168','172','173','174','175','176','177','178','179','180','181','182','183','184','185','186','187','188','189','190','345','354') LIMIT 1;
            Sposobuje to ten DISTINCT?
    - zisti prečo sa produkty na stránke http://vydavatel_new/kategoria/kucharky-a-napoje?limit=25&sort%5Bactual_price%5D=ASC nezoraďujú správne
    - EshopProductGroup.sale/tag - zjednoť to na tag

******************
SLAJDER V HLAVIČKE
******************
- ľavá šípka je bližšie ku knihám ako pravá
- úprav polohu šípiek aj výškovo

*************************************************
CARDPAY - storno platby (!!! už sa nepoužíva !!!)
*************************************************
- CardPay pri zlyhaní vyhodí chybu (chýba TID ktorý asi medzičasom prestali pripájať pri FAIL):
    >> 2018-09-13 08:39:36 *************** Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.140 Safari/537.36 Edge/17.17134 (/mvc/Eshop/EshopOrders/processPaymentResponse/_pm:cardpay/_vs:47688?AMT=13.63&CURR=978&VS=47688&RES=FAIL&TIMESTAMP=13092018063850&HMAC=b3a10d38e19e7d8dea3384390183ccdcea1b9c42f6c196983d78f6682f24e857&ECDSA_KEY=1&ECDSA=3046022100c29d61d06c56f19f82a2450b3b26cccffba4e7b4e36e4ad560715b4c01b9f34f0221009e747bff8cedd29508e75ba6a349f70f83d23c7051305be8bfb815338d57542a)
    - [Mandatory response param(s) TID is(are) missing for payment method cardpay] /app/modules/Eshop/models/EshopOrder.php (line 2667)

******
NAVRHY
******
- Objednávkový proces > Vyber doručenia platby - ak ostane len 1 platobná metóda tak nech je predvyplnená. Tiež si to môže pamätať posledné použíté (viď martinus)
- pridať ďalšie marketingové sekcie podľa Martinusu (najnovšie, najlepšie hodnotene, z tej istej edície, od toho istého autora, ...)
- porozmýšľať ako potiahnúť klienta inde na stránku, aby ho vždy ešte niečo zaujalo
- pri importe dostupnosti brať do úvahy dostupnosti od všetkých dodavateĺov
- Zmeniť výber odporúčaných produktov (buď nech sú to najlepšie hodnotené, alebo nech ich zadávajú ručne) Nemá význam aby tam bolo takmer to isté ako v novinkách
- Časom im môžeme navrhnúť ukladanie košíka do wishlistu - prenieslo by sa z alterega.
- Vo wishliste (prenesenom z drinku) im pribudol watchdog - nemali to predtým a ani sa to nedá zistiť že to tam je, môžeme im to predať
- odosielanie narodeninových emailov tak ako to robí martinus
- chcú aj odbornú literátúru?
- Zafixovať horné menu
- Košíka by sa nemusel prenášať celý, v cookie by bolo len nejaké id a s tým by sa párovali data v DB. Potom by v DB ostávali opustené košíky - tie by sa buď čistili kronom alebo by sa aj mohli využiť aby obchodník videl čo si klient chcel kúpiť ale nekúpil. Dali by sa robiť aj mailingové kampane pre nerozhodnutých klientov a každému by sa medzi inými (podobnými) poslali aj také ktoré chcel kúpiť.
- ak je klient prihlásený tak logovať jeho pohyb po stránke
- prežúvaci kron by pripravoval data ktoré má stránka zobraziť klientovi, samotný dopyt by už len chytil pripravené "napady" a zobrazil ich.
- na párovanie neprihlásených klientov by sa používala dlhodobá cookie s id
- mikrotagy, ked das vyhľadat knihu na martinus tak v  2. riadku zobrazi 'www.martinus.sk › ... › Umelci, spisovatelia' namiesto 'www.vydavatel.sk/kniha/narodny-kalendar-2016'
- pridať konverzný kód, tagmanažer, všetko čo google analytics vedia
- Zistiť akú IP majú Martinus, Pantharei a keď príde ich robot podstrčiť mu nejaké 2.triedne data
- pridať možnosť priradiť zlavový kód len vybraným produktom (žadali to v 1. októbrovom týždni 2016, potom to už bolo neaktuálne ale možno...)
INTERNÉ
- pri spustení vyhľadávanie može načítanie trvať dlhšie, mohol by sa tam zobraziť nejaký processing gif, prípadne také koliečko ako je v admine s logom vydávoteľa uprostred
- spýtaj sa ich či chcú pri importe nových preskakovať produkty ktoré sa nepodarilo zaradiť do žiadnej kategórie. Z tohoto vyplýva že by bolo fajn v zozname produktov zobraziť kateórie do ktorých je zaradený
- popremýšľaj či dedenie filtrov a sortov v paginatori nemá vplyv na google bota

*****************************
SYNTAX POZNÁMOK JE NASLEDOVNÁ
*****************************
- úloha
x urobená úloha
-x zrušená úloha
-! dôležitá úloha
x! urobená dôležitá úloha
-? otázka
x? zodpovedaná otázka >> Odpoveď na otázku
-?! dôležitá otázka
x?! zodpovedaná dôležitá otázka >> Odpoveď na otázku
-+ úloha ktorá nebola v časovom odhade 
-+! tutti-brutti úloha ktorá nebola v časovom odhade 
x+ úloha ktorá nebola v časovom odhade no urobila sa
- úloha s podúlohami
    - podúloha
    x urobená podúloha
    -? otázka k úlohe
    x? zodpovedaná otázka k úlohe >> Odpoveď na otázku
- poznámka so zoznamom, ktorého položky nie sú podúlohami:
    -- toto nie je podúloha
    -- ani toto nie je podúloha

***********************
HLAVNÉ ZADANIE (~TIKET)
***********************
- ...

------------------------------------------------------
Podzadanie (~podúloha tiketu) alebo podsekcia poznámok
------------------------------------------------------
- ...

---------------------------------------------
Vyhľadávanie úloh pomocou regulárnych výrazov
---------------------------------------------
- nedokončené úlohy (aj s otázkami):    ^\s*-[^\-x]
- dokončené úlohy (aj s otázkami):      ^\s*x\??!?
- nedokončené úlohy (bez otázok):       ^\s*-[^\-x\?]
- dokončené úlohy (bez otázok):         ^\s*x!?[^\?]
- zrušené úlohy:                        ^\s*-x
- nezodpovedané otázky:                 ^\s*-\?!?
- zodpovedané otázky:                   ^\s*x\?!?
- úlohy, ktoré neboli v časovom odhade: ^\s*[\-x]\+!?

