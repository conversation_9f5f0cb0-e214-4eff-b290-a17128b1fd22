<?php
if (!isset($_SERVER['argv'])) {
    echo "Please, call this script from command line like 'php hgexport.php -h'\n";
    exit;
}
$args = &$_SERVER['argv'];
// separate args and options (--my-option[=with-some-possible-value])
$options = array();
foreach ($args as $i => $arg) {
    if (preg_match('/^-{1,2}[a-z0-9]/i', $arg)) {
        $optionName = ltrim($arg, '-');
        $optionValue = true;
        if (strpos($optionName, '=') !== false) {
            $optionValue = explode('=', $optionName);
            $optionName = array_shift($optionValue);
            $optionValue = implode('=', $optionValue);
        }
        $options[$optionName] = $optionValue;
        unset($args[$i]);
    }
}
$args = array_values($args);

function _getRevNumber($rev = null) {
    $output = array();
    if ($rev) {
        exec("hg id -r {$rev} -n", $output);
    }
    else {
        exec('hg id -n', $output);
    }
    return $output[0];
}

function _getRevId($rev = null) {
    $output = array();
    if ($rev) {
        exec("hg id -r {$rev} -i", $output);
    }
    else {
        exec('hg id -i', $output);
    }
    return $output[0];
}

$currRev = trim(_getRevNumber(), ' +');
$currRev .= ':' . _getRevId();
if (substr($currRev, -1) === '+') {
    $currRev = trim($currRev, ' +') . " \e[01;37mwith uncommited changes\e[00m";
}

// provide help
if (
    isset($options['h']) 
    || isset($options['help']) 
) {
    echo "php hgexport.php START_REV END_REV EXPORT_DIR [--export-sources]\n";
    echo "\n";
    echo "\e[01;37mATTENTION:\e[00m Files are exported as they are in the actual state of repository (commited or uncommited).\n";
    echo "Provided start/end revisions are used just to find modifications in project which happened between them.\n";
    echo "They do not change the actual revision of project.\n";
    echo "To export files in versions from some specific revision or branch update the project accordingly by hg update.\n";
    echo "In the most of cases you will export the last version of files (you are working on) for FTP update.\n";
    echo "\n";
    echo "\e[01;37mATTENTION:\e[00m By default *.js and *.less (placed in directories .../js/sources/*.js,\n";
    echo ".../js/libs/sources/*.js, .../css/less/*.less and .../css/libs/less/*.less) are NOT exported.\n";
    echo "Use option --export-sources to export also *.js and *.less source files.\n";
    echo "\n";
    echo "\e[01;37mATTENTION:\e[00m Be aware that revisions in you local repository can have different local number\n";
    echo "than they have in repositories of your colleagues. When looking for correct revision number\n";
    echo "in your repository check always the global revision number (hash).\n";
    echo "To find your local number for global id use: hg id -r d4f5c4821e14 -n\n";
    echo "\n";
    echo "Usage:\n";
    echo "php hgexport.php 10 20 /tmp/export\n";
    echo "    Exports all files changed from rev 10 to 20 (inclusive) into directory /tmp/export.\n";
    echo "    The *.js and *.less sources are NOT exported.\n";
    echo "\n";
    echo "php hgexport.php 10 20 /tmp/export --export-sources\n";
    echo "    Exports all files changed from rev 10 to 20 (inclusive) into directory /tmp/export.\n";
    echo "    The *.js and *.less sources are exported too.\n";
    exit;
}

// validate and read input
if (empty($args[1])) {
    echo "Missing start revision\n";
    exit;
}
$rev1 = $args[1];
if ((string)$rev1 !== (string)(int)$rev1) {
    echo "Start revision must be an integer number\n";
    exit;
}

if (empty($args[2])) {
    echo "Missing end revision\n";
    exit;
}
$rev2 = $args[2];
if ((string)$rev2 !== (string)(int)$rev2) {
    echo "End revision must be an integer number\n";
    exit;
}

if ($rev1 > $rev2) {
    echo "Start revision is greater that end revision\n";
    exit;
}

if (empty($args[3])) {
    echo "Missing export dir\n";
    exit;
}
$dir = $args[3];
if (!is_writable($dir)) {
    echo "Directory $dir does not exist or it is not writable\n";
    exit;    
}
$dirFiles = scandir($dir);
if ($dirFiles === false) {
    echo "$dir is not a directory\n";
    exit;    
}
foreach ($dirFiles as $i => $file) {
    if ($file === '.' || $file === '..') {
        unset($dirFiles[$i]);
    }
}
if (!empty($dirFiles)) {
    echo "\e[01;37mATTENTION:\e[00m  Directory $dir is not empty!\n";
    echo "Press ENTER to continue or CTRL+C to exit\n";
    fgets(STDIN);
}

echo "Processing...\n";

// export
// - find exported and removed files
$modified = array();
$removed = array();
for ($rev = $rev1; $rev <= $rev2; $rev++) {
    $output = array();
    exec('hg status --change ' . $rev, $output);
    //print_r($output); //debug
    foreach ($output as $item) {
        $sign = strtoupper(substr($item, 0, 1));
        $file = substr($item, 2);
        if (
            $sign === 'A'
            || $sign === 'M'
        ) {
            $modified[$file] = true;
            unset($removed[$file]);
        }
        elseif ($sign === 'R') {
            $removed[$file] = true;
            unset($modified[$file]);
        }
        else {
            echo "Invalid output of hg status\n";
            print_r($output);
            exit;    
        }
    }
}
$modified = array_keys($modified);
$removed = array_keys($removed);
sort($modified);
sort($removed);
//print_r($modified); //debug
//print_r($removed); //debug

$ds = DIRECTORY_SEPARATOR;
// - remove js and less sources in exported files (keep the .htaccess and other files)
// - get updates
$vendorsRegex = str_replace('/', $ds, '#/vendors/#');
$sourcesRegex = str_replace('/', $ds, '#/(?:css|js)(?:/libs)?/(?:less|sources)/#');
$updatesRegex = str_replace('/', $ds, '#/updates/#');
$ignoredRegexes = array(
    str_replace('/', $ds, '#^\.devcontainer/#'),
    str_replace('/', $ds, '#^\.vscode/#'),
    str_replace('/', $ds, '#^misc/#'),
    str_replace('/', $ds, '#^\.dockerignore#'),
    str_replace('/', $ds, '#^\.editorconfig#'),
    str_replace('/', $ds, '#^\.hgignore#'),
    str_replace('/', $ds, '#^book\.json#'),
    str_replace('/', $ds, '#^book\.readme#'),
    str_replace('/', $ds, '#^composer\.json#'),
    str_replace('/', $ds, '#^composer\.lock#'),
    str_replace('/', $ds, '#^default\.hgrc#'),
    str_replace('/', $ds, '#^gulpfile\.js#'),
    str_replace('/', $ds, '#^hgexport\.php#'),
    str_replace('/', $ds, '#^package-lock\.json#'),
    str_replace('/', $ds, '#^package\.json#'),
    str_replace('/', $ds, '#^tsconfig\.json#'),
);
function _isIgnoredFile($file, $ignoredRegexes) {
    foreach ($ignoredRegexes as $ignoredRegex) {
        if (preg_match($ignoredRegex, $file)) {
            return true;
        }
    }
    return false;
}
$updates = array();
foreach ($modified as $i => $file) {
    if (
        !isset($options['export-sources'])
        && !preg_match($vendorsRegex, $file)
        && preg_match($sourcesRegex, $file)
        && (
            ($extension = pathinfo($file, PATHINFO_EXTENSION)) === 'less'
            || $extension === 'js'
        )
        ||
        !preg_match($vendorsRegex, $file)
        && _isIgnoredFile($file, $ignoredRegexes)
    ) {
        unset($modified[$i]);
    }
    elseif (
        !preg_match($vendorsRegex, $file)
        && preg_match($updatesRegex, $file)
    ) {
        $updates[] = $file;
    }
}

// - export modified files, 
$root = dirname(__FILE__);
$root = rtrim($root, $ds) . $ds;
$dir = rtrim($dir, $ds) . $ds;
if (!empty($modified)) {
    echo "\n\e[01;37mExported files\e[00m (in version from revision $currRev):\n";
    foreach ($modified as $file) {
        if (!file_exists($root . $file)) {
            echo "\e[01;37mATTENTION:\e[00m  File $file does not exist in actual revision ($currRev)!\n";
            continue;
        }
        if (!is_readable($root . $file)) {
            echo "\e[01;37mATTENTION:\e[00m  File $file is not available (check the rights)!\n";
            continue;
        }
        if (!is_file($root . $file)) {
            echo "\e[01;37mATTENTION:\e[00m  $file is not a file!\n";
            continue;
        }
        $file = ltrim($file, $ds);
        $subDirs = explode($ds, $file);
        array_pop($subDirs);
        $partialDir = rtrim($dir, $ds);
        foreach ($subDirs as $subDir) {
            $partialDir .= $ds . $subDir;
            if (!file_exists($partialDir)) {
                if (!@mkdir($partialDir)) {
                    echo "\e[01;37mATTENTION:\e[00m  Creation of path $partialDir has failed!\n";
                }
            }
        }
        if (!@copy($root . $file, $dir . $file)) {
            echo "\e[01;37mATTENTION:\e[00m  Copying of file $file has failed!\n";
            continue;
        }
        echo $file . "\n";
    }
}

if (!empty($removed)) {
    echo "\n\e[01;37mRemoved files:\e[00m\n";
    foreach ($removed as $file) {
        echo $file . "\n";
    }
}

if (!empty($updates)) {
    echo "\n\e[01;37mUpdate files:\e[00m (in version from revision $currRev)\n";
    foreach ($updates as $file) {
        if (!file_exists($root . $file)) {
            echo "\e[01;37mATTENTION:\e[00m  File $file does not exist in actual revision ($currRev)!\n";
            continue;
        }
        if (!is_readable($root . $file)) {
            echo "\e[01;37mATTENTION:\e[00m  File $file is not available (check the rights)!\n";
            continue;
        }
        if (!is_file($root . $file)) {
            echo "\e[01;37mATTENTION:\e[00m  $file is not a file!\n";
            continue;
        }
        echo $file . "\n";
    }
}

// add EXPORT_REV
// - find user name
$userName = 'unknown user';
$output = array();
exec('hg config', $output);
foreach ($output as $config) {
    if (substr($config, 0, 12) === 'ui.username=') {
        $userName = substr($config, 12);
        break;
    }
}
$fullUserName = $userName;
if ($userName) {
    // if username contains an email then use name in email as user name
    $match = array();
    if (preg_match('/[a-z0-9\.\_\-]+@[a-z0-9\.\_\-]+\.[a-z]{2,4}/i', $userName, $match)) {
        $userName = $match[0];
        $userName = explode('@', $userName);
        $userName = reset($userName);
    }
    $userName = strtoupper(preg_replace('/[^a-z0-9]+/i', '_', $userName));
}
// - find project name
if (!($projectName = getenv('DOCKER_WORKDIR_LOCAL_BASENAME'))) {
    $projectName = explode($ds, trim($root, $ds));
    $projectName = end($projectName);
}
$projectName = strtoupper(preg_replace('/[^a-z0-9]+/i', '_', $projectName));
// - create filename and save export revision of user
$filename = 'EXPORT_REV';
if ($userName) {
    $filename .= '_' . $userName;
}
if ($projectName) {
    $filename .= '_' . $projectName;
}
$output = array();
exec('hg log -r ' . $rev2, $output);
array_unshift($output, '');
array_unshift($output, "Exported from $root (in version from revision $currRev) by $fullUserName:");
$revId1 = _getRevId($rev1);
$revId2 = _getRevId($rev2);
$numberOfRevisions = $rev2 - $rev1 + 1;
$output[] = "Export was done from revision {$rev1}:{$revId1} to revision {$rev2}:{$revId2} ({$numberOfRevisions} revisions together).";
$output[] = '';
$output[] = "ATTENTION (if you are not the user \"{$fullUserName}\"): To find the end revision number";
$output[] = "in your local repository use command \"hg id -r {$revId2} -n\". Very probably it is different from {$rev2}.";
$output[] = "Moreover if some of your last revisions have not been pushed yet and merged with revisions you have just pulled";
$output[] = "then you must export even these revisions older than all pulled ones. You should look for first \"merge\" commit";
$output[] = "older that all pulled revisions and export from there (BUT even this can be false one if the \"merge\" commit";
$output[] = "was done by you and you have not pushed the commit to shared repository on server).";
$output[] = "If you have your own  \"EXPORT_REV_\" file on FTP then export from that revision.";
//print_r($output); //debug
file_put_contents($dir . $filename, trim(implode(PHP_EOL, $output)));