# example repository config (see 'hg help config' for more info)
[paths]
default = ssh://snahnican@91.148.0.111//home/<USER>/Documents/unisport

# path aliases to other clones of this repo in URLs or filesystem paths
# (see 'hg help config.paths' for more info)
#
# default:pushurl = ssh://<EMAIL>/hg/jdoes-fork
# my-fork         = ssh://<EMAIL>/hg/jdoes-fork
# my-clone        = /home/<USER>/jdoes-clone

[ui]
# name and email (local to this repository, optional), e.g.
# username = <PERSON> <<EMAIL>>

### autogenerated include by App::install() ###
%include ../default.hgrc