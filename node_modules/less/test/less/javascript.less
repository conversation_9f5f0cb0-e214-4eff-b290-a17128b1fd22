.eval {
    js: `42`;
    js: `1 + 1`;
    js: `"hello world"`;
    js: `[1, 2, 3]`;
    title: `typeof process.title`;
    ternary: `(1 + 1 == 2 ? true : false)`;
    multiline: `(function(){var x = 1 + 1;
           return x})()`;
}
.scope {
    empty: `+function(){}`;
    @foo: 42;
    var: `parseInt(this.foo.toJS())`;
    escaped: ~`2 + 5 + 'px'`;
}
.vars {
    @var: `4 + 4`;
    width: @var;
}
.escape-interpol {
    @world: "world";
    width: ~`"hello" + " " + @{world}`;
}
.arrays {
    @ary:  1, 2, 3;
    @ary2: 1  2  3;
    ary: `@{ary}.join(', ')`;
    ary1: `@{ary2}.join(', ')`;
}
.transitions(...) {
  @arg: ~`"@{arguments}".replace(/[\[\]]*/g, '')`;
  1: @arg;              // rounded to integers
  2: ~`"@{arguments}"`; // rounded to integers
  3: @arguments;        // OK
}
.test-tran {
  .transitions(opacity 0.3s ease-in 0.3s, max-height 0.6s linear, margin-bottom 0.4s linear;);
}
