.error {
  border: 1px #f00;
  background: #fdd;
}
.error.intrusion {
  font-size: 1.3em;
  font-weight: bold;
}
.intrusion .error {
  display: none;
}
.badError {
  &:extend(.error all);
  border-width: 3px;
}

.foo .bar, .foo .baz {
    display: none;
}

.ext1 .ext2 {
    &:extend(.foo all);
}

.ext3,
.ext4 {
  &:extend(.foo all);
  &:extend(.bar all);
}

div.ext5,
.ext6 > .ext5 {
    width: 100px;
}

.ext7 {
    &:extend(.ext5 all);
}

.ext8.ext9 {
  result: add-foo;
}
.ext8 .ext9,
.ext8 + .ext9,
.ext8 > .ext9 {
  result: bar-matched;
}
.ext8.nomatch {
  result: none;
}
.ext8 {
  .ext9 {
    result: match-nested-bar;
  }
}
.ext8 {
  &.ext9 {
    result: match-nested-foo;
  }
}

.fuu:extend(.ext8.ext9 all) {}
.buu:extend(.ext8 .ext9 all) {}
.zap:extend(.ext8 + .ext9 all) {}
.zoo:extend(.ext8 > .ext9 all) {}

.aa {
  color: black;
  .dd {
    background: red;
  }
}
.bb {
  background: red;
  .bb {
    color: black;
  }
}
.cc:extend(.aa,.bb) {}
.ee:extend(.dd all,.bb) {}
.ff:extend(.dd,.bb all) {}