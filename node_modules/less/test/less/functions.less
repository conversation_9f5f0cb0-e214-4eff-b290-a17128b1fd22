#functions {
  @var: 10;
  @colors: #000, #fff;
  color: _color("evil red"); // #660000
  width: increment(15);
  height: undefined("self");
  border-width: add(2, 3);
  variable: increment(@var);
  background: linear-gradient(@colors);
}

#built-in {
  @r: 32;
  escaped: e("-Some::weird(#thing, y)");
  lighten: lighten(#ff0000, 40%);
  lighten-relative: lighten(#ff0000, 40%, relative);
  darken: darken(#ff0000, 40%);
  darken-relative: darken(#ff0000, 40%, relative);
  saturate: saturate(#29332f, 20%);
  saturate-relative: saturate(#29332f, 20%, relative);
  desaturate: desaturate(#203c31, 20%);
  desaturate-relative: desaturate(#203c31, 20%, relative);
  greyscale: greyscale(#203c31);
  hsl-clamp: hsl(380, 150%, 150%);
  spin-p: spin(hsl(340, 50%, 50%), 40);
  spin-n: spin(hsl(30, 50%, 50%), -40);
  luma-white: luma(#fff);
  luma-black: luma(#000);
  luma-black-alpha: luma(rgba(0,0,0,0.5));
  luma-red: luma(#ff0000);
  luma-green: luma(#00ff00);
  luma-blue: luma(#0000ff);
  luma-yellow: luma(#ffff00);
  luma-cyan: luma(#00ffff);
  luma-differs-from-luminance: luma(#ff3600);
  luminance-white: luma(#fff);
  luminance-black: luma(#000);
  luminance-black-alpha: luma(rgba(0,0,0,0.5));
  luminance-red: luma(#ff0000);
  luminance-differs-from-luma: luminance(#ff3600);
  contrast-filter: contrast(30%);
  saturate-filter: saturate(5%);
  contrast-white: contrast(#fff);
  contrast-black: contrast(#000);
  contrast-red: contrast(#ff0000);
  contrast-green: contrast(#00ff00);
  contrast-blue: contrast(#0000ff);
  contrast-yellow: contrast(#ffff00);
  contrast-cyan: contrast(#00ffff);
  contrast-light: contrast(#fff, #111111, #eeeeee);
  contrast-dark: contrast(#000, #111111, #eeeeee);
  contrast-wrongorder: contrast(#fff, #eeeeee, #111111, 0.5);
  contrast-light-thresh: contrast(#fff, #111111, #eeeeee, 0.5);
  contrast-dark-thresh: contrast(#000, #111111, #eeeeee, 0.5);
  contrast-high-thresh: contrast(#555, #111111, #eeeeee, 0.6);
  contrast-low-thresh: contrast(#555, #111111, #eeeeee, 0.09);
  contrast-light-thresh-per: contrast(#fff, #111111, #eeeeee, 50%);
  contrast-dark-thresh-per: contrast(#000, #111111, #eeeeee, 50%);
  contrast-high-thresh-per: contrast(#555, #111111, #eeeeee, 60%);
  contrast-low-thresh-per: contrast(#555, #111111, #eeeeee, 9%);
  replace: replace("Hello, Mars.", "Mars\.", "World!");
  replace-captured: replace("This is a string.", "(string)\.$", "new $1.");
  replace-with-flags: replace("One + one = 4", "one", "2", "gi");
  replace-single-quoted: replace('foo-1', "1", "2");
  replace-escaped-string: replace(~"bar-1", "1", "2");
  replace-keyword: replace(baz-1, "1", "2");
  replace-with-color: replace("007", "0", #135, g);
  replace-with-number: replace("007", "0", 2em);
  format: %("rgb(%d, %d, %d)", @r, 128, 64);
  format-string: %("hello %s", "world");
  format-multiple: %("hello %s %d", "earth", 2);
  format-url-encode: %("red is %A", #ff0000);
  format-single-quoted: %('hello %s', "single world");
  format-escaped-string: %(~"hello %s", "escaped world");
  format-color-as-string: %("%s", #123);
  format-number-as-string: %("%s", 4px);
  eformat: e(%("rgb(%d, %d, %d)", @r, 128, 64));

  unitless: unit(12px);
  unit: unit((13px + 1px), em);
  unitpercentage: unit(100, %);

  get-unit: get-unit(10px);
  get-unit-empty: get-unit(10);

  hue: hue(hsl(98, 12%, 95%));
  saturation: saturation(hsl(98, 12%, 95%));
  lightness: lightness(hsl(98, 12%, 95%));
  hsvhue: hsvhue(hsv(98, 12%, 95%));
  hsvsaturation: hsvsaturation(hsv(98, 12%, 95%));
  hsvvalue: hsvvalue(hsv(98, 12%, 95%));
  red: red(#f00);
  green: green(#0f0);
  blue: blue(#00f);
  rounded: round((@r/3));
  rounded-two: round((@r/3), 2);
  roundedpx: round((10px / 3));
  roundedpx-three: round((10px / 3), 3);
  rounded-percentage: round(10.2%);
  ceil: ceil(10.1px);
  floor: floor(12.9px);
  sqrt: sqrt(25px);
  pi: pi();
  mod: mod(13m, 11cm); // could take into account units, doesn't at the moment
  abs: abs(-4%);
  tan: tan(42deg);
  sin: sin(10deg);
  cos: cos(12);
  atan: atan(tan(0.1rad));
  atan: convert(acos(cos(34deg)), deg);
  atan: convert(acos(cos(50grad)), deg);
  pow: pow(8px, 2);
  pow: pow(4, 3);
  pow: pow(3, 3em);
  min: min(0);
  min: min(6, 5);
  min: min(1pt, 3pt);
  min: min(1cm, 3mm);
  max: max(1, 3);
  max: max(3em, 1em, 2em, 5em);
  percentage: percentage((10px / 50));
  color-quoted-digit: color("#dda0dd");
  color-quoted-keyword: color("plum");
  color-color: color(#dda0dd);
  color-keyword: color(plum);
  tint: tint(#777777, 13);
  tint-full: tint(#777777, 100);
  tint-percent: tint(#777777, 13%);
  tint-negative: tint(#777777, -13%);
  shade: shade(#777777, 13);
  shade-full: shade(#777777, 100);
  shade-percent: shade(#777777, 13%);
  shade-negative: shade(#777777, -13%);

  fade-out: fadeout(red, 5%); // support fadeOut and fadeout
  fade-in: fadein(fadeout(red, 10%), 5%);
  fade-out-relative: fadeout(red, 5%,relative); 
  fade-in-relative: fadein(fadeout(red, 10%, relative), 5%, relative);
  fade-out2:  fadeout(fadeout(red, 50%), 50%); 
  fade-out2-relative: fadeout(fadeout(red, 50%, relative), 50%, relative);
  
  hsv: hsv(5, 50%, 30%);
  hsva: hsva(3, 50%, 30%, 0.2);

  mix: mix(#ff0000, #ffff00, 80);
  mix-0: mix(#ff0000, #ffff00, 0);
  mix-100: mix(#ff0000, #ffff00, 100);
  mix-weightless: mix(#ff0000, #ffff00);
  mixt: mix(#ff0000, transparent);

  .is-a {
    @rules: {
      color: red;
    };
    ruleset: isruleset(@rules);
    color: iscolor(#ddd);
    color1: iscolor(red);
    color2: iscolor(rgb(0, 0, 0));
    color3: iscolor(transparent);
    keyword: iskeyword(hello);
    number: isnumber(32);
    string: isstring("hello");
    pixel: ispixel(32px);
    percent: ispercentage(32%);
    em: isem(32em);
    ex: isunit(32ex, ex);
    rem: isunit(32rem, rem);
    vw: isunit(32vw, vw);
    vh: isunit(32vh, vh);
    vmin: isunit(32vmin, vmin);
    vmax: isunit(32vmax, vmax);
    ch: isunit(32ch, ch);
    cm: isunit(32cm, cm);
    mm: isunit(32mm, mm);
    pt: isunit(32pt, pt);
    q: isunit(32q, q);
    in: isunit(32in, in);
    cat: isunit(32cat, cat);
    no-unit-is-empty: isunit(32, '');
    case-insensitive-1: isunit(32CAT, cat);
    case-insensitive-2: isunit(32px, PX);
  }
}

#alpha {
  alpha: darken(hsla(25, 50%, 50%, 0.6), 10%);
  alpha2: alpha(rgba(3, 4, 5, 0.5));
  alpha3: alpha(transparent);
}

#blendmodes {
  multiply: multiply(#f60000, #f60000);
  screen: screen(#f60000, #0000f6);
  overlay: overlay(#f60000, #0000f6);
  softlight: softlight(#f60000, #ffffff);
  hardlight: hardlight(#f60000, #0000f6);
  difference: difference(#f60000, #0000f6);
  exclusion: exclusion(#f60000, #0000f6);
  average: average(#f60000, #0000f6);
  negation: negation(#f60000, #313131);
}

#extract-and-length {
  @anon: A B C 1 2 3;
  extract: extract(@anon, 6) extract(@anon, 5) extract(@anon, 4) extract(@anon, 3) extract(@anon, 2) extract(@anon, 1);
  length: length(@anon);
}

#quoted-functions-in-mixin {
  // Quoted type may have some weird side-effects when used in mixins (#2308)
  .mixin();
  .mixin() {
    replace-double-quoted: replace('foo-1', "1", "2");
    replace-single-quoted: replace('foo-3', "3", "4");
    replace-escaped-string: replace(~"bar-1", "1", "2");
    replace-keyword: replace(baz-1, "1", "2");
    replace-anonymous: replace(e("qux-1"), "1", "2");
    format-double-quoted: %("hello %s", "world");
    format-single-quoted: %('hello %s', "single world");
    format-escaped-string: %(~"hello %s", "escaped world");
    format-keyword: %(hello);
    format-anonymous: %(e("hello %s"), "anonymous world");
  }
}

#list-details {
  @list:
    a 1, // Some comment
    b 2;

  length: length(@list);
  one: extract(@list, 1);
  @two: extract(@list, 2);
  two: @two;
  two-length: length(@two);
  two-one: extract(@two, 1);
  two-two: extract(@two, 2);
}
@color1: #FFF;/* comment1 */
@color2: #FFF/* comment2 */;
html {
  color: mix(blue, @color1, 50%);
  color: mix(blue, @color2, 50%);
}

#boolean {
  a: boolean(not(2 < 1));
  b: boolean(not(2 > 1) and (true));
  c: boolean(not(boolean(true)));
}

#if {
  a: if(not(false), 1, 2);
  b: if(not(true), 1, 2);
  @1: if(not(false), {c: 3}, {d: 4}); @1();

  e: if(not(true), 5);
  @f: boolean(3 = 4);
  f: if(not(@f), 6);
  g: if(true, 3, 5);
  h: if(false, 3, 5);
  i: if(true and isnumber(6), 6, 8);
  j: if(not(true) and true, 6, 8);
  k: if(true or true, 1);

  if((false), {g: 7}); /* results in void */

  @conditional: if((true), {
    color: green;
  }, {});
  @conditional();

  @falsey: if((false), {
    color: orange;
  }, {
    color: purple;
  });
  @falsey();
}
