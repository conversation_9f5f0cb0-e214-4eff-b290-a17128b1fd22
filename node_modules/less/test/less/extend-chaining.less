//very simple chaining
.a {
  color: black;
}
.b:extend(.a) {}
.c:extend(.b) {}

//very simple chaining, ordering not important

.d:extend(.e) {}
.e:extend(.f) {}
.f {
  color: black;
}

//extend with all

.g.h {
  color: black;
}
.i.j:extend(.g all) {
  color: white;
}
.k:extend(.i all) {}

//extend multi-chaining

.l {
  color: black;
}
.m:extend(.l){}
.n:extend(.m){}
.o:extend(.n){}
.p:extend(.o){}
.q:extend(.p){}
.r:extend(.q){}
.s:extend(.r){}
.t:extend(.s){}

// self referencing is ignored

.u {color: black;}
.v.u.v:extend(.u all){}

// circular reference because the new extend product will match the existing extend

.w:extend(.w) {color: black;}
.v.w.v:extend(.w all){}

// classic circular references

.x:extend(.z) {
  color: x;
}
.y:extend(.x) {
  color: y;
}
.z:extend(.y) {
  color: z;
}

//very simple chaining, but with the extend inside the ruleset
.va {
  color: black;
}
.vb {
  &:extend(.va);
  color: white;
}
.vc {
  &:extend(.vb);
}

// media queries - don't extend outside, do extend inside

@media tv {
  .ma:extend(.a,.b,.c,.d,.e,.f,.g,.h,.i,.j,.k,.l,.m,.n,.o,.p,.q,.r,.s,.t,.u,.v,.w,.x,.y,.z,.md) {
    color: black;
  }
  .md {
    color: white;
  }
  @media plasma {
    .me, .mf {
      &:extend(.mb,.md);
      background: red;
    }
  }
}
.mb:extend(.ma) {};
.mc:extend(.mb) {};