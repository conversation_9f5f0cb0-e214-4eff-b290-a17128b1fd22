.comma-delimited {
  text-shadow: -1px -1px 1px red, 6px 5px 5px yellow;
  -moz-box-shadow: 0pt 0pt 2px rgba(255, 255, 255, 0.4) inset,
    0pt 4px 6px rgba(255, 255, 255, 0.4) inset;
  -webkit-transform: rotate(-0.0000000001deg);
}
@font-face {
  font-family: Headline;
  unicode-range: U+??????, U+0???, U+0-7F, U+A5;
}
.other {
  -moz-transform: translate(0, 11em) rotate(-90deg);
  transform: rotateX(45deg);
}
.item[data-cra_zy-attr1b-ut3=bold] {
  font-weight: bold;
}
p:not([class*="lead"]) {
  color: black;
}

input[type="text"].class#id[attr=32]:not(1) {
  color: white;
}

div#id.class[a=1][b=2].class:not(1) {
  color: white;
}

ul.comma > li:not(:only-child)::after {
  color: white;
}

ol.comma > li:nth-last-child(2)::after {
  color: white;
}

li:nth-child(4n+1),
li:nth-child(-5n),
li:nth-child(-n+2) {
  color: white;
}

a[href^="http://"] {
  color: black;
}

a[href$="http://"] {
  color: black;
}

form[data-disabled] {
  color: black;
}

p::before {
  color: black;
}

#issue322 {
  -webkit-animation: anim2 7s infinite ease-in-out;
}

@-webkit-keyframes frames {
  0% { border: 1px }
  5.5% { border: 2px }
  100% { border: 3px }
}

@keyframes fontbulger1 {
  to {
    font-size: 15px;
  }
  from,to {
    font-size: 12px;
  }
  0%,100% {
    font-size: 12px;
  }
}

@supports ( box-shadow: 2px 2px 2px black ) or
          ( -moz-box-shadow: 2px 2px 2px black ) {
  .outline {
    box-shadow: 2px 2px 2px black;
    -moz-box-shadow: 2px 2px 2px black;
  }
}

@-x-document url-prefix(""github.com"") {
  h1 {
    color: red;
  }
}

@viewport {
  font-size: 10px;
}
@namespace foo url(http://www.example.com);

foo|h1 { color: blue; }
foo|* { color: yellow; }
|h1 { color: red; }
*|h1 { color: green; }
h1 { color: green; }
.upper-test {
  UpperCaseProperties: allowed;
}
@host {
  div {
    display: block;
  }
}
::distributed(input::placeholder) {
  color: #b3b3b3;
}
.shadow ^ .dom,
body ^^ .shadow {
  display: done;
}

:host(.sel .a),
:host-context(.sel .b),
.sel /deep/ .b,
::content .sel {
  type: shadow-dom;
}
/deep/ b {
 c: 'd';
 &[e]{
   f: 'g';
 }
}

#issue2066 {
  background: url('/images/icon-team.svg') 0 0 / contain;
}

@counter-style triangle {
  system: cyclic;
  symbols: ‣;
  suffix: " ";
}

@-ms-viewport{
  // width: auto !important;
}

@unknown foo 42 (bar) {
    x {y: z}    
}

@unknown foo 43;
