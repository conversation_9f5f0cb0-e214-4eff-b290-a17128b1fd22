
.light when (lightness(@a) > 50%) {
  color: green;
}
.dark when (lightness(@a) < 50%) {
  color: orange;
}
@a: #ddd;

.see-the {
  @a: #444; // this mirrors what mixins do - they evaluate the guards at the point of definition
  .light();
  .dark();
}

.hide-the {
  .light();
  .dark();
}

.multiple-conditions-1 when (@b = 1), (@c = 2), (@d = 3) {
  color: red;
}

.multiple-conditions-2 when (@b = 1), (@c = 2), (@d = 2) {
  color: blue;
}

@b: 2;
@c: 3;
@d: 3;

.inheritance when (@b = 2) {
  .test {
    color: black;
  }
  &:hover {
    color: pink;
  }
  .hideme when (@b = 1) {
    color: green;
  }
  & when (@b = 1) {
    hideme: green;
  }
}

.hideme when (@b = 1) {
  .test {
    color: black;
  }
  &:hover {
    color: pink;
  }
  .hideme when (@b = 1) {
    color: green;
  }
}

& when (@b = 1) {
  .hideme {
    color: red;
  }
}

.mixin-with-guard-inside(@colWidth) {
  // selector with guard (applies also to & when() ...)
  .clsWithGuard when (@colWidth <= 0) {
    dispaly: none;
  }
}

.mixin-with-guard-inside(0px);

.dont-split-me-up {
  width: 1px;
  & when (@c = 3) {
    color: red;
  }
  & when (@c = 3) {
    height: 1px;
  }
  + & when (@c = 3) { // creates invalid css but tests that we don't fold it in
    sibling: true;
  }
}

.scope-check when (@c = 3) {
  @k: 1px;
  & when (@c = 3) {
    @k: 2px;
    sub-prop: @k;
  }
  prop: @k;
}
.scope-check-2 {
  .scope-check();
  @k:4px;
}
.errors-if-called when (@c = never) {
  .mixin-doesnt-exist();
}
a:hover when (2 = true) {5:-}