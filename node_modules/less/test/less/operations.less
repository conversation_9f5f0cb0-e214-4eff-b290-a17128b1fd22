#operations {
  color: (#110000 + #000011 + #001100); // #111111
  color-2: (yellow - #070707);
  height: (10px / 2px + 6px - 1px * 2); // 9px
  width: (2 * 4 - 5em); // 3em
  .spacing {
    height: (10px / 2px+6px-1px*2);
    width: (2  * 4-5em);
  }
  subtraction: (20 - 10 - 5 - 5); // 0
  division: (20 / 5 / 4); // 1
}

@x: 4;
@y: 12em;

.with-variables {
  height: (@x + @y); // 16em
  width: (12 + @y); // 24em
  size: (5cm - @x); // 1cm
}

.with-functions {
  color: (rgb(200, 200, 200) / 2);
  color: (2 * hsl(0, 50%, 50%));
  color: (rgb(10, 10, 10) + hsl(0, 50%, 50%));
}

@z: -2;

.negative {
  height: (2px + @z); // 0px
  width: (2px - @z); // 4px
}

.shorthands {
  padding: -1px 2px 0 -4px; //
}

.rem-dimensions {
  font-size: (20rem / 5 + 1.5rem); // 5.5rem
}

.colors {
  color: #123; // #112233
  border-color: (#234 + #111111); // #334455
  background-color: (#222222 - #fff); // #000000
  .other {
    color: (2 * #111); // #222222
    border-color: (#333333 / 3 + #111); // #222222
  }
}

.negations {
    @var: 4px;
    variable: (-@var); // 4
    variable1: (-@var + @var); // 0
    variable2: (@var + -@var); // 0
    variable3: (@var - -@var); // 8
    variable4: (-@var - -@var); // 0
    paren: (-(@var)); // -4px
    paren2: (-(2 + 2) * -@var); // 16
}
