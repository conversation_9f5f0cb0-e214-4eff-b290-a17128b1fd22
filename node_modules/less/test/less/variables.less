@a: 2;
@x: (@a * @a);
@y: (@x + 1);
@z: (@x * 2 + @y);
@var: -1;

.variables {
  width: (@z + 1cm); // 14cm
}

.variable-dash {
    @jumbotron-padding: 30px;

    .q {
        padding: @jumbotron-padding (@jumbotron-padding/2);
    }
}

@b: @a * 10;
@c: #888;

@fonts: "Trebuchet MS", Verdana, sans-serif;
@f: @fonts;

@quotes: "~" "~";
@q: @quotes;
@onePixel: 1px;

.variables {
  height: (@b + @x + 0px); // 24px
  color: @c;
  font-family: @f;
  quotes: @q;
}

.redef {
    @var: 0;
    .inition {
        @var: 4;
        @var: 2;
        three: @var;
        @var: 3;
    }
    zero: @var;
}

@important-var: @c !important;
@important-var-two: @a !important;
.values {
    minus-one: @var;
    @a: 'Trebuchet';
    @multi: 'A', B, C;
    font-family: @a, @a, @a;
    color: @c !important;
    same-color: @important-var;
    same-again: @important-var !important;
    multi-important: @important-var @important-var, @important-var-two;
    multi: something @multi, @a;
}

.variable-names {
    .quoted {
        @var: 'hello';
        @name: 'var';
        name: @@name;
    }

    .unquoted {
        @var: 'hello';
        @name: var;
        name: @@name;
    }
    
    .color-keyword {
        @red: 'hello';
        @name: red;
        name: @@name;
    }
}

.alpha {
    @var: 42;
    filter: alpha(opacity=@var);
}

.polluteMixin() {
    @a: 'pollution';
}
.testPollution {
    @a: 'no-pollution';
    a: @a;
    .polluteMixin();
    a: @a;
}

.units {
  width: @onePixel;
  same-unit-as-previously: (@onePixel / @onePixel);
  square-pixel-divided: (@onePixel * @onePixel / @onePixel);
  odd-unit: unit((@onePixel * 4em / 2cm));
  percentage: (10 * 50%);
  pixels: (50px * 10);
  conversion-metric-a: (20mm + 1cm);
  conversion-metric-b: (1cm + 20mm);
  conversion-imperial: (1in + 72pt + 6pc);
  custom-unit: (42octocats * 10);
  custom-unit-cancelling: (8cats * 9dogs / 4cats);
  mix-units: (1px + 1em);
  invalid-units: (1px * 1px);
  .fallback {
    @px: 14px;
    @em: 1.4em;
    @cm: 10cm;
    div-px-1: (@px / @em);
    div-px-2: ((@px / @em) / @cm);
    sub-px-1: (@px - @em);
    sub-cm-1: (@cm - (@px - @em));
    mul-px-1: (@px * @em);
    mul-em-1: (@em * @px);
    mul-em-2: ((@em * @px) * @cm);
    mul-cm-1: (@cm * (@em * @px));
    add-px-1: (@px + @em);
    add-px-2: ((@px + @em) + @cm);
    mul-px-2: ((1 * @px) * @cm);
    mul-px-3: ((@px * 1) * @cm);
  }
}
