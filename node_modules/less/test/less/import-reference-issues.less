// Tests following past issues:
// * #1851 - Extend within (reference) imported files 
// * #1896 - Namespace imported less code does not get properly referenced, when there's a (reference) keyword.
// * #1878 - extend inside referenced file should not extend outside selectors
// * #2716 - A file imported by reference and then normally - multiple imports should be independent
// * #1968 - When using an @import (reference), mixins that contain an & selector get added to the compiled output improperly
// * #2162 - Cannot put import by reference inside of a mixin (also doubles #1896)

// #1878: extend inside referenced file should not extend outside selectors
@import (reference) "import-reference-issues/global-scope-import.less";
.theOnlySelector {
  shall-have: one selector;
}

// #2716: A file imported by reference and then normally - multiple imports should be independent
// #1878: - double nested version
#do-not-show-import {
  @import (reference, multiple) "import-reference-issues/multiple-import.less";
}

show-all-content {
  @import (multiple) "import-reference-issues/multiple-import.less";
}
// #1896: Namespace imported less code does not get properly referenced, when there's a (reference) keyword.
#Namespace {
  @import (less, reference) "import-reference-issues/simple-mixin.css";
}
#used-namespaced-mixin {
  #Namespace > .mixin();
  shall-see: another property above;
}

// #1851: Extend within (reference) imported files 
// test-b is in global-scope-import.less file
.test-c {
  &:extend(.test-b all);
}

// #1968: When using an @import (reference), mixins that contain an & selector get added to the compiled output improperly
@import "import-reference-issues/mixin-1968.less";
@import (reference) "import-reference-issues/appender-reference-1968.less";

// #2162 - Cannot put import by reference inside of a mixin (also doubles #1896)
.mixin-with-import-by-reference-inside() {
  the-only-property: nothing-below-this;
  @import (reference) "import-reference-issues/simple-ruleset-2162.less";
}
call-mixin-with-import-by-reference-inside {
  .mixin-with-import-by-reference-inside();
}
