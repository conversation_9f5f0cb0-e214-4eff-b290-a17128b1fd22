.sidebar {
  width: 300px;
  background: red;

  .box {
    background: #FFF;
    border: 1px solid #000;
    margin: 10px 0;
  }
}

.sidebar2 {
  &:extend(.sidebar all);
  background: blue;
}

.type1 {
    .sidebar3 {
      &:extend(.sidebar all);
      background: green;
    }
}

.type2 {
  &.sidebar4 {
    &:extend(.sidebar all);
    background: red;
  }
}

.button {
  color: black;
  &:hover {
    color: white;
  }
}
.submit {
  &:extend(.button);
  &:hover:extend(.button:hover) {}
}

.nomatch {
  &:hover:extend(.button :hover) {}
}

.button2 {
  :hover {
    nested: white;
  }
}
.button2 :hover {
  notnested: black;
}

.nomatch :extend(.button2:hover) {}

.amp-test-a,
.amp-test-b {
  .amp-test-c &.amp-test-d&.amp-test-e {
    .amp-test-f&+&.amp-test-g:extend(.amp-test-h) {}
  }
}
.amp-test-h {
  test: extended by masses of selectors;
}