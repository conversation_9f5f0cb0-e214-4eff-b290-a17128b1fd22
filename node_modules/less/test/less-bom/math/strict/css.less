﻿@charset "utf-8";
div { color: black; }
div { width: 99%; }

* {
  min-width: 45em;
}

h1, h2 > a > p, h3 {
  color: none;
}

div.class {
  color: blue;
}

div#id {
  color: green;
}

.class#id {
  color: purple;
}

.one.two.three {
  color: grey;
}

@media print {
  * {
    font-size: 3em;
  }
}

@media screen {
  * {
    font-size: 10px;
  }
}

@font-face {
  font-family: 'Garamond Pro';
}

a:hover, a:link {
  color: #999;
}

p, p:first-child {
  text-transform: none;
}

q:lang(no) {
  quotes: none;
}

p + h1 {
  font-size: +2.2em;
}

#shorthands {
  border: 1px solid #000;
  font: 12px/16px Arial;
  font: 100%/16px Arial;
  margin: 1px 0;
  padding: 0 auto;
}

#more-shorthands {
  margin: 0;
  padding: 1px 0 2px 0;
  font: normal small/20px 'Trebuchet MS', Verdana, sans-serif;
  font: 0/0 a;
  border-radius: 5px / 10px;
}

.misc {
  -moz-border-radius: 2px;
  display: -moz-inline-stack;
  width: .1em;
  background-color: #009998;
  background: -webkit-gradient(linear, left top, left bottom, from(red), to(blue));
  margin: ;
  .nested-multiple {
    multiple-semi-colons: yes;;;;;;
  };
  filter: alpha(opacity=100);
  width: auto\9;
}

#important {
  color: red !important;
  width: 100%!important;
  height: 20px ! important;
}

.def-font(@name) {
    @font-face {
        font-family: @name
    }
}

.def-font(font-a);
.def-font(font-b);

.æøå {
  margin: 0;
}
