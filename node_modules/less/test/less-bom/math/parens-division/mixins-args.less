﻿.mixin (@a: 1px, @b: 50%) {
  width: (@a * 5);
  height: (@b - 1%);
  depth: @b - 1%;
}

.mixina (@style, @width, @color: black) {
    border: @width @style @color;
}

.mixiny
(@a: 0, @b: 0) {
  margin: @a;
  padding: @b;
}

.hidden() {
  color: transparent; // asd
}

#hidden {
  .hidden;
}

#hidden1 {
  .hidden();
}

.two-args {
  color: blue;
  .mixin(2px, 100%);
  .mixina(dotted, 2px);
}

.one-arg {
  .mixin(3px);
}

.no-parens {
  .mixin;
}

.no-args {
  .mixin();
}

.var-args {
  @var: 9;
  .mixin(@var, (@var * 2) / 2);
}

.multi-mix {
  .mixin(2px, 30%);
  .mixiny(4, 5);
}

.maxa(@arg1: 10, @arg2: #f00) {
  padding: (@arg1 * 2px);
  color: @arg2;
}

body {
  .maxa(15);
}

@glob: 5;
.global-mixin(@a:2) {
  width: (@glob + @a);
}

.scope-mix {
  .global-mixin(3);
}

.nested-ruleset (@width: 200px) {
    width: @width;
    .column { margin: @width; }
}
.content {
    .nested-ruleset(600px);
}

//

.same-var-name2(@radius) {
  radius: @radius;
}
.same-var-name(@radius) {
  .same-var-name2(@radius);
}
#same-var-name {
  .same-var-name(5px);
}

//

.var-inside () {
    @var: 10px;
    width: @var;
}
#var-inside { .var-inside; }

.mixin-arguments (@width: 0px, ...) {
    border: @arguments;
    width: @width;
}

.arguments {
    .mixin-arguments(1px, solid, black);
}
.arguments2 {
    .mixin-arguments();
}
.arguments3 {
    .mixin-arguments;
}

.mixin-arguments2 (@width, @rest...) {
    border: @arguments;
    rest: @rest;
    width: @width;
}
.arguments4 {
    .mixin-arguments2(0, 1, 2, 3, 4);
}

// Edge cases

.edge-case {
    .mixin-arguments("{");
}

// Division vs. Literal Slash
.border-radius(@r: 2px/5px) {
  border-radius: @r;
}
.slash-vs-math {
  .border-radius();
  .border-radius(5px/10px);
  .border-radius((3px * 2));
}
// semi-colon vs comma for delimiting

.mixin-takes-one(@a) {
    one: @a;
}

.mixin-takes-two(@a; @b) {
    one: @a;
    two: @b;
}

.comma-vs-semi-colon {
    .mixin-takes-two(@a : a; @b : b, c);
    .mixin-takes-two(@a : d, e; @b : f);
    .mixin-takes-one(@a: g);
    .mixin-takes-one(@a : h;);
    .mixin-takes-one(i);
    .mixin-takes-one(j;);
    .mixin-takes-two(k, l);
    .mixin-takes-one(m, n;);
    .mixin-takes-two(o, p; q);
    .mixin-takes-two(r, s; t;);
}

.mixin-conflict(@a:defA, @b:defB, @c:defC) {
  three: @a, @b, @c;
}

.mixin-conflict(@a:defA, @b:defB, @c:defC, @d:defD) {
  four: @a, @b, @c, @d;
}

#named-conflict {
  .mixin-conflict(11, 12, 13, @a:a);
  .mixin-conflict(@a:a, 21, 22, 23);
}
@a: 3px;
.mixin-default-arg(@a: 1px, @b: @a, @c: @b) {
  defaults: 1px 1px 1px;
  defaults: 2px 2px 2px;
}

.test-mixin-default-arg {
  .mixin-default-arg();
  .mixin-default-arg(2px);
}

.mixin-comma-default1(@color; @padding; @margin: 2, 2, 2, 2) {
  margin: @margin;
}
.selector {
  .mixin-comma-default1(#33acfe; 4);
}
.mixin-comma-default2(@margin: 2, 2, 2, 2;) {
  margin: @margin;
}
.selector2 {
  .mixin-comma-default2();
}
.mixin-comma-default3(@margin: 2, 2, 2, 2) {
  margin: @margin;
}
.selector3 {
  .mixin-comma-default3(4,2,2,2);
}

.test-calling-one-arg-mixin(@a) {
}

.test-calling-one-arg-mixin(@a, @b, @rest...) {
}

div {
  .test-calling-one-arg-mixin(1);
}

mixins-args-expand-op- {
  @x: 1, 2, 3;
  @y: 4  5  6;

  &1 {.m3(@x...)}
  &2 {.m3(@y...)}
  &3 {.wr(a, b, c)}
  &4 {.wr(a; b; c, d)}
  &5 {.wr(@x...)}
  &6 {.m4(0; @x...)}
  &7 {.m4(@x..., @a: 0)}
  &8 {.m4(@b: 1.5; @x...)}
  &9 {.aa(@y, @x..., and again, @y...)}

  .m3(@a, @b, @c) {
    m3: @a, @b, @c;
  }

  .m4(@a, @b, @c, @d) {
    m4: @a, @b, @c, @d;
  }

  .wr(@a...) {
    &a {.m3(@a...)}
    &b {.m4(0, @a...)}
    &c {.m4(@a..., 4)}
  }

  .aa(@a...) {
    aa: @a;
    a4: extract(@a, 5);
    a8: extract(@a, 8);
  }
}
#test-mixin-matching-when-default-2645 {
  .mixin(@height) {
    height: @height;
  }

  .mixin(@width, @height: 10px) {
    width: @width;

    .mixin(@height: @height);
  }

  .mixin(@height: 20px);
}