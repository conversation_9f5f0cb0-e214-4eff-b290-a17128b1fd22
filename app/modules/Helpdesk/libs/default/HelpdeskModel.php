<?php
/**
 * Use this class if you need to implement module-wide processing and/or methods 
 * common for all module models (or at least for that which extends this class).
 * 
 * Either preload HelpdeskController() class in Helpdesk::init() or
 * use following pattern to create new module model:
 *
 *      App::loadLib('Helpdesk', 'HelpdeskModel');
 *
 *      class Post extends HelpdeskModel {
 *  
 *      }
 */
class HelpdeskModel extends Model {
    
//    protected $table = 'run_helpdesk_model';
//    
//    protected $schema = array(
//        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
//        'name' => array('type' => 'varchar', 'length' => 100),
//        'created' => array('type' => 'datetime', 'default' => null),
//        'modified' => array('type' => 'datetime', 'default' => null),
//    );
//    
//    public function __construct(){
//        parent::__construct();
//        // set validations
//        $this->validations = array(                                 
//            'my_field' => array(
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Field is mandatory'),
//                    'on' => 'create',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Field is mandatory'),
//                ),
//            ),            
//        );
//    }

} 