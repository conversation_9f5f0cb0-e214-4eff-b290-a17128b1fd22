<?php
/**
 * Use this class if you need to implement module-wide processing common 
 * for all module controllers (or at least for that which extends this class).
 * Mostly you will use __construct() method for this purpose
 * 
 * Either preload HelpdeskController() class in Helpdesk::init() or 
 * use following pattern to create new module controller:
 *
 *      App::loadLib('Helpdesk', 'HelpdeskController');
 *
 *      class Posts extends HelpdeskController {
 *  
 *      }
 */
class HelpdeskController extends SmartController {
    
//    public function __construct(){
//        parent::__construct();
//        // ...
//    }

} 
