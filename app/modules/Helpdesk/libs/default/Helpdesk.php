<?php
/**
 * Load this class in App::init and launch Helpdesk::init() there.
 */
class Helpdesk { 
    
    /**
     * Was Helpdesk already initalized by Helpdesk::init()?
     * 
     * @var bool 
     */
    static protected $initialized = false;
    
    /**
     * Initialize Helpdesk module
     */
    static public function init() {
        // check for duplicit initialization of Helpdesk
        if (self::$initialized) {
            return;
        }
        
        // ensure that the default module config is loaded as the first one
        App::getConfig('Helpdesk');
        
        App::loadLib('Helpdesk', 'default/HelpdeskModel');
        
        // install Helpdesk if not installed yet
        if (!IS_INSTALLED) {
            self::install();
        }
        
        App::loadLib('Helpdesk', 'default/HelpdeskController');
        App::setCssFiles('Helpdesk', array(
            '/main.css',
        ));
        App::addLastCssFiles(array(
            '/app/modules/Helpdesk/css/main.css'
        ));
        if (App::$actionType === 'admin') {
            App::setCssFiles('Helpdesk', array(
                '/admin.css',
            ));
            App::addLastCssFiles(array(
                '/app/modules/Helpdesk/css/admin.css'
            ));
        }

        // keep track that the initialization has been made already
        self::$initialized = true;
    }
    
    /**
     * Installs the Helpdesk, means this method does some processing
     * which sould be done on the very first launch of Helpdesk
     */
    static protected function install() {
        if (IS_INSTALLED) {
            return;
        }
        // ...
    }
    
//    /**
//     * 
//     */
//    static public function login() {
//        
//    }

//    /**
//     * 
//     */
//    static public function logout() {
//        
//    }
    
    /**
     * Do shutdown logic of Helpdesk module
     */
    static public function shutdown() {
        
    }
}
