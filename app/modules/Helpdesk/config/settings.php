<?php
/**
 * Module settings initialization. Use following array structure:
 * 
 *      array(
 *          '{pid}' => array(
 *              'value' => '{settingValue}', 
 *              'label' => '{settingLabel}', 
 *              'description' => '{settingDescription}', 
 *              'js_visible' => '{settingJsVisible}',
 *          )
 *      )
 * 
 * Use '/mvc/Core/Tools/updateSettings/{Module}' to load settings into DB
 */ 
$settings = array(
    'HelpdeskTicket.newTicketEmail.from' => array(
        'value' => '<EMAIL>'
    ),
    'HelpdeskTicket.newTicketEmail.subject' => array(
        'value' => 'Nový tiket č. :number:'
    ),
    'HelpdeskTicket.newTicketEmail.body' => array(
        'value' => 'Dobrý deň,<br />
<br />
vaša požiadavka č. :number: bola zaregistrovaná a budeme vás kontaktovať hneď ako to bude možné.<br />
Pri akejko<PERSON><PERSON> komunikácii ohľadom vašej požiadavky použite prosím číslo požiadavky ako referenciu.<br />
<br />
Prajeme Vám príjemný deň.<br />
<br />
Váše :siteName:'
    ),    
);
