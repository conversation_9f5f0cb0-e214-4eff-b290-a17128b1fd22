<?php
class HelpdeskTickets extends HelpdeskController {
    
    protected $model = 'HelpdeskTicket';
    
    /**
     * Allow the model methods hinting in IDE
     * @var HelpdeskTicket
     */
    protected $Model;

    public function submit() {
        $this->displayOriginComment = true;
        $Ticket = $this->loadModel('HelpdeskTicket', true);
        if ($this->data) {
            $result = $Ticket->submit($this->data);
            if ($result) {
                App::setSuccessMessage(__(__FILE__, 'Nová požiadavka bola ú<PERSON>š<PERSON> odoslaná'));
                if (!$Ticket->sendNewTicketEmail($result['id'])) {
                    App::setSuccessMessage(__(__FILE__, 'Odoslanie notifikačného e-mailu zlyhalo'));
                }
                App::redirect(App::getRefererUrl('/'));
            }
            elseif ($result === false) {
                App::setErrorMessage(__(__FILE__, 'Opravte prosím chyby'));
            }
            else {
                App::setErrorMessage(__(__FILE__, 'Odoslanie novej požiadavky zlyhalo. Skúste prosím neskôr.'));
            }
        }
        return $this->loadView('HelpdeskTickets/form', array(
            'data' => $this->data,
            'errors' => $Ticket->getErrors(),
            'required' => $Ticket->getNotEmptyFields(array(
                'alternative' => 'submit',
                'data' => $this->data,
            )),
        ));
    }
    
////version without SmartController    
//
//    public function admin_index() {
//        $Ticket = $this->loadModel('HelpdeskTicket', true);
//        $tickets = $Ticket->find(array(
//            'order' => 'id DESC',
//            'paginate' => true,
//        ));
//        App::setSeoTitle(__a(__FILE__, 'Požiadavky'));
//        return Html::smartIndex(array(
//            'title' => __a(__FILE__, 'Požiadavky'),
//            'records' => $tickets,
//            'Paginator' => $Ticket->Paginator,
//            'columns' => array(
//                'id' => __a(__FILE__, 'Číslo'),
//                'name' => __a(__FILE__, 'Odosielateľ'),
//                'email' => __a(__FILE__, 'E-mail'),
//                'created' => __a(__FILE__, 'Čas odoslania'),
//            ),
//            'renderFields' => array(
//                'id' => function($value) {
//                    // get some nice ticket number
//                    return str_pad($value, 5, 0, STR_PAD_LEFT);
//                }
//            ),
//            'actions' => array(
//                'add' => array(
//                    'url' => '/mvc/Helpdesk/HelpdeskTickets/admin_add',
//                ),
//            ),
//            'recordActions' => array(
//                'edit' => array(
//                    'url' => '/mvc/Helpdesk/HelpdeskTickets/admin_edit',
//                ),
//                'delete' => array(
//                    'url' => '/mvc/Helpdesk/HelpdeskTickets/admin_delete',
//                ),
//            ),
//        ));
//    }
//    
    public function admin_index() {
        $this->viewOptions['columns'] = array(
            'id' => __a(__FILE__, 'Číslo'),
            'name' => __a(__FILE__, 'Odosielateľ'),
            'email' => __a(__FILE__, 'E-mail'),
            'created' => __a(__FILE__, 'Čas odoslania'),
        );
        $this->viewOptions['renderFields'] = array(
            'id' => function($value) {
                // get some nice ticket number
                return str_pad($value, 5, 0, STR_PAD_LEFT);
            }
        );
        $this->viewOptions['title'] = __a(__FILE__, 'Požiadavky');
        $this->seoTitle = __a(__FILE__, 'Požiadavky');
        return parent::admin_index();
    }
    
////version without SmartController    
//    
//    public function admin_add() {
//        $Ticket = $this->loadModel('HelpdeskTicket', true);
//        // save submitted data
//        if ($this->data) {
//            if ($Ticket->save($this->data)) {
//                App::setSuccessMessage(__a(__FILE__, 'Nová požiadavka bola úspešne vytvorená'));
//                App::redirect(App::getUrl(array(
//                    'module' => $this->module,
//                    'controller' => $this->name,
//                    'action' => 'admin_edit',
//                    'args' => array($Ticket->getPropertyId()),
//                    'source' => App::$requestSource,
//                )));
//            }
//            App::setErrorMessage(__a(__FILE__, 'Opravte prosím chyby'));
//        }
//        // display form
//        App::setSeoTitle(__a(__FILE__, 'Nová požiadavka'));
//        return $this->loadView('HelpdeskTickets/admin_form', array(
//            'title' => __a(__FILE__, 'Nová požiadavka'),
//            'data' => $this->data,
//            'Model' => $Ticket,
//        ));
//    }
//    
    public function admin_add() {
        $this->viewOptions['title'] = __a(__FILE__, 'Nová požiadavka');
        $this->view = 'HelpdeskTickets/admin_form';
        $this->seoTitle = __a(__FILE__, 'Nová požiadavka');
        return parent::admin_add();
    }
    
////version without SmartController    
//    
//    public function admin_edit($id = null) {
//        if (!$id) {
//            App::setErrorMessage(__a(__FILE__, 'Chýbajúce id požiadavky'));
//            App::redirect(App::getUrl(array(
//                'locator' => '/_error',
//                'source' => App::$requestSource,
//            )));
//        }
//        $Ticket = $this->loadModel('HelpdeskTicket', true);
//        // save submitted data
//        if ($this->data) {
//            if ($Ticket->save($this->data, array(
//                'alternative' => array('backend')
//            ))) {
//                App::setSuccessMessage(__a(__FILE__, 'Požiadavka bola úspešne aktualizovaná'));
//                App::redirect(App::$url);
//            }
//            App::setErrorMessage(__a(__FILE__, 'Opravte prosím chyby'));
//            
//        }
//        // load data on initial request
//        else {
//            $this->data = $Ticket->findFirstBy('id', $id);
//        }
//        // get some nice ticket number
//        $number = str_pad($id, 5, 0, STR_PAD_LEFT);
//        // display form
//        App::setSeoTitle(__a(__FILE__, '"%s"', $number));
//        return $this->loadView('HelpdeskTickets/admin_form', array(
//            'title' => __a(__FILE__, 'Upraviť požiadavku č. "%s"', $number),
//            'data' => $this->data,
//            'Model' => $Ticket,
//        ));
//    }
//    
    public function admin_edit($id = null) {
        // get some nice ticket number
        $number = str_pad($id, 5, 0, STR_PAD_LEFT);
        $this->viewOptions['title'] = __a(__FILE__, 'Upraviť požiadavku č. "%s"', $number);
        $this->view = 'HelpdeskTickets/admin_form';
        $this->seoTitle = __a(__FILE__, '"%s"', $number);
        return parent::admin_edit($id);
    }
    
////version without SmartController    
//    
//    public function admin_delete($id = null) {
//        if (!$id) {
//            App::setErrorMessage(__a(__FILE__, 'Chýbajúce id požiadavky'));
//            App::redirect(App::getRefererUrl(App::getUrl(array(
//                'locator' => '/_error',
//                'source' => App::$requestSource,
//            ))));
//        }
//        $Ticket = $this->loadModel('HelpdeskTicket', true);
//        // delete
//        $Ticket->deleteBy('id', $id);
//        App::setSuccessMessage(__a(__FILE__, 'Požiadavka bola úspešne zmazaná'));
//        App::redirect(App::getRefererUrl(App::getUrl(array(
//            'locator' => '/',
//            'source' => App::$requestSource,
//        ))));
//    }
}
