<?php
class HelpdeskSettings extends HelpdeskController {

    protected $model = 'HelpdeskSetting';
    
    /**
     * Allow the model methods hinting in IDE
     * @var HelpdeskSetting
     */
    protected $Model;
    
////version without SmartController  
//    
//    public function admin_edit() {   
//        $this->displayOriginComment = true;
//        $Setting = $this->loadModel('HelpdeskSetting', true);
//        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
//        if (!empty($this->data)) {
//            if (
//                $Setting->updateByName($this->data, array(
//                    'lang' => $lang,
//                ))
//            ) {
//                App::setSuccessMessage(__a(__FILE__, 'Nastavenia boli úspešne aktualizované'));
//                App::redirect(App::$url);
//            }
//            App::setErrorMessage(__a(__FILE__, 'Opravte prosím chyby'));
//        }
//        else {
//            $this->data = $Setting->findListWithNameKeys(array(
//                'lang' => $lang,
//            ));
//        }
//        App::setSeoTitle(__a(__FILE__, 'Nastavenia modulu Helpdesk'));
//        return Html::smartForm(array(
//            'title' => __a(__FILE__, 'Nastavenia Helpdesku'),
//            'data' => $this->data,
//            'Model' => $Setting,
//            'columns' => 4,
//            'actions' => array(
//                'lang' => true
//            ),
//            'fields' => array(
//                array('h1' => __a(__FILE__, 'Základné nastavenia')),
//                array('h2' => __a(__FILE__, 'Notifikačný e-mail nového tiketu')),
//                array('row', 'columns' => 2),
//                    'Helpdesk/HelpdeskTicket/newTicketEmail/subject' => array(
//                        'label' => __a(__FILE__, 'Predmet'),
//                    ),
//                    'Helpdesk/HelpdeskTicket/newTicketEmail/from' => array(
//                        'label' => __a(__FILE__, 'Odosielateľ'),
//                        'hint' => __a(__FILE__, 'Na túto adresu sa tiež odošle kópia e-mailu')
//                    ),
//                array('/row'),
//                array('row', 'columns' => 1),
//                    'Helpdesk/HelpdeskTicket/newTicketEmail/body' => array(
//                        'label' => __a(__FILE__, 'Text'),
//                        'type' => 'editor',
//                        'options' => array(
//                            'toolbar' => 'Email'
//                        )
//                    ),
//                array('/row'),
//            )
//        ));
//    }
//    
    public function admin_edit() {   
        if ($this->data) {
            $this->saveResult = $this->Model->updateByName($this->data, array(
                'lang' => $this->lang,
            ));
        }
        else {
            $this->viewOptions['data'] = $this->Model->findListWithNameKeys(array(
                'lang' => $this->lang,
            ));
        }
        $this->viewOptions['fields'] = array(
            array('h1' => __a(__FILE__, 'Základné nastavenia')),
            array('h2' => __a(__FILE__, 'Notifikačný e-mail nového tiketu')),
            array('row', 'columns' => 2),
                'Helpdesk/HelpdeskTicket/newTicketEmail/subject' => array(
                    'label' => __a(__FILE__, 'Predmet'),
                ),
                'Helpdesk/HelpdeskTicket/newTicketEmail/from' => array(
                    'label' => __a(__FILE__, 'Odosielateľ'),
                    'hint' => __a(__FILE__, 'Na túto adresu sa tiež odošle kópia e-mailu')
                ),
            array('/row'),
            array('row', 'columns' => 1),
                'Helpdesk/HelpdeskTicket/newTicketEmail/body' => array(
                    'label' => __a(__FILE__, 'Text'),
                    'type' => 'editor',
                    'options' => array(
                        'toolbar' => 'Email'
                    )
                ),
            array('/row'),
        );
        $this->viewOptions['title'] = __a(__FILE__, 'Nastavenia Helpdesku');
        $this->seoTitle = __a(__FILE__, 'Nastavenia modulu Helpdesk');
        return parent::admin_edit();        
    }
}
