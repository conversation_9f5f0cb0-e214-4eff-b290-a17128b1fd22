// Bootstrap core variables and mixins
@import "../../../../css/vendors/less/bootstrap/variables.less";
@import "../../../../css/vendors/less/bootstrap/mixins.less";
// local variables
@import "../../../../css/less/libs/_variables.less";
@import "../../../../css/less/_mixins.less";

@img-dir: '/app/modules/Admin/img';

@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    src: local('Open Sans'), local('OpenSans'), url(../../../fonts/OpenSans.woff) format('woff');
}
@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 700;
    src: local('Open Sans Bold'), local('OpenSans-Bold'), url(../../../fonts/OpenSans-Bold.woff) format('woff');
}

body {
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
	//background: url('@{img-dir}/wallpapers/run_cms_03.jpg') repeat fixed 50% 50% transparent;
}
#login-form {
    position: relative;
    margin: 0 auto;
    margin-top: 125px;
    width: 310px;
    padding: 39px 0 40px 0;
    border: 1px solid #E5E5E5;
    border-radius: 6px;
    //box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    background-color: #F7F7F7; //rgba(200,200,200,0.5);
    .clearfix();
    .logo {
        display: block;
        margin: 0 auto;
        margin-bottom: 24px;
    }
    .center {
        width: 210px;
        margin: 0 auto;
        .clear;
    }
    label {
        font-weight: normal;
        margin: 0;
        //cursor: pointer;
    }
    .input-empty-text-wrapper {
        position: relative;
        .input-empty-text {
            position: absolute;
            top: 1px;
            left: 1px;
            padding: 6px 12px;
            color: #AAA;
        }
    }
    .text-input {
        margin-bottom: 5px;
        height: 46px;
    }
    .permanent-login {
        float: right;
        font-size: 13px;
        .toggle-input {
            position: relative;
            top: 2px;
            margin-left: 5px;
            //cursor: pointer;
        }
    }
    .btn {
        clear: right;
        float: right;
        width: 100%;
        margin-top: 9px;
        padding: 12px 12px;
        color: @color-white;
        //border-color: #AC2925;
        //background-color: @color-alert;
        border-color: darken(#AC2925, 5%);
        background-color: darken(@color-alert, 5%);
        &:hover {
            border-color: darken(#AC2925, 7%);
            background-color: darken(@color-alert, 7%);
        }
    }
    .app-login-messages {
        clear: right;
        padding: 12px 10px;
        margin-bottom: 5px;
    }
    
    // BACK LINK
    .run {         
        position: absolute;
        bottom: -18px;
        right: 0;
        a, .a {
            display: block;
            padding: 2px 1px;
            //border: 1px solid @color-black; //#DDE0E4;
            //border-left: none;
            //.border-right-radius(7px);
            font-size: 11px;
            color: #AAA;
            text-decoration: none;
            text-transform: lowercase;
            &:hover {
                color: #0176C7; //#258BF0; //#0176C7;
                //border-color: #258BF0; //#0176C7;
            }
        }
    }
}

