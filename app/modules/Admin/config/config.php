<?php 
/**
 * Module configs
 * 
 * To read these values use App::getConfig() function, e.g.:
 *
 *      App::getConfig('{module}'); // will return whole module $config array
 *      App::getConfig('{module}', 'fbApiId'); // will return value of module $config['fbApiId']
 *      App::getConfig('{module}', 'google.analytics'); // will return value of module $config['google']['analytics']
 * 
 * @var array
 */
$config = array(   
    /**
     * List of modules to be loaded in admin
     * 
     * @var arrays
     */
    'adminModules' => array(
        'admins' => array(
            'App' => array(
                'WebContentWindow',
                'UsersWindow',
                'ToolsWindow'
            ),
            'Admin' => array(
                'FileManagerWindow',
                'NotepadWindow'
            ),
            'Eshop' => array(
                'EshopMainWindow',
//                'EshopProductsWindow',
//                'EshopProductCategoriesWindow',
//                'EshopOrdersWindow',
//                'EshopManufacturersWindow'
            ),
            'Payment' => array(

            ),
                'Mailer' => array(
                    'MailerWindow'
                ),
//                'Reservations' => array(
//                    'ReservationsWindow'
//                )
        ),
        'webmasters' => array(
            'App' => array(
                'WebContentWindow',
                'UsersWindow'
            ),
            'Admin' => array(
                'FileManagerWindow',
                'NotepadWindow'
            ),
            'Eshop' => array(
                'EshopMainWindow',
//                'EshopProductsWindow',
//                'EshopProductCategoriesWindow',
//                'EshopOrdersWindow',
//                'EshopManufacturersWindow'
            ),
            'Payment' => array(

            ),
                'Mailer' => array(
                    'MailerWindow'
                ),
//                'Reservations' => array(
//                    'ReservationsWindow'
//                )
        )
    ),   
    /**
     * List of selectable layouts for choosing in CMS combo
     * 
     * as key use moduleName coma layoutName
     * as value use label
     * 
     * @var arrays
     */
    'selectableLayouts' => array(
        '' => __(__FILE__, 'Undefined layout'),
        'App.home' => __(__FILE__, 'Home page'),
        'App.default' => __(__FILE__, 'Text layout'),
        'App.simple' => __(__FILE__, 'Product layout')
    ),
); 
