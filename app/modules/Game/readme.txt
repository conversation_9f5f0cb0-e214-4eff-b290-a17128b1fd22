How to create new module: 

    1] copy the folder NewModule under app/modules and rename it

    2] rename these files by the name of module:
        - controllers/NewModuleSettings.php
        - libs/default/NewModuleController.php
        - libs/default/NewModuleModel.php
        - libs/default/NewModule.php
        - locale/NewModule*.po, 
        - models/NewModuleSetting.php

    3] inside of new module .php, .js and .less files replace all occurences of NewModule by the
        name of module and all occurences of new_module by underscored name of module

    4] initialize new module at the end of App::init() by following code replacing 
        NewModule by the real name of module:  

            // load NewModule module class and init NewModule module
            require_once ROOT . DS . 'app' . DS . 'modules' . DS . 'NewModule' . DS . 'libs' . DS . 'default' . DS . 'NewModule.php';
            NewModule::init();

        (Once the classes autoloading will be implemented this will be no more necessary)


    5] it's all :)

    RECOMMENDATION: All new controllers and models define as child classes of NewModuleController.php
    and NewModuleModel.php (this is the default in new created module)
 
