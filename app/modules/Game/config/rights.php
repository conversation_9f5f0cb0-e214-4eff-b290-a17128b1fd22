<?php
/**
 * Module rights definitions
 * 
 * Auxiliary variable $edit serves to optimize performance. Use it in 'admins' rights 
 * to define headers and labels in case when rights are loaded for editing. Only items
 * with defined labels are included in editing. Use pattern like:
 * 
 *      'admins' => array(
 *          array('h1' => $edit ? __a(__FILE__, 'My module rights') : true),
 *          array('h2' => $edit ? __a(__FILE__, 'My section rights') : true),
 *          'MyModel.admin_myAction1' => $edit ? __a(__FILE__, 'Do my action') : true,
 *          'MyModel.admin_myAction3' => true,                                                      // this will not be included in editing
 *          'MyModel.admin_myAction2' => $edit ? __a(__FILE__, 'Not important for admin') : false,  // editable but 'admins' does not need to see it in backend
 *          ...,
 * 
 *      );
 * 
 * Admins rights must contain all actions which should be editable. If you have a case of 
 * method which is a weak version of stronger one, and admin does not need to use this method
 * and see it on backend then just set it to FALSE (see the last item in example above).
 * In special cases you can define FALSE items also in rights for other groups, just test it
 * if default behaviour does not meet your needs.
 * 
 * When loading rights for editing set $edit = TRUE. Otherwise just don't care about it.
 * 
 * When copying rights from 'admins' to other groups use regex:
 * \$edit\s+\?\s+__a?\(__FILE__,\s*'[^']*'\)\s*:\s*
 * to remove labels. !!!ATTENTION: Headers and labels must not be defined in rights 
 * for other than admins group!!!
 */
$edit = !empty($edit);
$rights = array(
    'public' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            'GameNoviceToNinjas.load' => true,
        ),
        // these rights apply when the element is required by snippet
        'element' => array(
        ),
    ),
    'admins' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            array('h1' => $edit ? __a(__FILE__, 'Game rights') : true),
            //'GameSettings.admin_edit' => $edit ? __a(__FILE__, 'Settings') : true,
            
            //array('h2' => $edit ? __a(__FILE__, 'Items rights') : true),
            //'GameItems.admin_index' => $edit ? __a(__FILE__, 'Display items list') : true,
            //'GameItems.admin_showTree' => $edit ? __a(__FILE__, 'Display items tree') : true,
            //'GameItems.admin_showTreeLevel' => $edit ? __a(__FILE__, 'Display items tree level') : true,
            //'GameItems.admin_move' => $edit ? __a(__FILE__, 'Reorder items') : true,
            //'GameItems.admin_add' => $edit ? __a(__FILE__, 'Add item') : true,
            //'GameItems.admin_view' => $edit ? __a(__FILE__, 'View item') : true,
            //'GameItems.admin_edit' => $edit ? __a(__FILE__, 'Edit item') : true,
            //'GameItems.admin_delete' => $edit ? __a(__FILE__, 'Delete item') : true,
            //'GameItems.admin_deleteFile' => $edit ? __a(__FILE__, 'Delete item file') : true,
            //'GameItems.admin_export' => $edit ? __a(__FILE__, 'Export items') : true,
            //'GameItems.admin_myAction' => true,
        ),
        // these rights apply when the element is required by snippet
        'element' => array(
        ),
    ),
    'webmasters' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            //'GameSettings.admin_edit' => true,
        ),
        // these rights apply when the element is required by snippet
        'element' => array(
        ),
    ),
); 
