<?php
/**
 * Module settings initialization. Use following array structure:
 * 
 *      array(
 *          '{pid}' => array(
 *              'value' => '{settingValue}', 
 *              'label' => '{settingLabel}', 
 *              'description' => '{settingDescription}', 
 *              'js_visible' => '{settingJsVisible}',
 *          )
 *      )
 * 
 * Use '/mvc/Core/Tools/updateSettings/{Module}' to load settings into DB
 */ 
$settings = array(
//    'mySetting' => array(
//        'value' => '',
//    ),
//    //...
);
