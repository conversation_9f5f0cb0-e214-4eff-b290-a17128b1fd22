<?php 
/**
 * Module configs
 * 
 * To read these values use App::getConfig() function, e.g.:
 *
 *      App::getConfig('{module}'); // will return whole module $config array
 *      App::getConfig('{module}', 'fbApiId'); // will return value of module $config['fbApiId']
 *      App::getConfig('{module}', 'google.analytics'); // will return value of module $config['google']['analytics']
 * 
 * @var array
 */
$config = array(
    'adminIcons' => array(
        'Module' => '<i class="fa fa-circle"></i>',
        'MyControllerA' => '<i class="fa fa-circle"></i>',
        'MyControllerB' => '<i class="fa fa-circle"></i>',
        'GameSettings' => '<i class="fa fa-gears"></i>',
    ),   
); 
