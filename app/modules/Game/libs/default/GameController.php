<?php
/**
 * Use this class if you need to implement module-wide processing common 
 * for all module controllers (or at least for that which extends this class).
 * 
 * Class GameController() is preloaded in Game::init(), but if not for 
 * some reasons, then use following pattern to create new module controller:
 *
 *      App::loadLib('Game', 'GameController');
 *
 *      class Posts extends GameController {
 *  
 *      }
 */
class GameController extends SmartController {
    
////if needed use this in your implementation of child classes
//    protected $model = 'GameMyModel';
//    
//    /**
//     * Allow the model methods hinting in IDE
//     * @var GameMyModel
//     */
//    protected $Model;

} 
