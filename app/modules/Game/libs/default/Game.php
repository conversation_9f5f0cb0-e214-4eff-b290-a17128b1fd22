<?php
/**
 * Load this class in App::init and launch Game::init() there.
 */
class Game { 
    
    /**
     * Was Game already initalized by Game::init()?
     * 
     * @var bool 
     */
    static protected $initialized = false;
    
    /**
     * Initialize Game module
     */
    static public function init() {
        // check for duplicit initialization of Game
        if (self::$initialized) {
            return;
        }
        
        // ensure that the default module config is loaded as the first one
        App::getConfig('Game'); 
        
        App::loadLib('Game', 'default/GameModel');
        
        // install Game if not installed yet
        if (!IS_INSTALLED) {
            self::install();
        }
        
        App::loadLib('Game', 'default/GameController');
        if (App::$actionType === 'admin') {
            App::setCssFiles('Game', array(
                '/admin.css',
            ));
            App::addLastCssFiles(array(
                '/app/modules/Game/css/admin.css'
            ));
        }
        else {
            App::setCssFiles('Game', array(
                '/main.css',
            ));
            App::addLastCssFiles(array(
                '/app/modules/Game/css/main.css'
            ));
        }

        // keep track that the initialization has been made already
        self::$initialized = true;
    }
    
    /**
     * Installs the Game, means this method does some processing
     * which sould be done on the very first launch of Game
     */
    static protected function install() {
        if (IS_INSTALLED) {
            return;
        }
        // ...
    }
    
//    /**
//     * 
//     */
//    static public function login() {
//        
//    }

//    /**
//     * 
//     */
//    static public function logout() {
//        
//    }
    
    /**
     * Do shutdown logic of Game module
     */
    static public function shutdown() {
        
    }
}
