<?php
/**
 * Use this class if you need to implement module-wide processing and/or methods 
 * common for all module models (or at least for that which extends this class).
 * 
 * Class GameModel() is preloaded in Game::init(), but if not for some 
 * reasons, then use following pattern to create new module model:
 * 
 *      App::loadLib('Game', 'GameModel');
 *
 *      class Post extends GameModel {
 *  
 *      }
 */
class GameModel extends Model {
    
////if needed use this in your implementation of child classes
//    protected $table = 'run_game_this_model_items';
//    
//    protected $schema = array(
//        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
//        'run_game_foreign_model_items_id' => array('type' => 'int', 'index' => 'index'),
//        'name' => array('type' => 'varchar', 'length' => 100),
//        'description' => array('type' => 'text'),
//        'active' => array('type' => 'bool', 'default' => 0),
//        'created' => array('type' => 'datetime', 'default' => null),
//        'modified' => array('type' => 'datetime', 'default' => null),
//    );
//    
//    public function __construct(){
//        parent::__construct();
//        // set validations
//        $this->validations = array(                                 
//            'my_field' => array(
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Enter a value'),
//                    'on' => 'create',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Enter a value'),
//                ),
//            ),            
//        );
//    }
//    
//    public function normalize($data, $options = array()) {
//        $defauts = array(
//            'on' => null,
//            'alternative' => null,
//        );
//        $options = array_merge($defauts, $options);
//        
//        // do your custom normalization...
//        
//        return parent::normalize($data, $options);
//    }
    
} 