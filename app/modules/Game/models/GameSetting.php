<?php
App::loadModel('App', 'Setting');
class GameSetting extends Setting {
    
    public function __construct() {
        parent::__construct();
        
        // VALIDATIONS
        // 
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        // !!! ATTENTION: Setting names must be provided here with pathSeparator (/) instead of dots !!!
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        $this->validations = array(
//            'Game/mySetting' => array(
//                //...
//            ),
//            //...
        );
    }
        
    /**
     * Updates settings provided in list '{settingName}' => '{value}'. Setting name 
     * is created from setting  module name and pid, e.g. Game/mySetting.
     * 
     * @param array $data Settings data
     * @param array $options Model::update() options. Defaults to empty array().
     * 
     * @return boolean TRUE on success. FALSE on validation failure
     */
    public function updateByName($data, $options = array()) {
        if (!parent::updateByName($data, $options)) {
            return false;
        }
        
        // treat special cases
        //...  
        return true;
    }    
}
