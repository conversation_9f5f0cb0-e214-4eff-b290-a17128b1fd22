<?php /* @var $this Template */
$this->displayOriginComment = true;

?><div id="board"></div><?php

/*/
// if the bootstrap code is provided in module file (the worst solution)
App::setJsFiles(array(
    '/app/modules/Game/js/GameNoviceToNinja.js' => 'module',
));
//*/

// inline bootstrap for systemjs modules
App::setJsFiles(array(
    '/app/js/vendors/system.min.js',
));
$jsOptions = array(
    'selector' => '#board',
    'gameIndex' => (int)$this->params['gameIndex'],
);
App::startJsCapture();
?><script type="text/javascript">
    try {
        System.import('/app/modules/Game/js/GameNoviceToNinja.js').then(function(module){
            new module.GameNoviceToNinja(<?php echo json_encode($jsOptions) ?>);
        });
    }
    catch (error) {
        console.error(error);
    }
</script><?php
App::endJsCapture();

/*/
// inline bootstrap for native (es6) modules
App::startBodyEndHtmlCapture();
?><script type="module">
    try {
        import { GameNoviceToNinja } from '/app/modules/Game/js/GameNoviceToNinja.js';
        new GameNoviceToNinja({selector: '#board canvas'});
    }
    catch (error) {
        console.error(error);
    }
</script><?php
App::endBodyEndHtmlCapture();
//*/
