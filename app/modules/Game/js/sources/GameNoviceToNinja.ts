// ATTENTION: Use non-relative imports for external libraries. They are for the moment possible 
// only with SystemJS (see app/config/config.php > 'jsModuleLoader', 'jsModuleImportmap') 
// or with Typescript (see  tsconfig.ts > "paths")
//import _ from 'lodash';

 // ATTENTION: Use relative paths (starting by `/`, `./`, or `../`) to import other
 // js modules from app. There are two possibilities:
 // - Paths starting by `/` (it means they are relative to `urlBase`, tsconfig.json > "urlBase", 
 // the value "./" resolves to app root) must point to compiled js files (out of sources dir).
 // They are resolved by mappings in tsconfig.json > "paths". They can be used both
 // for imports of js modules from the same fajnwork module and also from other fajnwork module.
 // - Paths starting by `./` or `../` (it means they are relative to actual js file
 // the import is done in) must point to sources files (but these paths are the same for 
 // compiled files too, that is why it works). They can be used only for imports of
 // js modules from the same fajnwork module.
import { KeyControls } from './libs/controls/KeyControls';
import { MouseControls } from './libs/controls/MouseControls';
import { ContainerEntity } from './libs/ContainerEntity';
import { TextEntity } from './libs/TextEntity';
import { CanvasRenderer } from './libs/renderer/CanvasRenderer';
import { Texture } from './libs/Texture';
import { SpriteEntity } from './libs/SpriteEntity';
import { Game } from './libs/Game';
import math from './libs/utils/math';
import { TileSpriteEntity } from './libs/TileSpriteEntity';
import { Squizz } from './entities/02/Squizz';
    
/**
 * Class GameNoviceToNinja.GameNoviceToNinja
 */
export class GameNoviceToNinja {

    //
    // RUNTIME PROPERTIES
    //

    /**
     * Options
     */
    protected options: GameNoviceToNinjaOptions = null;

    /**
     * Main element
     */
    protected board: HTMLDivElement;
    
    //
    // STATIC PROPERTIES & CONSTANTS
    //

    /**
     * Options defaults
     * 
     * They are defined this way to make them accessible also in static method getInstance().
     * If you don't use getInstance() method or if any problem occurs (mostly in some very
     * complicated inheritance cases) then move defaults into constructor by replacing
     * `GameNoviceToNinja.DEFAULTS` in `this.options = jQuery.extend(...)`
     */
    protected static readonly DEFAULTS: GameNoviceToNinjaOptions = {
        selector: null,
        gameIndex: null,
    }

    /**
     * Constructor
     */
    constructor(options: GameNoviceToNinjaOptions = {}) {

        //
        // APPLY CUSTOM OPTIONS PROPERTIES
        // 
        // Default options are defined in static property GameNoviceToNinja.DEFAULTS here above
        //
        this.options = Object.assign({}, GameNoviceToNinja.DEFAULTS, options)

        //
        // LAUNCH INIT
        //

        // launch init() only in case that you are in instantianized class constructor 
        // and not in its parent/super class constructor (see https://stackoverflow.com/q/44109220/1245149)
        if (this.constructor === GameNoviceToNinja) {
            this.init();
        }            
    }

    //
    // METHODS
    //
    //...

    /**
     * Initialization method called in constructor
     */
    protected init(): void {   
        const _this = this; 
        let elementsList: any;

        // initialize class element(s)
        elementsList = document.querySelectorAll(this.options.selector);
        if (elementsList.length === 0) {
            throw new Error(__jse(
                'Game', 
                'No board found accoding to provided selector ":selector:"', 
                this.options
            ));
        }
        if (elementsList.length > 1) {
            throw new Error(__jse(
                'Game', 
                'Many boards have been found accoding to provided selector ":selector:"', 
                this.options
            ));
        }
        this.board = elementsList[0];
        
        let methodName = 'runGame';
        if (this.options.gameIndex < 10) {
            methodName += '0';
        }
        methodName += this.options.gameIndex;
        if (
            this[methodName] 
            && typeof this[methodName] === 'function'
        ) {
            this[methodName]();
        }
        else {
            this.runGameDefault();
        }
    };

    protected runGameDefault() {
        // #07
        const width = 640;
        const height = 480;
        const renderer = new CanvasRenderer(width, height);
        this.board.appendChild(renderer.view);
        
        const scene = new ContainerEntity();

        // add message
        const message = new TextEntity('Please provide an existing game index:)', {
            font: '20px monospace',
            fill: 'blue',
            align: 'left',
        });
        message.position.x = width / 2;
        message.position.y = height / 2;
        message.update = function(dt, t) {
            this.position.x -= 100 * dt;
            if (this.position.x < -420) {
                this.position.x = width;
            }
        }
        scene.add(message);

        // add spaceships
        const texture = new Texture('/app/modules/Game/img/GameNoviceToNinja/01/spaceship.png');
        for (let i = 0; i < 50; i++) {
            const ship = new SpriteEntity(texture);
            ship.position.x = Math.random() * width;
            ship.position.y = Math.random() * height;
            const speed = Math.random() * 150 + 50;
            ship.update = function (dt) {
                this.position.x += speed * dt;
                if (this.position.x > width) {
                    this.position.x = -32;
                }
            };
            scene.add(ship);
        }
        
        // main loop
        let dt = 0;
        let last = 0;
        function loopy(ms: number) {
            requestAnimationFrame(loopy);
            
            const t = ms / 1000;
            dt = t - last;
            last = t;

            scene.update(dt, t);
            renderer.render(scene);
        }
        requestAnimationFrame(loopy);
    }

    protected runGame01() {
        const width = 640;
        const height = 300;
        const renderer = new CanvasRenderer(width, height);
        this.board.appendChild(renderer.view);
        
        const textures = {
            background: new Texture('/app/modules/Game/img/GameNoviceToNinja/01/bg.png'),
            spaceship: new Texture('/app/modules/Game/img/GameNoviceToNinja/01/spaceship.png'),
            bullet: new Texture('/app/modules/Game/img/GameNoviceToNinja/01/bullet.png'),
            baddie: new Texture('/app/modules/Game/img/GameNoviceToNinja/01/baddie.png'),
        };

        const scene = new ContainerEntity();
        const controls = new KeyControls();

        // ship
        const ship = new SpriteEntity(textures.spaceship);
        ship.position.x = 120;
        ship.position.y = height / 2 - 16;
        ship.update = function (dt) {
            this.position.x += controls.x * dt * 200;
            this.position.y += controls.y * dt * 200;
            if (this.position.x < 0) {
                this.position.x = 0;
            }
            if (this.position.x > width) {
                this.position.x = width;
            }
            if (this.position.y < 0) {
                this.position.y = 0;
            }
            if (this.position.y > height - 32) {
                this.position.y = height - 32;
            }
        }

        // bullets
        const bullets = new ContainerEntity();
        function fireBullet(x, y) {
            const bullet = new SpriteEntity(textures.bullet);
            bullet.position.x = x;
            bullet.position.y = y;
            bullet.update = function(dt) {
                this.position.x += 400 * dt;
            }
            bullets.add(bullet);
        }

        // bad boys
        const baddies = new ContainerEntity();
        function spawnBaddie(x, y, speed) {
            const baddie = new SpriteEntity(textures.baddie);
            baddie.position.x = x;
            baddie.position.y = y;
            baddie.update = function(dt) {
                this.position.x += speed * dt;
            }
            baddies.add(baddie);
        }

        // score
        const score = new TextEntity('score:', {
            font: '20px sans-serif',
            fill: '#8B8994',
            align: 'center',
        });
        score.position.x = width / 2;
        score.position.y = height - 30;

        // game over
        function doGameOver() {
            const gameOverMessage = new TextEntity('Game Over', {
                font: '30pt sans-serif',
                fill: '#8B8994',
                align: 'center',
            });
            gameOverMessage.position.x = width / 2;
            gameOverMessage.position.y = 120;
            scene.add(gameOverMessage);
            scene.remove(ship);
            gameOver = true;
        }

        // add everything to scene
        scene.add(new SpriteEntity(textures.background));
        scene.add(ship);
        scene.add(bullets);
        scene.add(baddies);
        scene.add(score);

        // game state
        let lastShot = 0;
        let lastSpawn = 0;
        let spawnSpeed = 1.0;
        let scoreAmount = 0;
        let gameOver = false;

        // main loop
        let dt = 0;
        let last = 0;
        function loopy(ms: number) {
            requestAnimationFrame(loopy);
            
            const t = ms / 1000;
            dt = t - last;
            last = t;
            
            // move everythig a tiny bit
            ship.position.x += Math.sin(t * 10); // "bob" the player
            scene.update(dt, t);

            // respond to collisions
            if (!gameOver && controls.action && t - lastShot > 0.15) {
                lastShot = t;
                fireBullet(ship.position.x + 24, ship.position.y + 10);
            }
            if (t - lastSpawn > spawnSpeed) {
                lastSpawn = t;
                const speed = -50 - (Math.random() * Math.random() * 100);
                const y = Math.random() * (height - 32);
                spawnBaddie(width, y, speed);
                spawnSpeed = spawnSpeed < 0.05 ? 0.6 : spawnSpeed * 0.97 + 0.001;
            }
            baddies.children.forEach(baddie => {
                bullets.children.forEach(bullet => {
                    const dx = baddie.position.x + 16 - (bullet.position.x + 8);
                    const dy = baddie.position.y + 16 - (bullet.position.y + 8);
                    const hypotenuse = Math.sqrt(dx * dx + dy * dy);
                    if (hypotenuse < 24) {
                        baddie.dead = true;
                        bullet.dead = true;
                        //scoreAmount += Math.floor(t);
                        scoreAmount++;
                        score.text = 'score: ' + scoreAmount;
                    }
                    // remove bullets out of screen
                    if (bullet.position.x >= width + 20) {
                        bullet.dead = true;
                    }
                });
                // check game over and remove beddies out of screen
                if (baddie.position.x < -32) {
                    if (!gameOver) {
                        doGameOver();
                    }
                    baddie.dead = true;
                }
            });

            // draw everything
            renderer.render(scene);
        }
        requestAnimationFrame(loopy);
    }

    protected runGame02() {
        const game = new Game(640, 320, this.board);
        const { scene, width, height } = game;

        const textures = {
            background: new Texture('/app/modules/Game/img/GameNoviceToNinja/02/bg.png'),
            building: new Texture('/app/modules/Game/img/GameNoviceToNinja/02/building.png'),
            ship: new Texture('/app/modules/Game/img/GameNoviceToNinja/02/spaceship.png'),
            crosshair: new Texture('/app/modules/Game/img/GameNoviceToNinja/02/crosshair.png'),
        }

        const mouse = new MouseControls(game.renderer.view);
        const keyboard = new KeyControls();

        // ship
        const ship = new SpriteEntity(textures.ship);
        ship.position = {x: 80, y: 120};
        ship.anchor = {x: -16, y: -16};
        ship.pivot = {x: 16, y: 16};
        ship.update = function(dt, t) {
            this.position.x += dt * 200;
            if (this.position.x > width) {
                this.position.x = -32;
            }
            const rps = Math.PI * 2 * dt;
            this.rotation += rps;
            // // pulse
            // this.scale.x = Math.abs(Math.sin(t)) + 1;
            // this.scale.y = Math.abs(Math.sin(t * 1.33)) + 1;
        }
        // buildings
        const buildings = new ContainerEntity();
        const makeBuildingRandom = (building, x) => {
            building.scale.x = math.getRandomFloat(1, 3);
            building.scale.y = math.getRandomFloat(1, 3);
            building.position.x = x;
            building.position.y = height - building.scale.y * 64;
        };
        for (let x = 0; x < 10; x++) {
            const building = buildings.add(new SpriteEntity(textures.building));
            makeBuildingRandom(building, math.getRandomInteger(width));
        }
        // crosshair
        const crosshair = new SpriteEntity(textures.crosshair);
        crosshair.position = ship.position;
        crosshair.anchor = {x: -16, y: -16};
        // squizzballs
        const balls = new ContainerEntity<Squizz>();
        for (let i = 0; i < 30; i++) {
            const squizz = balls.add(new Squizz(keyboard));
            squizz.position.x = math.getRandomInteger(width);
            squizz.position.y = math.getRandomInteger(height);
        }

        // scene
        scene.add(new SpriteEntity(textures.background));
        scene.add(buildings);
        scene.add(ship);
        scene.add(crosshair);
        scene.add(balls);

        game.run((dt, t) => {
            buildings.map(building => {
                building.position.x -= 100 * dt;
                if (building.position.x < -80) {
                    makeBuildingRandom(building, width);
                }
            });
            balls.map(ball => {
                if (ball.position.x > width) {
                    ball.position.x = -32;
                    ball.speed *= 1.1;
                }
                if (
                    mouse.justPressed 
                    && math.getDistance(mouse.position, ball.position) < 16
                ) {
                    if (ball.speed > 0) {
                        ball.speed = 0;
                    }
                    else {
                        ball.dead = true;
                    }
                }
            });
            mouse.reset();
        });
    }
}

export interface GameNoviceToNinjaOptions {

    /** Main element selector. Defaults to NULL. */
    selector?: string;

    /** Index of game to be launched. Defaults to NULL. */
    gameIndex?: number;
}
