import { KeyControls } from "../../libs/controls/KeyControls";
import { Texture } from "../../libs/Texture";
import { TileSpriteEntity } from "../../libs/TileSpriteEntity";
import { Position } from "../../libs/types";
import math from "../../libs/utils/math";

const texture = new Texture('/app/modules/Game/img/GameNoviceToNinja/02/player-walk.png');

export class Squizz extends TileSpriteEntity {

    public speed = math.getRandomFloat(0.9, 1.2);

    public anchor = {x: -16, y: -16};

    constructor(
        public controls: KeyControls
    ) {
        super(texture, 32, 32);
        this.animations.add('walk', [
            {x: 0, y: 0},
            {x: 1, y: 0},
            {x: 2, y: 0},
            {x: 3, y: 0},
        ], 0.07 * this.speed);
        this.animations.add('idle', [
            {x: 0, y: 0},
            {x: 4, y: 0},
            {x: 4, y: 1},
            {x: 4, y: 0},
        ], 0.15 * this.speed);
        this.animations.play('walk');
    }

    update(dt: number) {
        super.update(dt);

        this.position.x += this.controls.x * this.speed * 100 * dt;

        if (this.controls.x) {
            this.animations.play('walk');
            this.scale.x = Math.sign(this.controls.x);
            this.anchor.x = this.scale.x > 0 ? -16: 16;
        }
        else {
            this.animations.play('idle');
        }
    }
    
}