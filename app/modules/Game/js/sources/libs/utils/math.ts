import { Position } from "../types";

function getRandomFloat(min: number, max?: number): number {
    if (typeof max !== 'number') {
        max = min || 1;
        min = 0;
    }
    return Math.random() * (max - min) + min;
}

function  getRandomInteger(min: number, max?: number): number {
    return Math.floor(getRandomFloat(min, max));
}

function  getRandomTrueEach(times = 2): boolean {
    return getRandomInteger(times) === 0;
}

function  getRandonItemFrom(items: any[]): any {
    return items[getRandomInteger(items.length)];
}

function getDistance(a: Position, b: Position): number {
    const dx = a.x - b.x;
    const dy = a.y - b.y;
    return Math.sqrt(dx * dx + dy * dy);
}

export default {
    getRandomFloat,
    getRandomInteger,
    getRandomTrueEach,
    getRandonItemFrom,
    getDistance,
}
