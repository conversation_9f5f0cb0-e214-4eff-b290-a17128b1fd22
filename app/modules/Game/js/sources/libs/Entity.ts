import { Position } from "./types";

export class Entity {
        
    /** Position of entity anchor point */
    public position: Position = {x: 0,  y: 0};
    
    /** Anchor point of entity the position is applied to.
     * Value {x: 0, y: 0} corresponds to top left corner.
     * Value {x: -width, y: -height} corresponds to bottom right corner.
     * Value {x: -width / 2, y: -height / 2} corresponds to center. */
    public anchor: Position = {x: 0, y: 0};
    
    /** If negative then the image is flipped. */
    public scale: Position = {x: 1,  y: 1};

    /** Rotation angle in radians (n * Math.PI). 
     * The entity is rotated round specified pivot */
    public rotation: number = 0;

    /** Rotation pivot of entity. The entity is rotated round this pivot.
     * Value {x: 0, y: 0} corresponds to top left corner.
     * Value {x: width, y: height} corresponds to bottom right corner.
     * Value {x: width / 2, y: height / 2} corresponds to center. */
    public pivot: Position = {x: 0, y: 0};

    /** Invisible entities are not rendered (skipped on rendering) */
    public visible: boolean = true;

    /** Dead entities are removed on scene update */
    public dead: boolean = false;
    
    update(dt: number, t?: number): void {
        // by default no update is done, the entity is static
    }
    
}
