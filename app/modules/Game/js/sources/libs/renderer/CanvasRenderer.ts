import { Entity } from '../Entity';
import { ContainerEntity } from '../ContainerEntity';
import { TextEntity } from '../TextEntity';
import { SpriteEntity } from '../SpriteEntity';
import { TileSpriteEntity } from '../TileSpriteEntity';

export class CanvasRenderer {
    
    protected _view: HTMLCanvasElement;
    
    public get view() {
        return this._view;
    }
    
    protected _context: CanvasRenderingContext2D;
    
    constructor(
        protected width: number,
        protected height: number
    ) {
        const canvas = document.createElement('canvas');
        canvas.width = this.width;
        canvas.height = this.height;
        this._view = canvas;
        this._context = canvas.getContext('2d');
        // set default text positioning relatively to top-left-corner of rendered text
        this._context.textBaseline = 'top';
    }
    
    public render(
        entity: Entity | ContainerEntity, 
        clear = true
    ) {
        const _render = (entity: Entity | ContainerEntity) => {
            if (!entity.visible) {
                return;
            }
            this._context.save();
            // render the entity
            this._context.translate(
                Math.round(entity.position.x), 
                Math.round(entity.position.y)
            );
            this._context.translate(
                entity.anchor.x, 
                entity.anchor.y
            );
            this._context.scale(
                entity.scale.x, 
                entity.scale.y
            );
            if (entity.rotation) {
                this._context.translate(
                    entity.pivot.x, 
                    entity.pivot.y
                );
                this._context.rotate(
                    entity.rotation
                );
                this._context.translate(
                    -entity.pivot.x, 
                    -entity.pivot.y
                );
            }
            if (entity instanceof TextEntity) {
                if (entity.style.font) {
                    this._context.font = entity.style.font;
                }
                if (entity.style.fill) {
                    this._context.fillStyle = entity.style.fill;
                }
                if (entity.style.align) {
                    this._context.textAlign = entity.style.align;
                }
                this._context.fillText(entity.text, 0, 0);
            }
            else if (entity instanceof TileSpriteEntity) {
                this._context.drawImage(
                    entity.texture.image,
                    entity.frame.x * entity.tileWidth,          // source x
                    entity.frame.y * entity.tileHeight,         // source y
                    entity.tileWidth, entity.tileHeight,        // source width & height
                    0, 0,                                       // destination x & y
                    entity.tileWidth, entity.tileHeight         // destination width & height
                );
            }
            else if (entity instanceof SpriteEntity) {
                this._context.drawImage(entity.texture.image, 0, 0);
            }
            
            // render children
            if (entity instanceof ContainerEntity) {
                entity.children.forEach(child => {
                    _render(child);
                })
            }
            this._context.restore();
        }
        if (clear) {
            this._context.clearRect(0, 0, this.width, this.height);
        }
        _render(entity);
    }
}
