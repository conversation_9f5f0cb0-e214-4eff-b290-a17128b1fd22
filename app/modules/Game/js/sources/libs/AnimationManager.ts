import { TileSpriteAnimation } from "./TileSpriteAnimation";
import { TileSpriteEntity } from "./TileSpriteEntity";
import { Position } from "./types";

export class AnimationManager {

    protected _animations: Map<string, TileSpriteAnimation> = new Map();
    protected _currentAnimationName: string = null;

    constructor(
        protected _tileSprite: TileSpriteEntity
    ) {}

    add(
        name: string, 
        frames: Position[], 
        frameTime: number
    ): TileSpriteAnimation {
        this._animations.set(name, new TileSpriteAnimation(frames, frameTime));
        return this._animations.get(name);
    }

    update(dt: number) {
        if (!this._currentAnimationName) {
            return;
        }
        
        const animation = this._animations.get(this._currentAnimationName);
        animation.update(dt);
        this._tileSprite.frame.x = animation.frame.x;
        this._tileSprite.frame.y = animation.frame.y;
    }

    play(name: string) {
        if (name === this._currentAnimationName) {
            return;
        }
        this._currentAnimationName = name;
        this._animations.get(name).reset();
    }

    stop() {
        this._currentAnimationName = null;
    }
}