export class KeyControls {
    
    protected keys: {[code: number]: boolean} = {};
    
    constructor() {
        document.addEventListener('keydown', event => {
            if ([37, 38, 39, 40, 32].indexOf(event.which) >= 0) {
                event.preventDefault();
            }
            this.keys[event.which] = true;
        }, false);
        document.addEventListener('keyup', event => {
            this.keys[event.which] = false;
        }, false);
    }
    
    public get action(): boolean {
        return !!this.keys[32];
    }
    
    public get x(): -1 | 0 | 1 {
        // left arrow or A key
        if (this.keys[37] || this.keys[65]) {
            return -1;
        }
        // right arrow or D key
        if (this.keys[39] || this.keys[68]) {
            return 1;
        }
        return 0;
    }
    
    public get y(): -1 | 0 | 1 {
        // up arrow or W key
        if (this.keys[38] || this.keys[87]) {
            return -1;
        }
        // down arrow or S key
        if (this.keys[40] || this.keys[83]) {
            return 1;
        }
        return 0;
    }
    
    public key(code: number, value?: boolean): boolean {
        if (value !== undefined) {
            this.keys[code] = value;
        }
        return this.keys[code];
    }
    
    public reset() {
        for (let code in this.keys) {
            this.keys[code] = false;
        }
    }
    
}
