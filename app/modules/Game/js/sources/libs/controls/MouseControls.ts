import { Position } from "../types";

export class MouseControls {
    
    protected element: HTMLElement;
    
    public position: Position = {x: 0,  y: 0};
    
    /** This is TRUE all the time the mouse is pressed */
    public isPressed = false;
    
    /** This is TRUE only once per click-down */
    public justPressed = false;
    
    /** This is TRUE only once per click-up */
    public justReleased = false;
    
    constructor(element: HTMLElement) {
        this.element = element || document.body;
        
        document.addEventListener('mousemove', this.onMove.bind(this), false);
        document.addEventListener('mousedown', this.onDown.bind(this), false);
        document.addEventListener('mouseup', this.onUp.bind(this), false);
    }
    
    /** Resets justPressed and justReleased properties to FALSE.
     * This must be called at the end of game loop */
    public reset() {
        this.justPressed = false;
        this.justReleased = false;
    }
    
    protected resolveMousePositionFromEvent(event: MouseEvent) {
        const {clientX, clientY} = event;
        const rectangle = this.element.getBoundingClientRect();
        const xRate = (this.element as any).width / this.element.clientWidth;
        const yRate = (this.element as any).height / this.element.clientHeight;
        this.position.x = (clientX - rectangle.left) * xRate;
        this.position.y = (clientY - rectangle.top) * yRate;
    }
    
    protected onMove(event: MouseEvent) {
        this.resolveMousePositionFromEvent(event);
    }
    
    protected onDown(event: MouseEvent) {
        this.isPressed = true;
        this.justPressed = true;
        this.resolveMousePositionFromEvent(event);
    }
    
    protected onUp() {
        this.isPressed = false;
        this.justReleased =  true;
    }
        
}
