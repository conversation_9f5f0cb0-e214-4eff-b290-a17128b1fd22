import { Entity } from './Entity';

export class ContainerEntity<T extends Entity = Entity> extends Entity {
        
    public children: T[] = [];
        
    constructor() {
        super();
    }
    
    public add(child: T) {
        this.children.push(child);
        return child;
    }
    
    public remove(child: T) {
        this.children = this.children.filter(item => item !== child);
        return child;
    }
    
    public map(
        callback: (child: T) => any
    ) {
        return this.children.map(callback);
    }
    
    public update(dt: number, t: number) {
        this.children = this.children.filter(child => {
            child.update(dt, t);
            return !child.dead;
        })
    }
}
