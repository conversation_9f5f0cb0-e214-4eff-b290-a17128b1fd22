import { AnimationManager } from "./AnimationManager";
import { SpriteEntity } from "./SpriteEntity";
import { Texture } from "./Texture";
import { Position } from "./types";

export class TileSpriteEntity extends SpriteEntity {

    /** Tile frame (position / offset) given by x and y index */
    public frame: Position = {x: 0, y: 0};

    public animations = new AnimationManager(this);

    get tileWidth() {
        return this._tileWidth;
    }

    get tileHeight() {
        return this._tileHeight;
    }

    constructor(
        texture: Texture, 
        protected _tileWidth: number,
        protected _tileHeight: number
    ) {
        super(texture);
    }

    update(dt: number) {
        this.animations.update(dt);
    }
}