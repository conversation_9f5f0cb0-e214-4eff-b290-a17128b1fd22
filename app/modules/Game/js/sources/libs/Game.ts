import { ContainerEntity } from "./ContainerEntity";
import { CanvasRenderer } from "./renderer/CanvasRenderer";

export class Game {

    protected static readonly STEP = 1 / 60;
    protected static readonly MAX_FRAME = Game.STEP * 5;

    protected _renderer: CanvasRenderer;

    get renderer() {
        return this._renderer;
    }

    protected _scene: ContainerEntity;

    get scene() {
        return this._scene;
    }

    get width() {
        return this._width;
    }

    get height() {
        return this._height;
    }

    constructor (
        protected _width: number,
        protected _height: number,
        protected _parentElement: HTMLElement,
    ) {
        this._renderer = new CanvasRenderer(this._width, this._height);
        this._parentElement.appendChild(this._renderer.view);

        this._scene = new ContainerEntity();
    }

    run(updateState: (dt: number, t: number) => void) {
        let dt = 0;
        let last = 0;
        const loopy = (ms) => {
            requestAnimationFrame(loopy);

            const t = ms / 1000;
            dt = Math.min(t - last, Game.MAX_FRAME);
            last = t;

            this._scene.update(dt, t);
            updateState(dt, t);
            this._renderer.render(this._scene);
        }
        requestAnimationFrame(loopy);
    }
}