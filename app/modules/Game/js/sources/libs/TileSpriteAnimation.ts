import { Position } from "./types";

export class TileSpriteAnimation {

    protected _elapsedFrameTime = 0;
    protected _frameIndex = 0;
    protected _frame: Position;

    get frame() {
        return this._frame;
    }

    constructor(
        protected _frames: Position[],
        protected _frameTime: number
    ) {
        this.reset();
    }

    update(dt: number) {
        this._elapsedFrameTime += dt;
        if (this._elapsedFrameTime > this._frameTime) {
            this._frameIndex++;
            this._frame = this._frames[this._frameIndex % this._frames.length];
            this._elapsedFrameTime -= this._frameTime;
        }
    }

    reset() {
        this._frame = this._frames[0];
        this._elapsedFrameTime = 0;
        this._frameIndex = 0;
    }

}