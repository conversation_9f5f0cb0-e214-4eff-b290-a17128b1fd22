System.register([], function (exports_1, context_1) {
    "use strict";
    var Animation;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [],
        execute: function () {
            Animation = /** @class */ (function () {
                function Animation(_frameTime, _frames) {
                    this._frameTime = _frameTime;
                    this._frames = _frames;
                    this._elapsedFrameTime = 0;
                    this._frameIndex = 0;
                    this.reset();
                }
                Animation.prototype.update = function (dt) {
                    this._elapsedFrameTime += dt;
                    if (this._elapsedFrameTime > this._frameTime) {
                        this._frameIndex++;
                        this._frame = this._frames[this._frameIndex % this._frames.length];
                        this._elapsedFrameTime -= this._frameTime;
                    }
                };
                Animation.prototype.reset = function () {
                    this._frame = this._frames[0];
                    this._elapsedFrameTime = 0;
                    this._frameIndex = 0;
                };
                return Animation;
            }());
            exports_1("Animation", Animation);
        }
    };
});
