System.register(["./Entity"], function (exports_1, context_1) {
    "use strict";
    var __extends = (this && this.__extends) || (function () {
        var extendStatics = function (d, b) {
            extendStatics = Object.setPrototypeOf ||
                ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
                function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };
            return extendStatics(d, b);
        };
        return function (d, b) {
            extendStatics(d, b);
            function __() { this.constructor = d; }
            d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
        };
    })();
    var Entity_1, DynamicEntity;
    var __moduleName = context_1 && context_1.id;
    return {
        setters: [
            function (Entity_1_1) {
                Entity_1 = Entity_1_1;
            }
        ],
        execute: function () {
            DynamicEntity = /** @class */ (function (_super) {
                __extends(DynamicEntity, _super);
                function DynamicEntity() {
                    return _super !== null && _super.apply(this, arguments) || this;
                }
                DynamicEntity.prototype.update = function (dt, t) {
                };
                return DynamicEntity;
            }(Entity_1.Entity));
            exports_1("DynamicEntity", DynamicEntity);
        }
    };
});
