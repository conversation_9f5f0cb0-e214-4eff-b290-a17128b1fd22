System.register(["./Entity"],function(t,n){"use strict";var e,r,i,o=this&&this.__extends||(e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var r in n)n.hasOwnProperty(r)&&(t[r]=n[r])})(t,n)},function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)});n&&n.id;return{setters:[function(t){r=t}],execute:function(){i=function(n){function t(){var t=n.call(this)||this;return t.children=[],t}return o(t,n),t.prototype.add=function(t){return this.children.push(t),t},t.prototype.remove=function(n){return this.children=this.children.filter(function(t){return t!==n}),n},t.prototype.map=function(t){return this.children.map(t)},t.prototype.update=function(n,r){this.children=this.children.filter(function(t){return t.update(n,r),!t.dead})},t}(r.Entity),t("ContainerEntity",i)}}});