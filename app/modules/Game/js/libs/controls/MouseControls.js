System.register([],function(t,e){"use strict";var s;e&&e.id;return{setters:[],execute:function(){s=function(){function t(t){this.position={x:0,y:0},this.isPressed=!1,this.justPressed=!1,this.justReleased=!1,this.element=t||document.body,document.addEventListener("mousemove",this.onMove.bind(this),!1),document.addEventListener("mousedown",this.onDown.bind(this),!1),document.addEventListener("mouseup",this.onUp.bind(this),!1)}return t.prototype.reset=function(){this.justPressed=!1,this.justReleased=!1},t.prototype.resolveMousePositionFromEvent=function(t){var e=t.clientX,s=t.clientY,i=this.element.getBoundingClientRect(),n=this.element.width/this.element.clientWidth,o=this.element.height/this.element.clientHeight;this.position.x=(e-i.left)*n,this.position.y=(s-i.top)*o},t.prototype.onMove=function(t){this.resolveMousePositionFromEvent(t)},t.prototype.onDown=function(t){this.isPressed=!0,this.justPressed=!0,this.resolveMousePositionFromEvent(t)},t.prototype.onUp=function(){this.isPressed=!1,this.justReleased=!0},t}(),t("MouseControls",s)}}});