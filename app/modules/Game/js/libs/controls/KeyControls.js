System.register([],function(e,t){"use strict";var n;t&&t.id;return{setters:[],execute:function(){n=function(){function e(){var t=this;this.keys={},document.addEventListener("keydown",function(e){0<=[37,38,39,40,32].indexOf(e.which)&&e.preventDefault(),t.keys[e.which]=!0},!1),document.addEventListener("keyup",function(e){t.keys[e.which]=!1},!1)}return Object.defineProperty(e.prototype,"action",{get:function(){return!!this.keys[32]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"x",{get:function(){return this.keys[37]||this.keys[65]?-1:this.keys[39]||this.keys[68]?1:0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"y",{get:function(){return this.keys[38]||this.keys[87]?-1:this.keys[40]||this.keys[83]?1:0},enumerable:!1,configurable:!0}),e.prototype.key=function(e,t){return void 0!==t&&(this.keys[e]=t),this.keys[e]},e.prototype.reset=function(){for(var e in this.keys)this.keys[e]=!1},e}(),e("KeyControls",n)}}});