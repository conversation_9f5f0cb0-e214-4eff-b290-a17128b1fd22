System.register(["./AnimationManager","./SpriteEntity"],function(t,e){"use strict";var i,o,n,r,u=this&&this.__extends||(i=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});e&&e.id;return{setters:[function(t){o=t},function(t){n=t}],execute:function(){r=function(r){function t(t,e,n){var i=r.call(this,t)||this;return i._tileWidth=e,i._tileHeight=n,i.frame={x:0,y:0},i.animations=new o.AnimationManager(i),i}return u(t,r),Object.defineProperty(t.prototype,"tileWidth",{get:function(){return this._tileWidth},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tileHeight",{get:function(){return this._tileHeight},enumerable:!1,configurable:!0}),t.prototype.update=function(t){this.animations.update(t)},t}(n.SpriteEntity),t("TileSpriteEntity",r)}}});