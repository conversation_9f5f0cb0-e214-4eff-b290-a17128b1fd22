System.register(["../ContainerEntity","../TextEntity","../SpriteEntity","../TileSpriteEntity"],function(t,e){"use strict";var n,o,r,c,i;e&&e.id;return{setters:[function(t){n=t},function(t){o=t},function(t){r=t},function(t){c=t}],execute:function(){i=function(){function t(t,e){this.width=t,this.height=e;var i=document.createElement("canvas");i.width=this.width,i.height=this.height,this._view=i,this._context=i.getContext("2d"),this._context.textBaseline="top"}return Object.defineProperty(t.prototype,"view",{get:function(){return this._view},enumerable:!1,configurable:!0}),t.prototype.render=function(t,e){var i=this;void 0===e&&(e=!0);e&&this._context.clearRect(0,0,this.width,this.height),function e(t){t.visible&&(i._context.save(),i._context.translate(Math.round(t.position.x),Math.round(t.position.y)),i._context.translate(t.anchor.x,t.anchor.y),i._context.scale(t.scale.x,t.scale.y),t.rotation&&(i._context.translate(t.pivot.x,t.pivot.y),i._context.rotate(t.rotation),i._context.translate(-t.pivot.x,-t.pivot.y)),t instanceof o.TextEntity?(t.style.font&&(i._context.font=t.style.font),t.style.fill&&(i._context.fillStyle=t.style.fill),t.style.align&&(i._context.textAlign=t.style.align),i._context.fillText(t.text,0,0)):t instanceof c.TileSpriteEntity?i._context.drawImage(t.texture.image,t.frame.x*t.tileWidth,t.frame.y*t.tileHeight,t.tileWidth,t.tileHeight,0,0,t.tileWidth,t.tileHeight):t instanceof r.SpriteEntity&&i._context.drawImage(t.texture.image,0,0),t instanceof n.ContainerEntity&&t.children.forEach(function(t){e(t)}),i._context.restore())}(t)},t}(),t("CanvasRenderer",i)}}});