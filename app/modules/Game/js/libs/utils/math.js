System.register([],function(t,n){"use strict";n&&n.id;function e(t,n){return"number"!=typeof n&&(n=t||1,t=0),Math.random()*(n-t)+t}function r(t,n){return Math.floor(e(t,n))}function o(t){return void 0===t&&(t=2),0===r(t)}function u(t){return t[r(t.length)]}function a(t,n){var e=t.x-n.x,r=t.y-n.y;return Math.sqrt(e*e+r*r)}return{setters:[],execute:function(){t("default",{getRandomFloat:e,getRandomInteger:r,getRandomTrueEach:o,getRandonItemFrom:u,getDistance:a})}}});