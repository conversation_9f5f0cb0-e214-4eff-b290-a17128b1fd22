System.register(["./ContainerEntity","./renderer/CanvasRenderer"],function(e,t){"use strict";var r,i,n;t&&t.id;return{setters:[function(e){r=e},function(e){i=e}],execute:function(){n=function(){function s(e,t,n){this._width=e,this._height=t,this._parentElement=n,this._renderer=new i.CanvasRenderer(this._width,this._height),this._parentElement.appendChild(this._renderer.view),this._scene=new r.ContainerEntity}return Object.defineProperty(s.prototype,"renderer",{get:function(){return this._renderer},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"scene",{get:function(){return this._scene},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"width",{get:function(){return this._width},enumerable:!1,configurable:!0}),Object.defineProperty(s.prototype,"height",{get:function(){return this._height},enumerable:!1,configurable:!0}),s.prototype.run=function(r){var i=this,o=0,u=0;requestAnimationFrame(function e(t){requestAnimationFrame(e);var n=t/1e3;o=Math.min(n-u,s.MAX_FRAME),u=n,i._scene.update(o,n),r(o,n),i._renderer.render(i._scene)})},s.MAX_FRAME=5*(s.STEP=1/60),s}(),e("Game",n)}}});