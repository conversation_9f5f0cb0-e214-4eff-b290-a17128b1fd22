System.register(["./Entity"],function(t,n){"use strict";var o,e,r,i=this&&this.__extends||(o=function(t,n){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var e in n)n.hasOwnProperty(e)&&(t[e]=n[e])})(t,n)},function(t,n){function e(){this.constructor=t}o(t,n),t.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)});n&&n.id;return{setters:[function(t){e=t}],execute:function(){r=function(o){function t(t,n){void 0===t&&(t=""),void 0===n&&(n={});var e=o.call(this)||this;return e.text=t,e.style=n,e}return i(t,o),t}(e.Entity),t("TextEntity",r)}}});