System.register(["./Entity"],function(t,n){"use strict";var e,r,o,i=this&&this.__extends||(e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var r in n)n.hasOwnProperty(r)&&(t[r]=n[r])})(t,n)},function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)});n&&n.id;return{setters:[function(t){r=t}],execute:function(){o=function(r){function t(t){var n=r.call(this)||this;return n.texture=t,n}return i(t,r),t}(r.Entity),t("SpriteEntity",o)}}});