System.register(["../../libs/Texture","../../libs/TileSpriteEntity","../../libs/utils/math"],function(t,i){"use strict";var e,n,o,s,a,r,c=this&&this.__extends||(e=function(t,i){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var n in i)i.hasOwnProperty(n)&&(t[n]=i[n])})(t,i)},function(t,i){function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)});i&&i.id;return{setters:[function(t){n=t},function(t){o=t},function(t){s=t}],execute:function(){a=new n.Texture("/app/modules/Game/img/GameNoviceToNinja/02/player-walk.png"),r=function(n){function t(t){var i=n.call(this,a,32,32)||this;return i.controls=t,i.speed=s.default.getRandomFloat(.9,1.2),i.anchor={x:-16,y:-16},i.animations.add("walk",[{x:0,y:0},{x:1,y:0},{x:2,y:0},{x:3,y:0}],.07*i.speed),i.animations.add("idle",[{x:0,y:0},{x:4,y:0},{x:4,y:1},{x:4,y:0}],.15*i.speed),i.animations.play("walk"),i}return c(t,n),t.prototype.update=function(t){n.prototype.update.call(this,t),this.position.x+=this.controls.x*this.speed*100*t,this.controls.x?(this.animations.play("walk"),this.scale.x=Math.sign(this.controls.x),this.anchor.x=0<this.scale.x?-16:16):this.animations.play("idle")},t}(o.TileSpriteEntity),t("Squizz",r)}}});