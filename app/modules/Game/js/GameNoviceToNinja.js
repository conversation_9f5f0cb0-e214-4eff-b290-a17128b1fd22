System.register(["./libs/controls/KeyControls","./libs/controls/MouseControls","./libs/ContainerEntity","./libs/TextEntity","./libs/renderer/CanvasRenderer","./libs/Texture","./libs/SpriteEntity","./libs/Game","./libs/utils/math","./entities/02/Squizz"],function(i,t){"use strict";var G,x,E,T,N,C,S,y,v,g,n;t&&t.id;return{setters:[function(i){G=i},function(i){x=i},function(i){E=i},function(i){T=i},function(i){N=i},function(i){C=i},function(i){S=i},function(i){y=i},function(i){v=i},function(i){g=i}],execute:function(){n=function(){function t(i){void 0===i&&(i={}),this.options=null,this.options=Object.assign({},t.DEFAULTS,i),this.constructor===t&&this.init()}return t.prototype.init=function(){var i;if(0===(i=document.querySelectorAll(this.options.selector)).length)throw new Error(__jse("Game",'No board found accoding to provided selector ":selector:"',this.options));if(1<i.length)throw new Error(__jse("Game",'Many boards have been found accoding to provided selector ":selector:"',this.options));this.board=i[0];var t="runGame";this.options.gameIndex<10&&(t+="0"),this[t+=this.options.gameIndex]&&"function"==typeof this[t]?this[t]():this.runGameDefault()},t.prototype.runGameDefault=function(){var e=640,o=new N.CanvasRenderer(e,480);this.board.appendChild(o.view);var a=new E.ContainerEntity,i=new T.TextEntity("Please provide an existing game index:)",{font:"20px monospace",fill:"blue",align:"left"});i.position.x=320,i.position.y=240,i.update=function(i,t){this.position.x-=100*i,this.position.x<-420&&(this.position.x=e)},a.add(i);for(var s=new C.Texture("/app/modules/Game/img/GameNoviceToNinja/01/spaceship.png"),t=function(i){var t=new S.SpriteEntity(s);t.position.x=Math.random()*e,t.position.y=480*Math.random();var n=150*Math.random()+50;t.update=function(i){this.position.x+=n*i,this.position.x>e&&(this.position.x=-32)},a.add(t)},n=0;n<50;n++)t();var r=0,p=0;requestAnimationFrame(function i(t){requestAnimationFrame(i);var n=t/1e3;r=n-p,p=n,a.update(r,n),o.render(a)})},t.prototype.runGame01=function(){var a=640,s=300,r=new N.CanvasRenderer(a,s);this.board.appendChild(r.view);var p={background:new C.Texture("/app/modules/Game/img/GameNoviceToNinja/01/bg.png"),spaceship:new C.Texture("/app/modules/Game/img/GameNoviceToNinja/01/spaceship.png"),bullet:new C.Texture("/app/modules/Game/img/GameNoviceToNinja/01/bullet.png"),baddie:new C.Texture("/app/modules/Game/img/GameNoviceToNinja/01/baddie.png")},d=new E.ContainerEntity,u=new G.KeyControls,c=new S.SpriteEntity(p.spaceship);c.position.x=120,c.position.y=s/2-16,c.update=function(i){this.position.x+=u.x*i*200,this.position.y+=u.y*i*200,this.position.x<0&&(this.position.x=0),this.position.x>a&&(this.position.x=a),this.position.y<0&&(this.position.y=0),this.position.y>s-32&&(this.position.y=s-32)};var l=new E.ContainerEntity;var m=new E.ContainerEntity;var h=new T.TextEntity("score:",{font:"20px sans-serif",fill:"#8B8994",align:"center"});h.position.x=a/2,h.position.y=s-30,d.add(new S.SpriteEntity(p.background)),d.add(c),d.add(l),d.add(m),d.add(h);var f=0,x=0,y=1,v=0,g=!1,w=0,b=0;requestAnimationFrame(function i(t){requestAnimationFrame(i);var n=t/1e3;if(w=n-b,b=n,c.position.x+=Math.sin(10*n),d.update(w,n),!g&&u.action&&.15<n-f&&(f=n,function(i,t){var n=new S.SpriteEntity(p.bullet);n.position.x=i,n.position.y=t,n.update=function(i){this.position.x+=400*i},l.add(n)}(c.position.x+24,c.position.y+10)),y<n-x){x=n;var e=-50-Math.random()*Math.random()*100,o=Math.random()*(s-32);!function(i,t,n){var e=new S.SpriteEntity(p.baddie);e.position.x=i,e.position.y=t,e.update=function(i){this.position.x+=n*i},m.add(e)}(a,o,e),y=y<.05?.6:.97*y+.001}m.children.forEach(function(e){l.children.forEach(function(i){var t=e.position.x+16-(i.position.x+8),n=e.position.y+16-(i.position.y+8);Math.sqrt(t*t+n*n)<24&&(e.dead=!0,i.dead=!0,v++,h.text="score: "+v),i.position.x>=a+20&&(i.dead=!0)}),e.position.x<-32&&(g||function(){var i=new T.TextEntity("Game Over",{font:"30pt sans-serif",fill:"#8B8994",align:"center"});i.position.x=a/2,i.position.y=120,d.add(i),d.remove(c),g=!0}(),e.dead=!0)}),r.render(d)})},t.prototype.runGame02=function(){var i=new y.Game(640,320,this.board),t=i.scene,e=i.width,n=i.height,o={background:new C.Texture("/app/modules/Game/img/GameNoviceToNinja/02/bg.png"),building:new C.Texture("/app/modules/Game/img/GameNoviceToNinja/02/building.png"),ship:new C.Texture("/app/modules/Game/img/GameNoviceToNinja/02/spaceship.png"),crosshair:new C.Texture("/app/modules/Game/img/GameNoviceToNinja/02/crosshair.png")},a=new x.MouseControls(i.renderer.view),s=new G.KeyControls,r=new S.SpriteEntity(o.ship);r.position={x:80,y:120},r.anchor={x:-16,y:-16},r.pivot={x:16,y:16},r.update=function(i,t){this.position.x+=200*i,this.position.x>e&&(this.position.x=-32);var n=2*Math.PI*i;this.rotation+=n};for(var p=new E.ContainerEntity,d=function(i,t){i.scale.x=v.default.getRandomFloat(1,3),i.scale.y=v.default.getRandomFloat(1,3),i.position.x=t,i.position.y=n-64*i.scale.y},u=0;u<10;u++){var c=p.add(new S.SpriteEntity(o.building));d(c,v.default.getRandomInteger(e))}var l=new S.SpriteEntity(o.crosshair);l.position=r.position,l.anchor={x:-16,y:-16};for(var m=new E.ContainerEntity,h=0;h<30;h++){var f=m.add(new g.Squizz(s));f.position.x=v.default.getRandomInteger(e),f.position.y=v.default.getRandomInteger(n)}t.add(new S.SpriteEntity(o.background)),t.add(p),t.add(r),t.add(l),t.add(m),i.run(function(t,i){p.map(function(i){i.position.x-=100*t,i.position.x<-80&&d(i,e)}),m.map(function(i){i.position.x>e&&(i.position.x=-32,i.speed*=1.1),a.justPressed&&v.default.getDistance(a.position,i.position)<16&&(0<i.speed?i.speed=0:i.dead=!0)}),a.reset()})},t.DEFAULTS={selector:null,gameIndex:null},t}(),i("GameNoviceToNinja",n)}}});