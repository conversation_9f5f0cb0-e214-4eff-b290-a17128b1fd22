<?php
class GameSettings extends GameController {

    protected $model = 'GameSetting';
    
    /**
     * Allow the model methods hinting in IDE
     * @var GameSetting
     */
    protected $Model;

    public function admin_edit() {   
        if ($this->data) {
            $this->saveResult = $this->Model->updateByName($this->data, array(
                'lang' => $this->lang,
            ));
        }
        else {
            $this->viewOptions['data'] = $this->Model->findListWithNameKeys(array(
                'lang' => $this->lang,
            ));
        }
        $this->viewOptions['fields'] = array(
            array('h1' => __a(__FILE__, 'Basic settings')), // 'Základné nastavenia'
            array('row'),
//                'Game/mySetting' => array(
//                    'label' => __a(__FILE__, 'Default VAT rate (%)'),
//                ),
//                //...
            array('/row'),
        );
        $this->viewOptions['title'] = __a(__FILE__, 'Game settings'); // 'Nastavenia modulu Game'
        $this->seoTitle = __a(__FILE__, 'Game settings'); // 'Nastavenia modulu Game'
        return parent::admin_edit();        
    }
}
