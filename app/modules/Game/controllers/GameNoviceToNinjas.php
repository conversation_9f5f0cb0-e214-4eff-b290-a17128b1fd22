<?php

/**
 * Repo:
 * https://github.com/spbooks/html5games1
 * 
 * Pixel art editor:
 * https://www.aseprite.org/
 * 
 * Assets:
 * https://opengameart.org/
 * https://kenney.nl/assets
 * 
 * Frameworks:
 * http://phaser.io/
 * https://impactjs.com/
 * https://www.yoyogames.com/gamemaker
 * https://www.scirra.com/
 * https://unity.com/
 * https://godotengine.org/
 * https://www.pixijs.com/
 * 
 * Articles:
 * https://gafferongames.com/post/fix_your_timestep/
 */
class GameNoviceToNinjas  extends GameController {
    
    public function load($gameIndex = null) {
        $this->setLayout('default');
        App::setSeoTitle(__(__FILE__, 'Game Novice to Ninja'));
        return $this->loadView('GameNoviceToNinjas/load', array(
            'gameIndex' => $gameIndex,
        ));
    }
    
}
