<?php
class DocumentorTodoLog extends Model {
    
    protected $table = 'run_documentor_todo_logs';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_documentor_todos_id' => array('type' => 'int', 'index' => 'index'),
        'run_users_id' => array('type' => 'int', 'index' => 'index', 'comment' => 'Solver user id'),
        'changes' => array('type' => 'mediumtext'), 
        'created' => array('type' => 'datetime', 'default' => null),
    );
    
}
