<?php
/**
 * Modul Documentor refaktoruj na modul Team ktorý by sp<PERSON><PERSON><PERSON> todo a vláčiky do jedného.
 * 
 * Todo bysa do pracovného procesu zapájali (ak vôbec) tak len pomalých čiatkach.
 * Čiže na rozdiel od bežného projektu, ktorý sa kontinuálne zapracováva a občas ho niečo
 * malé ale ucelené preruší, by sa jedno todo zapracovávalo prerušovanie pomedzi kontinuálne projekty.
 * Todo by mohli by<PERSON> osobitne (často krát by sa zapracovávali vo voľnom čase a nie v práci, takže
 * by sa vôbec nerátali do vláčikov). Možno by sa mohli vytvárať rozn<PERSON> vl<PERSON> (TaskQueues)
 * 
 * TeamTasks rozneho druhu by sa zaraďovali do rôzných TeamTaskQueues (1:1) a v nich
 * by si ich vyberali a priraďovali konkrétni developeri. Boli by napr<PERSON><PERSON> 'Práca', 'Todo' alebo '<PERSON>sobné'.
 * Pr<PERSON>padne by bolo možné vytvoriť Queue, ktorá by patrila len danému užívateľovi a 
 * iní by ju ani (prípadne) nemuseli vidieť. Základná úloha by mala len všeobecné polia, 
 * ktoré by sa pre určitý typ úlohy (napr. typ FajnworkTodoTask) rozšírili pripojenou tabuľkou
 * o specifické polia a tie by sa potom doplňali aj do formulára úlohy. TeamTasks by mali svoje Occurences, 
 * ktoré by sa pre daného užívateľa nesmeli prekrývať (bez ohľadu na to v ktorej Queue je Task zaradená, 
 * prípadne by bolo potrebné premyslieť nejaký spôsob prekývania pre vsunúté, opakujúce sa úlohy s tým že by v konečnom
 * dosledku nešlo o prekrytie ale o prerušenie dlhšej úlohy a dočasné riešenie kratšej). 
 * Toto by sa prenieslo z OrganizerModulu. Prípadne ostáva na zváženie či by sa všetko horeuvedené nejak
 * nemohlo zapracovať do organizéra (OrganizerTasks, OrganizerTaskQueues, OrganizerTaskOccurences)
 * Takisto by mohli byť úlohy, ktoré by ešte neboli zaradené do žiadje Queue a čakali by na to, že
 * si ich niekto priradí.
 * 
 * Bolo by fajn keby bolo možné za prioritu Todo aj hlasovať.
 * Prípadne by bolo fajn mať možnosť označiť todo miľnikom/kmi (cieľmi?) (milestone/targets?/aims?) ku ktorým by 
 * mali byť zapracované. Napríklad zapracovanie namespaces, autoloadingu, inštančnej DB, 
 * App, FormHeper refact, ... k mílniku (cieľu?) "Zverejnenie fajnworku"
 * 
 * Bolo by fajn pridať aj možnosť špecifikovať zoznam ulohy ktoré je potrebné urobiť
 * predtým ako sa pustím do danej úlohy (required to be done, prerequisites), napr.
 * ak chcem zapracovať Model::$validations 'exception' tak potrebujem mať autoloading hotový.
 * Toto by následne ukázalo, ktoré úlohy je potrebné zapracovať ako prvé
 */
class DocumentorTodo extends Model {
    
    protected $table = 'run_documentor_todos';
    
    // @todo - postihni tu nejak prioritu urgent - buď osobitné pole alebo len správny popis pri priority?
    // @todo - postihni tu nejak príznak 'nevyhnutné' - necessity, necessary
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'creator_id' => array('type' => 'int', 'index' => 'index', 'comment' => 'Todo creator id'),
        'solver_id' => array('type' => 'int', 'index' => 'index', 'default' => null, 'comment' => 'Todo solver id'),
        'name' => array('type' => 'varchar', 'length' => 255),
        'description' => array('type' => 'mediumtext', 'default' => null, 'comment' => 'Markdown of description which is internaly compiled to compiled_description'),
        'compiled_description' => array('type' => 'mediumtext', 'default' => null),
        'reason' => array('type' => 'enum', 'values' => array('new_functionality', 'existing_functionality_extension', 'performance_optimization', 'systematization', 'bugfix', 'development_optimization', 'security')),
        'reason_description' => array('type' => 'mediumtext', 'default' => null, 'comment' => 'Markdown of reason description which is internaly compiled to compiled_reason_description'),
        'compiled_reason_description' => array('type' => 'mediumtext', 'default' => null),
        'keywords' => array('type' => 'varchar', 'content' => 'Comma separated list of keywords of actual item'),
        'core_version' => array('type' => 'enum', 'values' => array('v1', 'v2', 'v3')),
        'modules' => array('type' => 'varchar', 'length' => 255, 'comment' => 'Comma separated list of involved modules, e.g. Core, App, Eshop'), 
        'classes' => array('type' => 'varchar', 'length' => 255, 'comment' => 'Comma separated list of involved classes, e.g. Model, FormHelper'),
        'ux_px' => array('type' => 'enum', 'values' => array('', 'UX', 'PX')),
        'front_back' => array('type' => 'enum', 'values' => array('', 'Frontend', 'Backend')),
        'refactoring_level' => array('type' => 'decimal', 'length' => 3.2, 'comment' => 'Float 0 - 5 = no - high'), 
        'backward_compatibility' => array('type' => 'bool', 'default' => 1, 'comment' => 'Can be this change pulled into older projects without manual merging?'),
        'difficulty_level' => array('type' => 'decimal', 'length' => 3.2, 'comment' => 'Float 1 - 5 = low - high'),
        'time_demands' => array('type' => 'decimal', 'length' => 3.2, 'comment' => 'Float 1 - 5 = low - high'), // časová náročnosť
        'specification_level' => array('type' => 'decimal', 'length' => 3.2, 'comment' => 'How much is the functionality described/specified by todo. Float 1 - 5 = low - full'),
        'usage_frequence' => array('type' => 'decimal', 'length' => 3.2, 'comment' => 'Float 1 - 5 = low - high'), // 'rarely', 'sometimes', 'often', 'very_often', 'always'
        'marketing_value' => array('type' => 'decimal', 'length' => 3.2, 'comment' => 'Float 1 - 5 = low - high'), // žiadna, doťahujeme na iných, predbiehame iných
        'priority' => array('type' => 'decimal', 'length' => 3.2, 'comment' => 'Overall priority counted as average from each user prioties. Float 1 - 5 = low - high - urgent'), //importancy_level ???
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
        'opened' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime when todo was opened by solver and started to be solved'),
        'completed' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime when todo was resolved'),
        'approved' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime when todo was approved by user which has rights to approve todos'),
        'rejected' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime when todo was rejected by user which has rights to reject todos'),
        'closed' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime when todo was closed by user which has rights to close todos (after approval or rejection)'),
    );
}
