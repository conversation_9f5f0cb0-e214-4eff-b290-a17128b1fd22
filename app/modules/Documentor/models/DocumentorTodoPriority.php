<?php

class DocumentorTodoPriority extends Model {
    
    protected $table = 'run_documentor_todo_priorities';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_documentor_todos_id' => array('type' => 'int', 'index' => 'index'),
        'run_users_id' => array('type' => 'int', 'index' => 'index'),
        'priority' => array('type' => 'decimal', 'length' => 3.2, 'comment' => 'User priority. Float 1 - 5 = low - high'), 
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
}
