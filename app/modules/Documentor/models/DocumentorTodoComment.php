<?php

class DocumentorTodoComment extends Model {
    
    protected $table = 'run_documentor_todo_comments';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_documentor_todos_id' => array('type' => 'int', 'index' => 'index'),
        'run_users_id' => array('type' => 'int', 'index' => 'index'),
        'text' => array('type' => 'mediumtext'),
        'type' => array('type' => 'enum', 'values' => array('addition', 'question', 'answer', 'state')),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
    
}

