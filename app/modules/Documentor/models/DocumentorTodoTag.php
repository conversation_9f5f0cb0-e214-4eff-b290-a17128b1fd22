<?php
class DocumentorTodoTag extends Model {
    
    protected $table = 'run_documentor_tags';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'name' => array('type' => 'varchar'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
}
