<?php
$data = $this->params['data'];
$Model = $this->params['Model'];
echo Html::smartForm(array(
    'title' => $this->params['title'],
    'data' => $data,
    'Model' => $Model,
    'columns' => 4,
    'fields' => array(
        array('if' => !empty($data['id'])),
            'id' => array('type' => 'hidden'),
        array('endif'),

        array('row', 'columns' => array(9, 3)),
            array(
                'field' => 'name', 
                'label' => __a(__FILE__, 'Name')
            ), 
            array(
                'field' => 'reason', 
                'label' => __a(__FILE__, 'Short reason'),
            ), 
        array('/row'),
        array('row', 'columns' => 1),
            array(
                'field' => 'description', 
                'label' => __a(__FILE__, 'Description'),
                'hint' => __a(__FILE__, 'Detailed description of todo with architecture and implementation details plus code snippets. Markdown syntax is available here'),
            ),
        array('/row'),
        array('row', 'columns' => 1),
            array(
                'field' => 'reason_description', 
                'label' => __a(__FILE__, 'Reason description'),
                'hint' => __a(__FILE__, 'Reason details, pros and cons. Markdown syntax is available here'),
            ),
        array('/row'),
        array('row', 'columns' => 2),
            array(
                'field' => 'modules', 
                'label' => __a(__FILE__, 'Modules'),
                'hint' => __a(__FILE__, 'Comma separated list of involved modules, e.g. Core, App, Eshop')
            ),
            array(
                'field' => 'classes', 
                'label' => __a(__FILE__, 'Classes'),
                'hint' => __a(__FILE__, 'Comma separated list of involved classes, e.g. Model, FormHelper')
            ),
        array('/row'),

        // INFO
        array('h1' => __a(__FILE__, 'Info')),
        array('row'),
            array(
                'field' => 'id', 
                'label' => 'Id', 
                'type' => 'display'
            ),
            array(
                'field' => 'created',
                'label' => __a(__FILE__, 'Date of creation'), 
                'type' => 'display',
            ),
            array(
                'field' => 'modified',
                'label' => __a(__FILE__, 'Date of modification'), 
                'type' => 'display',
            ),
        array('/row'),
    )        
));   
