Use this folder to place any kind of default content or setting (or whatever else) templates.
Then you can use them in `contents.php` like this:

```php
'App.cookiesPrivacyPolicyInfo' => array(
    //...
    'text' => App::loadScript('app/config/templates/cookiesPrivacyPolicyInfo.html'),
    //...
),
```

Or in `settings.php` like this:

```php
'EshopOrder.msgBodyNewOrder' => array(
    'value' => App::loadScript('app/modules/Eshop/config/templates/newOrderEmailBody.html'),
),
```

To easily view and debug template files use `.../_debug/viewFile`. e.g.:

```
.../_debug/viewFile?file=app/config/templates/cookiesPrivacyPolicyInfo.html&layout=App.default
.../_debug/viewFile?file=app/modules/Eshop/config/templates/newOrderEmailBody.html&layout=0
```