<?php 
/**
 * Module configs
 * 
 * To read these values use App::getConfig() function, e.g.:
 *
 *      App::getConfig('{module}'); // will return whole module $config array
 *      App::getConfig('{module}', 'fbApiId'); // will return value of module $config['fbApiId']
 *      App::getConfig('{module}', 'google.analytics'); // will return value of module $config['google']['analytics']
 * 
 * @var array
 */
$config = array(

); 
