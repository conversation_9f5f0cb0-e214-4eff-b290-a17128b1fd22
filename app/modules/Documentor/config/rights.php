<?php
/**
 * Module rights definitions
 * 
 * Auxiliary variable $edit serves to optimize performance. Use it in admin rights 
 * to define headers and labels in case when rights are loaded for editing. Only items
 * with defined labels are included in editing. Use pattern like:
 * 
 *      'admins' => array(
 *          array('h1' => $edit ? __a(__FILE__, 'My module rights') : true),
 *          array('h2' => $edit ? __a(__FILE__, 'My section rights') : true),
 *          'MyModel.admin_myAction1' => $edit ? __a(__FILE__, 'Do my action') : true,
 *          'MyModel.admin_myAction3' => true,                                                       // this will not be included in editing
 *          'MyModel.admin_myAction2' => $edit ? __a(__FILE__, 'Not important for admin') : false,      // editable but admin does not need to see it in backend
 *          ...,
 * 
 *      );
 * 
 * Admin rights must contain all actions which should be editable. If you have a case of 
 * method which is a weak version of stronger one, and admn does not need to use this method
 * and see it on backend then just set it to FALSE (see the last item in example above).
 * In special cases you can define FALSE items also in rights for other groups, just test it
 * if default behaviour does not meet your needs.
 * 
 * When loading rights for editing set $edit = TRUE. Otherwise just dont care about it.
 * 
 * !!!ATTENTION: Do not define headers and labels in rights for other than admins group!!!
 */
$edit = !empty($edit);
$rights = array(
    'public' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
        ),
        // these rights apply when the element is required by snippet
        'element' => array(
        ),
    ),
    'admins' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            'DocumentorContents.admin_index' => true,
            'DocumentorContents.admin_showSimpleTree' => true,
            'DocumentorContents.admin_add' => true,
            'DocumentorContents.admin_edit' => true,
            'DocumentorContents.admin_editText' => true,
            'DocumentorContents.admin_delete' => true,
            'DocumentorContents.admin_move' => true,
            
            'DocumentorTodos.admin_add' => true,
            'DocumentorTodos.admin_edit' => true,
        ),
        // these rights apply when the element is required by snippet
        'element' => array(
        ),
        // these rights apply when settings are listed, viewed and edited
        'setting' => array(
            
        ),
    ),
    'webmasters' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
        ),
        // these rights apply when the element is required by snippet
        'element' => array(
        ),
        // these rights apply when settings are listed, viewed and edited
        'setting' => array(
            
        ),
    ),
); 
