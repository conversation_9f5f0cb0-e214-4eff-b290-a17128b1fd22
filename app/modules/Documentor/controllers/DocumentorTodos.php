<?php
class DocumentorTodos extends Controller {
    
    
    public function admin_add() {
        $Todo = $this->loadModel('DocumentorTodo', true);
        
        if ($this->data) {
            if ($Todo->save($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully created'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                    'args' => array($Todo->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        
        App::setSeoTitle(__a(__FILE__, 'Add new todo'));
        return $this->loadView('DocumentorTodos/admin_form', array(
            'title' => __a(__FILE__, 'Add new todo'),
            'data' => $this->data,
            'Model' => $Todo,
        ));
    }      
    public function admin_edit($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        $Todo = $this->loadModel('DocumentorTodo', true);
        
        if ($this->data) {
            if ($Todo->save($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => $this->action,
                    'args' => array($id),
                    'source' => App::$requestSource,
                    'inherit' => array('get' => array('lang'))
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        else {
            $this->data = $Todo->findFirstBy('id', $id);
            // if product does not exist then redirect to referer
            if (empty($this->data)) {
                App::setErrorMessage(__a(__FILE__, 'Invalid record id %s', (string)$id));
                App::redirect(App::getRefererUrl(App::getUrl(array(
                    'locator' => '/_error',
                    'source' => App::$requestSource,
                ))));
            }
        }
        
        // add name to title and tabTitle
        if (!empty($this->data['name'])) {
            $name = $this->data['name'];
        }
        // name should not be empty, if so then it is error so use actual name
        else {
            $name = $Todo->findFieldBy('name', 'id', $this->data['id']);
        }
        App::setSeoTitle(__a(__FILE__, '&quot;%s&quot;', $name));
        return $this->loadView('DocumentorTodos/admin_form', array(
            'title' => __a(__FILE__, 'Edit todo &quot;%s&quot;', $name),
            'data' => $this->data,
            'Model' => $Todo,
        ));     
    }
}
