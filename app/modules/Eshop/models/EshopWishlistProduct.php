<?php
class EshopWishlistProduct extends Model {
    protected $table = 'run_eshop_wishlist_products';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_eshop_wishlists_id' => array('type' => 'int', 'index' => 'index'),
        'run_eshop_products_id' => array('type' => 'int', 'index' => 'index'),
        'amount' => array('type' => 'int', 'default' => null, 'comment' => 'Used when EshopWishlist.from_cart is 1'),
        'attributes' => array('type' => 'text', 'default' => null, 'comment' => 'Json encoded product attributes. Used when EshopWishlist.from_cart is 1'),
        'watchdog' => array('type' => 'boolean', 'default' => 0, 'comment' => 'If the product is put to the wishlist as unavailable then watchdog is set to 1 and as soon as the product becomes available there is an email sent to client'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
}
