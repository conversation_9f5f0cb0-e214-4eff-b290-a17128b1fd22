<?php

class EshopAuthor extends EshopModel {
    protected $table = 'run_eshop_authors';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'slug' => array('type' => 'varchar', 'index' => 'index'),
        'seo_title' => array('type' => 'varchar', 'default' => null),
        'seo_description' => array('type' => 'varchar', 'default' => null),
        'seo_keywords' => array('type' => 'varchar', 'default' => null),
        'name' => array('type' => 'varchar', 'index' => 'index'),
        'description' => array('type' => 'text', 'default' => null),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
    );
    
    protected $translatedFields = array(
        'seo_title',
        'seo_description',
        'seo_keywords',
        'description',
    );
    
    protected $nameField = 'name';
    
    public function __construct() {
        parent::__construct();
        
        $this->validations = array(
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                ),
            ),  
            'slug' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Slug is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Slug is mandatory'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Slug must be unique'),
                ),
            ),                        
        );
    }
    
    /**
     * Normalizes data 
     * 
     * @param array $data
     * @param string $options Optional. 
     *      - 'on' (string) Possible values are 'create', 'update', NULL
     *      - 'alternative' (string) Possible values are 'search', NULL
     *      - 'avoidSlugs' (array) Slugs which should not be used. Defaults to empty array.
     * 
     * @return array
     */
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
            'avoidSlugs' => array(),
        );
        $options = array_merge($defauts, $options);
        
        /**
         * Save normalization
         */
        if (
            empty($data['slug'])
            && (
                $options['on'] === 'create'
                || array_key_exists('slug', $data) 
            )
        ) {
            $data['slug'] = Sanitize::value($data['name']);
        }
        
        if (!empty($data['slug'])) {
            if ($options['on'] == 'create') {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                    'avoidValues' => &$options['avoidSlugs'],
                ));
            }
            else {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                    'id' => Sanitize::value($data['id']),
                    'avoidValues' => &$options['avoidSlugs'],
                ));
            }
        }
        
        if (
            $options['on'] === 'create'
            && empty($data['seo_title']) 
            && !empty($data['name'])
        ) {
            $data['seo_title'] = $data['name'];
        }
                       
        return parent::normalize($data, $options);
    }    
    
    /**
     * Ensures the existence of author specified by name. In the case that 
     * author does not exist, it is created in DB or added to batch (see input $newBatch).
     * 
     * Used mostly for imports
     * 
     * @staticvar array $existing Internal cache of existing authors
     * @staticvar array $slugs Internal cache of new authors slugs added to batch
     * @staticvar array $newId Internal cache of new author id added to batch
     * 
     * @param string $name Author name to ensure existence of
     * @param array& $newBatch Optional, passed by reference, auxiliary output. 
     *      Array of new author records to be used in batch save. If not provided then new 
     *      authors are directly created in DB. Defaults to NULL (not provided).
     * 
     * @return int|bool Id of author having given $name. It does not matter if 
     *      author has existed or it has been created od added to batch. Returns 
     *      FALSE on $name validation error.
     */
    public function ensure($name, &$newBatch = null) {
        // internal cache of existing authors 
        static $existing = null;
        // internal chache of created slugs
        static $slugs = array();
        // internal cache of new record id for case of batch records
        static $newId = null;
        
        // load existing authors if not loaded yet
        if ($existing === null) {
            $itemsResource = $this->find(array(
                'fields' => array(
                    'EshopAuthor.id',
                    'EshopAuthor.name',
                ),
                'resource' => true,
            ));
            $existing = array();
            while ($item = DB::fetchArray($itemsResource)) {
                if ($item['name'] === '' || $item['name'] === null) {
                    continue;
                }
                $normalizedName = Str::normalizeForNonstrictComparison($item['name']);
                if ($normalizedName === '') {
                    continue;
                }
                if (!isset($existing[$normalizedName])) {
                    $existing[$normalizedName] = $item['id'];
                }
                else {
                    $existing[$item['name']] = $item['id'];
                }
            }
        }
        // load newId if not loaded yet
        if (is_array($newBatch) && $newId === null) {
            $newId = $newId = $this->getNextId();
        }
        // check for author existence
        if (isset($existing[$name])) {
            return $existing[$name];
        }
        $normalizedName = Str::normalizeForNonstrictComparison($name);
        if (isset($existing[$normalizedName])) {
            return $existing[$normalizedName];
        }
        // create new author
        $new = array(
            'name' => $name,
            'slug' => null,
            'created' => null,
            'modified' => null,
        );
        $new = $this->normalize($new, array(
            'on' => 'create',
            'avoidSlugs' => $slugs,
        ));
        if (
            !$this->validate($new, array(
                'on' => 'create',
                'normalize' => false,
                'allowFields' => array('name'),
            ))
        ) {
            return false;
        }
        // if batch array provided then put new record there
        if (is_array($newBatch)) {
            $new['id'] = $newId;
            $newBatch[] = $new;
            // store created slugs into cache in case of batch 
            $slugs[] = $new['slug'];
            $existing[$normalizedName] = $newId;
            $newId++;
        }
        // if no batch array provided then save it in DB
        else {
            $new = $this->save($new, array(
                'create' => true,
                'normalize' => false,
                'validate' => false,
            ));
            $existing[$normalizedName] = $new['id'];
            $newId = null;
        }
        // return id of author
        return $existing[$normalizedName];
    }

    /**
     * Unifies products authors by names, e.g. authors "Dominik Dan" and "Dan Dominik" are unified 
     * to one record (the older one of both)
     * 
     * @return bool If TRUE then all products authors has been unified. If FALSE then the process 
     *          has been interrupted.
     */
    public function unify($options = array()) {
        $defaults = array(
            'batchSize' => 10000,
            'microtimeReserve' => 10000,
            'processAmount' => 0, // for debuging, if 0 then all authors are processed
            'unifyAmount' => 0, // for debuging, if 0 then all authors are imported
        );
        $options = array_merge($defaults, $options);
        
        // !!! keep this is synchro with the app/config/database.php > 'tableReservationTimeLimit' => ...
        set_time_limit(600); 
        ini_set('memory_limit', '512M');
        
        // find new added relations between products and authors
        // which are not unified yet
        $nonUnifiedAuthors = $this->findList(array(
            'key' => 'EshopAuthor.id',
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'EshopProductAuthor',
                ),
            ),
            'conditions' => array(
                'EshopProductAuthor.author_unified' => false,
                'EshopAuthor.id !=' => null,
//                array('EshopAuthor.id *~*' => 'Dominik'),  // debug
//                array('EshopAuthor.id *~*' => 'Dan'),      // debug
            ),
            'group' => 'EshopAuthor.id',
            'order' => 'EshopAuthor.id ASC',
            'fields' => array('EshopAuthor.name'),
        ));
        
        // for each on non-unified relations between products and authors find duplicit author 
        // names (if any) and replace them in their relations (with products) by the 
        // first created duplicity of given author. So this can/will touch even already
        // unified relations but normally nothing will change (the first created duplicity
        // of given author will be replaced by first created duplicity of given author)
        // This redundancy helps to keep all relations of given "group of duplicities"
        // even if the first created duplicity changes (is deleted).
        $progress = array(
            'processedCount' => 0,
            'unifiedCount' => 0,
            'skippedCount' => 0,
            'totalCount' => count($nonUnifiedAuthors),
            'interrupted' => null,
            'unifiedAuthors' => array(),
        );
        $batch = array();
        $skippedIds = array();
        // do not use foreach if you need to unset following items in iterated array
        // and need it to apply immediatelly on iterated array, see .../_debug/unsetFollowingItemsInLoop
        reset($nonUnifiedAuthors);
        while (($name = current($nonUnifiedAuthors)) !== false) {
            $id = key($nonUnifiedAuthors);
            next($nonUnifiedAuthors);
            
            // check remaining time - finish the processing before php 
            // would be interrupted by execution time constrain
            if (App::getFreeMicrotime() < $options['microtimeReserve']) {
                $progress['interrupted'] = true;
                break;
            }
            // finish the processing if processAmount or unifyAmount is reached
            if (
                $options['processAmount'] && --$options['processAmount'] == 0 //debug
                || $options['unifyAmount'] && $options['unifyAmount'] == $progress['unifiedCount'] //debug
            ) {
                break;
            }

            $progress['processedCount']++;
            
            $name = $this->normalizeAuthorName($name);
            if (!preg_match('/\w+/', $name)) {
                $skippedIds[] = $id;
                unset($nonUnifiedAuthors[$id]);
                $progress['skippedCount']++;
                continue;
            }
            $nameParts = array_filter(explode(' ', $name));
            if (count($nameParts) <= 1) {
                $skippedIds[] = $id;
                unset($nonUnifiedAuthors[$id]);
                $progress['skippedCount']++;
                continue;
            }
            $conditions = array('id !=' => $id);
            $partsCount = array();
            foreach ($nameParts as $part) {
                if (empty($partsCount[$part])) {
                    $partsCount[$part] = 1;
                    $conditions[] = array('name *~*' => $part);
                }
                else {
                    $partsCount[$part]++;
                }
            }
            App::setSqlLogging(false);
            $duplicitAuthors = $this->find(array(
                'fields' => array('id', 'name'),
                'conditions' => $conditions,
            ));
            App::setSqlLogging();
            if (empty($duplicitAuthors)) {
                $skippedIds[] = $id;
                unset($nonUnifiedAuthors[$id]);
                $progress['skippedCount']++;
                continue;
            }
            // remove authors with invalid/non-matching names and 
            // find the first created duplicit author
            $duplicitAuthors[] = array(
                'id' => $id,
                'name' => $name,
            );
            $newId = $id;
            $newName = $name;
            foreach ($duplicitAuthors as $i => $duplicitAuthor) {
                $duplicitName = $this->normalizeAuthorName($duplicitAuthor['name']);
                if (strlen($name) !== strlen($duplicitName)) {
                    unset($duplicitAuthors[$i]);
                    continue;
                }
                foreach ($partsCount as $part => $count) {
                    if (
                        $count !== (
                            preg_match_all(
                                sprintf('/\s%s\s/i', preg_quote($part, '/')), 
                                $duplicitName
                            )
                            + preg_match_all(
                                sprintf('/^%s\s/i', preg_quote($part, '/')), 
                                $duplicitName
                            )
                            + preg_match_all(
                                sprintf('/\s%s$/i', preg_quote($part, '/')), 
                                $duplicitName
                            )
                            + preg_match_all(
                                sprintf('/^%s$/i', preg_quote($part, '/')), 
                                $duplicitName
                            )
                        )
                    ) {
                        unset($duplicitAuthors[$i]);
                        continue 2;
                    }
                }
                if ($duplicitAuthor['id'] < $newId) {
                    $newId = $duplicitAuthor['id'];
                    $newName = $duplicitAuthor['name'];
                }
            }
            if (count($duplicitAuthors) === 1) {
                $skippedIds[] = $id;
                unset($nonUnifiedAuthors[$id]);
                $progress['skippedCount']++;
                continue;
            }
            // create batch records
            foreach ($duplicitAuthors as $duplicitAuthor) {
                if ((int)$duplicitAuthor['id'] !== (int)$newId) {
                    // use old_id as key to be sure that only unique old_id-s are added
                    $batch[$duplicitAuthor['id']] = array( 
                        'old_id' => $duplicitAuthor['id'],
                        'new_id' => $newId,
                    );
                    $progress['unifiedAuthors'][] = sprintf(
                        "#%s: %s (%s) -> %s (%s)\n",
                        $progress['processedCount'],
                        $duplicitAuthor['name'],    
                        $duplicitAuthor['id'],
                        $newName,
                        $newId
                    );
                    $progress['unifiedCount']++;
                }
                unset($nonUnifiedAuthors[$duplicitAuthor['id']]);
            }
            if (count($batch) >= $options['batchSize']) {
                $this->saveUnifyBatch($batch, $skippedIds);
                $batch = array();
                $skippedIds = array();
            }
        }
        //$progress['batch'] = $batch; //debug
        $this->saveUnifyBatch($batch, $skippedIds);
        
        return $progress;
    }
    
    protected function normalizeAuthorName($name) {
        $name = preg_replace('/\.+/', '. ', $name);
        $name = preg_replace('/\-+/', ' - ', $name);
        $name = preg_replace('/\s+/', ' ', $name);
        $name = trim($name);
        return $name;
    }
    
    /**
     * Saves batch created internally in EshopAuthor::unify()
     * 
     * @param array& $batch
     * @param array& $skippedIds
     */
    protected function saveUnifyBatch(&$batch, &$skippedIds) {
        $this->loadModel('EshopProductAuthor');
        $ProductAuthor = new EshopProductAuthor();
        App::setSqlLogging(false);
        if (!empty($batch)) {
            // - drop and create the batch table
            $batchTable = '_' . $this->table . '_unify_batch';
            try {
                DB::dropTables($batchTable);
            } 
            catch (Throwable $e) {
                // ignore exception if table does not exist
            }
            DB::createTable($batchTable, array(
                'old_id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
                'new_id' => array('type' => 'int', 'index' => 'index'),
            ));
            // - insert batch records
            DB::insert($batchTable, $batch, array(
                'multiple' => true,
            ));
            // - update product authors by batch records
            $productAuthorTable = $ProductAuthor->getPropertyTable();
            DB::update(
                $batchTable,
                array(
                    'old.run_eshop_authors_id' => '`new`.`new_id`',
                ),
                array(
                    'alias' => 'new',
                    'joins' => array(
                        array(
                            'type' => 'left',
                            'table' => $productAuthorTable,
                            'alias' => 'old',
                            'conditions' => array(
                                'old.run_eshop_authors_id = new.old_id'
                            )
                        )
                    ),
                    'conditions' => array(
                        'old.id !=' => null,
                    ),
                    'literals' => array(
                        'fields' => array('old.run_eshop_authors_id')
                    )
                )
            );
            DB::dropTables($batchTable);
            // mark unified product authors
            $newIds = array();
            foreach($batch as $i => $item) {
                $newIds[$item['new_id']] = $item['new_id'];
                unset($batch[$i]);
            }
            $ProductAuthor->update(
                array(
                    'author_unified' => true,
                ),
                array(
                    'conditions' => array(
                        'run_eshop_authors_id' => $newIds,
                    )
                )
            );
        }
        if (!empty($skippedIds)) {
            // mark skipped (non duplicit) product authors also as unified
            $ProductAuthor->update(
                array(
                    'author_unified' => true,
                ),
                array(
                    'conditions' => array(
                        'run_eshop_authors_id' => $skippedIds,
                    )
                )
            );
        }
        App::setSqlLogging();
    }
    
    /**
     * @inheritdoc
     * 
     * Overrides Model::save() to allow:
     * - URL redirection if slug is changed
     * 
     * @param array $data
     * @param array $options Model::save() options plus following are available
     *      - 'addRedirection' (bool) Adds redirection on change of slug. Defaults to !ON_LOCALHOST.
     */
    public function save($data, $options = array()) {
        $options = array_merge(array(
            'addRedirection' => !ON_LOCALHOST,
        ), $options);
        // get old record for redirection addition here below
        $oldRecord = array();
        if (
            $options['addRedirection']
            && !empty($data['id'])
            && array_key_exists('slug', $data)
        ) {
            $oldRecord = $this->findFirstBy('id', $data['id'], array(
                'fields' => array($this->name . '.slug'),
                'lang' => Sanitize::value($options['lang']),
            ));
        }
        // save
        $result = parent::save($data, $options);
        if (!$result) {
            return false;
        }
        // add redirection if slug was changed
        if (
            $options['addRedirection']
            && !empty($oldRecord['slug'])
            && !empty($result['slug'])
            && $oldRecord['slug'] != $result['slug']
        ) {
            $webContentLocator = App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor', array(
                'lang' => Sanitize::value($options['lang']),
            ));
            $oldLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($oldRecord['slug']),
            ));
            $newLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($result['slug']),
            ));
            App::loadModel('App', 'UrlRedirection');
            $UrlRedirection = new UrlRedirection();
            $UrlRedirection->add($oldLocator, $newLocator);
        }
        return $result;
    }        
}
