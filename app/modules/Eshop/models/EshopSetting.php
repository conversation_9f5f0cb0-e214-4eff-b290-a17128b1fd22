<?php
App::loadModel('App', 'Setting');
class EshopSetting extends Setting {
    
    public function __construct() {
        parent::__construct();
        
        // VALIDATIONS
        // 
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        // !!! ATTENTION: Setting names must be provided here with pathSeparator (/) instead of dots !!!
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        $this->validations = array(
            'Eshop/email/from' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím e-mailovú adresu'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím e-mailovú adresu'),
                ),
                array(
                    'rule' => 'email',
                    'message' => __v(__FILE__, 'Zadajte prosím platnú e-mailovú adresu'),
                ),
            ),
            'Eshop/email/cc' => array(
                array(
                    'rule' => 'email',
                    'message' => __v(__FILE__, 'Zadajte prosím e-mailovú adresu'),
                ),
            ),
            'Eshop/email/bcc' => array(
                array(
                    'rule' => function($value) {
                        $emails = array_map('trim', explode(',', $value));
                        foreach ($emails as $email) {
                            if (!Validate::email($email)) {
                                return false;
                            }
                        }
                        return true;
                    },
                    'message' => __v(__FILE__, 'Zadajte prosím e-mailovú adresu alebo čiarkou oddelený zoznam e-mailových adries'),
                ),
            ),
            'Eshop/address/companyFullname' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please company name'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please company name'),
                ),
            ),
            'Eshop/address/street' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please street and house number/apartment'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please street and house number/apartment'),
                ),
                array(
                    'rule' => 'address',
                    'message' => __v(__FILE__, 'Provide please street and house number/apartment'),
                ),
            ),
            'Eshop/address/city' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please city'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please city'),
                ),
            ),
            'Eshop/address/zip' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please zip'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please zip'),
                ),
            ),
            'Eshop/address/country' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Select please country'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Select please country'),
                ),
            ),
            'Eshop/mrp/serverIpAddress' => array(
                array(
                    'rule' => 'notEmptyIfMrp',
                    'force' => true,
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                array(
                    'rule' => '/^[0-2]?[0-9]{1,2}\.[0-2]?[0-9]{1,2}\.[0-2]?[0-9]{1,2}\.[0-2]?[0-9]{1,2}$/',
                    'message' => __v(__FILE__, 'Zadajte platnú IP adresu'),
                ),
            ),
            'Eshop/mrp/serverPort' => array(
                array(
                    'rule' => 'notEmptyIfMrp',
                    'force' => true,
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                array(
                    'rule' => 'intNumber',
                    'message' => __v(__FILE__, 'Zadajte platné číslo portu od 0 do 65536'),
                ),
                array(
                    'rule' => array('between', 0, 65536),
                    'message' => __v(__FILE__, 'Zadajte platné číslo portu od 0 do 65536'),
                ),
            ),
            'Eshop/EshopProduct/discountPriceDecimals' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                array(
                    'rule' => 'intNumber',
                    'message' => __v(__FILE__, 'Zadajte celé číslo'),
                ),
                array(
                    'rule' => array('gte', 0),
                    'message' => __v(__FILE__, 'Zadajte celé číslo väčšie alebo rovné nule'),
                ),
            ),          
            'Eshop/mrp/privateKey' => array(
                array(
                    'rule' => 'notEmptyIfMrp',
                    'force' => true,
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
            ),
            'Eshop/EshopProduct/importedProductsMinMarkupRate' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                array(
                    'rule' => function($value, $field, $data, &$validation) {
                        $this->loadModel('EshopSupplierProduct');
                        $SupplierProduct = new EshopSupplierProduct();
                        try {
                            // validate syntax and that each supplier has defined min markup rate
                            $schema = $SupplierProduct->getPropertySchema();
                            foreach ($schema['supplier_pid']['values']  as $supplierPid) {
                                $SupplierProduct->getMinMarkupRate(array(
                                    'supplierPid' => $supplierPid,
                                    'exceptionOnFailure' => true,
                                    'minMarkupRateRawValue' => $value,
                                ));
                            }
                        } catch (Exception $e) {
                            $validation['message'] = $e->getMessage();
                            return false;
                        }
                        return true;
                    },
                ),
            ),          
            'Eshop/EshopProductCategory/sortProductsLimit' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                array(
                    'rule' => 'intNumber',
                    'message' => __v(__FILE__, 'Zadajte celé číslo'),
                ),
                array(
                    'rule' => array('gte', 0),
                    'message' => __v(__FILE__, 'Zadajte celé číslo väčšie alebo rovné nule'),
                ),
            ),
        );
    }
    
    public function normalize($data, $options = array()) {
        if (!empty($data['Eshop/EshopProduct/importedProductsMinMarkupRate'])) {
            $data['Eshop/EshopProduct/importedProductsMinMarkupRate'] = 
                str_replace(',', '.', $data['Eshop/EshopProduct/importedProductsMinMarkupRate']);
        }
        if (
            array_key_exists('Eshop/EshopVouchers/admin_print/template', $data)
            && empty($data['Eshop/EshopVouchers/admin_print/template'])
        ) {
            $data['Eshop/EshopVouchers/admin_print/template'] = App::loadView('Eshop', 'EshopVouchers/admin_print');
        }
        return $data;
    }
    
    /**
     * Updates settings provided in list '{settingName}' => '{value}'. Setting name 
     * is created from setting  module name and pid, e.g. Eshop/email/from.
     * 
     * NOTE: Some settings changes can imply some additional actions e.g. files 
     * (re)generation in case of termsAndConditions settings.
     * 
     * @param array $data Settings data
     * @param array $options Model::update() options. Defaults to empty array().
     * 
     * @return boolean TRUE on success. FALSE on validation failure
     */
    public function updateByName($data, $options = array()) {
        $importedProductsMinMarkupRate = $this->getSetting('EshopProduct.importedProductsMinMarkupRate');
        $allowExternalSearch = $this->getSetting('EshopProduct.allowExternalSearch');
        if (!parent::updateByName($data, $options)) {
            return false;
        }
        
        // treat special cases
        $this->generateTermsAndConditionsFile();
        $this->generateContractWithdrawalFile();
        
        if ($importedProductsMinMarkupRate != $this->getSetting('EshopProduct.importedProductsMinMarkupRate')) {
            App::loadModel('Eshop', 'EshopSupplierProduct');
            $SupplierProduct = new EshopSupplierProduct();
            $SupplierProduct->synchronize(array(
                'updatedOnly' => false,
            ));
        }
        if (
            !$allowExternalSearch
            && $this->getSetting('EshopProduct.allowExternalSearch')
        ) {
            App::loadModel('Eshop', 'EshopProductMeiliSearch');
            $ProductSearch = new EshopProductMeiliSearch();
            $ProductSearch->synchronize(array(
                'async' => true,
            ));
        }
        
        return true;
    }
    
    /**
     * Generates pdf for terms and conditions
     */
    public function generateTermsAndConditionsFile() {
        $termsAndConditions = $this->getSetting('EshopOrder.termsAndConditions');
        $termsAndConditionsFile = $this->getSetting('EshopOrder.termsAndConditionsFile');
        if (
            !empty($termsAndConditions)
            && !empty($termsAndConditionsFile)
        ) {
            File::createPdf(App::loadTextSnippets($termsAndConditions), array(
                'output' => 'file',
                'name' => 'userfiles/files/' . File::normalizePath($termsAndConditionsFile, 'pdf'),
                'marginBottom' => 10,
            ));
        }   
    }
    
    /**
     * Generates pdf for contract withdrawal
     */
    public function generateContractWithdrawalFile() {
        $contractWithdrawal = $this->getSetting('EshopOrder.contractWithdrawal');
        $contractWithdrawalFile = $this->getSetting('EshopOrder.contractWithdrawalFile');
        if (
            !empty($contractWithdrawal)
            && !empty($contractWithdrawalFile)
        ) {
            File::createPdf(App::loadTextSnippets($contractWithdrawal), array(
                'output' => 'file',
                'name' => 'userfiles/files/' . File::normalizePath($contractWithdrawalFile, 'pdf'),
                'marginBottom' => 10,
            ));
        }        
    }    
    
    public function validate_notEmptyIfMrp($fieldValue, $fieldName, $data) {
        if (
            (
                !empty($data['Eshop/mrp/serverIpAddress']) 
                || !empty($data['Eshop/mrp/serverPort'])
                || !empty($data['Eshop/mrp/privateKey'])
            ) && empty($fieldValue)
        ) {
            return false;
        }
        return true;
    }
}
