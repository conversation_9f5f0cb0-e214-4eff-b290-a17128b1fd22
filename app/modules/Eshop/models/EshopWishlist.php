<?php
class EshopWishlist extends Model {
    protected $table = 'run_eshop_wishlists';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_users_id' => array('type' => 'int', 'default' => null, 'index' => 'index', 'comment' => 'Used for wishlists (available only to registerd users)'),
        'email' => array('type' => 'varchar', 'default' => null, 'index' => 'index', 'comment' => 'Used for availability alerts (available publicly)'),
        'name' => array('type' => 'varchar'),
        'privacy_level' => array('type' => 'enum', 'values' => array('enum_wishlist_private', 'enum_wishlist_shared', 'enum_wishlist_public'), 'default' => 'enum_wishlist_private'),
        'access_token' => array('type' => 'varchar', 'length' => 40, 'comment' => 'Access token for wishlists with shared and public privacy level'),
        'from_cart' => array('type' => 'boolean', 'default' => 0, 'comment' => 'Was the wishlist created from cart?'),
        'default' => array('type' => 'boolean', 'default' => 0, 'comment' => 'Products are added to default wishlist. Only one of users wishlists can be set as default'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
    );
    
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(
            'run_users_id' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Wishlist must belong to an user'),
                    'on' => 'create', 
                    'alternative' => 'wishlist'
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Wishlist must belong to an user'),
                    'alternative' => 'wishlist'
                ),
            ),
            'email' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Enter'),
                    'on' => 'create', 
                    'alternative' => 'availabilityAlert'
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Enter'),
                    'alternative' => 'availabilityAlert'
                ),
            ),
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Wishlist name is mandatory'),
                    'on' => 'create', 
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Wishlist name is mandatory'),
                ),
            ),
            'privacy_level' => array(
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Privacy level is mandatory'),
                ),
                array(
                    'rule' => 'privacyLevel',
                    'message' => __v(__FILE__, 'Invalid privacy level'),
                ),
            ),
        );
    }       
    
    public function validate_privacyLevel($fieldValue) {
        return array_key_exists($fieldValue, $this->getEnumValues('privacy_level'));
    }
    
    /**
     * Adds availability alert of specified product for provided email 
     * 
     * @param int $productId Id of product to add to user wishlist
     * @param string $email Email used to identify wishlists of anonymous users.
     *      If not provided (empty) than it is set to email of logged user (if any).
     * 
     * @return boolean|string If the additions fails then FALSE, errors are stored
     *      under key'_processing'. If success then product slug is returned.
     */
    public function addProductAvailabilityAlert($productId, $email) {
        // normalize email
        if (empty($email)) {
            $email = App::getUser('email');
        }
        // validate email
        if (empty($email)) {
            $this->setError(__v(__FILE__, 'Enter an e-mail address'));
            return false;
        }
        elseif (!Validate::email($email)) {
            $this->setError(__v(__FILE__, 'Enter a valid e-mail address'));
            return false;                
        }
        // validate product
        $Product = $this->loadModel('EshopProduct', true);
        if (!($product = $Product->findFirstBy('id', $productId))) {
            $this->setError(__v(__FILE__, 'Invalid product id %s', $productId));
            return false;
        }
        if (!$product['unavailable']) {
            $this->setError(__v(__FILE__, 'Product is available now', $productId));
            return false;            
        }
        
        // get the default user wishlist
        $wishlist = $this->findFirst(array(
            'fields' => array('id'),
            'conditions' => array(
                'run_users_id' => null, // look only for availability alert 
                'email' => $email,
            ),
            'order' => array('default DESC', 'id ASC'),
        ));
        // create wishlist for user if not existing
        if (empty($wishlist)) {
            $this->addWishlist($email, array(
                'name' => __(__FILE__, 'Availability alert list'),
                'default' => true,
            ));
            $wishlistId = $this->id;
        }
        else {
            $wishlistId = $wishlist['id'];
        }
        
        // add new product to wishlist
        $WishlistProduct = $this->loadModel('EshopWishlistProduct', true);
        
        // validate if the product is not already in availability alert list of provide email
        $wishlistProduct = $WishlistProduct->findFirst(array(
            'fields' => array(
                'EshopWishlistProduct.id',
                'EshopWishlist.name AS wishlist_name'
            ),
            'conditions' => array(
                'EshopWishlistProduct.run_eshop_wishlists_id' => $wishlistId,
                'EshopWishlistProduct.run_eshop_products_id' => $productId,
            ),
            'joins' => array(
                array(
                    'model' => 'EshopWishlist',
                    'type' => 'left',
                )
            )
        ));        
        if ($wishlistProduct) {
            $WishlistProduct->save(array(
                'id' => $wishlistProduct['id'],
                'watchdog' => $product['unavailable'],
            ));
        }
        else {
            $WishlistProduct->save(array(
                'run_eshop_wishlists_id' => $wishlistId,
                'run_eshop_products_id' => $productId,
                'watchdog' => $product['unavailable'],
            ));
        }
        
        return $product['slug'];
    }
    
    /**
     * Adds specified product to wishlist of current user 
     * 
     * @param int $productId Id of product to add to user wishlist
     * 
     * @return boolean|string If the additions fails then FALSE, errors are stored
     *      under key'_processing'. If success then product slug is returned.
     */
    public function addProduct($productId) {
        // validate user
        $userId = App::getUser('id');
        if (!$userId) {
            $this->setError(__v(__FILE__, 'Only authenticated user can add products to wishlist'));
            return false;
        }
        // validate product
        $Product = $this->loadModel('EshopProduct', true);
        if (!($product = $Product->findFirstBy('id', $productId))) {
            $this->setError(__v(__FILE__, 'Invalid product id %s', $productId));
            return false;
        }
        
        // get the default user wishlist
        $wishlist = $this->findFirst(array(
            'fields' => array('id'),
            'conditions' => array(
                'run_users_id' => $userId,
            ),
            'order' => array('default DESC', 'id ASC'),
        ));
        // create wishlist for user if not existing
        if (empty($wishlist)) {
            $this->addWishlist($userId, array('default' => true));
            $wishlistId = $this->id;
        }
        else {
            $wishlistId = $wishlist['id'];
        }
        
        // add new product to wishlist
        $WishlistProduct = $this->loadModel('EshopWishlistProduct', true);
        
        // validate if the product is not already in wishlist of user
        $wishlistProduct = $WishlistProduct->findFirst(array(
            'fields' => array(
                'EshopWishlistProduct.id',
                'EshopWishlist.name AS wishlist_name'
            ),
            'conditions' => array(
                'EshopWishlist.run_users_id' => $userId,
                'EshopWishlistProduct.run_eshop_products_id' => $productId,
            ),
            'joins' => array(
                array(
                    'model' => 'EshopWishlist',
                    'type' => 'left',
                )
            )
        ));
        // if not adding a watchdog then inform user that the product is already in
        // a wishlist
        if (
            !$product['unavailable']
            && $wishlistProduct
        ) {
            $this->setError(__v(__FILE__, 'The product %s is already in your wishlist \'%s\'', $product['name'], $wishlistProduct['wishlist_name']));
            return false;
        }
        if ($wishlistProduct) {
            $WishlistProduct->save(array(
                'id' => $wishlistProduct['id'],
                'watchdog' => $product['unavailable'],
            ));
        }
        else {
            $WishlistProduct->save(array(
                'run_eshop_wishlists_id' => $wishlistId,
                'run_eshop_products_id' => $productId,
                'watchdog' => $product['unavailable'],
            ));
        }
        
        return $product['slug'];
    }
    
    public function deleteProduct($productId, $userId) {
        $wishlistId = $this->findList(array(
            'fields' => array('id'),
            'conditions' => array('run_users_id' => $userId),
        ));
        $WishlistProduct = $this->loadModel('EshopWishlistProduct', true);
        $WishlistProduct->delete(array(
            'conditions' => array(
                'run_eshop_products_id' => $productId,
                'run_eshop_wishlists_id' => $wishlistId,
            )
        ));
    }
    
    /**
     * Creates new wishlist
     * 
     * @param int|string $userIdOrEmail User id (integer) to create wishlist for. 
     *          In case of anonymous wishlist an email (string) can be provided here
     * 
     * @param array $data
     * 
     * @return array|bool
     */
    public function addWishlist($userIdOrEmail, $data = array()) {
        unset($data['id']);
        if (Validate::intNumber($userIdOrEmail)) {
            $data['run_users_id'] = $userIdOrEmail;
            $alternative = 'wishlist';
        }
        else {
            $data['email'] = $userIdOrEmail;
            $alternative = 'availabilityAlert';
        }
        $data['access_token'] = $this->generateAccessToken();
        if (empty($data['name'])) {
            $data['name'] = __(__FILE__, 'My wishlist');
        }
        if (($valid = $this->save($data, array(
            'alternative' => $alternative
        )))) {
            if (
                !empty($data['default']) 
                && $alternative === 'wishlist'
            ) {
                $this->update(
                    array('default' => 0),
                    array('conditions' => array(
                        'run_users_id' => $userIdOrEmail, 
                        'id !=' => $this->id
                    ))
                );
            }
        }
        return $valid;
    }
    
    public function updateWishlist($userId, $data = array()) {
        // normalize data
        $data['user_id'] = $userId;
        unset($data['access_token']);
        // save
        $valid = true;
        if ($this->isOwnedByUser($data['id'], $userId)) {
            if (($valid = $this->save($data, array(
                'alternative' => 'wishlist'
            )))) {
                if (!empty($data['default'])) {
                    $this->update(
                        array('default' => 0),
                        array('conditions' => array(
                            'run_users_id' => $userId, 
                            'id !=' => $data['id']
                        ))
                    );
                }
            }
        }
        
        return $valid;
    }
    
    public function removeWishlist($userId, $wishlistId) {
        if ($this->isOwnedByUser($wishlistId, $userId)) {
            $WishlistProduct = $this->loadModel('EshopWishlistProduct', true);
            // check if there are any products
            $wishlistProducts = $WishlistProduct->find(array(
                'fields' => array('id'),
                'conditions' => array('run_eshop_wishlists_id' => $wishlistId)
            ));
            if ($wishlistProducts) {
                $this->setError(__v(__FILE__, 'Before deleting the wishlist move or remove all its items'));
                return false;
            }
            $wishlistCount = $this->findCount(array(
                'conditions' => array('run_users_id' => $userId)
            ));
            if ($wishlistCount == 1) {
                $this->setError(__v(__FILE__, 'There must be at least one existing wishlist'));
                return false;
            }
            $this->deleteBy('id', $wishlistId);
        }
        return true;
    }
    
    /**
     * Searches users wishlists by provided user name or email
     * 
     * @param string $keywords Name or email of wishlist owner
     * @return array Array of retrieved wishlists like:
     * 
     *      array(
     *          // user 1
     *          array(
     *              'id' => ...,
     *              'first_name' => ...,
     *              'last_name' => ...,
     *              'address_city' => ...,
     *              'EshopWishlist' =>array(
     *                  // wishlist 1
     *                  array(
     *                      'id' => ...,
     *                      'name' => ...,
     *                      'access_token' => ...,
     *                      'EshopProduct => array(
     *                          // product 1
     *                          array(
     *                              'id' => ...,
     *                              'image' => ...,
     *                          )
     *                          // etc
     *                          ...
     *                      )
     *                  ),
     *                  // wishlist 2
     *                  ...
     *              ),
     *              // all user wishlist products
     *              'EshopProduct => array(
     *                  // product 1
     *                  array(
     *                      'id' => ...,
     *                      'image' => ...,
     *                  )
     *                  // etc
     *                  ...
     *              )
     *          ),
     *          // user 2
     *          array(...),
     *          // etc
     *          ...
     *      )
     */
    public function searchWishlistOwners($keywords) {
        // take the firts word from keywords
        $keywords = explode(' ', $keywords);
        $keyword = reset($keywords);
        // search for wishlist owner user_id
        $User = App::loadModel('App', 'User', true);
        if (Validate::email($keyword)) {
            $conditions = array(
                'User.email LIKE' => $keyword
            );
        }
        else {
            $conditions = array(
                'User.first_name LIKE' => $keyword,
                'OR',
                'User.last_name LIKE' => $keyword,
            );
        }
        $users = $User->findList(array(
            'joins' => array(
                array(
                    'model' => 'UserProfile',
                    'type' => 'left',
                ),
            ),
            'key' => 'User.id',
            'fields' => array(
                'User.id', 
                'User.first_name',
                'User.last_name',
                'UserProfile.city',
            ),
            'conditions' => $conditions
        ));
        if (!$users) {
            return array();
        }
        // get users ids
        $userIds = array_keys($users);
        // search for public wishlists owned by found owners
        $wishlists = $this->find(array(
            'fields' => array('id', 'run_users_id', 'name', 'access_token'),
            'conditions' => array(
                'run_users_id' => $userIds,
                'privacy_level' => 'enum_wishlist_public',
            ),
        ));
        if (!$wishlists) {
            return array();
        }
        // search for products in wishlists and remove wishlists with no products
        $Product = $this->loadModel('EshopProduct', true);
        $WishlistProduct = $this->loadModel('EshopWishlistProduct', true);        
        $tmp = array();
        $allProductIds = array();
        foreach ($wishlists as $wishlist) {
            // get wishlists products
            $products = $WishlistProduct->findList(array(
                'joins' => array(
                    array(
                        'model' => 'EshopProduct',
                        'type' => 'left',
                    )
                ),
                'key' => 'EshopProduct.id',
                'fields' => array(
                    'EshopProduct.id', 
                    'EshopProduct.slug',
                ),
                'conditions' => array(
                    EshopProduct::getPublishedConditions(),
                    'EshopWishlistProduct.run_eshop_wishlists_id' => $wishlist['id'],
                )
            ));
            // skip empty wishlists 
            if (!$products) {
                continue;
            }
            $allProductIds = array_merge($allProductIds, array_keys($products));
            // set products data
            $wishlist['EshopProduct'] = $products;
            $tmp[] = $wishlist;
        }
        $wishlists = $tmp;
        $allProductIds = array_unique($allProductIds);
        // retrieve products details
        $products = $Product->getDetails($allProductIds);
        // merge products details into wishlists products
        foreach ($wishlists as &$wishlist) {
            foreach ($wishlist['EshopProduct'] as $id => &$product) {
                $product = $products[$id];
                if (!empty($products[$id]['image'])) {
                    $product['image'] = $products[$id]['image']['small'];
                }
                else {
                    $product['image'] = null;
                }
            }
        }
        unset($wishlist);
        
        // reindex wishlists by user ids
        $tmp = array();
        foreach ($wishlists as $wishlist) {
            $tmp[$wishlist['run_users_id']][] = $wishlist;
        }
        $wishlists = $tmp;
        
        // set users wishlits, remove users without wishlists
        foreach ($users as $id => &$user) {
            if (empty($wishlists[$id])) {
                unset($users[$id]);
            }
            else {
                $user['EshopWishlist'] = $wishlists[$id];
                $user['EshopProduct'] = array();
                foreach ($user['EshopWishlist'] as $wishlist) {
                    $user['EshopProduct'] = array_merge($user['EshopProduct'], $wishlist['EshopProduct']);
                }
            }
        }
        return $users;
    }
    
    public function updateProduct($productId, $userId, $data) {
        // check for update of wishlist
        if (
            !empty($data['run_eshop_wishlists_id']) 
            && Validate::intNumber($data['run_eshop_wishlists_id'])
        ) {
            // if target wishlist is owned by current user then update
            if ($this->isOwnedByUser($data['run_eshop_wishlists_id'], $userId)) {
                $wishlistId = $this->findListBy('run_users_id', $userId, array(
                    'fields' => array('id')
                ));
                $WishlistProduct = $this->loadModel('EshopWishlistProduct', true); 
                $WishlistProduct->update(
                    array('run_eshop_wishlists_id' => $data['run_eshop_wishlists_id']),
                    array(
                        'conditions' => array(
                            'EshopWishlistProduct.run_eshop_wishlists_id' => $wishlistId,
                            'EshopWishlistProduct.run_eshop_products_id' => $productId,
                        ),
                    )
                );
            }
        }          
        return true;
    }
    
    /**
     * Gets list of translated privacy levels with items like:
     * 
     *      '{privacy_level_enum_value}' => 'Translated privacy level text'
     * 
     * @param bool $commented 
     * @return array
     */
    public function getPrivacyLevels($commented = true) {
        // get privacy levels and translate them
        $privacyLevels = $this->getEnumValues('privacy_level');
        if ($commented) {
            foreach ($privacyLevels as $levelEnum => &$levelLabel) {
                if (
                    $levelEnum == 'enum_wishlist_shared' 
                    || $levelEnum == 'enum_wishlist_public'
                ) {
                    $levelLabel = __(__FILE__, 'yes') . ' (' . $levelLabel . ')';
                }
                else {
                    $levelLabel = __(__FILE__, 'no') . ' (' . $levelLabel . ')';
                }
            }
        }
        return $privacyLevels;
    }
    
    /**
     * Generates unique access code
     * 
     * @return string
     */
    public function generateAccessToken() {
        do {
            $accessToken = sha1(uniqid(mt_rand(), true));
            
        } while(
            $this->findFirst(array(
                'conditions' => array('access_token' => $accessToken)
            ))   
        );
        return $accessToken;
    }
    
    /**
     * Checks if wishlist is owned by user
     * 
     * @param int $wishlistId
     * @param int $userId
     * 
     * @return array If the wishlist is owned by user then the corresponsing
     *      wishist record. Otherwise empty array.
     */
    public function isOwnedByUser($wishlistId, $userId) {
        return $this->findFirst(array(
            'fields' => array('id', 'privacy_level', 'default', 'access_token',  'name'),
            'conditions' => array(
                'id' => $wishlistId,
                'run_users_id' => $userId,
            )
        ));
    }
    
    /**
     * Utility method for creating wishlists for existing users. If user does not have 
     * a wishlist yet, it is created. Comment this out when not needed.
     */
    public function initWishlists() {
        $User = App::loadModel('App', 'User', true);
        $users = $User->findList(array(
            'fields' => array('id')
        ));
        if (empty($users)) {
            return true;
        }
        $wishlists = $this->findList(array(
            'fields' => array('run_users_id')
        ));
        $count = 0;
        foreach ($users as $userId) {
            if (!in_array($userId, $wishlists)) {
                $result = $this->addWishlist($userId, array(
                    'name' => __(__FILE__, 'My wishlist'),
                    'default' => true,
                ));
                if ($result) {
                    $count++;
                } 
                else {
                    App::debug('Failed to create new wishlist for user #' . $userId);
                }
            }
        }
        App::debug("$count wishlists has been created");
        
        // create access_tokens for wishlist without them
        $wishlists = $this->find( array(
            'fields' => array('id', 'access_token')
        ));
        $count = 0;
        foreach ($wishlists as $wishlist) {
            if (empty($wishlist['access_token'])) {
                $this->save(array(
                    'id' => $wishlist['id'],
                    'access_token' => $this->generateAccessToken(),
                ));
                $count++;
            }
        }
        App::debug("$count missing access tokens has been created.");
        return true;
    }     
    
    public function checkWatchdog() {
        $reserveMicrotime = 5000;
        
        // get all wishlist products with set watchdog
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $this->loadModel('EshopWishlistProduct');
        $WishlistProduct = new EshopWishlistProduct();
        $wishlistItems = $WishlistProduct->find(array(
            'conditions' => array(
                'EshopWishlistProduct.watchdog' => true,
            ),
            'fields' => array(
                'EshopWishlistProduct.id',
                'EshopWishlistProduct.run_eshop_wishlists_id',
                'EshopWishlistProduct.run_eshop_products_id',
            ),
        ));
        // get product details (to have actual disponibility)
        $productIds = array();
        foreach ($wishlistItems as $wishlistItem) {
            $productIds[] = $wishlistItem['run_eshop_products_id'];
        }
        $products = $Product->getOrderable($productIds, array(
            'additionalFields' => array('name', 'slug'),    
        ));
        // send email for items which are already available
        //$wishlistViewSlug = App::getContentLocatorByPid('Eshop.EshopWishlists.view');
        $wishlistIndexProductsSlug = App::getContentLocatorByPid('Eshop.EshopWishlists.indexProducts');
        $productViewSlug = App::getContentLocatorByPid('Eshop.EshopProducts.view');
        $emailBody = $this->getSetting('EshopWishlist.msgBodyWishlistWatchdog');
        $emailTextBody = Sanitize::htmlToText($emailBody);
        $emailSubject = $this->getSetting('EshopWishlist.msgSubjectWishlistWatchdog');
        foreach ($wishlistItems as $item) {
            // check the microtime - what is not send now it will be send the next time, it is not so urgent
            if (App::getFreeMicrotime() < $reserveMicrotime) {
                return;
            }
            // if product is not orderable then skip it
            if (empty($products[$item['run_eshop_products_id']])) {
                continue;
            }
            $product = $products[$item['run_eshop_products_id']];
            // get wishlist and user data
            $wishlist = $this->findFirst(array(
                'joins' => array(
                    array(
                        'module' => 'App',
                        'model' => 'User',
                        'type' => 'left',
                    ),
                ),
                'fields' => array(
                    'EshopWishlist.id',
                    'EshopWishlist.run_users_id',
                    'EshopWishlist.name',
                    'EshopWishlist.privacy_level',
                    'EshopWishlist.access_token',
                    'EshopWishlist.email AS wishlist_email',
                    'User.email AS user_email',
                    'User.first_name',
                    'User.last_name',
                ),
                'conditions' => array(
                    'EshopWishlist.id' => $item['run_eshop_wishlists_id'],
                )
            ));
            // prepare email inserts
            $wishlistUrl = 'javascript:void(0)';
//            if ($wishlist['privacy_level'] !== 'enum_wishlist_private') {
//                $wishlistUrl = App::resolveUrl(array(
//                    'locator' => $wishlistViewSlug,
//                    'args' => array($wishlist['access_token']),
//                    'absolute' => true,
//                ));
//            }
//            else
            if ($wishlist['run_users_id']) {
                $wishlistUrl = App::getUrl(array(
                    'locator' => $wishlistIndexProductsSlug,
                    'args' => array($wishlist['id']),
                    'absolute' => true,
                ));
            }
            $inserts = array(
                'eshopName' => App::getSetting('App', 'name'),
                'userName' => trim($wishlist['first_name'] . ' ' . $wishlist['last_name']),
                'wishlistName' => $wishlist['name'],
                'wishlistUrl' => $wishlistUrl,
                'wishlistLink' => '<a href="' . $wishlistUrl . '">' . $wishlist['name'] . '</a>',
                'productName' => $product['name'],
                'productUrl' => ($productUrl = App::getUrl(array(
                    'locator' => $productViewSlug,
                    'args' => array($product['slug']),
                    'absolute' => true,
                ))),
                'productLink' => '<a href="' . $productUrl . '">' . $product['name'] . '</a>',
            );
            // send email
            if ($wishlist['user_email']) {
                $email = $wishlist['user_email'];
            }
            else {
                $email = $wishlist['wishlist_email'];
            }
            $error = __(__FILE__, 'Unknown error');
            try {
                $result = App::sendEmail(
                     $emailBody,
                     $email,
                     array(
                        'subject' => $emailSubject,
                        'textBody' => $emailTextBody,
                        'inserts' => $inserts,
                        'embedImages' => true,
                     )
                );
            } 
            catch (Throwable $e) {
                $result = false;
                $error = $e->getMessage();
            }
            if (!$result) {
                App::logError('The wishlist watchdog email send has failed', array(
                    'var' => array(
                        'wishlist' => $wishlist,
                        'wishlistProduct' => $item,
                        'error' => $error,
                    ),
                    'email' => true,
                ));
            }
            // if the email is succesfully sent then turn off the watchdog
            else {
                $WishlistProduct->save(array(
                    'id' => $item['id'],
                    'watchdog' => false,
                ));
            }
        }
    }        
    
    
}