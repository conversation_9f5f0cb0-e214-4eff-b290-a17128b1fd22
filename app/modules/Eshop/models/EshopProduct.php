<?php

class EshopProduct extends EshopModel {
    protected $table = 'run_eshop_products';

    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'parent_id' => array('type' => 'int', 'default' => null, 'comment' => 'Relation between base product and its variants'),
        'code' => array('type' => 'varchar', 'length' => 50, 'default' => null, 'index' => 'index', 'comment' => 'Internal code of product (catalogue number, ...)'),
        'mrp_code' => array('type' => 'int', 'default' => null, 'index' => 'index', 'comment' => 'Product code in MRP'),
        'mrp_name' => array('type' => 'varchar', 'length' => 255, 'default' => null, 'comment' => 'Product name in MRP'),
        'ean' => array('type' => 'varchar', 'length' => 14, 'default' => null, 'index' => 'index'),
        'slug' => array('type' => 'varchar', 'index' => 'index'),
        'seo_title' => array('type' => 'tinytext', 'default' => null),
        'seo_description' => array('type' => 'tinytext', 'default' => null),
        'seo_keywords' => array('type' => 'tinytext', 'default' => null),
        'name' => array('type' => 'varchar', 'index' => 'index'),
        'description' => array('type' => 'text', 'default' => null),
        'short_description' => array('type' => 'text', 'default' => null),
        'image' => array('type' => 'tinytext', 'default' => null,  'comment' => 'Main image filename for product detail'),
        'video_url' => array('type' => 'varchar', 'default' => null, 'comment' => 'Relative or absolute URL to video file or URL to youtube or vimeo video'),
        'run_eshop_brands_id' => array('type' => 'int', 'default' => null, 'index' => 'index'),
        'run_eshop_manufacturers_id' => array('type' => 'int', 'default' => null, 'index' => 'index'),
        'run_eshop_manufacturer_ranges_id' => array('type' => 'int', 'default' => null, 'index' => 'index'),
        'manufacturer_group' => array('type' => 'enum', 'default' => null, 'values' => array('ALBATROS','IKAR','INFORM','PARTNERTECHNIC','MATICA','SLOVART')),
        'run_eshop_product_types_id' => array('type' => 'int', 'default' => null, 'index' => 'index'),
        'is_additional_service' => array('type' => 'enum', 'default' => null, 'values' => array('enum_gift_package'), 'comment' => 'Is this product an additional service to other products? If yes than which additional service?'),
        'is_gift_card' => array('type' => 'bool', 'default' => 0, 'comment' => 'Is this a gift card product?'),
        'price' => array('type' => 'decimal', 'length' => 8.2, 'default' => null, 'comment' => 'Maximum price is 999999.99'),
        'discount_price' => array('type' => 'decimal', 'length' => 8.2, 'default' => null),
        'discount_rate' => array('type' => 'decimal', 'length' => 8.2, 'default' => null, 'comment' => 'Discount percents. If this is used then discount_price should be calculated by trigger'),
        'discount_from' => array('type' => 'date', 'default' => null),
        'discount_to' => array('type' => 'date', 'default' => null),
        'tax_rate' => array('type' => 'enum', 'values' => array(0, 10, 20), 'default' => 20, 'comment' => 'VAT percents'),
        'synchronize_price_with_suppliers' => array('type' => 'bool', 'default' => 1, 'comment' => 'Should be price set according to prices of suppliers products?'),
        'stock' => array('type' => 'int', 'default' => 0),
        'units' => array('type' => 'enum', 'values' => array('enum_piece', 'enum_meter', 'enum_kilogram'), 'default' => 'enum_piece'),
        'stock_reserved' => array('type' => 'int', 'default' => 0, 'comment' => 'Number of reserved units in MRP. Used to actualize reserved amounts of order products to keep user informed about his/her order.'),
        'stock_location_code' => array('type' => 'tinytext', 'default' => null),
        'shop_location_code' => array('type' => 'varchar', 'length' => 10, 'default' => null),
        'supplier_info' => array('type' => 'varchar', 'default' => null, 'length' => 20, 'comment' => 'Info about product supplier (loaded from MRP)'),
        'supplier_pid' => array('type' => 'varchar', 'default' => null, 'length' => 30, 'comment' => 'Actual product supplier pid set by synchronization with supplier products'),
        'availability' => array('type' => 'enum', 'default' => 'enum_available', 'values' => array('enum_presale', 'enum_available', 'enum_on_demand', 'enum_soldout')),
        'long_delivery_time' => array('type' => 'bool', 'default' => 0, 'comment' => 'Is the product available but it can take a long time to deliver it?'),
        'reprint' => array('type' => 'bool', 'default' => 0, 'comment' => 'Is this a reprint of book?'),
        'available_from' => array('type' => 'date', 'default' => null, 'comment' => 'Date when the product gets available. Used especially in case of availability enum_presale.'),
        'unavailable' => array('type' => 'bool', 'default' => 0, 'comment' => 'Updated by trigger. 1 if the availability is enum_soldout and stock is 0. Serves for ordering purposes to put unavailable products as the last retrieved'),
        'shipment_time_off_stock' => array('type' => 'tinyint', 'length' => 3, 'default' => null, 'comment' => 'Days of shipment in case that the product is not in stock. If NULL then setting Eshop.product.shipmentTimeOffStock is used'),
        'weight' => array('type' => 'tinytext', 'default' => null, 'comment' => 'Weight in units according to setting EshopProduct.weightUnits'),
        'width' => array('type' => 'float', 'length' => 7.3, 'default' => null, 'comment' => 'Width in units according to setting EshopProduct.dimensionsUnits'),
        'height' => array('type' => 'float', 'length' => 7.3, 'default' => null, 'comment' => 'Height in units according to setting EshopProduct.dimensionsUnits'),
        'length' => array('type' => 'float', 'length' => 7.3, 'default' => null, 'comment' => 'Length in units according to setting EshopProduct.dimensionsUnits'),
        'dimensions' => array('type' => 'tinytext', 'default' => null),
        'note' => array('type' => 'text', 'default' => null, 'comment' => 'Internal note'),
        'active' => array('type' => 'bool', 'default' => 0),
        'heureka_category_name' => array('type' => 'tinytext', 'default' => null, 'comment' => 'Category name used to create content of CATEGORYTEXT tag in feed for Heureka.sk'),
        'new' => array('type' => 'bool', 'default' => 0),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
        'deleted' => array('type' => 'datetime', 'default' => null, 'index' => 'index'),
        'exported' => array('type' => 'datetime', 'default' => null, 'comment' =>  'Has the product been exported to accounting system?'),
        'image_exported' => array('type' => 'datetime', 'default' => null, 'comment' =>  'Has the product image been exported to accounting system?'),
        'translated' => array('type' => 'bool', 'default' => 1, 'comment' => 'Has been the record translated fields translated to corresponding lang version of this field? The default lang field value serves to keep track of default lang field translation in case that default lang texts are imported in other than default lang.'),
        // PROJECT SPECIFIC FIELDS
        'variant' => array('type' => 'text', 'default' => null, 'comment' => 'General attribute to introduce any kind of product variants. Variants should be introduced in such a way that they do not need attribute name to understand them.'),
        'secondary_variant' => array('type' => 'bool', 'default' => 0, 'comment' => 'Is the product a secondary variant (not the main one)?'),
        'variants_lowest_price' => array('type' => 'bool', 'default' => 0, 'comment' => 'Has the product the lowest price in a group of variants?'),
        'variants_common_name'=> array('type' => 'varchar', 'default' => null, 'comment' => 'Common name for all variants in a group of variants. NULL if there are no variants of product. Can be used to identify if product is in a group of variants.'),
        'subtitle' => array('type' => 'varchar', 'length' => 200, 'default' => null),
        'oversized' => array('type' => 'bool', 'default' => 0),
        'color' => array('type' => 'varchar', 'length' => 30, 'default' => null),
        'size' => array('type' => 'varchar', 'length' => 30, 'default' => null),
        'material_color' => array('type' => 'varchar', 'length' => 30, 'default' => null, 'comment' => 'Farba látky'),
        'diameter' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Priemer'),
        'marquee_height' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Pri výške markízy'),
        'extension' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Výsuv'),
        'material' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => ''),
        'sail_width' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Šírka plachty'),
        'in_series' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Na série'),
        'carrying_capacity' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Nosnosť'),
        'pressure' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Tlak'),
        'power' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Výkon'),
        'electric_current' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Prúd'),
        'inner_frame' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Vnútorný rám'),
        'outer_frame' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Vonkajší rám'),
        'roof_thickness' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Hrúbka strechy'),
        'inner_dimension' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Vnútorný rozmer'),
        'volume' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Objem'),
        'voltage' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Napätie'),
        'capacity' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Kapacita'),
        //polia importu Frankana:
        'tech_data_html' => array('type' => 'text', 'default' => null),
        'related_products' => array('type' => 'text', 'default' => null, 'comment' => 'Comma separated list of related product codes. This is populated by imports (e.g. Frankana).'),
    );

    protected $translatedFields = array(
        'slug',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'name',
        'description',
        'short_description',
        'translated',
        'tech_data_html',
    );

    protected $attributeFields = array(
        'variant' => array('selective' => true, 'priced' => true),
    );

    protected $fileFields = array(
        'image' => array(
            'extension' => 'jpg',
            'quality' => 90,
            'placeholder' => '_placeholder.png',
            'variants' => array(
                'tiny' => array(
                    'pngGifToJpg' => array(array(255, 255, 255)),
//                    'fit' => array(220, 220),
//                    'cover' => array(80, 124),
//                    'cropInMiddle' => array(80, 124),
                    'scaleByX' => array(200),
                    'lazy' => true,
                ),
                'small' => array(
                    'pngGifToJpg' => array(array(255, 255, 255)),
//                    'fit' => array(220, 220),
//                    'cover' => array(290, 231),
//                    'cropInMiddle' => array(290, 231),
//                    'scaleByX' => array(400), // largest image is 510px wide (in product index)
                    'fitX' => array(400), // largest image is 510px wide (in product index)
                    'lazy' => true,
                ),
                'large' => array(
                    'pngGifToJpg' => array(array(255, 255, 255)),
//                    'fit' => array(220, 220),
//                    'cover' => array(180, 280),
//                    'cropInMiddle' => array(180, 280),
//                    'scaleByX' => array(600),
                    'fitX' => array(600),
                    'lazy' => true,
                ),
                'original' => array(
                    'source' => true,
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(1920, 1080),
                ),
            ),
        ),
    );

    /**
     * Returns array of conditions for products published on frontend
     *
     * @return array
     */
    static public function getPublishedConditions() {
        return array(
            'EshopProduct.active' => 1,
            'EshopProduct.price >' => 0,
            'EshopProduct.translated' => 1,
        );
    }

    /**
     * Returns array of conditions for normal (non special) products.
     * Following products are excluded:
     * - additional services (gift package, ...)
     * - vouchers with absolute discount
     * - gift cards (optionaly)
     * - secondary variant products
     * - ...
     *
     * @param array $options Following are available:
     *      - 'giftCardProducts' (string) Possible values are:
     *          - 'allow' - gift card products are included too (are considered to be normal)
     *          - 'avoid' - gift card products are excluded (are considered to be special)
     *          - 'only' - only gift card products are returned
     *          Defaults to 'avoid'.
     *      - 'secondaryVariants' (string) Possible values are:
     *          - 'allow' - secondary variant products are included
     *          - 'avoid' - secondary variant products are excluded
     *          Defaults to 'avoid'.
     *
     * @return array
     */
    static public function getNormalProductsConditions($options = array()) {
        $options = array_merge(array(
            'giftCardProducts' => 'avoid',
            'secondaryVariants' => 'avoid',
        ), $options);
        static $voucherProductId = false;
        if ($voucherProductId === false) {
            $voucherProductId = App::getSetting('Eshop', 'EshopProduct.voucherProductId');
        }
        // avoid listing of special products
        $conditions = array(
            'EshopProduct.is_additional_service' => null,
        );
        $specialProductIds = array_filter(array(
            $voucherProductId,
            //...
        ));
        if ($specialProductIds) {
            $conditions['EshopProduct.id !='] = $specialProductIds;
        }
        // filter gift card products
        if ($options['giftCardProducts'] === 'only') {
            $conditions['EshopProduct.is_gift_card'] = true;
        }
        elseif ($options['giftCardProducts'] === 'avoid') {
            $conditions['EshopProduct.is_gift_card'] = false;
        }
        // filter secondary variant products
        if($options['secondaryVariants'] === 'avoid') {
            $conditions['EshopProduct.secondary_variant'] = false;
        }
        return $conditions;
    }

     /** *0*
     * Separator used to separate multiple values of filter params: znacka, krajina, vek, cena.
     *
     * @var type
     */
    public $filterValuesSeparator = '_';

    public $filterManufacturerParamName = 'výrobca';
    public $filterBrandParamName;
    public $filterPriceParamName = 'cena';
    public $sortParamName = 'zorad';

    public $filterFields = array(
        'color' => array(
            'slug' => 'farba',
            'label' => 'Farba',
        ),
        'material_color' => array(
            'slug' => 'farbalatky',
            'label' => 'Farba látky',
        ),
        'dimensions' => array(
            'slug' => 'rozmery',
            'label' => 'Rozmery',
        ),
        'diameter' => array(
            'slug' => 'priemer',
            'label' => 'Priemer',
        ),
        'marquee_height' => array(
            'slug' => 'vyskamarkizy',
            'label' => 'Pri výške markízy',
        ),
        'length' => array(
            'slug' => 'dlzka',
            'label' => 'Dĺžka',
        ),
        'extension' => array(
            'slug' => 'vysuv',
            'label' => 'Výsuv',
        ),
        'material' => array(
            'slug' => 'material',
            'label' => 'Materiál',
        ),
        'sail_width' => array(
            'slug' => 'sirkaplachty',
            'label' => 'Šírka plachty',
        ),
        'in_series' => array(
            'slug' => 'seria',
            'label' => 'Na série',
        ),
        'carrying_capacity' => array(
            'slug' => 'nosnost',
            'label' => 'Nosnosť',
        ),
        'pressure' => array(
            'slug' => 'tlak',
            'label' => 'Tlak',
        ),
        'power' => array(
            'slug' => 'vykon',
            'label' => 'Výkon',
        ),
        'electric_current' => array(
            'slug' => 'prud',
            'label' => 'Prúd',
        ),
        'inner_frame' => array(
            'slug' => 'vnutornyram',
            'label' => 'Vnútorný rám',
        ),
        'outer_frame' => array(
            'slug' => 'vonkajsiram',
            'label' => 'Vonkajší rám',
        ),
        'roof_thickness' => array(
            'slug' => 'hrubkastrechy',
            'label' => 'Hrúbka strechy',
        ),
        'inner_dimension' => array(
            'slug' => 'vnutornyrozmer',
            'label' => 'Vnútorný rozmer',
        ),
        'volume' => array(
            'slug' => 'objem',
            'label' => 'Objem',
        ),
        'voltage' => array(
            'slug' => 'napatie',
            'label' => 'Napätie',
        ),
        'capacity' => array(
            'slug' => 'kapacita',
            'label' => 'Kapacita',
        ),
        'variant' => array(
            'slug' => 'variant',
            'label' => 'Variant',
        ),
    );

    /**
     * Returns array of options for EshopProduct::find() to get orders counts
     * of products
     *
     * @param array $options Following are available:
     *      - 'forLastDays' (int) Defaults to 30.
     *      - 'forLastOrders' (int) Defaults to 100.
     *
     * @return array Find options containing:
     *      - 'joins'
     *      - 'fields'
     *      - 'literals'.'fields'
     *      - 'order'
     *      - 'literals'.'order'
     *      - 'group'
     */
    public function getOrdersCountsFindOptions($options = array()) {
        $options = array_merge(array(
            'forLastDays' => 30,
            'forLastOrders' => 100,
        ), $options);
        $findOptions = array();
        $Order = $this->loadModel('EshopOrder', true);
        $lastOrder = $Order->findFirst(array(
            'fields' => array('id', 'created'),
            'order' => 'EshopOrder.id DESC',
        ));
        // if no last order has been found then create a fake one to simplify the
        // further processing (this should happen only on a brand new eshop)
        if (!$lastOrder) {
            $lastOrder = array(
                'id' => 0,
                'created' => date('Y-m-d H:i:s'),
            );
        }
        $findOptions['joins'] = array(
            array(
                'model' => 'EshopOrderProduct',
                'type' => 'left',
            ),
            array(
                'model' => 'EshopOrder',
                'type' => 'left',
                'toModel' => 'EshopOrderProduct',
            ),
        );
        // consider only relevant orders
        $relevantOrdersConditions = array();
        // - if specified then only ordered products from last X days (counted from time of last order creation)
        if ($options['forLastDays']) {
            $relevantOrdersConditions['EshopOrder.created >'] = date(
                'Y-m-d H:i:s',
                strtotime($lastOrder['created']) - 60 * 60 * 24 * $options['forLastDays']
            );
        }
        // - if specified then only ordered products from last X orders
        if ($options['forLastOrders']) {
            if ($relevantOrdersConditions) {
                $relevantOrdersConditions[] = 'OR';
            }
            $relevantOrdersConditions['EshopOrder.id >'] = $lastOrder['id'] - 100;
        }
        // - orders count for unordered products is set to 0
        if ($relevantOrdersConditions) {
            $relevantOrdersConditions = array(
                'EshopOrder.id !=' => null,
                $relevantOrdersConditions,
            );
        }
        else {
            $relevantOrdersConditions = array(
                'EshopOrder.id !=' => null,
            );
        }

        $ordersCountSql = Str::fill('IF(:relevantOrdersConditions:, COUNT(EshopOrderProduct.id), 0)', array(
            'relevantOrdersConditions' => DB::getQueryConditions($relevantOrdersConditions)
        ));

        $findOptions['fields'] = array(
            $ordersCountSql . ' AS orders_count',
        );
        $findOptions['literals']['fields'][] = $ordersCountSql . ' AS orders_count';

        $findOptions['order'] = array(
            $ordersCountSql . ' DESC',
        );
        $findOptions['literals']['order'][] = $ordersCountSql . ' DESC';

        $findOptions['group'] = array('EshopProduct.id');
        return $findOptions;
    }

    /**
     * Sql to retrieve actual price or order by actual price chosen from price and discount_price
     */
    const ACTUAL_PRICE_SQL =
        'IF(
            `EshopProduct`.`discount_price` > 0 AND (`EshopProduct`.`discount_from` IS NULL OR DATEDIFF(NOW(),`EshopProduct`.`discount_from`) >= 0) AND (`EshopProduct`.`discount_to` IS NULL OR DATEDIFF(NOW(), `EshopProduct`.`discount_to`) <= 0),
            `EshopProduct`.`discount_price`,
            `EshopProduct`.`price`
        )';

    /**
     * Sql to retrieve actual taxed price or order by actual taxed price chosen from price and discount_price
     */
    const ACTUAL_PRICE_TAXED_SQL =
        'FORMAT(ROUND(
            IF(
                `EshopProduct`.`discount_price` > 0 AND (`EshopProduct`.`discount_from` IS NULL OR DATEDIFF(NOW(),`EshopProduct`.`discount_from`) >= 0) AND (`EshopProduct`.`discount_to` IS NULL OR DATEDIFF(NOW(), `EshopProduct`.`discount_to`) <= 0),
                `EshopProduct`.`discount_price`,
                `EshopProduct`.`price`
            ) * (1 + (`EshopProduct`.`tax_rate` / 100))
        ),2)';

    /**
     * Sql to check if the product is discounted
     */
    const IS_DISCOUNTED_SQL =
        '(`EshopProduct`.`discount_price` > 0 AND (`EshopProduct`.`discount_from` IS NULL OR DATEDIFF(NOW(),`EshopProduct`.`discount_from`) >= 0) AND (`EshopProduct`.`discount_to` IS NULL OR DATEDIFF(NOW(), `EshopProduct`.`discount_to`) <= 0))';

    const IS_NEW_SQL =
        '`EshopProduct`.`created` > CURDATE() - INTERVAL 14 DAY';

    /**
     * Product disponibility constants (disponibility = availability to client)
     * used to set product disponibility by method EshopProduct::getDetails()
     */
    const PRESALE = 0;

    /**
     * Product disponibility constants (disponibility = availability to client)
     * used to set product disponibility by method EshopProduct::getDetails()
     *
     * Autostock disponibility is used for products which cannot get out of stock.
     * E.g. gift card, ebook, absolute discount voucher
     */
    const AUTOSTOCK = -1;

    /**
     * Product disponibility constants (disponibility = availability to client)
     * used to set product disponibility by method EshopProduct::getDetails()
     */
    const STOCK = 1;

    /**
     * Product disponibility constants (disponibility = availability to client)
     * used to set product disponibility by method EshopProduct::getDetails()
     */
    const SUPPLIER = 2;

    /**
     * Product disponibility constants (disponibility = availability to client)
     * used to set product disponibility by method EshopProduct::getDetails()
     */
    const ON_DEMAND = 3;

    /**
     * Product disponibility constants (disponibility = availability to client)
     * used to set product disponibility by method EshopProduct::getDetails()
     */
    const SOLDOUT = 4;

    /**
     * List of available disponibilities and its (translated) labels like:
     *
     *      {disponibilityValue} => 'Disponibility label'
     *
     * Populated in __construct().
     *
     * @var array
     */
    protected $disponibilities = array();

    public function __construct() {
        parent::__construct();

        // set disponibilities
        $this->disponibilities = array(
            self::STOCK => __(__FILE__, 'on stock'),
            self::AUTOSTOCK => '',
            self::SUPPLIER => __(__FILE__, 'at supplier'),
            self::PRESALE => __(__FILE__, 'presale'),
            self::ON_DEMAND => __(__FILE__, 'on demand'),
            self::SOLDOUT => __(__FILE__, 'soldout'),
        );

        // set validations
        $this->validations = array(
            'code' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Code is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Code is mandatory'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Code must be unique'),
                ),
            ),
            'ean' => array(
                array(
                    'rule' => array('unique', array(), true),
                    'message' => __v(__FILE__, 'EAN must be unique'),
                ),
            ),
            'slug' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Slug is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Slug is mandatory'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Slug must be unique'),
                ),
            ),
            'seo_title' => array(
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Seo title is mandatory'),
//                    'on' => 'create',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Seo title is mandatory'),
//                ),
//                array(
//                    'rule' => 'unique',
//                    'message' => __v(__FILE__, 'Seo title must be unique'),
//                ),
            ),
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                ),
            ),
            'price' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Price is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => array('notEmpty', true),
                    'message' => __v(__FILE__, 'Price is mandatory'),
                ),
                array(
                    'rule' => 'number',
                    'message' => __v(__FILE__, 'Provide a number please'),
                ),
            ),
            'discount_price' => array(
                array(
                    'rule' => 'number',
                    'message' => __v(__FILE__, 'Provide a number please'),
                ),
                array(
                    'rule' => array('gt', 0),
                    'message' => __v(__FILE__, 'Zadajte nenulovú hodnotu'),
                ),
            ),
            'discount_rate' => array(
                array(
                    'rule' => 'number',
                    'message' => __v(__FILE__, 'Provide a number please'),
                ),
                array(
                    'rule' => function($value) {
                        return $value > 0 && $value < 100;
                    },
                    'message' => __v(__FILE__, 'Zadajte číslo väčšie ako 0 a menšie ako 100'),
                ),
            ),
            'discount_from' => array(
                array(
                    'rule' => 'date',
                    'message' => __v(__FILE__, 'Provide a date please'),
                ),
            ),
            'discount_to' => array(
                array(
                    'rule' => 'date',
                    'message' => __v(__FILE__, 'Provide a date please'),
                ),
            ),
            'stock' => array(
                array(
                    'rule' => 'number',
                    'message' => __v(__FILE__, 'Provide a number please'),
                ),
            ),
            'stock_location_code' => array(
                array(
                    'rule' => 'fieldMaxLength',
                    'message' => __v(__FILE__, 'Code can be max :length: characters long'),
                ),
            ),
            'shop_location_code' => array(
                array(
                    'rule' => 'fieldMaxLength',
                    'message' => __v(__FILE__, 'Code can be max :length: characters long'),
                ),
            ),
            'tax_rate' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Tax rate is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Tax rate is mandatory'),
                ),
                array(
                    'rule' => 'number',
                    'message' => __v(__FILE__, 'Provide a number please'),
                ),
            ),
            'availability' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Availability is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Availability is mandatory'),
                ),
            ),
            'units' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Units are mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Units are mandatory'),
                ),
            ),
            'related_ids' => array(
                // avoid too much ids here, especially if reciprocal relations are
                // applied because you get count^2 as resulting number of relations
                array(
                    'rule' => array('arrayMaxLength', 100),
                    'message' => __v(__FILE__, 'There can be max %s related products assigned to product', 100),
                ),
            ),
            'accessory_ids' => array(
                // avoid too much ids here, especially if reciprocal relations are
                // applied because you get count^2 as resulting number of relations
                array(
                    'rule' => array('arrayMaxLength', 100),
                    'message' => __v(__FILE__, 'There can be max %s accessories assigned to product', 100),
                ),
            ),
            'variants_common_name' => array(
                array(
                    'rule' => 'variantIds',
                    'force' => true,
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte spoločný názov pre varianty'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte spoločný názov pre varianty'),
                ),
            ),

            /**
             * Search fields
             */
            'keywords' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please enter search keywords'),
                    'alternative' => 'search',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please enter search keywords'),
                    'alternative' => 'search',
                ),
                array(
                    'rule' => 'searchKeywords',
                    'message' => __v(__FILE__, 'Keywords must be at least 2 chars long'),
                    'alternative' => 'search',
                ),
            ),
        );
    }

    /**
     * Returns EshopProduct::$disponibilities property
     *
     * @return array
     */
    public function getPropertyDisponibilities() {
        return $this->disponibilities;
    }

    /**
     * Normalizes data
     *
     * @param array $data
     * @param string $options Optional.
     *      - 'on' (string) Possible values are 'create', 'update', NULL
     *      - 'alternative' (string) Possible values are 'search', NULL
     *
     * @return array
     */
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];

        /**
         * Search fields normalization
         */
        if (in_array('search', $options['alternative'])) {
            //...
        }

        /**
         * Save normalization
         */
        if (
            empty($data['code'])
            && (
                $options['on'] === 'create'
                || array_key_exists('code', $data)
            )
        ) {
            $data['code'] = $this->getNextCode();
        }

        if (
            empty($data['slug'])
            && (
                $options['on'] === 'create'
                || array_key_exists('slug', $data)
            )
        ) {
            $data['slug'] = Sanitize::value($data['name']);
        }

        if (!empty($data['slug'])) {
            if ($options['on'] == 'create') {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                ));
            }
            else {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                    'id' => Sanitize::value($data['id']),
                ));
            }
        }

        if (!empty($data['description'])) {
            $data['description'] = Sanitize::contentHtml($data['description']);
        }

        if (
            $options['on'] === 'create'
            && empty($data['seo_title'])
            && !empty($data['name'])
        ) {
            $data['seo_title'] = $data['name'];
        }

        if (isset($data['price'])) {
            $data['price'] = str_replace(',', '.', $data['price']);
            if (
                $data['price'] !== ''
                && Validate::number($data['price'])
            ) {
                $data['price'] = (float)$data['price'];
            }
        }

        if (isset($data['discount_price'])) {
            $data['discount_price'] = str_replace(',', '.', $data['discount_price']);
            if (
                $data['discount_price'] !== ''
                && Validate::number($data['discount_price'])
            ) {
                $data['discount_price'] = (float)$data['discount_price'];
            }
        }

        if (isset($data['discount_rate'])) {
            $data['discount_rate'] = str_replace(',', '.', $data['discount_rate']);
            if (
                $data['discount_rate'] !== ''
                && Validate::number($data['discount_rate'])
            ) {
                $data['discount_rate'] = (float)$data['discount_rate'];
            }
        }

        // NOTE: discount_rate and discount_price are synchronized by DB trigger

        if (array_key_exists('dimensions', $data)) {
            $parsedDimensions = Number::parseDimensions($data['dimensions']);
            foreach ($parsedDimensions as $field => $dimension) {
                $data[$field] = Eshop::normalizeDimension($dimension);
            }
            $data['dimensions'] = Eshop::getDimensionsString($data);
        }

        if (!empty($data['weight'])) {
            $data['weight'] = str_replace(',', '.', $data['weight']);
        }

        if (
            empty($data['units'])
            && (
                $options['on'] === 'create'
                || array_key_exists('units', $data)
            )
        ) {
            $data['units'] = $this->getFieldDefaultValue('units');
        }

        if (
            empty($data['availability'])
            && (
                $options['on'] === 'create'
                || array_key_exists('availability', $data)
            )
        ) {
            $data['availability'] = $this->getFieldDefaultValue('availability');
        }

        // NOTE: unavailable field is set according availability and stock by DB trigger

        if (array_key_exists('active', $data)) {
            $data['active'] = (bool)$data['active'];
        }

        if (array_key_exists('stock', $data) && empty($data['stock'])) {
            $data['stock'] = 0;
        }

        if (!empty($data['discount_from'])) {
            $data['discount_from'] = date('Y-m-d', strtotime($data['discount_from']));
        }

        if (!empty($data['discount_to'])) {
            $data['discount_to'] = date('Y-m-d', strtotime($data['discount_to']));
        }

        if (!empty($data['available_from'])) {
            $data['available_from'] = date('Y-m-d', strtotime($data['available_from']));
        }

        if (!empty($data['author_ids'])) {
            if (is_string($data['author_ids'])) {
                $data['author_ids'] = explode(';', $data['author_ids']);
            }
            $data['author_ids'] = array_unique(array_filter(array_map('trim', $data['author_ids'])));
        }

        if (!empty($data['category_ids'])) {
            if (is_string($data['category_ids'])) {
                $data['category_ids'] = explode(';', $data['category_ids']);
            }
            $data['category_ids'] = array_unique(array_filter(array_map('trim', $data['category_ids'])));
        }

        if (!empty($data['related_ids'])) {
            if (is_string($data['related_ids'])) {
                $data['related_ids'] = explode(';', $data['related_ids']);
            }
            $data['related_ids'] = array_unique(array_filter(array_map('trim', $data['related_ids'])));
        }

        if (!empty($data['variant_ids'])) {
            if (is_string($data['variant_ids'])) {
                $data['variant_ids'] = explode(';', $data['variant_ids']);
            }
            $data['variant_ids'] = array_unique(array_filter(array_map('trim', $data['variant_ids'])));
        }

        if (!empty($data['accessory_ids'])) {
            if (is_string($data['accessory_ids'])) {
                $data['accessory_ids'] = explode(';', $data['accessory_ids']);
            }
            $data['accessory_ids'] = array_unique(array_filter(array_map('trim', $data['accessory_ids'])));
        }

        if (!empty($data['group_ids'])) {
            if (is_string($data['group_ids'])) {
                $data['group_ids'] = explode(';', $data['group_ids']);
            }
            $data['group_ids'] = array_filter(array_map('trim', $data['group_ids']));
        }

        //*/
        // resolve some product properties according to product type
        if (array_key_exists('run_eshop_product_types_id', $data)) {
            $this->loadModel('EshopProductType');
            $ProductType = new EshopProductType();
            $productTypePid = $ProductType->findFieldBy('pid', 'id', $data['run_eshop_product_types_id']);
            // resolve 'is_additional_service'
            $data['is_additional_service'] =
                in_array($productTypePid, $this->schema['is_additional_service']['values'])
                ? $productTypePid : null;
            // resolve 'is_gift_card'
            $data['is_gift_card'] = $productTypePid === 'giftCard';
        }
        ///*/

        if (
            array_key_exists('variant', $data)
            && (
                array_key_exists('run_eshop_product_types_id', $data)
                && ($productTypeId = $data['run_eshop_product_types_id'])
                ||
                $options['on'] === 'update'
                && ($productTypeId = $this->findFieldBy('run_eshop_product_types_id', 'id', $data['id']))
            )
        ) {
            $this->loadModel('EshopProductType');
            $ProductType = new EshopProductType();
            $variant = $ProductType->findFieldBy('variant', 'id', $productTypeId);
            if ((string)$data['variant'] !== (string)$variant) {
                $data['variant'] = $variant;
                $this->setWarning(__a(__FILE__, 'Product variant is set according to its product type'));
            }
        }

        return parent::normalize($data, $options);
    }

    /**
     * Normalizes find options. This is done in Model::find() if options['normalize'] is TRUE
     * after Paginator find options are merged, joins and fields are normalized
     * and before translation processing starts.
     *
     * @param array $options Find options to be normalized
     *
     * @return array Normalized find options
     */
    public function normalizeFindOptions($options) {
        // normalize order
        if (!empty($options['order'])) {
            $options['order'] = (array)$options['order'];
            $options['literals']['order'] = (array)Sanitize::value($options['literals']['order']);
            // $_GET['sort']['actual_price']
            $paths = Arr::search($options['order'], '/`actual_price`/', array('comparison' => 'regex', 'separator' => '/'));
            if (!empty($paths)) {
                foreach ($paths as $path) {
                    $value = Arr::getPath($options['order'], $path, '/');
                    $direction = explode(' ', $value);
                    $direction = end($direction);
                    Arr::setPath($options['order'], $path, self::ACTUAL_PRICE_SQL . ' ' . $direction, '/');
                    $options['literals']['order'][] = self::ACTUAL_PRICE_SQL . ' ' . $direction;
                }
            }
            // $_GET['sort']['bestseller']
            $paths = Arr::search($options['order'], '/`bestseller`/', array('comparison' => 'regex', 'separator' => '/'));
            if (!empty($paths)) {
                foreach ($paths as $path) {
                    Arr::unsetPath($options['order'], $path, array(
                        'separator' => '/',
                        'unsetEmptyTail' => true,
                    ));
                    $options = array_merge_recursive($this->getSortFindOptions('bestsellers'), $options);
                }
            }
        }
        // normalize having
        if (!empty($options['having'])) {
            $options['having'] = (array)$options['having'];
            $options['conditions'] = (array)Sanitize::value($options['conditions']);
            $options['literals']['conditions'] = (array)Sanitize::value($options['literals']['conditions']);
            // $_GET['filter']['discounted']
            $paths = Arr::search($options['having'], '/`discounted`/', array('comparison' => 'regex', 'separator' => '/'));
            if (!empty($paths)) {
                foreach ($paths as $path) {
                    Arr::unsetPath($options['having'], $path, array(
                        'separator' => '/',
                        'unsetEmptyTail' => true,
                    ));
                }
                $options['conditions'] = DB::nestConditions($options['conditions']);
                $options['conditions'][] = self::IS_DISCOUNTED_SQL;
                $options['literals']['conditions'][] = self::IS_DISCOUNTED_SQL;
            }
            // $_GET['filter']['new']
            $paths = Arr::search($options['having'], '/`new`/', array('comparison' => 'regex', 'separator' => '/'));
            if (!empty($paths)) {
                foreach ($paths as $path) {
                    Arr::unsetPath($options['having'], $path, array(
                        'separator' => '/',
                        'unsetEmptyTail' => true,
                    ));
                }
                $options['conditions'] = DB::nestConditions($options['conditions']);
////rblsb
//                $options['conditions'][] = self::IS_NEW_SQL;
//                $options['literals']['conditions'][] = self::IS_NEW_SQL;
                $options['conditions']['EshopProduct.new'] = true;

            }
        }
        // normalize conditions
        if (!empty($options['conditions'])) {
            // $_GET['filter']['stock'] = '>0'
            // if we are filtering for products on stock then we must resolve the case
            // when the diplayed product is the main variant (secondary_variant = 0)
            // but there are other (secondary) variants which are on the stock.
            $stockPaths = Arr::search($options['conditions'], '/`EshopProduct`\.`stock`/', array(
                'comparison' => 'regex', 'separator' => '/',
            ));
            $variantStockPaths = Arr::search($options['conditions'], '/`EshopVariantProduct`\.`stock`/', array(
                'comparison' => 'regex', 'separator' => '/',
            ));
            $secondaryVariantPath = Arr::search($options['conditions'], '/secondary_variant/', array(
                'for' => 'key', 'comparison' => 'regex', 'separator' => '/', 'first' => true,
            ));
            if (
                // if checking for stock
                !empty($stockPaths)
                // but not already resolved
                && empty($variantStockPaths)
                // and secondary variants are excluded from results
                && $secondaryVariantPath
                && !Arr::getPath($options['conditions'], $secondaryVariantPath, '/')
            ) {
                // add joins to product variants
                $options['joins'] = (array)Sanitize::value($options['joins']);
                $options['joins'][] = array(
                    'type' => 'left',
                    'model' => 'EshopVariantProduct',
                );
                $options['joins'][] = array(
                    'type' => 'left',
                    'model' => 'EshopProduct',
                    'alias' => 'EshopProductVariant',
                    'conditions' => array(
                        'EshopVariantProduct.run_eshop_variant_products_id = EshopProductVariant.id'
                    )
                );
                // enhance original EshopProduct.stock condition by the same condition for product variant
                foreach ($stockPaths as $stockPath) {
                    $originalCondition = Arr::getPath($options['conditions'], $stockPath, '/');
                    $variantStockCondition = array(
                        $originalCondition,
                        'OR',
                        str_replace('`EshopProduct`', '`EshopProductVariant`', $originalCondition)
                    );
                    Arr::setPath($options['conditions'], $stockPath, $variantStockCondition, '/');
                }
                // group HABTM records to single one
                $options['group'] = 'EshopProduct.id';
            }
        }

        return $options;
    }

    /**
     * Converts provided $searchData into find options used by Model::find().
     *
     * @param array $searchData Search data containing at least 'keywords' item.
     *      All other item are dependent by concrete implementation of this method on project.
     * @param array $options Following are avalible:
     *      - 'findOptions' (array) Find options to merge generated options into.
     *          Defaults to empty array().
     *      - 'relevanceField' (bool) If TRUE then search relevance is added to 'fields'
     *          as '_relevance' virtual field and it is not added to 'order' option.
     *          If FALSE then search relevance is added to 'order' option. Defaults to FALSE.
     *      - 'giftCardProducts' (string) Possible values are:
     *          - 'allow' - gift card products are included too
     *          - 'avoid' - gift card products are excluded
     *          - 'only' - only gift card products are returned
     *          Defaults to 'avoid'.
     *
     * @return array|bool Find options created according to provided $searchData.
     *      If $findOptions is provided the new options are merged into existing.
     *      Returns FALSE if $searchData are invalid.
     */
    public function getSearchFindOptions($searchData, $options = array()) {
        $options = array_merge(array(
            'findOptions' => array(),
            'relevanceField' => false,
            'giftCardProducts' => 'avoid',
        ), $options);
        $findOptions = &$options['findOptions'];
        // normalize and validate
        $searchData = $this->normalize($searchData, array('alternative' => 'search'));
        $valid = $this->validate($searchData, array(
            'normalize' => false,
            'alternative' => 'search',
        ));
        if (!$valid) {
            return false;
        }

        // ensure existence of folowing items in find options
        $findOptions['joins'] = (array)Sanitize::value($findOptions['joins']);
        $findOptions['conditions'] = (array)Sanitize::value($findOptions['conditions']);
        $findOptions['conditions'] = DB::nestConditions($findOptions['conditions']);
        $findOptions['literals'] = (array)Sanitize::value($findOptions['literals']);
        $findOptions['literals']['conditions'] = (array)Sanitize::value($findOptions['literals']['conditions']);
        if ($options['relevanceField']) {
            $findOptions['fields'] = (array)Sanitize::value($findOptions['fields']);
            $findOptions['literals']['fields'] = (array)Sanitize::value($findOptions['literals']['fields']);
        }
        else {
            $findOptions['order'] = (array)Sanitize::value($findOptions['order']);
            $findOptions['literals']['order'] = (array)Sanitize::value($findOptions['literals']['order']);
        }

        // set default conditions
        $findOptions['conditions'][] = self::getPublishedConditions();
        $findOptions['conditions'][] = self::getNormalProductsConditions($options);

        // if you order results also by some user selected custom sort (e.g. by price)
        // then strict search conditions (AND between keywords) must by generated.
        // Loose search conditions (OR between keywords) can be generated only in case of
        // pure relevence order as in this case the results containing all keywords are
        // placed as first by relevance. Even in case of user custom sort the relevance order
        // can be used too but only as secondary order.
        $strictSearchConditions = !empty($findOptions['order']);

        // keywords
        $keywords = self::parseSearchKeywords($searchData['keywords']);

        // check if we are searching for a single keyword
        $singleKeywordSearch = count($keywords) === 1;

        // check if we are searching for a single integer
        $singleIntegerSearch = (
            $singleKeywordSearch
            && preg_match('/^[0-9]+$/', $keywords[0])
        );

        // check if we are searching for strings only (there is no integer)
        $stringOnlySearch = true;
        foreach ($keywords as $keyword) {
            if (preg_match('/^[0-9]+$/', $keyword)) {
                $stringOnlySearch = false;
                break;
            }
        }

        // if single integer (code or ean) then make a simpler&faster search
        if (!$singleIntegerSearch) {
            $findOptions['joins'] = array_merge($findOptions['joins'], array(
                array(
                    'type' => 'left',
                    'model' => 'EshopProductCategoryProduct',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopProductCategory',
                    'toModel' => 'EshopProductCategoryProduct',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopManufacturer',
                ),
//                array(
//                    'type' => 'left',
//                    'model' => 'EshopProductAuthor',
//                ),
//                array(
//                    'type' => 'left',
//                    'model' => 'EshopAuthor',
//                    'toModel' => 'EshopProductAuthor',
//                ),
            ));
        }

        // prepare order by relevance (can be adjusted on project)
        // - define relevance levels from highest to lowest. Add any number
        // of your own conditions here or adjust the existing conditions.
        // The default operator applied betweed relevance conditions for each
        // keyword is OR - it means the relevance condition is TRUE if at least
        // one of keywords matches this condition. You can change the "between" operator
        // to 'AND' like {relevanceCondition} => 'AND' - it means you provide
        // the conditions like key and the value is 'AND'. By the way the 'AND'
        // is more relevant as the condition is TRUE only if it matches all keywords.
        // If you add fields from other models then actualize also the joins above
        if ($singleIntegerSearch) {
            $relevanceExpressions = array(
                //rblb//'(EshopProduct.name = ":k:" OR EshopProduct.code = ":k:" OR EshopProduct.ean = ":k:")',
                '(EshopProduct.name = ":k:" OR EshopProduct.code = ":k:" OR EshopProduct.ean = ":k:")',
                // matchs whole word in product or author name
                '(EshopProduct.name LIKE "% :k:" OR EshopProduct.name LIKE ":k: %" OR EshopProduct.name LIKE "% :k: %")',
                // matchs begining of word in product or author name
                '(EshopProduct.name LIKE ":k:%" OR EshopProduct.name LIKE "% :k:%")',
                // matchs anywhere in name
                'EshopProduct.name LIKE "%:k:%"',
            );
        }
        elseif ($singleKeywordSearch) {
            $relevanceExpressions = array(
                // matchs whole name
                '(EshopProduct.name = ":k:" OR EshopProduct.code = ":k:")',
                // matchs whole word in name
                '(EshopProduct.name LIKE "% :k:" OR EshopProduct.name LIKE ":k: %" OR EshopProduct.name LIKE "% :k: %")',
//                // matchs whole word in author name
//                '(EshopAuthor.name LIKE "% :k:" OR EshopAuthor.name LIKE ":k: %" OR EshopAuthor.name LIKE "% :k: %")',
                // matchs begining of word in name
                '(EshopProduct.name LIKE ":k:%" OR EshopProduct.name LIKE "% :k:%")',
                // matchs anywhere in name
                'EshopProduct.name LIKE "%:k:%"',
//                // matchs begining of word in author name
//                '(EshopAuthor.name LIKE ":k:%" OR EshopAuthor.name LIKE "% :k:%")',
//                // matchs anywhere in author name
//                'EshopAuthor.name LIKE "%:k:%"',
                // matchs whole word in manufacturer name
                '(EshopManufacturer.name LIKE "% :k:" OR EshopManufacturer.name LIKE ":k: %" OR EshopManufacturer.name LIKE "% :k: %")',
            );
        }
        else {
            $relevanceExpressions = array(
                // matchs whole name for phrase
                'EshopProduct.name = ":k:"' => 'PHRASE',
                // matchs whole code for phrase
                'EshopProduct.code = ":k:"' => 'PHRASE',
                // matchs beginning of name for phrase
                'EshopProduct.name LIKE ":k:%"' => 'PHRASE',
                // matchs anywhere in name for phrase
                'EshopProduct.name LIKE "%:k:%"' => 'PHRASE',
                // matchs whole word in name for all keywords
                '(EshopProduct.name LIKE "% :k:" OR EshopProduct.name LIKE ":k: %" OR EshopProduct.name LIKE "% :k: %")' => 'AND',
//                // matchs whole word in author name for all keywords
//                '(EshopAuthor.name LIKE "% :k:" OR EshopAuthor.name LIKE ":k: %" OR EshopAuthor.name LIKE "% :k: %")' => 'AND',
                // matchs anywhere in name for all keywords
                'EshopProduct.name LIKE "%:k:%"' => 'AND',
//                // matchs anywhere in author name
//                'EshopAuthor.name LIKE "%:k:%"' => 'AND',
                // matchs whole word in manufacturer name for all keywords
                '(EshopManufacturer.name LIKE "% :k:" OR EshopManufacturer.name LIKE ":k: %" OR EshopManufacturer.name LIKE "% :k: %")' => 'AND',
                // matchs whole product or author name or product ean for one of keywords
                //rblb//'(EshopProduct.name = ":k:" OR EshopProduct.ean = ":k:" OR EshopAuthor.name = ":k:" OR EshopManufacturer.name = ":k:")',
                '(EshopProduct.name = ":k:" OR EshopProduct.ean = ":k:" OR EshopManufacturer.name = ":k:")',
                // matchs whole word in product or author name for one of keywords
                //rblb//'(EshopProduct.name LIKE "% :k:" OR EshopProduct.name LIKE ":k: %" OR EshopProduct.name LIKE "% :k: %" OR EshopAuthor.name LIKE "% :k:" OR EshopAuthor.name LIKE ":k: %" OR EshopAuthor.name LIKE "% :k: %")',
                '(EshopProduct.name LIKE "% :k:" OR EshopProduct.name LIKE ":k: %" OR EshopProduct.name LIKE "% :k: %")',
                // matchs begining of word in product or author name for one of keywords
                //rblb//'(EshopProduct.name LIKE ":k:%" OR EshopProduct.name LIKE "% :k:%" OR EshopAuthor.name LIKE ":k:%" OR EshopAuthor.name LIKE "% :k:%")',
                '(EshopProduct.name LIKE ":k:%" OR EshopProduct.name LIKE "% :k:%")',
//                // matchs whole category name for one of keywords
//                'EshopProductCategory.name = ":k:"',
//                // matchs whole word in category name for one of keywords
//                '(EshopProductCategory.name LIKE "% :k:" OR EshopProductCategory.name LIKE ":k: %" OR EshopProductCategory.name LIKE "% :k: %")',
//                // matchs begining of word in category name for one of keywords
//                '(EshopProductCategory.name LIKE ":k:%" OR EshopProductCategory.name LIKE "% :k:%")',
                // matchs anywhere in name for one of keywords
                'EshopProduct.name LIKE "%:k:%"',
//                // matchs anywhere in category name for one of keywords
//                'EshopProductCategory.name LIKE "%:k:%"',
            );
        }
        // - get relevance order sql
        if (
            ($relevanceSql = self::getSearchRelevance(
                $relevanceExpressions,
                $searchData['keywords'],
                $keywords
            ))
        ) {
            if ($options['relevanceField']) {
                $findOptions['fields'][] = $relevanceSql . ' AS `_relevance`';
                $findOptions['literals']['fields'][] = $relevanceSql . ' AS `_relevance`';
            }
            else {
                $findOptions['order'][] = $relevanceSql . ' DESC';
                $findOptions['literals']['order'][] = $relevanceSql . ' DESC';
            }
        }

        // search for keywords
        if (!$singleIntegerSearch) {
            $searchFields = array(
                'EshopProduct.name',
//                'EshopAuthor.name',
                'EshopManufacturer.name',
                'EshopProductCategory.name',
                'EshopProduct.code',
                'EshopProduct.ean',
                'EshopProduct.slug',
                'EshopProduct.seo_title',
                'EshopProduct.seo_description',
                'EshopProduct.seo_keywords',
                'EshopProduct.description',
                'EshopProduct.short_description',
            );
        }
        else {
            $searchFields = array(
                'EshopProduct.name',
                'EshopProduct.code',
                'EshopProduct.ean',
            );
        }
        if (
            ($searchConditions = self::getSearchConditions(
                $searchFields,
                $keywords,
                array(
                    'strict' => $strictSearchConditions
                )
            ))
        ) {
            $findOptions['conditions'][] = $searchConditions;
        }

        // set default ordering (or add it to existing order)
        $findOptions['order'][] = 'EshopProduct.unavailable ASC';
        $findOptions['order'][] = 'EshopProduct.id DESC';
        // because of joins with categories some products can occure multiple times
        $findOptions['group'] = 'EshopProduct.id';

        return $findOptions;
    }

    /**
     * Returns array of product ids (resolved by external search API) for provided $searchData.
     *
     * ATTENTION: Read phpDoc of EshopProductMeiliSearch class.
     *
     * ATTENTION: If setting 'EshopProduct.allowExternalSearch' is FALSE then this
     * method does silently nothing and just returns TRUE.
     *
     * @param array $searchData Search data containing at least 'keywords' item.
     *      All other item are dependent by concrete implementation of this method on project.
     * @param array $findOptions Optional. Explicit find options but only 'limit' and 'sort' are considered.
     *      Defaults to empty array().
     *
     * @return array|bool|NULL List of product ids. This can be also empty array().
     *      Returns FALSE if $searchData are invalid. Returns NULL if external search
     *      is not allowed. On failure of request to external search API an exception is thrown.
     *
     * @throws Exception
     */
    public function getExternalSearchProductIds($searchData, $findOptions = array()) {
        if (!$this->getSetting('EshopProduct.allowExternalSearch')) {
            return null;
        }
        $findOptions = array_merge(array(
            'limit' => $this->getSetting('EshopProduct.pagingLimit'),
        ), $findOptions);
        // normalize and validate
        $searchData = $this->normalize($searchData, array('alternative' => 'search'));
        $valid = $this->validate($searchData, array(
            'normalize' => false,
            'alternative' => 'search',
        ));
        if (!$valid) {
            return false;
        }

        $this->loadPaginator($findOptions);
        $findOptions = $this->Paginator->getFindOptions($findOptions);

        // converts order comming from sort selectbox in list of search results
        // to external sort param
        // See https://docs.meilisearch.com/learn/advanced/sorting.html
        $sort = array();
        if (!empty($findOptions['order'])) {
            $findOptions['order'] = (array)$findOptions['order'];
            // $_GET['sort']['actual_price']
            // $_GET['sort']['name']
            // $_GET['sort']['sort_bestsellers']
            $paths = Arr::search(
                $findOptions['order'],
                '/(`actual_price`|`EshopProduct`.`name`|`sort_bestsellers`)/',
                array('comparison' => 'regex', 'separator' => '/')
            );
            if (!empty($paths)) {
                foreach ($paths as $path) {
                    $value = Arr::getPath($findOptions['order'], $path, '/');
                    $direction = explode(' ', $value);
                    $direction = strtolower(end($direction));
                    if (strpos($value, '`actual_price`') !== false) {
                        $sort[] = 'price_actual_taxed:' . $direction;
                    }
                    elseif (strpos($value, '`EshopProduct`.`name`') !== false) {
                        $sort[] = 'name:' . $direction;
                    }
                    elseif (strpos($value, '`sort_bestsellers`') !== false) {
                        $sort[] = 'orders_count:desc';
                    }
                }
            }
        }
        // converts having comming from filter selectbox in list of search results
        // to external sort param
        // See https://docs.meilisearch.com/learn/advanced/filtering_and_faceted_search.htm
        $filter = array();
        if (!empty($findOptions['having'])) {
            $findOptions['having'] = (array)$findOptions['having'];
            // $_GET['filter']['language']
            // $_GET['filter']['stock']
            $paths = Arr::search(
                $findOptions['having'],
                "/(`on_stock`|`discounted`|`new`|`actual_price`)/",
                array('comparison' => 'regex', 'separator' => '/')
            );
            if (!empty($paths)) {
                foreach ($paths as $path) {
                    $value = Arr::getPath($findOptions['having'], $path, '/');
                    if (strpos($value, '`on_stock`') !== false) {
                        $filter[] = 'on_stock = 1';
                    }
                    elseif (strpos($value, '`discounted`') !== false) {
                        $filter[] = 'is_discounted = 1';
                    }
                    elseif (strpos($value, '`new`') !== false) {
                        $filter[] = 'is_new = 1';
                    }
                    elseif (strpos($value, '`actual_price`') !== false) {
                        $value = str_replace('`actual_price`', 'price_actual_taxed', $value);
                        // remove apostrophs from values ('41' -> 41)
                        $value = str_replace('\'', '', $value);
                        $filter[] = $value;
                    }
                }
            }
        }

        $this->loadModel('EshopProductMeiliSearch');
        $ProductSearch = new EshopProductMeiliSearch();
        $SearchResult = $ProductSearch->search($searchData['keywords'], array(
            'offset' => (int)$this->Paginator->getOffset(),
            'limit' => (int)$this->Paginator->getPropertyLimit(),
            'filter' => $filter,
            'sort' => $sort,
        ));

        $rawResponse = $SearchResult->getRaw();
        $this->Paginator->setPropertyCount($rawResponse['estimatedTotalHits']);
        $productIds = array();
        foreach($rawResponse['hits'] as $hit) {
            $productIds[] = $hit['id'];
        }
        return $productIds;
    }

    /**
     * ATTENTION: If setting 'EshopProduct.allowExternalSearch' is FALSE then this
     * method does silently nothing and just returns TRUE.
     *
     * @param array $options Options of EshopProductMeiliSearch::synchronize()
     *
     * @return boolean Return value of EshopProductMeiliSearch::synchronize()
     */
    public function synchronizeWithExternalSearch($options = array()) {
        if (!$this->getSetting('EshopProduct.allowExternalSearch')) {
            return true;
        }
        // synchronize MeiliSearch data
        App::loadModel('Eshop', 'EshopProductMeiliSearch');
        $ProductSearch = new EshopProductMeiliSearch();
        return $ProductSearch->synchronize($options);
    }

    /**
     * This function find details for all products retrieved according to provided
     * options
     *
     * @param array $filterOptions Optional. Options to be used for EshopProduct::getFilterFindOptions() method
     *      Defaults to empty array().
     * @param &array $findOptions Optional. Passed by reference. Explicit find options used to retrieve product ids.
     *      They are updated by filter generated options. Defaults to empty array().
     * @param array $detailsOptions Optional. Options to be used for EshopProduct::getDetails() method.
     *      Defaults to empty array().
     *
     * @return array|bool Array of detailed product records. Returns FALSE if filter
     *      contains unexisting value (e.g. unexisting category, or manufacturer or author name.
     */
    public function filter($filterOptions = array(), &$findOptions = array(), $detailsOptions = array()) {
        $findOptions = $this->getFilterFindOptions($filterOptions, $findOptions);
        if ($findOptions === false) {
            return false;
        }
        $findOptions['fields'] = array($this->name . '.' . $this->primaryKey);
        $findOptions['key'] = $this->name . '.' . $this->primaryKey;
        $ids = array_unique(array_values($this->findList($findOptions)));
        return $this->getDetails($ids, $detailsOptions);
    }

    /**
     * Normalizes provided filter options like:
     *  - dynamic filter is converted to static one, e.g. the 'filter' => 'category'
     *      + 'filter_id' => 'xyz' is changed to 'filter_category' => 'xyz'.
     *
     * @param array $filterOptions
     *
     * @return array Normalized filter options
     */
    public function normalizeFilterOptions ($filterOptions) {
        // convert dynamic filter and its id to static filter
        if (
            !empty($filterOptions['filter'])
            && !empty($filterOptions['filter_id'])
        ) {
            $filterOptions['filter'] = 'filter_' . ltrim($filterOptions['filter'], '_');
            $filterOptions[$filterOptions['filter']] = $filterOptions['filter_id'];
        }
        return $filterOptions;
    }

    /**
     * Converts provided filter options into find options used by Model::find().
     * This method also sets seo title, keywords and description - see filter option
     * 'set_seo'.
     *
     * @param array $filterOptions Filter options with following posibilities:
     *      - 'filter' (string) Name of property for dynamic filtering of products.
     *          Possible values are: 'product', 'author', 'manufacturer, 'range', 'type',
     *          'category', 'group', 'wishlist', 'brand'. Defaults to NULL.
     *      - 'filter_id' (int|string|array) Id (int) or slug (string) or semicolon
     *          separated slugs/ids (string) or array of ids/slugs of dynamic filter.
     *          Defaults to NULL.
     *      - 'filter_product', 'filter_author', 'filter_manufacturer', 'filter_range', 'filter_type',
     *          'filter_category', 'filter_group', 'filter_wishlist' (int|string), 'filter_brand' (int|string),
     *          Static filtering by explicit id (int) or slug (string) or semicolon
     *          separated slugs/ids (string) or array of ids/slugs. All default to NULL.
     *      - 'avoidProduct' (int|string|array) Single product id (int) or semicolon
     *          separated product ids (string) or array of product ids to be excluded
     *          from found records. Defaults to NULL.
     *      - 'category_own_products'  (bool) If TRUE then only products of filtered category are indexed.
     *          If FALSE then all products of main category and its subcategories are indexed.
     *          Defaults to FALSE.
     *      - 'sort' (string) Single field or comma separated list of fields to sort
     *          products by. E.g. 'name' or 'EshopProduct.name' or 'product.name' (model
     *          nicknames can be used, see in code) or 'EshopProduct.name ASC' or
     *          'EshopProduct.modified ASC, EshopProduct.price DESC'.  Defaults to NULL.
     *      - 'direction' (string) Default direction used for sorting if the sort does
     *          not have defined any direction. Defaults to 'ASC'.
     *      - 'sort_random' (bool) If TRUE then an random ordering is set. Defaults to FALSE.
     *      - 'sort_bestsellers'|'sortBestsellers' (bool) If TRUE then ordering is set according selling
     *           frequecy of products. Defaults to FALSE.
     *      - 'limit' (int) Explicit limit of products to display. This can also change
     *          default pagination limit if option 'paginate' is TRUE. Defaults to NULL.
     *      - 'set_seo' (bool) If TRUE then seo title, keywords and description are set
     *          according to provided filter options. Defaults to FALSE.
     *      - 'availableOnly' (bool) If TRUE then only avaliable products are filtered
     *      - 'hasImage' (bool) Filter only products which have images
     *      - 'discount' (bool) Filter only discounted products
     *      - 'new' (bool) Filter only new products
     *      - 'avoidDefaultOrder' (bool) If TRUE then the default order is not set.
     *          Defaults to FALSE.
     *      - 'giftCardProducts' (string) Possible values are:
     *          - 'allow' - gift card products are included too
     *          - 'avoid' - gift card products are excluded
     *          - 'only' - only gift card products are returned
     *          Defaults to 'avoid'.
     *
     * Following are project dependent options:
     *
     * @param array $findOptions Optional. Existing find options to merge find options
     *      generated by filter into. Defaults to array().
     *
     * @return array|bool Find options created according to provided filter options.
     *      If $findOptions is provided the new options are merged into existing.
     *      Returns FALSE if filter contains unexisting value (e.g. unexisting category,
     *      or manufacturer or author name.
     */
    public function getFilterFindOptions($filterOptions, $findOptions = array()) {
        $filterDefaults = array(
            'filter' => null,
            'filter_id' => null,
            'filter_product' => null,
            'filter_author' => null,
            'filter_manufacturer' => null,
            'filter_range' => null,
            'filter_type' => null,
            'filter_category' => null,
            'filter_group' => null,
            'filter_wishlist' => null,
            'filter_brand' => null,
            'avoidProduct' => null,
            'category_own_products' => false,
            'sort' => null,
            'direction' => 'ASC',
            'sort_random' => false,
            'sort_bestsellers' => false,
            'sortBestsellers' => false,
            'sortTop' => false,
            'limit' => null,
            'offset' => null,
            'page' => null,
            'paginate' => false,
            'set_seo' => false,
            'availableOnly' => false,
            'hasImage' => null,
            'discount' => null,
            'new' => null,
            'avoidDefaultOrder' => false,
            'giftCardProducts' => 'avoid',
            // custom project options
            $this->filterManufacturerParamName => null,
            $this->filterPriceParamName => null,
            $this->sortParamName => null,
        );
        $filterOptions = array_merge($filterDefaults, $filterOptions);

        $filterOptions = $this->normalizeFilterOptions($filterOptions);

        // ensure existence of folowing items in find options
        $findOptions['joins'] = (array)Sanitize::value($findOptions['joins']);
        $findOptions['conditions'] = (array)Sanitize::value($findOptions['conditions']);
        $findOptions['conditions'] = DB::nestConditions($findOptions['conditions']);
        $findOptions['order'] = (array)Sanitize::value($findOptions['order']);
        $findOptions['group'] = (array)Sanitize::value($findOptions['group']);
        $findOptions['literals'] = (array)Sanitize::value($findOptions['literals']);
        $findOptions['literals']['order'] = (array)Sanitize::value($findOptions['literals']['order']);

        // force paging find options if they are set in filter options
        if ($filterOptions['limit'] !== null) {
            $findOptions['limit'] = $filterOptions['limit'];
        }

        // set default conditions
        $findOptions['conditions'][] = self::getPublishedConditions();
        $findOptions['conditions'][] = self::getNormalProductsConditions($filterOptions);

        // get sort field and sort model
        $sortModels = array();
        $sort = array();
        if ($filterOptions['sort']) {
            $supportedSortModels = array(
                'product' => 'EshopProduct',
                'manufacturer' => 'EshopManufacturer',
                'range' => 'EshopManufacturerRange',
                'type' => 'EshopProductType',
            );
            $sortCommaParts = explode(',', $filterOptions['sort']);
            $sortCommaParts = array_map('trim', $sortCommaParts);
            foreach ($sortCommaParts as $sortCommaPart) {
                $sortDotParts = explode('.', $sortCommaPart);
                $sortDotParts = array_map('trim', $sortDotParts);
                $sortField = array_pop($sortDotParts);
                $sortModel = array_pop($sortDotParts);
                $sortSpaceParts = explode(' ', $sortField);
                $sortField = array_shift($sortSpaceParts);
                $sortDirection = strtoupper(array_pop($sortSpaceParts));
                if (
                    !$sortDirection
                    || (
                        $sortDirection !== 'ASC'
                        && $sortDirection !== 'DESC'
                    )
                ) {
                    $sortDirection = $filterOptions['direction'];
                }
                if (!$sortModel) {
                    $sortModel = 'product';
                }
                if (isset($supportedSortModels[$sortModel])) {
                    $sortModel = $supportedSortModels[$sortModel];
                }
                elseif (!in_array($sortModel, $supportedSortModels)) {
                    // do nothing silently - it's maybe user who changes snippet options and he cannot just KO website
                    continue;
                    //throw new Exception("Invalid filter sort {$filterOptions['sort']}");
                }
                $sortModels[$sortModel] = true;
                $sort[] = array(
                    'model' => $sortModel,
                    'field' => $sortField,
                    'direction' => $sortDirection,
                );
            }
        }

        $conditionField = 'id';
        // product
        if($filterOptions['filter_product']) {
            // check for multiple ids or slugs are provided as semicolon separated string
            if (
                is_string($filterOptions['filter_product'])
                && strpos($filterOptions['filter_product'], ';') !== false
            ) {
                $filterOptions['filter_product'] = array_filter(array_map(
                    'trim', explode(';', $filterOptions['filter_product'])
                ));
            }
            // check if id(s) or slug(s) are provided
            if (is_array($filterOptions['filter_product'])) {
                $arrayOfIds = true;
                foreach($filterOptions['filter_product'] as $item) {
                    if (!Validate::intNumber($item)) {
                        $arrayOfIds = false;
                        break;
                    }
                }
                if (!$arrayOfIds) {
                    $conditionField = 'EshopProduct.slug';
                }
            }
            elseif (!Validate::intNumber($filterOptions['filter_product'])) {
                $conditionField = 'EshopProduct.slug';
            }
            $filterOptions['filter_product'] = $this->findList(array(
                'fields' => 'id',
                'conditions' => array(
                    $conditionField => $filterOptions['filter_product']
                ),
                'order' =>
                    !is_array($filterOptions['filter_product'])
                    ?
                    array()
                    :
                    array(
                        $conditionField => $filterOptions['filter_product']
                    ),
            ));
            if (!$filterOptions['filter_product']) {
                return false;
            }
            $findOptions['conditions']['EshopProduct.id']
                = $filterOptions['filter_product'];
            $findOptions['order'] = array(
                'EshopProduct.unavailable ASC',
                'EshopProduct.id' => $filterOptions['filter_product'],
            );
            // seo
            if ($filterOptions['set_seo']) {
                $seoData = $this->findFirst(array(
                    'fields' => array(
                        'EshopProduct.name',
                        'EshopProduct.seo_title',
                        'EshopProduct.seo_keywords',
                        'EshopProduct.seo_description'
                    ),
                    'conditions' => array(
                        'id' => $filterOptions['filter_product']
                    )
                ));
            }
        }
        // author
        if($filterOptions['filter_author']) {
            $Author = $this->loadModel('EshopAuthor', true);
            // check for multiple ids or slugs are provided as semicolon separated string
            if (
                is_string($filterOptions['filter_author'])
                && strpos($filterOptions['filter_author'], ';') !== false
            ) {
                $filterOptions['filter_author'] = array_filter(array_map(
                    'trim', explode(';', $filterOptions['filter_author'])
                ));
            }
            // check if id(s) or slug(s) are provided
            if (is_array($filterOptions['filter_author'])) {
                $arrayOfIds = true;
                foreach($filterOptions['filter_author'] as $item) {
                    if (!Validate::intNumber($item)) {
                        $arrayOfIds = false;
                        break;
                    }
                }
                if (!$arrayOfIds) {
                    $conditionField = 'EshopAuthor.slug';
                }
            }
            elseif (!Validate::intNumber($filterOptions['filter_author'])) {
                $conditionField = 'EshopAuthor.slug';
            }
            $filterOptions['filter_author'] = $Author->findList(array(
                'fields' => 'id',
                'conditions' => array(
                    $conditionField => $filterOptions['filter_author']
                )
            ));
            if (!$filterOptions['filter_author']) {
                return false;
            }
            $findOptions['conditions']['EshopProductAuthor.run_eshop_authors_id']
                = $filterOptions['filter_author'];
            $findOptions['joins'][] = array(
                'model' => 'EshopProductAuthor',
                'type' => 'left',
            );
            if (count($filterOptions['filter_author']) > 1) {
                $findOptions['group'] = 'EshopProduct.id';
            }
            // seo
            if ($filterOptions['set_seo']) {
                $seoData = $Author->findFirst(array(
                    'fields' => array(
                        'EshopAuthor.name',
                        'EshopAuthor.seo_title',
                        'EshopAuthor.seo_keywords',
                        'EshopAuthor.seo_description'
                    ),
                    'conditions' => array(
                        'id' => $filterOptions['filter_author']
                    )
                ));
            }
        }
        // manufacturer
        if($filterOptions['filter_manufacturer']) {
            $Manufacturer = $this->loadModel('EshopManufacturer', true);
            // check for multiple ids or slugs are provided as semicolon separated string
            if (
                is_string($filterOptions['filter_manufacturer'])
                && strpos($filterOptions['filter_manufacturer'], ';') !== false
            ) {
                $filterOptions['filter_manufacturer'] = array_filter(array_map(
                    'trim', explode(';', $filterOptions['filter_manufacturer'])
                ));
            }
            // check if id(s) or slug(s) are provided
            if (is_array($filterOptions['filter_manufacturer'])) {
                $arrayOfIds = true;
                foreach($filterOptions['filter_manufacturer'] as $item) {
                    if (!Validate::intNumber($item)) {
                        $arrayOfIds = false;
                        break;
                    }
                }
                if (!$arrayOfIds) {
                    $conditionField = 'EshopManufacturer.slug';
                }
            }
            elseif (!Validate::intNumber($filterOptions['filter_manufacturer'])) {
                $conditionField = 'EshopManufacturer.slug';
            }
            $filterOptions['filter_manufacturer'] = $Manufacturer->findList(array(
                'fields' => 'id',
                'conditions' => array(
                    'active' => true,
                    $conditionField => $filterOptions['filter_manufacturer']
                )
            ));
            if (!$filterOptions['filter_manufacturer']) {
                return false;
            }
            $findOptions['conditions']['EshopProduct.run_eshop_manufacturers_id']
                = $filterOptions['filter_manufacturer'];
            // seo
            if ($filterOptions['set_seo']) {
                $seoData = $Manufacturer->findFirst(array(
                    'fields' => array(
                        'EshopManufacturer.name',
                        'EshopManufacturer.seo_title',
                        'EshopManufacturer.seo_keywords',
                        'EshopManufacturer.seo_description'
                    ),
                    'conditions' => array(
                        'id' => $filterOptions['filter_manufacturer']
                    )
                ));
            }
        }
        if (isset($sortModels['EshopManufacturer'])) {
            $findOptions['joins'][] = array(
                'model' => 'EshopManufacturer',
                'type' => 'left',
            );
        }
        // range
        if($filterOptions['filter_range']) {
            $Range = $this->loadModel('EshopManufacturerRange', true);
            // check for multiple ids or slugs are provided as semicolon separated string
            if (
                is_string($filterOptions['filter_range'])
                && strpos($filterOptions['filter_range'], ';') !== false
            ) {
                $filterOptions['filter_range'] = array_filter(array_map(
                    'trim', explode(';', $filterOptions['filter_range'])
                ));
            }
            // check if id(s) or slug(s) are provided
            if (is_array($filterOptions['filter_range'])) {
                $arrayOfIds = true;
                foreach($filterOptions['filter_range'] as $item) {
                    if (!Validate::intNumber($item)) {
                        $arrayOfIds = false;
                        break;
                    }
                }
                if (!$arrayOfIds) {
                    $conditionField = 'EshopManufacturerRange.slug';
                }
            }
            elseif (!Validate::intNumber($filterOptions['filter_range'])) {
                $conditionField = 'EshopManufacturerRange.slug';
            }
            $filterOptions['filter_range'] = $Range->findList(array(
                'fields' => 'id',
                'conditions' => array(
                    $conditionField => $filterOptions['filter_range']
                )
            ));
            if (!$filterOptions['filter_range']) {
                return false;
            }
            $findOptions['conditions']['EshopProduct.run_eshop_manufacturer_ranges_id']
                = $filterOptions['filter_range'];
            // seo
            if ($filterOptions['set_seo']) {
                $seoData = $Range->findFirst(array(
                    'fields' => array(
                        'EshopManufacturerRange.name',
                        'EshopManufacturerRange.seo_title',
                        'EshopManufacturerRange.seo_keywords',
                        'EshopManufacturerRange.seo_description'
                    ),
                    'conditions' => array(
                        'id' => $filterOptions['filter_range']
                    )
                ));
            }
        }
        if (isset($sortModels['EshopManufacturerRange'])) {
            $findOptions['joins'][] = array(
                'model' => 'EshopManufacturerRange',
                'type' => 'left',
            );
        }
        // type
        if($filterOptions['filter_type']) {
            $ProductType = $this->loadModel('EshopProductType', true);
            // check for multiple ids or slugs are provided as semicolon separated string
            if (
                is_string($filterOptions['filter_type'])
                && strpos($filterOptions['filter_type'], ';') !== false
            ) {
                $filterOptions['filter_type'] = array_filter(array_map(
                    'trim', explode(';', $filterOptions['filter_type'])
                ));
            }
            // check if id(s) or slug(s) are provided
            if (is_array($filterOptions['filter_type'])) {
                $arrayOfIds = true;
                foreach($filterOptions['filter_type'] as $item) {
                    if (!Validate::intNumber($item)) {
                        $arrayOfIds = false;
                        break;
                    }
                }
                if (!$arrayOfIds) {
                    $conditionField = 'EshopProductType.slug';
                }
            }
            elseif (!Validate::intNumber($filterOptions['filter_type'])) {
                $conditionField = 'EshopProductType.slug';
            }
            $filterOptions['filter_type'] = $ProductType->findList(array(
                'fields' => 'id',
                'conditions' => array(
                    $conditionField => $filterOptions['filter_type']
                )
            ));
            if (!$filterOptions['filter_type']) {
                return false;
            }
            $findOptions['conditions']['EshopProduct.run_eshop_product_types_id']
                = $filterOptions['filter_type'];
            // seo
            if ($filterOptions['set_seo']) {
                $seoData = $ProductType->findFirst(array(
                    'fields' => array(
                        'EshopProductType.name',
                        'EshopBrand.name',
                        'EshopProductType.seo_title',
                        'EshopProductType.seo_keywords',
                        'EshopProductType.seo_description'
                    ),
                    'conditions' => array(
                        'id' => $filterOptions['filter_type']
                    )
                ));
            }
        }
        if (isset($sortModels['EshopProductType'])) {
            $findOptions['joins'][] = array(
                'model' => 'EshopProductType',
                'type' => 'left',
            );
        }
        // category
        if($filterOptions['filter_category']) {
            $Category = $this->loadModel('EshopProductCategory', true);
            // check for multiple ids or slugs are provided as semicolon separated string
            if (
                is_string($filterOptions['filter_category'])
                && strpos($filterOptions['filter_category'], ';') !== false
            ) {
                $filterOptions['filter_category'] = array_filter(array_map(
                    'trim', explode(';', $filterOptions['filter_category'])
                ));
            }
            // check if id(s) or slug(s) are provided
            if (($explicitIds = is_array($filterOptions['filter_category']))) {
                $arrayOfIds = true;
                foreach($filterOptions['filter_category'] as $item) {
                    if (!Validate::intNumber($item)) {
                        $arrayOfIds = false;
                        break;
                    }
                }
                if (!$arrayOfIds) {
                    $conditionField = 'EshopProductCategory.slug';
                }
            }
            elseif (!Validate::intNumber($filterOptions['filter_category'])) {
                $conditionField = 'EshopProductCategory.slug';
            }
            $filterOptions['filter_category'] = $Category->findList(array(
                'fields' => 'id',
                'conditions' => array(
                    'EshopProductCategory.active' => true,
                    $conditionField => $filterOptions['filter_category']
                )
            ));
            if (!$filterOptions['filter_category']) {
                return false;
            }
            $categoryIds = array_values($filterOptions['filter_category']);
            if (
                !$explicitIds
                && empty($filterOptions['category_own_products'])
            ) {
                foreach ($filterOptions['filter_category'] as $categoryId) {
                    $categories = $Category->findInTree($categoryId, array(
                        'fields' => array('id'),
                        'conditions' => array('EshopProductCategory.active' => true),
                    ));
                    foreach ($categories as $category) {
                        $categoryIds[] = $category['id'];
                    }
                }
            }
            $findOptions['conditions']['EshopProductCategoryProduct.run_eshop_product_categories_id']
                = $categoryIds;
            $findOptions['joins'][] = array(
                'model' => 'EshopProductCategoryProduct',
                'type' => 'left',
            );
            // apply ordering of products by their order in categories but only if:
            // - value of EshopProductCategory.sortProductsLimit setting is greater than zero
            // - all considered categories has sort_products set to TRUE
            $sortProductsLimit = $this->getSetting('EshopProductCategory.sortProductsLimit');
            if ($sortProductsLimit > 0) {
                $sortProductsCategoriesCount = $Category->findCount(array(
                    'conditions' => array(
                        'id' => $categoryIds,
                        'sort_products' => true,
                    )
                ));
                if (count($categoryIds) === $sortProductsCategoriesCount) {
                    if (count($categoryIds) > 1) {
                        $findOptions['order'] = array(
                            'EshopProductCategory.sort ASC',
                            // use EshopProductCategoryProduct.id instead of EshopProductCategoryProduct.sort as field sort is not populated
                            'EshopProductCategoryProduct.id ASC',
                        );
                        $findOptions['joins'][] = array(
                            'model' => 'EshopProductCategory',
                            'toModel' => 'EshopProductCategoryProduct',
                            'type' => 'left',
                        );
                    }
                    else {
                        $findOptions['order'] = 'EshopProductCategoryProduct.id ASC';
                    }
                }
            }
            if (count($categoryIds) > 1) {
                $findOptions['group'] = 'EshopProduct.id';
            }
            // seo
            if ($filterOptions['set_seo']) {
                $seoData = $Category->findFirst(array(
                    'fields' => array(
                        'EshopProductCategory.name',
                        'EshopProductCategory.seo_title',
                        'EshopProductCategory.seo_keywords',
                        'EshopProductCategory.seo_description',
                        'EshopProductCategory.path'
                    ),
                    'conditions' => array(
                        'id' => $filterOptions['filter_category']
                    )
                ));
                /*/>
                if ($seoData) {
                    $parentIds = Model::getParentIdsFromTreePath($seoData['path']);
                    $parentsSeoData = $Category->find(array(
                        'fields' => array(
                            'EshopProductCategory.name',
                            'EshopProductCategory.seo_title',
                            'EshopProductCategory.seo_keywords',
                            'EshopProductCategory.seo_description'
                        ),
                        'conditions' => array(
                            'id' => $parentIds
                        ),
                        'order' => array('EshopProductCategory.path DESC'),
                    ));
                    // remove root
                    array_pop($parentsSeoData);
                    // appent parent seo titles to actual category seo title
                    foreach($parentsSeoData as $parentSeoData) {
                        if ($parentSeoData['seo_title']) {
                            $seoData['seo_title'] .= $seoData['seo_title'] ? ' | ' : '';
                            $seoData['seo_title'] .= $parentSeoData['seo_title'];
                        }
                    }
                }
                //*/
            }
        }
        // group
        if($filterOptions['filter_group']) {
            $Group = $this->loadModel('EshopProductGroup', true);
            // check for multiple ids or slugs are provided as semicolon separated string
            if (
                is_string($filterOptions['filter_group'])
                && strpos($filterOptions['filter_group'], ';') !== false
            ) {
                $filterOptions['filter_group'] = array_filter(array_map(
                    'trim', explode(';', $filterOptions['filter_group'])
                ));
            }
            // check if id(s) or slug(s) are provided
            if (is_array($filterOptions['filter_group'])) {
                $arrayOfIds = true;
                foreach($filterOptions['filter_group'] as $item) {
                    if (!Validate::intNumber($item)) {
                        $arrayOfIds = false;
                        break;
                    }
                }
                if (!$arrayOfIds) {
                    $conditionField = 'EshopProductGroup.slug';
                }
            }
            elseif (!Validate::intNumber($filterOptions['filter_group'])) {
                $conditionField = 'EshopProductGroup.slug';
            }
            $groups = $Group->find(array(
                'fields' => array(
                    'id',
                    'hide_unavailable_products',
                ),
                'conditions' => array(
                    'active' => true,
                    $conditionField => $filterOptions['filter_group']
                )
            ));
            if (empty($groups)) {
                return false;
            }
            $filterOptions['filter_group'] = array();
            $groupsConditions = array();
            $unavailableHidden = true;
            foreach ($groups as $group) {
                $filterOptions['filter_group'][] = $group['id'];
                if (!empty($group['hide_unavailable_products'])) {
                    $groupsConditions[] = array(
                        'EshopProductGroupProduct.run_eshop_product_groups_id' => $group['id'],
                        'EshopProduct.unavailable' => 0,
                    );
                }
                else {
                    $groupsConditions[] = array(
                        'EshopProductGroupProduct.run_eshop_product_groups_id' => $group['id'],
                    );
                    $unavailableHidden = false;
                }
                $groupsConditions[] = 'OR';
            }
            array_pop($groupsConditions); // remove the last OR
            $findOptions['conditions'][] = $groupsConditions;
            $findOptions['order'] = array();
            if (!$unavailableHidden) {
                $findOptions['order'][] = 'EshopProduct.unavailable ASC';
            }
            if (count($groups) > 1) {
                $findOptions['order'][] = 'EshopProduct.id DESC';
            }
            else {
                // use EshopProductGroupProduct.id instead of EshopProductGroupProduct.sort as field sort is not populated
                $findOptions['order'][] = 'EshopProductGroupProduct.id ASC';
            }
            $findOptions['joins'][] = array(
                'model' => 'EshopProductGroupProduct',
                'type' => 'left',
            );
            if (count($groups) > 1) {
                $findOptions['group'] = 'EshopProduct.id';
            }
            // seo
            if ($filterOptions['set_seo']) {
                $seoData = $Group->findFirst(array(
                    'fields' => array(
                        'EshopProductGroup.name',
                        'EshopProductGroup.seo_title',
                        'EshopProductGroup.seo_keywords',
                        'EshopProductGroup.seo_description'
                    ),
                    'conditions' => array(
                        'id' => $filterOptions['filter_group']
                    )
                ));
            }
        }
        // wishlist
        if($filterOptions['filter_wishlist']) {
            $User = App::loadModel('App', 'User', true);
            // check for multiple ids or usernames are provided as semicolon separated string
            if (
                is_string($filterOptions['filter_wishlist'])
                && strpos($filterOptions['filter_wishlist'], ';') !== false
            ) {
                $filterOptions['filter_wishlist'] = array_filter(array_map(
                    'trim', explode(';', $filterOptions['filter_wishlist'])
                ));
            }
            // check if id(s) or slug(s) are provided
            if (is_array($filterOptions['filter_wishlist'])) {
                $arrayOfIds = true;
                foreach($filterOptions['filter_wishlist'] as $item) {
                    if (!Validate::intNumber($item)) {
                        $arrayOfIds = false;
                        break;
                    }
                }
                if (!$arrayOfIds) {
                    $conditionField = 'User.username';
                }
            }
            elseif (!Validate::intNumber($filterOptions['filter_wishlist'])) {
                $conditionField = 'User.username';
            }
            $filterOptions['filter_wishlist'] = $User->findList(array(
                'fields' => 'id',
                'conditions' => array(
                    $conditionField => $filterOptions['filter_wishlist']
                )
            ));
            if (!$filterOptions['filter_wishlist']) {
                return false;
            }
            $findOptions['conditions']['EshopWishlist.run_users_id']
                = $filterOptions['filter_wishlist'];
            $findOptions['joins'][] = array(
                'model' => 'EshopWishlist',
                'type' => 'left',
            );
            if (count($filterOptions['filter_wishlist']) > 1) {
                $findOptions['group'] = 'EshopProduct.id';
            }
        }
        // {$this->filterBrandParamName} user filter
        if (!empty($filterOptions[$this->filterBrandParamName])) {
            // this array of slugs will be resolved to array of ids here below
            $filterOptions['filter_brand'] = Utility::explode(
                $this->filterValuesSeparator,
                $filterOptions[$this->filterBrandParamName]
            );
        }
        // brand
        if($filterOptions['filter_brand']) {
            $Brand = $this->loadModel('EshopBrand', true);
            // check for multiple ids or slugs are provided as semicolon separated string
            if (
                is_string($filterOptions['filter_brand'])
                && strpos($filterOptions['filter_brand'], ';') !== false
            ) {
                $filterOptions['filter_brand'] = array_filter(array_map(
                    'trim', explode(';', $filterOptions['filter_brand'])
                ));
            }
            // check if id(s) or slug(s) are provided
            $conditionField = 'EshopBrand.id';
            if (is_array($filterOptions['filter_brand'])) {
                $arrayOfIds = true;
                foreach($filterOptions['filter_brand'] as $item) {
                    if (!Validate::intNumber($item)) {
                        $arrayOfIds = false;
                        break;
                    }
                }
                if (!$arrayOfIds) {
                    $conditionField = 'EshopBrand.slug';
                }
            }
            elseif (!Validate::intNumber($filterOptions['filter_brand'])) {
                $conditionField = 'EshopBrand.slug';
            }
            $filterOptions['filter_brand'] = $Brand->findList(array(
                'fields' => 'id',
                'conditions' => array(
                    $conditionField => $filterOptions['filter_brand']
                )
            ));
            $findOptions['conditions']['EshopProduct.run_eshop_brands_id']
                = $filterOptions['filter_brand'];
            // seo
            if (
                $filterOptions['set_seo']
                // ignore set_seo in case of user filtering
                && empty($filterOptions[$this->filterBrandParamName])
            ) {
                $seoData = $Brand->findFirst(array(
                    'fields' => array(
                        'EshopBrand.name',
                        'EshopBrand.seo_title',
                        'EshopBrand.seo_keywords',
                        'EshopBrand.seo_description'
                    ),
                    'conditions' => array(
                        'id' => $filterOptions['filter_brand']
                    )
                ));
            }
        }
        // avoidProduct
        if($filterOptions['avoidProduct']) {
            if (
                is_string($filterOptions['avoidProduct'])
                && strpos($filterOptions['avoidProduct'], ';') !== false
            ) {
                $filterOptions['avoidProduct'] = array_filter(array_map(
                    'trim', explode(';', $filterOptions['avoidProduct'])
                ));
            }
            if ($filterOptions['avoidProduct']) {
                $findOptions['conditions']['EshopProduct.id !=']
                    = $filterOptions['avoidProduct'];
            }
        }
        // availableOnly
        if($filterOptions['availableOnly']) {
            $findOptions['conditions']['EshopProduct.unavailable'] = false;
        }
        // discounted
        if($filterOptions['discount']) {
            $findOptions['conditions'][] = self::IS_DISCOUNTED_SQL;
            $findOptions['literals']['conditions'][] = self::IS_DISCOUNTED_SQL;
        }
        // new
        if($filterOptions['new']) {
////rblsb
//            $findOptions['conditions'][] = self::IS_NEW_SQL;
//            $findOptions['literals']['conditions'][] = self::IS_NEW_SQL;
            $findOptions['conditions']['EshopProduct.new'] = true;
        }
        // hasImage
        if($filterOptions['hasImage']) {
            $findOptions['conditions']['EshopProduct.image !='] = null;
        }
        // filter fields
        foreach ($this->filterFields as $filterField => $filterFieldOption) {
            if (!empty($filterOptions[$filterFieldOption['slug']])) {
                $filterFieldValues = Str::explode($this->filterValuesSeparator, $filterOptions[$filterFieldOption['slug']]);
                $findOptions['conditions']['EshopProduct.' . $filterField . ' %~%'] = $filterFieldValues;
            }
        }
        // výrobca
        if(!empty($filterOptions[$this->filterManufacturerParamName])) {
            $manufacturers = Str::explode($this->filterValuesSeparator, $filterOptions[$this->filterManufacturerParamName]);
            $findOptions['conditions']['EshopManufacturer.name'] = $manufacturers;
            $findOptions['joins'][] = array(
                'model' => 'EshopManufacturer',
                'type' => 'left',
                'conditions' => array(
                    'EshopManufacturer.id = EshopProduct.run_eshop_manufacturers_id',
                )
            );
        }
        // cena
        if(!empty($filterOptions[$this->filterPriceParamName])) {
            $prices = $this->parseFilterPriceParam($filterOptions[$this->filterPriceParamName]);
            if (Validate::intNumber($prices['maxPrice'])) {
                $findOptions['conditions'][] = self::ACTUAL_PRICE_SQL . '<=' . $prices['maxPrice'];
                $findOptions['literals']['conditions'][] = self::ACTUAL_PRICE_SQL . '<=' . $prices['maxPrice'];
            }
            if (Validate::intNumber($prices['minPrice'])) {
                $findOptions['conditions'][] = self::ACTUAL_PRICE_SQL . '>=' . $prices['minPrice'];
                $findOptions['literals']['conditions'][] = self::ACTUAL_PRICE_SQL . '>=' . $prices['minPrice'];
            }
        }
        // set random order
        if ($filterOptions['sort_random']) {
            $findOptions['order'] = array('EshopProduct.unavailable ASC', 'RAND()');
            $findOptions['literals']['order'][] = 'RAND()';
        }
        // set order by bestsellers
        elseif (
            $filterOptions['sort_bestsellers']
            || $filterOptions['sortBestsellers']
        ) {
            $ordersCountsFindOptions = $this->getOrdersCountsFindOptions();
            $findOptions['joins'] = array_merge($findOptions['joins'], $ordersCountsFindOptions['joins']);
            $findOptions['order'] = array(
                'EshopProduct.unavailable ASC',
                $ordersCountsFindOptions['order'][0],
                'EshopProduct.id DESC',
            );
            $findOptions['literals']['order'][] = $ordersCountsFindOptions['literals']['order'][0];
            $findOptions['group'] = $ordersCountsFindOptions['group'];
        }
        elseif ($filterOptions['sortTop']) {
            $findOptions = array_merge_recursive($findOptions, $this->getSortFindOptions('top'));
        }
        // set explicit order
        elseif ($sort) {
            $findOptions['order'] = array();
            $findOptions['literals']['order'] = array();
            array_unshift($sort, array(
                'model' => 'EshopProduct',
                'field' => 'unavailable',
                'direction' => 'ASC',
            ));
            foreach ($sort as $s) {
                // price order is always converted to actual price order
                if ($s['field'] == 'price' && $s['model'] == 'EshopProduct') {
                    $actualPriceOrder = self::ACTUAL_PRICE_SQL . ' ' . $s['direction'];
                    $findOptions['order'][] = $actualPriceOrder;
                    $findOptions['literals']['order'][] = $actualPriceOrder;
                }
                else {
                    $findOptions['order'][] = $s['model'] . '.' . $s['field'] . ' ' . $s['direction'];
                }
            }
        }
        // if no order specified till now then set default
        if (
            !$filterOptions['avoidDefaultOrder']
            && empty($findOptions['order'])
        ) {
            $findOptions['order'] = array();
            $findOptions['order'][] = 'EshopProduct.unavailable ASC';
            $findOptions['order'][] = 'IF(EshopProduct.stock > 0, 1, 0) DESC';
            $findOptions['order'][] = 'EshopProduct.name ASC';
            $findOptions['literals']['order'] = array(
                'IF(EshopProduct.stock > 0, 1, 0) DESC',
            );
        }
        // seo
        if ($filterOptions['set_seo'] && !empty($seoData)) {
            // seo title
            $seoTitle = Sanitize::value($seoData['seo_title']);
            if (empty($seoTitle)) {
                $seoTitle = Sanitize::value($seoData['name']);
            }
            App::setSeoTitle($seoTitle);
            // seo keywords
            $seoKeywords = Sanitize::value($seoData['seo_keywords']);
            App::setSeoKeywords($seoKeywords);
            // seo description
            $seoDescription = Sanitize::value($seoData['seo_description']);
            App::setSeoDescription($seoDescription);
        }

        return $findOptions;
    }

    /**
     * Gets find options to sort bestsellers.
     *
     * @param string $sort Type of sort. Following are available:
     *      - 'bestsellers'
     *      - 'top'
     *
     * @return array $findOptions options set according to the specified type. If invalid type is specified, returns
     *      empty array.
     */
    public function getSortFindOptions($sort) {
        $findOptions = array();
        if (
            $sort === 'bestsellers'
            || $sort === 'top'
        ) {
            $findOptions['joins'] = array(
                array(
                    'model' => 'EshopOrderProduct',
                    'type' => 'left',
                    'conditions' => array(
                        '`EshopProduct`.`id` = `EshopOrderProduct`.`run_eshop_products_id`'
                    )
                ),
            );
            $findOptions['order'] = array(
                'EshopProduct.unavailable ASC',
            );
            if ($sort == 'top') {
                $findOptions['order'][] = 'EshopProduct.stock > 0 DESC';
            }
            $findOptions['order'][] = 'SUM(EshopOrderProduct.bestseller_relevancy) DESC';
            $findOptions['order'][] = 'EshopProduct.id DESC';
            $findOptions['group'] = array('EshopProduct.id');
            $findOptions['literals'] = array(
                'order' => array(
                    'SUM(EshopOrderProduct.bestseller_relevancy) DESC',
                )
            );
        }
        return $findOptions;
    }

    /**
     * Gets content for actual filtering criterium (e.g. name and description
     * of category whose products are filtered)
     *
     * @param array $filterOptions
     *
     * @return array contaning keys:
     *      - 'model' (string), Model name the content is retrieved from according
     *          the filter options. E.g. 'EshopProductCategory'
     *      - 'id' (int),
     *      - 'name' (string),
     *      - 'text' (string)
     *      - 'showText' (string: enum_no, enum_before_products, enum_after_products, enum_without_products)
     */
    public function getFilterContent($filterOptions) {
        $filterDefaults = array(
            'filter' => null,
            'filter_id' => null,
            'filter_category' => null,
//            'filter_author' => null,
//            'filter_manufacturer' => null,
//            'filter_range' => null,
//            'filter_type' => null,
            'filter_group' => null,
//            'filter_wishlist' => null,
        );
        $filterOptions = array_merge($filterDefaults, $filterOptions);

        $filterOptions = $this->normalizeFilterOptions($filterOptions);

        $content = array(
            'model' => '',
            'id' => null,
            'name' => '',
            'text' => '',
            'showText' => 'enum_no'
        );
        if($filterOptions['filter_category']) {
            $Category = $this->loadModel('EshopProductCategory', true);
            if (!Validate::intNumber($filterOptions['filter_category'])) {
                $categoryId = $Category->findField('id', array(
                    'conditions' => array(
                        'EshopProductCategory.slug' => $filterOptions['filter_category']
                    )
                ));
                // if the category was found then
                if ($categoryId) {
                    $filterOptions['filter_category'] = $categoryId;
                }
            }
            $category = $Category->findFirst(array(
                'fields' => array(
                    'EshopProductCategory.id',
                    'EshopProductCategory.name',
                    'EshopProductCategory.alternative_name',
                    'EshopProductCategory.description',
                    'EshopProductCategory.show_description'
                ),
                'conditions' => array(
                    'id' => $filterOptions['filter_category']
                )
            ));
            if ($category) {
                $content['model'] = 'EshopProductCategory';
                $content['id'] = $category['id'];
                $content['name'] = $category['name'];
                $content['text'] = App::loadTextSnippets($category['description']);
                $content['showText'] = $category['show_description'];
                if ($category['alternative_name']) {
                    $content['name'] = $category['alternative_name'];
                }
            }
        }
        elseif ($filterOptions['filter_group']) {
            $Group = $this->loadModel('EshopProductGroup', true);
            if (!Validate::intNumber($filterOptions['filter_group'])) {
                $groupId = $Group->findField('id', array(
                    'conditions' => array(
                        'EshopProductGroup.slug' => $filterOptions['filter_group']
                    )
                ));
                // if the group was found then
                if ($groupId) {
                    $filterOptions['filter_group'] = $groupId;
                }
            }
            $group = $Group->findFirst(array(
                'fields' => array(
                    'EshopProductGroup.id',
                    'EshopProductGroup.name',
                    'EshopProductGroup.description',
                ),
                'conditions' => array(
                    'id' => $filterOptions['filter_group']
                )
            ));
            if ($group) {
                $content['model'] = 'EshopProductGroup';
                $content['id'] = $group['id'];
                $content['name'] = $group['name'];
                $content['text'] = App::loadTextSnippets($group['description']);
                $content['showText'] = $group['description'] ? 'enum_before_products' : 'enum_no';
            }
        }

        return $content;
    }

    /**
     * Converts price item in find $options['order'] to sql defined EshopProduct::ACTUAL_PRICE_SQL
     * which orders by actual price. If price item is not found then options are returned
     * unchanged.
     *
     * @param array $options
     * @return array Converted options.
     */
    public function convertPriceOrder2ActualPriceOrder($options) {
        $options['order'] = (array)Sanitize::value($options['order']);
        $options['literals'] = (array)Sanitize::value($options['literals']);
        $options['literals']['order'] = (array)Sanitize::value($options['literals']['order']);
        $new = array();
        foreach ($options['order'] as $k => $v) {
            $order = ($isString = is_string($k)) ? $k : $v;
            $orderParts = explode(' ', $order);
            $field = array_shift($orderParts);
            $fieldParts = explode('.', $field);
            $fieldName = array_pop($fieldParts);
            $fieldModel = array_pop($fieldParts);
            if (
                $fieldName == 'price'
                && (
                    !$fieldModel
                    || $fieldModel == $this->name)
            ) {
                $order = self::ACTUAL_PRICE_SQL;
                if (!empty($orderParts)) {
                    $order .= ' ' . implode(' ', $orderParts);
                }
                if ($isString) {
                    $order .= ' ' . $v;
                }
                $new[] = $order;
                $options['literals']['order'][] = $order;
            }
            else {
                $new[$k] = $v;
            }
        }
        $options['order'] = $new;

        return $options;
    }

    /**
     * Returns product(s) details according to provided id(s) or slug.
     * Ids (or slug) are used for sake of optimal db query time.
     * This method returns following virtual product fields:
     *  - 'disponibility' (int) set to one of disponibility constatnts of EshopProduct class
     *  - 'price_actual'
     *  - 'price_taxed'
     *  - 'price_actual_taxed'
     *  - 'savings_taxed'
     *  - 'price_taxless'
     *  - 'price_actual_taxless'
     *  - 'savings_taxless'
     *  - 'savings_rate'
     *  - 'tax'
     *  - 'tax_actual'
     *  - 'shipment_time'
     *  - 'total_shipment_time'
     *  - 'authors' (string) but only if $options['getAuthors'] is not FALSE
     *  - 'static_attributes' (array) They have following structure which can be
     *      directly used for $options['options'] of FormHelper::select():
     *
     *          array(
     *              '{attrFieldName1}' => array('value1' => 'value1', 'value2' => 'value2', 'value3' => 'value3', ...),
     *              '{attrFieldName2}' => array('value1' => 'value1', 'value2' => 'value2', 'value3' => 'value3', ...),
     *              ...
     *          )
     *
     *  - 'dynamic_attributes' (array) but only if $options['getDynamicAttributes'] is not FALSE
     *      They have following structure which can be directly used for
     *      $options['options'] of FormHelper::select():
     *
     *          array(
     *              '{attrId1}' => array('value1' => 'value1', 'value2' => 'value2', 'value3' => 'value3', ...),
     *              '{attrId2}' => array('value1' => 'value1', 'value2' => 'value2', 'value3' => 'value3', ...),
     *              ...
     *          )
     *  - 'special_offer_id' but only if $options['getSpecialOffers'] is TRUE. Contains
     *      id of special offer applicated tu product price (if any according to cart content)
     *  - 'parsed_tech_data' but only if $options['parseTechData'] is TRUE. They have
     *      following structure:
     *
     *          array(
     *              array('label' => ..., 'value' => ...),
     *              array('label' => ..., 'value' => ...),
     *              array('label' => null, 'value' => ...),     // label ca be NULL
     *              ...
     *          )
     *
     * Following models are attached to each product:
     *  - 'EshopManufacturer'
     *  - 'EshopManufacturerRange'
     *  - 'EshopProductType'
     *  - 'EshopAuthor' but only if $options['getAuthors'] is not FALSE
     *  - 'EshopProductCategory' but only if $options['getCategories'] is not FALSE
     *  - 'EshopProductImage' but only if $options['getImages'] is not FALSE
     *  - 'EshopRelatedProduct' but only if $options['getRelatedProducts'] is not FALSE
     *  - 'EshopAdditionalServiceProduct' but only if $options['getAdditionalServiceProducts'] is not FALSE
     *  - 'EshopProductAttribute' but only if $options['getDynamicAttributes'] is not FALSE
     *      Dynamic attributes of product. Attributes are parsed to following structure:
     *
     *          array(
     *              '{attrId1}' => array(
     *                  'name' =>
     *                  'values' => array('value1', 'value2', 'value3', ...),
     *                  'prices' => array(
     *                      'value1' => array('price' => 1.5, 'operator' => '+'),
     *                      'value2' => array('price' => 2, 'operator' => '+'),
     *                      ...
     *                   )
     *              )
     *              '{attrId2}' => array(...),
     *              ...
     *          )
     *  - 'DiscountingSpecialOffer' but only if $options['getSpecialOffers'] is TRUE
     *      Offers are provided as pairs {offerId} => {offerDetails}. Offer details have
     *      format returned by EshopSpecialOffer::getActive() plus two virtual boolean fields
     *      'applicable' (for offers which are applicated according to actual cart content)
     *      and 'applicated' (for offer which is applicated to product price according
     *      to actual cat conten).
     *  - 'PromotingSpecialOffer' but only if $options['getSpecialOffers'] is TRUE
     *      Offers are provided as pairs {offerId} => {offerDetails}. Offer details have
     *      format returned by EshopSpecialOffer::getActive() plus one virtual boolean field
     *      'applicable' (for offers which are applicated according to actual cart content).
     *
     *
     * @param int|string|array $id Single product id or slug or an array of product ids.
     *      Slugs cannot be passed in array. If an array of ids is passed then
     *      returned produts are in the same order as provided ids, but only if
     *      no 'order' options is defined. If there is an 'order' option defined
     *      then returned products are ordered acccording to it.
     * @param array $options Model::find() options plus following are available:
     *      - 'getManufacturer' (bool|array) If TRUE then manufacturer data (id, name, slug) are loaded.
     *          If an array then considered for list of manufacturer fields to be retrieved. Defaults to FALSE.
     *      - 'getManufacturerRange' (bool|array) If TRUE then manufacturer range data (id, name, slug) are loaded.
     *          If an array then considered for list of manufacturer range fields to be retrieved. Defaults to FALSE.
     *      - 'getProductType' (bool|array) If TRUE then product type data (id, name, pid) are loaded.
     *          If an array then considered for list of product type fields to be retrieved. Defaults to FALSE.
     *      - 'getAuthors' (bool|array) If TRUE then authors are populated. If an array then passed
     *          as options to EshopProduct::getAuthors() method. Defaults to FALSE.
     *      - 'getCategories' (bool|array) If TRUE then categories are populated. If an array then passed
     *          as options to EshopProduct::getCategories() method. Defaults to FALSE.
     *      - 'getImages' (bool|array) If TRUE thne images are populated. If an array then passed
     *          as options to EshopProduct::getImages() method. Defaults to FALSE.
     *      - 'getRelatedProducts' (bool|array) If TRUE then related products are populated. If an array then passed
     *          as options to EshopProduct::getRelatedProducts() method. Defaults to FALSE.
     *      - 'getAdditionalServiceProducts' (bool|array) If TRUE then related products are populated. If an array then passed
     *          as options to EshopProduct::getAdditionalServiceProducts() method. Defaults to FALSE.
     *      - 'getBrand' (bool|array) If TRUE the brand data (id, name, slug) are loaded.
     *      - 'getDynamicAttributes' (bool|array) If TRUE then dynamic attributes are populated. If an array then passed
     *          as options to EshopProduct::getDynamicAttributes() method. Defaults to FALSE.
     *      - 'publishedOnly' (bool) If TRUE then only published products are retrieved.
     *          See EshopProduct::getPublishedConditions() for conditions of published products.
     *          Defaults to FALSE.
     *      - 'getSpecialOffers' (bool|string) Possible values are TRUE, FALSE,
     * 'withoutSpecialPrices'.
     *          If TRUE then special offers are populated and discounted product prices are set
     *          according to special offers if thea are applicable (regardoing to cart content).
     *          If 'withoutSpecialPrices' then special offers are populated but product prices
     *          are let untouched. Defaults to FALSE.
     *      - 'loadContentBlocks' (bool) If TRUE then content blocks are loaded and
     *          appended to description. Defaults to FALSE.
     *      - 'loadDescriptionSnippets' (bool) If TRUE then product description snippets are loaded. Defaults to FALSE.
     *      - 'additionalDiscountRates' (array) Array of additional discout rates
     *          which should be applied to all retrieved products. Discount rates
     *          are given by float number or float number string or by array of pairs
     *          {productId} => {discountRate} to specify discount rates separately for each product.
     *          They are applied in provided order.
     *      - 'normalizeCode' (bool) If TRUE then product code is normalized. See phpDoc
     *          of EshopProduct::normalizeCode()
     *      - 'parseTechData' (bool) If TRUE the tech data are parsed (see the virtual field
     *          'parsed_tech_data here above). Defaults to FALSE.
     *
     * Following options can be used to define ordering of products:
     *      - 'order' (array) Order find option to order retrieved products correctly.
     *          Defauts to array()
     *      - 'joins' (array) Order related joins. Defauts to array()
     *      - 'group' (array) Order related group. Defauts to array()
     *      - 'literals' (array) Order related literals. Defauts to array()
     *
     * NOTE: If 'order' option is not defined then products are returned in the same order
     *      as passed ids.
     *
     * @return array List of product(s) detail arrays. Product ids are set as keys
     *      of array. If $id is provided as single integer (id) or string (slug)
     *      then single product record is returned.
     */
    public function getDetails($id, $options = array()) {
        $defaults = array(
            'getManufacturer' => false,
            'getManufacturerRange' => false,
            'getProductType' => false,
            'getAuthors' => false,
            'getCategories' => false,
            'getImages' => false,
            'getRelatedProducts' => false,
            'getVariantProducts' => false,
            'getAdditionalServiceProducts' => false,
            'getDynamicAttributes' => false,
            'getSpecialOffers' => false,
            'getBrand' => false,
            //'getPhotos' => false, //@todo - for case of product photogalerie
            'publishedOnly' => false,
            'loadContentBlocks' => false,
            'loadDescriptionSnippets' => false,
            'additionalDiscountRates' => array(),
            'normalizeCode' => false,
            'parseTechData' => false,
        );
        $options = array_merge($defaults, $options);

        if (empty($id)) {
            return array();
        }

        // ensure existence of folowing options
        $options['joins'] = (array)Sanitize::value($options['joins']);

        // avoid use of following options
        unset($options['limit']);
        unset($options['offset']);
        unset($options['page']);

        // set explicitly following options
        if (is_array($id) || Validate::intNumber($id)) {
            $bySlug = false;
            $options['conditions'] = array('EshopProduct.id' => $id);
        }
        else {
            $bySlug = true;
            $options['conditions'] = array('EshopProduct.slug' => $id);
        }
        if ($options['publishedOnly']) {
            $options['conditions'][] = self::getPublishedConditions();
        }
        // set fields
        $options['fields'] = array(
            'EshopProduct.id',
            'EshopProduct.code',
            'EshopProduct.ean',
            'EshopProduct.slug',
            'EshopProduct.seo_title',
            'EshopProduct.seo_description',
            'EshopProduct.seo_keywords',
            'EshopProduct.name',
            'EshopProduct.description',
            'EshopProduct.short_description',
            'EshopProduct.image',
            'EshopProduct.video_url',
            'EshopProduct.is_additional_service',
            'EshopProduct.is_gift_card',
            'EshopProduct.price',
            'EshopProduct.discount_price',
            'EshopProduct.discount_rate',
            'EshopProduct.discount_from',
            'EshopProduct.discount_to',
            self::ACTUAL_PRICE_SQL . ' AS `actual_price`',
            //rblb//self::IS_NEW_SQL . ' AS `new`',
            'EshopProduct.new',
            'EshopProduct.tax_rate',
            'EshopProduct.stock',
            'EshopProduct.units',
            'EshopProduct.stock_reserved',
            'EshopProduct.stock_location_code',
            'EshopProduct.shop_location_code',
            'EshopProduct.supplier_info',
            'EshopProduct.supplier_pid',
            'EshopProduct.availability',
            'EshopProduct.long_delivery_time',
            'EshopProduct.reprint',
            'EshopProduct.available_from',
            'EshopProduct.unavailable',
            'EshopProduct.shipment_time_off_stock',
            'EshopProduct.weight',
            'EshopProduct.width',
            'EshopProduct.height',
            'EshopProduct.length',
            'EshopProduct.dimensions',
            'EshopProduct.color',
            'EshopProduct.size',
            'EshopProduct.run_eshop_brands_id',
            'EshopProduct.material_color',
            'EshopProduct.diameter',
            'EshopProduct.marquee_height',
            'EshopProduct.extension',
            'EshopProduct.material',
            'EshopProduct.run_eshop_manufacturers_id',
            'EshopProduct.sail_width',
            'EshopProduct.in_series',
            'EshopProduct.carrying_capacity',
            'EshopProduct.pressure',
            'EshopProduct.power',
            'EshopProduct.electric_current',
            'EshopProduct.inner_frame',
            'EshopProduct.outer_frame',
            'EshopProduct.roof_thickness',
            'EshopProduct.inner_dimension',
            'EshopProduct.volume',
            'EshopProduct.voltage',
            'EshopProduct.capacity',
            'EshopProduct.variant',
            'EshopProduct.variants_lowest_price',
            'EshopProduct.variants_common_name',
            'EshopProduct.subtitle',
            'EshopProduct.active',
            'EshopProduct.run_eshop_manufacturers_id',
            'EshopProduct.oversized',
            'EshopProduct.tech_data_html',
            'EshopProduct.mrp_code',
        );
        $options['literals']['fields'] = array(self::ACTUAL_PRICE_SQL . ' AS `actual_price`');
        $options['separate'] = array();

        // get manufacturer
        if ($options['getManufacturer']) {
            $options['separate'][] = 'EshopManufacturer';
            $options['joins'][] = array(
                'model' => 'EshopManufacturer',
                'type' => 'left',
            );
            if (is_array($options['getManufacturer'])) {
                $options['fields'] = array_merge($options['fields'], $options['getManufacturer']);
            }
            else {
                $options['fields'] = array_merge($options['fields'], array(
                    'EshopManufacturer.id',
                    'EshopManufacturer.name',
                    'EshopManufacturer.slug',
                ));
            }
        }

        // get manufacturer range
        if ($options['getManufacturerRange']) {
            $options['separate'][] = 'EshopManufacturerRange';
            $options['joins'][] = array(
                'model' => 'EshopManufacturerRange',
                'type' => 'left',
            );
            if (is_array($options['getManufacturerRange'])) {
                $options['fields'] = array_merge($options['fields'], $options['getManufacturerRange']);
            }
            else {
                $options['fields'] = array_merge($options['fields'], array(
                    'EshopManufacturerRange.id',
                    'EshopManufacturerRange.name',
                    'EshopManufacturerRange.slug',
                ));
            }
        }

        // get product type
        if ($options['getProductType']) {
            $options['separate'][] = 'EshopProductType';
            $options['joins'][] = array(
                'model' => 'EshopProductType',
                'type' => 'left',
            );
            if (is_array($options['getProductType'])) {
                $options['fields'] = array_merge($options['fields'], $options['getProductType']);
            }
            else {
                $options['fields'] = array_merge($options['fields'], array(
                    'EshopProductType.id',
                    'EshopProductType.name',
                    'EshopProductType.pid',
                ));
            }
        }

        // get brand
        if ($options['getBrand']) {
            $options['separate'][] = 'EshopBrand';
            $options['joins'][] = array(
                'model' => 'EshopBrand',
                'type' => 'left',
            );
            if (is_array($options['getBrand'])) {
                $options['fields'] = array_merge($options['fields'], $options['getBrand']);
            }
            else {
                $options['fields'] = array_merge($options['fields'], array(
                    'EshopBrand.id',
                    'EshopBrand.name',
                    'EshopBrand.slug',
                ));
            }
            $this->loadModel('EshopBrand');
            $Brand = new EshopBrand();
        }

        // find products
        $products = $this->find($options);

        // if no products found and single id was passed
        // then check for possibility that it is a numeric slug
        if (!$products && Validate::intNumber($id)) {
            $bySlug = true;
            $options['conditions'] = array(
                'EshopProduct.slug' => $id
            );
            if ($options['publishedOnly']) {
                $options['conditions'][] = self::getPublishedConditions();
            }
            $products = $this->find($options);
        }

        if (!$products) {
            return array();
        }

        // if a single product was retrieved by slug then get it id
        if ($bySlug) {
            $id = $products[0]['id'];
        }

        // get authors
        if ($options['getAuthors']) {
            $authors = $this->getAuthors($id, (array)$options['getAuthors']);
        }
        // get categories
        if ($options['getCategories']) {
            $categories = $this->getCategories($id, (array)$options['getCategories']);
        }
        // get images
        if ($options['getImages']) {
            $images = $this->getImages($id, (array)$options['getImages']);
        }
        // get related products
        if ($options['getRelatedProducts']) {
            $relatedProducts = $this->getRelatedProducts($id, (array)$options['getRelatedProducts']);
        }
        // get variant products
        if ($options['getVariantProducts']) {
            $variantProducts = $this->getVariantProducts($id, (array)$options['getVariantProducts']);
        }
        // get related products
        if ($options['getAdditionalServiceProducts']) {
            $additionalServiceProducts = $this->getAdditionalServiceProducts($id, (array)$options['getAdditionalServiceProducts']);
        }
        // get dynamic attributes
        if ($options['getDynamicAttributes']) {
            $attributes = $this->getDynamicAttributes($id, (array)$options['getDynamicAttributes']);
        }
        // get special offers
        if ($options['getSpecialOffers']) {
            $promotingOffers = $this->getPromotingSpecialOffers($id);
            $applicablePromotingOffers = $this->getPromotingSpecialOffers((array)$id, array(
                'applicable' => true,
            ));
            $discountingOffers = $this->getDiscountingSpecialOffers((array)$id);
            $applicableDiscountingOffers = $this->getDiscountingSpecialOffers((array)$id, array(
                'applicable' => true,
            ));
        }

        // set products details
        if ($options['loadContentBlocks']) {
            App::loadModel('App', 'ContentBlockInstance');
            $BlockInstance = new ContentBlockInstance();
        }
        $shipmentTimeOnStock = $this->getSetting('EshopProduct.shipmentTimeOnStock');
        $shipmentTimeOffStock = $this->getSetting('EshopProduct.shipmentTimeOffStock');
        $pricesAreTaxed = $this->getSetting('pricesAreTaxed');
        $attrValuesSeparator = App::getSetting('App', 'attributeValuesSeparator');
        $attrPricesSeparator = App::getSetting('App', 'attributePricesSeparator');
        $attrPidsSeparator = App::getSetting('App', 'attributePidsSeparator');
        $additionalDiscountRateLimit = $this->getSetting('EshopProduct.additionalDiscountRateLimit');
        $additionalDiscountRateLimitForVydavatel = $this->getSetting('EshopProduct.additionalDiscountRateLimitForVydavatel');
        $vydavatelId = -999; //HARDCODED and not used on alterego
        $fileFields = array_keys($this->fileFields);
        $tmp = array();
        foreach ($products as &$product) {
            // set prices
            $product = $this->getPrices($product, array(
                'pricesAreTaxed' => $pricesAreTaxed,
                'additionalDiscountRates' => $options['additionalDiscountRates'],
                // !!! VYDAVATEL SPECIFIC additional discount rate limit
                'additionalDiscountRateLimit' => $product['run_eshop_manufacturers_id'] == $vydavatelId ?
                    $additionalDiscountRateLimitForVydavatel : $additionalDiscountRateLimit,
                'discountingSpecialOffers' =>
                    $options['getSpecialOffers'] && $options['getSpecialOffers'] !== 'withoutSpecialPrices'
                    ? (array)Sanitize::value($applicableDiscountingOffers[$product['id']]) : array(),
            ));
            // set shipment times
            $product = $this->getShipmentTimes($product, array(
                'shipmentTimeOnStock' => $shipmentTimeOnStock,
                'shipmentTimeOffStock' => $shipmentTimeOffStock,
            ));
            // set whole paths to files
            foreach ($fileFields as $fileField) {
                $product[$fileField] = $this->getFileFieldUrlPaths($fileField, array('file' => $product[$fileField]));
            }
            // normalize product code
            if ($options['normalizeCode']) {
                $product['code'] = self::normalizeCode($product['code']);
            }
            // parse selective attributes
            $product['static_attributes'] = array();
            foreach ($this->attributeFields as $fieldName => $fieldOptions) {
                if (!empty($fieldOptions['selective'])) {
                    $product[$fieldName] = $this->parseSelectiveAttribute(
                        $product[$fieldName],
                        array(
                            'priced' => !empty($fieldOptions['priced']),
                            'valuesSeparator' => $attrValuesSeparator,
                            'pricesSeparator' => $attrPricesSeparator,
                            'pidsSeparator' => $attrPidsSeparator,
                        )
                    );
                    $product['static_attributes'][$fieldName] = array();
                    if (!empty($product[$fieldName]['values'])) {
                        foreach ($product[$fieldName]['values'] as $attrPid => $attrValue) {
                            if (is_integer($attrPid)) {
                                $product['static_attributes'][$fieldName][$attrValue] = $attrValue;
                            }
                            else {
                                $product['static_attributes'][$fieldName][$attrPid] = $attrValue;
                            }
                        }
                    }
                }
            }
            // get manufacturer
            if (
                $options['getManufacturer']
                && empty($product['EshopManufacturer']['id']) // null
            ) {
                $product['EshopManufacturer'] = array();
            }
            // get manufacturer range
            if (
                $options['getManufacturerRange']
                && empty($product['EshopManufacturerRange']['id']) // null
            ) {
                $product['EshopManufacturerRange'] = array();
            }
            // get product type
            if (
                $options['getProductType']
                && empty($product['EshopProductType']['id']) // null
            ) {
                $product['EshopProductType'] = array();
            }
            // get brand
            if (
                $options['getBrand']
                && empty($product['EshopBrand']['id']) // null
            ) {
                $product['EshopBrand'] = array();
            }
            if (!empty($product['EshopBrand']['logo'])) {
                $product['EshopBrand']['logo'] = $Brand->getFileFieldUrlPath('logo', $product['EshopBrand']['logo']);
            }
            // get authors
            if ($options['getAuthors']) {
                $product['EshopAuthor'] = (array)Sanitize::value($authors[$product['id']]);
                $product['authors'] = '';
                foreach($product['EshopAuthor'] as $author) {
                    if (!empty($author['name'])) {
                        if ($product['authors']) {
                            $product['authors'] .= ', ';
                        }
                        $product['authors'] .= $author['name'];
                    }
                }
            }
            // get categories
            if ($options['getCategories']) {
                $product['EshopProductCategory'] = (array)Sanitize::value($categories[$product['id']]);
            }
            // get images
            if ($options['getImages']) {
                $product['EshopProductImage'] = (array)Sanitize::value($images[$product['id']]);
            }
            // get related products
            if ($options['getRelatedProducts']) {
                $product['EshopRelatedProduct'] = (array)Sanitize::value($relatedProducts[$product['id']]);
            }
            // get variant products
            if ($options['getVariantProducts']) {
                $product['EshopVariantProduct'] = (array)Sanitize::value($variantProducts[$product['id']]);
            }
            // get additional service products
            if ($options['getAdditionalServiceProducts']) {
                $product['EshopAdditionalServiceProduct'] = (array)Sanitize::value($additionalServiceProducts[$product['id']]);
            }
            // get dynamic attributes
            if ($options['getDynamicAttributes']) {
                $product['EshopProductAttribute'] = (array)Sanitize::value($attributes[$product['id']]);
                // populate virtual field $product['dynamic_attributes']
                $product['dynamic_attributes'] = array();
                foreach ($product['EshopProductAttribute'] as $fieldOptions) {
                    if (empty($fieldOptions['value'])) {
                        continue;
                    }
                    if ($fieldOptions['selective']) {
                        $product['dynamic_attributes'][$fieldOptions['name']] = $this->parseSelectiveAttribute(
                            $fieldOptions['value'],
                            array(
                                'priced' => !empty($fieldOptions['priced']),
                                'valuesSeparator' => $attrValuesSeparator,
                                'pricesSeparator' => $attrPricesSeparator,
                                'pidsSeparator' => $attrPidsSeparator,
                            )
                        );
                        $product['dynamic_attributes'][$fieldOptions['name']]['selective'] = 1;
                    } else {
                        $product['dynamic_attributes'][$fieldOptions['name']]['values'] = $fieldOptions['value'];
                        $product['dynamic_attributes'][$fieldOptions['name']]['selective'] = 0;
                    }
                    $product['dynamic_attributes'][$fieldOptions['name']]['description'] = $fieldOptions['description'];
                    $product['dynamic_attributes'][$fieldOptions['name']]['show_in_product_detail'] = $fieldOptions['show_in_product_detail'];
                    $product['dynamic_attributes'][$fieldOptions['name']]['columns_in_filter'] = $fieldOptions['columns_in_filter'];
                    $product['dynamic_attributes'][$fieldOptions['name']]['id'] = $fieldOptions['attr_type_id'];
                }
            }

            // set product disponibility (how it is available to clients)
            // It is set to one of disponibility constants: EshopProduct::PRESALE,
            // ::AUTOSTOCK, ::STOCK, ::SUPPLIER, ::SOLDOUT, ::ON_DEMAND
            $product['disponibility'] = $this->getDisponibility($product);

            // set special offers, see: misc/docs/implementationDetails.txt > ŠPECIÁLNE PONUKY
            if ($options['getSpecialOffers']) {
                // set discounting special offers
                $product['DiscountingSpecialOffer'] = array();
                if (!empty($discountingOffers[$product['id']])) {
                    $product['DiscountingSpecialOffer'] = $discountingOffers[$product['id']];
                    foreach ($product['DiscountingSpecialOffer'] as $offerId => &$offer) {
                        $offer['applicable'] = isset($applicableDiscountingOffers[$product['id']][$offerId]);
                        $offer['applicated'] = !empty($product['special_offer_id'])
                            && $product['special_offer_id'] === $offerId;
                    }
                    unset($offer);
                    // put the applicable offers to the beginning
                    $offerIds = array_keys($product['DiscountingSpecialOffer']);
                    foreach ($offerIds as $offerId) {
                        if (empty($product['DiscountingSpecialOffer'][$offerId]['applicable'])) {
                            $product['DiscountingSpecialOffer'] = Arr::moveKeyToEnd(
                                $product['DiscountingSpecialOffer'], $offerId
                            );
                        }
                    }
                    // put the applicated offer to the beginning
                    if (!empty($product['special_offer_id'])) {
                        $product['DiscountingSpecialOffer'] = Arr::moveKeyToStart(
                            $product['DiscountingSpecialOffer'], $product['special_offer_id']
                        );
                    }
                }
                // set promoting special offers, do it only after calculation of prices
                // as promoting offers applied by cart price are added to product according
                // to product actual price
                $product['PromotingSpecialOffer'] = array();
                if (!empty($promotingOffers['byCartPrice'])) {
                    foreach($promotingOffers['byCartPrice'] as $offerId => $offer) {
                        $product['PromotingSpecialOffer'][$offerId] = $offer;
                        $product['PromotingSpecialOffer'][$offerId]['applicable'] =
                            //HARDCODED comparison of taxed price
                            $product['price_actual_taxed'] >= $offer['cart_price_threshold'];
                            //@todo - its enough if product price is >= than missing part of free price
                    }
                }
                if (!empty($promotingOffers['byPromotedProducts'][$product['id']])) {
                    foreach ($promotingOffers['byPromotedProducts'][$product['id']] as $offerId => $offer) {
                        $product['PromotingSpecialOffer'][$offerId] = $offer;
                        $product['PromotingSpecialOffer'][$offerId]['applicable'] =
                            isset($applicablePromotingOffers['byPromotedProducts'][$product['id']][$offerId]);
                    }
                }
                if (!empty($promotingOffers['byPromotedProductsCartPrice'][$product['id']])) {
                    foreach ($promotingOffers['byPromotedProductsCartPrice'][$product['id']] as $offerId => $offer) {
                        $product['PromotingSpecialOffer'][$offerId] = $offer;
                        $product['PromotingSpecialOffer'][$offerId]['applicable'] =
                            isset($applicablePromotingOffers['byPromotedProductsCartPrice'][$product['id']][$offerId])
                            //HARDCODED comparison of taxed price
                            && $product['price_actual_taxed'] >= $offer['cart_price_threshold'];
                            //@todo - its enough if product price is >= than missing part of free price
                    }
                }
                // put the applicable offers to the beginning
                $offerIds = array_keys($product['PromotingSpecialOffer']);
                foreach ($offerIds as $offerId) {
                    if (empty($product['PromotingSpecialOffer'][$offerId]['applicable'])) {
                        $product['PromotingSpecialOffer'] = Arr::moveKeyToEnd(
                            $product['PromotingSpecialOffer'], $offerId
                        );
                    }
                }
            }
            if ($options['loadContentBlocks']) {
                $product['description'] .= $BlockInstance->loadOwnerInstancesViews('Eshop.EshopProduct', $product['id'], array(
                    'ownerRecord' => $product,
                ));
            }
            // load description snippets
            if ($options['loadDescriptionSnippets']) {
                $product['description'] = App::loadTextSnippets($product['description'], array(
                    'params' => array('_content' => $product),
                ));
            }
            // parse tech data
            if ($options['parseTechData']) {
                $techDataRows = array_filter(preg_split('#<br\s*/?>#i', $product['tech_data_html']));
                $product['parsed_tech_data'] = array();
                foreach($techDataRows as $techDataRow) {
                    $techDataRowParts = Str::explode(':', $techDataRow);
                    if (count($techDataRowParts) > 1) {
                        $product['parsed_tech_data'][] = array(
                            'label' => Str::uppercaseFirst(trim(array_shift($techDataRowParts))),
                            'value' => trim(implode(':', $techDataRowParts)),
                        );
                    }
                    else {
                        $product['parsed_tech_data'][] = array(
                            'label' => null,
                            'value' => trim($techDataRow),
                        );
                    }
                }
            }

            // reindex products by ids
            $tmp[$product['id']] = $product;
        }
        // unset reference
        unset($product);
        // replace products by reindexed products
        $products = $tmp;

        // reorder products according to passed ids, but only if order is not specified
        // explicitly by 'order' option and there is passed an array of ids
        if (
            empty($options['order'])
            && is_array($id)
        ) {
            $tmp = array();
            foreach ($id as $productId) {
                if (isset($products[$productId])) {
                    $tmp[$productId] = $products[$productId];
                }
            }
            $products = $tmp;
        }
        elseif (
            !empty($products)
            && !is_array($id)
        ) {
            $products = reset($products);
        }

        return $products;
    }

    /**
     * Returns product disponibility (= resulting availability of product to eshop user)
     *
     * @param array $product Array of product retrieved from DB containing 'stock',
     *      'availability' and possibly also 'available_from', 'id' (or 'run_eshop_products_id')
     *       'is_additional_service' and 'is_gift_card' fields. Whole product array can be passed in.
     *
     * @return int One of disponibility constants EshopProduct::STOCK, AUTOSTOCK, SUPPLIER, PRESALE, SOLDOUT
     *
     * @throws Exception on invalid $product array passed in
     */
    public function getDisponibility($product) {
        static $voucherProductId = false;
        if ($voucherProductId === false) {
            $voucherProductId = (int)$this->getSetting('EshopProduct.voucherProductId');
        }
        if (
            !isset($product['stock'])
            || !isset($product['availability'])
        ) {
            throw new Exception('Missing stock and/or availability fields in product data');
        }
        $disponibility = self::SOLDOUT; // fallback value if none of below is resolved
        if ($product['stock'] > 0) {
            $disponibility = self::STOCK;
        }
        elseif ($product['availability'] === 'enum_soldout') {
            $disponibility = self::SOLDOUT;
        }
        elseif ($product['availability'] === 'enum_on_demand') {
            $disponibility = self::ON_DEMAND;
        }
        elseif (
            $product['availability'] === 'enum_available'
            // consider also presale products to be available after their available_from
            // date is reached
            ||  (
                $product['availability'] === 'enum_presale'
                && !empty($product['available_from'])
                && $product['available_from'] <= date('Y-m-d')
            )
        ) {
            $disponibility = self::SUPPLIER;
        }
        elseif ($product['availability'] === 'enum_presale') {
            $disponibility = self::PRESALE;
        }
        // set autostock disponibility in following cases
        if (
            $disponibility !== self::SOLDOUT
            && (
                $voucherProductId
                && (
                    !empty($product['run_eshop_products_id'])
                    && $voucherProductId == (int)$product['run_eshop_products_id']
                    ||
                    empty($product['run_eshop_products_id'])
                    && !empty($product['id'])
                    && $voucherProductId == (int)$product['id']
                )
                ||
                !empty($product['is_additional_service'])
                ||
                !empty($product['is_gift_card'])
            )
        ) {
            $disponibility = self::AUTOSTOCK;
        }
        return $disponibility;
    }

    /**
     * Retrieves product(s) authors according to given id(s)
     *
     * @param int|array $productId Product id or an array of product ids
     * @param array $options Optional. Find options to precise the retrieved
     *      product(s) authors
     * @return array Array like:
     *
     *      array(
     *          '{productId1}' => array(
     *              array(
     *                  'id' => 1
     *                  'name' => 'Author Name1
     *                  'slug' => 'author-slug1
     *              ),
     *              array(
     *                  'id' => 2
     *                  'name' => 'Author Name2
     *                  'slug' => 'author-slug2
     *              ),
     *              ...
     *          ),
     *          '{productId2}' => array(),
     *          ...
     *      )
     */
    public function getAuthors($productId, $options = array()) {
        $defaults = array(
            'key' => 'EshopProductAuthor.run_eshop_products_id',
            'fields' => array(
                'EshopAuthor.id',
                'EshopAuthor.name',
                'EshopAuthor.slug',
            ),
            'conditions' => array(),
            'joins' => array(),
        );
        $options = array_merge($defaults, $options);
        // force following options
        $options['conditions']['EshopProductAuthor.run_eshop_products_id'] = $productId;
        $options['joins'][] = array(
            'model' => 'EshopProductAuthor',
            'type' => 'left',
            'conditions' => array(
                'EshopProductAuthor.run_eshop_authors_id = EshopAuthor.id',
            )
        );
        $options['accumulate'] = true;
        $options['plain'] = false;
        // find
        $Author = $this->loadModel('EshopAuthor', true);
        $authors = $Author->findList($options);
        return $authors;
    }

    /**
     * Retrieves product(s) images according to given id(s)
     *
     * @param int|array $productId Product id or an array of product ids
     * @param array $options Optional. Find options to precise the retrieved
     *      product(s) images
     * @return array Array like:
     *
     *      array(
     *          '{productId1}' => array(
     *              array(
     *                  'id' => 1
     *                  'name' => 'Image name1'
     *                  'file' => array('variant1' => ..., 'variant2' => ...)
     *              ),
     *              array(
     *                  'id' => 2
     *                  'name' => 'Image name2'
     *                  'file' => array('variant1' => ..., 'variant2' => ...)
     *              ),
     *              ...
     *          ),
     *          '{productId2}' => array(),
     *          ...
     *      )
     */
    public function getImages($productId, $options = array()) {
        $defaults = array(
            'key' => 'EshopProductImage.run_eshop_products_id',
            'fields' => array(
                'EshopProductImage.id',
                'EshopProductImage.name',
                'EshopProductImage.file',
            ),
            'conditions' => array(),
            'joins' => array(),
            'order' => array('EshopProductImage.sort ASC'),
        );
        $options = array_merge($defaults, $options);
        // force following options
        $options['conditions']['EshopProductImage.run_eshop_products_id'] = $productId;
        $options['accumulate'] = true;
        $options['plain'] = false;
        // find
        $Image = $this->loadModel('EshopProductImage', true);
        $images = $Image->findList($options);
        // set url paths of images
        foreach ($images as &$productImages) {
            foreach ($productImages as &$image) {
                $image['file'] = $Image->getFileFieldUrlPaths('file', array('file' => $image['file']));
            }
            unset($image);
        }
        unset($productImages);
        return $images;
    }

    /**
     * Gets categories the product(s) belongs to
     *
     * @param int|array $productId Product id or an array of product ids
     * @param array $options Optional. Find options to precise the retrieved
     *      product(s) categories
     *
     * @return array
     */
    public function getCategories($productId, $options = array()) {
        $defaults = array(
            'key' => 'EshopProductCategoryProduct.run_eshop_products_id',
            'fields' => array(
                'EshopProductCategory.id',
                'EshopProductCategory.name',
                'EshopProductCategory.slug',
                'EshopProductCategory.path',
            ),
            'conditions' => array(),
            'joins' => array(),
        );
        $options = array_merge($defaults, $options);
        // force following options
        $options['conditions'] = DB::nestConditions($options['conditions']);
        $options['conditions']['EshopProductCategoryProduct.run_eshop_products_id'] = $productId;
        $options['joins'][] = array(
            'model' => 'EshopProductCategoryProduct',
            'type' => 'left',
            'conditions' => array(
                'EshopProductCategoryProduct.run_eshop_product_categories_id = EshopProductCategory.id',
            )
        );
        $options['accumulate'] = true;
        $options['plain'] = false;
        // find
        $Category = $this->loadModel('EshopProductCategory', true);
        $categories = $Category->findList($options);
        return $categories;
    }

    /**
     * Gets product(s) related products
     *
     * @param int|array $productId Product id or an array of product ids
     * @param array $options Optional. Find options to precise the retrieved
     *      product(s) related products
     * @return array
     */
    public function getRelatedProducts($productId, $options = array()) {
        $defaults = array(
            'key' => 'EshopProduct.id',
            'fields' => array(
                'EshopProduct.id',
                'EshopProduct.name',
                'EshopProduct.availability',
                'EshopProduct.slug',
            ),
            'conditions' => array(),
            'joins' => array(),
        );
        $options = array_merge($defaults, $options);
        // force following options
        $options['conditions'] = DB::nestConditions($options['conditions']);
        $options['conditions']['EshopRelatedProduct.run_eshop_products_id'] = $productId;
        $options['joins'][] = array(
            'model' => 'EshopProduct',
            'type' => 'left',
            'conditions' => array(
                'EshopProduct.id = EshopRelatedProduct.run_eshop_related_products_id',
            )
        );
        $options['accumulate'] = true;
        $options['plain'] = false;
        // find
        $Related = $this->loadModel('EshopRelatedProduct', true);
        $products = $Related->findList($options);
        return $products;
    }

    /**
     * Gets product(s) variant products
     * ATTENTION: Product itself (specified by $productId) is included in returned products.
     *
     * @param int|array $productId Product id or an array of product ids
     * @param array $options Optional. Find options to precise the retrieved
     *      product(s) variant products
     *
     * @return array Array like:
     *
     *      array(
     *          '{productId1}' => array(
     *              array(
     *                  'id' => 1
     *                  'name' => 'Product Name1'
     *                  'availability' => 'enum_available'
     *                  'slug' => 'product-slug1'
     *                  'image' => array(
     *                      'tiny' => '...',
     *                      'small' => '...',
     *                      'large' => '...',
     *                      'original' => '...',
     *                   )
     *              ),
     *              array(
     *                  ...
     *              ),
     *              ...
     *          ),
     *          '{productId2}' => array(),
     *          ...
     *      )
     */
    public function getVariantProducts($productId, $options = array()) {
        $defaults = array(
            'key' => 'EshopVariantProduct.run_eshop_products_id',
            'fields' => array(
                'EshopProduct.id',
                'EshopProduct.name',
                'EshopProduct.availability',
                'EshopProduct.slug',
                'EshopProduct.image',
                'EshopProduct.color',
                'EshopProduct.size',
            ),
            'conditions' => array(),
            'joins' => array(),
            'order' => 'EshopProduct.name ASC',
        );
        $options = array_merge($defaults, $options);
        // force following options
        $options['conditions'] = DB::nestConditions($options['conditions']);
        $options['conditions']['EshopVariantProduct.run_eshop_products_id'] = $productId;
        $options['joins'][] = array(
            'model' => 'EshopProduct',
            'type' => 'left',
            'conditions' => array(
                'EshopProduct.id = EshopVariantProduct.run_eshop_variant_products_id',
            )
        );
        $options['accumulate'] = true;
        $options['plain'] = false;
        // find
        $Variant = $this->loadModel('EshopVariantProduct', true);
        $products = $Variant->findList($options);
        foreach ($products as &$productVariantProducts) {
            foreach ($productVariantProducts as &$productVariantProduct) {
                $productVariantProduct['image'] = $this->getFileFieldUrlPaths('image', array(
                    'file' => $productVariantProduct['image']
                ));
            }
            unset($productVariantProduct);
        }
        unset($productVariantProducts);
        return $products;
    }

    /**
     * Gets product(s) accessory products
     *
     * @param int|array $productId Product id or an array of product ids
     * @param array $options Optional. Find options to precise the retrieved
     *      product(s) accessory products
     * @return array
     */
    public function getAccessoryProducts($productId, $options = array()) {
        $defaults = array(
            'key' => 'EshopProduct.id',
            'fields' => array(
                'EshopProduct.id',
                'EshopProduct.name',
                'EshopProduct.availability',
                'EshopProduct.slug',
            ),
            'conditions' => array(),
            'joins' => array(),
        );
        $options = array_merge($defaults, $options);
        // force following options
        $options['conditions'] = DB::nestConditions($options['conditions']);
        $options['conditions']['EshopAccessoryProduct.run_eshop_products_id'] = $productId;
        $options['joins'][] = array(
            'model' => 'EshopProduct',
            'type' => 'left',
            'conditions' => array(
                'EshopProduct.id = EshopAccessoryProduct.run_eshop_accessory_products_id',
            )
        );
        $options['accumulate'] = true;
        $options['plain'] = false;
        // find
        $Accessory = $this->loadModel('EshopAccessoryProduct', true);
        $products = $Accessory->findList($options);
        return $products;
    }

    /**
     * Gets product(s) additional service products
     *
     * @param int|array $productId Product id or an array of product ids
     * @param array $options Optional. EshopProduct::getDetails() options to precise
     *      the retrieved product(s) additional service products
     * @return array
     */
    public function getAdditionalServiceProducts($productId, $options = array()) {
////rbcb
//        $defaults = array(
//            'key' => 'EshopAdditionalServiceProduct.run_eshop_products_id',
//            'fields' => array(
//                'EshopProduct.id',
//                'EshopProduct.name',
//                'EshopProduct.is_additional_service',
//                'EshopProduct.slug',
//            ),
//            'conditions' => array(),
//            'joins' => array(),
//        );
//        $options = array_merge($defaults, $options);
//        // force following options
//        $options['conditions'] = DB::nestConditions($options['conditions']);
//        $options['conditions']['EshopAdditionalServiceProduct.run_eshop_products_id'] = $productId;
//        $options['conditions']['EshopProduct.is_additional_service !='] = null;
//        $options['joins'][] = array(
//            'model' => 'EshopProduct',
//            'type' => 'left',
//            'conditions' => array(
//                'EshopProduct.id = EshopAdditionalServiceProduct.run_eshop_additional_service_products_id',
//            )
//        );
//        $options['accumulate'] = true;
//        $options['plain'] = false;
//        // find
//        $AdditionalService = $this->loadModel('EshopAdditionalServiceProduct', true);
//        $products = $AdditionalService->findList($options);

        // for the moment the additional services are defined globally,
        // there are no HABTM relations to existing products. So simulate here
        // the HABTM relations. If in future there will be real HABTM relations
        // you will just uncomment the above code and remove code here below.
        $additionalServiceProductIds = $this->findList(array(
            'key' => 'EshopProduct.id',
            'fields' => array('EshopProduct.id'),
            'conditions' => array(
                self::getPublishedConditions(),
                'EshopProduct.unavailable' => false,
                'EshopProduct.is_additional_service !=' => null,
            )
        ));
        $options['getAdditionalServiceProducts'] = false;
        $additionalServiceProductDetails = $this->getDetails($additionalServiceProductIds, $options);
        $products = array();
        if ($additionalServiceProductDetails) {
            $ownerProducts = $this->find(array(
                'fields' => array(
                    'EshopProduct.id',
                    'EshopProduct.is_additional_service'
                ),
                'conditions' => array(
                    'EshopProduct.id' => $productId,
                ),
            ));
            foreach ($ownerProducts as $product) {
                if (empty($product['is_additional_service'])) {
                    $products[$product['id']] = $additionalServiceProductDetails;
                }
            }
        }

        return $products;
    }

    /**
     * Returns list of product ids which have been bought together with specified
     * $productId. Product ids are ordered by count of occurences in orders.
     *
     * @param int $productId
     * @param array $options Find options to specify 'conditions' and 'joins'.
     *      All other find options are specified explicitly by the method. Join
     *      with EshopOrderProduct is made implicitly. Use qualified field names
     *      in 'conditions'.
     *
     * @return array List of product ids.
     */
    public function getBoughtWithProductIds($productId, $options = array()) {
        $defaults = array(
            'conditions' => array(),
            'joins' => array(),
            'literals' => array(),
        );
        $options = array_merge($defaults, $options);
        $OrderProduct = $this->loadModel('EshopOrderProduct', true);
        // get orders of specified $productId
        $orderIds = $OrderProduct->findList(array(
            'key' => 'EshopOrderProduct.run_eshop_orders_id',
            'fields' => array(
                'EshopOrderProduct.run_eshop_orders_id'
            ),
            'conditions' => array(
                'EshopOrderProduct.run_eshop_products_id' => $productId
            )
        ));
        if (empty($orderIds)) {
            return array();
        }
        // get product ids bought together with specified $productId
        // - force following options
        $options['key'] = 'EshopProduct.id';
        $options['fields'] = array('EshopProduct.id');
        $options['conditions'] = DB::nestConditions($options['conditions']);
        $options['conditions']['EshopOrderProduct.run_eshop_orders_id'] = $orderIds;
        $options['conditions']['EshopOrderProduct.run_eshop_products_id !='] = $productId;
        $options['conditions']['EshopProduct.id !='] = null;
        $options['group'] = 'EshopOrderProduct.run_eshop_products_id';
        $options['order'] = array(
            'COUNT(EshopOrderProduct.run_eshop_orders_id) DESC',
            'EshopOrderProduct.run_eshop_products_id DESC',
        );
        $options['literals']['order'] = array(
            'COUNT(EshopOrderProduct.run_eshop_orders_id) DESC',
        );
        $options['joins'][] = array(
            'model' => 'EshopProduct',
            'type' => 'left',
            'conditions' => array(
                'EshopProduct.id = EshopOrderProduct.run_eshop_products_id',
            )
        );
        return $OrderProduct->findList($options);
    }

    /**
     * Returns list of product ids which have the same author together with specified
     * $productId. Product ids are ordered from newest.
     *
     * @param int $productId
     * @param array $options Find options to specify 'conditions' and 'joins'.
     *      All other find options are specified explicitly by the method. Join
     *      with EshopProductAuthor is made implicitly. Use qualified field names
     *      in 'conditions'.
     *
     * @return array List of product ids.
     */
    public function getFromTheSameAuthorProductIds($productId, $options = array()) {
        $defaults = array(
            'conditions' => array(),
            'joins' => array(),
            'literals' => array(),
        );
        $options = array_merge($defaults, $options);
        $ProductAuthor = $this->loadModel('EshopProductAuthor', true);
        // get orders of specified $productId
        $authorIds = $ProductAuthor->findList(array(
            'key' => 'EshopProductAuthor.run_eshop_authors_id',
            'fields' => array(
                'EshopProductAuthor.run_eshop_authors_id'
            ),
            'conditions' => array(
                'EshopProductAuthor.run_eshop_products_id' => $productId
            )
        ));
        if (empty($authorIds)) {
            return array();
        }
        // get product ids bought together with specified $productId
        // - force following options
        $options['key'] = 'EshopProduct.id';
        $options['fields'] = array('EshopProduct.id');
        $options['conditions'] = DB::nestConditions($options['conditions']);
        $options['conditions']['EshopProductAuthor.run_eshop_authors_id'] = $authorIds;
        $options['conditions']['EshopProductAuthor.run_eshop_products_id !='] = $productId;
        $options['order'] = array(
            'EshopProductAuthor.run_eshop_products_id DESC',
        );
        $options['joins'][] = array(
            'model' => 'EshopProduct',
            'type' => 'left',
            'conditions' => array(
                'EshopProduct.id = EshopProductAuthor.run_eshop_products_id',
            )
        );
        return $ProductAuthor->findList($options);
    }

    /**
     * Gets product(s) dynamic attributes
     *
     * @param int|array $productId Product id or an array of product ids
     * @param array $options Optional. Find options to precise the retrieved
     *      product(s) dynamic attributes
     * @return array
     *
     * @todo Load also attribute name from EshopProductAttributeType
     */
    public function getDynamicAttributes($productId, $options = array()) {
        $defaults = array(
            'key' => 'EshopProductAttribute.run_eshop_products_id',
            'fields' => array(
                'EshopProductAttribute.id',
                'EshopProductAttribute.value',
                'EshopProductAttributeType.id as attr_type_id',
                'EshopProductAttributeType.selective',
                'EshopProductAttribute.priced',
                'EshopProductAttributeType.name',
                'EshopProductAttributeType.description',
                'EshopProductAttributeType.show_in_product_detail',
                'EshopProductAttributeType.columns_in_filter',
            ),
            'conditions' => array(),
            'joins' => array(),
        );
        $options = array_merge($defaults, $options);
        // force following options
        $options['conditions']['EshopProductAttribute.run_eshop_products_id'] = $productId;
        $options['joins'][] = array(
            'model' => 'EshopProductAttributeType',
            'type' => 'left',
            'conditions' => array(
                'EshopProductAttributeType.id = EshopProductAttribute.run_eshop_product_attribute_types_id',
            )
        );
        $options['accumulate'] = true;
        $options['plain'] = false;
        // find
        $Attribute = $this->loadModel('EshopProductAttribute', true);
        $attributes = $Attribute->findList($options);
        return $attributes;
    }

    /**
     * Retrieves promoting special offers according to given product id(s).
     *
     * ATTENTION: Returned result contains also offers applied by cart price threshold.
     * See the returned array example.
     *
     * See also misc/docs/implementationDetails.txt > ŠPECIÁLNE PONUKY and
     * EshopCart.php > comment block starting "// SPECIAL OFFERS"
     *
     * @param int|array $productId Product id or an array of product ids
     * @param array $options Following are available:
     *      - 'applicable' (bool) If TRUE then only promoting offers applicable
     *          according to actual cart products are returned for specified product ids.
     *          ATTENTION: Offers applied by cart price threshold are returned all
     *          which discounts some of cart products and it is up to user to resolve
     *          which apply also to specified product ids comparing cart price threshold and
     *          product actual taxed price! Defaults to FALSE, it means all promoting
     *          offers of specified product ids are returned.
     * @return array Array like:
     *      array(
     *          'byPromotedProducts' => array(
     *              '{productId1}' => array(
     *                  '{offerId1}' => array(
     *                      // array of offer details returned by EshopSpecialOffer::getActive()
     *                  ),
     *                  '{offerId2}' => array(
     *                      // array of offer details returned by EshopSpecialOffer::getActive()
     *                  ),
     *                  ...
     *              ),
     *              '{productId2}' => array(),
     *              ...
     *          ),
     *          'byPromotedProductsCartPrice' => array(
     *              '{productId3}' => array(
     *                  '{offerId3}' => array(
     *                      // array of offer details returned by EshopSpecialOffer::getActive()
     *                  ),
     *                  '{offerId4}' => array(
     *                      // array of offer details returned by EshopSpecialOffer::getActive()
     *                  ),
     *                  ...
     *              ),
     *              '{productId4}' => array(),
     *              ...
     *          ),
     *          'byCartPrice' => array(
     *              '{offerId5}' => array(
     *                  // array of offer details returned by EshopSpecialOffer::getActive()
     *              ),
     *              '{offerId6}' => array(),
     *              ...
     *          )
     *      )
     *
     * ATTENTION: If no offers are found then an empty array is returned
     */
    public function getPromotingSpecialOffers($productId, $options = array()) {
        $defaults = array(
            'applicable' => false,
        );
        $options = array_merge($defaults, $options);
        $offerOptions = array(
            'promotedProductIds' => $productId,
            'cartPrice' => true,
            'ordered' => true,
            'getDetails' => true,
        );
        if ($options['applicable']) {
            $this->loadModel('EshopCart');
            $Cart = new EshopCart();
            $offerOptions['discountedProductIds'] = $Cart->getProductIdsWithoutAppliedSpecialOffer();
        }
        $this->loadModel('EshopSpecialOffer');
        $Offer = new EshopSpecialOffer();
        $offers = $Offer->getActive($offerOptions);
        if (!$offers) {
            return array();
        }
        $promotingOffers = array(
            'byPromotedProducts' => array(),
            'byPromotedProductsCartPrice' => array(),
            'byCartPrice' => array(),
        );
        foreach($offers as $offerId => $offer) {
            if ($offer['apply_by'] === 'promoted_products_in_cart') {
                foreach($offer['matched_promoted_product_ids'] as $promotedProductId) {
                    $promotingOffers['byPromotedProducts'][$promotedProductId][$offerId] = $offer;
                }
            }
            elseif ($offer['apply_by'] === 'promoted_products_cart_price_threshold') {
                foreach($offer['matched_promoted_product_ids'] as $promotedProductId) {
                    $promotingOffers['byPromotedProductsCartPrice'][$promotedProductId][$offerId] = $offer;
                }
            }
            elseif ($offer['apply_by'] === 'cart_price_threshold') {
                $promotingOffers['byCartPrice'][$offerId] = $offer;
            }
            else {
                throw new Exception (__e(
                    __FILE__,
                    'Invalid apply_by definition %s for special offer %s',
                    $offer['apply_by'],
                    $offerId
                ));
            }
        }
        //unset($offer);
        return $promotingOffers;
    }

    /**
     * Retrieves discounting special offers according to given product id(s).
     *
     * @see: misc/docs/implementationDetails.txt > ŠPECIÁLNE PONUKY and
     * EshopCart.php > comment block starting "// SPECIAL OFFERS"
     *
     * @param int|array $productId Product id or an array of product ids
     * @param array $options Following are available:
     *      - 'applicable' (bool) If TRUE then only discounting offers applicable
     *          accordint to actual cart content are returned for specified product ids.
     *          Defaults to FALSE, it means all discounting offers of specified product
     *          ids are returned.
     *
     * @return array Array like:
     *
     *      array(
     *          '{productId1}' => array(
     *              '{offerId1}' => array(
     *                  // array of offer details returned by EshopSpecialOffer::getActive()
     *              ),
     *              '{offerId2}' => array(
     *                  // array of offer details returned by EshopSpecialOffer::getActive()
     *              ),
     *              ...
     *          ),
     *          '{productId2}' => array(),
     *          ...
     *      )
     *
     * ATTENTION: If $productId is provided as single integer then array of single product
     * offers is returned:
     *
     *      array(
     *          '{offerId1}' => array(
     *              // array of offer details returned by EshopSpecialOffer::getActive()
     *          ),
     *          '{offerId2}' => array(
     *              // array of offer details returned by EshopSpecialOffer::getActive()
     *          ),
     *          ...
     *      )
     */
    public function getDiscountingSpecialOffers($productId, $options = array()) {
        $defaults = array(
            'applicable' => false,
        );
        $options = array_merge($defaults, $options);
        $offerOptions = array(
            'discountedProductIds' => $productId,
            'ordered' => true,
            'getDetails' => true,
        );
        if ($options['applicable']) {
            $this->loadModel('EshopCart');
            $Cart = new EshopCart();
            $offerOptions['promotedProductIds'] = $Cart->getProductIdsWithoutAppliedSpecialOffer();
            $offerOptions['cartPrice'] = $Cart->getPriceWithoutAppliedSpecialOffer();
        }
        $this->loadModel('EshopSpecialOffer');
        $Offer = new EshopSpecialOffer();
        $offers = $Offer->getActive($offerOptions);
        $discountingOffers = array();
        foreach($offers as $offerId => $offer) {
            foreach($offer['matched_discounted_product_ids'] as $discountedProductId) {
                $discountingOffers[$discountedProductId][$offerId] = $offer;
            }
        }
        //unset($offer);
        if (!is_array($productId)) {
            $discountingOffers = reset($discountingOffers);
        }
        return $discountingOffers;
    }

    /**
     * @param array $product Product record containing at least 'shipment_time_off_stock'
     *      and 'available_from' fields
     * @param array $options
     *
     * @return array Product array with added virtual fields 'shipment_time' and 'total_shipment_time'
     */
    public function getShipmentTimes($product, $options = array()) {
        $defaults = array(
            'shipmentTimeOnStock' => null,
            'shipmentTimeOffStock' => null,
        );
        $options = array_merge($defaults, $options);
        if ($options['shipmentTimeOnStock'] === null) {
            $options['shipmentTimeOnStock'] = $this->getSetting('EshopProduct.shipmentTimeOnStock');
        }
        if ($options['shipmentTimeOffStock'] === null) {
            $options['shipmentTimeOffStock'] = $this->getSetting('EshopProduct.shipmentTimeOffStock');
        }

        // set shipment times
        if (isset($product['stock']) && $product['stock'] > 0) {
            $product['shipment_time'] = $options['shipmentTimeOnStock'];
        }
        elseif(!empty($product['shipment_time_off_stock'])) {
            $product['shipment_time'] = (int)$product['shipment_time_off_stock'];
        }
        else {
            $product['shipment_time'] = $options['shipmentTimeOffStock'];
        }
        // get total shipment time according to availability
        $product['total_shipment_time'] = $product['shipment_time'];
        if (
            !empty($product['available_from'])
            && ($days = Date::getDiff('d', date('Y-m-d'), $product['available_from'])) > 0
        ) {
            $product['total_shipment_time'] += $days;
        }

        return $product;
    }

    /**
     * Calculates all suplementary prices and price infos from the supplied price data
     * and sets then in provided product array. Following prices are set:
     *      - 'price_taxed'
     *      - 'price_actual_taxed'
     *      - 'savings_taxed'
     *      - 'price_taxless'
     *      - 'price_actual_taxless'
     *      - 'savings_taxless'
     *      - 'savings_rate'
     *      - 'tax'
     *      - 'tax_actual'
     *      - 'special_offer_id'
     *
     * @param array $product Array of product prices containing 'price', 'discount_price',
     *      'discount_from', 'discount_to' eventually 'actual_price'. Whole product array
     *      can be passed in.
     * @param array $options You may specify the following:
     *      - 'pricesAreTaxed' (bool) If TRUE then product prices are considered to be provided
     *          as taxed. If FALSE then they are considered to be taxless. Defaults to NULL means
     *          that setting Eshop.pricesAreTaxed is loaded to found this.
     *      - 'precision' (int) All numbers will be rounded to this precision. Defaults to 2.
     *      - 'additionalDiscountRates' (array) Array of additional discout rates
     *          which soud be applied to all retrieved products. Discount rates
     *          are given by float number or float number string or by array of pairs
     *          {productId} => {discountRate} to specify discount rates separately for each product.
     *          They are applied in provided order.
     *      - 'additionalDiscountRateLimit' (float) Resulting discount rate,
     *          after additional discount rates are applied, is limited by this value.
     *          But the primary product discount rate is not limited by this value.
     *          It means that if primary discount is 30% then additionalDiscountRateLimit 15% will
     *          not descrease resulting discount under 30%. Defaults to setting EshopProduct.additionalDiscountRateLimit.
     *      - 'discountingSpecialOffers' (array) Array of discounting special offers
     *          containing pairs {specialOfferId} => {specialOfferDetailsArray}.
     *          If provided then the offer giving the best price is applied/used
     *          to calculate actual price and its id is set as product virtual field
     *          'special_offer_id'. ATTENTION: All provided special offers are
     *          considered to be applicable. It means their activity or other relation to
     *          provided $product is not checked here. It is up to user calling this
     *          method. ATTENTION: If provided then any other disocunts and discount limit
     *          are ignored. Defaults to empty array().
     *
     * @return array Array contaning suplementary prices and price infos.
     *
     * @todo Unify 'discount_rate' and 'savings_rate' - 2 items for the same thing
     */
    public function getPrices($product, $options = array()) {
        $defaults = array(
            'pricesAreTaxed' => null,
            'precision' => 2,
        );
        $options = array_merge($defaults, $options);
        if ($options['pricesAreTaxed'] === null) {
            $options['pricesAreTaxed'] = $this->getSetting('pricesAreTaxed');
        }

        // to make following calculations correct round the input prices  to required precision first
        $product['price'] = round($product['price'], $options['precision']);
        if (isset($product['actual_price'])) {
            $product['actual_price'] = round($product['actual_price'], $options['precision']);
        }
        if (!empty($product['discount_price'])) {
            $product['discount_price'] = round($product['discount_price'], $options['precision']);
        }

        // calculate prices and savings
        $specialOfferId = null;
        if ($options['pricesAreTaxed']) {
            // calculate taxed prices & savings
            $product['price_taxed'] = round($product['price'], $options['precision']);
            $product['price_actual_taxed'] = $this->getActualPrice($product, $options, $specialOfferId);
            $product['special_offer_id'] = $specialOfferId;
            $product['actual_price'] = $product['price_actual_taxed'];
            $product['savings_taxed'] = $this->getSavings($product, $options);
            // calculate taxless prices & savings
            $product['price_taxless'] = Number::getTaxlessPrice($product['price'], $product['tax_rate'], $options['precision']);
            $product['price_actual_taxless'] = Number::getTaxlessPrice($product['price_actual_taxed'], $product['tax_rate'], $options['precision']);
            $product['savings_taxless'] = Number::getTaxlessPrice($product['savings_taxed'], $product['tax_rate'], $options['precision']);
        }
        else {
            // calculate taxless prices & savings
            $product['price_taxless'] = round($product['price'], $options['precision']);
            $product['price_actual_taxless'] = $this->getActualPrice($product, $options); // after discount
            $product['special_offer_id'] = $specialOfferId;
            $product['actual_price'] = $product['price_actual_taxless'];
            $product['savings_taxless'] = $this->getSavings($product, $options); // price - price after discount
            // calculate taxed prices & savings
            $product['price_taxed'] = Number::getTaxedPrice($product['price'], $product['tax_rate'], $options['precision']);
            $product['price_actual_taxed'] = Number::getTaxedPrice($product['price_actual_taxless'], $product['tax_rate'], $options['precision']);
            $product['savings_taxed'] = Number::getTaxedPrice($product['savings_taxless'], $product['tax_rate'], $options['precision']);
        }
        $product['savings_rate'] = $this->getSavingsRate($product, $options);
        $product['discount_rate'] = $product['savings_rate'];

        $product['tax'] = round($product['price_taxed'] - $product['price_taxless'], $options['precision']);
        $product['tax_actual'] = round($product['price_actual_taxed'] - $product['price_actual_taxless'], $options['precision']);

        return $product;
    }

    /**
     * Calculates product's actual price from the supplied price data taking
     * into account possible discount. If there is already defined 'actual_price'
     * item in prices then this is just returned. If 'discount_price' and 'discount_rate'
     * are defined both then 'dicount_price' is used to calculate actual price.
     *
     * @param array $prices Array of product prices containing 'price', 'discount_price',
     *      'discount_from', 'discount_to' eventually 'actual_price'.
     * @param array $options You may specify the following:
     *      - 'precision' (int) All numbers will be rounded to this precision.
     *          If FALSE then no rounding is made. Defaults to 2.
     *      - 'discountPricePrecision' (int) Discounted price will be rounded to this precision.
     *          If actual price equals to full price then this precision does not apply.
     *          If FALSE then no rounding is made. Defaults to setting 'EshopProduct.discountPriceDecimals'.
     *      - 'additionalDiscountRates' (array) Array of additional discout rates
     *          which soud be applied to all retrieved products. Discount rates
     *          are given by float number or float number string or by array of pairs
     *          {productId} => {discountRate} to specify discount rates separately for each product.
     *          They are applied in provided order (but it doesn't matter).
     *      - 'additionalDiscountRateLimit' (float) Resulting discount rate,
     *          after additional discount rates are applied, is limited by this value.
     *          But the primary product discount rate is not limited by this value.
     *          It means that if primary discount is 30% then additionalDiscountRateLimit 15% will
     *          not descrease resulting discount under 30%. Defaults to setting EshopProduct.additionalDiscountRateLimit.
     *      - 'discountingSpecialOffers' (array) Array of discounting special offers
     *          containing pairs {specialOfferId} => {specialOfferDetailsArray}.
     *          If provided then the offer giving the best price is applied/used
     *          to calculate actual price and its id is returned by aux output
     *          $appliedSpecialOfferId. ATTENTION: All provided special offers are
     *          considered to be applicable. It means their activity or other relation to
     *          provided $prices is not checked here. It is up to user calling this
     *          method. ATTENTION: If provided then any other disocunts and discount limit
     *          are ignored. Defaults to empty array().
     * @param int& $specialOfferId Optional aux output passed by reference. If 'discountingSpecialOffers'
     *          are provided then this is set to id of applied discounting special offer.
     *
     * @return float Actual price rounded to provided precision.
     */
    public function getActualPrice($prices, $options = array(), &$specialOfferId = null) {
        static $time = null;
        static $discountPriceDecimals = false;
        static $additionalDiscountRateLimit = false;
        if ($discountPriceDecimals === false) {
            $discountPriceDecimals = $this->getSetting('EshopProduct.discountPriceDecimals');
        }
        if ($additionalDiscountRateLimit === false) {
            $additionalDiscountRateLimit = $this->getSetting('EshopProduct.additionalDiscountRateLimit');
        }
        $defaults = array(
            'precision' => 2,
            'discountPricePrecision' => $discountPriceDecimals,
            'additionalDiscountRates' => array(),
            'additionalDiscountRateLimit' => $additionalDiscountRateLimit,
            'discountingSpecialOffers' => array(),
        );
        $options = array_merge($defaults, $options);
        $options['additionalDiscountRateLimit'] = (float)$options['additionalDiscountRateLimit'];

        // if the there is already actual_price (processed in DB) and there are
        // no additional discounts to be applied then do nothing
        $discounted = false;
        if (!empty($options['discountingSpecialOffers'])) {
            $prices['price'] = (float)Sanitize::value($prices['price']);
            $this->loadModel('EshopSpecialOffer');
            $specialPrices = array();
            foreach ($options['discountingSpecialOffers'] as $offer) {
                $specialPrices[$offer['id']] = (float)EshopSpecialOffer::applyPriceAdjustment(
                    $prices['price'], $offer['discounted_products_price_adjustment']
                );
            }
            asort($specialPrices, SORT_NUMERIC);
            $actualPrice = reset($specialPrices);
            $specialOfferId = key($specialPrices);
            $discounted = $actualPrice != $prices['price'];
        }
        else {
            if (
                isset($prices['actual_price'])
                && empty($options['additionalDiscountRates'])
            ) {
                $actualPrice = $prices['actual_price'];
                $discounted = $actualPrice != Sanitize::value($prices['price']);
            }
            else {
                if (is_null($time)) {
                    $time = time() - (60 * 60 * 24);
                }
                $prices['price'] = (float)Sanitize::value($prices['price']);
                if (
                    !empty($prices['discount_price'])
                    && (
                        empty($prices['discount_from'])
                        || strtotime($prices['discount_from']) <= $time
                    )
                    && (
                        empty($prices['discount_to'])
                        || $time <= strtotime($prices['discount_to'])
                    )
                ) {
                    $actualPrice = $prices['discount_price'];
                    if (
                        !empty($options['additionalDiscountRates'])
                        && !empty($options['additionalDiscountRateLimit'])
                    ) {
                        $primaryDiscount = $prices['price'] - $actualPrice;
                        $primaryDiscountRate = 100 * ($primaryDiscount / $prices['price']);
                    }
                    $discounted = true;
                }
                elseif (
                    !empty($prices['discount_rate'])
                    && (
                        empty($prices['discount_from'])
                        || strtotime($prices['discount_from']) <= $time
                    )
                    && (
                        empty($prices['discount_to'])
                        || $time <= strtotime($prices['discount_to'])
                    )
                ) {
                    $actualPrice = $prices['price'] * ((100 - $prices['discount_rate']) / 100);
                    if (
                        !empty($options['additionalDiscountRates'])
                        && !empty($options['additionalDiscountRateLimit'])
                    ) {
                        $primaryDiscountRate = $prices['discount_rate'];
                    }
                    $discounted = true;
                }
                else {
                    $actualPrice = $prices['price'];
                    if (
                        !empty($options['additionalDiscountRates'])
                        && !empty($options['additionalDiscountRateLimit'])
                    ) {
                        $primaryDiscountRate = 0;
                    }
                }
            }
            // apply additional discount rates
            if (!empty($options['additionalDiscountRates'])) {
                $additionalDiscountRate = 0;
                foreach ($options['additionalDiscountRates'] as $discountRate) {
                    if (is_array($discountRate)) {
                        if (empty($discountRate[$prices['id']])) {
                            continue;
                        }
                        $discountRate = $discountRate[$prices['id']];
                    }
                    if (
                        !is_numeric($discountRate)
                        || (float)$discountRate <= 0
                        || (float)$discountRate > 100
                    ) {
                        continue;
                    }
                    if (empty($additionalDiscountRate)) {
                        $additionalDiscountRate = $discountRate;
                    }
                    // accumulate 2 discounts: D = d1 + d2 - d1*d2/100
                    else {
                        $additionalDiscountRate = $additionalDiscountRate + $discountRate - $additionalDiscountRate * $discountRate / 100;
                    }
                }
                if (!empty($additionalDiscountRate)) {
                    if (empty($options['additionalDiscountRateLimit'])) {
                        $actualPrice *= ((100 - $additionalDiscountRate) / 100);
                    }
                    elseif ($options['additionalDiscountRateLimit'] > $primaryDiscountRate) {
                        // if final discount rate is over limit then calculate limited additional discount: ad = 100 * (D - pd) / (100 - pd)
                        $finalDiscountRate = $additionalDiscountRate + $primaryDiscountRate - $additionalDiscountRate * $primaryDiscountRate / 100;
                        if ($finalDiscountRate > $options['additionalDiscountRateLimit']) {
                            $additionalDiscountRate = ($options['additionalDiscountRateLimit'] - $primaryDiscountRate) * 100 / (100 - $primaryDiscountRate);
                        }
                        $actualPrice *= ((100 - $additionalDiscountRate) / 100);
                    }
                    $discounted = true;
                }
            }
        }
        // apply precision
        if (
            !$discounted
            && $options['precision'] === false
            ||
            $discounted
            && $options['discountPricePrecision'] === false
        ) {
            return $actualPrice;
        }
        if (!$discounted) {
            return round($actualPrice, $options['precision']);
        }
        return Number::roundDown($actualPrice, $options['discountPricePrecision']);
    }

    /**
     * Calculates how much is saved on a single product.
     *
     * @param array $prices Array of product prices containing 'price', 'discount_price',
     *      'discount_from', 'discount_to' eventually 'actual_price'.
     * @param array $options You may specify the following:
     *      - 'precision' (int) All numbers will be rounded to this precision. Defaults to 2.
     *
     * @return float Savings rounded to provided precision.
     */
    public function getSavings($prices, $options = array()) {
        static $time = null;
        $defaults = array(
            'precision' => 2,
        );
        $options = array_merge($defaults, $options);

        $prices['price'] = (float)Sanitize::value($prices['price']);

        // if the there is already actual_price (processed in DB) then use it to count savings
        if (isset($prices['actual_price'])) {
            $savings = $prices['price'] - $prices['actual_price'];
        }
        else {
            if (is_null($time)) {
                $time = time() - (60 * 60 * 24);
            }

            if (
                !empty($prices['discount_price'])
                && (
                    empty($prices['discount_from'])
                    || strtotime($prices['discount_from']) <= $time
                )
                && (
                    empty($prices['discount_to'])
                    || $time <= strtotime($prices['discount_to'])
                )
            ) {
                $savings = $prices['price'] - $prices['discount_price'];
            }
            elseif (
                !empty($prices['discount_rate'])
                && (
                    empty($prices['discount_from'])
                    || strtotime($prices['discount_from']) <= $time
                )
                && (
                    empty($prices['discount_to'])
                    || $time <= strtotime($prices['discount_to'])
                )
            ) {
                $savings = $prices['price'] - $prices['price'] * (1 - $prices['discount_rate'] / 100);
            }
            else {
                $savings = 0;
            }
        }
        return round($savings, $options['precision']);
    }

    /**
     * Calculates rate (%) of the savings on the supplied product.
     *
     * @access public
     * @param array $prices Array of product prices containing 'price', 'discount_price',
     *      'discount_from', 'discount_to' eventually 'actual_price' and/or 'savings'.
     * @param array $options You may specify the following:
     *      - 'precision' (int) All numbers will be rounded to this precision. Defaults to 2.
     *
     * @return float Savings rate rounded to provided precision.
     */
    public function getSavingsRate($prices, $options = array()) {
        static $time = null;
        $defaults = array(
            'precision' => 2,
        );
        $options = array_merge($defaults, $options);

        $prices['price'] = (float)Sanitize::value($prices['price']);

        // if the standard price is empty then return 0.0 for following reasons:
        //  - it is not possible to have any savings on standard price 0.0
        //  - we must avoid possible division by 0
        if (empty($prices['price'])) {
            $savingsRate = 0.0;
        }
        // if the there are already savings then use them to count savings rate
        elseif (!empty($prices['savings'])) {
            $savingsRate = 100 * ($prices['savings'] / $prices['price']);
        }
        else {
            if (is_null($time)) {
                $time = time() - (60 * 60 * 24);
            }
            if (isset($prices['actual_price'])) {
                $savings = $prices['price'] - $prices['actual_price'];
                $savingsRate = 100 * ($savings / $prices['price']);
            }
            elseif (
                !empty($prices['discount_rate'])
                && (
                    empty($prices['discount_from'])
                    || strtotime($prices['discount_from']) <= $time
                )
                && (
                    empty($prices['discount_to'])
                    || $time <= strtotime($prices['discount_to'])
                )
            ) {
                $savingsRate = $prices['discount_rate'];
            }
            elseif (
                !empty($prices['discount_price'])
                && (
                    empty($prices['discount_from'])
                    || strtotime($prices['discount_from']) <= $time
                )
                && (
                    empty($prices['discount_to'])
                    || $time <= strtotime($prices['discount_to'])
                )
            ) {
                $savings = $prices['price'] - $prices['discount_price'];
                $savingsRate = 100 * ($savings / $prices['price']);
            }
            else {
                $savingsRate = 0.0;
            }
        }
        return round($savingsRate, $options['precision']);
    }

    /**
     * Updates product prices according of attributes prices.
     *
     * @param array $product A single item returned by EshopProduct::getDetails() method.
     *
     * @param array $options Following options can be specified:
     *      - 'pricesAreTaxed' (bool) If TRUE then product prices are considered to be provided
     *          as taxed. If FALSE then they are considered to be taxless. Defaults to NULL means
     *          that setting Eshop.pricesAreTaxed is loaded to found this.
     *      - 'precision' (int) All numbers will be rounded to this precision. Defaults to 2.
     *      - 'staticAttributes' (array) Array containing pairs '{attrFieldName}' => '{attrValue}'.
     *          Prices are calculated for provided values of specified static attribute fields.
     *      - 'dynamicAttributes' (array) Array containing pairs '{attrId}' => '{attrValue}'
     *          Prices are calculated for provided values of specified dynamic attributes.
     */
    public function updatePricesByAttributes($product, $options = array()) {
        $defaults = array(
            'pricesAreTaxed' => null,
            'precision' => 2,
            'staticAttributes' => null,
            'dynamicAttributes' => null,
        );
        $options = array_merge($defaults, $options);
        if ($options['pricesAreTaxed'] === null) {
            $options['pricesAreTaxed'] = $this->getSetting('pricesAreTaxed');
        }

        // if no valid static or dynamic attributes are provided then return product as it is
        if (
            (
                empty($options['staticAttributes'])
                || !is_array($options['staticAttributes'])
            )
            && (
                empty($options['dynamicAttributes'])
                || !is_array($options['dynamicAttributes'])
            )
        ) {
            return $product;
        }

        // acumulate attribute prices
        $acumulativeAdjustment = array();
        // - static attributes prices
        if (
            !empty($options['staticAttributes'])
            && is_array($options['staticAttributes'])
        ) {
            foreach ($options['staticAttributes'] as $fieldName => $value) {
                if (isset($product[$fieldName]['prices'][$value])) {
                    $adjustment = $product[$fieldName]['prices'][$value];
                    $acumulativeAdjustment = $this->acumulatePriceAdjustments(
                        $product['price'], $adjustment, $acumulativeAdjustment
                    );
                }
            }
        }
        // - dynamic attributes prices
        if (
            !empty($options['dynamicAttributes'])
            && is_array($options['dynamicAttributes'])
        ) {
            foreach ($options['dynamicAttributes'] as $id => $value) {
                if (isset($product['EshopProductAttribute'][$id]['prices'][$value])) {
                    $adjustment = $product['EshopProductAttribute'][$id]['prices'][$value];
                    $acumulativeAdjustment = $this->acumulatePriceAdjustments(
                        $product['price'], $adjustment, $acumulativeAdjustment
                    );
                }
            }
        }

        // adjust price
        $product['price'] = $this->adjustPrice($product['price'], $acumulativeAdjustment);

        // force recounting of actual price by unsetting following prices
        // (discount_rate is preserved to force recalculation by discount_rate)
        unset($product['discount_price']);
        unset($product['actual_price']);
        $product = $this->getPrices($product, $options);

        return $product;
    }

    /**
     * Return price variants in multidimensional array where array keys are set to
     * cartesian product of all possible priced atrributeFields values. E.g. if
     * you have priced attributFields 'height' with values 50, 100 and 'color' with
     * values 'blue', 'red' then the resulting array will be:
     *
     *      array(
     *          50 => array(
     *              'blue' => array(
     *                  'price_taxed' => ...,
     *                  'price_actual_taxed' => ...,
     *                  'savings_rate' => ...,
     *                  ...
     *              ),
     *              'red' => array(...),
     *          ),
     *          100 => array(
     *              'blue' => array(...),
     *              'red' => array(...),
     *          )
     *      )
     *
     * And the auxiliary output $pricedAttributeFields is set to array('height', 'color')
     *
     * @param array $product A single item returned by EshopProduct::getDetails() method.
     * @param array& $pricedAttributeFields  Optional. Auxilairy output to get considered
     *      attributeFields names in order they apply.
     *
     * @return array Multidimensional array as shown here above
     */
    public function getPriceVariants($product, &$pricedAttributeFields = array()) {
        $pricedAttributeFields = array();
        foreach ((array)$this->attributeFields as $field => $options) {
            if (
                $options['priced']
                && !empty($product['static_attributes'][$field])
            ) {
                $pricedAttributeFields[$field] = $product['static_attributes'][$field];
            }
        }
        $priceKeys = array_flip(array(
            'price',
            'price_per_unit',
            'discount_price',
            'discount_rate',
            'discount_from',
            'discount_to',
            'tax_rate',
            'price_taxed',
            'price_actual_taxed',
            'actual_price',
            'savings_taxed',
            'price_taxless',
            'price_actual_taxless',
            'savings_taxless',
            'savings_rate',
            'tax',
            'tax_actual',
        ));
        $count = count($pricedAttributeFields);
        $priceVariants = array();
        $combinations = Arr::getCartesianProduct($pricedAttributeFields);
        foreach ($combinations as $combination) {
            $priceVariant = $this->updatePricesByAttributes($product, array(
                'staticAttributes' => $combination,
            ));
            $priceVariant = array_intersect_key($priceVariant, $priceKeys);
            $subArray = &$priceVariants;
            $i = 1;
            foreach ($combination as $value) {
                if ($i === $count) {
                    $subArray[$value] = $priceVariant;
                }
                else {
                    if (!isset($subArray[$value])) {
                        $subArray[$value] = array();
                    }
                    $subArray = &$subArray[$value];
                    $i++;
                }
            }
        }

        $pricedAttributeFields = array_keys($pricedAttributeFields);
        return $priceVariants;
    }

    /**
     * Returns all orderable products for provided product ids.
     *
     * ATTENTION: Keep this method in synchro with DB trigger actualizing field 'unavailable'.
     *
     * @param int|array $id Either single (int) product id, or an array of product ids.
     * @param array $options Following are available:
     *      - 'additionalFields' (array) Plain array of additional fields to be included in returned records.
     *          For all available fields see EshopProduct::getDetails(). Defaults to empty array().
     *
     * @return array If single id was passed then:
     *      array(
     *          'disponibility' => 1,
     *          'availability' => 'enum_available',
     *          'stock' => '10',
     *          'is_additional_service' => 'enum_gift_package',
     *      )
     *
     *      If array of ids was passed then:
     *          array(
     *              '{id1}' => array(
     *                  'disponibility' => 1,
     *                  'availability' => 'enum_available',
     *                  'stock' => '10',
     *                  'is_additional_service' => 'enum_gift_package',
     *              ),
     *              '{id2}' => array(
     *                  'disponibility' => 1,
     *                  'availability' => 'enum_soldout',
     *                  'stock' => '3',
     *                  'is_additional_service' => null,
     *              ),
     *              ...
     *          )
     */
    public function getOrderable($id, $options = array()) {
        $options = array_merge(array(
            'additionalFields' => array()
        ), $options);
        $fields = array_merge(array_flip((array)$options['additionalFields']), array(
            // forced fields
            'disponibility' => true,
            'availability' => true,
            'stock' => true,
            'is_additional_service' => true,
        ));
        $products = $this->getDetails((array)$id, array(
            'publishedOnly' => true,
        ));
        foreach ($products as $productId => $product) {
            // if product is not available then remove it
            if (
                $product['disponibility'] !== EshopProduct::AUTOSTOCK
                && $product['disponibility'] !== EshopProduct::STOCK
                && $product['disponibility'] !== EshopProduct::SUPPLIER
                && $product['disponibility'] !== EshopProduct::PRESALE
            ) {
                unset($products[$productId]);
            }
            else {
                // filter only following fields in each of records
                $products[$productId] = array_intersect_key(
                    $products[$productId], $fields
                );
            }
        }
        if (!is_array($id)) {
            $products = reset($products);
        }
        return $products;
    }

    /**
     * Updates stock quantities of products according to the passed data.
     * Non-existent products will be ignored. Stock quantities never falls below 0,
     * means stock -2 results to 0.
     *
     * ATTENTION: This method does not make reservation of products table. The
     * query is made atomic by launching direct calculations direct in DB by UPDATE
     * SQL query.
     *
     * @param array $products Array of cart/order products like:
     *      array(
     *          array(
     *              'id' => 55,
     *              'amount' => 2 // amount to be added/removed from stock
     *          ),
     *          array(
     *              'id' => 30,
     *              'amount' => 1 // amount to be added/removed from stock
     *          )
     *      )
     *
     * @param string $type Optional. Type of stock update: 'add' or 'remove'.
     *      Defaults to 'remove'.
     *
     * @return bool|array FALSE on failure. Otherwise array of updated products
     *      with resulting stock quantities (unexisting products are not included):
     *
     *      array(
     *          array(
     *              'id' => 55,
     *              'stock' => 10 // resulting stock quantity
     *          ),
     *          array(
     *              'id' => 30,
     *              'stock' => 0 // resulting stock quantity
     *          )
     *      )
     *
     *      ATTENTION: An empty array can be returned if all provided
     *      products has been unexisting.
     *
     * @throws Exception Resends catched exceptions
     */
    public function updateStock($products, $type = 'remove') {
        // update
        DB::startTransaction('EshopProduct_updateStock');
        try {
            $remove = (strtolower($type) === 'remove');
            $productIds = array();
            foreach($products as $product) {
                // skip invalid products
                if (
                    empty($product['id'])
                    || empty($product['amount'])
                    || !Validate::intNumber($product['amount'])
                ) {
                    continue;
                }
                // update stock directly in DB (to avoid need of table reservation)
                if ($remove) {
                    $data = array('stock' => str_replace(':a:', $product['amount'], 'IF((stock - :a:) < 0, 0, stock - :a:)'));
                }
                else {
                    $data = array('stock' => str_replace(':a:', $product['amount'], 'IF((stock + :a:) < 0, 0, stock + :a:)'));
                }
                $result = $this->update($data, array(
                    'conditions' => array('id' => $product['id']),
                    'normalize' => false,
                    'validate' => false,
                    'literals' => array(
                        'data' => true,
                    ),
                    'reserve' => false,
                ));
                if (!$result) {
                    DB::rollbackTransaction('EshopProduct_updateStock');
                    return false;
                }
                $productIds[] = $product['id'];
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopProduct_updateStock');
            throw $e;
        }
        DB::commitTransaction('EshopProduct_updateStock');

        try {
            // synchronize MeiliSearch data
            $this->synchronizeWithExternalSearch(array(
                'ids' => $productIds,
            ));
        }
        catch (Throwable $e) {
            App::catchThrowable($e);
        }

        return  $this->findList(array(
            'key' => 'EshopProduct.id',
            'fields' => array('EshopProduct.id', 'EshopProduct.stock'),
            'conditions' => array('EshopProduct.id' => $productIds),
        ));
    }

    /**
     *
     * @param array $options
     * @return array
     *
     * @throws Exception_DB_TablesReservationFailure
     */
    public function findAll($options = array()) {
        $defaults = array(
            'first' => false,
        );
        $options = array_merge($defaults, $options);
        // apply first
        $first = $options['first'];
        if ($first) {
            $options['first'] = false;
            $options['limit'] = 1;
        }

        // reserve tables (to avoid writes while reading data)
        $this->reserveTables('EshopProduct_findAll', array(
            'EshopProduct',
            'EshopProductAuthor',
            'EshopProductCategoryProduct',
            'EshopRelatedProduct',
            'EshopVariantProduct',
            'EshopAccessoryProduct',
            'EshopProductGroupProduct',
        ));
        $options['key'] = 'EshopProduct.id';
        $products = $this->findList($options);
        $productIds = array_keys($products);

        // get authors ids
        $ProductAuthor = $this->loadModel('EshopProductAuthor', true);
        $authors = $ProductAuthor->find(array(
            'fields' => array(
                'EshopProductAuthor.run_eshop_products_id',
                'EshopProductAuthor.run_eshop_authors_id',
            ),
            'conditions' => array('EshopProductAuthor.run_eshop_products_id' => $productIds),
        ));
        foreach ($authors as $author) {
            $productId = $author['run_eshop_products_id'];
            if (empty($products[$productId]['author_ids'])) {
                $products[$productId]['author_ids'] = array();
            }
            $products[$productId]['author_ids'][] = $author['run_eshop_authors_id'];
        }

        // get categories ids
        $ProductCategory = $this->loadModel('EshopProductCategoryProduct', true);
        $categories = $ProductCategory->find(array(
            'fields' => array(
                'EshopProductCategoryProduct.run_eshop_products_id',
                'EshopProductCategoryProduct.run_eshop_product_categories_id',
            ),
            'conditions' => array('EshopProductCategoryProduct.run_eshop_products_id' => $productIds),
        ));
        foreach ($categories as $category) {
            $productId = $category['run_eshop_products_id'];
            if (empty($products[$productId]['category_ids'])) {
                $products[$productId]['category_ids'] = array();
            }
            $products[$productId]['category_ids'][] = $category['run_eshop_product_categories_id'];
        }

        // get related ids
        $ProductRelatedProduct = $this->loadModel('EshopRelatedProduct', true);
        $relateds = $ProductRelatedProduct->find(array(
            'fields' => array(
                'EshopRelatedProduct.run_eshop_products_id',
                'EshopRelatedProduct.run_eshop_related_products_id',
            ),
            'conditions' => array('EshopRelatedProduct.run_eshop_products_id' => $productIds),
        ));
        foreach ($relateds as $related) {
            $productId = $related['run_eshop_products_id'];
            if (empty($products[$productId]['related_ids'])) {
                $products[$productId]['related_ids'] = array();
            }
            $products[$productId]['related_ids'][] = $related['run_eshop_related_products_id'];
        }

        // get variant ids
        $VariantProduct = $this->loadModel('EshopVariantProduct', true);
        $variants = $VariantProduct->find(array(
            'fields' => array(
                'EshopVariantProduct.run_eshop_products_id',
                'EshopVariantProduct.run_eshop_variant_products_id',
            ),
            'conditions' => array('EshopVariantProduct.run_eshop_products_id' => $productIds),
        ));
        foreach ($variants as $variant) {
            $productId = $variant['run_eshop_products_id'];
            if (empty($products[$productId]['variant_ids'])) {
                $products[$productId]['variant_ids'] = array();
            }
            $products[$productId]['variant_ids'][] = $variant['run_eshop_variant_products_id'];
        }

        // get accessory ids
        $ProductAccessoryProduct = $this->loadModel('EshopAccessoryProduct', true);
        $accessories = $ProductAccessoryProduct->find(array(
            'fields' => array(
                'EshopAccessoryProduct.run_eshop_products_id',
                'EshopAccessoryProduct.run_eshop_accessory_products_id',
            ),
            'conditions' => array('EshopAccessoryProduct.run_eshop_products_id' => $productIds),
        ));
        foreach ($accessories as $accessory) {
            $productId = $accessory['run_eshop_products_id'];
            if (empty($products[$productId]['accessory_ids'])) {
                $products[$productId]['accessory_ids'] = array();
            }
            $products[$productId]['accessory_ids'][] = $accessory['run_eshop_accessory_products_id'];
        }

        // get product groups ids
        $GroupXProduct = $this->loadModel('EshopProductGroupProduct', true);
        $groups = $GroupXProduct->find(array(
            'fields' => array(
                'EshopProductGroupProduct.run_eshop_products_id',
                'EshopProductGroupProduct.run_eshop_product_groups_id',
            ),
            'conditions' => array('EshopProductGroupProduct.run_eshop_products_id' => $productIds),
        ));
        foreach ($groups as $group) {
            $productId = $group['run_eshop_products_id'];
            if (empty($products[$productId]['group_ids'])) {
                $products[$productId]['group_ids'] = array();
            }
            $products[$productId]['group_ids'][] = $group['run_eshop_product_groups_id'];
        }
        // get supplier products
        $this->loadModel('EshopSupplierProduct');
        $SupplierProduct = new EshopSupplierProduct();
        $supplierProducts = $SupplierProduct->find(array(
            'conditions' => array('EshopSupplierProduct.run_eshop_products_id' => $productIds),
        ));
        foreach ($supplierProducts as $supplierProduct) {
            $productId = $supplierProduct['run_eshop_products_id'];
            if (empty($products[$productId]['EshopSupplierProduct'])) {
                $products[$productId]['EshopSupplierProduct'] = array();
            }
            $products[$productId]['EshopSupplierProduct'][] = $supplierProduct;
        }

        $this->unreserveTables('EshopProduct_findAll');

        // get just values (without product ids in keys) to be properly json encoded
        $products = array_values($products);

        if ($first) {
            $products = reset($products);
        }
        return $products;
    }

    /**
     * Saves:
     *      standard product fields
     *      + uploaded image
     *      + habtm authors (ids are in author_ids field)
     *      + habtm categories (ids are in category_ids field)
     *      + habtm related products (ids are in related_ids field)
     *      + habtm accessory products (ids are in accessory_ids field)
     *
     * @param array $data
     * @param array $options Model::save() options, e.g. 'lang' - probably the only used here.
     *
     * @return bool|array Array of saved data on success and FALSE on validation or processing failure.
     *
     * @throws Exception_DB_TablesReservationFailure
     * @throws Exception
     */
    public function saveAll($data, $options = array()) {
        try {
            $this->reserveTables('EshopProduct_saveAll', array(
                'EshopProduct',
                'App.ContentBlockInstance',
                'EshopProductAuthor',
                'EshopVariantProduct',
                'EshopProductCategoryProduct',
                'EshopRelatedProduct',
                'EshopAccessoryProduct',
                'EshopProductGroupProduct',
            ), array('tries' => 20, 'retryTime' => 1000));

            // update or insert
            $options['on'] = null; // to make Model::isCreation() resolve correctly
            $options['on'] = ($this->isCreation($data, $options)) ? 'create' : 'update';

            // normalize
            $data = $this->normalize($data, $options);
            // turn normalization off for further processing
            $options['normalize'] = false;

            // validate and turn validation off for further processing
            $productIsValid = $this->validate($data, $options);

            if (
                !$productIsValid
            ) {
                $this->unreserveTables('EshopProduct_saveAll');
                return false;
            }

            // turn validation off for further processing
            $options['validate'] = false;
            DB::startTransaction('EshopProduct_saveAll');
            if ($options['on'] === 'update') {
                $oldData = $this->findFirstBy('id', $data['id'], array(
                    'fields' => array(
                        'run_eshop_manufacturers_id',
                    )
                ));
            }
            $result = $this->save($data, $options);
            if (!$result) {
                DB::rollbackTransaction('EshopProduct_saveAll');
                $this->unreserveTables('EshopProduct_saveAll');
                return false;
            }
            $productId = $this->getPropertyId();
            $data = $result;
            // content blocks
            App::loadModel('App', 'ContentBlockInstance');
            $BlockInstance = new ContentBlockInstance();
            if (
                !empty($data['id'])
                && !$BlockInstance->saveOwnerInstancesData($data, 'Eshop.EshopProduct', $productId)
            ) {
                DB::rollbackTransaction('EshopProduct_saveAll');
                $this->unreserveTables('EshopProduct_saveAll');
                return false;
            }
            // check for author_ids
            if (isset($data['author_ids'])) {
                $ProductAuthor = $this->loadModel('EshopProductAuthor', true);
                $ProductAuthor->deleteBy('run_eshop_products_id', $productId);
                if (!empty($data['author_ids'])) {
                    $records = array();
                    foreach ($data['author_ids'] as $authorId) {
                        $records[] = array(
                            'run_eshop_products_id' => $productId,
                            'run_eshop_authors_id' => $authorId
                        );
                    }
                    DB::insert(
                        $ProductAuthor->getPropertyTable(),
                        $records,
                        array('multiple' => true)
                    );
                }
            }
            // check for category_ids
            if (isset($data['category_ids'])) {
                $ProductCategory = $this->loadModel('EshopProductCategoryProduct', true);
                $ProductCategory->deleteBy('run_eshop_products_id', $productId);
                if (!empty($data['category_ids'])) {
                    $records = array();
                    foreach ($data['category_ids'] as $categoryId) {
                        $records[] = array(
                            'run_eshop_products_id' => $productId,
                            'run_eshop_product_categories_id' => $categoryId
                        );
                    }
                    DB::insert(
                        $ProductCategory->getPropertyTable(),
                        $records,
                        array('multiple' => true)
                    );
                }
            }
            // check for related_ids
            if (isset($data['related_ids'])) {
                $ProductRelated = $this->loadModel('EshopRelatedProduct', true);
                $reciprocal = (bool)$this->getSetting('EshopRelatedProduct.reciprocal');
                $ProductRelated->deleteBy('run_eshop_products_id', $productId);
                if ($reciprocal) {
                    // if the relation is considered to be reciprocal then remove
                    // also all relation where the product itself is set as related
                    $ProductRelated->deleteBy('run_eshop_related_products_id', $productId);
                }
                if (!empty($data['related_ids'])) {
                    if ($reciprocal) {
                        // if the relation is considered to be reciprocal then remove
                        // also all relations between related products
                        $ProductRelated->delete(array(
                            'conditions' => array(
                                'run_eshop_products_id' => $data['related_ids'],
                                'run_eshop_related_products_id' => $data['related_ids'],
                            )
                        ));
                    }
                    $records = array();
                    foreach ($data['related_ids'] as $relatedId) {
                        $records[] = array(
                            'run_eshop_products_id' => $productId,
                            'run_eshop_related_products_id' => $relatedId,
                        );
                        // if the relation is considered to be reciprocal then create also
                        // relations between related products
                        if ($reciprocal) {
                            foreach ($data['related_ids'] as $relatedId2) {
                                if ($relatedId2 === $relatedId) {
                                    $relatedId2 = $productId;
                                }
                                $records[] = array(
                                    'run_eshop_products_id' => $relatedId,
                                    'run_eshop_related_products_id' => $relatedId2,
                                );
                            }
                        }
                    }
                    DB::insert(
                        $ProductRelated->getPropertyTable(),
                        $records,
                        array('multiple' => true)
                    );
                }
            }

            // check for variant
            if (isset($data['variant_ids'])) {
                $variantsCommonName = Sanitize::value($data['variants_common_name']);
                $this->resolveVariants($productId, $data['variant_ids'], $variantsCommonName);
            }

            // check for accessory_ids
            if (isset($data['accessory_ids'])) {
                $ProductAccessory = $this->loadModel('EshopAccessoryProduct', true);
                $reciprocal = (bool)$this->getSetting('EshopAccessoryProduct.reciprocal');
                $ProductAccessory->deleteBy('run_eshop_products_id', $productId);
                if ($reciprocal) {
                    // if the relation is considered to be reciprocal then remove
                    // also all relation where the product itself is set as accessory
                    $ProductAccessory->deleteBy('run_eshop_accessory_products_id', $productId);
                }
                if (!empty($data['accessory_ids'])) {
                    if ($reciprocal) {
                        // if the relation is considered to be reciprocal then remove
                        // also all relations between accessory products
                        $ProductAccessory->delete(array(
                            'conditions' => array(
                                'run_eshop_products_id' => $data['accessory_ids'],
                                'run_eshop_accessory_products_id' => $data['accessory_ids'],
                            )
                        ));
                    }
                    $records = array();
                    foreach ($data['accessory_ids'] as $accessoryId) {
                        $records[] = array(
                            'run_eshop_products_id' => $productId,
                            'run_eshop_accessory_products_id' => $accessoryId,
                        );
                        // if the relation is considered to be reciprocal then create also
                        // relations between accessory products
                        if ($reciprocal) {
                            foreach ($data['accessory_ids'] as $accessoryId2) {
                                if ($accessoryId2 === $accessoryId) {
                                    $accessoryId2 = $productId;
                                }
                                $records[] = array(
                                    'run_eshop_products_id' => $accessoryId,
                                    'run_eshop_accessory_products_id' => $accessoryId2,
                                );
                            }
                        }
                    }
                    DB::insert(
                        $ProductAccessory->getPropertyTable(),
                        $records,
                        array('multiple' => true)
                    );
                }
            }
            // check for group_ids
            if (isset($data['group_ids'])) {
                $GroupXProduct = $this->loadModel('EshopProductGroupProduct', true);
                $GroupXProduct->deleteBy('run_eshop_products_id', $productId);
                if (!empty($data['group_ids'])) {
                    $records = array();
                    foreach ($data['group_ids'] as $groupId) {
                        $records[] = array(
                            'run_eshop_products_id' => $productId,
                            'run_eshop_product_groups_id' => $groupId
                        );
                    }
                    DB::insert(
                        $GroupXProduct->getPropertyTable(),
                        $records,
                        array('multiple' => true)
                    );
                }
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopProduct_saveAll');
            $this->unreserveTables('EshopProduct_saveAll');
            throw $e;
        }

        DB::commitTransaction('EshopProduct_saveAll');
        $this->unreserveTables('EshopProduct_saveAll');
        // synchronize with supplier products if manufacturer id has changed
        // Do it only after transaction is commited as the synchronization uses Model::saveBatch()
        if (
            array_key_exists('run_eshop_manufacturers_id', $data)
            && (
                $options['on'] === 'create'
                ||
                (int)$oldData['run_eshop_manufacturers_id'] !==
                    (int)$data['run_eshop_manufacturers_id']
            )
        ) {
            $this->loadModel('EshopSupplierProduct');
            $SupplierProduct = new EshopSupplierProduct();
            $SupplierProduct->synchronize(array(
                'updatedOnly' => false,
                'productIds' => $productId,
            ));
        }
        try {
            // synchronize MeiliSearch data
            $this->synchronizeWithExternalSearch(array(
                'ids' => $this->getPropertyId(),
            ));
        }
        catch (Throwable $e) {
            App::catchThrowable($e);
        }

        return $data;
    }

    /**
     * Resolves product variants by updating records and creating new associations.
     *
     * - Resets previous variant associations for the given product.
     * - Deletes existing variant relationships.
     * - Creates new associations if variants are provided.
     * - Determines the main product based on the lowest price.
     * - Updates variant properties (`secondary_variant`, `variants_lowest_price`, `variants_common_name`).
     *
     * @param int   $productId  The ID of the main product for which variants are being resolved.
     * @param array $variantIds An array of product IDs representing the variants of the given product.
     * @param string|null $commonName The common name to be assigned to all variant products (nullable).
     *
     * @return void
     */
    public function resolveVariants($productId, $variantIds, $commonName = null) {
        $this->loadModel('EshopVariantProduct');
        $ProductVariant = new EshopVariantProduct();

        // ensure process atomicity (reserve processing and start transaction)
        try {
            App::reserveProcessing('EshopProduct::resolveVariants()');
        }
        catch (Throwable $e) {
            return;
        }
        DB::startTransaction('EshopProduct::resolveVariants()');

        try {
            // get existing variant ids of provided product id
            $prevProductIds = $ProductVariant->findList(array(
                'fields' => array(
                    'EshopVariantProduct.run_eshop_variant_products_id',
                ),
                'conditions' => array(
                    'EshopVariantProduct.run_eshop_products_id' => $productId,
                ),
            ));
            $prevProductIds[] = $productId;
            // reset variant group fields and remove actual HABTM relations between variant ids
            $records = array();
            foreach ($prevProductIds as $prevProductId) {
                $this->save(array(
                    'id' => $prevProductId,
                    'secondary_variant' => 0,
                    'variants_lowest_price' => 0,
                    'variants_common_name' => null,
                ));
            }
            $ProductVariant->deleteBy('run_eshop_products_id', $prevProductIds);

            if (!empty($variantIds)) {
                // create new HABTM relations for provided $productId and $variantIds
                $productIds = $variantIds;
                $productIds[] = $productId;
                foreach ($productIds as $productId1) {
                    foreach ($productIds as $productId2) {
                        if ($productId1 === $productId2) {
                            continue;
                        }
                        $records[] = array(
                            'run_eshop_products_id' => $productId1,
                            'run_eshop_variant_products_id' => $productId2,
                        );
                    }
                }
                DB::insert(
                    $ProductVariant->getPropertyTable(),
                    $records,
                    array('multiple' => true)
                );
                // set variants group fields
                // - resolve main variant id and if it has lowest price
                $actualPriceSql = self::ACTUAL_PRICE_SQL;
                $publishedProducts = $this->find(array(
                    'fields' => array(
                        'EshopProduct.id',
                        $actualPriceSql . ' as actual_price',
                    ),
                    'conditions' => array(
                        self::getPublishedConditions(),
                        'EshopProduct.id' => $productIds,
                    ),
                    'literals' => array(
                        'fields' => array(
                            $actualPriceSql . ' as actual_price',
                        ),
                        'order' => array(
                            $actualPriceSql . 'ASC',
                            'EshopProduct.id ASC',
                        ),
                    ),
                    'order' => array(
                        $actualPriceSql . 'ASC',
                        'EshopProduct.id ASC',
                    ),
                ));
                $mainProductId = null;
                if (count($publishedProducts) > 0) {
                    $lowestActualPrice = (float) $publishedProducts[0]['actual_price'];
                    $highestActualPrice = (float) $publishedProducts[array_key_last($publishedProducts)]['actual_price'];
                    $hasVariantsLowestPrice = $lowestActualPrice !== $highestActualPrice;
                    $mainProductId = $publishedProducts[0]['id'];
                }
                // - set secondary variant lowest price flags and variants common name
                foreach ($productIds as $id) {
                    $secondaryVariant = 1;
                    $variantsLowestPrice = 0;
                    if ((int)$id === (int)$mainProductId) {
                        $secondaryVariant = 0;
                        $variantsLowestPrice = $hasVariantsLowestPrice ? 1 : 0;
                    }
                    $this->save(array(
                        'id' => $id,
                        'secondary_variant' => $secondaryVariant,
                        'variants_lowest_price' => $variantsLowestPrice,
                        'variants_common_name' => $commonName,
                    ));
                }
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopProduct::resolveVariants()');
            App::unreserveProcessing('EshopProduct::resolveVariants()');
            throw $e;
        }
        DB::commitTransaction('EshopProduct::resolveVariants()');
        App::unreserveProcessing('EshopProduct::resolveVariants()');
    }

    /**
     * Deletes webcontent node and its childs with related webimages and content blocks
     *
     * @param int $id Id of the node to be removed.
     * @param array $options Optional. Options used for deleting the node and its
     *      childs. See Model::delete() method.
     *
     * @return bool|array Returns FALSE if the specified node does not exist.
     *      Returns empty array() if node cannot be removed (it is permanent).
     *      Othervise returns array of removed nodes ids.
     *
     * @throws Exception_DB_TablesReservationFailure
     */
    public function delete($options = array()) {
        $defaults = array(
            'cleanUpFiles' => true,
            'reserve' => true,
            'softDelete' => true,
        );
        $options = array_merge($defaults, $options);
        if ($options['reserve']) {
            $this->reserveTables('EshopProduct_delete', array(
                'EshopProduct',
                'EshopProductImage',
                'App.ContentBlockInstance',
            ));
        }
        $products = $this->find(array_merge($options, array(
            'first' => false,
            'fields' => array('EshopProduct.id'),
        )));
        $result = parent::delete(array_merge($options, array(
            'reserve' => false,
        )));
        if ($result && $products) {
            $productIds = array();
            foreach ($products as $product) {
                $productIds = $product['id'];
            }
            // delete gallery images
            $this->loadModel('EshopProductImage');
            $Image = new EshopProductImage();
            $Image->delete(array(
                'conditions' => array(
                    'run_eshop_products_id' => $productIds,
                ),
                'cleanUpFiles' => $options['cleanUpFiles'],
                'reserve' => false,
                'softDelete' => $options['softDelete'],
            ));
            // delete content block instances
            App::loadModel('App', 'ContentBlockInstance');
            $Instance = new ContentBlockInstance();
            $Instance->delete(array(
                'processContentBlockData' => true,
                'conditions' => array(
                    'owner_model' => 'Eshop.EshopProduct',
                    'owner_id' => $productIds,
                ),
                'cleanUpFiles' => $options['cleanUpFiles'],
                'reserve' => false,
                'softDelete' => $options['softDelete'],
            ));
        }
        if ($options['reserve']) {
            $this->unreserveTables('EshopProduct_delete');
        }
        return $result;
    }

    /**
     * Updates:
     *      standard product fields
     *      + habtm authors (ids are in author_ids field)
     *      + habtm categories (ids are in category_ids field)
     *      + habtm related products (ids are in related_ids field)
     *      + habtm accessory products (ids are in accessory_ids field)
     *
     * All fields containing empty string are ignored (their actual value is preserved)
     *
     * @param array $productIds Array of product ids
     * @param array $data
     *
     * @return bool|array Array of saved data on success and FALSE on validation or processing failure.
     *
     * @throws Exception_DB_TablesReservationFailure
     * @throws Exception
     */
    public function saveMany($productIds, $data) {
        $productIds = (array)$productIds;

        // empty string in data means "no change"
        $data = array_filter($data, function($v) {
            return $v !== '';
        });

        // clear all field where it is explicitly required
        foreach($data as $k => $v) {
            if (
                substr($k, 0, 8) === '_remove_'
                && !empty($v)
            ) {
                $field = substr($k, 8);
                $data[$field] = ''; // final empty value (null, '') will be resolved by normalization
            }
        }

        try {
            $this->reserveTables('EshopProduct_saveAll', array(
                'EshopProduct',
                'EshopProductAuthor',
                'EshopProductCategoryProduct',
                'EshopRelatedProduct',
                'EshopAccessoryProduct',
                'EshopProductGroupProduct',
            ), array('tries' => 20, 'retryTime' => 1000));

            // update or insert
            $options['on'] = 'update';

            // normalize
            $data = $this->normalize($data, $options);
            // turn normalization off for further processing
            $options['normalize'] = false;

            // no validation is done
            // turn validation off for further processing
            $options['validate'] = false;

            $options['conditions'] = array(
                'id' => $productIds
            );
            DB::startTransaction('EshopProduct_saveMany');
            // save product data but only if there is something to save
            if (array_intersect_key($data, $this->schema)) {
                $this->update($data, $options);
            }
            // check for author_ids
            if (!empty($data['author_ids'])) {
                $ProductAuthor = $this->loadModel('EshopProductAuthor', true);
                $existingRelations = array();
                if (
                    empty($data['_apply_author_ids'])
                    || $data['_apply_author_ids'] !== 'add'
                ) {
                    $ProductAuthor->deleteBy('run_eshop_products_id', $productIds);
                }
                // get existing relations to avoid creation of duplicit ones
                elseif ($data['_apply_author_ids'] === 'add') {
                    $existingRelations = $ProductAuthor->findList(array(
                        'key' => 'run_eshop_products_id',
                        'fields' => array(
                            'run_eshop_authors_id'
                        ),
                        'accumulate' => true,
                        'conditions' => array(
                            'run_eshop_products_id' => $productIds,
                        )
                    ));
                }
                $records = array();
                foreach ($productIds as $productId) {
                    foreach ($data['author_ids'] as $authorId) {
                        // add new relation only if it does not exist yet
                        if (
                            empty($existingRelations[$productId])
                            || !in_array($authorId, $existingRelations[$productId])
                        ) {
                            $records[] = array(
                                'run_eshop_products_id' => $productId,
                                'run_eshop_authors_id' => $authorId
                            );
                        }
                    }
                }
                if (!empty($records)) {
                    DB::insert(
                        $ProductAuthor->getPropertyTable(),
                        $records,
                        array('multiple' => true)
                    );
                }
            }
            // check for category_ids
            if (!empty($data['category_ids'])) {
                $ProductCategory = $this->loadModel('EshopProductCategoryProduct', true);
                $existingRelations = array();
                if (
                    empty($data['_apply_category_ids'])
                    || $data['_apply_category_ids'] !== 'add'
                ) {
                    $ProductCategory->deleteBy('run_eshop_products_id', $productIds);
                }
                // get existing relations to avoid creation of duplicit ones
                elseif ($data['_apply_category_ids'] === 'add') {
                    $existingRelations = $ProductCategory->findList(array(
                        'key' => 'run_eshop_products_id',
                        'fields' => array(
                            'run_eshop_product_categories_id'
                        ),
                        'accumulate' => true,
                        'conditions' => array(
                            'run_eshop_products_id' => $productIds,
                        )
                    ));
                }
                $records = array();
                foreach ($productIds as $productId) {
                    foreach ($data['category_ids'] as $categoryId) {
                        // add new relation only if it does not exist yet
                        if (
                            empty($existingRelations[$productId])
                            || !in_array($categoryId, $existingRelations[$productId])
                        ) {
                            $records[] = array(
                                'run_eshop_products_id' => $productId,
                                'run_eshop_product_categories_id' => $categoryId
                            );
                        }
                    }
                }
                 if (!empty($records)) {
                    DB::insert(
                        $ProductCategory->getPropertyTable(),
                        $records,
                        array('multiple' => true)
                    );
                }
            }
            // check for related_ids
            if (!empty($data['related_ids'])) {
                $ProductRelated = $this->loadModel('EshopRelatedProduct', true);
                $reciprocal = (bool)$this->getSetting('EshopRelatedProduct.reciprocal');
                $existingRelations = array();
                if (
                    empty($data['_apply_related_ids'])
                    || $data['_apply_related_ids'] !== 'add'
                ) {
                    $ProductRelated->deleteBy('run_eshop_products_id', $productIds);
                    if ($reciprocal) {
                        // if the relation is considered to be reciprocal then remove
                        // also all relation where the product itself is set as related
                        $ProductRelated->deleteBy('run_eshop_related_products_id', $productIds);
                        // if the relation is considered to be reciprocal then remove
                        // also all relations between related products
                        $ProductRelated->delete(array(
                            'conditions' => array(
                                'run_eshop_products_id' => $data['related_ids'],
                                'run_eshop_related_products_id' => $data['related_ids'],
                            )
                        ));
                    }
                }
                // get existing relations to avoid creation of duplicit ones
                elseif ($data['_apply_related_ids'] === 'add') {
                    $existingRelations = $ProductRelated->findList(array(
                        'key' => 'run_eshop_products_id',
                        'fields' => array(
                            'run_eshop_related_products_id'
                        ),
                        'accumulate' => true,
                        'conditions' => array(
                            'run_eshop_products_id' => $productIds,
                        )
                    ));
                }
                $records = array();
                foreach ($productIds as $productId) {
                    foreach ($data['related_ids'] as $relatedId) {
                        // add new relation only if it does not exist yet
                        if (
                            empty($existingRelations[$productId])
                            || !in_array($relatedId, $existingRelations[$productId])
                        ) {
                            $records[] = array(
                                'run_eshop_products_id' => $productId,
                                'run_eshop_related_products_id' => $relatedId,
                            );
                        }
                        // if the relation is considered to be reciprocal then create also
                        // relations between related products
                        if ($reciprocal) {
                            foreach ($data['related_ids'] as $relatedId2) {
                                if ($relatedId2 === $relatedId) {
                                    $relatedId2 = $productId;
                                }
                                // add new relation only if it does not exist yet
                                if (
                                    empty($existingRelations[$relatedId])
                                    || !in_array($relatedId2, $existingRelations[$relatedId])
                                ) {
                                    $records[] = array(
                                        'run_eshop_products_id' => $relatedId,
                                        'run_eshop_related_products_id' => $relatedId2,
                                    );
                                }
                            }
                        }
                    }
                }
                if (!empty($records)) {
                    DB::insert(
                        $ProductRelated->getPropertyTable(),
                        $records,
                        array('multiple' => true)
                    );
                }
            }
            // check for accessory_ids
            if (!empty($data['accessory_ids'])) {
                $ProductAccessory = $this->loadModel('EshopAccessoryProduct', true);
                $reciprocal = (bool)$this->getSetting('EshopAccessoryProduct.reciprocal');
                $existingRelations = array();
                if (
                    empty($data['_apply_accessory_ids'])
                    || $data['_apply_accessory_ids'] !== 'add'
                ) {
                    $ProductAccessory->deleteBy('run_eshop_products_id', $productIds);
                    if ($reciprocal) {
                        // if the relation is considered to be reciprocal then remove
                        // also all relation where the product itself is set as accessory
                        $ProductAccessory->deleteBy('run_eshop_accessory_products_id', $productIds);
                        // if the relation is considered to be reciprocal then remove
                        // also all relations between accessory products
                        $ProductAccessory->delete(array(
                            'conditions' => array(
                                'run_eshop_products_id' => $data['accessory_ids'],
                                'run_eshop_accessory_products_id' => $data['accessory_ids'],
                            )
                        ));
                    }
                }
                // get existing relations to avoid creation of duplicit ones
                elseif ($data['_apply_accessory_ids'] === 'add') {
                    $existingRelations = $ProductAccessory->findList(array(
                        'key' => 'run_eshop_products_id',
                        'fields' => array(
                            'run_eshop_accessory_products_id'
                        ),
                        'accumulate' => true,
                        'conditions' => array(
                            'run_eshop_products_id' => $productIds,
                        )
                    ));
                }
                $records = array();
                foreach ($productIds as $productId) {
                    foreach ($data['accessory_ids'] as $accessoryId) {
                        // add new relation only if it does not exist yet
                        if (
                            empty($existingRelations[$productId])
                            || !in_array($accessoryId, $existingRelations[$productId])
                        ) {
                            $records[] = array(
                                'run_eshop_products_id' => $productId,
                                'run_eshop_accessory_products_id' => $accessoryId,
                            );
                        }
                        // if the relation is considered to be reciprocal then create also
                        // relations between accessory products
                        if ($reciprocal) {
                            foreach ($data['accessory_ids'] as $accessoryId2) {
                                if ($accessoryId2 === $accessoryId) {
                                    $accessoryId2 = $productId;
                                }
                                // add new relation only if it does not exist yet
                                if (
                                    empty($existingRelations[$accessoryId])
                                    || !in_array($accessoryId2, $existingRelations[$accessoryId])
                                ) {
                                    $records[] = array(
                                        'run_eshop_products_id' => $accessoryId,
                                        'run_eshop_accessory_products_id' => $accessoryId2,
                                    );
                                }
                            }
                        }
                    }
                }
                if (!empty($records)) {
                    DB::insert(
                        $ProductAccessory->getPropertyTable(),
                        $records,
                        array('multiple' => true)
                    );
                }
            }
            // check for group_ids
            if (!empty($data['group_ids'])) {
                $GroupXProduct = $this->loadModel('EshopProductGroupProduct', true);
                $existingRelations = array();
                if (
                    !empty($data['_apply_group_ids'])
                    && $data['_apply_group_ids'] === 'remove'
                ) {
                    $GroupXProduct->delete(array(
                        'conditions' => array(
                            'run_eshop_product_groups_id' => $data['group_ids'],
                            'run_eshop_products_id' => $productIds,
                        ),
                    ));
                }
                else {
                    if (
                        empty($data['_apply_group_ids'])
                        || $data['_apply_group_ids'] !== 'add'
                    ) {
                        $GroupXProduct->deleteBy('run_eshop_products_id', $productIds);
                    }
                    // get existing relations to avoid creation of duplicit ones
                    elseif ($data['_apply_group_ids'] === 'add') {
                        $existingRelations = $GroupXProduct->findList(array(
                            'key' => 'run_eshop_products_id',
                            'fields' => array(
                                'run_eshop_product_groups_id'
                            ),
                            'accumulate' => true,
                            'conditions' => array(
                                'run_eshop_products_id' => $productIds,
                            )
                        ));
                    }
                    $records = array();
                    foreach ($productIds as $productId) {
                        foreach ($data['group_ids'] as $groupId) {
                            // add new relation only if it does not exist yet
                            if (
                                empty($existingRelations[$productId])
                                || !in_array($groupId, $existingRelations[$productId])
                            ) {
                                $records[] = array(
                                    'run_eshop_products_id' => $productId,
                                    'run_eshop_product_groups_id' => $groupId
                                );
                            }
                        }
                    }
                    if (!empty($records)) {
                        DB::insert(
                            $GroupXProduct->getPropertyTable(),
                            $records,
                            array('multiple' => true)
                        );
                    }
                }
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopProduct_saveMany');
            $this->unreserveTables('EshopProduct_saveMany');
            throw $e;
        }

        DB::commitTransaction('EshopProduct_saveMany');
        $this->unreserveTables('EshopProduct_saveMany');
        return $data;
    }


    /**
     * Gets last integer code used for an product.
     *
     * ATTENTION: Use this method only in case that integer product codes are used.
     *
     * @param array $options Find options to precise range of codes to
     *      find the last from.
     *
     * @return string|NULL
     */
    public function getLastCode($options = array()) {
        $defaults = array(
            'conditions' => null,
        );
        $options = array_merge($defaults, $options);
        // ensure existence of folowing items in options
        $options['conditions'] = DB::nestConditions($options['conditions']);
        // precise conditions
        $options['conditions'][] = 'code IS NOT NULL';
        // force following
        $options['order'] = array('(code + 0) DESC');
        $options['literals'] = array('order' => true);
        // get the code
        return $this->findField('code', $options);
    }

    /**
     * Gets next product code.
     *
     * ATTENTION: Use this method only in case that integer product codes are used.
     * Use this method and new product save inside of table reservation block.
     *
     * @param array $options Find options to precise range of codes to
     *      find the next for plus one extra option:
     *      - 'initial' (int) Initial value of next (new) order for provided find options.
     *      Defaults to 10000.
     *
     * @return string
     */
    public function getNextCode($options = array()) {
        $defaults = array(
            'initial' => 10000,
        );
        $options = array_merge($defaults, $options);
        $lastCode = $this->getLastCode($options);
        if ($lastCode === null) {
            return $options['initial'];
        }
        return (int)$lastCode + 1;
    }

    /**
     * Converts provided $weight from internal units (specified by setting Eshop.EshopProduct.weightUnits)
     * to specified $units
     *
     * @param float|int $weight Weight in internal units (specified by setting Eshop.EshopProduct.weightUnits)
     * @param string $units Output weight units, one of 'g', 'kg', 't'.
     *
     * @return float Weight in specified $units
     *
     * @throws Exception on invalid $units
     */
    public function convertWeight($weight, $units) {
        static $internalUnits = null;
        if (empty($weight)) {
            return 0.00;
        }
        if ($internalUnits === null) {
            $internalUnits = strtolower($this->getSetting('EshopProduct.weightUnits'));
        }
        $units = strtolower($units);
        $availableUnits = array('g' => 0, 'kg' => 3, 't' => 6);
        if (!isset($availableUnits[$units])) {
            throw new Exception(__e(__FILE__, 'Weight conversion has failed because of unvalid units "%s"', $units));
        }
        $weight = (float)$weight;
        $exponent = $availableUnits[$internalUnits] - $availableUnits[$units];
        if ($exponent > 0) {
            $weight *= pow(10, $exponent);
        }
        elseif ($exponent < 0) {
            $weight /= pow(10, abs($exponent));
        }
        return $weight;
    }

    /**
     * Generates code for Luigi's Box annotations of products search results index
     *
     * @see https://live.luigisbox.com/search_analytics.html#embedded-json-ld-basic-example
     * @see https://live.luigisbox.com/search_analytics.html#embedded-json-ld-no-search-results
     *
     * ATTENTION: An important part of luigis box annotations is also addition of
     * data-action="..." attributes in products index. E.g.:
     *          < button data-action="buy">Add to cart</ button>
     *          < a href="..." data-action="wishlist">Add to wishlist</ a>
     *
     * @see https://live.luigisbox.com/search_analytics.html#embedded-json-ld-conversions
     *
     * @param array $searchData Search data containing at least 'keywords' item.
     *      All other item are dependent by concrete implementation of this method on project.
     * @param array $products Array of found products (actual page), e.g. in EshopProducts::indexSearch()
     * @param string $productsListId Css id of products items wrapping element in index.
     *      This is an id of direct parent element (possibly  <ul> amd not the id of
     *      whole index (containing also filter, paginator, ...).
     * @param array $options Following are available:
     *      - 'Paginator' (Paginator) Instance of Model::$Paginator of model used
     *          to find the search results. Defaults to $this->Paginator (useful
     *          if this method is called by the same instance of EshopProduct model
     *          as the one used to find the product records).
     *      - 'productViewSlug' (string) Slug of content displaying product detail.
     *          Defaults to App::getContentLocatorByPid('Eshop.EshopProducts.view').
     *
     * @return string
     */
    public function getLugisBoxSearchIndexAnnotationsCode (
        $searchData,
        $products,
        $productsListId,
        $options
    ) {
        if (
            !App::$useJsEmbroidery
            ||
            !App::getSetting('App', 'luigisBox.jsScriptName')
        ){
            return '';
        }

        // normalize and validate
        $searchData = $this->normalize($searchData, array('alternative' => 'search'));
        $valid = $this->validate($searchData, array(
            'normalize' => false,
            'alternative' => 'search',
        ));
        if (!$valid) {
            return '';
        }

        $options = array_merge(array(
            'Paginator' => $this->Paginator,
            'productViewSlug' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),
        ), $options);
        if (!$options['Paginator']) {
            throw new Exception(__e(__FILE__, '\'Paginator\' option must be provided'));
        }
        if (!($options['Paginator'] instanceof Paginator)) {
            throw new Exception(__e(__FILE__, '\'Paginator\' option must be an instance of Paginator class'));
        }
        /* @var $Paginator Paginator */
        $Paginator = $options['Paginator'];

        $page = $Paginator->getPropertyPage();
        $limit = $Paginator->getPropertyLimit();
        $sort = $Paginator->getPropertySort();
        $filter = $Paginator->getPropertyFilter();
        $offset = ($page - 1) * $limit;
        $currencySymbol = Eshop::getActualCurrency('symbol');
        $itemListElement = array();
        $i = 1;
        foreach ($products as $product) {
            $productViewUrl = App::getUrl(array(
                'locator' => $options['productViewSlug'],
                'args' => array($product['slug'])
            ));
            $itemListElement[] = array(
                '@type' => array('ListItem', 'Product'),
                'position' => $offset + $i,
                'name' => $product['name'],
                'url' => $productViewUrl,
                'offers' => array(
                    '@type' => 'Offer',
                    'priceCurrency' => $currencySymbol,
                    'price' => $product['price_actual_taxed'],
                ),
            );
            $i++;
        }
        $annotations = array(
            '@context' => 'http://schema.org',
            '@type' => 'SearchAction',
            'query' => $searchData['keywords'],
            'result' => array(
                '@type' => 'ItemList',
                'name' => 'Search Results',
                'itemListElement' => $itemListElement,
            ),
            'instrument' => array(),
        );
        // add instrument for products index sort
        if (!empty($sort)) {
            $actionOption = null;
            if (!empty($sort['actual_price'])) {
                if (strtolower($sort['actual_price']) === 'desc') {
                    $actionOption = 'price descending';
                }
                else {
                    $actionOption = 'price ascendinng';
                }
            }
            elseif (!empty($sort['name'])) {
                if (strtolower($sort['name']) === 'desc') {
                    $actionOption = 'name descending';
                }
                else {
                    $actionOption = 'name ascendinng';
                }
            }
            elseif (!empty($sort['EshopProductAuthor>EshopAuthor']['name'])) {
                if (strtolower($sort['EshopProductAuthor>EshopAuthor']['name']) === 'desc') {
                    $actionOption = 'author_name descending';
                }
                else {
                    $actionOption = 'author_name ascendinng';
                }
            }
            elseif (!empty($sort['EshopManufacturer']['name'])) {
                if (strtolower($sort['EshopManufacturer']['name']) === 'desc') {
                    $actionOption = 'manufacturer_name descending';
                }
                else {
                    $actionOption = 'manufacturer_name ascendinng';
                }
            }
            elseif (!empty($sort['year'])) {
                if (strtolower($sort['year']) === 'desc') {
                    $actionOption = 'publication_year descending';
                }
                else {
                    $actionOption = 'publication_year ascendinng';
                }
            }
            else {
                throw new Exception(__e(__FILE__, 'Untreated sort parameter(s): %s. Please add them to EshopProduct::getLugisBoxSearchIndexAnnotationsCode() implementation.', implode(', ', array_keys($sort))));
            }
            $annotations['instrument'][] = array(
                '@type' => 'ChooseAction',
                'name' => 'Order by',
                'actionOption' => $actionOption,
            );
        }
        // add instrument for products index filter
        if (!empty($filter)) {
            if (array_key_exists('new', $filter)) {
                if (!empty($filter['new'])) {
                    $annotations['instrument'][] = array(
                        '@type' => 'ChooseAction',
                        'name' => 'Filter new',
                        'actionOption' => $filter['new'],
                    );
                }
                unset($filter['new']);
            }
            if (array_key_exists('discounted', $filter)) {
                if (!empty($filter['discounted'])) {
                    $annotations['instrument'][] = array(
                        '@type' => 'ChooseAction',
                        'name' => 'Filter discounted',
                        'actionOption' => $filter['discounted'],
                    );
                }
                unset($filter['discounted']);
            }
            if (array_key_exists('language', $filter)) {
                if (!empty($filter['language'])) {
                    $annotations['instrument'][] = array(
                        '@type' => 'ChooseAction',
                        'name' => 'Filter language',
                        'actionOption' => $filter['language'],
                    );
                }
                unset($filter['language']);
            }
            if (array_key_exists('stock', $filter)) {
                if (!empty($filter['stock'])) {
                    $annotations['instrument'][] = array(
                        '@type' => 'ChooseAction',
                        'name' => 'Filter stock',
                        'actionOption' => $filter['stock'],
                    );
                }
                unset($filter['stock']);
            }
            // remove integer keys (created e.g. when reseting filter by "filter[]=")
            foreach (array_keys($filter) as $key) {
                if (Validate::intNumber($key)) {
                    unset($filter[$key]);
                }
            }
            if (!empty($filter)) {
                throw new Exception(__e(__FILE__, 'Untreated filter parameter(s): %s. Please add them to EshopProduct::getLugisBoxSearchIndexAnnotationsCode() implementation.', implode(', ', array_keys($filter))));
            }
        }

        return $this->loadView('EshopProduct/getLugisBoxSearchIndexAnnotationsCode', array(
            'annotations' => $annotations,
            'productsListId' => $productsListId,
        ));
    }

    /**
     * Generates ld+json microdata code of specified product
     *
     * @param int|array $product Single product id or an array returned by
     *          method EshopProduct::getDetails().
     */
    public function getMicrodata($product) {
        if (!is_array($product)) {
            $product = $this->getDetails($product, array(
                'getManufacturer' => true,
                'getAuthors' => true,
            ));
        }
        // see https://schema.org/Product
        $microdata = array(
            '@context' => 'https://schema.org/',
            '@type' => 'Product',
            'name' => $product['name'],
        );
        // image
        if (!empty($product['image']['large'])) {
            $microdata['image'] = $product['image']['large'];
        }
        else if (!empty($product['image']['original'])) {
            $microdata['image'] = $product['image']['original'];
        }
        else if (!empty($product['image'])) {
            $microdata['image'] = end($product['image']);
        }
        // description
        $description = Sanitize::htmlToText($product['description'], false);
        if ($description) {
            $microdata['description'] = $description;
        }
        // ean (see https://schema.org/gtin)
        if (!empty($product['ean'])) {
            $microdata['gtin'] = $product['ean'];
        }
        // manufacturer (see https://schema.org/Brand)
        if (!empty($product['EshopManufacturer']['name'])) {
            $microdata['brand'] = array(
                '@type' => 'Brand',
                'name' => $product['EshopManufacturer']['name'],
            );
        }
        // price & availability
        if (($actualCurrency = Eshop::getActualCurrency())) {
            // see https://schema.org/ItemAvailability
            $disponibilityConversions = array(
                self::PRESALE => 'https://schema.org/PreSale',
                self::AUTOSTOCK => 'https://schema.org/InStock',
                self::STOCK => 'https://schema.org/InStock',
                self::SUPPLIER => 'https://schema.org/OutOfStock',
                self::ON_DEMAND => 'https://schema.org/LimitedAvailability',
                self::SOLDOUT => 'https://schema.org/SoldOut',
            );
            // see https://schema.org/Offer
            $microdata['offers'] = array(
                '@type' => 'Offer',
                'priceCurrency' => $actualCurrency['code'],
                'price' => (string)$product['price_actual_taxed'],
                'availability' => $disponibilityConversions[$product['disponibility']]
            );
        }
        // ratings (see https://schema.org/AggregateRating)
        App::loadModel('App', 'Comment');
        $Comment = new Comment();
        if (
            ($comments = $Comment->findList(array(
                'fields' => array('rating'),
                'conditions' => array(
                    'foreign_model' => 'Eshop.EshopProduct',
                    'foreign_id' => $product['id'],
                    Comment::getPublishedConditions(),
                )
            )))
        ) {
            $ratings = $Comment->getPropertyRatingOptions();
            $ratings = array_keys($ratings);
            $worstRating = reset($ratings);
            $bestRating = end($ratings);
            $microdata['aggregateRating'] = array(
                '@type' => 'AggregateRating',
                'worstRating' => $worstRating,
                'bestRating' => $bestRating,
                'ratingValue' => (string)round(array_sum($comments)/count($comments), 1),
                'ratingCount' => (string)count($comments),
            );
        }
        // pages & isbn (= @type Book)
        if (
            !empty($product['pages'])
            && !empty($product['isbn'])
        ) {
            $microdata['@type'] = 'Book';
            $microdata['additionalType'] = 'Product';
            $microdata['numberOfPages'] = (string)$product['pages'];
            $microdata['isbn'] = (string)$product['isbn'];
            // author
            if (!empty($product['authors'])) {
                $microdata['author'] = (string)$product['authors'];
            }
        }
        App::setMicrodata($microdata);
    }

    /**
     * Translates translated fields of specified records.
     *
     * ATTENTION: Translated records are neither normalized nor validated except of
     * custom normalization available through 'normalize' option.
     *
     * @param string $targetLang Target lang code, e.g 'en'.
     * @param array $options Following are available:
     *      - 'sourceLang' (string) Defalts to App::$lang.
     *      - 'rewrite' (bool) If TRUE then target lang version of translated fields
     *          are considered to be also the source and their values in sourceLang
     *          are rewritten by translations in targetLang. Or in other words: When rewriting
     *          then the source texts are stored already in target fields and they just
     *          need to be translated. Defalts to FALSE.
     *      - Almost all options of Model::find() method are available, above all: 'conditions',
     *          'fields', 'allowFields', 'avoidFields', 'order'. ATTENTION: 'fields',
     *          'allowFields', 'avoidFields' must be provided in qualified form.
     *          ATTENTION: Options 'lang' and 'translate' are forced internally.
     *          ATTENTION: If no 'conditions' are provided then "automatic" translation
     *          conditions are genetated based on Model::$translatedField. If the
     *          actual model does not have this field in schema then an exception is raised.
     *      - 'normalize' (function) Anonymous function having translated record on its input
     *          and returning normalized translated record. Defaults to NULL (no normalization
     *          is done)
     *      - 'microtimeReserve' (int) Time reserve in miliseconds to stop the processing
     *          if free microtime is under this value. Defaults to 20000.
     *      - 'reserve' (bool|string) If TRUE then the translation is executed only if process
     *          reservation succeed. If a tring then passed as reservation name to App::reserveProcessing().
     *          Defaults to TRUE.
     *      - 'progress' (array&) Aux output. Must be provided by reference.
     *          Returns progress array containing keys 'count', 'translatedCount',
     *          'translatedIds', 'skippedCount', 'skippedIds', 'failedCount', 'failedIds',
     *          'errors', 'interrulted'.
     *
     * @return bool TRUE if all specified records has been translated. FALSE if not
     *      (if the process has been interrupted).
     *
     * @throws Exception
     */
    public function translate($targetLang, $options = array()) {
        $options = array_merge(array(
            'avoidFields' => null,
            'normalize' => null,
        ), $options);

        // exclude EshopProduct.slug from translations
        $options['avoidFields'] = (array)$options['avoidFields'];
        if (!in_array('EshopProduct.slug', $options['avoidFields'])) {
            $options['avoidFields'][] = 'EshopProduct.slug';
        }
        // generate EshopProduct.slug from translated name
        if (Validate::callableFunction($options['normalize'])) {
            $customNormalize = $options['normalize'];
            $options['normalize'] = function ($data) use ($customNormalize) {
                $data = $customNormalize($data);
                if (!empty($data['name'])) {
                    $data['slug'] = $data['name'];
                }
                $data = $this->normalize($data, array(
                    'on' => 'update',
                ));
                return $data;
            };
        }
        else {
            $options['normalize'] = function ($data) {
                if (!empty($data['name'])) {
                    $data['slug'] = $data['name'];
                }
                $data = $this->normalize($data, array(
                    'on' => 'update',
                ));
                return $data;
            };
        }

        return parent::translate($targetLang, $options);
    }

   /**
     * Parses provided $value of filter price param (e.g. 12_120)
     * into array('minPrice' => ..., 'maxPrice' => ...).
     *
     * @param string $value Filter price param value
     *
     * @return array Array containing 'minPrice' and 'maxPrice' items. If some of them
     *      is not or is invalidly specified then its value is NULL.
     */
    public function parseFilterPriceParam($value) {
        $prices = Str::explode($this->filterValuesSeparator, $value);
        $maxPrice = array_pop($prices);
        if (!empty($maxPrice) && Validate::intNumber($maxPrice)) {
            $maxPrice = (int)$maxPrice;
        }
        else {
            $maxPrice = null;
        }
        $minPrice = array_shift($prices);
        if (Validate::intNumber($minPrice)) {
            $minPrice = (int)$minPrice;
        }
        else {
            $minPrice = null;
        }
        if ((int)$maxPrice < (int)$minPrice) {
            $maxPrice = $minPrice = null;
        }
        return array(
            'minPrice' => $minPrice,
            'maxPrice' => $maxPrice,
        );
    }

    /**
     * Normalizes product code.
     * Used to pair eshop products with MRP cards (MRP cards cannot use space in code)
     *
     * @param string $code
     *
     * @return string
     */
    public static function normalizeCode($code){
        return preg_replace('/\s+/', '', $code);
    }

    /**
     * Checks if specified product(s) is oversized. If product ids array is provided
     * then it checks if at least one of specified products is oversized.
     *
     * @param int|array $id Single product id or an array of product ids
     *
     * @return bool
     */
    public function isOversized($id) {
        return (bool)$this->find(array(
            'conditions' => array(
                'id' => $id,
                'oversized' => true,
            ),
            'fields' => array('id'),
        ));
    }

    /**
     * @inheritdoc
     *
     * Overrides Model::save() to allow:
     * - removal of image import source and result on image removal
     * to allow its reimport
     * - URL redirection if slug is changed
     *
     * @param array $data
     * @param array $options Model::save() options plus following are available
     *      - 'addRedirection' (bool) Adds redirection on change of slug. Defaults to !ON_LOCALHOST.
     */
    public function save($data, $options = array()) {
        $options = array_merge(array(
            'addRedirection' => !ON_LOCALHOST,
        ), $options);
        // get old record for redirection addition here below
        $oldRecord = array();
        if (
            $options['addRedirection']
            && !empty($data['id'])
            && array_key_exists('slug', $data)
        ) {
            $oldRecord = $this->findFirstBy('id', $data['id'], array(
                'fields' => array($this->name . '.slug'),
                'lang' => Sanitize::value($options['lang']),
            ));
        }
        // save
        $isCreation = $this->isCreation($data, $options);
        $result = parent::save($data, $options);
        if (!$result) {
            return false;
        }
        // remove image import source and result on image removal
        // to allow its reimport
        if (
            !$isCreation
            && array_key_exists('image', $data)
            && empty($data['image'])
        ) {
            $this->loadModel('EshopSupplierProduct');
            $SupplierProduct = new EshopSupplierProduct();
            $SupplierProduct->update(
                array(
                    'image_import_source' => null,
                    'image_import_result' => null,
                ),
                array(
                    'normalize' => false,
                    'validate' => false,
                    'conditions' => array(
                        'run_eshop_products_id' => $data['id'],
                    ),
                )
            );
        }
        // add redirection if slug was changed
        if (
            $options['addRedirection']
            && !empty($oldRecord['slug'])
            && !empty($result['slug'])
            && $oldRecord['slug'] != $result['slug']
        ) {
            $webContentLocator = App::getContentLocatorByPid('Eshop.EshopProducts.view', array(
                'lang' => Sanitize::value($options['lang']),
            ));
            $oldLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($oldRecord['slug']),
            ));
            $newLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($result['slug']),
            ));
            App::loadModel('App', 'UrlRedirection');
            $UrlRedirection = new UrlRedirection();
            $UrlRedirection->add($oldLocator, $newLocator);
        }
        return $result;
    }

    /**
     * Returns Facebook ViewContent event tracking code.
     * See https://developers.facebook.com/docs/meta-pixel/implementation/pixel-for-collaborative-ads > ViewContent
     * See https://developers.facebook.com/docs/meta-pixel/reference#utilizing_params > ViewContent
     * See https://www.facebook.com/business/help/402791146561655?id=1205376682832142 > ViewContent
     *
     * ATTENTION: Conversion code is generated only if facebook pixel (meta pixel)
     * code (see https://developers.facebook.com/docs/meta-pixel/get-started#base-code )
     * is inserted in customCode (htmlHead|htmlBodyStart|htmlBodyEnd)
     *
     * ATTENTION: If App::$useJsEmbroidery is FALSE and 'debug' option is FALSE
     * then this method generates nothing. If 'debug' option is TRUE then this method
     * returns tracking code enclosed in HTML comments (value of is ignored as the js code is not executed).
     *
     * @param int|array $productIds Single product id or an array of such ids.
     * @param array $options Following are available:
     *      - 'debug' (bool) If TRUE then this code is returned in HTML comment block.
     *      Defautls to value of ON_LOCALHOST constant.
     *
     * @return string Facebook ViewContent event tracking code
     */
    public function getFacebookViewCode($productIds, $options = array()) {
        $defaults = array(
            'debug' => ON_LOCALHOST,
        );
        $options = array_merge($defaults, $options);
        // normalize
        $productIds = (array)$productIds;

        if (
            !$productIds
            ||
            !App::$useJsEmbroidery
            && !$options['debug']
            ||
            // check for presence of facebook pixel in custom code
            (
                $customHtmlCode =
                    App::getSetting('App', 'customCode.htmlHead') . ' ' .
                    App::getSetting('App', 'customCode.htmlBodyStart') . ' ' .
                    App::getSetting('App', 'customCode.htmlBodyEnd')
            )
            && !preg_match('#fbevents\.js|https://www\.facebook\.com/tr\?id=#', $customHtmlCode)
        ) {
            return '';
        }

        // load view
        $code = $this->loadView('EshopProduct/getFacebookViewCode', array(
            'debug' => $options['debug'],
            'productIds' => $productIds,
        ));
        if ($options['debug']) {
            $code = Html::comment($code);
        }
        return $code;
    }

    public function validate_variantIds($fieldValue, $fieldName, $data, &$validation) {
        $validation['exit'] = empty($data['variant_ids']);
        return true;
    }
}