<?php
/**
 * Výpis produktov
 */
App::loadModel('App', 'ContentBlock');
class ContentBlockEshopProductsIndex extends ContentBlock {
    
    /**
     * If TRUE then the content block is available in dropdown menu of FormHelper::contentBlocks() input.
     * If you don't want to make it available for use in project then set it to FALSE.
     * ATTENTION: this does not influence activity of existing block instances.
     *
     * @var bool 
     */
    protected $available = true;

    /**
     * Content block admin (backend) view
     *
     * @var string
     */
    protected $adminView = 'ContentBlockEshopProductsIndex/admin_view';

    /**
     * Content block frontend view
     *
     * @var string
     */
    protected $view = 'ContentBlockEshopProductsIndex/view';

    /**
     * Content block view custom data fields provided like pairs {fieldName} => {defaultValue}
     * They may not contain fields defined in ContentBlockInstance::$schema!
     * Only fields specified here are saved for instance!
     *
     * @var array
     */
    protected $fields = array(
        // block title fields
        'title' => '',
        'title_tag' => 'h1',
        'title_color' => '',
        'title_include_in_text_menu' => '',
        'title_label_in_text_menu' => '',
        'title_subtitle' => '',
        'title_subtitle_locator' => '',
        
        // custom fields
        'typeOfFilter' => 'static',
        'filter_product' => '',
        'filter_group' => '',
        'filter_category' => null,
        'filter_type' => '',
        'filter_manufacturer' => '',
        'filter_author' => '',
        'filter' => '',
        'set_seo' => '',
        'paginate' => '1',
        'limit' => '', // default value set in constructor
        'avoidProduct' => '',
        'sort_bestsellers' => false,
        'availableOnly' => false,
        'discount' => false, 
        'loadMoreButtonLabel' => '',
        'indexType' => '',
//        'category_own_products' => false,
//        'sort' => null,
//        'direction' => 'ASC',
//        'sort_random' => false,
//        'offset' => null,
//        'page' => null,
//        'hasImage' => null,
//        'discount' => null,
//        'recommended' => null,
        
        // block param fields
        'block_top_padding' => '',
        'block_bottom_padding' => '',
        'block_center_content' => 'wide',
        //'block_bg_gradient' => '',
        'block_bg_image' => '',
        'block_bg_color' => '',
        'block_bg_horizontal_position' => '',
        'block_bg_vertical_position' => '',
        'block_bg_size' => 'cover',
        'block_bg_repeat' => '',
        'block_bg_parallax' => '',
    );

    /**
     * File fields of content block data.
     * Definition is the same as for Model::$fileFields
     *
     * @var array
     */
    protected $fileFields = array(

        'block_bg_image' => array(
            'quality' => 90,
            'variants' => array(
                '' => array(
                    'fit' => array(1920, 1920),
                ),
                'preview' => array(
                    'cover' => array(230, 230),
                ),
            ),
        ),
    );

    /**
     * Default file fields variants
     * for prepareViewData and prepareAdminViewData methods
     * {fileField} => {variant}, NULL = first specified variant
     * @var array
     */
    protected $defaultAdminFileFieldVariants = array(
        'block_bg_image' => 'preview',
    );
    protected $defaultFileFieldVariants = array(
        'block_bg_image' => null,
    );

    public function __construct() {

        $this->blockName = __a(__FILE__, 'Zoznam produktov');
        $this->description = __a(__FILE__, 'Zoznam produktov na základe zadaných podmienok');
        $this->imageUrlPath = '/app/modules/Eshop/img/cb-products-index.png';

        $this->validations = array(
            'block_bg_image' => array(
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                    'alternative' => 'contentBlockFields',
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                    'alternative' => 'contentBlockFields',
                ),
            ),
        );
        
        // set dynamic default values
        $this->fields = array_merge($this->fields, array(
            'limit' => App::getSetting('Eshop', 'EshopProduct.pagingLimit'),
        ));

        parent::__construct();
    }
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];

        foreach (
            array(        
                'filter_product',
                'filter_group',
                'filter_category',
                'filter_type',
                'filter_manufacturer',
                'filter_author',
            ) as $field
        ) {            
            if (!empty($data[$field])) {
                if (is_string($data[$field])) {
                    $data[$field] = explode(';', $data[$field]);
                }
                $data[$field] = array_filter(array_map('trim', $data[$field]));
            }
        }

        return parent::normalize($data, $options);
    }
    
    public function prepareAdminViewData($data, $dataAreSubmitted) {
        foreach ($this->defaultAdminFileFieldVariants as $fileField => $variant) {
            if (
                !$dataAreSubmitted
                && !empty($data[$fileField])
            ) {
                $data[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                    'file' => $data[$fileField],
                    'variant' => $variant,
                ));
            }
        }
        return $data;
    }

    public function prepareViewData($data) {
        foreach ($this->defaultFileFieldVariants as $fileField => $variant) {
            if (!empty($data[$fileField])) {
                $data[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                    'file' => $data[$fileField],
                    'variant' => $variant,
                ));
            }
        }
        // normalize typeOfFilter when called by App::loadContentBlockView();
        if (empty($data['typeOfFilter'])) {
            $data['typeOfFilter'] = !empty($data['filter']) ? 'dynamic' : 'static';
        }
        return $data;
    }    
    
}
