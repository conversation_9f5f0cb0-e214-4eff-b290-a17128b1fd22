<?php

class EshopProductImport extends Model {
    
    protected $Product = null;
    protected $SupplierProduct = null;
    protected $Manufacturer = null;
    protected $ManufacturerRange = null;
    protected $Author = null;
    protected $Category = null;
    protected $Image = null;
    protected $Export = null;
    
    /**
     * Basic progress properties. You can add any other if needed
     * 
     * @var array
     */
    protected $progress = array(
        'processedProductsCount' => 0,
        'importedProductsCount' => 0,
        'skippedProductsCount' => 0,
        'failedProductsCount' => 0,
        
        'createdProductsCount' => 0,
        'updatedProductsCount' => 0,
        
        'createdSupplierProductsCount' => 0,
        'updatedSupplierProductsCount' => 0,
        
        'interrupted' => null,
        'errors' => array(),
        'warnings' => array(),
    );
    
    //
    // IMPORT SPECIFIC PROPERTIES
    //
    // properties used for both addition and update imports
    protected $importPid = null;
    protected $supplier = null;
    protected $pairField = null;
    protected $pairFieldValues = array();
    protected $Reader = null;
    protected $categoriesRoot = array();
    protected $selectedItems = null;
    protected $selectedItemsCount = null;
    protected $defaultMarginRate = null;
    // properties used for addition imports
    protected $defaultDiscountRate = null;
    protected $discountTo = null;
    protected $importedSortiments = array();
    protected $manufacturerDiscountRates = array();
    protected $importedManufacturers = array();
    protected $addNewManufacturers = false;
    protected $ignoreProductsWithoutCategory = false;
    // properties used for update imports
    protected $supplierProductIds = null;
    protected $importedFields = array();
    protected $importedSupplierFields = array();
    protected $ignoreNewValueIf = array('', null);
    protected $updateOldValueIf = false;
    
    //
    // INTERNAL VARIABLES
    //
    protected $configFilepath = '/app/modules/Eshop/config/importConfig.php';
    protected $categoryConversions = null; // Loaded on runtime. Array of pairs {supplierCategoryCode} => {categoryId}
    protected $productSchema = null;
    
    /**
     *
     * @var type actually processed record
     */
    protected $record = array();
    
    /**
     *
     * @var type actually processed supplier record
     */
    protected $supplierRecord = array();
    
    public function __construct() {
        parent::__construct();
        
        // load used models
        $this->loadModel('EshopProduct');
        $this->Product = new EshopProduct();
        $this->loadModel('EshopSupplierProduct');
        $this->SupplierProduct = new EshopSupplierProduct();
        $this->loadModel('EshopManufacturer');
        $this->Manufacturer = new EshopManufacturer();
        $this->loadModel('EshopManufacturerRange');
        $this->ManufacturerRange = new EshopManufacturerRange();
        $this->loadModel('EshopAuthor');
        $this->Author = new EshopAuthor();
        $this->loadModel('EshopProductCategory');
        $this->Category = new EshopProductCategory();
        $this->loadModel('EshopProductImage');
        $this->Image = new EshopProductImage();
        $this->loadModel('EshopExport');
        $this->Export = new EshopExport();
        
        $this->productSchema = $this->Product->getPropertySchema();
        
        $this->configFilepath = File::normalizePath($this->configFilepath);
        
        // set validations
        $this->validations = array(          
            'importPid' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please choose import name'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please choose import name'),
                ),
            ),   
            'catalogueFile' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please choose a file'),
                    'alternative' => array(
                        'internalStockUpdate',
                    ),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please choose a file'),
                    'alternative' => array(
                        'internalStockUpdate',
                    ),
                ),
                array(
                    'rule' => array('path', array('exists' => true, 'file' => true)),
                    'message' => __v(__FILE__, 'File does not exist or is not readable.'),
                    'alternative' => array(
                        'pemicAddition', 
                        'informAddition', 
                        'partnertechnicAddition',
                        'albatrosAddition',
                        'pemicAvailabilityUpdate', 
                        'pemicUpdate',
                        'internalStockUpdate',
                        'ikarUpdate',
                        'partnertechnicUpdate',
                        'informUpdate', 
                        'albatrosUpdate',
                    ),
                ),
            ),   
            'priceFile' => array(
                array(
                    'rule' => array('path', array('exists' => true, 'file' => true)),
                    'message' => __v(__FILE__, 'File does not exist or is not readable.'),
                    'alternative' => array(
                        'pemicAddition',
                        'pemicPriceUpdate', 
                    ),
                ),
            ),   
            'stockFile' => array(
                array(
                    'rule' => array('path', array('exists' => true, 'file' => true)),
                    'message' => __v(__FILE__, 'File does not exist or is not readable.'),
                    'alternative' => array(
                        'pemicAddition',
                        'pemicAvailabilityUpdate',
                        'partnertechnicUpdate',
                    ),
                ),
            ),   
            'selectionFile' => array(
                array(
                    'rule' => array('path', array('exists' => true, 'file' => true)),
                    'message' => __v(__FILE__, 'File does not exist or is not readable.'),
                ),
            ),   
        );
    }
    
    /**
     * Imports new products from supplier file
     * 
     * ATTENTION: this method turns off sql logging!
     * 
     * @param string $importPid One of 'pemicAddition', 'informAddition', 'partnertechnicAddition',
     *      'albatrosAddition', 'ikarAddition', 'alteregoAddition', 'frankanaAddition'
     * @param array $options Following are available:
     *      - 'catalogueFile' (string) App root relative path to catalogue file. 
     *          If not provided or empty then it is downloaded. Defaults to NULL.
     *      - 'priceFile' (string) App root relative path to price file. 
     *          If not provided or empty then it is downloaded. Defaults to NULL.
     *      - 'stockFile' (string) App root relative path to stock file. 
     *          If not provided or empty then it is downloaded. Defaults to NULL.
     *      - 'selectionFile' (string) Name of CSV file placed under userfiles/files/import. 
     *          It specifies selection of items from $catalogue which should be imported. 
     *          In the first column there are EAN codes. In next columns there are 
     *          explicit values for data. Defaults to NULL, means it is not used and all 
     *          items from catalogue are imported.
     *      - 'useFullCatalogue' (bool) Applies only for 'pemicAddition' import if 
     *          'catalogueFile' is downloaded. If TRUE then a catalogue_full file 
     *          is downloaded. Defaults to FALSE. 
     *      - 'batchSize' (int) Number of records after which the batch is saved.
     *          Defaults to 10000.
     *      - 'microtimeReserve' (int) Time reserve in miliseconds to stop the processing 
     *          if free microtime is under this value. Defaults to 10000.
     *      - 'selectionFile' (string) Name of CSV file placed under userfiles/files/import. 
     *          It specifies selection of items from $catalogue which should be imported. 
     *          In the first column there are EAN codes. In next columns there are 
     *          explicit values for data. Defaults to NULL, means it is not used and all 
     *          items from catalogue are imported.
     * 
     * @return bool|array FALSE on invalid supplier, XML open failure or invalid XML file
     *      or any kind of other processing failure. Error message is set in '_processing' errors.
     *      On succes the array of progress is returned.
     */
    public function importAddition($importPid, $options = array()) {
        $defaults = array(
            'catalogueFile' => null,
            'priceFile' => null,
            'stockFile' => null,
            'selectionFile' => null,
            'useFullCatalogue' => false,
            'czkConversionRate' => null, // used by kosmas
            'batchSize' => 1000,
            'microtimeReserve' => 10000,
            'recordOffset' => 0,
            'processAmount' => 0, // for debuging, if 0 then all records are processed
            'importAmount' => 0, // for debuging, if 0 then all records are imported
            'simulate' => false, //for debuging, if TRUE then all imported records are written into log file EshopProductImport_importAddition instead to BD. Only categories created from category_paths are created even if simulate is TRUE.
        );
        $options = array_merge($defaults, $options);
        if ($options['processAmount']) {
            $options['processAmount']++;
        }
        
        // !!! keep this is synchro with the app/config/database.php > 'tableReservationTimeLimit' => ...
        set_time_limit(600);
        ini_set('memory_limit', '512M');
        
        $this->importPid = $importPid;
        
        // Ensure that there are not launched 2 paralel imports of new products
        try {
            App::reserveProcessing('EshopProductImport_importAddition');
        } 
        catch (Throwable $e) {
            $this->progress['warnings'][] = __v(__FILE__, "There is already a runnig import '%s'. Try later.", $this->importPid);
            return $this->progress;
        }
        
        // turn off sql logs to not get error ERR_RESPONSE_HEADERS_TOO_BIG
        App::setSqlLogging(false);
        
        try {
            // validate provided data
            if (!$this->validate(
                array(
                    'importPid' => $this->importPid,
                    'catalogueFile' => $options['catalogueFile'],
                    'priceFile' => $options['priceFile'],
                    'stockFile' => $options['stockFile'],
                    'selectionFile' => $options['selectionFile'],
                ),
                array(
                    'on' => 'create',
                    'alternative' => $this->supplier,
                )
            )) {
                return false;
            }
            
    //        $defaultTaxRate = $this->getSetting('defaultTaxRate');
    //        $lowTaxRate = $this->getSetting('lowTaxRate');

            // Get the catalogue reader instance
            // 
            // Each reader class loaded here below must implement method readRecord() which 
            // return array containing (at least some of) Product schema fields + 'authors' 
            // (array of authors) + 'manufacturer' (manufacturer name) + 'categories' 
            // (array of supplier category codes) + any other custom field you will process here

            // PEMIC - there are also some pemic specific if() in logic of import
            if ($this->importPid === 'pemicAddition') {
                $this->supplier = 'pemic';
                $this->categoriesRoot = 'categories';
    //                $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'ean'; 
                $this->loadPairFieldValues();
                $this->loadLib('PemicCatalogueCsvReader');
                $this->Reader = new PemicCatalogueCsvReader(array(
                    'catalogueFile' => $options['catalogueFile'],
                    'priceFile' => $options['priceFile'],
                    'stockFile' => $options['stockFile'],
                    'useFullCatalogue' => $options['useFullCatalogue'],
                    'accessId' => '03C86064-ACBD-4C68-98D8-D2A4C5FD124E', // alterego.sk access
                    'avoidEans' => &$this->pairFieldValues,
                    'recordOffset' => $options['recordOffset'],
                ));
                // define which sortiment is imported and which not
                $this->importedSortiments = array(
                    'balici papir' => false,
                    'diar knizni' => true,
                    'kalendar nastenny' => true,
                    'kalendar ostatni' => true,
                    'kalendar stolni' => true,
                    'karty' => true,
                    'kniha' => true,
                    'mapa knizni' => true,
                    'mapa nastenna' => true,
                    'mapa skladana' => true,
                    'medium CD' => true,
                    'medium DVD' => true,
                    'nespecifikovano' => false,
                    'ostatni' => true, // here are included also hračky
                    'periodikum ostatni' => false,
                    'periodikum tydenni' => false,
                    'stolni hra' => true,
                    'taska' => false,
                );
                // Pemic addition imports products for all existing manufacturers and even new manufacturers
                $this->loadManufacturerDiscountRates();
                $this->loadImportedManufacturers();
                $this->addNewManufacturers = true;
                $this->ignoreProductsWithoutCategory = false;
                // default discount rate applied to all imported products
                $this->defaultDiscountRate = 
                    $this->SupplierProduct->getDefaultDiscountRate($this->supplier);
                $this->defaultMarginRate = 30;
                $this->discountTo = null; //date('Y-m-d', time() + 60 * 60 * 24 * 21); //null;
            }
            /*/ unavailable for this site
            // INFORM
            elseif ($this->importPid === 'informAddition') {
                $this->supplier = 'inform';
                $this->categoriesRoot = 'categories';
    //                $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'ean'; 
                $this->loadPairFieldValues();
                $this->loadLib('InformCatalogueCsvReader');
                $this->Reader = new InformCatalogueCsvReader(array(
                    'catalogueFile' => $options['catalogueFile'],
                    'accessUser' => 'vydavatel.sk', // vydavatel.sk access
                    'accessPassword' => 'zjavka', // vydavatel.sk access
                    'minShipmentTimeOffStock' => 10, // vydavatel.sk specific
                    'recordOffset' => $options['recordOffset'],
                ));
                // define which sortiment is imported and which not
                $this->importedSortiments = array(
                    // = all sortiments are imported
                );
                // Inform addition imports products for all existing manufacturers and even new manufacturers
                $this->loadManufacturerDiscountRates();
                $this->loadImportedManufacturers();
                $this->addNewManufacturers = true;
                $this->ignoreProductsWithoutCategory = false;
                // default discount rate applied to all imported products
                $this->defaultDiscountRate = 
                    $this->SupplierProduct->getDefaultDiscountRate($this->supplier);
                $this->defaultMarginRate = 25;
                $this->discountTo = null;                
            }
            //*/
            
            // PARTNER TECHNIC
            elseif ($this->importPid === 'partnertechnicAddition') {
                $this->supplier = 'partnertechnic';
                $this->categoriesRoot = 'categories';
    //                $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'ean'; 
                $this->loadPairFieldValues();
                $this->loadLib('PartnerTechnicCatalogueXmlReader');
                $this->Reader = new PartnerTechnicCatalogueXmlReader(array(
                    'catalogueFile' => $options['catalogueFile'],
                    'useFullCatalogue' => $options['useFullCatalogue'],
                    'accessKey' => '0004778V10000101',
                    'recordOffset' => $options['recordOffset'],
                ));
                // define which sortiment is imported and which not
                $this->importedSortiments = array(
                    // = all sortiments are imported
                );
                // PT addition imports products for all existing manufacturers and even new manufacturers
                $this->loadManufacturerDiscountRates();
                $this->loadImportedManufacturers();
                $this->addNewManufacturers = true;
                // default discount rate applied to all imported products
                $this->defaultDiscountRate = 
                    $this->SupplierProduct->getDefaultDiscountRate($this->supplier);
                $this->defaultMarginRate = 30;
                $this->discountTo = null; //date('Y-m-d', time() + 60 * 60 * 24 * 21); //null;
            }
            
            // ALBATROS
            elseif ($this->importPid === 'albatrosAddition') {
                $this->supplier = 'albatros';
                $this->categoriesRoot = 'categories';
    //                $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'ean'; 
                $this->loadPairFieldValues();
                $this->loadLib('AlbatrosCatalogueXmlReader');
                $this->Reader = new AlbatrosCatalogueXmlReader(array(
                    'catalogueFile' => $options['catalogueFile'],
                    'accessKey' => 'a018668da9b64263916f7e6a31ddb8f0',
                    'recordOffset' => $options['recordOffset'],
                ));
                // define which sortiment is imported and which not
                $this->importedSortiments = array(
                    // = all sortiments are imported
                );
                $this->loadManufacturerDiscountRates();
                $this->loadImportedManufacturers();
                $this->ignoreProductsWithoutCategory = false;
                // default discount rate applied to all imported products
                $this->defaultDiscountRate = 
                    $this->SupplierProduct->getDefaultDiscountRate($this->supplier);
                $this->defaultMarginRate = 32; // fixed (it is not in catalogue)
                $this->discountTo = null; //date('Y-m-d', time() + 60 * 60 * 24 * 21); //null;
            }
            
            // IKAR
            elseif ($this->importPid === 'ikarAddition') {
                $this->supplier = 'ikar';
                $this->categoriesRoot = 'categories';
    //                $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'ean'; 
                $this->loadPairFieldValues();
                $this->loadLib('IkarCatalogueXmlReader');
                $this->Reader = new IkarCatalogueXmlReader(array(
                    'catalogueFile' => $options['catalogueFile'],
                    'username' => '366178',
                    'password' => '00854_AlterEgo',
                    // use 'storedTimestampName' to skip already imported items
                    'storedTimestampName' => $options['useFullCatalogue'] ? null : $this->importPid,
                    'recordOffset' => $options['recordOffset'],
                ));
                // define which sortiment is imported and which not
                $this->importedSortiments = array(
                    'knihy-viazane' => true,
                    'leporelo' => true,
                    'paperback' => true,
                    'kruzkova-vazba' => true,
                    'flexo' => true,
                    'spiralova-vazba' => true,
                    'mc' => false,
                    'cd' => true,
                    'hry' => true,
                    'hracky' => true,
                    'karty' => true,
                    'puzzle' => true,
                    'papierensky-tovar' => false,
                    'casopisy-noviny' => false,
                    'plagaty-reprodukcie' => false,
                    'esotericke-predmety' => false,
                    'ostatne' => true,
                    'video-VHS' => false,
                    'cd-rom' => true,
                    'dvd' => true,
                    'pc-hry' => true,
                    'neznamy' => true,
                );
                // Ikar addition imports products for all existing manufacturers and even new manufacturers
                $this->loadManufacturerDiscountRates();
                $this->loadImportedManufacturers();
                $this->addNewManufacturers = true;
                $this->ignoreProductsWithoutCategory = false;
                // default discount rate applied to all imported products
                $this->defaultDiscountRate = 
                    $this->SupplierProduct->getDefaultDiscountRate($this->supplier);
                $this->defaultMarginRate = 30;
                $this->discountTo = null; //date('Y-m-d', time() + 60 * 60 * 24 * 21); //null;
            }
            
            /*/ unavailable for this site
            // SLOVART
            elseif ($this->importPid === 'slovartAddition') {
                $this->supplier = 'slovart';
                $this->categoriesRoot = 'categories';
    //                $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'ean'; 
                $this->loadPairFieldValues();
                $this->loadLib('SlovartCatalogueXmlReader');
                $this->Reader = new SlovartCatalogueXmlReader(array(
                    'catalogueFile' => $options['catalogueFile'],
                    'accessKey' => '7d9e2a6dc299a32e686817793ad8e985',
                    'recordOffset' => $options['recordOffset'],
                ));
                // define which sortiment is imported and which not
                $this->importedSortiments = array(
                    // = all sortiments are imported
                );
                // Ikar addition imports products for all existing manufacturers and even new manufacturers
                $this->loadManufacturerDiscountRates();
                $this->loadImportedManufacturers();
                $this->addNewManufacturers = true;
                $this->ignoreProductsWithoutCategory = false;
                // default discount rate applied to all imported products
                $this->defaultDiscountRate = 
                    $this->SupplierProduct->getDefaultDiscountRate($this->supplier);
                $this->defaultMarginRate = 33;
                $this->discountTo = null; //date('Y-m-d', time() + 60 * 60 * 24 * 21); //null;
            }
            //*/
            
            // KOSMAS
            elseif ($this->importPid === 'kosmasAddition') {
                $this->supplier = 'kosmas';
                $this->categoriesRoot = 'categories';
    //                $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'ean'; 
                $this->loadPairFieldValues();
                $this->loadLib('KosmasCatalogueXmlReader');
                $this->Reader = new KosmasCatalogueXmlReader(array(
                    'catalogueFile' => $options['catalogueFile'],
                    'useFullCatalogue' => $options['useFullCatalogue'],
                    'czkConversionRate' => $options['czkConversionRate'],
                    'accessKey' => 'T5569',
                    'recordOffset' => $options['recordOffset'],
                ));
                // define which sortiment is imported and which not
                $this->importedSortiments = array(
                    // = all sortiments are imported
                );
                // Ikar addition imports products for all existing manufacturers and even new manufacturers
                $this->loadManufacturerDiscountRates();
                $this->loadImportedManufacturers();
                $this->addNewManufacturers = true;
                $this->ignoreProductsWithoutCategory = false;
                // default discount rate applied to all imported products
                $this->defaultDiscountRate = 
                    $this->SupplierProduct->getDefaultDiscountRate($this->supplier);
                $this->defaultMarginRate = 30; // 99% of products have 30% margin for alterego
                $this->discountTo = null; //date('Y-m-d', time() + 60 * 60 * 24 * 21); //null;
            }
            // ALTEREGO (INTERNAL)
            elseif($this->importPid === 'alteregoAddition') {
                $this->supplier = 'internal';
                $this->categoriesRoot = 'categories';
    //                $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'ean'; 
                $this->loadPairFieldValues();
                $this->loadLib('AlteregoCatalogueCsvReader');
                $this->Reader = new AlteregoCatalogueCsvReader(array(
                    'recordOffset' => $options['recordOffset'],
                ));
                // define which sortiment is imported and which not
                $this->importedSortiments = array(
                    // = all sortiments are imported
                );
                // Ikar addition imports products for all existing manufacturers and even new manufacturers
                $this->loadManufacturerDiscountRates();
                $this->loadImportedManufacturers();
                $this->addNewManufacturers = true;
                $this->ignoreProductsWithoutCategory = true;
                // default discount rate applied to all imported products
                $this->defaultDiscountRate = null;
                $this->defaultMarginRate = null;
                $this->discountTo = null; //date('Y-m-d', time() + 60 * 60 * 24 * 21); //null;     
            }
            // FRANKANA
            elseif ($this->importPid === 'frankanaAddition') {
                $this->supplier = 'frankana';
                $this->pairField = 'code';
                $this->loadPairFieldValues();
                $this->loadLib('FrankanaProductApiReader');
                $this->Reader = new FrankanaProductApiReader();
                $this->loadImportedManufacturers();
                $this->addNewManufacturers = true;
                // default discount rate applied to all imported products
                $this->defaultDiscountRate = null;
                $this->defaultMarginRate = null;
                $this->discountTo = null;
            }
            else {
                $this->setError('importPid', __v(__FILE__, 'Unknown import name %s', $this->importPid));
                return false;
            }            
            
            // read selected items if selection file provided
            if (!empty($options['selectionFile'])) {
                $this->loadSelectionRecords($options['selectionFile']);
            }
            
            // reserve tables
            $this->reserveTables('EshopProductImport_importAddition', array(
                'EshopProduct',
                'EshopSupplierProduct',
                'EshopManufacturer',
                'EshopManufacturerRange',
                'EshopAuthor',
                'EshopProductCategory',
            ));
            
            // load eans of cleaned up products which have been already exported to MRP
            // (file cleanedUpExportedProducts.php is created in Tool::cleanUpEshopOldProducts())
            $cleanedUpExportedProducts = array();
            $backupFile = 
                'app' . DS . 'modules' . DS . 'Eshop' . DS . 'config' . DS . 'cleanedUpExportedProducts.php';
            if (is_readable(ROOT . DS . $backupFile)) {
                App::loadScript($backupFile, array('catchVariables' => 'exportedProducts'), $vars);
                if (!empty($vars['exportedProducts'])) {
                    $cleanedUpExportedProducts = &$vars['exportedProducts'];
                    // remove codes which has been meanwhile recreated
                    $recreatedCodes = $this->Product->findList(array(
                        'key' => 'code',
                        'fields' => array('code'),
                        'conditions' => array(
                            'code' => $cleanedUpExportedProducts,
                        )
                    ));
                    foreach ($cleanedUpExportedProducts as $ean => $code) {
                        if (isset($recreatedCodes[$code])) {
                            unset($cleanedUpExportedProducts[$ean]);
                            unset($recreatedCodes[$code]);
                        }
                        if (empty($recreatedCodes)) {
                            break;
                        }
                    }
                }
            }
            // get next id of products
            $productNextId = $this->Product->getNextId();
            // get next code of products - vydavatel.sk specific
            $productNextCode = $this->Product->getNextCode();
            // initialize progress
            $this->progress['processedProductsCount'] = $options['recordOffset']; //0;
            // initialize batch variables
            $batchCount = 0;
            $newProducts = array();
            $newSupplierProducts = array();
            $newProductSlugs = array();
            $newManufacturers = array();
            $newManufacturerRanges = array();
            $newAuthors = array();
            $newProductAuthors = array();
            $newCategoryProducts = array();
            $pricesAreTaxed = App::getSetting('Eshop', 'pricesAreTaxed');
            $minMarkupRate = (float)$this->SupplierProduct->getMinMarkupRate(array(
                'supplierPid' => $this->supplier
            ));
            App::loadModel('Eshop', 'EshopProductCategory');
            $ProductCategory = new EshopProductCategory();
            $unclassifiedCategoryId = $ProductCategory->findFieldBy('id', 'pid', 'unclassified');
            if ($this->pairField === 'supplier_product_id') {
                $pairFieldOptions = DB::getFieldOptions($this->SupplierProduct->getPropertyTable(), $this->pairField);
            }
            else {
                $pairFieldOptions = DB::getFieldOptions($this->Product->getPropertyTable(), $this->pairField);
            }
            // do a shortcut variable
            $record = &$this->record;
            $supplierRecord = &$this->supplierRecord;
            $launchImagesImport = false;
            while (
                ($rawRecord = $this->Reader->readRecord())
                // if selected items are provided then quit as soon as all of them are imported
                && ($this->selectedItems === null || $this->selectedItemsCount > $this->progress['importedProductsCount'])
            ) {
                
                // check remaining time - finish the processing before php 
                // would be interrupted by execution time constrain
                if (App::getFreeMicrotime() < $options['microtimeReserve']) {
                    $this->progress['interrupted'] = true;
                    break;
                }
                // finish the processing if processAmount or importAmount is reached
                if (
                    $options['processAmount'] && --$options['processAmount'] == 0 //debug
                    || $options['importAmount'] && $options['importAmount'] == $this->progress['importedProductsCount'] //debug
                ) {
                    break;
                }
                
                $this->progress['processedProductsCount']++;
                                
                $record = array();
                // 'id' is used to create habtm records
                $record['id'] = $productNextId++;
                // to recreated products (after clean up) the old code will be set
                // (see here below on 'exported' field attribution)
                // If code is specified explicitly (in internal imports) then do not
                // use implicit generated code.
                if ($this->pairField !== 'code') {
                    $record['code'] = $productNextCode++;
                }
                $record['active'] = true; 
                
                // get pair field - ean or code (required)
                if (empty($rawRecord[$this->pairField])) {
                    $this->progress['errors'][] = __v(__FILE__, "Product with missing %s (record no. %s, supplier id: %s)", 
                        $this->pairField, $this->progress['processedProductsCount'], Sanitize::value($rawRecord['supplier_product_id']));
                    $this->progress['failedProductsCount']++;
                    $productNextId--;
                    $productNextCode--;
                    continue;
                }
                // validate pair field length to avoid incorrect save in DB
                if (strlen($rawRecord[$this->pairField]) > $pairFieldOptions['length']) {
                    $this->progress['errors'][] = __v(__FILE__, "Product with invalid %s - it is longer then allowed %s chars (%s: %s)", 
                        $this->pairField, $pairFieldOptions['length'], $this->pairField, $rawRecord[$this->pairField]);
                    $this->progress['failedProductsCount']++;
                    $productNextId--;
                    $productNextCode--;
                    continue;
                }
                // validate pair field type to avoid incorrect save in DB
                if (
                    $pairFieldOptions['type'] === 'int' 
                    && !Validate::intNumber($rawRecord[$this->pairField])
                ) {
                    $this->progress['errors'][] = __v(__FILE__, "Product with invalid %s - it is not integer (%s: %s)", 
                        $this->pairField, $this->pairField, $rawRecord[$this->pairField]);
                    $this->progress['failedProductsCount']++;
                    $productNextId--;
                    $productNextCode--;
                    continue;
                }
                if ($this->pairField !== 'supplier_product_id') {
                    $record[$this->pairField] = $rawRecord[$this->pairField];
                }
//                $substanceType = $this->Product->getSubstanceType($rawRecord);
                // skip product if...
                if (
                    // skip item which are not found in selected items (but only if selected items are provided)
                    $this->selectedItems !== null
                    && !isset($this->selectedItems[$rawRecord[$this->pairField]])
                    ||
                    // skip existing products
//rblb//                    isset($this->pairFieldValues[$substanceType][$rawRecord[$this->pairField]])
                    isset($this->pairFieldValues[$rawRecord[$this->pairField]])
                    || 
                    // skip soldout products
                    !empty($rawRecord['availability'])
                    && $rawRecord['availability'] === 'enum_soldout'
                    ||
                    // skip sortiments which are not imported but only if there are 
                    // any imported sortiments specified
                    !empty($this->importedSortiments)
                    && array_key_exists('sortiment', $rawRecord)
                    && empty($this->importedSortiments[$rawRecord['sortiment']])
                    ||
                    // skip norms which are not valid
                    $this->supplier == 'normservis' 
                    && empty($rawRecord['valid'])
                ) {
                    $this->progress['skippedProductsCount']++;
                    $productNextId--;
                    $productNextCode--;
                    continue;
                }
                
                // if selected item are provided then apply selected item data on raw record data
                if ($this->selectedItems !== null) {
                    $rawRecord = array_merge($rawRecord, $this->selectedItems[$rawRecord[$this->pairField]]);
                }
                                
                // set name (required)
                if (empty($rawRecord['name'])) {
                    $this->progress['errors'][] = __v(__FILE__, "Product with missing name (%s: %s)", 
                        $this->pairField, $rawRecord[$this->pairField]);
                    $this->progress['failedProductsCount']++;
                    $productNextId--;
                    $productNextCode--;
                    continue;
                }  
                $record['name'] = $rawRecord['name'];
                $record['seo_title'] = $rawRecord['name'];
                
                // tax rate (required)
                if (empty($rawRecord['tax_rate'])) {
                    $this->progress['errors'][] = __v(__FILE__, "Product with missing tax rate (%s: %s)", 
                        $this->pairField, $rawRecord[$this->pairField]);
                    $this->progress['failedProductsCount']++;
                    $productNextId--;
                    $productNextCode--;
                    continue;
                }  
                $record['tax_rate'] = $rawRecord['tax_rate'];

                // price (required)
                if (empty($rawRecord['price_taxed'])) {
                    $this->progress['errors'][] = __v(__FILE__, "Product with missing price (%s: %s)", 
                        $this->pairField, $rawRecord[$this->pairField]);
                    $this->progress['failedProductsCount']++;
                    $productNextId--;
                    $productNextCode--;
                    continue;
                }
                $record['price'] = $rawRecord['price_taxed'];
                if (
                    !$pricesAreTaxed
                    && !empty($record['tax_rate'])
                ) {
                    $record['price'] = Number::getTaxlessPrice(
                        $record['price'], 
                        $record['tax_rate'], 
                        2
                    );
                }   
                
                /*/>
                // set manufacturer(required)
                if (empty($rawRecord['manufacturer'])) {
                    $this->progress['errors'][] = __(__FILE__, "Product with missing manufacturer (%s: %s, title: %s)", 
                        $this->pairField, $rawRecord[$this->pairField], $record['name']);
                    $this->progress['failedProductsCount']++;
                    $productNextId--;
                    $productNextCode--;
                    continue;
                }
                //*/
                /*/>
                // skip products of Vydavatelstvo Matice Slovenskej
                if ($this->isManufacturerVydavatelstvoMaticeSlovenskej($rawRecord['manufacturer'])) {
                    $this->progress['skippedProductsCount']++;
                    $productNextId--;
                    $productNextCode--;
                    continue;
                }
                //*/
                $importedManufacturerId = $manufacturerId = null;
                if (!empty($rawRecord['manufacturer'])) {
                    $importedManufacturerId = $this->getImportedManufacturerId($rawRecord['manufacturer']);
                    // skip products of manufacturers which are not imported except cases when
                    // EshopProductImport::$addNewManufacturers is set to TRUE
                    if (
                        empty($this->addNewManufacturers)
                        && empty($importedManufacturerId)
                    ) {
                        $this->progress['warnings'][] = __v(__FILE__, "Product of unexisting manufacturer '%s' (%s: %s, title: %s)", 
                            $rawRecord['manufacturer'], $this->pairField, $rawRecord[$this->pairField], $record['name']);
                        $this->progress['skippedProductsCount']++;
                        $productNextId--;
                        $productNextCode--;
                        continue;
                    }
                    if (!empty($importedManufacturerId)) {
                        $manufacturerId = $importedManufacturerId;
                    }
                    elseif (
                        !($manufacturerId = $this->Manufacturer->ensure(
                            $rawRecord['manufacturer'],
                            $newManufacturers
                        ))
                    ) {
                        $this->progress['errors'][] = __v(__FILE__, "Product with invalid manufacturer name '%s' (%s: %s, title: %s)", 
                            $rawRecord['manufacturer'], $this->pairField, $rawRecord[$this->pairField], $record['name']);
                        $this->progress['failedProductsCount']++;
                        $productNextId--;
                        $productNextCode--;
                        continue;
                    }
                    $record['run_eshop_manufacturers_id'] = $manufacturerId;
                }
                
                // set slug
                $productSlug = $this->Product->getUniqueSlug(
                    $record['name'], 
                    array('avoidValues' => $newProductSlugs)
                );
                $newProductSlugs[] = $productSlug;
                $record['slug'] = $productSlug;
                
                // set category id(s)
                $hasCategory = false;
                // - first check for category names paths
                if (!empty($rawRecord['category_name_paths'])) {  
                    foreach ($rawRecord['category_name_paths'] as $categoryNamePath) {
                        $categoryId = $this->Category->ensure($categoryNamePath, $this->categoriesRoot);
                        $newCategoryProducts[] = array(
                            'run_eshop_product_categories_id' => $categoryId,
                            'run_eshop_products_id' => $record['id']
                        );
                    }
                    $hasCategory = true;
                }
                // - if no name paths defined then check for category codes
                elseif (!empty($rawRecord['categories'])) {
                    foreach ($rawRecord['categories'] as $supplierCategoryCode) {
                        if (($categoryId = $this->getCategoryId($supplierCategoryCode))) {
                            $newCategoryProducts[] = array(
                                'run_eshop_product_categories_id' => $categoryId,
                                'run_eshop_products_id' => $record['id']
                            );
                            $hasCategory = true;
                        }
                    }
                }
                else {
                    $newCategoryProducts[] = array(
                        'run_eshop_product_categories_id' => $unclassifiedCategoryId,
                        'run_eshop_products_id' => $record['id']
                    );
                    $hasCategory = true;
                }
//                else {
//                    $this->progress['errors'][] = __v(__FILE__, "Product with no category specified (%s: %s, title: %s)", 
//                        $this->pairField, $rawRecord[$this->pairField], $record['name']);
//                    $this->progress['failedProductsCount']++;
//                    $productNextId--;
//                    $productNextCode--;
//                    continue;
//                }
                // if EshopProductImport::$ignoreProductsWithoutCategory is TRUE 
                // then skip products which are not placed in any category
                if (
                    !$hasCategory
                    && $this->ignoreProductsWithoutCategory
                ) {
                    $this->progress['warnings'][] = __v(
                        __FILE__, 
                        "Product of unpaired category %s (%s: %s, title: %s)", 
                        trim(json_encode($rawRecord['categories']), '[]'), 
                        $this->pairField, 
                        $rawRecord[$this->pairField], 
                        $record['name']
                    );
                    $this->progress['skippedProductsCount']++;
                    $productNextId--;
                    $productNextCode--;
                    continue;
                }
                
                // discount price (can be specified in $this->selectedItems)
                $record['discount_price'] = null;
                if (!empty($rawRecord['discount_price'])) {
                    $record['discount_price'] = $rawRecord['discount_price'];
                    if (!$pricesAreTaxed) {
                        $record['discount_price'] = Number::getTaxlessPrice(
                            $record['discount_price'], 
                            $record['tax_rate'], 
                            2
                        );
                    }   
                }
                
                // discount rate and discount_to
                $marginRate = $this->defaultMarginRate; 
                if (!empty($rawRecord['margin_rate'])) {
                    $marginRate = $rawRecord['margin_rate'];
                }
                $record['discount_rate'] = $this->defaultDiscountRate;
                if (!empty($rawRecord['discount_rate'])) {
                    $record['discount_rate'] = $rawRecord['discount_rate'];
                }
                // - set discount rate according to imported manufacturers import discount rate
                $manufacturerDiscountRate = $this->getManufacturerDiscountRate($manufacturerId);
                if (
                    // if among loaded manufacturer discounts
                    $manufacturerDiscountRate !== false
                    // and not undefined
                    && $manufacturerDiscountRate !== null
                ) {
                    if (!empty($manufacturerDiscountRate)) {
                        $record['discount_rate'] = $manufacturerDiscountRate;
                    }
                    // import discount rate explicitly set to '0'
                    else {
                        $record['discount_rate'] = null;
                    }
                }
                if (
                    $record['discount_rate']
                    && $marginRate > 0
                    && Eshop::getMarkupRate($record['discount_rate'], $marginRate) < $minMarkupRate
                ) {
                    $record['discount_rate'] = Eshop::getDiscountRate($marginRate, $minMarkupRate);
                    if ($record['discount_rate'] < 0) {
                        $record['discount_rate'] = null;
                    }
                }
                $record['discount_to'] = $this->discountTo;
                
                // manufacturer range
                $record['run_eshop_manufacturer_ranges_id'] = null;
                if (!empty($rawRecord['range'])) {
                    if (
                        !($rangeId = $this->ManufacturerRange->ensure(
                            $rawRecord['range'],
                            $newManufacturerRanges
                        ))
                    ){
                        $this->progress['warnings'][] = __v(__FILE__, "Product with invalid manufacturer range name '%s' (%s: %s, title: %s)", 
                            $rawRecord['range'], $this->pairField, $rawRecord[$this->pairField], $record['name']);
                    }
                    else {
                        $record['run_eshop_manufacturer_ranges_id'] = $rangeId;
                    }
                }
                // authors
                if (!empty($rawRecord['authors'])) {
                    foreach ($rawRecord['authors'] as $author) {
                        if (
                            !($authorId = $this->Author->ensure(
                                $author,
                                $newAuthors
                            ))
                        ) {                            
                            $this->progress['warnings'][] = __v(__FILE__, "Product with invalid author name '%s' (%s: %s, title: %s)", 
                                $author, $this->pairField, $rawRecord[$this->pairField], $record['name']);
                        }
                        else {
                            $newProductAuthors[] = array(
                                'run_eshop_products_id' => $record['id'],
                                'run_eshop_authors_id' => $authorId
                            );
                        }
                    }
                }
                // product images (gallery)
                if (!empty($rawRecord['gallery_image_import_sources'])) {
                    $results = $this->Image->addFromSources($record['id'], $rawRecord['gallery_image_import_sources']);
                    foreach ($results as $source => $result) {
                        if ($result !== true) {                            
                            $this->progress['warnings'][] = __v(
                                __FILE__, 
                                "Product gallery image creation has failed with following validation errors (%s: %s, title: %s, source '%s'): %s", 
                                $this->pairField, 
                                $rawRecord[$this->pairField], 
                                $record['name'],
                                $source,
                                json_encode($result)
                            );
                        }
                    }
                }
                
                // availability fields
                $record['availability'] = 'enum_available';
                $record['available_from'] = null;
                if (!empty($rawRecord['availability'])) {
                    $record['availability'] = $rawRecord['availability'];
                }
                if (!empty($rawRecord['available_from'])) {
                    $record['available_from'] = $rawRecord['available_from'];
                }
                    
                // stock
                $record['stock'] = 0;
                if (
                    $this->supplier === 'internal'
                    && !empty($rawRecord['stock'])
                ) {
                    $record['stock'] = $rawRecord['stock'];
                }
                // dimensions & weight
                $record['width'] = null;
                if (!empty($rawRecord['width'])) {
                    $record['width'] = Eshop::normalizeDimension($rawRecord['width']);
                }
                $record['height'] = null;
                if (!empty($rawRecord['height'])) {
                    $record['height'] = Eshop::normalizeDimension($rawRecord['height']);
                }
                $record['length'] = null;
                if (!empty($rawRecord['length'])) {
                    $record['length'] = Eshop::normalizeDimension($rawRecord['length']);
                }
                $record['dimensions'] = Eshop::getDimensionsString($record);
                $record['weight'] = null;
                if (!empty($rawRecord['weight'])) {
                    $record['weight'] = Eshop::normalizeWeight($rawRecord['weight']);
                }
                
                // all products which are recreated after clean up but have been already
                // exported to MRP mark as exported after recreation too
                // Use datetime '2000-01-01 00:00:00' for reimported items (to find them easy in db)
                // Set them the same code as they had before (to keep relation with the item code in MRP)
                $record['exported'] = null;
                if (
                    $this->pairField === 'ean' // should be always in addition imports
                    && isset($cleanedUpExportedProducts[$rawRecord[$this->pairField]])
                ) {
                    $record['exported'] = '2000-01-01 00:00:00';
                    // use old code instead of new one
                    $record['code'] = $cleanedUpExportedProducts[$rawRecord[$this->pairField]];
                    $productNextCode--;
                }
                // explicit fields
                $record['created'] = null;
                $record['modified'] = null;
                
                // copy all other fields
                $this->copyMissingFields($rawRecord);
                
                // store actual record in batch
                $newProducts[] = $this->Product->normalizeEmptyValues($record);
                
                // supplier data
                if (
                    $this->supplier !== 'internal'
                    || !empty($rawRecord['image_import_source'])
                ) {
                    $supplierRecord = array();
                    $supplierRecord['run_eshop_products_id'] = $record['id'];
                    $supplierRecord['supplier_pid'] = $this->supplier;
                    $supplierRecord['image_import_source'] = null;
                    if (!empty($rawRecord['image_import_source'])) {
                        $supplierRecord['image_import_source'] = $rawRecord['image_import_source'];
                        $launchImagesImport = true;
                    }
                    if ($this->supplier !== 'internal') {
                        $supplierRecord['price'] = $record['price'];
                        $supplierRecord['tax_rate'] = $record['tax_rate'];
                        $supplierRecord['margin_rate'] = $marginRate;
                        $supplierRecord['discount_price'] = $record['discount_price'];
                        $supplierRecord['discount_rate'] = $record['discount_rate'];
                        $supplierRecord['stock'] = null;
                        if (isset($rawRecord['stock'])) {
                            $supplierRecord['stock'] = $rawRecord['stock'];
                        }
                        $supplierRecord['availability'] = $record['availability'];
                        if (array_key_exists('long_delivery_time', $record)) {
                            $supplierRecord['long_delivery_time'] = $record['long_delivery_time'];
                        }
                        if (array_key_exists('reprint', $record)) {
                            $supplierRecord['reprint'] = $record['reprint'];
                        }
                        $supplierRecord['available_from'] = $record['available_from'];
                        $supplierRecord['supplier_product_id'] = null;
                        if (!empty($rawRecord['supplier_product_id'])) {
                            $supplierRecord['supplier_product_id'] = $rawRecord['supplier_product_id'];
                        }
                        $supplierRecord['created'] = null;
                        $supplierRecord['modified'] = null;
                        // copy all other fields
                        $this->copyMissingFields($rawRecord, 'EshopSupplierProduct');
                    }
                    // store actual record in batch
                    $newSupplierProducts[] = $this->SupplierProduct->normalizeEmptyValues($supplierRecord);

                    // update progress info
                    $this->progress['createdSupplierProductsCount']++;
                }
                
                $batchCount++;

                // add pair field value to existing values to avoid creation of duplicit pair values
//rblb//                $this->pairFieldValues[$substanceType][$rawRecord[$this->pairField]] = true;
                $this->pairFieldValues[$rawRecord[$this->pairField]] = true;
                
                // update progress info
                $this->progress['importedProductsCount']++;
                $this->progress['createdProductsCount']++;
                
                
                // save the batch
                if ($batchCount >= $options['batchSize']) {
                    $batch = array(
                        'create' => array(
                            'EshopProduct' => &$newProducts,
                            'EshopSupplierProduct' => &$newSupplierProducts,
                            'EshopManufacturer' => &$newManufacturers,
                            'EshopManufacturerRange' => &$newManufacturerRanges,
                            'EshopAuthor' => &$newAuthors,
                            'EshopProductAuthor' => &$newProductAuthors,
                            'EshopProductCategoryProduct' => &$newCategoryProducts,
                        ),
                    );
                    if ($options['simulate']) {                
                        App::log('EshopProductImport_importAddition', 'batch', array(
                            'var' => $batch,
                        ));
                    }
                    else {  
                        $this->saveBatch($batch);
                    }
                    // reset batch variables
                    $batchCount = 0;
                    $newProductSlugs = array();
                    $newManufacturers = array();
                    $newManufacturerRanges = array();
                    $newAuthors = array();
                    $newProductAuthors = array();
                    $newCategoryProducts = array();
                    $newProducts = array();
                    $newSupplierProducts = array();
                }
            }

            // save last batch
            $batch = array(
                'create' => array(
                    'EshopProduct' => &$newProducts,
                    'EshopSupplierProduct' => &$newSupplierProducts,
                    'EshopManufacturer' => &$newManufacturers,
                    'EshopManufacturerRange' => &$newManufacturerRanges,
                    'EshopAuthor' => &$newAuthors,
                    'EshopProductAuthor' => &$newProductAuthors,
                    'EshopProductCategoryProduct' => &$newCategoryProducts,
                ),
            );
            if ($options['simulate']) {                
                App::log('EshopProductImport_importAddition', 'batch', array(
                    'var' => $batch,
                ));
            }
            else {  
                $this->saveBatch($batch);
            }
        }
        // IKAR: if only new items from last timestamp are loaded then it
        // occures quite often that catalogue file generation error occures
        catch (Exception_IkarXmlReader_FileTransferDataGenerationError $e) {
            $this->progress['warnings'][] = __(__FILE__, 'There are no new items in IKAR catalogue');

        }
        catch (Exception_IkarXmlReader_NoNewRecords $e) {
            $this->progress['warnings'][] = __(__FILE__, 'There are no new items in IKAR catalogue');
        }
        catch (Throwable $e) {
            App::log('EshopProductImport_importAddition', $e->getMessage(), array(
                'var' => $e, 
                'email' => true,
            ));
            $this->setError($e->getMessage());
            $this->progress = false;
        }
        
        $this->unreserveTables('EshopProductImport_importAddition');
        App::unreserveProcessing('EshopProductImport_importAddition');
        
        if (!empty($this->progress['interrupted'])) {
            App::log(
                'EshopProductImport_importAddition', 
                __a(__FILE__, 'Import %s has been interrupted', $this->importPid), 
                array(
                    'var' => $this->progress, 
                    'email' => true,
                )
            );
        }
        // launch images import
        elseif (!ON_LOCALHOST && $launchImagesImport) {
            $imagesImportRequestTimeout = 1000; // ms = 1s
            if (App::getFreeMicrotime() > $imagesImportRequestTimeout + 1000) {            
                try {
                    App::request(
                        App::getUrl(array(
                            'locator' => '/mvc/Eshop/EshopImports/autoimportProductFieldsFromSource/image',
                            'get' => array(
                                'launchedByImportPidJustForInfo' => $this->importPid,
                            ),
                            'absolute' => true,
                        )), array(
                            'timeout' => $imagesImportRequestTimeout / 1000,
                            'returnResponse' => false,
                        )
                    );
                } 
                catch (Throwable $e) {
                    // do nothing
                }
            }
        }        
        
        return $this->progress;
    } 
       
    /**
     * Imports new products from supplier file
     * 
     * ATTENTION: this method turns off sql logging!
     * 
     * @param string $catalogue Name of file placed under userfiles/files/import
     * @param string $importPid One of 'internalStockUpdate', 'internalProductUpdate',
     *          'mrpStockUpdate', 'frankanaStockUpdate', 'frankanaUpdate'
     * @param array $options Following are available:
     *      - 'catalogueFile' (string) App root relative path to catalogue file. 
     *          If not provided or empty then it is downloaded. Defaults to NULL.
     *      - 'priceFile' (string) App root relative path to price file. 
     *          If not provided or empty then it is downloaded. Defaults to NULL.
     *      - 'stockFile' (string) App root relative path to stock file. 
     *          If not provided or empty then it is downloaded. Defaults to NULL.
     *      - 'selectionFile' (string) Name of CSV file placed under userfiles/files/import. 
     *          It specifies selection of items from $catalogue which should be imported. 
     *          In the first column there are EAN codes. In next columns there are 
     *          explicit values for data. Defaults to NULL, means it is not used and all 
     *          items from catalogue are imported.
     *      - 'useFullCatalogue' (bool) Applies for 'pemicAvailabilityUpdate',
     *          'ikarDescriptionUpdate' and for  'mrpStockUpdate' imports
     *          For PEMIC and IKAR it applies if 'catalogueFile' is downloaded. 
     *          If TRUE then a catalogue_full file (PEMIC) or full annotation file 
     *          without timestamp (IKAR) is downloaded. For INTERNALSTOCK it is 
     *          used as 'extendedData' switch. Defaults to FALSE. 
     *      - 'batchSize' (int) Number of records after which the batch is saved.
     *          Defaults to 10000.
     *      - 'microtimeReserve' (int) Time reserve in miliseconds to stop the processing 
     *          if free microtime is under this value. Defaults to 10000.
     *      - 'updateType' (string) If 'availability' then only availability fields
     *          are updated. If 'price' then only price fields are updated. If 'stock'
     *          then only stock fields are updated.
     *      - 'importAmount' (integer) Amount of products to be updated. If 0 then 
     *          all in catalogue are updated. Defaults to 0.
     *      - 'recordOffset' (integer) Amount of records to be skipped in catalogue. 
     *          Only records over this offset are considered for update. If 0 then all 
     *          records in catalogue are considered. Defaults to 0.
     * 
     * @return bool|array FALSE on invalid supplier, XML open failure or invalid XML file
     *      or any kind of other processing failure. Error message is set in '_processing' errors.
     *      On succes the array of progress is returned.
     */
    public function importUpdate($importPid, $options = array()) {
        $defaults = array(
            'catalogueFile' => null,
            'priceFile' => null,
            'stockFile' => null,
            'selectionFile' => null,
            'useFullCatalogue' => false,
            'updateType' => null,
            'batchSize' => 5000,
            'microtimeReserve' => 10000,
            'recordOffset' => 0,
            'processAmount' => 0, // for debuging, if 0 then all records are processed
            'importAmount' => 0,
            'simulate' => false, //for debuging, if TRUE then all imported records are written into log file EshopProductImport_importUpdate instead to BD. Only categories created from category_paths are created even if simulate is TRUE.
        );
        $options = array_merge($defaults, $options);
        if ($options['processAmount']) {
            $options['processAmount']++;
        }
        
        // !!! keep this is synchro with the app/config/database.php > 'tableReservationTimeLimit' => ...
        set_time_limit(600);
        ini_set('memory_limit', '512M');
        
        $this->importPid = $importPid;
        
        // Ensure that there are not launched 2 paralel imports with the same $importPid 
        // (for tables reservation see below)
        try {
            $processingName = 'EshopProductImport_importUpdate_' . $this->importPid;
            App::reserveProcessing($processingName);
        } 
        catch (Throwable $e) {
            $this->progress['warnings'][] = __v(__FILE__, "There is already a runnig import '%s'. Try later.", $this->importPid);
            return $this->progress;
        }
        
        // turn off sql logs to not get error ERR_RESPONSE_HEADERS_TOO_BIG
        App::setSqlLogging(false);
        
        try {
            // validate provided data
            if (!$this->validate(
                array(
                    'importPid' => $this->importPid,
                    'catalogueFile' => $options['catalogueFile'],
                    'priceFile' => $options['priceFile'],
                    'stockFile' => $options['stockFile'],
                    'selectionFile' => $options['selectionFile'],
                ),
                array(
                    'alternative' => $this->importPid,
                )
            )) {
                return false;
            }
            if ($this->importPid === 'internalStockUpdate') {
                $this->supplier = 'internal';
                $this->categoriesRoot = 'categories';
//                    $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'code'; 
                $this->loadPairFieldValues();
                $this->loadLib('InternalStockCsvReader');
                $this->Reader = new InternalStockCsvReader($options['catalogueFile'], array(
                    'recordOffset' => $options['recordOffset'],
                ));
                $this->importedFields = array(
                    'stock' => true, 
                    'stock_location_code' => true, 
                    'shop_location_code' => true, 
                    'supplier_info' => true, 
                );
                $this->ignoreNewValueIf = false; // do not consider this option
            }
            // newer type of internal update
            elseif ($this->importPid === 'internalProductUpdate') {
                $this->supplier = 'internal';
                $this->categoriesRoot = 'categories';
//                    $substanceTypes = array('physical');
                $this->loadLib('EshopProductCsvReader');
                $this->Reader = new EshopProductCsvReader(array(
                    'catalogueFile' => $options['catalogueFile'],
                    'recordOffset' => $options['recordOffset'],
                ));
                $this->loadImportedManufacturers();
                $this->addNewManufacturers = true;
                $this->ignoreProductsWithoutCategory = false;
                // resolve pair field and imported fields according to actual fields in record
                $record = $this->Reader->readRecord();
                $this->Reader->reset();
                if (!$record) {
                    return $this->progress;
                }
                // - resolve pair field
                reset($record);
                $this->pairField = key($record);
                // - check that pair field values are not empty and unique
                $pairFieldValues = array();
                while (($record = $this->Reader->readRecord())) {
                    $pairFieldValue = $record[$this->pairField];
                    $this->Reader->getPropertyRowOffset();
                    $lineNumber = $this->Reader->getPropertyRowOffset();
                    if (Validate::emptyValue($pairFieldValue)) {
                        $this->progress['errors'][] = __v(
                            __FILE__, 
                            'Prázdna hodnota v párovacom stĺpci "%s" na riadku %s. Hodnoty v párovacom stĺpci musia byť neprázdne a unikátne.',
                            $this->pairField,
                            $lineNumber
                        );
                        return $this->progress;
                    }
                    if (isset($pairFieldValues[$pairFieldValue])) {
                        $this->progress['errors'][] = __v(
                            __FILE__, 
                            'Duplicitná hodnota "%s" v párovacom stĺpci "%s" na riadkoch %s a %s. Hodnoty v párovacom stĺpci musia byť neprázdne a unikátne.',
                            $pairFieldValue,
                            $this->pairField,
                            $pairFieldValues[$pairFieldValue],
                            $lineNumber
                        );
                    }
                    $pairFieldValues[$pairFieldValue] = $lineNumber;
                }
                unset($pairFieldValues);
                $this->Reader->reset();
                // - load existing pair values (ids, eans, codes, supplier_product_ids, ...)
                $this->loadPairFieldValues();
                // - imported fields
                $record = $this->Reader->readRecord();
                $this->Reader->reset();
                $this->importedFields = array();
                foreach(array_keys($record) as $field) {
                    if ($field === $this->pairField) {
                        continue;
                    }
                    if ($field === 'price_taxed') {
                        $field = 'price';
                    }
                    $this->importedFields[$field] = true;
                }
                $this->ignoreNewValueIf = null;
            }  
            elseif ($this->importPid === 'mrpStockUpdate') {
                if ($options['microtimeReserve'] < 60000) {
                    $options['microtimeReserve'] = 60000;
                }
                $this->supplier = 'internal';
                $this->categoriesRoot = 'categories';
//                    $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'mrp_code'; 
                $this->loadPairFieldValues();
                $this->loadLib('MrpStockReader');
                $serverIpAddress = $this->getSetting('mrp.serverIpAddress');
                $serverPort = $this->getSetting('mrp.serverPort');
                if (empty($serverIpAddress) || empty($serverPort)) {
                    throw new Exception(__e(__FILE__, 'Missing MRP server IP address or port number'));
                }
                $this->Reader = new MrpStockReader($serverIpAddress, $serverPort, array(
                    'privateKey' => $this->getSetting('mrp.privateKey'),
                    'fromCode' => $options['recordOffset'],
                ));
                $this->importedFields = array(
                    'stock' => true,
                    'availability' => true,
                    'stock_reserved' => true, 
                    'stock_location_code' => true, 
                    'shop_location_code' => true, 
                    'supplier_info' => true, 
                );
                $this->ignoreNewValueIf = false; // do not consider this option
            }  
            elseif ($this->importPid === 'frankanaStockUpdate') {
                $this->supplier = 'frankana';
                $this->categoriesRoot = 'categories';
    //                $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'code'; 
                $this->loadPairFieldValues(array(
                    'fields' => array(
                        'EshopProduct.id', 
                        'EshopProduct.tax_rate'
                    ),
                ));
                $this->loadSupplierProductIds();
                $this->loadLib('FrankanaStockCsvReader');
                $this->Reader = new FrankanaStockCsvReader(array(
                    'file' => $options['stockFile'],
                    'username' => '232331', // karavandoplnky.sk access
                    'password' => 'BnT19kLP', // karavandoplnky.sk access
                    'recordOffset' => $options['recordOffset'],
                ));
                $this->importedFields = array(
                );
                // tax_rate is inherited from existing products 
                // (see loadPairFieldValues() here above)
                $this->importedSupplierFields = array(
                    'price' => true,
                    'tax_rate' => true,
                    //'stock' => true, // stock is updated from MRP
                    'availability' => true,
                );
                $this->ignoreNewValueIf = array(
                    'EshopSupplierProduct.price' => array('', 0, null),
                    'EshopSupplierProduct.tax_rate' => array('', 0, null),
                    'EshopSupplierProduct.stock' => array(null),
                    'EshopSupplierProduct.availability' => array('', null),
                );
                $this->updateOldValueIf = array(
                );
            }
            elseif ($this->importPid === 'frankanaUpdate') {
                $this->supplier = 'frankana';
                $this->categoriesRoot = 'categories';
    //                $substanceTypes = array('physical');
                // load existing pair values (eans, codes, supplier_product_ids, ...)
                $this->pairField = 'code'; 
                $this->loadPairFieldValues();
                $this->loadSupplierProductIds();
                $this->loadLib('FrankanaProductApiReader');
                $this->Reader = new FrankanaProductApiReader();
                $this->importedFields = array(
                );
                $this->importedSupplierFields = array(
                    'image_import_source',
                );
                $this->ignoreNewValueIf = array(
                );
                $this->updateOldValueIf = array(
                );
            }
            else {
                $this->setError('importPid', __v(__FILE__, 'Unknown import name %s', $this->importPid));
                return false;
            }            
            
            // read selected items if selection file provided
            if (!empty($options['selectionFile'])) {
                $this->loadSelectionRecords($options['selectionFile']);
            }
//// Update inmport can be launched many times during the day so avoid product table 
//// reservation as it can cause complication on other operations done on products table.
//// At the same time product table reservation seems to be not necessary as only
//// changed product field are updated          
//            // reserve tables
//            $this->reserveTables('EshopProductImport_importUpdate', array(
//                'EshopProduct',
//            ));
            // initialize progress
            $this->progress['processedProductsCount'] = $options['recordOffset']; //0;
            // initialize batch variables
            $batchCount = 0;
            $updateProducts = array();
            $updateSupplierProducts = array();
            $newSupplierProducts = array();
            $newManufacturers = array();
            $newManufacturerRanges = array();
            $newAuthors = array();
            $newProductAuthors = array();
            $newCategoryProducts = array();
            $deleteAuthorsForProductIds = array();
            $deleteCategoriesForProductIds = array();
            $pricesAreTaxed = App::getSetting('Eshop', 'pricesAreTaxed');
            // do a shortcut variable
            $record = &$this->record;
            $supplierRecord = &$this->supplierRecord;
//            $_SESSION['timer'] = array( //debug
//                'read' => 0,
//                'save' => 0,
//                'dbDrop' => 0,
//                'dbInsert' => 0,
//                'dbUpdate' => 0,
//                'setInactive' => 0,
//            );
//            Utility::startTimer('read'); //debug
            while (
                ($rawRecord = $this->Reader->readRecord())
                // if selected items are provided then quit as soon as all of them are imported
                && ($this->selectedItems === null || $this->selectedItemsCount > $this->progress['importedProductsCount'])
            ) {      
                
                // check remaining time - finish the processing before php 
                // would be interrupted by execution time constrain
                if (App::getFreeMicrotime() < $options['microtimeReserve']) {
                    $this->progress['interrupted'] = true;
                    break;
                }
                // finish the processing if processAmount or importAmount is reached
                if (
                    $options['processAmount'] && --$options['processAmount'] == 0 //debug
                    || $options['importAmount'] && $options['importAmount'] == $this->progress['importedProductsCount'] //debug
                ) {
                    break;
                }
                
                $this->progress['processedProductsCount']++;
                                
                $record = array();
                $supplierRecord = array();
                
                /*/>debug
                if ($rawRecord['mrp_code'] == '100052') {
                    $x = 1;
                }
                //*/
                
                // get pair field - ean or code (required)
                if (empty($rawRecord[$this->pairField])) {
                    $this->progress['errors'][] = __v(__FILE__, "Product with missing %s (record no. %s)", 
                        $this->pairField, $this->progress['processedProductsCount']);
                    $this->progress['failedProductsCount']++;
                    continue;
                }
//                $substanceType = $this->Product->getSubstanceType($rawRecord);
                // skip product if...
                if (
                    // skip item which are not found in selected items (but only if selected items are provided)
                    $this->selectedItems !== null
                    && !isset($this->selectedItems[$rawRecord[$this->pairField]])
                    ||
                    // skip unexisting products
//rblb//                    !isset($this->pairFieldValues[$substanceType][$rawRecord[$this->pairField]])
                    !isset($this->pairFieldValues[$rawRecord[$this->pairField]])
                ) {
                    $this->progress['skippedProductsCount']++;
                    continue;
                }
                // get record id
                if ($this->pairField === 'id') {
                    $recordId = $rawRecord['id'];
                }
//rblb//                elseif (is_array($this->pairFieldValues[$substanceType][$rawRecord[$this->pairField]])) {
                elseif (is_array($this->pairFieldValues[$rawRecord[$this->pairField]])) {
//rblb//                    $recordId = $this->pairFieldValues[$substanceType][$rawRecord[$this->pairField]]['id'];
                    $recordId = $this->pairFieldValues[$rawRecord[$this->pairField]]['id'];
                }
                else {
//rblb//                    $recordId = $this->pairFieldValues[$substanceType][$rawRecord[$this->pairField]];
                    $recordId = $this->pairFieldValues[$rawRecord[$this->pairField]];
                }
                                
                // if selected item are provided then apply selected item data on raw record data
                if ($this->selectedItems !== null) {
                    $rawRecord = array_merge($rawRecord, $this->selectedItems[$rawRecord[$this->pairField]]);
                }
                                
                // tax_rate
                if (
                    !empty($this->importedFields['tax_rate'])
                    || !empty($this->importedSupplierFields['tax_rate'])
                ) {
                    // null = original value is preserved
                    $taxRate = null;
                    if (!empty($rawRecord['tax_rate'])) {
                        $taxRate = $rawRecord['tax_rate'];
                    }
                    elseif (
                        is_array($this->pairFieldValues[$rawRecord[$this->pairField]])
                        && isset($this->pairFieldValues[$rawRecord[$this->pairField]]['tax_rate'])
                    ) {
                        $taxRate = $this->pairFieldValues[$rawRecord[$this->pairField]]['tax_rate'];
                    }
                    if (!empty($this->importedFields['tax_rate'])) {
                        $record['tax_rate'] = $taxRate;
                    }
                    if (!empty($this->importedSupplierFields['tax_rate'])) {
                        $supplierRecord['tax_rate'] = $taxRate;
                    }
                }
                
                // price
                if (
                    !empty($this->importedFields['price'])
                    || !empty($this->importedSupplierFields['price'])
                ) {
                    // null = original value is preserved
                    $price = null;
                    if (
                        !empty($rawRecord['price_taxed'])
                        && !empty($taxRate)
                    ) {
                        $price = $rawRecord['price_taxed'];
                        if (!$pricesAreTaxed) {
                            $price = Number::getTaxlessPrice(
                                $price, 
                                $taxRate, 
                                2
                            );
                        }
                    }
                    if (!empty($this->importedFields['price'])) {
                        $record['price'] = $price;
                    }
                    if (!empty($this->importedSupplierFields['price'])) {
                        $supplierRecord['price'] = $price;
                    }
                }
                
                // manufacturer (for the moment used only by internalProductUpdate)
                if (!empty($this->importedFields['manufacturer'])) {
                    $record['run_eshop_manufacturers_id'] = null;
                    if (!empty($rawRecord['manufacturer'])) {
                        $importedManufacturerId = $this->getImportedManufacturerId($rawRecord['manufacturer']);
                        // skip products of manufacturers which are not imported except cases when
                        // EshopProductImport::$addNewManufacturers is set to TRUE
                        if (
                            empty($this->addNewManufacturers)
                            && empty($importedManufacturerId)
                        ) {
                            $this->progress['warnings'][] = __v(__FILE__, "Product of unexisting manufacturer '%s' (%s: %s, title: %s)", 
                                $rawRecord['manufacturer'], $this->pairField, $rawRecord[$this->pairField], $record['name']);
                            $this->progress['skippedProductsCount']++;
                            continue;
                        }
                        if (!empty($importedManufacturerId)) {
                            $manufacturerId = $importedManufacturerId;
                        }
                        elseif (
                            !($manufacturerId = $this->Manufacturer->ensure(
                                $rawRecord['manufacturer'],
                                $newManufacturers
                            ))
                        ) {
                            $this->progress['errors'][] = __v(__FILE__, "Product with invalid manufacturer name '%s' (%s: %s, title: %s)", 
                                $rawRecord['manufacturer'], $this->pairField, $rawRecord[$this->pairField], $record['name']);
                            $this->progress['failedProductsCount']++;
                            continue;
                        }
                        $record['run_eshop_manufacturers_id'] = $manufacturerId;
                    }
                    // internalProductUpdate uses empty values for erase
                    elseif (
                        $this->importPid === 'internalProductUpdate'
                        && $rawRecord['manufacturer'] === ''
                    ) {
                        $record['run_eshop_manufacturers_id'] = '';
                    }
                }
                
                // manufacturer range (for the moment used only by internalProductUpdate)
                if (!empty($this->importedFields['range'])) {
                    $record['run_eshop_manufacturer_ranges_id'] = null;
                    if (!empty($rawRecord['range'])) {
                        if (
                            !($rangeId = $this->ManufacturerRange->ensure(
                                $rawRecord['range'],
                                $newManufacturerRanges
                            ))
                        ){
                            $this->progress['warnings'][] = __v(__FILE__, "Product with invalid manufacturer range name '%s' (%s: %s, title: %s)", 
                                $rawRecord['range'], $this->pairField, $rawRecord[$this->pairField], $record['name']);
                        }
                        else {
                            $record['run_eshop_manufacturer_ranges_id'] = $rangeId;
                        }
                    }
                    // internalProductUpdate uses empty values for erase
                    elseif (
                        $this->importPid === 'internalProductUpdate'
                        && $rawRecord['range'] === ''
                    ) {
                        $record['run_eshop_manufacturer_ranges_id'] = '';
                    }
                }
                
                // categories (for the moment used only by internalProductUpdate)
                if (!empty($this->importedFields['categories'])) {
                    // - if no name paths defined then check for category codes
                    $hasCategory = false;
                    if (!empty($rawRecord['categories'])) {
                        foreach ($rawRecord['categories'] as $supplierCategoryCode) {
                            if (($categoryId = $this->getCategoryId($supplierCategoryCode))) {
                                $deleteCategoriesForProductIds[$recordId] = $recordId;
                                $newCategoryProducts[] = array(
                                    'run_eshop_product_categories_id' => $categoryId,
                                    'run_eshop_products_id' => $recordId
                                );
                                $hasCategory = true;
                            }
                        }
                    }
                    // if EshopProductImport::$ignoreProductsWithoutCategory is TRUE 
                    // then skip products which are not placed in any category
                    if (
                        !$hasCategory
                        && $this->ignoreProductsWithoutCategory
                    ) {
                        $this->progress['warnings'][] = __v(
                            __FILE__, 
                            "Product of unpaired category %s (%s: %s, title: %s)", 
                            trim(json_encode($rawRecord['categories']), '[]'), 
                            $this->pairField, 
                            $rawRecord[$this->pairField], 
                            $record['name']
                        );
                        $this->progress['skippedProductsCount']++;
                        continue;
                    }                        
                    // internalProductUpdate uses empty values for erase
                    elseif (
                        $this->importPid === 'internalProductUpdate'
                        && $rawRecord['categories'] === ''
                    ) {
                        $deleteCategoriesForProductIds[$recordId] = $recordId;
                    }
                }
                
                // authors (for the moment used only by internalProductUpdate)
                if (!empty($this->importedFields['authors'])) {
                    if (!empty($rawRecord['authors'])) {
                        foreach ($rawRecord['authors'] as $author) {
                            if (
                                !($authorId = $this->Author->ensure(
                                    $author,
                                    $newAuthors
                                ))
                            ) {                            
                                $this->progress['warnings'][] = __v(__FILE__, "Product with invalid author name '%s' (%s: %s, title: %s)", 
                                    $author, $this->pairField, $rawRecord[$this->pairField], $record['name']);
                            }
                            else {
                                $deleteAuthorsForProductIds[$recordId] = $recordId;
                                $newProductAuthors[] = array(
                                    'run_eshop_products_id' => $recordId,
                                    'run_eshop_authors_id' => $authorId
                                );
                            }
                        }
                    }
                    // internalProductUpdate uses empty values for erase
                    elseif (
                        $this->importPid === 'internalProductUpdate'
                        && $rawRecord['authors'] === ''
                    ) {
                        $deleteAuthorsForProductIds[$recordId] = $recordId;
                    }
                }

                // product images (gallery)
                if (!empty($rawRecord['gallery_image_import_sources'])) {
                    $results = $this->Image->addFromSources($recordId, $rawRecord['gallery_image_import_sources']);
                    foreach ($results as $source => $result) {
                        if ($result !== true) {                            
                            $this->progress['warnings'][] = __v(
                                __FILE__, 
                                "Product gallery image creation has failed with following validation errors (%s: %s, title: %s, source '%s'): %s", 
                                $this->pairField, 
                                $rawRecord[$this->pairField], 
                                $record['name'],
                                $source,
                                json_encode($result)
                            );
                        }
                    }
                }
                                
                // another fields
                foreach ($this->importedFields as $field => $v) {
                    // skip...
                    if (
                        // ...already treated fields
                        array_key_exists($field, $record)
                        // ...fields which are not present in raw record
                        || !array_key_exists($field, $rawRecord)
                        // ...virtual fields
                        || in_array($field, array('price_taxed', 'manufacturer', 'range', 'categories', 'authors'))
                    ) {
                        continue;
                    }
                    if (
                        // normalize empty strings to NULL except of internalProductUpdate
                        // which treats NULL and '' differently ("no change" vs "erase") 
                        $this->importPid !== 'internalProductUpdate'
                        && $rawRecord[$field] === ''
                    ) {
                        $record[$field] = null;
                    }
                    elseif (
                        !empty($rawRecord[$field])
                        && (
                            $field === 'width'
                            || $field === 'height'
                            || $field === 'length'
                        )
                    ) {
                        $record[$field] = Eshop::normalizeDimension($rawRecord[$field]);
                    }
                    elseif ($field === 'dimensions') {
                        $record['dimensions'] = Eshop::getDimensionsString($record);
                    }
                    elseif (
                        !empty($rawRecord[$field])
                        && $field === 'weight'
                    ) {
                        $record['weight'] = Eshop::normalizeWeight($rawRecord['weight']);
                    }
                    else {
                        $record[$field] = $rawRecord[$field];
                    }
                }
                
                // another supplier fields
                foreach ($this->importedSupplierFields as $field => $v) {
                    // skip...
                    if (
                        // ...already treated fields
                        array_key_exists($field, $supplierRecord)
                        // ...fields which are not present in raw record
                        || !array_key_exists($field, $rawRecord)
                        // ...virtual fields
                        || in_array($field, array('price_taxed', 'manufacturer', 'range', 'categories', 'authors'))
                    ) {
                        continue;
                    }
                    if (
                        // normalize empty strings to NULL except of internalProductUpdate
                        // which treats NULL and '' differently ("no change" vs "erase")
                        $this->importPid !== 'internalProductUpdate'
                        && $rawRecord[$field] === ''
                    ) {
                        $supplierRecord[$field] = null;
                    }
                    else {
                        $supplierRecord[$field] = $rawRecord[$field];
                    }
                }
                
                if (!empty($this->importedFields)) {
                    // skip empty records or records full of NULLs (preservation values)
                    if (empty($record)) {
                        $this->progress['warnings'][] = __v(__FILE__, "Product without any imported fields. It is skipped (%s: %s).", 
                            $this->pairField, $rawRecord[$this->pairField]);
                        $this->progress['skippedProductsCount']++;
                        unset($this->pairFieldValues[$rawRecord[$this->pairField]]);
                    }
                    elseif ($this->hasNullsOnlyValues($record)) {
                        $this->progress['warnings'][] = __v(
                            __FILE__, "Product without any imported fields - all are preserved. It is skipped (%s: %s).", 
                            $this->pairField, $rawRecord[$this->pairField]
                        );
                        $this->progress['skippedProductsCount']++;
                        unset($this->pairFieldValues[$rawRecord[$this->pairField]]);
                    }
                    else {
                        // explicit fields
                        $record['id'] = $recordId;
                        $record['modified'] = null;

                        // store actual record in batch
                        $updateProducts[] = $record;

                        $this->progress['updatedProductsCount']++;
                    }
                }
                
                if (!empty($this->importedSupplierFields)) {
                    // skip empty records or records full of NULLs (preservation values)
                    if (empty($supplierRecord)) {
                        $this->progress['warnings'][] = __v(__FILE__, "Supplier product without any imported fields. It is skipped (%s: %s).", 
                            $this->pairField, $rawRecord[$this->pairField]);
                        $this->progress['skippedProductsCount']++;
                        unset($this->pairFieldValues[$rawRecord[$this->pairField]]);
                    }
                    elseif ($this->hasNullsOnlyValues($supplierRecord)) {
                        $this->progress['warnings'][] = __v(
                            __FILE__, "Supplier product without any imported fields - all are preserved. It is skipped (%s: %s).", 
                            $this->pairField, $rawRecord[$this->pairField]
                        );
                        $this->progress['skippedProductsCount']++;
                        unset($this->pairFieldValues[$rawRecord[$this->pairField]]);
                    }
                    else {
                        // explicit fields
                        if (is_array($this->pairFieldValues[$rawRecord[$this->pairField]])) {
                            $supplierRecord['run_eshop_products_id'] = $this->pairFieldValues[$rawRecord[$this->pairField]]['id'];
                        }
                        else {
                            $supplierRecord['run_eshop_products_id'] = $this->pairFieldValues[$rawRecord[$this->pairField]];
                        }
                        if (array_key_exists('supplier_product_id', $rawRecord)) {
                            $supplierRecord['supplier_product_id'] = $rawRecord['supplier_product_id'];
                        }
                        if (empty($this->supplierProductIds[$supplierRecord['run_eshop_products_id']])) {
                            $supplierRecord['supplier_pid'] = $this->supplier;
                            $supplierRecord['created'] = null;
                            $supplierRecord['modified'] = null;
                            // store actual record in batch
                            $newSupplierProducts[] = $supplierRecord;
                            $this->progress['createdSupplierProductsCount']++;
                        }
                        elseif ($this->supplierProductIds[$supplierRecord['run_eshop_products_id']] !== true) {
                            $supplierRecord['id'] = $this->supplierProductIds[$supplierRecord['run_eshop_products_id']];
                            $supplierRecord['modified'] = null;
                            // store actual record in batch
                            $updateSupplierProducts[] = $supplierRecord;
                            $this->progress['updatedSupplierProductsCount']++;
                        }
                        // keep track that we are already processed supplier record for given product id
                        $this->supplierProductIds[$supplierRecord['run_eshop_products_id']] = true;
                    }
                }
                
                // remove processed pair field value from existing to not process it twice
//rblb//                unset($this->pairFieldValues[$substanceType][$rawRecord[$this->pairField]]);
                unset($this->pairFieldValues[$rawRecord[$this->pairField]]);
                
                $this->progress['importedProductsCount']++;
                $batchCount++;
                
                // save the batch
                if ($batchCount >= $options['batchSize']) {
//                    $_SESSION['timer']['read'] += Utility::getTimer('read', false); //debug
//                    Utility::startTimer('save'); //debug
                    $batch = array(
                        'delete' => array(),
                        'create' => array(
                            'EshopSupplierProduct' => &$newSupplierProducts,
                            'EshopManufacturer' => &$newManufacturers,
                            'EshopManufacturerRange' => &$newManufacturerRanges,
                            'EshopAuthor' => &$newAuthors,
                            'EshopProductAuthor' => &$newProductAuthors,
                            'EshopProductCategoryProduct' => &$newCategoryProducts,
                        ),
                        'update' => array(
                            'EshopProduct' => &$updateProducts,
                            'EshopSupplierProduct' => &$updateSupplierProducts,
                        ),
                    );
                    if ($deleteAuthorsForProductIds) {
                        $batch['delete']['EshopProductAuthor'] = array(
                            'run_eshop_products_id' => &$deleteAuthorsForProductIds,
                        );
                    }
                    if ($deleteCategoriesForProductIds) {
                        $batch['delete']['EshopProductCategoryProduct'] = array(
                            'run_eshop_products_id' => &$deleteCategoriesForProductIds,
                        );
                    }
                    if ($options['simulate']) {                
                        App::log('EshopProductImport_importUpdate', 'batch', array(
                            'var' => $batch,
                        ));
                    }
                    else {  
                        $this->saveBatch($batch, array(
                            'ignoreNewValueIf' => $this->ignoreNewValueIf,
                            'updateOldValueIf' => $this->updateOldValueIf,
                            //'debugUpdateQuery' => true,
                        ));
                    }
                    // reset batch variables
                    $updateProducts = array();
                    $updateSupplierProducts = array();
                    $newSupplierProducts = array();
                    $newManufacturers = array();
                    $newManufacturerRanges = array();
                    $newAuthors = array();
                    $newProductAuthors = array();
                    $newCategoryProducts = array();
                    $deleteAuthorsForProductIds = array();
                    $deleteCategoriesForProductIds = array();
                    $batchCount = 0;
//                    $_SESSION['timer']['save'] += Utility::getTimer('save', false); //debug
//                    Utility::startTimer('read'); //debug
                }
            }

//            $_SESSION['timer']['read'] += Utility::getTimer('read', false); //debug
//            Utility::startTimer('save'); //debug
            // save last batch
            $batch = array(
                'delete' => array(),
                'create' => array(
                    'EshopSupplierProduct' => &$newSupplierProducts,
                    'EshopManufacturer' => &$newManufacturers,
                    'EshopManufacturerRange' => &$newManufacturerRanges,
                    'EshopAuthor' => &$newAuthors,
                    'EshopProductAuthor' => &$newProductAuthors,
                    'EshopProductCategoryProduct' => &$newCategoryProducts,
                ),
                'update' => array(
                    'EshopProduct' => &$updateProducts,
                    'EshopSupplierProduct' => &$updateSupplierProducts,
                ),
            );
            if ($deleteAuthorsForProductIds) {
                $batch['delete']['EshopProductAuthor'] = array(
                    'run_eshop_products_id' => &$deleteAuthorsForProductIds,
                );
            }
            if ($deleteCategoriesForProductIds) {
                $batch['delete']['EshopProductCategoryProduct'] = array(
                    'run_eshop_products_id' => &$deleteCategoriesForProductIds,
                );
            }
            if ($options['simulate']) {                
                App::log('EshopProductImport_importUpdate', 'batch', array(
                    'var' => $batch,
                ));
                if (
                    empty($this->progress['interrupted'])
                    && empty($options['recordOffset'])
                    && (
                        $this->importPid === 'pemicAvailabilityUpdate'
                        && $options['useFullCatalogue']
                        || 
                        $this->importPid === 'ikarUpdate'
                        || 
                        $this->importPid === 'albatrosUpdate'
                        ||
                        $this->importPid === 'partnertechnicUpdate'
                        ||
                        $this->importPid === 'informUpdate'
                        ||
                        $this->importPid === 'slovartUpdate'
                        ||
                        $this->importPid === 'frankanaStockUpdate'
                        ||
                        $this->importPid === 'mrpStockUpdate'
                    )
                ) {
                    $this->progress['setSoldOutAvailabilitiesCount'] = count($this->pairFieldValues);
                    $this->progress['updatedSupplierProductsCount'] += count($this->pairFieldValues);
                    App::log('EshopProductImport_importUpdate', 'unprocessed eans', array(
                        'var' => $this->pairFieldValues,
                    ));
                }
//                $_SESSION['timer']['save'] += Utility::getTimer('save', false); //debug
            }
            else {  
                $this->saveBatch($batch, array(
                    'ignoreNewValueIf' => $this->ignoreNewValueIf,
                    'updateOldValueIf' => $this->updateOldValueIf,
                    //'debugUpdateQuery' => true,
                ));
//                $_SESSION['timer']['save'] += Utility::getTimer('save', false); //debug
//                Utility::startTimer('setInactive'); //debug
                // be sure that really all products without price and tax_rate
                // are inactive (despite the above effort to do so)
                $this->setInactiveWithoutPrice();
//                $_SESSION['timer']['setInactive'] += Utility::getTimer('setInactive', false); //debug
                if (
                    empty($this->progress['interrupted'])
                    && empty($options['recordOffset'])
                    && (
                        $this->importPid === 'pemicAvailabilityUpdate'
                        && $options['useFullCatalogue']
                        || 
                        $this->importPid === 'ikarUpdate'
                        || 
                        $this->importPid === 'albatrosUpdate'
                        ||
                        $this->importPid === 'partnertechnicUpdate'
                        ||
                        $this->importPid === 'informUpdate'
                        ||
                        $this->importPid === 'slovartUpdate'
                        ||
                        $this->importPid === 'frankanaStockUpdate'
                        ||
                        $this->importPid === 'mrpStockUpdate'
                    )
                ) {
                    $this->progress['setSoldOutAvailabilitiesCount'] = count($this->pairFieldValues);
                    $this->progress['updatedSupplierProductsCount'] += count($this->pairFieldValues);
                    if ($this->importPid === 'mrpStockUpdate') {
                        $this->setSoldoutAvailability($this->pairFieldValues);
                    }
                    else if ($this->importPid === 'frankanaStockUpdate') {
                        $productIds = array_column($this->pairFieldValues, 'id');
                        $this->setSupplierSoldoutAvailability($this->supplier, $productIds);
                    }
                    else {
                        $this->setSupplierSoldoutAvailability($this->supplier, $this->pairFieldValues);
                    }
                }
            }
        }
        // IKAR: if only new items from last timestamp are loaded then it
        // occures quite often that annotations file generation error occures
        catch (Exception_IkarXmlReader_FileTransferDataGenerationError $e) {
            $this->progress['warnings'][] = __(__FILE__, 'There are no new items in IKAR annotations catalogue');
        }
        catch (Exception_IkarXmlReader_NoNewRecords $e) {
            $this->progress['warnings'][] = __(__FILE__, 'There are no new items in IKAR annotations catalogue');
        }
        catch (Throwable $e) {
            App::log('EshopProductImport_importUpdate', $e->getMessage(), array(
                'var' => $e, 
                'email' => true,
            ));
            $this->setError($e->getMessage());
            $this->progress = false;
        }
////see reserveTables() comment note        
//        $this->unreserveTables('EshopProductImport_importUpdate');
        App::unreserveProcessing($processingName);
        
//        $_SESSION['timer']['total'] = $_SESSION['timer']['read'] + $_SESSION['timer']['save'] + $_SESSION['timer']['setInactive']; //debug
//        $_SESSION['timer']['dbTotal'] = $_SESSION['timer']['dbDrop'] + $_SESSION['timer']['dbInsert'] + $_SESSION['timer']['dbUpdate']; //debug
//        App::log('debug', 'timer', array('var' => $_SESSION['timer'])); //debug
//        unset($_SESSION['timer']); //debug
        
        if (!empty($this->progress['interrupted'])) {
            App::log(
                'EshopProductImport_importUpdate', 
                __a(__FILE__, 'Import %s has been interrupted', $this->importPid), 
                array(
                    'var' => $this->progress, 
                    'email' => true,
                )
            );
        }
        // launch synchronization
        else {
            $synchronizationRequestTimeout = 1000; // ms = 1s
            if (
                empty($this->importedSupplierFields)
                || $this->importPid === 'pemicUpdate'
            ) {
                // do not make autosychronization but also do not log error 
            }
            elseif (App::getFreeMicrotime() > $synchronizationRequestTimeout + 1000) {            
                //App::logDebug('importUpdate - before async'); //debug
                try {
                    App::request(
                        App::getUrl(array(
                            'locator' => '/mvc/Eshop/EshopImports/autosynchronizeSupplierProducts',
                            'get' => array(
                                'launchedByImportPidJustForInfo' => $this->importPid,
                            ),
                            'absolute' => true,
                        )), array(
                            'timeout' => $synchronizationRequestTimeout / 1000,
                            'returnResponse' => false,
                        )
                    );
                } 
                catch (Throwable $e) {
                    App::log(
                        'EshopProductImport_importUpdate', 
                        __a(__FILE__, 'Import %s  - synchronization request has failed', $this->importPid), 
                        array(
                            'var' => $e, 
                            'email' => true,
                        )
                    );
                }
                //App::logDebug('importUpdate - after async'); //debug
            }
            else {
                App::log(
                    'EshopProductImport_importUpdate', 
                    __a(__FILE__, 'Import %s  - synchronization request has not been launched because of insufficien time', $this->importPid), 
                    array(
                        'var' => $this->progress, 
                        'email' => true,
                    )
                );
            }
        }
        
        return $this->progress;
    }
    
    /**
     * 
     * @param string $file
     */
    protected function loadSelectionRecords($file) {
        App::loadLib('App', 'Csv');
        $Csv = new Csv($file, array(
            'delimiter' => ';',
            'enclosure' => '"',
            'encoding' => 'CP1250',
            'decimalPoint' => ',',
            'thousandsSeparator' => '',
            'hasHeaders' => true,
            'recordFields' => array(
                $this->pairField => array('column' => 0, 'trim' => true),                                                    
                'price' => array('column' => 1, 'convertType' => 'float'),                                         
                'discount_price' => array('column' => 2, 'convertType' => 'float'),                                
                'discount_rate' => array('column' => 3, 'convertType' => 'float'),                                
                'shipment_until' => array('column' => 4, 'convertEncoding' => true, 'trim' => true),             
                'categories' => array('column' => 5, 'explode' => ';', 'trim' => true), 
            )
        ));
        $this->selectedItems = array();
        while (($csvRecord = $Csv->readRecord()) !== null) {       
            $pairFieldValue = $csvRecord[$this->pairField];
            unset($csvRecord[$this->pairField]);
            $csvRecord = array_filter($csvRecord);
            $this->selectedItems[$pairFieldValue] = $csvRecord;
        }
        $this->selectedItemsCount = count($this->selectedItems);
    }
    
    /**
     * Gets pair field values in form of array like:
     * 
     *      array(
     *          // if 'fields' option is set to single field, e.g. 'EshopProduct.id' or array('EshopProduct.id'), then: 
     *          'pairFieldValue01' => 1,
     *          // if 'fields' option is set many fields, e.g. array('EshopProduct.id', 'EshopProduct.language') then: 
     *          'pairFieldValue02' => array('id' => 2, 'language' => 'Slovenský'),
     *          // if 'fields' option is set to empty array() then: 
     *          'pairFieldValue03' => TRUE,
     *          ...
     *      )
     * 
     * ATTENTION: Products in group 'SLOVART' are treated regarding to their language (as there are 2 
     * publishers with name 'Slovart' - czech and slovak one). Group 'SLOVART' is valid for slovak publisher.
     * So products with language "český" are considered to be NOT included in 'SLOVART' group
     * even their manufacturer is Slovart (ideally it should be e.g. SlovartCZ)
     * 
     * @param array $options Following are available:
     *      - 'conditions' (array) Conditions to retrieve pair field values. Defaults
     *          to empty array();
     *      - 'fields' (array) Fields to be returned for each pair field value. 
     *          If an empty array is provided then the resulting value is set to TRUE. 
     *          If single field is provided e.g. 'EshopProduct.id' or array('EshopProduct.id') 
     *          then just the value of that single field is used. If many fields are provided 
     *          then data array is used. Defaults to array('EshopProduct.id').
     *      - 'manufacturerGroups' (array|string) Single manufacturer group name or an
     *          array of manufacturer group names to retrieve pair values for. If 'all' then all
     *          existing groups are considered. Defaults to empty array(), it means pair field
     *          values are retrieved regardless to manufacturer group of product.
     *      - 'invertGroupsSelection' (bool) If TRUE the conditions created according to 
     *          option 'manufacturerGroups' are inverted/negated. It means all pair field
     *          values but not those of provided manufacturers groups are returned.
     *          Defaults to FALSE.
     *      - 'avoidVydavatelstvoMaticeSlovenskej' (bool) If TRUE then pair values of
     *          products of Vydavatelstvo Matice Slovenskej are omitted. Defaults to FALSE.
     * 
     * @return array
     */
    protected function loadPairFieldValues($options = array()) {
// ATTENTION: If once 'substanceTypes' will be used then actualize phpDoc according uniknihy > EshopProduct::getPairFieldValues()
        $defaults = array(
            'conditions' => array(),
            'fields' => array('EshopProduct.id'),
            'manufacturerGroups' => array(),
            'invertGroupsSelection'=> false,
            'avoidVydavatelstvoMaticeSlovenskej' => false,
// unused for the moment  - uncoment all comented rows to make it available          
//     *      - 'substanceTypes' (array) Array containing values 'physical' and/or 'downloadable'.
//     *          For the moment downloadable products are 'ebooks' and all other are 
//     *          physical.
//     *          Defaults to array('physical', 'downloadable').
//            'substanceTypes' => array('physical', 'downloadable'),
        );
        $options = array_merge($defaults, $options);
        $fields = (array)$options['fields'];
        $fieldsLength = count($options['fields']);
        if ($this->pairField === 'supplier_product_id') {
            $fields[] = 'EshopSupplierProduct.' . $this->pairField;
        } 
        else {
            $fields[] = 'EshopProduct.' . $this->pairField;
        }
        $manufacturerConditions = array();
        // ATTENTION: EshopManufacturer.import_group is considered only if EshopProduct.manufacturer_group is not specified (NULL)
        if (!empty($options['manufacturerGroups'])) {
            if ($options['manufacturerGroups'] === 'all') {
                $hasSlovart = true;
                // take all which are in some of existing groups (with exception of SLOVART CZ)
                if (empty($options['invertGroupsSelection'])) {
                    $manufacturerConditions = array(
                        'EshopProduct.manufacturer_group !=' => array(null, '', 'SLOVART'),
                        'OR',
                        'EshopManufacturer.import_group !=' => array(null, '', 'SLOVART'),
                        'OR',
                        array(
                            'EshopProduct.manufacturer_group' => 'SLOVART',
                            'OR',
                            array('EshopProduct.manufacturer_group' => null),
                            'EshopManufacturer.import_group' => 'SLOVART',
                        ),
                        'EshopProduct.language !~%' => 'cesk',
                    );
                }
                // take all which are not in any of existing groups (or are in SLOVART CZ)
                else {
                    $manufacturerConditions = array(
                        'EshopProduct.manufacturer_group' => array(null, ''),
                        'EshopManufacturer.import_group' => array(null, ''),
                        'OR',
                        array(   
                            'EshopProduct.manufacturer_group' => 'SLOVART',
                            'OR',
                            array('EshopProduct.manufacturer_group' => null),
                            'EshopManufacturer.import_group' => 'SLOVART',
                        ),
                        'EshopProduct.language ~%' => 'cesk',
                    );
                }
            }
            else {
                $options['manufacturerGroups'] = (array)$options['manufacturerGroups'];
                // check if there is SLOVART group and treat it separately
                $hasSlovart = array_search('SLOVART', $options['manufacturerGroups']);
                if ($hasSlovart !== false) {
                    unset($options['manufacturerGroups'][$hasSlovart]);
                    $hasSlovart = true;
                }
                // take all which are in some of specified groups
                if (empty($options['invertGroupsSelection'])) {
                    if (!empty($options['manufacturerGroups'])) {
                        $manufacturerConditions = array(
                            'EshopProduct.manufacturer_group' => $options['manufacturerGroups'],
                            'OR',
                            array('EshopProduct.manufacturer_group' => null),
                            'EshopManufacturer.import_group' => $options['manufacturerGroups'],
                        );
                    }
                    if ($hasSlovart) {
                        if (!empty($manufacturerConditions)) {
                            $manufacturerConditions[] = 'OR';
                        }
                        $manufacturerConditions[] = array(
                            array(
                                'EshopProduct.manufacturer_group' => 'SLOVART',
                                'OR',
                                array('EshopProduct.manufacturer_group' => null),
                                'EshopManufacturer.import_group' => 'SLOVART',
                            ),
                            'EshopProduct.language !~%' => 'cesk',
                        );
                    }
                }
                // take all which are not in any of specified groups
                else {
                    if ($hasSlovart) {
                        $options['manufacturerGroups'][] = 'SLOVART';
                    }
                    $manufacturerConditions = array(
                        'EshopProduct.manufacturer_group !=' => $options['manufacturerGroups'],
                        array(
                            'EshopProduct.manufacturer_group !=' => null, 
                            'OR',
                            'EshopManufacturer.import_group !=' => $options['manufacturerGroups'],
                        )
                    );
                    if ($hasSlovart) {
                        $manufacturerConditions = array_merge($manufacturerConditions, array(
                            'OR',
                            array(   
                                'EshopProduct.manufacturer_group' => 'SLOVART',
                                'EshopProduct.language ~%' => 'cesk',
                            ),
                            'OR',
                            array(   
                                'EshopProduct.manufacturer_group' => null, 
                                'EshopManufacturer.import_group' => 'SLOVART',
                                'EshopProduct.language ~%' => 'cesk',
                            ),
                        ));
                    }
                }
            }
        }
        $vydavatelConditions = array();
        if ($options['avoidVydavatelstvoMaticeSlovenskej']) {
            $this->loadModel('EshopManufacturer');
            $Manufacturer = new EshopManufacturer();
            $vydavatelIds = $Manufacturer->findList(array(
                'key' => 'id',
                'fields' => array('id'),
                'conditions' => array(
                    'name LIKE "%Vydavatelstvo%Matice%Slovenskej%"',
                    'OR',
                    'name LIKE "%Matica%Slovenska%"',
                ),
                'literals' => array(
                    'conditions' => array(
                        'name LIKE "%Vydavatelstvo%Matice%Slovenskej%"',
                        'name LIKE "%Matica%Slovenska%"',
                    )
                )
            ));
            $vydavatelConditions = array(
                'EshopProduct.run_eshop_manufacturers_id !=' => $vydavatelIds
            );
        }
        
//        $substanceTypes = (array)$options['substanceTypes'];
        $this->pairFieldValues = array();
//        foreach ($substanceTypes as $type) {
            // reset conditions by provided default
            $conditions = DB::nestConditions($options['conditions']);
            // pair field values may not be empty 
//            // and must be retrieved only for actual type
//            if ($type === 'downloadable') {
//                $conditions[] = array(
//                        'EshopProduct.' . $this->pairField . ' !=' => array(null, ''),
//                        'EshopProduct.type' => 'ebook',
//                );
//            }
//            else {
                if ($this->pairField === 'supplier_product_id') {
                    $conditions[] = array(
                            'EshopSupplierProduct.' . $this->pairField . ' !=' => array(null, ''),
//                            'EshopProduct.type !=' => 'ebook',
                    );
                }
                else {
                    $conditions[] = array(
                            'EshopProduct.' . $this->pairField . ' !=' => array(null, ''),
//                            'EshopProduct.type !=' => 'ebook',
                    );
                }
//            }
            $joins = array();
            if ($manufacturerConditions) {
                $conditions[] = $manufacturerConditions;
                $joins[] = array(
                    'model' => 'EshopManufacturer',
                    'type' => 'left',
                );
            }
            if ($this->pairField === 'supplier_product_id') {
                $joins[] = array(
                    'type' => 'left',
                    'model' => 'EshopSupplierProduct',
                    'conditions' => array(
                        '*',
                        'EshopSupplierProduct.supplier_pid' => $this->supplier,
                    ),
                );
            }
            if ($vydavatelConditions) {
                $conditions[] = $vydavatelConditions;
            }
            $itemsResource = $this->Product->find(array(
                'conditions' => $conditions,
                'joins' => $joins,
                'fields' => $fields,
                'resource' => true,
                'ignoreSoftDeleted' => false,
            ));
//            $this->pairFieldValues[$type] = array();
            while ($item = DB::fetchArray($itemsResource)) {
                if (empty($item[$this->pairField])) {
                    continue;
                }
                $pairFieldValue = trim($item[$this->pairField]);
                unset($item[$this->pairField]);
                if (empty($item)) {
                    $item = true;
                }
                elseif ($fieldsLength === 1) {
                    $item = reset($item);
                }
//rblb//                $this->pairFieldValues[$type][$pairFieldValue] = $item;
                $this->pairFieldValues[$pairFieldValue] = $item;
            }
//        }
    }
    
    /**
     * Loads supplier product ids into property EshopProductImport::$supplierProductIds
     * as array of pairs {productId} => {supplierProductId}. Products are loaded only
     * for supplier specified in EshopProductImport::$supplier
     */
    protected function loadSupplierProductIds() {
        $itemsResource = $this->SupplierProduct->find(array(
            'conditions' => array(
                'supplier_pid' => $this->supplier,
            ),
            'fields' => array('id', 'run_eshop_products_id'),
            'resource' => true,
        ));
        $this->supplierProductIds = array();
        while ($item = DB::fetchArray($itemsResource)) {
            $this->supplierProductIds[$item['run_eshop_products_id']] = $item['id'];
        }
    }
    
    /**
     * Is the provided manufacturer name name of "Vydavatelstvo Matice slovenskej"?
     * 
     * @param string $manufacturerName
     * @return bool
     */
    protected function isManufacturerVydavatelstvoMaticeSlovenskej($manufacturerName) {
        $normalizedManufacturerName = Str::slugize($manufacturerName);
        return (
            strpos($normalizedManufacturerName, 'vydavatelstvo') !== false
            && strpos($normalizedManufacturerName, 'matice') !== false
            && strpos($normalizedManufacturerName, 'slovenskej') !== false
            ||
            strpos($normalizedManufacturerName, 'matica') !== false
            && strpos($normalizedManufacturerName, 'slovenska') !== false
        );
    }
        
    /**
     * Loads specified manufacturers into property EshopProductImport::$importedManufacturers
     * as array of pairs {manufacturerNormalizedName} => {manufacturerId}. 
     * 
     * If there are several mannufacturers with the same normalized name then just the first
     * one is stored with normalized name and the othres are stored with unchanged names
     * to keep possibility to distinguish them.
     * 
     * @param array $options Following are available:
     *      - 'conditions' (array) Conditions to imported manufacturers. Defaults
     *          to empty array();
     */
    protected function loadImportedManufacturers($options = array()) {
        $defaults = array(
            'conditions' => array(),
        );
        $options = array_merge($defaults, $options);
        $itemsResource = $this->Manufacturer->find(array(
            'conditions' => $options['conditions'],
            'fields' => array(
                'EshopManufacturer.name',
                'EshopManufacturer.id', 
            ),
            'resource' => true,
        ));
        $this->importedManufacturers = array();
        while ($item = DB::fetchArray($itemsResource)) {
            $normalizedName = Str::normalizeForNonstrictComparison($item['name']);
            if (!isset($this->importedManufacturers[$normalizedName])) {
                $this->importedManufacturers[$normalizedName] = $item['id'];
            }
            else {
                $this->importedManufacturers[$item['name']] = $item['id'];
            }
        }
    }
    
    /**
     * Returns imported manufacturer id according provided name.
     * 
     * @param string $name Manufacturer name
     * 
     * @return int|NULL Imported manufacturer id or NULL if there is found no imported manufacturer
     *      for provided name
     */
    protected function getImportedManufacturerId($name) {
        if (!empty($this->importedManufacturers[$name])) {
            return $this->importedManufacturers[$name];
        }
        $normalizedName = Str::normalizeForNonstrictComparison($name);
        if (!empty($this->importedManufacturers[$normalizedName])) {
            return $this->importedManufacturers[$normalizedName];
        }
        return null;
    }
    
    /**
     * Loads specified manufacturers into property EshopProductImport::$manufacturerDiscountRates
     * as array of pairs {manufacturerId} => {importDiscountRate}. Discount rate NULL
     * values are replaced by '' (empty string) for easy isset() testing and means that
     * there is no import discount rate defined for manufacturer and the default one can be used. 
     * On other side if there is a 0 (0.0) value it means explicitly that import discount rate
     * is 0 (none) and even the default one must be ignored in this case.
     * 
     * @param array $options Following are available:
     *      - 'conditions' (array) Conditions to imported manufacturers. Defaults
     *          to empty array();
     */
    protected function loadManufacturerDiscountRates($options = array()) {
        $defaults = array(
            'conditions' => array(),
        );
        $options = array_merge($defaults, $options);
        $itemsResource = $this->Manufacturer->find(array(
            'conditions' => $options['conditions'],
            'fields' => array(
                'EshopManufacturer.id', 
                'EshopManufacturer.import_discount_rate',
            ),
            'resource' => true,
        ));
        $this->manufacturerDiscountRates = array();
        while ($item = DB::fetchArray($itemsResource)) {
            $this->manufacturerDiscountRates[$item['id']] = ($item['import_discount_rate'] === null ? '' : $item['import_discount_rate']);
        }
    }
    
    /**
     * Returns manufacturer discount rate according provided id.
     * 
     * @param int $manufacturerId
     * 
     * @return int|NULL|FALSE Manufacturer discount rate integer (even zero). NULL if specified 
     *      manufacturer has no explicit discount rate.
     *      FALSE if among loaded there is found no manufacturer for provided id.
     */
    protected function getManufacturerDiscountRate($manufacturerId) {
        if (isset($this->manufacturerDiscountRates[$manufacturerId])) {
            if ($this->manufacturerDiscountRates[$manufacturerId] !== '') {
                return $this->manufacturerDiscountRates[$manufacturerId];
            }
            return null;
        }
        return false;
    }
    
    protected function getCategoryId($supplierCategoryCode, $supplierRecord = null) {
        
        if ($this->categoryConversions === null) {
            $configName = null;
            switch ($this->supplier) {
                case 'pemic':
                    $configName = 'pemicCategoriesConversions';
                    break;

                case 'inform':
                    $configName = 'informCategoriesConversions';
                    break;

                case 'partnertechnic':
                    $configName = 'partnerTechnicCategoriesConversions';
                    break;

                case 'ikar':
                    $configName = 'ikarCategoriesConversions';
                    break;

                case 'slovart':
                    $configName = 'slovartCategoriesConversions';
                    break;
                
                case 'kosmas':
                    $configName = 'kosmasCategoriesConversions';
                    break;
                
                case 'albatros':
                    $configName = 'albatrosCategoriesConversions';
                    break;

                case 'internal':
                    break;

                default:
                    return null;
            }
            $categories = $this->Category->findList(array(
                'key' => 'code',
                'fields' => array('id'),
            ));
            if ($configName) {
                $this->categoryConversions = $this->getConfig($configName, $this->configFilepath);
                $tmp = array();
                foreach ($this->categoryConversions as $supplierCode => $internalCode) {
                    if (empty($internalCode)) {
                        continue;
                    }
                    $supplierCode = $this->normalizeSupplierCategoryCode($supplierCode);
                    $categoryId = Sanitize::value($categories[$internalCode]);
                    $tmp[$supplierCode] = $categoryId;
                }
                $this->categoryConversions = $tmp;
            }
            else {
                $this->categoryConversions = $categories;
            }
        }
        
        $supplierCategoryCode = $this->normalizeSupplierCategoryCode($supplierCategoryCode);
        if (isset($this->categoryConversions[$supplierCategoryCode])) {
            $categoryId = $this->categoryConversions[$supplierCategoryCode];
            
            // kosmas specific (for alterego)
            if (
                $this->supplier === 'kosmas' 
                && $supplierCategoryCode === 239
                && !empty($supplierRecord['_languageCode'])
                && isset($this->categoryConversions['239-' . $supplierRecord['_languageCode']])
            ) {
                $categoryId = $this->categoryConversions['239-' . $supplierRecord['_languageCode']];
            }
            
            return $categoryId;
        }
        return null;
    }  
    
    protected function normalizeSupplierCategoryCode($code) {
        switch ($this->supplier) {
            case 'partnertechnic':
                $code = Str::slugize(trim($code));
                break;
        }
        return $code;
    }
    
    /**
     * Copies all Product fields which exists in $rawRecord but not exist in $this->record 
     * or $this->supplierRecord yet
     * 
     * @param array& $rawRecord Raw record returned by CSV/XML reader. Passed by reference.
     * @param string $model Optional. Model name to copy missing fields for. Possible
     *      values are 'EshopProduct' ($this->record is processed) and 'EshopSupplierProduct'
     *      ($this->supplierRecord is processed). Defaults to 'EshopProduct'.
     */
    protected function copyMissingFields(&$rawRecord, $model = 'EshopProduct') {
        static $productFields = null;
        static $supplierProductFields = null;
        if ($model === 'EshopProduct') {            
            if ($productFields === null) {
                $productFields = array_flip($this->Product->getFields());
            }
            $this->record = array_merge(array_intersect_key($rawRecord, $productFields), $this->record);
        }
        elseif ($model === 'EshopSupplierProduct') {
            if ($supplierProductFields === null) {
                $supplierProductFields = array_flip($this->SupplierProduct->getFields());
            }
            $this->supplierRecord = array_merge(array_intersect_key($rawRecord, $supplierProductFields), $this->supplierRecord);
        }
    }   
    
    /**
     * Sets 'availability' to 'enum_soldout, 'long_delivery_time' and 'reprint' to FALSE
     * and 'available_from' to NULL for all ids in pairFieldValues property where 
     * product is not in presale or reprint. 
     * It means that the pairFieldValues must be initialized before the call of 
     * this method into list of {pairFieldValue} => {id}.
     * 
     * Used by pemicAvailabilityUpdate (full catalogue only), ikarUpdate, albatrosUpdate,
     * partnertechnicUpdate, informUpdate and slovartUpdate imports
     * 
     * @param string $supplier
     * @param array& $productIds Passed by reference (to save memory)
     */
    protected function setSupplierSoldoutAvailability($supplier, &$productIds) {
//        while (($batchSupplierProductIds = array_splice($updatedSupplierProductIds, 0, $options['batchSize']))) {
        $this->SupplierProduct->update(
            array(
                'stock' => 0,
                'availability' => 'enum_soldout',
                'long_delivery_time' => false,
                'reprint' => false,
                'available_from' => null,
            ), 
            array(
                'conditions' => array(
                    'supplier_pid' => $supplier,
                    'run_eshop_products_id' => &$productIds,
                    array(
                        'availability !=' => 'enum_presale',
                        /*/>
                        'OR',
                        'available_from' => null,
                        'OR',
                        'available_from < NOW() - INTERVAL 30 DAY',
                        //*/
                    )
                ),
            )
        );
    }

    /**
     * Sets 'availability' to 'enum_soldout, 'long_delivery_time' and 'reprint' to FALSE
     * and 'available_from' to NULL for all ids in pairFieldValues property where 
     * product is not in presale or reprint. 
     * It means that the pairFieldValues must be initialized before the call of 
     * this method into list of {pairFieldValue} => {id}.
     * 
     * @param array& $productIds Passed by reference (to save memory)
     */
    protected function setSoldoutAvailability(&$productIds) {
//        while (($batchSupplierProductIds = array_splice($updatedSupplierProductIds, 0, $options['batchSize']))) {
        $this->Product->update(
            array(
                'stock' => 0,
                'availability' => 'enum_soldout',
                'long_delivery_time' => false,
                'reprint' => false,
                'available_from' => null,
            ), 
            array(
                'conditions' => array(
                    'id' => &$productIds,
                    array(
                        'availability !=' => 'enum_presale',
                        /*/>
                        'OR',
                        'available_from' => null,
                        'OR',
                        'available_from < NOW() - INTERVAL 30 DAY',
                        //*/
                    )
                ),
            )
        );
    }
    
    /**
     * Sets 'active' to 0 for all products which have 'price' or 'tax_rate' 0.
     * 
     * ATTENTION: This take cca 10s in vydavatel DB with 160 000 products
     * 
     * Used by update imports to ensure this
     */
    protected function setInactiveWithoutPrice() {
        $this->Product->update(array('active' => false), array(
            'conditions' => array(
                'EshopProduct.price' => 0,
                'OR',
                'EshopProduct.tax_rate' => 0,
            )
        ));
    }
    
    /**
     * Do all values of provided array equal to NULL?
     * 
     * @param array $array
     * @return boolean
     */
    protected function hasNullsOnlyValues($array) {
        foreach ($array as $v) {
            if ($v !== null) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Imports specified $field value from its source stored in source field ({$field}_import_source)
     * 
     * @param string $field Field name to import from its source. One of 'image', 'description'
     * @param array $options Following are available:
     *      - 'conditions' (array) Aditional custom conditions to select products
     *          for which the import applies.
     *      - 'reimportFailed' (bool) All sources which has been already imported
     *          keeps track of their import result (OK/NOK) to avoid repetitive imports
     *          if sources wich are invalid/broken. But if (after some time) you
     *          would like to try to reimport them you have this possibility by 
     *          setting this option TRUE. Defaults to FALSE.
     * 
     * @return array The array of progress is returned.
     */
    public function importFromSource($field, $options = null) {
        $defaults = array(
            'conditions' => array(),
            'reimportFailed' => false,
            'microtimeReserve' => 15000,
            'processAmount' => 0, // for debuging, if 0 then all records are processed
            'importAmount' => 0, // for debuging, if 0 then all records are imported
        );
        $options = array_merge($defaults, $options);
        if (!in_array($field, array('image', 'description'))) {
            $this->progress['errors'][] = __v(__FILE__, "Invalid field name '%s'", $field);
            return $this->progress;
        }
        
        // !!! keep this is synchro with the app/config/database.php > 'tableReservationTimeLimit' => ...
        set_time_limit(600);
        ini_set('memory_limit', '256M');
        
        // Ensure that there are not launched 2 paralel imports of the same field 
        // Avoid reservation of products table (as this process will be launched often)
        // so use for this reservation of fictive table
        try {
            $processingName = 'EshopProductImport_importFromSource_' . $field;
            App::reserveProcessing($processingName);
        } 
        catch (Throwable $e) {
            $this->progress['warnings'][] = __v(__FILE__, "There is already a runnig import of '%s'. Try later.", $field);
            return $this->progress;
        }
        $sourceField = $field . '_import_source';
        $resultField = $field . '_import_result';
        $conditions = array(
            EshopProduct::getPublishedConditions(),
            'EshopProduct.unavailable' => false,
            'EshopProduct.deleted' => null,
            'EshopSupplierProduct.' . $sourceField . ' !=' => null,
            'EshopProduct.' . $field => null,
        );
        $order = array(
            'EshopProduct.id DESC',
        );
        $literals = array();
        if (!empty($options['reimportFailed'])) {
            $order[] = '(EshopSupplierProduct.' . $resultField . ' IS NULL) DESC';
            $literals['order'] = array(
                '(EshopSupplierProduct.' . $resultField . ' IS NULL) DESC',
            );
        }
        else {
            $conditions['EshopSupplierProduct.' . $resultField] = null;
        }
        $conditions = array_merge(
            DB::nestConditions((array)$options['conditions']), 
            $conditions
        );
        $order[] = 'EshopSupplierProduct.id ASC';
        
        $itemsResource = $this->Product->find(array(
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'EshopSupplierProduct',
                )
            ),
            'conditions' => $conditions,
            'fields' => array(
                'EshopProduct.id', 
                'EshopProduct.name', 
                'EshopSupplierProduct.id AS eshop_supplier_products_id',
                'EshopSupplierProduct.' . $sourceField
            ),
            'resource' => true,
            // import at first for newest items possibly visible on home page
            'order' => $order,
            'literals' => $literals,
            'group' => 'EshopProduct.id',
        ));
        while ($item = DB::fetchArray($itemsResource)) {
            
            // check remaining time - finish the processing before php 
            // would be interrupted by execution time constrain
            if (App::getFreeMicrotime() < $options['microtimeReserve']) {
                $this->progress['interrupted'] = true;
                break;
            }
            // finish the processing if processAmount or importAmount is reached
            if (
                $options['processAmount'] && --$options['processAmount'] == 0 //debug
                || $options['importAmount'] && $options['importAmount'] == $this->progress['importedProductsCount'] //debug
            ) {
                break;
            }

            $this->progress['processedProductsCount']++;     
            
            try {
                $data = array(
                    'id' => $item['id'],
                );
                $result = 'OK';
                if (
                    substr($item[$sourceField], 0, 4) ===  'http'
                    && !Validate::url($item[$sourceField], array('exists' => true))
                ) {
                    $this->progress['errors'][] = __e(
                        __FILE__, 
                        'Import of %s from source %s has failed (id: %s, title: %s): invalid source url', 
                        $field, $item[$sourceField], $item['id'], $item['name']
                    );
                    $result = 'NOK';
                }
                elseif ($field === 'description') {
                    if ( 
                        ($description = @file_get_contents($item[$sourceField]) 
                    )) {
                        $data[$field] = $description;
                    }
                    else {
                        $this->progress['errors'][] = __e(
                            __FILE__, 
                            'Import of %s from source %s has failed (id: %s, title: %s)', 
                            $field, $item[$sourceField], $item['id'], $item['name']
                        );
                        $result = 'NOK';
                    }
                }
                else {                    
                    $data[$field] = array($item[$sourceField]);
                }
                //App::log('EshopProductImport_importFromSource', 'Imported data record', array('var' => $data)); //debug
                if ($result !== 'NOK') {                    
                    $this->Product->save($data, array(
                        'validate' => false,
                    ));                
                }
            } 
            catch (Throwable $e) {
                $this->progress['errors'][] = __e(
                    __FILE__, 
                    'Import of %s from source %s has failed (id: %s, title: %s): %s', 
                    $field, $item[$sourceField], $item['id'], $item['name'], $e->getMessage()
                );
                $result = 'NOK';
            }
            // update progress info
            $this->progress['importedProductsCount']++;
            if ($result === 'OK') {
                $this->progress['updatedProductsCount']++;
            }
            else {
                $this->progress['failedProductsCount']++;
            }
            $this->SupplierProduct->save(
                array(
                    'id' => $item['eshop_supplier_products_id'],
                    $resultField => $result,
                ),
                array(
                    'validate' => false,
                )
            );                
        }
        
        App::unreserveProcessing($processingName);
        
        return $this->progress;
    }
    
    /**
     * Downloads specified import file from supplier source
     * 
     * @param string $filePid Available are 'albatrosCatalogue'
     * 
     * @return boolean
     */
    public function downloadImportFile($filePid) {
        try {
            if ($filePid === 'albatrosCatalogue') {
                $this->loadLib('AlbatrosCatalogueXmlReader');
                $Reader = new AlbatrosCatalogueXmlReader(array(
                    'accessKey' => 'a018668da9b64263916f7e6a31ddb8f0',
                ));
            }
            elseif ($filePid === 'albatrosStock') {
                $this->loadLib('AlbatrosStockXmlReader');
                $Reader = new AlbatrosStockXmlReader(array(
                    'accessKey' => 'a018668da9b64263916f7e6a31ddb8f0',
                ));
            }
            /*/
            elseif ($filePid === 'partnerTechnicCatalogue') {
                $this->loadLib('PartnerTechnicCatalogueXmlReader');
                $Reader = new PartnerTechnicCatalogueXmlReader(array(
                    //'useFullCatalogue' => true,
                    'accessKey' => '0004778V10000101',
                ));
            }
            elseif ($filePid === 'ikarCatalogue') {
                $this->loadLib('IkarCatalogueXmlReader');
                $Reader = new IkarCatalogueXmlReader(array(
                    //'useFullCatalogue' => true,
                    'username' => '366178',
                    'password' => '00854_AlterEgo',
                ));
            }
            elseif ($filePid === 'ikarAnnotation') {
                $this->loadLib('IkarDescriptionXmlReader');
                $Reader = new IkarDescriptionXmlReader(array(
                    //'useFullCatalogue' => true,
                    'username' => '366178',
                    'password' => '00854_AlterEgo',
                ));
            }
            elseif ($filePid === 'ikarStock') {
                $this->loadLib('IkarAvailabilityXmlReader');
                $Reader = new IkarAvailabilityXmlReader(array(
                    //'useFullCatalogue' => true,
                    'username' => '366178',
                    'password' => '00854_AlterEgo',
                ));
            }
            //*/
            else {
                $this->setError(__v(__FILE__, 'Unknown file name: %s', $filePid));
                return false;
            }
        } 
        catch (Exception $e) {
            $this->setError(__v(__FILE__, 'File download has failed with following error: %s', $e->getMessage()));
            return false;
        }
        $file = $Reader->getPropertyFile();
        if (!is_readable($file)) {
            $this->setError(__v(__FILE__, 'Unreadable downloaded file: %s', $file));
            return false;
        }
        File::output(File::getRelativePath($file), array(
            'filename' => $filePid . '_' . date('Ymd_His'),
            'disposition' => 'download',
        ));
        return true;
    }
}
