<?php

class EshopProductGroup extends EshopModel {
    protected $table = 'run_eshop_product_groups';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'pid' => array('type' => 'varchar', 'default' => null, 'index' => 'index'),
        'slug' => array('type' => 'varchar', 'index' => 'index'),
        'seo_title' => array('type' => 'varchar', 'default' => null),
        'seo_description' => array('type' => 'varchar', 'default' => null),
        'seo_keywords' => array('type' => 'varchar', 'default' => null),
        'name' => array('type' => 'varchar', 'index' => 'index'),
        'description' => array('type' => 'text', 'default' => null),
        'hide_unavailable_products' => array('type' => 'bool', 'default' => 0, 'comment' => 'If TRUE then unavailable products are NOT shown in group products index'),
        'type' => array('type' => 'enum', 'values' => array('sale'), 'default' => null),
        'active_from' => array('type' => 'date', 'default' => null),
        'active_to' => array('type' => 'date', 'default' => null),
        'active' => array('type' => 'bool', 'default' => 1),
        'sort' => array('type' => 'int', 'default' => 0),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),     
        'deleted' => array('type' => 'datetime', 'default' => null, 'index' => 'index'),  
    );
    
    protected $translatedFields = array(
        'slug',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'name',
        'description',
    );
    
    protected $nameField = 'name';
    
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(                                 
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                ),
            ),            
        );
    }    
    
    /**
     * Normalizes data 
     * 
     * @param array $data
     * @param string $options Optional. 
     *      - 'on' (string) Possible values are 'create', 'update', NULL
     *      - 'alternative' (string) Possible values are 'search', NULL
     * 
     * @return array
     */
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        
        if (
            empty($data['slug'])
            && (
                $options['on'] === 'create'
                || array_key_exists('slug', $data) 
            )
        ) {
            $data['slug'] = Sanitize::value($data['name']);
        }
        
        if (!empty($data['slug'])) {
            if ($options['on'] == 'create') {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                ));
            }
            else {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                    'id' => Sanitize::value($data['id']),
                ));
            }
        }
        
        if (
            $options['on'] === 'create'
            && empty($data['seo_title']) 
            && !empty($data['name'])
        ) {
            $data['seo_title'] = $data['name'];
        }
        
        if (!empty($data['active_from'])) {
            $data['active_from'] = date('Y-m-d', strtotime($data['active_from']));
        }
        
        if (!empty($data['active_to'])) {
            $data['active_to'] = date('Y-m-d', strtotime($data['active_to']));
        }
               
        if (!empty($data['product_ids'])) {
            if (is_string($data['product_ids'])) {
                $data['product_ids'] = explode(';', $data['product_ids']);
            }
            $data['product_ids'] = array_filter(array_map('trim', $data['product_ids']));
        }
        
        return parent::normalize($data, $options);
    }
    
    /**
     * Saves:
     *      standard product group fields
     *      + habtm products (ids are in product_ids field)
     * 
     * @param array $data
     * @param array $options Model::save() options, e.g. 'lang' - probably the only used here.
     * 
     * @return bool|array Array of saved data on success. FALSE on failure.
     * 
     * @throws Exception
     */
    public function saveAll($data, $options = array()) {        
        // update or insert
        $options['on'] = null; // to make Model::isCreation() resolve correctly
        $options['on'] = ($this->isCreation($data, $options)) ? 'create' : 'update';
        
        // normalize
        $data = $this->normalize($data, $options);
        // turn normalization off for further processing
        $options['normalize'] = false;
        
        // validate
        if (!$this->validate($data, $options)) {
            return false;
        }
        // turn validation off for further processing
        $options['validate'] = false;
        
        try {
            $this->reserveTables('EshopProductGroup_saveAll', array(
                'EshopProductGroup',
                'EshopProductGroupProduct',
            ), array('tries' => 20, 'retryTime' => 1000));
            DB::startTransaction('EshopProductGroup_saveAll');
            $result = $this->save($data, $options);
            if (!$result) {
                DB::rollbackTransaction('EshopProductGroup_saveAll');
                $this->unreserveTables('EshopProductGroup_saveAll');
                return false;
            }
            $productGroupId = $this->getPropertyId();
            $data = $result;
            // check for product_ids
            if (isset($data['product_ids'])) {
                $ProductGroupProduct = $this->loadModel('EshopProductGroupProduct', true);
                $ProductGroupProduct->deleteBy('run_eshop_product_groups_id', $productGroupId);
                if (!empty($data['product_ids'])) {
                    $records = array();
                    foreach ($data['product_ids'] as $groupedId) {
                        $records[] = array(
                            'run_eshop_products_id' => $groupedId,
                            'run_eshop_product_groups_id' => $productGroupId
                        );
                    }
                    DB::insert(
                        $ProductGroupProduct->getPropertyTable(), 
                        $records, 
                        array('multiple' => true)
                    );
                }
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopProductGroup_saveAll');
            $this->unreserveTables('EshopProductGroup_saveAll');
            throw new Exception($e->getMessage());
        }
        
        DB::commitTransaction('EshopProductGroup_saveAll');
        $this->unreserveTables('EshopProductGroup_saveAll');
        return $data;
    }
    
    public function findAll($options = array()) {
        $defaults = array(
            'first' => false,
        );
        $options = array_merge($defaults, $options);
        // apply first
        $first = $options['first'];
        if ($first) {
            $options['first'] = false;
            $options['limit'] = 1;
        }
        
        // reserve tables (to avoid writes while reading data)
        $this->reserveTables('EshopProductGroup_findAll', array(
            'EshopProductGroup',
            'EshopProductGroupProduct',
        )); 
        $options['key'] = 'EshopProductGroup.id';
        $productGroups = $this->findList($options);
        $productGroupIds = array_keys($productGroups);
                
        // get product ids
        $ProductGroupProduct = $this->loadModel('EshopProductGroupProduct', true);
        $products = $ProductGroupProduct->find(array(
            'fields' => array(
                'EshopProductGroupProduct.run_eshop_products_id', 
                'EshopProductGroupProduct.run_eshop_product_groups_id',
            ),
            'conditions' => array('EshopProductGroupProduct.run_eshop_product_groups_id' => $productGroupIds),
        ));
        foreach ($products as $product) {
            $groupId = $product['run_eshop_product_groups_id'];
            if (empty($productGroups[$groupId]['product_ids'])) {
                $productGroups[$groupId]['product_ids'] = array();
            }
            $productGroups[$groupId]['product_ids'][] = $product['run_eshop_products_id'];
        }
        $this->unreserveTables('EshopProductGroup_findAll');
        
        // get just values (without product ids in keys) to be properly json encoded
        $productGroups = array_values($productGroups);
        
        if ($first) {
            $productGroups = reset($productGroups);
        }
        return $productGroups;
    }
    
    /**
     * Resolves slug of active group
     * 
     * @param array $options Following are available
     *      - 'arg' (mixed) The first argument passed in actual URL. Defaults to App::$arg[0]
     *      - 'groupProductsIndexSlug' (string) Slug of content 'Eshop.EshopProducts.indexGroup'.
     *          It can be provided for optimisation reasons. If not provided then it is retrieved by method itself.
     *      - 'productViewSlug' (string) Slug of content 'Eshop.EshopProducts.view'.
     *          It can be provided for optimisation reasons. If not provided then it is retrieved by method itself.
     * 
     * @return string|null
     */
    public function resolveActiveSlug($options = array()) {
        $defaults = array(
            'arg' => null,
            'groupProductsIndexSlug' => null,
            'productViewSlug' => null,
        );
        $options = array_merge($defaults, $options);
        if (
            $options['arg'] === null
            && isset(App::$args[0])
        ) {
            $options['arg'] = App::$args[0];
        }
        
        $activeSlug = null;
        
        // find active slug
        // - if we are on group products index page then the arg is the group slug
        if (
           $options['groupProductsIndexSlug'] !== null
           && SLUG === $options['groupProductsIndexSlug']
           ||
           $options['groupProductsIndexSlug'] === null
           && SLUG === ($options['groupProductsIndexSlug'] = App::getContentLocatorByPid('Eshop.EshopProducts.indexGroup'))
        ) {
            $activeSlug = $options['arg'];
        }
        // - if we are on product view page, then $options['arg'] is product slug 
        // and actual group slug can be found... 
        elseif (
           $options['productViewSlug'] !== null
           && SLUG === $options['productViewSlug']
           ||
           $options['productViewSlug'] === null
           && SLUG === App::getContentLocatorByPid('Eshop.EshopProducts.view')
        ) {
            $GroupXProduct = $this->loadModel('EshopProductGroupProduct', true);
            $items = $GroupXProduct->find(array(
                'joins' => array(
                    array(
                        'model' => 'EshopProduct',
                        'type' => 'left',
                    )
                ),
                'conditions' => array('EshopProduct.slug' => $options['arg']),
                'fields' => array('run_eshop_product_groups_id'),
            ));
            if ($items) {
                // get groups ids
                $tmp = array();
                foreach($items as $item) {
                    $tmp[] = $item['run_eshop_product_groups_id'];
                }
                $items = $tmp;
                // get groups
                $groups = $this->findBy('id', $items, array(
                    'fields' => array('EshopProductGroup.slug')
                ));  
                if (count($groups) > 1) {
                    $refererUrl = App::parseUrl(App::getRefererUrl('/'));
                    if (
                        $refererUrl['type'] === 'slug' 
                        && $refererUrl['slug'] === $options['groupProductsIndexSlug']
                    ) {
                        // - then find the group we are going from
                        $groupSlug = reset($refererUrl['args']);
                        foreach ($groups as $group) {
                            if ($group['slug'] === $groupSlug) {
                                break;
                            }
                        }
                    }
                    else {
                        $group = reset($groups);
                    }
                }
                else {
                    $group = reset($groups);
                }
                $activeSlug = $group['slug'];
            }
        }
        
        return $activeSlug;
    }      
    
    /**
     * @inheritdoc
     * 
     * Overrides Model::save() to allow:
     * - URL redirection if slug is changed
     * 
     * @param array $data
     * @param array $options Model::save() options plus following are available
     *      - 'addRedirection' (bool) Adds redirection on change of slug. Defaults to !ON_LOCALHOST.
     */
    public function save($data, $options = array()) {
        $options = array_merge(array(
            'addRedirection' => !ON_LOCALHOST,
        ), $options);
        // get old record for redirection addition here below
        $oldRecord = array();
        if (
            $options['addRedirection']
            && !empty($data['id'])
            && array_key_exists('slug', $data)
        ) {
            $oldRecord = $this->findFirstBy('id', $data['id'], array(
                'fields' => array($this->name . '.slug'),
                'lang' => Sanitize::value($options['lang']),
            ));
        }
        // save
        $result = parent::save($data, $options);
        if (!$result) {
            return false;
        }
        // add redirection if slug was changed
        if (
            $options['addRedirection']
            && !empty($oldRecord['slug'])
            && !empty($result['slug'])
            && $oldRecord['slug'] != $result['slug']
        ) {
            $webContentLocator = App::getContentLocatorByPid('Eshop.EshopProducts.indexGroup', array(
                'lang' => Sanitize::value($options['lang']),
            ));
            $oldLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($oldRecord['slug']),
            ));
            $newLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($result['slug']),
            ));
            App::loadModel('App', 'UrlRedirection');
            $UrlRedirection = new UrlRedirection();
            $UrlRedirection->add($oldLocator, $newLocator);
        }
        return $result;
    }        
}
