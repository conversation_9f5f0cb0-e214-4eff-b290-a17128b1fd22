<?php

class EshopProductType extends Model {
    protected $table = 'run_eshop_product_types';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'pid' => array('type' => 'varchar', 'default' => null, 'index' => 'index'),
        'name' => array('type' => 'varchar'),
        'description' => array('type' => 'text', 'default' => null),
        // to implement subtypes organized in tree
        //'parent_id' => array('type' => 'int', 'default' => null),
        //'path' => array('type' => 'varchar', 'default' => null),
        //'sort' => array('type' => 'int', 'default' => null),
        'variant' => array('type' => 'text', 'default' => null, 'comment' => 'General attribute to introduce any kind of product type variants. Variants should be introduced in such a way that they do not need attribute name to understand them. If provided then variant of each products of the type is forced to this value (on product or product type create/update)'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
    );
    
    protected $translatedFields = array(
        'name',
        'description',
    );
    
    /** 
     * Saves provided data - see Model::save() for details - plus it updates variants
     * of all products belonging to saved product type according the variant of product 
     * type (if it has been changed)
     */
    public function save($data, $options = array()) {
        $oldVariant = null;
        $isCreation = $this->isCreation($data, $options);
        if (!$isCreation) {
            $oldVariant = $this->findFieldBy('variant', 'id', $data['id']);
        }
        $data = parent::save($data, $options);
        // update variants of all products belonging to this product type but only
        // if variant field is in data and it has been changed
        if (
            $data 
            && !$isCreation
            && array_key_exists('variant', $data)
            && (string)$data['variant'] !== (string)$oldVariant
        ) {
            $this->loadModel('EshopProduct');
            $Product = new EshopProduct();
            $Product->update(
                array('variant' => $data['variant']),
                array(
                    'normalize' => false,
                    'validate' => false,
                    'conditions' => array(
                        'run_eshop_product_types_id' => $data['id'],
//                        // do the change only if product variant has not be customized 
//                        // meanwhile
//                        'variant' => $oldVariant,
                    )
                )
            );
            
        }
        return $data;
    }
}
