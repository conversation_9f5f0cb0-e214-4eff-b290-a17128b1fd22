<?php

class EshopProductAttribute extends Model {
    protected $table = 'run_eshop_product_attributes';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_eshop_products_id' => array('type' => 'int', 'index' => 'index'),
        'run_eshop_product_attribute_types_id' => array('type' => 'int', 'index' => 'index'),
        'value' => array('type' => 'text', 'default' => null),
        'selective' => array('type' => 'bool', 'default' => 0, 'comment' => 'Is this attribute selective (list of values separated by semicolon ;)?'),
        'priced' => array('type' => 'bool', 'default' => 0, 'comment' => 'Are values of selective attribute priced (prices are separated from values by ::)?'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
    );
    
}
