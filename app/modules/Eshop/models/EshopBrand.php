<?php

class EshopBrand extends EshopModel {
    protected $table = 'run_eshop_brands';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'pid' => array('type' => 'varchar', 'default' => null, 'index' => 'unique'),
        'slug' => array('type' => 'varchar', 'index' => 'index'),
        'seo_title' => array('type' => 'varchar', 'length' => 255, 'default' => null),
        'seo_description' => array('type' => 'varchar', 'length' => 255, 'default' => null),
        'seo_keywords' => array('type' => 'varchar', 'length' => 255, 'default' => null),
        'name' => array('type' => 'varchar'),
        'description' => array('type' => 'text', 'default' => null),
        'logo' => array('type' => 'varchar', 'default' => null),
        'website' => array('type' => 'varchar', 'default' => null, 'comment' => 'Brand official website'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
        'deleted' => array('type' => 'datetime', 'default' => null, 'index' => 'index'),
    );
    
    protected $translatedFields = array(
        'seo_title',
        'seo_description',
        'seo_keywords',        
        'description',
    );
    
    //protected $fileStore = 'userfiles/eshop/EshopBrand'; //@todo - existing files must moved too
    
    protected $fileFields = array(
        'logo' => array(
            'variants' => array(
                '' => array(
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(450, 300),
                ),
            ),
        ),
    );
    
    public function __construct() {
        parent::__construct();
        
        $this->validations = array(
            'slug' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
            ),   
            'seo_title' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                array(
                    'rule' => 'fieldMaxLength',
                    'message' => __v(__FILE__, 'Príliš dlhý text'),
                )
            ),   
            'seo_description' => array(
                array(
                    'rule' => 'fieldMaxLength',
                    'message' => __v(__FILE__, 'Príliš dlhý text'),
                )
            ),   
            'seo_keywords' => array(
                array(
                    'rule' => 'fieldMaxLength',
                    'message' => __v(__FILE__, 'Príliš dlhý text'),
                )
            ),   
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Už existuje'),
                ),
            ),   
        );
    }    
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        
        // normalize name
        if (array_key_exists('name', $data)) {
            $data['name'] = trim($data['name']);
        }
                
        // populate empty slug by name
        if (
            !empty($data['name'])
            && (
                $options['on'] === 'create'
                && !isset($data['slug'])
                ||
                array_key_exists('slug', $data)
                && Validate::emptyValue($data['slug'])
            )
        ) {
            $data['slug'] = Validate::emptyValue($data['name']) ? '' : $data['name'];
        }
        
        // if slug is provided then force it to be unique and slugize it
        if (!empty($data['slug'])) {
            $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                'id' => empty($data['id']) ? null : $data['id'],
                'lang' => empty($options['lang']) ? null : $options['lang'],
            ));
        }
        
        // sanitize unallowed tags
        if (!empty($data['description'])) {
            $data['description'] = Sanitize::htmlTags($data['description'], array(
                'allow' => array(
                    'a', 'strong', 'br',
                ),
                'allowAttributes' => array(
                    'href', 'target',
                ),
            ));
        }
        if (!empty($data['banner_text'])) {
            $data['banner_text'] = Sanitize::htmlTags($data['banner_text'], array(
                'allow' => array(
                    'a', 'strong', 'br',
                ),
                'allowAttributes' => array(
                    'href', 'target',
                ),
            ));
        }
        
        // force seo_title and seo_keywords value to name value
        if (array_key_exists('name', $data)) {
            $data['seo_title'] = $data['name'];
            $data['seo_keywords'] = $data['name'];
        }
        
        // force seo_description
        if (array_key_exists('description', $data)) {
            $seoDescriptionFieldLength = $this->getFieldLength('seo_description');
            $description = trim(preg_replace('/(\s|\n)+/', ' ', Sanitize::htmlToText($data['description'], false)));
            $description = Str::explode('.', $description);
            $seoDescription = '';
            foreach ($description as $descriptionSentence) {
                if (mb_strlen($seoDescription . $descriptionSentence . '.') < $seoDescriptionFieldLength) {
                    $seoDescription .= $descriptionSentence . '.';
                }
                else {
                    break;
                }
            }
            $data['seo_description'] = $seoDescription;
        }        
        
        if (!empty($data['website'])) {
            $data['website'] = preg_replace('#^https?://#', '', trim($data['website']));
        }
                
        return parent::normalize($data, $options);
    }    
       /**
     * @inheritdoc
     * 
     * Overrides Model::save() to allow:
     * - URL redirection if slug is changed
     * 
     * @param array $data
     * @param array $options Model::save() options plus following are available
     *      - 'addRedirection' (bool) Adds redirection on change of slug. Defaults to !ON_LOCALHOST.
     */
    public function save($data, $options = array()) {
        $options = array_merge(array(
            'addRedirection' => true,
        ), $options);
        // get old record for redirection addition here below
        $oldRecord = array();
        if (
            $options['addRedirection']
            && !empty($data['id'])
            && array_key_exists('slug', $data)
        ) {
            $oldRecord = $this->findFirstBy('id', $data['id'], array(
                'fields' => array($this->name . '.slug'),
                'lang' => Sanitize::value($options['lang']),
            ));
        }
        // save
        $result = parent::save($data, $options);
        if (!$result) {
            return false;
        }
        // add redirection if slug was changed
        if (
            $options['addRedirection']
            && !empty($oldRecord['slug'])
            && !empty($result['slug'])
            && $oldRecord['slug'] != $result['slug']
        ) {
            $webContentLocator = App::getContentLocatorByPid('Eshop.EshopProducts.indexBrand', array(
                'lang' => Sanitize::value($options['lang']),
            ));
            $oldLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($oldRecord['slug']),
            ));
            $newLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($result['slug']),
            ));
            App::loadModel('App', 'UrlRedirection');
            $UrlRedirection = new UrlRedirection();
            $UrlRedirection->add($oldLocator, $newLocator, array(
                'lang' => Sanitize::value($options['lang'], App::$lang),
            ));
        }
        return $result;
    }   

}

