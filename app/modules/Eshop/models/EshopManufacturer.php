<?php

class EshopManufacturer extends EshopModel {
    protected $table = 'run_eshop_manufacturers';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'slug' => array('type' => 'varchar', 'index' => 'index'),
        'seo_title' => array('type' => 'varchar', 'default' => null),
        'seo_description' => array('type' => 'varchar', 'default' => null),
        'seo_keywords' => array('type' => 'varchar', 'default' => null),
        'name' => array('type' => 'varchar', 'index' => 'index'),
        'description' => array('type' => 'text', 'default' => null),
        'image' => array('type' => 'varchar', 'default' => null),
        'import_group' => array('type' => 'enum', 'values' => array('ALBATROS','IKAR','INFORM','PARTNERTECHNIC','MATICA','SLOVART'), 'default' => null, 'comment' => 'Manufacturer import group'),
        'import_discount_rate' => array('type' => 'decimal', 'length' => 5.2, 'default' => null, 'comment' => 'Manufacturer imported products discount rate. NULL means that some other general discount can be applied. 0 means that no (even general) discount is applied.'),
        'active' => array('type' => 'bool', 'default' => 1),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null), 
        'deleted' => array('type' => 'datetime', 'default' => null, 'index' => 'index'),  
    );
    
    protected $translatedFields = array(
        'slug',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'name',
        'description',
    );

    protected $fileFields = array(
        'image' => array(
            'variants' => array(
                '' => array(
//                    'extension' => 'jpg',
//                    'pngGifToJpg' => array(array(255, 255, 255)),
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fitY' => array(33),
                ),
                'original' => array(
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(300, 300),
                ),
            )
        ),
    );
    
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(                                 
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                ),
            ),            
            'image' => array(
                /*/>
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Vyberte obrázok'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Vyberte obrázok'),
                ),
                array(
                    'rule' => array('uploadData', array('notEmpty' => true)),
                    'message' => __v(__FILE__, 'Vyberte obrázok'),
                    'on' => 'create',
                ),
                //*/
                array(
                    'rule' => array('uploadData', array('notEmpty' => false)),
                    'message' => __v(__FILE__, 'Vyberte obrázok'),
                ),
            ),
        );
    }    
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
            'avoidSlugs' => array(),
        );
        $options = array_merge($defauts, $options);
        
        if (
            empty($data['slug'])
            && (
                $options['on'] === 'create'
                || array_key_exists('slug', $data) 
            )
        ) {
            $data['slug'] = Sanitize::value($data['name']);
        }
        
        if (!empty($data['slug'])) {
            if ($options['on'] == 'create') {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                    'avoidValues' => &$options['avoidSlugs'],
                ));
            }
            else {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                    'id' => Sanitize::value($data['id']),
                    'avoidValues' => &$options['avoidSlugs'],
                ));
            }
        }
        
        if (
            $options['on'] === 'create'
            && empty($data['seo_title']) 
            && !empty($data['name'])
        ) {
            $data['seo_title'] = $data['name'];
        }
                                
        return parent::normalize($data, $options);
    }
    
    /**
     * Ensures the existence of manufacturer specified by name. In the case that 
     * manufacturer does not exist, it is created in DB or added to batch (see input $newBatch).
     * 
     * Used mostly for imports
     * 
     * @staticvar array $existing Internal cache of existing manufacturers
     * @staticvar array $slugs Internal cache of new manufacturers slugs added to batch
     * @staticvar array $newId Internal cache of new manufacturer id added to batch
     * 
     * @param string $name Manufacturer name to ensure existence of
     * @param array& $newBatch Optional, passed by reference, auxiliary output. 
     *      Array of new manufacturer records to be used in batch save. If not provided then new 
     *      manufacturers are directly created in DB. Defaults to NULL (not provided).
     * 
     * @return int|bool Id of manufacturer having given $name. It does not matter if 
     *      manufacturer has existed or it has been created od added to batch. Returns 
     *      FALSE on $name validation error.
     */
    public function ensure($name, &$newBatch = null) {
        // internal cache of existing manufacturers 
        static $existing = null;
        // internal chache of created slugs
        static $slugs = array();
        // internal cache of new record id for case of batch records
        static $newId = null;
        
        // load existing manufacturers if not loaded yet
        if ($existing === null) {
            $itemsResource = $this->find(array(
                'fields' => array(
                    'EshopManufacturer.id',
                    'EshopManufacturer.name',
                ),
                'resource' => true,
            ));
            $existing = array();
            while ($item = DB::fetchArray($itemsResource)) {
                if ($item['name'] === '' || $item['name'] === null) {
                    continue;
                }
                $normalizedName = Str::normalizeForNonstrictComparison($item['name']);
                if ($normalizedName === '') {
                    continue;
                }
                if (!isset($existing[$normalizedName])) {
                    $existing[$normalizedName] = $item['id'];
                }
                else {
                    $existing[$item['name']] = $item['id'];
                }
            }
        }
        // load newId if not loaded yet
        if (is_array($newBatch) && $newId === null) {
            $newId = $this->getNextId();
        }
        // check for manufacturer existence
        if (isset($existing[$name])) {
            return $existing[$name];
        }
        $normalizedName = Str::normalizeForNonstrictComparison($name);
        if (isset($existing[$normalizedName])) {
            return $existing[$normalizedName];
        }
        // create new manufacturer
        $new = array(
            'name' => $name,
            'slug' => null,
            'created' => null,
            'modified' => null,
        );
        $new = $this->normalize($new, array(
            'on' => 'create',
            'avoidSlugs' => $slugs,
        ));
        if (
            !$this->validate($new, array(
                'on' => 'create',
                'normalize' => false,
                'allowFields' => array('name'),
            ))
        ) {
            return false;
        }
        // if batch array provided then put new record there
        if (is_array($newBatch)) {
            $new['id'] = $newId;
            $newBatch[] = $new;
            // store created slugs into cache in case of batch 
            $slugs[] = $new['slug'];
            $existing[$normalizedName] = $newId;
            $newId++;
        }
        // if no batch array provided then save it in DB
        else {
            $new = $this->save($new, array(
                'create' => true,
                'normalize' => false,
                'validate' => false,
            ));
            $existing[$normalizedName] = $new['id'];
            $newId = null;
        }
        // return id of manufacturer
        return $existing[$normalizedName];
    }
    
    /**
     * Resolves slug of active manufacturer
     * 
     * @param array $options Following are available
     *      - 'arg' (mixed) The first argument passed in actual URL. Defaults to App::$arg[0]
     *      - 'manufacturerProductsIndexSlug' (string) Slug of content 'Eshop.EshopProducts.indexManufacturer'.
     *          It can be provided for optimisation reasons. If not provided then it is retrieved by method itself.
     *      - 'productViewSlug' (string) Slug of content 'Eshop.EshopProducts.view'.
     *          It can be provided for optimisation reasons. If not provided then it is retrieved by method itself.
     * 
     * @return string|null
     */
    public function resolveActiveSlug($options = array()) {
        $defaults = array(
            'arg' => null,
            'manufacturerProductsIndexSlug' => null,
            'productViewSlug' => null,
        );
        $options = array_merge($defaults, $options);
        if (
            $options['arg'] === null
            && isset(App::$args[0])
        ) {
            $options['arg'] = App::$args[0];
        }
        
        $activeSlug = null;
        
        // find active slug
        // - if we are on manufacturer products index page then the arg is the manufacturer slug
        if (
           $options['manufacturerProductsIndexSlug'] !== null
           && SLUG === $options['manufacturerProductsIndexSlug']
           ||
           $options['manufacturerProductsIndexSlug'] === null
           && SLUG === ($options['manufacturerProductsIndexSlug'] = App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer'))
        ) {
            $activeSlug = $options['arg'];
        }
        // - if we are on product view page, then $options['arg'] is product slug 
        // and actual manufacturer slug can be found... 
        elseif (
           $options['productViewSlug'] !== null
           && SLUG === $options['productViewSlug']
           ||
           $options['productViewSlug'] === null
           && SLUG === App::getContentLocatorByPid('Eshop.EshopProducts.view')
        ) {
            $activeSlug = $this->findFieldBy('EshopManufacturer.slug', 'EshopProduct.slug',  $options['arg'], array(
                'joins' => array(
                    array(
                        'model' => 'EshopProduct',
                        'type' => 'left',
                    ),
                )
            ));
        }
        
        return $activeSlug;
    }        
    
    /**
     * @inheritdoc
     * 
     * Overrides Model::save() to allow:
     * - URL redirection if slug is changed
     * 
     * @param array $data
     * @param array $options Model::save() options plus following are available
     *      - 'addRedirection' (bool) Adds redirection on change of slug. Defaults to !ON_LOCALHOST.
     */
    public function save($data, $options = array()) {
        $options = array_merge(array(
            'addRedirection' => !ON_LOCALHOST,
        ), $options);
        // get old record for redirection addition here below
        $oldRecord = array();
        if (
            $options['addRedirection']
            && !empty($data['id'])
            && array_key_exists('slug', $data)
        ) {
            $oldRecord = $this->findFirstBy('id', $data['id'], array(
                'fields' => array($this->name . '.slug'),
                'lang' => Sanitize::value($options['lang']),
            ));
        }
        // save
        $result = parent::save($data, $options);
        if (!$result) {
            return false;
        }
        // add redirection if slug was changed
        if (
            $options['addRedirection']
            && !empty($oldRecord['slug'])
            && !empty($result['slug'])
            && $oldRecord['slug'] != $result['slug']
        ) {
            $webContentLocator = App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer', array(
                'lang' => Sanitize::value($options['lang']),
            ));
            $oldLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($oldRecord['slug']),
            ));
            $newLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($result['slug']),
            ));
            App::loadModel('App', 'UrlRedirection');
            $UrlRedirection = new UrlRedirection();
            $UrlRedirection->add($oldLocator, $newLocator);
        }
        return $result;
    }    
}
