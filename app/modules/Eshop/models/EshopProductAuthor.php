<?php

class EshopProductAuthor extends Model {
    protected $table = 'run_eshop_product_authors';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_eshop_products_id' => array('type' => 'int', 'index' => 'index'),
        'run_eshop_authors_id' => array('type' => 'int', 'index' => 'index'),
        'author_unified' => array('type' => 'bool', 'default' => 0, 'comment' => 'Has been the author unified?'),
        'sort' => array('type' => 'int', 'default' => null),
    );
}
