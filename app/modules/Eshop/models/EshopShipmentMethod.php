<?php

class EshopShipmentMethod extends EshopModel {
    protected $table = 'run_eshop_shipment_methods';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'pid' => array('type' => 'varchar', 'default' => null, 'index' => 'index'),
        'heureka_code' => array('type' => 'varchar', 'default' => null, 'length' => 25, 'comment' => 'Codes used in xml export file for heureka.sk'),
        'name' => array('type' => 'varchar'),
        'description' => array('type' => 'text', 'default' => null),
        'info' => array('type' => 'text', 'default' => null, 'comment' => 'Info hint text displayed on hovering info link'),
        'info_locator' => array('type' => 'varchar', 'default' => null, 'comment' => 'Slug, internal reference or an absolute URL to info page for given shipment method'),
        'price' => array('type' => 'decimal', 'length' => 8.2, 'default' => 0, 'null' => true, 'comment' => 'Shipment costs basic price. Can be changed by price alternatives. Use NULL if price has to be specified separately for each order'),
        'delivery_time' => array('type' => 'varchar', 'default' => null, 'comment' => 'Days of delivery time'),
        'products_total_price_alternatives' => array('type' => 'text', 'default' => null, 'comment' => 'Price alternatives according to order products total price, e.g. 50::2;75::1'),
        'package_weight_price_alternatives' => array('type' => 'text', 'default' => null, 'comment' => 'Price alternatives according to package weight, e.g. 4.5::3;5.5::4'),
        'free_shipment_allowed' => array('type' => 'bool', 'default' => 1, 'comment' => 'Allow setting EshopShipment.freeShipmentProductsTotal for this method?'),
        'pickup' => array('type' => 'bool', 'default' => 0, 'comment' => 'If 1 then client must come to some place to pickup the order. This should be 1 for all methods with pickup_places and also for method with pid localPickup'),
        'pickup_places' => array('type' => 'text', 'default' => null, 'comment' => 'New line separated list of pickup places to be displayed in checkout form'),
        //'country' => array('type' => 'char', 'length' => 2, 'default' => null, 'comment' =>'Country (ISO code 2) for which shipment method applies. If NULL then applicable for any coutry'), //@todo
        'sort' => array('type' => 'int', 'default' => null),
        'used_for_lang' => array('type' => 'varchar', 'length' => 5, 'default' => null, 'comment' => 'Lang version of eshop the shipmet method is used for'),
        'delivery_country' => array('type' => 'char', 'length' => 2, 'default' => null, 'comment' =>'Country ISO code 2. Delivery country the shipment method is used for. If NULL then it is up to implementation logic'),
        'list_color' => array('type' => 'varchar', 'length' => 20, 'default' => null, 'comment' => 'Hexadecimal code for RGB color to be used for shipment method in checkout list'),
        'active' => array('type' => 'bool', 'default' => 0),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
        'deleted' => array('type' => 'datetime', 'default' => null, 'index' => 'index'),        
    );
    
    protected $translatedFields = array(
        'name',
        'description',
        'info',
        'info_locator',
    );
    
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(                                 
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                ),
            ),            
        );
    }    
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
               
        if (!empty($data['payment_ids'])) {
            if (is_string($data['payment_ids'])) {
                $data['payment_ids'] = explode(';', $data['payment_ids']);
            }
            $data['payment_ids'] = array_filter(array_map('trim', $data['payment_ids']));
        }
        
        if (
            !empty($data['pickup_places'])
            || (
                !empty($data['pid'])
                && $data['pid'] === 'localPickup'
            )
        ) {
            $data['pickup'] = true;
        }
        
        // normalize default active value
        if (
            !array_key_exists('active', $data)
            && $options['on'] === 'create'
        ) {
            $data['active'] = false;
        }
        
        if (
            isset($data['price'])
            && $data['price'] !== ''
        ) {
            $data['price'] = str_replace(',', '.', $data['price']);
        }
        
        return parent::normalize($data, $options);
    }
    
    public function findAll($options = array()) {
        $defaults = array(
            'first' => false,
        );
        $options = array_merge($defaults, $options);
        // apply first
        $first = $options['first'];
        if ($first) {
            $options['first'] = false;
            $options['limit'] = 1;
        }
        
        // reserve tables (to avoid writes while reading data)
        $this->reserveTables('EshopShipmentMethod_findAll', array(
            'EshopShipmentMethod',
            'EshopShipmentPaymentMethod',
        )); 
        $options['key'] = 'EshopShipmentMethod.id';
        $shipments = $this->findList($options);
        $shipmentIds = array_keys($shipments);
        
        // get related ids
        $EshopShipmentPaymentMethod = $this->loadModel('EshopShipmentPaymentMethod', true);
        $payments = $EshopShipmentPaymentMethod->find(array(
            'fields' => array('run_eshop_shipment_methods_id', 'run_payment_methods_id'),
            'conditions' => array('run_eshop_shipment_methods_id' => $shipmentIds),
        ));
        foreach ($payments as $payment) {
            $shipmentId = $payment['run_eshop_shipment_methods_id'];
            if (empty($shipments[$shipmentId]['payment_ids'])) {
                $shipments[$shipmentId]['payment_ids'] = array();
            }
            $shipments[$shipmentId]['payment_ids'][] = $payment['run_payment_methods_id'];
        }
        $this->unreserveTables('EshopShipmentMethod_findAll'); 
        
        // return
        $shipments = array_values($shipments);
        
        if ($first) {
            $shipments = reset($shipments);
        }
        return $shipments;
    }
    
    /**
     * Saves:
     *      standard shipment method fields
     *      + habtm payment methods (ids are in payment_ids field)
     * 
     * @param array $data
     * @param array $options Model::save() options, e.g. 'lang' - probably the only used here.
     * 
     * @return bool|array Array of saved data on success and FALSE on validation or processing failure.
     * 
     * @throws Exception
     */
    public function saveAll($data, $options = array()) {        
        // update or insert
        $options['on'] = null; // to make Model::isCreation() resolve correctly
        $options['on'] = ($this->isCreation($data, $options)) ? 'create' : 'update';
        
        // normalize
        $data = $this->normalize($data);
        // turn normalization off for further processing
        $options['normalize'] = false;
        
        // validate
        if (!$this->validate($data, $options)) {
            return false;
        }
        // turn validation off for further processing
        $options['validate'] = false;
        
        try {
            $this->reserveTables('EshopShipmentMethod_saveAll', array(
                'EshopShipmentMethod',
                'EshopShipmentPaymentMethod',
            )); 
            DB::startTransaction('EshopShipmentMethod_saveAll');
            $result = $this->save($data, $options);
            if (!$result) {
                DB::rollbackTransaction('EshopShipmentMethod_saveAll');
                $this->unreserveTables('EshopShipmentMethod_saveAll'); 
                return false;
            }
            $shipmentId = $this->getPropertyId();
            $data = $result;
            // check for payment_ids (sent from admin extjs)
            if (isset($data['payment_ids'])) {
                $EshopShipmentPaymentMethod = $this->loadModel('EshopShipmentPaymentMethod', true);
                $EshopShipmentPaymentMethod->deleteBy('run_eshop_shipment_methods_id', $shipmentId);
                if (!empty($data['payment_ids'])) {
                    $records = array();
                    foreach ($data['payment_ids'] as $paymentId) {
                        $records[] = array(
                            'run_eshop_shipment_methods_id' => $shipmentId,
                            'run_payment_methods_id' => $paymentId
                        );
                    }
                    DB::insert(
                        $EshopShipmentPaymentMethod->getPropertyTable(), 
                        $records, 
                        array('multiple' => true)
                    );
                }
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopShipmentMethod_saveAll');
            $this->unreserveTables('EshopShipmentMethod_saveAll'); 
            throw new Exception($e->getMessage());
        }
        
        DB::commitTransaction('EshopShipmentMethod_saveAll');
        $this->unreserveTables('EshopShipmentMethod_saveAll'); 
        return $data;
    }
    
    /**
     * Checks if specified country has abroad delivery
     * 
     * @param string $contry Delivery country ISO code 2
     * 
     * @return bool
     */
    public function hasAbroadDelivery($contry) {
        return !empty($contry) && $this->getSetting('EshopShipment.specificAbroadDelivery')
            && ($homeCountry = $this->getSetting('address.country'))
            && strtoupper($contry) !== strtoupper($homeCountry);
    }
    
    /**
     * Returns actualy available country codes where the orders can be delivered.
     * 
     * NOTE: All available country codes can be adjusted in the code of this method.
     * 
     * @return array Returns associative array of actually availabe country codes,
     *      e.g.: array('SK' => 'SK', 'CZ' => 'CZ', 'AT' => 'AT', ...);
     */
    public function getCountryCodes() {
        static $countryCodes = null;
        
        if ($countryCodes === null) {
            // define here all available country codes of eshop in required order
            $availableCountryCodes = array(
                'SK', 
                'CZ', 
                'AT', 
                'DE',
                'HU',
                'PL', 
                'UA',
                'FR',
                'IT',
                'GB',
                'US',
            );
            $availableCountryCodes = array_combine(
                $availableCountryCodes, $availableCountryCodes
            );
            $countryCodes = $this->findList(array(
                'conditions' => array(
                    'active' => true,
                ),
                'key' => 'delivery_country',
                'fields' => array('delivery_country'),
            ));
            $homeCountry = $this->getSetting('address.country');
            if (array_key_exists('', $countryCodes)) {
                $countryCodes[$homeCountry] = $homeCountry;
            }
            $hasHomeCountry = !empty($countryCodes[$homeCountry]);
            if ($this->findFirst(array(
                'conditions' => array(
                    'active' => true,
                    'pid' => 'abroadDelivery'
                )
            ))) {
                $countryCodes = $availableCountryCodes;
                if (!$hasHomeCountry) {
                    unset($countryCodes[$homeCountry]);
                }
            }
            $countryCodes = array_intersect_key($availableCountryCodes, $countryCodes);
        }
        
        return $countryCodes;
    }
    
    /**
     * Checks if shipment method is a GEIS POINT according to provided $pid 
     * 
     * @param string $pid Shipment method pid
     * 
     * @return bool
     */
    public function isGeisPoint($pid) {
        return (substr($pid, 0, 9) === 'geisPoint');
    }
    
    /**
     * Loads (refreshs) list of available GEIS POINT pickup places from GEIS API
     * 
     * @param string|array $country Country ISO code 2 to get pickup places for,
     *          e.g. 'SK', 'CZ', 'PL'. Or array of such codes.
     * 
     * @throws Exception
     */
    public function loadGeisPointPickupPlaces($country) {
        $countries = array_map('strtoupper', (array)$country);
        App::loadVendor('Eshop', 'GeisPointSoapClient');
        $places = array();
        foreach ($countries as $country) {
            $response = GeisPointSoapClient::searchGP($country, '', '');
            $responseData = json_decode($response, true);
            if (
                empty($responseData)
                || !is_array($responseData)
            ) {
                throw new Exception(__e(__FILE__, 'GEIS POINT pickup places load failure: invalid response "%s"', $response));
            }
            if (isset($responseData[0]['error'])) {
                throw new Exception(__e(__FILE__, 'GEIS POINT pickup places load failure: %s', $responseData[0]['error']));
            }
            unset($response);
            // 'city', 'country', 'email', 'gpse', 'gpsn', 'holiday', 'idGP', 'idRegion', 
            // 'mapUrl', 'name', 'note', 'openiningHours', 'phone', 'photoUrl', 'postcode', 'street'
            $places[$country] = array();
            foreach($responseData as $item) {
                $places[$country][$item['idGP']] = array(
                    'id' => $item['idGP'],
                    'country' => $country,
                    'zip' => $item['postcode'],
                    'city' => $item['city'],
                    'street' => $item['street'],
                    'place' => $item['name'],
                    'url' => null,
                    'latitude' => $item['gpsn'],
                    'longitude' => $item['gpse'],
                );
            }
        }
//        foreach ($places as &$countryPlaces) { //debug
//            $countryPlaces = array_splice($countryPlaces, 0, 2); //debug
//        } //debug
//        unset($countryPlaces); //debug
        App::setSqlLogging(false);
        $this->setSetting('EshopShipmentMethod.geisPointPickupPlaces', json_encode($places));
        App::setSqlLogging();    
    }
    
    /**
     * Checks if shipment method is a Zasielkovna according to provided $pid 
     * 
     * @param string $pid Shipment method pid
     * 
     * @return bool
     */
    public function isZasielkovna($pid) {
        return (substr($pid, 0, 11) === 'zasielkovna');
    }
    
    /**
     * Loads (refreshs) list of available Zasielkovňa pickup places from Zasielkovňa API
     * 
     * @param string|array $country Country ISO code 2 to get pickup places for,
     *          e.g. 'SK', 'CZ', 'PL'. Or array of such codes.
     * @param array $options Following are available:
     *      - 'apiKey' (string) Zasielkovňa API key. Defaults to setting 'Eshop.EshopShipmentMethod.zasielkovnaApiKey'.
     * 
     * @throws Exception
     */
    public function loadZasielkovnaPickupPlaces($country, $options = array()) {
        $defaults = array(
            'apiKey' => $this->getSetting('EshopShipmentMethod.zasielkovnaApiKey'),
        );
        $options = array_merge($defaults, $options);
        if (empty($options['apiKey'])) {
            throw new Exception(__e(__FILE__, 'Zasielkovňa pickup places load failure: no API key provided'));
        }
        $countries = array_map('strtoupper', (array)$country);
        $url = 'http://www.zasielkovna.sk/api/v3/' . $options['apiKey'] . '/branch.json';
        $response = App::request($url);
        $responseData = json_decode($response, true);
        if (
            empty($responseData)
            || !is_array($responseData)
            || empty($responseData['data'])
        ) {
            throw new Exception(__e(__FILE__, 'Zasielkovňa pickup places load failure: invalid response "%s" for request URL "%s"', $response, $url));
        }
        unset($response);
        $places = array();
        foreach ($countries as $country) {
            $places[$country] = array();
        }
        foreach ($responseData['data'] as $item) {
            $itemCountry = strtoupper($item['country']);
            if (!isset($places[$itemCountry])) {
                continue;
            }
            $places[$itemCountry][$item['id']] = array(
                'id' => $item['id'],
                'country' => $itemCountry,
                'zip' => $item['zip'],
                'city' => $item['city'],
                'street' => $item['street'],
                'place' => $item['place'],
                'url' => $item['url'],
                'latitude' => $item['latitude'],
                'longitude' => $item['longitude'],
            );
        }
//        foreach ($places as &$countryPlaces) { //debug
//            $countryPlaces = array_splice($countryPlaces, 0, 2); //debug
//        } //debug
//        unset($countryPlaces); //debug
        App::setSqlLogging(false);
        $this->setSetting('EshopShipmentMethod.zasielkovnaPickupPlaces', json_encode($places));
        App::setSqlLogging();    
    }
    
    /**
     * Returns array of pickup places for specified $provider. Each place is represented by array
     * containing items: 'id', 'country', 'zip', 'city', 'street', 'place', 'url', 'latitude', 'longitude'.
     * Place ids are also used as keys in array.
     * 
     * @param string $provider Pickup places provide. One of 'zasielkovna', 'geisPoint'.
     * @param array $options Following options are available:
     *      - 'city' (string) Name of city
     *      - 'zip' (string) Postal code
     *      - 'country' (string|array) Country ISO code 2 to get pickup places for,
     *          e.g. 'SK', 'CZ', 'PL'. Or array of such codes. If empty (NULL) then
     *          all pickup places, from all loaded countries are returned.
     *          Defaults to setting Eshop.address.country (home country).
     *      - 'reload' (bool) If TRUE then provider places are reloaded for all
     *          already loaded countries and for countries specified in 'country'
     *          option. ATTENTION: If no countries are loaded yet (there are no pickup
     *          places) and no countries are specified in 'country' option then 
     *          nothing is reloaded (it means still no pickup places there). 
     *          Defaults to FALSE.
     * 
     * @return array The above descripted array of pickup places or an empty array
     *      if no pickup places has been found for provided options.
     */
    public function getPickupPlaces($provider, $options = array()) {
        $defaults = array(
            'city' => null,
            'zip' => null,
            'country' => $this->getSetting('address.country'),
            'reload' => false,
        );
        $options = array_merge($defaults, $options);
        $options['country'] = array_map('strtoupper', (array)$options['country']);
        $provider = strtolower($provider);
        if ($provider === 'zasielkovna') {
            $setttingName = 'EshopShipmentMethod.zasielkovnaPickupPlaces';
        }
        elseif ($provider === 'geispoint') {
            $setttingName = 'EshopShipmentMethod.geisPointPickupPlaces';
        }
        else {
            throw new Exception(__e(__FILE__, 'Invalid pickup places provider %s', $provider));
        }
        $places = json_decode($this->getSetting($setttingName), true);
        $missingCountries = false;
        foreach ($options['country'] as $country) {
            if (!isset($places[$country])) {
                $missingCountries = true;
                break;
            }
        }
        if ($missingCountries || $options['reload']) {
            $loadCountries = array();
            if ($places) {
                $loadCountries = array_keys($places);
            }
            if ($options['country']) {
                $loadCountries = array_merge($loadCountries, $options['country']);
            }
            $loadCountries = array_unique($loadCountries);
            if ($loadCountries) {
                if ($provider === 'zasielkovna') {
                    $this->loadZasielkovnaPickupPlaces($loadCountries);
                }
                elseif ($provider === 'geispoint') {
                    $this->loadGeisPointPickupPlaces($loadCountries);
                }
            }
            $places = json_decode($this->getSetting($setttingName), true);
        }
        $tmp = array();
        if  (!empty($options['country'])) {
            foreach ($options['country'] as $country) {
                if (!empty($places[$country])) {
                    $tmp += $places[$country];
                }
            }
        }
        else {
            foreach ($places as $countryPlaces) {
                $tmp += $countryPlaces;
            }            
        }
        $places = $tmp;
        if (
            !empty($options['city'])
            || !empty($options['zip'])
        ) {
            foreach($places as $id => $place) {
                if (
                    !empty($options['city'])
                    && $place['city'] !== $options['city']
                    || 
                    !empty($options['zip'])
                    && $place['zip'] !== $options['zip']
                ) {
                    unset($places[$id]);
                }
            }
        }
        return $places;
    }
    
    /**
     * Returns array of pickup place data for specified $provider. The array contains 
     * items: 'id', 'country', 'zip', 'city', 'street', 'place', 'url', 'latitude', 'longitude'.
     * 
     * @param string $provider Pickup places provide. One of 'zasielkovna', 'geisPoint'.
     * @param string|int $placeId Place id. Format can be provider specific
     * 
     * @return array|NULL The above descripted array of single pickup place or NULL
     *      if no pickup place has been found for provided $placeId.
     */
    public function getPickupPlace($provider, $placeId) {
        $places = $this->getPickupPlaces($provider, array('country' => null));
        if (isset($places[$placeId])) {
            return $places[$placeId];
        }
        return null;
    }
    
    /**
     * Calculates all suplementary prices and price infos from the supplied price data
     * and sets then in provided shipment array. Following prices are set:
     *      - 'shipment_price_taxed'
     *      - 'shipment_price_actual_taxed'
     *      - 'shipment_price_taxless'
     *      - 'shipment_price_actual_taxless'
     *      - 'shipment_tax'
     *      - 'shipment_tax_actual'
     * 
     * ATTENTION: If $shipment['price'] is NULL or '' empty string then all above 
     * mentioned new created items are also set to NULL (including $shipment['price']
     * normalized to NULL too).
     * 
     * @param array $shipment Array of shipment prices containing 'price', 'products_total_price_alternatives',
     *      'package_weight_price_alternatives'. Whole shipment array can be passed in.
     * @param array $options You may specify the following:
     *      - 'pricesAreTaxed' (bool) If TRUE then prices are considered to be provided
     *          as taxed. If FALSE then they are considered to be taxless. Defaults to NULL means
     *          that setting Eshop.pricesAreTaxed is loaded to found this.
     *      - 'taxRate' (float) Tax rate to be used to calculate taxed/taxless versions of price. 
     *          Defaults to NULL means that setting Eshop.defaultTaxRate is loaded to found this.
     *      - 'productsPrices' (array) Array of of order products prices (without shipment) to consider possible
     *          changes of shipment price. Array must contain 'products_price_actual_taxless' 
     *          and 'products_tax_actual' item. Defaults to NULL. 
     *      - 'packageWeight' (float) Total weight of package to consider possible
     *          changes of shipment price. Defaults to NULL. 
     *      - 'oversizedProducts' (bool) Are there oversized products to ship?
     *          Defaults to FALSE. 
     *      - 'precision' (int) All numbers will be rounded to this precision. Defaults to 2. 
     * 
     * @return array Array contaning suplementary prices and price infos.
     * 
     * @todo Unify 'discount_rate' and 'savings_rate' - 2 items for the same thing
     */
    public function getPrices($shipment, $options = array()) {
        $defaults = array(
            'pricesAreTaxed' => null,
            'taxRate' => null,
            'productsPrices' => null,
            'packageWeight' => null,
            'oversizedProducts' => false,
            'precision' => 2,
            
        );
        $options = array_merge($defaults, $options);
        if (
            $shipment['price'] === null 
            || $shipment['price'] === ''
            || $options['oversizedProducts']
        ) {
            $shipment['price'] = null;
            $shipment['shipment_price_taxed'] = null;
            $shipment['shipment_price_actual_taxed'] = null;
            $shipment['shipment_price_taxless'] = null;
            $shipment['shipment_price_actual_taxless'] = null;
            $shipment['shipment_tax'] = null;
            $shipment['shipment_tax_actual'] = null;
        }
        else {
            if ($options['pricesAreTaxed'] === null) {
                $options['pricesAreTaxed'] = $this->getSetting('pricesAreTaxed');
            }
            if ($options['taxRate'] === null) {
                $options['taxRate'] = $this->getSetting('defaultTaxRate');
            }
            // calculate prices and savings
            if ($options['pricesAreTaxed']) {
                // calculate taxed prices
                $shipment['shipment_price_taxed'] = round($shipment['price'], $options['precision']); 
                $shipment['shipment_price_actual_taxed'] = $this->getActualPrice($shipment, $options); 
                // calculate taxless prices
                $shipment['shipment_price_taxless'] = Number::getTaxlessPrice($shipment['price'], $options['taxRate'], $options['precision']);
                $shipment['shipment_price_actual_taxless'] = Number::getTaxlessPrice($shipment['shipment_price_actual_taxed'], $options['taxRate'], $options['precision']);
            } 
            else {
                // calculate taxless prices
                $shipment['shipment_price_taxless'] = round($shipment['price'], $options['precision']);
                $shipment['shipment_price_actual_taxless'] = $this->getActualPrice($shipment, $options);
                // calculate taxed prices
                $shipment['shipment_price_taxed'] = Number::getTaxedPrice($shipment['price'], $options['taxRate'], $options['precision']);
                $shipment['shipment_price_actual_taxed'] = Number::getTaxedPrice($shipment['shipment_price_actual_taxless'], $options['taxRate'], $options['precision']);
            }

            $shipment['shipment_tax'] = round($shipment['shipment_price_taxed'] - $shipment['shipment_price_taxless'], $options['precision']);
            $shipment['shipment_tax_actual'] = round($shipment['shipment_price_actual_taxed'] - $shipment['shipment_price_actual_taxless'], $options['precision']);
        }
        
        return $shipment;
    }   
    
    /**
     * Calculates shipment's actual price from the supplied price data taking 
     * into account 'products_total_price_alternatives' and 'package_weight_price_alternatives'.
     * 
     * @param array $shipment Array of shipment prices containing 'price', 'products_total_price_alternatives',
     *      'package_weight_price_alternatives'. Whole shipment array can be passed in.
     * @param array $options You may specify the following:
     *      - 'productsPrices' (array) Array of of order products prices (without shipment) to consider possible
     *          changes of shipment price. Array must contain 'products_price_actual_taxless' 
     *          and 'products_tax_actual' item. Defaults to NULL. 
     *      - 'packageWeight' (float) Total weight of package to consider possible
     *          changes of shipment price. Defaults to NULL. 
     *      - 'precision' (int) All numbers will be rounded to this precision. Defaults to 2. 
     * 
     * @return float Actual price rounded to provided precision.
     * 
     * @todo add the logic round 'products_total_price_alternatives' and 'package_weight_price_alternatives'
     */
    public function getActualPrice($shipment, $options = array()) {
        $defaults = array(
            'pricesAreTaxed' => null,
            'productsPrices' => null,
            'packageWeight' => null,
            'precision' => 2,
        );
        $options = array_merge($defaults, $options);
        if ($options['pricesAreTaxed'] === null) {
            $options['pricesAreTaxed'] = $this->getSetting('pricesAreTaxed');
        }
        
        $actualPrice = $shipment['price'];
        $acumulativeAdjustment = array();
        // change the shipment price according to actual products total
        if (!empty($options['productsPrices'])) {
            // get products total
            if ($options['pricesAreTaxed']) {
                $productsTotal = $options['productsPrices']['products_price_actual_taxless'] + $options['productsPrices']['products_tax_actual'];
            }
            else {
                // REPAIR. TAXES OF PRODUCTS MAY VARE. CALCUALTING MUST BE WITH VAT, IF PRICES ARE COMAPARE WITH VAT.
                // PRICE ALTERNATIVES MUST BE WITH VAT ALSO
                $productsTotal = $options['productsPrices']['products_price_actual_taxless'] + $options['productsPrices']['products_tax_actual'];
//                $productsTotal = $options['productsPrices']['products_price_actual_taxless'];            
            }
            // check the free shipment
            if (
                !empty($shipment['free_shipment_allowed'])
                && ($freeShipmentProductsTotal = $this->getSetting('EshopShipment.freeShipmentProductsTotal'))
                && $productsTotal >= $freeShipmentProductsTotal
            ) {
                return round(0.0, $options['precision']);
            }
            // apply 'products_total_price_alternatives'
            if (!empty($shipment['products_total_price_alternatives'])) {
                $totalAlternatives = $this->parseSelectiveAttribute($shipment['products_total_price_alternatives']);
                $totalAlternatives = $totalAlternatives['prices'];
                // reorder prices in descending order to make the folowing work
                krsort($totalAlternatives, SORT_NUMERIC);
                foreach ($totalAlternatives as $totalAlternative => $adjustment) {
                    if ($productsTotal >= $totalAlternative) {
                        $acumulativeAdjustment = $this->acumulatePriceAdjustments(
                            $actualPrice, $adjustment, $acumulativeAdjustment
                        );
                        break;
                    }
                }
            }
        }
        // change the shipment price according to actual package weight
        if (!empty($options['packageWeight'])) {
            // apply 'package_weight_price_alternatives'
            if (!empty($shipment['package_weight_price_alternatives'])) {
                $weightAlternatives = $this->parseSelectiveAttribute($shipment['package_weight_price_alternatives']);
                $weightAlternatives = $weightAlternatives['prices'];
                // reorder prices in descending order to make the folowing work
                krsort($weightAlternatives, SORT_NUMERIC);
                foreach ($weightAlternatives as $weightAlternative => $adjustment) {
                    if ($options['packageWeight'] >= $weightAlternative) {
                        $acumulativeAdjustment = $this->acumulatePriceAdjustments(
                            $actualPrice, $adjustment, $acumulativeAdjustment
                        );
                        break;
                    }
                }
            }
        } 
        
        $actualPrice = $this->adjustPrice($actualPrice, $acumulativeAdjustment);

        return round($actualPrice, $options['precision']);
    }

}