<?php

class EshopOrderProduct extends Model {
    protected $table = 'run_eshop_order_products';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'number' => array('type' => 'tinyint', 'comment' => 'Order item index number. First item in order has always number 1'),
        'parent_id' => array('type' => 'int', 'default' => null, 'index' => 'index', 'comment' => 'E.g. additional service product has here id of parent product it belongs to'),
        'run_eshop_orders_id' => array('type' => 'int', 'index' => 'index'),
        'run_eshop_products_id' => array('type' => 'int', 'index' => 'index'),
        'amount' => array('type' => 'int'),
        'stock' => array('type' => 'int', 'default' => 0, 'comment' => 'Product stock amount at the order time'),
        'reserved_amount' => array('type' => 'int', 'comment' => 'Current reserved amount of ordered product units. Equals the lower one of amount and stock values at order time. Later actualized after each stock import. Used to keep user informed about his/her order.'),
        'availability' => array('type' => 'varchar', 'comment' => 'Product availability at the order time'),
        'price_taxless' => array('type' => 'decimal', 'length' => 8.2, 'comment' => 'Product price without VAT before discount'),
        'tax' => array('type' => 'decimal', 'length' => 8.2, 'comment' => 'Product VAT before discount'),
        'price_actual_taxless' => array('type' => 'decimal', 'length' => 8.2, 'comment' => 'Product price without VAT after discount'),
        'tax_actual' => array('type' => 'decimal', 'length' => 8.2, 'comment' => 'Product VAT after discount'),
        'discount_rate' => array('type' => 'decimal', 'length' => 8.2, 'default' => null, 'comment' => 'Discount percentage'),
        'tax_rate' => array('type' => 'int', 'comment' => 'Product VAT percentage on order time'),
        'eshop_voucher_discount_rate' => array('type' => 'decimal', 'length' => 8.2, 'default' => null, 'comment' => 'Discount rate of applied percentage discount voucher'),
        'eshop_voucher_code' => array('type' => 'varchar', 'length' => 255, 'default' => null, 'index' => 'index', 'comment' => 'Applied absolute discount voucher code. If set then run_eshop_products_id must equal to setting Eshop.EshopProduct.voucherProductId'), 
        'static_attributes' => array('type' => 'text', 'comment' => 'JSON encoded list of product static attributes names and their coresponding values'),
        'dynamic_attributes' => array('type' => 'text', 'comment' => 'JSON encoded list of product dynamic attributes ids and their coresponding values'),
        'bestseller_relevancy' => array('type' => 'float', 'default' => 0,  'comment' => 'Used to order products by bestselling. Value of this field should be calculated daily by CRON like "EshopOrderProduct.amount * POW(10, -DATEDIFF(CURDATE(), DATE(EshopOrder.created))/X)" where X is number of days to divide relevancy by 10.'),
        // These data are needed to show order product details in order view/edit on frontend or backend. 
        // They can be populated anytime but if not earlier then they MUST be populated by the cleap up logic, e.g. in Tool::cleanUpEshopOldProducts()! 
        // Their content can be projet specific, e.g. array('name' => '???', ..., 'EshopManufacturer' => array('name' => '???', ...)),
        // but the most bulletproof way to populate them is by EshopProduct::getDetails(), it means everything is there.
        // The usual place where they are used (decoded) is EshopOrderProduct::getDetails().
        'cleaned_up_product_data' => array('type' => 'text', 'default' => null, 'comment' => 'JSON encoded data of the original cleaned up (it means definitely removed from DB) product'),
    );
    
////mojo: uncomment this when EshopOrders::admin_edit() will be refactored (see todo151207)     
//// and prepared to treat validations       
//    public function __construct() {
//        parent::__construct();
//        
//        $this->validations = array(
//            'amount' => array(
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Amount is required'),
//                    'on' => 'create',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Amount must not be empty'),
//                ),
//                array(
//                    'rule' => 'intValue',
//                    'message' => __v(__FILE__, 'Amount must be an integer value'),
//                ),
//            ),
//        );
//    }    
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        
        if (
            isset($data['reserved_amount'])
            && $data['reserved_amount'] < 0
        ) {
            $data['reserved_amount'] = 0;
        }
        
        if (
            array_key_exists('static_attributes', $data) 
            && (
                empty($data['static_attributes'])
                || is_array($data['static_attributes'])
            )
        ) {
            $data['static_attributes'] = json_encode((array)$data['static_attributes']);
        }
        
        if (
            array_key_exists('dynamic_attributes', $data) 
            && (
                empty($data['dynamic_attributes'])
                || is_array($data['dynamic_attributes'])
            )
            
        ) {
            $data['dynamic_attributes'] = json_encode((array)$data['dynamic_attributes']);
        }
        
        return parent::normalize($data, $options);
    }
    
    /**
     * Returns order products details. Retruned product details are identic to 
     * EshopProduct::getDetails() output + following fields are set according 
     * stored values of ordered products:
     *      - amount
     *      - price_actual_taxed
     *      - price_taxed
     *      - total_price_actual_taxless
     *      - total_price_taxed
     *      - total_price_actual_taxed
     *      - total_price_taxed
     *      - order_item_number
     *      - disponibility
     * 
     * Product prices are set according to prices they heve been ordered for.
     * 
     * Following models are attached to each product:
     * - 'EshopVoucher' but only if $options['getGiftCardVouchers'] is not FALSE
     * 
     * ATTENTION: Be aware that the same product (accordint to id) can occure more than once
     * in the output products list! Each occurrence is distinguished by attribute
     * values from others. Array keys are set to EshopOrderProduct.id, which is stored under
     * 'id' key too. EshopProduct.id is stored under key 'run_eshop_products_id'.
     * 
     * @param int|array $id Single order product id or an array of such ids
     * @param array $options Options passed to internal call of EshopProduct::getDetails()
     *      plus following:
     *          - 'getActualAvailability' (bool) If TRUE then 'stock', 'availability' and
     *              'disponibility' contain actual values. Otherwise they contain
     *              values recorded at the order time. Defaults to FALSE.
     *          - 'parseAttributes' (bool) If TRUE then json encoded attributes in static_attributes
     *              and dynamic_attributes are parsed and compiled into compehensible strings
     *              by which the values of fields static_attributes and dynamic_attributes
     *              are replaced. Defaults to FALSE.
     *          - 'getGiftCardVouchers' (bool|array) If TRUE then gift card vouchers are populated. 
     *              If an array then passed as options to EshopProduct::getGiftCardVouchers() method. 
     *              Defaults to FALSE.
     *           
     * @return array Array of order products detailed records if array of ids is provided
     *      on input with keys set to set to EshopOrderProduct.id. Single order product detailed 
     *      record if single integer id is passed on input.
     * 
     * @todo - savings rate and savings are not set here.
     */
    public function getDetails($id, $options = array()) {
        $options = array_merge(array(
            'getActualAvailability' => false,
            'parseAttributes' => false,
            'getGiftCardVouchers' => false,
        ), $options);
        
        $joins = array(
            array(
                'type' => 'left',
                'model' => 'EshopOrder',
            )
        );
        $fields = $this->getFields(array('qualify' => true));
        $fields[] = 'CONCAT(EshopOrder.number, "-", EshopOrderProduct.number) AS order_item_number';
        $separate = array();
                
        $orderProducts = $this->findList(array(
            'joins' => $joins,
            'conditions' => array(
                'EshopOrderProduct.id' => $id,
            ),
            'key' => 'EshopOrderProduct.id',
            'fields' => $fields,
            'literals' => array(
                'fields' => array(
                    'CONCAT(EshopOrder.number, "-", EshopOrderProduct.number) AS order_item_number',
                )
            ),
            'order' => 'EshopOrderProduct.id ASC',
            'separate' => $separate,
        ));
        
        if (!$orderProducts) {
            return array();
        }        
        
        // get gift card vouchers
        if ($options['getGiftCardVouchers']) {
            $giftCardVouchers = $this->getGiftCardVouchers($id, (array)$options['getGiftCardVouchers']);
        }
        
        // get order product ids
        $orderProductIds = array();
        $productIds = array();
        $amounts = array();
        foreach ($orderProducts as $orderProduct) {
            $orderProductIds[] = $orderProduct['id'];
            $productIds[] = $orderProduct['run_eshop_products_id'];
            $amounts[$orderProduct['run_eshop_products_id']] = $orderProduct['amount'];
        }
        $productIds = array_unique($productIds);
        // get products details
        $Product = $this->loadModel('EshopProduct', true);
        $options['amount'] = $amounts;
        $options['ignoreSoftDeleted'] = false;
        $products = $Product->getDetails($productIds, $options);
        // get order product details (there can be more products with the same id but distinguished by attributes)
        foreach ($orderProducts as &$orderProduct) {
            if (!empty($products[$orderProduct['run_eshop_products_id']])) {
                $product = $products[$orderProduct['run_eshop_products_id']];
            }
            elseif (!empty($orderProduct['cleaned_up_product_data'])) {
                $product = (array)json_decode($orderProduct['cleaned_up_product_data'], true);
            }
            else {
                $product = array(
                    'name' => __(__FILE__, 'Neznámy zmazaný produkt'),
                    'stock' => 0,
                    'availability' => 'enum_soldout',
                );
            }
            $orderProduct = array_merge($product, $orderProduct);
            // get gift card vouchers
            if ($options['getGiftCardVouchers']) {
                $orderProduct['EshopVoucher'] = (array)Sanitize::value($giftCardVouchers[$orderProduct['id']]);
            }
            // add voucher code to absolute discount voucher
            if ($orderProduct['eshop_voucher_code']) {
                $orderProduct['name'] = 
                    trim($orderProduct['name']) . ' ' . $orderProduct['eshop_voucher_code'];
            }
            if (
                $orderProduct['parent_id']
                && isset($orderProducts[$orderProduct['parent_id']])
            ) {
                $orderProduct['name'] .= __(
                    __FILE__, 
                    ' (k položke č. %s)', 
                    $orderProducts[$orderProduct['parent_id']]['number']
                );
            }
            // get taxed prices
            $orderProduct['price_actual_taxed'] = $orderProduct['price_actual_taxless'] + $orderProduct['tax_actual'];
            $orderProduct['price_taxed'] = $orderProduct['price_taxless'] + $orderProduct['tax'];
            // get product total prices
            $orderProduct['total_price_actual_taxless'] = $orderProduct['price_actual_taxless'] * $orderProduct['amount'];
            $orderProduct['total_price_taxed'] = $orderProduct['price_taxed'] * $orderProduct['amount'];
            $orderProduct['total_price_actual_taxed'] = $orderProduct['price_actual_taxed'] * $orderProduct['amount'];
            $orderProduct['total_price_taxed'] = $orderProduct['price_taxed'] * $orderProduct['amount'];
            if ($options['getActualAvailability']) {
                $orderProduct['stock'] = $product['stock'];
                $orderProduct['availability'] = $product['availability'];
            }
            // recalculate disponibility
            $orderProduct['disponibility'] = $Product->getDisponibility($orderProduct);
            if ($options['parseAttributes']) {
                $dynamicAttrs = json_decode($orderProduct['dynamic_attributes']);
                $orderProduct['dynamic_attributes'] = '';
                if (!empty($dynamicAttrs)) {
                    foreach ($dynamicAttrs as $attrName => $attrValue) {
                        $orderProduct['dynamic_attributes'] .= $attrName . ':' . $attrValue . ';';
                    }
                    $orderProduct['dynamic_attributes'] = rtrim($orderProduct['dynamic_attributes'], ';');
                }
                $staticAttrs = json_decode($orderProduct['static_attributes']);
                $orderProduct['static_attributes'] = '';
                if (!empty($staticAttrs)) {
                    foreach ($staticAttrs as $attrName => $attrValue) {
                        if ($attrName !== 'variant') {
                            $orderProduct['static_attributes'] .= $attrName . ':' . $attrValue . ';';
                        }
                        else {
                            $orderProduct['static_attributes'] .= $attrValue . ';';
                        }
                    }
                    $orderProduct['static_attributes'] = rtrim($orderProduct['static_attributes'], ';');
                }
            }
        }
        unset($orderProduct);
        
        if (
            !empty($orderProducts)
            && !is_array($id)
        ) {
            $orderProducts = reset($orderProducts);
        }
        return $orderProducts;
    }   
    
    /**
     * Returns total weight of order products
     * 
     * @param array $products Output of EshopCart::getProductsDetails() method
     * 
     * @return float Total cart weight
     */
    public function getWeight($products) {
        $weight = 0.0;
        foreach($products as $product) {
            if (empty($product['weight'])) {
                continue;
            }
            $weight += (float)$product['weight'] * $product['amount'];
        }
        return $weight;
    }
    
    /**
     * Updates reserved amount of products of new and opened orders according to 
     * products reservations on MRP stock. MRP stock reserved quantity is read from 
     * EshopProduct table and its actuality depends on regular update of products stock from MRP.
     * 
     * @return int Number of updated order products
     */
    public function updateReservedAmount() {
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        $liveOrderStatuses = array_keys($Order->getPropertyLiveStatuses());
        $orderProducts = $this->find(array(
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'EshopOrder',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopProduct',
                )
            ),
            'conditions' => array(
                'EshopOrder.status' => $liveOrderStatuses,
                'EshopOrder.exported !=' => null,
            ),
            'fields' => array(
                'EshopProduct.id',
                'EshopProduct.stock_reserved',
                'EshopOrderProduct.id',
                'EshopOrderProduct.amount',
            ),
            'order' => 'EshopOrder.created',
            'separate' => true,
        ));
        $products = array();
        $updateBatch = array();
        $updatedCount = 0;
        foreach($orderProducts as $orderProduct) {
            $productId = $orderProduct['EshopProduct']['id'];
            $stockReserved = $orderProduct['EshopProduct']['stock_reserved'];
            $orderProductId = $orderProduct['EshopOrderProduct']['id'];
            $orderProductAmount = $orderProduct['EshopOrderProduct']['amount'];
            if (!isset($products[$productId])) {
                $products[$productId] = $stockReserved;
            }
            if ($products[$productId] > 0) {
                $orderProductReservedAmount = ($products[$productId] < $orderProductAmount) 
                    ? $products[$productId] : $orderProductAmount;
                $products[$productId] -= $orderProductReservedAmount;
                $updateBatch[] = array(
                    'id' => $orderProductId,
                    'reserved_amount' => $orderProductReservedAmount,
                );
                $updatedCount++;
            }
        }
        $this->saveBatch(array(
            'update' => array(
                'EshopOrderProduct' => &$updateBatch,
            ),
        ));
        return $updatedCount;
    }
    
    /**
     * Creates vouchers (with absolute discount) for bought gift cards in following way:
     * - printed gift card vouchers are created after order creation (no other conditions are regured)
     * - electronic gift card vouchers are created (and sent to user) after order is paid
     */
    public function createGiftCardVouchers() {
        
        // !!! keep this is synchro with the app/config/database.php > 'tableReservationTimeLimit' => ...
        set_time_limit(600);
        
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $giftCardIds = $Product->findList(array(
            'conditions' => array(
                'is_gift_card' => true,
            ),
            'fields' => array('id'),
        ));
        if (!$giftCardIds) {
            return;
        }
        $orderedGiftCardsWithoutVoucher = $this->find(array(
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'EshopOrder',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopOrderGiftCardProductVoucher',
                ),
            ),
            'conditions' => array(
                'EshopOrderProduct.run_eshop_products_id' => $giftCardIds,
                'EshopOrderGiftCardProductVoucher.id' => null,
            ),
            'fields' => array(
                'EshopOrderProduct.id',
                'EshopOrderProduct.amount',
                'EshopOrderProduct.price_taxless',
                'EshopOrderProduct.tax',
                'EshopOrderProduct.static_attributes',
                'EshopOrder.payment_status',
            )
        ));
        $this->loadModel('EshopVoucher');
        $Voucher = new EshopVoucher();
        $this->loadModel('EshopOrderGiftCardProductVoucher');
        $GiftCardVoucher = new EshopOrderGiftCardProductVoucher();
        $actualCurrencyDecimals = (int)Eshop::getActualCurrency('decimals');
        $activeFrom = date('Y-m-d');
        $activeTo = date('Y-m-d', strtotime('+' . $this->getSetting('EshopOrderProduct.giftCardExpirationDays') . ' days'));
        $microtimeReserve = 10000;
        foreach ($orderedGiftCardsWithoutVoucher as $giftCard) {
            if (App::getFreeMicrotime() < $microtimeReserve) {
                break;
            }
            $giftCard['static_attributes'] = json_decode($giftCard['static_attributes'], true);
            if (empty($giftCard['static_attributes']['variant'])) {
                App::logError(__e(__FILE__, 'Invalid ordered gift card - missing static attribute variant'), array(
                    'var' => $giftCard, 
                    'email' => true,
                ));
                continue; 
            }
            // in case of electronic gift card create voucher (and send it to user)
            // only when the order is paid, otherwise skip it
            if (
                $giftCard['static_attributes']['variant'] === 'electronic'
                && $giftCard['payment_status'] !== 'enum_payment_paid'
            ) {
                continue;
            }
            // create as many vouchers as many gift cards have been ordered
            for ($i = 0; $i < $giftCard['amount']; $i++) {
                // create the voucher
                try {
                    DB::startTransaction('EshopOrderProduct::createGiftCardVouchers()');
                    $voucher = array(
                        'code' => $Voucher->getUniqueCode(),
                        'discount' => round($giftCard['price_taxless'] + $giftCard['tax'], $actualCurrencyDecimals),
                        'applications_limit' => 1,
                        'active_from' => $activeFrom,
                        'active_to' => $activeTo,
                        'active' => true,
                    );
                    if (!($savedVoucher = $Voucher->save($voucher))) {
                        DB::rollbackTransaction('EshopOrderProduct::createGiftCardVouchers()');
                        App::logError(__e(__FILE__, 'Creation of gift card voucher has failed on validation'), array(
                            'var' => array(
                                'voucher' => $voucher,
                                'errors' => $Voucher->getErrors(),
                            ),
                            'email' => true,
                        ));
                        continue;
                    }
                    $GiftCardVoucher->save(
                        array(
                            'run_eshop_order_products_id' => $giftCard['id'],
                            'run_eshop_vouchers_id' => $savedVoucher['id'],
                        ),
                        array(
                            'normalize' => false,
                            'validate' => false,
                        )
                    );
                    DB::commitTransaction('EshopOrderProduct::createGiftCardVouchers()');
                } 
                catch (Exception $e) {
                    DB::rollbackTransaction('EshopOrderProduct::createGiftCardVouchers()');
                    App::logError(__e(__FILE__, 'Creation of gift card voucher has failed on exception'), array(
                        'var' => $e,
                        'email' => true,
                    ));
                    continue;
                }
                // for electronic gift cards send voucher to user
                if ($giftCard['static_attributes']['variant'] === 'electronic') {
                    try {
                        if (!$this->sendGiftCardVoucherEmail($savedVoucher['id'])) {
                            App::logError(__e(__FILE__, 'Send of electronic gift card voucher has failed '), array(
                                'email' => true,
                            ));
                        }
                    } 
                    catch (Exception $e) {
                        App::logError(__e(__FILE__, 'Send of electronic gift card voucher has failed on exception'), array(
                            'var' => $e,
                            'email' => true,
                        ));
                    }
                }
            }
        }
    }
    
    /**
     * Sends email with voucher code of ordered gift card.
     * 
     * It is used to send electronic gift card vouchers but it can be used also
     * for printed gift cards - there is no internal restriction, just check the 
     * formulation of email subject and body (settings Eshop.EshopOrderProduct.msgSubjectGiftCardVoucher
     * and Eshop.EshopOrderProduct.msgBodyGiftCardVoucher)
     * 
     * @param int $voucherId
     * 
     * @return bool FALSE if specified voucher id does not exist or is not active or
     *          if it is not paired to an ordered gift card or if the email send fails.
     */
    public function sendGiftCardVoucherEmail($voucherId) {
        $this->loadModel('EshopOrderGiftCardProductVoucher');
        $GiftCardVoucher = new EshopOrderGiftCardProductVoucher();
        $giftCard = $GiftCardVoucher->findFirstBy(
            'EshopOrderGiftCardProductVoucher.run_eshop_vouchers_id',
            $voucherId, 
            array(
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopOrderProduct',
                    ),
                    array(
                        'type' => 'left',
                        'model' => 'EshopProduct',
                        'toModel' => 'EshopOrderProduct'
                    ),
                    array(
                        'type' => 'left',
                        'model' => 'EshopVoucher',
                    ),
                ),
                'fields' => array(
                    'EshopOrderProduct.run_eshop_orders_id',
                    'EshopProduct.name',
                    'EshopVoucher.code',
                ),
                'separate' => array('EshopProduct', 'EshopVoucher'),
            )
        );
        $this->loadModel('EshopVoucher');
        $Voucher = new EshopVoucher();
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        if (
            !$giftCard
            || !$giftCard['run_eshop_orders_id']
            || empty($giftCard['EshopVoucher']['code'])
            || !($voucher = $Voucher->getActiveByCode($giftCard['EshopVoucher']['code']))
            || (
                empty($voucher['discount_rate'])
                && empty($voucher['discount'])
            )
            || !($inserts = $Order->getInserts($giftCard['run_eshop_orders_id']))
        ) {
            return false;
        }
        // add specific inserts to order inserts
        $inserts['giftCardName'] = $giftCard['EshopProduct']['name'];
        $inserts['giftCardCode'] = $voucher['code'];
        if ($voucher['discount_rate']) {
            $inserts['giftCardDiscount'] = floor($voucher['discount_rate']) . ' %';
        }
        else {
            $inserts['giftCardDiscount'] = Eshop::formatPrice($voucher['discount']);
        }
        $inserts['giftCardActiveTo'] = Date::format($voucher['active_to'], 'j.n.Y');
        // send it
        $subject = $this->getSetting('EshopOrderProduct.msgSubjectGiftCardVoucher');
        $body = $this->getSetting('EshopOrderProduct.msgBodyGiftCardVoucher');            
        try {
            $result = (bool)App::sendEmail(
                $body, 
                $inserts['userEmail'], 
                array(
                    'subject' => $subject,
                    'from' => array(
                        $this->getSetting('email.from') => App::getSetting('App', 'name')
                    ),
                    'cc' => $this->getSetting('email.cc'),
                    'bcc' => array_filter(array_map('trim', explode(',', $this->getSetting('email.bcc')))),
                    'inserts' => $inserts,
                    'embedImages' => true,
                )
            );
        }
        catch (Throwable $e) {
            App::logError(__e(__FILE__, 'Send of gift card voucher email has failed'), array(
                'var' => $e,
                'email' => true,
            ));
            $result = false;
        }
        return $result;
    }
    
    /**
     * Sends emails for all gift cards with expiring vouchers
     * 
     * @param array $options See the method body
     */
    public function sendExpiringGiftCardVoucherEmails($options = array()) {
        $defaults = array(
            'minMicrotimeReserve' => 5000,
            'domainBatchSize' => 10, //5,
            'batchInterval' => 7,    //1,
            'smtpBatchSize' => 1,
            'perMinuteBatchSize' => 200,
        );
        $options = array_merge($defaults, $options);

        try {
            App::reserveProcessing('EshopOrderProduct::sendExpiringGiftCardVoucherEmails()', array(
                'tries' => 1
            ));
        }
        catch (Exception_App_ProcessingReservationFailure $e) {
            return;
        }
        $lastBatchTimestamp = $this->getSetting('EshopOrderProduct.sendExpiringGiftCardVoucherEmails.lastBatchTimestamp');
        if (!ON_LOCALHOST && time() < $lastBatchTimestamp + $options['batchInterval'] * 60) {
            App::unreserveProcessing('EshopOrderProduct::sendExpiringGiftCardVoucherEmails()');
            return;
        }
        $this->loadModel('EshopOrderGiftCardProductVoucher');
        $GiftCardVoucher = new EshopOrderGiftCardProductVoucher();
        $this->loadModel('EshopVoucher');
        $Voucher = new EshopVoucher();
        $giftCardExpiringEmailDays = abs($this->getSetting('EshopOrderProduct.giftCardExpiringEmailDays'));
        $giftCards = $GiftCardVoucher->find(array(
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'EshopOrderProduct',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopProduct',
                    'toModel' => 'EshopOrderProduct',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopVoucher',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopOrder',
                    'toModel' => 'EshopOrderProduct',
                ),
            ),
            'conditions' => array(
                'EshopVoucher.expiring_email_success' => false,
                EshopVoucher::getActiveConditions(),
                'DATEDIFF(`EshopVoucher`.`active_to`, NOW()) <= ' . $giftCardExpiringEmailDays,
            ),
            'fields' => array(
                'EshopOrderProduct.id',
                'EshopOrderProduct.run_eshop_orders_id',
                'EshopProduct.name',
                'EshopVoucher.id',
                'EshopVoucher.code',
                'EshopVoucher.discount_rate',
                'EshopVoucher.discount',
                'EshopVoucher.active_to',
                'EshopOrder.email',
            ),
            'literals' => array(
                'conditions' => array_merge(
                    array(
                        'DATEDIFF(`EshopVoucher`.`active_to`, NOW()) <= ' . $giftCardExpiringEmailDays,
                    ),
                    EshopVoucher::getActiveConditions(array('literals' => true))
                ),
            ),
            'separate' => array('EshopProduct', 'EshopVoucher', 'EshopOrder'),
        ));
        if ($giftCards) {
            $this->loadModel('EshopOrder');
            $Order = new EshopOrder();
            $subject = $this->getSetting('EshopOrderProduct.msgSubjectExpiringGiftCardVoucher');
            $body = $this->getSetting('EshopOrderProduct.msgBodyExpiringGiftCardVoucher');
            $updateBatch = array();
            $domainCounts = array();
            // send the message one by one
            $loopMaxTime = $options['minMicrotimeReserve']; // start with some minimal time reserve
            if ($options['perMinuteBatchSize']) {
                $thisMinuteBatchSize = $options['perMinuteBatchSize'];
                $thisMinuteEndTimestamp = time() + 60;
            }
            $sleepTime = 0;
            foreach ($giftCards as $giftCard) {
                $loopStartTime = App::getElapsedMicrotime();
                // check domain count
                $domain = explode('@', $giftCard['EshopOrder']['email']);
                $domain = end($domain);
                if (!isset($domainCounts[$domain])) {
                    $domainCounts[$domain] = 1;
                }
                else {
                    $domainCounts[$domain]++;
                }
                if ($domainCounts[$domain] > $options['domainBatchSize']) {
                    continue;
                }
                // send the email
                $inserts = $Order->getInserts($giftCard['run_eshop_orders_id']);
                // add specific inserts to order inserts
                $inserts['giftCardName'] = $giftCard['EshopProduct']['name'];
                $inserts['giftCardCode'] = $giftCard['EshopVoucher']['code'];
                if ($giftCard['EshopVoucher']['discount_rate']) {
                    $inserts['giftCardDiscount'] = floor($giftCard['EshopVoucher']['discount_rate']) . ' %';
                }
                else {
                    $inserts['giftCardDiscount'] = Eshop::formatPrice($giftCard['EshopVoucher']['discount']);
                }
                $inserts['giftCardActiveTo'] = Date::format($giftCard['EshopVoucher']['active_to'], 'j.n.Y');
                try {
                    $result = (bool)App::sendEmail(
                        $body,
                        $inserts['userEmail'],
                        array(
                            'subject' => $subject,
                            'from' => array(
                                $this->getSetting('email.from') => App::getSetting('App', 'name')
                            ),
                            'cc' => $this->getSetting('email.cc'),
                            'inserts' => $inserts,
                        )
                    );
                }
                catch (Throwable $e) {
                    App::logError('Send of gift card voucher email has failed', array(
                        'var' => $e,
                        // if mail sending failed so do not try to send mail
                        'email' => false,
                    ));
                    $result = false;
                }
                // if the email has succeded then keep track of it
                if ($result) {
                    $updateBatch[] = array(
                        'id' => $giftCard['EshopVoucher']['id'],
                        'expiring_email_success' => 1,
                    );
                }
                // check the time
                $loopTime = App::getElapsedMicrotime() - $loopStartTime + $sleepTime;
                if ($loopTime > $loopMaxTime) {
                    $loopMaxTime = $loopTime;
                }
                // if time is critical
                if (App::getFreeMicrotime() < $loopMaxTime) {
                    break;
                }
                // if perMinuteBatchSize is specified then sleep to not overcome it
                $sleepStartTime = App::getElapsedMicrotime();
                if ($options['perMinuteBatchSize']) {
                    $timestamp = time();
                    if ($timestamp >= $thisMinuteEndTimestamp) {
                        $thisMinuteEndTimestamp += 60;
                        $thisMinuteBatchSize = $options['perMinuteBatchSize'];
                    }
                    $sleepMicrotime = ($thisMinuteEndTimestamp - $timestamp) * 1000000;
                    if ($thisMinuteBatchSize > 0) {
                        $sleepMicrotime =  $sleepMicrotime / $thisMinuteBatchSize;
                    }
                    usleep($sleepMicrotime);
                    $thisMinuteBatchSize--;
                }
                $sleepTime = App::getElapsedMicrotime() - $sleepStartTime;
            }
            if ($updateBatch) {
                $Voucher->saveBatch(array(
                    'update' => array(
                        'EshopVoucher' => $updateBatch,
                    ),
                ));
            }
        }
        $this->setSetting('EshopOrderProduct.sendExpiringGiftCardVoucherEmails.lastBatchTimestamp', time());
        App::unreserveProcessing('EshopOrderProduct::sendExpiringGiftCardVoucherEmails()');
    }    
    
    /**
     * Gets gift card(s) vouchers
     * 
     * @param int|array $id Ordered gift card product id or an array of such ids
     * @param array $options Optional. Find options to precise the retrieved
     *      gift card(s) vouchers
     * 
     * @return array
     */
    public function getGiftCardVouchers($id, $options = array()) {
        $defaults = array(
            'key' => 'EshopOrderGiftCardProductVoucher.run_eshop_order_products_id',
            'fields' => array(
                'EshopVoucher.id',
                'EshopVoucher.code',
                'EshopVoucher.printed',
            ),
            'conditions' => array(),
            'joins' => array(),
        );
        $options = array_merge($defaults, $options);
        // force following options
        $options['conditions'] = DB::nestConditions($options['conditions']);
        $options['conditions']['EshopOrderGiftCardProductVoucher.run_eshop_order_products_id'] = $id;
        $options['joins'][] = array(
            'model' => 'EshopOrderGiftCardProductVoucher',        
            'type' => 'left',        
        );
        $options['accumulate'] = true;
        $options['plain'] = false;
        // find
        $Voucher = $this->loadModel('EshopVoucher', true);
        $vouchers = $Voucher->findList($options);
        return $vouchers;
    }
}