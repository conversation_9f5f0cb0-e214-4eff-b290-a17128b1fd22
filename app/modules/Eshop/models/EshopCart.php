<?php

class EshopCart extends Model {

    protected $schema = array(
        'id' => array('type' => 'int', 'comment' => 'Cart product id'),
        'amount' => array('type' => 'int', 'comment' => 'Cart product amount'),
        'static_attributes' => array('type' => 'int', 'comment' => 'Cart product static attributes array'),
        'dynamic_attributes' => array('type' => 'int', 'comment' => 'Cart product dynamic attributes array'),
    );

    /**
     * Constant used in output array of EshopCart::updateProduct() for products which
     * were removed from cart as they are no more available.
     */
    const PRODUCT_REMOVED = 'removed';

    /**
     * Constant used in output array of EshopCart::updateProduct() for products which
     * were not accesible in required amount and so their amounts were adjusted.
     */
    const PRODUCT_AMOUNT_ADJUSTED = 'adjusted';

    /**
     * Constant used in EshopCart::$adjustedProducts for discounted products whose special
     * offers appication has been changed by method EshopCart::applySpecialOffers()?
     */
    const PRODUCT_SPECIAL_OFFER_APPLICATION_ADJUSTED = 'specialOfferApplicationAdjusted';

    /**
     * List of adjusted products set by EshopCart::getOrderableProducts() method.
     * The structure of array is following:
     *
     *      array(
     *          '{cartIndex01}' => array(
     *              'id' => '{productId01}', 
     *              'adjustment' => EshopCart::PRODUCT_REMOVED
     *          ),
     *          '{cartIndex02}' => array(
     *              'id' => '{productId02}', 
     *              'adjustment' => EshopCart::PRODUCT_AMOUNT_ADJUSTED
     *          ),
     *          // if there are also occurrence items in cart then the structure is following
     *          '{cartIndex03}' => array(
     *              'id' => '{productId03}', 
     *              'run_eshop_product_occurrences_id' => '{productOccurrenceId01}', 
     *              'adjustment' => EshopCart::PRODUCT_REMOVED
     *          ),
     *          '{cartIndex04}' => array(
     *              'id' => '{productId04}', 
     *              'run_eshop_product_occurrences_id' => '{productOccurrenceId02}', 
     *              'adjustment' => EshopCart::PRODUCT_AMOUNT_ADJUSTED
     *          ),
     *          ...
     *      )
     *
     * @var array
     */
    protected $adjustedProducts = array();

    /**
     * Array of voucher codes which has been rejected as unexisting or unactive by methods
     * EshopCart::setVoucher() or EshopCart::synchronizeVoucher().
     *
     * @var array 
     */
    protected $rejectedVoucherCode = array();
    
    /**
     * Plain array of presale products ids. This is populated in EshopCart::addProducts()
     * by ids of new added products whose disponibility is set to presale.
     * 
     * @var array 
     */
    protected $presaleProducts = array();

    /**
     * Cart data storage type.
     * Possible values are 'session' and 'cookie'
     *
     * @var string
     */
    protected $storage = 'cookie';

    protected $CookieStorage;

    public function __construct() {
        parent::__construct();

        if ($this->storage == 'cookie') {
            App::loadLib('App', 'CookieStorage');
            $this->CookieStorage = new CookieStorage('_eshop_EshopCart_products_', array(
                'expire' => time() + 2592000, // 30 days
//                'compress' => false,
            ));
            // keep it backward compatible (products are stored under 'products' key)
            $cart = $this->CookieStorage->get();
            if (
                !empty($cart)
                && !array_key_exists('products', $cart)
            ) {
                $cart = array('products' => $cart);
                $this->CookieStorage->set($cart);
            }
        }

        // keep it backward compatible (there are new keys 'parent_index', 'child_indexes' and 'child_ids')
        $products = $this->getProducts();
        foreach ($products as &$product) {
            $product['parent_index'] = Sanitize::value($product['parent_index'], null, true);
            $product['child_indexes'] = (array)Sanitize::value($product['child_indexes']);
            $product['child_ids'] = (array)Sanitize::value($product['child_ids']);
        }
        unset($product);
        $this->setProducts($products);
        
        // validations of cart product fields
        // there are 2 alternatives:
        // - 'add' - used in addProducts()
        // - 'update' - used in updateProducts()
        $this->validations = array(
            'id' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'No product id provided'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'No product id provided'),
                ),
                array(
                    'rule' => 'intNumber',
                    'message' => __v(__FILE__, 'Product id must be an integer number'),
                ),
            ),
            'amount' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'No product amount provided'),
                    'alternative' => array('add', 'update'),
                ),
                array(
                    'rule' => 'notEmpty', // !!! 0 isn't considered to be "empty value" by Validate:.notEmpty()
                    'message' => __v(__FILE__, 'No product amount provided'),
                ),
                array(
                    'rule' => 'intNumber',
                    'message' => __v(__FILE__, 'Product amount must be an integer number'),
                ),
                array(
                    'rule' => array('gte', 0),
                    'message' => __v(__FILE__, 'Product amount must be zero or greater'),
                ),
            ),
            'static_attributes' => array(

            ),
            'dynamic_attributes' => array(

            ),
            'voucher_code' => array(
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide voucher code'),
                    'alternative' => 'voucher',
                ),
                array(
                    'rule' => 'activeVoucherCode',
                    'alternative' => 'voucher',
                ),
            )
        );
    }

    /**
     * Gets the cart id which is unique for actual cart
     *
     * @return string
     */
    public function getId() {
        if ($this->storage == 'cookie') {
            return $this->CookieStorage->getId();
        }
        else {
            //todo
        }
    }

    /**
     * Checks if actual cart is already orderer (there is an order created for it)
     *
     * NOTE: This check is used in case of duplicit order submition which happens
     * in following cases:
     *      - doubleclick on submit button (this can be treated also by js)
     *      - click on submit button + page reload, e.g by F5
     *      - submitting the same order from 2 diffrent tabs in the same browser
     *
     * The atomicity of order save (and the relevance of this check) is ensured by
     * php internal implementation of sessions: "session data is locked to prevent
     * concurrent writes only one script may operate on a session at any time" -
     * see session_write_close() php manual page. It means that concurent scripts
     * operating over a session with same name are blocked on session_start() and
     * executed one by one after session_write_close() in previous script.
     * All duplicit order submition cases suits this.
     *
     * @return bool
     */
    public function isOrdered() {
        $Order = $this->loadModel('EshopOrder', true);
        return $Order->findFirst(array(
            'fields' => array('id'),
            'conditions' => array(
                'cart_id' => $this->getId(),
            )
        ));
    }

    /**
     * Is the cart empty?
     *
     * @return bool
     */
    public function isEmpty() {
        //return $this->getProducts() === null;
        $cartProducts = $this->getProducts();
        return empty($cartProducts);
    }
    
    /**
     * Contains the cart an oversized product(s)?
     *
     * @return bool
     */
    public function hasOversizedProducts() {
        $cartProducts = $this->getProducts();
        if (!$cartProducts) {
            return false;
        }
        $productIds = array_column($cartProducts, 'id');
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        return $Product->isOversized($productIds);
    }

    /**
     * Sets the eshop cart products
     *
     * @param array $products
     * @return array Old value of eshop cart products
     */
    public function setProducts($products) {
        $oldProducts = $this->getProducts();
        if ($this->storage == 'cookie') {
            $this->CookieStorage->setKey('products', $products);
        }
        else {
            $_SESSION['_eshop']['EshopCart']['products'] = $products;
        }
        return $oldProducts;
    }

    /**
     * Gets the eshop cart products
     *
     * @return array Value of eshop cart products
     */
    public function getProducts() {
        if ($this->storage == 'cookie') {
            return (array)$this->CookieStorage->getKey('products');
        }
        else {
            return (array)Sanitize::value($_SESSION['_eshop']['EshopCart']['products']);
        }
    }

    /**
     * Returns a count of cart products
     *
     * @return int
     */
    public function countProducts() {
        return count($this->getProducts());
    }

    /**
     * Clears the eshop cart products
     *
     * @return array Old value of eshop cart products
     */
    public function clearProducts() {
        $products = $this->getProducts();
        if ($this->storage == 'cookie') {
            $this->CookieStorage->unsetKey('products');
        }
        else {
            unset($_SESSION['_eshop']['EshopCart']['products']);
        }
        return $products;
    }

    public function validate_activeVoucherCode($value, $field, $data, &$validation) {
        $Voucher = $this->loadModel('EshopVoucher', true);
        $voucher = $Voucher->getActiveByCode($value);
        if (
            !(  
                !empty($voucher) 
                && (
                    // discount rate voucher
                    !empty($voucher['discount_rate']) 
                    ||
                    // absolute discount voucher
                    !empty($voucher['discount'])
                    && $this->getSetting('EshopProduct.voucherProductId')
                )
            )
        ) {
            $validation['message'] = __v(__FILE__, 'Provided voucher code does not exist or has expired');
            return false;
        }
        // check if the voucher is not applicable only to some products
        $productVoucher = $Voucher->getActiveByCode($value, array(
            'productIds' => array_column($this->getProducts(), 'id'),
        ));
        if (empty($productVoucher)) {
            $validation['message'] = __v(
                __FILE__,
                'Zadaný zľavový kód nie je platný pre produkty umiestnené v košíku. Zoznam produktov, pre ktoré platí, najdete <a href="%s" target="_blank">tu</a>.',
                App::getContentUrlByPid('Eshop.EshopVouchers.verify')
            );
            return false;
        }
        return true;
    }

    /**
     * Sets the eshop cart voucher by provided $voucherCode
     *
     * @param array $voucherCode Voucher record array
     *
     * @return bool TRUE if there is an active voucher for provided $voucherCode. Otherwise
     *      FALSE.
     */
    public function setVoucher($voucherCode) {
        $data = array('voucher_code' => $voucherCode);
        $data = $this->normalize($data, array(
            'alternative' => 'voucher',
        ));
        $Voucher = $this->loadModel('EshopVoucher', true);
        // get the voucher for provided code before it is validated to make this
        // in simple way "atomic"
        $voucher = $Voucher->getActiveByCode($data['voucher_code'], array(
            'productIds' => array_column($this->getProducts(), 'id'),
        ));
        if (!$this->validate($data, array(
            'alternative' => 'voucher',
            'allowFields' => 'voucher_code',
            'normalize' => false,
        ))) {
            $this->rejectedVoucherCode[] = $data['voucher_code'];
            $this->clearVoucher();
            return false;
        }
        // discount rate voucher
        if ($voucher['discount_rate']) {
            if ($this->storage == 'cookie') {
                $this->CookieStorage->setKey('voucher', $voucher);
            }
            else {
                $_SESSION['_eshop']['EshopCart']['voucher'] = $voucher;
            }
        }
        // absolute discount voucher
        else {
            $this->addProducts(array(
                array(
                    'voucher' => $voucher,
                    'amount' => 1,
                )
            ));
        }
        return true;
    }

    /**
     * Checks if cart voucher is still active
     *
     * @return bool TRUE if cart voucher is still active or if there no voucher set
     *      for cart actually. FALSE if cart voucher has got unactive meanwhile.
     */
    public function synchronizeVoucher() {
        // discount rate voucher
        $voucher = $this->getVoucher();
        if (
            $voucher
            && !$this->validate(array('voucher_code' => $voucher['code']), array(
                'alternative' => 'voucher',
                'allowFields' => 'voucher_code',
            ))
        ) {
            $this->rejectedVoucherCode[] = $voucher['code'];
            $this->clearVoucher();
        }
        // absolute discount vouchers
        //@todo? This could be also placed in EshopCart::getOrderableProducts() together
        // with adjusted messages (?)
        $products = $this->getProducts();
        foreach ($products as $cartIndex => $product) {
            if (
                $product['voucher']
                && !$this->validate(array('voucher_code' => $product['voucher']['code']), array(
                    'alternative' => 'voucher',
                    'allowFields' => 'voucher_code',
                ))
            ) {
                $this->rejectedVoucherCode[] = $product['voucher']['code'];
                $this->removeProduct($cartIndex);
            }
        }
        if ($this->rejectedVoucherCode) {
            return false;
        }
        return true;
    }

    /**
     * Gets the eshop cart voucher
     *
     * @return array Voucher record array
     */
    public function getVoucher() {
        if ($this->storage == 'cookie') {
            return (array)$this->CookieStorage->getKey('voucher');
        }
        else {
            return (array)Sanitize::value($_SESSION['_eshop']['EshopCart']['voucher']);
        }
    }

    /**
     * Clears the eshop cart voucher code
     *
     * @return array Old value of eshop cart voucher
     */
    public function clearVoucher() {
        $oldVoucher = $this->getVoucher();
        if ($this->storage == 'cookie') {
            $this->CookieStorage->unsetKey('voucher');
        }
        else {
            unset($_SESSION['_eshop']['EshopCart']['voucher']);
        }
        return $oldVoucher;
    }

    /**
     * Returns the cart price amount missing to get the free shipment
     * 
     * @return float|bool FALSE if no free shipment is allowed. 0 if free shipment
     *      is already achieved. Otherwise a float number of price amount which is
     *      missing to get free shipment
     */
    public function getAmountToFreeShipment() {
        $freeShipmentProductsTotal = $this->getSetting('EshopShipment.freeShipmentProductsTotal');
        if (empty($freeShipmentProductsTotal)) {
            return false;
        }
        $cartPrices = $this->getPrices($this->getProductsDetails());
        $amountToFreeShipment = $freeShipmentProductsTotal - $cartPrices['products_price_actual_taxed'];
        if ($amountToFreeShipment < 0) {
            $amountToFreeShipment = 0;
        }
        return $amountToFreeShipment;
    }

    /**
     * Returns a message about the cart price amount missing to get the free shipment
     *
     * @param array $options Following are available:
     *      - 'freeShipmentAchieved' (bool&) Aux output, must be provided by reference.
     *          Has been the free shipment achieved?
     * 
     * @return null|string NULL if no free shipment is allowed. Otherwise info message
     *       about the cart price amount missing to get the free shipment.
     */
    public function getAmountToFreeShipmentMessage($options = array()) {
        $amountToFreeShipment = $this->getAmountToFreeShipment();
        // set aux output
        $options['freeShipmentAchieved'] = false;
        if ($amountToFreeShipment === false) {
            return null;
        }
        if ($amountToFreeShipment) {
            return __(__FILE__, 'Nakúpte ešte za %s a získate poštovné zdarma.', Eshop::formatPrice($amountToFreeShipment));
        }
        // set aux output
        $options['freeShipmentAchieved'] = true;
        return __(__FILE__, 'Poštovné máte zdarma.');
    }

    /**
     * Clears the eshop cart
     */
    public function clear() {
        if ($this->storage == 'cookie') {
            $this->CookieStorage->clear();
        }
        else {
            unset($_SESSION['_eshop']['EshopCart']);
        }
    }

    /**
     * Sets url where the process is redirected after adding a product to cart
     *
     * @param string $url Optional. Redirect url. If empty then actual referer is used.
     *      Defaults to NULL.
     *
     * @return string Old value of redirect url
     */
    public function setRedirectAfterAddProduct($url = null) {
        if (!$url) {
            $url = App::getRefererUrl('/');
        }
        $oldUrl = $this->getRedirectAfterAddProduct();
        $_SESSION['_eshop']['EshopCart']['redirectAfterAddProduct'] = $url;
        return $oldUrl;
    }

    /**
     * Gets url where the process is redirected after adding a product to cart
     *
     * @return string Value of redirect url
     */
    public function getRedirectAfterAddProduct() {
        return Sanitize::value($_SESSION['_eshop']['EshopCart']['redirectAfterAddProduct']);
    }

    /**
     * Clears url where the process is redirected after adding a product to cart
     *
     * @return string Old value of redirect url
     */
    public function clearRedirectAfterAddProduct() {
        $url = $this->getRedirectAfterAddProduct();
        unset($_SESSION['_eshop']['EshopCart']['redirectAfterAddProduct']);
        return $url;
    }

    /**
     *
     * @param array $data Product(s) data
     * @param array $options
     *      - 'single' (bool) If TRUE then provided data are a single product data.
     *          If FALSE the provided data are list of products. Dafaults to FALSE.
     *      - 'alternative' (string)
     *      - 'on' (string)
     */
    public function normalize($data, $options = array()) {
        $defaults = array(
            'single' => false,
            'alternative' => null,
            'on' => null,
        );
        $options = array_merge($defaults, $options);
        //$data = parent::normalize($data, $options);
        
        $voucherProductId = $this->getSetting('EshopProduct.voucherProductId');
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();

        if ($options['alternative'] === 'voucher') {
            if (!empty($data['voucher_code'])) {
                $data['voucher_code'] = strtoupper(trim($data['voucher_code']));
            }
        }
        else {
            if ($options['single']) {
                $data['id'] = Sanitize::value($data['id']);
                if ($Product->findFieldBy('units', 'id', $data['id']) === 'enum_piece') {
                    $data['amount'] = (int)str_replace(' ', '', Sanitize::value($data['amount']));
                }
                else {
                    $data['amount'] = (float)str_replace(
                        ' ', 
                        '', 
                        str_replace(',', '.', Sanitize::value($data['amount']))
                    );
                }
                $data['static_attributes'] = (array)Sanitize::value($data['static_attributes']);
                $data['dynamic_attributes'] = (array)Sanitize::value($data['dynamic_attributes']);
                if (isset($data['special_offer_id'])) {
                    $data['special_offer_id'] = (int)$data['special_offer_id'];
                }
                $data['voucher'] = $voucherProductId ? Sanitize::value($data['voucher']) : null;
                if ($data['voucher']) {
                    $data['id'] = $voucherProductId;
                }
            }
            else {
                foreach ($data as $i => &$product) {
                    // remove nonarray items
                    if (!is_array($product)) {
                        unset($data[$i]);
                        continue;
                    }
                    $product['id'] = Sanitize::value($product['id']);
                    if ($Product->findFieldBy('units', 'id', $product['id']) === 'enum_piece') {
                        $product['amount'] = (int)str_replace(' ', '', Sanitize::value($product['amount']));
                    }
                    else {                        
                        $product['amount'] = (float)str_replace(
                            ' ', 
                            '', 
                            str_replace(',', '.', Sanitize::value($product['amount']))
                        );
                    }
                    $product['static_attributes'] = (array)Sanitize::value($product['static_attributes']);
                    $product['dynamic_attributes'] = (array)Sanitize::value($product['dynamic_attributes']);
                    if (isset($product['special_offer_id'])) {
                        $product['special_offer_id'] = (int)$product['special_offer_id'];
                    }
                    $product['voucher'] = $voucherProductId ? Sanitize::value($product['voucher']) : null;
                    if ($product['voucher']) {
                        $product['id'] = $voucherProductId;
                    }
                }
                // unset reference
                unset($product);
            }
        }

        return $data;
    }

    /**
     * Compares two cart products. Two products equals if their 'id', 'static_attributes'
     * and 'dynamic_attributes' equal.
     *
     * @param array $product1 Cart product 1
     * @param array $product2 Cart product 2
     * @param array $options Following are available:
     *          - 'comparePairedProducts' (string|bool) Possible values are:
     *              - 'byCartIndex' - to compare only values of pairing cart indexes
     *                  ('promoted_cart_index' or 'discounted_cart_index')
     *              - 'byCartRecord' - to compare paired cart products records
     *              - FALSE - to not consider paired products in comparison (used
     *                  intenaly by 'byCartRecord').
     *              Defaults to 'byCartIndex'.
     *          - 'products' (array) Explicit list of products if 'comparePairedProducts'
     *              is set to 'byCartRecord'. It must have structure of cart products 
     *              array returned by EshopCart::getProducts().
     *
     * @return bool TRUE if products equals. FALSE otherwise.
     */
    public function compareProducts($product1, $product2, $options = array()) {
        $defaults = array(
            'comparePairedProducts' => 'byCartIndex',
            'products' => null,
        );
        $options = array_merge($defaults, $options);
        if (
            $options['comparePairedProducts'] === 'byCartRecord'
            && !isset($options['products'])
        ) {
            throw new Exception(__e(__FILE__, 'Missing cartProducts option'));
        }
        $cartProducts = $options['products'];
        if (
            $product1['id'] == $product2['id']
            // NULL and 0 are two different values, use strict comparison
            && Sanitize::value($product1['parent_index'], null, true) === Sanitize::value($product2['parent_index'], null, true) 
            && !array_diff(
                (array)Sanitize::value($product1['child_indexes']), 
                (array)Sanitize::value($product2['child_indexes'])
            )
            && (array)Sanitize::value($product1['static_attributes']) == (array)Sanitize::value($product2['static_attributes'])
            && (array)Sanitize::value($product1['dynamic_attributes']) == (array)Sanitize::value($product2['dynamic_attributes'])
            && Sanitize::value($product1['special_offer_id']) === Sanitize::value($product2['special_offer_id'])
            && Sanitize::value($product1['voucher']) == Sanitize::value($product2['voucher'])
            // promoted_cart_index
            && (
                $options['comparePairedProducts'] === false
                ||
                $options['comparePairedProducts'] === 'byCartIndex'
                && Sanitize::value($product1['promoted_cart_index']) === Sanitize::value($product2['promoted_cart_index'])
                ||
                $options['comparePairedProducts'] === 'byCartRecord'
                && (
                    !isset($product1['promoted_cart_index'])
                    && !isset($product2['promoted_cart_index'])
                    ||
                    isset($product1['promoted_cart_index'])
                    && !is_array($product1['promoted_cart_index'])
                    && isset($cartProducts[$product1['promoted_cart_index']])
                    && isset($product2['promoted_cart_index'])
                    && !is_array($product2['promoted_cart_index'])
                    && isset($cartProducts[$product2['promoted_cart_index']])
                    && $this->compareProducts(
                        $cartProducts[$product1['promoted_cart_index']],
                        $cartProducts[$product2['promoted_cart_index']],
                        array('comparePairedProducts' => false)
                    )
                    ||
                    isset($product1['promoted_cart_index'])
                    && is_array($product1['promoted_cart_index'])
                    && isset($product2['promoted_cart_index'])
                    && is_array($product2['promoted_cart_index'])
                    && ($count1 = count($product1['promoted_cart_index'])) 
                        === ($count2 = count($product2['promoted_cart_index']))
                    && ($countPairs = count($this->pairProducts(
                        $product1['promoted_cart_index'],
                        $product2['promoted_cart_index'],
                        $cartProducts
                    ))) === $count1
                    && $countPairs === $count2
                )
            )
            // discounted_cart_index
            && (
                $options['comparePairedProducts'] === false
                ||
                $options['comparePairedProducts'] === 'byCartIndex'
                && Sanitize::value($product1['discounted_cart_index']) === Sanitize::value($product2['discounted_cart_index'])
                ||
                $options['comparePairedProducts'] === 'byCartRecord'
                && (
                    !isset($product1['discounted_cart_index'])
                    && !isset($product2['discounted_cart_index'])
                    ||
                    isset($product1['discounted_cart_index'])
                    && !is_array($product1['discounted_cart_index'])
                    && isset($cartProducts[$product1['discounted_cart_index']])
                    && isset($product2['discounted_cart_index'])
                    && !is_array($product2['discounted_cart_index'])
                    && isset($cartProducts[$product2['discounted_cart_index']])
                    && $this->compareProducts(
                        $cartProducts[$product1['discounted_cart_index']],
                        $cartProducts[$product2['discounted_cart_index']],
                        array('comparePairedProducts' => false)
                    )
                    ||
                    isset($product1['discounted_cart_index'])
                    && is_array($product1['discounted_cart_index'])
                    && isset($product2['discounted_cart_index'])
                    && is_array($product2['discounted_cart_index'])
                    && ($count1 = count($product1['discounted_cart_index'])) 
                        === ($count2 = count($product2['discounted_cart_index']))
                    && ($countPairs = count($this->pairProducts(
                        $product1['discounted_cart_index'],
                        $product2['discounted_cart_index'],
                        $cartProducts
                    ))) === $count1
                    && $countPairs === $count2
                )
            )
            && Sanitize::value($product1['cart_price_threshold']) === Sanitize::value($product2['cart_price_threshold'])
        ) {
            return true;
        }
        return false;
    }
    
    /**
     * Compares provided cart product amounts
     * 
     * @param int|float|string $amount1
     * @param int|float|string $amount2
     * @param array $options Following are available:
     *      - 'hasFloat' (bool&) Aux output, must be provided by reference.
     *          If TRUE then at least one provided amounts is a float number.
     *          If FALSE then both provided amounts are integer numbers.
     *          Can be used to know to which type should be amounts
     *          typecasted when adding them together.
     * 
     * @return int Following values are returned:
     *      - -1 if $amount1 is less than $amount2
     *      - 0 if $amount1 equals to $amount2
     *      - 1 if $amount1 is greater than $amount2
     *              
     */
    protected function compareAmounts($amount1, $amount2, $options = array()) {
        // if at least one float amount
        if (
            Validate::floatNumber($amount1)
            ||
            Validate::floatNumber($amount2)
        ) {
            $amount1 = (float) $amount1;
            $amount2 = (float) $amount2;
            $options['hasFloat'] = true;            
            return Number::compare($amount1, $amount2);
        }
        // if both amounts are integer
        $amount1 = (int) $amount1;
        $amount2 = (int) $amount2;
        $options['hasFloat'] = false;
        return $amount1 <=> $amount2;
    }
    
    /**
     * Pairs provided cartIndexes1 with cartIndexes2 comparing corresponding products
     * in $cartProducts. Products are compared without comparing paired products.
     * Returned array contains pairs {cartIndex1} => {cartIndex2}. Only paired indexes 
     * are included. Indexes without pairs are omitted.
     * 
     * This method is used in comparison and merging of products of offers applied
     * by promoted products cart price. These products have 'promoted_cart_index'
     * and 'discounted_cart_index' set to array.
     * 
     * ATTENTION: {cartIndex1} and {cartIndex2} can have possibly the same value.
     * 
     * @param array $cartIndexes1
     * @param array $cartIndexes2
     * @param array $cartProducts Actual list of cart products
     * 
     * @return array The above described array
     */
    protected function pairProducts($cartIndexes1, $cartIndexes2, $cartProducts) {
        $pairs = array();
        foreach ($cartIndexes1 as $cartIndex1) {
            foreach ($cartIndexes2 as $k2 => $cartIndex2) {
                if ($this->compareProducts(
                        $cartProducts[$cartIndex1],
                        $cartProducts[$cartIndex2],
                        array('comparePairedProducts' => false)
                )) {
                    $pairs[$cartIndex1] = $cartIndex2;
                    unset($cartIndexes2[$k2]);
                    break;
                }
            }
        }
        return $pairs;
    }

    /**
     * Gets the list of orderable products from cart products or from explicitly
     * provided list of products (see $options).
     *
     * @param array $options
     *      - 'products' (array) Explicit list of items to get orderable items from.
     *          It must have structure of cart products:
     *              array(
     *                  array(
     *                      'id' => 1, 
     *                      'amount' => 2, 
     *                      'static_attributes' => array(), 
     *                      'dynamic_attributes' => array()
     *                  ), 
     *                  ...
     *              )
     *      - 'adjustAmounts' (bool) If TRUE then also items amounts are adjusted.
     *          If FALSE then amounts are not adjusted and only non-orderable items
     *          are removed. Defaults to TRUE.
     *      - 'validate' (bool) Defaults to FALSE.
     *      - 'alternative' (string) Validation alternative. Defaults to NULL.
     *
     * @return boolean|array FALSE on validation error. Array of orderable items
     *      otherwise. Removed and items with adjusted amounts are stored in 
     *      EshopCart::$adjustedProducts in array like:
     *          array(
     *              '{cartIndex01}' => array(
     *                  'id' => '{productId01}', 
     *                  'adjustment' => EshopCart::PRODUCT_REMOVED
     *              ),
     *              '{cartIndex02}' => array(
     *                  'id' => '{productId02}', 
     *                  'adjustment' => EshopCart::PRODUCT_AMOUNT_ADJUSTED
     *              ),
     *              // if there are also occurrence items in cart then the structure is following
     *              '{cartIndex03}' => array(
     *                  'id' => '{productId03}', 
     *                  'run_eshop_product_occurrences_id' => '{productOccurrenceId01}', 
     *                  'adjustment' => EshopCart::PRODUCT_REMOVED
     *              ),
     *              '{cartIndex04}' => array(
     *                  'id' => '{productId04}', 
     *                  'run_eshop_product_occurrences_id' => '{productOccurrenceId02}', 
     *                  'adjustment' => EshopCart::PRODUCT_AMOUNT_ADJUSTED
     *              ),
     *              ...
     *          )
     */
    public function getOrderableProducts($options = array()) {
        $defaults = array(
            'products' => array(),
            'adjustAmounts' => true,
            'validate' => false,
            'alternative' => null,
        );
        $options = array_merge($defaults, $options);
        $options['normalize'] = false;

        if ($options['products']) {
            $products = $this->normalize($options['products']);
        }
        else {
            $products = $this->getProducts();
        }
        // validate products and get product ids
        $productIds = array();
        foreach ($products as &$product) {
            if (
                $options['validate']
                && !$this->validate($product, $options)
            ) {
                return false;
            }
            $productIds[] = $product['id'];
        }
        // unset the reference
        unset($product);

        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $orderableProducts = $Product->getOrderable($productIds);
        $orderableToStockAmount = $this->getSetting('EshopProduct.orderableToStockAmount');
        $this->adjustedProducts = array();
        // array of adjusted items used to actualize child items in cart,
        // it contains pairs {cartIndex} => array('id' => {id}, 'amount' => {availableAmountOrZeroForRemoved})
        $adjustedItems = array();
        $hasFloatAmount = false;
        foreach ($products as $i => &$product) {
            if (!isset($orderableProducts[$product['id']])) {
                // keep the track off removed product
                $this->adjustedProducts[$i] = array(
                    'id' => $product['id'],
                    'adjustment' => self::PRODUCT_REMOVED,
                );
                // remove this product from the set
                unset($products[$i]);
                $adjustedItems[$i] = array(
                    'id' => $product['id'],
                    'amount' => 0, // 0 = removed
                );
            }
            // if orderable product then check if there is enough items in stock in case of sold-out
            // products. If not, update the amount up to the maximum possible amount
            elseif (
                $options['adjustAmounts']
                && $this->compareAmounts(
                    $product['amount'],
                    $orderableProducts[$product['id']]['stock'],
                    array(
                        'hasFloat' => &$hasFloatAmount
                    )
                ) === 1
                && empty($orderableProducts[$product['id']]['is_additional_service'])
                && (
                    $orderableToStockAmount
                    || $orderableProducts[$product['id']]['availability'] === 'enum_soldout'
                )
            ) {
                // not enough items in stock and the product is sold-out
                // update the maximum amount
                if ($hasFloatAmount) {
                    $product['amount'] = (float) $orderableProducts[$product['id']]['stock'];
                }
                else {
                    $product['amount'] = (int) $orderableProducts[$product['id']]['stock'];
                }
                $this->adjustedProducts[$i] = array(
                    'id' => $product['id'],
                    'adjustment' => self::PRODUCT_AMOUNT_ADJUSTED,
                );
                $adjustedItems[$i] = array(
                    'id' => $product['id'],
                    'amount' => $product['amount'],
                );
            }
        }
        // unset the reference
        unset($product);

        // adjust also related child and parent items
        if ($adjustedItems) {
            foreach ($adjustedItems as $adjustedIndex => $adjustedItem) {
                foreach ($products as $i => &$product) {
                    // adjust related child items
                    if ($product['parent_index'] === $adjustedIndex) { // NULL and 0 are two different values, use strict comparison
                        // remove child items if parent item has been removed 
                        if (!$adjustedItem['amount']) {                            
                            unset($products[$i]);
                        }
                        // update child items amount if parent amount has been changed
                        //rblb//elseif ((int) $product['amount'] > (int) $adjustedItem['amount']) {
                        else {
                            $product['amount'] = $adjustedItem['amount'];
                        }
                    }
                    // adjust related parent items
                    if (in_array($adjustedIndex, $product['child_indexes'])) {
                        // delete relation if child item has been removed
                        if (!$adjustedItem['amount']) {
                            $product['child_indexes'] = array_diff(
                                $product['child_indexes'], 
                                array($adjustedIndex)
                            );
                            $product['child_ids'] = array_diff(
                                $product['child_ids'], 
                                array($adjustedItem['id'])
                            );
                        }
                    }
                }
                // unset the reference
                unset($product);
            }
        }
        
        return $products;
    }

    /**
     * Gets value of EshopCart::$adjustedProducts property
     *
     * @return array Adjusted products
     */
    public function getAdjustedProducts() {
        return $this->adjustedProducts;
    }

    /**
     * Sets app messages for all adjusted items (implicit or provided)
     *
     * @param array $options Following are available
     *       - 'adjustedProducts' (array) Explicit array of adjusted items to 
     *          set messages for. Defaults to EshopCart::$adjustedProducts.
     *       - 'on' (string) Event name to display customized version of messages.
     *          Possible values are 'view' and 'add'. Defaults to 'view'.
     */
    public function setAdjustedProductsAppMessages($options = array()) {
        $defaults = array(
            'adjustedProducts' => $this->adjustedProducts,
            'on' => 'view',
        );
        $options = array_merge($defaults, $options);
        if (empty($options['adjustedProducts'])) {
            return;
        }
        $productIds = array();
        foreach ($options['adjustedProducts'] as $adjustedProduct) {
            $productIds[] = $adjustedProduct['id'];
        }
        $productIds = array_unique($productIds);
        $Product = $this->loadModel('EshopProduct', true);
        $adjustedProductNames = $Product->findList(array(
            'fields' => array('EshopProduct.name'),
            'conditions' => array('id' => $productIds)
        ));
        foreach($options['adjustedProducts'] as $adjustedProduct) {
            $adjustment = $adjustedProduct['adjustment'];
            $productId = $adjustedProduct['id'];
            $productName = Sanitize::value($adjustedProductNames[$productId]);
            if ($adjustment == EshopCart::PRODUCT_REMOVED) {
                if ($options['on'] === 'add') {
                    $message = __(
                        __FILE__, 
                        'Product "%s" is not orderable', 
                        $productName
                    );
                }
                else {
                    $message = __(
                        __FILE__, 
                        'Product "%s" is no longer available. It has been removed from your cart.',
                        $productName
                    );
                }
            }
            elseif ($adjustment == EshopCart::PRODUCT_AMOUNT_ADJUSTED) {
                $message = __(
                    __FILE__, 
                    'The product "%s" is no longer available from the manufacturer and there are fewer remaining items in our stock than what you requested. The maximum amount available has been inserted into your cart.',
                    $productName
                );
            }
            elseif ($adjustment == EshopCart::PRODUCT_SPECIAL_OFFER_APPLICATION_ADJUSTED) {
                $message = __(
                    __FILE__,
                    'Cena produktu "%s" v košíku sa zmenila s ohľadom na dostupné špeciálne ponuky',
                    $productName
                );
            }
            else {
                continue;
            }
            App::setMessage($message, array('modal' => true));
        }
    }
    /**
     * Gets value of EshopCart::$presaleProducts property
     *
     * @return array Presale products
     */
    public function getPresaleProducts() {
        return $this->presaleProducts;
    }

    /**
     * Sets app messages for all presale products (implicit or provided)
     *
     * @param array $options Following are available
     *       - 'presaleProducts' (array) Explicit array of presale products to
     *          set messages for. Defaults to EshopCart::$presaleProducts.
     *       - 'on' (string) Event name to display customized version of messages.
     *          Possible values are 'add'. Defaults to 'add'.
     */
    public function setPresaleProductsAppMessages($options = array()) {
        $defaults = array(
            'presaleProducts' => $this->presaleProducts,
            'on' => 'add',
        );
        $options = array_merge($defaults, $options);
        if (empty($options['presaleProducts'])) {
            return;
        }
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $presaleProducts = $Product->getDetails($options['presaleProducts']);
        foreach($presaleProducts as $presaleProduct) {
            if ($presaleProduct['disponibility'] == EshopProduct::PRESALE) { 
                $message = __(
                    __FILE__, 
                    'Produkt "%s" je v predpredaji a bude dostupný až od %s. Vašu objednávku preto vybavíme až po tomto dátume.', 
                    $presaleProduct['name'],
                    Date::format($presaleProduct['available_from'], 'j. n. Y')
                );
            }
            else {
                $message = __(
                    __FILE__, 
                    'Produkt "%s" bude dostupný až od %s. Vašu objednávku preto vybavíme až po tomto dátume.', 
                    $presaleProduct['name'],
                    Date::format($presaleProduct['available_from'], 'j. n. Y')
                );
            }
            App::setMessage($message, array('modal' => true));
        }
    }
    
    /**
     * Sets app confimation message of "Continue to order" button in cart.
     * This message is set if there is at leat one off stock product in cart.
     */
    public function setOffStockProductsAppConfirmationMessage() {
        $products = $this->getProductsDetails();
        $products = array_filter($products, function($item) {
            return $item['stock'] <= 0;
        });
        if (empty($products)) {
            return;
        }
        $this->loadView('EshopCart/setOffStockProductsAppConfirmationMessage', array(
            'products' => $products,
        ));
    }

    /**
     * Synchronize the cart products with state on stock.
     *
     * If you would like to inform user about changes in his cart after synchronization
     * then call EshopCart::setAdjustedProductsAppMessages().
     *
     * @param array $options See options of EshopCart::getOrderableProducts()
     *
     * @return bool|array FALSE on validation errors. TRUE on full success. Array
     *      of products which are adjusted because they are either not orderable already
     *      or they are not accessible in required amount. The returned array has
     *      following format:
     *
     *          array(
     *              // removed product
     *              '{cartIndex01}' => array(
     *                  'id' => '{productId01}', 
     *                  'adjustment' => EshopCart::PRODUCT_REMOVED
     *              ),
     *              // product with decreased amount
     *              '{cartIndex02}' => array(
     *                  'id' => '{productId02}', 
     *                  'adjustment' => EshopCart::PRODUCT_AMOUNT_ADJUSTED
     *              ),
     *              ...
     *          )
     */
    public function synchronizeProducts($options = array()) {
        $cartProducts = $this->getOrderableProducts($options);
        if ($cartProducts === false) {
            return false;
        }

        $this->setProducts($cartProducts);
        if ($this->adjustedProducts) {
            return $this->adjustedProducts;
        }
        return true;
    }

    /**
     * Adds products to cart stored in session in form of array like:
     *
     *      array(
     *              0 => array(
     *                  // product id
     *                  'id' => ... 
     *                  'amount' => ...
     *                  // ATTENTION: For parent_index NULL and 0 are two different values
     *                  'parent_index' => ... 
     *                  'child_indexes' => array(...)
     *                  'child_ids' => array(...)
     *                  'static_attributes' => array(
     *                      '{attrFieldName1}' => '{attrSelectedValue1}'
     *                      '{attrFieldName2}' => '{attrSelectedValue2}'
     *                      ...
     *                  ),
     *                  'dynamic_attributes' => array(
     *                      '{attrId1}' => '{attrSelectedValue1}'
     *                      '{attrId2}' => '{attrSelectedValue2}'
     *                      ...
     *                  ),
     *                  'voucher' => {emptyValueOrAbsoluteVoucherRecordArray},
     *              ),
     *              // ATTENTION: Do not depend on sequential order of indexes
     *              2 => array(...),
     *              1 => array(...),
     *              ...
     *      )
     *
     * @param array $products Products data to be added to cart with following structure:
     *
     *      array(
     *          0 => array(
     *              'id' => 34,
     *              'amount' => 1
     *              'static_attributes' => array(
     *                  '{attrFieldName1}' => '{attrSelectedValue1}'
     *                  '{attrFieldName2}' => '{attrSelectedValue2}'
     *                  ...
     *              ),
     *              'dynamic_attributes' => array(
     *                  ??? // this was not implemented yet - define it on the first implementation, maybe st like this:
     *                  '{attrId1}' => '{attrSelectedValue1}'
     *                  '{attrId2}' => '{attrSelectedValue2}'
     *                  ...
     *              ),
     *              'voucher' => {emptyValueOrAbsoluteVoucherRecordArray},
     *          ),
     *          1 => array(
     *              'id' => 22,
     *              'amount' => 5
     *              'static_attributes' => array(...)
     *              'dynamic_attributes' => array(...)
     *              'voucher' => array(...),
     *          ),
     *          ...
     *      )
     *
     * @param $options Optional.
     *      - 'synchronize' (bool|array) If TRUE then cart products are synchronized with stock
     *          after the new ones are added. If an array then it is used as options for
     *          EshopCart::synchronize(). Defaults to array('adjustAmounts' => false),
     *          it means that products are synchronized, non-orderable products
     *          are removed but products amounts are not adjusted.
     *      - 'productIndexes' (int) Aux output. Must be set by reference, e.g. 
     *          'productIndex' => &$productIndex. Array of integer cart product indexes
     *          if the products were added to cart.
     *
     * @return bool|array FALSE on invalid products data (missing ids or amounts).
     *      TRUE on full success. Array of adjusted products if option 'synchronize'
     *      is accordingly set and some products have been removed or their amounts adjusted.
     *      In such a case call EshopCart::setAdjustedProductsAppMessages() if you
     *      would like to inform user about changes in his cart. The returned array
     *      is never empty and has following format:
     *
     *          array(
     *              // removed product
     *              '{cartIndex01}' => array(
     *                  'id' => '{productId01}', 
     *                  'adjustment' => EshopCart::PRODUCT_REMOVED
     *              ),
     *              // product with decreased amount
     *              '{cartIndex02}' => array(
     *                  'id' => '{productId02}', 
     *                  'adjustment' => EshopCart::PRODUCT_AMOUNT_ADJUSTED
     *              ),
     *              ...
     *          )
     */
    public function addProducts($products, $options = array()) {
        $defaults = array(
            'synchronize' => array(
                'adjustAmounts' => false
            ),
            'productIndexes' => array(), // aux output
        );
        $options = array_merge($defaults, $options);

        // normalize
        $products = $this->normalize($products);

        // validate
        foreach($products as $product) {
            $valid = $this->validate($product, array(
                'alternative' => 'add',
                'normalize' => false
            ));
            if (!$valid) {
                return false;
            }
        }

        // add the product to cart
        $cartProducts = $this->getProducts();

        // check if there is already the same product in cart
        // if yes then augment its amount
        $options['productIndexes'] = array();
        foreach ($products as $product) {
            if (empty($product['amount'])) {
                continue;
            }
            $productAdded = false;
            $productIndex = null;
            foreach ($cartProducts as $i => &$cartProduct) {
                if ($this->compareProducts($cartProduct, $product)) {
                    $cartProduct['amount'] += $product['amount'];
                    $productAdded = true;
                    $productIndex = $i;
                    break;
                }
            }
            // unset reference
            unset($cartProduct);
            // product not in the cart yet, add it
            if (
                !$productAdded
                && $product['amount'] > 0
            ) {
                $cartProducts[] = array(
                    'id' => $product['id'],
                    'amount' => $product['amount'],
                    'parent_index' => null,
                    'child_indexes' => array(),
                    'child_ids' => array(),
                    'static_attributes' => $product['static_attributes'],
                    'dynamic_attributes' => $product['dynamic_attributes'],
                    // proposed special offer id
                    'special_offer_id' => Sanitize::value($product['special_offer_id']),
                    'voucher' => Sanitize::value($product['voucher']),
                );
                $productIndex = array_keys($cartProducts);
                $productIndex = end($productIndex);
            }
            // force amount 1 for absolute discount vouchers
            if ($cartProducts[$productIndex]['voucher']) {
                $cartProducts[$productIndex]['amount'] = 1;
            }
            $options['productIndexes'][] = $productIndex;
        }

        $this->setProducts($cartProducts);

        if ($options['synchronize']) {
            $this->synchronizeProducts((array)$options['synchronize']);
        }

        $cartProducts = $this->getProducts();
        $cartProducts = $this->applySpecialOffers($cartProducts);
        $this->setProducts($cartProducts);
        
        // check for presale products
        $productIds = array();
        foreach ($products as $product) {
            $productIds[$product['id']] = true;
        }
        $productIds = array_keys($productIds);
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $detailedProducts = $Product->getDetails($productIds);
        $this->presaleProducts = array();
        foreach ($detailedProducts as $detailedProduct) {
            if (
                !empty($detailedProduct['available_from'])
                && strtotime($detailedProduct['available_from']) > time()
                && $detailedProduct['disponibility'] != EshopProduct::STOCK
            ) {
                $this->presaleProducts[] = $detailedProduct['id'];
            }
        }
        
        if (
            $options['synchronize']
            && $this->adjustedProducts
        ) {
            return $this->adjustedProducts;
        }
        return true;
    }

    /**
     * Adds child product for specified product in cart.
     * The difference between child items and normal items in cart is that if the 
     * parent item is removed from cart then all its child items are removed too.
     * 
     * @param int $parentIndex Index of product in the cart to add child product for
     * @param int $childProductId Id of product to be added to cart as a child product
     *      for product specified by $cartIndex.
     * @param $options Optional. 
     *      - 'synchronize' (bool|array) If TRUE then cart products are synchronized with stock 
     *          after the new ones are added. If an array then it is used as options for 
     *          EshopCart::synchronize(). Defaults to array('adjustAmounts' => false),
     *          it means that products are synchronized, non-orderable products
     *          are removed but products amounts are not adjusted.
     * 
     * @return boolean FALSE on invalid products data (missing ids or amounts). 
     *      TRUE on full success.
     */
    public function addChildProduct($parentIndex, $childProductId, $options = array()) {
        $defaults = array(
            'synchronize' => array(
                'adjustAmounts' => false
            ),
        );
        $options = array_merge($defaults, $options);
        
        // validate existence of parent in cart
        $cartProducts = $this->getProducts();
        if (!isset($cartProducts[$parentIndex])) {
            return false;
        }
        // create child item
        $parentProduct = $cartProducts[$parentIndex];
        $childProduct = array(
            'id' => $childProductId,
            'amount' => $parentProduct['amount'], //see @implementationDetails > 200526a
            'parent_index' => (int)$parentIndex,
            'child_indexes' => array(),
            'child_ids' => array(),
            'static_attributes' => array(),
            'dynamic_attributes' => array(),
            'voucher' => null,
        );
        // validate child item
        if (!$this->validate($childProduct, array(
            'alternative' => 'add', 
            'normalize' => false
        ))) {
            return false;
        }
        // place the child item to cart behind the parent item but do not change
        // existing indexes. As the index order is mixed, the last index is not
        // necessarily the highest
        $childIndex = max(array_keys($cartProducts)) + 1;
        $tmp = array();
        foreach ($cartProducts as $cartIndex => $cartProduct) {
            // add child item index and id to parent item
            if ($cartIndex == $parentIndex) {
                $cartProduct['child_indexes'][] = $childIndex;
                $cartProduct['child_indexes'] = array_unique($cartProduct['child_indexes']);
                $cartProduct['child_ids'][] = $childProductId;
                $cartProduct['child_ids'] = array_unique($cartProduct['child_ids']);
            }
            $tmp[$cartIndex] = $cartProduct;
            // add child item to cart
            if ($cartIndex == $parentIndex) {
                $tmp[$childIndex] = $childProduct;
            }
        }
        $cartProducts = $tmp;
        
        $this->setProducts($cartProducts);
        
        if ($options['synchronize']) {
            $this->synchronizeProducts((array)$options['synchronize']);
        }     
        
        $cartProducts = $this->getProducts();
        $cartProducts = $this->applySpecialOffers($cartProducts);
        $this->setProducts($cartProducts);        
        
        if (
            $options['synchronize']
            && $this->adjustedProducts
        ) {
            return $this->adjustedProducts;
        }
        return true;
    }
    
    /**
     * Removes specified product from the cart
     *
     * @param int|array $product Product cart index (int). If an array then
     *      considered to be product data. Must contain at least 'id'. Static attributes
     *      must be stored under key 'static_attributes'. Dynamic attributes must
     *      be stored under key 'dynamic_attributes'.
     *
     * @return bool True if the product was removed from cart. FALSE on validation errors.
     *      Validation errors can be found in EshopOrderProduct::$errors.
     */
    public function removeProduct($product) {

        // load cart from the session
        $cartProducts = $this->getProducts();
        $removedIndex = null;
        $removedId = null;
        $productRemoved = false;
        if (!is_array($product)) {
            if (array_key_exists($product, $cartProducts)) {
                $removedIndex = (int)$product;
                $removedId = $cartProducts[$product]['id'];
                unset($cartProducts[$product]);
                $productRemoved = true;
            }
        }
        else {
            // normalize
            $product = $this->normalize($product, array('single' => true));
            // validate
            if (!$this->validate($product, array('normalize' => false))) {
                return false;
            }
            // remove the specified product cart item if it is in the cart
            foreach ($cartProducts as $i => $cartProduct) {
                if ($this->compareProducts($cartProduct, $product)) {
                    $removedIndex = $i;
                    $removedId = $cartProducts[$i]['id'];
                    unset($cartProducts[$i]);
                    $productRemoved = true;
                    break;
                }
            }
        }

        if ($productRemoved) {
            // adjust also related child and parent items
            foreach ($cartProducts as $i => &$cartProduct) {
                // remove related child items
                if ($cartProduct['parent_index'] === $removedIndex) { // NULL and 0 are two different values, use strict comparison
                    unset($cartProducts[$i]);
                }
                // delete relation in parent item if child item has been removed
                if (in_array($removedIndex, $cartProduct['child_indexes'])) {
                    $cartProduct['child_indexes'] = array_diff(
                        $cartProduct['child_indexes'], 
                        array($removedIndex)
                    );
                    $cartProduct['child_ids'] = array_diff(
                        $cartProduct['child_ids'], 
                        array($removedId)
                    );
                }
            }
            // unset the reference
            unset($cartProduct);
            // apply special offers
            $cartProducts = $this->applySpecialOffers($cartProducts);
            // save the updated cart
            $this->setProducts($cartProducts);
        }

        return true;
    }

    /**
     * Updates the content of the current cart using provided products.
     *
     * @param array $products Products data with following structure:
     *
     *      array(
     *          array(
     *              'id' => 34,
     *              'cartIndex' => 0,
     *              'amount' => 1
     *              'static_attributes' => array(
     *                  '{attrFieldName1}' => '{attrSelectedValue1}'
     *                  '{attrFieldName2}' => '{attrSelectedValue2}'
     *                  ...
     *              ),
     *              'dynamic_attributes' => array(
     *                  ??? // this was not implemented yet - define it on the first implementation, maybe st like this:
     *                  '{attrId1}' => '{attrSelectedValue1}'
     *                  '{attrId2}' => '{attrSelectedValue2}'
     *                  ...
     *              ),
     *              'voucher' => {emptyValueOrAbsoluteVoucherRecordArray},
     *          ),
     *          array(
     *              'id' => 22,
     *              'cartIndex' => 2,   // item with cart index 1 was removed
     *              'amount' => 5
     *              'static_attributes' => array(...)
     *              'dynamic_attributes' => array(...)
     *              'voucher' => array(...),
     *          ),
     *          ...
     *      )
     *
     * @return bool|array FALSE on invalid products data (missing ids or amounts). TRUE
     *      on full success. Array of adjusted products if some products have been
     *      removed or their amounts adjusted (if option 'adjustAmounts' is TRUE).
     *      In such a case call EshopCart::setAdjustedProductsAppMessages() if you
     *      would like to inform user about changes in his cart. The returned array
     *      is never empty and has following format:
     *
     *          array(
     *              // removed product
     *              '{cartIndex01}' => array(
     *                  'id' => '{productId01}', 
     *                  'adjustment' => EshopCart::PRODUCT_REMOVED
     *              ),
     *              // product with decreased amount
     *              '{cartIndex02}' => array(
     *                  'id' => '{productId02}', 
     *                  'adjustment' => EshopCart::PRODUCT_AMOUNT_ADJUSTED
     *              ),
     *              ...
     *          )
     */
    public function updateProducts($products) {

        // get products which are orderable
        $products = $this->getOrderableProducts(array(
            'products' => $products,
            'validate' => true,
            'alternative' => 'update',
        ));
        // if products data are not valid then return false
        if ($products === false) {
            return false;
        }

        // load cart from the session
        $cartProducts = $this->getProducts();

        // array of updated items used to actualize child items in cart,
        // it contains pairs {cartIndex} => array('id' => {id}, 'amount' => {updatedAmountOrZeroForRemoved})
        $updatedItems = array();
        // update the cart content
        foreach ($cartProducts as $i => &$cartProduct) {
            // remove removed products
            if (
                isset($this->adjustedProducts[$i]) 
                && $this->adjustedProducts[$i]['adjustment'] == self::PRODUCT_REMOVED
            ) {
                unset($cartProducts[$i]);
                continue;
            }
            $hasFloatAmount = false;
            foreach($products as $product) {
                if ($i === (int)$product['cartIndex']) {
                //rbla//if ($this->compareProducts($cartProduct, $product)) {
                    // force amount 1 for absolute discount vouchers
                    if ($cartProduct['voucher']) {
                        $cartProduct['amount'] = 1;
                    }
                    elseif (!$product['amount']) {
                        unset($cartProducts[$i]);
                        $updatedItems[$i] = array(
                            'id' => $cartProduct['id'],
                            'amount' => 0, // 0 = removed
                        );
                    }
                    elseif (
                        $this->compareAmounts(
                            $cartProduct['amount'],
                            $product['amount'],
                            array(
                                'hasFloat' => &$hasFloatAmount,
                            )
                        ) !== 0
                    ) {
                        if ($hasFloatAmount) {
                            $cartProduct['amount'] = (float)$product['amount'];
                        }
                        else {
                            $cartProduct['amount'] = (int)$product['amount'];
                        }
                        $updatedItems[$i] = array(
                            'id' => $cartProduct['id'],
                            'amount' => $cartProduct['amount'],
                        );
                    }
                    break;
                }
            }
        }
        // unset reference
        unset($cartProduct);

        // adjust also related child and parent items
        if ($updatedItems) {
            foreach ($updatedItems as $updatedIndex => $updatedItem) {
                foreach ($cartProducts as $i => &$cartProduct) {
                    // adjust related child items
                    if ($cartProduct['parent_index'] === $updatedIndex) { // NULL and 0 are two different values, use strict comparison
                        // remove child items if parent item has been removed 
                        if (!$updatedItem['amount']) {                            
                            unset($cartProducts[$i]);
                        }
                        // update child items amount if parent amount has been changed
                        //rblb//elseif ((int) $cartProduct['amount'] > (int) $updatedItem['amount']) {
                        else {
                            $cartProduct['amount'] = $updatedItem['amount'];
                        }
                    }
                    // adjust related parent items
                    if (in_array($updatedIndex, $cartProduct['child_indexes'])) {
                        // delete relation if child item has been removed
                        if (!$updatedItem['amount']) {
                            $cartProduct['child_indexes'] = array_diff(
                                $cartProduct['child_indexes'], 
                                array($updatedIndex)
                            );
                            $cartProduct['child_ids'] = array_diff(
                                $cartProduct['child_ids'], 
                                array($updatedItem['id'])
                            );
                        }
                    }
                }
                // unset the reference
                unset($cartProduct);
            }
        }        
        $cartProducts = $this->applySpecialOffers($cartProducts);
        $this->setProducts($cartProducts);

        if ($this->adjustedProducts) {
            return $this->adjustedProducts;
        }
        return true;
    }

    /**
     * Returns cart items details. Retruned item details are identic to 
     * EshopProduct::getDetails() output + there are following additional virtual
     * item fields:
     *      - amount - item amount placed in the cart
     *      - cartIndex - index of cart item the item is placed as
     *
     * !!! Product prices are updated by static/dynamic attribute prices!
     *
     * !!! Be aware that the same product (accordint to id) can occure more than once
     * in the output items list! They are distibguished by attribute
     * values from others. That is why keys of returned array are not set to product
     * ids but to cart indexes (starting from 0, some of them may be missing if 
     * cast item has ben removed)
     *
     * @param array $options Options of method EshopProduct::getDetails() plus following:
     *      - 'getSpecialOffers' (bool) If TRUE then special offers are populated 
     *          and discounted product prices are set according to special offers 
     *          if thea are applicable (regardoing to cart content). Defaults to FALSE. 
     *      - 'synchronize' (bool) If TRUE then cart items are synchronized with stock
     *          before retrieveing their details. If you would like to inform user
     *          about changes in his cart after synchronization then call 
     *          EshopCart::setAdjustedProductsAppMessages(). Defaults to FALSE.
     *      - 'products' (array) Explicit list of items to get details for.
     *          It must have structure of cart items array returned by EshopCart::getProducts().
     *          If not an array then actual cart items details are returned.
     *          Defaults to NULL, it means actual cart items details are returned.
     *
     * @return array Cart items details 
     */
    public function getProductsDetails($options = array()) {
        $defaults = array(
            'getSpecialOffers' => false,
            'synchronize' => false,
            'products' => null,
        );
        $options = array_merge($defaults, $options);

        if ($options['synchronize']) {
            $this->synchronizeProducts();
            $this->synchronizeVoucher();
        }

        if (is_array($options['products'])) {
            $cartProducts = $this->normalize($options['products']);
        }
        else {
            $cartProducts = $this->getProducts();
        }
        if ($options['synchronize']) {
            $cartProducts = $this->applySpecialOffers($cartProducts);
            $this->setProducts($cartProducts);
        }
        // get additional discount rates (comming from voucher, etc...)
        $options['additionalDiscountRates'] = $this->getAdditionalDiscountRates($cartProducts);
        // retrieve cart product ids
        $productIds = array();
        foreach ($cartProducts as $cartProduct) {
            $productIds[] = $cartProduct['id'];
        }
        $productIds = array_unique($productIds);
        // get products details
        $Product = $this->loadModel('EshopProduct', true);
        $detailsOptions = $options;
        if ($options['getSpecialOffers']) {
            $detailsOptions['getSpecialOffers'] = 'withoutSpecialPrices';
        }
        $products = $Product->getDetails($productIds, $detailsOptions);
        if (!$products) {
            return array();
        }
        $this->loadModel('EshopSpecialOffer');
        $Offer = new EshopSpecialOffer();
        $offers = $Offer->getActive(array(
            'promotedProductIds' => $productIds,
            'cartPrice' => true,
            'discountedProductIds' => $productIds,
        ));
        // get serial/sort numbers of cart items
        $serialNumbers = array_flip(array_keys($cartProducts));
        // get cart product details (there can be more products with the same id but distinguished by attributes)
        $detailedProducts = array();
        foreach ($cartProducts as $cartIndex => $cartProduct) {
            $detailedProduct = $products[$cartProduct['id']];
            $detailedProduct['cartIndex'] = $cartIndex;
            $detailedProduct = array_merge($detailedProduct, $cartProduct);
            // remove additional services for following products:
            if (
                // absolute discount voucher
                $detailedProduct['voucher']
                // electronic gift card
                || (
                    $detailedProduct['is_gift_card']
                    && $detailedProduct['static_attributes']['variant'] === 'electronic'
                )
            ) {
                unset($detailedProduct['EshopAdditionalServiceProduct']);
            }
            // absolute discount voucher product inherits price of applied voucher code
            // and there are no discounts (neither direct nor additional) applied on it
            if ($detailedProduct['voucher']) {
                $detailedProduct['price'] = -abs($detailedProduct['voucher']['discount']);
                // multipurpose vouchers have no tax (the are used as a prepaid part of price to pay)
                if ($detailedProduct['voucher']['purpose_type'] === 'multi') {
                    $detailedProduct['tax_rate'] = 0;
                }
                unset($detailedProduct['discount_from']);
                unset($detailedProduct['discount_to']);
                // force recounting of actual price by unsetting following items
                // (it means there is no discount)
                unset($detailedProduct['discount_price']);
                unset($detailedProduct['discount_rate']);
                unset($detailedProduct['actual_price']);
                unset($detailedProduct['special_offer_id']);
                $detailedProduct = $Product->getPrices($detailedProduct);
            }            
            // if products special offers are loaded then on products having applied
            // a special offer (special_offer_id) put this offer as the first one and
            // set it as applicated
            elseif (
                $options['getSpecialOffers']
                && !empty($detailedProduct['special_offer_id'])
            ) {
                $offerId = $detailedProduct['special_offer_id'];
                if (
                    isset($detailedProduct['discounted_cart_index'])
                    && !empty($detailedProduct['PromotingSpecialOffer'][$offerId])
                ) {
                    foreach ($detailedProduct['PromotingSpecialOffer'] as &$offer) {
                        $offer['applicated'] = false;
                    }
                    unset($offer);
                    $detailedProduct['PromotingSpecialOffer'] = Arr::moveKeyToStart(
                        $detailedProduct['PromotingSpecialOffer'], $offerId
                    );
                    $detailedProduct['PromotingSpecialOffer'][$offerId]['applicable'] = true;
                    $detailedProduct['PromotingSpecialOffer'][$offerId]['applicated'] = true;
                }
                elseif (
                    (
                        isset($detailedProduct['promoted_cart_index'])
                        || isset($detailedProduct['cart_price_threshold'])
                    )
                    && !empty($detailedProduct['DiscountingSpecialOffer'][$offerId])
                ) {
                    foreach ($detailedProduct['DiscountingSpecialOffer'] as &$offer) {
                        $offer['applicated'] = false;
                    }
                    unset($offer);
                    $detailedProduct['DiscountingSpecialOffer'] = Arr::moveKeyToStart(
                        $detailedProduct['DiscountingSpecialOffer'], $offerId
                    );
                    $detailedProduct['DiscountingSpecialOffer'][$offerId]['applicable'] = true;
                    $detailedProduct['DiscountingSpecialOffer'][$offerId]['applicated'] = true;
                }
            }
            // apply special offer price to special offer discounted products
            if (
                !empty($detailedProduct['special_offer_id'])
                && !empty($offers[$detailedProduct['special_offer_id']])
                && (
                    isset($detailedProduct['promoted_cart_index'])
                    || isset($detailedProduct['cart_price_threshold'])
                )
            ) {
                // force recounting of actual price by unsetting following prices
                // (discount_rate is preserved to force recalculation by discount_rate)
                unset($detailedProduct['discount_price']);
                unset($detailedProduct['actual_price']);
                $options['discountingSpecialOffers'] = array(
                    $detailedProduct['special_offer_id'] => $offers[$detailedProduct['special_offer_id']]
                );
                $detailedProduct = $Product->getPrices($detailedProduct, $options);
            }
            else {
                $detailedProduct = $Product->updatePricesByAttributes($detailedProduct, array(
                    'staticAttributes' => $detailedProduct['static_attributes'],
                    'dynamicAttributes' => $detailedProduct['dynamic_attributes'],
                ));
            }
            if ($detailedProduct['voucher']) {
                $detailedProduct['name'] = 
                    trim($detailedProduct['name']) . ' ' . $detailedProduct['voucher']['code'];
            }
            if (isset($cartProduct['parent_index'])) {
                $detailedProduct['name'] .= __(
                    __FILE__, 
                    ' (k položke č. %s)', 
                    $serialNumbers[$cartProduct['parent_index']] + 1
                );
            }
            $detailedProducts[$cartIndex] = $detailedProduct;
        }

        return $detailedProducts;
    }

    /**
     * Gets common prices required for cart view or order checkout. Following
     * items are set in output array:
     *  - 'products_price_taxless'
     *  - 'products_price_taxed'
     *  - 'products_tax'
     *  - 'products_price_actual_taxless'
     *  - 'products_price_actual_taxed'
     *  - 'products_tax_actual'
     *  - 'products_savings_taxless'
     *  - 'products_savings_taxed'
     *  - 'vouchers_discount' - multi purpose absolute discount vouchers total.
     *      It is a prepaid part of price to pay. It can be either taxed or taxless value 
     *      depending on order type (with or without VAT).
     *  - 'bonus_discount' - this is not discount, it is a prepaid part of price to pay.
     *      It can be either taxed or taxless value depending on order type (with or without VAT).
     *  - 'products_price_to_pay' - final products price (after substracting all discounts) to be paid.
     *      It can be either taxed or taxless value depending on order type (with or without VAT).
     *
     * @param array $products Output of EshopCart::getProductsDetails() method
     * @param array $options Following are available:
     *      - 'precision' (int|bool) Number of decimals of returned prices. If FALSE
     *          then no rounding is made internally. Defaults to 2.
     *
     * @return array Array with the keys listed here above.
     */
    public function getPrices($products, $options = array()) {
        $defaults = array(
            'precision' => 2,
        );
        $options = array_merge($defaults, $options);
        
        $priceActualTaxlessTotal = $priceActualTaxedTotal= $priceTaxlessTotal = 0;
        $priceTaxedTotal = $savingsTaxlessTotal = $savingsTaxedTotal = $taxTotal = $taxActualTotal = 0;
        // this is used for single purpose absolute discount vouchers
        $vouchersTaxlessTotal = $vouchersTaxedTotal = 0;
        // this is used for multi purpose absolute discount vouchers
        $vouchersDiscount = 0;
        foreach($products as $product) {   
            // @synchro with EshopOrder::saveAll()
            // 
            // normal products or single purpose absolute discount voucher (See 
            // phpDoc of EshopVoucher::$schema > 'purpose_type')
            if (
                empty($product['voucher'])
                || $product['voucher']['purpose_type'] === 'single'
            ) {                
                // into actual products price count both
                $priceActualTaxlessTotal += $product['price_actual_taxless'] * $product['amount'];
                $priceActualTaxedTotal += $product['price_actual_taxed'] * $product['amount'];
                $taxActualTotal += $product['tax_actual'] * $product['amount'];
                // full price count only for normal products (there are no vouchers and discounts in full price)
                if (empty($product['voucher'])) {
                    $priceTaxlessTotal += $product['price_taxless'] * $product['amount'];
                    $priceTaxedTotal += $product['price_taxed'] * $product['amount'];
                    $taxTotal += $product['tax'] * $product['amount'];
                    $savingsTaxlessTotal += $product['savings_taxless'] * $product['amount'];
                    $savingsTaxedTotal += $product['savings_taxed'] * $product['amount'];
                }
                // get the taxed and taxless total of all single purpose absolute discount vouchers
                else {
                    $vouchersTaxlessTotal += abs($product['price_taxless']) * $product['amount'];
                    $vouchersTaxedTotal += abs($product['price_taxed']) * $product['amount'];
                }
            }
            // multi purpose absolute discount voucher keep separately (do not count them 
            // to products price) as their price is a kind of prepaid part of price to pay.
            // See phpDoc of EshopVoucher::$schema > 'purpose_type'.
            else {
                $vouchersDiscount += abs($product['price_actual_taxed']) * $product['amount'];
            }
        }
        
        App::loadModel('App', 'UserProfile');
        $UserProfile = new UserProfile();
        $bonusDiscount = $UserProfile->getBonusDiscount();
        
        // if actual price is less than zero then set it to zero and adjust accordingly
        // voucher totals (this can happen only when absolute discount vouchers are applied)
        if ($priceActualTaxedTotal <= 0) {
            $vouchersTaxlessTotal += $priceActualTaxlessTotal;
            $vouchersTaxedTotal += $priceActualTaxedTotal;
            $priceActualTaxlessTotal = 0.0;
            $priceActualTaxedTotal = 0.0;
            $taxActualTotal = 0.0;
        }
        
        // increase the savings by single purpose absolute discount vouchers 
        $savingsTaxlessTotal += $vouchersTaxlessTotal;
        $savingsTaxedTotal += $vouchersTaxedTotal;
        
        // @todo - resolve if the order is with VAT or without VAT
        $taxedOrder = true;
        $priceToPay = $taxedOrder ? $priceActualTaxedTotal : $priceActualTaxlessTotal;
        if ($vouchersDiscount || $bonusDiscount) {
            $priceToPay = $priceToPay - $vouchersDiscount - $bonusDiscount;
        }
        if ($priceToPay < 0) {
            $priceToPay = 0.0;
        }
        
        $prices = array(
            'products_price_taxless' => $priceTaxlessTotal,
            'products_price_taxed' => $priceTaxedTotal,
            'products_tax' => $taxTotal,
            'products_price_actual_taxless' => $priceActualTaxlessTotal,
            'products_price_actual_taxed' => $priceActualTaxedTotal,
            'products_tax_actual' => $taxActualTotal,
            'products_savings_taxless' => $savingsTaxlessTotal,
            'products_savings_taxed' => $savingsTaxedTotal,
            'vouchers_discount' => $vouchersDiscount,
            'bonus_discount' => $bonusDiscount,
            'products_price_to_pay' => $priceToPay,
        );
        if ($options['precision'] !== false) {
            foreach ($prices as &$price) {
                $price = round($price, $options['precision']);
            }
            unset($price);
        }
        return $prices;
    }

    /**
     * Returns total weight of cart products
     *
     * @param array $products Output of EshopCart::getProductsDetails() method
     *
     * @return float Total cart weight
     */
    public function getWeight($products) {
        $weight = 0.0;
        foreach($products as $product) {
            if (empty($product['weight'])) {
                continue;
            }
            $weight += (float)$product['weight'] * $product['amount'];
        }
        return $weight;
    }

    /**
     * Returns array of additional discount rates to be used as EshopProduct::getDetail()
     * 'additionalDiscountRates' option.
     *
     * Additional discount rates come from voucher.
     *
     * @param array& $cartProducts Cart products. They are passed by reference and
     *      if voucher has special_discount_rate defined then they are changed by
     *      method - field eshop_voucher_discount_rate is added to each of them.
     *
     * @return array
     */
    protected function getAdditionalDiscountRates(&$cartProducts) {
        $additionalDiscountRates = array();
        // apply voucher discount rate
        if (($voucher = $this->getVoucher())) {
            // !!! VYDAVATEL SPECIFIC discount applying
            // voucher special_discount_rate is applied to all vydavatel products
            // and voucher discount_rate applies to other products. It means that
            // discount is applied separately to each product
            if (!empty($voucher['special_discount_rate'])) {
                $productIds = array();
                foreach ($cartProducts as $cartProduct) {
                    $productIds[] = $cartProduct['id'];
                }
                $productIds = array_unique($productIds);
                $Product = $this->loadModel('EshopProduct', true);
                $productManufacturers = $Product->findList(array(
                    'fields' => array('run_eshop_manufacturers_id'),
                    'conditions' => array('id' => $productIds),
                ));
                $voucherDiscounts = array();
                $vydavatelId = 3; //HARDCODED
                foreach ($cartProducts as &$cartProduct) {
                    $productId = $cartProduct['id'];
                    if (
                        !empty($productManufacturers[$productId])
                        && $productManufacturers[$productId] == $vydavatelId
                    ) {
                        $voucherDiscounts[$productId] = $voucher['special_discount_rate'];
                    }
                    else {
                        $voucherDiscounts[$productId] = $voucher['discount_rate'];
                    }
                    $cartProduct['eshop_voucher_discount_rate'] = $voucherDiscounts[$productId];
                }
                unset($cartProduct);
                $additionalDiscountRates[] = $voucherDiscounts;
            }
            // if no special discount defined the same discount is applied to all products
            else {
                foreach ($cartProducts as &$cartProduct) {
                    $cartProduct['eshop_voucher_discount_rate'] = $voucher['discount_rate'];
                }
                unset($cartProduct);
                $additionalDiscountRates[] = $voucher['discount_rate'];
            }
        }
        else {
            foreach ($cartProducts as &$cartProduct) {
                $cartProduct['eshop_voucher_discount_rate'] = null;
            }
            unset($cartProduct);
        }
        return $additionalDiscountRates;
    }

    //
    // SPECIAL OFFERS
    //
    // Special offers add following fields to cart products:
    // - 'special_offer_id' (int) Id of applied special offer. It is set on both promoted
    //      and discounted products. It can be set also on new coming discounted products with meaning
    //      of id of proposed special offer (user has seen applied price of proposed special offer when
    //      adding discounted product to cart). In each case, id of proposed special offer is only transactional
    //      state and once special offers are applied to cart products it is id of applied special offer.
    // - 'discounted_cart_index' (int|array) Cart index of paired discounted product. It is set
    //      on promoted products in case of special offers applied by promoted products in cart
    //      and special offers applied by promoted products cart price. In case of special offers 
    //      applied by promoted products cart price it is an array of indexes of all offers discounted products 
    //      placed in cart. Keys and values of this array are identical, it means promoted products indexes 
    //      are used both as key and value for the same entry.
    // - 'promoted_cart_index' (int|array) Cart index of paired promoted product. It is set
    //      on discounted products in case of special offers applied by promoted products in cart
    //      and special offers applied by promoted products cart price. In case of special offers 
    //      applied by promoted products cart price it is an array of indexes of all offers promoted products 
    //      placed in cart. Keys and values of this array are identical, it means promoted products indexes 
    //      are used both as key and value for the same entry.
    // - 'cart_price_threshold' (float) Applied cart price threshold set on discounted products
    //      in case of special offers applied by cart price threshold and special offers applied 
    //      by promoted products cart price
    //
    // Products which have no special offer applied have not the above mentioned fields.
    //
    // See also the phpDoc of EshopSpecialOffer::$validations in constructor of EshopSpecialOffer
    // and misc/docs/implementationDetails.txt > ŠPECIÁLNE PONUKY
    //

    /**
     * ATTENTION: This method possibly changes cart products but it does not update
     * them in cookie storage so be sure to use Eshopcart::setProducts() after each 
     * use of this method!
     *
     * @param array $cartProducts Cart products
     * @return array updated cart products
     */
    protected function applySpecialOffers($cartProducts) {
        if (!$cartProducts) {
            return $cartProducts;
        }
        // remove all inactive offers (synchronize with actual state)
        // and offers with changed 'apply_by'
        // and get list of all original discounted products
        $this->loadModel('EshopSpecialOffer');
        $Offer = new EshopSpecialOffer();
        $offers = $Offer->getActive();
        $originalDiscountedProducts = array();
        foreach ($cartProducts as $cartIndex => &$cartProduct) {
            if (isset($cartProduct['special_offer_id'])) {
                if (
                    isset($cartProduct['promoted_cart_index'])
                    || isset($cartProduct['cart_price_threshold'])
                    || 
                    // take also proposed products as original discounted
                    !isset($cartProduct['promoted_cart_index'])
                    && !isset($cartProduct['discounted_cart_index'])
                    && !isset($cartProduct['cart_price_threshold'])
                ) {
                    $originalDiscountedProducts[$cartIndex] = array(
                        'id' => $cartProduct['id'],
                        'special_offer_id' => $cartProduct['special_offer_id'],
                        'amount' => $cartProduct['amount'],
                    );
                }
                if (
                    // remove deactivated offers
                    empty($offers[$cartProduct['special_offer_id']])
                    ||
                    // remove offers with changed apply_by
                    ($offer = $offers[$cartProduct['special_offer_id']])
                    && (
                        (
                            isset($cartProduct['promoted_cart_index'])
                            && !is_array($cartProduct['promoted_cart_index'])
                            ||
                            isset($cartProduct['discounted_cart_index'])
                            && !is_array($cartProduct['discounted_cart_index'])
                        )
                        && $offer['apply_by'] !== 'promoted_products_in_cart'
                        ||
                        (
                            isset($cartProduct['promoted_cart_index'])
                            && is_array($cartProduct['promoted_cart_index'])
                            ||
                            isset($cartProduct['discounted_cart_index'])
                            && is_array($cartProduct['discounted_cart_index'])
                        )
                        && $offer['apply_by'] !== 'promoted_products_cart_price_threshold'
                        ||
                        isset($cartProduct['cart_price_threshold'])
                        && !isset($cartProduct['promoted_cart_index'])
                        && $offer['apply_by'] !== 'cart_price_threshold'
                    )
                ) {
                    unset($cartProduct['special_offer_id']);
                    unset($cartProduct['promoted_cart_index']);
                    unset($cartProduct['discounted_cart_index']);
                    unset($cartProduct['cart_price_threshold']);
                }
            }
        }
        unset($cartProduct);

        // check if existing active offers are still applicable in changed cart
        // (after update or delete)
        // 
        // 1] for special offers applied by promoted products in cart and by promoted 
        // products cart price check if amounts of paired products match. If not then
        // in case of offers applied by promoted products in cart separate unmatching 
        // and matching part of amount and in case offers applied by promoted products
        // cart price just actualize arrays of promoted_cart_index and discounted_cart_index
        $newCartIndex = max(array_keys($cartProducts));
        // cart indexes can be unordered / mixed so avoid sequential for() loop
        foreach (array_keys($cartProducts) as $i) {
            if (isset($cartProducts[$i]['promoted_cart_index'])) {
                $pairedIndex = $cartProducts[$i]['promoted_cart_index'];
                // special offers applied by promoted products cart price
                if (is_array($pairedIndex)) {
                    $pairedIndexes = $pairedIndex;
                    // check if some of paired promoted products have been removed
                    foreach ($pairedIndexes as $pairedIndex) {
                        if (!isset($cartProducts[$pairedIndex])) {
                            unset($cartProducts[$i]['promoted_cart_index'][$pairedIndex]);
                        }
                    }
                    // if all paired promoted products have been removed then unset 
                    // pairing properties of actual discounted product
                    if (empty($cartProducts[$i]['promoted_cart_index'])) {
                        unset($cartProducts[$i]['special_offer_id']);
                        unset($cartProducts[$i]['promoted_cart_index']);
                        unset($cartProducts[$i]['cart_price_threshold']);
                    }
                }
                // special offers applied by promoted products in cart
                else {
                    // if paired product has been removed then unset pairing properties of actual product
                    if (!isset($cartProducts[$pairedIndex])) {
                        unset($cartProducts[$i]['special_offer_id']);
                        unset($cartProducts[$i]['promoted_cart_index']);
                    }
                    elseif ($cartProducts[$i]['amount'] < $cartProducts[$pairedIndex]['amount']) {
                        $newCartIndex++;
                        $cartProducts[$newCartIndex] = $cartProducts[$pairedIndex];
                        $cartProducts[$newCartIndex]['amount'] = $cartProducts[$pairedIndex]['amount'] -
                            $cartProducts[$i]['amount'];
                        unset($cartProducts[$newCartIndex]['special_offer_id']);
                        unset($cartProducts[$newCartIndex]['discounted_cart_index']);
                        $cartProducts[$pairedIndex]['amount'] = $cartProducts[$i]['amount'];
                    }
                    elseif ($cartProducts[$i]['amount'] > $cartProducts[$pairedIndex]['amount']) {
                        $newCartIndex++;
                        $cartProducts[$newCartIndex] = $cartProducts[$i];
                        $cartProducts[$newCartIndex]['amount'] = $cartProducts[$i]['amount'] -
                            $cartProducts[$pairedIndex]['amount'];
                        unset($cartProducts[$newCartIndex]['special_offer_id']);
                        unset($cartProducts[$newCartIndex]['promoted_cart_index']);
                        $cartProducts[$i]['amount'] = $cartProducts[$pairedIndex]['amount'];
                    }
                }
            }
            elseif (isset($cartProducts[$i]['discounted_cart_index'])) {
                $pairedIndex = $cartProducts[$i]['discounted_cart_index'];
                // special offers applied by promoted products cart price
                if (is_array($pairedIndex)) {
                    $pairedIndexes = $pairedIndex;
                    // check if some of paired discounted products have been removed
                    foreach ($pairedIndexes as $pairedIndex) {
                        if (!isset($cartProducts[$pairedIndex])) {
                            unset($cartProducts[$i]['discounted_cart_index'][$pairedIndex]);
                        }
                    }
                    // if all paired discounted products have been removed then unset 
                    // pairing properties of actual promoted product
                    if (empty($cartProducts[$i]['discounted_cart_index'])) {
                        unset($cartProducts[$i]['special_offer_id']);
                        unset($cartProducts[$i]['discounted_cart_index']);
                    }
                }
                // special offers applied by promoted products in cart
                else {
                    // if paired product has been removed then unset pairing properties of actual product
                    if (!isset($cartProducts[$pairedIndex])) {
                        unset($cartProducts[$i]['special_offer_id']);
                        unset($cartProducts[$i]['discounted_cart_index']);
                    }
                    elseif ($cartProducts[$i]['amount'] < $cartProducts[$pairedIndex]['amount']) {
                        $newCartIndex++;
                        $cartProducts[$newCartIndex] = $cartProducts[$pairedIndex];
                        $cartProducts[$newCartIndex]['amount'] = $cartProducts[$pairedIndex]['amount'] -
                            $cartProducts[$i]['amount'];
                        unset($cartProducts[$newCartIndex]['special_offer_id']);
                        unset($cartProducts[$newCartIndex]['promoted_cart_index']);
                        $cartProducts[$pairedIndex]['amount'] = $cartProducts[$i]['amount'];
                    }
                    elseif ($cartProducts[$i]['amount'] > $cartProducts[$pairedIndex]['amount']) {
                        $newCartIndex++;
                        $cartProducts[$newCartIndex] = $cartProducts[$i];
                        $cartProducts[$newCartIndex]['amount'] = $cartProducts[$i]['amount'] -
                            $cartProducts[$pairedIndex]['amount'];
                        unset($cartProducts[$newCartIndex]['special_offer_id']);
                        unset($cartProducts[$newCartIndex]['discounted_cart_index']);
                        $cartProducts[$i]['amount'] = $cartProducts[$pairedIndex]['amount'];
                    }
                }
            }
        }
        // 2] for special offers applied by cart price or by promoted products cart price
        // check if free prices are higher or equal to 0. If not then unpair products
        // till it is ok.
        $availableCartPrice = $this->getPriceWithoutAppliedSpecialOffer(array(
            'products' => $cartProducts,
        ));
        $obsoleteOffers = array();
        foreach ($availableCartPrice as $offerId => $offerFreePrice) {
            if ($offerFreePrice < 0) {
                $obsoleteOffers[$offerId] = $offerFreePrice;
            }
        }
        if ($obsoleteOffers) {
            // unpair products of all obsolete offers and let apply offers to them 
            // once again here below 
            foreach ($cartProducts as $cartIndex => &$cartProduct) {
                if (
                    // discounted products of offers applyed by cart price
                    isset($obsoleteOffers['total'])
                    && isset($cartProduct['cart_price_threshold'])
                    && !isset($cartProduct['promoted_cart_index'])
                    || 
                    // discounted and promoted products of offers applyed by 
                    // promoted products cart price
                    isset($cartProduct['special_offer_id'])
                    && isset($obsoleteOffers[$cartProduct['special_offer_id']])
                    && (
                        isset($cartProduct['promoted_cart_index'])
                        || isset($cartProduct['discounted_cart_index'])
                    )
                ) {
                    unset($cartProduct['special_offer_id']);
                    unset($cartProduct['promoted_cart_index']);
                    unset($cartProduct['discounted_cart_index']);
                    unset($cartProduct['cart_price_threshold']);
                }
            }
            unset($cartProduct);
        }

        // find unpaired products and proposed discounting offers of new added products
        $productIds = array();
        $proposedOffers = array();
        foreach ($cartProducts as $cartIndex => $cartProduct) {
            if (
                !isset($cartProduct['promoted_cart_index'])
                && (
                    !isset($cartProduct['discounted_cart_index'])
                    // offers applied by promoted products cart price are never considered to be
                    // definitely paired, promoted and discounted products are loosely paired
                    || is_array($cartProduct['discounted_cart_index'])
                )
                && !isset($cartProduct['cart_price_threshold'])
            ) {
                $productIds[$cartIndex] = $cartProduct['id'];
                if (
                    !empty($cartProduct['special_offer_id'])
                    && !isset($cartProduct['discounted_cart_index'])
                ) {
                    // normally this should happen only to one new added product
                    // so ignore the fact that the same product id can occure more
                    // than once in cart. Moreover one product can be placed only
                    // to one active special offer
                    $proposedOffers[$cartProduct['id']] = $cartProduct['special_offer_id'];
                }
            }
        }
        // get cart indexes of retrieved product ids and make product ids unique as there can be
        // two same products on two different indexes in cart. It can happen if
        // two same products differs in attributes or if they are paired with different
        // special offer products (see here below)
        $cartIndexes = array();
        foreach ($productIds as $cartIndex => $productId) {
            if (!isset($cartIndexes[$productId])) {
                $cartIndexes[$productId] = array();
            }
            $cartIndexes[$productId][] = $cartIndex;
        }
        $productIds = array_keys($cartIndexes);

        // find active special offers for unpaired products
        $offers = $Offer->getActive(array(
            'promotedProductIds' => $productIds,
            'cartPrice' => true,
            'discountedProductIds' => $productIds,
        ));
        // apply special offers to unpaired products
        if ($offers) {
            // find optimal combinations of promoted and discounted products.
            //
            // From customer point of view it is the most interesting to get the highest
            // (absolute) savings on discounted product for the lowest price of promoted product.
            // Offers applied by cart price are prefered over offers applied by promoted products,
            // as custommer is free to buy/choose "promoted" products to reach cart price threshold.
            // The resulting price of discounted product is not so important as in each case 
            // customer is interested in product. That is why the savings coefficient is calcualated as:
            //
            //      $sc = {promotedProductActualPrice} - {discountedProductSpecialSavings}
            //
            // and we will look for lowest values of this coefficient.
            // The {promotedProductActualPrice} equals to real promoted product price
            // or to cart price threshold in case of offers applied by cart price.
            
            $this->loadModel('EshopProduct');
            $Product = new EshopProduct();
            $detailedProducts = $Product->getDetails($productIds);
            $availableCartPrice = $this->getPriceWithoutAppliedSpecialOffer(array(
                'products' => $cartProducts,
            ));
            $coefficients = array();
            foreach ($offers as $offerId => $offer) {
                foreach($offer['matched_discounted_product_ids'] as $discountedProductId) {
                    // if discounted product has a proposed offer (possible in case of new
                    // added products) then consider only the proposed one and ignore all other offers
                    if (
                        !empty($proposedOffers[$discountedProductId])
                        && (int)$proposedOffers[$discountedProductId] !== (int)$offerId
                    ) {
                        continue;
                    }
                    $price = $detailedProducts[$discountedProductId]['price_actual_taxed'];
                    $specialPrice = $Offer->applyPriceAdjustment($price, $offer['discounted_products_price_adjustment']);
                    $savings = $price - $specialPrice;
                    if ($offer['apply_by'] === 'promoted_products_in_cart') {
                        foreach($offer['matched_promoted_product_ids'] as $promotedProductId) {
                            $price = $detailedProducts[$promotedProductId]['price_actual_taxed'];
                            // !!! convert to string otherwise e.g. float -1.8 will change to -1 array integer index
                            $coefficient = (string)($price - $savings);
                            if (!isset($coefficients[$coefficient][$discountedProductId][$offerId]['promoted_product_ids'])) {
                                $coefficients[$coefficient][$discountedProductId][$offerId]['promoted_product_ids'] = array();
                                $coefficients[$coefficient][$discountedProductId][$offerId]['apply_by'] = $offer['apply_by'];
                                $coefficients[$coefficient][$discountedProductId][$offerId]['discounted_products_price_adjustment'] = 
                                    $offer['discounted_products_price_adjustment'];
                            }
                            $coefficients[$coefficient][$discountedProductId][$offerId]['promoted_product_ids'][] = $promotedProductId;
                        }
                    }
                    elseif (
                        $offer['apply_by'] === 'cart_price_threshold'
                        && $offer['cart_price_threshold'] <= $availableCartPrice['total']
                        ||
                        $offer['apply_by'] === 'promoted_products_cart_price_threshold'
                        && isset($availableCartPrice[$offerId])
                        && $offer['cart_price_threshold'] <= $availableCartPrice[$offerId]
                    ) {
                        // !!! convert to string otherwise e.g. float -1.8 will change to -1 array integer index
                        $coefficient = (string)($offer['cart_price_threshold'] - $savings);
                        $coefficients[$coefficient][$discountedProductId][$offerId]['cart_price_threshold'] = $offer['cart_price_threshold'];
                        $coefficients[$coefficient][$discountedProductId][$offerId]['apply_by'] = $offer['apply_by'];
                        $coefficients[$coefficient][$discountedProductId][$offerId]['discounted_products_price_adjustment'] = 
                            $offer['discounted_products_price_adjustment'];
                        if ($offer['apply_by'] === 'promoted_products_cart_price_threshold') {
                            $coefficients[$coefficient][$discountedProductId][$offerId]['promoted_product_ids'] 
                                = $offer['matched_promoted_product_ids'];
                        }
                    }
                }
            }
            ksort($coefficients, SORT_NUMERIC);
            $lastCartIndex = max(array_keys($cartProducts));
            foreach ($coefficients as $discountedProductIds) {
                foreach ($discountedProductIds as $discountedProductId => $offerIds) {
                    foreach ($offerIds as $offerId => $offer) {
                        // pair discounted products with proposed promoted products
                        if (
                            $offer['apply_by'] === 'promoted_products_in_cart'
                            && isset($offer['promoted_product_ids'])
                        ) {
                            // prefer matching amounts and promoted products on stock or at supplier
                            $pairedCartProducts = array();
                            foreach ($cartIndexes[$discountedProductId] as $discountedCartIndex) {
                                $discountedProduct = $detailedProducts[$discountedProductId];
                                $discountedProductAmount = $cartProducts[$discountedCartIndex]['amount'];
                                foreach ($offer['promoted_product_ids'] as $promotedProductId) {
                                    $promotedProduct = $detailedProducts[$promotedProductId];
                                    $coefficient = 0;
                                    if ($discountedProduct['disponibility'] === $promotedProduct['disponibility']) {
                                        $coefficient += 2;

                                    }
                                    elseif (
                                        $promotedProduct['disponibility'] === EshopProduct::STOCK
                                        || $promotedProduct['disponibility'] === EshopProduct::SUPPLIER
                                    ) {
                                        $coefficient += 1;
                                    }
                                    foreach ($cartIndexes[$promotedProductId] as $promotedCartIndex) {
                                        $promotedProductAmount = $cartProducts[$promotedCartIndex]['amount'];
                                        if ($discountedProductAmount === $promotedProductAmount) {
                                            $coefficient += 3;
                                        }
                                        if (!isset($pairedCartProducts[$coefficient][$discountedCartIndex])) {
                                            $pairedCartProducts[$coefficient][$discountedCartIndex] = array();
                                        }
                                        $pairedCartProducts[$coefficient][$discountedCartIndex][] = $promotedCartIndex;
                                    }
                                }
                            }
                            arsort($pairedCartProducts, SORT_NUMERIC);
                            // create pairs
                            foreach ($pairedCartProducts as $discountedCartIndexes) {
                                foreach ($discountedCartIndexes as $discountedCartIndex => $promotedCartIndexes) {
                                    // skip meanwhile paired discounted products
                                    if (
                                        isset($cartProducts[$discountedCartIndex]['promoted_cart_index'])
                                        || isset($cartProducts[$discountedCartIndex]['cart_price_threshold'])
                                    ) {
                                        continue;
                                    }
                                    foreach ($promotedCartIndexes as $promotedCartIndex) {
                                        // skip meanwhile paired promoted products
                                        if (isset($cartProducts[$promotedCartIndex]['discounted_cart_index'])) {
                                            continue;
                                        }
                                        $discountedProductAmount = $cartProducts[$discountedCartIndex]['amount'];
                                        $promotedProductAmount = $cartProducts[$promotedCartIndex]['amount'];
                                        if ($discountedProductAmount === $promotedProductAmount) {
                                            $cartProducts[$discountedCartIndex]['special_offer_id'] = $offerId;
                                            $cartProducts[$discountedCartIndex]['promoted_cart_index'] = $promotedCartIndex;
                                            $cartProducts[$promotedCartIndex]['special_offer_id'] = $offerId;
                                            $cartProducts[$promotedCartIndex]['discounted_cart_index'] = $discountedCartIndex;
                                        }
                                        elseif ($discountedProductAmount < $promotedProductAmount) {
                                            $lastCartIndex++;
                                            $cartProducts[$discountedCartIndex]['special_offer_id'] = $offerId;
                                            $cartProducts[$discountedCartIndex]['promoted_cart_index'] = $lastCartIndex;
                                            $cartProducts[$lastCartIndex] = $cartProducts[$promotedCartIndex];
                                            $cartProducts[$lastCartIndex]['amount'] = $discountedProductAmount;
                                            $cartProducts[$lastCartIndex]['special_offer_id'] = $offerId;
                                            $cartProducts[$lastCartIndex]['discounted_cart_index'] = $discountedCartIndex;
                                            $cartProducts[$promotedCartIndex]['amount'] = $promotedProductAmount - $discountedProductAmount;
                                        }
                                        else {
                                            $lastCartIndex++;
                                            $cartProducts[$lastCartIndex] = $cartProducts[$discountedCartIndex];
                                            $cartProducts[$lastCartIndex]['amount'] = $promotedProductAmount;
                                            $cartProducts[$lastCartIndex]['special_offer_id'] = $offerId;
                                            $cartProducts[$lastCartIndex]['promoted_cart_index'] = $promotedCartIndex;
                                            $cartProducts[$discountedCartIndex]['amount'] = $discountedProductAmount - $promotedProductAmount;
                                            $cartProducts[$promotedCartIndex]['special_offer_id'] = $offerId;
                                            $cartProducts[$promotedCartIndex]['discounted_cart_index'] = $lastCartIndex;
                                        }
                                        // update $availableCartPrice['total'] after discounted product price change
                                        $price = $detailedProducts[$discountedProductId]['price_actual_taxed'];
                                        $specialPrice = $Offer->applyPriceAdjustment($price, $offer['discounted_products_price_adjustment']);
                                        if ($discountedProductAmount > $promotedProductAmount) {
                                            $availableCartPrice['total'] += $promotedProductAmount * ($specialPrice - $price);
                                        }
                                        else {
                                            $availableCartPrice['total'] += $discountedProductAmount * ($specialPrice - $price);
                                        }
                                    }
                                }
                            }
                        }
                        // pair discounted products with available cart price
                        elseif (
                            $offer['apply_by'] === 'cart_price_threshold'
                            && $offer['cart_price_threshold'] <= $availableCartPrice['total']
                            ||
                            $offer['apply_by'] === 'promoted_products_cart_price_threshold'
                            && isset($availableCartPrice[$offerId])
                            && $offer['cart_price_threshold'] <= $availableCartPrice[$offerId]
                        ) {
                            $priceKey = 'total';
                            if ($offer['apply_by'] === 'promoted_products_cart_price_threshold') {
                                $priceKey = $offerId;
                            }
                            foreach ($cartIndexes[$discountedProductId] as $discountedCartIndex) {
                                // skip meanwhile paired discounted products
                                if (
                                    isset($cartProducts[$discountedCartIndex]['promoted_cart_index'])
                                    || isset($cartProducts[$discountedCartIndex]['cart_price_threshold'])
                                ) {
                                    continue;
                                }
                                $discountedProductAmount = $cartProducts[$discountedCartIndex]['amount'];
                                $applicableAmount = (int)($availableCartPrice[$priceKey] / $offer['cart_price_threshold']);
                                // check if $availableCartPrice['total'] is still acceptable (even after change of price of
                                // this discounted product) and if yes then update it
                                $price = $detailedProducts[$discountedProductId]['price_actual_taxed'];
                                $specialPrice = $Offer->applyPriceAdjustment($price, $offer['discounted_products_price_adjustment']);
                                if ($discountedProductAmount <= $applicableAmount) {
                                    $newAvailableCartPriceTotal = 
                                        $availableCartPrice['total'] + $discountedProductAmount * ($specialPrice - $price);
                                }
                                else {
                                    $newAvailableCartPriceTotal = 
                                        $availableCartPrice['total'] + $applicableAmount * ($specialPrice - $price);
                                }
                                if (
                                    $priceKey === 'total'
                                    && $offer['cart_price_threshold'] > $newAvailableCartPriceTotal
                                ) {
                                    continue;
                                }
                                $availableCartPrice['total'] = $newAvailableCartPriceTotal;
                                if ($discountedProductAmount <= $applicableAmount) {
                                    $indexKey = $discountedCartIndex;
                                    $cartProducts[$discountedCartIndex]['special_offer_id'] = $offerId;
                                    $cartProducts[$discountedCartIndex]['cart_price_threshold'] = $offer['cart_price_threshold'];
                                    $availableCartPrice[$priceKey] -= $discountedProductAmount * $offer['cart_price_threshold'];
                                }
                                else {
                                    $lastCartIndex++;
                                    $indexKey = $lastCartIndex;
                                    $cartProducts[$lastCartIndex] = $cartProducts[$discountedCartIndex];
                                    $cartProducts[$lastCartIndex]['amount'] = $applicableAmount;
                                    $cartProducts[$lastCartIndex]['special_offer_id'] = $offerId;
                                    $cartProducts[$lastCartIndex]['cart_price_threshold'] = $offer['cart_price_threshold'];
                                    $cartProducts[$discountedCartIndex]['amount'] = $discountedProductAmount - $applicableAmount;
                                    $availableCartPrice[$priceKey] -= $applicableAmount * $offer['cart_price_threshold'];
                                }
                                // set 'special_offer_id', 'promoted_cart_index' and 'discounted_cart_index' 
                                // for offers applied by promoted products cart price
                                if ($offer['apply_by'] === 'promoted_products_cart_price_threshold') {
                                    if (!isset($cartProducts[$indexKey]['promoted_cart_index'])) {
                                        $cartProducts[$indexKey]['promoted_cart_index'] = array();
                                    }
                                    foreach ($offer['promoted_product_ids'] as $promotedProductId) {
                                        foreach ($cartIndexes[$promotedProductId] as $promotedCartIndex) {
                                            $cartProducts[$indexKey]['promoted_cart_index'][$promotedCartIndex] = $promotedCartIndex;
                                            if (!isset($cartProducts[$promotedCartIndex]['discounted_cart_index'])) {
                                                $cartProducts[$promotedCartIndex]['discounted_cart_index'] = array();
                                            }
                                            $cartProducts[$promotedCartIndex]['special_offer_id'] = $offerId;
                                            $cartProducts[$promotedCartIndex]['discounted_cart_index'][$indexKey] = $indexKey;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        // if available cart price total has got meanwhile lower that 0 then reiterate
        // the discounted products starting from end (considering savings coeficients)
        // and remove them till available cart price total will get 0 or higher.
        // This should happen only if together with special offer applied by cart price
        // there is at least one another active special offer applied by promoted products
        // in cart.
        if ($availableCartPrice['total'] < 0) {
////@todo (190214)            
//            $coefficients = array_reverse($coefficients, true);
//            foreach ($coefficients as $discountedProductIds) {
//                foreach ($discountedProductIds as $discountedProductId => $offerIds) {
//                    foreach ($offerIds as $offerId => $offer) {
//                        //...
//                    }
//                }
//            }
        }
        // look for changes in offers application and set adjusted products
        foreach ($cartProducts as $cartIndex => $cartProduct) {
            if (
                isset($originalDiscountedProducts[$cartIndex])
                && $originalDiscountedProducts[$cartIndex]['id'] === $cartProduct['id']
                && (
                    // removed offer
                    !isset($cartProduct['special_offer_id'])
                    ||
                    // changed offer 
                    $originalDiscountedProducts[$cartIndex]['special_offer_id'] !== $cartProduct['special_offer_id']
                    ||
                    $originalDiscountedProducts[$cartIndex]['amount'] !== $cartProduct['amount']
                )
                ||
                // added offer
                !isset($originalDiscountedProducts[$cartIndex])
                && isset($cartProduct['special_offer_id'])
                && (
                    isset($cartProduct['promoted_cart_index'])
                    || isset($cartProduct['cart_price_threshold'])
                )
            ) {
                $this->adjustedProducts[$cartIndex] = array(
                    'id' => $cartProduct['id'],
                    'adjustment' => self::PRODUCT_SPECIAL_OFFER_APPLICATION_ADJUSTED,
                );
            }
        }
        // merge 'promoted_cart_index' and 'discounted_cart_index' arrays for
        // offers applied by promoted products cart price
        $mergedPromotedCartIndexes = array();
        $mergedDiscountedCartIndexes = array();
        foreach ($cartProducts as $cartProduct) {
            if (
                isset($cartProduct['promoted_cart_index'])
                && is_array($cartProduct['promoted_cart_index'])
            ) {
                if (!isset($mergedPromotedCartIndexes[$cartProduct['special_offer_id']])) {
                    $mergedPromotedCartIndexes[$cartProduct['special_offer_id']] = array();
                }
                $mergedPromotedCartIndexes[$cartProduct['special_offer_id']] += 
                    $cartProduct['promoted_cart_index'];
            }
            if (
                isset($cartProduct['discounted_cart_index'])
                && is_array($cartProduct['discounted_cart_index'])
            ) {
                if (!isset($mergedDiscountedCartIndexes[$cartProduct['special_offer_id']])) {
                    $mergedDiscountedCartIndexes[$cartProduct['special_offer_id']] = array();
                }
                $mergedDiscountedCartIndexes[$cartProduct['special_offer_id']] += 
                    $cartProduct['discounted_cart_index'];
            }
        }
        // apply merged and sort 'promoted_cart_index' and 'discounted_cart_index' 
        // arrays for offers applied by promoted products cart price
        foreach ($cartProducts as &$cartProduct) {
            if (
                isset($cartProduct['promoted_cart_index'])
                && is_array($cartProduct['promoted_cart_index'])
            ) {
                $cartProduct['promoted_cart_index'] = 
                    $mergedPromotedCartIndexes[$cartProduct['special_offer_id']];
                asort($cartProduct['promoted_cart_index'], SORT_NUMERIC);
            }
            elseif (
                isset($cartProduct['discounted_cart_index'])
                && is_array($cartProduct['discounted_cart_index'])
            ) {
                $cartProduct['discounted_cart_index'] = 
                    $mergedDiscountedCartIndexes[$cartProduct['special_offer_id']];
                asort($cartProduct['discounted_cart_index'], SORT_NUMERIC);
            }
        }
        unset($cartProduct);
        // remove unapplied/orphan proposed special_offer_id of unpaired products
        // (this happens if amount of new added discounted product is higher than 
        // possible existing pairs in cart)
        foreach ($cartProducts as &$cartProduct) {
            if (
                !isset($cartProduct['promoted_cart_index'])
                && !isset($cartProduct['discounted_cart_index'])
                && !isset($cartProduct['cart_price_threshold'])
            ) {
                unset($cartProduct['special_offer_id']);
            }
        }
        unset($cartProduct);

        // merge the same products
        $iIndexes = $jIndexes = array_keys($cartProducts);
        $removedIndexes = array();
        // cart indexes can be unordered / mixed so avoid sequential for() loop
        foreach ($iIndexes as $i) {
            // start $j from the next index after $i
            array_shift($jIndexes);
            if (
                // some indexes may be not present
                !isset($cartProducts[$i]) 
                || isset($removedIndexes[$i])
            ) {
                continue;
            }
            foreach ($jIndexes as $j) {
                if (
                    // some indexes may be not present
                    !isset($cartProducts[$j])
                    || isset($removedIndexes[$j])
                ) {
                    continue;
                }
                if ($this->compareProducts($cartProducts[$i], $cartProducts[$j], array(
                    'comparePairedProducts' => 'byCartRecord',
                    'products' => $cartProducts,
                ))) {
                    $cartProducts[$i]['amount'] += $cartProducts[$j]['amount'];
                    if (
                        isset($cartProducts[$i]['promoted_cart_index'])
                        && !is_array($cartProducts[$i]['promoted_cart_index'])
                        && $cartProducts[$i]['promoted_cart_index'] !== $cartProducts[$j]['promoted_cart_index']
                    ) {
                        $cartProducts[$cartProducts[$i]['promoted_cart_index']]['amount'] +=
                            $cartProducts[$cartProducts[$j]['promoted_cart_index']]['amount'];
                        $removedIndexes[$cartProducts[$j]['promoted_cart_index']] = true;
                    }
                    elseif (
                        isset($cartProducts[$i]['promoted_cart_index'])
                        && is_array($cartProducts[$i]['promoted_cart_index'])
                    ) {
                        $pairs = $this->pairProducts(
                            $cartProducts[$i]['promoted_cart_index'], 
                            $cartProducts[$j]['promoted_cart_index'], 
                            $cartProducts
                        );
                        foreach ($pairs as $pairIndex1 => $pairIndex2) {
                            if ($pairIndex1 === $pairIndex2) {
                                continue;
                            }
                            $cartProducts[$pairIndex1]['amount'] +=
                                $cartProducts[$pairIndex2]['amount'];
                            $removedIndexes[$pairIndex2] = true;
                        }
                    }
                    elseif (
                        isset($cartProducts[$i]['discounted_cart_index'])
                        && !is_array($cartProducts[$i]['discounted_cart_index'])
                        && $cartProducts[$i]['discounted_cart_index'] !== $cartProducts[$j]['discounted_cart_index']
                    ) {
                        $cartProducts[$cartProducts[$i]['discounted_cart_index']]['amount'] +=
                            $cartProducts[$cartProducts[$j]['discounted_cart_index']]['amount'];
                        $removedIndexes[$cartProducts[$j]['discounted_cart_index']] = true;
                    }
                    elseif (
                        isset($cartProducts[$i]['discounted_cart_index'])
                        && is_array($cartProducts[$i]['discounted_cart_index'])
                    ) {
                        $pairs = $this->pairProducts(
                            $cartProducts[$i]['discounted_cart_index'], 
                            $cartProducts[$j]['discounted_cart_index'], 
                            $cartProducts
                        );
                        foreach ($pairs as $pairIndex1 => $pairIndex2) {
                            if ($pairIndex1 === $pairIndex2) {
                                continue;
                            }
                            $cartProducts[$pairIndex1]['amount'] +=
                                $cartProducts[$pairIndex2]['amount'];
                            $removedIndexes[$pairIndex2] = true;
                        }
                    }
                    $removedIndexes[$j] = true;
                }
            }
        }
        $removedIndexes = array_keys($removedIndexes);
        foreach ($removedIndexes as $removedIndex) {
            unset($cartProducts[$removedIndex]);
            foreach ($cartProducts as &$cartProduct) {
                if (
                    isset($cartProduct['promoted_cart_index'])
                    && is_array($cartProduct['promoted_cart_index'])
                ) {
                    unset($cartProduct['promoted_cart_index'][$removedIndex]);
                }
                if (
                    isset($cartProduct['discounted_cart_index'])
                    && is_array($cartProduct['discounted_cart_index'])
                ) {
                    unset($cartProduct['discounted_cart_index'][$removedIndex]);
                }
            }
            unset($cartProduct);
        }

        // place paired products together
        $tmp = array();
        $k = -1;
        $indexConversions = array();
        // cart indexes can be unordered / mixed so avoid sequential for() loop
        foreach (array_keys($cartProducts) as $i) {
            // some indexes may be not present (are removed in previous iteration)
            if (!isset($cartProducts[$i])) {
                continue;
            }
            if (
                isset($cartProducts[$i]['promoted_cart_index'])
                && !is_array($cartProducts[$i]['promoted_cart_index'])
            ) {
                $k++;
                $tmp[$k] = $cartProducts[$cartProducts[$i]['promoted_cart_index']];
                $indexConversions[$cartProducts[$i]['promoted_cart_index']] = $k;
                $k++;
                $tmp[$k] = $cartProducts[$i];
                $indexConversions[$i] = $k;
                $tmp[$k - 1]['discounted_cart_index'] = $k;
                $tmp[$k]['promoted_cart_index'] = $k - 1;
                unset($cartProducts[$cartProducts[$i]['promoted_cart_index']]);
                unset($cartProducts[$i]);
            }
            elseif (
                isset($cartProducts[$i]['promoted_cart_index'])
                && is_array($cartProducts[$i]['promoted_cart_index'])
            ) {
                // NOTE: There should be the same arrays of promoted and discounted 
                // on all paired products
                $removedIndexes = array();
                $newPromotedIndexes = array();
                foreach ($cartProducts[$i]['promoted_cart_index'] as $oldPromotedIndex) {
                    $k++;
                    $tmp[$k] = $cartProducts[$oldPromotedIndex];
                    $indexConversions[$oldPromotedIndex] = $k;
                    $newPromotedIndexes[$k] = $k;
                    $removedIndexes[$oldPromotedIndex] = $oldPromotedIndex;
                }
                $oldDiscountedIndexes = $tmp[$k]['discounted_cart_index'];
                $newDiscountedIndexes = array();
                foreach ($oldDiscountedIndexes as $oldDiscountedIndex) {
                    $k++;
                    $tmp[$k] = $cartProducts[$oldDiscountedIndex];
                    $indexConversions[$oldDiscountedIndex] = $k;
                    $tmp[$k]['promoted_cart_index'] = $newPromotedIndexes;
                    $newDiscountedIndexes[$k] = $k;
                    $removedIndexes[$oldDiscountedIndex] = $oldDiscountedIndex;
                }
                foreach ($newPromotedIndexes as $newPromotedIndex) {
                    $tmp[$newPromotedIndex]['discounted_cart_index'] = $newDiscountedIndexes;
                }
                foreach ($removedIndexes as $removedIndex) {
                    unset($cartProducts[$removedIndex]);
                }
            }
            elseif (
                isset($cartProducts[$i]['discounted_cart_index'])
                && !is_array($cartProducts[$i]['discounted_cart_index'])
            ) {
                $k++;
                $tmp[$k] = $cartProducts[$i];
                $indexConversions[$i] = $k;
                $k++;
                $tmp[$k] = $cartProducts[$cartProducts[$i]['discounted_cart_index']];
                $indexConversions[$cartProducts[$i]['discounted_cart_index']] = $k;
                $tmp[$k - 1]['discounted_cart_index'] = $k;
                $tmp[$k]['promoted_cart_index'] = $k - 1;
                unset($cartProducts[$cartProducts[$i]['discounted_cart_index']]);
                unset($cartProducts[$i]);
            }
            elseif (
                isset($cartProducts[$i]['discounted_cart_index'])
                && is_array($cartProducts[$i]['discounted_cart_index'])
            ) {
                // NOTE: There should be the same arrays of promoted and discounted 
                // on all paired products
                $removedIndexes = array();
                $oldPromotedIndexes = array();
                foreach ($cartProducts[$i]['discounted_cart_index'] as $oldDiscountedIndex) {
                    if (!empty($cartProducts[$oldDiscountedIndex]['promoted_cart_index'])) {
                        $oldPromotedIndexes = $cartProducts[$oldDiscountedIndex]['promoted_cart_index'];
                        break;
                    }
                }
                $newPromotedIndexes = array();
                foreach ($oldPromotedIndexes as $oldPromotedIndex) {
                    $k++;
                    $tmp[$k] = $cartProducts[$oldPromotedIndex];
                    $indexConversions[$oldPromotedIndex] = $k;
                    $newPromotedIndexes[$k] = $k;
                    $removedIndexes[$oldPromotedIndex] = $oldPromotedIndex;
                }
                $newDiscountedIndexes = array();
                foreach ($cartProducts[$i]['discounted_cart_index'] as $oldDiscountedIndex) {
                    $k++;
                    $tmp[$k] = $cartProducts[$oldDiscountedIndex];
                    $indexConversions[$oldDiscountedIndex] = $k;
                    $tmp[$k]['promoted_cart_index'] = $newPromotedIndexes;
                    $newDiscountedIndexes[$k] = $k;
                    $removedIndexes[$oldDiscountedIndex] = $oldDiscountedIndex;
                }
                foreach ($newPromotedIndexes as $newPromotedIndex) {
                    $tmp[$newPromotedIndex]['discounted_cart_index'] = $newDiscountedIndexes;
                }
                foreach ($removedIndexes as $removedIndex) {
                    unset($cartProducts[$removedIndex]);
                }
            }            
            else {
                $k++;
                $tmp[$k] = $cartProducts[$i];
                $indexConversions[$i] = $k;
                unset($cartProducts[$i]);
            }
        }
        $cartProducts = $tmp;
        // actualize parent_index and child_indexes after the above products pairing
        // and replacing
        foreach ($cartProducts as &$cartProduct) {
            if (isset($cartProduct['parent_index'])) {
                $cartProduct['parent_index'] = $indexConversions[$cartProduct['parent_index']];
            }
            if (!empty($cartProduct['child_indexes'])) {
                $childIndexes = array();
                foreach ($cartProduct['child_indexes'] as $childIndex) {
                    $childIndexes[] = $indexConversions[$childIndex];
                }
                $cartProduct['child_indexes'] = $childIndexes;
            }
        }
        unset($cartProduct);
        
        return $cartProducts;
    }

    /**
     * Returns an array of cart products ids which have no applied special offer
     *
     * @return array
     */
    public function getProductIdsWithoutAppliedSpecialOffer() {
        $products = $this->getProducts();
        $produstIds = array();
        foreach ($products as $product) {
            if (!isset($product['special_offer_id'])) {
                $produstIds[$product['id']] = true;
            }
        }
        return array_keys($produstIds);
    }

    /**
     * Returns cart price minus the applied special offers cart price thresholds
     * (it means only special offers applied by cart price threshold and special 
     * offers applied by promoted products cart price are considered)
     *
     * ATTENTION: The available parts are counted from actual taxed price of products.
     *
     * @param array $options Following are available:
     *      - 'precision' (int) Number of decimals of returned prices. Defaults to 2.
     *      - 'products' (array) Explicit list of products to get available price for.
     *          It must have structure of cart products array returned by EshopCart::getProducts().
     *          If not an array or not provided then it defaults to actual existing cart products.
     *
     * @return array Available part of cart price is stored under key 'total'. Available parts 
     *      of special offers applied by promoted products cart price are stored under
     *      ids of these offers (they are populated for all active offers of this kind
     *      so there can be even 0 values if the offer has no promoted product in cart).
     */
    public function getPriceWithoutAppliedSpecialOffer($options = array()) {
        $defaults = array(
            'precision' => 2,
            'products' => null,
        );
        $options = array_merge($defaults, $options);
        if (!is_array($options['products'])) {
            $options['products'] = $this->getProductsDetails();
        }
        else {
            $options['products'] = $this->getProductsDetails(array(
                'products' => $options['products'],
            ));
        }
        // products of special offers applied by promoted products cart price
        static $specialOfferPromotedProducts = null;
        if ($specialOfferPromotedProducts === null) {
            $this->loadModel('EshopSpecialOffer');
            $Offer = new EshopSpecialOffer();
            $specialOffers = $Offer->getActive(array(
                'conditions' => array(
                    'apply_by' => 'promoted_products_cart_price_threshold',
                )
            ));
            $specialOfferPromotedProducts = array();
            foreach ($specialOffers as $specialOffer) {
                foreach ($specialOffer['promoted_product_ids'] as $productId) {
                    $specialOfferPromotedProducts[$productId] = $specialOffer['id'];
                }
            }
        }
        $cartPrice = $this->getPrices($options['products'], array(
            'precision' => false,
        ));
        $cartPrice = $cartPrice['products_price_to_pay']; //HARDCODED
        $freePrices = array('total' => $cartPrice);
        foreach ($options['products'] as $product) {
            // accumutate used part of cart price in case of discounted products 
            // belonging to special offers applied by cart price threshold or to
            // special offers applied by promoted products cart price
            // (cart_price_threshold is set in both cases)
            if (isset($product['cart_price_threshold'])) {
                $usedPrice = (float)$product['cart_price_threshold'] * $product['amount'];
                $freePrices['total'] -= $usedPrice;
                if (isset($product['promoted_cart_index'])) {
                    $offerId = $product['special_offer_id'];
                    if (!isset($freePrices[$offerId])) {
                        $freePrices[$offerId] = 0.0;
                    }
                    $freePrices[$offerId] -= $usedPrice;
                }
            }
            // accumulate cart prices in case of promoted products (both paired and unpaired)
            // belonging to special offers applied by promoted products cart price
            elseif (isset($specialOfferPromotedProducts[$product['id']])) {
                $offerId = $specialOfferPromotedProducts[$product['id']];
                if (!isset($freePrices[$offerId])) {
                    $freePrices[$offerId] = 0.0;
                }
                $freePrices[$offerId] += (float)$product['price_actual_taxed'] * $product['amount']; //HARDCODED
            }
        }
        foreach ($freePrices as &$freePrice) {
            $freePrice = round($freePrice, $options['precision']);
        }
        unset($freePrice);
        return $freePrices;
    }
    
    /**
     * Checks if there are gift cards in cart
     * 
     * @param array $options Following are available:
     *      - 'variant' (string) Gift card variant pid(s) to check for. Possible 
     *          values are 'electronic' and 'printed'. If empty then not considered. 
     *          Defaults to NULL.
     * 
     * @return boolean|string FALSE if there are no gift cards. TRUE or 'only' if 
     *      there are gift cards. In case of 'only' there are only gift cards in cart
     *      (no other products)
     */
    public function hasGiftCardProducts($options = array()) {
        $options = array_merge(array(
            'variant' => null,
        ), $options);
        $options['variant'] = (array)$options['variant'];
        
        $products = $this->getProducts();
        if (!($products)) {
            return false;
        }
        $productsIds = array();
        foreach ($products as $product) {
            $productsIds[$product['id']] = $product['id'];
        }
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $giftCardIds = $Product->findList(array(
            'conditions' => array(
                'id' => $productsIds,
                'is_gift_card' => true,
            ),
            'fields' => array('id')
        ));
        if (empty($giftCardIds)) {
            return false;
        }
        if ($options['variant']) {
            foreach ($products as $product) {
                // if product is gift card but not the required variant(s) then 
                // remove the corresponding gift card id
                if (
                    isset($giftCardIds[$product['id']])
                    && (
                        empty($product['static_attributes']['variant'])
                        || !in_array($product['static_attributes']['variant'], $options['variant'])
                    )
                ) {
                    unset($giftCardIds[$product['id']]);
                }
            }
        }
        return count($giftCardIds) === count($productsIds) ? 'only' : true;
    }
    
    /**
     * Checks if the cart contains downloadable products
     * 
     * @return boolean|string FALSE if there are no downloadable products. TRUE or 'only' if 
     *      there are downloadable products. In case of 'only' there are only downloadable products in cart
     *      (no other products)
     */
    public function hasDownloadableProducts() {
        return $this->hasGiftCardProducts(array(
            'variant' => 'electronic',
        ));
    }
    
    /**
     * Returns Facebook AddToCart event tracking code.
     * See https://developers.facebook.com/docs/meta-pixel/implementation/pixel-for-collaborative-ads > AddToCart
     * See https://developers.facebook.com/docs/meta-pixel/get-started/advantage-catalog-ads > AddToCart
     * 
     * ATTENTION: Conversion code is generated only if facebook pixel (meta pixel)
     * code (see https://developers.facebook.com/docs/meta-pixel/get-started#base-code )
     * is inserted in customCode (htmlHead|htmlBodyStart|htmlBodyEnd)
     * 
     * ATTENTION: If App::$useJsEmbroidery is FALSE and 'debug' option is FALSE 
     * then this method generates nothing. If 'debug' option is TRUE then this method
     * returns tracking code enclosed in HTML comments (value of is ignored as the js code is not executed).
     * 
     * @param array $oldCartProducts Products returned by EshopCart::getProducts()
     *      or by EshopCart::getProductsDetails(). These must be retrieved BEFORE the cart is changed.
     * @param array $newCartProducts Products returned by EshopCart::getProducts()
     *      or by EshopCart::getProductsDetails(). These must be retrieved AFTER the cart is changed.
     * @param array $options Following are available:
     *      - 'debug' (bool) If TRUE then this code is returned in HTML comment block.
     *      Defautls to value of ON_LOCALHOST constant.
     * 
     * @return string Facebook ViewContent event tracking code
     */
    public function getFacebookAddCode($oldCartProducts, $newCartProducts, $options = array()) {
        $defaults = array(
            'debug' => ON_LOCALHOST,
        );
        $options = array_merge($defaults, $options);
        
        if (
            !App::$useJsEmbroidery 
            && !$options['debug']
            ||
            // check for presence of facebook pixel in custom code
            (
                $customHtmlCode = 
                    App::getSetting('App', 'customCode.htmlHead') . ' ' .
                    App::getSetting('App', 'customCode.htmlBodyStart') . ' ' .
                    App::getSetting('App', 'customCode.htmlBodyEnd')
            )
            && !preg_match('#fbevents\.js|https://www\.facebook\.com/tr\?id=#', $customHtmlCode)
        ) {
            return '';
        }
        
        // resolve added products
        $addedProducts = array();
        foreach ($oldCartProducts as $i => $oldProduct) {
            foreach ($newCartProducts as $j => $newProduct) {
                if ($this->compareProducts($oldProduct, $newProduct)) {
                    if ($oldProduct['amount'] < $newProduct['amount']) {
                        $addedProduct = $newProduct;
                        $addedProduct['amount'] -= $oldProduct['amount'];
                        $addedProducts[] = $addedProduct;
                    }
                    // remove paired products for sake of further processing here below
                    unset($oldCartProducts[$i]);
                    unset($newCartProducts[$j]);
                }
            }
        }
        // - if there are some new products which has not been found in old products
        // then they are brand new added
        $addedProducts = array_merge($addedProducts, $newCartProducts);
        
        if (!$addedProducts) {
            return '';
        }
        
        $addedProducts = $this->getProductsDetails(array_merge($options, array(
            'products' => $addedProducts,
        )));
        $prices = $this->getPrices($addedProducts);
        // load view
        $code = $this->loadView('EshopCart/getFacebookAddCode', array(
            'debug' => $options['debug'],
            'price' => $prices['products_price_actual_taxed'],
            'products' => $addedProducts,
        ));
        if ($options['debug']) {
            $code = Html::comment($code);
        }
        return $code;        
    }    
}
