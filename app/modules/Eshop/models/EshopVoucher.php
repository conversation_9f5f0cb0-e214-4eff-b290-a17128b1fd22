<?php

class EshopVoucher extends Model {
    protected $table = 'run_eshop_vouchers';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'code' => array('type' => 'varchar', 'index' => 'unique', 'comment' => 'Code to apply for the voucher discount'),
        'run_users_id' => array('type' => 'int', 'default' => null, 'index' => 'index', 'comment' => 'If provided then voucher can be used only by given user'),
        'discount_rate' => array('type' => 'decimal', 'length' => 8.2, 'default' => null, 'comment' => 'Discount percents to be applyed to product / order price'),
        'special_discount_rate' => array('type' => 'decimal', 'length' => 8.2, 'default' => null, 'comment' => 'Special discount rate applied only to vydavatel books. If empty then discount_rate is applied for all books'),
        'discount' => array('type' => 'decimal', 'length' => 8.2, 'default' => null, 'comment' => 'Absolute discount to be applyed to order price'),
        'active_from' => array('type' => 'date', 'default' => null),
        'active_to' => array('type' => 'date', 'default' => null),
        'applications_limit' => array('type' => 'int', 'default' => null, 'comment' => 'Max allowed number of voucher uses/applications. When this limit is fulfilled then the voucher gets inactive. If empty then there is no limit'),
        'applications_count' => array('type' => 'int', 'default' => 0, 'comment' => 'How many times has been voucher applied already?'),
        /**
         * Is the voucher used for a single type of product (the VAT rate is foreknown)?
         * Or is the voucher used for different types of products (the VAT rate is not foreknown)?
         * 
         * For diffrence between single and multi-purpose vouchers see:
         * - https://www.financnasprava.sk/_img/pfsedit/Dokumenty_PFS/Zverejnovanie_dok/Dane/Metodicke_pokyny/Nepriame_dane/2019/2019.09.27_16_DPH_2019_MP.pdf > príklad 24
         * - https://www.podnikajte.sk/dan-z-pridanej-hodnoty/zdanovanie-poukazov-voucherov-dph-2019
         * - https://www.easystart.sk/poukazy-a-ich-zdanovanie/
         * 
         * Multi purpose vouchers are a prepaid part of price to pay - kind of "money".
         * Something like 20€ with only restriction that these 20€ can be used just 
         * on the specified e-shop
         */
        'purpose_type' => array('type' => 'enum', 'default' => 'multi', 'values' => array('single', 'multi'), 'comment' => 'Is the voucher used for a single type of product (the VAT rate is foreknown)? Or is the voucher used for different types of products (the VAT rate is not foreknown)?'),
        'printed' => array('type' => 'bool', 'default' => 0, 'comment' => 'Just a hand-set info for user if the voucher code has been already printed'),
        'expiring_email_success' => array('type' =>'bool', 'default' => 0, 'comment' => 'Has been the voucher expiring email successfully sent?'),
        'active' => array('type' => 'bool', 'default' => 1),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
    );
    
    protected $nameField = 'code';
    
    public function __construct() {
        parent::__construct();
        
        $this->validations = array(
            'code' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Enter code'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Enter code'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Code is used already'),
                ),
            ),       
            'discount_rate' => array(
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Enter a discount rate'),
//                    'on' => 'create',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Enter a discount rate'),
//                ),
                array(
                    'rule' => 'discountRateOrDiscount',
                    'force' => true,
                ),
                array(
                    'rule' => 'number',
                    'message' => __v(__FILE__, 'Discount rate must be number'),
                ),
                array(
                    'rule' => array('gt', 0),
                    'message' => __v(__FILE__, 'Discount rate must be greater than zero'),
                ),
            ),
            'special_discount_rate' => array(
                array(
                    'rule' => 'number',
                    'message' => __v(__FILE__, 'Discount rate must be number'),
                ),
                array(
                    'rule' => array('gt', 0),
                    'message' => __v(__FILE__, 'Discount rate must be greater than zero'),
                ),
            ),
            'discount' => array(
                array(
                    'rule' => 'discountRateOrDiscount',
                    'force' => true,
                ),
                array(
                    'rule' => 'number',
                    'message' => __v(__FILE__, 'Zadajte číslo'),
                ),
                array(
                    'rule' => array('gt', 0),
                    'message' => __v(__FILE__, 'Zadajte číslo väčšie ako nula'),
                ),
            ),            
            'active_from' => array(
                array(
                    'rule' => 'date',
                    'message' => __v(__FILE__, 'Enter valid date'),
                ),
            ),
            'active_to' => array(
                array(
                    'rule' => 'date',
                    'message' => __v(__FILE__, 'Enter valid date'),
                ),
                array(
                    'rule' => 'activeToAfterActiveFrom',
                    'message' => __v(__FILE__, 'End date must be the same or later that start date'),
                ),
            ),            
            'applications_limit' => array(
                array(
                    'rule' => 'intNumber',
                    'message' => __v(__FILE__, 'Enter an integer or let empty'),
                ),
            ), 
            'import_file' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Choose a file'),
                    'alternative' => 'importFromXls',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Choose a file'),
                    'alternative' => 'importFromXls',
                ),
                array(
                    'rule' => array('uploadData', array('notEmpty' => true)),
                    'message' => __v(__FILE__, 'Choose a file'),
                    'alternative' => 'importFromXls',
                ),
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Upload error has occurred'),
                    'alternative' => 'importFromXls',
                ),
                array(
                    'rule' => 'fileType',
                    'message' => __v(__FILE__, 'Choose a .xls file'),
                    'alternative' => 'importFromXls',
                ),
            ),
            
            // verify
            'verified_code' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'alternative' => 'verify',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'alternative' => 'verify',
                ),
            ),
            'sc' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'alternative' => 'verify',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'alternative' => 'verify',
                ),
                array(
                    'rule' => array('captcha', array('name' => 'voucher')),
                    'alternative' => 'verify',
                ),
            ),
        );
    }
    
    /**
     * Used to validate active_to
     * 
     * @param string $fieldValue
     * @param string $fieldName
     * @param array $data
     * 
     * @return boolean
     */
    public function validate_activeToAfterActiveFrom($fieldValue, $fieldName, $data) {
        // get start date
        $activeFrom = null;
        if (isset($data['active_from'])) {
            $activeFrom = $data['active_from'];
        }
        elseif (!empty($data['id'])) {
            $activeFrom = $this->findFieldBy('active_from', 'id', $data['id']);
        }
        if (
            !empty($activeFrom) 
            && !empty($fieldValue) 
            && $fieldValue < $activeFrom
        ) {
            return false;
        }
        return true;
    }   
    
    public function validate_discountRateOrDiscount($fieldValue, $fieldName, $data, &$validation) {
        if (
            Validate::emptyValue(Sanitize::value($data['discount_rate']))
            && Validate::emptyValue(Sanitize::value($data['discount']))
        ) {
            $validation['message'] = __v(__FILE__, 'Zadajte buď percento zľavy alebo absolútnu zľavu');
            return false;
        }
        if (
            !Validate::emptyValue(Sanitize::value($data['discount_rate']))
            && !Validate::emptyValue(Sanitize::value($data['discount']))
        ) {
            $validation['message'] = __v(__FILE__, 'Zadajte buď percento zľavy alebo absolútnu zľavu. Iba jedno z nich.');
            return false;
        }
        $voucherProductId = $this->getSetting('EshopProduct.voucherProductId');
        if (
            !$voucherProductId 
            && $fieldName === 'discount'
            && !Validate::emptyValue(Sanitize::value($data['discount']))
        ) {
            $validation['message'] = __v(__FILE__, 'Absolútnu zľavu je možné používať až po zadaní id produktu "Zľavový kód" v Admin > Eshop > Nastavenia > Základné nastavenia > Id produktu "Zľavový kód"');
            return false;
        }
        return true;
    }
    
    /**
     * 
     * @param type $data
     * @param type $options
     */
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        
        if (!empty($data['code'])) {
            $data['code'] = strtoupper($data['code']);
        }
        
        if (!empty($data['active_from'])) {
            $data['active_from'] = Date::format($data['active_from'], 'Y-m-d');
        }
        
        if (!empty($data['active_to'])) {
            $data['active_to'] = Date::format($data['active_to'], 'Y-m-d');
        }
        
        if (isset($data['discount_rate'])) {
            $data['discount_rate'] = str_replace(',', '.', $data['discount_rate']);
        }
        
        if (isset($data['special_discount_rate'])) {
            $data['special_discount_rate'] = str_replace(',', '.', $data['special_discount_rate']);
        }
        
        if (isset($data['discount'])) {
            $data['discount'] = str_replace(',', '.', $data['discount']);
        }
        
        if (
            $options['on'] === 'create'
            && empty($data['applications_limit'])
            && !empty($data['discount'])
        ) {
            $data['applications_limit'] = 1;
        }
        
        if (
            isset($data['applications_limit'])
            && empty($data['applications_limit'])
        ) {
            $data['applications_limit'] = null;
        }
        
        if (
            !empty($data['discount'])
            && empty($data['applications_limit'])
        ) {
            $this->setWarning(__v(__FILE__, 'Pozor, zľavový kód s absolútnou zľavou bez limitu na počet použití!'));
        }
        
        if (in_array('verify', $options['alternative'])) {
            $data['verified_code'] = trim($data['verified_code']);
            $data['sc'] = trim($data['sc']);
        }

        if (!empty($data['category_ids'])) {
            if (is_string($data['category_ids'])) {
                $data['category_ids'] = explode(';', $data['category_ids']);
            }
            $data['category_ids'] = array_unique(array_filter(array_map('trim', $data['category_ids'])));
        }

        if (!empty($data['product_ids'])) {
            if (is_string($data['product_ids'])) {
                $data['product_ids'] = explode(';', $data['product_ids']);
            }
            $data['product_ids'] = array_unique(array_filter(array_map('trim', $data['product_ids'])));
        }
        
        return parent::normalize($data, $options);
    }
    
    /**
     * Returns new unique voucher code
     * 
     * @param array $options Following are available
     *          - 'length' (integer) Code length. Defaults to 10.
     *          - 'chars' (string) Characters to be used in code. Defaults to 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'.
     *          - 'avoidValues' (array) Values which should be avoided
     * 
     * @return string
     */
    public function getUniqueCode($options = array()) {
        $defaults = array(
            'length' => 10,
            'chars' => 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789',
            'avoidValues' => array(),
        );
        $options = array_merge($defaults, $options);
        $options['avoidValues'] = (array)$options['avoidValues'];
        $code = Str::getRandom($options['length'],$options['chars']);
        $findOptions = array(
            'fields' => 'id',
            'conditions' => array(
                'code' => $code,
            )
        );
        while (
            in_array($code, $options['avoidValues']) || $this->findFirst($findOptions)
        ) {
            $code = Str::getRandom($options['length'], $options['chars']);
            $findOptions['conditions']['code'] = $code;
        }
        return $code;
    }
    
    /**
     * Returns array of conditions for active vouchers
     * 
     * @param array $options Following are available:
     *      - 'literals' (bool) If TRUE then literals for active conditions are
     *          returned. These should be set as find options > 'literals' > 'conditions'.
     *          Defaults to FALSE.
     *      - 'productIds' (int|array) Single product id or an array of product ids
     *          to get active vouchers condition for. If provided then only active vouchers
     *          which are assigned either to specified products or to no products are considered.
     *          Defaults to NULL.
     * 
     * @return array
     */
    static public function getActiveConditions($options = array()) {
        $options = array_merge(array(
            'literals' => false,
            'productIds' => null,
        ), $options);    
        
        $conditions = array(
            'EshopVoucher.active' => true,
            array(
                'EshopVoucher.active_from' => null,
                'OR',
                'DATEDIFF(NOW(),`EshopVoucher`.`active_from`) >= 0',
            ),
            array(
                'EshopVoucher.active_to' => null,
                'OR',
                'DATEDIFF(NOW(),`EshopVoucher`.`active_to`) <= 0',
            ),
            array(
                'EshopVoucher.applications_limit' => null,
                'OR',
                '`EshopVoucher`.`applications_limit` > `EshopVoucher`.`applications_count`',
            )
        );
        
        $literals = array(
            'DATEDIFF(NOW(),`EshopVoucher`.`active_from`) >= 0',
            'DATEDIFF(NOW(),`EshopVoucher`.`active_to`) <= 0',
            '`EshopVoucher`.`applications_limit` > `EshopVoucher`.`applications_count`',
        );
        
        if ($options['productIds']) {
            $Voucher = new self();
            $productVoucherIds = $Voucher->findList(array(
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopVoucherProduct',
                    ),
                ),
                'key' => 'EshopVoucher.id',
                'fields' => array(
                    'EshopVoucher.id',
                ),
                'conditions' => array_merge($conditions, array(
                    'EshopVoucherProduct.run_eshop_products_id' => $options['productIds'],
                )),
                'literals' => array(
                    'conditions' => $literals,
                ),
            ));
            $categoryVoucherIds = $Voucher->findList(array(
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopVoucherCategory',
                    ),
                    array(
                        'type' => 'left',
                        'model' => 'EshopProductCategoryProduct',
                        'conditions' => array(
                            'EshopProductCategoryProduct.run_eshop_product_categories_id = EshopVoucherCategory.run_eshop_product_categories_id'
                        ),
                    ),
                ),
                'key' => 'EshopVoucher.id',
                'fields' => array(
                    'EshopVoucher.id',
                ),
                'conditions' => array_merge($conditions, array(
                    'EshopProductCategoryProduct.run_eshop_products_id' => $options['productIds'],
                )),
                'literals' => array(
                    'conditions' => $literals,
                ),
            ));
            $noProductAndCategoryVoucherIds = $Voucher->findList(array(
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopVoucherProduct',
                    ),
                    array(
                        'type' => 'left',
                        'model' => 'EshopVoucherCategory',
                    ),
                ),
                'key' => 'EshopVoucher.id',
                'fields' => array(
                    'EshopVoucher.id',
                ),
                'conditions' => array_merge($conditions, array(
                    'EshopVoucherProduct.id' => null,
                    'EshopVoucherCategory.id' => null,
                )),
                'literals' => array(
                    'conditions' => $literals,
                ),
            ));
                    
            $conditions = array(
                'EshopVoucher.id' => 
                    $productVoucherIds 
                    + $categoryVoucherIds
                    + $noProductAndCategoryVoucherIds,
            );
            $literals = null;
        }
        
        if ($options['literals']) {
            return $literals;
        }
        return $conditions;
    }
    
    /**
     * Returns list of all active vouchers. 
     * Voucher codes are used as key of array.
     * 
     * @param array $options Following are available:
     *      - 'productIds' (int|array) Single product id or an array of product ids
     *          to get active vouchers condition for. If provided then only active vouchers
     *          which are assigned either to specified products or to no products are considered.
     *          Defaults to NULL.
     * 
     * @return array
     */
    public function getActive($options = array()) {
        $options = array_merge(array(
            'productIds' => null,
        ), $options);
        return $this->findList(array(
            'key' => 'EshopVoucher.code',
            'conditions' => array(
                self::getActiveConditions(array(
                    'productIds' => $options['productIds'],
                )),
            ),
            'literals' => array(
                'conditions' => self::getActiveConditions(array(
                    'literals' => true,
                    'productIds' => $options['productIds'],
                )),
            ),
        ));
    }
    
    /**
     * Returns voucher record specified by $code but only if it exists and is active
     * 
     * @param array $options Following are available:
     *      - 'productIds' (int|array) Single product id or an array of product ids
     *          to get active vouchers condition for. If provided then only active vouchers
     *          which are assigned either to specified products or to no products are considered.
     *          Defaults to NULL.
     * 
     * @return array|NULL Voucher record array or NULL if there is no active voucher
     *      with provided $code
     */
    public function getActiveByCode($code, $options = array()) {
        $options = array_merge(array(
            'productIds' => null,
        ), $options);
        return $this->findFirst(array(
            'conditions' => array(
                'EshopVoucher.code' => $code,
                self::getActiveConditions(array(
                    'productIds' => $options['productIds'],
                )),
            ),
            'literals' => array(
                'conditions' => self::getActiveConditions(array(
                    'literals' => true,
                    'productIds' => $options['productIds'],
                )),
            ),
        ));
    }
    
    /**
     * Is there any active voucher?
     * 
     * @param array $options Following are available:
     *      - 'productIds' (int|array) Single product id or an array of product ids
     *          to get active vouchers condition for. If provided then only active vouchers
     *          which are assigned either to specified products or to no products are considered.
     *          Defaults to NULL.
     * 
     * @return bool
     */
    public function hasActive($options = array()) {
        $options = array_merge(array(
            'productIds' => null,
        ), $options);
        return (bool)$this->findFirst(array(
            'fields' => array('EshopVoucher.id'),
            'conditions' => array(
                self::getActiveConditions(array(
                    'productIds' => $options['productIds'],
                )),
            ),
            'literals' => array(
                'conditions' => self::getActiveConditions(array(
                    'literals' => true,
                    'productIds' => $options['productIds'],
                )),
            ),
        ));
    }
    
    /**
     * Add one more application of the provided voucher code
     * 
     * @param string  $code
     */
    public function addApplicationByCode($code) {
        $this->update(
            array(
                'applications_count' => '`applications_count` + 1',
                'active' => 'IF (`active`, IF (`applications_count` >= `applications_limit`, 0, 1), `active`)',
            ),
            array(
                'conditions' => array(
                    'code' => $code,
                ),
                'literals' => array(
                    'data' => true,
                ),
                'normalize' => false,
                'validate' => false,
            )
        );
    }
    
    /**
     * Import vouchers from .xls file
     * 
     * @param array $importData Import data containing 'import_file' and 'update_on_duplicit'
     *          items.
     * @param array& $report Optional. Aux output. Passed by reference. Item by item 
     *          results report (rendered by view EshopVouchers/admin_importFromXls_ReportHtml).
     * 
     * @return boolean
     */
    public function importFromXls($importData, /*output*/ &$report = array()) {
        try {
            if (!$this->validate($importData, array(
                'normalize' => false,
                'alternative' => 'importFromXls'
            ))) {
                return false;
            }
            $file = File::transfer($importData['import_file'], File::getRelativePath(TMP));
            if (!$file) {
                $this->setError('import_file', __e(__FILE__, 'No file provided or file transfer has failed'));
                return false;
            }
            $updateOnDuplicit = !empty($importData['update_on_duplicit']);

            $existingVouchers = $this->findList(array(
                'key' => 'code',
                'fields' => 'id',
            ));

            App::loadVendor('App', 'excelreader/run_excel_reader.php');
            $Excel = new Run_Spreadsheet_Excel_Reader(TMP . DS . $file);

            $sheet = 0;
            $sheetLastCol = 3;
            $report = array();
            $createVouchers = array();
            $updateVouchers = array();
            $codeCol = 1;
            $discountRateCol = 2;
            $applicationsLimitCol = 3;
            $hasError = false;
            for($sheetRow = 1; /*infinite loop*/; $sheetRow++) {
    //            $emptyRow = true;
                $voucher = array(
                    'code' => null,
                    'discount_rate' => null,
                    'applications_limit' => null,
                );
                for(
                    $sheetCol = 1, $col = 1; 
                    $sheetCol <= $sheetLastCol; 
                    $sheetCol++, $col++
                ) {
                    $value = trim($Excel->content($sheetRow, $sheetCol, $sheet));
    //                if ($value !== '') {
    //                    $emptyRow = false;
    //                }
                    if ($sheetCol === $codeCol) {
                        $voucher['code'] = $value;
                    }
                    elseif ($sheetCol === $discountRateCol) {
                        $voucher['discount_rate'] = $value;
                    }
                    elseif ($sheetCol === $applicationsLimitCol) {
                        $voucher['applications_limit'] = $value;
                    }
                }
                // read untill first empty code
                if (empty($voucher['code'])) {
                    break;
                }
                $on = empty($existingVouchers[$voucher['code']]) ? 'create' : 'update';
                if ($on === 'update') {
                    if ($updateOnDuplicit) {
                        $voucher['id'] = $existingVouchers[$voucher['code']];
                    }
                    else {
                        $report[] = array(
                            $voucher['code'],
                            $voucher['discount_rate'],
                            $voucher['applications_limit'],
                            __(__FILE__, 'Skipped as voucher already exists'),
                            true,
                        );
                        $hasError = true;
                        continue;
                    }
                }
                $voucher = $this->normalize($voucher, array('on' => $on));
                if (!$this->validate($voucher, array('on' => 'update'))) {
                    $errorsString = 'Skipped because of errors: ';
                    foreach ($this->getErrors() as $field => $errors) {
                        $errorsString .= $field . ': ' . implode(', ', $errors) . ' | ';
                    }
                    $errorsString = trim($errorsString, ' |');
                    $report[] = array(
                        $voucher['code'],
                        $voucher['discount_rate'],
                        $voucher['applications_limit'],
                        $errorsString,
                        true,
                    );
                    $hasError = true;
                    continue;
                }
                if ($on === 'create') {
                    $createVouchers[] = $voucher;
                    $report[] = array(
                        $voucher['code'],
                        $voucher['discount_rate'],
                        $voucher['applications_limit'],
                        __(__FILE__, 'Created'),
                        false,
                    );
                }
                else {
                    $updateVouchers[] = $voucher;
                    $report[] = array(
                        $voucher['code'],
                        $voucher['discount_rate'],
                        $voucher['applications_limit'],
                        __(__FILE__, 'Updated'),
                        false,
                    );
                }
            }
            $this->saveBatch(array(
                'create' => array(
                    'EshopVoucher' => &$createVouchers
                ),
                'update' => array(
                    'EshopVoucher' => &$updateVouchers
                ),
            ));
        } 
        catch (Throwable $e) {
            $this->setError('import_file', $e->getMessage());
            $hasError = true;
        }
        if ($hasError) {
            $this->setError('import_file', __v(__FILE__, 'Not all vouchers has been imported'));
        }
        
        unlink(TMP . DS . $file);
        
        return !$hasError;
    }    
    
    public function validate_fileType($fieldValue) {
        $fileType = strtolower(File::getPathinfo($fieldValue['name'], PATHINFO_EXTENSION));
        return Validate::uploadData($fieldValue, array('empty' => true)) 
            || $fileType === 'xls';
    }
    
    /**
     * Verifies a voucher existence, activity (validity) and value (discount).
     * 
     * @param array $data Input data containing 'verified_code' and 'sc' items.
     * 
     * @return array Array containing 'message' (string) and 'active' (bool) items.
     */
    public function verify($data) {
        $data = $this->normalize($data, array(
            'alternative' => 'verify',
        ));
        if (!$this->validate($data, array(
            'allowFields' => array(
                'verified_code',
                'sc',
            ),
            'alternative' => 'verify',
            'normalize' => false,
        ))) {
            return false;
        }
        
        $message = '';
        $active = false;
        if (!($voucher = $this->findFirstBy('code', $data['verified_code']))) {
            $message = __(__FILE__, 'Zľavový kód %s neexistuje', $data['verified_code']);
        }
        elseif (($activeVoucher = $this->getActiveByCode($data['verified_code']))) {
            $discount = '';
            if ($activeVoucher['discount_rate']) {
                $discount = floor($activeVoucher['discount_rate']) . ' %';
            }
            else {
                $discount = Eshop::formatPrice($activeVoucher['discount']);
            }
            if ($activeVoucher['active_to']) {
                $message = __(
                    __FILE__, 
                    'Zľavový kód <code>%s</code> so zľavou %s je platný do %s',
                    $data['verified_code'],
                    $discount,
                    Date::format($activeVoucher['active_to'], 'j.n.Y')
                );
            }
            else {
                $message = __(
                    __FILE__, 
                    'Zľavový kód <code>%s</code> so zľavou %s je stále platný',
                    $data['verified_code'],
                    $discount
                );
            }
            $active = true;
        }
        else {
            $discount = '';
            if ($voucher['discount_rate']) {
                $discount = floor($voucher['discount_rate']) . ' %';
            }
            else {
                $discount = Eshop::formatPrice($voucher['discount']);
            }
            $now = date('Y-m-d');
            if (
                $voucher['active_from']
                && $voucher['active_from'] > $now
                && $voucher['active']
            ) {
                if ($voucher['active_to']) {
                    $message = __(
                        __FILE__, 
                        'Zľavový kód <code>%s</code> so zľavou %s je platný v období od %s do %s',
                        $data['verified_code'],
                        $discount,
                        Date::format($voucher['active_from'], 'j.n.Y'),
                        Date::format($voucher['active_to'], 'j.n.Y')
                    );
                    $active = true;
                }
                else {
                    $message = __(
                        __FILE__, 
                        'Zľavový kód <code>%s</code> so zľavou %s je platný od %s',
                        $data['verified_code'],
                        $discount,
                        Date::format($voucher['active_from'], 'j.n.Y')
                    );
                    $active = true;
                }
            }
            elseif (
                $voucher['active_to']
                && $voucher['active_to'] < $now
            ) {
                $message = __(
                    __FILE__, 
                    'Platnosť zľavového kódu <code>%s</code> skončila %s',
                    $data['verified_code'],
                    Date::format($voucher['active_to'], 'j.n.Y')
                );
            }
            else {
                $message = __(__FILE__, 'Zadaný zľavový kód už nie je platný');
            }
        }
        if ($active) {
            if (!empty($activeVoucher['id'])) {
                $voucherId = $activeVoucher['id'];
            }
            else {
                $voucherId = $voucher['id'];
            }
            $voucherDetails = $this->findAll($voucherId);
            if ($voucherDetails['product_ids']) {
                $this->loadModel('EshopProduct');
                $Product = new EshopProduct();
                $message .= ' ' . __(
                    __FILE__,
                    '<b>pre produkty:</b> %s',
                    '"' . implode(
                        '", "', 
                        $Product->findList(array(
                            'fields' => 'EshopProduct.name',
                            'conditions' => array(
                                'EshopProduct.id' => $voucherDetails['product_ids'],
                            ),
                        ))
                    ) . '"'
                );
                if ($voucherDetails['category_ids']) {
                    $message .= ' ' . __(__FILE__, 'alebo');
                }
            }
            if ($voucherDetails['category_ids']) {
                $this->loadModel('EshopProductCategory');
                $Category = new EshopProductCategory();
                $message .= ' ' . __(
                    __FILE__,
                    '<b>pre produkty zaradené v kategóriách:</b> %s',
                    '"' . implode(
                        '", "', 
                        $Category->findList(array(
                            'fields' => 'EshopProductCategory.name',
                            'conditions' => array(
                                'EshopProductCategory.id' => $voucherDetails['category_ids'],
                            ),
                        ))
                    ) . '"'
                );
            }
        }
        return array(
            'message' => $message,
            'active' => $active,
        );
    }

    /**
     * Returns record of specified vouucher plus HABTM categories (ids are in category_ids field)
     * and HABTM products (ids are in product_ids field)
     * 
     * @param array $voucherId
     * 
     * @return array
     * 
     * @throws Exception_DB_TablesReservationFailure
     */
    public function findAll($voucherId = null) {
        // reserve tables (to avoid writes while reading data)
        $this->reserveTables('EshopVoucher_findAll', array(
            'EshopVoucher',
            'EshopVoucherCategory',
            'EshopVoucherProduct',
        )); 
        
        $voucher = $this->findFirstBy('id', $voucherId);

        // get categories ids
        $VoucherCategory = $this->loadModel('EshopVoucherCategory', true);
        $voucher['category_ids'] = $VoucherCategory->findList(array(
            'key' => 'EshopVoucherCategory.run_eshop_product_categories_id',
            'fields' => array(
                'EshopVoucherCategory.run_eshop_product_categories_id',
            ),
            'conditions' => array('EshopVoucherCategory.run_eshop_vouchers_id' => $voucherId),
        ));

        // get product ids
        $VoucherProduct = $this->loadModel('EshopVoucherProduct', true);
        $voucher['product_ids'] = $VoucherProduct->findList(array(
            'key' => 'EshopVoucherProduct.run_eshop_products_id',
            'fields' => array(
                'EshopVoucherProduct.run_eshop_products_id',
            ),
            'conditions' => array('EshopVoucherProduct.run_eshop_vouchers_id' => $voucherId),
        ));

        $this->unreserveTables('EshopVoucher_findAll');

        return $voucher;
    }

    /**
     * Saves standard voucher fields plus HABTM categories (ids are in category_ids field)
     * and HABTM products (ids are in product_ids field)
     * 
     * @param array $data
     * @param array $options
     * 
     * @return bool|array Array of saved data on success and FALSE on validation or processing failure.
     * 
     * @throws Exception_DB_TablesReservationFailure
     * @throws Exception
     */
    public function saveAll($data, $options = array()) {
        // set 'on' and  'alternative' options 
        $options['on'] = ($this->isCreation($data, $options)) ? 'create' : 'update';
        $options['alternative'] = 'backend';

        try {
            $this->reserveTables('EshopVoucher_saveAll', array(
                'EshopVoucher',
                'EshopVoucherCategory',
                'EshopVoucherProduct',
            ), array('tries' => 20, 'retryTime' => 1000));

            // normalize and turn normalization off for further processing
            $data = $this->normalize($data, $options);
            $options['normalize'] = false;

            // validate and turn validation off for further processing
            if (!$this->validate($data, $options)) {
                $this->unreserveTables('EshopVoucher_saveAll');
                return false;
            }
            $options['validate'] = false;

            // save
            DB::startTransaction('EshopVoucher_saveAll');
            $result = $this->save($data, $options);
            if (!$result) {
                DB::rollbackTransaction('EshopVoucher_saveAll');
                $this->unreserveTables('EshopVoucher_saveAll');
                return false;
            }
            $data = $result;
            $voucherId = $this->getPropertyId();
            // create batch for "hasMany" related items
            $delete = array();
            $create = array();
            // save category_ids
            if (isset($data['category_ids'])) {
                $delete['EshopVoucherCategory'] = array(
                    'run_eshop_vouchers_id' => $voucherId,
                );
                foreach ($data['category_ids'] as $categoryId) {
                    $create['EshopVoucherCategory'][] = array(
                        'run_eshop_vouchers_id' => $voucherId,
                        'run_eshop_product_categories_id' => $categoryId,
                    );
                }
            }
            // save product_ids
            if (isset($data['product_ids'])) {
                $delete['EshopVoucherProduct'] = array(
                    'run_eshop_vouchers_id' => $voucherId,
                );
                foreach ($data['product_ids'] as $productId) {
                    $create['EshopVoucherProduct'][] = array(
                        'run_eshop_vouchers_id' => $voucherId,
                        'run_eshop_products_id' => $productId,
                    );
                }
            }
            $batch = array();
            if ($delete) {
                $batch['delete'] = &$delete;
            }
            if ($create) {
                $batch['create'] = &$create;
            }
            if (!empty($batch)) {
                $this->saveBatch($batch, array('reserve' => false));
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopVoucher_saveAll');
            $this->unreserveTables('EshopVoucher_saveAll');
            throw $e;
        }

        DB::commitTransaction('EshopVoucher_saveAll');
        $this->unreserveTables('EshopVoucher_saveAll');

        return $data;
    }
}
