<?php
// namespaces of https://packagist.org/packages/org.majkel/dbase used in 
// and ::exportMrpProductManufacturers()
use org\majkel\dbase\Builder;
use org\majkel\dbase\Format;
use org\majkel\dbase\Field;

class EshopExport extends Model {
    
    //
    // POHODA EXPORT
    //
    
    /**
     * Generate single invoice XML file for system Pohoda
     * (see http://www.stormware.sk/xml/dokladyimport.aspx#Faktury)
     * 
     * @param int $orderId Eshop order id
     * 
     * @return string App root relative filepath to exported XML file
     * 
     * @throws Exception on failure
     */
    public function exportPohodaInvoiceXml($orderId) {
        
        $Order = $this->loadModel('EshopOrder', true);        
        $order = $Order->findFirst(array(
            'conditions' => array(
                'id' => $orderId
            ),
        ));
        if (!$order) {
            throw new Exception(__e(__FILE__, 'Invalid order id'));
        }
        $order['items'] = $Order->getProductsDetails($orderId);
        // decode dynamic and static attributes
        foreach ($order['items'] as &$item) {
            // static
            $item['static_attributes'] = json_decode($item['static_attributes'], true);
            $tmp = '';
            foreach($item['static_attributes'] as $k => $v) {
                if ($tmp) {
                    $tmp .= ', ';
                }
                $tmp .= $k . ': ' . $v;
            }
            $item['static_attributes'] = $tmp;
            // dynamic
            $item['dynamic_attributes'] = json_decode($item['dynamic_attributes'], true);
            $tmp = '';
            foreach($item['dynamic_attributes'] as $k => $v) {
                if ($tmp) {
                    $tmp .= ', ';
                }
                $tmp .= $k . ': ' . $v;
            }
            $item['dynamic_attributes'] = $tmp;
        }
        unset($item);
        
        $cid = $this->getSetting('merchant.cid');
        if (empty($cid)) {
            throw new Exception(__e(__FILE__, 'Setting of merchant CID (merchant.cid) is missing.'));
        }
        $content = $this->loadView('EshopExport/exportPohodaInvoiceXml', array(
            'record' => $order,
            'cid' => $cid,
            'defaultTaxRate' => $this->getSetting('defaultTaxRate'),
            'dataPackId' => time(),
        ));

        $dir = 'userfiles' . DS . 'files' . DS . 'Pohoda';
        File::ensurePath($dir);
        $file = $dir . DS . 'INVOICE' . $order['number'] . '.xml';
        if(!file_put_contents(ROOT . DS . $file, $content)) {
            throw new Exception(__e(__FILE__, 'Creating of xml file for pohoda (%s) has failed.', ROOT . DS . $file));
        }
        return $file;
    }
    
    /**
     * Generate osingle order XML file for system Pohoda
     * (see http://www.stormware.sk/xml/dokladyimport.aspx#Objedn%C3%A1vky)
     * 
     * @param int $orderId Eshop order id
     * 
     * @return string App root relative filepath to exported XML file
     * 
     * @throws Exception on failure
     */
    public function exportPohodaOrderXml($orderId) {
        
        $Order = $this->loadModel('EshopOrder', true);
        $order = $Order->findFirst(array(
            'conditions' => array(
                'id' => $orderId
            ),
        ));
        if (!$order) {
            throw new Exception(__e(__FILE__, 'Invalid order id'));
        }
        $order['items'] = $Order->getProductsDetails($orderId);
        // decode dynamic and static attributes
        foreach ($order['items'] as &$item) {
            // static
            $item['static_attributes'] = json_decode($item['static_attributes'], true);
            $tmp = '';
            foreach($item['static_attributes'] as $k => $v) {
                if ($tmp) {
                    $tmp .= ', ';
                }
                $tmp .= $k . ': ' . $v;
            }
            $item['static_attributes'] = $tmp;
            // dynamic
            $item['dynamic_attributes'] = json_decode($item['dynamic_attributes'], true);
            $tmp = '';
            foreach($item['dynamic_attributes'] as $k => $v) {
                if ($tmp) {
                    $tmp .= ', ';
                }
                $tmp .= $k . ': ' . $v;
            }
            $item['dynamic_attributes'] = $tmp;
        }
        unset($item);
        
        $cid = $this->getSetting('merchant.cid');
        if (empty($cid)) {
            throw new Exception(__e(__FILE__, 'Setting of merchant CID (merchant.cid) is missing.'));
        }
        $content = $this->loadView('EshopExport/exportPohodaOrderXml', array(
            'record' => $order,
            'cid' => $cid,
            'defaultTaxRate' => $this->getSetting('defaultTaxRate'),
            'dataPackId' => time(),
        ));

        $dir = 'userfiles' . DS . 'files' . DS . 'Pohoda';
        File::ensurePath($dir);
        $file = $dir . DS . 'ORDER' . $order['number'] . '.xml';
        if(!file_put_contents(ROOT . DS . $file, $content)) {
            throw new Exception(__e(__FILE__, 'Creating of xml file for pohoda (%s) has failed.', ROOT . DS . $file));
        }
        return $file;
    }
    
    /**
     * Generate single product XML file for system Pohoda
     * (see http://www.stormware.sk/xml/dokladyimport.aspx#Z%C3%A1soby)
     * 
     * @param int $productId Eshop product id
     * 
     * @return string App root relative filepath to exported XML file
     * 
     * @throws Exception on failure
     */
    public function exportPohodaProductXml($productId) {
        
        $Product = $this->loadModel('EshopProduct', true);
        
        $product = $Product->getDetails($productId);
        if (!$product) {
            throw new Exception(__e(__FILE__, 'Invalid product id'));
        }
        
        $cid = $this->getSetting('merchant.cid');
        if (empty($cid)) {
            throw new Exception(__e(__FILE__, 'Setting of merchant CID (merchant.cid) is missing.'));
        }
        $content = $this->loadView('EshopExport/exportPohodaProductXml', array(
            'record' => $product,
            'cid' => $cid,
            'defaultTaxRate' => $this->getSetting('defaultTaxRate'),
            'dataPackId' => time(),
        ));
        
        $dir = 'userfiles' . DS . 'files' . DS . 'Pohoda';
        File::ensurePath($dir);
        $file = $dir . DS . 'PRODUCT' . $productId . '.xml';
        if(!file_put_contents(ROOT . DS . $file, $content)) {
            throw new Exception(__e(__FILE__, 'Creating of xml file for pohoda (%s) has failed.', ROOT . DS . $file));
        }
        return $file;
    }
    
    /**
     * Generate many products XML file for system Pohoda
     * (see http://www.stormware.sk/xml/dokladyimport.aspx#Z%C3%A1soby)
     * 
     * ATTENTION this method turns off slq logging!
     * 
     * @param array $options Following are available:
     *      - 'conditions' (array) Conditions to retrieve products for export. 
     *          Default to NULL, means all products are exported.
     *      - 'suffix' (array) Custom file suffix to make the exported file name
     *          more descriptive. Defaults to NULL.
     * 
     * @return string App root relative filepath to exported XML file. FALSE if
     *  no products has been retrieved according to given conditions
     * 
     * @throws Exception on failure
     */
    public function exportPohodaProductsXml($options = array()) {
        $defaults = array(
            'conditions' => null,
            'suffix' => null,
        );
        $options = array_merge($defaults, $options);
        
        $Product = $this->loadModel('EshopProduct', true);
        
        $cid = $this->getSetting('merchant.cid');
        if (empty($cid)) {
            throw new Exception(__e(__FILE__, 'Setting of merchant CID (merchant.cid) is missing.'));
        }
        
        //find product ids
        $products = $Product->find(array(
            'fields' => array('id'),
            'conditions' => $options['conditions'],
        ));
        if (!$products) {
            return false;
        }
        // create file name
        $time = time();
        $dir = 'userfiles' . DS . 'files' . DS . 'Pohoda';
        File::ensurePath($dir);
        $file = $dir . DS . 'PRODUCTS' . $time . $options['suffix'] . '.xml';
        
        // open file for writing
        if (!($fh = fopen(ROOT . DS . $file, 'w+'))) {
            throw new Exception(__e(__FILE__, 'Creating of xml file for pohoda (%s) has failed.', ROOT . DS . $file));
        }
        
        // write XML header
        $content = $this->loadView('EshopExport/exportPohodaProductXml', array(
            'cid' => $cid,
            'dataPackId' => $time,
            'part' => 'header',
        ));
        fwrite($fh, $content);
        
        // write products
        $defaultTaxRate = $this->getSetting('defaultTaxRate');
        // turn off sql logs to not get error ERR_RESPONSE_HEADERS_TOO_BIG
        App::setSqlLogging(false);
        foreach($products as $product) {
            $product = $Product->getDetails($product['id']);
            $content = $this->loadView('EshopExport/exportPohodaProductXml', array(
                'record' => $product,
                'defaultTaxRate' => $defaultTaxRate,
                'part' => 'body'
            ));
            fwrite($fh, $content);
        }
        // restore sql loging to previous state
        //App::setSqlLogging(); // this can break functionality if slqLogging is turned off by caller of this method
        
        // write footer
        $content = $this->loadView('EshopExport/exportPohodaProductXml', array(
            'part' => 'footer',
        ));
        fwrite($fh, $content);
        
        fclose($fh);
        
        return $file;
    }
    
    /**
     * Puts the provided file to Pohoda FTP server
     * 
     * @param string $file App root relative path to file
     * 
     * @throws Exception on failure
     */
    public function putFileToPohodaFtp($file) {
        // validate settings
        if (!($ftpHost = $this->getSetting('Export.pohoda.ftpHost'))) {
            throw new Exception(__e(__FILE__, 'Missing FTP host setting (Export.pohoda.ftpHost) to put Pohoda XML'));
        }
        if (!($ftpUsername = $this->getSetting('Export.pohoda.ftpUsername'))) {
            throw new Exception(__e(__FILE__, 'Missing FTP username setting (Export.pohoda.ftpUsername) to put Pohoda XML'));
        }
        if (!($ftpPassword = $this->getSetting('Export.pohoda.ftpPassword'))) {
            throw new Exception(__e(__FILE__, 'Missing FTP password setting (Export.pohoda.ftpPassword) to put Pohoda XML'));
        }
        // files created during development on localhost put to FTP subfolder /test 
        $ftpDir = '/';
        if (ON_LOCALHOST) {
            $ftpDir = '/test';
        }
        App::loadLib('App', 'Ftp');
        $Ftp = new Ftp($ftpHost, array(
            'username' => $ftpUsername,
            'password' => $ftpPassword,
        ));
        $Ftp->put($file, $ftpDir, array('overwrite' => true,));
        $Ftp->close();
    }
    
    /**
     * Exports single order XML file for system Pohoda to dedicated FTP server
     * 
     * @param int $orderId Id of order to export
     * 
     * @throws Exceptions on failure
     */
    public function exportPohodaOrderXmlToFtp($orderId) {
        $file = $this->exportPohodaOrderXml($orderId);
        $this->putFileToPohodaFtp($file);
    }
    
    /**
     * Exports single product XML file for system Pohoda to dedicated FTP server
     * 
     * @param int $productId Id of product to export
     * 
     * @throws Exceptions on failure
     */
    public function exportPohodaProductXmlToFtp($productId) {
        $file = $this->exportPohodaProductXml($productId);
        $this->putFileToPohodaFtp($file);
    }
    
    /**
     * Exports many products XML file for system Pohoda to dedicated FTP server
     * 
     * @param array $options Following are available:
     *      - 'conditions' (array) Conditions to retrieve products for export. 
     *          Default to NULL, means all products are exported.
     *      - 'suffix' (array) Custom file suffix to make the exported file name
     *          more descriptive. Defaults to NULL.
     * 
     * @throws Exceptions on failure
     */
    public function exportPohodaProductsXmlToFtp($options = array()) {
        if (($file = $this->exportPohodaProductsXml($options))) {
            $this->putFileToPohodaFtp($file);
        }
    }
    
    
    //
    // PRICE ENGINES
    //
    
    /**
     * Exports all active and available products into XMLs suitable for importing into various
     * price engines.
     * 
     * @see
     * heureka: http://sluzby.heureka.sk/napoveda/xml-feed/
     * zbozicz: https://napoveda.seznam.cz/cz/zbozi/specifikace-xml-pro-obchody/specifikace-xml-feedu/
     * pricemania: http://files.pricemania.sk/pricemania-struktura-xml-feedu.pdf
     * tovarsk: http://www.tovar.sk/vzor-xml/
     * najnakup: http://www.najnakup.sk/prenos-dat
     * 
     * Frequency of feed reloads by price engines:
     * Heureka: V případě PPC režimu je xml soubor importován každé cca 2 hodiny.
     *          V případě FREE režimu to jsou každé cca 4 hodiny. (<EMAIL>)
     * 
     * ATTENTION: This method have some uniknihy specifics so revise it well before 
     * using on other project! E.g it throws out toplevel category name when creating 
     * category paths.
     * Check if calculation of price is OK - e.g. actual price is not fetched by DB query
     * 
     * @param string|array $engines Engine(s) you wish to export to. Acceptable values:
     *      'pricemania', 'superdeal', 'najnakup', 'enakup', 'tovarsk', 'heureka', 'pesiazona', 'zbozicz',
     *      'google'
     * @param array $options Folowing are available:
     *      - 'limit' (int) Limit of exported products. For debuging purposes
     *      - 'taxlessPrice' (bool) If TRUE then prices in XML feed are taxless.
     *          For the moment this is implemented only for 'google' engine. Defaults to FALSE.
     * 
     * @return array Progress array
     * 
     * @throws Exception on failure
     */
    public function exportPriceEnginesXml($engines, $options = array()){
        $defaults = array(
            'limit' => null,
            'taxlessPrice' => false,
            'microtimeReserve' => 10000,
        );
        $options = array_merge($defaults, $options);
        // normalize engines
        $engines = (array)$engines;
        
        // adjust execution time
        set_time_limit(600);
        ini_set('memory_limit', '512M');
        
////mojo: seems this is not needed - content is not generated directly to output buffer        
        // turn of debuging to not break header
        App::setDebug(false);
        App::setSqlLogging(false);
        
        // create temp files and write XML headers
        $handles = array();
        foreach ($engines as $engine) {
            $filename = TMP . DS . $engine . '.xml';
            $handles[$engine] = fopen($filename, 'w+');
            if (!$handles[$engine]) {
                throw new Exception(__e(__FILE__, 'Creating of xml file %s has failed.', $filename));
            } 
            // write headers
            fwrite($handles[$engine], $this->getPriceEnginesXml($engine, array('part' => 'header')) );
        }
                
        // proces products
        // 
        // We need following fields (and virtual fields) of EshopProduct:
        // id, name, description, slug, sold_out, price_actual_taxed, ean, isbn, authors, 
        // shipment_time, image, category_path,  manufacturer
        // 
        // this is imitation of code from EshopProduct::getDetails(), duplicated
        // here because of optimisation reasons. EshopProduct::getDetails() always 
        // returns array. We use the resource here.
        $Product = $this->loadModel('EshopProduct', true);
        $productsResource = $Product->find(array(
            'conditions' => array(
                EshopProduct::getPublishedConditions(),
                EshopProduct::getNormalProductsConditions(array(
                    'giftCardProducts' => 'allow'
                )),
                'EshopProduct.unavailable' => false,
            ),
            'fields' => array(
                'EshopProduct.id',
                'EshopProduct.slug',
                'EshopProduct.name',
                'EshopProduct.description',
                'EshopProduct.code',
                'EshopProduct.ean',
                'EshopProduct.image',
                'EshopProduct.price',
                'EshopProduct.discount_price',
                'EshopProduct.tax_rate',
                'GROUP_CONCAT(DISTINCT EshopAuthor.name SEPARATOR ", ") as authors',
                'EshopProductCategory.id AS category_id',
                'EshopProductCategory.path AS category_parent_ids',
                'EshopProductCategory.heureka_name AS category_heureka_name',
                'EshopProduct.heureka_category_name',
                'EshopProductCategory.zbozicz_name AS category_zbozicz_name',
                'EshopManufacturer.name AS manufacturer',
                'EshopProduct.stock',
                'EshopProduct.shipment_time_off_stock',
                'EshopProduct.available_from',
            ),
            'literals' => array(
                'fields' => array(
                    'GROUP_CONCAT(DISTINCT EshopAuthor.name SEPARATOR ", ") as authors'
                )
            ),
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'EshopProductCategoryProduct',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopProductCategory',
                    // habtm conditions cannot be autogenerated
                    'conditions' => array(
                        'EshopProductCategoryProduct.run_eshop_product_categories_id = EshopProductCategory.id'
                    )
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopProductAuthor',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopAuthor',
                    // habtm conditions cannot be autogenerated
                    'conditions' => array(
                        'EshopAuthor.id = EshopProductAuthor.run_eshop_authors_id'
                    )
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopManufacturer',
                )
            ),
            'group' => array(
                'EshopProduct.id'
            ),
            'order' => 'EshopProduct.id ASC',
            'limit' => $options['limit'],
            'resource' => true,
        ));
        
        // set products details
        $shipmentTimeOnStock = $this->getSetting('EshopProduct.shipmentTimeOnStock');
        $shipmentTimeOffStock = $this->getSetting('EshopProduct.shipmentTimeOffStock');
        $freeShipmentLimit = $this->getSetting('EshopShipment.freeShipmentProductsTotal');
        $pricesAreTaxed = $this->getSetting('pricesAreTaxed');
        $defaultTaxRate = $this->getSetting('defaultTaxRate');
        $fileFields = $Product->getFileFieldOptions();
        $Category = $this->loadModel('EshopProductCategory', true);
        $categories = $Category->findList(array(
            'key' => 'id',
            'fields' => array(
                'EshopProductCategory.name', 
            ),
        )); 
        $productViewUrlBase = App::getUrl(array(
            'locator' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),
            'absolute' => true,
        ));
        // get shipment prices (this is used for Heureka DELIVERY tag)
        $Payment = App::loadModel('Payment', 'PaymentMethod', true);
        $codPaymentIds = $Payment->findList(array(
            'conditions' => array(
                'pid' => array('courier', 'cod', 'cashondelivery')
            ),
            'fields' => array('id'),
        ));
        $Shipment = $this->loadModel('EshopShipmentMethod', true);
        $shipmentPrices = $Shipment->find(array(
            'fields' => array(
                'EshopShipmentMethod.id',
                'EshopShipmentMethod.name', //debug
                'EshopShipmentMethod.price',
                'EshopShipmentMethod.heureka_code', 
            ),
            'order' => array('price ASC'),
            'conditions' => array(
                'EshopShipmentMethod.active' => true,
                'EshopShipmentMethod.delivery_country' => null,
            ),
        ));
        $ShipmentPaymentMethod = $this->loadModel('EshopShipmentPaymentMethod', true);
        $shipmentPaymentMethods = $ShipmentPaymentMethod->findList(array(
            'key' => 'run_eshop_shipment_methods_id',
            'fields' => array('run_payment_methods_id'),
            'accumulate' => true,
        ));
        // - separate cod shipments from non-cod shipments, find min shipment price
        // and remove items without heureka_code
        $minShipmentPrice = null;
        $codShipments = array();
        foreach ($shipmentPrices as $i => $shipment) {
            if (!$pricesAreTaxed) {
                $shipment['price'] = Number::getTaxedPrice($shipment['price'], $defaultTaxRate, 2);
            }            
            if (
                $shipment['price'] > 0
                && (
                    $minShipmentPrice === null
                    || $shipment['price'] < $minShipmentPrice
                )
            ) {
                $minShipmentPrice = $shipment['price'];
            }
            // cod shipments
            if (
                $shipment['heureka_code']
                && !empty($shipmentPaymentMethods[$shipment['id']])
                && array_intersect($shipmentPaymentMethods[$shipment['id']], $codPaymentIds)
            ) {
                $codShipments[$shipment['heureka_code']] = $shipment;
                unset($shipmentPrices[$i]);
            }
            // remove items without heureka_code
            elseif (!$shipment['heureka_code']) {
                unset($shipmentPrices[$i]);
            }
        }
        // - set cod_price for separated non-cod shipments
        foreach ($shipmentPrices as &$shipment) {
            if (!empty($codShipments[$shipment['heureka_code']])) {
                $shipment['cod_price'] = $codShipments[$shipment['heureka_code']]['price'];
            }
        }
        unset($shipment);
        
        // batch variables
        $batchSize = 100; // temp files will be written when we have processed this many products
        $batchCount = 0;
        $body = array_fill_keys($engines, '');
        $categoryPaths = array(); // cache created on fly
        $progress = array(
            'total' => DB::getNumberOfRows($productsResource),
            'processed' => 0,
            'interrupted' => null,
        );
        while ($product = DB::fetchArray($productsResource)) {
            
            // check remaining time - finish the processing before php 
            // would be interrupted by execution time constrain
            if (App::getFreeMicrotime() < $options['microtimeReserve']) {
                $progress['interrupted'] = true;
                break;
            }
            
            // set prices
            $product = $Product->getPrices($product, array('pricesAreTaxed' => $pricesAreTaxed));
            // set shipment times
            $product = $Product->getShipmentTimes($product, array(
                'shipmentTimeOnStock' => $shipmentTimeOnStock,
                'shipmentTimeOffStock' => $shipmentTimeOffStock,
            ));
            // set whole paths to image but keep the original image bacause of 
            // google engine processing here below
            $image = $product['image'];
            $product['image'] = $Product->getFileFieldUrlPaths('image', array('file' => $product['image'], 'variant' => 'large'));
            // set category names path
            if (!empty($categoryPaths[$product['category_id']])) {
                // fetch it from cache if possible
                $product['category_path'] = $categoryPaths[$product['category_id']];
            }
            else {
                $product['category_parent_ids'] = Model::getParentIdsFromTreePath($product['category_parent_ids']);
                $product['category_parent_ids'][] = $product['category_id'];
                // throw out the top level category 
                array_shift($product['category_parent_ids']); 
                $product['category_path'] = array();
                foreach ($product['category_parent_ids'] as $categoryId) {
                    $product['category_path'][] = $categories[$categoryId];
                }
                // put the created path to cache created on fly
                $categoryPaths[$product['category_id']] = $product['category_path'];
            }
            // create XML body     
            foreach ($engines as $engine) {
                if (
                    // in case of google and facebook feed skip products without images or without 'large'
                    // variant because google and facebook requires images with some min dimensions 
                    // (https://support.google.com/merchants/answer/6205396 )
                    (
                        $engine === 'google'
                        || $engine === 'facebook'
                    )
                    && (
                        empty($image)
                        // remove timestamp GET parameter and convert URL path to path
                        || !($imageUrlPath = parse_url($product['image']['large'], PHP_URL_PATH))
                        || !($imagePath = File::normalizePath($imageUrlPath))
                        || !file_exists(ROOT . DS . $imagePath)
                    )
                    ||
                    // for heureka export only selected products having specified 
                    // heureka category name
                    $engine === 'heureka' 
                    && empty($product['category_heureka_name'])
                    && empty($product['heureka_category_name'])
                    ||
                    // in case facebook feed skip products without description  
                    $engine === 'facebook'
                    && !trim(strip_tags(Sanitize::invalidXmlChars($product['description'])))
                        
                ) {
                    continue;
                }
                $body[$engine] .= $this->getPriceEnginesXml($engine, array(
                    'part' => 'body',
                    'product' => $product,
                    'productViewUrlBase' => $productViewUrlBase,
                    'shipmentPrices' => $shipmentPrices,
                    'minShipmentPrice' => $minShipmentPrice,
                    'freeShipmentLimit' => $freeShipmentLimit,
                    'taxlessPrice' => $options['taxlessPrice'],
                ));
            }
            // check the batch 
            $progress['processed']++;
            $batchCount++;
            if ($batchCount > $batchSize) {
                // write into the temp files
                foreach ($engines as $engine) {
                    fwrite($handles[$engine], $body[$engine]);
                }
                // reset the body
                $body = array_fill_keys($engines, '');
                // reset the counter
                $batchCount = 0;
            } 
        }   
        // write the remaining content
        foreach ($engines as $engine) {
            fwrite($handles[$engine], $body[$engine]);
        }

        // close the temp files
        foreach ($engines as $engine) {
            fwrite($handles[$engine], $this->getPriceEnginesXml($engine, array('part' => 'footer')));
            fclose($handles[$engine]);
        }

        if (empty($progress['interrupted'])) {
            // move the files to webroot to make them available for engines
            clearstatcache();
            foreach ($engines as $engine) {
                rename(TMP . DS . $engine . '.xml', ROOT . DS . $engine . '.xml');
            }

            // calculate CRC for pricemania XML exec(cksum 'pricemania.xml' > 'pricemania.crc')
            if (in_array('pricemania', $engines)) {
                $checksumPricemania = hash_file('crc32', ROOT . DS . 'pricemania.xml');
                file_put_contents(ROOT . DS . 'pricemania.crc', $checksumPricemania);
            }

            // zip files
            if (in_array('najnakup', $engines)) {
                File::zip('najnakup.xml');
            }
            if (in_array('tovarsk', $engines)) {
                File::zip('tovarsk.xml');
            }
        }    
        else {
            App::logError(
                __a(__FILE__, 'Export to price engine(s) %s has been interrupted', implode(', ', $engines)), 
                array(
                    'var' => $progress, 
                    'email' => true,
                )
            );
        }
        
        return $progress;
    }
        
    /**
     * Returns product formated into XML for price engine exports. The output depends
     * on the $engine parameter.
     * 
     * @see
     * heureka: http://sluzby.heureka.sk/napoveda/xml-feed/
     * zbozicz: https://napoveda.seznam.cz/cz/zbozi/specifikace-xml-pro-obchody/specifikace-xml-feedu/
     * pricemania: http://files.pricemania.sk/pricemania-struktura-xml-feedu.pdf
     * tovarsk: http://www.tovar.sk/vzor-xml/
     * najnakup: http://www.najnakup.sk/prenos-dat
     * 
     * @param array $product 
     * @param string $engine Possible values: najnakup, pricemania, heureka, tovarsk
     * @param array $options Possible keys:
     *      - productViewUrlBase (string) absolute URL of the page with product details. If not specified,
     *          it is pulled from the DB.
     * @access private
     * @return string
     */
    protected function getPriceEnginesXml($engine, $options = array()) {
        $defaults = array(
            'part' => 'body', // 'header', 'body', 'footer'
            'product' => array(),
            'shipmentPrices' => array(),
            'minShipmentPrice' => 0,
            'freeShipmentLimit' => 0,
            'productViewUrlBase' => null,
            'imageVariant' => 'large',
            'taxlessPrice' => false,
        );
        $options = array_merge($defaults, $options);

        // header 
        if ($options['part'] == 'header') {
            switch ($engine) {

                case 'pricemania':
                    $header = '<?xml version="1.0" encoding="utf-8" ?>' . "\n";
                    $header .= '<products>' . "\n";
                    return $header;
                
                case 'superdeal':
                    $header = '<?xml version="1.0" encoding="utf-8" ?>' . "\n";
                    $header .= '<shop>' . "\n";
                    return $header;
                    
                case 'zbozicz':
                    $header = '<?xml version="1.0" encoding="utf-8" ?>' . "\r\n";
                    $header .= '<SHOP xmlns="http://www.zbozi.cz/ns/offer/1.0">' . "\r\n";
                    return $header;
                
                case 'facebook':
                case 'google':
                    $header = '<?xml version="1.0" encoding="utf-8" ?>' . "\n";
                    $header .= '<rss version="2.0" xmlns:g="http://base.google.com/ns/1.0">' . "\n";
                    $header .= '<channel>' . "\n";
                    $header .= 
                        '<title>' . 
                            App::getSetting('App', 'name') . 
                        '</title>' . "\n";
                    $header .=
                        '<link>' .
                            App::getUrl(array('locator' => '/', 'absolute' => true)) .
                        '</link>' . "\n";
                    $header .=
                        '<description>' .
                            App::getSetting('App', 'seo.defaultDescription') .
                        '</description>' . "\n";
                    return $header;
                
                case 'najnakup':
                case 'enakup':
                case 'tovarsk':
                case 'heureka':
                case 'pesiazona':
                default:
                    $header = '<?xml version="1.0" encoding="utf-8" ?>' . "\n";
                    $header .= '<SHOP>' . "\n";
                    return $header;
            }
        }

        // footer
        if ($options['part'] == 'footer') {
            switch ($engine) {

                case 'pricemania':
                    $footer = '</products>' . "\n";
                    return $footer;
                
                case 'superdeal':
                    $footer = '</shop>' . "\n";
                    return $footer;
                
                case 'facebook':
                case 'google':
                    $footer = '</channel>' . "\n";
                    $footer .= '</rss>' . "\n";
                    return $footer;
                
                case 'najnakup':
                case 'enakup':
                case 'tovarsk':
                case 'heureka':
                case 'zbozicz':
                case 'pesiazona':
                default:
                    $footer = '</SHOP>' . "\n";
                    return $footer;
            }
        }

        // body
        $body = '';
        if (!$options['productViewUrlBase']) {
            $options['productViewUrlBase'] = App::getUrl(array(
                'locator' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),
                'absolute' => true,
            ));
        }

        $product = $options['product'];
        
        if (empty($product['slug'])) {
            $product['slug'] = $product['id'];
        }
        
        if (!empty($product['image'][$options['imageVariant']])) {
            $product['image'] = App::getUrl(array(
                'locator' => $product['image'][$options['imageVariant']],
                'absolute' => true,
            ));
        }
        else {
            $product['image'] = '';
        }
        
        switch ($engine) {

            case 'heureka':
                if (!empty($product['stock'])) {
                    // to display 'On stock' on heureka set DELIVERY_DATE to 0
                    $product['shipment_time'] = 0; 
                }
                elseif (strtotime($product['available_from']) > time()) {
                    $product['shipment_time'] = $product['available_from'];
                }
                $body .= '  <SHOPITEM>' . "\n";
                $body .= '    <ITEM_ID>' . $product['code'] . '</ITEM_ID>' . "\n";
                $body .= '    <PRODUCTNAME><![CDATA[' . Sanitize::invalidXmlChars($product['name']) . ']]></PRODUCTNAME>' . "\n";
                $body .= '    <PRODUCT><![CDATA[' . Sanitize::invalidXmlChars(rtrim($product['name'] . ' - ' . $product['authors'], '- ')) . ']]></PRODUCT>' . "\n";
                $body .= '    <DESCRIPTION><![CDATA[' . Sanitize::invalidXmlChars($product['description']) . ']]></DESCRIPTION>' . "\n";
                $body .= '    <URL>' . $options['productViewUrlBase'] . '/' . $product['slug'] . '</URL>' . "\n";
                $body .= '    <IMGURL>' . $product['image'] . '</IMGURL>' . "\n";
                $body .= '    <PRICE_VAT>' . number_format($product['price_actual_taxed'], 2, ',', '') . '</PRICE_VAT>' . "\n";
                $body .= '    <ITEM_TYPE>new</ITEM_TYPE>' . "\n";
                $body .= '    <MANUFACTURER><![CDATA[' . Sanitize::invalidXmlChars($product['manufacturer']) . ']]></MANUFACTURER>' . "\n";
                if (!empty($product['heureka_category_name'])) {
                    $body .= '    <CATEGORYTEXT><![CDATA[' . Sanitize::invalidXmlChars($product['heureka_category_name']) . ']]></CATEGORYTEXT>' . "\n";
                }
                elseif (!empty($product['category_heureka_name'])) {
                    $body .= '    <CATEGORYTEXT><![CDATA[' . Sanitize::invalidXmlChars($product['category_heureka_name']) . ']]></CATEGORYTEXT>' . "\n";
                }
                else {
                    $body .= '    <CATEGORYTEXT><![CDATA[' . Sanitize::invalidXmlChars(implode(' | ', @$product['category_path'])) . ']]></CATEGORYTEXT>' . "\n";
                }
                $body .= '    <EAN>' . (empty($product['ean']) ? '' : $product['ean']) . '</EAN>' . "\n";
                $body .= '    <DELIVERY_DATE>' . $product['shipment_time'] . '</DELIVERY_DATE>' . "\n";
                if ($options['shipmentPrices']) {
                    foreach ($options['shipmentPrices'] as $price) {
                        if (empty($price['heureka_code'])) {
                            continue;
                        }
                        // get shipment price
                        $p = number_format($price['price'], 2, ',', '');
                        if (
                            $options['freeShipmentLimit'] 
                            && $product['price_actual_taxed'] >= $options['freeShipmentLimit']
                        ) {
                            $p = 0;
                        }
                        $cp = null;
                        if (isset($price['cod_price'])) {
                            $cp = number_format($price['cod_price'], 2, ',', '');
                            if (
                                $options['freeShipmentLimit'] 
                                && $product['price_actual_taxed'] >= $options['freeShipmentLimit']
                            ) {
                                $cp = 0;
                            }
                        }
                        $body .= '    <DELIVERY>' . "\n";
                        $body .= '       <DELIVERY_ID>' . $price['heureka_code'] . '</DELIVERY_ID>' . "\n";
                        $body .= '       <DELIVERY_PRICE>' . $p . '</DELIVERY_PRICE>' . "\n";
                        if ($cp !== null) {
                            $body .= '       <DELIVERY_PRICE_COD>' . $cp . '</DELIVERY_PRICE_COD>' . "\n";
                        }   
                        $body .= '    </DELIVERY>' . "\n";
                    }
                }
                $body .= '  </SHOPITEM>' . "\n";
                return $body;
                
            case 'google':
                // for all available product attributes see https://support.google.com/merchants/topic/6324338
                $currencyCode = Eshop::getActualCurrency('code');
                if ($product['stock'] > 0) {
                    $stockAvailability = "in stock";
                } else {
                    $stockAvailability = "out of stock";
                }

                $body .= '  <item>' . "\n";
                $body .= '    <g:id>' . $product['id'] . '</g:id>' . "\n";
                if (
                    !empty($product['ean'])
                    && (
                        strlen($product['ean']) == 8
                        || strlen($product['ean']) == 12
                        || strlen($product['ean']) == 13
                        || strlen($product['ean']) == 14
                    )
                ) {
                    $body .= "    <g:gtin>" . $product['ean'] . "</g:gtin>" . "\n";
                }
                $body .= '    <g:title><![CDATA[' . Sanitize::invalidXmlChars($product['name']) . ']]></g:title>' . "\n";
                $body .= '    <g:link>' . $options['productViewUrlBase'] . '/' . $product['slug'] . '</g:link>' . "\n";
                $body .= '    <g:description><![CDATA[' . strip_tags(Sanitize::invalidXmlChars($product['description'])) . ']]></g:description>' . "\n";
                $body .= '    <g:image_link>' . $product['image'] . '</g:image_link>' . "\n";
                if ($options['taxlessPrice']) {
                    $body .= "    <g:price>" . number_format($product['price_taxless'], 2, '.', '') . " $currencyCode</g:price>" . "\n";
                    if ($product['discount_rate'] > 0) {
                        $body .= "    <g:sale_price>" . number_format($product['price_actual_taxless'], 2, '.', '') . " $currencyCode</g:sale_price>" . "\n";
                    }
                } else {
                    $body .= "    <g:price>" . number_format($product['price_taxed'], 2, '.', '') . " $currencyCode</g:price>" . "\n";
                    if ($product['discount_rate'] > 0) {
                        $body .= "    <g:sale_price>" . number_format($product['price_actual_taxed'], 2, '.', '') . " $currencyCode</g:sale_price>" . "\n";
                    }
                }
                //$body .= '    <g:condition>' . '' . '</g:condition>' . "\n";
                $body .= '    <g:availability>' . $stockAvailability . '</g:availability>' . "\n";
                $body .= '    <g:product_type><![CDATA[' . implode(' > ', Sanitize::value($product['category_path'], array())) . ']]></g:product_type>' . "\n";
                $body .= '    <g:brand><![CDATA[' . Sanitize::invalidXmlChars($product['manufacturer']) . ']]></g:brand>' . "\n";
                $body .= '  </item>' . "\n";
                return $body;

            case 'facebook':
                // for all avaiable product attributes see:
                // https://www.facebook.com/business/help/120325381656392?id=725943027795860
                $currencyCode = Eshop::getActualCurrency('code');
                if ($product['stock'] > 0) {
                    $stockAvailability = "in stock";
                } else {
                    $stockAvailability = "out of stock";
                }

                $body .= '  <item>' . "\n";
                $body .= '    <g:id>' . $product['id'] . '</g:id>' . "\n";
                if (
                    !empty($product['ean'])
                    && (
                        strlen($product['ean']) == 8
                        || strlen($product['ean']) == 12
                        || strlen($product['ean']) == 13
                        || strlen($product['ean']) == 14
                    )
                ) {
                    $body .= "    <g:gtin>" . $product['ean'] . "</g:gtin>" . "\n";
                }
                $body .= '    <g:title><![CDATA[' . $product['name'] . ']]></g:title>' . "\n";
                $body .= '    <g:description><![CDATA[' . 
                        trim(strip_tags(Sanitize::invalidXmlChars($product['description']))) . 
                    ']]></g:description>' . "\n";
                $body .= '    <g:availability>' . $stockAvailability . '</g:availability>' . "\n";
                $body .= '    <g:condition>' . 'new' . '</g:condition>' . "\n";
                if ($options['taxlessPrice']) {
                    $body .= "    <g:price>" . number_format($product['price_taxless'], 2, '.', '') . " $currencyCode</g:price>" . "\n";
                    if ($product['discount_rate'] > 0) {
                        $body .= "    <g:sale_price>" . number_format($product['price_actual_taxless'], 2, '.', '') . " $currencyCode</g:sale_price>" . "\n";
                    }
                }
                else {
                    $body .= "    <g:price>" . number_format($product['price_taxed'], 2, '.', '') . " $currencyCode</g:price>" . "\n";
                    if ($product['discount_rate'] > 0) {
                        $body .= "    <g:sale_price>" . number_format($product['price_actual_taxed'], 2, '.', '') . " $currencyCode</g:sale_price>" . "\n";
                    }
                }
                $body .= '    <g:link>' . $options['productViewUrlBase'] . '/' . $product['slug'] . '</g:link>' . "\n";
                $body .= '    <g:image_link>' . $product['image'] . '</g:image_link>' . "\n";
                $body .= '    <g:brand><![CDATA[' . $product['manufacturer'] . ']]></g:brand>' . "\n";
                $body .= '    <g:mpn>' . $product['code'] . '</g:mpn>' . "\n";
                /*/>
                $body .= '    <g:google_product_category><![CDATA[' . 
                            implode(' > ', Sanitize::value($product['category_path'], array())) . 
                        ']]></g:google_product_category>' . "\n";
                //*/
                // shipment(s)
                $shipmentCountryCode = 'SK'; //HARDCODED
                foreach ($options['shipmentPrices'] as $price) {
                    // get shipment price
                    $shipmentPrice = $price['price'];
                    if (
                        $options['freeShipmentLimit']
                        && $product['price_actual_taxed'] >= $options['freeShipmentLimit']
                    ) {
                        $shipmentPrice = 0.0;
                    }
                    $body .= '    <g:shipping>' . "\n";
                    $body .= '      <g:country>' . $shipmentCountryCode . '</g:country>' . "\n";
                    $body .= '      <g:service>' . $price['name'] . '</g:service>' . "\n";
                    $body .= '      <g:price>' . number_format($shipmentPrice, 2, '.', '') . " $currencyCode</g:price>" ."\n";
                    $body .= '    </g:shipping>' . "\n";
                }
                $body .= '  </item>' . "\n";
                return $body;
                
            case 'zbozicz':
                if (!empty($product['stock'])) {
                    // to display 'On stock' on heureka set DELIVERY_DATE to 0
                    $product['shipment_time'] = 0; 
                }
                if (
                    !empty($product['available_from'])
                    && strtotime($product['available_from']) > time()
                ) {
                    $product['shipment_time'] = $product['available_from'];
                }
                $body .= '  <SHOPITEM>' . "\n";
                $body .= '    <ITEM_ID>' . $product['id'] . '</ITEM_ID>' . "\n";
                $body .= '    <PRODUCTNAME><![CDATA[' . Sanitize::invalidXmlChars($product['name']) . ']]></PRODUCTNAME>' . "\n";
                $body .= '    <PRODUCT><![CDATA[' . Sanitize::invalidXmlChars(rtrim($product['name'] . ' - ' . $product['authors'], '- ')) . ']]></PRODUCT>' . "\n";
                $body .= '    <DESCRIPTION><![CDATA[' . Sanitize::invalidXmlChars($product['description']) . ']]></DESCRIPTION>' . "\n";
                $body .= '    <URL>' . $options['productViewUrlBase'] . '/' . $product['slug'] . '</URL>' . "\n";
                $body .= '    <IMGURL>' . $product['image'] . '</IMGURL>' . "\n";
                $body .= '    <PRICE_VAT>' . number_format($product['price_actual_taxed'], 2, ',', '') . '</PRICE_VAT>' . "\n";
                $body .= '    <ITEM_TYPE>new</ITEM_TYPE>' . "\n";
                $body .= '    <MANUFACTURER><![CDATA[' . Sanitize::invalidXmlChars($product['manufacturer']) . ']]></MANUFACTURER>' . "\n";
                if (!empty($product['category_zbozicz_name'])) {
                    $body .= '    <CATEGORYTEXT><![CDATA[' . Sanitize::invalidXmlChars($product['category_zbozicz_name']) . ']]></CATEGORYTEXT>' . "\n";
                }
                else {
                    $body .= '    <CATEGORYTEXT><![CDATA[' . Sanitize::invalidXmlChars(implode(' | ', @$product['category_path'])) . ']]></CATEGORYTEXT>' . "\n";
                }
                $body .= '    <EAN>' . (empty($product['ean']) ? '' : $product['ean']) . '</EAN>' . "\n";
                $body .= '    <DELIVERY_DATE>' . $product['shipment_time'] . '</DELIVERY_DATE>' . "\n";
                $body .= '  </SHOPITEM>' . "\n";
                return $body;

            case 'pricemania':
                if (!empty($product['stock'])) {
                    // "ihneď (okamžite k odoslaniu alebo vyzdvihnutiu)"
                    $availability = 0; 
                }
// there are no soldout products (aonly available products are retrieved from DB)                
//                elseif ($product['sold_out']) {
//                    // "nedostupný tovar"
//                    $availability = 100;
//                } 
                else {
                    $availability = (int) $product['shipment_time'];
                }
                // get shipment price
                $shipmentPrice = $options['minShipmentPrice'];
                if (
                    $options['freeShipmentLimit'] 
                    && $product['price_actual_taxed'] >= $options['freeShipmentLimit']
                ) {
                    $shipmentPrice = 0;
                }

                $body .= '  <product>' . "\n";
                $body .= '    <id>' . $product['id'] . '</id>' . "\n";
                $body .= '    <name><![CDATA[' . Sanitize::invalidXmlChars(rtrim($product['name'] . ' (' . $product['authors'] . ')', ' ()')) . ']]></name>' . "\n";
                $body .= '    <description><![CDATA[' . strip_tags(Sanitize::invalidXmlChars($product['description'])) . ']]></description>' . "\n";
                $body .= '    <price>' . $product['price_actual_taxed'] . '</price>' . "\n";
                $body .= '    <category><![CDATA[' . Sanitize::invalidXmlChars(implode(' > ', @$product['category_path'])) . ']]></category>' . "\n";
                $body .= '    <manufacturer><![CDATA[' . Sanitize::invalidXmlChars($product['manufacturer']) . ']]></manufacturer>' . "\n";
                $body .= '    <url>' . $options['productViewUrlBase'] . '/' . $product['slug'] . '</url>' . "\n";
                $body .= '    <picture>' . $product['image'] . '</picture>' . "\n";
                $body .= '    <shipping>' . $shipmentPrice . '</shipping>' . "\n";
                $body .= '    <availability>' . $availability . '</availability>' . "\n";
                $body .= '    <ean>' . (empty($product['ean']) ? '' : $product['ean']) . '</ean>' . "\n";
                $body .= '  </product>' . "\n";
                return $body;

            case 'tovarsk':
                $availability = 24 * (int) $product['shipment_time'];

                $body .= '  <SHOPITEM>' . "\n";
                $body .= '    <PRODUCT><![CDATA[' . Sanitize::invalidXmlChars(rtrim($product['name'] . ' (' . $product['authors'] . ')', ' ()')) . ']]></PRODUCT>' . "\n";
                $body .= '    <DESCRIPTION><![CDATA[' . strip_tags(Sanitize::invalidXmlChars($product['description'])) . ']]></DESCRIPTION>' . "\n";
                $body .= '    <URL>' . $options['productViewUrlBase'] . '/' . $product['slug'] . '</URL>' . "\n";
                $body .= '    <IMGURL>' . $product['image'] . '</IMGURL>' . "\n";
                $body .= '    <PRICE_VAT>' . $product['price_actual_taxed'] . '</PRICE_VAT>' . "\n";
                $body .= '    <CATEGORYTEXT><![CDATA[' . Sanitize::invalidXmlChars(implode('/', @$product['category_path'])) . ']]></CATEGORYTEXT>' . "\n";
                $body .= '    <MANUFACTURER><![CDATA[' . Sanitize::invalidXmlChars($product['manufacturer']) . ']]></MANUFACTURER>' . "\n";
                $body .= '    <AVAILABILITY>' . $availability . '</AVAILABILITY>' . "\n";
                $body .= '  </SHOPITEM>' . "\n";
                return $body;
            
            case 'superdeal':
                // get shipment price
                $shipmentPrice = $options['minShipmentPrice'];
                if (
                    $options['freeShipmentLimit'] 
                    && $product['price_actual_taxed'] >= $options['freeShipmentLimit']
                ) {
                    $shipmentPrice = 0;
                }
                
                $body .= '    <item>' . "\n";
                $body .= '        <internal_id>' . $product['id'] . '</internal_id>' . "\n";
                $body .= '        <name><![CDATA[' . Sanitize::invalidXmlChars(rtrim($product['name'] . ' (' . $product['authors'] . ')', ' ()')) . ']]></name>' . "\n";
                $body .= '        <description><![CDATA[' . strip_tags(Sanitize::invalidXmlChars($product['description'])) . ']]></description>' . "\n";
                $body .= '        <manufacturer><![CDATA[' . Sanitize::invalidXmlChars($product['manufacturer']) . ']]></manufacturer>' . "\n";
                $body .= '        <price_eur>' . $product['price_actual_taxed'] . '</price_eur>' . "\n";
                $body .= '        <vat>' . rtrim(rtrim(number_format($product['tax_rate'], 2, '.', ''), '0'), '.') . '</vat>' . "\n";
                $body .= '        <product_url>' . $options['productViewUrlBase'] . '/' . $product['slug'] . '</product_url>' . "\n";
                $body .= '        <product_img>' . $product['image'] . '</product_img>' . "\n";
                $body .= '        <ean>' . (empty($product['ean']) ? '' : $product['ean']) . '</ean>' . "\n";
                $body .= '        <part_no></part_no>' . "\n";
                $body .= '        <category><![CDATA[' . Sanitize::invalidXmlChars(implode(' | ', @$product['category_path'])) . ']]></category>' . "\n";
                $body .= '        <available></available>' . "\n";
                $body .= '        <shipping>' . $shipmentPrice . '</shipping>' . "\n";
                $body .= '        <highlight>0</highlight>' . "\n";
                $body .= '    </item>' . "\n";
                return $body;
                        
            case 'najnakup':
            case 'enakup':
            case 'pesiazona':
            default:
                if (!empty($product['stock'])) {
                    $availability = 'skladom'; 
                }
                else {
                    $availability = 'dostupné';
                }
                // get shipment price
                $shipmentPrice = number_format($options['minShipmentPrice'], 2, ',', '');
                if (
                    $options['freeShipmentLimit'] 
                    && $product['price_actual_taxed'] >= $options['freeShipmentLimit']
                ) {
                    $shipmentPrice = 0;
                }
                
                $body .= '  <SHOPITEM>' . "\n";
                $body .= '    <CODE>' . $product['id'] . '</CODE>' . "\n"; // it would be better to put 'code' here but it is not populated for all products on uniknihy
                $body .= '    <NAME><![CDATA[' . Sanitize::invalidXmlChars(rtrim($product['name'] . ' (' . $product['authors'] . ')', ' ()')) . ']]></NAME>' . "\n";
                $body .= '    <DESCRIPTION><![CDATA[' . strip_tags(Sanitize::invalidXmlChars($product['description'])) . ']]></DESCRIPTION>' . "\n";
                $body .= '    <IMAGE_URL>' . $product['image'] . '</IMAGE_URL>' . "\n";
                $body .= '    <PRODUCT_URL>' . $options['productViewUrlBase'] . '/' . $product['slug'] . '</PRODUCT_URL>' . "\n";
                $body .= '    <PRICE>' . number_format($product['price_actual_taxed'], 2, ',', '') . '</PRICE>' . "\n";
                $body .= '    <SHIPPING>' . $shipmentPrice . '</SHIPPING>' . "\n";
                $body .= '    <MANUFACTURER><![CDATA[' . Sanitize::invalidXmlChars($product['manufacturer']) . ']]></MANUFACTURER>' . "\n";
                $body .= '    <CATEGORY><![CDATA[' . Sanitize::invalidXmlChars(implode(' > ', @$product['category_path'])) . ']]></CATEGORY>' . "\n";
                $body .= '    <AVAILABILITY>' .  $availability . '</AVAILABILITY>' . "\n";
                $body .= '    <EAN>' . (empty($product['ean']) ? '' : $product['ean']) . '</EAN>' . "\n";

                $body .= '  </SHOPITEM>' . "\n";
                return $body;
        }
        
    }    
    
        
    /**
     * Exports GEIS csv file for specified order(s)
     * 
// see http://gclient.geis.cz/napoveda.aspx?control=print#import_expedic (requires authentication)   
//  
//Číslo dokladu
//Příjemce - název
//Příjemce - stát
//Příjemce - město
//Příjemce - ulice
//Příjemce - PSČ
//Příjemce - kontaktní osoba
//Příjemce - kontaktní email
//Příjemce - kontaktní telefon
//Datum svozu (když nebude vyplněno, nastaví se aktuální den)
//Reference
//EXW (ano - 1, ne - 0)
//Dobírka (ano - 1, ne - 0)
//Hodnota dobírky (ciarka ako oddelovac desatinnych miest)
//Variabilní symbol (VS je číslo pod ktorým na bankový účet vydavateľa budeme posielať dobierku k danej zásielke)
//Hmotnost (kg, ciarka ako oddelovac desatinnych miest)
//Objem (m3, ciarka ako oddelovac desatinnych miest)
//Počet (počet kusov v zásielke)
//Popis zboží
//Typ obalu - jedna z hodnot uvedených v tabulce typů obalů - viz níže
//Typ zakázky (parcel - 1, cargo - 0)
//Poznámka pro příjemce (volitelné, nemusí být vyplněno)
//Poznámka pro řidiče (volitelné, nemusí být vyplněno)
//Připojištění (ano - 1, ne - 0)
//Hodnota připojištění (ciarka ako oddelovac desatinnych miest)
//Avízo doručené zásilky (ano - 1, ne - 0)
//Avízo doručené zásilky - telefonní číslo nebo E-mail
//Avízo poškozené zásilky (ano - 1, ne - 0)
//Avízo poškozené zásilky - telefonní číslo nebo E-mail
//Avízo problémové zásilky (ano - 1, ne - 0)
//Avízo problémové zásilky - telefonní číslo nebo E-mail
//B2C - soukromá adresa (ano - 1, ne - 0)
//Doručení do 12 hod (ano - 1, ne - 0)
//Garantované doručení (ano - 1, ne - 0)
//POD avízo (ano - 1, ne - 0)
//POD - E-mail
//SMS avízo (ano - 1, ne - 0)
//Telefonické avízo (ano - 1, ne - 0)
//Příjemce - číslo popisné (volitelné, nemusí být vyplněno)
//Příjemce - číslo orientační (volitelné, nemusí být vyplněno)
//
//CrossDock - jméno (pouze pro CD)
//CrossDock - Typ CD svozu (vlastní - 1, geis - 2, pouze pro CD)
//CrossDock - Číslo auta v případě vlastního dodání(pouze pro CD)
//CrossDock - Datum dodání do DC (pouze pro CD)
//
//Email příjemce (ano - 1, ne - 0)
     * 
     * @param integer|array $orderId Single order id or array of such ids
     * @param array $options
     * 
     * @return boolean Returns FALSE if no orders are found for provided id
     */
    public function exportGeisOrderCsv($orderId, $options = array()) {  
        $defaults = array(
            'shipmentDate' => date('d.m.Y'), // datum zvozu
            'exw' => 0, // ex works
            'cod' => 0, // Dobierka
            'codPrice' => null, // Hodnota dobierky
            'vs' => null, // Variabilny symbol - použite je číslo objednávky
            'weight' => 0.0, // Hmotnost
            'volume' => null, // Objem
            'type' => 0, // Typ zákazky: parcel/cargo
            'assurance' => 0, // Pripoistenie
            'assurancePrice' => null, // Hodnota připojištění
            'b2c' => 1, // B2C - soukromá adresa
        );
        $options = array_merge($defaults, $options);
        // normalize
        if (empty($options['weight'])) {
            $options['weight'] = 0.0;
        }
        
        $Country = App::loadModel('App', 'Country', true);
        $countries  = $Country->findList(array(
            'key' => 'Country.iso_code_2',
            'fields' => array('Country.name'),
        ));
        
        // use find - maybe more then one order will be exported in future
        $Order = $this->loadModel('EshopOrder', true);
        $orders = $Order->find(array(
            'fields' => array(
                '*',
                'EshopShipmentMethod.pid AS shipmentMethodPid',
                'PaymentMethod.pid AS paymentMethodPid',
            ),
            'conditions' => array(
                'EshopOrder.id' => $orderId,
            ),
            'joins' => array(
                array(
                    'model' => 'EshopShipmentMethod',
                    'type' => 'left',
                ),
                array(
                    'module' => 'Payment',
                    'model' => 'PaymentMethod',
                    'type' => 'left',
                ),
            ),
        ));
        if (empty($orders)) {
            return false;
        }
        
        $orderNumber = '';
        if (!is_array($orderId) || count($orderId) === 1) {
            $orderNumber = $orders[0]['number'] . '_';
        }
        $filename = 'geis_' . $orderNumber . date('YmdHis') . '.csv';
        // output headers so that the file is downloaded rather than displayed
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);

        $inCharset = 'UTF-8';
        $outCharset = 'CP1250'; 
        $delimiter = ';';
        $enclosure = '"';
        
        $oh = fopen('php://output', 'w');
        
        $fields = array(
            'Číslo dokladu',
            'Príjmeca názov',
            'Príjemca štát',
            'Príjemca mesto',
            'Príjemca ulica',
            'Príjemca PSČ',
            'Príjemca kontaktna osoba',
            'Príjemca kontaktny email',
            'Príjemca kontaktny telefon',
            'Datum zvozu',
            'Referencia',
            'EXW',
            'Dobierka',     //0,1
            'Hodnota dobierky',
            'Variabilny symbol',
            'Hmotnost',
            'Objem',
            'Pocet',        // number of packages (not products in package) - set explicitly to 1
            'Popis tovaru',
            'Typ obalu',
            'Typ zákazky',
            'Poznamka pre príjemcu',
            'Poznámka pre vodica',
            'Pripoistenie',
            'Hodnota pripoistenia',
            'Avizo dorucenej zasielky',
            'Avizo dorucenej zasielky',
            'Avízo poskodenej zasielky',
            'Avízo poskodenej zasielky',
            'Avízo problémovej zásielky',
            'Avízo problémovej zásielky',
            'B2C súkromná adresa',
            'Dorucenie do 12 hod',
            'Garantovane dorucenie',
            'POD avízo',
            'POD e-mail',
            'SMS avizo',
            'Telefonicke avizo',
        );
        foreach ($fields as &$field) {
            $field = iconv($inCharset, $outCharset, $field);
        }
        // output the column headings
        fputcsv($oh, $fields, $delimiter, $enclosure);
        
        // output orders
        foreach ($orders as $i => $order) {  
            // @deparecated - normalize old orders data (see the method phpDoc)
            $order = $Order->getDeliveryData($order, array(
                'shipmentMethodPid' => $order['shipmentMethodPid'],
            ));
            if (!empty($order['delivery_company_fullname'])) {
                $name = (string)iconv($inCharset, $outCharset, $order['delivery_company_fullname']);
            } 
            else {
                $name = (string)iconv($inCharset, $outCharset, $order['delivery_fullname']);
            }
            $contactPerson = (string)iconv($inCharset, $outCharset, Sanitize::value($order['delivery_fullname']));                
            $street = (string)iconv($inCharset, $outCharset, $order['delivery_street']);
            $city = (string)iconv($inCharset, $outCharset, $order['delivery_city']);
            $zip = (string)iconv($inCharset, $outCharset, $order['delivery_zip']);
            $country = (string)iconv($inCharset, $outCharset, $countries[$order['delivery_country']]);
            $phone = iconv($inCharset, $outCharset, $order['delivery_phone']);
            $email = iconv($inCharset, $outCharset, $order['delivery_email']);
                        
            // check if cod and set price if yes
            if ($order['paymentMethodPid'] === 'courier') {
                $options['cod'] = 1;
                $options['codPrice'] = $order['order_price_to_pay'];
            }
            
            fputcsv($oh, array(
                $i + 1,         //    'Číslo dokladu',
                $name,          //    'Príjmeca názov',
                $country,       //    'Príjemca štát',
                $city,          //    'Príjemca mesto',
                $street,        //    'Príjemca ulica',
                $zip,           //    'Príjemca PSČ',
                $contactPerson, //    'Príjemca kontaktna osoba',
                $email,         //    'Príjemca kontaktny email',
                $phone,         //    'Príjemca kontaktny telefon',
                $options['shipmentDate'], //    'Datum zvozu',
                '',             //    'Referencia', ??? 
                (int)$options['exw'], //    'EXW',
                (int)$options['cod'], //    'Dobierka',     //0,1
                str_replace('.', ',', $options['codPrice']), //    'Hodnota dobierky'
                $options['vs'], //str_pad($order['number'], 10, 0, STR_PAD_LEFT), // 'Variabilny symbol'
                str_replace('.', ',', $options['weight']), //    'Hmotnost',
                $options['volume'], //    'Objem',
                '1', //    'Pocet' - number of packages (not products number)
                '', //    'Popis tovaru',
                '', //    'Typ obalu',
                $options['type'], //    'Typ zákazky': parcel/cargo
                '', //    'Poznamka pre príjemcu',
                '', //    'Poznámka pre vodica',
                $options['assurance'], //    'Pripoistenie'
                str_replace('.', ',', $options['assurancePrice']), //    'Hodnota pripoistenia',
                '', //    'Avizo dorucenej zasielky',
                '', //    'Avizo dorucenej zasielky',
                '', //    'Avízo poskodenej zasielky',
                '', //    'Avízo poskodenej zasielky',
                '', //    'Avízo problémovej zásielky',
                '', //    'Avízo problémovej zásielky',
                $options['b2c'], //    'B2C súkromná adresa',
                '', //    'Dorucenie do 12 hod',
                '', //    'Garantovane dorucenie',
                '', //    'POD avízo',
                '', //    'POD e-mail',
                '', //    'SMS avizo',
                '', //    'Telefonicke avizo',
            ), $delimiter, $enclosure);
        }
        
        return true;
    }  
    
    /**
     * List of files generated by exports to MRP containing pairs {filePid} => {fileName}
     * 
     * NOTE: When outputing files by ::outputMrpFile() then the 'mrp_' prefix is removed
     * 
     * @var array 
     */
    protected  $mrpFiles = array(
        'products' => 'MRP_KARTY.dbf',
        'productManufacturers' => 'MRP_VYROBCOVIA.dbf',
        'productImages' => 'MRP_OBRAZKY.zip',
        'addresses' => 'MRP_ADRESY.dbf',
        'orders' => 'MRP_ZAKAZKY.dbf',
        'ordersXml' => 'MRP_ZAKAZKY.xml',
        'orderProducts' => 'MRP_ZAKTEXT.dbf',
        'notes' => 'MRP_POZNAMKY.dbf',
    );
    
    /**
     * Returns value of property EshopExport::$mrpFiles
     * 
     * @return array
     */
    public function getPropertyMrpFiles() {
        return $this->mrpFiles;
    }
    
    /**
     * Exports products into .dbf file for MRP. Creates a file mrp_karty.dbf under 
     * tmp folder.
     *
     * NOTE: to run this method pecl dbase must be installed like: sudo pecl install dbase
     * and in /etc/php5/apache2/php.ini must be placed direction: extension = dbase.so
     * 
     * NOTE: MRP of vydavatel.sk:
     *      Version: 5.40
     *      Licence key: YC070072
     *      User: Vydavateľstvo matice Slovenskej
     *      Support: <EMAIL>, <EMAIL>, 0905/ 821 211, see http://www.mrp.sk/index.php/podpora/hotline.html
     *      Programmer: Pavol Botoš, <EMAIL> (programatorská podpora); Peter Turzík, <EMAIL> (vizuál=frontend)
     * 
     * @param array $options
     *      - 'productIds' (array) Product ids to be exported. If no specified or NULL
     *          then all products which had not been exported yet are exported.
     *      - 'batchSize' (int) Number of records to get product details for at once.
     *          Defaults to 1000.
     *      - 'microtimeReserve' (int) Time reserve in miliseconds to stop the processing 
     *          if free microtime is under this value. Defaults to 5000.
     * 
     * @return boolean|array On succes a progress array containing items (int)'count',
     *      (int)'exportedCount', (int)'failedCount', (bool)'interrupted', 
     *      (array)'errors'. FALSE on failure (check model errors in such a case)
     */
    public function exportMrpProducts($options = array()) {
        $defaults = array(
            'productIds' => null,
            'batchSize' => 1000,
            'microtimeReserve' => 10000,
            'processAmount' => 0, // for debuging, if 0 then all records are processed
            'simulate' => false, //for debuging, if TRUE then all exported records are written into log file EshopExport_exportMrpProducts instead dbf file. 
        );
        $options = array_merge($defaults, $options);
        if ($options['processAmount'] > 0) {
            $options['processAmount']++;
        }
        $progress = array(
            'count' => 0,
            'exportedCount' => 0,
            'failedCount' => 0,
            'interrupted' => null,
            'errors' => array(),
        );        
        
        $Product = $this->loadModel('EshopProduct', true);
        try {
            $Product->reserveTable('EshopExport_exportMrpProducts');
        } 
        catch (Throwable $e) {
            $this->setError(__e(__FILE__, 'Products table is reserved by another process. Please try later'));
            return false;
        }
        if ($options['productIds'] === null) {        
            $limit = null;
            if ($options['processAmount'] > 0) {
                $limit = $options['processAmount'];
            }
            $productIds = $Product->findList(array(
                'fields' => array('id'),
                'conditions' => array(
                    'exported' => array(null, '0000-00-00 00:00:00'),
                ),
                'order' => 'id ASC',
                'limit' => $limit,
            ));
        }
        else {
            $productIds = (array)$options['productIds'];
        }
        if (empty($productIds)) {
            $Product->unreserveTables('EshopExport_exportMrpProducts');
            return $progress;
        }
        $progress['count'] = count($productIds);

        $filename = TMP . DS . $this->mrpFiles['products'];
        $tmpFilename = TMP . DS . 'temp_' . $this->mrpFiles['products'];
        if (is_writable($tmpFilename)) {
            @unlink($tmpFilename);
        }

        // create the dbf file
        if (empty($options['simulate'])) {
            $db = Builder::create()
                ->setFormatType(Format::DBASE3)
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('CISKAT')->setLength(8))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('AUTOR')->setLength(64))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('NAZOV')->setLength(64))
                ->addField(Field::create(Field::TYPE_NUMERIC)->setName('CENABEZDPH')->setLength(15)->setDecimalCount(4))
                ->addField(Field::create(Field::TYPE_NUMERIC)->setName('DPH')->setLength(2)->setDecimalCount(0))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('KOD')->setLength(13))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('CENA2')->setLength(15))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('TYPKARTY')->setLength(20))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('VYPREDANE')->setLength(1))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('SKUPINA')->setLength(10))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('MJ')->setLength(3))
                ->addField(Field::create(Field::TYPE_NUMERIC)->setName('HMMOTNOST')->setLength(19)->setDecimalCount(6))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('POPIS')->setLength(254))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('KATEGORIE')->setLength(254))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('VYROBCA')->setLength(64))
                ->build($tmpFilename);
        }

        // save the data
        $exportedProductIds = array();
        $productIdsBatches = array_chunk($productIds, $options['batchSize']);
        foreach ($productIdsBatches as $productIdsBatch) {
            $products = $Product->getDetails($productIdsBatch, array(
                'getManufacturer' => array('EshopManufacturer.name'),
                'getProductType' => array('EshopProductType.name'),
                'getAuthors' => array(
                    'fields' => array(
                        'EshopAuthor.name',
                    )
                ),
                'getCategories' => array(
                    'fields' => array(
                        'EshopProductCategory.name',
                    )
                ),
            ));
            foreach ($products as $product) {
                // check remaining time - finish the processing before php 
                // would be interrupted by execution time constrain
                if (
                    App::getFreeMicrotime() < $options['microtimeReserve']
                    || $options['processAmount'] && --$options['processAmount'] == 0 //debug
                ) {
                    $progress['interrupted'] = true;
                    break 2;
                }
                
                $description = Sanitize::htmlToText($product['description'], false);
                $categories = '';
                foreach ($product['EshopProductCategory'] as $category) {
                    $categories .= '; ' . $category['name'];
                }
                $categories = trim($categories, '; ');
                try {
                    $record = array(
                        'CISKAT' => (int)$product['code'], 
                        // all mb_substr() here below is made on original string
                        // (instead of converted which would be better) as mb_substr()
                        // doesn't know 'CP1250' encoding
                        'AUTOR' => Str::encode(mb_substr($product['authors'], 0, 64, 'UTF-8'), array(
                            'inputEncoding' => 'UTF-8',
                            'outputEncoding' => 'CP1250',
                        )),
                        'NAZOV' => Str::encode(mb_substr($product['name'], 0, 64, 'UTF-8'), array(
                            'inputEncoding' => 'UTF-8',
                            'outputEncoding' => 'CP1250',
                        )),
                        'CENABEZDPH' => round((float) $product['price_taxless'], 4),
                        'DPH' => round((float) $product['tax_rate'], 2),
                        'KOD' => $product['ean'],
                        'CENA2' => round((float) $product['price_taxless'], 4),
                        'TYPKARTY' => '',
                        'VYPREDANE' => '',
                        'SKUPINA' => '',
                        'MJ' => Str::encode(__(__FILE__, $product['units']), array(
                            'inputEncoding' => 'UTF-8',
                            'outputEncoding' => 'CP1250',
                        )),
                        'HMMOTNOST' => number_format(round($Product->convertWeight($product['weight'], 'kg'), 3), 3, '.', ''),
                        'POPIS' => Str::encode(trim(mb_substr($description, 0, 254, 'UTF-8')), array(
                            'inputEncoding' => 'UTF-8',
                            'outputEncoding' => 'CP1250',
                        )),
                        'KATEGORIE' => Str::encode(trim(mb_substr($categories, 0, 254, 'UTF-8'), ', '), array(
                            'inputEncoding' => 'UTF-8',
                            'outputEncoding' => 'CP1250',
                        )),
                        'VYROBCA' => Str::encode(trim(mb_substr($product['EshopManufacturer']['name'], 0, 64, 'UTF-8'), ', '), array(
                            'inputEncoding' => 'UTF-8',
                            'outputEncoding' => 'CP1250',
                        )),
                    );
                    if ($options['simulate']) {
                        App::log('EshopExport_exportMrpProducts', 'record', array(
                            'var' => $record,
                        ));
                    }
                    else {
                        $db->insert($record);
                    }

                    // update count of exported products
                    $progress['exportedCount']++;
                    $exportedProductIds[] = $product['id'];
                } 
                catch (Throwable $e) {
                    $progress['failedCount']++;
                    $progress['errors'][] = $e->getMessage();
                    continue;
                }
            }
        }
        
        if (empty($options['simulate'])) {
            if(!$Product->update(
                array('exported' => 'NOW()'),
                array(
                    'validate' => false,
                    'conditions' => array('EshopProduct.id' => $exportedProductIds),
                    'literals' => array(
                        'data' => true,
                    )
                )
            )) {
                unlink($tmpFilename);
                $this->setError(__e(__FILE__, 'Failed to update exported products'));
                $Product->unreserveTables('EshopExport_exportMrpProducts');
                return false;
            }  
            rename($tmpFilename, $filename);
        }
        $Product->unreserveTables('EshopExport_exportMrpProducts');
        return $progress;
    }
    
    /**
     * Exports ALL AVAILABLE products manufacturer names into .dbf file for MRP. 
     * Creates a file mrp_vyrobcovia.dbf under tmp folder.
     * 
     * NOTE: This method is one-time-to-use to load all missing manufacturer names into MRP
     *
     * NOTE: to run this method pecl dbase must be installed like: sudo pecl install dbase
     * and in /etc/php5/apache2/php.ini must be placed direction: extension = dbase.so
     * 
     * NOTE: MRP of vydavatel.sk:
     *      Version: 5.40
     *      Licence key: YC070072
     *      User: Vydavateľstvo matice Slovenskej
     *      Support: <EMAIL>, <EMAIL>, 0905/ 821 211, see http://www.mrp.sk/index.php/podpora/hotline.html
     *      Programmer: Peter Turzík, <EMAIL>
     * 
     * @param array $options
     *      - 'productIds' (array) Product ids to be exported. If no specified or NULL
     *          then all products which had not been exported yet are exported.
     *      - 'batchSize' (int) Number of records to get product details for at once.
     *          Defaults to 1000.
     *      - 'microtimeReserve' (int) Time reserve in miliseconds to stop the processing 
     *          if free microtime is under this value. Defaults to 5000.
     * 
     * @return boolean|array On succes a progress array containing items (int)'count',
     *      (int)'exportedCount', (int)'failedCount', (bool)'interrupted', 
     *      (array)'errors'. FALSE on failure (check model errors in such a case)
     */
    public function exportMrpProductManufacturers($options = array()) {
        $defaults = array(
            'productIds' => null,
            'batchSize' => 1000,
            'microtimeReserve' => 10000,
            'processAmount' => 0, // for debuging, if 0 then all records are processed
            'simulate' => false, //for debuging, if TRUE then all exported records are written into log file EshopExport_exportMrpProductManufacturers instead dbf file. 
        );
        $options = array_merge($defaults, $options);
        if ($options['processAmount'] > 0) {
            $options['processAmount']++;
        }
        $progress = array(
            'count' => 0,
            'exportedCount' => 0,
            'failedCount' => 0,
            'interrupted' => null,
            'errors' => array(),
        );        
        
        $Product = $this->loadModel('EshopProduct', true);
        if ($options['productIds'] === null) {        
            $limit = null;
            if ($options['processAmount'] > 0) {
                $limit = $options['processAmount'];
            }
            $productIds = $Product->findList(array(
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopManufacturer',
                    )
                ),
                'key' => 'EshopProduct.id',
                'fields' => array('EshopProduct.id'),
                'conditions' => array(
                    EshopProduct::getPublishedConditions(),
                    'EshopProduct.unavailable' => false,
                    'EshopManufacturer.id !=' => null,
                ),
                'order' => 'EshopProduct.id ASC',
                'limit' => $limit,
            ));
        }
        else {
            $productIds = (array)$options['productIds'];
        }
        if (empty($productIds)) {
            return $progress;
        }
        $progress['count'] = count($productIds);

        $filename = TMP . DS . $this->mrpFiles['productManufacturers'];
        $tmpFilename = TMP . DS . 'temp_' . $this->mrpFiles['productManufacturers'];
        if (is_writable($tmpFilename)) {
            @unlink($tmpFilename);
        }

        // create the dbf file
        if (empty($options['simulate'])) {
            $db = Builder::create()
                ->setFormatType(Format::DBASE3)
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('CISKAT')->setLength(8))
                ->addField(Field::create(Field::TYPE_CHARACTER)->setName('VYROBCA')->setLength(64))
                ->build($tmpFilename);
        }

        // save the data
        $productIdsBatches = array_chunk($productIds, $options['batchSize']);
        foreach ($productIdsBatches as $productIdsBatch) {
            $products = $Product->find(array(
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopManufacturer',
                    )
                ),
                'fields' => array(
                    'EshopProduct.code',
                    'EshopManufacturer.name AS manufacturer',
                ),
                'conditions' => array(
                    'EshopProduct.id' => $productIdsBatch
                ),
            ));
            foreach ($products as $product) {
                // check remaining time - finish the processing before php 
                // would be interrupted by execution time constrain
                if (
                    App::getFreeMicrotime() < $options['microtimeReserve']
                    || $options['processAmount'] && --$options['processAmount'] == 0 //debug
                ) {
                    $this->progress['interrupted'] = true;
                    break 2;
                }
                try {
                    $record = array(
                        'CISKAT' => (int)$product['code'], 
                        'VYROBCA' => Str::encode(trim(mb_substr($product['manufacturer'], 0, 64, 'UTF-8'), ', '), array(
                            'inputEncoding' => 'UTF-8',
                            'outputEncoding' => 'CP1250',
                        )),
                    );
                    if ($options['simulate']) {
                        App::log('EshopExport_exportMrpProductManufacturers', 'record', array(
                            'var' => $record,
                        ));
                    }
                    else {
                        $db->insert($record);
                    }

                    // update count of exported products
                    $progress['exportedCount']++;
                } 
                catch (Throwable $e) {
                    $progress['failedCount']++;
                    $progress['errors'][] = $e->getMessage();
                    continue;
                }
            }
        }
        
        if (empty($options['simulate'])) {
            rename($tmpFilename, $filename);
        }
        return $progress;
    }
    
    /**
     * Exports product images for MRP. Creates a directory mrp_images under 
     * tmp folder.
     *
     * NOTE: MRP of vydavatel.sk:
     *      Version: 5.40
     *      Licence key: YC070072
     *      User: Vydavateľstvo matice Slovenskej
     *      Support: <EMAIL>, <EMAIL>, 0905/ 821 211, see http://www.mrp.sk/index.php/podpora/hotline.html
     *      Programmer: Peter Turzík, <EMAIL>
     * 
     * @param array $options
     *      - 'productIds' (array) Product ids to be exported. If no specified or NULL
     *          then all products which had not been exported yet are exported.
     *      - 'batchSize' (int) Number of records to get product details for at once.
     *          Defaults to 1000.
     *      - 'microtimeReserve' (int) Time reserve in miliseconds to stop the processing 
     *          if free microtime is under this value. Defaults to 5000.
     * 
     * @return boolean|array On succes a progress array containing items (int)'count',
     *      (int)'exportedCount', (int)'failedCount', (bool)'interrupted', 
     *      (array)'errors'. FALSE on failure (check model errors in such a case)
     */
    public function exportMrpProductImages($options = array()) {
        $defaults = array(
            'productIds' => null,
            'batchSize' => 1000,
            'microtimeReserve' => 10000,
            'processAmount' => 0, // for debuging, if 0 then all records are processed
            'simulate' => false, //for debuging, if TRUE then all exported records are written into log file EshopExport_exportMrpProductImages instead dbf file. 
        );
        $options = array_merge($defaults, $options);
        if ($options['processAmount'] > 0) {
            $options['processAmount']++;
        }
        $progress = array(
            'count' => 0,
            'exportedCount' => 0,
            'failedCount' => 0,
            'interrupted' => null,
            'errors' => array(),
        );        
        
        $Product = $this->loadModel('EshopProduct', true);
        try {
            $Product->reserveTable('EshopExport_exportMrpProductImages');
        } 
        catch (Throwable $e) {
            $this->setError(__e(__FILE__, 'Products table is reserved by another process. Please try later'));
            return false;
        }
        if ($options['productIds'] === null) {            
            $limit = null;
            if ($options['processAmount'] > 0) {
                $limit = $options['processAmount'];
            }
            $productIds = $Product->findList(array(
                'fields' => array('id'),
                'conditions' => array(
                    'unavailable' => false,
                    'image !=' => null,
                    'code !=' => array(null, ''),
                    'image_exported' => array(null, '0000-00-00 00:00:00'),
                ),
                'order' => 'id ASC',
                'limit' => $limit,
            ));
        }
        else {
            $productIds = (array)$options['productIds'];
        }
        if (empty($productIds)) {
            $Product->unreserveTables('EshopExport_exportMrpProductImages');
            return $progress;
        }
        $progress['count'] = count($productIds);

        $dirName = File::getPathinfo($this->mrpFiles['productImages'], PATHINFO_FILENAME);
        $targetDir = File::getRelativePath(TMP . DS . $dirName);
        $tmpTargetDir = File::getRelativePath(TMP . DS . 'temp_' . $dirName);
        File::removeTmpDir($tmpTargetDir);

        // save the data
        $exportedProductIds = array();
        $productIdsBatches = array_chunk($productIds, $options['batchSize']);
        foreach ($productIdsBatches as $productIdsBatch) {
            $products = $Product->find(array(
                'fields' => array(
                    'id',
                    'image',
                    'code',
                ),
                'conditions' => array(
                    'id' => $productIdsBatch,
                ),
                'order' => 'id ASC',
            ));            
            foreach ($products as $product) {
                // check remaining time - finish the processing before php 
                // would be interrupted by execution time constrain
                if (
                    App::getFreeMicrotime() < $options['microtimeReserve']
                    || $options['processAmount'] && --$options['processAmount'] == 0 //debug
                ) {
                    $this->progress['interrupted'] = true;
                    break 2;
                }
                
                try {
                    $product['image'] = $Product->getFileFieldPath('image', array(
                        'file' => $product['image'],
                        'variant' => 'small',
                    ));
                    File::copy(array($product['image']), $tmpTargetDir, array(
                        'name' => $product['code'] . '00M',
                        'unique' => false,
                    ));
                    // update count of exported products
                    $progress['exportedCount']++;
                    $exportedProductIds[] = $product['id'];
                } 
                catch (Throwable $e) {
                    $progress['failedCount']++;
                    $progress['errors'][] = $e->getMessage();
                    continue;
                }
            }
        }
        
        if (empty($options['simulate'])) {

            if(!$Product->update(
                array('image_exported' => 'NOW()'),
                array(
                    'validate' => false,
                    'conditions' => array('EshopProduct.id' => $exportedProductIds),
                    'literals' => array(
                        'data' => true,
                    )
                )
            )) {
                File::removeTmpDir($tmpTargetDir);
                $this->setError(__e(__FILE__, 'Failed to update products of exported images'));
                $Product->unreserveTables('EshopExport_exportMrpProductImages');
                return false;
            }  
        }
        File::removeTmpDir($targetDir);
        rename(ROOT . DS . $tmpTargetDir, ROOT . DS . $targetDir);
        // remove previously outputed zip file once new images are exported
        @unlink(TMP . DS . $this->mrpFiles['productImages']); 
        $Product->unreserveTables('EshopExport_exportMrpProductImages');
        return $progress;
    }
    
    /**
     * Exports orders into .dbf files for MRP. Creates a files mrp_adresy.dbf, mrp_zakazky.dbf
     * mrp_zaktext.dbf, mrp_poznamky.dbf under tmp folder.
     *
     * NOTE: to run this method pecl dbase must be installed like: sudo pecl install dbase
     * and in /etc/php5/apache2/php.ini must be placed direction: extension = dbase.so
     * 
     * NOTE: MRP of vydavatel.sk:
     *      Version: 5.40
     *      Licence key: YC070072
     *      User: Vydavateľstvo matice Slovenskej
     *      Support: <EMAIL>, <EMAIL>, 0905/ 821 211, see http://www.mrp.sk/index.php/podpora/hotline.html
     *      Programmer: Peter Turzík, <EMAIL>
     * 
     * @param array $options
     *      - 'orderIds' (int|array) Orders ids to be exported. If no specified or NULL
     *          then all orders which had not been exported yet are exported.
     *      - 'microtimeReserve' (int) Time reserve in miliseconds to stop the processing 
     *          if free microtime is under this value. Defaults to 5000.
     * 
     * @return boolean|array On succes a progress array containing items (int)'count',
     *      (int)'exportedCount', (int)'failedCount', (bool)'interrupted', 
     *      (array)'errors'. FALSE on failure (check model errors in such a case)
     */
    public function exportMrpOrders($options = array()) {
        $defaults = array(
            'orderIds' => null,
            'microtimeReserve' => 10000,
            'processAmount' => 0, // for debuging, if 0 then all records are processed
            'simulate' => false, //for debuging, if TRUE then all exported records are written into log file EshopExport_exportMrpOrders instead dbf file. 
        );
        $options = array_merge($defaults, $options);
        if ($options['processAmount'] > 0) {
            $options['processAmount']++;
        }
        $progress = array(
            'count' => 0,
            'exportedCount' => 0,
            'failedCount' => 0,
            'interrupted' => null,
            'errors' => array(),
        );        
        
        $Order = $this->loadModel('EshopOrder', true);
        if ($options['orderIds'] === null) {        
            $conditions = array(
                'exported' => array(null, '0000-00-00 00:00:00'),
            );
        }
        else {
            $conditions = array(
                'id' => $options['orderIds'],
            );
        }
        $orders = $Order->find(array(
            'fields' => array('*'),
            'conditions' => $conditions,
            'order' => 'id ASC',
        ));
        if (empty($orders)) {
            return $progress;
        }
        $progress['count'] = count($orders);
        
        // get orders products
        $orderIds = array_reduce($orders, function($carry, $item) {
            $carry[] = $item['id'];
            return $carry;
        }, array());
        $this->loadModel('EshopOrderProduct');
        $ordersProducts = $Order->getProductsDetails($orderIds);
        // rearange products to groups by order id
        $ordersProducts = array_reduce($ordersProducts, function($carry, $item) {
            $carry[$item['run_eshop_orders_id']][] = $item;
            return $carry;
        }, array());
        
        $addressFilename = TMP . DS . $this->mrpFiles['addresses'];
        $tempAddressFilename = TMP . DS . 'temp_' . $this->mrpFiles['addresses'];
        $orderFilename = TMP . DS . $this->mrpFiles['orders'];
        $tempOrderFilename = TMP . DS . 'temp_' . $this->mrpFiles['orders'];
        $productFilename = TMP . DS . $this->mrpFiles['orderProducts'];
        $tempProductFilename = TMP . DS . 'temp_' . $this->mrpFiles['orderProducts'];
        $noteFilename = TMP . DS . $this->mrpFiles['notes'];
        $tempNoteFilename = TMP . DS . 'temp_' . $this->mrpFiles['notes'];

        // create the dbf files
        if (empty($options['simulate'])) {
            $addressDb = dbase_create($tempAddressFilename, array(
                array('ID', 'N', 10, 0),
                array('JMENO', 'C', 20),
                array('PRIJMENI', 'C', 30),
                array('FIRMA', 'C', 30),
                array('ICO', 'C', 8),
                array('DIC', 'C', 15),
                array('IC_DPH', 'C', 20),
                array('ULICE', 'C', 30),
                array('MESTO', 'C', 30),
                array('PSC', 'C', 6),
                array('TEL', 'C', 20),
                array('EMAIL', 'C', 30),
                array('WWW', 'C', 30),
                array('LOGJMENO', 'C', 15),
                array('HESLO', 'C', 15),
                array('PRENOS', 'C', 1),
            ));
            if (!$addressDb) {
                $this->setError(__e(__FILE__, 'Failed to create/open DBF file \'%s\'', $tempAddressFilename));
                return false;
            }
            $orderDb = dbase_create($tempOrderFilename, array(
                array('CISOBJ', 'N', 10, 0),
                array('DATOBJ', 'C', 10),
                array('IDADRESY', 'N', 10, 0),
                array('CENA', 'N', 13, 2),
                array('CENASDPH', 'N', 13, 2),
                array('MENA', 'C', 3),
            ));
            if (!$orderDb) {
                dbase_close($addressDb);
                unlink($tempAddressFilename);
                $this->setError(__e(__FILE__, 'Failed to create/open DBF file \'%s\'', $tempOrderFilename));
                return false;
            }
            $productDb = dbase_create($tempProductFilename, array(
                array('KCISOBJ', 'N', 10, 0),
                array('KARTA', 'N', 10, 2),
                array('CENPOL', 'N', 10, 2),
                array('CENASDPH', 'C', 10, 2),
                array('MNO', 'N', 9, 0),
                array('DAN', 'N', 2, 0),
            ));
            if (!$productDb) {
                dbase_close($addressDb);
                unlink($tempAddressFilename);
                dbase_close($orderDb);
                unlink($tempOrderFilename);
                $this->setError(__e(__FILE__, 'Failed to create/open DBF file \'%s\'', $tempProductFilename));
                return false;
            }
            $noteDb = dbase_create($tempNoteFilename, array(
                array('CISOBJ', 'N', 10, 0),
                array('RADEK', 'N', 2, 0),
                array('TEXT', 'C', 70),
            ));
            if (!$noteDb) {
                dbase_close($addressDb);
                unlink($tempAddressFilename);
                dbase_close($orderDb);
                unlink($tempOrderFilename);
                dbase_close($productDb);
                unlink($tempProductFilename);
                $this->setError(__e(__FILE__, 'Failed to create/open DBF file \'%s\'', $tempNoteFilename));
                return false;
            }
        }

        // save dbf data
        $exportedOrderIds = array();
        $this->loadLib('EshopMrpQuickOrderAddressUniqueId');
        // before used only for quick orders (that is why the name of class ...)
        $OrderAddressUniqueId = new EshopMrpQuickOrderAddressUniqueId();
        $OrderAddressUniqueId->reserveProcessing();
        foreach ($orders as $order) {
            // check remaining time - finish the processing before php 
            // would be interrupted by execution time constrain
            if (
                App::getFreeMicrotime() < $options['microtimeReserve']
                || $options['processAmount'] && --$options['processAmount'] == 0 //debug
            ) {
                $this->progress['interrupted'] = true;
                break;
            }

            try {
                // @deparecated - normalize old orders data (see the method phpDoc)
                $order = $Order->getDeliveryData($order);
                // address
                // delivering to company: keep person delivery fullname
                if (!empty($order['delivery_company_fullname'])) {
                    $lastname = explode(' ', $order['delivery_fullname']);
                    $firstname = array_shift($lastname);
                    $lastname = implode(' ', $lastname);
                    $companyFullname = $order['delivery_company_fullname'];
                }
                // delivering to a person: set person name as company name
                else {
                    $firstname = $lastname = '';
                    $companyFullname = $order['delivery_fullname'];
                }
                // Unique addressId is assigned to each address regardless
                // to fact if the order is quick order or not, because the same user
                // can use at least 2 addresses (invoice and delivery). The only 
                // exception is company address (with company id number) which cannot
                // be set duplicitly in MRP (company id number must be unique) so 
                // in this case user id is used as addressId
                $companyIdNumber = $order['company_id_number'];
                if (!empty($companyIdNumber)) {
                    $addressId = $order['run_users_id'];
                }
                else {
                    $addressId = $OrderAddressUniqueId->get();
                    $companyIdNumber = 'bez';
                }
                $record = array(
                    $addressId,
                    Str::encode(mb_substr($firstname, 0, 20, 'UTF-8'), array(
                        'inputEncoding' => 'UTF-8',
                        'outputEncoding' => 'CP1250',
                    )),
                    Str::encode(mb_substr($lastname, 0, 30, 'UTF-8'), array(
                        'inputEncoding' => 'UTF-8',
                        'outputEncoding' => 'CP1250',
                    )),
                    Str::encode(mb_substr($companyFullname, 0, 30, 'UTF-8'), array(
                        'inputEncoding' => 'UTF-8',
                        'outputEncoding' => 'CP1250',
                    )),
                    substr($companyIdNumber, 0, 8), 
                    substr($order['company_tax_number'], 0, 15), 
                    substr($order['company_vat_number'], 0, 15), 
                    Str::encode(mb_substr($order['delivery_street'], 0, 30, 'UTF-8'), array(
                        'inputEncoding' => 'UTF-8',
                        'outputEncoding' => 'CP1250',
                    )),
                    Str::encode(mb_substr($order['delivery_city'], 0, 30, 'UTF-8'), array(
                        'inputEncoding' => 'UTF-8',
                        'outputEncoding' => 'CP1250',
                    )),
                    substr($order['delivery_zip'], 0, 6), 
                    substr($order['delivery_phone'], 0, 20), 
                    Str::encode(mb_substr($order['delivery_email'], 0, 30, 'UTF-8'), array(
                        'inputEncoding' => 'UTF-8',
                        'outputEncoding' => 'CP1250',
                    )), 
                    '',
                    '',
                    '',
                    '',
                );
                if ($options['simulate']) {
                    App::log('EshopExport_exportMrpOrders', 'address record', array(
                        'var' => $record,
                    ));
                }
                elseif (!dbase_add_record($addressDb, $record)) {
                    $progress['failedCount']++;
                    $progress['errors'][] = __e(__FILE__, 'Export of address data for order %s has failed', $order['number']);
                    continue;
                }
                
                // order
                $record = array(
                    (int)$order['number'], 
                    substr(date('d.m.Y', strtotime($order['created'])), 0, 10), 
                    $addressId,
                    round($order['order_price_actual_taxless'], 2), 
                    round((float)$order['order_price_actual_taxless'] + (float)$order['order_tax_actual'], 2), 
                    'EUR'
                );
                if ($options['simulate']) {
                    App::log('EshopExport_exportMrpOrders', 'order record', array(
                        'var' => $record,
                    ));
                }
                elseif (!dbase_add_record($orderDb, $record)) {
                    $progress['failedCount']++;
                    $progress['errors'][] = __e(__FILE__, 'Export of order data for order %s has failed', $order['number']);
                    continue;
                }
                
                // products
                $orderProducts = $ordersProducts[$order['id']];
                foreach ($orderProducts as $product) {
                    $record = array(
                        (int)$order['number'],
                        (int)$product['code'], 
                        round($product['price_actual_taxless'], 2), 
                        round($product['price_actual_taxed'], 2 ), 
                        round($product['amount'], 2), 
                        round($product['tax_rate'], 2),
                    );
                    if ($options['simulate']) {
                        App::log('EshopExport_exportMrpOrders', 'product record', array(
                            'var' => $record,
                        ));
                    }
                    elseif (!dbase_add_record($productDb, $record)) {
                        $progress['failedCount']++;
                        $progress['errors'][] = __e(__FILE__, 'Export of product \'%s\' data for order %s has failed', $product['name'], $order['number']);
                        continue 2;
                    }
                }
                // put also shipment costs as the last item (if there is a nonzero shipment price)
                if ((float)$order['shipment_price_actual_taxless']) {
                    //HARDCODED for vydavatel
                    $shipmentIdToStockCard = array(
                        1 => 21,
                        2 => 22,
                        3 => 23,
                        5 => 20,
                        6 => 24,
                        7 => 21,
                        8 => 26,
                    );
                    $record = array(
                        (int)$order['number'],
                        (int)$shipmentIdToStockCard[$order['run_eshop_shipment_methods_id']], 
                        round($order['shipment_price_actual_taxless'], 2), 
                        round($order['shipment_price_actual_taxless'] + $order['shipment_tax_actual'], 2 ), 
                        round(1, 2),    // amount
                        round(20, 2),   // tax rate
                    );
                    if ($options['simulate']) {
                        App::log('EshopExport_exportMrpOrders', 'shipment record', array(
                            'var' => $record,
                        ));
                    }
                    elseif (!dbase_add_record($productDb, $record)) {
                        $progress['failedCount']++;
                        $progress['errors'][] = __e(__FILE__, 'Export of shipment data for order %s has failed', $order['number']);
                        continue;
                    }
                }
                
                // notes
                // 
                // max row length is 70, maximum of 10 rows per order
                $lineNumber = 0;
                // put the payment method as the first note  
                $lineContent = __a(__FILE__, 'Payment method: %s', $order['payment_method_name']);
                $record = array(
                    (int)$order['number'],
                    ++$lineNumber,
                    Str::encode(mb_substr($lineContent, 0, 70, 'UTF-8'), array(
                        'inputEncoding' => 'UTF-8',
                        'outputEncoding' => 'CP1250',
                    )),
                );
                if ($options['simulate']) {
                    App::log('EshopExport_exportMrpOrders', 'note record', array(
                        'var' => $record,
                    ));
                }
                elseif (!dbase_add_record($noteDb, $record)) {
                    $progress['failedCount']++;
                    $progress['errors'][] = __e(__FILE__, 'Export of note data for order %s has failed', $order['number']);
                    continue;
                }
                // add the user order coment
                while ($lineNumber < 10 && ($lineContent = mb_substr($order['comment'], ($lineNumber - 1) * 70, 70, 'UTF-8'))) {
                    $record = array(
                        (int)$order['number'],
                        ++$lineNumber,
                        Str::encode(mb_substr($lineContent, 0, 70, 'UTF-8'), array(
                            'inputEncoding' => 'UTF-8',
                            'outputEncoding' => 'CP1250',
                        )),
                    );
                    if ($options['simulate']) {
                        App::log('EshopExport_exportMrpOrders', 'note record', array(
                            'var' => $record,
                        ));
                    }
                    elseif (!dbase_add_record($noteDb, $record)) {
                        $progress['failedCount']++;
                        $progress['errors'][] = __e(__FILE__, 'Export of note data for order %s has failed', $order['number']);
                        continue;
                    }
                }
                
                // update count of exported products
                $progress['exportedCount']++;
                $exportedOrderIds[] = $order['id'];
            } 
            catch (Throwable $e) {
                $progress['failedCount']++;
                $progress['errors'][] = $e->getMessage();
                $time = date('H:i');
                App::logError(__e(__FILE__, 'Failure of order %s export to MRP', $order['number']), array(
                    'var' => $e,
                    // export is launched each 10 minutes (see Eshop/config/crons.txt)
                    // so save the <NAME_EMAIL> mailbox and let us know if 
                    // it works only in morning when everybody is already in work
                    'email' => ($time >= '10:00' && $time <= '10:20'), //true,
                ));
                continue;
            }
        }
        $OrderAddressUniqueId->unreserveProcessing();
        
        if (empty($options['simulate'])) {
            dbase_close($addressDb);
            dbase_close($orderDb);
            dbase_close($productDb);
            dbase_close($noteDb);
            if(!$Order->update(
                array('exported' => 'NOW()'),
                array(
                    'validate' => false,
                    'conditions' => array('EshopOrder.id' => $exportedOrderIds),
                    'literals' => array(
                        'data' => true,
                    )
                )
            )) {
                unlink($tempAddressFilename);
                unlink($tempOrderFilename);
                unlink($tempProductFilename);
                unlink($tempNoteFilename);
                $this->setError(__e(__FILE__, 'Failed to update exported orders'));
                return false;
            }  
            rename($tempAddressFilename, $addressFilename);
            rename($tempOrderFilename, $orderFilename);
            rename($tempProductFilename, $productFilename);
            rename($tempNoteFilename, $noteFilename);
        }
        return $progress;
    }
    
    /**
     * Exports orders into .xml file for MRP. Creates a file mrp_zakazky.xml under tmp folder
     * or send a request using autonomous mode of MRP.
     * 
     * Licenčné údaje pre vydavatel.sk a kontakty do MRP viď v triede libs/MrpRequest.
     * Dokumentácia pre import objednávok:
     *      - http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#IMPEO0
     *      - http://www.mrp.sk/index.php/autonomnyr.html#IMPEO0
     * 
     * @param array $options
     *      - 'orderId' (int) Order id to be exported BUT only if the specified one has not been 
     *          exported yet. If empty then all orders which had not been exported yet are exported. 
     *          Defaults to NULL.
     *      - 'orderNumber' (int) Order number to be exported BUT only if the specified one has not been 
     *          exported yet. If empty then all orders which had not been exported yet are exported. 
     *          Defaults to NULL.
     *      - 'microtimeReserve' (int) Time reserve in miliseconds to stop the processing 
     *          if free microtime is under this value. Defaults to 10000.
     *      - 'sendToMrp' (bool) If TRUE then order is send to MRP using autonomous mode.
     *          In this case also order products are included (that which have been not
     *          exported yet to MRP). If FALSE then a XML file mrp_zakazky.xml is 
     *          created under tmp folder and products are not included.
     * 
     * @return boolean|array On success a progress array containing items (int)'count',
     *      (int)'exportedCount', (int)'failedCount', (bool)'interrupted', 
     *      (array)'errors'. FALSE on failure (check model errors in such a case)
     */
    public function exportMrpOrdersXml($options = array()) {
        $defaults = array(
            'orderId' => null,
            'orderNumber' => null,
            'microtimeReserve' => 10000,
            'sendToMrp' => false,
            'exportNewProducts' => false,
            'processAmount' => 0, // for debuging, if 0 then all records are processed
            'simulate' => false, //for debuging, if TRUE then only tmp file is created and exported records are not marked as exported
        );
        $options = array_merge($defaults, $options);
        if ($options['processAmount'] > 0) {
            $options['processAmount']++;
        }
        $progress = array(
            'count' => 0,
            'exportedCount' => 0,
            'interrupted' => null,
            'errors' => array(),
        );        
        
        $Order = $this->loadModel('EshopOrder', true);
        $conditions = array(
            'exported' => array(null, '0000-00-00 00:00:00'),
        );
        if ($options['orderNumber']) {
            $options['orderId'] = $Order->findFieldBy('id', 'number', $options['orderNumber']);
            if (!$options['orderId']) {
                $this->setError('orderNumber', __v(__FILE__, 'Objednávka č. %s neexistuje', $options['orderNumber']));
                $progress['errors'][] = __v(__FILE__, 'Objednávka č. %s neexistuje', $options['orderNumber']);
                return $progress;
            }
        }
        /*/>debug
        else {
            $this->setError('orderNumber', __v(__FILE__, 'Zadajte číslo objednávky'));
            $progress['errors'][] = __v(__FILE__, 'Zadajte číslo objednávky', $options['orderNumber']);
            return $progress;
        }
        //*/
        if ($options['orderId']) {        
            $conditions['id'] = $options['orderId'];
        }
        // if all unexported orders are treated then take only new orders
        else {
            $conditions['status'] = 'enum_new_order';
        }
        $limit = null;
        if ($options['processAmount'] > 0) {
            $limit = $options['processAmount'];
        }
        $orders = $Order->findList(array(
            'conditions' => $conditions,
            'order' => 'id ASC',
            'limit' => $limit,
        ));
        if (!$orders) {
            if ($options['orderNumber']) {
                $this->setError('orderNumber', __v(__FILE__, 'Objednávka č. %s už bola prenesená', $options['orderNumber']));
                $progress['errors'][] = __v(__FILE__, 'Objednávka č. %s už bola prenesená', $options['orderNumber']);
            }
            return $progress;
        }
        App::reserveProcessing('EshopExport::exportMrpOrdersXml()');
        $progress['count'] = count($orders);
        
        // get orders products
        $orderIds = array_keys($orders);
        $this->loadModel('EshopOrderProduct');
        $OrderProduct = new EshopOrderProduct();
        App::loadModel('Payment', 'PaymentMethod');
        $PaymentMethod = new PaymentMethod();
        $paymentMethodPids = $PaymentMethod->findList(array(
            'fields' => array('pid'),
        ));
        
        $paymentMethodPidToMrp = array(
            //'transfer' => array('id' => 1, 'name' => 'prev. prík'),
            'cash' => array('id' => 2, 'name' => 'hotovosť'),
            'courier' => array('id' => 3, 'name' => 'dobierka'),
            //'tatrapayDes' => array('id' => 4, 'name' => 'Tatrapay'),
            //'tatrapay' => array('id' => 4, 'name' => 'Tatrapay'),
            //'cardpayDes' => array('id' => 5, 'name' => 'Cardpay'),
            //'cardpay' => array('id' => 5, 'name' => 'Cardpay'),
            //'eplatby' => array('id' => 7, 'name' => 'VÚB Platba'),
            //'sporopay' => array('id' => 10, 'name' => 'Sporopay'),
        );
        /**
         * If name is provided then MRP shipment method name is returned.
         * If id is provided then MRP shipment method code (mrp_code of product) is returned.
         */
        $shipmentMethodToMrp = function($shipmentNameOrId) {
            if (!Validate::intNumber($shipmentNameOrId)) {
                $shipmentSlug = '-' . Str::slugize(trim($shipmentNameOrId)) . '-';
                if (strpos($shipmentSlug, '-geis-') !== false) {
                    if (strpos($shipmentSlug, '-point-') !== false) {
                        return 'GEIS POINT';
                    }
                    else {
                        return 'GEIS';
                    }
                }
                elseif (
                    strpos($shipmentSlug, '-zasielkovna-') !== false
                    || strpos($shipmentSlug, '-zasilkovna-') !== false
                ) {
                    return 'ZASIEL';
                }
                elseif (
                    strpos($shipmentSlug, '-slovenska-') !== false
                    && strpos($shipmentSlug, '-posta-') !== false
                ) {
                    return 'SLPOSTA';
                }
                elseif (
                    strpos($shipmentSlug, '-osobny-') !== false
                    && strpos($shipmentSlug, '-odber-') !== false
                ) {
                    return 'osobne';
                }
                elseif (
                    strpos($shipmentSlug, '-kurier-') !== false
                ) {
                    return 'DPD';
                }
                elseif (
                    strpos($shipmentSlug, '-dorucenie-') !== false
                    && strpos($shipmentSlug, '-zahranicia-') !== false
                ) {
                    return 'DPD';
                }
                else {
                    $shipmentSlug = str_replace('-kurierska-', '-', $shipmentSlug);
                    $shipmentSlug = str_replace('-sluzba-', '-', $shipmentSlug);
                    $shipmentSlug = str_replace('-platba-', '-', $shipmentSlug);
                    $shipmentSlug = str_replace('-vopred-', '-', $shipmentSlug);
                    $shipmentSlug = str_replace('-dobierka-', '-', $shipmentSlug);
                    $shipmentSlug = trim(str_replace('-', ' ', $shipmentSlug));
                    $shipmentSlug = preg_replace('/\s+/', ' ', $shipmentSlug);
                    $shipmentSlug = substr(strtoupper($shipmentSlug), 0, 10);
                    return $shipmentSlug;
                }
            }
            else {
                $shipmentMethodIdToMrpCode = array(
                    1 => 99998,    // Kuriér DPD - dobierka
                    2 => 99999,    // Osobný odber v predajni v Martine
    //                8 => 26,    // Zásielkovňa.sk - platba vopred
    //                5 => 20,    // Slovenská pošta - dobierka
    //                2 =>21,     // Slovenská pošta - platba vopred
    //                9 => 29,    // Doručenie do zahraničia (používa sa pre doručenie mimo SR a ČR)
    //                12 => 19,   // Zasilkovna.cz - dobierka (používa sa pre doručenie do ČR)
    //                13 => 27,   // Zasilkovna.cz - platba vopred (používa sa pre doručenie do ČR)
    //                14 => 24,   // Kuriérska služba GLS - dobierka
    //                15 => 23,   // Kuriérska služba GLS - platba vopred
                );
                return Sanitize::value($shipmentMethodIdToMrpCode[$shipmentNameOrId]);
            }
        };
        $ordersProducts = $Order->getProductsDetails($orderIds, array(
            'normalizeCode' => true,
        ));
        // rearange products to groups by order id
        $ordersProducts = array_reduce($ordersProducts, function($carry, $item) {
            $carry[$item['run_eshop_orders_id']][] = $item;
            return $carry;
        }, array());
        if ($options['exportNewProducts']) {
            // get new products (nonexportet yet) of exported orders 
            $newProductIds = $OrderProduct->findList(array(
                'key' => 'EshopProduct.id',
                'fields' => array('EshopProduct.id'),
                'conditions' => array(
                    // export all - you never know if "exported" are really exported
                    // MRP ignores all already existing products (matching by codes)
                    //'EshopProduct.exported' => array(null, '0000-00-00 00:00:00'),
                    // export only products without MRP code
                    'EshopProduct.mrp_code' => null,
                    'EshopOrderProduct.run_eshop_orders_id' => $orderIds
                ),
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopProduct',
                    )
                ),
                'order' => 'EshopProduct.id ASC',
            ));
            // products can be moved to MRP only in autonómny režim 
            if (!$options['sendToMrp']) {
                throw new Exception(__e(__FILE__, 'Nové produkty môžu byť prenesené do MRP len v autonómnom režime'));
            }
            $this->loadModel('EshopProduct');
            $Product = new EshopProduct();
            $newProducts = $Product->getDetails($newProductIds, array(
                'getManufacturer' => array('EshopManufacturer.name'),
                'getProductType' => array('EshopProductType.name'),
                /*/>
                'getAuthors' => array(
                    'fields' => array(
                        'EshopAuthor.name',
                    )
                ),
                //*/
                'normalizeCode' => true,
            ));
            // add MRP codes to new products
            foreach ($newProducts as &$newProduct) {
                if (!$newProduct['mrp_code']) {
                    $newProduct['mrp_code'] = $this->getNextMrpProductCode();
                }
            }
            unset($newProduct);
            // copy MRP codes of new products to their records in $ordersProducts
            foreach ($ordersProducts as &$orderProducts) {
                foreach ($orderProducts as &$orderProduct) {
                    $productId = $orderProduct['run_eshop_products_id'];
                    if (!empty($newProducts[$productId])) {
                        $orderProduct['mrp_code'] = $newProducts[$productId]['mrp_code'];
                    }
                }
                unset($orderProduct);
            }
            unset($orderProducts);
        }
        else {
            // if export of new products is not allowed then set them to empty
            // array() and so all furthen processing of new products is correctly
            // avoided (even processed).
            $newProducts = array();
            // remove orders having products without mrp_code
            foreach ($ordersProducts as $orderId => $orderProducts) {
                foreach ($orderProducts as $orderProduct) {
                    if (!$orderProduct['mrp_code']) {
                        $progress['errors'][] = __e(__FILE__, 'Objednávku %s nie je možné preniesť do MRP, pretože obsahuje produkty, ktoré nemajú zadaný MRP kód', $orders[$orderId]['number']);
                        unset($orders[$orderId]);
                        break;
                    }
                }
            }
            if (!$orders) {
                return $progress;
            }
        }
        
        // initialize variables here because of catch() block
        $filename = null;
        $tempFilename = null;
        $filehandle = null;
        $order = null;
        $requestId = date('YmdHis') . Str::getRandom(3, '0123456789');
        try {
            // XML file write
            if (!$options['sendToMrp']) {
                $filename = TMP . DS . $this->mrpFiles['ordersXml'];
                $tempFilename = TMP . DS . 'temp_' . $this->mrpFiles['ordersXml'];

                // create the XML tmp file
                $filehandle = fopen($tempFilename, 'w+');
                if (!$filehandle) {
                    throw new Exception(__e(__FILE__, 'Creation of XML file has failed'));
                } 

                // write header
                fwrite($filehandle, $this->loadView('EshopExport/mrpOrdersXmlHeader', array(
                    'requestId' => $requestId,
                )));
            }
            // send to MRP
            else {
                $this->loadLib('MrpRequest');                    
                if (ON_LOCALHOST) {
                    /*/>
                    // HARDCODED local network MRP server IP address to avoid
                    // accidental orders write onto production MRP server.
                    // To find the actual IP use ipconfig. 
                    $serverIpAddress = '************';
                    $serverPort = '120';        
                }
                else {
                    //*/
                    $serverIpAddress = $this->getSetting('mrp.serverIpAddress');
                    $serverPort = $this->getSetting('mrp.serverPort');
                }
                if (!$serverIpAddress || !$serverPort) {
                    throw new Exception(__e(__FILE__, 'Missing MRP server IP address or port number'));
                }
                $MrpRequest = new MrpRequest($serverIpAddress, $serverPort, array(
                    'privateKey' => $this->getSetting('mrp.privateKey'),
                ));
            }
            
            // save XML data
            $exportedOrderIds = array();
            $this->loadLib('EshopMrpQuickOrderAddressUniqueId');
            // before used only for quick orders (that is why the name of class ...)
            $OrderAddressUniqueId = new EshopMrpQuickOrderAddressUniqueId();
            $OrderAddressUniqueId->reserveProcessing();
            // XML file write
            if (!$options['sendToMrp']) {
                $newProductsXml = $this->loadView('EshopExport/mrpOrdersXmlProducts', array(
                    'products' => $newProducts,
                ));
                fwrite($filehandle, $newProductsXml);
            }
            foreach ($orders as $order) {
                // check remaining time - finish the processing before php 
                // would be interrupted by execution time constrain
                if (
                    App::getFreeMicrotime() < $options['microtimeReserve']
                    || $options['processAmount'] && --$options['processAmount'] == 0 //debug
                ) {
                    $progress['interrupted'] = true;
                    break;
                }
                
                $orderXml = '';
                
                // send to MRP - add new products separately for each order
                if ($options['sendToMrp']) {
                    $orderProducts = $ordersProducts[$order['id']];
                    $orderNewProducts = array();
                    foreach ($orderProducts as $orderProduct) {
                        $productId = $orderProduct['run_eshop_products_id'];
                        if (!empty($newProducts[$productId])) {
                            $orderNewProducts[$productId] = $newProducts[$productId];
                        }
                    }
                    $newProductsXml = $this->loadView('EshopExport/mrpOrdersXmlProducts', array(
                        'products' => $orderNewProducts,
                    ));
                    $orderXml .= $newProductsXml;
                }

                // @deparecated - normalize old orders data (see the method phpDoc)
                $order = $Order->getDeliveryData($order);
                // prepare addresses data
                // 
                // Unique addressId is assigned to each address regardless
                // to fact if the order is quick order or not, because the same user
                // can use at least 2 addresses (invoice and delivery). An exception
                // is company address (with company id number) which cannot
                // be set duplicitly in MRP (company id number must be unique) so 
                // in this case user id (if provided) is used as addressId.
                // Another exception is the delivery address, which is the same as 
                // invoicing address.
                $nameParts = explode(' ', $order['fullname']);
                $order['firstname'] = array_shift($nameParts);
                $order['lastname'] = implode(' ', $nameParts);
                $order['address_id'] = '';
                if (empty($order['company_fullname'])) {
                    $order['company_fullname'] = $order['fullname'];
                }
////let the IS generate address ids                
//                if (
//                    empty($order['company_id_number'])
//                ) {
//                    $order['address_id'] = $OrderAddressUniqueId->get();
//                }
                // in case of any pickup delivery (local pickup, Zasielkovna, GEIS point, ...):
                if (!empty($order['pickup_place'])) {
                    // replace the pickup place name by invoice person name
                    $order['delivery_fullname'] = $order['fullname'];
                }
                $nameParts = explode(' ', $order['delivery_fullname']);
                $order['delivery_firstname'] = array_shift($nameParts);
                $order['delivery_lastname'] = implode(' ', $nameParts);
                $order['delivery_address_id'] = '';
                // in case of any pickup delivery (local pickup, Zasielkovna, GEIS point, ...):
                if (!empty($order['pickup_place'])) {
////on client request the pickup place name is replaced by invoice person name here above                   
//                    // - set the invoice person name as company for info reasons
//                    $order['delivery_company_fullname'] = $order['fullname'];
////on client request the pickup_place_id was moved to 
////adresa_dod > firma > dic (see app/modules/Eshop/views/EshopExport/mrpOrdersXmlRecord.php)
//                    // - set pickup place id as delivery street (because of following export from MRP to Zasielkovna)
//                    $order['delivery_street'] = $order['pickup_place_id'];
                }
                elseif (empty($order['delivery_company_fullname'])) {
                    $order['delivery_company_fullname'] = $order['delivery_fullname'];
                }
////let the IS generate address ids                
//                $order['delivery_address_id'] = $OrderAddressUniqueId->get();
                
                // normalize following fields
                $order['zip'] = str_replace(' ', '', $order['zip']);
                $order['company_id_number'] = str_replace(' ', '', $order['company_id_number']);
                $order['company_tax_number'] = str_replace(' ', '', $order['company_tax_number']);
                $order['delivery_zip'] = str_replace(' ', '', $order['delivery_zip']);
                $order['phone'] = $Order->normalizePhone($order['phone']);
                
                // payment method
                $pamentMethodPid = Sanitize::value($paymentMethodPids[$order['run_payment_methods_id']]);
                if (!empty($paymentMethodPidToMrp[$pamentMethodPid])) {
                    $order['mrp_payment_method'] = $paymentMethodPidToMrp[$pamentMethodPid]['name'];
                }
                else {
                    $order['mrp_payment_method'] = $order['payment_method_name'];
                }
                // shipment method
                $order['mrp_shipment_method'] = $shipmentMethodToMrp($order['shipment_method_name']);
                
                // get order products
                $orderProducts = $ordersProducts[$order['id']];
                // put shipment costs as the last item (if there is a nonzero shipment price)
                if ((float)$order['shipment_price_actual_taxless']) {
                    if (!($shipmentMethodMrpCode  = $shipmentMethodToMrp(
                        $order['run_eshop_shipment_methods_id']
                    ))) {
                        $progress['errors'][] = __e(
                            __FILE__, 
                            'Objednávku %s nie je možné preniesť do MRP, pretože obsahuje spôsob dopravy "%s" pre ktorý neexistuje karta v MRP. Je potrebné ju vytvoriť a konverziu zadať do $shipmentMethodToMrp()',
                            $order['shipment_method_name']
                        );
                        continue;
                    }
                    $orderProducts[] = array(
                        // if using "Automatické vytvorenie skladových dokladov" in MRP then text item are not
                        // considered, that is why shipment methods are converted to regular stock items
                        'mrp_code' => $shipmentMethodMrpCode,
                        //'name' => $order['shipment_method_name'],
                        'price_actual_taxless' => round(
                            $order['shipment_price_actual_taxless'] + $order['payment_price_actual_taxless'], 
                            2
                        ),
                        'price_actual_taxed' => round(
                            $order['shipment_price_actual_taxless'] + $order['shipment_tax_actual'] 
                                + $order['payment_price_actual_taxless'] + $order['payment_tax_actual'], 
                            2 
                        ),
                        'amount' => round(1, 2),    // amount
                        'tax_rate' => round(20, 2),   // tax rate
                    );
                }
                
                // write record
                $orderXml .= $this->loadView('EshopExport/mrpOrdersXmlRecord', array(
                    'order' => $order,
                    'orderProducts' => $orderProducts,
                ));
                // - XML file write
                if (!$options['sendToMrp']) {
                    fwrite($filehandle, $orderXml);
                }
                // - send to MRP
                elseif (!$options['simulate']) {
                    $MrpRequest->sendOrders($requestId, $orderXml);
                    // update exported products and orders to not export them twice
                    $exportedDatetime = Date::getMySqlDatetime();
                    if (!empty($orderNewProducts)) {
                        $mrpCodesBatch = array();
                        foreach ($orderNewProducts as $orderNewProduct) {
                            $mrpCodesBatch[] = array(
                                'id' => $orderNewProduct['id'],
                                // if product has a MRP code it means that 
                                // it has been esported to MRP already
                                'mrp_code' => $orderNewProduct['mrp_code'],
                                // this is just for info
                                'exported' => $exportedDatetime,
                            );
                        }
                        $Product->saveBatch(array(
                            'update' => array(
                                'EshopProduct' => $mrpCodesBatch
                            ),
                        ));
                        $this->sendProductsExportedToMrpEmail($orderNewProducts);
                    }
                    $Order->update(
                        // if order has exported datetime it means that 
                        // it has been esported to MRP already
                        array('exported' => $exportedDatetime),
                        array(
                            'normalize' => false,
                            'validate' => false,
                            'conditions' => array(
                                'EshopOrder.id' => $order['id']
                            ),
                        )
                    );
                }
                // - send to MRP simulate
                else {
                    if (empty($progress['ordersXml'])) {
                        $progress['ordersXml'] = array();
                    }
                    $progress['ordersXml'][$order['number']] = '<xmp>' . $orderXml . '</xmp>';
                    App::logDebug('Orders XML', array(
                        'var' => $orderXml,
                    ));
                }
                
                // update count of exported products
                $progress['exportedCount']++;
                $exportedOrderIds[] = $order['id'];
            } 
            $OrderAddressUniqueId->unreserveProcessing();
            if (!$options['sendToMrp']) {
                // write footer
                fwrite($filehandle, $this->loadView('EshopExport/mrpOrdersXmlFooter'));

                fclose($filehandle);
            }

            // XML file write
            if (
                !$options['sendToMrp']
                && !$options['simulate']
            ) {
                rename($tempFilename, $filename);
                // update exported products and orders to not export them twice
                $exportedDatetime = Date::getMySqlDatetime();
                if (!empty($newProducts)) {
                    $mrpCodesBatch = array();
                    foreach ($newProducts as $newProduct) {
                        $mrpCodesBatch[] = array(
                            'id' => $newProduct['id'],
                            // if product has a MRP code it means that 
                            // it has been esported to MRP already
                            'mrp_code' => $newProduct['mrp_code'],
                            // this is just for info
                            'exported' => $exportedDatetime,
                        );
                    }
                    $Product->saveBatch(array(
                        'update' => array(
                            'EshopProduct' => $mrpCodesBatch
                        ),
                    ));
                    $this->sendProductsExportedToMrpEmail($newProducts);
                }
                $Order->update(
                    // if order has exported datetime it means that 
                    // it has been esported to MRP already
                    array('exported' => $exportedDatetime),
                    array(
                        'normalize' => false,
                        'validate' => false,
                        'conditions' => array(
                            'EshopOrder.id' => $exportedOrderIds
                        ),
                    )
                );  
            }
        }
        catch (Throwable $e) {
            // XML file write
            if (!$options['sendToMrp']) {
                fclose($filehandle);
                if (file_exists($tempFilename)) {
                    unlink($tempFilename);
                }
                if (file_exists($filename)) {
                    unlink($filename);
                }
            }
            $progress['exportedCount'] = 0;
            $progress['errors'][] = __e(
                __FILE__, 'Objednávka %s: %s', $order['number'], $e->getMessage()
            );
        }
        App::unreserveProcessing('EshopExport::exportMrpOrdersXml()');
                
        return $progress;
    }
    
    /**
     * Outputs specified MRP export file
     * 
     * @param string $mrpFilePid MRP export file pid. See EshopExport::$mrpFiles
     *      for possible values.
     * 
     * @throws Exception on failure
     */
    public function outputMrpFile($mrpFilePid) {
        if (empty($this->mrpFiles[$mrpFilePid])) {
            throw new Exception(__e(__FILE__, 'Unknown file pid %s', $mrpFilePid));
        }
        $file = File::getRelativePath(TMP . DS . $this->mrpFiles[$mrpFilePid]);
        if (!is_readable(ROOT . DS . $file)) {
            if (
                strtolower(File::getPathinfo($file, PATHINFO_EXTENSION)) === 'zip'
                && is_dir(ROOT . DS . ($dir = substr($file, 0, -4)))
            ) {
                $file = File::zip($dir);
            }
            else {
                throw new Exception(__e(__FILE__, 'Unexisting file %s', $file));
            }
        }
        File::output($file, array(
            'filename' => preg_replace('/^MRP_/i', '', $this->mrpFiles[$mrpFilePid])
        ));
    }

    /**
     * Sends notification e-mail about new products export to MRP
     *
     * @param array $newProducts New product records
     *
     * @return boolean Success
     */
    public function sendProductsExportedToMrpEmail($newProducts) {
        $body = __a(
                __FILE__, 
                'Do MRP boli exportované nové produkty (číslo - kód 2 - názov)'
        ) . ':<br><br>';
        foreach ($newProducts as $product) {
            $body .= $product['mrp_code'] . ' - ' . $product['code'] . ' - ' . $product['name'] . '<br>';
        }
        try {
            $result = (bool)App::sendEmail(
                $body,
                App::getSetting('App', 'email.to'),
                array(
                    'subject' => __a(__FILE__, 'Nové produty v MRP'),
                    'from' => App::getSetting('App', 'email.from'),
                    'cc' => App::getConfig('App', '_appInit.logEmail'),
                )
            );
        }
        catch (Exception $e) {
            App::logError('EshopExport::sendNewItemsEmail()', array(
                'var' => $e,
            ));
            $result = false;
        }
        return $result;
    }

    /**
     * Returns next MRP product code
     * 
     * @return int
     */
    public function getNextMrpProductCode() {
        App::reserveProcessing('EshopExport::getNextMrpCode');
        $number = $this->getSetting('mrp.lastCode');
        $number++;
        $this->setSetting('mrp.lastCode', $number);
        App::unreserveProcessing('EshopExport::getNextMrpCode');
        return $number;
    }
}
