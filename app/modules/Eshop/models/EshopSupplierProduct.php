<?php

class EshopSupplierProduct extends EshopModel {
    protected $table = 'run_eshop_supplier_products';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_eshop_products_id' => array('type' => 'int'),
        // following pids has been removed on karavandoplnky.sk: 
        // 'PEMIC', 'INFORM', 'PARTNERTECHNIC', 'DVDIMPORT', 'ALBATROS', 'IKAR', 'SLOVART', 'KOSMAS', 
        'supplier_pid' => array('type' => 'enum', 'values' => array('FRANKANA', 'INTERNAL')),
        'supplier_product_id' => array('type' => 'varchar', 'length' => 50, 'default' => null, 'comment' => 'Supplier internal product id (if any)'),
        'price' => array('type' => 'decimal', 'length' => 8.2, 'default' => null, 'comment' => 'Maximum price is 999999.99. Final price for customer'),
        'discount_price' => array('type' => 'decimal', 'length' => 8.2, 'default' => null, 'comment' => 'Final discount price for customer'),
        'discount_rate' => array('type' => 'decimal', 'length' => 8.2, 'default' => null, 'comment' => 'Final discount percents for customer. If this is used then discount_price should be calculated by trigger'),
        'tax_rate' => array('type' => 'enum', 'values' => array(0, 10, 20), 'default' => null, 'comment' => 'VAT percents'),
        // see phpDoc of Eshop::getDiscountRate()
        'margin_rate' => array('type' => 'decimal', 'length' => 4.2, 'default' => null, 'comment' => 'Margin (sk: rabat) percents'),
        'stock' => array('type' => 'int', 'default' => null),
        'availability' => array('type' => 'enum', 'default' => null, 'values' => array('enum_presale', 'enum_available', 'enum_on_demand', 'enum_soldout')),
        'long_delivery_time' => array('type' => 'bool', 'default' => null, 'comment' => 'Is the product available but it can take a long time to deliver it?'),
        'reprint' => array('type' => 'bool', 'default' => null, 'comment' => 'Is this a reprint of book?'),
        'available_from' => array('type' => 'date', 'default' => null, 'comment' => 'Can be used in case of some availabilities, e.g. enum_presale'),
        'shipment_time_off_stock' => array('type' => 'tinyint', 'length' => 3, 'default' => null, 'comment' => 'Days of shipment in case that the product is not in stock. If NULL then setting Eshop.product.shipmentTimeOffStock is used'),
        'image_import_source' => array('type' => 'tinytext', 'default' => null,  'comment' => 'URL or name of file to import (download from supplier or copy) product image from'),
        'image_import_result' => array('type' => 'enum', 'values' => array('OK', 'NOK'), 'default' => null,  'comment' => 'Result of image import from source specified by image_import_source'),
        'description_import_source' => array('type' => 'tinytext', 'default' => null,  'comment' => 'URL to import product description from supplier'),
        'description_import_result' => array('type' => 'enum', 'values' => array('OK', 'NOK'), 'default' => null,  'comment' => 'Result of description import from source specified by description_import_source'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
    protected $tableIndexes = array(
        array(
            'type' => 'unique', 
            'fields' => array('run_eshop_products_id', 'supplier_pid'), 
            'name' => 'SupplierProduct'
        ),
    );
    
    /**
     * 
     * @param array $options
     *      - 'updatedOnly' (bool) If FALSE then all products are synchronized. 
     *          Defaults to TRUE, it means only products which supplier products 
     *          has been updated (from last synchronization) are synchronized.
     *      - 'productIds' (int|array) Single product id or an array of product 
     *          ids to be synchronized. Defaults to NULL, it means all products are synchronized.
     * 
     * @return array Array contaning progress info
     */
    public function synchronize($options = array()) {
        //App::logDebug('synchronize - start'); //debug
        $defaults = array(
            'batchSize' => 10000,
            'microtimeReserve' => 10000,
            'updatedOnly' => true,
            'productIds' => null,
            'simulate' => false,
        );
        $options = array_merge($defaults, $options);
        
        App::reserveProcessing('EshopSupplierProduct_synchronize', array(
            'tries' => 1
        ));
        
        // turn off sql logs to not get error ERR_RESPONSE_HEADERS_TOO_BIG
        App::setSqlLogging(false);
        
        // !!! keep this is synchro with the app/config/database.php > 'tableReservationTimeLimit' => ...
        set_time_limit(600);
        ini_set('memory_limit', '512M');
        
        $this->synchronizeDiscountRate($options);
        
        $conditions = array(
            'supplier_pid !=' => 'internal',
        );
        if ($options['updatedOnly']) {
            $conditions['modified >='] = $this->getSetting('lastSynchronizationDatetime');
        }
        if ($options['productIds']) {
            $conditions['run_eshop_products_id'] = $options['productIds'];
        }
        // actualize datetime here (before the synchronization is done) to be sure 
        // that all supplier products which will be updated during this synchronization 
        // (by some import process) will get synchronized by next call of this method
        $this->setSetting('lastSynchronizationDatetime', date('Y-m-d H:i:s'));
        
        $updatedSupplierProductIds = $this->findList(array(
            'key' => 'run_eshop_products_id',
            'fields' => array('run_eshop_products_id'),
            'conditions' => $conditions,
        ));
        $progress = array(
            'synchronizedProductsCount' => 0,
            'interrupted' => null,
            'supplierOnStockProductsCount' => 0,
            'supplierOffStockProductsCount' => 0,
        );
        while (($batchSupplierProductIds = array_splice($updatedSupplierProductIds, 0, $options['batchSize']))) {
            $suppliersProducts = $this->findList(array(
                'key' => 'run_eshop_products_id',
                'fields' => array(
                    'supplier_pid',
                    'price',
                    'discount_price',
                    'discount_rate',
                    'tax_rate',
                    'margin_rate',
                    'stock',
                    'availability',
                    'long_delivery_time',
                    'reprint',
                    'available_from',
                    'shipment_time_off_stock',
                ),
                'accumulate' => true,
                'conditions' => array(
                    'run_eshop_products_id' => $batchSupplierProductIds
                )
            ));
            $batch = array();
//            $supplierOnStockProducts = array(); //debug
            foreach ($suppliersProducts as $productId => $suppliersProduct) {
                // check remaining time - finish the processing before php 
                // would be interrupted by execution time constrain
                if (App::getFreeMicrotime() < $options['microtimeReserve']) {
                    $progress['interrupted'] = true;
                    break;
                }
                $availabilityCoefficients = array();
                $allSuppliersOffStock = true;
                foreach ($suppliersProduct as $index => $supplierProduct) {
                    $coefficient = $this->getAvailabilityCoefficient($supplierProduct);
                    if (!isset($availabilityCoefficients[$coefficient])) {
                        $availabilityCoefficients[$coefficient] = array();
                    }
                    $availabilityCoefficients[$coefficient][] = $index;
                    if ($supplierProduct['stock'] > 0) {
                        $allSuppliersOffStock = false;
//                        //debug>
//                        if (!isset($supplierOnStockProducts[$productId])) {
//                            $supplierOnStockProducts[$productId] = array();
//                        }
//                        $supplierOnStockProducts[$productId][$index] = array(
//                            'supplier_pid' => $supplierProduct['supplier_pid'],
//                            'stock' => $supplierProduct['stock'],
//                        );
//                        //debug<
                    }
                }
                krsort($availabilityCoefficients, SORT_NUMERIC);
//// replaced by the highest margin rate                
//                // get the product with lowest (but not empty) actual price
//                $bestAvailabilityProductIndex = null;
//                $bestActualPrice = null;
//                foreach ($availabilityCoefficients as $bestAvailabilityProductIndexes) {
//                    foreach ($bestAvailabilityProductIndexes as $index) {
//                        $supplierProduct = $suppliersProduct[$index];
//                        $actualPrice = $supplierProduct['discount_price'] ?? $supplierProduct['price'];
//                        if (
//                            !$bestActualPrice
//                            ||
//                            $actualPrice
//                            && $actualPrice < $bestActualPrice
//                        ) {
//                            $bestActualPrice = $actualPrice;
//                            $bestAvailabilityProductIndex = $index;
//                        }
//                    }
//                    if (isset($bestAvailabilityProductIndex)) {
//                        break;
//                    }
//                }
                // get the product with highest margin rate
                $bestAvailabilityProductIndex = null;
                $bestMarginRate = null;
                foreach ($availabilityCoefficients as $bestAvailabilityProductIndexes) {
                    foreach ($bestAvailabilityProductIndexes as $index) {
                        $supplierProduct = $suppliersProduct[$index];
                        $marginRate = $supplierProduct['margin_rate'];
                        if (
                            !$bestMarginRate
                            ||
                            $marginRate > $bestMarginRate
                        ) {
                            $bestMarginRate = $marginRate;
                            $bestAvailabilityProductIndex = $index;
                        }
                    }
                    if (isset($bestAvailabilityProductIndex)) {
                        break;
                    }
                }
                // if nothing found, then fall back to the first with the best availability
                if (!isset($bestAvailabilityProductIndex)) {                    
                    $bestAvailabilityProductIndexes = reset($availabilityCoefficients);
                    $bestAvailabilityProductIndex = reset($bestAvailabilityProductIndexes);
                }
                $supplierProduct = $suppliersProduct[$bestAvailabilityProductIndex];                
//                $supplierOnStockProducts['best_index_' . $bestAvailabilityProductIndex] = true; //debug
                if (!$allSuppliersOffStock) {
                    $progress['supplierOnStockProductsCount']++;
                }
                else {
                    $progress['supplierOffStockProductsCount']++;
                }
                $shipmentTimeOffStock = null;
                if ($allSuppliersOffStock) {
                    $shipmentTimeOffStock = 15;
                }
                elseif ($supplierProduct['shipment_time_off_stock'] > $shipmentTimeOffStock) {
                    $shipmentTimeOffStock = $supplierProduct['shipment_time_off_stock'];
                }
//                //debug>
//                if ($productId === 175714) { 
//                    App::log('EshopSupplierProduct_synchronize', '', array(
//                        'var' => array(
//                            'suppliersProduct' => $suppliersProduct,
//                            'availabilityCoefficients' => $availabilityCoefficients,
//                            'supplierProduct' => $supplierProduct,
//                        )
//                    ));
//                } 
//                //debug<
                $batch[] = array(
                    'id' => $productId,
                    'supplier_pid' => $supplierProduct['supplier_pid'],
                    'price' => $supplierProduct['price'],
                    'discount_rate' => $supplierProduct['discount_rate'],
                    'tax_rate' => $supplierProduct['tax_rate'],
                    'availability' => $this->getAvailability($supplierProduct),
                    'long_delivery_time' => $allSuppliersOffStock ? true : $supplierProduct['long_delivery_time'],
                    'reprint' => $supplierProduct['reprint'],
                    'available_from' => $supplierProduct['available_from'],
                    'shipment_time_off_stock' => $shipmentTimeOffStock,
                );
                $progress['synchronizedProductsCount']++;
            }
            if ($options['simulate']) {                
                App::log('EshopSupplierProduct_synchronize', 'batch', array(
                    'var' => $batch,
                ));
            }
            else {  
                $this->saveBatch(
                    array(
                        'update' => array(
                            'EshopProduct' => &$batch
                        )
                    ),
                    array(
                        'ignoreNewValueIf' => array(
                            'EshopProduct.price' => array(
                                'old.synchronize_price_with_suppliers' => false,
                                'OR',
                                'new.price' => array('', 0, null),
                            ),
                            'EshopProduct.discount_rate' => array(
                                'old.synchronize_price_with_suppliers' => false,
                                'OR',
                                'new.price' => array('', 0, null),
                            ),
                            'EshopProduct.tax_rate' => array('', 0, null),
                            'EshopProduct.availability' => array(null),
                            'EshopProduct.long_delivery_time' => array(null),
                            'EshopProduct.reprint' => array(null),
                            'EshopProduct.available_from' => array(null),
                        )
                    )
                );
//                //debug>
//                App::log('EshopSupplierProduct_synchronize', 'supplierOnStockProducts', array(
//                    'var' => $supplierOnStockProducts,
//                ));
//                //debug<
            }
            if (!empty($progress['interrupted'])) {
                break;
            }
        }
    
        App::unreserveProcessing('EshopSupplierProduct_synchronize');
        
        if (!empty($progress['interrupted'])) {
            App::log(
                'EshopSupplierProduct_synchronize', 
                __e(__FILE__, 'Synchronization has been interrupted'), 
                array(
                    'var' => $progress, 
                    'email' => true,
                )
            );
        }
        //App::logDebug('synchronize - end'); //debug
        return $progress;
    }
    
    /**
     * Returns final availability of supplier products from e-shop point of view
     * 
     * @param  array $product Supplier product record containing availabilty fields
     *      'stock', 'availability', 'available_from'
     */
    protected function getAvailability($product) {
        $availability = $product['availability'];
        if ($product['stock'] > 0) {
            $availability = 'enum_available';
        }
        return $availability;
    }
    
    /**
     * Counts a coefficient of availability (higher means better)
     * 
     * @param array $product Supplier product record containing availabilty fields
     *      'stock', 'availability', 'long_delivery_time', 'available_from'
     * @return int
     */
    protected function getAvailabilityCoefficient($product) {
        if (
            $product['stock'] > 0
            && $product['availability'] === 'enum_available'
        ) {
            $coefficient = 7;
        }
        elseif (
            $product['stock'] > 0
        ) {
            $coefficient = 6;
        }
        elseif (
            $product['availability'] === 'enum_available'
            && !$product['long_delivery_time']
        ) {
            $coefficient = 5;
        }
        elseif (
            $product['availability'] === 'enum_available'
        ) {
            $coefficient = 4;
        }
        elseif (
            $product['availability'] === 'enum_presale'
            && $product['available_from']
        ) {
            $coefficient = 3;
        }
        elseif (
            $product['availability'] === 'enum_presale'
        ) {
            $coefficient = 2;
        }
        elseif (
            $product['availability'] !== null
        ) {
            $coefficient = 1;
        }
        else {
            $coefficient = 0;
        }
        return $coefficient;
    }
    
    /**
     * Returns default discount rate for specified supplier.
     * Default discount rates are defined/provided by client.
     * 
     * @param string $supplierPid Case insensitive supplier pid
     * 
     * @return float|NULL If supplier has defined empty default discount rate 
     *              then NULL (or any empty value). In such a case discount rate 
     *              is not synchronized, see synchronizeDiscountRate()
     * @throws Exception if specified supplier has no default discount rate defined.
     */
    public function getDefaultDiscountRate($supplierPid) {
        $supplierPid = strtolower($supplierPid);
        $discountRate = null;
        if ($supplierPid === 'pemic') {
            $discountRate = 10;
        }
        elseif ($supplierPid === 'inform') {
            $discountRate = 10;
        }
        elseif ($supplierPid === 'partnertechnic') {
            $discountRate = 10;
        }
        elseif ($supplierPid === 'albatros') {
            $discountRate = 10;
        }
        elseif ($supplierPid === 'ikar') {
            $discountRate = 10;
        }
        elseif ($supplierPid === 'slovart') {
            $discountRate = 10;
        }
        elseif ($supplierPid === 'kosmas') {
            $discountRate = 10;
        }
        elseif ($supplierPid === 'frankana') {
            $discountRate = null;
        }
        elseif ($supplierPid === 'internal') {
            $discountRate = null;
        }
        else {
            throw new Exception(__e(__FILE__, 'Supplier %s has no default discount defined', $supplierPid));
        }
        return $discountRate;
    }
    
    /**
     * @param array $options Following are available:
     *      - 'supplierPid' (string) If provided then single markup rate is returned. 
     *          If not provided then array of markup rates is returned with pairs 
     *          {supplier} => {markupRate}. Default markup rate is stored under key 0.
     *          Defaults to NULL.
     *      - 'exceptionOnFailure' (bool) Usefull for validations. Defaults to FALSE.
     *      - 'minMarkupRateRawValue' (string) Usefull for validations. Defaults to setting
     *          EshopProduct.importedProductsMinMarkupRate.
     * 
     * @return float|array
     * @throws Exception if specified supplier has no markup rate defined (even no default).
     */
    public function getMinMarkupRate($options = array()) {
        $options = array_merge(array(
            'supplierPid' => null,
            'exceptionOnFailure' => false,
            'minMarkupRateRawValue' => $this->getSetting('EshopProduct.importedProductsMinMarkupRate'),
        ), $options);
        
        $minMarkupRate = Utility::parseConditionedValue($options['minMarkupRateRawValue'], array(
            'allowConditions' => '^' . implode('$|^', $this->schema['supplier_pid']['values']) . '$',
            'allowValues' => '^[1-9]?[0-9](\.[0-9])?$',
            'exceptionOnFailure' => $options['exceptionOnFailure'],
        ));
        $tmp = array();
        foreach($minMarkupRate as $k => $v) {
            $k = strtolower($k);
            $tmp[$k] = (float)$v;
        }
        $minMarkupRate = $tmp;
        // dvd import is not much used so do not let it fail if there is no default
        // markup rate defined
        if (!isset($minMarkupRate[0])) {
            $minMarkupRate['dvdimport'] = 0;
        }
        if (!empty($options['supplierPid'])) {
            $supplierPid = strtolower($options['supplierPid']);
            if (isset($minMarkupRate[$supplierPid])) {
                return $minMarkupRate[$supplierPid];
            }
            elseif (isset($minMarkupRate[0])) {
                return $minMarkupRate[0];
            }
            elseif (!$options['exceptionOnFailure']) {
                return 0;
            }
            throw new Exception(__e(__FILE__, 'Dodávateľ %s nemá zadanú maržu', $options['supplierPid']));
        }
        return $minMarkupRate;
    }
    
    /**
     * Updates EshopSupplierProduc.discount_rate according to actual EshopSupplierProduc.margin_rate, 
     * default discount rate (EshopSupplierProduc::getDefaultDiscountRate()) and 
     * minimal markup rate (setting 'EshopProduct.importedProductsMinMarkupRate').
     * Discount rate is set to default discount rate if min markup rate is kept.
     * Otherwise a lower discount rate is calculated using min markup rate.
     * It is the same logic as used in EshopProductImport::importAddition().
     * 
     * @param array $options
     *      - 'updatedOnly' (bool) If FALSE then all products are synchronized. 
     *          Defaults to TRUE, it means only products which supplier products 
     *          has been updated (from last synchronization) are synchronized.
     *      - 'productIds' (int|array) Single product id or an array of product 
     *          ids to be synchronized. Defaults to NULL, it means all products are synchronized.
     *      - 'supplierPid' (string) Case insensitive supplier pid. Defaults to NULL
     *          it means synchronized are discount rates of all suppliers having
     *          default discount rate.
     */
    public function synchronizeDiscountRate($options = array()) {
        $options = array_merge(array(
            'updatedOnly' => true,
            'productIds' => null,
            'supplierPid' => null,
        ), $options);
        if ($options['supplierPid']) {
            $defaultDiscountRate = $this->getDefaultDiscountRate($options['supplierPid']);
            if (empty($defaultDiscountRate)) {
                return;
            }
            $minMarkupRate = $this->getMinMarkupRate(array(
                'supplierPid' => $options['supplierPid'],
            ));
        }
        else {
            $options['supplierPid'] = array();
            $defaultDiscountRateStartSql = '';
            $defaultDiscountRateEndSql = '';
            $minMarkupRateStartSql = '';
            $minMarkupRateEndSql = '';
            foreach($this->schema['supplier_pid']['values'] as $supplierPid) {
                try {
                    $supplierDefaultDiscountRate = $this->getDefaultDiscountRate($supplierPid);
                    if (empty($supplierDefaultDiscountRate)) {
                        continue;
                    }
                    $minMarkupRate = $this->getMinMarkupRate(array(
                        'supplierPid' => $supplierPid,
                    ));
                } 
                catch (Exception $e) {
                    continue;
                }
                $defaultDiscountRateStartSql .= sprintf(
                    'IF (EshopSupplierProduct.supplier_pid = "%s", %s, ',
                    $supplierPid,
                    $supplierDefaultDiscountRate   
                );
                $defaultDiscountRateEndSql .= ')';
                $minMarkupRateStartSql .= sprintf(
                    'IF (EshopSupplierProduct.supplier_pid = "%s", %s, ',
                    $supplierPid,
                    $minMarkupRate   
                );
                $minMarkupRateEndSql .= ')';
                $options['supplierPid'][] = $supplierPid;
            }
            $defaultDiscountRate = $defaultDiscountRateStartSql . '0' . $defaultDiscountRateEndSql;
            $normalizedDefaultDiscountRate = $defaultDiscountRateStartSql . 'NULL' . $defaultDiscountRateEndSql;
            $minMarkupRate = $minMarkupRateStartSql . '0' . $minMarkupRateEndSql;
        }
        $defaultDiscountRate = sprintf(
            'IF (EshopManufacturer.import_discount_rate IS NOT NULL, EshopManufacturer.import_discount_rate, %s)',
            $defaultDiscountRate
        );
        $normalizedDefaultDiscountRate = sprintf(
            'IF (EshopManufacturer.import_discount_rate IS NOT NULL, EshopManufacturer.import_discount_rate, %s)',
            $normalizedDefaultDiscountRate
        );
        $discountRateSql = Str::fill(
            'IF (' .
                '(100 * (100 - :default_discount_rate:) / (100 - EshopSupplierProduct.margin_rate) - 100) < :min_markup_rate:, '. 
                'IF (' . 
                    '(100 * :min_markup_rate: - 100 * EshopSupplierProduct.margin_rate - :min_markup_rate: * EshopSupplierProduct.margin_rate) < 0, ' . 
                    '100 - (100 - EshopSupplierProduct.margin_rate) * (100 + :min_markup_rate:) / 100, ' . 
                    'NULL' . 
                '), ' . 
                ':normalized_default_discount_rate:' . 
            ')',
            array(
                'default_discount_rate' => $defaultDiscountRate,
                'normalized_default_discount_rate' => $normalizedDefaultDiscountRate,
                'min_markup_rate' => $minMarkupRate,
            )
        );
        $conditions = array(
            'EshopSupplierProduct.margin_rate >' => 0,
            'EshopSupplierProduct.supplier_pid' => $options['supplierPid'],
        );
        if ($options['updatedOnly']) {
            $conditions['EshopSupplierProduct.modified >='] = $this->getSetting('lastSynchronizationDatetime');
        }
        if ($options['productIds']) {
            $conditions['EshopSupplierProduct.run_eshop_products_id'] = $options['productIds'];
        }
        $this->update(
            array(
                'EshopSupplierProduct.discount_rate' => $discountRateSql,
                'EshopSupplierProduct.modified' => 'NOW()',
            ),
            array(
                'allowFields' => array(
                    'EshopSupplierProduct.discount_rate',
                    'EshopSupplierProduct.modified',
                ),
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopProduct',
                    ),
                    array(
                        'type' => 'left',
                        'model' => 'EshopManufacturer',
                        'toModel' => 'EshopProduct',
                    )
                ),
                'conditions' => $conditions,
                'literals' => array(
                    'data' => true,
                ),
                // modified field is updated explicitly here above (bacuase of joined models)
                'updateModificationDatetime' => false,
            )
        );
    }
    
    /**
     * Returns supplier products with lowest purchase price for specified product ids.
     * Only available supplier products are considered. The returned array contains
     * pairs {productId} => {supplierProductRecord}.
     * 
     * @param array $productIds
     * @param array $options Options of Model::find(). Option 'order' is set internally.
     * 
     * @return array
     */
    public function findWithLowestPurchasePrice($productIds, $options) {
        $options['fields'] = (array)Sanitize::value($options['fields']);
        $options['conditions'] = (array)Sanitize::value($options['conditions']);
        $options['literals'] = (array)Sanitize::value($options['literals']);
        
        $options['fields'][] = 'EshopSupplierProduct.run_eshop_products_id';
        $options['conditions'] = DB::nestConditions($options['conditions']);
        $options['conditions'][] = array(
            'EshopSupplierProduct.run_eshop_products_id' => $productIds,
            'EshopSupplierProduct.availability !=' => 'enum_soldout'
        );
        $options['order'] = $options['literals']['order'] = 
            '(EshopSupplierProduct.price - EshopSupplierProduct.price * EshopSupplierProduct.margin_rate / 100) ASC';
        $products = $this->find($options);
        $lowestPurchasePriceProducts = array();
        foreach ($products as $i => $product) {
            if (!empty($product['EshopSupplierProduct'])) {
                $productId = $product['EshopSupplierProduct']['run_eshop_products_id'];
            }
            else {
                $productId = $product['run_eshop_products_id'];
            }
            if (empty($lowestPurchasePriceProducts[$productId])) {
                $lowestPurchasePriceProducts[$productId] = $product;
            }
            unset($products[$i]);
        }
        return $lowestPurchasePriceProducts;
    }
}