<?php

class EshopRelatedProduct extends Model {
    protected $table = 'run_eshop_related_products';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_eshop_products_id' => array('type' => 'int', 'index' => 'index'),
        'run_eshop_related_products_id' => array('type' => 'int', 'index' => 'index'),
        'sort' => array('type' => 'int', 'default' => null, 'comment' => 'Weight in ordered group of related products'),
        //'description' => array('type' => 'text', 'default' => null, 'comment' => 'Description of relation between two products'),
    );
    
}
