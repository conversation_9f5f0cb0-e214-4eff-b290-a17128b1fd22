<?php

class EshopImportFromOdoo extends Model {
    
    protected $Product = null;
    protected $Category = null;
    protected $Brand = null;
    
    public function __construct() {
        parent::__construct();
        
        // load used models
        $this->loadModel('EshopProduct');
        $this->Product = new EshopProduct();
        $this->loadModel('EshopProductCategory');
        $this->Category = new EshopProductCategory();
        $this->loadModel('EshopBrand');
        $this->Brand = new EshopBrand();
    }

    /**
     * Saves base64 images to disk
     * 
     * @param string $csvFilePath path to Csv file
     * @param string $slug of product
     * @param string $type of product(small, medium, original, large)
     */
    function saveBase64ImageFromOdooCsv($base64_string, $slug, $type) {
        $path = "userfiles/Eshop/EshopProduct/image/{$type}/";
        if (!is_dir($path)) {
            mkdir($path, 0777, true);
        }
        $data = explode(',', $base64_string);
        $imageData = count($data) > 1 ? base64_decode($data[1]) : base64_decode($base64_string);
        $finfo = new finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($imageData);
        if ($mimeType == "image/svg+xml") {
            $extension = "svg";
        } 
        elseif ($mimeType == "image/jpeg") {
            $extension = "jpg";
        } 
        elseif ($mimeType == "image/png") {
            $extension = "png";
        } 
        else {
            $extension = "png";
        } 
        $file = "{$slug}.{$extension}";
        $output_file = $path . $file;
        file_put_contents($output_file, $imageData);
        return $file;
    }

    /**
     * Reads and processes the CSV file
     * 
     * @param string $csvFilePath path to Csv file
     * 
     * Run after adding categories to the database
     * After adding the products to the database, run _debug/deleteIdFromCategoryName 
     * to remove the ID from the category name, then run _debug/addSizeColorToName 
     * to add size and color to the product's name
     */
    function importProductsFromOdooCsv($csvFilePath) {
        $data = [];
        $variants = [];
        $categories = [];
        $product = null;
        if (($handle = fopen($csvFilePath, "r")) !== FALSE) {
            $headers = fgetcsv($handle, 1000, ",");
            while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $row = array_map(function ($value) { return $value !== null ? $value : ''; }, $row);
                if (count($headers) > count($row)) {
                    $row = array_pad($row, count($headers), '');
                } 
                elseif (count($headers) < count($row)) {
                    $row = array_slice($row, 0, count($headers));
                }
                $rowData = array_combine($headers, $row);
                if (!$rowData || empty(array_filter($rowData, function ($value) { return $value !== null && $value !== ''; }))) {
                    continue; 
                }
                if (!empty($rowData['ID'])) {
                    if (!empty($rowData['Meno'])) {
                        $slug = isset($rowData['Hodnoty atribútu/Hodnota'])
                            ? strtolower($rowData['Meno'] . ' ' . $rowData['Hodnoty atribútu/Hodnota'])
                            : strtolower($rowData['Meno']);
                        $slug = Sanitize::nonAscii($slug);
                        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
                        $slug = trim($slug, '-');
                    }
                    $original_image = !empty($rowData['Big-sized image']) 
                        ? $this->saveBase64ImageFromOdooCsv($rowData['Big-sized image'], $slug, 'original') : '';
                    $product = [
                        'id' => trim($rowData['ID']),
                        'name' => $rowData['Meno'],
                        'seo_title' => $rowData['Meno'],
                        'slug' => $slug,
                        'price' => isset($rowData['Predajná cena']) ? $rowData['Predajná cena'] : 0,
                        'stock' => isset($rowData['Množstvo skladom']) ? $rowData['Množstvo skladom'] : 0,
                        'run_eshop_brands_id' => isset($rowData['Brand/ID']) ? $rowData['Brand/ID'] : null,
                        'image' => $original_image,
                        'created' => isset($rowData['Vytvorené']) ? $rowData['Vytvorené'] : null,
                        'active' => isset($rowData['Aktívne']) && strtolower(trim($rowData['Aktívne'])) === 'true' ? 1 : 0,
                        'tax_rate' => 20,
                        'code' => isset($rowData['Interná Referencia']) ? $rowData['Interná Referencia'] : null,
                        'mrp_code' => isset($rowData['Mrp Number']) ? $rowData['Mrp Number'] : null,
                        'description' => isset($rowData['Popis na webstránku']) ? $rowData['Popis na webstránku'] : null,
                    ];
                    if (isset($rowData['Platné riadky atribútu produktu/Zobrazovaný Názov'])) {
                        if ($rowData['Platné riadky atribútu produktu/Zobrazovaný Názov'] == 'Farba') {
                            $product['color'] = isset($rowData['Hodnoty atribútu/Hodnota']) ? $rowData['Hodnoty atribútu/Hodnota'] : null;
                            $product['size'] = null;
                        }
                        elseif ($rowData['Platné riadky atribútu produktu/Zobrazovaný Názov'] == 'Veľkosť') {
                            $product['size'] = isset($rowData['Hodnoty atribútu/Hodnota']) ? $rowData['Hodnoty atribútu/Hodnota'] : null;
                            $product['color'] = null;
                        }
                        else {
                            $product['color'] = null;
                            $product['size'] = null;
                        }
                    }
                    $data[] = $product;
                }
                if (!empty($rowData['Platné existujúce varianty/ID']) && $product) {
                    $variantIDs = array_filter(array_map('trim', explode(" ", $rowData['Platné existujúce varianty/ID'])));
                    foreach ($variantIDs as $variantID) {
                        if (!empty($variantID) && $variantID !== $product['id']) {
                            $variants[] = [
                                'run_eshop_products_id' => $product['id'],
                                'run_eshop_variant_products_id' => $variantID
                            ];
                        }
                    }
                }
                if (!empty($rowData['Produktová kategória webstránky/Meno']) && !empty($rowData['Produktová kategória webstránky/ID'])) {
                    $category_name = $rowData['Produktová kategória webstránky/Meno'] . '_' . $rowData['Produktová kategória webstránky/ID'];
                    $category_id =$this->Category->findFirst([
                        'conditions' => [
                            'name' => $category_name,
                        ],
                        'fields' => ['id']
                    ]);
                    if ($category_id) {
                        $categories[] = [
                            'run_eshop_products_id' => $product['id'],
                            'run_eshop_product_categories_id' => $category_id['id']
                        ];
                    }
                }
            }
            fclose($handle);
            if (!empty($data)) {
                $this->saveBatch(['create' => ['EshopProduct' => $data]], ['validate' => false]);
            }
            if (!empty($variants)) {
                $this->saveBatch(['create' => ['EshopVariantProduct' => $variants]], ['validate' => false]);
            }
            if (!empty($categories)) {
                $this->saveBatch(['create' => ['EshopProductCategoryProduct' => $categories]], ['validate' => false]);
            }
            echo "<pre>";
                print_r(['products' => $data, 'variants' => $variants, 'categories' => $categories]);
            echo "</pre>"; 
        }
    }

    /**
     * Reads and processes the CSV file and imports images
     * 
     * @param string $csvFilePath path to Csv file
     */
    function importProductImagesFromOdooCsv($csvFilePath) {[];
        if (($handle = fopen($csvFilePath, "r")) !== FALSE) {
            $headers = fgetcsv($handle, 1000, ",");
            while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $row = array_map(function ($value) { return $value !== null ? $value : ''; }, $row);
                if (count($headers) > count($row)) {
                    $row = array_pad($row, count($headers), '');
                } elseif (count($headers) < count($row)) {
                    $row = array_slice($row, 0, count($headers));
                }
                $rowData = array_combine($headers, $row);
                if (!$rowData || empty(array_filter($rowData, function ($value) { return $value !== null && $value !== ''; }))) {
                    continue; 
                }
                if (!empty($rowData['ID'])) {
                    $product = $this->Product->findFirstBy('id',$rowData['ID']);
                    $product['image'] = !empty($rowData['Big-sized image']) 
                        ? $this->saveBase64ImageFromOdooCsv($rowData['Big-sized image'], $product['slug'], 'original') 
                        : '';
                    $this->Product->save($product);    
                }              
            }
            fclose($handle);  
            echo("obrazky importovane");        
        }
    }
    
    /**
     * Reads and processes the CSV file and imports categories
     *
     * Commands for exporting website product categories from Odoo via CMD:
     * 1. Enter PostgreSQL inside Docker container:
     *      docker exec -it unisport-db-1 psql -U developer -d odoo
     * 2. Export data to CSV file:
     *      COPY (
     *          SELECT 
     *              name, 
     *              parent_id, 
     *              sequence, 
     *              create_date, 
     *              write_date, 
     *              description
     *          FROM product_public_category
     *      ) 
     *      TO '/var/lib/postgresql/data/public_categories_export.csv' 
     *      WITH CSV HEADER;
     *
     * 3.Move the CSV file to your local storage (replace with your own path):
     *  docker cp unisport-db-1:/var/lib/postgresql/data/public_categories.csv "Path/to/folder/where/you/want/to/save/public_categories.csv"
     * 
     * @param string $csvFilePath path to Csv file
     */
    function importCategoriesFromOdooCsv($csvFilePath) {
        $categories = [];
        if (($handle = fopen($csvFilePath, "r")) !== FALSE) {
            $headers = fgetcsv($handle, 1000, ","); 
            while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $rowData = array_combine($headers, $row);
                if (!empty($rowData['id']) && $rowData['id'] != 1) { 
                    $slug = null;
                    if (!empty($rowData['name'])) {
                        $slug = strtolower(Sanitize::nonAscii($rowData['name']));
                        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
                        $slug = trim($slug, '-');
                    }
                    $parent_id = !empty($rowData['parent_id']) ? $rowData['parent_id'] : null;
                    $categories[$rowData['id']] = [
                        'id'               => $rowData['id'],
                        'name'             => isset($rowData['name'], $rowData['id']) ? $rowData['name'] . '_' . $rowData['id'] : null,
                        'alternative_name' => $rowData['name'] ?? null,
                        'slug'             => $slug ?? null,
                        'parent_id'        => $parent_id,
                        'created'          => $rowData['create_date'] ?? null,
                        'modified'         => $rowData['write_date'] ?? null
                    ];
                }
            }
            fclose($handle);
        }
        foreach ($categories as $id => &$category) {
            $categoryPath = [];
            while ($id !== null && isset($categories[$id])) {
                array_unshift($categoryPath, $categories[$id]['name']); 
                $id = $categories[$id]['parent_id'];
            }
            $category['category_id'] = $this->Category->ensure($categoryPath, 1); 
        }
        echo "<pre>";
            print_r($categories);
        echo "</pre>";
    }

    /**
     * Reads and processes the CSV file and imports images
     *
     * @param string $csvFilePath path to Csv file
     */
    public function importBrandsFromOdooCsv($csvFilePath) {
        $data = [];
        if (($handle = fopen($csvFilePath, "r")) !== FALSE) {
            $headers = fgetcsv($handle, 1000, ","); 
            while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $rowData = array_combine($headers, $row); 
                $id = $rowData['ID'];
                $brandName = trim($rowData['Brand Name']);
                $created = $rowData['Vytvorené'];
                $modified = $rowData['Naposledy upravované'];
                //slug
                if ($brandName);
                    $slug = strtolower($brandName); 
                    $slug = Sanitize::nonAscii($slug); 
                    $slug = preg_replace('/[^a-z0-9]+/', '-', $slug); 
                    $slug = trim($slug, '-'); 
                $logoFilename = "";
                if (!empty($rowData['Logo File'])) {
                    if (!is_dir("userfiles/Eshop/EshopBrand/logo/")) {
                        mkdir("userfiles/Eshop/EshopBrand/logo/", 0777, true);
                    }
                    $imageData = explode(',', $rowData['Logo File']);
                    $imageData = count($imageData) > 1 ? base64_decode($imageData[1]) : base64_decode($rowData['Logo File']);
                    $finfo = new finfo(FILEINFO_MIME_TYPE);
                    $mimeType = $finfo->buffer($imageData);
                    if ($mimeType == "image/svg+xml") {
                        $extension = "svg";
                    } 
                    elseif ($mimeType == "image/jpeg") {
                        $extension = "jpg";
                    } 
                    elseif ($mimeType == "image/png") {
                        $extension = "png";
                    } 
                    else {
                        $extension = "png";
                    } 
                    $output_file = "userfiles/Eshop/EshopBrand/logo/{$slug}.{$extension}";
                    file_put_contents($output_file, $imageData);
                    $logoFilename ="{$slug}.{$extension}";;
                }
                $data[] = [
                    'id' => $id ?? null,
                    'name' => $brandName ?? null,
                    'seo_title' => $brandName ?? null,
                    'created' => $created ?? null,
                    'modified' => $modified ?? null,
                    'slug' => $slug ?? null,
                    'logo' => $logoFilename ?? null,
                ];
            }
            fclose($handle);
            $this->saveBatch(array(
                'create' => array(
                    'EshopBrand' => $data
                ),
            ),
            array(
                'validate' => false,
            ));
            echo "<pre>";
                print_r($data);
            echo "</pre>";
        }
    }

}
