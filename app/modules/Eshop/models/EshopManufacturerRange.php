<?php

class EshopManufacturerRange extends EshopModel {
    protected $table = 'run_eshop_manufacturer_ranges';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_eshop_manufacturers_id' => array('type' => 'int', 'index' => 'index'),
        'slug' => array('type' => 'varchar', 'index' => 'index'),
        'seo_title' => array('type' => 'varchar', 'default' => null),
        'seo_description' => array('type' => 'varchar', 'default' => null),
        'seo_keywords' => array('type' => 'varchar', 'default' => null),
        'name' => array('type' => 'varchar', 'index' => 'index'),
        'description' => array('type' => 'text', 'default' => null),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
    );
    
    protected $translatedFields = array(
        'slug',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'name',
        'description',
    );
    
    protected $nameField = 'name';
    
    public function __construct() {
        parent::__construct();
        
        $this->validations = array(
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                ),
            ),  
            'slug' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Slug is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Slug is mandatory'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Slug must be unique'),
                ),
            ),                        
            'run_eshop_manufacturers_id' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Choose manufacturer'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Choose manufacturer'),
                ),
            ),                        
        );
    }
    
    /**
     * Normalizes data 
     * 
     * @param array $data
     * @param string $options Optional. 
     *      - 'on' (string) Possible values are 'create', 'update', NULL
     *      - 'alternative' (string) Possible values are 'search', NULL
     *      - 'avoidSlugs' (array) Slugs which should not be used. Defaults to empty array.
     * 
     * @return array
     */
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
            'avoidSlugs' => array(),
        );
        $options = array_merge($defauts, $options);
        
        /**
         * Save normalization
         */
        if (
            empty($data['slug'])
            && (
                $options['on'] === 'create'
                || array_key_exists('slug', $data) 
            )
        ) {
            $data['slug'] = Sanitize::value($data['name']);
        }
        
        if (!empty($data['slug'])) {
            if ($options['on'] == 'create') {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                    'avoidValues' => &$options['avoidSlugs'],
                ));
            }
            else {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                    'id' => Sanitize::value($data['id']),
                    'avoidValues' => &$options['avoidSlugs'],
                ));
            }
        }
        
        if (
            $options['on'] === 'create'
            && empty($data['seo_title']) 
            && !empty($data['name'])
        ) {
            $data['seo_title'] = $data['name'];
        }
                       
        return parent::normalize($data, $options);
    }    
    
    /**
     * Ensures the existence of manufacturer range specified by name. In the case that 
     * manufacturer range does not exist, it is created in DB or added to batch (see input $newBatch).
     * 
     * Used mostly for imports
     * 
     * @staticvar array $existing Internal cache of existing manufacturer ranges
     * @staticvar array $slugs Internal cache of new manufacturer ranges slugs added to batch
     * @staticvar array $newId Internal cache of new manufacturer range id added to batch
     * 
     * @param string $name Manufacturer range name to ensure existence of
     * @param array& $newBatch Optional, passed by reference, auxiliary output. 
     *      Array of new manufacturer range records to be used in batch save. If not provided then new 
     *      manufacturer ranges are directly created in DB. Defaults to NULL (not provided).
     * 
     * @return int|bool Id of manufacturer range having given $name. It does not matter if 
     *      manufacturer range has existed or it has been created od added to batch. Returns 
     *      FALSE on $name validation error.
     */
    public function ensure($name, &$newBatch = null) {
        // internal cache of existing manufacturer ranges 
        static $existing = null;
        // internal chache of created slugs
        static $slugs = array();
        // internal cache of new record id for case of batch records
        static $newId = null;
        
        // load existing manufacturer ranges if not loaded yet
        if ($existing === null) {
            $itemsResource = $this->find(array(
                'fields' => array(
                    'EshopManufacturerRange.id',
                    'EshopManufacturerRange.name',
                ),
                'resource' => true,
            ));
            $existing = array();
            while ($item = DB::fetchArray($itemsResource)) {
                if ($item['name'] === '' || $item['name'] === null) {
                    continue;
                }
                $normalizedName = Str::normalizeForNonstrictComparison($item['name']);
                if ($normalizedName === '') {
                    continue;
                }
                if (!isset($existing[$normalizedName])) {
                    $existing[$normalizedName] = $item['id'];
                }
                else {
                    $existing[$item['name']] = $item['id'];
                }
            }
        }
        // load newId if not loaded yet
        if (is_array($newBatch) && $newId === null) {
            $newId = $this->getNextId();
        }
        // check for manufacturer range existence
        if (isset($existing[$name])) {
            return $existing[$name];
        }
        $normalizedName = Str::normalizeForNonstrictComparison($name);
        if (isset($existing[$normalizedName])) {
            return $existing[$normalizedName];
        }
        // create new manufacturer range
        $new = array(
            'name' => $name,
            'slug' => null,
            'created' => null,
            'modified' => null,
        );
        $new = $this->normalize($new, array(
            'on' => 'create',
            'avoidSlugs' => $slugs,
        ));
        if (
            !$this->validate($new, array(
                'on' => 'create',
                'normalize' => false,
                'allowFields' => array('name'),
            ))
        ) {
            return false;
        }
        // if batch array provided then put new record there
        if (is_array($newBatch)) {
            $new['id'] = $newId;
            $newBatch[] = $new;
            // store created slugs into cache in case of batch 
            $slugs[] = $new['slug'];
            $existing[$normalizedName] = $newId;
            $newId++;
        }
        // if no batch array provided then save it in DB
        else {
            $new = $this->save($new, array(
                'create' => true,
                'normalize' => false,
                'validate' => false,
            ));
            $existing[$normalizedName] = $new['id'];
            $newId = null;
        }
        // return id of manufacturer range
        return $existing[$normalizedName];
    }    
}
