<?php

class EshopProductAttributeDetail extends Model {
    protected $table = 'run_eshop_product_attribute_details';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_eshop_product_attributes_id' => array('type' => 'int', 'index' => 'index', 'default' => null),
        'run_eshop_products_id' => array('type' => 'int', 'index' => 'index', 'default' => null),
        'code' => array('type' => 'varchar', 'length' => 50),
        'name' => array('type' => 'varchar', 'length' => 50),
        'stock' => array('type' => 'int', 'default' => 0),
        'reserved' => array('type' => 'int', 'default' => 0, 'length' => 10),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
    );
    
}
