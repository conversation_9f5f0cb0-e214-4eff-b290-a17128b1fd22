<?php

class EshopProductCategory extends EshopModel {
    protected $table = 'run_eshop_product_categories';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'pid' => array('type' => 'varchar', 'default' => null, 'index' => 'index', 'comment' => 'Permanent id of category used in program logic'),
        'code' => array('type' => 'varchar', 'length' => 30, 'default' => null, 'index' => 'index', 'comment' => 'Permanent code of category used e.g. during imports'),
        'slug' => array('type' => 'varchar', 'index' => 'index'),
        'seo_title' => array('type' => 'varchar', 'default' => null),
        'seo_description' => array('type' => 'varchar', 'default' => null),
        'seo_keywords' => array('type' => 'varchar', 'default' => null),
        'name' => array('type' => 'varchar', 'index' => 'index'),
        'alternative_name' => array('type' => 'varchar', 'default' => null, 'index' => 'index', 'comment' => 'Alternative name can be used instead of name in some specific contexts, e.g. as a category name in category products index'),
        'heureka_name' => array('type' => 'tinytext', 'default' => null, 'comment' => 'Category name used to create content of CATEGORYTEXT tag in feed for Heureka.sk'),
        'zbozicz_name' => array('type' => 'tinytext', 'default' => null, 'comment' => 'Category name used to create content of CATEGORYTEXT tag in feed for Zbozi.cz'),
        'description' => array('type' => 'text', 'default' => null),
        'show_description' => array('type' => 'enum', 'values' => array('enum_no', 'enum_before_products', 'enum_after_products', 'enum_without_products'), 'default' => 'enum_no'),
        'sort_products' => array('type' => 'bool', 'default' => 0, 'comment' => 'If 1 then default products order in indexes is the order in which they are added to the category'),
        'product_parameters' => array('type' => 'text', 'default' => null, 'comment' => 'Comma separated list of product parameters (fields) which are applicable for category products'),
        'supplier_pid' => array('type' => 'varchar', 'default' => null, 'length' => 30, 'comment' => 'Actual category supplier pid set when category is created by import'),
        'active' => array('type' => 'bool', 'default' => 1),
        'parent_id' => array('type' => 'int', 'default' => null, 'index' => 'index'),
        'path' => array('type' => 'varchar', 'default' => null),
        'image' => array('type' => 'varchar', 'default' => null),
        'sort' => array('type' => 'int', 'default' => null),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
        'deleted' => array('type' => 'datetime', 'default' => null, 'index' => 'index'),        
    );
    
    protected $nameField = 'name';
    
    protected $translatedFields = array(
        'slug',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'name',
        'alternative_name',
        'description',
    );
    
    protected $fileFields = array(
        'image' => array(
            'path' => 'images',
            //'extension' => 'jpg',
            'quality' => 85,
        ),
    );
    
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(
            'slug' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Slug is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Slug is mandatory'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Slug must be unique'),
                ),
            ),            
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                ),
            ),            
            'sort_products' => array(
                array(
                    'rule' => function($value, $field, $data, &$validation) {
                        if ($value && !empty($data['id'])) {
                            $this->loadModel('EshopProductCategoryProduct');
                            $ProductCategory = new EshopProductCategoryProduct();
                            $productsCount = $ProductCategory->findCountBy('run_eshop_product_categories_id', $data['id']);
                            $productsLimit = $this->getSetting('EshopProductCategory.sortProductsLimit');
                            if ($productsLimit <= 0) {
                                $validation['message'] = __v(__FILE__, 'Tento príznak nie je možné zapnúť pretože nastavenie Eshop > Nastavenia > "Zoraďovať produkty v kategórii do počtu" má hodnotu 0', $productsCount, $productsLimit);
                                return false;
                            }
                            elseif ($productsCount > $productsLimit) {
                                $validation['message'] = __v(__FILE__, 'Tento príznak nie je možné zapnúť pretože počet produktov v tejto kategórii (%s) je väčší ako nastavený hraničný počet v Eshop > Nastavenia > Zoraďovať produkty v kategórii do počtu > %s', $productsCount, $productsLimit);
                                return false;
                            }
                        }
                        return true;
                    },
                ),
            ),      
                            
            /**
             * Search fields
             */
            'keywords' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please enter search keywords'),
                    'alternative' => 'search',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please enter search keywords'),
                    'alternative' => 'search',
                ),
                array(
                    'rule' => 'searchKeywords',
                    'message' => __v(__FILE__, 'Keywords must be at least 2 chars long'),
                    'alternative' => 'search',
                ),
            ),
        );
    }
        
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        
        if (
            empty($data['slug'])
            && (
                $options['on'] === 'create'
                || array_key_exists('slug', $data) 
            )
        ) {
            $data['slug'] = Sanitize::value($data['name']);
        }
        
        if (!empty($data['slug'])) {
            if ($options['on'] == 'create') {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                ));
            }
            else {
                $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                    'lang' => $this->getTranslationLang($options, $data),
                    'id' => Sanitize::value($data['id']),
                ));
            }
        }
        
        if (
            $options['on'] === 'create'
            && empty($data['seo_title']) 
            && !empty($data['name'])
        ) {
            $data['seo_title'] = $data['name'];
        }
        
        if (!empty($data['product_ids'])) {
            if (is_string($data['product_ids'])) {
                $data['product_ids'] = explode(';', $data['product_ids']);
            }
            $data['product_ids'] = array_unique(array_filter(array_map('trim', $data['product_ids'])));
        }
                
        return parent::normalize($data, $options);
    }
        
    /**
     * @inheritdoc
     * 
     * Overrides Model::save() to allow:
     * - tree node movement on save. The node is moved if the $data contains 'id', 'parent_id', 'new_parent_id'
     * and the 'parent_id' does not equals to 'new_parent_id'.
     * - URL redirection if slug is changed
     * 
     * @param array $data
     * @param array $options Model::save() options plus following are available
     *      - 'addRedirection' (bool) Adds redirection on change of slug. Defaults to !ON_LOCALHOST.
     * 
     * @return boolean array|bool Array of updated or created record data containing id of created record
     *      and other values after normalization or file uploads. FALSE if validation fails or 
     *      if the moved or new parent node does not exist or if the node tries to be moved under one of its childs.  
     */
    public function save($data, $options = array()) {
        $options = array_merge(array(
            'addRedirection' => !ON_LOCALHOST,
        ), $options);
        // get old record for redirection addition here below
        $oldRecord = array();
        if (
            $options['addRedirection']
            && !empty($data['id'])
            && array_key_exists('slug', $data)
        ) {
            $oldRecord = $this->findFirstBy('id', $data['id'], array(
                'fields' => array($this->name . '.slug'),
                'lang' => Sanitize::value($options['lang']),
            ));
        }
        // move tree node   
        // - check for new_previous_sibling_id
        if (
            !empty($data['id']) // on update
            && !empty($data['new_previous_sibling_id'])
            && !empty($data['previous_sibling_id'])
            && $data['new_previous_sibling_id'] != $data['previous_sibling_id']
        ) {
            if (!$this->moveTreeNodeBehind($data['id'], $data['new_previous_sibling_id'])) {
                return false;
            }
            // remove other three fields to no preak tree structure
            unset($data['parent_id']);
            unset($data['path']);
            unset($data['sort']);
        }
        // - check for new_parent_id
        elseif (
            !empty($data['id']) // on update
            && !empty($data['new_parent_id'])
            && !empty($data['parent_id'])
            && $data['new_parent_id'] != $data['parent_id']
        ) {
            if (!$this->moveTreeNode($data['id'], $data['new_parent_id'])) {
                return false;
            }
            unset($data['parent_id']);
            unset($data['path']);
            unset($data['sort']);
        }
        // reset 'deleted' for whole tree branch
        if (
            array_key_exists('deleted', $data)
            && !empty($data['id'])
        ) {
            if(!$this->updateInTree($data['id'], array_intersect_key($data, array(
                'deleted' => null,  // value does not matter here
                'active' => null,   // value does not matter here
            )))) {
                return false;
            }
        }
        // save its data
        if (!($result = parent::save($data, $options))) {
            return false;
        }
        // add redirection if slug was changed
        if (
            $options['addRedirection']
            && !empty($oldRecord['slug'])
            && !empty($result['slug'])
            && $oldRecord['slug'] != $result['slug']
        ) {
            $webContentLocator = App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory', array(
                'lang' => Sanitize::value($options['lang']),
            ));
            $oldLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($oldRecord['slug']),
            ));
            $newLocator = App::getUrl(array(
                'locator' => $webContentLocator,
                'args' => array($result['slug']),
            ));
            App::loadModel('App', 'UrlRedirection');
            $UrlRedirection = new UrlRedirection();
            $UrlRedirection->add($oldLocator, $newLocator);
        }
        return $result;
    }
    
    /**
     * 
     * @param array $options
     * @return array
     * 
     * @throws Exception_DB_TablesReservationFailure
     */
    public function findAll($options = array()) {
        $defaults = array(
            'first' => false,
        );
        $options = array_merge($defaults, $options);
        // apply first
        $first = $options['first'];
        if ($first) {
            $options['first'] = false;
            $options['limit'] = 1;
        }
        
        // reserve tables (to avoid writes while reading data)
        $this->reserveTables('EshopProductCategory_findAll', array(
            'EshopProductCategory',
            'EshopProductCategoryProduct',
        )); 
        $options['key'] = 'EshopProductCategory.id';
        $categories = $this->findList($options);
        $categoryIds = array_keys($categories);
                
        // get products ids (but only if it is allowed by nonzero value
        // of setting EshopProductCategory.sortProductsLimit)
        $sortProductsLimit = $this->getSetting('EshopProductCategory.sortProductsLimit');
        if ($sortProductsLimit > 0) {
            $ProductCategory = $this->loadModel('EshopProductCategoryProduct', true);
            $products = $ProductCategory->find(array(
                'fields' => array(
                    'EshopProductCategoryProduct.run_eshop_products_id', 
                    'EshopProductCategoryProduct.run_eshop_product_categories_id',
                ),
                'conditions' => array('EshopProductCategoryProduct.run_eshop_product_categories_id' => $categoryIds),
            ));
            foreach ($products as $product) {
                $categoryId = $product['run_eshop_product_categories_id'];
                if (empty($categories[$categoryId]['product_ids'])) {
                    $categories[$categoryId]['product_ids'] = array();
                }
                $categories[$categoryId]['product_ids'][] = $product['run_eshop_products_id'];
            }
            // turn off sort_products if products count is higher than $sortProductsLimit
            // This is done just to avoid admin form collapse
            foreach ($categories as $categoryId => &$category) {
                if (
                    !empty($category['sort_products'])
                    && !empty($category['product_ids'])
                    && count($category['product_ids']) > $sortProductsLimit
                ) {
                    $this->save(
                        array(
                            'id' => $categoryId,
                            'sort_products' => false
                        ),
                        array(
                            'normalize' => false,
                            'validate' => false,
                        )
                    );
                    $category['sort_products'] = false;
                    $this->setWarning('sort_products', __v(
                        __FILE__, 
                        'Príznak bol zrušený pretože počet produktov (%s) v tejto kategórii je väčší ako nastavený hraničný počet v Eshop > Nastavenia > Zoraďovať produkty v kategórii do počtu > %s',
                        count($category['product_ids']),
                        $sortProductsLimit
                    ));
                }
            }
        }

        $this->unreserveTables('EshopProductCategory_findAll');
        
        // get just values (without product ids in keys) to be properly json encoded
        $categories = array_values($categories);
        
        if ($first) {
            $categories = reset($categories);
        }
        return $categories;
    }
    
    /**
     * Saves:
     *      standard category fields
     *      + habtm products (ids are in product_ids field)
     * 
     * @param array $data
     * @param array $options Model::save() options, e.g. 'lang' - probably the only used here.
     * 
     * @return bool|array Array of saved data on success and FALSE on validation or processing failure.
     * 
     * @throws Exception_DB_TablesReservationFailure
     * @throws Exception
     */
    public function saveAll($data, $options = array()) {
        try {
            $this->reserveTables('EshopProductCategory_saveAll', array(
                'EshopProductCategory',
                'EshopProductCategoryProduct',
            ), array('tries' => 20, 'retryTime' => 1000));
            
            // update or insert
            $options['on'] = null; // to make Model::isCreation() resolve correctly
            $options['on'] = ($this->isCreation($data, $options)) ? 'create' : 'update';

            // normalize
            $data = $this->normalize($data, $options);
            // turn normalization off for further processing
            $options['normalize'] = false;
            
            // validate and turn validation off for further processing
            $categoryIsValid = $this->validate($data, $options);
        
            if (
                !$categoryIsValid 
            ) {
                $this->unreserveTables('EshopProductCategory_saveAll');
                return false;
            }
            $options['validate'] = false;

            DB::startTransaction('EshopProductCategory_saveAll');
            $result = $this->save($data, $options);
            if (!$result) {
                DB::rollbackTransaction('EshopProductCategory_saveAll');
                $this->unreserveTables('EshopProductCategory_saveAll');
                return false;
            }
            $categoryId = $this->getPropertyId();
            $data = $result;
            // check for product_ids 
            if (isset($data['product_ids'])) {
                $ProductCategory = $this->loadModel('EshopProductCategoryProduct', true);
                $ProductCategory->deleteBy('run_eshop_product_categories_id', $categoryId);
                if (!empty($data['product_ids'])) {
                    $records = array();
                    foreach ($data['product_ids'] as $productId) {
                        $records[] = array(
                            'run_eshop_products_id' => $productId,
                            'run_eshop_product_categories_id' => $categoryId
                        );
                    }
                    DB::insert(
                        $ProductCategory->getPropertyTable(), 
                        $records, 
                        array('multiple' => true)
                    );
                }
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopProductCategory_saveAll');
            $this->unreserveTables('EshopProductCategory_saveAll');
            throw $e;
        }
        
        DB::commitTransaction('EshopProductCategory_saveAll');
        $this->unreserveTables('EshopProductCategory_saveAll');
        return $data;
    }    

    /**
     * Finds category id by provided category names path, means array of names starting
     * by top level category name, passing by subnames and ending by searched category name.
     * 
     * @param array $namesPath Array of names contaning parents category names and
     *      the searched category name
     * @param array $categoriesTree Optional. Categories tree to search for the category.
     *      Used for optimization reasons in imports to avoid tree retrieval 
     *      on each new cycle turn. If not provided (NULL) then the method itself will 
     *      retrieve the tree. Defaults to NULL.
     * 
     * @return boolean|int
     */
    public function findIdByNamesPath($namesPath, $categoriesTree = null) {
        // load categories
        if ($categoriesTree === null) {
            $categoriesTree = $this->findTree(
                'categories',
                array(
                    'fields' => array('EshopProductCategory.name')
                )
            );
        }
        // search for category
        $namesPath = (array)$namesPath;
        $id = null;
        foreach ($namesPath as $name) {
            // get the top level categories
            if (!$id) {
                $curentLevelCategories = reset($categoriesTree);
            }
            // get sublevel categories
            elseif (!empty($categoriesTree[$id])) {
                $curentLevelCategories = $categoriesTree[$id];
            }
            // return false if sublevel does not exist
            else {
                return false;
            }
            $id = null;
            foreach($curentLevelCategories as $categoryId => $category) {
                if ($category['name'] == $name) {
                    $id = $categoryId;
                    break;
                }
            }
            if (!$id) {
                return false;
            }
        }
        return $id;
    }   
    
    /**
     * Ensures existence of category given by its names path, means array of names starting
     * by top level category name, passing by subnames and ending by searched category name.
     * All unexisting parts of names path are created.
     * 
     * @param array $namesPath Array of category names path e.g. array('Top Category', 'Second Level', ..., 'Target Category')
     * @param int|string $root Root node of tree to ensure the names path for.
     *      It can be specified like:
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for root node id. 
     *      - If non integer value is provided ('myMenu') then it is considered  for 
     *      root node pid.
     *      - If an array then it is taken for complete options to retrieve root.
     * 
     * @return int Id of ensured category.
     */
    public function ensure($namesPath, $root) {
        // internal cache of existing authors 
        static $existing = null;       
        
        // create string version of root to store cached trees in $existing
        $rootKey = $root;
        if (is_array($root)) {
            $rootKey = json_encode($root);
        }
        // retrieve tree and store it in cache
        if (!isset($existing[$rootKey])) {
            $existing[$rootKey] = $this->findTree($root, array(
                'fields' => array('EshopProductCategory.name')
            ));
            if (!$existing[$rootKey]) {
                throw new Exception(__e(__FILE__, 'Invalid tree root %s', $root));
            }
            // reformat tree from array('23' => array('45' => array('name' => 'myCategory'), ...), ...)
            // to array('23' => array('45' => 'myCategory', ...), ...)
            foreach ($existing[$rootKey] as &$treeLevel) {
                foreach ($treeLevel as &$treeLevelItem) {
                    $treeLevelItem = $treeLevelItem['name'];
                }
                unset($treeLevelItem);
            }
            unset($treeLevel);
        }
        // create category
        $namesPath = (array)$namesPath;
        $parentId = array_keys($existing[$rootKey]);
        $parentId = reset($parentId);
        $create = false;
        foreach ($namesPath as $name) {
            // search for existing part of category names path
            if (!$create) {
                // get current parent id subcategories
                if (!empty($existing[$rootKey][$parentId])) {
                    $curentLevelCategories = $existing[$rootKey][$parentId];
                }
                // set create flag if current parent ids has no subcategories
                else {
                    $create = true;
                }
                if (!$create) {
                    $id = null;
                    foreach($curentLevelCategories as $categoryId => $categoryName) {
                        if ($categoryName == $name) {
                            $id = $categoryId;
                            break;
                        }
                    }
                    if (!$id) {
                        $create = true;
                    }
                    else {
                        $parentId = $id;
                    }
                }
            }
            // create unexisting part of names path
            if ($create) {
                $this->addTreeNode($parentId, array(
                    'name' => $name,
                    'slug' => $this->getUniqueSlug($name),
                ));
                // update categories tree
                $existing[$rootKey][$parentId][$this->id] = $name;
                $existing[$rootKey][$this->id] = array();
                // use the created record id as a parent id for next item in names path
                $parentId = $this->id;
            }
        }
        
        // return bottom category id
        return $parentId;
    }   
    
    /**
     * Resolves slug of active category
     * 
     * @param array $options Following are available
     *      - 'arg' (mixed) The first argument passed in actual URL. Defaults to App::$arg[0]
     *      - 'categoryProductsIndexSlug' (string) Slug of content 'Eshop.EshopProducts.indexCategory'.
     *          It can be provided for optimisation reasons. If not provided then it is retrieved by method itself.
     *      - 'productViewSlug' (string) Slug of content 'Eshop.EshopProducts.view'.
     *          It can be provided for optimisation reasons. If not provided then it is retrieved by method itself.
     * 
     * @return string|null
     */
    public function resolveActiveSlug($options = array()) {
        $defaults = array(
            'arg' => null,
            'categoryProductsIndexSlug' => null,
            'productViewSlug' => null,
        );
        $options = array_merge($defaults, $options);
        if (
            $options['arg'] === null
            && isset(App::$args[0])
        ) {
            $options['arg'] = App::$args[0];
        }
        
        $activeSlug = null;
        
        // find active category slug
        // - if we are on category products index page then the arg is the category slug
        if (
           $options['categoryProductsIndexSlug'] !== null
           && SLUG === $options['categoryProductsIndexSlug']
           ||
           $options['categoryProductsIndexSlug'] === null
           && SLUG === ($options['categoryProductsIndexSlug'] = App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory'))
        ) {
            $activeSlug = $options['arg'];
        }
        // - if we are on product view page, then $options['arg'] is product slug 
        // and actual category slug can be found... 
        elseif (
           $options['productViewSlug'] !== null
           && SLUG === $options['productViewSlug']
           ||
           $options['productViewSlug'] === null
           && SLUG === App::getContentLocatorByPid('Eshop.EshopProducts.view')
        ) {
            $CategoryXProduct = $this->loadModel('EshopProductCategoryProduct', true);
            $items = $CategoryXProduct->find(array(
                'joins' => array(
                    array(
                        'model' => 'EshopProduct',
                        'type' => 'left',
                    )
                ),
                'conditions' => array('EshopProduct.slug' => $options['arg']),
                'fields' => array('run_eshop_product_categories_id'),
            ));
            if ($items) {
                // get categories ids
                $tmp = array();
                foreach($items as $item) {
                    $tmp[] = $item['run_eshop_product_categories_id'];
                }
                $items = $tmp;
                // get categories
                $categories = $this->findBy('id', $items, array(
                    'fields' => array('EshopProductCategory.slug')
                ));  
                if (count($categories) > 1) {
                    $refererUrl = App::parseUrl(App::getRefererUrl('/'));
                    if (
                        $refererUrl['type'] === 'slug' 
                        && $refererUrl['slug'] === $options['categoryProductsIndexSlug']
                    ) {
                        // - then find the category we are going from
                        $categorySlug = reset($refererUrl['args']);
                        foreach ($categories as $category) {
                            if ($category['slug'] === $categorySlug) {
                                break;
                            }
                        }
                    }
                    else {
                        $category = reset($categories);
                    }
                }
                else {
                    $category = reset($categories);
                }
                $activeSlug = $category['slug'];
            }
        }
        // - otherwise we cannot resolve category slug
        else {
            $activeSlug = null;
        }
        
        return $activeSlug;
    }    
    
    /**
     * Converts provided $searchData into find options used by Model::find().
     * 
     * @param array $searchData Search data containing at least 'keywords' item.
     *      All other item are dependent by concrete implementation of this method on project.
     * @param array $options Following are avalible:
     *      - 'findOptions' (array) Find options to merge generated options into. 
     *          Defaults to empty array().
     *      - 'relevanceField' (bool) If TRUE then search relevance is added to 'fields' 
     *          as '_relevance' virtual field and it is not added to 'order' option. 
     *          If FALSE then search relevance is added to 'order' option. Defaults to FALSE.
     * 
     * @return array|bool Find options created according to provided $searchData.
     *      If $findOptions is provided the new options are merged into existing.
     *      Returns FALSE if $searchData are invalid.
     */
    public function getSearchFindOptions($searchData, $options = array()) {
        $options = array_merge(array(
            'findOptions' => array(),
            'relevanceField' => false,
        ), $options);
        $findOptions = &$options['findOptions'];
        // normalize and validate
        $searchData = $this->normalize($searchData, array('alternative' => 'search'));
        $valid = $this->validate($searchData, array(
            'normalize' => false,
            'alternative' => 'search',
        ));
        if (!$valid) {
            return false;
        }
        
        // ensure existence of folowing items in find options
        $findOptions['conditions'] = (array)Sanitize::value($findOptions['conditions']);
        $findOptions['conditions'] = DB::nestConditions($findOptions['conditions']);
        $findOptions['literals'] = (array)Sanitize::value($findOptions['literals']);
        $findOptions['literals']['conditions'] = (array)Sanitize::value($findOptions['literals']['conditions']);
        if ($options['relevanceField']) {
            $findOptions['fields'] = (array)Sanitize::value($findOptions['fields']);
            $findOptions['literals']['fields'] = (array)Sanitize::value($findOptions['literals']['fields']);
        }
        else {
            $findOptions['order'] = (array)Sanitize::value($findOptions['order']);
            $findOptions['literals']['order'] = (array)Sanitize::value($findOptions['literals']['order']);
        }
        
        // set default conditions
        $findOptions['conditions'][] = array(
            'EshopProductCategory.active' => true,
        ); 
        
        // if you order results also by some user selected custom sort (e.g. by price)
        // then strict search conditions (AND between keywords) must by generated. 
        // Loose search conditions (OR between keywords) can be generated only in case of
        // pure relevence order as in this case the results containing all keywords are
        // placed as first by relevance. Even in case of user custom sort the relevance order 
        // can be used too but only as secondary order.
        $strictSearchConditions = !empty($findOptions['order']);
        
        // keywords
        $keywords = self::parseSearchKeywords($searchData['keywords']);

        // check if we are searching for a single integer
        $singleIntegerSearch = (
            count($keywords) === 1
            && preg_match('/^[0-9]+$/', $keywords[0])
        );

        // check if we are searching for strings only (there is no integer)
        $stringOnlySearch = true;
        foreach ($keywords as $keyword) {
            if (preg_match('/^[0-9]+$/', $keyword)) {
                $stringOnlySearch = false;
                break;
            }
        }

        // prepare order by relevance (can be adjusted on project)
        // - define relevance levels from highest to lowest. Add any number 
        // of your own conditions here or adjust the existing conditions.
        // The default operator applied betweed relevance conditions for each 
        // keyword is OR - it means the relevance condition is TRUE if at least
        // one of keywords matches this condition. You can change the "between" operator
        // to 'AND' like {relevanceCondition} => 'AND' - it means you provide
        // the conditions like key and the value is 'AND'. By the way the 'AND'
        // is more relevant as the condition is TRUE only if it matches all keywords.
        // If you add fields from other models then actualize also the joins above
        $relevanceExpressions = array(
            // matchs whole name for phrase
            'EshopProductCategory.name = ":k:"' => 'PHRASE',
            // matchs beginning of name for phrase
            'EshopProductCategory.name LIKE ":k:%"' => 'PHRASE',
            // matchs anywhere in name for phrase
            'EshopProductCategory.name LIKE "%:k:%"' => 'PHRASE',
            // matchs whole word in name for all keywords
            '(EshopProductCategory.name LIKE "% :k:" OR EshopProductCategory.name LIKE ":k: %" OR EshopProductCategory.name LIKE "% :k: %")' => 'AND',
            // matchs anywhere in name for all keywords
            'EshopProductCategory.name LIKE "%:k:%"' => 'AND',
            // matchs whole name for one of keywords
            'EshopProductCategory.name = ":k:"',
            // matchs whole word in name for one of keywords
            '(EshopProductCategory.name LIKE "% :k:" OR EshopProductCategory.name LIKE ":k: %" OR EshopProductCategory.name LIKE "% :k: %")',
            // matchs begining of word in name for one of keywords
            '(EshopProductCategory.name LIKE ":k:%" OR EshopProductCategory.name LIKE "% :k:%")',
            // matchs anywhere in name for one of keywords
            'EshopProductCategory.name LIKE "%:k:%"',
        );
        // - get relevance order sql
        if (
            ($relevanceSql = self::getSearchRelevance(
                $relevanceExpressions,
                $searchData['keywords'],
                $keywords
            ))
        ) {                
            if ($options['relevanceField']) {
                $findOptions['fields'][] = $relevanceSql . ' AS `_relevance`';
                $findOptions['literals']['fields'][] = $relevanceSql . ' AS `_relevance`';
            }
            else {
                $findOptions['order'][] = $relevanceSql . ' DESC';
                $findOptions['literals']['order'][] = $relevanceSql . ' DESC';                    
            }
        }

        // search for keywords
        $searchFields = array(
            'EshopProductCategory.name',
            //'EshopProductCategory.code',
            //'EshopProductCategory.slug',
            //'EshopProductCategory.seo_title',
            //'EshopProductCategory.seo_description',
            //'EshopProductCategory.seo_keywords',
            'EshopProductCategory.description',
        );
        if (
            ($searchConditions = self::getSearchConditions(
                $searchFields, 
                $keywords, 
                array(
                    'strict' => $strictSearchConditions
                )
            ))
        ) {
            $findOptions['conditions'][] = $searchConditions;
        }
        
//        // because of joins with ... some categories can occure multiple times
//        $findOptions['group'] = 'EshopProductCategory.id';
        
        return $findOptions;
    }
}
