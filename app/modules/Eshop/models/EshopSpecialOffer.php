<?php

class EshopSpecialOffer extends EshopModel {
    protected $table = 'run_eshop_special_offers';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'slug' => array('type' => 'varchar', 'default' => null, 'index' => 'unique'),
        'seo_title' => array('type' => 'varchar', 'default' => null),
        'seo_description' => array('type' => 'varchar', 'default' => null),
        'seo_keywords' => array('type' => 'varchar', 'default' => null),
        'name' => array('type' => 'varchar', 'index' => 'index'),
        'description' => array('type' => 'text', 'default' => null),
        'obfuscate_description' => array('type' => 'bool', 'default' => 0, 'comment' => 'Should be description obfuscated for sake of contained e-mails/phones protection?'),
        'menu_image' => array('type' => 'varchar', 'default' => null,  'comment' => 'Special offer image used for side menu'),
        'show_in_menu' => array('type' => 'bool', 'default' => 1, 'comment' => 'Should be special offer included in side menu?'),
        'banner_image' => array('type' => 'varchar', 'default' => null,  'comment' => 'Special offer banner image'),
        'banner_text' => array('type' => 'text', 'default' => null,  'comment' => 'Special offer banner text'),
        'cart_banner_type' => array('type' => 'enum', 'default' => 'image_with_text', 'values' => array('image_with_text', 'discounted_products_index', 'discounted_products_slider'), 'comment' => 'How is the special offer (if applicable) displayed in cart?'),
        'promoted_products_label_image' => array('type' => 'varchar', 'default' => null,  'comment' => 'Special offer products label image'),
        'promoted_products_label_text' => array('type' => 'varchar', 'default' => null,  'comment' => 'Special offer products label text'),
        'discounted_products_label_image' => array('type' => 'varchar', 'default' => null,  'comment' => 'Special offer discounted products label image'),
        'discounted_products_label_text' => array('type' => 'varchar', 'default' => null,  'comment' => 'Special offer discounted products label text'),
        'discounted_products_price_adjustment' => array('type' => 'varchar', 'length' => 10,  'comment' => '1.50 = 1.50 €, 0 = for free, 25% = discount percentage'),
        'apply_by' => array('type' => 'enum', 'default' => 'promoted_products_in_cart', 'values' => array('promoted_products_in_cart', 'cart_price_threshold', 'promoted_products_cart_price_threshold'), 'comment' => 'If offer is applied by promoted_products_in_cart, then discounted products are available for all promoted (normally priced) products placed in cart. If offer is applied by cart_price_threshold, then discounted products are available if cart total is higher than specified cart_price_threshold. If offer is applied by promoted_products_cart_price_threshold, then discounted products are available if cart total of promoted products is higher than specified cart_price_threshold.'),
        'cart_price_threshold' => array('type' => 'decimal', 'length' => 8.2, 'default' => null, 'comment' => 'Applies only if apply_by is set to cart_price_threshold'),
        'active_from' => array('type' => 'date', 'default' => null, 'comment' => 'If set then activity is constrained only to specified period'),
        'active_to' => array('type' => 'date', 'default' => null, 'comment' => 'If set then activity is constrained only to specified period'),
        'active' => array('type' => 'bool', 'default' => 0, 'comment' => 'If 1 and active_from or active_to is set then activity is constrained only to specified period'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
    protected $nameField = 'name';
    
    protected $fileFields = array(
        'menu_image' => array(
            'variants' => array(
                'menu' => array(
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fitX' => 760,
                ),
            ),
        ),
        'banner_image' => array(
            'variants' => array(
                'original' => array(
                    'source' => true,
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fitX' => 1190,
                ),
                'menu' => array(
                    'lazy' => true,
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fitX' => 760,
                ),
            ),
        ),
        'promoted_products_label_image' => array(
            'variants' => array(
                '' => array(
                    //'lazy' => true,
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(50, 50),
                ),
                'original' => array(
                    'source' => true,
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(200, 200),
                ),
            ),
        ),
        'discounted_products_label_image' => array(
            'variants' => array(
                '' => array(
                    //'lazy' => true,
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(50, 50),
                ),
                'original' => array(
                    'source' => true,
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(200, 200),
                ),
            ),
        ),
    );
    
    public function __construct() {
        parent::__construct();
        
        /**
         * Promoted (P) vs. discounted (D) product constrains in special offers (SO):
         * 
         *  - MANDATORY constrains:
         *      - the same product cannot be both P and D in two active overlaping SOs. That implies 
         *          that P can never became D (even in SO applied by cart price it is not possible)
         *      - combination of the same P and D cannot appear in two active overlaping SOs.  
         * 
         *  - OPTIONAL constrains (making it easier):
         *      - the same D product cannot appear in two active overlaping SOs.
         *      - the same P product cannot appear in two active overlaping SOs.
         * 
         * Special offers applied by cart price are apploied accordint to available part of cart price.
         * Available part of cart price is resolved like cart price minus cart_price_threshold-s
         * of applied special offers which applies by cart price
         * 
         * See also misc/docs/implementationDetails.txt > ŠPECIÁLNE PONUKY and 
         * EshopCart.php > comment block starting "// SPECIAL OFFERS"
         */
        $this->validations = array(
            'slug' => array(
                /*/
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                /*/
            ),   
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
            ),   
            'description' => array(
                /*/
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                /*/
            ),   
            'menu_image' => array(
                /*/
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Vyberte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Vyberte'),
                ),
                array(
                    'rule' => array('uploadData', array('notEmpty' => true)),
                    'message' => __v(__FILE__, 'Vyberte'), 
                    'on' => 'create',
                ),
                /*/
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
            'banner_image' => array(
                /*/
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Vyberte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Vyberte'),
                ),
                array(
                    'rule' => array('uploadData', array('notEmpty' => true)),
                    'message' => __v(__FILE__, 'Vyberte'), 
                    'on' => 'create',
                ),
                /*/
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
            'promoted_products_label_image' => array(
                /*/
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Vyberte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Vyberte'),
                ),
                array(
                    'rule' => array('uploadData', array('notEmpty' => true)),
                    'message' => __v(__FILE__, 'Vyberte'), 
                    'on' => 'create',
                ),
                /*/
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
            'discounted_products_label_image' => array(
                /*/
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Vyberte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Vyberte'),
                ),
                array(
                    'rule' => array('uploadData', array('notEmpty' => true)),
                    'message' => __v(__FILE__, 'Vyberte'), 
                    'on' => 'create',
                ),
                /*/
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
            'discounted_products_price_adjustment' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                array(
                    'rule' => 'discountedProductsPriceAdjustment',
                    'message' => __v(__FILE__, 'Zadajte platnú hodnotu'),
                ),
            ),
            'active_from' => array(
                array(
                    'rule' => 'date',
                    'message' => __v(__FILE__, 'Zadajte platný dátum'),
                ),
            ),
            'active_to' => array(
                array(
                    'rule' => 'date',
                    'message' => __v(__FILE__, 'Zadajte platný dátum'),
                ),
                array(
                    'rule' => 'activeToAfterActiveFrom',
                    'message' => __v(__FILE__, 'End date must be the same or later that start date'),
                ),
            ),     
            'promoted_product_ids' => array(
                array(
                    'rule' => 'promotedProducts',
                    'force' => true,
                ),
            ),
            'cart_price_threshold' => array(
                array(
                    'rule' => 'cartPriceThreshold',
                    'force' => true,
                ),
            ),
            'discounted_product_ids' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                array(
                    'rule' => 'discountedProducts',
                ),
            )
        );
    }
    
    /**
     * Used to validate discounted_products_price_adjustment
     * 
     * @param string $fieldValue
     * 
     * @return boolean
     */
    public function validate_discountedProductsPriceAdjustment($fieldValue) {
        return preg_match('/^(?:[0-9]|[1-9][0-9]+)(?:\.[0-9]+)?%?$/', $fieldValue);
    }
    
    /**
     * Used to validate active_to
     * 
     * @param string $fieldValue
     * @param string $fieldName
     * @param array $data
     * 
     * @return boolean
     */
    public function validate_activeToAfterActiveFrom($fieldValue, $fieldName, $data) {
        // get start date
        $activeFrom = null;
        if (isset($data['active_from'])) {
            $activeFrom = $data['active_from'];
        }
        elseif (!empty($data['id'])) {
            $activeFrom = $this->findFieldBy('active_from', 'id', $data['id']);
        }
        if (
            !empty($activeFrom) 
            && !empty($fieldValue) 
            && $fieldValue < $activeFrom
        ) {
            return false;
        }
        return true;
    }    
    
    /**
     * See phpDoc of EshopSpecialOffer::$validations in constructor
     */
    public function validate_promotedProducts($fieldValue, $fieldName, $data, &$validation) {
        if (
            !empty($data['apply_by'])
            && (
                $data['apply_by'] === 'promoted_products_in_cart'
                || $data['apply_by'] === 'promoted_products_cart_price_threshold'
            )
        ) {
            if (empty($fieldValue)) {                
                $validation['message'] = __v(__FILE__, 'Zadajte');
                return false;
            }
            $productIds = $this->unserializeIds($fieldValue);
            $overlapingOptions = $this->getOverlapingFindOptions(
                Sanitize::value($data['id']), 
                Sanitize::value($data['active_from']), 
                Sanitize::value($data['active_to'])
            );
            // the same product cannot be used as promoted in two active special offers
            // (this is an optional constrain to make it easier)
            $this->loadModel('EshopSpecialOfferPromotedProduct');
            $PromotedProduct = new EshopSpecialOfferPromotedProduct();
            $conditions = $overlapingOptions['conditions'];
            $conditions['EshopSpecialOfferPromotedProduct.run_eshop_products_id']
                = $productIds;
            $conflicts = $PromotedProduct->find(array(
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopSpecialOffer',
                    ),
                    array(
                        'type' => 'left',
                        'model' => 'EshopProduct',
                    ),
                ),
                'conditions' => $conditions,
                'fields' => array(
                    'EshopProduct.name AS product', 
                    'EshopSpecialOffer.name AS offer'
                ),
                'order' => array('EshopProduct.id' => $productIds),
                'literals' => $overlapingOptions['literals'],
            ));
            if ($conflicts) {
                $conflictsHtml = '';
                foreach ($conflicts as $conflict) {
                    $conflictsHtml .= __v(
                        __FILE__, 
                        '<li>produkt "%s" je už v ponuke "%s"</li>',
                        $conflict['product'],
                        $conflict['offer']
                    );
                }
                $validation['message'] = __v(
                    __FILE__, 
                    'Nasledovné produkty sú už pridané ako promované produkty v iných aktívnych špeciálnych ponukách:<ul>%s</ul>',
                    $conflictsHtml
                );
                return false;
            }
            // promoted products cannot be used as discounted products in other
            // active special offers
            $this->loadModel('EshopSpecialOfferDiscountedProduct');
            $DiscountedProduct = new EshopSpecialOfferDiscountedProduct();
            $conditions = $overlapingOptions['conditions'];
            $conditions['EshopSpecialOfferDiscountedProduct.run_eshop_products_id']
                = $productIds;
            $conflicts = $DiscountedProduct->find(array(
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopSpecialOffer',
                    ),
                    array(
                        'type' => 'left',
                        'model' => 'EshopProduct',
                    ),
                ),
                'conditions' => $conditions,
                'fields' => array(
                    'EshopProduct.name AS product', 
                    'EshopSpecialOffer.name AS offer'
                ),
                'order' => array('EshopProduct.id' => $productIds),
                'literals' => $overlapingOptions['literals'],
            ));
            if ($conflicts) {
                $conflictsHtml = '';
                foreach ($conflicts as $conflict) {
                    $conflictsHtml .= __v(
                        __FILE__, 
                        '<li>produkt "%s" je už v ponuke "%s"</li>',
                        $conflict['product'],
                        $conflict['offer']
                    );
                }
                $validation['message'] = __v(
                    __FILE__, 
                    'Nasledovné produkty sú už pridané ako produkty so špecialnou zľavou v iných aktívnych špeciálnych ponukách:<ul>%s</ul>',
                    $conflictsHtml
                );
                return false;
            }
            // the same combination of promoted product and discounted product cannot 
            // be used in other active special offers
            if (!empty($data['discounted_product_ids'])) {
                $discountedIds = $this->unserializeIds($data['discounted_product_ids']);
                $conditions = $overlapingOptions['conditions'];
                $conditions['EshopSpecialOfferPromotedProduct.run_eshop_products_id']
                    = $productIds;
                $conditions['EshopSpecialOffer.apply_by'] = 'promoted_products_in_cart';
                $conditions['EshopSpecialOfferDiscountedProduct.run_eshop_products_id']
                    = $discountedIds;
                $conflicts = $this->find(array(
                    'joins' => array(
                        array(
                            'type' => 'left',
                            'model' => 'EshopSpecialOfferPromotedProduct',
                        ),
                        array(
                            'type' => 'left',
                            'model' => 'EshopProduct',
                            'alias' => 'EshopPromotedProduct',
                            'toModel' => 'EshopSpecialOfferPromotedProduct'
                        ),
                        array(
                            'type' => 'left',
                            'model' => 'EshopSpecialOfferDiscountedProduct',
                        ),
                        array(
                            'type' => 'left',
                            'model' => 'EshopProduct',
                            'alias' => 'EshopDiscountedProduct',
                            'toModel' => 'EshopSpecialOfferDiscountedProduct',
                        ),
                    ),
                    'conditions' => $conditions,
                    'fields' => array(
                        'EshopPromotedProduct.name AS promoted_product', 
                        'EshopDiscountedProduct.name AS discounted_product', 
                        'EshopSpecialOffer.name AS offer'
                    ),
                    'order' => array('EshopPromotedProduct.id' => $productIds),
                    'literals' => $overlapingOptions['literals'],
                ));
                if ($conflicts) {
                    $conflictsHtml = '';
                    foreach ($conflicts as $conflict) {
                        $conflictsHtml .= __v(
                            __FILE__, 
                            '<li>Promovaný produkt "%s" a zľavnený produkt "%s" sú už v ponuke "%s"</li>',
                            $conflict['promoted_product'],
                            $conflict['discounted_product'],
                            $conflict['offer']
                        );
                    }
                    $validation['message'] = __v(
                        __FILE__, 
                        'Nasledovné kombinácie promovaných a zľavnených produktov sú už v iných aktívnych špeciálnych ponukách:<ul>%s</ul>',
                        $conflictsHtml
                    );
                    return false;
                }
            }
            
        }
        return true;
    }
    
    public function validate_cartPriceThreshold($fieldValue, $fieldName, $data, &$validation) {
        if (
            !empty($data['apply_by'])
            && (
                $data['apply_by'] === 'cart_price_threshold'
                || $data['apply_by'] === 'promoted_products_cart_price_threshold'
            )
        ) {
            if (empty($fieldValue)) {
                $validation['message'] = __v(__FILE__, 'Zadajte');
                return false;
            }
            if (!Validate::number($fieldValue)) {
                $validation['message'] = __v(__FILE__, 'Zadajte číslo');
                return false;
            }
            if ($fieldValue <= 0) {
                $validation['message'] = __v(__FILE__, 'Zadajte číslo väčšie ako 0');
                return false;
            }
        }
        return true;
    }
    
    /**
     * See phpDoc of EshopSpecialOffer::$validations in constructor
     */
    public function validate_discountedProducts($fieldValue, $fieldName, $data, &$validation) {
        $productIds = $this->unserializeIds($fieldValue);
        $overlapingOptions = $this->getOverlapingFindOptions(
            Sanitize::value($data['id']), 
            Sanitize::value($data['active_from']), 
            Sanitize::value($data['active_to'])
        );
        // the same product cannot be used as discounted in two active special offers
        // (this is an optional constrain to make it easier)
        $this->loadModel('EshopSpecialOfferDiscountedProduct');
        $DiscountedProduct = new EshopSpecialOfferDiscountedProduct();
        $conditions = $overlapingOptions['conditions'];
        $conditions['EshopSpecialOfferDiscountedProduct.run_eshop_products_id']
            = $productIds;
        $conflicts = $DiscountedProduct->find(array(
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'EshopSpecialOffer',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopProduct',
                ),
            ),
            'conditions' => $conditions,
            'fields' => array(
                'EshopProduct.name AS product', 
                'EshopSpecialOffer.name AS offer'
            ),
            'order' => array('EshopProduct.id' => $productIds),
            'literals' => $overlapingOptions['literals'],
        ));
        if ($conflicts) {
            $conflictsHtml = '';
            foreach ($conflicts as $conflict) {
                $conflictsHtml .= __v(
                    __FILE__, 
                    '<li>produkt "%s" je už v ponuke "%s"</li>',
                    $conflict['product'],
                    $conflict['offer']
                );
            }
            $validation['message'] = __v(
                __FILE__, 
                'Nasledovné produkty sú už pridané ako produkty so špecialnou zľavou v iných aktívnych špeciálnych ponukách:<ul>%s</ul>',
                $conflictsHtml
            );
            return false;
        }
        // discounted products cannot be used as promoted products in other
        // active special offers
        $this->loadModel('EshopSpecialOfferPromotedProduct');
        $PromotedProduct = new EshopSpecialOfferPromotedProduct();
        $conditions = $overlapingOptions['conditions'];
        $conditions['EshopSpecialOfferPromotedProduct.run_eshop_products_id']
            = $productIds;
        $conditions['EshopSpecialOffer.apply_by'] = 'promoted_products_in_cart';
        $conflicts = $PromotedProduct->find(array(
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'EshopSpecialOffer',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopProduct',
                ),
            ),
            'conditions' => $conditions,
            'fields' => array(
                'EshopProduct.name AS product', 
                'EshopSpecialOffer.name AS offer'
            ),
            'order' => array('EshopProduct.id' => $productIds),
            'literals' => $overlapingOptions['literals'],
        ));
        if ($conflicts) {
            $conflictsHtml = '';
            foreach ($conflicts as $conflict) {
                $conflictsHtml .= __v(
                    __FILE__, 
                    '<li>produkt "%s" je už v ponuke "%s"</li>',
                    $conflict['product'],
                    $conflict['offer']
                );
            }
            $validation['message'] = __v(
                __FILE__, 
                'Nasledovné produkty sú už pridané ako promované produkty v iných aktívnych špeciálnych ponukách:<ul>%s</ul>',
                $conflictsHtml
            );
            return false;
        }
        return true;
    }
    
    /**
     * 
     * @param type $data
     * @param type $options
     */
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        
        if (!empty($data['discounted_products_price_adjustment'])) {
            $data['discounted_products_price_adjustment'] = 
                str_replace(',', '.', preg_replace('/\s+/', '', $data['discounted_products_price_adjustment']));
        }
        
        if (!empty($data['active_from'])) {
            $data['active_from'] = Date::format($data['active_from'], 'Y-m-d');
        }
        
        if (!empty($data['active_to'])) {
            $data['active_to'] = Date::format($data['active_to'], 'Y-m-d');
        }
        
        if (array_key_exists('promoted_product_ids', $data)) {
            $data['promoted_product_ids'] = self::unserializeIds($data['promoted_product_ids']);
        }
        
        if (array_key_exists('discounted_product_ids', $data)) {
            $data['discounted_product_ids'] = self::unserializeIds($data['discounted_product_ids']);
        }
        
        // pre-populate promoted_products_label_text by name on creation
        if (
            $options['on'] === 'create'
            && empty($data['promoted_products_label_text'])
        ) {
            $data['promoted_products_label_text'] = empty($data['name']) ? '' : $data['name'];
        }
        
        // pre-populate discounted_products_label_text by name on creation
        if (
            $options['on'] === 'create'
            && empty($data['discounted_products_label_text'])
        ) {
            $data['discounted_products_label_text'] = empty($data['name']) ? '' : $data['name'];
        }
        
        // pre-populate seo_title by name on creation
        if (
            $options['on'] === 'create'
            && empty($data['seo_title'])
        ) {
            $data['seo_title'] = empty($data['name']) ? '' : $data['name'];
        }
        
        // pre-populate slug by slugized name on creation
        if (
            $options['on'] === 'create'
            && empty($data['slug'])
        ) {
            $data['slug'] = empty($data['name']) ? '' : Str::slugize($data['name']);
        }
        
        // if slug is provided then force it to be unique and slugize it
        if (!empty($data['slug'])) {
            $data['slug'] = $this->getUniqueSlug($data['slug'], array(
                'id' => empty($data['id']) ? null : $data['id'],
                'lang' => empty($data['lang']) ? null : $data['lang'],
            ));
        }
        
        // check for obfuscate autodetection
        if (isset($data['description'])) {
            $data['obfuscate_description'] = Validate::stringWithEmail($data['description']);
        }
        
        return parent::normalize($data, $options);
    }
    
    /**
     * Userializes list of "hasMany" ids
     * 
     * @param string|array $ids String list of ids. If provided as array then returned 
     *      without any change.
     * 
     * @return array
     */
    public static function unserializeIds($ids) {
        if (!is_array($ids)) {
            $ids = Str::explode(';', $ids);
        }
        $ids = array_filter(array_map('trim', $ids));
        return $ids;
    }
    
    /**
     * Returns ids of promoted products for specified special offer
     * 
     * @param int $id Special offer id
     * 
     * @return array List of specified offer promoted products ids
     */
    public function findPromotedProductIds($id) {
        $this->loadModel('EshopSpecialOfferPromotedProduct');
        $PromotedProduct = new EshopSpecialOfferPromotedProduct();
        return array_values($PromotedProduct->findList(array(
            'fields' => array('run_eshop_products_id'),
            'conditions' => array(
                'run_eshop_special_offers_id' => $id,
            ),
        )));
    }
    
    /**
     * Returns ids of discounted products for specified special offer
     * 
     * @param int $id Special offer id
     * 
     * @return array List of specified offer discounted products ids
     */
    public function findDiscountedProductIds($id) {
        $this->loadModel('EshopSpecialOfferDiscountedProduct');
        $DiscountedProduct = new EshopSpecialOfferDiscountedProduct();
        return array_values($DiscountedProduct->findList(array(
            'fields' => array('run_eshop_products_id'),
            'conditions' => array(
                'run_eshop_special_offers_id' => $id,
            ),
        )));
    }
    
    /**
     * 
     * @param int $id
     * @param array $options
     * @return array
     */
    public function findAll($id, $options = array()) {
        $data = $this->findFirstBy('EshopSpecialOffer.id', $id, $options);
        if (empty($data)) {
            return $data;
        }
        $data['promoted_product_ids'] = $this->findPromotedProductIds($id);
        $data['discounted_product_ids'] = $this->findDiscountedProductIds($id);
        return $data;
    }
    
    /**
     * 
     * @param array $data
     * @param array $options Model::save() options, 'lang' is probably the only used
     * 
     * @return boolean
     * @throws Exception
     */
    public function saveAll($data, $options = array()) {
        // set 'on' and  'alternative' options 
        $options['on'] = null; // to make Model::isCreation() resolve correctly
        $options['on'] = ($this->isCreation($data, $options)) ? 'create' : 'update';
        $options['alternative'] = 'backend';
        
        try {
            $this->reserveTables('EshopSpecialOffer_saveAll', array(
                'EshopSpecialOffer',
                'EshopSpecialOfferPromotedProduct',
                'EshopSpecialOfferDiscountedProduct',
            ), array('tries' => 20, 'retryTime' => 1000));
            
            // normalize and turn normalization off for further processing
            $data = $this->normalize($data, $options);
            $options['normalize'] = false;

            // validate and turn validation off for further processing
            if (!$this->validate($data, $options)) {
                $this->unreserveTables('EshopSpecialOffer_saveAll');
                return false;
            }
            $options['validate'] = false;
            
            // save
            DB::startTransaction('EshopSpecialOffer_saveAll');
            $result = $this->save($data, $options);
            if (!$result) {
                DB::rollbackTransaction('EshopSpecialOffer_saveAll');
                $this->unreserveTables('EshopSpecialOffer_saveAll');
                return false;
            }
            $data = $result;
            $offerId = $this->getPropertyId();
            // create batch for "hasMany" related items
            $delete = array();
            $create = array();
            // save promoted_product_ids
            if (isset($data['promoted_product_ids'])) {
                $delete['EshopSpecialOfferPromotedProduct'] = array(
                    'run_eshop_special_offers_id' => $offerId,
                );
                foreach ($data['promoted_product_ids'] as $productId) {
                    $create['EshopSpecialOfferPromotedProduct'][] = array(
                        'run_eshop_special_offers_id' => $offerId,
                        'run_eshop_products_id' => $productId,
                    );
                }
            }
            // save discounted_product_ids
            if (isset($data['discounted_product_ids'])) {
                $delete['EshopSpecialOfferDiscountedProduct'] = array(
                    'run_eshop_special_offers_id' => $offerId,
                );
                foreach ($data['discounted_product_ids'] as $productId) {
                    $create['EshopSpecialOfferDiscountedProduct'][] = array(
                        'run_eshop_special_offers_id' => $offerId,
                        'run_eshop_products_id' => $productId,
                    );
                }
            }
            $batch = array();
            if ($delete) {
                $batch['delete'] = &$delete;
            }
            if ($create) {
                $batch['create'] = &$create;
            }
            if (!empty($batch)) {
                $this->saveBatch($batch, array('reserve' => false));
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopSpecialOffer_saveAll');
            $this->unreserveTables('EshopSpecialOffer_saveAll');
            throw $e;
        }
        
        DB::commitTransaction('EshopSpecialOffer_saveAll');
        $this->unreserveTables('EshopSpecialOffer_saveAll');
         
        return $data;
    }
    
    /**
     * 
     * @param int $id
     * @param array $options Following are awailable:
     *      - 'notify' (boolean) If TRUE then an event notification is sent to all 
     *          event notifications recipients. Defaults to FALSE.
     * 
     * @return boolean
     * @throws Exception
     */
    public function delete($options = array()) {        
        try {
            $this->reserveTables('EshopSpecialOffer_deleteAll', array(
                'EshopSpecialOffer',
                'EshopSpecialOfferPromotedProduct',
                'EshopSpecialOfferDiscountedProduct',
            ), array('tries' => 20, 'retryTime' => 1000));
            DB::startTransaction('EshopSpecialOffer_deleteAll');
            $findOptions = $options;
            $findOptions['key'] = 'EshopSpecialOffer.id';
            $findOptions['fields'] = 'EshopSpecialOffer.id';
            $ids = $this->findList($findOptions);
            parent::delete($options);
            $this->loadModel('EshopSpecialOfferPromotedProduct');
            $PromotedProduct = new EshopSpecialOfferPromotedProduct();
            $PromotedProduct->deleteBy('run_eshop_special_offers_id', $ids);
            $this->loadModel('EshopSpecialOfferDiscountedProduct');
            $DiscountedProduct = new EshopSpecialOfferDiscountedProduct();
            $DiscountedProduct->deleteBy('run_eshop_special_offers_id', $ids);
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopSpecialOffer_deleteAll');
            $this->unreserveTables('EshopSpecialOffer_deleteAll');
            throw $e;
        }
        DB::commitTransaction('EshopSpecialOffer_deleteAll');
        $this->unreserveTables('EshopSpecialOffer_deleteAll');
        
        return true;
    }
    
    /**
     * Returns find options to find all actually active special offers.
     * Returned array contains items 'conditions', 'order', 'literals' > 'conditions',
     * and 'literals' > 'order'
     * 
     * @param array $options Following are available:
     *      - 'ordered' (bool) If TRUE then  internal ordering rules are set. Defaults to FALSE.
     *      - 'published' (bool) If TRUE then find option for published offers are returned.
     *          Under 'published' are meant active offers having active_from greater
     *          than actual date. Defaults to FALSE, it means that only really active
     *          offers (at this date) are returned.
     * 
     * @return array The above described find options array
     */
    public function getActiveFindOptions($options = array()) {
        $defaults = array(
            'published' => false,
            'ordered' => false,
        );
        $options = array_merge($defaults, $options);
        $conditions = array(
            'EshopSpecialOffer.active' => true,
            array(
                'EshopSpecialOffer.active_to' => null,
                'OR',
                'DATEDIFF(NOW(),`EshopSpecialOffer`.`active_to`) <= 0',
            ),
        );
        $literals = array(
            'conditions' => array(
                'DATEDIFF(NOW(),`EshopSpecialOffer`.`active_to`) <= 0',
            ),
        );
        if (!$options['published']) {
            $conditions[] = array(
                'EshopSpecialOffer.active_from' => null,
                'OR',
                'DATEDIFF(NOW(),`EshopSpecialOffer`.`active_from`) >= 0',
            );
            $literals['conditions'][] = 'DATEDIFF(NOW(),`EshopSpecialOffer`.`active_from`) >= 0';
        }
        $order = null;
        if ($options['ordered']) {
            $order = array(
                'IF(active_to IS NULL, 1, 0) ASC',
                'active_to ASC',
                'id DESC',
            );
            $literals['order'] = array(
                'IF(active_to IS NULL, 1, 0) ASC',
            );
        }
        return array(
            'conditions' => $conditions,
            'order' => $order,
            'literals' => $literals,
        );
    }
    
    /**
     * Returns find options to find all actually active special offers overlaping
     * with specified $activeFrom and $activeTo date.
     * Returned array contains items 'conditions' and 'literals' > 'conditions'
     * 
     * @param int $id Offer id or empty value in case of new created offer
     * @param string $activeFrom Offer active_from or empty value if not specified for offer
     * @param string $activeTo Offer active_to or empty value if not specified for offer
     * 
     * @return array The above described find options array
     */
    protected function getOverlapingFindOptions($id, $activeFrom, $activeTo) {
        $findOptions = $this->getActiveFindOptions(array(
            'published' => true,
        ));
        $conditions = $findOptions['conditions'];
        $literals = $findOptions['literals'];
        if ($id) {
            $conditions['EshopSpecialOffer.id !='] = $id;
        }
        if ($activeFrom) {
            $conditions [] = array(
                'EshopSpecialOffer.active_to' => null,
                'OR',
                'EshopSpecialOffer.active_to >=' => Date::format($activeFrom, 'Y-m-d'),
            );
            
        }
        if ($activeTo) {
            $conditions [] = array(
                'EshopSpecialOffer.active_from' => null,
                'OR',
                'EshopSpecialOffer.active_from <=' => Date::format($activeTo, 'Y-m-d'),
            );            
        }
        return array(
            'conditions' => $conditions,
            'literals' => $literals,
        );
    }
        
    /**
     * Returns list of all active special offers. 
     * Offers ids are used as list keys.
     * Each returned item contains all fields of EshopSpecialOffer plus following virtual
     * fields:
     *  - 'promoted_product_ids' containing array of offer promoted product ids
     *  - 'discounted_product_ids' containing array of offer discounted product ids
     *  - 'matched_promoted_product_ids' containing array of offer promoted product ids matching
     *      with provided ones in options 'promotedProductIds'
     *  - 'matched_discounted_product_ids' containing array of offer discounted product ids matching
     *      with provided ones in options 'discountedProductIds'
     * 
     * NOTE: 'promotedProductIds', 'cartPrice' and 'discountedProductIds' are
     * applied together in following way:
     * 
     *      (
     *          {atLeastOneOfPromotedProductIdsInActiveOffersAppliedByPromotedProductsInCart} 
     *          OR 
     *          {atLeastOneOfPromotedProductIdsInActiveOffersAppliedByPromotedProductsCartPrice} 
     *          AND {thresholdLowerOrEqualToCartPriceOfCorrespondingOffer} 
     *          OR
     *          {thresholdLowerOrEqualToTotalCartPrice}
     *      ) 
     *      AND {atLeastOneOfDiscountedProductIdsInAnyActiveOffer}
     * 
     * @param array $options Following are available:
     *      - 'promotedProductIds' (int|array) Single promoted product id or an array
     *          of such ids. If provided then only active special offers, which apply
     *          for at least one of specified promoted products, are returned. Defaults 
     *          to NULL it means no conditions for promoted products are created.
     *      - 'cartPrice' (bool|float|array) If TRUE then all active offers applied 
     *          by cart price are included in results. If float cart price (free 
     *          part of total cart price) then only active special offers which apply to provided
     *          free cart price are returned. If an array then it is considered for array of
     *          float prices containing:
     *              1] Whole cart free price part under key 'total',
     *              2] Free price parts of special offers applied by promoted products cart price.
     *              In this case each of prices is stored under id of corresponding special offer
     *              (it means there are some promoted products of that special offer placed in cart).
     *              E.g.:
     * 
     *                  array(
     *                      'total' => 45.80,   // free part of cart price total
     *                      3 => 15,            // free part of price of promoted products placed in cart which belongs to SO with id 3
     *                      7 => 12.40,         // free part of price of promoted products placed in cart which belongs to SO with id 7
     *                  )
     * 
     *          Defaults to NULL it means no conditions for cart price are created.
     *      - 'discountedProductIds' (int|array) Single discounted product id or an array
     *          of such ids. If provided then only active special offers, which apply
     *          for at least one of specified discounted products, are returned. 
     *          Defaults to NULL it means no conditions for discounted products are created.
     *      - 'ordered' (bool) If TRUE then special offers are ordered according
     *          internal ordering rules. Defaults to FALSE.
     *      - 'published' (bool) If TRUE then all published offers are returned.
     *          Under 'published' are meant active offers having active_from greater
     *          than actual date. Defaults to FALSE, it means that only really active
     *          offers (at this date) are returned.
     *      - 'getDetails' (bool) Defaults to FALSE.
     *      - 'conditions' (array) Model find conditions to add more constrains on
     *          retrieved special offers. Defaults to NULL.
     * 
     * @return array
     */
    public function getActive($options = array()) {
        $defaults = array(
            'promotedProductIds' => null,
            'cartPrice' => null,
            'discountedProductIds' => null,
            'getDetails' => false,
            'conditions' => null,
        );
        $options = array_merge($defaults, $options);
        // normalize cart price "single float case" to "array case"
        if (
            !empty($options['cartPrice'])
            && $options['cartPrice'] !== true
            && !is_array($options['cartPrice'])
        ) {
            $options['cartPrice'] = array('total' => $options['cartPrice']);
        }
        $activeFindOptions = $this->getActiveFindOptions($options);
        $activeConditions = $activeFindOptions['conditions'];
        $order = $activeFindOptions['order'];
        $literals = $activeFindOptions['literals'];
        $promotedConditions = array();
        if (isset($options['promotedProductIds'])) {
            $offerIds = array();
            if (!empty($options['promotedProductIds'])) {
                $conditions = $activeConditions;
                $conditions['EshopSpecialOffer.apply_by'] = array(
                    'promoted_products_in_cart',
                );
                $conditions['EshopSpecialOfferPromotedProduct.run_eshop_products_id'] = 
                    $options['promotedProductIds'];
                $offerIds = $this->findList(array(
                    'joins' => array(
                        array(
                            'type' => 'left',
                            'model' => 'EshopSpecialOfferPromotedProduct',
                        )
                    ),
                    'key' => 'EshopSpecialOffer.id',
                    'fields' => 'EshopSpecialOffer.id',
                    'conditions' => $conditions,
                    'literals' => $literals,
                ));
            }
            if ($offerIds) {
                $promotedConditions['EshopSpecialOffer.id'] = $offerIds;
            }
        }
        $promotedCartPriceConditions = array();
        if (isset($options['promotedProductIds'])) {
            $offerIds = array();
            if (!empty($options['promotedProductIds'])) {
                $conditions = $activeConditions;
                $conditions['EshopSpecialOffer.apply_by'] = array(
                    'promoted_products_cart_price_threshold',
                );
                $conditions['EshopSpecialOfferPromotedProduct.run_eshop_products_id'] = 
                    $options['promotedProductIds'];
                $offerIds = $this->findList(array(
                    'joins' => array(
                        array(
                            'type' => 'left',
                            'model' => 'EshopSpecialOfferPromotedProduct',
                        )
                    ),
                    'key' => 'EshopSpecialOffer.id',
                    'fields' => 'EshopSpecialOffer.id',
                    'conditions' => $conditions,
                    'literals' => $literals,
                ));
            }
            if ($offerIds) {
                if (
                    !empty($options['cartPrice'])
                    && $options['cartPrice'] !== true // => is array
                ) {
                    foreach ($options['cartPrice'] as $offerId => $cartPrice) {
                        if (!isset($offerIds[$offerId])) {
                            continue;
                        }
                        if (!empty($promotedCartPriceConditions)) {
                            $promotedCartPriceConditions[] = 'OR';
                        }
                        // consider only thresholds for offers applied by promoted products
                        // cart price (so skip the total which applies to offers applied by 
                        // cart price threshold)
                        if ($offerId !== 'total') {
                            // normally this should be (it gives a sense) an offer applied by promoted_products_cart_price_threshold
                            // but no explicit condition is introduced here here
                            // (use nested form to avoid rewriting of 'EshopSpecialOffer.cart_price_threshold <=')
                            $promotedCartPriceConditions[] = array(
                                'EshopSpecialOffer.id' => $offerId,
                                'EshopSpecialOffer.cart_price_threshold <=' => $cartPrice,
                            );
                        }
                    }
                }
                else {
                    $promotedCartPriceConditions['EshopSpecialOffer.id'] = $offerIds;
                }
            }
        }
        elseif (
            !empty($options['cartPrice'])
            && $options['cartPrice'] !== true // => is array
        ) {
            foreach ($options['cartPrice'] as $offerId => $cartPrice) {
                if (!empty($promotedCartPriceConditions)) {
                    $promotedCartPriceConditions[] = 'OR';
                }
                // consider only thresholds for offers applied by promoted products
                // cart price (so skip the total which applies to offers applied by 
                // cart price threshold)
                if ($offerId !== 'total') {
                    // normally this should be (it gives a sense) an offer applied by promoted_products_cart_price_threshold
                    // but no explicit condition is introduced here here
                    // (use nested form to avoid rewriting of 'EshopSpecialOffer.cart_price_threshold <=')
                    $promotedCartPriceConditions[] = array(
                        'EshopSpecialOffer.id' => $offerId,
                        'EshopSpecialOffer.cart_price_threshold <=' => $cartPrice,
                    );
                }
            }
        }
        $cartPriceConditions = array();
        if (!empty($options['cartPrice'])) {
            if ($options['cartPrice'] === true) {
                $cartPriceConditions['EshopSpecialOffer.apply_by'] = 'cart_price_threshold';
            }
            elseif (isset($options['cartPrice']['total'])) {
                $cartPriceConditions = array(
                    'EshopSpecialOffer.apply_by' => 'cart_price_threshold',
                    'EshopSpecialOffer.cart_price_threshold <=' => $options['cartPrice']['total'],
                );
            }
        }
        $dicountedConditions = array();
        if (isset($options['discountedProductIds'])) {
            $offerIds = array();
            if (!empty($options['discountedProductIds'])) {
                $conditions = $activeConditions;
                $conditions['EshopSpecialOfferDiscountedProduct.run_eshop_products_id'] = 
                    $options['discountedProductIds'];
                $offerIds = $this->findList(array(
                    'joins' => array(
                        array(
                            'type' => 'left',
                            'model' => 'EshopSpecialOfferDiscountedProduct',
                        )
                    ),
                    'key' => 'EshopSpecialOffer.id',
                    'fields' => 'EshopSpecialOffer.id',
                    'conditions' => $conditions,
                    'literals' => $literals,
                ));
            }
            if ($offerIds) {
                $dicountedConditions['EshopSpecialOffer.id'] = $offerIds;
            }
            // if no offers found for discountedProductIds then return empty array
            else {
                return array();
            }
        }
        // put partial conditions together
        $conditions = $activeConditions;
        $mergedPromotedConditions = array();
        foreach (
            array(
                $promotedConditions,
                $promotedCartPriceConditions,
                $cartPriceConditions,
            ) as $c
        ) {
            if (!empty($c)) {
                if (!empty($mergedPromotedConditions)) {
                    $mergedPromotedConditions[] = 'OR';
                }
                $mergedPromotedConditions[] = $c;
            }
        }
        if (!empty($mergedPromotedConditions)) {
            $conditions[] = $mergedPromotedConditions;
        }
        if (!empty($dicountedConditions)) {
            $conditions[] = $dicountedConditions;
        }
        if (!empty($options['conditions'])) {
            $conditions[] = $options['conditions'];
        }
        $offers = $this->findList(array(
            'key' => 'EshopSpecialOffer.id',
            'conditions' => $conditions,
            'order' => $order,
            'literals' => $literals,
        ));
        if (empty($offers)) {
            return array();
        }
        // load promoted_product_ids, discounted_product_ids
        // matched_promoted_product_ids and matched_discounted_product_ids
        // set also file fields url paths if required
        $fileFields = array_keys($this->fileFields);
        foreach ($offers as &$offer) {
            $offer['promoted_product_ids'] = array();
            $offer['discounted_product_ids'] = array();
            $offer['matched_promoted_product_ids'] = array();
            $offer['matched_discounted_product_ids'] = array();
            if ($options['getDetails']) {
                foreach ($fileFields as $fileField) {
                    if (!empty($offer[$fileField])) {
                        $offer[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                            'file' => $offer[$fileField]
                        ));
                    }
                }
            }
        }
        unset($offer);
        // - promoted_product_ids
        $this->loadModel('EshopSpecialOfferPromotedProduct');
        $PromotedProduct = new EshopSpecialOfferPromotedProduct();
        $promotedProducts = $PromotedProduct->findList(array(
            'key' => 'run_eshop_products_id',
            'fields' => array('run_eshop_special_offers_id'),
            'conditions' => array(
                'run_eshop_special_offers_id' => array_keys($offers),
            ),
            'order' => 'id ASC',
            'accumulate' => true,
        ));
        foreach($promotedProducts as $productId => $offerIds) {
            foreach ($offerIds as $offerId) {
                $offers[$offerId]['promoted_product_ids'][] = $productId;
            }
        }
        // - discounted_product_ids
        $this->loadModel('EshopSpecialOfferDiscountedProduct');
        $DiscountedProduct = new EshopSpecialOfferDiscountedProduct();
        $discountedProducts = $DiscountedProduct->findList(array(
            'key' => 'run_eshop_products_id',
            'fields' => array('run_eshop_special_offers_id'),
            'conditions' => array(
                'run_eshop_special_offers_id' => array_keys($offers),
            ),
            'order' => 'id ASC',
            'accumulate' => true,
        ));
        foreach($discountedProducts as $productId => $offerIds) {
            foreach ($offerIds as $offerId) {
                $offers[$offerId]['discounted_product_ids'][] = $productId;
            }
        }
        // - matched_promoted_product_ids and matched_discounted_product_ids
        $options['promotedProductIds'] = (array)$options['promotedProductIds'];
        $options['discountedProductIds'] = (array)$options['discountedProductIds'];
        foreach ($offers as &$offer) {
            if (!empty($options['promotedProductIds'])) {                        
                $offer['matched_promoted_product_ids'] = array_values(array_intersect(
                    $offer['promoted_product_ids'], $options['promotedProductIds']
                ));
            }
            if (!empty($options['discountedProductIds'])) {                        
                $offer['matched_discounted_product_ids'] = array_values(array_intersect(
                    $offer['discounted_product_ids'], $options['discountedProductIds']
                ));
            }
        }
        unset($offer);
        return $offers;
    }
    
    /**
     * Returns adjusted price
     * 
     * @param float $price
     * @param string $priceAdjustment
     * @return float
     */
    static public function applyPriceAdjustment($price, $priceAdjustment) {
        if (substr($priceAdjustment, -1) === '%') {
            $priceAdjustment = '*' . ((float)rtrim($priceAdjustment, '%') / 100);
        }
        return Number::adjust($price, $priceAdjustment);
    }
}
