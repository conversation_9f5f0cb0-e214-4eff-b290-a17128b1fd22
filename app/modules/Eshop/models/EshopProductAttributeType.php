<?php

class EshopProductAttributeType extends Model {
    protected $table = 'run_eshop_product_attribute_types';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
////mojo: there is no need for pid on dynamic atttributes. If you await existence of attribute
////      in app logic then such attribute must be defined as static in product table     
//        'pid' => array('type' => 'varchar', 'default' => null), 
        'run_eshop_product_types_id' => array('type' => 'int', 'default' => null, 'index' => 'index'),
        'name' => array('type' => 'varchar'),
        'description' => array('type' => 'text', 'default' => null),
        'default_value' => array('type' => 'text', 'default' => null),
        'selective' => array('type' => 'bool', 'default' => 0, 'comment' => 'Is this attribute type selective (list of values separated by semicolon ;)?'),
        'priced' => array('type' => 'bool', 'default' => 0, 'comment' => 'Are values of selective type attribute priced (prices are separated from values by ::)?'),
        'active' => array('type' => 'bool', 'default' => 1),
        'show_in_product_detail' => array('type' => 'bool', 'comment' => 'Some attributes is shown only in product filter on front'),
        'columns_in_filter' => array('type' => 'bool', 'length' => 1, 'comment' => 'Some attributes needs to be broken into more columns in product filter on front'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
    );
    
    protected $translatedFields = array(
        'name',
        'description',
    );
}
