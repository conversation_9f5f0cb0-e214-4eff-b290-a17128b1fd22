<?php

class EshopAccessoryProduct extends Model {
    protected $table = 'run_eshop_accessory_products';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_eshop_products_id' => array('type' => 'int', 'index' => 'index'),
        'run_eshop_accessory_products_id' => array('type' => 'int', 'index' => 'index'),
        'sort' => array('type' => 'int', 'default' => null, 'comment' => 'Weight in ordered group of accessory products'),
        //'description' => array('type' => 'text', 'default' => null, 'comment' => 'Description of relation between two products'),
    );
    
}
