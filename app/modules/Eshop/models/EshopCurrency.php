<?php

class EshopCurrency extends Model {
    
    protected $table = 'run_eshop_currencies';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'code' => array('type' => 'varchar', 'length' => 3, 'comment' => 'Currency ISO 4217 code, e.g. EUR, CZK'),
        'symbol' => array('type' => 'varchar', 'length' => 15, 'comment' => 'Currency symbol, e.g. &euro; Kč)'),
//        'symbol_position' => array('type' => 'emum', '' => array('enum_currency_symbol_before', 'enum_currency_symbol_after'), 'defaults' => 'enum_currency_symbol_after', 'comment' => 'Currency symbol position)'),
        'suffix' => array('type' => 'varchar', 'length' => 15, 'default' => null, 'comment' => 'Suffix used for currency integer prices e.g. ,&ndash;'),
        'decimals' => array('type' => 'tinyint', 'default' => null, 'comment' => 'Number of decimals used for currency float prices'),
        'conversion_rate' => array('type' => 'decimal', 'length' => 14.6, 'default' => null, 'comment' => 'Conversion rate from default currency prices. Keep it NULL for default currency'),
        'conversion_rate_updated_at' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime of last conversion_rate automatic update - see EshopCurrency::getActualConversionRate()'),
        'default' => array('type' => 'bool', 'comment' => 'This is the currency prices are stored in. From default currency it is possible compute prices in another currencies'),
        'sort' => array('type' => 'int'),
    );
    
//    public function __construct() {
//        parent::__construct();
//        
//        $this->validations = array();
//    }
    
    public function normalize($data, $options = array()) {
        
        // normalize conversion_rate
        if (!empty($data['conversion_rate'])) {
            $data['conversion_rate'] = str_replace(',', '.', $data['conversion_rate']);
        }
        
        return parent::normalize($data, $options);
    }
    
    /**
     * Returns actual conversion rate of specified currency relative to default currrency.
     * 
     * ATTENTION: For the moment only conversion rates from Euro (as a default currency)
     * to other currencies are supported. The other default currencies are not implemented
     * yet.
     * 
     * NOTE: If needed there is another webservice to get conversion rates: https://exchangeratesapi.io/
     * 
     * @param string $code Currency ISO 4217 code, e.g. 'CZK', 'EUR'
     * 
     * @return float
     * 
     * @throws Exception on failure
     */
    public function getActualConversionRate($code) {
        $currency = $this->findFirst(array(
            'conditions' => array(
                'code' => $code,
            ),
            'fields' => array(
                'id',
                'conversion_rate',
                'conversion_rate_updated_at',
                'default',
            )
        ));
        if (!$currency) {
            throw new Exception(__e(__FILE__, 'Invalid currency code: %s', $code));
        }
        if ($currency['default']) {
            return 1.00;
        }
        
        // set source specific variables
        $sourceTodayUpdateDatetime = $sourceUrl = $regexTemplate = null;
        $defaultCurrencyCode = strtoupper($this->findFieldBy('code', 'default', true));
        if ($defaultCurrencyCode === 'EUR') {
            // ECB updates euro foreign exchange reference rates round 16:00
            // plus 30 minutes are added here to be sure that new rates are ready
            // see https://www.ecb.europa.eu/stats/policy_and_exchange_rates/euro_reference_exchange_rates/html/index.en.html
            // NOTE: If needed there is another webservice to get conversion rates: https://exchangeratesapi.io/
            $sourceTodayUpdateDatetime = Date::getMySqlDate() . ' 16:30:00';
            $sourceUrl = 'https://www.ecb.europa.eu/stats/eurofxref/eurofxref-daily.xml';
            $regexTemplate = '/<Cube\s+currency=["\']%s["\']\s+rate=["\']([1-9][0-9]*(?:\.[0-9]+)?)["\']\s*\/>/i';
        }
        else {
            throw new Exception(__e(
                __FILE__, 
                'There is defined no source for conversion rates of %s with %s as a default currency.',
                $code,
                $defaultCurrencyCode
            ));
            // ...maybe just the EUR source can be used with inverted rates... ???
        }
        
        $actualDatetime = Date::getMySqlDatetime();
        if (
            !(
                // conversion rate should be updated:
                // - if it is empty
                // - or if the old one was updated before $sourceTodayUpdateDatetime and 
                // the actual datetime is greater than $sourceTodayUpdateDatetime
                // - or if the old one is older than 1 day 
                empty($currency['conversion_rate'])
                ||
                $currency['conversion_rate_updated_at'] <= $sourceTodayUpdateDatetime
                && $actualDatetime > $sourceTodayUpdateDatetime
                ||
                Date::getDiff('d', $currency['conversion_rate_updated_at'], $actualDatetime) >= 1
            )
        ) {
            return (float)$currency['conversion_rate'];
        }
        try {
            $xmlString = App::request($sourceUrl);
        } 
        catch (Exception $e) {
            throw new Exception(__e(
                __FILE__, 
                'Download of XML file from %s has failed with following error: %s',
                $sourceUrl,
                $e->getMessage()  
            ));
        }
        $validationRegex = sprintf($regexTemplate, '[a-z]{3}');
        if (!preg_match($validationRegex, $xmlString)) {
            throw new Exception(__e(
                __FILE__, 
                'XML file from %s has invalid structure',
                $sourceUrl
            ));
        }
        $currencyRegex = sprintf($regexTemplate, preg_quote($code, '/'));
        $match = null;
        if (!preg_match($currencyRegex, $xmlString, $match)) {
            throw new Exception(__e(
                __FILE__, 
                'No conversion rate for %s has been found in XML file from %s',
                $code,
                $sourceUrl
            ));
        }
        $currency['conversion_rate'] = $match[1];
        $currency['conversion_rate_updated_at'] = $actualDatetime;
        unset($currency['default']);
        $this->save($currency, array(
            'normalize' => false,
            'validate' => false,
        ));
        return (float)$currency['conversion_rate'];
    }
}