<?php
class EshopProductImage extends Model {
    
    protected $table = 'run_eshop_product_images';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_eshop_products_id' => array('type' => 'int', 'index' => 'index'),
        'file' => array('type' => 'varchar', 'comment' => 'Image file basename, it means filename and extension'),
        'source' => array('type' => 'varchar', 'comment' => 'There are following possibilities for different file sources: "{path}" in case of copy source, "{urlPath}" in case of download source, "upload {imageName}" in case of upload source, "ftp {imagePath}" in case of FTP source, "{sourcePid} {imageId}" in case of other source (e.g. images loaded from an API). Used to import and/or to reimport after a change in the source path is recognized.'),
        'name' => array('type' => 'varchar', 'default' => null),
        'sort' => array('type' => 'int', 'default' => null),
        'created' => array('type' => 'datetime', 'default' => null),
    );
        
    protected $fileFields = array(
        'file' => array(
            'extension' => 'jpg',
            'quality' => 90,
            'placeholder' => '_placeholder.png',
            'variants' => array(
                'originalThumb' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
//                    'fit' => array(200, 200),
                    'scaleByX' => array(200),
                    'lazy' => true,
                ),
                'tiny' => array(
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(100, 75),
                    'cover' => array(80, 80),
                    'cropInMiddle' => array(80, 80),
//                    'scaleByX' => array(80),
                    'lazy' => true,
                ),
                'small' => array(
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(240, 180),
                    'cover' => array(100, 100),
                    'cropInMiddle' => array(100, 100),
//                    'scaleByX' => array(100),
                    'lazy' => true,
                ),
                'large' => array(
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(400, 300),
//                    'cover' => array(180, 280),
//                    'cropInMiddle' => array(180, 280),
                    'fitX' => array(600),
                    'lazy' => true,
                ),
                'original' => array(
                    'source' => true,
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(1920, 1080),
                ),
            ),
        ),
    );
        
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                ),
            ),
            'file' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Image is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Image is mandatory'),
                ),
                array(
                    'rule' => function($value, $field, $data, &$validation) {
                        $sourceType = File::getSourceType($value);
                        if (
                            $sourceType === File::UPLOAD_SOURCE
                            && !Validate::uploadData($value, array('noErrors' => true))
                        ) {
                            $validation['message'] = __v(__FILE__, 'File upload error has occured');
                            return false;
                        }
                        elseif (
                            $sourceType === File::UPLOAD_SOURCE
                            && !Validate::uploadData($value, array('type' => '/^image\//'))
                        ) {
                            $validation['message'] = __v(__FILE__, 'File is not an image');
                            return false;
                        }
                        elseif (
                            $sourceType === File::COPY_SOURCE
                            && ($mimeType = File::getMimeType(reset($value)))
                            && !preg_match('/^image\//', $mimeType)
                        ) {
                            $validation['message'] = __v(__FILE__, 'File is not an image');
                            return false;
                        }
                        elseif (
                            $sourceType === File::DOWNLOAD_SOURCE
                            && ($mimeType = File::getMimeTypeByExtension(reset($value)))
                            && !preg_match('/^image\//', $mimeType)
                        ) {
                            $validation['message'] = __v(__FILE__, 'File is not an image');
                            return false;
                        }
                        elseif (
                            //$sourceType === File::NOT_SOURCE
                            // only these file sources are allowed
                            $sourceType !== File::UPLOAD_SOURCE
                            && $sourceType !== File::COPY_SOURCE
                            && $sourceType !== File::DOWNLOAD_SOURCE
                        ) {
                            $validation['message'] = __v(__FILE__, 'Invalid file source');
                            return false;
                        }
                        return true;
                    }
                ),
            ),
            'run_eshop_products_id' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Product id is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Product id is mandatory'),
                ),
            ),
        );
    }
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        
        // set missing name
        if (
            empty($data['id'])    
            && empty($data['name'])
            && !empty($data['file'])
        ) {
            $sourceType = File::getSourceType($data['file']);
            if ($sourceType === File::UPLOAD_SOURCE) {
                $data['name'] = File::getPathinfo($data['file']['name'], PATHINFO_FILENAME);
            }
            elseif (
                $sourceType === File::DOWNLOAD_SOURCE
                || $sourceType === File::COPY_SOURCE
            ) {
                $data['name'] = File::getPathinfo(reset($data['file']), PATHINFO_FILENAME);
            }
        }
        
        // set missing source
        if (
            empty($data['id'])    
            && empty($data['source'])
            && !empty($data['file'])
        ) {
            $sourceType = File::getSourceType($data['file']);
            if ($sourceType === File::UPLOAD_SOURCE) {
                $data['source'] = 'upload ' . $data['file']['name'];
            }
            elseif (
                $sourceType === File::DOWNLOAD_SOURCE
                || $sourceType === File::COPY_SOURCE
            ) {
                $data['source'] = reset($data['file']);
            }
        }
        
        return parent::normalize($data, $options);
    }
    
    /**
     * Gets images of specified product.
     * Images variants URL paths are resolved
     * 
     * @param int $productId
     * @return array
     */
    public function getProductImages($productId) {
        $images = $this->find(array(
            'fields' => array(
                'EshopProductImage.id', 
                'EshopProductImage.file', 
                'EshopProductImage.name', 
                'EshopProductImage.sort'
            ),
            'conditions' => array('run_eshop_products_id' => $productId),
            'order' => array('sort ASC'),
        ));
        $fileFields = array_keys($this->fileFields);
        foreach ($images as &$image) {
            // set whole paths to files
            foreach ($fileFields as $fileField) {
                $image[$fileField] = $this->getFileFieldUrlPaths($fileField, array('file' => $image[$fileField]));
            }
        }
        return $images;
    }
    
    /**
     * Adds a new image to specified product
     * 
     * @param int $productId Product id to add new image for
     * @param array $data New image data containing at least 'file' field
     * 
     * @return bool 
     */
    public function add($productId, $data) {
        try {
            if (!$this->addOrderedItem($data, array(
                'groupConditions' => array('run_eshop_products_id' => $productId), 
            ))) {
                return false;
            }
        }   
        catch (Throwable $e) {
            $this->setError($e->getMessage());
            return false;
        }
        return true;
    }
    
    /**
     * Reordes multiple images according to data provided in following form:
     * 
     *      array(
     *          array(                  // image 01
     *              'id' => '...',
     *              'weight' => '...',
     *          ),
     *          array(                  // image 02
     *              'id' => '...',
     *              'weight' => '...',
     *          ),
     *          ...,
     *          
     *      )
     * 
     * @param array $imagesData Reordered images data. Each image array must contain at least 
     *      'id' and 'weight' otherwise it is ignored
     * 
     * @return bool TRUE on success, FALSE on processing errors
     */
    public function reorder($imagesData) {
        // validate and get ids of all moved images
        if (!is_array($imagesData)) {
            $this->setError(__v(__FILE__, 'The $imagesData input must be an array'));
            return false;
        }
        $imageIds = array();
        foreach ($imagesData as $image) {
            if (
                empty($image['weight'])
                || empty($image['id'])
            ) {
                $this->setError(__v(__FILE__, 'Each item of $imagesData array must be an array too and must contain id and weight item'));
            }
            $imageIds[] = $image['id'];
        }
        // get list of all moved images products ids
        $productIds = $this->findList(array(
            'key' => 'id',
            'fields' => 'run_eshop_products_id',
            'conditions' => array(
                'id' => $imageIds,
            ),
        ));
        foreach ($imagesData as $image) {
            $this->moveOrderedItem($image['id'], array(
                'groupConditions' => array('run_eshop_products_id' => $productIds[$image['id']]), 
                'newOrderIndex' => $image['weight']
            ));
        }
        return true;
    }
    
    /**
     * Removes specified image
     * 
     * @param int $id Id of image to be removed
     */
    public function remove($id) {
        // get list of all moved images products ids
        $productId = $this->findField('run_eshop_products_id', array(
            'conditions' => array(
                'id' => $id,
            ),
        ));
        $this->deleteOrderedItem($id, array(
            'groupConditions' => array('run_eshop_products_id' => $productId),
        ));
        return true;
    }
    
    /**
     * Adds a new images to specified product
     * 
     * @param int $productId Product id to add new image for
     * @param array $sources Array of copy or download sources. 
     *      ATTENTION: Sources can be provided as an plain array of paths or URLs
     *      or as an array of arrays containing paths or URLs.
     * @param array $options Following are available:
     *      - 'synchronize' (bool) Defaults to FALSE.
     * 
     * @return array Array of pairs {source} => {result}, where {result} is either
     *      TRUE on success or an array of validation messages on failure.
     */
    public function addFromSources($productId, $sources, $options = array()) {
        $options = array_merge(array(
            'synchronize' => false,
        ), $options);
        
        // normalize and validate sources and grab source paths and hosts
        $sourcePaths = array();
        $sourceHosts = array();
        foreach ($sources as &$source) {
            // validate
            if (
                is_string($source)
                && ($sourceType = File::getSourceType(array($source))) !== File::COPY_SOURCE
                && $sourceType !== File::DOWNLOAD_SOURCE
                ||
                !is_string($source)
                && ($sourceType = File::getSourceType($source)) !== File::COPY_SOURCE
                && $sourceType !== File::DOWNLOAD_SOURCE
            ) {
                throw new Exception(__e(
                    'Invalid source (only copy and download sources and onix enclosures are supported): %s',
                    json_encode($source)
                ));
            }
            // normalize to plain array
            if (is_array($source)) {
                $source = reset($source);
            }
            // grab source paths and hosts
            elseif ($sourceType == File::COPY_SOURCE) {
                $sourcePath = File::getPathinfo($source, PATHINFO_DIRNAME);
                if (!in_array($sourcePath, $sourcePaths)) {
                    $sourcePaths[] = $sourcePath;
                }
            }
            elseif ($sourceType == File::DOWNLOAD_SOURCE) {
                $sourceHost = parse_url($source, PHP_URL_HOST);
                if (!in_array($sourceHost, $sourceHosts)) {
                    $sourceHosts[] = $sourceHost;
                }
            }
        }
        unset($source);
        
        // {id} => {source}
        $existingSources = $this->findList(array(
            'fields' => array('source'),
            'conditions' => array(
                'run_eshop_products_id' => $productId
            ),
        ));

        // synchronize (delete sources which are no more present in products gallery
        // image sources)
        if ($options['synchronize']) {
            $deleteIds = array();
            foreach ($existingSources as $id => $existingSource) {
                // skip present ones
                if (in_array($existingSource, $sources)) {
                    continue;
                }
                if (
                    ($existingSourceType = File::getSourceType(array($existingSource))) == File::COPY_SOURCE
                    && ($existingSourcePath = File::getPathinfo($existingSource, PATHINFO_DIRNAME))
                    && in_array($existingSourcePath, $sourcePaths)
                    ||
                    $existingSourceType == File::DOWNLOAD_SOURCE
                    && ($existingSourceHost = parse_url($existingSource, PHP_URL_HOST))
                    && in_array($existingSourceHost, $sourceHosts)
                ) {
                    $deleteIds[] = $id;
                }
            }
            if ($deleteIds) {
                $this->deleteBy('id', $deleteIds);
            }
        }
        
        // filter new sources
        $newSources = array_diff($sources, $existingSources);

        // create gallery images one by one (no batch processing)
        $results = array();
        foreach ($newSources as $newSource) {
            try {
                if ($this->add($productId, array(
                    'file' => array($newSource)
                ))) {
                    $results[$newSource] = true;
                }
                else {
                    $results[$newSource] = $this->getErrors();
                }
            } 
            catch (Throwable $e) {
                $results[$newSource] = array($e->getMessage());
            }
        }
        
        return $results;
    }
}