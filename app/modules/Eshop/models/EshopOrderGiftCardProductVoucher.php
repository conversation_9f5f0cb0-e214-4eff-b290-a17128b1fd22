<?php

class EshopOrderGiftCardProductVoucher extends Model {
    protected $table = 'run_eshop_order_gift_card_product_vouchers';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_eshop_order_products_id' => array('type' => 'int', 'index' => 'index', 'comment' => 'Ordered product whose run_eshop_products_id must equal to id of a gift card product (EshopProduct.is_gift_card = 1)'),
        'run_eshop_vouchers_id' => array('type' => 'int', 'index' => 'unique', 'comment' => 'Voucher created for ordered gift card.'),
    );
}
