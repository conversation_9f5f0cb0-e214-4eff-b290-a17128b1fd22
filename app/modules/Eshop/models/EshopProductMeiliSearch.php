<?php

/**
 * Facade class for MeiliSearch from products point of view 
 * 
 * NOTE: Index settings are set by synchronize() method - see there
 * 
 * ATTENTION: If search, filter or sort changes following code must be synchronized:
 * - app/modules/Eshop/views/EshopProducts/index.php > filter select / sort select
 * - EshopProductMeiliSearch::getProductsDetails() - fields which are included in MS search
 * - EshopProductMeiliSearch::updateSettings() - settings of MS search (filter fields, sort fields, typo tolerance, ...)
 * - EshopProduct::getExternalSearchProductIds() - filter and sort translated for MS search
 * - EshopProduct::getSearchFindOptions() - oldschool search (fields & settings)
 * 
 * For initial instalation see app/updates/2024/2024-02-29_svec_meilisearch.txt
 */
class EshopProductMeiliSearch extends ModuleObject {
    
    protected $Product;
    
    protected $MeiliSearch;
        
    public $version;
    
    /**
     * Batch size used when working with large amounts of products
     * 
     * @var int
     */
    protected $batchSize = 10000;
    
    /**
     * @param array $options Following are available:
     *      - 'indexHost' (string) Name of HTTP host the searh index belongs to.
     *          Defaults (if not provided or empty) to $_SERVER['HTTP_HOST']. 
     *          It can be rewriten for debugging.
     */
    public function __construct($options = array()) {
        parent::__construct();
        
        $this->loadModel('EshopProduct');
        $this->Product = new EshopProduct();
        
        App::loadLib('App', 'MeiliSearch');
        $options['apiUrlBase'] = App::getSetting('App', 'meilisearch.apiUrlBase');
        $options['apiKey'] = App::getSetting('App', 'meilisearch.apiKey');
        $this->MeiliSearch = new MeiliSearch(EshopProduct::class, $options);
        $this->version = $this->MeiliSearch->version;
    }

    /**
     * Returns product(s) search details according to provided id(s).
     * This method removes from product detail array all fields which are not used
     * by search to optimize search DB size and minimize search time. See the method
     * body for details.
     * 
     * ATTENTION: Read phpDoc of EshopProductMeiliSearch class.
     * 
     * @param int|array $id Single product id or an array of product ids. If an array 
     *      of ids is passed then returned produts are in the same order as provided ids.
     * @param array $options Options of EshopProduct::getDetails()
     * 
     * @return array List of product(s) detail arrays. Product ids are set as keys
     *      of array. If $id is provided as a single integer then single product record is returned.
     */
    protected function getProductsDetails($id, $options = array()) {
        $options = array_merge(array(
            'getManufacturer' => true,
            'publishedOnly' => true,
        ), $options);
        
        $products = $this->Product->getDetails(
            (array)$id, // force to return always an array of products (even if a single one)
            $options
        );

        $ordersCountsFindOptions = $this->Product->getOrdersCountsFindOptions();
        $ordersCountsFindOptions['conditions'] = array(
            'EshopProduct.id' => $id,
        );
        $ordersCountsFindOptions['key'] = 'EshopProduct.id';
        unset($ordersCountsFindOptions['order']);
        $ordersCounts = $this->Product->findList($ordersCountsFindOptions);
        
        // keep just fields used in search
        foreach ($products as &$product) {
            $onStock = $product['stock'] > 0;
            $isDiscounted = $product['savings_rate'] > 0;
            $product = array_intersect_key($product, array(
                'id' => null,
                'code' => null,
                'name' => null,
                'seo_keywords' => null,
                'price_actual_taxed' => null,
                'is_new' => null,
                'EshopManufacturer' => null,
            ));
            $product['orders_count'] = (int)Sanitize::value($ordersCounts[$product['id']]);
            // ATTENTION: Boolean values are converted to int
            $product['on_stock'] = (int)$onStock;
            $product['is_discounted'] = (int)$isDiscounted;
            $product['EshopManufacturer'] = array_intersect_key($product['EshopManufacturer'], array(
                'name' => null,
            ));
            // add fuzzy string
            $product['_fuzzy'] = '';
            if (!empty($product['name'])) {
                $product['_fuzzy'] .= ' ' . MeiliSearch::getFuzzyString($product['name']);
            }
            if (!empty($product['EshopManufacturer']['name'])) {
                $product['_fuzzy'] .= ' ' . MeiliSearch::getFuzzyString($product['EshopManufacturer']['name']);
            }
            $product['_fuzzy'] = trim($product['_fuzzy']);
            // remove all empty fields
            $product = Arr::filterRecursive($product, function($v) {
                return $v !== null && $v !== '' && $v !== array();
            });
        }
        unset($product);

        if (
            !empty($products)
            && !is_array($id) 
        ) {
            $products = reset($products);
        }
        
        return $products;
    }
    
    /**
     * Updates products search settings
     */
    public function updateSettings() {
        $this->MeiliSearch->updateSettings(array(
            'displayedAttributes' => array(
                $this->Product->getPropertyPrimaryKey(),
                '_fuzzy',
            ),
            // attribute ranking order is given by the order in which are attributes introduced 
            // https://docs.meilisearch.com/learn/configuration/displayed_searchable_attributes.html#searchable-fields
            // https://docs.meilisearch.com/learn/core_concepts/relevancy.html#attribute-ranking-order
            'searchableAttributes' => array(
                'name',
                'EshopManufacturer.name',
                'seo_keywords',
                'code',
                '_fuzzy',
            ),
            'filterableAttributes' => array(
                'is_new',
                'on_stock',
                'is_discounted',
                'price_actual_taxed',
            ),
            'sortableAttributes' => array(
                'name',
                'price_actual_taxed',
                'orders_count',
            ),
            // https://docs.meilisearch.com/learn/core_concepts/relevancy.html
            'rankingRules' => array(
                'sort',
                'words',
                'typo',
                'proximity',
                'attribute',
                'exactness',
            ),
            //'stopWords' => array(),
            //'synonyms' => (object)array(),
            'typoTolerance' => array(
//                'enabled' => true,
                'minWordSizeForTypos' => array(
                    'oneTypo' => 3,
                    'twoTypos' => 5,
                )
            )
        ));
    }
    
    /**
     * @param int|array $ids Single product id or an array of such ids.
     */
    public function addOrReplaceProducts($ids) {
        $ids = (array)$ids;
        App::setDebug(false);
        while ($batchIds = array_splice($ids, 0, $this->batchSize)) {
            $products = $this->getProductsDetails($batchIds);
            // delete all products whose details are not returned
            $productIds = array_keys($products);
            $missingIds = array_values(array_diff($batchIds, $productIds));
            if ($missingIds) {
                $this->deleteProducts($missingIds);
            }
            $products = array_values($products);
            if ($products) {                
                $this->MeiliSearch->addOrReplaceDocuments(
                    $products, $this->Product->getPropertyPrimaryKey()
                );
            }
        }
    }
    
    public function addOrReplaceAllProducts() {
        $productIds = $this->Product->findList(array(
            'fields' => array('id'),
        ));
        $this->addOrReplaceProducts($productIds);
    }
    
    /**
     * @param int|array $ids Single product id or an array of such ids.
     */
    public function addOrUpdateProducts($ids) {
        $ids = (array)$ids;
        App::setDebug(false);
        while ($batchIds = array_splice($ids, 0, $this->batchSize)) {
            $products = $this->getProductsDetails($batchIds);
            // delete all products whose details are not returned
            $productIds = array_keys($products);
            $missingIds = array_values(array_diff($batchIds, $productIds));
            if ($missingIds) {
                $this->deleteProducts($missingIds);
            }
            $products = array_values($products);
            if ($products) {
                $this->MeiliSearch->addOrUpdateDocuments(
                    $products, $this->Product->getPropertyPrimaryKey()
                );
            }
        }
    }
    
    public function addOrUpdateAllProducts() {
        $productIds = $this->Product->findList(array(
            'fields' => array('id'),
        ));
        $this->addOrUpdateProducts($productIds);
    }
    
    /**
     * @param int|array $ids Single product id or an array of such ids.
     */
    public function deleteProducts($ids) {
        $this->MeiliSearch->deleteDocuments($ids);
    }
    
    public function deleteAllProducts() {
        $this->MeiliSearch->deleteAllDocuments();
    }
    
    /**
     * NOTE: If no 'ids' option is defined then index settings are set by this method 
     * as it is supposed to be launched like this for initial addition of products to index 
     * and for regular index actualization.
     * 
     * ATTENTION: If all or many (> $this->batchSize) products are synchronized then process
     * reservation is done and concurent methods silently fail (FALSE is returned 
     * for sync call of method).
     * 
     * ATTENTION: Read phpDoc of EshopProductMeiliSearch class.
     * 
     * @param array $options Following are available:
     *      - 'ids' (int|array) Single product id or an array of such ids. If no ids are
     *          provided then ALL products are synchronized. Defaults to NULL.
     *      - 'modifiedAfter' (string|int|float) If provided then synchronized are all products
     *          which have been modified after specified datetime. The datetime can be specified
     *          absolutely by a datetime string or positive integer unix timestamp. Or it can
     *          be defined relatively by negative number (integer or float) representing
     *          number of hours to go to past, e.g. -2.5 means that all products modified 
     *          before 2.5 hours and later will be synchronized. Defaults to NULL.
     *      - 'supplierProductsModifiedAfter' (string|int|float) The same as 'modifiedAfter'
     *          but modification is resolved from supplier products. Defaults to NULL.
     *      - 'debug' (bool) If TRUE then a logs are done to tmp/logs/EshopProductMeiliSearch_synchronize.log.
     *          Defaults to FALSE.
     *      - 'async' (bool) If TRUE then the method is launched asynchronously (as a separate
     *          resquest) using controller action EshopProductMeiliSearches::synchronize().
     *          Defaults to FALSE.
     * 
     * @return boolean|NULL FALSE if process reservation fails in sync call of method.
     *          NULL if 'async' is TRUE.
     */
    public function synchronize($options = array()) {
        $options = array_merge(array(
            'ids' => null,
            'modifiedAfter' => null,
            'supplierProductsModifiedAfter' => null,
            'debug' => false,
            'async' => false,
            
            // internal use
            'launchedAsAsync' => false,
        ), $options);
        $options['ids'] = (array)$options['ids'];
        // sync call of method (but can be called by asyn)
        if (!$options['async']) {
            set_time_limit(600);
            ini_set('memory_limit', '512M');
            if ($options['modifiedAfter']) {
                if (
                    Validate::number($options['modifiedAfter'])
                    && $options['modifiedAfter'] < 0
                ) {
                    $options['modifiedAfter'] = strtotime(
                        sprintf('%s seconds', $options['modifiedAfter'] * 3600)
                    );
                }
                if (!($modifiedAfter = Date::format($options['modifiedAfter'], 'Y-m-d H:i:s'))) {
                    throw new Exception(__e(__FILE__, 'Invalid date %s', $options['modifiedAfter']));
                }
                $ids = $this->Product->findList(array(
                    'key' => $this->Product->getPropertyPrimaryKey(),
                    'fields' => array($this->Product->getPropertyPrimaryKey()),
                    'conditions' => array('modified >=' => $modifiedAfter),
                ));
                $options['ids'] = array_unique(
                    array_merge(
                        array_values($options['ids']), 
                        array_values($ids)
                    )
                );
            }
            if ($options['supplierProductsModifiedAfter']) {
                if (
                    Validate::number($options['supplierProductsModifiedAfter'])
                    && $options['supplierProductsModifiedAfter'] < 0
                ) {
                    $options['supplierProductsModifiedAfter'] = strtotime(
                        sprintf('%s seconds', $options['supplierProductsModifiedAfter'] * 3600)
                    );
                }
                if (!($modifiedAfter = Date::format($options['supplierProductsModifiedAfter'], 'Y-m-d H:i:s'))) {
                    throw new Exception(__e(__FILE__, 'Invalid date %s', $options['supplierProductsModifiedAfter']));
                }
                $this->loadModel('EshopSupplierProduct');
                $SupplierProduct = new EshopSupplierProduct();
                $ids = $SupplierProduct->findList(array(
                    'key' => $this->Product->getForeignKey(),
                    'fields' => array($this->Product->getForeignKey()),
                    'conditions' => array('modified >=' => $modifiedAfter),
                ));
                $options['ids'] = array_unique(
                    array_merge(
                        array_values($options['ids']), 
                        array_values($ids)
                    )
                );
            }
            // if one of 'modifiedAfter' or 'supplierProductsModifiedAfter' is provided 
            // and no ids are resolved here then there is nothing to synchronize
            if (
                !$options['ids']
                && (
                    $options['modifiedAfter'] 
                    || $options['supplierProductsModifiedAfter']
                )
            ) {
                return true;
            }
            // if all or many products are synchronized then allow only one synchronization at the time
            if (
                !$options['ids']
                || count($options['ids']) > $this->batchSize // many ids
            ) {
                try {
                    App::reserveProcessing('EshopProductMeiliSearch_synchronize', array(
                        'tries' => 1,
                    ));
                } 
                catch (Exception_App_ProcessingReservationFailure $e) {
                    if ($options['debug']) {
                        App::log('EshopProductMeiliSearch_synchronize', 'Reservation failed', array(
                            'var' => array(
                                'idsCount' => count($options['ids']),
                                'modifiedAfter' => $options['modifiedAfter'],
                                'supplierProductsModifiedAfter' => $options['supplierProductsModifiedAfter'],
                            )
                        ));
                    }
                    if ($options['launchedAsAsync']) {
                        throw $e;
                    }
                    return false;
                }
            }
            if ($options['debug']) {
                App::log('EshopProductMeiliSearch_synchronize', 'Sync launch', array(
                    'var' => array(
                        'idsCount' => count($options['ids']),
                        'modifiedAfter' => $options['modifiedAfter'],
                        'supplierProductsModifiedAfter' => $options['supplierProductsModifiedAfter'],
                    )
                ));
            }
            if (!$options['ids']) {                
                $this->deleteAllProducts();
                $this->updateSettings();
                $this->addOrReplaceAllProducts();
            }
            else {
                $this->addOrReplaceProducts($options['ids']);
            }
            App::unreserveProcessing('EshopProductMeiliSearch_synchronize');
            return true;
        }
        // launch the method asynchronously
        else {
            $requestOptions = array(
                'timeout' => 1,
                'returnResponse' => false,
                //'getInfo' => true,
                //'logFile' => '/tmp/logs/EshopSetting_updateByName_asyncRequest',
            );
            $data = array(
                'launchedAsAsync' => 1,
            );
            if ($options['ids']) {
                $data['ids'] = implode(',', $options['ids']);
            }
            if ($options['modifiedAfter']) {
                $data['modifiedAfter'] = $options['modifiedAfter'];
            }
            if ($options['supplierProductsModifiedAfter']) {
                $data['supplierProductsModifiedAfter'] = $options['supplierProductsModifiedAfter'];
            }
            if ($options['debug']) {
                $data['debug'] = (int)$options['debug'];
            }
            if ($data) {                
                $requestOptions['method'] = 'POST';
                $requestOptions['data'] = http_build_query($data);
            }
            App::request(
                App::getUrl(array(
                    'locator' => '/mvc/Eshop/EshopProductMeiliSearches/synchronize',
                    'absolute' => true,
                )), 
                $requestOptions
            );
            if ($options['debug']) {
                App::log('EshopProductMeiliSearch_synchronize', 'Async launch', array(
                    'var' => array(
                        'idsCount' => count($options['ids']),
                        'modifiedAfter' => $options['modifiedAfter'],
                        'supplierProductsModifiedAfter' => $options['supplierProductsModifiedAfter'],
                    )
                ));
            }
            return null;
        }
    }
    
    /**
     * @param string $keywords
     * @param array $options
     * 
     * @return MeiliSearch\Search\SearchResult
     */
    public function search($keywords, $options = array()) {
        $options = array_merge(array(
            'attributesToRetrieve' => array(
                $this->Product->getPropertyPrimaryKey(),
            ),
        ), $options);
        return $this->MeiliSearch->search($keywords, $options);
    }
}
