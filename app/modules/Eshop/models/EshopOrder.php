<?php

class EshopOrder extends Model {
    protected $table = 'run_eshop_orders';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'number' => array('type' => 'varchar', 'length' => 20, 'index' => 'unique' , 'comment' => 'Order number'),
        'token' => array('type' => 'varchar', 'length' => 40, 'comment' => 'Token used to securize payment request, as payment action must be public for case of quick orders'),
        'cart_id' => array('type' => 'varchar', 'length' => 40, 'default' => null, 'index' => 'unique', 'comment' => 'Id of cart the orders has been created for. Used to avoid multiple submitions of the same cart'),
        'run_users_id' => array('type' => 'int', 'default' => null, 'index' => 'index', 'comment' => 'Can be NULL for quick orders'),
        'specific' => array('type' => 'tinyint', 'default' => 0, 'comment' => 'If nonempty (bitwise addition of EshopOrder::SPECIFIC_??? constants) then order is specific and something must be set manually, e.g. shipment price'),
        'status' => array('type' => 'enum', 'values' => array('enum_new_order', 'enum_opened_order', 'enum_suspended_order', 'enum_pickup_order', 'enum_shipped_order', 'enum_closed_order', 'enum_canceled_order'), 'default' => 'enum_new_order'),
        'payment_status' => array('type' => 'enum', 'values' => array('enum_payment_none', 'enum_payment_advance_paid', 'enum_payment_partially_paid', 'enum_payment_paid', 'enum_payment_tout', 'enum_payment_failed', 'enum_payment_manipulated'), 'default' => 'enum_payment_none',  'comment' => 'Status enum_payment_manipulated means that payment request/response has been somehow hacked by client.'),
        'payment_signed' => array('type' => 'bool', 'default' => null, 'comment' => 'Is the payment status based on signed response data?'),
        'payment_result_text' => array('type' => 'text', 'default' => null, 'comment' => 'Result code(s) together with their text representation from bank response on payment request. This serves just for information'), 
        'run_payment_request_logs_id' => array('type' => 'int', 'default' => null, 'index' => 'index', 'comment' => 'Reference to payment request log (if any)'),
        'advance_rate' => array('type' => 'tinyint', 'length' => 3, 'default' => null, 'comment' => 'Advance rate choosen by client on checkout'),
        
        'order_price_taxless' => array('type' => 'decimal', 'length' => 14.2, 'comment' => 'Total order price without VAT before discount'),
        'order_tax' => array('type' => 'decimal', 'length' => 14.2, 'comment' => 'Total order VAT before discount'),
        'order_price_actual_taxless' => array('type' => 'decimal', 'length' => 14.2, 'comment' => 'Total order price without VAT after discount'),
        'order_tax_actual' => array('type' => 'decimal', 'length' => 14.2, 'comment' => 'Total order VAT after discount'),
        'order_price_to_pay' => array('type' => 'decimal', 'length' => 14.2, 'comment' => 'Total order price to pay. It is influenced by applied bonus and multi purpose absolute discount vouchers. It can be either taxed or taxless value depending on order type (with or without VAT)'),
        
        'products_price_taxless' => array('type' => 'decimal', 'length' => 14.2, 'comment' => 'Total order products price without VAT before discount'),
        'products_tax' => array('type' => 'decimal', 'length' => 14.2, 'comment' => 'Total order products VAT before discount'),
        'products_price_actual_taxless' => array('type' => 'decimal', 'length' => 14.2, 'comment' => 'Total order products price without VAT after discount'),
        'products_tax_actual' => array('type' => 'decimal', 'length' => 14.2, 'comment' => 'Total order products VAT after discount'),
        'products_price_to_pay' => array('type' => 'decimal', 'length' => 14.2, 'comment' => 'Total order products price to pay. It is influenced by applied bonus and multi-purpose-absolute-discount-vouchers. It can be either taxed or taxless value depending on order type (with or without VAT)'),
        
        // @see implementationDetails > ZĽAVOVÉ KÓDY S ABSOLÚTNOU ZĽAVOU (PEŇAŽNÉ POUKAZY)
        'vouchers_discount' => array('type' => 'decimal', 'length' => 14.2, 'default' => null, 'comment' => 'Multi-purpose-absolute-discount-vouchers discount substracted from the order products price. It can be either taxed or taxless value depending on order type (with or without VAT)'),
        
        // @see implementationDetails > BONUSY
        'bonus_discount' => array('type' => 'decimal', 'length' => 14.2, 'default' => null, 'comment' => 'Bonus discount substracted from the order products price. It can be either taxed or taxless value depending on order type (with or without VAT)'),
        'in_bonus_points' => array('type' =>'bool', 'default' => 0, 'comment' => 'Was the final products price of this order (after substracting possible bonus) added to UserProfile.bonus_points?'),
        
        'shipment_price_taxless' => array('type' => 'decimal', 'length' => 14.2, 'default' => null, 'comment' => 'Total shipment price without VAT before discount'),
        'shipment_tax' => array('type' => 'decimal', 'length' => 14.2, 'default' => null, 'comment' => 'Total shipment VAT before discount'),
        'shipment_price_actual_taxless' => array('type' => 'decimal', 'length' => 14.2, 'default' => null, 'comment' => 'Total shipment price without VAT after discount'),
        'shipment_tax_actual' => array('type' => 'decimal', 'length' => 14.2, 'default' => null, 'comment' => 'Total shipment VAT after discount'),
        
        'payment_price_taxless' => array('type' => 'decimal', 'length' => 14.2, 'default' => null, 'comment' => 'Total payment price without VAT before discount'),
        'payment_tax' => array('type' => 'decimal', 'length' => 14.2, 'default' => null, 'comment' => 'Total payment VAT before discount'),
        'payment_price_actual_taxless' => array('type' => 'decimal', 'length' => 14.2, 'default' => null, 'comment' => 'Total payment price without VAT after discount'),
        'payment_tax_actual' => array('type' => 'decimal', 'length' => 14.2, 'default' => null, 'comment' => 'Total payment VAT after discount'),
        
        'run_eshop_shipment_methods_id' => array('type' => 'int', 'default' => null),
        'shipment_method_name' => array('type' => 'varchar', 'default' => null),
        'run_payment_methods_id' => array('type' => 'int', 'default' => null),
        'payment_method_name' => array('type' => 'varchar', 'default' => null),
        
        'fullname' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Firstname and surname'),
        'street' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Street and number'),
        'city' => array('type' => 'varchar', 'length' => 50, 'default' => null),
        'zip' => array('type' => 'varchar', 'length' => 10, 'default' => null),
        'country' => array('type' => 'char', 'length' => 2, 'default' => null, 'comment' =>'Country ISO code 2'),
        'phone' => array('type' => 'varchar', 'length' => 30, 'default' => null),
        'email' => array('type' => 'varchar', 'length' => 100, 'default' => null),
        'company_fullname' => array('type' => 'varchar', 'length' => 100, 'default' => null),
        'company_id_number' => array('type' => 'varchar', 'length' => 20, 'default' => null, 'comment' => 'IČO'),
        'company_tax_number' => array('type' => 'varchar', 'length' => 20, 'default' => null, 'comment' => 'DIČ'),
        'company_vat_number' => array('type' => 'varchar', 'length' => 20, 'default' => null, 'comment' => 'IČ DPH'),
        
        'delivery_fullname' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Firstname and surname'),
        'delivery_street' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Street and number'),
        'delivery_city' => array('type' => 'varchar', 'length' => 50, 'default' => null),
        'delivery_zip' => array('type' => 'varchar', 'length' => 10, 'default' => null),
        'delivery_country' => array('type' => 'char', 'length' => 2, 'default' => null, 'comment' =>'Country ISO code 2'),
        'delivery_phone' => array('type' => 'varchar', 'length' => 30, 'default' => null),
        'delivery_email' => array('type' => 'varchar', 'length' => 100, 'default' => null),
        'delivery_company_fullname' => array('type' => 'varchar', 'length' => 100, 'default' => null),
        
        'comment' => array('type' => 'text', 'default' => null, 'comment' => 'Order comment posted by user in checkout'),
        'notes' => array('type' => 'text', 'default' => null, 'comment' => 'Text notes from admin and from system about changed status etc.'),
        'invoice_pdf' => array('type' => 'varchar', 'default' => null,  'comment' => 'Invoice pdf file'),
        
        // following 4 fields are used with quick orders
        'terms_and_conditions_agreement' => array('type' => 'bool', 'default' => 0, 'comment' => 'Agreement with business terms and conditions'),
        'prolonged_delivery_time_agreement' => array('type' => 'bool', 'default' => 0, 'comment' => 'Agreement with prolonged delivery time (>30 days)'),
        'newsletters_agreement' => array('type' => 'bool', 'default' => 0, 'comment' => 'Agreement with newsletters receiving'),
        'adulthood_declaration' => array('type' => 'bool', 'default' => 0, 'comment' => 'Declaration that person is adult and is more than 18 years old'),
        'reference_from' => array('type' => 'varchar', 'length' => 50, 'default' => null, 'comment' => 'Response on question: Where did you hear about us?'),
        
        'eshop_voucher_code' => array('type' => 'varchar', 'length' => 255, 'default' => null, 'index' => 'index'), 
        
        'new_order_email_success' => array('type' =>'bool', 'default' => 0, 'comment' => 'Has been the new order email successfully sent?'),
        'successful_payment_email_success' => array('type' =>'bool', 'default' => 0, 'comment' => 'Has been the successful payment email successfully sent?'),
        'lang' => array('type' => 'varchar', 'lenght' => 5, 'default' => null, 'comment' => 'Lang version of eshop the order was created for'),
        'pickup_place' => array('type' => 'varchar', 'lenght' => 255, 'default' => null, 'comment' => 'Pickup place address'),
        'pickup_place_id' => array('type' => 'varchar', 'lenght' => 25, 'default' => null, 'comment' => 'Pickup place id'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
        'exported' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime of order export to accounting system'),
        'deleted' => array('type' => 'datetime', 'default' => null, 'index' => 'index'),
    );
    
    /**
     * Orders with these statuses are considered to be live (not closed)
     * Use method EshopOrder::getPropertyLiveStatuses() to get them.
     * 
     * @var array 
     */
    protected $liveStatuses = array(
        'enum_new_order' => true, 
        'enum_opened_order' => true, 
        'enum_suspended_order' => true,
    );
    
    /**
     * Checkout wizard
     * 
     * @var Wizard 
     */
    public $Checkout;
    
    /**
     * Name used for checkout wizard.
     * 
     * @var string 
     */
    protected $checkoutWizardName = 'EshopOrder_checkout';
        
    /**
     * Definition of valid checkout wizard steps in order as they should occure.
     * The steps are defined in array containing pairs '{stepSlug}' => '{stepPid}'.
     * 
     * @var array
     */
    protected $checkoutWizardSteps = array(
        'fakturacne-udaje' => 'checkoutStep01',
        'sposob-dorucenia-a-platby' => 'checkoutStep02',
        'zhrnutie' => 'checkoutStep03',
        'odoslat' => 'checkoutSubmit',
    );
    
    protected $fileFields = array(
//        'wedding_announcement_files' => array(
//            'path' => 'wedding_announcement_files',
//            'placeholder' => '_placeholder.png',
//        ),
        'invoice_pdf' => true
    );
    
    /**
     * Store used for file uploades during checkout.
     * These files are moved to EshopOrder::$fileStore at the checkout submition.
     * 
     * @var string 
     */
    protected $checkoutFileStore = '/tmp/checkoutFiles';
        
    /**
     * Value to be set in EshopOrder.specific field to mark that order shipment is specific
     * 
     * Can be combined with other EshopOrder::SPECIFIC_??? constants like: 
     * $specific = EshopOrder::SPECIFIC_SHIPMENT | EshopOrder::SPECIFIC_PAYMENT.
     * 
     * To check if order has specific shipment use: $specific & EshopOrder::SPECIFIC_SHIPMENT
     */
    const SPECIFIC_SHIPMENT = 1;
    
    /**
     * Value to be set in EshopOrder.specific field to mark that order payment is specific
     * 
     * Can be combined with other EshopOrder::SPECIFIC_??? constants like:
     * $specific = EshopOrder::SPECIFIC_SHIPMENT | EshopOrder::SPECIFIC_PAYMENT.
     * 
     * To check if order has specific payment use: $specific & EshopOrder::SPECIFIC_PAYMENT
     */
    const SPECIFIC_PAYMENT = 2;
    
    /**
     * 
     * 
     * @param string $checkoutSlug Optional. Checkout wizard slug. If provided then 
     *      Checkout wizard instance is created and stored in EshopOrder::$Checkout property.
     *      Defaults to FALSE - checkout wizard instance is not created.
     */
    public function __construct($checkoutSlug = false) {
        parent::__construct();
        
        if ($checkoutSlug !== false) {
            $this->loadCheckoutWizard($checkoutSlug);
        }
        
        /**
         * There are defined validation alternatives for each of order steps (checkoutStep01,
         * checkoutStep02, checkoutStep03) and for quick order variants of this steps
         * (checkoutStep01_QuickOrder, checkoutStep02_QuickOrder, checkoutStep03_QuickOrder)
         */
        $this->validations = array(
            'id' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'alternative' => array('backend'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'alternative' => array('backend'),
                ),
            ),
            'number' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Order number is required'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Order number must not be empty'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Order number must be unique'),
                ),
            ),                    
            /**
             * Checkout wizard validations
             */
            // address screen
            'email' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide email'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide email'),
                    'alternative' => 'checkoutStep01',
                ),
                array(
                    'rule' => 'email',
                    'message' => __v(__FILE__, 'E-mail is not valid'),
                    'alternative' => 'checkoutStep01',
                ),
            ),
            'phone' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide phone'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide phone'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => array('phone', array(
                        //'internationalFormat' => true
                    )),
                    'message' => __v(__FILE__, 'Zadajte prosím platné telefónne číslo'),
                    //'message' => __v(__FILE__, 'Zadajte prosím platné telefónne číslo s medzinárodnou predvoľbou (napr. pre Slovensko +421...)'),
                    'alternative' => 'checkoutStep01',
                ),
                array(
                    'rule' => 'phoneCallingCode',
                    'alternative' => 'checkoutStep01',
                ),                
            ),
            'company_fullname' => array(
                array(
                    'rule' => 'checkoutCompany',
                    'force' => true,
                    'alternative' => 'checkoutStep01',
                    'exit' => true, // folowing validations are allowed by checkoutData rule
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide company name'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide company name'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'company_id_number' => array(
                array(
                    'rule' => 'checkoutCompany',
                    'force' => true,
                    'alternative' => 'checkoutStep01',
                    'exit' => true, // folowing validations are allowed by checkoutData rule
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide company id number'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide company id number'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'ico',
                    'message' => __v(__FILE__, 'Please provide a valid company id number'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'company_tax_number' => array(
                array(
                    'rule' => 'checkoutCompany',
                    'force' => true,
                    'alternative' => 'checkoutStep01',
                    'exit' => true, // folowing validations are allowed by checkoutData rule
                ),
                // not all organisations have tax number (DIČ), but for sure VAT payers have it                
                array(
                    'rule' => function($value, $field, $data) {
                        if (
                            !empty($data['company_vat_payer'])
                            && empty($value)
                        ) {
                            return false;
                        }
                        return true;
                    },
                    'message' => __v(__FILE__, 'Please provide company tax number'),
                    'alternative' => 'checkoutStep01',
                    'force' => true,
                ),  
                array(
                    'rule' => 'dic',
                    'message' => __v(__FILE__, 'Please provide a valid company tax number'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'company_vat_number' => array(
                array(
                    'rule' => 'checkoutCompany',
                    'force' => true,
                    'alternative' => 'checkoutStep01',
                    'exit' => true, // folowing validations are allowed by checkoutData rule
                ),
                array(
                    'rule' => function($value, $field, $data) {
                        if (
                            !empty($data['company_vat_payer'])
                            && empty($value)
                        ) {
                            return false;
                        }
                        return true;
                    },
                    'message' => __v(__FILE__, 'Please provide a company VAT number'),
                    'alternative' => 'checkoutStep01',
                    'force' => true,
                ),  
                array(
                    'rule' => array('dic', 'withCountryCode'),
                    'message' => __v(__FILE__, 'Please provide a valid company VAT number'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'fullname' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide firstname and lastname'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide firstname and lastname'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'humanFullname',
                    'message' => __v(__FILE__, 'Please provide firstname and lastname (both)'),
                    'alternative' => 'checkoutStep01',
                ),
            ),
            'street' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide street and house number'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide street and house number'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'address',
                    'message' => __v(__FILE__, 'Please provide street and house number'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'country' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide country'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide country'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'zip' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide zip'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide zip'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'city' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide city'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide city'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'deliveryAddress' => array(
                array(
                    'rule' => 'required',
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'checkoutAddress',
                    'alternative' => 'checkoutStep01',
                ),
            ),
            'delivery_fullname' => array(
                array(
                    'rule' => 'checkoutAddress',
                    'force' => true,
                    'alternative' => 'checkoutStep01',
                    'exit' => true, // folowing validations are allowed by checkoutData rule
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide firstname and lastname'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide firstname and lastname'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'humanFullname',
                    'message' => __v(__FILE__, 'Please provide firstname and lastname (both)'),
                    'alternative' => 'checkoutStep01',
                ),
            ),
            'delivery_street' => array(
                array(
                    'rule' => 'checkoutAddress',
                    'force' => true,
                    'alternative' => 'checkoutStep01',
                    'exit' => true, // folowing validations are allowed by checkoutData rule
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide street and house number'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide street and house number'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'address',
                    'message' => __v(__FILE__, 'Please provide street and house number'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'delivery_country' => array(
                array(
                    'rule' => 'checkoutAddress',
                    'force' => true,
                    'alternative' => 'checkoutStep01',
                    'exit' => true, // folowing validations are allowed by checkoutData rule
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide country'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide country'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'delivery_zip' => array(
                array(
                    'rule' => 'checkoutAddress',
                    'force' => true,
                    'alternative' => 'checkoutStep01',
                    'exit' => true, // folowing validations are allowed by checkoutData rule
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide zip'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide zip'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'delivery_city' => array(
                array(
                    'rule' => 'checkoutAddress',
                    'force' => true,
                    'alternative' => 'checkoutStep01',
                    'exit' => true, // folowing validations are allowed by checkoutData rule
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide city'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide city'),
                    'alternative' => 'checkoutStep01',
                ),  
            ),
            'delivery_phone' => array(
                array(
                    'rule' => 'checkoutAddress',
                    'force' => true,
                    'alternative' => 'checkoutStep01',
                    'exit' => true, // folowing validations are allowed by checkoutData rule
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide phone'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide phone'),
                    'alternative' => 'checkoutStep01',
                ),  
                array(
                    'rule' => array('phone', array(
                        //'internationalFormat' => true
                    )),
                    'message' => __v(__FILE__, 'Zadajte prosím platné telefónne číslo'),
                    //'message' => __v(__FILE__, 'Zadajte prosím platné telefónne číslo s medzinárodnou predvoľbou (napr. pre Slovensko +421...)'),
                    'alternative' => 'checkoutStep01',
                ),
                array(
                    'rule' => 'phoneCallingCode',
                    'alternative' => 'checkoutStep01',
                ),
            ),
            // shipment & payment screen
            'shipment' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please choose shipment'),
                    'alternative' => 'checkoutStep02',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please choose shipment'),
                    'alternative' => 'checkoutStep02',
                ),  
            ),
            'payment' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please choose payment'),
                    'alternative' => 'checkoutStep02',
                ),  
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please choose payment'),
                    'alternative' => 'checkoutStep02',
                ),  
                array(
                    'rule' => 'compatiblePayment',
                    'message' => __v(__FILE__, 'Please choose payment method compatible with selected shipment method'),
                    'alternative' => 'checkoutStep02',
                ),  
            ),
            'pickup_place' => array(
                array(
                    'rule' => 'requirePickupPlace',
                    'message' => __v(__FILE__, 'Please choose pickup place'),
                    'alternative' => 'checkoutStep02',
                    'force' => true,
                ),
            ),
            // summary screen
            'terms_and_conditions_agreement' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please confirm that you accept business terms and conditions'),
                    'on' => 'create',
                    'alternative' => 'checkoutStep03',
                ),
                array(
                    'rule' => array('notEmpty', true),
                    'message' => __v(__FILE__, 'Please confirm that you accept business terms and conditions'),
                    'alternative' => 'checkoutStep03',
                ),
            ),
        );
    }
    
    /**
     * Returns list of live statuses with pairs {status} => true
     * 
     * @return array
     */
    public function getPropertyLiveStatuses() {
        return $this->liveStatuses;
    }
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        
        // sanitize html to text (remove HTML tags) to avoid XSS attacks
        foreach ($this->getFields() as $field) {
            if (
                isset($data[$field])
                && is_string($data[$field])
                // avoid following fields
                && $field !== 'id'
            ) {
                $data[$field] = Sanitize::htmlTags($data[$field]);
            }
        }
        
        if (!empty($data['country'])) {
            $data['country'] = trim(strtoupper($data['country']));
        }
        if (!empty($data['company_country'])) {
            $data['company_country'] = trim(strtoupper($data['company_country']));
        }
        if (!empty($data['delivery_country'])) {
            $data['delivery_country'] = trim(strtoupper($data['delivery_country']));
        }
        
        if (isset($data['company_id_number'])) {
            $data['company_id_number'] = str_replace(array(' ', '-'), array('', ''), $data['company_id_number']);
        }
        
        if (isset($data['company_tax_number'])) {
            $data['company_tax_number'] = str_replace(array(' ', '-'), array('', ''), $data['company_tax_number']);
        }
        
        if (
            !empty($data)
            && array_key_exists('company_vat_payer', $data)
            && !$data['company_vat_payer']
        ) {
            $data['company_vat_number'] = '';
        }
        if (isset($data['company_vat_number'])) {
            $data['company_vat_number'] = str_replace(array(' ', '-'), array('', ''), $data['company_vat_number']);
        }
        
        if (isset($data['zip'])) {
            $data['zip'] = str_replace(array(' ', '-'), array('', ''), $data['zip']);
        }
        if (isset($data['delivery_zip'])) {
            $data['delivery_zip'] = str_replace(array(' ', '-'), array('', ''), $data['delivery_zip']);
        }
        
        if (isset($data['email'])) {
            $data['email'] = strtolower($data['email']);
        }
        if (isset($data['delivery_email'])) {
            $data['delivery_email'] = strtolower($data['delivery_email']);
        }
        
        if (
            isset($data['phone'])
            // normalize phone only at order data save but not in order checkout
            && !in_array('checkoutStep01', $options['alternative'])
        ) {
            $data['phone'] = $this->normalizePhone($data['phone']);
        }
        if (
            isset($data['delivery_phone'])
            // normalize phone only at order data save but not in order checkout
            && !in_array('checkoutStep01', $options['alternative'])
        ) {
            $data['delivery_phone'] = $this->normalizePhone($data['delivery_phone']);
        }
        
        // set payment_signed on update if there is payment_status
        if (
            !empty($data['id'])
            && !empty($data['payment_status'])
            && in_array('backend', $options['alternative'])
        ) {
            // for "unpaid" set always payment_signed to FALSE
            if ($data['payment_status'] === 'enum_payment_none') {
                $data['payment_signed'] = false;
            }
            // if manually changed to "paid" then set payment_signed to TRUE
            elseif (
                (
                    $data['payment_status'] === 'enum_payment_advance_paid'
                    || $data['payment_status'] === 'enum_payment_partially_paid'
                    || $data['payment_status'] === 'enum_payment_paid'
                )
                && $data['payment_status'] !== $this->findFieldBy('payment_status', 'id', $data['id'])
            ) {
                $data['payment_signed'] = true;
            }
        }
        
        return parent::normalize($data, $options);
    }
    
    /**
     * Normalizes phone number, e.g. for ' 00421 987-654-321 ' returns '+421987654321'
     *  
     * @param string $phone
     * 
     * @return string Normalized phone number
     */
    public function normalizePhone($phone) {
        $phone = trim($phone);
        $hasPlus = substr($phone, 0, 1) === '+';
        $phone = preg_replace('/^00/', '+', preg_replace('/[^0-9]/', '', $phone));
        if ($hasPlus) {
            $phone = '+' . $phone;
        }
        return $phone;
    }
    
    /**
     * Normalizes find options. This is done in Model::find() if options['normalize'] is TRUE
     * after Paginator find options are merged, joins and fields are normalized 
     * and before translation processing starts.
     * 
     * @param array $options Find options to be normalized
     * 
     * @return array Normalized find options
     */
    public function normalizeFindOptions($options) {
        // normalize joins
        if (!empty($options['joins'])) {
            // for filter in EshopOrders::admin_index()
            // 
            // if there is a join to EshopProduct model then add also join to habtm model EshopOrderProduct if not joined yet
            $paths = Arr::search($options['joins'], '/EshopProduct/', array(
                'comparison' => 'regex', 
                'separator' => '/',
                //'first' => true,
                'length' => 1,
            ));
            if ($paths !== false) {
                $addedCount = 0;
                foreach ($paths as $path) {
                    // add join to EshopOrderProduct if not defined yet (it must be placed before EshopProduct join)
                    $habtmPath = Arr::search($options['joins'], '/EshopOrderProduct/', array(
                        'comparison' => 'regex', 
                        'separator' => '/',
                        'first' => true,
                        'length' => 1,
                    ));
                    if ($habtmPath === false) {
                        array_unshift($options['joins'], array(
                            'model' => 'EshopOrderProduct',
                            'type' => 'left',
                        ));
                        $addedCount++;
                    }
                    // add 'toModel' => 'EshopOrderProduct' to 'EshopProduct' join definition
                    Arr::setPath($options['joins'], ($path + $addedCount) . '/toModel', 'EshopOrderProduct', '/');
                }
            }
        }
        return $options;
    }
    
    /**
     * Returns property  EshopOrder::$checkoutFileStore
     * 
     * @return string
     */
    public function getPropertyCheckoutFileStore() {
        return $this->checkoutFileStore;
    }
    
    /**
     * Returns property EshopOrder::$checkoutWizardSteps
     * 
     * @return array
     */
    public function getPropertyCheckoutWizardSteps() {
        return $this->checkoutWizardSteps;
    }
    
    /**
     * Loads checkout wizard into EshopOrder::$Checkout property
     * 
     * ATTENTION: Checkout wizard is loaded only if it has not been done yet. It
     * means checkout wizard is loaded only once.
     * 
     * @param string $checkoutSlug Optional. Checkout wizard slug. Defaults to NULL.
     */
    protected function loadCheckoutWizard($checkoutSlug = null) {
        if (empty($this->Checkout)) {
            App::loadLib('App', 'Wizard');
            $this->Checkout = new Wizard($this->checkoutWizardName, $this->checkoutWizardSteps, $checkoutSlug);
        }
    }
        
    /**
     * Can be the new order checkout processed on?
     * This checks rights on runtime for case of quick order.
     * 
     * New order checkout is allowed if:
     *      - there is logged user
     *      - quick order is the only ordering method, means setting 'EshopOrder.allowQuickOrder'
     *          has value 'force'
     *      - quick order is allowed (setting 'EshopOrder.allowQuickOrder' has value '1') 
     *          and the quick order has been started (see EshopOrders::login())
     * 
     * @return bool If TRUE then checkout can be processed. If FALSE go then go 
     *      to login page.
     */
    public function getCheckoutRights() {
        $quickOrderAllowed = $this->hasQuickOrderAllowed();
        return (bool)(
            App::getUser()
            ||
            $quickOrderAllowed === 'force'
            ||
            $quickOrderAllowed && $this->isQuickOrder()
        );
    }
    
    /**
     * Checks if quick order is allowed (can be started)
     * 
     * @return bool|string TRUE or 'force' if quick order is allowed. The 'force'
     *          means that there is no registration/login possibility and the 
     *          orders are supposed to be always done as quick orders. FALSE otherwise.
     */
    public function hasQuickOrderAllowed() {
        $allowQuickOrder = $this->getSetting('EshopOrder.allowQuickOrder');
        return $allowQuickOrder === 'force' ? 'force' : (bool)$allowQuickOrder;
    }
    
    /**
     * Starts quick order session.
     * 
     * Quick order session is started either if it is allowed and requested or if
     * it is forced (means that it is the only ordering possibility).
     * 
     * This method is used at the beginning of EshopOrders::checkout() and EshopOrder::add().
     * In EshopOrder::add() it us used for case of forced quick order.
     * 
     * @param array $data Optional. Order data to start the quick order session for. The method 
     *      search for '_start_quick_order' key in the data. Defaults to empty array.
     * 
     * @return array Order data with removed items '_start_quick_order' and '_target'
     */
    public function startQuickOrder($data = array()) {
        // start quick order if it is requested and it is allowed
        $quickOrderAllowed = $this->hasQuickOrderAllowed();
        if (array_key_exists('_start_quick_order', $data)){
            if(
                $data['_start_quick_order']
                && $quickOrderAllowed
            ) {
                $_SESSION['_eshop']['EshopOrder']['isQuickOrder'] = true;
            }
            unset($data['_start_quick_order']);
            unset($data['_target']);
        }
        // start quick order if it is forced ordering method
        if ($quickOrderAllowed === 'force') {
            $_SESSION['_eshop']['EshopOrder']['isQuickOrder'] = 'force';
        }
        // if there is an incorrect state of session (quick order is not allowed or 
        // not forced, but the session is set inadequately) then just clean it up.
        // This should not normally happen
        if (
            isset($_SESSION['_eshop']['EshopOrder']['isQuickOrder'])
            && (
                !$quickOrderAllowed
                && $_SESSION['_eshop']['EshopOrder']['isQuickOrder']
                ||
                $quickOrderAllowed !== 'force'
                && $_SESSION['_eshop']['EshopOrder']['isQuickOrder'] === 'force'
            )
        ) {
            $this->closeQuickOrder();
        }
        return $data;
    }
    
    /**
     * Is the actual order checkout processed as a quick order?
     * 
     * @return bool 
     */ 
    public function isQuickOrder() {
        $isQuickOrder = Sanitize::value($_SESSION['_eshop']['EshopOrder']['isQuickOrder'], false);
        return (
            !App::getUser() 
            && $isQuickOrder
            || 
            $isQuickOrder === 'force'
        );
    }
    
    /**
     * Closes quick order session
     */
    public function closeQuickOrder() {
        unset($_SESSION['_eshop']['EshopOrder']['isQuickOrder']);
    }
        
    /**
     * Sets checkout data of actual step
     * 
     * @param array $data
     * 
     * @return bool FALSE on validation error. Otherwise TRUE.
     */
    public function setCheckoutData($data) {
        $this->loadCheckoutWizard();
        // data are submited by previous step of wizard
        $previousStep = $this->Checkout->getPreviousStep();
        // validate
        $this->Checkout->setParameter('errors', array(), $previousStep);
        $alternative = array($previousStep);
        if ($this->isQuickOrder()) {
            $alternative[] = $previousStep . '_QuickOrder';
        }
        $data = $this->normalize($data, array('alternative' => $alternative));
        $valid = $this->validate($data, array('alternative' => $alternative));
        if (!$valid) {
            $this->Checkout->setParameter('errors', $this->getErrors(), $previousStep);
        }
        // upload files if provided (comment this out if there are no files uploaded in checkout process)
        $data = $this->prepareCheckoutFiles($data);
        // save data
        $this->Checkout->setParameter('data', $data, $previousStep);
        return $valid;
    }
    
    /**
     * Prepares checkout files to temporary place, till the checkout is submited
     * 
     * @param array $data Checkout data
     * 
     * @return array Data with updated values of file fields. File fields are set 
     *      to incoming file names.
     */
    public function prepareCheckoutFiles($data) {
        // check if there are file fields in current model
        if (!$this->fileFields) {
            return $data;
        }
        // check if there are file fields in provided data
        $fileFields = array_intersect_key($data, $this->fileFields);
        if (!$fileFields) {
            return $data;
        }
        // transfer and transform files
        $sessionId = session_id();
        foreach ($fileFields as $field => $files) {
            foreach ($files as $i => $value) {
                // use session ids to name files to allow implement a sort of garbage collector for files of unexisting sessions
                // Do not put any extension to it to facilitate further processing in view form (extension is separated by dot which means nesting in form fields names)
                $transferName = $sessionId . '_' . uniqid();
                // transfer
                $result = File::transfer($value, $this->checkoutFileStore, array(
                    'name' => $transferName,
                ));
                // in case that field $value is not a file source then let it untouched 
                // then save it as it is provided, just prepare obsolete fields here below
                if ($result === File::NOT_SOURCE) {
                    // do nothing
                }
                // if the $value is file source but transfer of file has failed then 
                // unset the field in data to avoid overwriting of existing field value
                elseif ($result === false) {
                    unset($data[$field][$i]);
                }
                // otherwise process transfered file
                else {
                    $data[$field][$result] = $value['name'];
                    // remove the source in data
                    unset($data[$field][$i]);
                }     
            }
        }
        
        return $data;
    }
    
    /**
     * Prepares new order files from checkout temporary files.
     * 
     * @param array $data Checkout data
     * 
     * @return array Data with updated values of file fields. File fields are set 
     *      to incoming file names.
     */
    public function prepareNewOrderFiles($data) {
        // check if there are file fields in current model
        if (!$this->fileFields) {
            return $data;
        }
        // check if there are file fields in provided data
        $fileFields = array_intersect_key($data, $this->fileFields);
        if (!$fileFields) {
            return $data;
        }
        // transfer and transform files
        foreach ($fileFields as $field => $files) {
            foreach ($files as $checkoutFile => $originalNane) {
                $fileSource = array(trim($this->checkoutFileStore, DS) . DS . $checkoutFile);
                // transfer checkout file to order store under the original name
                $result = File::transfer($fileSource, $this->fileStore, array(
                    'name' => $originalNane,
                ));
                // in case that field $value is not a file source then let it untouched 
                // then save it as it is provided, just prepare obsolete fields here below
                if ($result === File::NOT_SOURCE) {
                    // do nothing
                }
                // if the $value is file source but transfer of file has failed then 
                // unset the field in data to avoid overwriting of existing field value
                elseif ($result === false) {
                    unset($data[$field][$checkoutFile]);
                }
                // otherwise process transfered file
                else {
                    $data[$field][] = $result;
                    // remove the source in data
                    unset($data[$field][$checkoutFile]);
                    $this->setIncomingFiles($field, $result);
                }     
            }
            // reindex the values in array
            if (isset($data[$field])) {
                $data[$field] = array_values($data[$field]);
            }
        }
        
        return $data;
    }
    
    /**
     * Cleans up obsolete temporary checkout files.
     * Deleted are files which are not paired with any session and files which
     * are paired with current session id (as this is called at the end of checkout process) 
     */
    public function cleanUpCheckoutFiles() {
        $checkoutFilesStore = ROOT . DS . trim($this->checkoutFileStore, DS);
        if (!is_readable($checkoutFilesStore)) {
            return;
        }
        $dirItems = scandir($checkoutFilesStore);
        $sessionId = session_id();
        foreach ($dirItems as $item) {
            // skip hidden files (e.g. .htaccess)
            if (substr($item, 0, 1) === '.') {
                continue;
            }
            $itemPath = $checkoutFilesStore . DS . $item;
            if (is_file($itemPath)) {
                $item = explode('_', File::getPathinfo($item, PATHINFO_FILENAME));
                $itemSessionId = reset($item);
                if (
                    $itemSessionId === $sessionId
                        || !App::hasSessionFile($itemSessionId)
                ) {
                    unlink($itemPath);
                }
            }
        }
    }
    
    /**
     * Adds new order based on data of cart and checkout wizard.
     * 
     * NOTE: On success the new order id is stored in session. 
     *      Use EshopOrder::getNewOrderId() to get it.
     * 
     * 
     * @return int|NULL|bool|array|string On failure returns:
     *      - -1 if duplicit order submition
     *      - NULL if there are no order or cart data
     *      - FALSE for validation errors (check also EshopOrderProduc::$errors)
     *      - array of adjusted products.
     * 
     *      On success returns new order number (string).
     */
    public function add() {
        // check for quick order
        $isQuickOrder = $this->isQuickOrder();
        // load checkout wizard to retrieve checkout data
        $this->loadCheckoutWizard();
        $Cart = $this->loadModel('EshopCart', true);
        // check for duplicit order submition, see EshopCart::isOrdered() phpDoc
        if ($Cart->isOrdered()) {
            $this->close();
            return -1;
        }
        // check if there are any cart and order data
        if (
            !$this->Checkout->getParameterAllSteps('data')
            || $Cart->isEmpty()
        ) {
            return null;
        }
        // validate checkout data 
        $steps = $this->Checkout->getPropertySteps();
        foreach ($steps as $step) {
            $stepData = $this->Checkout->getParameter('data', $step);
            $alternative = array($step);
            if ($isQuickOrder) {
                $alternative[] = $step . '_QuickOrder';
            }
            if (!$this->validate($stepData, array('alternative' => $alternative))) {
                $this->Checkout->setParameter('errors', $this->getErrors(), $step);
                return false;
            }
        }
        // get synchronized cart products
        $cartProducts = $Cart->getProductsDetails(array('synchronize' => true));
        if (($adjustedProducts = $Cart->getAdjustedProducts())) {
            return $adjustedProducts;
        }
        // get cart prices
        $cartPrices =$Cart->getPrices($cartProducts);
        // get checkout data
        $data = $this->Checkout->getParameterAllSteps('data', true);    
        
        // if not quick order, means there is a logged user then use its profile
        // to populate possibly missing user related order data
        if (!$isQuickOrder) {
            $user = App::getUser();
            $User = App::loadModel('App', 'User', true);
            $userProfile = $User->findFirst(array(
                'fields' => array(
                    "CONCAT(User.first_name, ' ', User.last_name) AS fullname",
                    'User.email',
                    'UserProfile.salutation',
                    'UserProfile.degree',
                    'UserProfile.company_fullname',
                    'UserProfile.company_id_number',
                    'UserProfile.company_tax_number',
                    'UserProfile.company_vat_number',
                    'UserProfile.street',
                    'UserProfile.city',
                    'UserProfile.country',
                    'UserProfile.zip',
                    'UserProfile.phone',
                    'UserProfile.fax',
                    'UserProfile.terms_and_conditions_agreement',
                    'UserProfile.newsletters_agreement',
                    'UserProfile.reference_from',
                    'UserProfile.adulthood_declaration',
                ),
                'conditions' => array('User.id' => $user['id']),
                'joins' => array(
                    array(
                        'model' => 'UserProfile',
                        'type' => 'left',
                    )
                ),
                'literals' => array('fields' => true),
            ));  
            $data = array_merge(
                $userProfile,
                $data
            );
        }
        // normalize advance rate 
        if (empty($data['advance_rate'])) {
            $data['advance_rate'] = null;
        }
        
        // if not company order then nullify company data
        if ($data['subject_type'] !== 'enum_company') {
            $data['company_fullname'] = null;
            $data['company_id_number'] = null;
            $data['company_tax_number'] = null;
            $data['company_vat_payer'] = null;
            $data['company_vat_number'] = null;
            $data['company_alcohol_distribution_licence_number'] = null;
        }
        
        // resolve delivery data
        $data = $this->getNewOrderDeliveryData($data);
        
        // get shipment name & price
        $shipment = array();
        if (!empty($data['shipment'])) {
            $Shipment = $this->loadModel('EshopShipmentMethod', true);
            $shipment = $Shipment->findFirst(array(
                'conditions' => array(
                    'EshopShipmentMethod.id' => $data['shipment']
                ),
            ));
            $shipment = $Shipment->getPrices($shipment, array(
                'productsPrices' => $cartPrices,
                'packageWeight' => $Cart->getWeight($cartProducts),
            ));
        }
        // get payment name & price
        $payment = array();
        if (!empty($data['payment'])) {
            $Payment = App::loadModel('Payment', 'PaymentMethod', true);
            $payment = $Payment->findFirst(array(
                'fields' => array(
                    'PaymentMethod.id', 
                    'PaymentMethod.name', 
                    'PaymentMethod.price',
                    'PaymentMethod.products_total_price_alternatives',
                ),
                'conditions' => array(
                    'PaymentMethod.id' => $data['payment']
                ),
            ));
            $payment = $Payment->getPrices($payment, array(
                'productsPrices' => $cartPrices
            ));
        }
        // get order prices
        $orderPrices = $this->getPrices(array(
            'productsPrices' => $cartPrices,
            'shipmentPrices' => $shipment,
            'paymentPrices' => $payment,
        ));
        
        // set value of specific field
        $specific = 0;
        if (
            array_key_exists('shipment_price_taxless', $shipment) 
            && $shipment['shipment_price_taxless'] === null
        ) {
            $specific |= self::SPECIFIC_SHIPMENT;
        }
        if (                
            array_key_exists('payment_price_taxless', $payment)
            && $payment['payment_price_taxless'] === null
        ) {
            $specific |= self::SPECIFIC_PAYMENT;
        }
        
//        // prepare files (comment this out if there are no files uploaded in checkout process)
//        $data = $this->prepareNewOrderFiles($data);
        
        // prepare new order data
        $voucher = $Cart->getVoucher();
        $orderData = array(
            'number' => null,
            'token' => Str::getRandom(40),
            'cart_id' => $Cart->getId(),
            'run_users_id' => Sanitize::value($user['id']),
            'specific' => $specific,
            'status' => 'enum_new_order',
            'payment_status' => 'enum_payment_none',
            'advance_rate' => $data['advance_rate'],
            'order_price_taxless' => $orderPrices['order_price_taxless'],
            'order_tax' => $orderPrices['order_tax'],
            'order_price_actual_taxless' => $orderPrices['order_price_actual_taxless'],
            'order_tax_actual' => $orderPrices['order_tax_actual'],
            'order_price_to_pay' => $orderPrices['order_price_to_pay'],
            'products_price_taxless' => $cartPrices['products_price_taxless'],
            'products_tax' => $cartPrices['products_tax'],
            'products_price_actual_taxless' => $cartPrices['products_price_actual_taxless'],
            'products_tax_actual' => $cartPrices['products_tax_actual'],
            'products_price_to_pay' => $cartPrices['products_price_to_pay'],
            'vouchers_discount' => $cartPrices['vouchers_discount'],
            'bonus_discount' => $cartPrices['bonus_discount'],
            'shipment_price_taxless' => Sanitize::value($shipment['shipment_price_taxless']),
            'shipment_tax' => Sanitize::value($shipment['shipment_tax']),
            'shipment_price_actual_taxless' => Sanitize::value($shipment['shipment_price_actual_taxless']),
            'shipment_tax_actual' => Sanitize::value($shipment['shipment_tax_actual']),
            'payment_price_taxless' => Sanitize::value($payment['payment_price_taxless']),
            'payment_tax' => Sanitize::value($payment['payment_tax']),
            'payment_price_actual_taxless' => Sanitize::value($payment['payment_price_actual_taxless']),
            'payment_tax_actual' => Sanitize::value($payment['payment_tax_actual']),
            'run_eshop_shipment_methods_id' => Sanitize::value($shipment['id']),
            'shipment_method_name' => Sanitize::value($shipment['name']),
            'run_payment_methods_id' => Sanitize::value($payment['id']),
            'payment_method_name' => Sanitize::value($payment['name']),
            'fullname' => $data['fullname'],
            'street' => $data['street'],
            'city' => $data['city'],
            'zip' => $data['zip'],
            'country' => $data['country'],
            'phone' => $data['phone'],
            'email' => $data['email'],
            'company_fullname' => Sanitize::value($data['company_fullname']),
            'company_id_number' => Sanitize::value($data['company_id_number']),
            'company_tax_number' => Sanitize::value($data['company_tax_number']),
            'company_vat_number' => Sanitize::value($data['company_vat_payer']) ? 
                Sanitize::value($data['company_vat_number']) : null,
            'delivery_fullname' => Sanitize::value($data['delivery_fullname']),
            'delivery_street' => Sanitize::value($data['delivery_street']),
            'delivery_city' => Sanitize::value($data['delivery_city']),
            'delivery_zip' => Sanitize::value($data['delivery_zip']),
            'delivery_country' => Sanitize::value($data['delivery_country']),
            'delivery_phone' => Sanitize::value($data['delivery_phone']),
            'delivery_email' => Sanitize::value($data['delivery_email']),
            'delivery_company_fullname' => Sanitize::value($data['delivery_company_fullname']),
            'comment' => Sanitize::value($data['comment']),
//            'bride_fullname' => $data['bride_fullname'],
//            'bride_address' => $data['bride_address'],
//            'groom_fullname' => $data['groom_fullname'],
//            'groom_address' => $data['groom_address'],
//            'wedding_ceremony_place_and_time' => $data['wedding_ceremony_place_and_time'],
//            'wedding_announcement_text' => $data['wedding_announcement_text'],
//            'wedding_announcement_files' => json_encode($data['wedding_announcement_files']),
            'terms_and_conditions_agreement' => Sanitize::value($data['terms_and_conditions_agreement'], 0),
            'prolonged_delivery_time_agreement' => Sanitize::value($data['prolonged_delivery_time_agreement'], 0),
            'newsletters_agreement' => Sanitize::value($data['UserProfile']['newsletters_agreement'], 0),
            'reference_from' => Sanitize::value($data['reference_from']),
            'eshop_voucher_code' => Sanitize::value($voucher['code']),
            //'adulthood_declaration' => $data['adulthood_declaration'],
            'lang' => App::$lang,
            'pickup_place' => Sanitize::value($data['pickup_place']),
            'pickup_place_id' => Sanitize::value($data['pickup_place_id']),
        );
        // prepare new order products
        $orderProducts = array();
        $vouchers = array();
        $number = 0;
        foreach ($cartProducts as $cartIndex => $cartProduct) {
            $orderProducts[$cartIndex] = array_merge($cartProduct, array(
                'id' => null, // ensure empty id ($cartProduct['id'] is id of EshopProduct record)
                'number' => ++$number,
                'run_eshop_orders_id' => null, // this is populated here below
                'run_eshop_products_id' => $cartProduct['id'],
                'reserved_amount' => $cartProduct['stock'] < $cartProduct['amount'] ? $cartProduct['stock'] : $cartProduct['amount'],
                'eshop_voucher_discount_rate' => null,  // resolved here below
                'eshop_voucher_code' => null,           // resolved here below
                'static_attributes' => json_encode($cartProduct['static_attributes']),
                'dynamic_attributes' => json_encode($cartProduct['dynamic_attributes']),
                'bestseller_relevancy' => $cartProduct['amount'],
            ));
            // set absolute discount vouchers code and keep the vouchers to actualize 
            // their applications here below
            if (!empty($cartProduct['voucher'])) {
                $orderProducts[$cartIndex]['eshop_voucher_code'] = $cartProduct['voucher']['code']; 
                $vouchers[] = $cartProduct['voucher'];
            }
            // discount rate vouchers are applied only on "product" items in cart, 
            // but not to "absolute discount voucher" items
            else {
                $orderProducts[$cartIndex]['eshop_voucher_discount_rate'] = Sanitize::value($cartProduct['eshop_voucher_discount_rate']); 
            }
        }
        
        // save order and its products
        $OrderProduct = $this->loadModel('EshopOrderProduct', true);
        $Product = $this->loadModel('EshopProduct', true);
        $writeTries = 3;
        do {
            $recoverableFailure = false;
            DB::startTransaction('EshopOrder_add');
            try {
                // repeat 10x if the validation fails on order number (that it is not unique)
                $attempts = 10;
                $orderNumber = $this->getNextNumber();
                do {
                    $orderData['number'] = $orderNumber;
                    try {
                        $result = $this->save($orderData, array(
                            // avoid reservation failure on order creation as there is no possible conflict
                            // which should be treated by reservation
                            'reserve' => false
                        ));
                    } 
                    catch (Throwable $e) {
                        // if DB error is not ER_DUP_KEYNAME (http://dev.mysql.com/doc/refman/5.0/en/error-messages-server.html#error_er_dup_keyname)
                        // then rethrow the exception
                        if ($e->getCode() != 1062) {
                            throw $e;
                        }
                        $result = false;
                        $orderNumber++;
                    }
                }
                while(
                    !$result
                    && (
                        count($this->errors) == 0
                        || 
                        count($this->errors) == 1
                        && !empty($this->errors['number'])
                    )
                    && --$attempts > 0
                );
                if (!$result) {
                    throw new Exception(__e(__FILE__, 'New order data save has failed'));
                }
                $orderId = $this->id;
                foreach($orderProducts as &$orderProduct) {
                    $orderProduct['run_eshop_orders_id'] = $orderId;
                    $result = $OrderProduct->save($orderProduct, array(
                        // avoid reservation failure on order creation as there is no possible conflict
                        // which should be treated by reservation
                        'reserve' => false
                    ));
                    if (!$result) {
                        throw new Exception(__e(__FILE__, 'New order product data save has failed'));
                    }
                    $orderProduct['id'] = $result['id'];
                }
                unset($orderProduct);
                // create relation between parent and child products
                foreach ($cartProducts as $cartIndex => $cartProduct) {
                    if (
                        isset($cartProduct['parent_index'])
                        && isset($orderProducts[$cartProduct['parent_index']])
                    ) {
                        $result = $OrderProduct->save(
                            array(
                                'id' => $orderProducts[$cartIndex]['id'],
                                'parent_id' => $orderProducts[$cartProduct['parent_index']]['id'],
                            ), 
                            array(
                                'normalize' => false,
                                'validate' => false,
                                // avoid reservation failure on order creation as there is no possible conflict
                                // which should be treated by reservation
                                'reserve' => false
                            )
                        );
                        if (!$result) {
                            throw new Exception(__e(__FILE__, 'New order product data save has failed'));
                        }
                    }
                }
                if ($Product->updateStock($cartProducts, 'remove') === false) {
                    throw new Exception(__e(__FILE__, 'New order product stock update has failed'));
                }
                // save profile data
                if (
                    !$isQuickOrder
                    && !empty($data['UserProfile'])
                ) {
                    $Profile = App::loadModel('App', 'UserProfile', true);
                    $result = $Profile->update($data['UserProfile'], array(
                        'allowFields' => array('newsletters_agreement', 'reference_from'),
                        'conditions' => array('run_users_id' => $user['id'])
                    ));
                    if (!$result) {
                        throw new Exception(__e(__FILE__, 'New order user profile update has failed'));
                    }
                }
                // check for newsletters agreement and save mailer contact
                if (!empty($data['UserProfile']['newsletters_agreement'])) {
                    // if addition of mailer contact fails then do quietly nothing,
                    // this happens e.g. in case that quick order user provided 2x the same email
                    // and mailer return validation false for email duplicity
                    try {
                        $MailerContact = App::loadModel('Mailer', 'MailerContact', true);
                        $MailerContact->saveFromUser(App::getUser('id'), $data);
                    } 
                    catch (Throwable $e) {}
                }
            }
            catch (Exception_DB_RecoverableDeadlock $e) {
                DB::rollbackTransaction('EshopOrder_add');
                if (--$writeTries <= 0) {
                    App::log('failedOrders', 'New order failure: ' . $e->getMessage(), array(
                        'var' => array(
                            'orderJson' => json_encode(array(
                                '$orderData' => $orderData, 
                                '$orderProducts' => $orderProducts
                            )),
                            'exception' => $e,
                            'session' => $_SESSION,
                        ),
                        'email' => true,
                    ));
                    $this->setError($e->getMessage());
                    $this->cleanUpFiles(false);
                    return false;
                }
                $recoverableFailure = true;
            }
            catch (Throwable $e) {
                DB::rollbackTransaction('EshopOrder_add');
                App::log('failedOrders', 'New order failure: ' . $e->getMessage(), array(
                    'var' => array(
                        'orderJson' => json_encode(array(
                            '$orderData' => $orderData, 
                            '$orderProducts' => $orderProducts
                        )),
                        'exception' => $e,
                        'session' => $_SESSION,
                    ),
                    'email' => true,
                ));
                $this->setError($e->getMessage());
                $this->cleanUpFiles(false);
                return false;
            }
        }
        while ($recoverableFailure);
        
        // if bonus applied then reset bonus points
        if ((float)$orderData['bonus_discount']) {
            App::loadModel('App', 'UserProfile');
            $UserProfile = new UserProfile();
            $UserProfile->resetBonusPoints();
        }
    
        DB::commitTransaction('EshopOrder_add');
        
        // add vouchers application (if any)
        App::loadModel('Eshop', 'EshopVoucher');
        $Voucher = new EshopVoucher();
        // - discount rate voucher
        if (($voucher = $Cart->getVoucher())) {
            $Voucher->addApplicationByCode($voucher['code']);
        }
        // - absolute discount vouchers
        if ($vouchers) {
            foreach ($vouchers as $voucher) {
                $Voucher->addApplicationByCode($voucher['code']);
            }
        }
        
        $this->close();
        
        $this->setNewOrderId($this->id);
        
        // report order to heureka overeno zakaznikmi
        try {
            $this->sendHeurekaVerifiedByClientsRequest($this->id, array(
                'itemIdField' => 'code',
                'productsConditions' => array(
                    //rblb//'EshopOrderProduct.stock >' => 0
                    'EshopOrderProduct.availability !=' => 'enum_presale',
                )
            ));
        } 
        catch (Throwable $e) {
            App::logError('Order report to "Heureka overeno zakaznikmi" has failed', array(
                'var' => $e,
                'email' => true,
            ));
        }
        
        
//        // export Pohoda xml order
//        try {
//            $Export = $this->loadModel('EshopExport', true);
//            $Export->exportPohodaOrderXmlToFtp($this->id);
//        }
//        catch(Throwable $e) {
//            App::logError('Generation of new order Pohoda XML has failed', array(
//                'var' => $e,
//                'email' => true,
//            ));
//        }
        
        return $orderData['number'];
    }
    
    /**
     * Clears / resets the order checkout data
     */
    public function clearCheckout() {
        $this->loadCheckoutWizard();
        $this->Checkout->clearProperties();
        $this->closeQuickOrder();
//        $this->cleanUpFiles(true); // (comment this out if there are no files uploaded in checkout process)
//        $this->cleanUpCheckoutFiles(); // (comment this out if there are no files uploaded in checkout process)
    }
    
    /**
     * Closes the order creation, means all order data are cleaned/deleted
     */
    protected function close() {
        $this->loadModel('EshopCart');
        $Cart = new EshopCart();
        // add voucher application (if any)
        if (($voucher = $Cart->getVoucher())) {
            App::loadModel('Eshop', 'EshopVoucher');
            $Voucher = new EshopVoucher();
            $Voucher->addApplicationByCode($voucher['code']);
        }
        // delete cart and order checkout data and close (possible) quick order session
        $Cart->clear();
        $this->clearCheckout();
    }
    
    /**
     * Sends new order email
     * 
     * @param int $orderId
     * @param array $options Following are available:
     *      - 'onceOnly' (bool) If TRUE then new order email is sent only once, 
     *          it means that if email has been sent already then the method does nothing
     *          and returns TRUE. Defaults to FALSE, it means new order email is 
     *          sent on each method call.
     * 
     * @return bool FALSE if provided order id does not exist or if the email send fails.
     */
    public function sendNewOrderEmail($orderId, $options = array()) {
        $defaults = array(
            'onceOnly' => false
        );
        $options = array_merge($defaults, $options);
        if (
            $options['onceOnly']
            && $this->findFieldBy('new_order_email_success', 'id', $orderId)
        ) {
            return true;
        }
        // get inserts
        $inserts = $this->getInserts($orderId);
        if (!$inserts) {
            return false;
        }
        // get attachments
        $attachments = array();
        $termsAndConditionsFile = File::normalizePath('userfiles/files/' . $this->getSetting('EshopOrder.termsAndConditionsFile'), 'pdf');
        $contractWithdrawalFile = File::normalizePath('userfiles/files/' . $this->getSetting('EshopOrder.contractWithdrawalFile'), 'pdf');
        if (is_readable(ROOT. DS . $termsAndConditionsFile)) {
            $attachments[] = $termsAndConditionsFile;
        }
        if (is_readable(ROOT. DS . $contractWithdrawalFile)) {
            $attachments[] = $contractWithdrawalFile;
        }
        $subject = $this->getSetting('EshopOrder.msgSubjectNewOrder');
        $body = $this->getSetting('EshopOrder.msgBodyNewOrder');            
        if ($this->findFieldBy('specific', 'id', $orderId)) {
            if ($this->getSetting('EshopOrder.msgSubjectNewSpecificOrder')) {
                $subject = $this->getSetting('EshopOrder.msgSubjectNewSpecificOrder');
            }
            if ($this->getSetting('EshopOrder.msgBodyNewSpecificOrder')) {
                $body = $this->getSetting('EshopOrder.msgBodyNewSpecificOrder');
            }
        }
        // send it
        try {
            $result = (bool)App::sendEmail(
                $body, 
                $inserts['userEmail'], 
                array(
                    'subject' => $subject,
                    'from' => array(
                        $this->getSetting('email.from') => App::getSetting('App', 'name')
                    ),
                    'cc' => $this->getSetting('email.cc'),
                    'bcc' => array_filter(array_map('trim', explode(',', $this->getSetting('email.bcc')))),
                    'inserts' => $inserts,
                    'attachments' => $attachments,
                    'embedImages' => true,
                )
            );
            
        }
        catch (Throwable $e) {
            App::logError('EshopOrder::sendNewOrderEmail()', array(
                'var' => $e,
                // if mail sending failed so do not try to send mail 
                'email' => false,
            ));
            $result = false;
        }
        // if the new order email has succeded then keep track of it
        if ($result) {
            $this->update(
                array('new_order_email_success' => 1),
                array(
                    'conditions' => array('id' => $orderId),
                    'normalize' => false,
                    'validate' => false,
                    'reserve' => false,
                )
            );
        }
        return $result;
    }
    
    /**
     * Sends successful payment email
     * 
     * @param int $orderId
     * @param array $options Following are available:
     *      - 'onceOnly' (bool) If TRUE then new order email is sent only once, 
     *          it means that if email has been sent already then the method does nothing
     *          and returns TRUE. Defaults to FALSE, it means new order email is 
     *          sent on each method call.
     * 
     * @return bool FALSE if provided order id does not exist or if the email send fails.
     */
    public function sendPaymentSuccessfulEmail($orderId, $options = array()) {
        $defaults = array(
            'onceOnly' => false
        );
        $options = array_merge($defaults, $options);
        if (
            $options['onceOnly']
            && $this->findFieldBy('successful_payment_email_success', 'id', $orderId)
        ) {
            return true;
        }
        $inserts = $this->getInserts($orderId);
        if (!$inserts) {
            return false;
        }
        try {
            $result = (bool)App::sendEmail(
                $this->getSetting('EshopOrder.msgBodyPaymentSuccessful'), 
                $inserts['userEmail'], 
                array(
                    'subject' => $this->getSetting('EshopOrder.msgSubjectPaymentSuccessful'),
                    'from' => array(
                        $this->getSetting('email.from') => App::getSetting('App', 'name')
                    ),
                    'cc' => $this->getSetting('email.cc'),
                    'inserts' => $inserts,
                    'embedImages' => true,
                )
            );
        }
        catch (Throwable $e) {
            App::logError('EshopOrder::sendPaymentSuccessfulEmail()', array(
                'var' => $e,
                'email' => false,
            ));
            $result = false;
        }
        // if the new order email has succeded then keep track of it
        if ($result) {
            $this->update(
                array('successful_payment_email_success' => 1),
                array(
                    'conditions' => array('id' => $orderId),
                    'normalize' => false,
                    'validate' => false,
                    'reserve' => false,
                )
            );
        }
        return $result;
    }
    
    /**
     * Sends failed payment email
     * 
     * @param int $orderId
     * @return bool FALSE if provided order id does not exist or if the email send fails.
     */
    public function sendPaymentFailedEmail($orderId) {
        $inserts = $this->getInserts($orderId);
        if (!$inserts) {
            return false;
        }
        try {
            return (bool)App::sendEmail(
                $this->getSetting('EshopOrder.msgBodyPaymentFailed'), 
                $inserts['userEmail'], 
                array(
                    'subject' => $this->getSetting('EshopOrder.msgSubjectPaymentFailed'),
                    'from' => array(
                        $this->getSetting('email.from') => App::getSetting('App', 'name')
                    ),
                    'cc' => $this->getSetting('email.cc'),
                    'inserts' => $inserts,
                    'embedImages' => true,
                )
            );
        }
        catch (Throwable $e) {
            App::logError('EshopOrder::sendPaymentSuccessfulEmail()', array(
                'var' => $e,
                'email' => false,
            ));
            return false;
        }
    }
    
    /**
     * Sends internal email about payment status change
     * 
     * @param int $orderId
     * @return bool FALSE if provided order id does not exist or if the email send fails.
     */
    public function sendPaymentStatusChangedEmail($orderId) {
        $inserts = $this->getInserts($orderId);
        if (!$inserts) {
            return false;
        }
        try {
            return (bool)App::sendEmail(
                __a(__FILE__, 'Order :orderNumber: - Payment status has changed to :orderPaymentStatus:'), 
                $this->getSetting('email.from'), 
                array(
                    'subject' => __a(__FILE__, 'Order :orderNumber: - Payment status has changed to :orderPaymentStatus:'),
                    'from' => array(
                        $this->getSetting('email.from') => App::getSetting('App', 'name')
                    ),
                    'inserts' => $inserts,
                )
            );
        }
        catch (Throwable $e) {
            App::logError('EshopOrder::sendPaymentStatusChangedEmail()', array(
                'var' => $e,
                'email' => false,
            ));
            return false;
        }
    }
    
    /**
     * Creates list of order inserts. Following are accessible:
     * 
     *  'userName'
     *  'userFirstName'
     *  'userEmail'
     *  'userPhone'
     *  'userComment'
     *  'userAddress' // invoicing address
     *  'deliveryAddress'
     *  'orderNumber'
     *  'orderCreated'
     *  'orderProducts'
     *  'orderBonus'
     *  'orderTotal'
     *  'orderProductsTotal'
     *  'orderStatus'
     *  'orderPaymentStatus'
     *  'orderAdvanceRate'
     *  'orderAdvancePrice'
     *  'shipmentCost'
     *  'paymentMethod'
     *  'shipmentMethod'
     *  'termsAndConditionsAgreement'
     *  'prolongedDeliveryTimeAgreement'
     *  'bankAccount'
     *  'variableSymbol'
     *  'constantSymbol'
     *  'paymentUrl'
     *  'paymentLink'
     *  'ordersUrl'
     *  'companyInfo'
     *  'eshopName'
     *  'eshopUrl'
     *  'specificInfo'
     *  'specificItems'
     *  'invoiceNumber'
     *  'invoiceUrl' // populated only when invoice number is provided
     *  'voucherCode'
     *  'gdprUrl'
     *  'bonusPoints'
     *  'bonusPointsSummary'
     *  'electronicGiftCardInfo'
     * 
     * @param int $orderId Order id to create inserts for
     * @return array|bool Inserts list contaning pairs 'insertSearch' => 'insertReplace'
     *      Empty array if provided id order does not exist.
     */
    public function getInserts($orderId) {
        $order = $this->findFirstBy('id', $orderId);
        if (!$order) {
            return array();
        }
        
        $userFirstName = explode(' ', trim($order['fullname']));
        $userFirstName = reset($userFirstName);

        $Country = App::loadModel('App', 'Country', true);
        $order['country'] = $Country->findFieldBy('Country.name', 'iso_code_2', $order['country']);
        $order['delivery_country'] = $Country->findFieldBy('Country.name', 'iso_code_2', $order['delivery_country']);
        
        // @deparecated - normalize old orders data (see the method phpDoc)
        $order = $this->getDeliveryData($order);
        
        $Payment = App::loadModel('Payment', 'PaymentMethod', true);
        $payment = $Payment->findFirstBy('id', $order['run_payment_methods_id'], array(
            'fields' => array(
                'PaymentMethod.pid',
                'PaymentMethod.online',
            ),
        ));
        
        // set customer address
        $customerAddress = $order['fullname'] . '<br/>'
            . $order['street'] . '<br/>'
            . trim($order['zip'] . ' ' . $order['city']) . '<br/>'
            . $order['country'] . '<br/>';
        if (
            !empty($order['company_fullname'])
            && !empty($order['company_id_number'])
            && !empty($order['company_tax_number'])
        ) {
            $customerAddress = $order['company_fullname'] 
                . '<br/>' . $customerAddress
                . __(__FILE__, 'Company id') . ': ' . $order['company_id_number'] . '<br/>'
                . __(__FILE__, 'Tax number') . ': ' . $order['company_tax_number'] . '<br/>';
            if (!empty($order['company_vat_number'])) {
                $customerAddress .= __(__FILE__, 'VAT number') . ': ' . $order['company_tax_number'] . '<br/>';
            }
        }
                
        // set delivery address
        $deliveryAddress = $order['delivery_fullname'] . '<br/>'
            . $order['delivery_street'] . '<br/>'
            . trim($order['delivery_zip'] . ' '. $order['delivery_city']) . '<br/>'
            . $order['delivery_country'] . '<br/>';
        if (!empty($order['delivery_company_fullname'])) {
            $deliveryAddress = $order['delivery_company_fullname'] 
                . '<br/>' . $deliveryAddress;
        }
        
        // set company info
        $companyInfo = '';
        if ($order['company_fullname']) {
            $companyInfo = $order['company_fullname'];
            if ($order['company_id_number']) {
                $companyInfo .= ', ' . __(__FILE__, 'Company id') . ': ' . $order['company_id_number'];
            }
            if ($order['company_tax_number']) {
                $companyInfo .= ', ' . __(__FILE__, 'Tax number') . ': ' . $order['company_tax_number'];
            }
            if ($order['company_vat_number']) {
                $companyInfo .= ', ' . __(__FILE__, 'VAT number') . ': ' . $order['company_vat_number'] . 
                    ' (' . __(__FILE__, 'VAT payer') . ')';
            }
        }
        
        $paymentUrl = $this->getPaymentUrl($order['id']);
        $paymentLink = '';
        if ($payment && $payment['online'] && $this->isPayable($order['id'])) {
            $paymentLink = __(__FILE__, 'Click the following link to pay for your order (please ignore this if you have paid already).');
            $paymentLink .= '<br/>';
            $paymentLink .= '<a href="' . $paymentUrl . '" class="payment-link payment-link-' . $payment['pid'] . '">' . __(__FILE__, 'Pay now') . '</a>';
        }
        if ($order['specific']) {
            if  ($payment['online']) {
                $paymentLink = '<b>' . __(__FILE__, 'We will send the payment link together with the resulting order price.') . '</b>';
            }
            else {
                $paymentLink = '<b>' . __(__FILE__, 'Pay the order only when we send you the final price.') . '</b>';
            }
        }
        $paymentMethod = $order['payment_method_name'];
        if ($payment && $payment['online'] && $this->isPayable($order['id'])) {
            $paymentMethod = sprintf('<a href="%s">%s</a>', $paymentUrl, $paymentMethod);
        }
        
        $advanceRate = '';
        $advancePrice = '';
        if ($order['advance_rate'] && $order['advance_rate'] < 100) {
            $advanceRate = App::formatNumber($order['advance_rate']) . '%';
            $advancePrice = Number::applyRate($order['order_price_to_pay'], $order['advance_rate'], 2);
            $advancePrice = Eshop::formatPrice($advancePrice);
        }
        
        $Shipment = $this->loadModel('EshopShipmentMethod', true);
        $specificItems = '';
        $specificInfo = '';
        if ($order['specific']) {
            if ($order['shipment_price_taxless'] === null) {
                if ($Shipment->hasAbroadDelivery($order['delivery_country'] ?: $order['country'])) {
                    $specificItems .= __(__FILE__, 'Delivery to abroad') . ' ';
                }
                $specificItems .= __(__FILE__, 'Specific shipment price') . '<br/>';
            }
            if ($order['payment_price_taxless'] === null) {
                $specificItems .= __(__FILE__, 'Specific payment price') . '<br/>';
            }
            // remove tail '<br/>'
            $specificItems = mb_substr($specificItems, 0, -5, 'UTF-8');
            $specificInfo = 
                '<br/><br/>' . 
                __(__FILE__, 'Important: The amount listed here is not the final price of your order. For example, the amount may be increased by the shipping cost if you have provided a foreign delivery address. We will let you know the final price of your order by e-mail message.');
        }
        
        $orderTotal = Eshop::formatPrice($order['order_price_to_pay']);
        $orderTotalInfo = $orderTotal;
        $orderTotalInfo .= ' (' . __(__FILE__, 'with VAT') . ')';
        if ($order['specific']) {
            $orderTotalInfo .= ' - ' . __(__FILE__, 'The price is stated without shipping! Do not send us the money for now.');
        }
        
        $shipmentCost = Eshop::formatPrice($order['shipment_price_actual_taxless'] + $order['shipment_tax_actual']);
        $shipmentCostInfo = $shipmentCost;
        if ($order['specific']) {
            $shipmentCostInfo = __(__FILE__, 'We will inform you about the resulting postage price.');
        }
        
        $ordersUrl = App::getUrl(array(
            'locator' => App::getContentLocatorByPid('Eshop.EshopOrders.index'),
            'absolute' => true,
            'protocol' => 'https',
        ));
        
        $invoiceUrl = null;
        if (!empty($order['invoice_pdf'])) {
            $invoiceUrl = $this->getFileFieldUrlPath('invoice_pdf', array(
                'file' => $order['invoice_pdf'],
                'timestamp' => false,
            ));
        }
        
        $electronicGiftCardInfo = '';
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $this->loadModel('EshopOrderProduct');
        $OrderProduct = new EshopOrderProduct();
        if (
            ($giftCardIds = $Product->findList(array(
                'conditions' => array('is_gift_card' => true),
                'fields' => array('id'),
            )))
            && ($orderedGiftCards = $OrderProduct->find(array(
                'conditions' => array(
                    'EshopOrderProduct.run_eshop_orders_id' => $orderId,
                    'EshopOrderProduct.run_eshop_products_id' => $giftCardIds,
                ),
                'fields' => array('EshopOrderProduct.static_attributes')
            )))
            && ($orderedElectronicGiftCards = array_filter($orderedGiftCards, function($giftCard) {
                $attributes = json_decode($giftCard['static_attributes'], true);
                return !empty($attributes['variant']) && $attributes['variant'] === 'electronic';
            }))
        ) {
            if (count($orderedElectronicGiftCards) === 1) {
                $electronicGiftCardInfo = __(__FILE__, 'Elektronická darovacia karta: Po zaplatení objednávky Vám do e-mailu pošleme kód zakúpenej elektronickej darovacej karty aj s pokynmi, ako ju môžete použiť') . '<br/>';
            }
            else {
                $electronicGiftCardInfo = __(__FILE__, 'Elektronické darovacie karty: Po zaplatení objednávky Vám do e-mailu pošleme kódy zakúpených elektronických darovacích kariet aj s pokynmi, ako ich môžete použiť') . '<br/>';
            }
        }
        
        App::loadModel('App', 'UserProfile');
        $Profile = new UserProfile();
        
        // create inserts
        return array(
            'userName' => $order['fullname'],
            'userFirstName' => $userFirstName,
            'userEmail' => $order['email'],
//            'emailDeliveryResult' => $order['new_order_email_success'] ? '' : __(__FILE__, 'New order email delivery has failed'),
            'userPhone' => $order['phone'],
            'userComment' => nl2br($order['comment']),
            'userAddress' => $customerAddress,
            'deliveryAddress' => $deliveryAddress,
            'deliveryPhone' => $order['delivery_phone'],
            'orderNumber' => $order['number'],
            'orderCreated' => date('j.n.Y H:i', strtotime($order['created'])),
            'orderProducts' => $this->getProductsInfoString($orderId),
            'orderBonus' => '-' . Eshop::formatPrice($order['bonus_discount']),
            'orderTotal' => $orderTotal,
            'orderTotalInfo' => $orderTotalInfo,
            'orderProductsTotal' => Eshop::formatPrice($order['products_price_to_pay']),
            'orderStatus' => __(__FILE__, $order['status']),
            'orderPaymentStatus' => __(__FILE__, $order['payment_status']),
            'orderAdvanceRate' => $advanceRate,
            'orderAdvancePrice' => $advancePrice,
            'shipmentCost' => $shipmentCost,
            'shipmentCostInfo' => $shipmentCostInfo,
            'paymentMethod' => $paymentMethod,
            'shipmentMethod' => $order['shipment_method_name'],
            'termsAndConditionsAgreement' => $order['terms_and_conditions_agreement'] ? __(__FILE__, 'Yes') : __(__FILE__, 'No'),
            'prolongedDeliveryTimeAgreement' => $order['prolonged_delivery_time_agreement'] ? __(__FILE__, 'Yes') : __(__FILE__, 'No'),
            'deliveryTime' => ($deliveryTime = $this->getDeliveryTime($orderId)),
            'deliveryDate' => date('j.n.Y', ($deliveryTime) * 60 * 60 * 24 + time()),
            'bankAccount' => $this->getSetting('EshopOrder.bankAccount'),
            'variableSymbol' => $order['number'],
            'constantSymbol' => $this->getSetting('EshopOrder.bankTransferConstantSymbol'),
            'paymentUrl' => $paymentUrl,
            'paymentLink' => $paymentLink,
            'ordersUrl' => $ordersUrl,
            'ordersLink' => sprintf(
                '<a href="%s">%s</a>', 
                $ordersUrl,
                __(__FILE__, 'Moje objednávky')
            ),
            'companyInfo' => $companyInfo,
            'eshopName' => App::getSetting('App', 'name'),
            'eshopUrl' => App::$urlBaseWithRoot,
            'specificItems' => $specificItems,
            'specificInfo' => $specificInfo,
            'invoiceUrl' => $invoiceUrl,
            'voucherCode' => $order['eshop_voucher_code'],
            'gdprUrl' => App::getContentUrlByPid('App.privacyPolicyInfo', array(
                'absolute' => true,
            )),
            'bonusPoints' => $Profile->getBonusPoints(
                // pass here 0 instead of NULL to avoid set bonus point s of actually logged user
                $order['run_users_id'] ?? 0 
            ),
            'bonusPointsSummary' => App::loadControllerAction('App', 'UserProfiles', 'bonusPointsSummary', array(
                // pass here 0 instead of NULL to avoid set bonus points of actually logged user
                'userId' => $order['run_users_id'] ?? 0 
            )),
            'electronicGiftCardInfo' => $electronicGiftCardInfo,
            'oversizedMessage' => $this->hasOversizedProducts($orderId) 
                ? $this->getSetting('EshopOrder.oversizedProductMessage') : ''
        );
    }
    
    /**
     * Gets payment url for provided order id.
     * Returned URL is absolute (starts by http://...).
     *
     * @param int $orderId
     * @param array $options Follwing are available:
     *      - 'paymentId' (int) Id of payment method to be used. Defaults to first
     *          found online payment method (card methods are prefered) - if any.
     *      - 'forcePaymentId' (bool) Considered only if paymentId is provided. 
     *          If FALSE then specified paymentId is used only in case that order
     *          has no payment method attributed yet or attributed method is not active or online. 
     *          If TRUE then specified paymentId is used in any case. Defaults to FALSE.
     *
     * @return string
     */
    public function getPaymentUrl($orderId, $options = array()) {
        static $fallbackOnlinePaymentId = false;
        if ($fallbackOnlinePaymentId === false) {
            $Payment = App::loadModel('Payment', 'PaymentMethod', true);
            $fallbackOnlinePaymentId = $Payment->findField('id', array(
                'conditions' => array(
                    'PaymentMethod.online' => true,
                    'PaymentMethod.active' => true,
                ),
                'order' => 'PaymentMethod.card_payment DESC',
            ));
        }
        static $paymentLocator = null;
        if (empty($paymentLocator)) {
            if (!($paymentLocator = App::getContentLocatorByPid('Eshop.EshopOrders.pay'))) {
                $paymentLocator = '/mvc/Eshop/EshopOrders/pay/';
            }
        }
        $options = array_merge(array(
            'paymentId' => $fallbackOnlinePaymentId,
            'forcePaymentId' => false,
        ), $options);
        $order = $this->findFirst(array(
            'fields' => array('token', 'run_payment_methods_id'),
            'conditions' => array('id' => $orderId)
        ));
        if (empty($options['paymentId'])) {
            $options['paymentId'] = $order['run_payment_methods_id'];
        }
        if (empty($options['paymentId'])) {
            $Payment = App::loadModel('Payment', 'PaymentMethod', true);
            $options['paymentId'] = $Payment->findField('id', array(
                'conditions' => array(
                    'pid' => 'paypalStandard',
                    'active' => true,
                )
            ));
        }
        $args = array($orderId, $order['token']);
        if (!empty($options['paymentId'])) {
            $args[] = $options['paymentId'];
            if (!empty($options['forcePaymentId'])) {
                $args[] = 1;
            }
        }
        return App::getUrl(array(
            'locator' => $paymentLocator,
            'args' => $args,
            'absolute' => true,
            'protocol' => 'https',
        ));
    }
    
    /**
     * Returns order products details. Retruned product details are identic to 
     * EshopProduct::getDetails() output + following fields are set according 
     * stored values of ordered products:
     *      - amount
     *      - price_actual_taxed
     *      - price_taxed
     *      - total_price_actual_taxless
     *      - total_price_taxed
     *      - total_price_actual_taxed
     *      - total_price_taxed
     *      - order_item_number
     *      - disponibility
     * 
     * Means that product prices are set according to prices they heve been ordered for.
     * 
     * ATTENTION: Be aware that the same product (accordint to id) can occure more than once
     * in the output products list! Each occurance is distibguished by attribute
     * values from others. Array keys are set to EshopOrderProduct.id.
     * 
     * @param int $orderId
     * @param array $options See options of EshopProduct::getDetails() and EshopOrderProduct::getDetails()
     * 
     * @return array Order products details 
     * 
     * @todo - savings rate and savings are not set here.
     */
    public function getProductsDetails($orderId, $options = array()) {
        $OrderProduct = $this->loadModel('EshopOrderProduct', true);
        $orderProductIds = $OrderProduct->findList(array(
            'key' => 'EshopOrderProduct.id',
            'fields' => 'EshopOrderProduct.id',
            'conditions' => array(
                'EshopOrderProduct.run_eshop_orders_id' => $orderId,
            )
        ));
        if (empty($orderProductIds)) {
            return array();
        }
        return $OrderProduct->getDetails($orderProductIds, $options);
    }
    
    /**
     * Return order products info string which can be used as insert into order mails
     * 
     * @param int $orderId
     * @param array $options Following are available:
     *      - 'productNameMaxLength' (integer)
     *      - 'manufacturerNameMaxLength' (integer)
     *      - 'authorsNameMaxLength' (integer)
     *      - 'type' (string) Possible values are 'plain' and 'table'
     * 
     * @return string
     */
    public function getProductsInfoString($orderId, $options = array()) {
        $defaults = array(
            'productNameMaxLength' => 50,
            'manufacturerNameMaxLength' => 50,
            'authorsNameMaxLength' => 50,
            'type' => 'table'
        );
        $options = array_merge($defaults, $options);
        
        // retrieve products and their details
        $products = $this->getProductsDetails($orderId, array(
            'getManufacturer' => true,
            'getAuthors' => true,
        ));
        $Product = $this->loadModel('EshopProduct', true);
        $disponibilities = $Product->getPropertyDisponibilities();
        $infoString = '';
        if ($options['type'] === 'table') {
            $infoString = "<table style=\"font-size: inherit !important;\">\n";
        }
        foreach ($products as $product) {
            // truncate product name
            $productName = Str::truncate($product['name'], $options['productNameMaxLength']);
            // truncate manufacturer name
            $manufacturerName = Str::truncate(Sanitize::value($product['EshopManufacturer']['name']), $options['manufacturerNameMaxLength']);
            $authorsName = '';
            if ($product['authors']) {
                $authorsName = rtrim(Str::truncate(Sanitize::value($product['authors']), $options['authorsNameMaxLength']), ', ');
            }
            // find out disponibility
            $disponibilityName = Sanitize::value($disponibilities[$product['disponibility']]);
            // get attributes
            $attributes = '';
            // - static attributes
            $product['static_attributes'] = (array)json_decode($product['static_attributes'], true);
            foreach($product['static_attributes'] as $attrName => $attrValue) {
                // replace possible attr pids by attr values
                if (isset($product[$attrName]['values'][$attrValue])) {
                    $attrValue = $product[$attrName]['values'][$attrValue];
                }
                if ($attributes) {
                    $attributes .= ', ';
                }
                // variant attribute is general attribute which values should be clear without attribute name
                if ($attrName !== 'variant') {
                    $attributes .= __(__FILE__, $attrName) . ': ';
                }
                $attributes .= $attrValue ;
            }
            // - dynamic attributes
            $product['dynamic_attributes'] = (array)json_decode($product['dynamic_attributes'], true);
            foreach($product['dynamic_attributes'] as $attrName => $attrValue) {
                if ($attributes) {
                    $attributes .= ', ';
                }
                $attributes .= $attrName . ': ' . $attrValue ;
            }
            // build product insert string
            if ($options['type'] === 'plain') {
                $infoString .= '<strong>'.$product['amount'] . ' ' . __(__FILE__, 'itm') . '<br>' . $productName.'</strong>';
                if ($attributes) {
                    $infoString .= ', ' . $attributes;
                }
                if ($manufacturerName) {
                    $infoString .= '<br>' . $manufacturerName ;
                }
                $infoString .= ' (' . Eshop::formatPrice(
                        $product['price_actual_taxless'] + $product['tax_actual'])
                               . '/' . __(__FILE__, 'itm');
                //$infoString .= "; {$disponibilityName}";
                $infoString .= ') ' . __(__FILE__, 'subtotal') . ' ' . Eshop::formatPrice(
                        $product['amount'] * ($product['price_actual_taxless'] + $product['tax_actual']));
                // disponsibility
                if($product['disponibility'] == 'enum_presale') {
                    $infoString .=  ' | <span style="color:red">' . __($this, 'o termíne dodania tohto tovaru Vás budeme informovať') . '</span>';
                }
                $infoString .= '<br/><br/>';
            }
            else {
                $infoString .= "<tr style=\"font-size: inherit !important;\">\n";
                // ean
                $infoString .= 
                    '<td style="padding:5px 10px 5px 0;font-size: inherit !important;">' . 
                        $product['ean'] . 
                    "\n</td>";
                // amount
                $infoString .= 
                    '<td style="padding:5px 10px 5px 0;font-size: inherit !important;">-&nbsp;' . 
                        str_pad($product['amount'], 2, '0', STR_PAD_LEFT) . 
                        '&nbsp;' . __(__FILE__, 'itm') . 
                    "\n</td>";
                // price
                $infoString .= 
                    '<td style="padding:5px 10px 5px 0;text-align:right;font-size: inherit !important;">' . 
                        Eshop::formatPrice(
                            $product['price_actual_taxless'] + $product['tax_actual']
                        ) .
                    "\n</td>";
                // total price
                $infoString .=
                    '<td style="padding:5px 10px 5px 0;text-align:right;font-size: inherit !important;">spolu&nbsp;' .
                        Eshop::formatPrice(
                            $product['amount'] * ($product['price_actual_taxless'] + $product['tax_actual'])
                        ) .
                    "\n</td>";
                // tax rate
                $infoString .= 
                    '<td style="padding:5px 10px 5px 0;font-size: inherit !important;">DPH&nbsp;' . 
                        $product['tax_rate'] . '%' . 
                    "\n</td>";
                // product name
                $infoString .= 
                    '<td style="padding:5px 10px 5px 0;font-size: inherit !important;"><b>' . 
                        $productName . ($attributes ? ' (' . $attributes . ')' : '') .
                    "</b>\n</td>";
                // author and manufacturer
                $infoString .= '<td style="padding:5px 10px 5px 0;font-size: inherit !important;">';
                if ($authorsName) {  
                    $infoString .= '- ' . $authorsName;
                }
                if ($manufacturerName) {
                    $infoString .= $authorsName ? ', ' : '- ';
                    $infoString .= $manufacturerName;
                }
                $infoString .= "\n</td>";
                // availability
                $infoString .= '<td style="padding:5px 10px 5px 0;font-size: inherit !important;">'; 
                if ($disponibilityName) {
                    $infoString .= $disponibilityName . ' ';

                }
                if($product['disponibility'] == 'enum_presale') {
                    $infoString .=  '<span style="color:red">' . 
                        __($this, 'o termíne dodania tohto tovaru Vás budeme informovať') . 
                    '</span>';
                }
                $infoString .= "\n</td>";
                $infoString .= "\n</tr>";
            }
        }
        if ($options['type'] === 'table') {
            $infoString .= "\n</table>";
        }
        
        return $infoString;
    }
    
    /**
     * Sends a request to Heureka overeno zakaznikmi.
     * The overall process to implement "Heureka - overene zakaznikmi" is:
     * - activate the sevice for actual shop here http://sluzby.heureka.sk/sluzby/certifikat-spokojenosti-smlouva/
     * - set the value of 'EshopOrder.heurekaVerifiedByClientsKey' setting to key from http://sluzby.heureka.sk/sluzby/certifikat-spokojenosti/
     * - make a testing order to finish the activation (check result on http://sluzby.heureka.sk/sluzby/certifikat-spokojenosti/)
     * - after you get badge from heureka you can palce it on page. The HTML code can be downloaded from http://sluzby.heureka.sk/sluzby/certifikat-spokojenosti/
     * 
     * NOTE: Actual implementation details are on http://sluzby.heureka.sk/napoveda/ako-si-sluzbu-aktivovat/
     * 
     * @param int $orderId Order id to send the request for
     * @param array $options Following are available:
     *      - 'itemIdField' (string) Field name which is passed in heureka export in
     *          ITEM_ID XML tag. Defaults to 'id'.
     *      - 'productsConditions' (array) Order is reported to Heureka only in case
     *          that all products meet provided conditions here. This can be used
     *          to decide when it is good/convenient to send the report to heureka 
     *          and ask customer for rating and when not (e.g. in case of products
     *          with long delivery time or in presale which mostly returns bad 
     *          ratings). Here provided conditions are passed as 'conditions' option 
     *          to EshopOrderProduct::find(). Model EshopProduct is automatically 
     *          joined to this query. ATTENTION: Keep value of this option the same 
     *          also for method EshopOrder::getHeurekaConversionCode()! Defaults to NULL.
     * 
     * @throws Exception on invalid order id or on failure of heureka request
     */
    public function sendHeurekaVerifiedByClientsRequest($orderId, $options = array()) {
        $defaults = array(
            'itemIdField' => 'id',
            'productsConditions' => null,
        );
        $options = array_merge($defaults, $options);
        $itemIdField = $options['itemIdField'];
        
        $heurekaKey = App::getSetting('Eshop', 'EshopOrder.heurekaVerifiedByClientsKey');
        if (
            empty($heurekaKey)
            ||
            ON_LOCALHOST
        ) {
            return;
        }
        
        // get order user email and validate order existence
        $order = $this->findFirstBy('id', $orderId, array('fields' => array(
            'number',
            'email',
        )));
        if (!$order) {
            throw new Exception(__e(__FILE__, 'Invalid order id %s', $orderId));
        }
        // get order products
        $OrderProduct = $this->loadModel('EshopOrderProduct', true);
        $products = $OrderProduct->find(array(
            'joins' => array(
                array(
                    'model' => 'EshopProduct',
                    'left' => 'left',
                ),
            ),
            'conditions' => array(
                'run_eshop_orders_id' => $orderId
            ),
            'fields' => array(
                'EshopOrderProduct.static_attributes',
                'EshopOrderProduct.dynamic_attributes',
                'EshopProduct.name',
                'EshopProduct.' . $itemIdField,
            ),
            'separate' => array('EshopProduct'),
        ));
        if ($options['productsConditions']) {
            $productsConditions = array(
                'EshopOrderProduct.run_eshop_orders_id' => $orderId,
                $options['productsConditions'],
            );
            $convenientProducts = $OrderProduct->find(array(
                'joins' => array(
                    array(
                        'model' => 'EshopProduct',
                        'type' => 'left',
                    ),
                ),
                'conditions' => $productsConditions,
                'fields' => array('EshopOrderProduct.id')
            ));
            if (count($products) !== count($convenientProducts)) {
                return;
            }
        }
        
        // generate sequence of fictive order numbers as not all orders are reported
        // to heureka (if option 'productsConditions' is set)
        $order['number'] = $this->getOrderNumberForHeureka($orderId);
        
        // send request to heureka overeno zakaznikmi
        $this->loadVendor('HeurekaOvereno');
        try {
            $overeno = new HeurekaOvereno($heurekaKey, HeurekaOvereno::LANGUAGE_SK);
            // set customer email - MANDATORY
            $overeno->setEmail($order['email']);
            foreach ($products as $product) {
                
                // add product selective attributes to product name
                $attributes = '';
                // - static attributes
                $product['static_attributes'] = json_decode($product['static_attributes'], true);
                foreach($product['static_attributes'] as $attrName => $attrValue) {
                    if ($attributes) {
                        $attributes .= ', ';
                    }
                    $attributes .= $attrName . ': ' . $attrValue ;
                }
                // - dynamic attributes
                $product['dynamic_attributes'] = json_decode($product['dynamic_attributes'], true);
                foreach($product['dynamic_attributes'] as $attrId => $attrValue) {
                    // todo...
                }
                if ($attributes) {
                    $product['EshopProduct']['name'] .= ', ' . $attributes;
                }
                
                /**
                 * Products names should be provided in UTF-8 encoding. The service can handle
                 * WINDOWS-1250 and ISO-8859-2 if necessary             
                 */
                $overeno->addProduct($product['EshopProduct']['name']);
                /**
                 * And/or add products using item ID
                 */
                $overeno->addProductItemId($product['EshopProduct'][$itemIdField]);
            }
            // add order ID - BIGINT (0 - 18446744073709551615)
            // mark testing orders from localhost by addding 1000000000
            if (ON_LOCALHOST) {
                $order['number'] += 1000000000;
            }
            $overeno->addOrderId($order['number']);
            // send request
            $overeno->send();
        } 
        catch (HeurekaOverenoException $e) {
            throw $e;
        }
    }
    
    /**
     * Returns sequentional order number which can be used to report order (with
     * provided id) to heureka. The EshopOrder.number cannot be used as not all orders  
     * are reported to heureka if option 'productsConditions' is set - see methods 
     * EshopOrder::sendHeurekaVerifiedByClientsRequest() and ::getHeurekaConversionCode()
     * 
     * @param int $orderId Order id to report to heureka
     * @param array $options Options internally passed to App::reserveProcessing() 
     */
    protected function getOrderNumberForHeureka($orderId, $options = array()) {
        // generate sequence of fictive order numbers as not all orders are reported
        // to heureka (if option 'productsConditions' is set)
        App::reserveProcessing('EshopOrder_getOrderNumberForHeureka', $options);
        $orderNumbers = App::getSetting('Eshop', 'EshopOrder.heurekaOrderNumbers', array(
            'translate' => false,
        ));
        $save = false;
        if (!$orderNumbers) {
            $orderNumber = $this->findFieldBy('number', 'id', $orderId);
            if (!$orderNumber) {
                throw new Exception(__e(__FILE__, 'Invalid order id %s', $orderId));
            }
            $orderNumbers = array($orderId => (int)$orderNumber);
            $save = true;
        }
        else {
            $orderNumbers = json_decode($orderNumbers, true);
            if (!is_array($orderNumbers)) {
                throw new Exception(__e(__FILE__, 'Invalid value of setting Eshop.EshopOrder.heurekaOrderNumbers (must be array)'));
            }
        }
        if (array_key_exists($orderId, $orderNumbers)) {
            $orderNumber = $orderNumbers[$orderId];
        }
        else {
            $orderNumber = end($orderNumbers) + 1;
            $orderNumbers[$orderId] = $orderNumber;
            $save = true;
        }
        if ($save) {
            // keep track only for last 500 orders
            $orderNumbers = array_slice($orderNumbers, -500, 500, true);
            $orderNumbers = json_encode($orderNumbers);
            App::setSetting('Eshop', 'EshopOrder.heurekaOrderNumbers', $orderNumbers, array(
                'translate' => false,
            ));
        }
        App::unreserveProcessing('EshopOrder_getOrderNumberForHeureka');
        return $orderNumber;
    }
    
    /**
     * Sets delivery data in order data. Following fields are updated to order data:
     *      'delivery_company_fullname'
     *      'delivery_fullname'
     *      'delivery_street'
     *      'delivery_city'
     *      'delivery_zip'
     *      'delivery_country'
     *      'delivery_phone'
     *      'delivery_email'
     *      'pickup_place'
     * 
     * If there is specified 'pickup_place' then delivery data are populated according 
     * pickup place.
     * 
     * If shipmentMethodPid is 'localPickup' delivery data are populated according 
     * settings Eshop.address.{addressItem}
     * 
     * @param array $order New order data retrieved by EshopOrder::$Checkout::getParameterAllSteps("data", true).
     * 
     * @return array New order data with additional fields mentioned here above
     * 
     * @throws Exception if data seems to not be new order data
     */
    public function getNewOrderDeliveryData($order) {
        if (
            !isset($order['deliveryAddress'])
            || empty($order['shipment'])
        ) {
            throw new Exception(__e(__FILE__, 'Please provide here data returned by EshopOrder::$Checkout::getParameterAllSteps("data", true)'));
        }
        // set shipment method
        $order['run_eshop_shipment_methods_id'] = $order['shipment'];
        $Shipment = $this->loadModel('EshopShipmentMethod', true);
        $options['shipmentMethodPid'] = $Shipment->findFieldBy('pid', 'id', $order['run_eshop_shipment_methods_id']);
        
        // local pickup
        if ($options['shipmentMethodPid'] === 'localPickup') {
            $order['delivery_fullname'] = $this->getSetting('address.companyFullname');
            $order['delivery_street'] = $this->getSetting('address.street');
            $order['delivery_city'] = $this->getSetting('address.city');
            $order['delivery_zip'] = $this->getSetting('address.zip');
            $order['delivery_country'] = $this->getSetting('address.country');
            $order['pickup_place'] = $order['delivery_fullname'] . ', ' . 
                $order['delivery_street'] . ', ' . 
                trim($order['delivery_zip'] . ' ' . $order['delivery_city']) . ', ' . 
                $order['delivery_country'];            
        }
        // resolve GEIS POINT pickup place
        elseif ($Shipment->isGeisPoint($options['shipmentMethodPid'])) {
            $placeId = $order['pickup_place'][$order['shipment']];
            if (($place = $Shipment->getPickupPlace('geisPoint', $placeId))) {
                $order['delivery_fullname'] = $place['place'] . ' (GEIS POINT)';
                $order['delivery_street'] = $place['street'];
                $order['delivery_city'] =$place['city'];
                $order['delivery_zip'] = $place['zip'];
                $order['delivery_country'] = $place['country'];
                $order['pickup_place'] = $order['delivery_fullname'] . ', ' . 
                    $order['delivery_street'] . ', ' . 
                    trim($order['delivery_zip'] . ' ' . $order['delivery_city']) . ', ' . 
                    $order['delivery_country'];
                if (!empty($place['url'])) {
                    $order['pickup_place'] .= ' (' . $place['url'] . ')'; 
                }
                $order['pickup_place_id'] = $placeId;
            }
        }
        // resolve Zasielkovna pickup place
        elseif ($Shipment->isZasielkovna($options['shipmentMethodPid'])) {
            $placeId = $order['pickup_place'][$order['shipment']];
            if (($place = $Shipment->getPickupPlace('zasielkovna', $placeId))) {
                $order['delivery_fullname'] = $place['place'] . ' (ZÁSIELKOVŇA)';
                $order['delivery_street'] = $place['street'];
                $order['delivery_city'] =$place['city'];
                $order['delivery_zip'] = $place['zip'];
                $order['delivery_country'] = $place['country'];
                $order['pickup_place'] = $order['delivery_fullname'] . ', ' . 
                    $order['delivery_street'] . ', ' . 
                    trim($order['delivery_zip'] . ' ' . $order['delivery_city']) . ', ' . 
                    $order['delivery_country'];
                if (!empty($place['url'])) {
                    $order['pickup_place'] .= ' (' . $place['url'] . ')'; 
                }                
                $order['pickup_place_id'] = $placeId;
            }
        }
        // resolve manually provided pickup place
        elseif (!empty($order['pickup_place'][$order['shipment']])) {
            $place = $order['pickup_place'][$order['shipment']];
            $placeParts = array_map('trim', explode(',', $place));
            $city = array_shift($placeParts);
            $fullname = 'ZÁSIELKOVŇA';
            if (count($placeParts) > 1) {
                $fullname = array_pop($placeParts);
            }
            $order['delivery_fullname'] = $fullname;
            $order['delivery_street'] = implode(', ', $placeParts);
            $order['delivery_city'] = $city;
            $order['delivery_zip'] = null;
            $order['delivery_country'] = $this->getSetting('address.country');
            $order['pickup_place'] = $place;
        }
        // use invoicing address as delivery address
        elseif ($order['deliveryAddress'] !== 'otherAddress') {
            $order['delivery_company_fullname'] = null;
            if (
                $order['subject_type'] === 'enum_company'
                && !empty($order['company_fullname'])
            ) {
                $order['delivery_company_fullname'] = $order['company_fullname'];
            }
            $order['delivery_fullname'] = $order['fullname'];
            $order['delivery_street'] = $order['street'];
            $order['delivery_city'] = $order['city'];
            $order['delivery_zip'] = $order['zip'];
            $order['delivery_country'] = $order['country'];
            $order['pickup_place'] = null;
        }
        
        // resolve delivery phone and email
        if (
            $order['deliveryAddress'] !== 'otherAddress'
            || empty($order['delivery_phone'])
        ) {
            $order['delivery_phone'] = $order['phone'];
        }
        else {
            $order['delivery_phone'] = $order['delivery_phone'];
        }
        if (
            $order['deliveryAddress'] !== 'otherAddress'
            || empty($order['delivery_email'])
        ) {
            $order['delivery_email'] = $order['email'];
        }
        else {
            $order['delivery_email'] = $order['delivery_email'];
        }
        
        return $order;
    }
        
    /**
     * @deprecated - This method serves only to normalize old orders data. New orders
     * using method EshopOrder::getNewOrderDeliveryData() have data already normalized
     * and it is just enough to retrieve them from DB
     * 
     * Sets delivery data in order data. Following fields are updated to order data:
     *      'delivery_company_fullname'
     *      'delivery_fullname'
     *      'delivery_street'
     *      'delivery_city'
     *      'delivery_zip'
     *      'delivery_country'
     *      'delivery_phone'
     *      'delivery_email'
     * 
     * If there is specified 'pickup_place' then delivery data are populated according 
     * pickup place.
     * 
     * If shipmentMethodPid is 'localPickup' delivery data are populated according 
     * settings Eshop.address.{addressItem}
     * 
     * @param array $order Order record containing at least user ('fullname', 'street', ...),
     *          delivery ('delivery_fullname', ...), 'company_fullname' and 'run_eshop_shipment_methods_id' fields.
     *          Field 'pickup_place' must be contained only if present in EshopOrder::$schema.
     *          Field 'run_eshop_shipment_methods_id' must be contained only if option 
     *          'shipmentMethodPid' is not provided.
     * @param array $options
     *          - 'shipmentMethodPid' (string)
     * 
     * @return array Order array with additional fields mentioned here above
     * @throws Exception
     */
    public function getDeliveryData($order, $options = array()) {
        $defaults = array(
            'shipmentMethodPid' => false,
        );
        $options = array_merge($defaults, $options);
        $hasPickupPlace = !empty($this->schema['pickup_place']);
        $requiredFields = array(
            'fullname' => true,
            'street' => true,
            'city' => true,
            'zip' => true,
            'country' => true,
            'phone' => true,
            'email' => true,
            'company_fullname' => true,
            'delivery_fullname' => true,
            'delivery_street' => true,
            'delivery_city' => true,
            'delivery_zip' => true,
            'delivery_country' => true,
            'delivery_phone' => true,
            'delivery_email' => true,
            'delivery_company_fullname' => true,
        );
        if ($options['shipmentMethodPid'] === false) {
            $requiredFields['run_eshop_shipment_methods_id'] = true;
        }
        if ($hasPickupPlace) {
            $requiredFields['pickup_place'] = true;
        }
        if (($missingFields = array_diff_key($requiredFields, $order))) {
            throw new Exception(__e(__FILE__, 'Cannot resolve delivery data because of missing fields: %s', implode(', ', array_keys($missingFields))));
        }
        
        if ($options['shipmentMethodPid'] === false) {
            $Shipment = $this->loadModel('EshopShipmentMethod', true);
            $options['shipmentMethodPid'] = $Shipment->findFieldBy('pid', 'id', $order['run_eshop_shipment_methods_id']);
        }
        
        // if both invoicing and delivery address is provided so just ensure that 
        // delivery phone and email is provided too
        if (!empty($order['delivery_fullname'])) {
            $order['delivery_phone'] = $order['delivery_phone'] ?: $order['phone'];
            $order['delivery_email'] = $order['delivery_email'] ?: $order['email'];
        }
        // resolve local pickup address
        elseif ($options['shipmentMethodPid'] === 'localPickup') {
            $order['delivery_fullname'] = $this->getSetting('address.companyFullname');
            $order['delivery_street'] = $this->getSetting('address.street');
            $order['delivery_city'] = $this->getSetting('address.city');
            $order['delivery_zip'] = $this->getSetting('address.zip');
            $order['delivery_country'] = $this->getSetting('address.country');
            $order['delivery_phone'] = $order['delivery_phone'] ?: $order['phone'];
            $order['delivery_email'] = $order['delivery_email'] ?: $order['email'];
        }
        // resolve ZASIELKOVŇA pickup place
        elseif (!empty($order['pickup_place'])) {
            $order['delivery_fullname'] = $order['delivery_fullname'] ?: $order['fullname'];
            $placeParts = array_map('trim', explode(',', $order['pickup_place']));
            $city = array_shift($placeParts);
            $order['delivery_street'] = implode(', ', $placeParts);
            $order['delivery_city'] = $city;
            $order['delivery_zip'] = null;
            $order['delivery_country'] = $this->getSetting('address.country');
            $order['delivery_phone'] = $order['delivery_phone'] ?: $order['phone'];
            $order['delivery_email'] = $order['delivery_email'] ?: $order['email'];
        }
        // copy invoice address to delivery address if no delivery address provided
        else {
            $order['delivery_company_fullname'] = $order['company_fullname'];
            $order['delivery_fullname'] = $order['fullname'];
            $order['delivery_street'] = $order['street'];
            $order['delivery_city'] = $order['city'];
            $order['delivery_zip'] = $order['zip'];
            $order['delivery_country'] = $order['country'];
            $order['delivery_phone'] = $order['phone'];
            $order['delivery_email'] = $order['email'];
        }
        
        return $order;
    }
        
    /**
     * Returns prices required for order. Following items are set in output array:
     *  - 'order_price_taxless'
     *  - 'order_price_taxed'
     *  - 'order_price_actual_taxless'
     *  - 'order_price_actual_taxed'
     *  - 'order_tax'
     *  - 'order_tax_actual'
     *  - 'order_price_to_pay' - final order price (after substracting all discounts) to be paid.
     *      It can be either taxed or taxless value depending on order type (with or without VAT)
     * 
     * @param array $options
     *      - 'productsPrices' (array) Array returned by medhod EshopCart::getPrices().
     *          Defaults to none.
     *      - 'shipmentPrices' (array) Array returned by medhod EshopShipmentMethod::getPrices().
     *          Defaults to none.
     *      - 'paymentPrices' (array) Array returned by medhod PaymentMethod::getPrices().
     *          Defaults to none.
     *      - 'precision' (int) All numbers will be rounded to this precision. Defaults to 2. 
     * 
     * @return array Array with the keys listed here above.
     */
    public function getPrices($options = array()) {
        $defaults = array(
            'productsPrices' => array(
                'products_price_actual_taxless' => 0.0,
                'products_price_actual_taxed' => 0.0,
                'products_price_taxless' => 0.0,
                'products_price_taxed' => 0.0,
                'products_price_to_pay' => 0.0,
            ),
            'shipmentPrices' => array(
                'shipment_price_taxed' => 0.0,
                'shipment_price_actual_taxed' => 0.0,
                'shipment_price_taxless' => 0.0,
                'shipment_price_actual_taxless' => 0.0,
            ),
            'paymentPrices' => array(
                'payment_price_taxed' => 0.0,
                'payment_price_actual_taxed' => 0.0,
                'payment_price_taxless' => 0.0,
                'payment_price_actual_taxless' => 0.0,
            ),
            'precision' => 2,
        );
        $options = array_merge($defaults, $options);
        $options['productsPrices'] = array_merge($defaults['productsPrices'], $options['productsPrices']);
        $options['shipmentPrices'] = array_merge($defaults['shipmentPrices'], $options['shipmentPrices']);
        $options['paymentPrices'] = array_merge($defaults['paymentPrices'], $options['paymentPrices']);
        
        $priceTaxless = $options['productsPrices']['products_price_taxless'] 
            + $options['shipmentPrices']['shipment_price_taxless']
            + $options['paymentPrices']['payment_price_taxless'];
        
        $priceTaxed = $options['productsPrices']['products_price_taxed'] 
            + $options['shipmentPrices']['shipment_price_taxed']
            + $options['paymentPrices']['payment_price_taxed'];
        
        $priceActualTaxless = $options['productsPrices']['products_price_actual_taxless'] 
            + $options['shipmentPrices']['shipment_price_actual_taxless']
            + $options['paymentPrices']['payment_price_actual_taxless'];
        
        $priceActualTaxed = $options['productsPrices']['products_price_actual_taxed'] 
            + $options['shipmentPrices']['shipment_price_actual_taxed']
            + $options['paymentPrices']['payment_price_actual_taxed'];
        
        $priceToPay = $options['productsPrices']['products_price_to_pay'];
        // @synchro with EshopOrder::saveAll()
        // @todo - resolve if the order is with VAT or without VAT
        $taxedOrder = true;
        if ($taxedOrder) {
            $priceToPay += $options['shipmentPrices']['shipment_price_actual_taxed']
                + $options['paymentPrices']['payment_price_actual_taxed'];
        }
        else {
            $priceToPay += $options['shipmentPrices']['shipment_price_actual_taxless']
                + $options['paymentPrices']['payment_price_actual_taxless'];            
        }
        
        return array(
            'order_price_taxless' => round($priceTaxless, $options['precision']),
            'order_price_taxed' => round($priceTaxed, $options['precision']),
            'order_price_actual_taxless' => round($priceActualTaxless, $options['precision']),
            'order_price_actual_taxed' => round($priceActualTaxed, $options['precision']),
            'order_tax' => round($priceTaxed - $priceTaxless, $options['precision']),
            'order_tax_actual' => round($priceActualTaxed - $priceActualTaxless, $options['precision']),
            'order_price_to_pay' => $priceToPay,
        );
    }
    
    /**
     * Gets array of applicable advances according to provided $order total in a list like:
     * 
     *      array(
     *          {advanceOrderTotal1} => array(
     *              'rate' => {advanceRate1},
     *              'price' => {advancePrice1},
     *          ),
     *          {advanceOrderTotal2} => array(
     *              'rate' => {advanceRate2},
     *              'price' => {advancePrice2},
     *          ),
     *          ...
     *      )
     * 
     * @param array $order Order record retireved from DB containing at least 
     *      'order_price_to_pay'.
     * @param array $options You may specify the following:
     *      - 'precision' (int) All numbers will be rounded to this precision. Defaults to 2. 
     * 
     * @return array List described here above. If no advances are applicable then empty array.
     */
    public function getAdvances($order, $options = array()) {
        $defaults = array(
            'precision' => 2,
        );
        $options = array_merge($defaults, $options);
        
        $total = $order['order_price_to_pay'];
        $advances = array();
        if (($rawAdvances = $this->getSetting('EshopOrder.advanceRates'))) {
            $rawAdvances = $this->parseSelectiveAttribute($rawAdvances);
            $rawAdvances = $rawAdvances['prices'];
            foreach ($rawAdvances as $advanceTotal => $advance) {
                if ($advanceTotal <= $total) {                
                    $advance['rate'] = $advance['price'];
                    $advance['price'] = Number::applyRate($total, $advance['rate'], $options['precision']);
                    unset($advance['operator']);
                    $advances[$advanceTotal] = $advance;
                }
            }
        }
        
        return $advances;
    }
    
    /**
     * Gets last number used for an order.
     * 
     * @param array $options Find options to precise range of numbers to 
     *      find the last from.
     * 
     * @return string|NULL
     */
    public function getLastNumber($options = array()) {
        $defaults = array(
            'conditions' => null,
        );
        $options = array_merge($defaults, $options);
        // ensure existence of folowing items in options
        $options['conditions'] = DB::nestConditions($options['conditions']);
        // precise conditions
        $options['conditions'][] = 'number IS NOT NULL';
        // force following
        $options['order'] = array('(number + 0) DESC');
        $options['literals'] = array('order' => true);
        // get the number
        return $this->findField('number', $options);
    }
    
    /**
     * Gets next order number. 
     * 
     * @param array $options Find options to precise range of numbers to 
     *      find the next for plus one extra option:
     *      - 'initial' (int) Initial value of next (new) order for provided find options.
     *      Defaults to 10000.
     * 
     * @return string
     */
    public function getNextNumber($options = array()) {
        $defaults = array(
            'initial' => 10000,
        );
        $options = array_merge($defaults, $options);
        $lastNumber = $this->getLastNumber($options);
        if ($lastNumber === null) {
            return $options['initial'];
        }
        return str_pad(((int)$lastNumber + 1), 5, '0');
    }
    
    /**
     * Sets new order id in session
     * 
     * @param int $id
     */
    public function setNewOrderId($id) {
        $_SESSION['_eshop']['EshopOrder']['newOrderId'] = $id;
    }
    
    /**
     * Gets new order id from session
     * 
     * @return int
     */
    public function getNewOrderId() {
        return Sanitize::value($_SESSION['_eshop']['EshopOrder']['newOrderId']);
    }
    
    /**
     * Checks if the order is editable
     * 
     * NOTE: Under "editable" is meant if anything can be changed in order without change of its price
     *
     * @param int|array $order Order id or an array of order data containing at least
     *      fields 'status' and 'payment_status'.
     *
     * @return bool TRUE if order exists and is editable
     */
    public function isEditable($order, $options = array()) {
        if (
            !is_array($order)
            || !array_key_exists('status', $order)
            || !array_key_exists('payment_status', $order)
        ) {
            if (is_array($order)) {
                if (empty($order['id'])) {
                    throw new Exception(__e(__FILE__, 'Missing id in provided order data'));
                }
                $order = $order['id'];
            }
            $order = $this->findFirstBy('id', $order, array(
                'fields' => array(
                    'status',
                    'payment_status',
                ),
            ));
        }
        return (
            $order 
            && isset($this->liveStatuses[$order['status']]) 
            && $order['payment_status'] !== 'enum_payment_tout'
            && $order['payment_status'] !== 'enum_payment_paid'
            && $order['payment_status'] !== 'enum_payment_advance_paid'
            && $order['payment_status'] !== 'enum_payment_partially_paid'
            && $order['payment_status'] !== 'enum_payment_manipulated'
        );
    }
    
    /**
     * Checks if the order is payable by some active online method
     *
     * @param int|array $order Order id or an array of order data containing at least
     *      fields 'status', 'payment_status', 'specific', 'shipment_price_taxless'
     *      'payment_price_taxless' and 'run_payment_methods_id'.
     * @param array $options Following are available:
     *      - 'paymentMethodMustBeSpecified' (bool) Defaults to TRUE.
     *
     * @return bool TRUE if order exists and is payable by some online payment method
     */
    public function isPayable($order, $options = array()) {
        $options = array_merge(array(
            'paymentMethodMustBeSpecified' => true,
        ), $options);
        if (
            !is_array($order)
            || !array_key_exists('status', $order)
            || !array_key_exists('payment_status', $order)
            || !array_key_exists('specific', $order)
            || !array_key_exists('shipment_price_taxless', $order)
            || !array_key_exists('payment_price_taxless', $order)
            || !array_key_exists('run_payment_methods_id', $order)
        ) {
            if (is_array($order)) {
                if (empty($order['id'])) {
                    throw new Exception(__e(__FILE__, 'Missing id in provided order data'));
                }
                $order = $order['id'];
            }
            $order = $this->findFirstBy('id', $order, array(
                'fields' => array(
                    'status',
                    'payment_status',
                    'specific',
                    'shipment_price_taxless',
                    'payment_price_taxless',
                    'run_payment_methods_id'
                ),
            ));
        }
        $PaymentMethod = App::loadModel('Payment', 'PaymentMethod', true);
        return (
            $order
            && (
                !$options['paymentMethodMustBeSpecified']
                ||
                !empty($order['run_payment_methods_id'])
                && $PaymentMethod->findFirst(array(
                    'conditions' => array(
                        'id' => $order['run_payment_methods_id'],
                        'active' => true,
                        'online' => true
                    )
                ))
            )
            //&& $order['status'] !== 'enum_opened_order'
            && $order['status'] !== 'enum_shipped_order'
            //&& $order['status'] !== 'enum_pickup_order'
            && $order['status'] !== 'enum_closed_order'
            //&& $order['status'] !== 'enum_suspended_order'
            && $order['status'] !== 'enum_canceled_order'
            && $order['payment_status'] !== 'enum_payment_tout'
            && $order['payment_status'] !== 'enum_payment_paid'
            && $order['payment_status'] !== 'enum_payment_advance_paid'
            && $order['payment_status'] !== 'enum_payment_partially_paid'
            && $order['payment_status'] !== 'enum_payment_manipulated'
            && (
                !($order['specific'] & self::SPECIFIC_SHIPMENT)
                || $order['shipment_price_taxless'] !== null
            )
            && (
                !($order['specific'] & self::SPECIFIC_PAYMENT)
                || $order['payment_price_taxless'] !== null
            )
        );
    }
    
    /**
     * Makes payment request for given order id. Payment method is specified by order
     * as it was chosen in order checkout.
     * 
     * If it is not possible to make the payment then exceptions are thrown.
     * 
     * If the payment can be processed then redirection is make to the payment url.
     * 
     * @param int $orderId Id of order to be paid
     * @param int $orderToken Order token to securize public payment requests in case of 
     *      quick orders
     * @param string $returnLocator Locator to send the payment answer to, e.g. 
     *      'platba-odpoved', 'mvc/Eshop/EshopOrder/myActionToProcessPaymentAnswer'.
     *      The absolute URL is created from provided locator, payment method pid
     *      and variable symbol are attached as url params _pm and _vs.
     * @param array $options Follwing are available:
     *      - 'paymentId' (int) Id of payment method to be used. Defaults to NULL.
     *      - 'forcePaymentId' (bool) Considered only if paymentId is provided. 
     *          If FALSE then specified paymentId is used only in case that order
     *          has no payment method attributed yet or attributed method is not active or online. 
     *          If TRUE then specified paymentId is used in any case. Defaults to FALSE.
     */
    public function pay($orderId, $orderToken, $returnLocator, $options = array()) {
        $options = array_merge(array(
            'paymentId' => null,
            'forcePaymentId' => false,
        ), $options);
        App::loadModel('Payment', 'PaymentMethod');
        $PaymentMethod = new PaymentMethod();
        if (
            !empty($options['paymentId'])
            && ($explicitPayment = $PaymentMethod->findFirst(array(
                'conditions' => array('id' => $options['paymentId']),
                'fields' => array('active', 'online', 'name'),
            )))
            && $explicitPayment['active']
            && $explicitPayment['online']
            && (
                $options['forcePaymentId']
                || !($paymentId = $this->findFieldBy('run_payment_methods_id', 'id', $orderId))
                || !($payment = $PaymentMethod->findFirst(array(
                    'conditions' => array('id' => $paymentId),
                    'fields' => array('active', 'online'),
                )))
                || !$payment['active']
                || !$payment['online']
            )
        ) {
            $this->save(
                array(
                    'id' => $orderId, 
                    'run_payment_methods_id' => $options['paymentId'],
                    'payment_method_name' => $explicitPayment['name'],
                ), 
                array(
                    'normalize' => false, 
                    'validate' => false,
                    'reserve' => false,
                )
            );
        }
        // retrieve the order
        $order = $this->findFirst(array(
            'conditions' => array(
                'EshopOrder.id' => $orderId,
                'EshopOrder.token' => $orderToken,
            ),
            'fields' => array(
                'EshopOrder.id',
                'EshopOrder.number',
                'EshopOrder.status',
                'EshopOrder.payment_status',
                'EshopOrder.advance_rate',
                'EshopOrder.order_price_to_pay',
                'EshopOrder.shipment_price_actual_taxless',
                'EshopOrder.shipment_tax_actual',
                'EshopOrder.payment_price_actual_taxless',
                'EshopOrder.payment_tax_actual',
                'EshopOrder.fullname',
                'EshopOrder.street',
                'EshopOrder.city',
                'EshopOrder.zip',
                'EshopOrder.country',
                'EshopOrder.email',
                'EshopOrder.phone',
                'PaymentMethod.pid'
            ),
            'joins' => array(
                array(
                    'module' => 'Payment',
                    'model' => 'PaymentMethod',
                    'type' => 'left',
                )
            ),
            'separate' => array('PaymentMethod'),
        )); 
        if (empty($order)) {
            throw new Exception(__e(__FILE__, 'Invalid payment request'));
        }
        elseif ($this->isPayable($orderId)) {
            // just skip the bellow exceptions
        }
        elseif ($order['payment_status'] == 'enum_payment_advance_paid') {
            throw new Exception(__e(__FILE__, 'Order advance has been paid already!'));
        }
        elseif ($order['payment_status'] == 'enum_payment_paid') {
            throw new Exception(__e(__FILE__, 'Order has been paid already!'));
        }
        elseif ($order['status'] == 'enum_shipped_order') {
            throw new Exception(__e(__FILE__, 'Order has been shipped already!'));
        }
        elseif ($order['status'] == 'enum_pickup_order') {
            throw new Exception(__e(__FILE__, 'Order has been prepared for pickup already!'));
        }
        elseif ($order['status'] == 'enum_closed_order') {
            throw new Exception(__e(__FILE__, 'Order has been closed already!'));
        }
        elseif ($order['status'] == 'enum_suspended_order') {
            throw new Exception(__e(__FILE__, 'Order has been suspended!'));
        }
        elseif ($order['status'] == 'enum_canceled_order') {   
            throw new Exception(__e(__FILE__, 'Order has been canceled!'));
        }
        elseif ($order['payment_status'] == 'enum_payment_tout') {
            throw new Exception(__e(__FILE__, 'There is another order payment which is not finished yet!'));
        }
        else {
            throw new Exception(__e(__FILE__, 'Invalid payment request'));
        }
        
        if ($order['advance_rate'] && $order['advance_rate'] < 100) {
            $description = __(__FILE__, 'Payment of advance %s%% for order No. %s from %s', 
                    App::formatNumber($order['advance_rate']),
                    $order['number'], 
                    App::getSetting('App', 'name')
            );
            $amount = Number::applyRate($order['order_price_to_pay'], $order['advance_rate'], 2);
        }
        else {
            $description = __(__FILE__, 'Payment for order No. %s from %s', $order['number'], App::getSetting('App', 'name'));
            $amount = $order['order_price_to_pay'];            
        }
        $lastname = explode(' ', $order['fullname']);
        $firstname = array_shift($lastname);
        $lastname = implode(' ', $lastname);
        
        $products = $this->getProductsDetails($orderId);
        $items = array();
        foreach ($products as $product) {
            $items[] = array(
                'name' => $product['name'],
                'count' => $product['amount'],
                'amount' => ($product['price_actual_taxless'] + $product['tax_actual']) * $product['amount'],
            );
        }
        $items[] = array(
            'name' => __(__FILE__, 'Postage'),
            'count' => 1,
            'amount' => $order['shipment_price_actual_taxless'] + 
                $order['shipment_tax_actual'] + 
                $order['payment_price_actual_taxless'] + 
                $order['payment_tax_actual'],
        );
        
        // make the payment
        try {
            App::loadModel('Payment', 'PaymentMethod');
            $PaymentMethod = PaymentMethod::factory($order['PaymentMethod']['pid']);
            $PaymentMethod->makeRequest(array(
                'amount' => $amount,
                'variableSymbol' => $order['number'],
//                'specificSymbol' => '',
                'returnUrl' => $returnLocator,
                'paymentDescription' => $description,
                // used by paypal standard & GoPay
                'clientFirstname' => $firstname,
                'clientLastname' => $lastname,
                'clientStreet' => $order['street'],
                'clientCity' => $order['city'],
                'clientCountry' => $order['country'],
                'clientZip' => $order['zip'],
                'clientEmail' => $order['email'],
                'clientPhone' => $order['phone'],
                // used by cardpay
                'clientFullname' => $order['fullname'],
                // used by GoPay
                'items' => $items,
//                'debug' => true, // if TRUE the generated URL is dislayed by App::debug(), logged to debug_payment and no redirect to bank is done
            ));
            // if no exception is raised then this poins is not reached 
            // Application redirects to payment URL
        }
        catch (Throwable $e) {
            App::logError(
                __e(
                    __FILE__, 
                    'Payment of order "%s" by payment method "%s" has failed with an exception "%s"',
                    $order['number'],
                    $order['PaymentMethod']['pid'],
                    $e->getMessage()
                ),
                array(
                    'var' => $e,
                    'email' => true,
                )
            );
            throw new Exception(__e(__FILE__, "We are sorry but it's not possible to make the payment. Please contact our customer service."));
        }
    }
    
    /**
     * Processes the payment response and updates the order.
     * 
     * @param array $options Following are available:
     *      - 'paymentStatusChanged' (boolean) Aux output. Pass here a variable by
     *          reference to get an info if the new payment status is different from
     *          the last one. E.g. 'paymentStatusChanged' => &$changed.
     * 
     * @return string New order payment status. An exception is thrown on any error.
     */
    public function processPaymentResponse($options = array()) {
        $options = array_merge(array(
            'paymentStatusChanged' => false,
        ), $options);
        // process the response
        try {
            $responseData = array_merge($_REQUEST, App::$params);
            $paymentMethod = null;
            if (!empty($responseData['_pm'])) {
                $paymentMethod = $responseData['_pm'];
            }
            App::loadModel('Payment', 'PaymentMethod');
            $PaymentMethod = PaymentMethod::factory($paymentMethod);
            $result = $PaymentMethod->processResponse($responseData);   
        }
        catch (Throwable $e) {
            App::logError($e->getMessage(), array(
                'email' => true,
            ));
            throw new Exception(__e(__FILE__, "We are sorry but it's not possible to process the bank response. Please contact our customer service."));
        }
        
        // find the order
        $order = $this->findFirstBy('number', $result['variableSymbol'], array(
            'fields' => array(
                'EshopOrder.id', 
                'EshopOrder.advance_rate',
                'EshopOrder.payment_status',
                'EshopOrder.notes',
            ),
        ));
        if (empty($order)) {
            throw new Exception(__e(__FILE__, 'Order for variable symbol %s was not found! Please contact our customer service.', $result['variableSymbol']));
        }
        $oldPaymentStatus = $order['payment_status'];
                        
        // update the order 'status', 'payment_status', 'payment_signed'
        if ($result['status'] === PaymentMethod::PAYMENT_STATUS_PAID) {
            if ($order['advance_rate'] && $order['advance_rate'] < 100) {
                $order['payment_status'] = 'enum_payment_advance_paid';
            }
            else {
                $order['payment_status'] = 'enum_payment_paid';
            }
        }
        elseif ($result['status'] === PaymentMethod::PAYMENT_STATUS_PARTIALLY_PAID) {
            $order['payment_status'] = 'enum_payment_partially_paid';
        }
        // following 2 charge back responses comes from GoPay when the payment is
        // refunded or partially refunded
        elseif ($result['status'] === PaymentMethod::PAYMENT_STATUS_CHARGED_BACK) {
            $order['payment_status'] = 'enum_payment_none';
        }
        elseif ($result['status'] === PaymentMethod::PAYMENT_STATUS_PARTIALLY_CHARGED_BACK) {
            // do not change the payment status here. Partial charge back is done
            // mostly because some of ordered products are not available and
            // merchant refunds back the money paid for these unavailable items.
        }
        // timeouted payment session - happens when user opens the bank payment page
        // but he leaves it (in forgotten browser tab or he just closes that tab)
        // without submitting or cancelling the payment. In this case do not make 
        // any further processing of the response from bank.
        elseif ($result['status'] === PaymentMethod::PAYMENT_STATUS_TOUT) {
            return 'enum_payment_tout';
        }
        elseif ($result['status'] === PaymentMethod::PAYMENT_STATUS_MANIPULATED) {
            $order['payment_status'] = 'enum_payment_manipulated';
        }
        elseif ($result['status'] === PaymentMethod::PAYMENT_STATUS_FAILED) {
            $order['payment_status'] = 'enum_payment_failed';
        }
        else {
            throw new Exception (__e(__FILE__, 'Unknown payment result status %s. Please contact our customer service.', $result['status']));
        }
        $order['payment_signed'] = Sanitize::value($result['signed']);
        $order['payment_result_text'] = Sanitize::value($result['resultText']);
        $order['run_payment_request_logs_id'] = Sanitize::value($result['paymentRequestLogId']);
        // add a note about payment response
        if ($order['notes']) {
            $order['notes'] .= "\n";
        }
        if (empty($responseData['_nt'])) { // if regular response (not notification)
            $noteAuthor = __a(__FILE__, 'Payment (response from bank)');
        }
        else {
            $noteAuthor = __a(__FILE__, 'Payment (internal notification about status change from bank)');
        }
        if ($result['status'] === PaymentMethod::PAYMENT_STATUS_CHARGED_BACK) {
            $note = self::getNotesLog(__a(__FILE__, 'Payment charged back'), array(
                'author' => $noteAuthor,
            ));
        }
        elseif ($result['status'] === PaymentMethod::PAYMENT_STATUS_PARTIALLY_CHARGED_BACK) {
            $note = self::getNotesLog(__a(__FILE__, 'Payment partially charged back'), array(
                'author' => $noteAuthor,
            ));
        }
        else {            
            $note = self::getNotesLog(__a(__FILE__, 'PAYMENT STATUS') . ': ' . __(__FILE__, $order['payment_status']), array(
                'author' => $noteAuthor,
            ));
        }
        $order['notes'] .= $note;
        // save order
        if (!$this->save($order)) {
            throw new Exception (__e(__FILE__, 'Your payment cannot be processed due to an internal error. Please contact our customer service.'));
        }
        
        $options['paymentStatusChanged'] = $order['payment_status'] !== $oldPaymentStatus;
        return $order['payment_status'];
    }
    
    /**
     * Checks all unpaid orders with payment methods of Tatrabanka and if TB answers
     * that order is paid then its payment status is updated (otherwise nothing changes).
     * 
     * @return array Progress array containing amounts of processed orders
     */
    public function updateTatrabankaPaymentsStatuses() {
        $microtimeResereve = 5000;
        $progress = array(
            'ordersCount' => 0,
            'checkedCount' => 0,
            'paidCount' => 0,
            'skippedCount' => 0,
            'interrupted' => false,
        );
        App::loadModel('Payment', 'PaymentMethod');
        $PaymentMethod = new PaymentMethod();
        $paymentMethods = $PaymentMethod->findList(array(
            'fields' => array('pid'),
            'conditions' => array(
                'pid' => array('tatrapay', 'cardpay'),
            ),
        ));
        if (empty($paymentMethods)) {
            return $progress;
        }
        $orders = $this->findList(array(
            'key' => 'number',
            'fields' => array(
                'id', 
                'run_payment_methods_id', 
            ),
            'conditions' => array(
                'payment_status !=' => 'enum_payment_paid',
                'run_payment_methods_id' => array_keys($paymentMethods)
            ),
            'order' => 'id DESC',
//            'limit' => 5, //debug
        ));
        if (empty($orders)) {
            return $progress;
        }
        $progress['ordersCount'] = count($orders);
        $Cardpay = PaymentMethod::factory('cardpay');
        $Tatrapay = PaymentMethod::factory('tatrapay');
        foreach ($orders as $variableSymbol => $order) {
            if (App::getFreeMicrotime() < $microtimeResereve) {
                $progress['interrupted'] = true;
                break;
            }
            if ($paymentMethods[$order['run_payment_methods_id']] === 'cardpay') {
                $TheMethod = $Cardpay;
            }
            else {
                $TheMethod = $Tatrapay;
            }
            $status = $TheMethod->checkTransactionStatus(array(
//                'merchantId' => '', //debug
//                'key' => '', //debug
                'variableSymbol' => $variableSymbol,
            ));
            $progress['checkedCount']++;
            if ($status == PaymentMethod::PAYMENT_STATUS_PAID) {
                $progress['paidCount']++;
                $order['payment_status'] = 'enum_payment_paid';
                $order['payment_signed'] = true;
            }
            elseif ($status == PaymentMethod::PAYMENT_STATUS_TOUT) {
                $progress['skippedCount']++;
                continue;
            }
            elseif ($status == PaymentMethod::PAYMENT_STATUS_FAILED) {
                $progress['skippedCount']++;
                continue;
            }
            elseif ($status == PaymentMethod::PAYMENT_STATUS_UNPAID) {
                $progress['skippedCount']++;
                continue;
            }
            // save only payment status, do not touch/resave other data
            unset($order['run_payment_methods_id']);
            $this->save($order, array(
                'normalize' => false,
                'validate' => false,
            ));
        }
        
        return $progress;
    }
    
    /**
     * Checks payment status of specified order in Tatrabanka and returns payment
     * status value
     * 
     * @param int $id Order id
     * 
     * @return string|bool One of payment status values ('enum_payment_none', 
     *  'enum_payment_paid', 'enum_payment_tout', 'enum_payment_failed'). FALSE
     *  if there is no Tatrabanka payment method or if specified order with TB
     *  payment method does not exist.
     */
    public function checkTatrabankaPaymentStatus($id) {
        App::loadModel('Payment', 'PaymentMethod');
        $PaymentMethod = new PaymentMethod();
        $paymentMethods = $PaymentMethod->findList(array(
            'fields' => array('pid'),
            'conditions' => array(
                'pid' => array('tatrapay', 'cardpay'),
            ),
        ));
        if (empty($paymentMethods)) {
            return false;
        }
        $order = $this->findFirst(array(
            'fields' => array(
                'id', 
                'number',
                'run_payment_methods_id', 
            ),
            'conditions' => array(
                'id' => $id,
                'run_payment_methods_id' => array_keys($paymentMethods)
            ),
            'order' => 'id DESC',
        ));
        if (empty($order)) {
            return false;
        }
        $Cardpay = PaymentMethod::factory('cardpay');
        $Tatrapay = PaymentMethod::factory('tatrapay');
        if ($paymentMethods[$order['run_payment_methods_id']] === 'cardpay') {
            $TheMethod = $Cardpay;
        }
        else {
            $TheMethod = $Tatrapay;
        }
        $status = $TheMethod->checkTransactionStatus(array(
//            'merchantId' => '', //debug
//            'key' => '', //debug
            'variableSymbol' => $order['number'],
        ));
        if ($status == PaymentMethod::PAYMENT_STATUS_PAID) {
            $return = 'enum_payment_paid';
        }
        elseif ($status == PaymentMethod::PAYMENT_STATUS_TOUT) {
            $return = 'enum_payment_tout';
        }
        elseif ($status == PaymentMethod::PAYMENT_STATUS_FAILED) {
            $return = 'enum_payment_failed';
        }
        elseif ($status == PaymentMethod::PAYMENT_STATUS_UNPAID) {
            $return = 'enum_payment_none';
        }
        return $return;
    }
    
    /**
     * Charges back payment of specified order paid by Cardpay method (Tatrabanka)
     * 
     * @param int $id Order id
     * 
     * @return bool TRUE on success. FALSE on failure (if there is no Cardpay 
     * payment method or if specified order with Cardpay payment method does not exist
     * or if chargeback request to Cardpay fails).
     */
    public function chargeBackCardpayPayment($id) {
        App::loadModel('Payment', 'PaymentMethod');
        $PaymentMethod = new PaymentMethod();
        $paymentMethod = $PaymentMethod->findFirst(array(
            'fields' => array('id'),
            'conditions' => array(
                'pid' => array('cardpay'),
            ),
        ));
        if (empty($paymentMethod)) {
            return false;
        }
        $order = $this->findFirst(array(
            'fields' => array(
                'run_payment_request_logs_id', 
            ),
            'conditions' => array(
                'id' => $id,
                'run_payment_methods_id' => $paymentMethod['id']
            ),
            'order' => 'id DESC',
        ));
        if (empty($order)) {
            return false;
        }
        $Cardpay = PaymentMethod::factory('cardpay');
        $result = $Cardpay->chargeBack(array(
//            'merchantId' => '', //debug
//            'key' => '', //debug
            'paymentRequestLogId' => $order['run_payment_request_logs_id'],
        ));
        // update order payment_status and notes
        $this->reserveTable('EshopOrder_chargeBackCardpayPayment');
        $order = $this->findFirstBy('id', $id, array(
            'fields' => array('payment_status', 'notes')
        ));
        if ($result) {
            $note = self::getNotesLog(
                __(__FILE__, 'Storno CardPay platby') . ' (' . __a(__FILE__, 'PAYMENT STATUS') . ': ' . __(__FILE__, 'enum_payment_none') . ')'
            );
            $order['payment_status'] = 'enum_payment_none';
        }
        else {
            $note = self::getNotesLog(__(__FILE__, 'Storno CardPay platby zlyhalo'));
        }
        if (!empty($order['notes'])) {
            $note = PHP_EOL . $note;
        }
        $order['notes'] .= $note;
        $this->updateBy('id', $id, $order, array(
            'normalize' => false,
            'validate' => false,
        ));
        $this->unreserveTables('EshopOrder_chargeBackCardpayPayment');
        return $result;
    }
    
    /**
     * Sets order with provided id to canceled status (means something like deleted
     * but kept in DB for case that... = canceled orders should not appear in admin)
     * 
     * @param int $orderId Order id to be set as canceled
     * 
     * @return bool TRUE on success, FALSE on failure (processing errors are set)
     */
    public function cancel($orderId) {
        try {
            $this->save(
                array(
                    'id' => $orderId, 
                    'status' => 'enum_canceled_order',
                ),
                array(
                    'normalize' => false,
                    'validate' => false,
                )
            );
        }
        catch (Throwable $e) {
            $this->setError($e->getMessage());
            return false;
        }
        return true;
    }
    
    /**
     * Returns Google ecommerce tracking code.
     * 
     * NOTE: This method uses setting App.google.analyticsCode. 
     * ATTENTION: If App::$useJsEmbroidery is FALSE and 'debug' option is FALSE 
     * then this method generates nothing. If 'debug' option is TRUE then this method
     * returns tracking code enclosed in HTML comments (value of is ignored as the js code is not executed).
     * 
     * @param int $orderId
     * @param array $options Following are available:
     *      - 'api' (string) Possible values are 'analyticsjs' and 'gajs'. For
     *      differences see https://developers.google.com/analytics/devguides/collection/analyticsjs/ and
     *      https://developers.google.com/analytics/devguides/collection/gajs/.
     *      In general analytics.js is newer version and ga.js could be a functional 
     *      subset of analytics.js (but the API is different!). Defaults to 'analytics'.
     *      - 'debug' (bool) If TRUE then this code is returned in HTML comment block.
     *      Defautls to value of ON_LOCALHOST constant.
     * 
     * @return string Google ecommerce tracking code 
     */
    public function getGoogleEcommerceTrackingCode($orderId, $options = array()) {
        $defaults = array(
            'api' => 'analytics', // 'analyticsjs', 'gajs'
            'debug' => ON_LOCALHOST,
        );
        $options = array_merge($defaults, $options);
        
        $analyticsCode = App::getSetting('App', 'google.analyticsCode');
        if (
            empty($analyticsCode)
            ||
            !App::$useJsEmbroidery && !$options['debug']
        ) {
            return '';
        }
        
        $order = $this->findFirstBy('id', $orderId);
        $products = $this->getProductsDetails($orderId);
        // add product attributes to product name
        foreach ($products as &$product) {
            $staticAttributes = json_decode($product['static_attributes'], true);
            $attributes = '';
            foreach($staticAttributes as $attrName => $attrValue) {
                if ($attributes !== '') {
                    $attributes .= ', ';
                }
                $attributes .= __(__FILE__, $attrName) . ': ' . $attrValue ;
            }
            if ($attributes !== '') {
                $product['name'] .= ', ' . $attributes;
            }
        }        
        unset($product);
        // load view
        $code = $this->loadView('EshopOrder/getGoogleEcommerceTrackingCode', array(
            'analyticsCode' => $analyticsCode,
            'api' => $options['api'],
            'debug' => $options['debug'],
            'order' => $order,
            'products' => $products,
        ));
        if ($options['debug']) {
            $code = Html::comment($code);
        }
        return $code;
    }
    
    /**
     * Returns Heureka conversion code.
     * See https://sluzby.heureka.sk/obchody/mereni-konverzi/
     * 
     * NOTE: This method uses setting Eshop.EshopOrder.heurekaConversionKey. 
     * ATTENTION: If App::$useJsEmbroidery is FALSE and 'debug' option is FALSE 
     * then this method generates nothing. If 'debug' option is TRUE then this method
     * returns conversion code enclosed in HTML comments (value of is ignored as the js code is not executed).
     * 
     * @param int $orderId
     * @param array $options Following are available:
     *      - 'productsConditions' (array) Order is reported to Heureka only in case
     *          that all products meet provided conditions here. This can be used
     *          to decide when it is good/convenient to send the report to heureka 
     *          and ask customer for rating and when not (e.g. in case of products
     *          with long delivery time or in presale which mostly returns bad 
     *          ratings). Here provided conditions are passed as 'conditions' option 
     *          to EshopOrderProduct::find(). Model EshopProduct is automatically 
     *          joined to this query. ATTENTION: Keep value of this option the same 
     *          also for method EshopOrder::sendHeurekaVerifiedByClientsRequest()! 
     *          Defaults to NULL.
     *      - 'debug' (bool) If TRUE then this code is returned in HTML comment block.
     *      Defautls to value of ON_LOCALHOST constant.
     * 
     * @return string  Heureka conversion code or empty string if provided 'productsConditions'
     *      are not met by all products.
     */
    public function getHeurekaConversionCode($orderId, $options = array()) {
        $defaults = array(
            'productsConditions' => null,
            'debug' => ON_LOCALHOST,
        );
        $options = array_merge($defaults, $options);
        
        $heurekaKey = App::getSetting('Eshop', 'EshopOrder.heurekaConversionKey');
        if (
            empty($heurekaKey)
            ||
            !App::$useJsEmbroidery && !$options['debug']
        ) {
            return '';
        }
        
        $order = $this->findFirstBy('id', $orderId);
        $OrderProduct = $this->loadModel('EshopOrderProduct', true);
        $products = $OrderProduct->find(array(
            'joins' => array(
                array(
                    'model' => 'EshopProduct',
                    'type' => 'left',
                ),
            ),
            'conditions' => array(
                'EshopOrderProduct.run_eshop_orders_id' => $orderId,
            ),
            'fields' => array(
                'EshopProduct.id',
                'EshopProduct.name',
                'EshopOrderProduct.price_actual_taxless',
                'EshopOrderProduct.tax_actual',
                'EshopOrderProduct.amount',
                'EshopOrderProduct.static_attributes',
                'EshopOrderProduct.dynamic_attributes',
            )
        ));
        if ($options['productsConditions']) {
            $productsConditions = array(
                'EshopOrderProduct.run_eshop_orders_id' => $orderId,
                $options['productsConditions'],
            );
            $convenientProducts = $OrderProduct->find(array(
                'joins' => array(
                    array(
                        'model' => 'EshopProduct',
                        'type' => 'left',
                    ),
                ),
                'conditions' => $productsConditions,
                'fields' => array('EshopOrderProduct.id')
            ));
            if (count($products) !== count($convenientProducts)) {
                return '';
            }
        }
        
        // generate sequence of fictive order numbers as not all orders are reported
        // to heureka (if option 'productsConditions' is set)
        $order['number'] = $this->getOrderNumberForHeureka($orderId);
        
        // add product attributes to product name
        foreach ($products as &$product) {
            $staticAttributes = json_decode($product['static_attributes'], true);
            $attributes = '';
            foreach($staticAttributes as $attrName => $attrValue) {
                if ($attributes !== '') {
                    $attributes .= ', ';
                }
                $attributes .= __(__FILE__, $attrName) . ': ' . $attrValue ;
            }
            if ($attributes !== '') {
                $product['name'] .= ', ' . $attributes;
            }
        }        
        unset($product);
        // load view
        $code = $this->loadView('EshopOrder/getHeurekaConversionCode', array(
            'heurekaKey' => $heurekaKey,
            'debug' => $options['debug'],
            'order' => $order,
            'products' => $products,
        ));
        if ($options['debug']) {
            $code = Html::comment($code);
        }
        return $code;
    }
    
    /**
     * Returns Facebook Purchase event tracking code.
     * See https://developers.facebook.com/docs/meta-pixel/implementation/conversion-tracking/
     * 
     * ATTENTION: Conversion code is generated only if facebook pixel (meta pixel)
     * code (see https://developers.facebook.com/docs/meta-pixel/get-started#base-code )
     * is inserted in customCode (htmlHead|htmlBodyStart|htmlBodyEnd)
     * 
     * ATTENTION: If App::$useJsEmbroidery is FALSE and 'debug' option is FALSE 
     * then this method generates nothing. If 'debug' option is TRUE then this method
     * returns tracking code enclosed in HTML comments (value of is ignored as the js code is not executed).
     * 
     * @param int $orderId
     * @param array $options Following are available:
     *      - 'debug' (bool) If TRUE then this code is returned in HTML comment block.
     *      Defautls to value of ON_LOCALHOST constant.
     * 
     * @return string Facebook Purchase event tracking code
     */
    public function getFacebookConversionCode($orderId, $options = array()) {
        $defaults = array(
            'debug' => ON_LOCALHOST,
        );
        $options = array_merge($defaults, $options);
        
        if (
            !App::$useJsEmbroidery 
            && !$options['debug']
            ||
            // check for presence of facebook pixel in custom code
            (
                $customHtmlCode = 
                    App::getSetting('App', 'customCode.htmlHead') . ' ' .
                    App::getSetting('App', 'customCode.htmlBodyStart') . ' ' .
                    App::getSetting('App', 'customCode.htmlBodyEnd')
            )
            && !preg_match('#fbevents\.js|https://www\.facebook\.com/tr\?id=#', $customHtmlCode)
        ) {
            return '';
        }
        
        $order = $this->findFirstBy('id', $orderId);
        $products = $this->getProductsDetails($orderId);
        // add product attributes to product name
        foreach ($products as &$product) {
            $staticAttributes = json_decode($product['static_attributes'], true);
            $attributes = '';
            foreach($staticAttributes as $attrName => $attrValue) {
                if ($attributes !== '') {
                    $attributes .= ', ';
                }
                $attributes .= __(__FILE__, $attrName) . ': ' . $attrValue ;
            }
            if ($attributes !== '') {
                $product['name'] .= ', ' . $attributes;
            }
        }        
        unset($product);
        // load view
        $code = $this->loadView('EshopOrder/getFacebookConversionCode', array(
            'debug' => $options['debug'],
            'order' => $order,
            'products' => $products,
        ));
        if ($options['debug']) {
            $code = Html::comment($code);
        }
        return $code;        
    }
    
    public function save($data, $options = array()) {
        $oldOrder = array();
        if (!empty($data['id'])) {
            $oldOrder = $this->findFirstBy('id', $data['id'], array(
                'fields' => array(
                    'status',
                    'payment_status',
                    'run_users_id',
                    'products_price_to_pay',
                    'bonus_discount',
                    'in_bonus_points',
                )
            ));
            $oldOrder['products_price_to_pay'] = (float)$oldOrder['products_price_to_pay'];
            $oldOrder['bonus_discount'] = (float)$oldOrder['bonus_discount'];
        }
        
        if (!($data = parent::save($data, $options))) {
            return false;
        }
        
        DB::startTransaction('EshopOrder::save()');
        try {
            App::loadModel('App', 'UserProfile');
            $UserProfile = new UserProfile();
            // if the order payment status has been changed to paid then 
            // add order products price (without applied bonus) to bonus points of
            // order user (if not yet done)
            if (
                $oldOrder
                && !empty($data['payment_status'])
                && $data['payment_status'] === 'enum_payment_paid'
                && $oldOrder['payment_status'] !== 'enum_payment_paid'
                && $oldOrder['run_users_id']
                && !$oldOrder['in_bonus_points']
            ) {
                $UserProfile->addBonusPoints(
                    $oldOrder['run_users_id'], 
                    $oldOrder['products_price_to_pay']
                );
                parent::save(
                    array(
                        'id' => $data['id'],
                        'in_bonus_points' => true,
                    ),
                    array(
                        'validate' => false,
                        'normalize' => false,
                    )
                );
            }
            // if the order products price is in bonus points of order user and
            // the payment status changes to unpaid (!== enum_payment_paid) then remove
            // products price from bonus points of order user
            elseif (
                $oldOrder
                && !empty($data['payment_status'])
                && $data['payment_status'] !== 'enum_payment_paid'
                && $oldOrder['payment_status'] === 'enum_payment_paid'
                && $oldOrder['run_users_id']
                && $oldOrder['in_bonus_points']
            ) {
                $UserProfile->removeBonusPoints(
                    $oldOrder['run_users_id'], 
                    $oldOrder['products_price_to_pay']
                );
                parent::save(
                    array(
                        'id' => $data['id'],
                        'in_bonus_points' => false,
                    ),
                    array(
                        'validate' => false,
                        'normalize' => false,
                    )
                );                
            }
            
            // if the order status has been changed to cancelled and the order has 
            // the bonus discount then return the applicable bonus points back to the user
            if (
                $oldOrder
                && !empty($data['status'])
                && $data['status'] === 'enum_canceled_order'
                && $oldOrder['status'] !== 'enum_canceled_order'
                && $oldOrder['run_users_id']
                && $oldOrder['bonus_discount']
            ) {
                $UserProfile->addApplicableBonusPoints($oldOrder['run_users_id']);
            }
            // if the order has been dis-cancelled (!== enum_canceled_order) and the order has 
            // the bonus discount then reset the bonus points of the user
            elseif (
                $oldOrder
                && !empty($data['status'])
                && $data['status'] !== 'enum_canceled_order'
                && $oldOrder['status'] === 'enum_canceled_order'
                && $oldOrder['run_users_id']
                && $oldOrder['bonus_discount']
            ) {
                $UserProfile->removeApplicableBonusPoints($oldOrder['run_users_id']);
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopOrder::save()');
            throw $e;
        }
        DB::commitTransaction('EshopOrder::save()');
        return $data;
    }
    
    /**
     * Saves:
     *      standard product fields
     *      + uploaded image
     *      + habtm authors (ids are in author_ids field, model 'EshopProductAuthor')
     *      + habtm categories (ids are in category_ids field, model 'EshopProductCategoryProduct')
     * 
     * @param array $data
     * 
     * @return bool|NULL|array Array of saved data on success. DB::RESERVATION_FAILED on 
     *      reservation failure and FALSE on validatio or processing failure.
     * 
     * @throws Exception
     */
    public function saveAll($data, $options = array()) {
        
        $data = Arr::inflate($data);
        
        unset($data['modified']);
        
        $productPrices = array(
            'products_price_taxless' => 0,
            'products_tax' => 0,
            'products_price_actual_taxless' => 0,
            'products_tax_actual' => 0,
            'products_price_to_pay' => 0,
        );
        $EshopOrderProduct = $this->loadModel('EshopOrderProduct', true);        
        $Product = $this->loadModel('EshopProduct', true);
        
////mojo: uncomment this when EshopOrders::admin_edit() will be refactored (see todo151207)     
//// and prepared to treat validations   
//        // validate data
//        $orderDataValid = $this->validate($data);
//        $orderItemsDataValid = true;
//        if (isset($data['products'])) {
//            foreach ($data['products'] as $i => $item) {
//                if (!$EshopOrderProduct->validate($item)) {
//                    $orderItemsDataValid = false;
//                    // move item errors to EshopOrder model errors
//                    $errors = $EshopOrderProduct->getErrors();
//                    foreach ($errors as $field => $fieldErrors) {
//                        $this->setErrors('products.' . $i . '.' . $field, $fieldErrors);
//                    }
//                }
//            }
//        }
//        if (!$orderDataValid || !$orderItemsDataValid) {
//            return false;
//        }
        
        $this->reserveTables('EshopOrder_saveAll', array(
            'EshopOrder',
            'EshopOrderProduct',
            'EshopShipmentMethod',
            'Payment.PaymentMethod',
            'EshopProductAttributeDetail',
        ), array('tries' => 20, 'retryTime' => 1000));
        DB::startTransaction('EshopOrder_saveAll');
        try {
            $oldData = $this->findFirst(array(
                'conditions' => array(
                    'id' => $data['id'],
                )
            ));
            if (empty($oldData)) {
                throw new Exception_EshopOrder_ManipulatedOrderData(__e(__FILE__, 'Invalid order data'));
            }
            $isEditable = $this->isEditable($data['id']);
            
            // get data of shipment and payment
            $EshopShipmentMethod = $this->loadModel('EshopShipmentMethod', true);
            $PaymentMethod = App::loadModel('Payment', 'PaymentMethod', true);
            $calculateOrder = false;
            if ($isEditable && isset($data['run_eshop_shipment_methods_id'])) {
                $calculateOrder = true;
                if ($data['run_eshop_shipment_methods_id'] != $oldData['run_eshop_shipment_methods_id']) {
                    $shipment = $EshopShipmentMethod->findFirstBy('id', $data['run_eshop_shipment_methods_id']);
                    $data['shipment_method_name'] = $shipment['name'];
                }
            }
            elseif (
                $isEditable
                && $oldData['specific'] 
                && isset($data['specific_shipment_price']) 
            ) {
                $calculateOrder = true;
                $shipment['price'] = $data['specific_shipment_price'];
            }
            if ($isEditable && isset($data['run_payment_methods_id'])) {
                $calculateOrder = true;
                if ($data['run_payment_methods_id'] != $oldData['run_payment_methods_id']) {
                    $payment = $PaymentMethod->findFirstBy('id', $data['run_payment_methods_id']);   
                    $data['payment_method_name'] = $payment['name'];
                }
            }
            
            // save order items
            if ($isEditable && isset($data['products'])) {
                $calculateOrder = true;
                
                // get order existing item ids
                $existingItems = $EshopOrderProduct->findList(array(
                    'key' => 'id',
                    'conditions' => array(
                        'run_eshop_orders_id' => $data['id']
                    )
                ));
                // get ids of saved (updated) items and ids of new added products
                // remove new empty items (with unspecified run_eshop_products_id)
                // remove saved items with 0 amount
                $savedItemIds = array(); // {itemId} => {itemAmount}
                $hasNewProducts = false;
                $productIds = array();
                foreach ($data['products'] as $i => $item) {
                    if (empty($item['id'])) {
                        // remove items with unspecified run_eshop_products_id
                        if (empty($item['run_eshop_products_id'])) {
                            unset($data['products'][$i]);
                            continue;
                        }
                        $hasNewProducts = true;
                    }
                    // this can happen only if item id has been manipulated manually
                    // in console. In such a case treat hacked item as a new ones. 
                    elseif (
                        empty($existingItems[$item['id']])
                        || 
                        $existingItems[$item['id']]['run_eshop_products_id'] 
                            != $item['run_eshop_products_id']
                    ) {
                        throw new Exception_EshopOrder_ManipulatedOrderData(__e(__FILE__, 'Invalid order data'));
                    }
                    elseif ((int)$item['amount'] > 0) {
                        $savedItemIds[$item['id']] = true;
                    }
                    // remove items with 'amount' <= 0 
                    else {
                        unset($data['products'][$i]);
                        continue;
                    }
                    $productIds[] = $item['run_eshop_products_id'];
                }
                $productIds = array_filter(array_unique($productIds));
                // get new products details
                $products = array();
                if (!empty($productIds)) {
                    $products = $Product->getDetails($productIds);
                }
                // check for manipulated new items run_eshop_products_id
                foreach ($data['products'] as $item) {
                    if (
                        empty($item['id'])
                        && empty($products[$item['run_eshop_products_id']])
                    ) {
                        throw new Exception_EshopOrder_ManipulatedOrderData(__e(__FILE__, 'Invalid order data'));
                    }
                }
                //$EshopProductAttributeDetail = App::loadModel('Eshop', 'EshopProductAttributeDetail', true);
                // get order deleted item ids and removed them
                $deletedItemIds = array();
                foreach (array_keys($existingItems) as $id) {
                    if (empty($savedItemIds[$id])) {
                        $deletedItemIds[] = $id;
                    }
                }
                if (!empty($deletedItemIds)) {
                    $EshopOrderProduct->deleteBy('id', $deletedItemIds);
                }
                
                // get last order product number if there are any new products
                if ($hasNewProducts) {
                    $lastNumber = $EshopOrderProduct->findFieldBy('number', 'run_eshop_orders_id', $data['id'], array(
                        'order' => 'number DESC'
                    ));
                }
                
                $voucherProductId = (int)$this->getSetting('EshopProduct.voucherProductId');
                // pairs {orderProductId} => {voucherPurposeType}
                $voucherPurposes = $EshopOrderProduct->findList(array(
                    'joins' => array(
                        array(
                            'type' => 'left',
                            'model' => 'EshopVoucher',
                            'conditions' => array(
                                'EshopOrderProduct.eshop_voucher_code = EshopVoucher.code'
                            ),
                        ),
                    ),
                    'conditions' => array(
                        'EshopOrderProduct.run_eshop_orders_id' => $data['id'],
                    ),
                    'key' => 'EshopOrderProduct.id',
                    'fields' => array('EshopVoucher.purpose_type'),
                ));
                $vouchersDiscount = 0;
                foreach ($data['products'] as $item) {
                    // prepare static attributes
                    if (isset($item['static_attributes'])) {
                        $product = Sanitize::value($products[$item['run_eshop_products_id']]);
                        $stAttrs = array();
                        $stInputAttrs = Str::explode(';', $item['static_attributes']);
                        foreach ($stInputAttrs as $atrItem) {
                            $itemArray = explode(':', $atrItem);
                            $itemName = trim($itemArray[0]);
                            $itemValue = trim($itemArray[1]);
                            // replace attr values by attr pids
                            if (
                                !empty($product[$itemName]['values'])
                                && is_array($product[$itemName]['values'])
                                && ($attrPid = array_search($itemValue, $product[$itemName]['values'], true))
                                && is_string($attrPid)
                            ) {
                                $itemValue = $attrPid;
                            }
                            $stAttrs[$itemName] = $itemValue;
                        }
                        $item['static_attributes'] = json_encode($stAttrs);
                    }
                    // prepare dynamic attributes
                    if (isset($item['dynamic_attributes'])) {
                        $dynAttrs = array();
                        $dynInputAttrs = Str::explode(';', $item['dynamic_attributes']);
                        foreach ($dynInputAttrs as $atrItem) {
                            $itemArray = explode(':', $atrItem);
                            $itemName = trim($itemArray[0]);
                            $itemValue = trim($itemArray[1]);
                            $dynAttrs[$itemName] = $itemValue;
                        }
                        $item['dynamic_attributes'] = json_encode($dynAttrs);
                    }
                    
                    // sanitize item data
                    $item = array_intersect_key($item, array_flip(array(
                        'id', 
                        'run_eshop_products_id', 
                        'amount', 
                        'static_attributes', 
                        'dynamic_attributes',
                    )));
                    
                    // in case of new item set fields according product detail
                    if (empty($item['id'])) {
                        $item = array_merge(
                            $products[$item['run_eshop_products_id']],
                            $item
                        );
                        unset($item['id']); // inherited from product in above array_merge(), it is actually product id
                        $item['run_eshop_orders_id'] = $data['id'];
                        $item['number'] = ++$lastNumber;
                    }
                    else {
                        $item = array_merge(
                            $existingItems[$item['id']],
                            $item
                        );
                    }
                    
//                    if ($data['status'] == 'enum_shipped_order') {
//                        $item['shipped'] = 1;
//                    }
//                    if ($data['status'] == 'enum_new_order') {
//                        $item['shipped'] = 0;
//                    }

                    if (!$EshopOrderProduct->save($item)) {
                        throw new Exception(__e(__FILE__, 'Update of order product %s has failed', json_encode($item)));
                    }
                    
//                    // if shipped state has been changed, make what is needed
//                    if ($item['shipped'] != $oldProdsShipped[$item['id']]) {
//
//                        // solve product reservations
//                        if ($item['shipped'] == 1) {
//                            // decrease reservations and stock
//                            DB::query('UPDATE ' . $EshopProduct->getPropertyTable() . ' '
//                                . 'SET reserved = IF(reserved - ' . $item['reserved'] . ' >= 0, reserved - ' . $item['reserved'] . ', 0), '
//                                . 'stock = stock - ' . $item['amount'] . ' '
//                                . 'WHERE id = ' . $item['id']);
//                            // get item dynamic attribute value and decrease attribute
//                            $dynAttrs = json_decode($item['dynamic_attributes'], true);
//                            if (!empty($dynAttrs)) {
//                                $dynAttrValue = array_shift($dynAttrs);
//                                DB::query('UPDATE ' . $EshopProductAttributeDetail->getPropertyTable() . ' '
//                                    . 'SET reserved = IF(reserved - ' . $item['reserved'] . ' >= 0, reserved - ' . $item['reserved'] . ', 0), '
//                                    . 'stock = stock - ' . $item['amount'] . ' '
//                                    . 'WHERE run_eshop_products_id = ' . $item['id'] . ' AND name="' . $dynAttrValue . '"');
//                            }
//                        }
//                        if ($item['shipped'] == 0) {
//                            // increase reservations and stock
//                            DB::query('UPDATE ' . $EshopProduct->getPropertyTable() . ' '
//                                . 'SET reserved = reserved + ' . $item['reserved'] . ', '
//                                . 'stock = stock + ' . $item['amount'] . ' '
//                                . 'WHERE id = ' . $item['id']);
//                            // get item dynamic attribute value and decrease attribute
//                            $dynAttrs = json_decode($item['dynamic_attributes'], true);
//                            if (!empty($dynAttrs)) {
//                                $dynAttrValue = array_shift($dynAttrs);
//                                DB::query('UPDATE ' . $EshopProductAttributeDetail->getPropertyTable() . ' '
//                                    . 'SET reserved = IF(reserved - ' . $item['reserved'] . ' >= 0, reserved - ' . $item['reserved'] . ', 0), '
//                                    . 'stock = stock - ' . $item['amount'] . ' '
//                                    . 'WHERE run_eshop_products_id = ' . $item['id'] . ' AND name="' . $dynAttrValue . '"');
//                            }
//                        }
//                    }
                    
                    // @synchro with EshopCart::getPrices()
                    // 
                    // normal products or single purpose absolute discount voucher (See 
                    // phpDoc of EshopVoucher::$schema > 'purpose_type')
                    if (
                        // if not voucher
                        (int)$item['run_eshop_products_id'] !== $voucherProductId
                        // or if single purpose voucher
                        || (
                            isset($voucherPurposes[$item['run_eshop_products_id']])
                            && $voucherPurposes[$item['run_eshop_products_id']] === 'single'
                        )
                    ) {
                        // into actual products price count both
                        $productPrices['products_price_actual_taxless'] += $item['price_actual_taxless'] * $item['amount'];
                        $productPrices['products_tax_actual'] += $item['tax_actual'] * $item['amount'];
                        // full price count only for normal products (there are no vouchers and discounts in full price)
                        if ((int)$item['run_eshop_products_id'] !== $voucherProductId) {
                            $productPrices['products_price_taxless'] += $item['price_taxless'] * $item['amount'];
                            $productPrices['products_tax'] += $item['tax'] * $item['amount'];
                        }
                    }
                    // multi purpose absolute discount voucher keep separately (do not count them 
                    // to products price) as their price is a kind of prepaid part of price to pay.
                    // See phpDoc of EshopVoucher::$schema > 'purpose_type'.
                    else {
                        $vouchersDiscount += abs($item['price_taxless']) * $item['amount'] 
                            + abs($item['tax']) * $item['amount'];
                    }
                }
                
                // @todo - resolve if the order is with VAT or without VAT
                $taxedOrder = true;
                $productPrices['products_price_to_pay'] = $productPrices['products_price_actual_taxless'];
                if ($taxedOrder) {
                    $productPrices['products_price_to_pay'] += $productPrices['products_tax_actual'];
                }
                if ($vouchersDiscount) {
                    $productPrices['products_price_to_pay'] -= $vouchersDiscount;
                }
                if (
                    array_key_exists('bonus_discount', $data)
                    && (float)$data['bonus_discount']
                ) {
                    $productPrices['products_price_to_pay'] -= (float)$data['bonus_discount'];
                }
            }
            
            // calculate order prices
            if ($isEditable && $calculateOrder) {
                // get shipment prices
                if (!empty($shipment)) {
                    $shipmentPrices = $EshopShipmentMethod->getPrices($shipment, array(
                        'productsPrices' => $productPrices, 
//                        'divided' => $data['divided']
                    ));
                }
                elseif (isset($data['run_eshop_shipment_methods_id'])) {
                    $shipmentPrices['shipment_price_taxless'] = $oldData['shipment_price_taxless'];
                    $shipmentPrices['shipment_price_actual_taxless'] = $oldData['shipment_price_actual_taxless'];
                    $shipmentPrices['shipment_tax'] = $oldData['shipment_tax'];
                    $shipmentPrices['shipment_tax_actual'] = $oldData['shipment_tax_actual'];
                }
                else {
                    $shipmentPrices['shipment_price_taxless'] = 0.0;
                    $shipmentPrices['shipment_price_actual_taxless'] = 0.0;
                    $shipmentPrices['shipment_tax'] = 0.0;
                    $shipmentPrices['shipment_tax_actual'] = 0.0;
                }
                // get payment prices
                if (!empty($payment)) {
                    $paymentPrices = $PaymentMethod->getPrices($payment, array(
                        'productsPrices' => $productPrices, 
//                        'divided' => $data['divided']
                    ));
                }
                elseif (isset($data['run_payment_methods_id'])) {
                    $paymentPrices['payment_price_taxless'] = $oldData['payment_price_taxless'];
                    $paymentPrices['payment_price_actual_taxless'] = $oldData['payment_price_actual_taxless'];
                    $paymentPrices['payment_tax'] = $oldData['payment_tax'];
                    $paymentPrices['payment_tax_actual'] = $oldData['payment_tax_actual'];
                }
                else {
                    $paymentPrices['payment_price_taxless'] = 0.0;
                    $paymentPrices['payment_price_actual_taxless'] = 0.0;
                    $paymentPrices['payment_tax'] = 0.0;
                    $paymentPrices['payment_tax_actual'] = 0.0;
                }
                // set updated order data
                $data['order_price_taxless'] 
                        = $productPrices['products_price_taxless'] 
                        + $shipmentPrices['shipment_price_taxless'] 
                        + $paymentPrices['payment_price_taxless'];
                $data['order_tax'] 
                        = $productPrices['products_tax'] 
                        + $shipmentPrices['shipment_tax'] 
                        + $paymentPrices['payment_tax'];
                $data['order_price_actual_taxless'] 
                        = $productPrices['products_price_actual_taxless'] 
                        + $shipmentPrices['shipment_price_actual_taxless'] 
                        + $paymentPrices['payment_price_actual_taxless'];
                $data['order_tax_actual'] 
                        = $productPrices['products_tax_actual'] 
                        + $shipmentPrices['shipment_tax_actual'] 
                        + $paymentPrices['payment_tax_actual'];
                // @synchro with EshopOrder::getPrices()
                // @todo - resolve if the order is with VAT or without VAT
                $taxedOrder = true;
                $data['order_price_to_pay'] = $productPrices['products_price_to_pay'];
                $data['order_price_to_pay'] += $shipmentPrices['shipment_price_actual_taxless']
                    + $paymentPrices['payment_price_actual_taxless'];            
                if ($taxedOrder) {
                    $data['order_price_to_pay'] += $shipmentPrices['shipment_tax_actual']
                        + $paymentPrices['payment_tax_actual'];
                }
                $data['products_price_taxless'] = $productPrices['products_price_taxless'];
                $data['products_tax'] = $productPrices['products_tax'];
                $data['products_price_actual_taxless'] = $productPrices['products_price_actual_taxless'];
                $data['products_tax_actual'] = $productPrices['products_tax_actual'];
                $data['products_price_to_pay'] = $productPrices['products_price_to_pay'];
                $data['shipment_price_taxless'] = $shipmentPrices['shipment_price_taxless'];
                $data['shipment_tax'] = $shipmentPrices['shipment_tax'];
                $data['shipment_price_actual_taxless'] = $shipmentPrices['shipment_price_actual_taxless'];
                $data['shipment_tax_actual'] = $shipmentPrices['shipment_tax_actual'];
                $data['payment_price_taxless'] = $paymentPrices['payment_price_taxless'];
                $data['payment_tax'] = $paymentPrices['payment_tax'];
                $data['payment_price_actual_taxless'] = $paymentPrices['payment_price_actual_taxless'];
                $data['payment_tax_actual'] = $paymentPrices['payment_tax_actual'];
                
            }
            
            $notesRecord = '';
            // if status has been changed, write to notes
            if (!empty($oldData) && isset($data['status']) && $oldData['status'] != $data['status']) {
                $notesRecord = self::getNotesLog(__a(__FILE__, 'STATUS') . ': ' . __(__FILE__, $data['status']));
            }
            // if payment status has been changed, write to notes
            if (!empty($oldData) && isset($data['payment_status']) && $oldData['payment_status'] != $data['payment_status']) {
                if (!empty($notesRecord)) {
                    $notesRecord .= "\n";
                }
                $notesRecord .= self::getNotesLog(__a(__FILE__, 'PAYMENT STATUS') . ': ' . __(__FILE__, $data['payment_status']));
            }
            // if admin note has been set, write to notes
            if (!empty($data['admin_comment'])) {
                if (!empty($notesRecord)) {
                    $notesRecord .= "\n";
                }
                if (!empty($data['send_admin_comment_to_client'])) {
                    $result = $this->sendCommentMessage($data['id'], $data['admin_comment']);
                    if ($result === true) {
                        App::setSuccessMessage(__a(__FILE__, 'Order comment has been sent to client'));
                    }
                    elseif ($result === false) {
                        App::setErrorMessage(__a(__FILE__, 'Order comment e-mail has failed'));
                    }
                    else {
                        App::setInfoMessage(__a(__FILE__, 'Order comment has not been sent. Please check if comment e-mail template is set in eshop settings.'));
                    }
                }
                if (
                    empty($data['send_admin_comment_to_client'])
                    || $result === null
                ) {
                    $label = __a(__FILE__, 'COMMENT');
                }
                elseif ($result === true) {
                    $label = __a(__FILE__, 'COMMENT [Sent to client]');
                }
                elseif ($result === false) {
                    $label = __a(__FILE__, 'COMMENT [Sent to client failed]');
                }
                $notesRecord .= self::getNotesLog($label . ': ' . $data['admin_comment']);
            }
            // if something change, writo to notes
            if (!empty($data['_changes'])) {
                $changes = explode(';', $data['_changes']);
                $changes = array_filter($changes);
                if (!empty($changes)) {
                    if (!empty($notesRecord)) {
                        $notesRecord .= "\n";
                    }
                    $notesRecord .= self::getNotesLog(__a(__FILE__,  'CHANGES') . ': ' . implode(', ', $changes));
                }
            }
            
            // set notes field
            // ATTENTION: Keep the same logic also in EshopOrders::admin_ship()
            if (!empty($notesRecord)) {
                if (empty($oldData['notes'])) {
                    $data['notes'] = $notesRecord;
                }
                else {
                    $data['notes'] = $oldData['notes'] . "\n" . $notesRecord;
                }
            }
            
            if ($this->save($data, array('alternative' => 'backend'))) {
                // on request of Alterego payment emails are not sent when changing 
                // payment status in admin. They do it frequently to correctly treat bonus point
                /*/ 
                // check if order has been paid and let know the user
                if (
                    !empty($oldData) 
                    && isset($data['payment_status']) 
                    && $oldData['payment_status'] != $data['payment_status'] 
                    && $data['payment_status'] == 'enum_payment_paid'
                ) {
                    if ($this->sendPaymentSuccessfulEmail($data['id'], array('onceOnly' => true))) {
                        App::setSuccessMessage(__a(__FILE__, 'Successful payment e-mail has been sent to client'));
                    } 
                    else {
                        App::setErrorMessage(__a(__FILE__, 'Successful payment e-mail has failed'));
                    }
                }
                //*/
                // ATTENTION: Keep the same logic also in EshopOrders::admin_ship()
                
                // check if status has been changed
                if (
                    !empty($oldData) 
                    && isset($data['status']) 
                    && $oldData['status'] != $data['status']
                ) {
                    /*/// @unused on alterego
                    // create invoice pdf for shipped orders
                    // (pickup orders do not have invoices as client will get bill in shop)
                    if (
                        $data['status'] === 'enum_shipped_order'    
                        //&& empty($oldData['invoice_pdf'])
                    ) {
                        try {
                            $this->createInvoicePdfFile($data['id']);
                        } 
                        catch (Throwable $e) {
                            App::setErrorMessage(__a(__FILE__, 'Invoice creation has failed'));
                            App::logError($e->getMessage(), array(
                                'var' => $e,
                                'email' => true
                            ));
                        }
                    }
                    /*/
//                    // solve product reservations
//                    if ($data['status'] == 'enum_new_order' && ($oldData['status'] == 'enum_canceled_order' || $oldData['status'] == 'enum_test_order')) {
//                        // increase reservations and stock
//                        foreach ($data['products'] as $item) {
//                            DB::query('UPDATE ' . $EshopProduct->getPropertyTable() . ' '
//                                . 'SET reserved = reserved + ' . $item['reserved'] . ' '
//                                . 'WHERE id = ' . $item['id']);
//                        }
//                    }
//                    if (($data['status'] == 'enum_canceled_order' || $data['status'] == 'enum_test_order') && $oldData['status'] == 'enum_new_order') {
//                        // decrease reservations and stock
//                        foreach ($data['products'] as $item) {
//                            DB::query('UPDATE ' . $EshopProduct->getPropertyTable() . ' '
//                                . 'SET reserved = reserved - ' . $item['reserved'] . ' '
//                                . 'WHERE id = ' . $item['id']);
//                        }
//                    }
                    
                    $result = $this->sendStatusChangeMessages($data['id']);
                    if ($result['email'] === true) {
                        App::setSuccessMessage(__a(__FILE__, 'Order status change notification e-mail has been sent to client'));
                    }
                    elseif ($result['email'] === false) {
                        App::setErrorMessage(__a(__FILE__, 'Order status change notification e-mail has failed'));
                    }
                    else {
                        App::setInfoMessage(__a(__FILE__, 'Order status change notification e-mail has not been sent'));
                    }
                    if ($result['sms'] === true) {
                        App::setSuccessMessage(__a(__FILE__, 'Order status change notification sms has been sent to client'));
                    }
                    elseif ($result['sms'] === false) {
                        App::setErrorMessage(__a(__FILE__, 'Order status change notification sms has failed'));
                    }
                    else {
                        App::setInfoMessage(__a(__FILE__, 'Order status change notification sms has not been sent'));
                    }
                }
            } 
            else {
                DB::rollbackTransaction('EshopOrder_saveAll');
                $this->unreserveTables('EshopOrder_saveAll');
                return false;
            } 
        } 
        catch (Throwable $e) {
            DB::rollbackTransaction('EshopOrder_saveAll');
            $this->unreserveTables('EshopOrder_saveAll');
            throw $e;
        }
        DB::commitTransaction('EshopOrder_saveAll');
        $this->unreserveTables('EshopOrder_saveAll');
        return true;
    }
    
    /**
     * Returns log message for EshopOrder.notes
     * 
     * @param string $message
     * @param array $options Following are available:
     *      - 'author' (string|NULL|FALSE) Message author name. If FALSE then no 
     *          author name is added to log. If any other empty value then the logged in
     *          user name is used. Defaults to NULL (logged in user).
     *      - 'alert' (bool) If TRUE then the generated log message is colored
     *          by alert color. Defaults to FALSE.
     * 
     * @return string
     */
    public static function getNotesLog($message, $options = array()) {
        $options = array_merge(array(
            'author' => null,
            'alert' => false,
        ), $options);
        if ($options['author'] === null) {
            $user = App::getUser();
            $options['author'] = $user['first_name'] . ' ' . $user['last_name'];
        }
        $log = '>> ' . date('d.m.Y H:i');
        if ($options['author']) {
            $log .= ', ' . $options['author'];
        }
        $log .= ': ' . $message;
        if ($options['alert']) {
            $log = '<span style="color:red;">' . $log . '</span>';
        }
        return $log;
    }
        
    /**
     * Sends email and sms messages to specified order user about the change of order status 
     * if it is applicable (user has specified email/sms and there are also specified relative settings).
     * Sms messages are sent only in case of enum_pickup_order and enum_shipped_order statuses
     * 
     * ATTENTION: This method does not check if the order status has been really changed.
     * It just takes the actual order status and sends one of corresponding emails
     * (settings EshopOrder.msgBodyStatusChange.enum_???_order). Check of the status 
     * change is up to user calling this method.
     * 
     * @param int $orderId
     * 
     * @return array Array containing keys 'email' and 'sms'. Under each key is stored
     *      a result of corresponding message type send. TRUE for success, FALSE for failure
     *      and NULL for case then the message type is not applicable (either no message text
     *      of subject is set or user has no email or no phone number).
     */
    public function sendStatusChangeMessages($orderId) {
        $result = array(
            'email' => null,
            'sms' => null,
        );
        $status = $this->findFieldBy('status', 'id', $orderId);
        // do not send any message for following status(es)
        if ($status === 'enum_closed_order') {
            return $result;
        }
        $inserts = $this->getInserts($orderId);
        // email
        if (!empty($inserts['userEmail'])) {
            $emailBody = $this->getSetting('EshopOrder.msgBodyStatusChange.' . $status);
            $emailSubject = $this->getSetting('EshopOrder.msgSubjectStatusChange.' . $status);
            if (!empty($emailBody) && !empty($emailSubject)) {
                $attachments = array();
                if (
                    !empty($inserts['invoiceUrl'])
                    && $status === 'enum_shipped_order'
                    // invoices are not sent for pickup orders as client will get bill in shop
                ) {
                    $attachments[] = File::normalizeDS($inserts['invoiceUrl']);
                }
                try {
                    if (App::sendEmail(
                        $emailBody, 
                        $inserts['userEmail'], 
                        array(
                            'subject' => $emailSubject,
                            'from' => array(
                                $this->getSetting('email.from') => App::getSetting('App', 'name')
                            ),
                            'inserts' => $inserts,
                            'embedImages' => true,
                            'attachments' => $attachments,
                        )
                    )) {
                        $result['email'] = true;
                    } 
                    else {
                        $result['email'] = false;
                    }
                } 
                catch (Throwable $e) {
                    $result['email'] = false;
                    App::logError($e->getMessage(), array(
                        'var' => $e,
                        'email' => true
                    ));
                }
            } 
        }
        // sms
        if (
            !empty($inserts['userPhone'])
            && (
                $status === 'enum_pickup_order'
                || $status === 'enum_shipped_order'
            )
        ) {
            if ($status === 'enum_pickup_order') {
                $message = $this->getSetting('EshopOrder.smartsmsMessageA');
            }
            else {
                $message = $this->getSetting('EshopOrder.smartsmsMessageB');
            }
            if (!empty($message)) {     
                $message = App::getSetting('App', 'name') . ': ' . $message;
                try {
                    if (App::sendSms(
                        $message, 
                        $inserts['userPhone'], 
                        array(
                            'inserts' => $inserts,
                        )
                    )) {
                        $result['sms'] = true;
                    } 
                } 
                catch (Throwable $e) {
                    $result['sms'] = false;
                    App::logError($e->getMessage(), array(
                        'var' => $e,
                        'email' => true
                    ));
                }
            }
        }
        return $result;
    }
    
    /**
     * Sends email message with order comment to specified order user 
     * 
     * @param int $orderId
     * @param string $commentText
     * 
     * @return bool|NULL TRUE for success, FALSE for failure, NULL if settings
     *      EshopOrder.msgBodyNewComment or EshopOrder.msgSubjectNewComment are empty
     */
    public function sendCommentMessage($orderId, $commentText) {
        $result = true;
        try {
            $inserts = $this->getInserts($orderId);
            if (!empty($inserts['userEmail'])) {
                $emailBody = $this->getSetting('EshopOrder.msgBodyNewComment');
                $emailSubject = $this->getSetting('EshopOrder.msgSubjectNewComment');
                if (!empty($emailBody) && !empty($emailSubject)) {
                    $inserts['commentText'] = $commentText;
                    $inserts['commentAuthor'] = App::getUser('first_name') . ' ' 
                        . App::getUser('last_name');
                    if (App::sendEmail(
                        $emailBody, 
                        $inserts['userEmail'], 
                        array(
                            'subject' => $emailSubject,
                            'from' => array(
                                $this->getSetting('email.from') => App::getSetting('App', 'name')
                            ),
                            'inserts' => $inserts,
                            'embedImages' => true,
                        )
                    )) {
                        $result = true;
                    } 
                } 
                else {
                    $result = null;
                }
            }
            else {
                $result = null;
            }
        } 
        catch (Throwable $e) {
            App::logError($e->getMessage(), array(
                'var' => $e,
                'email' => true
            ));
        }
        return $result;
    }
    
    /**
     * Returns delivery country ISO code 2 of actually checkouted order
     * 
     * @return string
     */
    public function getDeliveryCountry() {
        $this->loadCheckoutWizard();
        $checkout = $this->Checkout->getParameterAllSteps('data', true);
        return $checkout['deliveryAddress'] !== 'otherAddress' 
                ? $checkout['country'] : $checkout['delivery_country'];
    }
    
    /**
     * Returns actual delivery time (number of days) for the specified order.
     * 
     * @param int $orderId
     * @return int
     */
    public function getDeliveryTime($orderId) {
        $order = $this->findFirstBy('id', $orderId, array(
            'fields' => array(
                'run_eshop_shipment_methods_id',
                'created',
            ),
        ));
        // find out latest shipment time
        $products = $this->getProductsDetails($orderId);
        $shipmentTime = 0;
        foreach ($products as $product) {
            if ($product['total_shipment_time'] > $shipmentTime) {
                $shipmentTime = $product['total_shipment_time'];
            }
        }
        // find out transport time
        $transportTime = $elapsedTime = 0;
        if ($order['run_eshop_shipment_methods_id']) {
            $Shipment = $this->loadModel('EshopShipmentMethod', true);
            $transportTime = $Shipment->findFieldBy('id', $order['run_eshop_shipment_methods_id'], 'EshopShipmentMethod.delivery_time');
            // find out elapsed days from order creation
            $elapsedTime = Date::getDiff('d', $order['created'], date('Y-m-d H:i:s'));
        }
        
        return $shipmentTime + $transportTime - $elapsedTime;
    }
    
    /**
     * Checks if actually checkouted order has abroad delivery
     * 
     * @return bool
     */
    public function hasAbroadDelivery() {
        $Shipment = $this->loadModel('EshopShipmentMethod', true);
        return $Shipment->hasAbroadDelivery($this->getDeliveryCountry());
    }
    
    /**
     * Return list of pickup places of specified $provider for actually checkouted order. 
     * Returned list contains pairs '{placeID}' => '{placeAddressString}'.
     * 
     * @param string $provider Pickup places provide. One of 'zasielkovna', 'geisPoint'.
     * 
     * @return array
     */
    public function getPickupPlacesList($provider) {
        $checkout = $this->Checkout->getParameterAllSteps('data', true);
        $Shipment = $this->loadModel('EshopShipmentMethod', true);
        if ($checkout['deliveryAddress'] !== 'otherAddress') {
            $country = $checkout['country'];
            $city = $checkout['city'];
            $zip = $checkout['zip'];
        }
        else {
            $country = $checkout['delivery_country'];
            $city = $checkout['delivery_city'];
            $zip = $checkout['delivery_zip'];
        }
        // take the first nonempty result
        $provider = strtolower($provider);
        if (
//            ($places = $Shipment->getPickupPlaces($provider, array(
//                'country' => $country
//                'zip' => $zip,
//            )))
//            ||        
//            ($places = $Shipment->getPickupPlaces($provider, array(
//                'country' => $country
//                'city' => $city,
//            )))
//            ||        
            ($places = $Shipment->getPickupPlaces($provider, array(
                'country' => $country
            )))
        ) {}
        $list = array();
        foreach($places as $place) {
            $list[$place['id']] = $place['city'] . ', ' . $place['street'] . ', ' . $place['place'];
        }
        if (class_exists('Collator')) {            
            $collator = new Collator(App::$locale);    
            $collator->asort($list);
        }
        elseif ($provider === 'geisPoint') {
            asort($list);
        }
        return $list;
    }
    
    /**
     * Generates html or pdf file of specified order invoice
     * 
     * @param int $orderNumber Original order number
     * @param array $options Following are available:
     *      - 'format' (string) Possible values are 'html', 'pdf'. Defaults to 'pdf'.
     *      - 'output' (string) Possible values are 'download', 'inline', 'save'.
     *          Ignored if 'format' is set to 'html'. Defaults to 'inline'.
     *      - 'name' (string) Created file name in case of 'format' set to 'pdf'. 
     *          Ignored if 'format' is set to 'html'. If 'output' is set to 'save' 
     *          then an app root relative path have to be provided. Defaults to uniqid().
     * @param string& $invoiceNumber Optional. Aux output. If provided then an invoice 
     *      number is returned in this variable.
     * 
     * @return string|void Html string of html file or void if pdf (it is downloaded or
     *      server inline)
     * @throws Exception
     */
    public function getInvoiceFile($orderNumber, $options = array(), &$invoiceNumber = null) {
        $defaults = array(
            'format' => 'pdf',
            'output' => 'inline',
            'name' => uniqid(),
        );
        $options = array_merge($defaults, $options);
        $format = $options['format'];
        
////debug cache>        
//// to allow fast debugging (avoid MRP request on each page reload) do cache the MRP response
//// (uncomment also the code here below)
//        $cacheFile = TMP . DS . 'invoice-test-' . $invoiceNumber . '.txt'; //debug
//        if (!file_exists($cacheFile)) { //debug
////<debug cache        
            
        //get invoice data
        App::loadLib('Eshop', 'MrpRequest');
        $serverIpAddress = App::getSetting('Eshop', 'mrp.serverIpAddress');
        $serverPort = App::getSetting('Eshop', 'mrp.serverPort');
        $MrpRequest = new MrpRequest($serverIpAddress, $serverPort, array(
            'privateKey' => App::getSetting('Eshop', 'mrp.privateKey'),
        ));
        $MrpResponse = $MrpRequest->getInvoices(array(
            'OriginalOrderNumber' => $orderNumber,
        ));
        $invoices = array();
        while ($invoice = $MrpResponse->readRecord()) {    
            $invoices[] = $invoice;
        }
        if (count($invoices) < 1) {
            throw new Exception(__e(__FILE__, 'No invoice has been found for specified order number %s', $orderNumber));
        }
        elseif (count($invoices) > 1) {
            throw new Exception(__e(__FILE__, 'Many invoices have been found for specified order number %s', $orderNumber));
        }
        $invoice = reset($invoices);
        // normalize invoice data to the same structure as the invoice data stored
        // in debug cache file
        $invoice = json_decode(json_encode($invoice));
        
////debug cache>       
//// to allow fast debugging (avoid MRP request on each page reload) do cache the MRP response
//// (uncomment also the code here above)
//        file_put_contents($cacheFile, json_encode($invoice)); //debug
//        } //debug
//        $invoice = json_decode(file_get_contents($cacheFile)); //debug
////<debug cache        
        
        // load invoice items eans;
        $items = @(array)$invoice->Items->Item;
        // XML is parsed a bit strangelly, if there is 2 and more <Item> tags 
        // under tag <Items> then its ok. If There is just one <Item> tag then its
        // fields are directly keays of $items. Normalize it in following way:
        if (Validate::assocArray($items)) {
            $items = array($invoice->Items->Item);
        }
        $codes = array();
        foreach ($items as $item) {
            $codes[] = Number::removeTrailingZeroDecimals(@(string)$item->StockCardNumber);
        }
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $eans = $Product->findList(array(
            'key' => 'code',
            'fields' => array('ean'),
            'conditions' => array(
                'code' => $codes
            )
        ));
        unset($codes);
        
        $local = $format !== 'html';
        
        // aux annonymous function to get invoice html
        $getHtml = function  ($part = null) use (&$invoice, &$eans, &$local, &$format) {
            $viewVariables = array();
            $html = $this->loadView(
                'EshopOrder/getInvoiceFile', 
                array(
                    'invoice' => $invoice,
                    'eans' => $eans,
                    'local' => $local,
                    'format' => $format,
                    'part' => $part,
                ), 
                null, 
                array(
                    'cssFiles',
                    'cssCodes',
                    'jsFiles', 
                    'jsCodes',
                ), 
                $viewVariables
            );

            $layoutParams = $viewVariables;
            $layoutParams['content'] = $html;
            $layoutParams['title'] = __(__FILE__, 'Invoice %s', @(string)$invoice->DocumentNumber);
            $layoutParams['cssClass'] = 'eshop-order-invoice-layout';
            $layoutParams['local'] = $local;
            $html = App::loadLayout('App', 'standalone', $layoutParams);
            $layoutParams['head'] = ''; // head is included together with body part
            $layoutParams['body'] = $html;
            return App::loadDoctype('App', 'html5', $layoutParams);
        };
        
        // validate and normalize file name if it is going to be saved
        if (
            $options['output'] === 'save'
            && $format !== 'html'
        ) {
            $nameInfo = File::getPathinfo($options['name']);
            if (!$nameInfo['dirname']) {
                throw new Exception(__e(__FILE__, 'Invoice file creation: \'name\' option must be an app root relative path (not only filename)'));
            }
            $options['name'] = ROOT . DS . File::normalizePath($options['name']);
        }
        
        $invoiceNumber = @(string)$invoice->DocumentNumber;
        
        // render invoice html into required format
        if ($format === 'pdf') {
            // for all options see https://wkhtmltopdf.org/usage/wkhtmltopdf.txt
            // installation details see in misc/docs/implementationDetails.txt > PRENOS FAKTUR Z MRP A ODOSLANIE FAKTURY ZÁKAZNÍKOVI
            $Pdf = new mikehaertl\wkhtmlto\Pdf(array(
                //'binary' => '/usr/bin/wkhtmltox/bin/wkhtmltopdf',
                'no-outline',         // Make Chrome not complain
                // following margins keep in synchro with css (.invoice padding)
                'margin-top' => 7.1, 
                'margin-right' => 10.2,
                'margin-bottom' => 10.2,
                'margin-left' => 10.2,
                'page-height' => 297,   // in mm !!! (floats are accepted)
                'page-width' => 210,     // in mm !!! (floats are accepted)
                'dpi' => 300,
                // to make it work the 'header-html' option of wkhtmltopdf must be provided as URL (not html code, as possible for page)
                //'header-html' => $getHtml('header'),
                'disable-smart-shrinking',
                // allow access to local files as by default, newer versions
                // of wkhtmltopdf block access to local files for security reasons
                'enable-local-file-access',
            ));
            //$Pdf->addPage($getHtml('body'));
            $Pdf->addPage($getHtml());
            if ($options['output'] === 'save') {
                $success = $Pdf->saveAs($options['name']);
            }
            elseif($options['output'] === 'download') {
                $success = $Pdf->send($options['name']);
            }
            else {
                $success = $Pdf->send($options['name'], true);
            }
            if (!$success) {
                throw new Exception(__e(__FILE__, 'Invoice pdf creation error: %s', $Pdf->getError()));
            }
        }
        else {
            return $getHtml();
        }
    }
    
    /**
     * Creates invoice pdf for specified order.
     * 
     * @param int $id Order id
     * @throws Exception
     */
    public function createInvoicePdfFile($id) {
        $orderNumber = $this->findFieldBy('number', 'id', $id);
        $tmpPath = File::getRelativePath(TMP . DS . 'EshopOrder');
        if (!File::ensurePath($tmpPath)) {
            throw new Exception('Creation of path %s has failed', $tmpPath);
        }
        $token = Str::getRandom(20);
        $tmpPdfFile = __(__FILE__, 'Invoice-%s-%s.pdf', $orderNumber, $token);
        $tmpPdfFile = $tmpPath . DS . $tmpPdfFile;
        try {
            $this->getInvoiceFile($orderNumber, array(
                'format' => 'pdf',
                'output' => 'save',
                'name' => $tmpPdfFile,
            ));
            $this->save(
                array(
                    'id' => $id,
                    'invoice_pdf' => array($tmpPdfFile),
                ),
                array('validate' => false)
            );
            unlink(ROOT . DS . $tmpPdfFile);
        } 
        catch (Throwable $e) {
            unlink(ROOT . DS . $tmpPdfFile);
            throw $e;
        }
    }
    
    public function validate_checkoutCompany($fieldValue, $fieldName, $data, &$validation) {
        if (!empty($data['subject_type']) && $data['subject_type'] == 'enum_company') {
            $validation['exit'] = false;
        }
        return true;
    }
    
    public function validate_checkoutAddress($fieldValue, $fieldName, $data, &$validation) {
        if (!empty($data['deliveryAddress']) && $data['deliveryAddress'] == 'otherAddress') {
            $validation['exit'] = false;
        }
        return true;
    }      
    
    public function validate_compatiblePayment($fieldValue, $fieldName, $data, &$validation) {
        if (
            !empty($fieldValue)
            && !empty($data['shipment'])
        ) {
            $ShipmentPayment = $this->loadModel('EshopShipmentPaymentMethod', true);
            if (!$ShipmentPayment->findFirst(array('conditions' => array(
                'run_eshop_shipment_methods_id' => $data['shipment'],
                'run_payment_methods_id' => $fieldValue,
            )))) {
                return false;
            }
        }
        return true;
    }         
    
    public function validate_requirePickupPlace($fieldValue, $fieldName, $data) {
        if (
            !empty($data['shipment'])
            && isset($fieldValue[$data['shipment']])
            && empty($fieldValue[$data['shipment']])
        ) {
            return false;
        }
        return true;
    }
    
    public function validate_phoneCallingCode($fieldValue, $fieldName, $data, &$validation) {
        // do not force calling code in case of phone if delivery_phone should be provided
        if (
            $fieldName === 'phone'
            && !empty($data['deliveryAddress'])
            && $data['deliveryAddress'] == 'otherAddress'
        ) {
            return true;
        }
        $countryCode = null;
        if (
            $fieldName === 'phone'
            && !empty($data['country'])
        ) {
            $countryCode = $data['country'];
        }
        elseif (
            $fieldName === 'delivery_phone'
            && !empty($data['delivery_country'])
        ) {
            $countryCode = $data['delivery_country'];
        }
        if ($countryCode) {
            App::loadModel('App', 'Country');
            $Country = new Country();
            $country = $Country->findFirst(array(
                'conditions' => array(
                    'Country.iso_code_2' => $countryCode
                ),
                'fields' => array(
                    'Country.name',
                    'Country.calling_code',
                )
            ));
            if (
                $country
                && $country['calling_code']
                && substr($this->normalizePhone($fieldValue), 1, strlen($country['calling_code'])) 
                    !== (string)$country['calling_code']
            ) {
                $validation['message'] = __v(
                    __FILE__, 
                    'Please provide a phone number valid for country %s', 
                    $country['name']
                );
                return false;
            }
        }
        return true;
    }
    
    /**
     * Contains the order an oversized product(s)?
     *
     * @param int $id Order id
     * 
     * @return bool
     */
    public function hasOversizedProducts($id) {
        $this->loadModel('EshopOrderProduct');
        $OrderProduct = new EshopOrderProduct();
        $productIds = $OrderProduct->findList(array(
            'key' => 'run_eshop_products_id',
            'fields' => 'run_eshop_products_id',
            'conditions' => array(
                'run_eshop_orders_id' => $id,
            )
        ));
        if (!$productIds) {
            return false;
        }
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        return $Product->isOversized($productIds);
    }
}
class Exception_EshopOrder_ManipulatedOrderData extends Exception {}