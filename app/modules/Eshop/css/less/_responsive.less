@import "_variables.less";
@import "_mixins.less";

//
// CUSTOM MEDIA QUERIES
//

// INITIAL QUERY AT CENTER-WIDTH
@media screen and (max-width: (@center-width + @grid-cols-gap-width)) {
    //...
}
@media screen and (max-width: 1100px) {
    .cb-products-slider {
        // keep these styles in synchro with slider responsive options in view
        &.with-image {
            .products {
                width: 590px / 890px * 100%;
            }
        }
    }
}
@media screen and (max-width: 800px) {
    .cb-products-slider {
        // keep these styles in synchro with slider responsive options in view
        &.with-image {
            .products {
                width: 290px / 590px * 100%;
            }
        }
    }
}
@media screen and (max-width: 680px) {
    .cb-products-slider {
        > .wrapper, > .center > .wrapper {
            > .button-wrapper {
                text-align: right;
                position: static;
                height: 30px;
                margin: -4px 0 16px;
            }
        }
    }
}
@media screen and (max-width: 540px) {
    .cb-products-slider {
        // keep these styles in synchro with slider responsive options in view
        &.with-image {
            > .wrapper, > .center > .wrapper {
                > .button-wrapper {
                    position: static;
                }
                > .image-wrapper {
                    > img {
                        display: none;
                    }                
                }
            }
            .products {
                width: 100%;
            }
        }
    }
}
 
