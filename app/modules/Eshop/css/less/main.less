@import "_variables.less";
@import "_mixins.less";

// MODULE COMMON STYLES
// ...

.cb-products-slider {
    padding-top: 50px;
    padding-bottom: 30px;
    > .wrapper, > .center > .wrapper {
        position: relative; // .button-wrapper
        > .button-wrapper {
            position: absolute;
            top: 0;
            right: 0;
            .button {
                color: @color-red;
                background-color: @color-white;
                transition: background-color .2s;
                &:focus,
                &:hover {
                    color: @color-white;
                    background-color: @color-red;
                }
            }
        }
    }
    &.no-title {
        > .wrapper, > .center > .wrapper {
            > .button-wrapper {
                text-align: right;
                position: static;
                height: 30px;
                margin: -4px 0 16px;
            }
        }
    }
    &.with-image {
        padding-bottom: 30px;
        > .wrapper, > .center > .wrapper {
            > .title {
                position: relative;
                z-index: 1; // to display over image
            }
            > .button-wrapper {
                position: absolute;
                top: auto;
                bottom: 20px;
                right: 0;
                z-index: 1;
            }
            > .image-wrapper {
                position: relative; // > img
                // responsivity is done by js in view
                > img {
                    position: absolute;
                    top: -50px;
                    right: -190px;
                    border-radius: 6px;
                }
            }
        }
        .products {
            width: 890px / @center-width * 100%;
        }
    }
    & + .cb-products-slider {
        padding-top: 0px;
    }
}

// MODULE RESPONSIVE STYLES
@import (less) "_responsive.less";
