@import "_variables.less";
@import "_mixins.less";

.related-items {
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 15px;
    background: white;
    .index {
        .template {
            display: none;
        }
        .item {
            // keep this styles in accord with app/css/libs/less/basic.less > .display-delete-button
            .delete {
                display: inline-block;
                margin-top: 15px;
                color: #000;
                font-size: 26px;
                font-weight: 700;
                text-decoration: none;
                opacity: 0.5;
                cursor: pointer;
                &:hover {
                    opacity: 1;
                    color: #C30404;
                }
            }            
        }
    }
}

.eshop-order-form {
    .order-products, .order-totals {
        padding: 0 15px;
        background: #fff;
    }
    .order-products {
        .order-product {
            .-run-action {
                display: inline-block;
                margin-top: 20px;
            }
        }
    }
}

.eshopx-view {
    padding: 0 18px 15px 25px;
    background-color: #f1f1f1;
    .title {
        line-height: 1.05em;
        .button {
            font-size: 14px;
            font-weight: normal;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            touch-action: manipulation;
            cursor: pointer;
            user-select: none;            
            float: right;
            margin: -7px 0 0 9px;
            background: 0 0;
            border-radius: 0;
            height: auto;
            line-height: 1.42857143;
            padding: 5px 13px;
            transition: all .2s;
            text-decoration: none;
            line-height: 29px;
            border: none;
            color: #555;            
            &:hover {
                color: #333;
            }
            &.close-button:before {
                .font-awesome-icon('\f057');
                font-size: 19px;            
                margin-right: 7px;                                
            }
            &.edit-button:before {
                .font-awesome-icon('\f040');
                font-size: 19px;            
                margin-right: 7px;                                
            }
        }
    }
    h1.title {
        padding: 21px 25px;
        margin: 0 -18px 15px -25px;
        line-height: 1.05em;
        font-size: 21px;
        font-weight: normal;
        color: #555;
        background-color: #fff;
        box-shadow: 0 1px 2px 0 rgba(0,0,0,.2);            
        border-bottom: 1px solid @color-admin-header-border;
    }
    h2.title {
        margin-top: 20px;
        margin-bottom: 20px;
        padding: 2px 0 5px 5px;
        font-size: 17px;
        font-weight: 500;
        color: #006BA5;
        line-height: 1.1;
    }
    .color {
        display: inline-block;
        margin-left: 0.5em;
        height: 0.75em;
        width: 0.75em;
    }
    dl {
        display: block;
        margin-top: 1em;
        dt {
            display: inline;
            font-weight: bold;
            &:after {
                content: ':';
            }
        }
        dd {
            display: inline;
            margin-left: 1em;
            &.description,
            &.attachments,
            &.contacts,
            &.events {
                display: block;
                margin: 1em 0 0 0;
            }
        }
    }
}
