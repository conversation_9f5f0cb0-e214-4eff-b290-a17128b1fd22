@import "_variables.less";
@import "_mixins.less";
@import "_animations.less";
@import "_fonts.less";


// Styles for view EshopOrder/getInvoiceFile
.invoice {
    min-height: 276mm;
    padding: 0;
    font-size: 8pt;
    line-height: 1.4;
    &.invoice-html-preview {        
        width: 210mm;
        min-height: 297mm;
        margin: 5mm auto 5mm auto;
        box-shadow: 0px 0px 10px 0px #d7d7d7;
        // following padding keep in synchro with pdf margins in EshopOrder::getInvoiceFile()
        padding: 7.1mm 10.2mm 10.2mm 10.2mm;
    }
    @content-width: 190.1mm;
    .content {
        position: relative; // .footer
        padding-bottom: 12mm; // space for .footer
        min-height: 276mm;
        border: 1px solid black;
        .header {
            border-bottom: 1px solid black;
            .header-left {
                display: inline-block;
                width: 87mm / @content-width * 100%;
                vertical-align: top;
                border-right: 1px solid black;
                .supplier {
                    padding: 5mm 4mm 2.5mm 4mm;
                    border-bottom: 1px solid black;
                    .company-id {
                        float: right;
                        width: 50mm;
                    }
                    .company-label {
                        font-weight: bold;
                    }
                    .company {
                        font-weight: bold;
                        font-size: 10pt;
                        margin-bottom: 3mm;
                    }
                    .address {
                        line-height: 2;
                    }
                    .tax-ids {
                        margin-bottom: 2mm;
                        span {
                            display: inline-block;
                            margin-right: 3mm;
                            white-space: nowrap;
                        }
                    }
                    .bank-accounts {
                        //line-height: 1.6;
                        margin-bottom: 3mm;
                    }
                    .registrator-info {
                        line-height: 1.6;
                    }
                }
                .dates {
                    padding: 2.7mm 4mm 2.7mm 4mm;
                    .label {
                        display: inline-block;
                        width: 30mm;
                    }
                }
            }
            .header-right {
                display: inline-block;
                width: 103.1mm / @content-width * 100%;
                vertical-align: top;
                .numbers {
                    padding: 4mm 4mm 6mm 5mm;
                    border-bottom: 1px solid black;
                    .page-number {
                        display: none;
                    }
                    .invoice-number {
                        font-size: 12pt;
                        font-weight: bold;
                    }
                    .order-number {
                        font-size: 10pt;
                        font-weight: bold;
                    }
                }
                .customer {
                    padding: 5mm 4mm 2.5mm 4mm;
                    .company-id {
                        float: right;
                        width: 50mm;
                    }
                    .company-label {
                        font-weight: bold;
                    }
                    .company {
                        font-weight: bold;
                        font-size: 10pt;
                        margin-bottom: 3mm;
                    }
                    .address {
                        line-height: 2;
                    }
                    .tax-ids {
                        margin-bottom: 2mm;
                        span {
                            display: inline-block;
                            margin-right: 3mm;
                            white-space: nowrap;
                        }
                    }
                }
            }
            .items-header {
                font-weight: bold;
                border-top: 1px solid black;
                padding: 6mm 0 4mm 0;
                div {
                    display: inline-block;
                    &.name {
                        padding-left: 4mm;
                        width: 100mm / @content-width * 100%;
                    }
                    &.amount {
                        width: 23mm / @content-width * 100%;
                    }
                    &.unit-price {
                        width: 15mm / @content-width * 100%;
                    }
                    &.vat {
                        width: 13mm / @content-width * 100%;
                    }
                    &.discount {
                        width: 20mm / @content-width * 100%;
                    }
                }
            }
            &.small {
                .header-left {
                    border-right: none;
                    .supplier {
                        display: none;
                    }
                    .dates {
                        display: none;
                    }
                }
                .header-right {
                    border-left: 1px solid black;
                    .numbers {
                        border-bottom: none;
                        padding: 2.5mm 4mm 5mm 5mm;
                        .page-number {
                            display: block;
                            float: right;
                        }
                        .invoice-number {
                            font-size: 10pt;
                        }
                        .order-number {
                            display: none;
                        }
                    }
                    .customer {
                        display: none;
                    }
                }
                .items-header {
                    padding: 3mm 0;
                }
            }
        }
        .body {
            .invoice-items {
                padding-top: 3mm;
                border-bottom: 1px solid black;
                .item {
                    padding-top: 1mm;
                    padding-bottom: 1mm;
                    div {
                        display: inline-block;
                        vertical-align: top;
                        &.ean {
                            padding-left: 4mm;
                            width: 30mm / @content-width * 100%;
                        }
                        &.name {
                            width: 80mm / @content-width * 100%;
                        }
                        &.amount {
                            width: 10mm / @content-width * 100%;
                            text-align: right;
                        }
                        &.unit-price {
                            width: 20mm / @content-width * 100%;
                            text-align: right;
                        }
                        &.vat {
                            width: 10mm / @content-width * 100%;
                            text-align: right;
                        }
                        &.discount {
                            width: 13mm / @content-width * 100%;
                            text-align: right;
                        }
                        &.price {
                            width: (@content-width - 163mm) / @content-width * 100%;
                            padding-right: 5mm;
                            text-align: right;
                        }
                    }
                }
            }
            .items-totals {
                padding-top: 1mm;
                margin-bottom: 14mm;
                div {
                    display: inline-block;
                    &.amount {
                        width: 120mm / @content-width * 100%;
                        text-align: right;
                    }
                    &.savings {
                        width: (@content-width - 120mm) / @content-width * 100%;
                        padding-left: 10mm;
                        padding-right: 4mm;
                        text-align: right;
                        span {
                            float: left;
                        }
                    }
                    &.price {
                        display: block;
                        padding-top: 6mm;
                        padding-right: 2mm;
                        font-weight: bold;
                        text-align: right;
                        span {
                            display: inline-block;
                            width: 28mm;
                            margin-left: 2.2mm;
                            padding: 0.5mm 1.5mm;
                            border: 2px solid black;
                        }
                    }
                }
            }
            .vat-info {
                margin-left: 4mm;
                width: 103mm;  
                padding: 1mm;
                border: 1px solid black;
                .header, .items, .totals {
                    div {
                        display: inline-block;
                    }
                }
                .header {
                    font-weight: bold;
                }
                .totals {
                    border-top: 1px solid black;
                }
                .percentage {
                    width: 30mm;
                }
                .base {
                    width: 23mm;
                    text-align: right;
                }
                .vat {
                    width: 23mm;
                    text-align: right;
                }
                .price {
                    width: 23mm;
                    text-align: right;
                }
            }
            .stamp {
                display: inline-block;
                margin-left: 110mm;
                margin-top: 3mm;
                width: 34mm;
            }
        }
        .footer {
            position: absolute;
            left: 0;
            bottom: 0;
            padding: 4mm;
            div {
                display: inline-block;
                margin-right: 15mm;
                &.info {
                    margin-right: 0;
                }
            }
        }
    }
}


// MODULE RESPONSIVE STYLES
@import "_responsive.less";