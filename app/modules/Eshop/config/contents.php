<?php
/**
 * Module contents initialization. Each content is defined by pair '{pid}' => {arrayOfWebContentFields}. 
 * There are two special fields: 'parent' and 'blocks'. See here below how to use them.
 * An example:
 * 
 *      array(
 *          '{pid}' => array( // pid is used to identify existing contents in DB and so avoid creation on duplicities
 *              'parent' => '{parentPid}', // use 'root' for toplevel folders,  required
                'lang' => '{lang1},{lang2},...', // comma separated list of languages to create the contents for
 *              'name' => '{contentName}', // if not defined then defaults to {pid}
 *              'locator' => '{contentSlug}', // if not defined then only content category is created without real content
 *              'text' => '{contentText}', // this is created as html content block
 *              'active' => false, // if not defined then defaults to TRUE
 *              'permanent' => true, // if not defined then defaults to FALSE
 *              'blocks' => array(
 *                  array(
 *                      'content_block_model' => 'ContentBlock.ContentBlockHtml', // required
 *                      'name' => '{contentBlockName}', // if not provided then default content block name is used
 *                      'active' => true, // if not defined then defaults to TRUE
 *                      'content_block_data' => array(
 *                          'html' => '<h1>My sample content</h1> This is just a sample content',
 *                      ),
 *                  ),
 *              ),
 *          ),
 *          '{pid2}' => array(...),
 *          ...,
 *      )
 * 
 * Use '/mvc/App/Tools/updateContents' to load contents into DB
 * 
 * Contents are created for all active langs from specified (in 'lang'). 
 * If no langs are specified then content is created for all active languages.
 * Missing lang roots are created too.
 */ 
$contents = array(
    //
    // ESHOP
    //
    'eshop' => array(
        'parent' => 'root',
        'name' => 'E-shop - systémové stránky', 
        'permanent' => 1,
    ),
    // required pages
    'Eshop.termsAndConditions' => array(
        'parent' => 'eshop',
        'name' => 'Obchodné podmienky', 
        'locator' => 'obchodne-podmienky', 
        'text' => '<object _snippet="e.App.setting" _snippet_generic="1" _snippet_name="Nastavenia: Obchodné podmienky" module="Eshop" pid="EshopOrder.termsAndConditions"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopProducts.indexCategory' => array(
        'parent' => 'eshop',
        'name' => 'Produkty kategórie', 
        'locator' => 'kategoria', 
        'text' => '<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" filter="category" set_seo="1"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopProducts.indexSearch' => array(
        'parent' => 'eshop',
        'name' => 'Výsledky vyhľadávania', 
        'locator' => 'eshop-vyhladavanie', 
        'text' => '<object _snippet="Eshop.EshopProducts.indexSearch" _snippet_generic="1" paginate="1"></object>',
        'permanent' => 1,
    ),    
    'Eshop.EshopProducts.view' => array(
        'parent' => 'eshop',
        'name' => 'Detail produktu', 
        'locator' => 'produkt', 
        'text' => '<object _snippet="Eshop.EshopProducts.view" _snippet_generic="1" set_seo="1"></object>',
        'has_side_content' => 0,
        'permanent' => 1,
    ),
    'Eshop.EshopCarts.view' => array(
        'parent' => 'eshop',
        'name' => 'Nákupný košík', 
        'locator' => 'kosik', 
        'text' => '<object _snippet="Eshop.EshopCarts.view" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopOrders.login' => array(
        'parent' => 'eshop',
        'name' => 'Prihlásenie v objednávkovom procese', 
        'locator' => 'objednavka-prihlasenie', 
        'text' => '<object _snippet="Eshop.EshopOrders.login" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopOrders.checkout' => array(
        'parent' => 'eshop',
        'name' => 'Objednávka', 
        'locator' => 'objednavka', 
        'text' => '<object _snippet="Eshop.EshopOrders.checkout" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopOrders.add' => array(
        'parent' => 'eshop',
        'name' => 'Odoslanie objednávky', 
        'locator' => 'odoslanie-objednavky', 
        'text' => '<h1>Nová objednávka</h1>Vaša objednávka č.<object _snippet="Eshop.EshopOrders.add" _snippet_generic="1" _snippet_name="Číslo objednávky"></object>&nbsp;bola úspešne prijatá.<br /><object _snippet="Eshop.EshopOrders.getNewOrderPaymentLink" _snippet_generic="1" _snippet_name="Platobny link"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopOrders.pay' => array(
        'parent' => 'eshop',
        'name' => 'Platba objednávky', 
        'locator' => 'platba', 
        'text' => '<object _snippet="Eshop.EshopOrders.pay" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopOrders.processPaymentResponse' => array(
        'parent' => 'eshop',
        'name' => 'Výsledok platby objednávky', 
        'locator' => 'vysledok-platby', 
        'text' => '<object _snippet="Eshop.EshopOrders.processPaymentResponse" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopOrders.index' => array(
        'parent' => 'eshop',
        'name' => 'Moje objednávky', 
        'locator' => 'moje-objednavky', 
        'text' => '<object _snippet="Eshop.EshopUsers.profileMenu" _snippet_generic="1" _snippet_name="Navigacia v profile"></object><object _snippet="Eshop.EshopOrders.index" _snippet_generic="1" _snippet_name="Zoznam objednavok uzivatela"></object>',
        'permanent' => 1,
    ),
    // aux indexes 
    'Eshop.EshopProducts.indexAuthor' => array(
        'parent' => 'eshop',
        'name' => 'Produkty autora', 
        'locator' => 'autor', 
        'text' => '<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" filter="author" set_seo="1"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopProducts.indexManufacturer' => array(
        'parent' => 'eshop',
        'name' => 'Produkty výrobcu', 
        'locator' => 'vyrobca', 
        'text' => '<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" filter="manufacturer" set_seo="1"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopProducts.indexManufacturerRange' => array(
        'parent' => 'eshop',
        'name' => 'Produkty edície', 
        'locator' => 'edicia', 
        'text' => '<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" filter="range" set_seo="1"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopProducts.indexBrand' => array(
        'parent' => 'eshop',
        'name' => 'Značka', 
        'url' => 'znacka', 
        'text' => '
            <object _snippet="Eshop.EshopProducts.breadcrumbs" _snippet_generic="1"></object>
            <section id="product-filter-wrapper">
                <object _snippet="Eshop.EshopProducts.filter" _snippet_generic="1"></object>
            </section>
            <section class="right" id="Products">
                <div class="wrapper">
                    <object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" filter="brand" set_seo="1"></object>
                </div>
            </section>',
    ),
    'Eshop.EshopProducts.indexGroup' => array(
        'parent' => 'eshop',
        'name' => 'Produkty skupiny', 
        'locator' => 'skupina', 
        'text' => '<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" filter="group" set_seo="1"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopProducts.indexTopTen' => array(
        'parent' => 'eshop',
        'name' => '10 najpredávanejších', 
        'locator' => 'top-10', 
        'layout' => 'App.simple',
        'text' => '<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" filter_group="top-produkty" limit="12"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopProducts.indexDiscounted' => array(
        'parent' => 'eshop',
        'name' => 'Akciové produkty', 
        'locator' => 'akciove-produkty', 
        'text' => '<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" discount="1" paginate="1"></object>',
        'permanent' => 1,
    ),
    'Eshop.EshopProducts.indexGiftCard' => array(
        'parent' => 'eshop',
        'name' => 'Darovacie karty', 
        'locator' => 'darovacie-karty', 
        'permanent' => 1,
        'blocks' => array(
            array(
                'content_block_model' => 'ContentBlock.ContentBlockHtml',
                'name' => 'Úvodny text', 
                'active' => true,
                'content_block_data' => array(
                    'html' => '<br /><br />
<h1>Darovacie karty</h1>
<br />
Chcete obdarovať knihou niekoho zo svojích blízkych, no neviete presne, ktorá by ho skutočne potešila? Kúpte mu niektorú z našich darovacích kariet a výber nechajte na neho.<br />
<br />
<br />',
                ),
            ),
            array(
                'content_block_model' => 'ContentBlock.ContentBlockHtml',
                'name' => 'Zoznam darovacích kariet',
                'active' => true,
                'content_block_data' => array(
                    'html' => '<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" gift-card-products="only" show-filter-select="0" show-sort-select="0" show-index-type-select="0"></object>',
                ),
            ),
            array(
                'content_block_model' => 'ContentBlock.ContentBlockHtml',
                'name' => 'Info o darovacích kartách', 
                'active' => true,
                'content_block_data' => array(
                    'html' => 'Darovacie karty fungujú nasledovne... <br /><br /><br />',
                ),
            ),
        ),
    ),
    
    // wishlist
    'Eshop.EshopWishlists.indexProducts' => array(
        'parent' => 'eshop',
        'name' => 'Produkty wishlistu užívateľa', 
        'locator' => 'moje-wishlisty', 
        'text' => '<object _snippet="Eshop.EshopWishlists.manage" _snippet_generic="1"></object><object _snippet="Eshop.EshopWishlists.indexProducts" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),
    
    'Eshop.EshopWishlists.view' => array(
        'parent' => 'eshop',
        'name' => 'Produkty wishlistu verejne', 
        'locator' => 'wishlist', 
        'text' => '<object _snippet="Eshop.EshopWishlists.view" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),
    
    // special offers
    'Eshop.EshopSpecialOffers.view' => array(
        'parent' => 'eshop',
        'name' => 'Info o špeciálnej ponuke', 
        'locator' => 'specialna-ponuka', 
        'text' => '<object _snippet="Eshop.EshopSpecialOffers.view" _snippet_generic="1"></object>',
        'permanent' => 1,
        'has_side_content' => 1,
    ),
    
    // bonus points
    'Eshop.bonusPointsInfo' => array(
        'parent' => 'eshop',
        'name' => 'Info o bónusových bodoch', 
        'locator' => 'bonus', 
        'text' => '<object _snippet="App.UserProfiles.bonusPointsSummary" _snippet_generic="1" _snippet_name="Info o stave bonusu"></object><br><br>Info o fungovaní bónusových bodov...',
        'permanent' => 1,
        'has_side_content' => 0,
    ),
    
    // vouchers
    'Eshop.EshopVouchers.verify' => array(
        'parent' => 'eshop',
        'name' => 'Overenie zľavového kódu', 
        'locator' => 'overenie-zlavoveho-kodu', 
        'permanent' => 1,
        'blocks' => array(
            array(
                'content_block_model' => 'ContentBlock.ContentBlockHtml',
                'name' => 'Úvodny text', 
                'active' => true,
                'content_block_data' => array(
                    'html' => '<br /><br />
<h1>Overenie zľavového kódu</h1>
<br />
Na tejto stránke si môžete overiť platnosť zľavového kódu alebo kódu darovacej karty.<br />
<br />
<br />',
                ),
            ),
            array(
                'content_block_model' => 'ContentBlock.ContentBlockHtml',
                'name' => 'Formulár overenia',
                'active' => true,
                'content_block_data' => array(
                    'html' => '<object _snippet="Eshop.EshopVouchers.verify" _snippet_generic="1"></object>',
                ),
            ),
            array(
                'content_block_model' => 'ContentBlock.ContentBlockHtml',
                'name' => 'Pätičkový text', 
                'active' => true,
                'content_block_data' => array(
                    'html' => 'Tip: Pozrite si ponuku naších <a href="/darovacie-karty">darovacích kariet</a><br><br>',
                ),
            ),
        ),
    ),    
);
