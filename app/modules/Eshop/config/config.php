<?php 
/**
 * Module configs
 * 
 * To read these values use App::getConfig() function, e.g.:
 *
 *      App::getConfig('{module}'); // will return whole module $config array
 *      App::getConfig('{module}', 'fbApiId'); // will return value of module $config['fbApiId']
 *      App::getConfig('{module}', 'google.analytics'); // will return value of module $config['google']['analytics']
 * 
 * @var array
 */
$config = array(
    'adminIcons' => array(
        'Module' => '<i class="fa fa-shopping-cart"></i>',
        'EshopProducts' => '<i class="fa fa-tag"></i>',
        'EshopOrders' => '<i class="fa fa-gift"></i>',
//        'EshopOrders' => '<i class="fa fa-cart-plus"></i>',
        'EshopProductCategories' => '<i class="fa fa-sitemap"></i>',
        'EshopProductTypes' => '<i class="fa fa-th"></i>',
//        'EshopProductTypes' => '<i class="fa fa-th-list"></i>',
//        'EshopManufacturers' => '<i class="fa fa-wrench"></i>',
        'EshopManufacturers' => '<i class="fa fa-industry"></i>',
//        'EshopManufacturerRanges' => '<i class="fa fa-puzzle-piece"></i>',
//        'EshopManufacturerRanges' => '<i class="fa fa-th-large"></i>',
//        'EshopManufacturerRanges' => '<i class="fa fa-stack-overflow"></i>',
//        'EshopManufacturerRanges' => '<i class="fa fa-sticky-note"></i>',
        'EshopManufacturerRanges' => '<i class="fa fa-cube"></i>',
        'EshopAuthors' => '<i class="fa fa-user-secret"></i>',
//        'EshopAuthors' => '<i class="fa fa-user"></i>',
//        'EshopAuthors' => '<i class="fa fa-pencil"></i>',
//        'EshopAuthors' => '<i class="fa fa-copyright"></i>',
        'EshopProductGroups' => '<i class="fa fa-tags"></i>',
        'EshopProductComments' => '<i class="fa fa-comments"></i>',
        'EshopShipmentMethods' => '<i class="fa fa-truck"></i>',
        'EshopVouchers' => '<i class="fa fa-percent"></i>',
        'EshopSpecialOffers' => '<i class="fa fa-smile-o"></i>',
        'EshopExports' => '<i class="fa fa-download"></i>',
        'EshopImports' => '<i class="fa fa-upload"></i>',
        'EshopSettings' => '<i class="fa fa-gears"></i>',
    ),   
); 
