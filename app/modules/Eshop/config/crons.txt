//
// GENERAL CRONS
//
// See https://en.wikipedia.org/wiki/Cron , https://crontab.guru/  
//  
// Minute  Hour  MonthDay  Month  WeekDay  Address

*/10    3-23,0-1   *   *   *   /mvc/Eshop/EshopImports/autoimportProductFieldsFromSource/image  
*/10    3-23,0-1   *   *   *   /mvc/Eshop/EshopImports/autoimportProductFieldsFromSource/description
00  6   *   *   *   /mvc/Eshop/EshopImports/autoimportProductFieldsFromSource/image/0/1  
15  6   *   *   *   /mvc/Eshop/EshopImports/autoimportProductFieldsFromSource/description/0/1

00  0,2-23  *   *   *  /mvc/Eshop/EshopExports/exportPriceEnginesXml/all
// 15  *   *   *   *   /mvc/Eshop/EshopOrders/updateTatrabankaPaymentsStatuses //if there is any TB payment method used on site
 
// 05   0   *   *   *   /mvc/Eshop/EshopProducts/actualizeAvailability   
25   3   *   *   *  /mvc/Eshop/EshopOrderProducts/updateBestsellerRelevancy

// 10   0   *   *   *   /mvc/Eshop/EshopShipmentMethods/loadPickupPlaces/geisPoint   
// 30  3   *   *   *   /mvc/Eshop/EshopShipmentMethods/loadPickupPlaces/zasielkovna   
  
//
// PROJECT SPECIFIC CRONS
//
// See https://en.wikipedia.org/wiki/Cron , https://crontab.guru/  
//  
// Minute  Hour  MonthDay  Month  WeekDay  Address

//0	*/2	*	*	*	/mvc/Eshop/EshopImports/autoimportProductsAddition/frankanaAddition
//10	*/2	*	*	*	/mvc/Eshop/EshopImports/autoimportProductsUpdate/frankanaStockUpdate
//30	*/2	*	*	*	/mvc/Eshop/EshopImports/autoimportProductsUpdate/frankanaUpdate

//20  */2    *   *   *   *	/mvc/Eshop/EshopProducts/insertRelatedProducts
//*/10    *   *   *   *	/mvc/Eshop/EshopProducts/translateGermanTextsToSlovak

*	*   *   *   *	/mvc/Eshop/EshopImports/autoimportProductsUpdate/mrpStockUpdate
// 00  4   *   *   *   /mvc/Eshop/EshopImports/autoimportProductsUpdate/mrpStockUpdate/0/1
*   *   *   *   *   /mvc/Eshop/EshopOrderProducts/updateReservedAmount
40 */3   *   *   *   /mvc/Eshop/EshopExports/autoexportMrpOrders/1/1

00  07  *   *   *    /mvc/Eshop/EshopWishlists/checkWatchdog

//15  07   *   *   *    /mvc/Eshop/EshopAuthors/unify

*    *   *   *   *   /mvc/Eshop/EshopOrderProducts/createGiftCardVouchers
*/10 *   *   *   *   /mvc/Eshop/EshopOrderProducts/sendExpiringGiftCardVoucherEmails


//00 1-23   *   *   *   /mvc/Eshop/EshopProductMeiliSearches/synchronize?modifiedAfter=-1&supplierProductsModifiedAfter=-1
//00 00   *   *   *   /mvc/Eshop/EshopProductMeiliSearches/synchronize
