<?php
// product availabilities enum
__(__FILE__, 'enum_presale');
__(__FILE__, 'enum_available');
__(__FILE__, 'enum_on_demand');
__(__FILE__, 'enum_soldout');

// product tax rates enum
__(__FILE__, '0');
__(__FILE__, '10');
__(__FILE__, '20');

// product units enum
__(__FILE__, 'enum_piece');
__(__FILE__, 'enum_meter');
__(__FILE__, 'enum_kilogram');

// product additional services enum
__(__FILE__, 'enum_gift_package');

// order statuses
__(__FILE__, 'enum_new_order');
__(__FILE__, 'enum_opened_order');
__(__FILE__, 'enum_suspended_order');
__(__FILE__, 'enum_pickup_order');
__(__FILE__, 'enum_shipped_order');
__(__FILE__, 'enum_closed_order');
__(__FILE__, 'enum_canceled_order');

// order payment statuses
__(__FILE__, 'enum_payment_none');
__(__FILE__, 'enum_payment_advance_paid');
__(__FILE__, 'enum_payment_partially_paid');
__(__FILE__, 'enum_payment_paid');
__(__FILE__, 'enum_payment_tout');
__(__FILE__, 'enum_payment_failed');
__(__FILE__, 'enum_payment_manipulated');

// wishlist privacy levels
__(__FILE__, 'enum_wishlist_private');
__(__FILE__, 'enum_wishlist_shared');
__(__FILE__, 'enum_wishlist_public');

// product group type
__(__FILE__, 'sale');

// special offer cart_banner_type
__(__FILE__, 'image_with_text');
__(__FILE__, 'discounted_products_index');
__(__FILE__, 'discounted_products_slider');

// special offer apply_by
__(__FILE__, 'promoted_products_in_cart');
__(__FILE__, 'cart_price_threshold');
__(__FILE__, 'promoted_products_cart_price_threshold');
