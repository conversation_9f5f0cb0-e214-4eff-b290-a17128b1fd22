<?php
/**
 * Module settings initialization. Use following array structure:
 * 
 *      array(
 *          '{pid}' => array(
 *              'value' => '{settingValue}', 
 *              'label' => '{settingLabel}', 
 *              'description' => '{settingDescription}', 
 *              'js_visible' => '{settingJsVisible}',
 *          )
 *      )
 * 
 * Use '/mvc/App/Tools/updateSettings/{Module}' to load settings into DB
 */ 
$settings = array(
    'email.from' => array(
        'value' => '<EMAIL>',
        'label' => 'Hlavný email eshopu',
        'description' => 'Používa sa ako adresa odosielatela e-mailov objednávok a pre komunikáciu s klientom, napr. <EMAIL>',
        'js_visible' => 0,
    ),
    'email.cc' => array(
        'value' => '<EMAIL>',
        'label' => 'Príjemca v kópii',
        'description' => 'Adresa príjemcu kópie e-mailu objednávok. Ak nie je zadaná, tak sa e-mail objednávky odošle len klientovi',
        'js_visible' => 0,
    ),
    'email.bcc' =>  array(
        'value' => '',
        'label' => 'Príjemca v skrytej kópii',
        'description' => 'Adresa príjemcu kópie e-mailu objednávok. Ak nie je zadaná, tak sa e-mail objednávky odošle len klientovi',
        'js_visible' => 0,
    ),
    'pricesAreTaxed' => array(
        'value' => '1', 
        'label' => 'Ceny zadávané s DPH', 
        'description' => '1 - ceny v eshope sú zadávané s DPH. 0 - ceny sú zadávané bez DPH',
        'js_visible' => 0,
    ),
    'defaultTaxRate' => array(
        'value' => '20', 
        'label' => 'Základná DPH', 
        'description' => 'Základná hodnota DPH použitá vo všetkých prípadoch keď sa nepoužíva znížená hodnota DPH',
        'js_visible' => 0,
    ),
    'address.companyFullname' => array(
        'value' => '', 
        'label' => 'Adresa - názov spoločnosti', 
        'description' => 'Obchodný názov spoločnosti použiteľný za učelom uvedenia v adrese e-shopu'
    ),
    'address.fullname' => array(
        'value' => '', 
        'label' => 'Adresa - osoby alebo miesto', 
        'description' => 'Meno osoby alebo názov miesta použiteľný za učelom uvedenia v adrese e-shopu'
    ),
    'address.street' => array(
        'value' => '', 
        'label' => 'Adresa - ulica a číslo domu e-shopu', 
        'description' => 'Ulica a číslo domu e-shopu'
    ),
    'address.city' => array(
        'value' => '', 
        'label' => 'Adresa - mesto e-shopu', 
        'description' => 'Mesto e-shopu'
    ),
    'address.zip' => array(
        'value' => '', 
        'label' => 'Adresa - PSČ e-shopu', 
        'description' => 'PSČ e-shopu'
    ),
    'address.country' => array(
        'value' => 'SK', 
        'label' => 'Adresa - krajina e-shopu', 
        'description' => 'Kód krajiny (ISO code 2) e-shopu. Toto nastavenie sa napr. vyhodnocuje ak nastavenie EshopShipment.specificAbroadDelivery = 1'
    ),
    'EshopProduct.pagingLimit' => array(
        'value' => '9', 
        'label' => 'Limit stránkovania produktov', 
        'description' => 'Počet položiek na jednej stránke pri stránkovaní produktov.'
    ),
    'EshopProduct.indexCategoryOwnProducts' => array(
        'value' => '0', 
        'label' => 'Zobrazuj len vlastné produkty kategórie', 
        'description' => 'Ak 1 tak sa v zozname produktov kategórie zobrazia sa len produkty umiestnené priamo vo filtrovanej kategórii. Ak 0 tak sa zobrazujú aj produkty všetkých podkategórií.'
    ),
    'EshopProduct.shipmentTimeOffStock' => array(
        'value' => '2', 
        'label' => 'Doba expedovania produktu mimo skladu', 
        'description' => 'Ako dlho trvá vyexpedovanie produktu, ktorý nie je na sklade. Zadávať v dňoch.',
        'js_visible' => 0,
    ),
    'EshopProduct.shipmentTimeOnStock' => array(
        'value' => '1', 
        'label' => 'Doba vyexpedovania produktu na sklade', 
        'description' => 'Zadávať v dňoch',
        'js_visible' => 0,
    ),
    'EshopProduct.orderableToStockAmount' => array(
        'value' => '0', 
    ),
    'EshopProduct.weightUnits' => array(
        'value' => 'kg', 
        'label' => 'Jednotky hmotnosti produktu', 
        'description' => 'Jednotky v ktorých sa zadáva hmotnosť produktu',
        'js_visible' => 0,
    ),
    'EshopProduct.dimensionsUnits' => array(
        'value' => 'mm', 
        'label' => 'Jednotky rozmerov produktu', 
        'description' => 'Jednotky v ktorých sa zadávajú rozmery produktu',
        'js_visible' => 0,
    ),
    'EshopProduct.discountPriceDecimals' => array(
        'value' => 2, 
        'label' => 'Desatinné miesta zľavnenej ceny', 
        'description' => 'Počet desatinných miest zľavnenej ceny. Ak sú ceny zadávané s DPH, tak sa uplatní len na zľavnené ceny s DPH. Ak sú ceny zadávané bez DPH, tak sa uplatní len na zľavnené ceny bez DPH. Pri zaokruhľovaní sa vždy zaokrúhľuje smerom nadol (aby klient dostal sľúbenú zľavu)',
        'js_visible' => 0,
    ),
    'EshopRelatedProduct.reciprocal' => array(
        'value' => '1', 
        'label' => 'Recipročné príbuzné produkty', 
        'description' => 'Príbuzné produkty vytvárať recipročne',
        'js_visible' => 0,
    ),
    'EshopOrder.allowQuickOrder' => array(
        'value' => '1', 
        'label' => 'Rýchla objednávka', 
        'description' => 'Ak je "1" tak objednávku možno vykonať aj bez prihlásenia, len na základe poskytnutia požadovaných informácií - tzv. rychla objednávka. Ak je "force" tak rýchla objednávka je jediný spôsob objednávky. Aj je "0" tak k objednávke môže pristúpiť len prihlásený uživateľ.',
        'js_visible' => 0,
    ),
    'EshopOrder.termsAndConditionsFile' => array(
        'value' => 'vseobecne_obchodne_podmienky.pdf', 
        'label' => 'Názov súboru všeobecných obchodných podmienok', 
        'description' => "Názov pdf súboru všeobecných obchodných podmienok pripojeného k emailu novej objednávky",
        'js_visible' => 0,
    ),
    'EshopOrder.termsAndConditions' => array(
        'value' => 'Obchodné podmienky', 
        'label' => 'Všeobecné obchodné podmienky', 
        'description' => "Text všeobecných obchodných podmienok slúžiaci na vygenerovanie pdf prikladaného k mailu novej objednávky",
        'js_visible' => 0,
    ),
    'EshopOrder.contractWithdrawalFile' => array(
        'value' => 'odstupenie_od_zmluvy.pdf', 
        'label' => 'Názov súboru s formularom odstúpenia od zmluvy', 
        'description' => "Názov pdf súboru s formularom odstúpenia od zmluvy pripojeného k emailu novej objednávky",
        'js_visible' => 0,
    ),
    'EshopOrder.contractWithdrawal' => array(
        'value' => '<div style="border-bottom:3px solid #000;font-size:1.8em;font-family:sans-serif;"><b>Formulár odstúpenia od zmluvy</b></div>
<i>(vyplňte a zašlite tento formulár len v prípade, že si želáte odstúpiť od zmluvy)</i>
<br>
<br>
<br>
<b>Komu:</b> 
<br>
<br>
<table>
    <tr>
        <td style="border:1px solid #000;padding:1em;">
Test &amp; Test, s.r.o.<br>
Test 1, 036 01 Martin, Slovenská republika<br>
IČO: 12 456 789, IČ DPH: SK2345678910<br>
+421 987 654 321, <EMAIL>, www.test.sk<br>
        </td>
    </tr>
</table>
<br>
<br>
<table style="width:100%;table-layout:fixed">
    <tbody><tr>
        <td style="width:8cm;padding-top:2em"><b>Týmto oznamujem, že odstupujem:</b></td>
        <td></td>
    </tr>
    <tr>
        <td style="padding-top:1em">- od zmluvy na tento tovar<sup>*</sup>:</td>
        <td style="border-bottom:1px dotted #000;"></td>
    </tr>
    <tr>
        <td style="padding-top:1em">- od zmluvy na poskytnutí tejto služby<sup>*</sup>:</td>
        <td style="border-bottom:1px dotted #000;"></td>
    </tr>
    <tr>
        <td style="padding-top:2em"><b>Dátum objednania:</b></td>
        <td style="border-bottom:1px dotted #000;"></td>
    </tr>
    <tr>
        <td style="padding-top:2em"><b>Dátum prijatia:</b></td>
        <td style="border-bottom:1px dotted #000;"></td>
    </tr>
    <tr>
        <td style="padding-top:2em"><b>Meno a priezvisko spotrebiteľa:</b></td>
        <td style="border-bottom:1px dotted #000;"></td>
    </tr>
    <tr>
        <td style="padding-top:2em"><b>Adresa spotrebiteľa:</b></td>
        <td style="border-bottom:1px dotted #000;"></td>
    </tr>
    <tr>
        <td style="padding-top:2em"><b>Podpis spotrebiteľa:</b></td>
        <td style="border-bottom:1px dotted #000;"></td>
    </tr>
    <tr>
        <td colspan="2" style="padding-top:0.5em"><i>(iba ak sa tento formulár podáva v listinnej podobe)</i></td>
    </tr>
    <tr>
        <td style="padding-top:2em"><b>Dátum:</b></td>
        <td style="border-bottom:1px dotted #000;"></td>
    </tr>
</tbody></table>
<br>
<br>
<br>
<br>
<br>
<i><sup>*</sup> Nehodiace sa prečiarknite</i>', 
        'label' => 'Formular odstúpenia od zmluvy', 
        'description' => "Text formuláru na odstúpenie od zmluvy",
        'js_visible' => 0,
    ),
    'EshopOrder.msgBodyNewOrder' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
ďakujeme Vám za nákup v našom internetovom obchode. Teší nás, že ste si vybrali práve nás a urobíme všetko pre to, aby sme Vás nesklamali.<br />
<br />
Tento e-mail je potvrdením, že sme Vašu objednávku prijali a začíname ju spracovávať. Hneď ako bude vybavená a bude pre Vás pripravená / odoslaná, pošleme Vám ďalší e-mail.<br />
<br />
Napriek našej snahe doručovať všetok objednaný tovar čo najskôr, vybavovanie Vašej objednávky sa môže predĺžiť podľa aktuálnej dostupnosti u našich dodávateľov. O vyexpedovaní Vašej objednávky Vás budeme informovať e-mailom aj SMS správou, ak ste nám poskytli svoje telefónne číslo. V prípade problémov s dostupnosťou titulov alebo prekročenia dodacej lehoty Vás budeme kontaktovať. Aktuálny stav vybavovania Vašej objednávky si možete kedykoľvek pozrieť aj cez Váš účet na našej web stránke.<br />
<br />
--------------------<br />
<br />
<strong> Čislo Vašej objednávky: :orderNumber:</strong><br />
(Ak nám pri akejkoľvek otázke ohľadom Vašej objednávky uvediete aj toto číslo, uľahčí nám to prácu.)<br />
Dátum a čas prijatia objednávky: :orderCreated:<b>:specificInfo:</b><br />
<br />
<strong> Objednaný tovar:</strong><br />
:orderProducts:<br />
<br />
Poštovné: :shipmentCostInfo:<br />
Spôsob doručenia: :shipmentMethod:<br />
Spôsob platby: :paymentMethod:<br />
<strong> Celková suma: :orderTotalInfo:</strong><br />
<br />
<strong> Adresa na doručenie:</strong><br />
:deliveryAddress:<br />
<br />
<strong>Platba za objednávku</strong><br />
:paymentLink:<br />
<br />
Ak sa rozhodnete platbu uskutočníť prevodným príkazom, uveďte nasledovné údaje:<br />
Bankový účet: :bankAccount:<br />
Variabilný symbol: :variableSymbol:<br />
Konštantný symbol: :constantSymbol:<br />
<br />
Ďakujeme Vám za dôveru a tešíme sa na Vašu ďalšiu návštevu.<br />
<br />
Prajeme Vám príjemný deň.<br />
Váše :eshopName:',
        'label' => 'Nová objednávka - text emailu',
        'description' => 'Text emailu zaslaného zákazníkovi pri vytvorení novej objednávky.',
        'js_visible' => 0,
    ),
    'EshopOrder.msgSubjectNewOrder' => array(
        'value' => 'Potvrdenie prijatia Vašej objednávky',
        'label' => 'Nová objednávka - predmet správy',
        'description' => 'Predmet emailu zaslaného zákazníkovi pri vytvorení novej objednávky',
        'js_visible' => 0,
    ),
    'EshopOrder.msgBodyNewSpecificOrder' => array(
        'value' => '',
        'label' => 'Nová špeciálna objednávka - text emailu',
        'description' => 'Text emailu zaslaného zákazníkovi pri vytvorení novej špeciálnej objednávky, keď je napr. potrebné upresniť cenu poštovného do zahraničia. Ak nie je zadaný tak sa pošle východzí.',
        'js_visible' => 0,
    ),
    'EshopOrder.msgSubjectNewSpecificOrder' => array(
        'value' => '',
        'label' => 'Nová špeciálna objednávka - predmet správy',
        'description' => 'Predmet emailu zaslaného zákazníkovi pri vytvorení novej špeciálnej objednávky, keď je napr. potrebné upresniť cenu poštovného do zahraničia.. Ak nie je zadaný tak sa pošle východzí.',
        'js_visible' => 0,
    ),
    'EshopShipment.specificAbroadDelivery' => array(
        'value' => '1', 
        'label' => 'Osobitné doručenie pre zahraničné objednávky', 
        'description' => 'Nastaviť 1 ak spôsob doručenia do zahraničia a poštovné má zadávať operátor ku každej objednávke zvlášť. 0 ak su prístupné tie isté metódy doručenia aj pre zahraničie.'
    ),
    'EshopOrder.smartsmsMessageA' => array(
        'value' => '',
        'label' => 'SmartSMS Správa pre klienta (osobný odber na predajni)',
        'description' => 'SMS správa, ktorá sa odošle klientovi s objednávkou určenou na osobný odber. Odoslanie prostredníctvom rozhrania na odosielania SMS správ - smartsms.sk',
        'js_visible' => 0,
    ),
    'EshopOrder.smartsmsMessageB' => array(
        'value' => 'Dobry den, Vasa objednavka bola dnes expedovana. Tovar ocakavajte v priebehu 1-3 dni. Viac info najdete v emaili. Drinkcentrum.sk',
        'label' => 'SmartSMS Správa pre klienta (iný ako osobný odber na predajni)',
        'description' => 'SMS správa, ktorá sa odošle klientovi s objednávkou, ktorá nie je určená na osobný odber(napr. kuriér). Odoslanie prostredníctvom rozhrania na odosielania SMS správ - smartsms.sk',
        'js_visible' => 0,
    ),
    'EshopOrder.bankAccount' => array(
        'label' => 'Bankový účet',
        'value' => '999 999 9999/9999',
        'description' => 'Spravidla desaťmiestne číslo účtu nasledované lomkou a štvormiestnym kódom banky. Toto číslo sa zobrazuje ako informácia pre zákazníkov pre platby bankvým prevodom.',
    ),
    'EshopOrder.bankTransferConstantSymbol' => array(
        'label' => 'Konštantný symbol',
        'value' => '0008',
        'description' => 'Štvormiestny číselný kód konštatného symbolu pre platby bankovým prevodom.',
    ),
    'EshopOrder.msgSubjectPaymentSuccessful' => array(
        'value' => 'Objednávka č. :orderNumber: - Vaša platba bola prijatá',
        'label' => 'Predmet - platba prijatá',
        'description' => 'Predmet správy zaslanej zákazníkovi po obdržaní platby',
    ),
    'EshopOrder.msgSubjectPaymentFailed' => array(
        'value' => 'Objednávka č. :orderNumber: - Vaša platba bola zamietnutá',
        'label' => 'Predmet - platba zamietnutá',
        'description' => 'Predmet správy zaslanej zákazníkovi po obdržaní platby',
    ),
    'EshopOrder.msgBodyPaymentSuccessful' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
Vaša platba za objednávku č. :orderNumber: bola úspešne spracovaná.<br />
<br />
Vašu objednávku by sme mali vyexpedovať približne do 30 pracovných dní. Napriek našej snahe doručovať všetok objednaný tovar čo najskôr, vybavovanie Vašej objednávky sa môže predĺžiť podľa aktuálnej dostupnosti u našich dodávateľov. O vyexpedovaní Vašej objednávky Vás budeme informovať e-mailom aj SMS správou, ak ste nám poskytli svoje telefónne číslo. V prípade problémov s dostupnosťou titulov alebo prekročenia dodacej lehoty Vás budeme kontaktovať. Aktuálny stav vybavovania Vašej objednávky si možete kedykoľvek pozrieť aj cez na našej web stránke.<br />
<br />
Ďakujeme Vám za dôveru a tešíme sa na Vašu ďalšiu návštevu. Prajeme Vám príjemný deň.<br />
<br />
Váše :eshopName:',
        'label' => 'Správa - platba prijatá',
        'description' => 'Obsah správy zasielanej zákazníkovi pri prijatí platby',
    ),

    'EshopOrder.msgBodyPaymentFailed' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
Vaša platba za objednávku č. :orderNumber: bola <strong>zamietnutá</strong> Vašou bankou. Prosím opakujte platbu za túto objednávku pomocou tlačidla "<strong>Zaplatiť</strong>", ktoré nájdete po prihlásení v prehľade tejto objednávky na adrese: :linkOrder:<br />
<br />
Prajeme Vám príjemný deň.<br />
<br />
Váše :eshopName:',
        'label' => 'Správa - platba odmietnutá',
        'description' => 'Text správy zasielanej zákazníkovi pri odmietnutí platby',
    ),
    
    /* EMAIL STATUSES */
        
    /* enum_opened_order */
    'EshopOrder.msgSubjectStatusChange.enum_opened_order' => array(
        'value' => 'Objednávka č. :orderNumber: - Zmena stavu Vašej objednávky', 
        'label' => 'Zmena stavu objednávky - predmet', 
        'description' => 'Predmet emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),        
    'EshopOrder.msgBodyStatusChange.enum_opened_order' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
radi by sme Vás informovali, že stav Vašej objednávky č. <strong>:orderNumber:</strong> (prijatá :orderCreated:) sa zmenil na <strong>:orderStatus:</strong>.<br />
<br />
<br />
Prajeme Vám príjemný deň.', 
        'label' => 'Zmena stavu objednávky - správa', 
        'description' => 'Text emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),
    
    /* enum_shipped_order */
    'EshopOrder.msgSubjectStatusChange.enum_shipped_order' => array(
        'value' => 'Objednávka č. :orderNumber: - Zmena stavu Vašej objednávky', 
        'label' => 'Zmena stavu objednávky - predmet', 
        'description' => 'Predmet emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),        
    'EshopOrder.msgBodyStatusChange.enum_shipped_order' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
radi by sme Vás informovali, že stav Vašej objednávky č. <strong>:orderNumber:</strong> (prijatá :orderCreated:) sa zmenil na <strong>:orderStatus:</strong>.<br />
<br />
<br />
Prajeme Vám príjemný deň.', 
        'label' => 'Zmena stavu objednávky - správa', 
        'description' => 'Text emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),
    
    /* enum_closed_order */
    'EshopOrder.msgSubjectStatusChange.enum_closed_order' => array(
        'value' => 'Objednávka č. :orderNumber: - Zmena stavu Vašej objednávky', 
        'label' => 'Zmena stavu objednávky - predmet', 
        'description' => 'Predmet emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),        
    'EshopOrder.msgBodyStatusChange.enum_closed_order' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
radi by sme Vás informovali, že stav Vašej objednávky č. <strong>:orderNumber:</strong> (prijatá :orderCreated:) sa zmenil na <strong>:orderStatus:</strong>.<br />
<br />
<br />
Prajeme Vám príjemný deň.', 
        'label' => 'Zmena stavu objednávky - správa', 
        'description' => 'Text emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),
    
    /* enum_suspended_order */
    'EshopOrder.msgSubjectStatusChange.enum_suspended_order' => array(
        'value' => 'Objednávka č. :orderNumber: - Zmena stavu Vašej objednávky', 
        'label' => 'Zmena stavu objednávky - predmet', 
        'description' => 'Predmet emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),        
    'EshopOrder.msgBodyStatusChange.enum_suspended_order' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
radi by sme Vás informovali, že stav Vašej objednávky č. <strong>:orderNumber:</strong> (prijatá :orderCreated:) sa zmenil na <strong>:orderStatus:</strong>.<br />
<br />
<br />
Prajeme Vám príjemný deň.', 
        'label' => 'Zmena stavu objednávky - správa', 
        'description' => 'Text emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),
    
    /* enum_pickup_order */
    'EshopOrder.msgSubjectStatusChange.enum_pickup_order' => array(
        'value' => 'Objednávka č. :orderNumber: - Zmena stavu Vašej objednávky', 
        'label' => 'Zmena stavu objednávky - predmet', 
        'description' => 'Predmet emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),        
    'EshopOrder.msgBodyStatusChange.enum_pickup_order' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
radi by sme Vás informovali, že Vaša objednávka č. <strong>:orderNumber:</strong> (prijatá :orderCreated:) je pre Vás prichystaná na vyzdvihnutie.<br />
<br />
<br />
Prajeme Vám príjemný deň.', 
        'label' => 'Zmena stavu objednávky - správa', 
        'description' => 'Text emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),
    
    /* enum_canceled_order */
    'EshopOrder.msgSubjectStatusChange.enum_canceled_order' => array(
        'value' => 'Objednávka č. :orderNumber: - Zmena stavu Vašej objednávky', 
        'label' => 'Zmena stavu objednávky - predmet', 
        'description' => 'Predmet emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),        
    'EshopOrder.msgBodyStatusChange.enum_canceled_order' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
radi by sme Vás informovali, že stav Vašej objednávky č. <strong>:orderNumber:</strong> (prijatá :orderCreated:) sa zmenil na <strong>:orderStatus:</strong>.<br />
<br />
<br />
Prajeme Vám príjemný deň.', 
        'label' => 'Zmena stavu objednávky - správa', 
        'description' => 'Text emailu zaslaného zákazníkovi pri zmene stavu objednávky.'
    ),
    
    /* EMAIL STATUSES END */
    
    'EshopOrder.msgBodyPaymentInProcessing' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
Vaša platba za objednávku č. :orderNumber: sa spracováva.<br />
O výsledku budete informovaný emailom.
<br />
Aktuálny stav vybavovania Vašej objednávky si možete kedykoľvek pozrieť aj cez na našej web stránke (:eshopUrl:)<br />
<br />
Ďakujeme Vám za dôveru a tešíme sa na Vašu ďalšiu návštevu. Prajeme Vám príjemný deň.<br />
<br />
Tím :eshopName:', 
        'label' => 'Platba sa spracováva - správa', 
        'description' => 'Text emailu zaslaného zákazníkovi pri prijatí nepotvrdenej platby.'
    ),
    'EshopOrder.msgSubjectPaymentInProcessing' => array(
        'value' => 'Objednávka č. :orderNumber: - Vaša platba sa spracováva', 
        'label' => 'Platba sa spracováva - predmet', 
        'description' => 'Predmet emailu zaslaného zákazníkovi pri prijatí nepotvrdenej platby.'
    ),
    'EshopOrder.openingHours' => array(
        'value' => '<p>Otváracie hodiny skladu<br />
Po - Pia: 8:00 - 16:00<br />
Sobota: 8:00 - 12:00<br />
Nedeľa - zatvorené</p>', 
        'label' => 'Otváracie hodiny', 
        'description' => 'Popis otváracej doby skladu'
    ),
    'EshopShipment.freeShipmentProductsTotal' => array(
        'value' => '99', 
        'label' => 'Doprava zadarmo pri objednávke nad', 
        'description' => 'Cena objednávky v Euro pri ktorej je doprava zadarmo',
        'js_visible' => 0,
    ),
    'EshopOrder.advanceRates' => array(
        'value' => '300:60', 
        'label' => 'Percenta zálohy', 
        'description' => 'Zoznam percent zálohových platieb: {totalObjednávky1}:{percentoZalohy1},{totalObjednávky2}:{percentoZalohy2},...',
        'js_visible' => 0,
    ),
    'EshopOrder.heurekaConversionKey' => array(
        'value' => '', 
        'label' => 'Kľúč pre meranie konverzií z Heuréky', 
        'description' => 'Tajný kľúč na meranie konverzií z Heuréky. Ak sa zadá tak pri každej novej objednávke sa  pošle do Heuréky info, aby mohli urobiť štatistiky konverzií (https://sluzby.heureka.sk/obchody/mereni-konverzi/)',
        'js_visible' => 0,
    ),
    'EshopOrder.heurekaVerifiedByClientsKey' => array(
        'value' => '', 
        'label' => 'Kľúč pre Heuréka Overené zákazníkmi', 
        'description' => 'Tajný kľúč na sprevázdkovanie služby Heuréka Overené zákazníkmi. Ak sa zadá tak o každej novej objednávke sa  pošle do Heuréky info, aby mohli urobiť prieskum  spokojnosti zákazníkov',
        'js_visible' => 0,
    ),
    'EshopOrder.heurekaVerifiedByClientsScript' => array(
        'value' => '', 
        'label' => 'Skript pre Heuréka Overené zákazníkmi', 
        'description' => 'Skript na zobrazenie odznaku Heuréka Overené zákazníkmi. Po obdržaní odznaku skopírujte skript z heureka admina presne tak ako je tam uvedený.',
        'js_visible' => 0,
    ),
    'EshopWishlist.msgBodyShareWishlist' => array(
        'value' => 'Dobrý deň,<br />
<br />
Používateľ :userName: Vám chce ukázať svoj wishlist :wishlistName: v internetovom obchode :eshopName:. Nájdete ho na nasledovnej adrese:<br />
<br />
:wishlistUrl:<br />
<br />
Prajeme Vám príjemný deň.<br />
<br />
Váše :eshopName:', 
        'label' => 'Zdieľanie wishlistu - správa', 
        'description' => 'Text emailu na zdieľanie wishlistu.',
        'js_visible' => 0,
    ),
    'EshopWishlist.msgSubjectShareWishlist' => array(
        'value' => 'Wishlist :userName: na :eshopName:', 
        'label' => 'Zdieľanie wishlistu - predmet', 
        'description' => 'Predmet emailu na zdieľanie wishlistu.',
        'js_visible' => 0,
    ),        
    'EshopWishlist.msgBodyWishlistWatchdog' => array(
        'value' => 'Dobrý deň,<br />
<br />
Položka <a href=":productUrl:">:productName:</a> z Vášho wishlistu <a href=":wishlistUrl:">:wishlistName:</a> bola v stave vypredaná a opäť je možné ju objednať.<br />
<br />
Prajeme Vám príjemný deň.<br />
<br />
Váše :eshopName:', 
        'label' => 'Watchdog wishlistu - správa', 
        'description' => 'Text emailu upozorňujúceho, že nedostupná položka wishlistu je už dostupná.',
        'js_visible' => 0,
    ),
    'EshopWishlist.msgSubjectWishlistWatchdog' => array(
        'value' => 'Položka wishlistu :productName: je dostupná', 
        'label' => 'Watchdog wishlistu - predmet', 
        'description' => 'Predmet emailu upozorňujúceho, že nedostupná položka wishlistu je už dostupná.',
        'js_visible' => 0,
    ),       
    'EshopProduct.allowExternalSearch' => array(
        'value' => '0', 
    ),
    'EshopProduct.additionalDiscountRateLimit' => array(
        'value' => '28.00',
        'label' => 'Hraničné percento zľavy',
        'description' => 'Percento výslednej zľavy po uplatnení dodatočných zliav je ohraničené touto hodnotou. Ak je však primárna zľava produktu výššia ako toto percento tak sa ako hraničná hodnota použije percento primárnej zľavy. Toto nastavenie plati všetkých výrobcov iných ako Vydavateľ. Pre produkty Vydavateľa platí nastavenie EshopProduct.additionalDiscountRateLimitForVydavatel',
    ),
    'EshopProduct.additionalDiscountRateLimitForVydavatel' => array(
        'value' => '50.00',
        'label' => 'Hraničné percento zľavy pre vydavateľa',
        'description' => 'Percento výslednej zľavy po uplatnení dodatočných zliav je ohraničené touto hodnotou. Ak je však primárna zľava produktu výššia ako toto percento tak sa ako hraničná hodnota použije percento primárnej zľavy. Toto nastavenie platí len pre produkty Vydavateľa',
    ),
    'EshopOrder.msgBodyNewComment' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />'
        . '<br />'
        . 'zasielame Vám novú informáciu k Vašej objednávke č. :orderNumber::<br />'
        . '<br />'
        . '<i>:commentText:</i><br />'
        . '<br />'
        . 'Prajeme Vám príjemný deň.<br />'
        . 'Váše :eshopName: (:commentAuthor:)', 
        'label' => 'Text emailu pre Nový komentár', 
        'description' => 'Text emailu zaslaného zákazníkovi pri vytvorení nového komentáru k objednávke.'
    ),
    'EshopOrder.msgSubjectNewComment' => array(
        'value' => 'Informácia k objednávke č. :orderNumber:', 
        'label' => 'Predmet emailu Nový komentár', 
        'description' => 'Predmet emailu zaslaného zákazníkovi pri vytvorení nového komentáru k objednávke'
    ),
    // Zasielkovňa API key used to load pickup places
    'EshopShipmentMethod.zasielkovnaApiKey' => array(
        'value' => '', 
    ),
    // Json encoded array of Zasielkovna pickup places
    'EshopShipmentMethod.zasielkovnaPickupPlaces' => array(
        'value' => '', 
    ),
    // Json encoded array of GEIS POINT pickup places
    'EshopShipmentMethod.geisPointPickupPlaces' => array(
        'value' => '', 
    ),
    // MRP server IP for automatic imports/exports
    'mrp.serverIpAddress' => array(
        'value' => '', 
    ),
    // MRP server port for automatic imports/exports
    'mrp.serverPort' => array(
        'value' => '', 
    ),
    // MRP server private key for encryption of automatic imports/exports
    'mrp.privateKey' => array(
        'value' => '', 
    ),
    // MRP last code
    'mrp.lastCode' => array(
        'value' => null
    ),
    // Minimal markup (sk: marža) percents of imported products. 
    // See phpDoc of Eshop::getDiscountRate()
    'EshopProduct.importedProductsMinMarkupRate' => array(
        'value' => '6', 
    ),
    // sum of points for which the EshopOrder.bonusDiscount can be applied
    // @see implementationDetails > Bonusy
    'EshopOrder.applicableBonusPoints' => array(
        'value' => '160',
    ),
    // discount (absolute in €) to be substracted from order products price
    // once the UserProfile.bonus_points reaches the EshopOrder.applicableBonusPoints
    // @see implementationDetails > Bonusy
    'EshopOrder.bonusDiscount' => array(
        'value' => '10',
    ),
    'EshopSupplierProduct.ignoredPublishers' => array(
        'value' => '', 
    ),
    // max amount of products for which is possible 
    // to set EshopProductCategory.sort_products to TRUE
    'EshopProductCategory.sortProductsLimit' => array(
        'value' => 0,
    ),
    // id of voucher product - it serves for implementation of vouchers with absolute
    // discount. Price of this product is set when added to cart, so it can be any value
    'EshopProduct.voucherProductId' => array(
        'value' => '',
    ),
    // used to send electronic gift card voucher to user
    // (in general this can be used even for printed if needed - depends
    // just on formulation of the email body)
    'EshopOrderProduct.msgSubjectGiftCardVoucher' => array(
        'value' => 'Objednávka č. :orderNumber: - darovacia karta :giftCardCode:',
    ),
    'EshopOrderProduct.msgBodyGiftCardVoucher' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
posielame Vám kód darovacej karty v hodnote :giftCardDiscount:, ktorú ste si u nás zakúpili (objednávka č. :orderNumber:):<br />
<br />
<b style="font-size:2em">:giftCardCode:</b><br />
<br />
Tento kód zadajte v objednávkovom procese na našom webe do poľa <i>Zľavový kód</i>.
Platnosť Vašej darovacej karty končí :giftCardActiveTo:.<br />
Viac o podmiekach použitia darovacích kariet si môžete <a href="/darovacie-karty">prečítať tu</a>.<br />
<br />
Ďakujeme Vám za dôveru a tešíme sa na Vašu ďalšiu návštevu. Prajeme Vám príjemný deň.<br />
<br />
Váše :eshopName:',
    ),
    'EshopOrderProduct.giftCardExpirationDays' => array(
        'value' => '366',
    ),
    'EshopOrderProduct.giftCardExpiringEmailDays' => array(
        'value' => '31',
    ),
    // This setting is used internally (not exposed to user)
    'EshopOrderProduct.sendExpiringGiftCardVoucherEmails.lastBatchTimestamp' => array(
        'value' => 0,
    ),
    'EshopOrderProduct.msgSubjectExpiringGiftCardVoucher' => array(
        'value' => 'Platnosť darovacej karty :giftCardCode: čoskoro končí',
    ),
    'EshopOrderProduct.msgBodyExpiringGiftCardVoucher' => array(
        'value' => 'Dobrý deň :userFirstName:,<br />
<br />
radi by sme Vás upozornili, že platnosť darovacej karty <b>:giftCardCode:</b>, ktorú ste si u nás zakúpili, končí :giftCardActiveTo:.<br />
Po tomto termíne bude karta neplatná a jej hodnota :giftCardDiscount: prepadne.<br />
Úprimne veríme, že sa tak nestane a určite si niečo vyberiete z našej pestrej ponuky knižných titulov.<br />
V takom prípade kód darovacej karty zadajte v objednávkovom procese na našom webe do poľa <i>Zľavový kód</i>.<br />
Viac o podmiekach použitia darovacích kariet si môžete <a href="/darovacie-karty">prečítať tu</a>.<br />
<br />
Ďakujeme Vám za dôveru a tešíme sa na Vašu návštevu. Prajeme Vám príjemný deň.<br />
<br />
Váše :eshopName:',
    ),
    // used in view of EshopVouchers::admin_print()
    'EshopVouchers.admin_print.template' => array(
        'value' => App::loadView('Eshop', 'EshopVouchers/admin_print'),
    ),
    'EshopOrder.oversizedProductMessage' => array(
        'value' => 'Cena za dopravu pri tomto tovare bude vzhľadom na nadštandardný rozmer/váhu vypočítaná individuálne. Cena Vám bude oznámená na odsúhlasenie nasledujúci pracovný deň.'
    ),
    'frankana.apiUrlBase' => array(
        'value' => 'https://ff.data.bloodstream.cloud/',
    ),
    'frankana.apiKey' => array(
        'value' => '9f34e353-ec28-42ac-b475-129f2e81090b',
    ),
);
