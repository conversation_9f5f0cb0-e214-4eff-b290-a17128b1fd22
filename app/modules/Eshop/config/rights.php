<?php
/**
 * Module rights definitions
 * 
 * Auxiliary variable $edit serves to optimize performance. Use it in admin rights 
 * to define headers and labels in case when rights are loaded for editing. Only items
 * with defined labels are included in editing. Use pattern like:
 * 
 *      'admins' => array(
 *          array('h1' => $edit ? __a(__FILE__, 'My module rights') : true),
 *          array('h2' => $edit ? __a(__FILE__, 'My section rights') : true),
 *          'MyModel.admin_myAction1' => $edit ? __a(__FILE__, 'Do my action') : true,
 *          'MyModel.admin_myAction3' => true,                                                       // this will not be included in editing
 *          'MyModel.admin_myAction2' => $edit ? __a(__FILE__, 'Not important for admin') : false,      // editable but admin does not need to see it in backend
 *          ...,
 * 
 *      );
 * 
 * Admin rights must contain all actions which should be editable. If you have a case of 
 * method which is a weak version of stronger one, and admn does not need to use this method
 * and see it on backend then just set it to FALSE (see the last item in example above).
 * In special cases you can define FALSE items also in rights for other groups, just test it
 * if default behaviour does not meet your needs.
 * 
 * When loading rights for editing set $edit = TRUE. Otherwise just dont care about it.
 * 
 * !!!ATTENTION: Do not define headers and labels in rights for other than admins group!!!
 */
$edit = !empty($edit);
$rights = array(
    'public' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            
            'EshopProducts.index' => true,
            'EshopProducts.indexShowcase' => true,
            'EshopProducts.indexSlider' => true,
            'EshopProducts.indexTop10' => true,
            'EshopProducts.indexRecommended' => true,
            'EshopProducts.view' => true,
            'EshopProducts.breadcrumbs' => true,
            'EshopProducts.getBannerCode' => true,
            'EshopProducts.miniSearch' => true,
            'EshopProducts.indexSearch' => true,
            'EshopProducts.actualizeAvailability' => true,
            'EshopProducts.exportPriceEnginesXml' => true, //@deprecated
            'EshopProducts.indexWithVue' => true,
            'EshopProducts.findDetailed' => true,
            'EshopProducts.photogallery' => true,
            'EshopProducts.filter' => true,
            'EshopProducts.insertRelatedProducts' => true,
            'EshopProducts.translateGermanTextsToSlovak' => true,
            
            'EshopCarts.addProducts' => true,
            'EshopCarts.addChildProduct' => true,
            'EshopCarts.removeProduct' => true,
            'EshopCarts.updateProducts' => true,
            'EshopCarts.clearProducts' => true,
            'EshopCarts.view' => true,
            'EshopCarts.viewMini' => true,
            'EshopCarts.setVoucher' => true,
            'EshopCarts.clearVoucher' => true,
            
            'EshopProductCategories.menu' => true,
            'EshopProductCategories.index' => true,
            
            'EshopManufacturers.menu' => true,
            'EshopManufacturers.indexSlider' => true,
            
            'EshopWishlists.view' => true,
            'EshopWishlists.addProductAvailabilityAlert' => true,
            'EshopWishlists.checkWatchdog' => true, // launched by cronjob
            
            'EshopExports.exportPriceEnginesXml' => true,
            'EshopExports.autoexportMrpOrders' => true,
            
            'EshopImports.autoimportProductsAddition' => true,
            'EshopImports.autoimportProductsUpdate' => true,
            'EshopImports.autosynchronizeSupplierProducts' => true,
            'EshopImports.autoimportProductFieldsFromSource' => true,
            
            'EshopShipmentMethods.loadPickupPlaces' => true, // launched by cronjob
                        
            'EshopSpecialOffers.view' => true,
            
            'EshopAuthors.unify' => true,
            
            // these methods must be public in any case (not only because of quick orders)
            'EshopOrders.getNewOrderPaymentLink' => true,
            'EshopOrders.getNewOrderEmail' => true,
            'EshopOrders.login' => true,
            'EshopOrders.pay' => true,
            'EshopOrders.processPaymentResponse' => true,
            'EshopOrders.updateTatrabankaPaymentsStatuses' => true,
            'EshopOrders.setCheckoutVoucher' => true,
            'EshopOrders.clearCheckoutVoucher' => true,
            
            'EshopOrderProducts.updateReservedAmount' => true,
            'EshopOrderProducts.createGiftCardVouchers' => true,
            'EshopOrderProducts.sendExpiringGiftCardVoucherEmails' => true,
            
            'EshopOrderProducts.updateBestsellerRelevancy' => true,
            
            'EshopVouchers.verify' => true,
            
            'EshopProductMeiliSearches.synchronize' => true,
            
            // RUNTIME decided rights
            'EshopOrders.checkout' => true,
            'EshopOrders.add' => true,
            'EshopUsers.menu' => true,
        ),
        // these rights apply when the element is required by snippet
        'element' => array(
            'termsAndConditions' => true,
            'address' => true,
        ),
    ),
    'admins' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            array('h1' => $edit ? __a(__FILE__, 'Eshop rights') : true),
            array('h2' => $edit ? __a(__FILE__, 'Products rights') : true),
            'EshopProducts.admin_index' => $edit ? __a(__FILE__, 'Display products list') : true,
            'EshopProducts.admin_export' => $edit ? __a(__FILE__, 'Export products') : true,
            'EshopProducts.admin_load' => true,
            'EshopProducts.admin_list' => true,
            'EshopProducts.admin_add' => $edit ? __a(__FILE__, 'Add product') : true,
            'EshopProducts.admin_copy' => $edit ? __a(__FILE__, 'Copy product') : true,
            'EshopProducts.admin_edit' => $edit ? __a(__FILE__, 'Edit product') : true,
            'EshopProducts.admin_editMany' => $edit ? __a(__FILE__, 'Bulk edit of products') : true,
            'EshopProducts.admin_delete' => $edit ? __a(__FILE__, 'Delete product') : true,
            'EshopProducts.admin_deleteFile' => true,
            'EshopProducts.admin_getSelectorInterface' => true,
            'EshopProducts.admin_getAutocompleteList' => true,
            'EshopProducts.admin_setSoldoutAvailability' => true,
            
            'EshopProducts.admin_indexComments' => true,
            'EshopProducts.admin_editComment' => true,
            'EshopProducts.admin_deleteComment' => true,
            
            'EshopManufacturers.admin_index' => true,
            'EshopManufacturers.admin_export' => true,
            'EshopManufacturers.admin_add' => true,
            'EshopManufacturers.admin_edit' => true,
            'EshopManufacturers.admin_delete' => true,
            'EshopManufacturers.admin_deleteFile' => true,
            'EshopManufacturers.admin_getSelectorItems' => true,

            'EshopBrands.admin_index' => true,
            'EshopBrands.admin_export' => true,
            'EshopBrands.admin_add' => true,
            'EshopBrands.admin_edit' => true,
            'EshopBrands.admin_delete' => true,
            'EshopBrands.admin_deleteFile' => true,

            'EshopManufacturerRanges.admin_index' => true,
            'EshopManufacturerRanges.admin_export' => true,
            'EshopManufacturerRanges.admin_add' => true,
            'EshopManufacturerRanges.admin_edit' => true,
            'EshopManufacturerRanges.admin_delete' => true,
            
            'EshopProductTypes.admin_index' => true,
            'EshopProductTypes.admin_add' => true,
            'EshopProductTypes.admin_edit' => true,
            'EshopProductTypes.admin_delete' => true,
            'EshopProductTypes.admin_deleteFile' => true,
            'EshopProductTypes.admin_getSelectorItems' => true,
            
            'EshopAuthors.admin_index' => true,
            'EshopAuthors.admin_export' => true,
            'EshopAuthors.admin_add' => true,
            'EshopAuthors.admin_edit' => true,
            'EshopAuthors.admin_delete' => true,
            'EshopAuthors.admin_getSelectorItems' => true,
            
            'EshopOrders.admin_index' => true,
            'EshopOrders.admin_printDetail' => true,
            'EshopOrders.admin_edit' => true,
            'EshopOrders.admin_ship' => true,
            'EshopOrders.admin_delete' => true,
            'EshopOrders.admin_generatePohodaXmlOrder' => true, //@deprecated
            'EshopOrders.admin_exportGeisOrderCsv' => true,
            'EshopOrders.admin_checkTatrabankaPaymentStatus' => true,
            'EshopOrders.admin_chargeBackCardpayPayment' => true,
            'EshopOrders.index' => true,
            
            'EshopOrderProducts.admin_indexHtmlTable' => true,
            
            'EshopProductCategories.admin_index' => true,
            'EshopProductCategories.admin_showTree' => true,
            'EshopProductCategories.admin_add' => true,
            'EshopProductCategories.admin_edit' => true,
            'EshopProductCategories.admin_move' => true,
            'EshopProductCategories.admin_delete' => true,
            'EshopProductCategories.admin_deleteFile' => true,
            'EshopProductCategories.admin_list' => true,
            'EshopProductCategories.admin_listForItemSelector' => true,
            'EshopProductCategories.admin_translate' => true,
            
            'EshopShipmentMethods.admin_index' => true,
            'EshopShipmentMethods.admin_add' => true,
            'EshopShipmentMethods.admin_edit' => true,
            'EshopShipmentMethods.admin_delete' => true,
            'EshopShipmentMethods.admin_move' => true,
            
            'EshopProductGroups.admin_index' => true,
            'EshopProductGroups.admin_add' => true,
            'EshopProductGroups.admin_edit' => true,
            'EshopProductGroups.admin_move' => true,
            'EshopProductGroups.admin_delete' => true,
            'EshopProductGroups.admin_getSelectorItems' => true,
            
            'EshopProductImages.admin_load' => true,
            'EshopProductImages.admin_add' => true,
            'EshopProductImages.admin_update' => true,
            'EshopProductImages.admin_move' => true,
            'EshopProductImages.admin_delete' => true,
            
            'EshopSettings.admin_edit' => $edit ? __a(__FILE__, 'Edit settings') : true,
            
            'EshopVouchers.admin_index' => true,
            'EshopVouchers.admin_add' => true,
            'EshopVouchers.admin_edit' => true,
            'EshopVouchers.admin_delete' => true,
            'EshopVouchers.admin_importFromXls' => true,
            'EshopVouchers.admin_print' => true,

            'EshopUsers.profileMenu' => true,
            
            'EshopWishlists.manage' => true,
            'EshopWishlists.add' => true,
            'EshopWishlists.edit' => true,
            'EshopWishlists.delete' => true,
            'EshopWishlists.send' => true,
            'EshopWishlists.addProduct' => true,
            'EshopWishlists.indexProducts' => true,
            'EshopWishlists.deleteProduct' => true,
            'EshopWishlists.updateProduct' => true,
            
            'EshopExports.admin_exportPohodaXmlOrder' => true,
            'EshopExports.admin_exportMrpProducts' => true,
            'EshopExports.admin_exportMrpProductManufacturers' => true,
            'EshopExports.admin_exportMrpProductImages' => true,
            'EshopExports.admin_exportMrpOrders' => true,
            'EshopExports.admin_exportMrpOrdersXml' => true,
            'EshopExports.admin_downloadMrpFile' => true,
            
            'EshopImports.admin_importProductsAddition' => true,
            'EshopImports.admin_importProductsUpdate' => true,
            'EshopImports.admin_downloadImportFile' => true,
            'EshopImports.admin_importProductsPemicDescriptionUpdate' => true,
            
            'EshopSpecialOffers.admin_index' => true,
            'EshopSpecialOffers.admin_add' => true,
            'EshopSpecialOffers.admin_edit' => true,
            'EshopSpecialOffers.admin_delete' => true,            
            'EshopSpecialOffers.admin_deleteFile' => true,            
        ),
        // these rights apply when the element is required by snippet
        'element' => array(
        ),
        // these rights apply when settings are listed, viewed and edited
        'setting' => array(
        ),
    ),
    'webmasters' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            'EshopProducts.admin_index' => true,
            'EshopProducts.admin_export' => true,
            'EshopProducts.admin_load' => true,
            'EshopProducts.admin_list' => true,
            'EshopProducts.admin_listAvailabilities' => true,
            'EshopProducts.admin_listTaxRates' => true,
            'EshopProducts.admin_add' => true,
            'EshopProducts.admin_copy' => true,
            'EshopProducts.admin_edit' => true,
            'EshopProducts.admin_editMany' => true,
            'EshopProducts.admin_delete' => true,
            'EshopProducts.admin_deleteFile' => true,
            'EshopProducts.admin_getSelectorInterface' => true,
            'EshopProducts.admin_getAutocompleteList' => true,
            'EshopProducts.admin_setSoldoutAvailability' => true,
            
            'EshopProducts.admin_indexComments' => true,
            'EshopProducts.admin_editComment' => true,
            'EshopProducts.admin_deleteComment' => true,
            
            'EshopManufacturers.admin_index' => true,
            'EshopManufacturers.admin_export' => true,
            'EshopManufacturers.admin_add' => true,
            'EshopManufacturers.admin_edit' => true,
            'EshopManufacturers.admin_delete' => true,
            'EshopManufacturers.admin_deleteFile' => true,
            'EshopManufacturers.admin_getSelectorItems' => true,
            
            'EshopBrands.admin_index' => true,
            'EshopBrands.admin_export' => true,
            'EshopBrands.admin_add' => true,
            'EshopBrands.admin_edit' => true,
            'EshopBrands.admin_delete' => true,
            'EshopBrands.admin_deleteFile' => true,
            
            'EshopManufacturerRanges.admin_index' => true,
            'EshopManufacturerRanges.admin_export' => true,
            'EshopManufacturerRanges.admin_add' => true,
            'EshopManufacturerRanges.admin_edit' => true,
            'EshopManufacturerRanges.admin_delete' => true,
            
            'EshopProductTypes.admin_index' => true,
            'EshopProductTypes.admin_add' => true,
            'EshopProductTypes.admin_edit' => true,
            'EshopProductTypes.admin_delete' => true,
            'EshopProductTypes.admin_deleteFile' => true,
            'EshopProductTypes.admin_getSelectorItems' => true,

            'EshopAuthors.admin_index' => true,
            'EshopAuthors.admin_export' => true,
            'EshopAuthors.admin_add' => true,
            'EshopAuthors.admin_edit' => true,
            'EshopAuthors.admin_delete' => true,
            'EshopAuthors.admin_getSelectorItems' => true,
            
            'EshopOrders.admin_index' => true,
            'EshopOrders.admin_printDetail' => true,
            'EshopOrders.admin_edit' => true,
            'EshopOrders.admin_ship' => true,
            'EshopOrders.admin_delete' => true,
            'EshopOrders.admin_generatePohodaXmlOrder' => true, //@deprecated
            'EshopOrders.admin_exportGeisOrderCsv' => true,
            'EshopOrders.admin_checkTatrabankaPaymentStatus' => true,
            'EshopOrders.admin_chargeBackCardpayPayment' => true,
            'EshopOrders.index' => true,
            
            'EshopOrderProducts.admin_indexHtmlTable' => true,
            
            'EshopProductCategories.admin_index' => true,
            'EshopProductCategories.admin_showTree' => true,
            'EshopProductCategories.admin_add' => true,
            'EshopProductCategories.admin_edit' => true,
            'EshopProductCategories.admin_move' => true,
            'EshopProductCategories.admin_delete' => true,
            'EshopProductCategories.admin_deleteFile' => true,
            'EshopProductCategories.admin_list' => true,
            'EshopProductCategories.admin_listForItemSelector' => true,
            'EshopProductCategories.admin_translate' => true,
            
            'EshopShipmentMethods.admin_index' => true,
            'EshopShipmentMethods.admin_add' => true,
            'EshopShipmentMethods.admin_edit' => true,
            'EshopShipmentMethods.admin_delete' => true,
            'EshopShipmentMethods.admin_move' => true,
            
            'EshopProductGroups.admin_index' => true,
            'EshopProductGroups.admin_add' => true,
            'EshopProductGroups.admin_edit' => true,
            'EshopProductGroups.admin_move' => true,
            'EshopProductGroups.admin_delete' => true,
            'EshopProductGroups.admin_getSelectorItems' => true,
            
            'EshopProductImages.admin_load' => true,
            'EshopProductImages.admin_add' => true,
            'EshopProductImages.admin_update' => true,
            'EshopProductImages.admin_move' => true,
            'EshopProductImages.admin_delete' => true,
            
            'EshopSettings.admin_edit' => true,
            
            'EshopVouchers.admin_index' => true,
            'EshopVouchers.admin_add' => true,
            'EshopVouchers.admin_edit' => true,
            'EshopVouchers.admin_delete' => true,
            'EshopVouchers.admin_importFromXls' => true,
            'EshopVouchers.admin_print' => true,

            'EshopUsers.profileMenu' => true,
            
            'EshopWishlists.manage' => true,
            'EshopWishlists.add' => true,
            'EshopWishlists.edit' => true,
            'EshopWishlists.delete' => true,
            'EshopWishlists.send' => true,
            'EshopWishlists.addProduct' => true,
            'EshopWishlists.indexProducts' => true,
            'EshopWishlists.deleteProduct' => true,
            'EshopWishlists.updateProduct' => true,
            
            'EshopExports.admin_exportPohodaXmlOrder' => true,
            'EshopExports.admin_exportMrpProducts' => true,
            'EshopExports.admin_exportMrpProductManufacturers' => true,
            'EshopExports.admin_exportMrpProductImages' => true,
            'EshopExports.admin_exportMrpOrders' => true,
            'EshopExports.admin_exportMrpOrdersXml' => true,
            'EshopExports.admin_downloadMrpFile' => true,
            
            'EshopImports.admin_importProductsAddition' => true,
            'EshopImports.admin_importProductsUpdate' => true,
            'EshopImports.admin_downloadImportFile' => true,
            'EshopImports.admin_importProductsPemicDescriptionUpdate' => true,
            
            'EshopSpecialOffers.admin_index' => true,
            'EshopSpecialOffers.admin_add' => true,
            'EshopSpecialOffers.admin_edit' => true,
            'EshopSpecialOffers.admin_delete' => true,            
            'EshopSpecialOffers.admin_deleteFile' => true,            
        ),
        // these rights apply when the element is required by snippet
        'element' => array(
        ),
        // these rights apply when settings are listed, viewed and edited
        'setting' => array(
        ),
    ),
    'editors' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            'EshopProducts.admin_index' => true,
            'EshopProducts.admin_export' => true,
            'EshopProducts.admin_load' => true,
            'EshopProducts.admin_list' => true,
            'EshopProducts.admin_listAvailabilities' => true,
            'EshopProducts.admin_listTaxRates' => true,
//            'EshopProducts.admin_add' => true,
//            'EshopProducts.admin_copy' => true,
            'EshopProducts.admin_edit' => true,
            'EshopProducts.admin_editMany' => true,
            'EshopProducts.admin_delete' => true,
            'EshopProducts.admin_deleteFile' => true,
            'EshopProducts.admin_getSelectorInterface' => true,
            'EshopProducts.admin_getAutocompleteList' => true,
//            'EshopProducts.admin_setSoldoutAvailability' => true,
            
            'EshopProducts.admin_indexComments' => true,
            'EshopProducts.admin_editComment' => true,
            'EshopProducts.admin_deleteComment' => true,
            
            'EshopManufacturers.admin_index' => true,
//            'EshopManufacturers.admin_export' => true,
//            'EshopManufacturers.admin_add' => true,
//            'EshopManufacturers.admin_edit' => true,
//            'EshopManufacturers.admin_delete' => true,
//            'EshopManufacturers.admin_deleteFile' => true,
            'EshopManufacturers.admin_getSelectorItems' => true,
            
//            'EshopProductTypes.admin_index' => true,
//            'EshopProductTypes.admin_add' => true,
//            'EshopProductTypes.admin_edit' => true,
//            'EshopProductTypes.admin_delete' => true,
//            'EshopProductTypes.admin_deleteFile' => true,
            'EshopProductTypes.admin_getSelectorItems' => true,
            
            'EshopManufacturerRanges.admin_index' => true,
//            'EshopManufacturerRanges.admin_export' => true,
            'EshopManufacturerRanges.admin_add' => true,
            'EshopManufacturerRanges.admin_edit' => true,
//            'EshopManufacturerRanges.admin_delete' => true,
            
            'EshopAuthors.admin_index' => true,
//            'EshopAuthors.admin_export' => true,
            'EshopAuthors.admin_add' => true,
            'EshopAuthors.admin_edit' => true,
//            'EshopAuthors.admin_delete' => true,
            'EshopAuthors.admin_getSelectorItems' => true,
            
//            'EshopOrders.admin_index' => true,
//            'EshopOrders.admin_printDetail' => true,
//            'EshopOrders.admin_edit' => true,
//            'EshopOrders.admin_ship' => true,
//            'EshopOrders.admin_delete' => true,
//            'EshopOrders.admin_generatePohodaXmlOrder' => true, //@deprecated
//            'EshopOrders.admin_exportGeisOrderCsv' => true,
//            'EshopOrders.admin_checkTatrabankaPaymentStatus' => true,
//            'EshopOrders.admin_chargeBackCardpayPayment' => true,
//            'EshopOrders.index' => true,
            
            'EshopOrderProducts.admin_indexHtmlTable' => true,
            
            'EshopProductCategories.admin_index' => true,
//            'EshopProductCategories.admin_showTree' => true,
//            'EshopProductCategories.admin_add' => true,
//            'EshopProductCategories.admin_edit' => true,
//            'EshopProductCategories.admin_move' => true,
//            'EshopProductCategories.admin_delete' => true,
//            'EshopProductCategories.admin_deleteFile' => true,
            'EshopProductCategories.admin_list' => true,
            'EshopProductCategories.admin_listForItemSelector' => true,
//            'EshopProductCategories.admin_translate' => true,
            
//            'EshopShipmentMethods.admin_index' => true,
//            'EshopShipmentMethods.admin_add' => true,
//            'EshopShipmentMethods.admin_edit' => true,
//            'EshopShipmentMethods.admin_delete' => true,
//            'EshopShipmentMethods.admin_move' => true,
            
//            'EshopProductGroups.admin_index' => true,
//            'EshopProductGroups.admin_add' => true,
//            'EshopProductGroups.admin_edit' => true,
//            'EshopProductGroups.admin_move' => true,
//            'EshopProductGroups.admin_delete' => true,
            'EshopProductGroups.admin_getSelectorItems' => true,
            
            'EshopProductImages.admin_load' => true,
            'EshopProductImages.admin_add' => true,
            'EshopProductImages.admin_update' => true,
            'EshopProductImages.admin_move' => true,
            'EshopProductImages.admin_delete' => true,
            
//            'EshopSettings.admin_edit' => true,
            
//            'EshopVouchers.admin_index' => true,
//            'EshopVouchers.admin_add' => true,
//            'EshopVouchers.admin_edit' => true,
//            'EshopVouchers.admin_delete' => true,
//            'EshopVouchers.admin_importFromXls' => true,
//            'EshopVouchers.admin_print' => true,

            'EshopUsers.profileMenu' => true,
            
            'EshopWishlists.manage' => true,
            'EshopWishlists.add' => true,
            'EshopWishlists.edit' => true,
            'EshopWishlists.delete' => true,
            'EshopWishlists.send' => true,
            'EshopWishlists.addProduct' => true,
            'EshopWishlists.indexProducts' => true,
            'EshopWishlists.deleteProduct' => true,
            'EshopWishlists.updateProduct' => true,
            
//            'EshopExports.admin_exportPohodaXmlOrder' => true,
//            'EshopExports.admin_exportMrpProducts' => true,
//            'EshopExports.admin_exportMrpProductManufacturers' => true,
//            'EshopExports.admin_exportMrpProductImages' => true,
//            'EshopExports.admin_exportMrpOrders' => true,
//            'EshopExports.admin_exportMrpOrdersXml' => true,
//            'EshopExports.admin_downloadMrpFile' => true,
            
//            'EshopImports.admin_importProductsAddition' => true,
//            'EshopImports.admin_importProductsUpdate' => true,
//            'EshopImports.admin_downloadImportFile' => true,
//            'EshopImports.admin_importProductsPemicDescriptionUpdate' => true,
            
//            'EshopSpecialOffers.admin_index' => true,
//            'EshopSpecialOffers.admin_add' => true,
//            'EshopSpecialOffers.admin_edit' => true,
//            'EshopSpecialOffers.admin_delete' => true,            
//            'EshopSpecialOffers.admin_deleteFile' => true,            
        ),
        // these rights apply when the element is required by snippet
        'element' => array(
        ),
        // these rights apply when settings are listed, viewed and edited
        'setting' => array(
        ),
    ),
    'clients' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            'EshopOrders.index' => true,
            
            'EshopUsers.profileMenu' => true,
            
            'EshopWishlists.manage' => true,
            'EshopWishlists.add' => true,
            'EshopWishlists.edit' => true,
            'EshopWishlists.delete' => true,
            'EshopWishlists.send' => true,
            'EshopWishlists.addProduct' => true,
            'EshopWishlists.indexProducts' => true,
            'EshopWishlists.deleteProduct' => true,
            'EshopWishlists.updateProduct' => true,
        ),
        // these rights apply when the screen is required by slug request or by snippet
        'screen' => array(
        ),
        // these rights apply when the element is required by snippet
        'element' => array(            
        ),
        // these rights apply when settings are listed, viewed and edited
        'setting' => array(
        ),
    )
); 
