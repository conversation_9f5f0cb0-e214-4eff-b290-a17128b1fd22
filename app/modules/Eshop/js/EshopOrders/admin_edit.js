requireDefined([
    'jQuery',
    'jQuery.fn.autocomplete', // jQueryUI autocomplete
    'Run.App.Number',
    'Run.App.SubWindow',
    // after refactoring this should be options of EshopOrder.js class instance:
    'Run.App.config.freeShipmentProductsTotal',
    'Run.App.config.decimals',
    'Run.App.config.decimalPoint',
    'Run.App.config.thousandsSeparator',
    'Run.Eshop.config.specificShipment',
    'Run.Eshop.config.specificPayment',
    'Run.Eshop.config.shipmentPayment',
    'Run.Eshop.config.orderProductRowTemplate',
    'Run.Eshop.config.productViewSlug'
], 
'Run.App.App');

//
// Utility functions used in business logic functions here below
//

/**
 * Returns jQuery Object of specified input
 * 
 * @param {String|DOMElement|jQuery} selector
 * @param {Object} options
 * 
 * @returns {jQuery}
 */
function _getInput(selector, options) {
    var input = jQuery(selector);
    options = $.extend({
        allowNone: false,
        allowMany: false
    }, options || {});
    if (
        !options.allowNone 
        && input.length === 0
    ) {
        throw new Error(__jse('Eshop' ,'No input found for selector :selector:', {selector: selector}));
    }
    if (
        !options.allowMany 
        && (
            // in case disabled inputs there is a paired hidden input to ensure
            // data submition to server. Consider this case as a single input.
            input.length === 2
            && input.eq(0).attr('type') !== 'hidden'
            && input.eq(1).attr('type') !== 'hidden'
            ||
            input.length > 2
        )
    ) {
        throw new Error(__jse('Eshop' ,'Many inputs found for selector :selector:', {selector: selector}));
    }
    return input;
}
/**
 * Returns value of specified input
 * 
 * @param {String|DOMElement|jQuery} selector
 * @param {String} type Optional. Possible values 'numeric'
 * @returns {String|Number}
 */
function _getInputVal(selector, type) {
    //console.log('getVal'); //debug
    var input = _getInput(selector), value;
    value = input.val();
    if (type === 'numeric') {
        value = _parseNumber(value);
    }
    return value;
}
/**
 * Sets value of specified input
 * 
 * @param {String|DOMElement|jQuery} selector
 * @param {String|Number} value
 * @param {String} type Optional. Possible values 'numeric'
 */
function _setInputVal(selector, value, type) {
    //console.log('setVal'); //debug
    var input = _getInput(selector), 
        decimals = parseInt(Run.App.config.decimals),
        decimalPoint = Run.App.config.decimalPoint,
        thousandsSeparator = Run.App.config.thousandsSeparator;
    if (type === 'numeric') {
        value = Run.App.Number.format(value, decimals, decimalPoint, thousandsSeparator);
    }
    input.val(value);
}
/**
 * Sets options of specified selectbox to provided ones
 * 
 * @param {String|DOMElement|jQuery} selector
 * @param {Object} options Object of select options like: [[value1, label1], [value2, label2], ...]
 */
function _setSelectOptions(selector, options) {
    var input = _getInput(selector), optionsHtml = '', i;
    for (i = 0; i <  options.length; i++) {
        optionsHtml += '<option value=' + options[i][0] + '>' + options[i][1] + '</option>';
    }
    input.empty();
    input.append(optionsHtml);
}
/**
 * Converts provided numeric string (possiblly formated) to number
 * 
 * @param {Number|String} number
 * @returns {Number}
 */
function _parseNumber(number) {
    number = '' + number;
    number = number.replace(Run.App.config.decimalPoint, '.');
    number = number.replace(Run.App.config.thousandsSeparator, '');
    return parseFloat(number);
}
/**
 * 
 * @param {DOMElement|jQuery|String} selector Product input element, jQuery object or selector
 * @returns {Number}
 */
function _getProductInputRowIndex(selector) {
    var input = _getInput(selector), name = input.attr('name');
    if (!name.match(/data\[[^\]\d]+\]\[\d+\]/)) {
        throw new Error(__jse('Eshop', 'Input :selector: is not a product input', {selector: selector}));
    }
    return parseInt(name.split('][')[1]);
}
// set below in window.onready()
var _lastProductRowIndex;
/**
 * Returns jQuery Object of specified product input
 * 
 * @param {Number} rowIndex Row index to get produt input from
 * @param {String} field product field name, e.g. 'name', 'code' ,'amount' or 'run_eshop_products_id'
 * @returns {jQuery}
 */
function _getProductInput(rowIndex, field) {
    var selector = '[name="data[products][' + rowIndex + '][' + field + ']"]';
    return _getInput(selector);
}
/**
 * Returns jQuery Object of specified product inputs
 * 
 * @param {String} field product field name, e.g. 'name', 'code' ,'amount' or 'run_eshop_products_id'
 * @returns {jQuery}
 */
function _getProductInputs(field) {
    var selector = '[name^="data[products]["]', inputs;
    inputs = _getInput(selector, {allowMany: true});
    return inputs.filter('[name$="[' + field + ']"]');
}
/**
 * Gets value of specified product input
 * 
 * @param {Number} rowIndex Row index to get produt input from
 * @param {String} field product field name, e.g. 'name', 'code' ,'amount' or 'run_eshop_products_id'
 * @param {String} type Optional. Possible values 'numeric'
 */
function _getProductInputVal(rowIndex, field, type) {
    var selector = _getProductInput(rowIndex, field);
    return _getInputVal(selector, type);
}
/**
 * Sets value of specified product input
 * 
 * @param {Number} rowIndex Row index to get produt input from
 * @param {String} field product field name, e.g. 'name', 'code' ,'amount' or 'run_eshop_products_id'
 * @param {String|Number} value
 * @param {String} type Optional. Possible values 'numeric'
 */
function _setProductInputVal(rowIndex, field, value, type) {
    var selector = _getProductInput(rowIndex, field);
    _setInputVal(selector, value, type);
}
/**
 * Sets value of specified product input
 * 
 * @param {Number} rowIndex Row index to get produt input from
 * @param {String} field product field name, e.g. 'name', 'code' ,'amount' or 'run_eshop_products_id'
 * @param {Object} options Object of select options like: [[value1, label1], [value2, label2], ...]
 */
function _setProductSelectOptions(rowIndex, field, options) {
    var selector = _getProductInput(rowIndex, field);
    _setSelectOptions(selector, options);
}
/**
 * Returns section heading(s) of specified input
 * 
 * @param {String|DOMElement|jQuery} selector Input selector
 * @param {Object} options
 * @returns {String}
 */
function _getSectionHeading(selector, options) {
    var defaults, $element = _getInput(selector), $heading, heading = '';
    defaults = {
        topParentSelector: 'body',
        sectionHeadingSelector: 'h1,h2,h3',
        sectionHeadingsSeparator: ' > ',
        sectionHeadingsCount: 1
    };
    options = $.extend(defaults, options || {});
    if (!options.topParentSelector) {
        options.topParentSelector = defaults.topParentSelector;
    }
    if (!options.sectionHeadingSelector) {
        options.sectionHeadingSelector = defaults.sectionHeadingSelector;
    }
    if (!options.sectionHeadingsSeparator) {
        options.sectionHeadingsSeparator = defaults.sectionHeadingsSeparator;
    }
    options.sectionHeadingsCount = parseInt(options.sectionHeadingsCount);
    if (options.sectionHeadingsCount < 1) {
        options.sectionHeadingsCount = defaults.sectionHeadingsCount;
    }
    do {
        $heading = $element.prevAll().filter(options.sectionHeadingSelector);
        if ($heading.length) {
            $heading = $heading.eq(0);
            if (heading) {
                heading = options.sectionHeadingsSeparator + heading;
            }
            heading = $heading.text() + heading;
            options.sectionHeadingsCount--;
        }
        $element = $element.parent();
    } while (
        !$element.is(options.topParentSelector)
        && options.sectionHeadingsCount
    );
    return heading;
}
/**
 * Loads provided data to product inputs in specified row
 * 
 * @param {Number} rowIndex
 * @param {Object} data Product data object to be loaded e.g. {name: 'My name', run_eshop_products_id: 45, ...}
 */
function _loadProductData(rowIndex, data) {
    var field, type;
    // load response data to product inputs
    
    for (var k in data) {
        if (!data.hasOwnProperty(k)) {
            continue;
        }
        try {
            field = (k === 'id') ? 'run_eshop_products_id' : k;
            if (/^price(_|$)/.test(field)) {
                type = 'numeric';
                _setProductInputVal(rowIndex, field, data[k], type);
            }
            /*/
            else if (/^run_eshop_XXX_id$/.test(field)) {
                _setProductSelectOptions(rowIndex, field, data[k]);
            }
            /*/
            else {
                type = null;
                _setProductInputVal(rowIndex, field, data[k], type);
            }
        }
        catch(error) {
            // if no such product field then do silently nothing
        }
    }
    _recalculateProductTotal(rowIndex);
}
/**
 * Loads specified product to specified row
 * 
 * @param {Number} rowIndex
 * @param {Number} productId
 * @param {Function} completeCallback Callback function launched after product data are loaded.
 *      Success and data args are passed to this function.
 */
function _loadProduct(rowIndex, productId, completeCallback) {
    jQuery.ajax({
        url: '/mvc/Eshop/EshopProducts/admin_load/' + productId
    }).complete(function(res) {
        var response = jQuery.parseJSON(res.responseText);
        if (response.success === true) {
            _loadProductData(rowIndex, response.data);
            jQuery('#product-' + rowIndex + ' a.-run-action-view').attr('href', '/'  + Run.Eshop.config.productViewSlug + '/' + response.data.slug);
        }
        if (typeof completeCallback === 'function') {
            completeCallback(response.success, response.data);
        }
    });    
}
/**
 * Adds new change text to form changes
 *   
 * @param {String} change
 */
function _addChange(change) {
    var changesInput = _getInput('#_changes');
    changesInput.val(changesInput.val() + ';' + change);             
}
/**
 * Recalculates produt total price in specified row
 * 
 * @param {Number} rowIndex
 */
function _recalculateProductTotal(rowIndex) {
    var newProductTotal = _getProductInputVal(rowIndex, 'price_actual_taxed', 'numeric') 
        * (_getProductInputVal(rowIndex, 'amount', 'numeric') || 0);
    _setProductInputVal(rowIndex, 'total_price_actual_taxed', newProductTotal, 'numeric');
}

//
// Business logic functions
//

function recalculateCartTotal() {
    var total = 0, prodTotal = 0, divided = parseInt($('#_divided').val()), 
        freeShippingLimit = _parseNumber(Run.App.config.freeShipmentProductsTotal), 
        specificShipment = Run.Eshop.config.specificShipment,
        specificPayment = Run.Eshop.config.specificPayment,
        shipping = 0, baseShipment = 0, basePayment = 0, basePaymentCheckedInput, 
        payment, shipment, baseShipmentCheckedInput, bonus;

    $('.productPrice').each(function () {
        prodTotal += _getInputVal(this, 'numeric');
    });
    if (prodTotal < 0) {
        prodTotal = 0;
    }    
    
    bonus = _getInputVal('#totalBonus', 'numeric');
    if (bonus > 0) {
        prodTotal -= bonus;
        if (prodTotal < 0) {
            prodTotal = 0;
        }
    }

//    // check for voucher rate
//    var voucherRate = $('#voucherDiscountRate').val() || '';
//    if (voucherRate != '') {
//        // calculate voucher discount
//        var voucherDiscount = -Math.round(prodTotal * _parseNumber(voucherRate) / 100);
//        $('#voucherDiscount').val(voucherDiscount);
//    }
//    if ($('#voucherDiscount').val() !== undefined) {
//        prodTotal += _parseNumber($('#voucherDiscount').val() || 0);
//    }

    try {
        _setInputVal('#totalPriceForProducts', prodTotal, 'numeric');
    }
    catch ($error) {}

    total += prodTotal;

    if (!specificShipment) {
        baseShipmentCheckedInput = $('input[name="data[run_eshop_shipment_methods_id]"][type=radio]:checked');
        if (baseShipmentCheckedInput.length !== 0) {
                baseShipment = _parseNumber(baseShipmentCheckedInput.attr('data-value'));
        }
    }
    else {
        baseShipment = _parseNumber($('input[name="data[shipment_price]"]').val());
    }
    if (!specificPayment) {
        basePaymentCheckedInput = $('input[name="data[run_payment_methods_id]"][type=radio]:checked');
        if (basePaymentCheckedInput.length !== 0) {
            basePayment = _parseNumber(basePaymentCheckedInput.attr('data-value'));
        }
    }
    else {
        basePayment = _parseNumber($('input[name="data[payment_price]"]').val());
    }
    
    if (
        prodTotal < freeShippingLimit 
        || specificShipment
    ) {
        if (!divided) {
            shipment = baseShipment;
            payment = basePayment;
        } 
        else {
            shipment = 2 * baseShipment;
            payment = 2 * basePayment;
        }
    } 
    else {
        if (!divided) {
            shipment = 0;
            payment = 0;
        } 
        else {
            shipment = baseShipment;
            payment = basePayment;
        }
    }
    
    if ($.isNumeric(shipment)) {
        shipping += shipment;
    }
    if ($.isNumeric(payment)) {
        shipping += payment;
    }

    try {
        _setInputVal('#totalShipping', shipping, 'numeric');
    }
    catch ($error) {}
    total += shipping;

//    total += _parseNumber($('#happyChange').val());

    try {
        _setInputVal('#totalPriceForOrder', total, 'numeric');
    }
    catch ($error) {}
}
// recalculate
recalculateCartTotal();

function togglePayment(shipmentId) {
    var specificShipment = Run.Eshop.config.specificShipment,
        specificPayment = Run.Eshop.config.specificPayment;
    if (specificShipment || specificPayment) {
        return;
    }
    if (typeof shipmentId === 'undefined') {
        shipmentId = $('input[type="radio"][name="data[run_eshop_shipment_methods_id]"]:checked').val();
    }
    if (typeof shipmentId === 'undefined') {
        return;
    }
    var checkedPayment = $('input[name="data[run_payment_methods_id]"]:checked');
    var checkedPaymentId = checkedPayment.val();
    var paymentIds = Run.Eshop.config.shipmentPayment[shipmentId];
    // reset all payment methods
    // - hide them
    $('.paymentWrapper').hide();
    // - uncheck them
    $('input[name="data[run_payment_methods_id]"]:checked').prop('checked', false);
    // display the available for choosen shipment
    var length = paymentIds.length;
    for (var i = 0; i < length; i++) {
        if (checkedPaymentId == paymentIds[i]) {
            $('input[name="data[run_payment_methods_id]"]').filter('[value="' + paymentIds[i] + '"]').prop('checked', true);
        }
        $('input[name="data[run_payment_methods_id]"]').filter('[value="' + paymentIds[i] + '"]').closest('.paymentWrapper').show();
        $('input[name="data[run_payment_methods_id]"][data-pid="' + checkedPayment.attr('data-pid') + '"]').filter('[value="' + paymentIds[i] + '"]').prop('checked', true);
        // check payment by pid of old checked
    }
    recalculateCartTotal();
}

function createEventsForProductRow(rowIndex) {
    var preselector = '', hasSearchSuggestions = false, selector;
    
    if (rowIndex) {
        preselector = '#product-' + rowIndex + ' ';
    }
    
    //
    // prevent Enter key default behaviour (form submition) on keydown for any of product inputs
    //
    selector = preselector + '[name^="data[products]["]';
    $(selector).keydown(function(e){ 
        var asciiCode = e.which; // recommended to use e.which, it's normalized across browsers
        if(asciiCode === 13) {
            e.preventDefault();
        }
    });
    
    //
    // Register .productIdInput change event - load specified product from server
    //
    $(preselector + '.productIdInput').keyup(function(e){ 
        var asciiCode = e.which; // recommended to use e.which, it's normalized across browsers
        if(asciiCode === 13){
            var rowIndex = _getProductInputRowIndex(this);
            var newProductId = parseInt($(this).val());
            var oldProductId = parseInt($(this)[0].defaultValue);
            _loadProduct(rowIndex, newProductId, function(success, data) {
                if (success) {
                    if (!oldProductId) {
                        _addChange(__js('Eshop', 'Added product: :new:', {new: data.name + ' [' + newProductId + ']'}));
                    }
                    else if (oldProductId !== newProductId) {
                        _addChange(__js('Eshop', 'Changed product (:old: > :new:)', {old: oldProductId, 'new': data.name + ' [' + newProductId + ']'}));
                    }
                }
                _recalculateProductTotal(rowIndex);
                recalculateCartTotal();
            });
        }
    });
    
    //
    // Register .productAmountInput keyup event - recalculate product total
    //
    selector = preselector + '.productAmountInput';
    $(selector).keyup(function(e){ 
        var rowIndex = _getProductInputRowIndex(this);
        _recalculateProductTotal(rowIndex);
        recalculateCartTotal();
    });
    
    //
    // Register .productNameInput change event - auto complete to choose the product and load specified product from server
    //
    $(preselector + 'input.productNameInput').autocomplete({
        source: window.location.origin + '/mvc/Eshop/EshopProducts/admin_getAutocompleteList',
        delay: 0,
        minLength: 2,
        open: function(event, ui) {
            hasSearchSuggestions = 'opened';
        },
        close: function(event, ui) { 
            $('input#autocomplete').val('');        
            hasSearchSuggestions = 'closed';
        },
        select: function(event, ui) {
            var rowIndex = _getProductInputRowIndex(this);
            var newProductId = parseInt(ui.item.value);
            var oldProductId = parseInt(_getProductInput(rowIndex, 'run_eshop_products_id')[0].defaultValue);
            _loadProduct(rowIndex, newProductId, function(success, data) {
                if (success) {
                    if (!oldProductId) {
                        _addChange(__js('Eshop', 'Added product: :new:', {new: data.name + ' [' + newProductId + ']'}));
                    }
                    else if (oldProductId !== newProductId) {
                        _addChange(__js('Eshop', 'Changed product (:old: > :new:)', {old: oldProductId, 'new': data.name + ' [' + newProductId + ']'}));
                    }
                }
                _recalculateProductTotal(rowIndex);
                recalculateCartTotal();
            });
        }         
    });
    $(preselector + 'input.productNameInput').on('focus', function() {
       if (hasSearchSuggestions === 'closed') {
           $(preselector + 'input.productNameInput').autocomplete('search');
       }
    });
    $(preselector + 'input.productNameInput').on('click', function() {
       if (hasSearchSuggestions === 'closed') {
           $(preselector + 'input.productNameInput').autocomplete('search');
       }
    });
}

function addProduct() {
    var rowIndex = ++_lastProductRowIndex, template = Run.Eshop.config.orderProductRowTemplate;
    template = template.replace(new RegExp(':rowIndex:', 'g'), rowIndex);
    template = template.replace(new RegExp('___rowIndex___', 'g'), '__' + rowIndex + '__');
    template = template.replace(new RegExp(':countIndex:', 'g'), rowIndex + 1);
    jQuery('#addProductRow').before(template);
    createEventsForProductRow(rowIndex);
}

function deleteProduct(rowIndex) {
    var name = _getProductInputVal(rowIndex, 'name'), 
        id = _getProductInputVal(rowIndex, 'run_eshop_products_id'),
        orderProductNumber = _getProductInputVal(rowIndex, 'order_item_number');
    if (name !== '' && orderProductNumber !== '' && id !== '') {
        if(
            confirm(
                __js('Eshop', 'Delete product: :product:?', {product: name + ' [' + id + ', ' + orderProductNumber + ']'})
            ) !== true
        ) {
            return false;
        }
        _addChange(__js('Eshop', 'Deleted product: :product:', {product: name + ' [' + id + ', ' + orderProductNumber + ']'}));
    }
    $('#product-' + rowIndex).remove();
    recalculateCartTotal();
}

jQuery(function(){
    // get initial value of _lastProductRowIndex
    _lastProductRowIndex = jQuery('.order-product').length - 1;
    
    //
    // shipment and payment screen
    //
    togglePayment();

    $('input[name="data[run_eshop_shipment_methods_id]"]').click(function(e){
        togglePayment($(this).val());
    }); 

    $('input[name="data[run_payment_methods_id]"]').click(function(e){
        recalculateCartTotal();
    }); 

    $('#rounding').change(function() {
        $('#radostne-drobne-zaokrouhleni').html($(this).val()) + ',-Kč';
        recalculateCartTotal();
    });

    $('#happyChange').keyup(function(e){ 
         recalculateCartTotal();
    });

    $('#voucherDiscount').keyup(function(e){ 
         recalculateCartTotal();
    });

    $('#voucherDiscountRate').keyup(function(e){ 
         recalculateCartTotal();
    });
    
    $('#totalBonus').keyup(function(e){ 
         recalculateCartTotal();
    });
    
    createEventsForProductRow();

/*///this is duplicit instance of subwindow - one other is created by Run.App.SmartForm()
    // create SubWindow instance if the class is defined
    if (isDefined('Run.App.SubWindow')) {
        new Run.App.SubWindow({
            selector: '.-run-sfo-fields',
            ParentWindow: window
        });
    }
/*/

    // after form is submitted, make some checking
    $('form').submit(function(e) {
        var specificShipment = Run.Eshop.config.specificShipment,
            specificPayment = Run.Eshop.config.specificPayment;
        // check dirtable text inputs
        $('input[type=text].dirtable').each(function() {
            var $input = $(this), newValue, oldValue, sectionHeading, rowIndex, 
                productNumber, field;
            newValue = $input.val();
            oldValue = $input[0].defaultValue;
            if (newValue !== oldValue) {
                field = $input.siblings('label').text().replace(/\*+$/g, '');
                try {
                    rowIndex = _getProductInputRowIndex(this);
                    productNumber = _getProductInputVal(rowIndex, 'order_item_number');
                }
                catch (error) {};
                if (productNumber) {
                    field = productNumber + ': ' + field;
                }
                try {
                    sectionHeading = _getSectionHeading(this, {
                        topParentSelector: '.-run-sfo-fields'
                    });
                }
                catch (error) {};
                if (sectionHeading) {
                    field = sectionHeading + ': ' + field;
                }
                _addChange(__js('Eshop', ':field: (:old: > :new:)', {
                    'field': field,
                    'old': oldValue,
                    'new': newValue
                }));
            }
        });
        // check dirtable selects
        $('select.dirtable').each(function() {
            var $input = $(this), newValue, newLabel, oldValue, oldLabel, sectionHeading, 
                rowIndex, productNumber, field;
            newValue = $input.val();
            if (
                typeof newValue === 'undefined'
                || newValue === ''
            ) {
                newValue = null;
            }
            oldValue = $input.find('option[selected]').val();
            if (
                typeof oldValue === 'undefined'
                || oldValue === ''
            ) {
                oldValue = null;
            }
            if (newValue !== oldValue) {
                field = $input.siblings('label').text().replace(/\*+$/g, '');
                try {
                    rowIndex = _getProductInputRowIndex(this);
                    productNumber = _getProductInputVal(rowIndex, 'order_item_number');
                }
                catch (error) {};
                if (productNumber) {
                    field = productNumber + ': ' + field;
                }
                try {
                    sectionHeading = _getSectionHeading(this, {
                        topParentSelector: '.-run-sfo-fields'
                    });
                }
                catch (error) {};
                if (sectionHeading) {
                    field = sectionHeading + ': ' + field;
                }
                newLabel = $input.find('option[value="' + newValue + '"]').text();
                oldLabel = $input.find('option[value="' + oldValue + '"]').text();
                _addChange(__js('Eshop', ':field: (:old: > :new:)', {
                    'field': field,
                    'old': oldLabel,
                    'new': newLabel
                }));
            }
        });
        // check radio doprava a platba
        if (!specificShipment) {
            var oldShipmentInput = $('#_shipment_id');
            var newShipmentInput = $('input[name="data[run_eshop_shipment_methods_id]"]:checked');
            if (
                newShipmentInput.length !== 0
                && oldShipmentInput.val() != newShipmentInput.val()
            ) {
                _addChange(__js('Eshop', ':field: (:old: > :new:)', {
                    'field': $('#shipmentAndPayment').html(),
                    'old': $('input[name="data[run_eshop_shipment_methods_id]"][value="' + oldShipmentInput.val() + '"]').next('label').text(),
                    'new': $(newShipmentInput).next('label').text()
                }));
            }
        }
        if (!specificPayment) {
            var oldPaymentInput = $('#_payment_id');
            var newPaymentInput = $('input[name="data[run_payment_methods_id]"]:checked');
            if (
                newPaymentInput.length !== 0
                && oldPaymentInput.val() != newPaymentInput.val()
            ) {
                _addChange(__js('Eshop', ':field: (:old: > :new:)', {
                    'field': $('#shipmentAndPayment').html(),
                    'old': $('input[name="data[run_payment_methods_id]"][value="' + oldPaymentInput.val() + '"]').next('label').text(),
                    'new': $(newPaymentInput).next('label').text()
                }));
            }
        }
//        console.log($('#_changes').val());
    });
});