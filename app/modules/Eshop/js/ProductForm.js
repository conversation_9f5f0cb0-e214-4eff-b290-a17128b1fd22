"use strict";requireDefined(["jQuery","Run.App.Validate","Run.Eshop.i18n.EshopAdmin"],"Run.Eshop.ProductForm"),loadNamespace("Run.Eshop"),Run.Eshop.ProductForm=function(){function e(e){this.options={selector:null,Window:window,relatedItemsContainerSelector:".related-items",relatedItemsIndexSelector:".index",relatedItemsTemplateSelector:".template",relatedItemsAddButtonSelector:".add-item",relatedItemsTemplateClass:"template",relatedItemsItemSelector:".item",relatedItemsDeleteButtonSelector:".delete"},this.$form=null,this.$relatedItemsContainers=null,this.init(e)}return e.prototype.init=function(e){var t=this;if(this.options=jQuery.extend({},this.options,e),Run.App.Validate.emptyValue(this.options.selector))throw new Error("Missing ProductForm selector");if(this.$form=jQuery(this.options.selector,this.options.Window.document),0===this.$form.length)throw new Error("Invalid ProductForm selector. No element found.");if(1<this.$form.length)throw new Error("Many ProductForm-s has been found accoding to provided selector "+this.options.selector);this.$relatedItemsContainers=this.$form.find(this.options.relatedItemsContainerSelector),this.$relatedItemsContainers.each(function(){this._itemTemplate=jQuery(this).find(t.options.relatedItemsIndexSelector).find(t.options.relatedItemsTemplateSelector).detach()}),this.registerEvents()},e.prototype.registerEvents=function(){var t=this;this.$relatedItemsContainers.on("click",this.options.relatedItemsAddButtonSelector,function(e){e.preventDefault(),e.stopPropagation(),t.addRelatedItem(this)}),this.$relatedItemsContainers.on("click",this.options.relatedItemsDeleteButtonSelector,function(e){!0!==confirm(__jsa("Eshop","Please, confirm removal of the item"))?(e.preventDefault(),e.stopPropagation()):t.deleteRelatedItem(this)})},e.prototype.deleteRelatedItem=function(e){var t=jQuery(e);t.attr("href")||t.closest(this.options.relatedItemsItemSelector).remove()},e.prototype.addRelatedItem=function(e){var t,o,r,s,n=jQuery(e).closest(this.options.relatedItemsContainerSelector),i=n.find(this.options.relatedItemsIndexSelector),l=(new Date).getTime();t=n.get(0)._itemTemplate,o=i.children().length,(r=t.clone().removeClass(this.options.relatedItemsTemplateClass)).html(r.html().replace(/\[\]/g,"["+o+"]").replace(/:i:/g,o).replace(/:i+1:/g,o+1).replace(/ id="([^"]+)"/g,' id="'+l+'_$1"').replace(/ for="([^"]+)"/g,' for="'+l+'_$1"')),r.appendTo(i),(s=r.find(".-run-hint")).length&&(s.removeClass("tooltipstered"),new Run.App.Hints({selector:s}))},e}();