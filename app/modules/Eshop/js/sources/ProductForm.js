'use strict';

requireDefined([
    'jQuery',
    'Run.App.Validate',
    'Run.Eshop.i18n.EshopAdmin'
], 
'Run.Eshop.ProductForm');

loadNamespace('Run.Eshop');

/**
 * Class Run.Eshop.ProductForm
 * 
 * NOTE: See ipaslovakia.sk > app/modules/Eshop/views/EshopProducts/admin_form.php
 * for corresponding php code
 */
Run.Eshop.ProductForm = (function() {
    
    /**
     * Constructor
     * 
     * @param {Object} options
     * @returns {Run.Eshop.ProductForm}
     */
    function ProductForm (options) {  
        
        //
        // OPTIONS PROPERTIES
        //

        this.options = {

            /**
             * Main element selector 
             * 
             * @type {String|Element|jQuery}
             */
            selector: null,

            /**
             * Window in which main element is placed
             * 
             * @type {Window}
             */
            Window: window,
            
            relatedItemsContainerSelector: '.related-items',
            relatedItemsIndexSelector: '.index',
            relatedItemsTemplateSelector: '.template',
            relatedItemsAddButtonSelector: '.add-item',
            relatedItemsTemplateClass: 'template',
            
            relatedItemsItemSelector: '.item',
            relatedItemsDeleteButtonSelector: '.delete'
        };

        //
        // RUNTIME PROPERTIES
        //

        /**
         * Main element jQuery object
         * 
         * @type {jQuery}
         */
        this.$form = null;

        this.$relatedItemsContainers = null;
        
        //
        // LAUNCH INIT
        //
        
        this.init(options);
    };
        
    //
    // METHODS
    //
        
    /**
     * Initialization method called in constructor
     * 
     * @param {Object} options 
     */
    ProductForm.prototype.init = function(options) {    
        var _this = this;
    
        // apply provided options to default options
        this.options = jQuery.extend({}, this.options, options);
        
        if (Run.App.Validate.emptyValue(this.options.selector)) {
            throw new Error('Missing ProductForm selector');
        }
        
        // initialize class element(s) by jQuery objects
        this.$form = jQuery(this.options.selector, this.options.Window.document);
        if (this.$form.length === 0) {
            throw new Error('Invalid ProductForm selector. No element found.');
            return;
        }
        if (this.$form.length > 1) {
            throw new Error('Many ProductForm-s has been found accoding to provided selector ' + this.options.selector);
        }
        
        this.$relatedItemsContainers = this.$form.find(this.options.relatedItemsContainerSelector);
        // find all templates, and store them detached (to avoid submition of their inputs)
        // as a property of relatedItemsContainer element
        this.$relatedItemsContainers.each(function(){
            this._itemTemplate = jQuery(this).find(_this.options.relatedItemsIndexSelector)
                .find(_this.options.relatedItemsTemplateSelector).detach();
        });
                        
        this.registerEvents();
    };
    
    ProductForm.prototype.registerEvents = function() {
        var _this = this;
        this.$relatedItemsContainers.on('click', this.options.relatedItemsAddButtonSelector, function(event) {
            event.preventDefault();
            event.stopPropagation();
            _this.addRelatedItem(this);
        });
        this.$relatedItemsContainers.on('click', this.options.relatedItemsDeleteButtonSelector, function(event) {
            if(confirm(__jsa('Eshop', 'Please, confirm removal of the item')) !== true) {
                event.preventDefault();
                event.stopPropagation();
            }
            else {
                _this.deleteRelatedItem(this);
            }
        });
    };
    
    ProductForm.prototype.deleteRelatedItem = function (deleteButtonElement) {
        var $deleteButton = jQuery(deleteButtonElement);
        if (!$deleteButton.attr('href')) {
            $deleteButton.closest(this.options.relatedItemsItemSelector).remove();
        }
    };
    
    ProductForm.prototype.addRelatedItem = function (addButtonElement) {
        var $itemsContainer = jQuery(addButtonElement).closest(this.options.relatedItemsContainerSelector), 
            $itemsIndex = $itemsContainer.find(this.options.relatedItemsIndexSelector),
            $itemTemplate, index, $newItem, $hints, uniquePrefix = new Date().getTime();
    
        $itemTemplate = $itemsContainer.get(0)._itemTemplate;
        index = $itemsIndex.children().length; 
        $newItem = $itemTemplate.clone().removeClass(this.options.relatedItemsTemplateClass);
        // set the same index to all inputs    
        $newItem.html(
            $newItem.html()
                .replace(/\[\]/g, '[' + index + ']')
                .replace(/:i:/g, index)
                .replace(/:i+1:/g, index + 1)
                .replace(/ id="([^"]+)"/g, ' id="' + uniquePrefix + '_$1"')
                .replace(/ for="([^"]+)"/g, ' for="' + uniquePrefix + '_$1"')
        );
        $newItem.appendTo($itemsIndex);
        // (re)create events for hints
        $hints = $newItem.find('.-run-hint');
        if ($hints.length) {
            $hints.removeClass('tooltipstered');
            new Run.App.Hints({selector: $hints});
        }
    };
    
    return ProductForm;
})();
