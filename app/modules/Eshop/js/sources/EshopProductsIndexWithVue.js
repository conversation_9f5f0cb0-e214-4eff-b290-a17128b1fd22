/**
 * New js class template. 
 * 
 * NOTE: All types of properties and methods are included and they are introduced 
 * in "the best practice way". All of them (except of constructor() and init()) can 
 * be replaced/renamed/removed according to your needs/decisions. All sections/places 
 * which you are supposed to change or add a new code to are marked by '//...' comment.
 * 
 * NOTE: Find all occurences of '////StateManager' comment to add state manager 
 * to the new class
 */
'use strict';

requireDefined([
    'jQuery',
    'Run.App.Validate',
    'Run.App.App'
////StateManager    
//    'Run.App.WindowStateManager'
    // + add here your dependences ...
], 
'Run.Eshop.EshopProductsIndexWithVue');

loadNamespace('Run.Eshop');

/**
 * Class Run.Eshop.EshopProductsIndexWithVue
 */
Run.Eshop.EshopProductsIndexWithVue = (function() {
        
    /**
     * Constructor
     * 
     * @param {Object} options
     * @returns {Run.Eshop.EshopProductsIndexWithVue}
     */
    function EshopProductsIndexWithVue (options) {  
        
        // do this only to make IDE (NetBeans) hinting functional :)
        // if your IDE hints available options even without this then remove it
        this.options = EshopProductsIndexWithVue.prototype.options; 
        
        //
        // APPLY CUSTOM OPTIONS PROPERTIES
        // 
        // Default options are defined just behind the constructor definition
        //

        this.options = jQuery.extend({}, this.options, jQuery.extend(true, {}, options));

        //
        // RUNTIME PROPERTIES
        //

        /**
         * Main element jQuery object
         * 
         * @type {jQuery}
         */
        this.$mainElement = null;

////StateManager        
//        /**
//         * State manager instance
//         * 
//         * @type {Run.App.WindowStateManager}
//         */
//        this.StateManager = null;        
        
        //
        // LAUNCH INIT
        //
        
        // launch init() only in case that you are in instantianized class constructor 
        // and not in its parent/super class constructor (see https://stackoverflow.com/q/44109220/1245149)
        if (this.constructor === EshopProductsIndexWithVue) {
            this.init();
        }
    };
    
    //
    // DEFAULT OPTIONS PROPERTIES
    // 
    // They are defined this way (out of constructor) to make them accessible also 
    // in static method getInstance(). If you don't use getInstance() method or if
    // any problem occurs (mostly in some very complicated inheritance cases) then 
    // move default options definition (this.options = {...}) into constructor 
    // before custom options are applied, instead of 'this.options = EshopProductsIndexWithVue.prototype.options;'
    // 
    //...

    EshopProductsIndexWithVue.prototype.options = {

        /**
         * Main element selector 
         * 
         * @type {String|Element|jQuery}
         */
        selector: null,

        /**
         * Window in which main element is placed
         * 
         * NOTE: This is usefull if controlling elements in other windows (iframes)
         * than the window in which is loaded this JS class.  If everything happens
         * in one window (iframe) then let it as it is.
         * 
         * @type {Window}
         */
        Window: window,
        
        emptyIndexMessage: null,
        showDisponibility: null,
        showAddToCartButton: null,
        showSortSelect: null,
        showFilterSelect: null,
        slugProductView: null,
        slugAuthorProducts: null,
        slugManufacturerProducts: null,
        findOptions: {}
    };

    //
    // METHODS
    //
        
    /**
     * Initialization method called in constructor
     */
    EshopProductsIndexWithVue.prototype.init = function() {    
        var _this = this, app;
    
        if (Run.App.Validate.emptyValue(this.options.selector)) {
            throw new Error('Missing EshopProductsIndexWithVue selector');
        }
        /*
	app = new Vue({
            el: this.options.selector,
            data: {
                products: []
            },
            methods: {
                viewUrl(product) {
                    return Run.App.App.getUrl({
                        locator: _this.options.slugProductView,
                        args: [product.slug]
                    });
                },
                image(product) {
                    if (product.image) {
                        return product.image.small;
                    }
                    return '';
                }
            },
            computed: {
            },
            created() {
                Run.App.App.requestApi({
                    module: 'Eshop',
                    controller: 'EshopProducts',
                    action: 'findDetailed'
                }, _this.options.findOptions)
                .then(response => {this.products = response.data;});
            }
	});
        */
                
//////StateManager        
////        this.registerPreserveState();
    };
    
////StateManager    
//    EshopProductsIndexWithVue.prototype.registerPreserveState = function () {
//        var _this = this, state;
//        // create instance of state manager
//        this.StateManager = new Run.App.WindowStateManager({
//            Window: this.options.Window,
//            domain: 'Run.Eshop.EshopProductsIndexWithVue',
//            autoSave: false,
//            //inherit: true
//        });
//        // load the last stored state
//        state = this.StateManager.getState();
//        if (typeof state.myProperty !== 'undefined') {
//            // load state ...
//        }
//        // save the new state on unload of window
//        jQuery(this.options.Window).on('beforeunload', function(){
//            _this.StateManager.setState({
//                myProperty: 'save state...'
//            });
//            _this.StateManager.save();
//        });
//    };
    
    //
    // RETURN INSTANCE
    //
    
    return EshopProductsIndexWithVue;
})();
