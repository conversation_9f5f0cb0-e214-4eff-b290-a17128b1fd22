requireDefined([
    'Run.App.Validate',
    'Run.App.Number',
    // php: Eshop::$actualCurrency
    'Run.Eshop.config.actualCurrency'
], 
'Run.Eshop.Eshop');

loadNamespace('Run.Eshop');

Run.Eshop.Eshop = {
        
    /**
     * Formats provide number into price
     * 
     * @param mixed number Integer or float number
     * @param array options Following are available:
     *      - 'decimals' (int) Defaults to Eshop::actualCurrency['decimals'].
     *      - 'suffix' (string) Suffix added to integer prices, e.g. ',-' in '5,-'. 
     *          Defaults to Eshop::actualCurrency['suffix'].
     *      - 'symbol' (string) Defaults to Eshop::actualCurrency['symbol'].
     * 
     * @return string
     */
    formatPrice: function(number, options) {
        options = jQuery.extend({}, jQuery.extend(true, {}, Run.Eshop.config.actualCurrency), jQuery.extend(true, {}, options));
        // round the number before checking if it is integer - because after rounding it can be integer
        number = parseFloat(number).toFixed(options['decimals']);
        if (
            Run.App.Validate.intNumber(number, true)
            && options['suffix']
        ) {
            number = Run.App.Number.removeTrailingZeroDecimals(number);
            number += options['suffix'];
        }
        else {
            number = Run.App.Number.format(number, options['decimals'], ',', '');
        }
        if (options['symbol']) {
            number += '&nbsp;' + options['symbol'];
        }
        return number;
    }
    
}