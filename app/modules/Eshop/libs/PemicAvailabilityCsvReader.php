<?php
App::loadLib('Eshop', 'PemicCsvReader');

/**
 * Pemic availability reader using catalogue, price and stock files
 * 
 * The Catalogue file contains following items:
 *      [0] => SORTKOD (A)
 *      [1] => SORTNAZEV (B)
 *      [2] => SORTZKNAZEV (C)
 *      [3] => SORTPODNAZEV (D)
 *      [4] => EDICE (E)
 *      [5] => SORTAUTOR (F)
 *      [6] => DOSTUPNOST (G)
 *      [7] => DATPLVYD (H)
 *      [8] => RMVYDANI (I)
 *      [9] => RMDOTISK (J)
 *      [10] => SORTJAZYK (K)
 *      [11] => SORTZNACKA (L)
 *      [12] => VYROBCE (M)
 *      [13] => VYROBCEICO (N)
 *      [14] => SORTDRUH (O)
 *      [15] => SKUPKOD (P)
 *      [16] => SKUPNAZEV (Q)
 *      [17] => VAZBA (R)
 *      [18] => EAN (S)
 *      [19] => ISBN (T)
 *      [20] => POCSTRAN (U)
 *      [21] => POCKSBAL (V)
 *      [22] => VYSKA (W)
 *      [23] => SIRKA (X)
 *      [24] => TLOUSTKA (Y)
 *      [25] => HMOTNOST (Z)
 *      [26] => ANOTACE (AA)
 *      [27] => PICTURE (AB)
 *      [28] => PICTURE_SMALL (AC)
 *      [29] => SORTAUTOR2 (AD)
 * 
 * The Price file contains following items:
 *      [0] => SORTKOD
 *      [1] => EAN
 *      [2] => PARTNERKOD
 *      [3] => PRODCENA
 *      [4] => MENA
 *      [5] => RABAT
 *      [6] => PLATNOSTOD
 *      [7] => PLATNOSTDO
 *      [8] => AKCECENA
 *      [9] => AKCNIRABAT
 *      [10] => AKCEOD
 *      [11] => AKCEDO
 *      [12] => DPH
 *      [13] => PREDBEZNACENA
 * 
 * The Stock file contains following items:
 *      [0] => KODSKLAD
 *      [1] => SORTKOD
 *      [2] => EAN
 *      [3] => MNOZSTVI_KUS
 *      [4] => MNOZSTVI_BAL
 *      [5] => MNOZSTVI_CELK
 * 
 */
class PemicAvailabilityCsvReader extends PemicCsvReader {
    
    /**
     * List of eans (= pairs {ean} => {?}) for which the availabilities are processed.
     * 
     * @var array
     */
    protected $processEans = null;
    
    /**
     * List of eans (= pairs {ean} => TRUE) which has been already processed but 
     * only in case that there are any processEans provided
     * 
     * @var array 
     */
    protected $processedEans = array();
    
    protected $catalogueRead = false;
    
    /**
     * Reads PEMIC availability using catalogue, price and stock files
     * 
     * @param array $options Options of Csv() class plus following:
     *      - 'catalogueFile' (string) Pemic catalogue file. If not provided or empty 
     *          then it is downloaded from Pemic site. In such a case option 'accessId'
     *          must be provided. Defaults to NULL.
     *      - 'priceFile' (string) Pemic price file. If not provided or empty 
     *          then it is downloaded from Pemic site. In such a case option 'accessId'
     *          must be provided. Defaults to NULL.
     *      - 'stockFile' (string) Pemic stock file. If not provided or empty 
     *          then it is downloaded from Pemic site. In such a case option 'accessId'
     *          must be provided. Defaults to NULL.
     *      - 'useFullCatalogue' (bool) Applied only if catalogueFile is downloaded.
     *          If TRUE then a catalogue_full file is downloaded. Defaults to FALSE. 
     *      - 'accessId' (string) PEMIC accessId. It is value of 'guid' GET param in
     *          PEMIC dowload link. Must be provided if some of 'catalogueFile', 
     *          'priceFile' or 'stockFile' is not provided or is empty. Defauts to NULL.
     *      - 'processEans' (array&) Reference to list of eans provided like array of pair {ean} => {?} 
     *          to return availabilities for. If not provided then availabilities 
     *          are processed only for eans included in catalogue. Provide it by 
     *          reference: 'processEans' => &$eans. Array pointer is changed by method 
     *          PemicAvailabilityCsvReader::readRecord() so the pointer position
     *          should be not changed by other processing if provided here. On other 
     *          it is good if external processing removes items in array which has 
     *          been already processed. ATTENTION: This option has been used on 
     *          vydavatel to update availabilities of all produts marked as "PEMIC"
     *          products. Normally (when we don't know which products are/are not
     *          "PEMIC" products) you should not use this option! Defaults to empty array(). 
     */    
    public function __construct($options = array()) {
        $defaults = array(
            'catalogueFile' => null,
            'priceFile' => null,
            'stockFile' => null,
            'useFullCatalogue' => false,
            'accessId' => null,
            'processEans' => array(),
        );
        $options = array_merge($defaults, $options);
        
        $this->processEans = &$options['processEans'];
        
        // check for files and transfer missing ones
        if (empty($options['catalogueFile'])) {
            if ($options['useFullCatalogue']) {
                $options['catalogueFile'] = $this->transferFile('catalogue_full', $options['accessId']);
            }
            else {
                $options['catalogueFile'] = $this->transferFile('catalogue', $options['accessId']);
            }
        }
        if (empty($options['priceFile'])) {
            $options['priceFile'] = $this->transferFile('price', $options['accessId']);
        }
        if (empty($options['stockFile'])) {
            $options['stockFile'] = $this->transferFile('stock', $options['accessId']);
        }
        // load price and stock records
        $this->loadPriceRecords($options['priceFile'], array(
            'keyField' => 'ean',
        ));
        $this->loadStockRecords($options['stockFile'], array(
            'keyField' => 'ean',
        ));
        
        parent::__construct($options['catalogueFile'], array_merge($options, array(
            'mode' => 'r',
            'delimiter' => ',',
            'enclosure' => '"',
            'hasHeaders' => true,
            'encoding' => 'CP1250',
            'decimalPoint' => ',',
            'thousandsSeparator' => '',
            'recordFields' => array(
                'supplier_product_id' => array('column' => 0, 'trim' => true, 'convert' => function($value) {
                    return $this->normalizePemicId($value);
                }),      
                'ean' => array('column' => 18, 'trim' => true, 'convert' => function($value) {
                    return $this->normalizeEan($value);
                }),            
                'stock' => function($record, $outputRecord) {
                    return $this->getStock($outputRecord['ean']);
                },            
                'availability' => array('column' => 6, 'trim' => true, 'convert' => function($value, $record, $outputRecord) {
                    return $this->getAvailability($value, $outputRecord['ean']);
                }),   
                'long_delivery_time' => array('column' => 6, 'trim' => true, 'convert' => function($value) {
                    return $value === 'Z';
                }),  
                'reprint' => array('column' => 6, 'trim' => true, 'convert' => function($value) {
                    return $value === 'D';
                }),  
                'available_from' => array('column' => 7, 'trim' => true, 'convert' => function($value) {
                    if (                    
                        !empty($value)
                        && substr($value, 0, 19) != '1900-01-01 00:00:00'
                        && preg_match('/^((19|20)[0-9]{2}-[0-9]{2}-[0-9]{2})/i', $value, $matches)
                    ) {
                        return $matches[1];
                    }
                    return null;
                }),   
                'shipment_time_off_stock' => function() {
                    return 7; //HARDCODED
                }, 
            )
        )));
    }
        
    /**
     * Reads the next csv record from file. Info line, header row and empty lines are omitted.
     * Returned record has following items:
     * 
     *      array(
     *          'supplier_product_id' => ...,
     *          'ean' => ...,
     *          'stock' => ...,
     *          'availability' => ...,
     *          'long_delivery_time' => ...,
     *          'reprint' => ...,
     *          'available_from' => ...,
     *          'shipment_time_off_stock' => ...,
     *      )
     * 
     * @return bool|array Next record array. NULL if the end of csv file is reached.
     */
    public function readRecord() {
        $record = null;
        // at first read items from catalogue
        if (!$this->catalogueRead) {            
            do {
                $record = parent::readRecord();
            // take the next item if:
            //  - we have not reached end of file yet 
            //  - and processEans are provided 
            //  - and the actual item ean is not in processEans
                /*///debug>
                if (!empty($record) && $record['ean'] === '9788086055800') {
                    $x = 1;
                }
                /*///<debug
            } while (
                !empty($record)
                && !empty($this->processEans)
                && empty($this->processEans[$record['ean']])
            );
            if (empty($record)) {                
                $this->catalogueRead = true;
                // reset processEans for below processing (it is quite time consumig so do it only once here)
                reset($this->processEans);
            }
        }
        // if the catalogue is read and processEans are provided then create "fictive" 
        // items from the rest of processEans
        if (
            $this->catalogueRead
            && !empty($this->processEans)
        ) {
            $ean = null;
            $availability = null;
            do {
                $item = each($this->processEans);
                if (!empty($item['key'])) {
                    $ean = $item['key'];
                    $availability = $this->getAvailability(null, $ean);
                }
            // take the next item if:
            //  - there is any next    
            //  - and no availability is found or the $ean has been already processed    
            } while (
                $item !== false  
                && (
                    empty($availability)
                    ||
                    !empty($this->processedEans[$ean])
                )
            );
            if (
                !empty($ean)
                && !empty($availability)
            ) {
                $record = array(
                    'supplier_product_id' => null,
                    'ean' => $ean,
                    'stock' => null,
                    'availability' => $availability,
                    'long_delivery_time' => null,
                    'reprint' => null,
                    'available_from' => null,
                );
            }
        }
        // keep track of eans which has been already processed but only in case that 
        // there are any processEans provided
        if (
            !empty($this->processEans)
            && !empty($record)
        ) {
            $this->processedEans[$record['ean']] = true;
        }
        
        return $record;
    }
}
