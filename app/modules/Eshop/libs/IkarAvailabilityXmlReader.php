<?php
App::loadLib('Eshop', 'IkarXmlReader');

/**
 * Reads Ikar dostupnost.mxl file.
 * 
 * ATTENTION: This reader cannot be reliably used to import availability as file 
 * dostupnost.xml does not contain reliable info about presale status of products. 
 * This status can be resolved only according to tituly.xml and so USE IkarCatalogueXmlReader()
 * to get availability info.
 * 
 * Zipped file can be downloaded like this: 
 *  1] Call URL: https://vo.kniznyweb.sk/b2bGate?login={username}&password={pwd}&synctype=D[&timestamp={timestamp}]
 *  This URL will return a token, e.g. bYRixCHO, which will be used to obtain stock file in step 2.
 * 
 *  2] Call URL: https://vo.kniznyweb.sk/b2bGate?login={username}&password={pwd}&token={token}
 *  This URL will return following HTTP codes with following meaning:
 *      200 - URL pro stažení ZIP souboru s daty (Data jsou připravena a je možné si je z vrácené URL stahnout)
 *      204 - Warning: still in progress (Data se stále generují, požadavek je potřeba opakovat později)
 *      400 - Token not found (Chybný název tokenu v požadavku)
 *      410 - Text chybového hlášení (Při generování dat došlo k chybě)
 *      500 - Text chybového hlášení (Došlo k chybě v API rozhraní)
 * 
 * ATTENTION: The same products have different timestamps in tituly.xml, dostupnost.xml
 * and anotace.xml!
 * 
 * E.g.:
 * https://vo.kniznyweb.sk/b2bGate?login=434888&password=Zjavkova&synctype=D
 * https://vo.kniznyweb.sk/b2bGate?login=434888&password=Zjavkova&token={vygenerovanyRetazecVratenyPrvymLinkom} 
 */
class IkarAvailabilityXmlReader extends IkarXmlReader {
               
    protected $availabilityConversions = array(
        // Prodej titulu je ukončen
        '-1' => array(
            'availability' => 'enum_soldout',   
            'long_delivery_time' => false,  
            'reprint' => false,  
        ),
        // Titul není skladem
        '1' => array(
            // possibly this can be 'enum_presale' and it is precised in IkarCatalogueXmlReader 
            // according parametry->novinka and parametry->predobjednavka
            'availability' => 'enum_soldout',   
            'long_delivery_time' => false,  
            'reprint' => false,  
        ),
        // Poslední kusy na skladě
        '3' => array(
            'availability' => 'enum_available',   
            'long_delivery_time' => false,  
            'reprint' => false,  
        ),
        // skladem více jak 100ks
        '4' => array(
            'availability' => 'enum_available',   
            'long_delivery_time' => false,  
            'reprint' => false,  
        ),
        // 20 - 30ks na skladě
        '5' => array(
            'availability' => 'enum_available',   
            'long_delivery_time' => false,  
            'reprint' => false,  
        ),
        // 31 - 50ks na skladě
        '6' => array(
            'availability' => 'enum_available',   
            'long_delivery_time' => false,  
            'reprint' => false,  
        ),
        // 51 - 70ks na skladě
        '7' => array(
            'availability' => 'enum_available',   
            'long_delivery_time' => false,  
            'reprint' => false,  
        ),
        // 70 - 100ks na skladě
        '8' => array(
            'availability' => 'enum_available',   
            'long_delivery_time' => false,  
            'reprint' => false,  
        ),
        // Titul dostupný u dodavatele
        '9' => array(
            'availability' => 'enum_available',   
            'long_delivery_time' => true,  
            'reprint' => false,  
        ),
    );
    
    /**
     * Opens XML file
     * 
     * @param array $options
     *      - 'stockFile' (string) App root relative path to XML file. If not provided
     *          then it is downloaded from import provider.
     *      - 'username' (string) Must be provided if 'stockFile' is not provided
     *      - 'password' (string) Must be provided if 'stockFile' is not provided
     *      - 'startTimestamp' (string) Starting timestamp to transfer incremental import file 
     *          with changes after this timestamp. If empty value then full import 
     *          file is transfered. If not provided and 'storedTimestampName' is
     *          provided then start timestamp is initialized to last stored timestamp.
     *          To reset stored timestamp set 'startTimestamp' to NULL.
     *      - 'storedTimestampName' (string) If provided then actual XML reader instance
     *          will use last stored timestamp under this name and at the end of processing 
     *          will also store the last processed record timestamp under this name. 
     *          This is a kind of timestamp namespace allowing to remember different 
     *          timestamps for different situations an IkarXmlReader child class 
     *          uses the timestamp in. E.g. there can be one timestamp used for import
     *          of new items and another timestamp for items actualization.
     *          If not provided then no stored timestamp is considered (not loaded nor stored). 
     *          If 'startTimestamp' is provided then stored timestamp is not used to 
     *          initialize the starting timestamp but at the end of processing  
     *          is updated the stored timestamp. Defaults to NULL.
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($options = array()) {
        $defaults = array(
            'stockFile' => null,
            'username' => null,
            'password' => null,
            //'startTimestamp' => null, // not always must be present
            'storedTimestampName' => null,
        );
        $options = array_merge($defaults, $options);
                
        // check for file and transfer if not provided
        if (empty($options['stockFile'])) {
            $options['stockFile'] = $this->transferFile('stock', $options['username'], $options['password'], $options);
        }
        
        parent::__construct($options['stockFile'], $options);     
    }
    
    public function validateRecord($XmlRecord) {
        $attrs = $XmlRecord->attributes();
        if (
            isset($attrs['id'])
            && isset($attrs['ean'])
            && isset($attrs['timestamp'])
        ) {
            return true;
        }
        return false;
    }
          
    /**
     * Reads XML record and parses it to following normalized array for file dostupnost.xml:
     * 
     *      array(
     *          'supplier_product_id' => ...,
     *          'ean' => ...,
     *          'stock' => ...,
     *          'availability' => ...,
     *          'long_delivery_time' => ...,
     *          'reprint' => ...,
     *          'available_from' => ...,
     *      )
     * 
     * 
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        $attrs = $XmlRecord->attributes();
        // - ean and name are available in all 3 xml files
        $record = array();
        $record['supplier_product_id'] = !empty($attrs['id']) ? trim((string)$attrs['id']) : null;
        $record['ean'] = !empty($attrs['ean']) ? trim((string)$attrs['ean']) : null;
        $record['stock'] = !empty($attrs['pocet']) ? (int)trim((string)$attrs['pocet']) : 0;
        $ikarAvailabilityCode = (string)$XmlRecord;
        if (isset($this->availabilityConversions[$ikarAvailabilityCode])) {
            $record = array_merge($record, $this->availabilityConversions[$ikarAvailabilityCode]);
        }
        else {
            $record = array_merge($record, array(                
                'availability' => null,   
                'long_delivery_time' => null,  
                'reprint' => null,  
            ));
        }
        return $this->applyAvoidFields($record);
    }    
}
