<?php
/**
 * Licenčné ú<PERSON> pre vydavatel.sk a kontakty do MRP viď v triede MrpRequest.
 * Dokumentácia pre export skladových kariet:
 *      - https://faq.mrp.cz/faqcz/FaqAnswer.aspx?cislo=483#EXPEO0
 *      - https://mrp.sk/component/content/article/87-podpora/91-autonomny-rezim#EXPEO0
 */
class MrpStockReader {

    private $extendedData = false;

    private $batchSize = 20000;

    private $batchFrom = 0;

    private $batchTo;

    private $MrpRequest;

    private $MrpResponse = null;

    /**
     * Max existing code in eshop
     *
     * @var int
     */
    private $maxCode = 0;


    /**
     * MrpStockReader class
     *
     * @param string $serverIpAddress MRP servert IP address
     * @param string $serverPort MRP server port
     * @param array $options Following are available:
     *      - 'compression' (bool) If TRUE then communication with server is compressed.
     *          Defaults to FALSE.
     *      - 'privateKey' (string) If nonempty value provided then communication
     *          with server is encrypted and signed. Defaults to NULL.
     *      - 'extendedData' (bool) If TRUE then extended stock data are returned by
     *          MrpStockReader::readRecord(). Defaults to FALSE.
     *      - 'batchSize' (int) Batch size by which items are read from MRP server.
     *          Defaults to 20000. Use smaller value if 'extendedData' are set to TRUE,
     *          e.g. 5000.
     *      - 'fromCode' (int) Code of the first item to be read from MRP server.
     *          Item codes go continuosly from 0 to higher numbers, so you can use
     *          this option in a same way as item offset. Defaults to 0, it means
     *          items are read from the very first one.
     */
    public function __construct($serverIpAddress, $serverPort, $options = array()) {
        $defaults = array(
            'extendedData' => $this->extendedData,
            'batchSize' => $this->batchSize,
            'fromCode' => $this->batchFrom,
        );
        $options = array_merge($defaults, $options);
        $this->extendedData = $options['extendedData'];
        $this->batchSize = (int)$options['batchSize'];
        $this->batchFrom = (int)$options['fromCode'];
        $this->batchTo = $this->batchFrom + $this->batchSize;
        App::loadLib('Eshop', 'MrpRequest');
        $this->MrpRequest = new MrpRequest($serverIpAddress, $serverPort, $options);
        try {
            $Product = App::loadModel('Eshop', 'EshopProduct', true);
            $this->maxCode = (int)$Product->findField('mrp_code', array(
                'order' => 'mrp_code DESC'
            ));
        }
        catch (Throwable $e) {}
    }

    /**
     * Reads MRP record and parses it to following normalized array:
     *
     *      array(
     *          'mrp_code' => ...,
     *          'stock' => ...,
     *          'stock_reserved' => ...,
     *          'availability' => 'enum_available',
     *          'mrp_name' => ...,
     *      )
     *
     * If reader instance is creaated with 'extendedData' option set to TRUE then
     * folloving array is retuned:
     *
     *      array(
     *          'mrp_code' => ...,
     *          'stock' => ...,
     *          'stock_reserved' => ...,
     *          'availability' => 'enum_available',
     *          'mrp_name' => ...,
     *          'code' => ...,
     *      )
     *
     * @return array|null Array described here above. If there is no next record then NULL.
     */
    public function readRecord() {
        // do the initial request for first batch
        if (!$this->MrpResponse) {
            $this->MrpResponse = $this->MrpRequest->getStock(array(
                'stavy' => $this->extendedData ? 'F' : 'T',
                'SKKAR.CISLO' => $this->batchFrom . '..' . $this->batchTo,
            ));
        }
        // get the record object
        $XmlRecord = $this->MrpResponse->readRecord();
        // if no next record then...
        if (!$XmlRecord) {
            // ...do request for next batch
            while (true) {
                $this->batchFrom = $this->batchTo + 1;
                $this->batchTo += $this->batchSize;
                $this->MrpResponse = $this->MrpRequest->getStock(array(
                    'stavy' => $this->extendedData ? 'F' : 'T',
                    'SKKAR.CISLO' => $this->batchFrom . '..' . $this->batchTo,
                ));
                $XmlRecord = $this->MrpResponse->readRecord();
//                App::log('MrpStockReader_readRecord', 'batchTo: ' . $this->batchTo); //debug
                if (!empty($XmlRecord)) {
//                    App::log('MrpStockReader_readRecord', '...with records'); //debug
                    break;
                }
                // if next batch is empty and we have no greater code then return NULL
                elseif ($this->batchTo >= $this->maxCode) {
//                    App::log('MrpStockReader_readRecord', '...last'); //debug
                    return null;
                }
//                App::log('MrpStockReader_readRecord', '...without records'); //debug
            }
        }
//        App::logInfo('MRP stock fields', array(
//            'var' => $XmlRecord->fields->asXML(),
//        ));

        $stockAmount = (int)$XmlRecord->fields->pocetmj;
        $reservedAmount = (int)$XmlRecord->fields->pocrezmj;
        $record = array(
            'mrp_code' => (string)$XmlRecord->fields->cislo,
            // available amount on stock
            'stock' => ($stockAmount > $reservedAmount) ? $stockAmount - $reservedAmount : 0,
            // pocetmj (Počet MJ na zvoleném skladu) and pocrezmj (Počet rezervovaných MJ na zvoleném skladu)
            // are independent values, it means pocetmj can be 0 and pocrezmj can be 2
            // it means reservation is possible even without having anything on stock
            // (seems logical once you get it). What need here is "reserved and on stock".
            //rblb//'stock_reserved' => (int)$XmlRecord->fields->pocrezmj,
            'stock_reserved' => ($stockAmount > $reservedAmount) ? $reservedAmount : $stockAmount,
            'availability' => 'enum_available',
            'mrp_name' => (string)$XmlRecord->fields->nazev,
        );
        if ($this->extendedData) {
            $record += array(
                'code' => (string)$XmlRecord->fields->kod2,
////                'shop_location_code' => (string)$XmlRecord->fields->jednotka, //VYDAVATEL SPECIFIC
//                'shop_location_code' => (string)$XmlRecord->fields->kod3, //VYDAVATEL SPECIFIC
//                'supplier_info' => (string)$XmlRecord->fields->skupina, //VYDAVATEL SPECIFIC
            );
        }

        return $record;
    }

}
