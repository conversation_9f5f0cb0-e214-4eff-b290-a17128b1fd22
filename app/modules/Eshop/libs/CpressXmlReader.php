<?php
App::loadLib('Eshop', 'EshopXmlReader');

/**
 * ATTENTION: This reader has not yet been used for vydavatel.sk - so before the 
 * first use check if its implementation is still actual
 * 
 * NOTE: It has the same structure as Albatros XML catalogue
 */
class CpressXmlReader extends EshopXmlReader {
    
    protected $recordTagName = 'ITEM';
    
    protected $decimalPoint = ',';
    
    protected $thousandsSeparator = '';
    
    /**
     * Opens XML file
     * 
     * @param string $file App root relative path to XML file
     * @param array $options
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($file, $options = array()) {
        $defaults = array(
            'avoidFields' => null,
        );
        $options = array_merge($defaults, $options);
        parent::__construct($file, $options);
        
        $this->avoidFields = (array)$options['avoidFields'];
    }
    
    public function validateRecord($XmlRecord) {
        if (
            isset($XmlRecord->NAME)
            && isset($XmlRecord->EAN)
            && isset($XmlRecord->TYPE)
            && isset($XmlRecord->SUBTITLE)
            && isset($XmlRecord->VAT)
            && isset($XmlRecord->PRICEVAT)
            && isset($XmlRecord->AVAILABILITY)
            && isset($XmlRecord->AUTHOR)
            && isset($XmlRecord->DATEEXP)
            && isset($XmlRecord->ANNOTATION)
            && isset($XmlRecord->PUBLISHING)
            && isset($XmlRecord->IMAGE)
            && isset($XmlRecord->IMAGE_LARGE)
            && isset($XmlRecord->PAGES)
            && isset($XmlRecord->BOOKBINDING)
            && isset($XmlRecord->DIMENSION)
            && isset($XmlRecord->RELEASE)
            && isset($XmlRecord->EDITION)
            && isset($XmlRecord->LANGUAGE)
            && isset($XmlRecord->GENRE)
        ) {
            return true;
        }
        return false;
    }

    /**
     * Reads XML record and parses it to following normalized array:
     * 
     *      array(
     *          'name' => ...,
     *          'ean' => ...,
     *          'sortiment' => ...,
     *          'subtitle' => ...,
     *          'tax_rate' => ...,
     *          'price_taxed' => ...,         // taxed price
     *          'availability' => ...,  // 'enum_presale', 'enum_available', 'enum_soldout'
     *          'authors' => ...,       // array of authors
     *          'available_from' => ...,
     *          'year' => ...,
     *          'description' => ...,
     *          'manufacturer' => ...,  // manufacturer name
     *          'image_url' => ...,
     *          'pages' => ...,
     *          'binding' => ...,
     *          'dimensions' => ...,
     *          'edition' => ...,
     *          'range' => ...,         // range name
     *          'language' => ...,
     *      )
     * 
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        $record = array();
        $record['ean'] = (string)$XmlRecord->EAN;
        $record['name'] = (string)$XmlRecord->NAME;
        $record['sortiment'] = (string)$XmlRecord->TYPE;
        $record['subtitle'] = (string)$XmlRecord->SUBTITLE;
        $record['tax_rate'] = (int)trim((string)$XmlRecord->VAT, ' %');
        // price
        $record['price_taxed'] = (float)$this->normalizeNumber((string)$XmlRecord->PRICEVAT);
        // availability
        if((string)$XmlRecord->AVAILABILITY == '1') {
            $record['availability'] = 'enum_available';
        }
        else {
            $record['availability'] = 'enum_soldout';
        }
        // authors
        $record['authors'] = array_filter(array_map('trim', Str::explode(',', (string)$XmlRecord->AUTHOR)));
        // available from and year
        $record['available_from'] = (string)$XmlRecord->DATEEXP;
        $record['year'] = substr($record['available_from'], 0, 4);
        
        $record['description'] = (string)$XmlRecord->ANNOTATION;
        $record['manufacturer'] = (string)$XmlRecord->PUBLISHING;
        $record['image_url'] = (string)$XmlRecord->IMAGE_LARGE;
        $record['pages'] = (string)$XmlRecord->PAGES;
        $record['binding'] = (string)$XmlRecord->BOOKBINDING;
        $record['dimensions'] = (string)$XmlRecord->DIMENSION;
        $record['edition'] = (string)$XmlRecord->RELEASE;
        if ($record['edition']) {
            $record['edition'] = $record['edition'] . '.';
        }
        $record['range'] = (string)$XmlRecord->EDITION;
        $record['language'] = Str::uppercaseFirst((string)$XmlRecord->LANGUAGE);
        $record['categories'] = array(trim((string)$XmlRecord->GENRE));
        
        return $this->applyAvoidFields($record);
    }
}
