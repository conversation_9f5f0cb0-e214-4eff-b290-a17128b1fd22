<?php
/**
 * Licenč<PERSON><PERSON> pre vydavatel.sk a kontakty do MRP viď v triede MrpRequest.
 * Dokumentácia pre kódovanie dat:
 *      - http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#CHAPTER_3_0
 */
class MrpDataEncoding {
    
    /**
     * Should be the communication data compressed?
     * 
     * @var bool
     */
    protected $compression = false;
    
    /**
     *  Private key binary data
     * 
     * @var string
     */
    protected $privateKey = null;
    
    /**
     *  Encryption key binary data
     * 
     * @var string
     */
    private $encryptionKey = null;
    
    /**
     *  Encryption initialization vector binary data
     * 
     * @var string
     */
    private $encryptionIv = null;
    
    /**
     *  Authentication key binary data
     * 
     * @var string
     */
    private $authenticationKey = null;
    
    /**
     *  Variant key binary data
     * 
     * @var string
     */
    protected $variantKey = null;
    
    /**
     * Should be the communication data encrypted?
     * 
     * @var bool
     */
    protected $encryption = false;
    
    /**
     * MRP class
     * 
     * @param string $serverIpAddress MRP servert IP address
     * @param string $serverPort MRP server port
     * @param array $options Following are available:
     *      - 'compression' (bool) If TRUE then communication with server is compressed.
     *          Defaults to FALSE.
     *      - 'privateKey' (string) If nonempty value provided then communication 
     *          with server is encrypted and signed. String of base64 encoded binary data.
     *          Defaults to NULL.
     *      - 'variantKey' (string) Explicit variant key. String of base64 encoded 
     *          binary data. If empty then it is auto generated. Defaults to NULL.
     */
    public function __construct($options = array()) {
        $defaults = array(
            'compression' => $this->compression,
        );
        $options = array_merge($defaults, $options);
        $this->compression = $options['compression'];
        $this->initEncryption($options);
    }
    
    private function initEncryption($options = array()) {
        $defaults = array(
            'privateKey' => $this->privateKey,
            'variantKey' => $this->variantKey,
        );
        $options = array_merge($defaults, $options);
        if (empty($options['privateKey'])) {
            return;
        }
        if (($this->privateKey = base64_decode($options['privateKey'])) === false) {
            throw new Exception(__e(__FILE__, 'Invalid private key. Enter a base64 encoded value'));
        }
        if (empty($options['variantKey'])) {
            $this->variantKey = pack('H*', hash("sha256", Str::getRandom(64)));
        }
        elseif (($this->variantKey = base64_decode($options['variantKey'])) === false) {
            throw new Exception(__e(__FILE__, 'Invalid variant key. Enter a base64 encoded value'));
        }
//        $this->variantKey = pack('H*', '1F5AC77ED30CC0A5F75BB035FF0566A50DB2127AAB32D8624E0DA4D4186E7F2F'); //debug
        $hashKey = hash_hmac("sha256", pack('H*', '01'), $this->privateKey, true);
        $this->authenticationKey = hash_hmac("sha256", $hashKey . pack('H*', '02'), $this->privateKey, true);
        $this->encryptionKey = hash_hmac("sha256", $this->variantKey, $hashKey, true);
        $this->encryptionIv = pack('H*', substr(hash("sha256", $this->variantKey), 0, 32));
        $this->encryption = true;
//        App::debug(bin2hex($this->authenticationKey)); //debug
//        App::debug(bin2hex($this->encryptionKey)); //debug
//        App::debug(bin2hex($this->encryptionIv)); //debug
//        App::debug(bin2hex($message = pack('H*', '0102030405060708090A0B0C0D0E0F1011121314'))); //debug
//        App::debug(bin2hex($encryptedMessage = $this->encrypt($message))); //debug
//        App::debug(bin2hex($this->decrypt($encryptedMessage))); //debug
    }
    
    protected function compress($message) {
        // @todo
    }
    
    protected function extract($message) {
        // @todo
    }
    
    protected function encrypt($message) {
        if (empty($this->encryption)) {
            throw new Exception(__e(__FILE__, 'Cannot encrypt as no private key has been provided'));
        }
        return openssl_encrypt(
            $message,
            'aes-256-ctr',
            $this->encryptionKey,
            OPENSSL_RAW_DATA,
            $this->encryptionIv
        );
    }
    
    protected function decrypt($message) {
        if (empty($this->encryption)) {
            throw new Exception(__e(__FILE__, 'Cannot encrypt as no private key has been provided'));
        }
        return openssl_decrypt(
            $message,
            'aes-256-ctr',
            $this->encryptionKey,
            OPENSSL_RAW_DATA,
            $this->encryptionIv
        );
    }
    
    protected function getSignature($message) {
        if (empty($this->encryption)) {
            throw new Exception(__e(__FILE__, 'Cannot get signature as no private key has been provided'));
        }
        return hash_hmac("sha256", $message, $this->authenticationKey, true);
    }
}
