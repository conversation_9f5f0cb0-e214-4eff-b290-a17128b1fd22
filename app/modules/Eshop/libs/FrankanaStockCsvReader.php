<?php
App::loadLib('Eshop', 'FrankanaCsvReader');

/**
 * Contact and documentation see in FrankanaCsvReader phpDoc
 */
class FrankanaStockCsvReader extends FrankanaCsvReader {
    
    /**
     * @param array $options Following are available:
     *      - 'file' (string) App root relative path to stock CSV file. If not provided
     *          then it is downloaded from import provider.
     *      - 'username' (string) Frankana SFTP  username. Must be provided if 'file' 
     *          is not provided or is empty. Defauts to NULL.
     *      - 'password' (string) Frankana SFTP  username. Must be provided if 'file' 
     *          is not provided or is empty. Defauts to NULL.
     */
    public function __construct($options = array()) {
        $options = array_merge(array(
            'file' => null,
            'username' => null,
            'password' => null,
        ), $options);
        
        
        // transfer file if not provided
        if (empty($options['file'])) {
            $options['file'] = $this->transferFile(
                'stock', $options['username'], $options['password']
            );
        }
        
        parent::__construct($options['file'], array_merge($options, array(
            'mode' => 'r',
            'delimiter' => '|',
            'enclosure' => '"',
            'hasHeaders' => false,
            'encoding' => 'UTF8',
            'decimalPoint' => ',',
            'thousandsSeparator' => '.',
            'recordFields' => array(
                'code' => array('column' => 0, 'convertEncoding' => true, 'trim' => true),
                'price_taxed' => array('column' => 6, 'trim' => true, 'convertType' => 'float'),
                // available stock statuses see here below
                // - Availability indicator Frankana Central (Gollhofen)
                'frankana_stock_1_status' => array('column' => 7, 'trim' => true, 'convertType' => 'int'),
                // - Availability indicator Frankana Branch (Uhlstädt)
                'frankana_stock_2_status' => array('column' => 8, 'trim' => true, 'convertType' => 'int'),
                // - Availability indicator Freiko Central (Gollhofen)
                'freiko_stock_status' => array('column' => 9, 'trim' => true, 'convertType' => 'int'),
            )
        )));
    }
    
    /**
     * Reads the next csv record from file. Returned record has following items:
     * 
     *      array(
     *          'code' => ...,
     *          'price_taxed' => ...,   // ATTENTION: There is no tax_rate, you must inherit it from product
     *          'availability' => ...,
     *      )
     * 
     * @return bool|array Next record array. NULL if the end of csv file is reached.
     */
    public function readRecord() {
        $csvRecord = parent::readRecord();
        if (!$csvRecord) {
            return null;
        }
        
        /**
         * There are following stock statuses:
         *  0 = item not stocked in this warehouse
         *  1 = no query was possible with this article
         *  2 = currently not available
         *  3 = limited stock
         *  4 = available
         */
        $stockStatus = max(
            $csvRecord['frankana_stock_1_status'],
            $csvRecord['frankana_stock_2_status'],
            $csvRecord['freiko_stock_status']
        );
        
        $availability = 'enum_available';
        if ($stockStatus <= 2) {
            $availability = 'enum_on_demand';
        }
        
        // simulate some supplier stock amounts according to actual stock status
        $stock = 0;
        if ($stockStatus === 3) {
            $stock = 1;
        }
        elseif ($stockStatus === 4) {
            $stock = 5;
        }
        
        return array(
            'code' => $csvRecord['code'],
            'price_taxed' => $csvRecord['price_taxed'],
            'availability' => $availability,
            'stock' => $stock,
        );
    }    
}

