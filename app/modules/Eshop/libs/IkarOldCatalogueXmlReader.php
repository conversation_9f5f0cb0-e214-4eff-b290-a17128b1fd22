<?php
App::loadLib('Eshop', 'EshopXmlReader');

/**
 * @deparecated
 * 
 * ATTENTION: Old version of IKAR catalogue reader.
 * Instead of this use:
 *  - IkarCatalogueXmlReader()
 *  - IkarStockXmlReader()
 *  - IkarAnnotationXmlReader()
 *  - IkarCodeXmlReader()
 * 
 * Ikar has 1 xml file:
 * 
 * - index.xml - full list of products
 * 
 * Zipped file can be downloaded here: 
 * 
 * https://vo.kniznyweb.sk/xml_export_secured/index.html?type=xml&extension=zip
 * 
 * XML (nonzipped) file can be downloaded here:
 * 
 * https://vo.kniznyweb.sk/xml_export_secured/index.html?type=xml
 */
class IkarOldCatalogueXmlReader extends EshopXmlReader {
    
    protected $recordTagName = 'SHOPITEM';
    
    protected $decimalPoint = '.';
    
    protected $thousandsSeparator = '';
        
    
    /**
     * IKAR bindings (ikar lowercased ascii binding => binding value) 
     * 
     * @var array
     */
    protected  $bindings = array(
        'knihy viazane' => 'Pevná',
        'paperback' => 'Brožovaná',
    );  
    
    /**
     * Opens XML file
     * 
     * @param array $options
     *      - 'catalogueFile' (string) App root relative path to XML file. If not provided
     *          then it is downloaded from import provider.
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($options = array()) {
        $defaults = array(
            'catalogueFile' => null,
        );
        $options = array_merge($defaults, $options);
        
        // check for file and transfer if not provided
        if (empty($options['catalogueFile'])) {
            $options['catalogueFile'] = $this->transferFile();
        }
        
        parent::__construct($options['catalogueFile'], $options);        
    }
    
    public function validateRecord($XmlRecord) {
        if (
            isset($XmlRecord->ID)
            && isset($XmlRecord->PRODUCT)
            && isset($XmlRecord->ISBN)
            && isset($XmlRecord->AUTHORS)
            && isset($XmlRecord->PUBLISHERS)
            && isset($XmlRecord->PRICE_VAT)
            && isset($XmlRecord->VAT)
            && isset($XmlRecord->AVAILABILITY)
            && isset($XmlRecord->GENRES)
            && isset($XmlRecord->EAN)
            && isset($XmlRecord->PAGE_COUNT)
            && isset($XmlRecord->ISSUE_YEAR)
            && isset($XmlRecord->SUPPLY_DATE)
            && isset($XmlRecord->SIZE)
            && isset($XmlRecord->WEIGHT)
            && isset($XmlRecord->BINDING)
            && isset($XmlRecord->ANNOTATION)
            && isset($XmlRecord->IMGURL)
        ) {
            return true;
        }
        return false;
    }
          
    /**
     * Reads XML record and parses it to following normalized array for files 
     * export.xml/update.xml ( is missing):
     * 
     *      array(
     *          'supplier_product_id' => ..., // ikar ID
     *          'name' => ...,
     *          'ean' => ...,
     *          'tax_rate' => ...,
     *          'price_taxed' => ...,         // taxed price
     *          'authors' => ...,       // array of authors
     *          'year' => ...,
     *          'description' => ...,
     *          'manufacturer' => ...,  // manufacturer name
     *          //@todo//'image_import_source' => ...,
     *          //@todo//'pages' => ...,
     *          //@todo//'binding' => ...,
     *          //@todo//'dimensions' => ...,
     *          //@todo//'genre' => ...,         
     *          'availability' => ...,
     *          //@todo//'shipment_until' => ... // NULL or number of days
     *      )
     * 
     * 
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        // - ean and name are available in all 3 xml files
        $record = array();
        $record['supplier_product_id'] = trim((string)$XmlRecord->ID);
        $record['ean'] = trim((string)$XmlRecord->EAN);
        $record['name'] = trim((string)$XmlRecord->PRODUCT);
        $record['tax_rate'] = (int)trim((string)$XmlRecord->VAT);
        // price
        $record['price_taxed'] = (float)$this->normalizeNumber((string)$XmlRecord->PRICE_VAT);
        // authors
        $authorArray = array();
        if (isset($XmlRecord->AUTHORS->AUTHOR)) {
            $authors = $XmlRecord->AUTHORS;
            foreach($authors->AUTHOR as $author) {
                $tmpArray = explode(',', (string)$author);
                foreach ($tmpArray as $value) {
                    if ($value !== 'autor neuvedený') {
                        array_push($authorArray, $value);
                    }
                }
            }
        }
        $record['authors'] = array_map('trim', $authorArray);

        // available from and year
        $record['year'] = trim((string)$XmlRecord->ISSUE_YEAR);
        // availability
        if ($XmlRecord->AVAILABILITY > 1) {
            $record['availability'] = 'enum_available';
        }
        // if AVAILABILITY == 1 => Nedostupné
        else {
            $record['availability'] = 'enum_soldout';
        }

        $record['description'] = trim((string)$XmlRecord->ANNOTATION->LONG);
        $record['manufacturer'] = trim((string)$XmlRecord->PUBLISHERS->PUBLISHER->MARK);
        
//        $record['image_import_source'] = trim((string)$XmlRecord->IMGURL);
//        $record['pages'] = trim((string)$XmlRecord->PAGE_COUNT);
//        $record['binding'] = trim((string)$XmlRecord->BINDING);
//        $width = trim((string)$XmlRecord->ROZMERY->SIRKA);
//        $height = trim((string)$XmlRecord->ROZMERY->VYSKA);
//        if ($width && $height) {
//            $record['dimensions'] = $width . 'x'. $height . ' mm';
//        }
//        $record['weight'] = trim((string)$XmlRecord->ROZMERY->HMOTNOST);
//        $record['genre'] = trim((string)$XmlRecord->GENRE);
        
        return $this->applyAvoidFields($record);
    }
    
    protected function transferFile() {
        $this->ensureTmpDir();
        $url = 'https://vo.kniznyweb.sk/xml_export_secured/index.html?type=xml&extension=zip';
        $zipFile = File::transfer(
            array($url),
            $this->tmpDir,
            array(
                'unique' => false,
                'name' => 'ikar.zip',
            )
        );
        if (empty($zipFile)) {
            throw new Exception(sprintf('Transfer of file %s has failed', $url));
        }
        $zipFile = $this->tmpDir . DS . $zipFile;
        File::unzip($zipFile, array('delete' => true));
        return $this->tmpDir . DS . 'index.xml';
    }
    
}