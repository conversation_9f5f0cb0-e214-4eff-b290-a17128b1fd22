<?php

/**
 * Updates existing products stock amount, based in their code.
 * Products of all types are handled here.
 * 
 * File EXPORTKARET.csv (exported from MRP) contains following columns: 
 * 
 *  - code
 *  - stock
 *  - reserved (number of reserved items by orders)
 *  - stock_location_code
 *  - shop_location_code
 *  - supplier_info
 * 
 * The actual stock number to be inported to eshop is (stock - reserved) 
 * 
 */
class InternalStockCsvReader extends Csv {
    
    /**
     * Reads existing products stock amount exported from MRP as csv file
     * 
     * @param string $file
     * @param array $options Options of Csv() class
     */    
    public function __construct($file, $options = array()) {
        parent::__construct($file, array_merge($options, array(
            'mode' => 'r',
            'delimiter' => ',',
            'enclosure' => '"',
            'hasHeaders' => false,
//            'encoding' => 'CP1250',
//            'decimalPoint' => ',',
            'thousandsSeparator' => '',
            'recordFields' => array(
                'code' => array('column' => 0, 'trim' => true),      
                'stock' => function ($record, $outputRecord) {
                    return (int)Sanitize::value($record[1]) - (int)Sanitize::value($record[2]);
                },            
                'stock_location_code' => array('column' => 3, 'trim' => true, 'convert' => function($value) {
                    if (empty($value)) {
                        return null;
                    }
                    return $value;
                }),   
                'shop_location_code' => array('column' => 4, 'trim' => true, 'convert' => function($value) {
                    if (empty($value)) {
                        return null;
                    }
                    return $value;
                }),   
                'supplier_info' => array('column' => 5, 'trim' => true, 'convert' => function($value) {
                    if (empty($value)) {
                        return null;
                    }
                    return $value;
                }),   
            )
        )));
    }
        
    /**
     * Reads the next csv record from file. Info line, header row and empty lines are omitted.
     * Returned record has following items:
     * 
     *      array(
     *          'code' => ...,
     *          'stock' => ...,
     *          'stock_location_code' => ...,
     *          'shop_location_code' => ...,
     *      )
     * 
     * @return bool|array Next record array. NULL if the end of csv file is reached.
     */
    public function readRecord() {
        return parent::readRecord();
    }
}
