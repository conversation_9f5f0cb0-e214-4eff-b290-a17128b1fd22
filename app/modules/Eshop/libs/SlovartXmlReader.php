<?php
App::loadLib('Eshop', 'EshopXmlReader');

/**
 * Abstract class to be used as base for Slovart catalogue reader.
 */
abstract class SlovartXmlReader extends EshopXmlReader {
    
    protected $recordTagName = 'SHOPITEM';
    
    protected $decimalPoint = '.';
    
    protected $thousandsSeparator = '';
    
    /**
     * Transfers Slovart import files
     * 
     * @param string $accessKey You can get it in Slovart B2B backend as the 
     *      'b2b=' get parameter of catalogue download URL.
     * 
     * @return string transfered filename path
     * 
     * @throws Exception
     */
    protected function transferFile($accessKey) {
        if (empty($accessKey)) {
            throw new Exception('Missing Slovart download access key');
        }
        $this->ensureTmpDir();
        $url = 'https://www.slovart.sk/eshop.html?page_id=21878&b2b=' . $accessKey;
        $file = File::transfer(
            array($url),
            $this->tmpDir,
            array(
                'unique' => false,
                'name' => 'catalogue.xml',
            )
        );
        if (empty($file)) {
            throw new Exception(sprintf('Transfer of file from URL %s has failed', $url));
        }
        $file = $this->tmpDir . DS . $file;  
        return $file;
    }    
}