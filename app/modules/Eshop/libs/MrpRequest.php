<?php
App::loadLib('Eshop', 'MrpDataEncoding');
/**
 * MRP vydavatel.sk:
 *      Licenčný kód: YC070072
 *      Užívateľ: Vydavateľstvo matice Slovenskej
 * Podpora:
 *      <EMAIL>, <EMAIL>, 0905/ 821 211, viď https://mrp.sk/podpora/hot-line
 * Podpora autonómneho režimu: 
 *      autor autonómneho režimu: <PERSON><PERSON>, <EMAIL>
 *      ohľadom skladových kariet a objednávok: <PERSON><PERSON><PERSON>, <EMAIL>
 *      ohľadom faktúr: <PERSON><PERSON><PERSON>, <EMAIL>
 * Dokumentácia: 
 *      - http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp
 *      - http://www.mrp.sk/index.php/autonomnyr.html
 *      - http://obchodymrp.sk/index.php/zakladne-otazky/14-nastavenie-rozhrania
 */
class MrpRequest extends MrpDataEncoding {
    
    private $serverIpAddress;
    
    private $serverPort;
    
    /**
     * List of available (implemented) commands
     * 
     * @var array
     */
    private $availableCommands = array(
        'EXPEO0' => true, 
        'EXPFV1' => true,
        'IMPEO0' => true,
    );
    
    /**
     * MrpRequest class
     * 
     * @param string $serverIpAddress MRP servert IP address
     * @param string $serverPort MRP server port
     * @param array $options Following are available:
     *      - 'compression' (bool) If TRUE then communication with server is compressed.
     *          Defaults to FALSE.
     *      - 'privateKey' (string) If nonempty value provided then communication 
     *          with server is encrypted and signed. Defaults to NULL.
     */
    public function __construct($serverIpAddress, $serverPort, $options = array()) {
        $this->serverIpAddress = $serverIpAddress;
        $this->serverPort = $serverPort;
        // force autogeneration of variantKey
        $options['variantKey'] = null;
        parent::__construct($options);
    }
    
    /**
     * Returns app root relative path (including file name) to file for saving response
     * File does not exists yet. It can be just used to save response.
     * 
     * @return string
     */
    private function getResponseFilePath() {       
        $tmpDir = File::getRelativePath(TMP);
        return $tmpDir . DS . uniqid(Str::underscore(get_class($this)) . '_response_tmp_');
    }
        
    /**
     * Executes provided command.
     * This is a general method internally used by publicly exposed methods.
     * 
     * NOTE: Filters and params allowing range (e.g. 'SKKAR.CISLO') use following 
     * syntax to specify the range: 
     *      - from 1 to 10: '1..10'
     *      - 1, 3 and 7: '1|3|7'
     *      - from 1..4, 6 and from 8 to 10: '1..4|6|8..10'
     * 
     * @param string $command One of 'EXPEO0', 'IMPEO0' (see Mrp::$availableCommands)
     * @param array $options
     *      - 'requestId' (string) Required only for 'IMPEO0' command. Defaults to NULL.
     *      - 'filters' (array) Array of filters pairs {filterName} => {filterValue}.
     *          For available possibilities see documentation of $command on http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#{$command}
     *          > > Atributy elementu fltvalue. Defaults to empy array().
     *      - 'params' (array) Array of params pairs {paramName} => {paramValue}.
     *          For available possibilities see documentation of $command on http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#{$command}
     *          > Atributy elementu paramvalue. Defaults to empy array().
     *      - 'orders' (string) XML string of orders data like '< objednavka >...< /objednavka >< objednavka >...< /objednavka >...'
     *          See http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#IMPEO0 .
     *          Required only for 'IMPEO0' command. Defaults to NULL.
     * 
     * @return MrpResponse Instance of MrpResponse class
     * 
     * @throws Exception
     */
    private function execute($command, $options = array()) {
        $command = strtoupper($command);
        if (empty($this->availableCommands[$command])) {
            throw new Exception(__e(__FILE__, 'Unsupported command "%s"', $command));
        }
        $defaults = array(
            'requestId' => null,
            'filters' => array(),
            'params' => array(),
            'orders' => null,
        );
        $options = array_merge($defaults, $options);
        // validate options
        if ($command === 'IMPEO0') {
            if (empty($options['orders'])) {
                throw new Exception(__e(__FILE__, 'Missing "orders" option for command "%s"', $command));
            }
            if (!empty($options['filters'])) {
                throw new Exception(__e(__FILE__, 'Command "%s" does not use "filters" option', $command));
            }
        }
        elseif ($command !== 'IMPEO0') {
            if (!empty($options['orders'])) {
                throw new Exception(__e(__FILE__, 'Command "%s" does not use "orders" option', $command));
            }
            if (!empty($options['params'])) {
                throw new Exception(__e(__FILE__, 'Command "%s" does not use "params" option', $command));
            }
        }
        // prepare request
        $requestXml = App::loadView('Eshop', 'MrpRequest/request', array_merge($options, array(
            'command' => $command,
        )));
//        App::log('MrpRequest_execute', '$requestXml', array('var' => $requestXml)); //debug
        if (!$this->compression && !$this->encryption) {
            $envelopeXml = App::loadView('Eshop', 'MrpRequest/envelope', array(
                'requestXml' => $requestXml,
            ));
        }
        else {
            $encodingParamsXml = App::loadView('Eshop', 'MrpRequest/encodingParams', array(
                'compression' => $this->compression,
                'encryption' => $this->encryption,
                'variantKey' => $this->variantKey,
            ));
            $encodedData = &$requestXml;
            if ($this->compression) {
                $encodedData = $this->compress($encodedData);
            }
            if ($this->encryption) {
                $encodedData = $this->encrypt($encodedData);
            }
            $authCode = $this->getSignature($encodingParamsXml . $encodedData);
            $envelopeXml = App::loadView('Eshop', 'MrpRequest/encodedEnvelope', array(
                'encodingParamsXml' => $encodingParamsXml,
                'encodedData' => $encodedData,
                'authCode' => $authCode,
            ));
        }
        //file_put_contents(TMP . DS . 'envelopeXml.txt', $envelopeXml); exit; //debug
        //echo $envelopeXml; exit; //debug
        // do request
        $requestUrl = 'http://' . $this->serverIpAddress . ':' . $this->serverPort;
        $responseFile = $this->getResponseFilePath();
        if (!App::request($requestUrl, array(
            'method' => 'post',
            'data' => $envelopeXml,
            'header' => array('Content-Type: text/xml'),
            'outputFile' => $responseFile,
        ))) {
            if (is_readable(ROOT . DS . $responseFile)) {
                unlink(ROOT . DS . $responseFile);
            }
            throw new Exception(__e(__FILE__, 'HTTP request to "%s" has failed', $requestUrl));
        }
        App::loadLib('Eshop', 'MrpResponse');
        return new MrpResponse($responseFile, array(
            'privateKey' => base64_encode($this->privateKey),
        ));
    }
    
    /**
     * Returns MRP stock XML
     * 
     * @param array $filters Optional. Array of filters pairs {filterName} => {filterValue}.
     *      For available possibilities see https://faq.mrp.cz/faqcz/FaqAnswer.aspx?cislo=483#EXPEO0 
     *      > Atributy elementu fltvalue. Defaults to array('stavy' => 'T').
     * 
     * @return MrpResponse Instance of MrpResponse class
     */
    public function getStock($filters = array()) {
        $defaultFilters = array(
            'stavy' => 'T',
        );
        $filters = array_merge($defaultFilters, $filters);
        return $this->execute('EXPEO0', array(
            'filters' => $filters,
        ));
    }
    
    /**
     * Returns MRP invoices XML
     * 
     * @param array $filters Optional. Array of filters pairs {filterName} => {filterValue}.
     *      The most common filter is 'DocumentNumber'. For available possibilities 
     *      see https://faq.mrp.cz/faqcz/FaqAnswer.aspx?cislo=483#EXPFV1 
     *      > Atributy elementu fltvalue. Defaults to empty array(), it means no filters are applied.
     * 
     * @return MrpResponse Instance of MrpResponse class
     */
    public function getInvoices($filters = array()) {
        return $this->execute('EXPFV1', array(
            'filters' => $filters,
        ));
    }
    
    /**
     * Sends orders to MRP
     * 
     * @param string $requestId Request id used to pair the same requests. This is very
     *      formal pairing id, and there is no real use so provide here e.g. timestamp 
     *      generated like: date('Ymdhis') . Str::getRandom(3, '0123456789');
     * @param string $orders XML string of orders data like '< objednavka >...< /objednavka >< objednavka >...< /objednavka >...'
     *          See https://faq.mrp.cz/faqcz/FaqAnswer.aspx?cislo=483#IMPEO0
     * @param array $params Optional. Array of params pairs {paramName} => {paramValue}.
     *      For available possibilities see https://faq.mrp.cz/faqcz/FaqAnswer.aspx?cislo=483#IMPEO0 
     *      > Atributy elementu paramvalue. Defaults to empy array().
     * 
     * @return MrpResponse Instance of MrpResponse class
     */
    public function sendOrders($requestId, $orders, $params = array()) {
        return $this->execute('IMPEO0', array(
            'requestId' => $requestId,
            'orders' => $orders,
            'params' => $params,
        ));
    }
}
