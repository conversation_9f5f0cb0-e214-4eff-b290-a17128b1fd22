<?php
App::loadLib('Eshop', 'MrpDataEncoding');
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pre vydavatel.sk a kontakty do MRP viď v triede MrpRequest.
 */
class MrpResponse extends MrpDataEncoding {
    
    private $responseFile;
    
    private $responseFileAbsolutePath;
    
    private $dataFile;
    
    private $dataFileAbsolutePath;
    
    private $command;
        
    private $XmlReader;
    
    /**
     * Has been the launch of destructor ensured by register_shutdown_function()?
     * 
     * @var bool 
     */
    protected $destructMethodEnsured = false;
    
    public function __construct($responseFile, $options = array()) {
        $this->responseFile = File::normalizePath($responseFile);
        $this->responseFileAbsolutePath = File::getAbsolutePath($this->responseFile);
        if (!is_readable($this->responseFileAbsolutePath)) {
            throw new Exception(__e(__FILE__, 'Unexisting response file "%s" provided', $this->responseFile));
        }
        $this->registerTmpFilesRemoval();
        $responseFileHandle = fopen($this->responseFileAbsolutePath, 'rb');
        if (File::searchString($responseFileHandle, '<encodedBody ') !== false) {
            // get encoding params
            $pos1 = File::searchString($responseFileHandle, '<encodingParams>', array(
                'return' => 'after',
            ));
            if ($pos1 === false) {
                throw new Exception(__e(__FILE__, 'Missing encodingParams tag'));
            }
            $pos2 = File::searchString($responseFileHandle, '</encodingParams>');
            if ($pos2 === false) {
                throw new Exception(__e(__FILE__, 'Missing encodingParams closing tag'));
            }
            fseek($responseFileHandle, $pos1);
            $encodingParams = fread($responseFileHandle, $pos2 - $pos1);
            $encodingParams = preg_replace('/^<!\[CDATA\[/i', '', $encodingParams);
            $encodingParams = preg_replace('/\]\]>$/i', '', $encodingParams);
            $encodingParams = base64_decode($encodingParams);
            // check for variant key 
            if (($pos1 = strpos($encodingParams, '<varKey>')) !== false) {
                $pos1 += strlen('<varKey>');
                $pos2 = strpos($encodingParams, '</varKey>');
                $variantKey = substr($encodingParams, $pos1, $pos2 - $pos1);
                $variantKey = preg_replace('/^<!\[CDATA\[/i', '', $variantKey);
                $variantKey = preg_replace('/\]\]>$/i', '', $variantKey);
                $options['variantKey'] = $variantKey;
            }
            // check for compression
            if (preg_match('/<mrpEncodingParams.*?compression="[^>]+>/i', $encodingParams)) {
                $options['compression'] = true;
            }
            // init data encoding
            parent::__construct($options);
            // get encoded data
            $pos1 = File::searchString($responseFileHandle, '<encodedData>', array(
                'return' => 'after',
            ));
            if ($pos1 === false) {
                throw new Exception(__e(__FILE__, 'Missing encodedData tag'));
            }
            $pos2 = File::searchString($responseFileHandle, '</encodedData>');
            if ($pos2 === false) {
                throw new Exception(__e(__FILE__, 'Missing encodedData closing tag'));
            }
            fseek($responseFileHandle, $pos1);
            $encodedData = fread($responseFileHandle, $pos2 - $pos1);
            $encodedData = preg_replace('/^<!\[CDATA\[/i', '', $encodedData);
            $encodedData = preg_replace('/\]\]>$/i', '', $encodedData);
            $encodedData = base64_decode($encodedData);
            // get auth code
            if ($this->encryption) {
                $pos1 = File::searchString($responseFileHandle, '<authCode>', array(
                    'return' => 'after',
                ));
                if ($pos1 === false) {
                    throw new Exception(__e(__FILE__, 'Missing authCode tag'));
                }
                $pos2 = File::searchString($responseFileHandle, '</authCode>');
                if ($pos2 === false) {
                    throw new Exception(__e(__FILE__, 'Missing authCode closing tag'));
                }
                fseek($responseFileHandle, $pos1);
                $authCode = fread($responseFileHandle, $pos2 - $pos1);
                $authCode = preg_replace('/^<!\[CDATA\[/i', '', $authCode);
                $authCode = preg_replace('/\]\]>$/i', '', $authCode);
                $authCode = base64_decode($authCode);
                if ($authCode !== $this->getSignature($encodingParams . $encodedData)) {
                    throw new Exception(__e(__FILE__, 'Invalid response signature'));
                }
                $data = '<mrpEnvelope><body>' . $this->decrypt($encodedData) . '</body></mrpEnvelope>';
            }
            fclose($responseFileHandle);
            $this->dataFile = $this->getDataFilePath();
            $this->dataFileAbsolutePath = File::getAbsolutePath($this->dataFile);
            file_put_contents($this->dataFileAbsolutePath, $data);
        }        
        else {
            $this->dataFile = $this->responseFile;
            $this->dataFileAbsolutePath = $this->responseFileAbsolutePath;
        }
        
        if (File::searchString($this->dataFile, '/<status>(.*?)<\/status>/is', array(
            'match' => &$match,
            'sliceSize' => 4096,
        )) === false) {
            throw new Exception(__e(__FILE__, 'Response status not found'));
        }
        $statusXml = $match[1];
        
        if (!preg_match('/<request .*?command="([^"]+)"[^>]*>/i', $statusXml, $match)) {
            throw new Exception(__e(__FILE__, 'Response command not identified'));
        }
        $this->command = strtoupper($match[1]);    
        
        if (preg_match('/<error[^>]*>(.*?)<\/error>/is', $statusXml, $match)) {
            throw new Exception(__e(__FILE__, 'Response error: %s', $match[1]));
        }
        
        // Returned records are always retrieved from path 'mrpEnvelope/body/mrpResponse/data/{???}'
        // in XML file where {???} depends on response command. For details see command
        // description on http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp
        //
        // do 2 following hacks to make it work
        // - preset records path to base path which is valid for case that there are no records in response
        $recordPath = 'mrpEnvelope/body/mrpResponse/data';
        // - preset records tag to 'unexsting' name till we are not sure that we are in 
        // section we are looking for ('karty' or 'objednavka'). Do this because 
        // the 'row' tag is present also in other sections under initial record 
        // path '.../datasets'(see the hack #1) and we need to avoid iterating rows 
        // in these other sections
        $recordTagName = 'unexisting';
        if ($this->command === 'EXPEO0') {
            if (File::searchString($this->dataFile, '/<data><datasets><karty><rows>/i') !== false) {
                $recordPath .= '/datasets/karty/rows';
                $recordTagName = 'row';
            }
        }
        elseif ($this->command === 'EXPFV1') {
            if (File::searchString($this->dataFile, '/<data><MRPKSData[^>]*>(<!--[^>]*-->)?<IssuedInvoices\/?>/i') !== false) {
                $recordPath .= '/MRPKSData/IssuedInvoices';
                $recordTagName = 'Invoice';
            }
        }
        elseif ($this->command === 'IMPEO0') {
            if (File::searchString($this->dataFile, '/<data><datasets><objednavka><rows>/i') !== false) {
                $recordPath .= '/datasets/objednavka/rows';
                $recordTagName = 'row';
            }
        }
        else {
            throw new Exception(__e(__FILE__, 'Unsupported command "%s"', $this->command));
        }
        App::loadLib('App', 'XmlRecordsReader');
        $this->XmlReader = new XmlRecordsReader($this->dataFile, array(
            'recordsPath' => $recordPath,
            'recordTagName' => $recordTagName,
            'allowNoRecords' => true,
            'decimalPoint' => '.',
            'thousandsSeparator' => '',
        ));
    } 
    
    /**
     * Reads next response record and returns it.
     * 
     * For details see command description on http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp
     * 
     * @return null|SimpleXMLElement NULL if there is no next record. Record object 
     *      parsed by SimpleXMLElement class. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = $this->XmlReader->readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        return $XmlRecord;
    }
    
////debug    
//    static private $dataFileCount = 0;
    
    /**
     * Returns app root relative path (including file name) to file for saving response
     * File does not exists yet. It can be just used to save response.
     * 
     * @return string
     */
    private function getDataFilePath() {      
        $tmpDir = File::getRelativePath(TMP);
////debug    
//        return $tmpDir . DS . uniqid(Str::underscore(get_class($this)) . '_data_tmp_'. str_pad(++self::$dataFileCount, 5, 0, STR_PAD_LEFT) . '_') . '.xml';
        return $tmpDir . DS . uniqid(Str::underscore(get_class($this)) . '_data_tmp_');
    }
    
    protected function removeTmpFiles() {
        if (is_readable($this->responseFileAbsolutePath)) {
            unlink($this->responseFileAbsolutePath);
        }
        if (
            $this->dataFileAbsolutePath !== $this->responseFileAbsolutePath
            && is_readable($this->dataFileAbsolutePath)
        ) {
            unlink($this->dataFileAbsolutePath);
        }
    }
    
    /**
     * Ensure that the destructor is called even in case of fatal error
     */
    protected function registerTmpFilesRemoval() {
        if (!$this->destructMethodEnsured) {
            // ensure that the destructor is called even in case of fatal error (downside
            // is that object reference = object itself exists till the end of script execution)
            register_shutdown_function(array($this, '__destruct'));
            $this->destructMethodEnsured = true;
        }
    }
    
    public function __destruct() {
        $this->removeTmpFiles();
    }
}
