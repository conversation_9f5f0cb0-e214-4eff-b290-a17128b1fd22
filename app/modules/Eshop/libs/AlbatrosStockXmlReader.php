<?php
App::loadLib('Eshop', 'AlbatrosXmlReader');

/**
 * Albatros XML stock file can be downloaded from https://www.distri.sk/xml-compact/{KEY}
 * The {KEY} you can get in albatros backend and it is generated from IP address you 
 * download the catalogue on.
 * 
 * XML FULL catalogue item tags:
 * 
 *      NAME - nazov titulu
 *      EAN - EAN kod (ISBN)
 *      VAT - sazba DPH
 *      PRICEVAT - doporucena cena
 *      AVAILABILITY - dostupnost (1 = skladom, 0 = nie je skladom)
 *      AVAILABILITY_DETAIL - skladová zásoba na distribučnom sklade v Pezinku (ks), doba dodania 2-3 dni
 *      AMOUNT_CENTRAL_AVAILABLE - skladová zásoba centrálnom sklade v Prahe (ks), doba dodania 10-14 dní
 *      DATEEXP - dátum vydania (rrrr-mm-dd)
 *      REPRINTING_DATE - datum dotlace 
 *  
 * Contact: <PERSON><PERSON><PERSON>, 0904 701 762)
 * 
 * NOTE: It has the same structure as Cpress XML catalogue.
 * 
 * NOTE: XML COMPACT catalogue items tags are just subset of FULL catalogue. 
 */
class AlbatrosStockXmlReader extends AlbatrosXmlReader {
    
    /**
     * Actual date in format YYYY-MM-DD. Used to skip old available_from dates
     * 
     * @var string
     */
    protected $actualDate = null;
    
    /**
     * Opens XML file
     * 
     * @param array $options
     *      - 'stockFile' (string) App root relative path to XML file. If not provided
     *          then it is downloaded from import provider.
     *      - 'accessKey' (string) ALBATROS accessKey. You can get it in albatros 
     *          b2b backend and it is generated from IP address you download the catalogue on. 
     *          Must be provided if 'stockFile' is not provided or is empty. Defauts to NULL.
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($options = array()) {
        $defaults = array(
            'stockFile' => null,
            'accessKey' => null,
        );
        $options = array_merge($defaults, $options);
        $this->actualDate = date('Y-m-d');
        
        // check for file and transfer if not provided
        if (empty($options['stockFile'])) {
            $options['stockFile'] = $this->transferFile('stock', $options['accessKey']);
        }
        
        parent::__construct($options['stockFile'], $options);        
    }
    
    public function validateRecord($XmlRecord) {
        if (
            isset($XmlRecord->NAME)
            && isset($XmlRecord->EAN)
            && isset($XmlRecord->VAT)
            && isset($XmlRecord->PRICEVAT)
            && isset($XmlRecord->AVAILABILITY)
            && isset($XmlRecord->AVAILABILITY_DETAIL)
            && isset($XmlRecord->AMOUNT_CENTRAL_AVAILABLE)
            && isset($XmlRecord->DATEEXP)
            && isset($XmlRecord->REPRINTING_DATE)
        ) {
            return true;
        }
        return false;
    }
          
    /**
     * Reads XML record and parses it to following normalized array for files 
     * export.xml/update.xml ( is missing):
     * 
     *      array(
     *          'name' => ...,
     *          'ean' => ...,
     *          'tax_rate' => ...,
     *          'price_taxed' => ...,           
     *          'availability' => ...,          // 'enum_presale', 'enum_available', 'enum_soldout'
     *          'long_delivery_time' => ...,          
     *          'reprint' => ...,            
     *          'available_from' => ...,
     *          'stock' => ...,
     *          'shipment_time_off_stock' => ...,  
     *      )
     * 
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        // - ean and name are available in all 3 xml files
        $record = array();
        $record['name'] = trim((string)$XmlRecord->NAME);
        $record['ean'] = trim((string)$XmlRecord->EAN);
        $record['tax_rate'] = (int)trim((string)$XmlRecord->VAT, ' %');
        // price
        $record['price_taxed'] = (float)$this->normalizeNumber((string)$XmlRecord->PRICEVAT);
        // availability, available_from & reprint
        if ((int)$XmlRecord->AVAILABILITY === 1) {
            $record['availability'] = 'enum_available';
        }
        else {
            $record['availability'] = 'enum_soldout';
        }
        $record['available_from'] = null;
        if (
            ($date = (string)$XmlRecord->DATEEXP)
            && ($date = Date::format($date, 'Y-m-d'))
            && $date > $this->actualDate
        ) {
            // to simulate real date of book availability add ~8 days to DATEEXP
            //rblb//$record['available_from'] = (string)$XmlRecord->DATEEXP;
            $record['available_from'] = date('Y-m-d', strtotime((string)$XmlRecord->DATEEXP . ' + 8 day'));
            $record['availability'] = 'enum_presale';
        }
        $record['reprint'] = false;
        if ((string)$XmlRecord->REPRINTING_DATE) {
            $record['reprint'] = true;
            $reprintingDate = Date::format((string)$XmlRecord->REPRINTING_DATE);
            if ($reprintingDate > $this->actualDate) {
                $record['available_from'] = $reprintingDate;
                $record['availability'] = 'enum_presale';
            }
        }
        $record['stock'] = (int)trim((string)$XmlRecord->AVAILABILITY_DETAIL);
        $record['long_delivery_time'] = false;
        if (!empty($record['stock'])) {
            $record['shipment_time_off_stock'] = 5; // 2-3 dni albatros -> obchodník + 2 obchodník -> zákazník
        }
        else {
            $record['shipment_time_off_stock'] = 16; // 10-14 dni albatros -> obchodník + 2 obchodník -> zákazník
            $record['long_delivery_time'] = true;
        }
        
        return $this->applyAvoidFields($record);
    }
}