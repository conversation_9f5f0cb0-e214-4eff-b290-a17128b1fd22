<?php
App::loadLib('Eshop', 'EshopXmlReader');

/**
 * Class containing functionality for Frankana API readers.
 *
 * See phpDoc of FrankanaApiRequest class
 * 
 * NOTE: Frankana API returns XML documents, that is why it is more suitable extend
 * here EshopXmlReader() because behind the scenes we are readig an XML file.
 * Class FrankanaApiRequest() is used internally just to obtain the XML document.   
 */
abstract class FrankanaApiReader extends EshopXmlReader {

    /**
     * @param string $apiUrlPath Url path as it is provided in (starting by /api/...)
     * @param array $options App::request() options plus following are available:
     *      - 'apiUrlBase' (string) Defaults to App::getSetting('Eshop', 'frankana.apiUrlBase').
     *      - 'apiKey' (string) Defaults to App::getSetting('Eshop', 'frankana.apiKey').
     *      - 'avoidFields' (array) List of fields which should not be returned by reader, 
     *          e.g. array('description', 'manufacturer'). See method ::applyAvoidFields()
     *      - 'apiResponseXmlFile' (string) App root relative path to Frankana API
     *          response XML file. Used only for development and debug purposes
     *          to save the time, as the Frankana API response time is rount 20 seconds.
     *
     * @throws Exception on failure
     */
    public function __construct($apiUrlPath, $options = array()) {
        $options = array_merge(array(
            'apiResponseXmlFile' => null, // debug only
        ), $options);
        
        // get tmp XML file
        if (empty($options['apiResponseXmlFile'])) {
            App::loadLib('Eshop', 'FrankanaApiRequest');
            $ApiReader = new FrankanaApiRequest($apiUrlPath, $options);
            $xml = $ApiReader->requestApi($options);
            $this->ensureTmpDir();
            $options['apiResponseXmlFile'] = $this->tmpDir . DS . 'apiResponse.xml';
            if (!(file_put_contents(ROOT . DS . $options['apiResponseXmlFile'], $xml))) {
                throw new Exception(__e(
                    __FILE__, 'Creation of tmp file "%s" has failed.', $options['apiResponseXmlFile']
                ));
            }
        }
        
        parent::__construct($options['apiResponseXmlFile'], $options);
    }
}
