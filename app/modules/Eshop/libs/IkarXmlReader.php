<?php
App::loadLib('Eshop', 'EshopXmlReader');

/**
 * Abstract class to be used as base for IKAR catalogue, stock and annotation readers.
 */
abstract class IkarXmlReader extends EshopXmlReader {
    
    protected $recordTagName = 'titul';
    
    protected $decimalPoint = '.';
    
    protected $thousandsSeparator = '';
    
    /**
     * Timestamp the records read should start from
     *
     * @var string 
     */
    protected $startTimestamp = null;
    
    /**
     * Last read timestamp
     * 
     * @var string 
     */
    protected $lastTimestamp = null;
    
    /**
     * If provided then actual XML reader instance will use last stored timestamp 
     * under this name and at the end of processing will also store the last processed 
     * record timestamp under this name. This is a kind of timestamp namespace allowing 
     * to remember different timestamps for different situations an IkarXmlReader child class 
     * uses the timestamp in. E.g. there can be one timestamp used for import
     * of new items and another timestamp for items actualization. If not provided 
     * then no stored timestamp is considered (not loaded nor stored). If 'startTimestamp'
     * is provided then stored timestamp is not used to initialize the starting 
     * timestamp but at the end of processing is updated the stored timestamp. 
     * 
     * @var string 
     */
    protected $storedTimestampName = null;   
   
    /**
     * Opens XML file
     * 
     * @param array $options
     *      - 'startTimestamp' (string) Starting timestamp to transfer incremental import file 
     *          with changes after this timestamp. If empty value then full import 
     *          file is transfered. If not provided and 'storedTimestampName' is
     *          provided then start timestamp is initialized to last stored timestamp.
     *          To reset stored timestamp set 'startTimestamp' to NULL.
     *      - 'storedTimestampName' (string) If provided then actual XML reader instance
     *          will use last stored timestamp under this name and at the end of processing 
     *          will also store the last processed record timestamp under this name. 
     *          This is a kind of timestamp namespace allowing to remember different 
     *          timestamps for different situations an IkarXmlReader child class 
     *          uses the timestamp in. E.g. there can be one timestamp used for import
     *          of new items and another timestamp for items actualization.
     *          If not provided then no stored timestamp is considered (not loaded nor stored). 
     *          If 'startTimestamp' is provided then stored timestamp is not used to 
     *          initialize the starting timestamp but at the end of processing  
     *          is updated the stored timestamp. Defaults to NULL.
     * 
     * @throws Exception_IkarXmlReader_NoNewRecords
     * @throws Exception on invalid XML file
     */
    public function __construct($file, $options = array()) {
        $defaults = array(
            //'startTimestamp' => null, // not always must be present
            'storedTimestampName' => null,
        );
        $options = array_merge($defaults, $options);
        $this->setTimestampProperties($options);
        
        try {
            parent::__construct($file, $options);
            $this->setRecordOffsetByStartTimestamp($this->startTimestamp);
        } 
        // if storedTimestampName is specified then change Exception_XmlRecordsReader_NoRecordTag 
        // to Exception_IkarXmlReader_NoNewRecords as it is quite obvious when timestamp 
        // is used that there is no new record in catalogue from last stored timestamp 
        // and we need to distinguish this case
        catch (Exception_XmlRecordsReader_NoRecordTag $e) {
            if (!empty($options['storedTimestampName'])) {
                throw new Exception_IkarXmlReader_NoNewRecords(
                    sprintf('There are no new items in IKAR file "%s" from last read for timestamp "%s"', $file, $options['storedTimestampName']), 
                    0, 
                    $e
                );
            }
            else {
                throw $e;
            }
        }
    }
    
    public function __destruct() {
        parent::__destruct();
        if (
            !empty($this->storedTimestampName)
            && !empty($this->lastTimestamp)
        ) {
            $this->storeTimestamp($this->lastTimestamp, $this->storedTimestampName);
        }
    }
    
    /**
     * Sets properties ::$storedTimestampName and ::$startTimestamp
     * 
     * @param array $options
     * 
     * @throws Exception on invalid 'startTimestamp'
     */
    protected function setTimestampProperties($options) {
        $defaults = array(
            //'startTimestamp' => null, // not always must be present
            'storedTimestampName' => null,
        );
        $options = array_merge($defaults, $options);
        // set
        $this->storedTimestampName = $options['storedTimestampName'];
        if (array_key_exists('startTimestamp', $options)) {
            $this->startTimestamp = $options['startTimestamp'];
        }
        elseif (!empty($this->storedTimestampName)) {
            $this->startTimestamp = $this->loadStoredTimestamp($this->storedTimestampName);
        }
        // validate
        if (
            !empty($this->startTimestamp)
            && !preg_match('/^[0-9]+$/', $this->startTimestamp)
        ) {
            $this->storedTimestampName = false;
            throw new Exception(sprintf('Invalid start timestamp "%s"', $this->startTimestamp));
        }
    }
    
    /**
     * Returns app root relative filepath to file (including it) keeping last import timestamp, 
     * e.g. /tmp/ikar_catalogue_xml_reader_last_import_timestamp
     * 
     * @param string $timestampName Kind of timestamp namespace allowing
     *      to remember different timestamps for different situations the IkarXmlReader 
     *      child class uses the timestamp. E.g. there can be one timestamp 
     *      used for import of new items and another timestamp for items actualization.
     *      Defaults to NULL.
     * 
     * @return string
     */
    protected function getStoredTimestampFilePath($timestampName) {
        $name = get_class($this);
        if (!empty($timestampName)) {
            $name .= '_' . $timestampName;
        }
        $name .= '_last_import_timestamp';
        return File::getRelativePath(TMP . DS . Str::underscore($name));
    }
    
    /**
     * Returns last stored timestamp of provided name processed by IkarXmlReader child class 
     * 
     * @param string $timestampName Kind of timestamp namespace allowing
     *      to remember different timestamps for different situations the IkarXmlReader 
     *      child class uses the timestamp. E.g. there can be one timestamp 
     *      used for import of new items and another timestamp for items actualization.
     *      Defaults to NULL.
     * 
     * @return string
     */
    protected function loadStoredTimestamp($timestampName) {
        $timestampFile = ROOT . DS . $this->getStoredTimestampFilePath($timestampName);
        if (is_readable($timestampFile)) {
            return file_get_contents($timestampFile);
        }
        return null;
    }
    
    /**
     * Sets new last timestamp processed by IkarXmlReader child class to keep
     * track for future imports
     * 
     * @param string $timestamp
     * @param string $timestampName Kind of timestamp namespace allowing
     *      to remember different timestamps for different situations the IkarXmlReader 
     *      child class uses the timestamp. E.g. there can be one timestamp 
     *      used for import of new items and another timestamp for items actualization.
     *      Defaults to NULL.
     */
    protected function storeTimestamp($timestamp, $timestampName) {
        $timestampFile = ROOT . DS . $this->getStoredTimestampFilePath($timestampName);
        $timestampTmpFile = $timestampFile . 'tmp';
        file_put_contents($timestampTmpFile, (string)$timestamp);
        rename($timestampTmpFile, $timestampFile);
    }
    
    /**
     * Transfers IKAR import files
     * 
     * @param string $file One of 'catalogue', 'annotation', 'stock'
     * @param string $username
     * @param string $password
     * @param array $options
     *      - 'startTimestamp' (string) Starting timestamp to transfer incremental import file 
     *          with changes after this timestamp. If empty value then full import 
     *          file is transfered. If not provided and 'storedTimestampName' is
     *          provided then start timestamp is initialized to last stored timestamp.
     *          To reset stored timestamp set 'startTimestamp' to NULL.
     *      - 'storedTimestampName' (string) If provided then actual XML reader instance
     *          will use last stored timestamp under this name and at the end of processing 
     *          will also store the last processed record timestamp under this name. 
     *          This is a kind of timestamp namespace allowing to remember different 
     *          timestamps for different situations an IkarXmlReader child class 
     *          uses the timestamp in. E.g. there can be one timestamp used for import
     *          of new items and another timestamp for items actualization.
     *          If not provided then no stored timestamp is considered (not loaded nor stored). 
     *          If 'startTimestamp' is provided then stored timestamp is not used to 
     *          initialize the starting timestamp but at the end of processing  
     *          is updated the stored timestamp. Defaults to NULL.
     * 
     * @return string transfered filename path
     * 
     * @throws Exception
     * @throws Exception_IkarXmlReader_FileTransferDataGenerationError
     */
    protected function transferFile($file, $username, $password, $options = array()) {
        $defaults = array(
            //'startTimestamp' => null, // not always must be present
            'storedTimestampName' => null,
        );
        $options = array_merge($defaults, $options);
        $this->setTimestampProperties($options);
        $files = array('catalogue' => 'T', 'annotation' => 'A', 'stock' => 'D');
        if (!isset($files[$file])) {
            throw new Exception(sprintf('Invalid file specification "%s". Possible values are "catalogue", "annotation", "stock".', $file));
        }
        if (empty($username)) {
            throw new Exception('Missing IKAR download username');
        }
        if (empty($password)) {
            throw new Exception('Missing IKAR download password');
        }
        $this->ensureTmpDir();
        $urlBase = 'https://vo.kniznyweb.sk/b2bGate?login=' . $username 
            . '&password=' . $password;
        $tokenUrl = $urlBase . '&synctype=' . $files[$file];
        if (!empty($this->startTimestamp)) {
            $tokenUrl .= '&timestamp=' . $this->startTimestamp;
        }
        $token = App::request($tokenUrl, array(
            'getInfo' => CURLINFO_HTTP_CODE,
        ), $code);
        if (empty($token)) {
            throw new Exception(sprintf('IKAR %s file download has failed: no token has been obtained', $file));
        }
        elseif ($code === 403) {
            throw new Exception(sprintf('IKAR %s file download has failed: invalid username or password', $file));
        }
        elseif ($code !== 200) {
            throw new Exception(sprintf('IKAR %s file download has failed: token request failure', $file));
        }
        $downloadUrl = $urlBase . '&token=' . $token;
        $tries = 1000;
        while(
            $tries > 0
            && ($result = App::request($downloadUrl, array(
                'returnResponse' => false,
                'includeHeader' => false,
                'includeBody' => false,
                'followLocation' => false,
                'getInfo' => CURLINFO_HTTP_CODE,
            ), $code))
        ) {
            if ($code === 200) {
                break;
            }
            elseif ($code === 204) {
                sleep(1);
                $tries--;
                continue;
            }
            elseif ($code === 400) {
                throw new Exception(sprintf('IKAR %s file download has failed: invalid token', $file));
            }
            elseif ($code === 410) {
                throw new Exception_IkarXmlReader_FileTransferDataGenerationError(sprintf('IKAR %s file download has failed: data generation error', $file));
            }
            elseif ($code === 500) {
                throw new Exception(sprintf('IKAR %s file download has failed: API error', $file));
            }
            else {
                throw new Exception(sprintf('IKAR %s file download has failed: unknown error', $file));
            }
        }
        if (!$result) {
            throw new Exception(sprintf('IKAR %s file download has failed: download url is unavailable', $file));
        }
        elseif ($tries === 0) {
            throw new Exception(sprintf('IKAR %s file download has failed: number of tries expired', $file));
        }
        $zipFile = File::transfer(
            array($downloadUrl),
            $this->tmpDir,
            array(
                'unique' => false,
                'name' => 'ikar.zip',
            )
        );
        if (empty($zipFile)) {
            throw new Exception(sprintf('Transfer of file %s has failed', $downloadUrl));
        }
        $zipFile = $this->tmpDir . DS . $zipFile;
        $Zip = new ZipArchive();
        if ($Zip->open($zipFile) !== true) {
            throw new Exception(sprintf('Invalid zip archive %s', $zipFile));
        }        
        $file = $this->tmpDir . DS . $Zip->getNameIndex(0);
        File::unzip($zipFile, array('delete' => true));
        return $file;
    }
    
    /**
     * Returns timestamp for provided XML record
     * 
     * @param SimpleXMLElement $XmlRecord Record returned by IkarXmlReader::readRecord()
     * 
     * @return string|NULL
     */
    protected function getRecordTimestamp(SimpleXMLElement $XmlRecord) {
        $timestamp = null;
        if (!empty($XmlRecord->timestamp)) {
             $timestamp = (string)$XmlRecord->timestamp;
        }
        else {
            $attrs = $XmlRecord->attributes();
            if (!empty($attrs['timestamp'])) {
                $timestamp = (string)$attrs['timestamp'];
            }
        }
        return $timestamp;
    }
    
    /**
     * Reads next record and returns it.
     * 
     * @return null|SimpleXMLElement NULL if there is no next record. Record object 
     *      parsed by SimpleXMLElement class. The properties of returned record object 
     *      depends on XML record structure.
     */
    public function readRecord() {
        $XmlRecord = parent::readRecord();
        if (
            $XmlRecord instanceof SimpleXMLElement
            && ($timestamp = $this->getRecordTimestamp($XmlRecord))
        ) {
            $this->lastTimestamp = $timestamp;
        }
        return $XmlRecord;
    }
    
    /**
     * Sets record offset so that the first record returned by ::readRecord() will
     * have the same or greater timestamp than provided $startTimestamp.
     * 
     * @param string $startTimestamp
     */
    protected function setRecordOffsetByStartTimestamp($startTimestamp) {
        if (empty($startTimestamp)) {
            return;
        }
        if (!preg_match('/^[0-9]+$/', $startTimestamp)) {
            throw new Exception(sprintf('Invalid start timestamp "%s"', $startTimestamp));
        }
        while ($this->Xml->name === $this->recordTagName) {
            $XmlRecord = new SimpleXMLElement($this->Xml->readOuterXML());
            $timestamp = $this->getRecordTimestamp($XmlRecord);
            if (empty($timestamp) || bccomp($timestamp, $startTimestamp) >= 0) {
                break;
            }
            $this->recordOffset++;
            $this->Xml->next($this->recordTagName);
        }
    }
}
class Exception_IkarXmlReader_FileTransferDataGenerationError extends Exception {}
class Exception_IkarXmlReader_NoNewRecords extends Exception_XmlRecordsReader_NoRecordTag {}