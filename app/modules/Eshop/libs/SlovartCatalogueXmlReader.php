<?php
App::loadLib('Eshop', 'SlovartXmlReader');

/**
 * Slovart has 1 XML catalogue file which can be downloaded from https://www.slovart.sk/eshop.html?page_id=21878&b2b={KEY}
 * This link can be obtained (already with the {KEY}) in Slovart backend.
 * The link works even without the {KEY} (https://www.slovart.sk/eshop.html?page_id=21878) 
 * but the returned catalogue does not contain tags B2B_PRICE_VAT and B2B_AVAILABILITY.
 * 
 * XML FULL catalogue item tags (https://www.slovart.sk/buxus/docs/b2b/slovart_partner_export_popis.pdf ):
 * 
 *      ITEM_ID - jedinečné ID knihy v našom systéme, INTEGER
 *      PRODUCT  - celý názov knihy, STRING
 *      PRODUCTNAME - celý názov knihy, STRING
 *      STATE - Slovart stav knihy, INTEGER, hodnoty: 
 *          - 1 - novinka (pre nové tituly naskladnené po prvý raz pred maximálne 60 dňami)
 *          - 8 - pripravujeme (predpredaj, kombinuje sa s B2B_AVAILABILITY 0 a 3)
 *      AUTHORS
 *          AUTHOR
 *              ID - jedinečné ID autora, INTEGER
 *              NAME - meno a priezvisko autora, STRING
 *      PUBLISHERS
 *          PUBLISHER
 *              ID - jedinečné ID vydavateľa, INTEGER
 *              NAME - názov vydavateľa, STRING
 *      PAGES - počet strán knihy, STRING
 *      YEAR_ISSUED - rok vydania, STRING (YYYY)
 *      PROBABLE_ISSUE_DATE - prepdokladaný dátum vydania, STRING (tvar rôzny)
 *      DIMENSIONS - rozmery knihy, STRING (tvar rôzny, v mm/cm)
 *      BINDING - väzba knihy, STRING
 *      LANGUAGE - jazykové zaradenie knihy, STRING (SK - kniha v slovenskom ALEBO českom jazyku, EN - cudzojazyčná kniha, momentálne angličtina)
 *      LANGUAGES - zoznam jazykov knihy, kniha môže byť písaná viacerými jazykmi
 *          LANGUAGE
 *              ID - jednoznačný identifikátor jazyka, INTEGER
 *              NAME - názov jazyka, STRING
 *      EAN - EAN kód knihy, STRING
 *      PRODUCTNO - EAN kód knihy, STRING
 *      DESCRIPTION - krátky popis knihy, STRING (bez HTML)
 *      IMGURL - URL adresa obálky, ak ku knihe existuje, STRING
 *      URL - URL adresa knihy na stránke slovart.sk, STRING
 *      MANUFACTURER - výrobca, STRING (zatiaľ vždy 'Slovart')
 *      CATEGORYTEXT - kategórie knihy oddelené znakom '|', STRING
 *      CATEGORIES - štruktúrované informácie o kategóriách v ktorých je kniha zaradená
 *          DEPARMENT (áno, takto s preklepom, bez T)
 *              ID - jednonzačný číselný identifikátor oddelenia, INTEGER
 *              NAME - názov oddelenia, STRING
 *          THEME
 *              ID - jednonzačný číselný identifikátor témy, INTEGER
 *              NAME - názov témy, STRING
 *          EDITIONS
 *              EDITION
 *                  ID - jednonzačný číselný identifikátor edície, INTEGER
 *                  NAME - názov edície, STRING
 *      STOCK_QUANTITY - počet kusov na sklade Slovartu, INTEGER, 0 pre vypredané produkty alebo produkty v predpredaji
 *      PRICE_VAT - MOC cena s DPH, FLOAT (00.00)
 *      B2B_PRICE_VAT - B2B cena, ak sa jedná o špecializovaný B2B export, FLOAT (00.00)
 *      B2B_AVAILABILITY - B2B kód dostupnosti, ak sa jedná o špecializovaný B2B export, hodnoty: 
 *          - -1 - zakazany (položka je pre B2B zákaznika zakázaná, t.j. akoby vypredaná)
 *          - 0 - vypredaný
 *          - 1 - dostupný
 *          - 2 - dodanie oneskorené (do 21 dní)
 *          - 3 - novinka (vždy len v kombinácii so STATE 8, STATE 8 sa ešte kombinuje s B2B_AVAILABILITY 0)
 *          - 4 - na objednávku (dodanie do 6 týždňov)
 *      VAT - sadzba DPH, INTEGER
 *  
 * Contact: Lucia Domjánová, <EMAIL>, +421249201821
 *          Veronika Parobková, <EMAIL>,  +421249201821 
 */
class SlovartCatalogueXmlReader extends SlovartXmlReader {
    
    /**
     * Binding convertions (Slovart lowercased ascii binding => binding value).
     * 
     * @var array
     */
    protected  $bindingConvertions = array(
        'tvrda' => 'Pevná',
        'tvrda s prebalom' => 'Pevná',
        'makka' => 'Brožovaná',
    );  
    
    /**
     * Language codes conversion from Slovart
     * 
     * @var array 
     */
    protected $languageConversions = array(
        'cesky' => 'Český',
        'slovensky' => 'Slovenský',
    );
    
    /**
     * Year months codes conversion from Slovart
     * 
     * @var array 
     */
    protected $monthConversions = array(
        'januar' => 1,
        'februar' => 2,
        'marec' => 3,
        'april' => 4,
        'maj' => 5,
        'jun' => 6,
        'jul' => 7,
        'august' => 8,
        'september' => 9,
        'oktober' => 10,
        'november' => 11,
        'december' => 12,
    );
    
    /**
     * Actual date in format YYYY-MM-DD. Used to skip old available_from dates
     * 
     * @var string
     */
    protected $actualDate = null;
    
    /**
     * Opens XML file
     * 
     * @param array $options
     *      - 'catalogueFile' (string) App root relative path to XML file. If not provided
     *          then it is downloaded from import provider.
     *      - 'accessKey' (string) Slovart accessKey. You can get it in Slovart 
     *          B2B backend as the 'b2b=' get parameter of catalogue download URL.;
     *          Defauts to NULL.
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($options = array()) {
        $defaults = array(
            'catalogueFile' => null,
            'accessKey' => null,
        );
        $options = array_merge($defaults, $options);
        $this->actualDate = date('Y-m-d');
        
        // check for file and transfer if not provided
        if (empty($options['catalogueFile'])) {
            $options['catalogueFile'] = $this->transferFile($options['accessKey']);
        }
        
        parent::__construct($options['catalogueFile'], $options);        
    }
    
    public function validateRecord($XmlRecord) {
        if (
            isset($XmlRecord->ITEM_ID)
            && isset($XmlRecord->PRODUCT)
            && isset($XmlRecord->PRODUCTNAME)
            && isset($XmlRecord->STATE)
            //&& isset($XmlRecord->AUTHORS)
            //&& isset($XmlRecord->PUBLISHERS)
            && isset($XmlRecord->PAGES)
            && isset($XmlRecord->YEAR_ISSUED)
            && isset($XmlRecord->PROBABLE_ISSUE_DATE)
            && isset($XmlRecord->DIMENSIONS)
            && isset($XmlRecord->BINDING)
            && isset($XmlRecord->LANGUAGE)
            //&& isset($XmlRecord->LANGUAGES)
            && isset($XmlRecord->EAN)
            && isset($XmlRecord->PRODUCTNO)
            && isset($XmlRecord->DESCRIPTION)
            //&& isset($XmlRecord->IMGURL)
            && isset($XmlRecord->URL)
            && isset($XmlRecord->MANUFACTURER)
            && isset($XmlRecord->CATEGORYTEXT)
            && isset($XmlRecord->CATEGORIES)
            && isset($XmlRecord->STOCK_QUANTITY)
            && isset($XmlRecord->PRICE_VAT)
            && isset($XmlRecord->B2B_PRICE_VAT)
            && isset($XmlRecord->B2B_AVAILABILITY)
            && isset($XmlRecord->VAT)
        ) {
            return true;
        }
        return false;
    }
          
    /**
     * Reads XML record and parses it to following normalized array for files 
     * export.xml/update.xml ( is missing):
     * 
     *      array(
     *          'supplier_product_id' => ...,
     *          'name' => ...,
     *          'ean' => ...,
     *          'tax_rate' => ...,
     *          'price_taxed' => ...,           
     *          'margin_rate' => ...,           // = % rabatu = (MOC - VOC) / MOC
     *          'stock' => ...,
     *          'availability' => ...,          // 'enum_presale', 'enum_available', 'enum_soldout'
     *          'long_delivery_time' => ...,          
     *          'reprint' => null,          
     *          'available_from' => ...,
     *          'authors' => ...,               // array of authors
     *          'year' => ...,
     *          'description' => ...,
     *          'manufacturer' => ...,          // manufacturer name
     *          'image_import_source' => ...,
     *          'pages' => ...,
     *          'binding' => ...,
     *          'width' => ...,                 // mm
     *          'height' => ...,                // mm
     *          'length' => ...,                // mm
     *          'range' => ...,                 // range name
     *          'language' => ...,
     *          'categories' => ...,            // array of Slovart category ids
     *          'shipment_time_off_stock' => ...,
     *      )
     * 
     * ATTENTION: Only czech and slovak books with defined binding converions values 
     * are read from XML catalogue.
     * 
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        do {
            // get the record object
            $XmlRecord = parent::readRecord();
            // if no next then return NULL
            if (!$XmlRecord) {
                return null;
            }
            $binding = strtolower(Sanitize::nonAscii(trim((string)$XmlRecord->BINDING)));
            $languageGroup = strtolower(Sanitize::nonAscii((string)$XmlRecord->LANGUAGE));
            $departmentId = (int)$XmlRecord->CATEGORIES->DEPARMENT->ID;
        } while(
            empty($this->bindingConvertions[$binding])
            || $languageGroup !== 'sk'
            || $departmentId !== 9829 // Knihy v slovenčine a češtine
        );
        
        // convert the XML oject to normalized array
        // - ean and name are available in all 3 xml files
        $record = array();
        $record['supplier_product_id'] = (int)$XmlRecord->ITEM_ID;
        $record['name'] = trim((string)$XmlRecord->PRODUCT); // or PRODUCTNAME
        $record['ean'] = trim((string)$XmlRecord->EAN); // or PRODUCTNO
        $record['tax_rate'] = (int)$XmlRecord->VAT;
        // price
        $record['price_taxed'] = (float)$this->normalizeNumber((string)$XmlRecord->PRICE_VAT);
        $b2bPriceTaxed = (float)$this->normalizeNumber((string)$XmlRecord->B2B_PRICE_VAT);
        $record['margin_rate'] = 100 * round(($record['price_taxed'] - $b2bPriceTaxed) / $record['price_taxed'], 2);
        // stock, availability, available_from
        $record['stock'] = (int)$XmlRecord->STOCK_QUANTITY;
        $state = (int)$XmlRecord->STATE;
        $avalability = (int)$XmlRecord->B2B_AVAILABILITY;
        $record['long_delivery_time'] = false;
        $record['shipment_time_off_stock'] = 5;
        if (
            $avalability <= 0 // -1, 0
            || 
            empty($record['stock'])
            && $state !== 8
        ) {
            $record['availability'] = 'enum_soldout';
        }
        elseif (
            $avalability === 2
            || $avalability === 4
        ) {
            $record['availability'] = 'enum_available';
            $record['long_delivery_time'] = true;
            $record['shipment_time_off_stock'] = 21;
            if ($avalability === 4) {
                $record['shipment_time_off_stock'] = 42;
            }
        }
        elseif (
            $state === 8
            && $avalability > 0 // 3
        ) {
            $record['availability'] = 'enum_presale';
        }
        else { // 1
            $record['availability'] = 'enum_available';
        }
        $record['reprint'] = null;
        $record['available_from'] = null;
        $dateMatch = null;
        $normalizedDate = null;
        if (
            ($date = trim((string)$XmlRecord->PROBABLE_ISSUE_DATE))
            && (
                preg_match(
                    '/^(?:(\d{1,2})\.?\s*)?(január|február|marec|apríl|máj|jún|júl|august|september|október|november|december)\s*(\d{4})$/i', 
                    $date, 
                    $dateMatch
                )
                || ($normalizedDate = Date::format($date, 'Y-m-d'))
            )
        ) {
            if ($dateMatch) {
                $month = strtolower(Sanitize::nonAscii($dateMatch[2]));
                if (!empty($this->monthConversions[$month])) {
                    $month = $this->monthConversions[$month];
                    $year = (int)$dateMatch[3];
                    if (!empty($dateMatch[1])) {
                        $day = $dateMatch[1];
                    }
                    else {
                        $month = ($month + 1) % 12;
                        if ($month === 1) {
                            $year++;
                        }
                        $day = '1';
                    }
                    $month = str_pad($month, 2, '0', STR_PAD_LEFT);
                    $day = str_pad($day, 2, '0', STR_PAD_LEFT);
                    $normalizedDate = $year . '-' . $month . '-' . $day;
                }
            }
            if (
                $normalizedDate
                && $normalizedDate > $this->actualDate
            ) {
                $record['available_from'] = $normalizedDate;
                $record['availability'] = 'enum_presale';
            }
        }
        // authors
        $authorArray = array();
        if (!empty($XmlRecord->AUTHORS)) {
            $authors = $XmlRecord->AUTHORS;
            foreach($authors->AUTHOR as $author) {
                if (!empty($author->NAME)) {
                    array_push($authorArray, (string)$author->NAME);
                }
            }
        }
        $record['authors'] = array_map('trim', $authorArray);
        $record['year'] = (int)$XmlRecord->YEAR_ISSUED;
        if (
            empty($record['year']) 
            && !empty($normalizedDate)
        ) {
            $record['year'] = (int)Date::format($normalizedDate, 'Y');
        }
        $record['description'] = trim((string)$XmlRecord->DESCRIPTION);
        $record['manufacturer'] = trim((string)$XmlRecord->MANUFACTURER);
        if (!empty($XmlRecord->PUBLISHERS)) {
            $publishers = $XmlRecord->PUBLISHERS;
            foreach($publishers->PUBLISHER as $publisher) {
                if (!empty($publisher->NAME)) {
                    $record['manufacturer'] = trim((string)$publisher->NAME);
                    // take the first publisher name only
                    break;
                }
            }
        }
        $record['image_import_source'] = null;
        if (!empty($XmlRecord->IMGURL)) {
            $record['image_import_source'] = trim((string)$XmlRecord->IMGURL);
        }
        $record['pages'] = trim((string)$XmlRecord->PAGES);
        $record['binding'] = $this->bindingConvertions[$binding];
        $record['width'] = null;
        $record['height'] = null;
        $record['length'] = null;
        if(($dimensions = trim((string)$XmlRecord->DIMENSIONS))) {
            $rate = strpos($dimensions, 'cm') !== false ? 10 : 1;
            $dimensions = trim($dimensions, ' cm');
            $dimensions = explode('x', $dimensions);
            if (!empty($dimensions[1])) {
                $record['width'] = (float)trim($dimensions[0]) * $rate;
                $record['height'] = (float)trim($dimensions[1]) * $rate;
                if (!empty($dimensions[2])) {
                    $record['length'] = (float)trim($dimensions[2]) * $rate;
                }
            }
        }
        $record['range'] = null;
        if (!empty($XmlRecord->CATEGORIES->EDITIONS)) {            
            $ranges = $XmlRecord->CATEGORIES->EDITIONS;
            foreach($ranges->EDITION as $range) {
                if (!empty($range->NAME)) {
                    $record['range'] = trim((string)$range->NAME);
                    // take the first range name only
                    break;
                }
            }
        }
        $bookLanguages = array();
        if (!empty($XmlRecord->LANGUAGES)) {
            $languages = $XmlRecord->LANGUAGES;
            foreach($languages->LANGUAGE as $language) {
                if (!empty($language->NAME)) {
                    $language = strtolower(Sanitize::nonAscii(trim((string)$language->NAME)));
                    $bookLanguages[$language] = true;
                }
            }
        }
        $record['language'] = null;
        if (isset($bookLanguages['slovensky'])) {
            $record['language'] = $this->languageConversions['slovensky'];
        }
        elseif (isset($bookLanguages['cesky'])) {
            $record['language'] = $this->languageConversions['cesky'];
        }
//        $record['debug_languages'] = array_keys($bookLanguages); //debug
//        $record['debug_language_group'] = $languageGroup; // debug
        $record['categories'] = array();
        if (
            !empty($XmlRecord->CATEGORIES->THEME->ID)
            && (int)$XmlRecord->CATEGORIES->THEME->ID !== 67190 // Mimo kategórie
        ) {
            $record['categories'] = array((int)$XmlRecord->CATEGORIES->THEME->ID);
        }
//        $record['debug_department_id'] = (int)$XmlRecord->CATEGORIES->DEPARMENT->ID; //debug
//        $record['debug_department_name'] = trim((string)$XmlRecord->CATEGORIES->DEPARMENT->NAME); //debug
//        $record['debug_theme_id'] = (int)$XmlRecord->CATEGORIES->THEME->ID; //debug
//        $record['debug_theme_name'] = trim((string)$XmlRecord->CATEGORIES->THEME->NAME); //debug

        return $this->applyAvoidFields($record);
    }
}