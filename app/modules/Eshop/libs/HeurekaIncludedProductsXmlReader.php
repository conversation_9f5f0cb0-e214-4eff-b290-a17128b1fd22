<?php
App::loadLib('Eshop', 'EshopXmlReader');

/**
 * Reader of XML file of products which are included/excluded from displaying on Heureka.sk.
 * This file can be downloaded in 'Administrácia E-shopu' > 'Prehľad zaradených produktov'
 */
class HeurekaIncludedProductsXmlReader extends EshopXmlReader {
    
    protected $recordTagName = 'ITEM';
    
    protected $decimalPoint = '.';
    
    protected $thousandsSeparator = '';
    
    public function validateRecord($XmlRecord) {
        if (
            isset($XmlRecord->ITEM_ID)
            && isset($XmlRecord->PRODUCTNAME)
            && isset($XmlRecord->URL)
            && isset($XmlRecord->PRICE)
        ) {
            return true;
        }
        return false;
    }
          
    /**
     * Reads XML record and parses it to following normalized array:
     * 
     *      array(
     *          'id' => ...,
     *          'name' => ...,
     *          'url' => ...,
     *          'price_taxed' => ...,           
     *      )
     * 
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        $record = array();
        $record['id'] = (int)trim((string)$XmlRecord->ITEM_ID);
        $record['name'] = trim((string)$XmlRecord->PRODUCTNAME);
        $record['url'] = trim((string)$XmlRecord->URL);
        $record['price_taxed'] = (float)$this->normalizeNumber((string)$XmlRecord->PRICE_VAT);

        return $this->applyAvoidFields($record);
    }

}
