<?php
App::loadLib('Eshop', 'EshopCsvReader');

/**
 * Internal catalogue CSV reader.
 * 
 * File PRODUKTY_???.csv contains following columns: 
 * 
 *  - 0: CISLO (= interný kód)
 *  - 1: NAZOV
 *  - 2: POZNAMKA
 *  - 3: PREDAJNÁ CENA s DPH
 *  - 4: JM
 *  - 5: HMOTNOSTJM
 *  - 6: DPH - kód 1,2,3
 *  - 7: SKUPINA_KOD
 *  - 8: SKUPINA_NAZOV
 *  - 9: KATALOGOVE_CISLO
 *  - 10: JKPOV
 *  - 11: SKARAB
 * 
 */
class InternalCatalogueCsvReader extends EshopCsvReader {
    
    /**
     * Reads existing products stock amount exported from MRP as csv file
     * 
     * @param string $file
     * @param array $options Options of Csv() class
     */    
    public function __construct($file, $options = array()) {
        if (Validate::uploadData($file, array('notEmpty' => true))) {
            $file = $this->uploadFile($file);
        }
        parent::__construct($file, array_merge($options, array(
            'mode' => 'r',
            'delimiter' => chr(9),
            'enclosure' => '"',
            'hasHeaders' => false,
            'encoding' => 'CP1250',
            'decimalPoint' => ',',
            'thousandsSeparator' => '',
            'recordFields' => array(
                'code' => array('column' => 9, 'convertEncoding' => true, 'trim' => true),
                'name' => array('column' => 1, 'convertEncoding' => true, 'trim' => true),
                'description' => array('column' => 2, 'convertEncoding' => true, 'trim' => true),
                'price_taxed' => array('column' => 3, 'convertEncoding' => true, 'convertType' => 'float'),
                'tax_rate' => array('column' => 6, 'convertEncoding' => true, 'trim' => true, 'convertType' => 'int', 'convert' => function($value) {  
                    if ($value === 1) {
                        return 20;
                    }
                    elseif ($value === 2) {
                        return 10;
                    }
                    elseif ($value === 1) {
                        return 0;
                    }
                    else {
                        return 20;
                        //return '???' . $value; //debug
                    }
                }),
                'units' => array('column' => 4, 'convertEncoding' => true, 'trim' => true, 'convert' => function($value) {  
                    $value = strtolower($value);
                    if ($value === 'ks') {
                        return 'enum_piece';
                    }
                    elseif ($value === 'm') {
                        return 'enum_meter';
                    }
                    elseif ($value === 'kg') {
                        return 'enum_kilogram';
                    }
                    else {
                        return null;
                        //return '???' . $value; //debug
                    }
                }),
//                'stock' => array(...),
//                'HMOTNOSTJM' => array('column' => 5, 'convertEncoding' => true, 'trim' => true), //debug
//                'SKUPINA_KOD' => array('column' => 7, 'convertEncoding' => true, 'trim' => true), //debug
//                'SKUPINA_NAZOV' => array('column' => 8, 'convertEncoding' => true, 'trim' => true), //debug
            )
        )));
    }
        
    /**
     * Reads the next csv record from file. Info line, header row and empty lines are omitted.
     * Returned record has following items:
     * 
     *      array(
     *          'code' => ...,
     *          'name' => ...,
     *          'description' => ...,
     *          'price_taxed' => ...,
     *          'tax_rate' => ...,
     *          'units' => ...,
     *      )
     * 
     * @return bool|array Next record array. NULL if the end of csv file is reached.
     */
    public function readRecord() {
        return parent::readRecord();
    }
}
