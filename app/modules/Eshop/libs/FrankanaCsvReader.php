<?php
App::loadLib('Eshop', 'EshopCsvReader');

/**
 * Abstract class to be used as base for FRANKANA catalogue and stock (availability 
 * + price) reader.
 * 
 * Contact: <PERSON>, IT department, <EMAIL>, +49 9339 9713-663
 * Documentation: http://projects.run.sk/projects/219/tickets/48?page=1#comment58037
 */
abstract class FrankanaCsvReader extends EshopCsvReader {
    
    /**
     * Transfers FRANKANA import files
     * 
     * @param string $file One of 'catalogue', 'stock'
     * @param string $username
     * @param string $password
     * 
     * @return string App root relative transferred filename path
     * 
     * @throws Exception
     */
    protected function transferFile($file, $username, $password) {
        $files = array('catalogue' => true, 'stock' => true);
        if (empty($files[$file])) {
            throw new Exception(sprintf('Invalid file specification: %s. Possible values are "catalogue" or "stock"', $file));
        }
        if (empty($username)) {
            throw new Exception('Missing FRANKANA download username');
        }
        if (empty($password)) {
            throw new Exception('Missing FRANKANA download password');
        }
        
        // do SFTP connection using SSH
        $url = '212.185.120.138';
        $port = '22';
        if (!($connection = ssh2_connect($url, $port))) {
            throw new Exception(sprintf('Connection to FRANKANA server %s:%s has failed', $url, $port));
        }
        if (!ssh2_auth_password($connection, $username, $password)) {
            throw new Exception(sprintf('Authentication to FRANKANA server %s:%s has failed', $url, $port));
        }
        if (!($sftp = ssh2_sftp($connection))) {
            throw new Exception(sprintf('SFTP initialization to FRANKANA server %s:%s has failed', $url, $port));
        }

        // copy the file from FTP to reader tmp dir
        $this->ensureTmpDir();
        if ($file === 'catalogue') {
            $remoteDir = '/Oeffentlicher_Bereich/e_commerce';
            $fileContent = file_get_contents(
                'ssh2.sftp://' . intval($sftp) . ssh2_sftp_realpath($sftp, $remoteDir) . '/export-distributor.csv'
            );
            //$fileContent = mb_convert_encoding($fileContent, 'UTF-8', 'UTF-16LE');
            $transferredFile = $this->tmpDir . DS . 'export-distributor.csv';
            file_put_contents(ROOT . $transferredFile, $fileContent);
        }
        
        else { // stock
            $remoteDir = 'Oeffentlicher_Bereich/Bestandsabfrage/Frankana';
            $fileContent = file_get_contents(
                'ssh2.sftp://' . intval($sftp) . ssh2_sftp_realpath($sftp, $remoteDir) . '/frankana_lager.txt'
            );
            // current encoding of file is UTF-8
            //$fileContent = mb_convert_encoding($fileContent, 'UTF-8', 'UTF-16LE');
            $transferredFile = $this->tmpDir . DS . 'frankana_lager.txt';
            file_put_contents(ROOT . DS . $transferredFile, $fileContent);
        }
        return $transferredFile;
    }    
}