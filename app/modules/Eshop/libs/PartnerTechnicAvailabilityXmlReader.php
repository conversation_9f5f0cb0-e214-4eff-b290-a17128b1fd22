<?php
App::loadLib('Eshop', 'PartnerTechnicXmlReader');

/**
 * Reads stock catalogue of Partner technic
 * 
 * For more details see phpDoc of PartnerTechnicXmlReader class
 */
class PartnerTechnicAvailabilityXmlReader extends PartnerTechnicXmlReader {
    
    protected $today = null;
    
    protected $loadPrice = false;
    
    /**
     * Opens XML file
     * 
     * @param array $options
     *      - 'stockFile' (string) App root relative path to stock XML file. If not provided
     *          then it is downloaded from import provider.
     *      - 'loadPrice' (bool) If TRUE then also price fields are loaded and populated
     *          in records returned by ::readRecord() method. Defauts to FALSE.
     *      - 'priceFile' (string) App root relative path to price XML file. If not provided
     *          then it is downloaded from import provider. Considered only if 'loadPrice' is TRUE.
     *      - 'accessKey' (string) Partner technic accessKey. Must be provided if 
     *          'stockFile' is not provided or is empty. Defauts to NULL.
     *      - 'processEans' (array&) List of eans to be allowed when loading (caching)
     *          price records. This is just question of memory save (= optimization).
     *          If not provided then all price records are loaded. To make it really 
     *          optimal provide this option by reference: 'processEans' => &$eans.
     *          Defaults to empty array(). 
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($options = array()) {
        $defaults = array(
            'stockFile' => null,
            'loadPrice' => $this->loadPrice,
            'priceFile' => null,
            'accessKey' => null,
            'processEans' => array(),
        );
        $options = array_merge($defaults, $options);
        
        $this->loadPrice = $options['loadPrice'];
        
        // check for file and transfer if not provided
        if (empty($options['stockFile'])) {
            $options['stockFile'] = $this->transferFile('stock', $options['accessKey']);
        }
        
        $this->today = date('Y-m-d');
        
        if ($this->loadPrice) {
            if (empty($options['priceFile'])) {
                $options['priceFile'] = $this->transferFile('price', $options['accessKey']);
            }
            $this->loadPriceRecords($options);
        }
        
        parent::__construct($options['stockFile'], $options);
    }
    
    public function validateRecord($XmlRecord) {
        if (
            isset($XmlRecord->EAN)
            && isset($XmlRecord->NAZOV)
            && isset($XmlRecord->DOSTUPNOST)
            && isset($XmlRecord->OCAKAVANE)
            && isset($XmlRecord->VYPREDANE)
        ) {
            return true;
        }
        return false;
    }
          
    /**
     * Reads XML record and parses it to following normalized array:
     * 
     *      array(
     *          'ean' => ...,
     *          'stock' => ...,                 // (int)|NULL
     *          'availability' => ...,          // 'enum_presale', 'enum_available', 'enum_soldout'
     *          'available_from' => ...,
     *          // ATTENTION: these fields are present only if reader instance 
     *          // has been created with 'loadPrice' => TRUE
     *          'price_taxed' => ...,           // (float)|NULL
     *          'tax_rate' => ...,              // (int)|NULL
     *          'margin_rate' => ...,           // (float)|NULL
     *      )
     * 
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        // - ean and name are available in all 3 xml files
        $record = array();
        $record['ean'] = trim((string)$XmlRecord->EAN);
        // supplier stock
        $record['stock'] = (int)$XmlRecord->DOSTUPNOST;
        if ($record['stock'] < 0) {
            $record['stock'] = 0;
        }
        
        // availability
        $record['availability'] = 'enum_available';
        $record['available_from'] = null;
        //$record['long_delivery_time'] = false;
        if ((string)$XmlRecord->VYPREDANE === 'Áno') {
            if (
                (int)$XmlRecord->DOSTUPNOST === -1
                || (int)$XmlRecord->DOSTUPNOST === -2
            ) {
                $record['availability'] = 'enum_soldout';
            }
            elseif ((int)$XmlRecord->DOSTUPNOST === 0) {
                //rblb//$record['long_delivery_time'] = true;
                $record['availability'] = 'enum_soldout';
            }
        }
        elseif (
            ($availableFrom = (string)$XmlRecord->OCAKAVANE)
            && ($availableFrom = Date::format($availableFrom))
            && $availableFrom > $this->today
            && (int)$XmlRecord->DOSTUPNOST === -2
        ) {
            $record['availability'] = 'enum_presale';
            $record['available_from'] = $availableFrom;
        }
        // price_taxed, tax_rate and margin_rate
        if ($this->loadPrice) {            
            $record['price_taxed'] = null;
            $record['tax_rate'] = null;
            $record['margin_rate'] = null;
            if (isset($this->priceRecords[$record['ean']])) {
                $record = array_merge($record, $this->priceRecords[$record['ean']]);
            }
        }
        
        return $this->applyAvoidFields($record);
    }
}
