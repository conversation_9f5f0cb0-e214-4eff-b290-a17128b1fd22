<?php
App::loadLib('Eshop', 'IkarXmlReader');

/**
 * Reads Ikar tituly.mxl file.
 * 
 * Zipped file can be downloaded like this: 
 *  1] Call URL: https://vo.kniznyweb.sk/b2bGate?login={username}&password={pwd}&synctype=T[&timestamp={timestamp}]
 *  This URL will return a token, e.g. bYRixCHO, which will be used to obtain catalogue file in step 2.
 * 
 *  2] Call URL: https://vo.kniznyweb.sk/b2bGate?login={username}&password={pwd}&token={token}
 *  This URL will return following HTTP codes with following meaning:
 *      200 - URL pro stažení ZIP souboru s daty (Data jsou připravena a je možné si je z vrácené URL stahnout)
 *      204 - Warning: still in progress (Data se stále generují, požadavek je potřeba opakovat později)
 *      400 - Token not found (Chybný název tokenu v požadavku)
 *      410 - Text chybového hlášení (Při generování dat došlo k chybě)
 *      500 - Text chybového hlášení (Došlo k chybě v API rozhraní)
 * 
 * ATTENTION: The same products have different timestamps in tituly.xml, dostupnost.xml
 * and anotace.xml!
 * 
 * NOTE: To extract list of categories from Ciselniky.xml (synctype=C) use following 
 * find/replace:
 * <zanr id="([^"]+)">(.+?)</zanr>
 * '$1' => ... // $2 \n
 * 
 * E.g.:
 * https://vo.kniznyweb.sk/b2bGate?login=434888&password=Zjavkova&synctype=T
 * https://vo.kniznyweb.sk/b2bGate?login=434888&password=Zjavkova&token={vygenerovanyRetazecVratenyPrvymLinkom} 
 */
class IkarCatalogueXmlReader extends IkarXmlReader {
        
    protected $considerOnlyFirstCategory = true;
    
    protected $todayDate = null;
       
    protected $languageConversions = array(
        'ALB' => 'Albánsky',
        'ARA' => 'Arabský',
        'ARM' => 'Arménsky',
        'BUL' => 'Bulharský',
        'CAT' => 'Katalánsky',
        'CZE' => 'Český',
        'DAN' => 'Dánsky',
        'DUT' => 'Holandský',
        'ENG' => 'Anglický',
        'EST' => 'Estónsky',
        'FIN' => 'Fínsky',
        'FRE' => 'Francúzsky',
        'GEO' => 'Gruzínsky',
        'GER' => 'Nemecký',
        'GRE' => 'Grécky',
        'HEB' => 'Hebrejský',
        'HRV' => 'Chorvátsky',
        'HUN' => 'Maďarský',
        'CHI' => 'Čínsky',
        'ICE' => 'Islandský',
        'IND' => 'Indonézsky',
        'ITA' => 'Taliánsky',
        'JPN' => 'Japonský',
        'KOR' => 'Kórejský',
        'LAT' => 'Latinský',
        'LAV' => 'Lotyšský',
        'LIT' => 'Litovský',
        'MON' => 'Mongolský',
        'NOR' => 'Nórsky',
        'POL' => 'Poľský',
        'POR' => 'Portugalský',
        'ROM' => 'Rómsky',
        'RUM' => 'Rumunský',
        'RUS' => 'Ruský',
        'SLO' => 'Slovenský',
        'SLV' => 'Slovinský',
        'SPA' => 'Španielský',
        'SRP' => 'Srbský',
        'SWE' => 'Švédsky',
        'THA' => 'Thajský',
        'TUR' => 'Turecký',
        'UKR' => 'Ukrajinský',
        'VIE' => 'Vietnamský',        
    );
    
    /**
     * NOTE: Find/replace, to get list:
     * find: \d{2} => array\('sortiment' => ('[^']+').*
     * replace: $1
     */
    protected $productTypeConversions = array(
        11 => array('sortiment' => 'knihy-viazane', 'binding' => 'Pevná'),
        12 => array('sortiment' => 'leporelo', 'binding' => 'Leporelo'),
        13 => array('sortiment' => 'paperback', 'binding' => 'Brožovaná'),
        16 => array('sortiment' => 'kruzkova-vazba', 'binding' => 'Špirálová'),
        17 => array('sortiment' => 'flexo', 'binding' => 'Flexi'),
        18 => array('sortiment' => 'spiralova-vazba', 'binding' => 'Špirálová'),
        24 => array('sortiment' => 'mc', 'binding' => null),
        25 => array('sortiment' => 'cd', 'binding' => null),
        70 => array('sortiment' => 'hry', 'binding' => null),
        71 => array('sortiment' => 'hracky', 'binding' => null),
        72 => array('sortiment' => 'karty', 'binding' => null),
        73 => array('sortiment' => 'puzzle', 'binding' => null),
        75 => array('sortiment' => 'papierensky-tovar', 'binding' => null),
        77 => array('sortiment' => 'casopisy-noviny', 'binding' => null),
        78 => array('sortiment' => 'plagaty-reprodukcie', 'binding' => null),
        79 => array('sortiment' => 'esotericke-predmety', 'binding' => null),
        81 => array('sortiment' => 'ostatne', 'binding' => null),
        84 => array('sortiment' => 'video-VHS', 'binding' => null),
        85 => array('sortiment' => 'cd-rom', 'binding' => null),
        86 => array('sortiment' => 'dvd', 'binding' => null),
        87 => array('sortiment' => 'pc-hry', 'binding' => null),
        99 => array('sortiment' => 'neznamy', 'binding' => null),
    );
    
    /**
     * List of availability records stored like {supplierId} => {arrayOfAvailabilirtyPriceFields}
     * where {arrayOfAvailabilirtyPriceFields} contains 'availability', 'long_delivery_time'
     * and 'reprint' items.
     * 
     * @var array 
     */
    protected $availabilityRecords = array();
    
    /**
     * Opens XML file
     * 
     * @param array $options
     *      - 'catalogueFile' (string) App root relative path to XML file. If not provided
     *          then it is downloaded from import provider.
     *      - 'username' (string) Must be provided if 'catalogueFile' is not provided
     *      - 'password' (string) Must be provided if 'catalogueFile' is not provided
     *      - 'startTimestamp' (string) Starting timestamp to transfer incremental import file 
     *          with changes after this timestamp. If empty value then full import 
     *          file is transfered. If not provided and 'storedTimestampName' is
     *          provided then start timestamp is initialized to last stored timestamp.
     *          To reset stored timestamp set 'startTimestamp' to NULL.
     *      - 'storedTimestampName' (string) If provided then actual XML reader instance
     *          will use last stored timestamp under this name and at the end of processing 
     *          will also store the last processed record timestamp under this name. 
     *          This is a kind of timestamp namespace allowing to remember different 
     *          timestamps for different situations an IkarXmlReader child class 
     *          uses the timestamp in. E.g. there can be one timestamp used for import
     *          of new items and another timestamp for items actualization.
     *          If not provided then no stored timestamp is considered (not loaded nor stored). 
     *          If 'startTimestamp' is provided then stored timestamp is not used to 
     *          initialize the starting timestamp but at the end of processing  
     *          is updated the stored timestamp. Defaults to NULL.
     *      - 'considerOnlyFirstCategory' (bool) If TRUE then only first of product categories
     *      - 'processEans' (array&) List of eans to be allowed when loading (caching)
     *          availability records. This is just question of memory save (= optimization).
     *          If not provided then all availability records are loaded. To make it really 
     *          optimal provide this option by reference: 'processEans' => &$eans.
     *          Defaults to empty array(). 
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($options = array()) {
        $defaults = array(
            'catalogueFile' => null,
            'stockFile' => null,
            'annotationFile' => null,
            'username' => null,
            'password' => null,
            //'startTimestamp' => null, // not always must be present
            'storedTimestampName' => null,
            'considerOnlyFirstCategory' => $this->considerOnlyFirstCategory,
            'processEans' => array(),
        );
        $options = array_merge($defaults, $options);
        
        $this->considerOnlyFirstCategory = (bool)$options['considerOnlyFirstCategory'];
        
        $this->todayDate = date('Y-m-d');
        
        // check for file and transfer if not provided
        if (empty($options['catalogueFile'])) {
            $options['catalogueFile'] = $this->transferFile('catalogue', $options['username'], $options['password'], $options);
        }
        
        parent::__construct($options['catalogueFile'], $options);     
        
        $this->loadAvailabilityRecords($options);
    }
    
    public function validateRecord($XmlRecord) {
        // commented items are not always present in XML record
        if (
            isset($XmlRecord->id)
            //&& isset($XmlRecord->ean)
            && isset($XmlRecord->nazev)
            //&& isset($XmlRecord->isbn)
            //&& isset($XmlRecord->autori)
            //&& isset($XmlRecord->vydani)
            //&& isset($XmlRecord->nakladatel)
            //&& isset($XmlRecord->znacka)
            && isset($XmlRecord->dph)
            && isset($XmlRecord->{'dpc-aktualni'})
            && isset($XmlRecord->{'dpc-puvodni'})
            && isset($XmlRecord->rabat)
            //&& isset($XmlRecord->zanry)
            //&& isset($XmlRecord->{'pocet-stran'})
            //&& isset($XmlRecord->rozmer1)   // the biggest dimension [mm]
            //&& isset($XmlRecord->rozmer2)   // middle dimension [mm]
            //&& isset($XmlRecord->rozmer3)   // the smallest dimension [mm]
            //&& isset($XmlRecord->hmotnost)  // [g]
            && isset($XmlRecord->{'typ-produktu'})
            && isset($XmlRecord->timestamp)
        ) {
            return true;
        }
        return false;
    }
    
    /**
     * Loads $availabilityRecords property as list of pairs {supplierId} => {arrayOfAvailabilirtyPriceFields}
     * where {arrayOfAvailabilirtyPriceFields} contains 'availability', 'long_delivery_time'
     * and 'reprint' items.
     * 
     * ATTENTION: Call this method in constructor only after parent::__construct() is called.
     * 
     * @param array $options Following are available:
     *      - 'stockFile' (string) App root relative path to XML file. If not provided
     *          then it is downloaded from import provider.
     *      - 'username' (string) Must be provided if 'stockFile' is not provided
     *      - 'password' (string) Must be provided if 'stockFile' is not provided
     *      - 'processEans' (array&) List of eans to be allowed when loading (caching)
     *          availability records. This is just question of memory save (= optimization).
     *          If not provided then all availability records are loaded. To make it really 
     *          optimal provide this option by reference: 'processEans' => &$eans.
     *          Defaults to empty array(). 
     */
    protected function loadAvailabilityRecords($options = array()) {
        $defaults = array(
            'stockFile' => null,
            'username' => null,
            'password' => null,
            'processEans' => array(),
        );
        $options = array_merge($defaults, $options);
        $options['startTimestamp'] = null; // load always all availabilities
        $options['storedTimestampName'] = null; // and do not keep any history of timestamps
        App::loadLib('Eshop', 'IkarAvailabilityXmlReader');
        $Reader = new IkarAvailabilityXmlReader($options);
        while($record = $Reader->readRecord()) {
            if (
                !empty($options['processEans'])
                && !empty($record['ean'])
                && empty($options['processEans'][$record['ean']])
            ) {
                continue;
            }
            $key = $record['supplier_product_id'];
            unset($record['supplier_product_id']);
            unset($record['ean']);
            $this->availabilityRecords[$key] = $record;
        }
    }
          
    /**
     * Reads XML record and parses it to following normalized array for files tituly.xml:
     * 
     *      array(
     *          'supplier_product_id' => ...,
     *          'ean' => ...,
     *          'name' => ...,
     *          'isbn' => ...,
     *          'authors' => ...,               // array of authors
     *          'edition' => ...,               // 1., 2.
     *          'manufacturer' => ...,          // manufacturer name
     *          'tax_rate' => ...,
     *          'price_taxed' => ...,           
     *          'margin_rate' => ...,           // = % rabatu = (MOC - VOC) / MOC
     *          'categories' => ...,            // array of IKAR genre ids
     *          'language' => ...,
     *          'pages' => ...,
     *          'availability' => ...,          // 'enum_presale', 'enum_soldout'
     *          'long_delivery_time' => ...,          
     *          'reprint' => ...,            
     *          'available_from' => ...,
     *          'year' => ...,
     *          'height' => ...,                // [mm]
     *          'width' => ...,                 // [mm]
     *          'length' => ...,                // [mm]
     *          'weight' => ...,                // [kg]
     *          'image_import_source' => ...,
     *          'sortiment' => ...,                  // 'knihy-viazane', 'leporelo', 'cd', ...
     *          'binding' => ...,
     *      )
     * 
     * ATTENTION: There is no description contained in output record. Use IkarDescriptionXmlReader()
     * to import descriptions
     * 
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        // - ean and name are available in all 3 xml files
        $record = array();
        $record['supplier_product_id'] = trim((string)$XmlRecord->id);
        $record['ean'] = (!empty($XmlRecord->ean)) ? trim((string)$XmlRecord->ean) : null;
        /*///debug>
        if (in_array($record['ean'], array('9788097308766', '9788081641985'))) {
            $x = 1;
        }
        /*///<debug
        $record['name'] = trim((string)$XmlRecord->nazev);
        $record['isbn'] = (!empty($XmlRecord->isbn)) ? trim((string)$XmlRecord->isbn) : null; 
        // authors
        $authorArray = array();
        if (isset($XmlRecord->autori->autor)) {
            $authors = $XmlRecord->autori;
            foreach($authors->autor as $author) {
                $attrs = $author->attributes();
                if (
                    empty($attrs['id'])
                    || !(string)$attrs['id']
                ) {
                    continue;
                }
                if (
                    !empty($attrs['jmeno'])
                    && !empty($attrs['prijmeni'])
                ) {
                    $firstname = trim(preg_replace('/\s+/', ' ', str_replace('.', '. ', (string)$attrs['jmeno'])));
                    $lastname = trim(preg_replace('/\s+/', ' ', str_replace('.', '. ', (string)$attrs['prijmeni'])));
                    $lastNameParts = explode(',', $lastname);
                    $authorName = '';
                    foreach ($lastNameParts as $i => &$lastNamePart) {
                        $lastNamePart = trim($lastNamePart);
                        $lastNameParts2 = explode(' ', $lastNamePart);
                        if (count($lastNameParts2) === 1) {
                            if ($i === 0) {
                                $authorName = $firstname . ' ' . $lastname;
                                break;
                            }
                            else {
                                $authorName = $lastname . ' ' . $firstname;
                                break;                                
                            }
                        }
                    }
                    if (empty($authorName)) {
                        $authorName = $firstname . ' ' . $lastname;
                    }
                }
                else { 
                    $authorName = (string)$author;
                }
                $authorName = trim(preg_replace('/\s+/', ' ', str_replace('.', '. ', $authorName)));
                $tmpArray = explode(',', $authorName);
                foreach ($tmpArray as $value) {
                    if ($value !== 'autor neuvedený') {
                        array_push($authorArray, $value);
                    }
                }
            }
        }
        $record['authors'] = array_filter(array_map('trim', $authorArray));
        $record['edition'] = (!empty($XmlRecord->vydani)) ? trim((string)$XmlRecord->vydani) : null; 
        $record['manufacturer'] = (!empty($XmlRecord->znacka)) ? trim((string)$XmlRecord->znacka) : null;
        $record['tax_rate'] = (int)trim((string)$XmlRecord->dph);
        // price
        $record['price_taxed'] = (float)$this->normalizeNumber((string)$XmlRecord->{'dpc-puvodni'});
        // margin rate
        $record['margin_rate'] = (float)$this->normalizeNumber((string)$XmlRecord->rabat);
        // categories
        $record['categories'] = array();
        if (isset($XmlRecord->zanry->zanr)) {
            $categories = $XmlRecord->zanry;
            foreach($categories->zanr as $category) {
                $attrs = $category->attributes();
                if (empty($attrs['id'])) {
                    continue;
                }
                $record['categories'][] = (string)$attrs['id'];
                if ($this->considerOnlyFirstCategory) {
                    break;
                }
            }
        }
        // language
        $record['language'] = null;
        if (isset($XmlRecord->jazyky->jazyk)) {
            $languages = $XmlRecord->jazyky;
            foreach($languages->jazyk as $language) {
                $attrs = $language->attributes();
                if (
                    empty($attrs['id'])
                    || (
                        ($languageId = (string)$attrs['id'])
                        && empty($this->languageConversions[$languageId])
                    )
                ) {
                    continue;
                }
                if (!empty($record['language'])) {
                    $record['language'] .= ', ';
                }
                $record['language'] .= $this->languageConversions[$languageId];
                break;
            }
        }
        $record['pages'] = (!empty($XmlRecord->{'pocet-stran'})) ? trim((string)$XmlRecord->{'pocet-stran'}) : null; 
        // available_from, year
        $record['available_from'] = null;
        $record['year'] = null;
        if (!empty($XmlRecord->{'datum-uvedeni'})) {
            $record['available_from'] = trim((string)$XmlRecord->{'datum-uvedeni'});
            $record['year'] = substr($record['available_from'], 0, 4);
        }
        // availability
        if (isset($this->availabilityRecords[$record['supplier_product_id']])) {
            $record = array_merge($record, $this->availabilityRecords[$record['supplier_product_id']]);
        }
        else {
            $record = array_merge($record, array(                
                'availability' => null,
                'long_delivery_time' => null,
                'reprint' => null,
            ));
        }
        // - precise availability according parametry->novinka and parametry->predobjednavka
        if (
            !empty($XmlRecord->parametry->novinka)
            && (string)$XmlRecord->parametry->novinka === 'A'        
        ) {
            $record = array_merge($record, array(                
                'availability' => 'enum_presale',
                'long_delivery_time' => false,
                'reprint' => false,
            ));
        }
        if (
            !empty($XmlRecord->parametry->predobjednavka)
            && (string)$XmlRecord->parametry->predobjednavka === 'A'
            && ($attrs = $XmlRecord->parametry->predobjednavka->attributes())
            && (
                empty($attrs['predpokladane-naskladneni'])
                ||
                (string)$attrs['predpokladane-naskladneni'] > $this->todayDate
            )
        ) {
            $record = array_merge($record, array(                
                'availability' => 'enum_presale',
                'long_delivery_time' => false,
                'reprint' => false,
            ));
            if (!empty($attrs['predpokladane-naskladneni'])) {
                $record['available_from'] = trim((string)$attrs['predpokladane-naskladneni']);
                $record['year'] = substr($record['available_from'], 0, 4);
            }
        }
        // dimensions
        $record['height'] = (!empty($XmlRecord->rozmer1)) ? trim((string)$XmlRecord->rozmer1) : null; 
        $record['width'] = (!empty($XmlRecord->rozmer2)) ? trim((string)$XmlRecord->rozmer2) : null; 
        $record['length'] = (!empty($XmlRecord->rozmer3)) ? trim((string)$XmlRecord->rozmer3) : null; 
        $record['weight'] = (!empty($XmlRecord->hmotnost)) ? (int)$XmlRecord->hmotnost / 1000 : null; 
        $record['image_import_source'] = null;
        if (!empty($XmlRecord->obrazky->velky)) {
            $record['image_import_source'] = trim((string)$XmlRecord->obrazky->velky);
        }
        elseif (!empty($XmlRecord->obrazky->stredni)) {
            $record['image_import_source'] = trim((string)$XmlRecord->obrazky->stredni);
        }
        elseif (!empty($XmlRecord->obrazky->maly)) {
            $record['image_import_source'] = trim((string)$XmlRecord->obrazky->maly);
        }
        // product type and binding
        if (isset($XmlRecord->{'typ-produktu'})) {
            $attrs = $XmlRecord->{'typ-produktu'}->attributes();
            if (
                !empty($attrs['id'])
                && ($typeId = (string)$attrs['id'])
                && !empty($this->productTypeConversions[$typeId])
            ) {
                $record = array_merge($record, $this->productTypeConversions[$typeId]);
            }
            else {
                $record['sortiment'] = Str::slugize((string)$XmlRecord->{'typ-produktu'});
                $record['binding'] = null;
            }
        }
//        // use separate import for products description
//        $record['description'] = null;
        return $this->applyAvoidFields($record);
    }    
}