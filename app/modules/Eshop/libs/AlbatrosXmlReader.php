<?php
App::loadLib('Eshop', 'EshopXmlReader');

/**
 * Abstract class to be used as base for ALBATROS catalogue and stock readers.
 */
abstract class AlbatrosXmlReader extends EshopXmlReader {
    
    protected $recordTagName = 'ITEM';
    
    protected $decimalPoint = ',';
    
    protected $thousandsSeparator = '';
    
    /**
     * Transfers ALBATROS import files
     * 
     * @param string $file One of 'catalogue', 'stock'
     * @param string $accessKey
     * 
     * @return string transfered filename path
     * 
     * @throws Exception
     */
    protected function transferFile($file, $accessKey) {
        $files = array('catalogue' => 'xml-full', 'stock' => 'xml-compact');
        if (empty($files[$file])) {
            throw new Exception(sprintf('Invalid file specification: %s. Possible values are "catalogue" or "stock"', $file));
        }
        if (empty($accessKey)) {
            throw new Exception('Missing ALBATROS download access key');
        }
        $this->ensureTmpDir();
        $url = 'https://www.distri.sk/' . $files[$file] . '/' . $accessKey;
        $transferredFile = File::transfer(
            array($url),
            $this->tmpDir,
            array(
                'unique' => false,
                'name' => 'catalogue.xml',
            )
        );
        if (empty($transferredFile)) {
            throw new Exception(sprintf('Transfer of file from URL %s has failed', $url));
        }
        $transferredFile = $this->tmpDir . DS . $transferredFile;  
        // if the $accessKey is invalid then the downloaded file contains 94 bytes  
        // long string: "Chybný klíč pro autorizaci k souboru - zkontrolujte nastavení v administraci www.distri.sk"
        // An valid XML catalogue with one item and all tags empty has 425 bytes.
        // An valid XML stock with one item and all tags empty has 169 bytes.
        if (
            $file === 'catalogue' 
            && filesize(ROOT . DS . $transferredFile) < 400
            ||
            $file === 'stock' 
            && filesize(ROOT . DS . $transferredFile) < 150
        ) {
            throw new Exception(sprintf('Download of file from URL %s has failed bacause of invalid access key', $url));
        }
        return $transferredFile;
    }    
}