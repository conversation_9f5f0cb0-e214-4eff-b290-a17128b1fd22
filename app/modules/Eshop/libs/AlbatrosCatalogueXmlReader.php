<?php
App::loadLib('Eshop', 'AlbatrosXmlReader');

/**
 * Albatros XML catalogue file can be downloaded from https://www.distri.sk/xml-full/{KEY}
 * The {KEY} you can get in albatros backend and it is generated from IP address you 
 * download the catalogue on.
 * 
 * XML FULL catalogue item tags:
 * 
 *      NAME - nazov titulu
 *      EAN - EAN kod (ISBN)
 *      TYPE - druh titulu
 *      SUBTITLE - podtitul
 *      VAT - sazba DPH
 *      PRICEVAT - doporucena cena
 *      AVAILABILITY - dostupnost (1 = skladom, 0 = nie je skladom)
 *      AVAILABILITY_DETAIL - skladová zásoba na distribučnom sklade v Pezinku (ks), doba dodania 2-3 dni
 *      AMOUNT_CENTRAL_AVAILABLE - skladová zásoba centrálnom sklade v Prahe (ks), doba dodania 10-14 dní
 *      AUTHOR - autor
 *      DATEEXP - dá<PERSON> vydania (rrrr-mm-dd)
 *      ANNOTATION - kratka anotacia
 *      PUBLISHING - nakladatelstvo
 *      IMAGE - cesta k obrazku
 *      IMAGE_LARGE - cesta k obrázku s vyšším rozlíšením
 *      PAGES - pocet stran
 *      BOOKBINDING - vazba
 *      DIMENSION - rozmery
 *      RELEASE – poradie vydania v nakladatelstve
 *      EDITION - edicia vydania
 *      LANGUAGE - jazyk vydania
 *      GENRE – zaner
 *      REPRINTING_DATE - datum dotlace 
 *  
 * Contact: Róbert Kögel, 0904 701 762)
 * 
 * NOTE: It has the same structure as Cpress XML catalogue.
 * 
 * NOTE: XML COMPACT catalogue items tags are just subset of FULL catalogue. 
 */
class AlbatrosCatalogueXmlReader extends AlbatrosXmlReader {
    
    /**
     * Binding convertions (first word of albatros lowercased ascii binding => binding value) 
     * 
     * @var array
     */
    protected  $bindingConvertions = array(
        'vazana' => 'Pevná',
        'brozovana' => 'Brožovaná',
        'leporelo' => 'Leporelo',
    );  
    
    /**
     * Language codes conversion from Albatros
     * 
     * @var array 
     */
    protected $languageConversions = array(
        'cestina' => 'Český',
        'spanielcina' => 'Španielský',
        'francuzstina' => 'Francúzsky',
        'nemcina' => 'Nemecký',
        'anglictina' => 'Anglický',
        'taliancina' => 'Taliánsky',
        'slovencina' => 'Slovenský',
        'fincina' => 'Fínsky',
        'madarcina' => 'Maďarský',
        'rustina' => 'Ruský',
    );
    
    /**
     * Actual date in format YYYY-MM-DD. Used to skip old available_from dates
     * 
     * @var string
     */
    protected $actualDate = null;
    
    /**
     * Opens XML file
     * 
     * @param array $options
     *      - 'catalogueFile' (string) App root relative path to XML file. If not provided
     *          then it is downloaded from import provider.
     *      - 'accessKey' (string) ALBATROS accessKey. You can get it in albatros 
     *          b2b backend and it is generated from IP address you download the catalogue on. 
     *          Must be provided if 'catalogueFile' is not provided or is empty. Defauts to NULL.
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($options = array()) {
        $defaults = array(
            'catalogueFile' => null,
            'accessKey' => null,
        );
        $options = array_merge($defaults, $options);
        $this->actualDate = date('Y-m-d');
        
        // check for file and transfer if not provided
        if (empty($options['catalogueFile'])) {
            $options['catalogueFile'] = $this->transferFile('catalogue', $options['accessKey']);
        }
        
        parent::__construct($options['catalogueFile'], $options);        
    }
    
    public function validateRecord($XmlRecord) {
        if (
            isset($XmlRecord->NAME)
            && isset($XmlRecord->EAN)
            && isset($XmlRecord->TYPE)
            && isset($XmlRecord->SUBTITLE)
            && isset($XmlRecord->VAT)
            && isset($XmlRecord->PRICEVAT)
            && isset($XmlRecord->AVAILABILITY)
            && isset($XmlRecord->AVAILABILITY_DETAIL)
            && isset($XmlRecord->AMOUNT_CENTRAL_AVAILABLE)
            && isset($XmlRecord->AUTHOR)
            && isset($XmlRecord->DATEEXP)
            && isset($XmlRecord->ANNOTATION)
            && isset($XmlRecord->PUBLISHING)
            && isset($XmlRecord->IMAGE)
            && isset($XmlRecord->IMAGE_LARGE)
            && isset($XmlRecord->PAGES)
            && isset($XmlRecord->BOOKBINDING)
            && isset($XmlRecord->DIMENSION)
            && isset($XmlRecord->RELEASE)
            && isset($XmlRecord->EDITION)
            && isset($XmlRecord->LANGUAGE)
            && isset($XmlRecord->GENRE)
            && isset($XmlRecord->REPRINTING_DATE)
        ) {
            return true;
        }
        return false;
    }
          
    /**
     * Reads XML record and parses it to following normalized array for files 
     * export.xml/update.xml ( is missing):
     * 
     *      array(
     *          'name' => ...,
     *          'ean' => ...,
     *          'sortiment' => ...,                  // 'Kniha', 'Kniha + CD', ...
     *          'subtitle' => ...,
     *          'tax_rate' => ...,
     *          'price_taxed' => ...,           
     *          'availability' => ...,          // 'enum_presale', 'enum_available', 'enum_soldout'
     *          'long_delivery_time' => ...,          
     *          'reprint' => ...,            
     *          'available_from' => ...,
     *          'authors' => ...,               // array of authors
     *          'year' => ...,
     *          'description' => ...,
     *          'manufacturer' => ...,          // manufacturer name
     *          'image_import_source' => ...,
     *          'pages' => ...,
     *          'binding' => ...,
     *          'width' => ...,
     *          'height' => ...,
     *          'edition' => ...,
     *          'range' => ...,                 // range name
     *          'language' => ...,
     *          'categories' => ...,            // array of Albatros genres
     *          'stock' => ...,
     *          'shipment_time_off_stock' => ...,  
     *      )
     * 
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        // - ean and name are available in all 3 xml files
        $record = array();
        $record['name'] = trim((string)$XmlRecord->NAME);
        $record['ean'] = trim((string)$XmlRecord->EAN);
        $record['sortiment'] = Str::slugize(
            preg_replace('/\s*\+\s*/', ' a ', (string)$XmlRecord->TYPE)
        );
        $record['subtitle'] = trim((string)$XmlRecord->SUBTITLE);
        $record['tax_rate'] = (int)trim((string)$XmlRecord->VAT, ' %');
        // price
        $record['price_taxed'] = (float)$this->normalizeNumber((string)$XmlRecord->PRICEVAT);
        // availability, available_from & reprint
        if ((int)$XmlRecord->AVAILABILITY === 1) {
            $record['availability'] = 'enum_available';
        }
        else {
            $record['availability'] = 'enum_soldout';
        }
        $record['available_from'] = null;
        if (
            ($date = (string)$XmlRecord->DATEEXP)
            && ($date = Date::format($date, 'Y-m-d'))
            && $date > $this->actualDate
        ) {
            // to simulate real date of book availability add ~8 days to DATEEXP
            //rblb//$record['available_from'] = (string)$XmlRecord->DATEEXP;
            $record['available_from'] = date('Y-m-d', strtotime((string)$XmlRecord->DATEEXP . ' + 8 day'));
            $record['availability'] = 'enum_presale';
        }
        $record['reprint'] = false;
        if ((string)$XmlRecord->REPRINTING_DATE) {
            $record['reprint'] = true;
            $reprintingDate = Date::format((string)$XmlRecord->REPRINTING_DATE);
            if ($reprintingDate > $this->actualDate) {
                $record['available_from'] = $reprintingDate;
                $record['availability'] = 'enum_presale';
            }
        }
        // authors
        $authorArray = array();
        if ((string)$XmlRecord->AUTHOR) {
            $tmpArray = explode(',', (string)$XmlRecord->AUTHOR);
            foreach ($tmpArray as $value) {
                $value = trim($value);
                if (
                    !empty($value)
                    && $value !== 'kolektiv'
                    && $value !== 'autora nemá'
                ) {
                    array_push($authorArray, $value);
                }
            }
        }
        $record['authors'] = $authorArray;
        $record['year'] = substr((string)$XmlRecord->DATEEXP, 0, 4);
        $record['description'] = (string)$XmlRecord->ANNOTATION;
        $record['manufacturer'] = (string)$XmlRecord->PUBLISHING;
        $record['image_import_source'] = (string)$XmlRecord->IMAGE_LARGE;
        $record['pages'] = (string)$XmlRecord->PAGES;
        $record['binding'] = null;
        if ((string)$XmlRecord->BOOKBINDING) {
            $binding = explode(' ', trim((string)$XmlRecord->BOOKBINDING));
            $binding = strtolower(Sanitize::nonAscii($binding[0]));
            if (!empty($this->bindingConvertions[$binding])) {
                $record['binding'] = $this->bindingConvertions[$binding];
            }
            else {
                $record['binding'] = Str::uppercaseFirst((string)$XmlRecord->BOOKBINDING);
            }
        }
        $record['width'] = null;
        $record['height'] = null;
        if((string)$XmlRecord->DIMENSION) {
            $dimensions = explode('x', trim((string)$XmlRecord->DIMENSION));
            $record['width'] = trim($dimensions[0]);
            $record['height'] = trim($dimensions[1], ' m');
        }
        $record['edition'] = (string)$XmlRecord->RELEASE;
        $record['range'] = (string)$XmlRecord->EDITION;
        $language = strtolower(Sanitize::nonAscii((string)$XmlRecord->LANGUAGE));
        if (!empty($this->languageConversions[$language])) {
            $record['language'] = $this->languageConversions[$language];
        }
        else {
            $record['language'] = Str::uppercaseFirst((string)$XmlRecord->LANGUAGE);
        }
        $record['categories'] = array_map(
            function($value) { 
                return Str::slugize(trim($value));                     
            },
            Str::explode(',', trim((string)$XmlRecord->GENRE))
        );
        $record['stock'] = (int)trim((string)$XmlRecord->AVAILABILITY_DETAIL);
        $record['long_delivery_time'] = false;
        if (!empty($record['stock'])) {
            $record['shipment_time_off_stock'] = 5; // 2-3 dni albatros -> obchodník + 2 obchodník -> zákazník
        }
        else {
            $record['shipment_time_off_stock'] = 16; // 10-14 dni albatros -> obchodník + 2 obchodník -> zákazník
            $record['long_delivery_time'] = true;
        }
        
        return $this->applyAvoidFields($record);
    }
}