<?php
App::loadLib('App', 'Csv');

/**
 * Abstract class to be used as base for eshop product catalogues CSV readers. Implements
 * methods ::ensureTmpDir(), ::removeTmpDir() and normalizeEan().
 * 
 * NOTE: For use-cases see vydavatel.sk
 */
abstract class EshopCsvReader extends Csv {
    
    /**
     * App root relative path to tmp directory there are stored tranfered files
     *
     * @var string
     */
    protected $tmpDir = null;
    
    /**
     * Has been the method EshopCsvReader::registerTmpDirRemoval() launched?
     * 
     * @var bool 
     */
    private $tmpDirRemovalRegistered = false;
    
        
    public function normalizeEan($ean) {
        $ean = preg_replace('/[^0-9]/', '', $ean);
        if (empty($ean)) {
            return null;
        }
        return $ean;
    }
    
    protected function ensureTmpDir() {
        $this->registerTmpDirRemoval();
        if (empty($this->tmpDir)) {
            $dirName = uniqid(Str::underscore(get_class($this)) . '_tmp_');
            if (
                !($this->tmpDir = File::getRelativePath(TMP . DS . $dirName))
                || !File::ensurePath($this->tmpDir) 
            ) {
                throw new Exception('Temporary directory creation failure');
            }
        }
    }
    
    protected function removeTmpDir() {
        if (!empty($this->tmpDir)) {
            File::removeTmpDir($this->tmpDir);
        }
    }
    
    /**
     * Ensure that the destructor is called even in case of fatal error
     */
    protected function registerTmpDirRemoval() {
        if (!$this->tmpDirRemovalRegistered) {
            // ensure that the destructor is called even in case of fatal error (downside
            // is that object reference = object itself exists till the end of script execution)
            register_shutdown_function(array($this, '__destruct'));
            $this->tmpDirRemovalRegistered = true;
        }
    }
    
    /**
     * Uploads import files
     * 
     * @param string $file File upload data
     * 
     * @return string App root relative path to uploaded file (containig file itself)
     */
    protected function uploadFile($file) {
        $this->ensureTmpDir();
        $file = File::upload(
            $file,
            $this->tmpDir,
            array(
                'unique' => true,
            )
        );
        $file = $this->tmpDir . DS . $file;
        return $file;
    }
    
    public function __destruct() {
        parent::__destruct();
        $this->removeTmpDir();
    }
    
    /**
     * Returns array of parsed dimensions like:
     * 
     *      array(
     *          'width' => ...,     // [mm]
     *          'height' => ...,    // [mm]
     *          'length' => ...,    // [mm]
     *      )
     * 
     * Returned dimensions are in millimeters.
     * 
     * @param string $dimensions E.g. '25,5x16 cm'
     * 
     * @return array
     */
    protected function parseDimensions($dimensions) {
        return Number::parseDimensions($dimensions, array(
            'thousandsSeparator' => $this->thousandsSeparator,
            'decimalPoint' => $this->decimalPoint,
        ));
    }
}

