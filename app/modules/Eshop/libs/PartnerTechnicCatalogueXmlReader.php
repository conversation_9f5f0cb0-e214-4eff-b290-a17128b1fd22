<?php
App::loadLib('Eshop', 'PartnerTechnicXmlReader');

/**
 * Reads products catalogue of Partner technic
 * 
 * For more details see phpDoc of PartnerTechnicXmlReader class
 */
class PartnerTechnicCatalogueXmlReader extends PartnerTechnicXmlReader {
    
    /**
     * Opens XML file
     * 
     * @param array $options
     *      - 'catalogueFile' (string) Partner technic catalogue file. If not provided or empty 
     *          then it is downloaded from Partner technic site. In such a case option 'accessKey'
     *          must be provided. Defaults to NULL.
     *      - 'useFullCatalogue' (bool) Applied only if catalogueFile is downloaded.
     *          If TRUE then a catalogue_full file is downloaded. Defaults to FALSE. 
     *      - 'priceFile' (string|FALSE) Partner technic price file. If not provided or empty 
     *          then it is downloaded from Partner technic site. In such a case option 'accessKey'
     *          must be provided. If FALSE then stock file is not used and price fields are not set. 
     *          Defaults to NULL.
     *      - 'stockFile' (string|FALSE) Partner technic stock file. If not provided or empty 
     *          then it is downloaded from Partner technic site. In such a case option 'accessKey'
     *          must be provided. If FALSE then stock file is not used and availability fields
     *          are not set. Defaults to NULL.
     *      - 'accessKey' (string) Partner technic accessKey. Must be provided if 
     *          some of files is not provided or is empty. Defauts to NULL.
     *      - 'processEans' (array&) List of eans to be allowed when loading (caching)
     *          price and availability (stock) records. This is just question of memory save (= optimization).
     *          If not provided then all price records are loaded. To make it really 
     *          optimal provide this option by reference: 'processEans' => &$eans.
     *          Defaults to empty array(). 
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($options = array()) {
        $defaults = array(
            'catalogueFile' => null,
            'useFullCatalogue' => false,
            'priceFile' => null,
            'stockFile' => null,
            'accessKey' => null,
            'processEans' => array(),
        );
        $options = array_merge($defaults, $options);
        
        // check for files and transfer missing ones
        if (empty($options['catalogueFile'])) {
            if ($options['useFullCatalogue']) {
                $options['catalogueFile'] = $this->transferFile('catalogue_full', $options['accessKey']);
            }
            else {
                $options['catalogueFile'] = $this->transferFile('catalogue', $options['accessKey']);
            }
        }
        if (
            empty($options['priceFile'])
            && $options['priceFile'] !== false
        ) {
            $options['priceFile'] = $this->transferFile('price', $options['accessKey']);
        }
        if (
            empty($options['stockFile'])
            && $options['stockFile'] !== false
        ) {
            $options['stockFile'] = $this->transferFile('stock', $options['accessKey']);
        }
        parent::__construct($options['catalogueFile'], $options);
        // populate processEans if not provided and update catalogue is read
        if (
            !$options['processEans']
            && !$options['useFullCatalogue']
            && (
                !empty($options['priceFile'])
                || !empty($options['stockFile'])
            )
        ) {
            $options['processEans'] = array();
            while($record = $this->readRecord()) {
                $options['processEans'][$record['ean']] = true;
            }
            // "reset" the catalogue file pointer to the first record
            $this->close();
            $this->open();
        }
        // load price and stock records
        if (!empty($options['priceFile'])) {
            $this->loadPriceRecords($options);
        }
        if (!empty($options['stockFile'])) {
            $this->loadAvailabilityRecords($options);
        }
    }
    
    public function validateRecord($XmlRecord) {
        if (
            // export.xml or update.xml
            isset($XmlRecord->EAN)
            && isset($XmlRecord->NAZOV)
            && isset($XmlRecord->PODNAZOV)
            && isset($XmlRecord->ISBN)
            && isset($XmlRecord->AUTORI)
            && isset($XmlRecord->VYDAVATEL)
//            && isset($XmlRecord->MOC_EUR) // this field can be deprecated by the time
            && isset($XmlRecord->LANGUAGE)
            && isset($XmlRecord->ZANER)
            && isset($XmlRecord->POCET_STRAN)
            && isset($XmlRecord->ROK_VYDANIA)
            && isset($XmlRecord->ROZMERY)
            && isset($XmlRecord->VAZBA)
            && isset($XmlRecord->ANOTACIA)
            && isset($XmlRecord->OBRAZOK)
//            && isset($XmlRecord->DPH) // this field can be deprecated by the time
        ) {
            return true;
        }
        return false;
    }
          
    /**
     * Reads XML record and parses it to following normalized array for files 
     * export.xml/update.xml:
     * 
     *      array(
     *          'name' => ...,
     *          'subtitle' => ...,
     *          'ean' => ...,
     *          'isbn' => ...,
     *          'price_taxed' => ...,   // taxed price
     *          'tax_rate' => ...,
     *          'margin_rate' => ...,
     *          'authors' => ...,       // array of authors
     *          'year' => ...,
     *          'description' => ...,
     *          'manufacturer' => ...,  // manufacturer name
     *          'image_import_source' => ...,
     *          'pages' => ...,
     *          'binding' => ...,
     *          'height' => ..., [mm]       // [mm]
     *          'width' => ..., [mm]        // [mm]
     *          'weigth' => ..., [kg]       // [kg]
     *          'language' => ...,      // 'Český', 'Slovenský', ...
     *          'language_code' => ..., // 'SK', 'CZ'
     *          'categories' => ...,    // array of categories     
     *          'stock' => ...,                 // (int)|NULL
     *          'availability' => ...,          // 'enum_presale', 'enum_available', 'enum_soldout'
     *          'available_from' => ...,
     *      )
     * 
     * ATTENTION: In case of export.xml the 'availability' and 'stock' are missing!
     *  
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        // - ean and name are available in all 3 xml files
        $record = array();
        $record['ean'] = trim((string)$XmlRecord->EAN);
        $record['name'] = trim((string)$XmlRecord->NAZOV);
        $record['subtitle'] = trim((string)$XmlRecord->PODNAZOV);
        $record['isbn'] = trim((string)$XmlRecord->ISBN);
        // price, tax_rate and margin_rate
        $record['price_taxed'] = null;
        $record['tax_rate'] = null;
        $record['margin_rate'] = null;
        if (
            !empty($XmlRecord->MOC_EUR)
            && !empty($XmlRecord->DPH)
        ) {
            $record['price_taxed'] = (float)$this->normalizeNumber((string)$XmlRecord->MOC_EUR);
            $record['tax_rate'] = (int)trim((string)$XmlRecord->DPH);
        }
        if (isset($this->priceRecords[$record['ean']])) {
            $record = array_merge($record, $this->priceRecords[$record['ean']]);
        }
        // authors
        $authorArray = array();
        $authors = $XmlRecord->AUTORI;
        foreach($authors->AUTOR as $author) {
            $tmpArray = explode(',', (string)$author);
            foreach ($tmpArray as $value) {
                array_push($authorArray, $value);
            }
        }
        $record['authors'] = array_map('trim', $authorArray);

        // available from and year
        $record['year'] = trim((string)$XmlRecord->ROK_VYDANIA);
        // supplier stock
        $record['stock'] = null;
        $record['availability'] = null;
        $record['available_from'] = null;
        if (isset($XmlRecord->DOSTUPNOST)) {
            $record['stock'] = (int)$XmlRecord->DOSTUPNOST;
            if ($record['stock'] < 0) {
                $record['stock'] = 0;
            }
        }
        if (isset($this->availabilityRecords[$record['ean']])) {
            $record = array_merge($record, $this->availabilityRecords[$record['ean']]);
        }

        $record['description'] = trim((string)$XmlRecord->ANOTACIA);
        $record['manufacturer'] = trim((string)$XmlRecord->VYDAVATEL);
        $record['image_import_source'] = trim((string)$XmlRecord->OBRAZOK);
        $record['pages'] = trim((string)$XmlRecord->POCET_STRAN);
        $record['binding'] = trim((string)$XmlRecord->VAZBA);
        $record['width'] = trim((string)$XmlRecord->ROZMERY->SIRKA);
        $record['height'] = trim((string)$XmlRecord->ROZMERY->VYSKA);
        $record['weight'] = (float)trim((string)$XmlRecord->ROZMERY->HMOTNOST)/1000;
        $language = explode(' ', trim((string)$XmlRecord->LANGUAGE));
        if (count($language) > 1) {
            $record['language_code'] = $language[0];
            $record['language'] = $language[1];
        }
        else {
            $record['language_code'] = null;
            $record['language'] = trim((string)$XmlRecord->LANGUAGE);
        }
        $record['categories'] = array(trim((string)$XmlRecord->ZANER));
        
        return $this->applyAvoidFields($record);
    }
}