<?php
App::loadLib('Eshop', 'EshopCsvReader');

/**
 * Eshop product import.txt file reader
 * 
 * Columns in file import.txt are distinguished by headers.
 * As headers use field names of EshopProduct table except of few prohibited (see
 * __construct() for removed files) plus some virtual fields (categories, authors,
 * manufacturer, ... see __construct() for all of them)
 */
class EshopProductCsvReader extends EshopCsvReader {
    
    /**
     * App root relative path to imported images directory.
     * 
     * @var string 
     */
    protected $imagesDirectory = '/userfiles/files/import/images';
    
    /**
     * Reads existing products stock amount exported from MRP as csv file
     * 
     * @param array $options Options of Csv() class plus following:
     *      - 'catalogueFile' (string) App root relative path to Alterego catalogue file. 
     *          Defaults to '/userfiles/files/import/knihy.csv'.
     */    
    public function __construct($options = array()) {
        $defaults = array(
            'catalogueFile' => '/userfiles/files/import/import.txt',
            'imagesDirectory' => $this->imagesDirectory,
        );
        $options = array_merge($defaults, $options);
        
        $this->imagesDirectory = $options['imagesDirectory'];
        
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $productSchema = $Product->getPropertySchema();
                
        parent::__construct($options['catalogueFile'], array_merge($options, array(
            'mode' => 'r',
            'delimiter' => ",",
            'enclosure' => '"',
            'hasHeaders' => true,
            'encoding' => 'CP1250',
            'decimalPoint' => '.',
            'thousandsSeparator' => '',
            'recordFields' => self::getRecordFields(),
            'ignoreUnexistingColumnsRecordFields' => true,
            'preprocessRecordFieldValue' => function($value, $field, $csvRecord, &$outputRecord) use ($productSchema) {
                // dimensions (intentionally before any other processing)
                if ($field === 'dimensions') {
                    $parsedDimensions = $this->parseDimensions($value);
                    if (
                        !array_key_exists('width', $csvRecord)
                        && !array_key_exists('width', $outputRecord)
                    ) {
                        $outputRecord['width'] = '';
                        if ($parsedDimensions['width']) {
                            $outputRecord['width'] = $parsedDimensions['width'];
                        }
                    }
                    if (
                        !array_key_exists('height', $csvRecord)
                        && !array_key_exists('height', $outputRecord)
                    ) {
                        $outputRecord['height'] = '';
                        if ($parsedDimensions['height']) {
                            $outputRecord['height'] = $parsedDimensions['height'];
                        }
                    }
                    if (
                        !array_key_exists('length', $csvRecord)
                        && !array_key_exists('length', $outputRecord)
                    ) {
                        $outputRecord['length'] = '';
                        if ($parsedDimensions['length']) {
                            $outputRecord['length'] = $parsedDimensions['length'];
                        }
                    }
                }                
                // empty string is normalized to NULL which is interpreted like 
                // "do not change", see Model::saveBatch() > 'ignoreNewValueIf'
                if ($value === '') {
                    return null;
                }
                // "-" is normalized to empty string which is interpreted as "erase value"
                if ($value === '-') {
                    return '';
                }
                // manufacturer name
                if ($field === 'manufacturer') {
                    $value = $this->convertEncoding($value);
                    $value = trim($value);
                    return $value;
                }
                // range name
                if ($field === 'range') {
                    $value = $this->convertEncoding($value);
                    $value = trim($value);
                    return $value;
                }
                // EshopCategory.code-s separated by ";"
                if ($field === 'categories') {
                    $value = Str::explode(';', $value);
                    $value = array_map('trim', $value);
                    return $value;
                }
                // author names separated by ";"
                if ($field === 'authors') {
                    $value = $this->convertEncoding($value);
                    $value = Str::explode(';', $value);
                    $value = array_map('trim', $value);
                    foreach ($value as $i => $v) {
                        if (Str::slugize($v) === 'neuvedeny') {
                            unset($value[$i]);
                        }
                    }
                    return $value;
                }
                // image_import_source is auto populated here according to files placed 
                // in import images directory and named like {ean}_0.jpg. To get here
                // the value of image_import_source column in csv file must be anything
                // that differs from '' or '-'. That csv value is ignored and the real
                // output value is set here.
                if ($field === 'image_import_source') {
                    if (
                        !empty($this->imagesDirectory)
                        && !empty($outputRecord['ean'])
                        && is_readable(
                            ROOT . ($image = DS . trim($this->imagesDirectory, DS) . DS . $outputRecord['ean'] . '_0.jpg')
                        )
                    ) {
                        return $image;
                    }
                    // if nothing found then return NULL and "do not change " anything
                    // (see here above for empty string normalization)
                    return null;
                }
                // gallery_image_import_sources is auto populated here according to files placed 
                // in import images directory and named like {ean}_{number>0}.jpg. To get here
                // the value of gallery_image_import_sources column in csv file must be anything
                // that differs from '' or '-'. That csv value is ignored and the real
                // output value is set here.
                if ($field === 'gallery_image_import_sources') {
                    $images = array();
                    if (
                        empty($this->imagesDirectory)
                        || empty($outputRecord['ean'])
                        || !is_readable(ROOT . DS . trim($this->imagesDirectory, DS))
                    ) {
                        return $images;
                    }
                    $i = 1;
                    while (
                        is_readable(
                            ROOT . ($image = DS . trim($this->imagesDirectory, DS) . DS . $outputRecord['ean'] . '_' . $i . '.jpg')
                        )
                    ) {
                        $images[] = $image;
                        $i++;
                    }
                    if ($images) {
                        return $images;
                    }
                    // if nothing found then return NULL and "do not change " anything
                    // (see here above for empty string normalization)
                    return null;
                }
                if ($field === 'price_taxed') {
                    $field = 'price';
                }
                if (isset($productSchema[$field]['type'])) {
                    switch (strtoupper($productSchema[$field]['type'])) {
                        case 'TINYTEXT': case 'TEXT': case 'MEDIUMTEXT': case 'LONGTEXT':
                        case 'STRING': case 'STR': case 'VARCHAR':
                        case 'CHAR':
                        case 'DATE': case 'TIME': case 'DATETIME':
                        case 'ENUM': case 'SET':
                            $value = $this->convertEncoding($value);
                            $value = trim($value);
                            break;
                        case 'INT': case 'INTEGER':
                        case 'TINYINT': case 'BOOL': case 'BOOLEAN':
                        case 'BIT': case 'SMALLINT': case 'MEDIUMINT': case 'BIGINT':
                        case 'YEAR': case 'TIMESTAMP':
                            $value = trim($value);
                            $value = $this->convertType($value, 'int');
                            break;
                        case 'REAL': case 'DOUBLE': case 'FLOAT': case 'DECIMAL': case 'NUMERIC':
                            $value = trim($value);
                            $value = $this->convertType($value, 'float');
                            break;
//                        case 'BINARY': case 'VARBINARY': case 'TINYBLOB':
//                        case 'BLOB': case 'MEDIUMBLOB': case 'LONGBLOB': 
//                            break;
                    }
                }
                return $value;
            }
        )));
    }
    
    /**
     * Returns array of record fields definitions which can be used for 'recordFields'
     * option of class constructor.
     * 
     * @return array
     */
    public static function getRecordFields() {
        // create record fields in a generic way
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $productSchema = $Product->getPropertySchema();
        $recordFields = array();
        foreach (array_keys($productSchema) as $field) {
            $recordFields[$field] = array();
        }
        // remove following fields (some of them are replaced by virtual fields here below)
        //unset($recordFields['id']); // do not remove id if used as pair field
        unset($recordFields['parent_id']);
        unset($recordFields['slug']);
        unset($recordFields['discount_price']);
        unset($recordFields['image']);
        unset($recordFields['run_eshop_manufacturers_id']);
        unset($recordFields['run_eshop_manufacturer_ranges_id']);
        unset($recordFields['run_eshop_product_types_id']);
        unset($recordFields['price']);
        unset($recordFields['unavailable']);
        unset($recordFields['created']);
        unset($recordFields['modified']);
        unset($recordFields['exported']);
        unset($recordFields['image_exported']);
        unset($recordFields['deleted']);
        // remove fields unused for project alterego
        unset($recordFields['manufacturer_group']);
        unset($recordFields['synchronize_price_with_suppliers']);
        unset($recordFields['supplier_info']);
        unset($recordFields['supplier_pid']);
        unset($recordFields['variant']);
        unset($recordFields['ebook_url']);
        unset($recordFields['ebook_url_2']);
        // add virtual fields
        $recordFields['price_taxed'] = array();
        $recordFields['manufacturer'] = array();
        $recordFields['range'] = array();
        $recordFields['categories'] = array();
        $recordFields['authors'] = array();
        //@todo200520 - uncomment once these fields are integrated in EshopProductImport::updateImport() > 'internalProductUpdate'
        //$recordFields['image_import_source'] = array(); 
        //$recordFields['gallery_image_import_sources'] = array();
        
        // do additional conversions for following fields (additional to generic 
        // conversion done by 'preprocessColumnValue' option
        $recordFields['ean']['convert'] = function($value, $record, $outputRecord, $Reader) {
            return $Reader->normalizeEan($value);
        };

        return $recordFields;
    }
        
    /**
     * Reads the next csv record from file. Info line, header row and empty lines 
     * are omitted. Returned record fields can vary. See EshopProductCsvReader::getRecordFields()
     * for all available posiibilities on project.
     * 
     * @return bool|array Next record array. NULL if the end of csv file is reached.
     */
    public function readRecord() {
        return parent::readRecord();
    }
}
