<?php
App::loadLib('App', 'XmlRecordsReader');

/**
 * Abstract class to be used for eshop product catalogues XML readers. Implements
 * methods ::ensureTmpDir(), ::removeTmpDir() and normalizeEan().
 * 
 * NOTE: For use-cases see vydavatel.sk
 */
abstract class EshopXmlReader extends XmlRecordsReader {
    
    /**
     * List of fields which should not be returned by reader, e.g. array('description', 'manufacturer'),
     * Initialized by options in constructor. 
     *      
     * @var array 
     */
    protected $avoidFields = null;
    
    /**
     * App root relative path to tmp directory where tranfered files are stored.
     * Path does not contain leading and trailing slashes.
     *
     * @var string
     */
    protected $tmpDir = null;
    
    /**
     * Has been the launch of destructor ensured by register_shutdown_function()?
     * 
     * @var bool 
     */
    protected $destructMethodEnsured = false;
    
    
    /**
     * Creates new XML reader instance
     * 
     * @param string $file App root relative path to XML file
     * @param array $options Options of XmlRecordsReader() class plus following:
     *      - 'avoidFields' (array) List of fields which should not be returned by reader, 
     *          e.g. array('description', 'manufacturer'). See method ::applyAvoidFields()
     */    
    public function __construct($file, $options = array()) {
        $defaults = array(
            'avoidFields' => null,
        );
        $options = array_merge($defaults, $options);
        $this->avoidFields = (array)$options['avoidFields'];
        
        parent::__construct($file, $options);
    }
    
    protected function normalizeEan($ean) {
        $ean = preg_replace('/[^0-9]/', '', $ean);
        if (empty($ean)) {
            return null;
        }
        return $ean;
    }
    
    protected function ensureTmpDir() {
        $this->registerTmpDirRemoval();
        if (empty($this->tmpDir)) {
            $dirName = uniqid(Str::underscore(get_class($this)) . '_tmp_');
            if (
                !($this->tmpDir = File::getRelativePath(TMP . DS . $dirName))
                || !File::ensurePath($this->tmpDir) 
            ) {
                throw new Exception('Temporary directory creation failure');
            }
        }
    }
    
    protected function removeTmpDir() {
        if (!empty($this->tmpDir)) {
            File::removeTmpDir($this->tmpDir);
        }
    }
    
    /**
     * Ensure that the destructor is called even in case of fatal error
     */
    protected function registerTmpDirRemoval() {
        if (!$this->destructMethodEnsured) {
            // ensure that the destructor is called even in case of fatal error (downside
            // is that object reference = object itself exists till the end of script execution)
            register_shutdown_function(array($this, '__destruct'));
            $this->destructMethodEnsured = true;
        }
    }
    
    /**
     * Uploads import files
     * 
     * @param string $file File upload data
     * 
     * @return string App root relative path to uploaded file (containig file itself)
     */
    protected function uploadFile($file) {
        $this->ensureTmpDir();
        $file = File::upload(
            $file,
            $this->tmpDir,
            array(
                'unique' => true,
            )
        );
        $file = $this->tmpDir . DS . $file;
        return $file;
    }
    
    public function __destruct() {
        parent::__destruct();
        $this->removeTmpDir();
    }
    
    /**
     * Removes ::$avoidFields from provided record.
     * 
     * NOTE: Call this method at the end of ::readRecord() method like return $this->applyAvoidFields($record);
     * 
     * @param array $record
     * @return array
     */
    protected function applyAvoidFields($record) {
        foreach ($this->avoidFields as $field) {
            if (array_key_exists($field, $record)) {
                unset($record[$field]);
            }
        }
        return $record;
    }    
    
    /**
     * Returns absolute path to imported catalogue (value of EshopXmlReader::$file)
     * 
     * @return string Absolute filename path
     */
    public function getPropertyFile() {
        return $this->file;
    }
    
    /**
     * Returns array of parsed dimensions like:
     * 
     *      array(
     *          'width' => ...,     // [mm]
     *          'height' => ...,    // [mm]
     *          'length' => ...,    // [mm]
     *      )
     * 
     * Returned dimensions are in millimeters.
     * 
     * @param string $dimensions E.g. '25,5x16 cm'
     * 
     * @return array
     */
    protected function parseDimensions($dimensions) {
        return Number::parseDimensions($dimensions, array(
            'thousandsSeparator' => $this->thousandsSeparator,
            'decimalPoint' => $this->decimalPoint,
        ));
    }
}

