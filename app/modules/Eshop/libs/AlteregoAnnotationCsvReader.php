<?php
App::loadLib('Eshop', 'EshopCsvReader');

/**
 * Alterego anotacie.txt file reader
 * 
 * The anotacie.txt file contains following columns: 
 *      [0] => CODE
 *      [1] => BINDING
 *      [2] => PAGES
 *      [3] => EAN
 *      [4] => DIMENSIONS
 *      [5] => PUBLISHING YEAR
 *      [6] => DESCRIPTION
 */
class AlteregoAnnotationCsvReader extends Eshop<PERSON>vReader {
    
    /**
     * App root relative path to Alterego images directory.
     * 
     * @var string 
     */
    protected $imagesDirectory = '/userfiles/files/import/images';
    
    /**
     * Reads existing products stock amount exported from MRP as csv file
     * 
     * @param array $options Options of Csv() class plus following:
     *      - 'annotationFile' (string) App root relative path to Alterego annotation file. 
     *          Defaults to '/userfiles/files/import/anotacie.txt'.
     *      - 'imagesDirectory' (string) App root relative path to Alterego images directory. 
     *          Defaults to '/userfiles/files/import/images'.
     */    
    public function __construct($options = array()) {
        $defaults = array(
            'annotationFile' => '/userfiles/files/import/anotacie.txt',
            'imagesDirectory' => $this->imagesDirectory,
        );
        $options = array_merge($defaults, $options);
        
        $this->imagesDirectory = $options['imagesDirectory'];
        
        parent::__construct($options['annotationFile'], array_merge($options, array(
            'mode' => 'r',
            'delimiter' => "|",
            'enclosure' => '"',
            'hasHeaders' => false,
            'encoding' => 'CP1250',
            'decimalPoint' => ',',
            'thousandsSeparator' => '',
            'recordFields' => array(
                'code' => array('column' => 0, 'trim' => true),
                'binding' => array('column' => 1, 'convertEncoding' => true, 'trim' => true),  
                'pages' => array('column' => 2, 'trim' => true, 'convertType' => 'int'),   
                'ean' => array('column' => 3, 'trim' => true, 'convert' => function($value) {
                    return $this->normalizeEan($value);
                }), 
                'width' => array('column' => 4, 'convertEncoding' => true, 'trim' => true, 'convert' => function($value) {
                    return $this->parseDimensions($value, 'width');
                }),
                'height' => array('column' => 4, 'convertEncoding' => true, 'trim' => true, 'convert' => function($value) {
                    return $this->parseDimensions($value, 'height');
                }),
                'length' => array('column' => 4, 'convertEncoding' => true, 'trim' => true, 'convert' => function($value) {
                    return $this->parseDimensions($value, 'length');
                }),
                'year' => array('column' => 5, 'trim' => true, 'convertType' => 'int'),
                'description' => array('column' => 6, 'convertEncoding' => true, 'trim' => true),
                'language' => array('column' => 6, 'convertEncoding' => true, 'trim' => true, 'convert' => function($value) {
                    $match = array();
                    if (preg_match('/(\S+)\s+(?:preklad|překlad|text)\.?\W*$/iu', $value, $match)) {
                        return strtolower($match[1]);
                    }
                    return null;
                }),
                'image_import_source' => function($record, $outputRecord) { 
                    if (
                        !empty($this->imagesDirectory)
                        && !empty($outputRecord['ean'])
                        && is_readable(
                            ROOT . ($image = DS . trim($this->imagesDirectory, DS) . DS . $outputRecord['ean'] . '_0.jpg')
                        )
                    ) {
                        return $image;
                    }
                    return null;
                },
                'gallery_image_import_sources' => function($record, $outputRecord) { 
                    $images = array();
                    if (
                        empty($this->imagesDirectory)
                        || empty($outputRecord['ean'])
                        || !is_readable(ROOT . DS . trim($this->imagesDirectory, DS))
                    ) {
                        return $images;
                    }
                    $i = 1;
                    while (
                        is_readable(
                            ROOT . ($image = DS . trim($this->imagesDirectory, DS) . DS . $outputRecord['ean'] . '_' . $i . '.jpg')
                        )
                    ) {
                        $images[] = $image;
                        $i++;
                    }
                    return $images;
                },
            ),
        )));
    }
    
    /**
     * Returns specified dimension value
     * 
     * @param string $dimensions
     * @param string $dimension One of 'width', 'height', 'length'
     * @return float
     */
    protected function parseDimensions($dimensions, $dimension) {
        if (empty($dimensions)) {
            return null;
        }
        $dimensionIndexes = array(
            'width' => 0,
            'height' => 1,
            'length' => 2,
        );
        $dimensionIndex = $dimensionIndexes[$dimension];
        $dimensions = strtolower($dimensions);
        $rate = strpos($dimensions, 'cm') !== false ? 10 : 1;
        $dimensions = trim($dimensions, ' cm');
        $dimensions = explode('x', $dimensions);
        if (!isset($dimensions[$dimensionIndex])) {
            return null;
        }
        return $this->convertType(trim($dimensions[$dimensionIndex], ' cm'), 'float') * $rate;
    }
        
    /**
     * Reads the next csv record from file. Info line, header row and empty lines are omitted.
     * Returned record has following items:
     * 
     *      array(
     *          'code' => ...,
     *          'binding' => ...,
     *          'pages' => ...,
     *          'ean' => ...,
     *          'width' => ...,
     *          'height' => ...,
     *          'length' => ...,
     *          'year' => ...,
     *          'description' => ...,
     *          'language' => ...,
     *          'image_import_source' => ...,
     *          'gallery_image_import_sources' => ...,
     *      )
     * 
     * @return bool|array Next record array. NULL if the end of csv file is reached.
     */
    public function readRecord() {
        return parent::readRecord();
    }
}
