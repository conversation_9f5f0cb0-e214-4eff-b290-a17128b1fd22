<?php
App::loadLib('Eshop', 'EshopCsvReader');

/**
 * Abstract class to be used as base for PEMIC catalogue, availability (stock) and 
 * price readers.
 */
abstract class PemicCsvReader extends EshopCsvReader {
    
    /**
     * Language codes conversion from Pemic
     * 
     * @var array 
     */
    protected $languageConversions = array(
        'CZE' => 'Český',
        'ESP' => 'Španielský',
        'FR' => 'Francúzsky',
        'GER' => 'Nemecký',
        'GB' => 'Anglický',
        'ITA' => '<PERSON><PERSON><PERSON>sky',
        'SK' => 'Slovenský',
    );
        
    /**
     * List of pairs {ean|supplierId} => {arrayOfPriceFields|TRUE}
     * 
     * @var array
     */
    protected $priceRecords = array();
    
    /**
     * List of pairs {ean|supplierId} => TRUE
     *
     * @var array
     */
    protected $stockRecords = array();
        
    /**
     * Returns options to create instance of PEMIC price CSV file reader
     * 
     * The Price file contains following items:
     * 
     *      [0] => SORTKOD
     *      [1] => EAN
     *      [2] => PARTNERKOD
     *      [3] => PRODCENA
     *      [4] => MENA
     *      [5] => RABAT
     *      [6] => PLATNOSTOD
     *      [7] => PLATNOSTDO
     *      [8] => AKCECENA
     *      [9] => AKCNIRABAT
     *      [10] => AKCEOD
     *      [11] => AKCEDO
     *      [12] => DPH
     *      [13] => PREDBEZNACENA
     * 
     * Returned record has following items:
     * 
     *      array(
     *          'supplier_product_id' => ...,
     *          'ean' => ...,
     *          'price_taxed' => ...,
     *          'tax_rate' => ...,
     *          'margin_rate' => ..., // = % rabatu = (MOC - VOC) / MOC
     *      )
     * 
     * @return array Array of Csv() class options
     */
    protected function getPriceCsvReaderOptions() {
        return array(
            'mode' => 'r',
            'delimiter' => ',',
            'enclosure' => '"',
            'hasHeaders' => true,
            'encoding' => 'CP1250',
            'decimalPoint' => ',',
            'thousandsSeparator' => '',
            'recordFields' => array(
                'supplier_product_id' => array('column' => 0, 'trim' => true, 'convert' => function($value) {
                    return $this->normalizePemicId($value);
                }),             
                'ean' => array('column' => 1, 'trim' => true, 'convert' => function($value) {
                    return $this->normalizeEan($value);
                }),            
                'price_taxed' => array('column' => 3, 'convertType' => 'float', 'convert' => function ($value, $record) {
                    // if price is preliminary (it can change much) then ignore it
                    if (strtolower(trim($record[13])) !== 'ne') {
                        $value = 0.0;
                    }
                    return $value;
                }),
                'tax_rate' => array('column' => 12, 'convertType' => 'int'),
                'margin_rate' => function ($record, $outputRecord, $PriceReader) {
                    // RABAT
                    $margin = 0.0;
                    if (!empty($record[5])) {
                        $margin = $PriceReader->convertType($record[5], 'float');
                    }
                    // AKCNIRABAT
                    $margin2 = 0.0;
                    if (!empty($record[9])) {
                        $margin2 = $PriceReader->convertType($record[9], 'float');
                    }
                    if ($margin2 > $margin) {
                        $margin = $margin2;
                    }
                    return $margin;
                },
            ),
        );
    }
        
    /**
     * Loads $priceRecords property as list of pairs {ean|supplierId} => {arrayOfPriceFields|TRUE}
     * 
     * @param string $priceFile App root relative path to price file
     * @param array $options Following are available:
     *      - 'avoidEans' (array) List of eans to be avoided when loading (caching)
     *          price and stock records. This is just question of memory save (= optimization).
     *          If not provided then all price and stock records are loaded. To make
     *          it really optimal provide this option by reference: 'avoidEans' => &$eans.
     *          Defaults to empty array(). 
     *      - 'keyField' (string) Possible values are 'supplier_product_id', 'ean'. Defaults to 'supplier_product_id'.
     *      - 'onlyKeys' (bool) If TRUE then price records are ommited and the
     *          resulting list consists of pairs {ean|supplierId} => TRUE. Defaults to FALSE.
     */
    protected function loadPriceRecords($priceFile, $options = array()) {
        $defaults = array(
            'avoidEans' => array(),
            'keyField' => 'supplier_product_id',
            'onlyKeys' => false,
        );
        $options = array_merge($defaults, $options);
        // load price records
        $PriceReader = new Csv($priceFile, $this->getPriceCsvReaderOptions());
        while($record = $PriceReader->readRecord()) {
            /*///debug>
            if ($record['ean'] === '9788086055800') {
                $x = 1;
            }
            /*///<debug
            if (
                !empty($options['avoidEans'])
                && !empty($options['avoidEans'][$record['ean']])
            ) {
                continue;
            }
            $key = $record[$options['keyField']];
            if ($options['onlyKeys']) {
                $record = true;
            }
            else {                
                unset($record['ean']);
                //$supplierId = $record['supplier_product_id'];
                unset($record['supplier_product_id']);
            }
            $this->priceRecords[$key] = $record;
        }
    }
    
    /**
     * Returns options to create instance of PEMIC stock CSV file reader
     * 
     * The Stock file contains following items:
     * 
     *      [0] => KODSKLAD
     *      [1] => SORTKOD
     *      [2] => EAN
     *      [3] => MNOZSTVI_KUS
     *      [4] => MNOZSTVI_BAL
     *      [5] => MNOZSTVI_CELK
     * 
     * Returned record has following items:
     * 
     *      array(
     *          'supplier_product_id' => ...,
     *          'ean' => ...,
     *          'stock' => ...,
     *      )
     * 
     * array Array of Csv() class options
     */
    protected function getStockCsvReaderOptions() {
        return array(
            'mode' => 'r',
            'delimiter' => ',',
            'enclosure' => '"',
            'hasHeaders' => true,
            'encoding' => 'CP1250',
            'decimalPoint' => ',',
            'thousandsSeparator' => '',
            'recordFields' => array(
                'supplier_product_id' => array('column' => 1, 'trim' => true, 'convert' => function($value) {
                    return $this->normalizePemicId($value);
                }),             
                'ean' => array('column' => 2, 'trim' => true, 'convert' => function($value) {
                    return $this->normalizeEan($value);
                }),            
                'stock' => array('column' => 3, 'trim' => true, 'convertType' => 'int'),            
            ),
        );
    }
    
    /**
     * Loads $stockRecords property as list of pairs {ean|supplierId} => {stock}
     * 
     * @param string $stockFile App root relative path to stock file
     * @param array $options Following are available:
     *      - 'avoidEans' (array) List of eans to be avoided when loading (caching)
     *          price and stock records. This is just question of memory save (= optimization).
     *          If not provided then all price and stock records are loaded. To make
     *          it really optimal provide this option by reference: 'avoidEans' => &$eans.
     *          Defaults to empty array(). 
     *      - 'keyField' (string) Possible values are 'supplier_product_id', 'ean'. Defaults to 'supplier_product_id'.
     */
    protected function loadStockRecords($stockFile, $options = array()) {
        $defaults = array(
            'avoidEans' => array(),
            'keyField' => 'supplier_product_id',
        );
        $options = array_merge($defaults, $options);
        // load stock records
        $StockReader = new Csv($stockFile, $this->getStockCsvReaderOptions());
        while($record = $StockReader->readRecord()) {
            /*///debug>
            if ($record['ean'] === '9788086055800') {
                $x = 1;
            }
            /*///<debug
            if (
                !empty($options['avoidEans'])
                && !empty($options['avoidEans'][$record['ean']])
            ) {
                continue;
            }
            $key = $record[$options['keyField']];
            if (empty($key)) {
                continue;
            }
            //unset($record['ean']);
            //$supplierId = $record['supplier_product_id'];
            //unset($record['supplier_product_id']);
            $this->stockRecords[$key] = $record['stock'];
        }
    }
    
    protected function normalizePemicId($pemicId) {
        if (empty($pemicId)) {
            return null;
        }
        return str_pad($pemicId, 7, '0', STR_PAD_LEFT);
    }
    
    /**
     * Returns applicable stock amount for the merchant
     * 
     * @param string $keyFieldValue Value of priceRecords and stockRecords 'keyField'.
     *      It means it is either pemic id or ean. If provided and price and/or stock 
     *      records are loaded then used to precise stock amount.
     * 
     * @return int
     */
    protected function getStock($keyFieldValue) {
        $stock = 0;
        // products which are not in price catalogue are not accessible
        // for merchant (regardless to stock amount in stock catalogue)
        if (
            !empty($keyFieldValue)
            && !empty($this->priceRecords)
            && empty($this->priceRecords[$keyFieldValue])
        ) {
            $stock = 0;
        }
        elseif (
            !empty($keyFieldValue)
            && isset($this->stockRecords[$keyFieldValue])
        ) {
            $stock = $this->stockRecords[$keyFieldValue];
        }
        return $stock;
    }
        
    /**
     * Converts Pemic availabilities to output availability values
     * 
     * @param string $pemicAvailability
     * @param string $keyFieldValue Optional. Value of priceRecords and stockRecords 'keyField'.
     *      It means it is either pemic id or ean. If provided and price and/or stock 
     *      records are loaded then used to precise output availability.
     * 
     * @return string
     */
    protected function getAvailability($pemicAvailability, $keyFieldValue = null) {
        $pemicAvailability = strtoupper($pemicAvailability);
        // item which is not in price file is considered to be soldout
        if (
            !empty($keyFieldValue)
            && !empty($this->priceRecords)
            && empty($this->priceRecords[$keyFieldValue])
        ) {
            $availability = 'enum_soldout';
        }
        // item which is on stock is considered to be available
        elseif (
            !empty($keyFieldValue)
            && isset($this->stockRecords[$keyFieldValue])
        ) {
            $availability = 'enum_available';
        }
        else {
            $availability = $this->getSupplierAvailability($pemicAvailability);
        }
        return $availability;
    }
    
    /**
     * Converts Pemic availabilities to output supplier availability values
     * 
     * @param string $pemicAvailability
     * 
     * @return string
     */
    protected function getSupplierAvailability($pemicAvailability) {
        $pemicAvailability = strtoupper($pemicAvailability);
        switch ($pemicAvailability) {
            case 'N':
            case 'D':
                $availability = 'enum_presale';
                break;
            case 'S':
            case 'Z':
                $availability = 'enum_available';
                break;
            case 'R':
                $availability = 'enum_soldout';
                break;
            default:
                $availability = null;
        }
        return $availability;
    }
        
    /**
     * Transfers PEMIC import files
     * 
     * @param string $file One of 'catalogue_full', 'catalogue', 'price', 'stock'
     * @param string $accessId PEMIC accessId. It is value of 'guid' GET param in
     *      PEMIC dowload link.
     * 
     * @return string transfered filename path
     * 
     * @throws Exception
     */
    protected function transferFile($file, $accessId) {
        if (!in_array(
            $file, array('catalogue_full', 'catalogue', 'price', 'stock')
        )) {
            throw new Exception(sprintf('Invalid file specification "%s". Possible values are "catalogue_full", "catalogue", "price", "stock".', $file));
        }
        if (empty($accessId)) {
            throw new Exception('Missing PEMIC download access id');
        }
        $this->ensureTmpDir();
        // get zip file
        $url = 'http://www.pemic-books.cz/PemicCatalog/Default.aspx?guid=' . $accessId . '&file=' . $file;
        $zipFile = File::transfer(
            array($url),
            $this->tmpDir,
            array(
                'unique' => false,
                'name' => $file . '.zip',
            )
        );
        if (empty($zipFile)) {
            throw new Exception(sprintf('Transfer of file %s has failed', $url));
        }
        // get zipped catalogue filename
        $zipFile = $this->tmpDir . DS . $zipFile;
        $Zip = new ZipArchive();
        if ($Zip->open($zipFile) !== true) {
            throw new Exception(sprintf('Invalid zip archive %s', $zipFile));
        }        
        $file = $this->tmpDir . DS . $Zip->getNameIndex(0);
        File::unzip($zipFile, array('delete' => true));
        return $file;
    }
}
