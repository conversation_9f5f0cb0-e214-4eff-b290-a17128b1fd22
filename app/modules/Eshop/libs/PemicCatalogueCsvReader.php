<?php
App::loadLib('Eshop', 'PemicCsvReader');

/**
 * Pemic catalogue CSV reader.
 * Full catalogu file is generated each morning at 6:00 (161005, <EMAIL>, +420 724 079 371)
 * 
 * The Catalogue file contains following items:
 *      [0] => SORTKOD (A)
 *      [1] => SORTNAZEV (B)
 *      [2] => SORTZKNAZEV (C)
 *      [3] => SORTPODNAZEV (D)
 *      [4] => EDICE (E)
 *      [5] => SORTAUTOR (F)
 *      [6] => DOSTUPNOST (G)
 *      [7] => DATPLVYD (H)
 *      [8] => RMVYDANI (I)
 *      [9] => RMDOTISK (J)
 *      [10] => SORTJAZYK (K)
 *      [11] => SORTZNACKA (L)
 *      [12] => VYROBCE (M)
 *      [13] => VYROBCEICO (N)
 *      [14] => SORTDRUH (O)
 *      [15] => SKUPKOD (P)
 *      [16] => SKUPNAZEV (Q)
 *      [17] => VAZBA (R)
 *      [18] => EAN (S)
 *      [19] => ISBN (T)
 *      [20] => POCSTRAN (U)
 *      [21] => POCKSBAL (V)
 *      [22] => VYSKA (W)
 *      [23] => SIRKA (X)
 *      [24] => TLOUSTKA (Y)
 *      [25] => HMOTNOST (Z)
 *      [26] => ANOTACE (AA)
 *      [27] => PICTURE (AB)
 *      [28] => PICTURE_SMALL (AC)
 *      [29] => SORTAUTOR2 (AD)
 * 
 * The Price file contains following items:
 *      [0] => SORTKOD
 *      [1] => EAN
 *      [2] => PARTNERKOD
 *      [3] => PRODCENA
 *      [4] => MENA
 *      [5] => RABAT
 *      [6] => PLATNOSTOD
 *      [7] => PLATNOSTDO
 *      [8] => AKCECENA
 *      [9] => AKCNIRABAT
 *      [10] => AKCEOD
 *      [11] => AKCEDO
 *      [12] => DPH
 *      [13] => PREDBEZNACENA
 * 
 * The Stock file contains following items:
 *      [0] => KODSKLAD
 *      [1] => SORTKOD
 *      [2] => EAN
 *      [3] => MNOZSTVI_KUS
 *      [4] => MNOZSTVI_BAL
 *      [5] => MNOZSTVI_CELK
 * 
 */
class PemicCatalogueCsvReader extends PemicCsvReader {
    
    /**
     * Reads PEMIC catalogue, price and stock file
     * 
     * @param array $options Options of Csv() class plus following:
     *      - 'catalogueFile' (string) Pemic catalogue file. If not provided or empty 
     *          then it is downloaded from Pemic site. In such a case option 'accessId'
     *          must be provided. Defaults to NULL.
     *      - 'priceFile' (string|FALSE) Pemic price file. If not provided or empty 
     *          then it is downloaded from Pemic site. In such a case option 'accessId'
     *          must be provided. If FALSE then stock file is not used and field
     *          'availability' is set less preciselly and fields 'price_taxed' and 
     *          'tax_rate' are not set at all. Defaults to NULL.
     *      - 'stockFile' (string|FALSE) Pemic stock file. If not provided or empty 
     *          then it is downloaded from Pemic site. In such a case option 'accessId'
     *          must be provided. If FALSE then stock file is not used and field
     *          'availability' is set less preciselly. Defaults to NULL.
     *      - 'useFullCatalogue' (bool) Applied only if catalogueFile is downloaded.
     *          If TRUE then a catalogue_full file is downloaded. Defaults to FALSE. 
     *      - 'accessId' (string) PEMIC accessId. It is value of 'guid' GET param in
     *          PEMIC dowload link. Must be provided if some of 'catalogueFile', 
     *          'priceFile' or 'stockFile' is not provided or is empty. Defauts to NULL.
     *      - 'avoidEans' (array) List of eans to be avoided when loading (caching)
     *          price and stock records. This is just question of memory save (= optimization).
     *          If not provided then all price and stock records are loaded. To make
     *          it really optimal provide this option by reference: 'avoidEans' => &$eans.
     *          Defaults to empty array(). 
     */    
    public function __construct($options = array()) {
        $defaults = array(
            'catalogueFile' => null,
            'priceFile' => null,
            'stockFile' => null,
            'useFullCatalogue' => false,
            'accessId' => null,
            'avoidEans' => array(),
        );
        $options = array_merge($defaults, $options);
        
        // check for files and transfer missing ones
        if (empty($options['catalogueFile'])) {
            if ($options['useFullCatalogue']) {
                $options['catalogueFile'] = $this->transferFile('catalogue_full', $options['accessId']);
            }
            else {
                $options['catalogueFile'] = $this->transferFile('catalogue', $options['accessId']);
            }
        }
        if (
            empty($options['priceFile'])
            && $options['priceFile'] !== false
        ) {
            $options['priceFile'] = $this->transferFile('price', $options['accessId']);
        }
        if (
            empty($options['stockFile'])
            && $options['stockFile'] !== false
        ) {
            $options['stockFile'] = $this->transferFile('stock', $options['accessId']);
        }
        // load price and stock records
        if (!empty($options['priceFile'])) {
            $this->loadPriceRecords($options['priceFile'], $options);
        }
        if (!empty($options['stockFile'])) {
            $this->loadStockRecords($options['stockFile'], $options);
        }
        
        parent::__construct($options['catalogueFile'], array_merge($options, array(
            'mode' => 'r',
            'delimiter' => ',',
            'enclosure' => '"',
            'hasHeaders' => true,
            'encoding' => 'CP1250',
            'decimalPoint' => ',',
            'thousandsSeparator' => '',
            'recordFields' => array(
                'supplier_product_id' => array('column' => 0, 'trim' => true, 'convert' => function($value) {
                    return $this->normalizePemicId($value);
                }),             
                'name' => array('column' => 1, 'convertEncoding' => true, 'trim' => true),                   
                'alternative_name' => array('column' => 2, 'convertEncoding' => true, 'trim' => true),                       
                'subtitle' => array('column' => 3, 'convertEncoding' => true, 'trim' => true),                    
                'range' => array('column' => 4, 'convertEncoding' => true, 'trim' => true),              
                'authors' => array('column' => 5, 'convertEncoding' => true, 'explode' => ';', 'trim' => true), 
                'price_taxed' => function($record, $outputRecord) {
                    if (
                        !empty($this->priceRecords[$outputRecord['supplier_product_id']]['price_taxed'])
                        && !empty($this->priceRecords[$outputRecord['supplier_product_id']]['tax_rate'])
                    ) {
                        return $this->priceRecords[$outputRecord['supplier_product_id']]['price_taxed'];
                    }
                    return 0;
                },
                'tax_rate' => function($record, $outputRecord) {
                    if (
                        !empty($this->priceRecords[$outputRecord['supplier_product_id']]['price_taxed'])
                        && !empty($this->priceRecords[$outputRecord['supplier_product_id']]['tax_rate'])
                    ) {
                        return $this->priceRecords[$outputRecord['supplier_product_id']]['tax_rate'];
                    }
                    return 0;
                },
                'margin_rate' => function($record, $outputRecord) {
                    if (
                        !empty($this->priceRecords[$outputRecord['supplier_product_id']]['margin_rate'])
                    ) {
                        return $this->priceRecords[$outputRecord['supplier_product_id']]['margin_rate'];
                    }
                    return 0;
                },
                'stock' => function($record, $outputRecord) {
                    return $this->getStock($outputRecord['supplier_product_id']);
                },            
                'availability' => array('column' => 6, 'trim' => true, 'convert' => function($value, $record, $outputRecord) {
                    return $this->getAvailability($value, $outputRecord['supplier_product_id']);
                }),   
                'long_delivery_time' => array('column' => 6, 'trim' => true, 'convert' => function($value) {
                    return $value === 'Z';
                }),  
                'reprint' => array('column' => 6, 'trim' => true, 'convert' => function($value) {
                    return $value === 'D';
                }),  
                'available_from' => array('column' => 7, 'trim' => true, 'convert' => function($value) {
                    if (                    
                        !empty($value)
                        && substr($value, 0, 19) != '1900-01-01 00:00:00'
                        && preg_match('/^((19|20)[0-9]{2}-[0-9]{2}-[0-9]{2})/i', $value, $matches)
                    ) {
                        return $matches[1];
                    }
                    return null;
                }),
                'shipment_time_off_stock' => function() {
                    return 7; //HARDCODED
                }, 
                'year' => array('column' => 8, 'trim' => true, 'convert' => function($value) {
                    // sometimes it is provided as yyyymm
                    return substr($value, 0, 4);
                }),   
                'language' => array('column' => 10, 'trim' => true, 'convert' => function($value) {
                    // translate the language code to a slovak word
                    if (!empty($this->languageConversions[$value])) {
                        $value = $this->languageConversions[$value];
                    }
                    return $value;
                }),   
                'manufacturer' => array('column' => 11, 'convertEncoding' => true, 'trim' => true),            
                'sortiment' => array('column' => 14, 'convertEncoding' => true, 'trim' => true, 'convert' => function($value) {
                    return preg_replace('/\s+/', ' ', strtolower(Sanitize::nonAscii($value)));
                }),            
                'categories' => array('column' => 15, 'trim' => true, 'convert' => function($value) {
                    if (!empty($value)) {
                        return array($value);
                    }
                    return array();
                }),            
                'binding' => array('column' => 17, 'convertEncoding' => true, 'trim' => true, 'convert' => function($value) {
                    if (!empty($value)) {
                        $value = explode(' ', $value);
                        return array_shift($value);
                    }
                    return null;
                }),            
                'ean' => array('column' => 18, 'trim' => true, 'convert' => function($value) {
                    return $this->normalizeEan($value);
                }),            
                'isbn' => array('column' => 19, 'trim' => true),
                'pages' => array('column' => 20, 'trim' => true, 'convert' => function($value) {
                    if (
                        !empty($value)
                        && Validate::intNumber($value)
                    ) {
                        return $value;
                    }
                    return null;
                }),
                'width' => array('column' => 23, 'trim' => true, 'convertType' => 'float', 'convert' => function($value) {
                    if (
                        empty($value) 
                        || preg_match('/^[0\.]+$/', $value)
                    ) {
                        return null;
                    }
                    return $value;
                }),
                'height' => array('column' => 22, 'trim' => true, 'convertType' => 'float', 'convert' => function($value) {
                    if (
                        empty($value) 
                        || preg_match('/^[0\.]+$/', $value)
                    ) {
                        return null;
                    }
                    return $value;
                }),
                'length' => array('column' => 24, 'trim' => true, 'convertType' => 'float', 'convert' => function($value) {
                    if (
                        empty($value) 
                        || preg_match('/^[0\.]+$/', $value)
                    ) {
                        return null;
                    }
                    return $value;
                }),
                'weight' => array('column' => 25, 'trim' => true, 'convertType' => 'float', 'convert' => function($value) {
                    if (
                        empty($value) 
                        || preg_match('/^[0\.]+$/', $value)
                    ) {
                        return null;
                    }
                    return $value;
                }),
                'media_type' => function($record, $outputRecord) {
                    $sortimentsToMediaType = array(
                        'diar knizni' => 'Knižný diár',
                        'kalendar nastenny' => 'Nastenný kalendár',
                        'kalendar ostatni' => 'Kalendár',
                        'kalendar stolni' => 'Stolný kalendár',
                        'karty' => 'Karty',
                        'kniha' => null,
                        'mapa knizni' => 'Knižná mapa',
                        'mapa nastenna' => 'Nastenná mapa',
                        'mapa skladana' => 'Mapa',
                        'medium CD' => 'CD',
                        'medium DVD' => 'DVD',
                        'stolni hra' => 'Hra',
                    );
                    if (!empty($sortimentsToMediaType[$outputRecord['sortiment']])) {
                        return $sortimentsToMediaType[$outputRecord['sortiment']];
                    }
                    return null;
                },
                'description_import_source' => array('column' => 26, 'trim' => true),
                'image_import_source' => array('column' => 27, 'trim' => true, 'convert' => function($value, $record, $outputRecord) {
                    if (
                        !empty($value)
                        && substr($value, 0, 7) === 'http://'
                    ) {
                        return $value;
                    }
                    elseif (!empty($outputRecord['supplier_product_id'])) {
                        $pemicId = $outputRecord['supplier_product_id'];
                        return 'http://img.pemic.cz/sortimg/' 
                            . substr($pemicId, 0, 3) . '/'
                            . substr($pemicId, 3, 1) . '/' 
                            . substr($pemicId, 4, 1) . '/' 
                            . $pemicId . '-23.jpg';
                    }
                    return null;
                }),
            )
        )));
    }

    /**
     * Reads the next csv record from file. Info line, header row and empty lines are omitted.
     * Returned record has following items:
     * 
     *      array(
     *          'supplier_product_id' => ...,
     *          'name' => ...,
     *          'alternative_name' => ...,
     *          'subtitle' => ...,
     *          'range' => ...,
     *          'authors' => ...,
     *          'price_taxed' => ...,   // present only if priceFile is provided
     *          'tax_rate' => ...,      // present only if priceFile is provided
     *          'margin_rate' => ...,   // present only if priceFile is provided, = % rabatu = (MOC - VOC) / MOC
     *          'stock' => ...,         // if priceFile and/or stockFile provided then resolved more precisely
     *          'availability' => ...,  // if priceFile and/or stockFile provided then resolved more precisely
     *          'long_delivery_time' => ...,
     *          'reprint' => ...,
     *          'available_from' => ...,
     *          'shipment_time_off_stock' => ...,
     *          'year' => ...,
     *          'language' => ...,
     *          'manufacturer' => ...,
     *          'sortiment' => ...,
     *          'categories' => ...,
     *          'binding' => ...,
     *          'ean' => ...,
     *          'isbn' => ...,
     *          'pages' => ...,
     *          'height' => ..., [mm]
     *          'width' => ..., [mm]
     *          'length' => ..., [mm]
     *          'media_type' => ...,
     *          'weigth' => ..., [kg]
     *          'description_import_source' => ...,
     *          'image_import_source' => ...,
     *      )
     * 
     * @return bool|array Next record array. NULL if the end of csv file is reached.
     */
    public function readRecord() {
        $record = parent::readRecord();
        if (!empty($record)) {
            // remove price fields if price file is not provided
            if (empty($this->priceRecords)) {
                unset($record['price_taxed']);
                unset($record['tax_rate']);
                unset($record['margin_rate']);
            }
        }
        return $record;
    }
}
