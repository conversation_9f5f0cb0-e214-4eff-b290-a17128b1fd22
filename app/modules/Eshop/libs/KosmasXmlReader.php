<?php
App::loadLib('Eshop', 'EshopXmlReader');

/**
 * Abstract class to be used as base for Kosmas catalogue and stock readers.
 * Kosmas feed documentation see on https://firma.kosmas.cz/napoveda_export.asp (u: test, p: test)
 */
abstract class KosmasXmlReader extends EshopXmlReader {
    
    protected $recordTagName = 'Product';
    
    protected $decimalPoint = '.';
    
    protected $thousandsSeparator = '';
    
    /**
     * Normalizes kosmas internal id of product
     * 
     * @param string $supplierId
     * 
     * @return int
     */
    protected function normalizeSupplierId($supplierId) {
        return (int)substr($supplierId, 3);
    }

    /**
     * Transfers Kosmas import files
     * 
     * @param string $file One of 'catalogue_full', 'catalogue', 'stock'.
     *          File 'catalogue' contains only new items from last 21 days.
     * @param string $accessKey
     * 
     * @return string transfered filename path
     * 
     * @throws Exception
     */
    protected function transferFile($file, $accessKey) {
        $files = array(
            'catalogue_full' => 'https://firma.kosmas.cz/export_download.asp?verze=1.1&typ=zip&kod=:accessKey:', 
            'catalogue' => 'https://firma.kosmas.cz/export_download.asp?verze=1.1&typ=zip&what=novinky&kod=:accessKey:', 
            'stock' => 'https://firma.kosmas.cz/export_download.asp?typ=avail_zip&kod=:accessKey:',
        );
        if (empty($files[$file])) {
            throw new Exception(sprintf('Invalid file specification: %s. Possible values are "catalogue_full", "catalogue", "stock".', $file));
        }
        if (empty($accessKey)) {
            throw new Exception('Missing Kosmas download access key');
        }
        $url = str_replace(':accessKey:', $accessKey, $files[$file]);
        $this->ensureTmpDir();
        $zipFile = File::transfer(
            array($url),
            $this->tmpDir,
            array(
                'unique' => false,
                'name' => $file . '.zip',
            )
        );
        if (empty($zipFile)) {
            throw new Exception(sprintf('Transfer of file from URL %s has failed', $url));
        }
        // get zipped catalogue filename
        $zipFile = $this->tmpDir . DS . $zipFile;
        $Zip = new ZipArchive();
        if ($Zip->open($zipFile) !== true) {
            throw new Exception(sprintf('Invalid zip archive %s', $zipFile));
        }        
        $file = $this->tmpDir . DS . $Zip->getNameIndex(0);
        File::unzip($zipFile, array('delete' => true));
        return $file;
    }    
}