<?php
App::loadLib('Eshop', 'KosmasXmlReader');

class KosmasCatalogueXmlReader extends KosmasXmlReader {
    
    /**
     * Binding convertions
     * 
     * @var array
     */
    protected  $bindingConvertions = array(
        'BA' => '',
        'BB' => 'pevná',
        'BC' => 'brožovaná',
        'BD' => 'pevná',
        'BE' => 'špirálová',
        'BF' => 'voľné listy',
        'BO' => 'brožovaná',
        'AB' => 'magnetofonová kazeta',
        'AC' => 'CD',
        'AI' => 'DVD',
        'AE' => 'LP, SP',
        'CA' => 'mapa',
        'DB' => 'CD-ROM',
        'PC' => 'kalendár',
        'PD' => 'hracie karty',
        'VJ' => 'VHS kazeta',
        'VO' => 'Blu-ray',
        'ZE' => 'hra',
    );
    
    /**
     * Language codes conversion
     * 
     * @var array 
     */
    protected $languageConversions = array(
        'cze' => 'Český',
        'eng' => 'Anglický',
        'fre' => '<PERSON>an<PERSON><PERSON><PERSON><PERSON>',
        'ger' => 'Nemecký',
        'rus' => 'Ruský',
        'slo' => 'Slovenský',
    );
    
    /**
     * Tax rate conversion
     * 
     * @var array 
     */
    protected $taxRateConversions = array(
        'S' => 20,
        'R' => 20,
        'R2' => 10,
        'Z' => 0,
    );
    
    /**
     * Actual date in format YYYY-MM-DD. Used to skip old available_from dates
     * 
     * @var string
     */
    protected $actualDate = null;
    
    /**
     *
     * @var type 
     */
    protected $czkConversionRate = null;
    
    /**
     * Opens XML file
     * 
     * @param array $options
     *      - 'catalogueFile' (string) App root relative path to XML file. If not provided
     *          then it is downloaded from import provider.
     *      - 'useFullCatalogue' (bool) Applied only if catalogueFile is downloaded.
     *          If TRUE then a catalogue_full file is downloaded. Defaults to FALSE. 
     *      - 'czkConversionRate' (float) E.g. '25.052'. If not set or NULL then defaults 
     *          to EshopCurrency::getActualConversionRate('CZK'). 
     *      - 'accessKey' (string) Kosmas accessKey. The same as username used to login to 
     *          Kosmas B2B backend. Defauts to NULL.
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($options = array()) {
        $defaults = array(
            'catalogueFile' => null,
            'useFullCatalogue' => false,
            'czkConversionRate' => $this->czkConversionRate,
            'accessKey' => null,
        );
        $options = array_merge($defaults, $options);
        
        if (!isset($options['czkConversionRate'])) {
            App::loadModel('Eshop', 'EshopCurrency');
            $Currency = new EshopCurrency();
            $options['czkConversionRate'] = $Currency->getActualConversionRate('CZK');
        }
        $this->czkConversionRate = (float)$options['czkConversionRate'];
        if ($this->czkConversionRate <= 0) {
            throw new Exception(__e(__FILE__, 'Option \'czkConversionRate\' must be a float number greater than 0'));
        }
        
        $this->actualDate = date('Y-m-d');
        
        // check for file and transfer if not provided
        if (empty($options['catalogueFile'])) {
            if ($options['useFullCatalogue']) {
                $options['catalogueFile'] = $this->transferFile('catalogue_full', $options['accessKey']);
            }
            else {
                $options['catalogueFile'] = $this->transferFile('catalogue', $options['accessKey']);
            }
        }
        
        parent::__construct($options['catalogueFile'], $options);        
    }
    
    public function validateRecord($XmlRecord) {
        if (
            isset($XmlRecord->ProductIdentifier)
            && isset($XmlRecord->ProductForm)
            //&& isset($XmlRecord->NumberOfPieces)
            && isset($XmlRecord->Title)
            && isset($XmlRecord->Contributor)
            //&& isset($XmlRecord->Language)
            //&& isset($XmlRecord->NumberOfPages)
            && isset($XmlRecord->OtherText)
            && isset($XmlRecord->MediaFile)
            && isset($XmlRecord->Publisher)
            && isset($XmlRecord->PublishingStatus)
            //&& isset($XmlRecord->PublicationDate)
            && isset($XmlRecord->SupplyDetail)
        ) {
            return true;
        }
        return false;
    }
    
    protected function normalizeDate($date) {
        return substr($date, 0, 4) . '-' . substr($date, 4, 2) . '-' . substr($date, 6, 2);
    }
    
    /**
     * Reads XML record and parses it to following normalized array for files 
     * export.xml/update.xml ( is missing):
     * 
     *      array(
     *          'supplier_product_id' => ...,
     *          'ean' => ...,
     *          'isbn' => ...,
     *          'binding' => ...,
     *          'name' => ...,
     *          'original_name' => ...,
     *          'subtitle' => ...,
     *          'authors' => ...,               // array of authors;
     *          'language' => ...,
     *          'pages' => ...,
     *          'categories' => ...,            // array of Kosmas category ids
     *          'description' => ...,
     *          'image_import_source' => ...,
     *          'manufacturer' => ...,          // manufacturer name
     *          'stock' => ...,
     *          'availability' => ...,          // 'enum_presale', 'enum_available', 'enum_soldout'
     *          'long_delivery_time' => ...,          
     *          'reprint' => null,          
     *          'available_from' => ...,
     *          'shipment_time_off_stock' => ...,
     *          'year' => ...,
     *          'tax_rate' => ...,
     *          'price_taxed' => ..., 
     * 
     *          // used to resolve category conversion
     *          '_languageCode' => ...,          // 'cze', 'eng', 'fre', 'ger', 'rus', 'slo'          
     *      )
     * 
     * ATTENTION: Only czech and slovak books with defined binding converions values 
     * are read from XML catalogue.
     * 
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        $record = array();
        
        // supplier_product_id, ean, isbn
        $record['supplier_product_id'] = $record['ean'] = $record['isbn'] = null;
        foreach ($XmlRecord->ProductIdentifier as $identifier) {
            if ((string)$identifier->ProductIDType === '01' ) {
                $record['supplier_product_id'] = $this->normalizeSupplierId((string)$identifier->IDValue);
            }
            if ((string)$identifier->ProductIDType === '02' ) {
                $record['isbn'] = (string)$identifier->IDValue;
            }
            elseif ((string)$identifier->ProductIDType === '03' ) {
                $record['ean'] = (string)$identifier->IDValue;
                $record['ean'] = $this->normalizeEan($record['ean']);
            }
        }        
        // binding
        $form = $record['binding'] = null;
        if (!empty($XmlRecord->ProductForm)) {
            $form = (string)$XmlRecord->ProductForm;
            $record['binding'] = Sanitize::value($this->bindingConvertions[$form]);                
        }
        // name, original_name
        $record['name'] = $record['original_name'] = null;
        if (!empty($XmlRecord->Title)) {
            foreach ($XmlRecord->Title as $title) {
                if ((string)$title->TitleType === '01') {
                    $record['name'] = (string)$title->TitleText;
                }
                elseif ((string)$title->TitleType === '03') {
                    $record['original_name'] = (string)$title->TitleText;
                }
            }
            if (empty($record['name']) && !empty($record['original_name'])) {
                $record['name'] = $record['original_name'];
            }
        }    
        // subtitle
        $record['subtitle'] = null;
        if (!empty($XmlRecord->Subtitle)) {
            $record['subtitle'] = (string)$XmlRecord->Subtitle;
        }
        // authors
        $record['authors'] = array();
        if (!empty($XmlRecord->Contributor)) {
            foreach ($XmlRecord->Contributor as $contributor) {
                // authors are under role A01
                if ((string)$contributor->ContributorRole === 'A01') {
                    $author = trim((string)$contributor->PersonName);
                    if (!empty($author)) {
                        $record['authors'][] = $author;
                    }
                }
            }
        }
        // language
        $record['language'] = $record['_languageCode'] = null;
        if (!empty($XmlRecord->Language)) {
            $record['_languageCode'] = trim((string)$XmlRecord->Language->LanguageCode);
            $record['language'] = Sanitize::value($this->languageConversions[$record['_languageCode']]);
        }
        // pages
        $record['pages'] = null;
        if (!empty($XmlRecord->NumberOfPages)) {
            $record['pages'] = (int)$XmlRecord->NumberOfPages;
        }
        // categories
        $record['categories'] = array();
        if (!empty($XmlRecord->Subject)) {
            // take only the last kosmas category code as the relevant one
            foreach ($XmlRecord->Subject as $subject) {
                $kosmasCategoryId = (int)$subject->SubjectCode;
            }
            $record['categories'] = array($kosmasCategoryId);
        }
        // description
        $record['description'] = null;
        if (!empty($XmlRecord->OtherText)) {
            $record['description'] = trim((string)$XmlRecord->OtherText->Text);
        }
        // image_import_source
        $record['image_import_source'] = null;
        if (!empty($XmlRecord->MediaFile)) {
            foreach ($XmlRecord->MediaFile as $mediaFile) {
                $mediaTypeCode = (string)$mediaFile->MediaFileTypeCode;
                // cover
                if ($mediaTypeCode == '04') {
                    $record['image_import_source'] = (string)$mediaFile->MediaFileLink;
                    break;
                }
                // cover preview (width 100px)
                elseif ($mediaTypeCode == '07' && empty($record['image_import_source'])) {
                    $record['image_import_source'] = (string)$mediaFile->MediaFileLink;
                }
            }
            if ($record['image_import_source']) {
                //  replace https by http
                $record['image_import_source'] = preg_replace('/^https:/i', 'http:', $record['image_import_source']);
            }
        }
        // manufacturer
        $record['manufacturer'] = null;
        if (!empty($XmlRecord->Publisher)) {
            $record['manufacturer'] = trim((string)$XmlRecord->Publisher->PublisherName);
        }
        // availability
        $record['availability'] = 'enum_available';
        if (!empty($XmlRecord->PublishingStatus)) {
            // 02 - připravovaný titul,
            // 04 - titul již byl vydán a je možné jej objednat,
            // 08 - titul není možné objednat.
            $publishingStatus = (string)$XmlRecord->PublishingStatus;
            if ($publishingStatus === '02') {
                $record['availability'] = 'enum_presale';
            }
            elseif ($publishingStatus === '04') {
                $record['availability'] = 'enum_available';
            }
            elseif ($publishingStatus === '08') {
                $record['availability'] = 'enum_soldout';
            }
        }
        // available_from, year, reprint
        $record['available_from'] = $record['year'] = null;
        $record['reprint'] = false;
        if (!empty($XmlRecord->PublicationDate)) {
            $record['available_from'] = $this->normalizeDate(
                (string)$XmlRecord->PublicationDate
            );
            $record['year'] = substr($record['available_from'], 0, 4);
        }
        if (!empty($XmlRecord->SupplyDetail->Reissue->ReissueDate)) {
            $record['available_from'] = $this->normalizeDate(
                (string)$XmlRecord->SupplyDetail->Reissue->ReissueDate
            );
            if (!$record['year']) {
                $record['year'] = substr($record['available_from'], 0, 4);
            }
            $record['reprint'] = true;
        }
        if ($record['available_from'] < $this->actualDate) {
            $record['available_from'] = null;
        }
        // ...availability, reprint, long_delivery_time
        $record['long_delivery_time'] = false;
        if (!empty($XmlRecord->SupplyDetail->ProductAvailability)) {
            // 21 - titul máme skladem,
            // 22 - titul máme k dispozici na pobočných skladech, ale momentálně není dostupný na hlavním skladě. 
            //      Na objednávku jej doplníme. (od verze 1.1),
            // 31 - titul momentálně není skladem, ale je možné jej doplnit od dodavatele,
            // 33 - titul momentálně není skladem, bude dotisk. Pokud je známo datum dotisku je uvedeno v tagu <ReissueDate> (níže),
            // 40 - titul není skladem a není možné jej doplnit.
            $availability = (string)$XmlRecord->SupplyDetail->ProductAvailability;
            if ($availability === '21') {
                $record['availability'] = 'enum_available';
            }
            elseif ($availability === '22') {
                $record['availability'] = 'enum_available';
                $record['long_delivery_time'] = true;
            }
            elseif ($availability === '31') {
                $record['availability'] = 'enum_available';
                $record['long_delivery_time'] = true;
            }
            elseif ($availability === '33') {
                $record['availability'] = 'enum_presale';
                $record['long_delivery_time'] = true;
                $record['reprint'] = true;
            }
            elseif ($availability === '40') {
                $record['availability'] = 'enum_soldout';
            }
        }
        // stock
        $record['stock'] = null;
        if (isset($XmlRecord->SupplyDetail->Stock->OnHand)) {
            $record['stock'] = (int)$XmlRecord->SupplyDetail->Stock->OnHand;
        } 
        // shipment_time_off_stock
        $record['shipment_time_off_stock'] = 14; //HARDCODED
        // price_taxed
        $record['price_taxed'] = null;
        if (!empty($XmlRecord->SupplyDetail->Price->PriceAmount)) {
            $record['price_taxed'] = (float)$XmlRecord->SupplyDetail->Price->PriceAmount;
            $currency = strtoupper((string)$XmlRecord->SupplyDetail->Price->CurrencyCode);
            if ($currency == 'CZK') {
                $record['price_taxed'] = round($record['price_taxed'] / $this->czkConversionRate, 3);
            }
        }
        // tax_rate
        $record['tax_rate'] = null;
////this is CZ version of tax_rate - unusable for SK         
//        if (!empty($XmlRecord->SupplyDetail->Price->TaxRateCode1)) {
//            $taxRateCode = strtoupper((string)$XmlRecord->SupplyDetail->Price->TaxRateCode1);
//            $record['tax_rate'] = Sanitize::value($this->taxRateConversions[$taxRateCode]);
//        }
        // SK tax_rate (HARDCODED)
        if (!$record['tax_rate']) {
            // fallback to 20% - this should not normaly happen
            if (!$record['ean']) {
                $record['tax_rate'] = 20;
            }
            // if book (EAN starts by 978) but not map (form = CA) then use 10% tax rate
            // otherwise 20%
            elseif (
                substr($record['ean'], 0, 3) === '978'
                && $form !== 'CA'
            ) {
                $record['tax_rate'] = 10;
            }
            else {
                $record['tax_rate'] = 20;
            }
        }

        return $this->applyAvoidFields($record);
    }
    
}
