<?php
App::loadLib('Eshop', 'EshopXmlReader');

/**
 * Abstract class to be used as base for PARTNER TECHNIC catalogue, stock and price readers.
 * 
 * Partner technic has following xml files:
 * 
 * http://obrazky.vsetkyknihy.sk/export.xml - full catalogue of products (with partial price and availablity info)
 * http://obrazky.vsetkyknihy.sk/update.xml - diff catalogue of new or changed products (with partial price and availablity info)
 * http://obrazky.vsetkyknihy.sk/ceny/dostupnost.php?idcode={KEY} - stock/availability catalogue of all products
 * http://obrazky.vsetkyknihy.sk/ceny/ceny_{KEY}.xml - price catalogue of all products
 * 
 * NOTE: Contact: +421 918 644 409, or see http://www.partnertechnic.sk
 */
abstract class PartnerTechnicXmlReader extends EshopXmlReader {
    
    protected $recordTagName = 'KNIHA';
    
    protected $decimalPoint = ',';
    
    protected $thousandsSeparator = '';
    
    /**
     * List of pairs {ean} => {arrayOfPriceFields}
     * 
     * @var array
     */
    protected $priceRecords = array();
    
    /**
     * List of pairs {ean} => {arrayOfAvailabilityFields}
     *
     * @var array
     */
    protected $availabilityRecords = array();
    
    
    /**
     * Loads $priceRecords property as list of pairs {ean} => {arrayOfPriceFields}
     * 
     * @param array $options Following are available:
     *      - 'priceFile' (string) App root relative path to XML file. If not provided
     *          then it is downloaded from import provider.
     *      - 'processEans' (array&) List of eans to be allowed when loading (caching)
     *          price records. This is just question of memory save (= optimization).
     *          If not provided then all price records are loaded. To make it really 
     *          optimal provide this option by reference: 'processEans' => &$eans.
     *          Defaults to empty array(). 
     */
    protected function loadPriceRecords($options = array()) {
        $defaults = array(
            'priceFile' => null,
            'processEans' => array(),
        );
        $options = array_merge($defaults, $options);
        // load price records
        App::loadLib('Eshop', 'PartnerTechnicPriceXmlReader');
        $Reader = new PartnerTechnicPriceXmlReader($options);
        while($record = $Reader->readRecord()) {
            if (
                empty($record['ean'])
                ||
                !empty($options['processEans'])
                && empty($options['processEans'][$record['ean']])
            ) {
                continue;
            }
            $this->priceRecords[$record['ean']] = $record;
        }
    }

    /**
     * Loads $availabilityRecords property as list of pairs {ean} => {arrayOfAvailabilityFields}
     * 
     * @param array $options Following are available:
     *      - 'stockFile' (string) App root relative path to XML file. If not provided
     *          then it is downloaded from import provider.
     *      - 'processEans' (array&) List of eans to be allowed when loading (caching)
     *          price records. This is just question of memory save (= optimization).
     *          If not provided then all price records are loaded. To make it really 
     *          optimal provide this option by reference: 'processEans' => &$eans.
     *          Defaults to empty array(). 
     */
    protected function loadAvailabilityRecords($options = array()) {
        $defaults = array(
            'stockFile' => null,
            'processEans' => array(),
        );
        $options = array_merge($defaults, $options);
        // load price records
        App::loadLib('Eshop', 'PartnerTechnicAvailabilityXmlReader');
        $Reader = new PartnerTechnicAvailabilityXmlReader($options);
        while($record = $Reader->readRecord()) {
            if (
                empty($record['ean'])
                ||
                !empty($options['processEans'])
                && empty($options['processEans'][$record['ean']])
            ) {
                continue;
            }
            $this->availabilityRecords[$record['ean']] = $record;
        }
    }

    
    /**
     * Transfers Partner technic import files
     * 
     * @param string $file One of 'catalogue_full', 'catalogue', 'price', 'stock'
     * @param string $accessKey
     * 
     * @return string transfered filename path
     * 
     * @throws Exception
     */
    protected function transferFile($file, $accessKey) {
        $files = array(
            'catalogue' => 'http://obrazky.vsetkyknihy.sk/update.xml', 
            'catalogue_full' => 'http://obrazky.vsetkyknihy.sk/export.xml', 
            'stock' => 'http://obrazky.vsetkyknihy.sk/ceny/dostupnost.php?idcode=:accessKey:',
            'price' => 'http://obrazky.vsetkyknihy.sk/ceny/ceny_:accessKey:.xml',
        );
        if (empty($accessKey)) {
            $files['stock'] = 'http://obrazky.vsetkyknihy.sk/dostupnost.xml';
        }
        if (empty($files[$file])) {
            throw new Exception(sprintf('Invalid file specification: %s. Possible values are "catalogue_full", "catalogue", "price", "stock".', $file));
        }
        if (
            empty($accessKey)
            && strpos($files[$file], ':accessKey:') !== false
        ) {
            throw new Exception('Missing Partner technic download access key');
        }
        $url = str_replace(':accessKey:', $accessKey, $files[$file]);
        $this->ensureTmpDir();
        $file = File::transfer(
            array($url),
            $this->tmpDir,
            array(
                'unique' => false,
                'name' => $file . '.xml',
            )
        );
        if (empty($file)) {
            throw new Exception(sprintf('Transfer of file from URL %s has failed', $url));
        }
        return $this->tmpDir . DS . $file;
    }    
}