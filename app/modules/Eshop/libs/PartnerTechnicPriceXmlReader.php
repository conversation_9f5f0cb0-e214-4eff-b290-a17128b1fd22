<?php
App::loadLib('Eshop', 'PartnerTechnicXmlReader');

/**
 * Reads price catalogue of Partner technic
 * 
 * For more details see phpDoc of PartnerTechnicXmlReader class
 */
class PartnerTechnicPriceXmlReader extends PartnerTechnicXmlReader {
    
    protected $today = null;
    
    /**
     * Opens XML file
     * 
     * @param array $options
     *      - 'priceFile' (string) App root relative path to XML file. If not provided
     *          then it is downloaded from import provider.
     *      - 'accessKey' (string) Partner technic accessKey. Must be provided if 
     *          'priceFile' is not provided or is empty. Defauts to NULL.
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($options = array()) {
        $defaults = array(
            'priceFile' => null,
            'accessKey' => null,
        );
        $options = array_merge($defaults, $options);
        
        // check for file and transfer if not provided
        if (empty($options['priceFile'])) {
            $options['priceFile'] = $this->transferFile('price', $options['accessKey']);
        }
        
        parent::__construct($options['priceFile'], $options);
    }
    
    public function validateRecord($XmlRecord) {
        if (
            isset($XmlRecord->EAN)
            && isset($XmlRecord->NAZOV)
            && isset($XmlRecord->MOC_EUR)
            && isset($XmlRecord->RABAT)
            && isset($XmlRecord->DPH)
        ) {
            return true;
        }
        return false;
    }
          
    /**
     * Reads XML record and parses it to following normalized array:
     * 
     *      array(
     *          'ean' => ...,
     *          'price_taxed' => ...,           // (float)|NULL
     *          'tax_rate' => ...,              // (int)|NULL
     *          'margin_rate' => ...,           // (float)|NULL
     *      )
     * 
     * @return array|null Array described here above. If there is no next record then NULL. 
     */
    public function readRecord() {
        // get the record object
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        $record = array();
        $record['ean'] = trim((string)$XmlRecord->EAN);
        $record['price_taxed'] = (float)$this->normalizeNumber((string)$XmlRecord->MOC_EUR);
        $record['tax_rate'] = (int)trim((string)$XmlRecord->DPH);
        $record['margin_rate'] = (float)$this->normalizeNumber((string)$XmlRecord->RABAT);
        
        return $this->applyAvoidFields($record);
    }
}