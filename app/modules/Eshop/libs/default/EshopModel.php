<?php

class EshopModel extends Model {
    
    /**
     * Returns unique slug for provided value.
     * 
     * @param string $slug
     * @param array $options Following are available:
     *      - 'id' (int) Record id. If provided then specifies record whose slug
     *          is excluded from comparison. Defaults to NULL.
     *      - 'lang' (string) Lang for which the slug must be unique. If not provided
     *          then the slug is unique for default lang. Defaults to NULL.
     *      - 'avoidValues' (array) Values which should not be used. Defaults to empty array.
     * 
     * @return string
     */    
    public function getUniqueSlug($slug, $options = array()) {
        $defaults = array(
            'lang' => null,
            'id' => null,
            'avoidValues' => array(),
        );
        $options = array_merge($defaults, $options);
        
        $findOptions = array();
        // - if id is specified then apply it
        if ($options['id']) {
            $findOptions['conditions'][$this->name . '.' . $this->primaryKey . ' !='] = $options['id'];
        }
        // - if lang is provided then apply it
        if ($options['lang']) {
            $findOptions['lang'] = $options['lang'];
        }
        // - if avoidValues is provided then apply it
        if ($options['avoidValues']) {
            $findOptions['avoidValues'] = &$options['avoidValues'];
        }
        // slug must be at least 3 chars long to not perplex it with lang code
        $slug = Str::slugize($slug);
        $slug = str_pad($slug, 3, '-');
        $slug = $this->getUniqueFieldValue($this->name . '.slug', $slug, $findOptions);
 
        return $slug;
    }    
} 