<?php
class Eshop { 
    
    /**
     * Was Eshop already initalized by Eshop::init()?
     * 
     * @var bool 
     */
    static protected $initialized = false;
    
    /**
     * Available currencies stored in array like:
     * 
     *      array(
     *          'EUR' => array(
     *              // currency record array
     *          ),
     *          '{code} => array(),
     *          ...
     *      )
     * 
     * @var type 
     */
    static protected $currencies = array();
    
    static protected $actualCurrency = array(
        'decimals' => 2, 
        'symbol' => null, 
        'code' => null,
        'suffix' => null,
    );
    
    /**
     * 
     */
    static public function init() {
        // check for duplicit initialization of Eshop
        if (self::$initialized) {
            return;
        }
        
        // ensure that the default module config is loaded as the first one
        App::getConfig('Eshop');
        
        App::loadLib('Eshop', 'default/EshopModel');
        
        // install Eshop if not installed yet
        if (!IS_INSTALLED) {
            self::install();
        }
        
        App::loadLib('Eshop', 'default/EshopController');
        if (App::$actionType === 'admin') {
            App::setCssFiles('Eshop', array(
                '/admin.css',
            ));
            App::addLastCssFiles(array(
                '/app/modules/Eshop/css/admin.css'
            ));
        }
        else {
            App::setCssFiles('Eshop', array(
                '/main.css',
            ));
            App::addLastCssFiles(array(
                '/app/modules/Eshop/css/main.css'
            ));
        }    
        
        // get currencies and set to system
        App::loadModel('Eshop', 'EshopCurrency');
        $EshopCurrency = new EshopCurrency();
        self::$currencies = $EshopCurrency->findList(array(
            'key' => 'code',
            'order' => array(
                'default DESC',
                'sort ASC',
            ),
        ));
        
        // set actual currency
        self::setActualCurrency();
        
        // load heureka badge script if provided and if we are on a web content page
        if (
            ($heurekaScript = App::getSetting('Eshop', 'EshopOrder.heurekaVerifiedByClientsScript'))
            && App::$requestType === 'slug'    
            && !App::hasScreen(App::$slug)    
        ) {
            App::startJsCapture();
            echo $heurekaScript;
            App::endJsCapture();
        }
        
        // keep track that the initialization has been made already
        self::$initialized = true;
    }
    
    /**
     * Normalizes provided weight in kilograms to units specified by setting Eshop.EshopProduct.weightUnits
     * 
     * @param float|int $weight Weight in kilograms
     * 
     * @return float Weight in units specified by setting Eshop.EshopProduct.weightUnits
     */
    static public function normalizeWeight($weight) {
        static $weightUnits = null;
        if (empty($weight)) {
            return null;
        }
        if ($weightUnits === null) {
            $weightUnits = strtolower(App::getSetting('Eshop', 'EshopProduct.weightUnits'));
        }
        $weight = (float)$weight;
        if ($weightUnits === 'g') {
            $weight *= 1000; 
        }
        elseif ($weightUnits === 't') {
            $weight = Number::removeTrailingZeroDecimals(round($weight / 1000, 3));
        }
        return $weight;
    }
    
    /**
     * Normalizes provided dimension in mm to units specified by setting Eshop.EshopProduct.dimensionsUnits
     * 
     * @param float|int $dimension Width, height, length /depth dimension in mm
     * 
     * @return float Dimension in units specified by setting Eshop.EshopProduct.dimensionsUnits
     */
    static public function normalizeDimension($dimension) {
        static $dimensionsUnits = null;
        if (empty($dimension)) {
            return null;
        }
        if ($dimensionsUnits === null) {
            $dimensionsUnits = strtolower(App::getSetting('Eshop', 'EshopProduct.dimensionsUnits'));
        }
        $dimension = (float)$dimension;
        if ($dimensionsUnits === 'cm') {
            $dimension = Number::removeTrailingZeroDecimals(round($dimension / 10, 1)); 
        }
        elseif ($dimensionsUnits === 'm') {
            $dimension = Number::removeTrailingZeroDecimals(round($dimension / 1000, 3));
        }
        return $dimension;
    }
    
    /**
     * Converts dimensions array to string. E.g array:
     * 
     *      array(
     *          'width' => '165',
     *          'height' => '250',
     *          'length' => '15',
     *      )
     * 
     * is converted to string:
     * 
     *      '165 x 250 x 15 mm'
     * 
     * Dimension units are specified by setting Eshop.EshopProduct.dimensionsUnits
     * 
     * @param array $dimensions
     * 
     * @return string
     */
    static public function getDimensionsString($dimensions) {
        static $dimensionsUnits = null;
        if ($dimensionsUnits === null) {
            $dimensionsUnits = strtolower(App::getSetting('Eshop', 'EshopProduct.dimensionsUnits'));
        }
        $dimensionsString = '';
        if (!empty($dimensions['width'])  && !empty($dimensions['height'])) {
            $dimensionsString = $dimensions['width'] . ' x ' . $dimensions['height'];
            if (!empty($dimensions['length'])) {
                $dimensionsString .= ' x ' . $dimensions['length'];
            }
            $dimensionsString .= ' ' . $dimensionsUnits;
        }
        return $dimensionsString;
    }    
    
    /**
     * List of actual defined currencies
     * 
     * @return array
     */
    static public function getCurrencies() {
        return self::$currencies;
    }
    
    /**
     * Returns property Eshop::$actualCurrency or its specified field
     * 
     * @param string $field Optional. Currency record field name. If specifed then
     *      only value of this field is returned. Defaults to NULL.
     * 
     * @return array|string|NULL Actual currency record array or string|NULL value of specified field.
     */
    static public function getActualCurrency($field = null) {
        if (!empty($field)) {
            if (
                is_array(self::$actualCurrency)
                && array_key_exists($field, self::$actualCurrency)
            ) {
                return self::$actualCurrency[$field];
            }
            else {
                return null;
            }
        }
        return self::$actualCurrency;
    }
    
    /**
     * Sets actual currency in following way:
     *      - according provided $code
     *      - if not provided or if invalid then according value stored in SESSION
     *      - if not available or invalid then according actual App::$lang
     *      - if not available or invalid then default currency is used
     * 
     * @param string $code Optional. Currency code to set the currency accordingly.
     *      If set and valid then also SESSION value is changed
     */
    static public function setActualCurrency($code = null) {
        if (
            !empty($code)
            && ($currency = self::getCurrencyByCode($code))
        ) {
            $_SESSION['_eshop']['actualCurrencyCode'] = $code;
            self::$actualCurrency = $currency;
        }
        elseif (
            !empty($_SESSION['_eshop']['actualCurrencyCode'])
            && ($currency = self::getCurrencyByCode($_SESSION['_eshop']['actualCurrencyCode']))
        ) {
            self::$actualCurrency = $currency;
        }
        elseif (
            ($currency = self::getCurrencyByLang(App::$lang))
        ) {
            self::$actualCurrency = $currency;
        }
        elseif (
            ($currency = self::getDefaultCurrency(App::$lang))
        ) {
            self::$actualCurrency = $currency;
        }
        else {
            throw new Exception(__e(__FILE__, 'Actual currency retrieval failure'));
        }
    }
    
    /**
     * Returns currency record specified by $lang or value of specified $field in record.
     * 
     * @param string $lang Lang code to get currency record for.
     * @param string $field Optional. Currency record field name. If specifed then
     *      only value of this field is returned. Defaults to NULL.
     * 
     * @return array|string|NULL Currency record array or string|NULL value of specified field.
     */
    static public function getCurrencyByLang($lang, $field = null) {
        $languages = App::getPropertyLanguages();
        $langCurrency = null;
        $currencyCode = null;
        foreach ($languages as $language) {
            if ($language['lang'] === $lang) {
                $currencyCode = $language['currency_code'];
                break;
            }
        }
        if (
            !empty($currencyCode)
            && !empty(self::$currencies[$currencyCode])
        ) {
            $langCurrency = self::$currencies[$currencyCode];
        }
        if (!empty($field)) {
            if (
                is_array($langCurrency)
                && array_key_exists($field, $langCurrency)
            ) {
                $langCurrency = $langCurrency[$field];
            }
            else {
                $langCurrency = null;
            }
        }
        return $langCurrency;
    }
    
    /**
     * Returns currency array for provided currency $code
     * 
     * @param string $code Currency ISO 4217 code, e.g. EUR, CZK
     * 
     * @return array|NULL Currency array containing all fields retrieved from DB.
     *      If no existing currency found for provided $code then NULL is returned. 
     */
    static public function getCurrencyByCode($code) {
        $codeCurrency = null;
        if (!empty(self::$currencies[$code])) {
            $codeCurrency = self::$currencies[$code];
        }
//        foreach (self::$currencies as $currency) {
//            if ($currency['code'] === $code) {
//                $codeCurrency = $currency;
//                break;
//            }
//        }
        return $codeCurrency;
    }
    
    /**
     * Returns default currency array
     * 
     * @return array|NULL Currency array containing all fields retrieved from DB.
     *      If no default currency found then NULL is returned. 
     */
    static public function getDefaultCurrency() {
        $defaultCurrency = null;
        foreach (self::$currencies as $currency) {
            if (!empty($currency['default'])) {
                $defaultCurrency = $currency;
                break;
            }
        }
        return $defaultCurrency;
    }
    
    /**
     * Formats provide number into price
     * 
     * @param mixed $number Integer or float number
     * @param array $options Following are available:
     *      - 'decimals' (int) Defaults to Eshop::$actualCurrency['decimals'].
     *      - 'suffix' (string) Suffix added to integer prices, e.g. ',-' in '5,-'. 
     *          Defaults to Eshop::$actualCurrency['suffix'].
     *      - 'symbol' (string) Defaults to Eshop::$actualCurrency['symbol'].
     *      - 'nbsp' (bool) If TRUE then spaces are replaced by nonbreakable space 
     *          (its html entity - &nbsp;). Defaults to TRUE.
     * 
     * An alternative signature can be used:
     * 
     * @param mixed $number Integer or float number
     * @param string $currencyCode Currency ISO 4217 code, e.g. EUR, CZK
     * 
     * @return string
     * @throws Exception on invalid $currencyCode
     * 
     * @todo - replace this by Money::formatPrice(), see todo (160429)
     */
    static public function formatPrice($number, $options = array()) {
        $defaults = array(
            'nbsp' => true,
        );
        if (is_string($options)) {
            $options = self::getCurrencyByCode($options);
            if (empty($options)) {
                throw new Exception(__e(__FILE__, 'Invalid currency code %s', $options));
            }
        }
        else {
            $options = array_merge(self::$actualCurrency, $options);
        }
        $options = array_merge($defaults, $options);
        // round the number before checking if it is integer - because after rounding it can be integer
        $number = round($number, $options['decimals']);
        if (
            Validate::intNumber($number, true)
            && !empty($options['suffix'])
        ) {
            $number = Number::removeTrailingZeroDecimals($number);
            $number .= $options['suffix'];
        }
        else {
            $number = App::formatNumber($number, $options['decimals'], $options['nbsp']);
        }
        if (!empty($options['symbol'])) {
            if ( $options['code'] == 'USD') {
                $number = $options['symbol'] . Number::removeTrailingZeroDecimals($number);
            } else {
                $number .= '&nbsp;' . $options['symbol'];
            }
        }
        return $number;
    }
    
    /**
     * Returns obvious list of options for country selects containing pairs {isoCode2} => {endonym}
     * Only countries used by eshop are included.
     * 
     * @return array
     */
    static public function getCountriesSelectList() {
        static $countries = null;
        if ($countries === null) {
            App::loadModel('App', 'Country');
            $Country = new Country();
            $countries = $Country->getSelectList(array(
                'fields' => array('name'),
                'conditions' => array(
                    'iso_code_2' => array(
                        // european EU countries
                        'AT',
                        'BE',
                        'BG',
                        'HR',
                        'CY',
                        'CZ',
                        'DK',
                        'EE',
                        'FI',
                        'FR',
                        'DE',
                        'GR',
                        'HU',
                        'IE',
                        'IT',
                        'LV',
                        'LT',
                        'LU',
                        'MT',
                        'NL',
                        'PL',
                        'PT',
                        'RO',
                        'SK',
                        'SI',
                        'ES',
                        'SE',
                        'GB',
                        // european non EU countries
                        'UA',
                        'RU',
                        'BY',
                        'CH',
                        'RS',
                        'NO',
                        'MD',
                        'BA',
                        'AL',
                        'MK',
                        'ME',
                    ),
                ),
            ));
            Arr::sortStrings($countries, array(
                'associative' => true
            ));
        }
        return $countries;
    }    
    
    /**
     * Calculates discount rate from provided margin rate (rabat) and markup rate (marža).
     * 
     * ATTENTION: It's up to you to check if returned value is a fair, it means in interval <0, 100>.
     * 
     * http://www.uspesne-podnikanie.sk/marza-a-rabat-aky-je-v-tom-rozdiel
     * https://www.inflowinventory.com/blog/calculate-margin-vs-markup/
     * margin (en) -> rabat (sk) = (MOC - VOC) / MOC; MOC je plná maloobchodná (predajná) cena
     * markup (en) -> marža (sk) = (ZMOC - VOC) / VOC; ZMOC je zľavnená maloobchodná (predajná) cena
     * 
     * @param float $marginRate Margin percents
     * @param float $markupRate Markup percents
     * 
     * @return float Discount percents
     */
    static public function getDiscountRate($marginRate, $markupRate) {
        return 100 - (100 - $marginRate) * (100 + $markupRate) / 100;
    }
    
    /**
     * Calculates margin rate (% rabatu) from provided discount and markup rate (% marže).
     * 
     * ATTENTION: It's up to you to check if returned value is a fair, it means in interval <0, 100>.
     * 
     * See also phpDoc of Eshop::getDiscountRate()
     * 
     * @param float $discountRate Discount percents
     * @param float $markupRate Markup percents
     * 
     * @return float Margin percents
     */
    static public function getMarginRate($discountRate, $markupRate) {
        return 100 - 100 * (100 - $discountRate) / (100 + $markupRate);
    }    
    
    /**
     * Calculates markup rate (% marže) from provided discount rate (% zľavy) and 
     * margin rate (% rabatu).
     * 
     * ATTENTION: It's up to you to check if returned value is a fair, it means greater than 0.
     * 
     * See also phpDoc of Eshop::getDiscountRate()
     * 
     * @param float $discountRate Discount percents
     * @param float $marginRate Margin percents
     * 
     * @return float Markup percents
     */
    static public function getMarkupRate($discountRate, $marginRate) {
        return 100 * (100 - $discountRate) / (100 - $marginRate) - 100;
    }
    
    /**
     * Calculates purchase / wholesale price (VOC) for provided full price 
     * for customer (MOC) and margin rate (% rabatu).
     * 
     * See also phpDoc of Eshop::getDiscountRate()
     * 
     * @param float $price Full price for customer
     * @param float $marginRate Margin percents
     * 
     * @return float Purchase price
     */
    static public function getPurchasePrice($price, $marginRate) {
        return $price - $price * $marginRate / 100;
    }
    
    /**
     * Calculates markup (marža, absolútna hodnota) for provided full price 
     * for customer (MOC), margin rate (% rabatu) and discount rate (% zľavy).
     * 
     * @param float $price Full price for customer
     * @param float $discountRate Discount percents
     * @param float $marginRate Margin percents
     * 
     * @return float
     */
    static public function getMarkup($price, $discountRate, $marginRate) {
        $purchasePrice = self::getPurchasePrice($price, $marginRate);
        $markupRate = self::getMarkupRate($discountRate, $marginRate);
        return $purchasePrice * $markupRate / 100;
    }
        
    /**
     * Installs the Eshop, means this method does some processing
     * which sould be done on the very first launch of Eshop
     */
    static protected function install() {
        if (IS_INSTALLED) {
            return;
        }
        
        // ensure EshopOrder::$checkoutFileStore folder and its .htaccess file
        $Order = App::loadModel('Eshop', 'EshopOrder', true);
        $checkoutFileStore = $Order->getPropertyCheckoutFileStore();
        File::ensurePath($checkoutFileStore);
        file_put_contents(
            ROOT . DS . File::normalizePath($checkoutFileStore) . DS . '.htaccess', 
            'Order Deny,Allow' . PHP_EOL .
            'Deny from none' . PHP_EOL .
            'Allow from all'
        );
    }
        
//    /**
//     * 
//     */
//    static public function afterLogin() {
//        
//    }

//    /**
//     * 
//     */
//    static public function afterLogout() {
//        
//    }
    
    /**
     * 
     */
    static public function shutdown() {
        
    }
}
