<?php
/**
 * Use this class if you need to implement module-wide processing common 
 * for all module controllers (or at least for that which extends this class).
 * 
 * Class EshopController() is preloaded in Eshop::init(), but if not for 
 * some reasons, then use following pattern to create new module controller:
 *
 *      App::loadLib('Eshop', 'EshopController');
 *
 *      class Posts extends EshopController {
 *  
 *      }
 */
class EshopController extends SmartController {
    
    /*/
    public function loadAction($action, $params, $data, $args, $allowOriginComments = true) {
        $return = parent::loadAction($action, $params, $data, $args, $allowOriginComments);
        if (
            $this->name === 'EshopExports'
            || $this->name === 'EshopImports'
        ) {
            App::log('ExportsImportsDebug', App::$url, array(
                'var' => $return,
            ));
        }
        return $return;
    }
    /*/
    
////if needed use this in your implementation of child classes
//    protected $model = 'EshopMyModel';
//    
//    /**
//     * Allow the model methods hinting in IDE
//     * @var EshopMyModel
//     */
//    protected $Model;

} 
