<?php
App::loadLib('Eshop', 'FrankanaApiReader');

/**
 * Reads product details from Frankana API
 * 
 * See phpDoc of FrankanaApiReader class
 *
 * XML document obtained from API contains following values translated from German:
 *      - sku: code (internal code)
 *      - name_pim_attribute: name
 *      - beschreibung: description
 *      - endverbraucherpreis_EUR: price_taxed
 *      - cross_sell_skus: related_products
 */
class FrankanaProductApiReader extends FrankanaApiReader {
    
    protected $recordTagName = 'product';
    
    protected $decimalPoint = ',';
    
    protected $thousandsSeparator = '';
    
    /**
     * Opens XML file
     * 
     * @param array $options Following are available:
     *      - 'apiUrlBase' (string) Defaults to App::getSetting('Eshop', 'frankana.apiUrlBase').
     *      - 'apiKey' (string) Defaults to App::getSetting('Eshop', 'frankana.apiKey').
     *      - 'avoidFields' (array) List of fields which should not be returned by reader, 
     *          e.g. array('description', 'manufacturer'). See method ::applyAvoidFields()
     *      - 'apiResponseXmlFile' (string) App root relative path to Frankana API
     *          response XML file. Used only for development and debug purposes
     *          to save the time, as the Frankana API response time is rount 20 seconds.
     *
     * @throws Exception on failure
     */
    public function __construct($options = array()) {
        parent::__construct('/api/v1/product', $options);
    }

    public function validateRecord($XmlRecord) {
        if (
            isset($XmlRecord->sku)
            && isset($XmlRecord->name_pim_attribute->de_DE)
            && isset($XmlRecord->beschreibung->de_DE)
            && isset($XmlRecord->endverbraucherpreis_EUR)
            && isset($XmlRecord->assets)
            && isset($XmlRecord->cross_sell_skus)
        ) {
            return true;
        }
        return false;
    }
        
    /**
     * Reads the next record from Frankana products XML file. Returned record has following items:
     *
     *      array(
     *          'code' => ...,
     *          'name' => ...,
     *          'description' => ...,
     *          'price_taxed' => ...,
     *          'tax_rate' => ...,
     *          'related_products' => ...,
     *          'image_import_source' => ...,
     *          'gallery_image_import_sources' => array(...),  // product gallery images
     *          'translated' => 0, // could be project secific (there are 'de' texts to be translated to 'sk')
     *      )
     *
     * @return bool|array Next record array. NULL if the end of records is reached.
     * 
     * @throws Exception on invalid data.
     */
    public function readRecord() {
        $XmlRecord = parent::readRecord();
        // if no next then return NULL
        if (!$XmlRecord) {
            return null;
        }
        // convert the XML oject to normalized array
        $product = array();
        
        $product = array();
        $product['code'] = (string)$XmlRecord->sku;
        $product['name'] = (string)$XmlRecord->name_pim_attribute->de_DE;
        $product['description'] = (string)$XmlRecord->beschreibung->de_DE;
        $product['price_taxed'] = (float)$this->normalizeNumber((string)$XmlRecord->endverbraucherpreis_EUR);
        $product['tax_rate'] = 20; // hardcoded    
        $product['related_products'] = (string)$XmlRecord->cross_sell_skus;
        // images
        $product['image_import_source'] = null;
        $product['gallery_image_import_sources'] = array();
        $images = explode(', ', (string)$XmlRecord->assets);
        if (!empty($images)) {
            if (count($images) > 1) {
                $product['image_import_source'] = array_shift($images);
                $product['gallery_image_import_sources'] = $images;
            }
            else {
                $product['image_import_source'] = array_shift($images);
            }
        }
        $product['translated'] = 0;

        return $this->applyAvoidFields($product);
    }
}
