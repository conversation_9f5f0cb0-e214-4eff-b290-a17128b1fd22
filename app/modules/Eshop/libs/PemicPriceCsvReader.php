<?php
App::loadLib('Eshop', 'PemicCsvReader');

/**
 * Pemic price CSV reader.
 * 
 * The Price file contains following items:
 * 
 *      [0] => SORTKOD
 *      [1] => EAN
 *      [2] => PARTNERKOD
 *      [3] => PRODCENA
 *      [4] => MENA
 *      [5] => RABAT
 *      [6] => PLATNOSTOD
 *      [7] => PLATNOSTDO
 *      [8] => AKCECENA
 *      [9] => AKCNIRABAT
 *      [10] => AKCEOD
 *      [11] => AKCEDO
 *      [12] => DPH
 *      [13] => PREDBEZNACENA
 * 
 */
class PemicPriceCsvReader extends PemicCsvReader {
    
    /**
     * Reads PEMIC catalogue, price and stock file
     * 
     * @param array $options Options of Csv() class plus following:
     *      - 'priceFile' (string) Pemic price file. If not provided or empty 
     *          then it is downloaded from Pemic site. In such a case option 'accessId'
     *          must be provided. Defaults to NULL.
     *      - 'accessId' (string) PEMIC accessId. It is value of 'guid' GET param in
     *          PEMIC dowload link. Must be provided if some of 'catalogueFile', 
     *          'priceFile' or 'stockFile' is not provided or is empty. Defauts to NULL.
     */    
    public function __construct($options = array()) {
        $defaults = array(
            'priceFile' => null,
            'accessId' => null,
        );
        $options = array_merge($defaults, $options);
        
        // check for files and transfer missing ones
        if (empty($options['priceFile'])) {
            $options['priceFile'] = $this->transferFile('price', $options['accessId']);
        }
        
        parent::__construct($options['priceFile'], array_merge($options, $this->getPriceCsvReaderOptions()));
    }

    /**
     * Reads the next csv record from file. Info line, header row and empty lines are omitted.
     * Returned record has following items:
     * 
     *      array(
     *          'supplier_product_id' => ...,
     *          'ean' => ...,
     *          'price_taxed' => ...,
     *          'tax_rate' => ...,
     *          'margin_rate' => ..., // = % rabatu = (MOC - VOC) / MOC
     *      )
     * 
     * @return bool|array Next record array. NULL if the end of csv file is reached.
     */
    public function readRecord() {
        return parent::readRecord();
    }
}
