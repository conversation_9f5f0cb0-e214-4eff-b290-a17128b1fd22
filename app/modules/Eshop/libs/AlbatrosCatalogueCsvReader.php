<?php
App::loadLib('Eshop', 'EshopCsvReader');

/**
 * ALBATROS .csv file reader
 * 
 * The file contains following columns: 
 * 
 *      [0] => TITLE
 *      [1] => AUTHOR
 *      [2] => EAN
 *      [3] => PRICE
 *      [4] => VAT RATE
 *      [5] => MANUFACTURER
 * 
 */
class AlbatrosCatalogueCsvReader extends EshopCsvReader {
    
    /**
     * Reads existing products stock amount exported from MRP as csv file
     * 
     * @param string $file
     * @param array $options Options of Csv() class
     */    
    public function __construct($file, $options = array()) {
        parent::__construct($file, array_merge($options, array(
            'mode' => 'r',
            'delimiter' => "\t",
            'enclosure' => '"',
            'hasHeaders' => true,
            'encoding' => 'CP1250',
            'decimalPoint' => ',',
            'thousandsSeparator' => '',
            'recordFields' => array(
                'name' => array('column' => 0, 'convertEncoding' => true, 'trim' => true),  
                'authors' => array('column' => 1, 'convertEncoding' => true, 'explode' => ',', 'trim' => true),                 
                'ean' => array('column' => 2, 'trim' => true, 'convert' => function($value) {
                    return $this->normalizeEan($value);
                }),    
                'price_taxed' => array('column' => 3, 'trim' => true, 'convertType' => 'float'),                 
                'tax_rate' => array('column' => 4, 'trim' => '%', 'convertType' => 'int'), 
                'manufacturer' => array('column' => 5, 'convertEncoding' => true, 'trim' => true),       
                'availability' => function() {
                    // all items included in catalogue are available
                    return 'enum_available';
                },       
                'shipment_time_off_stock' => function() {
                    // default for albatros
                    return 5;
                },       
            ),
        )));
    }
        
    /**
     * Reads the next csv record from file. Info line, header row and empty lines are omitted.
     * Returned record has following items:
     * 
     *      array(
     *          'name' => ...,
     *          'authors' => ...,
     *          'ean' => ...,
     *          'price_taxed' => ...,
     *          'tax_rate' => ...,
     *          'manufacturer' => ...,
     *          'availability' => 'enum_available', // all items included in catalogue are available
     *          'shipment_time_off_stock' => 5,     // default for albatros
     *      )
     * 
     * @return bool|array Next record array. NULL if the end of csv file is reached.
     */
    public function readRecord() {
        return parent::readRecord();
    }
}
