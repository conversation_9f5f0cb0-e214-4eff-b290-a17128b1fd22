<?php
App::loadLib('Eshop', 'EshopCsvReader');

/**
 * Alterego knihy.csv file reader
 * 
 * The knihy.csv file contains following columns: 
 *      [0] => CODE
 *      [1] => TITLE
 *      [2] => CATEGORY CODE
 *      [3] => PRICE
 *      [4] => AUTHOR
 *      [5] => MANUFACTURER
 *      [6] => A<PERSON><PERSON><PERSON>ILITY
 *      [7] => AVAILABILEFROM
 *      [8] => DISCOUNT RATE
 *      [10] => RANGE
 *      [11] => VAT RATE
 */
class AlteregoCatalogueCsvReader extends EshopCsvReader {
    
    /**
     * List of annotations having pair {productCode} => {annotationRecord}
     * 
     * @var (array)
     */
    protected $annotations = array();
    
    /**
     * Reads existing products stock amount exported from MRP as csv file
     * 
     * @param array $options Options of Csv() class plus following:
     *      - 'catalogueFile' (string) App root relative path to Alterego catalogue file. 
     *          Defaults to '/userfiles/files/import/knihy.csv'.
     *      - 'annotationFile' (string) App root relative path to Alterego annotation file. 
     *          Defaults to '/userfiles/files/import/anotacie.txt'.
     *      - 'imagesDirectory' (string) App root relative path to Alterego images directory. 
     *          Defaults to '/userfiles/files/import/images'.
     */    
    public function __construct($options = array()) {
        $defaults = array(
            'catalogueFile' => '/userfiles/files/import/knihy.csv',
        );
        $options = array_merge($defaults, $options);
        
        App::loadLib('Eshop', 'AlteregoAnnotationCsvReader');
        $AnnotationReader = new AlteregoAnnotationCsvReader($options);
        while ($annotation = $AnnotationReader->readRecord()) {
            $productCode = $annotation['code'];
            unset($annotation['code']);
            $this->annotations[$productCode] = $annotation;
        }
        
        parent::__construct($options['catalogueFile'], array_merge($options, array(
            'mode' => 'r',
            'delimiter' => ";",
            'enclosure' => '"',
            'hasHeaders' => false,
            'encoding' => 'CP1250',
            'decimalPoint' => '.',
            'thousandsSeparator' => '',
            'recordFields' => array(
                'code' => array('column' => 0, 'trim' => true),
                'name' => array('column' => 1, 'convertEncoding' => true, 'trim' => true),  
                'categories' => array('column' => 2, 'trim' => true, 'convert' => function($value) {
                    $value = (int)$value;
                    if (!empty($value)) {
                        return array($value);
                    }
                    return array();
                }),
                'price_taxed' => array('column' => 3, 'trim' => true, 'convertType' => 'float'),   
                'authors' => array(
                    'column' => 4, 
                    'convertEncoding' => true, 
                    'explode' => ',', 
                    'trim' => true, 
                    'convert' => function($value) {
                        foreach ($value as $i => $v) {
                            if (strpos(Str::slugize($v), 'neuvedeny') !== false) {
                                unset($value[$i]);
                            }
                        }
                        return $value;
                    }
                ),                 
                'manufacturer' => array('column' => 5, 'convertEncoding' => true, 'trim' => true),
                'stock' => array('column' => 9, 'trim' => true, 'convertType' => 'int'),
                // availability codes
                //A - anotovany titul bez zvlastneho oznacenia, na nasom internom v sklade 
                //B - zablokovany na predaj, na stranke vyobrazeny (vypredany, na sklade internom ani externom nedostupny)
                //C - ocakavany titul v priprave, nepouzivali sme tuto kategoriu, malo to byt na predobjednavky mesiac a viac dopredu 
                //D - zablokovany titul a na stranke neviditelny (drzany len kvoli historii objednavok)
                //F - titul na externom sklade pemic atd, dostupny do 7 dni
                //G - titul na externom sklade kosmas, inform zilina atd, dostupny do 21 dni (mozeme zmenit na kratsiu lehotu)
                //J - akciovy titul na sklade, - mali sme to kvoli karuselovemu banneru - to asi nebudeme pouzivat
                //K - akciovy titul na EXTERNOM sklade, dostupny do 7 dni - mali sme to kvoli karuselovemu banneru - to asi nebudeme pouzivat
                //M - novinky, ktore este nemame na sklade, dostupne v lehote 7 dni
                //N - novinky, ktore mame na sklade ihned dostupne
                //V a W - vypredajove tituly - mozeme ujednotit len na napriklad W
                //X - titul nespracovany, nezanotovany, nedokonceny a nemal by nam vbehnut do databazy, ale stane sa...
                'availability' => array(
                    'column' => 6, 
                    'convertEncoding' => true, 
                    'trim' => true, 
                    'convert' => function($value, $record, &$outputRecord) {
                        $value = strtoupper($value);
                        $availabilityToShippmentTimeOffstock = array(
                            'F' => 7,
                            'G' => 14,
                            'K' => 7,
                            'M' => 7,
                        );
                        $outputRecord['shipment_time_off_stock'] = Sanitize::value(
                            $availabilityToShippmentTimeOffstock[$value]
                        );
                        $outputRecord['active'] = null;
                        $availability = null;
                        if (
                            $value === 'A'
                            || $value === 'J'
                            || $value === 'N'
                            || $value === 'V' // no more used
                        ) {
                            $availability = 'enum_available';
                            // there is no exact stock amount info
                            // put just some big amount
                            //$outputRecord['stock'] = 10000;
                        }
                        elseif ($value === 'B') {
                            $availability = 'enum_soldout';
                            //$outputRecord['stock'] = 0;
                        }
                        elseif ($value === 'D') {
                            $availability = 'enum_soldout';
                            //$outputRecord['stock'] = 0;
                            $outputRecord['active'] = false;
                        }
                        elseif (
                            $value === 'F'
                            || $value === 'G'
                            || $value === 'K' // no more used
                        ) {
                            $availability = 'enum_available';
                            //$outputRecord['stock'] = 0;
                        }
                        elseif (
                            $value === 'M'
                            || $value === 'C' // no more used
                        ) {
                            $availability = 'enum_presale';
                            //$outputRecord['stock'] = 0;
                        }
                        elseif ($value === 'X') {
                            // this is internal value used only by reader itself
                            // these records are skipped / ignored in ::readRecord()
                            $availability = 'X';
                        }
                        return $availability;
                    }
                ),
                'available_from' => array('column' => 7, 'trim' => true, 'convert' => function($value) {
                    if ($value) {
                        return '20' . implode('-', array_reverse(explode('.', $value)));
                    }
                    return null;
                }),   
                'discount_rate' => array('column' => 8, 'trim' => true, 'convertType' => 'float'),
                'range' => array('column' => 10, 'convertEncoding' => true, 'trim' => true, 'convert' => function($value) { 
                    if ($value === 'XYYX') {
                        return null;
                    }
                    return $value;
                }), 
                'tax_rate' => array('column' => 11, 'trim' => true, 'convertType' => 'int'),
            ),
        )));
    }
        
    /**
     * Reads the next csv record from file. Info line, header row and empty lines are omitted.
     * Returned record has following items:
     * 
     *      array(
     *          // fields retrieved from knihy.csv
     *          'code' => ...,
     *          'name' => ...,
     *          'categories' => ...,
     *          'price_taxed' => ...,
     *          'tax_rate' => ...,
     *          'discount_rate' => ...,
     *          'authors' => ...,
     *          'manufacturer' => ...,
     *          'availability' => ...,
     *          'available_from' => ...,
     *          'range' => ...,
     * 
     *          // fields retrieved from anotacie.txt
     *          'binding' => ...,
     *          'pages' => ...,
     *          'ean' => ...,
     *          'width' => ...,
     *          'height' => ...,
     *          'length' => ...,
     *          'year' => ...,
     *          'description' => ...,
     *          'language' => ...,
     *          'image_import_source' => ...,
     *          'gallery_image_import_sources' => ...,
     *      )
     * 
     * @return bool|array Next record array. NULL if the end of csv file is reached.
     */
    public function readRecord() {
        do {
            $record = parent::readRecord();
        }
        while(
            $record 
            && (
                $record['availability'] === 'X'
                || empty($this->annotations[$record['code']])
            )
        );
        if ($record) {
            $record = array_merge($record, $this->annotations[$record['code']]);
        }
        return $record;
    }
}
