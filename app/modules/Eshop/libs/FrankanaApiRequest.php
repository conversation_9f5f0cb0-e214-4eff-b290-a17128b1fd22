<?php

/**
 * Class containing functionality used in Frankana readers and writers
 * 
 * To start communicate with API cet credentials in respective settings (see constructor
 * options defaults)
 */
class FrankanaApiRequest {

    /**
     * Frankana API URL
     *
     * @var string
     */
    protected $apiUrl = null;
    
    /**
     * Frankana API key
     *
     * @var string
     */
    protected $apiKey = null;

    /**
     * @param string $apiUrlPath Url path as it is provided in API documentation (starting by /api/...)
     * @param array $options Following are available:
     *      - 'apiUrlBase' (string) Defaults to App::getSetting('Eshop', 'frankana.apiUrlBase').
     *      - 'apiKey' (string) Defaults to App::getSetting('Eshop', 'frankana.apiKey').
     *
     * @throws Exception on failure
     */
    public function __construct($apiUrlPath, $options = array()) {
        $options = array_merge(array(
            'apiUrlBase' => App::getSetting('Eshop', 'frankana.apiUrlBase'),
            'apiKey' => App::getSetting('Eshop', 'frankana.apiKey'),
        ), $options);
        
        $this->apiKey = $options['apiKey'];
        $this->apiUrl = $this->getApiUrl($options['apiUrlBase'], $apiUrlPath);
    }

    /**
     * Returns API url for provided URL path
     *
     * @param string $apiUrlBase API Url base
     * @param string $apiUrlPath Url path as it is provided in API documentation (starting by /api/...)
     *
     * @return string
     *
     * @throws Exception on failure
     */
    protected function getApiUrl($apiUrlBase, $apiUrlPath) {
        if (preg_match('#https?://[^/]+/#i', $apiUrlPath)) {
            throw new Exception(__e(__FILE__, 'Invalid API URL path. Only part relative to %s must be provided', $apiUrlBase));
        }
        return rtrim($apiUrlBase, '/') . '/' . ltrim($apiUrlPath, '/');
    }

    /**
     * Does API request for specified url
     *
     * @param string $apiUrl
     * @param array $options App::request() options:
     *      - 'url' (string) Defaults to $this->apiUrl
     *      - 'method' (string) Defaults to 'GET'.
     *
     * @return string Api response XML string
     *
     * @throws Exception on failure
     */
    public function requestApi($options = array()) {
        $options = array_merge(array(
            'url' => $this->apiUrl,
            'method' => 'get',
        ), $options);
        $options['header'] = array(
            'Authorization: Bearer ' . $this->apiKey,
        );
        // turn off nested get params
        // See in phpDoc of App::getUrl() the diffecence between using 0 and FALSE
        $options['allowNestedGetParams'] = 0;
        $options['getInfo'] = true;
        $responseXml = App::request($options['url'], $options, $info);
        if ($info['http_code'] !== 200) {
            throw new Exception(__e(
                __FILE__, 'Request for API url "%s" has failed: "%s"', $options['url'], json_encode($info)
            ));
        }
        if (empty($responseXml)) {
            throw new Exception(__e(
                __FILE__, 'Empty respose for API url "%s"', $options['url']
            ));
        }
        
        return $responseXml;
    }
}
