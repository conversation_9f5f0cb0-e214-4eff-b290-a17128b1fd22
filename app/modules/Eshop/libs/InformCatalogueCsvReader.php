<?php
App::loadLib('Eshop', 'EshopCsvReader');

/**
 * INFORM catalogue can be downloaded from following ftp: ftp://b2b.inform-za.sk/inform_katalog.zip
 * Use the same login as the main login to INFORM administration.
 * For automatic download of archive use ftp://user:<EMAIL>/inform_katalog.zip
 * 
 * There are following files included in downloaded zip archive:
 *   - catalogue_dump.csv
 *   - categories_dump.csv
 *   - statuses_dump.csv
 * 
 * This reader serves to read catalogue_dump.csv. Statuses defined in statuses_dump.csv
 * are hardcoded here in property InformCatalogueCsvReader::$availabilityConversions
 */
class InformCatalogueCsvReader extends EshopCsvReader {
    
    /**
     * Ftp user to download INFORM catalogue
     *
     * @var string
     */
    protected $accessUser = null;
    
    /**
     * Ftp pwd to download INFORM catalogue
     *
     * @var string
     */
    protected $accessPassword = null;
    
    /**
     * Minimal shipment time off stock. If provided (not NULL) then it is used 
     * explicitly for case of any availability with shipment time off stock lower
     * as the specified minimal value.
     *
     * @var string
     */
    protected $minShipmentTimeOffStock = null;
    
    /**
     * Actual date in format YYYY-MM-DD. Used to skip old available_from dates
     * 
     * @var string
     */
    protected $actualDate = null;
    
    /**
     * Conversions of INFROM availability statuses (defined in statuses_dump.csv) 
     * to availability, shipment_time_off_stock, long_delivery_time and reprint
     * 
     * @var array
     */
    private $availabilityConversions = array(
        // Skladom (do 2 dní)
        1 => array(
            'availability' => 'enum_available',
            'shipment_time_off_stock' => 2, // 'do 2 dní',
            'long_delivery_time' => false,
            'reprint' => false,
        ),
        // Dostupná do 7 dní
        2 => array(
            'availability' => 'enum_available',
            'shipment_time_off_stock' => 7, // 'do 7 dní',
            'long_delivery_time' => true,
            'reprint' => false,
        ),
        // Dostupná do 5 dní
        3 => array(
            'availability' => 'enum_available',
            'shipment_time_off_stock' => 5, // 'do 5 dní',
            'long_delivery_time' => false,
            'reprint' => false,
        ),
        // Pripravovaná dotlač
        4 => array(
            'availability' => 'enum_presale',
            'shipment_time_off_stock' => null,
            'long_delivery_time' => false,
            'reprint' => true,
        ),
        // Pripravované nové vydanie
        5 => array(
            'availability' => 'enum_presale',
            'shipment_time_off_stock' => null,
            'long_delivery_time' => false,
            'reprint' => false,
        ),
        // Posledné kusy
        6 => array(
            'availability' => 'enum_available',
            'shipment_time_off_stock' => 5, // some average number
            'long_delivery_time' => false,
            'reprint' => false,
        ),
        // Pripravovaná novinka
        7 => array(
            'availability' => 'enum_presale',
            'shipment_time_off_stock' => null,
            'long_delivery_time' => false,
            'reprint' => false,
        ),
        // Dotlač (ale už vytlačené)
        8 => array(
            'availability' => 'enum_available',
            'shipment_time_off_stock' => 5, // some average number
            'long_delivery_time' => false,
            'reprint' => false,
        ),
        // Rozobraná
        9 => array(
            'availability' => 'enum_soldout',
            'shipment_time_off_stock' => null,
            'long_delivery_time' => false,
            'reprint' => false,
        ),
        // Dostupná do 14 dní
        10 => array(
            'availability' => 'enum_available',
            'shipment_time_off_stock' => 14, // 'do 14 dní',
            'long_delivery_time' => true,
            'reprint' => false,
        ),
    );
    
    /**
     * Reads Inform catalogue
     * 
     * @param array $options Options of Csv() class plus following:
     *      - 'catalogueFile' (string) Pemic catalogue file. If not provided or empty 
     *          then it is downloaded from Pemic site. In such a case option 'accessId'
     *          must be provided. Defaults to NULL.
     *      - 'accessUser' (string) INFORM FTP user. Must be provided if 'catalogueFile'
     *          is not provided or is empty. Defauts to NULL.
     *      - 'accessPassword' (string) INFORM FTP password. Must be provided if 'catalogueFile'
     *          is not provided or is empty. Defauts to NULL.
     *      - 'minShipmentTimeOffStock' (string) Minimal shipment time off stock. If provided 
     *          (not NULL) then it is used explicitly for case of any availability with 
     *          shipment time off stock lower as the specified minimal value. Defauts to NULL.
     */    
    public function __construct($options = array()) {
        $defaults = array(
            'catalogueFile' => null,
            'accessUser' => null,
            'accessPassword' => null,
            'minShipmentTimeOffStock' => null,
        );
        $options = array_merge($defaults, $options);
        
        $this->minShipmentTimeOffStock = $options['minShipmentTimeOffStock'];
        if ($this->minShipmentTimeOffStock !== null) {
            $this->minShipmentTimeOffStock = (int)$this->minShipmentTimeOffStock;
        }
        $this->accessUser = $options['accessUser'];
        $this->accessPassword = $options['accessPassword'];
        $this->actualDate = date('Y-m-d');
        
        // check for file and transfer if not provided
        if (empty($options['catalogueFile'])) {
            $options['catalogueFile'] = $this->transferFile();
        }
        
        parent::__construct($options['catalogueFile'], array(
            'mode' => 'r',
            'delimiter' => ';',
            'enclosure' => '"',
            'hasHeaders' => true,
            'encoding' => 'UTF8',
            'decimalPoint' => ',',
            'thousandsSeparator' => '',
            'recordFields' => array(
                'supplier_product_id' => array('column' => 35, 'trim' => true),            //AJ INTERNE ID
                'ean' => array('column' => 0, 'trim' => true, 'convert' => function($value) { //A
                    return $this->normalizeEan($value);
                }),            
                'isbn' => array('column' => 1, 'trim' => ' -', 'convert' => function($value) { //B
                    return $this->normalizeText($value);
                }),                    
                'price_taxed' => array('column' => 2, 'convertType' => 'float'),         //C MOC € S DPH
                'tax_rate' => array('column' => 24, 'trim' => true, 'convertType' => 'int'),               //Y DPH
                'name' => array('column' => 3, 'trim' => true, 'convert' => function($value) { //D NAZOV TITULU
                    return $this->normalizeText($value);
                }),                    
//                '' => array('column' => 4, 'trim' => true),               //E (Priezvisko, Meno)
                'manufacturer' => array('column' => 5, 'trim' => true, 'convert' => function($value) { //F VYDAVATEL
                    return $this->normalizeText($value);
                }),            
                'stock' => array('column' => 6, 'trim' => true),          //G (počet kusov skladom v Informe)
                'authors' => array('column' => 7, 'convert' => function($value) { //H (Meno Priezvisko)  
                    $value = $this->normalizeText($value);
                    return array_filter(array_map('trim', Str::explode(';', $value)));
                }),     
                'categories' => array('column' => 9, 'trim' => true, 'convert' => function($value) { //J TÉMATICKÁ OBLASŤ
                    $value = (int)$value;
                    if (!empty($value)) {
                        return array($value);
                    }
                    return array();
                }),            
                'available_from' => array('column' => 10, 'trim' => true, 'convert' => function($value) { //K TERMÍN VYDANIA
                    if (  
                        !empty($value)
                        && ($availableFrom = Date::format($value))
                        && $availableFrom > $this->actualDate
                    ) {
                        return $availableFrom;
                    }
                    return null;
                }),   
                'year' => array('column' => 11, 'trim' => true, 'convert' => function($value, $record, $outputRecord) { //L ROK 1. VYDANIA
                    $availableFrom = trim($outputRecord['available_from']);
                    if (!empty($availableFrom)) {
                        return substr($availableFrom, 0, 4);
                    }
                    return $value;
                }),                   
                'pages' => array('column' => 12, 'trim' => true),                  //M POČET STRÁN
                'binding' => array('column' => 13, 'trim' => true, 'convert' => function($value) { //N VAZBA
                    $value = $this->normalizeText($value);
                    return Str::uppercaseFirst($value);
                }),                
                'edition' => array('column' => 14, 'trim' => true, 'convertType' => 'int'),                //O VYDANIE (1., 2. , ...)
                'subtitle' => array('column' => 15, 'trim' => true, 'convert' => function($value) { //P PODNÁZOV
                    return $this->normalizeText($value);
                }),               
                'range' => array('column' => 16, 'trim' => true, 'convert' => function($value) { //Q EDÍCIA
                    return $this->normalizeText($value);
                }),             
//                '' => array('column' => 17),                                      //R FORMÁT [mm]
                'width' => array('column' => 17, 'trim' => true, 'convert' => function($value) {
                    if (empty($value)) {
                        return null;
                    }
                    $value = explode('x', strtolower($value));
                    if (!isset($value[0])) {
                        return null;
                    }
                    return $this->convertType(trim($value[0]), 'float');
                }),
                'height' => array('column' => 17, 'trim' => true, 'convert' => function($value) {
                    if (empty($value)) {
                        return null;
                    }
                    $value = explode('x', strtolower($value));
                    if (!isset($value[1])) {
                        return null;
                    }
                    return $this->convertType(trim($value[1]), 'float');
                }),
                'length' => array('column' => 17, 'trim' => true, 'convert' => function($value) {
                    if (empty($value)) {
                        return null;
                    }
                    $value = explode('x', strtolower($value));
                    if (!isset($value[2])) {
                        return null;
                    }
                    return $this->convertType(trim($value[2]), 'float');
                }),
                'weight' => array('column' => 31, 'trim' => true, 'convertType' => 'float', 'convert' => function($value) { //AF HMOTNOST [kg]
                    // convert grams to kilograms
                    if ($value > 10) {
                        $value /= 1000;
                    }
                    if (
                        empty($value) 
                        || preg_match('/^[0\.]+$/', $value)
                    ) {
                        return null;
                    }
                    return $value;
                }),
                'language' => array('column' => 18, 'trim' => true, 'convert' => function($value) { //S JAZYK (slovenský, český, ...)
                    $value = $this->normalizeText($value);
                    return Str::uppercaseFirst($value);
                }),               
                'description' => array('column' => 19, 'trim' => true, 'convert' => function($value) { //T ANOTÁCIA
                    return $this->normalizeText($value);
                }),            
                'keywords' => array('column' => 20, 'trim' => true, 'convert' => function($value) { //U KĽÚČOVÉ SLOVÁ
                    return $this->normalizeText($value);
                }),               
                'original_name' => array('column' => 21, 'trim' => true, 'convert' => function($value) { //V NÁZOV ORIGINÁLU
                    return $this->normalizeText($value);
                }),          
//                22 => array('key' => ''),                       //W DÁTUM ZARADENIA
//                23 => array('key' => ''),                       //X interný kód dodávatela:
//                25 => array('key' => ''),                       //Z MOC CZK
                'margin_rate' => array('column' => 26, 'convertType' => 'float'),                       //AA ZLAVA (%) Rabat e_shopy
//                27 => array('key' => ''),                       //AB Nákupná cena e_shopy (bez DPH)
//                28 => array('key' => ''),                       //AC Nákupná cena e_shopy CZK (bez DPH)
//                29 => array('key' => ''),                       //AD DPH v ČR (v %)
//                'has_preview' => array('column' => 30, 'trim' => true), //AE K DISPOZICII AKO PDF (0,1)
//                32 => array('key' => ''),                       //AG SERIA
//                33 => array('key' => ''),                       //AH SERIA (PORADOVE CISLO)
//                34 => array('key' => ''),                       //AI SERIA (POCET POLOZIEK)
//                36 => array('key' => ''),                       //AK NOSIČ - 1xkniha, 1xDVD, ...
//                37 => array('key' => ''),                       //AL ZLACNENÉ
                'availability' => array('column' => 8, 'trim' => true, 'convertType' => 'int', 'convert' => function($value) { //I STATUS
                    if (!empty($this->availabilityConversions[$value])) {
                        return $this->availabilityConversions[$value]['availability'];
                    }
                    return 'enum_available';
                }),                   
                'shipment_time_off_stock' => array('column' => 8, 'trim' => true, 'convertType' => 'int', 'convert' => function($value) { //I STATUS
                    if (!empty($this->availabilityConversions[$value])) {
                        $time = $this->availabilityConversions[$value]['shipment_time_off_stock'];
                        if (
                            $time !== null // if provided as NULL then do not change it
                            && $this->minShipmentTimeOffStock !== null
                            && $time < $this->minShipmentTimeOffStock 
                        ) {
                            return $this->minShipmentTimeOffStock;
                        }
                        return $time;
                    }
                    return null;
                }),                   
                'long_delivery_time' => array('column' => 8, 'trim' => true, 'convertType' => 'int', 'convert' => function($value) { //I STATUS
                    if (!empty($this->availabilityConversions[$value])) {
                        return $this->availabilityConversions[$value]['long_delivery_time'];
                    }
                    return false;
                }),                   
                'reprint' => array('column' => 8, 'trim' => true, 'convertType' => 'int', 'convert' => function($value) { //I STATUS
                    if (!empty($this->availabilityConversions[$value])) {
                        return $this->availabilityConversions[$value]['reprint'];
                    }
                    return false;
                }),   
                'image_import_source' => function ($record, $outputRecord) {
                    return 'https://b2b.inform-za.sk/obalky/obalky_170/' . $outputRecord['ean'] . '.jpg';
                }
            )
        ));
    }
    
    /**
     * Normalize quirks contained in INFORM texts
     * 
     * @param string $text
     * @return string
     */
    protected function normalizeText($text) {
        $text = html_entity_decode($text); // e.g. "y&#769;" -> "ý", "t&#700;" -> "ť", ...
        $text = preg_replace('/\&\#\d+/', '', $text); // remove invalid htmlentieties not replaced by above decoding (e.g. favorit "&#150" - stub of "En dash")
        $text = str_replace(' ', ' ', $text); // replace char 194 (= CTRL + SPACE) by plain space
        return $text;
    }
    
    /**
     * Transfers INFORM import file (catalogue_dump.csv)
     * 
     * @return string transfered filename path
     * 
     * @throws Exception
     */
    protected function transferFile() {
        if (empty($this->accessUser)) {
            throw new Exception('Missing INFORM download access FTP user');
        }
        if (empty($this->accessPassword)) {
            throw new Exception('Missing INFORM download access FTP password');
        }
        $this->ensureTmpDir();
        // get zip file
        $url = 'ftp://' . $this->accessUser . ':' . $this->accessPassword . '@b2b.inform-za.sk/inform_katalog.zip';
        $zipFile = File::transfer(
            array($url),
            $this->tmpDir,
            array(
                'unique' => false,
                'name' => 'inform.zip',
            )
        );
        if (empty($zipFile)) {
            throw new Exception(sprintf('Transfer of file %s has failed', $url));
        }
        $zipFile = $this->tmpDir . DS . $zipFile;
        File::unzip($zipFile, array('delete' => true));
        return $this->tmpDir . DS . 'catalogue_dump.csv';
    }
    
    /**
     * Reads the next csv record from file. Info line, header row and empty lines are omitted.
     * Returned record has following items:
     * 
     *      array(
     *          'supplier_product_id' => ...,       //AJ INTERNE ID
     *          'ean' => ...,               //A
     *          'isbn' => ...,              //B
     *          'price_taxed' => ...,       //C MOC € S DPH
     *          'tax_rate' => ...,          //Y DPH
     *          'margin_rate' => ...,       //AA ZLAVA (%) Rabat e_shopy
     *          'name' => ...,              //D NAZOV TITULU
     *          'manufacturer' => ...,      //F VYDAVATEL
     *          'stock' => ...,             //G (počet kusov skladom v Informe)
     *          'authors' => ...,           //H (Meno Priezvisko)
     *          'categories' => ...,        //J TÉMATICKÁ OBLASŤ
     *          'available_from' => ...,    //K TERMÍN VYDANIA
     *          'year' => ...,              //L ROK 1. VYDANIA
     *          'pages' => ...,             //M POČET STRÁN
     *          'binding' => ...,           //N VAZBA
     *          'edition' => ...,           //O VYDANIE (1., 2. , ...)
     *          'subtitle' => ...,          //P PODNÁZOV
     *          'range' => ...,             //Q EDÍCIA
     *          'width' => ..., [mm]        //R FORMÁT [mm]
     *          'height' => ..., [mm]       //R FORMÁT [mm]
     *          'length' => ..., [mm]       //R FORMÁT [mm]
     *          'weigth' => ..., [kg]       //AF HMOTNOST [kg]
     *          'language' => ...,          //S JAZYK (slovenský, český, ...)
     *          'description' => ...,       //T ANOTÁCIA
     *          'keywords' => ...,          //U KĽÚČOVÉ SLOVÁ
     *          'original_name' => ...,     //V NÁZOV ORIGINÁLU
     *          'availability' => ...,      //I STATUS - enum_available, enum_presale, enum_soldout
     *          'shipment_time_off_stock' => ...,       //I STATUS - NULL, 2, 5, 7, 14
     *          'long_delivery_time' => ...,            //I STATUS - TRUE/FALSE
     *          'reprint' => ...,                       //I STATUS - TRUE/FALSE
     *          'image_import_source' => ...,           // URL generated from EAN
     *      )
     * 
     * @return bool|array Next record array. FALSE if the end of csv file is reached.
     */
    public function readRecord() {
        return parent::readRecord();
    }
}

