-- triggers used in database
-- NOTE: before launching this queries change the delimiter in PMA to $$

DROP TRIGGER IF EXISTS run_eshop_products_before_update $$
CREATE TRIGGER run_eshop_products_before_update BEFORE UPDATE ON run_eshop_products
    FOR EACH ROW BEGIN
        -- switch status presale to available if there is stock > 0
        IF 
            COALESCE(NEW.stock, 0) > 0 
            AND NEW.availability = 'enum_presale'
        THEN
            SET NEW.availability = 'enum_available';
        END IF;
        -- set unavailable
        IF NEW.stock <> OLD.stock OR NEW.availability <> OLD.availability THEN  
            IF NEW.stock <= 0 AND NEW.availability = 'enum_soldout' THEN
                SET NEW.unavailable = 1;
            ELSE 
                SET NEW.unavailable = 0;
            END IF;
        END IF;
        -- synchronize discount_rate and discount_price
        IF 
            COALESCE(NEW.price, 0) <> COALESCE(OLD.price, 0)
            OR COALESCE(NEW.discount_rate, 0) <> COALESCE(OLD.discount_rate, 0) 
            OR COALESCE(NEW.discount_price, 0) <> COALESCE(OLD.discount_price, 0)
        THEN  
            IF 
                (
                    COALESCE(NEW.price, 0) <> COALESCE(OLD.price, 0)
                    OR COALESCE(NEW.discount_rate, 0) <> COALESCE(OLD.discount_rate, 0) 
                ) 
                AND COALESCE(NEW.discount_rate, 0) > 0 
            THEN
                SET NEW.discount_price = NEW.price * ((100 - NEW.discount_rate) / 100);
            ELSEIF 
                COALESCE(NEW.discount_price, 0) <> COALESCE(OLD.discount_price, 0)
                AND COALESCE(NEW.discount_price, 0) > 0 
            THEN
                SET NEW.discount_rate = 100 * ((NEW.price - NEW.discount_price) / NEW.price);
            ELSE
                SET NEW.discount_price = NULL;
                SET NEW.discount_rate = NULL;
            END IF;
        END IF;
    END $$

DROP TRIGGER IF EXISTS run_eshop_products_before_insert $$
CREATE TRIGGER run_eshop_products_before_insert BEFORE INSERT ON run_eshop_products
    FOR EACH ROW BEGIN
        -- switch status presale to available if there is stock > 0
        IF 
            COALESCE(NEW.stock, 0) > 0 
            AND NEW.availability = 'enum_presale'
        THEN
            SET NEW.availability = 'enum_available';
        END IF;
        -- set unavailable
        IF NEW.stock <= 0 AND NEW.availability = 'enum_soldout' THEN
            SET NEW.unavailable = 1;
        ELSE 
            SET NEW.unavailable = 0;
        END IF;
        -- synchronize discount_rate and discount_price
        IF NEW.discount_rate > 0 THEN
            SET NEW.discount_price = NEW.price * ((100 - NEW.discount_rate) / 100);
        ELSEIF NEW.discount_price > 0 THEN
            SET NEW.discount_rate = 100 * ((NEW.price - NEW.discount_price) / NEW.price);
        ELSE
            SET NEW.discount_price = NULL;
            SET NEW.discount_rate = NULL;
        END IF;
    END $$

DROP TRIGGER IF EXISTS run_eshop_supplier_products_before_update $$
CREATE TRIGGER run_eshop_supplier_products_before_update BEFORE UPDATE ON run_eshop_supplier_products
    FOR EACH ROW BEGIN
        -- synchronize discount_rate and discount_price
        IF 
            COALESCE(NEW.price, 0) <> COALESCE(OLD.price, 0)
            OR COALESCE(NEW.discount_rate, 0) <> COALESCE(OLD.discount_rate, 0) 
            OR COALESCE(NEW.discount_price, 0) <> COALESCE(OLD.discount_price, 0)
        THEN  
            IF 
                (
                    COALESCE(NEW.price, 0) <> COALESCE(OLD.price, 0)
                    OR COALESCE(NEW.discount_rate, 0) <> COALESCE(OLD.discount_rate, 0) 
                ) 
                AND COALESCE(NEW.discount_rate, 0) > 0 
            THEN
                SET NEW.discount_price = NEW.price * ((100 - NEW.discount_rate) / 100);
            ELSEIF 
                (
                    COALESCE(NEW.price, 0) <> COALESCE(OLD.price, 0)
                    OR COALESCE(NEW.discount_price, 0) <> COALESCE(OLD.discount_price, 0)
                ) 
                AND COALESCE(NEW.discount_price, 0) > 0 
            THEN
                SET NEW.discount_rate = 100 * ((NEW.price - NEW.discount_price) / NEW.price);
            ELSE
                SET NEW.discount_price = NULL;
                SET NEW.discount_rate = NULL;
            END IF;
        END IF;
    END $$

DROP TRIGGER IF EXISTS run_eshop_supplier_products_before_insert $$
CREATE TRIGGER run_eshop_supplier_products_before_insert BEFORE INSERT ON run_eshop_supplier_products
    FOR EACH ROW BEGIN
        -- synchronize discount_rate and discount_price
        IF NEW.discount_rate > 0 THEN
            SET NEW.discount_price = NEW.price * ((100 - NEW.discount_rate) / 100);
        ELSEIF NEW.discount_price > 0 THEN
            SET NEW.discount_rate = 100 * ((NEW.price - NEW.discount_price) / NEW.price);
        ELSE
            SET NEW.discount_price = NULL;
            SET NEW.discount_rate = NULL;
        END IF;
    END $$
