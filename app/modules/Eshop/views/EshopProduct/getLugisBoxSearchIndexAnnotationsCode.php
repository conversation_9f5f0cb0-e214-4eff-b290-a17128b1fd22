<?php /* @var $this Template */
$this->displayOriginComment = true;
$uniqueid = uniqid('luigisbox-search-annotations-');

App::setMicrodata($this->params['annotations'], array(
    'id' => $uniqueid
));

// Ensure existence of window.Luigis.Scan() for the case that script 
// https://scripts.luigisbox.com/LBX-12345.js is not loaded yet (as it is an async script)
// @see https://live.luigisbox.com/search_analytics.html#embedded-json-ld-explitict-notifications
App::startJsCapture();
?><script>
window._lbcq = window._lbcq || [];
window.Luigis = window.Luigis || {};
window.Luigis.Scan = window.Luigis.Scan || function(a, r) {
  window._lbcq.push([a, r]);
};
</script><?php
App::endJsCapture();

App::startJsCapture();
?><script>
jQuery(function(){
    window.Luigis.Scan('#<?php echo $uniqueid?>', '#<?php echo $this->params['productsListId'] ?>');
});
</script><?php
App::endJsCapture(array('last' => true));
