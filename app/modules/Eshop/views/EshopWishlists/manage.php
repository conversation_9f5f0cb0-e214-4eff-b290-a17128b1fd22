<?php /* @var $this Template */
$this->displayOriginComment = true;
App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    'data' => $this->params['data'],
    'required' => $this->params['requiredField'],
    'labelDefaults' => array(
        'separator' => false,
    )
));
Html::startCapture();
?><div class="nice-checkbox-wrapper"><?php
    ?><div class="nice-checkbox"><?php
        ?>:i:<?php
        ?><span class="image"></span><?php
    ?></div><?php
    ?>:e:<?php
?></div><?php
$checkboxTemplate = Html::endCapture();

?><div class="manage-wishlist"><?php
    echo $Form->select('wishlist', array(
        'options' => $this->params['wishlists'],
        'value' => $this->params['wishlistUrl'],
        'class' => 'select-wishlist',
        'onchange' => "window.location = this.value;",
    ));
    ?><!--<div class="add-wishlist"></div>--><?php
    ?><div class="setup-wishlist"><?php
        ?><nav><?php
            ?><a href="javascript:void(0);" class="button" onclick="jQuery('#editWishlistForm').hide(); jQuery('#shareWishlistForm').hide(); jQuery('#addWishlistForm').toggle();"><?php
                echo __(__FILE__, 'Create new');
            ?></a><?php
            if ($this->params['wishlist']) {
                ?><a href="javascript:void(0);" class="button" onclick="jQuery('#shareWishlistForm').hide(); jQuery('#addWishlistForm').hide(); jQuery('#editWishlistForm').toggle();"><?php
                    echo __(__FILE__, 'Edit wishlist');
                ?></a><?php
                ?><a href="javascript:void(0);" class="button" onclick="jQuery('#addWishlistForm').hide(); jQuery('#editWishlistForm').hide(); jQuery('#shareWishlistForm').toggle();"><?php
                    echo __(__FILE__, 'Share wishlist');
                ?></a><?php
                ?><a href="<?php echo $this->params['wishlistViewUrl'] ?>" class="button" target="_blank"><?php
                    echo __(__FILE__, 'Show wishlist public page')
                ?></a><?php
                if (count($this->params['wishlists']) > 2) : // 2 - all wishlists and the last one
                    ?><a href="<?php echo $this->params['wishlistDeleteUrl'] ?>" class="button" onclick="return confirm('<?php 
                        echo __(__FILE__, 'Do you really want to delete this wishlist?') 
                    ?>')"><?php
                        echo __(__FILE__, 'Delete wishlist');
                    ?></a><?php
                endif;
            }
        ?></nav><?php
        ?><div id="addWishlistForm"><?php
            ?><form action="<?php echo $this->params['wishlistAddUrl'] ?>" method="post" ><?php
                ?><input type="hidden" name="data[_target][]" value="Eshop.EshopWishlists.add"/><?php
                ?><table><?php
                    ?><tr><?php
                        ?><td class="label"><?php echo __(__FILE__, 'Wishlist name') ?></td><?php
                        ?><td><?php echo $Form->text('name') ?></td><?php
                    ?></tr><?php
                    ?><tr><?php
                        ?><td class="label"><?php echo __(__FILE__, 'Is wishlist visible to others') ?></td><?php
                        ?><td><?php echo $Form->select('privacy_level', array('options' => $this->params['privacyLevels'])) ?></td><?php
                    ?></tr><?php
                    ?><tr><?php
                        ?><td class="label"><?php echo __(__FILE__, 'Default wishlist') ?></td><?php
                        ?><td><?php echo $Form->checkbox('default', array('template' => $checkboxTemplate)) ?></td><?php
                    ?></tr><?php
                    ?><tr><?php
                        ?><td colspan="2" class="submit"><?php
                            ?><a href="javascript:void(0);" class="button" onclick="jQuery(this).closest('form').submit()"><?php
                                echo __(__FILE__, 'Create');
                            ?></a><?php
                        ?></td><?php
                    ?></tr><?php
                ?></table><?php
            ?></form><?php
        ?></div><?php
        if ($this->params['wishlist']) {
            ?><div id="editWishlistForm" ><?php
                ?><form action="<?php echo $this->params['wishlistEditUrl'] ?>" method="post" ><?php
                    ?><input type="hidden" name="data[_target][]" value="Eshop.EshopWishlists.edit"/><?php
                    echo $Form->input('id', array('type' => 'hidden', 'value' => $this->params['wishlist']['id']))
                    ?><table><?php
                        ?><tr><?php
                            ?><td class="label"><?php echo __(__FILE__, 'Wishlist name') ?></td><?php
                            ?><td><?php echo $Form->text('name', array('value' => $this->params['wishlist']['name'])) ?></td><?php
                        ?></tr><?php
                        ?><tr><?php
                            ?><td class="label"><?php echo __(__FILE__, 'Is wishlist visible to others') ?></td><?php
                            ?><td><?php 
                                echo $Form->select('privacy_level', array('options' => $this->params['privacyLevels'], 'value' => $this->params['wishlist']['privacy_level'])) 
                            ?></td><?php
                        ?></tr><?php
                        ?><tr><?php
                            ?><td class="label"><?php echo __(__FILE__, 'Default wishlist') ?></td><?php
                            ?><td><?php echo $Form->checkbox('default', array('checked' => (bool)$this->params['wishlist']['default'], 'template' => $checkboxTemplate)) ?></td><?php
                        ?></tr><?php
                        ?><tr><?php
                            ?><td colspan="2" class="submit"><?php
                                ?><a href="javascript:void(0);" class="button" onclick="jQuery(this).closest('form').submit()"><?php
                                    echo __(__FILE__, 'Save');
                                ?></a><?php
                            ?></td><?php
                        ?></tr><?php
                    ?></table><?php
                ?></form><?php
            ?></div><?php
            ?><div id="shareWishlistForm" ><?php
                if ($this->params['wishlist']['privacy_level'] != 'enum_wishlist_private') {
                    ?><table><?php
                        ?><tr><?php
                            ?><td class="label"><?php echo __(__FILE__, 'Wishlist URL') ?></td><?php
                            ?><td><?php
                                ?><span class="whishlist-url"><?php
                                    echo $this->params['wishlistViewUrl']
                                    ?><!--<a href="<?php echo $this->params['wishlistViewUrl'] ?>" target="_blank">(<?php echo __(__FILE__, 'Show wishlist public page') ?>)</a>--><?php
                                ?></span><?php
                            ?></td><?php
                        ?></tr><?php
                        ?><tr><?php
                            ?><td class="label"><?php echo __(__FILE__, 'Send wishlist to email') ?></td><?php
                            ?><td><?php
                                ?><form action="<?php echo $this->params['wishlistSendUrl'] ?>" method="post" ><?php
                                    ?><input type="hidden" name="data[_target][]" value="Eshop.EshopWishlists.send"/><?php
                                    echo $Form->hidden('id', array('value' => $this->params['wishlist']['id']));
                                    echo $Form->text('wishlist_email')
                                    ?><a href="javascript:void(0);" class="button" onclick="jQuery(this).closest('form').submit()"><?php
                                        echo __(__FILE__, 'Send');
                                    ?></a><?php
                                ?></form><?php
                            ?></td><?php
                        ?></tr><?php
                    ?></table><?php
                }
                else {
                    echo __(__FILE__, 'Wishlist is private. It is not possible to share it.') 
                    ?><br/><?php
                    echo __(__FILE__, 'If you want to share it then set its visibility to shared or public.');
                }
            ?></div><?php
        }
    ?></div><?php
?></div><?php
