<?php /* @var $this Template */
$this->displayOriginComment = true;
App::loadLib('App', 'FormHelper');
$Form = new FormHelper();
$columns = Sanitize::value($this->params['columns']);
$uniqueClass = uniqid('wishlist-products-index-');
?><div class="wishlist-products-index <?php echo $uniqueClass ?>"><?php 
/*
    ?><h2 class="section-title"><?php 
        echo __(__FILE__, 'Wishlist items') 
    ?></h2><?php 
*/
    ?><div class="index"><?php 
        if (empty($this->params['products'])) {
            echo App::loadElement('App', 'emptyIndexMessage', array('message' => $this->params['emptyIndexMessage']));
        }
        else {
            $this->params['products'] = array_values($this->params['products']);
            $lastIndex = count($this->params['products']) - 1;
            foreach ($this->params['products'] as $i => $product) { 
                $urlAddToCart = App::getUrl(array(
                    'module' => $this->module,
                    'controller' => 'EshopCarts',
                    'action' => 'addProducts',
                    'args' => array($product['id'])
                ));
                $urlShowDetail = App::getUrl(array(
                    'locator' => $this->params['slugProductView'],
                    'args' => array($product['slug'])
                ));
                $urlUpdateWishlistProduct = App::getUrl(array(
                    'module' => $this->module,
                    'controller' => 'EshopWishlists',
                    'action' => 'updateProduct',
                    'args' => array($product['id'])
                ));
                $urlDeleteWishlistProduct = App::getUrl(array(
                    'module' => $this->module,
                    'controller' => 'EshopWishlists',
                    'action' => 'deleteProduct',
                    'args' => array($product['id'])
                ));
                /*
                // create row starts if number of columns is provided
                if (
                    !empty($columns)
                    && $i % $columns == 0
                ) {
                    ?><div class="index-row"><?php
                }
                // get row position class if number of columns is provided
                $positionClass = '';
                if (!empty($columns)) {
                    if ($i % $columns == 0) {
                        $positionClass = ' first';
                    }
                    elseif (($i + 1) % $columns == 0) {
                        $positionClass = ' last';
                    }
                }
                */
                // product block
                ?><div class="product"<?php echo $this->params['SmartAdminLauncher']->markRecord($product['id']) ?>><?php
                    ?><div class="spacer"><?php 
                        // image
                        if (!empty($product['image']['small'])) {
                            ?><div class="image"><?php 
                                ?><div class="wrapper"><?php
                                    ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'More info') ?>" class="product-image"><?php
                                        ?><img src="<?php echo $product['image']['small']; ?>" alt="<?php echo $product['name']; ?>" /><?php
                                    ?></a><?php
                                ?></div><?php
                            ?></div><?php
                        }
                        ?><div class="stickies"><?php
                            if ($product['savings_rate'] > 0) {
                                ?><div class="stick savings-rate"><?php
                                    echo '-' . floor($product['savings_rate']) . '%';
                                ?></div><?php
                            }
                            if (!empty($product['new'])) {
                                ?><div class="stick new"><?php
                                    echo __(__FILE__, 'Novinka');
                                ?></div><?php
                            }
                        ?></div><?php
                        ?><div class="texts"><?php
                            // title
                            ?><h3 class="title"><?php
                                ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'More info') ?>"><?php
                                    echo $product['name'];
                                ?></a><?php
                            ?></h3><?php
                            // info
                            ?><div class="info"><?php 
                                // price
                                ?><div class="actual-price"><?php 
                                    //echo __(__FILE__, 'Our price') . ' '; 
                                    echo Eshop::formatPrice($product['price_actual_taxed'], array(
                                        'symbol' => '<span class="currency">' . Eshop::getActualCurrency('symbol') . '</span>')
                                    );
                                ?></div><?php
                                // saving rate
                                if (!empty($product['savings_rate'])) {
                                    if (false/*NOTUSED*/) {
                                        ?><div class="discount-label"><?php 
                                            echo __(__FILE__, 'Discount');
                                        ?></div><?php
                                    }
                                    /*
                                    ?><div class="savings-rate"><?php 
                                        //echo '-' . floor($product['savings_rate']) . '%';
                                    ?></div><?php
                                    */
                                }
                                // disponibility
                                if ($this->params['showDisponibility']) {
                                    // disponibility
                                    $disponibility = '';
                                    $disponibilityClass = '';
                                    switch ($product['disponibility']) {
                                        case EshopProduct::STOCK:
                                            $disponibility = __(__FILE__, 'Available on stock');
                                            $disponibilityClass = ' on-stock';
                                            break;
                                        case EshopProduct::SUPPLIER:
                                            $disponibility = __(__FILE__, 'Available at supplier');
                                            $disponibilityClass = ' at-supplier';
                                            break;
                                        case EshopProduct::ON_DEMAND:
                                            $disponibility = __(__FILE__, 'On demand');
                                            $disponibilityClass = ' on-demand';
                                            break;
                                        case EshopProduct::SOLDOUT:
                                            $disponibility = __(__FILE__, 'Sold out');
                                            $disponibilityClass = ' sold-out';
                                            break;
                                        case EshopProduct::PRESALE:
                                            if (empty($product['reprint'])) {
                                                $disponibility = __(__FILE__, 'Presale');
                                            }
                                            else {
                                                $disponibility = __(__FILE__, 'Reprint prepared');
                                            }
                                            $disponibilityClass = ' presale';
                                            break;
                                        default:
                                        break;
                                    }
                                    ?><div class="disponibility-wrapper"><?php 
                                        ?><div class="disponibility<?php echo $disponibilityClass ?>"><?php 
                                            echo $disponibility;
                                        ?></div><?php
                                    ?></div><?php
                                }
                            ?></div><?php
                            // add to cart button
                            if (
                                $this->params['showAddToCartButton']
                                && $product['disponibility'] !== EshopProduct::SOLDOUT
                            ) { 
                                ?><form action="<?php echo $urlAddToCart; ?>" method="post" class="add-to-cart"><?php
                                    ?><input type="hidden" name="data[_target]" value="Eshop.EshopCarts.addProducts" /><?php
                                    ?><input type="hidden" name="data[id]" value="<?php echo $product['id']; ?>" /><?php
                                    ?><input type="hidden" name="data[amount]" value="1" /><?php
                                    if (!empty($product['special_offer_id'])) {
                                        ?><input type="hidden" <?php 
                                            ?>name="data[special_offer_id]" <?php 
                                            ?>value="<?php echo $product['special_offer_id']; ?>"<?php 
                                        ?>/><?php
                                    }
                                    ?><button type="submit" data-action="buy"><?php 
                                        echo __(__FILE__, 'Insert to cart');
                                    ?></button><?php
                                ?></form><?php
                            }
                            ?><div class="wishlist-controls"><?php
                                ?><form action="<?php echo $urlUpdateWishlistProduct ?>" method="post"><?php
                                    ?><input type="hidden" name="data[_target][]" value="Eshop.EshopWishlists.updateProduct"/><?php
                                    echo $Form->select('run_eshop_wishlists_id', array(
                                        'options' => $this->params['wishlists'], 
                                        'value' => $this->params['productIdsXWishlistIds'][$product['id']],
                                        //'label' => __(__FILE__, 'Wishlist'),
                                        'onchange' => "jQuery(this).closest('form').submit()",
                                        'title' => __(__FILE__, 'Move to other wishlist'),
                                    )) 
                                ?></form><?php
                                ?><a href="<?php echo $urlDeleteWishlistProduct ?>" class="button" title="<?php echo __(__FILE__, 'Remove from wishlist') ?>"><?php
                                    echo __(__FILE__, 'Remove from wishlist');
                                ?></a><?php
                            ?></div><?php
                        ?></div><?php
                    ?></div><?php
                ?></div><?php
                /*
                // create row ends if number of columns is provided
                if (
                    !empty($columns)
                    && (
                        $i == $lastIndex
                        || ($i + 1) % $columns == 0
                    )
                ) {
                    ?></div><?php
                }
                */ 
            }
        }
    ?></div><?php
    // paginator
    if (isset($this->params['Paginator'])) {
        ?><div class="paginator"><?php 
            //echo $this->params['Paginator']->getLinks(); 
            echo $this->params['Paginator']->getPagesLinks();
            /*
            // page select
            echo $this->params['Paginator']->getPageSelect(array(
                'label' => __(__FILE__, 'Page'),
                'class' => 'page-select',
            ));
            // limit select
            echo $this->params['Paginator']->getLimitSelect(array(
                'label' => __(__FILE__, 'Limit'),
                'class' => 'limit-select',
            ));            
            */
        ?></div><?php
    }
?></div><?php
// set product css width according number of columns
if (!empty($columns)) {
    App::startCssCapture();
    ?><style type="text/css">
        .<?php echo $uniqueClass ?> .index .product {
            width: <?php echo 100 / $columns ?>%;
        }
    </style><?php
    App::endCssCapture();
}




