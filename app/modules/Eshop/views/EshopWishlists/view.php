<?php /* @var $this Template */
$this->displayOriginComment = true;
$params = $this->params;
App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    'data' => $params['data'],
    'labelDefaults' => array(
        'separator' => false,
    )
));

?><div class="wishlist-search"><?php 
    ?><form action="/<?php echo $params['wishlistViewSlug'] ?>" method="get" class=""><?php
        ?><input type="hidden" name="data[_target][]" value="Eshop.EshopWishlists.view"/><?php
        ?><div class="search-box"><?php 
            echo $Form->text('wishlist_keywords', array(
                'label' => __(__FILE__, 'Name or email of person whose wishlist you would like to find'),
            ));
            ?><a href="javascript:void(0);" class="button" onclick="jQuery(this).closest('form').submit()"><?php
                echo __(__FILE__, 'Search');
            ?></a><?php
        ?></div><?php
    ?></form><?php
?></div><?php
if (isset($params['owners'])) {
    ?><div class="owners-wishlists"><?php
        ?><h1 class="title"><?php
            echo __(__FILE__, 'Found wishlists')
        ?></h1><?php
        if (!$params['owners']) {
            echo App::loadElement('App', 'emptyIndexMessage', array(
                'message' => __(__FILE__, 'No wishlist owner was found according to provided keywords')
            )); 
        }
        else {
            ?><div class="owners"><?php
                foreach ($params['owners'] as $owner) {
                    ?><div class="owner"><?php
                        echo __(__FILE__, 'User') . ': ';
                        ?><strong class="name"><?php
                            echo $owner['first_name'] . ' ' . $owner['last_name']
                        ?></strong><?php
                        echo ' | ' . __(__FILE__, 'City') . ': ';
                        ?><strong class="city"><?php
                            echo $owner['city'];
                        ?></strong><?php
                        echo ' | ' . __(__FILE__, 'Wishlists') . ': ';
                        foreach ($owner['EshopWishlist'] as $wishlist) :
                            ?><a class="wishlist-link" href="/<?php echo $params['wishlistViewSlug'] . '/' . $wishlist['access_token'] ?>"><?php
                                echo $wishlist['name'];
                            ?></a><?php
                            ?> (<?php echo count($wishlist['EshopProduct']) ?>) <?php
                        endforeach;
                        ?><div class="wishlist-products-index"><?php
                            ?><div class="index"><?php
//                                $params['products'] = array_slice($owner['EshopProduct'], 0, 5);
                                foreach ($owner['EshopProduct'] as $product) :
                                    ?><div class="product"><?php
                                        ?><div class="spacer"><?php
                                            ?><div class="image"><?php
                                                ?><a class="product-image" href="/<?php echo $params['slugProductView'] . '/' . $product['slug'] ?>" title="<?php echo $product['name'] ?>"><?php
                                                    ?><img src="<?php echo $product['image'] ?>" alt="<?php echo $product['name'] ?>" /><?php
                                                ?></a><?php
                                            ?></div><?php
                                            ?><div class="texts"><?php
                                            ?><h3 class="title"><?php
                                                ?><a href="/<?php echo $params['slugProductView'] . '/' . $product['slug'] ?>" title="<?php echo $product['name'] ?>"><?php
                                                    echo $product['name'];
                                                ?></a><?php
                                            ?></h3><?php
                                        ?></div><?php
                                        ?></div><?php
                                    ?></div><?php
                                endforeach;
                            ?></div><?php
                        ?></div><?php
                    ?></div><?php
                }
            ?></div><?php
        }
    ?></div><?php
}
elseif (isset($params['wishlist'])) {
    ?><div class="wishlist-view"><?php
        if (empty($params['wishlist'])) {
            echo App::loadElement('App', 'emptyIndexMessage', array(
                'message' => __(__FILE__, 'Required wishlist does not exist, was deleted or set as private by owner')
            )); 
        }
        else {
            ?><h1 class="title">        <?php
                echo __(__FILE__, 'Items of wishlist') . ' '; 
                echo $params['user']['first_name'] . ' ' . $params['user']['last_name'] . ' - ' .  $params['wishlist']['name'];
            ?></h1><?php
            echo $this->loadView('EshopProducts/index', $params);
        }
    ?></div><?php
}
