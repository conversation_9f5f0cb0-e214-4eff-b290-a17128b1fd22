<script type="text/javascript">
$(document).ready(function() {   
    // when interact multiselect
    $("input#autocomplete").autocomplete({
        source: window.location.origin + '/mvc/Eshop/EshopAuthors/admin_getAutocompleteList',
        delay: 0,
        minLength: 3,
        close: function(event, ui) { 
            $('input#autocomplete').val('');        
        },
        select: function(event, ui) {
            addNewItem(ui.item.value, ui.item.label);
        }         
    });
});
</script>
<div class="run-selector-view">  
    <div class="multiselect-container">
        Pridať <input type="text" id="autocomplete" value="" />  
    </div>
    <ul id="sortable">
<?php foreach ($this->params['authors'] as $author) : ?>
        <li class="item" id="item<?php echo $author['id'] ?>">
            <?php echo $author['name'] ?>
            <div class="remove-item"></div>
        </li>
<?php endforeach; ?>
    </ul>
</div>