<?php /* @var $this Template */
$this->displayOriginComment = true;

$params = $this->params;
$params['showAffix'] = true;
$params['columns'] = 4;
$emptySelectOption = '--' . __a(__FILE__, 'undefined') . '--';
$params['fields'] = array(
    array('field' => 'id', 'type' => 'hidden'),
    // BASIC PARAMETERS
    array('h1' => __a(__FILE__, 'Basic parameters')),
    array('row'),
        array(
            'field' => 'name', 
            'label' => __a(__FILE__, 'Názov')
        ), 
        array(
            'field' => 'slug', 
            'label' => __a(__FILE__, 'Slug')
        ),
        array(
            'field' => 'website', 
            'label' => __a(__FILE__, 'Webová stránka')
        ),
    array('/row'),
    array('row'),
        array(
            'field' => 'logo',
            'label' => __a(__FILE__, 'Logo'),
            'hint' => __a(__FILE__,
                'Obrázok s minimálnou šírkou :width: px alebo minimálnou výškou :height: px. Obrázok (logo) musí mať priesvitné alebo čisto biele pozadie. Aktívni výrobcovia so zadaným logom sa zobrazujú v slajdri výrobcov.',
                array('width' => 150, 'height' => 100)
            )
        ),
        array('if' => !empty($params['data']['id'])),
            array(
                'field' => 'logo',
                'type' => 'image',
                'deleteImage' => '/mvc/Eshop/EshopBrands/admin_deleteFile/logo/' . Sanitize::value($params['data']['id']), 
            ),
        array('endif'),
    array('/row'),
    array('row', 'columns' => 1),
        array(
            'field' => 'description',
            'label' => __a(__FILE__, 'Popis'), 
            'type' => 'editor',
            'options' => array(
                'height' => '450px',
            )
        ),
    array('/row'),
    // SEO PARAMETERS
    array(
        'h1' => __a(__FILE__, 'SEO parameters'),
        'hint' => __a(__FILE__, 'Parameters for search engines, e.g. Google.com, Yahoo.com or Zoznam.cz')
    ),
    array('row', 'columns' => array(3,6,3)),
        array(
            'field' => 'seo_title',
            'label' => __a(__FILE__, 'SEO title'),
            'hint' => __a(__FILE__, 'Title is displayed in browser tab and in Google search results. It is one of most important texts for Google on your page'),
        ),
        array(
            'field' => 'seo_description',
            'label' => __a(__FILE__, 'SEO description'), 
            'type' => 'textarea', 
            'style' => 'height: 34px',
            'hint' => __a(__FILE__, 'This text is mostly used by Google in search results as text under result title. Sometimes Google decides to use some other text generated from page content'),
        ),
        array(
            'field' => 'seo_keywords',
            'label' => __a(__FILE__, 'SEO keywords'),
            'hint' => __a(__FILE__, 'Google does not consider much keywords. Other search engines may.')
        ),
    array('/row'),
    // INFO
    array('h1' =>__a(__FILE__, 'Info')),
    array('row'),
        array(
            'field' => 'id', 
            'label' => 'Id', 
            'type' => 'display'
        ),
        array(
            'field' => 'created',
            'label' =>__a(__FILE__, 'Date of creation'), 
            'type' => 'display',
        ),
        array(
            'field' => 'modified',
            'label' => __a(__FILE__, 'Date of modification'), 
            'type' => 'display',
        ),
        array('if' => App::getUser('Group.pid') === 'admins'),
            array(
                'field' => 'deleted',
                'label' => __a(__FILE__, 'Date of deletion'), 
                'type' => 'text'
            ),
        array('endif'),
    array('/row'),
);
echo Html::smartForm($params);
