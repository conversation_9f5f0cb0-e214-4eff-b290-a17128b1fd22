<?php /* @var $this Template */
$this->displayOriginComment = true;
?><div class="user-menu" id="user-menu"><?php 
    if (!App::getUser()) {
        ?><div class="menu-button" id="user-menu-button"><?php
            ?><div class="menu-button-text"><?php
                ?><img src="/app/img/ico-login.png"><?php 
                echo __(__FILE__, 'Login')
            ?></div><?php
            ?><div class="menu-list-arrow"></div><?php
        ?></div><?php
        ?><ul class="menu-list" id="user-menu-list"><?php
            ?><li class="menu-item"><?php
                ?><a href="<?php echo App::getContentUrlByPid('App.Users.login') ?>" class="menu-link"><?php 
                    echo __(__FILE__, 'Log in') 
                ?></a><?php
            ?></li><?php
            ?><li class="menu-item"><?php
                ?><a href="<?php echo App::getContentUrlByPid('App.Users.register') ?>" class="menu-link"><?php 
                    echo __(__FILE__, 'Create account') 
                ?></a><?php
            ?></li><?php
        ?></ul><?php
    }
    else {
        $user = App::getUser();
        ?><div class="menu-button user-logged" id="user-menu-button"><?php
            ?><div class="menu-button-text"><?php
                ?><img src="/app/img/ico-login.png"><?php
                echo $user['first_name'] . ' ' . $user['last_name'];
            ?></div><?php
            ?><div class="menu-list-arrow"></div><?php
        ?></div><?php
        ?><ul class="menu-list" id="user-menu-list"><?php
            ?><li class="menu-item"><?php
                ?><a href="<?php echo App::getContentUrlByPid('App.Users.update') ?>" class="menu-link"><?php 
                    echo __(__FILE__, 'My account') 
                ?></a><?php
            ?></li><?php
            ?><li class="menu-item"><?php
                ?><a href="<?php echo App::getContentUrlByPid('Eshop.EshopOrders.index') ?>" class="menu-link"><?php 
                    echo __(__FILE__, 'My orders') 
                ?></a><?php
            ?></li><?php
            ?><li class="menu-item"><?php
                ?><a href="<?php echo App::getContentUrlByPid('Eshop.EshopWishlists.indexProducts') ?>" class="menu-link"><?php 
                    echo __(__FILE__, 'Wishlist') 
                ?></a><?php
            ?></li><?php
            ?><li class="menu-item"><?php
                ?><a href="<?php echo App::getContentUrlByPid('App.Users.logout') ?>" class="menu-link"><?php 
                    echo __(__FILE__, 'Logout') 
                ?></a><?php
            ?></li><?php
        ?></ul><?php
    }
?></div><?php
App::startJsCapture();
?><script type="text/javascript">
    jQuery(function(){
        // activate dropdown menu
        var button = jQuery('#user-menu-button');
        var list = jQuery('#user-menu-list');
        button.click(function(e){
            e.stopPropagation();
            jQuery(this).toggleClass('active');
            list.slideToggle(100);
            // dropDowns.forEach(function(item) {
            //     if (item[0].selector !== '#user-menu-button') {
            //         item[0].removeClass('active');
            //         item[1].slideUp(100);
            //     }
            // });
        });
        list.mouseleave(function(e){
            jQuery(this).slideToggle(100);
            button.removeClass('active');
        });
        // if (typeof window.dropDowns === 'undefined'){
        //     window.dropDowns = [];
        // }
        // window.dropDowns.push([button, list]);
    });
</script><?php
App::endJsCapture();

// RETURN
return;

//
// Code store for other projects
//

