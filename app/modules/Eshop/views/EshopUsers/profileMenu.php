<?php /* @var $this Template */
$this->displayOriginComment = true;
?><div class="user-profile-menu"><?php 
    $itemUrl = App::getContentUrlByPid('App.Users.update');
    $tag = 'a';
    $href = ' href="' . $itemUrl . '"';
    if (App::$urlPath === $itemUrl) {
        $tag = 'span';
        $href = '';
    }
    ?><<?php echo $tag . $href ?> class="item"><?php
        ?><h1 class="title"><?php 
            echo __(__FILE__, 'My account') 
        ?></h1><?php
        echo __(__FILE__, 'Here you can change all informations about you');       
    ?></<?php echo $tag ?>><?php
    $itemUrl = App::getContentUrlByPid('Eshop.EshopOrders.index');
    $tag = 'a';
    $href = ' href="' . $itemUrl . '"';
    if (App::$urlPath === $itemUrl) {
        $tag = 'span';
        $href = '';
    }
    ?><<?php echo $tag . $href ?> class="item"><?php
        ?><h1 class="title"><?php 
            echo __(__FILE__, 'My orders') 
        ?></h1><?php
        echo __(__FILE__, 'Here you can see your orders');       
    ?></<?php echo $tag ?>><?php
?></div><?php

// RETURN
return;

//
// Code store for other projects
//
