<?php
$params = $this->params;
?><mrpRequest><?php
    ?><request command="<?php echo $params['command'] ?>" requestId="<?php echo $params['requestId'] ?>"><?php
    ?></request><?php
    ?><data><?php
        if (!empty($params['filters'])) {
            ?><filter><?php
                foreach ($params['filters'] as $name => $value) {
                    ?><fltvalue name="<?php echo $name ?>"><?php 
                        echo $value 
                    ?></fltvalue><?php
                }
            ?></filter><?php
        }
        if (!empty($params['params'])) {
            ?><params><?php
                foreach ($params['params'] as $name => $value) {
                    ?><paramvalue name="<?php echo $name ?>"><?php 
                        echo $value 
                    ?></paramvalue><?php
                }
            ?></params><?php
        }
        if (!empty($params['orders'])) {
            echo $params['orders'];
        }
    ?></data><?php
?></mrpRequest><?php

