<?php
$uniqueClass = uniqid('report-table-');
?><table class="<?php echo $uniqueClass ?>"><?php
    ?><tr><?php
        ?><th><?php
            echo __a(__FILE__, 'Voucher code');
        ?></th><?php 
        ?><th><?php
            echo __a(__FILE__, 'Discount rate');
        ?></th><?php 
        ?><th><?php
            echo __a(__FILE__, 'Usages limit');
        ?></th><?php 
        ?><th><?php
            echo __a(__FILE__, 'Import result');
        ?></th><?php 
    ?></tr><?php
    foreach ($this->params as $item) {
        ?><tr><?php
            for ($i = 0; $i <= 3; $i++) {
                ?><td><?php 
                    if ($i === 3) {
                        $color =  !empty($item[4]) ? 'red' : 'green';
                        ?><span style="color:<?php echo $color ?>"><?php 
                            echo $item[$i];
                        ?></span><?php
                    }
                    else {
                        echo $item[$i];
                    }
                    if ($i === 1) {
                        echo ' %';
                    }
                ?></td><?php
            }
        ?></tr><?php
    }
?></table><?php
App::startCssCapture();
?><style>
    .<?php echo $uniqueClass ?> th,
    .<?php echo $uniqueClass ?> td {
        padding: 5px 20px 0 0;
    }
</style><?php
App::endCssCapture();
