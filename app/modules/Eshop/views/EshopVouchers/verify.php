<?php /* @var $this Template *//** @var Template $this */
$this->displayOriginComment = true;

Html::startCapture();
?><div class="input-group-wrapper"><?php 
    ?><div class="label-wrapper"><?php 
        ?>:l:<?php
    ?></div><?php   
    ?><div class="input-wrapper"><?php 
        ?>:i::e:<?php
    ?></div><?php
?></div><?php
$inputTemplate = Html::endCapture();

App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    'data' => $this->params['data'],
    'errors' => $this->params['errors'],
    'required' => $this->params['required'],
    'inputDefaults' => array(
        'template' => $inputTemplate,
    ),
    'labelDefaults' => array(
        'separator' => '',
    ),
));

$formId = uniqid('verify-voucher-');

?><form action="" method="post" id="<?php echo $formId ?>" class="verify-voucher"><?php 
    echo $Form->hidden('_target', array(
        'explicitValue' => 'Eshop.EshopVouchers.verify'
    ));
    
    echo $Form->text('verified_code', array(
        'label' => __(__FILE__, 'Zľavový kód'),
        //'hint' => __(__FILE__, 'Zadajte zľavový kód'),
        //'placeholder' => Str::getRandom(10, 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'),
    ));
    
    Html::startCapture();
    ?><div class="input-group-wrapper sc-input" style="display:none"><?php 
        ?><div class="label-wrapper"><?php 
            ?>:l:<?php
            ?><a <?php 
                ?>class="sc-change" <?php 
                ?>onclick="getElementById('voucher-sc').src='/_sc?name=voucher&color=1C151C&bgColor=ffffff&' + new Date().getTime();"<?php 
            ?>><?php
                echo __(__FILE__, '[zmeniť]')
            ?></a><?php        
        ?></div><?php   
        ?><div class="input-wrapper"><?php 
            ?><div><?php 
                ?><div class="sc-wrapper"><?php 
                    ?>:i:<?php 
                    ?><div class="sc"><?php 
                        ?><img <?php 
                            ?>src="/_sc?name=voucher&color=1C151C&bgColor=ffffff" <?php 
                            ?>id="voucher-sc" <?php 
                            ?>onclick="this.src='/_sc?name=voucher&color=1C151C&bgColor=ffffff&' + new Date().getTime();"<?php 
                        ?>>:<?php 
                    ?></div><?php 
                ?></div><?php 
                ?>:e:<?php
            ?></div><?php
        ?></div><?php
    ?></div><?php
    $scInputTemplate = Html::endCapture();
    echo $Form->text('sc', array(
        'label' => __(__FILE__, 'Bezpečnostný kód'),
        'hint' => __(__FILE__, 'Opíšte prosím uvedený 5 znakový bezpečnostný kód. Aj je ťažko čitateľný, tak si ho môžete zmeniť.'),
        'explicitValue' => '',
        'template' => $scInputTemplate
    ));
    
    ?><button type="submit" class="button"><?php 
        echo __(__FILE__, 'Overiť')
    ?></button><?php
    
    if ($this->params['result']) {
        ?><div class="result-wrapper"><?php 
            $class = 'active';
            $message = __(__FILE__, 'Zadaný zľavový kód je platný');
            if (empty($this->params['result']['active'])) {
                $class = 'inactive';
                $message = __(__FILE__, 'Zadaný zľavový kód nie je platný');
            }
            if (!empty($this->params['result']['message'])) {
                $message = $this->params['result']['message'];
            }
            ?><div class="result <?php echo $class ?>"><?php 
                echo $message;
            ?></div><?php
        ?></div><?php
    }
    
?></form><?php
    $jsOptions = array(
        'selector' => '#' . $formId,
    );
    App::startJsCapture();
    ?><script type="text/javascript">jQuery(function(){new Run.App.SecureForm(<?php echo json_encode($jsOptions) ?>);});</script><?php
    App::endJsCapture();
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
    '/app/js/libs/globalFunctions.js',
    '/app/js/libs/SecureForm.js',
));
