<?php /* @var $this Template *//** @var Template $this */
$this->displayOriginComment = true;

Html::startCapture();
?><div style="padding-top: 51mm; padding-left: 13mm;">
    <h1 style="font-size: 20pt">:code:</h1>
    <small>Zľavový kód je platný do :activeTo:</small>
</div><?php
$defaultTemplate = Html::endCapture();

// if no 'voucher' is provided then return the default template
// (used to normalize empty value of setting 'EshopVouchers.admin_print.template'
if (empty($this->params['voucher'])) {
    echo $defaultTemplate;
    return;
}

if (!($template = $this->getSetting('EshopVouchers.admin_print.template'))) {
    $template = $defaultTemplate;
}

$inserts = $this->params['voucher'];
$inserts['activeFrom'] = Date::format($inserts['active_from'], 'j.n.Y');
$inserts['activeTo'] = Date::format($inserts['active_to'], 'j.n.Y');
if ($inserts['discount_rate']) {
    $inserts['discount'] = floor($inserts['discount_rate']) . ' %';
}
else {
    $inserts['discount'] = Eshop::formatPrice($inserts['discount']);
}

echo Str::fill($template, $inserts);

App::startJsCapture(); 
?><script type="text/javascript">
    jQuery(document).ready(function(){
        window.print();
        var SmartTabs = top.Run.App.SmartTabs.getInstance('#adminTabs');
        if (SmartTabs) {            
            setTimeout(function(){
                SmartTabs.closeActualTab();
            }, 5000);
        }
    });
</script><?php 
App::endJsCapture();
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
    '/app/js/libs/globalFunctions.js',
));


