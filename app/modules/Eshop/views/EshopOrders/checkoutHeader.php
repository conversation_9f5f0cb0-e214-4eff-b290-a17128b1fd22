<?php /* @var $this Template */
$this->displayOriginComment = true;
/**
 * Partial view (subview) used in views/EshopCarts/view and views/EshopOrders/checkoutStepXY
 * 
 * following items are avaited in $this->params:
 *  - 'checkoutStep' (string) Possible values are 'cart' and values of EshopOrder::$checkoutWizardSteps
 *  - 'productsCount' (integer) Cart products count
 */
$defaults = array(
    'checkoutStep' => null,
    'productsCount' => null,
);
$this->params = array_merge($defaults, $this->params);
$productCount = $this->params['productsCount'];
$itemName =  __(__FILE__, 'items2');
if ($productCount == 1) {
    $itemName =  __(__FILE__, 'item');
} 
elseif (
    $productCount > 1 
    && $productCount < 5
) {
    $itemName =  __(__FILE__, 'items');
}
?><div class="checkout-header"><?php 
    /*/
    ?><div class="title"><?php 
        echo __(__FILE__, 'My shopping cart');
        ?><span class="cart-items-number"><?php 
            echo ' (' . $productCount . '&nbsp;' . $itemName . ')';
        ?></span><?php
    ?></div><?php
    /*/
    echo $this->loadView('EshopOrders/checkoutProgressBar', array(
        'checkoutStep' => $this->params['checkoutStep'],
    ));
?></div><?php
