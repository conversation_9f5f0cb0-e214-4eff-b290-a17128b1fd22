<?php /* @var $this Template */
$this->displayOriginComment = true;
$orders = $this->params['orders'];
$liveOrderStatuses = $this->params['liveOrderStatuses'];
$locatorProductView = App::getContentLocatorByPid('Eshop.EshopProducts.view');
// orders index
?><div class="orders-index"><?php
    // header
    ?><div class="orders-index-header"><?php 
        ?><h1 class="title"><?php 
            echo __(__FILE__, 'My orders');
        ?></h1><?php
        echo __(__FILE__, 'Here you can see your orders');
    ?></div><?php
    if (empty($orders)) {
        echo App::loadElement('App', 'emptyIndexMessage', array('message' => __(__FILE__, 'You have no orders yet')));
    }
    else {
        ?><div class="orders"><?php 
            ?><div class="orders-header"><?php 
                ?><div class="order-actions"><?php 
                ?></div><?php
                ?><div class="order-number"><?php 
                    echo __(__FILE__, 'Order number');
                ?></div><?php
                ?><div class="order-created"><?php 
                    echo __(__FILE__, 'Order created');
                ?></div><?php
                ?><div class="order-products-price"><?php 
                    echo __(__FILE__, 'Products price');
                ?></div><?php
                ?><div class="order-shipment-price"><?php 
                    echo __(__FILE__, 'Shipment price');
                ?></div><?php
                ?><div class="order-total-price"><?php 
                    echo __(__FILE__, 'Total price');
                ?></div><?php
                ?><div class="order-status"><?php 
                    echo __(__FILE__, 'Status');
                ?></div><?php
                ?><div class="order-shipment-and-payment-method"><?php 
                    echo __(__FILE__, 'Shipment and payment');
                ?></div><?php
            ?></div><?php
            // orders
            $productsGrandTotal = 0;
            $grandTotal = 0;
            foreach ($orders as $order) {
                // order 
                $orderOverviewId = uniqid('order-overview-');
                ?><div class="order"><?php 
                    ?><div class="order-actions"><?php 
                        ?><i class="fa fa-eye show-order-overview" data-order-overview-id="<?php echo $orderOverviewId ?>"></i><?php
                    ?></div><?php
                    ?><div class="order-number"><?php 
                        echo $order['number'];
                    ?></div><?php
                    ?><div class="order-created"><?php 
                        echo Date::format($order['created'], 'd.m.Y H:i');
                    ?></div><?php
                    ?><div class="order-products-price"><?php 
                        echo Eshop::formatPrice($order['products_price_to_pay']);
                    ?></div><?php
                    ?><div class="order-shipment-price"><?php 
                        echo Eshop::formatPrice($order['shipment_price_actual_taxed'] + $order['payment_price_actual_taxed']);
                    ?></div><?php
                    ?><div class="order-total-price"><?php 
                        echo Eshop::formatPrice($order['order_price_to_pay']);
                    ?></div><?php
                    ?><div class="order-status"><?php 
                        echo __(__FILE__, $order['status']);
                        ?><br><?php
                        echo __(__FILE__, $order['payment_status']);
                    ?></div><?php
                    ?><div class="order-shipment-and-payment-method"><?php 
                        $text = '';
                        if (!empty($order['shipment_method_name'])) {
                            $text .= $order['shipment_method_name'];
                        }
                        if (!empty($order['payment_method_name'])) {
                            $text .= $text ? ', ' : '';
                            $text .= $order['payment_method_name'];
                        }
                        echo $text;
                    ?></div><?php
                ?></div><?php
                // order overview
                ?><div id="<?php echo $orderOverviewId ?>" class="order-overview"><?php 
                    ?><div class="order-overview-title"><?php 
                        echo __(__FILE__, 'Order items')
                    ?></div><?php
                    ?><div class="products"><?php 
                        ?><div class="products-header"><?php 
                            ?><div class="product-code"><?php 
                                echo __(__FILE__, 'Code');
                            ?></div><?php
                            ?><div class="product-name"><?php 
                                echo __(__FILE__, 'Name');
                            ?></div><?php
                            ?><div class="product-amount"><?php 
                                echo __(__FILE__, 'Amount');
                            ?></div><?php
                            ?><div class="product-reserved-amount"><?php 
                                echo __(__FILE__, 'Stock amount');
                            ?></div><?php
                            ?><div class="product-unit-price"><?php 
                                echo __(__FILE__, 'Unit price');
                            ?></div><?php
                            ?><div class="product-total-price"><?php 
                                echo __(__FILE__, 'Total price');
                            ?></div><?php
                            ?><div class="product-attributes"><?php 
                                echo __(__FILE__, 'Attributes');
                            ?></div><?php
                        ?></div><?php
                        // overview products
                        foreach ($order['items'] as $orderItem) {
                            $urlShowDetail = App::getUrl(array(
                                'locator' => $locatorProductView,
                                'args' => array($orderItem['slug'])
                            ));
                            $disponibility = '';
                            $disponibilityClass = '';
                            switch ($orderItem['disponibility']) {
                                case EshopProduct::STOCK:
                                    $disponibility = __(__FILE__, 'Available on stock');
                                    $disponibilityClass = ' on-stock';
                                    break;
                                case EshopProduct::SUPPLIER:
                                    $disponibility = __(__FILE__, 'Available at supplier');
                                    $disponibilityClass = ' at-supplier';
                                    break;
                                case EshopProduct::ON_DEMAND:
                                    $disponibility = __(__FILE__, 'On demand');
                                    $disponibilityClass = ' on-demand';
                                    break;
                                case EshopProduct::SOLDOUT:
                                    $disponibility = __(__FILE__, 'Sold out');
                                    $disponibilityClass = ' sold-out';
                                    break;
                                case EshopProduct::PRESALE:
                                    $disponibility = __(__FILE__, 'Presale');
                                    $disponibilityClass = ' presale';
                                    break;
                                default:
                                break;
                            }
                            $orderItem['static_attributes'] = json_decode($orderItem['static_attributes']);
                            $orderItem['dynamic_attributes'] = json_decode($orderItem['dynamic_attributes']);
                            $attributesHtml = '';
                            foreach($orderItem['static_attributes'] as $key => $value) {
                                $attributesHtml .= __(__FILE__, $key) . ': ' . $value . '<br>';
                            }
                            foreach($orderItem['dynamic_attributes'] as $key => $value) {
                                $attributesHtml .= __(__FILE__, $key) . ': ' . $value . '<br>';
                            }
                            $insufficientClass = '';
                            if ($orderItem['reserved_amount'] < $orderItem['amount']) {
                                $insufficientClass = ' insufficient';
                            }
                            ?><div class="product"><?php 
                                ?><div class="product-code"><?php 
                                    echo $orderItem['code'];
                                ?></div><?php
                                ?><div class="product-name<?php echo $disponibilityClass ?>"><?php 
                                    ?><a href="<?php echo $urlShowDetail; ?>"><?php 
                                        echo $orderItem['name'];
                                    ?></a><?php
                                    if (isset($liveOrderStatuses[$order['status']])) {                                        
                                        ?><div class="disponibility"><?php 
                                            echo $disponibility;
                                        ?></div><?php
                                        echo $this->loadView('EshopProducts/availableFromLabel', array('product' => $orderItem));
                                    }
                                ?></div><?php
                                ?><div class="product-amount"><?php 
                                    echo $orderItem['amount'];
                                ?></div><?php
                                ?><div class="product-reserved-amount<?php echo $insufficientClass ?>"><?php 
                                    echo $orderItem['reserved_amount'];
                                ?></div><?php
                                ?><div class="product-unit-price"><?php 
                                    echo Eshop::formatPrice($orderItem['price_actual_taxed']);
                                ?></div><?php
                                ?><div class="product-total-price"><?php 
                                    echo Eshop::formatPrice($orderItem['amount'] * $orderItem['price_actual_taxed']);
                                ?></div><?php
                                ?><div class="product-attributes"><?php 
                                    echo $attributesHtml;
                                ?></div><?php
                            ?></div><?php
                        }
                        if ((float)$order['bonus_discount']) {
                            ?><div class="product"><?php 
                                ?><div class="product-code"></div><?php
                                ?><div class="product-name"><?php 
                                    echo __(__FILE__, 'Bonus');
                                ?></div><?php
                                ?><div class="product-amount"></div><?php
                                ?><div class="product-reserved-amount"></div><?php
                                ?><div class="product-unit-price"></div><?php
                                ?><div class="product-total-price"><?php 
                                    echo '-' . Eshop::formatPrice($order['bonus_discount']);
                                ?></div><?php
                                ?><div class="product-attributes"></div><?php
                            ?></div><?php
                        }
                    ?></div><?php
                    ?><div class="order-info"><?php 
                        if (!empty($order['shipment_method_name'])) {
                        ?><b><?php 
                            echo __(__FILE__, 'Shipment') . ': ';
                        ?></b><?php
                            echo $order['shipment_method_name'] . ' (cena ' . Eshop::formatPrice($order['shipment_price_actual_taxed']) . ')';
                        ?><br><?php
                        }
                        if (!empty($order['payment_method_name'])) {
                        ?><b><?php 
                            echo __(__FILE__, 'Payment') . ': ';
                        ?></b><?php
                            echo $order['payment_method_name'];
                            if ((float)$order['payment_price_actual_taxed']) {
                                echo ' (cena ' . Eshop::formatPrice($order['payment_price_actual_taxed']) . ')';
                            }
                            if (!empty($order['paymentUrl'])) {
                                ?> <a href="<?php echo $order['paymentUrl'] ?>" target="_blank" class="payment-link"><?php 
                                    echo __(__FILE__, 'Zaplatiť') 
                                ?></a><?php
                            }
                        ?><br><?php
                        }
                        ?><br><?php
                        ?><b><?php 
                            echo __(__FILE__, 'Your note'). ': ' . $order['comment'];
                        ?></b><?php
                        ?><br><?php
                    ?></div><?php
                ?></div><?php
                $productsGrandTotal += $order['products_price_to_pay'];
                $grandTotal += $order['order_price_to_pay'];
            }
            // footer
            ?><div class="orders-footer"><?php 
                ?><div class="empty-before"></div><?php
                ?><div class="orders-grand-total-label"><?php 
                    echo __(__FILE__, 'TOTAL');
                ?></div><?php
                ?><div class="orders-products-grand-total-price"><?php 
                    echo Eshop::formatPrice($productsGrandTotal);
                ?></div><?php
                ?><div class="orders-grand-total-price"><?php 
                    echo Eshop::formatPrice($grandTotal);
                ?></div><?php
                ?><div class="empty-after"></div><?php
            ?></div><?php
        ?></div><?php
    }
?></div><?php
App::startJsCapture();
?><script type="text/javascript">
jQuery(function(){
    jQuery('.show-order-overview').on('click', function(){
        var overviewId = jQuery(this).attr('data-order-overview-id'), $overview = jQuery('#' + overviewId);
        $overview.slideToggle();
    });
});  
</script><?php
App::endJsCapture();
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
));
App::setCssFiles(array(
    '/app/css/vendors/font-awesome/css/font-awesome.min.css',
));

// RETURN
return;

//
// Code store for other projects
//
