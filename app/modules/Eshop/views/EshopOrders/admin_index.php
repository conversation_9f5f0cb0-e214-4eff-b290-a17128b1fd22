<?php /* @var $this Template */
$this->displayOriginComment = true;
$options = $this->params;
$lowestPurchasePriceSupplierProducts = $options['lowestPurchasePriceSupplierProducts'];
if ($options['Paginator']) {
    App::loadLib('App', 'FormHelper');
    $Form = new FormHelper(array('useDataWrapper' => false));
}
?><div class="-run-smart-index -run-protheme container-fluid<?php echo $options['class'] ?>"><?php 
    ?><div class="-run-six-header row"><?php 
        // build headerTemplate inserts
        $inserts = array(
            ':title:' => $options['title'],
            ':messages:' => App::messages(array(
                'class' => 'app-message',
                'view' => 'App/bootstrapMessages',
                'timeout' => false,
            )),
        );
        $inserts[':paginator:'] = '';
        if ($options['Paginator']) {
            $options['Paginator']->setLinksOptions(array(
                'prevImg' => '<i class="fa fa-angle-left"></i>',
                'nextImg' => '<i class="fa fa-angle-right"></i>',
                'firstPageText' => '<i class="fa fa-angle-double-left"></i>',
                'lastPageText' => '<i class="fa fa-angle-double-right"></i>',
//                'prevImg' => '<i class="fa fa-caret-left fa-lg"></i>',
//                'nextImg' => '<i class="fa fa-caret-right fa-lg"></i>',
//                'firstPageText' => '<i class="fa fa-fast-backward"></i>',
//                'lastPageText' => '<i class="fa fa-fast-forward"></i>',
            ));
            if (($links = $options['Paginator']->getLinks())) {
                $inserts[':paginator:'] = '<div class="-run-six-paginator">' . $links . '</div>';
            }
            if (($counts = $options['Paginator']->getCountOfDisplayed())) {
                $inserts[':paginator:'] .= '<div class="-run-six-count-info"><span>' . $counts . '</span></div>';
            }
        }
        $inserts[':actionsRight:'] = '';
        $inserts[':actionsLeft:'] = '';
        foreach ($options['actions'] as $actionName => $action) {
            $actionHtml = Html::action(
                $action,
                array(
                    'attributes' => array('role' => 'button'),
                    'tag' => 'a',
                )
            );
            if ($actionName === 'lang') {
                $inserts[':actionsLeft:'] .= $actionHtml;
            }
            else {
                $inserts[':actionsRight:'] .= $actionHtml;
            }
        }
        $bulkActionsHtml = '';
        foreach ($options['bulkActions'] as $actionName => $action) {
            $bulkActionsHtml .= Html::action(
                $action,
                array(
                    'attributes' => array('role' => 'button', 'class' => 'inactive'),
                    'tag' => 'a',
                )
            );
        }
        if ($bulkActionsHtml) {
            $bulkActionsHtml = '<div class="-run-six-actions -run-six-actions-bulk">' . $bulkActionsHtml . '</div>';
            $inserts[':actionsRight:'] .= $bulkActionsHtml;
        }
        if ($inserts[':actionsLeft:']) {
            $inserts[':actionsLeft:'] = '<div class="-run-six-actions -run-six-actions-left">' . $inserts[':actionsLeft:'] . '</div>';
        }
        if ($inserts[':actionsRight:']) {
            $inserts[':actionsRight:'] = '<div class="-run-six-actions -run-six-actions-right">' . $inserts[':actionsRight:'] . '</div>';
        }
        // display header
        echo str_replace(array_keys($inserts), array_values($inserts), $options['headerTemplate']);
    ?></div><?php
    ?><div class="-run-six-index row<?php if(!empty($options['tree'])) { echo ' ' . $options['tree']['class']; }?>"><?php 
        ?><div class="-run-six-index-scroll"><?php 
            ?><table class="table table-hover<?php if ($options['striped']) { echo ' table-striped'; } ?>"><?php 
                if (!empty($options['columns'])) : 
                    ?><thead><?php 
                        ?><tr><?php 
                            $colClass = 'odd'; 
                            ?><th class="<?php echo $colClass ?>"><?php 
                                ?><div class="-run-six-cell-content"><?php 
                                    echo __a(__FILE__, 'Action') 
                                ?></div><?php 
                            ?></th><?php 
                            foreach ($options['columns'] as $field => $column) : 
                                $colClass = ($colClass === 'odd') ? 'even' : 'odd';
                                ?><th class="<?php echo $colClass ?>"><?php 
                                    ?><div class="-run-six-cell-content"><?php    
                                        if ($options['Paginator']) : 
                                            ?><span class="-run-pg-sort-switch" data-run-pg-sort-field="<?php echo $field ?>"><?php 
                                                ?><span class="-run-pg-sort-switch-icon"></span><?php 
                                                echo $column['label']
                                            ?></span><?php 
                                        else:
                                            echo $column['label']; 
                                        endif; 
                                    ?></div><?php 
                                ?></th><?php
                            endforeach; 
                            if (!empty($options['bulkActions'])) {
                                $colClass = ($colClass === 'odd') ? 'even' : 'odd';
                                ?><th class="<?php echo $colClass ?>"><?php
                                    echo $Form->checkbox('', array(
                                        'class' => '-run-bulk-selects-reset', 
                                        'title' => __(__FILE__, 'Unselect all'),
                                        'disabled' => 'disabled'
                                    ))
                                ?></th><?php
                            }
                        ?></tr><?php 
                        if ($options['Paginator']) : 
                        ?><tr><?php 
                            $colClass = 'odd'; 
                            ?><th class="<?php echo $colClass ?>"></th><?php 
                            foreach ($options['columns'] as $field => $column) : 
                                $colClass = ($colClass === 'odd') ? 'even' : 'odd';
                                ?><th class="<?php echo $colClass ?>"><?php 
                                    ?><div class="-run-six-cell-content"><?php
                                        if (!empty($options['paginatorOptions']['filterSelectInputs'][$field])) {
                                            echo $Form->select('filter', array(
                                                'class' => 'form-control input-sm -run-pg-filter-input',
                                                'data-run-pg-filter-field' => $field,
                                                'options' => $options['paginatorOptions']['filterSelectInputs'][$field],
                                                'empty' => true,
                                            )) ;
                                        }
                                        else {
                                            echo $Form->text('filter', array(
                                                'class' => 'form-control input-sm -run-pg-filter-input',
                                                'data-run-pg-filter-field' => $field,
                                            )) ;
                                            echo Html::hint(__a(__FILE__, 
                                                'Use following expressions for filtering:<ul><li><code>abc</code> - contains abc</li><li><code>!abc</code> - does not contain abc</li><li><code>abc*</code> - starts by abc</li><li><code>!abc*</code> - does not start by abc</li><li><code>*abc</code> - ends by abc</li><li><code>!*abc</code> - does not end by abc</li><li><code>abc*klm*xyz</code> - starts by abc, contains klm and ends by xyz</li><li><code>=abc</code> - equals to abc</li><li><code>!=abc</code> - not equals to abc</li><li><code>=</code> - is empty</li><li><code>!=</code> or <code>!</code> - is not empty</li><code>NULL</code> - is NULL</li><li><code>!NULL</code> - is not NULL</li><li><code>&gt;abc</code> - is greater than abc</li><li><code>&lt;abc</code> - is less than abc</li><li><code>&gt;=abc</code> - is greater or equals to abc</li><li><code>&lt;=abc</code> - is less or equals to abc</li></ul>These expressions can be combined with operators <code>&</code> and <code>+</code>:<ul><li><code>&gt;abc&*x</code> - greater than abc <u>and</u> ending by x</li><li><code>&gt;abc+&lt;klm</code> - greater than abc <u>or</u> less than klm</li></ul>To reset filter use <kbd>Ctrl+Enter</kbd>'
                                            ), array('jsOptions' => array('position' => 'bottom')));
                                        }
                                    ?></div><?php 
                                ?></th><?php
                            endforeach; 
                        ?></tr><?php 
                        endif; 
                    ?></thead><?php 
                endif; 
                if (!empty($options['records'])) : 
                    ?><tbody><?php 
                        // ROWS
                        $rowClass = 'even';
                        foreach ($options['records'] as $recordIndex => $record) : 
                            $rowClass = ($rowClass === 'odd') ? 'even' : 'odd';
                            $attributes = array('class' => $rowClass);
                            // get attributes from row render
                            if (!empty($options['renderRow'])) {
                                $attributes = Html::getSmartIndexRenderRowAttributes(
                                    $options['renderRow'], 
                                    $record, 
                                    $attributes
                                );
                            }
                            if ($options['recordDefaultAction']) {
                                list($attributes) = Html::getActionAttributesAndContent(
                                    $options['recordDefaultAction'],
                                    array(
                                        'data' => $record,
                                        'dataPrimaryKey' => $options['primaryKey'],
                                        'attributes' => $attributes,
                                    )
                                );
                            }
                            if (!empty($options['recordActions']['move']['url'])) {
                                list($attributes) = Html::getActionAttributesAndContent(
                                    $options['recordActions']['move'],
                                    array(
                                        'data' => $record,
                                        'dataPrimaryKey' => $options['primaryKey'],
                                        'attributes' => $attributes,
                                    )
                                );
                            }
                            if (!empty($options['tree'])) {
                                list($attributes, $treeNavigation) = Html::getSmartIndexRecordTreeAttributesAndNavigation(
                                    $recordIndex,
                                    $attributes
                                );
                            }
                            unset($attributes['title']);
                            ?><tr<?php echo Html::attributes($attributes) ?>><?php 
                                // COLUMNS (row fields)
                                $colClass = 'odd'; 
                                ?><td class="<?php echo $colClass ?>"><?php  
                                    ?><div class="-run-six-cell-content"><?php 
                                        foreach ($options['recordActions'] as $actionName => $action) :
                                            // skip 'move' action, its applyed on drag&drop by SmartIndex.js
                                            if ($actionName === 'move') {
                                                continue;
                                            }
                                            // check callable or data condition 'if' on each record separately
                                            if (
                                                !empty($action['if'])
                                                && ( 
                                                    Validate::callableFunction($action['if'])
                                                    && !call_user_func_array($action['if'], array($record))
                                                    ||
                                                    !Validate::callableFunction($action['if'])
                                                    && is_array($action['if'])
                                                    && !Validate::dataConditions($record, $action['if'], array('flatData' => true))
                                                )
                                            ) {
                                                continue;
                                            }
                                            // check callable or data condition 'rights' on each record separately
                                            if (
                                                !empty($action['rights'])
                                                && ( 
                                                    Validate::callableFunction($action['rights'])
                                                    && !call_user_func_array($action['rights'], array($record))
                                                    ||
                                                    !Validate::callableFunction($action['rights'])
                                                    && is_array($action['rights'])
                                                    && !Validate::dataConditions($record, $action['rights'], array('flatData' => true))
                                                )
                                            ) {
                                                continue;
                                            }
                                            echo Html::action(
                                                $action,
                                                array(
                                                    'data' => $record,
                                                    'dataPrimaryKey' => $options['primaryKey'],
                                                    'attributes' => array('role' => 'button'),
                                                )
                                            );
                                            echo '<br>';
                                        endforeach; 
                                    ?></div><?php 
                                ?></td><?php
    //                            App::debug($record); 
                                $colClass = ($colClass === 'odd') ? 'even' : 'odd';
                                ?><td class="<?php echo $colClass ?>" colspan="<?php echo count($options['columns']); ?>"><?php 
                                    ?><table class="orderInfo"><?php
                                        ?><tbody><?php 
                                            ?><tr><?php
                                                ?><td colspan="<?php echo (empty($record['eshop_voucher_code']) ? 3 : 2) ?>"><?php 
                                                    echo __a(__FILE__, 'Status') . ': <b>' . __(__FILE__, $record['status']) . '</b>'; 
                                                    $paymentWarning = '';
                                                    if (
                                                        isset($record['payment_signed'])
                                                        && empty($record['payment_signed'])
                                                        && (
                                                            $record['payment_status'] === 'enum_payment_advance_paid'
                                                            || $record['payment_status'] === 'enum_payment_partially_paid'
                                                            || $record['payment_status'] === 'enum_payment_paid'
                                                        )
                                                    ) {
                                                        $paymentWarning = __a(__FILE__, 'Unsecured response from bank');
                                                    }
                                                    elseif (
                                                        $record['payment_status'] === 'enum_payment_manipulated'
                                                    ) {
                                                        $paymentWarning = __a(__FILE__, 'Manipulated response from bank');
                                                    }
                                                    if ($paymentWarning) {
                                                        $paymentWarning = ' <b class="important">(' . $paymentWarning . ')</b>';
                                                    }
                                                    echo ', '. __a(__FILE__, 'Status of payment') . ': <b>' . __(__FILE__, $record['payment_status']) . '</b>' . $paymentWarning;
                                                    if (
                                                        $record['specific'] & EshopOrder::SPECIFIC_SHIPMENT 
                                                        ||
                                                        $record['specific'] & EshopOrder::SPECIFIC_PAYMENT
                                                    ) {
                                                        $message = ', <strong class="important">' . __a(__FILE__, 'Specific order') . '</strong> - ';
                                                        if (
                                                            $record['specific'] & EshopOrder::SPECIFIC_SHIPMENT 
                                                            && $record['shipment_price_taxless'] === null
                                                        ) {
                                                            $message .= __a(__FILE__, 'missing shipment price') . ', ';
                                                        }
                                                        if (
                                                            $record['specific'] & EshopOrder::SPECIFIC_PAYMENT 
                                                            && $record['payment_price_taxless'] === null
                                                        ) {
                                                            $message .= __a(__FILE__, 'missing payment price') . ', ';
                                                        }
                                                        echo rtrim($message, '-, ');
                                                    }
                                                ?></td><?php
                                                if (!empty($record['eshop_voucher_code'])) {
                                                    ?><td><?php echo __a(__FILE__, 'Voucher code') . ': <strong>' . $record['eshop_voucher_code'] . '</strong>'; ?></td><?php
                                                }
                                            ?></tr><?php
                                            ?><tr><?php
                                                ?><td style="width: 30%;"><?php echo __a(__FILE__, 'Customer'); ?>: <strong><?php 
                                                    echo $record['fullname'];
                                                    ?></strong><?php
                                                    if (!empty($record['company_fullname'])) {
                                                        echo ', ' . __a(__FILE__, 'company') . ': <strong>' . $record['company_fullname'] . '</strong>';
                                                    }
                                                ?></td><?php 
                                                ?><td style="width: 30%;"><?php 
                                                    echo __a(__FILE__, 'Order number') . ': ';
                                                    ?><strong class="orderNumber"><?php 
                                                        echo $record['number'];                                                    
                                                    ?></strong><?php
                                                ?></td><?php
                                                ?><td style="width: 30%;"><?php echo __a(__FILE__, 'Spôsob platby') . ': <strong>' . $record['payment_method_name'] . '</strong>'; ?></td><?php
                                            ?></tr><?php
                                            ?><tr><?php
                                                ?><td><?php echo (!empty($record['run_users_id']) ? __a(__FILE__, 'REGISTERED CLIENT') : __a(__FILE__, 'UNREGISTERED CLIENT')); ?></td><?php
                                                ?><td><?php 
                                                    echo __a(__FILE__, 'Dátum a čas objednania') . ': ';
                                                    ?><strong><?php 
                                                        echo date('d.m.Y H:i', strtotime($record['created']));
                                                    ?></strong><?php
                                                    ?><br /><?php
                                                    echo __a(__FILE__, 'Dátum a čas exportu do MRP') . ': ';
                                                    if ($record['exported']) {
                                                        ?><strong><?php 
                                                            echo date('d.m.Y H:i', strtotime($record['exported']));
                                                        ?></strong><?php
                                                    }
                                                    else {
                                                        ?><strong class="important"><?php 
                                                            echo __a(__FILE__, 'export zlyhal');
                                                        ?></strong><?php                                                        
                                                    }
                                                ?></td><?php
                                                ?><td><?php echo __a(__FILE__, 'Spôsob doručenia') . ': <strong>' . $record['shipment_method_name'] . '</strong>'; ?></td><?php
                                            ?></tr><?php 
                                        ?></tbody><?php
                                    ?></table><?php 
                                    ?><br><?php 
                                    ?><table class="orderProducts" border="1" bordercolor><?php
                                        ?><tr><?php
                                            ?><th><?php echo __a(__FILE__, 'name'); ?></th><?php
                                            ?><th style="width: 20%;"><?php echo __a(__FILE__, 'Kód, MRP kód, EAN'); ?></th><?php
                                            ?><th style="width: 10%;"><?php echo __a(__FILE__, 'unit price'); ?></th><?php
                                            ?><th style="width: 10%;"><?php echo __a(__FILE__, 'amount'); ?></th><?php
                                            ?><th style="width: 10%;"><?php echo __a(__FILE__, 'price'); ?></th><?php
                                        ?></tr><?php
                                        if (!empty($options['products'][$record['id']])) :
                                            foreach ($options['products'][$record['id']] as $product) :
                                                $dynAttrs = json_decode($product['dynamic_attributes'], true);
                                                ?><tr><?php
                                                    ?><td><?php 
                                                        ?><a <?php 
                                                            ?>href="/mvc/Eshop/EshopProducts/admin_edit/<?php echo $product['run_eshop_products_id']; ?>/?_requestSource_=frame" <?php 
                                                            ?>target="_blank" <?php 
                                                            ?>data-run-st-creator<?php 
                                                        ?>><?php 
                                                            echo $product['name'];
                                                            foreach ($dynAttrs as $dynAttrName => $dynAttrValue) {
                                                                echo ', ' . $dynAttrName . ': ' . $dynAttrValue;
                                                            }
                                                        ?></a><?php 
                                                        /*/
                                                        $productId = $product['run_eshop_products_id'];
                                                        if (!empty($lowestPurchasePriceSupplierProducts[$productId]['supplier_pid'])) {
                                                            echo 
                                                                ' (' . 
                                                                    strtolower($lowestPurchasePriceSupplierProducts[$productId]['supplier_pid']) . 
                                                                ')';
                                                        }
                                                        //*/
                                                        /*/
                                                        if (!empty($product['supplier_pid'])) {
                                                            echo ' (' . strtolower($product['supplier_pid']) . ')';
                                                        }
                                                        //*/
                                                    ?></td><?php
                                                    ?><td class="a-right"><?php 
                                                        if (Validate::emptyValue($product['code'])) {
                                                            echo '???';
                                                        }
                                                        else {
                                                            echo $product['code'];
                                                        }
                                                        echo ', ';
                                                        if (Validate::emptyValue($product['mrp_code'])) {
                                                            ?><strong class="important"><?php 
                                                                echo '???';
                                                            ?></strong><?php
                                                        }
                                                        else {
                                                            echo $product['mrp_code'];
                                                        }
                                                        echo ', ';
                                                        if (Validate::emptyValue($product['ean'])) {
                                                            echo '???';
                                                        }
                                                        else {
                                                            echo $product['ean'];
                                                        }
                                                    ?></td><?php
                                                    ?><td class="a-right"><?php
                                                        if ($product['price_taxed'] > $product['price_actual_taxed']) {
                                                            echo '<span class="oldPrice">' . Eshop::formatPrice($product['price_taxed']) . '</span> ';
                                                        }
                                                        echo Eshop::formatPrice($product['price_actual_taxed']);
                                                    ?></td><?php
                                                    ?><td class="a-right"><?php echo $product['amount']; ?></td><?php
                                                    ?><td class="a-right"><?php echo Eshop::formatPrice($product['amount'] * $product['price_actual_taxed']) ?></td><?php
                                                ?></tr><?php
                                            endforeach;
                                        endif;
                                                ?><th colspan="4"><?php echo __a(__FILE__, 'Discount'); ?></th><?php
                                        if ((float)$record['bonus_discount']) {
                                            ?><tr><?php
                                                ?><th colspan="4"><?php echo __a(__FILE__, 'Bonus'); ?></th><?php
                                                ?><td class="a-right"><?php echo '-' . Eshop::formatPrice($record['bonus_discount']) ?></td><?php
                                            ?></tr><?php
                                        }
                                        ?><tr><?php
                                            ?><th colspan="4"><?php echo __a(__FILE__, 'Shipping and payment fee'); ?></th><?php
                                            ?><td class="a-right"><?php echo Eshop::formatPrice($record['shipment_price']) ?></td><?php
                                        ?></tr><?php
                                        ?><tr><?php
                                            ?><th colspan="4"><?php echo __a(__FILE__, 'Total value of order'); ?></th><?php
                                            ?><td class="a-right"><?php echo Eshop::formatPrice($record['order_price_to_pay']) ?></td><?php
                                        ?></tr><?php
                                        ?><tr><?php
                                            ?><td colspan="5"><?php echo __a(__FILE__, 'Client\'s note'); ?>: <?php echo str_replace("\n", '<br>', $record['comment']); ?></td><?php
                                        ?></tr><?php
                                        ?><tr><?php
                                    ?><td colspan="5" style="font-size: 0.9em"><?php echo str_replace("\n", '<br>', $record['notes']); ?></td><?php
                                        ?></tr><?php
                                    ?></table><br><br><?php 
                                ?></td><?php
                                if (!empty($options['bulkActions'])) {
                                    $colClass = ($colClass === 'odd') ? 'even' : 'odd';
                                    ?><td class="<?php echo $colClass ?>"><?php
                                        echo $Form->checkbox('', array(
                                            'class' => '-run-bulk-select',
                                            'toggleValue'=> Sanitize::value($record[$options['primaryKey']]),
                                        ))
                                    ?></td><?php
                                }
                            ?></tr><?php 
                        endforeach;     
                    ?></tbody><?php 
                endif; 
            ?></table><?php 
            // display empty index message
            if (empty($options['records'])) : 
                ?><div class="-run-six-empty-index-message"><?php 
                    echo $options['emptyIndexMessage'] 
                ?></div><?php 
            endif; 
        ?></div><?php 
    ?></div><?php 
?></div><?php 
App::startJsCapture();
?><script type="text/javascript">
jQuery(function(){
    new Run.App.SmartIndex(<?php echo json_encode(array(
        'loadPaginator' => (bool)$options['Paginator'],
        'paginatorOptions' => $options['paginatorOptions'],
        'tree' => $options['tree'],
        'moveUrl' => !empty($options['recordActions']['move']['url']) ? App::getUrl($options['recordActions']['move']['url']) : null,
    )) ?>);
}); 
</script><?php
App::endJsCapture();
App::startCssCapture();
?><style>
    .orderInfo {
        width: 100%;
    }
    .orderInfo > tbody {
        background:transparent !important;
    }
    .orderInfo th, .orderInfo td  {
        vertical-align: top;
    }
    .orderInfo .orderNumber {
        font-size: 1.2em;
        line-height: 1;
    }
    .orderProducts {
        width: 100%; 
        border-color: #888888;
    }
    .orderProducts th {
        padding: 2px !important;
        background-color: #dfdfdf;
    }
    .orderProducts td {
        padding: 2px !important;
    }
    .-run-smart-index .-run-six-index tbody .fa {
        margin-right: 4px;
    }
    .oldPrice {
        text-decoration: line-through; 
        color: #888888;
    }
    .a-right {
        text-align: right;
    }
    .important {
        color: #ba0707;
    }
</style><?php
App::endCssCapture();
App::setCssFiles(array(
    '/app/css/vendors/bootstrap.css',
    '/app/css/vendors/bootstrap-theme.css',
    '/app/css/vendors/font-awesome/css/font-awesome.min.css',
    '/app/css/libs/smartIndex.css',
));
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
    '/app/js/vendors/jquery-ui/jquery-ui.min.js',
    '/app/js/vendors/bootstrap.min.js',
    '/app/js/libs/globalFunctions.js',
    '/app/js/libs/App.js',
    '/app/js/libs/PhpJs.js',
    '/app/js/libs/String.js',
    '/app/js/libs/Number.js',
    '/app/js/libs/Utility.js',
    '/app/js/libs/Validate.js',
    '/app/js/libs/Sanitize.js',
    '/app/js/libs/SubWindow.js',
    '/app/js/libs/Arr.js',
    '/app/js/libs/WindowStateManager.js',
    '/app/js/libs/Paginator.js',
    '/app/js/libs/SmartIndex.js',
));
// js config for App.js
App::setJsConfig('App', array(
    'urlRoot' => App::$urlRoot,
    'urlLang' => App::$urlLang,
    'urlBase' => App::$urlBase,
    'homeSlug' => App::$homeSlug,
));
// i18n files
App::setJsI18nFiles(array(
    'App',
));

