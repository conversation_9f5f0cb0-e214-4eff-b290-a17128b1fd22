<?php /* @var $this Template */
$this->displayOriginComment = true;
/**
 * Partial view (subview) used in views/EshopCarts/view and views/EshopOrders/checkoutStepXY
 * 
 * following items are avaited in $this->params:
 *  - 'urlBack' (string) If provided then back link pointing to this url is created.
 *  - 'textBack' (string) Text (label) of the back link. Defaults to translation of 'Back'.
 *  - 'urlContinue' (string) If provided then continue link pointing to this url is created.
 *  - 'textContinue' (string) Text (label) of the continue link. Defaults to translation of 'Continue'.
 *  - 'submitFormId' (string) If provided then submit button to submit specified form is created.
 */
$defaults = array(
    'urlBack' => App::getRefererUrl('/'),
    'textBack' => __(__FILE__, 'Back'),
    'urlContinue' => null,
    'textContinue' => __(__FILE__, 'Continue'),
    'submitFormId' => null,
);
$this->params = array_merge($defaults, $this->params);
?><div class="checkout-footer"><?php 
    ?><a href="<?php echo $this->params['urlBack'] ?>" class="back"><?php 
        ?><i class="fa fa-chevron-left"></i><?php
        echo $this->params['textBack'];
    ?></a><?php
    if (!empty($this->params['urlContinue'])) {
        ?><a href="<?php echo $this->params['urlContinue'] ?>" class="button continue"><?php 
            ?><i class="fa fa-chevron-right"></i><?php
            echo $this->params['textContinue'];
        ?></a><?php
    }
    elseif (!empty($this->params['submitFormId'])) {
        ?><a href="javascript:void(0);" class="button continue"><?php 
            ?><i class="fa fa-chevron-right"></i><?php
            echo $this->params['textContinue'];
        ?></a><?php
        App::startJsCapture();
        ?><script type="text/javascript">
            jQuery(function(){
                
            var $form = jQuery('#<?php echo $this->params['submitFormId'] ?>'),
                $button = jQuery('.checkout-footer .button.continue');
            $button.on('click.submitCheckoutForm', function() {
                // disable double click on submit button
                $button.off('click.submitCheckoutForm');
                jQuery(document).on('keydown', function(event) {
                    if (
                        // prevent F5
                        event.which === 116
                        ||
                        // prevent ENTER
                        event.which === 13
                    ) {
                        event.preventDefault();
                    }
                });                
                $form.submit();
            });
            
            });
        </script><?php
        App::endJsCapture();
    }
?></div><?php
