<?php
$this->displayOriginComment = true;
$message = Sanitize::value($this->params['message']);
switch ($this->params['resultStatus']) {
    case 'enum_payment_partially_paid':
    case 'enum_payment_advance_paid':
    case 'enum_payment_paid':
        $resultStatus = __(__FILE__, 'Ďakujeme za Váš nákup.');
        break;

    case 'enum_payment_tout':
        $resultStatus = __(__FILE__, 'Ďakujeme za V<PERSON>š nákup. Platba sa spracováva. O výsledku spracovania platby Vás budeme informovať.');
        break;
    
    default:
        $resultStatus = __(__FILE__, 'Platba bohužiaľ zlyhala.');
        break;
}
?><div class="payment-response"><?php 
    ?><div class="text"><?php
        if ($message) {
            ?><span class="message"><?php 
                echo rtrim($message, '.') . '.';
            ?></span><?php            
        }
        echo ' ';
        ?><span class="status"><?php 
            echo $resultStatus;
        ?></span><?php
    ?></div><?php
    ?><a href="/" class="button"><?php 
        echo __(__FILE__, 'Zavrieť');
    ?></a><?php
?></div><?php


