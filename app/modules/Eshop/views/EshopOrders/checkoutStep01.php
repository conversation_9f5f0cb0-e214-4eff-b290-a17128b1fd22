<?php /* @var $this Template */
$this->displayOriginComment = true;
// prepare input templates
Html::startCapture();
?><div class="grid-row"><?php 
    ?><div class="grid-col grid-col-36-100"><?php 
        ?>:l:<?php
    ?></div><?php   
    ?><div class="grid-col grid-col-64-100 input-wrapper"><?php 
        ?>:i::e:<?php
    ?></div><?php
?></div><?php
$inputTemplateA = Html::endCapture();
Html::startCapture();
?><div class="grid-row"><?php 
    ?><div class="grid-col grid-col-18-100"><?php 
        ?>:l:<?php
    ?></div><?php   
    ?><div class="grid-col grid-col-82-100 input-wrapper"><?php 
        ?>:i::e:<?php
    ?></div><?php
?></div><?php
$inputTemplateB = Html::endCapture();
$inputTemplateC = '<div class="input-wrapper">:l::i::e:</div>';

Html::startCapture();
?><div class="grid-row"><?php
    ?><div class="grid-col grid-col-36-100"><?php
        ?>:l:<?php
        ?></div><?php
    ?><div class="grid-col grid-col-64-100 input-wrapper"><?php
        ?><div class="nice-checkbox"><?php
            ?>:i:<?php
            ?><span class="image"></span><?php
        ?></div><?php
    ?></div><?php
?></div><?php
$checkboxTemplate = Html::endCapture();

Html::startCapture();
?><div class="nice-radio"><?php
    ?>:i:<?php
    ?><span class="image"></span><?php
    ?>:l:<?php
?></div><?php
$radioTemplate = Html::endCapture();

App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    'data' => $this->params['data'],
    'errors' => $this->params['errors'],
    'required' => $this->params['required'],
    'inputDefaults' => array(
        'template' => $inputTemplateA,
    ),
    'labelDefaults' => array(
        'separator' => '',
    ),
    'testData' => array(
        'fullname' => 'Test Name',
        'street' => 'Test 01',
        'zip' => '012 34',
        'city' => 'Test',
        'country' => 'SK',
        'email' => '<EMAIL>',
        'phone' => '0987654321',
        'delivery_fullname' => 'Test Name',
        'delivery_street' => 'Test 01',
        'delivery_zip' => '012 34',
        'delivery_city' => 'Test',
        'delivery_country' => 'SK',
    )
));
?><div class="checkout checkout-address"><?php 
    echo $this->loadView('EshopOrders/checkoutHeader', array(
        'checkoutStep' => 'checkoutStep01',
        'productsCount' => $this->params['productCount'],
    ));
    $checkoutFormId = uniqid('checkout-form-');
    ?><form action="<?php echo $this->params['urlNext'] ?>" method="post" id="<?php echo $checkoutFormId ?>"><?php 
        ?><input type="hidden" name="data[_target][]" value="Eshop.EshopOrders.checkout" /><?php
        ?><div class="invoicing-address"><?php 
            ?><div class="title"><?php 
                echo __(__FILE__, 'Invoicing address');
            ?></div><?php
            ?><div class="subject-type-switch"><?php 
                $subjectTypeInputClass = uniqid('subject-type-');
                echo $Form->radio('subject_type', array(
                    'class' => $subjectTypeInputClass,
                    'options' => array(
                        'enum_person' => __(__FILE__, 'Private person'),
                        'enum_company' => __(__FILE__, 'Freelancer, company, organization, institution')
                    ),
                    'value' => 'enum_person',
//                    'template' => $inputTemplateC,
                    'template' => null,
                    'toggleTemplate' => $radioTemplate,
                ));     
            ?></div><?php
            $style = '';
            if(
                Sanitize::value($this->params['data']['subject_type']) !== 'enum_company'
            ) {
                $style = ' style="display: none;"';
            }
            ?><div class="company-data"<?php echo $style ?>><?php 
                ?><div class="grid-row"><?php 
                    ?><div class="grid-col grid-col-50-100"><?php 
                        echo $Form->text('company_fullname', array(
                            'label' => __(__FILE__, 'Company name'),
                            'title' => __(__FILE__, 'Company name'),
                        ));    
                        echo $Form->text('company_id_number', array(
                            'label' => __(__FILE__, 'Company ID number'),
                            'title' => __(__FILE__, 'Company ID number'),
                        ));    
                    ?></div><?php 
                    ?><div class="grid-col grid-col-50-100"><?php 
                        echo $Form->text('company_tax_number', array(
                            'label' => __(__FILE__, 'Company tax number'),
                            'title' => __(__FILE__, 'Company tax number'),
                        ));    
                        echo $Form->checkbox('company_vat_payer', array(
                            'label' => __(__FILE__, 'VAT payer'),
                            'title' => __(__FILE__, 'VAT payer'),
                            'class' => ($vatPayerInputClass = uniqid('company-vat-payer-')),
                            'template' => $checkboxTemplate,
                        ));    
                        $style = '';
                        if(!Sanitize::value($this->params['data']['company_vat_payer'])) {
                            $style = ' style="display: none;"';
                        }
                        ?><div class="company-vat-number"<?php echo $style ?>><?php 
                            echo $Form->text('company_vat_number', array(
                                'label' => __(__FILE__, 'Company VAT number'),
                                'title' => __(__FILE__, 'Company VAT number'),
                            ));    
                        ?></div><?php
                    ?></div><?php 
                ?></div><?php 
            ?></div><?php
            echo $Form->text('fullname', array(
                'label' => __(__FILE__, 'Firstname and lastname'),
                //'empty' => __(__FILE__, 'Firstname and lastname'),
                'title' => __(__FILE__, 'Firstname and lastname'),
                'template' => $inputTemplateB
            ));
            ?><div class="grid-row"><?php 
                ?><div class="grid-col grid-col-50-100"><?php 
                    echo $Form->text('street', array(
                        'label' => __(__FILE__, 'Street and house number/apartment'),
                        //'empty' => __(__FILE__, 'Street and house number/apartment'),
                        'title' => __(__FILE__, 'Street and house number/apartment'),
                    ));
                ?></div><?php 
                ?><div class="grid-col grid-col-50-100"><?php 
                    echo $Form->text('zip', array(
                        'label' => __(__FILE__, 'Zip'),
                        //'empty' => __(__FILE__, 'Zip'),
                        'title' => __(__FILE__, 'Zip'),
                    ));
                ?></div><?php 
            ?></div><?php 
            ?><div class="grid-row"><?php 
                ?><div class="grid-col grid-col-50-100"><?php 
                    echo $Form->text('city', array(
                        'label' => __(__FILE__, 'City'),
                        //'empty' => __(__FILE__, 'City'),
                        'title' => __(__FILE__, 'City'),
                    ));
                ?></div><?php 
                ?><div class="grid-col grid-col-50-100"><?php 
                    echo $Form->select('country', array(
                        'label' => __(__FILE__, 'Country'),
                        //'empty' => __(__FILE__, 'Country'),
                        'title' => __(__FILE__, 'Country'),
                        'options' => $this->params['countries'],
                        'value' => $this->getSetting('address.country'),
                    ));
                ?></div><?php 
            ?></div><?php 
            ?><div class="grid-row"><?php 
                ?><div class="grid-col grid-col-50-100"><?php 
                    echo $Form->text('email', array(
                        'label' => __(__FILE__, 'E-mail'),
                        //'empty' => __(__FILE__, 'E-mail'),
                        'title' => __(__FILE__, 'E-mail'),
                    ));
                ?></div><?php 
                ?><div class="grid-col grid-col-50-100"><?php 
                    echo $Form->text('phone', array(
                        'label' => __(__FILE__, 'Phone'),
                        //'empty' => __(__FILE__, 'Phone'),
                        'title' => __(__FILE__, 'Phone'),
                        'placeholder' => __(__FILE__, '+421...'),
                    ));
                ?></div><?php 
            ?></div><?php 
        ?></div><?php
        ?><div class="delivery-address"><?php 
            ?><div class="title"><?php 
                echo __(__FILE__, 'Delivery address');
            ?></div><?php
                ?><div class="delivery-address-switch"><?php 
                    echo $Form->radio('deliveryAddress', array(
                        'value' => $this->params['deliveryAddress'],
                        'options' => array(
                            'customerAddress' => __(__FILE__, 'Deliver to invoicing address'), //'Doručiť na fakturačnú adresu', 
                            'otherAddress' => __(__FILE__, 'Other delivery address'), //'Doručiť na inú adresu',
                            //'merchantAddress' => __(__FILE__, 'Local pickup'), //'Osobný odber',
                        ),
//                        'template' => $inputTemplateC,
                        'template' => null,
                        'toggleTemplate' => $radioTemplate,
                    ));     
                ?></div><?php
                ?><div id="otherAddress" class="other-delivery-address"><?php
                    echo $Form->text('delivery_fullname', array(
                        'label' => __(__FILE__, 'Firstname and lastname'),
                        //'empty' => __(__FILE__, 'Firstname and lastname'),
                        'title' => __(__FILE__, 'Firstname and lastname'),
                        'template' => $inputTemplateB,
                    ));
                    ?><div class="grid-row"><?php 
                        ?><div class="grid-col grid-col-50-100"><?php 
                            echo $Form->text('delivery_street', array(
                                'label' => __(__FILE__, 'Street and house number/apartment'),
                                //'empty' => __(__FILE__, 'Street and house number/apartment'),
                                'title' => __(__FILE__, 'Street and house number/apartment'),
                            ));
                        ?></div><?php 
                        ?><div class="grid-col grid-col-50-100"><?php 
                            echo $Form->text('delivery_zip', array(
                                'label' => __(__FILE__, 'Zip'),
                                //'empty' => __(__FILE__, 'Zip'),
                                'title' => __(__FILE__, 'Zip'),
                            ));
                        ?></div><?php 
                    ?></div><?php 
                    ?><div class="grid-row"><?php 
                        ?><div class="grid-col grid-col-50-100"><?php 
                            echo $Form->text('delivery_city', array(
                                'label' => __(__FILE__, 'City'),
                                //'empty' => __(__FILE__, 'City'),
                                'title' => __(__FILE__, 'City'),
                            ));
                        ?></div><?php 
                        ?><div class="grid-col grid-col-50-100"><?php 
                            echo $Form->select('delivery_country', array(
                                'label' => __(__FILE__, 'Country'),
                                //'empty' => __(__FILE__, 'Country'),
                                'title' => __(__FILE__, 'Country'),
                                'options' => $this->params['countries'],
                                'value' => $this->getSetting('address.country'),
                            ));
                        ?></div><?php 
                    ?></div><?php
                    ?><div class="grid-row"><?php 
                        ?><div class="grid-col grid-col-50-100"><?php 
                        ?></div><?php 
                        ?><div class="grid-col grid-col-50-100"><?php 
                            echo $Form->text('delivery_phone', array(
                                'label' => __(__FILE__, 'Phone'),
                                //'empty' => __(__FILE__, 'Phone'),
                                'title' => __(__FILE__, 'Phone'),
                                'placeholder' => __(__FILE__, '+421...'),
                            ));
                        ?></div><?php 
                    ?></div><?php                 
                ?></div><?php
                ?><div id="merchantAddress"  class="merchant-delivery-address"><?php
                    ?><div class="address"><?php 
                        echo $this->loadElement('address');
                    ?></div><?php
                    ?><div class="opening-hours"><?php 
                        echo $this->getSetting('EshopOrder.openingHours');
                    ?></div><?php
                    ?><div class="info"><?php 
                        echo __(__FILE__, 'No shipping costs are billed regardless of payment method');
                    ?></div><?php
                ?></div><?php
            ?></div><?php
    ?></form><?php
    echo $this->loadView('EshopOrders/checkoutFooter', array(
        'urlBack' => $this->params['urlPrevious'],
        'submitFormId' => $checkoutFormId,
    ));
?></div><?php
App::startjsCapture();
?><script type="text/javascript">
jQuery(function(){
    
jQuery('input.<?php echo $subjectTypeInputClass ?>').change(function() {
    if (jQuery(this).val() === 'enum_company') {
        jQuery('.company-data').slideDown('fast');
    }
    else {
        jQuery('.company-data').slideUp('fast');
    }
});
jQuery('input.<?php echo $vatPayerInputClass ?>').change(function() {
    if (jQuery(this).prop('checked')) {
        jQuery('.company-vat-number').slideDown({complete: function() {
            jQuery('.company-vat-number input[type="text"]').focus();
        }});
    }
    else {
        jQuery('.company-vat-number').slideUp();
    }
});
 
toggleDeliveryAddress();
jQuery('#<?php echo $checkoutFormId ?> input[name="data[deliveryAddress]"]').click(function(e){
    toggleDeliveryAddress(jQuery(this).val());
}); 

function toggleDeliveryAddress(value) {
    if (typeof value === 'undefined') {
        value = jQuery('#<?php echo $checkoutFormId ?> input[name="data[deliveryAddress]"]:checked').val();
    }
    if (value === 'merchantAddress') {
        jQuery('#otherAddress').slideUp('fast', function() {
            jQuery('#merchantAddress').slideDown('fast');
        });
    }
    else if (value === 'otherAddress') {
        jQuery('#merchantAddress').slideUp('fast', function() {
            jQuery('#otherAddress').slideDown('fast');
        });
    }
    else {
        jQuery('#merchantAddress').slideUp('fast');
        jQuery('#otherAddress').slideUp('fast');        
    }
}  
    
});
</script><?php
App::endJsCapture();
App::setCssFiles(array(
    '/app/css/vendors/font-awesome/css/font-awesome.min.css',
));
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
));

// RETURN
return;

//
// Code store for other projects
//

