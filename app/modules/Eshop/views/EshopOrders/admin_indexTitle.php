<?php /* @var $this Template */
echo ($this->params['viewType'] == 'index' ? __a(__FILE__, 'Orders preview') : __a(__FILE__, 'Orders composition'));

$filterData = $this->params['filterOptions']['filter'];
if (!empty($filterData)) {
    // - if Paginator::$filterFieldsSeparator is defined then get fields by parsing param value 
    if (!empty($this->params['filterFieldsSeparator'])) {        
        $filterFields = array();
        $filterItems = explode($this->params['filterFieldsSeparator'], $filterData);
        if (!empty($filterItems)) {
            foreach ($filterItems as $filterItem) {
                $filterItem = explode($this->params['filterExpressionSeparator'], $filterItem);
                $filterFields[$filterItem[0]] = $filterItem[1];
            }
        }
    }
    // - if Paginator::$filterFieldsSeparator is not defined then filterFields match with $filterData array
    else {
        $filterFields = $filterData;
    }
}
?><div class="titleMenu">
    <div class="left">
        <div class="dateFrom">
            od <select class="inputDay">
                <option value="0"> -- </option>
                <?php for ($x = 1; $x <= 31; $x++) : ?>
                <option value="<?php echo $x; ?>"<?php if (0) echo ' style="background:#F5F5F5;" selected=""'; ?>><?php echo $x; ?>.</option>
                <?php endfor; ?>
            </select>
            <select class="inputMonth">
                <option value="0"> -- </option>
                <option value="1">1.</option>
                <option value="2">2.</option>
                <option value="3">3.</option>
                <option value="4">4.</option>
                <option value="5">5.</option>
                <option value="6">6.</option>
                <option value="7">7.</option>
                <option value="8">8.</option>
                <option value="9">9.</option>
                <option value="10">10.</option>
                <option value="11">11.</option>
                <option value="12">12.</option>
            </select>
            <select class="inputYear">
                <option value="0"> -- </option>
                <?php for ($x = 2014; $x <= date('Y'); $x++) : ?>
                <option value="<?php echo $x; ?>"><?php echo $x; ?></option>
                <?php endfor; ?>
            </select> &nbsp; &nbsp; 
            <small> &nbsp; 
                <input name="chkgrp1[]" onclick="setDate(this, '<?php echo date("j;n;Y", mktime(0, 0, 0, date("n")-1, 1, date("Y"))); ?>');" type="checkbox">minulý mesiac
                 &nbsp; 
                <input name="chkgrp1[]" onclick="setDate(this, '<?php echo date("j;n;Y", strtotime("-1 days")); ?>');" type="checkbox">včera
                 &nbsp; 
                <input name="chkgrp1[]" onclick="setDate(this, '<?php echo date("j;n;Y"); ?>');" type="checkbox">dnes
                 &nbsp; 
                <input name="chkgrp1[]" onclick="reset(this);" type="checkbox">vynulovat
            </small>
        </div>
        <div class="dateTo">
            do <select class="inputDay">
                <option value="0"> -- </option>
                <?php for ($x = 1; $x <= 31; $x++) : ?>
                <option value="<?php echo $x; ?>"<?php if (0) echo ' style="background:#F5F5F5;" selected=""'; ?>><?php echo $x; ?>.</option>
                <?php endfor; ?>
            </select>
            <select class="inputMonth">
                <option value="0"> -- </option>
                <option value="1">1.</option>
                <option value="2">2.</option>
                <option value="3">3.</option>
                <option value="4">4.</option>
                <option value="5">5.</option>
                <option value="6">6.</option>
                <option value="7">7.</option>
                <option value="8">8.</option>
                <option value="9">9.</option>
                <option value="10">10.</option>
                <option value="11">11.</option>
                <option value="12">12.</option>
            </select>
            <select class="inputYear">
                <option value="0"> -- </option>
                <?php for ($x = 2014; $x <= date('Y'); $x++) : ?>
                <option value="<?php echo $x; ?>"><?php echo $x; ?></option>
                <?php endfor; ?>
            </select> &nbsp; &nbsp; 
            <small> &nbsp; 
                <input name="chkgrp2[]" onclick="setDate(this, '<?php echo date("j;n;Y", mktime(0, 0, 0, date("m"), 0, date("Y"))); ?>');" type="checkbox">minulý mesiac
                 &nbsp; 
                <input name="chkgrp2[]" onclick="setDate(this, '<?php echo date("j;n;Y", strtotime("-1 days")); ?>');" type="checkbox">včera
                 &nbsp; 
                <input name="chkgrp2[]" onclick="setDate(this, '<?php echo date("j;n;Y"); ?>');" type="checkbox">dnes
                 &nbsp; 
                <input name="chkgrp2[]" onclick="reset(this);" type="checkbox">vynulovat
            </small>
        </div>
    </div>
    <div class="submit">
        <input type="button" value="OK" onclick="sendFilterForm();">
    </div>
</div><?php 
App::startJsCapture();
?><script type="text/javascript">
function setDate(obj, dateEncoded) {
//    console.log(dateEncoded);
    var date = dateEncoded.split(';');
    var container = $(obj).parents('div')[0];
    $($(container).children('.inputDay')[0]).val(date[0]);
    $($(container).children('.inputMonth')[0]).val(date[1]);
    $($(container).children('.inputYear')[0]).val(date[2]);
}
function reset(obj) {
    var container = $(obj).parents('div')[0];
    $($(container).children('.inputDay')[0]).val(0);
    $($(container).children('.inputMonth')[0]).val(0);
    $($(container).children('.inputYear')[0]).val(0);
}
function setDateValue(string) {
    $('input[data-run-pg-filter-field="EshopOrder.created"]').val(string);
}
function sendFilterForm() {
    var fromDay = $('.dateFrom .inputDay').val();
    var fromMonth = $('.dateFrom .inputMonth').val();
    var fromYear = $('.dateFrom .inputYear').val();

    var toDay = $('.dateTo .inputDay').val();
    var toMonth = $('.dateTo .inputMonth').val();
    var toYear = $('.dateTo .inputYear').val();
    
    // check if date from is filled
    var fromDateIsFilled = true;
    if (fromDay === '0' || fromMonth === '0' || fromYear === '0') {
        fromDateIsFilled = false;
    }
    
    // check if date to is filled
    var toDateIsFilled = true;
    if (toDay === '0' || toMonth === '0' || toYear === '0') {
        toDateIsFilled = false;
    }
    
    // if filter is total empty, reset filter
    if (fromDay === '0' && fromMonth === '0' && fromYear === '0' && toDay === '0' && toMonth === '0' && toYear === '0') {
        $('input[data-run-pg-filter-field="EshopOrder.created"]').val('');

        var e = jQuery.Event("keydown");
        e.which = 13;
        $('input[data-run-pg-filter-field="EshopOrder.created"]').trigger(e);
        return;
    }
    
    // date is not filled, do nothing
    if (!fromDateIsFilled && !toDateIsFilled) return false;
        
    var dateString = '';
    if (fromDateIsFilled) {
        dateString += '>= ' + fromDay + '.' + fromMonth + '.' + fromYear;
    }
    if (fromDateIsFilled && toDateIsFilled) {
        dateString += ' & ';
    }
    if (toDateIsFilled) {
        dateString += '<= ' + toDay + '.' + toMonth + '.' + toYear;
    }
    $('input[data-run-pg-filter-field="EshopOrder.created"]').val(dateString);
    
    var e = jQuery.Event("keydown");
    e.which = 13;
    $('input[data-run-pg-filter-field="EshopOrder.created"]').trigger(e);
}
$(document).ready(function(){
    // get setted date from php filter
    var date = '<?php echo Sanitize::value($filterFields['EshopOrder.created'], ''); ?>';
    var dateFrom = '';
    var dateTo = '';
    
    var fromSign = date.indexOf('>');
    if (fromSign !== -1) {
        var andSign = date.indexOf('&');
        if (andSign !== -1) {
            dateFrom = date.substr(fromSign + 1, andSign - fromSign - 1);
        } else {
            dateFrom = date.substr(fromSign + 1);
        }
        dateFrom = dateFrom.replace('=', '');
        dateFrom = dateFrom.trim();
    }
    
    var toSign = date.indexOf('<');
    if (toSign !== -1) {
        dateTo = date.substr(toSign + 1);
        dateTo = dateTo.replace('=', '');
        dateTo = dateTo.trim();
    }
    
    if (dateFrom !== '') {
        var dateFromData = dateFrom.split('.');
        $('.dateFrom .inputDay').val(dateFromData[0]);
        $('.dateFrom .inputMonth').val(dateFromData[1]);
        $('.dateFrom .inputYear').val(dateFromData[2]);
    }
    if (dateTo !== '') {
        var dateToData = dateTo.split('.');
        $('.dateTo .inputDay').val(dateToData[0]);
        $('.dateTo .inputMonth').val(dateToData[1]);
        $('.dateTo .inputYear').val(dateToData[2]);
    }
    
    
    $("input:checkbox").click(function() {
        if ($(this).is(":checked")) {
            var group = "input:checkbox[name='" + $(this).attr("name") + "']";
            $(group).prop("checked", false);
            $(this).prop("checked", true);
        } else {
            $(this).prop("checked", false);
        }
    });
});
</script><?php 
App::endJsCapture();
App::startCssCapture();
?><style>
    .titleMenu {
        float: right;
        margin-top: -4px;
        margin-left: 20px;
        font-size: 12px;
        line-height: 19px;
    }
    .titleMenu ul {
        margin: 0;
        float: left;
    }
    .titleMenu ul li {
        
    }
    .titleMenu .left {
        float: left;
    }
    .titleMenu .submit {
        float: right;
        padding: 3px 0 0 15px;
    }
    .titleMenu .submit input {
        padding: 4px 7px;
    }
    .titleMenu input[type=checkbox] {
        position: relative;
        top: 3px;
        margin: 0 3px 0 0 !important;
    }
    .titleMenu input[type=button] {
        margin-top: 3px;
    }
</style><?php
App::endCssCapture();
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
));
