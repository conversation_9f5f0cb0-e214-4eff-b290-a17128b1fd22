<?php /* @var $this Template */
$this->displayOriginComment = true;
/**
 * Partial view (subview) used in views/EshopOrders/checkoutHeader
 * 
 * following items are avaited in $this->params:
 *  - 'checkoutStep' (string) Possible values are 'cart' and values of EshopOrder::$checkoutWizardSteps
 */
$defaults = array(
    'checkoutStep' => null,
);
$this->params = array_merge($defaults, $this->params);
$EshopOrder = $this->loadModel('EshopOrder', true);
$checkoutSteps = $EshopOrder->getPropertyCheckoutWizardSteps();
$steps = array_values($checkoutSteps);
foreach ($steps as $i => $step) {
    $steps[$step] = $i + 2;
}
$steps = array_merge($steps, array('cart' => 1));
$stepIndex = $steps[$this->params['checkoutStep']];
if ($stepIndex > 1) {
    $cartUrl = App::getContentUrlByPid('Eshop.EshopCarts.view');
}
if ($stepIndex > 2) {
    $checkoutSlugs = array_keys($checkoutSteps);
    $checkoutSlug = App::getContentUrlByPid('Eshop.EshopOrders.checkout');
}
?><div class="progress-bar"><?php 
    // step 1 (cart)
    $label = __(__FILE__, 'Shopping cart');
    if ($stepIndex > 1) {   
        $previousClass = ($stepIndex === 2) ? ' previous' : '';
        ?><a class="checkout-step checkout-step-01<?php echo $previousClass ?>" href="<?php echo $cartUrl ?>" title="<?php echo $label ?>"><?php 
            ?><span class="label"><?php 
                echo '1. ' . $label;
            ?></span><?php
        ?></a><?php
    }
    else {
        $activeClass = ($stepIndex === 1) ? ' active' : '';
        ?><span class="checkout-step checkout-step-01<?php echo $activeClass ?>" title="<?php echo $label ?>"><?php 
            ?><span class="label"><?php 
                echo '1. ' . $label;
            ?></span><?php
        ?></span><?php
    }
    // step 2
    $label = __(__FILE__, 'Billing details');
    if ($stepIndex > 2) {   
        $previousClass = ($stepIndex === 3) ? ' previous' : '';
        ?><a class="checkout-step checkout-step-02<?php echo $previousClass ?>" href="<?php echo App::getUrl(array('locator' => $checkoutSlug, 'args' => array($checkoutSlugs[0]))) ?>" title="<?php echo $label ?>"><?php 
            ?><span class="label"><?php 
                echo '2. ' . $label;
            ?></span><?php
        ?></a><?php
    }
    else {
        $activeClass = ($stepIndex === 2) ? ' active' : '';
        ?><span class="checkout-step checkout-step-02<?php echo $activeClass ?>" title="<?php echo $label ?>"><?php 
            ?><span class="label"><?php 
                echo '2. ' . $label;
            ?></span><?php
        ?></span><?php
    }
    // step 3
    $label = __(__FILE__, 'Method of delivery and payment');
    if ($stepIndex > 3) {   
        $previousClass = ($stepIndex === 4) ? ' previous' : '';
        ?><a class="checkout-step checkout-step-03<?php echo $previousClass ?>" href="<?php echo App::getUrl(array('locator' => $checkoutSlug, 'args' => array($checkoutSlugs[1]))) ?>" title="<?php echo $label ?>"><?php 
            ?><span class="label"><?php 
                echo '3. ' . $label;
            ?></span><?php
        ?></a><?php
    }
    else {
        $activeClass = ($stepIndex === 3) ? ' active' : '';
        ?><span class="checkout-step checkout-step-03<?php echo $activeClass ?>" title="<?php echo $label ?>"><?php 
            ?><span class="label"><?php 
                echo '3. ' . $label;
            ?></span><?php
        ?></span><?php
    }
    // step 4
    $label = __(__FILE__, 'Order confirmation');
    if ($stepIndex > 4) {   
        ?><a class="checkout-step checkout-step-04" href="<?php echo App::getUrl(array('locator' => $checkoutSlug, 'args' => array($checkoutSlugs[2]))) ?>" title="<?php echo $label ?>"><?php 
            ?><span class="label"><?php 
                echo '4. ' . $label;
            ?></span><?php
        ?></a><?php
    }
    else {
        $activeClass = ($stepIndex === 4) ? ' active' : '';
        ?><span class="checkout-step checkout-step-04<?php echo $activeClass ?>" title="<?php echo $label ?>"><?php 
            ?><span class="label"><?php 
                echo '4. ' . $label;
            ?></span><?php
        ?></span><?php
    }
?></div><?php
