<?php /* @var $this Template */
$this->displayOriginComment = true;
App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    'data' => $this->params['data'],
    'errors' => $this->params['errors'],
    'required' => $this->params['required'],
    'labelDefaults' => array(
        'separator' => '',
    ),
    'inputDefaults' => array(
        //'attachErrors' => false,
    )
));

Html::startCapture();
?><div class="nice-checkbox"><?php
    ?>:i:<?php
    ?><span class="image"></span><?php
    ?>:l:<?php
?></div><?php
$checkboxTemplate = Html::endCapture();

?><div class="checkout checkout-confirmation"><?php 
    echo $this->loadView('EshopOrders/checkoutHeader', array(
        'checkoutStep' => 'checkoutStep03',
        'productsCount' => $this->params['productCount'],
    ));
    $checkoutFormId = uniqid('checkout-form-');
    ?><form action="<?php echo $this->params['urlNext'] ?>" method="post" id="<?php echo $checkoutFormId ?>"><?php 
        ?><input type="hidden" name="data[_target][]" value="Eshop.EshopOrders.checkout" /><?php
        ?><div class="header"><?php 
            ?><div class="title"><?php 
                echo __(__FILE__, 'Order confirmation');
            ?></div><?php
            echo __(
                __FILE__, 
                '<b>Skontrolujte, prosím, ešte raz všetky údaje týkajúce sa Vašej objednávky. </b>Ak nájdete akúkoľvek chybu, stlačte tlačidlo "%s" a opravte ju. Ak je všetko v poriadku, kliknite na "%s". Po správnom uzavretí objednávky príde do Vašej e-mailovej schránky správa s potvrdením. Odoslaním objednávky súhlasíte s našimi <a href="%s">Všeobecnými obchodnými podmienkami</a>.',
                __(__FILE__, 'Change'),
                __(__FILE__, 'Submit order'),
                $this->params['urlTermsAndConditions']
            )
        ?></div><?php
        // products
        echo $this->loadView('EshopCarts/viewProducts', array(
            'products' => $this->params['EshopCartProduct'],
            'editable' => false,
        ));
        ?><a href="<?php echo $this->params['urlCartView'] ?>" class="button change-button secondary"><?php 
            echo __(__FILE__, 'Change');
        ?></a><?php
        ?><div class="checkout-data"><?php 
            // invoicing address 
            ?><div class="invoicing-address"><?php 
                ?><div class="title"><?php 
                    echo __(__FILE__, 'Invoicing address');
                ?></div><?php
                if ($this->params['Checkout']['subject_type'] === 'enum_company') {
                    echo $this->params['Checkout']['company_fullname'];
                    ?><br><?php
                }
                else {                    
                    echo $this->params['Checkout']['fullname'];
                    ?><br><?php
                }
                echo $this->params['Checkout']['street'];
                ?><br><?php
                echo $this->params['Checkout']['zip'];
                ?>&nbsp;<?php
                echo $this->params['Checkout']['city'];
                ?><br><?php
                echo $this->params['countries'][$this->params['Checkout']['country']];
                ?><br><?php
                echo __(__FILE__, 'Phone: %s', $this->params['Checkout']['phone']);
                ?><br><?php
                echo __(__FILE__, 'E-mail %s', $this->params['Checkout']['email']);
                ?><br><?php
                if ($this->params['Checkout']['subject_type'] === 'enum_company') {
                    echo __(__FILE__, 'ID:') . '&nbsp;' . $this->params['Checkout']['company_id_number'];
                    ?><br><?php
                    echo __(__FILE__, 'TIN:') . '&nbsp;' . $this->params['Checkout']['company_tax_number'];
                    ?><br><?php
                    if (!empty($this->params['Checkout']['company_vat_number'])) {
                        echo __(__FILE__, 'VAT ID:') . '&nbsp;' . 
                            $this->params['Checkout']['company_vat_number'] .
                            ' (' . __(__FILE__, 'VAT payer') . ')';
                        ?><br><?php
                    }
                }
                ?><a href="<?php echo $this->params['urlStep01'] ?>" class="button change-button secondary"><?php 
                    echo __(__FILE__, 'Change');
                ?></a><?php
            ?></div><?php
            // delivery address
            ?><div class="delivery-address"><?php 
                ?><div class="title"><?php 
                    echo __(__FILE__, 'Delivery address');
                ?></div><?php
                if (!empty($this->params['Checkout']['delivery_company_fullname'])) {                    
                    echo $this->params['Checkout']['delivery_company_fullname'];
                    ?><br><?php
                }
                if (!empty($this->params['Checkout']['delivery_fullname'])) {                    
                    echo $this->params['Checkout']['delivery_fullname'];
                    ?><br><?php
                }
                echo $this->params['Checkout']['delivery_street'];
                ?><br><?php
                if (!empty($this->params['Checkout']['delivery_zip'])) {                        
                    echo $this->params['Checkout']['delivery_zip'];
                    ?>&nbsp;<?php
                }
                echo $this->params['Checkout']['delivery_city'];
                ?><br><?php
                echo $this->params['countries'][$this->params['Checkout']['delivery_country']];
                ?><br><?php
                echo __(__FILE__, 'Phone: %s', $this->params['Checkout']['delivery_phone']);
                ?><br><?php
                ?><a href="<?php echo $this->params['urlStep01'] ?>" class="button change-button secondary"><?php 
                    echo __(__FILE__, 'Change');
                ?></a><?php
            ?></div><?php
            // shipment and payment
            ?><div class="shipment-and-payment"><?php 
                ?><div class="title"><?php 
                    echo __(__FILE__, 'Shipping and payment method');
                ?></div><?php
                echo __(__FILE__, 'Shipping: %s', $this->params['EshopShipmentMethod']['name']);
                ?><br><?php
                echo __(__FILE__, 'Payment: %s', $this->params['PaymentMethod']['name']);
                ?><br><?php
                ?><a href="<?php echo $this->params['urlStep02'] ?>" class="button change-button secondary"><?php 
                    echo __(__FILE__, 'Change');
                ?></a><?php
            ?></div><?php
        ?></div><?php
        ?><div class="checkout-summary"><?php 
            ?><div class="checkout-total"><?php 
                ?><div class="subtotals"><?php 
                    if ((float)$this->params['EshopCart']['bonus_discount']) {
                        ?><div class="bonus-subtotal"><?php 
                            ?><span class="label"><?php 
                                echo __(__FILE__, 'Bonus') . ': ';
                            ?></span><?php
                            ?><span class="price"><?php
                                echo '-' . Eshop::formatPrice($this->params['EshopCart']['bonus_discount']);      
                            ?></span><?php
                        ?></div><?php
                    }
                    if (!empty($this->params['EshopCart']['vouchers_discount'])) {
                        ?><div class="bonus"><?php 
                            ?><span class="label"><?php 
                                echo __(__FILE__, 'Zľava spolu') . ': ';
                            ?></span><?php
                            ?><span class="price"><?php 
                                echo '-' . Eshop::formatPrice($this->params['EshopCart']['vouchers_discount']);
                            ?></span><?php
                        ?></div><?php
                    }
                    ?><div class="products-subtotal"><?php 
                        ?><span class="label"><?php 
                            echo __(__FILE__, 'Total price for products') . ': ';
                        ?></span><?php
                        ?><span class="price"><?php
                            echo Eshop::formatPrice($this->params['EshopCart']['products_price_to_pay']);      
                        ?></span><?php
                    ?></div><?php
                    ?><div class="shipment-and-payment-subtotal"><?php 
                        ?><span class="label"><?php 
                            echo __(__FILE__, 'Postage') . ': ';
                        ?></span><?php
                        ?><span class="price"><?php
                            if (
                                isset($this->params['EshopShipmentMethod']['shipment_price_actual_taxed'])
                                && isset($this->params['PaymentMethod']['payment_price_actual_taxed'])
                            ) {
                                echo Eshop::formatPrice($this->params['EshopShipmentMethod']['shipment_price_actual_taxed'] + $this->params['PaymentMethod']['payment_price_actual_taxed']);      
                            }
                            else {
                                ?><span class="price-info"><?php
                                    if ($this->params['abroadDelivery']) { 
                                        echo __(__FILE__, 'Delivery to abroad') . '. ';
                                    }    
                                    echo __(__FILE__, 'You will be informed about final shipping price. Order isn\'t binding till your agreement with the final price');
                                ?></span><?php
                            }
                        ?></span><?php
                    ?></div><?php
                ?></div><?php
                ?><div class="totals"><?php 
                    $class = '';
                    if (
                        // @todo - resolve if the order is with VAT or without VAT
                        $this->params['EshopOrder']['order_price_to_pay'] === 
                            $this->params['EshopOrder']['order_price_actual_taxed']
                    ) {
                        $class = ' total-with-vat-highlighted';
                    }
                    ?><div class="total-with-vat<?php echo $class ?>"><?php 
                        ?><span class="label"><?php 
                            echo __(__FILE__, 'Total order price with VAT') . ': ';
                        ?></span><?php
                        ?><span class="price"><?php
                            echo Eshop::formatPrice($this->params['EshopOrder']['order_price_actual_taxed']);      
                        ?></span><?php
                    ?></div><?php
                    ?><div class="total-without-vat"><?php 
                        ?><span class="label"><?php 
                            echo __(__FILE__, 'Total order price without VAT') . ': ';
                        ?></span><?php
                        ?><span class="price"><?php
                            echo Eshop::formatPrice($this->params['EshopOrder']['order_price_actual_taxless']);      
                        ?></span><?php
                    ?></div><?php
                    if (
                        // @todo - resolve if the order is with VAT or without VAT
                        $this->params['EshopOrder']['order_price_to_pay'] !== 
                            $this->params['EshopOrder']['order_price_actual_taxed']
                    ) {
                        ?><div class="total-to-pay"><?php 
                            ?><span class="label"><?php 
                                echo __(__FILE__, 'Platba za objednávku') . ': ';
                            ?></span><?php
                            ?><span class="price"><?php
                                echo Eshop::formatPrice($this->params['EshopOrder']['order_price_to_pay']);      
                            ?></span><?php
                        ?></div><?php
                    }
                ?></div><?php
            ?></div><?php
            ?><div class="checkout-info"><?php 
                ?><i class="fa fa-info-circle"></i><?php
                echo __($this, 'Ak nie je uvedené inak, tak ceny sú <b>s DPH</b>');
                ?><br><?php 
                if ($this->params['hasOversizedProducts']) {
                    ?><i class="fa fa-info-circle"></i><?php
                    ?><span class="oversized-warning"><?php 
                        echo $this->getSetting('EshopOrder.oversizedProductMessage') . ' ';
                    ?></span><?php
                    ?><br><?php 
                }
                /*/>
                elseif (($freeShipmentProductsTotal = $this->getSetting('EshopShipment.freeShipmentProductsTotal'))) {
                    echo trim(
                        __(__FILE__, 'Free shipping on purchase over %s', Eshop::formatPrice($freeShipmentProductsTotal)), 
                        '. '
                    ) . '. ' ;
                }
                /*/
                $this->loadModel('EshopCart');
                $Cart = new EshopCart();
                $freeShipmentAchieved = false;
                if (
                    !($this->params['hasOversizedProducts'])
                    && ($amountToFreeShipmentMessage = $Cart->getAmountToFreeShipmentMessage(array(
                        'freeShipmentAchieved' => &$freeShipmentAchieved,
                    )))
                ) {
                    ?><i class="fa fa-info-circle"></i><?php
                    $class = $freeShipmentAchieved ? ' achieved' : '';
                    ?><span class="free-shipment<?php echo $class ?>"><?php 
                        echo $amountToFreeShipmentMessage;
                    ?></span><?php
                }
                //*/
            ?></div><?php
        ?></div><?php
        ?><div class="checkout-confirmation-inputs"><?php 
            ?><div class="grid-row"><?php 
                ?><div class="grid-col grid-col-50-100"><?php 
                    echo $Form->textarea('comment', array(
                        'placeholder' => __(__FILE__, 'Order note')
                    ));
                ?></div><?php 
                ?><div class="grid-col grid-col-50-100"><?php 
                    echo $Form->checkbox('terms_and_conditions_agreement', array(
                        'label' => __(
                            __FILE__, 
                            'Oboznámil som sa s <a href="%s" target="_blank">obchodnými podmienkami</a> a súhlasím s nimi.',
                            App::getContentUrlByPid('Eshop.termsAndConditions')
                        ),
//                        'template' => ':i::l:',
                        'template' => $checkboxTemplate,
                    )); 
                    if (empty($this->params['UserProfile']['newsletters_agreement'])) {
                        $label = __(__FILE__, 'Prihlasujem sa k odberu informačných e-mailov.');
                        $label .= '<br>';
                        $label .= __(__FILE__, 'Informačné e-maily sú určené pre osoby staršie ako 16 rokov.');
                        $label .= '<br>';
                        $label .= '<span class="gdpr-note">';
                        $label .= __(
                            __FILE__, 
                            'Prihlásením súhlasím so <a href="%s" target="_blank">spracovaním osobných údajov</a>.',
                            App::getContentUrlByPid('App.privacyPolicyInfo')
                        );
                        $label .= '</span>';
                        echo $Form->checkbox('UserProfile.newsletters_agreement', array(
                            'label' => $label,
//                            'template' => ':i::l:',
                            'template' => $checkboxTemplate,
                        ));
                        ?><br><?php
                    }
                ?></div><?php 
            ?></div><?php
        ?></div><?php
    ?></form><?php
    echo $this->loadView('EshopOrders/checkoutFooter', array(
        'urlBack' => $this->params['urlPrevious'],
        'submitFormId' => $checkoutFormId,
        'textContinue' => __(__FILE__, 'Submit order'),
    ));
?></div><?php
App::startjsCapture();
?><script type="text/javascript">
jQuery(function(){
        
});
</script><?php
App::endJsCapture();
App::setCssFiles(array(
    '/app/css/vendors/font-awesome/css/font-awesome.min.css',
));
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
));

// RETURN
return;

//
// Code store for other projects
//

                    if ($this->params['isQuickOrder']) {
                        echo $Form->checkbox('adulthood_declaration', array(
                            'label' => __(__FILE__, 'I am 18 years'),
                            'template' => ':i::l:',
                        )); 
                        ?><br><?php
                    }

