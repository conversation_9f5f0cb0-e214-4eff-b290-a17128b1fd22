<?php /* @var $this Template */
$this->displayOriginComment = true;
App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    'data' => $this->data,
));

Html::startCapture();
?><div class="nice-checkbox"><?php
?>:i:<?php
?><span class="image"></span><?php
?>:l:<?php
?></div><?php
$checkboxTemplate = Html::endCapture();

?><div id="login"><?php 
    ?><div class="login-header"><?php 
        ?><h1 class="title"><?php 
            if (($siteName = App::getSetting('App', 'name'))) {
                if (strpos($siteName, '.') !== false) {
                    $siteName = explode('.', $siteName);
                    $siteName = reset($siteName);
                }
                echo __(__FILE__, 'Login to %s', $siteName);
            }
            else {
                echo __(__FILE__, 'Login');
            }
        ?></h1><?php
        echo __(__FILE__, 'After login you don\'t need to enter all billing details in order checkout')
    ?></div><?php
    ?><div class="grid-row"><?php 
        ?><div class="grid-col grid-col-50-100"><?php 
            // login
            ?><form method="post" class="login-form"><?php
                ?><input type="hidden" name="data[_target][]" value="Eshop.EshopOrders.login" /><?php
                ?><input type="hidden" name="data[_login]" value="1" /><?php
                ?><h2 class="title"><?php 
                    echo __(__FILE__, 'Login form');
                ?></h2><?php
                echo $Form->text('username', array(
//                    'label' => __(__FILE__, 'Your e-mail'),
                    'placeholder' => __(__FILE__, 'Your e-mail'),
                ));
                echo $Form->password('password', array(
//                    'label' => __(__FILE__, 'Your password'),
                    'placeholder' => __(__FILE__, 'Your password'),
                ));
                if (App::hasPermanentSessionAvailable()) {
                    echo $Form->checkbox('_permanentLogin', array(
                        'label' => array(
                            'text' => __(__FILE__, 'Permanent login'),
                            'separator' => '',
                        ),
//                        'template' => '<span class="permanent-login">:i::l:</span>',
                        'template' => $checkboxTemplate,
                    ));
                }
                ?><a href="<?php echo App::getContentUrlByPid('App.Users.forgotPassword') ?>" class="forgotten-password"><?php 
                    echo __(__FILE__, 'I forgot password') 
                ?></a><?php
                ?><div class="footer"><?php 
                    ?><button type="submit"><?php 
                        echo __(__FILE__, 'Log in');
                    ?></button><?php
                ?></div><?php
            ?></form><?php
        ?></div><?php
        ?><div class="grid-col grid-col-50-100"><?php 
            // register
            ?><div class="registration-link"><?php 
                ?><h2 class="title"><?php 
                    echo __(__FILE__, 'Create new account');
                ?></h2><?php
                echo __(__FILE__, '<b>In a case you don\'t have an account with us yet,</b><br>you can create it easily in a minute');
                ?><br><?php
                ?><a href="<?php echo App::getContentUrlByPid('App.Users.register') ?>" class="button"><?php 
                    echo __(__FILE__, 'Create account') 
                ?></a><?php
            ?></div><?php
            // start quick order
            if (!empty($this->params['quickOrderAllowed'])) {
                ?><form method="post" action="<?php echo $this->params['checkoutUrl'] ?>" class="start-quick-order"><?php
                    ?><h2 class="title"><?php 
                        echo __(__FILE__, 'Continue without login');
                    ?></h2><?php
                    echo __(__FILE__, 'Quick order without registration and login')
                    ?><br><?php
                    ?><input type="hidden" name="data[_target][]" value="Eshop.EshopOrders.checkout" /><?php
                    ?><input type="hidden" name="data[_start_quick_order]" value="1" /><?php
                    ?><button type="submit"><?php 
                        echo __(__FILE__, 'Continue without login');
                    ?></button><?php
                ?></form><?php
            }
        ?></div><?php
    ?></div><?php
?></div><?php

// RETURN
return;

//
// Code store for other projects
//

