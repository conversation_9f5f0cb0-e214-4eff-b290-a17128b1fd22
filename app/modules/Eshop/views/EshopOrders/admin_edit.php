<?php /* @var $this Template */
$options = $this->params;
$OrderForm = new FormHelper(array(
    'data' => $this->params['data'],
    'labelDefaults' => array(
        'separator' => ''
    ),
    'compatibility' => 'bootstrap',
));
$locatorProductView = App::getContentLocatorByPid('Eshop.EshopProducts.view');
$orderEditable = $this->params['orderEditable'];
$readonlyOptions = array(
    'disabled' => 1, 
    'forceSubmit' => true,
);
?><form role="form" class="-run-smart-form -run-protheme<?php echo $options['class'] ?>"<?php echo $options['action'] . $options['method'] . $options['enctype'] ?>><?php 
    echo $OrderForm->hidden('_changes', array(
        'id' => '_changes'
    ));
    echo $OrderForm->hidden('_shipment_id', array(
        'id' => '_shipment_id',
        'value' => $this->params['data']['run_eshop_shipment_methods_id'],
    ));
    echo $OrderForm->hidden('_payment_id', array(
        'id' => '_payment_id',
        'value' => $this->params['data']['run_payment_methods_id'],
    ));
    ?><div class="-run-sfo-header"><?php 
        $inserts = array(
            ':title:' => $options['title'],
            ':buttonLabel:' => $options['buttonLabel'],
            ':closeButtonLabel:' => $options['closeButtonLabel'],
            ':saveAndCloseButtonLabel:' => $options['saveAndCloseButtonLabel'],
            ':deleteButton:' => $options['deleteButtonTemplate'],
            ':deleteButtonLabel:' => $options['deleteButtonLabel'],
            ':messages:' => App::messages(array(
                'class' => 'app-message',
                'view' => 'App/bootstrapMessages',
                'timeout' => false,
            )),
        );
        $inserts[':actions:'] = '';
        foreach ($options['actions'] as $actionName => $action) {
            $inserts[':actions:'] .= Html::action(
                $action,
                array(
                    'attributes' => array('role' => 'button'),
                    'tag' => 'a',
                )
            );
        }
        if ($inserts[':actions:']) {
            $inserts[':actions:'] = '<div class="-run-sfo-actions">' . $inserts[':actions:'] . '</div>';
        }
        echo str_replace(array_keys($inserts), array_values($inserts), $options['headerTemplate']);
    ?></div>
    <div class="-run-sfo-fields container-fluid"><?php 
        echo $options['fields']; ?>
        <h1 class="-run-sfo-heading" id="shipmentAndPayment"><?php echo __a(__FILE__, 'Shipment and payment'); ?></h1>
        <div class="row"><?php
            if (
                !empty($this->params['data']['specific'])
                && $this->params['data']['specific'] & EshopOrder::SPECIFIC_SHIPMENT
            ) {
                ?><div class="col-sm-6"><?php
                    $OrderForm->setPropertyCompatibility('bootstrap');
                    ?><div class="row"><?php
                        ?><div class="col-sm-6"><?php
                            echo $OrderForm->text('shipment_method_name', array(
                                'label' => __a(__FILE__, 'Shipment method'),
                                'class' => 'dirtable',
                            ));
                        ?></div><?php
                        ?><div class="col-sm-6"><?php
                            echo $OrderForm->text('specific_shipment_price', array(
                                'label' => __a(__FILE__, 'Shipment price'),
                                'class' => 'dirtable',
                            ));
                        ?></div><?php
                    ?></div><?php
                    $OrderForm->setPropertyCompatibility(null);
                ?></div><?php
            }
            elseif (!empty($this->params['shipments'])) {
                ?><div class="col-sm-6"><?php 
                    $OrderForm->setPropertyCompatibility(null);
                    foreach ($this->params['shipments'] as $shipment): ?>
                        <?php echo $OrderForm->radio('run_eshop_shipment_methods_id', array(
                            'toggleValue' => $shipment['id'],
                            'class' => 'noborder dirtable',
                            'label' => $shipment['name'] . ' (' . Eshop::formatPrice($shipment['shipment_price_actual_taxed'], array('symbol' => '')) . ')',
                            'template' => ':i::l:',
                            'data-value' => $shipment['shipment_price_actual_taxed'],
                            'data-base-label' => $shipment['name'],
                        ) + (!$orderEditable ? $readonlyOptions : array())) ?>
                        <?php if (/*NOTUSED*/false && $shipment['pid'] == 'ulozenka') : ?>
                            <?php $ulozenkaValue = mb_substr($this->params['data']['shipment_method_name'], mb_strpos($this->params['data']['shipment_method_name'], '(') + 1, -1); ?>
                            <?php echo $OrderForm->select('ulozenka', array(
                                'class' => 'ulozenka dirtable',
                                'template' => ':i:',
                                'options' => $this->params['ulozenka'],
                                'value' => $ulozenkaValue
                            )) ?>
                            <div class="ulozenka-link">
                                <a target="_blank" href="http://www.ulozenka.cz/vydejni-a-podaci-mista">více informací</a>
                            </div>
                        <?php endif ?>
                        <br><?php 
                    endforeach; 
                    $OrderForm->setPropertyCompatibility('bootstrap');
                ?></div><?php
            }
            ?><div class="col-sm-6"><?php 
                $OrderForm->setPropertyCompatibility(null);
                foreach ($this->params['payments'] as $payment): ?>
                    <?php echo $OrderForm->radio('run_payment_methods_id', array(
                        'toggleValue' => $payment['id'],
                        'class' => 'noborder dirtable',
                        'label' => $payment['name'] . ' (' . Eshop::formatPrice($payment['payment_price_actual_taxed'], array('symbol' => '')) . ')',
                        'template' => '<div class="paymentWrapper">:i::l:</div>',
                        'data-value' => $payment['payment_price_actual_taxed'],
                        'data-pid' => $payment['pid'],
                        'data-base-label' => $payment['name'],
                    ) + (!$orderEditable ? $readonlyOptions : array())) ?>
                <?php endforeach; 
                $OrderForm->setPropertyCompatibility('bootstrap');
                if (
                    !empty($this->params['isPayable'])
                    ||
                    !empty($this->params['hasCashPaymentMethod'])
                    && !empty($this->params['isPayableByCard'])
                ) {
                    echo '<br>' . __a(__FILE__, 'Payment link') . ': ';
                    ?><a href="<?php echo $this->params['paymentUrl'] ?>" target="_blank"><?php echo $this->params['paymentUrl'] ?></a><br><br><?php
                }
                ?>
            </div>
        </div>
        <h1 class="-run-sfo-heading"><?php echo __a(__FILE__, 'Products'); ?></h1>
        <table class="order-products table table-stripped"><?php 
            ?><tr><?php 
                ?><td><?php
                    echo __a(__FILE__, 'Number');
                ?></td><?php
                ?><td><?php
                    echo __a(__FILE__, 'ID');
                ?></td><?php
                ?><td><?php
                    echo __a(__FILE__, 'MRP kód');
                ?></td><?php
                ?><td><?php
                    echo __a(__FILE__, 'Name');
                ?></td><?php
                ?><td><?php
                    echo __a(__FILE__, 'Attributes');
                ?></td><?php
                ?><td><?php
                    echo __a(__FILE__, 'Price for unit');
                ?></td><?php
                ?><td><?php
                    echo __a(__FILE__, 'Amount');
                ?></td><?php
                ?><td><?php
                    echo __a(__FILE__, 'Price');
                ?></td><?php
                ?><td><?php
                    // actions
                ?></td><?php
            ?></tr><?php
            $ProductsForm = new FormHelper(array(
                'data' => array(
                    'products' => $this->params['products'],
                ),
                'compatibility' => 'bootstrap',
            ));
            for ($i = 0; $i <= count($this->params['products']); $i++) {
                $template = ($i === count($this->params['products']));
                if ($template) {
                    $rowIndex = ':rowIndex:';
                    Html::startCapture();
                }
                else {
                    $rowIndex = $i;
                    $product = $this->params['products'][$i];
                }
                ?><tr id="product-<?php echo $rowIndex ?>" class="order-product"><?php 
                    ?><td><?php
                        echo $ProductsForm->text(
                            'products.' . $rowIndex  . '.order_item_number', 
                            array(
                            ) + $readonlyOptions
                        );
                    ?></td><?php
                    ?><td><?php
                        echo $ProductsForm->text(
                            'products.' . $rowIndex  . '.run_eshop_products_id', 
                            array(
                                'class' => 'productIdInput',
                            ) 
                            + (!$template ? $readonlyOptions : array())
                        );
                        echo $ProductsForm->hidden('products.' . $rowIndex  . '.id');
                    ?></td><?php
                    ?><td><?php
                        echo $ProductsForm->text(
                            'products.' . $rowIndex  . '.mrp_code', 
                            array(
                            ) + $readonlyOptions
                        );
                    ?></td><?php
                    ?><td><?php
                        echo $ProductsForm->text(
                            'products.' . $rowIndex  . '.name', 
                            array(
                                'class' => 'productNameInput',
                            ) 
                            + (!$template ? $readonlyOptions : array())
                        );
                    ?></td><?php
                    ?><td><?php
                        echo $ProductsForm->text(
                            'products.' . $rowIndex  . '.static_attributes', 
                            array(
                                'class' => 'productAttributesInput',
                            ) 
                            + (!$orderEditable ? $readonlyOptions : array())
                        );
                    ?></td><?php
                    ?><td><?php
                        echo $ProductsForm->text(
                            'products.' . $rowIndex  . '.price_actual_taxed',
                            array(
                                'renderValue' => function($value) {
                                    return Eshop::formatPrice($value, array(
                                        'suffix' => null,
                                        'symbol' => null,
                                        'nbsp' => false,
                                    ));
                                }
                            ) + $readonlyOptions
                        );
                    ?></td><?php
                    ?><td><?php
                        echo $ProductsForm->text('products.' . $rowIndex  . '.amount', 
                            array(
                                'class' => 'productAmountInput dirtable',
                            ) 
                            + (!$orderEditable ? $readonlyOptions : array())
                            + ($template ? array('value' => 1) : array())
                        );
                    ?></td><?php
                    ?><td><?php
                        echo $ProductsForm->text(
                            'products.' . $rowIndex  . '.total_price_actual_taxed',
                            array(
                                'class' => 'productPrice',
                                'renderValue' => function($value) {
                                    return Eshop::formatPrice($value, array(
                                        'suffix' => null,
                                        'symbol' => null,
                                        'nbsp' => false,
                                    ));
                                }
                            ) + $readonlyOptions
                        );
                    ?></td><?php
                    ?><td style="white-space: nowrap;"><?php
                        if (!empty($product['EshopVoucher'])) {
                            foreach ($product['EshopVoucher'] as $voucher) {
                                $voucherUrl = App::getUrl(array(
                                    'module' => $this->module,
                                    'controller' => 'EshopVouchers',
                                    'action' => 'admin_edit',
                                    'args' => $voucher['id']
                                ));
                                $style = 'vertical-align: middle;';
                                $title = __a(__FILE__, '%s - pozrieť zľavový kód', $voucher['code']);
                                if ($voucher['printed']) {
                                    $style .= 'text-decoration: line-through;';
                                    $title .= ' (' . __a(__FILE__, 'vytlačený') . ')';
                                }
                                ?><a <?php 
                                    ?>class="-run-action" <?php 
                                    ?>data-run-st-creator <?php
                                    ?>style="<?php echo $style ?>" <?php 
                                    ?>title="<?php echo $title ?>" <?php 
                                    ?>href="<?php echo $voucherUrl  ?>" <?php 
                                    ?>target="_blank" <?php 
                                    ?>role="button"<?php 
                                ?>><?php
                                    echo __a(__FILE__, 'zk');
                                ?></a><?php 
                            }
                        }
                        if (!empty($product['slug'])) {
                            $viewUrl = App::getUrl(array(
                                'locator' => $locatorProductView, 
                                'args' => $product['slug'])
                            );
                            ?><a class="-run-action -run-action-view" <?php 
                                ?>style="vertical-align: middle;" <?php 
                                ?>title="<?php echo __a(__FILE__, 'View'); ?>" <?php 
                                ?>href="<?php echo $viewUrl  ?>" <?php 
                                ?>target="_blank" <?php 
                                ?>role="button"<?php 
                            ?>><?php
                                ?><i class="fa fa-eye"></i><?php
                            ?></a><?php 
                        }
                        if ($orderEditable) {
                            ?><a class="-run-action -run-action-edit" <?php 
                                ?>style="vertical-align: middle;" <?php 
                                ?>title="<?php echo __a(__FILE__, 'Upraviť'); ?>" <?php 
                                ?>href="<?php 
                                    echo App::getUrl(array(
                                        'module' => 'Eshop',
                                        'controller' => 'EshopProducts',
                                        'action' => 'admin_edit',
                                        'args' => array($product['run_eshop_products_id']),
                                    ))  
                                ?>" <?php 
                                ?>target="_blank" <?php 
                                ?>data-run-st-creator="" <?php
                                ?>role="button"<?php 
                            ?>><?php
                                ?><i class="fa fa-pencil"></i><?php
                            ?></a><?php 
                            ?><a <?php 
                                ?>class="-run-action -run-action-delete" <?php 
                                ?>style="vertical-align: middle;" <?php 
                                ?>title="<?php echo __a(__FILE__, 'Remove'); ?>" <?php 
                                ?>href="javascript:deleteProduct(<?php echo $rowIndex ?>);" <?php 
                                ?>role="button"<?php 
                            ?>><?php
                                ?><i class="fa fa-remove"></i><?php
                            ?></a><?php 
                        }
                    ?></td><?php
                ?></tr><?php
                if ($template) {
                    $productRowTemplate = Html::endCapture();
                }
            }
            ?><tr id="addProductRow"><?php 
                ?><td colspan="9"><?php 
                    if ($orderEditable) {
                        ?><a href="javascript:addProduct();"><?php echo __a(__FILE__, 'Add product'); ?></a><?php
                    }
                    else {
                        echo __a(__FILE__, 'Produkty už nie je možné upravovať');
                    }
                ?></td><?php
            ?></tr><?php
            ?><tr><?php 
                ?><td colspan="7"></td><?php
                ?><td colspan="2"><?php 
                    echo $OrderForm->text('bonus_discount', array(
                        'label' => __a(__FILE__, 'Bonus'),
                        'id' => 'totalBonus',
                    ) + (!$orderEditable ? $readonlyOptions : array()))
                ?></td><?php
            ?></tr><?php
            ?><tr><?php 
                ?><td colspan="7"></td><?php
                ?><td colspan="2"><?php 
                    echo $OrderForm->text('products_price_to_pay', array(
                        'label' => __a(__FILE__, 'Total price for products'),
                        'id' => 'totalPriceForProducts',
                    ) + $readonlyOptions)
                ?></td><?php
            ?></tr><?php
            ?><tr><?php 
                ?><td colspan="7"></td><?php
                ?><td colspan="2"><?php 
                    echo $OrderForm->text('shipment_price_actual_taxed', array(
                        'label' => __a(__FILE__, 'Total price for shipping'),
                        'id' => 'totalShipping',
                    ) + $readonlyOptions)
                ?></td><?php
            ?></tr><?php
            ?><tr><?php 
                ?><td colspan="7"></td><?php
                ?><td colspan="2"><?php 
                    echo $OrderForm->text('order_price_to_pay', array(
                        'label' => __a(__FILE__, 'Total price for order'),
                        'id' => 'totalPriceForOrder',
                    )+ $readonlyOptions)
                ?></td><?php
            ?></tr><?php
        ?></table><?php 
    ?></div>
</form><?php 
App::startJsCapture();
?><script type="text/javascript">
jQuery(function(){
    new Run.App.SmartForm(<?php echo json_encode(array(
        'tabsToReloadAfterSave' => $options['tabsToReloadAfterSave'],
        'saved' => $options['saved'],
    )) ?>);
});
</script><?php
App::endJsCapture();
App::setCssFiles(array(
    '/app/css/vendors/bootstrap.css',
    '/app/css/vendors/bootstrap-theme.css',
    '/app/js/vendors/jquery-ui/jquery-ui.css',
    '/app/css/vendors/font-awesome/css/font-awesome.min.css',
    '/app/css/libs/smartForm.css',
));
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
    '/app/js/vendors/jquery-ui/jquery-ui.min.js',
    //'/app/js/vendors/bootstrap.min.js',
    '/app/js/libs/globalFunctions.js',
    '/app/js/libs/App.js',
    '/app/js/libs/Arr.js',
    '/app/js/libs/String.js',
    '/app/js/libs/PhpJs.js',
    '/app/js/libs/Number.js',
    '/app/js/libs/Utility.js',
    '/app/js/libs/Validate.js',
    '/app/js/libs/Sanitize.js',
    '/app/js/libs/SubWindow.js',
    '/app/js/libs/Arr.js',
    '/app/js/libs/WindowStateManager.js',
    '/app/js/libs/SmartFrame.js',
    '/app/js/libs/SmartForm.js',
    '/app/modules/Eshop/js/EshopOrders/admin_edit.js'
));
App::setJsI18nFiles(array('App', 'Eshop'));
App::setJsConfig('App', array(
    'freeShipmentProductsTotal' => App::getSetting('Eshop', 'EshopShipment.freeShipmentProductsTotal'),
//    'freePaymentProductsTotal' => App::getSetting('App', 'Payment.freePaymentProductsTotal'),
    'decimals' => App::getActualDecimals(),
    'decimalPoint' => App::getActualDecimalPoint(),
    'thousandsSeparator' => App::getActualThousandsSeparator(),
    'urlRoot' => App::$urlRoot,
    'urlLang' => App::$urlLang,
    'urlBase' => App::$urlBase,
    'homeSlug' => App::$homeSlug,
));
App::setJsConfig('Eshop', array(
    'shipmentPayment' => $this->params['shipmentPayments'],
    'productViewSlug' => $locatorProductView,
    'specificShipment' => (bool)($this->params['data']['specific'] & EshopOrder::SPECIFIC_SHIPMENT),
    'specificPayment' => (bool)($this->params['data']['specific'] & EshopOrder::SPECIFIC_PAYMENT),
    'orderProductRowTemplate' => $orderEditable ? $productRowTemplate : '',
));
App::setCss('.order-products {background:#fff}');
App::setCss('#addProductRow > td {border-top:none;padding-top:0}');
App::setCss('.order-product .form-group {margin-bottom:0}');
App::setCss('.order-product + .order-product > td {border-top:none;padding-top:0}');
