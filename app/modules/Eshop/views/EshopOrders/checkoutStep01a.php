<?php /* @var $this Template */
App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    'data' => $this->params['data'],
    'errors' => $this->params['errors'],
    'inputDefaults' => array(
        'template' => '<div class="input-wrapper">:l::i::e:</div>',
    ),
    'labelDefaults' => array(
        'separator' => '',
    )
));
$itemName =  __(__FILE__, 'items2');
if ($this->params['productCount'] == 1) {
    $itemName =  __(__FILE__, 'item');
} else if ($this->params['productCount'] > 1 && $this->params['productCount'] < 5) {
    $itemName =  __(__FILE__, 'items');
}
$this->setJsConfig('checkout', $this->params['Checkout']);
?>
<header class="shopping">
    <h1><?php echo __(__FILE__, 'My shopping cart'); ?><strong> (<?php echo $this->params['productCount'].' '.$itemName; ?>)</strong></h1>
    <span>Obsah Vášho košíka tu zostane uložený po dobu 30 dní. Kedykoľvek sa k nej môžete znovu vrátiť.</span>
</header>
<section class="shopping">
    <span class="breadcrumbs dorucenie wrapper">
        <a href="<?php echo $this->params['urlCartView'] ?>" class="kosik backward">1. <?php echo __(__FILE__, 'Shopping cart'); ?></a>
        <span class="dorucenie active">2. <?php echo __(__FILE__, 'Delivery address'); ?></span>
        <span class="sposob-platby forward">3. <?php echo __(__FILE__, 'Method of delivery and payment'); ?></span>
        <span class="potvrdenie-objednavky forward">4. <?php echo __(__FILE__, 'Order confirmation'); ?></span>
    </span>
    <div class="shoppingtable_wrapper dorucenie-a-platba">
        <h2>Údaje oznámenia</h2>
        <form class="wrapper" id="checkoutForm" action="<?php echo $this->params['urlNext'] ?>" method="post" enctype="multipart/form-data">
            <input type="hidden" name="data[_target][]" value="Eshop.EshopOrders.checkout" />
            <div class="registracia address wedding-data">
                <div class="input-wrapper">
                    <button id="setBrideData">Ako objednávateľ</button>
                    <?php echo $Form->text('bride_fullname', array(
                        'class' => 'text bride-fullname', 
                        'label' => __(__FILE__, 'Meno nevesty'),
                        'template' => ':l::i::e:',

                    )); ?>
                </div>
                <?php echo $Form->text('bride_address', array(
                    'class' => 'text', 
                    'label' => __(__FILE__, 'Adresa nevesty (ulica, číslo, PSČ, Mesto)')
                )); ?>
                <div class="input-wrapper">
                    <button id="setGroomData">Ako objednávateľ</button>
                    <?php echo $Form->text('groom_fullname', array(
                        'class' => 'text groom-fullname', 
                        'label' => __(__FILE__, 'Meno ženícha'),
                        'template' => ':l::i::e:',
                    )); ?>
                </div>
                <?php echo $Form->text('groom_address', array(
                    'class' => 'text', 
                    'label' => __(__FILE__, 'Adresa ženícha (ulica, číslo, PSČ, Mesto)')
                )); ?>
                <?php echo $Form->text('wedding_ceremony_place_and_time', array(
                    'class' => 'text', 
                    'label' => __(__FILE__, 'Presné miesto, dátum a hodina konania obradu')
                )); ?>
                <?php echo $Form->textarea('wedding_announcement_text', array(
                    'class' => 'text', 
                    'label' => __(__FILE__, 'Vlastný text oznámenia')
                )); ?>
                <div id="announcementFiles">
                    <label><?php echo   __(__FILE__, 'Priloženie fotografií') ?></label>
                    <div class="uploaded-files">
                        <?php 
                        if (!empty($this->params['data']['wedding_announcement_files'])) :
                            foreach ($this->params['data']['wedding_announcement_files'] as $file => $filename): 
                                $filepath = $this->params['checkoutFileStore'] . $file; ?>
                                <a href="<?php echo $filepath  ?>" target="_blank"><?php echo $filename ?></a>
                                <?php echo $Form->hidden('wedding_announcement_files.' . $file);
                            endforeach; 
                        endif;?>
                    </div>
                    <?php echo $Form->file('wedding_announcement_files.', array(
                        'class' => 'text', 
                        'template' => '<div class="input-wrapper first">:l::i::e:</div>',
                    )); ?>
                </div>
                <div class="input-wrapper">
                    <button id="addAnnouncementFile">Pridaj súbor</button>
                </div>    
                <?php echo $Form->textarea('comment', array(
                    'class' => 'text',
                    'label' => __(__FILE__, 'Iné požiadavky')
                )) ?>
            </div>         
        </form>
        <p class="shoppingtable_nav">
            <span class="back"><a href="<?php echo $this->params['urlPrevious'] ?>">Krok späť</a></span>
            <span class="continue"><a href="javascript:void(0);" onclick="$('#checkoutForm').submit()">Pokračovať</a></span>
            <!--<span class="continue"><input type="submit" value="Pokračovať"></span>-->
        </p>
    </div>
</section>
