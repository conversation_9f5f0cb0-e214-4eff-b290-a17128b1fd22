<?php /* @var $this Template */
$this->displayOriginComment = true;
App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    'data' => $this->params['data'],
    'errors' => $this->params['errors'],
    //'required' => $this->params['required'],
    'inputDefaults' => array(
        'attachErrors' => false,
    ),
    'labelDefaults' => array(
        'separator' => '',
    )
));

Html::startCapture();
?><div class="nice-radio"><?php
    ?>:i:<?php
    ?><span class="image"></span><?php
    ?>:l:<?php
    ?></div><?php
$radioTemplate = Html::endCapture();

?><div class="checkout checkout-shipment-and-payment"><?php 
    echo $this->loadView('EshopOrders/checkoutHeader', array(
        'checkoutStep' => 'checkoutStep02',
        'productsCount' => $this->params['productCount'],
    ));
    $checkoutFormId = uniqid('checkout-form-');
    ?><form action="<?php echo $this->params['urlNext'] ?>" method="post" id="<?php echo $checkoutFormId ?>"><?php 
        ?><input type="hidden" name="data[_target][]" value="Eshop.EshopOrders.checkout" /><?php
        ?><div class="grid-row"><?php 
            ?><div class="grid-col grid-col-50-100"><?php 
                ?><div class="title"><?php 
                    echo __(__FILE__, 'Shipping method')
                ?></div><?php
                foreach ($this->params['EshopShipmentMethod'] as $item) {
                    ?><div class="shipment-method-item"><?php
                        echo $Form->radio('shipment', array(
                            'toggleValue' => $item['id'],
                            'label' => $item['name'],
                            'title' => $item['name'],
//                            'template' => ':i::l:',
                            'template' => $radioTemplate,
                        ));
                        if (
                            !empty($item['description'])
                            || !empty($item['shipment_price_actual_taxed'])
                            || !empty($item['info_locator'])
                            || !empty($item['pickup_places'])
                        ) {
                            ?><div class="info"><?php 
                                if (!empty($item['pickup_places'])) {
                                    echo $Form->select('pickup_place.' . $item['id'], array(
                                        'empty' => '-- ' . __(__FILE__, 'Please choose pickup place') . ' --', 
                                        'options' => $item['pickup_places']
                                    ));
                                    if (
                                        !empty($this->params['data']['shipment'])
                                        && $this->params['data']['shipment'] == $item['id']
                                    ) {
                                        echo $Form->errors('pickup_place');
                                    }
                                }
                                if (!empty($item['description'])) {
                                    echo nl2br($item['description']); 
                                    ?><br /><?php 
                                }
                                if (isset($item['shipment_price_actual_taxed'])) { 
                                    echo __(
                                        __FILE__, 
                                        'The shipment price is %s', 
                                        Eshop::formatPrice($item['shipment_price_actual_taxed'])
                                    );
                                }
                                if (!empty($item['info_locator'])) {
                                    ?><a <?php 
                                        ?>class="read-more" <?php 
                                        ?>title="<?php echo $item['info'] ?>" <?php 
                                        ?>href="<?php echo App::getUrl($item['info_locator']) ?>"<?php 
                                    ?>><?php 
                                        ?><i class="fa fa-info-circle"></i><?php
                                    ?></a><?php
                                }
                            ?></div><?php
                        }
                    ?></div><?php
                }
                echo $Form->errors('shipment')
            ?></div><?php 
            ?><div class="grid-col grid-col-50-100"><?php 
                ?><div class="title"><?php 
                    echo __(__FILE__, 'Payment method')
                ?></div><?php
                foreach ($this->params['PaymentMethod'] as $item) {
                    ?><div class="payment-method-item"><?php
                        echo $Form->radio('payment', array(
                            'toggleValue' => $item['id'],
                            'label' => $item['name'],
                            'title' => $item['name'],
//                            'template' => ':i::l:',
                            'template' => $radioTemplate,
                        ));
                        if (
                            !empty($item['description'])
                            || !empty($item['payment_price_actual_taxed'])
                            || !empty($item['info_locator'])
                        ) {
                            ?><div class="info"><?php 
                                if (!empty($item['description'])) {
                                    echo nl2br($item['description']);
                                }
                                if (!empty($item['payment_price_actual_taxed'])) { 
                                    echo __(
                                        __FILE__, 
                                        'The payment price is %s', 
                                        Eshop::formatPrice($item['payment_price_actual_taxed'])
                                    );
                                }
                                if (!empty($item['info_locator'])) {
                                    ?><a <?php 
                                        ?>class="read-more" <?php 
                                        ?>title="<?php echo $item['info'] ?>" <?php 
                                        ?>href="<?php echo App::getUrl($item['info_locator']) ?>"<?php 
                                    ?>><?php 
                                        ?><i class="fa fa-info-circle"></i><?php
                                    ?></a><?php
                                }
                            ?></div><?php
                        }
                    ?></div><?php
                }      
                echo $Form->errors('payment');
            ?></div><?php 
        ?></div><?php 
    ?></form><?php
    echo $this->loadView('EshopOrders/checkoutFooter', array(
        'urlBack' => $this->params['urlPrevious'],
        'submitFormId' => $checkoutFormId,
    ));
?></div><?php
App::startJsCapture();
?><script type="text/javascript">
jQuery(function(){
    
togglePayment();
jQuery('#<?php echo $checkoutFormId ?> input[name="data[shipment]"]').click(function(e){
    togglePayment(jQuery(this).val());
}); 
function togglePayment(shipmentId) {
    if (typeof shipmentId === 'undefined') {
        shipmentId = jQuery('#<?php echo $checkoutFormId ?> input[name="data[shipment]"]:checked').val();
    }
    if (typeof shipmentId === 'undefined') {
        return;
    }
    var checkedPaymentId = jQuery('#<?php echo $checkoutFormId ?> input[name="data[payment]"]:checked').val();
    var paymentIds = Run.Eshop.config.shipmentPayment[shipmentId];
    // reset all payment methods
    // - hide them
    jQuery('#<?php echo $checkoutFormId ?> .payment-method-item').hide();
    // - uncheck them
    jQuery('#<?php echo $checkoutFormId ?> input[name="data[payment]"]:checked').prop('checked', false);
    // display the available for choosen shipment
    var length = paymentIds.length;
    var hasCheckedPayment = false;
    for (var i = 0; i < length; i++) {
        if (checkedPaymentId === paymentIds[i]) {
            jQuery('#<?php echo $checkoutFormId ?> input[name="data[payment]"]').filter('[value="' + paymentIds[i] + '"]').prop('checked', true);
            hasCheckedPayment = true;
        }
        jQuery('#<?php echo $checkoutFormId ?> input[name="data[payment]"]').filter('[value="' + paymentIds[i] + '"]').closest('.payment-method-item').show();
    }
    if (!hasCheckedPayment && length === 1) {
        jQuery('#<?php echo $checkoutFormId ?> input[name="data[payment]"]').filter('[value="' + paymentIds[0] + '"]').prop('checked', true);
    }
}

// on selecting pickup place check implicitly the related shipment 
jQuery('#<?php echo $checkoutFormId ?> .shipment-method-item .info select').on('change', function() {
    var select = jQuery(this), value = select.val(), input;
    if (value !== '') {
        input = select.closest('.shipment-method-item').find('input[type="radio"]').prop('checked', true);
        input.prop('checked', true);
        input.click();
    }
});
    
});
</script><?php
App::endJsCapture();
$this->setJsConfig('shipmentPayment', $this->params['EshopShipmentPaymentMethod']);
App::setCssFiles(array(
    '/app/css/vendors/font-awesome/css/font-awesome.min.css',
));
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
));

// RETURN
return;

//
// Code store for other projects
//
