<?php /* @var $this Template */
$order = $this->params['order'];
$products = $this->params['products'];
$orderInserts = $this->params['orderInserts'];
?><div class="order-print-detail"><?php 
    ?><div class="header"><?php  
        ?><span class="logo"><?php 
            ?><img src="<?php echo File::getTimestampedUrlPath('/app/img/logo.png') ?>" alt="" /><?php 
        ?></span><?php
    ?><div class="texts"><?php 
        ?><h1><?php 
            echo __a(__FILE__, 'Order no.') . ' ' . Sanitize::value($order['number']); 
        ?></h1><?php 
        echo __a(__FILE__, 'ordered on %s at %s', Date::format($order['created'], 'j.n.Y'), Date::format($order['created'], 'G:i:s'));
    ?></div><?php
    ?></div><?php
    ?><div class="addresses"><?php 
        ?><div class="invoice-address"><?php 
            ?><h2><?php 
                echo  __a(__FILE__, 'Invoice address');
            ?></h2><?php
            echo $orderInserts['userAddress'];
            if (
                !empty($order['phone'])
                || !empty($order['email'])
            ) {
                if (!empty($order['phone'])) {
                    ?><br /><?php
                    echo '<b>' . __a(__FILE__, 'Phone') . ':</b> ' . $order['phone']; 
                }
                if (!empty($order['email'])) {
                    ?><br /><?php
                    echo '<b>' . __a(__FILE__, 'E-mail') . ':</b> ' . $order['email'];       
                }
            }
        ?></div><?php
        ?><div class="delivery-address"><?php 
            ?><h2><?php 
                echo  __a(__FILE__, 'Delivery address');
            ?></h2><?php
            echo $orderInserts['deliveryAddress'];
            if (
                !empty($order['delivery_phone'])
                || !empty($order['delivery_email'])
            ) {
                if (!empty($order['delivery_phone'])) {
                    ?><br /><?php
                    echo '<b>' . __a(__FILE__, 'Phone') . ':</b> ' . $order['delivery_phone']; 
                }
                if (!empty($order['delivery_email'])) {
                    ?><br /><?php
                    echo '<b>' . __a(__FILE__, 'E-mail') . ':</b> ' . $order['delivery_email'];       
                }
            }
        ?></div><?php
    ?></div><?php
    ?><div class="other-info"><?php 
        echo '<b>' . __a(__FILE__, 'Shipping') . ':</b> ' . $order['shipment_method_name']; 
        ?><br /><?php
        echo '<b>' . __a(__FILE__, 'Payment') . ':</b> ' . $order['payment_method_name']; 
        ?><br /><?php
        if ($order['comment']) {
            echo '<b>' . __a(__FILE__, 'Poznámka zákazníka') . ':</b> ' . $order['comment'];
        }
    ?></div><?php
    ?><table class="items">
        <thead>
            <tr>
                <th><?php echo __a($this, 'ID'); ?></th>
                <th><?php echo __a($this, 'Code'); ?></th>
                <th><?php echo __a($this, 'Name'); ?></th>
                <th><?php echo __a($this, 'Attributes'); ?></th>
                <th class="right" style="width: 100px;"><?php echo __a($this, 'Price for unit'); ?></th>
                <th class="right"><?php echo __a($this, 'Amount'); ?></th>
                <th class="right" style="width: 100px;"><?php echo __a($this, 'Price'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($products as $ind => $product) : ?>
            <tr>
                <td><?php echo $product['id']; ?></td>
                <td><?php echo $product['code']; ?></td>
                <td><?php echo $product['name']; ?></td>
                <td><?php echo $product['dynamic_attributes']; ?></td>
                <td class="right"><?php 
                    echo Eshop::formatPrice($product['price_actual_taxless'] + $product['tax_actual']) 
                ?></td>
                <td class="right"><?php echo $product['amount']; ?></td>
                <td class="right"><?php 
                    echo Eshop::formatPrice(($product['price_actual_taxless'] + $product['tax_actual']) * $product['amount']) 
                ?></td>
            </tr>
            <?php endforeach; ?>
            <tr>
                <td colspan="7">&nbsp;</td>
            </tr>
        </tbody>
        <tfoot>
            <?php if ((float)$order['bonus_discount']) { ?>
            <tr>
                <th colspan="6" class="right"><?php echo __a(__FILE__, 'Bonus'); ?></th>
                <th class="right"><?php echo '-' . Eshop::formatPrice($order['bonus_discount']) ?></th>
            </tr>    
            <?php } ?> 
            <tr>
                <th colspan="6" class="right"><?php echo __a(__FILE__, 'Total price for products'); ?></th>
                <th class="right"><?php echo Eshop::formatPrice($order['products_price_to_pay']) ?></th>
            </tr>
            <tr>
                <th colspan="6" class="right"><?php echo __a(__FILE__, 'Total price for shipping'); ?></th>
                <th class="right"><?php echo Eshop::formatPrice($order['shipment_price_actual_taxless'] + $order['shipment_tax_actual'] + $order['payment_price_actual_taxless'] + $order['payment_tax_actual']) ?></th>
            </tr>
            <tr>
                <th colspan="6" class="right"><?php echo __a(__FILE__, 'Total price for order'); ?></th>
                <th class="right"><?php echo Eshop::formatPrice($order['order_price_to_pay']) ?></th>
            </tr>
        </tfoot>
    </table><?php 
?></div><?php
App::startCssCapture(); 
?><style>
    body {
        padding: 0px;
        margin: 10px;
        font-family: sans-serif;
        font-size: 14px;
        color: #000;
    }
    .order-print-detail {
        max-width: 21cm;
    }
    table {
        width: 100%;
        border: 1px solid black;
        border-spacing: 0;
    }
    thead {
        border-bottom: 2px solid #ccc;
    }
    tbody td, tbody th {
        border-bottom: 1px solid #ccc;
    }
    tfoot {
        border-top: 2px solid #ccc;
    }
    tfoot td, tfoot th {
        border-top: 1px solid #ccc;
    }
    th {
        background: #ddd;    
    }
    th, td {
        padding: 2pt 4pt;
        font-size: 12px;
    }
    .right {
        text-align: right;
    }
    .header  {
        overflow: hidden;
        border-bottom: 1px solid black;
        padding-top: 10px;
        padding-bottom: 10px;
    }
    .header > * {
        float: left;
    }
    .header img {
        margin-right: 20px;
    }
    .header h1 {
        font-size: 34px;
        font-weight: bold;
    }
    h2 {
        font-size: 20px;
        font-weight: bold;
    }
    .invoice-address, .delivery-address {
        display: inline-block;
        width: 50%;
        vertical-align: top;
    }
    .addresses, .other-info, .items {
        margin-top: 20px;
    }
    .addresses h2 {
        margin-top: 0;
    }
</style><?php 
App::endCssCapture(); 
App::startJsCapture(); 
?><script type="text/javascript">
    jQuery(document).ready(function(){
        window.print();
        var SmartTabs = top.Run.App.SmartTabs.getInstance('#adminTabs');
        if (SmartTabs) {            
            setTimeout(function(){
                SmartTabs.closeActualTab();
            }, 5000);
        }
    });
</script><?php 
App::endJsCapture();
App::setJsConfig('App', array(
    'freeShipmentProductsTotal' => App::getSetting('Eshop', 'EshopShipment.freeShipmentProductsTotal'),
//    'freePaymentProductsTotal' => App::getSetting('App', 'Payment.freePaymentProductsTotal'),
));
App::setCssFiles(array(
    '/app/css/vendors/bootstrap.css',
    '/app/css/vendors/bootstrap-theme.css',
    '/app/css/libs/smartForm.css',
));
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
    //'/app/js/vendors/bootstrap.min.js',
    '/app/js/libs/globalFunctions.js',
    '/app/js/libs/PhpJs.js',
    '/app/js/libs/Number.js',
    '/app/js/libs/Utility.js',
    '/app/js/libs/Validate.js',
    '/app/js/libs/Sanitize.js',
    '/app/js/libs/SubWindow.js',
    '/app/js/libs/Arr.js',
    '/app/js/libs/WindowStateManager.js',
));
