<?php /* @var $this Template *//** @var Template $this */
$this->displayOriginComment = true;
if (!$this->params['debug']) {
    App::startJsCapture();
}
?><script type="text/javascript">
fbq('track', 'AddToCart', {
    currency: "EUR", 
    value: <?php echo $this->params['price'] ?>,
    content_type: "product",
    contents: <?php 
        $contents = array();
        foreach ($this->params['products'] as $product){
            $contents[] = array(
                'id' => $product['id'],
                'quantity' => $product['amount'],
            );
        }
        echo json_encode($contents);
    ?>
    
});
</script><?php 
if (!$this->params['debug']) {
    App::endJsCapture(array(
        'permanent' => true,
    ));
}