<?php /* @var $this Template */
$this->displayOriginComment = true;
if (empty($this->params['products'])) {
    return;
}
Html::startCapture();
?><div class="app-messages-container off-stock-products"><?php
    ?><ul><?php
        foreach ($this->params['products'] as $product) {
            ?><li><?php 
                echo $product['name']
            ?></li><?php
        }
    ?></ul><?php
    echo __(__FILE__, 'Vybraný tovar nie je momentálne <b>na sklade</b> a jeho dodanie môže trvať dlhšiu dobu. Pre rýchle vybavenie nákupu doporučujeme vybrať tovar, ktorý je <b>na sklade</b>.')
?></div><?php
$content = Html::endCapture();
App::startJsCapture();
?><script type="text/javascript">
    jQuery(function(){

    var $button = jQuery('.checkout-footer .button.continue');
    $button.on('click.confirmAppMessage', function(event) {
        event.preventDefault();
        jQuery('body').css('overflow', 'hidden');
        swal({
            content: jQuery('<?php echo $content ?>').get(0),
            className: '<?php echo 'app-messages-modal' ?>',
            closeOnClickOutside: false,
            closeOnEsc: true,
            buttons: ['Späť k nákupu',  'Pokračovať k objednávke']
        }).then(function(confirmed) {
            var href;
            if (confirmed) {
                $button.off('click.confirmAppMessage');
                if (
                    $button.prop('tagName') === 'A'
                    && (href = $button.attr('href'))
                ) {
                    window.location = href;
                }
                else {
                    $button.click();
                }
            }
            else {
                jQuery('body').css('overflow', '');
            }
        });
    });

    });
</script><?php
App::endJsCapture();
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
    '/app/js/vendors/sweetalert.min.js',
));

