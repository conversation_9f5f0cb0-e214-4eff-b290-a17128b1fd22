<?php /* @var $this Template */
$this->displayOriginComment = true;
?><div id="cart-view"><?php 
    echo $this->loadView('EshopOrders/checkoutHeader', array(
        'checkoutStep' => 'cart',
        'productsCount' => count($this->params['products']),
    ));
    ?><div class="cart"><?php 
        echo $this->loadView('EshopCarts/viewProducts', array(
            'products' => $this->params['products'],
            'editable' => true,
        ))
        ?><div class="cart-summary"><?php 
            ?><div class="cart-total"><?php 
                if ((float)$this->params['totals']['bonus_discount']) {
                    ?><div class="bonus"><?php 
                        ?><span class="label"><?php 
                            echo __(__FILE__, 'Bonus') . ': ';
                        ?></span><?php
                        ?><span class="price"><?php 
                            echo '-' . Eshop::formatPrice($this->params['totals']['bonus_discount']);
                        ?></span><?php
                    ?></div><?php
                }
                if (!empty($this->params['totals']['vouchers_discount'])) {
                    ?><div class="bonus"><?php 
                        ?><span class="label"><?php 
                            echo __(__FILE__, 'Zľava spolu') . ': ';
                        ?></span><?php
                        ?><span class="price"><?php 
                            echo '-' . Eshop::formatPrice($this->params['totals']['vouchers_discount']);
                        ?></span><?php
                    ?></div><?php
                }
                ?><div class="total"><?php 
                ?><div><?php
                    ?><span class="label"><?php 
                        echo __(__FILE__, 'Total price for products') . ': ';
                    ?></span><?php
                    ?><span class="price"><?php 
                        echo Eshop::formatPrice($this->params['totals']['products_price_to_pay']);        
                    ?></span><?php
                ?></div><?php
            ?></div><?php
        ?></div><?php
            ?><div class="cart-info"><?php 
                ?><i class="fa fa-info-circle"></i><?php
                echo __($this, 'Ak nie je uvedené inak, tak ceny sú <b>s DPH</b>');
                ?><br><?php 
                if ($this->params['hasOversizedProducts']) {
                    ?><i class="fa fa-info-circle"></i><?php
                    ?><span class="oversized-warning"><?php 
                        echo $this->getSetting('EshopOrder.oversizedProductMessage') . ' ';
                    ?></span><?php
                    ?><br><?php 
                }
                /*/>
                elseif (($freeShipmentProductsTotal = $this->getSetting('EshopShipment.freeShipmentProductsTotal'))) {
                    ?><i class="fa fa-info-circle"></i><?php
                    echo __(__FILE__, 'Free shipping on purchase over %s', Eshop::formatPrice($freeShipmentProductsTotal)) . '. ';
                    ?><br><?php 
                }
                /*/
                $this->loadModel('EshopCart');
                $Cart = new EshopCart();
                $freeShipmentAchieved = false;
                if (
                    !($this->params['hasOversizedProducts'])
                    && ($amountToFreeShipmentMessage = $Cart->getAmountToFreeShipmentMessage(array(
                        'freeShipmentAchieved' => &$freeShipmentAchieved,
                    )))
                ) {
                    ?><i class="fa fa-info-circle"></i><?php
                    $class = $freeShipmentAchieved ? ' achieved' : '';
                    ?><span class="free-shipment<?php echo $class ?>"><?php 
                        echo $amountToFreeShipmentMessage;
                    ?></span><?php
                }
                //*/
                /*/>
                ?><i class="fa fa-info-circle"></i><?php 
                ?><span class="covid-warning"><?php 
                    echo __($this, 'Vzhľadom na situáciu s pandémiou COVID-19 a obmedzeniami s tým spojenými, sa môže doba dodania tovaru predĺžiť. O dostupnosti Vášho tovaru Vás budeme telefonicky informovať.');
                ?></span><?php 
                //*/
            ?></div><?php
        ?></div><?php
        if (!empty($this->params['urlSetVoucher'])) {
            App::loadLib('App', 'FormHelper');
            $Form = new FormHelper(array(
                'labelDefaults' => array(
                    'separator' => false,
                )
            ));
            ?><div class="cart-additional-discounts"><?php 
                if (!empty($this->params['actualVoucher'])) {
                    ?><div class="clear-voucher"><?php 
                        echo __(__FILE__, 'Použili ste zľavový kód %s', $this->params['actualVoucher']['code']);
                        ?> <a href="<?php echo $this->params['urlClearVoucher'] ?>" class="button secondary"><?php 
                            echo __(__FILE__, 'Remove');
                        ?></a><?php
                    ?></div ><?php
                }
                else {
                    ?><form action="<?php echo $this->params['urlSetVoucher'] ?>" method="post" class="voucher"><?php 
                        $label = __(__FILE__, 'Voucher code');
                        echo $Form->text('voucher_code', array(
                            'label' => $label,
                        ))
                        ?><button type="submit" class="secondary"><?php 
                            echo __(__FILE__, 'Použiť')
                        ?></button><?php
                        ?><div class="note"><?php 
                            echo __(
                                __FILE__, 
                                'Tu si môžete uplatniť <b>zľavový kód</b> alebo kód <b>darovacej karty</b>',
                                $this->params['additionalDiscountRateLimit']
                            )
                        ?></div><?php
                    ?></form><?php
                }
            ?></div><?php
        }
        // special offer banners
        $discountingOffers = array();
        $promotingOffers = array();
        foreach ($this->params['products'] as $product) {
            if (!empty($product['DiscountingSpecialOffer'])) {
                foreach ($product['DiscountingSpecialOffer'] as $offerId => $offer) {
                    if (isset($discountingOffers[$offerId])) {
                        $discountingOffers[$offerId]['applicable'] = !empty($discountingOffers[$offerId]['applicable']) 
                            || !empty($offer['applicable']);
                        $discountingOffers[$offerId]['applicated'] = !empty($discountingOffers[$offerId]['applicated'])
                            || !empty($offer['applicated']);
                    }
                    else {
                        $discountingOffers[$offerId] = $offer;
                    }
                }
            }
        }
        foreach ($this->params['products'] as $product) {
            if (!empty($product['PromotingSpecialOffer'])) {
                foreach ($product['PromotingSpecialOffer'] as $offerId => $offer) {
                    // skip offers already listed in discounting ones
                    if (isset($discountingOffers[$offerId])) {
                        continue;
                    }
                    if (isset($promotingOffers[$offerId])) {
                        $promotingOffers[$offerId]['applicable'] = !empty($promotingOffers[$offerId]['applicable'])
                            || !empty($offer['applicable']);
                        $promotingOffers[$offerId]['applicated'] = !empty($promotingOffers[$offerId]['applicated'])
                            || !empty($offer['applicated']);
                    }
                    else {
                        $promotingOffers[$offerId] = $offer;
                    }
                }
            }
        }
        $discountingOfferBanners = $this->loadElement('specialOfferBanners', array(
            'offers' => $discountingOffers,
            'for' => 'cartProducts',
        ));
        if (!empty($discountingOfferBanners)) {
            ?><div class="special-offers"><?php 
                ?><div class="intro-text"><?php 
                    echo __(__FILE__, '<b>Produkty v košíku môžete získať výhodnejšie</b> vrámci nasledovných špeciálnych ponúk');
                ?></div><?php
                echo $discountingOfferBanners;
            ?></div><?php
        }
        $promotingOfferBanners = $this->loadElement('specialOfferBanners', array(
            'offers' => $promotingOffers,
            'for' => 'cartProducts',
        ));
        if (!empty($promotingOfferBanners)) {
            ?><div class="special-offers"><?php 
                ?><div class="intro-text"><?php 
                    echo __(__FILE__, 'Pre produkty v košíku <b>máme pre Vás nasledovné špeciálne ponuky</b>');
                ?></div><?php
                echo $promotingOfferBanners;
            ?></div><?php
        }
    ?></div><?php
    echo $this->loadView('EshopOrders/checkoutFooter', array(
        'urlBack' => $this->params['urlBack'],
        'urlContinue' => $this->params['urlOrderCheckout'],
    ));
?></div><?php 
App::setCssFiles(array(
    '/app/css/vendors/font-awesome/css/font-awesome.min.css',
));

// RETURN
return;

//
// Code store for other projects
//
