<?php /* @var $this Template */
$this->displayOriginComment = true;
$productCount = count($this->params['products']);
$itemName =  __(__FILE__, 'items2');
if ($productCount == 1) {
    $itemName =  __(__FILE__, 'item');
} else if ($productCount > 1 && $productCount < 5) {
    $itemName =  __(__FILE__, 'items');
}
$urlShowCart = App::getUrl(array(
    'locator' => $this->params['slugCartView']
));
?><a href="<?php echo $urlShowCart; ?>" id="mini-cart" class="mini-cart" title="<?php echo __(__FILE__, 'Your shopping cart') ?>"><?php 
    ?><span class="mini-cart-price"><?php 
        echo Eshop::formatPrice($this->params['totals']['products_price_to_pay']); 
    ?></span><?php 
    ?><span class="mini-cart-icon"><?php
        ?><img src="/app/img/ico-basket.svg?ts=220223"><?php
    ?></span><?php
    ?><span class="mini-cart-count"><?php 
        echo $productCount; 
    ?></span><?php 
?></a><?php 
