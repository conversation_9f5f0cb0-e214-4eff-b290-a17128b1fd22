<?php /* @var $this Template */
$this->displayOriginComment = true;
/**
 * Partial view (subview) used in views/EshopCarts/view and views/EshopOrders/checkoutStep03
 * 
 * following items are avaited in $this->params:
 *  - 'products' (array)
 *  - 'editable' (bool) If TRUE then products amount can be changed and ptoducts can be removed from cart entirely.
 *      If FALSE then amount input is replaced by plain text and actions column (containing delete action)
 *      is not generated. Defaults to TRUE.
 */
$defaults = array(
    'products' => null,
    'editable' => true,
);
$this->params = array_merge($defaults, $this->params);
$products = $this->params['products'];
$locatorProductView = App::getContentLocatorByPid('Eshop.EshopProducts.view');
$slugSpecialOfferView = App::getContentLocatorByPid('Eshop.EshopSpecialOffers.view');
if ($this->params['editable']) {
    $urlUpdateCart = App::getUrl(array( 
        'module' => $this->module,
        'controller' => 'EshopCarts',
        'action' => 'updateProducts',
    ));
    $cartFormId = uniqid('cart-view-form-');
    ?><form action="<?php echo $urlUpdateCart ?>" method="post" id="<?php echo $cartFormId ?>" class="cart-products"><?php 
        ?><input type="hidden" name="data[_target][]" value="Eshop.EshopCarts.updateProducts" /><?php
}
else {
    ?><div class="cart-products"><?php
}
    ?><div class="cart-products-header"><?php
        ?><div class="product-overview"><?php 
            echo __(__FILE__, 'Product');
        ?></div><?php
        ?><div class="product-unit-price"><?php 
            echo __(__FILE__, 'Unit price');
        ?></div><?php
        ?><div class="product-amount"><?php 
            echo __(__FILE__, 'Quantity');
        ?></div><?php
        ?><div class="product-total-price"><?php 
            echo __(__FILE__, 'Total price');
        ?></div><?php
        if ($this->params['editable']) {
            ?><div class="product-actions"><?php 
            ?></div><?php
        }
    ?></div><?php
    foreach ($products as $i => $product) {
        $urlShowDetail = App::getUrl(array(
            'locator' => $locatorProductView,
            'args' => array($product['slug'])
        ));
        if (
            !empty($product['voucher'])
            || !empty($product['is_additional_service'])
        ) {
            $urlShowDetail = 'javascript:void(0);';
        }
        $urlRemoveFromCart =  App::getUrl(array(
            'module' => $this->module,
            'controller' => 'EshopCarts',
            'action' => 'removeProduct',
            'args' => array($product['cartIndex'])
        ));
        $disponibility = '';
        $disponibilityClass = '';
        switch ($product['disponibility']) {
            case EshopProduct::STOCK:
                $disponibility = __(__FILE__, 'Available on stock');
                $disponibilityClass = ' on-stock';
                break;
            case EshopProduct::SUPPLIER:
                $disponibility = __(__FILE__, 'Available at supplier');
                $disponibilityClass = ' at-supplier';
                break;
            case EshopProduct::ON_DEMAND:
                $disponibility = __(__FILE__, 'On demand');
                $disponibilityClass = ' on-demand';
                break;
            case EshopProduct::SOLDOUT:
                $disponibility = __(__FILE__, 'Sold out');
                $disponibilityClass = ' sold-out';
                break;
            case EshopProduct::PRESALE:
                $disponibility = __(__FILE__, 'Presale');
                $disponibilityClass = ' presale';
                break;
            default:
                break;
        }
        if (
            !empty($product['available_from'])
            && strtotime($product['available_from']) > time()
        ) {
            $disponibilityClass .= ' with-available-from';
        }
        ?><div class="cart-product<?php echo $disponibilityClass ?>"><?php 
            ?><div class="product-overview"><?php 
                if (!empty($product['image']['small'])) {                    
                    ?><div class="image"><?php
                        /*/
                            ?><a href="<?php echo $urlShowDetail; ?>"><?php 
                                ?><img src="<?php echo $product['image']['small']; ?>" alt="<?php echo $product['name']; ?>" /><?php
                            ?></a><?php
                        /*/
                        ?><div class="wrapper"><?php 
                            // special offers
                            echo $this->loadElement('productSpecialOfferLabels', $this->params + array(
                                'product' => $product,
                                'for' => 'cartProduct',
                                'slugSpecialOfferView' => $slugSpecialOfferView,
                            ));
                            // product image
                            ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'More info') ?>" class="product-image"><?php
                                ?><img <?php 
                                    ?>src="<?php echo $product['image']['small']; ?>" <?php 
                                    ?>alt="<?php echo $product['name']; ?>"<?php 
                                ?>/><?php
                            ?></a><?php
                        ?></div><?php
                    ?></div><?php
                }
                ?><div class="info"><?php 
                    ?><div class="name"><?php 
                        ?><a href="<?php echo $urlShowDetail; ?>"><?php
                            echo $product['name']; 
                        ?></a><?php
                    ?></div><?php
                    ?><div class="attributes"><?php 
                        if (!empty($product['static_attributes'])) {
                            foreach ($product['static_attributes'] as $attrName => $attrValue) {
                                // replace possible attr pids by attr values
                                if (isset($product[$attrName]['values'][$attrValue])) {
                                    $attrValue = $product[$attrName]['values'][$attrValue];
                                }
                                // variant attribute is general attribute which values should be clear without attribute name
                                if ($attrName !== 'variant') {
                                    echo __(__FILE__, $attrName) . ': ';
                                }
                                echo $attrValue . '<br>';
                            }
                        } 
                        if (!empty($product['dynamic_attributes'])) {
                            foreach ($product['dynamic_attributes'] as $attrName => $attrValue) {
                                echo trim($attrName) . ': ' . $attrValue . '<br>';
                            }
                        } 
                    ?></div><?php
                    if ($disponibility) {                        
                        ?><div class="disponibility"><?php 
                            echo $disponibility;
                        ?></div><?php
                        echo $this->loadView('EshopProducts/availableFromLabel', array('product' => $product));
                    }
                    if (
                        $this->params['editable']
                        && empty($product['is_additional_service'])
                        && !empty($product['EshopAdditionalServiceProduct'])
                    ) {
                        ?><div class="additional-services"><?php 
                            foreach ($product['EshopAdditionalServiceProduct'] as $service) {
                                // skip already applied services
                                if (in_array($service['id'], $product['child_ids'])) {
                                    continue;
                                }
                                $urlAddChildProduct = App::getUrl(array( 
                                    'module' => $this->module,
                                    'controller' => 'EshopCarts',
                                    'action' => 'addChildProduct',
                                    'args' => array(
                                        $product['cartIndex'],
                                        $service['id']
                                    ),
                                ));
                                ?><a <?php 
                                    ?>href="<?php echo $urlAddChildProduct ?>" <?php 
                                    ?>class="additional-service" <?php 
                                    ?>title="<?php echo __(__FILE__, 'Pridať doplnkovú službu') ?>"<?php
                                ?>><?php 
                                    ?><input type="checkbox"><?php
                                    echo __(
                                        __FILE__, 
                                        '%s (%s / ks)',
                                        $service['name'],
                                        Eshop::formatPrice($service['price_actual_taxed'])
                                    );
                                ?></a><?php
                            }
                        ?></div><?php
                    }
                ?></div><?php
            ?></div><?php
            ?><div class="product-unit-price"><?php 
                    ?><span class="label">Cena / kus</span><?php
                    ?><div><?php
                        echo Eshop::formatPrice($product['price_actual_taxed']);
                    ?></div><?php
            ?></div><?php
            ?><div class="product-amount"><?php
                ?><span class="label">Množstvo</span><?php
                if ($this->params['editable']) {
                    ?><input type="hidden" name="data[<?php echo $i; ?>][id]" value="<?php echo $product['id']; ?>" /><?php
                    ?><input type="hidden" name="data[<?php echo $i; ?>][cartIndex]" value="<?php echo $product['cartIndex']; ?>" /><?php
                    if (!empty($product['static_attributes'])) :
                        foreach ($product['static_attributes'] as $attrName => $attrValue): 
                            ?><input type="hidden" name="data[<?php echo $i; ?>][static_attributes][<?php echo $attrName ?>]" value="<?php echo $attrValue; ?>" /><?php 
                        endforeach; 
                    endif;
                    if (!empty($product['dynamic_attributes'])) :
                        foreach ($product['dynamic_attributes'] as $attrName => $attrValue): 
                            ?><input type="hidden" name="data[<?php echo $i; ?>][dynamic_attributes][<?php echo $attrName ?>]" value="<?php echo $attrValue; ?>" /><?php 
                        endforeach; 
                    endif;
                    ?><div class="product-amount-change"><?php
                        ?><div class="product-amount-change-controls"><?php
                            ?><div <?php
                                ?>class="plus" <?php
                                if ($product['voucher']) {
                                    ?>disabled="disabled" <?php
                                }
                            ?>><?php
                            ?></div><?php
                            ?><div <?php
                                ?>class="minus" <?php
                                if ($product['voucher']) {
                                    ?>disabled="disabled" <?php
                                }
                            ?>><?php
                            ?></div><?php
                        ?></div><?php
                        ?><div class="product-amount-change-value"><?php
                            ?><input <?php 
                                ?>class="product-amount-input" <?php 
                                ?>type="text" <?php 
                                ?>name="data[<?php echo $i; ?>][amount]" <?php 
                                ?>value="<?php echo $product['amount']; ?>" <?php 
                                if ($product['voucher']) {
                                    ?>disabled="disabled" <?php
                                }
                            ?>/><?php
                        ?></div><?php
                    ?></div><?php
                }
                else {
                    echo $product['amount'];
                }
            ?></div><?php
            ?><div class="product-total-price"><?php 
                ?><span class="label">Cena spolu</span><?php
                ?><div><?php
                    echo Eshop::formatPrice($product['price_actual_taxed'] * $product['amount']);
                ?></div><?php
            ?></div><?php
            if ($this->params['editable']) {
                ?><div class="product-actions"><?php 
                    ?><a href="<?php echo $urlRemoveFromCart; ?>" title="<?php echo __(__FILE__, 'Remove'); ?>"><?php 
                        ?><i class="fa fa-remove"></i><?php
                    ?></a><?php
                ?></div><?php
            }
        ?></div><?php
    }
if ($this->params['editable']) {
    ?></form><?php

    App::startjsCapture();
    ?><script type="text/javascript">
    function activateAmountButtons() {
        var productAmountChangeSelector = '.product-amount-change';
        var amountInputSelector = '.product-amount-input';
        var activatedClass = '_activated';
        var increaseBtn = $( productAmountChangeSelector + ' .plus' ).not('.' + activatedClass);
        var decreaseBtn = $( productAmountChangeSelector + ' .minus' ).not('.' + activatedClass);
        increaseBtn.click( function() {
            var productAmountChangeElement = $(this).closest(productAmountChangeSelector);
            var amountInput = productAmountChangeElement.find(amountInputSelector);
            var amount = parseInt(amountInput.val());
            amountInput.val(amount + 1);
            // trigger the change event to submit the form
            amountInput.change(); 
        });
        decreaseBtn.click( function() {
            var productAmountChangeElement = $(this).closest(productAmountChangeSelector);
            var amountInput = productAmountChangeElement.find(amountInputSelector);
            var amount = parseInt(amountInput.val());
            if (amount > 1) {
                amountInput.val(amount - 1);
                // trigger the change event to submit the form
                amountInput.change(); 
            }
        });
        // mark all buttons which have been activated already to not
        // activate them twice on infine scroll or if there are many 
        // add to cart buttons on a single page
        increaseBtn.addClass(activatedClass);
        decreaseBtn.addClass(activatedClass);
    }

    // activate products amount change controls
    activateAmountButtons();

    jQuery(function(){
        // submit form after changing product amount value
        jQuery('.product-amount-input', '#<?php echo $cartFormId ?>').change(function() {
            jQuery('#<?php echo $cartFormId ?>').submit();
        });
        // forward additional service checkbox click to its wrapping <a> element
        jQuery('.additional-service input[type="checkbox"]', '#<?php echo $cartFormId ?>').click(function() {
            window.location = jQuery(this).closest('a').attr('href');
        });
    });
    </script><?php
    App::endJsCapture();
    App::setJsFiles(array(
        '/app/js/vendors/jquery.min.js',
    ));
}
else {
    ?></div><?php
}
App::setCssFiles(array(
    '/app/css/vendors/font-awesome/css/font-awesome.min.css',
));

// RETURN
return;

//
// Code store for other projects
//
