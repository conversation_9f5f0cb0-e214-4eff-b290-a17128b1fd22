<?php /* @var $this Template */
// this view combine mini cart with roll down cart, that is displayed after onmouseover on mini cart

$products = $this->params['products'];
$productCount = count($this->params['products']);
$itemName =  __(__FILE__, 'items2');
if ($productCount == 1) {
    $itemName =  __(__FILE__, 'item');
} else if ($productCount > 1 && $productCount < 5) {
    $itemName =  __(__FILE__, 'items');
}
$active = '';
if ($this->params['active']) {
    $active = ' class="active"';
}
?>
<div id="smallCartContainer">
    <a href="<?php echo $this->params['urlCartView']; ?>" id="MiniCart" title="<?php echo __(__FILE__, 'Your shopping cart') ?>"<?php echo $active ?>>
        <img src="/app/img/cart-icon.png" alt="cart" class="shopping-cart">
        <span>
            <?php // echo __(__FILE__, 'Cart') ?>
            <?php echo $productCount.' '.$itemName.' '.__(__FILE__, 'for'); ?> 
            <strong><?php echo Eshop::formatPrice($this->params['totals']['products_price_to_pay']); ?></strong>
        </span>
    </a>
    <section class="cartSmall">
        <div class="cartTitle"><?php echo __(__FILE__, 'Shopping cart'); ?></div>
        <div class="shoppingtable_wrapper">
            <div class="table-margin">
                <table>
                    <tbody>
                        <?php if (empty($products)) echo 'Váš košík je prázdny.<br><br>'; ?>
                        <?php foreach ($products as $i => $product) : 
                            $urlShowDetail = url(array(
                                'locator' => $this->params['slugProductView'],
                                'args' => array($product['slug'])
                            ));
                            $urlRemoveFromCart =  url(array(
                                'module' => $this->module,
                                'controller' => 'EshopCarts',
                                'action' => 'removeProduct',
                                'args' => array($product['cartIndex'])
                            ));
                        ?>
                        <tr>
                            <td class="img"><img src="<?php echo $product['image']['small']; ?>" alt="" /></td>
                            <td class="title"><?php echo $product['amount']; ?>x <strong><a href="<?php echo $urlShowDetail; ?>"><?php echo $product['name'] ?></a></strong></td>
                            <td class="bin"><a href="<?php echo $urlRemoveFromCart; ?>"><?php // echo __(__FILE__, 'Remove'); ?></a></td>
                        </tr>
                        <tr>
                            <td class="altogether" colspan="3"><?php echo Eshop::formatPrice($product['price_actual_taxed'] * $product['amount']); ?></td>
                        </tr>
                        <tr>
                            <td colspan="3" class="desc"><?php echo $product['short_description']; ?><br>
                                <?php if (is_array($product['dynamic_attributes'])) foreach ($product['dynamic_attributes'] as $attrName => $attrValue) : ?>
                                <?php echo trim($attrName); ?>: <?php echo $attrValue; ?><br>
                                <?php endforeach; ?>
                            </td>
                        </tr>
                        <?php endforeach ?>
                    </tbody>
                </table>
            </div>
            <?php if (!empty($products)) : ?>
            <div class="shoppingtable_info wrapper">
                <p>
                    <span class="icon"></span>
                    <?php $freeShipmentProductsTotal = $this->getSetting('EshopShipment.freeShipmentProductsTotal'); ?>
                    <?php if ($freeShipmentProductsTotal <= $this->params['totals']['products_price_to_pay']) : ?>
                        <strong>Doručenie kuriérom a Zásielkovňou zdarma.</strong><br />
                    <?php else: ?>
                        Pri nákupe <strong>nad <?php echo $freeShipmentProductsTotal ?> &euro; doručenie kuriérom a Zásielkovňou zdarma.</strong><br />
                    <?php endif; ?>
                <p class="total"><?php echo __(__FILE__, 'Total price of the order'); ?>:<strong> <?php echo Eshop::formatPrice($this->params['totals']['products_price_to_pay']); ?></strong></p>
                <p>
                    Ceny sú vrátane DPH.
                </p>
            </div>
            <p class="shoppingtable_nav">
                <a class="cartnicebutton" href="<?php echo $this->params['urlCartView'] ?>"><span class="cartnicebutton"><?php echo __(__FILE__, 'Cart'); ?></span></a>
                <a class="progressnicebutton right" href="<?php echo $this->params['urlOrderCheckout'] ?>" class=""><span class="progressnicebutton"><?php echo __(__FILE__, 'Make order'); ?></span></a>
            </p>
            <?php endif; ?>
        </div>
    </section>
</div>
