<?php
$autonomous = !empty($this->params['autonomous']);
$allowSingleItemExport = !empty($this->params['allowSingleItemExport']);
App::setSeoTitle($this->params['title']);
echo Html::smartForm(array(
    'action' => App::getUrl(array_merge(App::$parsedUrl, array('args' => array(1)))),
    'title' => $this->params['title'],
    'buttonLabel' => $autonomous ? __a(__FILE__, 'Preniesť')  : __a(__FILE__, 'Export'),
    'closeButtonLabel' => false,
    'saveAndCloseButtonLabel' => false,
    'data' => $this->params['data'],
    'errors' => $this->params['errors'],
    'columns' => 6,
    'fields' => array(
        array('row'),
            array('if' => $allowSingleItemExport),
                array(
                    'field' => 'single_item_export',
                    'type' => 'checkbox',
                    'label' => __a(__FILE__, 'Preniesť len jednu položku'),
                    'hint' => __a(__FILE__, 'Má sa preniesť len prvá (najstaršia) neprenesená objednávka?')
                ),
                array('col'),
                    array(
                        'html' => '<div style="text-align:center;">' . __a(__FILE__, 'alebo') . '</div>'
                    ),
                array('/col'),
                array(
                    'field' => 'orderNumber',
                    'type' => 'number',
                    'label' =>  __a(__FILE__, 'Preniesť len objednávku číslo'),
                    'hint' => __a(__FILE__, 'Má sa preniesť len objednávka so zadaným číslom?')
                ),
            array('endif'),
        array('/row'),
        array('if' => !empty($this->params['progress'])),
            array('row', 'columns' => 1),
                array('col'),
                    array('html' => '<pre>' . print_r($this->params['progress'], true) . '</pre>'),
                array('/col'),
            array('/row'),
        array('endif'),
    ),
));
