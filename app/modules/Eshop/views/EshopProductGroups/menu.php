<?php /* @var $this Template */
$this->displayOriginComment = true;
$menuId = uniqid('product-groups-menu-');
echo Html::toggleButton('#' . $menuId, array(
    'class' => 'product-groups-menu-toggle-button',
    'label' => !empty($this->params['title']) ? $this->params['title'] : __(__FILE__, 'Product groups'),
));
?><nav id="<?php echo $menuId ?>" class="product-groups-menu"><?php 
    if (!($sideContentTabTitle = App::getGlobal('App', 'sideContentTabTitle'))) {
        App::setGlobal('App', 'sideContentTabTitle', true);
    }
    if (!empty($this->params['title'])) {
        if (!$sideContentTabTitle) {
            App::setGlobal('App', 'sideContentTabTitle', $this->params['title']);
        }
        else {
            ?><div class="title"><?php 
                echo $this->params['title'];
            ?></div><?php
        }
    }
    echo Html::menu($this->params['list'], array(
        'activeItem' => $this->params['activeItem'],
        'homeSlug' => HOME_SLUG,
        'lang' => URL_LANG,
        'urlBase' => $this->params['urlBase'],
        'SmartAdminLauncher' => $this->params['SmartAdminLauncher'],
    ));
?></nav><?php
