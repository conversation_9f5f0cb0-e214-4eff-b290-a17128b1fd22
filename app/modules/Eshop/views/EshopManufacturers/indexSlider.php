<?php /* @var $this Template */
$this->displayOriginComment = true;

$params = $this->params;
$uniqueId = uniqid('manufacturers-slider-');
if (!empty($params['manufacturers'])) {
    ?><section class="manufacturers-slider" id="<?php echo $uniqueId; ?>"><?php
        if (
            !empty($params['title'])
            || !empty($params['buttonText'])
        ) {
            ?><div class="intro"><?php
                if (!empty($params['title'])) {
                    ?><h1 class="heading"><?php
                        echo $params['title'];
                    ?></h1><?php
                }
                if (!empty($params['buttonText'])) {
                    ?><a class="text-button" href="<?php echo $params['indexLocator']; ?>"><?php
                        echo $params['buttonText'];
                    ?></a><?php
                }
            ?></div><?php
        }
        ?><div class="slider-wrapper"><?php
        $owlClass = '';
            if (sizeof($params['manufacturers']) > 1) {
                $owlClass = ' owl-carousel';
            }
            $slugManufacturerProducts = App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer');
            ?><div class="list<?php echo $owlClass ?>"><?php
            foreach ($params['manufacturers'] as $manufacturer) {
                ?><article class="item"<?php echo $this->params['SmartAdminLauncher']->markRecord($manufacturer['id']) ?>><?php
                    if (!empty($manufacturer['slug'])) {
                        $url = App::getUrl(array(
                            'locator' => $slugManufacturerProducts,
                            'args' => $manufacturer['slug']
                        ));
                        ?><a <?php 
                            ?>class="link" <?php 
                            ?>href="<?php echo App::getUrl($url) ?>" <?php 
                            if (Validate::externalUrl($manufacturer['slug'])) {
                                ?>target="_blank" <?php
                            }
                            ?>title="<?php echo $manufacturer['name'] ?>"<?php 
                        ?>><?php
                            ?><img class="image" src="<?php echo $manufacturer['image'] ?>" alt="<?php echo $manufacturer['name'] ?>"><?php
                        ?></a><?php
                    } 
                    else {
                        ?><div class="link" title="<?php echo $manufacturer['name'] ?>"><?php
                            ?><img class="image" src="<?php echo $manufacturer['image'] ?>" alt="<?php echo $manufacturer['name'] ?>"><?php
                        ?></div><?php
                    }
                ?></article><?php
            }
            ?></div><?php
            /*/
            ?><div class="dots"></div><?php
            //*/
         ?></div><?php
    ?></section><?php
}

if (sizeof($params['manufacturers']) > 1) {
    App::setCssFiles(array(
        '/app/css/vendors/owl.carousel.css',
    ));

    App::setJsFiles(array(
        '/app/js/vendors/jquery.min.js',
        '/app/js/vendors/owl.carousel.min.js',
    ));

    App::startJsCapture();
    ?><script type="text/javascript">
        jQuery(function(){
            var slider = $("#<?php echo $uniqueId; ?> .list");
            if ( slider.length ) {
                slider.owlCarousel({
                    stageElement: 'ul',
                    itemElement: 'li',
                    dots: false,
                    /*/
                    dotsContainer: '#<?php echo $uniqueId; ?> .dots',
                    dotsSpeed: 200,
                    //*/
                    items: 5,
                    autoplay: true,
                    autoplayTimeout: 5000,
                    autoplaySpeed: 1000,
                    autoplayHoverPause: true,
                    loop: true,
                    responsiveRefreshRate: 50,
                    responsive: {
                        0: {
                            items: 2
                        },
                        400: {
                            items: 3
                        },
                        540: {
                            items: 4
                        },
                        830: {
                            items: 6
                        },
                        1024: {
                            items: 7
                        }
                    },
                    singleItem: true
                });
            }
        });
    </script><?php
    App::endJsCapture();
}