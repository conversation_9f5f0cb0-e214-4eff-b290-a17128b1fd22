<?php 
if (
    empty($this->params['analyticsCode'])
    ||
    !App::$useJsEmbroidery && !$this->params['debug']
) {
    return;
}
$order = $this->params['order'];
if (!$this->params['debug']) {
    App::startJsCapture();
}
if ($this->params['api'] === 'analytics') { 
// ============= analytics.js API (NEW API) ============= ?>
<script type="text/javascript">
(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
})(window,document,'script','//www.google-analytics.com/analytics.js','ga');

ga('create', '<?php echo $this->params['analyticsCode'] ?>', 'auto');
ga('send', 'pageview');

ga('require', 'ecommerce');

ga('ecommerce:addTransaction', {
  'id': '<?php echo $order['number'] ?>',                                       // order ID - required
  'affiliation': '',                                                            // affiliation or store name
  'revenue': '<?php echo $order['order_price_to_pay'] ?>',   // total - required
  'shipping': '<?php echo $order['shipment_price_actual_taxless'] + $order['shipment_tax_actual'] + $order['payment_price_actual_taxless'] + $order['payment_tax_actual'] ?>', // shipping
  'tax': '<?php echo $order['order_tax_actual'] + $order['shipment_tax_actual'] + $order['payment_tax_actual'] ?>'                             // tax
});

<?php foreach ($this->params['products'] as $product) : ?>
ga('ecommerce:addItem', {
  'id': '<?php echo $order['number'] ?>',                                       // order ID - required
  'name': '<?php echo $product['name'] ?>',                                     // product name
  'sku': '<?php echo $product['id'] ?>',                                        // SKU/code - required - other alternatives: $product['code'] or $product['ean']
  'category': '',                                                               // category or variation
  'price': '<?php echo $product['price_actual_taxless'] + $product['tax_actual'] ?>', // unit price - required
  'quantity': '<?php echo $product['amount'] ?>'                                // quantity - required
});
<?php endforeach; ?>
  
ga('ecommerce:send');
</script>
<?php 
} 
else {
// ============= ga.js API (OLD API) ============= ?>
<script type="text/javascript">
var _gaq = _gaq || [];
_gaq.push(['_setAccount', '<?php echo $this->params['analyticsCode'] ?>']);
_gaq.push(['_trackPageview']);
_gaq.push(['_addTrans',
    '<?php echo $order['number'] ?>',               // order ID - required
    '',                                         // affiliation or store name
    '<?php echo $order['order_price_to_pay'] ?>',                 // total - required
    '<?php echo $order['order_tax_actual'] + $order['shipment_tax_actual'] + $order['payment_tax_actual'] ?>',                   // tax
    '<?php echo $order['shipment_price_actual_taxless'] + $order['shipment_tax_actual'] + $order['payment_price_actual_taxless'] + $order['payment_tax_actual'] ?>',              // shipping
    '',                                     // city
    '',                                     // state or province
    ''                                      // country
]);

<?php foreach ($this->params['products'] as $product) : ?>
_gaq.push(['_addItem',
    '<?php echo $order['number'] ?>',                                           // order ID - required
    '<?php echo $product['id'] ?>',                                             // SKU/code - required - other alternatives: $product['code'] or $product['ean']
    '<?php echo $product['name'] ?>',                                           // product name
    '',                                                                         // category or variation
    '<?php echo $product['price_actual_taxless'] + $product['tax_actual'] ?>',  // unit price - required
    '<?php echo $product['amount'] ?>'                                          // quantity - required
]);
<?php endforeach; ?>
  
_gaq.push(['_trackTrans']); //submits transaction to the Analytics servers

(function() {
    var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
})();
</script>
<?php 
}
if (!$this->params['debug']) {
    App::endJsCapture();
}
