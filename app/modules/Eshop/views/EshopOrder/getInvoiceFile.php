<?php
// pdf header/footer: 
//  - to make it work the 'header-html' option of wkhtmltopdf must be provided as URL (not html code, as possible for page)
//  - find 'header' on https://wkhtmltopdf.org/usage/wkhtmltopdf.txt (here you will
//      find also how arguments are sent to the header/footer html documents in get fashion,
//      see the js at the end of this file)
//  - https://stackoverflow.com/questions/12806702/how-can-i-use-footers-and-headers-with-wkhtmltopdf
$invoice = &$this->params['invoice'];
$eans = &$this->params['eans'];
$class = '';
if ($this->params['format'] === 'html') {
    $class = ' invoice-html-preview';
}
$part = Sanitize::value($this->params['part']);
$showHeader =  (!$part || $part === 'header');
$showBody =  (!$part || $part === 'body'); // = show body & footer
?><div class="invoice<?php echo $class ?>"><?php 
    ?><div class="content"><?php 
        if ($showHeader) {
            ?><div class="header"><?php 
                ?><div class="header-left"><?php 
                    ?><div class="supplier"><?php 
                        ?><div class="company-id"><?php 
                            ?>IČO: <b>36418404</b><?php
                        ?></div><?php
                        ?><div class="company-label">Dodávateľ:</div><?php
                        ?><div class="company">Vydavateľstvo Matice slovenskej, s. r. o.</div><?php
                        ?><div class="address"><?php 
                            ?>M. R. Štefánika 25/A<br><?php
                            ?>036 01 Martin<?php
                        ?></div><?php
                        ?><div class="tax-ids"><?php 
                            ?><span>DIČ: **********</span><?php
                            ?><span>IČ DPH: SK**********</span><?php
                        ?></div><?php
                        ?><div class="bank-accounts"><?php 
                            ?>IBAN/SWIFT: SK25 0900 0000 0003 ********/GIBASKBX<?php
                        ?></div><?php
                        ?><div class="registrator-info"><?php 
                            ?>zapísané v obch reg. Okresného súdu Žilina<br><?php
                            ?>odd. s. r. o. vl. č. 14931/L<?php
                        ?></div><?php
                    ?></div><?php
                    ?><div class="dates"><?php 
                        ?><b class="label">Dátum splatnosti:</b><?php 
                        ?><b><?php 
                            echo Date::format(@(string)$invoice->PaymentDueDate, 'd.m.Y') 
                        ?></b><br><?php
                        ?><span class="label">Dátum vyhotovenia:</span><?php 
                            echo Date::format(@(string)$invoice->IssueDate, 'd.m.Y') 
                        ?><br><?php
                        ?><span class="label">Daňová povinnosť:</span><?php 
                            echo Date::format(@(string)$invoice->TaxPointDate, 'd.m.Y') 
                        ?><br><?php
                    ?></div><?php
                ?></div><?php
                ?><div class="header-right"><?php 
                    ?><div class="numbers"><?php 
                        ?><div class="page-number"><?php 
                            ?>Strana <span class="page"></span> z <span class="topage"></span><?php
                        ?></div><?php
                        ?><div class="invoice-number"><?php 
                            ?><span>FAKTÚRA</span> č. <?php echo @(string)$invoice->DocumentNumber
                        ?></div><?php
                        ?><div class="order-number"><?php 
                            ?>Objednávka č. <?php echo ltrim(@(string)$invoice->OrderNumber, '0')
                        ?></div><?php
                    ?></div><?php
                    ?><div class="customer"><?php 
                        $companyId = ltrim((string)$invoice->Company->CompanyId, '0');
                        if (Validate::intNumber(substr($companyId, 0, 1))) {                        
                            ?><div class="company-id"><?php 
                                ?>IČO: <b><?php echo $companyId ?></b><?php
                            ?></div><?php
                        }
                        ?><div class="company-label">Odberateľ:</div><?php
                        ?><div class="company"><?php 
                            echo (string)$invoice->Company->Name
                        ?></div><?php
                        ?><div class="address"><?php 
                            echo @(string)$invoice->Company->Street ?><br><?php
                            echo @(string)$invoice->Company->ZipCode . ' ' . @(string)$invoice->Company->City
                        ?></div><?php
                        ?><div class="tax-ids"><?php 
                            ?><span>DIČ: <?php echo @(string)$invoice->Company->VatNumber ?></span><?php
                            ?><span>IČ DPH: <?php echo @(string)$invoice->Company->VatNumberSK ?></span><?php
                        ?></div><?php
                        ?><div class="payment-method"><?php 
                            ?>Forma úhrady: <?php echo @(string)$invoice->PaymentMeansCode ?><?php
                        ?></div><?php
                    ?></div><?php
                ?></div><?php
                ?><div class="items-header"><?php 
                    ?><div class="name"><?php 
                        ?>Fakturujeme Vám<?php
                    ?></div><?php
                    ?><div class="amount"><?php 
                        ?>Počet<?php
                    ?></div><?php
                    ?><div class="unit-price"><?php 
                        ?>Cena MJ<?php
                    ?></div><?php
                    ?><div class="vat"><?php 
                        ?>%DPH<?php
                    ?></div><?php
                    ?><div class="discount"><?php 
                        ?>Zľava<?php
                    ?></div><?php
                    ?><div class="price"><?php 
                        ?>Celkom<?php
                    ?></div><?php
                ?></div><?php
            ?></div><?php
        }
        if ($showBody) {
            ?><div class="body"><?php 
                ?><div class="invoice-items"><?php 
                    $totalAmount = 0.0;
                    $totalDiscountTaxed = 0.0;
                    $totalPriceTaxed = 0.0;
                    $items = @(array)$invoice->Items->Item;
                    // XML is parsed a bit strangelly, if there is 2 and more <Item> tags 
                    // under tag <Items> then its ok. If There is just one <Item> tag then its
                    // fields are directly keays of $items. Normalize it in following way:
                    if (Validate::assocArray($items)) {
                        $items = array($invoice->Items->Item);
                    }
                    foreach ($items as $item) {
                        ?><div class="item"><?php 
                            ?><div class="ean"><?php 
                                $code = Number::removeTrailingZeroDecimals(@(string)$item->StockCardNumber);
                                if (!empty($eans[$code])) {
                                    echo $eans[$code];
                                }
                            ?></div><?php
                            ?><div class="name"><?php 
                                echo @(string)$item->Description
                            ?></div><?php
                            ?><div class="amount"><?php 
                                $amount = (float)Number::removeTrailingZeroDecimals(@(string)$item->Quantity);
                                echo str_replace('.', ',', $amount);
                                $totalAmount += $amount;
                            ?></div><?php
                            ?><div class="unit-price"><?php 
                                $unitPrice = (float)Number::removeTrailingZeroDecimals(@(string)$item->UnitPrice);
                                echo str_replace('.', ',', $unitPrice);
                            ?></div><?php
                            ?><div class="vat"><?php 
                                $taxPercent = Number::removeTrailingZeroDecimals(@(string)$item->TaxPercent);
                                echo str_replace('.', ',', $taxPercent);
                                ?>%<?php
                            ?></div><?php
                            ?><div class="discount"><?php 
                                $discountPercent = Number::removeTrailingZeroDecimals(@(string)$item->DiscountPercent);
                                if ($discountPercent) {
                                    echo str_replace('.', ',', $discountPercent);
                                    ?>%<?php
                                }
                            ?></div><?php
                            ?><div class="price"><?php 
                                $unitPriceTaxed = 
                                    round(
                                        round($unitPrice, 3) * (100 + $taxPercent) / 100,
                                        3
                                    );
                                $actualUnitPriceTaxed = round(
                                    $unitPriceTaxed * (100 - $discountPercent) / 100,
                                    3
                                );
                                $actualPriceTaxed = $actualUnitPriceTaxed * $amount;
                                echo App::formatNumber($actualPriceTaxed);
                                $unitDiscountTaxed = $unitPriceTaxed - $actualUnitPriceTaxed;
                                $totalDiscountTaxed += $unitDiscountTaxed * $amount;
                                $totalPriceTaxed += $actualPriceTaxed;
                            ?></div><?php
                        ?></div><?php
                    }
                ?></div><?php
                ?><div class="items-totals"><?php 
                    ?><div class="amount"><?php 
                        echo str_replace('.', ',', $totalAmount)
                    ?></div><?php
                    ?><div class="savings"><?php 
                        if (!empty($totalDiscountTaxed)) {                        
                            ?><span>Zľava za položky celkom</span><?php
                            echo App::formatNumber($totalDiscountTaxed);
                        }
                    ?></div><?php
                    ?><div class="price"><?php 
                        ?>Celkom EUR<?php
                        ?><span><?php 
                            echo App::formatNumber($totalPriceTaxed)
                        ?></span><?php
                    ?></div><?php
                ?></div><?php
                ?><div class="vat-info"><?php
                    ?><div class="header"><?php 
                        ?><div class="percentage"><?php 
                            ?>Rozpis DPH<?php
                        ?></div><?php
                        ?><div class="base"><?php 
                            ?>Základ DPH<?php
                        ?></div><?php
                        ?><div class="vat"><?php 
                            ?>DPH<?php
                        ?></div><?php
                        ?><div class="price"><?php 
                            ?>Celkom<?php
                        ?></div><?php
                    ?></div><?php
                    ?><div class="items"><?php 
                        $baseTaxless = $baseTax = 0.0;
                        if (@(float)$invoice->BaseTaxRateAmount) {
                            ?><div class="item"><?php 
                                ?><div class="percentage"><?php 
                                    ?>V sadzbe 20%<?php
                                ?></div><?php
                                ?><div class="base"><?php 
                                    $baseTaxless = @(float)$invoice->BaseTaxRateAmount;
                                    echo str_replace('.', ',', $baseTaxless);
                                ?></div><?php
                                ?><div class="vat"><?php 
                                    $baseTax = @(float)$invoice->BaseTaxRateTax;
                                    echo str_replace('.', ',', $baseTax);
                                ?></div><?php
                                ?><div class="price"><?php 
                                    echo str_replace('.', ',', $baseTaxless + $baseTax);
                                ?></div><?php
                            ?></div><?php
                        }
                        $reducedTaxless = $reducedTax = 0.0;
                        if (@(float)$invoice->ReducedTaxRateAmount) {
                            ?><div class="item"><?php 
                                ?><div class="percentage"><?php 
                                    ?>V sadzbe 10%<?php
                                ?></div><?php
                                ?><div class="base"><?php 
                                    $reducedTaxless = @(float)$invoice->ReducedTaxRateAmount;
                                    echo str_replace('.', ',', $reducedTaxless);
                                ?></div><?php
                                ?><div class="vat"><?php 
                                    $reducedTax = @(float)$invoice->ReducedTaxRateTax;
                                    echo str_replace('.', ',', $reducedTax);
                                ?></div><?php
                                ?><div class="price"><?php 
                                    echo str_replace('.', ',', $reducedTaxless + $reducedTax);
                                ?></div><?php
                            ?></div><?php
                        }
                    ?></div><?php
                    ?><div class="totals"><?php 
                        ?><div class="percentage"><?php 
                            ?>Súčet<?php
                        ?></div><?php
                        ?><div class="base"><?php 
                            echo str_replace('.', ',', $baseTaxless + $reducedTaxless);
                        ?></div><?php
                        ?><div class="vat"><?php 
                            echo str_replace('.', ',', $baseTax + $reducedTax);
                        ?></div><?php
                        ?><div class="price"><?php 
                            echo str_replace('.', ',', $baseTaxless + $reducedTaxless + $baseTax + $reducedTax);
                        ?></div><?php
                    ?></div><?php
                ?></div><?php
                $src = '/app/modules/Eshop/img/stamp.png';
                if ($this->params['local']) {
                    $src = 'file://' . File::normalizeDS(ROOT . $src);
                }
                ?><img class="stamp" src="<?php echo $src ?>"><?php
            ?></div><?php
            ?><div class="footer"><?php 
                ?><div class="issued-by"><?php 
                    ?>Vystavil: Zjavková<?php
                ?></div><?php
                ?><div class="email"><?php 
                    ?>E-mail: <EMAIL><?php
                ?></div><?php
                ?><div class="phone"><?php 
                    ?>Telefón: 043/32 40 557<?php
                ?></div><?php
                ?><div class="info"><?php 
                    ?>Faktúra slúži ako dodací list<?php
                ?></div><?php
            ?></div><?php
        }
    ?></div><?php
?></div><?php
?><script type="text/javascript">
function preparePage() {
    var params = {}, queryStrings = document.location.search.substring(1).split('&'),
        i, parts, classes, element, j;
    for (i in queryStrings) {
        if (queryStrings.hasOwnProperty(i)) {
            parts = queryStrings[i].split('=', 2);
            params[parts[0]] = decodeURI(parts[1]);
        }
    }
    classes = ['page', 'topage'];
    for (i in classes) {
        if (classes.hasOwnProperty(i)) {
            element = document.getElementsByClassName(classes[i]);
            for (j = 0; j < element.length; ++j) {
                element[j].textContent = params[classes[i]];
            }
        }
    }
    if (params['page'] > 1) {
        element = document.getElementsByClassName('header');
        for (j = 0; j < element.length; ++j) {
            element[j].classList.add('small');
        }
    }
}
window.addEventListener('load', function() {
    preparePage();
});
</script><?php
$cssFiles = array(
    '/app/modules/Eshop/css/invoice.css',
);
/*
?><pre><?php 
    print_r($invoice);
?></pre><?php
*/

