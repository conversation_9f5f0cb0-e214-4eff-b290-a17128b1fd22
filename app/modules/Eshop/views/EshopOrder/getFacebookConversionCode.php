<?php /* @var $this Template *//** @var Template $this */
$this->displayOriginComment = true;
$order = $this->params['order'];
if (!$this->params['debug']) {
    App::startJsCapture();
}
?><script type="text/javascript">
fbq('track', 'Purchase', {
    currency: "EUR", 
    value: <?php echo $order['order_price_to_pay'] ?>,
    content_type: "product",
    contents: <?php 
        $contents = array();
        foreach ($this->params['products'] as $product){
            $contents[] = array(
                'id' => $product['id'],
                'quantity' => $product['amount'],
            );
        }
        echo json_encode($contents);
    ?>
    
});
</script><?php 
if (!$this->params['debug']) {
    App::endJsCapture(array(
        'permanent' => true,
    ));
}