<?php 
if (
    empty($this->params['heurekaKey'])
    ||
    !App::$useJsEmbroidery && !$this->params['debug']
) {
    return;
}
$order = $this->params['order'];
if (!$this->params['debug']) {
    App::startJsCapture();
}
?>
<script type="text/javascript">
var _hrq = _hrq || [];
_hrq.push(['setKey', '<?php echo $this->params['heurekaKey'] ?>']);
_hrq.push(['setOrderId', '<?php echo $order['number'] ?>']);

<?php foreach ($this->params['products'] as $product) { ?>
_hrq.push(['addProduct',
    '<?php echo $product['name'] ?>',
    '<?php echo $product['price_actual_taxless'] + $product['tax_actual'] ?>',
    '<?php echo $product['amount'] ?>'
]);
<?php } ?>

_hrq.push(['trackOrder']);

(function() {
    var ho = document.createElement('script'); ho.type = 'text/javascript'; ho.async = true;
    ho.src = 'https://im9.cz/sk/js/ext/2-roi-async.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ho, s);
})();
</script>
<?php 
if (!$this->params['debug']) {
    App::endJsCapture();
}
