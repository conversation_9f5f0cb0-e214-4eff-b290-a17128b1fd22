<?php
/**
 * Dokumentácia: 
 *   - http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#IMPEO0
 *   - http://www.mrp.sk/index.php/autonomnyr.html#IMPEO0
 * 
 * Skratky v dokumentácii: DM = desatinné miesta, O/M = ???
 * Dĺžka hodnôť tagov a parametrov - je potrebné jú orezať podľa dokumentácie.
 * V atribútpch môžu byť aj mäkčene a dĺžne.
 * 
 * <objednavka>
 *      - 'puvodniCislo' slúži na zadanie číslo objednávky (CISOBJ), variabilniSymbol 
 *      je osobitný údaj.
 *      - 'cenySDPH' môže nadobúdať hodnoty 'T' a 'F' (true/false)
 * 
 * <polozky>
 *      - Poštovné je možné zadať ako poslednú položku
 * 
 *      <polozka>
 *          - Položka zadaná bez 'cisloKarty' je len textova položka (bez náväznosti na sklad)
 * 
 * <poznámka>
 *      - Môže mať ľubovolnú dĺžku
 * 
 * <adresa>, <adresa_dod>
 *      - 'id' slúži na spárovanie adresy už s existujúcou adresou v IS. Toto párovanie
 *      však nefunguje na viacero rovnakých, nových (v IS neexistujúcich) adries v tom istom 
 *      importe. Takéto adresy je potrebné zadať každú s novým id. Ak sa adresa s tým istým
 *      'id' po čase na eshope zmení, tak aj po úspešnom párovaní sa v IS neopravuje (t.j. 
 *      ostane tam jej prvá naimportovaná verzia). Ak sa 'id' nezadá tak si ho vygeneruje
 *      IS a toto je možno najlepší prístup. Na podrobné vysvetlenia procesu párovania
 *      adries pozri http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#IMPEO0
 * 
 *      <osoba>
 *          - ide vyslovene len o kontaktnú osobu (aj v prípade fakturačnej adresy) a
 *          preto je potrebné uviesť meno osoby ako firmu bez IČO.
 * 
 *      <firma>
 *          - 'ico' ak sa zadajú dve rozne adresy s tým istým 'ico' tak sa zoberie ta prvá.
 *          'ico' vlastne funguje ako 'id' - najprv sa kontroluje 'id' a potom 'ico' (ak je zadané)
 * 
 * @param 'order' (array)
 * @param 'orderProducts' (array)
 */
if (empty($this->params['order'])) {
    throw new Exception(__e(__FILE__, 'Missing view param order'));
}
if (empty($this->params['orderProducts'])) {
    throw new Exception(__e(__FILE__, 'Missing view param orderProducts'));
}
$order = $this->params['order'];
$orderProducts = $this->params['orderProducts'];
// are prices taxed or taxless?
$taxedPrices = $this->getSetting('pricesAreTaxed');
if ($taxedPrices) {
    $taxedFlag = 'T';
    $priceField = 'price_actual_taxed';
}
else {
    $taxedFlag = 'F';
    $priceField = 'price_actual_taxless';    
}
// function to set exact lengths of fields
$s = function($string, $length = null) {
    $string = Sanitize::invalidXmlChars($string);
    // ensure that encode string is shorter than required length
    if (
        !empty($length) 
        && Validate::intNumber($length)
    ) {
        $tmpLength = $length;
        do {
            $tmpString = mb_substr($string, 0, $tmpLength--, 'UTF-8');
            $tmpString = htmlspecialchars($tmpString, ENT_XML1 | ENT_QUOTES, 'UTF-8');
        }
        while(mb_strlen($tmpString, 'UTF-8') > $length);
        $string = $tmpString;
    }
    else {
        $string = htmlspecialchars($string, ENT_XML1 | ENT_QUOTES, 'UTF-8');
    }
    return $string;
};
// ATTENTION:
// "$s($order['company_vat_number'], 17)" - the length 17 is just coppied from "dic", "ic_dph" is not documented
?><objednavka <?php 
    ?>formaUhrady="<?php echo $s($order['mrp_payment_method'], 10) ?>" <?php 
    ?>puvodniCislo="<?php echo $s($order['number'], 50) ?>" <?php 
    ?>datum="<?php echo $s($order['created'], 10) ?>" <?php 
    ?>cenySDPH="<?php echo $taxedFlag ?>" <?php 
    ?>zpusobDopravy="<?php echo $s($order['mrp_shipment_method'], 10) ?>"<?php
?>><?php
    ?><mena kod="EUR" kurz="1" mnozstvi="1"/><?php
    ?><adresa <?php 
        ?>id="<?php echo $s($order['address_id'], 10) ?>" <?php 
        ?>ulice="<?php echo $s($order['street'], 30) ?>" <?php 
        ?>mesto="<?php echo $s($order['city'], 30) ?>" <?php 
        ?>psc="<?php echo $s($order['zip'], 15) ?>"<?php 
    ?>><?php
        ?><firma <?php 
            ?>nazev="<?php echo $s($order['company_fullname'], 100) ?>" <?php 
            ?>ico="<?php echo $s($order['company_id_number'], 12) ?>" <?php 
            ?>dic="<?php echo $s($order['company_tax_number'], 17) ?>" <?php 
            ?>ic_dph="<?php echo $s($order['company_vat_number'], 14) ?>"<?php 
        ?>/><?php
        ?><osoba <?php 
            ?>jmeno="<?php echo $s($order['firstname'], 30) ?>" <?php 
            ?>prijmeni="<?php echo $s($order['lastname'], 30) ?>"<?php 
        ?>/><?php
        ?><email><?php echo $s($order['email'], 256) ?></email><?php
        ?><tel><?php echo $s($order['phone'], 30) ?></tel><?php
    ?></adresa><?php
    ?><adresa_dod <?php 
        ?>id="<?php echo $s($order['delivery_address_id'], 10) ?>" <?php 
        ?>ulice="<?php echo $s($order['delivery_street'], 30) ?>" <?php 
        ?>mesto="<?php echo $s($order['delivery_city'], 30) ?>" <?php 
        ?>psc="<?php echo $s($order['delivery_zip'], 15) ?>"<?php 
    ?>><?php
        ?><firma <?php 
            ?>nazev="<?php echo $s($order['delivery_company_fullname'], 100) ?>" <?php 
            ?>ico="" <?php 
            // on client request: put here id of pickup place (local pickup, Zasielkovna, GEIS point, ...)
            ?>dic="<?php echo $s($order['pickup_place_id'], 14) ?>"<?php 
        ?>/><?php
        ?><osoba <?php 
            ?>jmeno="<?php echo $s($order['delivery_firstname'], 30) ?>" <?php 
            ?>prijmeni="<?php echo $s($order['delivery_lastname'], 30) ?>"<?php 
        ?>/><?php
        ?><email><?php echo $s($order['delivery_email'], 256) ?></email><?php
        ?><tel><?php echo $s($order['delivery_phone'], 30) ?></tel><?php
    ?></adresa_dod><?php
    ?><polozky><?php
        foreach ($orderProducts as $product) {
            if (!empty($product['mrp_code'])) { 
                ?><polozka <?php 
                    ?>cisloKarty="<?php echo $s($product['mrp_code'], 10) ?>" <?php
                    /*/
                    ?>kodKarty="<?php echo $s($product['code'], 30) ?>" <?php
                    /*/
                    ?>text="" <?php 
                    ?>cenaMJ="<?php echo $product[$priceField] ?>" <?php 
                    ?>pocetMJ="<?php echo $product['amount'] ?>" <?php 
                    ?>sazbaDPH="<?php echo $product['tax_rate'] ?>"<?php 
                ?>/><?php
            /*/
            } elseif (!empty($product['name'])) {
                ?><polozka <?php 
                    ?>cisloKarty="" <?php 
                    ?>text="<?php echo $s($product['name'], 50) ?>" <?php 
                    ?>cenaMJ="<?php echo $product[$priceField] ?>" <?php 
                    ?>pocetMJ="<?php echo $product['amount'] ?>" <?php 
                    ?>sazbaDPH="<?php echo $product['tax_rate'] ?>"<?php 
                ?>/><?php
            /*/
            } else { 
                $message = __e(__FILE__, 'Invalid order product data - without both "code" and "name".');
                App::logError($message, array(
                    'var' => array(
                        'order' => $order,
                        'product' => $product,
                    ),
                    'email' => true,
                ));
                throw new Exception($message);
            }
        } 
    ?></polozky><?php
    ?><poznamka><?php echo $s($order['comment']) ?></poznamka><?php
?></objednavka><?php
