<?php echo '<?xml version="1.0" encoding="UTF-8"?>';
$record = $this->params['record'];
$taxedPrices = $this->getSetting('pricesAreTaxed');
?>

<dat:dataPack <?php 
    ?>id="inv<?php echo $this->params['dataPackId'] ?>" <?php 
    ?>ico="<?php echo $this->params['cid'] ?>" <?php 
    ?>application="<?php echo App::getSetting('App', 'name') ?>" <?php 
    ?>version = "2.0" <?php 
    ?>note="Import FA" <?php 
    ?>xmlns:dat="http://www.stormware.cz/schema/version_2/data.xsd" <?php 
    ?> xmlns:inv="http://www.stormware.cz/schema/version_2/invoice.xsd" <?php 
    ?>xmlns:typ="http://www.stormware.cz/schema/version_2/type.xsd"<?php 
?>>
    
    <dat:dataPackItem id="INV<?php echo $record['id'] ?>" version="2.0">
        <inv:invoice version="2.0">
        
            <inv:invoiceHeader>
                <inv:invoiceType>issuedInvoice</inv:invoiceType>
                <inv:symVar><?php echo $record['number'] ?></inv:symVar>
                <inv:number>
                    <typ:numberRequested><?php echo $record['number'] ?></typ:numberRequested>
                </inv:number>
                <inv:date><?php echo date('Y-m-d', strtotime($record['created'])) ?></inv:date>
                <inv:dateDue><?php echo date('Y-m-d', strtotime($record['created']) + 1209600) ?></inv:dateDue>
                <inv:classificationVAT>
                    <typ:classificationVATType>inland</typ:classificationVATType>
                </inv:classificationVAT>
                <inv:text>Faktúra za tovar</inv:text>
                <inv:partnerIdentity>
                    <typ:address>
                        <?php if ($record['company_name']) : ?>
                        <typ:company><![CDATA[<?php echo $record['company_fullname'] ?>]]></typ:company>
                        <?php endif; ?>
                        <typ:name><![CDATA[<?php echo $record['fullname'] ?>]]></typ:name>
                        <typ:city><![CDATA[<?php echo $record['city'] ?>]]></typ:city>
                        <typ:street><![CDATA[<?php echo $record['street'] ?>]]></typ:street>
                        <typ:zip><![CDATA[<?php echo $record['zip'] ?>]]></typ:zip>
                        <?php if ($record['company_id_number']) : ?>
                        <typ:ico><![CDATA[<?php echo $record['company_id_number'] ?>]]></typ:ico>
                        <?php endif; ?>
                        <?php if ($record['company_tax_number']) : ?>
                        <typ:dic><![CDATA[<?php echo $record['company_tax_number'] ?>]]></typ:dic>
                        <?php endif; ?>
                        <?php 
//                        <typ:division></typ:division>
//                        <typ:icDph></typ:icDph>
                        ?>
                        <?php if ($record['customer_email']) : ?>
                        <typ:email><![CDATA[<?php echo $record['customer_email'] ?>]]></typ:email>
                        <?php endif; ?>
                    </typ:address>
                </inv:partnerIdentity>
                <inv:dateOrder><?php echo date('Y-m-d', strtotime($record['created'])) ?></inv:dateOrder>
                <?php 
//                <inv:paymentType><typ:ids></typ:ids></inv:paymentType>
                ?>
                <inv:note><![CDATA[<?php echo $record['comment'] ?>]]></inv:note>
                <inv:intNote>Načítané z XML, id dokladu vo webovej tabulke: <?php echo $record['id'] ?></inv:intNote>
                <?php 
//                <inv:centre><typ:ids></typ:ids></inv:centre>
//                <inv:activity><typ:ids></typ:ids></inv:activity>
                ?>
                <inv:contract>
                    <typ:ids><?php echo $record['id'] ?></typ:ids>
                </inv:contract>     
                <inv:accounting>
                    <typ:ids>tovar</typ:ids>
                </inv:accounting>                
            </inv:invoiceHeader>    
          
            <inv:invoiceDetail>
<?php foreach ($record['items'] as $item) : ?>
                <inv:invoiceItem>
                    <inv:text><![CDATA[<?php echo $item['name'] ?>]]></inv:text>
                    <inv:quantity><?php echo $item['amount'] ?></inv:quantity>
                    <inv:unit>ks</inv:unit>
                    <inv:rateVAT><?php echo $item['tax_rate'] == $this->params['defaultTaxRate'] ? 'high' : 'low' ?></inv:rateVAT>
                    <inv:discountPercentage><?php echo $item['discount_rate'] ?></inv:discountPercentage>
                    <inv:payVAT><?php echo $taxedPrices ? 'true' : 'false' ?></inv:payVAT>
                    <inv:homeCurrency>
                        <typ:unitPrice><?php 
                            if ($taxedPrices) {
                                echo $item['price_taxless'] + $item['tax'];
                            }
                            else {
                                echo $item['price_taxless'];
                            }
                        ?></typ:unitPrice>
                    </inv:homeCurrency>
					<inv:stockItem>
						<typ:stockItem>
							<typ:ids><?php echo $item['id'] ?></typ:ids>
						</typ:stockItem>
					</inv:stockItem>
                    <inv:note><![CDATA[<?php echo $item['static_attributes'] . $item['dynamic_attributes'] ?>]]></inv:note>
                </inv:invoiceItem>
<?php endforeach ?>
                <?php // add shipment and payment prices ?>
                <inv:orderItem>
                    <inv:text><![CDATA[Poštovné]]></inv:text>
                    <inv:quantity>1</inv:quantity>
                    <inv:unit>ks</inv:unit>
                    <inv:rateVAT>high</inv:rateVAT>
                    <inv:payVAT><?php echo $taxedPrices ? 'true' : 'false' ?></inv:payVAT>
                    <inv:homeCurrency>
                        <typ:unitPrice><?php 
                            if ($taxedPrices) {
                                echo $record['shipment_price_actual_taxless'] + $record['shipment_tax_actual'] +
                                    $record['payment_price_actual_taxless'] + $record['payment_tax_actual'];
                            }
                            else {
                                echo $record['shipment_price_actual_taxless'] + $record['payment_price_actual_taxless'];
                            }
                        ?></typ:unitPrice>
                    </inv:homeCurrency>
                </inv:orderItem>
            </inv:invoiceDetail> 
            
        </inv:invoice>
    </dat:dataPackItem>
</dat:dataPack>