<?php echo '<?xml version="1.0" encoding="UTF-8"?>';
$record = $this->params['record'];
$taxedPrices = $this->getSetting('pricesAreTaxed');
?>

<dat:dataPack <?php 
    ?>id="ord<?php echo $this->params['dataPackId'] ?>" <?php 
    ?>ico="<?php echo $this->params['cid'] ?>" <?php 
    ?>application="<?php echo App::getSetting('App', 'name') ?>" <?php 
    ?>version = "2.0" <?php 
    ?>note="Import Objednávky" <?php 
    ?>xmlns:dat="http://www.stormware.cz/schema/version_2/data.xsd" <?php 
    ?>xmlns:ord="http://www.stormware.cz/schema/version_2/order.xsd" <?php 
    ?>xmlns:typ="http://www.stormware.cz/schema/version_2/type.xsd"<?php 
?>>
    
    <dat:dataPackItem id="ORD<?php echo $record['id'] ?>" version="2.0">
        <ord:order version="2.0">
        
            <ord:orderHeader>
                <ord:orderType>receivedOrder</ord:orderType>
                <ord:numberOrder><?php echo $record['number'] ?></ord:numberOrder>
                <ord:date><?php echo date('Y-m-d', strtotime($record['created'])) ?></ord:date>
                <?php 
//                <ord:dateDelivery></ord:dateDelivery>
//                <ord:dateFrom></ord:dateFrom>
//                <ord:dateTo></ord:dateTo>
//                <ord:text><![CDATA[< ?php echo mb_substr($record['comment'], 0, 240, 'UTF-8') ? >]]></ord:text>
                ?>
                <ord:partnerIdentity>
                    <typ:address>
                        <?php if ($record['company_name']) : ?>
                        <typ:company><![CDATA[<?php echo $record['company_fullname'] ?>]]></typ:company>
                        <?php endif; ?>
                        <typ:name><![CDATA[<?php echo $record['fullname'] ?>]]></typ:name>
                        <typ:city><![CDATA[<?php echo $record['city'] ?>]]></typ:city>
                        <typ:street><![CDATA[<?php echo $record['street'] ?>]]></typ:street>
                        <typ:zip><![CDATA[<?php echo $record['zip'] ?>]]></typ:zip>
                        <?php if ($record['company_id_number']) : ?>
                        <typ:ico><![CDATA[<?php echo $record['company_id_number'] ?>]]></typ:ico>
                        <?php endif; ?>
                        <?php if ($record['company_tax_number']) : ?>
                        <typ:dic><![CDATA[<?php echo $record['company_tax_number'] ?>]]></typ:dic>
                        <?php endif; ?>
                        <?php 
//                        <typ:division></typ:division>
//                        <typ:icDph></typ:icDph>
                        ?>
                        <?php if ($record['customer_email']) : ?>
                        <typ:email><![CDATA[<?php echo $record['customer_email'] ?>]]></typ:email>
                        <?php endif; ?>
                    </typ:address>
                </ord:partnerIdentity>
                <?php 
//                <ord:paymentType><typ:ids></typ:ids></ord:paymentType>
//                <ord:note><![CDATA[]]></ord:note>
                ?>
                <ord:intNote><![CDATA[<?php echo $record['comment'] ?>]]></ord:intNote>            
            </ord:orderHeader>    
            
            <ord:orderDetail>
<?php foreach ($record['items'] as $item) : ?>
                <ord:orderItem>
                    <ord:text><![CDATA[<?php echo mb_substr($item['name'], 0, 90, 'UTF-8') ?>]]></ord:text>
                    <ord:quantity><?php echo $item['amount'] ?></ord:quantity>
                    <ord:unit>ks</ord:unit>
                    <?php 
//                    <ord:delivered></ord:delivered>
                    ?>
                    <ord:rateVAT><?php echo $item['tax_rate'] == $this->params['defaultTaxRate'] ? 'high' : 'low' ?></ord:rateVAT>
                    <ord:discountPercentage><?php echo $item['discount_rate'] ?></ord:discountPercentage>
                    <ord:payVAT><?php echo $taxedPrices ? 'true' : 'false' ?></ord:payVAT>
                    <ord:homeCurrency>
                        <typ:unitPrice><?php 
                            if ($taxedPrices) {
                                echo $item['price_taxless'] + $item['tax'];
                            }
                            else {
                                echo $item['price_taxless'];
                            }
                        ?></typ:unitPrice>
                    </ord:homeCurrency>
					<ord:stockItem>
						<typ:stockItem>
							<typ:ids><?php echo $item['id'] ?></typ:ids>
						</typ:stockItem>
					</ord:stockItem>
                    <ord:note><![CDATA[<?php echo mb_substr($item['static_attributes'] . $item['dynamic_attributes'], 0, 90, 'UTF-8'); ?>]]></ord:note>
                </ord:orderItem>
<?php endforeach ?>
                <?php // add shipment and payment prices ?>
                <ord:orderItem>
                    <ord:text><![CDATA[Poštovné]]></ord:text>
                    <ord:quantity>1</ord:quantity>
                    <ord:unit>ks</ord:unit>
                    <ord:rateVAT>high</ord:rateVAT>
                    <ord:payVAT><?php echo $taxedPrices ? 'true' : 'false' ?></ord:payVAT>
                    <ord:homeCurrency>
                        <typ:unitPrice><?php 
                            if ($taxedPrices) {
                                echo $record['shipment_price_actual_taxless'] + $record['shipment_tax_actual'] +
                                    $record['payment_price_actual_taxless'] + $record['payment_tax_actual'];
                            }
                            else {
                                echo $record['shipment_price_actual_taxless'] + $record['payment_price_actual_taxless'];
                            }
                        ?></typ:unitPrice>
                    </ord:homeCurrency>
                </ord:orderItem>
            </ord:orderDetail> 
            
        </ord:order>
    </dat:dataPackItem>
</dat:dataPack>