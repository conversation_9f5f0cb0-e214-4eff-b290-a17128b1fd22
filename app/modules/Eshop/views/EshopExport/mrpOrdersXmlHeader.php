<?php
/**
 * Dokumentácia: 
 *   - http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#IMPEO0
 *   - http://www.mrp.sk/index.php/autonomnyr.html#IMPEO0
 * 
 * <request>
 *      - 'requestId' - Slúži na to, aby sa ten istý request nezapísal 2x. Ak napríklad 
 *      vypadne spojenie a nevieme či sa požiadavku podarilo zapísať alebo nie tak
 *      stačí ten istý request (s tým istým requestId) poslať ešte raz a systém 
 *      buď zistí, že už ho vykonal a ignoruje ho alebo že ho ešte nevykonal a
 *      vykoná ho.
 * 
 * <params> 
 *      - Ak to chceš nechať na prednastavených hodnotách systému, tak je možné tag 
 *      params úplne vynechať.
 * 
 * @param 'requestId' (string) See the above explanation or the documentation
 */
if (empty($this->params['requestId'])) {
    throw new Exception(__e(__FILE__, 'Missing view param requestId'));
}
echo '<?xml version="1.0" encoding="utf-8"?>' . PHP_EOL
?><mrpEnvelope>
    <body>
        <mrpRequest>
            <request command="IMPEO0" requestId="<?php echo $this->params['requestId'] ?>"> 
            </request>
            <data>
<?php /* ?>                
                <params>
                    <paramvalue name="cisloSkladu">1</paramvalue>
                    <paramvalue name="stredisko">0</paramvalue>
                    <paramvalue name="cisloZakazky">0</paramvalue>
                    <!-- paramvalue name="prefixRadyObj">EO001</paramvalue> -->
                </params>
<?php */
