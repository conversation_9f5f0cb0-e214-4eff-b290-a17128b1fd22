<?php 
// header (dislayed conditionally !!!)
if (empty($this->params['part']) ||  $this->params['part'] == 'header') :
echo '<?xml version="1.0" encoding="UTF-8"?>'; ?>
<dat:dataPack <?php 
    ?>id="prod<?php echo $this->params['dataPackId'] ?>" <?php 
    ?>ico="<?php echo $this->params['cid'] ?>" <?php 
    ?>application="<?php echo App::getSetting('App', 'name') ?>" <?php 
    ?>version = "2.0" <?php 
    ?>note="Import skladovej zásoby" <?php 
    ?>xmlns:dat="http://www.stormware.cz/schema/version_2/data.xsd" <?php 
    ?>xmlns:stk="http://www.stormware.cz/schema/version_2/stock.xsd" <?php 
    ?>xmlns:typ="http://www.stormware.cz/schema/version_2/type.xsd"<?php 
?>>
<?php 
endif; 
// body (dislayed conditionally !!!)
if (empty($this->params['part']) ||  $this->params['part'] == 'body') :
    $record = $this->params['record']; ?>
    <dat:dataPackItem id="PROD<?php echo $record['id'] ?>" version="2.0">
        <stk:stock version="2.0">
            <?php 
            // add is the default action so it is not necessary to put it explicitly
//			<stk:actionType><stk:add></stk:add></stk:actionType>
            ?>
            <stk:stockHeader>
                <stk:stockType>card</stk:stockType>
                <stk:code><?php echo $record['id'] ?></stk:code>
                <stk:EAN><?php echo $record['ean'] ?></stk:EAN>
                <?php 
//				<stk:PLU></stk:PLU>
                ?>
				<stk:isSales>true</stk:isSales>
				<stk:isSerialNumber>false</stk:isSerialNumber>
				<stk:isInternet>true</stk:isInternet>
				<stk:isBatch>false</stk:isBatch>
				<stk:purchasingRateVAT><?php echo $record['tax_rate'] == $this->params['defaultTaxRate'] ? 'high' : 'low' ?></stk:purchasingRateVAT>
				<stk:sellingRateVAT><?php echo $record['tax_rate'] == $this->params['defaultTaxRate'] ? 'high' : 'low' ?></stk:sellingRateVAT>
				<stk:name><![CDATA[<?php echo mb_substr($record['name'], 0, 90, 'UTF-8') ?>]]></stk:name>
                <?php /*/ 
				<stk:nameComplement><![CDATA[<?php echo mb_substr($record['subtitle'], 0, 90, 'UTF-8') ?>]]></stk:nameComplement>
                /*/?>
				<stk:unit>ks</stk:unit>
				<stk:storage>
					<typ:ids>01</typ:ids><?php // hardcoded for uniknihy  ?>
				</stk:storage>
				<stk:typePrice>
					<typ:ids>SK</typ:ids><?php // hardcoded for uniknihy  ?>
				</stk:typePrice>
                <stk:payVAT><?php echo $taxedPrices ? 'true' : 'false' ?></stk:payVAT>
                <?php 
//				<stk:purchasingPrice></stk:purchasingPrice>
                ?>
				<stk:sellingPrice><?php 
                    if ($taxedPrices) {
                        echo $record['price_actual_taxed'];
                    }
                    else {
                        echo $record['price_actual_taxless'];
                    }
                ?></stk:sellingPrice>
                <?php 
//				<stk:limitMin></stk:limitMin>
//				<stk:limitMax></stk:limitMax>
//				<stk:mass></stk:mass>
//				<stk:volume></stk:volume>
//				<stk:supplier><typ:id></typ:id></stk:supplier>
//				<stk:orderName></stk:orderName>
//				<stk:orderQuantity></stk:orderQuantity>
                ?>
				<stk:shortName><![CDATA[<?php echo mb_substr(Sanitize::toAscii($record['name']), 0, 24) ?>]]></stk:shortName>	
                <?php
//				<stk:guaranteeType>year</stk:guaranteeType>
//				<stk:guarantee>2</stk:guarantee>
//				<stk:producer></stk:producer>
//				<stk:yield></stk:yield>
//              <stk:description></stk:description>
//				<stk:description2></stk:description2>
                ?>
            </stk:stockHeader>    
                        
        </stk:stock>
    </dat:dataPackItem>
<?php 
endif; 
// footer (dislayed conditionally !!!)
if (empty($this->params['part']) ||  $this->params['part'] == 'footer') :
?>
</dat:dataPack>
<?php 
endif;
?>