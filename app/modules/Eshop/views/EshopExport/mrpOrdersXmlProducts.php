<?php
/**
 * Dokumentácia: 
 *   - http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#IMPEO0
 *   - http://www.mrp.sk/index.php/autonomnyr.html#IMPEO0
 *   - sekcia skladoveKarty je popisaná v http://projects.run.sk/categories/1/documents/16
 * 
 * Skratky v dokumentácii: DM = desatinné miesta, O/M = ???
 * Dĺžka hodnôť tagov a parametrov - je potrebné jú orezať podľa dokumentácie.
 * V atribútpch môžu byť aj mäkčene a dĺžne.
 * 
 * @param 'products' (array)
 */
if (!isset($this->params['products'])) {
    throw new Exception(__e(__FILE__, 'Missing view param products'));
}
if (empty($this->params['products'])) {
    return;
}
$products = $this->params['products'];
// function to set exact lengths of fields
$s = function($string, $length = null) {
    $string = Sanitize::invalidXmlChars($string);
    // ensure that encode string is shorter than required length
    if (
        !empty($length) 
        && Validate::intNumber($length)
    ) {
        $tmpLength = $length;
        do {
            $tmpString = mb_substr($string, 0, $tmpLength--, 'UTF-8');
            $tmpString = htmlspecialchars($tmpString, ENT_XML1 | ENT_QUOTES, 'UTF-8');
        }
        while(mb_strlen($tmpString, 'UTF-8') > $length);
        $string = $tmpString;
    }
    else {
        $string = htmlspecialchars($string, ENT_XML1 | ENT_QUOTES, 'UTF-8');
    }
    return $string;
};
?><skladoveKarty><?php
    foreach ($products as $product) {
        ?><skladovaKarta cislo="<?php echo $s($product['mrp_code'], 10) ?>" cena1sDPH="<?php echo $product['price_actual_taxed'] ?>" sazbaDPH="<?php echo $product['tax_rate'] ?>" cena3sDPH="<?php echo $product['price_actual_taxed'] ?>"><?php
            ?><nazev><?php echo $s($product['name'], 64) ?></nazev><?php
            if (!empty($product['authors'])) {
                ?><nazev2><?php echo $s($product['authors'], 64) ?></nazev2><?php
            }
            ?><kod2><?php echo $s($product['code'], 50) ?></kod2><?php
            if (!empty($product['ean'])) {
                ?><ean><?php echo $s($product['ean'], 14) ?></ean><?php
            }
        ?></skladovaKarta><?php
    } 
?></skladoveKarty><?php
