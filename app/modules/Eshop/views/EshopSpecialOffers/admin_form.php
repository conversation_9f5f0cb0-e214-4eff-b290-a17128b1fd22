<?php /* @var $this Template */
$params = &$this->params;
/* @var $SpecialOffer EshopSpecialOffer */
$SpecialOffer = &$params['SpecialOffer'];
$data = array();
if (isset($params['data'])) {
    $data = &$params['data'];
}
$id = Sanitize::value($data['id']);
$edit = !empty($id);
$applyBy = Sanitize::value($data['apply_by']);
$viewLocator = App::getContentLocatorByPid('Eshop.EshopSpecialOffers.view');
$viewUrlInfo = '/' . $viewLocator . '/{slug}';    
if (!empty($data['slug'])) {
    $viewUrl = App::getUrl(array(
        'locator' => $viewLocator,
        'args' => $data['slug']
    ));
    $viewUrlInfo .= ' (' . __a(
        __FILE__, 
        'v prípade tejto špeciáln<PERSON> ponuky %s',
        '<a href="' . $viewUrl . '" target="_blank">' . $viewUrl . '</a>'
    ) . ')';
}
$params['fields'] = array(
    array('if' => $edit),
        array('field' => 'id', 'type' => 'hidden'),
    array('endif'),
    array('h1' => __a(__FILE__, 'Základné parametre')),
    array('row'),
        array(
            'field' => 'name',
            'label' => __a(__FILE__, 'Názov'),
        ),
        array(
            'field' => 'active_from',
            'type' => 'date',
            'label' => __a(__FILE__, 'Od'),
        ),
        array(
            'field' => 'active_to',
            'type' => 'date',
            'label' => __a(__FILE__, 'Do'),
        ),
        array(
            'field' => 'active',
            'label' => __a(__FILE__, 'Aktívna'),
            'hint' => __a(__FILE__, 'Ak špeciálna ponuka je "Aktívna" a má nastavený dátum "Od", tak pred týmto dátumom je len zverejnená v menu špeciálnych ponúk, no produkty je možné vrámci tejto špeciálnej ponuky nakupovať až po dátume "Od"'),
        ),
    array('/row'),
    array('row',  'columns' => array(3, 6, 3)),
        array(
            'field' => 'menu_image',
            'label' => __a(__FILE__, 'Obrázok v menu'),
            'hint' => __a(
                __FILE__, 
                'Obrázok v bočnom menu. Na mobilných zariadeniach je na celú šírku obrazovky. Šírka %s', 
                '760px'
            ),
        ),
        array('if' => 
            !empty($data['menu_image'])
            && $edit
        ),
            array(
                'field' => 'menu_image',
                'type' => 'image',
                'deleteImage' => '/mvc/Eshop/EshopSpecialOffers/admin_deleteFile/menu_image/' . $id
            ),
        array('endif'),
        array(
            'field' => 'show_in_menu',
            'label' => __a(__FILE__, 'Zobraziť v menu'),
            'hint' => __a(__FILE__, 'Má sa táto špeciálna ponuka zobraziť v bočnom menu?'),
        ),
    array('/row'),
    array(
        'h1' => __a(__FILE__, 'Podmienky získania špeciálnej zľavy'),
        'hint' => __a(__FILE__, 'Zľavnené produkty sú dostupné v špeciálnych cenách za tu určených podmienok')
    ),
    array('row', 'columns' => array(3, 6, 3)),
        array(
            'field' => 'apply_by',
            'label' => __a(__FILE__, 'Uplatniť pre'),
            'options' => $SpecialOffer->getEnumValues('apply_by'),
        ), 
        array('col'),
            array('row', 
                'columns' => 1, 
                'class' => 'promoted-products',
//                'style' => 
//                    !$applyBy || in_array(
//                        $applyBy, 
//                        array('promoted_products_in_cart', 'promoted_products_cart_price_threshold')
//                    ) 
//                    ? '' : 'display:none;'
            ),
                array(
                    'field' => 'promoted_product_ids',
                    'label' => __a(__FILE__, 'Promované produkty'),
                    'hint' => 
                        '<div class="promoted-products-hint">' . 
                            __a(__FILE__, 'Ak sa niektorý z promovaných produktov nachádza v košíku, tak zľavnené produkty sú dostupné v špeciálnych cenách. Ku každému z vybraných produktov je možné pridať do košíka jeden zľavnený produkt v špeciálnej cene. Produkt môže byť zaradený ako promovaný súčastne do viacerých časovo prekrývajúcich sa aktívnych špeciálnych ponúk') .
                        '</div>' .
                        '<div class="promoted-products-cart-price-threshold-hint">' . 
                            __a(__FILE__, 'Ak celková cena promovaných produktov v košíku presahuje zadanú hraničnú cenu, tak zľavnené produkty sú dostupné v špeciálnych cenách. Ku každému násobku hraničnej ceny je možné pridať do košíka jeden zľavnený produkt v špeciálnej cene. Produkt môže byť zaradený ako promovaný súčastne do viacerých časovo prekrývajúcich sa aktívnych špeciálnych ponúk') .
                        '</div>',
                    'type' => 'itemselector',
                    'options' => '/mvc/Eshop/EshopProducts/admin_list'
                ),
            array('/row'),
            array('row', 
                'columns' => 2, 
                'class' => 'cart-price-threshold',
//                'style' => 
//                    in_array(
//                        $applyBy, 
//                        array('cart_price_threshold', 'promoted_products_cart_price_threshold')
//                    ) 
//                    ? '' : 'display:none;'
            ),
                array(
                    'field' => 'cart_price_threshold',
                    'label' => __a(__FILE__, 'Hraničná cena košíka'),
                    'hint' => 
                        '<div class="cart-price-threshold-hint">' . 
                            __a(__FILE__, 'Ak je celková cena produktov v košíku rovná alebo väčšia ako tu zadaná hraničná cena, tak zľavnené produkty sú dostupné v špeciálnych cenách. Ku každému násobku tejto hraničnej ceny je možné pridať do košíka jeden zľavnený produkt v špeciálnej cene.') .
                        '</div>' .
                        '<div class="cart-price-threshold-of-promoted-products-hint">' . 
                            __a(__FILE__, 'Ak je celková cena promovaných produktov v košíku rovná alebo väčšia ako tu zadaná hraničná cena, tak zľavnené produkty sú dostupné v špeciálnych cenách. Ku každému násobku tejto hraničnej ceny je možné pridať do košíka jeden zľavnený produkt v špeciálnej cene.') .
                        '</div>',
                ),
            array('/row'),
        array('/col'),
        array('col',
            'class' => 'promoted-products-label',
        ),
            array(
                'field' => 'promoted_products_label_image',
                'label' => __a(__FILE__, 'Štítok'),
                'hint' => __a(__FILE__, 'Motivujúci štítok označujúci promované produkty. Ideálne okrúhly obrázok s rozmermi %s', '50 x 50px'),
            ),
            array('if' => 
                !empty($data['promoted_products_label_image'])
                && $edit
            ),
                array(
                    'field' => 'promoted_products_label_image',
                    'type' => 'image',
                    'deleteImage' => '/mvc/Eshop/EshopSpecialOffers/admin_deleteFile/promoted_products_label_image/' . $id
                ),
            array('endif'),
            array(
                'field' => 'promoted_products_label_text',
                'label' => __a(__FILE__, 'Popisok štítku'),
                'hint' => __a(__FILE__, 'Popisok štítku promovaných produktov zobrazený pri nájazde myšou na štítok. Text popisku by mal byť stručný, motivujúci k akcii'),
            ),
        array('/col'),
    array('/row'),
    array(
        'h1' => __a(__FILE__, 'Produkty so špeciálnou zľavou'),
        'hint' => __a(__FILE__, 'Tieto produkty je možné zakúpiť (umiestniť do košíka) v špeciálnych cenách za vyššie nastavených podmienok')
    ),
    array('row', 'columns' => array(3, 6, 3)),
        array(
            'field' => 'discounted_products_price_adjustment',
            'label' => __a(__FILE__, 'Cena zľavnených produktov'),
            'hint' => __a(__FILE__, 'Cena zľavnených produktov bude pri splnení vyššie nastavených podmienok taká, ako je uvedená tu. Možnosti sú nasledovné (napríklad):<ul><li><code>1.5</code> - cena zľavnených produktov bude 1,5 €</li><li><code>0</code> - zľavnené produkty budú zadarmo</li><li><code>25%</code> - cena zľavnených produktov bude 25% z pôvodnej ceny</li></ul>'),
        ),
        array(
            'field' => 'discounted_product_ids',
            'label' => __a(__FILE__, 'Zľavnené produkty'),
            'hint' => __a(__FILE__, 'Produkt môže byť zaradený ako zľavnený súčastne len do jednej z viacerých časovo prekrývajúcich sa aktívnych špeciálnych ponúk'),
            'type' => 'itemselector',
            'options' => '/mvc/Eshop/EshopProducts/admin_list'
        ),
        array('col'),
            array(
                'field' => 'discounted_products_label_image',
                'label' => __a(__FILE__, 'Štítok'),
                'hint' => __a(__FILE__, 'Motivujúci štítok označujúci zľavnené produkty. Ideálne okrúhly obrázok s rozmermi %s', '50 x 50px'),
            ),
            array('if' => 
                !empty($data['discounted_products_label_image'])
                && $edit
            ),
                array(
                    'field' => 'discounted_products_label_image',
                    'type' => 'image',
                    'deleteImage' => '/mvc/Eshop/EshopSpecialOffers/admin_deleteFile/discounted_products_label_image/' . $id
                ),
            array('endif'),
            array(
                'field' => 'discounted_products_label_text',
                'label' => __a(__FILE__, 'Popisok štítku'),
                'hint' => __a(__FILE__, 'Popisok štítku zľavnených produktov zobrazený pri nájazde myšou na štítok. Text popisku by mal byť stručný, motivujúci k akcii'),
            ),
        array('/col'),    
    array('/row'),
    array(
        'h1' => __a(__FILE__, 'Info stránka'),
        'hint' => __a(__FILE__, 'Ak je špeciálna ponuka aktívna, tak infostránka je dostupná na adrese %s. Info stránka by mala obsahovať podrobný popis špeciálnej ponuky s vhodne zakomponovaným zoznamom promovaných a zľavnených produktov', $viewUrlInfo)
    ),
    array('row'),
        array(
            'field' => 'slug',
            'label' => __a(__FILE__, 'Slug'),
            'hint' => __a(__FILE__, 'Použije sa v URL adrese na zobrazenie info stránky, ktorá je dostupná na adrese %s', $viewUrlInfo)
        ),
    array('/row'),    
    array('row', 'columns' => 1),
        array(
            'field' => 'description',
            'label' => __a(__FILE__, 'Popis'),
            'type' => 'editor',
            'hint' => __a(__FILE__, 'Popis špeciálnej ponuky, ktorý sa zobrazí v jej detaile. V texte je možné použiť nasledovné vsuvky:<ul><li><code>:promotedProducts:</code> - zoznam promovaných produktov</li><li><code>:cartPrice:</code> - cena košíka</li><li><code>:discountedProducts:</code>  - zoznam produktov so špeciálnou zľavou</li></ul>Ak sa v popise nenájde žiadna vsuvka, tak sa za popis vygenerujú prednastavené zoznamy produktov.')
        ),
    array('/row'),    
    array(
        'h1' => __a(__FILE__, 'Banner'),
        'hint' => __a(__FILE__, 'Banner špeciálnej ponuky sa zobrazí:<ul><li>v hlavičke infostránky špeciálnej ponuky</li><li>v košíku, ak košík spĺňa nastavené podmienky získania zľavy</li><li>v detaile promovaných produktov</li><li>v detaile zľavnených produktov</li></ul><br>Banner pozostáva z obrázku a stručného textu umiestneného pod obrázkom.')
    ),
    array('row', 'columns' => 1),
        array('col'),
            array(
                'field' => 'banner_image',
                'label' => __a(__FILE__, 'Obrázok bannera'),
                'hint' => __a(__FILE__, 'Šírka %s, výška ideálne okolo %s', '1190px', '120px'),
            ),
            array('if' => 
                !empty($data['banner_image'])
                && $edit
            ),
                array(
                    'field' => 'banner_image',
                    'type' => 'image',
                    'deleteImage' => '/mvc/Eshop/EshopSpecialOffers/admin_deleteFile/banner_image/' . $id
                ),
            array('endif'),
        array('/col'),
    array('/row'),
    array('row', 'columns' => 1),
        array(
            'field' => 'banner_text',
            'label' => __a(__FILE__, 'Text bannera'),
            'hint' => __a(__FILE__, 'Stručný motivujúci popis špeciálnej ponuky'),
            'type' => 'editor',
        ),
    array('/row'),
    array('row'),
        array(
            'field' => 'cart_banner_type',
            'label' => __a(__FILE__, 'Zobrazenie v košíku'),
            'hint' => __a(__FILE__, 'Ako sa zobrazuje špeciálna ponuka v košíku? Ako obrázkový banner s textom (tu hore zadaný) alebo ako zoznam/slajder produktov so špeciálnou zľavou (a prípadným tu hore zadaným textom)?'),
            'options' => $SpecialOffer->getEnumValues('cart_banner_type'),
        ),
    array('/row'),
    array(
        'h1' => __a(__FILE__, 'SEO parametre info stránky'),
        'hint' => __a(__FILE__, 'Parametre infostránky pre vyhladávače, napr. Google.com, Yahoo.com alebo Zoznam.cz')
    ),
    array('row', 'columns' => array(3,6,3)),
        array(
            'field' => 'seo_title', 
            'label' => __a(__FILE__, 'Titulok'),
            'hint' => __a(__FILE__, 'Titulok sa zobrazuje v založke internetového prehliadača a vo výsledkoch Googlu. Je to jeden z najdôležitejších textov pre Google.'),
        ),
        array('field' => 'seo_description', 
            'label' => __a(__FILE__, 'Popis'),
            'type' => 'textarea', 
            'style' => 'height: 34px',
            'hint' => __a(__FILE__, 'Tento text Google väčšinou zobrazuje vo výsledkoch hľadania ako popisný text pod názvom výsledku. Niekedy však použije iný text vygenerovaný z obsahu stránky'),
        ),
        array('field' => 'seo_keywords', 
            'label' => __a(__FILE__, 'Kľúčové slová'),
            'hint' => __a(__FILE__, 'Google neberie kľúčové slová do úvahy. Iné vyhľadávače ich môžu brať do úvahy.')
        ),
    array('/row'),                
    
);
$params['showAffix'] = true;
$params['class'] = 'eshop-special-offer-form';
$params['id'] = uniqid('eshop-special-offer-form-');
echo Html::smartForm($params);
App::startJsCapture();
?><script type="text/javascript">
jQuery(function() {
    var $form = jQuery('#<?php echo $this->params['id'] ?>'),
        $applyBySelectbox = $form.find('[name="data[apply_by]"]'),
        $promotedProducts = $form.find('.promoted-products'),
        $cartPriceThreshold = $form.find('.cart-price-threshold'),
        $promotedProductsLabel = $form.find('.promoted-products-label'),
        styleElement, styleSheet;
        styleElement = document.createElement('style');
        document.head.appendChild(styleElement);
        styleSheet = styleElement.sheet;
    function synchronizeFormInputs() {
        if ($applyBySelectbox.val() === 'promoted_products_in_cart') {
            $cartPriceThreshold.hide();
            try { styleSheet.deleteRule(0) } catch {};
            styleSheet.insertRule('.promoted-products-cart-price-threshold-hint{display:none}', 0);
            $promotedProducts.show();
            $promotedProductsLabel.show();
        }
        else if ($applyBySelectbox.val() === 'cart_price_threshold') {
            $promotedProducts.hide();  
            $promotedProductsLabel.hide();
            try { styleSheet.deleteRule(0) } catch {};
            styleSheet.insertRule('.cart-price-threshold-of-promoted-products-hint{display:none}', 0);
            $cartPriceThreshold.show();
        }
        else {
            try { styleSheet.deleteRule(0) } catch {};
            styleSheet.insertRule('.promoted-products-hint,.cart-price-threshold-hint{display:none}', 0);
            $promotedProducts.show();            
            $cartPriceThreshold.show();
            $promotedProductsLabel.show();
        }
    }
    synchronizeFormInputs();
    $applyBySelectbox.on('change', function() {
        synchronizeFormInputs();
    });
});
</script><?php
App::endJsCapture();
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
));

