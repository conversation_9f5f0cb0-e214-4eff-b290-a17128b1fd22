<?php /* @var $this Template */
$this->displayOriginComment = true;
$offer = $this->params['offer'];
?><div class="special-offer-view"<?php echo $this->params['OfferSmartAdminLauncher']->markRecord($offer['id']) ?>><?php 
    ?><h1 class="title"><?php 
        echo $offer['name'];
    ?></h1><?php
    if (!empty($offer['banner_image'])) {
        ?><div class="image"><?php 
            ?><img <?php 
                ?>src="<?php echo $offer['banner_image'] ?>" <?php 
                ?>alt="<?php echo Sanitize::htmlToText($offer['name'], false) ?>"<?php
            ?>/><?php
        ?></div><?php
    }
    $promotedProductsHtml = '';
    if (
        $offer['apply_by'] === 'promoted_products_in_cart'
        || $offer['apply_by'] === 'promoted_products_cart_price_threshold'
    ) {
        $promotedProductsHtml = $this->loadView('EshopProducts/index', 
                $this->params + array('products' => &$this->params['promotedProducts'])
        );
    }
    $discountedProductsHtml = $this->loadView('EshopProducts/index', 
        $this->params + array('products' => &$this->params['discountedProducts'])
    );
    ?><div class="description"><?php 
        if (!empty($offer['description'])) {
            echo Str::fill($offer['description'], array(
                'promotedProducts' => &$promotedProductsHtml,
                'discountedProducts' => &$discountedProductsHtml,
                'price' => Eshop::formatPrice($offer['cart_price_threshold'])
            ));
        }
        elseif (!empty($offer['banner_text'])) {
            echo $offer['banner_text'];
        }
        else {
            echo __(
                __FILE__, 
                'Informácie ohľadom tejto špeciálnej ponuky Vám radi poskytneme, <a href="%s">kontaktujte nás prosím</a>',
                App::getContentUrlByPid('contact')
            );
        }
        // normally promoted and discounted products are placed as inserts in description
        // if no is provided then generate this fallback lists
        if (!preg_match('/:[a-z]+:/i', $offer['description'])) {
            if ($offer['apply_by'] === 'promoted_products_in_cart') {
                ?><br /><?php
                ?><br /><?php
                ?><h2><?php 
                    echo __(__FILE__, 'Získajte tieto produkty so špeciálnymi zľavami') . ':';
                ?></h2><?php
                echo $discountedProductsHtml;
                ?><h2><?php 
                    echo __(__FILE__, '...ku káždému z nasledovných produktov') . ':';
                ?></h2><?php
                echo $promotedProductsHtml;
            }
            elseif ($offer['apply_by'] === 'promoted_products_cart_price_threshold') {
                ?><br /><?php
                ?><br /><?php
                ?><h2><?php 
                    echo __(__FILE__, 'Získajte tieto produkty so špeciálnymi zľavami') . ':';
                ?></h2><?php
                echo $discountedProductsHtml;
                ?><h2><?php 
                    echo __(
                        __FILE__,
                        '...za každých %s nakúpených z nasledovnej ponuky',
                        Eshop::formatPrice($offer['cart_price_threshold'])
                    ) . ':';
                ?></h2><?php
                echo $promotedProductsHtml;
            }
            else {
                ?><br /><?php
                ?><br /><?php
                ?><h2><?php 
                    echo __(
                        __FILE__, 
                        'Získajte nasledovné produkty so špeciálnymi zľavami za každých %s vo vašom nákupnom košíku',
                        Eshop::formatPrice($offer['cart_price_threshold'])
                    ) . ':';
                ?></h2><?php
                echo $discountedProductsHtml;
            }
        }
    ?></div><?php
?></div><?php
