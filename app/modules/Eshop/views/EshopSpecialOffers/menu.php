<?php /* @var $this Template */
$this->displayOriginComment = true;
$menuId = uniqid('special-offers-menu-');
echo Html::toggleButton('#' . $menuId, array(
    'class' => 'special-offers-menu-toggle-button',
    'label' => !empty($this->params['title']) ? $this->params['title'] : __(__FILE__, 'Špeciálne ponuky'),
));
$class = 'special-offers-menu';
if ($this->params['class']) {
    $class .= ' ' . trim($this->params['class']);
}
?><nav id="<?php echo $menuId ?>" class="<?php echo $class ?>"><?php 
    if (!($sideContentTabTitle = App::getGlobal('App', 'sideContentTabTitle'))) {
        App::setGlobal('App', 'sideContentTabTitle', true);
    }
    if (!empty($this->params['title'])) {
        if (!$sideContentTabTitle) {
            App::setGlobal('App', 'sideContentTabTitle', $this->params['title']);
        }
        else {            
            ?><div class="title"><?php
                echo $this->params['title'];
            ?></div><?php
        }
    }
    echo Html::menu($this->params['offers'], array(
        'activeItem' => $this->params['activeItem'],
        'homeSlug' => HOME_SLUG,
        'lang' => URL_LANG,
        'urlBase' => $this->params['urlBase'],
        'labelWrappers' => array('l'),
        'SmartAdminLauncher' => $this->params['SmartAdminLauncher'],
    ));
?></nav><?php
