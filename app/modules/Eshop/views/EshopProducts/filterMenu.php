<?php /* @var $this Template */
$this->displayOriginComment = true;
App::loadLib('App', 'Paginator');
$Paginator = new Paginator();
?><div class="filter-menu"><?php 
echo $Paginator->getFilterSwitch('discounted', '1', array(
    'label' => __(__FILE__, 'Deals'),
));
/* hederer 14.4 .2016
echo $Paginator->getFilterSwitch('for_beginners', '1', array(
    'label' => __(__FILE__, 'For beginners'),
    'resetParams' => array(
        'page',
        'filter.for_advanced',
        'filter.for_professionals',
    ),
));
echo $Paginator->getFilterSwitch('for_advanced', '1', array(
    'label' => __(__FILE__, 'For advanced'),
    'resetParams' => array(
        'page',
        'filter.for_beginners',
        'filter.for_professionals',
    ),
));
echo $Paginator->getFilterSwitch('for_professionals', '1', array(
    'label' => __(__FILE__, 'For professionals'),
    'resetParams' => array(
        'page',
        'filter.for_beginners',
        'filter.for_advanced',
    ),
));
*/
?></div><?php 
