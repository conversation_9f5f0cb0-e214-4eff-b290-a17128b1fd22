<section id="best_sell_sidebar">
    <h3><?php echo __(__FILE__, 'Bestsellers') ?></h3>
    <ul>
        <?php 
        $productsCount = count($this->params['products']);
        foreach ($this->params['products'] as $product) : 
            $urlShowDetail = App::getUrl(array(
                'locator' => $this->params['slugProductView'],
                'args' => array($product['slug'])
            ));
            $class = '';
            if (--$productsCount == 0) {
                $class = ' class="last"';
            }
        ?>
        <li<?php echo $class ?>>
            <a href="<?php echo $urlShowDetail; ?>">
                <span>
                    <img src="<?php echo $product['image']['small']; ?>" alt="<?php echo $product['name']; ?>" class="product_img" />
                </span>
                <p>
                    <strong class="heading"><?php echo $product['name']; ?></strong>
                    <span class="description"><?php echo $product['short_description']; ?></span>
                    <span><?php echo __(__FILE__, 'Our price'); ?>:<strong class="price"> <?php echo Eshop::formatPrice($product['price_actual_taxed']) ?></strong>
                        <?php if ($product['savings_rate']) {
                        echo '<br>' . __(__FILE__, 'You save') . ': <strong class="save_up">'.floor($product['savings_rate']).'%</strong>';
                        } ?>
                    </span>
                </p>
            </a>
        </li>
        <?php 
        endforeach; ?>
    </ul>
</section>
