<?php /* @var $this Template */
$this->displayOriginComment = true;
$content = Sanitize::value($this->params['content']);
$columns = $this->params['columns'];
$uniqueClass = uniqid('products-index-');
?><div class="products-index <?php echo $uniqueClass ?>"><?php
    if ($this->params['title']) {
        $titleTag = Sanitize::value($this->params['titleTag'], 'h1');
        ?><<?php echo $titleTag ?> class="section-title"><?php 
            echo $this->params['title'];
        ?></<?php echo $titleTag ?>><?php
    }
    // content text before products
    if (
        !empty($content['text'])
        && !empty($content['showText'])
        && (
            $content['showText'] === 'enum_without_products'
            || $content['showText'] === 'enum_before_products'
        )
    ) {
        ?><div class="index-text"<?php 
            echo $this->params['smartAdminLauncherContentRecordAttribute'] 
        ?>><?php 
            echo $content['text'] 
        ?></div><?php 
    }
    // products
    if (
        empty($content['showText'])
        || $content['showText'] !== 'enum_without_products'
    ) {
        if (
            isset($this->params['Paginator'])
            &&  (
                !empty($this->params['showFilterSelect'])
                || 
                !empty($this->params['showSortSelect'])
            )
        ) {
            ?><div class="filter-and-sort-select"><?php 
    //            // filter select
    //            if ($this->params['showFilterSelect']) {
    //                $this->params['manufacturers'] = array('' => __(__FILE__, 'choose a value...')) + $this->params['manufacturers'];
    //                echo $this->params['Paginator']->getFilterSelect(array(
    //                    'field' => 'run_eshop_manufacturers_id',
    //                    'label' => __(__FILE__, 'Manufacturer'),
    //                    'class' => 'filter-select',
    //                    'options' => $this->params['manufacturers'],
    //                ));
    //            }
                // sort select 
                if ($this->params['showSortSelect']) {
                    echo $this->params['Paginator']->getSortSelect(array(
                        'label' => __(__FILE__, 'Sort by'),
                        'class' => 'sort-select',
                        'options' => array(
                            '' => __(__FILE__, 'choose a value...'),
//                            'name:ASC' => __(__FILE__, 'name (a-z)'),
//                            'name:DESC' => __(__FILE__, 'name (z-a)'),
                            'actual_price:ASC' => __(__FILE__, 'price (ascendinng)'),
                            'actual_price:DESC' => __(__FILE__, 'price (descending)'),
                            'EshopManufacturer.name:ASC' => __(__FILE__, 'manufacturer (a-z)'),
                            'EshopManufacturer.name:DESC' => __(__FILE__, 'manufacturer (z-a)'),
                        ),
                        'resetParams' => 'sort',
                    ));
                }
            ?></div><?php
        }
        ?><div class="index"><?php 
            // product block
            ?><div class="product" v-for="product in products"><?php
                ?><div class="spacer"><?php 
                    // image
                    ?><a <?php
                        ?>v-if="image(product)" <?php
                        ?>:href="viewUrl(product)" <?php
                        ?>title="<?php echo __(__FILE__, 'More info') ?>" <?php
                        ?>class="image"<?php
                    ?>><?php
                        ?><img :src="image(product)" :alt="product.name" /><?php
                    ?></a><?php
                    /*
                    // title
                    ?><h3 class="title"><?php
                        ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'More info') ?>"><?php
                            echo $product['name'];
                        ?></a><?php
                    ?></h3><?php
                    // info
                    ?><div class="info"><?php 
                        // price
                        ?><div class="actual-price"><?php 
                            //echo __(__FILE__, 'Our price') . ' '; 
                            echo Eshop::formatPrice($product['price_actual_taxed'], array(
                                'symbol' => '<span class="currency">' . Eshop::getActualCurrency('symbol') . '</span>')
                            );
                        ?></div><?php
                        // saving rate
                        if (!empty($product['savings_rate'])) {
                            ?><div class="discount-label"><?php 
                                echo __(__FILE__, 'Discount');
                            ?></div><?php
                            ?><div class="savings-rate"><?php 
                                //echo '-' . floor($product['savings_rate']) . '%';
                            ?></div><?php
                        }
                        // disponibility
                        if ($this->params['showDisponibility']) {
                            $disponibility = '';
                            switch ($product['disponibility']) {
                                case EshopProduct::STOCK:
                                    $disponibility = __(__FILE__, 'Available on stock');
                                    break;
                                case EshopProduct::SUPPLIER:
                                    $disponibility = __(__FILE__, 'Available at supplier');
                                    break;
                                case EshopProduct::ON_DEMAND:
                                    $disponibility = __(__FILE__, 'On demand');
                                    break;
                                case EshopProduct::SOLDOUT:
                                    $disponibility = __(__FILE__, 'Sold out');
                                    break;
                                case EshopProduct::PRESALE:
                                    $disponibility = __(__FILE__, 'Presale');
                                    break;
                                default:
                                break;
                            }
                            ?><div class="disponibility"><?php 
                                echo $disponibility;
                            ?></div><?php
                        }
                    ?></div><?php
                    // add to cart button
                    if (
                        $this->params['showAddToCartButton']
                        && $product['disponibility'] !== EshopProduct::SOLDOUT
                    ) { 
                        ?><form action="<?php echo $urlAddToCart; ?>" method="post" class="add-to-cart"><?php
                            ?><input type="hidden" name="data[_target]" value="Eshop.EshopCarts.addProducts" /><?php
                            ?><input type="hidden" name="data[id]" value="<?php echo $product['id']; ?>" /><?php
                            ?><input type="hidden" name="data[amount]" value="1" /><?php
                            ?><button type="submit" data-action="buy"><?php 
                                echo __(__FILE__, 'Insert to cart');
                            ?></button><?php
                        ?></form><?php
                    }
                     */
                ?></div><?php
            ?></div><?php
        ?></div><?php
    }
    // content text after products
    if (
        !empty($content['text'])
        && !empty($content['showText'])
        && $content['showText'] === 'enum_after_products'
    ) {
        ?><div class="index-text"><?php 
            echo $content['text'] 
        ?></div><?php 
    }
?></div><?php
// set product css width according number of columns
if (!empty($columns)) {
    App::startCssCapture();
    ?><style type="text/css">
        .<?php echo $uniqueClass ?> .index .product {
            width: <?php echo 100 / $columns ?>%;
        }
    </style><?php
    App::endCssCapture();
}
$jsOptions = array(
    'selector' => '.' . $uniqueClass,
    'emptyIndexMessage' => $this->params['emptyIndexMessage'],
    'showDisponibility' => $this->params['showDisponibility'],
    'showAddToCartButton' => $this->params['showAddToCartButton'],
    'showSortSelect' => $this->params['showSortSelect'],
    'showFilterSelect' => $this->params['showFilterSelect'],
    'slugProductView' => $this->params['slugProductView'],
    'slugAuthorProducts' => $this->params['slugAuthorProducts'],
    'slugManufacturerProducts' => $this->params['slugManufacturerProducts'],
);
$jsOptions['findOptions'] = array_diff_key($this->params, $jsOptions, array(
    'title' => null,
    'titleTag' => null,
    'columns' => null,
    'content' => null,
    '_snippet' => null,
    '_snippet_name' => null,
    '_snippet_generic' => null,
    '_text' => null,
));
App::startJsCapture();
?><script type="text/javascript">
    new Run.Eshop.EshopProductsIndexWithVue(<?php echo json_encode($jsOptions) ?>);
</script><?php
App::endJsCapture();
App::setJsFiles(array(
    'https://cdn.jsdelivr.net/npm/vue/dist/vue.js',
    '/app/js/vendors/jquery.min.js',
    '/app/js/libs/PhpJs.js',
    '/app/js/libs/Number.js',
    '/app/js/libs/Utility.js',
    '/app/js/libs/Validate.js',
    '/app/js/libs/Sanitize.js',
    '/app/js/libs/App.js',
    '/app/modules/Eshop/js/EshopProductsIndexWithVue.js',
));
App::setJsConfig('App', array(
    'urlRoot' => App::$urlRoot,
    'urlLang' => App::$urlLang,
    'urlBase' => App::$urlBase,
    'homeSlug' => App::$homeSlug,
));

// RETURN
return;

//
// Code store for other projects
//

