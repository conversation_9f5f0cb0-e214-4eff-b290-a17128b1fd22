<?php /* @var $this Template */
$this->displayOriginComment = true;
$product = $this->params['product'];
// available from
if (
    !empty($product['available_from'])
    && strtotime($product['available_from']) > time()
    && $product['disponibility'] != EshopProduct::STOCK
) {
    if ($product['disponibility'] == EshopProduct::PRESALE) {
        $availableFromLabel = __(__FILE__, 'Published on');
    }       
    else {
        $availableFromLabel = __(__FILE__, 'Available on');
    }
    ?><div class="available-from"><?php 
        echo $availableFromLabel . ' ' . Date::format($product['available_from'], 'j. n. Y');
    ?></div><?php 
}
