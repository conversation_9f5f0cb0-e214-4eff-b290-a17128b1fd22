<?php /* @var $this Template */
$this->displayOriginComment = true;
App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    //'data' => $this->params['data'],
));
$columns = $this->params['columns'];
$detailedIndex = (!empty($this->params['indexType']) && $this->params['indexType'] === 'detailed');
$this->params['products'] = array_values($this->params['products']);
$lastIndex = count($this->params['products']) - 1;
$imageVariant = $this->params['imageVariant'];
foreach ($this->params['products'] as $i => $product) { 
    $urlAddToCart = App::getUrl(array(
        'module' => $this->module,
        'controller' => 'EshopCarts',
        'action' => 'addProducts',
        'args' => array($product['id'])
    ));
    $urlAddToWishlist = App::getUrl(array(
        'module' => $this->module,
        'controller' => 'EshopWishlists',
        'action' => 'addProduct',
        'args' => array($product['id']),
        'get' => array(
            'redirectUrl' => App::$url,
        )
    ));
    $urlAddAvailabilityAlert = App::getUrl(array(
        'module' => $this->module,
        'controller' => 'EshopWishlists',
        'action' => 'addProductAvailabilityAlert',
        'args' => array($product['id']),
        'get' => array(
            'redirectUrl' => App::$url,
        )
    ));
    $urlShowDetail = App::getUrl(array(
        'locator' => $this->params['slugProductView'],
        'args' => array($product['slug'])
    ));
    // create row starts if number of columns is provided
    if (
        !empty($columns)
        && $i % $columns == 0
    ) {
        ?><div class="index-row"><?php
    }
    // get row position class if number of columns is provided
    $productClass = '';
    if (!empty($columns)) {
        if ($i % $columns == 0) {
            $productClass = ' first';
        }
        elseif (($i + 1) % $columns == 0) {
            $productClass = ' last';
        }
    }
    $productRepresentsVariants = !empty($product['variants_common_name']);
    $productName = $productRepresentsVariants ? $product['variants_common_name'] : $product['name'];
    if ($productRepresentsVariants) {
        $productClass .= ' represents-variants';
    }
    // product block
    ?><div class="product<?php echo $productClass ?>"<?php echo $this->params['SmartAdminLauncher']->markRecord($product['id']) ?>><?php
        ?><div class="spacer"><?php 
            // image
            if (!empty($product['image'][$imageVariant])) {
                ?><div class="image"><?php 
                    ?><div class="wrapper"><?php 
                        // special offers
                        echo $this->loadElement('productSpecialOfferLabels', $this->params + array('product' => $product));
                        // product image
                        ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'More info') ?>" class="product-image"><?php
                            ?><img <?php 
                                ?>src="<?php echo $product['image'][$imageVariant]; ?>" <?php 
                                ?>alt="<?php echo $productName; ?>"<?php 
                            ?>/><?php
                        ?></a><?php
                    ?></div><?php
                ?></div><?php
            }
            ?><div class="stickies"><?php
                if ($product['savings_rate'] > 0) {
                    ?><div class="stick savings-rate"><?php
    //                    echo __(__FILE__, 'Zľava') . ' '; 
                        echo '-' . floor($product['savings_rate']) . '%';
                    ?></div><?php
                }
                if (!empty($product['new'])) {
                    ?><div class="stick new"><?php
                        echo __(__FILE__, 'Novinka');
                    ?></div><?php
                }
            ?></div><?php
            ?><div class="texts"><?php 
                // title
                ?><h3 class="title"><?php
                    ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'More info') ?>"><?php
                        echo $productName;
                    ?></a><?php
                ?></h3><?php
                // subtitle and code
                if (
                    !empty($product['subtitle'])
                    || !empty($product['code']
                    &&
                    !$productRepresentsVariants)
                ) {
                    ?><div class="subtitle-and-code"><?php
                        if (!empty($product['subtitle'])) {
                            ?><span class="subtitle"><?php
                                echo $product['subtitle'];
                            ?></span><?php
                        }
                        if (!empty($product['code'])) {
                            ?><span class="code"><?php
                                echo $product['code'];
                            ?></span><?php
                        }
                    ?></div><?php
                }
                // info
                ?><div class="info"><?php 
                    ?><div class="origin"><?php 
                        // authors
                        if (!empty($product['EshopAuthor'])) {
                            ?><div class="authors"><?php 
                                foreach ($product['EshopAuthor'] as $j => $author) {
                                    if ($j > 0) {
                                        echo ', ';
                                    }
                                    if (!empty($this->params['slugAuthorProducts'])) {                                                
                                        $urlAuthorProducts = App::getUrl(array(
                                            'locator' => $this->params['slugAuthorProducts'],
                                            'args' => array($author['slug'])
                                        ));
                                        ?><a href="<?php echo $urlAuthorProducts ?>" title="<?php echo __(__FILE__, 'See products of author %s', $author['name']) ?>"><?php 
                                            echo $author['name'];
//                                            echo str_replace(
//                                                ',&nbsp;', ', ', preg_replace('/\s+/', '&nbsp;', $author['name'])
//                                            );
                                        ?></a><?php
                                    }
                                    else {
                                        echo str_replace(
                                            ',&nbsp;', ', ', preg_replace('/\s+/', '&nbsp;', $author['name'])
                                        );
                                    }
                                }
                            ?></div><?php 
                        }
                        // manufacturer
                        if (!empty($product['EshopManufacturer']['name'])) {
                            $url = App::getUrl(array(
                                'locator' => $this->params['slugManufacturerProducts'],
                                'args' => array($product['EshopManufacturer']['slug'])
                            ));
                            ?><div class="manufacturer"><?php 
                                ?><a href="<?php echo $url ?>" title="<?php echo __(__FILE__, 'See products of manufacturer %s', $product['EshopManufacturer']['name']) ?>"><?php 
                                    echo $product['EshopManufacturer']['name'];
                                ?></a><?php
                            ?></div><?php
                        }
                    ?></div><?php
                    if ($detailedIndex) {
                        // description
                        ?><div class="attributes"><?php
                            if (!empty($product['dimensions'])) {
                                ?><span><?php 
                                    echo $product['dimensions'];
                                ?></span><?php
                            }
                        ?></div><?php
                        ?><div class="description"><?php 
                            echo Str::getExcerpt(Sanitize::htmlToText($product['description']), array('length' => 210));
                        ?></div><?php
                    }
                    ?><div class="price-and-disponibility"><?php 
                        // price
                        ?><div class="actual-price"><?php 
                            //echo __(__FILE__, 'Our price') . ' '; 
                            $formatedPrice = Eshop::formatPrice($product['price_actual_taxed'], array(
                                'symbol' => '<span class="currency">' . Eshop::getActualCurrency('symbol') . '</span>')
                            );
                            if ($product['variants_lowest_price']) {
                                echo __(__FILE__, 'Od %s', $formatedPrice);
                            } else {
                                echo $formatedPrice;
                            }
                        ?></div><?php
                        // old price
                        if ($product['price_actual_taxed'] < $product['price_taxed']) :
                            ?><div class="old-price"><?php
                                echo Eshop::formatPrice($product['price_taxed'], array(
                                    'symbol' => '<span class="currency">' . Eshop::getActualCurrency('symbol') . '</span>')
                                );
                            ?></div><?php
                        endif;
                        // serial label
                        if (!empty($this->params['showSerialLabel'])) {                                    
                            ?><div class="serial-label"><?php 
                                echo $i + 1;
                            ?></div><?php
                        }
                        // saving rate label (but only if seial label is not shown)
                        elseif (!empty($product['savings_rate'])) {
                            if (false/*NOTUSED*/) {
                                ?><div class="discount-label"><?php 
                                    echo __(__FILE__, 'Discount');
                                ?></div><?php
                            }
                        }
                        // disponibility (do not display for the product that represents variants)
                        if ($this->params['showDisponibility'] && !$productRepresentsVariants) {
                            // disponibility
                            $disponibility = '';
                            $disponibilityClass = '';
                            switch ($product['disponibility']) {
                                case EshopProduct::STOCK:
                                    $disponibility = __(__FILE__, 'Available on stock');
                                    $disponibilityClass = ' on-stock';
                                    break;
                                case EshopProduct::SUPPLIER:
                                    $disponibility = __(__FILE__, 'Available at supplier');
                                    $disponibilityClass = ' at-supplier';
                                    break;
                                case EshopProduct::ON_DEMAND:
                                    $disponibility = __(__FILE__, 'On demand');
                                    $disponibilityClass = ' on-demand';
                                    break;
                                case EshopProduct::SOLDOUT:
                                    $disponibility = __(__FILE__, 'Sold out');
                                    $disponibilityClass = ' sold-out';
                                    break;
                                case EshopProduct::PRESALE:
                                    if (empty($product['reprint'])) {
                                        $disponibility = __(__FILE__, 'Presale');
                                    }
                                    else {
                                        $disponibility = __(__FILE__, 'Reprint prepared');
                                    }
                                    $disponibilityClass = ' presale';
                                    break;
                                default:
                                break;
                            }
                            ?><div class="disponibility-wrapper"><?php 
                                ?><div class="disponibility<?php echo $disponibilityClass ?>"><?php 
                                    echo $disponibility;
                                ?></div><?php
                            ?></div><?php
                        }
                    ?></div><?php
                    ?><div class="controls"><?php
                        // button to see variants (replacement for add to cart button in case of variants)
                        if (
                            $this->params['showAddToCartButton']
                            && $productRepresentsVariants
                        ) {
                            ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'Pozrite si varianty') ?>" class="button"><?php
                                echo __(__FILE__, 'Pozrite si varianty');
                            ?></a><?php
                        }
                        // add to cart button
                        else if (
                            $this->params['showAddToCartButton']
                            && $product['disponibility'] !== EshopProduct::SOLDOUT
                            && empty($product['is_gift_card'])
                        ) {
                            ?><form <?php 
                                ?>action="<?php echo $urlAddToCart ?>" <?php 
                                ?>method="post" <?php 
                                ?>class="add-to-cart"<?php 
                            ?>><?php 
                                ?><input type="hidden" name="data[_target]" value="Eshop.EshopCarts.addProducts" /><?php
                                ?><input type="hidden" name="data[id]" value="<?php echo $product['id']; ?>" /><?php
                                if (!empty($product['special_offer_id'])) {
                                    ?><input type="hidden" <?php 
                                        ?>name="data[special_offer_id]" <?php 
                                        ?>value="<?php echo $product['special_offer_id']; ?>"<?php 
                                    ?>/><?php
                                }
                                ?><div class="to-cart-change"><?php
                                    ?><div class="plus"></div><?php
                                    ?><div class="minus"></div><?php
                                ?></div><?php
                                ?><div class="to-cart-count"><?php
                                    ?><input class="number-of-pieces" name="data[amount]" type="text" value="1" /><?php
                                ?></div><?php
                                ?><div class="to-cart-button"><?php
                                    ?><span class="button-container"><?php
                                        ?><input class="button" type="submit" value="" name="" title="<?php echo __(__FILE__, 'do košíka'); ?>" data-action="buy"/><?php
                                    ?></span><?php
                                ?></div><?php
                            ?></form><?php
                        }
                        if ($detailedIndex && !$productRepresentsVariants) {
                            if ($product['disponibility'] !== EshopProduct::SOLDOUT) {
                                ?><a href="<?php echo $urlAddToWishlist ?>" class="add-to-wishlist" data-action="wishlist"><?php
                                echo __(__FILE__, 'To wishlist');
                                ?></a><?php
                            }
                            else {
                                ?><form action="<?php echo $urlAddAvailabilityAlert ?>" method="post" class="add-availability-alert"><?php
                                    ?><input type="hidden" name="data[_target]" value="Eshop.EshopWishlists.addProductAvailabilityAlert" /><?php
                                    if (!App::getUser()) {
                                        echo $Form->text('email', array(
                                            'placeholder' => __(__FILE__, 'Váš e-mail')
                                        ));
                                    }
                                    ?><button data-action="watchdog"><?php
                                    echo __(__FILE__, 'Watch');
    //                                if (!App::getUser()) {
    //                                    echo ': ';
    //                                }
                                    ?></button><?php
                                    ?><div class="note"><?php
                                    echo ' ' . __(__FILE__, 'Budeme vás informovať, hneď ako bude produkt dostupný');
                                    ?></div><?php
                                ?></form><?php
                            }
                        }
                    ?></div><?php                    
                ?></div><?php
            ?></div><?php
        ?></div><?php
    ?></div><?php
    // create row ends if number of columns is provided
    if (
        !empty($columns)
        && (
            $i == $lastIndex
            || ($i + 1) % $columns == 0
        )
    ) {
        ?></div><?php
    } 
}
$this->loadModel('EshopProduct');
$Product = new EshopProduct();
echo $Product->getFacebookViewCode(array_column($this->params['products'], 'id'));
if (!empty($this->params['productsListId'])) {
    echo $Product->getLugisBoxSearchIndexAnnotationsCode(
        Sanitize::value($this->params['data']), 
        $this->params['products'], 
        $this->params['productsListId'], 
        array(
            'Paginator' => $this->params['Paginator'],
        )
    );
}

App::startjsCapture();
?><script type="text/javascript">
function activateAmountButtons() {
    var addToCartSelector = '.add-to-cart';
    var amountInputSelector = '.number-of-pieces';
    var activatedClass = '_activated';
    var increaseBtn = $( addToCartSelector + ' .plus' ).not('.' + activatedClass);
    var decreaseBtn = $( addToCartSelector + ' .minus' ).not('.' + activatedClass);
    increaseBtn.click( function() {
        var addToCartElement = $(this).closest(addToCartSelector);
        var amountInput = addToCartElement.find(amountInputSelector);
        var amount = parseInt(amountInput.val());
        amountInput.val(amount + 1);
    });
    decreaseBtn.click( function() {
        var addToCartElement = $(this).closest(addToCartSelector);
        var amountInput = addToCartElement.find(amountInputSelector);
        var amount = parseInt(amountInput.val());
        if (amount > 1) {
            amountInput.val(amount - 1);
        }
    });
    // mark all buttons which have been activated already to not
    // activate them twice on infine scroll or if there are many 
    // add to cart buttons on a single page
    increaseBtn.addClass(activatedClass);
    decreaseBtn.addClass(activatedClass);
}

// activate amount buttons in product index section
activateAmountButtons();
</script><?php
App::endJsCapture();