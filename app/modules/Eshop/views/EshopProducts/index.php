<?php /* @var $this Template */
$this->displayOriginComment = true;
$content = Sanitize::value($this->params['content']);
// if no index text neither no products neither no message to display then return nothing
if (
    // negate the following    
    !(
        // if some products and they are going to be displayed
        !empty($this->params['products'])
        && (
            empty($content['showText'])
            || $content['showText'] !== 'enum_without_products'
        )
        ||
        // or if some index text and it is going to be displayed
        !empty($content['text'])
        && !empty($content['showText'])
        && $content['showText'] !== 'enum_no'
        ||
        // or if empty index message is allowed
        !empty($this->params['emptyIndexMessage'])
    )
) {
    return;
}
$columns = $this->params['columns'];
$uniqueClass = uniqid('products-index-');
$class = 'products-index ' . $uniqueClass;
$detailedIndex = (!empty($this->params['indexType']) && $this->params['indexType'] === 'detailed');
if ($detailedIndex) {
    $class .= ' detailed-products-index';
}
if (!empty($this->params['limitImageHeight'])) {
    $class .= ' with-limited-image-height';
}
if (!empty($this->params['class'])) {
    $class .= ' ' . $this->params['class'];
}
$uniqueId = uniqid('products-index-');
?><div id="<?php echo $uniqueId ?>" class="<?php echo $class ?>"><?php
    if ($this->params['title']) {
        $titleTag = Sanitize::value($this->params['titleTag'], 'h1');
        ?><<?php echo $titleTag ?> class="section-title"><?php 
            echo $this->params['title'];
        ?></<?php echo $titleTag ?>><?php
    }
    // content text before products
    if (
        !empty($content['text'])
        && !empty($content['showText'])
        && (
            $content['showText'] === 'enum_without_products'
            || $content['showText'] === 'enum_before_products'
        )
    ) {
        ?><div class="index-text-container"<?php 
            echo $this->params['smartAdminLauncherContentRecordAttribute'] 
        ?>><?php 
            ?><div class="index-text"><?php 
                ?><div class="text"><?php 
                    echo $content['text']
                ?></div><?php
            ?></div><?php
            ?><div class="button-wrapper"><?php 
                ?><div class="more-button"><?php
                    echo __( $this, 'Čítať viac' );
                ?></div><?php 
            ?></div><?php
        ?></div><?php
    }
    if(!empty($this->params['subCategories'])){
        ?><div id="subcategories" class="subcategories"><?php 
            ?><ul class="list"><?php 
                $categoryProductsSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory');
                foreach ($this->params['subCategories'] as $subCategory){
                    $class = 'item';
                    if(!empty($subCategory['image'])){
                        $class .= ' with-image';
                    }
                    ?><li class="<?php echo $class ?>"><?php
                        $categoryProductsUrl = App::getUrl(array(
                            'locator' => $categoryProductsSlug,
                            'args' => $subCategory['slug'],
                        ));
                        ?><a class="button" href="<?php echo $categoryProductsUrl ?>" title="<?php echo $subCategory['name']; ?>"><?php
                            if(!empty($subCategory['image'])){
                                ?><span class="button-image"><?php
                                    ?><img src="<?php echo $subCategory['image']; ?>"><?php
                                ?></span><?php
                            }
                            ?><span class="button-text"><?php
                                echo $subCategory['name'];
                            ?></span><?php
                        ?></a><?php
                    ?></li><?php 
                }
            ?></ul><?php 
            ?><div class="expander"><?php
                ?><div class="expand-button button"><?php
                    echo __( $this, 'All subcategories');
                ?></div><?php
            ?></div><?php
        ?></div><?php 
    }
    // products
    if (
        empty($content['showText'])
        || $content['showText'] !== 'enum_without_products'
    ) {
        if (
            isset($this->params['Paginator'])
            &&  (
                !empty($this->params['showFilterSelect'])
                || 
                !empty($this->params['showSortSelect'])
            )
            ||
            !empty($this->params['showIndexTypeSelect'])
        ) {
            //rblsb//echo $this->loadElement('multifilter', $this->params);
            ?><div class="index-controls"><?php 
                // filter select
                if (
                    isset($this->params['Paginator']) 
                    && !empty($this->params['showFilterSelect'])
                ) {
                    echo $this->loadControllerAction(
                        'EshopProducts', 'filter', App::$params, true, App::$args
                    );
                    /*/
                    echo $this->params['Paginator']->getFilterSelect(array(
                        'field' => 'language',
                        //'label' => null,
                        'class' => 'filter-select',
                        'options' => array(
                            '' => __(__FILE__, 'Filter all languages'), 
                            '*sloven*+NULL' => __(__FILE__, 'Filter slovak language'), 
                            '*cesk*' => __(__FILE__, 'Filter czech language'), 
                            '!*sloven*&!*cesk*&!NULL' => __(__FILE__, 'Filter other languages'), 
                        ),
                    ));
                    echo $this->params['Paginator']->getFilterSwitch('stock', '>0', array(
                        'label' => __(__FILE__, 'only on stock'),
                        'class' => 'filter-switch',
                        'showCheckbox' => true,
                    ));
                    /*/
                }
                ?><div class="products-index-sort-and-type"><?php 
                    // index type select
                    if (!empty($this->params['showIndexTypeSelect'])) {
                        echo $this->loadView('EshopProducts/indexTypeSelect');
                    }
                    // sort select 
                    if (
                        isset($this->params['Paginator']) 
                        && !empty($this->params['showSortSelect'])
                    ) {
                        /*/
                        echo $this->params['Paginator']->getSortSelect(array(
                            'label' => __(__FILE__, 'Sort by'),
                            'class' => 'sort-select',
                            'options' => array(
                                '' => __(__FILE__, 'choose a value...'),
                                'name:ASC' => __(__FILE__, 'name (a-z)'),
                                'name:DESC' => __(__FILE__, 'name (z-a)'),
                                'actual_price:ASC' => __(__FILE__, 'price (ascendinng)'),
                                'actual_price:DESC' => __(__FILE__, 'price (descending)'),
                                'EshopProductAuthor>EshopAuthor.name:ASC' => __(__FILE__, 'author (a-z)'),
                                'year:DESC' => __(__FILE__, 'year (descending)'),
    //                            'EshopManufacturer.name:ASC' => __(__FILE__, 'manufacturer (a-z)'),
    //                            'EshopManufacturer.name:DESC' => __(__FILE__, 'manufacturer (z-a)'),
                            ),
                            'resetParams' => 'sort',
                        ));
                        /*/
                        echo $this->params['Paginator']->getSortSwitch('', '', array(
                            'label' => __(__FILE__, 'TOP'),
                            'class' => 'sort-switch',
                        ));
                        echo $this->params['Paginator']->getSortSwitch('bestseller', '', array(
                            'label' => __(__FILE__, 'Najpredávanejšie'),
                            'class' => 'sort-switch',
                            'resetParams' => array('sort.actual_price')
                        ));
                        echo $this->params['Paginator']->getSortSwitch('actual_price', 'ASC', array(
                            'label' => __(__FILE__, 'Najlacnejšie'),
                            'class' => 'sort-switch',
                            'resetParams' => array('sort.bestseller')
                        ));
                        echo $this->params['Paginator']->getSortSwitch('actual_price', 'DESC', array(
                            'label' => __(__FILE__, 'Najdrahšie'),
                            'class' => 'sort-switch',
                            'resetParams' => array('sort.bestseller')
                        ));
                        Html::startCapture();
                        ?><a class=":c:" href=":u:":a:><?php 
                            ?><div class="nice-checkbox"><?php
                                ?>:ch:<?php
                                ?><span class="image"></span><?php
                                ?>:l:<?php
                            ?></div><?php
                        ?></a><?php
                        $filterSwitchTemplate = Html::endCapture();                        
                        echo $this->params['Paginator']->getFilterSwitch('stock', '>0', array(
                            'label' => __(__FILE__, 'len skladom'),
                            'class' => 'filter-switch',
                            'showCheckbox' => true,
                            'template' => $filterSwitchTemplate,
                        ));
                    }
                ?></div><?php
            ?></div><?php            
        }
        $idHtml = '';
        if (!empty($this->params['productsListId'])) {
            $idHtml = ' id="' . $this->params['productsListId'] . '"';
        }
        ?><div class="index"<?php echo $idHtml ?>><?php 
            if (
                empty($this->params['products'])
            ) {
                // show empty index message but only if there is no index text shown 
                // or to be shown
                if (
                    empty($content['text'])
                    || empty($content['showText'])
                    || $content['showText'] === 'enum_no'
                ) {
                    echo App::loadElement('App', 'emptyIndexMessage', array('message' => $this->params['emptyIndexMessage']));
                    if (!empty($this->params['productsListId'])) {
                        $this->loadModel('EshopProduct');
                        $Product = new EshopProduct();
                        echo $Product->getLugisBoxSearchIndexAnnotationsCode(
                            Sanitize::value($this->params['data']), 
                            array(), 
                            $this->params['productsListId'], 
                            array(
                                'Paginator' => $this->params['Paginator'],
                            )
                        );
                    }
                }
            }
            else {
                 echo $this->loadView($this->params['recordsView'], $this->params);
            }
        ?></div><?php
        // paginator
        if (isset($this->params['Paginator'])) {
            if (empty($this->params['infiniteScroll'])) {
                $paginatorPagesLinks = $this->params['Paginator']->getPagesLinks();
                $paginatorPageSelect = $this->params['Paginator']->getPageSelect(array(
                    'label' => __(__FILE__, 'Page'),
                    'class' => 'page-select',
                ));
                $limit = $this->getSetting('EshopProduct.pagingLimit');
                $paginatorLimitSelect = $this->params['Paginator']->getLimitSelect(array(
                    'label' => __(__FILE__, 'Limit'),
                    'class' => 'limit-select',
                    'options' => array(
                        $limit => $limit,
                        2 * $limit => 2 * $limit,
                        4 * $limit => 4 * $limit,
                        8 * $limit => 8 * $limit,
                    )
                ));
                if (
                    $paginatorPagesLinks 
                    || $paginatorPageSelect 
                    || $paginatorLimitSelect
                ) {                
                    ?><div class="paginator"><?php 
                        //echo $this->params['Paginator']->getLinks(); 
                        echo $paginatorPagesLinks;
                        echo $paginatorPageSelect;
                        echo $paginatorLimitSelect;            
                    ?></div><?php
                }
            }
            else {
                $jsOptions = array(
                    'infiniteScroll' => true,
                    //'recordsScrollContainerSelector' => '...', // see here below
                    'recordsContainerSelector' => '.' . $uniqueClass . ' .index',
                    'recordSelector' => '.' . $uniqueClass . ' .index .product',
                    'loadMoreRecordsButtonSelector' => '.' . $uniqueClass . ' .load-more-items',
                    'restoreRecords' => true,
                );
                App::startJsCapture();
                ?><script type="text/javascript">
                jQuery(function(){
                    var options = <?php echo json_encode($jsOptions) ?>;
                    options.recordsScrollContainerSelector = window;
                    new Run.App.Paginator(options);
                }); 
                </script><?php
                App::endJsCapture();
                App::setJsFiles(array(
                    '/app/js/vendors/jquery.min.js',
                    '/app/js/libs/App.js',
                    '/app/js/libs/Arr.js',
                    '/app/js/libs/PhpJs.js',
                    '/app/js/libs/String.js',
                    '/app/js/libs/Number.js',
                    '/app/js/libs/Utility.js',
                    '/app/js/libs/Validate.js', 
                    '/app/js/libs/Sanitize.js',
                    '/app/js/libs/WindowStateManager.js',
                    '/app/js/libs/Paginator.js',
                ));
                // js config for App.js
                App::setJsConfig('App', array(
                    'urlRoot' => App::$urlRoot,
                    'urlLang' => App::$urlLang,
                    'urlBase' => App::$urlBase,
                    'homeSlug' => App::$homeSlug,
                ));
                App::setJsI18nFiles(array('App'));
                ?><div class="paginator"><?php
                    echo $this->params['Paginator']->getLoadMoreButton(array(
                        'label' => __(__FILE__, 'Načítať ďalšie'),
                        'class' => 'button load-more-items',
                    ));
                ?></div><?php
            }            
        }
    }
    // content text after products
    if (
        !empty($content['text'])
        && !empty($content['showText'])
        && $content['showText'] === 'enum_after_products'
    ) {
        ?><div class="index-text"><?php 
            echo $content['text'] 
        ?></div><?php 
    }
?></div><?php
// set product css width according number of columns
if (!empty($columns)) {
    App::startCssCapture();
     ?><style type="text/css">
        .<?php echo $uniqueClass ?> .index .product {
            width: <?php echo 100 / $columns ?>%;
        }
    </style><?php
    App::endCssCapture();
}

App::startJsCapture();
?><script type="text/javascript">
    function setExpandableDescription() {
        var description = jQuery('#<?php echo $uniqueId ?> .index-text-container');
        var descContainer = description.find('.text');
        var button = description.find('.more-button');
        var descContainerHeight = descContainer.outerHeight();
        var descContainerMaxHeight = 60;
        description.removeClass('expandable');
        if (descContainerHeight >= descContainerMaxHeight + 5){
            description.addClass('expandable');
            var collapseButtonText = '<?php echo __( __FILE__, 'Čítať menej' ) ?>';
            var expandButtonText = '<?php echo __( __FILE__, 'Čítať viac' ) ?>';
            descContainer.css('max-height', descContainerMaxHeight + 'px');
            button.click (function() {
                if (!description.hasClass('expanded')){
                    descContainer.css('max-height', descContainerHeight + 'px');
                    jQuery(this).text(collapseButtonText);
                } else {
                    descContainer.css('max-height', descContainerMaxHeight + 'px');
                    jQuery(this).text(expandButtonText);
                }
                description.toggleClass('expanded');
            });
        }
    }

    setExpandableDescription();
</script><?php
App::endJsCapture();

/*/>
App::startJsCapture();
?><script type="text/javascript">
    jQuery(function(){

        function setExpandableCategories() {
            var categories = jQuery('#subcategories');
            var catContainer = jQuery('#subcategories > .list');
            var button = jQuery('#subcategories .expand-button');
            var catContainerHeight = catContainer.outerHeight();
            // var catContainerMaxHeight = 84;
            var catContainerMaxHeight = 38;
            categories.removeClass('expandable');
            if (
                (jQuery(window).width() <= 1000) && (catContainerHeight >= catContainerMaxHeight + 5)
                ){
                categories.addClass('expandable');
                var collapseButtonText = '<?php echo __( $this, 'Collapse subcategories' ) ?>';
                var expandButtonText = '<?php echo __( $this, 'All subcategories' ) ?>';
                catContainer.css('max-height', catContainerMaxHeight + 'px');
                button.click (function() {
                    if (!categories.hasClass('expanded')){
                        catContainer.css('max-height', catContainerHeight + 'px');
                        jQuery(this).text(collapseButtonText);
                    } else {
                        catContainer.css('max-height', catContainerMaxHeight + 'px');
                        jQuery(this).text(expandButtonText);
                    }
                    categories.toggleClass('expanded')
                });
            }
        }

        setExpandableCategories();

    });
</script><?php
App::endJsCapture();//*/
//*/

// RETURN
return;

//
// Code store for other projects
//

