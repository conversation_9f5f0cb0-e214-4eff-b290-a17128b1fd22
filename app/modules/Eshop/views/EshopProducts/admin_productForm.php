<?php
/**
 * Displays product edit form. This is used to:
 * - edit single product
 * - edit many products (bulk edit), see variable $editMany
 * - edit new copied product
 *
 * @param string $this->params['title'] Smart form title
 * @param array $this->params['data'] Smart form data
 * @param Model|array $this->params['Model'] Smart form Model
 * @param string $this->params['lang'] Smart form lang
 * @param array $this->params['products'] Optional. Array of products for bulk edit
 * @param string $this->params['slugProductView'] Optional. Content slug to show bulk products detail
 */

$data = $this->params['data'];
// resolve type of action the form is used for
$action = 'edit';
if (!empty($this->params['products'])) {
    $action = 'editMany';
}
elseif (empty($data['id'])) {
    $action = 'add';
}
$applicableParameters = Sanitize::value($this->params['applicableParameters']);
// if many products are edited then prepare their list html
$productsHtml = '';
if (
    $action === 'editMany'
    && !empty($this->params['slugProductView'])
) {
    Html::startCapture();
    ?><div class="bulk-records"><?php
        foreach ($this->params['products'] as $i => $product) {
            if ($i !== 0) {
                echo ', ';
            }
            $url = App::getUrl(array(
                'locator' => $this->params['slugProductView'],
                'args' => array($product['slug']),
            ));
            ?><a href="<?php echo $url ?>" target="_blank"><?php
                echo $product['name'];
            ?></a><?php
        }
    ?></div><?php
    $productsHtml = Html::endCapture();
    App::startCssCapture();
    ?><style type="text/css">
        .bulk-records {
            margin-bottom: 15px;
        }
        .bulk-records a {
            color: #333;
        }
    </style><?php
    App::endCssCapture();
}

$Product = $this->loadModel('EshopProduct', true);
$availabilities = $Product->getEnumValues('availability');
$units = $Product->getEnumValues('units');
$taxRates = $Product->getEnumValues('tax_rate');
$additionalServices = $Product->getEnumValues('is_additional_service');
$manufacturerGroups = $Product->getEnumValues('manufacturer_group');

$Manufacturer = $this->loadModel('EshopManufacturer', true);
$manufacturers = $Manufacturer->findList(array('fields' => array('EshopManufacturer.name'), 'order' => 'EshopManufacturer.name'));

$ProductType = $this->loadModel('EshopProductType', true);
$productTypes = $ProductType->findList(array(
    'fields' => array('EshopProductType.name'),
    'order' => 'EshopProductType.name',
));
$EshopBrand = $this->loadModel('EshopBrand', true);
$brands = $EshopBrand->findList(array(
    'fields' => array('EshopBrand.name'),
    'order' => 'EshopBrand.name',
));

$ManufacturerRange = $this->loadModel('EshopManufacturerRange', true);
$manufacturerRanges = $ManufacturerRange->findList(array(
    'fields' => array('EshopManufacturerRange.name'),
    'order' => 'EshopManufacturerRange.name',
));

if (!empty($data['discount_from'])) {
    $data['discount_from'] = date('d.m.Y', strtotime($data['discount_from']));
}
if (!empty($data['discount_to'])) {
    $data['discount_to'] = date('d.m.Y', strtotime($data['discount_to']));
}

$vatInfo = App::getSetting('Eshop', 'pricesAreTaxed') ? __a(__FILE__, 'with VAT') : __a(__FILE__, 'without VAT');
if ($action === 'editMany') {
    $emptySelectOption = '--' . __a(__FILE__, 'no change') . '--';
}
else {
    $emptySelectOption = '--' . __a(__FILE__, 'undefined') . '--';
}
$auxCheckboxTempate = '<div class="checkbox"><label>:i:%s</label></div>';

$supplierProductsHtml = null;
if (!empty($data['EshopSupplierProduct'])) {
    App::loadLib('App', 'FormHelper');
    $Form = new FormHelper(array(
        'data' => $data
    ));
    Html::startCapture();
    ?><div class="supplier-products"><?php
        for ($i = 0; $i < count($data['EshopSupplierProduct']); $i++) {
            ?><h2 class="-run-sfo-heading"><?php
                echo $data['EshopSupplierProduct'][$i]['supplier_pid'];
            ?></h2><?php
            ?><div class="row"><?php
                ?><div class="col-sm-4"><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.tax_rate', array(
                        'label' => __a(__FILE__, 'Tax rate')
                    ));
                    ?><br /><?php
                    $purchasePrice = null;
                    if (
                        $data['EshopSupplierProduct'][$i]['price']
                        && $data['EshopSupplierProduct'][$i]['margin_rate']
                    ) {
                        $purchasePrice = round(Eshop::getPurchasePrice(
                            $data['EshopSupplierProduct'][$i]['price'],
                            $data['EshopSupplierProduct'][$i]['margin_rate']
                        ), 2);
                    }
                    echo $Form->display('EshopSupplierProduct.' . $i . '._purchase_price', array(
                        'label' => __a(__FILE__, 'Nákupná cena'),
                        'explicitValue' => $purchasePrice,
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.price', array(
                        'label' => __a(__FILE__, 'Price')
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.discount_price', array(
                        'label' => __a(__FILE__, 'Discount price')
                    ));
                    ?><br /><?php
                    $markup = null;
                    if (
                        $data['EshopSupplierProduct'][$i]['price']
                        && $data['EshopSupplierProduct'][$i]['margin_rate']
                    ) {
                        $markup = round(Eshop::getMarkup(
                            $data['EshopSupplierProduct'][$i]['price'],
                            $data['EshopSupplierProduct'][$i]['discount_rate'],
                            $data['EshopSupplierProduct'][$i]['margin_rate']
                        ), 2);
                    }
                    echo $Form->display('EshopSupplierProduct.' . $i . '._markup', array(
                        'label' => __a(__FILE__, 'Marža'),
                        'explicitValue' => $markup,
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.discount_rate', array(
                        'label' => __a(__FILE__, 'Discount rate')
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.margin_rate', array(
                        'label' => __a(__FILE__, 'Percento rabatu')
                    ));
                    ?><br /><?php
                    $markupRate = null;
                    if (
                        $data['EshopSupplierProduct'][$i]['discount_rate']
                        && $data['EshopSupplierProduct'][$i]['margin_rate']
                    ) {
                        $markupRate = round(Eshop::getMarkupRate(
                            $data['EshopSupplierProduct'][$i]['discount_rate'],
                            $data['EshopSupplierProduct'][$i]['margin_rate']
                        ), 2);
                    }
                    echo $Form->display('EshopSupplierProduct.' . $i . '._markup_rate', array(
                        'label' => __a(__FILE__, 'Percento marže'),
                        'explicitValue' => $markupRate,
                    ));
                ?></div><?php
                ?><div class="col-sm-4"><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.created', array(
                        'label' => __a(__FILE__, 'Date of creation')
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.modified', array(
                        'label' => __a(__FILE__, 'Date of modification')
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.supplier_product_id', array(
                        'label' => __a(__FILE__, 'Supplier code')
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.stock', array(
                        'label' => __a(__FILE__, 'Stock'),
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.availability', array(
                        'label' => __a(__FILE__, 'Availability'),
                        'explicitValue' => __(__FILE__, $data['EshopSupplierProduct'][$i]['availability'])
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.available_from', array(
                        'label' => __a(__FILE__, 'Available from')
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.reprint', array(
                        'label' => __a(__FILE__, 'Reprint'),
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.long_delivery_time', array(
                        'label' => __a(__FILE__, 'Long delivery time'),
                    ));
                    ?><br /><?php
                    echo $Form->display('EshopSupplierProduct.' . $i . '.shipment_time_off_stock', array(
                        'label' => __a(__FILE__, 'Shipment time off stock'),
                    ));
                ?></div><?php
                ?><div class="col-sm-4"><?php
                    echo $Form->image('EshopSupplierProduct.' . $i . '.image_import_source', array(
                        'label' => __a(__FILE__, 'Image'),
                    ));
                ?></div><?php
            ?></div><?php
        }
    ?></div><?php
    $supplierProductsHtml = Html::endCapture();
    App::startCssCapture();
    ?><style type="text/css">
        .supplier-products .display-input {
            display: inline-block;
            margin-left: 0.5em;
        }
        .supplier-products .row:last-child {
            padding-bottom: 15px;
        }
        .supplier-products .display-image-input {
            display: block;
            cursor: pointer;
            max-width: 82px;
        }
        .supplier-products .display-image-input:hover {
            max-width: 100%;
        }
    </style><?php
    App::endCssCapture();
}

App::setCss('.treeselector fieldset{height:500px}');
App::setCss('.itemselector fieldset{height:500px}');
echo Html::smartForm(array(
    'title' => $this->params['title'],
    'data' => $this->params['data'],
    'Model' => $this->params['Model'],
    'lang' => $this->params['lang'],
    'columns' => 4,
    'showAffix' => true,
    'actions' => array(
        'lang' => true,
    ),
    'fields' => array(
        array('if' => $action === 'edit'),
            'id' => array('type' => 'hidden'),
        array('endif'),

        array('if' => $action === 'editMany'),
            // BULK RECORDS
            array(
                'h1' => __a(__FILE__, 'List of edited products'),
                'hint' => __a(__FILE__, 'V nasledovnom formulári na hromadnú úpravu tu úvedených produktov je východzí stav jednotlivých polí nastavený tak, aby sa ich hodnoty pri uložení nezmenili, t.j. aby sa zachovali pôvodné hodnoty nastavené na uvedených produktoch. V prípade textových polí sa pôvodná hodnota zachová, ak pole ostane prázdne. Všade tam, kde sa zadá nová neprázdna hodnota, sa táto prepíše na všetkých tú uvedených produktoch.'),
            ),
            array('row', 'columns' => 1),
                array('col'),
                    array('html' => $productsHtml),
                array('/col'),
            array('/row'),
        array('endif'),

        // BASIC PARAMETERS
        array('h1' => __a(__FILE__, 'Basic parameters')),
        array('row', 'columns' => 2),
            array('col', 'columns' => 2),
                array('row'),
                    array('if' => $action !== 'editMany'),
                        array(
                            'field' => 'name',
                            'label' => __a(__FILE__, 'Name')
                        ),
                        array('if' => $action === 'edit'),
                            array(
                                'field' => 'slug',
                                'label' => __a(__FILE__, 'Slug')
                            ),
                        array('endif'),
                    array('endif'),
                array('/row'),
                array('row'),
                    array('if' => $action !== 'editMany'),
                        array(
                            'field' => 'subtitle',
                            'label' => __a(__FILE__, 'Dodatok názvu'),
                            'hint' => __a(__FILE__, 'Upresňujúci dotatok k názvu produktu. Malo by ísť o krátky súhrn najdôležitejších alebo odlišujúcich parametrov, napr. <code>48x43x122cm</code> alebo <code>výška stredná, dĺžka 4 m</code>.')
                        ),
                    array('endif'),
                    array(
                        'field' => 'run_eshop_product_types_id',
                        'label' => __a(__FILE__, 'Product type'),
                        'type' => 'select',
                        'options' => $productTypes,
                        'empty' => $emptySelectOption,
                    ),
                array('/row'),
                array('if' => $action !== 'editMany'),
                    array('row'),
                        array(
                            'field' => 'code',
                            'label' => __a(__FILE__, 'Internal code')
                        ),
                        array(
                            'field' => 'mrp_code',
                            'label' => __a(__FILE__, 'MRP kód'),
                            'hint' => __a(__FILE__, 'Kód produktu (číslo karty) v MRP')
                        ),
                    array('/row'),
                    array('row'),
                        array(
                            'field' => 'mrp_name',
                            'label' => __a(__FILE__, 'MRP názov'),
                            'hint' => __a(__FILE__, 'Názov produktu v MRP'),
                            'readonly' => true
                        ),
                    array('/row'),
                array('endif'),
                array('row'),
                    array('if' => $action !== 'editMany'),
                        array(
                            'field' => 'ean',
                            'label' => __a(__FILE__, 'EAN')
                        ),
                    array('endif'),
                    array(
                        'field' => 'run_eshop_manufacturers_id',
                        'label' => __a(__FILE__, 'Manufacturer'),
                        'type' => 'select',
                        'options' => $manufacturers,
                        'empty' => $emptySelectOption,
                    ),
                array('/row'),
                array('row'),
                    array(
                        'field' => 'run_eshop_brands_id',
                        'label' => __a(__FILE__, 'Brand'),
                        'type' => 'select',
                        'options' => $brands,
                        'empty' => $emptySelectOption,
                    ),
                array('/row'),
                array('row'),
                    array('col'),
                        array('if' => $action !== 'editMany'),
                            array(
                                'field' => 'image',
                                'label' => __a(__FILE__, 'Image'),
                                'type' => 'file',
                                'hint' => __a(
                                    __FILE__,
                                    'Vyberte obrázok o šírke aspoň 600px. Výška obrázku nie je stanovená.'
                                ),
                            ),
                            array(
                                'field' => 'video_url',
                                'label' => __a(__FILE__, 'Video URL') . (
                                    $data['video_url']
                                    ?
                                    ' ' .
                                    Html::hyperlinkTag(
                                        $data['video_url'],
                                        '<i class="fa fa-external-link"></i>',
                                        array(
                                            'attributes' => array('target' => '_blank')
                                        )
                                    )
                                    :
                                    ''
                                ),
                                'hint' => __a(__FILE__, 'URL adresa súboru videa. Môže tu byť aj URL adresa Youtube alebo Vimeo videa.')
                            ),
                            array('row', 'columns' => 2),
                                array(
                                    'field' => 'active',
                                    'label' => __a(__FILE__, 'Active'),
                                    'type' => 'checkbox'
                                ),
                                array(
                                    'field' => 'translated',
                                    'label' => __a(__FILE__, 'Preložený'),
                                    'hint' => __a(__FILE__, 'Sú všetky texty preložené do jazyka "%s"', $this->params['lang']),
                                    'type' => 'checkbox'
                                ),
                            array('/row'),
                            array(
                                'field' => 'new',
                                'label' => __a(__FILE__, 'Novinka'),
                                'type' => 'checkbox'
                            ),
                        array('else'),
                            array('row', 'columns' => 2),
                                array(
                                    'field' => 'active',
                                    'label' => __a(__FILE__, 'Active'),
                                    'type' => 'radio',
                                    'options' => array(
                                        '' => __a(__FILE__, 'no change'),
                                        '0' => __a(__FILE__, 'no'),
                                        '1' => __a(__FILE__, 'yes'),
                                    ),
                                    'value' => '',
                                ),
                                array(
                                    'field' => 'translated',
                                    'label' => __a(__FILE__, 'Preložený'),
                                    'hint' => __a(__FILE__, 'Sú všetky texty preložené do jazyka "%s"', $this->params['lang']),
                                    'type' => 'radio',
                                    'options' => array(
                                        '' => __a(__FILE__, 'no change'),
                                        '0' => __a(__FILE__, 'no'),
                                        '1' => __a(__FILE__, 'yes'),
                                    ),
                                    'value' => '',
                                ),
                            array('/row'),
                            array(
                                'field' => 'new',
                                'label' => __a(__FILE__, 'Novinka'),
                                'type' => 'radio',
                                'options' => array(
                                    '' => __a(__FILE__, 'no change'),
                                    '0' => __a(__FILE__, 'no'),
                                    '1' => __a(__FILE__, 'yes'),
                                ),
                                'value' => '',
                            ),
                        array('endif'),
                    array('/col'),
                    array('col'),
                        array('if' => $action === 'edit'),
                            array(
                                'field' => 'image',
                                'type' => 'image',
                                'style' => 'height: 118px',
                                'deleteImage' => '/mvc/Eshop/EshopProducts/admin_deleteFile/image/' . Sanitize::value($data['id']),
                            ),
                        array('endif'),
                        /*/
                        array(
                            'field' => 'run_eshop_manufacturer_ranges_id',
                            'label' => __a(__FILE__, 'Manufacturer range'),
                            'type' => 'select',
                            'options' => $manufacturerRanges,
                            'empty' => $emptySelectOption,
                        ),
                        array('if' => $action === 'editMany'),
                            array(
                                'field' => '_remove_run_eshop_manufacturer_ranges_id',
                                'type' => 'checkbox',
                                'template' => Str::fill($auxCheckboxTempate, null, __a(__FILE__, 'remove manufacturer range')),
                                'toggleValue' => 'add',
                                'compatibility' => null,
                            ),
                        array('endif'),
                        /*/
                    array('/col'),
                    /*/
                    array('col'),
                        array(
                            'field' => 'manufacturer_group',
                            'label' => __a(__FILE__, 'Manufacturer import group'),
                            //'type' => 'select',
                            'options' => $manufacturerGroups,
                            'empty' => $emptySelectOption,
                        ),
                    array('/col'),
                    /*/
                array('/row'),
                /*/
                array('row'),
                    array(
                        'field' => 'is_additional_service',
                        'label' => __a(__FILE__, 'Doplnková služba'),
                        'hint' => __a(__FILE__, 'Je tento produkt doplnkovou službou? Ak áno, akou?'),
                        'type' => 'select',
                        'options' => $additionalServices,
                        'empty' => __a(__FILE__, 'No'),
                    ),
                array('/row'),
                //*/
            array('/col'),
            array('col'),
                array(
                    'field' => 'category_ids',
                    'label' => __a(__FILE__, 'Categories'),
//                    'type' => 'treeselector',
                    'type' => 'itemselector',
//                    'options' => '/mvc/Eshop/EshopProductCategories/admin_list',
                    'options' => '/mvc/Eshop/EshopProductCategories/admin_listForItemSelector',
                ),
                array('if' => $action === 'editMany'),
                    array(
                        'field' => '_apply_category_ids',
                        'type' => 'select',
                        'options' => array(
                            '' => __a(__FILE__, 'set selected items'),
                            'add' => __a(__FILE__, 'add selected items'),
                        ),
                    ),
                array('endif'),
            array('/col'),
        array('/row'),
        /*/
        array('row', 'columns' => 2),
            array('col'),
                array(
                    'field' => 'author_ids',
                    'label' => __a(__FILE__, 'Authors'),
                    'type' => 'itemselector',
                    'options' => '/mvc/Eshop/EshopAuthors/admin_getSelectorItems'
                ),
                array('if' => $action === 'editMany'),
                    array(
                        'field' => '_apply_author_ids',
                        'type' => 'select',
                        'options' => array(
                            '' => __a(__FILE__, 'set selected items'),
                            'add' => __a(__FILE__, 'add selected items'),
                        ),
                    ),
                array('endif'),
            array('/col'),
            array('col'),
            array('/col'),
        array('/row'),
        /*/
        // PRICE
        array('h2' => __a(__FILE__, 'Price')),
        array('row', 'columns' => array(2, 2, 2, 2, 2, 2)),
            array('col'),
                array(
                    'field' => 'tax_rate',
                    'label' => __a(__FILE__, 'Tax rate'),
                    'type' => 'select',
                    'options' => $taxRates,
                    'empty' => $action === 'editMany' ? $emptySelectOption : null,
                ),
            array('/col'),
            array('col'),
                array(
                    'field' => 'price',
                    'label' => __a(__FILE__, 'Price') . ' <span class="small">(' . $vatInfo . ')</span>'
                ),
            array('/col'),
            array('col'),
                array(
                    'field' => 'discount_price',
                    'label' => __a(__FILE__, 'Discount price') . ' <span class="small">(' . $vatInfo . ')</span>'
                ),
                array('if' => $action === 'editMany'),
                    array(
                        'field' => '_remove_discount_price',
                        'type' => 'checkbox',
                        'template' => Str::fill($auxCheckboxTempate, null, __a(__FILE__, 'remove discount price')),
                        'toggleValue' => 'add',
                        'compatibility' => null,
                    ),
                array('endif'),
            array('/col'),
            array('col'),
                array(
                    'field' => 'discount_rate',
                    'label' => __a(__FILE__, 'Discount rate')
                ),
                array('if' => $action === 'editMany'),
                    array(
                        'field' => '_remove_discount_rate',
                        'type' => 'checkbox',
                        'template' => Str::fill($auxCheckboxTempate, null, __a(__FILE__, 'remove discount rate')),
                        'toggleValue' => 'add',
                        'compatibility' => null,
                    ),
                array('endif'),
            array('/col'),
            array('col'),
                array(
                    'field' => 'discount_from',
                    'label' => __a(__FILE__, 'Discount from'),
                    'type' => 'date'
                ),
                array('if' => $action === 'editMany'),
                    array(
                        'field' => '_remove_discount_from',
                        'type' => 'checkbox',
                        'template' => Str::fill($auxCheckboxTempate, null, __a(__FILE__, 'remove discount start date')),
                        'toggleValue' => 'add',
                        'compatibility' => null,
                    ),
                array('endif'),
            array('/col'),
            array('col'),
                array(
                    'field' => 'discount_to',
                    'label' => __a(__FILE__, 'Discount to'),
                    'type' => 'date'
                ),
                array('if' => $action === 'editMany'),
                    array(
                        'field' => '_remove_discount_to',
                        'type' => 'checkbox',
                        'template' => Str::fill($auxCheckboxTempate, null, __a(__FILE__, 'remove discount end date')),
                        'toggleValue' => 'add',
                        'compatibility' => null,
                    ),
                array('endif'),
            array('/col'),
        array('/row'),
        /*/
        array('row'),
            array('if' => $action !== 'editMany'),
                array(
                    'field' => 'synchronize_price_with_suppliers',
                    'label' => __a(__FILE__, 'Nastaviť cenu podľa dodávateľov'),
                    'hint' => __a(__FILE__, 'Má sa nastaviť cena a zľavnéná cena (percento zľavy) automaticky podľa cien produktu u dodávateľov?'),
                ),
            array('else'),
                array(
                    'field' => 'synchronize_price_with_suppliers',
                    'label' => __a(__FILE__, 'Nastaviť cenu podľa dodávateľov'),
                    'hint' => __a(__FILE__, 'Má sa nastaviť cena a zľavnéná cena (percento zľavy) automaticky podľa cien produktu u dodávateľov?'),
                    'type' => 'radio',
                    'options' => array(
                        '' => __a(__FILE__, 'no change'),
                        '0' => __a(__FILE__, 'no'),
                        '1' => __a(__FILE__, 'yes'),
                    ),
                    'value' => '',
                ),
            array('endif'),
        array('/row'),
        /*/

        // STOCK
        array('h2' => __a(__FILE__, 'Stock')),
        array('row'),
            'stock' => array(
                'label' => __a(__FILE__, 'On stock'),
                //'readonly' => true,
                //'disabled' => 'disabled'
            ),
            'units' => array(
                'label' => __a(__FILE__, 'Units'),
                'type' => 'select',
                'options' => $units,
                'empty' => $action === 'editMany' ? $emptySelectOption : null,
            ),
            'stock_location_code' => array(
                'label' => __a(__FILE__, 'Stock location code'),
            ),
            /*/
            'shop_location_code' => array(
                'label' => __a(__FILE__, 'Shop location code'),
            ),
            /*/
            array('if' => $action !== 'editMany'),
                array(
                    'field' => 'oversized',
                    'label' => __a(__FILE__, 'Nadrozmerný balík'),
                ),
            array('else'),
                array(
                    'field' => 'oversized',
                    'label' => __a(__FILE__, 'Nadrozmerný balík'),
                    'type' => 'radio',
                    'options' => array(
                        '' => __a(__FILE__, 'no change'),
                        '0' => __a(__FILE__, 'no'),
                        '1' => __a(__FILE__, 'yes'),
                    ),
                    'value' => '',
                ),
            array('endif'),
        array('/row'),
        array('row'),
            'availability' => array(
                'label' => __a(__FILE__, 'Availability'),
                'type' => 'select',
                'options' => $availabilities,
                'empty' => $action === 'editMany' ? $emptySelectOption : null,
            ),
            'available_from' => array(
                'label' => __a(__FILE__, 'Available from'),
                'type' => 'date'
            ),
            'shipment_time_off_stock' => array(
                'label' => __a(__FILE__, 'Shipment time off stock'),
                'hint' => __a(__FILE__, 'Number of days to ship the order in case that the product is not on stock'),
            ),
            /*/
            'supplier_info' => array(
                'label' => __a(__FILE__, 'Dodávateľ (info)'),
                'hint' => __a(__FILE__, 'Info naimportované z MRP'),
            ),
            /*/
            array('if' => $action !== 'editMany'),
                /*/
                array(
                    'field' => 'reprint',
                    'label' => __a(__FILE__, 'Reprint'),
                ),
                /*/
                array(
                    'field' => 'long_delivery_time',
                    'label' => __a(__FILE__, 'Long delivery time'),
                ),
                /*/
                'supplier_pid' => array(
                    'label' => __a(__FILE__, 'Dodávateľ (import)'),
                    'hint' => __a(__FILE__, 'Podľa ktorého importného dodávateľa je nastavená cena (ak je to povolené) a dostupnosť?'),
                ),
                /*/
            array('else'),
                /*/
                array(
                    'field' => 'reprint',
                    'label' => __a(__FILE__, 'Reprint'),
                    'type' => 'radio',
                    'options' => array(
                        '' => __a(__FILE__, 'no change'),
                        '0' => __a(__FILE__, 'no'),
                        '1' => __a(__FILE__, 'yes'),
                    ),
                    'value' => '',
                ),
                /*/
                array(
                    'field' => 'long_delivery_time',
                    'label' => __a(__FILE__, 'Long delivery time'),
                    'type' => 'radio',
                    'options' => array(
                        '' => __a(__FILE__, 'no change'),
                        '0' => __a(__FILE__, 'no'),
                        '1' => __a(__FILE__, 'yes'),
                    ),
                    'value' => '',
                ),
            array('endif'),
        array('/row'),

        // PRODUCTS DESCRIPTION
        array('h2' => __a(__FILE__, 'Product description')),
        array('row', 'columns' => 1),
//                        array(
//                            'field' => 'short_description',
//                            'label' => __a(__FILE__, 'Short description'),
//                            'type' => 'textarea',
//                            'style' => 'height: 109px'
//                        ),
                array(
                    'field' => 'description',
//                            'label' => __a(__FILE__, 'Description'),
                    'type' => 'editor',
                    'options' => array(
                        'toolbar' => 'ProductFull',
                    )
                ),
        array('/row'),
//        array('row', 'columns' => 1),
//            array(
//                'field',
//                //'label' => __a(__FILE__, 'Product description'),
//                'type' => 'contentBlocks',
//                'ownerModel' => 'Eshop.EshopProduct',
//                'ownerId' => Sanitize::value($data['id']),
//                'contentBlockModels' => 'ContentBlock.*',
//                //'avoidContentBlockModels' => 'ContentBlock.ContentBlockSome',
//            ),
//        array('/row'),

        // TECHNICAL PARAMETERS
        array(
            'h2' => __a(__FILE__, 'Technické parametre'),
            'hint' => __(__FILE__, 'Textový súhrn parametrov produktu. Do tohto pola sa ukladajú napríklad tech_data_html z Frankana importu')
        ),
        array('row', 'columns' => 1),
            array(
                'field' => 'tech_data_html',
                'type' => 'editor',
                'options' => array(
                    'toolbar' => 'PlainText',
                )
            ),
        array('/row'),


        // SEO PARAMETERS
        array(
            'h1' => __a(__FILE__, 'SEO parameters'),
            'hint' => __a(__FILE__, 'Parameters for search engines, e.g. Google.com, Yahoo.com or Zoznam.cz')
        ),
        array('row', 'columns' => array(3,6,3)),
            'seo_title' => array(
                'label' => __a(__FILE__, 'SEO title'),
                'hint' => __a(__FILE__, 'Title is displayed in browser tab and in Google search results. It is one of most important texts for Google on your page'),
            ),
            'seo_description' => array(
                'label' => __a(__FILE__, 'SEO description'),
                'type' => 'textarea',
                'style' => 'height: 34px',
                'hint' => __a(__FILE__, 'This text is mostly used by Google in search results as text under result title. Sometimes Google decides to use some other text generated from page content'),
            ),
            'seo_keywords' => array(
                'label' => __a(__FILE__, 'SEO keywords'),
                'hint' => __a(__FILE__, 'Google does not consider much keywords. Other search engines may.')
            ),
        array('/row'),

        array('if' => $action !== 'editMany'),
            // FILTERABLE PARAMETERS
           array(
               'h1' => __a(__FILE__, 'Filtrovateľné parametre'),
               'hint' => __a(__FILE__, 'Výber týchto parametrov sa robí na základe hodnoty poľa "Parametre produktov" kategórií do ktorých je produkt zaradený. Ak je táto sekcia prázdna tak kategórie, do ktorých je produkt zaradený, nemajú zadané žiadne "Parametre produktov".'),
            ),
            // do not use explicit rows here as parameters are displayed dynamically
            array('if' => isset($applicableParameters['color'])),
                'color' => array(
                    'label' => __a(__FILE__, 'Color'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['material_color'])),
                'material_color' => array(
                    'label' => __a(__FILE__, 'Material color'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['dimensions'])),
                'dimensions' => array(
                    'label' => __a(__FILE__, 'Dimensions'),
                    'hint' => __a(__FILE__, 'Popis rozmerov produktu (šírka x výška x hrúbka), napr. <code>21 x 29.7 x 5 cm</code>. Ak nie sú zadané jednotky, tak jednotlivé rozmery by mali byť v [%s]. Ak sú zadané jednotky (viď uvedený príklad), tak sa jednotlivé rozmery pri uložení automaticky prepočítajú na [%s]', $this->getSetting('EshopProduct.dimensionsUnits'), $this->getSetting('EshopProduct.dimensionsUnits')),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['diameter'])),
                'diameter' => array(
                    'label' => __a(__FILE__, 'Diameter'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['marquee_height'])),
                'marquee_height' => array(
                    'label' => __a(__FILE__, 'Marquee height'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['length'])),
                'length' => array(
                    'label' => __a(__FILE__, 'Length'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['extension'])),
                'extension' => array(
                    'label' => __a(__FILE__, 'Extension length'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['material'])),
                'material' => array(
                    'label' => __a(__FILE__, 'Material'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['sail_width'])),
                'sail_width' => array(
                    'label' => __a(__FILE__, 'Sail width'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['in_series'])),
                'in_series' => array(
                    'label' => __a(__FILE__, 'In series'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['carrying_capacity'])),
                'carrying_capacity' => array(
                    'label' => __a(__FILE__, 'Carrying capacity'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['pressure'])),
                'pressure' => array(
                    'label' => __a(__FILE__, 'Pressure'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['power'])),
                'power' => array(
                    'label' => __a(__FILE__, 'Power'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['electric_current'])),
                'electric_current' => array(
                    'label' => __a(__FILE__, 'Electric current'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['inner_frame'])),
                'inner_frame' => array(
                    'label' => __a(__FILE__, 'Inner frame'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['outer_frame'])),
                'outer_frame' => array(
                    'label' => __a(__FILE__, 'Outer frame'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['roof_thickness'])),
                'roof_thickness' => array(
                    'label' => __a(__FILE__, 'Roof thickness'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['inner_dimension'])),
                'inner_dimension' => array(
                    'label' => __a(__FILE__, 'Inner dimension'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['volume'])),
                'volume' => array(
                    'label' => __a(__FILE__, 'Volume'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['voltage'])),
                'voltage' => array(
                    'label' => __a(__FILE__, 'Voltage'),
                ),
            array('endif'),
            array('if' => isset($applicableParameters['capacity'])),
                'capacity' => array(
                    'label' => __a(__FILE__, 'Capacity'),
                ),
            array('endif'),
            'variant' => array(
                'label' => __a(__FILE__, 'Variants'),
                'type' => 'text',
                'hint' => __a(__FILE__, 'Provide product variants separated by ":s:", e.g. blue:s:red:s:green', array('s' => App::getSetting('App', 'attributeValuesSeparator')))
            ),
            array(
                'field' => 'color',
                'label' => __a(__FILE__, 'Color')
            ),
            array(
                'field' => 'size',
                'label' => __a(__FILE__, 'Size')
            ),
            array('row', 'columns' => 2),
            array('col'),
                array(
                    'field' => 'variant_ids',
                    'label' => __a(__FILE__, 'Varianty produktu'),
                    'hint' => __a(__FILE__, 'Varianty k aktuálne upravovanému produktu. Napr. k sivému mobilnému telefónu je variantom ten istý mobilný telefón (čo sa týka technických parametrov) ale biely. Toto zadávanie variantov je vhodné použiť ak je jednoduché textové zadávanie variantov v poli "Varianty" nedostačujúce.'),
                    'type' => 'itemselector',
                    'options' => '/mvc/Eshop/EshopProducts/admin_list'
                ),
                array('if' => $action === 'editMany'),
                    array(
                        'field' => '_apply_variant_ids',
                        'type' => 'select',
                        'options' => array(
                            '' => __a(__FILE__, 'set selected items'),
                            'add' => __a(__FILE__, 'add selected items'),
                        ),
                    ),
                array('endif'),
            array('/col'),
        array('/row'),
        array('if' => !empty($data['variant_ids'])),
            array('row', 'columns' => 2),
                array(
                    'field' => 'variants_common_name',
                    'label' => __a(__FILE__, 'Spoločný názov variantov'),
                    'hint' => __a(__FILE__, 'Spoločný názov pre všetky varianty v skupine variantov, ktorý sa bude zobrazovať v zozname produktov.')
                ),
            array('/row'),
        array('endif'),
            // force end row for above implicit dynamic rows by inserting one explicit row
            ('row'),
            ('/row'),
        array('endif'),

        // OTHER PARAMETERS
        array('h1' => __a(__FILE__, 'Other parameters')),
        array('if' => $action !== 'editMany'),
            array('row'),
                'weight' => array(
                    'label' => __a(__FILE__, 'Weight'),
                    'hint' => __a(__FILE__, 'Hmotnosť produktu [%s], napr. <code>3.25</code>', $this->getSetting('EshopProduct.weightUnits')),
                ),
                /*/
                'dimensions' => array(
                    'label' => __a(__FILE__, 'Dimensions'),
                    'hint' => __a(__FILE__, 'Popis rozmerov produktu (šírka x výška x hrúbka), napr. <code>21 x 29.7 x 5 cm</code>. Ak nie sú zadané jednotky, tak jednotlivé rozmery by mali byť v [%s]. Ak sú zadané jednotky (viď uvedený príklad), tak sa jednotlivé rozmery pri uložení automaticky prepočítajú na [%s]', $this->getSetting('EshopProduct.dimensionsUnits'), $this->getSetting('EshopProduct.dimensionsUnits')),
                ),
                /*/
            array('/row'),
            array('row', 'columns' => 1),
                'note' => array(
                    'label' => __a(__FILE__, 'Internal note'),
                    'type' => 'textarea',
                    'style' => 'height: 34px'
                ),
            array('/row'),
        array('endif'),
        array('row', 'columns' => 2),
            array('col'),
                array(
                    'field' => 'accessory_ids',
                    'label' => __a(__FILE__, 'Accessory products'),
                    'hint' => __a(__FILE__, 'Accessory products which can be used as accessories to actually edited product. E.g. mobile case or charger is an accessory for mobile phone'),
                    'type' => 'itemselector',
                    'options' => '/mvc/Eshop/EshopProducts/admin_list'
                ),
                array('if' => $action === 'editMany'),
                    array(
                        'field' => '_apply_accessory_ids',
                        'type' => 'select',
                        'options' => array(
                            '' => __a(__FILE__, 'set selected items'),
                            'add' => __a(__FILE__, 'add selected items'),
                        ),
                    ),
                array('endif'),
            array('/col'),
            array('col'),
                array(
                    'field' => 'related_ids',
                    'label' => __a(__FILE__, 'Related products'),
                    'hint' => __a(__FILE__, 'Related products which can be used instead of actually edited product. E.g. another mobile phone is a related product to mobile phone.'),
                    'type' => 'itemselector',
                    'options' => '/mvc/Eshop/EshopProducts/admin_list'
                ),
                array('if' => $action === 'editMany'),
                    array(
                        'field' => '_apply_related_ids',
                        'type' => 'select',
                        'options' => array(
                            '' => __a(__FILE__, 'set selected items'),
                            'add' => __a(__FILE__, 'add selected items'),
                        ),
                    ),
                array('endif'),
            array('/col'),
            /*/
            array('col'),
                array(
                    'field' => 'group_ids',
                    'label' => __a(__FILE__, 'Product groups'),
                    'type' => 'itemselector',
                    'options' => '/mvc/Eshop/EshopProductGroups/admin_getSelectorItems'
                ),
                array('if' => $action === 'editMany'),
                    array(
                        'field' => '_apply_group_ids',
                        'type' => 'select',
                        'options' => array(
                            '' => __a(__FILE__, 'set selected items'),
                            'add' => __a(__FILE__, 'add selected items'),
                            'remove' => __a(__FILE__, 'remove selected items'),
                        ),
                    ),
                array('endif'),
            array('/col'),
            /*/
        array('/row'),
//        array('if' => $action !== 'editMany'),
            array('row', 'columns' => 1),
                'heureka_category_name' => array(
                    'label' => __a(__FILE__, 'Názov kategórie pre Heuréku'),
                    'type' => 'text',
                    'hint' => __a(
                        __FILE__,
                        'Ak je vyplnené potom sa použije namiesto názvu kategórie pri vytváraní XML súboru pre Heureka.sk, napr. <code>Heureka.sk | Oblečenie a móda | Pánske oblečenie | Pánske tričká</code> (t.j. obsah tagu <code>&lt;CATEGORY_FULLNAME&gt;</code>). Zoznam všetkých dostupných viď <a href="%s" target="_blank">tu</a>',
                        'https://sluzby.heureka.sk/napoveda/strom-kategorii-heureka-sk/'
                    ),
                ),
            array('/row'),
//        array('endif'),
        array('if' => $action === 'edit'),
            // PHOTOGALLERY
            array('h1' => __a(__FILE__, 'Photogallery')),
            array('row', 'columns' => 1),
                'EshopProductImage.file' => array(
                    'type' => 'gallery',
                    'options' => array(
                        'foreignKey' => Sanitize::value($data['id']),
                        'nameField' => 'EshopProductImage.name',
                        'fields' => array(
                            'EshopProductImage.file' => array('label' => 'Image'),
                            'EshopProductImage.name' => array('label' => 'Name'),
                        ),
                        'actions' => array(
                            'load' => '/mvc/Eshop/EshopProductImages/admin_load',
                            'move' => '/mvc/Eshop/EshopProductImages/admin_move',
                            'add' => array(
                                'label' => __a(__FILE__, 'Add new image'),
                                'url' => '/mvc/Eshop/EshopProductImages/admin_add',
                            ),
                            'update' => array(
                                'label' => __a(__FILE__, 'Update image'),
                                'url' => '/mvc/Eshop/EshopProductImages/admin_update',
                            ),
                            'delete' => array(
                                'label' => __a(__FILE__, 'Delete image'),
                                'url' => '/mvc/Eshop/EshopProductImages/admin_delete',
                                'confirmMessage' => __a(__FILE__, 'Please confirm removal of image'),
                            ),
                        ),
                        //'fileMaxSize' => '50KB',
                    )
                ),
            array('/row'),
        array('endif'),

        array('if' => $action !== 'editMany'),
            // IMPORT
            /*/
            array('if' => !empty($supplierProductsHtml)),
                array(
                    'h1' => __a(__FILE__, 'Import'),
                    'hint' => __a(__FILE__, 'Dostupnosť produktu je nastavovaná na základe parametrov od nasledovných dodávateľov. Ceny su uvedené len orientačne.'),
                ),
                array('html' => $supplierProductsHtml),
            array('endif'),
            /*/
            // INFO
            array('h1' => __a(__FILE__, 'Info')),
            array('row'),
                array(
                    'field' => 'id',
                    'label' => __a(__FILE__, 'Id'),
                    'type' => 'display',
                ),
                array(
                    'field' => 'created',
                    'label' => __a(__FILE__, 'Date of creation'),
                    'type' => 'display',
                ),
                array(
                    'field' => 'modified',
                    'label' => __a(__FILE__, 'Date of modification'),
                    'type' => 'display',
                ),
                array('if' => App::getUser('Group.pid') === 'admins'),
                    array(
                        'field' => 'deleted',
                        'label' => __a(__FILE__, 'Date of deletion'),
                        'type' => 'text'
                    ),
                array('endif'),
            array('/row'),
        array('endif'),
    ),
));