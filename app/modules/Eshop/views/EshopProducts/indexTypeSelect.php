<?php
// set default option
$type = 'brief';
//if (!empty($_SESSION['_eshop']['EshopProducts']['indexType'])) {
//    $type = $_SESSION['_eshop']['EshopProducts']['indexType'];
//}
if (!empty($_COOKIE['epit'])) {
    $type = $_COOKIE['epit'];
}
// add here nore options if you need
$options = array('brief', 'detailed');
$uniqueClass = uniqid('products-index-type-select-');
?><div class="products-index-type-select <?php echo $uniqueClass ?>"><?php 
    foreach ($options as $option) {
        $class = ' option-' . $option;
        $checked = '';
        if ($type === $option) {
            $class .= ' active';
            $checked = 'checked="checked" ';
        }
        if ($option == 'brief') {
            $faIcon = 'fa-th';
        } else if ($option == 'detailed') {
            $faIcon = 'fa-th-list';
        }
        ?><label class="option<?php echo $class ?>"><?php 
            ?><input <?php 
                ?>type="radio" <?php 
                ?>value="<?php echo $option ?>" <?php 
                echo $checked 
                ?>style="display: none;"<?php 
            ?>><?php
            ?><i class="fa <?php echo $faIcon; ?>"></i><?php
        ?></label><?php
    }
?></div><?php
App::startJsCapture();
?><script type="text/javascript">
jQuery(function() {

jQuery('.<?php echo $uniqueClass ?> input').on('change', function() {
    setCookie('epit', $(this).val());
    window.location.reload();
});

});
</script><?php
App::endJsCapture();
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
));

