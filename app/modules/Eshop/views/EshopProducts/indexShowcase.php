<?php
$this->displayOriginComment = true;
// if no products neither no message to display then return nothing
if (
    empty($this->params['products'])
    && empty($this->params['emptyIndexMessage'])
) {
    return;
}
$columns = $this->params['columns'];
$content = Sanitize::value($this->params['content']);
$uniqueClass = uniqid('products-index-');
?><div class="showcase-products-index products-index <?php echo $uniqueClass ?>"><?php
    // content text before products
    if (
        !empty($content['text'])
        && !empty($content['showText'])
        && (
            $content['showText'] === 'enum_without_products'
            || $content['showText'] === 'enum_before_products'
        )
    ) {
        ?><div class="index-text"><?php 
            echo $content['text'] 
        ?></div><?php 
    }
    // products
    if (
        empty($content['text'])    
        || empty($content['showText'])
        || $content['showText'] !== 'enum_without_products'
    ) {
        if (
            !empty($this->params['products'])
            && isset($this->params['Paginator'])
            &&  (
                !empty($this->params['showFilterSelect'])
                || 
                !empty($this->params['showSortSelect'])
            )
        ) {
            ?><div class="index-controls"><?php 
    //            // filter select
    //            if ($this->params['showFilterSelect']) {
    //                $this->params['manufacturers'] = array('' => __(__FILE__, 'choose a value...')) + $this->params['manufacturers'];
    //                echo $this->params['Paginator']->getFilterSelect(array(
    //                    'field' => 'run_eshop_manufacturers_id',
    //                    'label' => __(__FILE__, 'Manufacturer'),
    //                    'class' => 'filter-select',
    //                    'options' => $this->params['manufacturers'],
    //                ));
    //            }
                // sort select 
                if ($this->params['showSortSelect']) {
                    echo $this->params['Paginator']->getSortSelect(array(
                        'label' => __(__FILE__, 'Sort by'),
                        'class' => 'sort-select',
                        'options' => array(
                            '' => __(__FILE__, 'choose a value...'),
//                            'name:ASC' => __(__FILE__, 'name ascendinng'),
//                            'name:DESC' => __(__FILE__, 'name descending'),
                            'actual_price:ASC' => __(__FILE__, 'price ascendinng'),
                            'actual_price:DESC' => __(__FILE__, 'price descending'),
                            'EshopManufacturer.name:ASC' => __(__FILE__, 'manufacturer ascendinng'),
                            'EshopManufacturer.name:DESC' => __(__FILE__, 'manufacturer descending'),
                        ),
                        'resetParams' => 'sort',
                    ));
                }
            ?></div><?php
        }
        ?><div class="index showcaseslider"><?php
            if (empty($this->params['products'])) {
                echo App::loadElement('App', 'emptyIndexMessage', array('message' => $this->params['emptyIndexMessage']));
            }
            else {
                $this->params['products'] = array_values($this->params['products']);
                $lastIndex = count($this->params['products']) - 1;
                foreach ($this->params['products'] as $i => $product) { 
                    $urlAddToCart = App::getUrl(array(
                        'module' => $this->module,
                        'controller' => 'EshopCarts',
                        'action' => 'addProducts',
                        'args' => array($product['id'])
                    ));
                    $urlShowDetail = App::getUrl(array(
                        'locator' => $this->params['slugProductView'],
                        'args' => array($product['slug'])
                    ));
                    // create row starts if number of columns is provided
                    if (
                        !empty($columns)
                        && $i % $columns == 0
                    ) {
                        ?><div class="index-row"><?php
                    }
                    // get row position class if number of columns is provided
                    $productClass = '';
                    if (!empty($columns)) {
                        if ($i % $columns == 0) {
                            $productClass = ' first';
                        }
                        elseif (($i + 1) % $columns == 0) {
                            $productClass = ' last';
                        }
                    }
                    $productRepresentsVariants = !empty($product['variants_common_name']);
                    $productName = $productRepresentsVariants ? $product['variants_common_name'] : $product['name'];
                    if ($productRepresentsVariants) {
                        $productClass .= ' represents-variants';
                    }
                    // product block
                    ?><div class="product<?php echo $productClass ?>"<?php echo $this->params['SmartAdminLauncher']->markRecord($product['id']) ?>><?php
                        ?><div class="spacer"><?php 
                            // image
                            if (!empty($product['image']['large'])) {
                                ?><div class="image"><?php 
                                    ?><div class="shadow-spacer"><?php
                                        ?><div class="shadow"></div><?php
                                    ?></div><?php
                                    ?><div class="wrapper"><?php 
                                        // special offers
                                        echo $this->loadElement('productSpecialOfferLabels', $this->params + array('product' => $product));
                                        // product image
                                        ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'More info') ?>" class="product-image"><?php
                                            ?><img <?php 
                                                ?>src="<?php echo $product['image']['large']; ?>" <?php 
                                                ?>alt="<?php echo $productName; ?>"<?php 
                                            ?>/><?php
                                        ?></a><?php
                                    ?></div><?php
                                ?></div><?php
                            }
                            // title
                            ?><h3 class="title"><?php
                                ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'More info') ?>"><?php
                                    echo $productName;
                                ?></a><?php
                            ?></h3><?php
                            // info
                            ?><div class="info"><?php 
                                //author
                                if (!empty($product['EshopAuthor'])) {
                                    ?><div class="authors"><?php 
                                        foreach ($product['EshopAuthor'] as $j => $author) {
                                            if ($j > 0) {
                                                echo ', ';
                                            }
                                            if (!empty($this->params['slugAuthorProducts'])) {                                                
                                                $urlAuthorProducts = App::getUrl(array(
                                                    'locator' => $this->params['slugAuthorProducts'],
                                                    'args' => array($author['slug'])
                                                ));
                                                ?><a href="<?php echo $urlAuthorProducts ?>" title="<?php echo __(__FILE__, 'See products of author %s', $author['name']) ?>"><?php 
                                                    echo str_replace(' ', '&nbsp;', $author['name']);
                                                ?></a><?php
                                            }
                                            else {
                                                echo str_replace(' ', '&nbsp;', $author['name']);
                                            }
                                        }
                                    ?></div><?php 
                                }
                                /*
                                // price
                                ?><div class="actual-price"><?php 
                                    //echo __(__FILE__, 'Our price') . ' '; 
                                    $formatedPrice = Eshop::formatPrice($product['price_actual_taxed'], array(
                                        'symbol' => '<span class="currency">' . Eshop::getActualCurrency('symbol') . '</span>')
                                    );
                                    if ($product['variants_lowest_price']) {
                                        echo __(__FILE__, 'Od %s', $formatedPrice);
                                    } else {
                                        echo $formatedPrice;
                                    }
                                ?></div><?php
                                // saving rate
                                if (!empty($product['savings_rate'])) {
                                    ?><div class="discount-label"><?php 
                                        echo __(__FILE__, 'Discount');
                                    ?></div><?php
                                    ?><div class="savings-rate"><?php 
                                        //echo '-' . floor($product['savings_rate']) . '%';
                                    ?></div><?php
                                }
                                */
                                // disponibility
                                if ($this->params['showDisponibility'] && !$productRepresentsVariants) {
                                    $disponibility = '';
                                    switch ($product['disponibility']) {
                                        case EshopProduct::STOCK:
                                            $disponibility = __(__FILE__, 'Available on stock');
                                            break;
                                        case EshopProduct::SUPPLIER:
                                            $disponibility = __(__FILE__, 'Available at supplier');
                                            break;
                                        case EshopProduct::ON_DEMAND:
                                            $disponibility = __(__FILE__, 'On demand');
                                            break;
                                        case EshopProduct::SOLDOUT:
                                            $disponibility = __(__FILE__, 'Sold out');
                                            break;
                                        case EshopProduct::PRESALE:
                                            $disponibility = __(__FILE__, 'Presale');
                                            break;
                                        default:
                                        break;
                                    }
                                    ?><div class="disponibility"><?php 
                                        echo $disponibility;
                                    ?></div><?php
                                }
                            ?></div><?php
                            // button to see variants (replacement for add to cart button in case of variants)
                            if (
                                $this->params['showAddToCartButton']
                                && $productRepresentsVariants
                            ) {
                                ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'Pozrite si varianty') ?>" class="button"><?php
                                    echo __(__FILE__, 'Pozrite si varianty');
                                ?></a><?php
                            }
                            // add to cart button
                            else if (
                                $this->params['showAddToCartButton']
                                && $product['disponibility'] !== EshopProduct::SOLDOUT
                            ) { 
                                ?><form action="<?php echo $urlAddToCart; ?>" method="post" class="add-to-cart"><?php
                                    ?><input type="hidden" name="data[_target]" value="Eshop.EshopCarts.addProducts" /><?php
                                    ?><input type="hidden" name="data[id]" value="<?php echo $product['id']; ?>" /><?php
                                    ?><input type="hidden" name="data[amount]" value="1" /><?php
                                    if (!empty($product['special_offer_id'])) {
                                        ?><input type="hidden" <?php 
                                            ?>name="data[special_offer_id]" <?php 
                                            ?>value="<?php echo $product['special_offer_id']; ?>"<?php 
                                        ?>/><?php
                                    }
                                    ?><button type="submit" data-action="buy"><?php 
                                        echo __(__FILE__, 'Insert to cart');
                                    ?></button><?php
                                ?></form><?php
                            }
                        ?></div><?php
                    ?></div><?php
                    // create row ends if number of columns is provided
                    if (
                        !empty($columns)
                        && (
                            $i == $lastIndex
                            || ($i + 1) % $columns == 0
                        )
                    ) {
                        ?></div><?php
                    } 
                }
            }
        ?></div><?php
        // paginator
        if (isset($this->params['Paginator'])) {
            ?><div class="paginator"><?php 
                //echo $this->params['Paginator']->getLinks(); 
                echo $this->params['Paginator']->getPagesLinks();
                // page select
                echo $this->params['Paginator']->getPageSelect(array(
                    'label' => __(__FILE__, 'Page'),
                    'class' => 'page-select',
                ));
                // limit select
                echo $this->params['Paginator']->getLimitSelect(array(
                    'label' => __(__FILE__, 'Limit'),
                    'class' => 'limit-select',
                ));            
            ?></div><?php
        }
    }
    // content text after products
    if (
        !empty($content['text'])
        && !empty($content['showText'])
        && $content['showText'] === 'enum_after_products'
    ) {
        ?><div class="index-text"><?php 
            echo $content['text'] 
        ?></div><?php 
    }
?></div><?php
// set product css width according number of columns
if (!empty($columns)) {
    App::startCssCapture();
 ?><style type="text/css">
        .<?php echo $uniqueClass ?> .index .product {
            width: <?php echo 100 / $columns ?>%;
        }
    </style><?php
    App::endCssCapture();
}
App::startJsCapture();
?><script type="text/javascript">
    jQuery(window).load(function(){
        // show image shadows but only when window is loaded
        jQuery('.<?php echo $uniqueClass ?> .image .shadow').css({visibility: 'visible'});
        
        // align item images on bottom (using .spacer margin-top)
        // and set the same heights of all .item block to make borders looks ok
        //
        // If jQuery(window).load() does not work correctly the see this http://stackoverflow.com/questions/910727/jquery-event-for-images-loaded
        // and connect events on image loading
        function align() {
            var index = jQuery('.<?php echo $uniqueClass ?>'), indexHeight, images = index.find('.image'),
            maxImageHeight = 0, items = index.find('.product'), maxItemHeight = 0,
            numberOfItemsInRow = Math.round(index.width() / items.first().width()) /*NOTUSEDYET*/;
            // reset items explicit height set before here below
            images.closest('.spacer').css('margin-top', '0');
            items.css('height', '');
            // if there is just one item block per index width do nothing (just let reset the index height to implicit here above)
            if (items.first().width() * 2 > index.width() * 1.5) {
                return;
            }
            // get maxItemHeight
            items.each(function(){
                var item = jQuery(this), itemHeight;
                var itemHeight = item.outerHeight();
                if (itemHeight > maxItemHeight) {
                    maxItemHeight = itemHeight;
                }
            });
            // align item images on bottom (using .spacer margin-top)
            images.each(function(){
                var image = jQuery(this), imageHeight;
                var imageHeight = image.height();
                if (imageHeight > maxImageHeight) {
                    maxImageHeight = imageHeight;
                }
            });
            images.each(function(){
                var image = jQuery(this), imageHeight = image.height(), marginTop = maxImageHeight - imageHeight;
                if (marginTop !== 0) {
                    image.closest('.spacer').css('margin-top', marginTop + 'px');
                }
            });
            // set the same height for all items to align left/right border
            index.find('.product').outerHeight(maxItemHeight); 
        };
        // call align on window load
        align();
        // call align on each window resize
        jQuery(window).resize(function(){align();});
    });

    $(document).ready(function() {
        $('.showcaseslider').bxSlider({
            auto: false,
            pager: false,
            responsive: true,
            slideSelector: 'div.product',
            moveSlides: 1,
            slideWidth: 300,
            autoReload: true,
            breaks: [{screen:0, slides:1},{screen:640, slides:2},{screen:960, slides:3},{screen: 1280, slides:4}]
        });
    });
</script><?php
App::endJsCapture();
App::setCssFiles(array(
    '/app/js/vendors/bxslider/jquery.bxslider.css',
));
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
    '/app/js/vendors/bxslider/jquery.bxslider.min.js',
));

// RETURN
return;

//
// Code store for other projects
//

