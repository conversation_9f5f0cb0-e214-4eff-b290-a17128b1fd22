<?php
$this->displayOriginComment = true;
// if there are no products in top level category (there is nothing to filter) then 
// do not display filter at all, stay at the categories menu
if ($this->params['noProductsInTopcategory']) {
//    echo App::loadControllerAction('Eshop', 'EshopProductCategories', 'index', array('root_pid' => 'categories'), array(), App::$params);
    return;
}

$menuId = uniqid('product-categories-menu-');
?><div id="<?php echo $menuId ?>" style="display:none;"><?php 
//    echo App::loadControllerAction('Eshop', 'EshopProductCategories', 'index', array('root_pid' => 'categories'), array(), App::$params);
?></div><?php
$filterId = uniqid('products-filter-');
?><aside id="<?php echo $filterId ?>" class="products-filter"><?php
    /*/
    ?><h1 class="title"><?php 
        echo $this->params['topCategory']['name'];
        ?><a href="/" class="close" rel="nofollow">×</a><?php
    ?></h1><?php
    /*/
    ?><div class="product-filters-content-spacer clearfix"><?php 
        /*if (!empty($this->params['categories'])) {
            ?><section class="navigation"><?php
                ?><section><?php
                    ?><ul class="level-01"><?php
                        $this->params['categories'] = array_values($this->params['categories']);
                        $lastIndex = count($this->params['categories']) - 1;
                        foreach ($this->params['categories'] as $i => $category) {
                            $class = '';
                            if ($i === 0) {
                                $class = 'first';
                            }
                            if ($i === $lastIndex) {
                                $class .= ' last';
                            }
                            if (!empty($class)) {
                                $class = ' class="' . trim($class) . '"';
                            }
                            $checked = '';
                            if (!empty($category['active'])) {
                               $checked = ' checked="checked"';
                            }
                            ?><li<?php echo $class ?>><?php 
                                ?><a href="<?php echo $category['url'] ?>" class="label"><?php 
                                    ?><input type="checkbox"<?php echo $checked ?>/><?php
                                    echo $category['name'] . ' (' . $category['count'] . ')';
                                ?></a><?php
                            ?></li><?php
                        }
                    ?></ul><?php
                ?></section><?php
            ?></section><?php
        }
        if (!empty($this->params['countries'])) {
            ?><section class="navigation"><?php
                ?><header><?php 
                    echo __($this, 'Country');
                ?></header><?php
                ?><section class="values-list"><?php
                    ?><ul class="level-01"><?php
                        $this->params['countries'] = array_values($this->params['countries']);
                        $lastIndex = count($this->params['countries']) - 1;
                        foreach ($this->params['countries'] as $i => $country) {
                            $class = '';
                            if ($i === 0) {
                                $class = 'first';
                            }
                            if ($i === $lastIndex) {
                                $class .= ' last';
                            }
                            if (!empty($class)) {
                                $class = ' class="' . trim($class) . '"';
                            }
                            $checked = '';
                            if (!empty($country['active'])) {
                               $checked = ' checked="checked"';
                            }
                            ?><li<?php echo $class ?>><?php 
                                ?><a href="<?php echo $country['url'] ?>" class="label" rel="nofollow"><?php 
                                    ?><input type="checkbox"<?php echo $checked ?>/><?php
                                    echo $country['name'] . ' (' . $country['count'] . ')';
                                ?></a><?php
                            ?></li><?php
                        }
                    ?></ul><?php
                ?></section><?php
            ?></section><?php
        }*/
        foreach ($this->params['filterFields'] as $filterField) {
            if (!empty($filterField['values'])) {
                ?><section class="navigation"><?php
                    ?><header><?php
                        echo $filterField['label'];
                    ?></header><?php
                    ?><section class="values-list"><?php
                        ?><ul class="level-01"><?php
                        $filterField['values'] = array_values($filterField['values']);
                        $lastIndex = count($filterField['values']) - 1;
                        foreach ($filterField['values'] as $i => $val) {
                            $class = '';
                            if ($i === 0) {
                                $class = 'first';
                            }
                            if ($i === $lastIndex) {
                                $class .= ' last';
                            }
                            if (!empty($class)) {
                                $class = ' class="' . trim($class) . '"';
                            }
                            $checked = '';
                            if (!empty($val['active'])) {
                                $checked = ' checked="checked"';
                            }
                            ?>
                            <li<?php echo $class ?>><?php
                                ?><a href="<?php echo $val['url'] ?>" class="label" rel="nofollow"><?php
                                    ?><input type="checkbox"<?php echo $checked ?>/><?php
                                    echo $val['name'] . ' (' . $val['count'] . ')';
                                ?></a><?php
                            ?></li><?php
                        }
                        ?></ul><?php
                    ?></section><?php
                ?></section><?php
            }
        }

        if (!empty($this->params['manufacturers'])) {
            ?><section class="navigation"><?php
                ?><header><?php 
                    echo __($this, 'Manufacturer');
                ?></header><?php
                ?><section class="values-list"><?php
                    ?><ul class="level-01"><?php
                        $this->params['manufacturers'] = array_values($this->params['manufacturers']);
                        $lastIndex = count($this->params['manufacturers']) - 1;
                        foreach ($this->params['manufacturers'] as $i => $manufacturer) {
                            $class = '';
                            if ($i === 0) {
                                $class = 'first';
                            }
                            if ($i === $lastIndex) {
                                $class .= ' last';
                            }
                            if (!empty($class)) {
                                $class = ' class="' . trim($class) . '"';
                            }
                            $checked = '';
                            if (!empty($manufacturer['active'])) {
                               $checked = ' checked="checked"';
                            }
                            ?><li<?php echo $class ?>><?php 
                                ?><a href="<?php echo $manufacturer['url'] ?>" class="label" rel="nofollow"><?php 
                                    ?><input type="checkbox"<?php echo $checked ?>/><?php
                                    echo $manufacturer['name'] . ' (' . $manufacturer['count'] . ')';
                                ?></a><?php
                            ?></li><?php
                        }
                    ?></ul><?php
                ?></section><?php
            ?></section><?php
        }

        if (isset($this->params['minPrice']) && isset($this->params['maxPrice'])) {
            $class='';
            if (empty($this->params['filterResetControls'])) {
                $class = ' last-section';
            }
            ?><section class="navigation filter-price<?php echo $class ?>"><?php
                ?><header><?php 
                    echo __($this, 'Price') . ': ';
                    ?><span class="price-range"><?php echo $this->params['minPrice'] . ' € - ' . $this->params['maxPrice'] . ' €' ?></span><?php
                ?></header><?php
                ?><section class="values-list"><?php
                    ?><div class="price-range-slider"></div><?php
                ?></section><?php
            ?></section><?php
        }

        if (!empty($this->params['filterResetControls'])) {
            ?><section class="navigation filter-reset-controls last-section"><?php
                ?><header><?php 
                    echo __($this, 'Aktívne filtre');
                ?></header><?php
                ?><section class="values-list"><?php
                    ?><ul class="level-01"><?php
                        $this->params['filterResetControls'] = array_values($this->params['filterResetControls']);
                        $lastIndex = count($this->params['filterResetControls']) - 1;
                        foreach ($this->params['filterResetControls'] as $i => $control) {
                            $class = '';
                            if ($i === 0) {
                                $class = 'first';
                            }
                            if ($i === $lastIndex) {
                                $class .= ' last';
                            }
                            if (!empty($class)) {
                                $class = ' class="' . trim($class) . '"';
                            }
                            ?><li<?php echo $class ?>><?php 
                                ?><span class="label"><?php 
                                    ?><a href="<?php echo $control['url'] ?>" class="reset" rel="nofollow">×</a><?php
                                    echo $control['name'];
                                ?></span><?php
                            ?></li><?php
                        }
                    ?></ul><?php
                ?></section><?php
            ?></section><?php
        }
        
    ?></div><?php
?></aside><?php
App::setJsFiles(array('/app/js/vendors/jquery-ui/jquery-ui.min.js'));
App::setCssFiles(array('/app/js/vendors/jquery-ui/jquery-ui.min.css'));
App::startJsCapture();
?><script type="text/javascript">
jQuery(function(){
    var $productsFilter = jQuery('#<?php echo $filterId ?>');
    // collapse filters and activate toggle buttons
    $('.product-filters-content-spacer .navigation section').hide();
    $('.product-filters-content-spacer .navigation header').on('click', function(event){
        var $buttons = $productsFilter.find('.navigation header');
        var $thisButton = $(this);
        $buttons.not($thisButton).removeClass('expanded');
        $thisButton.toggleClass('expanded');
        
        var $valuesLists = $productsFilter.find('.navigation .values-list');
        var $thisValueList = $(this).closest('.navigation').find('.values-list');
        $valuesLists.not($thisValueList).slideUp();
        $thisValueList.slideToggle();
        
        event.stopPropagation();
        event.preventDefault();
    });
    // follow the link also on checkbox click
    $productsFilter.find('input[type="checkbox"]').click(function(){
        window.location.href = jQuery(this).closest('a').attr('href');
    });
    // init price slider
    $productsFilter.find('.price-range-slider').slider({
        range: true,
        min: <?php echo $this->params['floorPrice'] ?>,
        max: <?php echo $this->params['ceilPrice'] ?>,
        values:[<?php echo $this->params['minPrice'] ?>, <?php echo $this->params['maxPrice'] ?>],
        slide: function(event, ui) {
            jQuery('.price-range').text(ui.values[0] + ' € - ' + ui.values[1] + ' €');
        },
        change: function(event, ui) {
            var url, newRange, pageUrlParam = '<?php echo $this->params['pageUrlParam'] ?>';
            url = Run.App.App.parseUrl(window.location.href, {
                hasGetParams: true,
                parseGetParams: true
            });
            newRange = ui.values[0] + '<?php echo $this->params['filterValuesSeparator'] ?>' + ui.values[1];
            // if the new range equals to floor - ceil range then remove the filter (no filtering is applied)
            if (newRange === '<?php echo $this->params['floorPrice'] . $this->params['filterValuesSeparator'] . $this->params['ceilPrice'] ?>') {
                delete(url.params['<?php echo $this->params['filterPriceParamName'] ?>']);
            }
            else {
                url.params['<?php echo $this->params['filterPriceParamName'] ?>'] = newRange;
            }
            // if pageUrlParam is defined (there is Paginator) then reset get paging param for each filter change
            if (pageUrlParam) {
                delete(url.get[pageUrlParam]);
            }
            window.location.href = Run.App.App.getUrl(url);
        }
    });
    // register filter close 
    $productsFilter.find('.close').click(function(e){
        e.preventDefault();
        $productsFilter.hide();
        jQuery('#<?php echo $menuId ?>').show();
    });
    var $filterItems = $productsFilter.find('.navigation').not('.filter-reset-controls').not('.filter-price');
    /*/
    // set widths    
    var filterItemsCount = $filterItems.length;
    if (filterItemsCount > 4) {
        var percents = 25;
    } else {
        var percents = 100 / filterItemsCount;
    }

    $filterItems.css({width: percents + '%'});
    /*/
    // set heights
    var heights = $filterItems.find('> section').map(function () {
        return jQuery(this).height();
    }).get(),
    maxHeight = Math.max.apply(null, heights);
    var maxHeightNormalized = maxHeight + 1;
    //$filterItems.find('> section').css({height: maxHeightNormalized + 'px'});
});
</script><?php
App::endJsCapture();
return;

                ?><header><?php 
    //                echo $this->params['topCategory']['name'];
                    echo __($this, 'Category');
                ?></header><?php


