<?php /* @var $this Template *//** @var Template $this */
$this->displayOriginComment = true;
$params = $this->params;
foreach($params['images'] as $i => $image) {
    $titleHtml = '';
    if (
        $params['showTitle']
        && !empty($image['name'])
    ) {
        $titleHtml = ' title="' . $image['name'] . '"';
    }
    ?><div class="photogallery-image"><?php 
        ?><a rel="<?php echo $params['id'] ?>" href="<?php echo $image['file'] ?>"<?php echo $titleHtml  ?>><?php
            ?><img src="<?php echo $image['thumb'] ?>" alt="<?php echo $image['name'] ?>"<?php echo $params['styleHtml'] ?>/><?php
            ?><div class="hover-mask"></div><?php     
        ?></a><?php
    ?></div><?php
}
