<?php /* @var $this Template */
$this->displayOriginComment = true;
$product = $this->params['product'];
App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    //'data' => $this->params['data'],
));
// prepare special offer banners in advance as an anchor is added to redirect url 
// if there are any banners
$specialOffersAnchor = $recommendedProductsAnchor = '';
if (
    !empty($product['DiscountingSpecialOffer'])
    || !empty($product['PromotingSpecialOffer'])
) {
    if (!empty($product['DiscountingSpecialOffer'])) {
        $offers = $product['DiscountingSpecialOffer'];
        $for = 'discountedProduct';
        $introText = __(__FILE__, 'Tento produkt môžete získať výhodnejšie vrámci nasledovných špeciálnych ponúk');
    }
    else {
        $offers = $product['PromotingSpecialOffer'];
        $for = 'promotedProduct';
        $introText = __(__FILE__, 'Pri kúpe tohoto produktu máme pre Vás pripravené nasledovné špeciálne ponuky');
    }
    $specialOfferBanners = $this->loadElement('specialOfferBanners', array(
        'offers' => $offers,
        'for' => $for,
    ));
}
if (!empty($specialOfferBanners)) {
    $specialOffersAnchor = __(__FILE__, 'specialne-ponuky');
}
// prepare author, bought with, accessory & related products in advance as an anchor 
// is added to redirect url if there are any
$authorHtml = $this->loadControllerAction('EshopProducts', 'indexFromTheSameAuthor', array(
    'emptyIndexMessage' => false,
    'view' => 'indexSlider',
    'paginate' => false,
    'indexType' => 'brief',
), null, $product['id']);
$boughtWithHtml = $this->loadControllerAction('EshopProducts', 'indexBoughtWith', array(
    'emptyIndexMessage' => false,
    'view' => 'indexSlider',
    'paginate' => false,
    'indexType' => 'brief',
), null, $product['id']);
$accessoryHtml = $this->loadControllerAction('EshopProducts', 'indexAccessory', array(
    'emptyIndexMessage' => false,
    'view' => 'indexSlider',
    'paginate' => false,
    'indexType' => 'brief',
), null, $product['id']);
$relatedHtml = $this->loadControllerAction('EshopProducts', 'indexRelated', array(
    'emptyIndexMessage' => false,
    'view' => 'indexSlider',
    'paginate' => false,
    'indexType' => 'brief',
), null, $product['id']);
if (
    !empty($authorHtml) 
    || !empty($boughtWithHtml) 
    || !empty($accessoryHtml) 
    || !empty($relatedHtml)
) {
    $recommendedProductsAnchor = __(__FILE__, 'odporucane-produkty');
}
// add anchor to redirect url
$redirectUrl = '';
if (!empty($specialOffersAnchor)) {
    $redirectUrl = App::getUrl(array(
        'locator' => App::$url,
        'anchor' => $specialOffersAnchor
    ));
}
elseif (!empty($recommendedProductsAnchor)) {
    $redirectUrl = App::getUrl(array(
        'locator' => App::$url,
        'anchor' => $recommendedProductsAnchor
    ));
}
if (!empty($redirectUrl)) {    
    $this->params['urlAddToCart'] = App::rebuildUrl($this->params['urlAddToCart'], array(
        'get' => array('redirectUrl' => $redirectUrl),
        'mergeGet' => true,
    ));
}
$uniqueClass = uniqid('product-view-');
?><div class="product-view <?php echo $uniqueClass ?>"<?php echo $this->params['SmartAdminLauncher']->markRecord($product['id']) ?>><?php
    ?><div class="card"><?php 
        // images
        $galleryId = uniqid('product-view-gallery-');
        ?><div class="images" id="<?php echo $galleryId ?>"><?php 
            // image
            ?><div class="image"><?php 
                ?><div class="wrapper"><?php 
                    // special offers
                    echo $this->loadElement('productSpecialOfferLabels', $this->params + array('product' => $product));
                    // product image
                    ?><a href="<?php echo $product['image']['original'] ?>" class="product-image" rel="<?php echo $galleryId ?>"><?php
                        ?><img <?php 
                            ?>src="<?php echo $product['image']['large']; ?>" <?php 
                            ?>alt="<?php echo $product['name']; ?>"<?php 
                        ?>/><?php
                    ?></a><?php
                ?></div><?php
            ?></div><?php
            // gallery
            if (!empty($product['EshopProductImage'])) {
                $galleryThumbnailsSliderId = uniqid('product-view-gallery-thumbnails-slider-');
                ?><div class="gallery owl-carousel" id="<?php echo $galleryThumbnailsSliderId ?>"><?php 
                    foreach ($product['EshopProductImage'] as $i => $image) {
                        ?><a href="<?php echo $image['file']['original'] ?>" title="<?php echo $image['name'] ?>" class="gallery-image" rel="<?php echo $galleryId ?>"><?php
                            ?><img src="<?php echo $image['file']['small'] ?>" alt="<?php echo $image['name'] ?>"/><?php
                        ?></a><?php
                    }
                ?></div><?php
            }
            ?><div class="labels"><?php 
                if (!empty($product['savings_rate'])) {
                    ?><div class="discount-label"><?php 
                        echo '-' . floor($product['savings_rate']) . '%';
                    ?></div><?php
                }
                if (!empty($product['new'])) {
                    ?><div class="new-label"><?php 
                        echo __(__FILE__, 'Novinka');
                    ?></div><?php
                }
            ?></div><?php
        ?></div><?php
        // texts
        ?><div class="texts"><?php 
            // info
            ?><div class="info"><?php
                // title
                ?><h1 class="title"><?php
                    echo $product['name'];
                ?></h1><?php
                if (!empty($product['subtitle'])) {
                    // subtitle
                    ?><div class="subtitle"><?php
                        echo $product['subtitle'];
                    ?></div><?php
                }
                /*/
                ?><div class="origin"><?php 
                    if (!empty($product['EshopManufacturerRange']['name'])) {
                        ?><dl class="manufacturer-range"><?php 
                            ?><dt><?php 
                                echo __(__FILE__, 'edícia/séria'); 
                            ?></dt><?php
                            ?>&nbsp;<?php
                            ?><dd><?php 
                                // slugManufacturerRangeProducts
                                $urlManufacturerRangeProducts = App::getUrl(array(
                                    'locator' => $this->params['slugManufacturerRangeProducts'],
                                    'args' => array($product['EshopManufacturerRange']['slug'])
                                ));
                                ?><a href="<?php echo $urlManufacturerRangeProducts ?>"><?php 
                                    echo $product['EshopManufacturerRange']['name'];
                                ?></a><?php
                            ?></dd><?php
                        ?></dl><?php
                    }
                    // authors
                    if (!empty($product['EshopAuthor'])) {
                        ?><div class="authors"><?php 
                            foreach ($product['EshopAuthor'] as $j => $author) {
                                if ($j > 0) {
                                    echo ', ';
                                }
                                if (!empty($this->params['slugAuthorProducts'])) {                                                
                                    $urlAuthorProducts = App::getUrl(array(
                                        'locator' => $this->params['slugAuthorProducts'],
                                        'args' => array($author['slug'])
                                    ));
                                    ?><a href="<?php echo $urlAuthorProducts ?>" title="<?php echo __(__FILE__, 'See products of author %s', $author['name']) ?>"><?php 
                                        echo str_replace(' ', '&nbsp;', $author['name']);
                                    ?></a><?php
                                }
                                else {
                                    echo str_replace(' ', '&nbsp;', $author['name']);
                                }
                            }
                        ?></div><?php 
                    }     
                    // manufacturer
                    if (!empty($product['EshopManufacturer']['name'])) {
                        $url = App::getUrl(array(
                            'locator' => $this->params['slugManufacturerProducts'],
                            'args' => array($product['EshopManufacturer']['slug'])
                        ));
                        ?><div class="manufacturer"><?php 
                            ?><a href="<?php echo $url ?>" title="<?php echo __(__FILE__, 'See products of manufacturer %s', $product['EshopManufacturer']['name']) ?>"><?php 
                                echo $product['EshopManufacturer']['name'];
                            ?></a><?php
                        ?></div><?php
                    }
                    if (!empty($product['EshopBrand'])) {
                            ?><tr><?php 
                                ?><th><?php 
                                    echo __(__FILE__, 'Značka') 
                                ?>:</th><?php 
                                ?><td><?php
                                    $brand = $product['EshopBrand'];
                                    $url = url(array(
                                        'locator' => $this->params['slugBrandProducts'],
                                        'args' => array($brand['slug'])
                                    ));
                                    ?><a <?php 
                                        ?>href="<?php echo $url ?>" <?php 
                                        ?>title="<?php echo __(__FILE__, 'More info') ?>"<?php 
                                    ?>><?php 
                                        echo $brand['name'];
                                    ?></a><?php
                                ?></td><?php 
                            ?></tr><?php 
                        }
                ?></div><?php
                //*/
                ?><div class="prices"><?php 
                    // price
                    if (!empty($product['savings_rate'])) {
                        ?><div class="price"><?php 
                            echo Eshop::formatPrice($product['price_taxed'], array(
                                'symbol' => '<span class="currency">' . Eshop::getActualCurrency('symbol') . '</span>')
                            );
                        ?></div><?php
                        /*/
                        // savings
                        ?><dl class="savings"><?php 
                            ?><dt><?php 
                                echo __(__FILE__, 'You save'); 
                            ?></dt><?php
                            ?>&nbsp;<?php
                            ?><dd id="saving-rate-value"><?php 
                                echo floor($product['savings_rate']) . '%';
                                echo '&nbsp;(' . Eshop::formatPrice($product['savings_taxed'], array(
                                    'symbol' => '<span class="currency">' . Eshop::getActualCurrency('symbol') . '</span>'
                                )) . ')';                    
                            ?></dd><?php            
                        ?></dl><?php
                        //*/
                    }
                    // actual price
                    ?><div class="actual-price"><?php 
                        echo Eshop::formatPrice($product['price_actual_taxed'], array(
                            'symbol' => '<span class="currency">' . Eshop::getActualCurrency('symbol') . '</span>')
                        );
                    ?></div><?php
                ?></div><?php
                // attributes
                ?><div class="attributes"><?php
                    ?><table class="attributes-table"><?php
                        if (!empty($product['code'])) { 
                            ?><tr><?php
                                ?><td><?php 
                                    echo __(__FILE__, 'Product code'); 
                                ?></td><?php
                                ?><td><?php 
                                    echo $product['code']; 
                                ?></td><?php
                            ?></tr><?php
                        }
                        if (!empty($product['ean'])) { 
                            ?><tr><?php
                                ?><td><?php 
                                    echo __(__FILE__, 'EAN code'); 
                                ?></td><?php
                                ?><td><?php 
                                    echo $product['ean']; 
                                ?></td><?php
                            ?></tr><?php
                        }
                        if (!empty($product['EshopManufacturer']['name'])) {
                            ?><tr><?php
                                ?><td><?php 
                                    echo __(__FILE__, 'Manufacturer'); 
                                ?></td><?php
                                ?><td><?php 
                                    echo $product['EshopManufacturer']['name']; 
                                ?></td><?php
                            ?></tr><?php
                        }
                        if (!empty($product['oversized'])) {
                            ?><tr><?php
                                ?><td><?php 
                                    echo __(__FILE__, 'Shipment'); 
                                ?></td><?php
                                ?><td><?php 
                                    echo __($this, 'Nadrozmerný produkt');
                                ?></td><?php
                            ?></tr><?php
                        }
                    ?></table><?php
                ?></div><?php
                if ($product['disponibility'] !== EshopProduct::AUTOSTOCK) {
                    // disponibility
                    $disponibility = '';
                    $disponibilityClass = '';
                    switch ($product['disponibility']) {
                        case EshopProduct::STOCK:
                            if($product['stock'] > 10){
                                $stockAmount = '10+ KS';
                            }
                            else{
                                $stockAmount = $product['stock'] . ' KS';
                            }
                            $disponibility = __(__FILE__, 'Available on stock') . ' ' . $stockAmount;
                            $disponibilityClass = ' on-stock';
                            break;
                        case EshopProduct::SUPPLIER:
                            $disponibility = __(__FILE__, 'Available at supplier');
                            $disponibilityClass = ' at-supplier';
                            break;
                        case EshopProduct::ON_DEMAND:
                            $disponibility = __(__FILE__, 'On demand');
                            $disponibilityClass = ' on-demand';
                            break;
                        case EshopProduct::SOLDOUT:
                            $disponibility = __(__FILE__, 'Sold out');
                            $disponibilityClass = ' sold-out';
                            break;
                        case EshopProduct::PRESALE:
                            if (empty($product['reprint'])) {
                                $disponibility = __(__FILE__, 'Presale');
                            }
                            else {
                                $disponibility = __(__FILE__, 'Reprint prepared');
                            }
                            $disponibilityClass = ' presale';
                            break;
                        default:
                        break;
                    }
                    ?><div class="disponibility<?php echo $disponibilityClass ?>"><?php 
                        echo $disponibility;
                        if (
                            $product['disponibility'] != EshopProduct::SOLDOUT
                            && $product['disponibility'] != EshopProduct::ON_DEMAND
                        ) {
                            // available from
                            echo $this->loadView('EshopProducts/availableFromLabel', array('product' => $product));
                            // shipment time
                            if ($product['shipment_time'] < 3) {
                                $shipmentTime =  __(__FILE__, 'do %s hodin', 24 * $product['shipment_time']);
                            }
                            else {
                                $shipmentTime = __(__FILE__, 'do %s pracovných dní', $product['shipment_time']);
                            }
                            if ($product['disponibility'] === EshopProduct::PRESALE) {
                                if (empty($product['reprint'])) {
                                    $shipmentTimeLabel = __(__FILE__, 'Will be shipped %s after publishing');
                                }
                                else {
                                    $shipmentTimeLabel = __(__FILE__, 'Will be shipped %s after reprint');
                                }
                            }
                            elseif(
                                $product['disponibility'] != EshopProduct::STOCK
                                && !empty($product['available_from'])
                                && strtotime($product['available_from']) > time()
                            ) {
                                $shipmentTimeLabel = __(__FILE__, 'Will be shipped %s after being available');
                            }
                            else {
                                $shipmentTimeLabel = __(__FILE__, 'Will be shipped %s');
                            }                
                            ?><div class="shipment-time"><?php 
                                echo Str::fill($shipmentTimeLabel, $shipmentTime);
                            ?></div><?php
                        }
                    ?></div><?php
                }
                ?><div class="controls"><?php
                    if (!empty($product['EshopVariantProduct'])) {
                        ?><h2 class="title"><?php
                            echo  __(__FILE__, 'Vyberte variant');
                        ?></h2><?php
                        ?><ul class="variants"><?php
                            foreach ($product['EshopVariantProduct'] as $variantProduct) {
                                $urlShowVariantDetail = App::getUrl(array(
                                    'locator' => $this->params['slugProductView'],
                                    'args' => array($variantProduct['slug'])
                                ));
                                $variantClassHtml = '';
                                if ($variantProduct['id'] == $product['id']) {
                                    $variantClassHtml = ' class="active"';
                                }
                                ?><li<?php echo $variantClassHtml ?>><?php
                                    ?><a href="<?php echo $urlShowVariantDetail?>"><?php
                                        ?><img <?php 
                                            ?>src="<?php echo $variantProduct['image']['tiny'] ?>" <?php 
                                            ?>alt="<?php echo $variantProduct['name'] ?>"<?php 
                                        ?>/><?php
                                        echo $variantProduct['name'];
                                    ?></a><?php
                                ?></li><?php
                            }
                        ?></ul><?php
                    }
                    // add to cart form
                    if (
                        !empty($this->params['urlAddToCart'])
                        && $product['disponibility'] !== EshopProduct::SOLDOUT
                    ) { 
                        ?><form <?php 
                            ?>action="<?php echo $this->params['urlAddToCart']; ?>" <?php 
                            ?>method="post" <?php 
                            ?>class="add-to-cart"<?php 
                        ?>><?php 
                            ?><input type="hidden" name="data[_target]" value="Eshop.EshopCarts.addProducts" /><?php
                            ?><input type="hidden" name="data[id]" value="<?php echo $product['id']; ?>" /><?php
                            if (!empty($product['special_offer_id'])) {
                                ?><input type="hidden" <?php 
                                    ?>name="data[special_offer_id]" <?php 
                                    ?>value="<?php echo $product['special_offer_id']; ?>"<?php 
                                ?>/><?php
                            }
                //             // selective attributes (if any nonempty)
                //             if (
                //                 !empty($product['static_attributes'])
                //                 && ($product['static_attributes'] = array_filter($product['static_attributes']))
                //             ) {
                //                 ?><div class="attributes"><?php
                //                     if (!empty($product['static_attributes']['my_attr'])) {
                //                         ?><div class="attribute"><?php
                //                             echo $Form->select('static_attributes.my_attr', array(
                //                                 'label' => __(__FILE__, 'My attr'),
                //                                 'options' => $product['static_attributes']['my_attr'],
                //                             ));
                //                         ?></div><?php
                //                     }
                //                     if (!empty($product['static_attributes']['variant'])) {
                //                         ?><div class="attribute"><?php
                //                             echo $Form->input('static_attributes.variant', array(
                //                                 'type' => count($product['static_attributes']['variant']) > 2 ? 'select' : 'radio',
                //                                 //'label' => __(__FILE__, 'Vyberte variant'),
                //                                 'options' => $product['static_attributes']['variant'],
                //                                 'value' => current(array_keys($product['static_attributes']['variant'])),
                // //                                'onchange' => '__setPrice();',
                // //                                'id' => 'my_attr',
                //                             ));
                //                         ?></div><?php
                //                     }
                //                 ?></div><?php
                //             }
                            ?><div class="add-to-cart-button"><?php 
                                ?><div class="to-cart-change"><?php
                                    ?><div class="plus"></div><?php
                                    ?><div class="minus"></div><?php
                                ?></div><?php
                                ?><div class="to-cart-count"><?php
                                    ?><input class="number-of-pieces" name="data[amount]" type="text" value="1" /><?php
                                ?></div><?php
                                ?><div class="to-cart-button"><?php
                                    ?><span class="button-container"><?php
                                        ?><input class="button" type="submit" value="<?php echo __(__FILE__, 'do košíka'); ?>" name="" data-action="buy"/><?php
                                    ?></span><?php
                                ?></div><?php
                            ?></div><?php
                        ?></form><?php 
                    }
                    if ($product['disponibility'] === EshopProduct::AUTOSTOCK) {
                        // do not display neither wishlist nor watchdog
                    }
                    elseif ($product['disponibility'] !== EshopProduct::SOLDOUT) {
                        ?><a href="<?php echo $this->params['urlAddToWishlist'] ?>" class="add-to-wishlist" data-action="wishlist"><?php 
                            /*/
                            ?><i class="fas fa-heart"></i><?php
                            /*/
                            echo __(__FILE__, 'Pridať do wishlistu');
                        ?></a><?php 
                    }
                    else {
                        ?><form action="<?php echo $this->params['urlAddAvailabilityAlert'] ?>" method="post" class="add-availability-alert"><?php
                            ?><input type="hidden" name="data[_target]" value="Eshop.EshopWishlists.addProductAvailabilityAlert" /><?php
                            if (!App::getUser()) {                            
                                echo $Form->text('email', array(
                                    'placeholder' => __(__FILE__, 'Váš e-mail')
                                ));
                            }
                            ?><button data-action="watchdog"><?php
                                echo __(__FILE__, 'Watch');
                            ?></button><?php 
                            ?><div class="note"><?php 
                                echo __(__FILE__, 'Budeme vás informovať, hneď ako bude tovar dostupný');
                            ?></div><?php
                        ?></form><?php
                    }
                ?></div><?php
            ?></div><?php
        ?></div><?php
        // description
        ?><div id="description" class="description"><?php 
            ?><div class="description-text"><?php 
                ?><h2 class="section-title"><?php 
                    echo __(__FILE__, 'Popis produktu'); 
                ?></h2><?php
                ?><div class="text"><?php 
                    echo $product['description'];                    
                ?></div><?php
            ?></div><?php
            ?><div class="button-wrapper"><?php 
                ?><div class="more-button"><?php
                    echo __( $this, 'Expand description' );
                ?></div><?php 
            ?></div><?php    
            if ($product['video_url']) {
                ?><div class="video"><?php 
                    echo Html::video($product['video_url'], array(
                        // width is set to 100% by CSS
                        'height' => 400,
                    ));
                ?></div><?php
            }
        ?></div><?php
        // attributes
        ?><div class="attributes"><?php
            ?><h2 class="section-title"><?php 
                    echo __(__FILE__, 'Parametre'); 
            ?></h2><?php
            ?><table class="attributes-table"><?php
                foreach ($product['parsed_tech_data'] as $attribute) {
                    ?><tr><?php
                        ?><td><?php 
                            echo $attribute['label']; 
                        ?></td><?php
                        ?><td><?php 
                            echo $attribute['value']; 
                        ?></td><?php
                    ?></tr><?php
                }
                //*/
                if (!empty($product['dimensions'])) { 
                    ?><tr><?php
                        ?><td><?php 
                            echo __(__FILE__, 'Dimensions'); 
                        ?></td><?php
                        ?><td><?php 
                            echo $product['dimensions']; 
                        ?></td><?php
                    ?></tr><?php
                }
                if (!empty($product['weight'])) { 
                    ?><tr><?php
                        ?><td><?php 
                            echo __(__FILE__, 'Weight'); 
                        ?></td><?php
                        ?><td><?php 
                            echo App::formatNumber($product['weight']) . '&nbsp;kg';
                        ?></td><?php
                    ?></tr><?php
                }
                if (!empty($product['color'])) { 
                    ?><tr><?php
                        ?><td><?php 
                            echo __(__FILE__, 'Color'); 
                        ?></td><?php
                        ?><td><?php 
                            echo $product['color'];
                        ?></td><?php
                    ?></tr><?php
                }
                if (!empty($product['size'])) { 
                    ?><tr><?php
                        ?><td><?php 
                            echo __(__FILE__, 'Size'); 
                        ?></td><?php
                        ?><td><?php 
                            echo $product['size'];
                        ?></td><?php
                    ?></tr><?php
                }
                //*/
            ?></table><?php
        ?></div><?php
    ?></div><?php
    // socials
    /*/
    ?><div class="socials"><?php 
        ?><div class="social"><?php 
            echo App::loadElement('App', 'fbLikeIt', array(
                'locator' => App::$url,
                'layout' => 'button_count',
            ));
        ?></div><?php
        ?><div class="social"><?php 
            echo App::loadElement('App', 'twitterTweet', array(
                'locator' => App::$url,
                'text' => $product['name'],
            ));
        ?></div><?php
    ?></div><?php
    //*/
    ?><div class="comments"><?php
        // internal comments
        $commentsHtml = App::loadControllerAction('App','Comments', 'add', null, null, array('Eshop.EshopProduct', $product['id']));
        $commentsHtml .= App::loadControllerAction('App','Comments', 'index', null, null, array('Eshop.EshopProduct', $product['id']));
        if (!empty($commentsHtml)) {
            ?><div class="internal-comments"><?php 
                ?><h2 class="section-title"><?php 
                    echo __(__FILE__, 'Diskusia');
                ?></h2><?php
                ?><div class="card"><?php 
                    echo $commentsHtml;
                ?></div><?php
            ?></div><?php
        }
    ?></div><?php
    // special offer banners (prepared at the beginning of view)
    if (!empty($specialOfferBanners)) {
        ?><div class="special-offers"><?php 
            if (!empty($specialOffersAnchor)) {
                ?><a name="<?php echo $specialOffersAnchor ?>" id="<?php echo $specialOffersAnchor ?>"></a><?php
            }
            ?><h2 class="section-title"><?php 
                echo __(__FILE__, 'Špeciálne ponuky');
            ?></h2><?php
            ?><div class="intro-text"><?php 
                echo $introText . ':';
            ?></div><?php
            echo $specialOfferBanners;
        ?></div><?php
    }
    // recommended products
    if (
        !empty($authorHtml) 
        || !empty($boughtWithHtml) 
        || !empty($accessoryHtml) 
        || !empty($relatedHtml)
    ) {
        ?><div class="recommended-products"><?php 
            if (!empty($recommendedProductsAnchor)) {
                ?><a name="<?php echo $recommendedProductsAnchor ?>" id="<?php echo $recommendedProductsAnchor ?>"></a><?php
            }
            if (!empty($authorHtml)) {        
                ?><div class="author-products"><?php 
                    ?><h2 class="section-title"><?php 
                        echo __(__FILE__, 'From the same author');
                    ?></h2><?php
                    echo $authorHtml;
                ?></div><?php
            }
            if (!empty($boughtWithHtml)) {        
                ?><div class="bought-with-products"><?php 
                    ?><h2 class="section-title"><?php 
                        echo __(__FILE__, 'Bought with');
                    ?></h2><?php
                    echo $boughtWithHtml;
                ?></div><?php
            }
            if (!empty($accessoryHtml)) {        
                ?><div class="accessory-products"><?php 
                    ?><h2 class="section-title"><?php 
                        echo __(__FILE__, 'Accessories');
                    ?></h2><?php
                    echo $accessoryHtml;
                ?></div><?php
            }
            if (!empty($relatedHtml)) {        
                ?><div class="related-products"><?php 
                    ?><h2 class="section-title"><?php 
                        echo __(__FILE__, 'Related products');
                    ?></h2><?php
                    echo $relatedHtml;
                ?></div><?php
            }
        ?></div><?php
    }    
?></div><?php
// attach fancybox js
if (!empty($galleryId)) {
    App::startJsCapture();
    ?><script type="text/javascript">
    jQuery(function(){
        jQuery('#<?php echo $galleryId ?> a[rel="<?php echo $galleryId ?>"]').fancybox({
            "openEffect":"elastic",
            "closeEffect":"elastic"
        });
    });
    </script><?php
    App::endJsCapture();
}
// create gallery thumbnails slider
if (!empty($product['EshopProductImage'])) {
    App::startJsCapture();
    ?><script type="text/javascript">
        jQuery(function(){
            var owlProducts = jQuery("#<?php echo $galleryThumbnailsSliderId ?>");
            owlProducts.owlCarousel({
                autoplay: false,
                autoplaySpeed: 500,
                navSpeed: 200,
                nav: true,
                navText: ['', ''], // ['next', 'prev']
                dots: false,
                autoWidth: true,
                margin: 25
                // loop: true
            });
        });
    </script><?php
    App::endJsCapture();
    App::setCssFiles(array(
        '/app/js/vendors/owlcarousel/owl.carousel.min.css',
        '/app/js/vendors/owlcarousel/owl.theme.default.min.css',
    ));
    App::setJsFiles(array(
        '/app/js/vendors/jquery.min.js',
        '/app/js/vendors/owlcarousel/owl.carousel.min.js',
    ));
}

App::startJsCapture();
?><script type="text/javascript">
// function for increase and decrease amount of items button in product
function activateAmountButton() {
    var addToCartSelector = '.add-to-cart-button';
    var amountInputSelector = '.number-of-pieces';
    var activatedClass = '_activated';
    var increaseBtn = $( addToCartSelector + ' .plus' ).not('.' + activatedClass);
    var decreaseBtn = $( addToCartSelector + ' .minus' ).not('.' + activatedClass);
    increaseBtn.click( function() {
        var addToCartElement = $(this).closest(addToCartSelector);
        var amountInput = addToCartElement.find(amountInputSelector);
        var amount = parseInt(amountInput.val());
        amountInput.val(amount + 1);
    });
    decreaseBtn.click( function() {
        var addToCartElement = $(this).closest(addToCartSelector);
        var amountInput = addToCartElement.find(amountInputSelector);
        var amount = parseInt(amountInput.val());
        if (amount > 1) {
            amountInput.val(amount - 1);
        }
    });
    // mark all buttons which have been activated already to not
    // activate them twice on infine scroll or if there are many 
    // add to cart buttons on a single page
    increaseBtn.addClass(activatedClass);
    decreaseBtn.addClass(activatedClass);
}

// activate amount buttons in product detail section
activateAmountButton();

jQuery(function(){    
    function registerPriceEvents() {
        var fields = <?php echo json_encode($this->params['priceAttributeFields']) ?>, i;
        
        if (Run.App.Validate.emptyValue(fields)) {
            return;
        }
        for (i = 0; i < fields.length; i++) {
            jQuery('[name="data[static_attributes][' + fields[i] + ']"').on('change', function() {
                setPrice();
            });
        }
    }
    
    function setPrice() {
        var prices = <?php 
                if (defined(JSON_UNESCAPED_UNICODE)) {
                    // if value of attribute contains unicode characters then do not escape them,
                    // e.g. instead of "zelen\u00e9" will be "zelené"
                    echo json_encode($this->params['priceVariants'], JSON_UNESCAPED_UNICODE);
                }
                else {
                    echo json_encode($this->params['priceVariants']);
                }
            ?>,
            fields = <?php echo json_encode($this->params['priceAttributeFields']) ?>, 
            i, price, fieldValue, element, html;
    
        if (
            Run.App.Validate.emptyValue(prices)
            || Run.App.Validate.emptyValue(fields)
        ) {
            return;
        }
        price = prices;
        for (i = 0; i < fields.length; i++) {
            fieldValue = jQuery('[name="data[static_attributes][' + fields[i] + ']"').val(),
            price = price[fieldValue] || null;
        }
        if (!Run.App.Validate.emptyValue(price)) {
            element = jQuery('#actual-price-value');
            if (element.length) {
                html = element.html();
                html = html.replace(/^\s+/gm, '');
                html = html.replace(/^[\d\.\,\-\–]+/gm, '');
                html = Run.Eshop.Eshop.formatPrice(price['price_actual_taxed'], {symbol: false}) + html;
                element.html(html);
            }
            element = jQuery('#price-value');
            if (element.length) {
                html = element.html();
                html = html.replace(/^\s+/gm, '');
                html = html.replace(/^[\d\.\,\-\–]+/gm, '');
                html = Run.Eshop.Eshop.formatPrice(price['price_taxed'], {symbol: false}) + html;
                element.html(html);
            }
            element = jQuery('#saving-rate-value');
            if (element.length) {
                html = element.html();
                html = html.replace(/^\s+/gm, '');
                html = html.replace(/^[\d\.\,\-\–]+/gm, '');
                html = price['saving_rate'] + html;
                element.html(html);
            }
        }
    }
    
    function setExpandableDescription() {
        var description = jQuery('#description');
        var descContainer = jQuery('#description .text');
        var button = jQuery('#description .more-button');
        var descContainerHeight = descContainer.outerHeight();
        var descContainerMaxHeight = 144;
        description.removeClass('expandable');
        if (descContainerHeight >= descContainerMaxHeight + 5){
            description.addClass('expandable');
            var collapseButtonText = '<?php echo __( $this, 'Collapse description' ) ?>';
            var expandButtonText = '<?php echo __( $this, 'Expand description' ) ?>';
            descContainer.css('max-height', descContainerMaxHeight + 'px');
            button.click (function() {
                if (!description.hasClass('expanded')){
                    descContainer.css('max-height', descContainerHeight + 'px');
                    jQuery(this).text(collapseButtonText);
                } else {
                    descContainer.css('max-height', descContainerMaxHeight + 'px');
                    jQuery(this).text(expandButtonText);
                }
                description.toggleClass('expanded')
            });
        }
    }

    
    // register price events and set the price at the beginning
    registerPriceEvents();
    setPrice();
    setExpandableDescription();
});
//jQuery(window).load(function(){
//    // If jQuery(window).load() does not work correctly the see this http://stackoverflow.com/questions/910727/jquery-event-for-images-loaded
//    // and connect events on image loading
//    function resolveDescriptionHeight() {
//        var view = jQuery('.<?php //echo $uniqueClass ?>//');
//        //@todo-redesign
//    };
//    // call align on window load
//    resolveDescriptionHeight();
//    // call align on each window resize
//    jQuery(window).resize(function(){resolveDescriptionHeight();});
//});
</script><?php
App::endJsCapture();
App::setCssFiles(array(
    '/app/js/vendors/fancybox/jquery.fancybox.css',
));
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
    '/app/js/vendors/jquery.mousewheel-3.0.6.pack.js',
    '/app/js/vendors/fancybox/jquery.fancybox.pack.js',
    '/app/js/libs/PhpJs.js',
    '/app/js/libs/Number.js',
    '/app/js/libs/Validate.js',
    '/app/modules/Eshop/js/Eshop.js',
));
App::setJsConfig('Eshop', array(
    'actualCurrency' => Eshop::getActualCurrency(),
));

$this->loadModel('EshopProduct');
$Product = new EshopProduct();
echo $Product->getFacebookViewCode($product['id']);

// RETURN
return;

//
// Code store for other projects
//
