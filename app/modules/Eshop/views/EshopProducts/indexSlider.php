<?php /* @var $this Template */
$this->displayOriginComment = true;
if (empty($this->params['products'])) {
    return;
}
$uniqueId = uniqid('products-slider-');
$class = 'products-slider';
if (!empty($this->params['limitImageHeight'])) {
    $class .= ' with-limited-image-height';
}
?><div class="<?php echo $class ?>" id="<?php echo $uniqueId; ?>"><?php
    ?><div class="index owl-carousel"><?php
        echo $this->loadView($this->params['recordsView'], $this->params);
    ?></div><?php
    /*/
    ?><div class="arrow left"></div><?php
    ?><div class="arrow right"></div><?php
    ?><div class="dots"></div><?php
    /*/
?></div><?php
$hasSideContent = App::getPropertyContent()['has_side_content'];
App::startJsCapture();
?><script type="text/javascript">
    jQuery(function(){
        var owlProducts = jQuery("#<?php echo $uniqueId; ?> .index");
        owlProducts.owlCarousel({
            <?php 
            // allow to override responsive options
            if (!empty($this->params['sliderResponsiveOptions'])) { ?>
            responsive: <?php echo json_encode($this->params['sliderResponsiveOptions']) ?>,
            <?php 
            }
            // keep these breakpoints in synchro with media queries in css for .products-index
            elseif ($hasSideContent){ ?>
            responsive: {
                0: {
                    items: 1,
                    slideBy: 1,
                    margin: 10,
                },
                541: {
                    items: 2,
                    slideBy: 2,
                    margin: 10,
                },
                801: {
                    items: 3,
                    slideBy: 3,
                    margin: 10,
                },
                1101: {
                    items: 4,
                    slideBy: 4,
                    margin: 10,
                },
            },
            <?php } else { ?>
            responsive: {
                0: {
                    items: 1,
                    slideBy: 1,
                    margin: 10,
                },
                541: {
                    items: 2,
                    slideBy: 2,
                    margin: 10,
                },
                801: {
                    items: 3,
                    slideBy: 3,
                    margin: 10,
                },
                1101: {
                    items: 4,
                    slideBy: 4,
                    margin: 10,
                },
            },
            <?php } ?>
            autoplay: false,
            autoplaySpeed: 500,
            navSpeed: 200,
            nav: true,
            navText: ['', ''], // ['next', 'prev']
            dots: true,
            // loop: true
            /*/
            dotsEach: 1,
            dotsContainer: '#<?php echo $uniqueId; ?> .dots'
            /*/
        });
    });
</script><?php
App::endJsCapture();
/*/
App::startJsCapture();
?><script type="text/javascript">
    jQuery(function(){
        jQuery("#<?php echo $uniqueId; ?> .arrow.left").click(function(){
            owlProducts.trigger('prev.owl.carousel');
        });
        jQuery("#<?php echo $uniqueId; ?> .arrow.right").click(function(){
            owlProducts.trigger('next.owl.carousel');
        });
    });
</script><?php
App::endJsCapture();
/*/
App::setCssFiles(array(
    '/app/js/vendors/owlcarousel/owl.carousel.min.css',
    '/app/js/vendors/owlcarousel/owl.theme.default.min.css',
));
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
    '/app/js/vendors/owlcarousel/owl.carousel.min.js',
));
