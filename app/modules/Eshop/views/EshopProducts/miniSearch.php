<?php /* @var $this Template */
$this->displayOriginComment = true;
App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    'data' => $this->params['data'],
));
?><div class="mini-search"><?php
    ?><div id="mini-search-mobile-button" class="mini-search-mobile-button"><?php
        ?><div class="mini-search-icon"></div><?php
        ?><div class="mini-search-arrow"></div><?php
    ?></div><?php
    ?><form class="mini-search-form" id="mini-search" action="<?php echo $this->params['urlSearchProducts']; ?>" method="get"><?php 
        ?><div class="keywords-wrapper"><?php
            echo $Form->search('keywords', array('placeholder' => __(__FILE__, 'Vyhľadajte medzi %s unisport produktami...', $this->params['productsCount'])));
        ?></div><?php
        ?><div class="button-wrapper"><?php
            ?><input type="submit" title="<?php echo __(__FILE__, 'Search');?>" value="<?php echo __(__FILE__, 'Search');?>" /><?php 
        ?></div><?php
        ?><input type="hidden" name="data[_target][]" value="Eshop.EshopProducts.indexSearch" /><?php 
        ?><input type="hidden" name="data[_target][]" value="Eshop.EshopProducts.miniSearch" /><?php
        // on each new search reset possible filter
        ?><input type="hidden" name="<?php echo $this->params['paginatorResetUrlParam'] ?>" value="1" /><?php 
        // show suggestions
        ?><div class="products-index"><?php
            ?><div class="close" title="<?php echo __(__FILE__, 'Zavrieť') ?>"></div><?php
            ?><div class="scroll-wrapper"><?php 
                ?><div class="index"></div><?php
                ?><div class="button-wrapper"><?php 
                    ?><button type="submit"><?php echo __(__FILE__, 'Zobraziť všetky výsledky'); ?></button><?php
                ?></div><?php
            ?></div><?php
        ?></div><?php
    ?></form><?php
?></div><?php
// autofocus search input if on search page
if (
    trim($this->params['urlSearchProducts'], '/') 
        === trim(App::$parsedUrl['locator'], '/')
) {
    App::startJsCapture();
    ?><script type="text/javascript">
    jQuery(function(){
        var miniSearch = jQuery('#mini-search');
        miniSearch.addClass('opened');
        var miniSearchInput = miniSearch.find('input[type=search]');
        miniSearchInput.focus();
        miniSearchInput.select();
    });
    </script><?php
    App::endJsCapture();
}
App::startJsCapture();
?><script type="text/javascript">
jQuery(function(){
    var miniSearch = jQuery('#mini-search');
    var miniSearchInput = miniSearch.find('input[type=search]');
    var suggestions = miniSearch.find('.products-index');
    var suggestionsClose = suggestions.find('.close');
    var suggestionsScrollWrapper = suggestions.find('.scroll-wrapper');
    var suggestionsIndex = suggestions.find('.index');
    var paginatorResetUrlParam = '<?php echo $this->params['paginatorResetUrlParam'] ?>';
    var getThrottledSuggestions = throttle(getSuggestions, 1000);
    var lastKeywords = '';

    jQuery(document).on('click', function() {
        suggestions.css('display', 'none');
    });
    
    function setSuggestionsTop() {
        var miniSearchInputTop = miniSearchInput.offset().top;
        var miniSearchInputHeight = miniSearchInput.height();
        var suggestionsTopOffset = jQuery(window).width() < 800 ? 52 : 10;
        var suggestionsTop = miniSearchInputTop + miniSearchInputHeight + suggestionsTopOffset;
        
        // keep this in synchro with css
        suggestions.css({
            top: suggestionsTop,
            maxHeight: 'calc(100vh - ' + (suggestionsTop) + 'px)'
        });
        suggestionsScrollWrapper.css({
            maxHeight: 'calc(100vh - ' + (suggestionsTop) + 'px)'
        });
    }

    miniSearch.on('click', function(e){
        setSuggestionsTop();
        getThrottledSuggestions();
        keywordsWrapper = miniSearch.find('.keywords-wrapper');
        if (
            !miniSearch.hasClass('opened')
            && keywordsWrapper.width() === 0
        ) {
            miniSearch.addClass('opened');
            e.preventDefault();
        }
        // stop click event propagation to parent elements 
        // to prevent click event on document defined here above
        e.stopPropagation();
    });
    // activate mobile mini search
    var button = jQuery('#mini-search-mobile-button');
    var form = jQuery('#mini-search');
    button.click(function(e){
        e.stopPropagation();
        jQuery(this).toggleClass('active');
        form.slideToggle(100);
        // dropDowns.forEach(function(item) {
        //     if (item[0].selector !== '#top-categories-extra-button') {
        //         item[0].removeClass('active');
        //         item[1].slideUp(100);
        //     }
        // });
    });
    form.mouseleave(function(e){
        if (jQuery(window).width() <= 790) {
            jQuery(this).slideToggle(100);
            button.removeClass('active');
        }
    });
    // if (typeof window.dropDowns === 'undefined'){
    //     window.dropDowns = [];
    // }
    // window.dropDowns.push([button, form]);

    jQuery(window).resize(function(){
        if (jQuery(window).width() > 790) {
            form.css('display', 'flex');
        } else {
            if (!button.hasClass('active')){
                form.css('display', '');
                suggestions.css('display', 'none');
            } else {
                form.css('display', 'block');
                suggestions.css('display', 'block');
            }
        }
    });

    suggestions.on('click', function(e) {
        // stop click event propagation to parent elements 
        // to prevent click event on document and miniSearch defined here above
        e.stopPropagation();
    });
    
    miniSearchInput.on('input', function() {
        getThrottledSuggestions();
    });
    
    suggestionsClose.on('click', function() {
        suggestions.css('display', 'none');
    });

    function getSuggestions() {
        var searchData = miniSearch.serializeArray();
        var keywords;
        // get keywords and remove paginator reset url param (to avoid redundand redirect for sake of filter reset)
        searchData.forEach((data, index) => {
            if (data['name'] === 'data[keywords]') {
                keywords = data['value'];
            }
            else if (data['name'] === paginatorResetUrlParam) {
                delete searchData[index];
            }
        });
        // if no keywords provided then empty index and hide suggestions
        if (!keywords.length) {
            suggestionsIndex.empty();
            suggestions.css('display', 'none');
            return;
        }
        // if still the same keywords (after closing suggestions and reopening them)
        // then just show what has been shown last time
        if (keywords === lastKeywords) {
            suggestions.css('display', 'block');
            return;
        }
        lastKeywords = keywords;
        // get suggestions from server
        // - add recordsOnly GET param
        searchData.push({name: 'recordsOnly', value: 1});
        jQuery.ajax({
            url: '/mvc/Eshop/EshopProducts/indexSearch/indexType:suggestions',
            type: 'GET',
            data: searchData,
            success: function(response) {
                suggestionsIndex.empty();
                if (response) {
                    suggestionsIndex.append(response);
                    suggestions.css('display', 'block');
                }
                else {
                    suggestions.css('display', 'none');
                }
            }
        });
    }
});
</script><?php
App::endJsCapture();
