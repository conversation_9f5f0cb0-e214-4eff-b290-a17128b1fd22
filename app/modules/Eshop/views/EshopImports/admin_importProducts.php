<?php
$importPid = $this->params['importPid'];
$imports = $this->params['imports'];
// get list of source dir files
$sourceDir = 'userfiles' . DS . 'files' . DS . 'import';
$dirItems = scandir(ROOT . DS . $sourceDir);
$files = array();
foreach ($dirItems as $dirItem) {
    $dirItemPath = $sourceDir . DS . $dirItem;
    if (
        !is_dir(ROOT . DS . $dirItemPath)
        && is_readable(ROOT . DS . $dirItemPath)
    ) {
        $files[$dirItemPath] = $dirItem;
    }
}
$title = __a(__FILE__, 'Import');
if (
    !empty($importPid)
    && !empty($imports[$importPid])
) {
    $title .= ' "' . $imports[$importPid] . '"';
}
elseif (!empty($importPid)) {
    $title .= ' "' . $importPid . '"';
}
// generate import info
Html::startCapture();
if ($importPid === 'alteregoAddition') {
    ?>Predtým ako sputíte import: Nakopírujte súbory <code>knihy.csv</code> a <code>anotacie.txt</code> do priečinka <code>/userfiles/files/import</code> a obrázky produktov do priečinka <code>/userfiles/files/import/images</code><br><br><?php
}
elseif ($importPid === 'internalProductUpdate') {
?>Predtým než začnete importovať:<br><br><?php
    ?><b>1]</b> Vytvorte importný súbor v nasledovnom formáte:<br><br><?php
    ?><pre>
ean,{field01Name},{field02Name},...
8588003391186,{field01Value},{field02Value},...
9788089322008,{field01Value},{field02Value},...
8588003391223,{field01Value},{field02Value},...
8586006663392,{field01Value},{field02Value},...
8588003391162,{field01Value},{field02Value},...
9788080672140,{field01Value},{field02Value},...</pre><?php
    ?><br><?php
    ?><b>Ako prvý stĺpec</b> je uvedený párovací stĺpec. Bežne to bude niektorý zo stĺpcov <code>id</code>, <code>ean</code>, <code>code</code>, prípadne nejaký iný stĺpec, podľa ktorého je produkt jednoznačne idetifikovaný. Hodnoty v párovacom stĺpci musia byť neprázdne a unikátne.<?php
    ?><br><?php
    ?><b>Ostatné stĺpce</b> sú importované stĺpce <code>{field??Name}</code> a môžu byť uvedené v ľubovolnom poradí a počte:<br><br><?php
    ?><code>price_taxed</code> - celé alebo desatinné číslo. Ako oddeľovač desatinných miest musí byť použitá bodka '.', napr.: <tt>"1.25"</tt>). Musí byť spolu so stĺpcom  <code>tax_rate</code><br><?php
    ?><code>tax_rate</code> - jedna z hodnôt <tt>"0"</tt>, <tt>"10"</tt>, <tt>"20"</tt><br><?php
    ?><code>discount_rate</code> - celé alebo desatinné číslo. Ako oddeľovač desatinných miest musí byť použitá bodka '.', napr.: <tt>"1.25"</tt>)<br><?php
    ?><code>discount_from</code> - dátum vo formáte <tt>"YYYY-MM-DD"</tt>, napr.: <tt>"2020-03-15"</tt><br><?php
    ?><code>discount_to</code> - dátum vo formáte <tt>"YYYY-MM-DD"</tt>, napr.: <tt>"2020-10-15"</tt><br><?php
    ?><code>stock</code> - celé číslo<br><?php
    ?><code>availability</code> - jedna z hodnôt <tt>"enum_presale"</tt>, <tt>"enum_available"</tt>, <tt>"enum_soldout"</tt><br><?php
    ?><code>long_delivery_time</code> - <tt>"0"</tt> alebo <tt>"1"</tt><br><?php
    ?><code>reprint</code> - <tt>"0"</tt> alebo <tt>"1"</tt><br><?php
    ?><code>available_from</code> - dátum vo formáte <tt>"YYYY-MM-DD"</tt>, napr.: <tt>"2020-03-15"</tt><br><?php
    ?><code>shipment_time_off_stock</code> - celé číslo<br><?php
    ?><code>width</code>, <code>height</code>, <code>length</code> - šírka, výška a dĺžka (alebo hrúbka / hĺbka) v milimetroch, napr.: <tt>"40"</tt>. Ak sú uvedené tieto stĺpce, tak stĺpec <code>dimensions</code> by nemal byť uvedený<br><?php
    ?><code>dimensions</code> - rozmery v milimetroch (šírka x výška x hrúbka), napr.: <tt>"165x250x15"</tt> alebo <tt>"165 x 250"</tt> - nemusia byť uvedené všetky, medzery nie sú dôležité. Hodnoty môžu byť aj v iných jednotkách, no tieto musia byť uvedené: <tt>"16.5 x 25 x 1,5 cm"</tt> alebo <tt>"165mm x 25 cm"</tt> - okrem bodky '.' je možné tiež použiť ako oddeľovač desatinných miest aj čiarku ','. Ak je uvedený tento stĺpec, tak stĺpce <code>width</code>, <code>height</code>, <code>length</code> by nemali byť uvedené<br><?php
    ?><code>weight</code> - váha v kilogramoch, napr.: <tt>"0.25"</tt><br><?php
    ?><code>active</code> - <tt>"0"</tt> alebo <tt>"1"</tt><br><?php
    ?><code>manufacturer</code> - meno výrobcu, napr.: <tt>"Mladé letá"</tt><br><?php
    ?><code>range</code> - názov edície, napr.: <tt>"Stopy"</tt><br><?php
    ?><code>categories</code> - kódy kategórií oddelené bodkočiarkou ';', napr.: <tt>"10002;124"</tt><br><?php
    ?><code>authors</code> - mená autorov oddelené bodkočiarkou ';', napr.: <tt>"Elena Čepčeková;Mária Ďuríčková"</tt><br><?php
    ?><code>name</code>, <code>description</code>, <code>seo_title</code>, ...  a iné textové polia - <u>Ak text obsahuje čiarku</u>, tak je potrebné ho ohraničiť úvodzovkami, napr.: <tt>"Importy včera, dnes a zajtra"</tt>. <u>Ak text obsahuje úvodzovky</u>, tak tieto musia byť zdvojené, aby sa nechápali ako ohraničovacie: <tt>"Predchádzajúci text bol ""Importy včera, dnes a zajtra"""</tt>. <b>Jednoduchý návod je</b>: <b>1]</b> nahradzovať všetky úvodzovky v hodnotách dvojitými úvodzovkami a <b>2]</b> ohraničovať všetky hodnoty úvodzovkami (bez ohľadu na to či je to potrebné):<br><br><?php
    ?><pre>
"ean","{field01Name}","{field02Name}",...
"8588003391186","{field01Value}","{field02Value}",...
"9788089322008","{field01Value}","{field02Value}",...</pre><?php
    ?><br><?php
    ?>Zoznam všetkých dostupných polí je nasledovný: <?php
    App::loadLib('Eshop', 'EshopProductCsvReader');
    $recordFields = EshopProductCsvReader::getRecordFields();
    ?><code><?php 
        echo implode('</code>, <code>', array_keys($recordFields))
    ?></code><?php
    ?><br><?php
    ?><br><?php
    ?><b>Pozor:</b> Prázdna hodnota (<tt>""</tt>) sa interpretuje ako <i>"Hodnotu poľa nemeň"</i>. Na zmazanie hodnoty poľa (nahradenie prázdnou hodnotou) sa používa pomlčka / mínus (<tt>"-"</tt>). Napríklad:<br><br><?php
    ?><pre>
"ean","price_taxed","available_from",...
"8588003391186","12.50","",...          // zmení sa price_taxed, available_from sa nemení
"9788089322008","","2020-03-15",...     // price_taxed sa nemení, zmení sa available_from
"8588003391223","","-",...              // price_taxed sa nemení, zmaže sa available_from</pre><?php
    ?><br><?php
    ?><b>2]</b> Nakopírujte importný súbor do priečinku <code>/userfiles/files/import</code>, vyberte ho z nasledujúceho selectboxu a spustite import:<br><?php
    ?><br><?php
}
$importInfo = Html::endCapture();
App::setSeoTitle($title);
App::loadModel('Eshop', 'EshopCurrency');
$Currency = new EshopCurrency();
echo Html::smartForm(array(
    'title' => $title,
    'buttonLabel' => __a(__FILE__, 'Importovať'),
    'closeButtonLabel' => false,
    'saveAndCloseButtonLabel' => false,
    'data' => $this->params['data'],
    'Model' => $this->params['Model'],
    'columns' => 6,
    'fields' => array(
        array('if' => $importInfo),
            array('row', 'columns' => 1),
                array('col'),
                    array('html' => $importInfo),
                array('/col'),
            array('/row'),
        array('endif'),
        array('row'),
            array(
                'field' => 'importPid',
                'label' => __a(__FILE__, 'Import name'),
                'type' => !empty($importPid) ? 'hidden' : 'select',
                'empty' => true,
                'options' => $imports,
            ),
            array('if' => $importPid === 'mrpStockUpdate'),
                array(
                    'field' => 'useFullCatalogue',
                    'label' => __a(__FILE__, 'Importovať aj info o skladovom mieste a dodávateľovi'),
                    'type' => 'checkbox',
                ),
            array('elseif' => $importPid === 'pemicUpdate'),
                array(
                    'field' => 'useFullCatalogue',
                    'label' => __a(__FILE__, 'Import z celého katalógu'),
                    'hint' => __a(
                        __FILE__, 
                        'Aktualizovať z celého katalógu alebo len z rozdielového za posledných %s dní?',
                        7
                    ),
                    'type' => 'checkbox',
                ),
            array('elseif' =>
                $importPid === 'pemicAddition'
                || $importPid === 'ikarAddition'
                || $importPid === 'partnertechnicAddition'
            ),
                array(
                    'field' => 'importAmount',
                    'label' => __a(__FILE__, 'Počet importovaných položiek'),
                    'hint' => __a(__FILE__, 'Ohraničenie počtu importovaných položiek. Má zmysel viacmenej len za účelom testovania. Ak sa nevyplní (alebo <code>0</code>), tak počet importovaných položiek je neobmedzený.'),
                    'type' => 'text',
                ),
                array(
                    'field' => 'useFullCatalogue',
                    'label' => __a(__FILE__, 'Import celého katalógu'),
                    'hint' => __a(
                        __FILE__, 
                        'Importovať celý katalóg alebo len rozdielový za posledných %s dní?',
                        $importPid === 'pemicAddition'
                        ? '7' : '?'
                    ),
                    'type' => 'checkbox',
                ),
            array('elseif' =>
                $importPid === 'albatrosAddition'
            ),
                array(
                    'field' => 'importAmount',
                    'label' => __a(__FILE__, 'Počet importovaných položiek'),
                    'hint' => __a(__FILE__, 'Ohraničenie počtu importovaných položiek. Má zmysel viacmenej len za účelom testovania. Ak sa nevyplní (alebo <code>0</code>), tak počet importovaných položiek je neobmedzený.'),
                    'type' => 'text',
                ),
            array('elseif' => $importPid === 'kosmasAddition'),
                array(
                    'field' => 'importAmount',
                    'label' => __a(__FILE__, 'Počet importovaných položiek'),
                    'hint' => __a(__FILE__, 'Ohraničenie počtu importovaných položiek. Má zmysel viacmenej len za účelom testovania. Ak sa nevyplní (alebo <code>0</code>), tak počet importovaných položiek je neobmedzený.'),
                    'type' => 'text',
                ),
                array(
                    'field' => 'useFullCatalogue',
                    'label' => __a(__FILE__, 'Import celého katalógu'),
                    'hint' => __a(__FILE__, 'Importovať celý katalóg alebo len novinky za posledných 21 dní?'),
                    'type' => 'checkbox',
                ),
                array(
                    'field' => 'czkConversionRate',
                    'label' => __a(__FILE__, 'Kurz českéj koruny'),
                    'hint' => __a(__FILE__, '? Kč = 1 €, napr. <code>25.34</code>. Prednastavená hodnota je aktuálny konverzný kurz Kč načítaný z ECB'),
                    'type' => 'text',
                    'value' => $Currency->getActualConversionRate('CZK')
                ),
                array('col'),
                    array(
                        'html' => '<a href="http://www.nbs.sk/sk/statisticke-udaje/kurzovy-listok/grafy-kurzov/_CZK" target="_blank" style="display: block;margin-top: 32px;">kurzový lístok</a>',
                    ),
                array('/col'),
            array(
                'elseif' => 
                    $importPid === 'alteregoAddition'
                    || $importPid === 'frankanaAddition'
                    || $importPid === 'frankanaUpdate'
                    || $importPid === 'frankanaStockUpdate'
            ),
                // do not show the import file selectbox
            array('else'),
                array(
                    'field' => 'catalogueFile',
                    'label' => __a(__FILE__, 'Import file'),
                    'type' => 'select',
                    'hint' => __a(__FILE__, 'Filename of the catalogue file placed under /userfiles/files/import.'),
                    'empty' => true,
                    'options' => $files,
                ),
                array('col'),
                    array(
                        'html' => 
                            '<a href="" style="display: block;margin-top: 32px;">' .
                                '<i class="fa fa-refresh" style="padding-right:5px;"></i>' .
                                'Obnoviť zoznam súborov' .
                            '</a>'
                    ),
                array('/col'),
            array('endif'),
        array('/row'),
        array('if' => !empty($this->params['progress'])),
            array('row', 'columns' => 1),
                array('col'),
                    array('html' => '<pre>' . print_r($this->params['progress'], true) . '</pre>'),
                array('/col'),
            array('/row'),
        array('endif'),
    ),
));
