<?php
$filePid = $this->params['filePid'];
$downloadUrl = $this->params['downloadUrl'];
$files = $this->params['files'];
$title = __a(__FILE__, 'Stiahnúť súbor dodávateľa');
if (
    !empty($filePid)
    && !empty($files[$filePid])
) {
    $title .= ': "' . $files[$filePid] . '"';
}
elseif (!empty($filePid)) {
    $title .= ': "' . $filePid . '"';
}
// generate info
Html::startCapture();
if (!empty($filePid)) {
    ?>Kliknite na "Uložiť"<br><br><?php
}
else {
    ?>Vyberte súbor na stiahnutie a kliknite na "Uložiť"<br><br><?php
}
$info = Html::endCapture();

App::setSeoTitle($title);
echo Html::smartForm(array(
    'title' => $title,
    'data' => $this->params['data'],
    'Model' => $this->params['Model'],
    'columns' => 6,
    'fields' => array(
        array('if' => $info),
            array('row', 'columns' => 1),
                array('col'),
                    array('html' => $info),
                array('/col'),
            array('/row'),
        array('endif'),
        array('row'),
            array(
                'field' => 'filePid',
                'label' => __a(__FILE__, 'File name'),
                'type' => !empty($filePid) ? 'hidden' : 'select',
                'empty' => true,
                'options' => $files,
            ),
        array('/row'),
    ),
));

// launch programatically download in a new tab
if ($downloadUrl) {
    App::startJsCapture();
    ?><script type="text/javascript">window.open('<?php echo$downloadUrl ?>', '_blank');</script><?php
    App::endJsCapture();
}
