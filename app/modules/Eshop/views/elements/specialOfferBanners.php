<?php /* @var $this Template */
$this->displayOriginComment = true;
/**
 * Used as partial view in product view and cart view to generate special offers list
 * 
 * Params 'offers' and 'for' param must be passed here. Format of 'offers' param is an 
 * array returned by EshopSpecialOffer::getActive(). Param 'for' can have following values
 * 'promotedProduct', 'discountedProduct', 'cartProducts'
 */
$slugSpecialOfferView = App::getContentLocatorByPid('Eshop.EshopSpecialOffers.view');
$offers = Sanitize::value($this->params['offers']);
$for = Sanitize::value($this->params['for']);
foreach ($offers as $offer) {
    if (
        empty($offer['banner_image'])
        && empty($offer['banner_text']) 
        && (
            $for !== 'cartProducts'
            || (
                $offer['cart_banner_type'] !== 'discounted_products_index'
                && $offer['cart_banner_type'] !== 'discounted_products_slider'
            )
        )
        ||
        // do not show banner for promoted products of special offers applied by 
        // cart price threshold
        $offer['apply_by'] === 'cart_price_threshold'
        && $for === 'promotedProduct'
    ) {
       continue;
    }
    $offerClass = !empty($offer['applicable']) ? ' applicable' : '';
    $offerClass .= !empty($offer['applicated']) ? ' applicated' : '';
    $offerTitle = __(__FILE__, 'More info'); //$offer['name'];
    if (!empty($offer['applicated'])) {
        if ($for === 'discountedProduct') {
            $offerTitle = rtrim ($offer['discounted_products_label_text'], '. ') . '. ';
            $offerTitle .= __(__FILE__, 'Na základe obsahu košíka môžete tento produkt kúpiť za uvedenú zvýhodnenú cenu');
        }
    }
    elseif (!empty($offer['applicable'])) {
        if ($for === 'promotedProduct') {
            $offerTitle = rtrim ($offer['promoted_products_label_text'], '. ') . '. ';
            $offerTitle .= __(__FILE__, 'Ak tento produkt pridáte do košíka, tak niektoré produkty v košíku túto zvýhodnenu cenu získajú.');
        }
    }
    $url = '';
    if (!empty($offer['slug'])) {
        $url = App::getUrl(array(
            'locator' => $slugSpecialOfferView, 
            'args' => array($offer['slug'])
        ));
    }
    ?><div class="special-offer-banner promoting<?php echo $offerClass ?>"><?php 
        if ($url) {
            ?><a href="<?php echo $url?>" class="special-offer-banner-name" title="<?php echo $offerTitle ?>"><?php 
        }
        else {
            ?><div class="special-offer-banner-name"><?php
        }
        echo $offer['name'];
        if (!$url) {
            ?></div><?php
        }
        else {
            ?></a><?php
        }
        if (
            $for === 'cartProducts'
            && (
                $offer['cart_banner_type'] === 'discounted_products_index'
                || $offer['cart_banner_type'] === 'discounted_products_slider'
            )
        ) {
            if (!empty($offer['banner_text'])) {
                ?><div class="special-offer-banner-text"><?php 
                    echo $offer['banner_text'];
                ?></div><?php
            }
            if ($offer['cart_banner_type'] === 'discounted_products_slider') {                
                echo $this->loadControllerAction('EshopProducts', 'indexSlider', array(
                    'filter_product' => $offer['discounted_product_ids'],
                    //'hasImage' => true,
                    'availableOnly' => true,
                ));
            }
            else {
                echo $this->loadControllerAction('EshopProducts', 'index', array(
                    'filter_product' => $offer['discounted_product_ids'],
                    'showSortSelect' => false, 
                    'indexType' => 'detailed',
                    'showIndexTypeSelect' => false, 
                    'imageVariant' => 'small', 
                    'showSerialLabel' => false, 
                    'paginate' => false, 
                    'limit' => false,
                    'hasImage' => true,
                    'availableOnly' => true,
                ));
            }
        }
        else {
            if (!empty($offer['banner_image'])) {
                if ($url) {
                    ?><a href="<?php echo $url ?>" class="special-offer-banner-image" title="<?php echo $offerTitle ?>"><?php 
                }
                else {
                    ?><div class="special-offer-banner-image"><?php 
                }
                ?><img <?php 
                    ?>src="<?php echo $offer['banner_image'] ?>" <?php 
                    ?>alt="<?php echo Sanitize::htmlToText($offer['name'], false) ?>"<?php
                ?>/><?php
                if (
                    !empty($offer['applicable']) 
                    || !empty($offer['applicated'])
                ) {
                    ?><span class="check-mark" title="<?php echo $offerTitle ?>"></span><?php
                }
                if (!$url) {
                    ?></div><?php
                }
                else {
                    ?></a><?php
                }
            }
            if (!empty($offer['banner_text'])) {
                ?><div class="special-offer-banner-text"><?php 
                    echo $offer['banner_text'];
                ?></div><?php
            }
        }
    ?></div><?php
}
