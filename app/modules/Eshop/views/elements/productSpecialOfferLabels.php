<?php /* @var $this Template */
$this->displayOriginComment = true;
/**
 * Used as partial view in product indexes or product view to generate product 
 * special offers labels
 * 
 * All params of parent view plus 'slugSpecialOfferView', 'product' params must be 
 * passed here. Param 'for' is optional and it can have value 'cartProduct'
 */
$product = $this->params['product'];
$for = Sanitize::value($this->params['for']);
// discounting special offers
if (!empty($product['DiscountingSpecialOffer'])) {
    foreach ($product['DiscountingSpecialOffer'] as $offerId => $offer) {
        if (!empty($offer['discounted_products_label_image'])) {
            $offerClass = !empty($offer['applicable']) ? ' applicable' : '';
            $offerClass .= !empty($offer['applicated']) ? ' applicated' : '';
            $offerTitle = $offer['discounted_products_label_text'];
            if (!empty($offer['applicated'])) {
                $offerTitle = rtrim ($offerTitle, '. ') . '. ' 
                    . __(__FILE__, 'Na základe obsahu košíka môžete tento produkt kúpiť za uvedenú zvýhodnenú cenu');
            }
            if (!empty($offer['slug'])) {
                $url = App::getUrl(array(
                    'locator' => $this->params['slugSpecialOfferView'], 
                    'args' => array($offer['slug'])
                ));
                ?><a <?php 
                    ?>href="<?php echo $url ?>" <?php 
                    ?>class="special-offer-label discounting<?php echo $offerClass ?>" <?php 
                    ?>title="<?php echo $offerTitle ?>"<?php 
                ?>><?php 
            }
            else {
                ?><span <?php 
                    ?>class="special-offer-label discounting<?php echo $offerClass ?>" <?php 
                    ?>title="<?php echo $offerTitle ?>"<?php 
                ?>><?php 
            }
            ?><img <?php 
                ?>src="<?php echo $offer['discounted_products_label_image'] ?>" <?php 
                ?>alt="<?php echo $offerTitle ?>" <?php 
                ?>title="<?php echo $offerTitle ?>"<?php 
            ?>/><?php
            if (
                !empty($offer['applicable']) 
                || !empty($offer['applicated'])
            ) {
                ?><span class="check-mark"></span><?php
            }
            if (empty($offer['slug'])) {
                ?></span><?php
            }
            else {
                ?></a><?php
            }
            if (
                $for === 'cartProduct'
                && !empty($offer['applicated'])
            ) {
                break;
            }
        }
    }                                            
}
// promoting special offers
elseif (!empty($product['PromotingSpecialOffer'])) {
    foreach ($product['PromotingSpecialOffer'] as $offer) {
        if ($offer['apply_by'] === 'cart_price_threshold') {
            // do not show label for promoted products of special offers applied by 
            // cart price threshold
            continue;
        }
        if (!empty($offer['promoted_products_label_image'])) {
            $offerClass = !empty($offer['applicable']) ? ' applicable' : '';
            $offerClass .= !empty($offer['applicated']) ? ' applicated' : '';
            $offerTitle = $offer['promoted_products_label_text'];
            // applicated happens for promoted products only in cart
            if (!empty($offer['applicated'])) {                
                if ($offer['apply_by'] === 'promoted_products_cart_price_threshold') {                    
                    $offerTitle = rtrim ($offerTitle, '. ') . '. ' 
                        . __(__FILE__, 'Niektoré produkty v košíku túto zvýhodnenu cenu získali.');
                }
                else {                    
                    $offerTitle = rtrim ($offerTitle, '. ') . '. ' 
                        . __(__FILE__, 'Nasledujúci produkt túto zvýhodnenu cenu získal.');
                }
            }
            elseif (!empty($offer['applicable'])) {
                $offerTitle = rtrim ($offerTitle, '. ') . '. ' 
                    . __(__FILE__, 'Ak tento produkt pridáte do košíka, tak niektoré produkty v košíku túto zvýhodnenu cenu získajú.');
            }
            if (!empty($offer['slug'])) {
                $url = App::getUrl(array(
                    'locator' => $this->params['slugSpecialOfferView'], 
                    'args' => array($offer['slug'])
                ));
                ?><a <?php 
                    ?>href="<?php echo $url ?>" <?php 
                    ?>class="special-offer-label promoting<?php echo $offerClass ?>" <?php 
                    ?>title="<?php echo $offerTitle ?>"<?php 
                ?>><?php 
            }
            else {
                ?><span <?php 
                    ?>class="special-offer-label promoting<?php echo $offerClass ?>" <?php 
                    ?>title="<?php echo $offerTitle ?>"<?php 
                ?>><?php 
            }
            ?><img <?php 
                ?>src="<?php echo $offer['promoted_products_label_image'] ?>" <?php 
                ?>alt="<?php echo $offerTitle ?>" <?php 
                ?>title="<?php echo $offerTitle ?>"<?php 
            ?>/><?php
            if (
                !empty($offer['applicable']) 
                || !empty($offer['applicated'])
            ) {
                ?><span class="check-mark"></span><?php
            }
            if (empty($offer['slug'])) {
                ?></span><?php
            }
            else {
                ?></a><?php
            }
            if (
                $for === 'cartProduct'
                && !empty($offer['applicated'])
            ) {
                break;
            }
        }
    }
}
