<?php /* @var $this Template */
$this->displayOriginComment = true;

if (
    empty($this->params['showIndexTypeSelect'])
    && empty($this->params['showSortSelect'])
    && empty($this->params['showFilterSelect'])
) {
    return;
}

App::loadLib('App', 'FormHelper');
$Form = new FormHelper(array(
    'data' => array_merge(Sanitize::value($this->params['data'], array()), $_GET),
    'useDataWrapper' => false,
));

// check if extended filter is used
$filterVals = Sanitize::value($_GET['filter'], array());
unset($filterVals['actual_price']);
$isEmpty = true;
foreach ($filterVals as $f => $v) {
    if (!empty($v)) {
        $isEmpty = false;
    }
}

$isSelectedFilterItem = function($filterItemValue, $actualFilterValue) {
    $filterItemValueRegex = '/' . preg_quote('+' . $filterItemValue . '+', '/') . '/';
    return (bool)preg_match($filterItemValueRegex, '+' . $actualFilterValue . '+');
};

App::setJsFiles('/app/js/vendors/jquery-ui/jquery-ui.js');
App::setJsFiles('/app/js/vendors/jquery-ui/jquery.ui.touch-punch.min.js');
App::setJsFiles('/app/js/vendors/jquery-nice-select/js/jquery.nice-select.min.js');

App::setCssFiles('/app/js/vendors/jquery-ui/jquery-ui.css');
App::setCssFiles('/app/js/vendors/jquery-nice-select/css/nice-select.css');

App::startJsCapture(); ?>
<script type="text/javascript">

    function removeFilterItem(closeObj, filterItemValue) {
        var actualFilterValue = $(closeObj).closest('.multiselect').find('input.real').val();
        actualFilterValue = '+' + actualFilterValue + '+';
        actualFilterValue = actualFilterValue.replace('+' + filterItemValue + '+', '').replace(/^\++/g, '').replace(/\++$/g, '');
        $(closeObj).closest('.multiselect').find('input.real').val(actualFilterValue);
        $('#filterForm').submit();
    }
    
    $(function() {
        
        <?php if (!empty($_GET['data']['_target']) && !$isEmpty) : ?>
        setTimeout(function(){
            $('html, body').animate({
                scrollTop: $("#filterForm").offset().top - 110
            }, 0);
        }, 250);
        <?php endif ?>

        $('select').niceSelect();
        
        $('#filterForm select').change(function() {
            $('#filterForm').submit();
        });

        $('#filterForm .singlecheckbox').click(function() {
            $('#filterForm').submit();
        });

        $('#filterForm .multicheckbox').click(function() {
            var chcks = $(this).closest('.multiselect-list').find('.multicheckbox:checked');
            var values = [];
            $.each(chcks, function(ind, obj) {
                values.push($(obj).val());
            });
            $(this).closest('.multiselect').find('input.real').val(values.join('+'));
            $('#filterForm').submit();
        });

        // toggle filter
        $('.display-extended-filter-btn').click(function() {
            $ ( this ).toggleClass('is-active');
            var block = $( '.extended-filter' );
            block.slideToggle( 400, function() {

            });
        });
        $('#cancel-filter-btn').click(function() {
            var url = decodeURI(window.location.href);
            // remove any "filter[...]=...&" from url
            url = url.replace(/filter\[[^\]]*\]=[^&]*&?/g, '');
            // and replace it by "filter[]="
            if (url.indexOf('?') === -1) {
                url += '?filter[]=';
            }
            else {
                url += '&filter[]=';
            }
            window.location = url;
        });
    });
</script>
<?php App::endJsCapture(); ?>
<div class="multifilter index-controls">
    <div class="filter-bar cf"><?php 
        if (!empty($this->params['showIndexTypeSelect'])) {
            echo $this->loadView('EshopProducts/indexTypeSelect');
        } 
        ?><div class="right-controls-wrapper"><?php
            if (
                !empty($this->params['Paginator'])
                && !empty($this->params['showSortSelect'])
            ) {
                ?><div class="price-sort-select"><?php
                    // SORTING
                    echo $this->params['Paginator']->getSortSelect(array(
        //                'label' => __(__FILE__, 'Sort by'),
                        'class' => 'sort-select',
                        'options' => array(
                            '' => __(__FILE__, 'Sort by'),
                            'name:ASC' => __(__FILE__, 'name (a-z)'),
                            'name:DESC' => __(__FILE__, 'name (z-a)'),
                            'actual_price:ASC' => __(__FILE__, 'price (ascendinng)'),
                            'actual_price:DESC' => __(__FILE__, 'price (descending)'),
                            'EshopProductAuthor>EshopAuthor.name:ASC' => __(__FILE__, 'author (a-z)'),
        //                    'EshopManufacturer.name:ASC' => __(__FILE__, 'manufacturer (a-z)'),
        //                    'EshopManufacturer.name:DESC' => __(__FILE__, 'manufacturer (z-a)'),
                        ),
                        'resetParams' => 'sort',
                    ));
                ?></div><?php
            }
            if (!empty($this->params['showFilterSelect'])) {
                ?><a <?php 
                    ?>class="display-extended-filter-btn button<?php 
                        if (!$isEmpty) {
                            ?> is-active<?php
                        }
                    ?>"<?php 
                ?>><?php 
                    echo __(__FILE__, 'Filter')
                ?></a><?php 
            }
        ?></div><?php 
    ?></div><?php 
    if (!empty($this->params['showFilterSelect'])) {
        ?><form <?php 
            ?>method="get" <?php 
            ?>id="filterForm" <?php 
            ?>class="extended-filter cf" <?php 
            if ($isEmpty) {
                ?>style="display: none;"<?php
            }
        ?>><?php
            ?><input type="hidden" name="data[_target][]" value="Eshop.EshopProducts.index" /><?php
            ?><input type="hidden" name="data[_target][]" value="Eshop.EshopProducts.indexSearch" /><?php
            ?><input type="hidden" name="data[_target][]" value="Eshop.EshopProducts.miniSearch" /><?php
            if (isset($_GET['data']['keywords'])){ ?>
                ?><input type="hidden" name="data[keywords]" value="<?php echo $_GET['data']['keywords'] ?>" /><?php
            }
            // STOCK
            $options = array(
                '>0' => __(__FILE__, 'skladom'),
                '=0' => __(__FILE__, 'skladom u dodávateľa'),
            );        
            $actualFilterValue = Sanitize::value($_GET['filter']['stock']);
            ?><div class="multiselect cf"><?php
                ?><div class="multiselect-dropdown"><?php
                    ?><div class="multiselect-button"><?php 
                        echo __(__FILE__, 'Dostupnosť') . '...'; 
                    ?></div><?php

                    ?><input <?php 
                        ?>type="hidden" <?php 
                        ?>class="real" name="filter[stock]" <?php 
                        ?>value="<?php echo $actualFilterValue ?>"<?php 
                    ?>><?php
                    ?><ul class="multiselect-list"><?php 
                        foreach ($options as $filterItemValue => $filterItemLabel) { 
                            ?><li><?php 
                                ?><div class="nice-checkbox"><?php
                                    ?><input <?php 
                                        ?>id="stock-<?php echo $filterItemValue; ?>" <?php 
                                        ?>class="multicheckbox" value="<?php echo $filterItemValue; ?>" <?php 
                                        ?>type="checkbox" <?php 
                                        if ($isSelectedFilterItem($filterItemValue, $actualFilterValue))  {
                                            ?>checked="checked"<?php
                                        }
                                    ?>><?php
                                    ?><span class="image"></span><?php
                                    ?><label for="stock-<?php echo $filterItemValue; ?>"><?php
                                        ?><span class="label-text-right"><?php 
                                            echo $filterItemLabel 
                                        ?></span><?php
                                    ?></label><?php
                                ?></div><?php
                            ?></li><?php 
                        } 
                    ?></ul><?php
                ?></div><?php
                if ($actualFilterValue) {
                    ?><div class="selected-items"><?php
                        foreach ($options as $filterItemValue => $filterItemLabel) {
                            if ($isSelectedFilterItem($filterItemValue, $actualFilterValue))  {
                                ?><div class="sel-item cf"><?php 
                                    ?><span><?php 
                                        echo $filterItemLabel; 
                                    ?></span><?php 
                                    ?><a <?php 
                                        ?>href="javascript:void(0);" <?php 
                                        ?>onclick="removeFilterItem(this, '<?php echo $filterItemValue ?>');"<?php 
                                    ?>><?php 
                                    ?></a><?php 
                                ?></div><?php
                            }
                        }
                    ?></div><?php
                }
            ?></div><?php

            // NEWS
            ?><div class="multiselect checkbox nice-checkbox cf">
                <input <?php 
                    ?>value="1" <?php 
                    ?>name="filter[new]" <?php 
                    ?>class="singlecheckbox" <?php 
                    ?>id="new-checkbox" <?php 
                    ?>type="checkbox" <?php 
                    if (!empty($_GET['filter']['new'])) {
                        ?>checked="checked"<?php
                    }
                ?>>
                <span class="image"></span>
                <label class="shop-checkbox cf" for="new-checkbox">
                    <span class="label-text-right"><?php 
                        echo __(__FILE__, 'Novinky'); 
                    ?></span>
                </label>
            </div>
            <?php

            // DISCOUNTS
            ?><div class="multiselect checkbox nice-checkbox cf">
                <input <?php 
                    ?>value="1" <?php 
                    ?>name="filter[discounted]" <?php 
                    ?>class="singlecheckbox" <?php 
                    ?>id="action-checkbox" <?php 
                    ?>type="checkbox" <?php 
                    if (!empty($_GET['filter']['discounted'])) {
                        ?>checked="checked"<?php
                    }
                ?>>
                <span class="image"></span>
                <label class="shop-checkbox cf" for="action-checkbox">
                    <span class="label-text-right"><?php 
                        echo __(__FILE__, 'Akcie'); 
                    ?></span>
                </label>
            </div><?php
            if (!$isEmpty) {
                ?><a id="cancel-filter-btn" class="button"><?php 
                    echo __(__FILE__, 'Zrušiť')
                ?></a><?php
            }
        ?></form><?php
    }
?></div><?php
