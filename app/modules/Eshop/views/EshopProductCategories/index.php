<?php /* @var $this Template */
$this->displayOriginComment = true;
// if no items neither no message to display then return nothing
if (
    empty($this->params['items'])
    && empty($this->params['emptyIndexMessage'])
) {
    return;
}
$columns = $this->params['columns'];
$content = Sanitize::value($this->params['content']);
$indexId = uniqid('categories-index-');
?><div class="categories-index" id="<?php echo $indexId ?>"><?php
    // content text before index
    if (
        !empty($content['text'])
        && !empty($content['showText'])
        && (
            $content['showText'] === 'enum_without_index'
            || $content['showText'] === 'enum_before_index'
        )
    ) {
        ?><div class="index-text"><?php 
            echo $content['text'] 
        ?></div><?php 
    }
    // items
    if (
        empty($content['text'])    
        || empty($content['showText'])
        || $content['showText'] !== 'enum_without_index'
    ) {
        ?><div class="index"><?php 
            if (empty($this->params['items'])) {
                echo App::loadElement('App', 'emptyIndexMessage', array('message' => $this->params['emptyIndexMessage']));
            }
            else {
                $this->params['items'] = array_values($this->params['items']);
                $lastIndex = count($this->params['items']) - 1;
                foreach ($this->params['items'] as $i => $item) { 
                    $urlAddToCart = App::getUrl(array(
                        'module' => $this->module,
                        'controller' => 'EshopCarts',
                        'action' => 'addProducts',
                        'args' => array($item['id'])
                    ));
                    $urlShowDetail = App::getUrl(array(
                        'locator' => $this->params['slugItemView'],
                        'args' => array($item['slug'])
                    ));
                    // create row starts if number of columns is provided
                    if (
                        !empty($columns)
                        && $i % $columns == 0
                    ) {
                        $rowPositionClass = '';
                        if ($i === 0) {
                            $rowPositionClass = ' first';
                        }
                        if ($i + $columns - 1 >= $lastIndex) {
                            $rowPositionClass .= ' last';
                        }
                        ?><div class="index-row<?php echo $rowPositionClass ?>"><?php
                    }
                    // get row position class if number of columns is provided
                    $positionClass = '';
                    if (!empty($columns)) {
                        if ($i % $columns == 0) {
                            $positionClass = ' first';
                        }
                        elseif (($i + 1) % $columns == 0) {
                            $positionClass = ' last';
                        }
                    }
                    // item block
                    ?><div class="item<?php echo $positionClass ?>"<?php echo $this->params['SmartAdminLauncher']->markRecord($item['id']) ?>><?php
                        ?><div class="spacer"><?php 
                            // image
                            if (!empty($item['image'])) {
                                ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'More info') ?>" class="image"><?php
                                        ?><img src="<?php echo $item['image']; ?>" alt="<?php echo $item['name']; ?>" /><?php
                                ?></a><?php
                            }
                            // title
                            ?><h3 class="title"><?php
                                ?><a href="<?php echo $urlShowDetail; ?>" title="<?php echo __(__FILE__, 'More info') ?>"><?php
                                    echo $item['name'];
                                ?></a><?php
                            ?></h3><?php
                            // info
                            ?><div class="info"><?php 
                            ?></div><?php
                        ?></div><?php
                    ?></div><?php
                    // create row ends if number of columns is provided
                    if (
                        !empty($columns)
                        && (
                            $i == $lastIndex
                            || ($i + 1) % $columns == 0
                        )
                    ) {
                        ?></div><?php
                    } 
                }
            }
        ?></div><?php
        // paginator
        if (isset($this->params['Paginator'])) {
            //$links = $this->params['Paginator']->getLinks();
            $pagesLinks = $this->params['Paginator']->getPagesLinks();
            $pageSelect = $this->params['Paginator']->getPageSelect(array(
                'label' => __(__FILE__, 'Page'),
                'class' => 'page-select',
            ));
            $limitSelect = $this->params['Paginator']->getLimitSelect(array(
                'label' => __(__FILE__, 'Limit'),
                'class' => 'limit-select',
            ));
            if (
                !empty($pagesLinks)
                || !empty($pageSelect)
                || !empty($limitSelect)
            ) {
                ?><div class="paginator"><?php 
                    //echo $links; 
                    echo $pagesLinks;
                    echo $pageSelect;
                    echo $limitSelect;            
                ?></div><?php
            }
        }
    }
    // content text after items
    if (
        !empty($content['text'])
        && !empty($content['showText'])
        && $content['showText'] === 'enum_after_index'
    ) {
        ?><div class="index-text"><?php 
            echo $content['text'] 
        ?></div><?php 
    }
?></div><?php
// set item css width according number of columns
if (!empty($columns)) {
    App::startCssCapture();
    ?><style type="text/css">
        .categories-index .index .item {
            width: <?php echo 100 / $columns ?>%;
        }
    </style><?php
    App::endCssCapture();
}
// align item images on bottom (using .spacer margin-top)
// and set the same heights of all .item block to make borders looks ok
//
// If jQuery(window).load() does not work correctly the see this http://stackoverflow.com/questions/910727/jquery-event-for-images-loaded
// and connect events on image loading
App::startJsCapture();
?><script type="text/javascript">
    jQuery(window).load(function(){
        function align() {
            var index = jQuery('#<?php echo $indexId ?>'), rows = index.find('.index-row');
            if (rows.length === 0) {
                return;
            }
            rows.each(function(){
                var row = jQuery(this), rowHeight, images = row.find('.image'),
                maxImageHeight = 0, items = row.find('.item');
                // reset items explicit height set before here below
                items.css('height', '');
                // if item blocks are not floating then do nothing (just let reset the row height to implicit here above)
                if (items.first().css('float') === 'none') {
                    return;
                }
                // find actual row implicit height
                rowHeight = row.height();
                // align item images on bottom (using .spacer margin-top)
                images.each(function(){
                    var imageHeight = jQuery(this).height();
                    if (imageHeight > maxImageHeight) {
                        maxImageHeight = imageHeight;
                    }
                });
                images.each(function(){
                    var image = jQuery(this), imageHeight = image.height(), marginTop = maxImageHeight - imageHeight;
                    if (marginTop !== 0) {
                        image.closest('.spacer').css('margin-top', marginTop + 'px');
                    }
                });
                // set the same height for all items to align left/right border
                row.find('.item').outerHeight(rowHeight); 
            });
        };
        // call align on window load
        align();
        // call align on each window resize
        jQuery(window).resize(function(){align()});
    });
</script><?php
App::endJsCapture();

// RETURN
return;

//
// Code store for other projects
//

