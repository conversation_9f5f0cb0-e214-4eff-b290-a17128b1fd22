<?php /* @var $this Template */
$this->displayOriginComment = true;
$menuId = uniqid('categories-menu-');
/*
echo Html::toggleButton('#' . $menuId, array(
    'class' => 'product-categories-menu-toggle-button',
    'label' => !empty($this->params['title']) ? $this->params['title'] : __(__FILE__, 'Categories'),
));
*/
?><nav id="<?php echo $menuId ?>" class="categories-menu"><?php 
    /*
    if (!($sideContentTabTitle = App::getGlobal('App', 'sideContentTabTitle'))) {
        App::setGlobal('App', 'sideContentTabTitle', true);
    }
    if (!empty($this->params['title'])) {
        if (!$sideContentTabTitle) {
            App::setGlobal('App', 'sideContentTabTitle', $this->params['title']);
        }
        else {
            ?><div class="title"><?php
                echo $this->params['title'];
            ?></div><?php
        }
    }
    */
    echo Html::menu($this->params['list'], array(
        'activeItem' => $this->params['activeItem'],
        'homeSlug' => HOME_SLUG,
        'lang' => URL_LANG,
        'urlBase' => $this->params['urlBase'],
        'labelWrappers' => array('l'),
        'showExpandButtons' => true,
        'SmartAdminLauncher' => $this->params['SmartAdminLauncher'],
        'iconAsImage' => true,
        'level03ButtonMore' => true,
        'level02ParentHeading' => true,
    ));
?></nav><?php
// opener selector - js selector of elements in menu which will open/close (slide down/up)
// the menu items after mouse clicking
$openerSelector = 'a > .l'; // span.l is opener (contains [+] / [-])
//$openerSelector = '.has-subitems > a'; // whole link is opener
App::startJsCapture();
?><script type="text/javascript">
jQuery(function() {
    
    // activate dropdown menu
    var button = jQuery('#all-categories-button');
    var list = jQuery('#<?php echo $menuId ?>');
    button.click(function(e){
        if (jQuery(window).width() > 1000) {
            e.stopPropagation();
            jQuery(this).toggleClass('active');
            list.slideToggle(100);
        }
    });
    /*list.mouseleave(function(e){
        if (jQuery(window).width() > 1000) {
            jQuery(this).slideToggle(100);
            button.removeClass('active');
        }
    });*/
    jQuery(window).resize(function(){
        if (jQuery(window).width() > 1000) {
            jQuery(".categories-menu .expand-button").removeClass('active');
            jQuery(".categories-menu .level-02").css('display', '');
        }
    });
    jQuery(window).resize(function(){
        if (jQuery(window).width() <= 1000) {
            jQuery(".categories-menu").css('display', '');
        }
    });
    
    list.find('<?php echo $openerSelector ?>').on('click', function(e){
        e.preventDefault();
        var li = jQuery(this).closest('li');
        li.find('> ul').stop().slideToggle(500, function(){
            if (
                li.hasClass('-opened')
                || (
                    !li.hasClass('-opened')
                    && !li.hasClass('-closed')
                    && (
                        li.hasClass('open')
                        || li.hasClass('active')
                    )
                )
            ) {
                li.removeClass('-opened');
                li.addClass('-closed');
            }
            else {
                li.removeClass('-closed');
                li.addClass('-opened');
            }
        });
    });
    
    // activate expand-button in main-navigation
    var expandButtons = jQuery("#<?php echo $menuId ?> .expand-button");
    expandButtons.on('click', function(event){
        if (jQuery(window).width() <= 1000) {
            var expandButton = jQuery(this);
            // toggle the actual submenu
            if (!expandButton.hasClass('active')){
                expandButton.addClass('active');
                expandButton.closest('li').find('> ul').slideDown(200);
            }
            else {
                expandButton.removeClass('active');
                expandButton.closest('li').find('> ul').slideUp(200);
            }
            event.stopPropagation();
            event.preventDefault();
        }
    });
    
    jQuery("#<?php echo $menuId ?> > .level-01 > li > a").mouseenter(function(e){
        if (jQuery(window).width() > 1000) {
            var catMenuHeight = $(this).closest(".categories-menu").height();
            $(this).closest('li').find('.level-02').css('height', catMenuHeight);
            
        }
    });
    jQuery("#<?php echo $menuId ?> .level-03-button-more").click(function(e) {
        if (!$(this).hasClass('all')) {
            $(this).closest('.level-03').find('.level-03-items-hidden').addClass("level-03-items-expanded")
            $(this).closest('.level-03').find('li').removeClass("level-03-items-hidden");
            $(this).addClass('all');
            $(this).text(__js('Eshop', 'Skryť kategórie'))
        }
        else{
            $(this).closest('.level-03').find('.level-03-items-expanded').addClass("level-03-items-hidden");
            $(this).closest('.level-03').find('li').removeClass("level-03-items-expanded");
            $(this).removeClass('all');
            $(this).text(__js('Eshop', 'Ďalšie kategórie'))
        }
    });
    
});
</script><?php
App::endJsCapture();
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
));
App::setCssFiles(array(
    '/app/css/vendors/font-awesome/css/font-awesome.min.css',
));

//echo App::loadControllerAction('Eshop', 'EshopProducts', 'index', array('sort_bestsellers' => true, 'limit' => 5, 'view' => 'miniSideIndex'));
//echo App::loadControllerAction('Eshop', 'EshopProducts', 'index', array('filter_group' => 'top-produkty', 'limit' => 5, 'view' => 'miniSideIndex', 'paginate' => false)); 
