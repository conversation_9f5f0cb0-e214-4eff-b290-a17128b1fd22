<?php /* @var $this Template */
$this->displayOriginComment = true;
$data = &$this->params;
$actionParams = array(    
    'paginate' => Sanitize::value($data['paginate'], false),
    'limit' => Sanitize::value($data['limit']),
    'avoidProduct' => Sanitize::value($data['avoidProduct']),
    'sort_bestsellers' => Sanitize::value($data['sort_bestsellers'], false),
    'availableOnly' => Sanitize::value($data['availableOnly'], false),
    'discount' => Sanitize::value($data['discount'], false),
    'loadMoreButtonLabel' => Sanitize::value($data['loadMoreButtonLabel'], false),
);
if (!empty($data['indexType'])) {
    $actionParams['indexType'] = $data['indexType'];
}
if ($data['typeOfFilter'] === 'static') {
    $actionParams['filter_product'] = Sanitize::value($data['filter_product']);
    $actionParams['filter_group'] = Sanitize::value($data['filter_group']);
    $actionParams['filter_category'] = Sanitize::value($data['filter_category']);
    $actionParams['filter_type'] = Sanitize::value($data['filter_type']);
    $actionParams['filter_manufacturer'] = Sanitize::value($data['filter_manufacturer']);
    $actionParams['filter_author'] = Sanitize::value($data['filter_author']);
}
else {
    $actionParams['filter'] = Sanitize::value($data['filter']);
    $actionParams['set_seo'] = Sanitize::value($data['set_seo'], false);
}
$actionHtml = $this->loadControllerAction('EshopProducts', 'index', $actionParams, true, App::$args);
if (!$actionHtml) {
    return;
}
$uniqueClass = uniqid('cb-');
?><section class="cb-products-index <?php echo $uniqueClass ?>"><?php
    if (!empty($data['block_center_content'])) {
        if ($data['block_center_content'] === 'narrow') {
            ?><div class="center narrow"><?php
        }
        else {
            ?><div class="center"><?php
        }
    }
    echo App::loadView('ContentBlock', 'blockTitle', $this->params);
    echo $actionHtml;
    if (!empty($data['block_center_content'])) {
        ?></div><?php
    }
?></section><?php

App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css',
));
App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueClass' => $uniqueClass)));
