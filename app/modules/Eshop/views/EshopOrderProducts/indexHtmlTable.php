<?php $sum = 0 ?>
<?php if (empty($this->params['items'])) : ?>
<?php echo __(__FILE__, 'Order does not contain any item.') ?>
<?php else : ?>
<table border="0" class="html_table">
    <tr>
        <th><?php echo __(__FILE__, 'Code') ?></th>
        <th><?php echo __(__FILE__, 'Item name') ?></th>
        <th><?php echo __(__FILE__, 'Amount') ?></th>
        <th><?php echo __(__FILE__, 'Unit price') ?></th>
        <th><?php echo __(__FILE__, 'Price') ?></th>
        <th><?php echo __(__FILE__, 'Attribute') ?></th>
    </tr>
    <?php foreach ($this->params['items'] as $item) : ?>
    <?php 
        $item['static_attributes'] = json_decode($item['static_attributes']);
        $item['dynamic_attributes'] = json_decode($item['dynamic_attributes']);
        $item['attribute'] = '';
        foreach($item['static_attributes'] as $key => $value) {
            $item['attribute'] .= __(__FILE__, $key) . ': ' . $value . '<br>';
        }
        foreach($item['dynamic_attributes'] as $key => $value) {
            $item['attribute'] .= __(__FILE__, $key) . ': ' . $value . '<br>';
        }
    ?>
    <tr>
        <td><?php echo $item['code'] ?></td>
        <td><?php echo $item['name'] ?></td>
        <td class="number"><?php echo $item['amount'] ?></td>
        <td class="number"><?php echo number_format(($item['price_actual_taxless'] + $item['tax_actual']), 2, ',', '') ?> &euro;</td>
        <td class="number"><?php echo number_format((($item['price_actual_taxless'] + $item['tax_actual']) * $item['amount']), 2, ',', '') ?> &euro;</td>
        <td><?php echo $item['attribute'] ?></td>
    </tr>
    <?php $sum += (($item['price_actual_taxless'] + $item['tax_actual']) * $item['amount']); ?>
    <?php endforeach ?>
    <tr><td colspan="4">&nbsp;</td></tr>
    <tr>
        <td><?php echo __(__FILE__, 'Total') ?>:</td>
        <td></td>
        <td></td>
        <td></td>
        <td class="number"><?php echo number_format($sum, 2, ',', '') ?> &euro;</td>
    </tr>
</table>
<?php endif; ?>

