<?php /* @var $this Template */
$this->displayOriginComment = true;
$data = &$this->params;
$buttonText = Sanitize::value($data['button_text']);
$buttonUrl = Sanitize::value($data['button_url']);
$image = Sanitize::value($data['image']);
$actionParams = array(    
    'paginate' => Sanitize::value($data['paginate'], false),
    'limit' => Sanitize::value($data['limit']),
    'avoidProduct' => Sanitize::value($data['avoidProduct']),
    'sort_bestsellers' => Sanitize::value($data['sort_bestsellers'], false),
    'availableOnly' => Sanitize::value($data['availableOnly'], false),
    'discount' => Sanitize::value($data['discount'], false),
    'loadMoreButtonLabel' => Sanitize::value($data['loadMoreButtonLabel'], false),
    'limitImageHeight' => true, //(bool)$image,
    'sliderResponsiveOptions' => !$image ? array() : array(
        0 =>  array(
            'items' => 1,
            'slideBy' => 1,
            'margin' => 10,
        ),
        /*/
        541 =>  array(
            'items' => 1,
            'slideBy' => 1,
            'margin' => 10,
        ),
        /*/
        801 =>  array(
            'items' => 2,
            'slideBy' => 2,
            'margin' => 10,
        ),
        1101 =>  array(
            'items' => 3,
            'slideBy' => 3,
            'margin' => 10,
        ),
    ),
);
if ($data['typeOfFilter'] === 'static') {
    $actionParams['filter_product'] = Sanitize::value($data['filter_product']);
    $actionParams['filter_group'] = Sanitize::value($data['filter_group']);
    $actionParams['filter_category'] = Sanitize::value($data['filter_category']);
    $actionParams['filter_type'] = Sanitize::value($data['filter_type']);
    $actionParams['filter_manufacturer'] = Sanitize::value($data['filter_manufacturer']);
    $actionParams['filter_author'] = Sanitize::value($data['filter_author']);
}
else {
    $actionParams['filter'] = Sanitize::value($data['filter']);
    $actionParams['set_seo'] = Sanitize::value($data['set_seo'], false);
}
$actionHtml = $this->loadControllerAction('EshopProducts', 'indexSlider', $actionParams, true, App::$args);
if (!$actionHtml) {
    return;
}
$uniqueClass = uniqid('cb-');
$class = 'cb-products-slider';
if (empty($data['title'])) {
    $class .= ' no-title';
}
if ($image) {
    $class .= ' with-image';
}
$class .= ' ' . $uniqueClass;
?><section class="<?php echo $class ?>"><?php
    if (!empty($data['block_center_content'])) {
        if ($data['block_center_content'] === 'narrow') {
            ?><div class="center narrow"><?php
        }
        else {
            ?><div class="center"><?php
        }
    }
    ?><div class="wrapper"><?php 
        echo App::loadView('ContentBlock', 'blockTitle', $this->params);
        ?><div class="image-wrapper"><?php 
            ?><div class="products"><?php
                echo $actionHtml;
            ?></div><?php
            if ($image) {
                ?><img src="<?php echo $image ?>"/><?php
            }
        ?></div><?php
        if ($buttonText && $buttonUrl) {
            ?><div class="button-wrapper"><?php
                ?><a class="button" href="<?php echo $buttonUrl ?>"><?php
                    echo $buttonText;
                ?></a><?php
            ?></div><?php
        }
    ?></div><?php
    if (!empty($data['block_center_content'])) {
        ?></div><?php
    }
?></section><?php

App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css',
));
//App::setJsFiles(array(
//    '/app/js/vendors/wow.min.js',
//));
/*
App::startJsCapture();
?><script type="text/javascript">
jQuery(function() {
    new WOW().init();
});
</script><?php
App::endJsCapture();
*/
App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueClass' => $uniqueClass)));
if ($image) {
    App::startJsCapture();
    // keep the image (its position) responsive
    ?><script type="text/javascript">
    jQuery(function(){
        var $image = jQuery('.<?php echo $uniqueClass ?> .image-wrapper > img');
        var imageDefaultRight = parseInt($image.css('right'));
        var $window = jQuery(window);
        function setImagePosition() {
            var windowWidth = $window.width();
            var imageWidth = $image.width();
            var imageLeftOffset = $image.offset().left;
            var imageOverhang = imageWidth + imageLeftOffset - windowWidth;
            var imageRight = parseInt($image.css('right'));
            if (imageOverhang !== 0) {
                // !!! the "rigth" property of overhanging image is negative
                // that is why the "+" (and not  "-") here below
                imageRight = imageRight + imageOverhang;
                if (
                    imageOverhang > 0 
                    && imageRight > 0
                ) {
                    imageRight = 0;
                }
                else if (
                    imageOverhang < 0 
                    && imageRight < imageDefaultRight
                ) {
                    imageRight = imageDefaultRight;
                }
                $image.css('right', imageRight);
            }
        }
        $image.on('load', setImagePosition);
        //rblsb//$window.on('resize', setImagePosition);
        $window.on('resize', function(){
            var timeoutId = null;
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
            timeoutId = setTimeout(setImagePosition, 100);
        });
    });
    </script><?php
    App::endJsCapture();
}

