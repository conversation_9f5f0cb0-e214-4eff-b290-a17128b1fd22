<?php /* @var $this Template */
$this->displayOriginComment = true;
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];
$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
echo App::loadView('ContentBlock', 'admin_blockTitleForm', array_merge($this->params, array()));
$formSectionId = uniqid('form-section-');
?><div id="<?php echo $formSectionId ?>"><?php
    ?><h2 class="-run-sfo-heading"><?php
        echo __a(__FILE__, 'Tlačítko na všetky produkty')
    ?></h2><?php
    ?><div class="grid-row grid-break-780"><?php
        ?><div class="grid-col grid-col-20-100"><?php
            echo $Form->text('button_text', array(
                'label' => __a(__FILE__, 'Text v tlačítku'),
                'hint' => __a(__FILE__, 'Ak ostane prázdny, tlačítko nad slajdrom produktov sa nezobrazí.'),
            ));
        ?></div><?php
        ?><div class="grid-col grid-col-20-100"><?php
            echo $Form->text('button_url', array(
                'label' => __a(__FILE__, 'Odkaz tlačítka'),
                'hint' => __a(__FILE__, 'Ak ostane prázdny, tlačítko nad slajdrom produktov sa nezobrazí.<br>Link tlačidla buď v absolútnej forme, napr. <code>https://www.google.com</code> alebo ak link smeruje na http://%s, tak môže byť aj v relativnej forme, napr. <code>/kontakt</code>. Na spustenie tlače zadaj <code>javascript:window.print()</code>. Na odoslanie mailu zadaj napr. <code>mailto:<EMAIL></code>. Na vytočenie telefónneho čísla (ak je stránka prehliadaná na mobilnom telefóne) zadaj napr. <code>tel:+421987654321</code>.', $_SERVER['HTTP_HOST']),
            ));
        ?></div><?php
        ?><div class="grid-col grid-col-20-100"><?php
            echo $Form->file('image', array(
                'label' => __a(__FILE__, 'Obrázok pod tlačítkom'),
                'hint' => __a(__FILE__, 'Ak je nahratý, tak slajder sa zobrazí v asymetrickom dizajne s obrázkom prečnievajúcim za pravý okraj centrálneho bloku. Výška obrázku je 480px až 490px. Širka obrázku aspoň 600px.'),
            ));
        ?></div><?php
        ?><div class="grid-col grid-col-40-100"><?php
            echo $Form->image('image', array(
                'deleteImage' => '/mvc/App/ContentBlockInstances/admin_deleteInstanceFile/image/' . $blockInstanceId,
            ));
        ?></div><?php
    ?></div><?php
    ?><h2 class="-run-sfo-heading"><?php
        echo __a(__FILE__, 'Výber produktov')
    ?></h2><?php
    ?><div class="grid-row grid-break-780"><?php
        ?><div class="grid-col grid-col-100-100"><?php
            echo $Form->radio('typeOfFilter', array(
                'options' => array(
                    'static' => __a(__FILE__, 'Na základe tu zvolených kritérií'),
                    'dynamic' => __a(__FILE__, 'Podľa slugu (skupiny/kategórie/výrobcu/...) v URL adrese'),
                ),
            ));
        ?></div><?php
    ?></div><?php
    ?><div class="static-filter-fields"><?php 
        ?><div class="grid-row grid-break-780"><?php
            ?><div class="grid-col grid-col-50-100"><?php
                echo $Form->itemselector('filter_product', array(
                    'label' => __a(__FILE__, 'Zobraz produkty'),
                    'options' => '/mvc/Eshop/EshopProducts/admin_list',
                ));
            ?></div><?php
            ?><div class="grid-col grid-col-50-100"><?php
                echo $Form->itemselector('filter_group', array(
                    'label' => __a(__FILE__, 'Zobraz produkty nasledovných produktových skupín'),
                    'options' => '/mvc/Eshop/EshopProductGroups/admin_getSelectorItems',
                ));
            ?></div><?php
        ?></div><?php
        ?><div class="grid-row grid-break-780"><?php
            ?><div class="grid-col grid-col-50-100"><?php
                echo $Form->treeselector('filter_category', array(
                    'label' => __a(__FILE__, 'Zobraz produkty nasledovných kategórií'),
                    'options' => '/mvc/Eshop/EshopProductCategories/admin_list',
                ));
            ?></div><?php
            ?><div class="grid-col grid-col-50-100"><?php
                echo $Form->itemselector('filter_manufacturer', array(
                    'label' => __a(__FILE__, 'Zobraz produkty nasledovných výrobcov'),
                    'options' => '/mvc/Eshop/EshopManufacturers/admin_getSelectorItems',
                ));
            ?></div><?php
        ?></div><?php
        /*/
        ?><div class="grid-row grid-break-780"><?php
            ?><div class="grid-col grid-col-50-100"><?php
                echo $Form->itemselector('filter_type', array(
                    'label' => __a(__FILE__, 'Zobraz produkty nasledovných typov'),
                    'options' => '/mvc/Eshop/EshopProductTypes/admin_getSelectorItems',
                ));
            ?></div><?php
            ?><div class="grid-col grid-col-50-100"><?php
                echo $Form->itemselector('filter_author', array(
                    'label' => __a(__FILE__, 'Zobraz produkty nasledovných autorov'),
                    'options' => '/mvc/Eshop/EshopAuthors/admin_getSelectorItems',
                ));
            ?></div><?php
        ?></div><?php
        //*/
    ?></div><?php
    ?><div class="dynamic-filter-fields"><?php 
        ?><div class="grid-row grid-break-780"><?php
            ?><div class="grid-col grid-col-20-100"><?php
                echo $Form->select('filter', array(
                    'label' => __a(__FILE__, 'Zobraz produkty'),
                    // keep this synchronized with options of EshopProduct::getFilterFindOptions()
                    'options' => array(
                        'group' => __a(__FILE__, 'produktovej skupiny, ktorej slug je v URL'),
                        'category' => __a(__FILE__, 'kategórie, ktorého slug je v URL'),
                        //'type' => __a(__FILE__, 'typu, ktorého slug je v URL'),
                        'manufacturer' => __a(__FILE__, 'výrobcu, ktorej slug je v URL'),
                        //'author' => __a(__FILE__, 'autora, ktorého slug je v URL'),
                    ),
                    'empty' => true,
                ));
            ?></div><?php
            ?><div class="grid-col grid-col-20-100"><?php
                echo $Form->checkbox('set_seo', array(
                    'label' => __a(__FILE__, 'Nastaviť SEO parametre'),            
                    'hint' => __a(__FILE__, 'Majú sa SEO parametre zobrazeného zoznamu nastaviť podľa SEO parametrov položky vybranej v <code>Zobraz produkty</code>?'),
                ));
            ?></div><?php    
        ?></div><?php
    ?></div><?php
    ?><h2 class="-run-sfo-heading"><?php
        echo __a(__FILE__, 'Iné')
    ?></h2><?php
    ?><div class="grid-row grid-break-780"><?php
        ?><div class="grid-col grid-col-20-100"><?php
            echo $Form->checkbox('paginate', array(
                'label' => __a(__FILE__, 'Stránkovať'),  
            ));
        ?></div><?php
        ?><div class="grid-col grid-col-20-100"><?php
            echo $Form->number('limit', array(
                'label' => __a(__FILE__, 'Počet položiek na stránke'),            
                'hint' => __a(__FILE__, 'Ak nie je strankovanie zaškrtnuté, tak ide o celkový počet zobrazených položiek'),
            ));
        ?></div><?php
        ?><div class="grid-col grid-col-20-100"><?php
            echo $Form->checkbox('availableOnly', array(
                'label' => __a(__FILE__, 'Len dostupné produkty'),            
            ));
        ?></div><?php
        ?><div class="grid-col grid-col-20-100"><?php
            echo $Form->checkbox('discount', array(
                'label' => __a(__FILE__, 'Len zlavnené produkty'),            
            ));
        ?></div><?php
        ?><div class="grid-col grid-col-20-100"><?php
            echo $Form->checkbox('sort_bestsellers', array(
                'label' => __a(__FILE__, 'Zoradiť od najpredávanejších'),            
            ));
        ?></div><?php
        /*/
        ?><div class="grid-col grid-col-30-100"><?php
            echo $Form->text('loadMoreButtonLabel', array(
                'label' => __a(__FILE__, 'Text tlačítka "Načítať ďalšie produkty"'),
                'hint' => __a(__FILE__, 'Ak ostane prázdny, zvolí sa preddefinovaná hodnota "Načítať ďalšie produkty".'),
            ));
        ?></div><?php
        /*/
    ?></div><?php
?></div><?php
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js'
));
App::startJsCapture();
?><script type="text/javascript">
jQuery(function() {
    var $formSection = jQuery('#<?php echo $formSectionId ?>'),
        $staticFilterFields = $formSection.find('.static-filter-fields'),
        $dynamicFilterFields = $formSection.find('.dynamic-filter-fields');
    function synchronizeTypeOfFilter(animate) {
        var typeOfFilter = $formSection.find('input[name$="[typeOfFilter]"]:checked').val();
        if (animate) {        
            if (typeOfFilter === 'static') {
                $dynamicFilterFields.slideUp('fast', function() {
                    $staticFilterFields.slideDown('fast');
                });
            }
            else {
                $staticFilterFields.slideUp('fast', function() {
                    $dynamicFilterFields.slideDown('fast');
                });
            }
        }
        else {
            if (typeOfFilter === 'static') {
                $dynamicFilterFields.hide();
                $staticFilterFields.show();
            }
            else {
                $staticFilterFields.hide();
                $dynamicFilterFields.show();
            }        
        }
    }
    synchronizeTypeOfFilter();
    $formSection.find('input[name$="[typeOfFilter]"]').click(function() {
        synchronizeTypeOfFilter(true);
    });
});
</script><?php
App::endJsCapture();

echo App::loadView('ContentBlock', 'admin_blockParamsForm', $this->params + array(
    //'showBlockCenterContent' => false,
));
App::setCssFiles(array(
    '/app/css/grid.css'
));
