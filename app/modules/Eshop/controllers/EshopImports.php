<?php

class EshopImports extends EshopController {
    
    /**
     * /mvc/Eshop/EshopImports/admin_importProductsAddition/{importPid}
     * 
     * NOTE: For moment not used, all imports of new products are done automatically (on vydavatel.sk)
     * 
     * @param string $importPid One of 'pemicAddition', 'informAddition', 'partnertechnicAddition',
     *      'kosmasAddition', 'alteregoAddition'
     * 
     * @return string
     */
    public function admin_importProductsAddition($importPid= null) {
        $Import = $this->loadModel('EshopProductImport', true);
        $progress = null;
        if ($this->data) {
            $progress = $Import->importAddition($this->data['importPid'], array(
                'catalogueFile' => Sanitize::value($this->data['catalogueFile']),
                'useFullCatalogue' => Sanitize::value($this->data['useFullCatalogue'], false),
                'czkConversionRate' => Sanitize::value($this->data['czkConversionRate']),
                'importAmount' => Sanitize::value($this->data['importAmount']),
//                'simulate' => true, //debug
//                'processAmount' => 10, //debug
            ));
            if (
                empty($progress)
                && ($processingErrors = $Import->getErrors('_processing'))
            ) {
                App::setErrorMessage(array_shift($processingErrors));
            }            
        }
        if (empty($this->data) && !empty($importPid)) {
            $this->data['importPid'] = $importPid;
        }
        return $this->loadView('EshopImports/admin_importProducts', array(
            'data' => $this->data,
            'Model' => array(
                array($Import, $importPid),
            ),
            'importPid' => $importPid,
            'imports' => array(
                //@todo
            ),   
            'progress' => $progress,
        ));
    }
    
    /**
     * Imports new products automatically for specified import name
     * 
     * @param string $importPid One of 'pemicAddition', 'informAddition', 'partnertechnicAddition',
     *      'albatrosAddition', 'ikarAddition', 'kosmasAddition', 'frankanaAddition'
     * @param integer $importAmount Amount of products to be imported. If 0 then all in catalogue are imported.
     *      Defaults to 0;
     * @param bool $useFullCatalogue Applied only for 'pemicAddition'
     *      If 1 then a catalogue_full file is downloaded. Defaults to 0. 
     * 
     * @return string Progress array dump
     */
    public function autoimportProductsAddition($importPid = null, $importAmount = 0, $useFullCatalogue = 0) {
        App::setLayout('App', 'admin');
        if (empty($importPid)) {
            return;
        }
        $Import = $this->loadModel('EshopProductImport', true);
        $progress = $Import->importAddition($importPid, array(
            'importAmount' => $importAmount,
            'useFullCatalogue' => $useFullCatalogue,
//            'catalogueFile' => '/userfiles/files/import/inform.csv', //debug
//            'simulate' => true, //debug
//            'processAmount' => 10, //debug
        ));
        if ($progress) {
            $progress['errors'] = array_merge($progress['errors'], $Import->getErrors());
        }
        else {
            $progress['errors'] = $Import->getErrors();
        }
        return '<pre>' . print_r($progress, true) . '</pre>';
    }
    
    /**
     * 
     * @param string $importPid One of 'pemicAvailabilityUpdate', 'pemicPriceUpdate', 
     *      'pemicUpdate', 'ikarUpdate', 'albatrosUpdate', 'informUpdate', 'partnertechnicUpdate',
     *      'internalStockUpdate', 'mrpStockUpdate'
     * 
     * @return string
     */
    public function admin_importProductsUpdate($importPid= null) {
        $Import = $this->loadModel('EshopProductImport', true);
        $progress = null;
        if ($this->data) {
            $progress = $Import->importUpdate($this->data['importPid'], array(
                'catalogueFile' => Sanitize::value($this->data['catalogueFile']),
                'useFullCatalogue' => Sanitize::value($this->data['useFullCatalogue'], false),
                'importAmount' => Sanitize::value($this->data['importAmount']),
//                'simulate' => true, //debug
//                'processAmount' => 10, //debug
            ));
            if (
                empty($progress)
                && ($processingErrors = $Import->getErrors('_processing'))
            ) {
                App::setErrorMessage(array_shift($processingErrors));
            }
        }
        if (empty($this->data) && !empty($importPid)) {
            $this->data['importPid'] = $importPid;
        }
        return $this->loadView('EshopImports/admin_importProducts', array(
            'data' => $this->data,
            'Model' => array(
                array($Import, $importPid),
            ),
            'importPid' => $importPid,
            'imports' => array(
                'albatrosUpdate' => __a(__FILE__, 'Albatros aktualizácia'),
                'internalStockUpdate' => __a(__FILE__, 'Aktualizácia skladových stavov (CSV)'),
                'mrpStockUpdate' => __a(__FILE__, 'Aktualizácia skladových stavov (MRP)'),
            ),   
            'progress' => $progress,
        ));
    }
    
    /**
     * Imports product fields update automatically for specified import name
     * 
     * @param string $importPid One of 'pemicAvailabilityUpdate', 'pemicPriceUpdate', 
     *      'pemicUpdate', 'ikarUpdate', 'ikarDescriptionUpdate', 'albatrosUpdate', 'informUpdate', 'partnertechnicUpdate',
     *      'mrpStockUpdate'
     * @param integer $importAmount Amount of products to be updated. If 0 then all in catalogue are updated.
     *      Defaults to 0.
     * @param bool $useFullCatalogue Applied only for 'pemicAvailabilityUpdate'
     *      If 1 then a catalogue_full file is downloaded. Defaults to 0. 
     * @param integer $recordOffset Amount of records to be skipped in catalogue. 
     *      Only records over this offset are considered for update. If 0 then all 
     *      records in catalogue are considered. Defaults to 0.
     * 
     * @return string Progress array dump
     */
    public function autoimportProductsUpdate($importPid = null, $importAmount = 0, $useFullCatalogue = 0, $recordOffset = 0) {
        App::setLayout('App', 'admin');
        if (empty($importPid)) {
            return;
        }
        $Import = $this->loadModel('EshopProductImport', true);
        $progress = $Import->importUpdate($importPid, array(
            'importAmount' => $importAmount,
            'useFullCatalogue' => $useFullCatalogue,
            'recordOffset' => $recordOffset,
//            'catalogueFile' => '/userfiles/files/import/albatros.xml', //debug
//            'simulate' => true, //debug
//            'processAmount' => 10, //debug
        ));
        if ($progress) {
            $progress['errors'] = array_merge($progress['errors'], $Import->getErrors());
        }
        else {
            $progress['errors'] = $Import->getErrors();
        }
        return '<pre>' . print_r($progress, true) . '</pre>';
    }
    
    /**
     * CRON or launched as async request at the end of update import 
     */
    public function autosynchronizeSupplierProducts($updatedOnly = true) {
        App::setLayout('App', 'admin');
        $this->loadModel(EshopSupplierProduct::class);
        $SupplierProduct = new EshopSupplierProduct();
        $progress = $SupplierProduct->synchronize(array(
            'updatedOnly' => $updatedOnly,
        ));
        return '<pre>' . print_r($progress, true) . '</pre>';
    }
    
    /**
     * Imports specified $field value from its source stored in supplier source field
     * (EshopSupplierProduct.{$field}_import_source)
     * 
     * @param string $field Field name to import from its source. One of 'image', 'description'
     * @param integer $processAmount Amount of products to be processed. If 0 then all 
     *          possible are processed. Defaults to 0;
     * @param bool $reimportFailed Optional. All sources which has been already imported
     *          keeps track of their import result (OK/NOK) to avoid repetitive imports
     *          if sources wich are invalid/broken. But if (after some time) you
     *          would like to try to reimport them you have this possibility by 
     *          setting this TRUE (1). Defaults to FALSE.
     * 
     * @return string Progress array dump
     */
    public function autoimportProductFieldsFromSource($field = null, $processAmount = 0, $reimportFailed = false) {
        App::setLayout('App', 'admin');
        if (empty($field)) {
            return;
        }
        $Import = $this->loadModel('EshopProductImport', true);
        $progress = $Import->importFromSource($field, array(
            'processAmount' => $processAmount,
            'reimportFailed' => $reimportFailed,
//            'importAmount' => 3, //debug
        ));
        if ($progress) {
            $progress['errors'] = array_merge($progress['errors'], $Import->getErrors());
        }
        else {
            $progress['errors'] = $Import->getErrors();
        }
        return '<pre>' . print_r($progress, true) . '</pre>';        
    }
    
    /**
     * Downloads specified import file from supplier source
     * 
     * @param string $filePid Available are 'albatrosCatalogue'
     * @param boolean $launchDownload If TRUE then downoad is launched. Defaults to FALSE.
     */
    public function admin_downloadImportFile($filePid = null, $launchDownload = false)  {
        $this->loadModel('EshopProductImport');
        $Import = new EshopProductImport();
        $downloadUrl = null;
        if ($this->data) {
            if ($this->data['filePid']) {
                $downloadUrl = App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => $this->action,
                    'args' => array(
                        $this->data['filePid'],
                        1
                    )
                ));
            }
            else {
                App::setErrorMessage(__a(__FILE__, 'Vyberte prosím súbor'));
            }
        }
        if ($launchDownload) {
            $result = $Import->downloadImportFile($filePid);
            if (
                empty($result)
                && ($processingErrors = $Import->getErrors('_processing'))
            ) {
                App::setErrorMessage(array_shift($processingErrors));
            }
        }
        if (empty($this->data) && !empty($filePid)) {
            $this->data['filePid'] = $filePid;
        }
        return $this->loadView('EshopImports/admin_downloadImportFile', array(
            'data' => $this->data,
            'Model' => array(
                array($Import, $filePid),
            ),
            'filePid' => $filePid,
            'downloadUrl' => $downloadUrl,
            'files' => array(
                'albatrosCatalogue' => __a(__FILE__, 'Albatros katalóg'),
                'albatrosStock' => __a(__FILE__, 'Albatros sklad'),
//                'partnerTechnicCatalogue' => __a(__FILE__, 'PartnerTechnic katalóg'),
//                'ikarCatalogue' => __a(__FILE__, 'Ikar katalóg'),
//                'ikarAnnotation' => __a(__FILE__, 'Ikar anotácie'),
//                'ikarStock' => __a(__FILE__, 'Ikar sklad'),
            ),   
        ));
    }
    
    public function admin_importProductsPemicDescriptionUpdate() {
        $Import = $this->loadModel('EshopProductImport', true);
        $progress = null;
        if ($this->data) {
            $updateProgress = $Import->importUpdate('pemicUpdate', array(
                'useFullCatalogue' => Sanitize::value($this->data['useFullCatalogue'], false),
//                'simulate' => true, //debug
//                'processAmount' => 10, //debug
            ));
            if (
                empty($updateProgress)
                && $Import->getErrors('_processing')
            ) {
                App::setErrorMessage(array_shift($Import->getErrors('_processing')));
            }
            
            $descriptionProgress = $Import->importFromSource('description', array(
                'reimportFailed' => true,
    //            'importAmount' => 3, //debug
            ));
            if ($descriptionProgress) {
                $descriptionProgress['errors'] = array_merge($descriptionProgress['errors'], $Import->getErrors());
            }
            else {
                $descriptionProgress['errors'] = $Import->getErrors();
            }
            $progress = array(
                'pemicUpdateProgress' => $updateProgress,
                'descriptionDownloadProgress' => $descriptionProgress,
            );
        }
        return $this->loadView('EshopImports/admin_importProducts', array(
            'data' => $this->data,
            'Model' => array(
                array($Import, 'pemicUpdate'),
            ),
            'importPid' => 'pemicUpdate',
            'imports' => array(
                'pemicUpdate' => __a(__FILE__, 'Pemic aktualizácia anotácií'),
            ),   
            'progress' => $progress,
        ));
    }
}
