<?php

class EshopVouchers extends SmartController {
    
    protected $model = 'EshopVoucher';
    
    /**
     * Allow the model methods hinting in IDE
     * @var EshopVoucher
     */
    protected $Model;
    
    public function admin_index() {
        $this->viewOptions['columns'] = array(
            'code' => __a(__FILE__, 'Voucher code'),
            'discount_rate' => __a(__FILE__, 'Discount rate'),
            //'special_discount_rate' => __a(__FILE__, 'Special discount rate'),
            'discount' => __a(__FILE__, 'Absolútna zľava'),
            'active_from' => __a(__FILE__, 'Active from'),
            'active_to' => __a(__FILE__, 'Active to'),
            'applications_limit' => __a(__FILE__, 'Usages limit'),
            'applications_count' => __a(__FILE__, 'Usages count'),
            'active' => __a(__FILE__, 'Active'),
        );
        $this->viewOptions['title'] = __a(__FILE__, 'Vouchers');
        $this->viewOptions['actions'] = array(
            '*',
            'import' => App::getUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => 'admin_importFromXls',
            )),
        );
        $this->seoTitle = __a(__FILE__, 'Vouchers');
        return parent::admin_index();      
    }
    
    public function admin_add() {
        // get lang of translated fields (if any)
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        // use custom save method in following way
        if ($this->data) {
            $this->saveResult = $this->Model->saveAll($this->data, array(
                'lang' => $lang,
            ));
            // possible redirection on success can be done here. If not
            // then it is done by parent::admin_edit()
        }
        $this->viewOptions['fields'] = array(
            array('row'),
                array(
                    'field' => 'code',
                    'label' => __a(__FILE__, 'Voucher code'),
                    'value' => $this->Model->getUniqueCode(),
                ),
                array(
                    'field' => 'discount_rate',
                    'label' => __a(__FILE__, 'Discount rate'),
                    'hint' => __a(__FILE__, 'E.g. <code>10</code> for discount rate 10%'),
                ),
                /*/
                array(
                    'field' => 'special_discount_rate',
                    'label' => __a(__FILE__, 'Special discount rate'),
                    'hint' => __a(__FILE__, 'Applies to products of Vydavateľstvo Matice slovenskej'), //HARDCODED
                ),
                /*/
                array(
                    'field' => 'discount',
                    'label' => __a(__FILE__, 'Absolútna zľava'),
                    'hint' => __a(__FILE__, 'Absolútnu zľavu je možné používať až po zadaní id produktu "Zľavový kód" v Admin > Eshop > Nastavenia > Základné nastavenia > Id produktu "Zľavový kód"'),
                ) + (!$this->getSetting('EshopProduct.voucherProductId') ? array('disabled' => true) : array()),
            array('/row'),
            array('row'),
                array(
                    'field' => 'active_from',
                    'type' => 'date',
                    'label' => __a(__FILE__, 'Active from'),
                ),
                array(
                    'field' => 'active_to',
                    'type' => 'date',
                    'label' => __a(__FILE__, 'Active to'),
                ),
                array(
                    'field' => 'applications_limit',
                    'label' => __a(__FILE__, 'Usages limit'),
                    'hint' => __a(__FILE__, 'Max allowed number of voucher usages/applications. When this limit is fulfilled then the voucher gets inactive. If empty then there is no limit')
                ),
                array(
                    'field' => 'active',
                    'label' => __a(__FILE__, 'Active'),
                ),
            array('/row'),
            array('row', 'columns' => 2),
                array(
                    'field' => 'category_ids',
                    'label' => __a(__FILE__, 'Kategórie'),
                    'hint' => __a(__FILE__, 'Zľavový kód bude platný len pre produkty zvolených kategórií'),
                    'type' => 'itemselector', 
                    'options' => '/mvc/Eshop/EshopProductCategories/admin_listForItemSelector',
                ),
                array(
                    'field' => 'product_ids',
                    'label' => __a(__FILE__, 'Produkty'), 
                    'hint' => __a(__FILE__, 'Zľavový kód bude platný len pre zvolené produkty'),
                    'type' => 'itemselector', 
                    'options' => '/mvc/Eshop/EshopProducts/admin_list',
                ),
            array('/row'),
        );
        $this->viewOptions['title'] = __a(__FILE__, 'New voucher');
        $this->seoTitle = __a(__FILE__, 'New voucher');
        return parent::admin_add();      
    }
    
    public function admin_edit($id = null) {
        // get lang of translated fields (if any)
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        // use custom save method in following way
        if ($this->data) {
            $this->saveResult = $this->Model->saveAll($this->data, array(
                'lang' => $lang,
            ));
            // possible redirection on success can be done here. If not
            // then it is done by parent::admin_edit()
        }
        // use custom find method in following way
        else {
            $this->viewOptions['data'] = $this->Model->findAll($id, array(
                'lang' => $lang,
            ));
            if (empty($this->viewOptions['data'])) {
                App::setErrorMessage(__a(__FILE__, 'Invalid record id %s', (string)$id));
                return App::loadScreen('_error');
            }            
        }
        
        $this->viewOptions['fields'] = array(
            array(
                'field' => 'id',
                'type' => 'hidden',
            ),
            array('row'),
                array(
                    'field' => 'code',
                    'label' => __a(__FILE__, 'Voucher code'),
                ),
                array(
                    'field' => 'discount_rate',
                    'label' => __a(__FILE__, 'Discount rate'),
                    'hint' => __a(__FILE__, 'E.g. <code>10</code> for discount rate 10%'),
                ),
                /*/
                array(
                    'field' => 'special_discount_rate',
                    'label' => __a(__FILE__, 'Special discount rate'),
                    'hint' => __a(__FILE__, 'Applies to products of Vydavateľstvo Matice slovenskej'), //HARDCODED
                ),
                /*/
                array(
                    'field' => 'discount',
                    'label' => __a(__FILE__, 'Absolútna zľava'),
                    'hint' => __a(__FILE__, 'Absolútnu zľavu je možné používať až po zadaní id produktu "Zľavový kód" v Admin > Eshop > Nastavenia > Základné nastavenia > Id produktu "Zľavový kód"'),
                ) + (!$this->getSetting('EshopProduct.voucherProductId') ? array('disabled' => true) : array()),
                array(
                    'field' => 'printed',
                    'label' => __a(__FILE__, 'Vytlačený'),
                    'hint' => __a(__FILE__, 'Bol už zľavový kód vytlačený? Nastavuje sa ručne a ide len o informatívny checkbox.')
                ),
            array('/row'),
            array('row'),
                array(
                    'field' => 'active_from',
                    'type' => 'date',
                    'label' => __a(__FILE__, 'Active from'),
                ),
                array(
                    'field' => 'active_to',
                    'type' => 'date',
                    'label' => __a(__FILE__, 'Active to'),
                ),
                array(
                    'field' => 'applications_limit',
                    'label' => __a(__FILE__, 'Usages limit'),
                    'hint' => __a(__FILE__, 'Max allowed number of voucher usages/applications. When this limit is fulfilled then the voucher gets inactive. If empty then there is no limit')
                ),
                array(
                    'field' => 'active',
                    'label' => __a(__FILE__, 'Active'),
                ),
            array('/row'),
            array('row', 'columns' => 2),
                array(
                    'field' => 'category_ids',
                    'label' => __a(__FILE__, 'Kategórie'),
                    'hint' => __a(__FILE__, 'Zľavový kód bude platný len pre produkty zvolených kategórií'),
                    'type' => 'itemselector', 
                    'options' => '/mvc/Eshop/EshopProductCategories/admin_listForItemSelector',
                ),
                array(
                    'field' => 'product_ids',
                    'label' => __a(__FILE__, 'Produkty'), 
                    'hint' => __a(__FILE__, 'Zľavový kód bude platný len pre zvolené produkty'),
                    'type' => 'itemselector', 
                    'options' => '/mvc/Eshop/EshopProducts/admin_list',
                ),
            array('/row'),
        );
        $this->viewOptions['title'] = __a(__FILE__, 'Edit voucher "%s"');
        $this->viewOptions['actions'] = array(
            'print' => array(
                'url' => array(
                    'locator' => '/mvc/Eshop/EshopVouchers/admin_print',
                    'args' => array($id),
                ),
                'icon' => '<i class="fa fa-print"></i>',
                'target' => '_blank',
                'title' => __a(__FILE__, 'Print'),
                'label' => __a(__FILE__, 'Print'),
            ),
        );
        return parent::admin_edit($id);      
    }
    
    public function admin_importFromXls() {
        $this->loadModel('EshopVoucher');
        $Voucher = new EshopVoucher();
        $reportHtml = '';
        if ($this->data) {
            if ($Voucher->importFromXls($this->data, $report)) {
                App::setSuccessMessage(__a(__FILE__, '%s vouchers has been succesfully imported', count($report)));
                //App::redirect(App::$url);
            }
            else {
                App::setErrorMessage(__a(__FILE__, 'Please, check errors'));
            }
            $reportHtml = $this->loadView('EshopVouchers/admin_importFromXls_ReportHtml', $report);
        }
        App::setSeoTitle(__a(__FILE__, 'Import from .xls'));
        return Html::smartForm(array(
            'data' => $this->data,
            'Model' => $Voucher,
            'title' => __a(__FILE__, 'Import from .xls'),
            'columns' => 4,
            'fields' => array(
                array('h1' => __a(__FILE__, 'Import inputs')),
                array('row'),
                    array(
                        'field' => 'import_file',
                        'label' => __a(__FILE__, 'Import file (.xls)'),
                        'hint' => __a(__FILE__, 'Choose a .xls file with following columns: <code>code</code>, <code>dicount %</code>, <code>usages limit</code>'),
                        'type' => 'file',
                    ),
                    array(
                        'field' => 'update_on_duplicit',
                        'label' => __a(__FILE__, 'Update existing vouchers'),
                        'hint' => __a(__FILE__, 'If checked then existing voucher codes are updated. Otherwise they are ignored/skipped'),
                        'type' => 'checkbox',
                    ),
                array('/row'),
                array('if' => $reportHtml),
                    array('h1' => __a(__FILE__, 'Import report')),
                    array('row', 'columns' => 1),
                    array('col'),
                        array(
                            'html' => $reportHtml
                        ),
                    array('/col'),
                    array('/row'),
                array('endif'),
            ),
            'buttonLabel' => __a(__FILE__, 'Import'),
            'saveAndCloseButtonLabel' => __a(__FILE__, 'Import & Close'),
        ));
    }
    
    public function admin_print($id = null) {
        if (!$id) {
            App::setMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/',
                'source' => App::$requestSource,
            ))));
        }
                
        $voucher = $this->Model->findFirst(array(
            'conditions' => array(
                'id' => $id
            )
        ));
        
        App::setSeoTitle(__a(__FILE__, 'Print "%s"', $voucher['code']));
        return $this->loadView('EshopVouchers/admin_print', array(
            'voucher' => $voucher,
        ));        
    }
        
    /**
     * Verifies a voucher existence, activity (validity) and value (discount)
     */
    public function verify() {
        $this->loadModel('EshopVoucher');
        $Voucher = new EshopVoucher();
        $result = array();
        if (
            $this->data
            && ($result = $Voucher->verify($this->data))
        ) {
            // clear the provided code once it is verified and wait for a new one
            $this->data['verified_code'] = '';
        }
        return $this->loadView('EshopVouchers/verify', array(
            'data' => $this->data,
            'errors' => $Voucher->getErrors(),
            'required' => $Voucher->getRequiredFields(array(
                'alternative' => 'verify',
            )),
            'result' => $result,
        ));
    }
}