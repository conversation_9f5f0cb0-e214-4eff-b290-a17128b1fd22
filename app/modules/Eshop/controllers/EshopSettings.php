<?php
class EshopSettings extends Controller {
          
    /**
     * Creates and saves html form to edit settings
     * 
     * @return string
     */
    public function admin_edit() {   
        $this->displayOriginComment = true;
        $Setting = $this->loadModel('EshopSetting', true);
        
        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        // save settings
        if (!empty($this->data)) {
            if (
                $Setting->updateByName($this->data, array(
                    'lang' => $lang,
                ))
            ) {
                App::setSuccessMessage(__a(__FILE__, 'Settings has been succesfully updated'));
                App::redirect(App::$url);
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
        }
        // load settings
        else {
            $this->data = $Setting->findListWithNameKeys(array(
                'lang' => $lang,
            ));
        }
        
        $Country = App::loadModel('App', 'Country', true);
        $countries = $Country->findList(array(
            'key' => 'Country.iso_code_2',
            'fields' => 'Country.name',
            'conditions' => array(
                'Country.iso_code_2' => array('SK', 'CZ', 'PL', 'AT', 'UA'),
            ),
            'order' => array(
                'Country.iso_code_2' => array(
                    'SK', 
                    'CZ', 
                    'AT', 
                    'PL', 
                    'UA',
                ),
            )
        ));
                
        // render view
        App::setSeoTitle(__a(__FILE__, 'E-shop settings'));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'E-shop module settings'),
            'data' => $this->data,
            'Model' => $Setting,
            'columns' => 4,
            'actions' => array(
                'lang' => true
            ),
            'showAffix' => true,
            'fields' => array(
                // BASIC SETTINGS
                array('h1' => __a(__FILE__, 'Basic settings')),
                array('row'),
                    'Eshop/EshopProduct/pagingLimit' => array(
                        'label' => __a(__FILE__, 'Paging limit')
                    ),
                    'Eshop/defaultTaxRate' => array(
                        'label' => __a(__FILE__, 'Default VAT rate (%)'),
                    ),
                    'Eshop/pricesAreTaxed' => array(
                        'label' => __a(__FILE__, 'Prices are entered with VAT'),
                        'type' => 'checkbox',
                    ),
                    'Eshop/EshopOrder/allowQuickOrder' => array(
                        'label' => __a(__FILE__, 'Order without registration'),
                        'type' => 'select',
                        'options' => array(
                            0 => __a(__FILE__, 'forbid'), 
                            1 => __a(__FILE__, 'allow'), 
                            'force' => __a(__FILE__, 'always')
                        )
                    ),
                array('/row'),
////not used till now (150707)                
//                array('row'),
//                    'Eshop/EshopOrder/advanceRates' => array(
//                        'label' => __a(__FILE__, 'Advance rates'),
//                        'hint' => __a(__FILE__, 'List of advance rates provided like: {orderTotalA}:{advanceRateA};{orderTotalB}:{advanceRateB}. E.g. 150:80;300:40')
//                    ),
//                array('/row'),
                array('row'),
                    'Eshop/EshopProduct/shipmentTimeOnStock' => array(
                        'label' => __a(__FILE__, 'Shipment time of the product which is on stock'),
                        'hint' => __a(__FILE__, 'Number of days to ship the order in case that all order products are on stock'),
                    ),
                    'Eshop/EshopProduct/shipmentTimeOffStock' => array(
                        'label' => __a(__FILE__, 'Shipment time of the product which is off stock'),
                        'hint' => __a(__FILE__, 'Number of days to ship the order in case that some of order products are not on stock'),
                    ),
                    'Eshop/EshopProduct/orderableToStockAmount' => array(
                        'label' => __a(__FILE__, 'Produkty sú objednateľné len do skladového množstva'),
                        'hint' => __a(__FILE__, 'Ak je zaškrtnuté, tak produkty je možné objednať len do množstva, ktoré je aktuálne dostupné na sklade.'),
                        'type' => 'checkbox',
                    ),
                    'Eshop/EshopShipment/freeShipmentProductsTotal' => array(
                        'label' => __a(__FILE__, 'Free shipment products total'),
                        'hint' => __a(__FILE__, 'Enter zero value if you don\'t want to apply this')
                    ),
                array('/row'), 
                array('row'),
                    'Eshop/EshopProduct/discountPriceDecimals' => array(
                        'label' => __a(__FILE__, 'Desatinné miesta zľavnenej ceny'),
                        'hint' => __a(__FILE__, 'Počet desatinných miest zľavnenej ceny. Ak sú ceny zadávané s DPH, tak sa uplatní len na zľavnené ceny s DPH. Ak sú ceny zadávané bez DPH, tak sa uplatní len na zľavnené ceny bez DPH. Pri zaokruhľovaní sa vždy zaokrúhľuje smerom nadol (aby klient dostal sľúbenú zľavu)'),
                    ),
                    /*/
                    'Eshop/EshopProduct/importedProductsMinMarkupRate' => array(
                        'label' => __a(__FILE__, 'Minimálna marža importovaných produktov (%)'),
                        'hint' => __a(__FILE__, 'Zľavy importovaných produktov sa nastavujú tak, aby výsledná marža neklesla pod túto hodnotu. Hodnotu je možné zadať aj osobitne pre jednotlivých dodávateľov, napr.: <code>12;slovart:5,albatros:5</code> znamená, že východzia hodnota je 12% no pre Slovart a Albatros je 5%. Dostupní sú nasledovní dodávatelia: <code>pemic</code>, <code>inform</code>, <code>partnertechnic</code>, <code>albatros</code>, <code>ikar</code>, <code>slovart</code>. Po zmene hodnoty sa aktualizujú zlavnené ceny všetkých produktov uvedených dodávateľov čo môže chvíľu trvať.'),
                    ),
                    /*/
                    'Eshop/EshopRelatedProduct/reciprocal' => array(
                        'label' => __a(__FILE__, 'Create related products reciprocally'),
                        'type' => 'checkbox',
                    ),
                    'Eshop/EshopProduct/indexCategoryOwnProducts' => array(
                        'label' => __a(__FILE__, 'Index only category own products'),
                        'hint' => __a(__FILE__, 'If checked then only products of given category are indexed. Otherwise also products of its subcategories are indexed.'),
                        'type' => 'checkbox',
                    ),
                    'Eshop/EshopProductCategory/sortProductsLimit' => array(
                        'label' => __a(__FILE__, 'Zoraďovať produkty v kategórii do počtu'),
                        'hint' => __a(__FILE__, 'Danej produktovej kategórii je možné zapnúť zoraďovanie produktov v nej zaradených len ak je ich počet menší ako tu zadaná hodnota. Ak je nastavené na 0, tak zoraďovanie produktov podľa zaradenia v kategórii je úplne vypnuté.'),
                    ),                    
                array('/row'),
                array('row'),
                    'Eshop/EshopProduct/additionalDiscountRateLimit' => array(
                        'label' => __a(__FILE__, 'Hraničné percento zľavy'),
                        'hint' => __a(__FILE__, 'Percento výslednej zľavy po uplatnení dodatočných zliav je ohraničené touto hodnotou. Ak je však primárna zľava produktu výššia ako toto percento tak sa ako hraničná hodnota použije percento primárnej zľavy.'),
                    ),
                    /*/
                    'Eshop/EshopProduct/additionalDiscountRateLimitForVydavatel' => array(
                        'label' => __a(__FILE__, 'Hraničné percento zľavy pre Vydavateľa'),
                        'hint' => __a(__FILE__, 'Percento výslednej zľavy po uplatnení dodatočných zliav je ohraničené touto hodnotou. Ak je však primárna zľava produktu výššia ako toto percento tak sa ako hraničná hodnota použije percento primárnej zľavy. Toto nastavenie platí len pre produkty Vydavateľa.'),
                    ),
                    /*/
                    'Eshop/EshopProduct/voucherProductId' => array(
                        'label' => __a(__FILE__, 'Id produktu "Zľavový kód"'),
                        'hint' => __a(__FILE__, 'Slúži na implementáciu zľavových kódov s absolútnou zľavou. Cena daného produktu sa nastaví až v košíku pri použití konkrétneho zľavového kódu takže môže byť ľubovolná. K názvu daného produktu sa v košíku prída konkrétny použitý zľavový kód, napr. "Zľavový kód ABC123".'),
                    ),
                array('/row'),
                array('row'),
                    'Eshop/EshopProduct/allowExternalSearch' => array(
                        'label' => __a(__FILE__, 'Vyhľadávanie produktov pomocou MeiliSearch'),
                        'hint' => __a(__FILE__, 'Ak je zaškrtnuté, tak produkty sa pre daný výraz vyhľadávajú cez MeiliSearch API. Ak nie je zaškrtnuté, tak sa používa starý spôsob vyhľadávania dopytom do DB.'),
                        'type' => 'checkbox',
                    ),
                    'App/meilisearch/apiUrlBase' => array(
                        'label' => __a(__FILE__, 'MeiliSearch API URL'),
                        'hint' => __a(__FILE__, 'Ak nie je zadané, tak sa použije východzie lokálne URL, t.j. <code>http://localhost:7700/</code>'),
                    ),
                    'App/meilisearch/apiKey' => array(
                        'label' => __a(__FILE__, 'MeiliSearch API kľúč'),
                        'hint' => __a(__FILE__, 'Kľúč na povolenie komunikácie s MeiliSearch. Musí isť o "Admin API Key", ktorý má práva nie len na vyhľadávanie ale aj na zápis údajov. Ak nie je zadané, tak sa predpokladá, že komunikácia s MeiliSearch API nie je chránená API klúčom.'),
                    ),
                array('/row'),
                
                // ADDRESS
                array('h1' => __a(__FILE__, 'Address')),
                array('row', 'columns' => 2),
                    'Eshop/address/companyFullname' => array(
                        'label' => __a(__FILE__, 'Company name'),
                    ),
                    'Eshop/address/fullname' => array(
                        'label' => __a(__FILE__, 'Person or place name'),
                    ),
                array('/row'), 
                array('row'),
                    'Eshop/address/street' => array(
                        'label' => __a(__FILE__, 'Street and house number/apartment'),
                    ),
                    'Eshop/address/city' => array(
                        'label' => __a(__FILE__, 'City'),
                    ),
                    'Eshop/address/zip' => array(
                        'label' => __a(__FILE__, 'ZIP'),
                    ),
                    'Eshop/address/country' => array(
                        'label' => __a(__FILE__, 'Country'),
                        'hint' => __a(__FILE__, 'If "Specific shipment for orders with delivery to abroad" is checked then orders with delivery to other countries are treated separately and their shipment method and price is set manualy'),
                        'type' => 'select',
                        'options' => $countries,
                    ),
                array('/row'), 
                array('row'), 
                    'Eshop/EshopShipment/specificAbroadDelivery' => array(
                        'label' => __a(__FILE__, 'Specific shipment for orders with delivery to abroad'),
                        'hint' => __a(__FILE__, 'Check in case that orders with delivery to abroad have to be treated separately and their shipment method and price will be set manually by operator'),
                        'type' => 'checkbox',
                    ),
                array('/row'), 
                

                // PAYMENT METHODS 
                array(
                    'h1' => __a(__FILE__, 'Online platobné metódy'), 
                    'hint' => __a(__FILE__, 'Parametre použité pre všetky dostupné online platobné metódy')
                ),
////these are used only in PaymentMethod::getPrices() and ::getActualPrice() and till now (150707) has not been used                
//                array('row'),
//                    'Payment/defaultTaxRate' => array(
//                        'label' => __a(__FILE__, 'Default VAT rate (%)')
//                    ),
//                    'Payment/pricesAreTaxed' => array(
//                        'label' => __a(__FILE__, 'Prices are entered with VAT'),
//                        'type' => 'checkbox'
//                    ),
//                array('/row'),
                array('row'),
                'Payment/constantSymbol' => array(
                    'label' => __a(__FILE__, 'Constant symbol')
                ),
                'Payment/specificSymbol' => array(
                    'label' => __a(__FILE__, 'Specific symbol')
                ),
                'Payment/paymentDescription' => array(
                    'label' => __a(__FILE__, 'Payment description')
                ),
                array('/row'),
                array('row'),
                    'Payment/returnEmail' => array(
                        'label' => __a(__FILE__, 'Return e-mail address'),
                        'hint' => __a(__FILE__, 'E-mail address to which the bank sends reports with results of payments'),
                    ),
                    'Payment/returnSMS' => array(
                        'label' => __a(__FILE__, 'Return SMS phone number'),
                        'hint' => __a(__FILE__, 'Phone number to which the bank sends SMS reports with results of payments'),
                    ),
                array('/row'),

                // INFO TEXTS
                array('h1' => __a(__FILE__, 'Info texts')),
                array('row'),
                    'Eshop/EshopOrder/bankAccount' => array(
                        'label' => __a(__FILE__, 'Účet na platby bankovým prevodom'),
                        'hint' => __a(__FILE__, 'Bank account number displayed as info (checkout, order e-mail) for bank transfer payments'),
                    ),
                    'Eshop/EshopOrder/bankTransferConstantSymbol' => array(
                        'label' => __a(__FILE__, 'Bank transfer constant symbol'),
                        'hint' => __a(__FILE__, 'Constant symbol displayed as info (checkout, order e-mail) for bank transfer payments'),
                    ),
                array('/row'),
                array('row', 'columns' => 2),
                    'Eshop/EshopOrder/openingHours' => array(
                        'label' => __a(__FILE__, 'Opening hours'),
                        'hint' => __a(__FILE__, 'Displayed e.g. in checkout when personal pickup is choosen'),
                        'type' => 'editor',
                        'options' => array(
                        ),
                    ),
                array('/row'),

                // TERMS AND CONDITIONS
                array(
                    'h1' => __a(__FILE__, 'Terms and conditions'), 
                    'hint' => __a(__FILE__, 'Specification of file attached to new order e-mail'),
                ),                           
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/termsAndConditionsFile' => array(
                        'label' => __a(__FILE__, 'Terms and conditions file name')
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/termsAndConditions' => array(
                        'label' => __a(__FILE__, 'Terms and conditions file content'),
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Text'
                        )
                    ),
                array('/row'),

                // CONTRACT WITHDRAWAL
                array(
                    'h1' => __a(__FILE__, 'Contract withdrawal'),
                    'hint' => __a(__FILE__, 'Specification of file attached to new order e-mail'),
                ),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/contractWithdrawalFile' => array(
                        'label' => __a(__FILE__, 'Contract withdrawal file name')
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/contractWithdrawal' => array(
                        'label' => __a(__FILE__, 'Contract withdrawal file content'),
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Text'
                        )
                    ),
                array('/row'),

                // EMAILS
                array(
                    'h1' => __a(__FILE__, 'Order e-mails'),
                    'hint' => __a(__FILE__, 'E-maily odosielané klientovi pri vytvorení novej objednávky alebo pri zmene stavu objednávky. Správa sa odošle len v prípade, že predmet aj text e-mailu je vyplnený. V textoch môžu byť použité nasledovné vsuvky: <ul><li><code>:userName:</code> - meno a priezvisko užívateľa,</li><li><code>:userFirstName:</code> - krstné meno užívateľa,</li><li><code>:userEmail:</code> - e-mail užívateľa,</li><li><code>:userPhone:</code> - telefónne číslo užívateľa,</li><li><code>:userComment:</code> - komentár k objednávke,</li><li><code>:userAddress:</code> - adresa užívateľa,</li><li><code>:deliveryAddress:</code> - adresa doručenia,</li><li><code>:deliveryPhone:</code> - telefónne číslo doručenia,</li><li><code>:companyAddress:</code> - adresa firmy,</li><li><code>:invoicingAddress:</code> - fakturačná adresa,</li><li><code>:orderNumber:</code> - číslo objednávky,</li><li><code>:orderCreated:</code> - dátum a čas vytvorenia objednávky,</li><li><code>:orderProducts:</code> - tabuľka produktov objednávky obsahujúca množstvá a ceny,</li><li><code>:orderTotal:</code> - celková cena objednávky,</li><li><code>:orderTotalInfo:</code> - celková cena objednávky, v prípade špeciálnej objednávky obsahuje aj upozornenie</li><li><code>:orderStatus:</code> - stav objednávky,</li><li><code>:shipmentCost:</code> - cena poštovného,</li><li><code>:shipmentCost:</code> - cena poštovného, v prípade špeciálnej objednávky obsahuje aj upozornenie</li><li><code>:shipmentMethod:</code> - názov spôsobu doručenia,</li><li><code>:paymentMethod:</code> - názov platobnej metódy,</li><li><code>:termsAndConditionsAgreement:</code> - súhlas s obchodnými podmienkami (áno/nie),</li><li><code>:prolongedDeliveryTimeAgreement:</code> - súhlas s predĺženým časom dodania,</li><li><code>:bankAccount:</code> - číslo účtu bankového prevodu uvedené v nastaveniach E-shopu,</li><li><code>:variableSymbol:</code> - variabilný symbol (zohduje sa s číslom objednávky),</li><li><code>:constantSymbol:</code> -  konštantný symbol bankového prevodu uvedené v nastaveniach E-shopu,</li><li><code>:paymentUrl:</code> - URL na vykonanie platby (môže sa použiť na vytvorenie platobného linku),</li><li><code>:paymentLink:</code> - HTML platobného linku,</li><li><code>:ordersUrl:</code> - URL zoznamu objednávok užívateľa,</li><li><code>:companyInfo:</code> - informácia o spoločnosti na ktorú bola objednávka vytvorená,</li><li><code>:eshopName:</code> - názov E-shopu uvedený ako meno webu v systémových nastaveniach,</li><li><code>:eshopUrl:</code> - E-shop URL</li><li><code>:specificItems:</code> - zoznam položiek, ktoré majú byť vyriešené/upresnené v prípade špeciálnej objednávky,</li><li><code>:specificInfo:</code> - upozornenie v prípade špeciálnej objednávky</li><li><code>:voucherCode:</code> - zľavový kód objednávky</li><li><code>:bonusPoints:</code> - aktuálny stav bónusových bodov (číslo)</li><li><code>:bonusPointsSummary:</code> - aktuálny stav bónusových bodov (veta)</li><li><code>:electronicGiftCardInfo:</code> - info o obdržaní a použití elektronickej darovacej karty</li></ul>'),
                    // <li><code>:orderAdvanceRate:</code> - order advance rate,</li><li><code>:orderAdvancePrice:</code> - order advance price,</li>
                ),
                array('row'),
                    'Eshop/email/from' => array(
                        'label' => __a(__FILE__, 'Sender of E-shop order e-mails')
                    ),
                    'Eshop/email/cc' => array(
                        'label' => __a(__FILE__, 'Kópia e-mailov objednávky E-shopu'),
                        'hint' => __a(__FILE__, 'Adresa príjemncu kópie e-mailu objednávok'),
                    ),
                    'Eshop/email/bcc' => array(
                        'label' => __a(__FILE__, 'Skrytá kópia e-mailov objednávky E-shopu'),
                        'hint' => __a(__FILE__, 'Adresa príjemncu skrytej kópie e-mailu objednávok alebo čiarkou oddelený zoznam adries'),
                    ),
                array('/row'),
                array(
                    'h2' => __a(__FILE__, 'New order'),
                    'hint' => __a(__FILE__, 'E-mail sent to client on new order creation'),
                ),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgSubjectNewOrder' => array(
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgBodyNewOrder' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),
                array('/row'),
                array(
                    'h2' => __a(__FILE__, 'New specific order'),
                    'hint' => __a(__FILE__, 'E-mail sent to client on new specific order creation, e.g. with delivery to abroad. If not provided then default new order e-mail is used'),
                ),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgSubjectNewSpecificOrder' => array(
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgBodyNewSpecificOrder' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),
                array('/row'),
                array('h2' => __a(__FILE__, 'Status change to') . ' ' . __a(__FILE__, 'enum_opened_order')),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgSubjectStatusChange/enum_opened_order' => array(
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgBodyStatusChange/enum_opened_order' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),
                array('/row'),
                array('h2' => __a(__FILE__, 'Status change to') . ' ' . __a(__FILE__, 'enum_suspended_order')),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgSubjectStatusChange/enum_suspended_order' => array(
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgBodyStatusChange/enum_suspended_order' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),
                array('/row'),
                array(
                    'h2' => __a(__FILE__, 'Status change to') . ' ' . __a(__FILE__, 'enum_pickup_order'),
                    'hint' => __a(__FILE__, 'Status "%s" is used for orders prepared for client personal pickup', __a(__FILE__, 'enum_pickup_order')),
                ),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgSubjectStatusChange/enum_pickup_order' => array(
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgBodyStatusChange/enum_pickup_order' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),
                array('/row'),
                array('h2' => __a(__FILE__, 'Status change to') . ' ' . __a(__FILE__, 'enum_shipped_order')),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgSubjectStatusChange/enum_shipped_order' => array(
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgBodyStatusChange/enum_shipped_order' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),
                array('/row'),
//                array('h2' => __a(__FILE__, 'Status change to') . ' ' . __a(__FILE__, 'enum_closed_order')),
//                array('row', 'columns' => 1),
//                    'Eshop/EshopOrder/msgSubjectStatusChange/enum_closed_order' => array(
//                    ),
//                array('/row'),
//                array('row', 'columns' => 1),
//                    'Eshop/EshopOrder/msgBodyStatusChange/enum_closed_order' => array(
//                        'type' => 'editor',
//                        'options' => array(
//                            'toolbar' => 'Email'
//                        )
//                    ),
//                array('/row'),
                array('h2' => __a(__FILE__, 'Status change to') . ' ' . __a(__FILE__, 'enum_canceled_order')),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgSubjectStatusChange/enum_canceled_order' => array(
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgBodyStatusChange/enum_canceled_order' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),
                array('/row'),
                
                array(
                    'h1' => __a(__FILE__, 'Payment e-mails'),
                    'hint' => __a(__FILE__, 'E-mails sent client after payment is done, informing about the result of payment. Message is sent only in case that both e-mail subject and body are set. There can be used the same inserts as in case of order e-mails'),
                    // <li><code>:orderAdvanceRate:</code> - order advance rate,</li><li><code>:orderAdvancePrice:</code> - order advance price,</li>
                ),
                array('h2' => __a(__FILE__, 'Successfull payment')),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgSubjectPaymentSuccessful' => array(
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgBodyPaymentSuccessful' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),
                array('/row'),
                array('h2' => __a(__FILE__, 'Payment in processing')),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgSubjectPaymentInProcessing' => array(
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgBodyPaymentInProcessing' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),
                array('/row'),
                array('h2' => __a(__FILE__, 'Failed payment')),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgSubjectPaymentFailed' => array(
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgBodyPaymentFailed' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),
                array('/row'),

                // Smart SMS
                array(
                    'h1' => __a(__FILE__, 'Order SMS messages'),
                    'hint' => __a(__FILE__, 'SMS sent to client on order shipment or if the order is prepared for pickup. It is sent without diacritic signs and is automatically prepended by website name, e.g.: "Mysite.com: My message to client".'),
                ),
                array('row', 'columns' => 2),
                    'Eshop/EshopOrder/smartsmsMessageA' => array(
                        'label' => __a(__FILE__, 'Message (cash)'),
                        'type' => 'textarea',
                    ),
                    'Eshop/EshopOrder/smartsmsMessageB' => array(
                        'label' => __a(__FILE__, 'Message (dispatch)'),
                        'type' => 'textarea',
                    ),
                array('/row'),
                
                // Gift cards settings
                array('h1' => __a(__FILE__, 'Darovacie karty'), 'columns' => 4),
                array('row'),
                    'Eshop/EshopOrderProduct/giftCardExpirationDays' => array(
                        'label' => __a(__FILE__, 'Dĺžka platnosti'),
                        'hint' => __a(__FILE__, 'Dĺžka platnosti darovacej karty zadaná v dňoch. Počíta sa od dátumu vygenerovania zľavového kódu pre danú kartu. T.j. v prípade tlačenej karty je to od dátumu prijatia objednávky a v prípade elektronickej karty od dátumu zaplatenia objednávky.')
                    ),
                    'Eshop/EshopOrderProduct/giftCardExpiringEmailDays' => array(
                        'label' => __a(__FILE__, 'Čas odoslania e-mailu expirácie'),
                        'hint' => __a(__FILE__, 'Počet dni pred koncom platnosti darovacej karty, kedy sa má odoslať upozorňujúci e-mail expirácie darovacej karty')
                    ),
                array('/row'),
                array(
                    'h2' => __a(__FILE__, 'E-mail elektronickej darovacej karty'),
                    'hint' => __a(__FILE__, 'E-mail slúžiaci na odoslanie kódu zakúpenej elektronickej darovacej karty. V textoch môžu byť použité tie isté vsuvky ako v e-mailoch objednávky plus nasledovné: <ul><li><code>:giftCardName:</code> - aktuálny názov zakúpeného produktu "Darovacia karta"</li><li><code>:giftCardCode:</code> - zľavový kód zakúpenej darovacej karty</li><li><code>:giftCardDiscount:</code> - hodnota zakúpenej darovacej karty</li><li><code>:giftCardActiveTo:</code> - dátum konca platnosti zakúpenej darovacej karty</li></ul>')
                ),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrderProduct/msgSubjectGiftCardVoucher' => array(
                    ),                    
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrderProduct/msgBodyGiftCardVoucher' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),                    
                array('/row'),
                array(
                    'h2' => __a(__FILE__, 'E-mail expirácie darovacej karty'),
                    'hint' => __a(__FILE__, 'E-mail upozorňujúci na blížiaci sa koniec platnosti darovacej karty. V textoch môžu byť použité tie isté vsuvky ako v e-mailoch objednávky plus nasledovné: <ul><li><code>:giftCardName:</code> - aktuálny názov zakúpeného produktu "Darovacia karta"</li><li><code>:giftCardCode:</code> - zľavový kód zakúpenej darovacej karty</li><li><code>:giftCardDiscount:</code> - hodnota zakúpenej darovacej karty (t.j. jej zľavového kódu)</li><li><code>:giftCardActiveTo:</code> - dátum konca platnosti zakúpenej darovacej karty (t.j. jej zľavového kódu)</li></ul>')
                ),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrderProduct/msgSubjectExpiringGiftCardVoucher' => array(
                    ),                    
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrderProduct/msgBodyExpiringGiftCardVoucher' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),                    
                array('/row'),
                
                // Vouchers settings
                array('h1' => __a(__FILE__, 'Zľavové kódy'), 'columns' => 4),
                array('row', 'columns' => 1),
                    'Eshop/EshopVouchers/admin_print/template' => array(
                        'label' => __a(__FILE__, 'Šablóna tlače'),
                        'hint' => __a(__FILE__, 'Šablóna použitá pri tlači zľavového kódu. Sú v nej dostupné nasledovné vsuvky: <ul><li><code>:code:</code> - zľavový kód</li><li><code>:discount:</code> - hodnota zľavového kódu</li><li><code>:activeTo:</code> - dátum konca platnosti zľavového kódu</li></ul> POZOR: Vzhľadom k nastavenému hornému odsadeniu je zrejme potrebné použiť scroll alebo tlačidlo <kbd>Maximize</kbd>, aby sa text šablóny zobrazil!'),
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Empty'
                        ),
                    ),
                array('/row'),
                
                // Other settings
                array('h1' => __a(__FILE__, 'Other settings'), 'columns' => 4),
                array(
                    'h2' => __a(__FILE__, 'Order comment e-mail'),
                    'hint' => __a(__FILE__, 'E-mail with new comment to order sent to client. Message is sent only in case that both e-mail subject and body are set. There can be used the same inserts as in case of order e-mails plus following: <ul><li><code>:commentText:</code> - text of new added comment,</li><li><code>:commentAuthor:</code> - name of new comment author</li></ul>')
                ),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgSubjectNewComment' => array(
                    ),                    
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/msgBodyNewComment' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),                    
                array('/row'),
                array('h2' => __a(__FILE__, 'Wishlist sharing e-mail')),
                array('row', 'columns' => 1),
                    'Eshop/EshopWishlist/msgSubjectShareWishlist' => array(
                    ),                    
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopWishlist/msgBodyShareWishlist' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),                    
                array('/row'),
                array('h2' => __a(__FILE__, 'E-mail strážneho psa wishlistu')),
                array('row', 'columns' => 1),
                    'Eshop/EshopWishlist/msgSubjectWishlistWatchdog' => array(
                    ),                    
                array('/row'),
                array('row', 'columns' => 1),
                    'Eshop/EshopWishlist/msgBodyWishlistWatchdog' => array(
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Email'
                        )
                    ),                    
                array('/row'),
                array('h2' => __a(__FILE__, 'Heureka - Overené zákazníkmi')),
                array('row', 'columns' => 2),
                    'Eshop/EshopOrder/heurekaVerifiedByClientsKey' => array(
                        'label' => __a(__FILE__, 'Key'),
                        'hint' => __a(__FILE__, 'Tajný kľúč na sprevázdkovanie služby "Heureka - Overené zákazníkmi". Ak sa zadá tak o každej novej objednávke sa pošle do Heuréky info, aby mohli urobiť prieskum spokojnosti zákazníkov.Kľuč je možné ziskať <a href="%s" target="_blank">tu</a>.', 'https://sluzby.heureka.sk/n/sluzby/certifikat-spokojenosti/'),
                    ),
                    'Eshop/EshopOrder/heurekaVerifiedByClientsScript' => array(
                        'label' => __a(__FILE__, 'Script'),
                        'hint' => __a(__FILE__, 'Script to display badge "Heureka - Overené zákazníkmi". Once you gain the badge paste here the exact copy of the script from Heureka admin'),
                        'type' => 'textarea',
                        'style' => 'height: 34px',
                    ),
                array('/row'),
                array('h2' => __a(__FILE__, 'Heureka - Meranie konverzií')),
                array('row', 'columns' => 2),
                    'Eshop/EshopOrder/heurekaConversionKey' => array(
                        'label' => __a(__FILE__, 'Key'),
                        'hint' => __a(__FILE__, 'Tajný kľúč na sprevázdkovanie merania konverzií z Heuréky. Ak sa zadá tak pri každej novej objednávke sa pošle do Heuréky info, aby mohli urobiť štatistiky konverzií. Kľuč je možné ziskať <a href="%s" target="_blank">tu</a>.', 'https://sluzby.heureka.sk/obchody/mereni-konverzi/'),
                    ),
                array('/row'),
                array('h2' => __a(__FILE__, 'Zásielkovňa')),
                array('row', 'columns' => 2),
                    'Eshop/EshopShipmentMethod/zasielkovnaApiKey' => array(
                        'label' => __a(__FILE__, 'Key'),
                        'hint' => __a(__FILE__, 'Secret key of service "Zásielkovňa" to load pickup places.'),
                    ),
                array('/row'),
                array(
                    'h2' => __a(__FILE__, 'MRP - autonómny režim'),
                    'hint' => __a(__FILE__, 'Slúži na automatický prenos skladových stavov z MRP na web a prenos objednávok z webu do MRP')
                ),
                array('row', 'columns' => array(3, 3, 3, 3)),
                    'Eshop/mrp/serverIpAddress' => array(
                        'label' => __a(__FILE__, 'Verejná IP adresa servera'),
                        'hint' => __a(__FILE__, 'Server musí mať pevnú verejnú IP adresu. Táto sa dá zistiť zavolaním <code>http://whatismyip.cz/</code>. V routeri lokalnej siete musí byť urobený forwarding z vonkajšieho portu verejnej IP adresy na MRP port lokálnej IP adresy servera. Lokálnu IP adresu servera je možné zistiť pomocou konzolového príkazu <kbd>ipconfig</kbd>. Server musí mať tiež nastavenú pevnú lokálnu IP asresu (cez nastavenia jeho sieťovej karty)'),
                    ),
                    'Eshop/mrp/serverPort' => array(
                        'label' => __a(__FILE__, 'Port'),
                        'hint' => __a(__FILE__, 'Číslo portu, na ktorom MRP očakáva požiadavky autonómneho režimu'),
                    ),
                    'Eshop/mrp/privateKey' => array(
                        'label' => __a(__FILE__, 'Kryptovací kľúč'),
                        'hint' => __a(__FILE__, 'Kľúč služiaci na zakryptovanie komunikácie autonómneho režimu. Skopírujte ho sem tak ako je vygenerovaný v MRP.'),
                    ),
                    /*/>
                    'Eshop/mrp/lastCode' => array(
                        'label' => __a(__FILE__, 'Kód posledného produktu'),
                        'hint' => __a(__FILE__, 'Číslo poslednej produktovej karty, ktorá bola importovaná do MRP'),
                    ),
                    //*/
                
                array('/row'),
                array(
                    'h2' => __a(__FILE__, 'Frankana API'),
                    'hint' => __a(__FILE__, 'Slúži na import nopvých produktov')
                ),
                array('row', 'columns' => array(3, 3, 3, 3)),
                    'Eshop/frankana/apiUrlBase' => array(
                        'label' => __a(__FILE__, 'API URL'),
                        'hint' => __a(__FILE__, 'URL adresa Frankana API, napr.: <code>https://ff.data.bloodstream.cloud/</code>'),
                    ),
                    'Eshop/frankana/apiKey' => array(
                        'label' => __a(__FILE__, 'API kľúč'),
                        'hint' => __a(__FILE__, 'Tajný prístupový kľúč (bearer) na prístup k Frankana API'),
                    ),
                array('/row'),
                array(
                    'h2' => __a(__FILE__, 'Nákupný košík'),
                    'hint' => __a(__FILE__, 'Nastavenia košíka')
                ),
                array('row', 'columns' => 1),
                    'Eshop/EshopOrder/oversizedProductMessage' => array(
                        'label' => __a(__FILE__, 'Správa v košíku s nadrozmerným produktom'),
                        'hint' => __a(__FILE__, 'Správa bude zobrazovaná v košíku s nadrozmerným produktom namiesto informácií o doprave zdarma'),
                    ),
                array('/row'),
            ),
        ));
    }
}

