<?php

class EshopManufacturers extends SmartController {
        
    /**
     * MVC element
     * 
     * Generates manufacturers side menu
     * 
     * @param string $arg
     * @return string
     */
    public function menu($arg = null) {
                        
        $conditions = array(
            'EshopManufacturer.active' => 1
        );
        
        // get system slugs
        $manufacturersSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer');
        $urlBase = $manufacturersSlug;
                
        $Manufacturer = $this->loadModel('EshopManufacturer', true);
        $activeSlug = $Manufacturer->resolveActiveSlug(array(
            'arg' => $arg,
            'manufacturerProductsIndexSlug' => $manufacturersSlug,
        ));
                 
        $list = $Manufacturer->findList(array(
            'conditions' => $conditions,
            'fields' => array(
                'EshopManufacturer.name AS label',
                'EshopManufacturer.slug AS locator',
            ),
            //'order' => 'EshopManufacturer.name ASC',
        )); 
        // simulate tree
        $list = array($list);
        
        return $this->loadView('EshopManufacturers/menu', array(
            'list' => $list,
            'urlBase' => $urlBase,
            'activeItem' => $activeSlug,
        ));
    }
    
    public function admin_index() {
        $Manufacturer = $this->loadModel('EshopManufacturer', true);
        
        // get lang to retrieve records for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        $records = $Manufacturer->find(array(
            'fields' => '*',
            'order' => 'name',
            'paginate' => true,
            'lang' => $lang,
        ));
        
        $viewLocator = App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer');
        App::setSeoTitle(__a(__FILE__, 'Manufacturers'));
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'Manufacturers'),
            'records' => $records,
            'primaryKey' => 'id',
            'columns' => array(
                'name' => __a(__FILE__, 'Name'),
                'slug' => __a(__FILE__, 'Slug'),
//                'import_group' => __a(__FILE__, 'Import group'),
                'image' => __a(__FILE__, 'Obrázok'),
                'import_discount_rate' => __a(__FILE__, 'Import discount rate'),
            ),
            'renderFields' => array(
                'active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'slug' => function($value) use ($lang, $viewLocator) {
                    $locator = App::getUrl(array(
                        'locator' => $viewLocator,
                        'args' => array($value)
                    ));
                    return Html::hyperlinkTag($locator, $value, array(
                        'lang' => $lang,
                        'attributes' => array('target' => '_blank')
                    ));
                },
                'image' => function($value) use ($Manufacturer) {
                    if (!empty($value)) {
                        return 
                        '<img src="' . $Manufacturer->getFileFieldUrlPath('image', array(
                            'file' => $value,
                            'variant' =>  ''
                        )) . '"class="display-image-input">';
                    }
                    return null;
                },
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('active' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                )
            ),
            'Paginator' => $Manufacturer->Paginator,
//            'paginatorOptions' => array(
//                'filterSelectInputs' => array(
//                    'import_group' => $Manufacturer->getEnumValues('import_group'),
//                ),
//            ),
            'actions' => array(                
                'add' => array(
                    'url' => '/mvc/Eshop/EshopManufacturers/admin_add',
                ),
                'export' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopManufacturers/admin_export',
                        'inherit' => array('get' => array(
                            $Manufacturer->Paginator->getPropertyFilterUrlParam(),
                            $Manufacturer->Paginator->getPropertySortUrlParam(),
                        )),
                    ),
                ),
                'lang' => true
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopManufacturers/admin_edit',
                        'get' => array(
                            'lang' => $lang
                        )
                    )
                ),
//                'copy' => array(
//                    'url' => '/mvc/App/Users/<USER>',
//                ),
                'delete' => array(
                    'url' => '/mvc/Eshop/EshopManufacturers/admin_delete',
                    'confirmMessage' => __a(__FILE__, 'Please, confirm removal of item :name:'),
                ),
            )
        ));
    }
    
    public function admin_export() {
        $Manufacturer = $this->loadModel('EshopManufacturer', true);
        
        $Manufacturer->export(
            array(
                'avoidFields' => array(
                    'EshopManufacturer.modified',
                    'EshopManufacturer.deleted',
                ),
                'order' => 'EshopManufacturer.name',
                // allow paginator filtering & sorting without limit
                'paginate' => true,
                'limit' => false,
            ), 
            array(
                'file' => $this->name,
                'format' => 'xlsx',
            )
        );
    }    
    
    public function admin_add() {
        $Manufacturer = $this->loadModel('EshopManufacturer', true);
        
        if ($this->data) {
            if ($Manufacturer->save($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'New record has been succesfully created'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                    'args' => array($Manufacturer->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        
        App::setSeoTitle(__a(__FILE__, 'Add new manufacturer'));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Add new manufacturer'),
            'data' => $this->data,
            'Model' => $Manufacturer,
            'lang' => DEFAULT_LANG,
            'columns' => 4,
            'actions' => array(
                'lang' => array(
                    'options' => array_combine(array(DEFAULT_LANG), array(DEFAULT_LANG)),
                )
            ),
            'fields' => array(
                array(
                    'field' => 'name', 
                    'label' => __a(__FILE__, 'Name'),
                    'autofocus' => true,
                ),
            )
        ));
    }  
    
    public function admin_edit($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        
        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        $Manufacturer = $this->loadModel('EshopManufacturer', true);
        
        if ($this->data) {
            if ($Manufacturer->save($this->data, array('lang' => $lang))) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => $this->action,
                    'args' => array($id),
                    'source' => App::$requestSource,
                    'inherit' => array('get' => array('lang'))
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        else {
            $this->data = $Manufacturer->findFirstBy('id', $id, array('lang' => $lang));
            // if product does not exist then redirect to referer
            if (empty($this->data)) {
                App::setErrorMessage(__a(__FILE__, 'Invalid record id %s', (string)$id));
                App::redirect(App::getRefererUrl(App::getUrl(array(
                    'locator' => '/_error',
                    'source' => App::$requestSource,
                ))));
            }
            // get file fields real path
            $fileFields = array_keys($Manufacturer->getPropertyFileFields());
            foreach($fileFields as $fileField) {
                if (!empty($this->data[$fileField])) {
                    $this->data[$fileField] = $Manufacturer->getFileFieldUrlPath($fileField, array('file' => $this->data[$fileField]));
                }
            }
        }
        
        // add name to title and tabTitle
        if (!empty($this->data['name'])) {
            $name = $this->data['name'];
        }
        // name should not be empty, if so then it is error so use actual name
        else {
            $name = $Manufacturer->findFieldBy('EshopManufacturer.name', 'id', $this->data['id']);
        }

        App::setSeoTitle(__a(__FILE__, '&quot;%s&quot;', $name));

        return $this->loadView('EshopManufacturers/admin_form', array(
            'title' => __a(__FILE__, 'Edit manufacturer "%s"', $name),
            'data' => $this->data,
            'Model' => $Manufacturer,
            'lang' => $lang,
            'columns' => 4,
            'actions' => array(
                'lang' => true
            )
        ));
    }

    public function admin_delete($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        $Manufacturer = $this->loadModel('EshopManufacturer', true);
        $Manufacturer->deleteBy('id', $id);
        App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'module' => $this->module,
            'controller' => $this->name,
            'action' => 'admin_index',
            'source' => App::$requestSource,
        ))));
    }

    public function indexSlider() {
        $this->displayOriginComment = true;
        $defaults = array(
            'title' => null, // __(__FILE__, 'Naši výrobcovia'),
            'buttonText' => null, //__(__FILE__, 'Všetci výrobcovia'),
        );
        $inputOptions = Arr::camelizeKeys($this->params, array('separator' => '-'));
        $options = array_merge($defaults, $inputOptions);
        $Manufacturer = $this->loadModel('EshopManufacturer', true);
        $manufacturers = $Manufacturer->find(array(
            'conditions' => array(
                'EshopManufacturer.active' => true,
                'EshopManufacturer.image !=' => null,
            ),
            'fields' => array(
                'EshopManufacturer.id',
                'EshopManufacturer.name',
                'EshopManufacturer.slug',
                'EshopManufacturer.image',
            ),
        ));
        // set image url paths
        foreach ($manufacturers as &$manufacturer) {
            if (!empty($manufacturer['image'])) {
                $manufacturer['image'] = $Manufacturer->getFileFieldUrlPath('image', array(
                    'file' => $manufacturer['image'],
                    'variant' =>  ''
                ));
            }
        }
        unset($manufacturer);

        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('EshopManufacturers/indexSlider', array(
            'title' => $options['title'],
            'buttonText' => $options['buttonText'],
            'manufacturers' => $manufacturers,
            //'indexLocator' => App::getContentLocatorByPid('???'), //@todo
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Upraviť výrobcu'),
                )
            ),
        ));
    }
}
