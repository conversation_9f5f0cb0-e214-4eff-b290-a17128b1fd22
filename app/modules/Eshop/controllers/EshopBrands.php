<?php

class EshopBrands extends SmartController {
    
    /**
     * Allow the model methods hinting in IDE
     * @var EshopBrand
     */
    protected $Model;
    
    public function __construct(){
        parent::__construct();
        $this->loadModel('EshopBrand');
    }
    
    public function admin_index() {
        $Brand = new EshopBrand();
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        $records = $Brand->find(array(
            'fields' => '*',
            'order' => 'name',
            'paginate' => true,
            'lang' => $lang,
        ));     
        // set file fields url paths
        $fielFieds = array_keys($this->Model->getPropertyFileFields());
        foreach ($records as &$record) {
            foreach ($fielFieds as $fileField) {
                if (!empty($record[$fileField])) {
                    $record[$fileField] = $this->Model->getFileFieldUrlPath($fileField, array(
                        'file' => $record[$fileField],
                    ));
                }
            }
        }
        App::setSeoTitle(__a(__FILE__, 'Brands'));
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'Brands'),
            'records' => $records,
            'primaryKey' => 'id',
            'columns' => array(
                'name' => __a(__FILE__, 'Name'),
                'logo' => __a(__FILE__, 'Obrázok'),
                'slug' => __a(__FILE__, 'Slug'),
            ),
            'renderFields' => array(
                'logo' => function($value) use ($Brand) {
                    if (!empty($value)) {
                        return  '<img src="' . $value . '" style="width:30px;">';
                    }
                    return null;
                },
            ),
            'Paginator' => $Brand->Paginator,
            'actions' => array(                
                'add' => array(
                    'url' => '/mvc/Eshop/EshopBrands/admin_add',
                ),
                'export' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopBrands/admin_export',
                        'inherit' => array('get' => array(
                            $Brand->Paginator->getPropertyFilterUrlParam(),
                            $Brand->Paginator->getPropertySortUrlParam(),
                        )),
                    ),
                ),
                'lang' => true
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopBrands/admin_edit',
                        'get' => array(
                            'lang' => $lang
                        )
                    )
                ),
                'delete' => array(
                    'url' => '/mvc/Eshop/EshopBrands/admin_delete',
                    'confirmMessage' => __a(__FILE__, 'Please, confirm removal of item :name:'),
                ),
            )
        ));
    }
    
    public function admin_add() {
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        $Brand = new EshopBrand();
        if ($this->data) {
            if ($Brand->save($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'New record has been succesfully created'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                    'args' => array($Brand->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));   
        }
        App::setSeoTitle(__a(__FILE__, 'Add new brand'));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Add new brand'),
            'data' => $this->data,
            'Model' => $Brand,
            'lang' => $lang,
            'columns' => 4,
            'actions' => array(
                'lang' => array(
                    'options' => array_combine(array($lang), array($lang)),
                )
            ),
            'fields' => array(
                array(
                    'field' => 'name', 
                    'label' => __a(__FILE__, 'Name'),
                    'autofocus' => true,
                ),
            )
        ));
    }
    
    public function admin_edit($id = null) {
        $lang = !empty($_REQUEST['lang']) ? $_REQUEST['lang'] : App::$lang;
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        $Brand = new EshopBrand();
        if ($this->data) {
            if ($Brand->save($this->data, array('lang' => $lang))) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => $this->action,
                    'args' => array($id),
                    'source' => App::$requestSource,
                    'inherit' => array('get' => array('lang'))
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        else {
            $this->data = $Brand->findFirstBy('id', $id, array('lang' => $lang));
            // if product does not exist then redirect to referer
            if (empty($this->data)) {
                App::setErrorMessage(__a(__FILE__, 'Invalid record id %s', (string)$id));
                App::redirect(App::getRefererUrl(App::getUrl(array(
                    'locator' => '/_error',
                    'source' => App::$requestSource,
                ))));
            }
            // get file fields real path
            $fileFields = array_keys($Brand->getPropertyFileFields());
            foreach($fileFields as $fileField) {
                if (!empty($this->data[$fileField])) {
                    $this->data[$fileField] = $Brand->getFileFieldUrlPath($fileField, array('file' => $this->data[$fileField]));
                }
            }
        }
        
        // add name to title and tabTitle
        if (!empty($this->data['name'])) {
            $name = $this->data['name'];
        }
        // name should not be empty, if so then it is error so use actual name
        else {
            $name = $Brand->findFieldBy('EshopBrand.name', 'id', $this->data['id']);
        }
        App::setSeoTitle(__a(__FILE__, '&quot;%s&quot;', $name));
        return $this->loadView('EshopBrands/admin_edit', array(
            'title' => __a(__FILE__, 'Edit brand "%s"', $name),
            'data' => $this->data,
            'Model' => $Brand,
            'lang' => $lang,
            'columns' => 4,
            'actions' => array(
                'lang' => true
            )
        ));
    }
    
    public function admin_delete($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        $Brand = new EshopBrand();
        $Brand->deleteBy('id', $id);
        App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'module' => $this->module,
            'controller' => $this->name,
            'action' => 'admin_index',
            'source' => App::$requestSource,
        ))));
    }
}