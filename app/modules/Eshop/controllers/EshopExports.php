<?php

class EshopExports extends EshopController {
    
    /**
     * CRON
     * 
     * Exports all products data into price engines XML files
     * 
     * @param string $engine One of 'pricemania', 'najnakup', 'enakup', 'tovarsk', 
     *      'heureka', 'pesiazona', 'zbozicz', 'google', 'facebook'. If 'all' then all price 
     *      engines are exported at once.
     */
    public function exportPriceEnginesXml($engine = null) {
        App::setLayout(false);        
        // following engines are available for this project
        $availableEngines = array(
            //'pricemania',
            //'najnakup',
            //'enakup',
            //'tovarsk',
            'heureka',
            //'pesiazona',
            //'zbozicz',
            'google',
            'facebook',
        );
        if ($engine === 'all') {
            $engine = $availableEngines;
        }
        if (
            $engine === $availableEngines
            || in_array($engine, $availableEngines)
        ) {
            $Export = $this->loadModel('EshopExport', true);
            $Export->exportPriceEnginesXml($engine);
        }
    }    
    
    /**
     * Exports Pohody system XML file for specified order.
     */
    public function admin_exportPohodaXmlOrder($id) {       
        
        if (empty($id) || !is_numeric($id)) {
            App::setMessage(__a(__FILE__, 'Wrong identifikator of order id'));
            return;
        }
        try {
            $Export = $this->loadModel('EshopExport', true);
            $Export->exportPohodaOrderXml($id);
            App::setMessage(__a(__FILE__, 'Pohoda xml order was successfully created'));
            return;
        }
        catch (Throwable $e) {
            App::setMessage($e->getMessage());
        }
        return;
    }    
    
    /**
     * Exports products data into DBF file for MRP
     * 
     * @param bool $export Optional. Export is done only if TRUE otherwise only 
     *      submit button is displayed. Defaults to FALSE.
     * 
     * @return string
     */
    public function admin_exportMrpProducts($export = false) {
        $Export = $this->loadModel('EshopExport', true);
        $progress = null;
        if (!empty($export)) {            
            $progress = $Export->exportMrpProducts(array(
//                'simulate' => true,
            ));
        }
        return $this->loadView('EshopExports/admin_exportMrpItems', array(
            'title' => __a(__FILE__, 'Export of products to MRP'),
            'data' => $this->data,
            'progress' => $progress,
            'errors' => $Export->getErrors(),
        ));
    }
    
    /**
     * Exports product manufacturers names into DBF file for MRP
     * 
     * @param bool $export Optional. Export is done only if TRUE otherwise only 
     *      submit button is displayed. Defaults to FALSE.
     * 
     * @return string
     */
    public function admin_exportMrpProductManufacturers($export = false) {
        $Export = $this->loadModel('EshopExport', true);
        $progress = null;
        if (!empty($export)) {            
            $progress = $Export->exportMrpProductManufacturers(array(
//                'simulate' => true,
            ));
        }
        return $this->loadView('EshopExports/admin_exportMrpItems', array(
            'title' => __a(__FILE__, 'Export výrobcov produktov do MRP'),
            'data' => $this->data,
            'progress' => $progress,
            'errors' => $Export->getErrors(),
        ));
    }
    
    /**
     * Exports product images for MRP
     * 
     * @param bool $export Optional. Export is done only if TRUE otherwise only 
     *      submit button is displayed. Defaults to FALSE.
     * 
     * @return string
     */
    public function admin_exportMrpProductImages($export = false) {
        $Export = $this->loadModel('EshopExport', true);
        $progress = null;
        if (!empty($export)) {            
            $progress = $Export->exportMrpProductImages(array(
//                'simulate' => true,
                'processAmount' => 10000,
            ));
        }
        return $this->loadView('EshopExports/admin_exportMrpItems', array(
            'title' => __a(__FILE__, 'Export obrázkov produktov do MRP (v dávkach po 10000)'),
            'data' => $this->data,
            'progress' => $progress,
            'errors' => $Export->getErrors(),
        ));
    }
    
    /**
     * Exports orders data into DBF file for MRP
     * 
     * To import the DBF file in MRP use: Udrzba > Udrzba > Import > Objednavky
     * 
     * @param bool $export Optional. Export is done only if TRUE otherwise only 
     *      submit button is displayed. Defaults to FALSE.
     * 
     * @return string
     */
    public function admin_exportMrpOrders($export = false) {
        $Export = $this->loadModel('EshopExport', true);
        $progress = null;
        if (!empty($export)) {
            $progress = $Export->exportMrpOrders(array(
//                'simulate' => true,
            ));
        }
        return $this->loadView('EshopExports/admin_exportMrpItems', array(
            'title' => __a(__FILE__, 'Export of orders to MRP'),
            'data' => $this->data,
            'progress' => $progress,
            'errors' => $Export->getErrors(),
        ));
    }
    
    /**
     * Exports orders data into XML file for MRP
     * 
     * To import the XML file in MRP use: Udrzba > Udrzba > Import > Eobchod (Objednavky) > MRP Eshop(XML)
     * 
     * @param bool $export Optional. Export is done only if TRUE otherwise only 
     *      submit button is displayed. Defaults to FALSE.
     * 
     * @return string
     */
    public function admin_exportMrpOrdersXml($export = false) {
        $Export = $this->loadModel('EshopExport', true);
        $progress = null;
        if (!empty($export)) {
            $progress = $Export->exportMrpOrdersXml(array(
//                'simulate' => true,
//                'processAmount' => 1,
            ));
        }
        return $this->loadView('EshopExports/admin_exportMrpItems', array(
            'title' => __a(__FILE__, 'Export of orders into XML for MRP'),
            'data' => $this->data,
            'progress' => $progress,
            'errors' => $Export->getErrors(),
        ));
    }
    
    /**
     * Exports orders data automatically into MRP
     * 
     * @param bool $export If TRUE then the export is done. Defaults to FALSE, it
     *          means the launch screen is displayed.
     * @param bool $allowErrorsEmail If TRUE the errors email is allowed, it means
     *          there is send an info email if any error occures. Defaults to FALSE.
     * @param bool $this->data['single_item_export'] GET param
     * @param bool $this->data['orderNumber'] GET param
     * 
     * @return string
     */
    public function autoexportMrpOrders($export = false, $allowErrorsEmail = false) {
        $Export = $this->loadModel('EshopExport', true);
        $progress = null;
        if (!empty($export)) {
            $progress = $Export->exportMrpOrdersXml(array(
                'sendToMrp' => true,
                'orderNumber' => Sanitize::value($this->data['orderNumber']),
                'processAmount' => empty($this->data['single_item_export']) ? 0 : 1,
                //'simulate' => ON_LOCALHOST,
            ));
        }
        App::setLayout('App', 'frame');
        
        // if there are errors then send an info email if allowed
        if (
            !empty($progress['errors'])
            && $allowErrorsEmail
        ) {
            $to = $this->getSetting('email.from');
            if (($logEmail = App::getPropertyLogEmail())) {
                $to = array_merge((array)$to, (array)$logEmail);
            }
            try {
                App::sendEmail(
                     __e(
                        __FILE__, 
                        'Prenos objednávok do MRP zlyhal pre nasledovné objednávky:<ul><li>%s</li></ul>',
                        implode('</li><li>', $progress['errors'])
                    ),
                    $to,
                    array(
                        'from' => $this->getSetting('email.from'),
                        'subject' => __e(__FILE__, 'Prenos objednávok do MRP zlyhal'),

                    )
                );
            } 
            catch (Exception $e) {}
        }
        
        return $this->loadView('EshopExports/admin_exportMrpItems', array(
            'title' => __a(__FILE__, 'Prenos objednávok do MRP'),
            'data' => $this->data,
            'progress' => $progress,
            'errors' => $Export->getErrors(),
            'autonomous' => true,
            'allowSingleItemExport' => true,
        ));
    }
    
    public function admin_downloadMrpFile($filePid = null) {
        $Export = $this->loadModel('EshopExport', true);
        if (empty($this->data) && !empty($filePid)) {
            $this->data['filePid'] = $filePid;
        }
        if ($this->data) {            
            try {    
                $Export->outputMrpFile($this->data['filePid']);
                App::setLayout(false);
                return;
            } 
            catch (Throwable $e) {
                App::setErrorMessage($e->getMessage());
            }
        }
        $title = __a(__FILE__, 'Download MRP file');
        App::setSeoTitle($title);
        return Html::smartForm(array(
            'title' => $title,
            'columns' => 4,
            'fields' => array(
                array(
                    'field' => 'filePid',
                    'label' => __a(__FILE__, 'File'),
                    'type' => 'select',
                    'options' => $Export->getPropertyMrpFiles(),
                ),
            )
        ));
    }    
}
