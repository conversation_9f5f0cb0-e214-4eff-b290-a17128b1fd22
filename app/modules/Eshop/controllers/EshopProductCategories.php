<?php

class EshopProductCategories extends Controller {
    
    public function __construct(){
        parent::__construct();
        $this->loadModel('EshopProductCategory');
    }
    
    /**
     * MVC element
     *
     * Generates categories side menu
     *
     * @param string $arg
     * @return string
     */
    public function menu($arg = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'parent' => null,
            'title' => '',
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
                       
        if (!empty($options['parent'])) {
            $parent = $options['parent'];
            }
        else {
            $parent = 'categories';
        }
       
        $categoriesSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory');
               
        $Category = $this->loadModel('EshopProductCategory', true);
        $activeSlug = $Category->resolveActiveSlug(array(
            'arg' => $arg,
            'categoryProductsIndexSlug' => $categoriesSlug,
        ));
                
        // get $urlBase
        $root = $Category->findFirstUpInTree($parent, array(
            'start' => -1,
            'fields' => array('EshopProductCategory.pid'),
        ));
        if ($root['pid'] === 'designerCategories') {
            $urlBase = App::getContentLocatorByPid('Eshop.EshopProducts.designer');
        }
        elseif ($root['pid'] === 'opportunities') {
            $urlBase = App::getContentLocatorByPid('Eshop.EshopProducts.indexOpportunity');
        }
        else {
            $urlBase = $categoriesSlug;
        }
                
        $list = $Category->findTree(
            $parent,
            array(
                'conditions' => array(
                    'EshopProductCategory.active' => true,
                ),
                'fields' => array(
                    'EshopProductCategory.name AS label',
                    'EshopProductCategory.slug AS locator',
                    'EshopProductCategory.image AS icon',
                ),
                //'order' => 'EshopProductCategory.name ASC',
            )
        );
        
        // set images url paths (but only on top level)
        $topLevel = true;
        foreach ($list as &$levelItems) {
            foreach ($levelItems as &$item) {
                if (
                    $topLevel
                    && $item['icon']
                ) {
                    $item['icon'] = $Category->getFileFieldUrlPath('image', array(
                        'file' => $item['icon'],
                    ));
                }
                else {
                    unset($item['icon']);
                }
            }
            unset($item);
            //$topLevel = false; //images url paths only on top level
        }
        unset($levelItems);
                        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('EshopProductCategories/menu', array(
            'list' => $list,
            'urlBase' => $urlBase,
            'activeItem' => $activeSlug,
            'title' => $options['title'],
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit category'),
                )
            )
        ));
    }
    
    /**
     * MVC element
     *
     * Generates categories side dropdownMenu
     *
     * @return string
     */
    public function dropdownMenu() {
        $this->displayOriginComment = true;
        return $this->loadView('EshopProductCategories/dropdownMenu');
    }

    /**
     * Indexes categories of specified parent category
     * 
     * @param int|string $this->params['parent'] Id or pid of parent category to 
     *      index categories from.
     * @param int|string $this->params['parentSlug'] Slug of of parent category to 
     *      index categories from. Considered only if previous param is empty.
     * @param string $arg Actual URL first argument used to resolve active slug to 
     *      index categories from. Considered only if previous params are empty.
     * 
     * @return string Html
     */
    public function index($arg = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'parent' => null,
            'parentSlug' => null,
            'view' => 'index',
            'title' => null,
            'emptyIndexMessage' => false,
            'paginate' => false ,
            'columns' => null,
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        
        $categoriesSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory');
        
        $Category = $this->loadModel('EshopProductCategory', true);
        $activeSlug = $Category->resolveActiveSlug(array(
            'arg' => $arg,
            'categoryProductsIndexSlug' => $categoriesSlug,
        ));
                
        if (!empty($options['parent'])) {
            $parent = $options['parent'];
        }
        elseif (!empty($options['parentSlug'])) {
            $parent = array(
                'conditions' => array(
                    'slug' => $options['parentSlug'],
                )
            );
        }
        elseif (
            ($activeSlug = $Category->resolveActiveSlug(array(
                'arg' => $arg,
                'categoryProductsIndexSlug' => $categoriesSlug,
            )))
        ) {
            $parent = array(
                'conditions' => array(
                    'slug' => $activeSlug,
                )
            );
        }
        else {
            $parent = 'categories';
        }
        $Category = $this->loadModel('EshopProductCategory', true);
        $categories = $Category->findInTree(
            $parent,
            array(
                'conditions' => array(
                    'EshopProductCategory.active' => true,
//                    'EshopProductCategory.image !=' => null,
                ),
                'fields' => array(
                    'EshopProductCategory.id',
                    'EshopProductCategory.name',
                    'EshopProductCategory.slug',
                    'EshopProductCategory.image',
                ),
                'order' => 'EshopProductCategory.sort ASC',
                'depth' => 1,
                'paginate' => $options['paginate'],
            )
        );
        foreach ($categories as &$category) {
            $category['image'] = $Category->getFileFieldUrlPath('image', array('file' => $category['image'], 'variant' =>  'small'));
        }
        unset($category);
        
        // get slugItemView
        $root = $Category->findFirstUpInTree($parent, array(
            'start' => -1,
            'fields' => array('EshopProductCategory.pid'),
        ));
        if ($root['pid'] === 'designerCategories') {
            $slugItemView = App::getContentLocatorByPid('Eshop.EshopProducts.designer');
        }
        elseif ($root['pid'] === 'opportunities') {
            $slugItemView = App::getContentLocatorByPid('Eshop.EshopProducts.indexOpportunity');
        }
        else {
            $slugItemView = $categoriesSlug;
        }
        
        // load view
        App::loadLib('App', 'SmartAdminLauncher');
        $options['view'] = 'EshopProductCategories/' . $options['view'];
        return $this->loadView($options['view'], array(
            'title' => $options['title'],
            'emptyIndexMessage' => $options['emptyIndexMessage'],
            'columns' => $options['columns'],
            'items' => $categories,
            'slugItemView' => $slugItemView,
            'Paginator' => $Category->Paginator,
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit category'),
                )
            )
        ));
    }
            
    public function admin_index() {
        $Category = $this->loadModel('EshopProductCategory', true);
        
        // get lang to retrieve records for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        // find roots
        $roots = $Category->findList(array(
            'fields' => array('EshopProductCategory.name'),
            'conditions' => array('parent_id' => null),
            'order' => 'sort ASC',
            'lang' => $lang,
        ));
        
        // get root
        $rootIds = array_keys($roots);
        $root = !empty($_GET['root']) ? $_GET['root'] : reset($rootIds);
        
        $categories = $Category->findInTree(
            $root,
            array(
                'fields' => array(
                    'EshopProductCategory.name',
                    'EshopProductCategory.alternative_name',
                    'EshopProductCategory.code',
                    'EshopProductCategory.slug',
                    'EshopProductCategory.active',
                    'EshopProductCategory.path',
                ),
                'order' => 'sort',
                'paginate' => true,
                'lang' => $lang,
            )
        );
        
        $viewLocator = App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory');
        App::setSeoTitle(__a(__FILE__, 'Product categories - index'));
        $url = App::getUrl(array(
            'locator' => '/mvc/Eshop/EshopProductCategories/admin_index', 
            'inherit' => array('get' => 'root')
        ));
        $title = '<a href="' . $url . '">' . __a(__FILE__, 'Product categories') . '</a>';
        return Html::smartIndex(array(
            'title' => $title,
            'records' => $categories,
            'columns' => array(
                'name' => __a(__FILE__, 'Name'),
                'alternative_name' => __a(__FILE__, 'Alternatívny názov'),
                'code' => __a(__FILE__, 'Category code'),
                'slug' => __a(__FILE__, 'Slug'),
                'active' => __a(__FILE__, 'Active'),
            ),
            'renderFields' => array(
                'active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'slug' => function($value) use ($lang, $viewLocator) {
                    $locator = App::getUrl(array(
                        'locator' => $viewLocator,
                        'args' => array($value)
                    ));
                    return Html::hyperlinkTag($locator, $value, array(
                        'lang' => $lang,
                        'attributes' => array('target' => '_blank')
                    ));
                },
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('active' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                )
            ),
            'Paginator' => $Category->Paginator,
            'actions' => array(
                'showTree' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopProductCategories/admin_showTree',
                        'inherit' => array('get' => 'root'),
                    ),
                ),                
                'root' => array(
                    'if' => count($roots) > 1,
                    'url' => '/mvc/Eshop/EshopProductCategories/admin_index',
                    'options' => $roots,
                ),
                'lang' => true,
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopProductCategories/admin_edit',
                        'get' => array(
                            'lang' => $lang
                        )
                    )
                ),
            )
        ));
    }
    
    /**
     * Shows simple tree of contents for provided parent id and root.
     * The root is received by $_GET['root'] from select action. 
     * If no root provided then first found top level root is used.
     * 
     * @param int $parentId Id of parent to display childs for. If NULL then 
     *      top level is shown
     * @param string $_GET['root'] Optional
     * 
     * @return string View html
     */
    public function admin_showTree() {
        $Category = $this->loadModel('EshopProductCategory', true);
        
        // get lang to retrieve records for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        // find roots
        $roots = $Category->findList(array(
            'fields' => array('EshopProductCategory.name'),
            'conditions' => array('parent_id' => null),
            'order' => 'sort ASC',
            'lang' => $lang,
        ));
        
        // get root
        $rootIds = array_keys($roots);
        $root = !empty($_GET['root']) ? $_GET['root'] : reset($rootIds);
                        
        // get the parent direct childs
        $categories = $Category->findInTree(
            $root,
            array(
                'fields' => array(
                    'EshopProductCategory.name',
                    'EshopProductCategory.alternative_name',
                    'EshopProductCategory.code',
                    'EshopProductCategory.slug',
                    'EshopProductCategory.active',
                    'EshopProductCategory.path',
                ),
                'order' => 'sort',
                'lang' => $lang,
            ),
            // get root parent id in case 
            $rootId
        );
        if (empty($rootId)) {
            App::setErrorMessage(__a(__FILE__, 'Invalid tree root specification'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        
        // render view
        $viewLocator = App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory');
        App::setSeoTitle(__a(__FILE__, 'Product categories'));
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'Product categories'),
            'tree' => array(
                'column' => 'name',
                'status' => 'collapsed', //'expanded'
                'showNodeType' => true,
            ),
            'records' => $categories,
            'columns' => array(
                'name' => __a(__FILE__, 'Name'),
                'alternative_name' => __a(__FILE__, 'Alternatívny názov'),
                'code' => __a(__FILE__, 'Category code'),
                'slug' => __a(__FILE__, 'Slug'),
                'active' => __a(__FILE__, 'Active'),
            ),
            'renderFields' => array(
                'active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'slug' => function($value) use ($lang, $viewLocator) {
                    $locator = App::getUrl(array(
                        'locator' => $viewLocator,
                        'args' => array($value)
                    ));
                    return Html::hyperlinkTag($locator, $value, array(
                        'lang' => $lang,
                        'attributes' => array('target' => '_blank')
                    ));
                },
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('active' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                )
            ),
            'actions' => array(
                'showIndex' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopProductCategories/admin_index', 
                        'inherit' => array('get' => 'root'),
                        'get' => array(
                            'lang' => $lang
                        )
                    ),
                ),
                'add' => array(
                    'url' => '/mvc/Eshop/EshopProductCategories/admin_add/' . $rootId,
                ),
                'root' => array(
                    'if' => count($roots) > 1,
                    'url' => '/mvc/Eshop/EshopProductCategories/admin_showTree',
                    'options' => $roots,
                ),
                'lang' => true,
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopProductCategories/admin_edit',
                        'get' => array(
                            'lang' => $lang
                        )
                    )
                ),
//                'view' => array(
//                    'url' => '/mvc/Eshop/EshopProductCategories/admin_view',
//                ),
//                'copy' => array(
//                    'url' => '/mvc/Eshop/EshopProductCategories/admin_add/' . (int)$parentId, // ensure that zero is here if $parentId is NULL
//                ),
                'delete' => array(
                    'if' => array('permanent' => 0),
                    'url' => '/mvc/Eshop/EshopProductCategories/admin_delete',
                    'confirmMessage' => __a(__FILE__, 'Please, confirm removal of item :name:'),
                ),
//                'move' => array(
//                    'url' => '/mvc/Eshop/EshopProductCategories/admin_move/' . (int)$parentId,
//                ),
                'addChild' => array(
                    'url' => '/mvc/Eshop/EshopProductCategories/admin_add/',
                ),
            )
        ));
    }
    
    /**
     * Action to add new web content under provided parentId and possibly copying
     * specfied web content
     * 
     * @param int $parentId
     * 
     * @return string View html
     */
    public function admin_add($parentId = null) {
        if (!$parentId) {
            App::setErrorMessage(__a(__FILE__, 'Missing parent id of new created category'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/',
                'source' => App::$requestSource,
            ))));
        }
        
        $Category = $this->loadModel('EshopProductCategory', true);
                
        if ($this->data) {
            $this->data['parent_id'] = $parentId;
            if ($Category->addTreeNode($this->data['parent_id'], $this->data, array(
                'create' => true,
            ))) {
                App::setSuccessMessage(__a(__FILE__, 'New record has been succesfully created'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                    'args' => array($Category->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
        }
        
        // get rootId used below in Model::findTreeSelectList()
        $parent = $Category->findFirstBy('id', $parentId, array('fields' => array('EshopProductCategory.name')));
                
        App::setSeoTitle(__a(__FILE__, 'New category'));
        $title = __a(__FILE__, 'New category under &quot;%s&quot;', $parent['name']);
        return Html::smartForm(array(
            'title' => $title,
            'data' => $this->data,
            'Model' => $Category,
            'lang' => DEFAULT_LANG,
            'columns' => 4,
            'tabsToReloadAfterSave' => array(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_index',
                )),
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_showTree',
                )),
            ),
            'fields' => array(
                array(
                    'field' => 'name', 
                    'label' => __a(__FILE__, 'Category name'),
                    'autofocus' => true,
                ),
            )
        ));
    }
    
    public function admin_edit($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        $Category = $this->loadModel('EshopProductCategory', true);
        
        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        if ($this->data) {
            try {
                if ($Category->saveAll($this->data, array('lang' => $lang))) {
                    App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                    App::redirect(App::$url);
                }
                App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));                
            } 
            catch (Exception_DB_TablesReservationFailure $e) {
                App::setErrorMessage(__a(__FILE__, 'Record save has failed. Data are reserved by other process. Please try later.')); 
                App::logError($e->getMessage(), array(
                    'var' => $e,
                    'email' => true,
                ));
            }
        }
        else {
            try {
                $this->data = $Category->findAll(array(
                    'conditions' => array('EshopProductCategory.id' => $id),
                    'lang' => $lang,
                    'first' => true,
                ));
            }
            catch (Exception_DB_TablesReservationFailure $e) {
                App::setErrorMessage(__a(__FILE__, 'Data are reserved by other process. Please try later.'));
                App::redirect(App::getUrl(array(
                    'locator' => '/_error',
                    'source' => App::$requestSource,
                )));
            }
            // if product does not exist then redirect to referer
            if (empty($this->data)) {
                App::setErrorMessage(__a(__FILE__, 'Invalid record id %s', (string)$id));
                App::redirect(App::getRefererUrl(App::getUrl(array(
                    'locator' => '/_error',
                    'source' => App::$requestSource,
                ))));
            }
            // get file fields real path
            $fileFields = array_keys($Category->getPropertyFileFields());
            foreach($fileFields as $fileField) {
                if (!empty($this->data[$fileField])) {
                    $this->data[$fileField] = $Category->getFileFieldUrlPath($fileField, array('file' => $this->data[$fileField]));
                }
            }
            // preset new previous sibling id to actual one
            $this->data['previous_sibling_id'] = $Category->getPreviousSiblingId($this->data);
            $this->data['new_previous_sibling_id'] = $this->data['previous_sibling_id'];
        }
        
        // get rootId used below in Model::findTreeSelectList()
        $parentIds = Model::getParentIdsFromTreePath($this->data['path']);
        $rootId = reset($parentIds);
        
        // add name to title and tabTitle
        if (!empty($this->data['name'])) {
            $name = $this->data['name'];
        }
        // - name should not be empty, if so then it is error so use actual name
        else {
            $name = $Category->findFieldBy('EshopProductCategory.name', 'id', $this->data['id']);
        }
        App::setSeoTitle(__a(__FILE__, '&quot;%s&quot;', $name));
        $sortProductsLimit = $this->getSetting('EshopProductCategory.sortProductsLimit');
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Product category &quot;%s&quot;', $name),
            'data' => $this->data,
            'Model' => $Category,
            'lang' => $lang,
//            'nonTranslatedProcessing' => 'ignore',
            'columns' => 4,
            'showAffix' => true,
            'tabsToReloadAfterSave' => array(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_index',
                )),
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_showTree',
                )),
            ),
            'actions' => array(
                'lang' => array(
                    'url' => App::getUrl(array('locator' => '/mvc/Eshop/EshopProductCategories/admin_edit', 'args' => array($this->data['id']))),
                    'options' => array_combine(App::getPropertyLangs(), App::getPropertyLangs()),
                )
            ),
            'fields' => array(
                'id' => array('type' => 'hidden'),
                'parent_id' => array('type' => 'hidden'),
                'previous_sibling_id' => array('type' => 'hidden'),
                'path' => array('type' => 'hidden'),
                
                // BASIC PARAMETERS
                array('h1' => __a(__FILE__, 'Basic parameters')),
                array('row'),
                    array('field' => 'name', 'label' => __a(__FILE__, 'Category name')), 
                    'slug' => array('label' => __a(__FILE__, 'Category Slug')),
                    'code' => array('label' => __a(__FILE__, 'Category code')),
                    array('field' => 'active', 'label' => __a(__FILE__, 'Active')),
                array('/row'),
                array('row', 'columns' => array(3, 3, 6)),
                    'alternative_name' => array(
                        'label' => __a(__FILE__, 'Alternatívny názov'),
                        'hint' => __a(__FILE__, 'Ak je zadaný, tak sa použije prednostne (pred hodnotou v poli "Názov") v zozname produktov danej kategórie.'),
                    ),
                    'show_description' => array(
                        'label' => __a(__FILE__, 'Show description'), 
                        'hint' => __a(__FILE__, 'Show description in category products index. If yes then where?'),
                        'type' => 'select',
                        'options' => array(
                            'enum_no' => __a(__FILE__, 'no'), 
                            'enum_before_products' => __a(__FILE__, 'before products'), 
                            // 'enum_after_products' => __a(__FILE__, 'after products'), 
                            // 'enum_without_products' => __a(__FILE__, 'without products'),
                        )
                    ),
                    'product_parameters' => array(
                        'label' => __a(__FILE__, 'Parametre produktov'),
                        'hint' => __a(__FILE__, 'Čiarkou oddelený zoznam parametrov produktov aplikovateľných na produkty zaradené v tejto kategórii. Len tu zadané sa následne zobrazujú v admin formulári produktu aje možné ich filtrovať na frontende. Zoznam všetkých dostupných: <code>color</code>, <code>material_color</code>, <code>dimensions</code>, <code>diameter</code>, <code>marquee_height</code>, <code>length</code>, <code>extension</code>, <code>material</code>, <code>sail_width</code>, <code>in_series</code>, <code>carrying_capacity</code>, <code>pressure</code>, <code>power</code>, <code>electric_current</code>, <code>inner_frame</code>, <code>outer_frame</code>, <code>roof_thickness</code>, <code>inner_dimension</code>, <code>volume</code>, <code>voltage</code>, <code>capacity</code>, <code>variant</code>'),
                    ),                    
                array('/row'),
                array('row', 'columns' => 1),
                    'description' => array(
                        'label' => __a(__FILE__, 'Category description'), 
                        'type' => 'editor',
                        'options' => array(
                            'height' => '450px',
//                                'toolbar' => 'Full',
                        )
                    ),
                array('/row'),
                
                // IMAGE
                array('h1' => __a(__FILE__, 'Image')),
                array('row', 'columns' => 2),
                    array('col'),
                        array('row', 'columns' => 1),
                            array('col'),
                                array(
                                    'field' => 'image', 
                                    'label' => __a(__FILE__, 'Image'),
                                    'hint' => __a(__FILE__, 'Obrázok, ktorý sa zobrazuje v menu kategórií. Pre vrchnú úroveň kategórií nahraj ikonu tak, ako je zadaná v dizajne. Pre podkategórie nahrávaj obrázok s rozmermi %s. Pozadie by mal mať priesvitné alebo biele.', '30 x 30 px')
                                ),
                                array(
                                    'field' => 'image', 
                                    'type' => 'image',
                                    'deleteImage' => '/mvc/Eshop/EshopProductCategories/admin_deleteFile/image/' . $this->data['id'],
                                ),
                            array('/col'),
                        array('/row'),
                    array('/col'),
                array('/row'),
                
                // SEO PARAMETERS
                array(
                    'h1' => __a(__FILE__, 'SEO parameters'),
                    'hint' => __a(__FILE__, 'Parameters for search engines, e.g. Google.com, Yahoo.com or Zoznam.cz')
                ),
                array('row', 'columns' => array(3,6,3)),
                    'seo_title' => array(
                        'label' => __a(__FILE__, 'SEO title'),
                        'hint' => __a(__FILE__, 'Title is displayed in browser tab and in Google search results. It is one of most important texts for Google on your page'),
                    ),
                    'seo_description' => array(
                        'label' => __a(__FILE__, 'SEO description'), 
                        'type' => 'textarea', 
                        'style' => 'height: 34px',
                        'hint' => __a(__FILE__, 'This text is mostly used by Google in search results as text under result title. Sometimes Google decides to use some other text generated from page content'),
                    ),
                    'seo_keywords' => array(
                        'label' => __a(__FILE__, 'SEO keywords'),
                        'hint' => __a(__FILE__, 'Google does not consider much keywords. Other search engines may.')
                    ),
                array('/row'),     
                
                // OTHER PARAMETERS
                array('h1' => __a(__FILE__, 'Other parameters')),
                array('row', 'columns' => 2),
                    array('col', 'columns' => 1),
                        array('row'),
                            array(
                                'field' => 'new_previous_sibling_id', 
                                'label' => __a(__FILE__, 'Location in tree behind'),
                                'type' => 'select',
                                'options' => $Category->findTreeSelectList($rootId, array(
                                    'firstPlaceholder' => true,
                                    'prependRoot' => true,
                                    // avoid content itself and its childs
                                    'conditions' => array(
                                        'id !=' => $this->data['id'],
                                        'path !~%' => $this->data['path'] . $this->data['id'] . '-',
                                    ),
                                )),
                            ),
                        array('/row'),
                        array('row'),
                            'heureka_name' => array(
                                'label' => __a(__FILE__, 'Name for Heureka'),
                                'type' => 'text',
                                'hint' => __a(
                                    __FILE__, 
                                    'Ak je vyplnené potom sa použije namiesto názvu pri vytváraní XML súboru pre Heureka.sk, napr. <code>Heureka.sk | Oblečenie a móda | Pánske oblečenie | Pánske tričká</code> (t.j. obsah tagu <code>&lt;CATEGORY_FULLNAME&gt;</code>). Zoznam všetkých dostupných viď <a href="%s" target="_blank">tu</a>', 
                                    'https://sluzby.heureka.sk/napoveda/strom-kategorii-heureka-sk/'
                                ),
                            ),
                        array('/row'),
                        array('row'),
                            'zbozicz_name' => array(
                                'label' => __a(__FILE__, 'Názov pre Zbozi.cz'),
                                'type' => 'text',
                                'hint' => __a(__FILE__, 'Ak je vyplnené potom sa použije namiesto názvu pri vytváraní XML súboru pre Zbozi.cz. Zbozi.cz zaradi len tie produkty, ktoré majú vyplnený ich názov kategórie, napr. Počítače | Ukládání dat | Paměťové karty. Zoznam všetkých dostupných viď <a href="%s" target="_blank">tu</a>', 'https://www.zbozi.cz/static/categories.csv'),
                            ),
                        array('/row'),
                    array('/col'),
                    array('col', 'columns' => 1),
                        array('if' => $sortProductsLimit > 0),
                            array('row'),
                                array(
                                    'field' => 'sort_products', 
                                    'label' => __a(__FILE__, 'Zoraďovať produkty'),
                                    'hint' => __a(
                                        __FILE__, 
                                        'Kategórii je možné zapnúť zoraďovanie produktov, ale len ak je počet produktov zaradených v tejto kategórii menší ako nastavený hraničný počet v Eshop > Nastavenia > Zoraďovať produkty v kategórii do počtu > %s', 
                                        $sortProductsLimit
                                    )
                                ),
                            array('/row'),
                            array('if' => $this->data['sort_products'] && App::$requestMethod === 'GET'),
                                array('row'),
                                    'product_ids' => array(
                                        'label' => __a(__FILE__, 'Produkty kategórie'), 
                                        'hint' => __a(__FILE__, 'Nastavte poradie produktov v kategórii'),
                                        'type' => 'itemselector', 
                                        'options' => '/mvc/Eshop/EshopProducts/admin_list'
                                    ),
                                array('/row'),
                            array('endif'),
                        array('endif'),
                    array('/col'),
                array('/row'),
                
                // INFO
                array('h1' => __a(__FILE__, 'Info')),
                array('row'),
                    array('field' => 'id', 'label' => 'Id', 'type' => 'display'),
                    array('if' => App::getUser('Group.pid') === 'admins'),
                        array('field' => 'pid', 'label' => 'Pid'),
                    array('else'),
                        array('field' => 'pid', 'label' => 'Pid', 'type' => 'display'),
                    array('endif'),
                array('/row'),
                array('row'),
                    array(
                        'field' => 'created',
                        'label' => __a(__FILE__, 'Date of creation'), 
                        'type' => 'display',
                    ),
                    array(
                        'field' => 'modified',
                        'label' => __a(__FILE__, 'Date of modification'), 
                        'type' => 'display',
                    ),
                    array('if' => App::getUser('Group.pid') === 'admins'),
                        array(
                            'field' => 'deleted',
                            'label' => __a(__FILE__, 'Date of deletion'), 
                            'type' => 'text'
                        ),
                    array('endif'),
                array('/row'),
            )
        ));
    }
    
    public function admin_delete($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        $Category = $this->loadModel('EshopProductCategory', true);
        $Category->deleteTreeNode($id);
        App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }
    
    public function admin_deleteFile($fileField = null, $id = null) {
        if (!$fileField || !$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record fileField and/or id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/',
                'source' => App::$requestSource,
            ))));
        }
        $Category = $this->loadModel('EshopProductCategory', true);
        $fileFields = $Category->getPropertyFileFields();
        if (!isset($fileFields[$fileField])) {
            App::setErrorMessage(__a(__FILE__, 'Invalid file field'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/',
                'source' => App::$requestSource,
            ))));
        }
        $Category->save(array('id' => $id, $fileField => ''));
        App::setSuccessMessage(__a(__FILE__, 'File has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }
    
    public function admin_move($parentId = null, $id = null, $orderIndex = null) { // debug
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        // normalize parent id
        if (empty($parentId)) {
            $parentId = null;
        }
        $Category = $this->loadModel('EshopProductCategory', true);
        if (!$Category->moveTreeNode($id, $parentId, array('newOrderIndex' => $orderIndex))) {
            $Response->success = false;
            App::setErrorMessage(__a(__FILE__, 'Node move has failed'), true);
            App::debug(__a(__FILE__, 'Node move has failed')); //debug
        }
        App::setLayout('App', 'json');
        return $Response->getJson();
    }
    
    /**
     * Return data for jQuery autocomplete plugin
     * 
     * @return json string
     */
    public function admin_getAutocompleteList() {
        $EshopProductCategory = new EshopProductCategory();
        $categories = $EshopProductCategory->find(array(
            'fields' => array(
                'EshopProductCategory.id',
                'EshopProductCategory.name',
            ),
            'conditions' => array(
                'name LIKE %~%' => $_GET['term']
            ),
        ));

        $result = array();
        foreach ($categories as $category) {
            $result[] = array('label' => $category['name'], 'value' => $category['id']);
        }
        if (empty($result)) return '[""]';
        $result = json_encode($result);
        
        return $result;
    }
    
    /**
     * Creates Json encoded list
     * 
     * @param string $parentId Parent category id
     * @param string|array $ids Ids of records provided either in string separated by ";" 
     *      or as array. If specified, method returns labels of this ids.
     * 
     * @return string Json encoded data
     */
    public function admin_list($parentId = null, $ids = null) {
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        $Response->data = array();
        $EshopProductCategory = new EshopProductCategory();
        App::setLayout('App', 'json');
        
        $options = array(
            'fields' => array( 
                'EshopProductCategory.id', 
                'EshopProductCategory.name', 
                'EshopProductCategory.path', 
            ),
            'order' => array(
                'name ASC'
            )
        );
        if (isset($ids)) {
            if (!is_array($ids)) {
                $ids = explode(';', $ids);
            }
            $options['conditions'] = array(
                'id' => $ids,
            );           
        } 
        else {
            if (empty($parentId)) {
                $parentId = $EshopProductCategory->findFieldBy('id', 'pid', 'categories');
            }
            $options['conditions'] = array(
                'active' => true,
                'parent_id' => $parentId,
            );
            $cat = $EshopProductCategory->findFirstBy('id', $parentId, array(
                'fields' => array('name', 'path', 'parent_id')
            ));
            $Response->data['parentId'] = $cat['parent_id'];
            $path = explode('-', trim($cat['path'], '-'));
            array_shift($path);
            $parentCatNames = array();
            if (!empty($path)) {                
                $parentCatNames = $EshopProductCategory->findList(array(
                    'fields' => 'EshopProductCategory.name',
                    'order' => 'sort',
                    'conditions' => array(
                        'id' => $path
                    )
                ));
            }
            $Response->data['parentName'] = $cat['name'];
            if (!empty($parentCatNames)) {
                $Response->data['parentName'] = implode(' - ', $parentCatNames) . ' - ' . $cat['name'];
            }
            $Response->total = $EshopProductCategory->findCount($options);
            if ($Response->total > App::getSetting('App', 'itemselector.maxItemsToShow')) {
                return $Response->getJson();
            }
        }
        
        $cats = $EshopProductCategory->find($options);
        $list = array();
        foreach ($cats as $cat) {
            $path = explode('-', trim($cat['path'], '-'));
            array_shift($path);
            $parentCatNames = array();
            if (!empty($path)) {                
                $parentCatNames = $EshopProductCategory->findList(array(
                    'fields' => 'EshopProductCategory.name',
                    'order' => 'sort',
                    'conditions' => array(
                        'id' => $path
                    )
                ));
            }
            $list[$cat['id']] = $cat['name'];
            if (!empty($parentCatNames)) {
                $list[$cat['id']] = implode(' - ', $parentCatNames) . ' - ' . $cat['name'];
            }
        }
        $Response->data['records'] = $list;
        $Response->success = true;

        return $Response->getJson();
    }
    
    /**
     * Creates Json encoded list. 
     * 
     * Used in related products itemselector in product edit
     * 
     * @param string $keyword Keyword to filter list by name
     * @param string $ids Ids of records separated by ";". If specified, method
     * return labels of this ids
     * 
     * @return string Json encoded data
     */
    public function admin_listForItemSelector($keyword = null, $ids = null) {
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        $EshopProductCategory = new EshopProductCategory();
        App::setLayout(false);
        
        $options = array(
            'conditions' => array(
                'active' => 1
            ),
            'fields' => array( 
                'EshopProductCategory.id', 
                'EshopProductCategory.name', 
                'EshopProductCategory.path', 
            ),
            'order' => array(
                'sort ASC'
            )
        );
        if (isset($ids)) {
            if (!is_array($ids)) {
                $ids = explode(';', $ids);
            }
            $options['conditions'] = array(
                'id' => $ids,
            );           
        } 
        else {
            // see the comment in app/js/sources/libs/Itemselector.js > loadListFromAjax()
            if (isset($_REQUEST['keyword'])) {
                $keyword = $_REQUEST['keyword'];
            }
            if (isset($keyword)) {
                $options['conditions'] = array(
                    'name %~%' => $keyword
                );
            }
            $Response->total = $EshopProductCategory->findCount($options);
            if ($Response->total > App::getSetting('App', 'itemselector.maxItemsToShow')) {
                return $Response->getJson();
            }
        }
        
        $cats = $EshopProductCategory->find($options);
        $list = array();
        foreach ($cats as $cat) {
            $path = explode('-', trim($cat['path'], '-'));
            array_shift($path);
            $parentCatNames = array();
            if (!empty($path)) {                
                $parentCatNames = $EshopProductCategory->findList(array(
                    'fields' => 'EshopProductCategory.name',
                    'order' => 'sort',
                    'conditions' => array(
                        'id' => $path
                    )
                ));
            }
            $list[$cat['id']] = $cat['name'];
            if (!empty($parentCatNames)) {
                $list[$cat['id']] = implode(' - ', $parentCatNames) . ' - ' . $cat['name'];
            }
        }
        $Response->data = $list;
        $Response->success = true;

        return $Response->getJson();
    }
    
    /**
     * Perform automatic translate for translated fields of EshopProductCategory
     * Google translate API is used for this
     *
     * @param null $id - if entered, only category with this id is translated, if not, batch is processed
     * @throws Exception
     */
    public function admin_translate($id = null) {
        $EshopProductCategory = new EshopProductCategory();

        $apiUrl = $this->getSetting('googleTranslate.url');
        $sourceLang = 'cs';
        $targetLang = 'sk';
        $key = $this->getSetting('googleTranslate.key');

        if ($id === null) {

            $cats = $EshopProductCategory->find(array(
                'fields' => array(
                    'EshopProductCategory.id',
                    'EshopProductCategory.name',
                    'EshopProductCategory.description',
                    'EshopProductCategory.seo_title',
                    'EshopProductCategory.seo_description',
                    'EshopProductCategory.seo_keywords',
                    'EshopProductCategory.path',
                ),
                'conditions' => array(
                    'EshopProductCategory._slug_sk IS NULL',
                    'EshopProductCategory.id != 1',
                ),
                'limit' => 10,
                'order' => array('EshopProductCategory.sort ASC')
            ));
            foreach ($cats as $cat) {
                $params = "key=$key" .
                    "&q=" . rawurlencode($cat['name']) .
                    "&q=" . rawurlencode($cat['description']) .
                    "&q=" . rawurlencode($cat['seo_title']) .
                    "&q=" . rawurlencode($cat['seo_description']) .
                    "&q=" . rawurlencode($cat['seo_keywords']) .
                    "&source=$sourceLang&target=$targetLang";

                $resultOrig = $result = file_get_contents($apiUrl . $params);
                $result = json_decode($result, true);
                if (!isset($result['data']['translations'][0]['translatedText'])) {
                    // try without tags
                    $params = "key=$key" .
                        "&q=" . rawurlencode($cat['name']) .
                        "&q=" . rawurlencode(strip_tags($cat['description'])) .
                        "&q=" . rawurlencode($cat['seo_title']) .
                        "&q=" . rawurlencode(strip_tags($cat['seo_description'])) .
                        "&q=" . rawurlencode($cat['seo_keywords']) .
                        "&source=$sourceLang&target=$targetLang";

                    $resultOrig = $result = file_get_contents($apiUrl . $params);
                    $result = json_decode($result, true);
                }
                if (!isset($result['data']['translations'][0]['translatedText'])) {
                    return $resultOrig . '<br>' .
                        print_r($result) .
                        'No response for category id ' . $cat['id'] . '<br>' .
                        '<a href="' . $apiUrl . $params . '" target="_blank">link</a>';
                }
                // generate slug
                $path = explode('-', trim($cat['path'], '-'));
                $lastCatId = end($path);
                $slug = '';
                if ($lastCatId != 1) {
                    // get slug of category in path
                    $tmpCat = $EshopProductCategory->findFirst(array(
                        'fields' => array(
                            '_slug_sk'
                        ),
                        'conditions' => array(
                            'id' => $lastCatId
                        )
                    ));
                    $slug .= $tmpCat['_slug_sk'] . '/';
                }
                $slug .= Str::slugize($result['data']['translations'][0]['translatedText']) . '-k' . $cat['id'];
                $slug = trim($slug, '/');

                $data = array(
                    'id' => $cat['id'],
                    '_name_sk' => $result['data']['translations'][0]['translatedText'],
                    '_description_sk' => $result['data']['translations'][1]['translatedText'],
                    '_seo_title_sk' => $result['data']['translations'][2]['translatedText'],
                    '_seo_description_sk' => $result['data']['translations'][3]['translatedText'],
                    '_seo_keywords_sk' => $result['data']['translations'][4]['translatedText'],
                    '_slug_sk' => $slug
                );
                DB::update($EshopProductCategory->getPropertyTable(), $data, array(
                    'conditions' => array(
                        'id' => $cat['id']
                    )
                ));

                return '<a href="" onclick="window.location.reload();">REFRESH</a><br>' . 
                    'result:<br><pre>' . print_r($data, true) . '</pre>';
            }
        } else {
            $cat = $EshopProductCategory->findFirst(array(
                'fields' => array(
                    'EshopProductCategory.id',
                    'EshopProductCategory.name',
                    'EshopProductCategory.description',
                    'EshopProductCategory.seo_title',
                    'EshopProductCategory.seo_description',
                    'EshopProductCategory.seo_keywords',
                    'EshopProductCategory.path',
                ),
                'conditions' => array(
                    'EshopProductCategory.id' => $id,
                ),
            ));

            $params = "key=$key" .
                "&q=" . rawurlencode($cat['name']) .
                "&q=" . rawurlencode($cat['description']) .
                "&q=" . rawurlencode($cat['seo_title']) .
                "&q=" . rawurlencode($cat['seo_description']) .
                "&q=" . rawurlencode($cat['seo_keywords']) .
                "&source=$sourceLang&target=$targetLang";

            $resultOrig = $result = file_get_contents($apiUrl . $params);
            $result = json_decode($result, true);
            if (!isset($result['data']['translations'][0]['translatedText'])) {
                // try without tags
                $params = "key=$key" .
                    "&q=" . rawurlencode($cat['name']) .
                    "&q=" . rawurlencode(strip_tags($cat['description'])) .
                    "&q=" . rawurlencode($cat['seo_title']) .
                    "&q=" . rawurlencode(strip_tags($cat['seo_description'])) .
                    "&q=" . rawurlencode($cat['seo_keywords']) .
                    "&source=$sourceLang&target=$targetLang";

                $resultOrig = $result = file_get_contents($apiUrl . $params);
                $result = json_decode($result, true);
            }
            if (!isset($result['data']['translations'][0]['translatedText'])) {
                return $resultOrig . '<br>' .
                    print_r($result) .
                    'No response for category id ' . $cat['id'] . '<br>' .
                    '<a href="' . $apiUrl . $params . '" target="_blank">link</a>';
            }
            // generate slug
            $path = explode('-', trim($cat['path'], '-'));
            $slug = '';
            foreach ($path as $catId) {
                if ($catId == 1) continue;
                // get slug of category in path
                $tmpCat = $EshopProductCategory->findFirst(array(
                    'fields' => array(
                        '_slug_sk'
                    ),
                    'conditions' => array(
                        'id' => $catId
                    )
                ));
                $slug .= $tmpCat['_slug_sk'] . '/';
            }
            $slug .= Str::slugize($result['data']['translations'][0]['translatedText']) . '-k' . $cat['id'];

            $data = array(
                'id' => $cat['id'],
                '_name_sk' => $result['data']['translations'][0]['translatedText'],
                '_description_sk' => $result['data']['translations'][1]['translatedText'],
                '_seo_title_sk' => $result['data']['translations'][2]['translatedText'],
                '_seo_description_sk' => $result['data']['translations'][3]['translatedText'],
                '_seo_keywords_sk' => $result['data']['translations'][4]['translatedText'],
                '_slug_sk' => $slug
            );
            DB::update($EshopProductCategory->getPropertyTable(), $data, array(
                'conditions' => array(
                    'id' => $cat['id']
                )
            ));
            App::setSuccessMessage(__a(__FILE__, 'Category has been successfully translated.'));
        }
    }
}

