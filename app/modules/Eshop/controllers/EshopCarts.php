<?php

class EshopCarts extends Controller {
    
    public function __construct(){
        parent::__construct();
        $this->loadModel('EshopCart');
    }
    
    /**
     * View current cart content (also by anonymous user). The cart is stored in Session.
     * 
     * @return string Html of cart view
     */
    public function view() {
        $this->displayOriginComment = true;
        $Cart = $this->loadModel('EshopCart', true);
        
        // get cart products details
        $products = $Cart->getProductsDetails(array(
            'getAdditionalServiceProducts' => array(
                'publishedOnly' => true,
            ),
            'getSpecialOffers' => true,
            'synchronize' => true,
        ));
        $Cart->setAdjustedProductsAppMessages();
        //$Cart->setOffStockProductsAppConfirmationMessage(); //unused on alterego
        if (!$products) {
            App::setMessage(__(__FILE__, 'Your cart is empty'));
            App::redirect(App::getRefererUrl('/'));
        }
        // get totals
        $totals = $Cart->getPrices($products);
        // prepare url for button *back*
        $refererUrl = App::getRefererUrl('/');
        $actualUrl = App::getUrl(array('locator' => SLUG, 'absolute' => true));
        if ($refererUrl == $actualUrl) {
            $refererUrl = '/';
        }
        // check if there are any active vouchers
        $Voucher = $this->loadModel('EshopVoucher', true);
        $urlSetVoucher = null;
        $urlClearVoucher = null;
        $additionalDiscountRateLimit = null;
        $actualVoucher = null;
        if (
            $Voucher->hasActive(array(
                'productIds' => array_column($products, 'id'),
            ))
        ) {
            $urlSetVoucher = App::getUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => 'setVoucher',
            ));
            $urlClearVoucher = App::getUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => 'clearVoucher',
            ));
            $additionalDiscountRateLimit = $this->getSetting('EshopProduct.additionalDiscountRateLimit');
            /*/
            // !!! VYDAVATEL SPECIFIC
            $additionalDiscountRateLimitForVydavatel = $this->getSetting('EshopProduct.additionalDiscountRateLimitForVydavatel');
            if ($additionalDiscountRateLimitForVydavatel > $additionalDiscountRateLimit) {
                $additionalDiscountRateLimit = $additionalDiscountRateLimitForVydavatel;
            }
            /*/
            $actualVoucher = $Cart->getVoucher();
        }        
        return $this->loadView(
            'EshopCarts/view',
            array(
                'products' => $products,
                'totals' => $totals,  
                'urlBack' => App::getUrl($refererUrl),
                'urlOrderCheckout' => App::getContentUrlByPid('Eshop.EshopOrders.checkout'),
                'urlSetVoucher' => $urlSetVoucher,
                'urlClearVoucher' => $urlClearVoucher,
                'additionalDiscountRateLimit' => $additionalDiscountRateLimit,
                'actualVoucher' => $actualVoucher,
                'hasOversizedProducts' => $Cart->hasOversizedProducts(),
            )
        );

    }
    
    public function viewMini() {
        $this->displayOriginComment = true;
        $Cart = $this->loadModel('EshopCart', true);
        
        // get cart products details
        $products = $Cart->getProductsDetails();
        if (!$products) {
            //App::setMessage(__(__FILE__, 'Your cart is empty'));
            //App::redirect(App::getRefererUrl('/'));
        }
        // get totals
        $totals = $Cart->getPrices($products);

        return $this->loadView(
            'EshopCarts/viewMini',
            array(
                'products' => $products,
                'totals' => $totals,
                'slugCartView' => App::getContentLocatorByPid('Eshop.EshopCarts.view'),
                'slugProductView' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),  
                'slugOrderCheckout' => App::getContentLocatorByPid('Eshop.EshopOrders.checkout'),  
                'urlUpdateCart' => App::getUrl(array( // submit all cart form here
                    'module' => $this->module,
                    'controller' => 'EshopCarts',
                    'action' => 'updateProduct',
                )),
                'urlClearCart' => App::getUrl(array(  // link 
                    'module' => $this->module,
                    'controller' => 'EshopCarts',
                    'action' => 'clear',
                )),
                'urlRemoveFromCart' => App::getUrl(array(  // link, bulid like urlRemoveFromCart/$product['cartIndex']
                    'module' => $this->module,
                    'controller' => 'EshopCarts',
                    'action' => 'removeProduct',
                ))
            )
        );
    }
    
    /**
     * Adds product(s) to cart. After the job is done, the app is redirected to given
     * redirect url.
     * 
     * A single product data can be provided by action args. 
     * Many products data are retrieved from EshopCarts::$data.
     * 
     * @param int $id Id of single product to be added to cart.
     * @param int $amount Optional. amount of a single product to be added to the cart. Defaults to 1.
     * @param string $redirectUrl Optional. If provided then the app is redirected
     *      to this url after the product is added to cart. It can be provided also 
     *      as $_GET['redirectUrl'] for case that it contains an entire url path 
     *      which cannot be attached as URL argument. If not provided, then
     *      EshopCart::getRedirectAfterAddProduct() is checked to find redirect url.
     *      And even if this is empty then the app is redirected to actual referer.
     * @param bool|array $_GET['synchronize'] If is set then passed as synchronize 
     *      option to EshopCart::addProducts()
     */
    public function addProducts($id = null, $amount = 1, $redirectUrl = null) {
        // retrieve products data
        if ($this->data) {
            $products = $this->data;
            // if single product is provided then normalize it
            if (isset($products['id']) && isset($products['amount'])) {
                $products = array($products);
            }
        }
        else {
            $products = array(array('id' => $id, 'amount' => $amount));
        }
        $options = array();
        if (isset($_GET['synchronize'])) {
            $options['synchronize'] = $_GET['synchronize'];
        }
        
        $Cart = null;
        try {
            // add to cart and validate
            $Cart = $this->loadModel('EshopCart', true);
            $oldCartProducts = $Cart->getProducts();
            $result = $Cart->addProducts($products, $options);
            if ($result === false) {
                $message = __(
                    __FILE__, 
                    'Kontaktujte nás prosím. Pridanie produktu do košíka zlyhalo s nasledovnou chybou: %s', 
                    $Cart->getError()
                );
                App::setMessage($message, array('modal' => true));
                App::logError($message, array(
                    'email' => true,
                ));
                App::redirect(App::getRefererUrl('/'));
            }
        }
        catch (Throwable $e) {
            $message = __(
                __FILE__, 
                'Kontaktujte nás prosím. Pridanie produktu do košíka zlyhalo s nasledovnou chybou: %s', 
                $e->getMessage()
            );
            App::setMessage($message, array('modal' => true));
            App::logError($message, array(
                'var' => $e,
                'email' => true,
            ));
            App::redirect(App::getRefererUrl('/'));
        }
        
        $presaleProducts = $Cart->getPresaleProducts();
        if (
            is_array($result)
            || $presaleProducts 
        ) {
            $Cart->setAdjustedProductsAppMessages(array(
                'on' => 'add',
            ));
            $Cart->setPresaleProductsAppMessages(array(
                'on' => 'add',
            ));
        }
        else {
            if (count($products) === 1) {
                App::setMessage(__(__FILE__, 'The product has been added to your cart.'));
            }
            else {
                App::setMessage(__(__FILE__, 'Products have been added to your cart.'));
            }
        }
        
        $Cart->getFacebookAddCode($oldCartProducts, $Cart->getProducts());
        
        // find redirect url
        if (
            $redirectUrl
            || ($redirectUrl = Sanitize::value($_GET['redirectUrl']))
            || ($redirectUrl = $Cart->getRedirectAfterAddProduct())
        ) {
            $redirectUrl = App::getUrl($redirectUrl);
        }
        else {
            $redirectUrl = App::getRefererUrl('/');
        }
        // remove stored redirect url
        $Cart->clearRedirectAfterAddProduct();
        // redirect
        App::redirect($redirectUrl);
    }
    
    /**
     * Adds child product for specified product in cart. After the job is done, 
     * the app is redirected to given redirect url.
     * 
     * The difference between child items and normal items in cart is that if the 
     * parent item is removed from cart then all its child items are removed too.
     * 
     * @param int $parentIndex Index of product in the cart to add child product for
     * @param int $childProductId Id of product to be added to cart as a child product
     *      for product specified by $cartIndex.
     * @param string $redirectUrl Optional. If provided then the app is redirected
     *      to this url after the product is added to cart. It can be provided also 
     *      as $_GET['redirectUrl'] for case that it contains an entire url path 
     *      which cannot be attached as URL argument. If not provided, then
     *      EshopCart::getRedirectAfterAddProduct() is checked to find redirect url.
     *      And even if this is empty then the app is redirected to actual referer.
     * @param bool|array $_GET['synchronize'] If is set then passed as synchronize 
     *      option to EshopCart::addProducts()
     */
    public function addChildProduct($parentIndex = null, $childProductId = null, $redirectUrl = null) {
        $options = array();
        if (isset($_GET['synchronize'])) {
            $options['synchronize'] = $_GET['synchronize'];
        }
        
        $Cart = null;
        try {
            // add to cart and validate
            $Cart = $this->loadModel('EshopCart', true);
            $oldCartProducts = $Cart->getProducts();
            $result = $Cart->addChildProduct($parentIndex, $childProductId, $options);
            if ($result === false) {
                $message = __(
                    __FILE__, 
                    'Kontaktujte nás prosím. Pridanie produktu do košíka zlyhalo s nasledovnou chybou: %s', 
                    $Cart->getError()
                );
                App::setMessage($message, array('modal' => true));
                App::logError($message, array(
                    'email' => true,
                ));
                App::redirect(App::getRefererUrl('/'));
            }
        }
        catch (Throwable $e) {
            $message = __(
                __FILE__, 
                'Kontaktujte nás prosím. Pridanie produktu do košíka zlyhalo s nasledovnou chybou: %s', 
                $e->getMessage()
            );
            App::setMessage($message, array('modal' => true));
            App::logError($message, array(
                'var' => $e,
                'email' => true,
            ));
            App::redirect(App::getRefererUrl('/'));
        }
        
        if (is_array($result)) {
            $Cart->setAdjustedProductsAppMessages(array(
                'on' => 'add',
            ));
        }
        else {
            App::setMessage(__(__FILE__, 'The product has been added to your cart.'));
        }
        
        $Cart->getFacebookAddCode($oldCartProducts, $Cart->getProducts());
        
        // find redirect url
        if (
            $redirectUrl
            || ($redirectUrl = Sanitize::value($_GET['redirectUrl']))
            || ($redirectUrl = $Cart->getRedirectAfterAddProduct())
        ) {
            $redirectUrl = App::getUrl($redirectUrl);
        }
        else {
            $redirectUrl = App::getRefererUrl('/');
        }
        // remove stored redirect url
        $Cart->clearRedirectAfterAddProduct();
        // redirect
        App::redirect($redirectUrl);        
    }
    
    /**
     * Removes specified product from the cart. After the job is done, the app is 
     * redirected to referer url.
     * 
     * Additional product data (e.g. static and dynamic attributes) are looked for in
     * submited EshopCarts::$data.
     * 
     * @param int $cartIndex Index of product in the cart.
     */
    public function removeProduct($cartIndex = 0) {        
        // remove from cart and validate
        $this->loadModel('EshopCart');
        $Cart = new EshopCart();
        if (!$Cart->removeProduct($cartIndex)) {
            App::setMessage($Cart->getError(), array('modal' => true));
        }
        elseif ($Cart->getAdjustedProducts()) {
            $Cart->setAdjustedProductsAppMessages();
        }
        else {
            App::setMessage(__(__FILE__, 'The product has been removed from your cart.'));
        }
        // redirect back
        if ($Cart->getProducts()) {
            App::redirect(App::getRefererUrl('/'));
        }
        App::setMessage(__(__FILE__, 'Your cart is empty'));
        App::redirect('/');
    }
    
    /**
     * Updates the content of the current cart using the submitted form data.
     * After the job is done, the app is redirected to referer url.
     *
     * The data should be submitted in the form of:
     * 
     *      array(
     *          0 => array(
     *              'id' => 34,
     *              'amount' => 1,
     *              'cartIndex' => 0,
     *          ),
     *          1 => array(
     *              'id' => 22,
     *              'amount' => 5
     *              'cartIndex' => 1,
     *              // optionally static and/or dynamic attributes
     *              'static_attributes' => array(
     *                  'my_attr' => 'my_val', 
     *                  ...
     *              ),
     *              'dynamic_attributes' => array(
     *                  'my_attr' => 'my_val', 
     *                  ...
     *              ),
     *          ),
     *          ...
     *      )
     */
    public function updateProducts() {
        $data = $this->data;
        unset($data['_target']);
        if (empty($data)) {
            // nothing to change, go back to the referer
            App::redirect(App::getRefererUrl('/'));
        }
        $Cart = $this->loadModel('EshopCart', true);
        $oldCartProducts = $Cart->getProducts();
        $result = $Cart->updateProducts($data);
        if ($result === false) {
            App::setMessage($Cart->getError(), array('modal' => true));
        }
        elseif (is_array($result)) {
            $Cart->setAdjustedProductsAppMessages();
        }
        else {
            App::setMessage(__(__FILE__, 'Your cart has been updated.'));
        }
        
        $Cart->getFacebookAddCode($oldCartProducts, $Cart->getProducts());
        
        App::redirect(App::getRefererUrl('/'));
    }
    
    /**
     * Empties the cart. After the job is done, the app is redirected to given
     * redirect url.
     * 
     * @param string $redirectUrl Optional. If provided then the app is redirected
     *      to this url after the cart is emptied. It can be provided also 
     *      as $_GET['redirectUrl'] for case that it contains an entire url path 
     *      which cannot be attached as URL argument. If not provided, then the app 
     *      is redirected to actual referer.
     */
    public function clearProducts($redirectUrl = null) {
        $Cart = $this->loadModel('EshopCart', true);
        $Cart->clearProducts();
        
        App::setMessage(__(__FILE__, 'Your cart is empty'));
        
        // get redirect url
        if (
            $redirectUrl
            || ($redirectUrl = Sanitize::value($_GET['redirectUrl']))
        ) {
            $redirectUrl = App::getUrl($redirectUrl);
        }
        else {
            $redirectUrl = App::getRefererUrl('/');
        }
        // redirect
        App::redirect($redirectUrl);
    }
    
    /**
     * Sets voucher code
     * 
     * @param string $voucherCode Voucher code to be set. Can be provided also as 
     *      $this->data['voucher_code']
     */
    public function setVoucher($voucherCode = null) {
        if (!empty($this->data['voucher_code'])) {
            $voucherCode = $this->data['voucher_code'];
        }
        $Cart = $this->loadModel('EshopCart', true);
        $result = $Cart->setVoucher($voucherCode);
        if ($result === false) {
            App::setMessage($Cart->getError());
        }
        else {
            App::setMessage(__(__FILE__, 'Voucher code has been applied'));
        }
        App::redirect(App::getRefererUrl('/'));
    }
    
    
    /**
     * Clears voucher (if any)
     */
    public function clearVoucher() {
        $Cart = $this->loadModel('EshopCart', true);
        $Cart->clearVoucher();
        App::setMessage(__(__FILE__, 'Voucher code has been removed'));
        App::redirect(App::getRefererUrl('/'));
    }        
}