<?php

class EshopShipmentMethods extends Controller {
    
    public function admin_index() {
        $ShipmentMethod = $this->loadModel('EshopShipmentMethod', true);
        
        $records = $ShipmentMethod->find(array(
            'fields' => '*',
            'order' => 'sort ASC',
            'paginate' => true,
        ));
        
        App::setSeoTitle(__a(__FILE__, 'Shipment methods'));
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'Shipment methods'),
            'records' => $records,
            'primaryKey' => 'id',
            'columns' => array(
                'name' => __a(__FILE__, 'Name'),
                'price' => __a(__FILE__, 'Price'),
                'delivery_time' => __a(__FILE__, 'Delivery time'),
                'delivery_country' => __a(__FILE__, 'Delivery country'),
                'free_shipment_allowed' => __a(__FILE__, 'Free shipment allowed'),
            ),
            'renderFields' => array(
                'active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'free_shipment_allowed' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('active' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                )
            ),
            'Paginator' => $ShipmentMethod->Paginator,
            'actions' => array(                
                'add' => array(
                    'url' => '/mvc/Eshop/EshopShipmentMethods/admin_add',
                ),
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => '/mvc/Eshop/EshopShipmentMethods/admin_edit',
                ),
                'delete' => array(
                    'url' => '/mvc/Eshop/EshopShipmentMethods/admin_delete',
                    'confirmMessage' => __a(__FILE__, 'Please, confirm removal of item :name:'),
                ),
                'move' => array(
                    'url' => '/mvc/Eshop/EshopShipmentMethods/admin_move/',
                ),
            )
        ));
    }
    
    public function admin_add() {
        $ShipmentMethod = $this->loadModel('EshopShipmentMethod', true);
        
        if ($this->data) {
            if ($ShipmentMethod->addOrderedItem($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'New record has been succesfully created'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                    'args' => array($ShipmentMethod->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        
        App::setSeoTitle(__a(__FILE__, 'Add new shipment method'));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Add new shipment method'),
            'data' => $this->data,
            'Model' => $ShipmentMethod,
            'lang' => DEFAULT_LANG,
            'columns' => 4,
            'actions' => array(
                'lang' => array(
                    'options' => array_combine(array(DEFAULT_LANG), array(DEFAULT_LANG)),
                )
            ),
            'fields' => array(
                array(
                    'field' => 'name', 
                    'label' => __a(__FILE__, 'Name'),
                    'autofocus' => true,
                ),
            )
        ));
    }  
    
    public function admin_edit($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        
        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        $ShipmentMethod = $this->loadModel('EshopShipmentMethod', true);
        
        if ($this->data) {
            if ($ShipmentMethod->saveAll($this->data, array('lang' => $lang))) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => $this->action,
                    'args' => array($id),
                    'source' => App::$requestSource,
                    'inherit' => array('get' => array('lang'))
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        else {
            $this->data = $ShipmentMethod->findAll(array(
                'conditions' => array('id' => $id),
                'lang' => $lang,
                'first' => true,
            ));
            // if product does not exist then redirect to referer
            if (empty($this->data)) {
                App::setErrorMessage(__a(__FILE__, 'Invalid record id %s', (string)$id));
                App::redirect(App::getRefererUrl(App::getUrl(array(
                    'locator' => '/_error',
                    'source' => App::$requestSource,
                ))));
            }
            // get file fields real path
            $fileFields = array_keys($ShipmentMethod->getPropertyFileFields());
            foreach($fileFields as $fileField) {
                if (!empty($this->data[$fileField])) {
                    $this->data[$fileField] = $ShipmentMethod->getFileFieldUrlPath($fileField, array('file' => $this->data[$fileField]));
                }
            }
        }
        
        if (
            empty($this->data['heureka_code'])
            && (
                $this->getSetting('EshopOrder.heurekaVerifiedByClientsKey')
                || $this->getSetting('EshopOrder.heurekaConversionKey')
            )
        ) {
            $message = __a(__FILE__, 'Ak "Kód na heureka.sk" nie je zadaný, tak sa tento spôsob doručenia nebude zobrazovať na heureka.sk');
            $ShipmentMethod->setWarning('heureka_code', $message);
            App::setWarningMessage($message);
        }
        
        // add name to title and tabTitle
        if (!empty($this->data['name'])) {
            $name = $this->data['name'];
        }
        // name should not be empty, if so then it is error so use actual name
        else {
            $name = $ShipmentMethod->findFieldBy('EshopShipmentMethod.name', 'id', $this->data['id']);
        }
        App::loadModel('App', 'Country');
        $Country = new Country();
        App::setSeoTitle(__a(__FILE__, '&quot;%s&quot;', $name));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Edit shipment method &quot;%s&quot;', $name),
            'data' => $this->data,
            'Model' => $ShipmentMethod,
            'lang' => $lang,
//            'nonTranslatedProcessing' => 'ignore',
            'columns' => 4,
            'showAffix' => true,
            'actions' => array(
                'lang' => true
            ),
            'fields' => array(
                'id' => array('type' => 'hidden'),
                
                // BASIC PARAMETERS
                array('h1' => __a(__FILE__, 'Basic parameters')),
                array('row'),
                    array(
                        'field' => 'name', 
                        'label' => __a(__FILE__, 'Shipment method name')
                    ), 
                    array(
                        'field' => 'description',
                        'label' => __a(__FILE__, 'Shipment method description'), 
                        'type' => 'textarea', 
                        'style' => 'height: 34px',
                    ),
                    array(
                        'field' => 'delivery_time',
                        'label' => __a(__FILE__, 'Shipment method delivery time'), 
                        'hint' => __a(__FILE__, 'Čas doručenia zásielky v dňoch'), 
                    ),
                    array(
                        'field' => 'active', 
                        'label' => __a(__FILE__, 'Active')
                    ),
                array('/row'),
                array('row'),
                    array(
                        'field' => 'delivery_country', 
                        'label' => __a(__FILE__, 'Delivery country'),
                        'hint' => __a(__FILE__, 'Delivery country the shipment method is used for. If not provided then default behaviour is applied'),
                        'type' => 'select',
                        'options' => $Country->getSelectList(),
                        'empty' => true,
                    ), 
                array('/row'),
                
                // PRICE
                array('row'),
                    array(
                        'field' => 'price', 
                        'label' => __a(__FILE__, 'Shipment method price'),
                        'hint' => __a(__FILE__, 'Let this field empty if there should be displayed no info about price in order checkout (nor "Price is 0,- €") and price will be set manually at order processing')
                    ),
                    array(
                        'field' => 'products_total_price_alternatives', 
                        'label' => __a(__FILE__, 'Price alternatives according to products total price'),
                        'hint' => __a(__FILE__, 'Enter price changes according to increasing total of products. E.g. <code>20:80%;30:50%;100:0</code> or <code>20:1.5;30:1;100:0</code> or <code>20:-0.5;30:-1.5;100:0</code>'),
                        'type' => 'textarea', 
                        'style' => 'height: 34px',
                    ),
                    array(
                        'field' => 'package_weight_price_alternatives', 
                        'label' => __a(__FILE__, 'Price alternatives according to package weight'),
                        'hint' => __a(__FILE__, 'Enter price changes according to increasing package weight. E.g. <code>2:120%;2.5:150%</code> or <code>2:2;2.5:3</code> or <code>2:+1;3:+1.5</code>'),
                        'type' => 'textarea', 
                        'style' => 'height: 34px',
                    ),
                    array(
                        'field' => 'free_shipment_allowed', 
                        'label' => __a(__FILE__, 'Free shipment allowed'),
                        'hint' => __a(__FILE__, 'Check this if you would like the setting "Free shipment products total" to be applicable to this shipment method? On other side, this has no influence on price alternatives according to products total price or according to package weight even if they will result to zero price.')
                    ),
                array('/row'),
                
                // PAYMENT METHODS
                array('row', 'columns' => 1),
                    array(
                        'field' => 'payment_ids',
                        //'label' => __a(__FILE__, 'Payment methods'), 
                        'type' => 'itemselector', 
                        'options' => '/mvc/Payment/PaymentMethods/admin_getSelectorItems',
                    ),
                array('/row'),         
                
                // OTHER PARAMS
                array('h1' => __a(__FILE__, 'Other params')),
                array('row', 'columns' => 2),
                    array('col'),
                        array('row', 'columns' => 2),
                            array(
                                'field' => 'heureka_code',
                                'label' => __a(__FILE__, 'Shipment method heureka.sk code'), 
                                'hint' => __a(__FILE__, 'Shipment method code used in XML export file for heureka.sk. Find all available codes <a href="http://sluzby.heureka.sk/napoveda/xml-feed/#DELIVERY" target="_blank">here</a>.'),
                            ),
                            array(
                                'field' => 'info_locator',
                                'label' => __a(__FILE__, 'Shipment method info page url'), 
                            ),
                        array('/row'),
                        array('row', 'columns' => 1),
                            array(
                                'field' => 'info',
                                'label' => __a(__FILE__, 'Info link hint'), 
                                'style' => 'height: 34px;'
                            ),
                        array('/row'),
                    array('/col'),
                    array('col'),
                        array(
                            'field' => 'pickup_places',
                            'label' => __a(__FILE__, 'List of pickup places'), 
                            'hint' => __a(__FILE__, 'Pickup places are separated by new lines, it means each one is on new row'), 
                            'type' => 'textarea', 
                            'style' => 'height: 109px;'
                        ),
                    array('/col'),
                array('/row'),
                
                // INFO
                array('h1' => __a(__FILE__, 'Info')),
                array('row'),
                    array('field' => 'id', 'label' => 'Id', 'type' => 'display'),
                    array('if' => App::getUser('Group.pid') === 'admins'),
                        array('field' => 'pid', 'label' => 'Pid'),
                    array('else'),
                        array('field' => 'pid', 'label' => 'Pid', 'type' => 'display'),
                    array('endif'),
                array('/row'),
                array('row'),
                    array(
                        'field' => 'created',
                        'label' => __a(__FILE__, 'Date of creation'), 
                        'type' => 'display',
                    ),
                    array(
                        'field' => 'modified',
                        'label' => __a(__FILE__, 'Date of modification'), 
                        'type' => 'display',
                    ),
                    array('if' => App::getUser('Group.pid') === 'admins'),
                        array(
                            'field' => 'deleted',
                            'label' => __a(__FILE__, 'Date of deletion'), 
                            'type' => 'text'
                        ),
                    array('endif'),
                array('/row'),
            )        
        ));
    }    
    
    public function admin_delete($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        $ShipmentMethod = $this->loadModel('EshopShipmentMethod', true);
        $ShipmentMethod->deleteBy('id', $id);
        App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'module' => $this->module,
            'controller' => $this->name,
            'action' => 'admin_index',
            'source' => App::$requestSource,
        ))));
    }       
    
    public function admin_move($id = null, $orderIndex = null) { 
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        $ShipmentMethod = $this->loadModel('EshopShipmentMethod', true);
        if (!$ShipmentMethod->moveOrderedItem($id, array('newOrderIndex' => $orderIndex))) {
            $Response->success = false;
            App::setErrorMessage(__a(__FILE__, 'Node move has failed'), true);
        }
        App::setLayout('App', 'json');
        return $Response->getJson();
    }
    
    /**
     * @cron Launch once per day for each provider to update list of his available pickup places
     * 
     * @param string $provider Pickup places provide. One of 'zasielkovna', 'geisPoint'.
     */
    public function loadPickupPlaces($provider = null) {
        App::setLayout(false);
        $ShipmentMethod = $this->loadModel('EshopShipmentMethod', true);
        $provider = strtolower($provider);
        $ShipmentMethod->getPickupPlaces($provider, array(
            // initial countries to be loaded
            'country' => array(
                'SK',
                'CZ',
            ),
            'reload' => true,
        ));
    }
}

