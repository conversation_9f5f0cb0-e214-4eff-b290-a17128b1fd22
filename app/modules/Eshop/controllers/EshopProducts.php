<?php

class EshopProducts extends SmartController {
    
    public function __construct(){
        parent::__construct();
        $this->loadModel('EshopProduct');
    }
    
    /**
     * Indexes products accordint to filter and display options contained in $this->params. 
     * 
     * Following filter options can be used - for complete list see EshopProduct::getFilterFindOptions():
     *      - 'filter' (string) Name of property for dynamic filtering of products. 
     *          Possible values are: 'author', 'manufacturer, 'range', 'type', 
     *          'category', 'group', 'wishlist'. Defaults to NULL.
     *      - 'filter_author', 'filter_manufacturer', 'filter_range', 'filter_type',
     *          'filter_category', 'filter_group', 'filter_wishlist' (int|string)
     *          plus 'filter_product' (int|array)
     *          Static filtering by fix id (int) or slug (string). All default to NULL.
     *      - 'sort' (string) Product table field to sort products by. Defaults to NULL.
     *      - 'direction' (string) Product table field to sort products by.
     *      - 'sort_random' (bool) If TRUE then an random ordering is set. Defaults to FALSE.
     *      - 'limit' (int) Explicit limit of products to display. This can also change
     *          default pagination limit if option 'paginate' is TRUE. Defaults to NULL.
     *      - 'set_seo' (bool) If TRUE then seo title, keywords and description are set 
     *          according to provided filter options. Defaults to FALSE.
     * 
     * Following display options can be used:
     *      - 'view' (string) Name of view under views/eshopProducts to be used. Defaults to 'index'.
     *      - 'title' (string) Title of index view. Defaults to NULL.
     *      - 'class' (string) Additional toplevel container CSS class. Defaults to NULL.
     *      - 'indexType' (string) Possible values are NULL (default index) and 'detailed'. Defaults to NULL.
     *      - 'emptyIndexMessage' (bool|string) Message to be displayed if the index is empty. 
     *          If FALSE the no message is generated. If TRUE then default message is used. 
     *          Defaults to TRUE. 
     *      - 'paginate' (bool) If TRUE then pagination params from URL are parsed
     *          and paginator links are displayed. Defaults to FALSE.
     *      - 'columns' (integer) Number of columns (products in row). If 0 then 
     *          product blocks are created without row wrappers.
     *      - 'showSerialLabel' (bool) Should be serial labels displayed (1., 2., 3., ...).
     *          Used e.g. for TOP10 index. If TRUE then sale labels are omitted.
     *          Defaults to FALSE.
     * 
     * @param (int|string) $filterId Id or slug of filtered entity. This is set as 'filter_id' 
     *      option of filter.
     * 
     * @return string Html of index view
     */
    public function index($filterId = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'view' => 'index',
            'title' => null,
            'titleTag' => 'h1',
            'emptyIndexMessage' => true,
            'paginate' => true,
            'infiniteScroll' => true,
            'recordsView' => 'indexRecords',
            'columns' => 0,
            'class' => null,
            // cookie epit is set by view EshopProducts/indexTypeSelect
            'indexType' => Sanitize::value($_COOKIE['epit']),
            'imageVariant' => 'small',
            'limitImageHeight' => true,
            'showDisponibility' => true,
            'showAddToCartButton' => true,
            'showSortSelect' => true,
            'showFilterSelect' => true,
            'showIndexTypeSelect' => false,
            'showSerialLabel' => false,
            'category_own_products' => $this->getSetting('EshopProduct.indexCategoryOwnProducts'),
            'showMoreButtonLabel' => null, // used only in indexSlider
            'showMoreButtonUrl' => null, // used only in indexSlider
            'showTotalCount' => false, // used only in indexSlider
            'sliderResponsiveOptions' => array(), // used only in indexSlider
            'sortTop' => true,
        );
        $inputOptions = Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        ));
        $options = array_merge($defaults, $inputOptions);
        if (
            $options['indexType'] === 'detailed' 
            && !isset($inputOptions['showDisponibility'])
        ) {
            $options['showDisponibility'] = true;
        }
        $options['filter_id'] = $filterId;
        
        $findOptions = array(
            'paginate' => $options['paginate'],
            'limit' => $this->getSetting('EshopProduct.pagingLimit'),
            'normalizeFindOptions' => true,
        );
        
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $Category = $this->loadModel('EshopProductCategory', true);
        try {
            $products = $Product->filter(
                $options, 
                // find options are passed by reference and updated by the method 
                $findOptions, 
                array(
                    'getManufacturer' => true,
                    'getAuthors' => true,
                    'getSpecialOffers' => true,
                )
            );
        } 
        catch (Exception_DB_QueryError $e) {
            App::logError(__e(__FILE__, 'Products index query has failed with error: %s', $e->getMessage()), array(
                'var' => $e,
                'email' => true,
            )); 
            return App::loadScreen('_404');
        }
        if ($products === false) {
            return App::loadScreen('_404');
        }
        
        App::loadLib('App', 'SmartAdminLauncher');
        
        $content = $Product->getFilterContent($options);
        $smartAdminLauncherContentRecordAttribute = null;
        if (!empty($content['model'])) {
            if ($content['model'] === 'EshopProductCategory') {
                if (empty($options['title'])) {
                    $options['title'] = $content['name'];
                }
                $Launcher = new SmartAdminLauncher(
                    App::getUrl(array(
                        'module' => $this->module,
                        'controller' => 'EshopProductCategories',
                        'action' => 'admin_edit',
                    )),
                    array(
                        'triggerTitle' => __a(__FILE__, 'Edit category'),
                    )
                );
                $smartAdminLauncherContentRecordAttribute = $Launcher->markRecord($content['id']);
            }
            elseif ($content['model'] === 'EshopProductGroup') {
                $Launcher = new SmartAdminLauncher(
                    App::getUrl(array(
                        'module' => $this->module,
                        'controller' => 'EshopProductGroups',
                        'action' => 'admin_edit',
                    )),
                    array(
                        'triggerTitle' => __a(__FILE__, 'Edit product group'),
                    )
                );
                $smartAdminLauncherContentRecordAttribute = $Launcher->markRecord($content['id']);
            }
        }
        
        // list of subcategories
        $subCategories = null;
        $categoryId = null;
        if(
            isset($filterId)
            && SLUG === App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory')
        ){
            $this->loadModel('EshopProductCategory');
            $EshopProductCategory = new EshopProductCategory();
            $categoryId = $EshopProductCategory->findFirst(array(
                'fields' => array(
                    'EshopProductCategory.id',
                    'EshopProductCategory.slug',
                    'EshopProductCategory.name',
                ),
                'conditions' => array( 
                    'EshopProductCategory.slug' => $filterId,
                ),
            ));
            $subCategories = $EshopProductCategory->find(array(
                'fields' => array(
                    'EshopProductCategory.id',
                    'EshopProductCategory.slug',
                    'EshopProductCategory.name',
                    'EshopProductCategory.image',
                ),
                'conditions' => array( 
                    'EshopProductCategory.parent_id' => $categoryId['id'],
                    'EshopProductCategory.active' => true,
                ),
                'order' => 'EshopProductCategory.sort ASC',
            ));
            
            foreach ($subCategories as &$subCategory) {

                if ($subCategory['image']) {
                    $subCategory['image'] = $Category->getFileFieldUrlPath('image', array(
                        'file' => $subCategory['image'],
                    ));
                }
                else {
                    unset($subCategory['image']);
                }
            }
            unset($subCategory);
            
        }
        
        // load view
        $viewParams = array(
            'title' => $options['title'],
            'titleTag' => $options['titleTag'],
            'emptyIndexMessage' => $options['emptyIndexMessage'],
            'columns' => $options['columns'],
            'class' => $options['class'],
            'indexType' => $options['indexType'],
            'imageVariant' => $options['imageVariant'],
            'limitImageHeight' => $options['limitImageHeight'],
            'showDisponibility' => $options['showDisponibility'],
            'showAddToCartButton' => $options['showAddToCartButton'],
            'showSortSelect' => $options['showSortSelect'],
            'showFilterSelect' => $options['showFilterSelect'],
            'showIndexTypeSelect' => $options['showIndexTypeSelect'],
            'showSerialLabel' => $options['showSerialLabel'],
            'showMoreButtonLabel' => $options['showMoreButtonLabel'],
            'showMoreButtonUrl' => $options['showMoreButtonUrl'],
            'sliderResponsiveOptions' => $options['sliderResponsiveOptions'],
            'products' => $products,
            'categoryId' => $categoryId,
            'subCategories' => $subCategories,
            'slugProductView' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),  
            'slugAuthorProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor'),  
            'slugManufacturerProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer'),
            'slugSpecialOfferView' => App::getContentLocatorByPid('Eshop.EshopSpecialOffers.view'),
            'Paginator' => $Product->Paginator,
            'content' => $content,
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit product'),
                )
            ),
            'smartAdminLauncherContentRecordAttribute' => $smartAdminLauncherContentRecordAttribute,
        );
        $options['recordsView'] = 'EshopProducts/' . $options['recordsView'];
        if (!empty($_REQUEST['recordsOnly'])) {
            $this->forceExclusiveDirectOutput();
            if (empty($products)) {
                return '';
            }
            else {
                return $this->loadView(
                    $options['recordsView'],
                    $viewParams
                );
            }
        }
        $options['view'] = 'EshopProducts/' . $options['view'];
        $viewParams['recordsView'] = $options['recordsView'];
        $viewParams['infiniteScroll'] = $options['infiniteScroll'];
        return $this->loadView(
            $options['view'],
            $viewParams
        );
    }
    
    // replace index by indexWithVue
    public function loadAction($action, $params, $data, $args, $allowOriginComments = true) { //debug
        //$action = ($action === 'index') ? 'indexWithVue' : $action; //debug
        return parent::loadAction($action, $params, $data, $args, $allowOriginComments); //debug
    } //debug
    
    public function indexWithVue($filterId = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'view' => 'indexWithVue',
            'title' => null,
            'titleTag' => 'h1',
            'emptyIndexMessage' => true,
            'paginate' => true,
            'columns' => 3,
            'showDisponibility' => false,
            'showAddToCartButton' => false,
            'showSortSelect' => true,
            'showFilterSelect' => true,
            'category_own_products' => $this->getSetting('EshopProduct.indexCategoryOwnProducts'),
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        $options['filter_id'] = $filterId;
        
        // load view
        $options['view'] = 'EshopProducts/' . $options['view'];
        $viewOptions = array_diff_key(
            array_merge($options, array(
                'slugProductView' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),  
                'slugAuthorProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor'),  
                'slugManufacturerProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer'),
            )), 
            array(
                'view' => null,
            )
        );
        return $this->loadView($options['view'], $viewOptions);
    }
    
    public function findDetailed() {
        $defaults = array(
            'paginate' => true,
            'limit' => $this->getSetting('EshopProduct.pagingLimit'),
        );
        $options = $this->getRequestFindOptions();
        $options = array_merge($defaults, $options);
        
        $findOptions = array(
            'paginate' => $options['paginate'],
            'limit' => $options['limit'],
        );
        
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        try {
            $products = $Product->filter(
                $options, 
                // find options are passed by reference and updated by the method 
                $findOptions, 
                array(
                    'getManufacturer' => true,
                )
            );
        } 
        catch (Exception_DB_QueryError $e) {
            App::logError(__e(__FILE__, 'Products index query has failed with error: %s', $e->getMessage()), array(
                'var' => $e,
                'email' => true,
            ));
            $products = false; //@todo 404 - not found
        }
        if ($products === false) {
            //@todo 404 - not found
            // porozmýšľaj ako obslúžiť stav 404, stačí preposlať FALSE ako hodnotu AjaxResponse->data ?
            $products = false; 
        }
        else {
            $products = array_values($products);
        }
        $this->viewOptions['data'] = $products;
        return parent::find(array('first' => false));
    }
    
    public function indexRelated($productId) {
        $this->displayOriginComment = true;
        $defaults = array(
            'view' => 'index',
            'recordsView' => 'indexRecords',
            'title' => null,
            'titleTag' => 'h1',
            'emptyIndexMessage' => true,
            'columns' => 0,
            'imageVariant' => 'small',
            'limitImageHeight' => true,
            'showDisponibility' => true,
            'showAddToCartButton' => true,
            'limit' => 3 * $this->getSetting('EshopProduct.pagingLimit'),
        );
        $params = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $relatedProducts = $Product->getRelatedProducts($productId, array(
            'conditions' => array(
                EshopProduct::getPublishedConditions(),
                EshopProduct::getNormalProductsConditions(),
                'EshopProduct.unavailable' => false
            ),
            'limit' => $params['limit'],
        ));
        
        $relatedProductIds = array_keys($relatedProducts);

        $products = $Product->getDetails($relatedProductIds, array(
            'getManufacturer' => true,
            'getAuthors' => true,
            'getSpecialOffers' => true,
        ));
        
        App::loadLib('App', 'SmartAdminLauncher');
                        
        // load view
        $params['view'] = 'EshopProducts/' . $params['view'];
        return $this->loadView(
            $params['view'],
            array(
                'recordsView' => 'EshopProducts/' . $params['recordsView'],
                'title' => $params['title'],
                'titleTag' => $params['titleTag'],
                'emptyIndexMessage' => $params['emptyIndexMessage'],
                'products' => $products,
                'columns' => $params['columns'],
                'imageVariant' => $params['imageVariant'],
                'limitImageHeight' => $params['limitImageHeight'],
                'showDisponibility' => $params['showDisponibility'],
                'showAddToCartButton' => $params['showAddToCartButton'],
                'slugProductView' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),  
                'slugAuthorProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor'),  
                'slugManufacturerProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer'),
                'slugSpecialOfferView' => App::getContentLocatorByPid('Eshop.EshopSpecialOffers.view'),
                'SmartAdminLauncher' => new SmartAdminLauncher(
                    App::getUrl(array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_edit',
                    )),
                    array('triggerTitle' => __a(__FILE__, 'Edit product'))
                )
            )
        );
    }
    
    public function indexAccessory($productId) {
        $this->displayOriginComment = true;
        $defaults = array(
            'view' => 'index',
            'recordsView' => 'indexRecords',
            'title' => null,
            'titleTag' => 'h1',
            'emptyIndexMessage' => true,
            'columns' => 0,
            'imageVariant' => 'small',
            'limitImageHeight' => true,
            'showDisponibility' => true,
            'showAddToCartButton' => true,
            'limit' => 3 * $this->getSetting('EshopProduct.pagingLimit'),
        );
        $params = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $accessoryProducts = $Product->getAccessoryProducts($productId, array(
            'conditions' => array(
                EshopProduct::getPublishedConditions(),
                EshopProduct::getNormalProductsConditions(),
                'EshopProduct.unavailable' => false
            ),
            'limit' => $params['limit'],
        ));
        
        $accessoryProductIds = array_keys($accessoryProducts);

        $products = $Product->getDetails($accessoryProductIds, array(
            'getManufacturer' => true,
            'getAuthors' => true,
            'getSpecialOffers' => true,
        ));
        
        App::loadLib('App', 'SmartAdminLauncher');
                        
        // load view
        $params['view'] = 'EshopProducts/' . $params['view'];
        return $this->loadView(
            $params['view'],
            array(
                'recordsView' => 'EshopProducts/' . $params['recordsView'],
                'title' => $params['title'],
                'titleTag' => $params['titleTag'],
                'emptyIndexMessage' => $params['emptyIndexMessage'],
                'products' => $products,
                'columns' => $params['columns'],
                'imageVariant' => $params['imageVariant'],
                'limitImageHeight' => $params['limitImageHeight'],
                'showDisponibility' => $params['showDisponibility'],
                'showAddToCartButton' => $params['showAddToCartButton'],
                'slugProductView' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),  
                'slugAuthorProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor'),  
                'slugManufacturerProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer'),
                'slugSpecialOfferView' => App::getContentLocatorByPid('Eshop.EshopSpecialOffers.view'),
                'SmartAdminLauncher' => new SmartAdminLauncher(
                    App::getUrl(array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_edit',
                    )),
                    array('triggerTitle' => __a(__FILE__, 'Edit product'))
                )
            )
        );
    }
        
    public function indexShowcase($filterId = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'filter_group' => 'banner', //HARDCODED (better to use slug here)
            'view' => 'indexShowcase',
            'showDisponibility' => false,
            'showAddToCartButton' => false,
            'showSortSelect' => false,
            'showFilterSelect' => false,
            'showIndexTypeSelect' => false,
            //'columns' => 4, 
            'limit' => 20,
            'paginate' => false,
            'hasImage' => true,
            'sort_random' => true,
        );
        $this->params = array_merge($defaults, Arr::camelizeKeys($this->params, array('separator' => '-')));
        return $this->index($filterId);
    }
    
    public function indexTop10($filterId = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'showSortSelect' => false, 
            'indexType' => 'brief',
            'showIndexTypeSelect' => false, 
            'imageVariant' => 'tiny',
            'showSerialLabel' => true, 
            'class' => 'top-10-products-index',
            //'columns' => 5,  
            'limit' => 10 ,
            'paginate' => false, 
            'hasImage' => true,
            'availableOnly' => true,
            'sortBestsellers' => true,
            'showDisponibility' => false,
        );
        $this->params = array_merge($defaults, Arr::camelizeKeys($this->params, array('separator' => '-')));
        return $this->index($filterId);
    }
    
    public function indexRecommended($filterId = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'showSortSelect' => false, 
            'indexType' => 'brief',
            'showIndexTypeSelect' => false, 
            'class' => 'recommended-products-index',
            //'columns' => 7, 
            'limit' => 7, 
            'paginate' => false, 
            'hasImage' => true,
            'availableOnly' => true,
        );
        $this->params = array_merge($defaults, Arr::camelizeKeys($this->params, array('separator' => '-')));
        return $this->index($filterId);
    }
    
    
    public function indexSlider($filterId = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'view' => 'indexSlider',
            'paginate' => false,
            'indexType' => 'brief',
            'showDisponibility' => true,
        );
        $this->params = array_merge($defaults, Arr::camelizeKeys($this->params, array('separator' => '-')));
        return $this->index($filterId);
    }
    
    public function indexBoughtWith($productId) {
        $this->displayOriginComment = true;
        $defaults = array(
            'view' => 'index',
            'recordsView' => 'indexRecords',
            'title' => null,
            'titleTag' => 'h1',
            'emptyIndexMessage' => true,
            'columns' => 0,
            'imageVariant' => 'small',
            'limitImageHeight' => true,
            'showDisponibility' => true,
            'showAddToCartButton' => true,
            'limit' => 3 * $this->getSetting('EshopProduct.pagingLimit'),
        );
        $params = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $productIds = $Product->getBoughtWithProductIds($productId, array(
            'conditions' => array(
                EshopProduct::getPublishedConditions(),
                EshopProduct::getNormalProductsConditions(array(
                    //'giftCardProducts' => 'allow'
                )),
                'EshopProduct.availability !=' => 'enum_soldot',
            ),
            'limit' => $params['limit'],
            'order' => array(
                'EshopProduct.unavailable ASC', 
                'EshopProduct.id DESC'
            ),
        ));
        
        $products = $Product->getDetails($productIds, array(
            'getManufacturer' => true,
            'getAuthors' => true,
            'getSpecialOffers' => true,
        ));
        
        App::loadLib('App', 'SmartAdminLauncher');
        
        // load view
        $params['view'] = 'EshopProducts/' . $params['view'];
        return $this->loadView(
            $params['view'],
            array(
                'recordsView' => 'EshopProducts/' . $params['recordsView'],
                'title' => $params['title'],
                'titleTag' => $params['titleTag'],
                'emptyIndexMessage' => $params['emptyIndexMessage'],
                'products' => $products,
                'columns' => $params['columns'],
                'imageVariant' => $params['imageVariant'],
                'limitImageHeight' => $params['limitImageHeight'],
                'showDisponibility' => $params['showDisponibility'],
                'showAddToCartButton' => $params['showAddToCartButton'],
                'slugProductView' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),  
                'slugAuthorProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor'),  
                'slugManufacturerProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer'),
                'slugSpecialOfferView' => App::getContentLocatorByPid('Eshop.EshopSpecialOffers.view'),
                'SmartAdminLauncher' => new SmartAdminLauncher(
                    App::getUrl(array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_edit',
                    )),
                    array('triggerTitle' => __a(__FILE__, 'Edit product'))
                )
            )
        );
    }
    
    public function indexFromTheSameAuthor($productId) {
        $this->displayOriginComment = true;
        $defaults = array(
            'view' => 'index',
            'recordsView' => 'indexRecords',
            'title' => null,
            'titleTag' => 'h1',
            'emptyIndexMessage' => true,
            'columns' => 0,
            'imageVariant' => 'small',
            'limitImageHeight' => true,
            'showDisponibility' => true,
            'showAddToCartButton' => true,
            'limit' => 3 * $this->getSetting('EshopProduct.pagingLimit'),
        );
        $params = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $productIds = $Product->getFromTheSameAuthorProductIds($productId, array(
            'conditions' => array(
                EshopProduct::getPublishedConditions(),
                EshopProduct::getNormalProductsConditions(),
                'EshopProduct.availability !=' => 'enum_soldout',
            ),
            'limit' => $params['limit'],
            'order' => array(
                'EshopProduct.unavailable ASC', 
                'EshopProduct.id DESC'
            ),
        ));
                
        $products = $Product->getDetails($productIds, array(
            'getManufacturer' => true,
            'getAuthors' => true,
            'getSpecialOffers' => true,
        ));
        
        App::loadLib('App', 'SmartAdminLauncher');
        
        // load view
        $params['view'] = 'EshopProducts/' . $params['view'];
        return $this->loadView(
            $params['view'],
            array(
                'recordsView' => 'EshopProducts/' . $params['recordsView'],
                'title' => $params['title'],
                'titleTag' => $params['titleTag'],
                'emptyIndexMessage' => $params['emptyIndexMessage'],
                'products' => $products,
                'columns' => $params['columns'],
                'imageVariant' => $params['imageVariant'],
                'limitImageHeight' => $params['limitImageHeight'],
                'showDisponibility' => $params['showDisponibility'],
                'showAddToCartButton' => $params['showAddToCartButton'],
                'slugProductView' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),  
                'slugAuthorProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor'),  
                'slugManufacturerProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer'),
                'slugSpecialOfferView' => App::getContentLocatorByPid('Eshop.EshopSpecialOffers.view'),
                'SmartAdminLauncher' => new SmartAdminLauncher(
                    App::getUrl(array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_edit',
                    )),
                    array('triggerTitle' => __a(__FILE__, 'Edit product'))
                )
            )
        );
    }
    
    /**
     * Action to display mini search element
     */
    public function miniSearch() {
        $this->displayOriginComment = true;
        App::loadLib('App', 'Paginator');
        $Paginator = new Paginator();
        
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $productsCount = $Product->findCount(array(
            'conditions' => EshopProduct::getPublishedConditions(),
        ));
        
        // load view
        return $this->loadView(
            'EshopProducts/miniSearch', array(
                'urlSearchProducts' => App::getContentUrlByPid('Eshop.EshopProducts.indexSearch'), 
                'productsCount' => $productsCount,
                'data' => $this->data,
                'paginatorResetUrlParam' => $Paginator->getPropertyResetUrlParam()
            )
        );
    }
    
    /**
     * 
     * @return string Html index of found products
     */
    public function indexSearch() {
        $this->displayOriginComment = true;
        $defaults = array(
            'view' => 'index',
            'title' => null, //__(__FILE__, 'Search results'),
            'titleTag' => 'h1',
            'emptyIndexMessage' => true,
            'paginate' => true,
            'infiniteScroll' => true,
            'recordsView' => 'indexRecords',            
            'columns' => 0,
            'class' => null,
            // cookie epit is set by view EshopProducts/indexTypeSelect
            'indexType' => Sanitize::value($_COOKIE['epit']),
            'imageVariant' => 'small',
            'limitImageHeight' => true,
            'showDisponibility' => true,
            'showAddToCartButton' => true,
            'showSortSelect' => true,
            'showFilterSelect' => true,
            'showIndexTypeSelect' => true,
            'showSerialLabel' => false,
        );    
        $inputParams = Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        ));
        $params = array_merge($defaults, $inputParams);
        if (
            $params['indexType'] === 'detailed' 
            && !isset($inputParams['showDisponibility'])
        ) {
            $params['showDisponibility'] = true;
        }
        elseif ($params['indexType'] === 'suggestions') {
            $params['showDisponibility'] = false;
            $params['showAddToCartButton'] = false;
        }
        
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        
        // apply filter to searched products
        $findOptions = $Product->getFilterFindOptions(array_merge($this->params, array(
            'avoidDefaultOrder' => true,
            'giftCardProducts' => 'allow',
        )));
        
        $externalSearchSucceeded = false;
        try {
            if (
                ($productIds = $Product->getExternalSearchProductIds(
                    $this->data, $findOptions
                )) === false
            ) {
                if (
                    $params['indexType'] === 'suggestions'
                    && !empty($_REQUEST['recordsOnly'])
                ) {
                    $this->forceExclusiveDirectOutput();
                    return '';
                }
                $error = $Product->getErrors();
                $error = reset($error);
                $error = reset($error);
                App::setMessage($error);
                App::redirect(App::getRefererUrl('/'));
            }
            $externalSearchSucceeded = $productIds !== null;
        } 
        catch (Throwable $e) {
            App::logError('External search API request failure', array(
                'var' => $e,
                'email' => true,
            ));
        }
        if (!$externalSearchSucceeded) {
            if (
                ($options = $Product->getSearchFindOptions($this->data, array(
                    'findOptions' => $findOptions,
                    'giftCardProducts' => 'allow',
                ))) === false
            ) {
                if (
                    $params['indexType'] === 'suggestions'
                    && !empty($_REQUEST['recordsOnly'])
                ) {
                    $this->forceExclusiveDirectOutput();
                    return '';
                }
                $error = $Product->getErrors();
                $error = reset($error);
                $error = reset($error);
                App::setErrorMessage($error);
                App::redirect(App::getRefererUrl('/'));
            }

            // get product ids
            $options['key'] = 'EshopProduct.id';
            $options['fields'] = array('EshopProduct.id');
            $options['paginate'] = true;
            $options['limit'] = $this->getSetting('EshopProduct.pagingLimit');
            $options['normalizeFindOptions'] = true;
            //$options['avoidCache'] = true; //debug
            $productIds = $Product->findList($options);
        }
        
        // get product details
        $products = $Product->getDetails($productIds, array(
            'getManufacturer' => true,
            'getAuthors' => true,
            'getSpecialOffers' => true,
        ));
        
        App::loadLib('App', 'SmartAdminLauncher');
        
        // load view
        $viewParams = array(
            'title' => $params['title'],
            'titleTag' => $params['titleTag'],
            'emptyIndexMessage' => $params['emptyIndexMessage'],
            'products' => $products,
            'columns' => $params['columns'],
            'class' => $params['class'],
            'indexType' => $params['indexType'],
            'imageVariant' => $params['imageVariant'],
            'limitImageHeight' => $params['limitImageHeight'],
            'showDisponibility' => $params['showDisponibility'],
            'showAddToCartButton' => $params['showAddToCartButton'],
            'showSortSelect' => $params['showSortSelect'],
            'showFilterSelect' => $params['showFilterSelect'],
            'showIndexTypeSelect' => $params['showIndexTypeSelect'],
            'showSerialLabel' => $params['showSerialLabel'],
            'slugProductView' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),  
            'slugAuthorProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor'),  
            'slugManufacturerProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer'),
            'slugSpecialOfferView' => App::getContentLocatorByPid('Eshop.EshopSpecialOffers.view'),
            'Paginator' => $Product->Paginator,
            'productsListId' => 'products-index-list', // this option is used as trigger to generate Luigis box search index annotations
            'data' => $this->data, // this option is used to generate Luigis box search index annotations
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array('triggerTitle' => __a(__FILE__, 'Edit product'))
            )
        );
        $params['recordsView'] = 'EshopProducts/' . $params['recordsView'];
        if (!empty($_REQUEST['recordsOnly'])) {
            $this->forceExclusiveDirectOutput();
            if (empty($products)) {
                return '';
            }
            else {
                return $this->loadView(
                    $params['recordsView'],
                    $viewParams
                );
            }
        }
        $params['view'] = 'EshopProducts/' . $params['view'];
        $viewParams['recordsView'] = $params['recordsView'];
        $viewParams['infiniteScroll'] = $params['infiniteScroll'];
        return $this->loadView(
            $params['view'],
            $viewParams
        );        
    }

    /**
     * NOTE: If you need to view product in a customized version (e.g. link from 
     * eshop cart) then pass here $cartIndex as second arg and get customized data 
     * from cart product
     */
    public function view($id = null) {
        $this->displayOriginComment = true;
        if (!$id) {
            App::setErrorMessage(__(__FILE__, 'The product could not be found'));
            App::redirect('/');
        }
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $product = $Product->getDetails($id, array(
            'getManufacturer' => true,
            'getManufacturerRange' => true,
            'getAuthors' => true,
            'getCategories' => true,
            'getImages' => true,
            'getSpecialOffers' => true,
            'getBrand' => array(
                'EshopBrand.id',
                'EshopBrand.name',
                'EshopBrand.slug',
                'EshopBrand.website',
                'EshopBrand.logo',
                'EshopBrand.description',
            ),
            'publishedOnly' => true,
            'loadContentBlocks' => true,
            'loadDescriptionSnippets' => true,
            'parseTechData' => true,
            'getVariantProducts' => true,
        ));
        if (!$product) {
            return App::loadScreen('_404', array('message' => __(__FILE__, 'This product is no more available')));
        }

        // add product itself to variants and resort variants
        if (!empty($product['EshopVariantProduct'])) {
            $product['EshopVariantProduct'][] = $product;
            Arr::sortStrings(
                $product['EshopVariantProduct'],
                array(
                    'compare' => function ($a, $b, $Collator) {
                        return $Collator->compare($a['name'], $b['name']);
                    }
                )
            );
        }

        if ($product['EshopProductCategory']) {
            $category = array_shift($product['EshopProductCategory']);
            $categoryParentIds = Model::getParentIdsFromTreePath($category['path']);
            $topCategoryId = (count($categoryParentIds) > 1) ? $categoryParentIds[1] : $category['id'];
            $Category = $this->loadModel('EshopProductCategory', true);
            $product['EshopProductTopCategory'] = $Category->findFirstBy('id', $topCategoryId, 
                array('fields' => array(
                    'EshopProductCategory.id',
                    'EshopProductCategory.name',
                    'EshopProductCategory.slug'
            )));
        }
        
        $priceVariants = $Product->getPriceVariants($product, $priceAttributeFields);
        
        // set App module global property FB.object which is used by App.fbObject element
        // in layouts to insert facebook opengraph metatags
        App::setGlobal('App', 'FB.object', array(
            'ogType' => 'book',
            'ogTitle' => $product['name'],
            'ogDescription' => $product['description'],
            'ogImage' => $product['image']['large'],
        ));
        
        // set seo params
        // - seo title
        $seoTitle = $product['seo_title'];
        if (!$seoTitle) {
            $seoTitle = $product['name'];
        }
        if (!empty($product['authors'])) {
            $seoTitle .= ' - ' . $product['authors'];
        }
        if (!empty($product['price_actual_taxed'])) {
            $seoTitle .= __(__FILE__, ' - za %s', Eshop::formatPrice($product['price_actual_taxed']));
        }
//        if (!empty($this->params['_content']['seo_title'])) {
//            $seoTitle = $this->params['_content']['seo_title'] . ' - ' . $seoTitle;
//        }
        App::setSeoTitle($seoTitle);
        // - seo description
        $seoDescription = $product['seo_description'];
//        if (!$seoDescription) {
//            $seoDescription = strip_tags($product['description']);
//        }
        App::setSeoDescription($seoDescription);
        // - seo keywords
        $seoKeywords = $product['seo_keywords'];
//        if (!$seoKeywords) {
//            $seoKeywords = $product['name'];
//        }
        App::setSeoKeywords($seoKeywords);
        $Product->getMicrodata($product);
        
        App::loadLib('App', 'SmartAdminLauncher');
        
        return $this->loadView(
            'EshopProducts/view',
            array(
                'product' => $product,
                'filterFields' => $Product->filterFields,
                'priceVariants' => $priceVariants,
                'priceAttributeFields' => $priceAttributeFields,
                'slugAuthorProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor'),  
                'slugManufacturerProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer'),
                'slugManufacturerRangeProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturerRange'),
                'slugCategoryProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory'),
                'slugSpecialOfferView' => App::getContentLocatorByPid('Eshop.EshopSpecialOffers.view'),
                'slugProductView' =>  App::getContentLocatorByPid('Eshop.EshopProducts.view'),
                'urlAddToCart' => App::getUrl(array(
                    'module' => $this->module,
                    'controller' => 'EshopCarts',
                    'action' => 'addProducts',
                    'args' => array($product['id'])
                )),
                'urlAddToWishlist' => App::getUrl(array(
                    'module' => $this->module,
                    'controller' => 'EshopWishlists',
                    'action' => 'addProduct',
                    'args' => array($product['id']),
                    'get' => array(
                        'redirectUrl' => App::$url,
                    )
                )),
                'urlAddAvailabilityAlert' => App::getUrl(array(
                    'module' => $this->module,
                    'controller' => 'EshopWishlists',
                    'action' => 'addProductAvailabilityAlert',
                    'args' => array($product['id']),
                    'get' => array(
                        'redirectUrl' => App::$url,
                    )
                )),
                'SmartAdminLauncher' => new SmartAdminLauncher(
                    App::getUrl(array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_edit',
                    )),
                    array('triggerTitle' => __a(__FILE__, 'Edit product'))
                )
            )
        );
    }
    
    public function admin_index() {
        $Product = $this->loadModel('EshopProduct', true);
        $Manufacturer = $this->loadModel('EshopManufacturer', true);
        $manufacturers = $Manufacturer->findList(array(
            'fields' => array('name'),
            'order' => 'name ASC',
        ));
        $ProductType = $this->loadModel('EshopProductType', true);
        $productTypes = $ProductType->findList(array(
            'fields' => array('name'),
            'order' => 'name ASC',
        ));
        
        // get lang to retrieve records for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;

        if (App::getSetting('Eshop', 'pricesAreTaxed')) {
            $actualPriceSql = $Product::ACTUAL_PRICE_SQL;
        } 
        else {
            $actualPriceSql = $Product::ACTUAL_PRICE_TAXED_SQL;
        }
        
        $records = $Product->find(array(
            'fields' => array(
                'EshopProduct.id',
                'EshopProduct.name',
                'EshopProduct.code',
                'EshopProduct.mrp_code',
                'EshopProduct.ean',
                'EshopProduct.slug',
                'EshopProduct.run_eshop_manufacturers_id',
                'EshopProduct.run_eshop_product_types_id',
                'EshopProduct.stock',
                'EshopProduct.availability',
                'EshopProduct.available_from',
//                'EshopProduct.long_delivery_time',
//                'EshopProduct.reprint',
                'EshopProduct.price',
                $actualPriceSql . ' as actual_price',
                'EshopProduct.discount_price',
                'EshopProduct.discount_rate',
                'EshopProduct.color',
                'EshopProduct.size',
                'EshopManufacturer.import_group',
                //'EshopProduct.discount_from',
                'EshopProduct.discount_to',
                '(EshopProduct.image IS NOT NULL) AS has_image',
                'EshopProduct.active',
                'EshopProduct.translated',
            ),
            'joins' => array(
                array(
                    'model' => 'EshopManufacturer',
                    'type' => 'left',
                )
            ),
            'literals' => array(
                'fields' => array(
                    $actualPriceSql . ' as actual_price',
                    '(EshopProduct.image IS NOT NULL) AS has_image',
                )
            ),
            'group' => 'EshopProduct.id',
            'order' => 'EshopProduct.id DESC',
            'paginate' => true,
            'separate' => true,
            'lang' => $lang,
            'normalizeFindOptions' => function($options) use ($Product) {
                $options = $Product->normalizeFindOptions($options);
                // normalize ordering by EshopProduct.run_eshop_manufacturers_id
                // and by EshopProduct.run_eshop_product_types_id
                if (!empty($options['order'])) {
                    $options['order'] = (array)$options['order'];
                    $options['literals']['order'] = (array)Sanitize::value($options['literals']['order']);
                    $options['joins'] = (array)Sanitize::value($options['joins']);
                    // $_GET['sort']['EshopProduct.run_eshop_manufacturers_id']
                    $paths = Arr::search($options['order'], '/`EshopProduct`.`run_eshop_manufacturers_id`/', array(
                        'comparison' => 'regex', 
                        'separator' => '/'
                    ));
                    if (!empty($paths)) {
                        foreach ($paths as $path) {
                            $value = Arr::getPath($options['order'], $path, '/');
                            $direction = explode(' ', $value);
                            $direction = end($direction);
                            Arr::setPath($options['order'], $path, 'EshopManufacturer.name ' . $direction, '/');
                        }
                        $options['joins'][] = array(
                            'model' => 'EshopManufacturer',
                            'type' => 'left',
                        );
                    }
                    // $_GET['sort']['EshopProduct.run_eshop_product_types_id']
                    $paths = Arr::search($options['order'], '/`EshopProduct`.`run_eshop_product_types_id`/', array(
                        'comparison' => 'regex', 
                        'separator' => '/'
                    ));
                    if (!empty($paths)) {
                        foreach ($paths as $path) {
                            $value = Arr::getPath($options['order'], $path, '/');
                            $direction = explode(' ', $value);
                            $direction = end($direction);
                            Arr::setPath($options['order'], $path, 'EshopProductType.name ' . $direction, '/');
                        }
                        $options['joins'][] = array(
                            'model' => 'EshopProductType',
                            'type' => 'left',
                        );
                    }
                }
                if (!empty($options['conditions'])) {
                    $path = Arr::search($options['conditions'], '/`EshopSpecialOffer`.`name`/', array(
                        'comparison' => 'regex', 
                        'separator' => '/',
                        // we suppose there is just one condition for special offer name
                        'first' => true,
                    ));
                    if ($path !== false) {
                        $this->loadModel('EshopSpecialOffer');
                        $Offer = new EshopSpecialOffer();
                        $offerIds = $Offer->findList(array(
                            'fields' => array('id'),
                            'conditions' => Arr::getPath($options['conditions'], $path, '/'),
                        ));
                        $this->loadModel('EshopSpecialOfferPromotedProduct');
                        $OfferPromotedProduct = new EshopSpecialOfferPromotedProduct();
                        $offerProductIds = $OfferPromotedProduct->findList(array(
                            'key' => 'run_eshop_products_id',
                            'fields' => array('run_eshop_products_id'),
                            'conditions' => array('run_eshop_special_offers_id' => $offerIds),
                        ));
                        $this->loadModel('EshopSpecialOfferDiscountedProduct');
                        $OfferDiscountedProduct = new EshopSpecialOfferDiscountedProduct();
                        $offerProductIds += $OfferDiscountedProduct->findList(array(
                            'key' => 'run_eshop_products_id',
                            'fields' => array('run_eshop_products_id'),
                            'conditions' => array('run_eshop_special_offers_id' => $offerIds),
                        ));
                        Arr::setPath($options['conditions'], $path, array('EshopProduct.id' => $offerProductIds), '/');
                        // remove unneeded (an invalid) join
                        $path = Arr::search(
                            $options['joins'], 
                            array(
                                'model' =>  'EshopSpecialOffer',
                                'module' =>  'Eshop',
                                'type' =>  'left',
                            ), 
                            array(
                                'comparison' => 'intersect', 
                                'separator' => '/',
                                // we suppose there is just one join for special offer
                                'first' => true,
                            )
                        );
                        if ($path !== false) {
                            Arr::unsetPath($options['joins'], $path, '/');
                        }
                    }
                }
                return $options;
            }
        ));
        
        // add HABTM record names
        $ids = array();
        foreach ($records as $record) {
            $ids[] = $record['EshopProduct']['id'];
        }
        $Category = $this->loadModel('EshopProductCategory', true);
        $categories = $Category->findList(array(
            'key' => 'EshopProductCategoryProduct.run_eshop_products_id',
            'fields' => array('EshopProductCategory.name'),
            'conditions' => array(
                'EshopProductCategoryProduct.run_eshop_products_id' => $ids
            ),
            'joins' => array(
                array(
                    'model' => 'EshopProductCategoryProduct',
                    'type' => 'left',
                ),
            ),
            'accumulate' => true,
        ));
        $Group = $this->loadModel('EshopProductGroup', true);
        $groups = $Group->findList(array(
            'key' => 'EshopProductGroupProduct.run_eshop_products_id',
            'fields' => array('EshopProductGroup.name'),
            'conditions' => array(
                'EshopProductGroupProduct.run_eshop_products_id' => $ids
            ),
            'joins' => array(
                array(
                    'model' => 'EshopProductGroupProduct',
                    'type' => 'left',
                ),
            ),
            'accumulate' => true,
        ));
        $Author = $this->loadModel('EshopAuthor', true);
        $authors = $Author->findList(array(
            'key' => 'EshopProductAuthor.run_eshop_products_id',
            'fields' => array('EshopAuthor.name'),
            'conditions' => array(
                'EshopProductAuthor.run_eshop_products_id' => $ids
            ),
            'joins' => array(
                array(
                    'model' => 'EshopProductAuthor',
                    'type' => 'left',
                ),
            ),
            'accumulate' => true,
        ));
        foreach ($records as &$record) {
            $id = $record['EshopProduct']['id'];
            $record['EshopProductCategoryProduct>EshopProductCategory']['name'] = '';
            if (!empty($categories[$id])) {
                $record['EshopProductCategoryProduct>EshopProductCategory']['name'] = implode('; ', $categories[$id]);
            }
            $record['EshopProductCategoryProduct>EshopProductCategory']['id'] = reset($categories[$id]);
            $record['EshopProductGroupProduct>EshopProductGroup']['name'] = '';
            if (!empty($groups[$id])) {
                $record['EshopProductGroupProduct>EshopProductGroup']['name'] = implode('; ', $groups[$id]);
            }
            $record['EshopProductAuthor>EshopAuthor']['name'] = '';
            if (!empty($authors[$id])) {
                $record['EshopProductAuthor>EshopAuthor']['name'] = implode('; ', $authors[$id]);
            }
        }
        
        $categoriesSelectList = $Category->findTreeSelectList('categories', array(
            'indent' => '&nbsp;&nbsp;'
        ));
        
        $viewLocator = App::getContentLocatorByPid('Eshop.EshopProducts.view');
        App::setSeoTitle(__a(__FILE__, 'Products'));
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'Products'),
            'records' => $records,
            'primaryKey' => 'EshopProduct.id',
            'columns' => array(
                'EshopProduct.name' => __a(__FILE__, 'Name'),
                'EshopProductCategoryProduct>EshopProductCategory.id' => __a(__FILE__, 'Kategória'),
                'EshopProduct.code' => __a(__FILE__, 'Code'),
                'EshopProduct.mrp_code' => __a(__FILE__, 'MRP kód'),
                'EshopProduct.ean' => __a(__FILE__, 'EAN'),
//                'EshopProduct.slug' => __a(__FILE__, 'Slug'),
                'EshopProduct.run_eshop_manufacturers_id' => __a(__FILE__, 'Manufacturer'),
//                'EshopProduct.price' => __a(__FILE__, 'Price'),
                'actual_price' => __a(__FILE__, 'Price'),
//                'EshopProduct.discount_price' => __a(__FILE__, 'Discount price'),
//                'EshopProduct.discount_rate' => __a(__FILE__, 'Discount rate'),
//                'EshopProduct.discount_to' => __a(__FILE__, 'Discount to'),
//                'EshopManufacturer.import_group' => __a(__FILE__, 'Manufacturer group'),
                'EshopProduct.stock' => __a(__FILE__, 'Stock'),
                'EshopProduct.availability' => __a(__FILE__, 'Availability'),
//                'EshopProduct.available_from' => __a(__FILE__, 'Available from'),
//                'EshopProduct.long_delivery_time' => __a(__FILE__, 'Long delivery time'),
//                'EshopProduct.reprint' => __a(__FILE__, 'Reprint'),
//                'EshopProduct.run_eshop_product_types_id' => __a(__FILE__, 'Product type'),
               'EshopProductCategoryProduct>EshopProductCategory.name' => __a(__FILE__, 'Categories'),
//                'EshopProductGroupProduct>EshopProductGroup.name' => __a(__FILE__, 'Product groups'),
                /*/
                // this is resolved in normalizeFindOptions()
                'EshopSpecialOffer.name' => __a(__FILE__, 'Špeciálne ponuky'),
                /*/
//                'EshopProductAuthor>EshopAuthor.name' => __a(__FILE__, 'Authors'),
//                'has_image' => __a(__FILE__, 'Obrázok'),
                'EshopProduct.active' => __a(__FILE__, 'Active'),
                'EshopProduct.translated' => __a(__FILE__, 'Preložený'),
            ),
            'renderFields' => array(
                'EshopProduct.name' => function($value, $field, $data) use ($lang, $viewLocator) {
                    if (empty($data['EshopProduct.slug'])) {
                        return $value;
                    }
                    $locator = App::getUrl(array(
                        'locator' => $viewLocator,
                        'args' => array($data['EshopProduct.slug'])
                    ));
                    return $value . ' ' . Html::hyperlinkTag(
                        $locator, 
                        '<i class="fa fa-external-link"></i>', 
                        array(
                            'lang' => $lang,
                            'attributes' => array('target' => '_blank')
                        )
                    );
                },
                'EshopProduct.slug' => function($value) use ($lang, $viewLocator) {
                    $locator = App::getUrl(array(
                        'locator' => $viewLocator,
                        'args' => array($value)
                    ));
                    return Html::hyperlinkTag($locator, $value, array(
                        'lang' => $lang,
                        'attributes' => array('target' => '_blank')
                    ));
                },
                'EshopProductCategoryProduct>EshopProductCategory.id' => $categoriesSelectList,
                'EshopProduct.run_eshop_manufacturers_id' => $manufacturers,
                'EshopProduct.availability' => $Product->getEnumValues('availability'),
                'EshopProduct.long_delivery_time' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'EshopProduct.reprint' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'has_image' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'EshopProduct.active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'EshopProduct.translated' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'EshopProduct.run_eshop_product_types_id' => $productTypes,
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('EshopProduct.active' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                ),
                array(
                    'conditions' => array('EshopProduct.translated' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                ),
            ),
            'Paginator' => $Product->Paginator,
            'actions' => array(                
                'add' => array(
                    'url' => '/mvc/Eshop/EshopProducts/admin_add',
                ),
                'export' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopProducts/admin_export',
                        'inherit' => array('get' => array(
                            'lang',
                            $Product->Paginator->getPropertyFilterUrlParam(),
                            $Product->Paginator->getPropertySortUrlParam(),
                        )),
                    ),
                ),
                'lang' => true,
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopProducts/admin_edit',
                        'get' => array(
                            'lang' => $lang
                        )
                    )
                ),
                'copy' => array(
                    'url' => '/mvc/Eshop/EshopProducts/admin_copy',
                ),
                'delete' => array(
                    'url' => '/mvc/Eshop/EshopProducts/admin_delete',
                    'confirmMessage' => __a(__FILE__, 'Please, confirm removal of item ":EshopProduct.name:"'),
                ),
            ),
            'bulkActions' => array(
                'deleteMany' => array(
                    'url' => '/mvc/Eshop/EshopProducts/admin_delete',
                ),
                'editMany' => array(
                    'url' => '/mvc/Eshop/EshopProducts/admin_editMany',
                ),
            ),                
        ));        
    }
    
    public function admin_export() {
        // !!! keep this is synchro with the app/config/database.php > 'tableReservationTimeLimit' => ...
        set_time_limit(600); 
        ini_set('memory_limit', '512M');
        
        $Product = $this->loadModel('EshopProduct', true);
        
        // get lang to retrieve records for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        // prepare fields
        $fields = $Product->getFields(array('qualify' => true));
        $fields[] = 'EshopManufacturer.name AS manufacturer_name';
        
        $Product->export(
            array(
                'fields' => $fields,
                'avoidFields' => array(
                    'EshopProduct.parent_id',
                    'EshopProduct.run_eshop_manufacturers_id',
                    'EshopProduct.run_eshop_manufacturer_ranges_id',
                    'EshopProduct.run_eshop_product_types_id',
                    'EshopProduct.modified',
                    'EshopProduct.deleted',
                ),
                'joins' => array(
                    array(
                        'model' => 'EshopManufacturer',
                        'type' => 'left',
                    )
                ),
                'order' => 'EshopProduct.name',
                'lang' => $lang,
                // allow paginator filtering & sorting without limit
                'paginate' => true,
                'limit' => false,
            ), 
            array(
                'file' => $this->name,
                'format' => 'csv', //'xlsx',
            )
        );
    }    
    
    /**
     * Loads single product detail.
     * 
     * Used on order edit.
     * 
     * @param integer $id Product id
     * 
     * @return string Json encoded product record
     */
    public function admin_load($id) {
        App::loadLib('App', 'AjaxResponse');
        $Res = new AjaxResponse();
        App::setLayout('App', 'json');
        $Product = new EshopProduct();
        $product = $Product->getDetails($id);
        $Res->data = $product;
        $Res->success = true;
        return $Res->getJson();
    }
    
    /**
     * Creates Json encoded list. 
     * 
     * Used in related products itemselector in product edit
     * 
     * @param string $keyword Keyword to filter list by name
     * @param string $ids Ids of records separated by ";". If specified, method
     * return labels of this ids
     * 
     * @return string Json encoded data
     */
    public function admin_list($keyword = null, $ids = null) {
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        $EshopProduct = new EshopProduct();
        App::setLayout('App', 'json');
        
        $options = array(
            'conditions' => array(
                EshopProduct::getPublishedConditions(),
                EshopProduct::getNormalProductsConditions(array(
                    'giftCardProducts' => 'allow'
                )),
            ),
            'fields' => array( 
                'CONCAT(EshopProduct.code, " - ", EshopProduct.name) AS name', 
            ),
            'order' => array(
                'EshopProduct.name ASC'
            ),
            'literals' => array(
                'fields' => true,
            )
        );
        if (isset($ids)) {
            if (!is_array($ids)) {
                $ids = explode(';', $ids);
            }
            $options['conditions'] = array(
                'id' => $ids,
            );           
        } 
        else {
            // see the comment in app/js/sources/libs/Itemselector.js > loadListFromAjax()
            if (isset($_REQUEST['keyword'])) {
                $keyword = $_REQUEST['keyword'];
            }
            if (isset($keyword)) {
                $options['conditions'] = array(
                    'EshopProduct.code %~%' => $keyword,
                    'OR',
                );
                $keywords = array_filter(preg_split('/\s+/', $keyword));
                foreach($keywords as $k) {                    
                    $options['conditions'][] = array('EshopProduct.name %~%' => $k);
                }
                // if single interger then check also ean
                if (preg_match('/^[0-9]+$/', $keyword)) {
                    $options['conditions'][] = 'OR';
                    $options['conditions']['EshopProduct.ean %~%'] = $keyword;
                }
            }
            $Response->total = $EshopProduct->findCount($options);
            if ($Response->total > App::getSetting('App', 'itemselector.maxItemsToShow')) {
                return $Response->getJson();
            }
        }
        
        $Response->data = $EshopProduct->findList($options);
        $Response->success = true;

        return $Response->getJson();
    }
    
    public function admin_add() {
        
        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        $Product = $this->loadModel('EshopProduct', true);
        
        if ($this->data) {
            try {
                if ($Product->saveAll($this->data, array('lang' => $lang))) {
                    App::setSuccessMessage(__a(__FILE__, 'New record has been succesfully created'));
                    App::redirect(App::getUrl(array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_edit',
                        'args' => array($Product->getPropertyId()),
                        'source' => App::$requestSource,
                    )));
                }
                App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            } 
            catch (Exception_DB_TablesReservationFailure $e) {
                App::setErrorMessage(__a(__FILE__, 'Record save has failed. Data are reserved by other process. Please try later.')); 
                App::logError($e->getMessage(), array(
                    'var' => $e,
                    'email' => true,
                ));
            }
        }
        App::setSeoTitle(__a(__FILE__, 'New product'));
        
        $taxRates = $Product->getEnumValues('tax_rate');

        $vatInfo = App::getSetting('Eshop', 'pricesAreTaxed') ? __a(__FILE__, 'with VAT') : __a(__FILE__, 'without VAT');
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'New product'),
            'data' => $this->data,
            'Model' => $Product,
            'columns' => 4,
            'actions' => array(
                // a bit of hack to diplay default lang icon in form header
                'lang' => array(
                    'options' => array_combine(array($lang), array($lang)),
                )
            ),
            'fields' => array(                
                array(
                    'field' => 'name',
                    'label' => __a(__FILE__, 'Name'),
                    'autofocus' => true,
                ),
//// autogenerated by normalization                
//                array(
//                    'field' => 'code',
//                    'label' => __a(__FILE__, 'Internal code')
//                ),
                array(
                    'field' => 'tax_rate',
                    'label' => __a(__FILE__, 'Tax rate'),
                    'type' => 'select', 
                    'options' => $taxRates,
                    'value' => $Product->getFieldDefaultValue('tax_rate'),
                ),
                array(
                    'field' => 'price',
                    'label' => __a(__FILE__, 'Price') . ' <span class="small">(' . $vatInfo . ')</span>'
                ),
            ),
        ));        
    }
    
    public function admin_copy($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        // get lang to retrieve contents for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        $Product = $this->loadModel('EshopProduct', true);
        
        if ($this->data) {
            try {
                if ($Product->saveAll($this->data, array('lang' => $lang))) {
                    App::setSuccessMessage(__a(__FILE__, 'New record has been succesfully created'));
                    App::redirect(App::getUrl(array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_edit',
                        'args' => array($Product->getPropertyId()),
                        'source' => App::$requestSource,
                    )));
                }
                App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            } 
            catch (Exception_DB_TablesReservationFailure $e) {
                App::setErrorMessage(__a(__FILE__, 'Record save has failed. Data are reserved by other process. Please try later.')); 
                App::logError($e->getMessage(), array(
                    'var' => $e,
                    'email' => true,
                ));
            }
        }
        else {
            try {
                $this->data = $Product->findAll(array(
                    'conditions' => array('EshopProduct.id' => $id),
                    'lang' => $lang,
                    'first' => true,
                ));
            }
            catch (Exception_DB_TablesReservationFailure $e) {
                App::setErrorMessage(__a(__FILE__, 'Data are reserved by other process. Please try later.'));
                App::redirect(App::getUrl(array(
                    'locator' => '/_error',
                    'source' => App::$requestSource,
                )));
            }
            // clear specific fields
            $this->data['id'] = $this->data['code'] = $this->data['slug'] = null;
            $this->data['name'] = $this->data['seo_title'] = null;
            $this->data['description'] = $this->data['discount_price'] = null;
            $this->data['ean'] = null;
            $this->data['weight'] = $this->data['width'] = $this->data['height'] = null;
            $this->data['length'] = $this->data['dimensions'] = null;
            $this->data['created'] = $this->data['modified'] = null;
            // - file fields
            $fileFields = array_keys($Product->getPropertyFileFields());
            foreach($fileFields as $fileField) {
                $this->data[$fileField] = null;
            }
            // - project specific fields
            $this->data['exported'] = null;
        }
        
        // add name to title and tabTitle
        $sourceData = $Product->findFirstBy('id', $id, array(
            'fields' => array('EshopProduct.name', 'EshopProduct.slug')
        ));
        App::setSeoTitle(__a(__FILE__, 'New product - copy of "%s"', $sourceData['name']));
        return $this->loadView('EshopProducts/admin_productForm', array(
            'title' => __a(
                __FILE__, 
                'New product - copy of "%s"', 
                Str::fill('<a href="/' . App::getContentLocatorByPid('Eshop.EshopProducts.view') . '/:slug:" target="_blank">:name:</a>', $sourceData)
            ),
            'data' => $this->data,
            'Model' => $Product,
            'lang' => $lang,
        ));
    }
    
    public function admin_edit($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        /* @var $Product EshopProduct */
        $Product = $this->loadModel('EshopProduct', true);
        
        if ($this->data) {
            try {
                if ($Product->saveAll($this->data, array('lang' => $lang))) {
                    App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                    // warnings can be set even in case of successful save
                    if (($processingWarnings = $Product->getProcessingWarnings())) {
                        foreach ($processingWarnings as $processingWarning) {
                            App::setWarningMessage($processingWarning);
                        }
                    }
                    App::redirect(App::$url);
                }
                App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
                if (($processingErrors = $Product->getProcessingErrors())) {
                    foreach ($processingErrors as $processingError) {
                        App::setErrorMessage($processingError);
                    }
                }
                if (($processingWarnings = $Product->getProcessingWarnings())) {
                    foreach ($processingWarnings as $processingWarning) {
                        App::setWarningMessage($processingWarning);
                    }
                }
            } 
            catch (Exception_DB_TablesReservationFailure $e) {
                App::setErrorMessage(__a(__FILE__, 'Record save has failed. Data are reserved by other process. Please try later.')); 
                App::logError($e->getMessage(), array(
                    'var' => $e,
                    'email' => true,
                ));
            }
        }
        else {
            try {
                $this->data = $Product->findAll(array(
                    'conditions' => array('EshopProduct.id' => $id),
                    'lang' => $lang,
                    'first' => true,
                ));
            }
            catch (Exception_DB_TablesReservationFailure $e) {
                App::setErrorMessage(__a(__FILE__, 'Data are reserved by other process. Please try later.'));
                App::redirect(App::getUrl(array(
                    'locator' => '/_error',
                    'source' => App::$requestSource,
                )));
            }
            // get file fields real path
            $fileFields = array_keys($Product->getPropertyFileFields());
            foreach($fileFields as $fileField) {
                if (!empty($this->data[$fileField])) {
                    $this->data[$fileField] = $Product->getFileFieldUrlPath($fileField, array('file' => $this->data[$fileField]));
                }
            }
        }
        
        if (
            array_key_exists('mrp_code', $this->data)
            && !$this->data['mrp_code']
        ) {
            App::setWarningMessage(__a(__FILE__, 'Produkt nemá zadaný MRP kód'));
        }
        
        // zobrazované paramete produktov podla zaradenia do kategórií
        $applicableParameters = array();
        $this->loadModel('EshopProductCategory');
        $Category = new EshopProductCategory();
        foreach($this->data['category_ids'] as $categoryId){
            $parameters = $Category->findFieldBy(
                'EshopProductCategory.product_parameters', 
                'EshopProductCategory.id', 
                $categoryId
            );
            $parameters = Str::explode(',', $parameters);
            $applicableParameters = array_merge($applicableParameters, $parameters);
        }
        $applicableParameters = array_map('trim', $applicableParameters);
        $applicableParameters = array_filter($applicableParameters);
        $applicableParameters = array_flip($applicableParameters);
        
        // add name to title and tabTitle
        if (!empty($this->data['name']) && !empty($this->data['slug'])) {
            $actualData = array('name' => $this->data['name'], 'slug' => $this->data['slug']);
        }
        // - name should not be empty, if so then it is error so use actual name
        else {
            $actualData = $Product->findFirstBy('id', $this->data['id'], array(
                'fields' => array('EshopProduct.name', 'EshopProduct.slug')
            ));
        }
        App::setSeoTitle(__a(__FILE__, '&quot;%s&quot;', $actualData['name']));
        return $this->loadView('EshopProducts/admin_productForm', array(
            'title' => __a(
                __FILE__, 
                'Edit product "%s"', 
                Str::fill('<a href="/' . App::getContentLocatorByPid('Eshop.EshopProducts.view') . '/:slug:" target="_blank">:name:</a>', $actualData)
            ),
            'data' => $this->data,
            'Model' => $Product,
            'lang' => $lang,
            'applicableParameters' => $applicableParameters,
        ));
    }
    
    /**
     * Edits many specified products
     * 
     * @param string $_GET['ids'] Comma separated list of product ids
     * 
     * @return string Html
     */
    public function admin_editMany() {
        if (($ids = $this->getBulkActionIds()) === false) {
            App::setErrorMessage(__a(__FILE__, 'Missing record ids'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        
        // get lang to retrieve contents for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        $Product = $this->loadModel('EshopProduct', true);
        
        if ($this->data) {
            try {
                $this->data = $Product->saveMany($ids, $this->data, array('lang' => $lang));
                App::setSuccessMessage(__a(__FILE__, 'Records has been succesfully updated'));
            } 
            catch (Exception_DB_TablesReservationFailure $e) {
                App::setErrorMessage(__a(__FILE__, 'Records save has failed. Data are reserved by other process. Please try later.')); 
                App::logError($e->getMessage(), array(
                    'var' => $e,
                    'email' => true,
                ));
            }
        }   
        // bulk records
        $products = $Product->find(array(
            'fields' => array(
                'EshopProduct.name',
                'EshopProduct.slug',
            ),
            'conditions' => array(
                'EshopProduct.id' => $ids,
            ),
            'order' => array(
                'EshopProduct.id' => $ids, 
            )
        ));
        App::setSeoTitle(__a(__FILE__, 'Bulk edit of %s products', count($products)));
        return $this->loadView('EshopProducts/admin_productForm', array(
            'title' => __a(__FILE__, 'Bulk edit of %s products', count($products)),
            'data' => $this->data,
            'Model' => $Product,
            'lang' => $lang,
            'products' => $products,
            'slugProductView' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),
        ));
    }
    
    /**
     * Deletes specified single product record or many product records
     * 
     * @param int $id Single product id
     * @param string $_GET['ids'] Comma separated list of product ids. If provided 
     *      then $id param is not considered.
     * 
     * @todo delete images too
     */
    public function admin_delete($id = null) {
        if (empty($id) && !$this->hasBulkActionIds()) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/',
                'source' => App::$requestSource,
            ))));
        }
        // check for bulk delete
        if (($ids = $this->getBulkActionIds()) === false) {
            $bulkAction = false;
            $ids = (array)$id;
        }
        else {
            $bulkAction = true;
        }
        $EshopProduct = $this->loadModel('EshopProduct', true);
        try {
            $EshopProduct->deleteBy('id', $ids);
            
            if ($bulkAction) {
                App::setSuccessMessage(__a(__FILE__, 'Records has been succesfully deleted'));
            }
            else {
                App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
            }
        } 
        catch (Exception_DB_TablesReservationFailure $e) {
            if ($bulkAction) {
                App::setErrorMessage(__a(__FILE__, 'Records delete has failed. Data are reserved by other process. Please try later.')); 
            }
            else {
                App::setErrorMessage(__a(__FILE__, 'Record delete has failed. Data are reserved by other process. Please try later.')); 
            }
            App::logError($e->getMessage(), array(
                'var' => $e,
                'email' => true,
            ));
        }
        
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    } 
    
    public function admin_deleteFile($fileField = null, $id = null) {
        if (!$fileField || !$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record fileField and/or id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        $EshopProduct = $this->loadModel('EshopProduct', true);
        $fileFields = $EshopProduct->getPropertyFileFields();
        if (!isset($fileFields[$fileField])) {
            App::setErrorMessage(__a(__FILE__, 'Invalid file field'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        try {
            $EshopProduct->save(array('id' => $id, $fileField => ''));
            App::setSuccessMessage(__a(__FILE__, 'File has been succesfully deleted'));
        } 
        catch (Exception_DB_TablesReservationFailure $e) {
            App::setErrorMessage(__a(__FILE__, 'File delete has failed. Data are reserved by other process. Please try later.')); 
            App::logError($e->getMessage(), array(
                'var' => $e,
                'email' => true,
            ));
        }
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }
    
    /**
     * Perform automatic translate for translated fields of EshopProduct
     * Google translate API is used for this
     *
     * @param null $id - if entered, only product with this id is translated, if not, batch is processed
     * @throws Exception
     */
    public function admin_translate($id = null) {
        $EshopProduct = new EshopProduct();

        $apiUrl = $this->getSetting('googleTranslate.url');
        $sourceLang = 'cs';
        $targetLang = 'sk';
        $key = $this->getSetting('googleTranslate.key');

        if ($id === null) {
            $skipped = array(187610, 187791);

            $products = $EshopProduct->find(array(
                'fields' => array(
                    'EshopProduct.id',
                    'EshopProduct.name',
                    'EshopProduct.description',
                    'EshopProduct.short_description',
                    'EshopProduct.seo_title',
                    'EshopProduct.seo_description',
                    'EshopProduct.seo_keywords',
                    'EshopProduct.feed_name',
                ),
                'conditions' => array(
                    'EshopProductLang._slug_sk IS NULL',
                    'EshopProduct.type' => 'gift',
//                    'EshopProduct.id NOT IN (' . implode(',', $skipped) .')',
                ),
                'joins' => array(
                    array(
                        'table' => $EshopProduct->getPropertyTable() . '_i18ns',
                        'alias' => 'EshopProductLang',
                        'conditions' => array(
                            'EshopProduct.id = EshopProductLang.run_eshop_products_id'
                        )
                    )
                ),
                'limit' => 10,
                'literals' => array(
                    'conditions' => array(
                        'EshopProduct.id NOT IN (' . implode(',', $skipped) .')',
                    )
                )
            ));
            foreach ($products as $product) {
                $params = "key=$key" .
                    "&q=" . rawurlencode($product['name']) .
                    "&q=" . rawurlencode($product['description']) .
                    "&q=" . rawurlencode($product['short_description']) .
                    "&q=" . rawurlencode($product['seo_title']) .
                    "&q=" . rawurlencode($product['seo_description']) .
                    "&q=" . rawurlencode($product['seo_keywords']) .
                    "&q=" . rawurlencode($product['feed_name']) .
                    "&source=$sourceLang&target=$targetLang";

                $resultOrig = $result = file_get_contents($apiUrl . $params);
                $result = json_decode($result, true);
                if (!isset($result['data']['translations'][0]['translatedText'])) {
                    // try without tags
                    $params = "key=$key" .
                        "&q=" . rawurlencode($product['name']) .
                        "&q=" . rawurlencode(strip_tags($product['description'])) .
                        "&q=" . rawurlencode(strip_tags($product['short_description'])) .
                        "&q=" . rawurlencode($product['seo_title']) .
                        "&q=" . rawurlencode(strip_tags($product['seo_description'])) .
                        "&q=" . rawurlencode($product['seo_keywords']) .
                        "&q=" . rawurlencode($product['feed_name']) .
                        "&source=$sourceLang&target=$targetLang";

                    $resultOrig = $result = file_get_contents($apiUrl . $params);
                    $result = json_decode($result, true);
                }
                if (!isset($result['data']['translations'][0]['translatedText'])) {
                    return $resultOrig . '<br>' .
                        print_r($result) .
                        'No response for product id ' . $product['id'] . '<br>' .
                        '<a href="' . $apiUrl . $params . '" target="_blank">link</a>';
                }
                $data = array(
                    '_name_sk' => $result['data']['translations'][0]['translatedText'],
                    '_description_sk' => $result['data']['translations'][1]['translatedText'],
                    '_short_description_sk' => $result['data']['translations'][2]['translatedText'],
                    '_seo_title_sk' => $result['data']['translations'][3]['translatedText'],
                    '_seo_description_sk' => $result['data']['translations'][4]['translatedText'],
                    '_seo_keywords_sk' => $result['data']['translations'][5]['translatedText'],
                    '_feed_name_sk' => $result['data']['translations'][6]['translatedText'],
                    '_slug_sk' => Str::slugize($result['data']['translations'][0]['translatedText']) . '-z' . $product['id']
                );
                DB::update($EshopProduct->getPropertyTable() . '_i18ns', $data, array(
                    'conditions' => array(
                        $EshopProduct->getPropertyTable() . '_id' => $product['id']
                    )
                ));

                return '<a href="" onclick="window.location.reload();">REFRESH</a><br>' .
                    'result:<br><pre>' . print_r($data, true) . '</pre>';
            }
        } else {
            $product = $EshopProduct->findFirst(array(
                'fields' => array(
                    'EshopProduct.id',
                    'EshopProduct.name',
                    'EshopProduct.description',
                    'EshopProduct.short_description',
                    'EshopProduct.seo_title',
                    'EshopProduct.seo_description',
                    'EshopProduct.seo_keywords',
                    'EshopProduct.feed_name',
                ),
                'conditions' => array(
                    'EshopProduct.id' => $id,
                ),
            ));

            $params = "key=$key" .
                "&q=" . rawurlencode($product['name']) .
                "&q=" . rawurlencode($product['description']) .
                "&q=" . rawurlencode($product['short_description']) .
                "&q=" . rawurlencode($product['seo_title']) .
                "&q=" . rawurlencode($product['seo_description']) .
                "&q=" . rawurlencode($product['seo_keywords']) .
                "&q=" . rawurlencode($product['feed_name']) .
                "&source=$sourceLang&target=$targetLang";

            $resultOrig = $result = file_get_contents($apiUrl . $params);
            $result = json_decode($result, true);
            if (!isset($result['data']['translations'][0]['translatedText'])) {
                // try without tags
                $params = "key=$key" .
                    "&q=" . rawurlencode($product['name']) .
                    "&q=" . rawurlencode(strip_tags($product['description'])) .
                    "&q=" . rawurlencode(strip_tags($product['short_description'])) .
                    "&q=" . rawurlencode($product['seo_title']) .
                    "&q=" . rawurlencode(strip_tags($product['seo_description'])) .
                    "&q=" . rawurlencode($product['seo_keywords']) .
                    "&q=" . rawurlencode($product['feed_name']) .
                    "&source=$sourceLang&target=$targetLang";

                $resultOrig = $result = file_get_contents($apiUrl . $params);
                $result = json_decode($result, true);
            }
            if (!isset($result['data']['translations'][0]['translatedText'])) {
                return $resultOrig . '<br>' .
                    print_r($result) .
                    'No response for product id ' . $product['id'] . '<br>' .
                    '<a href="' . $apiUrl . $params . '" target="_blank">link</a>';
            }
            $data = array(
                '_name_sk' => $result['data']['translations'][0]['translatedText'],
                '_description_sk' => $result['data']['translations'][1]['translatedText'],
                '_short_description_sk' => $result['data']['translations'][2]['translatedText'],
                '_seo_title_sk' => $result['data']['translations'][3]['translatedText'],
                '_seo_description_sk' => $result['data']['translations'][4]['translatedText'],
                '_seo_keywords_sk' => $result['data']['translations'][5]['translatedText'],
                '_feed_name_sk' => $result['data']['translations'][6]['translatedText'],
                '_slug_sk' => Str::slugize($result['data']['translations'][0]['translatedText']) . '-z' . $product['id']
            );
            DB::update($EshopProduct->getPropertyTable() . '_i18ns', $data, array(
                'conditions' => array(
                    $EshopProduct->getPropertyTable() . '_id' => $product['id']
                )
            ));
            App::setSuccessMessage(__a(__FILE__, 'Product has been successfully translated.'));
        }
    }
    
    public function admin_indexComments() {
        $Comment = App::loadModel('App', 'Comment', true);
        
        $records = $Comment->find(array(
            'fields' => '*',
            'conditions' => array(
                'Comment.foreign_model' => 'Eshop.EshopProduct',
                //'Comment.status !=' => 'enum_rejected_comment',
            ),
            'joins' => array(
                array(
                    'module' => 'Eshop',
                    'model' => 'EshopProduct',
                    'type' => 'left',
                    'conditions' => array(
                        'Comment.foreign_id = Eshop.EshopProduct.id',
                        'Comment.foreign_model' => 'Eshop.EshopProduct',
                    ),
                ),
            ),
            'order' => 'Comment.created DESC',
            'paginate' => true,
            'separate' => true,
            'moduleAlias' => array('Eshop'),
        ));
        
        $productViewLocator = App::getContentLocatorByPid('Eshop.EshopProducts.view');
        App::setSeoTitle(__a(__FILE__, 'Product comments'));
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'Product comments'),
            'records' => $records,
            'primaryKey' => 'Comment.id',
            'columns' => array(
                'Comment.status' => __a(__FILE__, 'Status'),
                'Eshop.EshopProduct.name' => __a(__FILE__, 'Product'),
                'Comment.name' => __a(__FILE__, 'User name'),
                'Comment.text' => __a(__FILE__, 'Text'),
                'Comment.rating' => __a(__FILE__, 'Rating'),
                'Comment.created' => __a(__FILE__, 'Created'),
            ),
            'renderFields' => array(
                'Comment.status' => $Comment->getEnumValues('status'),
                'Eshop.EshopProduct.name' => function($value, $name, $record) use ($productViewLocator) {
                    $locator = App::getUrl(array(
                        'locator' => $productViewLocator,
                        'args' => array($record['Eshop.EshopProduct.slug'])
                    ));
                    return Html::hyperlinkTag($locator, $value, array(
                        //'lang' => $lang,
                        'attributes' => array('target' => '_blank')
                    ));
                },
            ),
//            'renderRow' => array(
//                array(
//                    'conditions' => array('active' => 0),
//                    'attributes' => array('class' => '-run-six-inactive'),
//                )
//            ),
            'Paginator' => $Comment->Paginator,
            'recordActions' => array(
                'edit' => array(
                    'url' => '/mvc/Eshop/EshopProducts/admin_editComment',
                ),
                'delete' => array(
                    'url' => '/mvc/Eshop/EshopProducts/admin_deleteComment',
                    'confirmMessage' => __a(__FILE__, 'Please, confirm removal of item'),
                ),
            )
        ));
    }
    
    public function admin_editComment($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        $Comment = App::loadModel('App', 'Comment', true);
        
        if ($this->data) {
            if ($Comment->save($this->data['Comment'], array(
                'avoidFields' => array(
                    'run_users_id',
                    'rating',
                )
            ))) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                App::redirect(App::$url);
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
        }
        // if copy id is provided then populate new record data from copy record
        else {
            $this->data = $Comment->findFirst(array(
                'fields' => '*',
                'conditions' => array(
                    'Comment.id' => $id,
                    'Comment.foreign_model' => 'Eshop.EshopProduct',
                ),
                'joins' => array(
                    array(
                        'module' => 'Eshop',
                        'model' => 'EshopProduct',
                        'type' => 'left',
                        'conditions' => array(
                            'Comment.foreign_id = EshopProduct.id',
                            'Comment.foreign_model' => 'Eshop.EshopProduct',
                        ),
                    ),
                    array(
                        'module' => 'App',
                        'model' => 'User',
                        'type' => 'left',
                    ),
                ),
                'separate' => true,
            ));
            // get file fields real path
            $fileFields = array_keys($Comment->getPropertyFileFields());
            foreach($fileFields as $fileField) {
                if (!empty($this->data['Comment'][$fileField])) {
                    $this->data['Comment'][$fileField] = $Comment->getFileFieldUrlPath($fileField, array('file' => $this->data['Comment'][$fileField]));
                }
            }
        }
        
        // add name to title and tabTitle
        if (!empty($this->data['Comment']['name']) && !empty($this->data['EshopProduct']['name'])) {
            $actualData = $this->data;
        }
        // - name should not be empty, if so then it is error so use actual name
        else {
            $actualData = $Comment->findFirst(array(
                'fields' => array(
                    'Comment.name',
                    'EshopProduct.name',
                ),
                'conditions' => array(
                    'Comment.id' => $this->data['Comment']['id'],
                    'Comment.foreign_model' => 'Eshop.EshopProduct',
                ),
                'joins' => array(
                    array(
                        'module' => 'Eshop',
                        'model' => 'EshopProduct',
                        'type' => 'left',
                        'conditions' => array(
                            'Comment.foreign_id = EshopProduct.id',
                            'Comment.foreign_model' => 'Eshop.EshopProduct',
                        ),
                    ),
                ),
                'separate' => true,
            ));
        }
        
        App::setSeoTitle(__a(__FILE__, '&quot;%s&quot; on &quot;%s&quot;', $actualData['Comment']['name'], $actualData['EshopProduct']['name']));
        return Html::smartForm(array(
            'title' => __a(
                __FILE__, 
                'Comment of "%s" on "%s"', 
                $actualData['Comment']['name'],
                Str::fill('<a href="/' . App::getContentLocatorByPid('Eshop.EshopProducts.view') . '/:EshopProduct.slug:" target="_blank">:EshopProduct.name:</a>', $actualData)
            ),
            'data' => $this->data,
            'Model' => array('Comment' => $Comment),
            'columns' => 4,
//            'showAffix' => true,
            'fields' => array(
                'Comment.id' => array('type' => 'hidden'),
                'Comment.run_users_id' => array('type' => 'hidden'),
                'EshopProduct.name' => array('type' => 'hidden'),
                
                // BASIC PARAMETERS
                array('h1' => __a(__FILE__, 'Basic parameters')),
                array('row'),
                    array(
                        'field' => 'Comment.name',
                        'label' => __a(__FILE__, 'User name')
                    ),
                    array(
                        'field' => 'Comment.rating',
                        'label' => __a(__FILE__, 'Rating'),
                        'type' => 'display',
                    ),
                    array('if' => !empty($this->data['Comment']['run_users_id'])),
                        array(
                            'field' => 'User.email',
                            'label' => __a(__FILE__, 'E-mail'),
                            'type' => 'display'
                        ),
                    array('endif'),
                array('/row'),
                array('row', 'columns' => 2),
                    array(
                        'field' => 'Comment.text',
                        'label' => __a(__FILE__, 'Text'),
                        'type' => 'textarea'
                    ),
                array('/row'),
                array('row'),
                    array(
                        'field' => 'Comment.status',
                        'label' => __a(__FILE__, 'Status'),
                        'options' => $Comment->getEnumValues('status'),
                    ),
                    array(
                        'field' => 'Comment.created',
                        'label' => __a(__FILE__, 'Date of creation'),
                        'type' => 'display',
                    ),
                array('/row'),
            )
        ));  
    }
    
    public function admin_deleteComment($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        $Comment = App::loadModel('App', 'Comment', true);
        $this->data = $Comment->deleteBy('id', $id);
        App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }  
    
    public function getBannerCode() {
        $Product = App::loadModel('Eshop', 'EshopProduct', true);
        $product = $Product->getDetails($this->params['product_id'], array(
            'getManufacturer' => true,
        ));
        return $this->loadView(
            'EshopProducts/getBannerCode',
            array(
              'product' => $product,
              'urlAddToCart' => App::getUrl(array(
                  'module' => $this->module,
                  'controller' => 'EshopCarts',
                  'action' => 'addProducts',
                  'args' => array($product['id'])
              )),
            )
        );
    }
    
    /**
     * MVC ELEMENT
     * 
     * Generates product/categories breadcrumbs html 
     * 
     * @param type $arg
     * @return string
     */
    public function breadcrumbs($arg = null) {
        $this->displayOriginComment = true;
        if (empty($arg)) {
            //return '';
            return App::loadControllerAction('App', 'WebContents', 'breadcrumbs', $this->params, null, App::$args);
        }
        
        $options = Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        ));
        $options = array_merge(array(
            'addProductName' => false,
        ), $options);
        
        // if on product detail page then retrieve product detail
        $product = array();
        $productViewSlug = App::getContentLocatorByPid('Eshop.EshopProducts.view');
        if (SLUG === $productViewSlug) {
            $Product = $this->loadModel('EshopProduct', true);
            $product = $Product->getDetails($arg);
        }
        // get active category (this can be resolved even if on product detail)
        $activeCategorySlug = null;
        $categoryProductsIndexSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory');
        $Category = $this->loadModel('EshopProductCategory', true);
        $activeCategorySlug = $Category->resolveActiveSlug(array(
            'arg' => $arg,
            'categoryProductsIndexSlug' => $categoryProductsIndexSlug,
            'productViewSlug' => $productViewSlug,
        ));
        // get active group
        $activeGroupName = null;
        if (
            SLUG !== $productViewSlug 
            && SLUG !== $categoryProductsIndexSlug
        ) {
            $groupProductsIndexSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexGroup');
            if (SLUG === $groupProductsIndexSlug) {            
                $Group = $this->loadModel('EshopProductGroup', true);
                $activeGroupName = $Group->findFieldBy('EshopProductGroup.name', 'EshopProductGroup.slug', $arg);
            }
        }
        // get active manufacturer
        $activeManufacturerName = null;
        if (
            SLUG !== $productViewSlug 
            && SLUG !== $categoryProductsIndexSlug
            && empty($activeGroupName)
        ) {
            $manufacturerProductsIndexSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer');
            if (SLUG === $manufacturerProductsIndexSlug) {            
                $Manufacturer = $this->loadModel('EshopManufacturer', true);
                $activeManufacturerName = $Manufacturer->findFieldBy('EshopManufacturer.name', 'EshopManufacturer.slug', $arg);
            }
        }
        // get active manufacturer range
        $activeManufacturerRangeName = null;
        if (
            SLUG !== $productViewSlug 
            && SLUG !== $categoryProductsIndexSlug
            && empty($activeGroupName)
            && empty($activeManufacturerName)
        ) {
            $manufacturerRangeProductsIndexSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturerRange');
            if (SLUG === $manufacturerRangeProductsIndexSlug) {            
                $ManufacturerRange = $this->loadModel('EshopManufacturerRange', true);
                $activeManufacturerRangeName = $ManufacturerRange->findFieldBy('EshopManufacturerRange.name', 'EshopManufacturerRange.slug', $arg);
            }
        }
        // get active author
        $activeAuthorName = null;
        if (
            SLUG !== $productViewSlug 
            && SLUG !== $categoryProductsIndexSlug
            && empty($activeGroupName)
            && empty($activeManufacturerName)
            && empty($activeManufacturerRangeName)
        ) {
            $authorProductsIndexSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor');
            if (SLUG === $authorProductsIndexSlug) {            
                $Author = $this->loadModel('EshopAuthor', true);
                $activeAuthorName = $Author->findFieldBy('EshopAuthor.name', 'EshopAuthor.slug', $arg);
            }
        }
        if (
            empty($product)
            && empty($activeCategorySlug) 
            && empty($activeGroupName) 
            && empty($activeManufacturerName) 
            && empty($activeManufacturerRangeName) 
            && empty($activeAuthorName) 
        ) {
            //return '';
            return App::loadControllerAction('App', 'WebContents', 'breadcrumbs', $this->params, null, App::$args);
        }
        // get parent categories of active category 
        $parentCategories = array();
        if (!empty($activeCategorySlug)) {
            $category = $Category->findFirstBy('EshopProductCategory.slug', $activeCategorySlug, array(
                'fields' => array('id', 'path'),
            ));
            if (!empty($category['path'])) {
                $parentIds = Model::getParentIdsFromTreePath($category['path']);
                array_shift($parentIds); // remove root of category tree
                $parentIds[] = $category['id'];
                $parentCategories = $Category->findList(array(
                    'fields' => array('EshopProductCategory.name', 'EshopProductCategory.slug'),
                    'conditions' => array('id' => $parentIds),
                    'order' => array('sort ASC')
                ));
            }
        }
        
        // create breadcrumbs items
        $items = array();
        foreach ($parentCategories as $category) {
            $items[] = array(
                'url' => App::getUrl(array(
                    'locator' => $categoryProductsIndexSlug, 
                    'args' => array($category['slug']),
                )),
                'label' => $category['name'],
            );
        }
        if (!empty($product)) {
            if ($options['addProductName']) {                
                $items[] = array(
                    'label' => $product['name'],
                );
            }
        }
        elseif (!empty($activeGroupName)) {
            $items[] = array(
                'label' => $activeGroupName,
            );
        }
        elseif (!empty($activeManufacturerName)) {
            $items[] = array(
                'label' => $activeManufacturerName,
            );
        }
        elseif (!empty($activeManufacturerRangeName)) {
            $items[] = array(
                'label' => $activeManufacturerRangeName,
            );
        }
        elseif (!empty($activeAuthorName)) {
            $items[] = array(
                'label' => $activeAuthorName,
            );
        }
        // unset 'url' of last item to make it inactive (but only if not in produt detail)
        if (SLUG !== $productViewSlug) {
            $lastIndex = count($items) - 1;
            unset($items[$lastIndex]['url']);
        }
        
        return Html::breadcrumbs($items, array('class' => 'eshop-breadcrumbs') + $this->params);
    }
    
    /**
     * MVC ELEMENT
     * 
     * Generates filter menu html
     * 
     * @param type $arg
     * @return string
     */
    public function filterMenu() {
        $this->displayOriginComment = true;
        $content = App::getPropertyContent();
        if (
            empty($content['id'])
            && !empty($this->params['_content'])
        ) {
            $content = $this->params['_content'];
        }
        // do not generate if no content is loaded or not on eshop page (except home page)
        if (
            empty($content['id'])
            || 
            substr($content['pid'], 0, 6) !== 'Eshop.' 
            && SLUG !== HOME_SLUG
            ||
            $content['pid'] === 'Eshop.EshopProducts.view'
        ) {
            return '';
        }
        
        return $this->loadView('EshopProducts/filterMenu');
    }
    
    /**
     * Return data for jQuery autocomplete plugin
     * 
     * @return json string
     */
    public function admin_getAutocompleteList() {
        App::setLayout(false);
        $EshopProduct = new EshopProduct();
        $products = $EshopProduct->find(array(
            'fields' => array(
                'EshopProduct.id',
                'EshopProduct.name'
            ),
            'conditions' => array(
                'EshopProduct.name %~%' => $_GET['term']
            ),
        ));

        $result = array();
        foreach ($products as $product) {
            $result[] = array('label' => $product['name'], 'value' => $product['id']);
        }
        if (empty($result)) {
            return '[""]';
        }
        $result = json_encode($result);
        
        return $result;
    }
    
    /**
     * @deprecated Use EshopExports::exportPriceEnginesXml() instead
     * 
     * Exports all products data into price engines XML files
     * 
     * @param string $engine One of 'pricemania', 'najnakup', 'enakup', 'tovarsk', 
     *      'heureka', 'pesiazona', 'zbozicz', 'google', 'facebook'. If 'all' then all price 
     *      engines are exported at once.
     */
    public function exportPriceEnginesXml($engine = null) {
        App::setLayout(false);        
        // following engines are available for this project
        $availableEngines = array(
            //'pricemania',
            //'najnakup',
            //'enakup',
            //'tovarsk',
            'heureka',
            //'pesiazona',
            //'zbozicz',
            'google',
            'facebook',
        );
        if ($engine === 'all') {
            $engine = $availableEngines;
        }
        if (
            $engine === $availableEngines
            || in_array($engine, $availableEngines)
        ) {
            $Export = $this->loadModel('EshopExport', true);
            $progress = $Export->exportPriceEnginesXml($engine);
            return '<pre>' . print_r($progress, true) . '</pre>';
        }
    }
    
    /**
     * Updates availabilities from 'enum_presale' to 'enum_available' for all products 
     * with 'available_from' equal or older than actual date
     * 
     * @cron Launch this address as a cron once per day, the best immediatelly 
     * after midnight before all actualization import are launched
     */
    public function actualizeAvailability() {
        App::setLayout(false);
        $Product = $this->loadModel('EshopProduct', true);
        $Product->update(
            array(
                'availability' => 'enum_available'
            ), 
            array(
                'validate' => false,
                'normalize' => false,
                'conditions' => array(
                    'availability' => 'enum_presale',
                    'available_from !=' => null,
                    'available_from < NOW() - INTERVAL 15 DAY',
                )
            )
        );
     }
     
    public function admin_setSoldoutAvailability() {
        $message = __a(__FILE__, 'Kliknite na "Uložiť"');
        if ($this->data) {
            $Product = $this->loadModel('EshopProduct', true);
            $message = __a(__FILE__, 'Hotovo :)');
            // get ids of special products which should not be set soldout
            $ignoredProductIds = array();
            $voucherProductId = $this->getSetting('EshopProduct.voucherProductId');
            $ignoredProductIds[$voucherProductId] = $voucherProductId;
            $giftCardIds = $Product->findList(array(
                'conditions' => array(
                    'is_gift_card' => true,
                ),
                'fields' => array('id'),
            ));
            $ignoredProductIds = array_merge($ignoredProductIds, $giftCardIds);
            $Product->update(
                array(
                    'availability' => 'enum_soldout'
                ), 
                array(
                    'validate' => false,
                    'normalize' => false,
                    'conditions' => array(
                        'availability !=' => 'enum_soldout',
                        'id !=' => $ignoredProductIds,
                    )
                )
            );
        }
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Nastaviť stav "vypredané" všetkým produktom'),
            'columns' => 1,
            'fields' => array(
                array(
                    'field' => '_launch',
                    'type' => 'hidden', 
                    'explicitValue' => 1
                ),
                array(
                    'html' => $message,
                ),
            ),
        ));
    }
    
    /**
     * MVC SNIPPET
     * 
     * Generates photogallery
     * 
     * @param $this->params['source'] (int|string) Id or locator of source content to get images from.
     * @param $this->params['_content']['id'] (int) Product id to get images from.
     * 
     * @return string
     */
    public function photogallery() {
        $this->displayOriginComment = true;
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $defaults = array(
            'source' => null,
            '_content' => null,
            //'type' => null, // not used here, see WebContents::photogallery() how to use this
            
            // this is here just to remove it from options when separating options for Html::photogallery()
            '_text' => null,
            '_snippet' => null,
            '_snippet_generic' => null,
            '_snippet_name' => null,
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        // get options for Html::photogallery() - remove expected options of this method
        // because Html::photogallery() pass all unknown options to js library instance.
        $htmlOptions = array_diff_key($options, $defaults);
        // continue in processing for other types of galleries
        if (!empty($options['source'])) {
            if (Validate::intNumber($options['source'])) {
                $productId = $options['source'];
            }
            else {
                $productId = $Product->findFieldBy('id', 'slug', $options['source']);                
            }
        }
        elseif (!empty($options['_content']['id'])) {
            $productId = $options['_content']['id'];
        }
        else {
            return '';
        }
        // set thumb variant and view for Html::photogallery() according to type 
        // and thumb dimesion
        $thumbVariant = 'small';
        $htmlOptions['imagesView'] = array(
            'module' => $this->module, 
            'name' => 'EshopProducts/photogallery'
        );
        // get images (
        $this->loadModel('EshopProductImage');
        $Image = new EshopProductImage();
        $images = $Image->find(array(
            'fields' => array(
                'file',
                'name',
            ),
            'conditions' => array(
                'EshopProductImage.run_eshop_products_id' => $productId,
            ),
            'order' => array('EshopProductImage.sort ASC')
        ));
        // set url paths, thumbs and apply alternative thumbs
        foreach ($images as &$image) {
            $fileVariants = $Image->getFileFieldUrlPaths('file', array('file' => $image['file']));
            $image['file'] = $fileVariants['original'];
            $image['thumb'] = $fileVariants[$thumbVariant];
        }
        unset($image); // unset reference
        App::loadLib('App', 'SmartAdminLauncher');
        $Launcher = new SmartAdminLauncher(
            App::getUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => 'admin_edit',
            )),
            array(
                'triggerTitle' => __a(__FILE__, 'Edit photogallery product'),
            )
        );
        $htmlOptions['smartAdminLauncherRecordAttribute'] = $Launcher->markRecord($productId);        
        return Html::photogallery($images, $htmlOptions);
    }
    
    public function filter($arg = null) {
        $options = $this->params;

        $treeConditions = array();
        $conditions = array(
            'EshopProductCategory.active' => 1
        );

        // get system slugs
        $categoriesSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory');
        $opportunitiesSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexOpportunity');

        // set tree conditions and set urlBase
        $urlBase = $categoriesSlug;
        if (!empty($options['root_pid'])) {
            $treeConditions['EshopProductCategory.pid'] = $options['root_pid'];
            if ($options['root_pid'] == 'opportunities') {
                $urlBase = $opportunitiesSlug;
            }
        }
        elseif (!empty($options['root_id'])) {
            $treeConditions['EshopProductCategory.id'] = $options['root_id'];
        }
        else {
            $treeConditions['EshopProductCategory.pid'] = 'categories';
        }

        $Category = $this->loadModel('EshopProductCategory', true);
        $activeCategorySlug = $Category->resolveActiveSlug(array(
            'arg' => $arg,
            'categoryProductsIndexSlug' => $categoriesSlug,
        ));

        // if no active category identified then display categories menu
        if (empty($activeCategorySlug)) {
            $list = $Category->findTree(
                array(
                    'conditions' => $treeConditions,
                ),
                array(
                    'conditions' => $conditions,
                    'fields' => array(
                        'EshopProductCategory.name AS label',
                        'EshopProductCategory.slug AS url',
                    ),
                    'order' => 'EshopProductCategory.name ASC',
                )
            );
            return $this->loadView('EshopProductCategories/index', array(
                'list' => $list,
                'urlBase' => $urlBase,
                'activeItem' => $activeCategorySlug,
                'categoriesSlug' => $categoriesSlug,
                'opportunitiesSlug' => $opportunitiesSlug
            ));
        }

        // if there is an active category then display filter for that category
        // - find top level category
//        $topCategory = $Category->findFirstUpInTree(
//            array(
//                'conditions' => array(
//                    'slug' => $activeCategorySlug,
//                )
//            ),
//            array(
//                'start' => -2,
//                'fields' => array(
//                    'EshopProductCategory.id',
//                    'EshopProductCategory.name',
//                    'EshopProductCategory.slug',
//                    'EshopProductCategory.path',
//                )
//
//            )
//        );
//use the active category as the top level one
        $topCategory = $Category->findFirst(array(
            'conditions' => array(
                'slug' => $activeCategorySlug,
            ),
            'fields' => array(
                'EshopProductCategory.id',
                'EshopProductCategory.name',
                'EshopProductCategory.slug',
                'EshopProductCategory.path',
            )
        ));
        // - get categories (direct subcategories) of top category
//        $categories = $Category->findList(array(
//            'conditions' => array(
//                'active' => true,
//                'parent_id' => $topCategory['id'],
//            ),
//            'order' => 'name ASC',
//            'fields' => array(
//                'EshopProductCategory.id',
//                'EshopProductCategory.name',
//                'EshopProductCategory.slug',
//                'EshopProductCategory.path',
//            )
//        ));
        // - get all subCategories of top category
        $subCategories = $Category->findList(array(
            'conditions' => array(
                'active' => true,
                'path LIKE' => '%-' . $topCategory['id'] . '-%',
            ),
            'order' => 'name ASC',
            'fields' => array(
                'EshopProductCategory.id',
                'EshopProductCategory.name',
                'EshopProductCategory.slug',
                'EshopProductCategory.path',
            )
        ));

        // - get allCategories
        $allCategories = array($topCategory['id'] => $topCategory) + $subCategories;
        $allCategoryIds = array_keys($allCategories);
        // - get all categories product ids
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $findOptions = $Product->getFilterFindOptions(array(
            'filter_category' => $allCategoryIds
        ));
        $findOptions['fields'] = array('EshopProduct.id');
        $findOptions['key'] = 'EshopProduct.id';
        $allProductIds = array_values($Product->findList($findOptions));
        // - get product counts in categories (top + sub categories)
        $ProductXCategory = $this->loadModel('EshopProductCategoryProduct', true);
//        $productIdsXCategoryIds = $ProductXCategory->findList(array(
//            'key' => 'run_eshop_products_id',
//            'fields' => array('run_eshop_product_categories_id'),
//            'accumulate' => true,
//            'conditions' => array(
//                'run_eshop_product_categories_id' => $allCategoryIds,
//                'run_eshop_products_id' => $allProductIds,
//            )
//        ));
        $allProducts = $Product->getDetails($allProductIds, array(
            'getManufacturer' => true
        ));
        $possibleParams = array(
//            $Product->filterSubcategoryParamName => '',
            $Product->filterPriceParamName => '',
            $Product->filterManufacturerParamName => '', // *0*
        );
        $filterFields = $Product->filterFields;
        foreach ($filterFields as $field => $option) {
            $possibleParams[$option['slug']] = '';
        }
        // - check which filter params are set and how many they are
        $filterParams = array_intersect_key($this->params, $possibleParams);
        $filterParamsCount = count($filterParams);
        // check how filter itself apply to its controls
//        $categoryControlsFiltered = $filterParamsCount > 1 || $filterParamsCount === 1 && empty($filterParams[$Product->filterSubcategoryParamName]);
        foreach ($filterFields as $field => $option) {
            $filterFields[$field]['filtered'] = $filterParamsCount > 1 || $filterParamsCount === 1 && empty($filterParams[$option['slug']]);
            $filterFields[$field]['values'] = array();
        }

        $manufacturerControlsFiltered = $filterParamsCount > 1 || $filterParamsCount === 1 && empty($filterParams[$Product->filterManufacturerParamName]); //*0*
        //
        //$ageControlsFiltered = $filterParamsCount > 1 || $filterParamsCount === 1 && empty($filterParams[$Product->filterAgeParamName]);
        $priceControlsFiltered = !empty($filterParams[$Product->filterPriceParamName]) || $filterParamsCount > 0;
        // - in each case restrain filtered products to allCategories products
        $filterParams['filter_category'] = $allCategoryIds;
        // - get basic counts (counted from allProducts) for all unfiltered controls
        $manufacturers = array();

        $minPrice = null;
        $maxPrice = null;
        // - if there is a Paginator then reset get paging param for each filter change
        // so prepare pageUrlParam
        $pageUrlParam = null;
        if (!empty($Product->Paginator)) {
            $paginatorOptions = $Product->Paginator->getOptions();
            $pageUrlParam = $paginatorOptions['pageUrlParam'];
        }
        foreach ($allProducts as $productId => $product) {
//            // category
//            if (!$categoryControlsFiltered) {
//                $productCategoryIds = $productIdsXCategoryIds[$productId];
//                // aux array to avoid count one product into the same category twice
//                // (when it is placed in more subcategories of parent category)
//                $productCategories = array();
//                foreach ($productCategoryIds as $productCategoryId) {
//                    // if on top category level then do nothing, this cannot be counted into any of subcategories
//                    if ($productCategoryId === $topCategory['id']) {
//                        continue;
//                    }
//                    // find the category id (id of direct subcategory of top category)
//                    if (!empty($categories[$productCategoryId])) {
//                        $categoryId = $productCategoryId;
//                    }
//                    else {
//                        $productCategory = $allCategories[$productCategoryId];
//                        $parentIds = Model::getParentIdsFromTreePath($productCategory['path']);
//                        $categoryId = $parentIds[2];
//                    }
//                    // if subcategory belongs to not-present category (category was inactive and subcategory was active) then skip it
//                    if (empty($categories[$categoryId])) {
//                        continue;
//                    }
//                    // avoid count one product into the same category twice
//                    // (when it is placed in more subcategories of parent category)
//                    if (empty($productCategories[$categoryId]['count'])) {
//                        $productCategories[$categoryId]['count'] = 0;
//                    }
//                    if ($productCategories[$categoryId]['count'] === 1) {
//                        continue;
//                    }
//                    $productCategories[$categoryId]['count']++;
//
//                    if (empty($categories[$categoryId]['count'])) {
//                        $categories[$categoryId]['count'] = 0;
//                    }
//                    $categories[$categoryId]['count']++;
//                }
//            }

            // dimension
            foreach ($filterFields as $field => $option) {
                if (!$option['filtered'] && !is_array($product[$field])) {
                    $val = trim($product[$field]);
                    if (!empty($val)) {
                        if (empty($filterFields[$field]['values'][$val]['count'])) {
                            $filterFields[$field]['values'][$val]['count'] = 0;
                            $filterFields[$field]['values'][$val]['name'] = $val;
                        }
                        $filterFields[$field]['values'][$val]['count']++;
                    }
                    ksort($filterFields[$field]['values']);
                }
            }
            // manufacturer
            if (!$manufacturerControlsFiltered) {
                if (!empty($product['EshopManufacturer']['name'])) {
                    $manufacturer = trim($product['EshopManufacturer']['name']);
                    if (empty($manufacturers[$manufacturer]['count'])) {
                        $manufacturers[$manufacturer]['count'] = 0;
                        $manufacturers[$manufacturer]['name'] = $manufacturer;
                    }
                    $manufacturers[$manufacturer]['count']++;
                }
            }
            // prices
            if (!$priceControlsFiltered) {
                if ($minPrice === null) {
                    $minPrice = $product['price_actual_taxed'];
                    $maxPrice = $product['price_actual_taxed'];
                }
                elseif ($minPrice > $product['price_actual_taxed']) {
                    $minPrice = $product['price_actual_taxed'];
                }
                elseif ($maxPrice < $product['price_actual_taxed']) {
                    $maxPrice = $product['price_actual_taxed'];
                }
            }
        }

        if (!$manufacturerControlsFiltered) { //*0*
            ksort($manufacturers);
        }
        if (!$priceControlsFiltered) {
            $minPrice = floor($minPrice);
            $maxPrice = ceil($maxPrice);
        }
        // - if the filter is active then correct controls items and amounts accordingly
        if (!empty($filterParamsCount)) {
            // categories
//            if ($categoryControlsFiltered) {
//                $filterSubParams = $filterParams;
//                unset($filterSubParams[$Product->filterSubcategoryParamName]);
//                $findOptions = $Product->getFilterFindOptions($filterSubParams);
//                $findOptions['fields'] = array('EshopProduct.id');
//                $findOptions['key'] = 'EshopProduct.id';
//                $productIds = array_values($Product->findList($findOptions));
//                foreach ($productIds as $productId) {
//                    $productCategoryIds = $productIdsXCategoryIds[$productId];
//                    // aux array to avoid count one product into the same category twice
//                    // (when it is placed in more subcategories of parent category)
//                    $productCategories = array();
//                    foreach ($productCategoryIds as $productCategoryId) {
//                        // if on top category level then do nothing, this cannot be counted into any of subcategories
//                        if ($productCategoryId === $topCategory['id']) {
//                            continue;
//                        }
//                        // find the category id (id of direct subcategory of top category)
//                        if (!empty($categories[$productCategoryId])) {
//                            $categoryId = $productCategoryId;
//                        }
//                        else {
//                            $productCategory = $allCategories[$productCategoryId];
//                            $parentIds = Model::getParentIdsFromTreePath($productCategory['path']);
//                            $categoryId = $parentIds[2];
//                        }
//                        // if subcategory belongs to not-present category (category was inactive and subcategory was active) then skip it
//                        if (empty($categories[$categoryId])) {
//                            continue;
//                        }
//                        // avoid count one product into the same category twice
//                        // (when it is placed in more subcategories of parent category)
//                        if (empty($productCategories[$categoryId]['count'])) {
//                            $productCategories[$categoryId]['count'] = 0;
//                        }
//                        if ($productCategories[$categoryId]['count'] === 1) {
//                            continue;
//                        }
//                        $productCategories[$categoryId]['count']++;
//
//                        if (empty($categories[$categoryId]['count'])) {
//                            $categories[$categoryId]['count'] = 0;
//                        }
//                        $categories[$categoryId]['count']++;
//                    }
//                }
//            }

            // filter fields
            foreach ($filterFields as $field => $option) {
                if ($option['filtered']) {
                    $filterSubParams = $filterParams;
                    unset($filterSubParams[$option['slug']]);
                    $findOptions = $Product->getFilterFindOptions($filterSubParams);
                    $findOptions['fields'] = array('EshopProduct.id');
                    $findOptions['key'] = 'EshopProduct.id';
                    $allProductIds = array_values($Product->findList($findOptions));
                    $allProducts = $Product->getDetails($allProductIds, array(
//                        'getManufacturer' => true
                    ));
                    foreach ($allProducts as $product) {
                        $val = trim($product[$field]);
                        if (!empty($val)) {
                            if (empty($filterFields[$field]['values'][$val]['count'])) {
                                $filterFields[$field]['values'][$val]['count'] = 0;
                                $filterFields[$field]['values'][$val]['name'] = $val;
                            }
                            $filterFields[$field]['values'][$val]['count']++;
                        }
                    }
                    ksort($filterFields[$field]['values']);
                }
            }

            // manufacturers
            if ($manufacturerControlsFiltered) {
                $filterSubParams = $filterParams;
                unset($filterSubParams[$Product->filterManufacturerParamName]);
                $findOptions = $Product->getFilterFindOptions($filterSubParams);
                $findOptions['fields'] = array('EshopManufacturer.name');
                $findOptions['key'] = 'EshopProduct.id';
                $findOptions['joins'][] = array(
                    'model' => 'EshopManufacturer',
                    'type' => 'left',
                    'conditions' => array(
                        'EshopManufacturer.id = EshopProduct.run_eshop_manufacturers_id',
                    )
                );
                $productManufacturers = array_values($Product->findList($findOptions));
                foreach ($productManufacturers as $manufacturer) {
                    $manufacturer = trim($manufacturer);
                    if (!empty($manufacturer)) {
                        if (empty($manufacturers[$manufacturer]['count'])) {
                            $manufacturers[$manufacturer]['count'] = 0;
                            $manufacturers[$manufacturer]['name'] = $manufacturer;
                        }
                        $manufacturers[$manufacturer]['count']++;
                    }
                }
                ksort($manufacturers);
            }

            if ($priceControlsFiltered) {
                // set min and max price according to filter price param in URL, populate $filterResetControls
                if (!empty($filterParams[$Product->filterPriceParamName])) {
                    $prices = $Product->parseFilterPriceParam($filterParams[$Product->filterPriceParamName]);
                    if (isset($prices['minPrice'])) {
                        $minPrice = $prices['minPrice'];
                    }
                    if (isset($prices['maxPrice'])) {
                        $maxPrice = $prices['maxPrice'];
                    }
                }
                else {
                    $findOptions = $Product->getFilterFindOptions($filterParams);
                    $findOptions['fields'] = array(
                        EshopProduct::ACTUAL_PRICE_SQL . ' AS actual_price',
                        'EshopProduct.tax_rate',
                    );
                    $findOptions['literals']['fields'] = true;
                    $findOptions['key'] = 'EshopProduct.id';
                    $products = $Product->findList($findOptions);
                    $pricesAreTaxed = $this->getSetting('pricesAreTaxed');
                    foreach ($products as $product) {
                        if (!$pricesAreTaxed) {
                            $product['actual_price'] = Number::getTaxedPrice($product['actual_price'], $product['tax_rate'], 2);
                        }
                        if ($minPrice === null) {
                            $minPrice = $product['actual_price'];
                            $maxPrice = $product['actual_price'];
                        }
                        elseif ($minPrice > $product['actual_price']) {
                            $minPrice = $product['actual_price'];
                        }
                        elseif ($maxPrice < $product['actual_price']) {
                            $maxPrice = $product['actual_price'];
                        }
                    }
                    $minPrice = floor($minPrice);
                    $maxPrice = ceil($maxPrice);
                }
            }
        }

        $valuesSeparator = $Product->filterValuesSeparator;
        $filterResetControls = array();
        $parsedUrl = App::$parsedUrl;
        // if there is a Paginator then reset get paging param for each filter change
        // (pageUrlParam is retrieved here above)
        if (!empty($pageUrlParam)) {
            unset($parsedUrl['get'][$pageUrlParam]);
        }

//        // remove categories without any product and set url and active
//        $tmp = array();
//        $subcategoryParamName = $Product->filterSubcategoryParamName;
//        $subcategoryParam = Sanitize::value($filterParams[$subcategoryParamName], '');
//        $subcategoryParamItems = array_flip(Str::explode($valuesSeparator, $subcategoryParam));
//        foreach ($categories as $id => $category) {
//            if (empty($category['count'])) {
//                continue;
//            }
//            $category['url'] = $parsedUrl;
//            $newSubcategoryParamItems = $subcategoryParamItems;
//            if (isset($newSubcategoryParamItems[$category['slug']])) {
//                $active = true;
//                // deactivate it on next click
//                unset($newSubcategoryParamItems[$category['slug']]);
//                $newSubcategoryParamItems = array_flip($newSubcategoryParamItems);
//            }
//            else {
//                $active = false;
//                // activate it on next click
//                $newSubcategoryParamItems = array_flip($newSubcategoryParamItems);
//                $newSubcategoryParamItems[] = $category['slug'];
//            }
//            $category['active'] = $active;
//            if (!empty($newSubcategoryParamItems)) {
//                $category['url']['params'][$subcategoryParamName] = implode($valuesSeparator, $newSubcategoryParamItems);
//            }
//            else {
//                unset($category['url']['params'][$subcategoryParamName]);
//            }
//            $category['url'] = App::getUrl($category['url']);
//            $tmp[$id] = $category;
//        }
//        $categories = $tmp;
//        // populate reset controls
//        if (!empty($subcategoryParamItems)) {
//            $categoryNames = array();
//            foreach ($allCategories as $category) {
//                $categoryNames[$category['slug']] = $category['name'];
//            }
//            foreach($subcategoryParamItems as $item => $v) {
//                $newSubcategoryParamItems = $subcategoryParamItems;
//                unset($newSubcategoryParamItems[$item]);
//                $newSubcategoryParamItems = array_flip($newSubcategoryParamItems);
//                $url = $parsedUrl;
//                if (!empty($newSubcategoryParamItems)) {
//                    $url['params'][$subcategoryParamName] = implode($valuesSeparator, $newSubcategoryParamItems);
//                }
//                else {
//                    unset($url['params'][$subcategoryParamName]);
//                }
//                $name = Sanitize::value($categoryNames[$item], $item);
//                $filterResetControls[] = array(
//                    'url' => App::getUrl($url),
//                    'name' => __($this, 'Category') . ': ' . $name,
//                );
//            }
//        }

        // set filter fields urls and actives
        foreach ($filterFields as $field => $option) {
            $tmp = array();
            $filterFieldParam = Sanitize::value($filterParams[$option['slug']], '');
            $filterFieldParamItems = array_flip(Str::explode($valuesSeparator, $filterFieldParam));
            foreach ($option['values'] as $val) {
                $val['url'] = $parsedUrl;
                $newFilterFieldParamItems = $filterFieldParamItems;
                if (isset($newFilterFieldParamItems[$val['name']])) {
                    $active = true;
                    // deactivate it on next click
                    unset($newFilterFieldParamItems[$val['name']]);
                    $newFilterFieldParamItems = array_flip($newFilterFieldParamItems);
                } else {
                    $active = false;
                    // activate it on next click
                    $newFilterFieldParamItems = array_flip($newFilterFieldParamItems);
                    $newFilterFieldParamItems[] = $val['name'];
                }
                $val['active'] = $active;
                if (!empty($newFilterFieldParamItems)) {
                    $val['url']['params'][$option['slug']] = implode($valuesSeparator, $newFilterFieldParamItems);
                } else {
                    unset($val['url']['params'][$option['slug']]);
                }
                $val['url'] = App::getUrl($val['url']);
                $tmp[] = $val;
            }
            $filterFields[$field]['values'] = $tmp;

            // populate reset controls
            foreach($filterFieldParamItems as $item => $v) {
                $newFilterFieldParamItems = $filterFieldParamItems;
                unset($newFilterFieldParamItems[$item]);
                $newFilterFieldParamItems = array_flip($newFilterFieldParamItems);
                $url = $parsedUrl;
                if (!empty($newFilterFieldParamItems)) {
                    $url['params'][$option['slug']] = implode($valuesSeparator, $newFilterFieldParamItems);
                }
                else {
                    unset($url['params'][$option['slug']]);
                }
                $filterResetControls[] = array(
                    'url' => App::getUrl($url),
                    'name' => $option['label'] . ': ' . $item,
                );
            }
        }

        // set manufacturers url and active
        $tmp = array();
        $manufacturerParamName = $Product->filterManufacturerParamName;
        $manufacturerParam = Sanitize::value($filterParams[$manufacturerParamName], '');
        $manufacturerParamItems = array_flip(Str::explode($valuesSeparator, $manufacturerParam));
        foreach ($manufacturers as $manufacturer) {
            $manufacturer['url'] = $parsedUrl;
            $newManufacturerParamItems = $manufacturerParamItems;
            if (isset($newManufacturerParamItems[$manufacturer['name']])) {
                $active = true;
                // deactivate it on next click
                unset($newManufacturerParamItems[$manufacturer['name']]);
                $newManufacturerParamItems = array_flip($newManufacturerParamItems);
            }
            else {
                $active = false;
                // activate it on next click
                $newManufacturerParamItems = array_flip($newManufacturerParamItems);
                $newManufacturerParamItems[] = $manufacturer['name'];
            }
            $manufacturer['active'] = $active;
            if (!empty($newManufacturerParamItems)) {
                $manufacturer['url']['params'][$manufacturerParamName] = implode($valuesSeparator, $newManufacturerParamItems);
            }
            else {
                unset($manufacturer['url']['params'][$manufacturerParamName]);
            }
            $manufacturer['url'] = App::getUrl($manufacturer['url']);
            $tmp[] = $manufacturer;
        }
        $manufacturers = $tmp;
        // populate reset controls
        foreach($manufacturerParamItems as $item => $v) {
            $newManufacturerParamItems = $manufacturerParamItems;
            unset($newManufacturerParamItems[$item]);
            $newManufacturerParamItems = array_flip($newManufacturerParamItems);
            $url = $parsedUrl;
            if (!empty($newManufacturerParamItems)) {
                $url['params'][$manufacturerParamName] = implode($valuesSeparator, $newManufacturerParamItems);
            }
            else {
                unset($url['params'][$manufacturerParamName]);
            }
            $filterResetControls[] = array(
                'url' => App::getUrl($url),
                'name' => __($this, 'Manufacturer') . ': ' . $item,
            );
        }

        // set price $filterResetControls
        $priceParamName = $Product->filterPriceParamName;
        if (!empty($filterParams[$priceParamName])) {
            $url = $parsedUrl;
            unset($url['params'][$priceParamName]);
            $filterResetControls[] = array(
                'url' => App::getUrl($url),
                'name' => __($this, 'Price') . ': ' . (int)$minPrice . ' - ' . (int)$maxPrice,
            );
        }

        // get floor and ceil price
        $floorPrice = null;
        $ceilPrice = null;
        if (!empty($allProductIds)) {
            // floor price
            $product = $Product->findFirst(array(
                'fields' => array(
                    EshopProduct::ACTUAL_PRICE_SQL . ' AS actual_price',
                    'EshopProduct.tax_rate',
                ),
                'conditions' => array(
                    'EshopProduct.active' => true,
                    'EshopProduct.id' => $allProductIds,
                ),
                'order' => EshopProduct::ACTUAL_PRICE_SQL . ' ASC',
                'literals' => array(
                    'fields' => true,
                    'order' => true,
                )
            ));
            $floorPrice = $product['actual_price'];
            if (!$this->getSetting('pricesAreTaxed')) {
                $floorPrice = Number::getTaxedPrice($floorPrice, $product['tax_rate'], 2);
            }
            $floorPrice = floor($floorPrice);
            // ceil price
            $product = $Product->findFirst(array(
                'fields' => array(
                    EshopProduct::ACTUAL_PRICE_SQL . ' AS actual_price',
                    'EshopProduct.tax_rate',
                ),
                'conditions' => array(
                    'EshopProduct.active' => true,
                    'EshopProduct.id' => $allProductIds,
                ),
                'order' => EshopProduct::ACTUAL_PRICE_SQL . ' DESC',
                'literals' => array(
                    'fields' => true,
                    'order' => true,
                )
            ));
            $ceilPrice = $product['actual_price'];
            if (!$this->getSetting('pricesAreTaxed')) {
                $ceilPrice = Number::getTaxedPrice($ceilPrice, $product['tax_rate'], 2);
            }
            $ceilPrice = ceil($ceilPrice);
        }

        // if no min/max prices are retrieved (no products found for filter and no explicit filter set) then
        // set them to floor/ceil prices
        if (empty($minPrice)) {
            $minPrice = $floorPrice;
        }
        if (empty($maxPrice)) {
            $maxPrice = $ceilPrice;
        }

        return $this->loadView('EshopProducts/filter', array(
            'noProductsInTopcategory' => empty($allProductIds),
            'topCategory' => $topCategory,
        //            'categories' => $categories,

            'filterFields' => $filterFields,

            'manufacturers' => $manufacturers,
        //            'ages' => $ages,
            'minPrice' => $minPrice,
            'maxPrice' => $maxPrice,
            'floorPrice' => $floorPrice,
            'ceilPrice' => $ceilPrice,
            'filterResetControls' => $filterResetControls,
            'filterValuesSeparator' => $Product->filterValuesSeparator,
            'filterPriceParamName' => $Product->filterPriceParamName,
            'pageUrlParam' => $pageUrlParam,
        ));
    }   
    
    /**
     * CRON once per day
     * 
     * Converts EshopProduct.related_products to HABTM relation
     */
    public function insertRelatedProducts() {
        App::setLayout(false);
        // get existing relations
        App::loadModel('Eshop', 'EshopRelatedProduct');
        $RelatedProduct = new EshopRelatedProduct();
        $relatedProducts = $RelatedProduct->find(array(
           'fields' => array(
               'run_eshop_products_id',
               'run_eshop_related_products_id',
           ) 
        ));
        $tmp = array();
        foreach ($relatedProducts as $relatedProduct) {
            if (empty($tmp[$relatedProduct['run_eshop_products_id']])) {
                $tmp[$relatedProduct['run_eshop_products_id']] = array();
            }
            if (empty($tmp[$relatedProduct['run_eshop_related_products_id']])) {
                $tmp[$relatedProduct['run_eshop_related_products_id']] = array();
            }
            $tmp[$relatedProduct['run_eshop_products_id']][$relatedProduct['run_eshop_related_products_id']] = true;
            $tmp[$relatedProduct['run_eshop_related_products_id']][$relatedProduct['run_eshop_products_id']] = true;
        }
        $relatedProducts = $tmp;
        unset($tmp);
        // get products with provided related_products
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $productIds = $Product->findList(array(
            'key' => 'code',
            'fields' => array('id'),
        ));
        $products = $Product->find(array(
            'fields' => array('id', 'code', 'related_products'),
            'conditions' => array(
                'related_products !=' => array(null, ''),
            )
        ));
        // convert related_products to habtm relations
        $progress = array(
            'countCreated' => 0,
            'countSkipped' => 0,
            'countFailed' => 0,
        );
        $batch = array();
        foreach ($products as $product) {
            $productId = $product['id'];
            $relatedCodes = preg_split('/[,;]/', $product['related_products']);
            $relatedCodes = array_map('trim', $relatedCodes);
            $relatedCodes = array_filter($relatedCodes);
            foreach ($relatedCodes as $relatedCode) {
                // if no product is found for the code then fail a
                if (empty($productIds[$relatedCode])) {
                    $progress['countFailed']++;
                    continue;
                }
                $relatedProductId = $productIds[$relatedCode];
                // check for direct relation
                // - if it exists already then skip it
                if (!empty($relatedProducts[$productId][$relatedProductId])) {
                    $progress['countSkipped']++;
                }
                // - otherwise create it
                else {
                    $batch[] = array(
                        'run_eshop_products_id' => $productId,
                        'run_eshop_related_products_id' => $relatedProductId,
                    );
                    if (empty($relatedProducts[$productId])) {
                        $relatedProducts[$productId] = array();
                    }
                    $relatedProducts[$productId][$relatedProductId] = true;
                    $progress['countCreated']++;
                }
                // check for reciprocal relation
                // - if it exists already then skip it
                if (!empty($relatedProducts[$relatedProductId][$productId])) {
                    $progress['countSkipped']++;
                }
                // - otherwise create it
                else {
                    $batch[] = array(
                        'run_eshop_products_id' => $relatedProductId,
                        'run_eshop_related_products_id' => $productId,
                    );
                    if (empty($relatedProducts[$relatedProductId])) {
                        $relatedProducts[$relatedProductId] = array();
                    }
                    $relatedProducts[$relatedProductId][$productId] = true;
                    $progress['countCreated']++;
                }
            }
        }
        // save habtm relations
        $RelatedProduct->saveBatch(array(
            'create' => array(
                'EshopRelatedProduct' => $batch,
            ),
        ));
        
        return echoReadable($progress, array('return' => true));
    }

    /**
     * CRON eech 10 minutes
     * 
     * Translates german text imported (by Frankana import) to slovak.
     * Both are stored in default lang (sk) fields.
     */
    public function translateGermanTextsToSlovak() {
        App::setLayout(false);
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $progress = array();
        $Product->translate('sk', array(
            'sourceLang' => 'de',
            'rewrite' => true,
            'progress' => &$progress
        ));
        return echoReadable($progress, array('return' => true));
    }
}
