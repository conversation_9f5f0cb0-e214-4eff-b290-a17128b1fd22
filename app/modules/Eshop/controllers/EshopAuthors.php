<?php

class EshopAuthors extends SmartController {
    
    protected $model = 'EshopAuthor';

    /** 
     * @var EshopAuthor
     */
    protected $Model;
    
    public function admin_index() {
        $this->viewOptions = array(
            'columns' => array(
                'name' => __a(__FILE__, 'Author name'),
                'slug' => __a(__FILE__, 'Slug'),
            ),
            'renderFields' => array(
                'active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'slug' => '<a href="/' . App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor') . '/:slug:" target="_blank">:slug:</a>',
            ),            
        );
        $this->viewOptions['title'] = __a(__FILE__, 'Authors');
        $this->seoTitle = __a(__FILE__, 'Authors');
        return parent::admin_index();
    }
    
    public function admin_add() {
        $this->viewOptions = array(
            'fields' => array(
                array(
                    'field' => 'name',
                    'label' => __a(__FILE__, 'Author name'),
                ),
            )
        );
        $this->viewOptions['title'] = __a(__FILE__, 'New author');
        $this->seoTitle = __a(__FILE__, 'New author');
        return parent::admin_add();
    }
    
    public function admin_edit($id = null) {
        $this->viewOptions = array(
            'fields' => array(
                array(
                    'field' => 'id',
                    'type' => 'hidden',
                ),
                
                // BASIC PARAMETERS
                array('h1' => __a(__FILE__, 'Basic parameters')),
                array('row', 'columns' => array(3, 9)),
                    array('col'),
                        array(
                            'field' => 'name',
                            'label' => __a(__FILE__, 'Author name'),
                        ),
                        array(
                            'field' => 'slug',
                            'label' => __a(__FILE__, 'Slug'),
                        ),
                    array('/col'),
                    array('col'),
                        array(
                            'field' => 'description',
                            'label' => __a(__FILE__, 'Description'),
                            'style' => 'height: 108px',
                        ),
                    array('/col'),
                array('/row'),
                
                // SEO PARAMETERS
                array(
                    'h1' => __a(__FILE__, 'SEO parameters'),
                    'hint' => __a(__FILE__, 'Parameters for search engines, e.g. Google.com, Yahoo.com or Zoznam.cz')
                ),
                array('row', 'columns' => array(3,6,3)),
                    'seo_title' => array(
                        'label' => __a(__FILE__, 'SEO title'),
                        'hint' => __a(__FILE__, 'Title is displayed in browser tab and in Google search results. It is one of most important texts for Google on your page'),
                    ),
                    'seo_description' => array(
                        'label' => __a(__FILE__, 'SEO description'), 
                        'type' => 'textarea', 
                        'style' => 'height: 34px',
                        'hint' => __a(__FILE__, 'This text is mostly used by Google in search results as text under result title. Sometimes Google decides to use some other text generated from page content'),
                    ),
                    'seo_keywords' => array(
                        'label' => __a(__FILE__, 'SEO keywords'),
                        'hint' => __a(__FILE__, 'Google does not consider much keywords. Other search engines may.')
                    ),
                array('/row'),
                
                // INFO
                array('h1' => __a(__FILE__, 'Info')),
                array('row'),
                    array(
                        'field' => 'id', 
                        'label' => 'Id', 
                        'type' => 'display'
                    ),
                    array(
                        'field' => 'created',
                        'label' => __a(__FILE__, 'Date of creation'), 
                        'type' => 'display',
                    ),
                    array(
                        'field' => 'modified',
                        'label' => __a(__FILE__, 'Date of modification'), 
                        'type' => 'display',
                    ),
                array('/row'),
                
            )
        );
        $this->viewOptions['title'] = __a(__FILE__, 'Edit author "%s"');
        return parent::admin_edit($id);
    }
    
    /**
     * CRON .../mvc/Eshop/EshopAuthors/unify
     */
    public function unify() {
        App::setLayout('App', 'admin');
        $progress = $this->Model->unify();
        return '<pre>' . print_r($progress, true) . '</pre>';
    }
}
