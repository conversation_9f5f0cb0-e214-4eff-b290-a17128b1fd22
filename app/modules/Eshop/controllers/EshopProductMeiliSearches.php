<?php

class EshopProductMeiliSearches extends Controller {
    
    /**
     * @param string $_REQUEST['ids'] Optional. Single product id or an comma separated 
     *      string of such ids. If not provided then all products are (re)synchronized.
     * @param string|int|float  $_REQUEST['modifiedAfter'] Optional. If provided then synchronized are all products
     *          which have been modified after specified datetime. The datetime can be specified
     *          absolutely by a datetime string or positive integer unix timestamp. Or it can
     *          be defined relatively by negative number (integer or float) representing
     *          number of hours to go to past, e.g. -2.5 means that all products modified 
     *          before 2.5 hours and later will be synchronized. Defaults to NULL.
     * @param string|int|float  $_REQUEST['supplierProductsModifiedAfter'] Optional. The same as 'modifiedAfter'
     *          but modification is resolved from supplier products. Defaults to NULL.
     * @param bool  $_REQUEST['debug'] Optional. If TRUE then a logs are done to tmp/logs/EshopProductMeiliSearch_synchronize.log.
     *          Defaults to FALSE.
     */
    public function synchronize() {
        App::setLayout(false);
        $options = array();
        if (!empty($_REQUEST['ids'])) {
            $options['ids'] = Str::explode(',', $_REQUEST['ids']);
        }
        if (!empty($_REQUEST['modifiedAfter'])) {
            $options['modifiedAfter'] = $_REQUEST['modifiedAfter'];
        }
        if (!empty($_REQUEST['supplierProductsModifiedAfter'])) {
            $options['supplierProductsModifiedAfter'] = $_REQUEST['supplierProductsModifiedAfter'];
        }
        if (!empty($_REQUEST['debug'])) {
            $options['debug'] = $_REQUEST['debug'];
        }
        if (!empty($_REQUEST['launchedAsAsync'])) {
            $options['launchedAsAsync'] = $_REQUEST['launchedAsAsync'];
        }
        $this->loadModel('EshopProductMeiliSearch');
        $ProductSearch = new EshopProductMeiliSearch();
        $ProductSearch->synchronize($options);
    }
}
