<?php
/**
 * @class EshopOrders
 * A simple application controller extension
 */
class EshopOrders extends Controller {
    
    public function __construct(){
        parent::__construct();
        App::loadModel('Eshop', 'EshopOrder');      
    }
    
    public function admin_index($viewType = 'index') {
        /* @var $EshopOrder EshopOrder */
        $EshopOrder = $this->loadModel('EshopOrder', true);
        
        // get lang of translated fields
        $langs = App::getPropertyLangs();
        $lang = $langs['default'];

        $decimals = Eshop::getCurrencyByLang($lang, 'decimals');

        if ($viewType == 'composition') {
            $order = 'EshopOrder.created DESC';
            $limit = 200;
        } 
        else {
            $order = 'EshopOrder.created DESC';
            $limit = 25;
        }
        $records = $EshopOrder->find(array(
            'fields' => array(
                'EshopOrder.id',
                'EshopOrder.number',
                'EshopOrder.specific',
                'EshopOrder.shipment_price_taxless',
                'EshopOrder.payment_price_taxless',
                'EshopOrder.fullname',
                'EshopOrder.company_fullname',
                'EshopOrder.run_users_id',
                'EshopOrder.shipment_method_name',
                'EshopOrder.payment_method_name',
                'EshopOrder.comment',
                'EshopOrder.notes',
                'EshopOrder.products_price_actual_taxless',
                'EshopOrder.products_tax_actual',
                'ROUND(EshopOrder.order_price_actual_taxless + EshopOrder.order_tax_actual, ' . $decimals . ') as order_price_actual_taxed',
                'EshopOrder.order_price_to_pay',
                'EshopOrder.vouchers_discount',
                'EshopOrder.bonus_discount',
                'ROUND(EshopOrder.shipment_price_actual_taxless + EshopOrder.shipment_tax_actual, ' . $decimals . ') + ROUND(EshopOrder.payment_price_actual_taxless + EshopOrder.payment_tax_actual, ' . $decimals . ') as shipment_price',
                'EshopOrder.eshop_voucher_code',
                'EshopOrder.status',
                'EshopOrder.payment_status',
                'EshopOrder.payment_signed',
                'EshopOrder.created',
                'EshopOrder.exported',
            ),
            'literals' => array(
                'fields' => true
            ),
            'order' => $order,
            'limit' => $limit,
            'paginate' => true,
            'normalizeFindOptions' => true,
        ));
        // prepare products
        $products = array();
        $productIds = array();
        App::setSqlLogging(false);
        foreach ($records as $record) {
            $products[$record['id']] = $EshopOrder->getProductsDetails($record['id']);
            foreach ($products[$record['id']] as $pkey => $product) {
                $productId = $product['run_eshop_products_id'];
                $productIds[$productId] = $productId;
                $attributes = '';
                $dynAttrs = json_decode($product['dynamic_attributes']);
                if (!empty($dynAttrs)) {
                    foreach ($dynAttrs as $attrName => $attrValue) {
                        $attributes .= $attrName . ':' . $attrValue . '; ';
                    }
                }
                $staticAttrs = json_decode($product['static_attributes']);
                if (!empty($staticAttrs)) {
                    foreach ($staticAttrs as $attrName => $attrValue) {
                        // replace possible attr pids by attr values
                        if (isset($product[$attrName]['values'][$attrValue])) {
                            $attrValue = $product[$attrName]['values'][$attrValue];
                        }
                        if ($attrName !== 'variant') {
                            $attributes .= $attrName . ':' . $attrValue . '; ';
                        }
                        else {
                            $attributes .= $attrValue . '; ';
                        }
                    }
                }
                if (!empty($attributes)) {
                    $attributes = rtrim($attributes, '; ');
                    $products[$record['id']][$pkey]['name'] .= ' - ' . $attributes;
                }
                
            }
        }
        $this->loadModel('EshopSupplierProduct');
        $SupplierProduct = new EshopSupplierProduct();
        $lowestPurchasePriceSupplierProducts = $SupplierProduct->findWithLowestPurchasePrice(
            $productIds, 
            array(
                'fields' => array('supplier_pid')
            )
        );
        App::setSqlLogging();
        // calculate totals
        if ($viewType == 'composition') {
            $paginatorOptions = $EshopOrder->Paginator->getFindOptions();
            $prodTotals = $EshopOrder->findFirst(array(
                'fields' => array(
                    'SUM(EshopOrderProduct.amount) as productsAmount',
                    'SUM(ROUND(EshopOrderProduct.price_actual_taxless * EshopOrderProduct.amount, ' . $decimals . ')) as productsPriceTaxless',
                    'SUM(ROUND(EshopOrderProduct.tax_actual * EshopOrderProduct.amount, ' . $decimals . ')) as productsTax',
                    'SUM(ROUND((EshopOrderProduct.price_actual_taxless + EshopOrderProduct.tax_actual) * EshopOrderProduct.amount, ' . $decimals . ')) as productsPriceTaxed',
                ),
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopOrderProduct',
                    ),
                    array(
                        'type' => 'left',
                        'model' => 'EshopProduct',
                        'conditions' => array(
                            'EshopProduct.id = EshopOrderProduct.run_eshop_products_id'
                        )
                    ),
                ),
                'conditions' => Sanitize::value($paginatorOptions['conditions'], array()),
                'literals' => array(
                    'fields' => true,
                    'conditions' => Sanitize::value($paginatorOptions['literals']['conditions'], array()),
                ),
                'paginate' => false,
            ));
            $orders = $EshopOrder->find(array(
                'fields' => array(
                    'EshopOrder.id',
                    'ROUND(EshopOrder.shipment_price_actual_taxless + EshopOrder.shipment_tax_actual, ' . $decimals . ') + ROUND(EshopOrder.payment_price_actual_taxless + EshopOrder.payment_tax_actual, ' . $decimals . ') as shipmentPrice',
                ),
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopOrderProduct',
                    ),
                    array(
                        'type' => 'left',
                        'model' => 'EshopProduct',
                        'conditions' => array(
                            'EshopProduct.id = EshopOrderProduct.run_eshop_products_id',
                        )
                    ),
                ),
                'conditions' => Sanitize::value($paginatorOptions['conditions'], array()),
                'literals' => array(
                    'fields' => true,
                    'conditions' => Sanitize::value($paginatorOptions['literals']['conditions'], array()),
                ),
                'group' => 'EshopOrder.id',
                'paginate' => false,
            ));
            $orderTotals = array(
                'ordersCount' => 0,
                'shipmentPrice' => 0,
            );
            $orderTotals['ordersCount'] = count($orders);
            foreach ($orders as $order) {
                $orderTotals['shipmentPrice'] += $order['shipmentPrice'];
            }
            $totals = array_merge($prodTotals, $orderTotals);
//            App::debug($paginatorOptions);
//            App::debug($totals);
        }

        App::setSeoTitle(__a(__FILE__, 'Orders'));
        App::setTabClass('st-app-languages-index');

        return Html::smartIndex(array(
            'view' => array('module' => 'Eshop', 'name' => 'EshopOrders/admin_' . $viewType),
            'title' => $this->loadView('EshopOrders/admin_indexTitle', array(
                'viewType' => $viewType,
                'filterOptions' => $EshopOrder->Paginator->getOptions(),
                'filterFieldsSeparator' => $EshopOrder->Paginator->getPropertyFilterFieldsSeparator(),
                'filterExpressionSeparator' => $EshopOrder->Paginator->getPropertyFilterExpressionSeparator(),
            )),
            'records' => $records,
            'products' => $products,
            'lowestPurchasePriceSupplierProducts' => $lowestPurchasePriceSupplierProducts,
            'totals' => Sanitize::value($totals, array()),
            'primaryKey' => 'id',
            'columns' => array(
                'EshopOrder.number' => __a(__FILE__, 'Number'),
                'EshopOrder.fullname' => __a(__FILE__, 'Zákazník'),
                'EshopProduct.name' => __a(__FILE__, 'Item name'),
                'EshopOrder.order_price_to_pay' => __a(__FILE__, 'Price'),
                'EshopOrder.status' => __a(__FILE__, 'Status'),
                'EshopOrder.payment_status' => __a(__FILE__, 'Status of payment'),
                'EshopOrder.created' => __a(__FILE__, 'Dátum a čas objednania'),
                'EshopOrder.exported' => __a(__FILE__, 'Dátum a čas exportu do MRP'),
            ),
            'renderFields' => array(
                'EshopOrder.status' => $EshopOrder->getEnumValues('status'),
                'EshopOrder.payment_status' => $EshopOrder->getEnumValues('payment_status'),
            ),
////this classes are not styled yet (also payment status could be somehow emphasized (paid are red text - see uniknihy.sk)
            'renderRow' => array(
                array(
                    'conditions' => array('status' => 'enum_new_order'),
                    'attributes' => array('class' => 'order-new'), // e.g. green
                ),
                array(
                    'conditions' => array('status' => 'enum_opened_order'),
                    'attributes' => array('class' => 'order-opened'), // e.g. yellow
                ),
                array(
                    'conditions' => array('status' => 'enum_suspended_order'),
                    'attributes' => array('class' => 'order-suspended'), // e.g. gray
                ),
                array(
                    'conditions' => array('status' => 'enum_pickup_order'),
                    'attributes' => array('class' => 'order-pickup'), // e.g. blue
                ),
                array(
                    'conditions' => array('status' => 'enum_shipped_order'),
                    'attributes' => array('class' => 'order-shipped'), // e.g. violet or white
                ),
                array(
                    'conditions' => array('status' => 'enum_canceled_order'),
                    'attributes' => array('class' => 'order-cancelled'), // e.g. red or semi-transparent
                ),
            ),
            'Paginator' => $EshopOrder->Paginator,
            'paginatorOptions' => array(
                'infiniteScroll' => false,
            ),
            'actions' => array(                
                'add' => array(
                    'url' => '/mvc/Eshop/EshopOrders/admin_add',
                ),
                'composition' => array(
                    'url' => array(
                        'locator' => $viewType == 'index' ? '/mvc/Eshop/EshopOrders/admin_index/composition' : '/mvc/Eshop/EshopOrders/admin_index',
                    ),
                    'label' => $viewType == 'index' ? __a(__FILE__, 'Composition') : __a(__FILE__, 'Overview')
                ),  
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopOrders/admin_edit',
                    ),
                ),
                'print' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopOrders/admin_printDetail',
                    ),
                    'icon' => '<i class="fa fa-print"></i>',
//                    'class' => 'glyphicon glyphicon-print',
                    'target' => '_blank',
                    'title' => __a(__FILE__, 'Print'),
                    'label' => __a(__FILE__, 'Print'),
                ),
//                'geis' => array(
//                    'url' => array(
//                        'locator' => '/mvc/Eshop/EshopOrders/admin_exportGeisOrderCsv',
//                    ),
//                    'icon' => '<i class="fa fa-download"></i>',
////                    'class' => 'glyphicon glyphicon-print',
//                    'target' => '_blank',
//                    'title' => __a(__FILE__, 'Vytvoriť expedičný súbor GEIS'),
//                    'label' => __a(__FILE__, 'GEIS'),
//                ),
                'ship' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopOrders/admin_ship',
                    ),
                    'icon' => '<i class="fa fa-send-o"></i>',
                    'title' => __a(__FILE__, 'Change order status to "Shipped"'),
                    'label' => __a(__FILE__, 'Ship'),
                    'confirmMessage' => __a(__FILE__, 'Please, confirm status change to "Shipped" for order no. :number:'),
                ),                
//                'copy' => array(
//                    'url' => '/mvc/App/Users/<USER>',
//                ),
//                'delete' => array(
//                    'url' => '/mvc/Eshop/EshopOrders/admin_delete',
//                    'confirmMessage' => __a(__FILE__, 'Please, confirm removal of order no. :number:'),
//                ),
            ),
//            'bulkActions' => array(
//                'editMany' => array(
//                    'url' => '/mvc/Eshop/EshopOrders/admin_edit',
//                    //'title' => __a(__FILE__, 'Edit many'),
//                    //'icon' => '<i class="fa fa-pencil"></i>'
//                ),
//            ),
        ));        
    }
    
    public function admin_edit($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        $Order = $this->loadModel('EshopOrder', true);
        if ($this->data) {
            try {
                // force $data['id'] according the $id passed as arg
                $this->data['id'] = $id;
                if ($Order->saveAll($this->data)) {
                    App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                    App::redirect(App::$url);
                }
                App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            } 
            catch (Exception_DB_TablesReservationFailure $e) {
                App::setErrorMessage(__a(__FILE__, 'Record save has failed. Data are reserved by other process. Please try later.')); 
                App::logError($e->getMessage(), array(
                    'var' => $e,
                    'email' => true,
                ));
            }
        }
        $storedData = $Order->findFirst(array(
            'conditions' => array(
                'id' => $id
            )
        ));
        // prepare taxed total
        $storedData['shipment_price_actual_taxed'] = $storedData['shipment_price_actual_taxless'] + $storedData['shipment_tax_actual'];
        // prepare virtual field shipment_price for specific orders (e.g. delivery to abroad)
        $storedData['specific_shipment_price'] = $storedData['shipment_price_actual_taxless'];
        if (
            $storedData['specific_shipment_price'] !== null
            && $this->getSetting('pricesAreTaxed')
        ) {
            $storedData['specific_shipment_price'] += $storedData['shipment_tax_actual'];
        }
        $this->data = array_merge($storedData, (array)$this->data);

        if (App::getSetting('Eshop', 'priceAreTaxed')) {
            $vatInfo = __a(__FILE__, 'with VAT');
        } else {
            $vatInfo = __a(__FILE__, 'without VAT');
        }

        // get lang of translated fields
        $curr_price_postfix = '';

        // prepare available shipping
        $Shipment = $this->loadModel('EshopShipmentMethod', true);
        $shipmentConditions = array(
//            'EshopShipmentMethod.used_for_lang' => $lang
        );
        $shipments = $Shipment->find(array(
            'conditions' => $shipmentConditions,
            'order' => array('EshopShipmentMethod.sort ASC')
        ));

        $shipmentIds = array();
//        $packageWeight = $Cart->getWeight($wp['EshopCartProduct']);
        foreach($shipments as &$shipment) {
            // get shipmentIds to retrieve only asociated payment methods
            $shipmentIds[] = $shipment['id'];
            // update shipment price
            $shipment = $Shipment->getPrices($shipment, array(
//                    'productsPrices' => array(
//                        'products_price_taxless' => $this->data['products_price_taxless'],
//                        'products_tax' => $this->data['products_tax'],
//                        'products_price_actual_taxless' => $this->data['products_price_actual_taxless'],
//                        'products_tax_actual' => $this->data['products_tax_actual'],
//                    ),
//                    'packageWeight' => $packageWeight,
//                    'divided' => $this->data['divided'],
            ));
            if ($shipment['id'] == $this->data['run_eshop_shipment_methods_id']) {
                $shipment['price'] = round($this->data['shipment_price_actual_taxless'] + $this->data['shipment_tax_actual']);
                $shipment['shipment_price_taxless'] = $this->data['shipment_price_taxless'];
                $shipment['shipment_price_actual_taxless'] = $this->data['shipment_price_actual_taxless'];
                $shipment['shipment_tax'] = $this->data['shipment_tax'];
                $shipment['shipment_tax_actual'] = $this->data['shipment_tax_actual'];
                $shipment['shipment_price_taxed'] = $this->data['shipment_price_taxless'] + $this->data['shipment_tax'];
                $shipment['shipment_price_actual_taxed'] = $this->data['shipment_price_actual_taxless'] + $this->data['shipment_tax_actual'];
            }
        }
        unset($shipment); // unset reference
        
        // get actual shipment method
        $actualShipment = $Shipment->findFirstBy('id', $this->data['run_eshop_shipment_methods_id']);

        // prepare available payment
        $Payment = App::loadModel('Payment', 'PaymentMethod', true);
        $payments = $Payment->find(array(
            'fields' => array(
                'PaymentMethod.*',
            ),
            'conditions' => array(
//                'PaymentMethod.used_for_lang' => $lang,
                'EshopShipmentPaymentMethod.run_eshop_shipment_methods_id' => $shipmentIds,
            ),
            'joins' => array(
                array(
                    'module' => 'Eshop',
                    'model' => 'EshopShipmentPaymentMethod',
                    'type' => 'left',
                )
            ),
            //rblb//'order' => array('PaymentMethod.sort ASC'),
            'order' => array('EshopShipmentPaymentMethod.id ASC'),
            'group' => array('PaymentMethod.id'),
        ));
        foreach($payments as &$payment) {
            $payment = $Payment->getPrices($payment, array(
//                'productsPrices' => array(
//                    'products_price_taxless' => $this->data['products_price_taxless'],
//                    'products_tax' => $this->data['products_tax'],
//                    'products_price_actual_taxless' => $this->data['products_price_actual_taxless'],
//                    'products_tax_actual' => $this->data['products_tax_actual'],
//                ),
//                'divided' => $this->data['divided'],
            ));
            if ($payment['id'] == $this->data['run_payment_methods_id']) {
                $payment['price'] = round($this->data['payment_price_actual_taxless'] + $this->data['payment_tax_actual']);
                $payment['payment_price_taxless'] = $this->data['payment_price_taxless'];
                $payment['payment_price_actual_taxless'] = $this->data['payment_price_actual_taxless'];
                $payment['payment_tax'] = $this->data['payment_tax'];
                $payment['payment_tax_actual'] = $this->data['payment_tax_actual'];
                $payment['payment_price_taxed'] = $this->data['payment_price_taxless'] + $this->data['payment_tax'];
                $payment['payment_price_actual_taxed'] = $this->data['payment_price_actual_taxless'] + $this->data['payment_tax_actual'];
            }
        }
        unset($payment); // unset reference
        
        // prepare available relations between shipments and payments
        $ShipmentPayment = $this->loadModel('EshopShipmentPaymentMethod', true);
        $shipmentPayments = $ShipmentPayment->findList(array(
            'key' => 'run_eshop_shipment_methods_id',
            'fields' => array('run_payment_methods_id'),
            'conditions' => array('run_eshop_shipment_methods_id' => $shipmentIds),
            'accumulate' => true,
        ));
        // explicitly add the payment method paired with the order (it can be out
        // of defined pairs {shipmentId} => {paymentId} if the order has been paid 
        // by some fallback online payment method)
        $shipmentPayments[$this->data['run_eshop_shipment_methods_id']][] = 
            $this->data['run_payment_methods_id'];
        $shipmentPayments[$this->data['run_eshop_shipment_methods_id']] = 
            array_unique($shipmentPayments[$this->data['run_eshop_shipment_methods_id']]);
        
        // prepare products
        if (!empty($this->data['products'])) {
            $products = $this->data['products'];
        }
        else {
            $products = $Order->getProductsDetails($id, array(
                'getGiftCardVouchers' => true,
            ));
            foreach ($products as &$product) {
                $dynAttrs = json_decode($product['dynamic_attributes']);
                $product['dynamic_attributes'] = '';
                if (!empty($dynAttrs)) {
                    foreach ($dynAttrs as $attrName => $attrValue) {
                        // attrName is used to save attribute in EshopOrder::saveAll()
                        $product['dynamic_attributes'] .= $attrName . ':' . $attrValue . ';';
                    }
                    $product['dynamic_attributes'] = rtrim($product['dynamic_attributes'], ';');
                }
                $staticAttrs = json_decode($product['static_attributes']);
                $product['static_attributes'] = '';
                if (!empty($staticAttrs)) {
                    foreach ($staticAttrs as $attrName => $attrValue) {
                        // replace possible attr pids by attr values
                        if (isset($product[$attrName]['values'][$attrValue])) {
                            $attrValue = $product[$attrName]['values'][$attrValue];
                        }
                        // attrName is used to save attribute in EshopOrder::saveAll()
                        $product['static_attributes'] .= $attrName . ':' . $attrValue . '; ';
                    }
                    $product['static_attributes'] = rtrim($product['static_attributes'], '; ');
                }
                // prepare actual taxed price
                $product['price_actual_taxed'] = $product['price_actual_taxless'] + $product['tax_actual'];
                // prepare total product actual taxed price
                $product['total_price_actual_taxed'] = ($product['price_actual_taxless'] + $product['tax_actual']) * $product['amount'];
            }
            unset($product);
            // remove order products ids as keys
            $products = array_values($products);
        }
        
//        // prepare ulozenka
//        $Ulozenka = $this->loadModel('EshopUlozenka', true);
//        $ulozenkaOptions = $Ulozenka->getOptions();
        
        // prepare statuses
        $statuses = $Order->getEnumValues('status');
//        // hide some statuses for actual status
//        if ($this->data['status'] == 'enum_canceled_order') {
//            unset($statuses['enum_shipped_order']);
//        }
//        elseif ($this->data['status'] == 'enum_shipped_order') {
//            unset($statuses['enum_canceled_order']);
//        }
        
        if (!empty($this->data['created'])) {
            $this->data['_created_info'] = date('d.m.Y H:i:s', strtotime($this->data['created']));
        }
//        if ($this->data['divided']) {
//            $this->data['_divided_info'] = __a(__FILE__, 'Yes');
//        } else {
//            $this->data['_divided_info'] = __a(__FILE__, 'No');
//        }

        $Country = App::loadModel('App', 'Country', true);
        
        $this->data['_invoice_address'] = '';
        if (!empty($this->data['company_fullname'])) {
            $this->data['_invoice_address'] .= $this->data['company_fullname'] . "\n";
        }
        $this->data['_invoice_address'] .= $this->data['fullname'];
        $this->data['_invoice_address'] .= "\n" . $this->data['street'];
        $this->data['_invoice_address'] .= "\n" . trim($this->data['zip'] . ' ' . $this->data['city']);
        $this->data['_invoice_address'] .= "\n" . $Country->findFieldBy('Country.name', 'iso_code_2', $this->data['country']);
                
        $this->data['_shipping_address'] = '';
        if (!empty($this->data['delivery_company_fullname'])) {
            $this->data['_shipping_address'] .= $this->data['delivery_company_fullname'] . "\n";
        }
        $this->data['_shipping_address'] .= $this->data['delivery_fullname'];
        $this->data['_shipping_address'] .= "\n" . $this->data['delivery_street'];
        $this->data['_shipping_address'] .= "\n" . trim($this->data['delivery_zip'] . ' ' . $this->data['delivery_city']);
        $this->data['_shipping_address'] .= "\n" . $Country->findFieldBy('Country.name', 'iso_code_2', $this->data['delivery_country']);
        
        App::setSeoTitle(__a(__FILE__, '"%s"', $this->data['number']));
        $orderEditable = $Order->isEditable($id);
        $paymentWarning = '';
        if (
            isset($this->data['payment_signed'])
            && empty($this->data['payment_signed'])
            && (
                $this->data['payment_status'] === 'enum_payment_advance_paid'
                || $this->data['payment_status'] === 'enum_payment_partially_paid'
                || $this->data['payment_status'] === 'enum_payment_paid'
            )
        ) {
            $paymentWarning = __a(__FILE__, 'Unsecured response from bank');
        }
        elseif (
            $this->data['payment_status'] === 'enum_payment_manipulated'
        ) {
            $paymentWarning = __a(__FILE__, 'Manipulated response from bank');
        }
        if ($paymentWarning) {
            $paymentWarning = ' <b style="color:#ba0707">(' . $paymentWarning . ')</b>';
        }
        
        $cardPayPaymentMethod = $Payment->findFirst(array(
            'fields' => array('id'),
            'conditions' => array(
                'pid' => array('cardpay'),
            ),
        ));
        $readonlyOptions = array(
            'disabled' => 1, 
            'forceSubmit' => true,
        );
        $urlOrderExportToMrp = App::getUrl(array(
            'module' => $this->module,
            'controller' => 'EshopExports',
            'action' => 'autoexportMrpOrders',
            'args' => array(1),
            'get' => array('data' => array('orderNumber' => $this->data['number'])),
            'absolute' => true,
        ));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Edit order &quot;%s&quot;', Sanitize::value($this->data['number'])),
            'data' => $this->data,
            'curr_price_postfix' => $curr_price_postfix,
            'shipments' => $shipments,
            'payments' => $payments,
            'shipmentPayments' => $shipmentPayments,
            'products' => $products,
            'isPayable' => $Order->isPayable($id),
            'isPayableByCard' => $Order->isPayable($id, array(
                'paymentMethodMustBeSpecified' => false,
            )),
            'hasCashPaymentMethod' => (
                (int)$this->data['run_payment_methods_id'] === (int)$Payment->findFieldBy('id', 'pid', 'cash')
            ),
            'paymentUrl' => $Order->getPaymentUrl($id),
//            'ulozenka' => $ulozenkaOptions,
            'orderEditable' => $orderEditable,
            'vatInfo' => $vatInfo,
            'actions' => array(                
                'print' => array(
                    'url' => '/mvc/Eshop/EshopOrders/admin_printDetail/' . $id,
                    'icon' => '<i class="fa fa-print"></i>',
                    //'class' => 'glyphicon glyphicon-print',
                    'target' => '_blank',
                    'title' => __a(__FILE__, 'Print'),
                ),
                'geisExport' => array(
                    'url' => '/mvc/Eshop/EshopOrders/admin_exportGeisOrderCsv/' . $id,
                    'icon' => '<i class="fa fa-download"></i>',
                    //'class' => 'glyphicon glyphicon-print',
                    'target' => '_blank',
                    'label' => __a(__FILE__, 'GEIS'),
                    'title' => __a(__FILE__, 'Export for GEIS'),
                ),
                'chargeBackCardpay' => array(
                    'url' => '/mvc/Eshop/EshopOrders/admin_chargeBackCardpayPayment/' . $id,
                    'icon' => '<i class="fa fa-credit-card-alt"></i>',
                    //'icon' => '<i class="fa fa-money"></i>',
                    //'class' => 'glyphicon glyphicon-print',
                    //'target' => '_blank',
                    //'title' => __a(__FILE__, 'Storno platby'),
                    'label' => __a(__FILE__, 'Storno platby'),
                    'if' => (
                        (
                            $this->data['payment_status'] === 'enum_payment_advance_paid'
                            || $this->data['payment_status'] === 'enum_payment_partially_paid'
                            || $this->data['payment_status'] === 'enum_payment_paid'
                        )    
                        && !empty($this->data['run_payment_methods_id'])
                        && !empty($cardPayPaymentMethod)
                        && $this->data['run_payment_methods_id'] === $cardPayPaymentMethod['id']
                        && !empty($this->data['run_payment_request_logs_id'])
                    ),
                    'confirmMessage' => __a(__FILE__, 'Platba za objednávku bude vrátená zákazníkovi. Ste si istý?')
                ),
            ),
            'view' => array(
                'module' => 'Eshop',
                'name' => 'EshopOrders/admin_edit'
            ),
            'Model' => array(
                'EshopOrder' => $Order, 
            ),
            'columns' => 4,
            //'deviceSize' => 'md',
            'fields' => array(
                'id' => array('type' => 'hidden'),
                
                // BASIC OPTIONS
                array('h1' => __a(__FILE__, 'Basic parameters')),
                array('row', 'columns' => 2),
                    array('col'),
                        array('row', 'columns' => 3),
                            'number' => array('label' => __a(__FILE__, 'Number')) + $readonlyOptions,
                            'status' => array('label' => __a(__FILE__, 'Status'), 'type' => 'select', 'options' => $statuses),
                            'payment_status' => array(
                                'label' => __a(__FILE__, 'Status of payment') . $paymentWarning, 
                                'type' => 'select', 
                                'options' => $Order->getEnumValues('payment_status')
                            ),
                        array('/row'),
                        array('row', 'columns' => 3),
                            '_created_info' => array('label' => __a(__FILE__, 'Created')) + $readonlyOptions,
                            array('if' => !empty($this->data['exported'])),
                                array(
                                    'field' => 'exported',
                                    'label' => __a(__FILE__, 'Exported'),
                                    'hint' => __a(__FILE__, 'V prípade potreby znovu exportovať objednávku do MRP vymažte hodnotu poľa.'),
                                ),
                            array('else'),
                                array(
                                    'field' => '_',
                                    'label' => __a(__FILE__, 'Exported'),
                                    'type' => 'display',
                                    'explicitValue' => __a(
                                        __FILE__,
                                        '<a href="%s" target="_blank" class="btn btn-default">Exportovať do MRP</a>',
                                        $urlOrderExportToMrp
                                    ),
                                ),
                            array('endif'),
                            array('if' => !empty($this->data['eshop_voucher_code'])),
                                'eshop_voucher_code' => array('label' => __a(__FILE__, 'Voucher code')) + $readonlyOptions,
                            array('endif'),
                            //'_divided_info' => array('label' => __a(__FILE__, 'Divide order')) + $readonlyOptions,
                        array('/row'),
                        array('row', 'columns' => 3),
                            array('if' => !empty($this->data['invoice_pdf']) && is_string($this->data['invoice_pdf'])),
                                'invoice_pdf' => array(
                                    'label' => __a(__FILE__, 'Invoice'),
                                    'type' => 'display',
                                    'renderValue' => function ($value) use ($Order) {
                                        $url = $Order->getFileFieldUrlPath('invoice_pdf', array(
                                            'file' => $value,
                                        ));
                                        return '<a href="' . $url . '" target="_blank">' . $value . '</a>';
                                    }
                                ),
                            array('endif'),
                        array('/row'),
                        array('row', 'columns' => 1),
                            'comment' => array('label' => __a(__FILE__, 'Note from client'), 'type' => 'textarea', 'rows' => 3) + $readonlyOptions,
                        array('/row'),
                        array('row', 'columns' => 1),
                            'admin_comment' => array('label' => __a(__FILE__, 'Note from admin'), 'type' => 'textarea', 'rows' => 2),
                        array('/row'),
                    array('/col'),
                    array('col'),
                        'notes' => array('label' => __a(__FILE__, 'Changes in order'), 'type' => 'textarea', 'rows' => 13) + $readonlyOptions,
                        'send_admin_comment_to_client' => array('label' => __a(__FILE__, 'Send admin comment to client'), 'type' => 'checkbox'),
                    array('/col'),
                array('/row'),
                array('h1' => __a(__FILE__, 'Invoice address')),
                array('row', 'columns' => array(9, 3)),
                    array('col'),
                        array('row', 'columns' => 4),
                            'fullname' => array('label' => __a(__FILE__, 'Name'), 'class' => 'dirtable'),
                            'street' => array('label' => __a(__FILE__, 'Street'), 'class' => 'dirtable'),
                            'city' => array('label' => __a(__FILE__, 'City'), 'class' => 'dirtable'),
                            'zip' => array('label' => __a(__FILE__, 'Zip'), 'class' => 'dirtable'),
                        array('/row'),
                        array('row', 'columns' => 4),
                            'country' => array('label' => __a(__FILE__, 'Country'), 'class' => 'dirtable'),
                            'phone' => array('label' => __a(__FILE__, 'Phone'), 'class' => 'dirtable'),
                            'email' => array('label' => __a(__FILE__, 'E-mail'), 'class' => 'dirtable'),
                        array('/row'),
                        array('row', 'columns' => 4),
                            'company_fullname' => array('label' => __a(__FILE__, 'Company fullname'), 'class' => 'dirtable'),
                            'company_id_number' => array('label' => __a(__FILE__, 'Company id number'), 'class' => 'dirtable'),
                            'company_tax_number' => array('label' => __a(__FILE__, 'Company tax number'), 'class' => 'dirtable'),
                            'company_vat_number' => array('label' => __a(__FILE__, 'Company vat number'), 'class' => 'dirtable'),
                        array('/row'),
                    array('/col'),
                    array('col'),
                        '_invoice_address' => array(
                            'label' => __a(__FILE__, 'Invoice address'), 
                            'type' => 'textarea', 
                            'rows' => 5, 
                            'style' => 'cursor: text;'
                        ) + $readonlyOptions,
                    array('/col'),
                array('/row'),
                array('if' => array_key_exists('pid', $actualShipment) && $actualShipment['pid'] !== 'localPickup'),
                    array('h1' => __a(__FILE__, 'Shipping address')),
                    array('row', 'columns' => array(9, 3)),
                        array('col'),
                            array('row', 'columns' => 4),
                                'delivery_fullname' => array('label' => __a(__FILE__, 'Name'), 'class' => 'dirtable'),
                                'delivery_street' => array('label' => __a(__FILE__, 'Street'), 'class' => 'dirtable'),
                                'delivery_city' => array('label' => __a(__FILE__, 'City'), 'class' => 'dirtable'),
                                'delivery_zip' => array('label' => __a(__FILE__, 'Zip'), 'class' => 'dirtable'),
                            array('/row'),
                            array('row', 'columns' => 4),
                                'delivery_country' => array('label' => __a(__FILE__, 'Country'), 'class' => 'dirtable'),
                                'delivery_phone' => array('label' => __a(__FILE__, 'Phone'), 'class' => 'dirtable'),
                                'delivery_email' => array('label' => __a(__FILE__, 'E-mail'), 'class' => 'dirtable'),
                            array('/row'),
                            array('row', 'columns' => 4),
                                'delivery_company_fullname' => array('label' => __a(__FILE__, 'Company fullname'), 'class' => 'dirtable'),
                            array('/row'),
                        array('/col'),
                        array('col'),
                                '_shipping_address' => array(
                                    'label' => __a(__FILE__, 'Shipping address'), 
                                    'type' => 'textarea', 
                                    'rows' => 5, 
                                    'style' => 'cursor: text;'
                                ) + $readonlyOptions,
                        array('/col'),
                    array('/row'),
                array('endif'),
            ),
        ));        
    }
    
    public function admin_ship($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }

        $Order = $this->loadModel('EshopOrder', true);
        $Order->reserveTables('EshopOrders_admin_ship', array(
            'EshopOrder',
        ), array('tries' => 20, 'retryTime' => 1000));
        $order = $Order->findFirstBy('id', $id, array(
            'fields' => array('status', 'notes')
        ));
        if ($order['status'] === 'enum_shipped_order') {
            App::setInfoMessage(__a(__FILE__, 'Order has been in "Shipped" status already'));
        }
        else {
            // ATTENTION: Keep the same logic also in EshopOrder::saveAll()
            $note = EshopOrder::getNotesLog(__a(__FILE__, 'STATUS') . ': ' . __a(__FILE__, 'enum_shipped_order'));
            if (!empty($order['notes'])) {
                $notes = $order['notes'] . "\n" . $note;
            }
            if (!$Order->updateBy('id', $id, array(
                    'status' => 'enum_shipped_order',
                    'notes' => $notes,
                ), array(
                    'normalize' => false,
                    'validate' => false,
                )
            )) {
                App::setErrorMessage(__a(__FILE__, 'Order status change has failed'));
            }
            else {
                App::setSuccessMessage(__a(__FILE__, 'Order status has been changed to "Shipped"'));
                
                // ATTENTION: Keep the same logic also in EshopOrder::saveAll()
                
                // create invoice pdf for shipped orders
                // (pickup orders do not have invoices as client will get bill in shop)
                try {
                    $Order->createInvoicePdfFile($id);
                } 
                catch (Throwable $e) {
                    App::setErrorMessage(__a(__FILE__, 'Invoice creation has failed'));
                    App::logError($e->getMessage(), array(
                        'var' => $e,
                        'email' => true
                    ));
                }
                $result = $Order->sendStatusChangeMessages($id);
                if ($result['email'] === true) {
                    App::setSuccessMessage(__a(__FILE__, 'Order status change notification e-mail has been sent to client'));
                }
                elseif ($result['email'] === false) {
                    App::setErrorMessage(__a(__FILE__, 'Order status change notification e-mail has failed'));
                }
                else {
                    App::setInfoMessage(__a(__FILE__, 'Order status change notification e-mail has not been sent'));
                }
                if ($result['sms'] === true) {
                    App::setSuccessMessage(__a(__FILE__, 'Order status change notification sms has been sent to client'));
                }
                elseif ($result['sms'] === false) {
                    App::setErrorMessage(__a(__FILE__, 'Order status change notification sms has failed'));
                }
                else {
                    App::setInfoMessage(__a(__FILE__, 'Order status change notification sms has not been sent'));
                }
            }
        }
        $Order->unreserveTables('EshopOrders_admin_ship');
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }
    
    public function admin_printDetail($id = null) {
        if (!$id) {
            App::setMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/',
                'source' => App::$requestSource,
            ))));
        }
        
        $EshopOrder = $this->loadModel('EshopOrder', true);
        
        $order = $EshopOrder->findFirst(array(
            'conditions' => array(
                'id' => $id
            )
        ));
        
        $orderInserts = $EshopOrder->getInserts($id);
        
        // prepare products
        $products = $EshopOrder->getProductsDetails($id, array(
            'getActualAvailability' => true,
            'getManufacturer' => true,
            'parseAttributes' => true,
        ));
                
        $Shipment = $this->loadModel('EshopShipmentMethod', true);
        $shipment = $Shipment->findFirstBy('id', $order['run_eshop_shipment_methods_id']);
        
        App::setSeoTitle(__a(__FILE__, 'Packing slip "%s"', $order['number']));
        return $this->loadView('EshopOrders/admin_printDetail', array(
            'order' => $order,
            'products' => $products,
            'shipment' => $shipment,
            'orderInserts' => $orderInserts,
        ));
    }
    
    public function admin_delete($id = null) {
        if (!$id) {
            App::setMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/',
                'source' => App::$requestSource,
            ))));
        }
        
        $EshopOrder = $this->loadModel('EshopOrder', true);
        $EshopOrder->deleteBy('id', $id);
        App::setMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }
    
    /**
     * Order login. Standard login is not used as the order can be processed also 
     * without loging in as a 'quick order'.
     * 
     * @return html
     */
    public function login() {
        $this->displayOriginComment = true;
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        App::requireHttps();
        return $this->loadView(
            'EshopOrders/login', 
            array(
                'quickOrderAllowed' => $Order->hasQuickOrderAllowed(),
                'checkoutUrl' => App::getContentLocatorByPid('Eshop.EshopOrders.checkout'),
            ),
            $this->data
        );
    }
        
    public function checkout($stepSlug = null) {
        $this->displayOriginComment = true;
        App::requireHttps();
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder($stepSlug);
        $this->loadModel('EshopCart');
        $Cart = new EshopCart();
        // check for quick order start
        $this->data = $Order->startQuickOrder($this->data);
        
        // authenticate
        App::authenticate('Eshop', 'controlleraction', 'EshopOrders.checkout', array(
            'loginSlug' => App::getContentLocatorByPid('Eshop.EshopOrders.login'),
            'loginMessage' => __(__FILE__, 'Please login, register or continue without login'),
            'hasRights' => $Order->getCheckoutRights(),
        ));
        
        // if the order is submited duplicitly (see EshopCart::isOrdered() phpDoc)
        // or the cart is empty then redirect to new order add page which resolves 
        // this specific situation 
        $emptyCart = $Cart->isEmpty();
        $duplicitOrderSubmition = $Cart->isOrdered();
        if (
            $emptyCart
            || $duplicitOrderSubmition
        ) {
            App::redirect(App::getContentUrlByPid('Eshop.EshopOrders.add'));
        }
                
        // get checkout step
        $step = $Order->Checkout->getStep();
        
        //
        // process the submited data
        //
        if ($this->data) {
            $valid = $Order->setCheckoutData($this->data);
            if (
                // if data are invalid then we must go back and display errors
                !$valid
                // or if you submited data only to validate then return back to the step 
                // which has sent the data
                || !empty($this->data['_validate'])
            ) {
                if (!$valid) {
                    App::setMessage(__(__FILE__, 'Please complete or correct required data'));
                }
                App::redirect(App::getUrl(array(
                    'locator' => SLUG, 
                    'args' => array($Order->Checkout->getPreviousSlug())
                )));
            }
            // if last step then redirect to order add page
            if ($step == 'checkoutSubmit') {
                App::redirect(App::getContentUrlByPid('Eshop.EshopOrders.add'));
            }
        }
        
        //
        // Validate progress (do it only when data of previous step are processed)
        // 
        if (!$Order->Checkout->validateProgress()) {
            // if the progress is broken then redirect to first step of wizard
            App::clearMessages();
            App::setMessage(__(__FILE__, 'Checkout data has expired. Provide please missing data on order checkout.'));
            App::redirect(App::getUrl(SLUG));
        }
             
        //
        // prepare view params
        //
        $wp = array();
        $wp['data'] = $Order->Checkout->getParameter('data');
        $wp['errors'] = $Order->Checkout->getParameter('errors');
        $wp['required'] = $Order->getNotEmptyFields(array(
            'alternative' => $step,
            'data' => $Order->Checkout->getParameterAllSteps('data', true)
        ));
        $wp['productCount'] = $Cart->countProducts();
        $wp['isQuickOrder'] = $Order->isQuickOrder();
        $Country = App::loadModel('App', 'Country', true);
        $Shipment = $this->loadModel('EshopShipmentMethod', true);
        $countryCodes = $Shipment->getCountryCodes();
        $wp['countries'] = $Country->findList(array(
            'key' => 'Country.iso_code_2',
            'fields' => 'Country.name',
            'conditions' => array('Country.iso_code_2' => $countryCodes),
            'order' => array('Country.iso_code_2' => $countryCodes)
        ));
        $wp['hasOversizedProducts'] = $Cart->hasOversizedProducts();
        
        // load step specific data
        if ($step == 'checkoutStep01') {
            $user = App::getUser();
            if (
                !$wp['isQuickOrder']
                || !empty($user)
            ) {
                $User = App::loadModel('App', 'User', true);
                $wp['UserProfile'] = $User->findFirst(array(
                    'fields' => array(
                        '*',
                        "CONCAT(User.first_name, ' ', User.last_name) AS fullname",
                    ),
                    'conditions' => array('User.id' => $user['id']),
                    'joins' => array(
                        array(
                            'model' => 'UserProfile',
                            'type' => 'left',
                        )
                    ),
                    'literals' => array('fields' => true),
                ));
                if ($wp['UserProfile']['another_delivery_address']) {
                    $wp['UserProfile']['deliveryAddress'] = 'otherAddress';
                }
                else {
                    $wp['UserProfile']['deliveryAddress'] = 'customerAddress';
                }
                $wp['data'] = array_merge($wp['UserProfile'], (array)$wp['data']);
            }
            $wp['deliveryAddress'] = !empty($wp['data']['deliveryAddress']) ? $wp['data']['deliveryAddress'] : 'customerAddress';
        }
        elseif ($step == 'checkoutStep01a') {
            $wp['Checkout'] = $Order->Checkout->getParameterAllSteps('data', true);
            $wp['checkoutFileStore'] = '/' . trim(File::normalizeDS($Order->getPropertyCheckoutFileStore(), '/'), '/') . '/';
        }
        elseif ($step == 'checkoutStep02') {
            $wp['Checkout'] = $Order->Checkout->getParameterAllSteps('data');
            $cartProducts = $Cart->getProductsDetails(array('synchronize' => false));
            $cart = $Cart->getPrices($cartProducts);
            $Shipment = $this->loadModel('EshopShipmentMethod', true);
            // set conditions to retrieve shipment according to choosen delivery in previous step
            $deliveryAddress = $wp['Checkout']['checkoutStep01']['deliveryAddress'];
            $abroadDelivery = $Order->hasAbroadDelivery();
            $shipmentConditions = array('EshopShipmentMethod.active' => true);
            $hasDownloadableProducts = $Cart->hasDownloadableProducts();
            if ($hasDownloadableProducts === 'only') {
                $shipmentConditions['EshopShipmentMethod.pid'] = 'download';
            }
            elseif ($deliveryAddress == 'merchantAddress') {
                $shipmentConditions['EshopShipmentMethod.pid'] = 'localPickup';
            }
            elseif ($abroadDelivery) {
                $shipmentConditions[] = array(
                    'EshopShipmentMethod.delivery_country' => $Order->getDeliveryCountry(),
                    'OR',
                    'EshopShipmentMethod.pid' => 'abroadDelivery'
                );
            }
            else {
                $shipmentConditions[] = array(
                    'EshopShipmentMethod.delivery_country' => null,
                    array(
                        'EshopShipmentMethod.pid' => null,
                        'OR',
                        // show localPickup among other shipment methods
                        //rblb//'EshopShipmentMethod.pid !=' => array('localPickup', 'abroadDelivery', 'download')
                        'EshopShipmentMethod.pid !=' => array('abroadDelivery', 'download')
                    ),
                );
            }
            $wp['EshopShipmentMethod'] = $Shipment->find(array(
                'conditions' => $shipmentConditions,
                'order' => array('EshopShipmentMethod.sort ASC')
            ));
            $shipmentIds = array();
            $shipmentCount = count($wp['EshopShipmentMethod']);
            $packageWeight = $Cart->getWeight($cartProducts);
            foreach($wp['EshopShipmentMethod'] as $i => &$shipment) {
                // if abroad delivery and there are specific shipment methods for 
                // abroad country then remove the generic sone
                if (
                    $abroadDelivery 
                    && count($wp['EshopShipmentMethod']) > 1
                    && $shipment['pid'] === 'abroadDelivery'
                ) {
                    unset($wp['EshopShipmentMethod'][$i]);
                    continue;
                }
                // get shipmentIds to retrieve only asociated payment methods
                $shipmentIds[] = $shipment['id'];
                // if there is just one shipment method or if the pickup was 
                // selected in the first step then select shipment implicitly
                if (
                    $shipmentCount == 1
                    || (                                
                        $wp['Checkout']['checkoutStep01']['deliveryAddress'] == 'merchantAddress'
                        && $shipment['pid'] == 'localPickup'
                    )
                ) {
                    // if shipment was already set and it differs now then reset payment
                    if (
                        isset($wp['data']['shipment'])
                        &&$wp['data']['shipment'] != $shipment['id']
                    ) {
                        unset($wp['data']['payment']);
                    }
                    $wp['data']['shipment'] = $shipment['id'];
                }
                // update shipment price
                $shipment = $Shipment->getPrices($shipment, array(
                    'productsPrices' => $cart,
                    'packageWeight' => $packageWeight,
                    'oversizedProducts' => $wp['hasOversizedProducts'],
                ));
                if (!empty($shipment['description'])) {
                    $shipment['description'] = App::loadTextSnippets($shipment['description']);
                }
                // get GEIS POINT pickup places, but if no found then remove GEIS POINT from shipment methods
                if (
                    ($isGeisPoint = $Shipment->isGeisPoint($shipment['pid']))
                    && !($shipment['pickup_places'] = $Order->getPickupPlacesList('geisPoint'))
                ) {
                    unset($wp['EshopShipmentMethod'][$i]);
                }
                // get Zasielkovna pickup places, but if no found then remove Zasielkovna from shipment methods
                elseif (
                    ($isZasielkovna = $Shipment->isZasielkovna($shipment['pid']))
                    && !($shipment['pickup_places'] = $Order->getPickupPlacesList('zasielkovna'))
                ) {
                    unset($wp['EshopShipmentMethod'][$i]);
                }
                // prepare manually specified pickup places
                elseif (
                    !$isGeisPoint
                    && !$isZasielkovna
                    && !empty($shipment['pickup_places'])
                ) {
                    $shipment['pickup_places'] = explode("\n", $shipment['pickup_places']);
                    $shipment['pickup_places'] = array_filter(array_map('trim', $shipment['pickup_places']));
                    $shipment['pickup_places'] = array_combine($shipment['pickup_places'], $shipment['pickup_places']);
                }
            }
            unset($shipment); // unset reference
            $Payment = App::loadModel('Payment', 'PaymentMethod', true);
            $paymentConditions = array(
                'PaymentMethod.active' => true,
                'EshopShipmentPaymentMethod.run_eshop_shipment_methods_id' => $shipmentIds,
                // display also tested payment methods if there is 'tp' GET param set
                // This is used during payment methods testing on live site by bank.
                //'PaymentMethod.pid !=' => (isset($_GET['tp'])) ? '' : array('goPay'), //debug
            );
            if ($hasDownloadableProducts === 'only') {
                $paymentConditions['PaymentMethod.online'] = true;
            }
            $wp['PaymentMethod'] = $Payment->find(array(
                'fields' => array(                    
                    'PaymentMethod.id',
                    'PaymentMethod.pid',
                    'PaymentMethod.name',
                    'PaymentMethod.description',
                    'PaymentMethod.info',
                    'PaymentMethod.price',
                    'PaymentMethod.products_total_price_alternatives',
                    'PaymentMethod.info_locator',
                ),
                'conditions' => $paymentConditions,
                'joins' => array(
                    array(
                        'module' => 'Eshop',
                        'model' => 'EshopShipmentPaymentMethod',
                        'type' => 'left',
                    )
                ),
                //rblb//'order' => array('PaymentMethod.sort ASC'),
                'order' => array('EshopShipmentPaymentMethod.id ASC'),
                'group' => array('PaymentMethod.id'),
            ));
            foreach($wp['PaymentMethod'] as &$payment) {
                $payment = $Payment->getPrices($payment, array(
                    'productsPrices' => $cart,
                    'oversizedProducts' => $wp['hasOversizedProducts'],
                ));
                if (!empty($payment['description'])) {
                    $payment['description'] = App::loadTextSnippets($payment['description']);
                }
            }
            // if there is just one payment then select it implicitly
            if (count($wp['PaymentMethod']) == 1) {
                $wp['data']['payment'] = $payment['id'];
            }
            unset($payment); // unset reference
            $ShipmentPayment = $this->loadModel('EshopShipmentPaymentMethod', true);
            $wp['EshopShipmentPaymentMethod'] = $ShipmentPayment->findList(array(
                'key' => 'run_eshop_shipment_methods_id',
                'fields' => array('run_payment_methods_id'),
                'conditions' => array('run_eshop_shipment_methods_id' => $shipmentIds),
                'accumulate' => true,
            ));
        }
        elseif ($step == 'checkoutStep03') {
            // get cart products details
            $wp['EshopCartProduct'] = $Cart->getProductsDetails(array(
                'getSpecialOffers' => true,
                'synchronize' => true,
            ));
            $Cart->setAdjustedProductsAppMessages();
            if (!$wp['EshopCartProduct']) {
                App::setMessage(__(__FILE__, 'Your cart is empty'));
                App::redirect('/');
            }
            // get totals
            $wp['EshopCart'] = $Cart->getPrices($wp['EshopCartProduct']);
            $wp['Checkout'] = $Order->getNewOrderDeliveryData(
                $Order->Checkout->getParameterAllSteps('data', true)
            );
            $wp['checkoutFileStore'] = '/' . trim(File::normalizeDS($Order->getPropertyCheckoutFileStore(), '/'), '/') . '/';
            // get user info
            if (!$Order->isQuickOrder()) {
                $user = App::getUser();
                $User = App::loadModel('App', 'User', true);
                $userProfile = $User->findFirst(array(
                    'fields' => array(
                        "CONCAT(User.first_name, ' ', User.last_name) AS fullname",
                        'User.email',
                        'UserProfile.salutation',
                        'UserProfile.degree',
                        'UserProfile.street',
                        'UserProfile.city',
                        'UserProfile.country',
                        'UserProfile.zip',
                        'UserProfile.phone',
                        'UserProfile.fax',
                        'UserProfile.reference_from',
                        'UserProfile.newsletters_agreement',
                    ),
                    'conditions' => array('User.id' => $user['id']),
                    'joins' => array(
                        array(
                            'model' => 'UserProfile',
                            'type' => 'left',
                        )
                    ),
                    'literals' => array('fields' => true),
                ));
                $wp['UserProfile'] = $userProfile;
                $wp['Checkout'] = array_merge(
                    $userProfile,
                    $wp['Checkout']
                );
                // set the email explicitly
                // $wp['Checkout']['email'] = $userProfile['email'];                
            }
            
            // get shipment name & price
            $Shipment = $this->loadModel('EshopShipmentMethod', true);
            $shipment = $Shipment->findFirst(array(
                'conditions' => array(
                    'EshopShipmentMethod.id' => $wp['Checkout']['shipment']
                ),
            ));
            $shipment = $Shipment->getPrices($shipment, array(
                'productsPrices' => $wp['EshopCart'],
                'packageWeight' => $Cart->getWeight($wp['EshopCartProduct']),
            ));
            $wp['EshopShipmentMethod'] = $shipment;
            $wp['abroadDelivery'] = $Order->hasAbroadDelivery();
            // get payment name & price
            $Payment = App::loadModel('Payment', 'PaymentMethod', true);
            $payment = $Payment->findFirst(array(
                'fields' => array(
                    'PaymentMethod.name', 
                    'PaymentMethod.price',
                    'PaymentMethod.products_total_price_alternatives',
                ),
                'conditions' => array(
                    'PaymentMethod.id' => $wp['Checkout']['payment']
                ),
            ));
            $payment = $Payment->getPrices($payment, array(
                'productsPrices' => $wp['EshopCart'],
            ));
            $wp['PaymentMethod'] = $payment;
            // get order prices
            $wp['EshopOrder'] = $Order->getPrices(array(
                'productsPrices' => $wp['EshopCart'],
                'shipmentPrices' => $wp['EshopShipmentMethod'],
                'paymentPrices' => $wp['PaymentMethod'],
            ));
            // get urls
            $stepSlugs = $Order->Checkout->getSlugs();
            $wp['urlStep01'] = App::getUrl(array(
                'locator' => SLUG,
                'args' => array($stepSlugs['checkoutStep01']),
            ));
            $wp['urlStep01a'] = App::getUrl(array(
                'locator' => SLUG,
                //'args' => array($stepSlugs['checkoutStep01a']),
            ));
            $wp['urlStep02'] = App::getUrl(array(
                'locator' => SLUG,
                'args' => array($stepSlugs['checkoutStep02']),
            ));
            $wp['urlTermsAndConditions'] = App::getContentUrlByPid('Eshop.termsAndConditions');
            $wp['slugProductView'] = App::getContentLocatorByPid('Eshop.EshopProducts.view');
            // get list of possible advances
            $wp['advances'] = $Order->getAdvances($wp['EshopOrder']);
            
        } //END elseif ($step == 'checkoutStep03')
        
        // prepare urls for checkout steps navigation
        $wp['urlCartView'] = App::getContentUrlByPid('Eshop.EshopCarts.view');
        // prepare urls for previous and next steps
        if (($previousSlug = $Order->Checkout->getPreviousSlug())) {
            $wp['urlPrevious'] = App::getUrl(array(
                'locator' => SLUG,
                'args' => array($previousSlug),
            ));
        }
        else {
            // if there is no previous step, go to cart
            $wp['urlPrevious'] = App::getContentUrlByPid('Eshop.EshopCarts.view');
        }
        if (($nextSlug = $Order->Checkout->getNextSlug())) {
            $wp['urlNext'] = App::getUrl(array(
                'locator' => SLUG,
                'args' => array($nextSlug),
            ));
        }
        else {
            // it should not happen that there is no next step 
            // (the process is redirected on last valid data submit to EshopOrder::add())
            App::setMessage(__(__FILE__, 'Invalid state of order checkout. Please, contact us.'));
            App::logError('Invalid state of order checkout', array(
                'var' => array('session' => $_SESSION),
                'email' => true,
            ));
            App::redirect(App::getRefererUrl('/'));
        }
        
        return $this->loadView('EshopOrders/' . $step, $wp);
    }
        
    /**
     * Adds new order and returns it number on success.
     * This should be called as snippet on last page of order process, e.g.:
     * 
     * Your order number&nbsp;<object _snippet="Eshop.EshopOrders.add" _snippet_generic="1"></object>&nbsp;has been successfully submited.
     * 
     * @return string New order number
     */
    public function add() {
        $this->displayOriginComment = true;
        App::requireHttps();
        /* @var $Order EshopOrder */
        $Order = $this->loadModel('EshopOrder', true);
        
        // check for quick order start
        $Order->startQuickOrder();
        
        // authenticate
        App::authenticate('Eshop', 'controlleraction', 'EshopOrders.checkout', array(
            'loginSlug' => App::getContentLocatorByPid('Eshop.EshopOrders.login'),
            'loginMessage' => __(__FILE__, 'Please login, register or continue without login'),
            'hasRights' => $Order->getCheckoutRights(),
        ));
        
        $orderAddResult = $Order->add();
        // duplicit order submition
        if ($orderAddResult === -1) {
            // return order number & generate google ecommerce tracking code
            $orderId = $Order->getNewOrderId();
            $orderNumber = $Order->findField('number', array(
                'conditions' => array(
                    'id' => $orderId
                )
            ));
            return $orderNumber . 
                $Order->getGoogleEcommerceTrackingCode($orderId) .
                $Order->getFacebookConversionCode($orderId) .
                $Order->getHeurekaConversionCode($orderId, array(
                    'productsConditions' => array(
                        //rblb//'EshopOrderProduct.stock >' => 0
                        'EshopOrderProduct.availability !=' => 'enum_presale',
                    )
                ));
        }
        // missing checkout or cart data
        elseif ($orderAddResult === null) {
            if (!($newOrderId = $Order->getNewOrderId())) {
                App::setMessage(__(__FILE__, 'Your order has either expired or has been already submited'));
                App::redirect('/');
            }
            // if there is still new order id stored in session so this is 
            // just refreshed order add page - display it
            $order = $Order->findFirstBy('id', $newOrderId, array('fields' => 'number'));
            // if no order found (this can happen onl if you remove the order record from DB)
            if (empty($order)) {
                App::setMessage(__(__FILE__, 'Your order has either expired or has been already submited'));
                App::redirect('/');
            }
            return $order['number'];
        }
        // checkout data validation error
        elseif ($orderAddResult === false) {
            $OrderProduct = $this->loadModel('EshopOrderProduct', true);
            if (
                !empty($Order->Checkout)
                && ($errors = $Order->Checkout->getParameterAllSteps('errors'))
                && ($errors = array_filter($errors))
            ) {
                reset($errors);
                $invalidStep = key($errors);
                $invalidStepSlug = $Order->Checkout->getSlug($invalidStep);
                $invalidStepUrl = App::getUrl(array(
                    'locator' => App::getContentLocatorByPid('Eshop.EshopOrders.checkout'), 
                    'args' => array($invalidStepSlug)
                ));
                App::setMessage(__(__FILE__, 'Please complete or correct required data'));
                App::redirect($invalidStepUrl);
            }
            elseif (($errors = $Order->getErrors())) {
                if (!empty($errors['_processing'])) {
                    App::setMessage(__(__FILE__, 'Internal error has occured. Please, contact us.'));
                    App::logError('Invalid new order data', array(
                        'var' => array(
                            'EshopOrderErrors' => $errors,
                            'session' => $_SESSION,
                        ),
                        'email' => true,

                    ));
                }
                else {
                    App::setMessage(array_shift(array_shift($errors)));
                }
            }
            elseif (($errors = $OrderProduct->getErrors())) {
                App::setMessage(array_shift(array_shift($errors)));
                App::logError('Invalid new order product data', array(
                    'var' => array( 
                        'EshopOrderProductErrors' => $errors,
                        'session' => $_SESSION,
                    ),
                    'email' => true,
                ));
            }
            else {
                App::setMessage(__(__FILE__, 'Invalid new order data. Please, contact us.'));
                App::logError('Invalid new order data', array(
                    'var' => array(
                        'session' => $_SESSION
                    ),
                    'email' => true,
                ));
            }
            App::redirect(App::getRefererUrl('/'));
        }
        // adjusted products
        elseif (is_array($orderAddResult)) {
            $Cart = $this->loadModel('EshopCart', true);
            $Cart->setAdjustedProductsAppMessages(array(
                'adjustedProducts' => $orderAddResult
            ));
            App::redirect(App::getRefererUrl(App::getContentUrlByPid('Eshop.EshopCarts.view')));
        }
        // ok, new order number returned 
        else {
            // send message to client
            if (!$Order->sendNewOrderEmail($Order->getPropertyId())) {
                App::setMessage(__(__FILE__, 'New order message send has failed'));
                App::logError('New order message send has failed', array(
                    'email' => true
                ));
            }
            // launch "async" request for order export to MRP
            App::request(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => 'EshopExports',
                    'action' => 'autoexportMrpOrders',
                    'args' => array(1, 1),
                    'get' => array('data' => array('orderNumber' => $this->data['number'])),
                    'absolute' => true,
                )),
                array(
                    // to make a quasi "async" request use the following options
                    'timeout' => 1,
                    'returnResponse' => false,
                )
            );
            // return order number & generate google ecommerce tracking code
            return $orderAddResult . 
                $Order->getGoogleEcommerceTrackingCode($Order->getPropertyId()) .
                $Order->getFacebookConversionCode($Order->getPropertyId()) .
                $Order->getHeurekaConversionCode($Order->getPropertyId(), array(
                    'productsConditions' => array(
                        //rblb//'EshopOrderProduct.stock >' => 0
                        'EshopOrderProduct.availability !=' => 'enum_presale',
                    )
                ));
        }
    }
    
    /**
     * Gets new order payment link to be used as info on last page of order process:
     * 
     * Your order number&nbsp;<object _snippet="Eshop.EshopOrders.add" _snippet_generic="1"></object>&nbsp;has been successfully submited.
     * <object _snippet="Eshop.EshopOrders.getNewOrderPaymentLink" _snippet_generic="1"></object>
     * 
     * @return string New order payment link. The link is returned together with 
     *      corresponding text, because it is displayed only in case that the chosen
     *      payment method of order is online
     */
    public function getNewOrderPaymentLink() {
        $this->displayOriginComment = true;
        $Order = $this->loadModel('EshopOrder', true);
        if (!($newOrderId = $Order->getNewOrderId())) {
            return false;
        }
        $inserts = $Order->getInserts($newOrderId);
        return $inserts['paymentLink'];
    }
    
    /**
     * Gets new order email to be used as info on last page of order process:
     * 
     * Your order number&nbsp;<object _snippet="Eshop.EshopOrders.add" _snippet_generic="1"></object>&nbsp;has been successfully submited.
     * En email message has been send to <object _snippet="Eshop.EshopOrders.getNewOrderEmail" _snippet_generic="1"></object> address.
     * 
     * @return string New order email
     */
    public function getNewOrderEmail() {
        $this->displayOriginComment = true;
        $Order = $this->loadModel('EshopOrder', true);
        if (!($newOrderId = $Order->getNewOrderId())) {
            return false;
        }
        $inserts = $Order->getInserts($newOrderId);
        return $inserts['userEmail'];
    }
    
    /**
     * Make the payment.
     *
     * @param int $orderId Order id
     * @param int $orderToken Order token to securize public payment requests in case of
     *      quick orders
     * @param int $paymentId Order id Optional. Id of payment method to be used. Defaults to NULL.
     * @param bool $forcePaymentId Optional. Considered only if $paymentId is provided. 
     *      If FALSE then specified $paymentId is used only in case that order
     *      has no payment method attributed yet or attributed method is not active or online. 
     *      If TRUE then specified paymentId is used in any case. Defaults to FALSE.
     */
    public function pay($orderId, $orderToken, $paymentId = null, $forcePaymentId = false) {
        $Order = $this->loadModel('EshopOrder', true);
        try {
            if (!($responseLocator = App::getContentLocatorByPid('Eshop.EshopOrders.processPaymentResponse'))) {
                $responseLocator = '/mvc/Eshop/EshopOrders/processPaymentResponse/';
            }
            $Order->pay($orderId, $orderToken, $responseLocator, array(
                'paymentId' => $paymentId,
                'forcePaymentId' => $forcePaymentId,
            ));
            // if no exception is raised then this poins is not reached
            // Application redirects to payment URL
        }
        catch (Throwable $e) {
            App::setMessage($e->getMessage(), 'error');
            App::redirect('/');
        }
    }
    
    /**
     * Process the payment response
     * 
     * NOTE: There are two cases when this method can be called by bank:
     * 1] After redirecting the user back to the site once the payment is done or rejected. 
     * This url is provided to bank mostly as 'returnUrl'. Payment result email is sent
     * to user regardless to fact if the payment status has changed or no.
     * 2] Internal notification of payment status change called by bank "backend".
     * This case is distinguished by param "_nt:1" in url and is processed slightly
     * different than the case 1]:
     *      - payment result email is sent to user only if payment status has changed
     *      - there is sent also an internal email to merchant about payment status change
     * 
     * NOTE: You can call this action and display payment result screen (for testing 
     * and styling purposes) on localhost without doing any payment. Set just resultStatus
     * GET param in URL (/mvc/Eshop/EshopOrders/processPaymentResponse?resultStatus={paymentStatus}
     * or /vysledok-platby?resultStatus={paymentStatus})
     */
    public function processPaymentResponse() {
        // display response screen (see the code at the end of method)
        if (!empty($_SESSION['_eshop']['EshopOrders::processPaymentResponse()'])) {
            $params = $_SESSION['_eshop']['EshopOrders::processPaymentResponse()'];
            unset($_SESSION['_eshop']['EshopOrders::processPaymentResponse()']);
            return $this->loadView('EshopOrders/processPaymentResponse', $params);
        }
        // check if this is an internal notification response
        $isNotification = !empty($this->params['_nt']);
        $paymentStatusChanged = false;
        // allow to call this action and display payment result screen (for testing 
        // and styling purposes) on localhost without doing any payment. Set just resultStatus
        // GET param in URL (/mvc/Eshop/EshopOrders/processPaymentResponse?resultStatus={paymentStatus})
        $testing = false;
        if (ON_LOCALHOST && !empty($_GET['resultStatus'])) {
            $resultStatus = $_GET['resultStatus'];
            $testing = true;
        }
        else {            
            // if you reload the response screen then redirect to homepage
            if (empty($this->params['_pm'])) {
                App::redirect('/');
            }
            $Order = $this->loadModel('EshopOrder', true);
            try {
                $resultStatus = $Order->processPaymentResponse(array(
                    'paymentStatusChanged' => &$paymentStatusChanged
                ));
            }
            catch (Throwable $e) {
                if ($isNotification) {
                    throw $e;
                }
                App::setMessage($e->getMessage(), 'error');
                App::redirect('/');
            }
        }
        // give the answer to the customer
        switch ($resultStatus) {
            case 'enum_payment_partially_paid':
            case 'enum_payment_advance_paid':
            case 'enum_payment_paid':
                $message = __(__FILE__, 'Your payment was accepted.');
                App::setSuccessMessage($message);
                if (
                    !$testing
                    && (!$isNotification || $paymentStatusChanged)
                    && !$Order->sendPaymentSuccessfulEmail($Order->getPropertyId(), array('onceOnly' => true))
                ) {
                    App::logError('Order successful payment message send has failed', array(
                        'email' => true
                    ));
                }
                break;
                
            case 'enum_payment_none':
                // this happens on GoPay charge back
                break;
            
            case 'enum_payment_tout':
                $message = __(__FILE__, 'Your payment session has expired. Please try again');
                App::setInfoMessage($message);
                break;
            
            case 'enum_payment_manipulated':
                $message = __(__FILE__, 'We are sorry. There is some problem with your payment. Please contact our customer service.');
                App::setInfoMessage($message);
                break;
            
            case 'enum_payment_failed':
            default:
                $message = __(__FILE__, 'We are sorry. Your payment was not authorized by your bank.');
                App::setErrorMessage($message);
                if (
                    !$testing
                    && (!$isNotification || $paymentStatusChanged)
                    && !$Order->sendPaymentFailedEmail($Order->getPropertyId())
                ) {
                    App::logError('Order failed payment message send has failed', array(
                        'email' => true
                    ));
                }
                break;
        }
        // send an internal email about payment status change
        if (
            $paymentStatusChanged
            && !$Order->sendPaymentStatusChangedEmail($Order->getPropertyId())
        ) {
            App::logError('Order payment status changed message send has failed', array(
                'email' => true
            ));
        }
        // in case of internal notification do not display anything (nobody see it and 
        // bank does not wait for answer)
        if ($isNotification) {
            App::setLayout(false);
        }
        // in case of redirecting user back after payment, redirect the action on itself 
        // (to hide the response params anf to display response screen)
        // Use a temporary SESSION record for this purpose. 
        else {
            $_SESSION['_eshop']['EshopOrders::processPaymentResponse()'] = array(
                'resultStatus' => $resultStatus,
                'message' => $message,
            );
            App::redirect(App::getContentUrlByPid('Eshop.EshopOrders.processPaymentResponse'));
        }
    }    
    
    /**
     * CRON
     * 
     * Checks all unpaid orders with payment methods of Tatrabanka and if TB answers
     * that order is paid then its payment status is updated (otherwise nothing changes).
     * 
     * @return array Progress array containing amounts of processed orders
     */
    public function updateTatrabankaPaymentsStatuses() {
        App::setLayout(false);
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        $result = $Order->updateTatrabankaPaymentsStatuses();
        return '<pre>' . print_r($result, true) . '</pre>';
    }
    
    /**
     * AJAX
     * 
     * Checks payment status of specified order in Tatrabanka and returns json encoded
     * answer.
     * 
     * @param int $id Order id
     * 
     * @return string Json encoded array containing items 'paymentStatus' and 'message'.
     */
    public function admin_checkTatrabankaPaymentStatus($id = null) {
        App::setLayout(false);
        $response = array(
            'paymentStatus' => null,
            'message' => null,
        );
        try {   
            $this->loadModel('EshopOrder');
            $Order = new EshopOrder();
            $result = $Order->checkTatrabankaPaymentStatus($id);
            $response['paymentStatus'] = $result;
            if (empty($result)) {
                $response['message'] = __a(__FILE__, 'Objednávka nie je hradená platobnou metódou Tatrabanky');
            }
            elseif ($result === 'enum_payment_paid') {
                $response['message'] = __a(__FILE__, 'Tatrabanka eviduje úspešnú platbu objednávky');
            }
            elseif ($result === 'enum_payment_tout') {
                $response['message'] = __a(__FILE__, 'Tatrabanka spracováva platbu objednávky. Výsledok spracovanie skontrolujte neskôr.');
            }
            elseif ($result === 'enum_payment_failed') {
                $response['message'] = __a(__FILE__, 'Tatrabanka neeviduje platbu objednávky');
            }
            elseif ($result === 'enum_payment_none') {
                $response['message'] = __a(__FILE__, 'Tatrabanka neeviduje platbu objednávky');
            }
        } 
        catch (Throwable $ex) {
            $response['message'] = $ex->getMessage();
        }
        return json_encode($response);        
    }  
    
    public function admin_chargeBackCardpayPayment($id = null) {
        try {   
            $this->loadModel('EshopOrder');
            $Order = new EshopOrder();
            if ($Order->chargeBackCardpayPayment($id)) {
                App::setSuccessMessage(__a(__FILE__, 'Platba bola úspešne stornovaná'));
            }
            else {
                App::setErrorMessage(__a(__FILE__, 'Storno platby zlyhalo'));
            }
        } 
        catch (Throwable $e) {
            App::setErrorMessage(__a(__FILE__, 'Storno platby zlyhalo z nasledujúcou chybou: %s', $e->getMessage()));
        }
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }
    
    /**
     * Index of actual user orders. 
     * Available for logged in users in their profile section.
     */
    public function index() {
        $this->displayOriginComment = true;
        $Order = $this->loadModel('EshopOrder', true);
        $PaymentMethod = App::loadModel('Payment', 'PaymentMethod', true);
        
        $userId = App::getUser('id');
        
        // get orders of user
        $orders = $Order->find(array(
            'fields' => array(
                '*',
                'shipment_price_actual_taxless + shipment_tax_actual as shipment_price_actual_taxed',
                'payment_price_actual_taxless + payment_tax_actual as payment_price_actual_taxed',
            ),
            'conditions' => array(
                'run_users_id' => $userId,
                'status !=' => 'enum_canceled_order',
            ),
            'order' => array(
                'created DESC'
            )
        ));
        
        // get items and paymentUrl of orders
        $onlinePaymentMethods = $PaymentMethod->findList(array(
            'fields' => 'id',
            'conditions' => array(
                'active' => true,
                'online' => true,
            ),
        ));
        foreach ($orders as &$order) {
            $order['items'] = $Order->getProductsDetails($order['id']);
            $order['paymentUrl'] = null;
            if (
                isset($onlinePaymentMethods[$order['run_payment_methods_id']])
                && $Order->isPayable($order['id'])
            ) {
                $order['paymentUrl'] = $Order->getPaymentUrl($order['id']);
            }
        }
        unset($order);
        
        return $this->loadView('EshopOrders/index', array(
            'orders' => $orders,
            'liveOrderStatuses' => $Order->getPropertyLiveStatuses(),
        ));
    }
    
    /**
     * @deprecated Use EshopExports::admin_exportPohodaXmlOrder() instead
     * 
     * Generate xml file(eshop order) for system Pohoda.
     */
    public function admin_generatePohodaXmlOrder($id) {       
        
        if (empty($id) || !is_numeric($id)) {
            App::setMessage(__a(__FILE__, 'Wrong identifikator of order id'));
            return;
        }
        try {
            $Export = $this->loadModel('EshopExport', true);
            $Export->exportPohodaOrderXml($id);
            App::setMessage(__a(__FILE__, 'Pohoda xml order was successfully created'));
            return;
        }
        catch (Throwable $e) {
            App::setMessage($e->getMessage());
        }
        return;
    } 
    
    public function admin_exportGeisOrderCsv($orderId = null) {
        $Export = $this->loadModel('EshopExport', true);
        if (
            $this->data
            && $Export->exportGeisOrderCsv($orderId, $this->data)
        ) {
            App::setLayout(false);
        }
        else {
            $Order = $this->loadModel('EshopOrder', true);
            if (!($orderNumber = $Order->findFieldBy('number', 'id', $orderId))) {
                App::setErrorMessage(__a(__FILE__, 'Invalid order id %s', $orderId));
            }
            else {       
                App::setSeoTitle(__a(__FILE__, 'GEIS for "%s"', $orderNumber));
                return Html::smartForm(array(
                    'data' => $this->data,
                    'title' => __a(__FILE__, 'Vytvorenie expedičného súboru GEIS pre objednávku č. %s', $orderNumber),
                    'columns' => 4,
                    'buttonLabel' => __a(__FILE__, 'Vytvoriť súbor'),
                    'fields' => array(
                        array('row'),
                            array(
                                'field' => 'shipmentDate',
                                'label' => __a(__FILE__, 'Dátum zvozu (ak sa nevyplni tak dnešný)'),
                                'type' => 'date',
                            ),
                            array(
                                'field' => 'vs',
                                'label' => __a(__FILE__, 'Variabilný symbol'),
                            ),
                        array('/row'),
                        array('row'),
                            array(
                                'field' => 'weight',
                                'label' => __a(__FILE__, 'Váha') . ' [kg]',
                                'value' => 20,
                            ),
                            array(
                                'field' => 'volume',
                                'label' => __a(__FILE__, 'Objem') . ' [m3]',
                                'value' => 0.3,
                            ),
                        array('/row'),
                        array('row'),
                            array(
                                'field' => 'exw',
                                'label' => __a(__FILE__, 'EXW'),
                                'type' => 'checkbox',
                            ),
                            array(
                                'field' => 'type',
                                'label' => __a(__FILE__, 'Typ zákazky'),
                                'type' => 'radio',
                                'options' => array(
                                    0 => 'cargo',
                                    1 => 'parcel'
                                ),
                                'value' => 1,
                            ),
                            array(
                                'field' => 'b2c',
                                'label' => __a(__FILE__, 'Súkromna adresa'),
                                'type' => 'checkbox',
                                'value' => 1,
                            ),
                        array('/row'),
                        array('row'),
                            array(
                                'field' => 'assurance',
                                'label' => __a(__FILE__, 'Pripoistenie'),
                                'type' => 'checkbox',
                            ),
                            array(
                                'field' => 'assurancePrice',
                                'label' => __a(__FILE__, 'Cena pripoistenia'),
                            ),
                        array('/row'),
                    ),
                ));
            }
        }
    }
}