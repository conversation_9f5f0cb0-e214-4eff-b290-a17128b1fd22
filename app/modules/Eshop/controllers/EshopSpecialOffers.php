<?php

class EshopSpecialOffers extends SmartController {
    
    /**
     * Allow the model methods hinting in IDE
     * @var EshopSpecialOffer
     */
    protected $Model;
    
    public function admin_index() {
        $this->viewOptions['columns'] = array(
            'name' => __a(__FILE__, 'Názov'),
            'apply_by' =>__a(__FILE__, 'Uplatniť pre'),
            'discounted_products_price_adjustment' =>__a(__FILE__, '<PERSON>na z<PERSON>ch produktov'),
            'active_from' =>__a(__FILE__, 'Od'),
            'active_to' =>__a(__FILE__, 'Do'),
            'active' =>__a(__FILE__, 'Aktívna'),
        );
        $this->viewOptions['title'] =__a(__FILE__, 'Špeciálne ponuky');
        $this->seoTitle =__a(__FILE__, 'Špeciálne ponuky');
        return parent::admin_index();      
    }
    
    public function admin_add() {
        // save submitted data
        if ($this->data) {
            $this->saveResult = $this->Model->saveAll($this->data);
            //debug >
            if (
                $this->saveResult
                && $this->data['apply_by'] === 'cart_price_threshold'
            ) {
                try {
                    App::sendEmail(
                        Str::fill(
                            'Na vydavatel.sk pribudla špeciálna ponuka ":name:" so zľavami podľa ceny košíka. Zapracuj todoProject > 190214. Odoslané automaticky z EshopSpecialOffers::admin_edit()',
                            $this->data
                        ), 
                        '<EMAIL>',
                        array(
                            'subject' => '!!! Špeciálna ponuka so zľavami podľa ceny košíka !!!'
                        )
                    );
                    $_SESSION['_eshop']['EshopSpecialOffers']['newCartPriceThresholdOffer'][$this->saveResult['id']] = true;
                } 
                catch (Throwable $e) {}
            }
            //< debug
        }
        $this->viewOptions['SpecialOffer'] = $this->Model;
        $this->viewOptions['title'] =__a(__FILE__, 'Nová špeciálna ponuka');
        $this->seoTitle =__a(__FILE__, 'Nová špeciálna ponuka');
        $this->view = 'EshopSpecialOffers/admin_form';
        return parent::admin_add();      
    }
    
    public function admin_edit($id = null) {
        // save submitted data
        if ($this->data) {
            $this->saveResult = $this->Model->saveAll($this->data);
            //debug >
            if (
                $this->saveResult
                && $this->data['apply_by'] === 'cart_price_threshold'
                && empty($_SESSION['_eshop']['EshopSpecialOffers']['newCartPriceThresholdOffer'][$id])
            ) {
                try {
                    App::sendEmail(
                        Str::fill(
                            'Na vydavatel.sk pribudla špeciálna ponuka ":name:" so zľavami podľa ceny košíka. Zapracuj todoProject > 190214. Odoslané automaticky z EshopSpecialOffers::admin_edit()',
                            $this->data
                        ), 
                        '<EMAIL>',
                        array(
                            'subject' => '!!! Špeciálna ponuka so zľavami podľa ceny košíka !!!'
                        )
                    );
                    $_SESSION['_eshop']['EshopSpecialOffers']['newCartPriceThresholdOffer'][$id] = true;
                } 
                catch (Throwable $e) {}
            }
            //< debug
        }
        else {
            $data = $this->Model->findAll($id);
            $fielFieds = array_keys($this->Model->getPropertyFileFields());
            foreach ($fielFieds as $fileField) {
                if (!empty($data[$fileField])) {
                    $data[$fileField] = $this->Model->getFileFieldUrlPath($fileField, array(
                        'file' => $data[$fileField]
                    ));
                }
            }
            $this->viewOptions['data'] = $data;
        }
        $this->viewOptions['SpecialOffer'] = $this->Model;
        $this->viewOptions['SpecialOffer'] = $this->Model;
        $this->viewOptions['title'] =__a(__FILE__, 'Špeciálna ponuka "%s"');
        $this->view = 'EshopSpecialOffers/admin_form';
        return parent::admin_edit($id);      
    }
    
    public function menu() {
        $this->displayOriginComment = true;
        $defaults = array(
            'title' => '',
            'class' => '',
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array('separator' => '-')));
        $findOptions = $this->Model->getActiveFindOptions(array(
            'published' => true,
            'ordered' => true,
        ));
        $findOptions['conditions'] = array(
            $findOptions['conditions'],
            'EshopSpecialOffer.slug !=' => null,
            'EshopSpecialOffer.show_in_menu' => true,
        );
        $findOptions['key'] = 'EshopSpecialOffer.id';
        $findOptions['fields'] = array(
            'EshopSpecialOffer.name AS label',
            'EshopSpecialOffer.slug AS locator',
            'EshopSpecialOffer.menu_image',
        );
        $offers = $this->Model->findList($findOptions);
        if (!$offers) {
            return '';
        }
        foreach ($offers as &$offer) {
            if (!empty($offer['menu_image'])) {
                $offer['menu_image'] = $this->Model->getFileFieldUrlPath('menu_image', array(
                    'file' => $offer['menu_image'],
                    'variant' => 'menu',
                ));
                $offer['label'] = 
//                    $offer['label'] . '<br>' . 
                    '<img src="' . $offer['menu_image'] . '" title="' . $offer['label'] . '" alt="' . $offer['label'] . '">';
            }
            unset($offer['menu_image']);
        }
        unset($offer);
        // simulate tree data
        $offers = array($offers);
        //  get active slug
        $specialOffersViewSLug = App::getContentLocatorByPid('Eshop.EshopSpecialOffers.view');
        $activeSlug = (SLUG === $specialOffersViewSLug) ? Sanitize::value(App::$args[0]) : null;
        return $this->loadView('EshopSpecialOffers/menu', array(
            'offers' => $offers,
            'urlBase' => $specialOffersViewSLug,
            'activeItem' => $activeSlug,
            'title' => $options['title'],
            'class' => $options['class'],
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Upraviť špeciálnu ponuku'),
                )
            )
        ));
    }
    
    /**
     * ATTENTION: Default value '' for slug is used intentionally (instead of NULL) 
     * to avoid return the first special offer without slug if no slug is provided.
     */
    public function view($slug = '') {
        $this->displayOriginComment = true;
        $offer = $this->Model->getActive(array(
            'conditions' => array(
                'slug' => $slug
            ),
            'published' => true,
            'getDetails' => true,
        ));
        if (!$offer) {            
            return App::loadScreen('_404');
        }
        // EshopSpecialOffer::getActive() return an array of offers, take the first one
        $offer = reset($offer);
        if ($offer['obfuscate_description']) {
            $offer['description'] = Str::obfuscateHtml($offer['description'], array(
                'subject' => array('emailAddresses', 'phoneNumbers')
            ));
        }
        // load discounted and promoted products
        $this->loadModel('EshopProduct');
        $Product = new EshopProduct();
        $promotedProducts = array();
        if (
            $offer['apply_by'] === 'promoted_products_in_cart'
            || $offer['apply_by'] === 'promoted_products_cart_price_threshold'
        ) {
            $promotedProducts = $Product->getDetails($offer['promoted_product_ids'], array(
                'getManufacturer' => true,
                'getAuthors' => true,
            ));
        }
        $discountedProducts = $Product->getDetails($offer['discounted_product_ids'], array(
            'getManufacturer' => true,
            'getAuthors' => true,
        ));
        // apply offer prices
        foreach ($discountedProducts as &$product) {
            // force recounting of actual price by unsetting following prices
            unset($product['discount_price']);
            unset($product['actual_price']);
            $product['special_offer_id'] = $offer['id'];
            $product = $Product->getPrices($product, array(
                'discountingSpecialOffers' => array(
                    $offer['id'] => $offer
                )
            ));
        }
        unset($product);
        
        App::setSeoTitle($offer['seo_title']);
        App::setSeoDescription($offer['seo_description']);
        App::setSeoKeywords($offer['seo_keywords']);
        
        App::loadLib('App', 'SmartAdminLauncher');
        
        return $this->loadView('EshopSpecialOffers/view', array(
            'offer' => $offer,
            'promotedProducts' => $promotedProducts,
            'discountedProducts' => $discountedProducts,
            'OfferSmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array('triggerTitle' => __a(__FILE__, 'Upraviť špeciálnu ponuku'))
            ),
            
            // EshopProduct/index params
            'title' => null,
            'titleTag' => null,
            'emptyIndexMessage' => null,
            'recordsView' => 'EshopProducts/indexRecords',
            //'products' => null, 
            'columns' => 0,
            'imageVariant' => 'small',
            'showDisponibility' => false,
            'showAddToCartButton' => false,
            'slugProductView' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),  
            'slugAuthorProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor'),  
            'slugManufacturerProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer'),
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => 'EshopProducts',
                    'action' => 'admin_edit',
                )),
                array('triggerTitle' => __a(__FILE__, 'Edit product'))
            )
        ));
    }
}
