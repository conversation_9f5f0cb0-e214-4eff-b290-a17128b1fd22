<?php
class EshopProductImages extends Controller {
    
    /**
     * Loads gallery items for provided product id.
     * Used by AJAX
     * 
     * @param int $productId
     * 
     * @return string JSON encoded list of images records
     */
    public function admin_load($productId = null) {
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        // normalize parent id
        $Response->data = array();
        if (!empty($productId)) {
            $EshopProductImage = $this->loadModel('EshopProductImage', true);
            $images = $EshopProductImage->findBy('run_eshop_products_id', $productId, array(
                'fields' => array(
                    'EshopProductImage.id', 
                    'EshopProductImage.file', 
                    'EshopProductImage.name'
                ),
                'order' => 'sort ASC',
            ));
            foreach ($images as &$image) {
                $image['file'] = $EshopProductImage->getFileFieldUrlPath('file', array('file' => $image['file']));
            }
            $Response->data = $images;
        }
        App::setLayout('App', 'json');
        return $Response->getJson();
    }
    
    /**
     * Add new gallery item for provided product id.
     * Used by AJAX
     * 
     * @param int $productId
     * 
     * @return string JSON encoded array of new image record
     */
    public function admin_add($productId = null) {
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        // normalize parent id
        $Response->data = array();
        if (!empty($productId) && !empty($this->data)) {
            $EshopProductImage = $this->loadModel('EshopProductImage', true);
            $this->data['run_eshop_products_id'] = $productId;
            if (
                $EshopProductImage->addOrderedItem($this->data, array(
                    'groupConditions' => array('run_eshop_products_id' => $productId),
                ))
            ) {
                $data = $EshopProductImage->findFirstBy('id', $EshopProductImage->getPropertyId());
                if (!empty($data['file'])) {
                    $data['file'] = $EshopProductImage->getFileFieldUrlPath('file', array('file' => $data['file']));
                }
                $Response->data = $data;
            }
            else {
                $Response->success = false;
                $Response->errors = $EshopProductImage->getErrors();
            }
        }
        App::setLayout('App', 'json');
        return $Response->getJson();
    }
    
    /**
     * Updates gallery item for provided item id (id is present in data).
     * Used by AJAX
     * 
     * @return string JSON encoded array of updated image record
     */
    public function admin_update() {
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        if ($this->data) {
            $EshopProductImage = $this->loadModel('EshopProductImage', true);
            if ($EshopProductImage->save($this->data)) {
                $data = $EshopProductImage->findFirstBy('id', $EshopProductImage->getPropertyId());
                if (!empty($data['file'])) {
                    $data['file'] = $EshopProductImage->getFileFieldUrlPath('file', array('file' => $data['file']));
                }
                $Response->data = $data;
            }
            else {
                $Response->success = false;
                $Response->errors = $EshopProductImage->getErrors();
            }
        }
        App::setLayout('App', 'json');
        return $Response->getJson();
    }
    
    /**
     * Updates gallery item for provided item id (id is present in data).
     * Used by AJAX
     * 
     * @param int $productId
     * @param int $id
     * @param int $orderIndex
     * 
     * @return string JSON success response
     */
    public function admin_move($productId = null, $id = null, $orderIndex = null) { // debug
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        if (!empty($productId) && !empty($id)) {
            $EshopProductImage = $this->loadModel('EshopProductImage', true);
            if (!$EshopProductImage->moveOrderedItem($id, array(
                'groupConditions' => array('run_eshop_products_id' => $productId), 
                'newOrderIndex' => $orderIndex
            ))) {
                $Response->success = false;
                $Response->message = __a(__FILE__, 'Node move has failed');
            }
        }
        App::setLayout('App', 'json');
        return $Response->getJson();
    }          
    
    /**
     * Delete gallery item for provided item id (id is present in data).
     * Used by AJAX
     * 
     * @param int $productId
     * @param int $id
     * 
     * @return string JSON success response
     */
    public function admin_delete($productId = null, $id = null) {      
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        if (!empty($productId) && !empty($id)) {
            $EshopProductImage = $this->loadModel('EshopProductImage', true);
            if (!$EshopProductImage->deleteOrderedItem($id, array(
                'groupConditions' => array('run_eshop_products_id' => $productId), 
            ))) {
                $Response->success = false;
                $Response->message = __a(__FILE__, 'Node delete has failed');
            }
        }
        App::setLayout('App', 'json');
        return $Response->getJson();
    }  

}
