<?php

class EshopProductTypes extends SmartController {
    
    protected $model = 'EshopProductType';
    
    /** @var EshopProductType */
    protected $Model;
    
    public function admin_index() {
        $this->viewOptions['columns'] = array(
            'name' => __a(__FILE__, 'Názov'),
        );
        $this->viewOptions['title'] = __a(__FILE__, 'Typy produktov');
        $this->viewOptions['recordActions'] = array(
            '*',
            'delete' => array(
                'if' => array('pid' => array('', null)),
                'url' => array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_delete',
                ),
                'confirmMessage' => __a(__FILE__, 'Please, confirm removal of item :name:'),
            )
        );
        $this->seoTitle = __a(__FILE__, 'Typy produktov');
        return parent::admin_index();
    }
    
    public function admin_add() {
        $this->viewOptions['fields'] = array(
            array(
                'field' => 'name',
                'label' => __a(__FILE__, 'Názov'),
            ),
            array(
                'field' => 'variant',
                'label' => __a(__FILE__, 'Varianty'),
                'type' => 'text',
            ),
        );
        $this->viewOptions['title'] = __a(__FILE__, 'Nový typ produktu');
        $this->seoTitle = __a(__FILE__, 'Nový typ produktu');
        return parent::admin_add();
    }
    
    public function admin_edit($id = null) {
        $this->viewOptions['fields'] = array(
            array(
                'field' => 'id',
                'type' => 'hidden',
            ),
            array(
                'field' => 'name',
                'label' => __a(__FILE__, 'Názov'),
            ),
            array(
                'field' => 'variant',
                'label' => __a(__FILE__, 'Varianty'),
                'type' => 'text',
            ),
        );
        $this->viewOptions['title'] = __a($this, 'Upraviť typ produktu "%s"');
        return parent::admin_edit($id);
    }
}