<?php
/**
 * @class EshopOrders
 * A simple application controller extension
 */
class EshopOrderProducts extends Controller {
    
    public function __construct(){
        parent::__construct();
        App::loadModel('Eshop', 'EshopOrderProduct');      
    }
    
    /**
     * CRON
     * 
     * Updates reserved amount of products of new and opened orders according to 
     * products reservations on MRP stock. MRP stock reserved quantity is read from 
     * EshopProduct table and its actuality depends on regular update of products stock from MRP.
     */
    public function updateReservedAmount() {
        App::setLayout('App', 'admin');
        $this->loadModel('EshopOrderProduct');
        $OrderProduct = new EshopOrderProduct();
        return __(__FILE__, '%s order products have been updated', $OrderProduct->updateReservedAmount());
    }
    
    /**
     * CRON
     */
    public function createGiftCardVouchers() {
        App::setLayout('App', 'admin');
        $this->loadModel('EshopOrderProduct');
        $OrderProduct = new EshopOrderProduct();
        $OrderProduct->createGiftCardVouchers();
    }
    
    /**
     * CRON
     */
    public function sendExpiringGiftCardVoucherEmails() {
        App::setLayout('App', 'admin');
        $this->loadModel('EshopOrderProduct');
        $OrderProduct = new EshopOrderProduct();
        $OrderProduct->sendExpiringGiftCardVoucherEmails();
    }
    
    /**
     * @deprecated - check if this method is used (very probably no) and delete it
     * if no (delete here, in rights file, delete the view)
     */
    public function admin_indexHtmlTable() {
        $Res = new ExtResponse();
        
        $EshopOrderProduct = new EshopOrderProduct();
        
        $data = array();
        $data['items'] = $EshopOrderProduct->find(array(
            'fields' => array(
                'EshopOrderProduct.id',
                'EshopProduct.code',
                'EshopProduct.name',
                'EshopOrderProduct.amount',
                'EshopOrderProduct.price_actual_taxless',
                'EshopOrderProduct.tax_actual',
                'EshopOrderProduct.tax_rate',
                'EshopOrderProduct.static_attributes',
                'EshopOrderProduct.dynamic_attributes',
            ),
            'joins' => array(
                array(
                    'model' => 'EshopProduct',
                    'type' => 'left',
                )
            ),
            'conditions' => array(
                'EshopOrderProduct.run_eshop_orders_id' => intval($_POST['order_id'])
            )
        ));

        return $this->loadView('EshopOrderProducts/indexHtmlTable', $data);
    }
    
    /**
     * CRON launched once per day
     */
    public function updateBestsellerRelevancy($divideBy10Days = null) {
        App::setLayout(false);
        // calculate relevancy as EshopOrderProduct.amount divided by 10 each X days
        $divideBy10Days = (int)abs($divideBy10Days);
        if (!$divideBy10Days) {
            $divideBy10Days = 60;
        }
        $relevancySQL = sprintf('EshopOrderProduct.amount * POW(10, -DATEDIFF(CURDATE(), DATE(EshopOrder.created))/%s)', $divideBy10Days);
        $OrderProduct = new EshopOrderProduct();
        $OrderProduct->update(
            array(
                'bestseller_relevancy' => $relevancySQL,
            ),
            array(
                'normalize' => false,
                'validate' => false,
                'joins' => array(
                    array(                        
                        'type' => 'left',
                        'model' => 'EshopOrder',
                    ),
                ),
                'conditions' => array(
                    'EshopOrderProduct.id >' => 0 // => update all
                ),
                'literals' => array(
                    'fields' => array(
                        'bestseller_relevancy',
                    ),
                )
            )
        );
    }
}