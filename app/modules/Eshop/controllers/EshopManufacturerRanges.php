<?php

class EshopManufacturerRanges extends SmartController {
    
    protected $model = 'EshopManufacturerRange';
    
    public function admin_index() {
        $Manufacturer = $this->loadModel('EshopManufacturer', true);
        $manufacturers = $Manufacturer->findList(array(
            'fields' => array('name'),
            'order' => 'name ASC',
        ));
        $this->findOptions['fields'] = array(
                'EshopManufacturerRange.id',
                'EshopManufacturerRange.name',
                'EshopManufacturerRange.slug',
                'EshopManufacturerRange.run_eshop_manufacturers_id',
            
        );
        $this->findOptions['separate'] = true;
        $this->findOptions['normalizeFindOptions'] = function($options) {
            $options = $this->Model->normalizeFindOptions($options);
            // normalize ordering by EshopManufacturerRange.run_eshop_manufacturers_id
            if (!empty($options['order'])) {
                $options['order'] = (array)$options['order'];
                $options['literals']['order'] = (array)Sanitize::value($options['literals']['order']);
                $options['joins'] = (array)Sanitize::value($options['joins']);
                // $_GET['sort']['EshopManufacturerRange.run_eshop_manufacturers_id']
                $paths = Arr::search($options['order'], '/`EshopManufacturerRange`.`run_eshop_manufacturers_id`/', array(
                    'comparison' => 'regex', 
                    'separator' => '/'
                ));
                if (!empty($paths)) {
                    foreach ($paths as $path) {
                        $value = Arr::getPath($options['order'], $path, '/');
                        $direction = explode(' ', $value);
                        $direction = end($direction);
                        Arr::setPath($options['order'], $path, 'EshopManufacturer.name ' . $direction, '/');
                    }
                    $options['joins'][] = array(
                        'model' => 'EshopManufacturer',
                        'type' => 'left',
                    );
                }
            }
            return $options;
        };
        $this->viewOptions = array(
            'columns' => array(
                'EshopManufacturerRange.name' => __a(__FILE__, 'Name'),
                'EshopManufacturerRange.slug' => __a(__FILE__, 'Slug'),
                'EshopManufacturerRange.run_eshop_manufacturers_id' => __a(__FILE__, 'Manufacturer'),
            ),
            'renderFields' => array(
                'EshopManufacturerRange.slug' => '<a href="/' . App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturerRange') . '/:EshopManufacturerRange.slug:" target="_blank">:EshopManufacturerRange.slug:</a>',
                'EshopManufacturerRange.run_eshop_manufacturers_id' => $manufacturers,
            )
        );
        $this->viewOptions['title'] = __a(__FILE__, 'Manufacturer ranges');
        $this->seoTitle = __a(__FILE__, 'Manufacturer ranges');
        return parent::admin_index();
    }
    
    public function admin_add() {
        $Manufacturer = $this->loadModel('EshopManufacturer', true);
        $manufacturers = $Manufacturer->findList(array(
            'fields' => array('name'),
            'order' => 'name ASC',
        ));
        $this->viewOptions = array(
            'fields' => array(
                array(
                    'field' => 'name',
                    'label' => __a(__FILE__, 'Name'),
                ),
                array(
                    'field' => 'run_eshop_manufacturers_id',
                    'label' => __a(__FILE__, 'Manufacturer'),
                    'type' => 'select',
                    'options' => $manufacturers,
                    'empty' => true,
                ),
            )
        );
        $this->viewOptions['title'] = __a(__FILE__, 'New manufacturer range');
        $this->seoTitle = __a(__FILE__, 'New manufacturer range');
        return parent::admin_add();
    }
    
    public function admin_edit($id = null) {
        $Manufacturer = $this->loadModel('EshopManufacturer', true);
        $manufacturers = $Manufacturer->findList(array(
            'fields' => array('name'),
            'order' => 'name ASC',
        ));
        $this->viewOptions = array(
            'fields' => array(
                array(
                    'field' => 'id',
                    'type' => 'hidden',
                ),
                
                // BASIC PARAMETERS
                array('h1' => __a(__FILE__, 'Basic parameters')),
                array('row', 'columns' => array(3, 9)),
                    array('col'),
                        array(
                            'field' => 'name',
                            'label' => __a(__FILE__, 'Author name'),
                        ),
                        array(
                            'field' => 'slug',
                            'label' => __a(__FILE__, 'Slug'),
                        ),
                        array(
                            'field' => 'run_eshop_manufacturers_id',
                            'label' => __a(__FILE__, 'Manufacturer'),
                            'type' => 'select',
                            'options' => $manufacturers,
                            'empty' => true,
                        ),
                    array('/col'),
                    array('col'),
                        array(
                            'field' => 'description',
                            'label' => __a(__FILE__, 'Description'),
                            'style' => 'height: 182px',
                        ),
                    array('/col'),
                array('/row'),
                
                // SEO PARAMETERS
                array(
                    'h1' => __a(__FILE__, 'SEO parameters'),
                    'hint' => __a(__FILE__, 'Parameters for search engines, e.g. Google.com, Yahoo.com or Zoznam.cz')
                ),
                array('row', 'columns' => array(3,6,3)),
                    'seo_title' => array(
                        'label' => __a(__FILE__, 'SEO title'),
                        'hint' => __a(__FILE__, 'Title is displayed in browser tab and in Google search results. It is one of most important texts for Google on your page'),
                    ),
                    'seo_description' => array(
                        'label' => __a(__FILE__, 'SEO description'), 
                        'type' => 'textarea', 
                        'style' => 'height: 34px',
                        'hint' => __a(__FILE__, 'This text is mostly used by Google in search results as text under result title. Sometimes Google decides to use some other text generated from page content'),
                    ),
                    'seo_keywords' => array(
                        'label' => __a(__FILE__, 'SEO keywords'),
                        'hint' => __a(__FILE__, 'Google does not consider much keywords. Other search engines may.')
                    ),
                array('/row'),
                
                // INFO
                array('h1' => __a(__FILE__, 'Info')),
                array('row'),
                    array(
                        'field' => 'id', 
                        'label' => 'Id', 
                        'type' => 'display'
                    ),
                    array(
                        'field' => 'created',
                        'label' => __a(__FILE__, 'Date of creation'), 
                        'type' => 'display',
                    ),
                    array(
                        'field' => 'modified',
                        'label' => __a(__FILE__, 'Date of modification'), 
                        'type' => 'display',
                    ),
                array('/row'),
                
            )
        );
        $this->viewOptions['title'] = __a(__FILE__, 'Edit manufacturer range "%s"');
        return parent::admin_edit($id);
    }
}

