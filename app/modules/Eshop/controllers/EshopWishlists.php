<?php
/**
 * ATTENTION: Wishlist (initial default) are not created explicitly on new user creation.
 * 
 * Initial default wishlists are created implicitly in folowing cases:
 *      - on addition (EshopWishlists::addProduct()) of product to wishlist by user which does not have a wishlist
 *      - on managing wishlists (EshopWishlists::manage()) by user which does not have a wishlist 
 * 
 * If you would like to create initial default wishlist for each new created user 
 * then just add to that method/action following: $Wishlist->addWishlist($userId, array('default' => true));
 */
class EshopWishlists extends Controller {
    
    public function manage($wishlistId = null) {
        $this->displayOriginComment = true;
        $redirectUrl = App::getRefererUrl('/');
        
        $userId = App::getUser('id');
        if (!$userId) {
            App::setMessage(__(__FILE__, 'Invalid user id'));
			App::redirect($redirectUrl);
        }
        
        // get all user wishlists
        $Wishlist = $this->loadModel('EshopWishlist', true);
        // if the user has no wishlist...
        while (
            !($wishlists = $Wishlist->find(array(
                'fields' => array('id', 'name', 'privacy_level', 'default', 'access_token'),
                'conditions' => array(
                    'EshopWishlist.run_users_id' => $userId,
                )
            )))
        ) {
            // ...then create one
            $Wishlist->addWishlist($userId, array('default' => true));
        }
        $wishlistCount = count($wishlists);
        
        // prepare options for wishlists select and find out actual wishlist 
        $indexUrl = '/' . App::getContentLocatorByPid('Eshop.EshopWishlists.indexProducts');
        $wishlist = array();
        $tmp = array($indexUrl => __(__FILE__, 'All wishlists'));
        foreach ($wishlists as $w) {
            $optionText = $w['name'] . ' (' . __(__FILE__, $w['privacy_level']);
            if ($w['default']) {
                $optionText .= ', ' . __(__FILE__, 'default wishlist');
            }
            $optionText .= ')';
            $wishlistIndexUrl = $indexUrl . '/' . $w['id'];
            $tmp[$wishlistIndexUrl] = $optionText;
            if ($wishlistId !== null && $wishlistId == $w['id']) {
                $wishlist = $w;
            }
        }
        $wishlists = $tmp;
        
        // get urls
        $wishlistUrl = $indexUrl;
        $wishlistViewUrl = null;
        $wishlistDeleteUrl = null;
        $wishlistName = __(__FILE__, 'All wishlists products');
        if ($wishlist) {
            $wishlistUrl .= '/' . $wishlist['id'];
            $wishlistViewUrl = App::getUrl(array(
                'locator' => App::getContentLocatorByPid('Eshop.EshopWishlists.view') . '/' . $wishlist['access_token'],
                 'absolute' => true,
            ));
            $wishlistDeleteUrl = App::getUrl(array(
                    'locator' => 'mvc/Eshop/EshopWishlists/delete',
                    'args' => array($wishlist['id'])
            ));
            $wishlistName = $wishlist['name'];
        }
        
        // get privacy levels and translate them
        $privacyLevels = $Wishlist->getPrivacyLevels('privacy_level');
        
        // load view
        return $this->loadView(
            'EshopWishlists/manage',
            array(
                'privacyLevels' => $privacyLevels, 
                'wishlists' => $wishlists, 
                'wishlist' => $wishlist, 
                'wishlistCount' => $wishlistCount, 
                'wishlistId' => $wishlistId, 
                'wishlistName' => $wishlistName,
                'wishlistUrl' => $wishlistUrl, 
                'wishlistViewUrl' => $wishlistViewUrl, 
                'wishlistDeleteUrl' => $wishlistDeleteUrl, 
                'wishlistEditUrl' => App::getUrl('mvc/Eshop/EshopWishlists/edit'), 
                'wishlistSendUrl' => App::getUrl('mvc/Eshop/EshopWishlists/send'), 
                'wishlistAddUrl' => App::getUrl('mvc/Eshop/EshopWishlists/add'),
                'requiredField' => NULL,//$Wishlist->getRequiredFields(),
                'data' => NULL,
            )
        );
    }
    
    public function add() {
        if ($this->data) {
            // if wishlist succesfully created then redirect to the index of products 
            // of new created wishlist
            $Wishlist = $this->loadModel('EshopWishlist', true);
            if ($Wishlist->addWishlist(App::getUser('id'), $this->data)) {
                App::setMessage(__(__FILE__, 'New wishlist has been successfully created'));
                App::redirect(App::getUrl(array(
                    'locator' => App::getContentLocatorByPid('Eshop.EshopWishlists.indexProducts'),
                    'args' => array($Wishlist->getPropertyId())
                )));
            }
            // if there is an error then redirect back to referer
            else {
                $errors = $Wishlist->getErrors();
                App::setMessage(array_shift(array_shift($errors)));
            }
        }
        App::redirect(App::getRefererUrl('/'));
    }
    
    public function edit() {
        if ($this->data) {
            $Wishlist = $this->loadModel('EshopWishlist', true);
            if ($Wishlist->updateWishlist(App::getUser('id'), $this->data)) {
                App::setMessage(__(__FILE__, 'Wishlist has been successfully updated'));
            }
            else {
                $errors = $Wishlist->getErrors();
                App::setMessage(array_shift(array_shift($errors)));
            }
        }        
        App::redirect(App::getRefererUrl('/'));
    }
    
    public function delete($id = null) {
        if ($id) {
            $Wishlist = $this->loadModel('EshopWishlist', true);
            if ($Wishlist->removeWishlist(App::getUser('id'), $id)) {
                App::setMessage(__(__FILE__, 'Wishlist has been successfully deleted'));
                App::redirect(App::getUrl(array(
                    'locator' => App::getContentLocatorByPid('Eshop.EshopWishlists.indexProducts'),
                )));
            }
            else {
                $errors = $Wishlist->getErrors();
                App::setMessage(array_shift(array_shift($errors)));   
            }
        }
        App::redirect(App::getRefererUrl('/'));
    }
    
    public function send () {
        $Wishlist = $this->loadModel('EshopWishlist', true);
        // chcek if the wishlist is owned by current user
        if (
            !empty($this->data['id'])
            && !empty($this->data['wishlist_email'])
            && ($wishlist = $Wishlist->isOwnedByUser($this->data['id'], App::getUser('id')))
        ) {
            // validate email
            $emails = explode(',', $this->data['wishlist_email']);
            foreach ($emails as &$email) {
                $email = trim($email);
                if (!Validate::email($email)) {
                    App::setMessage(__(__FILE__, 'Invalid email address provided'));
                    App::redirect(App::getRefererUrl('/'));
                }
            }
            $this->data['wishlist_email'] = implode(',', $emails);
            // check privacy level of wishlist
            if ($wishlist['privacy_level'] == 'enum_wishlist_private') {
                App::setMessage(__(__FILE__, 'It is not possible to share a private wishlist'));
                App::redirect(App::getRefererUrl('/'));
            }
            // send email
            // - create inserts            
            $User = App::loadModel('App', 'User', true);
            $user = $User->findFirst(array(
                'fields' => array('first_name', 'last_name'),
                'conditions' => array('User.id' => App::getUser('id'))
            ));            
            $inserts = array(
                'eshopName' => App::getSetting('App', 'name'),
                'userName' => $user['first_name'] . ' ' . $user['last_name'],
                'wishlistName' => $wishlist['name'],
                'wishlistUrl' => ($wishlistUrl = App::getUrl(array(
                    'locator' => App::getContentLocatorByPid('Eshop.EshopWishlists.view'),
                    'args' => array($wishlist['access_token']),
                    'absolute' => true,
                ))),
                'wishlistLink' => '<a href="' . $wishlistUrl . '">' . $wishlist['name'] . '</a>',
            );
            $result = App::sendEmail(
                 $this->getSetting('EshopWishlist.msgBodyShareWishlist'),
                 $this->data['wishlist_email'],
                 array(
                     'subject' => $this->getSetting('EshopWishlist.msgSubjectShareWishlist'),
                     'inserts' => $inserts,
                 )
            );
            if (!$result) {
                App::setMessage(__(__FILE__, 'The email send has failed. Contact us, please.'));
            }
            else {
                App::setMessage(__(__FILE__, 'The email was sucessfully sent'));
            }
        }
        App::redirect(App::getRefererUrl('/'));
    }
    
    public function view ($accessToken = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'title' => null,
            'emptyIndexMessage' => true,
            'columns' => 0,
            'indexType' => Sanitize::value($_COOKIE['epit']),
            'imageVariant' => 'small',
            'showDisponibility' => false,
            'showAddToCartButton' => true,
            'showSortSelect' => false,
            'showFilterSelect' => false,
            'showIndexTypeSelect' => true,
        );
        $inputOptions = Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        ));
        $options = array_merge($defaults, $inputOptions);
        if (
            $options['indexType'] === 'detailed' 
            && !isset($inputOptions['showDisponibility'])
        ) {
            $options['showDisponibility'] = true;
        }
        $vp = array();
        $vp['wishlistViewSlug'] = App::getContentLocatorByPid('Eshop.EshopWishlists.view');
        $vp['data'] = $this->data;
        $Wishlist = $this->loadModel('EshopWishlist', true);
        // show the requested wishlist
        if ($accessToken) {
            // get wishlist
            $wishlist = $Wishlist->findFirst(array(
                'fields' => array('id', 'run_users_id', 'name'),
                'conditions' => array(
                    'access_token' => $accessToken,
                    'privacy_level' => array('enum_wishlist_public', 'enum_wishlist_shared'),
                ),
            ));
            if ($wishlist) {
                // get wishlist owner profile
                $User = App::loadModel('App', 'User', true);
                $user = $User->findFirst(array(
                    'fields' => array('first_name', 'last_name'),
                    'conditions' => array('User.id' => $wishlist['run_users_id'])
                ));
                
                // get products
                $products = array();
                $WishlistProduct = $this->loadModel('EshopWishlistProduct', true);
                $productIdsXWishlistIds = $WishlistProduct->findList(array(
                    'key' => 'run_eshop_products_id',
                    'fields' => array('run_eshop_wishlists_id'),
                    'conditions' => array('run_eshop_wishlists_id' => $wishlist['id']),
                    'group' => 'run_eshop_products_id',
                    'paginate' => true,
                    'limit' => $this->getSetting('EshopProduct.pagingLimit'),
                ));
                if ($productIdsXWishlistIds) {
                    $Product = $this->loadModel('EshopProduct', true);
                    $products = $Product->getDetails(array_keys($productIdsXWishlistIds), array(
                        'getManufacturer' => true,
                    ));
                }
                // set view properties
                $vp['user'] = $user;
                $vp['products'] = $products;
                $vp['Paginator'] = $WishlistProduct->Paginator;
            }
            else {
                $wishlist = array();
            }
            $vp['wishlist'] = $wishlist;
        }
        // search for public wishlists
        elseif (!empty($this->data['wishlist_keywords'])) {
            $vp['owners'] = $Wishlist->searchWishlistOwners($this->data['wishlist_keywords']);
        }
        App::loadLib('App', 'SmartAdminLauncher');
        $vp['emptyIndexMessage'] = $options['emptyIndexMessage'];
        $vp['title'] = $options['title'];
        $vp['columns'] = $options['columns'];
        $vp['indexType'] = $options['indexType'];
        $vp['imageVariant'] = $options['imageVariant'];
        $vp['showDisponibility'] = $options['showDisponibility'];
        $vp['showAddToCartButton'] = $options['showAddToCartButton'];
        $vp['showSortSelect'] = $options['showSortSelect'];
        $vp['showFilterSelect'] = $options['showFilterSelect'];
        $vp['showIndexTypeSelect'] = $options['showIndexTypeSelect'];
        $vp['slugProductView'] = App::getContentLocatorByPid('Eshop.EshopProducts.view');
        $vp['slugAuthorProducts'] = App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor');
        $vp['slugManufacturerProducts'] = App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer');
        $vp['SmartAdminLauncher'] = new SmartAdminLauncher(
            App::getUrl(array(
                'module' => $this->module,
                'controller' => 'EshopProducts',
                'action' => 'admin_edit',
            )),
            array(
                'triggerTitle' => __a(__FILE__, 'Edit product'),
            )
        );
        $options['recordsView'] = 'EshopProducts/indexRecords';
        if (!empty($_REQUEST['recordsOnly'])) {
            $this->forceExclusiveDirectOutput();
            if (empty($products)) {
                return '';
            }
            else {
                return $this->loadView(
                    $options['recordsView'],
                    $vp
                );
            }
        }
        $vp['recordsView'] = $options['recordsView'];
        // load view
        return $this->loadView('EshopWishlists/view', $vp);
    }
    
	public function indexProducts($wishlistId = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'emptyIndexMessage' => true,
            'columns' => 0,
            'showDisponibility' => false,
            'showAddToCartButton' => false,
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        $redirectUrl = App::getRefererUrl('/');
        
        $userId = App::getUser('id');
        if (!$userId) {
            App::setMessage(__(__FILE__, 'Invalid user id'));
			App::redirect($redirectUrl);
        }
        
        // get all user wishlists
        $Wishlist = $this->loadModel('EshopWishlist', true);
        $wishlists = $Wishlist->findList(array(
            'fields' => array('name'),
            'conditions' => array(
                'EshopWishlist.run_users_id' => $userId,
            )
        ));
        
        // get all user wishlists products
        if (!$wishlistId) {
            $wishlistId = array_keys($wishlists);
            $wishlistName = __(__FILE__, 'All wishlists products');
        }
        // here just validate that the provided wishlist is owned by actual user
        else {
            if (!empty($wishlists[$wishlistId])) {
                $wishlistName = $wishlists[$wishlistId];
            }
            else {
                $wishlistId = null;
                $wishlistName = __(__FILE__, 'Invalid wishlist id provided');
            }
        }
        
        // get products
        $products = array();
        if ($wishlistId) {
            $WishlistProduct = $this->loadModel('EshopWishlistProduct', true);
            $productIdsXWishlistIds = $WishlistProduct->findList(array(
                'key' => 'run_eshop_products_id',
                'fields' => array('run_eshop_wishlists_id'),
                'conditions' => array('run_eshop_wishlists_id' => $wishlistId),
                'group' => 'run_eshop_products_id',
                'paginate' => true,
                'limit' => $this->getSetting('EshopProduct.pagingLimit'),
                'order' => 'id DESC',
            ));
            if ($productIdsXWishlistIds) {
                $Product = $this->loadModel('EshopProduct', true);
                $products = $Product->getDetails(array_keys($productIdsXWishlistIds), array(
                    'getManufacturer' => true,
                ));
            }
        }
        
        // load view
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView(
            'EshopWishlists/indexProducts',
            array(
                'emptyIndexMessage' => $options['emptyIndexMessage'],
                'columns' => $options['columns'],
                'showDisponibility' => $options['showDisponibility'],
                'showAddToCartButton' => $options['showAddToCartButton'],
                'products' => $products,
                'wishlists' => $wishlists,
                'productIdsXWishlistIds' => $productIdsXWishlistIds,
                'slugProductView' => App::getContentLocatorByPid('Eshop.EshopProducts.view'),  
                'slugAuthorProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexAuthor'),  
                'slugManufacturerProducts' => App::getContentLocatorByPid('Eshop.EshopProducts.indexManufacturer'),
                'Paginator' => $WishlistProduct->Paginator,
                'SmartAdminLauncher' => new SmartAdminLauncher(
                    App::getUrl(array(
                        'module' => $this->module,
                        'controller' => 'EshopProducts',
                        'action' => 'admin_edit',
                    )),
                    array(
                        'triggerTitle' => __a(__FILE__, 'Edit product'),
                    )
                ),
            )
        );
	}
    
    /**
     * Adds new product to wishlist of logged user and redirects back
     * 
     * @param int $productId 
     * @param string $this->data['email'] Optional. Must be provided if no user is logged in.
     * @param string $_GET['redirectUrl'] Optional. If provided then the app is redirected
     *      to this url after the product is added to cart. It is provided as GET 
     *      parameter because it contains an entire url path which cannot be attached 
     *      as URL argument. If not provided, then the app is redirected to actual referer.
     */
	public function addProductAvailabilityAlert($productId = null) {        
        $Wishlist = $this->loadModel('EshopWishlist', true);
        if (
            !($result = $Wishlist->addProductAvailabilityAlert(
                $productId, 
                Sanitize::value($this->data['email'])
            ))
        ) {
            $errors = $Wishlist->getErrors('_processing');
            App::setMessage(reset($errors));
        }
        else {
            App::setMessage(__(__FILE__, 'Ďakujeme za Váš záujem. Ihneď ako bude kniha dostupná, budeme Vás o tom informovať na Vašej e-mailovej adrese'));
        }
        if (!($redirectUrl = Sanitize::value($_GET['redirectUrl']))) {
            $redirectUrl = App::getUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => 'manage',
            ));
        }
        App::redirect($redirectUrl);
	}
        
    /**
     * Adds new product to wishlist of logged user and redirects back
     * 
     * @param int $productId 
     * @param string $_GET['redirectUrl'] Optional. If provided then the app is redirected
     *      to this url after the product is added to cart. It is provided as GET 
     *      parameter because it contains an entire url path which cannot be attached 
     *      as URL argument. If not provided, then the app is redirected to actual referer.
     */
	public function addProduct($productId = null) {        
        $Wishlist = $this->loadModel('EshopWishlist', true);
        if (!($result = $Wishlist->addProduct($productId))) {
            $errors = $Wishlist->getErrors('_processing');
            App::setMessage(reset($errors));
        }
        else {
            App::setMessage(__(__FILE__, 'Your wishlist was succesfully updated'));
        }
        if (!($redirectUrl = Sanitize::value($_GET['redirectUrl']))) {
            $redirectUrl = App::getUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => 'manage',
            ));
        }
        App::redirect($redirectUrl);
	}
    
    public function updateProduct($productId = null) {
        // validate
		if (!$productId) {
			App::setMessage(__(__FILE__, 'Invalid product id'));
            App::redirect(App::getUrl(array(
                'locator' => App::getContentLocatorByPid('Eshop.EshopWishlists.indexProducts'),
            )));
		}
        $Wishlist = $this->loadModel('EshopWishlist', true);
        if ($this->data) {
            $Wishlist->updateProduct($productId, App::getUser('id'), $this->data);
            App::setMessage(__(__FILE__, 'Product was succefully moved to new wishlist'));
        }
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => App::getContentLocatorByPid('Eshop.EshopWishlists.indexProducts'),
        ))));
    }

    /**
     * Delete a product from wishlist of logged user and redirects back to referer page.
     * Afterwards, user is redirected to his wishlist.
     * 
     * @param type $productId 
     * @return void
     * 
     */
	public function deleteProduct($productId = null) {
        // validate
		if (!$productId) {
			App::setMessage(__(__FILE__, 'Invalid product id'));
            App::redirect(App::getUrl(array(
                'locator' => App::getContentLocatorByPid('Eshop.EshopWishlists.indexProducts'),
            )));
		}
        $userId = App::getUser('id');
        if (!$userId) {
			App::setMessage(__(__FILE__, 'Invalid user id'));
            App::redirect(App::getUrl(array(
                'locator' => App::getContentLocatorByPid('Eshop.EshopWishlists.indexProducts'),
            )));
        }
        
        $Wishlist = $this->loadModel('EshopWishlist', true);
        $Wishlist->deleteProduct($productId, $userId);
        
        App::setMessage(__(__FILE__, 'Your wishlist was succesfully updated'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => App::getContentLocatorByPid('Eshop.EshopWishlists.indexProducts'),
        ))));
	}
    
    /**
     * This action should be launched each day by cronjob
     */
    public function checkWatchdog() {
        App::setLayout(false);
        $Wishlist = $this->loadModel('EshopWishlist', true);
        $Wishlist->checkWatchdog();
    }
    
}
