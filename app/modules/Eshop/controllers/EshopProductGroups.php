<?php

class EshopProductGroups extends SmartController {
    
    protected $model = 'EshopProductGroup';
    
    public function admin_index() {
        $ProductGroup = $this->loadModel('EshopProductGroup', true);
        
        // get lang to retrieve records for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        $records = $ProductGroup->find(array(
            'fields' => '*',
            'order' => 'sort ASC',
            'paginate' => true,
            'lang' => $lang,
        ));
        
        $viewLocator = App::getContentLocatorByPid('Eshop.EshopProducts.indexGroup');
        App::setSeoTitle(__a(__FILE__, 'Product groups'));
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'Product groups'),
            'records' => $records,
            'primaryKey' => 'id',
            'columns' => array(
                'name' => __a(__FILE__, 'Name'),
                'slug' => __a(__FILE__, 'Slug'),
                'type' => __a(__FILE__, 'Type'),
                'hide_unavailable_products' => __a(__FILE__, 'Nedostupné produkty sú skryté'),
            ),
            'renderFields' => array(
                'active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'slug' => function($value) use ($lang, $viewLocator) {
                    $locator = App::getUrl(array(
                        'locator' => $viewLocator,
                        'args' => array($value)
                    ));
                    return Html::hyperlinkTag($locator, $value, array(
                        'lang' => $lang,
                        'attributes' => array('target' => '_blank')
                    ));
                },
                'type' => $ProductGroup->getEnumValues('type'),
                'hide_unavailable_products' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('active' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                )
            ),
            'Paginator' => $ProductGroup->Paginator,
            'actions' => array(                
                'add' => array(
                    'url' => '/mvc/Eshop/EshopProductGroups/admin_add',
                ),
                'lang' => true,
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => array(
                        'locator' => '/mvc/Eshop/EshopProductGroups/admin_edit',
                        'get' => array(
                            'lang' => $lang
                        )
                    )
                ),
//                'copy' => array(
//                    'url' => '/mvc/App/Users/<USER>',
//                ),
                'delete' => array(
                    'url' => '/mvc/Eshop/EshopProductGroups/admin_delete',
                    'confirmMessage' => __a(__FILE__, 'Please, confirm removal of item :name:'),
                ),
                'move' => array(
                    'url' => '/mvc/Eshop/EshopProductGroups/admin_move'
                )
            )
        ));
    }
    
    public function admin_add() {
        $ProductGroup = $this->loadModel('EshopProductGroup', true);
        
        if ($this->data) {
            if ($ProductGroup->save($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'New record has been succesfully created'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                    'args' => array($ProductGroup->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        
        App::setSeoTitle(__a(__FILE__, 'Add new product group'));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Add new product group'),
            'data' => $this->data,
            'Model' => $ProductGroup,
            'lang' => DEFAULT_LANG,
            'columns' => 4,
            'actions' => array(
                'lang' => array(
                    'options' => array_combine(array(DEFAULT_LANG), array(DEFAULT_LANG)),
                )
            ),
            'fields' => array(
                array(
                    'field' => 'name', 
                    'label' => __a(__FILE__, 'Name'),
                    'autofocus' => true,
                ),
            )
        ));
    }  
    
    public function admin_edit($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        
        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        $ProductGroup = $this->loadModel('EshopProductGroup', true);
        
        if ($this->data) {
            if ($ProductGroup->saveAll($this->data, array('lang' => $lang))) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => $this->action,
                    'args' => array($id),
                    'source' => App::$requestSource,
                    'inherit' => array('get' => array('lang'))
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        else {
            $this->data = $ProductGroup->findAll(array(
                'conditions' => array('id' => $id),
                'lang' => $lang,
                'first' => true,
            ));
            // if product does not exist then redirect to referer
            if (empty($this->data)) {
                App::setErrorMessage(__a(__FILE__, 'Invalid record id %s', (string)$id));
                App::redirect(App::getRefererUrl(App::getUrl(array(
                    'locator' => '/_error',
                    'source' => App::$requestSource,
                ))));
            }
            // get file fields real path
            $fileFields = array_keys($ProductGroup->getPropertyFileFields());
            foreach($fileFields as $fileField) {
                if (!empty($this->data[$fileField])) {
                    $this->data[$fileField] = $ProductGroup->getFileFieldUrlPath($fileField, array('file' => $this->data[$fileField]));
                }
            }
        }
        
        // add name to title and tabTitle
        if (!empty($this->data['name'])) {
            $name = $this->data['name'];
        }
        // name should not be empty, if so then it is error so use actual name
        else {
            $name = $ProductGroup->findFieldBy('EshopProductGroup.name', 'id', $this->data['id']);
        }
        App::setSeoTitle(__a(__FILE__, '&quot;%s&quot;', $name));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Edit product group &quot;%s&quot;', $name),
            'data' => $this->data,
            'Model' => $ProductGroup,
            'lang' => $lang,
//            'nonTranslatedProcessing' => 'ignore',
            'columns' => 4,
            'showAffix' => true,
            'actions' => array(
                'lang' => true
            ),
            'fields' => array(
                'id' => array('type' => 'hidden'),
                
                // BASIC PARAMETERS
                array('h1' => __a(__FILE__, 'Basic parameters')),
                array('row'),
                    array(
                        'field' => 'name', 
                        'label' => __a(__FILE__, 'Name')
                    ), 
                    array(
                        'field' => 'slug', 
                        'label' => __a(__FILE__, 'Slug')
                    ),
                    /*/
                    array(
                        'field' => 'type',
                        'label' => __a(__FILE__, 'Type'),
                        'type' => 'select',
                        'empty' => true,
                        'options' => array('sale' => __a(__FILE__, 'Sale'))
                    ),
                    /*/
                    array(
                        'field' => 'active', 
                        'label' => __a(__FILE__, 'Active')
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    array(
                        'field' => 'product_ids',
                        'label' => __a(__FILE__, 'Group products'), 
                        'type' => 'itemselector', 
                        'options' => '/mvc/Eshop/EshopProducts/admin_list',
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    array(
                        'field' => 'hide_unavailable_products', 
                        'label' => __a(__FILE__, 'Skryť nedostupné produkty'),
                        'hint' => __a(__FILE__, 'Nedostupné produkty sa nebudú zobrazovať na webe v zozname produktov priradených do produktovej skupiny')
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    array(
                        'field' => 'description',
                        'label' => __a(__FILE__, 'Group description'), 
                        'type' => 'editor',
                        'options' => array(
                            'height' => '450px',
//                                'toolbar' => 'Full',
                        )
                    ),
                array('/row'),
                
                // SEO PARAMETERS
                array(
                    'h1' => __a(__FILE__, 'SEO parameters'),
                    'hint' => __a(__FILE__, 'Parameters for search engines, e.g. Google.com, Yahoo.com or Zoznam.cz')
                ),
                array('row', 'columns' => array(3,6,3)),
                    array(
                        'field' => 'seo_title',
                        'label' => __a(__FILE__, 'SEO title'),
                        'hint' => __a(__FILE__, 'Title is displayed in browser tab and in Google search results. It is one of most important texts for Google on your page'),
                    ),
                    array(
                        'field' => 'seo_description',
                        'label' => __a(__FILE__, 'SEO description'), 
                        'type' => 'textarea', 
                        'style' => 'height: 34px',
                        'hint' => __a(__FILE__, 'This text is mostly used by Google in search results as text under result title. Sometimes Google decides to use some other text generated from page content'),
                    ),
                    array(
                        'field' => 'seo_keywords',
                        'label' => __a(__FILE__, 'SEO keywords'),
                        'hint' => __a(__FILE__, 'Google does not consider much keywords. Other search engines may.')
                    ),
                array('/row'),                
                
                // INFO
                array('h1' => __a(__FILE__, 'Info')),
                array('row'),
                    array('field' => 'id', 'label' => 'Id', 'type' => 'display'),
                    array('if' => App::getUser('Group.pid') === 'admins'),
                        array('field' => 'pid', 'label' => 'Pid'),
                    array('else'),
                        array('field' => 'pid', 'label' => 'Pid', 'type' => 'display'),
                    array('endif'),
                array('/row'),
                array('row'),
                    array(
                        'field' => 'created',
                        'label' => __a(__FILE__, 'Date of creation'), 
                        'type' => 'display',
                    ),
                    array(
                        'field' => 'modified',
                        'label' => __a(__FILE__, 'Date of modification'), 
                        'type' => 'display',
                    ),
                    array('if' => App::getUser('Group.pid') === 'admins'),
                        array(
                            'field' => 'deleted',
                            'label' => __a(__FILE__, 'Date of deletion'), 
                            'type' => 'text'
                        ),
                    array('endif'),
                array('/row'),
            )        
        ));
    }    
    
    public function admin_delete($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        $ProductGroup = $this->loadModel('EshopProductGroup', true);
        $ProductGroup->deleteBy('id', $id);
        App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'module' => $this->module,
            'controller' => $this->name,
            'action' => 'admin_index',
            'source' => App::$requestSource,
        ))));
    } 
    
    /**
     * MVC element
     * 
     * Generates product groups side menu
     * 
     * @param string $arg
     * @param string $this->params['title'] Defaults to empty string
     * @param string $this->params['type'] Groups type to get menu for. Defaults to NULL.
     * 
     * @return string
     */
    public function menu($arg = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'title' => '',
            'type' => null,
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array('separator' => '-')));
               
        $conditions = array(
            'EshopProductGroup.active' => true,
            'EshopProductGroup.type' => $options['type'],
        );
        
        // get system slugs
        $groupSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexGroup');
        $urlBase = $groupSlug;
                
        $ProductGroup = $this->loadModel('EshopProductGroup', true);
        $activeSlug = $ProductGroup->resolveActiveSlug(array(
            'arg' => $arg,
            'groupProductsIndexSlug' => $groupSlug,
        ));
                 
        $list = $ProductGroup->findList(array(
            'conditions' => $conditions,
            'fields' => array(
                'EshopProductGroup.name AS label',
                'EshopProductGroup.slug AS locator',
            ),
            'order' => 'EshopProductGroup.sort ASC',
        )); 
        if (empty($list)) {
            return '';
        }
        // simulate tree
        $list = array($list);
        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('EshopProductGroups/menu', array(
            'list' => $list,
            'urlBase' => $urlBase,
            'activeItem' => $activeSlug,
            'title' => $options['title'],
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit product group'),
                )
            )
        ));
    }
}

