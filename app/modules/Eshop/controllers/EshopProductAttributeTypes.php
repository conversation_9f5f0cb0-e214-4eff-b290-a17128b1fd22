<?php

class EshopProductAttributeTypes extends Controller {
    
    public function __construct(){
        parent::__construct();
        $this->loadModel('EshopProductAttributeType');
    }
    
    /**
     * Creates <PERSON><PERSON> encoded data for extjs grid
     * 
     * @return string <PERSON><PERSON> encoded data for extjs grid
     */
    public function admin_index() {
        $Res = new ExtResponse();
        
        $ProductAttributeType = new EshopProductAttributeType();
        
        $options = ExtRequest::parsePaging();  
        $options['order'][] = 'EshopProductAttributeType.name ASC';
        $options = ExtRequest::parseRunFilter($ProductAttributeType->getSchema(), $options);
        
        // find data
        $options['fields'] = array(
            'EshopProductAttributeType.id', 
            'EshopProductAttributeType.run_eshop_product_types_id', 
            'EshopProductAttributeType.name', 
            'EshopProductAttributeType.description',
            'EshopProductAttributeType.default_value',
            'EshopProductAttributeType.selective',
            'EshopProductAttributeType.priced',
            'EshopProductAttributeType.active',
            'EshopProductAttributeType.show_in_product_detail',
            'EshopProductAttributeType.columns_in_filter',
            'EshopProductAttributeType.created',
            'EshopProductAttributeType.modified',
        );
        $Res->data = $ProductAttributeType->find($options);
        
        // find count
        $Res->total = $ProductAttributeType->findCount(array(
            'literals' => array(
                'conditions' => $options['literals']['conditions']
            ),
            'conditions' => $options['conditions']
        ));
        
        $Res->success = true;
        return $Res->toJson();
    }
        
    /**
     * Creates Json encoded data for extjs comboboxes
     * 
     * @return string Json encoded data for extjs comboboxes
     */
    public function admin_list() {
        $Res = new ExtResponse();
        
        $ProductAttributeType = new EshopProductAttributeType();
        
////mojo: do not make pagination in select box        
//        $options = ExtRequest::parsePaging();  
        $options['order'][] = 'EshopProductAttributeType.name ASC';
//        $options = ExtRequest::parseRunFilter($ProductAttributeType->getSchema(), $options);
        
        // find data
        $options['fields'] = array(
                'EshopProductAttributeType.id', 
                'EshopProductAttributeType.name', 
        );
        $Res->data = $ProductAttributeType->find($options);
        
//        // find count
//        $Res->total = $ProductAttributeType->findCount(array(
//            'literals' => array(
//                'conditions' => $options['literals']['conditions']
//            ),
//            'conditions' => $options['conditions']
//        ));
        
        $Res->success = true;
        return $Res->toJson();
    }
        
    public function admin_create() {
        $Res = new ExtResponse();

        $EshopProductAttributeType = new EshopProductAttributeType();
        if ($EshopProductAttributeType->save($this->data, array('create' => true))) {
            
            $Res->success = true;
            $Res->message = array(
                'text' => __a(__FILE__, 'Creation of attributeType has succeeded'),
                'type' => 'info'
            );
            $this->data['id'] = $EshopProductAttributeType->getId();
            $this->data['created'] = date('Y-m-d H:i:s');
            $this->data['modified'] = date('Y-m-d H:i:s');
            $Res->data = $this->data;
        }
        else {
            $Res->success = false;
            $Res->errors = $EshopProductAttributeType->getErrors();
            $Res->message = array(
                'text' => __a(__FILE__, 'Creation of attributeType has failed'),
                'type' => 'error',
                'errors' => $EshopProductAttributeType->getErrors()
            );
        }
        return $Res->toJson();
    }  
    
    public function admin_update() {
        $Res = new ExtResponse();
        
        unset($this->data['modified']);
        $EshopProductAttributeType = new EshopProductAttributeType();
        if ($EshopProductAttributeType->save($this->data)) {
            $Res->success = true;
            $Res->data = array(
                'modified' => date('Y-m-d H:i:s')
            );
            $Res->message = array(
                'text' => __a(__FILE__, 'Update of attributeType has succeeded'),
                'type' => 'info'
            );
        } else {
            $Res->message = array(
                'text' => __a(__FILE__, 'Update of attributeType has failed'),
                'type' => 'error',
                'errors' => $EshopProductAttributeType->getErrors()
            );
        }
        return $Res->toJson();
    }    
    
    public function admin_delete() {
        $Res = new ExtResponse();
        $EshopProductAttributeType = new EshopProductAttributeType();                     
        if ($EshopProductAttributeType->deleteBy('id', $this->data['id'])) {
            
            $Res->success = true;
            $Res->message = array(
                'text' => __a(__FILE__, 'Deletion of attributeType has succeeded'),
                'type' => 'info'
            );
            $Res->data = array();
        }
        else {
            $Res->success = false;
            $Res->errors = $EshopProductAttributeType->getErrors();
            $Res->message = array(
                'text' => __a(__FILE__, 'Deletion of attributeType has failed'),
                'type' => 'error',
                'errors' => $EshopProductAttributeType->getErrors()
            );
        }
        return $Res->toJson();
    }
    
    /**
     * Return complete view for item selector in ExtJs admin, used for managing related records
     * 
     * @param int $product_id
     * @return html view
     */
    public function admin_getSelectorInterface($category_id = 0) {
        $Res = new ExtResponse();
        App::setLayout('Admin', 'run_selector');
        
        // get category with attr type ids
        $this->loadModel('EshopProductCategory', true);
        $EshopProductCategory = new EshopProductCategory();
        $category = $EshopProductCategory->findFirst(array(
            'fields' => array(
                'EshopProductCategory.attribute_types',
            ),
            'conditions' => array(
                'EshopProductCategory.id' => $category_id
            )
        ));
        $attributeTypes = array();
        if (!empty($category['attribute_types'])) {
            $attrTypeIds = explode(';', $category['attribute_types']);
            if (!empty($attrTypeIds)) {
                $this->loadModel('EshopProductAttributeType');
                $EshopProductAttributeType = new EshopProductAttributeType();
                $attributeTypes = $EshopProductAttributeType->find(array(
                    'fields' => array(
                        'EshopProductType.name as product_type_name', 
                        'EshopProductAttributeType.name',
                        'EshopProductAttributeType.id'
                    ),
                    'joins' => array(
                        array(
                            'type' => 'left',
                            'model' => 'EshopProductType',
                            'conditions' => array(
                                'EshopProductType.id = EshopProductAttributeType.run_eshop_product_types_id'
                            )
                        )
                    ),
                    'conditions' => array('EshopProductAttributeType.id' => $attrTypeIds),
                    'order' => array('FIELD(EshopProductAttributeType.id, ' . implode(', ', $attrTypeIds) . ')'),
                    'literals' => array(
                        'order' => array('FIELD(EshopProductAttributeType.id, ' . implode(', ', $attrTypeIds) . ')')
                    )
                ));
            }
        }
        return $this->loadView('eshopProductAttributeTypes/getSelectorInterface', array(
            'attributeTypes' => $attributeTypes
        ));
    }
    
    /**
     * Return data for jQuery autocomplete plugin
     * 
     * @return json string
     */
    public function admin_getAutocompleteList() {
        $EshopProductAttributeType = new EshopProductAttributeType();
        $attributeTypes = $EshopProductAttributeType->find(array(
            'fields' => array(
                'EshopProductAttributeType.id',
                'EshopProductAttributeType.name',
                'EshopProductType.name as product_type_name',
            ),
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'EshopProductType',
                    'conditions' => array(
                        'EshopProductType.id = EshopProductAttributeType.run_eshop_product_types_id'
                    )
                )
            ),
            'conditions' => array(
                'EshopProductAttributeType.name %~%' => $_GET['term'],
                'OR',
                'EshopProductType.name %~%' => $_GET['term'],
            ),
            'order' => array(
                'EshopProductType.name',
                'EshopProductAttributeType.name'
            )
        ));

        $result = array();
        foreach ($attributeTypes as $attributeType) {
            $result[] = array('label' => $attributeType['product_type_name'] . ' - ' . $attributeType['name'], 'value' => $attributeType['id']);
        }
        if (empty($result)) return '[""]';
        $result = json_encode($result);
        
        return $result;
    }
}