<?php
class EshopUsers extends Controller {

    /**
     * MVC ELEMENT
     * 
     * User menu displayed in header
     */
    public function menu() {
        $this->displayOriginComment = true;
        $this->loadModel('EshopOrder');
        $Order = new EshopOrder();
        // if quick order is forced (no order by logged user is required) then 
        // do not generate any user menu
        if ($Order->hasQuickOrderAllowed() === 'force') {
            return '';
        }
        return $this->loadView('EshopUsers/menu');
    } 
    
    /**
     * MVC ELEMENT
     * 
     * User profile menu displayed in user private zone
     */
    public function profileMenu() {
        $this->displayOriginComment = true;
        return $this->loadView('EshopUsers/profileMenu');
    } 
}
