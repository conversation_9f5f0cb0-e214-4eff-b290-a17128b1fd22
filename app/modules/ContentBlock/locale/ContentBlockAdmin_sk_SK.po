# LANGUAGE translation of CakePHP Application
# <AUTHOR> <EMAIL>
#
msgid ""
msgstr ""
"Project-Id-Version: runPHP\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-18 08:49+0100\n"
"PO-Revision-Date: 2019-01-18 08:49+0100\n"
"Last-Translator: Developer <<EMAIL>>\n"
"Language-Team: Run Group <<EMAIL>>\n"
"Language: sk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __a:2\n"
"X-Poedit-Basepath: .\n"
"X-Generator: Poedit 2.0.6\n"
"X-Poedit-SearchPath-0: ../models\n"
"X-Poedit-SearchPath-1: ../views\n"
"X-Poedit-SearchPath-2: ../config\n"
"X-Poedit-SearchPath-3: ../libs\n"

#: ../config/rights.php:42
msgid "ContentBlock rights"
msgstr ""

#: ../models/ContentBlockBackgroundEnd.php:41
msgid "Obaľovací blok - koniec"
msgstr ""

#: ../models/ContentBlockBackgroundEnd.php:42
msgid "Párový koncový blok ku bloku \"Obaľovací blok - začiatok\""
msgstr ""

#: ../models/ContentBlockBackgroundEnd.php:43
#: ../models/ContentBlockBackgroundStart.php:86
msgid "Obaľovací blok (pozadie, centrovanie)"
msgstr ""

#: ../models/ContentBlockBackgroundStart.php:84
msgid "Obaľovací blok - začiatok"
msgstr ""

#: ../models/ContentBlockBackgroundStart.php:85
msgid ""
"Dvojica blokov umožňujúca vloženie spoločného pozadia pre viacero obsahových "
"blokov naraz alebo zarovnanie obsiahnutých blokov do centrálneho bloku "
"stránky."
msgstr ""

#: ../models/ContentBlockColumnEnd.php:45
msgid "Stĺpec - koniec"
msgstr ""

#: ../models/ContentBlockColumnEnd.php:46
msgid "Párový koncový blok ku bloku \"Stĺpec - začiatok\""
msgstr ""

#: ../models/ContentBlockColumnEnd.php:47
#: ../models/ContentBlockColumnStart.php:61
msgid "Stĺpec"
msgstr ""

#: ../models/ContentBlockColumnStart.php:59
msgid "Stĺpec - začiatok"
msgstr ""

#: ../models/ContentBlockColumnStart.php:60
msgid ""
"Dvojica blokov umožňujúca umiestnenie viacerých obsahových blokov do jedného "
"stĺpca. Na vytvorenie viacerých stĺpcov v jednom riadku je ideálne vložiť "
"bloky stĺpcov do obaľovacieho bloku. Toto tiež umožňuje umiestniť stĺpce v "
"centrálnom bloku stránky. Ak stĺpec obsahuje len HTML blok(y), tak je "
"výhodnejšie použiť obsahový blok \"Html stĺpec\". Na odriadkovanie stĺpcov "
"pod seba sa používa blok \"Nový riadok\"."
msgstr ""

#: ../models/ContentBlockExample001.php:162
msgid "Page XYZ > Section XYZ"
msgstr ""

#: ../models/ContentBlockExample001.php:163
msgid "An example content block definition"
msgstr ""

#: ../models/ContentBlockHtml.php:71
msgid "HTML"
msgstr ""

#: ../models/ContentBlockHtml.php:72
msgid "HTML editor na zadanie ľubovolného HTML obsahu alebo snipetov"
msgstr ""

#: ../models/ContentBlockHtmlColumn.php:41
msgid "HTML stĺpec"
msgstr ""

#: ../models/ContentBlockHtmlColumn.php:42
msgid ""
"HTML editor na zadanie ľubovolného HTML obsahu alebo snipetov do stĺpca. Na "
"vytvorenie viacerých stĺpcov v jednom riadku je ideálne vložiť bloky HTML "
"stĺpcov do obaľovacieho bloku. Toto tiež umožňuje umiestniť stĺpce v "
"centrálnom bloku stránky. Ak majú byť v stĺpci aj iné obsahové bloky, tak je "
"potrebné použiť obsahový blok \"Stĺpec\".  Na odriadkovanie stĺpcov pod seba "
"sa používa blok \"Nový riadok\"."
msgstr ""

#: ../models/ContentBlockLineBreak.php:44
msgid "Nový riadok"
msgstr ""

#: ../models/ContentBlockLineBreak.php:45
msgid ""
"Vloží nový riadok medzi obsahové bloky nachádzajúce sa vedľa seba. Vhodné na "
"použitie s obsahovými blokmi \"Stĺpec\" a \"HTML stĺpec\"."
msgstr ""

#: ../models/ContentBlockSlider.php:78
#: ../views/ContentBlockSlider/admin_view.php:15
msgid "Slajder"
msgstr ""

#: ../models/ContentBlockSlider.php:79
msgid "Slajder definovaný v \"Obsah webu\" > \"Slajdre\""
msgstr ""

#: ../models/ContentBlockTextMenu.php:26
msgid "Menu nadpisov"
msgstr ""

#: ../models/ContentBlockTextMenu.php:27
msgid ""
"Menu pozostávajúce z nadpisov blokov v stránke. Zahrnuté sú len nadpisy so "
"zaškrtnutým \"Zobraziť v menu nadpisov\"."
msgstr ""

#: ../models/ContentBlockTitle.php:77 ../views/admin_blockTitleForm.php:26
msgid "Titulok bloku"
msgstr ""

#: ../models/ContentBlockTitle.php:78
msgid "Titulok (nadpis) bloku"
msgstr ""

#: ../models/ContentBlockWebContent.php:83
msgid "Obsah inej stránky"
msgstr ""

#: ../models/ContentBlockWebContent.php:84
msgid ""
"Blok na vloženie obsahu inej existujúcej stránky v \"Obsah webu\" > \"Sekcie "
"a stránky\""
msgstr ""

#: ../models/ContentBlockWebContent.php:186
#: ../models/ContentBlockWebContentsAccordion.php:180
msgid "neaktívna"
msgstr ""

#: ../models/ContentBlockWebContentsAccordion.php:84
msgid "Akordeón obsahov"
msgstr ""

#: ../models/ContentBlockWebContentsAccordion.php:85
msgid ""
"Blok na vloženie akordeónu obsahov podstránok vybranej stránky v \"Obsah webu"
"\" > \"Sekcie a stránky\""
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:9
#: ../views/ContentBlockExample001/admin_view.php:114
#: ../views/ContentBlockHtmlColumn/admin_view.php:14
msgid "Šírka"
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:10
#: ../views/ContentBlockHtmlColumn/admin_view.php:15
msgid ""
"Zádajte šírku stĺpca podľa šírky obrazovky: <ul><li><code>50%</code> "
"znamená, že stĺpec bude mať vždy šírku 50% (t.j. polovica šírky rodičovského "
"bloku) bez ohľadu na šírku obrazovky.</li><li><code>768px:100%</code> "
"znamená, že na obrazovkách so šírkou 768px a menších (tablety, mobily) bude "
"mať šírku 100% (t.j. celá šírka rodičovského bloku).</li><li><code>1/3; "
"768px:0.5; 480px:100%</code> znamená, že stĺpec bude mať na veľkých "
"obrazovkách šírku jednej tretiny rodičovského bloku. Na obrazovkách so "
"šírkou 768px a menších (tablety) bude mať šírku 50% a na obrazovkách so "
"šírkou 480px a menších (mobily) bude na celú šírku rodičovského bloku.</li></"
"ul> Jednotky na zadanie šírky stĺpca môžu byť ľubovolné (<code>%</code>, "
"<code>px</code>, <code>mm</code>), no z pohľadu responzivity je ideálne "
"používať pomerné hodnoty, t.j. <code>%</code> alebo desatinné čislo. "
"Napríklad <code>0.5</code> je to isté ako <code>50%</code>. Ako oddeľovač "
"desatinných miest je potrebné použiť bodku <code>.</code> (nie čiarku). "
"Desatinné číslo je možné zadať aj formou zlomku,  napríklad <code>1/3</"
"code>, čo je približne <code>0.3333</code> alebo <code>33.33%</code>. "
"Hraničné šírky obrazoviek musia byť vždy zadané v <code>px</code> a preto "
"pri nich nie je nutné uvádzať jednotky, t.j. <code>768px:100%</code> je to "
"isté ako <code>768:100%</code>. Prednastavená šírka je 50%."
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:15
#: ../views/ContentBlockHtmlColumn/admin_view.php:20
msgid "Zvislé zarovnanie"
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:16
#: ../views/ContentBlockHtmlColumn/admin_view.php:21
msgid "Vyberte zvislé (vertikálne) zarovnanie stĺpca so susednými stĺpcami"
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:18
#: ../views/ContentBlockHtmlColumn/admin_view.php:23
#: ../views/admin_blockParamsForm.php:155
msgid "Hore"
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:19
#: ../views/ContentBlockHtmlColumn/admin_view.php:24
#: ../views/admin_blockParamsForm.php:142
#: ../views/admin_blockParamsForm.php:154
msgid "Uprostred"
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:20
#: ../views/ContentBlockHtmlColumn/admin_view.php:25
#: ../views/admin_blockParamsForm.php:157
msgid "Dole"
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:26
#: ../views/ContentBlockHtmlColumn/admin_view.php:31
msgid "Zaradiť na koniec"
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:27
#: ../views/ContentBlockHtmlColumn/admin_view.php:32
msgid ""
"Ak má byť stĺpec na velkých obrazovkách zaradený na konci riadku stĺpcov a "
"na malých obrazovkách má byť ako prvý zo zalomených obsahov, tak je potrebné "
"ho umiestniť ako prvý a zašktnúť \"Zaradiť na koniec\". Pre takého bloky je "
"možné len zvislé zarovnanie hore."
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:34
#: ../views/ContentBlockHtmlColumn/admin_view.php:39
#: ../views/admin_blockParamsForm.php:57
msgid "Priestor hore"
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:35
#: ../views/ContentBlockHtmlColumn/admin_view.php:40
msgid ""
"Zádajte priestor hore podľa šírky obrazovky. Napríklad <code>768px:10%</"
"code> znamená, že na obrazovkách so šírkou 768px a menších (tablety) bude "
"priestor hore 10% zo šírky rodičovského bloku. Presný popis možností viď v "
"nápovede k polu \"Širka\". Prednastavený priestor hore je 0."
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:40
#: ../views/ContentBlockHtmlColumn/admin_view.php:45
#: ../views/admin_blockParamsForm.php:63
msgid "Priestor dole"
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:41
#: ../views/ContentBlockHtmlColumn/admin_view.php:46
msgid ""
"Zádajte priestor dole podľa šírky obrazovky. Napríklad <code>768px:10%</"
"code> znamená, že na obrazovkách so šírkou 768px a menších (tablety) bude "
"priestor dole 10% zo šírky rodičovského bloku.  Presný popis možností viď v "
"nápovede k polu \"Širka\". Prednastavený priestor dole je 0."
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:46
#: ../views/ContentBlockHtmlColumn/admin_view.php:51
msgid "Priestor vľavo"
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:47
#: ../views/ContentBlockHtmlColumn/admin_view.php:52
msgid ""
"Zádajte priestor vľavo podľa šírky obrazovky. Napríklad <code>10%; 768px:0</"
"code> znamená, že východzí priestor dole na veľkých obrazovkach bude 10% zo "
"šírky rodičovského bloku a na obrazovkách so šírkou 768px a menších "
"(tablety) nebude žiadny. Presný popis možností viď v nápovede k polu \"Širka"
"\". Prednastavený priestor vľavo je 0."
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:52
#: ../views/ContentBlockHtmlColumn/admin_view.php:57
msgid "Priestor vpravo"
msgstr ""

#: ../views/ContentBlockColumnStart/admin_view.php:53
#: ../views/ContentBlockHtmlColumn/admin_view.php:58
msgid ""
"Zádajte priestor vpravo podľa šírky obrazovky. Napríklad <code>10%; 768px:0</"
"code> znamená, že východzí priestor dole na veľkých obrazovkach bude 10% zo "
"šírky rodičovského bloku a na obrazovkách so šírkou 768px a menších "
"(tablety) nebude žiadny. Presný popis možností viď v nápovede k polu \"Širka"
"\". Prednastavený priestor vpravo je 0."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:11
msgid "Obrázky"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:17
msgid "Obrázok"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:18
msgid "Vyberte obrázok o rozmeroch aspoň 300 x 300px"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:23
msgid "Ikona"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:24
msgid ""
"Zaškrtnite, ak vybraný obrázok je len ikona, ktorá sa má zobraziť uprostred. "
"Ideálne by to mal byť PNG obrázok s priehľadným pozadím."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:34
msgid "Popisok obrázka"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:42
msgid "Farba pozadia obrázka"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:43
msgid ""
"Zadajte farbu pozadia obrázkov pomocou niektorého z webových formátov "
"zapisovania farieb, napr. <code>black</code>, <code>#000</code>, "
"<code>rgb(0,0,0)</code>. Priesvitnú farbu zadajte ako <code>transparent</"
"code>. Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:48
msgid "Veľkosť pozadia obrázka"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:49
msgid ""
"Zadajte veľkosť pozadia obrázkov v <code>px</code>, napr. <code>300px</"
"code>. Obzazky sa budú správať podľa toho či ide o ikonu (nemení veľkosť) "
"alebo nie (obrázok sa zväčší spolu s pozadím). Ak nie je zadaná, tak sa "
"použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:54
msgid "Medzera medzi obrázkami"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:55
msgid ""
"Zadajte veľkosť medzery medzi obrázkami navzájom v <code>px</code>, napr. "
"<code>50px</code>. Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:60
msgid "Odsadenie od názvu"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:61
msgid ""
"Zadajte veľkosť medzery medzi obrázkami a názvom bloku v <code>px</code>, "
"napr. <code>50px</code>. Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:68
#: ../views/ContentBlockExample001/admin_view.php:123
msgid "Farba písma"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:69
msgid ""
"Zadajte farbu písma popiskov obrázkov pomocou niektorého z webových formátov "
"zapisovania farieb, napr. <code>black</code>, <code>#000</code>, "
"<code>rgb(0,0,0)</code>. Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:74
#: ../views/ContentBlockExample001/admin_view.php:129
msgid "Veľkosť písma"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:75
msgid ""
"Zadajte veľkosť písma popiskov obrázkov v <code>px</code>, napr. <code>15px</"
"code>. Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:80
#: ../views/ContentBlockExample001/admin_view.php:135
msgid "Hrúbka písma"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:81
msgid "Vyberte hrúbku písma popiskov obrázkov."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:84
#: ../views/ContentBlockExample001/admin_view.php:139
msgid "Normálna"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:85
#: ../views/ContentBlockExample001/admin_view.php:140
msgid "Tučná"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:91
msgid "Odsadenie od obrázkov"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:92
msgid ""
"Zadajte veľkosť medzery medzi popiskami a obrázkami v <code>px</code>, napr. "
"<code>50px</code>. Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:97
msgid "Tlačidlá"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:103
#: ../views/admin_blockTitleForm.php:33 ../views/admin_blockTitleForm.php:38
msgid "Text"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:108
msgid "Link"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:109
#, php-format
msgid ""
"Link tlačidla buď v absolútnej forme, napr. <code>https://www.google.com</"
"code> alebo ak link smeruje na http://%s, tak môže byť aj v relativnej "
"forme, napr. <code>/kontakt</code>. Na spustenie tlače zadaj "
"<code>javascript:window.print()</code>. Na odoslanie mailu zadaj napr. "
"<code>mailto:<EMAIL></code>. Na vytočenie telefónneho čísla "
"(ak je stránka prehliadaná na mobilnom telefóne) zadaj napr. <code>tel:"
"+421987654321</code>."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:115
msgid ""
"Zadajte šírku tlačidla v <code>px</code>, napr. <code>150px</code>. Ak nie "
"je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:124
msgid ""
"Zadajte farbu písma tlačidiel pomocou niektorého z webových formátov "
"zapisovania farieb, napr. <code>black</code>, <code>#000</code>, "
"<code>rgb(0,0,0)</code>. Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:130
msgid ""
"Zadajte veľkosť písma tlačidiel v <code>px</code>, napr. <code>15px</code>. "
"Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:136
msgid "Vyberte hrúbku písma tlačidiel."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:146
msgid "Typ"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:147
msgid "Vyberte typ tlačidla"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:150
msgid "Oblé rohy"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:151
msgid "Ostré rohy"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:159
#: ../views/admin_blockParamsForm.php:114
msgid "Farba pozadia"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:160
msgid ""
"Zadajte farbu pozadia tlačidiel pomocou niektorého z webových formátov "
"zapisovania farieb, napr. <code>black</code>, <code>#000</code>, "
"<code>rgb(0,0,0)</code>. Priesvitnú farbu zadajte ako <code>transparent</"
"code>. Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:165
msgid "Farba okraja"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:166
msgid ""
"Zadajte farbu okraja tlačidiel pomocou niektorého z webových formátov "
"zapisovania farieb, napr. <code>black</code>, <code>#000</code>, "
"<code>rgb(0,0,0)</code>. Priesvitnú farbu zadajte ako <code>transparent</"
"code>. Ak tlačidlo nemá mať okraj, tak zadajte <code>none</code>. Ak nie je "
"zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:171
msgid "Priestor po stranách"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:172
msgid ""
"Zadajte veľkosť priestoru medzi textom a pravým / ľavým okrajom tlačidla v "
"<code>px</code>, napr. <code>50px</code>. Ak nie je zadaná, tak sa použije "
"prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:177
msgid "Medzera medzi tlačidlami"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:178
msgid ""
"Zadajte veľkosť medzery medzi tlačidlami navzájom v <code>px</code>, napr. "
"<code>50px</code>. Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:183
msgid "Odsadenie od obrázkov s popiskami"
msgstr ""

#: ../views/ContentBlockExample001/admin_view.php:184
msgid ""
"Zadajte veľkosť medzery medzi tlačidlami a obrázkami s popiskami v <code>px</"
"code>, napr. <code>50px</code>. Ak nie je zadaná, tak sa použije "
"prednastavená."
msgstr ""

#: ../views/ContentBlockSlider/admin_view.php:16
msgid ""
"Vyberte niektorý so slajdrov definovaných v \"Obsah webu\" > \"Slajdre\""
msgstr ""

#: ../views/ContentBlockSlider/admin_view.php:22
msgid "Čas efektu [s]"
msgstr ""

#: ../views/ContentBlockSlider/admin_view.php:23
msgid "Čas prechodu medzi dvoma slajdami (v sekundách)"
msgstr ""

#: ../views/ContentBlockSlider/admin_view.php:28
msgid "Čas slajdu [s]"
msgstr ""

#: ../views/ContentBlockSlider/admin_view.php:29
msgid "Čas trvania jedného slajdu (v sekundách)"
msgstr ""

#: ../views/ContentBlockSlider/admin_view.php:34
msgid "Automatické spúšťanie"
msgstr ""

#: ../views/ContentBlockSlider/admin_view.php:35
msgid "Má sa slajder spustiť automaticky?"
msgstr ""

#: ../views/ContentBlockSlider/admin_view.php:40
msgid "Stop ak je kurzor nad slajdrom"
msgstr ""

#: ../views/ContentBlockSlider/admin_view.php:41
msgid "Má sa slajder zastaviť, ak je kurzor nad ním?"
msgstr ""

#: ../views/ContentBlockWebContent/admin_view.php:10
msgid "Stránka"
msgstr ""

#: ../views/ContentBlockWebContent/admin_view.php:11
msgid ""
"Vyberte stránku, ktorej obsah sa má zobraziť. Ak je stránka neaktívna, tak "
"jej obsah sa nezobrazí. Neaktívne stránky sú v zozname označené príponou "
"<code>(neaktívna)</code>. Stránkam, ktoré slúžia len na vytvorenie "
"zdieľaných obsahov, je vhodné vymazať \"URL\", aby takéto stránky neboli "
"dostupné z URL adresy aj napriek tomu, že sú aktívne."
msgstr ""

#: ../views/ContentBlockWebContentsAccordion/admin_view.php:10
msgid "Rodičovská stránka"
msgstr ""

#: ../views/ContentBlockWebContentsAccordion/admin_view.php:11
msgid ""
"Vyberte rodičovskú stránku. Obsahy jej podstránok sa zobrazia v akordeóne. "
"Ak je podstránka neaktívna, tak jej obsah sa v akordeóne nezobrazí. "
"Neaktívne stránky sú v zozname označené príponou <code>(neaktívna)</code>. "
"Stránkam, ktoré slúžia len na vytvorenie obsahov akordeónu, je vhodné "
"vymazať \"URL\", aby takéto stránky neboli dostupné z URL adresy aj napriek "
"tomu, že sú aktívne."
msgstr ""

#: ../views/ContentBlockWebContentsAccordion/admin_view.php:18
msgid "Otvoriť prvú položku"
msgstr ""

#: ../views/ContentBlockWebContentsAccordion/admin_view.php:19
msgid "Má byť prvá položka akordeónu pri načítaní stránky otvorená?"
msgstr ""

#: ../views/admin_blockParamsForm.php:41
msgid "Vlastnosti bloku"
msgstr ""

#: ../views/admin_blockParamsForm.php:58
msgid ""
"Zádajte priestor hore podľa šírky obrazovky: <ul><li><code>50px</code> "
"znamená, že priestor hore bude vždy 50px bez ohľadu na šírku obrazovky.</"
"li><li><code>768px:10%</code> znamená, že na obrazovkách so šírkou 768px a "
"menších (tablety, mobily) bude 10% zo šírky rodičovského bloku.</"
"li><li><code>50/1170; 768px:33px; 480px:20px</code> znamená, že východzí "
"priestor hore na veľkých obrazovkach bude zlomok 50/1170 zo šírky "
"rodičovského bloku (t.j. ak je rodič široký 1170px tak priestor hore bude "
"presne 50px, pri zužovaní rodiča sa bude zmenšovať). Na obrazovkách so "
"šírkou 768px a menších (tablety) bude priestor hore 33px a na obrazovkách so "
"šírkou 480px a menších (mobily) bude priestor hore 20px.</li></ul> Jednotky "
"na zadanie priestoru môžu byť ľubovolné (<code>%</code>, <code>px</code>, "
"<code>mm</code>), no z pohľadu responzivity je ideálne používať pomerné "
"hodnoty, t.j. <code>%</code> alebo desatinné čislo. Napríklad <code>0.5</"
"code> je to isté ako <code>50%</code>. Ako oddeľovač desatinných miest je "
"potrebné použiť bodku <code>.</code> (nie čiarku). Desatinné číslo je možné "
"zadať aj formou zlomku,  napríklad <code>1/3</code>, čo je približne "
"<code>0.3333</code> alebo <code>33.33%</code>. Hraničné šírky obrazoviek "
"musia byť vždy zadané v <code>px</code> a preto pri nich nie je nutné "
"uvádzať jednotky, t.j. <code>768px:10%</code> je to isté ako <code>768:10%</"
"code>."
msgstr ""

#: ../views/admin_blockParamsForm.php:64
msgid ""
"Zádajte priestor dole podľa šírky obrazovky: <ul><li><code>50px</code> "
"znamená, že priestor dole bude vždy 50px bez ohľadu na šírku obrazovky.</"
"li><li><code>768px:10%</code> znamená, že na obrazovkách so šírkou 768px a "
"menších (tablety, mobily) bude 10% zo šírky rodičovského bloku.</"
"li><li><code>50/1170; 768px:33px; 480px:20px</code> znamená, že východzí "
"priestor dole na veľkých obrazovkach bude zlomok 50/1170 zo šírky "
"rodičovského bloku (t.j. ak je rodič široký 1170px tak priestor dole bude "
"presne 50px, pri zužovaní rodiča sa bude zmenšovať). Na obrazovkách so "
"šírkou 768px a menších (tablety) bude priestor dole 33px a na obrazovkách so "
"šírkou 480px a menších (mobily) bude priestor dole 20px.</li></ul> Jednotky "
"na zadanie priestoru môžu byť ľubovolné (<code>%</code>, <code>px</code>, "
"<code>mm</code>), no z pohľadu responzivity je ideálne používať pomerné "
"hodnoty, t.j. <code>%</code> alebo desatinné čislo. Napríklad <code>0.5</"
"code> je to isté ako <code>50%</code>. Ako oddeľovač desatinných miest je "
"potrebné použiť bodku <code>.</code> (nie čiarku). Desatinné číslo je možné "
"zadať aj formou zlomku,  napríklad <code>1/3</code>, čo je približne "
"<code>0.3333</code> alebo <code>33.33%</code>. Hraničné šírky obrazoviek "
"musia byť vždy zadané v <code>px</code> a preto pri nich nie je nutné "
"uvádzať jednotky, t.j. <code>768px:10%</code> je to isté ako <code>768:10%</"
"code>."
msgstr ""

#: ../views/admin_blockParamsForm.php:82
msgid "Šírka obsahu"
msgstr ""

#: ../views/admin_blockParamsForm.php:83
msgid ""
"Má byť šírka HTML obsahu obmedzená na veľkých obrazovkách len na centrálny "
"blok o zvolenej šírke alebo má byť na celú šírku obrazovky."
msgstr ""

#: ../views/admin_blockParamsForm.php:85
msgid "Celá šírka obrazovky"
msgstr ""

#: ../views/admin_blockParamsForm.php:86
msgid "Centrálny blok"
msgstr ""

#: ../views/admin_blockParamsForm.php:108
msgid "Obrázok pozadia"
msgstr ""

#: ../views/admin_blockParamsForm.php:109
msgid ""
"Vyberte obrázok pozadia bloku. Keďže ide o pomerne veľký obrázok, tak aj z "
"ohľadu voči mobilným datam návštevníkov webu, je vhodné ho pred nahratím "
"uložiť vo formate JPG a minimalizovať jeho veľkosť pomocou <a href=\"https://"
"compressor.io/compress\" target=\"_blank\">online kompresora</a>. Ak obrázku "
"pozadia nastavíte aj paralaxu, tak obrázok by mal mať dostatočnú rezervu "
"hore aj dolu. Riešením môže tiež byť vytvorenie symetrického obrázku s "
"dvojnásobnou výškou, tak že obrázok sa prevrati zhora nadol a tato "
"prevratena verzia sa prida na spodok pôvodného obrazku."
msgstr ""

#: ../views/admin_blockParamsForm.php:115
msgid ""
"Zadajte farbu pozadia bloku podľa šírky obrazovky, napríklad "
"<code>transparent; 768px:rgba(255,255,255,0.5)</code>. Použite niektorý z "
"webových formátov zapisovania farieb, napr. <code>black</code>, <code>#000</"
"code>, <code>rgb(0,0,0)</code>, <code>rgba(0,0,0,0.5)</code>. Priesvitnú "
"farbu zadajte ako <code>transparent</code>. Ak nie je zadaná, tak sa použije "
"prednastavená"
msgstr ""

#: ../views/admin_blockParamsForm.php:138
msgid "Vodorovné umiestnenie obrázku"
msgstr ""

#: ../views/admin_blockParamsForm.php:139
msgid "Vyberte vodorovné (horizontálne) umiestnenie obrázku pozadia"
msgstr ""

#: ../views/admin_blockParamsForm.php:143
msgid "Vľavo"
msgstr ""

#: ../views/admin_blockParamsForm.php:144
msgid "Vpravo"
msgstr ""

#: ../views/admin_blockParamsForm.php:150
msgid "Zvislé umiestnenie obrázku"
msgstr ""

#: ../views/admin_blockParamsForm.php:151
msgid ""
"Vyberte zvislé (vertikálne) umiestnenie obrázku pozadia. Pri použití "
"symetrického obrázku (viď napovedu k \"Obrázok pozadia\") je vhodné použiť "
"umiestnenie \"Horná štvrtina.\""
msgstr ""

#: ../views/admin_blockParamsForm.php:156
msgid "Horná štvrtina"
msgstr ""

#: ../views/admin_blockParamsForm.php:163
msgid "Veľkosť obrázku"
msgstr ""

#: ../views/admin_blockParamsForm.php:164
msgid "Vyberte umiestnenie obrázku pozadia."
msgstr ""

#: ../views/admin_blockParamsForm.php:167
msgid "Automaticky"
msgstr ""

#: ../views/admin_blockParamsForm.php:168
msgid "Obsiahnúť"
msgstr ""

#: ../views/admin_blockParamsForm.php:169
msgid "Pokryť"
msgstr ""

#: ../views/admin_blockParamsForm.php:185
msgid "Opakovanie obrázku"
msgstr ""

#: ../views/admin_blockParamsForm.php:186
msgid "Vyberte opakovanie obrázku pozadia."
msgstr ""

#: ../views/admin_blockParamsForm.php:189
msgid "Neopakovat"
msgstr ""

#: ../views/admin_blockParamsForm.php:190
msgid "Opakovat"
msgstr ""

#: ../views/admin_blockParamsForm.php:191
msgid "Opakovat vodorovne"
msgstr ""

#: ../views/admin_blockParamsForm.php:192
msgid "Opakovat zvislo"
msgstr ""

#: ../views/admin_blockParamsForm.php:198
msgid "Paralaxa obrázku"
msgstr ""

#: ../views/admin_blockParamsForm.php:199
msgid ""
"Zadajte veľkosť paralaxy obrázku pozadia formou desatinného čísla, napr. "
"<code>0.5</code>. Pevné pozadie (bez paralaxy) zadajte ako <code>0</code>. "
"Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""

#: ../views/admin_blockTitleForm.php:44
msgid "Úroveň titulku"
msgstr ""

#: ../views/admin_blockTitleForm.php:45
msgid ""
"Vzhľad jednotlivých úrovní je daný dizajnom. Úroveň titulku je dôležitá aj s "
"pohľadu internetových vyhľadávačov. Pre nadpisy dôležitých blokov vyberte "
"\"Nadpis 1\". Pre menej dôležité vyberte niektorú z nižších úrovní."
msgstr ""

#: ../views/admin_blockTitleForm.php:47
msgid "Nadpis 1"
msgstr ""

#: ../views/admin_blockTitleForm.php:48
msgid "Nadpis 2"
msgstr ""

#: ../views/admin_blockTitleForm.php:49
msgid "Nadpis 3"
msgstr ""

#: ../views/admin_blockTitleForm.php:50
msgid "Nadpis 4"
msgstr ""

#: ../views/admin_blockTitleForm.php:51
msgid "Nadpis 5"
msgstr ""

#: ../views/admin_blockTitleForm.php:52
msgid "Nadpis 6"
msgstr ""

#: ../views/admin_blockTitleForm.php:58
msgid "Zobraziť v menu nadpisov"
msgstr ""

#: ../views/admin_blockTitleForm.php:59
msgid ""
"Ak je vložený do stránky blok \"Menu nadpisov\", tak sa v ňom zobrazia len "
"titulky, ktoré majú zaškrtnuté toto pole."
msgstr ""

#: ../views/admin_blockTitleForm.php:64
msgid "Text v menu nadpisov"
msgstr ""

#: ../views/admin_blockTitleForm.php:65
msgid ""
"Verzia textu titulku, ktorá sa použije v bloku \"Menu nadpisov\". Ak nie je "
"vyplnené, tak sa použije v menu nadpisov originálny text titulku."
msgstr ""

#: ../views/admin_blockTitleForm.php:73
msgid "Farba"
msgstr ""

#: ../views/admin_blockTitleForm.php:74
msgid ""
"Zadajte farbu písma titulku pomocou niektorého z webových formátov "
"zapisovania farieb, napr. <code>black</code>, <code>#000</code>, "
"<code>rgb(0,0,0)</code>. Ak nie je zadaná, tak sa použije prednastavená."
msgstr ""
