<?php /* @var $this Template */
$this->displayOriginComment = true;
$data = &$this->params;
// presence of at least one quotation is crucial for displaying whole block
if (empty($data['itemsCount'])) {
    return;
}
$uniqueClass = uniqid('cb-');
$i = rand(1, $data['itemsCount']);
$text = Sanitize::value($data['item_' . $i . '_quotation_ok']);
$author = Sanitize::value($data['item_' . $i . '_author_ok']);

?><section class="cb-quotations <?php echo $uniqueClass ?>"><?php
    if (!empty($data['block_center_content'])) {
        if ($data['block_center_content'] === 'narrow') {
            ?><div class="center narrow"><?php
        }
        else {
            ?><div class="center"><?php
        }
    }
//    echo App::loadView('ContentBlock', 'blockTitle', $this->params);
    ?><blockquote class="quotation"><?php
        ?><p class="text"><?php
            echo nl2br($text);
        ?></p><?php
        if ($author) {
            ?><cite class="author"><?php
                echo nl2br($author);
            ?></cite><?php
        }
    ?></blockquote><?php
    if (!empty($data['block_center_content'])) {
        ?></div><?php
    }
?></section><?php
App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css',
));
App::setJsFiles(array(
    '/app/js/vendors/wow.min.js',
));
App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueClass' => $uniqueClass)));

