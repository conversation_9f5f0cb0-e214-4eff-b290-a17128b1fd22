<?php /* @var $this Template */
$this->displayOriginComment = true;
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];
$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
/*
echo App::loadView('ContentBlock', 'admin_blockTitleForm', array_merge($this->params, array()));
?><h2 class="-run-sfo-heading"><?php
    echo __a(__FILE__, 'Citáty')
?></h2><?php
*/
for ($i = 1; $i <= $data['maxItemsCount']; $i++) {
    ?><div class="grid-row grid-break-780"><?php
        ?><div class="grid-col grid-col-70-100"><?php
            echo $Form->textarea('item_' . $i . '_quotation', array(
                'label' => __a(__FILE__, 'Citát') . ' ' . $i,
                'hint' => __a(__FILE__, 'Ak ostane p<PERSON>dny, citát sa nebude generovať.'),
            ));
        ?></div><?php
        ?><div class="grid-col grid-col-30-100"><?php
            echo $Form->text('item_' . $i . '_author', array(
                'label' => __a(__FILE__, 'Autor citátu') . ' ' . $i,
            ));
        ?></div><?php
    ?></div><?php
}

echo App::loadView('ContentBlock', 'admin_blockParamsForm', $this->params + array(
    //'showBlockCenterContent' => false,
));
App::setCssFiles(array(
    '/app/css/grid.css'
));
