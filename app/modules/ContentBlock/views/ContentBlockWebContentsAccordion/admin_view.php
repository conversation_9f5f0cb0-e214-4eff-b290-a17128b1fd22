<?php /* @var $this Template */
$this->displayOriginComment = true;
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];
//$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-40-100"><?php 
        echo $Form->select('run_web_contents_id', array(
            'label' => __a(__FILE__, 'Rodičovská stránka'),
            'hint' => __a(__FILE__, 'Vyberte rodičovskú stránku. Obsahy jej podstránok sa zobrazia v akordeóne. Ak je podstránka neaktívna, tak jej obsah sa v akordeóne nezobrazí. Neaktívne stránky sú v zozname označené príponou <code>(neaktívna)</code>. <PERSON><PERSON><PERSON><PERSON><PERSON>, ktoré slúžia len na vytvorenie obsahov akordeónu, je vhodné vymazať "URL", aby takéto stránky neboli dostupné z URL adresy aj napriek tomu, že sú aktívne.'),
            'options' => $data['contents'],
            'empty' => true,
        ));
    ?></div><?php
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->checkbox('open_first', array(
            'label' => __a(__FILE__, 'Otvoriť prvú položku'),
            'hint' => __a(__FILE__, 'Má byť prvá položka akordeónu pri načítaní stránky otvorená?'),
        ));
    ?></div><?php
?></div><?php
echo App::loadView('ContentBlock', 'admin_blockParamsForm', array_merge(array(
    //'showBlockCenterContent' => false,
    //'showBlockBgGradient' => false,
    //'showBlockBgImage' => false,
    //'showBlockBgColor' => false,
    //'showBlockBgHorizontalPosition' => false,
    //'showBlockBgVerticalPosition' => false,
    //'showBlockBgSize' => false,
    //'showBlockBgRepeat' => false,
    //'showBlockBgParallax' => false,
), $this->params));
App::setCssFiles(array(
    '/app/css/grid.css',
    //'/app/modules/{Module}/css/admin.css',
));
