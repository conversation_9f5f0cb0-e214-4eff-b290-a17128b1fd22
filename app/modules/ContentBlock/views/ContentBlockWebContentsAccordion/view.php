<?php /* @var $this Template */
$data = &$this->params;
$uniqueClass = uniqid('cb-');
$topPadding = Sanitize::value($data['block_top_padding'], '');
$bottomPadding = Sanitize::value($data['block_bottom_padding'], '');
$bgImage = Sanitize::value($data['block_bg_image'], '');
$bgColor = Sanitize::value($data['block_bg_color'], '');
$class = 'cb-web-contents-accordion wow animate ';
if ($topPadding !== '') {
    $class .= 'with-top-padding ';
}
if ($bottomPadding !== '') {
    $class .= 'with-bottom-padding ';
}
if ($bgImage !== '' || $bgColor !== '') {
    $class .= 'with-background ';
}
if (!empty($data['block_center_content'])) {
    $class .= 'with-center ';
}
?><div class="<?php echo $class . $uniqueClass ?>"><?php
    if (!empty($data['block_center_content'])) {
        if ($data['block_center_content'] === 'narrow') {
            ?><div class="center narrow"><?php
        }
        else {
            ?><div class="center"><?php
        }
    }
    echo App::loadControllerAction('App', 'WebContents', 'childsAccordion', array(
        'source-id' => $data['run_web_contents_id'],
        'heading-tag' => false,
        'open-first' => $data['open_first'],
    ));
    if (!empty($data['block_center_content'])) {
        ?></div><?php
    }
?></div><?php
App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css',
));
App::setJsFiles(array(
    '/app/js/vendors/wow.min.js',
));
App::startJsCapture();
?><script type="text/javascript">
jQuery(function() {
    new WOW().init();
});
</script><?php
App::endJsCapture();
App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueClass' => $uniqueClass)));
