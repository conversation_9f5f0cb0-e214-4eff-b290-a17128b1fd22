<?php /* @var $this Template */
$this->displayOriginComment = true;
$data = &$this->params;
$uniqueClass = uniqid('cb-');
$uniqueSelector = '.' . $uniqueClass;
$class = 'cb-slider';
$hasSmallGap = Sanitize::value($data['has_small_gap']);
if ($hasSmallGap) {
    $class .= ' with-small-gap';
    $uniqueSelector .= '.with-small-gap';
}
$class .= ' ' . $uniqueClass;
?><div class="<?php echo $class ?>"><?php 
    if (!empty($data['block_center_content'])) {
        if ($data['block_center_content'] === 'narrow') {
            ?><div class="center narrow"><?php
        }
        else {
            ?><div class="center"><?php
        }
    }
    //rblb//cho App::loadControllerAction('App', 'WebContentsSliders', 'slider', $this->params);
    echo App::loadView('ContentBlock', 'owlCarouselSlider', $this->params);
    if (!empty($data['block_center_content'])) {
        ?></div><?php
    }
?></div><?php
App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css',
));
App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueSelector' => $uniqueSelector)));

