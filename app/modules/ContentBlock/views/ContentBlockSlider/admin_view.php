<?php /* @var $this Template */
$this->displayOriginComment = true;
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];
$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
/*
?><h2 class="-run-sfo-heading"><?php 
    echo __a(__FILE__, 'Slajder')
?></h2><?php
 */
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->select('slider_id', array(
            'label' => __a(__FILE__, 'Slajder'),
            'hint' => __a(__FILE__, 'Vyberte niektorý so slajdrov definovaných v "Obsah webu" > "Slajdre"'),
            'options' => $data['sliders']
        ));
    ?></div><?php    
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->text('slider_transition_time', array(
            'label' => __a(__FILE__, 'Čas efektu [s]'),
            'hint' => __a(__FILE__, 'Čas prechodu medzi dvoma slajdami (v sekundách)'),
        ));
    ?></div><?php    
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->text('slider_slide_time', array(
            'label' => __a(__FILE__, 'Čas slajdu [s]'),
            'hint' => __a(__FILE__, 'Čas trvania jedného slajdu (v sekundách)'),
        ));
    ?></div><?php    
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->checkbox('slider_auto_play', array(
            'label' => __a(__FILE__, 'Automatické spúšťanie'),
            'hint' => __a(__FILE__, 'Má sa slajder spustiť automaticky?'),
        ));
    ?></div><?php    
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->checkbox('slider_stop_on_hover', array(
            'label' => __a(__FILE__, 'Stop ak je kurzor nad slajdrom'),
            'hint' => __a(__FILE__, 'Má sa slajder zastaviť, ak je kurzor nad ním?'),
        ));
    ?></div><?php    
?></div><?php
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-50-100"><?php 
        echo $Form->checkbox('has_small_gap', array(
            'label' => __a(__FILE__, 'Malá medzera'),
            'hint' => __a(__FILE__, 'Aká má byť medzera medzi blokmi umiestnenými nad sebou?')
        ));
    ?></div><?php
?></div><?php
echo App::loadView('ContentBlock', 'admin_blockParamsForm', $this->params + array(
    //'showBlockCenterContent' => false,
));
App::setCssFiles(array(
    '/app/css/grid.css'
));
