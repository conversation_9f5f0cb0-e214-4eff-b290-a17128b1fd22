<?php /* @var $this Template */
$this->displayOriginComment = true;
$data = &$this->params;
$uniqueClass = uniqid('cb-');
?><div class="cb-background <?php echo $uniqueClass ?>"><?php 
if (!empty($data['block_center_content'])) {
    if ($data['block_center_content'] === 'narrow') {
        ?><div class="center narrow"><?php
    }
    else {
        ?><div class="center"><?php
    }
}
App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css'
));
App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueClass' => $uniqueClass)));
