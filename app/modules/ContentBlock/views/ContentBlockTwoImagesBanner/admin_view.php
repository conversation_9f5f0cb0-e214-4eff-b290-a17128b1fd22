<?php /* @var $this Template */
$this->displayOriginComment = true;
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];
$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
/*/
echo App::loadView('ContentBlock', 'admin_blockTitleForm', array_merge($this->params, array(
    'showTitleTag' => false,
    'showTitleTextMenuIntegration' => false,
    //'showTitleColor' => true
)));
/*/
?><h2 class="-run-sfo-heading"><?php 
    echo __a(__FILE__, 'Obr<PERSON>zky s textami');
    echo Html::hint(__a(__FILE__, 'Zobrazia sa len položky, ktoré zadáte. Je vhodné pretestovať aj responzívnu verziu zmenšovaním okna prehliadača (napr. Firefox-u sa dá nastaviť ľubovolná širka okna).'));
?></h2><?php
?><div class="grid-row grid-break-780"><?php 
    for ($i = 1;  $i <= 2;  $i++) {
        ?><div class="grid-col grid-col-50-100"><?php 
            echo $Form->file('image_' . $i, array(
                'label' => __a(__FILE__, 'Obrázok č. %s', $i),
                'hint' => __a(__FILE__, 'Vyberte obrázok o šírke 590px. Výška obrázku nie je stanovená no mala by byť pri oboch rovnaká.')
            ));
            echo $Form->image('image_' . $i, array(
                'deleteImage' => '/mvc/App/ContentBlockInstances/admin_deleteInstanceFile/image_' . $i . '/' . $blockInstanceId,
            ));
            echo $Form->checkbox('veiled_image_' . $i, array(
                'label' => __a(__FILE__, 'Stmaviť obrázok č. %s', $i),
                'hint' => __a(__FILE__, 'Má sa obrázok stmaviť pomocou tmavého priehľadného závoja?. Toto je vhodné použiť v prípade keď biely text nie je dostatočne viditeľný nad svetlým obrázkom.')
            ));
            echo $Form->text('url_' . $i, array(
                'label' => __a(__FILE__, 'URL č. %s', $i),
                'hint' => __a(__FILE__, 'URL adresa, na ktorú je obrázok prelinkovaný'),
            ));
            echo $Form->text('title_' . $i, array(
                'label' => __a(__FILE__, 'Nadpis č. %s', $i),
            ));
            echo $Form->textarea('text_' . $i, array(
                'label' => __a(__FILE__, 'Text č. %s', $i),
            ));
            echo $Form->text('button_label_' . $i, array(
                'label' => __a(__FILE__, 'Text tlačidla č. %s', $i),
                'hint' => __a(__FILE__, 'Toto tlačidlo má len vizuálny efekt ("call to action"). Na horezadané URL je prelinkovaný celý obrázok'),
            ));
        ?></div><?php
    }
?></div><?php
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-50-100"><?php 
        echo $Form->checkbox('has_small_gap', array(
            'label' => __a(__FILE__, 'Malá medzera medzi obrázkami'),
            'hint' => __a(__FILE__, 'Aká má byť medzera medzi obrázkami?')
        ));
    ?></div><?php
?></div><?php
echo App::loadView('ContentBlock', 'admin_blockParamsForm', array_merge($this->params, array(
    //'showBlockCenterContent' => false,
)));
App::setCssFiles(array(
    '/app/css/grid.css'
));
