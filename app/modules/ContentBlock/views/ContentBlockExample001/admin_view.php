<?php /* @var $this Template */
$this->displayOriginComment = true;
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];
$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
echo App::loadView('ContentBlock', 'admin_blockTitleForm', array_merge($this->params, array(
    'showTitleTag' => false,
    'showTitleTextMenuIntegration' => false,
    //'showTitleColor' => true
)));
?><h2 class="-run-sfo-heading"><?php 
    echo __a(__FILE__, '<PERSON>br<PERSON>zky s textami');
    echo Html::hint(__a(__FILE__, 'Zobrazia sa len položky, ktoré zadáte'));
?></h2><?php
for ($i = 1;  $i <= 3;  $i++) {
    ?><div class="grid-row grid-break-780"><?php 
        ?><div class="grid-col grid-col-20-100"><?php 
            echo $Form->file('image_' . $i, array(
                'label' => __a(__FILE__, 'Obrázok'),
                'hint' => __a(__FILE__, 'Vyberte obrázok o rozmeroch aspoň 300 x 300px')
            ));
        ?></div><?php
        ?><div class="grid-col grid-col-20-100"><?php 
            echo $Form->image('image_' . $i, array(
                'deleteImage' => '/mvc/App/ContentBlockInstances/admin_deleteInstanceFile/image_' . $i . '/' . $blockInstanceId,
            ));
        ?></div><?php
        ?><div class="grid-col grid-col-60-100"><?php 
            echo $Form->textarea('text_' . $i, array(
                'label' => __a(__FILE__, 'Text obrázka')
            ));
        ?></div><?php
    ?></div><?php
}
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-40-100"><?php 
    ?></div><?php
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->text('text_size', array(
            'label' => __a(__FILE__, 'Veľkosť písma textov'),
            'hint' => __a(__FILE__, 'Zadajte veľkosť písma textov obrázkov v <code>px</code>, napr. <code>15px</code>. Ak nie je zadaná, tak sa použije prednastavená.'),
        ));
    ?></div><?php
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->select('text_weight', array(
            'label' => __a(__FILE__, 'Hrúbka písma textov'),
            'hint' => __a(__FILE__, 'Vyberte hrúbku písma textov obrázkov.'),
            'options' => array(
                '' => '',
                'normal' => __a(__FILE__, 'Normálna'),
                'bold' => __a(__FILE__, 'Tučná'),
            )
        ));
    ?></div><?php
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->text('text_top_margin', array(
            'label' => __a(__FILE__, 'Odsadenie textov od obrázkov'),
            'hint' => __a(__FILE__, 'Zadajte veľkosť medzery medzi textami a obrázkami podľa šírky obrazovky: <ul><li><code>50px</code> znamená, že medzera bude vždy 50px bez ohľadu na šírku obrazovky.</li><li><code>768px:10%</code> znamená, že na obrazovkách so šírkou 768px a menších (tablety, mobily) bude 10% zo šírky rodičovského bloku.</li><li><code>50/1170; 768px:33px; 480px:20px</code> znamená, že východzia medzera na veľkých obrazovkach bude zlomok 50/1170 zo šírky rodičovského bloku (t.j. ak je rodič široký 1170px tak priestor hore bude presne 50px, pri zužovaní rodiča sa bude zmenšovať). Na obrazovkách so šírkou 768px a menších (tablety) bude medzera 33px a na obrazovkách so šírkou 480px a menších (mobily) bude medzera 20px.</li></ul> Jednotky na zadanie medzery môžu byť ľubovolné (<code>%</code>, <code>px</code>, <code>mm</code>), no z pohľadu responzivity je ideálne používať pomerné hodnoty, t.j. <code>%</code> alebo desatinné čislo. Napríklad <code>0.5</code> je to isté ako <code>50%</code>. Ako oddeľovač desatinných miest je potrebné použiť bodku <code>.</code> (nie čiarku). Desatinné číslo je možné zadať aj formou zlomku,  napríklad <code>1/3</code>, čo je približne <code>0.3333</code> alebo <code>33.33%</code>. Hraničné šírky obrazoviek musia byť vždy zadané v <code>px</code> a preto pri nich nie je nutné uvádzať jednotky, t.j. <code>768px:10%</code> je to isté ako <code>768:10%</code>. Ak nie je zadaná, tak sa použije prednastavená.'),
        ));
    ?></div><?php
?></div><?php
echo App::loadView('ContentBlock', 'admin_blockParamsForm', array_merge($this->params, array(
    //'showBlockCenterContent' => false,
)));
App::setCssFiles(array(
    '/app/css/grid.css'
));
