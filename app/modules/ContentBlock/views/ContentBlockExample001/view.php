<?php /* @var $this Template */
$this->displayOriginComment = true;
$data = &$this->params;
$uniqueClass = uniqid('cb-');
?><section class="cb-example-001 <?php echo $uniqueClass ?>"><?php 
    if (!empty($data['block_center_content'])) {
        if ($data['block_center_content'] === 'narrow') {
            ?><div class="center narrow"><?php
        }
        else {
            ?><div class="center"><?php
        }
    }
    echo App::loadView('ContentBlock', 'blockTitle', array_merge($this->params, array('uniqueClass' => $uniqueClass)));
    ?><div class="columns"><?php     
        for ($i = 1; $i <= 3; $i++) {
            $image = Sanitize::value($data['image_' . $i]);
            $text = Sanitize::value($data['text_' . $i]);
            if ($image || $text) {
                ?><div class="column"><?php 
                    if ($image) {
                        ?><div class="image"><?php 
                            ?><img src="<?php echo $image ?>" alt=""/><?php
                        ?></div><?php
                    }
                    if ($text) {
                        ?><div class="text"><?php 
                            echo nl2br($text);
                        ?></div><?php
                    }
                ?></div><?php
            }
        }
    ?></div><?php
    if (!empty($data['block_center_content'])) {
        ?></div><?php
    }
?></section><?php
App::startCssCapture();
?><style type="text/css"><?php 
    if (
        !empty($data['text_size'])
        || 
        !empty($data['text_weight'])
    ) {
        ?>.<?php echo $uniqueClass ?> .columns .column .text{<?php
            if (!empty($data['text_size'])) {
                ?>font-size:<?php echo $data['text_size'] ?>;<?php
            }
            if (!empty($data['text_weight'])) {
                ?>font-weight:<?php echo $data['text_weight'] ?>;<?php
            }
        ?>}<?php
    }
    if (
        isset($data['text_top_margin']) 
        && !Validate::emptyValue($data['text_top_margin'])
    ) {
        $marginTop = Html::parseResponsiveCssValue($data['text_top_margin'], array(
            'allowValues' => false,
            'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
        ));
        echo Html::cssMediaQueries('.' . $uniqueClass . ' .columns .column .text', array(
            'margin-top' => $marginTop, 
        ));
    }
?></style><?php
App::endCssCapture();
App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css'
));
App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueClass' => $uniqueClass)));

