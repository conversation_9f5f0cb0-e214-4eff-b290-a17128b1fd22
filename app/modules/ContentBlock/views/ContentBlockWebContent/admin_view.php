<?php /* @var $this Template */
$this->displayOriginComment = true;
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];
//$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-40-100"><?php 
        echo $Form->select('run_web_contents_id', array(
            'label' => __a(__FILE__, 'Stránka'),
            'hint' => __a(__FILE__, '<PERSON><PERSON><PERSON><PERSON> stránku, ktorej obsah sa má zobraziť. Ak je stránka neaktívna, tak jej obsah sa nezobrazí. Neaktívne stránky sú v zozname označené príponou <code>(neaktívna)</code>. <PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> sl<PERSON> len na vytvorenie zdieľaných o<PERSON>, je vho<PERSON> vym<PERSON>ť "URL", aby tak<PERSON>to stránky neboli dostupné z URL adresy aj napriek tomu, že sú aktívne.'),
            'options' => $data['contents'],
            'empty' => true,
        ));
    ?></div><?php
?></div><?php
echo App::loadView('ContentBlock', 'admin_blockParamsForm', array_merge(array(
    //'showBlockCenterContent' => false,
    //'showBlockBgGradient' => false,
    //'showBlockBgImage' => false,
    //'showBlockBgColor' => false,
    //'showBlockBgHorizontalPosition' => false,
    //'showBlockBgVerticalPosition' => false,
    //'showBlockBgSize' => false,
    //'showBlockBgRepeat' => false,
    //'showBlockBgParallax' => false,
), $this->params));
App::setCssFiles(array(
    '/app/css/grid.css',
    //'/app/modules/{Module}/css/admin.css',
));
