<?php /* @var $this Template */
$this->displayOriginComment = true;
$data = &$this->params;
if (!isset($data['html'])) {
    return;
}
$class = 'cb-html-column ';
if (!empty($data['put_on_end'])) {
    $class .= 'put-on-end ';
}
$uniqueClass = uniqid('cb-');
?><div class="<?php echo $class . $uniqueClass ?>"><?php 
    echo $data['html'];
?></div><?php
App::startCssCapture();
?><style type="text/css"><?php
    if (!empty($data['vertical_align'])) {        
        ?>.<?php echo $uniqueClass ?>{<?php
            ?>vertical-align:<?php echo $data['vertical_align'] ?>;<?php
        ?>}<?php
    }
    echo Html::cssMediaQueries('.' . $uniqueClass, array(
        'width' => Html::parseResponsiveCssValue($data['responsive_width'], array(
            'allowValues' => false,
            'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
        )), 
        'padding-top' => Html::parseResponsiveCssValue($data['responsive_padding_top'], array(
            'allowValues' => false,
            'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
        )), 
        'padding-right' => Html::parseResponsiveCssValue($data['responsive_padding_right'], array(
            'allowValues' => false,
            'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
        )), 
        'padding-bottom' => Html::parseResponsiveCssValue($data['responsive_padding_bottom'], array(
            'allowValues' => false,
            'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
        )), 
        'padding-left' => Html::parseResponsiveCssValue($data['responsive_padding_left'], array(
            'allowValues' => false,
            'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
        ))
    ));
?></style><?php
App::endCssCapture();
App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css'
));
if (!empty($data['animated'])) {
    App::setJsFiles(array(
        '/app/js/vendors/wow.min.js',
    ));
App::startJsCapture();
?><script type="text/javascript">
jQuery(function() {
    new WOW().init();
});
</script><?php
App::endJsCapture();
}
App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueClass' => $uniqueClass)));
