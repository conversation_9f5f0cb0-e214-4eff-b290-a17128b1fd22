<?php /* @var $this Template */
/**
 * Use this view to generate block title.
 * Include it into your view end like: 
 * 
 *      echo App::loadView('ContentBlock', 'blockTitle', array_merge($this->params, array('uniqueClass' => $uniqueClass)));
 * 
 * or:
 * 
 *      echo App::loadView('ContentBlock', 'blockTitle', array_merge($this->params, array('uniqueSelector' => $uniqueSelector)));
 * 
 * NOTE: For your content block admin_view see ContentBlock/views/admin_blockTitleForm.php
 */
$this->displayOriginComment = true;
$data = &$this->params;
if (empty($data['title'])) {
    return;
}
$tag = Sanitize::value($data['title_tag'], 'div', true);
$class = '';
if (!empty($data['title_include_in_text_menu'])) {
    $class = ' text-menu-included';
}
$attr = '';
if (!empty($data['title_label_in_text_menu'])) {
    $data['title_label_in_text_menu'] = htmlentities($data['title_label_in_text_menu'], ENT_COMPAT, 'UTF-8', false);
    $attr = ' data-text-menu-label="' . $data['title_label_in_text_menu'] . '"';
}
?><<?php echo $tag ?> class="title wow animate<?php echo $class ?>"<?php echo $attr ?>><?php
    echo nl2br($data['title'])
?></<?php echo $tag ?>><?php
if (!empty($data['title_subtitle'])) {
    if (!empty($data['title_subtitle_locator'])) {
        $subtitleUrl = App::getUrl($data['title_subtitle_locator']);
        ?><a href="<?php echo $subtitleUrl ?>" class="subtitle wow animate"><?php
    }
    else {
        ?><p class="subtitle wow animate"><?php
    }
    echo nl2br($data['title_subtitle']);
    if (empty($data['title_subtitle_locator'])) {
        ?></p><?php
    }
    else {
        ?></a><?php
    }
}
App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css',
));
App::setJsFiles(array(
    '/app/js/vendors/wow.min.js',
));
App::startJsCapture();
?><script type="text/javascript">
jQuery(function() {
    new WOW().init();
});
</script><?php
App::endJsCapture();
if (!empty($data['uniqueClass'])) {
    $data['uniqueSelector'] = '.' . $data['uniqueClass'];
}
if (empty($data['uniqueSelector'])) {
    return;
}
$uniqueSelector = $data['uniqueSelector'];
App::startCssCapture();
?><style type="text/css"><?php 
    // title
    if (
        !empty($data['title_color'])
    ) {
        echo $uniqueSelector ?> .title{<?php
            if (!empty($data['title_color'])) {
                ?>color:<?php echo $data['title_color'] ?>;<?php
            }
        ?>}<?php
    }
?></style><?php
App::endCssCapture();
