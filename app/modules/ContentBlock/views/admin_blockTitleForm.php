<?php /* @var $this Template */
/**
 * Use this view to generate section for block title in your content block admin_view form.
 * Include it into your admin_view like: 
 * 
 *      echo App::loadView('ContentBlock', 'admin_blockTitleForm', array_merge($this->params, array(
 *          // put here some option if needed
 *      )));
 * 
 * NOTE: For your content block view (frontend) see ContentBlock/views/blockTitle.php
 * NOTE: For list o available options see defined $defaults
 */
$this->displayOriginComment = true;
$defaults = array(
    'showHeading' => true,
    'multiLineTitle' => false,
    'showTitleTag' => true,
    'showTitleTextMenuIntegration' => true,
    'showTitleColor' => false,
    'showTitleSubtitle' => false,
);
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];
$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
$options = array_merge($defaults, array_intersect_key($this->params, $defaults));
if ($options['showHeading']) {
    ?><h2 class="-run-sfo-heading"><?php 
        echo __a(__FILE__, 'Titulok bloku')
    ?></h2><?php
}
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-40-100"><?php 
        if ($options['multiLineTitle']) {            
            echo $Form->textarea('title', array(
                'label' => __a(__FILE__, 'Text titulku')
            ));
        }
        else {            
            echo $Form->text('title', array(
                'label' => __a(__FILE__, 'Text titulku')
            ));
        }
    ?></div><?php
    if ($options['showTitleTag']) {
        ?><div class="grid-col grid-col-20-100"><?php 
            echo $Form->select('title_tag', array(
                'label' => __a(__FILE__, 'Úroveň titulku'),
                'hint' => __a(__FILE__, 'Vzhľad jednotlivých úrovní je daný dizajnom. Úroveň titulku je dôležitá aj s pohľadu internetových vyhľadávačov. Pre nadpisy dôležitých blokov vyberte "Nadpis 1". Pre menej dôležité vyberte niektorú z nižších úrovní. Ak nechcete titulok vôbec zaraďovať medzi nadpisy na stránke tak vyberte "Bežný text".'),
                'options' => array(
                    'h1' => __a(__FILE__, 'Nadpis 1'),
                    'h2' => __a(__FILE__, 'Nadpis 2'),
                    'h3' => __a(__FILE__, 'Nadpis 3'),
                    'h4' => __a(__FILE__, 'Nadpis 4'),
                    'h5' => __a(__FILE__, 'Nadpis 5'),
                    'h6' => __a(__FILE__, 'Nadpis 6'),
                    '' => __a(__FILE__, 'Bežný text'),
                )
            ));
        ?></div><?php
    }
    if ($options['showTitleTextMenuIntegration']) {
        ?><div class="grid-col grid-col-20-100"><?php 
            echo $Form->checkbox('title_include_in_text_menu', array(
                'label' => __a(__FILE__, 'Zobraziť v menu nadpisov'),
                'hint' => __a(__FILE__, 'Ak je vložený do stránky blok "Menu nadpisov", tak sa v ňom zobrazia len titulky, ktoré majú zaškrtnuté toto pole.')
            ));
        ?></div><?php
        ?><div class="grid-col grid-col-20-100"><?php 
            echo $Form->text('title_label_in_text_menu', array(
                'label' => __a(__FILE__, 'Text v menu nadpisov'),
                'hint' => __a(__FILE__, 'Verzia textu titulku, ktorá sa použije v bloku "Menu nadpisov". Ak nie je vyplnené, tak sa použije v menu nadpisov originálny text titulku.')
            ));
        ?></div><?php
    }
?></div><?php
if ($options['showTitleColor']) {
    ?><div class="grid-row grid-break-780"><?php 
        ?><div class="grid-col grid-col-20-100"><?php
            echo $Form->text('title_color', array(
                'label' => __a(__FILE__, 'Farba titulku'),
                'hint' => __a(__FILE__, 'Zadajte farbu písma titulku pomocou niektorého z webových formátov zapisovania farieb, napr. <code>black</code>, <code>#000</code>, <code>rgb(0,0,0)</code>. Ak nie je zadaná, tak sa použije prednastavená.'),
            ));
        ?></div><?php 
    ?></div><?php
}
if ($options['showTitleSubtitle']) {
    ?><div class="grid-row grid-break-780"><?php 
        ?><div class="grid-col grid-col-40-100"><?php
            echo $Form->text('title_subtitle', array(
                'label' => __a(__FILE__, 'Text podtitulku'),
            ));
        ?></div><?php 
        ?><div class="grid-col grid-col-20-100"><?php
            echo $Form->text('title_subtitle_locator', array(
                'label' => __a(__FILE__, 'URL podtitulku'),
            ));
        ?></div><?php 
    ?></div><?php
}
