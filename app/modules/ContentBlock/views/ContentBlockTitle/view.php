<?php /* @var $this Template */
$this->displayOriginComment = true;
$data = &$this->params;
$uniqueClass = uniqid('cb-');
$title = App::loadView('ContentBlock', 'blockTitle', $this->params, null, 'tag', $vars);
?><section class="cb-title <?php echo $vars['tag'] . ' ' . $uniqueClass ?>"><?php
    if (!empty($data['block_center_content'])) {
        if ($data['block_center_content'] === 'narrow') {
            ?><div class="center narrow"><?php
        }
        else {
            ?><div class="center"><?php
        }
    }
    echo $title;
    if (!empty($data['block_center_content'])) {
        ?></div><?php
    }
?></section><?php
App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css',
));
App::setJsFiles(array(
    '/app/js/vendors/wow.min.js',
));
App::startJsCapture();
?><script type="text/javascript">
jQuery(function() {
    new WOW().init();
});
</script><?php
App::endJsCapture();
App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array(
    'uniqueClass' => $uniqueClass,
    'useMarginInsteadOfPadding' => true,
)));
