<?php /* @var $this Template */
$this->displayOriginComment = true;
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];
$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
echo App::loadView('ContentBlock', 'admin_blockTitleForm', array_merge($this->params, array(
)));
echo App::loadView('ContentBlock', 'admin_blockParamsForm', array_merge($this->params, array(
    'showBlockBgImage' => false,
    'showBlockBgColor' => false,
)));
App::setCssFiles(array(
    '/app/css/grid.css'
));


