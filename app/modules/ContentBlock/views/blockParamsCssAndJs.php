<?php /* @var $this Template */
/**
 * Use this view to generate css & js according block parameters of your content block.
 * Include it into your view end like: 
 * 
 *      App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueClass' => $uniqueClass)));
 * 
 * or:
 *  
 *      App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueSelector' => $uniqueSelector)));
 * 
 * NOTE: For your content block admin_view see ContentBlock/views/admin_blockParamsForm.php
 * NOTE: For list o available options see defined $defaults
 */
$this->displayOriginComment = true;
$defaults = array(
    'useMarginInsteadOfPadding' => false,
);
$options = array_merge($defaults, array_intersect_key($this->params, $defaults));
$data = &$this->params;
if (!empty($data['uniqueClass'])) {
    $data['uniqueSelector'] = '.' . $data['uniqueClass'];
}
if (empty($data['uniqueSelector'])) {
    return;
}
$uniqueSelector = $data['uniqueSelector'];
App::startCssCapture();
?><style type="text/css"><?php 
    // block
    if (
        isset($data['block_top_padding']) 
        && !Validate::emptyValue($data['block_top_padding'])
        || 
        isset($data['block_bottom_padding']) 
        && !Validate::emptyValue($data['block_bottom_padding'])
        ||
////as the block wrapper (.cb-...) has always full width (width of window) it is 
////up to .center block (or even some more inner) to set the side paddings of block content
//        isset($data['block_left_padding']) 
//        && !Validate::emptyValue($data['block_left_padding'])
//        ||
//        isset($data['block_right_padding']) 
//        && !Validate::emptyValue($data['block_right_padding'])
//        ||
        !empty($data['block_bg_gradient'])
        || 
        !empty($data['block_bg_image'])
        || 
        !empty($data['block_bg_color'])
        || 
        !empty($data['block_bg_horizontal_position'])
        || 
        !empty($data['block_bg_vertical_position'])
        || 
        !empty($data['block_bg_size'])
        || 
        !empty($data['block_bg_repeat'])     
    ) {
        // write responsive styles
        $paddingTop = array();
        if (
            isset($data['block_top_padding']) 
            && !Validate::emptyValue($data['block_top_padding'])
        ) {
            $paddingTop = Html::parseResponsiveCssValue($data['block_top_padding'], array(
                'allowValues' => false,
                'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
            ));
        }
        $paddingBottom = array();
        if (
            isset($data['block_bottom_padding']) 
            && !Validate::emptyValue($data['block_bottom_padding'])
        ) {
            $paddingBottom = Html::parseResponsiveCssValue($data['block_bottom_padding'], array(
                'allowValues' => false,
                'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
            ));
        }
////see the comment above
//        $paddingLeft = array();
//        if (
//            isset($data['block_left_padding']) 
//            && !Validate::emptyValue($data['block_left_padding'])
//        ) {
//            $paddingLeft = Html::parseResponsiveCssValue($data['block_left_padding'], array(
//                'allowValues' => false,
//                'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
//            ));
//        }
//        $paddingRight = array();
//        if (
//            isset($data['block_right_padding']) 
//            && !Validate::emptyValue($data['block_right_padding'])
//        ) {
//            $paddingRight = Html::parseResponsiveCssValue($data['block_right_padding'], array(
//                'allowValues' => false,
//                'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
//            ));
//        }
        $backgrounColor = array();
        if (!empty($data['block_bg_color'])) {
            $backgrounColor = Html::parseResponsiveCssValue($data['block_bg_color'], array(
                'allowValues' => true,
                'allowNumericValues' => false,
            ));
        }
        if (!$options['useMarginInsteadOfPadding']) {            
            echo Html::cssMediaQueries($uniqueSelector, array(
                'padding-top' => $paddingTop, 
                'padding-bottom' => $paddingBottom, 
//                'padding-left' => $paddingLeft, 
//                'padding-right' => $paddingRight, 
                'background-color' => $backgrounColor, 
            ));
        }
        else {
            echo Html::cssMediaQueries($uniqueSelector, array(
                'margin-top' => $paddingTop, 
                'margin-bottom' => $paddingBottom, 
//                'margin-left' => $paddingLeft, 
//                'margin-right' => $paddingRight, 
                'background-color' => $backgrounColor, 
            ));
        }
        // write other styles
        echo $uniqueSelector ?>{<?php
            $image = '';
            if (!empty($data['block_bg_gradient'])) {
                $image .= ', ' . $data['block_bg_gradient'];
            }
            if (!empty($data['block_bg_image'])) {
                $image .= ', url(\'' . $data['block_bg_image'] . '\')';
            }
            $image = ltrim($image, ' ,');
            if (!empty($image)) {
                ?>background-image:<?php echo $image ?>;<?php
            }
            if (
                !empty($data['block_bg_horizontal_position'])
                && !empty($data['block_bg_vertical_position'])
            ) {
                ?>background-position:<?php echo $data['block_bg_horizontal_position'] . ' ' . $data['block_bg_vertical_position'] ?>;<?php
            }
            elseif (!empty($data['block_bg_horizontal_position'])) {
                ?>background-position:<?php echo $data['block_bg_horizontal_position'] ?> 25%;<?php
            }
            elseif (!empty($data['block_bg_vertical_position'])) {
                ?>background-position:center <?php echo $data['block_bg_vertical_position'] ?>;<?php
            }
            if (!empty($data['block_bg_size'])) {
                ?>background-size:<?php echo $data['block_bg_size'] ?>;<?php
            }
            if (!empty($data['block_bg_repeat'])) {
                ?>background-repeat:<?php echo $data['block_bg_repeat'] ?>;<?php
            }
        ?>}<?php
    }
?></style><?php
App::endCssCapture();
if (!empty($data['block_bg_parallax'])) {
    App::startJsCapture();
    ?><script type="text/javascript"><?php 
        ?>jQuery('<?php echo $uniqueSelector ?>').parallax({speed:<?php echo $data['block_bg_parallax'] ?>})<?php
    ?></script><?php
    App::endJsCapture();
}
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
    '/app/js/libs/jquery.parallax.js'
));