<?php /* @var $this Template */
$data = &$this->params;
$icon = Sanitize::value($data['icon']);
$iconClass = empty($icon) ? ' without-icon' : '';
$faq_text = Sanitize::value($data['faq_text']);
$faqCompiled = Sanitize::value($data['faq_compiled']);
// $uniqueClass is used to generate dynamic styles
$uniqueClass = uniqid('cb-');

//if (empty($faqCompiled)) {
//    return;
//}

?><section class="cb-google-map <?php echo $uniqueClass ?>"><?php
    if (!empty($data['block_center_content'])) {
        if ($data['block_center_content'] === 'narrow') {
            ?><div class="center narrow"><?php
        }
        else {
            ?><div class="center"><?php
        }
    }
    echo App::loadElement('App', 'googleMap', array(
        'height' => $data['height'],
        'zoom' => $data['zoom_level'],
        'markers' => array(
            array(
                'latitude' => $data['latitude'],
                'longitude' => $data['longitude'],
                'label' => $data['label'],
            )
        ),
    ));
//    echo App::loadElement('App', 'googleMap', array(
//        'height' => 600,
//        'zoom' => 9,
//        'markers' => array(
//            array(
//                'latitude' => 49.220498,
//                'longitude' => 18.754241,
//                'label' => 'Ipa Slovakia, s.r.o.',
//            )
//        ),
//    ));
    if (!empty($data['block_center_content'])) {
        ?></div><?php
    }
?></section><?php


App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css'
));
App::setJsFiles(array(
//    '/app/js/vendors/wow.min.js',
    '/app/js/vendors/jquery.min.js',
));
//App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueClass' => $uniqueClass)));