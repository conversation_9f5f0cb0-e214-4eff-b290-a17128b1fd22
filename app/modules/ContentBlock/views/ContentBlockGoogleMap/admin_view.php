<?php /* @var $this Template */
$this->displayOriginComment = true;
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];

$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);

?><div class="grid-row grid-break-780"><?php
    ?><div class="grid-col grid-col-20-100"><?php
        echo $Form->text('latitude', array(
            'label' => __a(__FILE__, 'Zemepišná šírka'),
            'hint' => __a(__FILE__, 'Napr.:<code>49.220416</code>.Ak ostane prázdne, blok s mapou sa nezobrazí.'),
        ));
    ?></div><?php
    ?><div class="grid-col grid-col-20-100"><?php
        echo $Form->text('longitude', array(
            'label' => __a(__FILE__, 'Zemepišná dĺžka'),
            'hint' => __a(__FILE__, 'Napr.:<code>18.754283</code>.Ak ostane prázdne, blok s mapou sa nezobrazí.'),
        ));
    ?></div><?php
    ?><div class="grid-col grid-col-40-100"><?php
        echo $Form->text('label', array(
            'label' => __a(__FILE__, 'Popisok'),
            'hint' => __a(__FILE__, 'Popisok, ktorý sa zobrazí pri bode na mape. Napr.:<code>Ipa Slovakia, s.r.o.</code>. Ak ostane prázdny, popisok k bodu sa nezobrazí.'),
        ));
    ?></div><?php
    ?><div class="grid-col grid-col-20-100"><?php
        echo $Form->select('zoom_level', array(
            'label' => __a(__FILE__, 'Priblíženie mapy'),
//            'hint' => __a(__FILE__, 'Stupeň priblíženia mapy.'),
            'options' => array(
                12 => 'Mesto s okolím',
                15 => 'Časť mesta',
                17 => 'Budovy v meste',
            ),
        ));
    ?></div><?php
?></div><?php
echo App::loadView('ContentBlock', 'admin_blockParamsForm', array_merge($this->params, array(
    //'showBlockCenterContent' => false,
)));
App::setCssFiles(array(
    '/app/css/grid.css',
    //'/app/modules/{Module}/css/admin.css',
));
