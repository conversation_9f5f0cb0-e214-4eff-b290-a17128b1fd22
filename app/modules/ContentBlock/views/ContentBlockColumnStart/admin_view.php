<?php /* @var $this Template */
$this->displayOriginComment = true;
$Form = &$this->params['Form'];
$data = &$this->params['data'];
$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-40-100"><?php 
        echo $Form->text('responsive_width', array(
            'label' => __a(__FILE__, 'Šírka'),
            'hint' => __a(__FILE__, 'Zádajte šírku stĺpca podľa šírky obrazovky: <ul><li><code>50%</code> znamená, že stĺpec bude mať vždy šírku 50% (t.j. polovica šírky rodičovského bloku) bez ohľadu na šírku obrazovky.</li><li><code>768px:100%</code> zname<PERSON>, že na obrazovkách so šírkou 768px a menš<PERSON>ch (tablety, mobily) bude mať šírku 100% (t.j. cel<PERSON> šírka rodičovského bloku).</li><li><code>1/3; 768px:0.5; 480px:100%</code> znamená, že stĺpec bude mať na veľkých obrazovkách šírku jednej tretiny rodičovského bloku. Na obrazovkách so šírkou 768px a menších (tablety) bude mať šírku 50% a na obrazovkách so šírkou 480px a menších (mobily) bude na celú šírku rodičovského bloku.</li></ul> Jednotky na zadanie šírky stĺpca môžu byť ľubovolné (<code>%</code>, <code>px</code>, <code>mm</code>), no z pohľadu responzivity je ideálne používať pomerné hodnoty, t.j. <code>%</code> alebo desatinné čislo. Napríklad <code>0.5</code> je to isté ako <code>50%</code>. Ako oddeľovač desatinných miest je potrebné použiť bodku <code>.</code> (nie čiarku). Desatinné číslo je možné zadať aj formou zlomku,  napríklad <code>1/3</code>, čo je približne <code>0.3333</code> alebo <code>33.33%</code>. Hraničné šírky obrazoviek musia byť vždy zadané v <code>px</code> a preto pri nich nie je nutné uvádzať jednotky, t.j. <code>768px:100%</code> je to isté ako <code>768:100%</code>. Prednastavená šírka je 50%.')
        ));
    ?></div><?php
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->select('vertical_align', array(
            'label' => __a(__FILE__, 'Zvislé zarovnanie'),
            'hint' => __a(__FILE__, 'Vyberte zvislé (vertikálne) zarovnanie stĺpca so susednými stĺpcami'),
            'options' => array(
                'top' => __a(__FILE__, 'Hore'),
                'middle' => __a(__FILE__, 'Uprostred'),
                'bottom' => __a(__FILE__, 'Dole'),
            )
        ));
    ?></div><?php  
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->checkbox('put_on_end', array(
            'label' => __a(__FILE__, 'Zaradiť na koniec'),
            'hint' => __a(__FILE__, 'Ak má byť stĺpec na velkých obrazovkách zaradený na konci riadku stĺpcov a na malých obrazovkách má byť ako prvý zo zalomených obsahov, tak je potrebné ho umiestniť ako prvý a zašktnúť "Zaradiť na koniec". Pre takého bloky je možné len zvislé zarovnanie hore.'),
        ));
    ?></div><?php  
?></div><?php
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->text('responsive_padding_top', array(
            'label' => __a(__FILE__, 'Priestor hore'),
            'hint' => __a(__FILE__, 'Zádajte priestor hore podľa šírky obrazovky. Napríklad <code>768px:10%</code> znamená, že na obrazovkách so šírkou 768px a menších (tablety) bude priestor hore 10% zo šírky rodičovského bloku. Presný popis možností viď v nápovede k polu "Širka". Prednastavený priestor hore je 0.')
        ));
    ?></div><?php
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->text('responsive_padding_bottom', array(
            'label' => __a(__FILE__, 'Priestor dole'),
            'hint' => __a(__FILE__, 'Zádajte priestor dole podľa šírky obrazovky. Napríklad <code>768px:10%</code> znamená, že na obrazovkách so šírkou 768px a menších (tablety) bude priestor dole 10% zo šírky rodičovského bloku.  Presný popis možností viď v nápovede k polu "Širka". Prednastavený priestor dole je 0.')
        ));
    ?></div><?php
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->text('responsive_padding_left', array(
            'label' => __a(__FILE__, 'Priestor vľavo'),
            'hint' => __a(__FILE__, 'Zádajte priestor vľavo podľa šírky obrazovky. Napríklad <code>10%; 768px:0</code> znamená, že východzí priestor dole na veľkých obrazovkach bude 10% zo šírky rodičovského bloku a na obrazovkách so šírkou 768px a menších (tablety) nebude žiadny. Presný popis možností viď v nápovede k polu "Širka". Prednastavený priestor vľavo je 0.')
        ));
    ?></div><?php
    ?><div class="grid-col grid-col-20-100"><?php 
        echo $Form->text('responsive_padding_right', array(
            'label' => __a(__FILE__, 'Priestor vpravo'),
            'hint' => __a(__FILE__, 'Zádajte priestor vpravo podľa šírky obrazovky. Napríklad <code>10%; 768px:0</code> znamená, že východzí priestor dole na veľkých obrazovkach bude 10% zo šírky rodičovského bloku a na obrazovkách so šírkou 768px a menších (tablety) nebude žiadny. Presný popis možností viď v nápovede k polu "Širka". Prednastavený priestor vpravo je 0.')
        ));
    ?></div><?php
?></div><?php
App::setCssFiles(array(
    '/app/css/grid.css'
));
