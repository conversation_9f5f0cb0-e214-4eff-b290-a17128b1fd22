<?php /* @var $this Template */
/**
 * Use this view to generate owl carousel slider in your frontend views.
 * Include it into your view like: 
 * 
 *      echo App::loadView('ContentBlock', 'owlCarouselSlider', $this->params);
 * 
 * The $this->params must contain 'slider_id'. Optionally 'slider_transition_time', 
 * 'slider_transition_time', 'slider_slide_time', 'slider_auto_play', 'slider_stop_on_hover'
 */
$this->displayOriginComment = true;
$data = &$this->params;
$sliderId = uniqid('slider-');
App::startJsCapture();
?><script type="text/javascript">
    /**
     * Returns object of transition slides containing 'leaving' and 'coming' items,
     * both items are jQuery objects.
     */
    function getOwlCarouselTransitionSlides(slideEvent, debug) {
        var eventType = slideEvent.type, 
            $slider = jQuery(slideEvent.target),
            sliderId = $slider.attr('id'),
            $stage = $slider.find('.owl-stage'),
            $stageItem = $slider.find('.owl-item'),
            $activeStageItem = $stageItem.filter('.active'),
            comingSlideIndex = 0,
            transitionSlides, log, image, translate, i, widths = '';

        transitionSlides = loadNamespace('owlCarouselTransitionSlides.' + sliderId);

        if (eventType === 'initialize') {
            transitionSlides.leaving = null;
            comingSlideIndex = 0;
            transitionSlides.coming = $slider.find('.slide').eq(comingSlideIndex);
        }
        else if (eventType === 'drag') {
            transitionSlides.leaving = $activeStageItem.find('.slide');
            comingSlideIndex = slideEvent.item.index + 1;
            if (comingSlideIndex === $stageItem.length - 1) {
                comingSlideIndex = 0;
            }
            transitionSlides.coming = $stageItem.eq(comingSlideIndex).find('.slide');
        }
        else if (eventType === 'translate') {
            transitionSlides.leaving = $activeStageItem.find('.slide');
            comingSlideIndex = slideEvent.item.index;
            transitionSlides.coming = $stageItem.eq(comingSlideIndex).find('.slide');
        }
        else if (eventType === 'translated') {
            comingSlideIndex = slideEvent.item.index;
            transitionSlides.coming = $stageItem.eq(comingSlideIndex).find('.slide');
        }

        // debug
        if (debug) {
            if (eventType === 'translate') {
                console.log('-----------------------------'); //debug
            }
            console.log('=> on ' + eventType + ' (' + sliderId + ')' + ':'); //debug
            console.log(slideEvent); //debug
            console.log(transitionSlides); //debug
            log = '';
            if (eventType !== 'initialize') {
                image = $activeStageItem.find('.slide-image').attr('style').match(/\/([^\/]+\.jpg)/i);
                if (image) {
                    log += 'active stage item slide image: ' + image[1] + ', ';
                }
            }
            log += 'slide index: ' + slideEvent.item.index + ', ';
            log += 'comingSlideIndex: ' + comingSlideIndex + ', ';
            //log += 'slide count: ' + slideEvent.item.count + ' ';
            log += '$stageItem.length: ' + $stageItem.length + ', ';
            if (eventType !== 'initialize') {
                translate = $stage.attr('style').match(/translate3d\(([^,]+),[^,]+,[^,]+\)/i);
                if (translate) {
                    log += 'stage transform: ' + Math.abs(parseInt(translate[1])) + ', ';
                    log += 'slide index by stage transform: ' + (Math.abs(parseInt(translate[1])) / $stageItem.eq(0).width()) + ', ';

                }
            }
            //log += '$stage.width(): ' + $stage.width() + ', ';
            console.log(log); //debug
            for (i = 1; i < $stageItem.length; i++) {
                widths += $stageItem.eq(i).width() + ', ';
            }
            log = '$stageItem widths: ' + widths;
            console.log(log); //debug
        }

        return transitionSlides;
    }
    jQuery(function(){
        jQuery(window).on('resize', function(){
            var $slider = jQuery('#<?php echo $sliderId ?>');
            var $firstSlide = $slider.find('.slide').eq(0);
            if ($firstSlide.hasExplicitHeight()) {
                $slider.find('.slide').css('height', '').height($slider.height());
            }
        });
    });
</script><?php
App::endJsCapture();
// onInitialize
App::startJsCapture();
?><script type="text/javascript">
    function(event) {
        transitionSlides = getOwlCarouselTransitionSlides(event, false);
        transitionSlides.coming.find('.slide-image').animateCss('animate', {
            reset: false
        });
        transitionSlides.coming.find('.slide-content').animateCss('animate', {
            reset: false
        });
    }
</script><?php
$onInitialize = App::endJsCapture(array('return' => true));
// onInitilized
App::startJsCapture();
?><script type="text/javascript">
    function(event) {
        var $slider = jQuery(event.target);
        var $firstSlide = $slider.find('.slide').eq(0);
        if ($firstSlide.hasExplicitHeight()) {
            $slider.find('.slide').height($slider.height());
        }
    }
</script><?php
$onInitialized = App::endJsCapture(array('return' => true));
// onDrag
App::startJsCapture();
?><script type="text/javascript">
    function(event) {
        var $slider = jQuery(event.target),
            $nonActiveStageItem = $slider.find('.owl-item').not('.active');
        $nonActiveStageItem.find('.slide-content').css('opacity', 0);
    }
</script><?php
$onDrag = App::endJsCapture(array('return' => true));
// onTranslate
App::startJsCapture();
?><script type="text/javascript">
    function(event) {
        transitionSlides = getOwlCarouselTransitionSlides(event, false);
        transitionSlides.coming.find('.slide-image').animateCss('animate', {
            reset: false
        });
        transitionSlides.coming.find('.slide-content').css('opacity', '').animateCss('animate', {
            reset: false
        });
    }
</script><?php
$onTranslate = App::endJsCapture(array('return' => true));
// onTranslated
App::startJsCapture();
?><script type="text/javascript">
    function(event) {
        transitionSlides = getOwlCarouselTransitionSlides(event, false);
        transitionSlides.leaving.find('.slide-image').animateCss('animate', 'reset');
        transitionSlides.leaving.find('.slide-content').animateCss('animate', 'reset');
    }
</script><?php
$onTranslated = App::endJsCapture(array('return' => true));
$sliderOptions = array(
    'id' => $sliderId,
    'type' => 'owlCarousel',
    'sliderId' => $data['slider_id'],
    'onInitialize' => $onInitialize,
    'onInitialized' => $onInitialized,
    'onDrag' => $onDrag,
    'onTranslate' => $onTranslate,
    'onTranslated' => $onTranslated,
    'nav' => true,
    //'animateOut' => 'fadeOut',
    'slideTemplate' => function ($slide){
        $texts = preg_split("/<br ?\/?>/i", $slide['text']);
        $lineBreak = '<br>';
//        $numberOfLines = count($texts);
        foreach ($texts as $key=>&$text) {
            if ($key == (count($texts) - 1) ){
                $lineBreak = '';
            }
            $text = '<span class="slide-text-line">' . trim($text) . '</span>' . $lineBreak;
        }
        $slide['text'] = implode($texts);
        if ( !empty($slide['locator']) && !empty($slide['button']) ){
            $template = '<div class="slide-image" style="background-image: url(\':image:\')"></div><div class="slide-content-wrapper"><div class="slide-content"><div class="slide-text">' . $slide['text'] . '</div><div class="button green sweep-to-right">:button:</div></div></div>';
        } else {
            $template = '<div class="slide-image" style="background-image: url(\':image:\')"></div><div class="slide-content-wrapper"><div class="slide-content"><div class="slide-text">' . $slide['text'] . '</div></div></div>';
        }
        return $template;
    }
);
if (!empty($data['slider_transition_time'])) {
    $sliderOptions['autoplaySpeed'] = (float)$data['slider_transition_time'] * 1000;
}
if (!empty($data['slider_slide_time'])) {
    $sliderOptions['autoplayTimeout'] = (float)$data['slider_slide_time'] * 1000;
    // owlCarousel needs this addition
    if (!empty($data['slider_transition_time'])) {
        $sliderOptions['autoplayTimeout'] += (float)$data['slider_transition_time'] * 1000;
    }
}
if (isset($data['slider_auto_play'])) {
    $sliderOptions['autoplay'] = (bool)$data['slider_auto_play'];
}
if (isset($data['slider_stop_on_hover'])) {
    $sliderOptions['autoplayHoverPause'] = (bool)$data['slider_stop_on_hover'];
}
echo App::loadControllerAction('App', 'WebContentsSliders', 'slider', $sliderOptions);
App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css',
));
App::setJsFiles(array(
    '/app/js/vendors/jquery.min.js',
    '/app/js/libs/jquery.animatecss.js',
    '/app/js/libs/jquery.hasExplicitHeight.js',
));

