<?php /* @var $this Template */
/**
 * Use this view to generate section for block parameters in your content block admin_view form.
 * Include it into your admin_view like: 
 * 
 *      echo App::loadView('ContentBlock', 'admin_blockParamsForm', array_merge($this->params, array(
 *          'showBlockCenterContent' => false, // or set some other option
 *          'additionalHtml' => '<div> ... </div>', // add some additional HTML (appended at the end)
 *      )));
 * 
 * NOTE: For your content block view (frontend) see ContentBlock/views/blockParamsCssAndJs.php
 * NOTE: For list o available options see defined $defaults
 */
$this->displayOriginComment = true;
$defaults = array(
    'showHeading' => true,
    'showBlockTopPadding' => true,
    'showBlockBottomPadding' => true,
////as the block wrapper (.cb-...) has always full width (width of window) it is 
////up to .center block (or even some more inner) to set the side paddings of block content
//    'showBlockLeftPadding' => false,
//    'showBlockRightPadding' => false,
    'showBlockCenterContent' => true,
    'showBlockBgGradient' => true,
    'showBlockBgImage' => true,
    'showBlockBgColor' => true,
    'showBlockBgHorizontalPosition' => true,
    'showBlockBgVerticalPosition' => true,
    'showBlockBgSize' => true,
    'showBlockBgRepeat' => true,
    'showBlockBgParallax' => true,
    'additionalHtml' => null,
);
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];
$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
$options = array_merge($defaults, array_intersect_key($this->params, $defaults));
if ($options['showHeading']) {
    ?><h2 class="-run-sfo-heading collapsible-section-heading collapsed" style="cursor:pointer"><?php 
        echo __a(__FILE__, 'Vlastnosti bloku')
    ?></h2><?php
    ?><div class="collapsible-section collapsed"><?php 
}
    ?><div class="grid-row grid-break-780"><?php 
        if (
            $options['showBlockTopPadding']
            || $options['showBlockBottomPadding']
////see the comment above                
//            || $options['showBlockLeftPadding']
//            || $options['showBlockRightPadding']
            || $options['showBlockCenterContent']
        ) {    
            ?><div class="grid-col grid-col-20-100"><?php 
                if ($options['showBlockTopPadding']) {  
                    echo $Form->text('block_top_padding', array(
                        'label' => __a(__FILE__, 'Priestor hore'),
                        'hint' => __a(__FILE__, 'Zádajte priestor hore podľa šírky obrazovky: <ul><li><code>50px</code> znamená, že priestor hore bude vždy 50px bez ohľadu na šírku obrazovky.</li><li><code>768px:10%</code> znamená, že na obrazovkách so šírkou 768px a menších (tablety, mobily) bude 10% zo šírky rodičovského bloku.</li><li><code>50/1170; 768px:33px; 480px:20px</code> znamená, že východzí priestor hore na veľkých obrazovkach bude zlomok 50/1170 zo šírky rodičovského bloku (t.j. ak je rodič široký 1170px tak priestor hore bude presne 50px, pri zužovaní rodiča sa bude zmenšovať). Na obrazovkách so šírkou 768px a menších (tablety) bude priestor hore 33px a na obrazovkách so šírkou 480px a menších (mobily) bude priestor hore 20px.</li></ul> Jednotky na zadanie priestoru môžu byť ľubovolné (<code>%</code>, <code>px</code>, <code>mm</code>), no z pohľadu responzivity je ideálne používať pomerné hodnoty, t.j. <code>%</code> alebo desatinné čislo. Napríklad <code>0.5</code> je to isté ako <code>50%</code>. Ako oddeľovač desatinných miest je potrebné použiť bodku <code>.</code> (nie čiarku). Desatinné číslo je možné zadať aj formou zlomku,  napríklad <code>1/3</code>, čo je približne <code>0.3333</code> alebo <code>33.33%</code>. Hraničné šírky obrazoviek musia byť vždy zadané v <code>px</code> a preto pri nich nie je nutné uvádzať jednotky, t.j. <code>768px:10%</code> je to isté ako <code>768:10%</code>.'),
                    ));
                }
                if ($options['showBlockBottomPadding']) {  
                    echo $Form->text('block_bottom_padding', array(
                        'label' => __a(__FILE__, 'Priestor dole'),
                        'hint' => __a(__FILE__, 'Zádajte priestor dole podľa šírky obrazovky: <ul><li><code>50px</code> znamená, že priestor dole bude vždy 50px bez ohľadu na šírku obrazovky.</li><li><code>768px:10%</code> znamená, že na obrazovkách so šírkou 768px a menších (tablety, mobily) bude 10% zo šírky rodičovského bloku.</li><li><code>50/1170; 768px:33px; 480px:20px</code> znamená, že východzí priestor dole na veľkých obrazovkach bude zlomok 50/1170 zo šírky rodičovského bloku (t.j. ak je rodič široký 1170px tak priestor dole bude presne 50px, pri zužovaní rodiča sa bude zmenšovať). Na obrazovkách so šírkou 768px a menších (tablety) bude priestor dole 33px a na obrazovkách so šírkou 480px a menších (mobily) bude priestor dole 20px.</li></ul> Jednotky na zadanie priestoru môžu byť ľubovolné (<code>%</code>, <code>px</code>, <code>mm</code>), no z pohľadu responzivity je ideálne používať pomerné hodnoty, t.j. <code>%</code> alebo desatinné čislo. Napríklad <code>0.5</code> je to isté ako <code>50%</code>. Ako oddeľovač desatinných miest je potrebné použiť bodku <code>.</code> (nie čiarku). Desatinné číslo je možné zadať aj formou zlomku,  napríklad <code>1/3</code>, čo je približne <code>0.3333</code> alebo <code>33.33%</code>. Hraničné šírky obrazoviek musia byť vždy zadané v <code>px</code> a preto pri nich nie je nutné uvádzať jednotky, t.j. <code>768px:10%</code> je to isté ako <code>768:10%</code>.'),
                    ));
                }
////see the comment above                
//                if ($options['showBlockLeftPadding']) {  
//                    echo $Form->text('block_left_padding', array(
//                        'label' => __a(__FILE__, 'Priestor vľavo'),
//                        'hint' => __a(__FILE__, 'Zádajte priestor vľavo podľa šírky obrazovky. Viac viď v nápovede k vstupom "Priestor hore" alebo "Priestor dole".'),
//                    ));
//                }
//                if ($options['showBlockRightPadding']) {  
//                    echo $Form->text('block_right_padding', array(
//                        'label' => __a(__FILE__, 'Priestor vpravo'),
//                        'hint' => __a(__FILE__, 'Zádajte priestor vpravo podľa šírky obrazovky. Viac viď v nápovede k vstupom "Priestor hore" alebo "Priestor dole".'),
//                    ));
//                }
                if ($options['showBlockCenterContent']) {
                    echo $Form->select('block_center_content', array(
                        'label' => __a(__FILE__, 'Šírka obsahu'),
                        'hint' => __a(__FILE__, 'Má byť šírka HTML obsahu obmedzená na veľkých obrazovkách len na centrálny blok o zvolenej šírke alebo má byť na celú šírku obrazovky.'),
                        'options' => array(
                            '' => __a(__FILE__, 'Celá šírka obrazovky'),
                            'wide' => __a(__FILE__, 'Centrálny blok'),
                            //'narrow' => __a(__FILE__, 'Úžší centrálny blok'),
                        )
                    ));
                }
            ?></div><?php
        }
        if (
            $options['showBlockBgGradient']
            || $options['showBlockBgImage']
            || $options['showBlockBgColor']
        ) {    
            ?><div class="grid-col grid-col-20-100"><?php 
                /*
                if ($options['showBlockBgGradient']) {  
                    echo $Form->text('block_bg_gradient', array(
                        'label' => __a(__FILE__, 'Farebný prechod'),
                        'hint' => __a(__FILE__, 'Zadajte farebný prechod pozadia, napr. <code>linear-gradient(180deg, grey 0%, transparent 100%)</code>. Kód prechodu môžete vygenerovať aj pomocou <a href="https://www.google.com/search?q=css+gradient+online+generator" target="_blank">webového nástroja</a>. Prechod je umiestnený nad obrázkom.'),
                    ));
                }
                 */
                if ($options['showBlockBgImage']) {  
                    echo $Form->file('block_bg_image', array(
                        'label' => __a(__FILE__, 'Obrázok pozadia'),
                        'hint' => __a(__FILE__, 'Vyberte obrázok pozadia bloku. Keďže ide o pomerne veľký obrázok, tak aj z ohľadu voči mobilným datam návštevníkov webu, je vhodné ho pred nahratím uložiť vo formate JPG a minimalizovať jeho veľkosť pomocou <a href="https://compressor.io/compress" target="_blank">online kompresora</a>. Ak obrázku pozadia nastavíte aj paralaxu, tak obrázok by mal mať dostatočnú rezervu hore aj dolu. Riešením môže tiež byť vytvorenie symetrického obrázku s dvojnásobnou výškou, tak že obrázok sa prevrati zhora nadol a tato prevratena verzia sa prida na spodok pôvodného obrazku.'),
                    ));
                }
                if ($options['showBlockBgColor']) {  
                    echo $Form->text('block_bg_color', array(
                        'label' => __a(__FILE__, 'Farba pozadia'),
                        'hint' => __a(__FILE__, 'Zadajte farbu pozadia bloku podľa šírky obrazovky, napríklad <code>transparent; 768px:rgba(255,255,255,0.5)</code>. Použite niektorý z webových formátov zapisovania farieb, napr. <code>black</code>, <code>#000</code>, <code>rgb(0,0,0)</code>, <code>rgba(0,0,0,0.5)</code>. Priesvitnú farbu zadajte ako <code>transparent</code>. Ak nie je zadaná, tak sa použije prednastavená'),
                    ));
                }
            ?></div><?php
        }
        if ($options['showBlockBgImage']) {
            ?><div class="grid-col grid-col-20-100"><?php 
                echo $Form->image('block_bg_image', array(
                    'deleteImage' => '/mvc/App/ContentBlockInstances/admin_deleteInstanceFile/block_bg_image/' . $blockInstanceId,
                ));
            ?></div><?php
        }
        if (
            $options['showBlockBgImage']
            && (
                $options['showBlockBgVerticalPosition']
                || $options['showBlockBgHorizontalPosition']
                || $options['showBlockBgSize']
            )
        ) {    
            ?><div class="grid-col grid-col-20-100"><?php 
                if ($options['showBlockBgHorizontalPosition']) {
                    echo $Form->select('block_bg_horizontal_position', array(
                        'label' => __a(__FILE__, 'Vodorovné umiestnenie obrázku'),
                        'hint' => __a(__FILE__, 'Vyberte vodorovné (horizontálne) umiestnenie obrázku pozadia'),
                        'options' => array(
                            '' => '',
                            'center' => __a(__FILE__, 'Uprostred'),
                            'left' => __a(__FILE__, 'Vľavo'),
                            'right' => __a(__FILE__, 'Vpravo'),
                        )
                    ));
                }
                if ($options['showBlockBgVerticalPosition']) {
                    echo $Form->select('block_bg_vertical_position', array(
                        'label' => __a(__FILE__, 'Zvislé umiestnenie obrázku'),
                        'hint' => __a(__FILE__, 'Vyberte zvislé (vertikálne) umiestnenie obrázku pozadia. Pri použití symetrického obrázku (viď napovedu k "Obrázok pozadia") je vhodné použiť umiestnenie "Horná štvrtina."'),
                        'options' => array(
                            '' => '',
                            'center' => __a(__FILE__, 'Uprostred'),
                            'top' => __a(__FILE__, 'Hore'),
                            '25%' => __a(__FILE__, 'Horná štvrtina'),
                            'bottom' => __a(__FILE__, 'Dole'),
                        )
                    ));
                }
                if ($options['showBlockBgSize']) {
                    echo $Form->select('block_bg_size', array(
                        'label' => __a(__FILE__, 'Veľkosť obrázku'),
                        'hint' => __a(__FILE__, 'Vyberte umiestnenie obrázku pozadia.'),
                        'options' => array(
                            '' => '',
                            'auto' => __a(__FILE__, 'Automaticky'),
                            'contain' => __a(__FILE__, 'Obsiahnúť'),
                            'cover' => __a(__FILE__, 'Pokryť'),
                        )
                    ));
                }
            ?></div><?php
        }
        if (
            $options['showBlockBgImage']
            && (
                $options['showBlockBgRepeat']
                || $options['showBlockBgParallax']
            )
        ) {    
            ?><div class="grid-col grid-col-20-100"><?php 
                if ($options['showBlockBgRepeat']) {
                    echo $Form->select('block_bg_repeat', array(
                        'label' => __a(__FILE__, 'Opakovanie obrázku'),
                        'hint' => __a(__FILE__, 'Vyberte opakovanie obrázku pozadia.'),
                        'options' => array(
                            '' => '',
                            'no-repeat' => __a(__FILE__, 'Neopakovat'),
                            'repeat' => __a(__FILE__, 'Opakovat'),
                            'repeat-x' => __a(__FILE__, 'Opakovat vodorovne'),
                            'repeat-y' => __a(__FILE__, 'Opakovat zvislo'),
                        )
                    ));
                }
                if ($options['showBlockBgParallax']) {
                    echo $Form->text('block_bg_parallax', array(
                        'label' => __a(__FILE__, 'Paralaxa obrázku'),
                        'hint' => __a(__FILE__, 'Zadajte veľkosť paralaxy obrázku pozadia formou desatinného čísla, napr. <code>0.5</code>. Pevné pozadie (bez paralaxy) zadajte ako <code>0</code>. Ak nie je zadaná, tak sa použije prednastavená.'),
                    ));
                }
            ?></div><?php
        }
    ?></div><?php
    if (!empty($options['additionalHtml'])) {
        echo $options['additionalHtml'];
    }
if ($options['showHeading']) {
    ?></div><?php
}    
App::setCssFiles(array(
    '/app/css/grid.css'
));