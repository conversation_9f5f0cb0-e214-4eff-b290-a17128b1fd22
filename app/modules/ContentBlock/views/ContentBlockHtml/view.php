<?php /* @var $this Template */
$this->displayOriginComment = true;
$data = &$this->params;
if (empty($data['html'])) {
    return;
}
$uniqueClass = uniqid('cb-');
?><div class="cb-html <?php echo $uniqueClass ?>"><?php 
    if (!empty($data['block_center_content'])) {
        if ($data['block_center_content'] === 'narrow') {
            ?><div class="center narrow"><?php
        }
        else {
            ?><div class="center"><?php
        }
    }
    echo $data['html'];
    if (!empty($data['block_center_content'])) {
        ?></div><?php
    }
?></div><?php
App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css'
));
App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueClass' => $uniqueClass)));
