<?php /* @var $this Template */
$this->displayOriginComment = true;
/* @var $Form FormHelper */
$Form = &$this->params['Form'];
$data = &$this->params['data'];
$blockInstanceId = Sanitize::value($data['run_content_block_instances_id']);
/*/
echo App::loadView('ContentBlock', 'admin_blockTitleForm', array_merge($this->params, array(
    'showTitleTag' => false,
    'showTitleTextMenuIntegration' => false,
    //'showTitleColor' => true
)));
/*/
?><h2 class="-run-sfo-heading"><?php 
    echo __a(__FILE__, 'Obr<PERSON>zky s textami');
    echo Html::hint(__a(__FILE__, 'Zobrazia sa len položky, ktoré zadáte. Je vhodné pretestovať aj responzívnu verziu zmenšovaním okna prehliadača (napr. Firefox-u sa dá nastaviť ľubovolná širka okna).'));
?></h2><?php
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-100-100"><?php 
        echo $Form->file('image', array(
            'label' => __a(__FILE__, 'Obrázok'),
            'hint' => __a(__FILE__, 'Vyberte obrázok o šírke 1190px. Výška obrázku nie je stanovená.')
        ));
        echo $Form->image('image', array(
            'deleteImage' => '/mvc/App/ContentBlockInstances/admin_deleteInstanceFile/image/' . $blockInstanceId,
        ));
        echo $Form->checkbox('veiled_image', array(
            'label' => __a(__FILE__, 'Stmaviť obrázok'),
            'hint' => __a(__FILE__, 'Má sa obrázok stmaviť pomocou tmavého priehľadného závoja?. Toto je vhodné použiť v prípade keď biely text nie je dostatočne viditeľný nad svetlým obrázkom.')
        ));
    ?></div><?php
?></div><?php
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-50-100"><?php 
        echo $Form->text('url', array(
            'label' => __a(__FILE__, 'URL'),
            'hint' => __a(__FILE__, 'URL adresa, na ktorú je obrázok prelinkovaný'),
        ));
        echo $Form->text('title', array(
            'label' => __a(__FILE__, 'Nadpis'),
        ));
        echo $Form->textarea('text', array(
            'label' => __a(__FILE__, 'Text'),
        ));
        echo $Form->text('button_label', array(
            'label' => __a(__FILE__, 'Text tlačidla'),
            'hint' => __a(__FILE__, 'Toto tlačidlo má len vizuálny efekt ("call to action"). Na horezadané URL je prelinkovaný celý obrázok'),
        ));
    ?></div><?php
?></div><?php
?><div class="grid-row grid-break-780"><?php 
    ?><div class="grid-col grid-col-50-100"><?php 
        echo $Form->checkbox('has_small_gap', array(
            'label' => __a(__FILE__, 'Malá medzera'),
            'hint' => __a(__FILE__, 'Aká má byť medzera medzi blokmi umiestnenými nad sebou?')
        ));
    ?></div><?php
?></div><?php
echo App::loadView('ContentBlock', 'admin_blockParamsForm', array_merge($this->params, array(
    //'showBlockCenterContent' => false,
)));
App::setCssFiles(array(
    '/app/css/grid.css'
));
