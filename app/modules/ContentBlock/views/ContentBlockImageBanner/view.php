<?php /* @var $this Template */
$this->displayOriginComment = true;
$data = &$this->params;
$uniqueClass = uniqid('cb-');
$uniqueSelector = '.' . $uniqueClass;
$class = 'cb-image-banner';
$hasSmallGap = Sanitize::value($data['has_small_gap']);
if ($hasSmallGap) {
    $class .= ' with-small-gap';
    $uniqueSelector .= '.with-small-gap';
}
$class .= ' ' . $uniqueClass;
?><section class="<?php echo $class ?>"><?php 
    if (!empty($data['block_center_content'])) {
        if ($data['block_center_content'] === 'narrow') {
            ?><div class="center narrow"><?php
        }
        else {
            ?><div class="center"><?php
        }
    }
    /*/
    echo App::loadView('ContentBlock', 'blockTitle', array_merge($this->params, array('uniqueSelector' => $uniqueSelector)));
    /*/
    $image = Sanitize::value($data['image']);
    $url = Sanitize::value($data['url']);
    $title = Sanitize::value($data['title']);
    $text = Sanitize::value($data['text']);
    $buttonLabel = Sanitize::value($data['button_label']);
    $veiledImage = Sanitize::value($data['veiled_image']);
    if ($image) {
        $class = '';
        if ($veiledImage) {
            $class .= 'veiled-image';
        }
        $style = sprintf('background-image: url(%s);', $image);
        ?><article class="<?php echo $class ?>" style="<?php echo $style ?>"><?php 
            if ($url) {
                ?><a href="<?php echo $url ?>" class="wrapper"><?php 
            }
            else {
                ?><div class="wrapper"><?php 
            }
            if ($image) {
                ?><img src="<?php echo $image ?>" class="image"/><?php
            }
            if ($title || $text || $buttonLabel) {
                ?><div class="content"><?php 
                    if ($title) {
                        ?><div class="title"><?php 
                            echo $title;
                        ?></div><?php                                
                    }
                    if ($text) {
                        ?><div class="text"><?php 
                            echo nl2br($text);
                        ?></div><?php
                    }
                    if ($buttonLabel) {
                        ?><div class="button"><?php 
                            echo $buttonLabel;
                        ?></div><?php                                
                    }
                ?></div><?php
            }
            if (!$url) {
                ?></div><?php
            }
            else {
                ?></a><?php
            }
        ?></article><?php
    }
    if (!empty($data['block_center_content'])) {
        ?></div><?php
    }
?></section><?php
App::setCssFiles(array(
    '/app/modules/ContentBlock/css/main.css'
));
App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueSelector' => $uniqueSelector)));

