@import "_variables.less";
@import "_mixins.less";
@import "_animations.less";

body#content-block-preview-layout {
    background-color: @color-page-bg;
}

.cb-background {
    .clear();
    background-size: auto;
    background-position: center;
    background-repeat: no-repeat;
}

.cb-column, .cb-html-column {
    display: inline-block;
    width: 50%;
    padding: 0;
    &.put-on-end {
        float: right;
    }
}

.cb-line-break {
    clear: both;
    margin: 0;
    height: 0;
    padding: 0;
    background: transparent;
    & + & {
        // keep this in accord with body line-height in app main.less
        padding-top: 23px / @font-size * 1em;
    }
}

.cb-title {
    &.h1 {
        border-bottom: none;
    }
    .title {
        margin: 0;
        padding: 0;
        &.animate {
            .animateFadeInLeft();
        }
    }
}

.cb-web-content, .cb-web-contents-accordion {
    // if content block has defined following block params then reset them on the
    // nested content blocks
    &.with-top-padding {
        > *:first-child {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
        &.with-center {
            > .center > *:first-child {
                margin-top: 0 !important;
                padding-top: 0 !important;
            }
        }
    }
    &.with-bottom-padding {
        > *:last-child {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }
        &.with-center {
            > .center > *:last-child {
                margin-bottom: 0 !important;
                padding-bottom: 0 !important;
            }
        }
    }
    &.with-background {
        > * {
            background: none !important;
        }
        &.with-center {
            > .center > * {
                background: none !important;
            }
        }
    }
}
.cb-web-contents-accordion {
    &.animate {
        //.animateFadeInUpTiny();
    }
}

// EXAMPLE 001
.cb-example-001 {
    padding: 50px 0;
    .title {
        font-size: 27px;
        font-weight: 700;
        text-align: center;
        margin: 0;
        padding: 0;
    }
    .title + .columns {
        margin-top: 35px;
    }
    .columns {
        text-align: center;
        .column {
            display: inline-block;
            vertical-align: top;
            width: 1/3 * 100%;
            padding: 0 2vw;
            .image {
                position: relative;
                overflow: hidden;
                margin: 0 auto;
                border-radius: 9999px;
                background-color: #fff;
                img {
                    display: block;
                    width: 100%;
                    height: 100%;
                }
            }
            .text {
                margin-top: 26px;
                margin-bottom: 26px;
                font-size: 20px;
                color: @color-main;
                font-weight: 600;
                line-height: 30px/20px * 1em;
                letter-spacing: -0.1px;
            }
        }
    }
}

.cb-google-map {
    .googlemap {
        display: block;
        height: 600px;
    }
}

// QUOTATIONS
.cb-quotations {
    .quotation {
        text-align: center;
        padding: 16px;
        border-radius: 4px;
        margin: 0 0 16px;
        background-color: @color-light-cream;
        box-shadow: 0 0 16px fade(@color-black, 16%);
    }
    .text {
        font-size: 20px;
        font-style: italic;
        line-height: 1.5;
        margin: 0;
        &:before {
            content: open-quote;
        }
        &:after {
            content: close-quote;
        }
    }
    .author {
        font-style: normal;
        display: inline-block;
        margin-top: 8px;
    }
}

// PRODUCTS SLIDER

// SLIDER
.cb-slider {
    // use padding (not margin) to allow user change it by cb "Vlastnosti bloku"
    padding-top: 20px;
    &.with-small-gap {
        // use padding (not margin) to allow user change it by cb "Vlastnosti bloku"
        padding-top: 10px;
    }
}

// IMAGE BANNER
.cb-image-banner {
    // use padding (not margin) to allow user change it by cb "Vlastnosti bloku"
    padding-top: 20px;
    article {
        position: relative; // .content, &:before
        overflow: hidden;
        border-radius: 6px;
        background-repeat: no-repeat;
        background-size: cover;            
        background-position: center;            
        &.veiled-image {
            &:before {
                content: '';
                display: block;
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                z-index: 1;
                background-color: #000;
                opacity: 0.32;
            }
        }
        .wrapper {
            text-decoration: none; // bug fix for the grid used here below
            // stock .image and .content on top of each other by using .grid
            // (see https://stackoverflow.com/q/6780614/1245149 )
            // This lets both .image and .content as regular .wrapper content
            // which means that .wrapper contains both (which is not true if .content
            // is absolutely positioned over .image)
            display: grid;
            & > * {
                grid-row: 1;
                grid-column: 1;
            }
        }
        .image {
            display: block;
            min-width: 100%;
            height: 100%;
            // hidden image is used just to properly size the .wrapper block,
            // the image itself is displayed as article background to profit
            // from background-size: cover; once the whole image cannot be displayed
            visibility: hidden;
        }
        .content {
            position: relative;
            z-index: 2; // to place .content over image veil (article:before)
            padding-top: 26px / @center-width * 100%;
            padding-right: 26px / @center-width * 100%;
            padding-bottom: 26px / @center-width * 100%;
            padding-left: 43px / @center-width * 100%;
            .title {
                font-size: 25px;
                line-height: 28/25;
                letter-spacing: -0.7px;
                font-weight: bold;
                color: @color-light-cream;
                margin-bottom: 18px;
            }
            .text {
                font-size: 14px;
                line-height: 21/14;
                letter-spacing: -0.39px;
                color: @color-light-cream;
                margin-bottom: 18px;
                max-width: 470px;
            }
            .button {}
        }
    }
    &.with-small-gap {
        // use padding (not margin) to allow user change it by cb "Vlastnosti bloku"
        padding-top: 10px;
    }
}

// TWO IMAGES BANNER
.cb-two-images-banner {
    // use padding (not margin) to allow user change it by cb "Vlastnosti bloku"
    padding-top: 20px;
    .columns {
        display: flex;
        .column {
            position: relative; // .content, &:before
            overflow: hidden;
            border-radius: 6px;
            background-repeat: no-repeat;
            background-size: cover;            
            background-position: center;            
            // 2 items per row
            width: 585px / @center-width * 100%;
            margin-left: 20px / @center-width * 100%;
            &:nth-of-type(2n + 1) {
                margin-left: 0;
            }
            &.veiled-image {
                &:before {
                    content: '';
                    display: block;
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    z-index: 1;
                    background-color: #000;
                    opacity: 0.32;
                }
            }
            .wrapper {
                text-decoration: none; // bug fix for the grid used here below
                // stock .image and .content on top of each other by using .grid
                // (see https://stackoverflow.com/q/6780614/1245149 )
                // This lets both .image and .content as regular .wrapper content
                // which means that .wrapper contains both (which is not true if .content
                // is absolutely positioned over .image)
                display: grid;
                & > * {
                    grid-row: 1;
                    grid-column: 1;
                }
            }
            .image {
                display: block;
                min-width: 100%;
                height: 100%;
                // hidden image is used just to properly size the .wrapper block,
                // the image itself is displayed as .column background to profit
                // from background-size: cover; once the whole image cannot be displayed
                visibility: hidden;
            }
            .content {
                position: relative;
                z-index: 2; // to place .content over image veil (.column:before)
                padding-top: 26px / 585px * 100%;
                padding-right: 26px / 585px * 100%;
                padding-bottom: 26px / 585px * 100%;
                padding-left: 43px / 585px * 100%;
                .title {
                    font-size: 25px;
                    line-height: 28/25;
                    letter-spacing: -0.7px;
                    font-weight: bold;
                    color: @color-light-cream;
                    margin-bottom: 18px;
                }
                .text {
                    font-size: 14px;
                    line-height: 21/14;
                    letter-spacing: -0.39px;
                    color: @color-light-cream;
                    margin-bottom: 18px;
                    max-width: 265px;
                }
                .button {}
            }
        }
    }
    &.with-small-gap {
        // use padding (not margin) to allow user change it by cb "Vlastnosti bloku"
        padding-top: 10px;
        .columns {
            .column {
                // 2 items per row
                width: 590px / @center-width * 100%;
                margin-left: 10px / @center-width * 100%;
                &:nth-of-type(2n + 1) {
                    margin-left: 0;
                }
                .content {
                    padding-top: 26px / 590px * 100%;
                    padding-right: 26px / 590px * 100%;
                    padding-bottom: 26px / 590px * 100%;
                    padding-left: 43px / 590px * 100%;
                }
            }
        }
    }
}

// TWO ASYMMETRIC IMAGES BANNER
.cb-two-asymmetric-images-banner {
    // use padding (not margin) to allow user change it by cb "Vlastnosti bloku"
    padding-top: 20px;
    .columns {
        display: flex;
        .column {
            position: relative; // .content, &.veiled-image:before, .background-small-image
            overflow: hidden;
            border-radius: 6px;
            background-repeat: no-repeat;
            background-size: cover;            
            background-position: center;            
            // 2 items per row
            width: 685px / @center-width * 100%;
            margin-left: 20px / @center-width * 100%;
            &:first-child {
                margin-left: 0;
            }
            &.veiled-image {
                &:before {
                    content: '';
                    display: block;
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    z-index: 1;
                    background-color: #000;
                    opacity: 0.32;
                }
            }
            .wrapper {
                text-decoration: none; // bug fix for the grid used here below
                // stock .image and .content on top of each other by using .grid
                // (see https://stackoverflow.com/q/6780614/1245149 )
                // This lets both .image and .content as regular .wrapper content
                // which means that .wrapper contains both (which is not true if .content
                // is absolutely positioned over .image)
                display: grid;
                & > * {
                    grid-row: 1;
                    grid-column: 1;
                }
            }
            .big-image,
            .small-image {
                display: block;
                min-width: 100%;
                height: 100%;
                // hidden image is used just to properly size the .wrapper block,
                // the image itself is displayed as .column background to profit
                // from background-size: cover; once the whole image cannot be displayed
                visibility: hidden;
            }
            .background-small-image {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                z-index: 0;
                background-repeat: no-repeat;
                background-size: cover;            
                background-position: center;            
            }
            .content {
                position: relative;
                z-index: 2; // to place .content over image veil (.column:before)
                padding-top: 26px / 685px * 100%;
                padding-right: 26px / 685px * 100%;
                padding-bottom: 26px / 685px * 100%;
                padding-left: 43px / 685px * 100%;
                .title {
                    font-size: 25px;
                    line-height: 28/25;
                    letter-spacing: -0.7px;
                    font-weight: bold;
                    color: @color-light-cream;
                    margin-bottom: 18px;
                }
                .text {
                    font-size: 14px;
                    line-height: 21/14;
                    letter-spacing: -0.39px;
                    color: @color-light-cream;
                    margin-bottom: 18px;
                    max-width: 265px;
                }
                .button {}
            }
            &:first-child {
                .small-image,
                .background-small-image {
                    display: none;
                }
            }
            &:last-child {
                width: 485px / @center-width * 100%;
                .big-image {
                    display: none;
                }
                .content {
                    padding-top: 26px / 485px * 100%;
                    padding-right: 26px / 485px * 100%;
                    padding-bottom: 26px / 485px * 100%;
                    padding-left: 43px / 485px * 100%;
                }
            }
        }
    }
    &.with-left-small-image {
        .columns {
            .column {
                &:first-child {
                    width: 485px / @main-content-width * 100%;
                    .small-image,
                    .background-small-image {
                        display: block;
                    }
                    .big-image {
                        display: none;
                    }
                    .content {
                        padding-top: 26px / 485px * 100%;
                        padding-right: 26px / 485px * 100%;
                        padding-bottom: 26px / 485px * 100%;
                        padding-left: 43px / 485px * 100%;
                    }
                }
                &:last-child {
                    width: 685px / @main-content-width * 100%;
                    .small-image,
                    .background-small-image {
                        display: none;
                    }
                    .big-image {
                        display: block;
                    }
                    .content {
                        padding-top: 26px / 685px * 100%;
                        padding-right: 26px / 685px * 100%;
                        padding-bottom: 26px / 685px * 100%;
                        padding-left: 43px / 685px * 100%;
                    }
                }
            }
        }
    }
    &.with-small-gap {
        // use padding (not margin) to allow user change it by cb "Vlastnosti bloku"
        padding-top: 10px;
        .columns {
            .column {
                // 2 items per row
                width: 690px / @main-content-width * 100%;
                margin-left: 10px / @center-width * 100%;
                &:first-child {
                    margin-left: 0;
                }
                .content {
                    padding-top: 26px / 690px * 100%;
                    padding-right: 26px / 690px * 100%;
                    padding-bottom: 26px / 690px * 100%;
                    padding-left: 43px / 690px * 100%;
                }
                &:last-child {
                    width: 490px / @main-content-width * 100%;
                    .content {
                        padding-top: 26px / 490px * 100%;
                        padding-right: 26px / 490px * 100%;
                        padding-bottom: 26px / 490px * 100%;
                        padding-left: 43px / 490px * 100%;
                    }
                }
            }
        }
        &.with-left-small-image {
            .columns {
                .column {
                    &:first-child {
                        width: 490px / @main-content-width * 100%;
                        .content {
                            padding-top: 26px / 490px * 100%;
                            padding-right: 26px / 490px * 100%;
                            padding-bottom: 26px / 490px * 100%;
                            padding-left: 43px / 490px * 100%;
                        }
                    }
                    &:last-child {
                        width: 690px / @main-content-width * 100%;
                        .content {
                            padding-top: 26px / 690px * 100%;
                            padding-right: 26px / 690px * 100%;
                            padding-bottom: 26px / 690px * 100%;
                            padding-left: 43px / 690px * 100%;
                        }
                    }
                }
            }
        }        
    }
}

// MODULE RESPONSIVE STYLES
@import (less) "_responsive.less";