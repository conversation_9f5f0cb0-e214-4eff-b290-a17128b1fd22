@import "_variables.less";
@import "_mixins.less";

//
// CUSTOM MEDIA QUERIES
//

// INITIAL QUERY AT CENTER-WIDTH
@media screen and (max-width: @center-width + @grid-cols-gap-width) {
    // nested .cb-background blocks (e.g. in wrapping content blocks) 
    // should not add additional padding but in the same time should 
    // cover whole width. See also .center responsive styles
    // ATTENTION: Important for nested content blocks implementation
    .cb-background {
        .cb-background {
            margin-left: -@grid-cols-gap-width / 2;
            margin-right: -@grid-cols-gap-width / 2;
            > .center {
                padding-left: @grid-cols-gap-width / 2;
                padding-right: @grid-cols-gap-width / 2;      
            }
        }
    }
}
@media screen and (max-width: @center-width) {
    .cb-slider,
    .cb-image-banner,
    .cb-two-images-banner,
    .cb-two-asymmetric-images-banner {
        padding-top: 20px / @center-width * 100%;
        &.with-small-gap {
            padding-top: 10px / @center-width * 100%;
        }
    }
}

@media screen and (max-width: 660px) {
    // keep the same paddings on all image banners 
    .cb-slider {
        padding-top: 3.41%; //20px / 585px * 100%;
        &.with-small-gap {
            padding-top: 1.69%; //10px / 590px * 100%;
        }
    }
    .cb-image-banner {
        padding-top: 3.41%; //20px / 585px * 100%;
        .content {
            padding-top: 4.44%; //26px / 585px * 100%;
            padding-right: 4.44%; //26px / 585px * 100%;
            padding-bottom: 4.44%; //26px / 585px * 100%;
            padding-left: 7.35%; //43px / 585px * 100%;
        }
        &.with-small-gap {
            padding-top: 1.69%; //10px / 590px * 100%;
            .content {
                padding-top: 4.4%; //26px / 590px * 100%;
                padding-right: 4.4%; //26px / 590px * 100%;
                padding-bottom: 4.4%; //26px / 590px * 100%;
                padding-left: 7.28%; //43px / 590px * 100%;
            }
        }
    }
    .cb-two-images-banner {
        padding-top: 3.41%; //20px / 585px * 100%;
        .columns {
            display: block;
            .column {
                // 1 items per row
                width: 100%;
                margin-top: 3.41%; //20px / 585px * 100%;
                margin-left: 0;
                &:nth-of-type(n) {
                    margin-left: 0;
                }
                &:first-child {
                    margin-top: 0;
                }
            }
        }
        &.with-small-gap {
            padding-top: 1.69%; //10px / 590px * 100%;
            .columns {
                .column {
                    // 2 items per row
                    width: 100%;
                    margin-top: 1.69%; //10px / 590px * 100%;
                    margin-left: 0;
                    &:nth-of-type(n) {
                        margin-left: 0;
                    }
                    &:first-child {
                        margin-top: 0;
                    }
                }
            }
        }
    }
    .cb-two-asymmetric-images-banner {
        padding-top: 3.41%; //20px / 585px * 100%;
        &, &.with-left-small-image {
            .columns {
                display: block;
                .column {
                    &:first-child, &:last-child {
                        // 1 items per row
                        width: 100%;
                        margin-top: 3.41%; //20px / 585px * 100%;
                        margin-left: 0;
                        .small-image,
                        .background-small-image {
                            display: none;
                        }
                        .big-image {
                            display: block;
                        }
                    }
                    &:first-child {
                        margin-top: 0;
                    }
                }
            }
            &.with-small-gap {
                padding-top: 1.69%; //10px / 590px * 100%;
                .columns {
                    .column {
                        &:first-child, &:last-child {
                            // 2 items per row
                            width: 100%;
                            margin-top: 1.69%; //10px / 590px * 100%;
                        }
                        &:first-child {
                            margin-top: 0;
                        }
                    }
                }
            }
        }
    }
}

@media screen and (max-width: 520px) {
    // on small screens display small images instead of big ones
    .cb-two-asymmetric-images-banner {
        &, &.with-left-small-image {
            .columns {
                .column {
                    &:first-child, &:last-child {
                        .small-image,
                        .background-small-image {
                            display: block;
                        }
                        .big-image {
                            display: none;
                        }
                    }
                }
            }
        }
    }    
}

@media screen and (max-width: 420px) {
    .cb-quotations {
        .text {
            font-size: 18px;
        }
    }
}
 
