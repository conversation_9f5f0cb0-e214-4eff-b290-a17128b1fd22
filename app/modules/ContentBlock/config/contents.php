<?php
/**
 * Module contents initialization. Each content is defined by pair '{pid}' => {arrayOfWebContentFields}. 
 * There are two special fields: 'parent' and 'blocks'. See here below how to use them.
 * An example:
 * 
 *      array(
 *          '{pid}' => array( // pid is used to identify existing contents in DB and so avoid creation on duplicities
 *              'parent' => '{parentPid}', // use 'root' for toplevel folders,  required
                'lang' => '{lang1},{lang2},...', // comma separated list of languages to create the contents for
 *              'name' => '{contentName}', // if not defined then defaults to {pid}
 *              'locator' => '{contentSlug}', // if not defined then only content category is created without real content
 *              'text' => '{contentText}', // this is created as html content block
 *              'active' => false, // if not defined then defaults to TRUE
 *              'permanent' => true, // if not defined then defaults to FALSE
 *              'blocks' => array(
 *                  array(
 *                      'content_block_model' => 'ContentBlock.ContentBlockHtml', // required
 *                      'name' => '{contentBlockName}', // if not provided then default content block name is used
 *                      'active' => true, // if not defined then defaults to TRUE
 *                      'content_block_data' => array(
 *                          'html' => '<h1>My sample content</h1> This is just a sample content',
 *                      ),
 *                  ),
 *              ),
 *          ),
 *          '{pid2}' => array(...),
 *          ...,
 *      )
 * 
 * Use '/mvc/App/Tools/updateContents' to load contents into DB
 * 
 * Contents are created for all active langs from specified (in 'lang'). 
 * If no langs are specified then content is created for all active languages.
 * Missing lang roots are created too.
 */ 
$contents = array(
//    // ContentBlock top level container
//    'ContentBlock' => array(
//        'parent' => 'system',
//        'name' => 'ContentBlock', 
//        'locator' => 'ContentBlock', 
//        'permanent' => 1,
//    ),
//    // ContentBlock example page
//    'ContentBlock.MyController.myAction' => array(
//        'parent' => 'ContentBlock',
//        'name' => 'My name', 
//        'locator' => 'my-slug', 
//        'text' => '<object _snippet="ContentBlock.MyController.myAction" _snippet_generic="1" _snippet_name="My name"></object>',
//        'permanent' => 1,
//    ),
);
