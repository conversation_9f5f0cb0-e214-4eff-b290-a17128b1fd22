<?php
/**
 * Pozadie - koniec
 */
App::loadModel('App', 'ContentBlock');
class ContentBlockBackgroundEnd extends ContentBlock {
    
    /**
     * Content block admin (backend) view
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockBackgroundEnd/admin_view';
    
    /**
     * Content block frontend view 
     * 
     * @var string 
     */
    protected $view = 'ContentBlockBackgroundEnd/view';
    
    protected $previewable = false;
    
    protected $fields = array(        
        'block_start_data' => '',
    );    
    
    /**
     * Name of paired content blocks start model. 
     * Should be defined in end model.
     * 
     * NOTE: According convention the start model name must end by "Start" and
     * end model name must end by "End"
     * 
     * @var string 
     */
    protected $startModel = 'ContentBlockBackgroundStart';
    
    public function __construct() {
        
        $this->blockName = __a(__FILE__, 'Obaľovací blok - koniec');
        $this->description = __a(__FILE__, 'Párový koncový blok ku bloku "Obaľovací blok - zač<PERSON>tok"');
        $this->pairName = __a(__FILE__, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> blok (pozadie, centrovanie)');
                                    
        parent::__construct();
    }
}
