<?php
/**
 * Stĺpec - koniec
 */
App::loadModel('App', 'ContentBlock');
class ContentBlockTextMenu extends ContentBlock {
    
    /**
     * Content block admin (backend) view
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockTextMenu/admin_view';
    
    /**
     * Content block frontend view 
     * 
     * @var string 
     */
    protected $view = 'ContentBlockTextMenu/view';
    
    protected $previewable = false;
        
    public function __construct() {
        
        $this->blockName = __a(__FILE__, 'Menu nadpisov');
        $this->description = __a(__FILE__, 'Menu pozostávajúce z nadpisov blokov v stránke. Zahrnuté sú len nadpisy so zaškrtnutým "Zobraziť v menu nadpisov".');
        //$this->imageUrlPath = '/app/modules/ContentBlock/img/cb-text-menu.jpg';
                                    
        parent::__construct();
    }
}
