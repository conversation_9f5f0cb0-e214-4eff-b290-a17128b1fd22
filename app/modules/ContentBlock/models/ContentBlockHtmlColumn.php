<?php
App::loadModel('App', 'ContentBlock');
class ContentBlockHtmlColumn extends ContentBlock {
    
    /**
     * Content block admin (backend) view
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockHtmlColumn/admin_view';
    
    /**
     * Content block frontend view 
     * 
     * @var string 
     */
    protected $view = 'ContentBlockHtmlColumn/view';
    
    protected $previewable = false;
        
    /**
     * Content block view custom data fields provided like pairs {fieldName} => {defaultValue}
     * They may not contain fields defined in ContentBlockInstance::$schema!
     * Only fields specified here are saved for instance!
     * 
     * @var array 
     */
    protected $fields = array(
        'html' => '',
        'responsive_width' => '50%; 768px:100%',
        'vertical_align' => '',
        'responsive_padding_top' => '',
        'responsive_padding_right' => '',
        'responsive_padding_bottom' => '',
        'responsive_padding_left' => '',
        'put_on_end' => '',
    );
        
    public function __construct() {
        
        $this->blockName = __a(__FILE__, 'HTML stĺpec');
        $this->description = __a(__FILE__, 'HTML editor na zadanie ľubovolného HTML obsahu alebo snipetov do stĺpca. Na vytvorenie viacerých stĺpcov v jednom riadku je ideálne vložiť bloky HTML stĺpcov do obaľovacieho bloku. Toto tiež umožňuje umiestniť stĺpce v centrálnom bloku stránky. Ak majú byť v stĺpci aj iné obsahové bloky, tak je potrebné použiť obsahový blok "Stĺpec".  Na odriadkovanie stĺpcov pod seba sa používa blok "Nový riadok".');
        
        $this->validations = array(
            'responsive_width' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                array(
                    'rule' => array('responsiveValue', array(
                        'allowValues' => false,
                        'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
                    )),
                ),
            ),
            'vertical_align' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Vyberte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Vyberte'),
                ),
            ),
            'responsive_padding_top' => array(
                array(
                    'rule' => array('responsiveValue', array(
                        'allowValues' => false,
                        'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
                    )),
                ),
            ),
            'responsive_padding_right' => array(
                array(
                    'rule' => array('responsiveValue', array(
                        'allowValues' => false,
                        'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
                    )),
                ),
            ),
            'responsive_padding_bottom' => array(
                array(
                    'rule' => array('responsiveValue', array(
                        'allowValues' => false,
                        'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
                    )),
                ),
            ),
            'responsive_padding_left' => array(
                array(
                    'rule' => array('responsiveValue', array(
                        'allowValues' => false,
                        'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
                    )),
                ),
            ),
        );
        
        parent::__construct();
    }    
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        
        if (!empty($data['html'])) {
            $data['html'] = Sanitize::contentHtml($data['html']);
        }
        
        return parent::normalize($data, $options);
    }
    
    public function prepareAdminViewData($data, $dataAreSubmitted) {
        if (
            !$dataAreSubmitted 
            && !empty($data['block_bg_image'])
        ) {
            $data['block_bg_image'] = $this->getFileFieldUrlPath('block_bg_image', array(
                'file' => $data['block_bg_image'],
                'variant' => 'preview',
            ));
        }
        return $data;
    }
    
    public function prepareViewData($data, $options = array()) {
        $defaults = array(
            'ownerModel' => null,
            'ownerId' => null,
            'ownerRecord' => null,
        );
        $options = array_merge($defaults, $options);
        $data['animated'] = preg_match('/<[^>]+ class="[^"]*wow[^"]*"[^>]*>/i', $data['html']);
        if (!empty($data['html'])) {
            $snippetsOptions = array();
            if ($options['ownerModel'] === 'App.WebContent') {
                if (!empty($options['ownerRecord'])) {
                    $snippetsOptions['params'] = array('_content' => $options['ownerRecord']);
                }
                elseif (!empty($options['ownerId'])) {
                    $snippetsOptions['params'] = array('_content' => array('id' => $options['ownerId']));
                }
            }
            $data['html'] = App::loadTextSnippets($data['html'], $snippetsOptions);
        }
        if (!empty($data['block_bg_image'])) {
            $data['block_bg_image'] = $this->getFileFieldUrlPath('block_bg_image', array(
                'file' => $data['block_bg_image'],
            ));
        }
        return $data;
    }
}
