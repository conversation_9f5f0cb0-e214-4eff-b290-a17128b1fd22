<?php
App::loadModel('App', 'ContentBlock');

class ContentBlockGoogleMap extends ContentBlock {

    protected $adminView = 'ContentBlockGoogleMap/admin_view';

    protected $view = 'ContentBlockGoogleMap/view';

    protected $fields = array(
        'latitude' => '',
        'longitude' => '',
        'label' => '',
        'zoom_level' => '12',
        
        // block params fields
        'block_top_padding' => '',
        'block_bottom_padding' => '',
        'block_center_content' => 'wide',
        //'block_bg_gradient' => '',
        'block_bg_image' => '',
        'block_bg_color' => '',
        'block_bg_horizontal_position' => '',
        'block_bg_vertical_position' => '',
        'block_bg_size' => 'cover',
        'block_bg_repeat' => '',
        'block_bg_parallax' => '',
    );
    
    public function __construct() {
        $this->blockName = __a(__FILE__, 'Mapa');
        $this->description = __a(__FILE__, 'Google mapa s vyznačeným miestom.');
        $this->imageUrlPath = '/app/modules/ContentBlock/img/cb-google-map.png';

        $this->validations = array(
            'latitude' => array(
                array(
                    'rule' => 'number',
                    'message' => __v(__FILE__, 'Vložte prosím číslo'),
                ),
            ),
            'longitude' => array(
                array(
                    'rule' => 'number',
                    'message' => __v(__FILE__, 'Vložte prosím číslo'),
                ),
            ),
        );

        parent::__construct();
    }

}