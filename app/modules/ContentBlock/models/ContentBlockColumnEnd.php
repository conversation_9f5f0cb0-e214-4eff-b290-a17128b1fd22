<?php
/**
 * Column - end
 * 
 * ATTENTION: To make column blocks to work the origin comments must be turned off.
 * Origin comments are separated by new lines and so they broke rows of inline or 
 * inline-block elements.
 */
App::loadModel('App', 'ContentBlock');
class ContentBlockColumnEnd extends ContentBlock {
    
    /**
     * Content block admin (backend) view
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockColumnEnd/admin_view';
    
    /**
     * Content block frontend view 
     * 
     * @var string 
     */
    protected $view = 'ContentBlockColumnEnd/view';
    
    protected $previewable = false;
    
    protected $fields = array(        
        'block_start_data' => '',
    );    
    
    /**
     * Name of paired content blocks start model. 
     * Should be defined in end model.
     * 
     * NOTE: According convention the start model name must end by "Start" and
     * end model name must end by "End"
     * 
     * @var string 
     */
    protected $startModel = 'ContentBlockColumnStart';
    
    public function __construct() {
        
        $this->blockName = __a(__FILE__, 'Stĺpec - koniec');
        $this->description = __a(__FILE__, '<PERSON>árový koncový blok ku bloku "Stĺpec - začiatok"');
        $this->pairName = __a(__FILE__, 'Stĺpec');
                                    
        parent::__construct();
    }
}
