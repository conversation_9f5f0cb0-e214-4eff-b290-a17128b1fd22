<?php
/**
 * Citáty (výroky)
 */
App::loadModel('App', 'ContentBlock');
class ContentBlockQuotations extends ContentBlock {

    /**
     * Content block admin (backend) view
     *
     * @var string
     */
    protected $adminView = 'ContentBlockQuotations/admin_view';

    /**
     * Content block frontend view
     *
     * @var string
     */
    protected $view = 'ContentBlockQuotations/view';

    /**
     * Maximum amount of advantages
     * @var number
     */
    private $maxItemsCount = 10;

    /**
     * Content block view custom data fields provided like pairs {fieldName} => {defaultValue}
     * They may not contain fields defined in ContentBlockInstance::$schema!
     * Only fields specified here are saved for instance!
     *
     * @var array
     */
    protected $fields = array(
        // block title fields
//        'title' => '',
//        'title_subtitle' => '',
//        'title_subtitle_locator' => '',

        // content fields are generated in constructor

        'block_top_padding' => '',
        'block_bottom_padding' => '',
        'block_center_content' => 'wide',
        //'block_bg_gradient' => '',
        'block_bg_image' => '',
        'block_bg_color' => '',
        'block_bg_horizontal_position' => '',
        'block_bg_vertical_position' => '',
        'block_bg_size' => 'cover',
        'block_bg_repeat' => '',
        'block_bg_parallax' => '',
    );

    /**
     * File fields of content block data.
     * Definition is the same as for Model::$fileFields
     *
     * @var array
     */
    protected $fileFields = array(
         // content file fields are generated in constructor
        'block_bg_image' => array(
            'quality' => 90,
            'variants' => array(
                '' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(230, 230),
                    //'cover' => array(230, 230),
                    //'cropInMiddle' => array(230, 230),
                    'fit' => array(1920, 1920),
                ),
                'preview' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(230, 230),
                    'cover' => array(230, 230),
                ),
            ),
        ),
    );

    /**
     * Default file fields variants
     * for prepareViewData and prepareAdminViewData methods
     * are generated in constructor (content file fields)
     * {fileField} => {variant}, NULL = first specified variant
     * @var array
     */
    private $defaultAdminFileFieldVariants = array(
        'block_bg_image' => 'preview',
    );
    private $defaultFileFieldVariants = array(
        'block_bg_image' => null,
    );

    public function __construct() {

        $this->blockName = __a(__FILE__, 'Citáty / slávne výroky');
        $this->description = __a(__FILE__, 'Maximálne 10 citátov s autorom, ktoré sa generujú náhodne v obsahovom bloku.');
        $this->imageUrlPath = '/app/modules/ContentBlock/img/cb-quotations.png';

        $this->validations = array(
            'block_bg_image' => array(
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                    'alternative' => 'contentBlockFields',
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                    'alternative' => 'contentBlockFields',
                ),
            ),
        );

        // generate custom data, file fields, validation rules and default file fields variants
        for ($i = 1; $i <= $this->maxItemsCount; $i++) {
            $this->fields['item_' . $i . '_quotation'] = '';
            $this->fields['item_' . $i . '_author'] = '';
        }

        parent::__construct();
    }

    public function prepareAdminViewData($data, $dataAreSubmitted) {
        foreach ($this->defaultAdminFileFieldVariants as $fileField => $variant) {
            if (
                !$dataAreSubmitted
                && !empty($data[$fileField])
            ) {
                $data[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                    'file' => $data[$fileField],
                    'variant' => $variant,
                ));
            }
        }
        $data['maxItemsCount'] = $this->maxItemsCount;
        return $data;
    }

    public function prepareViewData($data) {
        $itemsCount = 0;
        $newItemIndex = 1;
        for ($i = 1; $i <= $this->maxItemsCount; $i++) {
            if (!empty($data['item_' . $i . '_quotation'])) {
                $data['item_' . $newItemIndex . '_quotation_ok'] = $data['item_' . $i . '_quotation'];
                $data['item_' . $newItemIndex . '_author_ok'] = $data['item_' . $i . '_author'];
                $itemsCount++;
                $newItemIndex++;
            } 
            unset($data['item_' . $i . '_quotation']);
            unset($data['item_' . $i . '_author']);
        }
        $data['itemsCount'] = $itemsCount;
        return $data;
    }    
    
}
