<?php
App::loadModel('App', 'ContentBlock');
class ContentBlockHtml extends ContentBlock {
    
    /**
     * Content block admin (backend) view
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockHtml/admin_view';
    
    /**
     * Content block frontend view 
     * 
     * @var string 
     */
    protected $view = 'ContentBlockHtml/view';
        
    /**
     * Content block view custom data fields provided like pairs {fieldName} => {defaultValue}
     * They may not contain fields defined in ContentBlockInstance::$schema!
     * Only fields specified here are saved for instance!
     * 
     * @var array 
     */
    protected $fields = array(
        'html' => '',
        
        // block
        'block_top_padding' => '',
        'block_bottom_padding' => '',
        'block_center_content' => 'wide',
        //'block_bg_gradient' => '',
        'block_bg_image' => '',
        'block_bg_color' => '',
        'block_bg_horizontal_position' => '',
        'block_bg_vertical_position' => '',
        'block_bg_size' => 'cover',
        'block_bg_repeat' => '',
        'block_bg_parallax' => '',
    );
    
    /**
     * File fields of content block data. 
     * Definition is the same as for Model::$fileFields
     *
     * @var array 
     */
    protected $fileFields = array(
        'block_bg_image' => array(
            'quality' => 90,
            'variants' => array(
                '' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(230, 230),
                    //'cover' => array(230, 230),
                    //'cropInMiddle' => array(230, 230),
                    'fit' => array(1920, 1920),
                ),
                'preview' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(230, 230),
                    'cover' => array(230, 230),
                ),
            ),
        ),
    );
    
    public function __construct() {
        
        $this->blockName = __a(__FILE__, 'HTML');
        $this->description = __a(__FILE__, 'HTML editor na zadanie ľubovolného HTML obsahu alebo snipetov');
        $this->imageUrlPath = '/app/modules/ContentBlock/img/cb-html.png';
        
        $this->validations = array(
            'block_bg_image' => array(
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
        );
                    
        parent::__construct();
    }    
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        
        if (!empty($data['html'])) {
            $data['html'] = Sanitize::contentHtml($data['html']);
        }
        
        return parent::normalize($data, $options);
    }
    
    public function prepareAdminViewData($data, $dataAreSubmitted) {
        if (
            !$dataAreSubmitted 
            && !empty($data['block_bg_image'])
        ) {
            $data['block_bg_image'] = $this->getFileFieldUrlPath('block_bg_image', array(
                'file' => $data['block_bg_image'],
                'variant' => 'preview',
            ));
        }
        return $data;
    }
    
    public function prepareViewData($data, $options = array()) {
        $defaults = array(
            'ownerModel' => null,
            'ownerId' => null,
            'ownerRecord' => null,
        );
        $options = array_merge($defaults, $options);
        if (!empty($data['html'])) {
            $snippetsOptions = array();
            if ($options['ownerModel'] === 'App.WebContent') {
                if (!empty($options['ownerRecord'])) {
                    $snippetsOptions['params'] = array('_content' => $options['ownerRecord']);
                }
                elseif (!empty($options['ownerId'])) {
                    $snippetsOptions['params'] = array('_content' => array('id' => $options['ownerId']));
                }
            }
            $data['html'] = App::loadTextSnippets($data['html'], $snippetsOptions);
        }
        if (!empty($data['block_bg_image'])) {
            $data['block_bg_image'] = $this->getFileFieldUrlPath('block_bg_image', array(
                'file' => $data['block_bg_image'],
            ));
        }
        return $data;
    }
}
