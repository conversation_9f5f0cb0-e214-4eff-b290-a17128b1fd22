<?php
/**
 * Pozadie - začiatok
 */
App::loadModel('App', 'ContentBlock');
class ContentBlockBackgroundStart extends ContentBlock {
    
    /**
     * Content block admin (backend) view
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockBackgroundStart/admin_view';
    
    /**
     * Content block frontend view 
     * 
     * @var string 
     */
    protected $view = 'ContentBlockBackgroundStart/view';
    
    protected $previewable = false;
        
    /**
     * Content block view custom data fields provided like pairs {fieldName} => {defaultValue}
     * They may not contain fields defined in ContentBlockInstance::$schema!
     * Only fields specified here are saved for instance!
     * 
     * @var array 
     */
    protected $fields = array(        
        'block_top_padding' => '',
        'block_bottom_padding' => '',
        'block_center_content' => 'wide',
        //'block_bg_gradient' => '',
        'block_bg_image' => '',
        'block_bg_color' => '',
        'block_bg_horizontal_position' => '',
        'block_bg_vertical_position' => '',
        'block_bg_size' => 'cover',
        'block_bg_repeat' => '',
        'block_bg_parallax' => '',
    );
    
    /**
     * File fields of content block data. 
     * Definition is the same as for Model::$fileFields
     *
     * @var array 
     */
    protected $fileFields = array(
        'block_bg_image' => array(
            'quality' => 90,
            'variants' => array(
                '' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(390, 272),
                    //'cover' => array(390, 272),
                    //'cropInMiddle' => array(390, 272),
                    'fit' => array(1920, 1920),
                ),
                'preview' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(390, 390),
                    'fit' => array(230, 230),
                ),
            ),
        ),
    );
    
    /**
     * Name of paired content blocks end model. 
     * Should be defined in start model.
     * 
     * NOTE: According convention the start model name must end by "Start" and
     * end model name must end by "End"
     * 
     * @var string 
     */
    protected $endModel = 'ContentBlockBackgroundEnd';
    
    public function __construct() {
        
        $this->blockName = __a(__FILE__, 'Obaľovací blok - začiatok');
        $this->description = __a(__FILE__, 'Dvojica blokov umožňujúca vloženie spoločného pozadia pre viacero obsahových blokov naraz alebo zarovnanie obsiahnutých blokov do centrálneho bloku stránky.');
        $this->pairName = __a(__FILE__, 'Obaľovací blok (pozadie, centrovanie)');
                
        $this->validations = array(
            'block_bg_image' => array(
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
        );
                    
        parent::__construct();
    }
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
                
        return parent::normalize($data, $options);
    }
    
    public function prepareAdminViewData($data, $dataAreSubmitted) {
        // {fileField} => {variant}, NULL = first specified variant
        $fileFields = array(
            'block_bg_image' => 'preview',
        );
        foreach ($fileFields as $fileField => $variant) {
            if (
                !$dataAreSubmitted
                && !empty($data[$fileField])
            ) {
                $data[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                    'file' => $data[$fileField],
                    'variant' => $variant,
                ));
            }
        }
        return $data;
    }    
    
    public function prepareViewData($data) {
        // {fileField} => {variant}, NULL = first specified variant
        $fileFields = array(
            'block_bg_image' => null,
        );
        foreach ($fileFields as $fileField => $variant) {
            if (!empty($data[$fileField])) {
                $data[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                    'file' => $data[$fileField],
                    'variant' => $variant,
                ));
            }
        }
        return $data;
    }    
    
}
