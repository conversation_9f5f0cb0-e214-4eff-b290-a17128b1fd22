<?php
App::loadModel('App', 'ContentBlock');
class ContentBlockSlider extends ContentBlock {
    
    /**
     * Content block admin (backend) view
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockSlider/admin_view';
    
    /**
     * Content block frontend view 
     * 
     * @var string 
     */
    protected $view = 'ContentBlockSlider/view';
    
    
    protected $previewable = false;
        
    /**
     * Content block view custom data fields provided like pairs {fieldName} => {defaultValue}
     * They may not contain fields defined in ContentBlockInstance::$schema!
     * Only fields specified here are saved for instance!
     * 
     * @var array 
     */
    protected $fields = array(
        'slider_id' => '',
        'slider_transition_time' => '', // default value is defined in constructor
        'slider_slide_time' => '', // default value is defined in constructor
        'slider_auto_play' => '', // default value is defined in constructor
        'slider_stop_on_hover' => '', // default value is defined in constructor
        
        'has_small_gap' => true,
        
        // block
        'block_top_padding' => '',
        'block_bottom_padding' => '',
        'block_center_content' => 'wide',
        //'block_bg_gradient' => '',
        'block_bg_image' => '',
        'block_bg_color' => '',
        'block_bg_horizontal_position' => '',
        'block_bg_vertical_position' => '',
        'block_bg_size' => 'cover',
        'block_bg_repeat' => '',
        'block_bg_parallax' => '',
    );
    
    /**
     * File fields of content block data. 
     * Definition is the same as for Model::$fileFields
     *
     * @var array 
     */
    protected $fileFields = array(
//        'block_bg_image' => array(
//            'quality' => 90,
//            'variants' => array(
//                '' => array(
//                    //'pngGifToJpg' => array(array(255, 255, 255)),
//                    //'fit' => array(230, 230),
//                    //'cover' => array(230, 230),
//                    //'cropInMiddle' => array(230, 230),
//                    'fit' => array(1920, 1920),
//                ),
//                'preview' => array(
//                    //'pngGifToJpg' => array(array(255, 255, 255)),
//                    //'fit' => array(230, 230),
//                    'cover' => array(230, 230),
//                ),
//            ),
//        ),
    );
    
    public function __construct() {
        
        // If TRUE then the content block is available in dropdown menu of FormHelper::contentBlocks() input.
        // If you don't want to make it available for use in project then set it to FALSE.
        // ATTENTION: this does not influence activity of existing block instances.
        $this->available = true; // DEVELOPER === 'durik';
        
        $this->blockName = __a(__FILE__, 'Slajder');
        $this->description = __a(__FILE__, 'Slajder definovaný v "Obsah webu" > "Slajdre"');
        $this->imageUrlPath = '/app/modules/ContentBlock/img/cb-slider.png';
        
        // custom data default values
        $this->fields['slider_transition_time'] = (float)App::getSetting('App', 'slider.transitionTime');
        $this->fields['slider_slide_time'] = (float)App::getSetting('App', 'slider.slideTime');
        $this->fields['slider_auto_play'] = (bool)App::getSetting('App', 'slider.autoPlay');
        $this->fields['slider_stop_on_hover'] = (bool)App::getSetting('App', 'slider.stopOnHover');
        
        $this->validations = array(
            'slider_id' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Vyberte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Vyberte'),
                ),
            ),
            'block_bg_image' => array(
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
        );
                    
        parent::__construct();
    }    
    
    public function prepareAdminViewData($data, $dataAreSubmitted) {
        if (
            !$dataAreSubmitted 
            && !empty($data['block_bg_image'])
        ) {
            $data['block_bg_image'] = $this->getFileFieldUrlPath('block_bg_image', array(
                'file' => $data['block_bg_image'],
                'variant' => 'preview',
            ));
        }
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        App::loadModel('App', 'WebContent');
        $Content = new WebContent();
        $data['sliders'] = $Content->getSlidersSelectList($lang);
        return $data;
    }
    
    public function prepareViewData($data) {
        if (!empty($data['block_bg_image'])) {
            $data['block_bg_image'] = $this->getFileFieldUrlPath('block_bg_image', array(
                'file' => $data['block_bg_image'],
            ));
        }
        if (empty($data['slider_transition_time'])) {
            $data['slider_transition_time'] = App::getSetting('App', 'slider.transitionTime');
        }
        if (empty($data['slider_slide_time'])) {
            $data['slider_slide_time'] = App::getSetting('App', 'slider.slideTime');
        }
        return $data;
    }
    
}
