<?php
App::loadModel('App', 'ContentBlock');
class ContentBlockLine<PERSON>reak extends ContentBlock {

    /**
     * !!! MUST BE SPECIFIED IN CHILD CLASSES !!!
     * 
     * Content block admin (backend) view path relative to {Module}/views.
     * You can use any "css & js methods" of App class in view definition.
     * !!! Only fields defined in $fields property are saved !!!
     * There are following items in $this->params: 
     * - 'data' containing instance data
     * - 'Form' containing instance of FormHelper class initialized by options 'data', 
     *      'required' and 'errors'. Use only this FormHelper instance to create inputs.
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockLineBreak/admin_view';

    /**
     * !!! MUST BE SPECIFIED IN CHILD CLASSES !!!
     * 
     * Content block frontend view path relative to {Module}/views.
     * You can use any "css & js methods" of App class in view definition.
     * If some text field(s) possibly contain text snippets it is up to you to load 
     * them, the best in prepareViewData() method. The $this->params 
     * of view are set to instance data.
     * 
     * @var string 
     */
    protected $view = 'ContentBlockLineBreak/view';
    
    protected $previewable = false;

    /**
     * Validations of content block admin (backend) custom data. 
     * Definition is the same as for Model::$validations
     * 
     * @var array 
     */
    protected $validations = array();
    
    public function __construct() {
        $this->blockName = __a(__FILE__, 'Nový riadok');
        $this->description = __a(__FILE__, 'Vloží nový riadok medzi obsahové bloky nachádzajúce sa vedľa seba. Vhodné na použitie s obsahovými blokmi "Stĺpec" a "HTML stĺpec".');
        
        // call parent constructor at the end
        parent::__construct();
    }
}
