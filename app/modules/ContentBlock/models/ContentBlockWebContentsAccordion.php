<?php
App::loadModel('App', 'ContentBlock');
class ContentBlockWebContentsAccordion extends ContentBlock {

    /**
     * !!! MUST BE SPECIFIED IN CHILD CLASSES !!!
     * 
     * Content block admin (backend) view path relative to {Module}/views.
     * You can use any "css & js methods" of App class in view definition.
     * !!! Only fields defined in $fields property are saved !!!
     * There are following items in $this->params: 
     * - 'data' containing instance data
     * - 'Form' containing instance of FormHelper class initialized by options 'data', 
     *      'required' and 'errors'. Use only this FormHelper instance to create inputs.
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockWebContentsAccordion/admin_view';

    /**
     * !!! MUST BE SPECIFIED IN CHILD CLASSES !!!
     * 
     * Content block frontend view path relative to {Module}/views.
     * You can use any "css & js methods" of App class in view definition.
     * If some text field(s) possibly contain text snippets it is up to you to load 
     * them, the best in prepareViewData() method. The $this->params 
     * of view are set to instance data.
     * 
     * @var string 
     */
    protected $view = 'ContentBlockWebContentsAccordion/view';

    /**
     * Content block instance custom data fields provided like pairs {fieldName} => {defaultValue}
     * !!! They may not contain fields defined in $schema property !!!
     * !!! Only custom data fields specified here are saved for instance !!!
     * 
     * @var array 
     */
    protected $fields = array(        
        'run_web_contents_id' => '',
        'open_first' => '1',
        
        'block_top_padding' => '',
        'block_bottom_padding' => '',
        'block_center_content' => 'wide',
        //'block_bg_gradient' => '',
        'block_bg_image' => '',
        'block_bg_color' => '',
        'block_bg_horizontal_position' => '',
        'block_bg_vertical_position' => '',
        'block_bg_size' => 'cover',
        'block_bg_repeat' => '',
        'block_bg_parallax' => '',
    );
    
    /**
     * File fields of content block data. 
     * Definition is the same as for Model::$fileFields
     *
     * @var array 
     */
    protected $fileFields = array(
        'block_bg_image' => array(
            'quality' => 90,
            'variants' => array(
                '' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(390, 272),
                    //'cover' => array(390, 272),
                    //'cropInMiddle' => array(390, 272),
                    'fit' => array(1920, 1920),
                ),
                'preview' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(390, 390),
                    'fit' => array(230, 230),
                ),
            ),
        ),
    );
        
    public function __construct() {
////rblsb - the final look is much closer to "tabs" than to "accordion". So make it more comprehensible for users
//        $this->blockName = __a(__FILE__, 'Akordeón obsahov');
//        $this->description = __a(__FILE__, 'Blok na vloženie akordeónu obsahov podstránok vybranej stránky v "Obsah webu" > "Sekcie a stránky"');
        $this->blockName = __a(__FILE__, 'Taby (záložky/karty) obsahov');
        $this->description = __a(__FILE__, 'Blok na vloženie tabov obsahov podstránok vybranej stránky v "Obsah webu" > "Sekcie a stránky"');
        
        $this->validations = array(
            'run_web_contents_id' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Vyberte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Vyberte'),
                ),
            ),
            'block_bg_image' => array(
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
        );
        
        // call parent constructor at the end
        parent::__construct();
    }
    
    /**
     * Normalizes content block admin (backend) view data.
     *
     * @param array $data Data to be normalized.
     * @param array $options Options of Model::normalizeEmptyValues() plus following:
     *      - 'on' (string) Is the normalization done on 'create' or 'update'. 
     *      Defaults to NULL (not applied).
     *      - 'alternative' (string) Normalization alternative. Defaults to NULL (not applied).
     *          only in case that normalize() is called from instance. Defaults to NULL.
     * 
     * ATTENTION: Be carefull to apply Sanitize::value() on $data. Always consider 
     *      that e.g. $name = Sanitize::value($data['name']) will create $data['name'] === NULL
     *      in case that 'name' key is not present in $data array in tne moment of
     *      the check. And like this you can erase 'name' in DB record. To avoid
     *      this trap use either ternary expessions or make copy of $data to an internal variable.
     * 
     * @return array Normalized data
     */
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        
        // do your custom normalization...
        
        return parent::normalize($data, $options);
    }
    
    /**
     * Prepares data for content block admin (backend) view, e.g. file fields url 
     * paths can be set here
     * 
     * NOTE: Instance id (if any) is contained in data: $data['run_content_block_instances_id']
     * 
     * @param array $data Content block raw data
     * @prams bool $dataAreSubmitted Are the incoming data just retrieved from DB 
     *      or are they submitted form data?
     * 
     * @return array Prepared data
     */
    public function prepareAdminViewData($data, $dataAreSubmitted) {
        // {fileField} => {variant}, NULL = first specified variant
        $fileFields = array(
            'block_bg_image' => 'preview',
        );
        foreach ($fileFields as $fileField => $variant) {
            if (
                !$dataAreSubmitted
                && !empty($data[$fileField])
            ) {
                $data[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                    'file' => $data[$fileField],
                    'variant' => $variant,
                ));
            }
        }
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        App::loadModel('App', 'WebContent');
        $Content = new WebContent();
        $data['contents'] = $Content->getPagesSelectList($lang, array(
            'excludeSpecificSections' => true,
            'firstPlaceholder' => false,
            'markInactive' => ' (' . __a(__FILE__, 'neaktívna') . ')',
        ));
        return $data;
    }    
    
    /**
     * Prepares data for content block frontend view, e.g. file fields url paths 
     * can be set here or text snippets loaded
     * 
     * NOTE: Instance id is contained in data: $data['run_content_block_instances_id']
     * 
     * @param array $data Content block raw data
     * 
     * @return array Prepared data
     */
    public function prepareViewData($data) {
        // {fileField} => {variant}, NULL = first specified variant
        $fileFields = array(
            'block_bg_image' => null,
        );
        foreach ($fileFields as $fileField => $variant) {
            if (!empty($data[$fileField])) {
                $data[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                    'file' => $data[$fileField],
                    'variant' => $variant,
                ));
            }
        }
        return $data;
    }    
    
}
