<?php
/**
 * Page XYZ > Section XYZ
 */
App::loadModel('App', 'ContentBlock');
class ContentBlockExample001 extends ContentBlock {
        
    /**
     * Content block admin (backend) view
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockExample001/admin_view';
    
    /**
     * Content block frontend view 
     * 
     * @var string 
     */
    protected $view = 'ContentBlockExample001/view';
        
    /**
     * Content block view custom data fields provided like pairs {fieldName} => {defaultValue}
     * They may not contain fields defined in ContentBlockInstance::$schema!
     * Only fields specified here are saved for instance!
     * 
     * @var array 
     */
    protected $fields = array(
        // block title fields
        'title' => '',
        'title_tag' => 'h1',
        'title_color' => '',
        'title_include_in_text_menu' => '',
        'title_label_in_text_menu' => '',
        'title_subtitle' => '',
        'title_subtitle_locator' => '',
        
        // block custom fields (add your fields here)
        'image_1' => '',
        'text_1' => '',
        'image_2' => '',
        'text_2' => '',
        'image_3' => '',
        'text_3' => '',
        'text_size' => '',
        'text_weight' => '',
        'text_top_margin' => '',
        
        // block params fields
        'block_top_padding' => '',
        'block_bottom_padding' => '',
        'block_center_content' => 'wide',
        //'block_bg_gradient' => '',
        'block_bg_image' => '',
        'block_bg_color' => '',
        'block_bg_horizontal_position' => '',
        'block_bg_vertical_position' => '',
        'block_bg_size' => 'cover',
        'block_bg_repeat' => '',
        'block_bg_parallax' => '',
    );
    
    /**
     * File fields of content block data. 
     * Definition is the same as for Model::$fileFields
     *
     * @var array 
     */
    protected $fileFields = array(
        'image_1' => array(
            'quality' => 90,
            'variants' => array(
                '' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    // !!! These transformation are adjusted (some are turn off) 
                    // by normalize() according image type (is icon / is not icon)
                    'fit' => array(230, 230),
                    'cover' => array(230, 230),
                    'cropInMiddle' => array(230, 230),
                ),
            ),
        ),
        'image_2' => array(
            'quality' => 90,
            'variants' => array(
                '' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    // !!! These transformation are adjusted (some are turn off) 
                    // by normalize() according image type (is icon / is not icon)
                    'fit' => array(230, 230),
                    'cover' => array(230, 230),
                    'cropInMiddle' => array(230, 230),
                ),
            ),
        ),
        'image_3' => array(
            'quality' => 90,
            'variants' => array(
                '' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    // !!! These transformation are adjusted (some are turn off) 
                    // by normalize() according image type (is icon / is not icon)
                    'fit' => array(230, 230),
                    'cover' => array(230, 230),
                    'cropInMiddle' => array(230, 230),
                ),
            ),
        ),
        'block_bg_image' => array(
            'quality' => 90,
            'variants' => array(
                '' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(230, 230),
                    //'cover' => array(230, 230),
                    //'cropInMiddle' => array(230, 230),
                    'fit' => array(1920, 1920),
                ),
                'preview' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(230, 230),
                    'cover' => array(230, 230),
                ),
            ),
        ),
    );
    
    public function __construct() {
        
        // If TRUE then the content block is available in dropdown menu of FormHelper::contentBlocks() input.
        // If you don't want to make it available for use in project then set it to FALSE.
        // ATTENTION: this does not influence activity of existing block instances.
        $this->available = false; // DEVELOPER === 'durik';
        
        $this->blockName = __a(__FILE__, 'Obrázky s textami (vzor)');
        $this->description = __a(__FILE__, 'Obsahový blok obsahujúci obrázky s popisnými textami pod mini. Ide o vzorovú implementáciu obsahového bloku.');
        $this->imageUrlPath = '/app/modules/ContentBlock/img/cb-example-001.png';
        
        $this->validations = array(
            'image_1' => array(
////if possible the the best strategy is "only things provided on backend are displayed on frontend"
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Vyberte'),
//                    'on' => 'create',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Vyberte'),
//                ),
//                array(
//                    'rule' => array('uploadData', array('notEmpty' => true)),
//                    'message' => __v(__FILE__, 'Vyberte'), 
//                    'on' => 'create',
//                ),
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
            'image_2' => array(
////if possible the the best strategy is "only things provided on backend are displayed on frontend"
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Vyberte'),
//                    'on' => 'create',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Vyberte'),
//                ),
//                array(
//                    'rule' => array('uploadData', array('notEmpty' => true)),
//                    'message' => __v(__FILE__, 'Vyberte'),  
//                    'on' => 'create',
//                ),
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
            'image_3' => array(
////if possible the the best strategy is "only things provided on backend are displayed on frontend"
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Vyberte'),
//                    'on' => 'create',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Vyberte'),
//                ),
//                array(
//                    'rule' => array('uploadData', array('notEmpty' => true)),
//                    'message' => __v(__FILE__, 'Vyberte'),   
//                    'on' => 'create',
//                ),
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
            'block_bg_image' => array(
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
        );
                    
        parent::__construct();
    }
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        
        return parent::normalize($data, $options);
    }
    
    public function prepareAdminViewData($data, $dataAreSubmitted) {
        // {fileField} => {variant}, NULL = first specified variant
        $fileFields = array(
            'image_1' => null, 
            'image_2' => null, 
            'image_3' => null,
            'block_bg_image' => 'preview',
        );
        foreach ($fileFields as $fileField => $variant) {
            if (
                !$dataAreSubmitted
                && !empty($data[$fileField])
            ) {
                $data[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                    'file' => $data[$fileField],
                    'variant' => $variant,
                ));
            }
        }
        return $data;
    }    
    
    public function prepareViewData($data, $options = array()) {
        $defaults = array(
            'ownerModel' => null,
            'ownerId' => null,
            'ownerRecord' => null,
        );
        $options = array_merge($defaults, $options);
        // set a file fields URL paths only in case of instance data
        // if explicit data are provided then let filefields as they are
        if (!empty($options['ownerId'])) {
            // {fileField} => {variant}, NULL = first specified (default) variant
            $fileFields = array(
                'image_1' => null, 
                'image_2' => null, 
                'image_3' => null,
                'block_bg_image' => null,
            );
            foreach ($fileFields as $fileField => $variant) {
                if (!empty($data[$fileField])) {
                    $data[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                        'file' => $data[$fileField],
                        'variant' => $variant,
                    ));
                }
            }
        }
        return $data;
    }    
    
}
