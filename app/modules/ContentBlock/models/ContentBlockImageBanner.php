<?php
App::loadModel('App', 'ContentBlock');
class ContentBlockImageBanner extends ContentBlock {
    
    /**
     * Content block admin (backend) view
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockImageBanner/admin_view';
    
    /**
     * Content block frontend view 
     * 
     * @var string 
     */
    protected $view = 'ContentBlockImageBanner/view';
        
    /**
     * Content block view custom data fields provided like pairs {fieldName} => {defaultValue}
     * They may not contain fields defined in ContentBlockInstance::$schema!
     * Only fields specified here are saved for instance!
     * 
     * @var array 
     */
    protected $fields = array(
//        // block title fields
//        'title' => '',
//        'title_tag' => 'h1',
//        'title_color' => '',
//        'title_include_in_text_menu' => '',
//        'title_label_in_text_menu' => '',
//        'title_subtitle' => '',
//        'title_subtitle_locator' => '',
        
        // block custom fields (add your fields here)
        'has_small_gap' => true,
        
        'image' => '',
        'url' => '',
        'title' => '',
        'text' => '',
        'button_label' => '',
        'veiled_image' => false,
        
        // block params fields
        'block_top_padding' => '',
        'block_bottom_padding' => '',
        'block_center_content' => 'wide',
        //'block_bg_gradient' => '',
        'block_bg_image' => '',
        'block_bg_color' => '',
        'block_bg_horizontal_position' => '',
        'block_bg_vertical_position' => '',
        'block_bg_size' => 'cover',
        'block_bg_repeat' => '',
        'block_bg_parallax' => '',        
    );
    
    /**
     * File fields of content block data. 
     * Definition is the same as for Model::$fileFields
     *
     * @var array 
     */
    protected $fileFields = array(
        'image' => array(
            'quality' => 90,
            'variants' => array(
                '' => array(
                    'fitY' => array(1190),
                ),
            ),
        ),
        
        'block_bg_image' => array(
            'quality' => 90,
            'variants' => array(
                '' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(230, 230),
                    //'cover' => array(230, 230),
                    //'cropInMiddle' => array(230, 230),
                    'fit' => array(1920, 1920),
                ),
                'preview' => array(
                    //'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(230, 230),
                    'cover' => array(230, 230),
                ),
            ),
        ),
    );
    
    public function __construct() {
        
        // If TRUE then the content block is available in dropdown menu of FormHelper::contentBlocks() input.
        // If you don't want to make it available for use in project then set it to FALSE.
        // ATTENTION: this does not influence activity of existing block instances.
        $this->available = true; // DEVELOPER === 'durik';
        
        $this->blockName = __(__FILE__, 'Obrázkový banner');
        $this->description = __(__FILE__, 'Banner pozostávajúci z obrázka a textu zobrazeného nad nim');
        $this->imageUrlPath = '/app/modules/ContentBlock/img/cb-image-banner.png';
        
        //if possible the the best strategy is "only things provided on backend are displayed on frontend"
        $this->validations = array(
            'image' => array(
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),

            'block_bg_image' => array(
                array(
                    'rule' => array('uploadData', array('noErrors' => true)),
                    'message' => __v(__FILE__, 'Chyba nahrávania obrázku'),
                ),
                array(
                    'rule' => array('uploadData', array('type' => '/^image\//')),
                    'message' => __v(__FILE__, 'Vyberte prosím obrázok'),
                ),
            ),
        );        

        parent::__construct();
    }
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
                
        return parent::normalize($data, $options);
    }
    
    public function prepareAdminViewData($data, $dataAreSubmitted) {
        // {fileField} => {variant}, NULL = first specified variant
        $fileFields = array(
            'image' => null, 
            'block_bg_image' => 'preview',
        );
        foreach ($fileFields as $fileField => $variant) {
            if (
                !$dataAreSubmitted
                && !empty($data[$fileField])
            ) {
                $data[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                    'file' => $data[$fileField],
                    'variant' => $variant,
                ));
            }
        }
        return $data;
    }    
    
    public function prepareViewData($data, $options = array()) {
        $defaults = array(
            'ownerModel' => null,
            'ownerId' => null,
            'ownerRecord' => null,
        );
        $options = array_merge($defaults, $options);
        // set a file fields URL paths only in case of instance data
        // if explicit data are provided then let filefields as they are
        if (!empty($options['ownerId'])) {
            // {fileField} => {variant}, NULL = first specified (default) variant
            $fileFields = array(
                'image' => null, 
                'block_bg_image' => null,
            );
            foreach ($fileFields as $fileField => $variant) {
                if (!empty($data[$fileField])) {
                    $data[$fileField] = $this->getFileFieldUrlPath($fileField, array(
                        'file' => $data[$fileField],
                        'variant' => $variant,
                    ));
                }
            }
        }
        return $data;
    }    
}
