<?php
/**
 * Column - start
 * 
 * ATTENTION: To make column blocks to work the origin comments must be turned off.
 * Origin comments are separated by new lines and so they broke rows of inline or 
 * inline-block elements.
 */
App::loadModel('App', 'ContentBlock');
class ContentBlockColumnStart extends ContentBlock {
    
    /**
     * Content block admin (backend) view
     * 
     * @var string 
     */
    protected $adminView = 'ContentBlockColumnStart/admin_view';
    
    /**
     * Content block frontend view 
     * 
     * @var string 
     */
    protected $view = 'ContentBlockColumnStart/view';
    
    protected $previewable = false;
        
    /**
     * Content block view custom data fields provided like pairs {fieldName} => {defaultValue}
     * They may not contain fields defined in ContentBlockInstance::$schema!
     * Only fields specified here are saved for instance!
     * 
     * @var array 
     */
    protected $fields = array(        
        'responsive_width' => '50%; 768px:100%',
        'vertical_align' => '',
        'responsive_padding_top' => '',
        'responsive_padding_right' => '',
        'responsive_padding_bottom' => '',
        'responsive_padding_left' => '',
        'put_on_end' => '',
    );
    
    
    /**
     * Name of paired content blocks end model. 
     * Should be defined in start model.
     * 
     * NOTE: According convention the start model name must end by "Start" and
     * end model name must end by "End"
     * 
     * @var string 
     */
    protected $endModel = 'ContentBlockColumnEnd';
    
    public function __construct() {
        
        $this->blockName = __a(__FILE__, 'Stĺpec - začiatok');
        $this->description = __a(__FILE__, 'Dvojica blokov umožňujúca umiestnenie viacerých obsahových blokov do jedného stĺpca. Na vytvorenie viacerých stĺpcov v jednom riadku je ideálne vložiť bloky stĺpcov do obaľovacieho bloku. Toto tiež umožňuje umiestniť stĺpce v centrálnom bloku stránky. Ak stĺpec obsahuje len HTML blok(y), tak je výhodnejšie použiť obsahový blok "Html stĺpec". Na odriadkovanie stĺpcov pod seba sa používa blok "Nový riadok".');
        $this->pairName = __a(__FILE__, 'Stĺpec');
                
        $this->validations = array(
            'responsive_width' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte'),
                ),
                array(
                    'rule' => array('responsiveValue', array(
                        'allowValues' => false,
                        'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
                    )),
                ),
            ),
            'vertical_align' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Vyberte'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Vyberte'),
                ),
            ),
            'responsive_padding_top' => array(
                array(
                    'rule' => array('responsiveValue', array(
                        'allowValues' => false,
                        'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
                    )),
                ),
            ),
            'responsive_padding_right' => array(
                array(
                    'rule' => array('responsiveValue', array(
                        'allowValues' => false,
                        'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
                    )),
                ),
            ),
            'responsive_padding_bottom' => array(
                array(
                    'rule' => array('responsiveValue', array(
                        'allowValues' => false,
                        'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
                    )),
                ),
            ),
            'responsive_padding_left' => array(
                array(
                    'rule' => array('responsiveValue', array(
                        'allowValues' => false,
                        'allowNumericValues' => array('%', 'vw', 'px', 'cm'),
                    )),
                ),
            ),
        );
                    
        parent::__construct();
    }
}
