<?php
/**
 * Module rights definitions
 * 
 * Auxiliary variable $edit serves to optimize performance. Use it in admin rights 
 * to define headers and labels in case when rights are loaded for editing. Only items
 * with defined labels are included in editing. Use pattern like:
 * 
 *      'admins' => array(
 *          array('h1' => $edit ? __a(__FILE__, 'My module rights') : true),
 *          array('h2' => $edit ? __a(__FILE__, 'My section rights') : true),
 *          'MyModel.admin_myAction1' => $edit ? __a(__FILE__, 'Do my action') : true,
 *          'MyModel.admin_myAction3' => true,                                                       // this will not be included in editing
 *          'MyModel.admin_myAction2' => $edit ? __a(__FILE__, 'Not important for admin') : false,      // editable but admin does not need to see it in backend
 *          ...,
 * 
 *      );
 * 
 * Admin rights must contain all actions which should be editable. If you have a case of 
 * method which is a weak version of stronger one, and admn does not need to use this method
 * and see it on backend then just set it to FALSE (see the last item in example above).
 * In special cases you can define FALSE items also in rights for other groups, just test it
 * if default behaviour does not meet your needs.
 * 
 * When loading rights for editing set $edit = TRUE. Otherwise just dont care about it.
 * 
 * !!!ATTENTION: Do not define headers and labels in rights for other than admins group!!!
 */
$edit = !empty($edit);
$rights = array(
    'public' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            'AppBasicActions.contactForm' => true,
            'AppBasicActions.popupButton' => true,
            'Users.login' => true,
            'Users.logout' => true,
            'Users.register' => true,
            'Users.forgotPassword' => true,
            'Users.switchToOriginal' => true,
            'Users.switchToOriginalLink' => true,
            'UserProfiles.bonusPointsSummary' => true,
            'WebContents.miniSearch' => true,
            'WebContents.indexSearch' => true,
            'WebContents.textMenu' => true,
            'WebContents.submenu' => true,
            'WebContents.childsAccordion' => true,
            'WebContents.contentAccordion' => true,
            'WebContents.photogallery' => true,
            'WebContents.breadcrumbs' => true,
            'WebContentsArticles.review' => true,
            'WebContentsArticles.index' => true,
            'WebContentsArticles.view' => true,
            'WebContentsNews.review' => true,
            'WebContentsNews.index' => true,
            'WebContentsNews.view' => true,
            'WebContentsReferences.slider' => true,
            'WebContentsSliders.slider' => true,
            'TimelineEvents.timeline' => true,
            'Comments.add' => true,
            'Countries.find' => true,
            'Countries.findFirst' => true,
            'Countries.findList' => true,
            'References.indexSlider' => true,
            'Partners.indexSlider' => true,
        ),
        // these rights apply when the screen is required by slug request or by snippet
        'screen' => array(
            '_404' => true,
            '_ajax' => true,
            '_sc' => true, // captcha
            '_debug' => true,
            '_error' => true,
            '_fakeBackend' => true,
            '_robots' => true,
            '_pwaManifest' => true,
            '_pwaServiceWorker' => true,
            '_pwaOffline' => true,
            '_sitemap' => true,
            '_imageSitemap' => true,
            '_redirectionLoopEscape' => true,
            '_bl' => true,
            '_getImage' => true,
            'runlogin' => true,
            'runlogout' => true,
        ),
        // these rights apply when the element is required by snippet
        'element' => array(
            'htmlHead' => true,
            'htmlHeadCss' => true,
            'htmlHeadJs' => true,
            'htmlBodyStart' => true,
            'htmlBodyHeader' => true,
            'htmlBodyFooter' => true,
            'htmlBodyEnd' => true,
            
            'breadcrumbs' => true,
            'albums' => true,
            'banner' => true,
            'menuAnnouncement' => true,
            'emptyIndexMessage' => true,
            'fbComments' => true,
            'fbLikeIt' => true,
            'fbObject' => true,
            'flash' => true,
            'googleAnalytics' => true,
            'googleTagManager' => true,
            'google_ecommerce_tracking' => true,
            'googleMap' => true,
            'google_webmasters' => true,
            'highlighted' => true,
            'news_all' => true,
            'photogallery' => true,
            'popup' => true,
            'redirect' => true,
            'set_layout' => true,
            'scrolling_text' => true,
            'social_buttons' => true,
            'youtubeVideo' => true,
            'countdown' => true,
            'tagCloud' => true,
            'pwa' => true,
            'search' => true,
            'setting' => true,
        ),
    ),
    'admins' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            
            array('h1' => $edit ? __a(__FILE__, 'Web content rights') : true),
            'WebContents.admin_add' => $edit ? __a(__FILE__, 'Add page') : true,
            'WebContents.admin_copy' => $edit ? __a(__FILE__, 'Copy page') : true,
            'WebContents.admin_edit' => $edit ? __a(__FILE__, 'Edit page') : true,
            'WebContents.admin_delete' => $edit ? __a(__FILE__, 'Delete page') : true,
            'WebContents.admin_export' => $edit ? __a(__FILE__, 'Export pages') : true,
            'WebContents.admin_index' => $edit ? __a(__FILE__, 'Display pages list') : true,
            'WebContents.admin_showTreeLevel' => $edit ? __a(__FILE__, 'Display tree level') : true,
            'WebContents.admin_showTree' => $edit ? __a(__FILE__, 'Display pages tree') : true,
            'WebContents.admin_move' => $edit ? __a(__FILE__, 'Reorder pages') : true,
            'WebContents.admin_view' => $edit ? __a(__FILE__, 'View page') : true,
            'WebContents.admin_deleteFile' => $edit ? __a(__FILE__, 'Delete page files') : true,
            
            'WebContentsArticles.admin_add' => $edit ? __a(__FILE__, 'Add page') : true,
            'WebContentsArticles.admin_edit' => $edit ? __a(__FILE__, 'Edit page') : true,
            'WebContentsArticles.admin_delete' => $edit ? __a(__FILE__, 'Delete page') : true,
            'WebContentsArticles.admin_export' => $edit ? __a(__FILE__, 'Export pages') : true,
            'WebContentsArticles.admin_index' => $edit ? __a(__FILE__, 'Display pages list') : true,
            'WebContentsArticles.admin_showTreeLevel' => $edit ? __a(__FILE__, 'Display tree level') : true,
            'WebContentsArticles.admin_showTree' => $edit ? __a(__FILE__, 'Display pages tree') : true,
            'WebContentsArticles.admin_move' => $edit ? __a(__FILE__, 'Reorder pages') : true,
            'WebContentsArticles.admin_view' => $edit ? __a(__FILE__, 'View page') : true,
            'WebContentsArticles.admin_deleteFile' => $edit ? __a(__FILE__, 'Delete page files') : true,
                        
            'WebContentsNews.admin_add' => $edit ? __a(__FILE__, 'Add page') : true,
            'WebContentsNews.admin_edit' => $edit ? __a(__FILE__, 'Edit page') : true,
            'WebContentsNews.admin_delete' => $edit ? __a(__FILE__, 'Delete page') : true,
            'WebContentsNews.admin_export' => $edit ? __a(__FILE__, 'Export pages') : true,
            'WebContentsNews.admin_index' => $edit ? __a(__FILE__, 'Display pages list') : true,
            'WebContentsNews.admin_showTreeLevel' => $edit ? __a(__FILE__, 'Display tree level') : true,
            'WebContentsNews.admin_showTree' => $edit ? __a(__FILE__, 'Display pages tree') : true,
            'WebContentsNews.admin_move' => $edit ? __a(__FILE__, 'Reorder pages') : true,
            'WebContentsNews.admin_view' => $edit ? __a(__FILE__, 'View page') : true,
            'WebContentsNews.admin_deleteFile' => $edit ? __a(__FILE__, 'Delete page files') : true,
             
            //@deprecated - Use functionality defined in Reference model and References controller
            'WebContentsReferences.admin_add' => $edit ? __a(__FILE__, 'Add page') : true,
            'WebContentsReferences.admin_edit' => $edit ? __a(__FILE__, 'Edit page') : true,
            'WebContentsReferences.admin_delete' => $edit ? __a(__FILE__, 'Delete page') : true,
            'WebContentsReferences.admin_export' => $edit ? __a(__FILE__, 'Export pages') : true,
            'WebContentsReferences.admin_index' => $edit ? __a(__FILE__, 'Display pages list') : true,
            'WebContentsReferences.admin_showTreeLevel' => $edit ? __a(__FILE__, 'Display tree level') : true,
            'WebContentsReferences.admin_showTree' => $edit ? __a(__FILE__, 'Display pages tree') : true,
            'WebContentsReferences.admin_move' => $edit ? __a(__FILE__, 'Reorder pages') : true,
            'WebContentsReferences.admin_view' => $edit ? __a(__FILE__, 'View page') : true,
            'WebContentsReferences.admin_deleteFile' => $edit ? __a(__FILE__, 'Delete page files') : true,
                        
            'References.admin_add' => $edit ? __a(__FILE__, 'Add reference') : true,
            'References.admin_edit' => $edit ? __a(__FILE__, 'Edit reference') : true,
            'References.admin_delete' => $edit ? __a(__FILE__, 'Delete reference') : true,
            'References.admin_export' => $edit ? __a(__FILE__, 'Export references') : true,
            'References.admin_index' => $edit ? __a(__FILE__, 'Display references list') : true,
            'References.admin_deleteFile' => $edit ? __a(__FILE__, 'Delete reference files') : true,
                        
            'Partners.admin_add' => $edit ? __a(__FILE__, 'Add partner') : true,
            'Partners.admin_edit' => $edit ? __a(__FILE__, 'Edit partner') : true,
            'Partners.admin_delete' => $edit ? __a(__FILE__, 'Delete partner') : true,
            'Partners.admin_export' => $edit ? __a(__FILE__, 'Export partners') : true,
            'Partners.admin_index' => $edit ? __a(__FILE__, 'Display partners list') : true,
            'Partners.admin_deleteFile' => $edit ? __a(__FILE__, 'Delete partner files') : true,
            'Partners.admin_move' => $edit ? __a(__FILE__, 'Delete partner files') : true,
                        
            'WebContentsSliders.admin_add' => $edit ? __a(__FILE__, 'Add page') : true,
            'WebContentsSliders.admin_edit' => $edit ? __a(__FILE__, 'Edit page') : true,
            'WebContentsSliders.admin_delete' => $edit ? __a(__FILE__, 'Delete page') : true,
            'WebContentsSliders.admin_export' => $edit ? __a(__FILE__, 'Export pages') : true,
            'WebContentsSliders.admin_index' => $edit ? __a(__FILE__, 'Display pages list') : true,
            'WebContentsSliders.admin_showTreeLevel' => $edit ? __a(__FILE__, 'Display tree level') : true,
            'WebContentsSliders.admin_showTree' => $edit ? __a(__FILE__, 'Display pages tree') : true,
            'WebContentsSliders.admin_move' => $edit ? __a(__FILE__, 'Reorder pages') : true,
            'WebContentsSliders.admin_view' => $edit ? __a(__FILE__, 'View page') : true,
            'WebContentsSliders.admin_deleteFile' => $edit ? __a(__FILE__, 'Delete page files') : true,
                        
            'WebImages.admin_load' => $edit ? __a(__FILE__, 'Load photogallery') : true,
            'WebImages.admin_add' => $edit ? __a(__FILE__, 'Add photogallery image') : true,
            'WebImages.admin_update' => $edit ? __a(__FILE__, 'Update photogallery') : true,
            'WebImages.admin_move' => $edit ? __a(__FILE__, 'Reorder photogallery images') : true,
            'WebImages.admin_delete' => $edit ? __a(__FILE__, 'Delete photogallery image') : true,
            
            'ContentBlockInstances.admin_loadAdminView' => $edit ? __a(__FILE__, 'Add content block instance') : true,
            'ContentBlockInstances.loadInstanceView' => $edit ? __a(__FILE__, 'Preview content block instance') : true,
            'ContentBlockInstances.getOwnerInstancesView' => $edit ? __a(__FILE__, 'Preview page content block instances') : true,
            'ContentBlockInstances.admin_deleteInstanceFile' => $edit ? __a(__FILE__, 'Delete content block instance files') : true,
            
            array('h1' => $edit ? __a(__FILE__, 'System rights') : true),
            
            array('h2' => __a(__FILE__, 'Timeline events rights')),
            'TimelineEvents.admin_index' => $edit ? __a(__FILE__, 'Display events list') : true,
            'TimelineEvents.admin_add' => $edit ? __a(__FILE__, 'Add event') : true,
            'TimelineEvents.admin_edit' => $edit ? __a(__FILE__, 'Edit event') : true,
            'TimelineEvents.admin_delete' => $edit ? __a(__FILE__, 'Delete event') : true,
            
            array('h2' => __a(__FILE__, 'Languages rights')),
            'Languages.admin_index' => $edit ? __a(__FILE__, 'Display languages list') : true,
            'Languages.admin_add' => $edit ? __a(__FILE__, 'Add language') : true,
            'Languages.admin_edit' => $edit ? __a(__FILE__, 'Edit language') : true,
            'Languages.admin_delete' => $edit ? __a(__FILE__, 'Delete language') : true,
            'Languages.admin_move' => $edit ? __a(__FILE__, 'Reorder languages') : true,
            'Languages.admin_deleteFile' => $edit ? __a(__FILE__, 'Delete language image') : true,
            
            array('h2' => __a(__FILE__, 'Countries rights')),
            'Countries.admin_index' => $edit ? __a(__FILE__, 'Display countries list') : true,
            'Countries.admin_add' => $edit ? __a(__FILE__, 'Add country') : true,
            'Countries.admin_edit' => $edit ? __a(__FILE__, 'Edit country') : true,
            'Countries.admin_deleteFile' => $edit ? __a(__FILE__, 'Delete country image') : true,
            'Countries.admin_getSelectorItems' => true,

            'Groups.admin_index' => true,
            
            'Regions.admin_index' => true,
            
            array('h2' => __a(__FILE__, 'Settings rights')),
            'Settings.admin_edit' => $edit ? __a(__FILE__, 'Edit settings') : true,
            'Settings.admin_deleteFile' => true,

            array('h2' => __a(__FILE__, 'URL redirection rights')),
            'UrlRedirections.admin_index' => $edit ? __a(__FILE__, 'Zobraziť zoznam URL presmerovaní') : true,
            'UrlRedirections.admin_add' => $edit ? __a(__FILE__, 'Pridať URL presmerovanie') : true,
            'UrlRedirections.admin_view' => $edit ? __a(__FILE__, 'Zobraziť URL presmerovanie') : true,
            'UrlRedirections.admin_delete' => $edit ? __a(__FILE__, 'Zmazať URL presmerovanie') : true,

            'Tools.menu' => true,
            'Tools.displayInfo' => true,
            'Tools.createTable' => true,
            'Tools.updateTableFields' => true,
            'Tools.updateSettings' => true,
            'Tools.updateContents' => true,
            'Tools.compilePoFiles' => true,
            'Tools.sortTreeByName' => true,
            'Tools.recoverTree' => true,
            'Tools.regenerateImages' => true,
            'Tools.copyWebContentLangBranch' => true,
            'Tools.deleteWebContentLangBranch' => true,
            'Tools.deleteSessionFiles' => true,
            'Tools.sendTestEmail' => true,
            'Tools.swapWebContentPids' => true,
            'Tools.setSessionPath' => true,
            'Tools.setCookie' => true,
            'Tools.cleanUpEshopDatabase' => true,
            'Tools.cleanUpEshopOldProducts' => true,
            'Tools.deleteEshopProductOrphanFiles' => true,
            'Tools.changeEshopOrderNumbers' => true,
            'Tools.normalizeText' => true,
            'Tools.sumTimeEstimates' => true,
            'Tools.checkContentBlocksDefinitions' => true,
            'Tools.refactorContentBlockField' => true,
            'Tools.checkForCleanedUpProduct' => true,
            'Tools.displayCookieStorage' => true,
            'Tools.allowUnpublishedLangsLocally' => true,
            'Tools.testLogEmail' => true,
            'Tools.viewEshopOrdersStatistics' => true,
            
            'Migrations.admin_index' => true,
////commented for security reasons            
//            'Migrations.admin_migrateAppUsers' => true,
//            'Migrations.admin_migrateEshopProductCategories' => true,
//            'Migrations.admin_migrateEshopAuthors' => true,
//            'Migrations.admin_migrateEshopManufacturers' => true,
//            'Migrations.admin_migrateEshopManufacturerRanges' => true,
//            'Migrations.admin_migratePaymentMethods' => true,
//            'Migrations.admin_migrateEshopShipmentMethods' => true,
//            'Migrations.admin_migrateEshopProductGroups' => true,
//            'Migrations.admin_migrateEshopOrders' => true,
//            'Migrations.admin_migrateEshopProductTypes' => true,
//            'Migrations.admin_migrateEshopProductComments' => true,
//            'Migrations.admin_migrateEshopProducts' => true,
//            'Migrations.admin_migrateEshopProductImages' => true,
//            'Migrations.admin_moveEshopProductImages' => true,
//            'Migrations.admin_migrateEshopWishlists' => true,
//            'Migrations.admin_migrateEshopVouchers' => true,
//            'Migrations.admin_migrateMailerContacts' => true,
//            'Migrations.admin_migrateSettings' => true,
            
            'UserProfiles.admin_load' => true,
            'UserProfiles.admin_update' => true,
            'UserProfiles.admin_deleteFile' => true,
            
            array('h2' => __a(__FILE__, 'Users rights')),
            'Users.admin_index' => $edit ? __a(__FILE__, 'Display users list') : true,
            'Users.admin_export' => $edit ? __a(__FILE__, 'Export users') : true,
            'Users.admin_add' => $edit ? __a(__FILE__, 'Add user') : true,
            'Users.admin_update' => $edit ? __a(__FILE__, 'Edit user') : true,
            'Users.admin_updateProfile' => $edit ? __a(__FILE__, 'Edit profile') : true,
            'Users.admin_editRights' => $edit ? __a(__FILE__, 'Edit user rights') : true,
            'Users.admin_delete' => $edit ? __a(__FILE__, 'Delete user') : true,
            'Users.admin_switchToAnother' => $edit ? __a(__FILE__, 'Switch user') : true,
            'Users.admin_getSelectorItems' => true,
            'Users.update' => true,
            
            'UserSettings.admin_load' => true,
            'UserSettings.admin_update' => true,

            'Tests.admin_index' => true,
            'Tests.admin_showTree' => true,
            'Tests.admin_showTreeLevel' => true,
            'Tests.admin_add' => true,
            'Tests.admin_edit' => true,
            'Tests.admin_delete' => true,
            'Tests.admin_deleteFile' => true,
            'Tests.admin_move' => true,
            'Tests.admin_export' => true,
            
        ),
        // these rights apply when the screen is required by slug request or by snippet
        'screen' => array(
            array('h2' => __a(__FILE__, 'Other rights')),
            'run' => $edit ? __a(__FILE__, 'Admin access') : true,
            '_fileManager' => $edit ? __a(__FILE__, 'File manager access') : true,
        ),
        // these rights apply when the element is required by snippet
        'element' => array(            
        ),
        // these rights apply when settings are listed, viewed and edited
        'setting' => array(
        ),        
    ),
    'webmasters' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            'WebContents.admin_add' => true,
            'WebContents.admin_copy' => true,
            'WebContents.admin_edit' => true,
            'WebContents.admin_editText' => true,
            'WebContents.admin_delete' => true,
            'WebContents.admin_export' => true,
            'WebContents.admin_index' => true,
            'WebContents.admin_showTreeLevel' => true,
            'WebContents.admin_showTree' => true,
            'WebContents.admin_move' => true,
            'WebContents.admin_deleteFile' => true,
            'WebContents.admin_view' => true,
            
            'WebContentsArticles.admin_add' => true,
            'WebContentsArticles.admin_edit' => true,
            'WebContentsArticles.admin_delete' => true,
            'WebContentsArticles.admin_export' => true,
            'WebContentsArticles.admin_index' => true,
            'WebContentsArticles.admin_showTreeLevel' => true,
            'WebContentsArticles.admin_showTree' => true,
            'WebContentsArticles.admin_move' => true,
            'WebContentsArticles.admin_view' => true,
            'WebContentsArticles.admin_deleteFile' => true,
                        
            'WebContentsNews.admin_add' => true,
            'WebContentsNews.admin_edit' => true,
            'WebContentsNews.admin_delete' => true,
            'WebContentsNews.admin_export' => true,
            'WebContentsNews.admin_index' => true,
            'WebContentsNews.admin_showTreeLevel' => true,
            'WebContentsNews.admin_showTree' => true,
            'WebContentsNews.admin_move' => true,
            'WebContentsNews.admin_view' => true,
            'WebContentsNews.admin_deleteFile' => true,
            
            //@deprecated - Use functionality defined in Reference model and References controller
            'WebContentsReferences.admin_add' => true,
            'WebContentsReferences.admin_edit' => true,
            'WebContentsReferences.admin_delete' => true,
            'WebContentsReferences.admin_export' => true,
            'WebContentsReferences.admin_index' => true,
            'WebContentsReferences.admin_showTreeLevel' => true,
            'WebContentsReferences.admin_showTree' => true,
            'WebContentsReferences.admin_move' => true,
            'WebContentsReferences.admin_view' => true,
            'WebContentsReferences.admin_deleteFile' => true,
            
            'References.admin_add' => true,
            'References.admin_edit' => true,
            'References.admin_delete' => true,
            'References.admin_export' => true,
            'References.admin_index' => true,
            'References.admin_deleteFile' => true,
                        
            'Partners.admin_add' => true,
            'Partners.admin_edit' => true,
            'Partners.admin_delete' => true,
            'Partners.admin_export' => true,
            'Partners.admin_index' => true,
            'Partners.admin_deleteFile' => true,
            'Partners.admin_move' => true,
                        
            'WebContentsSliders.admin_add' => true,
            'WebContentsSliders.admin_edit' => true,
            'WebContentsSliders.admin_delete' => true,
            'WebContentsSliders.admin_export' => true,
            'WebContentsSliders.admin_index' => true,
            'WebContentsSliders.admin_showTreeLevel' => true,
            'WebContentsSliders.admin_showTree' => true,
            'WebContentsSliders.admin_move' => true,
            'WebContentsSliders.admin_view' => true,
            'WebContentsSliders.admin_deleteFile' => true,                  
            
            'WebImages.admin_load' => true,
            'WebImages.admin_add' => true,
            'WebImages.admin_update' => true,
            'WebImages.admin_move' => true,
            'WebImages.admin_delete' => true,
            
            'ContentBlockInstances.admin_loadAdminView' => true,
            'ContentBlockInstances.loadInstanceView' => true,
            'ContentBlockInstances.getOwnerInstancesView' => true,
            'ContentBlockInstances.admin_deleteInstanceFile' => true,
            
            'TimelineEvents.admin_index' => true,
            'TimelineEvents.admin_add' => true,
            'TimelineEvents.admin_edit' => true,
            'TimelineEvents.admin_delete' => true,
            
            'Languages.admin_index' => true,
            'Languages.admin_edit' => true,
            'Languages.admin_move' => true,
            'Languages.admin_deleteFile' => true,
            
            'Countries.admin_index' => true,
            'Countries.admin_edit' => true,
            'Countries.admin_deleteFile' => true,
            'Countries.admin_getSelectorItems' => true,
            
            'UserProfiles.admin_deleteFile' => true,
            
            'Users.admin_index' => true,
            'Users.admin_export' => true,
            'Users.admin_add' => true,
            'Users.admin_update' => true,
            'Users.admin_updateProfile' => true,
            'Users.admin_editRights' => true,
            'Users.admin_delete' => true,
            //'Users.admin_switchToAnother' => true,
            'Users.admin_getSelectorItems' => true,
            'Users.update' => true,
            
            'Settings.admin_edit' => true,
            'Settings.admin_deleteFile' => true,

            'UrlRedirections.admin_index' => true,
            'UrlRedirections.admin_add' => true,
            'UrlRedirections.admin_view' => true,
            'UrlRedirections.admin_delete' => true,

            //'Tools.displayInfo' => true,
            //'Tools.cleanUpEshopOldProducts' => true,
            //'Tools.deleteEshopProductOrphanFiles' => true,
            'Tools.changeEshopOrderNumbers' => true,
            'Tools.normalizeText' => true,
            //'Tools.checkForCleanedUpProduct' => true,
            'Tools.allowUnpublishedLangsLocally' => true,
            'Tools.viewEshopOrdersStatistics' => true,
        ),
        // these rights apply when the screen is required by slug request or by snippet
        'screen' => array(
            'run' => true,
            '_fileManager' => true,
        ),
        // these rights apply when the element is required by snippet
        'element' => array(            
        ),
        // these rights apply when settings are listed, viewed and edited
        'setting' => array(
        ),
    ),
    'editors' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
//            'WebContents.admin_add' => true,
//            'WebContents.admin_copy' => true,
//            'WebContents.admin_edit' => true,
//            'WebContents.admin_editText' => true,
            'WebContents.admin_index' => true,
            'WebContents.admin_showTreeLevel' => true,
            'WebContents.admin_showTree' => true,
//            'WebContents.admin_move' => true,
//            'WebContents.admin_deleteFile' => true,
            'WebContents.admin_view' => true,
            
//            'WebContentsArticles.admin_add' => true,
//            'WebContentsArticles.admin_edit' => true,
//            'WebContentsArticles.admin_delete' => true,
//            'WebContentsArticles.admin_export' => true,
            'WebContentsArticles.admin_index' => true,
            'WebContentsArticles.admin_showTreeLevel' => true,
            'WebContentsArticles.admin_showTree' => true,
//            'WebContentsArticles.admin_move' => true,
            'WebContentsArticles.admin_view' => true,
//            'WebContentsArticles.admin_deleteFile' => true,
                        
//            'WebContentsNews.admin_add' => true,
//            'WebContentsNews.admin_edit' => true,
//            'WebContentsNews.admin_delete' => true,
//            'WebContentsNews.admin_export' => true,
            'WebContentsNews.admin_index' => true,
            'WebContentsNews.admin_showTreeLevel' => true,
            'WebContentsNews.admin_showTree' => true,
//            'WebContentsNews.admin_move' => true,
            'WebContentsNews.admin_view' => true,
//            'WebContentsNews.admin_deleteFile' => true,
            
            //@deprecated - Use functionality defined in Reference model and References controller
//            'WebContentsReferences.admin_add' => true,
//            'WebContentsReferences.admin_edit' => true,
//            'WebContentsReferences.admin_delete' => true,
            'WebContentsReferences.admin_export' => true,
            'WebContentsReferences.admin_index' => true,
            'WebContentsReferences.admin_showTreeLevel' => true,
            'WebContentsReferences.admin_showTree' => true,
//            'WebContentsReferences.admin_move' => true,
            'WebContentsReferences.admin_view' => true,
//            'WebContentsReferences.admin_deleteFile' => true,
            
//            'References.admin_add' => true,
//            'References.admin_edit' => true,
//            'References.admin_delete' => true,
//            'References.admin_export' => true,
//            'References.admin_index' => true,
//            'References.admin_deleteFile' => true,
            
//            'Partners.admin_add' => true,
//            'Partners.admin_edit' => true,
//            'Partners.admin_delete' => true,
//            'Partners.admin_export' => true,
//            'Partners.admin_index' => true,
//            'Partners.admin_deleteFile' => true,
//            'Partners.admin_move' => true,
                                                
//            'WebContentsSliders.admin_add' => true,
//            'WebContentsSliders.admin_edit' => true,
//            'WebContentsSliders.admin_delete' => true,
//            'WebContentsSliders.admin_export' => true,
            'WebContentsSliders.admin_index' => true,
            'WebContentsSliders.admin_showTreeLevel' => true,
            'WebContentsSliders.admin_showTree' => true,
//            'WebContentsSliders.admin_move' => true,
            'WebContentsSliders.admin_view' => true,
//            'WebContentsSliders.admin_deleteFile' => true,     
            
            'WebImages.admin_load' => true,
//            'WebImages.admin_add' => true,
//            'WebImages.admin_update' => true,
//            'WebImages.admin_move' => true,
//            'WebImages.admin_delete' => true,
            
            'ContentBlockInstances.admin_loadAdminView' => true,
            'ContentBlockInstances.loadInstanceView' => true,
            'ContentBlockInstances.getOwnerInstancesView' => true,
//            'ContentBlockInstances.admin_deleteInstanceFile' => true,
            
//            'TimelineEvents.admin_index' => true,
//            'TimelineEvents.admin_add' => true,
//            'TimelineEvents.admin_edit' => true,
//            'TimelineEvents.admin_delete' => true,
            
//            'Languages.admin_index' => true,
//            'Languages.admin_edit' => true,
//            'Languages.admin_move' => true,
                        
            'Users.admin_updateProfile' => true,
            'Users.update' => true,
            
            'Tools.allowUnpublishedLangsLocally' => true,
        ),
        // these rights apply when the screen is required by slug request or by snippet
        'screen' => array(
            'run' => true,
            '_fileManager' => true,
        ),
        // these rights apply when the element is required by snippet
        'element' => array(            
        ),
        // these rights apply when settings are listed, viewed and edited
        'setting' => array(
        ),
    ),
    'clients' => array(
        // these rights apply when the controller is required by mvc request or by snippet
        'controlleraction' => array(
            'Users.update' => true,
        ),
        // these rights apply when the screen is required by slug request or by snippet
        'screen' => array(
        ),
        // these rights apply when the element is required by snippet
        'element' => array(            
        ),
        // these rights apply when settings are listed, viewed and edited
        'setting' => array(
        ),
    )
); 
