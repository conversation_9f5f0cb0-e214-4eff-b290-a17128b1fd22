<?php
// user salutations
__(__FILE__, 'enum_mr');
__(__FILE__, 'enum_mrs');

// libs/Paginator.php link titles
__(__FILE__, 'First page');
__(__FILE__, 'Previous page');
__(__FILE__, 'Next page');
__(__FILE__, 'Last page');
__(__FILE__, 'Page %d');

// TimelineEvent positions
__(__FILE__, 'enum_left');
__(__FILE__, 'enum_right');

// TimelineEvent positions
__(__FILE__, 'enum_left');
__(__FILE__, 'enum_right');


__(__FILE__, 'enum_submitted_comment');
__(__FILE__, 'enum_approved_comment');
__(__FILE__, 'enum_rejected_comment');

__(__FILE__, 'enum_automatic');
__(__FILE__, 'enum_manual');