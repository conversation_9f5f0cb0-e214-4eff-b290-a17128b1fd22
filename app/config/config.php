<?php 
/**
 * Application configs
 * 
 * Global functions, clases Arr, Date, File, Html, Number, Sanitize, String, 
 * Utility, Validate and constants ON_LOCALHOST, ON_HTTPS, DS, ROOT, TMP, 
 * IS_INSTALLED can be used for _appInit section of this file
 * 
 * To read these values use App::getConfig() function, e.g.:
 *
 *      App::getConfig('App'); // will return whole app $config array
 *      App::getConfig('App', 'smtp'); // will return value of app $config['smtp']
 * 
 * @var array
 */
$onDevIps = ON_LOCALHOST || in_array(
    Sanitize::value($_SERVER['REMOTE_ADDR']), 
    array('************')
);
$config = array(
    
    /**
     * Inital values for App class config properties
     * 
     * @var array
     */
    '_appInit' => array(
        /**
         * Debug ON/OFF. 
         * 
         * If TRUE then all App::debug() occurences are executed regardles to output type.
         * To preserve working only some kind of output use array of allowed outputs,
         * e.g. array ('log', 'fb').
         * 
         * FirePHP class is loaded if App::$debug is not entirely turned off and if
         * initial debugOutput is set to 'fb'.
         * 
         * This is checked by App::debug() basic function.
         * 
         * Use App::setDebug() / App::getPropertyDebug() to set & get this property 
         * 
         * @var bool|array
         */
        'debug' => $onDevIps,
        
        /**
         * Debug output
         * 
         * This property is checked by App::debug() function.
         * 
         * Switches the output to:
         *      - 'echo': browser screen
         *      - 'hidden': browser screen with css display:none. Use showDebug() from JS console.
         *      - 'console': JS console.
         *      - 'fb': FireBug console. FirePHP class is loaded even if debugEnabled is FALSE.
         *      - 'log': debug.log file
         * 
         * @var string
         */
        'debugOutput' => 'fb',
        
        /**
         * Layouts in which the App::debug() should avoid to use html output type ('console', 'echo', 'hidden').
         * Instead of it the 'log' output is used. Layout are specified in array like:
         * 
         *      array(
         *          array('module' => 'MyModule', 'name' => 'myLayout1'),
         *          array('name' => 'myLayout2'),                           // layout with provided name in any module
         *          'myLayout3',                                            // layout with provided name in any module
         *          false,                                                  // Use FALSE if html debug should be avoided also in case of no layout used
         *      )
         *
         * @var array 
         */
        'avoidHtmlDebugLayouts' => array(
            false,
            array('module' => 'App', 'name' => 'ajax'),
            array('module' => 'App', 'name' => 'json'),
            array('module' => 'App', 'name' => 'plaintext'),
        ),
        
        /**
         * Options to be used for method App::sendEmail() during debuging/developing
         * the project. All options of App::sendEmail() can be used plus following additional:
         *      - 'debug' (bool) If TRUE then corresponding options will be rewrited
         *      by values defined here in all occurences of App::sendEmail(). If FALSE
         *      then App::sendEmail() work in production mode.
         *      - 'to' (string|array) Specification of $to argument of App::sendEmail()
         *      - 'smtp' (array) Specification of smtp options to be used if 'engine'
         *      is set to 'smtp' value. These options are used instead of standard 
         *      App smtp settings ('smtp.host', ..., 'smtp.encryption'). The array is like:
         * 
         *          array(
         *              'host' => '',
         *              'port' => '',
         *              'username' => '',
         *              'password' => '',
         *              'encryption' => '', // NULL, 'tls', 'ssl'
         *          )
         * 
         * @var array
         */
        'debugEmailOptions' => array(
            'debug' => ON_LOCALHOST,
            'engine' => 'smtp',
            'smtp' => array(
                /*/>
                // ATTENTION: if sent of debug emails fails then very probably google have blocked you
                // (for security reasons). In such a case log into gmail (https://www.google.com/gmail ),
                // open secury alert email and confirm that it was you who tried to use the account.
                // Verification email is <NAME_EMAIL> so you can let google to send the
                // verification code there. Verification phone is set to Mojos phone so if
                // verification email does not work you can use also this possibility.
                'host' => 'smtp.gmail.com',
                'port' => '587',
                'username' => '<EMAIL>',
                'password' => 'HqlP78M!Am?X4l',
                'encryption' => 'tls',
                /*/
                'host' => 'mail.webglobe.sk',
                'port' => '587',
                'username' => '<EMAIL>',
                'password' => '4YzmYzaN',
                'encryption' => 'tls',
                //*/
            ),
            'to' => '<EMAIL>',
            'cc' => null,
            'bcc' => null,
            'sender' => null, // it will be set to smtp host by App::sendEmail()
        ),

        /**
         * Email address or array of addresses where the log emails are sent by App::log(). 
         * Messages are send only under condition that 'email' option of App::log() is TRUE.
         * If FALSE then the sending of log emails is turned off.
         *
         * @var string|array|bool 
         */
        'logEmail' => ON_LOCALHOST ? false : '<EMAIL>',
        
        /**
         * Options passed to FB::setOptions() method of FirePHP.
         * This is done in App::debug() when TRUE is on input.
         * 
         * If this is not an array then FB::setOptions() method is not launched, which
         * results in default FirePHP options.
         * 
         * @var array
         */
        'fbOptions' => array(
            'maxObjectDepth' => 10,
            'maxArrayDepth' => 10,
            'maxDepth' => 10,
            'useNativeJsonEncode' => true,
            'includeLineNumbers' => true
        ),
        
        /**
         * Sql logging on/off.
         * Can be TRUE/FALSE or DB::LOG_WITH_BACKTRACE.
         * ATTENTION: Do not use DB::LOG_WITH_BACKTRACE for production!
         *
         * @var bool 
         */
        'sqlLogging' => $onDevIps,

        /**
         * Error reporting ON/OFF
         * TRUE/FALSE can be used to simply turn reporting on/off.
         * PHP error constans can be used for precise specification of errors
         * which should be reported:
         * 
         * E_ERROR | E_WARNING | E_PARSE // Report simple running errors
         * E_ERROR | E_WARNING | E_PARSE | E_NOTICE // Reporting E_NOTICE can be good too (to report uninitialized variables or catch variable name misspellings ...)
         * E_ALL ^ E_NOTICE // Report all errors except E_NOTICE
         * E_ALL & ~E_NOTICE // The same as above
         * 
         * @var bool|int
         */
        'errorReporting' => ON_LOCALHOST ? E_ALL & ~E_STRICT & ~E_DEPRECATED : false,
        
        /**
         * Sets which PHP errors are catched by App::catchError()
         * TRUE/FALSE can be used to simply turn catching on/off.
         * PHP error constans can be used for precise specification of errors
         * which should be caught:
         * 
         * E_ERROR | E_WARNING | E_PARSE // Report simple running errors
         * E_ERROR | E_WARNING | E_PARSE | E_NOTICE // Reporting E_NOTICE can be good too (to report uninitialized variables or catch variable name misspellings ...)
         * E_ALL ^ E_NOTICE // Report all errors except E_NOTICE
         * E_ALL & ~E_NOTICE // The same as above
         * 
         * @var bool|int
         */
        'errorCatching' => ON_LOCALHOST ? 
            E_ALL & ~E_STRICT & ~E_DEPRECATED : 
            E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED & ~E_WARNING, 

        /**
         * Allows to turn off all js 'embroidery', e.g. facebook, google and twitter
         * social plugins, google analytics, ... and so make debuging of local js
         * much more easier (=quicker). 
         * 
         * It is up to developer to use this property in any new js 'embroidery' (mostly 
         * implemented as App module elements) and to keep posibility to switch off 
         * all of them at once ... and breath a fresh air :)
         * 
         * @var bool 
         */
        'useJsEmbroidery' => ON_LOCALHOST ? false : true,
        
        /**
         * If TRUE then origin comments in templates are allowed.
         * 
         * $var bool
         */
        'allowOriginComments' => ON_LOCALHOST ? true : false,
        
        /**
         * Name of database connection config (in app/config/database.php) which 
         * will be used for database connection
         * 
         * @var type 
         */
        'dbConfigName' => 'default',
        
        /**
         * Name of DB table where users are defined. 
         * 
         * This table must contain at least following columns:
         * 
         * - 'id' int(11) - primary key
         * - 'username' varchar(255)
         * - 'password' varchar(255)
         * - 'active' bool(1)
         * - '{groupsTable}_id' int(11) - foreign key to user group
         * 
         * @var string
         */
        'usersTable' => 'run_users',

        /**
         * Name of DB table where groups are defined. 
         * 
         * This table must contain at least following columns:
         * 
         * - 'id' int(11) - primary key
         * - 'pid' varchar(20) - internal name of group refered in program code
         * - 'name' varchar(100)
         * - 'hierarchy' int(11) - Group with lower value is superior to group with higher value. Some groups can have the same values if they are on the same level
         * 
         * @var string
         */
        'groupsTable' => 'run_groups',
    
        /**
         * Name of DB table where settings are defined. 
         * 
         * This table must contain at least following columns:
         * 
         * - 'id' int(11) - primary key
         * - 'module' varchar(255) - module name the setting belongs to
         * - 'pid' varchar(255) - internal name of setting refered in program code
         * - 'name' varchar(255) - label used to describe settings form field
         * - 'description' text - detailed setting description of settings form field
         * - 'value' text - setting value
         * - 'js_visible' bool(1) - If TRUE the the setting value is sent also to js scripts (using js config object)
         * 
         * @var string
         */
        'settingsTable' => 'run_settings',
        
        /**
         * Name of DB table where contents are defined. 
         * 
         * This table must contain at least following columns:
         * @todo
         * 
         * @var strings 
         */
        'contentsTable' => 'run_web_contents',

        /**
         * Name of DB table where languages are defined. 
         * 
         * This table must contain at least following
         * columns:
         * - 'name' varchar(50) - language name
         * - 'lang' varchar(2) - ISO 639-1 code for language
         * - 'code' varchar(3) - ISO 639-2 code for language
         * - 'locale' varchar(5) - IETF language tag
         * - 'icon' varchar(255)
         * - 'sort' int(10)
         * - 'default' bool(1)
         * - 'active' bool(1)
         * 
         * Alternatively the languages table can be defined directly here as array
         * like:
         * 
         *      array(
         *          array(
         *              'name' => 'Slovak'
         *              'lang' => 'sk',
         *              'code' => 'slo',
         *              'locale' => 'sk_SK',
         *          ),
         *          array(
         *              'name' => 'English'
         *              'lang' => 'en',
         *              'code' => 'eng',
         *              'locale' => 'en_GB',
         *          ),
         *          ...
         *      )
         * 
         * Default language must be placed as the first, and only active languages are
         * listed. That is why in this case 'default' and 'active' fields are not included. 
         * 
         * @var string|array
         */
        'languagesTable' => 'run_languages',
//        'languagesTable' => array(
//            array(
//                'name' => 'Slovak',
//                'lang' => 'sk',
//                'code' => 'slo',
//                'locale' => 'sk_SK',
//            ),
//            array(
//                'name' => 'English',
//                'lang' => 'en',
//                'code' => 'eng',
//                'locale' => 'en_GB',
//            ),
//            array(
//                'name' => 'German',
//                'lang' => 'de',
//                'code' => 'deu',
//                'locale' => 'de_DE',
//            ),
//        ),
        
        /**
         * If TRUE then lang code is localized and it is composed from country code
         * and language code separated by slash, e.g. 'us/en', 'us/es'. If FALSE then
         * the lang code is composed just from language code, e.g. 'en', 'es'.
         * 
         * NOTE: 
         * Country codes: http://www.iso.org/iso/country_names_and_code_elements
         * Language codes: http://www.loc.gov/standards/iso639-2/php/code_list.php
         *
         * @var bool 
         */
        'localizedLang' => false,
        
        /**
         * If TRUE then in case of localized lang (App::$localizedLang is TRUE) the language part
         * can be autodetected from information sent by browser. It means that the localized 
         * lang can be incomplete, lang part can be omitted ('us/my-page' instead of 'us/en/my-page').
         * In such a case the lang part is autodetected.
         *
         * @var bool 
         */
        'autodetectLang' => false,

        /**
         * If TRUE then default lang does not appear in urls
         * (App::urlLang and URL_LANG is set to NULL)
         * 
         * @var bool
         */
        'defaultLangHidden' => true,
        
        /**
         * If TRUE then urls are created with trailing slash at the end of url path. 
         * If FALSE then urls are created without trailing slash at the end of url path.
         * If NULL then trailing slash is let as it is.
         * 
         * ATTENTION: If you change this then update .htaccess accordingly
         */
        'urlPathTrailingSlash' => false, 
        
        /**
         * Single URL option or array of URL options to be inherited by default 
         * from app actual url into each generated url, e.g. array('get' => array('lang')) 
         * to inherit GET param 'lang' from actual URL to new created. You can write this also
         * as array('get/lang'). If you use the path syntax then the specified
         * path can be as long as you need: 'get/filter/User.name'. Array syntax
         * allows only 2 levels (see the 1st example) but is faster. Defaults to empty array().
         * 
         * @var string|array
         */
        'urlInherit' => array(
//            'get/filter/discounted',
//            'get/filter/for_beginners',
//            'get/filter/for_advanced',
//            'get/filter/for_professionals',
        ),

        /**
         * Options for App::hash()
         * @var array
         */
        'hashOptions' => array(
            /**
             * Secret key used for generating the HMAC variant of the message digest.
             * 
             * @var string
             */
            'key' => '',

            /**
             * String appended to the string before hashing.
             *  
             * @var string
             */
            'suffix' => '',
        ),

        /**
         * Options for App::formatNumber()
         * Default number format properties are defined for each of used langs
         * in associative arrays like this:
         * 
         *      array(
         *          '{lang01}' => '{propertyValue01}',
         *          '{lang02}' => '{propertyValue02}',
         *          ...
         *      )
         */

        /**
         * Default number of decimal points
         * @var array of integers 
         */
        'decimals' => array(
            'sk' => 2,
            'en' => 2,
            'de' => 2,
            'fr' => 2,
        ),

        /**
         * Default separator for the decimal point
         * @var array of strings 
         */
        'decimalPoint' => array(
            'sk' => ',',
            'en' => '.',
            'de' => ',',
            'fr' => ',',
        ), 

        /**
         * Default thousands separator
         * @var array of strings 
         */
        'thousandsSeparator' => array(
            'sk' => ' ',
            'en' => ',',
            'de' => ' ',
            'fr' => ' ',
        ),   
        
        /**
         * Url root of the project, e.g. '/my_project'.
         * 
         * URL_ROOT constant is defined according to this.
         * 
         * It does not contain trailing slash or it is just empty.
         * 
         * It is not possible to retrieve url root so easily as ROOT taking into account
         * different apache configurations on different hostings.
         * That's why it is up to developer to set this properly here.
         *
         * @var string 
         */
        'urlRoot' => '',

        /**
         * Home slug 
         * HOME_SLUG constant is defined according to this.
         *
         * @var string
         */
        'homeSlug' => 'home',
        
        /**
         * Default layout name.
         * If string then the layout is searched under App module. To provide layouts
         * from other modules use array like array('module' => ..., 'name' => ...)
         * 
         * @var string|array
         */
        'defaultLayout' => 'default',

        /**
         * Home layout name.
         * If string then the layout is searched under App module. To provide layouts
         * from other modules use array like array('module' => ..., 'name' => ...)
         * 
         * @var string|array
         */
        'homeLayout' => 'default', //'home'
        
        /**
         * Admin login slug
         * 
         * Slug on which is accessible login to administration
         * 
         * @var string
         */
        'adminLoginSlug' => 'runlogin',
                
        /**
         * Admin slug
         * 
         * Slug on which is accessible administration
         * 
         * @var string
         */
        'adminSlug' => 'run',
        
        /**
         * Does the actual application use also frontend or is it pure backend 
         * (intranet) application?
         * 
         * @var bool
         */
        'adminOnly' => false,
        
        /**
         * Login page pid
         * 
         * Page on which is accessible frontend login for registered users
         * 
         * @var string
         */
        'userLoginPid' => 'App.Users.login',
        
        /**
         * Frontend login default target content pid
         * 
         * Login target URL is stored on implicit login to session.
         * 
         * In case that nothing is there (frontend login page is called explicitly by user)
         * then slug of this content is used to for target URL. 
         * 
         * This value or the value of login target URL (using App::setLoginTargetUrl())
         * can be tweaked e.g. in app/configs/init.php.
         * 
         * The default login target from admin login page is always admin slug!
         * 
         * Use App::getLoginTargetUrl() to get the final value of login target URL
         * 
         * @var string 
         */
        'userLoginTargetPid' => 'home',
        
        /**
         * Permanent session lifetime
         * 
         * Lifetime of pernanent sessions used for permanent logins. In seconds.
         * e.g. 14 days = 1209600
         * 
         * @var int
         */
        'permanentSessionLifetime' => 1209600,
        
        /**
         * Default mail engine used by method App::sendEmail().
         * 
         * Possible valuse are 'mail', 'smtp'.
         *
         * @var string 
         */
        'mailEngine' => 'smtp',
                
        /**
         * Image processing engine (used by File class).
         * 
         * Possible values are 'gd', 'imagick', 'imlib'
         * 
         * ATTENTION: engine 'gd' has problems with transparent .png files.
         * So avoid the use of this engine if possible and use imagick instead!
         * 
         * @var string 
         */
        'imageEngine' => ON_LOCALHOST ? 'imagick' : 'imagick',
            
        /**
         * Is the https protocol available?
         * 
         * @var bool 
         */
        'httpsIsAvailable' => false,
        
        /**
         * Is switching between http and https protocol allowed?
         * 
         * For case http-to-https switch this applies only if config 'httpsIsAvailable' is TRUE.
         * For case https-to-http switch this applies always.
         * Set this FALSE if you would like to keep your site only on http or on https.
         */
        'allowSwitchBetweenHttpAndHttps' => false,
        
        /**
         * Definition of css files which should be placed as first (if attached)
         * and in here defined order. It form is:
         * 
         *      array(
         *          '/app/css/my.css',
         *          '/app/css/libs/my-lib.css',
         *          ...
         *      )
         * 
         * Files which are not mentioned in App::$firstCssFiles nor in App::$lastCssFiles 
         * are placed between these two groups of files (in order as they occure in App::$cssFiles)
         * 
         * NOTE: Also modules css files must be defined here
         */
        'firstCssFiles' => array(
            '/app/css/vendors/normalize.min.css',
            '/app/css/vendors/bootstrap.css', // bootstrap contains normalize.css so use only one of them
            '/app/css/vendors/bootstrap-theme.css',
            '/app/css/libs/basic.css',
            // be sure that fa5 is loaded before fa4.7 to allow fa4.7 css override fa5 where needed
            '/app/css/vendors/font-awesome-5/css/all.min.css', 
        ),
        
        /**
         * Definition of css files which should be placed as last (if attached)
         * and in here defined order. It form is:
         * 
         *      array(
         *          '/app/css/my.css',
         *          '/app/css/libs/my-lib.css',
         *          ...
         *      )
         * 
         * Files which are not mentioned in App::$firstCssFiles nor in App::$lastCssFiles 
         * are placed between these two groups of files (in order as they occure in App::$cssFiles)
         * 
         * NOTE: Also modules css files must be defined here
         */
        'lastCssFiles' => array(
            '/app/css/main.css',
            '/app/css/print.css',
            '/app/css/ie.css',
        ),
        
        /**
         * List of substitute files for attached css files like
         * 
         *      array(
         *          '/atached/file/path.css' => '/substitute/file/path.css',
         *          '/app/css/my.css' => '/app/css/my.min.css',
         *          ...
         *      )
         * 
         * Is suposed for fast replacements of used files by thier new / other version (e.g CDN) 
         * 
         * @var type 
         */
        'cssFilesSubstitutions' => array(
            // turn off bootstrap theme
            '/app/css/vendors/bootstrap-theme.css' => '/app/css/vendors/bootstrap.css',
        ),
        
        /**
         * Should be css files compiled to assets?
         * 
         * @var bool 
         */
        'compileCss' => false,
        
        /**
         * Definition of js files which should be placed in html head (if attached)
         * and in here defined order. Its form is:
         * 
         *      array(
         *          '/app/js/my.js',
         *          '/app/js/libs/my-lib.js',
         *          ...
         *      )
         * 
         * Use App::getHeadJsLinks() to create html for these group of js files.
         * Links for files which are not mentioned here are created by App::getJsLinks().
         * 
         * NOTE: Also modules js files must be defined here
         */
        'headJsFiles' => array(
            '/app/js/vendors/modernizr.min.js',
        ),
        
        /**
         * Definition of js files which should be placed as first (if attached)
         * and in here defined order. Its form is:
         * 
         *      array(
         *          '/app/js/my.js',
         *          '/app/js/libs/my-lib.js',
         *          ...
         *      )
         * 
         * Files which are not mentioned in App::$headJsFiles, App::$firstJsFiles nor 
         * in App::$lastJsFiles are placed between App::$firstJsFiles and App::$lastJsFiles 
         * in order as they occure in App::$jsFiles.
         * 
         * NOTE: Also modules js files must be defined here
         */
        'firstJsFiles' => array(
            '/app/js/vendors/jquery.min.js',
            '/app/js/vendors/jquery-ui/jquery-ui.min.js',
            '/app/js/vendors/bootstrap.min.js',
            '/app/js/libs/globalFunctions.js',
        ),
        
        /**
         * Definition of js files which should be placed as last (if attached)
         * and in here defined order. Its form is:
         * 
         *      array(
         *          '/app/js/my.js',
         *          '/app/js/libs/my-lib.js',
         *          ...
         *      )
         * 
         * Files which are not mentioned in App::$headJsFiles, App::$firstJsFiles nor 
         * in App::$lastJsFiles are placed between App::$firstJsFiles and App::$lastJsFiles 
         * in order as they occure in App::$jsFiles.
         * 
         * NOTE: Also modules js files must be defined here
         */
        'lastJsFiles' => array(
            '/app/js/main.js'
        ),

        /**
         * List of substitute files for attached js files like
         * 
         *      array(
         *          '/atached/file/path.js' => '/substitute/file/path.js',
         *          '/app/js/my.js' => '/app/js/my.min.js',
         *          ...
         *      )
         * 
         * Is suposed for fast replacements of used files by thier new / other version (e.g CDN) 
         * 
         * @var array 
         */
        'jsFilesSubstitutions' => array(
            //'/app/js/vendors/jquery.min.js' => '/app/js/vendors/jquery.js',
            //'/app/js/vendors/jquery.min.js' => '//ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js',
            '/app/js/vendors/bootstrap.min.js' => '/app/js/vendors/bootstrap.js',
        ),
        
        /**
         * What is the used javacript module implementation?
         * Possible values are:
         *      - 'system' for SystemJs (see https://github.com/systemjs/systemjs)
         *      - 'native' for native modules implementation in browsers (ES6 / ES2015, 
         *          see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Modules)
         * This is set to 'system' as SystemJS allows to define importmap - see phpDoc 
         * for 'jsImportmap' config.
         * 
         * ATTENTION: Keep this in synchro with options in tsconfig.json! 
         * For 'system' set following compiler options in tsconfig.json:
         *      "compilerOptions": {
         *          "target": "ES5",
         *          "module": "system",
         *      }
         * For 'native' set following compiler options in tsconfig.json:
         *      "compilerOptions": {
         *          "target": "ES2015",
         *          "module": "ES2015",
         *      }
         * 
         * NOTE: See also "Module Resolution Options" in tsconfig.json 
         * 
         * @var string 
         */
        'jsModuleImplementation' => 'system',
        
        /**
         * Importmap used when 'jsModuleImplementation' is set to 'system' (SystemJS).
         * 
         * This allows to write in .ts files following module imports, e.g. for lodash:
         * 
         *      import _ from 'lodash'
         * 
         * This import is ok for typescript (under condition that you have installed
         * types for lodash like `npm install @types/lodash --save-dev`). But we need
         * to know the real path to get the lodash from. The SystemJS allows to do this
         * substitutions by defining importmap. ATTENTION: The real source must be 
         * a plain (non-modular) javascript! That is why we can import the whole
         * library ("default import" in words of ES6 modules), but it is not possible
         * to import only some members/parts of the library.
         * 
         * The another possibility (instead of doing imports for used libraries)
         * is to load libs globaly by App::setJsFiles() - just in the same way
         * as jQuery is loaded and use them in js modules without imports.
         * 
         * ATTENTION: All fajnwork modules must define their mappings in app module config!
         * 
         * @var array
         */
        'jsModuleImportmap' => array(
            //'jquery' => 'https://code.jquery.com/jquery-2.2.4.min.js',
            'jquery' => '/app/js/vendors/jquery.min.js',
            //'lodash' => '/app/js/vendors/lodash.min.js',
            'lodash' => 'https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.19/lodash.min.js',
        ),
        
        /**
         * Should be js files compiled to assets?
         * 
         * @var bool 
         */
        'compileJs' => false,

        /**
         * The default key used by Str::crypt() and String:decrypt().
         * Use any random ASCII string. Internally it is hashed by md5() 
         * to 32-character hexadecimal number.
         * 
         * @var string
         */
        'cryptKey' => 'codice',
        
        /**
         * Switch for obfuscation of actual content text. Possible values are: 
         * - If 'allow' then content text obfuscation is allowed and done according to value of 'obfuscate' field.
         * - If TRUE then all content texts are obfuscated regardless to value of 'obfuscate' field.
         * - If FALSE then content text obfuscation is denied regardless to value of 'obfuscate' field.
         * 
         * @var string|bool
         */
        'obfuscateContentText' => 'allow',
    ),
    
    'adminIcons' => array(
//        'Module' => '<i class="fa fa-th"></i>',
//        'Module' => '<i class="fa fa-th-large"></i>',
//        'Module' => '<i class="fa fa-dashboard"></i>',
//        'Module' => '<i class="fa fa-cube"></i>',
//        'Module' => '<i class="fa fa-circle"></i>',
        'Module' => '<i class="fa fa-sliders"></i>',
//        'Languages' => '<i class="fa fa-globe"></i>',
        'Languages' => '<i class="fa fa-language"></i>',
//        'Countries' => '<i class="fa fa-flag"></i>',
        'Countries' => '<i class="fa fa-globe"></i>',
        'UrlRedirections' => '<i class="fa fa-external-link"></i>',
        'Users' => '<i class="fa fa-user"></i>',
        'Settings' => '<i class="fa fa-gears"></i>',
        'FileManager' => '<i class="fa fa-folder-open"></i>',
//        'WebContents' => '<i class="fa fa-file-o"></i>',
        'WebContents' => '<i class="fa fa-file"></i>',
//        'WebContents' => '<i class="fa fa-th"></i>'
        'References' => '<i class="fa fa-thumbs-o-up"></i>',
//        'References' => '<i class="fa fa-bullhorn"></i>',
        'Tools' => '<i class="fa fa-wrench"></i>',
    )
    
    /**
     * Put here your custom app level config values...
     */
    
//    'smtp' => array(
//        'host' => '',
//        'port' => '',
//        'username' => '',
//        'password' => '',
//    )
    

);
