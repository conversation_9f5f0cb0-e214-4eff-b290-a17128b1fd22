<?php
/**
 * Module contents initialization. Each content is defined by pair '{pid}' => {arrayOfWebContentFields}. 
 * There are two special fields: 'parent' and 'blocks'. See here below how to use them.
 * An example:
 * 
 *      array(
 *          '{pid}' => array( // pid is used to identify existing contents in DB and so avoid creation on duplicities
 *              'parent' => '{parentPid}', // use 'root' for toplevel folders,  required
                'lang' => '{lang1},{lang2},...', // comma separated list of languages to create the contents for
 *              'name' => '{contentName}', // if not defined then defaults to {pid}
 *              'locator' => '{contentSlug}', // if not defined then only content category is created without real content
 *              'text' => '{contentText}', // this is created as html content block
 *              'active' => false, // if not defined then defaults to TRUE
 *              'permanent' => true, // if not defined then defaults to FALSE
 *              'blocks' => array(
 *                  array(
 *                      'content_block_model' => 'ContentBlock.ContentBlockHtml', // required
 *                      'name' => '{contentBlockName}', // if not provided then default content block name is used
 *                      'active' => true, // if not defined then defaults to TRUE
 *                      'content_block_data' => array(
 *                          'html' => '<h1>My sample content</h1> This is just a sample content',
 *                      ),
 *                  ),
 *              ),
 *          ),
 *          '{pid2}' => array(...),
 *          ...,
 *      )
 * 
 * Use '/mvc/App/Tools/updateContents' to load contents into DB
 * 
 * Contents are created for all active langs from specified (in 'lang'). 
 * If no langs are specified then content is created for all active languages.
 * Missing lang roots are created too.
 */ 
$contents = array(
    //
    // MAINMENU
    //
    'mainmenu' => array(
        'parent' => 'root',
        'name' => 'Hlavné menu', 
        'permanent' => 1,
    ),
            
    //
    // FOOTERMENU
    //
    'footermenu' => array(
        'parent' => 'root',
        'name' => 'Pätičkové menu',
        'permanent' => 1,
    ),

    //
    // SYSTEM
    //
    'system' => array(
        'parent' => 'root',
        'name' => 'Systémové stránky', 
        'permanent' => 1,
    ),
    'search' => array(
        'parent' => 'system',
        'name' => 'Výsledky vyhľadávania', 
        'locator' => 'vyhladavanie', 
        'text' => '<object _snippet="e.App.search" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),    
    'App.WebContentsNews.index' => array(
        'parent' => 'system',
        'name' => 'Aktuality', 
        'locator' => 'aktuality', 
        'text' => '<object _snippet="App.WebContentsNews.index" _snippet_generic="1" _snippet_name="Aktuality"></object>',
        'permanent' => 1,
    ),   
    'App.WebContentsNews.view' => array(
        'parent' => 'system',
        'name' => 'Aktualita', 
        'locator' => 'aktualita', 
        'text' => '<object _snippet="App.WebContentsNews.view" _snippet_generic="1" _snippet_name="Detail aktuality"></object>',
        'permanent' => 1,
    ),   
    'App.WebContentsArticles.index' => array(
        'parent' => 'system',
        'name' => 'Blog', 
        'locator' => 'blog', 
        'text' => '<object _snippet="App.WebContentsArticles.index" _snippet_generic="1" _snippet_name="Clanky"></object>',
        'permanent' => 1,
    ),   
    'App.WebContentsArticles.view' => array(
        'parent' => 'system',
        'name' => 'Článok', 
        'locator' => 'clanok-blogu', 
        'text' => '<object _snippet="App.WebContentsArticles.view" _snippet_generic="1" _snippet_name="Detail clanku"></object>',
        'permanent' => 1,
    ),   
    'App.Users.login' => array(
        'parent' => 'system',
        'name' => 'Prihlasovanie', 
        'locator' => 'login', 
        'text' => '<object _snippet="App.Users.login" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),
    'App.Users.logout' => array(
        'parent' => 'system',
        'name' => 'Odhlásenie', 
        'locator' => 'logout', 
        'text' => '<object _snippet="App.Users.logout" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),
    'App.Users.register' => array(
        'parent' => 'system',
        'name' => 'Vytvoriť účet', 
        'locator' => 'vytvorit-ucet', 
        'text' => '<object _snippet="App.Users.register" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),
    'App.Users.update' => array(
        'parent' => 'system',
        'name' => 'Môj profil', 
        'locator' => 'moj-ucet', 
        'text' => '<object _snippet="Eshop.EshopUsers.profileMenu" _snippet_generic="1" _snippet_name="Navigacia v profile"></object><object _snippet="App.Users.update" _snippet_generic="1" _snippet_name="Uprava uctu"></object>',
        'permanent' => 1,
    ),
    'App.Users.forgotPassword' => array(
        'parent' => 'system',
        'name' => 'Zabudnuté heslo', 
        'locator' => 'zabudnute-heslo', 
        'text' => '<object _snippet="App.Users.forgotPassword" _snippet_generic="1"></object>',
        'permanent' => 1,
    ),
    'App.privacyPolicyInfo' => array(
        'parent' => 'footermenu',
        'name' => 'Spracovanie osobných údajov', 
        'locator' => 'spracovanie-osobnych-udajov', 
        'text' => 'Spracovanie osobných údajov - pripravujeme',
        'permanent' => 1,
    ),
    'App.cookiesPrivacyPolicyInfo' => array(
        'parent' => 'system',
        'name' => 'Použitie cookies', 
        'locator' => 'pouzitie-cookies', 
        'text' => '<h1>Používanie cookies</h1>
<p>Cookies sú malé súbory, ktoré sa bežne ukladajú na Vašom počítači alebo mobilnom zariadení pri prezeraní webových stránok prostredníctvom internetového prehliadača. Používajú sa jednak na bežné fungovanie stránky (napr. prihlásenie) alebo na poskytovanie lepších služieb.</p>
<p>Pri prehliadaní našej stránky sa používajú tieto typy cookies:</p>
<p>1. Session cookie</p>
<p>Slúži na zabezpečenie základnej funkcionality stránky. Jej trvanlivosť je dočasná a vymaže sa z Vášho zariadenia automaticky po zatvorení okna prehliadača.</p>
<p>2. Funkčná cookie</p>
<p>Slúži na zapamätanie si Vášho súhlasu s používaním súborov cookies na tejto webovej stránke pri jej opätovnej návšteve. Vďaka nej nemusíte potvrdzovať súhlas s používaním cookies pri každej návšteve tejto webovej stránky. Na Vašom zariadení je uložená trvale pokým ju sami neodstránite. Ako odstrániť súbory cookies z Vášho zariadenia (resp. ako nastaviť ich správu) sa dozviete na stránke výrobcu konkrétneho internetového prehliadača (napr. <a href="https://support.google.com/accounts/answer/61416?hl=sk" target="_blank">Chrome</a>, <a href="https://support.mozilla.org/sk/kb/cookies-udaje-ktore-webove-stranky-ukladaju-na-vas-pocitac?redirectlocale=sk&amp;redirectslug=Spr%C3%A1va+cookies" target="_blank">Firefox</a>, <a href="https://support.microsoft.com/sk-sk/help/17442/windows-internet-explorer-delete-manage-cookies" target="_blank">Internet Explorer</a> atď.).</p>
<p>3. Analytické cookies</p>
<p>Slúžia nám na sledovanie a analyzovanie návštevnosti webovej stránky pomocou služby Google Analytics (cookie tretích strán). Bližšie informácie nájdete <a href="https://support.google.com/analytics/topic/2919631?hl=sk&amp;ref_topic=1008008" target="_blank">na stránke poskytovateľa služby</a>. Ak si neprajete aby boli Vaše dáta spracovávané službou Google Analytics môžete si nainštalovať vhodný softvérový doplnok do webového prehliadača (napr. <a href="https://tools.google.com/dlpage/gaoptout" target="_blank">Google Analytics Opt-out Browser Add-on</a> atď.).</p>',
        'permanent' => 1,
    ),
    
    //
    // UNCLASSIFIED
    //
    'unclassified' => array(
        'parent' => 'root',
        'name' => 'Nezaradené',
        'permanent' => 1,
    ),
    'home' => array(
        'parent' => 'unclassified',
        'name' => 'Home', 
        'locator' => 'home', 
        'text' => '
<h2 class="section-title"><a href="/novinky">Novinky</a></h2>
<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" _snippet_name="Novinky" has-image="1" index-type="brief" limit="4" paginate="0" show-index-type-select="0" show-sort-select="0"></object>

<h2 class="section-title"><a href="/novinky">Pripravujeme</a></h2>
<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" _snippet_name="Novinky" has-image="1" index-type="brief" limit="4" paginate="0" show-index-type-select="0" show-sort-select="0"></object>

<h2 class="section-title"><a href="/novinky">Akcie</a></h2>
<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" _snippet_name="Novinky" has-image="1" index-type="brief" limit="4" paginate="0" show-index-type-select="0" show-sort-select="0"></object>

<h2 class="section-title"><a href="/novinky">Odporúčame</a></h2>
<object _snippet="Eshop.EshopProducts.index" _snippet_generic="1" _snippet_name="Novinky" has-image="1" index-type="brief" limit="4" paginate="0" show-index-type-select="0" show-sort-select="0"></object>
        ',
        'has_side_content' => 1,
        'side_text' => '<object _snippet="Eshop.EshopProducts.indexTop10" _snippet_generic="1" _snippet_name="Top 10"></object>',
        'permanent' => 1,
    ),
    
    //
    // SHARED
    //
    'shared' => array(
        'parent' => 'root',
        'name' => 'Zdieľané',
        'permanent' => 1,
    ),
    'homeProductSlidersTabs' => array(
        'parent' => 'shared',
        'name' => 'Taby produktových slajdrov na úvodnej stránke',
        'permanent' => 1,
    ),
    'homeProductSliderTab01' => array(
        'parent' => 'homeProductSlidersTabs',
        'name' => 'Odporúčame',
        'blocks' => array(
            array(
                'content_block_model' => 'Eshop.ContentBlockEshopProductsSlider',
                'name' => 'Slajder odporúčaných produktov',
                'content_block_data' => array(
                    'typeOfFilter' => 'static',
                    // these are just some sample products
                    'filter_product' => array(9305, 9311, 9320, 9327, 9335, 12025),
                    'block_center_content' => 'wide',
                ),
            ),
        ),
    ),
    'homeProductSliderTab02' => array(
        'parent' => 'homeProductSlidersTabs',
        'name' => 'Výpredaj',
        'blocks' => array(
            array(
                'content_block_model' => 'Eshop.ContentBlockEshopProductsSlider',
                'name' => 'Slajder výpredajových produktov',
                'content_block_data' => array(
                    'typeOfFilter' => 'static',
                    // these are just some sample products
                    'filter_product' => array(2973, 10749, 10785, 18103, 18435, 18741, 21410, 23609),
                    'block_center_content' => 'wide',
                ),
            ),
        ),
    ),
    'homeProductSliderTab03' => array(
        'parent' => 'homeProductSlidersTabs',
        'name' => 'Najpredávanejšie',
        'blocks' => array(
            array(
                'content_block_model' => 'Eshop.ContentBlockEshopProductsSlider',
                'name' => 'Slajder najpredávanejších produktov',
                'content_block_data' => array(
                    'typeOfFilter' => 'static',
                    // these are just some sample products
                    'filter_product' => array(7935, 10700, 14077),
                    'block_center_content' => 'wide',
                ),
            ),
        ),
    ),
    
    //
    // SLIDERS
    //
    'sliders' => array(
        'parent' => 'root',
        'name' => 'Slajdre',
        'permanent' => 1,
    ),
    
    //
    // NEWS
    //
    'news' => array(
        'parent' => 'root',
        'name' => 'Novinky',
        'permanent' => 1,
    ),   
    
    //
    // ARTICLES (BLOG)
    //
    'articles' => array(
        'parent' => 'root',
        'name' => 'Blog',
        'permanent' => 1,
    ),   
    
    //
    // REFERENCES
    //
    'references' => array(
        'parent' => 'root',
        'name' => 'Referencie',
        'permanent' => 1,
    ),   
    
    //
    // DEALERSHIPMENU
    //
    'dealershipmenu' => array(
        'parent' => 'root',
        'name' => 'Menu obchodných zastúpení',
        'permanent' => 1,
    ),    
    
    //
    // SOCIALNETWORKSMENU
    //
    'socialnetworksmenu' => array(
        'parent' => 'root',
        'name' => 'Menu sociálnych sietí',
        'permanent' => 1,
    ),    
);
