<?php
/**
 * ATTENTION: This is just template config file! 
 * Copy this file into app/config/database.php and set your DB config params there!
 * 
 * NOTE: For DB export/import you can use following commands (both Windows & Linux):
 * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
 * 
 * ON LOCALHOST:
 * - - - - - - -
 * 1] UTF8 SAFE EXPORT:
 * nice mysqldump -u [dbuser] -p'[dbpwd]' [dbname] -r dumpfile.sql
 * nice mysqldump -u [dbuser] -p'[dbpwd]' [dbname] [table1 table2] -r dumpfile.sql
 *
 * 2] REMOVE TRIGGERS DEFINER (linux command):
 * sed 's/DEFINER=`[^`]*`@`[^`]*`//g' dumpfile.sql > changed_dumpfile.sql
 *
 * 3] UTF8 SAFE IMPORT:
 * mysql -u [dbuser] -p'[dbpwd]' --default-character-set=utf8 -D [dbname]
 * mysql> SOURCE changed_dumpfile.sql
 * 
 * IN DOCKER ON LOCALHOST:
 * - - - - - - - - - - - -
 * In case of local docker/devcontainer the `[dbuser]`, `[dbpwd]` and `[dbname]`
 * are preset and the host is `db` (see docker-compose.yml)
 * 
 * 1] UTF8 SAFE EXPORT:
 * nice mysqldump -u root -proot -h db project -r dumpfile.sql
 * nice mysqldump -u root -proot -h db project [table1 table2] -r dumpfile.sql
 * 
 * 2] REMOVE TRIGGERS DEFINER (linux command):
 * sed 's/DEFINER=`[^`]*`@`[^`]*`//g' dumpfile.sql > changed_dumpfile.sql
 *
 * 3] UTF8 SAFE IMPORT:
 * mysql -u root -proot -h db --default-character-set=utf8 -D project
 * mysql> SOURCE changed_dumpfile.sql
 * 
 * 
 * If you need to change table name in dumpfile use following command:
 * sed 's/`my_table`/`_my_table_changed`/g' dumpfile.sql > changed_dumpfile.sql
 * 
 * If you have a MySQL dumpfile containing multiple databases, and you want 
 * to restore a single database from that file, you can use the following command
 * (replace "my_database" by name of the database to be extracted):
 * gunzip -c multiple_db_dumpfile.sql.gz | sed -n -e '/^CREATE DATABASE.*`my_database`/,/^CREATE DATABASE/ p' | sed -e '$d' > my_database.sql
 * 
 * Databases backups on web7.run.sk are stored in /var/backup/databases-{YYYYMMDD}.sql.gz
 * These dumpfiles contain all databases from web7.
 * 
 * To migrate DB from web4.run.sk to web?.run.sk launch on server web?.run.sk folowing:
 * ssh web4.run.sk 'mysqldump -u root source_db_name | gzip' | gunzip -c | sed 's/DEFINER=`[^`]*`@`[^`]*`//g' | mysql -u root target_db_name
 */
// in docker on localhost or running test units from docker image console
if (IN_DOCKER === 'onLocalhost') {
    $dbConfig = array(
        'default' => array (
            // use 'host' => 'host.docker.internal' to use localhost DB.
            // In such a case you have to change credentials accordingly.
            'host' => 'db', 
            'username' => 'root',
            'password' => 'root',
            'database' => 'project',
            'encoding' => 'utf8',
            'port' => null,
            'socket' => null,
            /**
             * Max time interval (in seconds) a table or process can be reserved. 
             * After this interval any table or process reservations are ignored (removed). 
             * This serves to avoid deadlocks caused by orphaned reservations caused
             * by DB server outage or by php scripts (with reservations) which are teminated 
             * because memory limit is exceeded and in such a case App::shutdown() handler
             * is not executed (as the last place where script reservations are cleaned up).
             * If empty then table reservations cleaning is not done.
             * 
             * ATTENTION: Set this according to the longest possible processing time on 
             * actual project.
             * 
             * @var NULL|int 
             */
            'tableReservationTimeLimit' => 600,
        ),
        'test' => array (
            'host' => 'db',
            'username' => 'root',
            'password' => 'root',
            'database' => 'project_test',
            'encoding' => 'utf8',
            'port' => null,
            'socket' => null,
            'tableReservationTimeLimit' => null,
        ),
    );
}
// on localhost or running test units from console
elseif(ON_LOCALHOST) {    
    $dbConfig = array(
        'default' => array (
            'host' => 'localhost',
            'username' => 'root',
            'password' => 'root',
            'database' => 'my_local_db',
            'encoding' => 'utf8',
            'port' => null,
            'socket' => null,
            /**
             * Max time interval (in seconds) a table or process can be reserved. 
             * After this interval any table or process reservations are ignored (removed). 
             * This serves to avoid deadlocks caused by orphaned reservations caused
             * by DB server outage or by php scripts (with reservations) which are teminated 
             * because memory limit is exceeded and in such a case App::shutdown() handler
             * is not executed (as the last place where script reservations are cleaned up).
             * If empty then table reservations cleaning is not done.
             * 
             * ATTENTION: Set this according to the longest possible processing time on 
             * actual project.
             * 
             * @var NULL|int 
             */
            'tableReservationTimeLimit' => 600,
        ),
        'test' => array (
            'host' => 'localhost',
            'username' => 'root',
            'password' => 'root',
            'database' => 'my_local_db_for_tests',
            'encoding' => 'utf8',
            'port' => null,
            'socket' => null,
            'tableReservationTimeLimit' => null,
        ),
    );
}
// on production server
else {
    $dbConfig = array(
        'default' => array (
            'host' => 'localhost',
            'username' => 'my_production_db_user',
            'password' => '???',
            'database' => 'my_production_db',
            'encoding' => 'utf8',
            'port' => null,
            'socket' => null,
            'tableReservationTimeLimit' => 600,
        ),
        'test' => array (
            'host' => 'localhost',
            'username' => 'my_production_db_user',
            'password' => '???',
            'database' => 'my_production_db_for_tests',
            'encoding' => 'utf8',
            'port' => null,
            'socket' => null,
            'tableReservationTimeLimit' => null,
        ),
    );
}
