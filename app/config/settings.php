<?php
/**
 * Module settings initialization. Use following array structure:
 * 
 *      array(
 *          '{pid}' => array(
 *              'value' => '{settingValue}', 
 *              'label' => '{settingLabel}', 
 *              'description' => '{settingDescription}', 
 *              'js_visible' => '{settingJsVisible}',
 *          )
 *      )
 * 
 * Use '/mvc/App/Tools/updateSettings/{Module}' to load settings into DB
 */ 
$settings = array(
    'name' => array(
        'value' => 'MojWeb.sk', 
        'label' => 'Názov webu', 
        'description' => 'Jednotne zadefinovaný názov webu pre použitie na stránkach a v emailoch',
        'js_visible' => 0,
    ),
    'announcement.title' => array(
        'value' => '',
        'label' => 'Oznámenie - nadpis', 
        'description' => 'Nadpis oznámenia, ktoré sa zobrazí v hlavičke webu',
        'js_visible' => 0,
    ),
    'announcement.text' => array(
        'value' => '',
        'label' => 'Oznámenie - text', 
        'description' => 'Text oznámenia, ktoré sa zobrazí v hlavičke webu',
        'js_visible' => 0,
    ),
    'announcement.textIsHidden' => array(
        'value' => '1',
        'label' => 'Oznámenie - text je skrytý', 
        'description' => 'Text oznámenia je na začiatku skrytý?',
        'js_visible' => 0,
    ),
    'announcement.homePageOnly' => array(
        'value' => '1',
        'label' => 'Oznámenie - len na úvodnej stránke', 
        'description' => 'Má byť oznámenie zobrazené len na úvodnej stránke?',
        'js_visible' => 0,
    ),
    'messages.timeout' => array(
        'value' => '5',
        'label' => 'App messages timeout',
        'description' => 'Timeout after which the application messages are autoclosed. Leave this setting empty to turn off autoclosing',
        'js_visible' => 0,
    ),
    'smtp.host' => array(
        'value' => '',
        'label' => 'SMTP server',
        'description' => '',
        'js_visible' => 0,
    ),
    'smtp.port' =>  array(
        'value' => '',
        'label' => 'Port pripojenia k SMTP serveru',
        'description' => '
            Porty pre SSL/TLS-zabezpecenu komunikaciu: POP3s port 995, SMTPs port 465, IMAPs port 993
            Porty pre nezabezpecenu komunikaciu POP3 port 110, SMTP port 25, IMAP port 143',
        'js_visible' => 0,
    ),
    'smtp.username' =>  array(
        'value' => '',
        'label' => 'Uživateľské meno pripojenia k SMTP serveru',
        'description' => '',
        'js_visible' => 0,
    ),
    'smtp.password' =>  array(
        'value' => '',
        'label' => 'Heslo SMTP servera',
        'description' => '',
        'js_visible' => 0,
    ),
    'smtp.encryption' =>  array(
        'value' => '',
        'label' => 'Zabezpečenie pripojenia k SMTP serveru',
        'description' => 'Zabezpečenie pripojenia k SMTP serveru. Možné hodnoty su SSL, TLS alebo prázdne',
        'js_visible' => 0,
    ),
    'email.from' =>  array(
        'value' => '',
        'label' => 'Hlavný odosielateľ',
        'description' => 'Hlavný odosielateľ aplikácie použitý ako default hodnota v App::sendMessage()',
        'js_visible' => 0,
    ),
    'email.to' =>  array(
        'value' => '',
        'label' => 'Hlavný príjemca',
        'description' => 'Hlavný príjemca aplikácie kam sa odosielajú napr. kontaktné formuláre',
        'js_visible' => 0,
    ),
    'email.cc' =>  array(
        'value' => '',
        'label' => 'Príjemca v kópii',
        'description' => 'Príjemca v kópii aplikácie kam sa odosielajú napr. objednávkové maily',
        'js_visible' => 0,
    ),
    'email.bcc' =>  array(
        'value' => '',
        'label' => 'Príjemca v skrytej kópii',
        'description' => 'Príjemca v skrytej kópii aplikácie kam sa odosielajú napr. objednávkové maily',
        'js_visible' => 0,
    ),
    'smartsms.username' => array(
        'value' => '',
        'label' => 'SmartSMS Username',
        'description' => 'Užívateľské meno pre rozhranie odosielania SMS správ prostredníctvom smartsms.sk',
        'js_visible' => 0,
    ),
    'smartsms.password' => array(
        'value' => '',
        'label' => 'SmartSMS Password',
        'description' => 'Heslo pre rozhranie odosielania SMS správ prostredníctvom smartsms.sk',
        'js_visible' => 0,
    ),
    'smartsms.from' => array(
        'value' => '421xxxxxxxxx',
        'label' => 'SmartSMS Mob. číslo odosielateľa',
        'description' => 'Mobilné číslo odosielateľa, z ktorého sa odošle SMS prostredníctvom rozhrania na odosielania SMS správ - smartsms.sk',
        'js_visible' => 0,
    ),
    'attributeValuesSeparator' => array(
        'value' => ';',
        'label' => 'Oddeľovač hodnôt selektívnych atribútov',
        'description' => 'Znak použitý ako oddeľovač hodnôt selektívnych atribútov.',
        'js_visible' => 0,
    ),
    'attributePricesSeparator' => array(
        'value' => ':',
        'label' => 'Oddeľovač cien nacenených selektívnych atribútov',
        'description' => 'Znak použitý ako oddeľovač cien v hodnotách nacenených selektívnych atribútoch.',
        'js_visible' => 0,
    ),    
    'attributePidsSeparator' => array(
        'value' => '=',
        'label' => 'Oddeľovač pid-ov selektívnych atribútov',
        'description' => 'Znak použitý ako oddeľovač pid-ov v hodnotách selektívnych atribútoch.',
        'js_visible' => 0,
    ),    
    'liveChat.account' => array(
        'value' => '',
        'label' => 'Konto pre livechat',
        'description' => 'Konto pre livechat',
        'js_visible' => 0,
    ), 
    'seo.defaultTitle' => array(
        'value' => '',
        'label' => 'Východzí SEO titulok',
        'description' => '',
        'js_visible' => 0,
    ),
    'seo.defaultKeywords' => array(
        'value' => '',
        'label' => 'Východzie SEO kľúčové slová',
        'description' => '',
        'js_visible' => 0,
    ),
    'seo.defaultDescription' => array(
        'value' => '',
        'label' => 'Východzí SEO popis',
        'description' => '',
        'js_visible' => 0,
    ),
    'seo.titlePrefix' => array(
        'value' => '',
        'label' => 'Predpona SEO titulku',
        'description' => 'Text ktorý sa automaticky pridá pred každý SEO titulok',
        'js_visible' => 0,
    ),
    'seo.titleSuffix' => array(
        'value' => '',
        'label' => 'Prípona SEO titulku',
        'description' => 'Text ktorý sa automaticky pridá za každý SEO titulok',
        'js_visible' => 0,
    ),
    'itemselector.maxItemsToShow' => array(
        'value' => 100,
        'label' => 'ItemSelector - max items to show',
        'description' => 'zoznam dostupných položiek v itemselectore sa zobrazí'
        . 'len vtedy, ak počet odfltrovaných položiek neprekročí túto hodnotu'
        . ' settingu',
        'js_visible' => false
    ),
    'company.address' => array(
        'value' => '',
        'label' => 'Company address',
        'description' => '',
        'js_visible' => 0,
    ),
    'company.gpsLatitude' => array(
        'value' => '',
        'label' => 'Company GPS latitude',
        'description' => '',
        'js_visible' => 0,
    ),
    'company.gpsLongitude' => array(
        'value' => '',
        'label' => 'Company GPS longitude',
        'description' => '',
        'js_visible' => 0,
    ),
    'company.email' => array(
        'value' => '<EMAIL>',
    ),
    'company.phone' => array(
        'value' => '0948 202 500',
    ),
    'company.openingHours' => array(
        'value' => 'PO-PIA 7:00-15:30',
    ),
    'home.video' => array(
        'value' => '',
        'label' => 'Homevideo link',
        'description' => '',
        'js_visible' => 0,
    ),
    'home.videoTitle' => array(
        'value' => '',
        'label' => 'Homevideo title',
        'description' => '',
        'js_visible' => 0,
    ),
    'slider.effect' => array(
        'value' => 'cube',
        'label' => 'Slider effect',
        'description' => '',
        'js_visible' => 0,
    ),
    'slider.transitionTime' => array(
        'value' => 2,
        'label' => 'Slider effect transition time',
        'description' => 'Slider effect transition time in seconds',
        'js_visible' => 0,
    ),
    'slider.slideTime' => array(
        'value' => 2.5,
        'label' => 'Slider slide time',
        'description' => 'Slider slide time in seconds',
        'js_visible' => 0,
    ),
    'slider.autoPlay' => array(
        'value' => 1,
        'label' => 'Slider auto play',
        'description' => 'Should the slider start automatically?',
        'js_visible' => 0,
    ),
    'slider.stopOnHover' => array(
        'value' => 1,
        'label' => 'Slider stop on hover',
        'description' => 'Should the slider stop when hovered by mouse?',
        'js_visible' => 0,
    ),
    // custom html/js/css code to be placed at the beginning of html <head> tag
    // mostly used to injest analytic js codes
    'customCode.htmlHead' => array(
        'value' => null,
    ),
    // custom html/js/css code to be placed at the beginning of html <body> tag
    // mostly used to injest analytic js codes
    'customCode.htmlBodyStart' => array(
        'value' => null,
    ),
    // custom html/js/css code to be placed at the end of html <body> tag
    // mostly used to injest analytic js codes
    'customCode.htmlBodyEnd' => array(
        'value' => null,
    ),
    // Code used to generate google analytics code and google ecommerce code
    'google.analyticsCode' => array(
        'value' => null,
        'label' => 'Google analytics code',
        'description' => 'Google analytics code used by element App.googleAnalytics',
        'js_visible' => 0,
    ),
    // Code used to generate google tag manager code, used in element App.googleTagManager
    'google.tagManagerCode' => array(
        'value' => null,
    ),
    'google.apiKey' => array(
        'value' => '',
        'label' => 'Google API key',
        'description' => 'Google API key. Used e.g. by element App.googleMap. The key must have enabled all google service APIs it is used for on actual website. See https://console.developers.google.com for general administration of API keys or see https://developers.google.com/maps/documentation/javascript/get-api-key#key to get key with Google Maps JavaScript API enabled',
        'js_visible' => 0,
    ),
    // Keep the default value of google API key separately from google.apiKey as
    // it is quite sensible info and better to not show it in administration of settings
    'google.defaultApiKey' => array(
        //'value' => 'AIzaSyCNfymBfOYLLSXlbZEmZgrJ8MdqmrT-gF8', // Run default API Key with enabled "Google Maps JavaScript API"
        'value' => 'AIzaSyAzv_SeXSYfiuTKXZkjapJ7veJHnzMnvBg', // Igor > B-mat.cz API Key with enabled "Google Maps JavaScript API". This key has unlimites usage of "Map loads per day".
        'label' => 'Default google API key',
        'description' => 'Google API key. Used e.g. by element App.googleMap. The key must have enabled all google service APIs it is used for on actual website. See https://console.developers.google.com for general administration of API keys or see https://developers.google.com/maps/documentation/javascript/get-api-key#key to get key with Google Maps JavaScript API enabled',
        'js_visible' => 0,
    ),
    'Comment.onlyByRegisteredUsers' => array(
        'value' => 0,
        'label' => 'Comments only by registered',
        'description' => 'If 1 then comments can be created only by registered users',
        'js_visible' => 0,
    ),
    'Comment.autoApprove' => array(
        'value' => 1,
        'label' => 'Autoapprove comments',
        'description' => 'If 1 then all submitted comments are published',
        'js_visible' => 0,
    ),
    'Comment.allowRatings' => array(
        'value' => 1,
        'label' => 'Allow ratings',
        'description' => 'Are ratings allowed on comments creation?',
        'js_visible' => 0,
    ),
    'cookiesPrivacyPolicyInfo' => array(
        'value' => 'Táto stránka používa cookies. Údaje v nich uložené sa používajú jednak na bežné fungovanie stránky (napr. prihlásenie) a jednak nám pomáhajú poskytovať lepšie služby. Niektoré údaje v cookies zdieľame aj s tretími stranami (napr. pri vyhodnocovaní návštevnosti). Používaním našej stránky súhlasíte s využitím cookies.',
        'label' => 'Text infa o používaní cookies',
        'description' => '',
        'js_visible' => 0,
    ),    
    'cookiesPrivacyPolicyInfoPosition' => array(
        'value' => 'fixedAtBottom', // 'scrollableAtTop' | 'fixedAtBottom'
        'label' => 'Umiestnenie infa o používaní cookies',
        'description' => '',
        'js_visible' => 0,
    ),  
    // @deprecated - use setting App.customCode.html??? / App.element html???CustomCode instead
    // name of luigisbox analytic script name, e.g. LBX-12345.js
    'luigisBox.jsScriptName' => array(
        'value' => '',
    ),    
    'facebook.appId' => array(
        'value' => '',
    ),
    // default site image used for shared content on facebook
    'facebook.defaultImage' => array(
        'value' => '',
    ),
    // used internally to store data of Tools::viewEshopOrdersStatistics()
    'Tools.viewEshopOrdersStatistics.data' => array(
        'value' => '',
    ),
    'meilisearch.apiUrlBase' => array(
        'value' => '',
    ),
    'meilisearch.apiKey' => array(
        'value' => '',
    ),
    //
    // PROJECT SPECIFIC
    //
    'headerText' => array(
        'value' => 'Vaše miesto na Turci',
        'label' => 'Text displayed in header',
        'description' => 'Text displayed in header',
        'js_visible' => 0,
    ),
    'footerText' => array(
        'value' => 'Pätičkový text',
        'label' => 'Text displayed in footer',
        'description' => 'Text displayed in footer',
        'js_visible' => 0,
    ),
    'menuAnnouncement.text' => array(
        'value' => '',
        'label' => 'Text oznámenia',
        'description' => 'Text, ktorý sa zobrazí pod hlavným menu',
        'js_visible' => 0,
    ),
    'menuAnnouncement.active' => array(
        'value' => '',
        'label' => 'Aktívny',
        'description' => 'Zobrazovanie oznámenia',
        'js_visible' => 0,
    ),
    'menuAnnouncement.lottery' => array(
        'value' => '',
        'label' => 'Hra',
        'description' => 'Zobrazenie ikony kolesa šťastia',
        'js_visible' => 0,
    ),
    'menuAnnouncement.locator' => array(
        'value' => '',
        'label' => 'URL oznámenia',
        'description' => 'URL na vlastnú stránku oznámenia',
        'js_visible' => 0,
    ),
    'menuAnnouncement.background' => array(
        'value' => '#DCAF0E\'',
        'label' => 'Farba pozadia',
        'description' => 'Farba pozadia oznámenia',
        'js_visible' => 0,
    ),
);
