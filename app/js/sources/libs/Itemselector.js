requireDefined([
    'jQuery',
    'jQuery.fn.sortable' // jQueryUI sortable
], 
'Run.App.Itemselector');

loadNamespace('Run.App');

/**
 * Class Run.App.Itemselector
 */

Run.App.Itemselector = (function() {
    
    /**
     * Constructor
     * 
     * @param {String|Element|jQuery} selector
     * @param {Object} options
     * @returns {Run.App.Itemselector}
     */
    function Itemselector (options) {   
        this.init(options);
    };
    
    //
    // OPTIONS PROPERTIES
    //

    Itemselector.prototype.options = {
                
        /**
         * Unique id of html input element
         * 
         * @type {String}
         */
        inputId: '',
                
        /**
         * Url to load data via ajax
         * 
         * @type {String}
         */
        remoteUrl: '',
                
        /**
         * Count of max items to show list. Elsewhere itemselector shows only 
         * info message, that count of items is too big
         * 
         * 
         * @type {String}
         */
        maxItemsToShow: '',
                
        /**
         * CSS class of contected containers for drag and drop functionality
         * 
         * @type {String}
         */
        connectedClass: 'connectedSortable',
                
        /**
         * This text appears, when count of filtered items is too big
         * 
         * @type {String}
         */
        messageTooManyItems: 'Too many items to display (%s). Specify selection.'
        
    };

    //
    // RUNTME PROPERTIES
    //
    
    //
    // METHODS
    //
        
    /**
     * 
     * @param {Object} options 
     */
    Itemselector.prototype.init = function(options) {    
        var me = this;
    
        // apply provided options to default options
        this.options = jQuery.extend({}, this.options, jQuery.extend(true, {}, options));

        jQuery("#sortable2-" + this.options.inputId).sortable({
            connectWith: "." + this.options.connectedClass
        }).disableSelection();
        
        jQuery("#sortable1-" + this.options.inputId).sortable({
            connectWith: "." + this.options.connectedClass,
            update: function(event, ui) {
                var inputValues = [];
                // collect selected values to array
                jQuery.each(jQuery(this).children('li'), function(inputItemIndex, inputItem) {
                    inputValues[inputItemIndex] = jQuery(inputItem).attr('data-value');
                });
                // update hidden input with ; separated values and trigger change
                // event (the update of value itself does not trigger event)
                jQuery('#' + me.options.inputId).val(inputValues.join(';'));
                jQuery('#' + me.options.inputId).trigger('change');
            }
        }).disableSelection();
        
        jQuery("#search-button-" + this.options.inputId).click(function() {
            me.loadListFromAjax(me);
        });

        jQuery("#search-" + this.options.inputId).keypress(function(e) {
            var code = e.which;
            if (code == 13) {
                e.preventDefault();
                me.loadListFromAjax(me);
            }
        });
    };
    
    Itemselector.prototype.loadListFromAjax = function(me) {

        // clear old list
        jQuery("#sortable2-" + me.options.inputId).html('');
        var keyword = jQuery("#search-" + me.options.inputId).val();
        var url = me.options.remoteUrl;
        var data = {};
        /*/> Even space makes sometime (e.g. on localhost) problem in URL, so use just the GET param version.
        // If there is a slash in keyword then we must send the keyword to the server as 
        // a GET parameter. It is because the slash would be interpereted as a part of URL path and the
        // keyword would be broken into parts by included slash(es). More over it is
        // not possible to use encoded slash (%2F) as such URLs are ignored by Apache (404 Not found),
        // see https://httpd.apache.org/docs/2.4/mod/core.html#allowencodedslashes
        if (keyword.indexOf('/') !== -1) {
            data.keyword = keyword;
        }
        else {
            url = url + '/' + keyword;
        }
        /*/
        data.keyword = keyword;
        //*/
        jQuery.ajax({
            url: url,
            data: data
        }).done(function(jsonData) {
            var data = jQuery.parseJSON(jsonData);
            if (data.total <  + me.options.maxItemsToShow) {
                jQuery.each(data.data, function(itemValue, itemLabel) {
                    jQuery("#sortable2-" + me.options.inputId)
                    .append('<li class="ui-state-default panel panel-default" data-value="' + itemValue + '">' + itemLabel + '</li>');
                });
                jQuery('#search-message-' + me.options.inputId).html('');
            } else {
                jQuery('#search-message-' + me.options.inputId).html(me.options.messageTooManyItems.replace('%s', data.total));
            }
        });
    };
        
    return Itemselector;
})();
