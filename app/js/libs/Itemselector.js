requireDefined(["jQuery","jQuery.fn.sortable"],"Run.App.Itemselector"),loadNamespace("Run.App"),Run.App.Itemselector=function(){function t(t){this.init(t)}return t.prototype.options={inputId:"",remoteUrl:"",maxItemsToShow:"",connectedClass:"connectedSortable",messageTooManyItems:"Too many items to display (%s). Specify selection."},t.prototype.init=function(t){var n=this;this.options=jQuery.extend({},this.options,jQuery.extend(!0,{},t)),jQuery("#sortable2-"+this.options.inputId).sortable({connectWith:"."+this.options.connectedClass}).disableSelection(),jQuery("#sortable1-"+this.options.inputId).sortable({connectWith:"."+this.options.connectedClass,update:function(t,e){var o=[];jQuery.each(jQuery(this).children("li"),function(t,e){o[t]=jQuery(e).attr("data-value")}),jQuery("#"+n.options.inputId).val(o.join(";")),jQuery("#"+n.options.inputId).trigger("change")}}).disableSelection(),jQuery("#search-button-"+this.options.inputId).click(function(){n.loadListFromAjax(n)}),jQuery("#search-"+this.options.inputId).keypress(function(t){13==t.which&&(t.preventDefault(),n.loadListFromAjax(n))})},t.prototype.loadListFromAjax=function(o){jQuery("#sortable2-"+o.options.inputId).html("");var t=jQuery("#search-"+o.options.inputId).val(),e=o.options.remoteUrl,n={};n.keyword=t,jQuery.ajax({url:e,data:n}).done(function(t){var e=jQuery.parseJSON(t);e.total<+o.options.maxItemsToShow?(jQuery.each(e.data,function(t,e){jQuery("#sortable2-"+o.options.inputId).append('<li class="ui-state-default panel panel-default" data-value="'+t+'">'+e+"</li>")}),jQuery("#search-message-"+o.options.inputId).html("")):jQuery("#search-message-"+o.options.inputId).html(o.options.messageTooManyItems.replace("%s",e.total))})},t}();