requireDefined(["jQuery"],"Run.App.Timeline"),loadNamespace("Run.App"),Run.App.Timeline=function(){function t(t){this.init(t)}return t.prototype.options={selector:".timeline",Window:window,maxGap:30,minPxPerYear:35},t.prototype.Axis=null,t.prototype.Events=null,t.prototype.LastEvent=null,t.prototype.init=function(t){if(this.options=jQuery.extend({},this.options,jQuery.extend(!0,{},t)),this.Axis=jQuery(this.options.selector+" .axis",this.options.Window.document),0===this.Axis.length)throw new Error("Invalid timeline selector. No axis found.");if(1<this.Axis.length)throw new Error("Many timeline axis has been found accoding to provided selector "+this.options.selector);this.Events=jQuery(".event",this.Axis),this.LastEvent=jQuery(this.options.selector+" .event-last",this.options.Window.document),this.setEventsPositions(),this.registerClickEvent(),this.registerAnimation()},t.prototype.setEventsPositions=function(){var t,e,s,o,i,n,r,h,a,p,l,u,d,f,c,y,v=this;if(e=parseInt(this.Events.first().find(".event-date .arrow").html().substr(0,4)),(s=parseInt(this.Events.last().find(".event-date .arrow").html().substr(0,4)))<=e)throw new Error("Invalid years order in timeline "+this.options.selector);(t=Math.round((this.Events.last().offset().top-this.Events.first().offset().top)/(s-e)))<this.options.minPxPerYear&&(t=this.options.minPxPerYear),n=this.Events.first().offset().top+this.Events.first().outerHeight(),r=e,h=this.Events.first().find(".event-date").offset().top,this.Events.each(function(){p=jQuery(this),l=p.find(".event-date"),o=parseInt(l.find(".arrow").html().substr(0,4)),i=l.offset().top,0<(a=(o-r)*t+h-i)&&(u=parseInt(l.css("top")),d=l.outerHeight(),f=p.outerHeight(),a+u+d<f-5?l.css("top",a+u+"px"):((y=i+(c=a+u+d-(f-5))-n)>v.options.maxGap&&(c=c-y+v.options.maxGap,a=a-y+v.options.maxGap),p.css("margin-top",c+"px"),l.css("top",a+u-c+"px"))),n=p.offset().top+p.outerHeight(),r=o,h=l.offset().top})},t.prototype.registerClickEvent=function(){var e=this;this.Events.on("click",function(t){jQuery(this).outerHeight()>jQuery(window).height()?e.scrollToTop(this):e.scrollToCenter(this),t.preventDefault()}),this.LastEvent.on("click",function(t){jQuery(this).outerHeight()>jQuery(window).height()?e.scrollToTop(this):e.scrollToCenter(this),t.preventDefault()})},t.prototype.scrollToTop=function(t){jQuery("html,body").animate({scrollTop:jQuery(t).offset().top},"slow")},t.prototype.scrollToCenter=function(t){jQuery("html,body").animate({scrollTop:jQuery(t).offset().top-Math.round((jQuery(window).height()-jQuery(t).outerHeight())/2)},"slow")},t.prototype.registerAnimation=function(){var t=this;t.Events.each(function(){jQuery(this).offset().top>jQuery(window).scrollTop()+.75*jQuery(window).height()&&jQuery(this).addClass("hidden")}),t.LastEvent.offset().top>jQuery(window).scrollTop()+.75*jQuery(window).height()&&t.LastEvent.addClass("hidden"),jQuery(window).on("scroll",function(){t.Events.each(function(){jQuery(this).offset().top<=jQuery(window).scrollTop()+.75*jQuery(window).height()&&(jQuery(this).hasClass("hidden")||jQuery(this).hasClass("hide"))&&jQuery(this).removeClass("hidden").removeClass("hide").addClass("show")}),t.LastEvent.offset().top<=jQuery(window).scrollTop()+.85*jQuery(window).height()&&(t.LastEvent.hasClass("hidden")||t.LastEvent.hasClass("hide"))&&t.LastEvent.removeClass("hidden").removeClass("hide").addClass("bounce")})},t}();