requireDefined(["jQuery"],"Run.App.ChildsAccordion"),loadNamespace("Run.App"),Run.App.ChildsAccordion=function(){function o(o){this.init(o),this.setInstance()}return o.prototype.options={selector:".childs-accordion",Window:window,openSpeed:"slow",closeSpeed:"fast",allowScrollTop:!0,scrollTopSpeed:"slow"},o.prototype.Accordion=null,o.prototype.Navigation=null,o.prototype.Links=null,o.prototype.Items=null,o.prototype.FixedItems=null,o.prototype.openFunctionName="slideDown",o.prototype.closeFunctionName="slideUp",o.prototype.init=function(o){var t,e;if(this.options=jQuery.extend({},this.options,jQuery.extend(!0,{},o)),this.options.openSpeed||(this.openFunctionName="show"),this.options.closeSpeed||(this.closeFunctionName="hide"),this.options.allowScrollTop||(this.options.scrollTopSpeed=0),this.Accordion=jQuery(this.options.selector,this.options.Window.document),0===this.Accordion.length)throw new Error("Invalid childs accordion selector. No accordion found.");if(1<this.Accordion.length)throw new Error("Many childs accordions has been found accoding to provided selector "+this.options.selector);this.Navigation=jQuery("nav",this.Accordion),this.Links=jQuery("a",this.Navigation),this.Items=jQuery(".accordion-item",this.Accordion),1<(t=this.options.Window.location.href.split("#")).length&&(e=t[1],this.openItem(e,!0)),this.registerToggleEvent()},o.prototype.registerToggleEvent=function(){var e=this;this.Links.on("click",function(o){var t=jQuery(this).attr("href").substr(1);e.openItem(t),o.preventDefault()})},o.prototype.openItem=function(o,t){var e,i,n,s,c=this,r=jQuery('a[href="#'+o+'"]',this.Navigation),p=jQuery('a[name="'+o+'"]',this.Items);if(0!==p.length){if(0!==(e=jQuery("li.active",this.Navigation)).length){if((i=jQuery("a",e).attr("href").substr(1))===o)return;e.removeClass("active"),(s=jQuery(".accordion",this.Accordion)).css("min-height",s.height()+"px"),n=jQuery('a[name="'+i+'"]',this.Items),c.options.allowScrollTop||t?jQuery("html, body",c.options.Window.document).animate({scrollTop:c.getScrollTop()},{duration:c.options.scrollTopSpeed,complete:function(){n.closest(".accordion-item")[c.closeFunctionName](c.options.closeSpeed,function(){p.closest(".accordion-item")[c.openFunctionName](c.options.openSpeed,function(){s.css("min-height",0)})})}}):n.closest(".accordion-item")[c.closeFunctionName](c.options.closeSpeed,function(){p.closest(".accordion-item")[c.openFunctionName](c.options.openSpeed,function(){s.css("min-height",0)})})}else c.options.allowScrollTop||t?jQuery("html, body",c.options.Window.document).animate({scrollTop:c.getScrollTop()},{duration:c.options.scrollTopSpeed,complete:function(){p.closest(".accordion-item")[c.openFunctionName](c.options.openSpeed)}}):p.closest(".accordion-item")[c.openFunctionName](c.options.openSpeed);r.closest("li").addClass("active")}},o.prototype.getFixedHeight=function(o){var e=0;return this.FixedItems&&!o||(this.FixedItems=jQuery("*").filter(function(){var o,t,e=jQuery(this);return"fixed"===e.css("position")&&(o=e.css("top")).match(/[0-9]/)&&(!(t=e.css("bottom")).match(/[0-9]/)||parseInt(o)<parseInt(t))})),this.FixedItems.length&&this.FixedItems.each(function(){var o=parseInt(jQuery(this).css("top")),t=jQuery(this).outerHeight();e<o+t&&(e=o+t)}),e},o.prototype.getScrollTop=function(){var o=this.getFixedHeight(!0);return this.Navigation.offset().top-o},o.prototype.hasItem=function(o){return 0!==jQuery('a[name="'+o+'"]',this.Items).length},o.prototype.setInstance=function(){loadNamespace("instance",Run.App.ChildsAccordion,this)},o.getInstance=function(o){return isDefined("instance",Run.App.ChildsAccordion)?Run.App.ChildsAccordion.instance:new this(o)},o}();