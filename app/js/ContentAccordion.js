requireDefined(["jQuery"],"Run.App.ContentAccordion"),loadNamespace("Run.App"),Run.App.ContentAccordion=function(){function o(o){this.init(o),this.setInstance()}return o.prototype.options={selector:".content-accordion",Window:window,openSpeed:"slow",closeSpeed:"slow"},o.prototype.Accordion=null,o.prototype.Heading=null,o.prototype.Content=null,o.prototype.anchorName=null,o.prototype.init=function(o){var n;if(this.options=jQuery.extend({},this.options,jQuery.extend(!0,{},o)),this.Accordion=jQuery(this.options.selector,this.options.Window.document),0===this.Accordion.length)throw new Error("Invalid content accordion selector. No accordion found.");if(1<this.Accordion.length)throw new Error("Many content accordions has been found accoding to provided selector "+this.options.selector);this.Heading=jQuery(".accordion-heading",this.Accordion),this.Content=jQuery(".accordion-content",this.Accordion),this.anchorName=this.Heading.find("a[name]").attr("name"),1<(n=this.options.Window.location.href.split("#")).length&&this.anchorName===n[1]&&this.open(),this.registerToggleEvent()},o.prototype.registerToggleEvent=function(){var n=this;this.Heading.on("click",function(o){n.toggle(),o.preventDefault()})},o.prototype.toggle=function(o){"none"===this.Content.css("display")?this.open(o):this.close()},o.prototype.open=function(o){this.Content.slideDown(this.options.openSpeed,function(){"function"==typeof o&&o()})},o.prototype.close=function(){this.Content.slideUp(this.options.closeSpeed)},o.prototype.setInstance=function(){loadNamespace("instances",Run.App.ContentAccordion)[this.options.selector]=this},o.getInstance=function(o){if(!o.selector)throw new Error("Missing selector option");return isDefined("instances",Run.App.ContentAccordion)&&Run.App.ContentAccordion.instances[o.selector]?Run.App.ContentAccordion.instances[o.selector]:new this(o)},o}();