requireDefined(["jQuery"],"Run.App.ImagePhotogallery"),loadNamespace("Run.App"),Run.App.ImagePhotogallery=function(){function t(t){this.init(t)}return t.prototype.options={selector:".image-photogallery",Window:window},t.prototype.Gallery=null,t.prototype.Image=null,t.prototype.Ribbon=null,t.prototype.Thumbs=null,t.prototype.init=function(t){if(this.options=jQuery.extend({},this.options,jQuery.extend(!0,{},t)),this.Gallery=jQuery(this.options.selector,this.options.Window.document),0===this.Gallery.length)throw new Error("Invalid gallery selector. No gallery found.");if(1<this.Gallery.length)throw new Error("Many galleries has been found accoding to provided selector "+this.options.selector);this.Image=jQuery(".main-image-right img",this.Gallery),this.Ribbon=jQuery(".main-image-right .ribbon",this.Gallery),this.Thumbs=jQuery(".photogallery-image",this.Gallery),this.registerThumbClickEvent()},t.prototype.registerThumbClickEvent=function(){var r=this;this.Thumbs.on("click",function(t){var e=jQuery(this).find("a"),i=e.attr("href"),o=e.attr("title");r.Image.attr("src",i),r.Ribbon.html(o),t.preventDefault()})},t}();