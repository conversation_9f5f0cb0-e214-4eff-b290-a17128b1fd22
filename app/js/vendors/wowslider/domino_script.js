// -----------------------------------------------------------------------------------
// http://wowslider.com/
// JavaScript Wow Slider is a free software that helps you easily generate delicious 
// slideshows with gorgeous transition effects, in a few clicks without writing a single line of code.
// Generated by WOW Slider 6.3
//
//***********************************************
// Obfuscated by Javascript Obfuscator
// http://javascript-source.com
//***********************************************
jQuery.extend(jQuery.easing,{easeInOutSine:function(j,i,b,c,d){return -c/2*(Math.cos(Math.PI*i/d)-1)+b}});function ws_domino(j,g,i){$=jQuery;var f=$(this);jQuery.extend(j,{columns:j.columns||5,rows:j.rows||2,centerRow:j.centerRow||2,centerColumn:j.centerColumn||2});var e=$("<div>").addClass("ws_effect").css({position:"absolute",width:"100%",height:"100%",top:0,overflow:"hidden"}).appendTo(i.parent());var c=$("<div>").addClass("ws_zoom").appendTo(e);var h=$("<div>").addClass("ws_parts").appendTo(e);var d=i.find(".ws_list");var a;function b(o,l,m,k,n){if(j.support.transform&&j.support.transition){if(!l.transform){l.transform=""}if(l.left||l.top){l.transform+=" translate3d("+(l.left?l.left:0)+"px,"+(l.top?l.top:0)+"px,0px)"}if(m){l.transition=m+"ms all "+k+"ms cubic-bezier(0.445, 0.050, 0.550, 0.950)"}delete l.left;delete l.top;o.css(l);if(n){o.on("transitionend webkitTransitionEnd oTransitionEnd MSTransitionEnd",n)}}else{if(n){o.animate(l,m,"easeInOutSine",n)}else{o.css(l)}}}this.go=function(u,t){function v(){h.find("img").stop(1,1);h.empty();c.empty();a=0}v();if(j.fadeOut){d.fadeOut(j.duration)}var o=$(g.get(t));o={width:o.width(),height:o.height(),marginTop:parseFloat(o.css("marginTop")),marginLeft:parseFloat(o.css("marginLeft"))};var y=$(g.get(t)).clone().appendTo(c).css({position:"absolute",top:0,left:0}).css(o);b(y,{transform:"scale(1)"});var m=e.width();var l=e.height();var s=Math.floor(m/j.columns);var r=Math.floor(l/j.rows);var p=m-s*(j.columns-1);var z=l-r*(j.rows-1);function D(G,F){return Math.random()*(F-G+1)+G}var q=[];for(var x=0;x<j.rows;x++){q[x]=[];for(var w=0;w<j.columns;w++){var n=j.duration*(1-Math.abs((j.centerRow*j.centerColumn-x*w)/(2*j.rows*j.columns)));var A=w<j.columns-1?s:p;var k=x<j.rows-1?r:z;q[x][w]=$("<div>").css({width:A,height:k,position:"absolute",top:x*r,left:w*s,overflow:"hidden"});var C=D(x-2,x+2);var B=D(w-2,w+2);q[x][w].appendTo(h);var E=$(g.get(u)).clone().appendTo(q[x][w]).css(o);b(E,{position:"absolute",top:-C*r,left:-B*s,opacity:0},n,0)}}setTimeout(function(){if(j.support.transform&&j.support.transition){b(y,{transform:"scale(1.6)"},j.duration,j.duration*0.2)}for(var G=0;G<j.rows;G++){for(var F=0;F<j.columns;F++){b(q[G][F].find("img"),{top:-G*r,left:-F*s,opacity:1},n,0,function(){a++;if(a==j.rows*j.columns){v();d.stop(1,1);f.trigger("effectEnd")}})}}},0)}};// -----------------------------------------------------------------------------------
// http://wowslider.com/
// JavaScript Wow Slider is a free software that helps you easily generate delicious 
// slideshows with gorgeous transition effects, in a few clicks without writing a single line of code.
// Generated by WOW Slider 6.3
//
//***********************************************
// Obfuscated by Javascript Obfuscator
// http://javascript-source.com
//***********************************************
//jQuery("#wowslider-container1").wowSlider({effect:"domino",prev:"",next:"",duration:20*100,delay:20*100,width:940,height:314,autoPlay:true,autoPlayVideo:false,playPause:true,stopOnHover:false,loop:false,bullets:true,caption:true,captionEffect:"fade",controls:true,responsive:1,fullScreen:false,onBeforeStep:0,images:0});
jQuery("#wowslider-container1").wowSlider(window._wowSliderOptions);