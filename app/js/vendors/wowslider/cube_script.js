// -----------------------------------------------------------------------------------
// http://wowslider.com/
// JavaScript Wow Slider is a free software that helps you easily generate delicious 
// slideshows with gorgeous transition effects, in a few clicks without writing a single line of code.
// Generated by WOW Slider 6.3
//
//***********************************************
// Obfuscated by Javascript Obfuscator
// http://javascript-source.com
//***********************************************
function ws_cube(o,j,a){var d=jQuery,i=d(this),k=(/iPhone|iPod|iPad|Android|BlackBerry/).test(navigator.userAgent),f=d(".ws_list",a),b=o.perspective||2000,c={position:"absolute",backgroundSize:"cover",left:0,top:0,width:"100%",height:"100%",backfaceVisibility:"hidden"};var n={domPrefixes:" Webkit Moz ms O Khtml".split(" "),testDom:function(q){var p=this.domPrefixes.length;while(p--){if(typeof document.body.style[this.domPrefixes[p]+q]!=="undefined"){return true}}return false},cssTransitions:function(){return this.testDom("Transition")},cssTransforms3d:function(){var q=(typeof document.body.style.perspectiveProperty!=="undefined")||this.testDom("Perspective");if(q&&/AppleWebKit/.test(navigator.userAgent)){var s=document.createElement("div"),p=document.createElement("style"),r="Test3d"+Math.round(Math.random()*99999);p.textContent="@media (-webkit-transform-3d){#"+r+"{height:3px}}";document.getElementsByTagName("head")[0].appendChild(p);s.id=r;document.body.appendChild(s);q=s.offsetHeight===3;p.parentNode.removeChild(p);s.parentNode.removeChild(s)}return q},webkit:function(){return/AppleWebKit/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)}};var e=(n.cssTransitions()&&n.cssTransforms3d()),l=n.webkit();var h=d("<div>").addClass("ws_effect").css(c).css({transformStyle:"preserve-3d",perspective:l?"none":b,zIndex:8,overflow:(o.responsive>1?"hidden":"visible")});d("<div>").addClass("ws_effect").css(c).append(h).appendTo(a.parent());if(!e&&o.fallback){return new o.fallback(o,j,a)}function m(p,q,s,r){return"inset "+(-r*p*1.2/90)+"px "+(s*q*1.2/90)+"px "+(p+q)/20+"px rgba("+((s<r)?"0,0,0,.6":(s>r)?"255,255,255,0.8":"0,0,0,.0")+")"}var g;this.go=function(A,x){var s=d(j[x]);s={width:s.width(),height:s.height(),marginTop:parseFloat(s.css("marginTop")),marginLeft:parseFloat(s.css("marginLeft"))};function r(R,E,G,H,F,D,P,Q,O,N){R.parent().css("perspective",b);var M=R.width(),J=R.height();E.front.css({transform:"rotateY(0deg) rotateX(0deg)"});E.back.css({opacity:1,transform:"rotateY("+P+"deg) rotateX("+D+"deg)"});if(!k){var I=d("<div>").css(c).css("boxShadow",m(M,J,0,0)).appendTo(E.front);var L=d("<div>").css(c).css("boxShadow",m(M,J,D,P)).appendTo(E.back)}if(l){R.css({transform:"translateZ(-"+G+"px)"})}var K=setTimeout(function(){var w="all "+o.duration+"ms cubic-bezier(0.645, 0.045, 0.355, 1.000)";E.front.css({transition:w,boxShadow:k?"":m(M,J,Q,O),transform:"rotateX("+Q+"deg) rotateY("+O+"deg)",zIndex:0});E.back.css({transition:w,boxShadow:k?"":m(M,J,0,0),transform:"rotateY(0deg) rotateX(0deg)",zIndex:20});if(!k){I.css({transition:w,boxShadow:m(M,J,Q,O)});L.css({transition:w,boxShadow:m(M,J,0,0)})}K=setTimeout(N,o.duration)},20);return{stop:function(){clearTimeout(K);N()}}}if(e){if(g){g.stop()}var B=a.width(),y=a.height();var v={left:[B/2,B/2,0,0,90,0,-90],right:[B/2,-B/2,0,0,-90,0,90],down:[y/2,0,-y/2,90,0,-90,0],up:[y/2,0,y/2,-90,0,90,0]}[o.direction||["left","right","down","up"][Math.floor(Math.random()*4)]];var C=d("<img>").css(s),u=d("<img>").css(s).attr("src",j.get(A).src);var p=d("<div>").css({overflow:"hidden",transformOrigin:"50% 50% -"+v[0]+"px",zIndex:20}).css(c).append(C).appendTo(h);var q=d("<div>").css({overflow:"hidden",transformOrigin:"50% 50% -"+v[0]+"px",zIndex:0}).css(c).append(u).appendTo(h);C.on("load",function(){f.hide()});C.attr("src",j.get(x).src).load();h.parent().show();g=new r(h,{front:p,back:q},v[0],v[1],v[2],v[3],v[4],v[5],v[6],function(){i.trigger("effectEnd");h.empty().parent().hide();g=0})}else{h.css({position:"absolute",display:"none",zIndex:2,width:"100%",height:"100%"});h.stop(1,1);var t=(!!((A-x+1)%j.length)^o.revers?"left":"right");var p=d("<div>").css({position:"absolute",left:"0%",right:"auto",top:0,width:"100%",height:"100%"}).css(t,0).append(d(j[x]).clone().css({width:100*s.width/a.width()+"%",height:100*s.height/a.height()+"%",marginLeft:100*s.marginLeft/a.width()+"%"})).appendTo(h);var z=d("<div>").css({position:"absolute",left:"100%",right:"auto",top:0,width:"0%",height:"100%"}).append(d(j[A]).clone().css({width:100*s.width/a.width()+"%",height:100*s.height/a.height()+"%",marginLeft:100*s.marginLeft/a.width()+"%"})).appendTo(h);h.css({left:"auto",right:"auto",top:0}).css(t,0).show();h.show();f.hide();z.animate({width:"100%",left:0},o.duration,"easeInOutExpo",function(){d(this).remove()});p.animate({width:0},o.duration,"easeInOutExpo",function(){i.trigger("effectEnd");h.empty().hide()})}}};// -----------------------------------------------------------------------------------
// http://wowslider.com/
// JavaScript Wow Slider is a free software that helps you easily generate delicious 
// slideshows with gorgeous transition effects, in a few clicks without writing a single line of code.
// Generated by WOW Slider 6.3
//
//***********************************************
// Obfuscated by Javascript Obfuscator
// http://javascript-source.com
//***********************************************
//rblb//jQuery("#wowslider-container1").wowSlider({effect:"cube",prev:"",next:"",duration:20*100,delay:20*100,width:940,height:314,autoPlay:true,autoPlayVideo:false,playPause:true,stopOnHover:true,loop:false,bullets:true,caption:true,captionEffect:"parallax",controls:true,responsive:1,fullScreen:false,onBeforeStep:0,images:0});
jQuery("#wowslider-container1").wowSlider(window._wowSliderOptions);