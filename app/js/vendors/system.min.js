/*
* SystemJS 6.3.3
*/
!function(){function e(e,t){return(t||"")+" (SystemJS Error#"+e+" https://git.io/JvFET#"+e+")"}function t(e,t){if(-1!==e.indexOf("\\")&&(e=e.replace(/\\/g,"/")),"/"===e[0]&&"/"===e[1])return t.slice(0,t.indexOf(":")+1)+e;if("."===e[0]&&("/"===e[1]||"."===e[1]&&("/"===e[2]||2===e.length&&(e+="/"))||1===e.length&&(e+="/"))||"/"===e[0]){var n,r=t.slice(0,t.indexOf(":")+1);if(n="/"===t[r.length+1]?"file:"!==r?(n=t.slice(r.length+2)).slice(n.indexOf("/")+1):t.slice(8):t.slice(r.length+("/"===t[r.length])),"/"===e[0])return t.slice(0,t.length-n.length-1)+e;for(var i=n.slice(0,n.lastIndexOf("/")+1)+e,o=[],u=-1,c=0;i.length>c;c++)-1!==u?"/"===i[c]&&(o.push(i.slice(u,c+1)),u=-1):"."===i[c]?"."!==i[c+1]||"/"!==i[c+2]&&c+2!==i.length?"/"===i[c+1]||c+1===i.length?c+=1:u=c:(o.pop(),c+=2):u=c;return-1!==u&&o.push(i.slice(u)),t.slice(0,t.length-n.length)+o.join("")}}function n(e,n){return t(e,n)||(-1!==e.indexOf(":")?e:t("./"+e,n))}function r(e,t){for(var n in t)e[n]=t[n];return e}function i(e,n,r,i,o){for(var u in e){var f=t(u,r)||u,a=e[u];if("string"==typeof a){var l=s(i,t(a,r)||a,o);l?n[f]=l:c("W1",u,a,"bare specifier did not resolve")}}}function o(e,t){if(t[e])return e;var n=e.length;do{var r=e.slice(0,n+1);if(r in t)return r}while(-1!==(n=e.lastIndexOf("/",n-1)))}function u(e,t){var n=o(e,t);if(n){var r=t[n];if(null===r)return;if(n.length>=e.length||"/"===r[r.length-1])return r+e.slice(n.length);c("W2",n,r,"should have a trailing '/'")}}function c(t,n,r,i){console.warn(e(t,"Package target "+i+", resolving target '"+r+"' for "+n))}function s(e,t,n){for(var r=e.scopes,i=n&&o(n,r);i;){var c=u(t,r[i]);if(c)return c;i=o(i.slice(0,i.lastIndexOf("/")),r)}return u(t,e.imports)||-1!==t.indexOf(":")&&t}function f(){this[O]={}}function a(e){return e.id}function l(e,t,n){if(e.onload(n,t.id,t.d&&t.d.map(a)),n)throw n}function d(e,t){g&&[].forEach.call(document.querySelectorAll('script[type="systemjs-importmap"]'+t),e)}function h(){[].forEach.call(document.querySelectorAll("script[type=systemjs-module]"),(function(e){e.src&&System.import("import:"===e.src.slice(0,7)?e.src.slice(7):n(e.src,v))}))}var v,p="undefined"!=typeof Symbol,m="undefined"!=typeof self,g="undefined"!=typeof document,y=m?self:global;if(g){var b=document.querySelector("base[href]");b&&(v=b.href)}if(!v&&"undefined"!=typeof location){var S=(v=location.href.split("#")[0].split("?")[0]).lastIndexOf("/");-1!==S&&(v=v.slice(0,S+1))}var E,x=p&&Symbol.toStringTag,O=p?Symbol():"@",w=f.prototype;w.import=function(t,n){var r=this;return Promise.resolve(r.prepareImport()).then((function(){return r.resolve(t,n)})).then((function(t){var n=function t(n,r,i){var o=n[O][r];if(o)return o;var u=[],c=Object.create(null);x&&Object.defineProperty(c,x,{value:"Module"});var s=Promise.resolve().then((function(){return n.instantiate(r,i)})).then((function(t){if(!t)throw Error(e(2,"Module "+r+" did not instantiate"));var i=t[1]((function(e,t){o.h=!0;var n=!1;if("object"!=typeof e)e in c&&c[e]===t||(c[e]=t,n=!0);else{for(var r in e)t=e[r],r in c&&c[r]===t||(c[r]=t,n=!0);e.__esModule&&(c.__esModule=e.__esModule)}if(n)for(var i=0;u.length>i;i++){var s=u[i];s&&s(c)}return t}),2===t[1].length?{import:function(e){return n.import(e,r)},meta:n.createContext(r)}:void 0);return o.e=i.execute||function(){},[t[0],i.setters||[]]})),f=(s=s.catch((function(e){l(n,o,e)}))).then((function(e){return Promise.all(e[0].map((function(i,o){var u=e[1][o];return Promise.resolve(n.resolve(i,r)).then((function(e){var i=t(n,e,r);return Promise.resolve(i.I).then((function(){return u&&(i.i.push(u),!i.h&&i.I||u(i.n)),i}))}))}))).then((function(e){o.d=e}))}));return f.catch((function(e){o.e=null,o.er=e})),o=n[O][r]={id:r,i:u,n:c,I:s,L:f,h:!1,d:void 0,e:void 0,er:void 0,E:void 0,C:void 0}}(r,t);return n.C||function(e,t){return t.C=function e(t,n,r){if(!r[n.id])return r[n.id]=!0,Promise.resolve(n.L).then((function(){return Promise.all(n.d.map((function(n){return e(t,n,r)})))}))}(e,t,{}).then((function(){return function e(t,n,r){function i(){try{var e=n.e.call(j);if(e)return e=e.then((function(){n.C=n.n,n.E=null,l(t,n,null)}),(function(e){l(t,n,e)})),n.E=n.E||e;n.C=n.n,l(t,n,null)}catch(r){n.er=r,l(t,n,r)}finally{n.L=n.I=void 0,n.e=null}}if(!r[n.id]){if(r[n.id]=!0,!n.e){if(n.er)throw n.er;return n.E?n.E:void 0}var o;return n.d.forEach((function(i){try{(u=e(t,i,r))&&(u.catch((function(e){l(t,n,e)})),(o=o||[]).push(u))}catch(c){l(t,n,c)}var u})),o?Promise.all(o).then(i):i()}}(e,t,{})})).then((function(){return t.n}))}(r,n)}))},w.createContext=function(e){return{url:e}},w.onload=function(){},w.register=function(e,t){E=[e,t]},w.getRegister=function(){var e=E;return E=void 0,e};var j=Object.freeze(Object.create(null));y.System=new f;var P=p?Symbol():"#",C=p?Symbol():"$";d((function(e){e._t=fetch(e.src).then((function(e){return e.text()}))}),"[src]"),w.prepareImport=function(){var t=this;return t[C]||(t[P]={imports:{},scopes:{}},t[C]=Promise.resolve(),d((function(o){t[C]=t[C].then((function(){return(o._t||o.src&&fetch(o.src).then((function(e){return e.text()}))||Promise.resolve(o.innerHTML)).then((function(t){try{return JSON.parse(t)}catch(n){throw Error(e(1,"systemjs-importmap contains invalid JSON"))}})).then((function(e){t[P]=function(e,t,o){var u={imports:r({},o.imports),scopes:r({},o.scopes)};if(e.imports&&i(e.imports,u.imports,t,o,null),e.scopes)for(var c in e.scopes){var s=n(c,t);i(e.scopes[c],u.scopes[s]||(u.scopes[s]={}),t,o,s)}return u}(e,o.src||v,t[P])}))}))}),"")),t[C]},w.resolve=function(n,r){return s(this[P],t(n,r=r||v)||n,r)||function(t,n){throw Error(e(8,"Unable to resolve bare specifier '"+t+(n?"' from "+n:"'")))}(n,r)};var I,L,M=w.register;w.register=function(e,t){M.call(this,e,t)},w.createScript=function(e){var t=document.createElement("script");return t.charset="utf-8",t.async=!0,t.crossOrigin="anonymous",t.src=e,t},w.instantiate=function(t,n){var r=this;return new Promise((function(i,o){var u=w.createScript(t);u.addEventListener("error",(function(){o(Error(e(3,"Error loading "+t+(n?" from "+n:""))))})),u.addEventListener("load",(function(){document.head.removeChild(u),I===t?o(L):i(r.getRegister())})),document.head.appendChild(u)}))},g&&(window.addEventListener("error",(function(e){I=e.filename,L=e.error})),window.addEventListener("DOMContentLoaded",h),h()),m&&"function"==typeof importScripts&&(w.instantiate=function(e){var t=this;return Promise.resolve().then((function(){return importScripts(e),t.getRegister()}))}),function(e){function t(t){return!e.hasOwnProperty(t)||!isNaN(t)&&e.length>t||f&&e[t]&&"undefined"!=typeof window&&e[t].parent===window}var n,r,i,o=e.System.constructor.prototype,u=o.import;o.import=function(o,c){return function(){for(var o in n=r=void 0,e)t(o)||(n?r||(r=o):n=o,i=o)}(),u.call(this,o,c)};var c=[[],function(){return{}}],s=o.getRegister;o.getRegister=function(){var o=s.call(this);if(o)return o;var u,f=function(){var o,u=0;for(var c in e)if(!t(c)){if(0===u&&c!==n||1===u&&c!==r)return c;u++,o=c}if(o!==i)return o}();if(!f)return c;try{u=e[f]}catch(a){return c}return[[],function(e){return{execute:function(){e({default:u,__useDefault:!0})}}}]};var f="undefined"!=typeof navigator&&-1!==navigator.userAgent.indexOf("Trident")}("undefined"!=typeof self?self:global),function(t){var n=t.System.constructor.prototype,r=n.instantiate;n.shouldFetch=function(e){var t=e.split("?")[0].split("#")[0];return t.slice(t.lastIndexOf(".")).match(/\.(css|html|json|wasm)$/)},n.fetch=function(e){return fetch(e)},n.instantiate=function(t,n){var i=this;return this.shouldFetch(t)?this.fetch(t).then((function(r){if(!r.ok)throw Error(e(7,r.status+" "+r.statusText+", loading "+t+(n?" from "+n:"")));var o=r.headers.get("content-type");if(!o)throw Error(e(4,'Missing header "Content-Type", loading '+t+(n?" from "+n:"")));if(o.match(/^(text|application)\/(x-)?javascript(;|$)/))return r.text().then((function(e){return(0,eval)(e),i.getRegister()}));if(o.match(/^application\/json(;|$)/))return r.text().then((function(e){return[[],function(t){return{execute:function(){t("default",JSON.parse(e))}}}]}));if(o.match(/^text\/css(;|$)/))return r.text().then((function(e){return[[],function(t){return{execute:function(){var n=new CSSStyleSheet;n.replaceSync(e),t("default",n)}}}]}));if(o.match(/^application\/wasm(;|$)/))return(WebAssembly.compileStreaming?WebAssembly.compileStreaming(r):r.arrayBuffer().then(WebAssembly.compile)).then((function(e){var t=[],n=[],r={};return WebAssembly.Module.imports&&WebAssembly.Module.imports(e).forEach((function(e){var i=e.module;-1===t.indexOf(i)&&(t.push(i),n.push((function(e){r[i]=e})))})),[t,function(t){return{setters:n,execute:function(){return WebAssembly.instantiate(e,r).then((function(e){t(e.exports)}))}}}]}));throw Error(e(4,'Unknown module type "'+o+'"'))})):r.apply(this,arguments)}}("undefined"!=typeof self?self:global);var _="undefined"!=typeof Symbol&&Symbol.toStringTag;w.get=function(e){var t=this[O][e];if(t&&null===t.e&&!t.E)return t.er?null:t.n},w.set=function(t,n){try{new URL(t)}catch(u){console.warn(Error(e("W3",'"'+t+'" is not a valid URL to set in the module registry')))}var r;_&&"Module"===n[_]?r=n:(r=Object.assign(Object.create(null),n),_&&Object.defineProperty(r,_,{value:"Module"}));var i=Promise.resolve(r),o=this[O][t]||(this[O][t]={id:t,i:[],h:!1,d:[],e:null,er:void 0,E:void 0});return!o.e&&!o.E&&(Object.assign(o,{n:r,I:void 0,L:void 0,C:i}),r)},w.has=function(e){return!!this[O][e]},w.delete=function(e){var t=this[O],n=t[e];if(!n||null!==n.e||n.E)return!1;var r=n.i;return n.d&&n.d.forEach((function(e){var t=e.i.indexOf(n);-1!==t&&e.i.splice(t,1)})),delete t[e],function(){var n=t[e];if(!n||!r||null!==n.e||n.E)return!1;r.forEach((function(e){n.i.push(e),e(n.n)})),r=null}};var A="undefined"!=typeof Symbol&&Symbol.iterator;w.entries=function(){var e,t,n=this,r=Object.keys(n[O]),i=0,o={next:function(){for(;void 0!==(t=r[i++])&&void 0===(e=n.get(t)););return{done:void 0===t,value:void 0!==t&&[t,e]}}};return o[A]=function(){return this},o}}();
