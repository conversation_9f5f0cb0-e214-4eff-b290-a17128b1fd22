{"name": "j<PERSON>y-nice-select", "version": "1.1.0", "description": "A lightweight jQuery plugin that replaces native select elements with customizable dropdowns.", "main": "./js/jquery.nice-select.min.js", "dependencies": {"jquery": "^2.2.3"}, "devDependencies": {"gulp": "^3.9.0", "gulp-autoprefixer": "^3.1.0", "gulp-jshint": "^2.0.0", "gulp-rename": "^1.2.2", "gulp-sass": "^2.1.1", "gulp-uglify": "^1.5.1", "jshint": "^2.8.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/hernansartorio/jquery-nice-select.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/hernansartorio/jquery-nice-select/issues"}, "homepage": "https://github.com/hernansartorio/jquery-nice-select#readme"}