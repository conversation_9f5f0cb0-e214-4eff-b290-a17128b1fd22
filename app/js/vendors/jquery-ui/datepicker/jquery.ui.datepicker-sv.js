/* Swedish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> ( <EMAIL>). */
jQuery(function($){
    $.datepicker.regional['sv'] = {
		closeText: 'Stäng',
        prevText: '&laquo;Förra',
		nextText: '<PERSON><PERSON><PERSON>&raquo;',
		currentText: 'Idag',
        monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','April','<PERSON>','<PERSON><PERSON>',
        '<PERSON><PERSON>','Augusti','September','Okto<PERSON>','November','December'],
        monthNamesShort: ['Jan','Feb','Mar','Apr','Maj','Jun',
        'Jul','Aug','Sep','Okt','Nov','Dec'],
		dayNamesShort: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','Tis','Ons','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>'],
		dayNames: ['<PERSON>öndag','<PERSON><PERSON>ndag','Tisdag','Onsdag','<PERSON>sdag','<PERSON><PERSON>','<PERSON><PERSON>rda<PERSON>'],
		dayNamesMin: ['<PERSON><PERSON>','<PERSON><PERSON>','Ti','On','<PERSON>','<PERSON>','<PERSON>ö'],
		weekHeader: 'Ve',
        dateFormat: 'yy-mm-dd',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
    $.datepicker.setDefaults($.datepicker.regional['sv']);
});
