requireDefined(["jQuery"],"Run.App.TextMenu"),loadNamespace("Run.App"),Run.App.TextMenu=function(){function t(t){this.init(t)}return t.prototype.options={selector:null,Window:window,affix:!0,activeLinkClass:"active"},t.prototype.Menu=null,t.prototype.Links=null,t.prototype.ChildsAccordion=null,t.prototype.topOffsetBeforeFix=null,t.prototype.FixedItems=null,t.prototype.MenuClone=null,t.prototype.init=function(t){if(this.options=jQuery.extend({},this.options,jQuery.extend(!0,{},t)),this.Menu=jQuery(this.options.selector,this.options.Window.document),0===this.Menu.length)throw new Error("Invalid text menu selector. No accordion found.");if(1<this.Menu.length)throw new Error("Many text menus has been found accoding to provided selector "+this.options.selector);if(this.Links=jQuery("a",this.Menu),void 0!==Run.App.ChildsAccordion)try{this.ChildsAccordion=Run.App.ChildsAccordion.getInstance()}catch(t){console.log("TextMenu.js has not initiated ChildsAccordion because: "+t.message)}this.registerEvents()},t.prototype.registerEvents=function(){var i=this;this.Links.on("click",function(t){var e,o=jQuery(this).closest("li");o.hasClass("has-subitems")?o.hasClass("open")?(o.find(".level-02").slideUp("slow"),o.removeClass("open")):(o.find(".level-02").slideDown("slow"),o.addClass("open")):(e=i.getLinkAnchorName(this),null!==i.ChildsAccordion&&i.ChildsAccordion.hasItem(e)?i.ChildsAccordion.scrollToTop(function(){i.ChildsAccordion.openItem(e,function(){})}):i.scrollToView(e)),t.preventDefault()}),this.options.affix&&(this.toggleFix(),this.setActiveLink(),jQuery(this.options.Window).on("scroll",function(){i.toggleFix(),i.setActiveLink()}))},t.prototype.getLinkAnchorName=function(t){var e=jQuery(t).attr("href").match(/#([^#]*)/);return e?e[1]:null},t.prototype.getAnchor=function(t){return jQuery('a[name="'+t+'"]',this.options.Window.document)},t.prototype.setActiveLink=function(){var o,i=this,s=null,t=jQuery(this.options.Window).scrollTop();o=null===this.topOffsetBeforeFix?this.Menu.offset().top+this.Menu.outerHeight()+150:t+parseInt(this.Menu.css("top"))+this.Menu.outerHeight()+150,this.Links.each(function(){var t=i.getLinkAnchorName(this),e=i.getAnchor(t);return!e.length||!(o<e.offset().top)&&void(s=jQuery(this))}),s||(s=this.Links.eq(0)),this.Links.removeClass(this.options.activeLinkClass),s.addClass(this.options.activeLinkClass)},t.prototype.toggleFix=function(){var t=this.getFixedHeight(),e=jQuery(this.options.Window).scrollTop();this.MenuClone||(this.MenuClone=this.Menu.clone(),this.MenuClone.css({display:"none"}),this.MenuClone.insertBefore(this.Menu)),null===this.topOffsetBeforeFix&&e+t>=this.Menu.offset().top?(this.topOffsetBeforeFix=this.Menu.offset().top,this.Menu.css({position:"fixed",top:t+"px"}),this.MenuClone.css({display:""})):null!==this.topOffsetBeforeFix&&e+t<this.topOffsetBeforeFix&&(this.topOffsetBeforeFix=null,this.Menu.css({position:"",top:""}),this.MenuClone.css({display:"none"}))},t.prototype.getFixedHeight=function(t){var o=0;return this.FixedItems&&!t||(this.FixedItems=jQuery("*").filter(function(){var t,e,o=jQuery(this);return"fixed"===o.css("position")&&(t=o.css("top")).match(/[0-9]/)&&(!(e=o.css("bottom")).match(/[0-9]/)||parseInt(t)<parseInt(e))})),this.FixedItems.length&&this.FixedItems.each(function(){var t=parseInt(jQuery(this).css("top")),e=jQuery(this).outerHeight();o<t+e&&(o=t+e)}),o},t.prototype.scrollToView=function(t){var e,o=this.getAnchor(t);o.length&&(e=null===this.topOffsetBeforeFix?jQuery(this.options.Window).height()/5+100:parseInt(this.Menu.css("top"))+this.Menu.outerHeight()+100,jQuery("html,body").animate({scrollTop:o.offset().top-e},"slow"))},t}();