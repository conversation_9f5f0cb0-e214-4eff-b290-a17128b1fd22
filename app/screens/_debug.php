<?php /* @var $this Template */
if (
    !ON_LOCALHOST // everything is public on localhost
    && App::getUser('Group.pid') !== 'admins'
    //&& Sanitize::value($this->args[0]) !== 'myPublicTest' // allow public access
    && Sanitize::value($this->args[0]) !== 'testPayPalTls' // allow public access
    && Sanitize::value($this->args[0]) !== 'testBackgroundRequest' // allow public access
    && Sanitize::value($this->args[0]) !== 'whatIsMyIp' // allow public access
    && Sanitize::value($this->args[0]) !== 'testMrpStockReader' // allow public access
) {
    echo 'You must login as admin';
    return;
}
App::setLayout('App', 'debug');
App::setSeoTitle('debug');
// get args
$case = Sanitize::value($this->args[0]);

Utility::startTimer('debug');
switch ($case) {

    // .../_debug/updateProductVariantsInfo
    case 'updateProductVariantsInfo':
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        App::loadModel('Eshop', 'EshopVariantProduct');
        $ProductVariant = new EshopVariantProduct();

        $productIds = $ProductVariant->findList(array(
            'fields' => array(
                'EshopVariantProduct.run_eshop_products_id',
            ),
            'group' => 'EshopVariantProduct.run_eshop_products_id',
        ));

        while (!empty($productIds)) {
            $productId = array_shift($productIds);
            $productVariantIds = $ProductVariant->findList(array(
                'fields' => array(
                    'EshopVariantProduct.run_eshop_variant_products_id',
                ),
                'conditions' => array(
                    'EshopVariantProduct.run_eshop_products_id' => $productId,
                ),
            ));
            $variantGroupIds = $productVariantIds;
            $variantGroupIds[] = $productId;
            $variantGroupNames = $Product->findList(array(
                'fields' => array(
                    'EshopProduct.name',
                ),
                'conditions' => array(
                    'EshopProduct.id' => $variantGroupIds,
                ),
            ));
            $splitNames = array_map(function($name) {
                $name = trim($name);
                $name = preg_replace('/\s+/', ' ', $name);
                return explode(' ', $name);
            }, $variantGroupNames);
            $commonParts = $splitNames[$productId];
            foreach ($splitNames as $nameParts) {
                foreach ($commonParts as $index => $part) {
                    if (!isset($nameParts[$index]) || $nameParts[$index] !== $part) {
                        $commonParts = array_slice($commonParts, 0, $index);
                        break 2;
                    }
                }
            }
            $commonName = rtrim(implode(' ', $commonParts), ' -');
            //echo "Spoločný názov: " . $commonName . '<br><br>';
            $Product->resolveVariants($productId, $productVariantIds, $commonName);
        }
        break;

    // .../_debug/deleteIdFromCategoryName
    case 'deleteIdFromCategoryName':
        App::loadModel('Eshop', 'EshopProductCategory');
        $Category  = new EshopProductCategory();
        $categories = $Category->find();
        foreach ($categories as $category) {
            $category['name'] = preg_replace('/_\d+/', '', $category['name']);
            $category['seo_title'] = preg_replace('/_\d+/', '', $category['seo_title']);
            $category['slug'] = preg_replace('/-\d+/', '', $category['slug']);
            $Category->save($category);
        }
        echo("ids vymazane");       
        break;
    // .../_debug/printProductsWithCategories
    case 'printProductsWithCategories':
        App::loadModel('Eshop', 'EshopProduct');
        $Product  = new EshopProduct();
        App::loadModel('Eshop', 'EshopProductCategory');
        $Category  = new EshopProductCategory();
        $productIds = $Product->findList(array(
            'fields' => array('id'),
            'limit' => 500,
        ));
        
        $products = $Product->getDetails($productIds, array(
            'getCategories' => true,
        ));
        echo '<table>';
            foreach ($products as $product) {
                echo '<tr style="border-bottom: 1px solid black;">';
                    echo '<td>';
                    echo $product['code'];
                    echo '</td>';
                    echo '<td>';
                    echo  $product['name'] ;
                    echo '</td>';
                    $categoriesNames = [];
                
                    foreach ($product['EshopProductCategory'] as $category) {
                        $treeNames = array_column(array_reverse($Category->findUpInTree($category['id'])), 'name');
                        if (!empty($treeNames)) {
                            $categoriesNames[] = implode(' - ', $treeNames);
                        }
                    }
                    echo '<td>';
                    echo !empty($categoriesNames) ? implode(' <br>', $categoriesNames) : '';
                    echo '</td>';
                echo '</tr>';
            }
        echo '</table>';
        break;
    // .../_debug/addSizeColorToName
    case 'addSizeColorToName':
        App::loadModel('Eshop', 'EshopProduct');
        $Product  = new EshopProduct();
        $conditions = array(         
            'EshopProduct.size !=' => null,
            'OR',
            'EshopProduct.color !=' => null,
           );
        $products = $Product->find(array(
            'conditions' => $conditions,
        ));
        foreach($products as $product){
            $productAttribute = implode(', ', array_filter([$product['color'] ?? '', $product['size'] ?? '']));
            if ($productAttribute) {
                $productAttribute = " - " . $productAttribute;
            }   
            $product['name']= $product['name'] . $productAttribute;  
            $Product->save($product); 
        } 
        
        break;
    
    // .../_debug/categoriesImportFromOdooCsv
    case 'categoriesImportFromOdooCsv':
        App::loadModel('Eshop', 'EshopImportFromOdoo');
        $Category  = new EshopImportFromOdoo();
        $csvFilePath = 'userfiles/files/import/categories_import_final.csv';
        $Category->importCategoriesFromOdooCsv($csvFilePath);
        break;

    // .../_debug/brandsImportFromOdooCsv
    case 'brandsImportFromOdooCsv':
        App::loadModel('Eshop', 'EshopImportFromOdoo');
        $Brand  = new EshopImportFromOdoo();
        $csvFilePath = 'userfiles/files/import/brands_import.csv';
        $Brand->importBrandsFromOdooCsv($csvFilePath);
        break;
    
    // .../_debug/productsImportFromOdooCsv
    case 'productsImportFromOdooCsv':
        App::loadModel('Eshop', 'EshopImportFromOdoo');
        $Product  = new EshopImportFromOdoo();
        $csvFilePath = 'userfiles/files/import/products_all4.csv';
        $Product->importProductsFromOdooCsv($csvFilePath);
        break;

    // .../_debug/productImagesImportFromOdooCsv
    case 'productImagesImportFromOdooCsv':
        App::loadModel('Eshop', 'EshopImportFromOdoo');
        $Product  = new EshopImportFromOdoo();
        $csvFilePath = 'userfiles/files/import/images_all.csv';
        $Product->importProductImagesFromOdooCsv($csvFilePath);
        break;

    // .../_debug/addEshopShipmentMethod
    case 'addEshopShipmentMethod':
        App::loadModel('Eshop', 'EshopShipmentMethod');
        App::loadModel('App', 'Setting');
        $ShipmentMethod  = new EshopShipmentMethod();
        $Setting  = new Setting();
        $data = array(
            array(
                'name' => 'Slovenská pošta',
                'pid' => 'slovenskaPosta',
                'price' => 4.5,
                'delivery_country' => 'sk',
                'free_shipment_allowed' => 1
            ),
            array(
                'name' => 'Osobný odber',
                'pid' => 'localPickup',
                'price' => 0,
                'delivery_country' => 'sk',
                'free_shipment_allowed' => 0
            ),
            array(
                'name' => 'Osobný odber',
                'pid' => 'pickup',
                'price' => 0,
                'delivery_country' => 'cs',
                'free_shipment_allowed' => 0
            ),
            array(
                'name' => 'Pošta',
                'pid' => 'abroadDelivery',
                'price' => 10,
                'delivery_country' => 'cs',
                'free_shipment_allowed' => 0
            )
        );
        $ShipmentMethod->saveBatch(array(
            'create' => array(
                'EshopShipmentMethod' => $data
            ),
        ),
        array(
            'validate' => false,
        ));
        $Setting->update(
        array(
                'value' => 100,
        ), 
        array(
            'conditions' => array(
                'pid' => 'EshopShipment.freeShipmentProductsTotal',
            ),
            'validate' => false,
        ));
        break;

    // .../_debug/getMeilisearchStats
    case 'getMeilisearchStats':
        App::loadLib('App', 'MeiliSearch');
        $MeiliSearch = new MeiliSearch(EshopProduct::class, array(
            'apiUrlBase' => App::getSetting('App', 'meilisearch.apiUrlBase'),
            'apiKey' => App::getSetting('App', 'meilisearch.apiKey'),
        ));
        $stats = $MeiliSearch->getStats();
        echoReadable($stats);
        break;
        
    // .../_debug/getMeilisearchKeys[/{masterKey}]
    case 'getMeilisearchKeys':
        if (!($masterKey = Sanitize::value($this->args[1]))) {
            $masterKey = 'MASTER_KEY';
        }
        App::loadLib('App', 'MeiliSearch');
        $MeiliSearch = new MeiliSearch(EshopProduct::class, array(
            'apiUrlBase' => App::getSetting('App', 'meilisearch.apiUrlBase'),
            'apiKey' => $masterKey,
        ));
        $keys = $MeiliSearch->getKeys();
        echoReadable($keys);
        break;
    
    // .../_debug/initializeMeilisearchProducts
    case 'initializeMeilisearchProducts':
        App::loadModel('Eshop', 'EshopProductMeiliSearch');
        $ProductSearch = new EshopProductMeiliSearch();
        //echo $ProductSearch->version;
        set_time_limit(600);
        ini_set('memory_limit', '256M');
        $ProductSearch->deleteAllProducts();
        $ProductSearch->updateSettings();
        $ProductSearch->addOrReplaceAllProducts();
        break;
    
    // .../_debug/updateMeilisearchProductsSettings
    case 'updateMeilisearchProductsSettings':
        App::loadModel('Eshop', 'EshopProductMeiliSearch');
        $ProductSearch = new EshopProductMeiliSearch();
        $ProductSearch->updateSettings();
        break;
    
    // .../_debug/synchronizeMeilisearchProducts
    case 'synchronizeMeilisearchProducts':
        App::loadModel('Eshop', 'EshopProductMeiliSearch');
        $ProductSearch = new EshopProductMeiliSearch();
        $ProductSearch->synchronize(array(
            //'modifiedAfter' => '2022-09-01 00:00:00',
            'modifiedAfter' => -1,
            'supplierProductsModifiedAfter' => -1,
            'debug' => true,
            'async' => true,
        ));
        break;
   
    // .../_debug/addUnclassifiedCategory
    case 'addUnclassifiedCategory':
        App::loadModel('Eshop', 'EshopProductCategory');
        $Category = new EshopProductCategory();
        if (!$Category->findFirstBy('pid', 'unclassified')) {            
            $Category->addTreeNode(
                $Category->findFieldBy('id', 'pid', 'categories'), 
                array(
                    'pid' => 'unclassified',
                    'name' => 'Nezaradené',
                    'active' => 0,
                )
            );
        }
        break;

    // .../_debug/testGoogleTranslate
    case 'testGoogleTranslate':
        try {
            App::loadLib('App', 'Google');
            $Google = new Google(array(
                //'apiKey' => '???',
            ));
            $translatedText = $Google->translate('This is a test of the Google Api translation method.', 'sk', array(
                'sourceLang' => 'en',
            ));
            echoReadable($translatedText);
            $translatedText = $Google->translate(
                array(
                    'This is a test of the Google Api translation method.',
                    'If it works also for <strong>array of strings</strong>.',
                    'And if it keeps <strong>keys is output array</strong>.',
                ), 
                'sk', 
                array(
                    'sourceLang' => 'en',
                )
            );
            echoReadable($translatedText);
            $translatedText = $Google->translate(
                array(
                    'name' => 'This is a test of the Google Api translation method.',
                    'description' => 'If it works also for <strong>array of strings</strong>.',
                    'And if it keeps <strong>keys is output array</strong>.',
                ), 
                'sk', 
                array(
                    'sourceLang' => 'en',
                )
            );
            echoReadable($translatedText);
        
        } 
        catch (Exception $e) {
            echoReadable($e->getMessage());
            throw $e;
        }
        break;

    // .../_debug/testFrankanaStockCsvReader
    case 'testFrankanaStockCsvReader':
        set_time_limit(600);
        ini_set('memory_limit', '512M');

        App::loadLib('Eshop', 'FrankanaStockCsvReader');
        $Reader = new FrankanaStockCsvReader(array(
            'username' => '232331', // karavandoplnky.sk access
            'password' => 'BnT19kLP', // karavandoplnky.sk access
            //'file' => 'tmp/???.xml', //debug
        ));
        $data = array();
        $availabilities = array();
        while($result = $Reader->readRecord()) {
            $data[] = $result;
            if (!isset($availabilities[$result['availability']])) {
                $availabilities[$result['availability']] = 0;
            }
            $availabilities[$result['availability']]++;
        }        
        echo echoReadable($availabilities);
        echo echoReadable($data);
        break;
        
    // .../_debug/testFrankanaProductApiReader
    case 'testFrankanaProductApiReader':
        set_time_limit(600);
        ini_set('memory_limit', '512M');

        App::loadLib('Eshop', 'FrankanaProductApiReader');
        $Reader = new FrankanaProductApiReader(array(
            'apiResponseXmlFile' => 'tmp/frankanaApiResponse.xml', //debug
        ));
        $data = array();
        $imageCounts = array();
        while($result = $Reader->readRecord()) {
            $data[] = $result;
            
            $imageCount = 0;
            if (!empty($result['image_import_source'])) {
                $imageCount++;
            }
            $imageCount += count($result['gallery_image_import_sources']);
            if (!isset($imageCounts[$imageCount])) {
                $imageCounts[$imageCount]['count'] = 0;
                $imageCounts[$imageCount]['examples'] = array();
            }
            $imageCounts[$imageCount]['count']++;
            if (count($imageCounts[$imageCount]['examples']) < 10) {
                $imageCounts[$imageCount]['examples'][] = $result['code'];
            }
        }
        
        /*/>
        ksort($imageCounts);
        echo echoReadable($imageCounts);
        //*/
        
        echo echoReadable($data);
        break;
    
    // .../_debug/testFacebookConversionCode
    case 'testFacebookConversionCode':
        App::loadModel('Eshop', 'EshopOrder');
        $Order = new EshopOrder();
        echo $Order->getFacebookConversionCode(1365);
        break;
    
    // .../_debug/checkEshopProductImagesConsistency/{variant}[/{ignoreSoftDeleted}]
    case 'checkEshopProductImagesConsistency':
        $field = 'image';
        $variant = $case = Sanitize::value($this->args[1], 'large');
        $ignoreSoftDeleted = $case = Sanitize::value($this->args[2]);
        
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $dbFiles = $Product->findList(array(
            'key' => $field,
            'fields' => array('id'),
            'conditions' => array(
                $field . ' !=' => array('', null),
            ),
            'ignoreSoftDeleted' => $ignoreSoftDeleted,
        ));
        foreach ($dbFiles as $basename => $v) {
            // remove extension (it can differ from the extension of existing files)
            $filename = File::getPathinfo($basename, PATHINFO_FILENAME);
            unset($dbFiles[$basename]);
            $dbFiles[$filename] = true;
        }
        
        $paths = $Product->getFileFieldPaths($field);
        foreach ($paths as &$path) {
            $path = ROOT . DS . $path;
        }
        unset($path);
        
        $progress = array(
            'orphanFilesCount' => 0,
            'noOriginalFilesCount' => 0,
            'orphanFiles' => array(),
            'noOriginalFiles' => array(),
        );
        
        $files = scandir($paths[$variant]);
        foreach ($files as $file) {
            if (is_dir($paths[$variant] . DS . $file)) {
                continue;
            }
            // remove extension (it can differ from the extension stored in DB)
            $filename = File::getPathinfo($file, PATHINFO_FILENAME);
            if (!isset($dbFiles[$filename])) {
                $progress['orphanFilesCount']++;
                $progress['orphanFiles'][] = $file;
            }
            if (
                !is_readable($paths['original'] . DS . $file)
                && !is_readable($paths['original'] . DS . $filename . '.png')
                && !is_readable($paths['original'] . DS . $filename . '.jpg')
            ) {
                $progress['noOriginalFilesCount']++;
                $progress['noOriginalFiles'][] = $file;
            }
        }
        echo sprintf('Results for EshopProduct.%s variant %s:', $field, $variant);
        echo '<br><br><pre>' . print_r($progress, true) . '</pre>';
        break;
    
    // .../_debug/changeJpegToJpgOnEshopProductImages[/{previewOnly}]
    case 'changeJpegToJpgOnEshopProductImages':
        $previewOnly = Sanitize::value($this->args[1]);
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $images = $Product->findList(array(
            'fields' => array('image'),
            'conditions' => array(
                'image !=' => array(null, ''),
            ),
            'ignoreSoftDeleted' => false,
        ));
        $progress = array(
            'jpegCount' => 0,
            'jpeCount' => 0,
            'normalizedCount' => 0,
            'failedCount' => 0,
            'failed' => array(),
        );
        $normalizedImages = array();
        foreach ($images as $id => $image) {
            $imageInfo = File::getPathinfo($image);
            $imageExtension = strtolower($imageInfo['extension']);
            if (
                $imageExtension === 'jpeg'
                || $imageExtension === 'jpe'
            ) {
                $imageExtension === 'jpeg' ? $progress['jpegCount']++ : $progress['jpeCount']++;
                $normalizedImage = $imageInfo['filename'] . '.jpg';
                $normalizedOriginalVariant = $Product->getFileFieldPath('image', array(
                    'file' => $normalizedImage,
                    'variant' => 'original',
                ));
                if (is_readable(ROOT . DS . $normalizedOriginalVariant)) {
                    $normalizedImages[] = array(
                        'id' => $id,
                        'image' => $normalizedImage,
                    );
                    $progress['normalizedCount']++;
                }
                else {
                    $progress['failedCount']++;
                    $progress['failed'][$id] = $image;
                }
            }
        }
        if (!$previewOnly) {
            $Product->saveBatch(array(
                'update' => array(
                    'EshopProduct' => $normalizedImages
                )
            ));
        }
        echo '<br><br><pre>' . print_r($progress, true) . '</pre>';
        break;
    
    // .../_debug/checkForProductImageVariantsExistence
    case 'checkForProductImageVariantsExistence':
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $images = $Product->findList(array(
            'fields' => array('image'),
            'conditions' => array(
                'image !=' => array(null, ''),
            ),
        ));
        echo 'Following product image variants have not been found:<br>';
        $progress = array(
            'imagesCount' => 0,
            'missingImagesCount' => 0,
            'missingVariantsCount' => array(),
        );
        foreach ($images as $id => $image) {
            $variants = $Product->getFileFieldPaths('image', array(
                'file' => $image,
            ));
            $progress['imagesCount']++;
            foreach ($variants as $variantName => $variant) {
                if (!is_readable(ROOT . DS . $variant)) {
                    $progress['missingImagesCount']++;
                    echo sprintf('%s: product id %s - %s is not readable', $progress['missingImagesCount'], $id, $variant) . '<br>';
                    if (empty($progress['missingVariantsCount'][$variantName])) {
                        $progress['missingVariantsCount'][$variantName] = 0;
                    }
                    $progress['missingVariantsCount'][$variantName]++;
                }
            }
        }
        echo '<br><br><pre>' . print_r($progress, true) . '</pre>';
        break;
    
    // .../_debug/setContentBlocksCenterContentToDefault
    case 'setContentBlocksCenterContentToDefault':
        App::loadModel('App', 'ContentBlockInstance');
        $BlockInstance = new ContentBlockInstance();
        $blocks = $BlockInstance->find(array(
            'fields' => array(
                'id',
                'content_block_model',
                'content_block_data',
            ),
        ));
        foreach ($blocks as &$block) {
            list($module, $model) = explode('.', $block['content_block_model']);
            /* @var $Block ContentBlock */
            $Block = App::loadModel($module, $model, true);
            $fields = $Block->getPropertyFields();
            if (
                array_key_exists('block_center_content', $fields)
            ) {
                $data = json_decode($block['content_block_data'], true);
                $data['block_center_content'] = $fields['block_center_content'];
                $block['content_block_data'] = json_encode($data, JSON_UNESCAPED_UNICODE);
            }
        }
        unset($block);
        $BlockInstance->saveBatch(array(
            'update' => array(
                'ContentBlockInstance' => $blocks,
            ),
        ));
        break;
    
    case 'createFrankanaSupplierProducts':
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $products = $Product->find(array(
            'fields' => array(
                'id AS run_eshop_products_id',
                'supplier_pid',
                'code AS supplier_product_id', 
                'price',
                'tax_rate',
                'availability',
                'created',
                'modified',
            ),
            'conditions' => array(
                'supplier_pid' => 'FRANKANA',
            ),
        ));
        $Product->saveBatch(array(
            'create' => array(
                'EshopSupplierProduct' => $products,
            ),
        ));
        break;
            
    case 'refactorGiftCardVouchers':
        $orderedGiftCardProducts = DB::select('run_eshop_order_products', array(
            'conditions' => array(
                'run_eshop_products_id' => array(297443, 297444, 297446, 297447),
                'run_eshop_vouchers_id !=' => null,
            ),
            'fields' => array(
                'id AS run_eshop_order_products_id',
                'run_eshop_vouchers_id',
            ),
        ));
        DB::insert(
            'run_eshop_order_gift_card_product_vouchers', 
            $orderedGiftCardProducts, 
            array(
                'multiple' => true,
            )
        );
        // add manually created codes SRAQPRLHBV (5€) and Y73RUG2M6J (10€)
        // for products 297443 (5€) and 297444 (10€) in order 49596
        DB::insert(
            'run_eshop_order_gift_card_product_vouchers', 
            array(
                // 10€
                array(
                    'run_eshop_order_products_id' => 78725,
                    'run_eshop_vouchers_id' => 13,
                ),
                // 5€
                array(
                    'run_eshop_order_products_id' => 78726,
                    'run_eshop_vouchers_id' => 14,
                ),
            ), 
            array(
                'multiple' => true,
            )
        );        
        break;
    
    // use this case e.g. to display files in .../config/templates/
    case 'viewFile':
        // resolve file
        $path = ROOT . DS;
        if (empty($_GET['file'])) {
            echo sprintf('Zadaj cestu k súboru z priečiku %s', $path);
            break;
        }
        $path .= ltrim($_GET['file'], DS);
        if (!is_readable($path)) {
            echo sprintf('Neexistujúci súbor %s', $path);
            break;
        }
        // resolve layout
        if (!array_key_exists('layout', $_GET)) {
            App::setLayout('App', 'default');
        }
        elseif (empty($_GET['layout'])) {
            App::setLayout(false);
        }
        else {
            $layoutParts = explode('.', $_GET['layout']);
            $layoutModule = count($layoutParts) > 1 ? reset($layoutParts) : 'App';
            $layoutName = end($layoutParts);
            App::setLayout($layoutModule, $layoutName);
        }
        require_once $path;
        break;    
    
    case 'testHumanizeTimePeriod':
        $periods = array(
           0.5,
           1.5,
           1.55,
           7.5,
           7.55,
           10.5,
           35.5,
           38.55,
        );
        foreach ($periods as $i) {
            for ($d = 1; $d <= 2; $d++) {
                echo '<br>' . $i . ' -> ' . Utility::humanizeTimePeriod($i, array(
                    'declension' => $d,
                ));
            }
        }
        for ($i = 1; $i < 50; $i++) {
            for ($d = 1; $d <= 2; $d++) {
                echo '<br>' . $i . ' -> ' . Utility::humanizeTimePeriod($i, array(
                    'declension' => $d,
                ));
            }
        }
        break;
    
    // .../_debug/checkSession/{sessionId}
    case 'checkSession':
        $sessionId = Sanitize::value($this->args[1]);
        if (empty($sessionId)) {
            echo 'Zadajte session id: .../_debug/checkSession/{sessionId}';
            break;
        }
        $sessionPath = TMP . DS . 'sessions' . DS . 'sess_' . $sessionId;
        $permanentSessionPath = TMP . DS . 'sessions' . DS . 'permanent' . DS . 'sess_' . $sessionId;
        if (is_readable($sessionPath)) {
            $sessionContent = file_get_contents($sessionPath);
        }
        elseif (is_readable($permanentSessionPath)) {
            echo 'Ide o permanentnu session<br>';
            $sessionContent = file_get_contents($permanentSessionPath);
        }
        else {
            echo 'Session sa nenašla';
            break;
        }
        $sessionBackup = $_SESSION;
        session_decode($sessionContent);
        echo '<pre>' . print_r($_SESSION, true) . '</pre>';
        $_SESSION = $sessionBackup;
        break;
    
    case 'testExceptionWithCode':
        throw new Exception('Exception with code', 45);
        //throw new Exception('Exception with code', 'tralala');
        break;
    
    case 'testExceptionWithoutCode':
        throw new Exception('Exception without code');
        break;
    
    case 'geneticAlgorithm':
        // see https://www.sitepoint.com/genetic-algorithms-introduction/
        // run .../_debug/geneticAlgorithm and see the result in js console
        App::startJsCapture();
        ?><script type="text/javascript">
        (function() {
            
            class Candidate {
                constructor(chromosome, fitness) {
                    this.chromosome = chromosome;
                    this.fitness = fitness;
                }

                /**
                 * Convenience method to sort an array of Candidate
                 * objects.
                 */
                static sort(candidates, asc) {
                    candidates.sort((a, b) => (asc)
                        ? (a.fitness - b.fitness)
                        : (b.fitness - a.fitness)
                    );
                }
            }

            class GeneticAlgorithm {
                
                constructor(params) {
                    this.alphabet = params.alphabet;
                    this.target = params.target;
                    this.chromosomeLength = params.target.length;
                    this.populationSize = params.populationSize;
                    this.selectionSize = params.selectionSize;
                    this.mutationRate = params.mutationRate;
                    this.mutateGeneCount = params.mutateGeneCount;
                    this.maxGenerations = params.maxGenerations;
                }

                /**
                 * Convenience method to return a random integer [0-max).
                 */
                randomInt(max) {
                    return Math.floor(Math.random() * max);
                }

                /**
                 * Create a new chromosome from random alleles.
                 */
                createChromosome() {
                    const chrom = [];
                    for (let i = 0; i < this.chromosomeLength; i++) {
                        chrom.push(this.alphabet[
                            this.randomInt(this.alphabet.length)
                        ]);
                    }
                    return chrom;
                }

                /**
                 * Create the initial population with random chromosomes
                 * and assign each a fitness score for later evaluation.
                 */
                init() {
                    this.generation = 0;
                    this.population = [];

                    for (let i = 0; i < this.populationSize; i++) {
                        const chrom = this.createChromosome();
                        const score = this.calcFitness(chrom);
                        this.population.push(new Candidate(chrom, score));
                    }
                }

                /**
                 * Measure a chromosome’s fitness based on how close its
                 * genes match those of the target; uses mean squared
                 * error.
                 */
                calcFitness(chrom) {
                    let error = 0;
                    for (let i = 0; i < chrom.length; i++) {
                        error += Math.pow(
                            this.target[i].charCodeAt() - chrom[i].charCodeAt(),
                            2
                        );
                    }
                    return error / chrom.length;
                }

                /**
                 * Reduce the population to only the fittest candidates;
                 * elitist selection strategy.
                 */
                select() {
                    // lower MSE is better
                    Candidate.sort(this.population, true);
                    this.population.splice(this.selectionSize);
                }

                /**
                 * Apply crossover and mutation to create new offspring
                 * chromosomes and increase the population.
                 */
                reproduce() {
                    const offspring = [];
                    const numOffspring = this.populationSize /
                        this.population.length * 2;

                    for (let i = 0; i < this.population.length; i += 2) {
                        for (let j = 0; j < numOffspring; j++) {
                            let chrom = this.crossover(
                                this.population[i].chromosome,
                                this.population[i + 1].chromosome,
                            );
                            chrom = this.mutate(chrom);

                            const score = this.calcFitness(chrom);
                            offspring.push(new Candidate(chrom, score));
                        }
                    }

                    this.population = offspring;
                }

                /**
                 * Create a new chromosome through uniform crossover.
                 */
                crossover(chromA, chromB) {
                    const chromosome = [];
                    for (let i = 0; i < this.chromosomeLength; i++) {
                        chromosome.push(
                            this.randomInt(2) ? chromA[i] : chromB[i]
                        );
                    }
                    return chromosome;
                }

                /**
                 * (Possibly) introduce mutations to a chromosome.
                 */
                mutate(chrom) {
                    if (this.mutationRate < this.randomInt(1000) / 1000) {
                        return chrom;
                    }

                    for (let i = 0; i < this.mutateGeneCount; i++) {
                        chrom[this.randomInt(this.chromosomeLength)] =
                            this.alphabet[
                                this.randomInt(this.alphabet.length)
                            ];
                    }
                    return chrom;
                }

                /**
                 * Return whether execution should continue processing
                 * the next generation or should stop.
                 */
                stop() {
                    if (this.generation > this.maxGenerations) {
                        return true;
                    }

                    for (let i = 0; i < this.population.length; i++) {
                        if (this.population[i].fitness == 0) {
                            return true;
                        }
                    }
                    return false;
                }

                /**
                 * Repeatedly perform genetic operations on the
                 * population of candidate chromosomes in an attempt to
                 * converge on the fittest solution.
                 */
                evolve() {
                    this.init();
                    do {
                        this.generation++;
                        this.select();
                        this.reproduce();
                    } while (!this.stop());

                    return {
                        generation: this.generation,
                        population: this.population
                    };
                }
            }

            const result = new GeneticAlgorithm({
                alphabet: Array.from('ABCDEFGHIJKLMNOPQRSTUVWXYZ !'),
                target: Array.from('HELLO WORLD!'),
                populationSize: 100,
                selectionSize: 40,
                mutationRate: 0.03,
                mutateGeneCount: 2,
                maxGenerations: 1000000
            }).evolve();

            console.log('Generation', result.generation);
            Candidate.sort(result.population, true);
            console.log('Fittest candidate', result.population[0]);            
        })();
        </script><?php
        App::endJsCapture();
        break;
    
    case 'App_getUrl':
        echo App::getUrl(array(
            'locator' => '/x/y/p:7?a=2&b=3#top',
            'args' => array(5),
            'params' => array('g' => 8, 'r' => 9),
            'get' => array('c' => 4),
            'anchor' => 'bottom',
        ));
        break;
    
    case 'countTranslatedPoCharacters':
//        $utf8CharsKey = 'utf8Chars';
//        $bytesKey = 'bytes';
//        $noHtmlUtf8CharsKey = 'noHtmlUtf8Chars';
//        $noHtmlBytesKey = 'noHtmlBytes';
//        $pagesKey = 'pages';
//        $noHtmlPagesKey = 'noHtmlPages';
//        $googleTranslatePriceKey = 'googleTranslatePrice';
        $utf8CharsKey = 'pocet znakov';
        $bytesKey = 'pocet bajtov';
        $noHtmlUtf8CharsKey = 'pocet znakov po vynechani HTML tagov';
        $noHtmlBytesKey = 'pocet bajtov po vynechani HTML tagov';
        $pagesKey = 'pocet stranok';
        $noHtmlPagesKey = 'pocet stranok po vynechani HTML tagov';
        $googleTranslatePriceKey = 'cena za google translate';
        
        $result = array(
            'total' => array(
                $utf8CharsKey => 0, 
                $bytesKey => 0,
                $noHtmlUtf8CharsKey => 0, 
                $noHtmlBytesKey => 0,
            ), 
            'errors' => array()
        );
        
        $poFiles = array(
            '/app/locale/App_sk_SK.po',
            '/app/locale/App_sk_SK_js.po',
            '/app/modules/Eshop/locale/Eshop_sk_SK.po',
            '/app/modules/Eshop/locale/EshopValidations_sk_SK.po',
        );
        foreach ($poFiles as $poFile) {
            try {
                $translations = File::parsePo(ROOT . $poFile, array(
                    'translatedOnly' => false,
                ));
                $texts = implode(' ', array_keys($translations));

                $result[$poFile][$utf8CharsKey] += mb_strlen($texts, 'utf8');
                $result[$poFile][$bytesKey] += strlen($texts);
                $texts = Sanitize::htmlToText($texts, false);
                $result[$poFile][$noHtmlUtf8CharsKey] += mb_strlen($texts, 'utf8');
                $result[$poFile][$noHtmlBytesKey] += strlen($texts);

                $result['total'][$utf8CharsKey] += $result[$poFile][$utf8CharsKey];
                $result['total'][$bytesKey] += $result[$poFile][$bytesKey];
                $result['total'][$noHtmlUtf8CharsKey] += $result[$poFile][$noHtmlUtf8CharsKey];
                $result['total'][$noHtmlBytesKey] += $result[$poFile][$noHtmlBytesKey];
            } 
            catch (Exception $e) {
                $result['errors'][] = $e->getMessage();
                continue;
            }
        }
        
        foreach ($result as $key => &$value) {
            if (!isset($value[$utf8CharsKey])) {
                continue;
            }
            $value[$pagesKey] = $value[$utf8CharsKey] / 1800;
            $value[$noHtmlPagesKey] = $value[$noHtmlUtf8CharsKey] / 1800;
            $value[$googleTranslatePriceKey] = $value[$bytesKey] / 1000000 * 16;
        }
        
        echo '<pre>' . print_r($result, true) . '</pre>';
        
        break;
    
    case 'countTranslatedDbCharacters':
//        $utf8CharsKey = 'utf8Chars';
//        $bytesKey = 'bytes';
//        $noHtmlUtf8CharsKey = 'noHtmlUtf8Chars';
//        $noHtmlBytesKey = 'noHtmlBytes';
//        $pagesKey = 'pages';
//        $noHtmlPagesKey = 'noHtmlPages';
//        $googleTranslatePriceKey = 'googleTranslatePrice';
        $utf8CharsKey = 'pocet znakov';
        $bytesKey = 'pocet bajtov';
        $noHtmlUtf8CharsKey = 'pocet znakov po vynechani HTML tagov';
        $noHtmlBytesKey = 'pocet bajtov po vynechani HTML tagov';
        $pagesKey = 'pocet stranok';
        $noHtmlPagesKey = 'pocet stranok po vynechani HTML tagov';
        $googleTranslatePriceKey = 'cena za google translate';
        
        $result = array(
            'total' => array(
                $utf8CharsKey => 0, 
                $bytesKey => 0,
                $noHtmlUtf8CharsKey => 0, 
                $noHtmlBytesKey => 0,
            ), 
            'errors' => array()
        );
        
        $models = App::getModels();
        foreach ($models as $module => $moduleModels) {
            if ($module === 'Payment') {
                continue;
            }
            foreach ($moduleModels as $model) {
                if (in_array($model, array(
                    'EshopHeureka',
                    'EshopManufacturer',
                ))) {
                    continue;
                }
                try {
                    $Model = App::loadModel($module, $model, true);
                    if (!($fields = $Model->getTranslatedFields())) {
                        continue;
                    }
                    $items = $Model->find(array(
                        'fields' => $fields,
                        'avoidFields' => array(
                            'slug',
                            'url',
                            'locator',
                            'price',
                            'discount_price',
                            'heureka_url',
                            'heureka_category_name',
                            'heureka_name',
                            'active',
                        ),
                    ));
                    if (empty($items)) {
                        continue;
                    }
                    $result[$model] = array(
                        $utf8CharsKey => 0, 
                        $bytesKey => 0,
                        $noHtmlUtf8CharsKey => 0, 
                        $noHtmlBytesKey => 0,
                    );
                    foreach ($items as $item) {
                        $texts = implode(' ', $item);
                        $result[$model][$utf8CharsKey] += mb_strlen($texts, 'utf8');
                        $result[$model][$bytesKey] += strlen($texts);
                        $texts = Sanitize::htmlToText($texts, false);
                        $result[$model][$noHtmlUtf8CharsKey] += mb_strlen($texts, 'utf8');
                        $result[$model][$noHtmlBytesKey] += strlen($texts);
                    }
                    $result['total'][$utf8CharsKey] += $result[$model][$utf8CharsKey];
                    $result['total'][$bytesKey] += $result[$model][$bytesKey];
                    $result['total'][$noHtmlUtf8CharsKey] += $result[$model][$noHtmlUtf8CharsKey];
                    $result['total'][$noHtmlBytesKey] += $result[$model][$noHtmlBytesKey];
                } 
                catch (Exception $e) {
                    $result['errors'][] = $e->getMessage();
                    continue;
                }
            }
        }
        
        App::loadModel('App', 'WebContent');
        $WebContent = new WebContent();
        $items = $WebContent->find(array(
            'fields' => array(
                'id',
                'name',
                'seo_title',
                'seo_description',
                'seo_keywords',
                'text',
            ),
            'conditions' => array(
                'lang' => 'sk',
                'id !=' => null,
                // do not export: oznam, obchodné podmienky
                'locator !*~*' => array('obchodne', 'oznam'),
            ),
            'order' => 'id ASC',
        ));  
        $model = 'WebContent';
        $result[$model] = array(
            $utf8CharsKey => 0, 
            $bytesKey => 0,
            $noHtmlUtf8CharsKey => 0, 
            $noHtmlBytesKey => 0,
        );
        foreach ($items as $item) {
            $texts = implode(' ', $item);
            $result[$model][$utf8CharsKey] += mb_strlen($texts, 'utf8');
            $result[$model][$bytesKey] += strlen($texts);
            $texts = Sanitize::htmlToText($texts, false);
            $result[$model][$noHtmlUtf8CharsKey] += mb_strlen($texts, 'utf8');
            $result[$model][$noHtmlBytesKey] += strlen($texts);
        }
        $result['total'][$utf8CharsKey] += $result[$model][$utf8CharsKey];
        $result['total'][$bytesKey] += $result[$model][$bytesKey];
        $result['total'][$noHtmlUtf8CharsKey] += $result[$model][$noHtmlUtf8CharsKey];
        $result['total'][$noHtmlBytesKey] += $result[$model][$noHtmlBytesKey];
        
        foreach ($result as $key => &$value) {
            if (!isset($value[$utf8CharsKey])) {
                continue;
            }
            $value[$pagesKey] = $value[$utf8CharsKey] / 1800;
            $value[$noHtmlPagesKey] = $value[$noHtmlUtf8CharsKey] / 1800;
            $value[$googleTranslatePriceKey] = $value[$bytesKey] / 1000000 * 16;
        }
        
        echo '<pre>' . print_r($result, true) . '</pre>';
        break;    
    
    case 'exportWebContentsTexts':
        App::setLayout(false);
        App::setDebug(false);
        App::loadModel('App', 'WebContent');
        $WebContent = new WebContent();
        $WebContent->export(
            array(
                'fields' => array(
                    'id',
                    'name',
                    'seo_title',
                    'seo_description',
                    'seo_keywords',
                    'text',
                ),
                'conditions' => array(
                    'lang' => 'sk',
                    'id !=' => null,
                    // do not export: oznam, obchodné podmienky
                    'locator !*~*' => array('obchodne', 'oznam'),
                ),
                'order' => 'id ASC',
            ),
            array(
                'file' => 'WebContent',
                'format' => 'xlsx', // 'xml'
                //'xmlFormatted' => true,
            )
        );
        break;
    
    case 'exportTranslatedTexts':
        $actualModule = App::getValue($this->args[1]);
        $actualModel = App::getValue($this->args[2]);
        if (
            !empty($actualModule) 
            && !empty($actualModel)
        ) {
            App::setLayout(false);
            App::setDebug(false);
            $Model = App::loadModel($actualModule, $actualModel, true);
            $fields = $Model->getTranslatedFields();
            array_unshift($fields, 'id');
            $Model->export(
                array(
                    'fields' => $fields,
                    'avoidFields' => array(
                        'slug',
                        'url',
                        'locator',
                        'price',
                        'discount_price',
                        'heureka_url',
                        'heureka_category_name',
                        'heureka_name',
                        'active',
                    ),
                    'order' => 'id ASC',
                ),
                array(
                    'file' => $actualModel,
                    'format' => 'xlsx', // 'xml'
                    //'xmlFormatted' => true,
                )
            );
            exit();
        }

        echo 'Click on following links to export translated fields of respective models:<br><br>';
        $models = App::getModels();
        foreach ($models as $module => $moduleModels) {
            if ($module === 'Payment') {
                continue;
            }
            foreach ($moduleModels as $model) {
                try {
                    $Model = App::loadModel($module, $model, true);
                } 
                catch (Exception $ex) {
                    echo sprintf('Loading of model %s has failed', $model) . '<br><br>';
                    continue;
                }
                if ($Model->getTranslatedFields()) {
                    ?><a <?php 
                        ?>href="/_debug/exportTranslatedTexts/<?php echo $module ?>/<?php echo $model ?>" <?php 
                        ?>target="_blank"<?php 
                    ?>><?php
                        echo $model;
                    ?></a><br><br><?php
                }
            }
        }
        break;
        
    case 'jsChannel':
        // !!! make symbolic link core-v2-other-domain to core-v2
        ?><iframe src="http://core-v2-other-domain/_debug/jsChannelChild" frameborder="0" id="iframe"></iframe><?php
        App::setJsFiles(array(
            '/app/js/vendors/jschannel.js',
        ));
        App::startJsCapture();
        ?><script type="text/javascript">
            var channel = Channel.build({
                window: document.getElementById('iframe').contentWindow,
                //origin: '*',
                origin: 'http://core-v2-other-domain',
                scope: 'roundcube'
            });
            channel.bind('getPassword', function(trans) {
                console.log('jsChannelParent getPassword');
                return 'thisisapassword';
            });
        </script><?php
        App::endJsCapture();
        break;
    
    case 'jsChannelChild':
        App::setJsFiles(array(
            '/app/js/vendors/jschannel.js',
        ));
        App::startJsCapture();
        ?><script type="text/javascript">
            var channel = Channel.build({
                window: window.parent,
                //origin: '*',
                origin: 'http://core-v2',
                scope: 'roundcube'
            });
            channel.call({
                method: 'getPassword',
                success: function (password) {
                    console.log('jsChannelChild calling getPassword: ' + password);
                }
            });
        </script><?php
        App::endJsCapture();
        break;
    
    case 'test':
        $getParams = array(
            'p1' => 'v1', 
            'p2' => array('v2', 'v3'),
            'p3' => array(
                'p31' => 'v4',
                'p32' => array('v5'),
                'v6',
            )
        );
        echo rawurldecode(App::getUrl(array(
            'locator' => 'test-url',
            'get' => $getParams,
        ))) . '<br><br>';
        echo rawurldecode(App::getUrl(array(
            'locator' => 'test-url',
            'get' => $getParams,
            'allowNestedGetParams' => false,
        ))) . '<br><br>';
        unset($getParams['p3']);
        echo rawurldecode(App::getUrl(array(
            'locator' => 'test-url',
            'get' => $getParams,
        ))) . '<br><br>';
        echo rawurldecode(App::getUrl(array(
            'locator' => 'test-url',
            'get' => $getParams,
            'allowNestedGetParams' => false,
        ))) . '<br><br>';
        break;
    
    case 'checkHeurekaIncludedProducts':
        App::loadLib('Eshop', 'HeurekaIncludedProductsXmlReader');
        //$file = '/app/tmp/produkty-v-katalogu-heureky.xml';
        $file = '/app/tmp/nezaradene-produkty.xml';
        $Reader = new HeurekaIncludedProductsXmlReader($file);
        $products = array();
        while (($product = $Reader->readRecord())) {
            $products[$product['url']] = $product;
        }
        // regex to convert URLs to array string items:
        // find:^ *(http\S+) *$
        // replace:            '$1',
        $checkedProducts = array(
            // copy-paste here list of product absolute URLs and use the above
            // regext to convert them to array string items
            //...
        );
        $checkedProducts = array_flip($checkedProducts);
        $intersectProducts = array_intersect_key($checkedProducts, $products);
        //$intersectProducts = array_values($intersectProducts);
        $intersectProducts = array_flip($intersectProducts);
        $diffProducts = array_diff_key($checkedProducts, $products);
        //$diffProducts = array_values($diffProducts);
        $diffProducts = array_flip($diffProducts);
        echo sprintf(
            'Following %s products has been found from all %s checked products in file %s:',
                count($intersectProducts),
                count($checkedProducts),
                $file
        );
        echo '<pre>' . print_r($intersectProducts, true) . '</pre>';
        echo '<br><br>';
        echo sprintf(
            'Following %s products has NOT been found from all %s checked products in file %s:',
                count($diffProducts),
                count($checkedProducts),
                $file
        );
        echo '<pre>' . print_r($diffProducts, true) . '</pre>';
        break;
        
    case 'whatIsMyIp':
        $this->setLayout(false);
        echo $_SERVER['REMOTE_ADDR'];
        break;
    
    case 'test':
        App::setJsFiles(array(
            '/app/js/libs/Validate.js',
            '/app/js/libs/NewClass.js',
            '/app/js/libs/NewChildClass.js',
        ));
        ?><div id="new-class-element"></div><?php
        ?><div id="new-child-class-element"></div><?php
        App::startJsCapture();
        ?><script type="text/javascript">
            new Run.SomeModule.NewClass({selector: '#new-class-element'});
            new Run.SomeModule.NewChildClass({selector: '#new-child-class-element'});
        </script><?php
        App::endJsCapture();
        break;
    
    case 'findGapsInOrderNumbers':
        App::loadModel('Eshop', 'EshopOrder');
        $Order = new EshopOrder();
        $numbers = $Order->findList(array(
            'fields' => array(
                'number'
            ),
            'order' => 'id DESC'
        ));
        $nextNumber = null;
        foreach ($numbers as $number) {
            if ($nextNumber === null) {
                $nextNumber = $number;
                continue;
            }
            if (($diff = $nextNumber - $number) > 1) {
                echo $nextNumber . ' - ' . $number . ' = ' . $diff . '<br>';
            }
            $nextNumber = $number;
        }
        break;
        
    case 'getParams':
        echo '<xmp>' . print_r($_GET, true) . '</xmp>';
        break;
    
    case 'normalizePhones':
        App::loadModel('App', 'UserProfile');
        $Profile = new UserProfile();
        $Profile->update(
            array('phone' => null),
            array(
                'conditions' => array('phone' => ''),
                'normalize' => false,
                'validate' => false,
            )
        );
        $phones = $Profile->findList(array(
            'fields' => array('phone'),
            'conditions' => array(
                'phone !=' => array(null, '')
            ),
        ));
        
        // keep backup
        file_put_contents(
            TMP . DS . 'normalizePhonesBackup_' . date('Ymd_His') . '.php', 
            '<?php $phonesBackup=' . Arr::getLiteral($phones) . ';'
        );
        
        $validPhones = array();
        $almostValidPhones = array();
        $invalidPhones = array();
        $failedPhones = array(
            '__empty__' => 0,
            '__spacesonly__' => 0,
        );
        $batch = array();
        foreach ($phones as $id => $phone) {
            $phoneParts = preg_split('/[,;]/', $phone);
            $lastPhone = array_pop($phoneParts);
            $normalizedPhone = preg_replace('/[^\+0-9]/i', '', $lastPhone);
            $normalizedPhoneLength = strlen($normalizedPhone);
            // valid phones (+...)
            // to verify if the phone number is really valid use https://numverify.com/
            if (
                $phone === $normalizedPhone
                && $normalizedPhoneLength >= 11
                && $normalizedPhoneLength <= 14
                && substr($normalizedPhone, 0, 1) === '+'
            ) {
                $validPhones[] = $phone;
            }
            // almost valid phones (0NNNNNNNNN without +421...)
            elseif (
                $phone === $normalizedPhone
                && $normalizedPhoneLength === 10
                && preg_match('/^0[1-9]/', $normalizedPhone)
            ) {
                // skip valid numbers
                $normalizedPhone = '+421' . substr($normalizedPhone, 1);
                $almostValidPhones[$phone] = $normalizedPhone;
                $batch[] = array(
                    'id' => $id,
                    'phone' => $normalizedPhone,
                );
            }
            // invalid phones
            else {
                // 0NNNNNNNNN => +421NNNNNNNNN
                if (
                    $normalizedPhoneLength === 10
                    && preg_match('/^0[1-9]/', $normalizedPhone)
                ) {
                    $normalizedPhone = '+421' . substr($normalizedPhone, 1);
                }
                // 9NNNNNNNN => +4219NNNNNNNN
                elseif (
                    $normalizedPhoneLength === 9
                    && preg_match('/^9/', $normalizedPhone)
                ) {
                    $normalizedPhone = '+421' . $normalizedPhone;
                }
                // 421... => +421...
                elseif (
                    $normalizedPhoneLength >= 10
                    && $normalizedPhoneLength <= 13
                    && preg_match('/^[1-9]/', $normalizedPhone)
                ) {
                    $normalizedPhone = '+' . $normalizedPhone;
                }
                // 0421... => +421...
                elseif (
                    $normalizedPhoneLength >= 11
                    && $normalizedPhoneLength <= 14
                    && preg_match('/^0[1-9]/', $normalizedPhone)
                ) {
                    $normalizedPhone = '+' . substr($normalizedPhone, 1);
                }
                // 00421..., 0033..., 00... => +421..., +33.., +...
                elseif (
                    $normalizedPhoneLength >= 12
                    && $normalizedPhoneLength <= 15
                    && preg_match('/^00[1-9]/', $normalizedPhone)
                ) {
                    $normalizedPhone = '+' . ltrim($normalizedPhone, '0');
                }
                // +421..., +33..., +... => +421..., +33.., +...
                elseif (
                    $normalizedPhoneLength >= 11
                    && $normalizedPhoneLength <= 14
                    && preg_match('/^\+[1-9]/', $normalizedPhone)
                ) {
                    // these are numbers which are valid after normalization
                }
                // 0042109..., 042109..., +42109... => +4219...
                elseif (
                    preg_match('/^(\+|0|00)?4210[1-9]/', $normalizedPhone)
                    && ($normalizedPhone2 = ltrim($normalizedPhone, '+0'))
                    && strlen($normalizedPhone2) === 13
                ) {
                    $normalizedPhone = '+421' . substr($normalizedPhone2, 4);
                }
                // USA numbers, e.g. **************
                elseif (
                    ($lastPhone = trim($lastPhone))
                    && substr($lastPhone, 0, 2) ===  '1-'
                ) {
                    $normalizedPhone = '+' . $normalizedPhone;
                }
                else {
                    if ($phone === '' || $phone === null) {
                        $failedPhones['__empty__']++;
                    }
                    elseif (trim($phone) === '') {
                        $failedPhones['__spacesonly__']++;
                    }
                    else {
                        $failedPhones[$phone] = $normalizedPhone;
                    }
                    $batch[] = array(
                        'id' => $id,
                        'phone' => null,
                    );
                    $normalizedPhone = 'failed';
                }
                if ($normalizedPhone !== 'failed') {
                    $invalidPhones[$phone] = $normalizedPhone;
                    $batch[] = array(
                        'id' => $id,
                        'phone' => $normalizedPhone,
                    );
                }
            }
        }
        
        echo '<xmp>total:' . count($phones) . '</xmp>';
        echo '<br><br><br>';
        echo '<xmp>failed total:' . count($failedPhones) . '</xmp>';
        echo '<xmp>' . print_r($failedPhones, true) . '</xmp>';
        echo '<br><br><br>';
        echo '<xmp>invalid total:' . count($invalidPhones) . '</xmp>';
        echo '<xmp>' . print_r($invalidPhones, true) . '</xmp>';
        echo '<br><br><br>';
        echo '<xmp>almost valid total:' . count($almostValidPhones) . '</xmp>';
        echo '<xmp>' . print_r($almostValidPhones, true) . '</xmp>';
        echo '<br><br><br>';
        echo '<xmp>valid total:' . count($validPhones) . '</xmp>';
        echo '<xmp>' . print_r($validPhones, true) . '</xmp>';
        //*/
        $Profile->saveBatch(array(
            'update' => array(
                'UserProfile' => $batch,
            )
        ));
        //*/
        break;
        
    case 'setOrderUsers':
        /*/
        // order users are set according to emails (used to rapir incorrectly created 
        // orders with incorrectly forced quick order)
        App::loadModel('Eshop', 'EshopOrder');
        $Order = new EshopOrder();
        $orderEmails = $Order->findList(array(
            'conditions' => array(
                'id >' => 32123,
                'run_users_id' => null
            ),
            'fields' => array('email')
        ));
        App::loadModel('App', 'User');
        $User = new User();
        $emailUserIds = $User->findList(array(
            'conditions' => array(
                'email' => array_unique($orderEmails)
            ),
            'key' => 'email',
            'fields' => array('id')
        ));
        $orders = array();
        foreach ($orderEmails as $orderId => $orderEmail) {
            if (!empty($emailUserIds[$orderEmail])) {
                $orders[] = array(
                    'id' => $orderId,
                    'run_users_id' => $emailUserIds[$orderEmail],
                );
            }
        }
        echo '<xmp>' . print_r($orderEmails, true) . '</xmp>';
        echo '<xmp>' . print_r($emailUserIds, true) . '</xmp>';
        echo '<xmp>' . print_r($orders, true) . '</xmp>';
        $Order->saveBatch(array(
            'update' => array(
                'EshopOrder' => $orders
            )
        ));
        //*/
        break;
    
    case 'getActualConversionRate':
        App::loadModel('Eshop', 'EshopCurrency');
        $Currency = new EshopCurrency();
        $czkRate = $Currency->getActualConversionRate('CZK');
        echo $czkRate;
        break;
    
    case 'movePrekvapcoProducts':
        App::loadModel('Eshop', 'EshopSpecialOfferPromotedProduct');
        $PromotedProduct = new EshopSpecialOfferPromotedProduct();
        $ids = $PromotedProduct->findList(array(
            'key' => 'run_eshop_products_id',
            'fields' => array('run_eshop_products_id'),
            'conditions' => array(
                'run_eshop_special_offers_id' => 7,
            ),
        ));
        echo count($ids) . ' promoted products found<br>';
        //break;
        $groupProducts = array();
        foreach ($ids as $id) {
            $groupProducts[] = array(
                'run_eshop_product_groups_id' => 26,
                'run_eshop_products_id' => $id,
            );
        }
        $PromotedProduct->saveBatch(array(
            'create' => array(
                'EshopProductGroupProduct' => $groupProducts,
            ),
        ));  
        echo count($groupProducts) . ' products migrated<br>';
        break;
    
    case 'normalizePhones':
        App::loadModel('App', 'UserProfile');
        $Profile = new UserProfile();
        $Profile->update(
            array('phone' => null),
            array(
                'conditions' => array('phone' => ''),
                'normalize' => false,
                'validate' => false,
            )
        );
        $phones = $Profile->findList(array(
            'fields' => array('phone'),
            'conditions' => array(
                'phone !=' => array(null, '')
            ),
        ));
        
        // keep backup
        file_put_contents(
            TMP . DS . 'normalizePhonesBackup_' . date('Ymd_His') . '.php', 
            '<?php $phonesBackup=' . Arr::getLiteral($phones) . ';'
        );
        
        $validPhones = array();
        $almostValidPhones = array();
        $invalidPhones = array();
        $failedPhones = array(
            '__empty__' => 0,
            '__spacesonly__' => 0,
        );
        $batch = array();
        foreach ($phones as $id => $phone) {
            $phoneParts = preg_split('/[,;]/', $phone);
            $lastPhone = array_pop($phoneParts);
            $normalizedPhone = preg_replace('/[^\+0-9]/i', '', $lastPhone);
            $normalizedPhoneLength = strlen($normalizedPhone);
            // valid phones (+...)
            // to verify if the phone number is really valid use https://numverify.com/
            if (
                $phone === $normalizedPhone
                && $normalizedPhoneLength >= 11
                && $normalizedPhoneLength <= 14
                && substr($normalizedPhone, 0, 1) === '+'
            ) {
                $validPhones[] = $phone;
            }
            // almost valid phones (0NNNNNNNNN without +421...)
            elseif (
                $phone === $normalizedPhone
                && $normalizedPhoneLength === 10
                && preg_match('/^0[1-9]/', $normalizedPhone)
            ) {
                // skip valid numbers
                $normalizedPhone = '+421' . substr($normalizedPhone, 1);
                $almostValidPhones[$phone] = $normalizedPhone;
                $batch[] = array(
                    'id' => $id,
                    'phone' => $normalizedPhone,
                );
            }
            // invalid phones
            else {
                // 0NNNNNNNNN => +421NNNNNNNNN
                if (
                    $normalizedPhoneLength === 10
                    && preg_match('/^0[1-9]/', $normalizedPhone)
                ) {
                    $normalizedPhone = '+421' . substr($normalizedPhone, 1);
                }
                // 9NNNNNNNN => +4219NNNNNNNN
                elseif (
                    $normalizedPhoneLength === 9
                    && preg_match('/^9/', $normalizedPhone)
                ) {
                    $normalizedPhone = '+421' . $normalizedPhone;
                }
                // 421... => +421...
                elseif (
                    $normalizedPhoneLength >= 10
                    && $normalizedPhoneLength <= 13
                    && preg_match('/^[1-9]/', $normalizedPhone)
                ) {
                    $normalizedPhone = '+' . $normalizedPhone;
                }
                // 0421... => +421...
                elseif (
                    $normalizedPhoneLength >= 11
                    && $normalizedPhoneLength <= 14
                    && preg_match('/^0[1-9]/', $normalizedPhone)
                ) {
                    $normalizedPhone = '+' . substr($normalizedPhone, 1);
                }
                // 00421..., 0033..., 00... => +421..., +33.., +...
                elseif (
                    $normalizedPhoneLength >= 12
                    && $normalizedPhoneLength <= 15
                    && preg_match('/^00[1-9]/', $normalizedPhone)
                ) {
                    $normalizedPhone = '+' . ltrim($normalizedPhone, '0');
                }
                // +421..., +33..., +... => +421..., +33.., +...
                elseif (
                    $normalizedPhoneLength >= 11
                    && $normalizedPhoneLength <= 14
                    && preg_match('/^\+[1-9]/', $normalizedPhone)
                ) {
                    // these are numbers which are valid after normalization
                }
                // 0042109..., 042109..., +42109... => +4219...
                elseif (
                    preg_match('/^(\+|0|00)?4210[1-9]/', $normalizedPhone)
                    && ($normalizedPhone2 = ltrim($normalizedPhone, '+0'))
                    && strlen($normalizedPhone2) === 13
                ) {
                    $normalizedPhone = '+421' . substr($normalizedPhone2, 4);
                }
                // USA numbers, e.g. **************
                elseif (
                    ($lastPhone = trim($lastPhone))
                    && substr($lastPhone, 0, 2) ===  '1-'
                ) {
                    $normalizedPhone = '+' . $normalizedPhone;
                }
                else {
                    if ($phone === '' || $phone === null) {
                        $failedPhones['__empty__']++;
                    }
                    elseif (trim($phone) === '') {
                        $failedPhones['__spacesonly__']++;
                    }
                    else {
                        $failedPhones[$phone] = $normalizedPhone;
                    }
                    $batch[] = array(
                        'id' => $id,
                        'phone' => null,
                    );
                    $normalizedPhone = 'failed';
                }
                if ($normalizedPhone !== 'failed') {
                    $invalidPhones[$phone] = $normalizedPhone;
                    $batch[] = array(
                        'id' => $id,
                        'phone' => $normalizedPhone,
                    );
                }
            }
        }
        
        echo '<xmp>total:' . count($phones) . '</xmp>';
        echo '<br><br><br>';
        echo '<xmp>failed total:' . count($failedPhones) . '</xmp>';
        echo '<xmp>' . print_r($failedPhones, true) . '</xmp>';
        echo '<br><br><br>';
        echo '<xmp>invalid total:' . count($invalidPhones) . '</xmp>';
        echo '<xmp>' . print_r($invalidPhones, true) . '</xmp>';
        echo '<br><br><br>';
        echo '<xmp>almost valid total:' . count($almostValidPhones) . '</xmp>';
        echo '<xmp>' . print_r($almostValidPhones, true) . '</xmp>';
        echo '<br><br><br>';
        echo '<xmp>valid total:' . count($validPhones) . '</xmp>';
        echo '<xmp>' . print_r($validPhones, true) . '</xmp>';
        /*/
        $Profile->saveBatch(array(
            'update' => array(
                'UserProfile' => $batch,
            )
        ));
        //*/
        break;
        
    case 'getActualConversionRate':
        App::loadModel('Eshop', 'EshopCurrency');
        $Currency = new EshopCurrency();
        $czkRate = $Currency->getActualConversionRate('CZK');
        echo $czkRate;
        break;
    
    case 'unsetFollowingItemsInLoop':
        echo 'foreach: ';
        $arr = array(1,2,3,4,5,6,7,8,9);
        foreach($arr as $key=>$value) {
            unset($arr[$key + 1]);
            echo $value . ' ';
        }
        echo '<br>!!!POZOR: foreach si vytvorí internú kópiu poľa cez ktorú iteruje a tá je pre nás nedosiahnuteľná. Jednak navyšuje použitú pamäť, a jednak nefunguje odstráňovanie položiek za behu cyklu (s tým že by to bolo v danom cykl zohľadnené)<br><br>';
        
        echo 'while + key + current + next: ';
        $arr = array(1,2,3,4,5,6,7,8,9);
        reset($arr);
        while (($key = key($arr)) !== null) {
            $value = current($arr); // or $value = $arr[$key] - but current() should be optimal
            next($arr);
            
            unset($arr[$key + 1]);
            echo $value . ' ';
        }
        echo '<br><br>';
        
        echo 'while + each (DEPRECATED as of PHP 7.2.0.): ';
        $arr = array(1,2,3,4,5,6,7,8,9);
        while (list($key, $value) = each($arr)) {
            unset($arr[$key + 1]);
            echo $value . ' ';
        }
        echo '<br><br>';
        
        echo 'use "while + key + current + unset" to optimally move/convert content of one huge array to another: ';
        $arr1 = array(1,2,3,4,5,6,7,8,9);
        $arr2 = array();
        echo '<br>' . 
            'arr1 (before move): ' . print_r($arr1, true) . '<br>' . 
            'arr2 (before move): ' . print_r($arr2, true) . '<br>';
        reset($arr1);
        while (($key = key($arr1)) !== null) {
            $value = current($arr1); // or $value = $arr1[$key] - but current() should be optimal
            $arr2[$key] = 2 * $value;
            unset($arr1[$key]);
        }
        echo 'arr1 (after move): ' . print_r($arr1, true) . '<br>' . 
            'arr2 (after move): ' . print_r($arr2, true) . '<br>' . 
            'You don\'t need to move pointer. After unset() is the next item always the first one.<br><br>';
        break;
    
    case 'tryCatchFinally':
        echo 'exception case:<br><br>';
        try{
            try {
                echo 'throwing testing exception<br>';
                throw new Exception('Testing exception thrown by .../_debug/tryCatchFinally');
            } 
            catch (Exception $e) {
                echo 'catching the testing exception and rethrowing<br>';
                throw $e;
            }
            finally {
                echo 'doing some final work...<br>';
            }
        } 
        catch (Exception $e) {
            echo 'catching the rethrowed testing exception<br>';
        }
        
        echo '<br><br>no exception case:<br><br>';
        try{
            try {
                echo 'doing something without exception<br>';
            } 
            catch (Exception $e) {
                echo 'catching the testing exception and rethrowing<br>';
                throw $e;
            }
            finally {
                echo 'doing some final work...<br>';
            }
        } 
        catch (Exception $e) {
            echo 'catching the rethrowed testing exception<br>';
        }
        break;
    
    case 'saveBatch':
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $Product->saveBatch(array(
            'update' => array(
                'EshopProduct' => array(
                    array(
                        'id' => '252892',
                        'price' => '10.5',
                        'description' => ':))',
                        'availability' => 'enum_available',
                    ),
                    array(
                        'id' => '252891',
                        'price' => 25,
                        'description' => '',
                        'availability' => 'enum_available',
                    ),
                )
            )
        ), array(
            'ignoreNewValueIf' => 'ignore'
        ));
        break;
    
    case 'repairDuplicitCodes':
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $Product->reserveTables('_debug_repairDuplicitCodes', array(
            'EshopProduct',
        ));
        $duplicitCodes = $Product->findDuplicit('code', array(
            'ignoreSoftDeleted' => false,
        ));
        if (!$duplicitCodes) {
            echo '<xmp>No duplicit codes</xmp>';
            break;
        }
        echo '<xmp>' . count($duplicitCodes) . '</xmp>';
        echo '<xmp>' . print_r($duplicitCodes, true) . '</xmp>';
        $products = $Product->find(array(
            'conditions' => array(
                'code' => array_keys($duplicitCodes)
            ),
            'fields' => array(
                'id', 'code', 'created'
            ),
            'order' => array(
                'code ASC',
                'created ASC',
            ),
            'ignoreSoftDeleted' => false,
        ));
        echo '<xmp>' . print_r($products, true) . '</xmp>';
//        $idCounts = array();
        $laterProducts = array();
        foreach ($products as $product) {
//            if (empty($idCounts[$product['id']])) {
//                $idCounts[$product['id']] = 0;
//            }
//            $idCounts[$product['id']]++;
            if (
                empty($laterProducts[$product['code']])
                || $laterProducts[$product['code']]['created'] < $product['created']
            ) {
                $laterProducts[$product['code']] = $product;
            }
        }
//        echo '<xmp>' . print_r($idCounts, true) . '</xmp>';
        echo '<xmp>' . print_r($laterProducts, true) . '</xmp>';
        $productNextCode = $Product->getNextCode();
        foreach ($laterProducts as &$product) {
            $product['code'] = $productNextCode++;
        }
        unset($product);
        echo '<xmp>' . print_r($laterProducts, true) . '</xmp>';
        $Product->saveBatch(array(
            'update' => array(
                'EshopProduct' => $laterProducts,
            ),
        ));
        $Product->unreserveTables('_debug_repairDuplicitCodes');
        break;
    
    case 'getSourceSettingsInserts';
        App::loadModel('App', 'Migration');
        $Migration = new Migration();
        $inserts = $Migration->getSourceSettingsInserts();
        echo '<xmp>' . print_r($inserts, true) . '</xmp>';
        break;
    
    case 'testUpdate':
        App::loadModel('Eshop', 'EshopManufacturer');
        $Manufacturer = new EshopManufacturer();
        $Manufacturer->update(
            array(
                'EshopManufacturer.seo_title' => 'Jamaha',
                'EshopManufacturerRange.seo_title' => 'Flautos',
                'EshopManufacturer.modified' => 'NOW()',
                'EshopManufacturerRange.modified' => 'NOW()',
            ),
            array(
                'lang' => 'en',
                'conditions' => array(
                    'EshopManufacturer.id' => 169,
                    'OR',
                    'EshopManufacturer.name' => 'Jamaha',
                    'OR',
                    'EshopManufacturerRange.name' => 'Flautos',
                ),
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopManufacturerRange',
                    )
                ),
                'literals' => array(
                    'fields' => array(
                        'EshopManufacturer.modified',
                        'EshopManufacturerRange.modified',
                    ),
//                    'data' => array(
//                        'NOW()',
//                    ),
                ),
                'updateModificationDatetime' => false,
                'reserve' => false,
            )
        );
        break;

    case 'test':
        $result = DB::query('SHOW ENGINE INNODB STATUS;');
        echo '<xmp>' . print_r($result, true) . '</xmp>';
        break;
    
    case 'checkForOrdersOfRecreatedProductsAfterCleanUp':
        App::setSqlLogging(false);
        $backupFile = 
            'app' . DS . 'modules' . DS . 'Eshop' . DS . 'config' . DS . 'cleanedUpExportedProducts.php';
        if (!is_readable(ROOT . DS . $backupFile)) {
            echo sprintf('Súbor %s nie je dostupný', $backupFile);
            break;
        }
        // these are exported products to MRP - but finally all deleted products were already exported
        // so this is list of all deleted products
        App::loadScript($backupFile, array('catchVariables' => 'exportedProducts'), $vars);
        $exportedProducts = &$vars['exportedProducts'];
        App::loadModel('Eshop', 'EshopOrderProduct');
        $OrderProduct = new EshopOrderProduct();
        $products = $OrderProduct->find(array(
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'EshopProduct',
                ),
                array(
                    'type' => 'left',
                    'model' => 'EshopOrder',
                ),
            ),
            'fields' => array(
                'EshopOrder.id',
                'EshopOrder.number',
                'EshopOrder.created',
                'EshopOrder.exported',
                'EshopOrderProduct.id',
                'EshopOrderProduct.number',
                'EshopProduct.id',
                'EshopProduct.ean',
                'EshopProduct.code',
                'EshopProduct.name',
                'EshopProduct.slug',
                'EshopProduct.supplier_pid',
                'EshopProduct.created',
                'EshopProduct.modified',
                'EshopProduct.exported',
            ),
            'conditions' => array(
                'EshopProduct.ean' => array_keys($exportedProducts),
                'EshopProduct.created >' => '2019-10-28 10:50:00', // date of cleanup
            ),
            'separate' => true,
            'order' => 'EshopOrder.created DESC',
        ));
        echo '<xmp>' . count($products) . '</xmp>';
        echo '<xmp>' . print_r($products, true) . '</xmp>';
        break;
        
    case 'setExportedEansAndCodes':
        App::setSqlLogging(false);
        $backupFile = 
            'app' . DS . 'modules' . DS . 'Eshop' . DS . 'config' . DS . 'cleanedUpExportedProducts.php';
        if (!is_readable(ROOT . DS . $backupFile)) {
            echo sprintf('Súbor %s nie je dostupný', $backupFile);
            break;
        }
        App::loadScript($backupFile, array('catchVariables' => 'exportedProducts'), $vars);
        $exportedProducts = &$vars['exportedProducts'];
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $products = $Product->find(array(
            'fields' => array(
                'id',
                'ean',
                'code',
                'name',
                'slug',
                'stock',
                'availability',
                'available_from',
                'supplier_pid',
                'created',
                'modified',
                'exported',
            ),
            'conditions' => array(
                'ean' => array_keys($exportedProducts),
                'created >' => '2019-10-28 10:50:00', // date of cleanup
            ),
            'order' => 'created',
        ));
        $batch = array();
        if ($products) {
            foreach ($products as $product) {
                if (empty($exportedProducts[$product['ean']])) {
                    continue;
                }
                $batch[] = array(
                    'id' => $product['id'],
                    'code' => $exportedProducts[$product['ean']],
                );
            }
            if ($batch) {
                $Product->saveBatch(array(
                    'update' => array(
                        'EshopProduct' => $batch,
                    )
                ));
            }
        }
        echo '<xmp>' . count($exportedProducts) . '</xmp>';
        echo '<xmp>' . count($products) . '</xmp>';
        echo '<xmp>' . count($batch) . '</xmp>';
        echo '<xmp>' . print_r($products, true) . '</xmp>';
        break;
    
    case 'testUpdateImports':
        App::loadLib('Eshop', 'PemicAvailabilityCsvReader');
        $Reader = new PemicAvailabilityCsvReader(array(
            'useFullCatalogue' => false,
            'accessId' => '524B85B3-33D3-48FE-AF7C-49DAD52B3DF1', // vydavatel.sk access
        ));
        
//        App::loadLib('Eshop', 'IkarCatalogueXmlReader');
//        $Reader = new IkarCatalogueXmlReader(array(
//            'username' => '434888',
//            'password' => 'Zjavkova',
//        ));
        
        while ($record = $Reader->readRecord()) {
            /*///debug>
            /*///<debug
            if (!empty($record) && $record['ean'] === '9788099952004') {
                $x = 1;
            }
            $record;
        }
        break;
    
    case 'testGet':
        echo '<pre>' . print_r($_GET, true) . '</pre>';
        echo '<pre>' . print_r(App::$parsedUrl, true) . '</pre>';
        break;
    
    case 'testNbsp':
        $nbsp = ' ';
        for ($i = 0; $i < strlen($nbsp); $i++) {
            echo dechex(ord(substr($nbsp, $i, 1))) . '<br>'; //C2 A0
        }
        $nbsp = html_entity_decode('&nbsp;');
        for ($i = 0; $i < strlen($nbsp); $i++) {
            echo dechex(ord(substr($nbsp, $i, 1))) . '<br>'; //C2 A0
        }
        echo strtolower('ľščťĽŠČŤ') . '<br>';
        echo mb_strtolower('ľščťĽŠČŤ') . '<br>';
        break;
        
    case 'testPartnerTechnic2':
        App::loadLib('Eshop', 'PartnerTechnicAvailabilityXmlReader');
        $Reader = new PartnerTechnicAvailabilityXmlReader(array(
            'accessKey' => '00224134X0000101',
            'loadPrice' => true,
        ));
        $records = array();
        for ($i = 0; $i <= 200; $i++) {
            if (!($record = $Reader->readRecord())) {
                break;
            }   
            $records[] = $record;
        }
        echo '<pre>' . print_r($records, true) . '</pre>';
        break;
    
    case 'testPartnerTechnic1':
        App::loadLib('Eshop', 'PartnerTechnicCatalogueXmlReader');
        $Reader = new PartnerTechnicCatalogueXmlReader(array(
            'accessKey' => '00224134X0000101',
        ));
        $records = array();
        for ($i = 0; $i <= 200; $i++) {
            if (!($record = $Reader->readRecord())) {
                break;
            }   
            $records[] = $record;
        }
        echo '<pre>' . print_r($records, true) . '</pre>';
        break;
    
    case 'exportProductsForOdoo':
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $products = $Product->find(array(
            'fields' => array(                    
                'CONCAT("EshopProduct_", EshopProduct.id) AS external_id',
                'EshopProduct.name',
                'EshopProduct.price',
                'EshopProduct.stock',
                'EshopProduct.description',
                'EshopProduct.image',
                'EshopProduct.created',
                'EshopProduct.modified',
            ),
            'conditions' => array(
                'EshopProduct.active' => true,
                'EshopProduct.availability' => 'enum_available',
                'EshopProduct.image !=' => null,
            ),
            'literals' => array(
                'fields' => array(
                    'CONCAT("EshopProduct_", EshopProduct.id) AS external_id',
                    'IF(EshopProduct.image AND EshopProduct.image != "image_47183_19_v1.jpeg", CONCAT("https://www.vydavatel.sk/userfiles/Eshop/EshopProduct/image/original/", EshopProduct.image), "") AS image_url',
                ),
            ),
            'limit' => (int)Sanitize::value($this->args[2], 500),
            'page' => (int)Sanitize::value($this->args[1], 1),
        ));
        foreach ($products as &$product) {
            $imagePath = $Product->getFileFieldPath('image', array(
                'file' => $product['image'],
                'variant' => 'original',
            ));
            if (is_readable(ROOT . DS .$imagePath)) {
                $product['image'] = App::getUrl(array(
                    'locator' => $Product->getFileFieldUrlPath('image', array(
                        'file' => $product['image'],
                        'variant' => 'original',
                    )),
                    'absolute' => true,
                ));
            }
            else {
                $product['image'] = null;
            }
            // add explicit fields
            $product['category'] = 'product.product_category_all';
        }
        unset($product);
        $Product->export(
            $products, 
            array(
                'file' => 'odooProducts',
                'output' => 'file',
                'headers' => array(
                    "Externé ID",
                    "Meno",
                    "Predajná cena",
                    "Množstvo skladom",
                    "Popis na webstránku",
                    "Obrázok",
                    "Vytvorené",
                    "Posledná modifikácia",
                    "Kategória produktu"
                ),
                'csvForceEnclosure' => true,
            )
        );
        break;
        
    case 'synchronizeDiscountRate':
        App::loadModel('Eshop', 'EshopSupplierProduct');
        $SupplierProduct = new EshopSupplierProduct();
        $SupplierProduct->synchronizeDiscountRate(array(
            'updatedOnly' => false,
        ));
        break;
    
    case 'synchronizeSupplierProducts':
        App::loadModel('Eshop', 'EshopSupplierProduct');
        $SupplierProduct = new EshopSupplierProduct();
        $SupplierProduct->synchronize(array(
            'updatedOnly' => false,
        ));
        break;
    
    case 'testAppParse':
        $parsedUrl = App::parseUrl('/produkt/menovka-levandula/16#accessories');
        echo '<pre>' . print_r($parsedUrl, true) . '</pre>';
        break;

    case 'refactorMailerGroups':
        // Relationship between mailer contact and mailer group was changed from 1 - 1 to  1 - n
        // Database data refactoring is here
        App::loadModel('Mailer', 'MailerContact');
        $MailerContact = new MailerContact();
        App::loadModel('Mailer', 'MailerGroupContact');
        $MailerGroupContact = new MailerGroupContact();
        $contacts = $MailerContact->find(array(
            'fields' => array(
                'id AS run_mailer_contacts_id', 
                'run_mailer_groups_id',
            ),
        ));
        App::setSqlLogging(false);
        $MailerGroupContact->delete(array(
            'conditions' => array(
                'id >' => 0
            )
        ));
        if (!empty($contacts)) {
            $MailerGroupContact->saveBatch(array(
                'create' => array(
                    'MailerGroupContact' => &$contacts,
                ),
            ));
        }
        break;

    case 'refactorTermsAndConditions':
        $slug = App::getSetting('Eshop', 'EshopOrder.termsAndConditionsLocator');
        if ($slug === null) {
            echo 'Nastavenie "EshopOrder.termsAndConditionsLocator" je už refaktorované';
            break;
        }
        $slug = trim($slug, '/');
        $pages = array();
        if ($slug) {
            $pages = DB::select('run_web_contents', array(
                'conditions' => array(
                    'locator' => $slug,
                )
            ));
        }
        if ($pages) {
            echo Str::fill('Pid "Eshop.termsAndConditions" bude nastavený nasledovným stránkam so slugom "%s":<br/>', $slug);
            foreach ($pages as $page) {
                echo $page['lang'] . ' > ' . $page['name'] . '(' . $page['locator'] . ')<br/>';
            }
            DB::update('run_web_contents', array(
                'pid' => 'Eshop.termsAndConditions',
                'permanent' => true,
            ), array(
                'conditions' => array(
                    'locator' => $slug,
                )
            ));
        }
        else {
            echo Str::fill('<b style="color:red">Nenašli sa žiadne stránky so slugom "%s". Vyhľadajte ich v DB ručne a nastavte im pid "Eshop.termsAndConditions"</b>', $slug);
        }
        echo '<br>';
        echo 'Refrešni admin.<br>';
        echo 'Nájdené stránky prejdi a ich obsah prekopíruj do nastavenia Eshop > Nastavenia > Obchodné podmienky (v príslušnom jazyku).<br>';
        echo 'Ako obsah týchto stránok stránky potom zadaj:<br>';
        echo '<xmp><object _snippet="e.Eshop.termsAndConditions" _snippet_generic="1" _snippet_name="Nastavenie obchodnych podmienok"></object></xmp>';
        DB::delete('run_settings', array(
            'conditions' => array(
                'module' => 'Eshop',
                'pid' => 'EshopOrder.termsAndConditionsLocator',
            )
        ));
        break;

    case 'matrixUnfolding':
        // https://stackoverflow.com/a/48048260/1245149
        // vyrába si zakaždým zvrtnutú podmaticu (nextMatrix) a vždy z nej zoberie prvý riadok
        // (firstRow). Keďže zvrtnutú podmaticu vyraba rekurzívne z predchádzajúcej zvrtnutej
        // tak matica sa pekne točí a prvý riadok po každom pootočení obsahuje prvky
        // ktoré treba pridať do výslednéj rozvynutej postupnosti
        // Je to milé riešenie (vrtenie matice) ale neefektívne (vrtenie matice)
        App::startJsCapture();
        ?><script type="text/javascript">
(function () {
    let array_masalah = [
      [11, 12, 13],
      [21, 22, 23],
      [31, 32, 33],
      [41, 42, 43]
    ];

    function polaSpiral(array_masalah) {
      function spiral(array) {
        if (array.length === 1) {
          return array[0];
        }

        var firstRow = array[0],
          numRows = array.length,
          nextMatrix = [],
          newRow, rowIdx, colIdx = array[1].length - 1;

        for (colIdx; colIdx >= 0; colIdx--) {
          newRow = [];

          for (rowIdx = 1; rowIdx < numRows; rowIdx++) {
            newRow.push(array[rowIdx][colIdx]);
          }

          nextMatrix.push(newRow);
        }

        firstRow.push.apply(firstRow, spiral(nextMatrix));
        return firstRow;
      }

      console.log(spiral(array_masalah));
    }
    debugger;
    polaSpiral(array_masalah);
})();
        </script><?php
        App::endJsCapture();
        break;    

    case 'cleanProducts':
        $productIds = array(
            255476, 255482, 255484, 255485, 255486, 255490, 255491, 255513, 255514, 255516, 255517, 255518, 255521, 255523, 255524, 255525, 255526, 255527, 255528, 255529, 255530, 255531, 255532, 255533, 255534, 255535, 255536, 255537, 255538, 255539, 255540, 255541, 255542, 255543, 255564, 255570, 255574, 255579, 255598, 255599, 255600, 255601, 255602, 255603, 255604, 255605, 255606, 255607, 255608, 255609, 255610, 255619, 255620, 255621
        );
        $Product = App::loadModel('Eshop', 'EshopProduct', true);
        $Product->delete(array(
            'conditions' => array(
                'id' => $productIds,
            ),
            'softDelete' => false,
        ));
        // remove records in following related Models
        $relatedModels = array(
            'EshopProductAuthor',
            'EshopProductCategoryProduct',
            'EshopProductGroupProduct',
            'EshopAccessoryProduct',
            'EshopRelatedProduct',
            'EshopWishlistProduct',
            'EshopProductAttribute',
            'EshopProductImage',
        );
        foreach ($relatedModels as $model) {
            $Model = App::loadModel('Eshop', $model, true);
            $Model->delete(array(
                'conditions' => array(
                    'run_eshop_products_id' => $productIds,
                ),
                'softDelete' => false,
            ));
        }
        break;
    
    case 'testSlovartCatalogueXmlReader2':
        App::loadLib('Eshop', 'SlovartCatalogueXmlReader');
        $this->Reader = new SlovartCatalogueXmlReader(array(
            'catalogueFile' => null, //'slovart-b2b.txt',
            'accessKey' => '7d9e2a6dc299a32e686817793ad8e985',
        ));
        $records = array();
        while (
            ($record = $this->Reader->readRecord())
        ) {
            $records[] = $record;
        }
        echo '<pre>' . print_r($records, true) . '</pre>';
        break;
        
    case 'testSlovartCatalogueXmlReader':
        App::loadLib('Eshop', 'SlovartCatalogueXmlReader');
        $this->Reader = new SlovartCatalogueXmlReader(array(
            'catalogueFile' => 'slovart-b2b.txt',
            'accessKey' => '...',
        ));
        $bindings = $languages1 = $languages2 = $departments = $themes = array();
        $languages2Counts = array();
        $i = 0;
        while (
            ($record = $this->Reader->readRecord())
        ) {
            if (!empty($record['binding'])) {
                if (empty($bindings[$record['binding']])) {
                    $bindings[$record['binding']] = 0;
                }
                $bindings[$record['binding']]++;
            }
            if (!empty($record['debug_language_group'])) {
                if (empty($languages1[$record['debug_language_group']])) {
                    $languages1[$record['debug_language_group']] = 0;
                }
                $languages1[$record['debug_language_group']]++;
            }
            if (!empty($record['debug_languages'])) {
                foreach ($record['debug_languages'] as $language) {
                    if (empty($languages2[$language])) {
                        $languages2[$language] = 0;
                    }
                    $languages2[$language]++;
                }
                $count = count($record['debug_languages']);
                if (empty($languages2Counts[$count])) {
                    $languages2Counts[$count] = 0;
                }
                $languages2Counts[$count]++;
            }
            if (!empty($record['debug_department_id'])) {
                $departments[$record['debug_department_id']] = $record['debug_department_name'];
            }
            if (!empty($record['debug_theme_id'])) {
                $themes[$record['debug_theme_id']] = $record['debug_theme_name'];
            }
            if ($i++ % 1000 === 0) {
                $x = 1;
            }
        }
        $languages2 = array_unique($languages2);
        asort($languages2);
        asort($departments);
        asort($themes);
        echo '<br><br>VAZBY (pocet: ' . count($bindings) . ') ---<br>';
        foreach ($bindings as $binding => $count) {
            echo $binding . ' (pocet: ' . $count . ') -> <br>';
        }
        echo '<br><br>JAZYKY ZAKLADNE ROZDELENIE (pocet: ' . count($languages1) . ') ---<br>';
        foreach ($languages1 as $language => $count) {
            echo $language . ' (pocet: ' . $count . ') -> <br>';
        }
        echo '<br><br>JAZYKY POUZITE V KNIHE (pocet: ' . count($languages2) . ') ---<br>';
        foreach ($languages2 as $language => $count) {
            echo $language . ' (pocet: ' . $count . ') -> <br>';
        }
        echo '<br><br>PODLA POCTU JAZYKOV KNIHY ---<br>';
        foreach ($languages2Counts as $numLanguages => $count) {
            echo $numLanguages . ' jazyk/y/ov (pocet: ' . $count . ') -> <br>';
        }
        echo '<br><br>ODDELENIA (pocet: ' . count($departments) . ') ---<br>';
        foreach ($departments as $departmentId => $department) {
            echo $departmentId . ' (' . $department . ') -> <br>';
        }
        echo '<br><br>KATEGORIE (pocet: ' . count($themes) . ') ---<br>';
        foreach ($themes as $themeId => $theme) {
            echo $themeId . ' (' . $theme . ') -> <br>';
        }
        break;
    
    case 'exportMrpOrder':
        $Export = App::loadModel('Eshop', 'EshopExport', true);
        $progress = $Export->exportMrpOrdersXml(array(
            'orderIds' => 51718,
            //'sendToMrp' => true,
            'simulate' => true,
        ));
        echo '<pre>' . print_r($progress, true) . '</pre>'; 
        break;
    
    case 'test':
        echo $this->args[1] === 0 ? 'y' : 'n' . '<br>';
        echo $this->args[1] === '0' ? 'y' : 'n' . '<br>';
        echo gethostname() . '<br>'; //mojo
        echo $_SERVER['HTTP_HOST'] . '<br>'; //core-v2
        echo $_SERVER['SERVER_NAME'] . '<br>'; //core-v2
        echo $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] . '<br>'; //core-v2
        break;

    case 'testReferenceToReference':
        $v1 = array(1, 2, 3);
        $v2 = &$v1;
        $v3 = &$v2;
        echo '<pre>' . print_r($v3, true) . '</pre>';
        unset($v3[1]);
        echo '<pre>' . print_r($v1, true) . '</pre>';
        echo '<pre>' . print_r($v2, true) . '</pre>';
        unset($v2[0]);
        echo '<pre>' . print_r($v1, true) . '</pre>';
        echo '<pre>' . print_r($v3, true) . '</pre>';
        break;

    case 'testMagicConstantClass':
        echo EshopProduct::class;
        break;

    case 'testForeach':
        // this works the same in PHP 5 & 7
        // test it here: http://sandbox.onlinephpfunctions.com/
        $array = array(10, 11, 12, 13, 14);
        foreach ($array as $k => $v) {
            $array[4] = 24;
            if ($v === 12) {
                unset($array[$k]);
            }
            if ($v === 13) {
                $array[$k] = 23;
            }
            echo $k . ':' . $v . '<br>';
        }
        echo '<pre>' . print_r($array, true) . '</pre>';
        break;

    case 'loadCountryData':
        $file = 'https://raw.githubusercontent.com/mledoze/countries/master/countries.json';
        $jsonCountries = json_decode(file_get_contents($file), true);
        if (!$jsonCountries) {
            echo __d(
                __FILE__,
                'Download of file %s has failed',
                $file
            );
            break;
        }
        App::loadModel('App', 'Country');
        $Country = new Country();
        $dbCountries = $Country->findList(array(
            'key' => 'iso_code_2',
            'fields' => 'id',
        ));
        $batch = array();
        foreach ($jsonCountries as $jsonCountry) {
            if (!isset($dbCountries[$jsonCountry['cca2']])) {
                continue;
            }
            $dbCountry = array(
                'id' => $dbCountries[$jsonCountry['cca2']]
            );
            unset($dbCountries[$jsonCountry['cca2']]);

            // edit following to load needed items
            $dbCountry['calling_code'] = 0;
            if (!empty($jsonCountry['callingCode'])) {
                $dbCountry['calling_code'] = reset($jsonCountry['callingCode']);
            }
            $dbCountry['endonym'] = '';
            if (!empty($jsonCountry['name']['native'])) {
                // there can be more languages, take the first of them
                $native = reset($jsonCountry['name']['native']);
                if (!empty($native['common'])) {
                    $dbCountry['endonym'] = $native['common'];
                }
                elseif (!empty($native['official'])) {
                    $dbCountry['endonym'] = $native['official'];
                }
            }
//            $dbCountry['name'] = '';
//            // slovak name (after loading make visual control of all names - there are some 3-4 to repair)
//            if (!empty($jsonCountry['translations']['slk']['common'])) {
//                $dbCountry['name'] = $jsonCountry['translations']['slk']['common'];
//            }
////            // english name
////            if (!empty($jsonCountry['name']['common'])) {
////                $dbCountry['name'] = $jsonCountry['name']['common'];
////            }
            $dbCountry = $Country->normalize($dbCountry);
            $batch[] = $dbCountry;
        }
//        App::logDebug('countries batch', array(
//            'var' => $batch,
//        ));
        $Country->saveBatch(
            array(
                'update' => array(
                    'Country' => &$batch
                )
            ),
            array(
                'ignoreNewValueIf' => array(0, '', null)
            )
        );
        echo __d(__FILE__, 'Done. %s countries has been updated', count($batch));
        if ($dbCountries) {
            echo '<br>' . __d(__FILE__, 'Following countries were not updated: %s', implode(', ', array_keys($dbCountries)));
        }
        break;

    case 'testElasticSearch':
//        // https://search-elastic-test-01-zt5ldyauvqz26dswynbk3vmqfy.eu-central-1.es.amazonaws.com/bank/_search?q=daniel&sort=account_number:asc&pretty
//        $url = App::getUrl([
//            'absolute' => true,
//            'base' => 'https://search-elastic-test-01-zt5ldyauvqz26dswynbk3vmqfy.eu-central-1.es.amazonaws.com',
//            'locator' => 'bank/_search',
//            'get' => [
//                'q' => 'daniel',
//                'sort' => 'account_number:asc',
//                'pretty' => null,
//            ],
//        ]);
//        $response = App::request(
//            //'https://search-elastic-test-01-zt5ldyauvqz26dswynbk3vmqfy.eu-central-1.es.amazonaws.com/bank/_search?q=daniel&sort=account_number:asc&pretty'
//            $url
//        );
//        echo '<pre>' . print_r($response, true) . '</pre><br>';
//        break;

        // https://www.google.com/search?q=elasticsearch+ecommerce+example&oq=elasticsearch+ecommerce+&aqs=chrome.5.69i57j69i60j0l4.17963j0j7&sourceid=chrome&ie=UTF-8
        //
        // [1] https://project-a.github.io/on-site-search-design-patterns-for-e-commerce/
        // [2] Detailnejšie rozvedenie článku [1]: https://codeburst.io/elasticsearch-by-example-part-1-a4a38cd97f55 (spolu 5 častí)
        // [3] Zdá sa že implementuje postupy z článku [1]: https://github.com/spryker/search
        // [4] Porovnanie ES a Solr: https://stackoverflow.com/a/********/1245149
        // [5] Schema z [1] prepísaná v kóde [3] do verzie pre ES 5.x: https://github.com/spryker/search/blob/master/src/Spryker/Shared/Search/IndexMap/search.json
        //
        // https://www.elastic.co/guide/en/elasticsearch/reference/current/getting-started-concepts.html
        //
        // Glossary of terms: https://www.elastic.co/guide/en/elasticsearch/reference/current/glossary.html
        // ES cheatsheet: http://elasticsearch-cheatsheet.jolicode.com/
        // Blog o ES: https://codingexplained.com/coding/elasticsearch
        // Index settings: https://www.elastic.co/guide/en/elasticsearch/reference/current/index-modules.html (viď aj zoznam na konci)
        // ES analyza textu:
        //      - https://www.elastic.co/guide/en/elasticsearch/reference/current/analyzer-anatomy.html
        //      - https://codingexplained.com/coding/elasticsearch/understanding-analysis-in-elasticsearch-analyzers
        //
        // ES pre slovenčinu:
        //      - https://github.com/SlovakNationalGallery/elasticsearch-slovencina (stopwords)
        //      - https://github.com/essential-data/elasticsearch-sk
        //      - https://www.slideshare.net/lab_SNG/elasticsearch-po-slovensky
        //      - https://www.zdrojak.cz/clanky/elasticsearch-vyhledavame-cesky/
        // Slovníky synoným:
        //      - stačí rozbaliť: https://extensions.openoffice.org/en/project/slovak-dictionary-package-slovenske-slovniky
        //      - tu je popísaný formát slovníku z predchádzajúceho linku: https://www.openoffice.org/lingucomponent/MyThes-1.zip > data_layout.txt
        //      - rôzné slovníky: http://www.sk-spell.sk.cx/files/
        //      - dal by sa rozparsovať z HTLM (~12000 slov, 423 stránok): http://slovnik.dovrecka.sk/synonymicky-slovnik/strana-1
        //      - prípadne pozri aj odkazy tu: https://www.elastic.co/guide/en/elasticsearch/reference/6.4/analysis-hunspell-tokenfilter.html
        //
        // Ben Corlett - Discovering ElasticSearch at Laracon EU 2014: https://www.youtube.com/watch?v=waTWeJeFp4A
        // AWS PHP API: https://docs.aws.amazon.com/sdk-for-php/v3/developer-guide/welcome.html

        // When creating index and defining its types:
        //      - 'text' => full-text searches
        //      - 'keyword' => exact matches
        //
        // When searching:
        //      - 'term' for exact match (used for 'keyword' type)
        //      - 'match' for full-text search (used for 'text' type)
        //      - 'range' for numbers
        //      - 'bool' for AND, OR
        //      - 'must' vs 'filter' - 'must' sets score, 'filter' not
        //      -
        //

        // Otázky na Tomáša:
        // - Ako odlíšil data jednotlivých webov? (index?, príznak?)
        // - Použil nejakú všeobecnú schému alebo konkrétnu (podľa stĺpcov)?
        // - Kde sa dá pozrieť jeho kód?
        // - Má nejaké odporúčanie k použitiu ES?
        //


        // document structure
        //
        //      {
        //          owner: EshopProduct | WebContent
        //          data : {
        //              owner_id:
        //              owner_model:
        //
        //          }
        //
        //      }
        //

        // sudo systemctl start elasticsearch.service @deprecated (use AWS)
        // sudo systemctl stop elasticsearch.service @deprecated (use AWS)
        //
        // PHP API: https://www.elastic.co/guide/en/elasticsearch/client/php-api/6.0
        // PHP administracne API: https://www.elastic.co/guide/en/elasticsearch/client/php-api/6.0/_namespaces.html
        $client = Elasticsearch\ClientBuilder::create()
            ->setHosts([
                [
                    'host' => 'search-elastic-test-01-zt5ldyauvqz26dswynbk3vmqfy.eu-central-1.es.amazonaws.com',
                    'port' => '',
                    'scheme' => 'https',
                ]
            ])
            ->build();

//        $document = [
//            'index' => 'customer',
//            'type' => '_doc',
//            'id' => '2',
//            'body' => [
//                'name' => 'John Doolittle'
//            ]
//        ];
//        $response = $client->index($document);

//        $document = [
//            'index' => 'customer',
//            'type' => '_doc',
//            'id' => '2',
//            'body' => [
//                'doc' => [
//                    'name' => 'Jane Doe'
//                ]
//            ]
//        ];
//        $response = $client->update($document);

        $indices = $client->indices();

//        $response = $indices->analyze([
//            'body' => [
////                'analyzer' => 'standard',
////                'analyzer' => 'english',
////                'text' => 'The QUICK brown foxes jumped over the lazy dog!',
////                'analyzer' => 'czech',
////                'text' => 'Jakési RÝCHLE líštičky přeskočili přes líného psa!',
//                'analyzer' => [
//                    'default' => [
//                        'tokenizer' => 'standard',
//                        'filter' => ['asciifolding'],
//                    ],
//                ],
//                'text' => 'Jakési RÝCHLE líštičky přeskočili přes líného psa!',
//            ]
//        ]);
//        echo '<pre>' . print_r($response, true) . '</pre><br>';

        $responseDeleteIndex = $indices->delete([
            'index' => ['analyzer_tests'],
        ]);
        echo '<pre>' . print_r($responseDeleteIndex, true) . '</pre><br>';

        $responseCreateIndex = $indices->create([
            'index' => 'analyzer_tests',
            'body' => [
                'settings' => [
                    'analysis' => [
                        // https://www.elastic.co/guide/en/elasticsearch/reference/6.4/analyzer-anatomy.html
                        'analyzer' => [
                            'my_analyzer_01' => [
                                'type' => 'custom',
                                'char_filter' => [
                                    'html_strip',
                                    'my_char_filter',
                                ],
                                'tokenizer' => 'standard', // An analyzer must have exactly one tokenizer
                                'filter' => [
                                    'stopwords_sk',
                                    'lowercase',
                                    'synonyms_sk',
                                    'asciifolding',
                                ],
                            ],
                        ],
                        'char_filter' => [
                            'my_char_filter' => [
                                'type' => 'mapping',
                                'mappings' => [
                                    "٠ => 0",
                                    "١ => 1",
                                    "٢ => 2",
                                    "٣ => 3",
                                    "٤ => 4",
                                    "٥ => 5",
                                    "٦ => 6",
                                    "٧ => 7",
                                    "٨ => 8",
                                    "٩ => 9"
                                ],
                            ],
                        ],
                        'filter' => [
                            'stopwords_sk' => [
                                'type' => 'stop',
                                'stopwords' => [
                                    'a','aby','aj','ak','ako','ale','alebo','and','ani','áno','asi','až','bez','bude','budem','budeš','budeme','budete','budú','by','bol','bola','boli','bolo','byť','cez','čo','či','ďalší','ďalšia','ďalšie','dnes','do','ho','ešte','for','i','ja','je','jeho','jej','ich','iba','iné','iný','som','si','sme','sú','k','kam','každý','každá','každé','každí','kde','keď','kto','ktorá','ktoré','ktorou','ktorý','ktorí','ku','lebo','len','ma','mať','má','máte','medzi','mi','mna','mne','mnou','musieť','môcť','môj','môže','my','na','nad','nám','náš','naši','nie','nech','než','nič','niektorý','nové','nový','nová','nové','noví','o','od','odo','of','on','ona','ono','oni','ony','po','pod','podľa','pokiaľ','potom','práve','pre','prečo','preto','pretože','prvý','prvá','prvé','prví','pred','predo','pri','pýta','s','sa','so','si','svoje','svoj','svojich','svojím','svojími','ta','tak','takže','táto','teda','te','tě','ten','tento','the','tieto','tým','týmto','tiež','to','toto','toho','tohoto','tom','tomto','tomuto','toto','tu','tú','túto','tvoj','ty','tvojími','už','v','vám','váš','vaše','vo','viac','však','všetok','vy','z','za','zo','že',
                                ],
                                'ignore_case' => true,
                            ],
                            // toto je vhodné použiť len pri analyzovaní zadaných vyhľadávaných
                            // slov (t.j. nie pri analyzovaní uloižených dat)
                            'synonyms_sk' => [
                                'type' => 'synonym',
                                'synonyms' => [
                                    'pes, psisko, psiska',
                                ]
                            ]
                        ],
                    ]
                ]
            ],
        ]);
        echo '<pre>' . print_r($responseCreateIndex, true) . '</pre><br>';

        $responseTestAnalyzer = $indices->analyze([
            'index' => 'analyzer_tests',
            'body' => [
//                'analyzer' => 'standard',
//                'analyzer' => 'english',
//                'text' => 'The QUICK brown foxes jumped over the lazy dog!',
//                'analyzer' => 'czech',
//                'text' => 'Jakési RÝCHLE líštičky přeskočili přes líného psa!',
                'analyzer' => 'my_analyzer_01',
                'text' => 'Akési <b>RÝCHLE</b> líštičky preskočili cez lenivého psiska, ktorý tam ležal asi 3 dni. To sú arabské čísla: ٠‎١٢٣٤٥٦٧٨‎٩!',
            ]
        ]);
        echo '<pre>' . print_r($responseTestAnalyzer, true) . '</pre><br>';


        break;


        $document = [
            'index' => 'bank',
            'type' => '_doc',
            'body' =>
//'{
//  "query": {
//    "bool": {
//      "must": [
//        { "match": { "address": "mill" } },
//        { "match": { "address": "lane" } }
//      ]
//    }
//  }
//}',

//'{
//  "query": {
//    "bool": {
//      "should": [
//        { "match": { "address": "mill" } },
//        { "match": { "address": "lane" } }
//      ]
//    }
//  }
//}',

//'{
//  "query": {
//    "bool": {
//      "must": { "match_all": {} },
//      "filter": {
//        "range": {
//          "balance": {
//            "gte": 20000,
//            "lte": 30000
//          }
//        }
//      }
//    }
//  }
//}',

//'{
//  "query": {
//    "bool": {
//      "should": [
//        { "match": { "address": "mill" } },
//        { "match": { "address": "lane" } }
//      ],
//      "filter": {
//        "range": {
//          "balance": {
//            "gte": 20000,
//            "lte": 30000
//          }
//        }
//      }
//    }
//  }
//}',

'{
  "size": 0,
  "aggs": {
    "group_by_state": {
      "terms": {
        "field": "state.keyword",
        "order": {
          "average_balance": "desc"
        }
      },
      "aggs": {
        "average_balance": {
          "avg": {
            "field": "balance"
          }
        }
      }
    }
  }
}',

        ];
        $response = $client->search($document);

        echo '<pre>' . print_r($response, true) . '</pre><br>';
        break;

    case 'test':
        App::loadModel('App', 'User');
        $User = new User();
        $User->find(array(
            'fields' => array(
                'nonexisting_field'
            )
        ));
        break;

    case 'mergeEshopErrorsPoFileAfterCleanUp':
        $commandTemplate = 'msgcat --use-first :file1: :file2: -o :file2:';
        '/app/modules/Eshop/locale/Eshop_sk_SK.po';
        $inserts = array(
            ':file1:' => ROOT . FIle::normalizeDS('/app/modules/Eshop/locale/Eshop_sk_SK.po'),
            ':file2:' => ROOT . FIle::normalizeDS('/app/modules/Eshop/locale/EshopErrors_sk_SK.po'),
        );
        $command = str_replace(array_keys($inserts), $inserts, $commandTemplate);
        exec($command);
        echo '<strong style="color:red">Súbor app/modules/Eshop/locale/EshopErrors_sk_SK.po otvor v textovom editore a riadok 17 zmeň z "X-Poedit-KeywordsList: __:2\n" na "X-Poedit-KeywordsList: __e:2\n". Potom ho otvor v poEditore a aktualizuj</strong>';
        break;

    case 'test':
        App::loadModel('Eshop', 'EshopOrder');
        $Order = new EshopOrder();
        $result = $Order->checkTatrabankaPaymentStatus(36879);
        App::debug($result); //debug
        break;
    
    case 'testSpecialOffer':
        App::loadModel('Eshop', 'EshopSpecialOffer');
        $Offer = new EshopSpecialOffer();
        $offers = $Offer->getActive(array(
            'promotedProductIds' => array(
                244244, // ŠP1
                234622, // ŠP2
            ),
            'cartPrice' => 20.5, // ŠP3
            'discountedProductIds' => array(
                237797, // ŠP1
//                235111, // ŠP2
                243084, // ŠP3
            ),
        ));
        App::debug($offers); //debug
        break;
    
    case 'mergePoFilesAfterCleanUp':
        $commandTemplate = 'msgcat --use-first :file1: :file2: -o :file1:';
        $modules = App::getModules();
        foreach ($modules as $module) {
            //if ($module !== 'App') continue; //debug
            $modulePath = App::getModulePath($module);
            $localePath = $modulePath . DS . 'locale';
            if (!file_exists(ROOT . $localePath)) {
                continue;
            }
            $items = @scandir(ROOT . $localePath);
            if (!empty($items)) {
                foreach ($items as $item) {
                    $file = $localePath . DS . $item;
                    $match = array();
                    if (
                        is_dir(ROOT . $file)
                        ||
                        !preg_match("/^$module([a-z]*)_([a-z]{2}_[a-z]{2})\.po$/i", $item, $match)
                    ) {
                        continue;
                    }
                    $subdomain = $match[1]; // '', 'Admin', 'Errors', 'Validations'
                    $locale = $match[2]; //'sk_SK', 'en_GB'
                    // merge __() translations to __a(), __e(), __v() translations
                    if (empty($subdomain)) {
                        foreach (array('Admin', 'Errors', 'Validations') as $updateSubdomain) {
                            $updatedFile = $localePath . DS . "{$module}{$updateSubdomain}_{$locale}.po";
                            $templateFile = File::normalizeDS(
                                "/misc/templates/NewModule/locale/NewModule{$updateSubdomain}_{$locale}.po"
                            );
                            if (
                                !file_exists(ROOT . $updatedFile)
                                && is_readable(ROOT . $templateFile)
                            ) {
                                copy(ROOT . $templateFile, ROOT . $updatedFile);
                            }
                            if (is_writable(ROOT . $updatedFile)) {
                                $inserts = array(
                                    ':file1:' => ROOT . $updatedFile,
                                    ':file2:' => ROOT . $file,
                                );
                                $command = str_replace(array_keys($inserts), $inserts, $commandTemplate);
                                exec($command);
                                //echo '<br><small>spustil sa príkaz: "' . $command . '"</small>'; //debug
                            }
                        }
                    }
                    echo "<br><strong>Súbor <span style='color:blue'>$file</span> otvor v poEditore a aktalizuj ho</strong>";
                }
            }
        }
        echo "<br><br><strong style='color:red'>Krátky návod: choď do locale/ priečinku v každom používanom module (App, ContentBlock, Eshop, Mailer, ...) a v poEditore otvor a aktualizuj všetky .po súbory okrem _js.po súborov</strong>";
        break;

    case 'testJsonApi':
        App::setJsFiles(array(
            '/app/js/vendors/jquery.min.js',
            '/app/js/libs/PhpJs.js',
            '/app/js/libs/Number.js',
            '/app/js/libs/Utility.js',
            '/app/js/libs/Validate.js',
            '/app/js/libs/Sanitize.js',
            '/app/js/libs/App.js',
        ));
        App::setJsConfig('App', array(
            'urlRoot' => App::$urlRoot,
            'urlLang' => App::$urlLang,
            'urlBase' => App::$urlBase,
            'homeSlug' => App::$homeSlug,
        ));
App::startJsCapture();
?><script type="text/javascript">

// use of promises
//Run.App.App.requestApi('/_debug/testRawInput/', {
//Run.App.App.requestApi('/_debug/testRawInput/1', {
Run.App.App.requestApi('/mvc/App/Countries/find', {
    fields:['id', 'name'],
    conditions: {'name *~': 'a'},
    order: 'name DESC',
    limit: 3
})
.then(response => console.log(response))
.catch(error => console.log(error));

// use of async functions
(async function () {
    try {
//        response = await Run.App.App.requestApi('/_debug/testRawInput', {
//        response = await Run.App.App.requestApi('/_debug/testRawInput/1', {
        response = await Run.App.App.requestApi('/mvc/App/Countries/find', {
            fields:['id', 'name'],
            conditions: {'name *~': 'a'},
            order: 'name DESC',
            limit: 3
        });
        console.log(response);
    }
    catch (error) {
        console.log(error);
    }
})();

</script><?php
App::endJsCapture();
        break;

    case 'test1':
        $response = App::request('https://flynwetsuits.com/api/catalog/vue_storefront_catalog/product/_search?size=50&from=0&sort=&_source_exclude=updated_at%2Ccreated_at%2Cattribute_set_id%2Coptions_container%2Cmsrp_display_actual_price_type%2Chas_options%2Cstock.manage_stock%2Cstock.use_config_min_qty%2Cstock.use_config_notify_stock_qty%2Cstock.stock_id%2Cstock.use_config_backorders%2Cstock.use_config_enable_qty_inc%2Cstock.enable_qty_increments%2Cstock.use_config_manage_stock%2Cstock.use_config_min_sale_qty%2Cstock.notify_stock_qty%2Cstock.use_config_max_sale_qty%2Cstock.use_config_max_sale_qty%2Cstock.qty_increments%2Csmall_image', array(
            'method' => 'POST',
            'data' => '{"query":{"bool":{"filter":{"bool":{"must":[{"terms":{"visibility":[2,3,4]}},{"terms":{"status":[0,1]}},{"terms":{"stock.is_in_stock":[true]}},{"terms":{"category_ids":[15,23,26,16,22,37,39]}}]}}}},"aggs":{"agg_terms_color":{"terms":{"field":"color"}},"agg_terms_color_options":{"terms":{"field":"color_options"}},"agg_terms_size":{"terms":{"field":"size"}},"agg_terms_size_options":{"terms":{"field":"size_options"}},"agg_terms_price":{"terms":{"field":"price"}},"agg_range_price":{"range":{"field":"price","ranges":[{"from":0,"to":50},{"from":50,"to":100},{"from":100,"to":150},{"from":150}]}},"agg_terms_erin_recommends":{"terms":{"field":"erin_recommends"}},"agg_terms_erin_recommends_options":{"terms":{"field":"erin_recommends_options"}}}}',
            'header' => 'Content-Type: application/json',
        ));
        echo $response;
        break;

    case 'test2':
        $response = App::request('https://flynwetsuits.com/api/catalog/vue_storefront_catalog/attribute/_search?size=50&from=0&sort=', array(
            'method' => 'POST',
            'data' => '{"query":{"bool":{"filter":{"terms":{"attribute_code":["color","size","price","erin_recommends"]}}}}}',
            'header' => 'Content-Type: application/json',
        ));
        echo $response;
        break;

    case 'test3':
        $response = App::request('https://vuejobs.com/api/positions/search/?jobs_per_page=100');
        echo $response;
        break;

    case 'test4':
        $response = App::request('https://www.alza.sk/Services/EShopService.svc/Filter', array(
            'method' => 'POST',
            'data' => '{"idCategory":18842920,"producers":"","parameters":[],"idPrefix":1776,"prefixType":1,"page":1,"pageTo":3,"inStock":false,"newsOnly":false,"commodityStatusType":0,"upperDescriptionStatus":0,"branchId":-1,"sort":2,"categoryType":4,"searchTerm":"","sendProducers":false,"layout":1,"append":true,"leasingCatId":null,"yearFrom":null,"yearTo":null,"artistId":null,"minPrice":-1,"maxPrice":-1,"shouldDisplayVirtooal":false,"callFromParametrizationDialog":false,"commodityWearType":null,"scroll":8135,"hash":"#f&cst=0&cud=0&pg=1-3&pn=2&prod=","counter":3}',
            'header' => 'Content-Type: application/json',
        ));
        echo $response;
        break;

    case 'test':
        $result = json_decode('{"a":"1"}', true);
        echo json_encode($result);
        echo '<br>';
        echo '<br>';
        $result = json_decode('{"a":"1}', true);
        echo json_encode($result); // NULL

        App::loadModel('App', 'User');
        $User = new User();
        $users = $User->find(array(
            'fields' => array('CONCAT(first_name, " ", last_name) AS name'),
            'literals' => true, // :(
            'literals' => 'tralala', // :(
            'literals' => array('fields' => 'tralala'), // :(
            'literals' => array('fields' => true), // :)
            'literals' => array('fields' => 'CONCAT(first_name, " ", last_name) AS name'), // :)
        ));
        echo '<br><br><pre>' . print_r($users, true) . '</pre>';

        App::setSqlLogging(false);
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            $users = $User->find();
        }
        Utility::getTimer(); // ~360ms - 550ms
        App::setSqlLogging();
        $users = $User->find();
        echo '<br><br><pre>' . print_r($users, true) . '</pre>';
        break;

    case 'testAllowedFindOptions':
        $allowedJoins = array(
            array(
                'type' => 'left',
                'model' => 'EshopProduct',
            ),
            array(
                'type' => 'left',
                'model' => 'User',
                'module' => 'App',
            ),
        );
        $requestJoins = array(
            array(
                'type' => 'left',
                'model' => 'EshopProduct',
            ),
            array(
                'type' => 'left',
                'module' => 'App',
                'model' => 'UserProfile',
                'toModel' => 'User',
                'toModule' => 'App',
            ),
            array(
                'type' => 'left',
                'module' => 'App',
                'model' => 'User',
            ),
        );
        $finalJoins = array();
        foreach($requestJoins as $key => $join) {
            if (in_array($join, $allowedJoins, false)) {
                $finalJoins[] = $join;
            }
        }
        echo '<br><br><pre>' . print_r($finalJoins, true) . '</pre>';

        $allowLiterals = array(
            'joins' => true,
            'fields' => true,
            'order' => 'CONCAT(name, surname) ESC',
            'conditions' => array(
                'CONCAT(name, surname) = "Trala La"'
            )
        );
        $requestLiterals = array(
            'joins' => true,
            'fields' => 'CONCAT(name, surname) AS fullname',
            'order' => true,
            'group' => true,
            'conditions' => array(
                'CONCAT(name, surname) = "Trala La"',
                'SUBSTR(name, 0, 1) = "a"',
            )
        );
        foreach($requestLiterals as $key => &$literal) {
            if (
                empty($allowLiterals[$key])
                || empty($literal)
            ) {
                unset($requestLiterals[$key]);
            }
            elseif ($literal === true) {
                $literal = $allowLiterals[$key];
            }
            elseif ($allowLiterals[$key] !== true) {
                $literal = array_intersect(
                    (array)$literal,
                    (array)$allowLiterals[$key]
                );
            }
        }
        echo '<br><br><pre>' . print_r($requestLiterals, true) . '</pre>';

        break;

    case 'testCallable':
        $func = function($a, $b) {
            echo "$a $b";
        };
        if (Validate::callableFunction($func)) {
            return call_user_func_array($func, array('is', 'callable'));
        }
        elseif (Validate::anonymousFunction($func)) {
            return $func($value, 'Validate', 'anonymousFunction');
        }
        break;

    /**
     * launch following js code in browser console:
     *
fetch('http://core-v1/_debug/testRawInput', {
    method: 'POST',
    body: JSON.stringify({fields:['id', 'name'], order: 'name ASC'}),
    headers:{
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(response => console.log(':) fetch success:', JSON.stringify(response)))
.catch(error => console.error(':( fetch error:', error));
     *
     */
    case 'testRawInput':
        App::setLayout(false);
        $input = App::getRawInput();
//        header($_SERVER['SERVER_PROTOCOL'] . ' 404 Not Found');
//        header('Status: 404 Not Found');
        echo $input;
        if (Sanitize::value($this->args[1])) {
            echo  '<br><br>$_REQUEST: <pre>' . print_r($_REQUEST, true) . '</pre><br>';
            echo  '<br><br>$_POST: <pre>' . print_r($_POST, true) . '</pre><br>';
            echo  '<br><br>$_SERVER: <pre>' . print_r($_SERVER, true) . '</pre><br>';
            echo  '<br><br>getallheaders(): <pre>' . print_r(getallheaders(), true) . '</pre><br>';
        }
        break;

    case 'testBackgroundRequestJsLauncher':
        $url = App::getUrl(array(
            'locator' => '_debug/testBackgroundRequest/js',
            'absolute' => true,
        ));
        App::log('testBackgroundRequest', 'js launcher request start');
        App::setJs("(function () {var http = new XMLHttpRequest(); http.open('GET', '$url'); http.send();})();");
        App::log('testBackgroundRequest', 'js launcher request end');
        break;

    case 'testBackgroundRequestPhpLauncher2':
        $id = uniqid();
        echo $id . '<br>';
        $url = App::getUrl(array(
            'locator' => '_debug/testBackgroundRequest/php/' . $id,
            'absolute' => true,
        ));
        App::log('testBackgroundRequest', 'php launcher request start ' . $id);
        try {
            App::request($url, array(
                'timeout' => Sanitize::value($this->args[1], 0.001),
                'returnResponse' => false,
            ));
        }
        catch (Throwable $e) {
            echo $e->getMessage() . '<br>';
            echo $e->getCode() . '<br>';
        }
        App::log('testBackgroundRequest', 'php launcher request end ' . $id);
        break;

    case 'testBackgroundRequestPhpLauncher':
        $id = uniqid();
        echo $id . '<br>';
        $url = App::getUrl(array(
            'locator' => '_debug/testBackgroundRequest/php' . $id,
            'absolute' => true,
        ));
        App::log('testBackgroundRequest', 'php launcher request start ' . $id);
        // !!! TOTO NEFUNGUJE - ak sa request neukončí do timeoutu tak to vyhodí exception
        // K "async" dopytom pozri toto:
        // - https://segment.com/blog/how-to-make-async-requests-in-php/
        // - https://stackoverflow.com/q/14359926/1245149
        App::request($url, array(
            'timeout' => 0.001
        ));
        App::log('testBackgroundRequest', 'php launcher request end ' . $id);
        break;

    case 'testBackgroundRequest':
        $launcherInfo = '';
        if (($launcherName = Sanitize::value($this->args[1]))) {
            $launcherInfo .= $launcherName . ' launcher ';
        }
        if (($launcherId = Sanitize::value($this->args[2]))) {
            $launcherInfo .= $launcherId;
        }
        $launcherInfo = trim($launcherInfo);
        if ($launcherInfo) {
            $launcherInfo = ' (' . $launcherInfo . ')';
        }
        App::log('testBackgroundRequest', sprintf('background request start%s', $launcherInfo));
        sleep(10);
        App::log('testBackgroundRequest', sprintf('background request end%s', $launcherInfo));
        break;

    case 'testClosureCompiler':
        /**
         * @todo skús porovnať výkon closure SIMPLE_OPTIMIZATIONS vs gulp-uglify
         *
         * @todo najprv by sa mohlo zavolať s 'output_format' => 'text' a keď to vráti
         * prázdny string pri zlyhaní (over že je to tak) tak zavolaš 2.x a zistíš chyby
         *
         * @todo !!! ak sa nepoužije ADVANCED_OPTIMIZATIONS tak SIMPLE_OPTIMIZATIONS aplikovaná
         * na gulp-uglified súbory vráti buď veľmi podobnú veľkosť alebo aj väčšiu,
         * t.j. v takomto prípade je výhodnejšie všetko len hoodiť do jedného súboru bez ďalšej
         * minifikácie. Je možné že keď sa minifikuje neminifikovaný subor (ktorý nie je vopred
         * zbalený cez gulp-uglify) tak colosure môže mať lepšie výsledky - lepšie sa môže vystopovať
         * nepoužitý kód. Treba to skúsiť. Mohlo by sa pri posielaní kódov do closure zisťovať či existuje
         * /sources/ verzia .js súboru a ak áno tak použiť túto na vstupe
         *
         * @todo viď nižšie "!!! odstraňovanie 'use strict'"
         *
         * @todo v *.js súboroch projektu vyhľadaj _isInheritable a nahraď to if (this.constructor === NewClass) {
         * Viď zmeny v core-v1/misc/templates/NewClass.js v revízii 2083:0ffeceaaa16e
         *
         * @todo pri kompilovaní by sa mohli vynechávať súbory obsahujúce v ceste '/vendors/'
         * a tieto by sa naopak mohli uvádzať ako externs
         *
         * @todo Ideálne by bolo keby sa zoznam .js súborov dal spárovať z príslušným bundle.js
         * bez hľadania v DB napríklad cez sha1() hash zoznamu .js súborov. Až v prípade keby
         * by zistilo že buď bundle neexistuje alebo niektorý zo súborov je novší ako bundle
         * tak by sa vytvorila úloha na vytvorenie bundle. POkiaľ by bundle neexistovalo tak by sa pripájali
         * .js súbory
         */
        $homePageJsScripts = array(
            "/app/js/vendors/jquery.min.js",
            "/app/js/libs/globalFunctions.js",
            "/app/js/vendors/jquery.mousewheel-3.0.6.pack.js",
            "/app/js/vendors/fancybox/jquery.fancybox.pack.js",
            "/app/js/libs/Validate.js",
            "/app/js/libs/Arr.js",
            "/app/js/libs/WindowStateManager.js",
            "/app/js/libs/SmartAdminLauncher.js",
            "/app/js/libs/jquery.parallax.js",
            "/app/js/vendors/bxslider/jquery.bxslider.min.js",
            "/app/js/libs/SecureForm.js",
        );
        $jsCode = '';
        foreach ($homePageJsScripts as $script) {
            $scriptCode = file_get_contents(File::getAbsolutePath(File::normalizePath($script)));
            $scriptCode = trim($scriptCode, ' ;') . ';';
            // @todo !!! odstraňovanie 'use strict' nefunguje to celkom ok - možno nie každý use strict je na začiatku riadku (?)
            // skús do niektorého skriptu pridat volanie arguments.callee a uvidíš či to ide
            $scriptCode = preg_replace('/^\'use strict\'/m', '', $scriptCode);
            $jsCode .= $scriptCode . PHP_EOL;
        }
//        $jsCode = 'alert("hello");// This comment should be stripped'; //debug

        // https://developers.google.com/closure/compiler/docs/api-tutorial1
        // https://developers.google.com/closure/compiler/docs/api-ref
        $data = array(
            'js_code' => $jsCode,
            'compilation_level' => 'WHITESPACE_ONLY',
            'compilation_level' => 'SIMPLE_OPTIMIZATIONS',
//            'compilation_level' => 'ADVANCED_OPTIMIZATIONS',
            'output_format' => 'json', // 'json', 'text'
//            'output_info' => 'compiled_code', // 'compiled_code', 'warnings', 'errors', 'statistics'
        );
        // add to output_info also 'warnings', 'errors' and'statistics'
        $data = http_build_query($data);
        $data .= '&output_info=warnings';
        $data .= '&output_info=errors';
        $data .= '&output_info=statistics';
//        $response = App::request('http://krasnasprava/_debug/testClosureCompilerRequest', array(
        $response = App::request('https://closure-compiler.appspot.com/compile', array(
            'method' => 'post',
            'userAgent' => null,
            'header' => 'Content-type: application/x-www-form-urlencoded',
            'data' => $data
        ));
        App::log('testClosureCompiler', 'response', array(
            'var' => json_decode($response, true),
        ));
        break;

    case 'jsObjectCopy':
        App::setJsFiles(array(
            '/app/js/vendors/jquery.min.js',
        ));
        App::startJsCapture();
?><script type="text/javascript">
(function() {
    {
        console.log('Object.assign() - shallow copy'); //debug
        let o1 = {a: 5, b: {c: 6}, d: [11, 12]}, o2;
        o2 = Object.assign({}, o1);
        console.log('o1', JSON.stringify(o1)); //debug
        console.log('o2', JSON.stringify(o2)); //debug
        o2.a = 4;
        o2.b.c = 7;
        o2.d[0] = 13;
        console.log('o1', JSON.stringify(o1), '!!!'); //debug !!!
        console.log('o2', JSON.stringify(o2)); //debug
    }{
        console.log('jQuery.extend() - shallow copy'); //debug
        let o1 = {a: 5, b: {c: 6}, d: [11, 12]}, o2;
        o2 = jQuery.extend({}, o1);
        console.log('o1', JSON.stringify(o1)); //debug
        console.log('o2', JSON.stringify(o2)); //debug
        o2.a = 4;
        o2.b.c = 7;
        o2.d[0] = 13;
        console.log('o1', JSON.stringify(o1), '!!!'); //debug !!!
        console.log('o2', JSON.stringify(o2)); //debug
    }{
        console.log('jQuery.extend() - deep copy'); //debug
        let o1 = {a: 5, b: {c: 6}, d: [11, 12]}, o2;
        o2 = jQuery.extend(true, {}, o1);
        console.log('o1', JSON.stringify(o1)); //debug
        console.log('o2', JSON.stringify(o2)); //debug
        o2.a = 4;
        o2.b.c = 7;
        o2.d[0] = 13;
        console.log('o1', JSON.stringify(o1), ':)'); //debug :)
        console.log('o2', JSON.stringify(o2)); //debug
    }{
        console.log('!!! jQuery.extend() - deep copy used to merge objects !!!'); //debug
        let o3 = {a: 5, b: {c: 6}, d: [11, 12]}, o4 = {a: 54, b: {c: [4]}, d: [4]};
        console.log('o3', JSON.stringify(o3)); //debug
        console.log('o4', JSON.stringify(o4)); //debug
        o3 = jQuery.extend(true, {}, o3, o4);
        console.log('o3 + o4', JSON.stringify(o3), '!!!'); //debug !!!
    }{
        console.log('jQuery.extend() - shallow copy used to merge deep copied objects'); //debug
        let o3 = {a: 5, b: {c: 6}, d: [11, 12]}, o4 = {a: 54, b: {c: [4]}, d: [4]};
        console.log('o3', JSON.stringify(o3)); //debug
        console.log('o4', JSON.stringify(o4)); //debug
        o3 = jQuery.extend({},  jQuery.extend(true, {}, o3),  jQuery.extend(true, {}, o4));
        console.log('o3 + o4', JSON.stringify(o3), ':)'); //debug :)
    }
})();
</script><?php
        App::endJsCapture();
        break;

    case 'test':
        echo Str::fill(
            'My name is :name:. My surname is :surname:',
            array('name' => 'Mojo', 'surname' => 'Ďurík')
        ) . '<br>';
        echo Str::fill(
            'My name is %s. My surname is %s',
            'Mojo',
            'Ďurík'
        ) . '<br>';
        echo Str::fill(
            'My name is %s. My surname is %s',
            array('Mojo', 'Ďurík')
        ) . '<br>';
        echo Str::fill(
            'My name is :name:. My surname is %s',
            array('name' => 'Mojo'),
            'Ďurík'
        ) . '<br>';
        echo Str::fill(
            'My name is :name:. My surname is %s',
            array('name' => 'Mojo'),
            null
        ) . '<br>';
        echo Str::fill(
            'My name is :name:. My surname is :surname:. Age is %s',
            array('Mojo', 'Ďurík')
        ) . '<br>';
        echo Str::fill(
            'My name is :name:. My surname is :surname:. Age is %s',
            array('Mojo', 'Ďurík'),
            23
        ) . '<br>';
        echo Str::fill(
            'My name is :name:. My surname is :surname:. Age is %s. My height is %s',
            array('name' => 'Mojo', 'Ďurík'),
            23
        ) . '<br>';

        echo '<br><br>';

        echo __d(__FILE__,
            'My name is :name:. My surname is :surname:',
            array('name' => 'Mojo', 'surname' => 'Ďurík')
        ) . '<br>';
        echo __d(__FILE__,
            'My name is %s. My surname is %s',
            'Mojo',
            'Ďurík'
        ) . '<br>';
        echo __d(__FILE__,
            'My name is %s. My surname is %s',
            array('Mojo', 'Ďurík')
        ) . '<br>';
        echo __d(__FILE__,
            'My name is :name:. My surname is %s',
            array('name' => 'Mojo'),
            'Ďurík'
        ) . '<br>';
        echo __d(__FILE__,
            'My name is :name:. My surname is %s',
            array('name' => 'Mojo'),
            null
        ) . '<br>';
        echo __d(__FILE__,
            'My name is :name:. My surname is :surname:. Age is %s',
            array('Mojo', 'Ďurík')
        ) . '<br>';
        echo __d(__FILE__,
            'My name is :name:. My surname is :surname:. Age is %s',
            array('Mojo', 'Ďurík'),
            23
        ) . '<br>';
        echo __d(__FILE__,
            'My name is :name:. My surname is :surname:. Age is %s. My height is %s',
            array('name' => 'Mojo', 'Ďurík'),
            23
        ) . '<br>';

        break;

    case 'testSimpleXMLElement':
        // compare difference in [item] format in following to cases!
        Html::startCapture();
        ?><record><?php
            ?><name>Record01</name><?php
            ?><items><?php
                ?><item><?php
                    ?><code>12</code><?php
                    ?><price>23</price><?php
                ?></item><?php
                ?><item><?php
                    ?><code>45</code><?php
                    ?><price>56</price><?php
                ?></item><?php
            ?></items><?php
        ?></record><?php
        $xml = Html::endCapture();
        $XmlRecord = new SimpleXMLElement($xml);
        echo '<code>' . print_r($XmlRecord, true) . '</code>'; // [item] => Array ( [0] => SimpleXMLElement Object ( [code] => 12 [price] => 23 ) [1] => SimpleXMLElement Object ( [code] => 45 [price] => 56 ) )

        Html::startCapture();
        ?><record><?php
            ?><name>Record01</name><?php
            ?><items><?php
                ?><item><?php
                    ?><code>12</code><?php
                    ?><price>23</price><?php
                ?></item><?php
            ?></items><?php
        ?></record><?php
        $xml = Html::endCapture();
        $XmlRecord = new SimpleXMLElement($xml);
        echo '<br><br><code>' . print_r($XmlRecord, true) . '</code>'; // [item] => SimpleXMLElement Object ( [code] => 12 [price] => 23 )
        break;

    case 'test':
        App::loadModel('App', 'ContentBlock');
        $parsed = Html::parseResponsiveCssValue('1/3; 768px:0.5; 480px:100%');
        App::debug($parsed); //debug
        $parsed = Html::parseResponsiveCssValue('1/3; 768px:0.5; 480px:100%', array(
            'allowNumericValues' => array('', '%')
        ));
        App::debug($parsed); //debug
        $parsed = Html::parseResponsiveCssValue('1/3; 768px:0.5; 480px:100%', array(
            'allowNumericValues' => array('%')
        ));
        App::debug($parsed); //debug
        $parsed = Html::parseResponsiveCssValue('1/3; 768px:50%; 480px:none', array(
            'allowValues' => array('none'),
            'allowNumericValues' => array('%'),
        ));
        App::debug($parsed); //debug
        $parsed = Html::parseResponsiveCssValue('transparent; 768px:rgba(255,255,255,0.5)', array(
            'allowValues' => array('transparent', 'rgba\(\d{1,3},\d{1,3},\d{1,3},0?\.?[0-9]+\)'),
            //'allowNumericValues' => array('%'),
        ));
        App::debug($parsed); //debug
        $parsed = Html::parseResponsiveCssValue('1/3; 768:1/2; 480px:100%');
        App::debug($parsed); //debug
        $parsed = Html::parseResponsiveCssValue('250px; 768:1/2; 480px:1');
        App::debug($parsed); //debug
        $parsed = Html::parseResponsiveCssValue('');
        App::debug($parsed); //debug
        $parsed = Html::parseResponsiveCssValue('250px; 768; 480px:1');
        App::debug($parsed); //debug
        $parsed = Html::parseResponsiveCssValue('250px; 768; 480px:1', array(
            'exceptionOnFailure' => true,
        ));
        App::debug($parsed); //debug
        break;

    case 'createUniqueIdTable':
        App::loadLib('App', 'UniqueId');
        $UniqueId = new UniqueId();
        break;

    case 'createUniqueIdTable':
        App::loadLib('App', 'UniqueId');
        $UniqueId = new UniqueId();
        break;
    
    case 'debugInvoice49618':
        // order No. 49618
        $invoice = '{"DocumentNumber":"1807806","IssueDate":"2018-11-08","CurrencyCode":"EUR","TaxCode":"10","ZeroTaxRateAmount":"0.00","ReducedTaxRateAmount":"104.88","BaseTaxRateAmount":"0.00","RoundingAmount":"0.00","ReducedTaxRateTax":"10.49","BaseTaxRateTax":"0.00","TotalWithTaxCurr":"0.00","TaxPointDate":"2018-11-08","DeliveryDate":"2018-11-08","DoubleEntryBookkeepingCode":"0.000","SingleEntryBookkeepingCode":"0","SingleEntryBookkeepingSubCode":"0","ControlStatement_Leasing":"F","TotalWeight":"11.405000","CostCentre":"100","ContractNumber":"K1202","VatRegime":"0","VatCountry":"SK","EURExchangeRate":"1.000000","EURExchangeRateAmount":"1.000000","ConstantSymbol":"008","PaymentDueDate":"2018-11-18","CurrRateAmount":"1.000000","CurrRate":"1.000000","PaidAmount":"0.00","PaidAmountCurr":"0.00","InvoiceType":"F","DeliveryNoteID":"1807806","PaymentMeansCode":"dobierka","OrderNumber":"0000049618","OrderDate":"2018-11-07","OriginalOrderNumber":"49618","Discount":"0.00","Company":{"CompanyId":"51765276","Name":"Ing. Jaroslav Gerec - PRE\u010cO KNIHY","CustomerName":"Gerec Jaroslav","Street":"Nadabula 153","City":"Ro\u017e\u0148ava","ZipCode":"04801","VatNumber":"1083703533","Phone":"0907951340","Email":"<EMAIL>","NaturalPerson":"F"},"Items":{"Item":[{"Description":"Pr\u00edbeh slovenskej hymny a jej autora","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"2.000000","UnitPrice":"5.364000","TaxPercent":"10.00","TaxAmount":"0.536000","DiscountPercent":"2.00","UnitDiscount":"0.107000","StockCardNumber":"1092.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.840000"},{"Description":"Hradba z\u00e1padu","RowType":"1","TaxCode":"10","Quantity":"1.000000","UnitPrice":"8.145000","TaxPercent":"10.00","TaxAmount":"0.814000","DiscountPercent":"1.00","UnitDiscount":"0.081000","StockCardNumber":"348371.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.000000"},{"Description":"S pokojom v du\u0161i","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"1.000000","UnitPrice":"8.782000","TaxPercent":"10.00","TaxAmount":"0.878000","DiscountPercent":"1.00","UnitDiscount":"0.088000","StockCardNumber":"345326.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.000000"},{"Description":"Karpatsk\u00e9 povesti","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"1.000000","UnitPrice":"3.545000","TaxPercent":"10.00","TaxAmount":"0.355000","DiscountPercent":"2.00","UnitDiscount":"0.071000","StockCardNumber":"865.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.355000"},{"Description":"Povesti o slovensk\u00fdch hradoch","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"1.000000","UnitPrice":"4.455000","TaxPercent":"10.00","TaxAmount":"0.446000","DiscountPercent":"2.00","UnitDiscount":"0.089000","StockCardNumber":"767.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.620000"},{"Description":"Krv\u00e1caj\u00faca hranica","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"3.000000","UnitPrice":"1.364000","TaxPercent":"10.00","TaxAmount":"0.136000","DiscountPercent":"2.00","UnitDiscount":"0.027000","StockCardNumber":"969.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.000000"},{"Description":"Drot\u00e1rska odysea","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"3.000000","UnitPrice":"2.273000","TaxPercent":"10.00","TaxAmount":"0.227000","DiscountPercent":"2.00","UnitDiscount":"0.045000","StockCardNumber":"813.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.565000"},{"Description":"Etnogen\u00e9za Slov\u00e1kov","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"3.000000","UnitPrice":"2.636000","TaxPercent":"10.00","TaxAmount":"0.264000","DiscountPercent":"2.00","UnitDiscount":"0.053000","StockCardNumber":"805.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.520000"},{"Description":"N\u00e1rod a \u0161t\u00e1t","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"3.000000","UnitPrice":"0.455000","TaxPercent":"10.00","TaxAmount":"0.046000","DiscountPercent":"2.00","UnitDiscount":"0.009000","StockCardNumber":"831.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.500000"},{"Description":"Divy Slovenska nielen pre deti alebo Vlastiveda ako lusk","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"1.000000","UnitPrice":"6.300000","TaxPercent":"10.00","TaxAmount":"0.630000","DiscountPercent":"2.00","UnitDiscount":"0.126000","StockCardNumber":"902.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.900000"},{"Description":"Divy Slovenska II","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"1.000000","UnitPrice":"4.455000","TaxPercent":"10.00","TaxAmount":"0.446000","DiscountPercent":"2.00","UnitDiscount":"0.089000","StockCardNumber":"1000.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.810000"},{"Description":"\u010cas slu\u017eobn\u00edkov diabla","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"3.000000","UnitPrice":"2.636000","TaxPercent":"10.00","TaxAmount":"0.264000","DiscountPercent":"2.00","UnitDiscount":"0.053000","StockCardNumber":"587.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.450000"},{"Description":"\u00dalomky \u017eeny 2","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"2.000000","UnitPrice":"8.518000","TaxPercent":"10.00","TaxAmount":"0.852000","DiscountPercent":"1.00","UnitDiscount":"0.085000","StockCardNumber":"336036.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.250000"},{"Description":"Rockov\u00e9 tango","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"1.000000","UnitPrice":"8.173000","TaxPercent":"10.00","TaxAmount":"0.817000","DiscountPercent":"1.00","UnitDiscount":"0.082000","StockCardNumber":"279772.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.000000"},{"Description":"Slovensk\u00e9 povesti M\u00e1rie \u010eur\u00ed\u010dkovej","RowType":"1","TaxCode":"10","UnitCode":"ks","Quantity":"1.000000","UnitPrice":"9.909000","TaxPercent":"10.00","TaxAmount":"0.991000","DiscountPercent":"32.00","UnitDiscount":"3.171000","StockCardNumber":"3824.00","CostCentre":"100","ContractNumber":"K1202","RowSumType":"1","TotalWeight":"0.435000"}]},"Payments":{"Payment":{"PaymentType":"0","Amount":"0.00","AmountCurr":"0.00","AmountPaidDocumentCurr":"0.00","CurrRate":"0.000000","CurrRateAmount":"0.000000"}}}';
        $invoice = json_decode($invoice);
        $totalAmount = 0.0;
        $totalDiscount = 0.0;
        $totalPrice = 0.0;
        $totalPrice2 = 0.0;
        $items = @(array)$invoice->Items->Item;
        ?><table><?php 
        foreach ($items as $item) {
            ?><tr><?php 
                ?><td><?php 
                    $code = Number::removeTrailingZeroDecimals(@(string)$item->StockCardNumber);
                    if (!empty($eans[$code])) {
                        echo $eans[$code];
                    }
                ?></td><?php
                ?><td><?php 
                    echo @(string)$item->Description
                ?></td><?php
                ?><td><?php 
                    $amount = (float)Number::removeTrailingZeroDecimals(@(string)$item->Quantity);
                    echo str_replace('.', ',', $amount);
                    $totalAmount += $amount;
                ?></td><?php
                ?><td><?php 
                    $unitPrice = (float)Number::removeTrailingZeroDecimals(@(string)$item->UnitPrice);
                    echo str_replace('.', ',', $unitPrice);
                ?></td><?php
                ?><td><?php 
                    $taxPercent = Number::removeTrailingZeroDecimals(@(string)$item->TaxPercent);
                    echo str_replace('.', ',', $taxPercent);
                    ?>%<?php
                ?></td><?php
                ?><td><?php 
                    $discountPercent = Number::removeTrailingZeroDecimals(@(string)$item->DiscountPercent);
                    if ($discountPercent) {
                        echo str_replace('.', ',', $discountPercent);
                        ?>%<?php
                    }
                ?></td><?php
                ?><td><?php 
                    $unitDiscount = (float)Number::removeTrailingZeroDecimals(@(string)$item->UnitDiscount);
                    $taxAmount = (float)Number::removeTrailingZeroDecimals(@(string)$item->TaxAmount);
                    $actualUnitPrice = $unitPrice + $taxAmount - $unitDiscount;
                    $actualPrice = $actualUnitPrice * $amount;
                    echo App::formatNumber($actualPrice);
                    $totalDiscount += $unitDiscount * $amount;
                    $totalPrice += $actualPrice;
                ?></td><?php
                ?><td><?php 
                    $actualUnitPrice2 = round(round(round($unitPrice, 3) * (100 + $taxPercent) / 100, 3) * (100 - $discountPercent) / 100, 3);
                    $actualPrice2 = $actualUnitPrice2 * $amount;
                    echo App::formatNumber($actualPrice2);
                    $totalDiscount += $unitDiscount * $amount;
                    $totalPrice2 += $actualPrice2;
                ?></td><?php
                ?><td>|</td><?php
                ?><td><?php 
                    $unitDiscount = (float)Number::removeTrailingZeroDecimals(@(string)$item->UnitDiscount);
                    echo str_replace('.', ',', $unitDiscount);
                ?></td><?php
            ?></tr><?php
        }
        ?></table><?php
        echo '<br>' . round($totalPrice, 2);
        echo '<br>' . round($totalPrice2, 2);
        App::startCssCapture();
        ?><style type="text/css">
            td {padding: 2px 10px;}
        </style><?php
        App::endCssCapture();
        break;
    
    case 'debugInvoice':
        App::loadModel('Eshop', 'EshopOrder');
        $Order = new EshopOrder();
        App::setLayout(false);
        if (!($orderNumber = Sanitize::value($this->args[1]))) {
            $orderNumber = '49618'; //'46788';
        }
        echo $Order->getInvoiceFile($orderNumber, array( // 41603 41608
            'format' => 'html',
//            'format' => 'pdf',
//            'output' => 'inline',
        ));
//        $orderId = $Order->findFieldBy('id', 'number', $orderNumber);
//        $Order->createInvoicePdfFile($orderId);
        break;
    
    case 'test':
        App::loadLib('Eshop', 'MrpRequest');
        $serverIpAddress = App::getSetting('Eshop', 'mrp.serverIpAddress');
        $serverPort = App::getSetting('Eshop', 'mrp.serverPort');
        $MrpRequest = new MrpRequest($serverIpAddress, $serverPort, array(
            'privateKey' => App::getSetting('Eshop', 'mrp.privateKey'),
        ));
        if (!($orderNumber = Sanitize::value($this->args[1]))) {
            $orderNumber = '46003';
        }
        $MrpResponse = $MrpRequest->getInvoices(array(
            //'DocumentNumber' => '1802739', // 1804749 1804607 1802739
            'OriginalOrderNumber' => $orderNumber, // 41603 41608
            //'IssueDate' => '28.06.2018..29.06.2018',
        ));
    
        $mrpRecords = array();
        while ($mrpRecord = $MrpResponse->readRecord()) {    
            $mrpRecords[] = $mrpRecord;
        }
        echo '<pre>' . print_r($mrpRecords, true) . '</pre>';
//        App::log('ordersImportFromMrpTest', '$mrpRecords', array(
//            'var' => $mrpRecords
//        ));
        break;
        
    case 'testFonts':
        App::loadModel('App', 'Font');
        $Font = new Font(array(
            'familySpecificity' => 'variant',
            'conditions' => array(
                'family' => array(
                    'Roboto','Open Sans','Lato','Montserrat','Shrikhand','PT Serif','Spirax'
                )
            ),
        ));
        $fonts = $Font->getList();
        $selectOptions = $Font->getSelectOptions(array(
            'fonts' => $fonts
        ));
        foreach ($fonts as $font) {
            $Font->addCssFontface($font);
        }
        App::loadLib('App', 'FormHelper');
        $Form = new FormHelper();
        Html::startCapture();
        echo $Form->select('my_font', array(
            'label' => 'Select a google font',
            'options' => $Font->getSelectOptions(),
        ));
        /*
        $Font->addCssFontface('Lobster_400');
        ?><div style="font-size:40px;font-family:<?php echo $Font->getCssFontFamily('Lobster_400') ?>;"><?php
            ?>Toto je skusobny text bez diakritiky (to combo hore je len na okrasu, s tymto textom nema nic)<br><?php
            ?>Toto je skušobný text s diakritikou ľščťžýáíéäôňú<?php
        ?></div><?php
         */
        $html = Html::endCapture();
        echo $html;
        break;

    case 'downloadRemoteFilesToLocalFiles':
        App::loadModel('App', 'Font');
        $Font = new Font();
        $Font->downloadRemoteFilesToLocalFiles();
        break;

    case 'loadGoogleFonts':
        App::loadModel('App', 'Font');
        $Font = new Font();
        $Font->updateGoogleFonts(array(
            // https://console.developers.google.com
            'googleApiKey' => ':) generate your own google API key',
        ));
        break;

    case 'testGoogleFonts':
        $url = App::getUrl(array(
            'locator' => 'https://www.googleapis.com/webfonts/v1/webfonts',
            'get' => array(
                // https://console.developers.google.com
                'key' => ':) generate your own google API key',
                'sort' => 'alpha',
            )
        ));
        $response = App::request($url);
        $responseData = json_decode($response, true);
        echo '<pre>' . print_r($responseData, true) . '</pre>';
        break;

    case 'deleteSpamMailerContacts':
        $Contact = App::loadModel('Mailer', 'MailerContact', true);
        $contacts = $Contact->find(array(
            'fields' => array('id', 'email','created'),
            'conditions' => array(
                'run_users_id' => null,
                'status' => 'enum_submitted_contact',
            ),
            'order' => 'id ASC'
        ));
        $spamContacts = array();
        
        foreach($contacts as $i => $contact) {
            $emailDotParts = explode('.', trim($contact['email']));
            $emailAtParts = explode('@', trim($contact['email']));
            $countryDomain = array_pop($emailDotParts);
            $domain = array_pop($emailAtParts);
            $name = strtolower(array_pop($emailAtParts));
            if (
                $countryDomain === 'sk' 
                || $countryDomain === 'cz'
                || strpos($name, 'kova') !== false
                || strpos($name, 'cova') !== false
                || strpos($name, 'nova') !== false
                || strpos($name, 'rova') !== false
                || strpos($name, 'ska') !== false
            ) {
                continue;
            }
            if (
                !empty($prevContact)
                && (
                    ($diffMinutes = Date::getDiff('n', $prevContact['created'], $contact['created'])) < 60
                    || strpos($name, 'x') !== false
                    || strpos($name, 'w') !== false
                    || strpos($name, 'q') !== false
                    || strpos($name, 'th') !== false
                    || strpos($name, 'tion') !== false
                    || $domain === 'mail.ru'
                    || $domain === 'yahoo.com'
                )
            ) {
                $spamContacts[$prevContact['id']] = array(
                    'email' => $prevContact['email'],
                    'created' => $prevContact['created'],
                );
                $spamContacts[$contact['id']] = array(
                    'email' => $contact['email'],
                    'created' => $contact['created'],
                );
            }
            $prevContact = $contact;
        }
        if (empty($this->args[1])) {
            echo 'spam contacts: ' . count($spamContacts) . '<br>';
            foreach ($spamContacts as $spamContact) {
                echo $spamContact['email'] . ' - ' . $spamContact['created'] . '<br>';
            }
            echo '<br><br>good contacts:<br>';
            $count = 0;
            foreach ($contacts as $contact) {
                if (empty($spamContacts[$contact['id']])) {
                    echo $contact['email'] . ' - ' . $contact['created'] . '<br>';
                    $count++;
                }
            }
            echo '<br><br>good contacts: ' . $count . '<br>';
            
        }
        else {
//            $Contact->deleteBy('id', array_keys($spamContacts));
        }
        break;
    
    case 'generateMailerContactTokens':
        App::setSqlLogging(false);
        $Contact = App::loadModel('Mailer', 'MailerContact', true);
        $contacts = $Contact->find(array(
            'fields' => array('id'),
            'conditions' => array(
                'token' => ''
            )
        ));
        foreach ($contacts as &$contact) {
            $contact['token'] = Str::getrandom(40);
        }
        unset($contact);
        $Contact->saveBatch(array(
            'update' => array(
                'Mailer.MailerContact' => &$contacts
            )
        ));
        break;
        
    case 'sendNewOrderEmail':
        if (empty($this->args[1])) {
            echo 'Zadaj id objednávky: .../_debug/sendNewOrderEmail/{id}';
            break;
        }
        App::loadModel('Eshop', 'EshopOrder');
        $Order = new EshopOrder();
        $Order->sendNewOrderEmail($this->args[1]);
        break;

//    case 'sendPaymentLink':
//        App::loadModel('Eshop', 'EshopOrder');
//        $Order = new EshopOrder();
//        $orders = $Order->find(array(
//            'joins' => array(
//                array(
//                    'module' => 'Payment',
//                    'model' => 'PaymentMethod',
//                    'type' => 'left',
//                )
//            ),
//            'conditions' => array(
//                array(
//                    'EshopOrder.number >=' => 45234,
//                    'EshopOrder.number <=' => 45282,
//                    'OR',
//                    'EshopOrder.number' => 45282,
//                ),
//                'PaymentMethod.online' => true,
//                'EshopOrder.payment_status' => 'enum_payment_none',
//            ),
//            'fields' => array(
//                'EshopOrder.id',
//                'EshopOrder.number',
//                'EshopOrder.email',
//                'EshopOrder.status',
//                'EshopOrder.payment_status',
//            )
//        ));
//        echo '<pre>' . print_r($orders, true) . '</pre>';
//        foreach ($orders as $order) {
//            $url = $Order->getPaymentUrl($order['id']);
//            $body = sprintf('Dobrý deň vážený zákazník,<br><br>k Vašej objednávke č. %s sme Vám nedopatrením poslali správu o novej objednávke bez platobného linku.<br>Preto Vám dodatočne posielame chýbajúci platobný link: <a href="%s">%s</a>.<br>Za spôsobené nedorozumenie sa ospravedlňujeme.<br><br>So želaním príjemného dňa,<br><br>Vydavatel.sk', $order['number'], $url, $url);
//            if (App::sendEmail($body, $order['email'], array(
//                'subject' => sprintf('Objednávka %s - platobný link', $order['number']),
//                'from' => '<EMAIL>'
//            ))) {
//                echo $order['email'] . ' - ok<br>';
//            }
//            else {
//                echo $order['email'] . ' - nok<br>';
//            }
//        }
//        break;
//    
    case 'sendNewOrderEmail':
        if (empty($this->args[1])) {
            echo 'Zadaj id objednávky';
        }
        App::loadModel('Eshop', 'EshopOrder');
        $Order = new EshopOrder();
        $Order->sendNewOrderEmail($this->args[1]);
        break;
    
    case 'addEshopOrderProductNumbers':
        App::loadModel('Eshop', 'EshopOrderProduct');
        $OrderProduct = new EshopOrderProduct();
        $orderProducts = $OrderProduct->findList(array(
            'key' => 'run_eshop_orders_id',
            'fields' => 'id',
            'order' => 'id ASC',
            'accumulate' => true,
        ));
        $productNumbers = array();
        foreach ($orderProducts as $productIds) {
            $number = 1;
            foreach($productIds as $productId) {
                $productNumbers[] = array(
                    'id' => $productId,
                    'number' => $number++,
                );
            }
        }
        //App::debug($productNumbers); //debug
        $OrderProduct->saveBatch(array(
            'update' => array(
                'EshopOrderProduct' => &$productNumbers
            )
        ));
        break;

    case 'convertWebContentTextsToHtmlContentBlocks':
        if (empty($this->args[1])) {
            echo '<br>';
            echo 'Zálohoval si si tabuľku run_web_contents?!? ';
            echo '<a href="' . rtrim(App::$url, '/') . '/1' . '" style="padding:5px;border:1px solid red;color:red;text-decoration:none">áno, pokračovať ďalej</a>';
            break;
        }
        App::loadModel('App', 'WebContent');
        $Model = new WebContent();
        $sliderIds = $Model->findList(array(
            'fields' => 'id',
            'conditions' => array(
                'pid' => 'sliders'
            )
        ));
        //echo '<pre>' . print_r($sliderIds, true) . '</pre><br>'; break;
        foreach ($sliderIds as &$sliderId) {
            $sliderId = '-' . $sliderId . '-';
        }
        unset($sliderId);
        //echo '<pre>' . print_r($sliderIds, true) . '</pre><br>'; break;
        $items = $Model->find(array(
            'fields' => array(
                'id', 'text'
            ),
            'conditions' => array(
                'text !=' => array(null, ''),
                'path !%~%' => $sliderIds,
            ),
            'ignoreSoftDeleted' => false,
        ));
        //echo '<pre>' . print_r($items, true) . '</pre><br>';
        //echo '<pre>' . print_r(count($items), true) . '</pre><br>'; break;
        App::loadModel('App', 'ContentBlockInstance');
        $Block = new ContentBlockInstance();
        $blocks = $Block->findList(array(
            'key' => 'owner_id',
            'fields' => 'owner_id',
            'conditions' => array(
                'owner_model' => 'App.WebContent'
            )
        ));
        //echo '<pre>' . print_r($blocks, true) . '</pre><br>';
        //echo '<pre>' . print_r(count($blocks), true) . '</pre><br>';
        App::setSqlLogging(false);
        $progress = array(
            'skipped' => 0,
            'created' => 0,
        );
        try {
            DB::startTransaction('convertWebContentTextsToHtmlContentBlocks');
            App::loadModel('ContentBlock', 'ContentBlockHtml', true);
            $HtmlBlock = new ContentBlockHtml();
            $Block->setPropertyContentBlock($HtmlBlock);
            foreach($items as $item) {
                if (!empty($blocks[$item['id']])) {
                    $progress['skipped']++;
                    continue;
                }
                $Block->save(
                    array(
                        'owner_model' => 'App.WebContent',
                        'owner_id' => $item['id'],
                        'content_block_model' => 'ContentBlock.ContentBlockHtml',
                        'html' => $item['text'],
                        'active' => true,
                    ),
                    array(
                        'processContentBlockData' => true
                    )
                );
                $item['text'] = null;
                $Model->save($item, array(
                    'normalize' => null,
                    'validate' => null,
                ));
                $progress['created']++;
            }
            DB::commitTransaction('convertWebContentTextsToHtmlContentBlocks');
        }
        catch (Throwable $e) {
            echo '<pre>' . $e->getMessage() . '</pre><br>';
            Db::rollbackTransaction('convertWebContentTextsToHtmlContentBlocks');
        }
        echo '<pre>' . print_r($progress, true) . '</pre><br>';
        break;

    case 'convertEshopProductDescriptionsToHtmlContentBlocks':
        if (empty($this->args[1])) {
            echo '<br>';
            echo 'Zálohoval si si tabuľku run_eshop_products?!? ';
            echo '<a href="' . rtrim(App::$url, '/') . '/1' . '" style="padding:5px;border:1px solid red;color:red;text-decoration:none">áno, pokračovať ďalej</a>';
            break;
        }
        App::loadModel('Eshop', 'EshopProduct');
        $Model = new EshopProduct();
        //echo '<pre>' . print_r($sliderIds, true) . '</pre><br>'; break;
        $items = $Model->find(array(
            'fields' => array(
                'id', 'description'
            ),
            'conditions' => array(
                'description !=' => array(null, ''),
            ),
            'ignoreSoftDeleted' => false,
        ));
        //echo '<pre>' . print_r($items, true) . '</pre><br>';
        //echo '<pre>' . print_r(count($items), true) . '</pre><br>'; break;
        App::loadModel('App', 'ContentBlockInstance');
        $Block = new ContentBlockInstance();
        $blocks = $Block->findList(array(
            'key' => 'owner_id',
            'fields' => 'owner_id',
            'conditions' => array(
                'owner_model' => 'App.WebContent'
            )
        ));
        //echo '<pre>' . print_r($blocks, true) . '</pre><br>';
        //echo '<pre>' . print_r(count($blocks), true) . '</pre><br>';
        App::setSqlLogging(false);
        $progress = array(
            'skipped' => 0,
            'created' => 0,
        );
        try {
            DB::startTransaction('convertWebContentTextsToHtmlContentBlocks');
            App::loadModel('ContentBlock', 'ContentBlockHtml', true);
            $HtmlBlock = new ContentBlockHtml();
            $Block->setPropertyContentBlock($HtmlBlock);
            foreach($items as $item) {
                if (!empty($blocks[$item['id']])) {
                    $progress['skipped']++;
                    continue;
                }
                $Block->save(
                    array(
                        'owner_model' => 'App.WebContent',
                        'owner_id' => $item['id'],
                        'content_block_model' => 'ContentBlock.ContentBlockHtml',
                        'html' => $item['description'],
                        'active' => true,
                    ),
                    array(
                        'processContentBlockData' => true
                    )
                );
                $item['description'] = null;
                $Model->save($item, array(
                    'normalize' => null,
                    'validate' => null,
                ));
                $progress['created']++;
            }
            DB::commitTransaction('convertWebContentTextsToHtmlContentBlocks');
        }
        catch (Throwable $e) {
            echo '<pre>' . $e->getMessage() . '</pre><br>';
            Db::rollbackTransaction('convertWebContentTextsToHtmlContentBlocks');
        }
        echo '<pre>' . print_r($progress, true) . '</pre><br>';
        break;

    case 'test':
        App::loadLib('App', 'FormHelper');
        $Form = new FormHelper();
        echo $Form->gallery('WebImage.file', array(
            'options' => array(
                'foreignKey' => 25,
                'nameField' => 'WebImage.name',
                'fields' => array(
                    'WebImage.file' => array('label' => __d(__FILE__, 'Image')),
                    'WebImage.name' => array('label' => __d(__FILE__, 'Name')),
                    array('field' => 'WebImage.alternative_file', 'label' => __d(__FILE__, 'Alternative thumb'), 'type' => 'file'),
                    'WebImage._delete_alternative_file' => array('label' => __d(__FILE__, 'Delete alternative thumb'), 'type' => 'checkbox'),
                    array('field' => 'WebImage.alternative_file', 'type' => 'image', 'style' => 'height:100px'),
                ),
                'actions' => array(
                    'load' => '/mvc/App/WebImages/admin_load',
                    'move' => '/mvc/App/WebImages/admin_move',
                    'add' => array(
                        'label' => __d(__FILE__, 'Add new image'),
                        'url' => '/mvc/App/WebImages/admin_add',
                    ),
                    'update' => array(
                        'label' => __d(__FILE__, 'Update image'),
                        'url' => '/mvc/App/WebImages/admin_update',
                    ),
                    'delete' => array(
                        'label' => __d(__FILE__, 'Delete image'),
                        'url' => '/mvc/App/WebImages/admin_delete',
                        'confirmMessage' => __d(__FILE__, 'Please confirm removal of image'),
                    ),
                )
            )
        ));
        break;

    case 'test':
        App::loadLib('App', 'FormHelper');
        $Form = new FormHelper();
        echo $Form->contentBlocks(array(
            'ownerModel' => 'App.WebContent',
            'ownerId' => 378,
        ));
        break;

    case 'test':
        $Instance = App::loadModel('App', 'ContentBlockInstance', true);
        $list = $Instance->getContentBlocksList(array(
            'ContentBlock.ContentBlockTeam',
            'ContentBlock.*',
            'ContentBlock.ContentBlock003',
//            'App.User'
        ));
        echo '<pre>' . print_r($list, true) . '</pre>';
        break;

    case 'App_messages_1':
        App::setSuccessMessage('Hurááá', array(
//            'modal' => true
        ));
        App::setErrorMessage('Ach jaj', array(
            'modal' => true
        ));
        break;

    case 'testFetchAndCache':
        App::startJsCapture();
        ?><script type="text/javascript">

    var _cacheName = 'pwa-cache-v1';

    // is image URL?
    function _isImage(url) {
        return ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp'].reduce(function(ret, ext) {return  ret || url.endsWith(ext);}, false);
    }

    // return offline asset
    function _getOfflineAsset(url) {
        if (_isImage(url)) {
          // return offline placeholder for image
          return new Response(
                '<svg role="img" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg"><title>offline</title><path d="M0 0h400v300H0z" fill="#eee" /><text x="200" y="150" text-anchor="middle" dominant-baseline="middle" font-family="sans-serif" font-size="50" fill="#ccc">offline</text></svg>',
                {
                    headers: {
                        'Content-Type': 'image/svg+xml',
                        'Cache-Control': 'no-store'
                    }
                }
          );
        }
        else {
            // return offline page
            //rblb//return caches.match(offlineURL); //@todo
            return new Response("Offline", {
                status: 404
            });
        }
    }

    // application fetch network data
    // for possible tweaks see https://developers.google.com/web/fundamentals/instant-and-offline/offline-cookbook/#putting_it_together
    function _getUrl(url) {
        console.log('fetch ' + url); //debug
        fetch(url)
        .then(function(response) {
            if (url === 'http://localhost/img/log_reg_cart.png') {
                console.log('r1:', response); //debug
            }
            if (response.ok) {
                console.log('network fetch: ' + url); //debug
                var responseClone = response.clone();
                caches.open(_cacheName)
                .then(function(cache) {
                    cache.put(url, responseClone);
                });
                return response;
            }
            return caches.open(_cacheName)
            .then(function(cache) {
                return cache.match(url)
                .then(function(response) {
                    if (response) {
                        console.log('cache fetch: ' + url); //debug
                        return response;
                    }
                    console.log('offline asset fetch: ' + url); //debug
                    return _getOfflineAsset(url);
                });
            });
        })
        .catch(function() {
            caches.open(_cacheName)
            .then(function(cache) {
                return cache.match(url)
                .then(function(response) {
                    if (response) {
                        console.log('cache fetch: ' + url); //debug
                        return response;
                    }
                    console.log('offline asset fetch: ' + url); //debug
                    return _getOfflineAsset(url);
                });
            });
        })

//        caches.open(_cacheName)
//        .then(function(cache) {
//            return cache.match(url)
//            .then(function(response) {
//                if (response) {
//                    // return cached file
//                    console.log('cache fetch: ' + url); //debug
//                    return response;
//                }
//                // make network request
//                return fetch(url)
//                    .then(function(networkResponse) {
//                        if (networkResponse.ok) {
//                            console.log('network fetch: ' + url); //debug
//                            cache.put(url, networkResponse.clone());
//                        }
//                        return networkResponse;
//                    })
//                    // app is offline
//                    .catch(function() {return _getOfflineAsset(url);});
//            });
//        })
    };
        </script><?php
        App::endJsCapture();
        break;

    case 'test':
        App::log('test', 'testujem', array(
            'maxSize' => '5.5MB',
            'email' => true,
        ));
        break;

    case 'requestAnimationFrame':
        ?><div id="SomeElementYouWantToAnimate" style="background:red;height:20px;width:20px;"></div><?php
        ?><script type="text/javascript">
            var start = null;
            var element = document.getElementById('SomeElementYouWantToAnimate');
            element.style.position = 'absolute';

            function step(timestamp) {
              if (!start) start = timestamp;
              var progress = timestamp - start;
              element.style.left = Math.min(progress / 10, 200) + 'px';
              if (progress < 2000) {
                window.requestAnimationFrame(step);
              }
            }

            window.requestAnimationFrame(step);
        </script><?php
        break;

    case 'testPayPalTls':
        // see https://github.com/paypal/TLS-update
        // !!! To make this test functional save file https://github.com/paypal/TLS-update/blob/master/php/cacert.pem
        // into TMP folder as testPayPalTlsCert.pem
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://tlstest.paypal.com/");
        curl_setopt($ch, CURLOPT_CAINFO, TMP . DS . 'testPayPalTlsCert.pem');

        // Some environments may be capable of TLS 1.2 but it is not in their list of defaults so need the SSL version option to be set.
        curl_setopt($ch, CURLOPT_SSLVERSION, 6);

        curl_exec($ch);
        echo '<br>';
        echo 'CURL VERSION:<br>';
        echo json_encode(curl_version());
        echo '<br>';

        if (($err = curl_error($ch))) {
            echo 'ERROR:<br>';
            var_dump($err);
        }
        break;

    case 'test':
        //HTTPS!
        $url = 'https://cdn.albatrosmedia.sk/Images/Product/12042180?2E48E27CBD26B5AFFECD973E10F4F3C7&size=normal';

        $file = ROOT . DS . 'tmp' . DS . uniqid('get-contents-');
        file_put_contents($file, file_get_contents($url)); // works

        $file = ROOT . DS . 'tmp' . DS . uniqid('copy-');
        copy($url, $file); //works

        File::download(array($url), 'tmp', array( //works
            'name' => uniqid('download-')
        ));
        break;

    case 'test':
        App::debug(File::getPathinfo('C:\my\path\to\file.txt')); //debug
        App::debug(File::getPathinfo(File::normalizeDS('C:\my\path\to\file.txt'))); //debug
        App::debug(File::getPathinfo('/my/path/to/file.txt')); //debug
        break;

    case 'Arr_mergeTree':
        $a = array(
            24 => array(
                37 => array('name' => 'Fruit'),
            ),
            37 => array(
                39 => array('name' => 'Apple'),
                35 => array('name' => 'Bannana'),
            ),
        );
        $b = array(
            '??' => array(
                25 => array('name' => 'Vegetable'),
            ),
            25 => array(
                68 => array('name' => 'Cauliflower'),
                72 => array('name' => 'Pumpkin'),
            ),
        );
        echo '<pre>' . print_r(Arr::mergeTree($a, $b), true) . '</pre></br>'; //debug
        echo '<pre>' . print_r(Arr::mergeTree($b, $a), true) . '</pre>'; //debug
        break;

    case 'test':
App::loadModel('App', 'User');
$User = new User();
$users = $User->find(array(
    'joins' => array(
        array(
            'type' => 'left',
            'model' => 'Group'
        ),
    ),
    'fields' => array(
        'Group.name',
        'GROUP_CONCAT(User.last_name ORDER BY User.last_name DESC SEPARATOR ", ")',
    ),
    'group' => 'Group.name',
    'literals' => array(
        'fields' => array(
            'GROUP_CONCAT(User.last_name ORDER BY User.last_name DESC SEPARATOR ", ")',
        )
    )
));

        App::debug($users); //debug
        break;

    case 'test':
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            strpos(__FILE__, '/');
        }
        Utility::getTimer(); // ~88ms

        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            strpos(__FILE__, '\\');
        }
        Utility::getTimer(); // ~88ms

        break;

    case 'highQualityPdfByMpdf':
        ?><style type="text/css">
            <?php Html::startCapture(); ?>
            .page {
                position: relative;
                height: 210mm;
                width: 210mm;
                background-image: url('/userfiles/images/pdf_test/pozadie_210x210_300dpi.jpg');
                background-size: contain;
            }
            .image {
                /*!!! in mPdf positioning works on relative to maim (body/page) element !!!*/
                /*position: absolute;*/
                /*top: 20mm;*/
                /*left: 20mm;*/
                /*max-width: 170mm;*/
                /*max-height: 170mm;*/

                padding: 20mm;
            }
            img {
                display: block;
                width: 100%;
            }
            .text {
                /*!!! in mPdf positioning works on relative to maim (body/page) element !!!*/
                /*position: absolute;*/
                /*top: 190mm;*/
                /*left: 20mm;*/
                font-size: 24pt;
                color: #777;
            }
            <?php $css = Html::endCapture(); ?>
        </style><?php
        Html::startCapture();
        ?><div class="page"><?php
            ?><div class="image"><?php
                ?><img src="/userfiles/images/pdf_test/DSC_6007.jpg" alt="" /><?php
            ?></div><?php
            ?><div class="text"><?php
                echo 'Toto je prvá strana';
            ?></div><?php
        ?></div><?php
        ?><div class="page"><?php
            ?><div class="image"><?php
                ?><img src="/userfiles/images/pdf_test/DSC_6009.jpg" alt="" /><?php
            ?></div><?php
            ?><div class="text"><?php
                echo 'Toto je druhá strana';
            ?></div><?php
        ?></div><?php
        $html = Html::endCapture();
        // transform css to inline styles
        $css = preg_replace('/\s+/', ' ', $css);
        $css = preg_replace('/\n/', '', $css);
        $css = preg_replace('/\/\*[^\*]*\*\//', '', $css);
        $css = preg_replace('/ ?([{};:]) ?/', '$1', $css);
        $cssParts = explode('}', $css);
        $styles = array();
        foreach ($cssParts as $cssPart) {
            $cssPartParts = explode('{', $cssPart);
            if (count($cssPartParts) !== 2) {
                continue;
            }
            $selector = trim($cssPartParts[0]);
            $rules = trim($cssPartParts[1]);
            if (isset($styles[$selector])) {
                $styles[$selector] .= $rules;
            }
            else {
                $styles[$selector] = $rules;
            }
        }
        foreach ($styles as $selector => $rules) {
            if (substr($selector, 0, 1) === '.') {
                $html = preg_replace('/class="' . substr($selector, 1) . '"/', 'style="' . $rules . '"', $html);
            }
            elseif (substr($selector, 0, 1) === '#') {
                $html = preg_replace('/id="' . substr($selector, 1) . '"/', 'style="' . $rules . '"', $html);
            }
            else {
                $html = preg_replace('/<' . $selector . '/', '<' . $selector . ' style="' . $rules . '"', $html);
            }
        }
        //App::setCss($css);
        echo $html;
        if (!empty($_GET['generate'])) {
            File::createPdf($html, array(
                'name' => '/userfiles/images/pdf_test/mypdf-mpdf-' . date('YmdHis') . '.pdf',
                'output' => 'file',
                'format' => array(210, 210),
                'css' => $css,
                'dpi' => 300,
                'img_dpi' => 300,
                'marginTop' => 0,
                'marginRight' => 0,
                'marginBottom' => 0,
                'marginLeft' => 0,
                'marginHeader' => 0,
                'marginHeader' => 0,
            ));
        }
        break;

    case 'highQualityPdf':
        $generateHtml = function($pages, $local = false, $compressor = false) {
            $pages = (array)$pages;
            $rootPath = '/userfiles/images/pdf_test/';
            if ($compressor) {
                $compressor = '_compressor';
            }
            else {
                $compressor = '';
            }
            if ($local) {
                $rootPath = 'file://' . File::normalizeDS(ROOT . $rootPath);
            }
            Html::startCapture();
            ?><!doctype html><?php
            ?><html><?php
                ?><head><?php
                    ?><meta charset="utf-8"><?php
                    ?><title></title><?php
                    ?><link rel="stylesheet"  type="text/css" href="/app/css/libs/basic.css" /><?php
                    ?><style type="text/css">
                        @font-face {
                            font-family: 'Clicker Script';
                            font-style: normal;
                            font-weight: 400;
                            src: url(http://fonts.gstatic.com/s/clickerscript/v4/Zupmk8XwADjufGxWB9KThBnpV0hQCek3EmWnCPrvGRM.ttf) format('truetype');
                        }
                        html, body, div, span, applet, object, iframe,h1, h2, h3, h4, h5, h6, p, blockquote, pre,a, abbr, acronym, address, big, cite, code,del, dfn, em, img, ins, kbd, q, s, samp,small, strike, strong, sub, sup, tt, var,b, u, i, center,dl, dt, dd, ol, ul, li,fieldset, form, label, legend,table, caption, tbody, tfoot, thead, tr, th, td,article, aside, canvas, details, embed,figure, figcaption, footer, header, hgroup,menu, nav, output, ruby, section, summary,time, mark, audio, video {
                            margin: 0;
                            padding: 0;
                            border: 0;
                            font-size: 100%;
                            font: inherit;
                            vertical-align: baseline;
                        }
                        article, aside, details, figcaption, figure,footer, header, hgroup, menu, nav, section {
                            display: block;
                        }
                        body {
                            line-height: 1;
                        }
                        ol, ul {
                            list-style: none;
                        }
                        blockquote, q {
                            quotes: none;
                        }
                        blockquote:before, blockquote:after,
                        q:before, q:after {
                            content: '';
                            content: none;
                        }
                        table {
                            border-collapse: collapse;
                            border-spacing: 0;
                        }
                        /* custom styles */
                        .page {
                            position: relative;
                            overflow: hidden;
                            height: 210mm;
                            max-height: 210mm;
                            height: 209.9mm;
                            width: 210mm;
                            max-width: 210mm;
                            /*width: 209.9mm;*/
                            background-image: url('<?php echo $rootPath ?>pozadie_210x210_300dpi<?php echo $compressor ?>.jpg');
                            background-size: contain;
                        }
                        .image {
                            position: absolute;
                            top: 20mm;
                            left: 20mm;
                            max-width: 170mm;
                            max-height: 170mm;
                        }
                        img {
                            display: block;
                            width: 100%;
                        }
                        .text {
                            position: absolute;
                            top: 190mm;
                            left: 20mm;
                            width: 170mm;
                            font-family: 'Clicker Script';
                            font-size: 28pt;
                        }
                    </style><?php
                ?></head><?php
                ?><body><?php
                    foreach ($pages as $page) {
                        ?><div class="page"><?php
                            ?><div class="image"><?php
                                if ($page % 2 === 0) {
                                    ?><img src="<?php echo $rootPath ?>DSC_6007_170<?php echo $compressor ?>.jpg" alt="" /><?php
                                }
                                else {
                                    ?><img src="<?php echo $rootPath ?>DSC_6009_170<?php echo $compressor ?>.jpg" alt="" /><?php
                                }
                            ?></div><?php
                            ?><div class="text"><?php
                                echo 'Toto je strana číslo ' . $page;
                            ?></div><?php
                        ?></div><?php
                    }
                ?></body><?php
            ?></html><?php
            return Html::endCapture();
        };
        // generate html
        App::setLayout(false);
        $pages = Sanitize::value($_GET['pages'], '1,2');
        $pages = explode(',', $pages);
        echo $generateHtml($pages, false, true);
        if (
            !empty($_GET['generate'])
            && (
                $_GET['generate'] === 'ByPhpWkHtmlToPdf1'
                || $_GET['generate'] === 'ByPhpWkHtmlToPdf2'
            )
        ) {
            /*
- na generovanie pdf a jpg súborov z html som použil PHP knižnicu https://github.com/mikehaertl/phpwkhtmltopdf ktorá interne používa wkhtmltopdf a wkhtmltoimage (https://wkhtmltopdf.org). Je potrebné má nainštalovanú s "patched Qt" a pokiaľ možno najnovšiu. Možnosti sú nasledovné (Linux):
    1] Inštalácia stiahnutého .deb súboru (PHP bude vidieť priamo príkaz wkhtmltopdf, nebude potrebné zadávať cestu):
        - stiahni najnovšiu verziu z https://wkhtmltopdf.org/downloads.html
        - spusti inštaláciu stiahnutého balíčka: sudo dpkg -i {downloadedPackage}.deb
        - ak ti to hodí chybu počas inštalácie (chýbajúce dependencies) tak spusti: sudo apt-get -f install

    2] Ak by predošlá inštalácia nefungovala tak skús (v PHP bude potrebné zadávať cestu):
        - download last version from https://wkhtmltopdf.org/downloads.html
        - unpack it
        - sudo mv ~/Downloads/wkhtmltox /usr/bin/wkhtmltox
        - sudo chown root:root /usr/bin/wkhtmltox
        - sudo chmod +x /usr/bin/wkhtmltox
        - export PATH=$PATH":/usr/bin/wkhtmltox/bin"
        - and it worked, if not then check https://github.com/mikehaertl/phpwkhtmltopdf or http://blog.hemantthorat.com/install-wkhtmltopdf-on-ubuntu-12-04/

Inštalácia je možná aj cez sudo apt-get install wkhtmltopdf ale verzia tam je staršia (viď apt-cache policy wkhtmltopdf) a nainštaluje sa vo verzii "unpatched Qt" ktorá nepozná options --disable-smart-shrinking.

Samotnú PHP knižnicu https://github.com/mikehaertl/phpwkhtmltopdf som nainštaloval ako vendora cez composer (nie je zahrnutý v repozitári) a po naklonovaníé projektu už stačí len spustiť (súc v projektovom priečinku):
    - composer install

POZOR: wkhtmltopdf ma bug pri pouziti s viacerimi fontami. Viac viď phpDoc k FontVariant::$schema
            */
            $pdf = new mikehaertl\wkhtmlto\Pdf(array(
                //'binary' => '/usr/bin/wkhtmltox/bin/wkhtmltopdf',
                'no-outline',         // Make Chrome not complain
                'margin-top'    => 0,
                'margin-right'  => 0,
                'margin-bottom' => 0,
                'margin-left'   => 0,
                'page-width'   => 210,
                'page-height'   => 210,
                'dpi' => 300,

                // Default page options
                'disable-smart-shrinking',
                // allow access to local files as by default, newer versions
                // of wkhtmltopdf block access to local files for security reasons
                'enable-local-file-access',
                //'user-style-sheet' => '/path/to/pdf.css',
            ));
            for ($i = 1; $i <= 30; $i++) {
                if ($_GET['generate'] === 'ByPhpWkHtmlToPdf1') {
                    $pdf->addPage('http://core_v1/_debug/highQualityPdf?pages=' . $i);
                }
                else {
                    $pdf->addPage($generateHtml($i, true, true));
                }
            }
            if (!$pdf->saveAs(ROOT . '/userfiles/images/pdf_test/mypdf-pwkhtp-' . date('YmdHis') . '.pdf')) {
                echo $pdf->getError();
            }
        }
        elseif (
            !empty($_GET['generate'])
            && $_GET['generate'] === 'ByHtml2PdfRocket'
        ) {
            $apikey = 'b9a4b46e-0314-4721-973b-79a76fc97888';
            $value = 'http://vydavatel.sk/_debug/highQualityPdf';
            $url = 'http://api.html2pdfrocket.com/pdf?';
            $url .= 'apikey=' . urlencode($apikey);
            $url .= '&value=' . urlencode($value);
            $url .= '&PageWidth=210';
            $url .= '&PageHeight=210';
            $url .= '&Dpi=100';
            $url .= '&DisableShrinking=true';
            $result = file_get_contents($url);
            file_put_contents(ROOT . '/userfiles/images/pdf_test/mypdf-h2pr-' . date('YmdHis') . '.pdf', $result);
        }
        break;

    case 'TestPrivateClassProperties':
    // !!! NEFUNGUJE TO - premenná age je zdialaňá medzi inštanciami - je to skôr statická súkromná premenná
?><script type="text/javascript">
(function() {

var Animal = (function () {
    var age;
    function Animal(theName, theAge) {
        this.numberOfLegs = 4;
        this.name = theName;
        age = theAge;
        if (this.constructor === Animal) {
            console.log("In Animal is ");
            this.init();
        }
        else {
            console.log("Skipping Animal init() because inherited");
        }
    }
    Animal.prototype.init = function () {
        console.log("the Animal init() called");
        this.aboutMe = "I'm " + this.name + " with " + this.numberOfLegs + " legs and I'm " + age + " years old";
    };
    Animal.prototype.tellAboutMe = function() {
        console.log("I'm " + this.name + " with " + this.numberOfLegs + " legs and I'm " + age + " years old");
    };

    return Animal;
}());

var animal1 = new Animal('Bimbo', 2);
var animal2 = new Animal('Tutu', 3);
//console.log(animal1.aboutMe);
//console.log(animal2.aboutMe);
animal1.tellAboutMe();
animal2.tellAboutMe();
animal1.name = 'Bubo';
animal1.age = 5;
//console.log(animal1.aboutMe);
animal1.tellAboutMe();

})();
</script><?php

        break;

    case 'TestTypeScriptInheritanceResolvedBetter':

        /**
         * TYPESCRIPT VERSION
         */
/*
class Animal {
    public name: string;
    public numberOfLegs: number = 4;
    public aboutMe: string;
    constructor(theName: string) {
        this.name = theName;
        if (this.constructor === Animal) {
            console.log("In Animal is ");
            this.init();
        } else {
            console.log("Skipping Animal init() because inherited");
        }
    }
    init() {
        console.log("the Animal init() called");
        this.aboutMe = `I'm ${this.name} with ${this.numberOfLegs} legs`;
    }
}

class Bird extends Animal {
    public numberOfLegs: number = 2;
    constructor(theName: string) {
        super(theName);
        if (this.constructor === Bird) {
            console.log("In Bird is ");
            this.init();
        } else {
            console.log("Skipping Bird init() because inherited");
        }
    }
    init() {
        super.init();
        console.log("and also some additionals in the Bird init() called");
    }
}

class CityBird extends Bird {
    public numberOfLegs: number = 1;
    constructor(theName: string) {
        super(theName);
        if (this.constructor === CityBird) {
            console.log("In CityBird is ");
            this.init();
        } else {
            console.log("Skipping CityBird init() because inherited");
        }
    }
    init() {
        super.init();
        console.log("and also some additionals in the CityBird init() called");
    }
}

var bird = new CityBird('Bimbo');
console.log(bird.aboutMe);
*/

?><script type="text/javascript">
(function() {

var __extends = (this && this.__extends) || function (d, b) {
    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
};
var Animal = (function () {
    function Animal(theName) {
        this.numberOfLegs = 4;
        this.name = theName;
        if (this.constructor === Animal) {
            console.log("In Animal is ");
            this.init();
        }
        else {
            console.log("Skipping Animal init() because inherited");
        }
    }
    Animal.prototype.init = function () {
        console.log("the Animal init() called");
        this.aboutMe = "I'm " + this.name + " with " + this.numberOfLegs + " legs";
    };
    return Animal;
}());
var Bird = (function (_super) {
    __extends(Bird, _super);
    function Bird(theName) {
        var _this = _super.call(this, theName) || this;
        _this.numberOfLegs = 2;
        if (_this.constructor === Bird) {
            console.log("In Bird is ");
            _this.init();
        }
        else {
            console.log("Skipping Bird init() because inherited");
        }
        return _this;
    }
    Bird.prototype.init = function () {
        _super.prototype.init.call(this);
        console.log("and also some additionals in the Bird init() called");
    };
    return Bird;
}(Animal));
var CityBird = (function (_super) {
    __extends(CityBird, _super);
    function CityBird(theName) {
        var _this = _super.call(this, theName) || this;
        _this.numberOfLegs = 1;
        if (_this.constructor === CityBird) {
            console.log("In CityBird is ");
            _this.init();
        }
        else {
            console.log("Skipping CityBird init() because inherited");
        }
        return _this;
    }
    CityBird.prototype.init = function () {
        _super.prototype.init.call(this);
        console.log("and also some additionals in the CityBird init() called");
    };
    return CityBird;
}(Bird));
var bird = new CityBird('Bimbo');
console.log(bird.aboutMe);

})();
</script><?php

?><script type="text/javascript">
(function() {

class Animal {

    constructor (theName) {
        this.name = theName;
        this.numberOfLegs = 4;
        if (this.constructor === Animal) {
            console.log("In Animal is ");
            this.init();
        } else {
            console.log("Skipping Animal init() because inherited");
        }
    }
    init() {
        console.log("the Animal init() called");
        this.aboutMe = "I'm " + this.name + " with " + this.numberOfLegs + " legs";
    }
}

class Bird extends Animal {
    constructor (theName) {
        super(theName);
        this.numberOfLegs = 2;
        if (this.constructor === Bird) {
            console.log("In Bird is ");
            this.init();
        } else {
            console.log("Skipping Bird init() because inherited");
        }
    }
    init() {
        super.init();
        console.log("and also some additionals in the Bird init() called");
    }
}

class CityBird extends Bird {
    constructor (theName) {
        super(theName);
        this.numberOfLegs = 1;
        if (this.constructor === CityBird) {
            console.log("In CityBird is ");
            this.init();
        } else {
            console.log("Skipping CityBird init() because inherited");
        }
    }
    init() {
        super.init();
        console.log("and also some additionals in the CityBird init() called");
    }
}

var bird = new CityBird('Bimbo');
console.log(bird.aboutMe);

})();
</script><?php

    break;

    case 'TestTypeScriptInheritanceResolved':

        /**
         * TYPE SCRIPT
         */

/*
class Animal {
	static _isInheritable = true;
	public name: string;
	public numberOfLegs: number = 4;
	public aboutMe: string;
	constructor(theName: string) {
		this.name = theName;

		var isInheirited = (arguments.callee.caller !== null ? arguments.callee.caller._isInheritable !== void 0 : false);
        if (!isInheirited) {
            console.log("In Animal is ");
			this.init();
		} else {
			console.log("Skipping Animal init() because inherited");
		}
	}
	init() {
		console.log("the Animal init() called");
		this.aboutMe = `I'm ${this.name} with ${this.numberOfLegs} legs`;
	}
}

class Bird extends Animal {
	public numberOfLegs: number = 2;
	constructor(theName: string) {
		super(theName);

		var isInheirited = (arguments.callee.caller !== null ? arguments.callee.caller._isInheritable !== void 0 : false);
        if (!isInheirited) {
            console.log("In Bird is ");
			this.init();
		} else {
			console.log("Skipping Bird init() because inherited");
		}
    }
    init() {
        super.init();
        console.log("and also some additionals in the Bird init() called");
    }
}

class CityBird extends Bird {
    public numberOfLegs: number = 1;
    constructor(theName: string) {
		super(theName);

		var isInheirited = (arguments.callee.caller !== null ? arguments.callee.caller._isInheritable !== void 0 : false);
        if (!isInheirited) {
            console.log("In CityBird is ");
			this.init();
		} else {
			console.log("Skipping CityBird init() because inherited");
		}
    }
    init() {
        super.init();
        console.log("and also some additionals in the CityBird init() called");
    }
}

var bird = new CityBird('Bimbo');
console.log(bird.aboutMe);
*/

?><script type="text/javascript">
(function() {

var __extends = (this && this.__extends) || function (d, b) {
    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
};
var Animal = (function () {
    function Animal(theName) {
        this.numberOfLegs = 4;
        this.name = theName;
        var isInheirited = (arguments.callee.caller !== null ? arguments.callee.caller._isInheritable !== void 0 : false);
        if (!isInheirited) {
            console.log("In Animal is ");
            this.init();
        }
        else {
            console.log("Skipping Animal init() because inherited");
        }
    }
    Animal.prototype.init = function () {
        console.log("the Animal init() called");
        this.aboutMe = "I'm " + this.name + " with " + this.numberOfLegs + " legs";
    };
    return Animal;
}());
Animal._isInheritable = true;
var Bird = (function (_super) {
    __extends(Bird, _super);
    function Bird(theName) {
        var _this = _super.call(this, theName) || this;
        _this.numberOfLegs = 2;
        var isInheirited = (arguments.callee.caller !== null ? arguments.callee.caller._isInheritable !== void 0 : false);
        if (!isInheirited) {
            console.log("In Bird is ");
            _this.init();
        }
        else {
            console.log("Skipping Bird init() because inherited");
        }
        return _this;
    }
    Bird.prototype.init = function () {
        _super.prototype.init.call(this);
        console.log("and also some additionals in the Bird init() called");
    };
    return Bird;
}(Animal));
var CityBird = (function (_super) {
    __extends(CityBird, _super);
    function CityBird(theName) {
        var _this = _super.call(this, theName) || this;
        _this.numberOfLegs = 1;
        var isInheirited = (arguments.callee.caller !== null ? arguments.callee.caller._isInheritable !== void 0 : false);
        if (!isInheirited) {
            console.log("In CityBird is ");
            _this.init();
        }
        else {
            console.log("Skipping CityBird init() because inherited");
        }
        return _this;
    }
    CityBird.prototype.init = function () {
        _super.prototype.init.call(this);
        console.log("and also some additionals in the CityBird init() called");
    };
    return CityBird;
}(Bird));
var bird = new CityBird('Bimbo');
console.log(bird.aboutMe);

})();
</script><?php
        break;

    case 'TestTypeScriptInheritance':

        /**
         * TYPESCRIPT VERSION
         */
/*
class Animal {
    name: string;
    numberOfLegs: number = 4;
    aboutMe: string;
    constructor (theName: string) {
        this.name = theName;
        this.init();
    }
    init() {
        this.aboutMe = `I'm ${this.name} with ${this.numberOfLegs} legs`;
    }
}

class Bird extends Animal {
    name: string;
    numberOfLegs: number = 2;
    constructor (theName: string) {
        super(theName);
    }
}

var bird = new Bird('Bimbo');
console.log(bird.aboutMe);
*/

?><script type="text/javascript">
(function() {

var __extends = (this && this.__extends) || function (d, b) {
    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
};
var Animal = (function () {
    function Animal(theName) {
        this.numberOfLegs = 4;
        this.name = theName;
        this.init();
    }
    Animal.prototype.init = function () {
        this.aboutMe = "I'm " + this.name + " with " + this.numberOfLegs + " legs";
    };
    return Animal;
}());
var Bird = (function (_super) {
    __extends(Bird, _super);
    function Bird(theName) {
        var _this = _super.call(this, theName) || this;
        _this.numberOfLegs = 2;
        return _this;
    }
    return Bird;
}(Animal));
var bird = new Bird('Bimbo');
console.log(bird.aboutMe);

})();
</script><?php

?><script type="text/javascript">
(function() {

class Animal {

    constructor (theName) {
        this.name = theName;
        this.numberOfLegs = 4;
        this.init();
    }
    init() {
        this.aboutMe = "I'm " + this.name + " with " + this.numberOfLegs + " legs";
    }
}

class Bird extends Animal {
    constructor (theName) {
        super(theName);
        this.numberOfLegs = 2;
    }
}

var bird = new Bird('Bimbo');
console.log(bird.aboutMe);

})();
</script><?php

        /**
         * PHP VERSION
         */
        class Animal {
            protected $name;
            protected $numberOfLegs = 4;
            public $aboutMe;
            public function __construct ($theName) {
                $this->name = $theName;
                $this->init();
            }
            protected function init() {
                $this->aboutMe = "I'm {$this->name} with {$this->numberOfLegs} legs";
            }
        }

        class Bird extends Animal {
            protected $numberOfLegs = 2;
            public function __construct ($theName) {
                parent::__construct($theName);
            }
        }

        $bird = new Bird('Bimbo');
        echo $bird->aboutMe;
        break;

    case 'TestJsInheritance':
        // !!! ATTENTION: For testing comment the contents of /misc/.htaccess
        App::setJsFiles(array(
            '/app/js/vendors/jquery.min.js',
            '/app/js/libs/Validate.js',
            '/misc/templates/NewClass.js',
            '/misc/templates/NewChildClass.js',
        ));
        App::startJsCapture();
?><script type="text/javascript">
jQuery(function() {
    if (typeof Run.SomeModule.NewClass === 'undefined') {
        console.log('!!!ATTENTION: To use this debug case comment the contents of /misc/.htaccess !!!'); //debug
    }
//    __a0 = Run.SomeModule.NewClass.getInstance({selector: 'body'});
    __a1 = new Run.SomeModule.NewClass({a: 1, selector: 'body', testCase: 'new Run.SomeModule.NewClass', onSomeEvent: 'do nothing :)'});
    console.log('new Run.SomeModule.NewClass', __a1); //debug
    __b1 = new Run.SomeModule.NewChildClass({b: 2, selector: 'body', testCase: 'new Run.SomeModule.NewChildClass'});
    console.log('new Run.SomeModule.NewChildClass', __b1); //debug
    __a2 = new Run.SomeModule.NewClass({c: 3, selector: 'body', testCase: 'new Run.SomeModule.NewClass'});
    console.log('new Run.SomeModule.NewClass', __a2); //debug
    __a3 = Run.SomeModule.NewClass.getInstance({d: 4, selector: 'body', testCase: 'Run.SomeModule.NewClass.getInstance'});
    console.log('Run.SomeModule.NewClass.getInstance', __a3); //debug
    __b2 = Run.SomeModule.NewChildClass.getInstance({e: 5, selector: 'body', testCase: 'Run.SomeModule.NewChildClass.getInstance'});
    console.log('Run.SomeModule.NewChildClass.getInstance', __b2); //debug
    __a4 = Run.SomeModule.NewClass.getInstance({f: 6, selector: 'head', testCase: 'Run.SomeModule.NewClass.getInstance'});
    console.log('Run.SomeModule.NewClass.getInstance', __a4); //debug
});
</script><?php
        App::endJsCapture();
        break;


    case 'MrpResponse':
        App::setLayout(false);
        App::loadLib('Eshop', 'MrpResponse');
//        $Response = new MrpResponse('tmp/mrp_request_response_tmp_58d8da45a765f', array(
//        $Response = new MrpResponse('tmp/mrp_request_response_tmp_58d8fa33e2154', array(
        $Response = new MrpResponse('tmp/mrp_request_tmp', array(
            //key from http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#PRIKLAD_VYPOCTU_SIFROVANI_A_AUTENTIZACE
//            'privateKey' => 'bRtFEufmEgrJyhai6ltDSV9svtpN3Jb/5oWBBYhDJ30=', 
            'privateKey' => 'toYmORZPGxbHfc0Z17FdQzJWXsPFH8viFwNMvo8x61M=',
        ));
        break;
    
    case 'File_searchString2':
        $fileContent =
<<<EOS
<mrpEnvelope><encodedBody authentication="hmac_sha256"><encodingParams><![CDATA[PG1ycEVuY29kaW5nUGFyYW1zIGVuY3J5cHRpb249ImFlcyI+PHZhcktleT5CNHhnMFlFUC9XQWZh
amZqZHRxSFV4WFgza2NwVHZIcVJTNjA2NG1qbURJPTwvdmFyS2V5PjwvbXJwRW5jb2RpbmdQYXJh
bXM+DQo=]]></encodingParams><encodedData><![CDATA[t5Grtok9DJQCVIyuzXhPHfLDzKvN23jj7oIe3BnhFyXJnoFJkOUwUPIItbcGeoW+uFuYoZkuNLfR
BFvLdp5uQarty+O/nIzGUXQp7jKja9EzpSccRzsdhY679wEWanlIt6qB3dkmclyxxj+vI0lvch1W
BiQhZFctPbUWTYKtyhUJ+A5ExY7GucNLEAmtpC+4fYj8vP/XTEu3RyMyq/KHz9LO24kZuBoaajfp
ogfAP0rHek3aGWpLc2ZnHmQ3uk3vHYa3VQt3JpMouS1RXi/1pQ==]]></encodedData><authCode>1D7PmBVEnfz2wXf80GiNQmi0nnQcQKYkoddMPYdsSu4=</authCode></encodedBody></mrpEnvelope>
EOS;
        $file = TMP . DS . uniqid('File_searchString_'). '.txt';
        file_put_contents($file, $fileContent);

        $pos = File::searchString(File::getRelativePath($file), '<encodedBody ');
        App::debug($pos, $pos . ' - <encodedBody'); //debug

        $pos = File::searchString(File::getRelativePath($file), '/<encodedBody([^>]*)>/i', array(
            'match' => &$match,
        ));
        App::debug($pos, $pos . ' - /<encodedBody([^>]*)>/i'); //debug
        App::debug($match, '$match'); //debug

        $pos = File::searchString(File::getRelativePath($file), '/<xxxencodedBody([^>]*)>/i', array(
            'match' => &$match,
        ));
        App::debug($pos, $pos . ' - /<xxxencodedBody([^>]*)>/i'); //debug

        $pos = File::searchString(File::getRelativePath($file), '<encodingParams>');
        App::debug($pos, $pos . ' - '); //debug

        $pos1 = File::searchString(File::getRelativePath($file), '<encodingParams>', array(
            'return' => 'after',
        ));
        App::debug($pos, $pos . ' - '); //debug

        $pos2 = File::searchString(File::getRelativePath($file), '</encodingParams>');
        App::debug($pos, $pos . ' - '); //debug
        App::debug(substr($fileContent, $pos1, $pos2 - $pos1));

        // get times
        $relativePath = File::getRelativePath($file);
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            // in old version the slice size wasfixed to 1
//            File::searchStringOld($relativePath, '</encodedBody>');
//        }
//        Utility::getTimer(); // ~2500ms
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            File::searchString($relativePath, '</encodedBody>');
        }
        Utility::getTimer(); // ~88ms
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            File::searchString($relativePath, '</encodedBody>', array(
                'sliceSize' => 1
            ));
        }
        Utility::getTimer(); // ~320ms
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            File::searchString($relativePath, '/<\/encodedBody>/i');
        }
        Utility::getTimer(); // ~100ms

        $fileHandle = fopen($file, 'rb');
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            File::searchString($fileHandle, '</encodedBody>');
        }
        Utility::getTimer(); // ~59ms
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            File::searchString($fileHandle, '</encodedBody>', array(
                'sliceSize' => 1
            ));
        }
        Utility::getTimer(); // ~300ms
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            File::searchString($fileHandle, '/<\/encodedBody>/i');
        }
        Utility::getTimer(); // ~70ms
        fclose($fileHandle);

        unlink($file);
        break;

    case 'File_searchString1':
        $file = TMP . DS . uniqid('File_searchString_'). '.txt';
        file_put_contents($file,
<<<EOS
abcdefghijklmn
01234567890123
EOS
        );
        $pos = File::searchString(File::getRelativePath($file), 'ab');
        App::debug($pos, $pos); //debug
        $pos = File::searchString(File::getRelativePath($file), 'ab', array(
            'return' => 'after',
        ));
        App::debug($pos, $pos); //debug
        $pos = File::searchString(File::getRelativePath($file), 'de');
        App::debug($pos, $pos); //debug
        $pos = File::searchString(File::getRelativePath($file), 'de', array(
            'return' => 'after',
        ));
        App::debug($pos, $pos); //debug
        $pos = File::searchString(File::getRelativePath($file), 'mn');
        App::debug($pos, $pos); //debug
        $pos = File::searchString(File::getRelativePath($file), 'lm', array(
            'return' => 'after',
            'sliceSize' => 3,
        ));
        App::debug($pos, $pos . ' - lm o0 sz 3'); //debug
        $pos = File::searchString(File::getRelativePath($file), 'mn', array(
            'return' => 'after',
        ));
        App::debug($pos, $pos . ' - o0'); //debug
        $pos = File::searchString(File::getRelativePath($file), 'mn', array(
            'offset' => 11,
            'return' => 'after',
        ));
        App::debug($pos, $pos . ' - o11'); //debug
        $pos = File::searchString(File::getRelativePath($file), 'mn', array(
            'offset' => 12,
            'return' => 'after',
        ));
        App::debug($pos, $pos . ' - o12'); //debug
        $pos = File::searchString(File::getRelativePath($file), 'mn', array(
            'offset' => 13,
            'return' => 'after',
        ));
        App::debug($pos, $pos); //debug
        $pos = File::searchString(File::getRelativePath($file), 'mn', array(
            'offset' => 14,
            'return' => 'after',
        ));
        $pos = File::searchString(File::getRelativePath($file), 'mn', array(
            'offset' => 114,
            'return' => 'after',
        ));
        App::debug($pos, $pos); //debug
        $pos = File::searchString(File::getRelativePath($file), 'ax');
        App::debug($pos, $pos); //debug
        $pos = File::searchString(File::getRelativePath($file), 'ax', array(
            'return' => 'after',
        ));
        App::debug($pos, $pos); //debug
        unlink($file);
        break;

    case 'MrpCryptExample':
        // http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#PRIKLAD_VYPOCTU_SIFROVANI_A_AUTENTIZACE
        $secretKeyBase64 = 'bRtFEufmEgrJyhai6ltDSV9svtpN3Jb/5oWBBYhDJ30=';
        $secretKey = base64_decode($secretKeyBase64);
        $variantKey = pack('H*', '1F5AC77ED30CC0A5F75BB035FF0566A50DB2127AAB32D8624E0DA4D4186E7F2F');
        $auxKey = hash_hmac("sha256", pack('H*', '01'), $secretKey, true);
        $authenticationKey = hash_hmac("sha256", $auxKey . pack('H*', '02'), $secretKey, true);
        $encryptionKey = hash_hmac("sha256", $variantKey, $auxKey, true);
        $encryptionIv = pack('H*', substr(hash("sha256", $variantKey), 0, 32));
        $message = pack('H*', '0102030405060708090A0B0C0D0E0F1011121314');
        $encryptedMessage = openssl_encrypt(
            $message,
            'aes-256-ctr',
            $encryptionKey,
            OPENSSL_RAW_DATA,
            $encryptionIv
        );
        $decryptedMessage = openssl_decrypt(
            $encryptedMessage,
            'aes-256-ctr',
            $encryptionKey,
            OPENSSL_RAW_DATA,
            $encryptionIv
        );
        $signature = hash_hmac("sha256", $message, $authenticationKey, true);
        
        echo '$secretKeyBase64: ' . $secretKeyBase64 . '<br>';
        echo '$secretKey: ' . strtoupper(bin2hex($secretKey)) . '<br>';
        echo '$variantKey: ' . strtoupper(bin2hex($variantKey)) . '<br>';
        echo '$auxKey: ' . strtoupper(bin2hex($auxKey)) . '<br>';
        echo '$authenticationKey: ' . strtoupper(bin2hex($authenticationKey)) . '<br>';
        echo '$encryptionKey: ' . strtoupper(bin2hex($encryptionKey)) . '<br>';
        echo '$encryptionIv: ' . strtoupper(bin2hex($encryptionIv)) . '<br>';
        echo '$message: ' . strtoupper(bin2hex($message)) . '<br>';
        echo '$encryptedMessage: ' . strtoupper(bin2hex($encryptedMessage)) . '<br>';
        echo '$decryptedMessage: ' . strtoupper(bin2hex($decryptedMessage)) . '<br>';
        echo '$signature: ' . strtoupper(bin2hex($signature)) . '<br>';
        break;
    
    case 'test':
        $key = base64_decode('bRtFEufmEgrJyhai6ltDSV9svtpN3Jb/5oWBBYhDJ30=');
        $hexKey = strtoupper(bin2hex($key));
        echo $hexKey;
        echo '<br/><br/>';

//        // all these 3 below returns the same value, means $key === pack("H*" , $hexKey) === hex2bin($hexKey)
//        echo $key;
//        echo '<br/>';
//        echo pack("H*" , $hexKey);
//        echo '<br/>';
//        echo hex2bin($hexKey);
//        echo '<br/><br/>';

        $hexVariantKey = '1F5AC77ED30CC0A5F75BB035FF0566A50DB2127AAB32D8624E0DA4D4186E7F2F';
        $variantKey = pack('H*', $hexVariantKey);
        $hexHashKey = strtoupper(hash_hmac("sha256", pack('H*', '01'), $key));
        echo $hexHashKey;
        echo '<br/><br/>';
        $hexAuthKey = strtoupper(hash_hmac("sha256", pack('H*', $hexHashKey) . pack('H*', '02'), $key));
        echo $hexAuthKey;
        echo '<br/><br/>';
        $hexFinalHashKey = strtoupper(hash_hmac("sha256", $variantKey, pack('H*', $hexHashKey)));
        echo $hexFinalHashKey;
        echo '<br/><br/>';
        $hexIv = substr(strtoupper(hash("sha256", $variantKey)), 0, 32);
        echo $hexIv;
        echo '<br/><br/>';
        $message = pack('H*', '0102030405060708090A0B0C0D0E0F1011121314');
        $encryptedMessage = openssl_encrypt(
            $message,
            'aes-256-ctr',
            pack('H*', $hexFinalHashKey),
            OPENSSL_RAW_DATA,
            pack('H*', $hexIv)
        );
        echo '$encryptedMessage: '  . bin2hex($encryptedMessage);
        echo '<br/><br/>';
        $messageAuth = strtoupper(hash_hmac("sha256", $message, pack('H*', $hexAuthKey)));
        echo $messageAuth;
        echo '<br/><br/>';
        $decodedMessage = openssl_decrypt(
            $encryptedMessage,
            'aes-256-ctr',
            pack('H*', $hexFinalHashKey),
            OPENSSL_RAW_DATA,
            pack('H*', $hexIv)
        );
        echo bin2hex($decodedMessage);
        echo '<br/><br/>';
        echo base64_encode($decodedMessage);
        echo '<br/><br/>';

        break;

    case 'File_download':
        //File::download(array('http://b2b.inform-za.sk/obalky/obalky_170/9788080631345.jpg'), 'tmp');
        //File::download(array('http://b2b.inform-za.sk/obalky/obalky_170/9788080631642.jpg'), 'tmp');
        File::download(array('http://b2b.inform-za.sk/obalky/obalky_170/9788080631000.jpg'), 'tmp');
        break;

    case 'App_request':
        $response = App::request('http://www.pemic-books.cz/PemicCatalog/Default.aspx?guid=524B85B3-33D3-48FE-AF7C-49DAD52B3DF1&file=catalogue_full', array(
            'outputFile' => 'tmp/app_request_output_file.zip'
        ));
        echo json_encode($response);
//        // generate token here https://vo.kniznyweb.sk/b2bGate?login=434888&password=Zjavkova&synctype=T
//        // and update it in URL below:
//        App::request('https://vo.kniznyweb.sk/b2bGate?login=434888&password=Zjavkova&token=mVFNU9Eq', array(
//            'outputFile' => 'tmp/app_request_output_file2.zip'
//        ));
        break;

    // .../_debug/testMrpStockReader
    case 'testMrpStockReader':
        App::loadLib('Eshop', 'MrpStockReader');
        $serverIpAddress = App::getSetting('Eshop', 'mrp.serverIpAddress');
        $serverPort = App::getSetting('Eshop', 'mrp.serverPort');
        $Reader = new MrpStockReader($serverIpAddress, $serverPort, array(
            'privateKey' => App::getSetting('Eshop', 'mrp.privateKey'),
            'extendedData' => true,
        ));
        $records = array();
        while($record = $Reader->readRecord()) {
            $records[] = $record;
        }
        echoReadable($records);
        break;
    
    case 'MrpRequest_getStock2':
        App::setLayout(false);
        App::loadLib('Eshop', 'MrpRequest');
//        $Mrp = new MrpRequest('************', '120', array(
        $Mrp = new MrpRequest('************', '120', array(
            //key from http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#PRIKLAD_VYPOCTU_SIFROVANI_A_AUTENTIZACE
//            'privateKey' => 'bRtFEufmEgrJyhai6ltDSV9svtpN3Jb/5oWBBYhDJ30=', 
            'privateKey' => 'toYmORZPGxbHfc0Z17FdQzJWXsPFH8viFwNMvo8x61M=',
        ));
        $step = 10000;
        $from = 1;
        $to = $step;
        while (true) {
            $Response = $Mrp->getStock(array(
                'SKKAR.CISLO' => $from . '..' . $to,
            ));
            if (!($record = $Response->readRecord())) {
                break;
            }
            echo '<pre>' . print_r($record, true) . '</pre><br/>';
            $from = $to + 1;
            $to += $step;
        }
//        while ($record = $Response->readRecord()) {
//            echo '<pre>' . print_r($record, true) . '</pre><br/>';
//        }
        break;
        
    case 'MrpRequest_getStock1':
        App::setLayout(false);
        App::loadLib('Eshop', 'MrpRequest');
        $Mrp = new MrpRequest('************', '120', array(
//        $Mrp = new MrpRequest('************', '120', array(
            //key from http://www.mrp.cz/software/ucetnictvi/ks/autonomni-rezim.asp#PRIKLAD_VYPOCTU_SIFROVANI_A_AUTENTIZACE
//            'privateKey' => 'bRtFEufmEgrJyhai6ltDSV9svtpN3Jb/5oWBBYhDJ30=', 
            'privateKey' => 'toYmORZPGxbHfc0Z17FdQzJWXsPFH8viFwNMvo8x61M=',
        ));
        $Response = $Mrp->getStock(array(
            'SKKAR.CISLO' => '312378..312385',
//            'SKKAR.CISLO' => '312326..312326',
        ));
        while ($record = $Response->readRecord()) {
            echo '<pre>' . print_r($record, true) . '</pre><br/>';
        }
        break;
        
    case 'EshopOrder_getInserts':
        $Order = App::loadModel('Eshop', 'EshopOrder', true);
        $ids = $Order->findList(array(
            'fields' => 'id',
            'limit' => 5,
            'order' => 'id DESC',
        ));
        foreach ($ids as $id) {
            $inserts = $Order->getInserts($id);
            echo '<pre>' . print_r($inserts, true) . '</pre><br><br>';
        }
        break;
    
    case 'CardpayTestRequest':
        App::loadLib('Payment', 'Cardpay');
        $Cardpay = new Cardpay(
            '9999',
            '31323334353637383930313233343536373839303132333435363738393031323132333435363738393031323334353637383930313233343536373839303132',
            true
        );
        $Cardpay->makeRequest(array(
            'amount' => '1234.50',
            'variableSymbol' => '1111',
            'name' => 'Ján Pokusný',
            'returnUrl' => 'http://drinkcentrum/_debug/CardpayTestResponse',
        ));
        break;

    case 'CardpayTestResponse':
        App::loadLib('Payment', 'Cardpay');
        $Cardpay = new Cardpay(
            '9999',
            '31323334353637383930313233343536373839303132333435363738393031323132333435363738393031323334353637383930313233343536373839303132',
            true
        );
        $result = $Cardpay->processResponse($_REQUEST);
        App::debug($result); //debug
        break;

    case 'File_getMimeType':
        $r = File::getMimeType('/userfiles/phpinfo.jpg', array(), $byExtension);
        App::debug($r); //debug
        App::debug($byExtension); //debug
        $r = File::getMimeType('/userfiles/phpinfo.jpg', array('byExtension' => true), $byExtension);
        App::debug($r); //debug
        App::debug($byExtension); //debug
        $r = File::getMimeType('/userfiles/phpinfo.png', array(), $byExtension);
        App::debug($r); //debug
        App::debug($byExtension); //debug
        break;

    case 'fullPageIframe':
        //App::setCss('body, html {margin: 0; padding: 0; height: 100%; overflow: hidden;}');
        $a = Sanitize::value($this->args[1]);
        if ($a == 1) {
            ?>
            <style type="text/css">
                body, html {margin: 0; padding: 0; height: 100%; overflow: hidden;}
            </style>
            <?php
            ?><iframe src="http://biznismedia.run.sk/kalkulacka-3d-napisov" frameborder="0" style="overflow:hidden;height:100%;width:100%" height="100%" width="100%"></iframe><?php
        }
        elseif ($a == 2) {
            ?>
            <style type="text/css">
                body, html {margin: 0; padding: 0; height: 100%; overflow: hidden;}
            </style>
            <?php
            ?><iframe src="http://biznismedia.run.sk/kalkulacka-3d-napisov" frameborder="0" style="overflow:hidden;overflow-x:hidden;overflow-y:hidden;height:100%;width:100%;position:absolute;top:0px;left:0px;right:0px;bottom:0px" height="100%" width="100%"></iframe><?php
        }
        elseif ($a == 3) {
            ?>
            <style type="text/css">
                body, html {margin: 0; padding: 0; height: 100%; overflow: hidden;}
            </style>
            <?php
            ?>
            <div style="overflow:hidden;overflow-x:hidden;overflow-y:hidden;position:absolute;top:0px;left:0px;right:0px;bottom:0px;z-index:9999;">
                <iframe src="http://biznismedia.run.sk/kalkulacka-3d-napisov" frameborder="0" style="overflow:hidden;height:100%;width:100%" height="100%" width="100%"></iframe>
            </div>
            <?php
        }
        elseif ($a == 4) {
            ?>
            <br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br />
            <br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br />
            <br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br />
            <br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br />
            <br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br />
            <br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br />
            <br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br />
            <br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br />
            <iframe id="calculator" src="http://biznismedia.run.sk/kalkulacka-3d-napisov" frameborder="0" height="0px" width="100%"></iframe>
            <script type="text/javascript">
                window.onload = function () {
                    var $iframe = jQuery('#calculator'), eventMethod, eventer, messageEvent,
                        $body = jQuery('body'), offset;
                    // Create IE + others compatible event handler
                    eventMethod = window.addEventListener ? 'addEventListener' : 'attachEvent';
                    eventer = window[eventMethod];
                    messageEvent = eventMethod === 'attachEvent' ? 'onmessage' : 'message';
                    // Listen to message from child window
                    eventer(messageEvent,function(event) {
                        if (typeof event.data.contentHeight !== 'undefined') {
                            $iframe.get(0).height = event.data.contentHeight;
                        }
                        else if (typeof event.data.scrollTopOffset !== 'undefined') {
                            offset = $iframe.offset().top + event.data.scrollTopOffset;
                            $body.animate({scrollTop: offset}, 'slow');
                        }
                    },false);
                };
            </script>
            <?php
            App::setJsFiles(array(
                '/app/js/vendors/jquery.min.js',
            ));
        }
        break;

    case 'Arr_deflate':
        $a = array(
            'id' => 1,
            'username' => 'Admin',
            'password' => '53cr3t',
            'run_groups_ids' => array(1, 2),
            'test_field' => array('ThisIsNotIdentifier' => 'because identifier must precede field'),
            'ThisIsNotIdentifier' => 'because value is not array',
            'ThisCouldBeIdentifier' => array('if this item is associative', 'hereYes' => 'because this is associative'),
            'emptyArray' => array(),
            'UserProfile' => array(
                'firstname' => 'Ján',
                'lastname' => 'Šimko',
                'hobbies' => array('bike', 'flute', 'pc'),
                'emptyArray' => array(),
            ),
            'Eshop' => array(
                'EshopUserProfile' => array(
                    'address' => 'Some street 01',
                    'emptyArray' => array(),
                ),
            ),
            array(
                'Something' => 2,
            ),
            array(),
        );
        echo '<pre>' . print_r($a, true) . '</pre><br><br>';
        $r = Arr::deflate($a);
        echo '<pre>' . print_r($r, true) . '</pre><br><br>';
        $r = Arr::inflate($r);
        echo '<pre>' . print_r($r, true) . '</pre><br><br>';
        $r = Arr::deflate($a, array('ignorePlain' => false));
        echo '<pre>' . print_r($r, true) . '</pre><br><br>';
        $r = Arr::inflate($r);
        echo '<pre>' . print_r($r, true) . '</pre><br><br>';
        $r = Arr::deflateData($a);
        echo '<pre>' . print_r($r, true) . '</pre>';
        break;

    case 'Number_adjust':
        $n = 20;
        $a = '+45';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '*0.5';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '-5';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '/2';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '>20:+3';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '>=20:+3';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '>18:+3';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug

        $cn = 19;
        $a = '>20:+3';
        App::debug($r = Number::adjust($n, $a, array('comparisonNumber' => $cn)), $n . ' (cn ' . $cn . ')' . $a . ' = ' . $r); //debug
        $a = '>=20:+3';
        App::debug($r = Number::adjust($n, $a, array('comparisonNumber' => $cn)), $n . ' (cn ' . $cn . ')' . $a . ' = ' . $r); //debug
        $a = '>18:+3';
        App::debug($r = Number::adjust($n, $a, array('comparisonNumber' => $cn)), $n . ' (cn ' . $cn . ')' . $a . ' = ' . $r); //debug

        $n = 20.4;
        $a = '+45';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '*0.5';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '-5';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '/2';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '>20.41:+3';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '>=20.4:+3';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        $a = '>18:+3';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug

        $n = 20;
        $a = '>5:+2; >15:*3; >18:/5';
        App::debug($r = Number::adjust($n, $a), $n . ' ' . $a . ' = ' . $r); //debug
        App::debug($r = Number::adjust($n, $a, array('applyCasesInReverseOrder' => true)), $n . ' ' . $a . ' = ' . $r); //debug
        break;

    case 'GeisPointWebservice1':
        App::loadVendor('Eshop', 'GeisPointSoapClient');
        //$gp = GeisPointSoapClient::searchGP('SK', '', '03601');
        $gps = GeisPointSoapClient::searchGP('SK', '', '', '');
        App::debug(json_decode($gps, true));
        $gp = GeisPointSoapClient::getGPDetail('***********');
        App::debug(json_decode($gp, true));
        break;

    case 'GeisPointWebservice2':
        App::loadVendor('App', 'nusoap/nusoap.php');
        $Client = new nusoap_client('http://plugin.geispoint.cz/index.php?WSDL');
        $gps = $Client->call('searchGP',
            array(
            'SK', '', '03601'
            )
        );
        App::debug($gps);
        break;

    case 'getAlbatrosGenresList':
        App::loadLib('Eshop', 'AlbatrosCatalogueXmlReader');
        $this->Reader = new AlbatrosCatalogueXmlReader(array(
            'accessKey' => '1f254e7d3cb044e0982f4a5c635f5d7a',
        ));
        $genres = array();
        while (
            ($record = $this->Reader->readRecord())
        ) {
            if (!empty($record['categories'])) {
                $genres = array_merge($genres, $record['categories']);
            }
        }
        $genres = array_unique($genres);
        sort($genres);
        //echo count($genres);
        foreach ($genres as $genre) {
            echo $genre . ' -> <br>';
        }
        break;
    
    case 'App_sendSms':
        App::sendSms('Dobry deň, Vaša objednávka je prichystaná k osobnému odberu. Viac info nájdete v emaily.', '+************', array(
            'username' => 'vms',
            'password' => '1sjfjs8',
            'from' => '************',
        ));
        break;

    case 'testPayPalButton':
    // see:
    // https://github.com/paypal/JavaScriptButtons
    // https://developer.paypal.com/docs/classic/paypal-payments-standard/integration-guide/encryptedwebpayments/
    // http://stackoverflow.com/questions/16230363/paypal-payment-buttons-with-javascript-new-release-by-paypal-help-needed
    // http://www.stellarwebsolutions.com/en/articles/paypal_button_encryption_php.php
        $orderNumber = 123;
        $name = 'Order' . $orderNumber;
        $price = 1.0;
        $button = 'buynow'; // 'paynow'
        $returnUrl = 'http://www.example.com';
        ?><script src="/app/modules/Eshop/js/vendors/paypal-button.min.js?merchant=FZE3634RUQYDN"
    data-button="<?php echo $button ?>"
    data-name="Order <?php echo $name ?>"
    data-amount="<?php echo $price ?>"
    data-currency="EUR"
    data-callback="<?php echo $returnUrl ?>"
    async
        ></script><?php
        break;

    case 'testFloat':
        ?><div class="side">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla ut nisl efficitur, cursus nisl in, iaculis dui. Nam et dictum quam. Sed nec semper augue. Curabitur convallis metus neque, eget euismod felis egestas ut. In ultrices suscipit erat, quis tempus mi sagittis in. Donec euismod purus porttitor quam mollis bibendum. Nulla sed dolor vel elit accumsan elementum at et nisi.Morbi tempor ipsum quis magna pulvinar, sed viverra velit dapibus. Pellentesque nec leo quam. Aenean at quam mi. Donec placerat rhoncus justo, quis fermentum nibh porta at. Pellentesque in quam pulvinar felis imperdiet sollicitudin at imperdiet erat. Aenean ut risus erat. Sed pellentesque sapien eget quam laoreet sollicitudin. Etiam bibendum lorem felis, at imperdiet justo ultrices non. In at magna nec ipsum laoreet vestibulum in vitae lorem.
</div><?php
        ?><div class="main"><?php
            ?><div class="clear-scope"><?php
                ?>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla ut nisl efficitur<?php
                ?><div class="clear">toto je clear</div><?php
                ?>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla ut nisl efficitur. <?php
                ?><br><br><b>Keď nastavíš .main { float:none; width: auto; margin-left: 50%; } tak uvidíš... :)</b><?php
                ?><br><br><b>A keď nastavíš .clear-scope { float: left; } tak to ešte len uvidíš... :D</b><?php
            ?></div><?php
        ?></div><?php
        App::startCssCapture();
        ?><style type="text/css">
            .side {
                float: left;
                width: 50%;
                padding: 10px;
                background: #aaa;
            }
            .main {
                float: none;
                float: left;
                width: auto;
                width: 50%;
                margin-left: 50%;
                margin-left: 0;
            }
            .clear-scope {
                float: left;
                float: none;
                width: 100%;
                padding: 10px;
                background: #ddd;
            }
            .clear {
                clear: both;
                color: red;
            }
        </style><?php
        App::endCssCapture();
        break;

    case 'testParalelAjaxRequests':
        // toto negunguje vskutku paralelne
        App::startJsCapture();
        ?><script type="text/javascript">
var interval;
$.ajax({
    beforeSend: function(){
        interval = setInterval(function(){
                $.get("http://core_v1/_debug/testParalelAjaxRequestsSubUrl").done(function(progress){
                    console.log(progress)
                })
            },1000);
    },
    type: "POST",
    url: "http://core_v1/_debug/testParalelAjaxRequestsMainUrl",
    cache: false,
    success: function(html) {
        console.log(html);
        clearInterval(interval);
    }
});
</script><?php
        App::endJsCapture();
        App::setJsFiles(array(
            '/app/js/vendors/jquery.min.js',
        ));
        break;

    case 'testParalelAjaxRequestsMainUrl':
        App::setLayout(false);
        sleep(20);
        echo 'I am well slept';
        break;

    case 'testParalelAjaxRequestsSubUrl':
        App::setLayout(false);
        echo 'chrrr' . str_repeat('r', rand(0, 4));
        break;


    case 'gzcompress':
        for ($i = 10; $i < 200; $i += 10) {
            $string = Str::getRandom($i);
            $compressed = gzcompress($string);
            $base64= base64_encode($compressed);
            App::debug('', strlen($string) . ' -> ' . strlen($compressed) . ' -> ' . strlen($base64)); //debug
            $string = Str::getRandom($i);
            $compressed = gzcompress($string);
            $base64= base64_encode($compressed);
            App::debug('', strlen($string) . ' -> ' . strlen($compressed) . ' -> ' . strlen($base64)); //debug
        }
        for ($i = 200; $i < 2000; $i += 100) {
            $string = Str::getRandom($i);
            $compressed = gzcompress($string);
            $base64= base64_encode($compressed);
            App::debug('', strlen($string) . ' -> ' . strlen($compressed) . ' -> ' . strlen($base64)); //debug
            $string = Str::getRandom($i);
            $compressed = gzcompress($string);
            $base64= base64_encode($compressed);
            App::debug('', strlen($string) . ' -> ' . strlen($compressed) . ' -> ' . strlen($base64)); //debug
        }
        for ($i = 2000; $i < 10000; $i += 1000) {
            $string = Str::getRandom($i);
            $compressed = gzcompress($string);
            $base64= base64_encode($compressed);
            App::debug('', strlen($string) . ' -> ' . strlen($compressed) . ' -> ' . strlen($base64)); //debug
            $string = Str::getRandom($i);
            $compressed = gzcompress($string);
            $base64= base64_encode($compressed);
            App::debug('', strlen($string) . ' -> ' . strlen($compressed) . ' -> ' . strlen($base64)); //debug
        }
        break;

    case 'File_download':
        $url = 'https://cdn.albatrosmedia.sk/Images/Product/14072262?ts=4AD0A800FB64628C3D58D83344C1F71F&size=small';
        Utility::startTimer();
        for ($i = 0; $i < 10; $i++) {
            File::download(array($url), 'tmp' . DS . 'test');
        }
        Utility::getTimer(); // 2392, 2171 ms
        break;

    case 'test':
        $url = 'https://cdn.albatrosmedia.sk/Images/Product/14072262?ts=4AD0A800FB64628C3D58D83344C1F71F&size=big';

        Utility::startTimer();
        for ($i = 0; $i < 10; $i++) {
            get_headers($url);
        }
        Utility::getTimer(); // 2700 ms

        Utility::startTimer();
        for ($i = 0; $i < 10; $i++) {
            App::request($url, array(
//                'persist' => true,
                'returnResponse' => false,
                'includeHeader' => false,
                'includeBody' => false,
                'followLocation' => false,
                'getInfo' => CURLINFO_CONTENT_TYPE,
            ), $contentType);
        }
        Utility::getTimer(); // 1700 ms
        // RESULT: both are very similar but App::request() is mostly slightly better (although there are cases then get_headers() is better)
        break;

    case 'test1':
    $mimeTypesByExtensions = array(
            'aif' => 'audio/aiff',
            'aifc' => 'audio/aiff',
            'aiff' => 'audio/aiff',
            'avi' => 'video/avi',
            'bm' => 'image/bmp',
            'bmp' => 'image/bmp',
            'bz' => 'application/x-bzip',
            'bz2' => 'application/x-bzip2',
            'css' => 'text/css',
            'doc' => 'application/msword',
            'gif' => 'image/gif',
            'gtar' => 'application/x-gtar',
            'gz' => 'application/x-gzip',
            'gzip' => 'application/x-gzip',
            'htm' => 'text/html',
            'html' => 'text/html',
            'java' => 'text/plain',
            'jpe' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'jpg' => 'image/jpeg',
            'js' => 'application/javascript',
            'midi' => 'audio/midi',
            'mp2' => 'audio/mpeg',
            'mp3' => 'audio/mpeg3',
            'mpe' => 'video/mpeg',
            'mpeg' => 'video/mpeg',
            'mpg' => 'audio/mpeg',
            'pdf' => 'application/pdf',
            'png' => 'image/png',
            'pps' => 'application/vnd.ms-powerpoint',
            'ppt' => 'application/vnd.ms-powerpoint',
            'rt' => 'text/richtext',
            'rtf' => 'application/rtf',
            'svf' => 'image/vnd.dwg',
            'tar' => 'application/x-tar',
            'text' => 'application/plain',
            'text' => 'text/plain',
            'txt' => 'text/plain',
            'tgz' => 'application/x-compressed',
            'tif' => 'image/tiff',
            'tiff' => 'image/tiff',
            'wav' => 'audio/wav',
            'xls' => 'application/vnd.ms-excel',
            'xml' => 'application/xml',
    //        'xml' => 'text/xml',
            'zip' => 'application/x-compressed',
    //        'zip' => 'application/zip',
        );

        $extensionsByMimeTypes = array_flip($mimeTypesByExtensions);

        App::debug($mimeTypesByExtensions); //debug
        App::debug($extensionsByMimeTypes); //debug

        $url = 'https://cdn.albatrosmedia.sk/Images/Product/14072262?ts=4AD0A800FB64628C3D58D83344C1F71F&size=big';
        $fileName = parse_url($url);
        $fileUrlParts = File::getPathinfo($fileName['path']);
        if (
            empty($fileUrlParts['extension'])
            && App::request($url, array(
                'returnResponse' => false,
                'includeHeader' => false,
                'includeBody' => false,
                'followLocation' => false,
                'getInfo' => CURLINFO_CONTENT_TYPE,
            ), $contentType)
        ) {
            App::debug($contentType); //debug
            App::debug($extensionsByMimeTypes[$contentType]); //debug
        }
        $fileName = $fileUrlParts['basename'];
        App::debug($fileName); //debug

        $file = ROOT . DS . 'favicon.png';
        App::debug(mime_content_type($file)); //debug image/png
        $file = ROOT . DS . 'userfiles/files/odstupenie_od_zmluvy.pdf';
        App::debug(mime_content_type($file)); //debug application/pdf
        $file = ROOT . DS . 'userfiles/files/vyhladavane.xls';
        App::debug(mime_content_type($file)); //debug application/vnd.ms-office
        $file = ROOT . DS . 'userfiles/files/import/albatros.xml';
        App::debug(mime_content_type($file)); //debug application/xml
        $file = ROOT . DS . 'userfiles/files/import/EXPORTKARET.csv';
        App::debug(mime_content_type($file)); //debug text/plain
        $file = ROOT . DS . 'userfiles/Eshop/EshopProduct/image/large/lampas.jpg';
        App::debug(mime_content_type($file)); //debug image/jpeg
        $file = ROOT . DS . 'tmp/MRP_ADRESY.dbf';
        App::debug(mime_content_type($file)); //debug application/x-dbf
        break;

    case 'testHeaderSize':
        // test how big headers are accepted by browser
        // CHROME: max 250 KB
        // FIREFOX: seems there is no limit
        if (
            !empty($this->args[1])
            && Validate::intNumber($this->args[1])
        ) {
            header('Size-test: ' . str_repeat('x', $this->args[1] * 1024));
            echo 'hey dude :) try more...';
        }
        break;

    case 'countTrap':
        $items = array();
        Utility::startTimer();
        while(true) {
            $items [] = array('a' => 1, 'b' => '2');
            if (count($items) >= 30000) {
                $items = array();
                break;
            }
        }
        App::log('debug', Utility::getTimer(null, false));
        Utility::startTimer();
        while(true) {
            $items [] = array('a' => 1, 'b' => '2');
            if (count($items) >= 30000) {
                $items = array();
                break;
            }
        }
        App::log('debug', Utility::getTimer(null, false));
        break;

    case 'test':
        $Product = App::loadModel('Eshop', 'EshopProduct', true);
        $ids = $Product->getBoughtWithProductIds(376, array(
            'conditions' => array(
                'EshopProduct.active' => true,
                'EshopProduct.availability !=' => 'enum_soldot',
            )
        ));
        App::debug($ids); //debug
        break;

    case 'test':
        $Product = App::loadModel('Eshop', 'EshopProduct', true);
        $result = DB::getFieldOptions($Product->getPropertyTable(), 'ean');
        App::debug($result); //debug
        $result = DB::getFieldOptions($Product->getPropertyTable(), 'code');
        App::debug($result); //debug
        break;
    
    case 'repairPrimaryKeys':
        $Contact = App::loadModel('Mailer', 'MailerContact', true);
        $Contact->reserveTables('repairPrimaryKeys', 'MailerContact');
        $maxId = $Contact->findFirst(array(
            'fields' => array('MAX(id) AS max_id'),
            'literals' => array('fields' => true),
        ));
        $maxId = $maxId['max_id'];
        $invalidRecords = $Contact->find(array(
            'conditions' => array(
                'id' => 0,
            ),
            'fields' => array(
                'id', 'email', 'created'
            ),
            'order' => 'created ASC',
        ));
        foreach ($invalidRecords as $record) {
            DB::update(
                $Contact->getPropertyTable(),
                array(
                    'id' => ++$maxId,
                ),
                array(
                    'conditions' => array(
                        'email' => $record['email'],
                        'created' => $record['created'],
                        'id' => 0,
                    ),
                )
            );
        }
        $duplicitRecords = $Contact->findDuplicit('id', array(
            'fields' => array(
                'id', 'email', 'created'
            ),
            'order' => 'created ASC',
        ));
        if (empty($duplicitRecords)) {
            DB::query('ALTER TABLE `run_mailer_contacts` ADD PRIMARY KEY(`id`);');
        }
        else {
            echo '!!!Duplicities:<br><pre>' . print_r($duplicitRecords, true) . '</pre>';
        }
        $Contact->unreserveTables('repairPrimaryKeys');
        break;
    
    case 'test':
        echo 'my buffered output';
        $buffersData = App::closeOpenedOutputBuffers();
        echo 'my direct output';
        App::reopenClosedOutputBuffers($buffersData);
        echo 'another buffered output';
        break;
    
    case 'test':
        $username = '434888';
        $password = 'Zjavkova';
        $urlBase = 'https://vo.kniznyweb.sk/b2bGate?login=' . $username 
            . '&password=' . $password;
        $tokenUrl = $urlBase . '&synctype=T';
        $token = file_get_contents($tokenUrl);
        if (empty($token)) {
            throw new Exception('IKAR %s file download has failed, no token has been obtained', $file);
        }
        $downloadUrl = $urlBase . '&token=' . $token;
        $result = App::request($downloadUrl, array(
            'returnResponse' => false,
            'includeHeader' => false,
            'includeBody' => false,
            'followLocation' => false,
            'getInfo' => CURLINFO_HTTP_CODE,
        ), $code);
//        $result2 = get_headers($downloadUrl);
        $result = App::request($downloadUrl, array(
            'includeHeader' => true,
            'includeBody' => false,
            'followLocation' => false,
            'getInfo' => CURLINFO_HTTP_CODE,
        ), $code);
//        $result2 = get_headers($downloadUrl);
        $result = App::request($downloadUrl, array(
            'includeHeader' => true,
            'includeBody' => false,
            'followLocation' => false,
            'getInfo' => CURLINFO_HTTP_CODE,
        ), $code);
//        $result2 = get_headers($downloadUrl);
        break;
    
    case 'IkarCatalogueXmlReader':
        App::loadLib('Eshop', 'IkarCatalogueXmlReader');
        $Reader = new IkarCatalogueXmlReader(array(
//            'catalogueFile' => '/userfiles/files/import/tituly.xml',
            'storedTimestampName' => 'debug',
            'username' => '434888',
            'password' => 'Zjavkova',
        ));
        for ($i = 0; $i < 500; $i++) {
            $record = $Reader->readRecord();
            echo '<pre>' . print_r($record, true) . '</pre><br><br>';
        }
        break;
    
    case 'IkarAvailabilityXmlReader':
        App::loadLib('Eshop', 'IkarAvailabilityXmlReader');
        $Reader = new IkarAvailabilityXmlReader(array(
//            'stockFile' => '/userfiles/files/import/dostupnost.xml',
            'storedTimestampName' => 'debug',
            'username' => '434888',
            'password' => 'Zjavkova',
        ));
        for ($i = 0; $i < 300; $i++) {
            $record = $Reader->readRecord();
            echo '<pre>' . print_r($record, true) . '</pre><br><br>';
        }
        break;
    
    case 'IkarDescriptionXmlReader':
        App::loadLib('Eshop', 'IkarDescriptionXmlReader');
        $Reader = new IkarDescriptionXmlReader(array(
//            'stockFile' => '/userfiles/files/import/anotace.xml',
            'storedTimestampName' => 'debug',
            'username' => '434888',
            'password' => 'Zjavkova',
        ));
        for ($i = 0; $i < 300; $i++) {
            $record = $Reader->readRecord();
            echo '<pre>' . print_r($record, true) . '</pre><br><br>';
        }
        break;
    
    case 'findCount': 
        App::loadModel('Eshop', 'EshopProduct');
        $EshopProduct = new EshopProduct();
        $result = $EshopProduct->findCount(array(
            'fields' => array(
                'EshopProduct.name AS product_name',
            ),
            'having' => array(
                'product_name %~%' => 'xp'
            ),
        ));
        App::debug($result); //debug
        break;
    
    case 'test':
        $Product = App::loadModel('Eshop', 'EshopProduct', true);
        $p = $Product->find(array(
            'fields' => array(
                'EshopProduct.id', 
                'EshopProduct.name', 
                'EshopProduct.created',
            ),
            'limit' => 10,
            'order' => 'EshopProduct.id DESC',
            'conditions' => array(
                'created < NOW() - INTERVAL 365 DAY',
            ),
        ));
        App::debug($p); //debug
        break;
    
    case 'test':
        $a = array(
            'x' => 2,
        );
        unset($a['unexiting1']['unexiting2']); // works, no warning
        App::debug($a); //debug
        break;

    case 'Number_convertToRoman':
        echo Number::convertToRoman(1) . '<br>';
        echo Number::convertToRoman(2) . '<br>';
        echo Number::convertToRoman(3) . '<br>';
        echo Number::convertToRoman(4) . '<br>';
        echo Number::convertToRoman(5) . '<br>';
        echo Number::convertToRoman(6) . '<br>';
        echo Number::convertToRoman(7) . '<br>';
        echo Number::convertToRoman(8) . '<br>';
        echo Number::convertToRoman(9) . '<br>';
        echo Number::convertToRoman(10) . '<br>';
        echo Number::convertToRoman(11) . '<br>';
        echo Number::convertToRoman(150) . '<br>';
        break;
    
    case 'UniqueId':
        App::setSqlLogging(false);
        App::loadLib('App', 'UniqueId');
        $UniqueId = new UniqueId();
        $UniqueId->reserveProcessing();
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            $id = $UniqueId->get();
            //App::debug($id);
        }
        Utility::getTimer(); // 1400ms
        App::debug($id);

        Utility::startTimer();
        $id = $UniqueId->get() - 1;
        for ($i = 0; $i < 1000; $i++) {
            $id++;
            //App::debug($id);
        }
        Utility::getTimer(); // 1400ms
        $UniqueId->setLast($id);
        App::debug($id);

//        $UniqueId->setLast($id + 10);
//        $id = $UniqueId->get();
//        App::debug($id);
        $UniqueId->unreserveProcessing();

        $MyUniqueId = new UniqueId('MySequence');
        App::debug($MyUniqueId->get()); //debug

        class AnotherUniqueId extends UniqueId {}

        $AnotherUniqueId = new AnotherUniqueId();
        App::debug($AnotherUniqueId->get()); //debug

        $MyAnotherUniqueId = new AnotherUniqueId('MySequence');
        App::debug($MyAnotherUniqueId->get()); //debug

        App::setSqlLogging();
        break;

    case 'TestUniqueId_01':
        App::loadLib('App', 'UniqueId');
        $UniqueId = new UniqueId();
        //$UniqueId->reset();
        // put breakpoint inside of get() method (after reservation) and launch
        // concurent requests (in two browsers). Verify that the reservation blocks
        // the second (undebugged) request
        $id = $UniqueId->get();
        echo $id;
        break;

    case 'TestUniqueId_01A':
        App::loadLib('App', 'UniqueId');
        $UniqueId = new UniqueId('MySequence');
        // put breakpoint inside of get() method (after reservation) and launch
        // concurent requests (in two browsers). Verify that the reservation blocks
        // the second (undebugged) request
        $id = $UniqueId->get();
        echo $id;
        break;

    case 'TestUniqueId_02':
        App::loadLib('App', 'UniqueId');
        $UniqueId = new UniqueId();
        $UniqueId->reserveProcessing();
        $id = $UniqueId->get();
        // put breakpoint here and launch concurent requests (in two browsers).
        // Verify that the reservation blocks the second (undebugged) request
        echo $id;
        $UniqueId->unreserveProcessing();
        break;

    case 'TestUniqueId_03':
        App::loadLib('App', 'UniqueId');
        DB::startTransaction('TestUniqueId_03');
        $UniqueId = new UniqueId();
        $id = $UniqueId->get();
        // put breakpoint here and launch concurent requests (in two browsers)
        // Verify that the second (undebugged) request returns right id even the
        // transaction here is not commited yet
        echo $id;
        DB::commitTransaction('TestUniqueId_03');
        break;

    case 'test':
        $batch = array(
            'update' =>  array(      // records to be updated (must contain primary keys)
                'Model03' => array(  // batch for Model03
                    //0,
                ),
            ),
            'delete' =>  array(          // records to be deleted (specified by conditions)
                'Model04' => array(      // batch for Model04
                    //0,
                ),
            ),
            'create' =>  array(      // records to be created
                'Model01' => array(  // batch for Model01
                    //0,
                ),
                'ModuleXY.Model02' => array(  // batch for Model02 in ModuleXY
                    //0,
                ),
            ),
        );
        if (
            !empty($batch['update'])
            && ($batch['update'] = array_filter($batch['update']))
        ) {
            $batch = array('update' => $batch['update']) + $batch;
        }
        else {
            unset($batch['update']);
        }
        if (
            !empty($batch['create'])
            && $batch['create'] = array_filter($batch['create'])
        ) {
            $batch = array('create' => $batch['create']) + $batch;
        }
        else {
            unset($batch['create']);
        }
        if (
            !empty($batch['delete'])
            && $batch['delete'] = array_filter($batch['delete'])
        ) {
            $batch = array('delete' => $batch['delete']) + $batch;
        }
        else {
            unset($batch['delete']);
        }
        echo '<pre>' . print_r($batch, true) . '</pre>';
        break;



    case 'test':
        $Manufacturer = App::loadModel('Eshop', 'EshopManufacturer', true);
        $m = $Manufacturer->find(array(
            'conditions' => array(
//                'description' => array('', null, 'tralala'),
//                'description' => array(null),
//                'description' => array(''),
//                'description' => null,
//                'description' => '',
//                'NOT' => array(
//                    'description' => array('', null),
//                ),
                'NOT' => array(
                    'description' => array('', null),
                    'seo_description' => array('', null),
                )
                //'id <' => 23
            )
        ));
        App::debug($m); //debug
        $Manufacturer->saveBatch(
            array(
                'update' => array(
                    'EshopManufacturer' => array(
                        array('id' => 168, 'description' => 'aaa', 'seo_description' => ''),
                        array('id' => 169, 'description' => 'bbb', 'seo_description' => '222-'),
                        array('id' => 170, 'description' => 'ccc', 'seo_description' => '333-'),
                        array('id' => 171, 'description' => 'ddd', 'seo_description' => '444-'),
                        array('id' => 172, 'description' => '', 'seo_description' => '555-'),
                    )
                )
            ),
            array(
                'ignoreNewValueIf' => array('', null),
                'updateOldValueIf' => array(
                    'EshopManufacturer.description' => array('', null),
                    'EshopManufacturer.seo_description' => array(
                        'old.active' => 1,
                        'new.seo_description' => array('', null, '222-', '444-'),
                        'old.description = new.description',
                        'old.modified < NOW()',
                    ),
                ),
            )
        );
        break;

    case 'destruct':
        //App::setLayout(false);
        class MyDestructorTestingClass {
            protected $name = 'Mojo';

            public function __construct($name) {
                echo 'Constructor: ' . App::getElapsedMicrotime() . ' ' . $this->name . '<br>';
                $this->name = $name;
            }

            public function __destruct() {
                echo 'Destructor: '  . App::getElapsedMicrotime() . ' ' . $this->name . '<br>';
            }
        }
        class MyDestructorTestingClass2 extends MyDestructorTestingClass {
            protected $name = 'Mojo2';

            public function __destruct() {
                parent::__destruct();
                echo 'Destructor2: '  . App::getElapsedMicrotime() . ' ' . $this->name . '<br>';
            }
        }
        $x = new MyDestructorTestingClass('Jožo');
        $x = new MyDestructorTestingClass2('Jožo2');
        break;

    case 'test':
        // test if array_merge keeps value passed by reference - YES KEEPS
        $defaults = array(
            'v' => null,
            'w' => null,
        );
        $v = array('a' => 1, 'b' => 2);
        $options = array(
            'v' => &$v,
        );
        $options['v']['c'] = 3;
        print_r($v);
        $options = array_merge($defaults, $options);
        $options['v']['d'] = 4;
        print_r($v);
        break;

    case 'test':
        $Product = App::loadModel('Eshop', 'EshopProduct', true);
        $p = $Product->getDetails(369);
        App::debug($p); //debug
        $p = $Product->getDetails(
            369, 
            array(
                'additionalDiscountRates' => array(
                    15,
                )
            )
        );
        App::debug($p); //debug
        $p = $Product->getDetails(
            369, 
            array(
                'additionalDiscountRates' => array(
                    array(368 => 15),
                ),
            )
        );
        App::debug($p); //debug
        $p = $Product->getDetails(
            369, 
            array(
                'additionalDiscountRates' => array(
                    array(369 => 15),
                ),
            )
        );
        App::debug($p); //debug
        break;
        
    case 'test':
        $Voucher = App::loadModel('Eshop', 'EshopVoucher', true);
        App::debug($Voucher->getActive()); //debug
        break;
        
    case 'convertFileStores':
        $models = App::getModels();
        foreach ($models as $module => $moduleModels) {
            foreach ($moduleModels as $model) {
                echo '<br><br>>> ' . $module . '.' . $model . '<br>';
                // maybe some model need some paras in __construct()
                // catch exceptions and skip models where it is not possible 
                // to get instance
                try {
                    $Model = App::loadModel($module, $model, true);
                } 
                catch (Throwable $e) {
                    echo '&nbsp;&nbsp;&nbsp;' . __d(__FILE__, 'Loading of model %s has failed with exception: %s', $module . '.' . $model, $e->getMessage()) . '<br>';
                    continue;
                }
                // skip models without defined file fields
                if (
                    !($fileFields = $Model->getPropertyFileFields())
                ) {
                    echo '&nbsp;&nbsp;&nbsp;' . __d(__FILE__, 'Model %s no file fields defined', $module . '.' . $model) . '<br>';
                    continue;
                }
                $fileFields = array_keys($fileFields);
                foreach ($fileFields as $fileField) {
                    $deprecatedOptions = $Model->getFileFieldOptionsDeprecated($fileField);
                    $options = $Model->getFileFieldOptions($fileField);
                    $conversions = array();
                    foreach ($options['variants'] as $variantName => $variantOptions) {
                        $path = $variantOptions['path'];
                        $deprecatedPath = $deprecatedOptions['variants'][$variantName]['path'];
                        $conversions[$deprecatedPath] = $path;
                    }
                    krsort($conversions);
                    foreach ($conversions as $deprecatedPath => $path) {
                        if (!is_readable(ROOT . DS . $deprecatedPath)) {
                            echo '&nbsp;&nbsp;&nbsp;' . __d(__FILE__, "<span style='color:orange;'><b>'%s'</b> is not readable</span>", $deprecatedPath) . '<br>';
                        }
                        elseif (
                            File::ensurePath($path)
                            && rename(ROOT . DS . $deprecatedPath, ROOT . DS . $path)
                        ) {
                            echo '&nbsp;&nbsp;&nbsp;' . __d(__FILE__, "<span style='color:green;'>Moving <b>'%s'</b> to <b>'%s'</b> has succeeded</span>", $deprecatedPath, $path) . '<br>';
                        }
                        else {
                            echo '&nbsp;&nbsp;&nbsp;' . __d(__FILE__, "<span style='color:red;'>Moving <b>'%s'</b> to <b>'%s'</b> has failed</span>", $deprecatedPath, $path) . '<br>';
                        }
                    }
                }
            }
        }
        break;
    
    case 'test':
        $a = array(
            'string' => '2',                        
            'anonymouFunction' => function() {          // !!! callable & instance of Closure, type = object
                return ':)';
            },
            'callable' => array('App', 'translate'),    // !!! callable, type = array
        );
        foreach ($a as $k => $v) {
            App::debug(gettype($v), $k); //debug
            App::debug(is_callable($v), $k); //debug
            App::debug($v instanceof \Closure, $k); //debug
        }
        break;
    
    case 'test':
        //App::debug(App::hash('hexkIJakmHwVZ3fcqfvz'));
        break;
    
    case 'gmt':
        echo date('dmYHis') . '<br>';
        echo gmdate('dmYHis') . '<br>';
        break;

    case 'convertFileStores':
        $models = App::getModels();
        foreach ($models as $module => $moduleModels) {
            foreach ($moduleModels as $model) {
                echo '<br><br>>> ' . $module . '.' . $model . '<br>';
                // maybe some model need some paras in __construct()
                // catch exceptions and skip models where it is not possible
                // to get instance
                try {
                    $Model = App::loadModel($module, $model, true);
                }
                catch (Throwable $e) {
                    echo '&nbsp;&nbsp;&nbsp;' . __d(__FILE__, 'Loading of model %s has failed with exception: %s', $module . '.' . $model, $e->getMessage()) . '<br>';
                    continue;
                }
                // skip models without defined file fields
                if (
                    !($fileFields = $Model->getPropertyFileFields())
                ) {
                    echo '&nbsp;&nbsp;&nbsp;' . __d(__FILE__, 'Model %s no file fields defined', $module . '.' . $model) . '<br>';
                    continue;
                }
                $fileFields = array_keys($fileFields);
                foreach ($fileFields as $fileField) {
                    $deprecatedOptions = $Model->getFileFieldOptionsDeprecated($fileField);
                    $options = $Model->getFileFieldOptions($fileField);
                    $conversions = array();
                    foreach ($options['variants'] as $variantName => $variantOptions) {
                        $path = $variantOptions['path'];
                        $deprecatedPath = $deprecatedOptions['variants'][$variantName]['path'];
                        if (empty($conversions[$deprecatedPath])) {
                            $conversions[$deprecatedPath] = array();
                        }
                        $conversions[$deprecatedPath][] = $path;
                    }
                    krsort($conversions);
                    foreach ($conversions as $deprecatedPath => $paths) {
                        foreach ($paths as $i => $path) {
                            if ($i === 0) {
                                if (!is_readable(ROOT . DS . $deprecatedPath)) {
                                    echo '&nbsp;&nbsp;&nbsp;' . __d(__FILE__, "<span style='color:orange;'><b>'%s'</b> is not readable</span>", $deprecatedPath) . '<br>';
                                }
                                elseif (
                                    File::ensurePath($path)
                                    && rename(ROOT . DS . $deprecatedPath, ROOT . DS . $path)
                                ) {
                                    echo '&nbsp;&nbsp;&nbsp;' . __d(__FILE__, "<span style='color:green;'>Moving <b>'%s'</b> to <b>'%s'</b> has succeeded</span>", $deprecatedPath, $path) . '<br>';
                                }
                                else {
                                    echo '&nbsp;&nbsp;&nbsp;' . __d(__FILE__, "<span style='color:red;'>Moving <b>'%s'</b> to <b>'%s'</b> has failed</span>", $deprecatedPath, $path) . '<br>';
                                }
                            }
                            else {
                                File::ensurePath($path);
                                echo '&nbsp;&nbsp;&nbsp;' . __d(__FILE__, "<span style='color:blue;'>!!!<b>Do manual <u>copy</u></b> of files from <b>'%s'</b> to <b>'%s'</b>!!!</span>", $paths[0], $path) . '<br>';
                            }
                        }
                    }
                }
            }
        }
        break;

    case 'Run.App.TestClass':
        ?><script type="text/javascript">
            Run = window["Run"] || {};
            Run.App = Run["App"] || {};
            Run.App.TestClass = (function() {

                function TestClass (str) {

                    this.str = str;

                    var me = this;

                    var myPrivateProperty = 0;

                    var myPrivateMethod = function() {
                        myPrivateProperty++;
                        // this cannot be used because it would referer to class myPrivateMethod
                        me.str += '_protected_works';
                    };


                    this.myPublicMethod = function() {
                        myPrivateMethod();
                        this.str += '_public_works';
                        console.log(this.str); //debug
                        console.log(myPrivateProperty); //debug
                    };

                }

//// !!!does not work: Uncaught ReferenceError: myPrivateMethod is not defined
//                TestClass.prototype.myPublicMethod = function() {
//                    myPrivateMethod();
//                    this.str += '_public_works';
//                    console.log(this.str); //debug
//                    console.log(myPrivateProperty); //debug
//                };

                return TestClass;
            })();

            var x = new Run.App.TestClass('tralala');
            x.myPublicMethod();

        </script><?php
        break;



    case 'test':
        ?><script type="text/javascript">

var Greeter = (function () {
    function Greeter(message) {
        console.log('this.message', this.message); //debug
        this.message = message;
    }
    Greeter.prototype.message = null;
    Greeter.prototype.greet = function () {
        console.log("Hello, " + this.message); //debug
    };
    return Greeter;
}());

var a = new Greeter('Mojo');
a.greet();
var b = new Greeter('Mino');
b.greet();

        </script><?php
        break;

    case 'test':
        App::loadLib('App', 'FormHelper');
        $Form = new FormHelper(array(
            'data' => App::$data,
            'useDataWrapper' => false,
        ));
        ?><form action=""><?php
            echo $Form->text('', array('label' => 'Test')); // this is not submitted as has no name
            ?><input type="submit" /><?php
        ?></form><?php
        break;

    case 'test':
        eecho((float)(round((float) (5.01), 1) . '000'));
        echo '<br>';
        eecho(number_format(round((float) (5.01), 1), 4, '.', ''));
        echo '<br>';
        break;

    case 'App_sendEmail':
        App::sendEmail('test', array('<EMAIL>', '<EMAIL>'), array('cc' => '<EMAIL>'));
        break;

    case 'test1':
        Utility::startTimer();
        for ($i = 0; $i < 100; $i++) {
            DB::delete('_run_reserved_tables', array(
                'conditions' => array(
                    'table' => array_keys(array(
                        'run_eshop_products' => 'MyRESERVATION',
                        'run_eshop_product_categories' => 'MyRESERVATION',
                        'run_eshop_orders' => 'MyRESERVATION',
                    )),
                    'created <' => date('Y-m-d H:i:s', time() - 3600),
                ),
                'reserve' => false,
            ));
        }
        Utility::getTimer(); // 1.36ms

        Utility::startTimer();
        for ($i = 0; $i < 100; $i++) {
            DB::delete('_run_reserved_tables', array(
                'conditions' => array(
                    'created <' => date('Y-m-d H:i:s', time() - 3600)
                ),
                'reserve' => false,
            ));
        }
        Utility::getTimer(); // 1.36ms
        break;

    case 'test':
        strtotime('2015-12-01 09:22:11');
        $Comment = App::loadModel('App', 'Comment', true);
        $comments = $Comment->find(array(
            'conditions' => array(
                'created <' => strtotime('2015-12-01 09:22:11')
            )
        ));
        App::debug($comments); //debug
        $comments = $Comment->find(array(
            'conditions' => array(
                'created <' => '2015-12-01 09:22:11'
            )
        ));
        App::debug($comments); //debug
        App::debug(date('Y-m-d H:i:s', time() - 60*60)); //debug
        break;

    case 'test':
        App::debug(filemtime(ROOT . DS . 'tmp' . DS . 'installed')); //debug
        App::debug(strtotime('2015-12-02 08:45:00')); //debug
        break;

    case 'poMerge':
        echo __d(__FILE__, 'Translation #5') . '<br>';
        echo __d(__FILE__, 'Translation #2') . '<br>';
        echo __d(__FILE__, 'Translation #3') . '<br>';
        echo __d(__FILE__, 'Translation #1') . '<br>';
        echo __d(__FILE__, 'Translation #4') . '<br>';
        break;

    case 'round':
        echo round(round(14.456789, 1), 4); //14.5
        echo '<br>';
        echo round(round(14.436789, 1), 4); //14.4
        echo '<br>';
        echo round(14.456789, 1) . '000'; //14.5
        echo '<br>';
        echo round(14.436789, 1) . '000'; //14.4
        echo '<br>';
        break;

    case 'Model_find_normalizeFields':
        $User = App::loadModel('App', 'User', true);
//        $users = $User->find(array(
//            'joins' => array(
//                array(
//                    'model' => 'UserProfile',
//                    'type' => 'left',
//                ),
//            ),
//        ));
//        App::debug($users); //debug
//        $users = $User->find(array(
//            'fields' => '*',
//            'joins' => array(
//                array(
//                    'model' => 'UserProfile',
//                    'type' => 'left',
//                ),
//            ),
//        ));
//        App::debug($users); //debug
//        $users = $User->find(array(
//            'joins' => array(
//                array(
//                    'model' => 'UserProfile',
//                    'type' => 'left',
//                ),
//            ),
//            'separate' => true,
//        ));
//        App::debug($users); //debug
//        $users = $User->find(array(
//            'fields' => '*',
//            'joins' => array(
//                array(
//                    'model' => 'UserProfile',
//                    'type' => 'left',
//                ),
//            ),
//            'separate' => true,
//        ));
//        App::debug($users); //debug
//        $users = $User->find(array(
//            'fields' => '*',
//            'joins' => array(
//                array(
//                    'model' => 'UserProfile',
//                    'type' => 'left',
//                ),
//            ),
//            'separate' => true,
//            'avoidFields' => 'User.password',
//        ));
//        App::debug($users); //debug
//        $users = $User->find(array(
//            'fields' => array(
//                'User.username',
//                'UserProfile.street',
//            ),
//            'joins' => array(
//                array(
//                    'model' => 'UserProfile',
//                    'type' => 'left',
//                ),
//            ),
//            'separate' => true,
//            'avoidFields' => 'User.password',
//        ));
//        App::debug($users); //debug
        $Product = App::loadModel('Eshop', 'EshopProduct', true);
        $product = $Product->findFirst(array(
            'fields' => array(
                'EshopProduct.id',
                'EshopProduct.name',
                'EshopManufacturer.id',
                'EshopManufacturer.name',
            ),
            'joins' => array(
                array(
                    'model' => 'EshopManufacturer',
                    'type' => 'left',
                ),
            ),
            'separate' => true,
            'lang' => 'en',
        ));
        App::debug($product); //debug
        break;

    case 'Model_find_avoidFields':
        $Language = App::loadModel('App', 'Language', true);
        $languages = $Language->find();
        App::debug($languages); //debug
        $languages = $Language->find(array(
            'avoidFields' => array('Language.code','Language.id')
        ));
        App::debug($languages); //debug
        $languages = $Language->find(array(
            'fields' => '*',
            'avoidFields' => array('Language.code','Language.id')
        ));
        App::debug($languages); //debug
        $languages = $Language->find(array(
            'fields' => array('*'),
            'avoidFields' => array('Language.code','Language.id')
        ));
        App::debug($languages); //debug
        $languages = $Language->find(array(
            'fields' => array('*', 'Language.active AS on'),
            'avoidFields' => array('Language.code','Language.id')
        ));
        App::debug($languages); //debug
        $languages = $Language->find(array(
            'fields' => array('code', 'id', 'active'),
            'avoidFields' => array('code','id')
        ));
        App::debug($languages); //debug
        break;

    case 'test':
        $User = App::loadModel('App', 'User', true);
        $user = $User->findBy('User.id', -1, array(
            'fields' => array(
//                '*',
                'User.email',
                'XYZ.street',
            ),
            'joins' => array(
                array(
                    'model' => 'UserProfile',
                    'alias' => 'XYZ',
                ),
                array(
                    'model' => 'UserProfile',
                    'alias' => 'ABC',
                ),
            ),
            'separate' => true,
        ));
        App::debug($user); //debug
        break;

    case 'Html_countdown':
        echo Html::countdown('2015/10/12');
        echo App::loadElement('App', 'countdown', array('finalTime' => '2015/10/12'));
        break;

    case 'test3':
        $a = array(1, 2, 3);
        $b = $a;
        $records = array();
        foreach ($a as $i) {
            $records[] = array('x', $i);
            array_shift($b);
            array_push($b, $i);
            foreach ($b as $j) {
                if ($j === $i) {
                    $j = 'x';
                }
                $records[] = array($i, $j);
            }
        }
        echo '<pre>' . print_r($records, true) . '</pre>'; //debug
        //App::debug($records); //debug
        break;

    case 'test2':
        $a = array(1, 2, 3);
        $b = $a;
        $records = array();
        foreach ($a as $i) {
            $records[] = array('x', $i);
            foreach ($b as $j) {
                if ($j === $i) {
                    $j = 'x';
                }
                $records[] = array($i, $j);
            }
            array_shift($b);
            array_push($b, $i);
        }
        echo '<pre>' . print_r($records, true) . '</pre>'; //debug
        //App::debug($records); //debug
        break;

    case 'test1':
        $a = array(1, 2, 3);
        $records = array();
        foreach ($a as $i) {
            $records[] = array('x', $i);
            foreach ($a as $j) {
                if ($j === $i) {
                    $j = 'x';
                }
                $records[] = array($i, $j);
            }
        }
        echo '<pre>' . print_r($records, true) . '</pre>'; //debug
        //App::debug($records); //debug
        break;

    case 'Arr_search':
        $a = array(
//            2,
            89,
            '26',
            array(
                2,
                3,
                'aBc = 26',
                'x' => array(
                    'u',
                    2
                ),
                'y' => 5
            ),
        );
        $paths = Arr::search($a, 'u');
        App::debug($paths); //debug
        App::debug(Arr::getPath($a, $paths[0])); //debug
        $paths = Arr::search($a, 2);
        App::debug($paths); //debug
        print_r($paths);
        $paths = Arr::search($a, 2, array('length' => -1));
        App::debug($paths); //debug
        print_r($paths);
//        App::debug(Arr::getPath($a, $paths[0])); //debug
//        App::debug(Arr::getPath($a, $paths[1])); //debug
//        App::debug(Arr::getPath($a, $paths[2])); //debug
        $paths = Arr::search($a, 2, array('first' => true));
        App::debug($paths, 'first'); //debug
        print_r($paths);
        print_r(($paths === false));
        $paths = Arr::search($a, array('u', 2));
        App::debug($paths); //debug
        $paths = Arr::search($a, '/^abc/i', array('comparison' => 'regex'));
        App::debug($paths); //debug
        $paths = Arr::search($a, '/^aXc/i', array('comparison' => 'regex'));
        App::debug($paths); //debug
        $paths = Arr::search($a, '/^abc/i', array('comparison' => 'regex', 'first' => true));
        App::debug($paths); //debug
        $paths = Arr::search($a, '/^aXc/i', array('comparison' => 'regex', 'first' => true));
        App::debug($paths); //debug
        break;

    case 'explode':
        App::debug(explode('.', 'asd')); //debug
        break;

    case 'Run_App_PlainObject_unsetPath':
        App::setJsFiles(array(
            '/app/js/libs/PlainObject.js'
        ));
        App::startJsCapture();
        ?><script type="text/javascript">
            var a = {x: {y: 5, q: 3}, z: 6};
            console.log(Run.App.PlainObject.unsetPath(a, 'x.y')); //debug
            console.log(a); //debug
            console.log(Run.App.PlainObject.unsetPath(a, 'a.b')); //debug
            console.log(a); //debug
            console.log(Run.App.PlainObject.unsetPath(a, 'x.y.q')); //debug
            console.log(a); //debug
            console.log(Run.App.PlainObject.unsetPath(a, 'x')); //debug
            console.log(a); //debug
        </script><?php
        App::endJsCapture();
        break;

    case 'Run_App_PlainObject_setPath':
        App::setJsFiles(array(
            '/app/js/libs/PlainObject.js'
        ));
        App::startJsCapture();
        ?><script type="text/javascript">
            var a = {x: {y: 5}, z: 6};
            console.log(Run.App.PlainObject.setPath(a, 'x.y', 3)); //debug
            console.log(a); //debug
            console.log(Run.App.PlainObject.setPath(a, 'a.b', 1)); //debug
            console.log(a); //debug
            console.log(Run.App.PlainObject.setPath(a, 'x.y.q', 2)); //debug
            console.log(a); //debug
            console.log(Run.App.PlainObject.setPath(a, 'x.q', 4)); //debug
            console.log(a); //debug
            console.log(Run.App.PlainObject.setPath(a, 'x.y.q', 22)); //debug
            console.log(a); //debug
        </script><?php
        App::endJsCapture();
        break;

    case 'Run_App_PlainObject_getPath':
        App::setJsFiles(array(
            '/app/js/libs/PlainObject.js'
        ));
        App::startJsCapture();
        ?><script type="text/javascript">
            var a = {x: {y: 5}, z: 6};
            console.log(Run.App.PlainObject.getPath(a, 'x.y')); //debug
            console.log(Run.App.PlainObject.getPath(a, 'z')); //debug
            console.log(Run.App.PlainObject.getPath(a, 'x')); //debug
            console.log(Run.App.PlainObject.getPath(a, 'a.b.c')); //debug
            console.log(Run.App.PlainObject.getPath(a, 'x.y.q')); //debug
            console.log(Run.App.PlainObject.getPath(a, 'x.q')); //debug
            console.log(a); //debug
        </script><?php
        App::endJsCapture();
        break;

    case 'Arr_unsetPath':
        $a = array('x' => array('y' => 5), 'z' => 6);
        $v = Arr::unsetPath($a, 'k.l');
        App::debug($v); //debug
        App::debug($a); //debug
        break;

    case 'Arr_setPath':
        $a = array('x' => array('y' => 5), 'z' => 6);
        Arr::setPath($a, 'a.b', 1);
        App::debug($a); //debug
        Arr::setPath($a, 'l.m', 0);
        App::debug($a); //debug
        break;

    case 'Arr_getPath':
        $a = array('x' => array('y' => 5), 'z' => 6);
        App::debug(Arr::getPath($a, 'x.y')); //debug
        App::debug(Arr::getPath($a, 'z')); //debug
        App::debug(Arr::getPath($a, 'a.b.c')); //debug
        App::debug($a); //debug
        break;

    case 'App_parseUrl':
        App::debug(App::parseUrl('/my-slug/1/dsf/name:test')); //debug
        App::debug(App::parseUrl('/my-slug/1/dsf/name:test?getpar=2')); //debug
        App::debug(App::parseUrl('/my-slug/1/dsf/name:test?getpar=2', array('hasGetParams' => true))); //debug
        App::debug(App::parseUrl('/my-slug/1/dsf/name:test?getpar=2', array('hasGetParams' => true, 'parseGetParams' => true))); //debug
        App::debug(App::parseUrl('/en/my-slug/1/dsf/name:test?getpar=2', array('hasGetParams' => true, 'parseGetParams' => true))); //debug
        App::debug(App::parseUrl('/en/mvc/MyModule/MyCnt/action/1/dsf/name:test?getpar=2', array('hasGetParams' => true, 'parseGetParams' => true))); //debug
        App::debug(App::parseUrl('/en/mvc/MyModule/MyCnt/admin_action/1/dsf/name:test?getpar=2', array('hasGetParams' => true, 'parseGetParams' => true))); //debug
        App::debug(App::parseUrl('http://run.sk/en/mvc/MyModule/MyCnt/admin_action/1/dsf/name:test?getpar=2', array('hasGetParams' => true, 'parseGetParams' => true))); //debug
        App::debug(App::parseUrl('http://******2.localhost/mvc/App/Languages/testSmartFrame1?page=1&filter=user', array('hasGetParams' => true, 'parseGetParams' => true))); //debug
        $url = '/mySlug/?filter[name]=*a+>b&filter[id]==5&sort[manufacturer.name]=DESC&a[]=45&a[]=25&a=5&a[6]=8&a[x]=x&a[5]=54&a[1]=36&a[]=la st';
        $r = App::parseUrl($url, array('hasGetParams' => true, 'parseGetParams' => true));
        echo '<pre>' . print_r($r, true) . '</pre>';
        App::debug($r, $url); //debug
        App::debug(App::getUrl($r)); //debug
        App::debug(http_build_query($r['get']));
        App::debug(http_build_query($r['get'], null, null));
        break;

    case 'Eshop_formatPrice':
        App::debug(Eshop::formatPrice('10'), '10'); //debug
        App::debug(Eshop::formatPrice('10.00'), '10.00'); //debug
        App::debug(Eshop::formatPrice('10.25'), '10.25'); //debug
        App::debug(Eshop::formatPrice(10), '10'); //debug
        App::debug(Eshop::formatPrice(10.00), '10.00'); //debug
        App::debug(Eshop::formatPrice(10.25), '10.25'); //debug
        break;

    case 'Validate_intNumber':
        App::debug(Validate::intNumber('1'), '1'); //debug
        App::debug(Validate::intNumber('1.00'), '1.00'); //debug
        App::debug(Validate::intNumber('10', true), '10'); //debug
        App::debug(Validate::intNumber('1.00', true), '1.00'); //debug
        App::debug(Validate::intNumber('1.10', true), '1.10'); //debug
        break;

    case 'test':
        set_time_limit(6000);
        App::debug(ini_get('max_execution_time')); //debug
        break;

    case 'pathinfo':
        $r = File::getPathinfo('myfile');
        App::debug($r); //debug
        $r = File::getPathinfo('myfile.txt');
        App::debug($r); //debug
        $r = File::getPathinfo('/myfile');
        App::debug($r); //debug
        $r = File::getPathinfo('/myfile.txt');
        App::debug($r); //debug
        $r = File::getPathinfo('path/to/myfile');
        App::debug($r); //debug
        $r = File::getPathinfo('path/to/myfile.txt');
        App::debug($r); //debug
        $r = File::getPathinfo('/path/to/myfile');
        App::debug($r); //debug
        $r = File::getPathinfo('/path/to/myfile.txt');
        App::debug($r); //debug
        break;

    case 'performance_func_num_args':
        function test150061 () {
            $x = 1;
        }
        function test150062 () {
            $c = func_num_args();
            $x = 1;
        }
        // test150061 ();
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            test150061();
        }
        Utility::getTimer(); // 1.36ms

        // test150062 ();
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            test150062();
        }
        Utility::getTimer(); // 2.35ms
        break;

    case 'test':
        $x = 1;
        $validLogin = (bool)preg_match('/^(?:[a-z0-9\_\-\.]+(?:\:[a-z0-9' . preg_quote('!"$&\'()*+,-._;=~') . ']+)?\@)$/i', 'vydavatel.sk:zjavka@');
        $validHost = (bool)preg_match('/^(?:[a-z0-9][-a-z0-9]*\.)*(?:[a-z0-9][-a-z0-9]{0,62})\.(?:(?:[a-z]{2}\.)?[a-z]{2,4}|museum|travel)$/i', 'b2b.inform-za.sk');
        $validCharsRegex = '([' . preg_quote('!"$&\'()*+,-.@_:;=~') . '\/0-9a-z]|(%[0-9a-f]{2}))';
        $validPath = (bool)preg_match(
            '/^(?:\/?|\/' . $validCharsRegex . '*)?' .
			'(?:\?' . $validCharsRegex . '*)?' .
			'(?:#' . $validCharsRegex . '*)?$/i',
            '/inform_katalog.zip'
        );
        $x = Validate::url('ftp://vydavatel.sk:<EMAIL>/inform_katalog.zip', array('scheme' => 'ftp'));
//        $catalogueZipFile = File::transfer(
//            array('ftp://vydavatel.sk:<EMAIL>/inform_katalog.zip'),
//            '/tmp/',
//            array(
//                'unique' => false,
//                'name' => 'inform_katalog.zip',
//            )
//        );
        break;

    case 'attention':
        // ATTENTION: any int number string key is converted to integer in foreach ($a as $k => $v)!
        $a = array(
            'xd' => 'v',
            '5' => 'v',
            '0' => 'v',
            '2.3' => 'v',
            'a' => 'v',
        );
        print_r($a);
        echo '<br>';
        foreach ($a as $k => $v) {
            echo $k . ': ' . gettype($k) . '<br>';
        }

        // ATTENTION: ('somestr' == 0) === (0 == 'somestr') === TRUE!
        // Because (see http://au.php.net/manual/en/language.operators.comparison.php):
        // "If you compare a number with a string or the comparison involves numerical strings,
        // then each string is converted to a number and the comparison performed numerically."
        echo "'somestr' == '0': " . ('somestr' == '0' ? 'TRUE' : 'FALSE') . '<br>'; //debug FALSE
        echo "'somestr' == 0: " . ('somestr' == 0 ? 'TRUE' : 'FALSE') . '<br>'; //debug TRUE !!!
        echo "0 == 'somestr': " . (0 == 'somestr' ? 'TRUE' : 'FALSE') . '<br>'; //debug TRUE !!!
        echo "'somestr' + 2: " . ('somestr' + 2) . '<br>'; //debug 2
        echo "2 + 'somestr': " . (2 + 'somestr') . '<br>'; //debug 2
        echo "'2' + 0: " . ('2' + 0) . '<br>'; //debug 2
        echo "0 + '2': " . (0 + '2') . '<br>'; //debug 2

        ?><script type="text/javascript">
            console.log(('somestr' == '0' ? 'TRUE' : 'FALSE'), "'somestr' == '0'"); //debug FALSE
            console.log(('somestr' == 0 ? 'TRUE' : 'FALSE'), "'somestr' == 0"); //debug FALSE
            console.log((0 == 'somestr' ? 'TRUE' : 'FALSE'), "0 == 'somestr'"); //debug FALSE
            console.log(('0' == 0 ? 'TRUE' : 'FALSE'), "'0' == 0"); //debug TRUE
            console.log((0 == '0' ? 'TRUE' : 'FALSE'), "0 == '0'"); //debug TRUE
            console.log('somestr' + 2, "'somestr' + 2"); //debug 'somestr2' !!!
            console.log(2 + 'somestr', "2 + 'somestr'"); //debug '2somestr' !!!
            console.log('2' + 0, "'2' + 0"); //debug '20' !!!
            console.log(0 + '2', "0 + '2'"); //debug '02' !!!
        </script><?php
        break;

    case 'String_obfuscate':
        echo Str::obfuscate('<EMAIL>');
        break;


    case 'String_obfuscateHtml2':
$string = <<<STRING
<div class="contact" style="line-height: 21.98px;" data="<EMAIL>">
<script type="text/javascript">
    var x = '<EMAIL>';
</script>
<table>
	<tbody>
		<tr>
			<td class="top-title"><span style="line-height: 21.98px;">Generálny riaditeľ</span></td>
		</tr>
		<tr>
			<td class="image"><img alt="" src="/userfiles/images/Schwab%20Patrick%2C%20Ing.JPG" style="width: 100px; line-height: 21.98px;" /></td>
		</tr>
		<tr>
			<td class="bottom-title"><span style="line-height: 21.98px;">Ing. Patrick Schwab</span></td>
		</tr>
		<tr>
			<td><span style="line-height: 21.98px;"><b>e-mail:</b>&nbsp;</span><a href="mailto:<EMAIL>" style="line-height: 21.98px;"><EMAIL></a></td>
		</tr>
	</tbody>
</table>
</div>
<style type="text/css">
    x {height: 421987654321px}
</style>
<div class="contact">
<table>
    <tbody>
        <tr>
            <td class="top-title">&nbsp;</td>
        </tr>
        <tr>
            <td class="image">&nbsp;</td>
        </tr>
        <tr>
            <td class="bottom-title">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
        </tr>
    </tbody>
</table>
<style type="text/css">
    x {height: 421987654321px}
</style>
<script type="text/javascript">
    var x = '<EMAIL>';
</script>
</div>

<div class="contact" style="line-height: 21.98px;">
<table>
    <tbody>
        <tr>
            <td class="top-title"><span style="line-height: 21.98px;">Finančný riaditeľ</span></td>
        </tr>
        <tr>
            <td class="image"><img alt="" src="/userfiles/images/Budisk%C3%BD%20Peter%2C%20Ing.JPG" style="width: 100px; line-height: 21.98px;" /></td>
        </tr>
        <tr>
            <td class="bottom-title"><span style="line-height: 21.98px;">Ing. Peter Budiský</span></td>
        </tr>
        <tr>
            <td><span style="line-height: 21.98px;"><b>e-mail:</b>&nbsp;</span><a href="mailto:<EMAIL>"><EMAIL></a></td>
        </tr>
        <tr>
            <td><span style="line-height: 21.98px;"><b>tel:</b>+421 43 4201 400</span></td>
        </tr>
    </tbody>
</table>
</div>
<script type="text/javascript">
    var x = '<EMAIL>';
</script>
<style type="text/css">
    x {height: 421987654321px}
</style>
<div class="contact" style="line-height: 21.98px;">
<table>
    <tbody>
        <tr>
            <td class="top-title"><span style="line-height: 21.98px;">Obchodná riaditeľka</span></td>
        </tr>
        <tr>
            <td class="image"><img alt="" src="/userfiles/images/Magva%C5%A1iov%C3%A1%20Helena%2C%20Ing.JPG" style="width: 100px; line-height: 21.98px;" /></td>
        </tr>
        <tr>
            <td class="bottom-title"><span style="line-height: 21.98px;">Ing. Helena Magvašiová</span></td>
        </tr>
        <tr>
            <td><span style="line-height: 21.98px;"><b>e-mail:</b>&nbsp;</span><a href="mailto:<EMAIL>" style="line-height: 21.98px;"><EMAIL></a></td>
        </tr>
        <tr>
            <td><span style="line-height: 21.98px;"><b>tel:</b>&nbsp;+421 43 4201 240</span></td>
        </tr>
    </tbody>
</table>
</div>

<div class="contact">
<table>
    <tbody>
        <tr>
            <td class="top-title">Výrobno-technický&nbsp;<span style="line-height: 21.9799995422363px; text-align: center;">riaditeľ</span></td>
        </tr>
        <tr>
            <td class="image"><img alt="" src="/userfiles/images/Luk%C3%A1%C4%8D%20Milo%C5%A1%20Bc.jpg" style="width: 100px; line-height: 21.98px;" /></td>
        </tr>
        <tr>
            <td class="bottom-title">Bc. Miloš&nbsp;<span style="line-height: 21.9900016784668px; text-align: center;">Lukáč&nbsp;</span></td>
        </tr>
        <tr>
            <td><span style="text-align: center; line-height: 21.98px;"><b>e-mail:</b>&nbsp;</span><a href="mailto:<EMAIL>" style="line-height: 21.98px;"><EMAIL></a></td>
        </tr>
        <tr>
            <td><span style="line-height: 21.98px;"><b>tel:</b>&nbsp;+421 43 4201 300</span></td>
        </tr>
    </tbody>
</table>
</div>
<script type="text/javascript">
    var x = '<EMAIL>';
</script>
<h2 class="section-title"><a name="text-sekretariat"></a>Sekretariát</h2>

<div class="contact" style="line-height: 21.98px; font-size: 15.45px;">
<table>
    <tbody>
        <tr>
            <td class="top-title"><span style="line-height: 21.98px;">Sekretariát Generálneho&nbsp;<br />
            a Finančného riaditeľa</span></td>
        </tr>
        <tr>
            <td class="image"><img alt="" src="/userfiles/images/Lenka%20Sadilov%C3%A1.JPG" style="width: 100px; line-height: 21.98px;" /></td>
        </tr>
        <tr>
            <td class="bottom-title">Lenka Sadilová</td>
        </tr>
        <tr>
            <td><span style="line-height: 21.98px;">e-mail:&nbsp;</span><a href="mailto:<EMAIL>" style="line-height: 21.98px;"><EMAIL></a></td>
        </tr>
        <tr>
            <td><span style="line-height: 21.98px;">tel:+421 43 4201 201</span></td>
        </tr>
    </tbody>
</table>
</div>

<div class="contact" style="line-height: 21.98px; font-size: 15.45px;">
<table>
    <tbody>
        <tr>
            <td class="top-title">&nbsp;</td>
        </tr>
        <tr>
            <td class="image">&nbsp;</td>
        </tr>
        <tr>
            <td class="bottom-title">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
        </tr>
    </tbody>
</table>
</div>

<div class="contact" style="line-height: 21.98px; font-size: 15.45px;">
<table>
    <tbody>
        <tr>
            <td class="top-title"><span style="line-height: 21.98px;">Sekretariát Obchodnej riaditeľky</span><br />
            <span style="line-height: 21.9799995422363px; text-align: center;">a Výrobno-technického riaditeľa</span></td>
        </tr>
        <tr>
            <td class="image"><img alt="" src="/userfiles/images/Bo%C5%BEena%20%C4%8Ci%C4%8Dmancov%C3%A1.JPG" style="width: 100px; line-height: 21.98px;" /></td>
        </tr>
        <tr>
            <td class="bottom-title"><span style="line-height: 21.98px;">Božena Čičmancová</span></td>
        </tr>
        <tr>
            <td><span style="line-height: 21.98px;">e-mail:&nbsp;</span><a href="mailto:<EMAIL>" style="line-height: 21.98px;"><EMAIL></a></td>
        </tr>
        <tr>
            <td><span style="line-height: 21.98px;">tel:&nbsp;+421 43 4201 241</span></td>
        </tr>
    </tbody>
</table>
</div>            
STRING;

//        echo $string;
//        break;
        $string = Str::obfuscateHtml($string, array(
            'subject' => array(
                'emailAddresses',
                'phoneNumbers'
    //            'riaditeľ',
            )
        ));
        echo $string;
        break;


    case 'String_obfuscateHtml1':
$string = <<<STRING
Generálny riaditeľ

Ing. Patrick Schwab
e-mail: <EMAIL> 
 
Finančný riaditeľ

Ing. Peter Budiský
e-mail: <EMAIL>
tel:+421 43 4201 400 
Obchodná riaditeľka

Ing. Helena Magvašiová
e-mail: <EMAIL>
tel: +421 43 4201 240 
Výrobno-technický riaditeľ

Bc. Miloš Lukáč 
e-mail: <EMAIL>
tel: +421 43 4201 300
SEKRETARIÁT

Sekretariát Generálneho 
a Finančného riaditeľa

Lenka Sadilová
e-mail: <EMAIL>
tel:+421 43 4201 201 
 
Sekretariát Obchodnej riaditeľky
a Výrobno-technického riaditeľa

Božena Čičmancová
e-mail: <EMAIL>
tel: +421 43 4201 241
OBCHODNÉ TÍMY - PODĽA JAZYKA

Nemecký
Slovenský a český
Poľský
Anglický
Maďarský
Ruský
Francúzsky
Nemecký

Knihy

Ing. Jozef Malý 
e-mail: <EMAIL>
tel: +421 43 4201 274
mobil: +421 907 830 509 
Knihy

Ing. Ján Muríň 
e-mail: <EMAIL>
tel: +421 43 4201 491
mobil: +421 908 997 262 
Knihy

Bc. Miroslava Hrivnáková 
e-mail: <EMAIL>
tel: +421 43 4201 499
mobil: +421 907 741 840
STRING;
        $string = Str::obfuscateHtml($string);
        echo $string;
        break;

    case 'String_obfuscate':
        App::debug(preg_quote('(?:\S+@[^\.]+\.\S+)', '/')); //debug
        $s = 'us23dš56gffť12';
        echo(mb_substr($s, 1) . '<br>');  //debug OK
        echo(mb_substr($s, 1, 1000000000,'UTF8') . '<br>');  //debug OK
        echo PHP_INT_MAX . '<br>';
        echo(substr($s, 1) . '<br>');            //debug OK
        echo(mb_substr($s, 5, 100,'UTF8') . '<br>');  //debug OK
        echo(substr($s, 5) . '<br>');            //debug NOK
        echo(mb_substr($s, 6, 100,'UTF8') . '<br>');  //debug OK
        echo(substr($s, 6) . '<br>');            //debug NOK

        // get obfustated version of $subjectString
        $encoding = 'UTF8';
        ?>
<script type="text/javascript">
function dot(b) {
    var s='',e='';while(b.length){s+=b.shift();if (b.length) {e=b.shift()+e;}}
    console.log(s+e);
}
function dot(b) {
    var s='',e='';while(b.length){s+=b.shift();if (b.length) {e=b.shift()+e;}}s+=e;
//    s=document.createTextNode(s);
//    a=document.getElementById(a);
//    a.parentNode.insertBefore(s, a);
//    a.parentNode.removeChild(a);
}
</script>
        <?php
        $subjectString = 'us23dš56gffť12';
        $lastIndex = mb_strlen($subjectString, $encoding) - 1;
        $obfuscatedObject = array();
        for ($i = 0; $i <= $lastIndex; $i++) {
            $headIndex = $i;
            $tailIndex = $lastIndex - $i;
            if ($headIndex > $tailIndex) {
                break;
            }
            $obfuscatedObject[] = mb_substr($subjectString, $headIndex, 1, $encoding);
            if ($headIndex === $tailIndex) {
                break;
            }
            $obfuscatedObject[] = mb_substr($subjectString, $tailIndex, 1, $encoding);
        }
        echo $subjectString . ' - ' . mb_strlen($subjectString, $encoding) . '<br>';
        echo print_r(json_encode($obfuscatedObject), true) . '<br>';
        echo '<script type="text/javascript">dot(' . json_encode($obfuscatedObject) . ');</script>';

        $subjectString = 'us23dš56gffť1';
        $lastIndex = mb_strlen($subjectString, $encoding) - 1;
        $obfuscatedObject = array();
        for ($i = 0; $i <= $lastIndex; $i++) {
            $headIndex = $i;
            $tailIndex = $lastIndex - $i;
            if ($headIndex > $tailIndex) {
                break;
            }
            $obfuscatedObject[] = mb_substr($subjectString, $headIndex, 1, $encoding);
            if ($headIndex === $tailIndex) {
                break;
            }
            $obfuscatedObject[] = mb_substr($subjectString, $tailIndex, 1, $encoding);
        }
        echo $subjectString . ' - ' . mb_strlen($subjectString, $encoding) . '<br>';
        echo print_r($obfuscatedObject, true) . '<br>';
        echo '<script type="text/javascript">dot(' . json_encode($obfuscatedObject) . ');</script>';

        $subjectString = 'usd';
        $lastIndex = mb_strlen($subjectString, $encoding) - 1;
        $obfuscatedObject = array();
        for ($i = 0; $i <= $lastIndex; $i++) {
            $headIndex = $i;
            $tailIndex = $lastIndex - $i;
            if ($headIndex > $tailIndex) {
                break;
            }
            $obfuscatedObject[] = mb_substr($subjectString, $headIndex, 1, $encoding);
            if ($headIndex === $tailIndex) {
                break;
            }
            $obfuscatedObject[] = mb_substr($subjectString, $tailIndex, 1, $encoding);
        }
        echo $subjectString . ' - ' . mb_strlen($subjectString, $encoding) . '<br>';
        echo print_r($obfuscatedObject, true) . '<br>';?><?php
        echo '<script type="text/javascript">dot(' . json_encode($obfuscatedObject) . ');</script>';

        break;

    case 'String_getUtf8Code_getUtf8Char':
        $s = 'asdfgh';
        App::debug($s[1]); //debug
        App::debug($s{1}); //debug
        $char = 'ľ';
        $code = Str::getUtf8Code($char);
        App::debug($code, $char . ': ' . $code . ' -> ' . Str::getUtf8Char($code)); //debug
        $char = 'Ľ';
        $code = Str::getUtf8Code($char);
        App::debug($code, $char . ': ' . $code . ' -> ' . Str::getUtf8Char($code)); //debug
        $char = 'š';
        $code = Str::getUtf8Code($char);
        App::debug($code, $char . ': ' . $code . ' -> ' . Str::getUtf8Char($code)); //debug
        $char = 'Š';
        $code = Str::getUtf8Code($char);
        App::debug($code, $char . ': ' . $code . ' -> ' . Str::getUtf8Char($code)); //debug
        $char = 'č';
        $code = Str::getUtf8Code($char);
        App::debug($code, $char . ': ' . $code . ' -> ' . Str::getUtf8Char($code)); //debug
        $char = 'Č';
        $code = Str::getUtf8Code($char);
        App::debug($code, $char . ': ' . $code . ' -> ' . Str::getUtf8Char($code)); //debug
        $char = 'ä';
        $code = Str::getUtf8Code($char);
        App::debug($code, $char . ': ' . $code . ' -> ' . Str::getUtf8Char($code)); //debug
        $char = 'ô';
        $code = Str::getUtf8Code($char);
        App::debug($code, $char . ': ' . $code . ' -> ' . Str::getUtf8Char($code)); //debug
        $char = 'u';
        $code = Str::getUtf8Code($char);
        App::debug($code, $char . ': ' . $code . ' -> ' . Str::getUtf8Char($code)); //debug
        break;

    case 'recoverTree':
        App::loadModel('App', 'WebContent');
        $Content = new WebContent();
        $Content->recoverTree();
        break;

    case 'test':
        App::debug(App::hasModel('Eshop', 'EshopProduct'), 'EshopProduct'); //debug
        App::debug(App::hasModel('Eshop', 'EshopProductXXX'), 'EshopProductXXX'); //debug
        App::debug(App::hasModule('Eshop'), 'Eshop'); //debug
        App::debug(App::hasModule('EshopXXX'), 'EshopXXX'); //debug
        break;

    case 'test':
        $r = Arr::convertNumericToAlphabeticIndex(0);
        App::debug($r, ' 0'); //debug
        $r = Arr::convertNumericToAlphabeticIndex(1);
        App::debug($r, ' 1'); //debug
        $r = Arr::convertNumericToAlphabeticIndex(27);
        App::debug($r, ' 27'); //debug
        $r = Arr::convertNumericToAlphabeticIndex(14557);
        App::debug($r, ' 14557'); //debug
        $r = Arr::convertNumericToAlphabeticIndex(1, false);
        App::debug($r, ' 1'); //debug
        $r = Arr::convertNumericToAlphabeticIndex(2, false);
        App::debug($r, ' 2'); //debug
        $r = Arr::convertNumericToAlphabeticIndex(28, false);
        App::debug($r, ' 28'); //debug
        $r = Arr::convertNumericToAlphabeticIndex(14558, false);
        App::debug($r, ' 14558'); //debug
        break;

    case 'test':
        $this->loadView('Tools/menu', null, null, 'myVar', $catched);
        App::debug($catched); //debug
        break;

    case 'test':
        App::debug(File::getPathInfo('/'), '/'); //debug
        App::debug(File::getPathInfo('file.php'), 'file.php'); //debug
        App::debug(File::getPathInfo('file'), 'file'); //debug
        App::debug(File::getPathInfo('/my/path/file'), '/my/path/file'); //debug
        App::debug(File::getPathInfo('my/path/file'), 'my/path/file'); //debug
        App::debug(File::getPathInfo('/file.php'), '/file.php'); //debug
        App::debug(File::getPathInfo('/file'), '/file'); //debug
        App::debug(File::getPathInfo('/path/'), '/path/'); //debug
        App::debug(File::getPathInfo('/my/path/'), '/my/path/'); //debug
        break;

    case 'Model_findSiblingsInTree':
        $Content = App::loadModel('App', 'WebContent', true);
        $r = $Content->findSiblingsInTree(
            225, // 234
            array(
                'fields' => array('name', 'id', 'parent_id'),
                'filter' => 'previous',
//                'includeNode' => true,
                'first' => true,
            ),
            $record
        );
        App::debug($record); //debug
        App::debug($r); //debug
        echo '<pre>' . print_r($r, true) . '</pre>';
        break;

    case 'Model_findTreeSelectList':
        $Content = App::loadModel('App', 'WebContent', true);
        $r = $Content->findTreeSelectList(
            array(
                'conditions' => array(
                    'parent_id' => null,
                    'lang' => 'sk',
                ),
            ),
            array(
                'firstPlaceholder' => true,
                'firstPlaceholderStart' => 2,
//                'prependRoot' => true,
//                'depth' => 2,
//                'indent' => '-',
//                'accumulate' => true,
//                'template' => '->> :l:',
            )
        );
        //App::debug($r); //debug
        echo '<pre>' . print_r($r, true) . '</pre>';
        App::loadLib('App', 'FormHelper');
        $Form = new FormHelper();
        echo $Form->select('test', array('options' => $r));
        $r = $Content->findTreeSelectList(
            34,
            array(
//                'indent' => '-',
//                'accumulate' => true,
//                'template' => '->> :l:',
            )
        );
        //App::debug($r); //debug
        echo '<pre>' . print_r($r, true) . '</pre>';
        break;

    case 'App_loadScript':
        App::loadScript('/app/config/config.php', array('catchVariables' => 'config'), $vars);
        echo '<pre>' . nl2br(print_r($vars, true)) . '</pre>'; //debug
        break;

    case 'compact':
        $a = 1;
        $b = 2;
        $c = null;
        $r = compact(array('a', 'b', 'c', 'd'));
        echo '<pre>' . nl2br(print_r($r, true)) . '</pre>'; //debug
        break;

    case 'Arr_getLiteral':
        $a = array(
            'name' => 'a',
            'lastname' => 'b',
            'data' => array(
                'x' => 'c',
                'm' => 'n',
            ),
        );
        App::debug(Arr::getLiteral($a)); //debug
        App::debug(Arr::getLiteral($a, 'js')); //debug
        break;

    case 'performance_Validate_externalUrl':
        App::debug(Validate::externalUrl('http://sme.sk')); //debug TRUE
        App::debug(Validate::externalUrl('http://gader.localhost')); //debug FALSE
        App::debug(Validate::externalUrl('http://gader.localhost/')); //debug FALSE
        App::debug(Validate::externalUrl('http://gader.localhost/slug')); //debug FALSE
        App::debug(Validate::externalUrl('https://gader.localhost/slug')); //debug FALSE
        // Validate::externalUrl('http://sme.sk');
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            Validate::externalUrl('http://sme.sk');
        }
        Utility::getTimer(); // ~5ms
        break;


    case 'String_slugize':
        $r = Str::slugize('/ščť/RČf_h-j');
        App::debug($r); //debug
        $r = Str::slugize('a  - _ strín%+*G/-to_be:SLUGGe@#d 01');
        App::debug($r); //debug
        $r = Str::slugize('a/ešte/opäť-úžasne_vymakaný:meruôsmy slugičôček');
        App::debug($r); //debug
        $r = Str::slugize('a/ešte/opäť-úžasne_vymakaný:meruôsmy slugičôček', array('separator' => ''));
        App::debug($r); //debug
        $r = Str::slugize('a  - _ strín%+*G/-to_be:SLUGGe@#d 01', array('preserve' => array('/', ':')));
        App::debug($r); //debug
        $r = Str::slugize('a  - _ strín%+*G/-to_be:SLUGGe@#d 01', array('separator' => '_', 'preserve' => array('/', ':')));
        App::debug($r); //debug
        $r = Str::slugize('a  - _ strín%+*G/-to_be:SLUGGe@#d 01', array('separator' => '_', 'preserve' => array('/', ':', '+')));
        App::debug($r); //debug
        $r = Str::slugize('a/ešte/opäť-úžasne_vymakaný:meruôsmy slugičôček', array('preserve' => array('/', ':')));
        App::debug($r); //debug
        break;

    case 'App_redirect_target':
        echo '<pre>' . nl2br(print_r($_POST, true)) . '</pre>'; //debug
        break;

    case 'App_redirect':
        $data = array(
            'firstname' => 'Mojo "Celliny"',
            'lastname' => 'Ďurík',
            'content' => '<b style="color:red">Obsah</b> post formuláru',
        );
        App::loadLib('App', 'FormHelper');
        $Form = new FormHelper(array(
            'data' => $data,
        ));
        $inputs = '';
        foreach ($data as $name => $value) {
            $inputs .= $Form->hidden($name);
        }
        App::redirect('/_debug/App_redirect_target', array(
            'js' => true,
            'data' => $data,
//            'data' => $inputs,
        ));
        break;

    case 'Model_getUniqueFieldValue':
        $Request = App::loadModel('Payment', 'PaymentRequestLog',  true);
//        App::debug(DB::hasField($Request->getPropertyTable(), 'token')); //debug
//        App::debug(DB::hasField($Request->getPropertyTable(), 'token', array(
//            'index' => array('unique', 'primary')
//        ))); //debug
//        App::debug(DB::hasField($Request->getPropertyTable(), 'token', array(
//            'index' => array('unique')
//        ))); //debug
//        App::debug(DB::hasField($Request->getPropertyTable(), 'token', array(
//            'index' => 'unique'
//        ))); //debug
//        App::debug(DB::hasField($Request->getPropertyTable(), 'token', array(
//            'index' => array('primary')
//        ))); //debug
//        App::debug(DB::hasField($Request->getPropertyTable(), 'token', array(
//            'index' => 'primary'
//        ))); //debug
//        App::debug(DB::hasField($Request->getPropertyTable(), 'token', array(
//            'index' => false
//        ))); //debug
//
//        App::debug($Request->getUniqueFieldValue('token', 'test')); //debug
//        App::debug($Request->getUniqueFieldValue('token', 'test', array('reserve' => true))); //debug
//        App::debug($Request->getUniqueFieldValue('token', 'test', array('reserve' => 'byUniqueIndex'))); //debug
        App::debug($Request->getUniqueFieldValue('token', 20, array('reserve' => true))); //debug
//        App::debug($Request->getUniqueFieldValue('token', '20', array('reserve' => true))); //debug
//        App::debug($Request->getUniqueFieldValue('token', array('length' => 40), array('reserve' => true))); //debug
        App::debug($Request->getPropertyId()); //debug
        break;

    case 'performance_token':
        App::debug(Str::getRandom(40)); //debug
        // Str::getRandom(40)
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            Str::getRandom(8);
        }
        Utility::getTimer(); // ~29ms
        break;

    case 'translate':
        echo __d(__FILE__, 'Test text1 with apostroph: \'') . '<br>';
        echo __d(__FILE__, "Test text2 with apostroph: '") . '<br>';
        echo __d(__FILE__, 'Test text1 with quote: "') . '<br>';
        echo __d(__FILE__, "Test text2 with quote: \"") . '<br>';
        echo '\\"' . '<br>';
        echo '\"' . '<br>';
        echo "\'" . '<br>';
        echo "\"" . '<br>';
        echo '\'' . '<br>';
        break;

    case 'sortable':
        App::setCssFiles(array(
            '/app/css/vendors/normalize.css',
            '/app/css/vendors/bootstrap.css',
            '/app/css/vendors/bootstrap-theme.css',
            '/app/css/vendors/font-awesome/css/font-awesome.min.css',
        ));
        App::setJsFiles(array(
            '/app/js/vendors/jquery.min.js',
            '/app/js/vendors/jquery-ui/jquery-ui.min.js',
            '/app/js/vendors/bootstrap.min.js',
        ));
        ?>
<div role="tabpanel">
  <!-- Nav tabs -->
  <ul class="nav nav-tabs" role="tablist"><?php
    ?><li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab">Home sd</a></li><?php
    ?><li role="presentation"><a href="#profile" aria-controls="profile" role="tab" data-toggle="tab">Profile sdf sd</a></li><?php
    ?><li role="presentation"><a href="#messages" aria-controls="messages" role="tab" data-toggle="tab">Messages sd</a></li><?php
    ?><li role="presentation"><a href="#settings" aria-controls="settings" role="tab" data-toggle="tab">Settings rer</a></li><?php
?></ul>
  <!-- Tab panes -->
  <div class="tab-content">
    <div role="tabpanel" class="tab-pane active" id="home">Home sd</div>
    <div role="tabpanel" class="tab-pane" id="profile">Profile sdf sd</div>
    <div role="tabpanel" class="tab-pane" id="messages">Messages sd</div>
    <div role="tabpanel" class="tab-pane" id="settings">Settings rer</div>
  </div>
</div>
<?php App::startCssCapture(); ?>
<style type="text/css">
.nav-tabs {
    padding: 3px 23px 0 23px;
}
.nav-tabs > * {
    /*white-space: nowrap;*/
}
.nav-tabs > li {
    /*float: none;*/
    /*display: inline-block;*/
}
</style>
<?php App::endCssCapture(); ?>
<?php App::startJsCapture(); ?>
<script type="text/javascript">
jQuery(function(){

jQuery('.nav-tabs').sortable({
    appendTo: jQuery('.nav-tabs'),
    containment: jQuery('.nav-tabs'), // this makes problem if there is a small tab as the firts
    tolerance: 'pointer',
    axis: 'x',
    delay: 150,
    stop: function(event, ui) {
        console.log('stop'); //debug
        jQuery(ui.item).tab('show');
    }
});

});
</script>
<?php App::endJsCapture();
        break;

    case 'test':
        $file = 'AppErrors_sk_SK_js';
        $format = substr($file, -3); // _js
        $locale = substr($file, -8, -3); // sk_SK
        $domain = substr($file, 0, -9); // AppErrors
        App::debug($format); //debug
        App::debug($locale); //debug
        App::debug($domain); //debug
        break;

    case 'performance_rightsLoading':
        $rightsFile = ROOT . DS . 'app' . DS . 'config' . DS . 'rights.php';
        $rightsoldFile = ROOT . DS . 'app' . DS . 'config' . DS . 'rightsold.php';
        // require $rightsFile;
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            require $rightsFile;
        }
        Utility::getTimer(); // ~450ms

        // require $rightsFile; ($edit = TRUE)
        $edit = true;
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            require $rightsFile;
        }
        Utility::getTimer(); // ~700ms

        // require $rightsoldFile;
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            require $rightsoldFile;
        }
        Utility::getTimer(); // ~250-300ms
        break;

    case 'performance_scandir':
        $dir = ROOT . DS . 'app' . DS . 'controllers';
        App::debug(scandir($dir)); //debug
        $files = scandir($dir);
        foreach ($files as $file) {
            $file = $dir . DS . $file;
            App::debug('', $file . ' is dir: ' . (is_dir($file) ? '1' : '0')); //debug
        }
        // scandir();
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            scandir($dir);
        }
        Utility::getTimer(); // ~24ms

        break;

    case 'performance_poLoading':
        $module = 'App';
        $modulePath = App::getModulePath($module);
        $poFilePath = $modulePath . DS . 'locale' . DS . $module . '_sk_SK.po';
        $phpFilePath = ROOT . $modulePath . DS . 'locale' . DS . 'compiled' . DS . $module . '_sk_SK.php';

        App::debug(filemtime(ROOT . $poFilePath)); //debug
        App::debug(filemtime($phpFilePath)); //debug
        // File::parsePo();
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            File::parsePo($poFilePath);
        }
        Utility::getTimer(); // ~21000ms

        // require
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            require $phpFilePath;
        }
        Utility::getTimer(); // ~700ms

        // require + is_readable + filemtime
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            is_readable($phpFilePath);
            filemtime(ROOT . $poFilePath);
            filemtime($phpFilePath);
            require $phpFilePath;
        }
        Utility::getTimer(); // ~700ms
        break;

    case 'performance_callUserFuncArray':
        $args = array('My string', 1);
        // 'Str::fill'
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            call_user_func_array('Str::fill', $args);
        }
        Utility::getTimer(); // ~14ms

        // array('Str', 'fill')
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            call_user_func_array(array('Str', 'fill'), $args);
        }
        Utility::getTimer(); // ~14ms
        break;

    case 'performance_prependArray':
        $a = array(array('abc' => 2, 'xyz' => 3), 5);
        $firstItem = 'some string';
        // +
        App::debug(array($firstItem) + $a); //debug - NEFUNGUJE SPRÁVNE - asociatívne položky sa strátia pretože sú na tom istom indexe (0) ako $firstItem
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            $x = array($firstItem) + $a;
        }
        Utility::getTimer(); // ~0.87ms

        // array_merge
        App::debug(array_merge(array($firstItem), $a)); //debug
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            $x = array_merge(array($firstItem), $a);
        }
        Utility::getTimer(); // ~2.5ms

        // array_unshift
        $x = $a;
        array_unshift($x, $firstItem);
        App::debug($x); //debug
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            $x = $a;
            array_unshift($x, $firstItem);
        }
        Utility::getTimer(); // ~2.16ms
        break;

    case 'performance_i18n':
        // __d('App')
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            __d(__FILE__, 'Some test translation');
        }
        Utility::getTimer(); // ~5ms

        // __d($this)
        App::debug(__d(__FILE__, 'Some test translation')); //debug
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            __d(__FILE__, 'Some test translation');
        }
        Utility::getTimer(); // ~5ms

        // __d($this)
        App::debug(__d(__FILE__, 'Some test translation No.%s', 1)); //debug
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            __d(__FILE__, 'Some test translation No.%s', 1);
        }
        Utility::getTimer(); // ~25ms (~13ms in old version)

        // __d($this)
        App::debug(__d(__FILE__, 'Some test translation No.:no: and %s', array('no' => 2), 3)); //debug
        Utility::startTimer();
        for ($i = 0; $i < 1000; $i++) {
            __d(__FILE__, 'Some test translation No.:no: and %s', array('no' => 2), 3);
        }
        Utility::getTimer(); // ~50ms
        break;

    case 'niceselect':
        App::setJsFiles('/app/js/vendors/jquery.min.js');
        App::setJsFiles('/app/js/vendors/jquery-ui/jquery-ui.min.js');
        App::setCssFiles('/app/js/vendors/jquery-ui/jquery-ui.css');
        App::setJsFiles('/app/js/vendors/bootstrap.js');
        App::setCssFiles('/app/css/vendors/bootstrap.css');
        ?>
<select id="xxx">
    <option value="sk"><img src="/app/img/libs/lang/sk.png"/>SK<span>an</span></option>
    <option value="en"><img src="/app/img/libs/lang/en.png"/>EN<span>ab</span></option>
</select>
<div class="dropdown">
  <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-expanded="true">
    <img src="/app/img/libs/lang/sk.png"/>
    <span class="caret"></span>
  </button>
  <ul class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu1">
    <li role="presentation"><a role="menuitem" href="#"><img src="/app/img/libs/lang/sk.png"/></a></li>
    <li role="presentation"><a role="menuitem" href="#"><img src="/app/img/libs/lang/en.png"/></a></li>
  </ul>
</div>
<?php App::startJsCapture() ?>
<script type="text/javascript">
$(function() {
//    if (!jQuery.fn.selectmenu) {
//        console.log('no select menu plugin');
//    }
//    else {
//        $.widget( "custom.iconselectmenu", $.ui.selectmenu, {
//          _renderItem: function( ul, item ) {
//            var li = $( "<li>", { text: item.label } );
//            console.log(item.element.html()); //debug
//
//            if ( item.disabled ) {
//              li.addClass( "ui-state-disabled" );
//            }
//
//            $( "<span>", {
//              style: item.element.attr( "data-style" ),
//              "class": "ui-icon " + item.element.attr( "data-class" )
//            })
//              .appendTo( li );
//
//            return li.appendTo( ul );
//          }
//        });
//
//        $( "#xxx" ).iconselectmenu();
//    }
    $('option', "#xxx" ).each(function() {
        var x = jQuery(this).html();
        console.log(x); //debug
    });
  });
</script>
<?php App::endJsCapture() ?>

        <?php
        break;

    case 'Model_getTranslationQuery':
        $query = "SELECT `A`.`a`, `A`.`b`, IF (`A`.`a` IS NOT NULL AND (`A`.`a` IS NULL OR DATEDIFF(NOW(),`A`.`a`) >= 0) AND (`A`.`a` IS NULL OR DATEDIFF(NOW(), `A`.`a`) <= 0), `A`.`a`, `A`.`b`), `A`.`a`, `A`.`b`, IF (A.offer_type = 2 OR A.a = 1 OR A.a = 0 AND DATE(A.a) > CURDATE() - INTERVAL 90 DAY, 1, 0), `A`.`a`";
        echo '<xmp>' . print_r($query, true) . '</xmp>'; //debug

        $enc = DB::getPropertyNameEnclosure();
        $field = 'a';
        $searchQualifier = $enc . 'A' . $enc;
        $replaceQualifier  = $enc . 'run_a_i18n' . $enc;
        $translationField = Model::getTranslationField($field, 'zh');
        $replacements = array();
        $replacements['select']['search'][] =
            $searchQualifier . '.' . $enc . $field . $enc . ' AS ';
        $replacements['select']['replace'][] =
            $replaceQualifier . '.' . $enc . $translationField . $enc . ' AS ';
        $replacements['select']['search'][] =
            $searchQualifier . '.' . $enc . $field . $enc;
        $replacements['select']['replace'][] =
            $replaceQualifier . '.' . $enc . $translationField . $enc . ' AS ' . $enc . $field . $enc;
         // create not aliased replacement for general
        $replacements['others']['search'][] =
            $searchQualifier . '.' . $enc . $field . $enc;
        $replacements['others']['replace'][] =
            $replaceQualifier . '.' . $enc . $translationField . $enc;

        $braceParts = explode('(', $query);
        $level = 0;
        foreach ($braceParts as &$part) {
            $level++;
            if (($closedCount = substr_count($part, ')'))) {
                $level -= $closedCount;
            }
            echo '<xmp>' . print_r($part, true) . ' --- ' . $level . '</xmp>'; //debug
            if ($level === 1) {
                if ($closedCount !== 0) {
                    $subparts = explode(')', $part);
                    $lastSubpart = array_pop($subparts);
                    $part = implode(')', $subparts);
                    $part = str_replace(
                        $replacements['others']['search'],
                        $replacements['others']['replace'],
                        $part
                    );
                    $lastSubpart = str_replace(
                        $replacements['select']['search'],
                        $replacements['select']['replace'],
                        $lastSubpart
                    );
                    $part .= ')' . $lastSubpart;
                }
                else {
                    $part = str_replace(
                        $replacements['select']['search'],
                        $replacements['select']['replace'],
                        $part
                    );
                }
            }
            else {
                $part = str_replace(
                    $replacements['others']['search'],
                    $replacements['others']['replace'],
                    $part
                );
            }
        }
        $translatedQuery = implode('(', $braceParts);
        echo '<xmp>' . print_r($translatedQuery, true) . '</xmp>'; //debug
        break;
  case 'hint':
        ?>
        <a class="selector" title="xxx">qtip</a>

        <?php
//        App::setJsFiles('/app/js/vendors/qtip/jquery.qtip.js');
//        App::setCssFiles('/app/js/vendors/qtip/jquery.qtip.min.css');
//        App::setJs('$(function(){$(".selector").qtip();})');

//        App::setJsFiles('/app/js/vendors/jquery-ui/jquery-ui.min.js');
//        App::setCssFiles('/app/js/vendors/jquery-ui/jquery-ui.min.css');
//        App::setJs('$(function(){$(".selector").tooltip();})');

        App::setJsFiles('/app/js/vendors/tooltipster/js/jquery.tooltipster.min.js');
        App::setCssFiles('/app/js/vendors/tooltipster/css/tooltipster.css');
        App::setCssFiles('/app/js/vendors/tooltipster/css/themes/tooltipster-light.css');
        App::setJs('$(function(){$(".selector").tooltipster({theme: "tooltipster-light"});})');
        break;

    case 'Arr_buildTree':
        $a = array(
                array('level' => 1, 'label' => 'Item 0'),
                array('level' => 1, 'label' => 'Item 1'),
                array('level' => 2, 'label' => 'Item 2'),
                array('level' => 2, 'label' => 'Item 3'),
                array('level' => 1, 'label' => 'Item 4'),
                array('level' => 2, 'label' => 'Item 5'),
                array('level' => 3, 'label' => 'Item 6'),
                array('level' => 2, 'label' => 'Item 7'),
        );
        // invalid
        $a = array(
                array('level' => 2, 'label' => 'Item 0'),
                array('level' => 1, 'label' => 'Item 1'),
                array('level' => 3, 'label' => 'Item 2'),
                array('level' => 2, 'label' => 'Item 3'),
                array('level' => 1, 'label' => 'Item 4'),
                array('level' => 2, 'label' => 'Item 5'),
                array('level' => 3, 'label' => 'Item 6'),
                array('level' => 2, 'label' => 'Item 7'),
        );
        $r = Arr::buildTree($a);
        App::debug($r); //debug
        break;

    case 'test':
        $Order = App::loadModel('Eshop', 'EshopOrder', true);
        $orderData = array(
            'number' => '10001',
            'token' => 'asdasdasd',
            'run_users_id' => null,
            'status' => 'enum_new_order',
            'payment_status' => 'enum_payment_none',
//            'advance_rate' => $data['advance_rate'],
//            'order_price_taxless' => $orderPrices['order_price_taxless'],
//            'order_tax' => $orderPrices['order_tax'],
//            'order_price_actual_taxless' => $orderPrices['order_price_actual_taxless'],
//            'order_tax_actual' => $orderPrices['order_tax_actual'],
//            'products_price_taxless' => $cartPrices['products_price_taxless'],
//            'products_tax' => $cartPrices['products_tax'],
//            'products_price_actual_taxless' => $cartPrices['products_price_actual_taxless'],
//            'products_tax_actual' => $cartPrices['products_tax_actual'],
//            'shipment_price_taxless' => Sanitize::value($shipment['shipment_price_taxless'], 0.0),
//            'shipment_tax' => Sanitize::value($shipment['shipment_tax'], 0.0),
//            'shipment_price_actual_taxless' => Sanitize::value($shipment['shipment_price_actual_taxless'], 0.0),
//            'shipment_tax_actual' => Sanitize::value($shipment['shipment_tax_actual'], 0.0),
//            'payment_price_taxless' => Sanitize::value($payment['payment_price_taxless'], 0.0),
//            'payment_tax' => Sanitize::value($payment['payment_tax'], 0.0),
//            'payment_price_actual_taxless' => Sanitize::value($payment['payment_price_actual_taxless'], 0.0),
//            'payment_tax_actual' => Sanitize::value($payment['payment_tax_actual'], 0.0),
//            'run_eshop_shipment_methods_id' => Sanitize::value($shipment['id']),
//            'shipment_method_name' => Sanitize::value($shipment['name']),
//            'run_payment_methods_id' => Sanitize::value($payment['id']),
//            'payment_method_name' => Sanitize::value($payment['name']),
//            'fullname' => $data['fullname'],
//            'street' => $data['street'],
//            'city' => $data['city'],
//            'zip' => $data['zip'],
//            'country' => $data['country'],
//            'phone' => $data['phone'],
//            'email' => $data['email'],
//            'company_fullname' => Sanitize::value($data['company_fullname']),
//            'company_id_number' => Sanitize::value($data['company_id_number']),
//            'company_tax_number' => Sanitize::value($data['company_tax_number']),
//            'company_vat_number' => Sanitize::value($data['company_vat_number']),
//            'delivery_fullname' => Sanitize::value($data['delivery_fullname']),
//            'delivery_street' => Sanitize::value($data['delivery_street']),
//            'delivery_city' => Sanitize::value($data['delivery_city']),
//            'delivery_zip' => Sanitize::value($data['delivery_zip']),
//            'delivery_country' => Sanitize::value($data['delivery_country']),
//            'comment' => $data['comment'],
//            'bride_fullname' => $data['bride_fullname'],
//            'bride_address' => $data['bride_address'],
//            'groom_fullname' => $data['groom_fullname'],
//            'groom_address' => $data['groom_address'],
//            'wedding_ceremony_place_and_time' => $data['wedding_ceremony_place_and_time'],
//            'wedding_announcement_text' => $data['wedding_announcement_text'],
//            'wedding_announcement_files' => json_encode($data['wedding_announcement_files']),
//            'terms_and_conditions_agreement' => Sanitize::value($data['terms_and_conditions_agreement'], 0),
//            'prolonged_delivery_time_agreement' => Sanitize::value($data['prolonged_delivery_time_agreement'], 0),
//            'newsletters_agreement' => Sanitize::value($data['newsletters_agreement'], 0),
//            'reference_from' => Sanitize::value($data['reference_from']),
            //'adulthood_declaration' => $data['adulthood_declaration'],
        );
        try {
            $Order->save($orderData, array('validate' => false));
        }
        catch (Throwable $e) {
            App::debug($e->getMessage()); //debug
            App::debug($e->getCode()); //debug
            App::debug($e->getCode() == 1062); //debug
            App::debug(gettype($e->getCode())); //debug
            if ($e->getCode() != 1062) {
                throw $e;
            }
        }

        break;

    case 'App_messages':
        App::setMessage('Message 01');
        App::setMessage('Message 02');
        App::setMessage('Message 03');
        echo App::messages(array(
            'separate' => true,
            //'class' => 'admin-app-messages',
            //'closeButton' => '<span>x</span>',
            //'timeout' => 2,
        ));
        break;

    case 'Csv_output()':
        App::loadLib('App', 'Csv');
        $Content = $this->loadModel('WebContent', true);

        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;

        $conditions = array();
        if (App::getUser('Group.pid') !== 'admins') {
            $conditions['hidden'] = false;
        }

        $data = $Content->findInTree(
//        $contents = $Content->find(
            array(
                'conditions' => array(
                    'parent_id' => null,
                    'lang' => $lang,
                ),
            ),
            array(
                'fields' => array('*'),
                'conditions' => $conditions,
                'order' => 'sort',
                'paginate' => true,
            )
        );
        Csv::output($data);
        App::setLayout(false);
        break;

    case 'File_output':
//        File::output('/docs/zakon_o_ochrane_spotrebitela.pdf', array(
//            'disposition' => 'inline',
//        ));
        File::output('/misc/docs/zakon_o_ochrane_spotrebitela_resume.txt', array(
            //'disposition' => 'inline',
            'encoding' => 'utf8',
        ));
        break;

    case 'test':
        $Content = App::loadModel('App', 'WebContent', true);
        $Content->updateInTree(
            34,
            array('hidden' => false),
            array(
                //'depth' => 1,
                //'includeRoot' => true,
            )
        );
        break;

    case 'test4':
        $Content = App::loadModel('App', 'WebContent', true);
        $menu = $Content->findTree('mainmenu', array(
            'fields' => array(
                'WebContent.name',
                'WebImage.name AS image_name',
            ),
            'fields' => '*',
            'joins' => array(
                array(
                    'model' => 'WebImage',
                    'type' => 'left',
                    'conditions' => array(
                        '*',
                        'WebImage.sort' => 0,
                    )
                )
            ),
            'conditions' => array(
                'WebImage.id !=' => null,
            ),
            'separate' => true,
        ));
        App::debug($menu); //debug
        break;

    case 'test3':
        $Order = App::loadModel('Eshop', 'EshopOrder', true);
        $orders = $Order->find(array(
//            'fields' => array(
//                'EshopOrder.number',
//                'EshopProduct.name',
//                'User.username',
//            ),
//            'fields' => '*',
//            'fields' => array(
//                '*',
//                'CONCAT(`User`.`username`, " <", `User`.`email`, ">") AS `User.email_address`',
//            ),
            'literals' => array(
                'fields' => array(
                    'CONCAT(`User`.`username`, " <", `User`.`email`, ">") AS `User.email_address`'
                )
            ),
            'joins' => array(
                array(
                    'model' => 'EshopOrderProduct',
                    'type' => 'left',
                ),
                array(
                    'model' => 'EshopProduct',
                    'toModel' => 'EshopOrderProduct',
                    'type' => 'left',
                ),
                array(
                    'module' => 'App',
                    'model' => 'User',
                    'type' => 'left',
                )
            ),
            'separate' => true,

        ));
        App::debug($orders); //debug
        break;

    case 'test2':
        $Product = App::loadModel('Eshop', 'EshopProduct', true);
        $Product->update(
            array(
                'stock' => 'stock + 1',
            ),
            array(
                'conditions' => array(
                    'EshopProduct.id' => 1,
                ),
                'literals' => array(
                    'data' => array(
                        'stock + 1', // NOT 'stock'
                    ),
//                    'fields' => array(
//                        'stock', //NOT 'stock + 1',
//
//                    ),
                )
            )
        );
        App::debug($Product->findFieldBy('stock', 'id', 1)); //debug;
        break;

    case 'test1':
        $Order = App::loadModel('Eshop', 'EshopOrder', true);
        $orders = $Order->find(array(
            'fields' => array(
                'EshopOrder.number',
                'EshopProduct.name',
                'User.username',
            ),
            'joins' => array(
                array(
                    'model' => 'EshopOrderProduct',
                    'type' => 'left',
                ),
                array(
                    'model' => 'EshopProduct',
                    'toModel' => 'EshopOrderProduct',
                    'type' => 'left',
                ),
                array(
                    'module' => 'App',
                    'model' => 'User',
                    'type' => 'left',
                )
            )
        ));
        App::debug($orders); //debug
        $Content = App::loadModel('App', 'WebContent', true);
        $contents = $Content->find(array(
            'fields' => array(
                'WebContent.name',
                'WebImage.name',
            ),
            'joins' => array(
                array(
                    'model' => 'WebImage',
                    'type' => 'left',
                    'conditions' => array(
                        '*',
                        'WebImage.sort' => 0,
                    )
                )
            ),
            'conditions' => array(
                'WebImage.id !=' => null,
            ),
            'qualify' => true,
        ));
        App::debug($contents); //debug
        break;

    case 'test':
        $a = array(
            'AND',
            'a' => 'asd',
        );
        array_unshift($a, 'asas = sdfsdf');
        print_r($a);
        break;

    case 'test':
        App::debug(base64_encode('A')); //debug
        App::debug(base64_encode(' ')); //debug
        App::debug(base64_encode('')); //debug
        App::debug(base64_decode('')); //debug
        break;

        function _xyz($uri) {
            $uriRegex = preg_quote($uri, '/ ');
            if (substr($uriRegex, -2) !== '\*') {
                $uriRegex = $uriRegex . '$';
            }
            else {
                $uriRegex = substr($uriRegex, 0, -2);
            }
            if (substr($uriRegex, 0, 2) !== '\*') {
                $uriRegex = '^' . $uriRegex;
            }
            else {
                $uriRegex = substr($uriRegex, 2);
            }
            $uriRegex = str_replace('\*' , '(.*?)', $uriRegex);
            return $uriRegex;
        }
        App::debug(_xyz('/slug')); //debug
        App::debug(_xyz('/slug*')); //debug
        App::debug(_xyz('*slug')); //debug
        App::debug(_xyz('*slug*')); //debug
        App::debug(_xyz('/slug1*slug2')); //debug
        App::debug(_xyz('/slug1*slug2*')); //debug
        App::debug(_xyz('*slug1*slug2')); //debug

   case 'pdf1':
        ?>

<!--<object data="/userfiles/files/piesne.pdf" type="application/pdf" width="300" height="200" style="width:100%">
  alt : <a href="/userfiles/files/piesne.pdf">pdf</a>
</object>

<embed src="/userfiles/files/piesne.pdf" type="application/pdf" width="300" height="200" />-->
<br><iframe src="/userfiles/files/poziadavky_2013.pdf" frameborder="0" style="width: 100%; min-height: 300px;"></iframe>
<!--<br><iframe src="http://b2b.inform-za.sk/obsahy/9788086606651.pdf" frameborder="0" style="width: 100%; min-height: 300px;" ></iframe>-->
<!--<iframe src="http://drive.google.com/viewerng/viewer?url=http://www.neografia.sk/userfiles/files/V%C5%A0EOBECN%C3%89%20OBCHODN%C3%89%20PODMIENKY%20NEOGRAFIE_2014.pdf" frameborder="0"></iframe>-->

        <?php
        break;

    case 'pdf2':
        ?>

<!--<object data="/userfiles/files/piesne.pdf" type="application/pdf" width="300" height="200" style="width:100%">
  alt : <a href="/userfiles/files/piesne.pdf">pdf</a>
</object>

<embed src="/userfiles/files/piesne.pdf" type="application/pdf" width="300" height="200" />-->
<!--<br><iframe src="/userfiles/files/piesne.pdf" frameborder="0" style="width: 100%; min-height: 300px;"></iframe>-->
<br><iframe src="http://b2b.inform-za.sk/obsahy/9788086606651.pdf" frameborder="0" style="width: 100%; min-height: 300px;" ></iframe>
<!--<iframe src="http://drive.google.com/viewerng/viewer?url=http://www.neografia.sk/userfiles/files/V%C5%A0EOBECN%C3%89%20OBCHODN%C3%89%20PODMIENKY%20NEOGRAFIE_2014.pdf" frameborder="0"></iframe>-->

        <?php
        break;


    case 'testPdf':
        $id = uniqid('_pdf_');
        ?><a id="<?php echo $id ?>" href="/_debug/pdf1" data-fancybox-type="iframe" style="font-size: 40px"><?php
            ?>pdf1<?php
        ?></a><?php
        ?><br><?php
        ?><a id="<?php echo $id ?>" href="/_debug/pdf2" data-fancybox-type="iframe" style="font-size: 40px"><?php
            ?>pdf2<?php
        ?></a><?php
        App::startJsCapture();
        ?><script type="text/javascript">jQuery(function(){jQuery("#<?php echo $id ?>").fancybox({
            fitToView	: true,
            //width		: '70%',
            //height		: '70%',
            autoSize	: true,
            closeClick	: false,
            openEffect	: 'none',
            closeEffect	: 'none'
    //        helpers : {
    //			media : {}
    //		}
        });});</script><?php
        App::endJsCapture();
        // attach fancybox
        App::setCssFiles(array(
            '/app/js/vendors/fancybox/jquery.fancybox.css',
            //'/app/js/vendors/fancybox/helpers/jquery.fancybox-buttons.css',
            //'/app/js/vendors/fancybox/helpers/jquery.fancybox-thumbs.css',
        ));
        App::setJsFiles(array(
            '/app/js/vendors/jquery.min.js',
            '/app/js/vendors/jquery.mousewheel-3.0.6.pack.js',
            '/app/js/vendors/fancybox/jquery.fancybox.pack.js',
            //'/app/js/vendors/fancybox/helpers/jquery.fancybox-buttons.js',
            '/app/js/vendors/fancybox/helpers/jquery.fancybox-media.js',
            //'/app/js/vendors/fancybox/helpers/jquery.fancybox-thumbs.js',
        ));
        break;

//    case 'deleteEnAndDeRoots':
//        $Content = App::loadModel('App', 'WebContent', true);
//        // remove en and de roots
//        $Content->deleteTreeNode(134);
//        $Content->deleteTreeNode(135);
////        $Content->deleteTreeNode(145);
//
//        break;
//
//    case 'Html_contentMenu':
//        $content = '
//            <h2>c</h2>
//                <h3>d</h3>
//            <h2>e</h2>
//                <h3>f</h3>
//            <h1>a</h1>
//            <h1>b</h1>
//                <h2>c</h2>
//                    <h4>d</h4>
//                <h3>e</h3>
//                <h3>f</h3>
//            <h1>g</h1>
//                <h2>h</h2>
//                    <h3>i</h3>
//                <h2>j</h2>';
////        $content = '
//
//    case 'test1':
//        $uri = '/prod*ukt/*';
//        $uriRegex = preg_quote($uri, '/ ');
//        if (
//            substr($uriRegex, -2) === '\*'
//            && substr($uriRegex, 0, 2) === '\*'
//        ) {
//            $uriRegex = substr($uriRegex, 2, -2);
//        }
//        elseif (substr($uriRegex, -2) === '\*') {
//            $uriRegex = '^' . substr($uriRegex, 0, -2);
//        }
//        elseif (substr($uriRegex, 0, 2) === '\*') {
//            $uriRegex = substr($uriRegex, 2) . '$';
//        }
//        $uriRegex = str_replace('\*' , '(.*?)', $uriRegex);
//        App::debug($uriRegex); //debug
//        break;
//
//    case 'test':
//        App::debug(__FILE__ ); //debug
//        App::setLayout(false);
//        $response = array(
//            'default' => 'xxxx',
//            '/' => 'yyy',
//            '/my-page' => 'xxxqqqd',
//        );
//        // update $backlinks variable in this file
//        $backlinksString = str_pad('', 8) . "\$backlinks = array(\n";
//        foreach($response as $k => $v) {
//            $k = str_replace("'", "\'", $k);
//            $v = str_replace("'", "\'", $v);
//            $backlinksString .= str_pad('', 12) . "'{$k}' => '{$v}',\n";
//        }
//        $backlinksString .= str_pad('', 8) . ");\n";
//        $fileString = file_get_contents(ROOT . DS . 'app' . DS . 'screens' . DS . '_bl.php');
//        $startToken = "function _bl_get() {";
//        $endToken = "        \$actualUri = '/' . ltrim(\$_SERVER['REQUEST_URI'], '/');";
//        $start = strpos($fileString, $startToken);
//        $end = strpos($fileString, $endToken);
//        // if no start and/or end has been found then keep the actual link and return INVALID
//        if (
//            $start === false
//            || $end === false
//        ) {
//            return 'INVALID';
//        }
//        $start += strlen($startToken);
//        $fileString = rtrim(substr($fileString, 0, $start)) . PHP_EOL . $backlinksString . substr($fileString, $end);
//        echo $fileString;
//        break;
//
//    case 'String_explode':
//        $r = explode('.', 'a.b.c.d.e');
//        App::debug($r); //debug
//        $r = explode('.', 'a.b.c.d.e', null);
//        App::debug($r); //debug
//        $r = explode('.', 'a.b.c.d.e', 2);
//        App::debug($r); //debug
//        break;
//
    case 'Model_export':
//        $Language = App::loadModel('App', 'Language', true);
//        $Language->export(array('conditions' => array('id >' => 0)));
        $Product = App::loadModel('Eshop', 'EshopProduct', true);
//        $Product->export(array('limit' => 50));
//        $Product->export(array('limit' => 50), array('encoding' => 'CP1250'));
//        $records = $Product->find(array('limit' => 50, 'resource' => true));
//        $Product->export($records, array('encoding' => 'CP1250'));
        $Product->export(array('limit' => 50), array(
            'format' => 'xls',
//            'output' => 'save',
        ));
        break;
//
//    case 'pathinfo':
//        $r = File::getPathinfo('');
//        App::debug($r); //debug
//        $r = File::getPathinfo('php://output');
//        App::debug($r); //debug
//        $r = File::getPathinfo('php://outputmyfile');
//        App::debug($r); //debug
//        $r = File::getPathinfo('php://output/myfile');
//        App::debug($r); //debug
//        $r = File::getPathinfo('php://output/myfile.csv');
//        App::debug($r); //debug
//        $r = parse_url('php://output');
//        App::debug($r, 'parse_url'); //debug
//        $r = parse_url('php://outputmyfile');
//        App::debug($r, 'parse_url'); //debug
//        $r = parse_url('php://output/myfile');
//        App::debug($r, 'parse_url'); //debug
//        $r = parse_url('php://output/myfile.csv');
//        App::debug($r, 'parse_url'); //debug
//        $r = parse_url('php://output/somedir/myfile.csv');
//        App::debug($r, 'parse_url'); //debug
//        $r = parse_url('/output/somedir/myfile.csv');
//        App::debug($r, 'parse_url :)'); //debug
//        $r = parse_url('php://output',  PHP_URL_PATH);
//        App::debug($r, 'parse_url'); //debug
//        $r = parse_url('php://outputmyfile',  PHP_URL_PATH);
//        App::debug($r, 'parse_url'); //debug
//        $r = parse_url('php://output/myfile',  PHP_URL_PATH);
//        App::debug($r, 'parse_url'); //debug
//        $r = parse_url('php://output/myfile.csv',  PHP_URL_PATH);
//        App::debug($r, 'parse_url'); //debug
//        $r = parse_url('php://output/somedir/myfile.csv',  PHP_URL_PATH);
//        App::debug($r, 'parse_url'); //debug
//        break;
//
////    case 'settings':
////        $r = App::getSetting('App', 'seo.defaultTitle');
////        App::debug($r); //debug
////        $r = App::getSetting('App', 'seo.defaultTitle', array('lang' => 'sk'));
////        App::debug($r); //debug
////        $r = App::getSetting('App', 'seo.defaultTitle', array('lang' => 'de'));
////        App::debug($r); //debug
////        $r = App::getSetting('App', 'seo.defaultTitle', array('lang' => 'en'));
////        App::debug($r); //debug
////
////        App::setSetting('App', 'seo.defaultTitle', 'SK value', array('lang' => 'sk'));
////        App::setSetting('App', 'seo.defaultTitle', 'EN value', array('lang' => 'en'));
////        App::setSetting('App', 'seo.defaultTitle', 'DE value', array('lang' => 'de'));
////        App::setSetting('App', 'seo.defaultTitle', 'DEFAULT App::$lang value');
////
////        $r = App::getSetting('App', 'seo.defaultTitle');
////        App::debug($r); //debug
////        $r = App::getSetting('App', 'seo.defaultTitle', array('lang' => 'sk'));
////        App::debug($r); //debug
////        $r = App::getSetting('App', 'seo.defaultTitle', array('lang' => 'de'));
////        App::debug($r); //debug
////        $r = App::getSetting('App', 'seo.defaultTitle', array('lang' => 'en'));
////        App::debug($r); //debug
////        break;
//
////            <h2>c</h2>
////                <h3>d</h3>
////            <h2>e</h2>
//
////            <h2>j</h2>';
//        echo Html::textMenu($content);
//        break;
//
//    case 'test':
//        App::debug(array_merge(
//                array(123 => 456, 78 => 45),
//                array(789 => 963, 23 => 56)
//        )); //debug // RESULT: Array ( [0] => 456 [1] => 45 [2] => 963 [3] => 56 )
//        print_r(array_merge(
//                array(123 => 456, 78 => 45),
//                array(789 => 963, 23 => 56)
//        )); //debug
//
//        App::debug(array(123 => 456, 78 => 45) + array(789 => 963, 23 => 56)); //debug // RESULT: Array ( [123] => 456 [78] => 45 [789] => 963 [23] => 56 )
//        print_r(array(123 => 456, 78 => 45) + array(789 => 963, 23 => 56)); //debug
//        break;
//
//    case 'Model_copyTreeNode':
//        $Content = App::loadModel('App', 'WebContent', true);
////        $copyIds = $Content->copyTreeNode(
////            array('conditions' => array('pid' => 'mainmenu', 'lang' => 'sk')),
////            4,
////            array('targetData' => array('lang' => 'en'))
////        );
//        $copyIds = $Content->copyTreeNode(
//            array('conditions' => array('pid' => 'root', 'lang' => 'sk')),
//            array('conditions' => array('pid' => 'root', 'lang' => 'en', 'parent_id' => null)),
//            array('targetData' => array('lang' => 'en'))
//        );
//        App::debug($copyIds); //debug
//        App::debug($Content->getErrors()); //debug
//        break;
//
//
//    case 'Arr_camelizeKeys':
//        $a = array(
//            'a-key-number-one' => '1',
//            'a-key-number-two' => '2',
//            'someOtherKey' => 123,
//            45,
//            'a-key-number-three' => array(
//                'a-key-number-four' => 4,
//                'a-key-number-five' => array(
//                    'a-key-number-six' => 6,
//                ),
//                23,
//            ),
//            array(
//                'a-key-number-seven' => 7,
//                'a-key-number-eight' => array(
//                    'a-key-number-nine' => 9,
//                ),
//                78,
//            ),
//        );
//        $r = Arr::camelizeKeys($a);
//        App::debug($r); //debug
//        $r = Arr::camelizeKeys($a, array('separator' => '-'));
//        App::debug($r); //debug
//        $r = Arr::camelizeKeys($a, array('separator' => '-', 'depth' => 2));
//        App::debug($r); //debug
//        break;
//
//    case 'String_camelize':
//        $r = Str::camelize('this-is-a-dashed-string');
//        App::debug($r); //debug
//        $r = Str::camelize('this-is-a-dashed-string', array('separator' => '-'));
//        App::debug($r); //debug
//        break;
//
//    case 'String_dasherize':
//        $r = Str::dasherize('thisIsADashedString');
//        App::debug($r); //debug
//        break;
//
//    case 'Utility_convertToBytes':
//        $r = Utility::convertToBytes(123);
//        App::debug($r); //debug
//        $r = Utility::convertToBytes('40B');
//        App::debug($r); //debug
//        $r = Utility::convertToBytes('40K');
//        App::debug($r); //debug
//        $r = Utility::convertToBytes('40M');
//        App::debug($r); //debug
//        $r = Utility::convertToBytes('40TB');
//        App::debug($r); //debug
//        $r = Utility::convertToBytes('40asdB');
//        App::debug($r); //debug
//        break;
//
//    case 'test':
//        App::debug(ini_get('upload_max_filesize')); //debug
//        App::debug(ini_get('post_max_size')); //debug
//        break;
//
//    case 'WebContent_move':
//        $Content = App::loadModel('App', 'WebContent', true);
//        // move home
//        $Content->moveTreeNode(3, 60);
//        break;
//
//    case 'DB_getQueryConditions':
//        $Content = App::loadModel('App', 'WebContent', true);
//        $r = $Content->find(array(
//            'conditions' => array(
//                'pid' => null,
//            )
//        ));
//        App::debug($r); //debug
//        $r = $Content->find(array(
//            'conditions' => array(
//                'pid !=' => null,
//            )
//        ));
//        App::debug($r); //debug
//        $r = $Content->find(array(
//            'conditions' => array(
//                'id !=' => array(1, 3, 4),
//            )
//        ));
//        App::debug($r); //debug
//        $r = $Content->find(array(
//            'conditions' => array(
//                'name ~' => 'absi\'',
//            )
//        ));
//        App::debug($r); //debug
//        $r = $Content->find(array(
//            'conditions' => array(
//                'name !~' => 'absi\'',
//            )
//        ));
//        App::debug($r); //debug
//        $r = $Content->find(array(
//            'conditions' => array(
//                'name %~%' => 'absi\'',
//            )
//        ));
//        App::debug($r); //debug
//        $r = $Content->find(array(
//            'conditions' => array(
//                'name ~' => '%absi\'%',
//            )
//        ));
//        App::debug($r); //debug
//        $r = $Content->find(array(
//            'conditions' => array(
//                'name NOT LIKE' => '%absi\'%',
//            )
//        ));
//        App::debug($r); //debug
////// EXCEPTION: (ERROR) 0.28 ms / 2.37 ms - SELECT * FROM `run_web_contents` AS `WebContent` WHERE `name` NOT LIKE '%absi'%';
////        $r = $Content->find(array(
////            'conditions' => array(
////                'name NOT LIKE \'%absi\'%\'',
////            )
////        ));
//        $r = $Content->find(array(
//            'conditions' => array(
//                'name NOT LIKE \'%absi%\'',
//            )
//        ));
//        App::debug($r); //debug
//        break;
//
//
//    case 'test':
//        App::debug(explode('-', '-2-56-')); //debug
//        break;
//
//    case 'Model_findUpInTree':
//        $Content = App::loadModel('App', 'WebContent', true);
//        $r = $Content->findUpInTree(39, array('start' => -2, 'length' => 1));
//        $r = $Content->findUpInTree(39);
//        App::debug(count($r)); //debug
//        App::debug($r); //debug
//        $r = $Content->findLevelInTree(39);
//        App::debug($r); //debug
////        $r = $Content->findUpInTree('_footer.aboutUs.1');
////        $r = $Content->findUpInTree(array('id' => 39, 'path' => '-1-34-38-'));
////        $r = $Content->findUpInTree(array('conditions' => array('pid' => '_footer.aboutUs.1', 'lang' => App::$lang)));
////        $r = $Content->findUpInTree(39, array(
////            'conditions' => array('pid' => 'footer')
////        ));
//        break;
//
//    case 'Arr_deflateData':
//        App::debug((int)'A'); //debug
//        App::debug(ord('A')); //debug
//        $r = Arr::deflateData(array(
//            'id' => 1,
//            'username' => 'Admin',
//            'password' => '53cr3t',
//            'run_groups_ids' => array(1, 2),
//            'test_field' => array('ThisIsNotIdentifier' => 'because identifier must precede field'),
//            'ThisIsNotIdentifier' => 'because value is not array',
//            'ThisCouldBeIdentifier' => array('if this item is associative', 'hereYes' => 'because this is associative'),
//            'UserProfile' => array(
//                'firstname' => 'Ján',
//                'lastname' => 'Šimko',
//                'hobbies' => array('bike', 'flute', 'pc'),
//            ),
//            'Eshop' => array(
//                'EshopUserProfile' => array(
//                    'address' => 'Some street 01',
//                    'emptyArray' => array(),
//                ),
//            ),
//        ));
//        App::debug($r); //debug
//        break;
//
//    case 'get_browser':
//        echo '<pre>';
//        print_r(get_browser(
//            'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/36.0.1985.125 Safari/537.36',
//            true
//        ));
//        echo '</pre>';
//        break;
//
//    case 'rawurlencode':
//        App::debug(rawurlencode('panel/index?filter=all'));
//        // if you call this debug like: .../_debug/rawurlencode/XeHNksvVFs/panel%2Findex%3Ffilter%3Dall
//        App::debug($_GET, '$_GET'); //debug
//        App::debug(App::$args, 'App::$args'); //debug
//        break;
//
//    case 'File_createPdf':
////        $r = File::createPdf(
////            '<h1>Hello world</h1><p>Is\'t it great to create pdf files in such an easy way?!</p> :)',
////            array(
////                'name' => 'userfiles/files/hello.pdf',
////                'output' => 'file',
////            )
////        );
////        echo $r;
//
////        App::sendEmail('Hello, I send you a file', '<EMAIL>', array(
////           'attachments' => array(array(
////               'data' => File::createPdf(
////                    '<h1>Hello world</h1><p>Is\'t it great to create pdf files in such an easy way?!</p> :)',
////                    array('output' => 'string')
////                ),
////               'filename' => 'my-file.pdf',
////               'contentType' => 'application/pdf',
////           )),
////        ));
//
//        App::sendEmail('Hello, I send you a file', '<EMAIL>', array(
//           'attachments' => array(
//               'userfiles/files/hello.pdf'
//           ),
//        ));
//        break;
//
//    case 'parse_str':
//        $str = "first=value&arr[]=foo+bar&arr[]=baz";
//        $str = json_encode(array(
//            'data' => json_encode(array('x' => 1, 'y' => 2)),
//            'id' => 23,
//            'name' => 'mojo',
//        ));
//        parse_str($str, $output);
//        App::debug($output); //debug
//        break;
//
//    case 'App_request':
////        App::debug(curl_version()); //debug
//
////        $r = App::request('http://distri.albatrosmedia.sk/');
//
//        $r = App::request('http://distri.albatrosmedia.sk/', array(
//            'method' => 'post',
//            //'persist' => true,
//            //'timeout' => 1,
//            'post' => array(
//                'authUsername' => 'PV0011443',
//                'authPassword' => 'zjavka',
//                'authLogin' => 'Prihlásiť',
//            )
//        ));
//
////        $r = App::request('http://distri.albatrosmedia.sk/?p=actions&action=download/store'); // este to nefunguje tak ako ma, medzi dvoma nasledujúcimi volaniami App::request() sa nezachová session
////        $r = App::request('http://distri.albatrosmedia.sk/?p=actions&action=download/store', array(
////            'method' => 'post',
////            //'persist' => true,
////            'post' => array(
////                'authUsername' => 'PV0011443',
////                'authPassword' => 'zjavka',
////                'authLogin' => 'Prihlásiť',
////            )
////        ));
//
////        $r = App::request('http://******2.localhost/mvc/App/Tests/testAppRequest', array(
////            'method' => 'put',
////            //'timeout' => 1,
////            'get' => array(
////                'a' => 2,
////                'b' => 'saddsfčťčšť/das',
////            ),
////            'args' => array(23,56),
////            'params' => array(
////                'par1' => 45,
////                'par2' => '4šk23as',
////            ),
////            'put' => http_build_query(array(
////                'data' => json_encode(array('x' => 1, 'y' => 2)),
////                'id' => 23,
////                'name' => 'mojo',
////            ))
////        ));
//
//        echo $r;
//        break;
//
//    case 'DB_nestConditions':
//        $options['conditions'] = array(
//            'name' => 'test',
//            'OR',
//            'id' => 5,
//        );
////        $options['conditions'] = null;
//
//        $options['conditions'] = DB::nestConditions($options['conditions']);
//        $options['conditions']['active'] = 1;
//
//        $options['conditions'] = DB::nestConditions($options['conditions']);
//        $options['conditions'][] = array(
//            'slug' => 'my-slug',
//            'OR',
//            'id' => '2',
//        );
//
//        App::debug($options); //debug
//        App::debug(DB::getSelectQuery('my_table', $options)); //debug
//        break;
//
//    case 'Drinkcentrum_EshopOrders_addExternal':
//        App::loadVendor('App', 'nusoap/nusoap');
//        //$Client = new nusoap_client('https://drinkcentrum.sk/_soap?wsdl');
//        $Client = new nusoap_client('http://drinkcentrum/_soap?wsdl');
//        //$Client->setCredentials($username, $password, $authtype);
//
//        $order = array(
//            // address + contact
//            'fullname' => 'Test Tetstovic',
//            'street' => 'Test 23',
//            //'street' => '"Test 11"', // this will cause validation failure
//            'city' => 'Test',
//            'zip' => '03601',
//            'country' => 'Test',
//            'email' => '<EMAIL>',
//            'phone' => '0987654321',
//            'comment' => 'TOTO JE LEN TESTOVACIA OBJEDNAVKA',
//            // delivery
//            'delivery_fullname' => '',
//            'delivery_street' => '',
//            'delivery_city' => '',
//            'delivery_zip' => '',
//            'delivery_country' => '',
//            // company
//            'company_name' => '',
//            'company_id_number' => '',          // IČO
//            'company_tax_number' => '',         // DIČ
//            'company_vat_number' => '',         // IČ DPH
//            // shipment & payment
//            'shipment_price_taxless' => '2.9',             //??? ako je to s dopravou a platbou
//            'shipment_tax_rate' => '20',             //??? ako je to s dopravou a platbou
//            'shipment_name' => 'Kurierska služba DPD',
//            'payment_name' => 'Platba kartou',
//            'payment_method' => 'cardpay',
//            // items
//            'items' => array(
//                array(
//                    'id' => '900', // product code !!!, use 901 to test unexisting
//                    'amount' => '2',
//                    'price_taxless' => '11.50',
//                    'tax_rate' => '20'
//                ),
//                array(
//                    'id' => '27190', // product code !!!
//                    'amount' => '1',
//                    'price_taxless' => '28.50',
//                    'tax_rate' => '20'
//                )
//            )
//        );
//
//        $result = $Client->call('EshopOrders.addExternal', array('merchant' => 'rumovabanka', 'order' => Str::encrypt(json_encode($order), '3NDNmIpjnx3mrs03VHGj')));
//        //App::debug($result); //debug
//        echo '<pre>' . print_r($result, true) . '</pre>';
//        break;
//
//    case 'crypt':
//        $string = 'Milan Kopča Mojmír Ďurík =?/* "hehehe" a \'huhu\'';
//        echo $string . '(' . mb_strlen($string). ')<br />';
//        $crypt = Str::encrypt($string);
//        echo $crypt . '<br />';
//        $decrypt = Str::decrypt($crypt);
//        echo $decrypt . '(' . mb_strlen($string). ')<br />';
//        break;
//
//    case 'App_sendEmail':
//        App::sendEmail(
//            'Lorem ipsum a tak ďalej...',
//            '<EMAIL>',
//            array(
//                'subject' => 'Testovací email',
//            )
//        );
//        break;
//
//    case 'ikarCatalogueDownload':
//        $r = file_get_contents('https://vo.kniznyweb.sk/prihlasenie/index$204-login.html?loginId=<EMAIL>&passwd=matica123');
//        echo $r;
//        break;
//
//    case 'throwException':
//        echo '1' .PHP_EOL;
//        echo '2' .PHP_EOL;
//        throw new Exception('3');
//        echo '4' .PHP_EOL;
//        break;
//
//    case 'lockTables':
////        DB::query('LOCK TABLES `run_eshop_products` READ');
//        DB::query('LOCK TABLES `run_eshop_products` WRITE');
//        sleep(20);
//        DB::query('UNLOCK TABLES');
//        break;
//
//    case 'DB_reserveTables3':
//        DB::startTransaction('test');
//        DB::reserveTables('test', array(
//            'run_eshop_products',
//            'run_countries',
//            'run_eshop_authors',
//        ));
////        DB::insert(
////            '_run_reserved_tables',
////            array(
////                'table' => 'my',
////            ),
////            array(
////                'reserve' => false
////            )
////        );
//        sleep(15);
//        DB::commitTransaction('test');
//        sleep(15);
//        break;
//
//
//    case 'DB_reserveTables1':
//        DB::reserveTables('test', array(
//            'run_eshop_products',
//            'run_countries',
//            'run_eshop_authors',
//        ));
//        sleep(15);
//        break;
//
//    case 'DB_reserveTables2':
//        try {
//            DB::reserveTables('test', array(
//                'run_eshop_manufacturers',
//                'run_countries',
//                'run_eshop_authors',
//            ), array('retryTime' => 1000));
//            App::debug('ok'); //debug
//        }
//        catch (Exception_DB_TablesReservationFailure $e) {
//            App::debug('nok'); //debug
//        }
//        //sleep(10);
//        break;
//
//    case 'array_slice':
//        $a = array('a', 'b', 'c', 'd');
//        App::debug(array_slice($a, -1, 1)); //debug
//        App::debug(array_slice($a, -5, 1)); //debug
//        App::debug(array_slice($a, -100, 1)); //debug
//        break;
//
//    case 'test_td':
//        App::setJsI18nFiles('App');?>
        <script type="text/javascript">
           console.log(__td('App', 'Declaration of adulthood')); //debug
           console.log(__td('App', 'Main email of E-shop')); //debug
           console.log(__td('App', 'New\'s order')); //debug
        </script>
        //<?php
//        break;
//
//    case 'test':
////        App::debug(array_merge(
////            array('a' => 1, 'c' => 4),
////            array('b' => 2, 'a' => 3, 'd' => 5)
////        )); //debug
////        App::debug(
////            array('a' => 1, 'c' => 4) + array('b' => 2, 'a' => 3, 'd' => 5)
////        ); //debug
////        App::debug(array_merge(
////            array('b' => 2, 'a' => 3, 'd' => 5),
////            array('a' => 1, 'c' => 4)
////        )); //debug
////        App::debug(
////            array('b' => 2, 'a' => 3, 'd' => 5) + array('a' => 1, 'c' => 4)
////        ); //debug
//        App::debug(array_merge_recursive(
//            array('Admin' => array('zh_ZH'), 'Eshop' => array('sk_SK')),
//            array('Admin' => array('sk_SK'), 'Eshop' => array('sk_SK', 'us_EN'), 'Mailer' => 'us_EN')
//        )); //debug
//        App::debug(array_merge_recursive(
//            array('Admin' => array('zh_ZH' => true), 'Eshop' => array('sk_SK' => true)),
//            array('Admin' => array('sk_SK' => true), 'Eshop' => array('sk_SK' => true, 'us_EN' => true), 'Mailer' => array('us_EN' => true))
//        )); //debug
//        break;
//
//    case 'Html_smartForm':
//        echo Html::smartForm(array(
//            'columns' => 2,
//            'fields' => array(
//                'EshopProduct.name' => array('label' => __d(__FILE__, 'Name')),
//                'EshopProduct.active' => array('label' => __d(__FILE__, 'Active'), 'type' => 'checkbox'),
//                'EshopProduct.subtitle' => array('label' => __d(__FILE__, 'Subtitle')),
//                'EshopProduct.code' => array('label' => __d(__FILE__, 'Code')),
//                'EshopProduct.annotation' => array('label' => __d(__FILE__, 'Annotation')),
//                'EshopProduct.pages' => array('label' => __d(__FILE__, 'Pages')),
//                'EshopProduct.run_eshop_manufacturers_id' => array('label' => __d(__FILE__, 'Manufacturer')),
//                array('fieldset' => __d(__FILE__, 'Description'), 'columns' => 1),
//                    'EshopProduct.dimensions' => array('label' => __d(__FILE__, 'Dimensions')),
//                    'EshopProduct.weight' => array('label' => __d(__FILE__, 'Weight')),
//                    'EshopProduct.binding' => array('label' => __d(__FILE__, 'Binding')),
//                    'EshopProduct.language' => array('label' => __d(__FILE__, 'Language')),
//                    'EshopProduct.description' => array('label' => __d(__FILE__, 'Description')),
//                array('/fieldset'),
//                array('fieldset' => __d(__FILE__, 'SEO')),
//                    'EshopProduct.slug' => array('label' => __d(__FILE__, 'Slug')),
//                    'EshopProduct.seo_title' => array('label' => __d(__FILE__, 'Title')),
//                    'EshopProduct.seo_description' => array('label' => __d(__FILE__, 'Description')),
//                    'EshopProduct.seo_keywords' => array('label' => __d(__FILE__, 'Keywords')),
//                array('/fieldset'),
//                array('fieldset' => __d(__FILE__, 'Prices'), 'columns' => 3),
//                    'EshopProduct.price' => array('label' => __d(__FILE__, 'Price')),
//                    'EshopProduct.discount_from' => array('label' => __d(__FILE__, 'Discount from')),
//                    'EshopProduct.tax_rate' => array('label' => __d(__FILE__, 'Tax')),
//                    'EshopProduct.discount_price' => array('label' => __d(__FILE__, 'Discount price')),
//                    'EshopProduct.discount_to' => array('label' => __d(__FILE__, 'Discount to')),
//                     array('row'),
//                    'EshopProduct.uniklub_price' => array('label' => __d(__FILE__, 'Uniklub price')),
//                array('/fieldset'),
//            )
//        ));
//        break;
//
//    case 'Validate_dataConditions':
//        $data = array(
//            'username' => 'admin',
//            'group_id' => 2,
//            'active' => true,
//            'Profile' => array(
//                'firstname' => 'Adminko',
//                'lastname' => 'Adminovič',
//                'age' => 38,
//            )
//        );
//        $r = 1;
//        Validate::dataConditions($data, array(
//            'username =' => 'admin',
//            'Profile.firstname ~' => 'adminko',
//            'group_id !' => 3,
//            'age >' => 5,
//            'active' => 1,
//            'Profile.lastname ~' => 'adminovic',
//        ));
//        Validate::dataConditions($data, array(
//            'username' => 'admin',
//            'Profile.firstname' => 'Adminko',
//            array(
//                array(
//                    'group_id' => 2,    // true
//                    'OR',
//                    'age' => 5,
//                ),
//                'AND',
//                'active' => 1,
//                'OR',
//                'group_id' => 3,
//                'AND',
//                'Profile.lastname' => 'Adminovič',
//            )
//        ));
//        $r = Validate::dataConditions($data, array(
//            'username' => 'admin',
//            'Profile.firstname' => 'Adminko',
//            array(
//                'group_id' => array(3, 9),
//                'AND',
//                'active' => 1,
//                'OR',
//                'group_id' => 2,        // true
//                'AND',
//                'Profile.lastname' => 'Adminovič',
//            )
//        ));
//        $r = Validate::dataConditions($data, array(
//            'username' => 'admin',
//            'Profile.firstname' => 'Adminko',
//            array(
//                'group_id' => array(2, 9),
//                'AND',
//                'active' => 1,          // false (in strict comparison)
//                'OR',
//                'group_id' => 3,
//                'AND',
//                'Profile.lastname' => 'Adminovič',
//            )
//        ), true) ;
//        App::debug($r); //debug
//        break;
//
//    case 'copyWebTree':
//        $Tool = App::loadModel('App' ,'Tool', true);
//        $Tool->copyWebTree(
//            1193,
////            array(
////                'conditions' => array(
////                    'pid' => 'officesRent',
////                    'run_languages_code' => 'us/en'
////                )
////            ),
//            1194,
//            'us/ja'
//        );
//        $Tool->copyWebTree(
//            1193,
////            array(
////                'conditions' => array(
////                    'pid' => 'officesRent',
////                    'run_languages_code' => 'us/en'
////                )
////            ),
//            1195,
//            'us/zh'
//        );
//////mojo: main menu
////        $Tool->copyWebTree(
////            694,
//////            array(
//////                'conditions' => array(
//////                    'pid' => 'mainmenu',
//////                    'run_languages_code' => 'us/en'
//////                )
//////            ),
////            1315,
////            'us/ja'
////        );
////        $Tool->copyWebTree(
////            694,
//////            array(
//////                'conditions' => array(
//////                    'pid' => 'mainmenu',
//////                    'run_languages_code' => 'us/en'
//////                )
//////            ),
////            1316,
////            'us/zh'
////        );
//        break;
//
//    case 'copyWebTreeOld':
//        $Tool = App::loadModel('App' ,'Tool', true);
//        $Tool->copyWebTree(
//            array(
//                'conditions' => array(
//                    'pid' => 'root',
//                    'run_languages_code' => 'jp/en'
//                )
//            ),
//            null,
//            'us/en'
//        );
//        $Tool->copyWebTree(
//            array(
//                'conditions' => array(
//                    'pid' => 'root',
//                    'run_languages_code' => 'jp/ja'
//                )
//            ),
//            null,
//            'us/ja'
//        );
//        $Tool->copyWebTree(
//            array(
//                'conditions' => array(
//                    'pid' => 'root',
//                    'run_languages_code' => 'jp/zh'
//                )
//            ),
//            null,
//            'us/zh'
//        );
//        break;
//
//    case 'moduleAlias':
//        $User = App::loadModel('App', 'User', true);
//        $users = $User->find(array(
//            'joins' => array(
//                array(
//                    'model' => 'MailerContact',
//                    'module' => 'Mailer',
//                    'type' => 'left'
//                )
//            ),
//            'fields' => array(
//                'App.User.username',
//                'App.User.email',
//                'Mailer.MailerContact.email',
//            ),
//            'moduleAlias' => true,
//            'separate' => array('Mailer.MailerContact'),
//            'conditions' => array(
//                'App.User.username' => 'admin',
//            )
//        ));
////        $users = $User->find(array(
////            'joins' => array(
////                array(
////                    'model' => 'MailerContact',
////                    'module' => 'Mailer',
////                    'type' => 'left'
////                )
////            ),
////            'fields' => array(
////                'User.username',
////                'User.email',
////                'MailerContact.email',
////            ),
////            //'moduleAlias' => true,
////            //'separate' => array('Mailer.MailerContact'),
////        ));
//        App::debug($users); //debug
//        break;
//
//    case 'test':
//        $match = array();
//        preg_match('/(\S+)\s+(?:preklad|překlad|text)\.?\W*$/i', 'český překlad', $match); // ok
//        App::debug($match); //debug
//        preg_match('/(\S+)\s+(?:preklad|překlad|text)\.?\W*$/iu', 'český překlad', $match); // ok
//        App::debug($match); //debug
//        preg_match('/(\S+)\s+(?:preklad|překlad|text)\.?\W*$/iu', 'cesky preklad', $match); // ok
//        App::debug($match); //debug
//        break;
//
//    case 'Utility_isprintf':
//        $r = Str::isprintf(
//            'Inserts contain following items: :User.name:, :User.id:, :Group.pid:. And there is %s more %s.',
//            array(
//                'User.name' => 'Test',
//                'User.id' => 12,
//                'Group.pid' => 'admins',
//            ),
//            'something',
//            'there'
//        );
//        App::debug($r); //debug
//        __d(
//            'Inserts contain following items: :User.name:, :User.id:, :Group.pid:. And there is %s more %s.',
//            array(
//                'User.name' => 'Test',
//                'User.id' => 12,
//                'Group.pid' => 'admins',
//            ),
//            'something',
//            'there'
//        );
//        App::debug($r); //debug
//        break;
//
//    case 'test':
//        $x = '956';
//        App::debug($x[0]); //debug 9
//        App::debug($x[1]); //debug 5
//        App::debug($x[2]); //debug 6
//        App::debug($x['path']); //debug 9 !!!
//        App::debug(empty($x['path'])); //debug FALSE !!!
//        App::debug(isset($x['path'])); //debug TRUE !!!
//        $x = 956;
//        $x['path']; //debug
//        App::debug(empty($x['path'])); //debug TRUE
//        break;
//
//    case 'jstest':
//        App::setJsFiles('App', array(
//            '/libs/App.js',
//            '/libs/PhpJs.js',
//            '/libs/Arr.js',
//            '/libs/String.js',
//            '/libs/Number.js',
//            '/libs/Utility.js',
//            '/libs/Validate.js',
//            '/libs/Sanitize.js',
//        ));
//        ?>
        <script type="text/javascript">
            var Utility = Run.App.Utility, a, result;

            result = Run.App.App.parseUrl(
                'http://******2.localhost/mvc/App/Languages/admin_edit/1?_requestSource_=frame&data[User][name]=mojo&data[User][age]=5',
                {
                    'hasGetParams': true,
                    'parseGetParams': true
                }
            );
            console.log(result); //debug
            result = Run.App.App.rebuildUrl(
                '/myslug/36/paramX:xyz?getX=3',
                {
                    'params': {'paramA': 'abc'},
                    'args': [25],
                    'get': {'getA': 2}
                }
            );
            console.log(result); //debug
            result = Run.App.App.rebuildUrl(
                '/myslug/36/paramX:xyz?getX=3',
                {
                    'params': {'paramA': 'abc'},
                    'args': [25],
                    'get': {'getA': 2},
                    'mergeParams': true,
                    'mergeArgs': true,
                    'mergeGet': true
                }
            );
            console.log(result); //debug

            console.log(Run.App.PhpJs.microtime(), 'Run.App.PhpJs.microtime()'); //debug
            console.log(Run.App.PhpJs.microtime(true), 'Run.App.PhpJs.microtime(true)'); //debug
            console.log(new Date().getTime(), 'new Date().getTime()'); //debug
            console.log(Run.App.String.slugize('a-strín%+*G/to_be:SLUGGe@#d'), 'a-strín%+*G/to_be:SLUGGe@#d');
            console.log(Run.App.String.slugize('a  - _ strín%+*G/-to_be:SLUGGe@#d 01'), 'a  - _ strín%+*G/-to_be:SLUGGe@#d 01');
            console.log(Run.App.String.slugize('a  - _ strín%+*G-/-to_be:SLUGGe@#d 02', '_'), 'a  - _ strín%+*G-/-to_be:SLUGGe@#d 02');
            console.log(Run.App.String.slugize('a/ešte/opäť-úžasne_vymakaný:meruôsmy slugičôček'), 'a/ešte/opäť-úžasne_vymakaný:meruôsmy slugičôček');
            console.log("\xE2\x80\x93", "\xE2\x80\x93"); //debug
        </script>
        //<?php
//        break;
//
//    case 'Test_static_inheritance':
//        App::loadModel('Eshop', 'EshopProduct');
//        $r = EshopProduct::getParentIdsFromTreePath('-23-56-78-');
//        App::debug($r); //debug
//        break;
//
//    case 'js':
//        App::setJsFiles('App', array('libs/' . $this->args[1] . '.js'));
//        break;
//
//    case 'Validate_emptyValue_js':
//        App::setJsFiles('App', array('libs/Validate.js'))
//        ?>
        <script type="text/javascript">
            var Validate = Run.App.Validate, a;
            console.log(Validate.emptyValue(a), 'a');
            console.log(Validate.emptyValue(undefined), 'undefined');
            console.log(Validate.emptyValue(null), 'null');
            console.log(Validate.emptyValue(false), 'false');
            console.log(Validate.emptyValue(''), "''");
            console.log(Validate.emptyValue([]), '[]');
            console.log(Validate.emptyValue({}), '{}');
            console.log(Validate.emptyValue(['a']), "['a']");
            console.log(Validate.emptyValue({a: 'b'}), "{a: 'b'}");
        </script>
        //<?php
//        break;
//
//    case 'js_types':
//        ?>
        <script type="text/javascript">
            var a;
            console.log(a instanceof Array); //debug
            a = [];
            console.log(a instanceof Array); //debug
            a = {};
            console.log(a instanceof Array); //debug
            console.log(a instanceof Object); //debug
        </script>
        //<?php
//        break;
//
//    case 'App_getValue_js':
//        App::setJsFiles('App', array('libs/Validate.js','libs/Utility.js', 'libs/PhpJs.js', 'libs/App.js'))
//        ?>
        <script type="text/javascript">
            var App = Run.App.App, a;
            console.log(a); //debug
            console.log(App.getValue(a)); //debug
            //console.log(App.getValue(a[0])); //debug - !!! Uncaught TypeError: Cannot read property '0' of undefined
            a = [];
            console.log(App.getValue(a[0])); //debug
            console.log(App.getValue(a[1])); //debug
            console.log(App.getValue(a[1], '2')); //debug
            a[0] = '';
            console.log(App.getValue(a[0], '2')); //debug
            console.log(App.getValue(a[0], '2', true)); //debug
        </script>
        //<?php
//        break;
//
//    case 'App_resolveUrl_js':
//        App::setJsFiles('App', array('libs/Validate.js','libs/Utility.js', 'libs/PhpJs.js', 'libs/App.js'))
//        ?>
        <script type="text/javascript">
            var App = Run.App.App, r;
            r = App.getUrl('myslug');
            App::debug(r); //debug
            r = App.getUrl({
                'locator': 'myslug',
                'absolute': true,
            });
            App::debug(r); //debug
            r = App.getUrl({
                'locator': 'myslug',
                'lang': 'sk',
                'absolute': true,
            });
            App::debug(r); //debug
            r = App.getUrl({
                'locator': 'http://mysite.sk',
                'lang': 'sk',
                'absolute': true,
            });
            App::debug(r); //debug
            r = App.getUrl({
                'controller': 'Users',
                'action': 'index',
            });
            App::debug(r); //debug
            r = App.getUrl(
                {
                    'controller': 'Users',
                    'action': 'index',
                    'args': [25, 'my-param'],
                    'params': {
                        'page': 5,
                    },
                    'get': {
                        'id': 7,
                        'name': 'Pištík Lajčík',
                    }
                }
            );
            App::debug(r); //debug
            r = App.getUrl(
                {
                    'controller': 'Users',
                    'action': 'index',
                    'lang': 'sk',
                    'absolute': true,
                    'rawurlencode': true,
                    'args': [25, 'my-param'],
                    'params': {
                        'page': 5,
                    },
                    'get': {
                        'id': 7,
                        'name': 'Pištík Lajčík',
                    }
                }
            );
            App::debug(r); //debug
        </script>
        //<?php
//        break;
//
//    case 'App_resolveUrl':
//        $r = App::getUrl('myslug');
//        App::debug($r); //debug
//        $r = App::getUrl(array(
//            'locator' => 'myslug',
//            'absolute' => true,
//        ));
//        App::debug($r); //debug
//        $r = App::getUrl(array(
//            'locator' => 'myslug',
//            'lang' => 'sk',
//            'absolute' => true,
//        ));
//        App::debug($r); //debug
//        $r = App::getUrl(array(
//            'locator' => 'http://mysite.sk',
//            'lang' => 'sk',
//            'absolute' => true,
//        ));
//        App::debug($r); //debug
//        $r = App::getUrl(array(
//            'controller' => 'Users',
//            'action' => 'index',
//        ));
//        App::debug($r); //debug
//        $r = App::getUrl(
//            array(
//                'controller' => 'Users',
//                'action' => 'index',
//                'params' => array(
//                    25,
//                    'my-param',
//                    'page' => 5,
//                ),
//                'get' => array(
//                    'id' => 7,
//                    'name' => 'Pištík Lajčík',
//                )
//            )
//        );
//        App::debug($r); //debug
//        $r = App::getUrl(
//            array(
//                'controller' => 'Users',
//                'action' => 'index',
//                'rawurlencode' => true,
//                'lang' => 'sk',
//                'absolute' => true,
//                'params' => array(
//                    25,
//                    'my-param',
//                    'page' => 5,
//                ),
//                'get' => array(
//                    'id' => 7,
//                    'name' => 'Pištík Lajčík',
//                )
//            )
//        );
//        App::debug($r); //debug
//        break;
//
    case 'App_parseUrl_js':
//        App::setJsFiles('App', array('/libs/Validate.js','libs/Utility.js', 'libs/PhpJs.js', 'libs/App.js'));
////        App::setJsFiles('App', array('/libs/Validate.js', 'libs/PhpJs.js', 'libs/App.js'));
////        App::setJsFiles('App', array('/vendors/head.load.min.js'));
////        App::setJsFiles('App', array('/vendors/LAB.min.js'));
//        ?>
        <script type="text/javascript">
//            // head loader
//            window.head.load(
//            ['/app/js/libs/Validate.js','/app/js/libs/Utility.js', '/app/js/libs/PhpJs.js'],
//            function() {

//            // LAB loader
//            window.$LAB
//            .script(['/app/js/libs/Validate.js','/app/js/libs/Utility.js', '/app/js/libs/PhpJs.js'])
//            .wait(function(){


            var App = Run.App.App;
            App::debug(App.parseUrl('/my-slug/1/dsf/name:test')); //debug
            App::debug(App.parseUrl('/my-slug/1/dsf/name:test?getpar=2')); //debug
            App::debug(App.parseUrl('/my-slug/1/dsf/name:test?getpar=2', {'hasGetParams': true})); //debug
            App::debug(App.parseUrl('/my-slug/1/dsf/name:test?getpar=2', {'hasGetParams': true, 'parseGetParams': true})); //debug
            App::debug(App.parseUrl('/en/my-slug/1/dsf/name:test?getpar=2', {'hasGetParams': true, 'parseGetParams': true})); //debug
            App::debug(App.parseUrl('/en/mvc/MyModule/MyCnt/action/1/dsf/name:test?getpar=2', {'hasGetParams': true, 'parseGetParams': true})); //debug
            App::debug(App.parseUrl('/en/mvc/MyModule/MyCnt/admin_action/1/dsf/name:test?getpar=2', {'hasGetParams': true, 'parseGetParams': true})); //debug
            App::debug(App.parseUrl('http://run.sk/en/mvc/MyModule/MyCnt/admin_action/1/dsf/name:test?getpar=2', {'hasGetParams': true, 'parseGetParams': true})); //debug
            App::debug(App.parseUrl('http://******2.localhost/mvc/App/Languages/testSmartFrame1?page=1&filter=user', {'hasGetParams': true, 'parseGetParams': true})); //debug

//            }); // head & LAB loaders end

        </script>
        //<?php
        break;
//
//    case 'App_args':
//        App::debug(App::$args); //debug
//        App::debug($_GET); //debug
//        break;
//
//    case 'loadMailerTestContacts':
//        $Contact = App::loadModel('Mailer', 'MailerContact', true);
//        $Contact->loadTestRecords();
//        break;
//
//    case 'createMailerCampaignTable':
//        $Campaign = App::loadModel('Mailer', 'MailerCampaign', true);
//        $Campaign->createTable();
//        break;
//
//
//    case 'Model_deleteOrderedItem':
//        App::loadModel('Eshop', 'EshopProductImage');
//        $ProductImage = new EshopProductImage();
//        $ProductImage->deleteOrderedItem(3, array(
//            'groupConditions' => array('run_eshop_products_id' => 2),
//        ));
//        break;
//
//    case 'Model_moveOrderedItem':
//        App::loadModel('Eshop', 'EshopProductImage');
//        $ProductImage = new EshopProductImage();
//        $ProductImage->moveOrderedItem(2, array(
//            'groupConditions' => array('run_eshop_products_id' => 2),
//            'newOrderIndex' => 12
//        ));
//        break;
//
//    case 'Model_addOrderedItem':
//        App::loadModel('Eshop', 'EshopProductImage');
//        $ProductImage = new EshopProductImage();
//        $ProductImage->addOrderedItem(array('image' => 'testiamge01', 'testname01'), array(
//            'groupConditions' => array('run_eshop_products_id' => 2),
//        )
//        break;
//
//    case 'loadEshopProductImageTable':
//        App::loadModel('Eshop', 'EshopProductImage');
//        $ProductImage = new EshopProductImage();
//        $ProductImage->createTable();
//        break;
//
//    case 'test':
//        App::debug(is_file(ROOT .  '/app/tmp/checkoutFiles/kme0if0j3k23rk6o0ar2pbjva5_2')); //debug
//        break;
//
//    case 'loadEshopOrderTable':
//        App::loadModel('Eshop', 'EshopOrder');
//        $Order = new EshopOrder();
//        $Order->createTable();
//        break;
//
//    case 'email':
//
//        $host = 'smtp.run.sk';
//        $username = '<EMAIL>';
//        $password = '4YzmYzaN';
//        $port = 25;
//        $encryption = null; // null,  'ssl', 'tls'
//        $from = '<EMAIL>';
//        $to  = '<EMAIL>';
//        //$to  = '<EMAIL>';
//        $returnPath = '<EMAIL>';
//        $subject = 'TEST';
//        $body = 'test';
//        // include SwiftMailer (cca + 19 ms)
//        App::loadVendor('App', 'swiftmailer/lib/swift_required.php');
//
//        // Create the Transport
//        $Transport = Swift_SmtpTransport::newInstance(
//            $host,
//            $port,
//            $encryption
//        )
//            ->setUsername($username)
//            ->setPassword($password)
//        ;
//
//        // Create the Mailer using your created Transport
//        $Mailer = Swift_Mailer::newInstance($Transport);
//        // Create a message
//        $Message = Swift_Message::newInstance($subject)
//            ->setSender($username)
////            ->setFrom('<EMAIL>') //552 Blocked, unknown email address in envelope-sender or From header. http://antispam.websupport.sk/?from=<EMAIL>
//            ->setFrom($from)
//            ->setReturnPath($returnPath)
//            ->setTo($to)
//            ->setBody($body, 'text/html')
//            ->addPart(__d(__FILE__, 'This is a Html message. To view it properly turn on display of Html content in your mail client'), 'text/plain')
//        ;
//        // Send it
//        try {
//            $result = $Mailer->send($Message);
//            App::debug($result, '$result'); //debug
//        }
//        catch (Throwable $e) {
//            echo 'see app/tmp/logs/debugemail.log';
//            @unlink (ROOT . DS . 'app/tmp/logs/debugemail.log');
//            App::log('debugemail', 'Mailer exception', array('var' => $e));
//        }
//        break;
//
//    case 'test':
//        $string ='abcd';
//        $array = array('A', 'B', 'C');
//
//        echo $string[1];            // b
//        echo $string['records'];    // a !!! - string expects integer index so 'records' is typecasted to int
//        isset($string['records']);  // TRUE !!!
//        echo $string[0];            // a
//        echo $array['records'];     // E_NOTICE: Undefined index: records - array expects both int and string indexes (keys) that is why 'records' is not typecasted
//        echo $array[0];             // A
//        echo (int)'records';        // 0
//        break;
//
//    case 'File_transferFromFtp':
//        File::transfer(array('ftp://grada_cz_obalky:<EMAIL>/049068.jpg'), '/app/tmp');
//        break;
//
//    case 'Validate_ftpUrl':
//        $r = Validate::url('ftp://grada_cz_obalky:<EMAIL>/049068.jpg', array('scheme' => 'ftp'));
//        App::debug($r, 'ftp://grada_cz_obalky:<EMAIL>/049068.jpg'); //debug
//        $r = Validate::url('ftp://ftp.myhost.com:21/my/path/to/file.txt', array('scheme' => 'ftp'));
//        App::debug($r, 'ftp://ftp.myhost.com:21/my/path/to/file.txt'); //debug
//        $r = Validate::url('ftp://myuser:<EMAIL>:21/my/path/to/file.txt', array('scheme' => 'ftp'));
//        App::debug($r, 'ftp://myuser:<EMAIL>:21/my/path/to/file.txt'); //debug
//        $r = Validate::url('ftp://myuser:<EMAIL>:21/my/path/to/file.txt', array('scheme' => 'http'));
//        App::debug($r, 'ftp://myuser:<EMAIL>:21/my/path/to/file.txt'); //debug
//        $r = Validate::url('ftp://myuser:<EMAIL>:21/my/path/to/file.txt', array('scheme' => 'any'));
//        App::debug($r, 'ftp://myuser:<EMAIL>:21/my/path/to/file.txt'); //debug
//        $r= File::getPathinfo('ftp://myuser:<EMAIL>:21/my/path/to/file.txt');
//        App::debug($r); //debug
//        $r= parse_url('ftp://myuser:<EMAIL>:21/my/path/to/file.txt');
//        App::debug($r); //debug
//        break;
//
//    case 'Ftp_benchmark':
//        App::loadLib('App', 'Ftp');
//        $Ftp1 = new Ftp('grada.cz', array(
////        $Ftp1 = new Ftp('************', array( // this is quicker - DNS resolving is avoided
//            'username' => 'grada_cz_obalky',
//            'password' => 'obalky',
//            'connect' => false,
//        ));
//        App::setPropertyDebugOutput('log');
//        // no cache - connection on each turn (this is simulation of case than ftp is used by some
//        // 'utility' function which is used in loop and the connection to FTP must
//        // be established on each call of function as the Ftp instance is function local variable)
//        Utility::startTimer();
//        for ($i = 0; $i < 10; $i++) {
//            $Ftp1->connect();
//            $Ftp1->get('049068.jpg', '/app/tmp', array('overwrite' => true));
//            $Ftp1->close();
//        }
//        Utility::getTimer(); // 5572.91 ms
//        // cache - connection only on the first instance creation (this is simulation of case than ftp is used by some
//        // 'utility' function which is used in loop but the connection is not established on each
//        // call of function as it is cached on the first Ftp instance creation)
//        $Ftp1->connect();
//        Utility::startTimer();
//        for ($i = 0; $i < 10; $i++) {
//            $Ftp1->get('049068.jpg', '/app/tmp', array('overwrite' => true));
//        }
//        Utility::getTimer(); // 3238.99 ms
//        $Ftp1->close();
//        break;
//
//    case 'Ftp_test1':
//        App::loadLib('App', 'Ftp');
//        $Ftp1 = new Ftp('grada.cz', array(
////        $Ftp1 = new Ftp('************', array( // this is quicker - DNS resolving is avoided
//            'username' => 'grada_cz_obalky',
//            'password' => 'obalky',
//        ));
//        $Ftp2 = new Ftp('195.168.120.162', array(
//            'username' => 'uniknihy',
//            'password' => 'k9_n1H1',
//        ));
//
//        $Ftp1->get('049068.jpg', '/app/tmp', array('overwrite' => true));
//        $Ftp2->put('/app/tmp/049068.jpg', '/', array('overwrite' => true));
//        break;
//
//    case 'Ftp_put':
//        App::loadLib('App', 'Ftp');
//        $Ftp = new Ftp('195.168.120.162', array(
//            'username' => 'uniknihy',
//            'password' => 'k9_n1H1',
//            //'ssl' => true, //ftp_put(): Illegal PORT command
//        ));
//        $Ftp->put('/app/tmp/test.txt', '/', array('overwrite' => true));
//        break;
//
//    case 'Ftp_putContents':
//        App::loadLib('App', 'Ftp');
//        $Ftp = new Ftp('195.168.120.162', array(
//            'username' => 'uniknihy',
//            'password' => 'k9_n1H1',
//            //'ssl' => true, //ftp_put(): Illegal PORT command
//        ));
//        $Ftp->putContents('Testtovací obsah', '/test2.txt', array('overwrite' => true));
//        break;
//
//    case 'Ftp_get':
//        App::loadLib('App', 'Ftp');
//        $Ftp = new Ftp('195.168.120.162', array(
//            'username' => 'uniknihy',
//            'password' => 'k9_n1H1',
//            //'ssl' => true, //ftp_put(): Illegal PORT command
//        ));
//        $Ftp->get('test.txt', '/app/tmp', array('overwrite' => true));
//        break;
//
//    case 'Ftp_getContents':
//        App::loadLib('App', 'Ftp');
//        $Ftp = new Ftp('195.168.120.162', array(
//            'username' => 'uniknihy',
//            'password' => 'k9_n1H1',
//            //'ssl' => true, //ftp_put(): Illegal PORT command
//        ));
//        $c = $Ftp->getContents('test2.txt');
//        App::debug($c); //debug
//        break;
//
//    case 'EshopProductCategory_findIdByNamesPath':
//        $ProductCategory = App::loadModel('Eshop', 'EshopProductCategory', true);
//        $result = $ProductCategory->findIdByNamesPath(array('Absinth', '80%'));
//        App::debug($result); //debug
//        break;
//
//    case 'EshopProductCategory_findTree':
//        $ProductCategory = App::loadModel('Eshop', 'EshopProductCategory', true);
//        $categories = $ProductCategory->findTree(
//            'categories',
//            array(
//                'fields' => array('EshopProductCategory.name')
//            )
//        );
//        App::debug($categories); //debug
//        break;
//
//    case 'DB_getTableStatus':
//        App::debug(DB::getTableStatus('run_regions')); //debug
//        break;
//
//    case 'testCsv':
//        App::loadLib('App', 'Csv');
//        $Csv = new Csv('/app/tmp/katalog_e-shopy_2.csv', array(
//            'delimiter'=>';',
//            'enclosure'=>'"',
//            'encoding' => 'CP1250',
//            'decimalPoint' => ',',
//            'thousandsSeparator' => '.',
//            'hasHeaders' => true,
//            'recordFields' => array(
//                'price' => array('column' => 2, 'convertType' => 'float'),
//                'ean' => array('column' => 0),
//                'VYDAVATEĽSTVO:' => array('key' => 'publisher', 'convertEncoding' => true),
//                'AUTOR (Meno Priezvisko)' => array('key' => 'author', 'convertEncoding' => true),
//                'POČET STRÁN:' => true,
//                13 => array('convertEncoding' => true),
//            )
//        ));
//        App::debug($Csv->getPropertyHeaders(), 'getHeaders'); //debug
//        App::debug($Csv->getPropertyInfo(), 'getInfo'); //debug
//        App::debug($Csv->readRecord(), 'readRecord'); //debug
//        App::debug($Csv->getRecordsCount(), 'getRecordsCount'); //debug
//        App::debug($Csv->getRowsCount(), 'getRowsCount'); //debug
//        App::debug($Csv->readRecord(), 'readRecord'); //debug
//        App::debug($Csv->getPropertyRecordOffset(), 'getRecordOffset'); //debug
//        App::debug($Csv->readRecord(), 'readRecord'); //debug
//        App::debug($Csv->getPropertyRecordOffset(), 'getRecordOffset'); //debug
//        App::debug($Csv->readRecord(), 'readRecord'); //debug
//        App::debug($Csv->getPropertyRecordOffset(), 'getRecordOffset'); //debug
//        App::debug($Csv->readRecord(), 'readRecord'); //debug
//        App::debug($Csv->getPropertyRecordOffset(), 'getRecordOffset'); //debug
//        App::debug($Csv->setRecordOffset(0), 'setRecordOffset'); //debug
//        App::debug($Csv->getPropertyRecordOffset(), 'getRecordOffset'); //debug
//        App::debug($Csv->readRecord(), 'readRecord'); //debug
//        App::debug($Csv->getPropertyRecordOffset(), 'getRecordOffset'); //debug
//        //debug($Csv->normalize(), 'normalize'); //debug
//        App::debug($Csv->readRecord(), 'readRecord'); //debug
//        App::debug($Csv->getPropertyRecordOffset(), 'getRecordOffset'); //debug
//        App::debug($Csv->setRecordOffset(4), 'setRecordOffset'); //debug
//        App::debug($Csv->readRecord(), 'readRecord'); //debug
//        App::debug($Csv->getPropertyInfo(), 'getInfo'); //debug
//        break;
//
//    case 'testFileFields':
//        $Test = App::loadModel('App', 'Test', true);
////        $Test->createTable();
//        ?>
        <form action="" method="post" enctype="multipart/form-data">
            id:
            <input type="text" name="data[id]"/><br />
            name:
            <input type="text" name="data[name]"/><br />
            image_a:
            <input type="file" name="data[image_a]"/><br />
            image_b:
            <input type="file" name="data[image_b]"/><br />
            <input type="submit" name="data[action]" value="Save"/>
            <input type="submit" name="data[action]" value="Delete"/>
        </form>
        //<?php
//        App::debug(App::$data); //debug
//        if (App::$data) {
//            if (
//                App::$data['action'] == 'Delete'
//                && App::$data['id']
//            ) {
//                $Test->deleteBy('id', App::$data['id']);
//            }
//            elseif (App::$data['action'] == 'Save') {
//                $Test->save(App::$data);
//            }
//        }
//
//        break;
//
//    case 'createMyTable':
//        $schema = array(
//            'id' => array('type' => 'int', 'index' => 'key', 'autoIncrement' => true),
//            'location' => array('type' => 'varchar', 'default' => null, 'NULL = Honolulu, Hawai`i - Ala Moana'),
//            'number' => array('type' => 'char', 'length' => 5),
//            'services' => array('type' => 'text', 'comment' => 'Comma separated list of service pids'),
//            'service_labels' => array('type' => 'text'),
//            'start_date' => array('type' => 'date'),
//            'months' => array('type' => 'tinyint'),
//            'total' => array('type' => 'float'),
//            'firstname' => array('type' => 'varchar'),
//            'lastname' => array('type' => 'varchar'),
//            'company' => array('type' => 'varchar'),
//            'industry' => array('type' => 'varchar', 'default' => null),
//            'phone' => array('type' => 'varchar', 'length' => 20),
//            'mobile' => array('type' => 'varchar', 'length' => 20, 'default' => null),
//            'email' => array('type' => 'varchar', 'length' => 50),
//            'address' => array('type' => 'text'),
//            'state' => array('type' => 'varchar'),
//            'country' => array('type' => 'char', 'length' => 2),
//            'postcode' => array('type' => 'varchar', 'length' => 10, 'default' => null),
//            'terms_and_conditions_accepted' => array('type' => 'bool', 'default' => 0),
//            'payment_status' => array('type' => 'varchar', 'default' => null, 'comment' => 'NULL - pending, 0 - paypal success result, 1, 2, 3, ... - paypal error results'),
//            'new' => array('type' => 'bool', 'default' => 1),
//    //        'new_signup_email_success' => array('type' =>'bool', 'default' => 1, 'comment' => 'Has been the new signup email successfully sent?'),
//            'created' => array('type' => 'datetime', 'default' => null),
//            'modified' => array('type' => 'datetime', 'default' => null),
//        );
//        DB::createTable('run_proworks_office_signups', $schema);
//        break;
//
//    case 'App_getValue':
//        App::debug(Sanitize::value($noVar['nokey1']['nokey2'], 'defaultValue')); //debug
//        break;
//
//    case 'File_download':
//        File::download(
//            array('http://www.executivecentre.com.hk/static/img/logo-footer.png'),
//            '/userfiles/x'
//        );
//        File::download(
//            array('http://www.executivecentre.com.hk/static/img/logo-footer.png'),
//            '/userfiles/x',
//            array('name' => 'my_picture.jpg')
//        );
//        break;
//
//    case 'File_copy':
//        File::copy(
//            array('/todo.txt'),
//            '/userfiles/x'
//        );
//        File::copy(
//            array('/todo.txt'),
//            '/userfiles/x',
//            array('name' => 'my_file.txt')
//        );
//        break;
//
//    case 'Model_getUniqueFieldValue':
//        $User = App::loadModel('App', 'User', true);
//        $v = $User->getUniqueFieldValue('email', '<EMAIL>');
//        App::debug($v); //debug
//        $v = $User->getUniqueFieldValue('email', '<EMAIL>', array(
//            'conditions' => array(
//                'first_name' => 'Web',
//                'OR',
//                'last_name' => 'Admin',
//            )
//        ));
//        App::debug($v); //debug
//        $q = DB::getSelectQuery('table_a', array(
//            'conditions' => array(
//                'field0' => 2,
//                array(),
//                'field1' => true,
//                'OR',
//                array(),
//                'OR',
//                'field2' => true,
//
//            )
//        ));
//        App::debug($q); //debug
//        $q = DB::getSelectQuery('table_a', array(
//            'conditions' => array(
//                'field0' => 2,
//                array(),
//                array(
//                    'field1' => true,
//                    'OR',
//                    array(),
//                    'OR',
//                    'field2' => true,
//                )
//
//            )
//        ));
//        App::debug($q); //debug
//        break;
//
//    case 'HTTP_ACCEPT_LANGUAGE':
//        App::debug($_SERVER['HTTP_ACCEPT_LANGUAGE']); //debug;
//        break;
//
//    case 'initUndefinedVariable':
//        // using @
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            $a['key'] = @$b['unexisting'];
//        }
//        Utility::getTimer(); // 0.43 ms
//        // using Sanitize::value()
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            $a['key'] = Sanitize::value($b['unexisting']);
//        }
//        Utility::getTimer(); // 0.46 ms
//        // using Sanitize::value()
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            $a['key'] = Sanitize::value($b['unexisting']);
//        }
//        Utility::getTimer(); // 0.46 ms
//        // using isset() ? :
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            $a['key'] = isset($b['unexisting']) ? $b['unexisting'] : null;
//        }
//        Utility::getTimer(); // 0.46 ms
//        // using !empty() ? :
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            $a['key'] = !empty($b['unexisting']) ? $b['unexisting'] : null;
//        }
//        Utility::getTimer(); // 0.46 ms
//        break;
//
//    case 'typecastEmptyArray':
//        App::debug((array)null, '(array)null'); //debug  array()
//        App::debug((array)0, '(array)0'); //debug  array('0'=>'0')
//        App::debug((array)'', '(array)""'); //debug  array('0'=>'')
//        break;
//
//    case 'array_compare':
//        $a1 = array(
//            'a',
//            2,
//        );
//        $a2 = array(
//            'a',
//            '2',
//        );
//        $a3 = array(
//            'x' => 'a',
//            2,
//        );
//        $a4 = array(
//            'y' => 'a',
//            2,
//        );
//        $a5 = array(
//            'a',
//            2,
//            'x' => array('6', 'z' => array(3)),
//        );
//        $a6 = array(
//            'a',
//            2,
//            'x' => array('6', 'z' => array(3)),
//        );
//        $a7 = array(
//            'a',
//            2,
//            'x' => array('6', 'z' => array(5)),
//        );
//        $a8 = array(
//            'a',
//            'x' => array('z' => array(3), '6'),
//            2,
//        );
//
//        App::debug($a1 == $a2, '$a1 == $a2'); //debug 1
//        App::debug($a1 == $a3, '$a1 == $a3'); //debug 0
//        App::debug($a3 == $a4, '$a3 == $a4'); //debug 0
//        App::debug($a5 == $a6, '$a5 == $a6'); //debug 1
//        App::debug($a5 == $a7, '$a5 == $a7'); //debug 0
//        App::debug($a5 == $a8, '$a5 == $a8'); //debug 1
//
//        App::debug($a1 === $a1, '$a1 === $a1'); //debug 1
//        App::debug($a1 === $a2, '$a1 === $a2'); //debug 0
//        App::debug($a1 === $a3, '$a1 === $a3'); //debug 0
//        App::debug($a3 === $a4, '$a3 === $a4'); //debug 0
//        App::debug($a5 === $a6, '$a5 === $a6'); //debug 1
//        App::debug($a5 === $a7, '$a5 === $a7'); //debug 0
//        App::debug($a5 === $a8, '$a5 === $a8'); //debug 0 ! different order od array items
//
//        App::debug(array_diff($a1, $a2), 'array_diff($a1, $a2)'); //debug array()
//        App::debug(array_diff($a1, $a3), 'array_diff($a1, $a3)'); //debug array()
//        App::debug(array_diff($a3, $a4), 'array_diff($a3, $a4)'); //debug array()
//        App::debug(array_diff($a5, $a6), 'array_diff($a5, $a6)'); //debug array()
//        App::debug(array_diff($a5, $a7), 'array_diff($a5, $a7)'); //debug array() !!! NOK
//
//        App::debug(array_diff_assoc($a1, $a2), 'array_diff_assoc($a1, $a2)'); //debug array()
//        App::debug(array_diff_assoc($a1, $a3), 'array_diff_assoc($a1, $a3)'); //debug array('0'=>'a', '1'=>'2')
//        App::debug(array_diff_assoc($a3, $a4), 'array_diff_assoc($a3, $a4)'); //debug array('x'=>'a')
//        App::debug(array_diff_assoc($a5, $a6), 'array_diff_assoc($a5, $a6)'); //debug array()
//        App::debug(array_diff_assoc($a5, $a7), 'array_diff_assoc($a5, $a7)'); //debug array() !!! NOK
//        break;
//
//    case 'Model_findList_acumulated':
//        $Author = App::loadModel('Eshop', 'EshopAuthor', true);
//        $authors = $Author->findList(array(
//            'key' => 'EshopProductAuthor.run_eshop_products_id',
//            'fields' => array(
//                'EshopAuthor.id',
//                'EshopAuthor.name',
//                'EshopAuthor.slug',
//            ),
//            'conditions' => array(
//                'EshopProductAuthor.run_eshop_products_id' => 6,
//            ),
//            'joins' => array(
//                array(
//                    'model' => 'EshopProductAuthor',
//                    'type' => 'left',
//                ),
//            ),
//            'accumulate' => true,
//        ));
//        App::debug($authors); //debug
//        break;
//
//    case 'Model_find_separated':
//        $Product = App::loadModel('Eshop', 'EshopProduct', true);
//        $products = $Product->find(array(
//            'fields' => array(
//                'EshopProduct.name',
//                'EshopProduct.slug',
//                'IF(
//                    `EshopProduct`.`discount_price` IS NOT NULL AND DATEDIFF(NOW(),`EshopProduct`.`discount_from`) >= 0 AND DATEDIFF(NOW(), `EshopProduct`.`discount_to`) <=0,
//                    `EshopProduct`.`discount_price`,
//                    `EshopProduct`.`price`
//                ) AS `EshopProduct.price`'
//            ),
//            'conditions' => array('EshopProduct.name' => 'Hruškovica'),
//            'separate' => true,
//            'literals' => array(
//                'fields' =>
//                'IF(
//                    `EshopProduct`.`discount_price` IS NOT NULL AND DATEDIFF(NOW(),`EshopProduct`.`discount_from`) >= 0 AND DATEDIFF(NOW(), `EshopProduct`.`discount_to`) <=0,
//                    `EshopProduct`.`discount_price`,
//                    `EshopProduct`.`price`
//                ) AS `EshopProduct.price`'
//            )
//        ));
//        App::debug($products); //debug
//        break;
//
//    case 'instanceof':
//        $User = App::loadModel('App', 'User', true);
//        App::debug($User instanceof ModuleObject); //debug TRUE
//        // not existing class
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            $User instanceof NoClass;
//        }
//        Utility::getTimer(); // 0.43 ms
//        // existing class
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            $User instanceof ModuleObject;
//        }
//        Utility::getTimer(); // 0.46 ms
//        // not an object
//        $User = 2;
//        App::debug($User instanceof ModuleObject); //debug FALSE
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            $User instanceof ModuleObject;
//        }
//        Utility::getTimer(); // 0.46 ms
//        break;
//
//    case 'App_loadModel':
//        // without instance
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            App::loadModel('Eshop', 'EshopManufacturer');
//        }
//        Utility::getTimer(); // 43 - 55 ms without class_exists, 8.5 ms with class_exists
//        // with instance
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            App::loadModel('Eshop', 'EshopProductType', true);
//        }
//        Utility::getTimer(); // 186 - 216 ms without class_exists, 135 ms with class_exists, 4.4 after refactoring to retrun singleton bor each module model
//
//        $Product1 = App::loadModel('Eshop', 'EshopProduct', true);
//        $Product1->validate(array('code' => '42'));
//        App::debug($Product1->getErrors(), '$Product1->getErrors()'); //debug
//
//        $Product2 = App::loadModel('Eshop', 'EshopProduct', true);
//        App::debug($Product2->getErrors(), '$Product2->getErrors()'); //debug - the same array as the one above
//
//        $Product3 = new EshopProduct();
//        App::debug($Product3->getErrors(), '$Product3->getErrors()'); //debug - empty array
//
//
////        // using Model::get() (with instance)
////        Utility::startTimer();
////        for ($i = 0; $i < 1000; $i++) {
////            Model::get('Eshop', 'EshopAuthor');
////        }
////        Utility::getTimer(); // 3.5 ms with class_exists
////        // using Model::get() with ModuleObject as $module (with instance)
////        $Product = Model::get('Eshop', 'EshopProduct');
////        App::debug($Product->getPropertyModule()); //debug
////        App::debug($this->module); //debug
////        Utility::startTimer();
////        for ($i = 0; $i < 1000; $i++) {
////            Model::get($Product, 'EshopOrder');
////        }
////        Utility::getTimer(); // 3.5 ms with class_exists
//        break;
//
//    case 'class_exists':
//        // not existing class
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            class_exists('NoClass');
//        }
//        Utility::getTimer(); // 2.8 ms
//        // existing class
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            class_exists('App');
//        }
//        Utility::getTimer(); // 1.4 ms
//        break;
//
//    case 'Model_getModel':
//        $Product = App::loadModel('Eshop', 'EshopProduct', true);
//        $Product2 = $Product;
//        $Product = clone $Product2; // clone
//        break;
//
//    case 'Model_getUniqueFieldValue':
//        $Product = App::loadModel('Eshop', 'EshopProduct', true);
//        App::debug($Product->getUniqueFieldValue('slug', 'hruskovica')); //debug
//        $Content = App::loadModel('App', 'WebContent', true);
//        App::debug($Content->getUniqueFieldValue('WebContent.url', 'wellness', array(
//            'conditions' => array(
//                $this->name . '.lang' => 'sk',
//            ),
//        ))); //debug
//        break;
//
//    case 'filefieldoptions':
//        App::loadModel('Eshop', 'EshopProduct');
//        $Product = new EshopProduct();
//        App::debug($Product->getFileFieldOptions('image')); //debug
//        App::debug($Product->getFileFieldVariants('image')); //debug
//        App::debug($Product->getFileFieldPaths('image')); //debug
//        break;
//
//    case 'unexistingClass':
//        try {
//            $x = new XYZ();
//        }
//        catch (Throwable $e) {
//            throw new Exception('Undefined class name');
//        }
//        break;
//
//    case 'js_undefined':
//        ?>
        <script type="text/javascript" >
            // it is not possible to check undefined in one step (st like empty(a['undefined01']['undefined02'])
//            console.log(typeof window.undefinedProperty.undefinedPreperty2); //debug TypeError: window.undefinedProperty is undefined
            console.log(typeof window['undefinedProperty']['undefinedPreperty2']); //debug TypeError: window.undefinedProperty is undefined
        </script>
        //<?php
//        break;
//
//    case 'File_transfer':
//        ?>
        <form method="post" enctype="multipart/form-data">
            <input type="file" name="data[test_file]"/>
            <input type="submit" value="Transfer"/>
        </form>
        //<?php
//        if ($this->data) {
//            App::debug($this->data); //debug
//            $result = File::transfer($this->data['test_file'], 'userfiles/test', array('name' => 'my_test_file'));
//            App::debug($result); //debug
//        }
//        break;
//
//    case 'bg_process_table':
//        $schema = array(
//
//        // process properties
//        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
//        'processId' => array('type' => 'varchar', 'index' => 'unique'),
//        'url' => array('type' => 'text', 'default' => null),
//        'method' => array('type' => 'enum', 'values' => array('get', 'post'), 'default' => null),
//        'params' => array('type' => 'text', 'default' => null, 'comment' => 'Json encoded array of params to be passed to process url'),
//        'launches' => array('type' => 'int', 'default' => 0, 'comment' => 'Number of process (re)launches'),
//        'summary' => array('type' => 'text', 'default' => null, 'comment' => 'Actual or final process state can be summarized here'),
//        // process html output
//        'output' => array('type' => 'text', 'default' => null, 'Process html output'),
//        // process times
//        'startMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'Microtime when the process was created'),
//        'startDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime when the process was created'),
//        'touchMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'A kind of live-man control - microtime when the process was touched last time'),
//        'touchDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'A kind of live-man control - datetime when the process was touched last time'),
//        'pauseMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'Microtime when the process was paused'),
//        'pauseDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'Microtime when the process was paused'),
//        'endMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'Microtime when the process was finished'),
//        'endDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'Microtime when the process was finished'),
//        'loopMicrotimeReserve' => array('type' => 'int', 'default' => 5000),
//        'loopStartMicrotime' => array('type' => 'double', 'default' => null),
//        'loopMaxMicrotime' => array('type' => 'double', 'default' => null),
//        // process flags
//        'isLaunched' => array('type' => 'bool', 'default' => 0),
//        'isPaused' => array('type' => 'bool', 'default' => 0),
//        'isCanceled' => array('type' => 'bool', 'default' => 0),
//        'isFinished' => array('type' => 'bool', 'default' => 0),
//        'isDeleted' => array('type' => 'bool', 'default' => 0),
//        // process progress
//        'progress' => array('type' => 'text', 'default' => null, 'comment' => 'Json encoded array of process progress params. Content is up to user. Should be relatively small'),
//        'progressUpdateMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'Microtime of last data update'),
//        'progressUpdateDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime of last data update'),
//        // process data
//        'data' => array('type' => 'longtext', 'default' => null, 'comment' => 'Json encoded array of process data. Content is up to user. Can be huge.'),
//        'dataUpdateMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'Microtime of last data update'),
//        'dataUpdateDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime of last data update'),
//
//        );
//        DB::createTable('bg_processes', $schema);
//        $rawMicrotime = microtime(true);
//        $microtime = round(1000 * ($rawMicrotime), 2);
//        App::debug($rawMicrotime); //debug
//        App::debug($microtime); //debug
//        DB::insert('bg_processes', array(
//            'processId' => uniqid('my_process'),
//            'url' => 'localhost',
//            'method' => 'post',
//            'params' => null,
//            'startMicrotime' => $rawMicrotime,
//            'updateMicrotime' => $microtime,
//            'endMicrotime' => $rawMicrotime,
//            'loopStartMicrotime' => $microtime,
//        ));
//        break;
//
//    case 'pathinfo':
//        App::debug(pathinfo('www/var/phpinfo.php')); //debug
//        App::debug(pathinfo('www/var/phpinfo')); //debug
//        App::debug(pathinfo('www@/var/phpinfo')); //debug
//        $pathinfo = pathinfo('www/var/phpinfo.');
//        App::debug($pathinfo['extension'] === null, '$pathinfo[\'extension\'] === null');
//        App::debug($pathinfo['extension'] === '','$pathinfo[\'extension\'] === \'\'');
//        break;
//
//    case 'uniqid':
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            uniqid('transfer_', true);
//        }
//        Utility::getTimer(); // 2.56 ms
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            uniqid('transfer_');
//        }
//        Utility::getTimer(); // 77 ms
//        App::debug(uniqid('transfer_', true)); //debug
//        App::debug(uniqid('transfer_')); //debug
//        App::debug(uniqid('transfer_')); //debug
//        App::debug(uniqid('transfer_')); //debug
//        App::debug(uniqid('transfer_')); //debug
//        App::debug(uniqid('transfer_')); //debug
//        break;
//
//    case 'Validate_path':
//        App::debug(Validate::path('/var/www/******/app')); //debug TRUE
//        App::debug(Validate::path('/var/www/******/')); //debug TRUE
//        App::debug(Validate::path('/var/www/******')); //debug TRUE
//        App::debug(Validate::path('/var/www/vydavatel/')); //debug FALSE
//        App::debug(Validate::path('/var/www/******/app/x')); //debug TRUE
//        App::debug(Validate::path('/var/www/******/app/x', array('exists' => true))); //debug FALSE
//        break;
//
//    case 'is_file':
//        App::debug(is_file('/var/www/phpinfo.php')); //debug TRUE
//        App::debug(is_file('/var/www/no_phpinfo.php')); //debug FALSE
//        App::debug(is_file('/var/www/')); //debug FALSE
//        break;
//
//    case 'Validate_url':
//        App::debug(Validate::url('http://vydavatel.sk/produkty/1/page:3?test=d')); //debug
//        App::debug(Validate::url('https://vydavatel.sk/produkty/1/page:3?test=d')); //debug
//        App::debug(Validate::url('file://vydavatel.sk/produkty/1/page:3?test=d')); //debug TRUE !!!
//        App::debug(Validate::url('/var/www/vydavatel/app/tmp/test.php')); //debug FALSE !!
//        App::debug(Validate::url('/var/www/vydavatel/app/tmp/test.php', array('strict' => false))); //debug FALSE !!
//        App::debug(Validate::url('file:///var/www/vydavatel/app/tmp/test.php')); //debug FALSE !!
//        App::debug(Validate::url('///var/www/vydavatel/app/tmp/test.php', array('strict' => false))); //debug FALSE !!
//        App::debug(Validate::url('http://vydavatel.sk', array('exists' => false))); //debug
//        App::debug(Validate::url('www.vydavatel.sk', array('strict' => false, 'exists' => false))); //debug
//        break;
//
//    case 'parse_url':
//        App::debug(parse_url('http://vydavatel.sk/produkty/1/page:3?test=d')); //debug
//        App::debug(parse_url('https://vydavatel.sk/produkty/1/page:3?test=d')); //debug
//        App::debug(parse_url('/var/www/vydavatel/app/tmp/test.php')); //debug
//        break;
//
//    case 'fileUpload':
//        App::debug($_FILES); //debug
//        App::debug(App::$data); //debug
//        ?>
<form enctype="multipart/form-data" action="/_debug/fileUpload" method="POST">
    <input type="text" name="data[test]" value="test input value" />
    Send this file: <input name="data[MyModel][myfile1]" type="file" />
    Send this file: <input name="data[MyModel][myfile2][]" type="file" />
    Send this file: <input name="data[MyModel][myfile2][]" type="file" />
    Send this file: <input name="data[myfile3]" type="file" />
    <input type="submit" value="Send File" />
</form>
        //<?php
//        break;
//
//    case 'loadEshopTables':
//        App::loadModel('Eshop', 'EshopProduct');
//        $Product = new EshopProduct();
//        $Product->createTable();
//
//        App::loadModel('Eshop', 'EshopProductType');
//        $ProductType = new EshopProductType();
//        $ProductType->createTable();
//
//        App::loadModel('Eshop', 'EshopProductTypeAttribute');
//        $ProductTypeAttribute = new EshopProductTypeAttribute();
//        $ProductTypeAttribute->createTable();
//
//        App::loadModel('Eshop', 'EshopProductAttribute');
//        $ProductAttribute = new EshopProductAttribute();
//        $ProductAttribute->createTable();
//
//        App::loadModel('Eshop', 'EshopRelatedProduct');
//        $RelatedProduct = new EshopRelatedProduct();
//        $RelatedProduct->createTable();
//
//        App::loadModel('Eshop', 'EshopAccessoryProduct');
//        $AccessoryProduct = new EshopAccessoryProduct();
//        $AccessoryProduct->createTable();
//
//        App::loadModel('Eshop', 'EshopProductGroup');
//        $ProductGroup = new EshopProductGroup();
//        $ProductGroup->createTable();
//
//        App::loadModel('Eshop', 'EshopProductGroupProduct');
//        $ProductGroupProduct = new EshopProductGroupProduct();
//        $ProductGroupProduct->createTable();
//
//        App::loadModel('Eshop', 'EshopProductCategory');
//        $ProductCategory = new EshopProductCategory();
//        $ProductCategory->createTable();
//
//        App::loadModel('Eshop', 'EshopProductCategoryProduct');
//        $ProductCategoryProduct = new EshopProductCategoryProduct();
//        $ProductCategoryProduct->createTable();
//
//        App::loadModel('Eshop', 'EshopManufacturer');
//        $Manufacturer = new EshopManufacturer();
//        $Manufacturer->createTable();
//
//        App::loadModel('Eshop', 'EshopManufacturerRange');
//        $ManufacturerRange = new EshopManufacturerRange();
//        $ManufacturerRange->createTable();
//
//        App::loadModel('Eshop', 'EshopAuthor');
//        $Author = new EshopAuthor();
//        $Author->createTable();
//
//        App::loadModel('Eshop', 'EshopProductAuthor');
//        $ProductAuthor = new EshopProductAuthor();
//        $ProductAuthor->createTable();
//
//        App::loadModel('Eshop', 'EshopOrder');
//        $Order = new EshopOrder();
//        $Order->createTable();
//
//        App::loadModel('Eshop', 'EshopOrderProduct');
//        $OrderProduct = new EshopOrderProduct();
//        $OrderProduct->createTable();
//
//        App::loadModel('Eshop', 'EshopShipmentMethod');
//        $ShipmentMethod = new EshopShipmentMethod();
//        $ShipmentMethod->createTable();
//
//        App::loadModel('Eshop', 'EshopShipmentPaymentMethod');
//        $ShipmentPaymentMethod = new EshopShipmentPaymentMethod();
//        $ShipmentPaymentMethod->createTable();
//
//        App::loadModel('Eshop', 'EshopProfile');
//        $Profile = new EshopProfile();
//        $Profile->createTable();
//
//        App::loadModel('Eshop', 'EshopWishlist');
//        $Wishlist = new EshopWishlist();
//        $Wishlist->createTable();
//
//        break;
//
//    case 'compareNumberStrings':
//        App::debug('9' > '23'); //debug FALSE - in Javascript this gives TRUE!!!
//        App::debug('9' < '23'); //debug TRUE  - in Javascript this gives FALSE!!!
//        App::debug('c' > 'a'); //debug TRUE
//        App::debug('cx' > 'ab'); //debug TRUE
//        break;
//
//    case 'App_resolveUrl':
//        $r = App::getUrl('myslug');
//        App::debug($r); //debug
//        $r = App::getUrl(array(
//            'locator' => 'myslug',
//            'absolute' => true,
//        ));
//        App::debug($r); //debug
//        $r = App::getUrl(array(
//            'locator' => 'myslug',
//            'lang' => 'sk',
//            'absolute' => true,
//        ));
//        App::debug($r); //debug
//        $r = App::getUrl(array(
//            'locator' => 'http://mysite.sk',
//            'lang' => 'sk',
//            'absolute' => true,
//        ));
//        App::debug($r); //debug
//        $r = App::getUrl(array(
//            'controller' => 'Users',
//            'action' => 'index',
//        ));
//        App::debug($r); //debug
//        $r = App::getUrl(
//            array(
//                'controller' => 'Users',
//                'action' => 'index',
//                'params' => array(
//                    25,
//                    'my-param',
//                    'page' => 5,
//                ),
//                'get' => array(
//                    'id' => 7,
//                    'name' => 'Pištík Lajčík',
//                )
//            )
//        );
//        App::debug($r); //debug
//        $r = App::getUrl(
//            array(
//                'controller' => 'Users',
//                'action' => 'index',
//                'rawurlencode' => true,
//                'params' => array(
//                    25,
//                    'my-param',
//                    'page' => 5,
//                ),
//                'get' => array(
//                    'id' => 7,
//                    'name' => 'Pištík Lajčík',
//                )
//            )
//        );
//        App::debug($r); //debug
//        break;
//
//    case 'is_int':
//        App::debug(is_int(2), "is_int(2)"); //debug
//        App::debug(is_int('2'), "is_int('2')"); //debug
//        $a = array( 1 => 'intkey', '2' => 'stringkey');
//        foreach ($a as $k => $v) {
//            App::debug(is_int($k), "$v: is_int($k)"); //debug !!! key created like '2' => ... is changed to int!!!
//        }
//        break;
//
//    case 'DB_getSelectQuery':
//        $q = DB::getSelectQuery('table_a', array(
//            'fields' => array('url AS slug', 'name'),
//            'conditions' => array(
//                "table_b.pid" => 'test',
//                "table_b.lang" => 'sk',
//                'table_b.url IS NULL',
//                'table_b.url' => null,
//                'table_b.url' => array(1, 2, 3),
//
//            ),
//            'joins' => array(
//                array(
//                    'table' => 'table_b',
//                    'type' => 'left',
//                    'conditions' => array(
//                        "table_b.id = table_a.table_b_id",
//                        "table_b.lang = 'sk'",
//                        "table_b.lang = sk",
//                        "table_b.lang = 0",
//                    ),
//                )
//            ),
//            'first' => true,
//        ));
//        App::debug($q); //debug
//        break;
//
//    case 'App_getContentLocatorByPid':
//        App::debug(App::getContentLocatorByPid('accommodation')); //debug
//        break;
//
//    case 'duplicitTransaction':
//        DB::rollbackTransaction();
//        //DB::commitTransaction();
//        //DB::insert('unexisting_table', array('a' => 1));
//        //DB::startTransaction();
//        break;
//
//    case 'getExtTreeMenuNodeChilds':
//        App::loadModel('App', 'WebContent');
//        $WebContent = new WebContent();
//        $r = $WebContent->getExtTreeMenuNodeChilds(1, 'sk');
//        App::debug($r); //debug
//        break;
//
//    case 'stringAsArray':
//        $a = 'asdfghj';
//        App::debug($a[2]); //debug
//
//        $x = 0;
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            if (substr($a, 0, 1) == 'a') {
//                $x++;
//            }
//        }
//        Utility::getTimer(); // 1.7 ms
//
//        $x = 0;
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            if ($a[0] == 'a') {
//                $x++;
//            }
//        }
//        Utility::getTimer(); // 0.7 ms
//
//        $x = 0;
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            if ($a == 'asdfghj') {
//                $x++;
//            }
//        }
//        Utility::getTimer(); // 0.6 ms
//
//        $a = true;
//        $x = 0;
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            if ($a) {
//                $x++;
//            }
//        }
//        Utility::getTimer(); // 0.55 ms
//        break;
//
//    case 'DB_getTables':
//        $r = DB::getTables();
//        App::debug($r); //debug
//        break;
//
//    case 'DB_getFields':
//        $r = DB::getFields('run_mailer_campaigns');
//        App::debug($r); //debug
//        $r = DB::getFields('run_mailer_campaigns', 'format');
//        App::debug($r); //debug
//        break;
//
//    case 'nonstrictComparison':
//        App::debug(0 == 'default', "0 == 'default'"); //debug TRUE !!!
//        App::debug(0 == 'nimportque', "0 == 'nimportque'"); //debug TRUE !!!
//        App::debug(1 == 'nimportque', "1 == 'nimportque'"); //debug
//        App::debug(true == 'nimportque', "TRUE == 'nimportque'"); //debug
//        App::debug((int)'nimportque', "(int)'nimportque'"); //debug 0
//        App::debug((bool)'nimportque', "(bool)'nimportque'"); //debug TRUE
//        break;
//
//    case 'emptyValues':
//        App::debug(empty($a), "not defined \$a"); //debug
//
//        $a = '';
//        App::debug(empty($a), "''"); //debug
//
//        $a = false;
//        App::debug(empty($a), "FALSE"); //debug
//
//        $a = array();
//        App::debug(empty($a), "array()"); //debug
//
//        $a = 0;
//        App::debug(empty($a), "0"); //debug
//
//        $a = '0';
//        App::debug(empty($a), "'0'"); //debug
//
//        $a = 0.00;
//        App::debug(empty($a), "0.00"); //debug
//
//        $a = '0.00';
//        App::debug(empty($a), "'0.00'"); //debug FALSE
//
//        $a = (float)'0.00';
//        App::debug(empty($a), "(float)'0.00'"); //debug
//
//        $a = '0000-00-00';
//        App::debug(empty($a), "'0000-00-00'"); //debug FALSE
//
//        $a = '0000-00-00 00:00:00';
//        App::debug(empty($a), "'0000-00-00 00:00:00'"); //debug FASLE
//
//        break;
//
//    case 'composed_sql_query':
//        // there are 2 queries here: ALTER TABLE..., ADD ...
//        //DB::query('ALTER TABLE `test` ADD `testb` INT NULL AFTER `id`, ADD UNIQUE (`testb`);'); // OK, WORKS :)
//        //DB::query('ALTER TABLE  `test` ADD  `id` INT NOT NULL AUTO_INCREMENT FIRST, ADD PRIMARY KEY ( `id` );'); //OOO, WORKS TOO !)
//        break;
//
//    case 'PDO':
//        //require_once ROOT . DS . 'app' . DS . 'config' . DS . 'database.php';
//        // connect
//        //$dsn = 'mysql:dbname=******;host=localhost;user=******;password=******';
//        $dsn = 'mysql:dbname=******;host=localhost;user=root;password=root';
//        try {
//            $PDO = new PDO($dsn);
//            App::debug("PDO connection has been succesfully created"); //debug
//            // disconnect
//            $PDO = null;
//            App::debug('PDO connection has been succesfully closed'); //debug
//        }
//        catch (PDOException $e) {
//            App::debug('PDO connection error: ' . $e->getMessage()); //debug
//        }
//        break;
//
//    case 'Model_find':
//        App::loadModel('App', 'WebContent');
//        $WebContent = new WebContent();
//        $r = $WebContent->findParentsInTree(104, array(
//        ));
//        App::debug($r); //debug
//        $r = $WebContent->findParentsInTree(array('path' => '-1-101-'), array(
//        ));
//        App::debug($r); //debug
//        $r = $WebContent->findTree(1, array(
//            'fields' => 'name',
//            //'depth' => 3,
//        ));
//        App::debug($r); //debug
//        $r = $WebContent->findInTree(1, array(
//            'fields' => 'name',
//        ));
//        App::debug($r); //debug
//        $r = $WebContent->findCount(array(
//            'conditions' => array(
//                'parent_id' => 1,
//            ),
//            'fields' => 'name',
//        ));
//        App::debug($r); //debug
//        break;
//
//    case 'arrayAssign':
//        $a['x'][] = 2; // works :)
//        App::debug($a); //debug
//        break;
//
//    case 'js_class':
//        ?>
        <script type="text/javascript" >
        var i;
        // try if you can define function vith the same name as variable
        var a = 1;
        console.log('typeof a = 1: ' + typeof a); //debug: number

        function a() {
            console.log('here I am in a function'); //debug
        }
        console.log('typeof a(): ' + typeof a); //debug: number

        //a(); // error - Property 'a' of object [object Window] is not a function
        //RESULT: you cannot define function with the same name as variable

        // make it invesely
        function b() {
            console.log('here I am in b function'); //debug
        }
        console.log('typeof b(): ' + typeof b); //debug: function

        var b = 1;
        console.log('typeof b = 1: ' + typeof b); //debug: number


        //b(); // error (Property 'b' of object [object Window] is not a function )
        //RESULT: you cannot define function with the same name as variable

        function c() {
            console.log('here I am in c function'); //debug
        }
        console.log(c); //debug
        for (i in c) {
            console.log(i);
            console.log(c[i]);
        }

        var d = new Object();
        console.log(d); //debug
        console.log(d.__proto__); //debug
        for (i in d) {
            console.log(i);
            console.log(d[i]);
        }
        for (i in d.__proto__) {
            console.log(i);
            console.log(d.__proto__[i]);
        }

        var e = {a:1,b:2};
        console.log(e); //debug
        for (i in e) {
            console.log(i);
            console.log(e[i]);
        }

        // CLASS CREATION
        // #1
        function MyClass () {
            console.log('here I am in constructor of MyClass'); //debug
        }
        MyClass.prototype = {
            constructor: MyClass,
            myMethod: function() {
                console.log('here I am in MyClass.myMethod()'); //debug
                console.log(this.myProp); //debug
            },
            myProp: 10
        }

        var m = new MyClass();
        m.myMethod();

        for (i in MyClass) { // nothing if iterating over class itself
            console.log(i);
            console.log(MyClass[i]);
        }

        for (i in m) { // you must iterate over instance of class
            console.log(i);
            console.log(m[i]);
        }

        // #2
        function MyClass2 () {
            console.log('here I am in constructor of MyClass2'); //debug
        }
        MyClass2.prototype.myMethod = function() {
                console.log('here I am in MyClass2.myMethod()'); //debug
                console.log(this.myProp); //debug
        };
        MyClass2.prototype.myProp = 11;

        m = new MyClass2();
        m.myMethod();
        for (i in m) {
            console.log(i);
            console.log(m[i]);
        }

        // #3 - does not work (TypeError: MyClass3 is not a constructor)
        //MyClass3 = {};
        //MyClass3.prototype = {
        //    constructor: function () {
        //        console.log('here I am in constructor of MyClass3'); //debug
        //    },
        //    myMethod: function() {
        //        console.log('here I am in MyClass3.myMethod()'); //debug
        //        console.log(this.myProp); //debug
        //    },
        //    myProp: 12
        //}
        //
        //m = new MyClass3(); // TypeError: MyClass3 is not a constructor
        //m.myMethod();
        //for (i in m) {
        //    console.log(i);
        //    console.log(m[i]);
        //}

        // #4
        MyClass4 = function(){};
        MyClass4.prototype = {
            constructor: function () {
                console.log('here I am in constructor of MyClass4'); //debug
            },
            myMethod: function() {
                console.log('here I am in MyClass4.myMethod()'); //debug
                console.log(this.myProp); //debug
            },
            myProp: 13
        }

        m = new MyClass4(); // TypeError: MyClass4 is not a constructor
        m.myMethod();
        for (i in m) {
            console.log(i);
            console.log(m[i]);
        }
        </script>
        //<?php
//        break;
//
//    case 'File_ensurePath':
//        App::debug(File::ensurePath('/testXXX/test3'));
//        App::debug(File::ensurePath(ROOT . '/testXXX/test2', true));
//        break;
//
//    case 'Utility_prependArray':
//        $a1 = array('a' => 'dasd', 'b' => 2);
//        $a2 = array('a' => 'dasd', 'b' => 2, 'x');
//        $a3 = array('dasd', 2, 'x');
//        $a4 = array(10 => 'dasd', 23 => 2, 7 => 'x');
//        $a = Arr::prepend($a1, 'p');
//        App::debug($a); //debug
//        $a = Arr::prepend($a2, 'p');
//        App::debug($a); //debug
//        $a = Arr::prepend($a3, 'p');
//        App::debug($a); //debug
//        $a = Arr::prepend($a, 'p2');
//        App::debug($a); //debug
//        $a = Arr::prepend($a4, 'p');
//        App::debug($a); //debug
//        $a = Arr::prepend($a4, 'p', 5);
//        App::debug($a); //debug
//        $a = Arr::prepend($a4, 'p', 'my');
//        App::debug($a); //debug
//        $a = Arr::prepend($a4, 'p', 7);
//        App::debug($a); //debug
//        $a = Arr::prepend($a4, 'p', '7');
//        App::debug($a); //debug
//        break;
//
//    case 'Utility_truncate':
//        //               1          2         3           4
//        //      1234567890123456789 012345678901 234567 890123
//        $str = "1Asdsdf 2fsdf 3sdf\n4sdf 5fsadf\t6fsdf\n7fasdf";
//        $len = strlen($str);
//        for ($i = 0; $i < $len; $i++) {
//            $char = substr($str, $i, 1);
//            echo $i + 1 . ': ' . $char . '(' . ord($char) . ')' . '<br />';
//        }
//        App::debug(strlen($str), 'strlen($str)'); //debug
//        echo "<pre>{$str}</pre>";
//        $str2 = Str::truncate($str, 21);
//        echo "21<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 25);
//        echo "25<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 30);
//        echo "30<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 34);
//        echo "34<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 36);
//        echo "36<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 37);
//        echo "37<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 38);
//        echo "38<pre>{$str2}</pre>";
//        //               1          2         3           4
//        //      1234567890123456789 012345678901 234567 890123
//        $str = "1Ašdädf 2fsdf 3ždf\n4sdf 5fľťôé\t6fsdf\n7fasdf";
//        $len = strlen($str);
//        for ($i = 0; $i < $len; $i++) {
//            $char = substr($str, $i, 1);
//            echo $i + 1 . ': ' . $char . '(' . ord($char) . ')' . '<br />';
//        }
//        App::debug(strlen($str), 'strlen($str)'); //debug
//        App::debug(mb_strlen($str, 'UTF-8'), 'mb_strlen($str, UTF-8)'); //debug
//        echo "<pre>{$str}</pre>";
//        $str2 = Str::truncate($str, 21, '...', 'UTF-8');
//        echo "21<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 25, '...', 'UTF-8');
//        echo "25<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 30, '...', 'UTF-8');
//        echo "30<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 34, '...', 'UTF-8');
//        echo "34<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 36, '...', 'UTF-8');
//        echo "36<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 37, '...', 'UTF-8');
//        echo "37<pre>{$str2}</pre>";
//        $str2 = Str::truncate($str, 38, '...', 'UTF-8');
//        echo "38<pre>{$str2}</pre>";
//        break;
//
//
//////mojo: use with attention, this can broke the tree if launched 2 times
////    case 'convertTree':
////        // create model
////        App::loadModel('App', 'WebContent');
////        $WebContent = new WebContent();
////
////        $WebContent->convertTree();
////        break;
//
//    case 'session':
//        App::debug($_SESSION); //debug
//        break;
//
//    case 'unsetSessionKey':
//        $key = @$this->args[1];
//        if ($key !== null) {
//            unset($_SESSION[$key]);
//        }
//        App::debug($_SESSION); //debug
//        break;
//
//    case 'rights':
//        $r = App::getRights(null, 'Reservations', 'controllers', 'ReservationsOrders.admin_update');
//        App::debug($r, 'public - ReservationsOrders.admin_update'); //debug
//        $r = App::getRights('admins', 'Reservations', 'controllers', 'ReservationsOrders.admin_update');
//        App::debug($r, 'admins - ReservationsOrders.admin_update'); //debug
//        $r = App::getRights('webmasters', 'Reservations', 'controllers', 'ReservationsOrders.admin_update');
//        App::debug($r, 'webmasters - ReservationsOrders.admin_update'); //debug
//        $r = App::getRights('webmasters', 'Reservations', 'controllers', 'ReservationsOrders.processPaymentResponse');
//        App::debug($r, 'webmasters - ReservationsOrders.processPaymentResponse'); //debug
//        $r = App::getRights('', 'Reservations', 'controllers', 'ReservationsOrders.processPaymentResponse');
//        App::debug($r, 'public - ReservationsOrders.processPaymentResponse'); //debug
//        break;
//
//    case 'binaryProduct':
//        define('THIRD_IS_SET', 4); //100 - kontrola 3. pozície
//        $b = 7; //111
//        App::debug(THIRD_IS_SET & $b); //debug
//        $b = 3; //011
//        App::debug(THIRD_IS_SET & $b); //debug
//        break;
//
//    case 'Form1':
//        App::debug($_POST); //debug
//        App::loadLib('App', 'FormHelper');
//        $Form = new FormHelper(array(
//           'data' => @$_POST['data'],
//           'required' => array(
//                'User' => array(
//                   'name' => 'name',
//                   'email' => 'email',
//               ),
//               'Profile' => array(
//                   'name' => 'name',
//                   'surname' => 'surname',
//                   'music_instrumets' => 'music_instrumets',
//               ),
//           ),
//           'defaultValues' => array(
//               'Profile' => array(
//                   'newsletter' => true,
//                   'country1' => array(1,3),
//                   'country2' => 2,
//               ),
//           ),
//           'inputDefaults' => array(
//               'template' => '<div>:l::i::e:</div>'
//           ),
//        ));
//        echo '<form method="post" action="/_debug/Form1">';
//        echo $Form->hidden('User.id');
//        echo $Form->text('User.name', array(
//            'label' => 'Login',
//        ));
//        echo $Form->text('User.email', array(
//            'label' => 'E-mail',
//        ));
//        echo $Form->hidden('Profile.id');
//        echo $Form->input('Profile.name', array(
//            'label' => 'Meno',
//        ));
//        echo $Form->checkbox('Profile.newsletter', array(
//            'label' => 'Novinky',
//        ));
//        echo $Form->select('Profile.country1', array(
//            'label' => 'Krajina',
//            'options' => array(
//                1 => 'Slovakia',
//                2 => 'Sweden',
//                3 => 'England',
//            ), //$myCountries, // array('optionValue' => 'optionLabel')
//            'multiple' => true,
//        ));
//        echo $Form->select('Profile.country2', array(
//            'label' => 'Krajina',
//            'options' => array(
//                1 => 'Slovakia',
//                2 => 'Sweden',
//                3 => 'England',
//            ), //$myCountries, // array('optionValue' => 'optionLabel')
//        ));
//        echo $Form->checkbox('Profile.hobby', array(
//            'label' => 'Hobby',
//            'options' => array('s' => 'Plavania', 'b' => 'Bicykel'), //$myHobby, // array('optionValue' => 'optionLabel')
//        ));
//        echo $Form->radio('Profile.sex', array(
//            'label' => 'Pohlavie',
//            'options' => array('m' => 'Muž', 'f' => 'Žena'),
//        ));
//        echo '<input type="submit" value="Submit"/>';
//        echo '</form>';
//        break;
//
//    case 'FormHelper':
//        App::debug($_POST); //debug
//        App::loadLib('App', 'FormHelper');
//        $Form = new FormHelper(array(
//            'data' => @$_POST['data'],
////           'data' => array(
////               'User' => array(
////                   'name' => 'durik',
////                   'email' => '<EMAIL>',
////               ),
////               'Profile' => array (
////                   'name' => 'Mojo',
////                   'surname' => 'Durik',
////                   'age' => '36',
////                   'phone' => '0987654321',
////                   'country' => 1,
////               ),
////           ),
//           'errors' => array (
//               'Profile' => array(
//                   'phone' => array(
//                       'Invalid phone number'
//                   ),
//               ),
//           ),
//           'required' => array(
//                'User' => array(
//                   'name' => 'name',
//                   'email' => 'email',
//               ),
//               'Profile' => array(
//                   'name' => 'name',
//                   'surname' => 'surname',
//                   'music_instrumets' => 'music_instrumets',
//               ),
//           ),
//           'defaultValues' => array(
//                'User' => array(
//                   //'email' => '<EMAIL>',
//               ),
//               'Profile' => array(
//                   'gender' => 'm',
//                   'newsletter' => true,
//                   'music_instrumets' => 'violin',
//                   'hobby' => array('swim', 'bike'),
//               ),
//           ),
////           'disabled' => array(
////               'User' => array(
////                    'name' => true,
////               ),
////           ),
//           'inputDefaults' => array(
//               'template' => '<div>:l::i::e:</div>'
//           ),
//        ));
////        // to test scroll to error
////        echo '<br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>';
////        echo '<br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>';
////        echo '<br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/><br/>';
//        echo '<form method="post" action="/_debug/Form">';
//        echo $Form->input('User.name', array(
//            'label' => 'Login',
//            'value' => 'Laszlo',
//        ));
//        echo $Form->input('User.email', array(
//            'label' => 'E-mail',
//            'placeholder' => 'Zadajte email',
//            //'value' => '<EMAIL>',
//        ));
//        echo $Form->input('Profile.phone', array(
//            'label' => 'Mobil',
//        ));
//        echo $Form->input('Profile.newsletter', array(
//            'type' => 'checkbox',
//            'label' => 'Novinky',
//            //'onclick' => "alert('good choice !')"
//            //'value' => 'yes',
//        ));
//        echo $Form->select('Profile.country', array(
//            'label' => 'Krajina',
//            'options' => array(
//                1 => 'Slovakia',
//                2 => 'Sweden',
//                3 => 'England',
//            ),
//            //'multiple' => true,
//            'value' => 2,
//        ));
////        echo $Form->hidden('Profile.hobby', array(
////            'value' => 'none',
////        ));
//        echo $Form->input('Profile.hobby.', array(
//            'type' => 'checkbox',
//            'label' => 'Bicykel',
//            'toggleValue' => 'bike',
//            //'value' => 'bike',
//            //'checked' => true,
//        ));
//        echo $Form->input('Profile.hobby.', array(
//            'type' => 'checkbox',
//            'label' => 'Plavanie',
//            'toggleValue' => 'swim',
//            //'value' => 'swim',
//            //'checked' => true,
//        ));
//        echo $Form->input('Profile.music_instrumets', array(
//            'type' => 'checkbox',
//            'label' => 'Hudobne nastroje',
//            'options' => array(
//                'flute' => 'Flauta',
//                'violin' => 'Husle',
//            ),
//            //'value' => 'flute',
//            'toggleTemplate' => ':i::l: ',
//        ));
//        echo $Form->input('Profile.gender', array(
//            'type' => 'radio',
//            'toggleValue' => 'm',
//            'label' => 'Muz',
//        ));
//        echo $Form->input('Profile.gender', array(
//            'type' => 'radio',
//            'toggleValue' => 'f',
//            'label' => 'Zena',
//        ));
//        echo $Form->input('Profile.profession', array(
//            'type' => 'radio',
//            'value' => 'manag',
//            'label' => 'Povolanie',
//            'options' => array(
//                'prog' => 'Programator',
//                'manag' => 'Manazer',
//                'manual' => 'Robotnik',
//                'science' => 'Vedec',
//
//            ),
//            'toggleTemplate' => ':i::l: ',
//        ));
//
//        echo '<input type="submit" value="Submit"/>';
//        echo '</form>';
//        break;
//
//    case 'is_null':
//        $a = null;
//        $b = false;
//        $c = '';
//        App::debug($a == null, '$a == null'); //debug 1
//        App::debug($b == null, '$b == null'); //debug 1
//        App::debug($c == null, '$b == null'); //debug 1
//        App::debug($a === null, '$a === null'); //debug 1
//        App::debug($b === null, '$b === null'); //debug 0
//        App::debug($c === null, '$c === null'); //debug 0
//
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            is_null($a);
//        }
//        Utility::getTimer(); // 1.16 ms
//
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            $a === null;
//        }
//        Utility::getTimer(); // 0.33 ms
//
//        break;
//
//    case 'url_base_with_root':
//        App::debug(App::$urlBaseWithRoot); //debug
//        break;
//
//    case 'tatrapay':
//        App::loadModel('Payment', 'payment_method.php');
//        $PaymentMethod = new PaymentMethod('tatrapay');
//        App::debug($PaymentMethod->isOnline()); //debug
////        App::debug($PaymentMethod->makeRequest(array(
////            'amount' => 20.00,
////            'variableSymbol' => 1456,
////        )));
//
//
////        $s['a']['b']['c'][] = 'x';
////        App::debug($s); //debug
////        App::debug((array)@$s['y']); //debug
//
//        break;
//
//    case 'ctype_digit':
//        App::debug(ctype_digit(10)); //debug FALSE
//        App::debug(ctype_digit((string)10)); //debug TRUE
//        App::debug(ctype_digit('10')); //debug TRUE
//        App::debug(ctype_digit(10.00)); //debug FALSE
//        App::debug(ctype_digit((string)10.02)); //debug FALSE
//        App::debug(ctype_digit('10.02')); //debug FALSE
//        // RESULT: input of ctype_digit() must be typecasted to string
//        break;
//
//    case 'ini':
//        ini_get('upload_max_filesize');
//        App::debug(ini_get('upload_max_filesize'), 'get upload_max_filesize');
//        App::debug(ini_get('post_max_size'), 'get post_max_size');
//        App::debug(ini_get('max_execution_time'), 'get max_execution_time');
//        App::debug(ini_set('upload_max_filesize', '8M'), 'set upload_max_filesize to 8M');
//        App::debug(ini_set('post_max_size', '8M'), 'set post_max_size to 8M');
//        App::debug(ini_set('max_execution_time', '600'), 'set max_execution_time to 600');
//        App::debug(ini_get('upload_max_filesize'), 'get upload_max_filesize');
//        App::debug(ini_get('post_max_size'), 'get post_max_size');
//        App::debug(ini_get('max_execution_time'), 'get max_execution_time');
//        break;
//
//    echo '<form name="csobpt_resp" action="http://alterego.localhost/p/eshop/eshop_orders/listen/12" method="post">
//            <input type="hidden" name="ZPRAVA" id="ZPRAVA" value="' . $zprava . '"/>
//            <input type="submit" value="Odpoved z CSOB">
//        </form>';
//        break;
//
//    case 'strlen':
//        $x = "";
//        echo strlen($x);
//        break;
//
//    case 'parents':
//        $parents = DB::query("SELECT * FROM `run_web_categories` WHERE (SELECT path FROM `run_web_categories` WHERE id = 90) LIKE CONCAT('%-',id, '-%') ORDER BY path ASC");
//        App::debug($parents); //debug
//        break;
//
//    case 'cofig_vs_property':
//        for ($i = 0; $i < 1000; $i++) {
//            $x = 0;
//        }
//
//    case 'validate_email':
//        $result = Validate::email('<EMAIL>');
//        //$result = Validate::email('<EMAIL>');
//        App::debug($result); //debug
//        break;
//
//    case 'const':
//        App::$locale;
//        App::$slug;
//        App::$parentSlug;
//        App::$urlLang;
//        break;
//
//    case 'empty':
//        $a = array('x' => 1);
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            if (empty($a['y'])) {
//                $a['x']++;
//            }
//        }
//        Utility::getTimer(); // 0.6 ms
//        App::debug($a['x']); //debug
//
//        $a = array('x' => 1);
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            if (!@$a['y']) {
//                $a['x']++;
//            }
//        }
//        Utility::getTimer(); // 1.6 ms
//        App::debug($a['x']); //debug
//
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            empty($a['y']);
//        }
//        Utility::getTimer(); // 0.37 ms
//
//        Utility::startTimer();
//        for ($i = 0; $i < 1000; $i++) {
//            !@$a['y'];
//        }
//        Utility::getTimer(); // 1.45 ms
//
//        break;
//
//    case 'select':
//        App::loadModel('Mailer', 'MailerContact');
//        $Contact = new MailerContact();
//        $contacts = $Contact->find(array(
//            'joins' => array(array(
//                'model' => 'MailerGroup',
//                'type' => 'left',
        echo 'gethostname (): ' . gethostname () . '<br />' ;                           // 3ilab
//                'conditions' => array(
//                    'MailerContact.run_mailer_groups_id = MailerGroup.id'
//                )
//            )),
//            'fields' => array(
//                'MailerContact.id',
//                'MailerContact.email',
//                'MailerContact.first_name',
//                'MailerGroup.id',
//                'MailerGroup.name',
//            ),
//            'qualify' => true,
//            'inflate' => true,
//        ));
//        App::debug($contacts); //debug
//        break;
//
//    case 'debug':
//        App::debug($_SERVER); //debug
//        //App::loadModel('', 'NotExisting'); // to rise exception
//        break;
//
//    case 'explode':
//        App::debug(explode(';', '')); //debug
//        App::debug(explode(' ', '')); //debug
//        App::debug(explode(';', null)); //debug
//        App::debug($a = Str::explode(';', '')); //debug
//        App::debug(array_pop($a)); //debug
//        App::debug(Str::explode(';', null)); //debug
//        App::debug(Str::explode(';', 'a;b;c;d;e;f;g;h')); //debug
//        App::debug(Str::explode(';', 'a;b;c;d;e;f;g;h', 3)); //debug
//        break;
//
//    case 'notEmpty':
//        App::debug(Validate::notEmpty(null), 'null'); //debug
//        App::debug(Validate::notEmpty(''), "''"); //debug
//        App::debug(Validate::notEmpty(array()), 'array()'); //debug
//        App::debug(Validate::notEmpty('0'), "'0'"); //debug
//        App::debug(Validate::notEmpty(0), '0'); //debug
//        App::debug(Validate::notEmpty(false), 'false'); //debug
//        App::debug(Validate::notEmpty('false'), "'false'"); //debug
//
//        App::debug(Validate::notEmpty('0', array(0)), "'0'"); //debug
//        App::debug(Validate::notEmpty(0, array(0)), '0'); //debug
//
//        App::debug(Validate::notEmpty(null, true), 'null'); //debug
//        App::debug(Validate::notEmpty('', true), "''"); //debug
//        App::debug(Validate::notEmpty(array(), true), 'array()'); //debug
//        App::debug(Validate::notEmpty('0', true), "'0'"); //debug
//        App::debug(Validate::notEmpty(0, true), '0'); //debug
//        App::debug(Validate::notEmpty(false, true), 'false'); //debug
//        App::debug(Validate::notEmpty('false', true), "'false'"); //debug
//        break;
//
//    case 'in_array':
//        $n1 = 'x';
//        $n2 = 'y';
//        $n3 = array('y', 1);
//        $n4 = array('y', 2);
//        $n5 = 1;
//        $n6 = '1';
//        $h = array(
//            'x',
//            array('y', 2),
//            1,
//        );
//        App::debug(in_array($n1, $h, true)); //debug
//        App::debug(in_array($n2, $h, true)); //debug
//        App::debug(in_array($n3, $h, true)); //debug
//        App::debug(in_array($n4, $h, true)); //debug
//        App::debug(in_array($n5, $h, true)); //debug
//        App::debug(in_array($n6, $h, true)); //debug
//        App::debug(in_array($n6, $h)); //debug
//
//        App::debug(in_array('1', array(1)), "'1' == 1"); //debug
//        App::debug(in_array('1', array(1), true), "'1' == 1 strict"); //debug
//        App::debug(in_array('1', array(true)), "'1' == true"); //debug
//        App::debug(in_array('1', array(true), true), "'1' == true"); //debug
//
//        $a = null;
//        App::debug((array)$a); //debug
//        App::debug((array)$a === array()); //debug
//        break;
//
//    case 'array_diff_key':
//        $a1 = array("a" => "green", "b" => "brown", "c" => "blue", "red");
//        $a2 = array("a" => "green", "yellow", "red");
//        App::debug(array_diff_assoc($a1, $a2)); //debug
//        App::debug(array_diff_key($a1, $a2)); //debug
//
//        $a1 = array("a" => "orange", "b" => "brown", "c" => "blue", "red");
//        $a2 = array("a" => "green", "yellow", "red");
//        App::debug(array_diff_assoc($a1, $a2)); //debug
//        App::debug(array_diff_key($a1, $a2)); //debug
//        break;
//
//    case 'convertDate':
//        App::debug(Date::convert('11.5.2011')); //debug
//        App::debug(Date::convert('11.5.2011', 'mdy')); //debug
//        App::debug(Date::convert('11/ 5/ 2011')); //debug
//        break;
//
//
//    case 'dateDiff':
//        App::debug(strtotime('2011-05-11')); //debug
//        App::debug(strtotime('11.05.2011')); //debug
//        App::debug(strtotime('11.5.2011')); //debug
//        App::debug(strtotime('11/05/2011')); //debug
//        App::debug(strtotime('11-05-2011')); //debug
//        App::debug(Date::getDiff('d', '11.5.2011', '13.5.2011'), 'dateDiff of days'); //debug;
//        App::debug(Date::getDiff('d', '11.5.2011', '2011-05-13')); //debug;
//        App::debug(Date::getDiff('d', '13.5.2011', '11.5.2011')); //debug;
//        App::debug(Date::getDiff('d', '11.5.2011', '11.5.2011')); //debug;
//        App::debug(Date::getDiff('d', '28.2.2011', '1.3.2011')); //debug;
//        App::debug(Date::getDiff('d', '28.2.2012', '1.3.2012')); //debug;
//        App::debug(Date::getDiff('yyyy', '14.3.1976', date('Y-m-d')), 'dateDiff of years'); //debug;
//        App::debug(Date::getDiff('yyyy', '14.3.1976', '13.3.2012')); //debug;
//        App::debug(Date::getDiff('yyyy', '14.3.1976', '14.3.2012')); //debug;
//        App::debug(Date::getDiff('yyyy', '14.3.1976', '15.3.2012')); //debug;
//        App::debug(Date::getDiff('s', '14.3.1976', date('Y-m-d')), 'dateDiff of seconds'); //debug;
//        App::debug(Date::getDiff('s', '14.3.1976 16:23:15', '14.3.1976 16:25:32')); //debug;
//        break;
//
//    case 'config':
//        App::debug(App::getConfig('App')); //debug
//        App::debug(App::getConfig('App','i18n.language')); //debug
//        App::debug(App::setConfig('App','parentSlug.x', 'a')); //debug
//        App::debug(App::getConfig('App')); //debug
//        App::debug(App::setConfig('App', 'new.tralala', 'test1')); //debug
//        App::debug(App::getConfig('App')); //debug
//        App::debug(App::setConfig('App','new.tralalb', 'test2')); //debug
//        App::debug(App::getConfig('App')); //debug
//
//        break;
//
//    case 'arrayPath':
//        $a = array(
//            'a' => array(
//                'aa' => array (
//                    'aaa' => 'aval',
//                ),
//            ),
//            'b' => 'bval',
//            'debug' => 1,
//        );
//        App::debug(Arr::getPath($a, '')); //debug
//        App::debug(Arr::getPath($a, 'a.aa')); //debug
//        App::debug(Arr::getPath($a, 'a.aa.aaa')); //debug
//        App::debug(Arr::getPath($a, 'b')); //debug
//        App::debug(Arr::getPath($a, 'b.aa')); //debug
//
//        App::debug(Arr::setPath($a, 'b.x.abc', 'ahoj')); //debug
//        App::debug($a); //debug
//        App::debug(Arr::setPath($a, 'b.s.r', 'ahoj')); //debug
//        App::debug($a); //debug
//        App::debug(Arr::setPath($a, 'x.s.r', 'ahoj1')); //debug
//        App::debug($a); //debug
//        App::debug(Arr::setPath($a, 'x.s.r', 'ahoj2')); //debug
//        App::debug($a); //debug
//        App::debug(Arr::setPath($a, 'x.s.r.y', 'ahoj3')); //debug
//        App::debug($a); //debug
//
//        break;
//
//    case 'Spreadsheet_Excel_Reader':
//        App::loadVendor('App', 'excelreader/excel_reader2.php');
//        //$data = new Spreadsheet_Excel_Reader("userfiles/files/pricelists/pricelists.xls");
//        $data = new Spreadsheet_Excel_Reader(ROOT . "/pricelists.xls");
//        echo $data->dump(false, false, 4);
//        break;
//
//    case 'getEmailTemplateFromXls':
//        App::loadModel('Reservations', 'ReservationsPackage');
//        $Package = new ReservationsPackage();
//        echo $Package->getEmailTemplateFromXls();
//        break;
//
//    case 'getFromXls':
//        App::setPropertyDebugOutput('echo');
//        App::loadModel('Reservations', 'ReservationsPackage');
//        $Package = new ReservationsPackage();
//        //$package = $Package->find(); // should riswe an Exception()
//        $package = $Package->getFromXls('pilatesweekend');
//        App::debug($package); //debug
//        $package = $Package->getFromXls('full');
//        App::debug($package); //debug
//        $package = $Package->getListFromXls();
//        App::debug($package); //debug
//        break;
//
//    case 'weekday':
//        $date = '2012-06-19';
//        $date = strtotime($date);
//        for ($i = 0; $i < 8; $i++) {
//            echo date('w', strtotime("+$i days", $date)) . ' ' . date('Y-m-d', strtotime("+$i days", $date)) . '<br />';
//        }
//        break;
//
//    case 'FB':
//        echo 'To send headers ...';
//        FB::log('hello from easycms');
//        FB::warn($_SERVER);
//
//        // table
//        $table   = array();
//        $table[] = array('Col 1 Heading','Col 2 Heading');
//        $table[] = array('Row 1 Col 1','Row 1 Col 2');
//        $table[] = array('Row 2 Col 1','Row 2 Col 2');
//        $table[] = array('Row 3 Col 1','Row 3 Col 2');
//        FB::table('Table Label', $table);
//
//        // trace
//        FB::trace('Trace Label');
//
//        //groups
//        FB::group('Test Group');
//        FB::log('Hello World');
//        FB::groupEnd();
//        FB::group(
//            'Collapsed and Colored Group',
//            array(
//                'Collapsed' => true,
//                'Color' => '#FF00FF'
//            )
//        );
//        FB::log('hello from easycms');
//        FB::warn($_SERVER);
//        FB::trace('Trace Label');
//        FB::groupEnd();
//        FB::log('this is after Collapsed and Colored Group');
//
//        break;
//
//    case 'list':
//        App::loadModel('Mailer', 'MailerCampaign');
//        $Campaign = new MailerCampaign();
//        //rblb//list($active, $suspendedAt) = $Campaign->findFirst(array( // does NOT work (awaits keys 0, 1 in array)
//        $campaign = $Campaign->findFirst(array(
//            'conditions' => array('id' => 1),
//            'fields' => array('active', 'suspended_at'),
//        ));
//        App::debug($campaign); //debug
//        break;
//
//    case 'findDuplicit':
//        App::loadModel('Mailer', 'MailerContact');
//        $Contact = new MailerContact();
//        $r = $Contact->findDuplicit('email', array(
//            'conditions' => array('deleted = 0'),
//        ));
//        App::debug($r); //debug
//        $r = $Contact->findDuplicit('email', array(
//            'fields' => array('id', 'email'),
//            'conditions' => array('deleted = 0'),
//            'rows' => true,
//        ));
//        App::debug($r); //debug
//        $r = $Contact->findDuplicit('email', array(
//                'conditions' => array(
//                    'deleted' => false
//                ),
//                'fields' => array(
//                    'id',
//                    'first_name',
//                    'last_name',
//                    'company_name',
//                    'email',
//                    'address_1',
//                    'address_2',
//                    'city',
//                    'zip',
//                    'birthday',
//                    'active',
//                    'run_mailer_groups_id',
//                ),
//                'offset' => 0,
//                'limit' => 25,
//                'rows' => true
//                //'order' => $order,
//
//            ));
//        App::debug($r); //debug
//        break;
//
//    case 'mergeLiterals':
//        $la = array(
//            'conditions' => true,
//            'fields' => array('a', 'b'),
//            'order' => array('a', 'b'),
//        );
//        $lb = array(
//            'conditions' => array('x'),
//            'fields' => array('c', 'd'),
//            'order' => true,
//            'having' => 'h',
//        );
//        App::debug(DB::mergeLiterals($la, $lb)); //debug
//        App::debug(DB::mergeLiterals(@$lx, $lb)); //debug
//        break;
//
//    case 'Model_findList':
//        App::loadModel('Mailer', 'MailerContact');
//        $Contact = new MailerContact();
//        $list = $Contact->findList(array(
//            'fields' => array('id'),
//        ));
//        App::debug($list); //debug
//        $list = $Contact->findList(array(
//            'key' => 'id',
//            'fields' => array('last_name'),
//        ));
//        App::debug($list); //debug
//        $list = $Contact->findList(array(
//            'key' => 'last_name',
//            'fields' => array('last_name'),
//        ));
//        App::debug($list); //debug
//        $list = $Contact->findList(array(
//            'fields' => array('last_name'),
//        ));
//        App::debug($list); //debug
//        $list = $Contact->findList(array(
//            'key' => 'id',
//            'fields' => array('id', 'last_name'),
//        ));
//        App::debug($list); //debug
//        $list = $Contact->findList(array(
//            'key' => 'id',
//            'fields' => array('last_name', 'first_name'),
//        ));
//        App::debug($list); //debug
//        $list = $Contact->findList(array(
//            'key' => 'last_name',
//            'fields' => array('last_name', 'first_name'),
//        ));
//        App::debug($list); //debug
//        $list = $Contact->findList();
//        App::debug($list); //debug
//        break;
//
//    case 'constants':
//        // compare mysite.localhost/debug.php?test=constants vs. localhost/mysite/debug.php?test=constants
//        echo 'LANG: ' . App::$lang . '<br />' ;
//        echo 'URL_LANG: ' . URL_LANG . '<br />' ;
//        echo 'HOME_SLUG: ' . HOME_SLUG . '<br />' ;
//        echo 'SLUG: ' . SLUG . '<br />' ;
//        echo 'PARENT_SLUG: ' . PARENT_SLUG . '<br />' ;
//        echo 'ROOT: ' . ROOT . '<br />' ;
//        echo 'URL_ROOT: ' . URL_ROOT . '<br />' ;
//        echo '$_SERVER[DOCUMENT_ROOT]: ' . $_SERVER['DOCUMENT_ROOT'] . '<br />' ;
//        echo '$_SERVER[HTTP_HOST]: ' . $_SERVER['HTTP_HOST'] . '<br />' ;
//        echo '$_SERVER[SCRIPT_FILENAME]: ' . $_SERVER['SCRIPT_FILENAME'] . '<br />' ;
//        echo '$_SERVER[REQUEST_URI]: ' . $_SERVER['REQUEST_URI'] . '<br />' ;
//        echo '$_SERVER[SCRIPT_NAME]: ' . $_SERVER['SCRIPT_NAME'] . '<br />' ;
//        echo '$_SERVER[PHP_SELF]: ' . $_SERVER['PHP_SELF'] . '<br />' ;
//        echo '$_SERVER[PATH_INFO]: ' . $_SERVER['PATH_INFO'] . '<br />' ;
//        break;
//
//
//    case 'intersect':
//        $a = array(
//            'default' => 'sk',
//            'en',
//            'de',
//        );
//        $b = array(
//            'sk',
//            'en',
//        );
//        App::debug(array_intersect($a, $b)); //debug
//        break;
//
//    case 'translate':
//        App::loadModel('Mailer', 'MailerCampaign');
//        $Campaign = new MailerCampaign();
//        App::loadModel('Mailer', 'MailerContact');
//        $Contact = new MailerContact();
//        $contact = $Contact->findFirst(array(
//            'lang' => 'de',
//        ));
//        App::debug($contact); //debug
//        break;
//
//    case 'selectliteral':
//        App::loadModel('Mailer', 'MailerContact');
//        $Contact = new MailerContact();
//        $beforeBirthdayPeriod = App::getSetting('Mailer', 'campaign.beforeBirthdayPeriod');
//        $rawContacts = $Contact->find(array(
//            'conditions' => array(
//                'active' => true,
//                'unsubscribed' => false,
//                'run_mailer_groups_id' => 1,
//                "CONCAT(YEAR(CURDATE()), DATE_FORMAT(birthday, '-%m-%d')) >= CURDATE()",
//                "CONCAT(YEAR(CURDATE()), DATE_FORMAT(birthday, '-%m-%d')) <= CURDATE() + INTERVAL 30 DAY",
//            ),
//            'order' => array(
//                'email' => 'ASC',
//                "CONCAT(YEAR(CURDATE()), DATE_FORMAT(birthday, '-%m-%d'))",
//            ),
//            'fields' => array(
//                'id',
//                'first_name',
//                'last_name',
//                'company_name',
//                'email',
//            ),
//            'literals' => array(
////                'conditions' => array(
////                    "CONCAT(YEAR(CURDATE()), DATE_FORMAT(birthday, '-%m-%d')) >= CURDATE()",
////                    "CONCAT(YEAR(CURDATE()), DATE_FORMAT(birthday, '-%m-%d')) <= CURDATE() + INTERVAL 30 DAY",
////                ),
//                'conditions' => true,
//                'order' => array(
//                    "CONCAT(YEAR(CURDATE()), DATE_FORMAT(birthday, '-%m-%d'))"
//                ),
////                'order' => true,
//            ),
//        ));
//        App::debug($rawContacts); //debug
//        break;
//
//    case 'updateliteral':
//        DB::getUpdateQuery(
//            'run_mailer_campaigns',
//            array(
//                'started_at' => 'NOW()',
//                'active' => "CURDATE() > '2012-04-16'",
//            ),
//            array(
//                'conditions' => array(
//                    'active' => true,
//                ),
//                'literals' => array(
//                    'fields' => array(
//                        'started_at',
//                        'active',
//                    )
////                    'fields' => true,
//                ),
//            )
//        );
//        break;
//
//    case 'insertliteral':
//        DB::getInsertQuery(
//            'run_mailer_campaigns',
//            array(
//                'started_at' => 'NOW()',
//                'active' => "CURDATE() > '2012-04-16'",
//            ),
//            array(
//                'literals' => array(
//                    'fields' => array(
//                        'started_at',
//                        'active',
//                    ),
////                    'fields' => true,
//                ),
//            )
//        );
//        break;
//
//    case 'matchdate':
//        preg_match('/(\d{4}-\d\d-\d\d)(?:(?:T|\s)\d\d:\d\d:\d\d)?/', "2008-01-01T03:00:00", $matches);
//        App::debug($matches); //debug
//        preg_match('/(?:\d{4}-\d\d-\d\d(?:T|\s))?(\d\d:\d\d:\d\d)/', "2008-01-01T03:00:00", $matches);
//        App::debug($matches); //debug
//        break;
//
//    case 'findCount':
//        App::loadModel('Mailer', 'MailerContact');
//        $Contact = new MailerContact();
//        $result = $Contact->findCount(array(
//            'conditions' => array(
//                'last_name' => 'Chlapecka'
//            ),
//        ));
//        App::debug($result); //debug
//        break;
//
//    case 'birthday':
//        App::loadModel('Mailer', 'MailerContact');
//        $Contact = new MailerContact();
//        $beforeBirthdayPeriod = App::getSetting('Mailer', 'campaign.beforeBirthdayPeriod');
//        $rawContacts = $Contact->find(array(
//            'conditions' => array(
//                //'active' => true,
//                //'unsubscribed' => false,
//                //'run_mailer_groups_id' => 1,
//            ),
//            'order' => array('email'),
//            'fields' => array(
//                'id',
//                'first_name',
//                'last_name',
//                'company_name',
//                'email',
//                "CONCAT(YEAR(CURDATE()), DATE_FORMAT(`birthday`, '-%m-%d')) AS `anniversary`"
//            ),
//            'having' => array(
//                'anniversary >= CURDATE()',
//                "anniversary <= CURDATE() + INTERVAL $beforeBirthdayPeriod DAY",
//            ),
//            //'literals' => array('fields' => "CONCAT(YEAR(CURDATE()), DATE_FORMAT(birthday, '-%m-%d')) AS anniversary"),
//            'literals' => array('fields' => array("CONCAT(YEAR(CURDATE()), DATE_FORMAT(`birthday`, '-%m-%d')) AS `anniversary`", 'email')),
//        ));
//        App::debug($rawContacts); //debug
//
//        break;
//
//    case 'URL_ROOT':
//        App::debug(URL_ROOT);
//        break;
//
//    case 'log':
//        App::log('log', 'Ahoj2');
//        App::log('debug2', 'Ahoj3');
//        break;
//
//    case 'datestrcompare':
//        App::debug(date('Y-m-d H:i:s'));
//        App::debug('2012-02-03 15:23:02' > '2012-02-03 15:23:03');
//        App::debug('2012-02-03 15:23:04' > '2012-02-03 15:23:03');
//        App::debug('2013-02-03 15:23:02' > '2012-02-03 15:23:03');
//        break;
//
//    case 'DB::getUpdateQuery':
//        Utility::startTimer();
//        DB::getUpdateQuery('t1',
//            array(
//                't2.name' => 'Kukurica',
//            ),
//            array(
//                'joins' => array(
//                    array(
//                        'table' => 't2',
//                        'type' => 'left',
//                        'conditions' => array(
//                            't1.id = t2.t1_id'
//                        ),
//                        'delete' => true,
//                    ),
//                ),
//                'conditions' => array(
//                    't1.id' => '1',
//                ),
//                'allowFields' => array(
//                    't2.name',
//                ),
//            )
//        );
//        Utility::getTimer();
//        App::getElapsedMicrotime(true);
//        break;
//
//    case 'DB::getDeleteQuery':
//        Utility::startTimer();
//        DB::getDeleteQuery('t1',array(
//            'joins' => array(
//                array(
//                    'table' => 't2',
//                    'type' => 'left',
//                    'conditions' => array(
//                        't1.id = t2.t1_id'
//                    ),
//                    'delete' => true,
//                ),
//            ),
//            'conditions' => array(
//                't2.name' => 'Kukurica',
//            ),
//        ));
//        Utility::getTimer();
//        App::getElapsedMicrotime(true);
//        break;
//
//    case 'loadControllerAction':
//        $output = App::loadControllerAction('MailerContacts', 'admin_index');
//        App::debug($output); //debug
//        $output = App::loadControllerAction('MailerContacts', 'admin_index');
//        App::debug($output); //debug
//        break;
//
//    case 'loadContents':
//        App::getContentByLocator('sluzby');
//        break;
//
//    case 'val':
//        $a['x'] = 'a';
//        $b['x'] = 0;
//        $a['x'] = Sanitize::value($a['x'], 'vala');
//        $b['x'] = Sanitize::value($b['x'], 'valb');
//        App::debug($a); //debug
//        App::debug($b); //debug
//        $a['x'] = 'a';
//        $b['x'] = null;
//        $a['x'] = Sanitize::value($a['x'], 'vala');
//        $b['x'] = Sanitize::value($b['x'], 'valb');
//        App::debug($a); //debug
//        App::debug($b); //debug
//        break;
//
//    case 'reference':
//        $x = Sanitize::value($a['x'], 'nothing');
//        App::debug($x); //debug
//        App::debug($a); //debug
//        empty($a['y']);
//        App::debug($a); //debug
//        break;
//
//    case 'microtime':
//        App::debug(microtime()); //debug
//        App::debug(microtime(true)); //debug
//        App::debug(microtime(true)*10000); //debug
//        App::debug((int)(microtime(true)*10000)); //debug
//        App::debug(time()); //debug
//        break;
//
//    case 'debug_backtrace':
//        DB::getSelectQuery('run_users', array('fields' => array('id')));
//        DB::select('run_users', array('fields' => array('id')));
//        App::loadModel('App', 'User');
//        $UserModel = new User();
//        $UserModel->find(array('fields' => array('id')));
//        break;
//
//
//    case 'checkbox':
//        App::debug($_POST,  '$_POST'); //debug
//        ?>
        <form action="debug.php?test=checkbox" method="post">
        <input type="checkbox" name="vehicle[]" value="Bike" /> I have a bike<br />
        <input type="checkbox" name="vehicle[]" value="Car" /> I have a car<br />
        <input type="checkbox" name="vehicle[3]" value="Tree" /> I have a tree<br />
        <input type="submit" />
        </form>
        //<?php
//        break;
//
//
//    case 'gettext':
//        echo __d(__FILE__, 'Creation has failed');
//        echo __d(__FILE__, 'Creation has failed');
//        echo __d(__FILE__, 'enum_text_email');
//        echo __d(__FILE__, 'enum_text_email');
//        break;
//
//    case 'DB::enumOptions':
//        App::debug(DB::getEnumValues('mailer_campaigns', 'format'), 'Known column'); //debug
//        App::debug(DB::getEnumValues('mailer_campaigns', 'formatx'), 'Unknown column'); //debug
//        break;

    default:
        App::debug('', "The '{$case}' does not match to any of debugging cases");
}
Utility::getTimer('debug', $case);
