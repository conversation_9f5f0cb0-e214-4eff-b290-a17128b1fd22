//
// Define animations mixins here
//
// For inspiration see https://raw.githubusercontent.com/daneden/animate.css/master/animate.css
// NOTE: It is preffred to copy the animation code here instead of include whole animation library css!
//

.animateFadeInUpTiny() {
    animation-name: animateFadeInUpTiny;
    animation-duration: 1s;
    animation-delay: 0s;
    animation-fill-mode: both;        
}
@keyframes animateFadeInUpTiny {
    from {
        opacity: 0;
        transform: translate3d(0, 50px, 0);
    }
    to {
        opacity: 1;
        transform: none;
    }
}

.animateFadeInUp() {
    animation-name: animateFadeInUp;
    animation-duration: 1s;
    animation-delay: 0s;
    animation-fill-mode: both;        
}
@keyframes animateFadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 100%, 0);
    }
    to {
        opacity: 1;
        transform: none;
    }
}

.animateFadeInRight() {
    animation-name: animateFadeInRight;
    animation-duration: 1s;
    animation-delay: 0s;
    animation-fill-mode: both;        
}
@keyframes animateFadeInRight {
    from {
        opacity: 0;
        transform: translate3d(100%, 0, 0);
    }
    to {
        opacity: 1;
        transform: none;
    }
}

.animateFadeInLeft() {
    animation-name: animateFadeInLeft;
    animation-duration: 1s;
    animation-delay: 0s;
    animation-fill-mode: both;        
}
@keyframes animateFadeInLeft {
    from {
        opacity: 0;
        transform: translate3d(-100%, 0, 0);
    }
    to {
        opacity: 1;
        transform: none;
    }
}

.animateZoomInTiny() {
    animation-name: animateZoomInTiny;
    animation-duration: 20s;
    animation-delay: 0s;
    animation-fill-mode: both;        
}
@keyframes animateZoomInTiny {
    to {
        transform: scale3d(1.1, 1.1, 1.1);
    }
}

.animateBounce() {
    animation-name: animateBounce;
    animation-duration: 1s;
    animation-delay: 0s;
    animation-fill-mode: both;        
    transform-origin: center bottom;
}
@keyframes animateBounce {
    from, 20%, 53%, 80%, to {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -30px, 0);
    }
    70% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0,-4px,0);
    }
}

.animateBounceIn() {
    animation-name: animateBounceIn;
    animation-duration: 0.75s;
    animation-delay: 0s;
    animation-fill-mode: both;        
    transform-origin: center bottom;
}
@keyframes animateBounceIn {
    from, 20%, 40%, 60%, 80%, to {
         animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    }
    0% {
        opacity: 0;
        transform: scale3d(.3, .3, .3);
    }
    20% {
       transform: scale3d(1.1, 1.1, 1.1);
    }
    40% {
        transform: scale3d(.9, .9, .9);
    }
    60% {
        opacity: 1;
        transform: scale3d(1.03, 1.03, 1.03);
    }
    80% {
        transform: scale3d(.97, .97, .97);
    }
    to {
        opacity: 1;
        transform: scale3d(1, 1, 1);
    }
}

.animateRotate() {
  animation: animateRotate 2s infinite linear;
}
@keyframes animateRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

.animateShakeX() {
    animation-name: animateShakeX;
    animation-duration: .8s;
}
@keyframes animateShakeX {
    from,
    to {
        transform: translate3d(0, 0, 0);
    }
    10%,
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: translate3d(-8px, 0, 0);
        transform: translate3d(-8px, 0, 0);
    }
    20%,
    40%,
    60%,
    80% {
        -webkit-transform: translate3d(10px, 0, 0);
        transform: translate3d(10px, 0, 0);
    }
}
