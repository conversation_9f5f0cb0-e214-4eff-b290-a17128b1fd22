@img-dir: '/app/img';

// <PERSON>erdana makes problems in Linux vs. Windows.
// Windows knows Verdana, Linux not.
// Verdana is much more wider than arial...
// <PERSON><PERSON>
@font-family: 'Plus Jakarta Sans', sans-serif;
//@font-family-1: 'Work Sans', sans-serif;
//@font-family-2: @font-family-1;   

@font-size: 15px;

// dimensions
@center-width: 1310px;
@narrow-center-indent: 10px;
@main-content-width: 1110px;
@side-content-width: 185px;
@bottom-content-center-width: @center-width;
@footer-center-width: @center-width;
// ATTENTION: Less variables are lazy evaluated (http://lesscss.org/features/#variables-feature-lazy-evaluation)
// This "lazy evaluation" seems to work like "set all literals values at first and only then compute expression".
// This can cause failure of variable @b value expression computation if the value of variable @a used in
// the expression is changed later (even "below" the line where @b is defined in code) to a value which 
// is invalid from point of view of math operations (e.g. auto, none, initial, ...).
// E.g. following code will fail:
//
// @a: 1200px;
// @b: @a / 2;
// @a: auto; // comment / uncomment this line and see the diference in compilation 
//
// Implications:
// 1] To make the above code work you must use "math-valid" variable @-a:
//
// @a: 1200px;
// @-a: 1200px;
// @b: @-a / 2;
// ...
// @a: auto;
// @b: auto; // normally you should also do this if @b is still somehaw related to actual @a
//
// 2] If you need to recalculate @b after @a is changed to another math-valid value 
// (valid from point of view of math operations) then you need to reset also 
// the "math-valid" variable @-a:
//
// @a: 1200px;
// @-a: 1200px;
// @b: @-a / 2;
// ...
// @a: 1400px;
// @-a: 1400px;
//
// Of course if you don't need more to keep relation between @a and @b then you can 
// set @b to whatever literal or new expresion you want.
@-center-width: 1190px;

@checkout-border-radius: 6px;

// colors
@color-black: #1A1A1A;
@color-light-black-2: #161616;
@color-grey: #808080;
@color-main-grey: #f8f9fa;
@color-dark-grey: #4D4D4D;
@color-dark-grey-2: #9D9D9D;
@color-dark-grey-3: #333333;
@color-light-grey: #CCCCCC;
@color-light-grey-2: #d6d6d6; // =#00000029
@color-light-grey-3: #F0F0F0;
@color-light-grey-4: #F7F7F7;
@color-light-grey-5: #E5E5E5;
@color-light-grey-6: @color-light-grey-5;
@color-cream: #FAF7EE;
@color-light-cream: #FFFDFA;
@color-green: #40B95F;
@color-dark-green: #083a1f;
@color-red: #E30A17;
// convert any color to red one, see https://codepen.io/sosuke/pen/Pjoqqp
@color-by-filter-red: brightness(0) saturate(100%) invert(26%) sepia(95%) saturate(7083%) hue-rotate(350deg) brightness(87%) contrast(105%);
@color-by-filter-dark-green: brightness(0) saturate(100%) invert(16%) sepia(46%) saturate(748%) hue-rotate(95deg) brightness(94%) contrast(97%);
@color-blue: #397EDD;
@color-orange: #ED8021; //#FF7600
@color-white: #fff;


@color-main: @color-black;
@color-main-light: @color-black;
@color-main-dark: @color-black;
@color-highlighted: @color-dark-green;
@color-by-filter-highlighted: @color-by-filter-dark-green;
@color-highlighted-light: @color-red;
@color-darkred: @color-red;
@color-alert: @color-red;
@color-transparent-mask: rgba(24, 25, 26, 0.78);
@color-topmenu: @color-main-grey;
@color-topmenu-hover: @color-main-grey; 

@color-body-bg: @color-dark-green;
@color-page-bg: @color-main-grey;
@color-header-bg: @color-white;
@color-footer-bg: @color-body-bg;
@color-border: @color-grey;
@color-border-light: @color-light-grey;
@color-section-border: @color-border;
@color-input-border: @color-light-grey;
@color-button-bg: @color-highlighted;

@transition-duration: 1s;
@menu-transition-duration: @transition-duration / 2;
@transition-timing-function: cubic-bezier(0.14, 0.56, 0.71, 0.975);

// responsive grid variables
@grid-cols-gap-width: 30px;
