.border-radius(@value) {
    -webkit-border-radius: @value;
    -moz-border-radius: @value;
    border-radius: @value;
}
.border-top-radius(@value) {
    -webkit-border-top-left-radius: @value;
    -webkit-border-top-right-radius: @value;
    -moz-border-radius-topleft: @value;
    -moz-border-radius-topright: @value;
    border-top-left-radius: @value;
    border-top-right-radius: @value;
}
.border-bottom-radius(@value) {
    -webkit-border-bottom-left-radius: @value;
    -webkit-border-bottom-right-radius: @value;
    -moz-border-radius-bottomleft: @value;
    -moz-border-radius-bottomright: @value;
    border-bottom-left-radius: @value;
    border-bottom-right-radius: @value;
}
.border-left-radius(@value) {
    -webkit-border-bottom-left-radius: @value;
    -webkit-border-top-left-radius: @value;
    -moz-border-radius-bottomleft: @value;
    -moz-border-radius-topleft: @value;
    border-bottom-left-radius: @value;
    border-top-left-radius: @value;
}
.border-right-radius(@value) {
    -webkit-border-bottom-right-radius: @value;
    -webkit-border-top-right-radius: @value;
    -moz-border-radius-bottomright: @value;
    -moz-border-radius-topright: @value;
    border-bottom-right-radius: @value;
    border-top-right-radius: @value;
}
.border-top-left-radius(@value) {
    -webkit-border-top-left-radius: @value;
    -moz-border-radius-topleft: @value;
    border-top-left-radius: @value;
}
.border-top-right-radius(@value) {
    -webkit-border-top-right-radius: @value;
    -moz-border-radius-topright: @value;
    border-top-right-radius: @value;
}
.border-bottom-left-radius(@value) {
    -webkit-border-bottom-left-radius: @value;
    -moz-border-radius-bottomleft: @value;
    border-bottom-left-radius: @value;
}
.border-bottom-right-radius(@value) {
    -webkit-border-bottom-right-radius: @value;
    -moz-border-radius-bottomright: @value;
    border-bottom-right-radius: @value;
}
// @see http://www.w3schools.com/cssref/css3_pr_animation.asp
// name duration timing-function delay iteration-count direction fill-mode play-state
.animation(@value) {
    -webkit-animation: @value;
    -moz-animation: @value;
    -o-animation: @value;
    animation: @value;
}

// @see http://www.w3schools.com/cssref/css3_pr_transform.asp
.transform(@value) {
    -webkit-transform: @value;
    -moz-transform: @value;
    -ms-transform: @value;
    -o-transform: @value;
    transform: @value;
}
// @see http://www.w3schools.com/cssref/css3_pr_transition.asp
// property(comma separated list) duration timing-function delay
.transition(@value) { 
    -webkit-transition: @value; 
    -moz-transition: @value; 
    -o-transition: @value;
    transition: @value;
}
.transition-property(@value) {
    -webkit-transition-property: @value; 
    -moz-transition-property: @value; 
    -o-transition-property: @value; 
    transition-property: @value;
}
.transition-duration(@value) {
    -webkit-transition-duration: @value; 
    -moz-transition-duration: @value; 
    -o-transition-duration: @value;
    transition-duration: @value;
}
// @see http://www.w3schools.com/cssref/css3_pr_transition-timing-function.asp
.transition-timing-function(@value) {
    -webkit-transition-timing-function: @value; 
    -moz-transition-timing-function: @value; 
    -o-transition-timing-function: @value; 
    transition-timing-function: @value;
}
.transition-delay(@value) {
    -webkit-transition-delay: @value; 
    -moz-transition-delay: @value; 
    -o-transition-delay: @value; 
    transition-delay: @value;
}
.vertical-align(@value) {
    position: relative;
    top: @value;
    -webkit-transform: translateY(-@value);
    -ms-transform: translateY(-@value);
    transform: translateY(-@value);
}
// @unicode E.g. '\f196'
//
// Once element is styled as icon by this mixin then use only content: '\...' to 
// change icon e.g. on hover. Common usage pattern is:
//
//      .my-element {
//          &:before {
//              .font-awesome-icon('\f196');
//          }
//      }
.font-awesome-icon(@unicode) {
    display: inline-block;
    font: normal normal normal @font-size/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: @unicode;
}
.flexi-centered() {
    display:table;
    margin:0 auto;
    word-wrap:initial;
}
.clear() {
    &:before,
    &:after {
        content: " ";
        display: table;
    }
    &:after {
        clear: both;
    }
    *zoom: 1;
}
.image-zoom-transition() {
    transition-property: transform;
    transition-duration: @menu-transition-duration;
    transition-timing-function: @transition-timing-function;
    transition-delay: initial;                
}
.image-zoom-transform() {
    transform: scale(1.025);                
}
.image-grayscale-transition() {
    filter: grayscale(100%);
    transition-property: filter;
    transition-duration: @menu-transition-duration;
    transition-timing-function: @transition-timing-function;
    transition-delay: initial;                
}
.image-grayscale-transform() {
    filter: grayscale(0%);
}

.center-horizontally() {
    position: absolute;
    left: 50%;
    transform: translate3d(-50%, 0, 0);
}
.center-vertically() {
    position: absolute;
    top: 50%;
    transform: translate3d(0, -50%, 0);
}
.center-both() {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
}
.expanded-menu-arrow(@size, @color: @color-light-grey-5) {
    position: absolute;
    width: @size * 2;
    height: @size;
    bottom: -8px;
    border-left: @size solid transparent;
    border-right: @size solid transparent;
    border-bottom: @size solid @color;
    opacity: 0;
    transition: opacity .2s;
}

.card-mixin(@padding: 24px 28px) {
    padding: @padding;
    border-radius: 4px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.14);
    background: @color-white;
}
.slider-arrow(@size: 56px) {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    width: @size;
    height: @size;
    border-radius: 999px;
    margin-top: -(@size/2);
    background-color: fade(@color-white, 60%);
    //background-image: url('@{img-dir}/arrow-left-black.png');
    //background-repeat: no-repeat;
    //background-position: center;
    z-index: 1;
    box-shadow: 0 0 1px @color-black;
    transition: background-color .2s;
    //*/
    &:before {
        //.font-awesome-icon('\f053');
        .font-awesome-icon('\f104');
        color: @color-black;
        font-size: (@size/2) - 2px;
        line-heigth: (@size/2) - 2px;
        text-indent: 0;
        display: block;
        width: (@size/2) - 2px;
        text-align: center;
        opacity: 0.4;
    }
    //*/
    &:hover {
        background-color: fade(@color-white, 80%);
        &:before {

        }
    }
}
.owl-carousel() {
    overflow: hidden;
    .owl-item {
        text-align: center;
    }
    .owl-nav {
        position: absolute;
        width: 100%;
        top: 50%;
        .owl-prev,
        .owl-next {
            .slider-arrow();
            position: absolute;
            left: -12px;
            //opacity: 1;
            //transition: opacity .2s;
            &.disabled {
                opacity: .3;
                cursor: not-allowed;
            }
        }
        .owl-next {
            left: auto;
            right: -12px;
            transform: rotate(180deg);
        }
        .owl-dots {
            display: none;
        }
    }
}
