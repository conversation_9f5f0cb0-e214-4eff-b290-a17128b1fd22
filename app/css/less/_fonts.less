//
// ATTENTION: Not all font formats are supported in all browsers, e.g. .otf does not work in IE.
// See table here http://www.w3schools.com/css/css3_fonts.asp
//

/**
 * Plus Jakarta Sans
 * https://tokotype.github.io/plusjakarta-sans/
 */
/*

/* Plus Jakarta Sans Extra Light */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: normal;
    font-weight: 200;
    src: local('Plus Jakarta Sans Extra Light'), local('PlusJakartaSans-ExtraLight'),
    url('/app/fonts/PlusJakartaSans-ExtraLight.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-ExtraLight.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-ExtraLight.ttf') format('truetype') /* Safari, Android, iOS */
}
/* Plus Jakarta Sans Extra Light Italic */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: italic;
    font-weight: 200;
    src: local('Plus Jakarta Sans Extra Light Italic'), local('PlusJakartaSans-ExtraLightItalic'),
    url('/app/fonts/PlusJakartaSans-ExtraLightItalic.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-ExtraLightItalic.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-ExtraLightItalic.ttf') format('truetype') /* Safari, Android, iOS */
}

/* Plus Jakarta Sans Light */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: normal;
    font-weight: 300;
    src: local('Plus Jakarta Sans Light'), local('PlusJakartaSans-Light'),
    url('/app/fonts/PlusJakartaSans-Light.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-Light.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-Light.ttf') format('truetype') /* Safari, Android, iOS */
}
/* Plus Jakarta Sans Light Italic */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: italic;
    font-weight: 300;
    src: local('Plus Jakarta Sans Light Italic'), local('PlusJakartaSans-LightItalic'),
    url('/app/fonts/PlusJakartaSans-LightItalic.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-LightItalic.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-LightItalic.ttf') format('truetype') /* Safari, Android, iOS */
}

/* Plus Jakarta Sans Regular */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: normal;
    font-weight: 400;
    src: local('Plus Jakarta Sans'), local('PlusJakartaSans'),
    url('/app/fonts/PlusJakartaSans-Regular.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-Regular.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-Regular.ttf') format('truetype') /* Safari, Android, iOS */
}
/* Plus Jakarta Sans Regular Italic */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: italic;
    font-weight: 400;
    src: local('Plus Jakarta Sans Italic'), local('PlusJakartaSans-Italic'),
    url('/app/fonts/PlusJakartaSans-Italic.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-Italic.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-Italic.ttf') format('truetype') /* Safari, Android, iOS */
}

/* Plus Jakarta Sans Medium */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: normal;
    font-weight: 500;
    src: local('Plus Jakarta Sans Medium'), local('PlusJakartaSans-Medium'),
    url('/app/fonts/PlusJakartaSans-Medium.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-Medium.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-Medium.ttf') format('truetype') /* Safari, Android, iOS */
}
/* Plus Jakarta Sans Medium Italic */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: italic;
    font-weight: 500;
    src: local('Plus Jakarta Sans Medium Italic'), local('PlusJakartaSans-MediumItalic'),
    url('/app/fonts/PlusJakartaSans-MediumItalic.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-MediumItalic.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-MediumItalic.ttf') format('truetype') /* Safari, Android, iOS */
}

/* Plus Jakarta Sans Bold */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: normal;
    font-weight: 700;
    src: local('Plus Jakarta Sans Bold'), local('PlusJakartaSans-Bold'),
    url('/app/fonts/PlusJakartaSans-Bold.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-Bold.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-Bold.ttf') format('truetype') /* Safari, Android, iOS */
}
/* Plus Jakarta Sans Bold Italic */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: italic;
    font-weight: 700;
    src: local('Plus Jakarta Sans Bold Italic'), local('PlusJakartaSans-BoldItalic'),
    url('/app/fonts/PlusJakartaSans-BoldItalic.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-BoldItalic.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-BoldItalic.ttf') format('truetype') /* Safari, Android, iOS */
}

/* Plus Jakarta Sans Extra Bold */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: normal;
    font-weight: 800;
    src: local('Plus Jakarta Sans Extra Bold'), local('PlusJakartaSans-ExtraBold'),
    url('/app/fonts/PlusJakartaSans-ExtraBold.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-ExtraBold.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-ExtraBold.ttf') format('truetype') /* Safari, Android, iOS */
}
/* Plus Jakarta Sans Extra Bold Italic */
@font-face {
    font-family: 'Plus Jakarta Sans';
    font-style: italic;
    font-weight: 800;
    src: local('Plus Jakarta Sans Extra Bold Italic'), local('PlusJakartaSans-ExtraBoldItalic'),
    url('/app/fonts/PlusJakartaSans-ExtraBoldItalic.woff2') format('woff2'), /* Super Modern Browsers */
    url('/app/fonts/PlusJakartaSans-ExtraBoldItalic.woff') format('woff'), /* Modern Browsers */
    url('/app/fonts/PlusJakartaSans-ExtraBoldItalic.ttf') format('truetype') /* Safari, Android, iOS */
}


// 
// Inter 
// https://fonts.google.com/specimen/Inter

@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 100;
    src: local('Inter Thin'), local('Inter-Thin'),
    url('/app/fonts/Inter-Thin.ttf') format('truetype')
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 200;
    src: local('Inter Extra Light'), local('Inter-Extra-Light'),
    url('/app/fonts/Inter-ExtraLight.ttf') format('truetype')
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 300;
    src: local('Inter Light'), local('Inter-Light'),
    url('/app/fonts/Inter-Light.ttf') format('truetype')
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    src: local('Inter'),
    url('/app/fonts/Inter-Regular.ttf') format('truetype')
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 500;
    src: local('Inter Medium'), local('Inter-Medium'),
    url('/app/fonts/Inter-Medium.ttf') format('truetype')
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 600;
    src: local('Inter Semi Bold'), local('Inter-SemiBold'),
    url('/app/fonts/Inter-SemiBold.ttf') format('truetype')
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 700;
    src: local('Inter Bold'), local('Inter-Bold'),
    url('/app/fonts/Inter-Bold.ttf') format('truetype')
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 800;
    src: local('Inter Extra Bold'), local('Inter-ExtraBold'),
    url('/app/fonts/Inter-ExtraBold.ttf') format('truetype')
}
@font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 900;
    src: local('Inter Black'), local('Inter-Black'),
    url('/app/fonts/Inter-Black.ttf') format('truetype')
}

