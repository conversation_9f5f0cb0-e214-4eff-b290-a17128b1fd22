// Bootstrap core variables and mixins
//@import "../vendors/less/bootstrap/variables.less";
//@import "../vendors/less/bootstrap/mixins.less";
// local variables
@import "_variables.less";
@import "_mixins.less";

// Use this css styles to customize ckeditor display of content
body {
    margin: 0;
    min-width: 0;
    max-width: @center-width;
    margin: 10px;
    background: #ffffff;
    &.footer-text {
        font-size: 13px;
        color: @color-main-light;
    }
    &.header-text-layout {
        max-width: 260px;
    }
    &.news-resume-layout {
        max-width: 285px;
    }
    &.news-content-layout {
        max-width: 860px;
    }
    &.timeline-event-layout {
        max-width: 328px;
    }
}

table.layout td, 
table.layout th, 
.contact,
.contact div {
    border: 1px #D3D3D3 dotted;
}
.contact {
    margin-right: -1px; 
}
.white {
    color: #787878;
}
div.circle {
    border-color: #D3D3D3;
    color: #787878;
}
