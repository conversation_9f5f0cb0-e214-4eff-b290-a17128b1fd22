// Bootstrap core variables and mixins
@import "../vendors/less/bootstrap/variables.less";
@import "../vendors/less/bootstrap/mixins.less";
// local variables
@import "./libs/_variables.less";

//
// ATTENTION: copy these styles to the end of /app/vendors/ckeditor/plugins/ckfinder/skins/v1/app.css
//

@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    src: local('Open Sans'), local('OpenSans'), url(../../fonts/OpenSans.woff) format('woff');
}
@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 700;
    src: local('Open Sans Bold'), local('OpenSans-Bold'), url(../../fonts/OpenSans-Bold.woff) format('woff');
}
// main wrapper
#ckfinder_view {
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
}
// toolbar
#toolbar_view {
    background-color: rgb(241, 241, 241);
    border-bottom: 1px solid @color-admin-header-border;
}
.cke_skin_v1 .cke_toolbar_start {
    display: none;
}
// sidebar
#sidebar_container {
    border-color: rgb(241, 241, 241);
    cursor: col-resize;
}
.view h2 {
  background: rgb(241, 241, 241);
  border-bottom: 1px solid @color-admin-header-border;
}
// other
#panel_view .panel_widget .buttons {
    background: rgb(241, 241, 241);
}

