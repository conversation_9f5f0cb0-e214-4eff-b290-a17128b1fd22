// Bootstrap core variables and mixins
//@import "../vendors/less/bootstrap/variables.less";
//@import "../vendors/less/bootstrap/mixins.less";
// local variables
@import "_variables.less";
@import "_mixins.less";
@import "_animations.less";
@import "_fonts.less";

// BASIC GLOBAL STYLES
body {
    position: relative;
    font: 400 @font-size/1.6 @font-family; // line-height: 24/15
    color: @color-main;
    letter-spacing: -0.15px;
    text-align: left;
    background-color: @color-body-bg;
    &.no-scroll {
        height: 100%;
        overflow: hidden;
        //&:before {
        //    content: '';
        //    position: fixed;
        //    top: 0;
        //    left: 0;
        //    display: block;
        //    width: 100vw;
        //    height: 100vh;
        //    background-color: fade(@color-key, 50%);
        //    z-index: 2;
        //}
    }
}
body > #page {
    background-color: @color-page-bg;
}

a {
    color: @color-dark-green;
    text-decoration: underline;
    &:hover, &:focus {
        text-decoration: none;
        color: @color-highlighted;
    }
    &:active, &:focus {
        outline: 0;
    }
}
:active, :focus {
    outline: 0;
}
::-webkit-input-placeholder { /* WebKit, Blink, Edge */
    color: @color-black;
}
:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color: @color-black;
    opacity:  1;
}
::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: @color-black;
    opacity:  1;
}
:-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: @color-black;
}
hr {
    margin: 0;
    border: none;
    border-top: 1px solid @color-border;
    background: none;
}
h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
    margin-top: 0;
    margin-bottom: 0.84em;
    font: 700 58px/1.22 @font-family; 
    color: @color-dark-green;
    letter-spacing: -2.32px;
    a {
        text-decoration: none;
    }
    &.section-title {
        font-size: 58px;
    }
}
h2, .h2 {
    font: 700 25px/1.12 @font-family; // line-height: 28/25
    color: @color-dark-green;
    letter-spacing: -0.7px;
}
h3, .h3 {
    font: 700 23px/1.22 @font-family; // line-height: 28/23
    color: @color-dark-green;
    letter-spacing: -0.64px;
}
h4, .h4 {
    font: 700 18px/1.33 @font-family; // line-height: 24/18
    color: @color-black;
    letter-spacing: -0.5px;
}
h5, .h5 {
    font: 700 17px/1.41 @font-family; // line-height: 24/17
    color: @color-black;
    letter-spacing: -0.5px;
}
h6, .h6 {
    font: 700 16px/1.31 @font-family; // line-height: 21/16
    color: @color-black;
    letter-spacing: -0.32px;
}
b {
    font-weight: 700;
}
.lead {
    font-size: 18px;
}
.important {
    font-size: 18px;
}
small {
    font-size: 12px;
}
.vertically-centered {
    vertical-align: middle;
    & * {
        vertical-align: middle;
    }
}
.white {
    color: @color-white !important;
    * {
        color: @color-main !important;
    }

}
.highlighted {
    color: @color-highlighted !important;
    * {
        color: @color-highlighted !important;
    }
}
//.alt-font {
//    font-family: @font-family-1 !important;
//    * {
//        font-family: @font-family-1 !important;
//    }
//}
//blockquote {
//    margin: 0;
//    background: url('@{img-dir}/quotes-top-left-small.png') 1px 1px no-repeat;
//    font-style: italic;
//    font-weight: 400;
//    p {
//        margin: 0 0 0 40px;
//        padding: 17px 40px 17px 7px;
//        border-left: 1px solid #dfe0e2;
//    }
//}
.center {
    max-width: @center-width;
    margin: 0 auto;
    .clear;
    // nested .center blocks (e.g. in wrapping content blocks) 
    // should not have restricted width
    // ATTENTION: Important for nested content blocks implementation
    .center {
        max-width: none;
    }
}
.app-announcement {
    background-color: @color-dark-green;
    color: @color-white;
    box-shadow: #aaa 0 2px 5px;
    a {
        color: @color-white;
    }
    .title {
        &:hover {
            color: @color-white; // necessary otherwise it inherits red color
        }
    }
    .close {
        top: 9px;
    }
}

// LAYOUT TABLE
table.layout {
    width: 100%;
    border: none;
    table-layout: fixed;
    td {
        border: none;
        padding: 0;
        vertical-align: top;
    }
    &.centered td {
        text-align: center;
    }
    &.vertically-inside-bordered {
        td+td {
            border-left: 1px solid #dbdbdb;
        }
    }
    &.horizontally-inside-bordered {
        tr+tr>td {
            border-top: 1px solid #dbdbdb;
        }
    }
    &.top-bordered {
        td {
            border-top: 1px solid #dbdbdb;
        }
    }
}

// BUTTON
.button, button {
    display: inline-block;
    .border-radius(5px); // to make it just round
    border: none;
    height: 41px;
    line-height: 41px;
    padding: 0 22px 0 19px;
    font-family: @font-family;
    font-size: 14px;
    font-weight: 700;
    letter-spacing: 0px;
    color: @color-light-cream;
    text-decoration: none;
    cursor: pointer;
    white-space: nowrap;
    background: @color-dark-green;
    &.secondary {
        border: 1px solid @color-dark-green;
        line-height: 39px;
        color: @color-dark-green;
        background: @color-light-cream;
    }
}
a.button {
    &:hover, &:active, &:focus {
        color: @color-white;
    }
    &.secondary {
        &:hover, &:active, &:focus {
            color: @color-red;
        }
    }
}

a.info {
    font-family: @font-family;
    font-weight: 700;
    text-transform: uppercase;
    text-decoration: none;
}

/*// ARROW LINK
a.arrow {
    padding-right: 24px;
    font-size: 15px;
    font-weight: 400;
    color: @color-black;
    cursor: pointer;
    background: transparent url('@{img-dir}/arrow-right-blue.png') right center no-repeat;    
}*/

.clearfix:after {
  content: "";
  display: table;
  clear: both;
} 

// BASIC INPUT STYLES
.text-input {
    margin-bottom: 15px;
    border: 1px solid transparent;
    border-radius: 6px;
    height: 41px;
    padding: 0 5px 0 15px;
    font: 400 12px/1.25 @font-family; // line-height: 15/12
    color: @color-dark-grey;
    background: @color-white;
    &.invalid {
        border-color: @color-alert !important;
    }
    &::placeholder {
        color: @color-dark-grey-2;
    }
}
input.text-input {
    //line-height: 15px;
}
textarea.text-input {
    height: 7.13em;
    width: 770px;
    padding: 16px;
}
select.text-input {
    padding-right: 15px;
}
.text-input + .input-errors {
    margin-top: -13px;
    margin-bottom: 15px;
}
.input-wrapper {
    margin-bottom: 15px;
    .text-input {
        margin-bottom: 0;
        & + .input-errors {
            margin-top: 0;
            margin-bottom: 0;
        }
    }
}
label {
    display: block;
    padding-bottom: 9px;
    color: #000;
    text-align: right;
    .required-mark {
        padding-left: 3px;
        color: #000;
    }
}
.toggle-input {
    cursor: pointer;
    & + label {
        display: inline;
        margin-left: 5px;
        margin-right: 10px;
        cursor: pointer;
    }
}
.input-error {
    color: @color-alert;
    font-size: 12px;
}
// - EMPTY TEXT (PLACEHOLDER)
.input-empty-text-wrapper {
    position: relative; // .input-empty-text
    width: 279px;
    .input-empty-text {
        position: absolute;
        top: 14px;
        left: 18px;
    }
}
// - SECURITY CODE (CAPTCHA)
.sc-input {
    .sc-wrapper {
        position: relative; // .sc
        width: 279px;
        .sc {
            position: absolute;
            left: 12px;
            top: 7px;
            color: @color-dark-grey;
            img {
                vertical-align: middle;
                cursor: pointer;
            }
        }
        .sc-change {
            position: absolute;
            top: 8px;
            left: 245px;
            font-size: 10px;
            color: @color-dark-grey;
            cursor: pointer;
        }
        .text-input {
            padding-left: 115px;
            letter-spacing: 5px;
        }
        .input-empty-text {
            left: 92px;
        }
    }
    .label-wrapper {
        overflow: hidden;
        label {
            float: left;
        }
        .sc-change {
            padding-left: 3px;
            font-size: 10px;
            color: @color-highlighted;
            cursor: pointer;
        }
    }
}

// NICE TABLE
table.nice-table {
    width: 100%;
    border: none;
    table-layout: fixed;
    margin-left: auto;
    margin-right: auto;
    th, td {
        border: none;
        border: 1px solid @color-border-light;
        padding: 19px 20px 15px 20px;
        text-align: left;
        vertical-align: top;
        background: @color-white;
    }
    &.centered {
        th, td {
            text-align: center;
        }
    }
    &.vertically-centered {
        th, td {
            vertical-align: middle;
        }
    }
    &.opened {
        tr:last-child {
            th, td {
                border-bottom: none;
            }
        }
    }
    &.stripped {
        //tr:nth-child(even)>td {
        //    background-color: #fbf9fa;
        //}
    }
    &.stripped-odd {
        //tr:nth-child(odd)>td {
        //    background-color: #fbf9fa;
        //}
    }
}
// RESPONSIVE TABLE: use like div.responsive-table > table
.responsive-table {
    //width: 100%;
    min-height: .01%;
    overflow-x: auto;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
}

// NICE CHECKBOX AND RADIO
.nice-radio,
.nice-checkbox {
    .clear;
    position: relative;
    min-height: 20px;
    padding: 0 0 0 27px;
    input,
    .image {
        position: absolute;
        top: 0;
        left: 0;
        display: block;
        width: 20px;
        height: 20px;
        border-radius: 4px;
    }
    input {
        z-index: 1;
        opacity: 0;
        cursor: pointer;
        &:checked + .image {
            background-size: 18px;
        }
        &.invalid + .image {
            border: 1px solid @color-red;
        }
    }
    .image {
        display: block;
        width: 20px;
        height: 20px;
        border: 1px solid @color-grey;
        cursor: pointer;
        background: url('@{img-dir}/tick-sign.png') center no-repeat @color-white; // svg fallback
        background: url('@{img-dir}/tick-sign.svg') center no-repeat @color-white;
        background-size: 0;
        transition: all .2s;
    }
    label {
        color: @color-black;
        font-size: 14px;
        font-weight: 400;
        white-space: normal;
        text-align: left;
        line-height: 1.2;
        cursor: pointer;
        overflow: visible;
    }
    &:focus,
    &:hover {
        .image {
            border-color: @color-black;
        }
        input {
            &.invalid + .image {
                border: 1px solid @color-red;
            }
        }
    }
}
.nice-radio {
    .image {
        border-radius: 9999px;
        background: @color-white;
        &:before {
            content: '';
            display: block;
            width: 12px;
            height: 12px;
            border-radius: 9999px;
            background-color: @color-dark-green;
            margin: 3px 0 0 3px;
            transform: scale(0);
            transition: all .2s;
        }
    }
    input {
        &:checked {
            & + .image {
                &:before {
                    transform: scale(1);
                }
            }
        }
    }
}

// NICE LIST
ul.nice-list {
    list-style: none;
    padding-left: 14px;
    list-style-image: url('@{img-dir}/list-bullet.png');
    li {
        padding-left: 15px;
        //background: url('@{img-dir}/list-bullet.png') no-repeat 15px 5px;
        margin-bottom: 1.13em;
    }
}

// NICE IMAGE
img.nice-image {
    display: inline-block;
    .border-radius(14px);
}
img.nice-image-right {
    float: right;
    display: block;
    margin-left: 37px;
    margin-bottom: 20px;
    .border-radius(14px);
}
img.nice-image-left {
    float: left;
    display: block;
    margin-right: 37px;
    margin-bottom: 20px;
    .border-radius(14px);
}

// ROUND IMAGE
img.round-image {
    .border-radius(9999px);
}
img.round-image-bordered {
    border: 1px solid @color-border;
    .border-radius(9999px);
}

// SECTION HEADER (used inline in this file below)
.section-header {
    margin-bottom: 58px;
    border-bottom: 1px solid @color-dark-green;
    padding-top: 28px;
    padding-bottom: 30px;
    text-align: center;
    .title {
        font-size: 29px;
        font-weight: 700;
        letter-spacing: -0.7px;
    }
}

// ANIMATIONS
@keyframes fadeIn {
    0% {
        visibility: hidden;
        opacity: 0;
    }
    1% {
        visibility: visible;
        opacity: 0;
    }
    100% {
        visibility: visible;
        opacity: 1;
    }
}
@keyframes fadeOut {
    0% {
        visibility: visible;
        opacity: 1;
    }
    1% {
        visibility: visible;
        opacity: 1;
    }
    100% {
        visibility: hidden;
        opacity: 0;
    }
}
@keyframes menuOnStage {
    0% {
        display: none;
        opacity: 0;
        height: 0;
    }
    75% {
        display: block;
        opacity: 0;
        height: 0;
    }
    100% {
        display: block;
        opacity: 1;
        height: auto;
    }
}

// HEADER
.header-top {
    .main-contacts {
        float: right;
        margin-top: 24px;
    }
}
header.with-showcase {
    #header-top {
    }
}
.header-middle {
    padding-bottom: 37px;
    background-color: @color-main-grey;
    & > .center {
        display: flex;
    }
    .mini-cart {
        position: relative;
    }
}
//#header-bottom {
//    background: #f1edea url('@{img-dir}/header-bottom-bg.png') center top repeat-x;
//    min-height: 210px - 79px;
//    .center {
//        position: relative; //#mini-cart
//    }
//    #language-menu {
//        position: absolute;
//        top: 20px;
//        right: 20px; // keep menu text right align with center right edge
//    }
//    #language-dropdown-menu {
//        float: right;
//        margin-top: 30px;
//        margin-right: 61px;
//    }
//    #facebook-logo {
//        position: absolute;
//        width: 23px;
//        height: 23px;
//        top: 2px;
//        right: 20px;
//    }
//    #main-menu, .main-menu {
//        //position: absolute;
//        //width: 100%;
//    }
//}
//header.with-showcase {
//    #header-bottom {
//        background: #e6ded9 url('@{img-dir}/header-bottom-high-bg.png') center top repeat-x;
//        min-height: 765px- 79px;
//    }    
//}
.main-logo {
    //display: inline-block;
    display: flex;
    align-items: center;
    width: 333px;
    height: auto;
    margin: 3px auto 0 2px;
    img {
        &.default {
            display: block;
        }
        &.small {
            display: none;
        }
    }
}
// MAIN MENU
#main-menu, .main-menu {
    .center;
    text-align: center;
    text-transform: uppercase;
    padding: 12px 0 20px;
    ul {
        margin: 0;
        padding: 0;
    }
    a, span {
        display: block;
    }
    .level-01 {
        text-align: center;
        & > li {
            display: inline-block;
            position: relative; // .level-02
            margin: 0 71px / @center-width * 100%;
            & > a, & > span {
                text-decoration: none;
                font-weight: 800;
                color: @color-topmenu;
                letter-spacing: 2px;
                font-size: 12px;
                &:hover {
                    color: @color-highlighted; //@color-topmenu-hover;
                    text-decoration: none;
                }
            }
            &.first {
                margin-left: 0;
                & > a, & > span {
                }
            }
            &.last {
                margin-right: 0;
                & > a, & > span {
                }
            }
            &.open, &.active {
                & > a, & > span {
                    color: @color-highlighted;
                }
            }
            &:hover {
                cursor: pointer; // to make the hover work also for iOS touch event
                & > .level-02 {
                    display: block;
                }
            }
        }
        // if menu is inactive (none of its items is displayed) then show the first item submenu
        &.inactive {
            & > .first {
                //background: url('@{img-dir}/violet-triangle.png') center bottom no-repeat;
                .level-02 {
                    //display: block;
                }
            }

        }
    }
    .level-02 {
        @-background: rgba(255, 255, 255, 0.88);
        display: none;
        position: absolute;
        left: 0;
        z-index: 1;
        padding-top: 10px;
        text-align: left;
        &:before {
            content: '';
            display: block;
            position: absolute;
            top: 0px;
            left: 10px;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 10px 10px 10px;
            border-color: transparent transparent @-background transparent;
            -webkit-transform: scale(.9999);
            -moz-transform: scale(.9999);
            transform: scale(.9999);
        }
        & > .w {
            border: none;
            .border-radius(5px);
            //.border-top-left-radius(0);
            padding: 0 10px;
            background-color: @-background;
            & > li {
                position: relative; // .level-03
                float: none;
                display: block;
                min-width: 160px;
                & > a, & > span {
                    display: block;
                    padding: 0 6px 0 6px;
                    padding: 8px 3px;                
                    margin-left: -6px;
                    color: @color-black;
                    font-weight: 600;
                    text-transform: lowercase;
                    text-decoration: none;
                    border-bottom: 1px solid #c5c5c5;
                    &:hover {
                        color: @color-highlighted;
                        text-decoration: none;
                    }
                }
                &.last {
                    & > a, & > span {
                        border-bottom: none;
                    }
                }
                &.open, &.active {
                    & > a, & > span {
                        color: @color-highlighted;
                    }
                }
                &:hover {
                cursor: pointer; // to make the hover work also for iOS touch event
                    & > .level-03 {
                        display: block;
                    }
                }
            }
        }
    }
}
header.with-showcase {
    #main-menu, .main-menu {
        .level-02 {
            //.border-bottom-radius(0);
        }
    }
}

// USER MENU
.user-menu {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    height: 38px;
    margin: 34px 30px 0 43px;
    .menu-button {
        line-height: 38px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        height: 100%;
        cursor: pointer;
        transition: color .2s;
        &:hover,
        &:focus,
        &.active {
        }
        &.active {
            .menu-list-arrow {
                opacity: 1;
            }
        }
    }
    .menu-button-text {
        font-size: 14px;
        font-weight: 700;
        letter-spacing: -0.28px;
        line-height: 41px;
        position: relative;
        display: inline-block;
        max-width: 150px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        img {
            margin-right: 8px;
            position: relative;
            top: 4px;
            height: 21px;
        }
    }
    .menu-list-arrow {
        .expanded-menu-arrow(16px);
    }
    .menu-list {
        list-style: none;
        padding: 0;
        margin: 0;
        display: none;
        position: absolute;
        top: 46px;
        background-color: @color-light-grey-5;
        z-index: 2;
        border: 1px solid @color-light-grey-5;
        border-radius: 4px;
    }
    .menu-item {
        white-space: nowrap;
    }
    .menu-link {
        text-decoration: none;
        display: block;
        padding: 14px 40px;
        transition: color .2s, background-color .2s;
        color: @color-black;
        &:hover,
        &:focus {
            color: @color-highlighted;
        }
    }
}

// BONUS INFO
.bonus-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    height: 41px;
    margin: 34px 37px 0 0;
    .bonus-button {
        line-height: 41px;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        position: relative;
        height: 100%;
        cursor: pointer;
        transition: color .2s;
        &.user-not-logged {
            .bonus-value {
                color: @color-black;
                margin-left: -4px;
            }
        }
        &.bonus-activated {
            .bonus-pie {
                background: @color-blue;
                &:before { // semicircle section
                    display: none;
                }
                &:after { // tick sign icon
                    display: inline-block;
                }
            }
            .bonus-button-text {
                color: @color-black;
            }
        }
        &:hover,
        &:focus,
        &.active {
            color: inherit;
        }
        &.active {
            .bonus-menu-arrow {
                opacity: 1;
            }
        }
    }
    .bonus-button-text {
        color: @color-black;
        font-size: 14px;
        font-weight: 700;
        letter-spacing: -0.28px;
        line-height: 41px;
        position: relative;
        display: inline-block;
        max-width: 120px;
        white-space: nowrap;
        .bonus-progress {
            position: relative; // .bonus-full
            top: 4px;
            display: inline-block;
            overflow: hidden;
            width: 21px;
            height: 18px;
            margin-right: 12px;
            img {
                display: block;
            }
            .bonus-full {
                position: absolute;
                bottom: 0;
                width: 100%;
                height: 0;
                overflow: hidden;
                img {
                    position: absolute;
                    bottom: 0;
                }
            }
        }
    }
    .bonus-pie {
        color: @color-white;
        font-size: 12px;
        font-weight: 700;
        text-align: center;
        line-height: 22px;
        position: absolute;
        top: 20px;
        left: 16px;
        min-width: 22px;
        height: 22px;
        border-radius: 9999px;
        background: url('@{img-dir}/bonus-pie-bg.png') no-repeat; // svg fallback
        background: linear-gradient(to right, @color-white 50%, @color-highlighted 0);
        overflow: hidden;
        &:before { // semicircle section
            content: '';
            display: block;
            height: 100%;
            background-color: @color-white;
            margin-left: 50%;
            transform-origin: left;
            transform: rotate(30deg);
        }
        &:after { // tick sign icon
            .font-awesome-icon('\f00c');
            display: none;
            color: @color-white;
            font-size: 12px;
            line-height: 22px;
            vertical-align: middle;
        }
        &.over-50-percent {
            &:before {
                background-color: @color-blue;
            }
        }
    }
    .bonus-value {
        display: inline-block;
    }
    .bonus-menu-arrow {
        .expanded-menu-arrow(16px);
    }
    .bonus-menu {
        padding: 16px;
        display: none;
        position: absolute;
        top: 46px;
        background-color: @color-light-grey-5;
        z-index: 2;
        border: 1px solid @color-light-grey-5;
        border-radius: 4px;
    }
    .bonus-info-text {
        margin: 0 0 8px;
    }
    .button-wrapper {
        text-align: center;
    }
}

// LANGUAGE MENU
#language-menu {
    ul {
        display: block;
        margin: 0;
        padding: 0;
    }
    li {
        float: left;
        height: 27px;
        
        a {
            display: block;
            width: 27px;
            height: 27px;
            line-height: 27px;
            margin-left: 19px;
            border-radius: 9999px;
            text-transform: uppercase;
            font-weight: 700;
            font-size: 12px;
            text-align: center;
            color: @color-black;
            text-decoration: none;
            .transition-duration(@menu-transition-duration);
            @-properties: background, color;
            .transition-property(@-properties);
            &:hover {
                color: #161718;
                background-color:  @color-white;
            }
        }
        &.active {
            a {
                color: #161718;
                background-color:  @color-white;
            }
        }
    }
}

// LANGUAGE DROPDOWN MENU
#language-dropdown-menu {
    position: relative; //.dropdown
    //overflow: hidden;
    height: 40px;
    width: 71px;
    border: 1px solid @color-border;
    .border-radius(1px);
    //font-family: @font-family-1;
    font-size: 13px;
    color: @color-highlighted;
    .transition-duration(@menu-transition-duration);
    .transition-property(background-color);
    &:hover {
        background-color: #f7f4ea;
    }
    .active-item, li a {
        display: block;
        position: relative; //.icon
        height: 40px;
        line-height: 38px;
        padding-right: 11px;
        text-align: right;
        text-transform: uppercase;
        .icon {
            display: block;
            position: absolute;
            top: 0px;
            left: 0px;
            height: 38px;
            width: 34px;
            background-position: right center;
            background-repeat: no-repeat;
        }
    }
    .active-item {
        overflow: hidden; // text-indent: 9999em;
        text-indent: 9999em;
        background: url('@{img-dir}/violet-arrow-down.png') 47px center no-repeat;
    }
    ul.dropdown {
        display: none;
        position: absolute;
        top: 38px;
        left: -1px;
        z-index: 1;
        margin: 0;
        width: 71px;
        border: 1px solid @color-border;
        border-top: none;
        padding: 0;
        background-color: #f7f4ea;
    }
    li {
        a {
            border-top: 1px solid @color-border;
            .transition-duration(@menu-transition-duration);
            .transition-property(background-color);
            text-decoration: none;
            &:hover {
                background-color: #e9dfcc;
                text-decoration: none;
            }
        }
    }
}

// MINI SEARCH
.mini-search {
    display: flex;
    height: 41px;
    margin-top: 34px;
    .mini-search-mobile-button {
        display: none;
        width: 38px;
        height: 41px;
        .mini-search-icon {
            width: 100%;
            height: 100%;
            background: url('@{img-dir}/ico-search.svg') center no-repeat;
            filter: @color-by-filter-highlighted;
        }
        /*/
        &:before {
            .font-awesome-icon('\f002');
            color: @color-red;
            font-size: 24px;
            text-align: center;
            line-height: 41px;
            //position: absolute;
            //left: 0;
            width: 100%;
            height: 100%;
            margin-top: -2px;
            cursor: pointer;
        }
        /*/
    }
    .mini-search-form {
        display: flex;
    }
    .keywords-wrapper {
        width: 408px;
        height: 41px;
    }
    input[type="search"] {
        line-height: 41px;
        width: 100%;
        height: 100%;
        border: none;
        border-radius: 6px 0 0 6px;
        margin: 0;
    }
    .button-wrapper {
        color: @color-white;
        position: relative;
        width: 84px;
        height: 41px;
        border-radius: 6px;
        background-color: @color-dark-green;
        background-position: center center;
        background-repeat: no-repeat;
    }
    input[type="submit"] {
        color: @color-white;
        font-size: 16px;
        position: absolute;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
        background-color: transparent;
    }
    .products-index {
        display: none;
        position: fixed;
        top: 50px; // keep this in synchro with EshopProducts/miniSearch > setSuggestionsTop()
        z-index: 999;
        max-width: 1190px;
        max-height: ~"calc(100vh - 50px)"; // keep this in synchro with EshopProducts/miniSearch > setSuggestionsTop()
        background-color: @color-white;
        border: 1px solid #ccc;
        left: 0px;
        right: 0px;
        margin: 0px auto;
        &:before {
            display: block;
            content: '';
            position: absolute;
            top: 0;
            left: 480px;
            border: solid #ccc;
            border-width: 1px 1px 0 0;
            background: #fff;
            width: 8px;
            height: 8px;
            transform-origin: 0 0;
            transform: rotate(-45deg);        
        }
        .close {
            height: 16px;
            width: 16px;
            background-image: url(/app/img/basic/close.png);
            background-size: cover;
            position: absolute;
            top: 20px;
            right: 25px;
            &:hover {
                cursor: pointer;
            }
        }
        .scroll-wrapper {
            max-height: ~"calc(100vh - 50px)"; // keep this in synchro with EshopProducts/miniSearch > setSuggestionsTop()
            overflow-y: auto;
            padding: 50px;
        }
        .index {
            overflow-y: auto;
            .product {
                width: 1 / 6 * 93%;
                margin-left: 10px / @center-width * 100% !important;
            }
        }
        .button-wrapper {
            padding-top: 20px;
            text-align: center;
            background-color: transparent;
            display: flex;
            justify-content: center;
            width: 100%;
        }
        button {
            background-color: @color-red;
            color: #FFF;
            &:hover {
                cursor: pointer;
            }
        }
    }
}

// MAIN NAVIGATION
.main-navbar {
    display: none;
    .clear;
    position: relative; // important for main-menu-wrapper width
    z-index: 1; // to make the box-shadow visible
    height: 41px;
    border-radius: 6px;
    box-shadow: 0px 1px 0px @color-light-grey-2;
    margin-top: 0px;
    background-color: @color-white;
}
// ALL CATEGORIES MENU
.all-categories {
    display: none;
    float: left;
}
.all-categories-button {
    color: @color-white;
    font-size: 16px;
    font-weight: 700;
    text-align: center;
    line-height: 41px;
    text-overflow: ellipsis;
    display: inline-block;
    position: relative;
    height: 41px;
    padding: 0 56px 0 32px;
    border: 0;
    border-radius: 6px;
    background-color: @color-dark-green;
    cursor: pointer;
    &:after {
        .font-awesome-icon('\f078');
        display: block;
        position: absolute;
        top: 12px;
        right: 32px;
        width: 16px;
        height: 16px;
        transform: rotate3d(0, 0, 0, 0);
        transition: transform .2s;
    }
    &:hover,
    &:focus {
        background-color: @color-dark-green;
        transition: background-color .2s;
    }
    &.active {
        &:after {
            transform: rotate3d(0, 0, 1, 180deg);
        }
    }
}
.categories-menu {
    display: block;
    position: relative;
    z-index: 3;
    background-color: @color-white;
    border-radius: 0;
    font-family: "Inter", sans-serif;
    border-radius: 6px;
    padding: 7px 0px;
    .level-01 {
        .clear;
        display: flex;
        flex-wrap: wrap;
        list-style: none;
        padding: 0;
        margin: 0;
        overflow-y: auto;
        a {
            text-decoration: none;
            display: block;
            color: @color-black;
            &:hover, &:focus, &:active {
                
            }
        }
        .menu-img-wr {
            display: inline-block;
            width: 30px;
            text-align: center;
            flex-shrink: 0;
        }
        .icon {
            display: inline-block;
            max-width: 80%;
            vertical-align: middle;
            margin-right: 0;
            margin-right: 8px;
            margin-bottom: 2px;
        }
        .expand-button {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            top: 0;
            right: 8px;
            width: 18px;
            height: auto;
            float: right;
            margin-left: auto;
            &:after {
                //.font-awesome-icon('\f078');
                content: "";
                display: block;
                width: 4px;
                height: 4px;
                border-top: 1px solid #B7B7B7;
                border-left: 1px solid #B7B7B7;
                transform: rotate(135deg);
                transition: transform .2s;
            }
        }
        & > li {
            flex: 0 0 100%;
            padding: 0 0 0 0;
            min-height: 34px;
            & > a {
                display:flex;
                align-items: center;
                font-weight: 500;
                font-size: 13px;
                line-height: 16px;
                margin-bottom: 0px;
                padding: 5px 0 5px 13px;
                border-left: 2px solid transparent;
                min-height: 34px;
                &:hover{
                    font-weight: 700;
                    border-left: 2px solid #b4b4b4;
                    background: linear-gradient(to right, #DEDEDE , white);
                }
            }
            &:hover {
                & > .level-02 {
                    display: block;
                    min-height: 100%;
                    height: auto !important;
                }
            }
            &.active, &.open {
                font-weight: 700;
                background: linear-gradient(to right, #DEDEDE , white);
            }
        }
    }
    .level-02 {
        display: none;
        list-style: none;
        padding: 20px;
        margin: 0;
        position: absolute;
        z-index: 10;
        top: 0;
        left: 185px;
        width: 1130px;
        border-left: 1px solid @color-main-grey;
        border-bottom: 1px solid @color-main-grey;
        background: @color-white;
        border-radius: 6px;
        &.show{
            display: block;
            //flex-wrap: wrap;
        }
        a {
            display: inline-block;
            font-size: 14px;
            line-height: 20px;
            position: relative;
        }
        & > li {
            display: inline-block;
            width: 25%;
            padding: 20px 20px 20px 50px;
            float: left;
            position: relative;
            &:nth-of-type(4n+2) {
                clear: both;
            }
            & > a {
                font-weight: 700;
            }
            .menu-img-wr {
                position: absolute;
                top: -2px;
                left: -37px;
                .icon {
                    margin-right: 0px;
                    margin-bottom: 0px;
                    height: 30px;
                    width: 30px;
                    max-width: none;
                }
            }
        }
        .expand-button{
            display: none;
        }
        .heading01 {
            display: block;
            font-size: 22px;
            font-weight: 400;
            padding: 2px 13px 5px;
            width: 100%;
        }
    }
    .level-03 {
        display: block;
        padding: 0;
        margin: 5px 0 0;
        & > li {
            &.level-03-items-hidden{
                display: none;
            }
            &:hover{
            }
            & > a {
                font-weight: 400;
                font-size: 11px;
                line-height: 15px;
                margin-bottom: 0px;
                padding: 0;
            }
        }
        
    }
    .level-03-button-more {
        font-size: 10px;
        font-weight: 500;
        color: #d36868;
        padding: 2px 0;
        cursor: pointer;
    }
    .level-04,.level-05 {
        display: none;
    }
}
.products-index-side-menu{
    .categories-menu .level-01 > li:hover .level-02{
        display: block;
    }
}
.products-index-menu-wrapper{
    display:flex;
    width: 100%;
    margin-bottom: 30px;
    .products-index-side-menu{
        width: 180px;
        flex: 1 1 auto;
        align-content: flex-start;
    }
    .subcategories-wrapper{
        width: calc(~"100% - 180px");
        flex: 1 1 auto;
        align-content: flex-start;
        background: white;
    }
}
#content .categories-menu {
}

// TOP CATEGORIES MENU
.top-categories {
    float: right;
    width: 100%;
}
.top-categories-button {
    display: none;
    color: @color-white;
    font-size: 16px;
    font-weight: 700;
    text-align: center;
    line-height: 41px;
    text-overflow: ellipsis;
    height: 41px;
    padding: 0 16px;
    background-color: @color-dark-green;
}
.top-categories-list {
    .clear();
    list-style: none;
    height: 41px;
    padding: 0 0 0 0px;
    margin: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
}
.top-categories-item {
    float: left;
    height: 100%;
    &:hover, &:focus, &:active {
        .top-categories-link {
            color: @color-black;
            transition: color .2s;
        }
    }
    .child-items{
        display: none;
        position: absolute;
        z-index: 2;
        left: 0px;
        width: 100%;
        margin: 0;
        padding: 0 0 20px;
        border-radius: 6px;
        //border-top: 1px solid @color-main-grey;
        //border-right: 1px solid @color-main-grey;
        //box-shadow: 0px 1px 0px @color-light-grey-2;
        background: @color-white;
        &.show-submenu{
            display: block; 
        }
        & > li{
            width: 20%;
            float: left;
            padding: 25px 10px 0;
            &:nth-of-type(5n+1){
                clear: left;
            }
            .child-item-link{
                display: inline-block;
                padding: 0 0 0 60px;
                width: 100%;
                position: relative;
                img{
                    max-width: 60px;
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translate(0, -50%);
                }
                .child-item-name {
                    display: inline-block;
                    width: 100%;
                    color: @color-black;
                    font-weight: 700;
                    letter-spacing: -.28px;
                    text-decoration: none;
                    line-height: 20px;
                    padding: 12px 24px 12px 7px;
                    border-radius: 6px;
                    position: relative;
                    &:hover, &:focus, &:active {
                        color: @color-highlighted; 
                    }
                }
            }
            .grand-child-items{
                display: inline-block;
                padding: 0 0 0 60px;
                margin: 5px 0;
                width: 100%;
                .grand-child-item-link{
                    display: inline-block;
                    width: 100%;
                    img{
                        display: none;
                    }
                    span{
                        display: inline-block;
                        width: 100%;
                        color: @color-black;
                        font-size: 12px;
                        font-weight: 400;
                        letter-spacing: -.28px;
                        text-decoration: none;
                        line-height: 16px;
                        padding: 4px 7px;
                        border-radius: 6px;
                        &:hover, &:focus, &:active {
                            color: @color-highlighted; 
                        }
                    }
                }
            }
        }
    }
    .expand-button {
        display: none;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 0;
        right: 0;
        width: 52px;
        height: 100%;
        &:after {
            .font-awesome-icon('\f078');
            display: block;
            position: absolute;
            width: 16px;
            height: 16px;
            transform: rotate3d(0, 0, 0, 0);
            transition: transform .2s;
        }
        &.show-submenu {
            &:after {
                transform: rotate3d(0, 0, 1, 180deg);
            }
        }
    }
}
.top-categories-link {
    color: @color-red;
    font-weight: 700;
    letter-spacing: -0.28px;
    line-height: 41px;
    white-space: nowrap; // important for JS calculations
    text-decoration: none;
    display: block;
    width: 100%; // important for JS calculations
    height: 100%;
    padding: 0 8px;
    cursor: pointer;
    border-radius: 6px;
    background: @color-white;
    text-align: center;
}
.top-categories-text {
    display: inline-block;
}
.top-categories-extra-item {
    display: none;
    float: left;
    position: relative;
    width: 41px;
    height: 41px;
    &.active {
        display: block;
        //&:hover,
        //&:focus {
        //    .top-categories-extra-menu {
        //        visibility: visible;
        //        animation: fadeIn .2s ease-out;
        //    }
        //}
    }
}
.top-categories-extra-button {
    color: @color-red;
    position: relative;
    display: inline-block;
    width: 41px;
    height: 41px;
    cursor: pointer;
    //background: red;
    &:after {
        .font-awesome-icon('\f054');
        text-align: center;
        display: block;
        position: absolute;
        top: 13px;
        left: 13px;
        width: 16px;
        height: 16px;
        transform: rotate3d(0, 0, 0, 0);
        transition: transform .2s;
    }
    &:hover,
    &:focus {
        color: @color-black;
        transition: color .2s;
    }
    &.active {
        &:after {
            transform: rotate3d(0, 0, 1, 180deg);
        }
    }
}
.top-categories-extra-menu {
    display: none;
    position: absolute;
    top: 42px;
    right: 0;
    list-style: none;
    padding: 0;
    margin: 0;
    border-radius: 6px;
    background-color: @color-white;
    z-index: 2;
    .top-categories-item {
        float: none;
        width: 100%;
        display: block;
        &:nth-child(n){
            .top-categories-link {
                border-right: none;
            }
        }
    }
    .top-categories-link {
        text-align: left;
    }
}


 // HAMBURGER
.hamburger {
    display: none;
    justify-content: center;
    align-items: center;
    width: 38px;
    height: 38px;
    cursor: pointer;
    border: 0;
    margin: 10px 0 0 8px;
    overflow: visible;
    .box {
        width: 30px;
        height: 24px;
        position: relative;
    }
    .bar {
        display: block;
        top: 2px;
        margin-top: -2px;
        &,
        &::before,
        &::after {
            width: 30px;
            height: 4px;
            background-color: @color-black;
            position: absolute;
            border-radius: 2px;
            transition-property: transform, left, background-color, opacity;
            transition-duration: 0.2s;
            transition-timing-function: ease;
        }
        &::before,
        &::after {
            content: "";
            display: block;
        }
        &::before {
            top: 10px;
        }
        &::after {
            top: 20px;
        }
    }
    &:hover {
        .box {
            .bar {
                &,
                &::before,
                &::after {
                    background-color: @color-dark-green;
                }
                &::before {
                    left: 3px;
                }
            }
        }
    }
    &.active {
        .bar {
            transform: translate3d(0, 10px, 0) rotate(-45deg);
            &::before {
                transform: rotate(45deg) translate3d(5.71429px, -6px, 0);
                opacity: 0;
            }
            &::after {
                transform: translate3d(0, -20px, 0) rotate(90deg);
            }
        }
    }
}
//.hamburger {
//    width: 30px;
//    height: 30px;
//    background: red;
//    &:before {
//        .font-awesome-icon('\f0c9');
//        display: inline-block;
//    }
//}


// HEADER TEXT
#header-text {
}

// SHOWCASE
#showcase {
    position: relative; // .label, #promotion
    .center;
    &.with-image > img {
        display: block;
        width: @center-width;
    }
    &.with-slider, &.with-image {
        .label {
            position: absolute;
            bottom: 0;
            width: 100%;
            padding: 59px 0px 100px 0px;
            font-size: 16px;
            color: #c6c6c6;
            background-color: @color-transparent-mask;
            h1, .h1, h2, .h2 {
                margin-bottom: 75px;
                font-size: 34px;
                font-weight: 300;
                letter-spacing: 3px;
                color: @color-white;
            }
        }
    }
    &.with-products {
        .label {
            font-size: 34px;
            font-weight: 300;
            color: #000;
            letter-spacing: 4px;
            text-align: center;
            padding: 0 @grid-cols-gap-width 72px @grid-cols-gap-width;
        }
    }
}
.-cke-showcase-label {
    padding: 59px 0px 100px 0px;
    font-size: 16px;
    color: #c6c6c6;
    background-color: @color-transparent-mask;
    h1, .h1, h2, .h2 {
        margin-bottom: 75px;
        font-size: 34px;
        font-weight: 300;
        letter-spacing: 3px;
        color: @color-white;
    }
}

// SLIDER
.slider {
    border-radius: 6px;
    overflow: hidden;
    .slide {
        .smart-image-with-content {
            position: relative; // .content, &:before
            overflow: hidden;
            border-radius: 6px;
            background-repeat: no-repeat;
            background-size: cover;            
            background-position: center;
            text-align: left;
            &.veiled-image {
                &:before {
                    content: '';
                    display: block;
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    z-index: 1;
                    background-color: #000;
                    opacity: 0.32;
                }
            }
            .wrapper {
                text-decoration: none; // bug fix for the grid used here below
                // stock .image and .content on top of each other by using .grid
                // (see https://stackoverflow.com/q/6780614/1245149 )
                // This lets both .image and .content as regular .wrapper content
                // which means that .wrapper contains both (which is not true if .content
                // is absolutely positioned over .image)
                display: grid;
                & > * {
                    grid-row: 1;
                    grid-column: 1;
                }
            }
            .image {
                display: block;
                min-width: 100%;
                height: 100%;
                // hidden image is used just to properly size the .wrapper block,
                // the image itself is displayed as article background to profit
                // from background-size: cover; once the whole image cannot be displayed
                visibility: hidden;
            }
            .content {
                position: relative;
                z-index: 2; // to place .content over image veil (article:before)
                padding-top: 26px / @center-width * 100%;
                padding-right: 26px / @center-width * 100%;
                padding-bottom: 26px / @center-width * 100%;
                padding-left: 43px / @center-width * 100%;
                .title {
                    font-size: 25px;
                    line-height: 28/25;
                    letter-spacing: -0.7px;
                    font-weight: bold;
                    color: @color-light-cream;
                    margin-bottom: 18px;
                }
                .text {
                    font-size: 14px;
                    line-height: 21/14;
                    letter-spacing: -0.39px;
                    color: @color-light-cream;
                    margin-bottom: 18px;
                    max-width: 470px;
                }
                .button {}
            }
        }    
    }
}

// SLIDESHOW (WOWSLIDER TWEAKS)
#showcase {
    #wowslider-container1 {
        // place it at the bottom
        z-index: 0; 
        .ws-title {
            display: none;
            left: 19px;
            bottom: 37px;
            width: 260px;
            font-size: 15px;
            color: @color-white;    
        }
        .ws_prev, .ws_next {
            display: none;
            position: absolute;
            top: 291px;
            left: 405px;
            .border-radius(9999px);
            height: 40px;
            width: 70px;
            background-image: url('@{img-dir}/prev.png');
            background-position: center 11px;
            background-size: auto;
            background-repeat: no-repeat;
            &:hover {
                background-color: @color-highlighted;
                background-position: center -29px;
                .transition-duration(@transition-duration);
                .transition-property(background-color);
            }
        }
        .ws_next {
            left: 465px;
            background-image: url('@{img-dir}/next.png');
        }
        .navigation-bg {
            position: absolute;
            top: 274px; //291px;?
            left: 405px;
            .border-radius(9999px);
            height: 40px;
            width: 130px;
            background-color: transparent;
            .transition-duration(@transition-duration);
            .transition-property(background-color);
        }
        &:hover {
            cursor: pointer; // to make the hover work also for iOS touch event
            .ws_prev, .ws_next {
                display: block;
            }
            .navigation-bg{
                background-color: fade(@color-white, 75%);
            }
        }
        .ws_bullets {
            display: none;
            bottom: 10px;
            left: 19px;
            div {
                left: 0;
            }
            a {
                background: url('@{img-dir}/slider-bullets.png');
            }
        }
        .label {
            z-index: 10;
        }
    }
}

// PROMOTION
#promotion {
    position: absolute;
    right: -85px;
    top: 192px;
    width: 170px;
    text-align: center;
    color: @color-white;
    .vertical-spacer {
        position: relative;
        //margin-top: -50%;
        min-height: 170px;
        //max-height: 380px;
        max-height: 330px;
        padding-top: 30px;
        padding-bottom: 30px;
        background: #725a42;
        .border-radius(9999px);
    }
    .title {
        //font-family: @font-family-1;
        font-size: 35px;
        line-height: 35px;
        text-transform: uppercase;
        .border {
            display: inline-block;
            border-bottom: 1px solid #896c4f;
            .border {
                padding: 0 4px 15px 6px;
                margin-bottom: 2px;
            } 
        }
    }
    .resume {
        margin: 15px 15px 20px 15px;
    }
    .read-more {
        //font-family: @font-family-1;
        font-weight: 700;
        color: @color-white;
        text-transform: uppercase;
        text-decoration: none;
    }
}
#promotion .resume,
.-cke-promotion-resume {
    color: @color-white;
    text-align: center;
    max-height: 166px;
    overflow: hidden;
    text-overflow: ellipsis; 
    background: #725a42;
}
.-cke-promotion-resume {
    width: 140px;
    min-width: 140px;
}

// CATEGORIES DROPDOWN MENU
.categories-dropdown-menu {
    position: relative;
    .button {
        height: 38px;
        line-height: 37px;
        border-radius: 6px 0 0 6px;
    }
    &:hover {
        .content {
            display: block;
        }
    }
    .content {
        border-top: 1px solid @color-light-grey-3;
        border-right: 1px solid @color-light-grey-3;
        border-bottom:1px solid @color-light-grey-3;
        position: absolute;
        width: 185px;
        z-index: 10000;
        display: none;
        .level-02 {
            border-left: 0;
        }
    }
}

.categories-dropdown-and-breadcrumbs {
    display: flex;
    .-run-breadcrumbs {
        width: 100%;
        border-radius: 0 6px 6px 0;
    }
}

// BREADCRUMBS
.-run-breadcrumbs {
    max-width: @center-width;
    margin: 0 auto 15px auto;
    padding: 9px 0 9px 10px;
    border-radius: 6px;
    font-size: 12px;
    letter-spacing: -0.24px;
    line-height: 15/12;
    text-decoration: underline;
    background: @color-white;
    .clear;
    > * {
        display: inline-block;
        vertical-align: middle;
    }
    a {
        color: @color-black;
        text-decoration: underline;
        &:hover {
            text-decoration: underline;
        }
    }
    .home-item {
        position: relative;
        top: -2px;
        font-size: 0;
        &:before{
            content: '';
            display: inline-block;
            position: relative;
            top: 3px;
            background-repeat: no-repeat;
            background-image: url('@{img-dir}/home.png');
            background-size: 23px;
            width: 23px;
            height: 20px;
            filter: @color-by-filter-highlighted;
        }
    }
    .item:first-letter {
        text-transform: uppercase;
    }
    .separator {
        width: 37px;
        height: 13px;
        text-align: center;
        //overflow: hidden; // text-indent: 9999em;
        //text-indent: 9999em;
        background: url('@{img-dir}/breadcrumb-arrow.png?ts=220223') center no-repeat;
        &:after {
            //content: '>';
            //display: inline;
            //.font-awesome-icon('\f105');
        }
    }
}

// MULTIFILTER

//.checkbox {
//    padding-left: 4px;
//    padding-top: 6px;
//}
.multifilter {
    .text-input {
        border-color: @color-light-grey;
        color: #373A3C;
        padding-right: 33px;
        margin-bottom: 0;
    }
    .products-index-type-select {
        flex: 0 0 80px;
        //display: inline-block;
        margin-top: 1px;
        //width: 80px;
        //vertical-align: top;
    }
    .filter-bar {
        //position: relative;
        padding: 0 10px;
        min-height: 38px;
        text-align: left;
        display: flex;
        flex-direction: row;
        .right-controls-wrapper {
            text-align: right;
            width: 100%; //calc(~"100% - 90px");
            display: inline-block;
        }
    }
    .price-sort-select {
        display: inline-block;
        margin: 0 9px;
    }
    .price-slider {
        display: inline-block;
        position: absolute;
        left: 36%;
        .min-price, .max-price{
            width: 65px;
            height: 35px;
            line-height: 35px;
            background: @color-white;
            border: 1px solid @color-grey;
            text-align: center;
            float: left;
        }
        .max-price{
        }
        .slider-axis{
            width: 120px;
            height: 35px;
            display: inline-block;
            position: relative;
            top: 0px;
            margin: 0 15px 0 25px;
            float: left;
        }
        .ui-slider{
            top: 16px;
            background: @color-grey;
            border: none;
            .ui-state-default, .ui-widget-content .ui-state-default {
                background: #aaa;
                border: 1px solid #aaa;
                cursor: pointer;
            }
            .ui-slider-handle {
                width: 20px;
                height: 20px;
                border-radius: 10px;

            }
            .ui-slider-range {

            }
        }
        .ui-slider-horizontal {
            height: 6px;
            .ui-slider-handle {
                top: -7px;
                margin-left: -15px;
            }
        }
        .ui-widget-header {
            background: #e4ab52;
        }
        .ui-state-hover,
        .ui-widget-content .ui-state-hover,
        .ui-widget-header .ui-state-hover {
            //background: #e7dcc6;
            //border: 1px solid #e7dcc6;
        }
    }
    a.display-extended-filter-btn {
        float: right;
        display: block;
        position: relative;
        height: 38px;
        line-height: 34px;
        padding: 0 28px 0 16px;
        font-size: 16px;
        font-weight: bold;
        //margin: 0 6px;
        margin: 0;
        letter-spacing: -0.5px;
        &:hover {
            color: @color-white;
        }
        &:after {
            border-bottom: 2px solid @color-white;
            border-right: 2px solid @color-white;
            content: '';
            display: block;
            height: 10px;
            margin-top: -6px;
            pointer-events: none;
            position: absolute;
            right: 12px;
            top: 50%;
            -webkit-transform-origin: 66% 66%;
            -ms-transform-origin: 66% 66%;
            transform-origin: 66% 66%;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
            -webkit-transition: all 0.15s ease-in-out;
            transition: all 0.15s ease-in-out;
            width: 10px; 
        }
        &.is-active {
            &:after {
                -webkit-transform: rotate(-135deg);
                -ms-transform: rotate(-135deg);
                transform: rotate(-135deg); 
            }
        }
    }
    #cancel-filter-btn {
        float: right;
        height: 35px;
        line-height: 35px;
        padding: 0 22px 0 18px;
        margin-bottom: 0px;
        &:after {
            content: '';
            display: block;
            position: absolute;
            top: -1px;
            right: 0;
            width: 35px;
            height: 35px;
            //background-image: url('@{img-dir}/cross-white.png');
            background-repeat: no-repeat;
            background-position: center center;
            transform: rotate3d(0, 0, 1, 0deg);
            transition: transform .4s;
        }
    }
    .extended-filter {
        //display: none;
        font-size: 0;
        margin-top: 3px;
        //overflow: visible !important;
        padding: 7px 10px 0 10px;
        border-top: 1px solid @color-white;
        //height: 0;
        //overflow: hidden;
        &.is-active {

        }
        //&:before,
        //&:after {
        //    content: '';
        //    display: block;
        //    width: 100%;
        //    height: 12px;
        //}
    }

    .multiselect {
        font-size: 14px;
        position: relative;
        display: inline-block;
        vertical-align: top;
        min-height: 35px;
        width: 100%;
        max-width: 160px;
        margin: 0 14px 10px 0;
        //&:first-child {
        //    margin-right: 1%;
        //}
        .multiselect-title {

        }

        .multiselect-dropdown {
            min-height: 35px;
            position: absolute;
            width: 100%;
            &:hover {
                .multiselect-list {
                    visibility: visible;
                }
            }
        }
        .multiselect-button {
            display: inline-block;
            color: #373A3C;
            cursor: pointer;
            text-align: left;
            padding: 0 30px 0 14px;
            height: 38px;
            line-height: 36px;
            min-width: 160px;
            margin-bottom: 0;
            border-radius: 4px;
            border: 1px solid @color-light-grey;
            background: url('@{img-dir}/select.png') right 10px bottom 13px no-repeat #fff;
            //background-position: right 0px bottom 0px;
            width: 100%;
        }
        .multiselect-list {
            // display: none;
            visibility: hidden;
            position: absolute;
            z-index: 2;
            width: 100%;
            max-height: 230px;
            //border-radius: 4px;
            border: 1px solid @color-light-grey;
            background-color: #fff;
            overflow: auto;
            //box-shadow: 0 0 20px #E5E5E5;
            transition-delay: 0.1s;

            //border-radius: 0px;
            //border: 0;
            background-color: rgba(255, 255, 255, .9);
            //box-shadow: none;
            transition-delay: .1s;
            padding: 5px 0 1px;
            margin: 0;
        }
        .checkbox {
            margin-top: 40px;
        }
        li{
            padding: 0 10px;
        }
        .selected-items{
            padding: 5px 11px 0;
            margin-top: 35px;
            width: 100%;
            .clear;
            .sel-item{
                width: 100%;
                float: left;
                margin-bottom: 1px;
                span{
                    display: inline-block;
                    width: calc(~'100% - 25px');
                    float: left;
                    margin-right: 8px;
                }
                a {
                    cursor: pointer;
                    display: block;
                    outline: 0 none;
                    background: url('@{img-dir}/close.png') no-repeat 1px 1px;
                    height: 14px;
                    width: 14px;
                    float: right;
                    margin-top: 3px;
                    opacity: 0.7;
                    &:hover{
                        //background-position: 0px -14px;
                        opacity: 1;
                        filter: brightness(0.2);
                    }
                }
            }
        }
        .label-text-right {
            color: #373A3C;
            font-weight: 400;
            display: inline-block;
            height: 20px;
            line-height: 20px;
        }
        &.checkbox {
            //margin: 10px 0 0;
            width: auto;
            min-height: 27px;
            margin: 8px 25px 0 0;
            //width: 69px;
        }
    }
}

// CONTENT
#content {
    //padding-top: 60px;
    position: relative; // to display over .label in #showcase
    z-index: 0; // to display .main-header main menu dropdown over #content
    /*background: 
        url('@{img-dir}/bg_ico9.png') 1470/1570 * 100% 85px no-repeat,      // mapa
        url('@{img-dir}/bg_ico2.png') 60/1570 * 100% 830px no-repeat,       // termoska
        url('@{img-dir}/bg_ico3.png') 1500/1570 * 100% 1120px no-repeat,    // cajnik
        url('@{img-dir}/bg_ico.png') 110/1570 * 100% 1230px no-repeat,      // stol
        url('@{img-dir}/bg_ico4.png') 1450/1570 * 100% 1590px no-repeat,    // hory
        url('@{img-dir}/bg_ico8.png') 40/1570 * 100% 1870px no-repeat,      // karavan 
        url('@{img-dir}/bg_ico6.png') 1460/1570 * 100% 2100px no-repeat,    // kompas
        url('@{img-dir}/bg_ico7.png') 1510/1570 * 100% 2810px no-repeat,    // strom
        url('@{img-dir}/bg_ico5.png') 110/1570 * 100% 2840px no-repeat,     // flasa 
        url('@{img-dir}/bg-road-texture.png') top center;*/
    border-bottom: 1px solid @color-main-grey; // avoid margin overflow
    #content-main {
        //.clear;
        float: right;
        //margin-right: -@main-content-width; // use this if content-main is placed in content-side (to stretch height of side content)
        //width: @main-content-width / @center-width * 100%;
        width: @main-content-width;
        //margin-top: 16px;
        // Scope of clearing is on the closest floated parent.
        // If there is no floated parent then clearing is global.
        //> .clearing-scope {
        //    float: left;
        //    width: 100%;
        //}
    }
    #content-side {
        .clear;
        float: left;
        //margin-left: -@side-content-width;
        //width: @side-content-width / @center-width * 100%;
        width: @side-content-width;
        //margin-top: 16px;
        
        > .spacer {
            position: relative;
            margin-top: 0; //-16px;
            padding-bottom: 30px;
        }
    }
    #content-bottom {
        padding-top: 60px;
        padding-bottom: 58px;
        background: @color-white;
        .center {
            max-width: @bottom-content-center-width;
        }
    }
}
.-cke-content-main {
    width: @main-content-width;
    min-width: @main-content-width;
}
.-cke-content-side {
    width: @side-content-width - 80px;
    min-width: @side-content-width - 80px;
}

// CONTENT TAB
.content-tab {
    .clear;
    margin-top: -28px;
    > * {
        float: left;
    }
    .tab-left-side {
        width: 51px;
        height: 28px;
        background: url('@{img-dir}/tabstart.png') no-repeat left top;
    }
    .tab-right-side {
        width: 51px;
        height: 28px;
        background: url('@{img-dir}/tabend.png') no-repeat right top;
    }
    .tab-content {
        padding-top: 13px;
        background-color: #fcfbf9;
        text-transform: uppercase;
        color: #93122d; 
        font-weight: 700;
        margin-bottom: 17px;
        font-size: 14px;
        letter-spacing: 1px;        
    }
    &.empty {
        display: table;
        width: 200px;
        table-layout: fixed;
        > * {
            float:none;
            display: table-cell;
        }
        .tab-content {
            width: 100%;
        }
    }
}
.content-tab {
    position: relative; //.-run-breadcrumbs
    .filter-menu {
        margin-top: -6px;
        margin-left: -14px;
        margin-bottom: 30px;
        + .-run-breadcrumbs {
            position: absolute;
            margin-top: 5px;
            margin-bottom: 0;
            left: 0;
        }
    }
}


// FOOTER
footer {
    color: @color-main-light;
    background-color: @color-main-grey;
    .footer-blog-news {
        padding: 32px 0 36px 0;
        background: @color-light-cream;
    }
    .footer-most-searched {
        display: none;
        padding: 32px 0 39px 0;
        background: @color-white;
    }
    .footer-badges {
        padding: 65px 0 31px 0;
        background: @color-light-cream;
        .center {
            padding: 0 10px;
        }
    }
    .footer-announcement {
        padding: 13px 0 14px 0;
        color: @color-white;
        font-size: 16px;
        line-height: 20/16;
        font-weight: 700;
        background: @color-green;
        a {
            color: @color-white;
        }
    }
    .footer-main {
        padding: 54px 0 70px 0;
        color: @color-white;
        background-color: @color-dark-grey-3;
        background-image: url('@{img-dir}/mapbg.svg');
        background-repeat: no-repeat;
        background-position: right top;
        .center{
            padding: 0 @narrow-center-indent;
            .spacer {
                max-width: 770px;
            }
        }
        .col1, .col2, .col3, .col4 {
            display: inline-block;
            vertical-align: top;
        }
        .col2, .col3, .col4 {
            padding: 50px 10px 0 0px;
            font-weight: 500;
            font-size: 14px;
            line-height: 18/14;
            letter-spacing: -0.28px;
            b{
                font-weight: 500 !important;  
            }
        }
        .col1 {
            padding: 0px;
            width: 100%;
            border-bottom: 2px solid @color-white;
        }
        .col2 {
            width: 39%;
            li{
                padding-bottom: 11px;
            }
            .footer-column-title{
                padding-bottom: 23px;
            }
        }
        .col3 {
            width: 39%;
            td{
                text-align: left;
                vertical-align: top;
                padding-bottom: 5px;
                line-height: 18px;
                &.first-col{
                    text-align: right;
                    padding-right: 10px;
                }
            }
        }
        .col4 {
            width: 22%;
            .footer-address{
                display: block;
                margin-bottom: 10px;
            }
        }
        .footer-column-title{
            display: block;
            font-size: 14px;
            font-weight: 800;
            line-height: 18/14;
            letter-spacing: -0.28px;
            padding-bottom: 22px;
        }
        .logo {
            display: block;
            margin-left: -6px;
        }
        #social-networks-menu {
            float: none;
            margin-left: 0px;
            margin-top: 39px;
            li {
                a {
                    color: @color-white;
                }
            }
        } 
        .recommended {
            display: none;
            margin-top: 24px;
            .title {
                font-size: 14px;
                line-height: 21/14;
                margin-bottom: 3px;
            }
        }
    }
    .footer-payment-methods {
        padding: 23px 0 8px 0;
        background: @color-white;
        .payment-methods {
            //display: flex;
            //flex-direction: row;
            //justify-content: flex-start;
            //align-items: center;
            margin: 0 -10px;
            a {
                display: inline-block;
                vertical-align: middle;
                padding: 0 10px 10px;
            }
        }
    }
    .footer-copyright {
        padding: 13px 0 14px 0;
        background: @color-main-grey;
    }
    
}

// BADGES MENU
.badges-menu {
    ul {             
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }
    li {
        padding: 0 16px 20px 0;
        a { 
            display: flex;
            justify-content: flex-start;
            align-items: center;
            color: @color-dark-green;
            font-size: 18px;
            font-weight: 700;
            line-height: 20px;
            letter-spacing: -0.5px;
            text-decoration: none;
            img {
                margin-right: 7%;
            }
        }
    }
}

// FOOTER MENU
.footer-menu {
    ul {             
       overflow: hidden;
       margin: 0;
       padding: 0;
    }
    li {
        a, span {
            display: block;
            margin-bottom: 6px;
            padding: 3px 0;
            text-decoration: none;
            color: @color-white;
            &:hover {
                text-decoration: underline;
            }
        }
        &.active {
            a, span {
                font-weight: 700;
            }
        }
    }
}

// RECOMMENDED MENU
.recommended-menu {
    ul {             
       overflow: hidden;
       margin: 0;
       padding: 0;
    }
    li {
        display: inline-block;
        padding-right: 8px;
    }
}

// LOGIN
#login {
    .login-header {
        .section-header;
    }
}
// LOGIN FORM
.login-form, .forgotten-password-form {
    @-width: 300px;
    width: @-width;
    margin: 0 auto;
    padding-bottom: 35px;
    .title {
        font-weight: 700;
        text-align: center;
    }
    .text-input {
        display: block;
        width: @-width;
        margin-bottom: 25px;
        &[type="password"] {
            margin-bottom: 20px;
        }
    }
    .permanent-login {
        font-size: 12px;
        label {
            position: relative;
            top: -2px;
        }
    }
    .forgotten-password {
        //font-size: 12px;
        display: block;
        text-align: right;
        //float: right;
    }
    .footer {
        margin-top: 25px;
        text-align: center;
    }
}
// REGISTRATION LINK
.registration-link, 
.start-quick-order {
    text-align: center;
    padding-bottom: 35px;
    .title {
        font-weight: 700;
    }
    .button, button {
        margin-top: 25px;
    }
}

// USER PROFILE
.user-profile {
    padding-bottom: 35px;
    .user-profile-header {
        .section-header;
    }
    .title {
        font-weight: 700;
    }
    .required-mark {
        color: @color-alert;
        font-weight: 700;
    }
    .subject-type-switch, .delivery-address-switch {
        margin-bottom: 15px;
        .nice-radio {
            display: inline-block;
            margin-right: 16px;
        }
    }
    .form-section {
        margin-bottom: 35px;
    }
    .form-note {
        font-style: italic;
        //font-size: 12px;
        //opacity: 0.8;
    }
    .form-bottom {
        margin-top: 35px;
        text-align: center;
    }
    .toggle-input + .input-errors {
        display: inline-block;
        position: relative;
        top: -2px;
        margin-left: 3px;
    }
    input[type="text"], input[type="password"] {
        min-width: 250px;
    }
    .gdpr-note {
        color: @color-grey;
        display: block;
        a {
            color: inherit;
            transition: color .2s;
            &:hover,
            &:focus {
                color: @color-red;
            }
        }
    }
}
.user-profile-menu + .user-profile {
    .user-profile-header {
        display: none;
    }
}

// SOCIAL NETWORKS MENU
#social-networks-menu {
    float: left;
    display: block;  
    ul {  
       margin: 0;
       padding: 0;
        list-style-type: none;
    }
    li {
        display: inline-block;
        a {
            color: @color-red;
            font-size: 24px;
            line-height: 32px;
            display: block;
            padding: 0 34px 10px 0;
            background-color: transparent !important;
            &:hover,
            &:focus {
                color: @color-red;
            }
            div {
                height: 32px;
                width: 32px;
                background-repeat: no-repeat;
                background-position: center;
            }
        }
        &:last-of-type{
            a{
                padding: 0 0px 10px 0;

            }
        }
    }
}

#social-networks-floating-menu {
    position: fixed;
    top: 120px;
    right: 0px;
    z-index: 99999;
    width: 50px;    
    > ul {             
        margin: 0;
        padding: 0;
        > li {
            display: block;
            margin: 0;
            padding: 0;
            > a {
                display: block;
                width: 50px;
                height: 50px;
                padding: 0;
                margin-bottom: 1px;
                text-align: center;
                line-height: 50px;
                font-size: 22px;
                background-color: #8e8e93;
                color: #fff;
                transition: all 250ms;     
                &:hover {
                    width: 70px;
                    padding-right: 20px;
                    margin-left: -20px;
                }
            }
        }
    }
}
// TOP HEADER MAIN CONTACTS
.main-contacts {
    .contacts-list {
        list-style-type: none;
        padding: 0;
        margin: 0;
        .item {
            display: inline-block;
            &.primary-mail {
                margin-right: 37px;
            }
            &.primary-phone {
                margin-right: 37px;
            }
            &.primary-hours {
                a {
                    float: right;
                }
                img{
                    width: 14px;
                }
            }
            img{
                margin-right: 8.5px;
                position: relative;
                top: 1px;
            }
        }
        a {
            color: @color-black;
            text-decoration: none;
            font: 500 13px/1.23 @font-family; // line-height: 24/15
            letter-spacing: -0.26px;
            display: block;
            &:hover,
            &:focus {
                color: @color-dark-green;
            }
        }
        .fa {
            margin-right: 4px;
        }
    }
}
// ARTICLES REVIEW & LIST
.footer-blog-articles {
}
.articles-review,
.articles-list {
    position: relative;
    padding-top: 32px;
    margin-bottom: 60px;
    padding-right: @narrow-center-indent;
    padding-left: @narrow-center-indent;
    .title {
        padding-bottom: 11px;
        margin-bottom: 33px;
        border-bottom: 2px solid @color-dark-green;
    }
    .article-item {
        .clear();
        vertical-align: top;
        display: inline-block;
        // 3 articles per row
        width: 370px / (@center-width - 2 * @narrow-center-indent) * 100%;
        margin-left: 30px / (@center-width - 2 * @narrow-center-indent) * 100%;
        margin-bottom: 30px / (@center-width - 2 * @narrow-center-indent) * 100%;
        &:nth-of-type(3n + 1) {
            margin-left: 0;
        }
        img {
            max-width: none;
            width: 100%;
            float: left;
            border-radius: 6px;
        }
    }
    .article-image-link {
        .clear();
        display: block;
        width: 100%;
    }
    .article-text {
        width: 100%;
        margin-top: 25px;
    }
    .article-category {
        color: @color-black;
        margin: 0 0 2px;
    }
    .article-title {
        margin: 0 0 13px;
        a{
            color: @color-black; 
        }
    }
    .article-time {
        color: @color-black;
        display: block;
        margin-bottom: 6px;
        font-size: 14px;
        font-weight: 500;
        line-height: 18/14;
        letter-spacing: -0.28px;
    }
    .article-resume {
        font-size: 14px;
        font-weight: 500;
        line-height: 21/14;
        letter-spacing: -0.39px;
        max-height: 126px;
        position: relative;
        overflow: hidden;
        margin-bottom: 12px;
        &:after {
            content: '';
            position: absolute;
            display: none;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 17px;
            background: linear-gradient(fade(@color-main-grey, 0), @color-main-grey);
            opacity: 1;
            transition: opacity .2s;
        }
        &.cut-off-active {
            &:after {
                display: block;
            }
        }
    }
    .button-wrapper {
        text-align: center;
    }
    .read-more{
    }
    .more-articles-button {
        transition: background-color .2s;
        &:focus,
        &:hover {
            //background-color: @color-red;
        }
    }
}
.articles-list {
    padding-top: 0;
}
.article-view {
    .clear();
    padding-top: 9px;
    margin-bottom: 88px;
    padding-right: @narrow-center-indent;
    padding-left: @narrow-center-indent;
    .article-and-comments {
        float: left;
        width: 870px / (@center-width - 2 * @narrow-center-indent) * 100%;
        margin-right: 30px / (@center-width - 2 * @narrow-center-indent) * 100%;
    }
    article {
        
    }
    .resume {
        .clear();
        margin-bottom: 24px;
    }
    .resume-image-wrapper {
        width: 100%;
        margin: 24px 0 15px;
        img {
            max-width: 100%;
            border-radius: 6px;
        }
    }
    .resume-content {
        //float: right;
        //width: 54%;
    }
    .article-category {
        color: @color-black;
        font-size: 14px;
        font-weight: 800;
        line-height: 25px;
        letter-spacing: 0.84px;
        text-transform: uppercase;
        margin: 0 0 8px;
    }
    .item-title {
        margin: 0 0 6px -5px;
    }
    .article-time {
        color: @color-black;
        font-size: 14px;
        font-weight: 700;
        line-height: 18/14;
        letter-spacing: -0.28px;
        display: block;
        text-transform: uppercase;
    }
    .resume-text {
        margin-top: 32px;
        color: @color-black;
        font-size: 17px;
        font-weight: 500;
        line-height: 29px;
        letter-spacing: -0.17px;
    }
    .content {
        margin-bottom: 16px;
    }
    .socials {
        text-align: center;
    }
    .comments.blog-comments {
        width: 100%;
        max-width: 770px;
        padding: 0;
        margin-top: 64px;
        .header {
            margin-bottom: 0;
        }
        .info {
            display: block;
            margin: 10px 0 21px;
        }
        .comment-form {
            .form-fields {
                .name-and-rating {
                    .name, .email {
                        max-width: unset;
                    } 
                    .name {
                        margin-right: 30px;
                    } 
                }
            }
        } 
    }    
    .next-articles-review {
        width: 270px / (@center-width - 2 * @narrow-center-indent) * 100%;
        float: right;
        .title {
            padding-bottom: 11px;
            margin-bottom: 32px;
            width: 100%;
            border-bottom: 2px solid @color-red;
        }
        .article-item {
            width: 100%;
            padding: 0 0 23px;
            img {
                max-width: none;
                width: 100%;
                border-radius: 6px;
            }
        }
        .article-title {
            margin: 0 0 13px;
            a {
                color: @color-black; 
            }
        }
        .article-time {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
            margin-top: 3px;
        }
        .article-resume {
            max-height: 119px;
            position: relative;
            overflow: hidden;
            margin-bottom: 12px;
            &:after {
                content: '';
                position: absolute;
                display: none;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 17px;
                background: linear-gradient(fade(@color-main-grey, 0), @color-main-grey);
                opacity: 1;
                transition: opacity .2s;
            }
            &.cut-off-active {
                &:after {
                    display: block;
                }
            }
        }
        .button-wrapper {
            text-align: left;
            margin-bottom: 24px;
        }
    }
}
@-articles-review-image-width: 180px;
@-articles-review-image-gap: 20px;
@-articles-review-content-margin-left: @-articles-review-image-width + @-articles-review-image-gap;

// BACK LINK
.run {         
    position: absolute;
    bottom: 0;
    left: 0;
    a, .a {
        display: block;
        padding: 2px 15px;
        border: 1px solid transparent; //#2f2f2f; //#DDE0E4;
        .border-top-right-radius(7px);
        font-size: 11px;
        color: @color-main-grey; //#2f2f2f;
        text-shadow: -1px -1px 0 rgba(140,140,140,0.3), -1px -1px 0px rgba(0,0,0,0.4);
        text-decoration: none;
        .transition-duration(@menu-transition-duration);
        //@-properties: color, border-color;
        @-properties: color, background-color, text-shadow;
        .transition-property(@-properties);
        &:hover {
            color: #FFF; //#258BF0; //#0176C7;
            //border-color: #258BF0; //#0176C7;
            background-color: #258BF0;
            text-shadow: -1px -1px 0 rgba(140,140,140,0.6), -1px -1px 0px rgba(0,0,0,0.67);
        }
    }
}
 
// CONTACT FORM & RESERVATION FORM
.contact-form, .reservation-form {
    overflow: hidden;
    margin: 0 auto;
    width: @main-content-width / @center-width * 100%;
    .form-title {
        display: table;
        margin: 0 auto 30px auto;
        padding: 0 64px;
        color:  @color-white;
        font-size: 13px;
        font-weight: 800;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 2px;
        background: url('@{img-dir}/left-wing.png') left center no-repeat, url('@{img-dir}/right-wing.png') right center no-repeat;
    }
    .form-fields {
        border: 1px solid #686868;
        padding: 37px 19px;
    }
    .grid-row {
        margin-left: -20px;
        margin-right: -20px;
    }
    .grid-col {
        padding-left: 20px;
        padding-right: 20px;
        .input-empty-text-wrapper, .text-input, .sc-wrapper {
            display: block;
            width: 100%;
        }
    }
    label {
        color: @color-white;
        .required-mark {
            color: @color-white;
        }
    }
    .text-input {
        border-bottom: 1px solid #808080;
        color: @color-white;
    }
    .sc-input {
        display: none;
        .sc {
            color: @color-white;
        }
        &.invalid {
            display: block;
        }
        .label-wrapper {
            text-align: right;
            label {
                padding-bottom: 0;
            }
        }
    }
    .form-bottom {
        margin-top: 28px;
        button {
            float: right;
        }
    }
    .input-errors {
        display: none;
    }
    .small-label {
        font-size: 12px;
        .required-mark {
            font-size: 14px;
        }
    }
}
.reservation-form {
    .input-errors {
        display: block;
    }
    .date-input {
        position: relative; //img
        .text-input {
            padding-right: 55px;
        }   
        img {
            position: absolute;
            top: 1px;
            right: 1px;
            width: 56px;
            height: 48px;
            background: url('@{img-dir}/datepicker.png') left top no-repeat;
            &:hover {
                background-position: left bottom;
            }
        }
    }
}

// NEWSLETTER SUBSCRIPTION FORM 
#newsletter-subscription-form {
    width: 421px;
    margin-bottom: 46px;
    .text {
        margin-bottom: 7px;
        color: @color-white;
        .title {
            display: block;
            font-size: 25px;
            line-height: 28px;
            font-weight: 700;
            letter-spacing: -0.7px;
        }
        .newsletter-text{
            display: block;
            font-size: 14px;
            line-height: 21px;
            font-weight: 500;
            letter-spacing: -0.39px;
            padding: 15px 0 26px;
        }
    }
    .form {
        label {
            padding-bottom: 0;
            padding-right: 6px;
            color: @color-white;
            position: relative;
            top: -9px;
        }
        .text-input {
        }
        .email-wrapper {
            display: flex;
            //flex-direction: row;
            //justify-content: flex-start;
            //align-items: center;
            //width: 480 / 540 * 100%;
            margin-bottom: 9px;
            position: relative;
            .text-input {
                //flex-grow: 999;
                width: 100%;
                //width: 380 / 540 * 100%;
                padding-right: 173px;
            }
            button {
                padding: 0 43px 0 48px;
                position: absolute;
                right: 0;
            }
            & + .input-errors {
                position: relative;
                top: -9px;
            }
        }
        .button {
            //...
        }
        .sc-input {
            display: flex;
            //flex-direction: row;
            //justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            //width: 480 / 540 * 100%;
            margin-top: 11px;
            .input-wrapper {
                //flex-grow: 999;
                flex: 0 0 210px;
                .sc-wrapper {
                    width: 100%;
                    .text-input {
                        width: 100%;
                    }
                }
            }
            label {
                position: relative; //.sc-change
                padding-bottom: 16px;
                white-space: nowrap;
                .sc-change {
                    position: absolute;
                    bottom: 2px;
                    right: 8px;
                    font-size: 10px;
                    color: @color-white;
                    cursor: pointer;
                    text-decoration: none;
                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
        .gdpr-note {
            display: none;
            color: @color-white;
            a {
                color: @color-white;
            }
        }
    }
}

// COMMENT FORM
.comment-form {
    // form styles
    .form-fields {
        .name-and-rating {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 23px;
            .rating {
                min-width: 109px;
                display: inline-flex;
                label {
                    font-size: 12px;
                    color: @color-dark-grey;
                    letter-spacing: 0;
                    line-height: 41px;
                    margin-right: 21px;
                    padding-bottom: 0;
                    white-space: nowrap;
                }
                .input-wrapper {
                    margin-bottom: 0px;
                    padding-top: 5px;
                }
            }
            .name {
                margin-right: 46px/600px * 100%;
            }
            .name, .email {
                flex-grow: 1;
                max-width: 370px;
                .input-wrapper {
                    margin-bottom: 0px;
                }
            }
        }
    }
    .input-empty-text-wrapper, .text-input, .sc-wrapper {
        display: block;
        width: 100%;
    }
    textarea.text-input {
        height: 179px;
        resize: vertical;
    }
    .sc-input {
        display: none;
        &.invalid {
            display: block;
        }
        .label-wrapper {
            position: relative; // .sc-change
            text-align: right;
            label {
                float: none;
                padding-bottom: 0;
            }
        }
    }
    .form-bottom {
        margin-top: 46px;
        text-align: right;
        button {
            width: 263px;
            text-align: left;
        }
    }
    .input-errors {
        display: none;
    }  
    .rating {
        .input-errors {
            display: block;
        }  
    }
    & + .comments-index {
        padding-top: 35px;
    }
}
// COMMENTS INDEX
.comments-index {
    .comment {
        padding-bottom: 25px;
        &:last-child {
            padding-bottom: 0;
        }
        .info {
            font-size: 12px;
            color: @color-grey;
            .name {
                font-weight: bold;
                &:after {
                    content: ', ';
                    display: inline;
                }
            }
            .br-wrapper-f {
                position: relative;
                top: 1px;
                margin-right: -3px;
                padding-right: 5px;
                .br-widget {
                    a {
                        margin-right: 3px;
                        &:after {
                            font-size: 1rem;
                        }
                    }
                }
            }
            .created {
            }
        }
        .text {
            margin-top: 5px;
        }
    }
}
// RATING STYLES
.br-wrapper-f {
    .br-widget {
        height: 14px;
        min-width: 120px;
        a {
            color: @color-light-grey;
            background-image: none; 
            //background-image: url('@{img-dir}/rating-stars.png');
            width: 15px;
            height: 15px;
            display: block;
            float: left;
            margin-right: 9px;
            &:after {
                .font-awesome-icon('\f005');
                font-size: 16px;
            }
            &:hover, &.br-active, &.br-selected {
                background-position: 0 16px;
                color: @color-highlighted;
                &:after {
                    content: '\f005';
                }
            }
        }
        &.br-readonly {
            a {
                background-color: transparent;
                cursor: default;
                &:hover {
                    background-position: 0 0;
                    color: @color-black;
                    &:after {
                        content: '\f006';
                    }
                }
                &.br-active, &.br-selected {
                    &:hover {
                        background-position: 0 16px;
                        color: @color-highlighted;
                        &:after {
                            content: '\f005';
                        }
                    }
                }
            }
        }
    }
}

// TEXT MENU
.text-menu {
    float: right;
    width: 262px;
    .border-radius(14px);
    overflow: hidden; // border-radius
    ul {
        margin: 0;
        padding: 0;
        ul {
            padding-left: 30px;
        }
    }
    li {
        background-color: @color-transparent-mask;
        a {   
            display: block;
            margin-bottom: 1px;
            //height: 36px;
            //line-height: 36px;
            padding: 7px 15px 7px 20px;
            color: @color-white;
            font-weight: 400;
            background: transparent url('@{img-dir}/arrow-right-white.png') no-repeat 3px 9px;
        }
        &.open {
            & > a {
                background: transparent url('@{img-dir}/arrow-down-white.png') no-repeat 0px 11px;
            }
        }
        ul {
            display: none;
            li {
                a {
                    color: @color-black;
                    border-bottom: none;
                    background-image: url('@{img-dir}/arrow-right-gray.png');
                }
                &.has-subitems {
                    & > a {
                        background-image: url('@{img-dir}/arrow-down-gray.png');
                    }
                }
            }
        }
    }
    // let the menu ends up with blue bottom border
    .level-01 {
        margin-bottom: 1px;
        li.last {
            & > a {
                border-bottom: none;
            }
            &.has-subitems > a{
                margin-bottom: 1px;
            }
        }
    }
}

// SIDEMENU
.side-menu {
    @-color-hover: @color-highlighted;
    font-size: 12px;
    font-weight: 600;
    max-width: 199px;
    margin-bottom: 45px;
    .toggle-button + & {
        margin-top: 0;
    }
    
    .title {
        font-weight: 700;
        margin-bottom: 17px;
        font-size: 14px;
        text-align: right;
        letter-spacing: 1px;
    }
    ul {
        margin: 0;
        padding: 0;
    }
    .has-subitems {
        > a, > span {
            .l {
                position: relative;
                top: 1px;
                display: none;
                margin-right: 5px;
                &:before {
                    display: inline-block;
                    font: normal normal normal 14px/1 FontAwesome;
                    font-size: inherit;
                    text-rendering: auto;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                    content: '\f196'; //'\f055';
                }
            }
            &:hover {
                cursor: pointer; // to make the hover work also for iOS touch event
                .l {
                    display: inline-block;
                }
            }
        }
        &.active, &.open, &.-opened {
            > a, > span {
                .l:before {
                    content: '\f147'; //'\f056';
                }
            }
        }
        &.-closed {
            > a, > span {
                .l:before {
                    content: '\f196'; //'\f055';
                }
            }
        }
    }
    .level-01 {
        > li {
            //hederer border-bottom: 1px solid #C5C5C5;
            > a, > span {
                display: block;
                //border-bottom: 1px solid #C5C5C5;
                padding: 16px 15px 2px 0;
                text-align: right;
                text-decoration: none;
            }
            &.last {
                border-bottom: none;
                > a, > span {
                    //border-bottom: none;
                }
            }
            &:hover {
                > a, > span {
                    color: @-color-hover;
                }
            }
            &.active {
                > a, > span {
                    color: @color-highlighted;
                    background: url('@{img-dir}/side-menu-bullet.png') no-repeat right 21px;
                }
            }
            &.open {
                > a {                    
                    color: @color-darkred;
                }
            }
            &.active, &.open, &.-opened {
                .level-02 {
                    display: block;
                }
            }
            &.-closed {
                .level-02 {
                    display: none;
                }                
            }
        }
    }
    .level-02 {
        display: none;
        //margin-top: -8px;
        padding-right: 0px; //10px;
        padding-bottom: 1px;
        font-weight: 400;
        > li {
            > a, > span {
                display: block;
                padding: 2px 15px 2px 0;
                text-align: right;
                text-decoration: none;
            }
            &:hover {
                > a, > span {
                    color: @-color-hover;
                }
            }
            &.active {
                > a, > span {
                    color: @color-highlighted;
                    background: url('@{img-dir}/side-menu-bullet.png') no-repeat right center;
                }
            }
            &.open {
                > a {                    
                    color: @color-darkred;
                }
            }
            &.active, &.open, &.-opened {
                .level-03 {
                    display: block;
                }
            }
            &.-closed {
                .level-03 {
                    display: none;
                }                
            }
        }
    }
    .level-03 {
        display: none;
        padding-right: 10px;
        a {
            opacity: 0.8;
        }
        > li {
            > a, > span {
                display: block;
                padding: 2px 15px 2px 0;
                text-align: right;
                text-decoration: none;
            }
            &:hover {
                > a, > span {
                    color: @-color-hover;
                }
            }
            &.active {
                > a, > span {
                    color: @color-highlighted;
                    background: url('@{img-dir}/side-menu-bullet.png') no-repeat right center;
                }
            }
            &.open {
                > a {                    
                    color: @color-darkred;
                }
            }
            &.active, &.open, &.-opened {
                .level-04 {
                    display: block;
                }
            }
            &.-closed {
                .level-04 {
                    display: none;
                }                
            }
        }
    }
    .level-04 {
        display: none;
        padding-right: 10px;
        > li {
            > a {   
                display: block;
                padding: 2px 15px 2px 0;
                text-align: right;
                text-decoration: none;
            }
            &:hover {
                > a {
                    color: @-color-hover;
                }
            }
            &.active {
                > a {
                    color: @color-highlighted;
                    background: url('@{img-dir}/side-menu-bullet.png') no-repeat right center;
                }
            }
            &.open {
                > a {                    
                    color: @color-darkred;
                }
            }
            &.active, &.open, &.-opened {
                .level-05 {
                    display: block;
                }
            }
            &.-closed {
                .level-05 {
                    display: none;
                }                
            }
        }
    }
    .level-05 {
        display: none;
        padding-right: 10px;
        > li {
            > a {   
                display: block;
                padding: 2px 15px 2px 0;
                text-align: right;
                text-decoration: none;
            }
            &:hover {
                > a {
                    color: @-color-hover;
                }
            }
            &.active {
                > a {
                    color: @color-highlighted;
                    background: url('@{img-dir}/side-menu-bullet.png') no-repeat right center;
                }
            }
            &.open {
                > a {                    
                    color: @color-darkred;
                }
            }
            &.active, &.open, &.-opened {
                .level-06 {
                    display: block;
                }
            }
            &.-closed {
                .level-06 {
                    display: none;
                }                
            }
        }
    }
}

// SUBMENU
.submenu {
    .side-menu;
}

// CHILDS ACCORDION
.childs-accordion {
    padding-top: 85px;
    nav {   
        margin-bottom: 31px;
        margin-left: 8px;
        ul {
            margin: 0;
            padding: 0;
        }
        li {
            display: inline-block;
            margin-right: 40px;
            a {   
                display: block;
                padding-bottom: 1px;
                font-size: 18px;
                line-height: 23/18;
                color: @color-grey;
                font-weight: normal;
                letter-spacing: -0.36px;
                text-decoration: none;
                border-bottom: 2px solid transparent;
            }
            &.active, &:hover {
                a {
                    color: @color-black;
                    font-weight: medium;
                    text-decoration: none;
                }
            }
            &.active {
                a {
                    border-bottom-color: @color-red;
                }
            }
        }
        &.column {
            li {
                display: block;
                margin-right: 0;
            }
        }
    }
    .accordion {
        .section-title {
            margin-top: 0;
        }
        .accordion-item {
            display: none;
            // remove top padding and margin on displayed contents
            & > a:first-child + * {
                margin-top: 0 !important;
                padding-top: 0 !important;
            }
        }
    }
}

// PAGINATOR
.paginator {
    text-align: center;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 14px;
    margin-bottom: 60px;
    .paginator-pages {
    }
    .-run-pg-actual-page, 
    .-run-pg-link {
        color: @color-light-cream;
        line-height: 38px;
        font-weight: 700;
        text-decoration: none;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 4px 8px;
        width: 38px;
        height: 38px;
        border-radius: 6px;
        background: @color-red;
    }
    .-run-pg-actual-page{
        color: @color-red;
        //border: 2px solid @color-red;
        background: @color-light-cream;
    }
    .-run-pg-link-first,
    .-run-pg-link-back,
    .-run-pg-link-next,
    .-run-pg-link-last {
        font-size: 18px;
        text-indent: -9999px;
        &:after {
            content: '';
            text-indent: 0;
            line-height: 18px;
            display: block;
            width: 18px;
            height: 18px;
        }
    }
    .-run-pg-link-first {
        &:after {
            .font-awesome-icon('\f100');
        }
    }
    .-run-pg-link-back {
        &:after {
            .font-awesome-icon('\f104');
        }
    }
    .-run-pg-link-next {
        &:after {
            .font-awesome-icon('\f105');
        }
    }
    .-run-pg-link-last {
        &:after {
            .font-awesome-icon('\f101');
        }
    }
    .button.load-more-items {
        padding: 0 83px 0 84px;
        font-size: 14px;
        &.loading {
            position: relative;
            .loading-mask {
                position: absolute;
                top: -1px;
                right: -1px;
                bottom: -1px;
                left: -1px;
                display: flex;
                justify-content: center;
                align-items: center;
                background: white;
                color: black;
                opacity: 0.6;
                font-size: 28px;                
                &::before {
                    .font-awesome-icon('\f110');
                    animation: animateRotate 1s infinite steps(8);
                }
            }
        }
    }
}
.-run-pg-mask {
    .-run-loader {
        width: 120px;
        height: 120px;
        background-image: url('@{img-dir}/logomark.png');
        background-size: 75px;
        > span {
            width: 120px;
            height: 120px;
            > span {
                width: 120px;
                height: 120px;
                border-top-color: @color-dark-green;
            }
        }
    }
}

// URL GET PARAM SELECT (used for sort|filter|page|limit selects)
.-run-url-param-select {
    label {
        display: inline;
        margin-right: 10px;
        color: @color-black;
    }
    select {
        border:  1px solid #cccccc;
        .border-radius(9999px);
        margin-bottom: 0;
        height: 30px;
        width: auto;
        max-width: 200px;
        padding-left: 8px;
        text-overflow: ellipsis;
    }
}

// SORT|FILTER|PAGE|LIMIT SELECT
.index-controls {
    .sort-select {
        margin-right: 10px;
        select {
            width: 169px;
        }
    }
    .filter-select {
        margin-right: 10px;
        select {
            width: 133px;
        }
    }
    .filter-switch {
        text-decoration: none;
        .toggle-input {
            position: relative;
            top: 1px;            
        }
    }
    select {
        width: 155px;
    }
}
.paginator {
    .page-select {
        //display: inline-block;
        margin: 8px auto 0;
    }
    .limit-select {
        //display: inline-block;
        margin: 8px auto 0;
    }
}

// FILTER MENU
.filter-menu {    
    .clear;
    .-run-url-param-switch {
        display: block;
        float: left;
        margin-right: 45px;
        height: 30px;
        line-height: 30px;
        .border-radius(9999px);
        padding: 0 12px;
        color: @color-black;
        font-weight: 700;
        letter-spacing: 1.5px;
        text-decoration: none;
        .transition-duration(@transition-duration);
        @-properties: background-color, color;
        .transition-property(@-properties);
        &.active, &:hover {
            color: @color-white;
            background-color: @color-black;
        }
        &.active {
            position: relative;
            padding-right: 18px;
            &:after {
                content: '×';
                display: block;
                position: absolute;
                right: 4px;
                top: 1px;
            }
        }
        &:last-child {
            margin-right: 0;
        }
    }
}

// PHOTOGALLERY
.photogallery {
    margin: 0 auto;
    text-align: left;
    .clear;
    .photogallery-image {
        display: inline-block;
        vertical-align: middle;
        a {
            position: relative; //.hover-mask
            display: block;
            margin: 0 20px 20px 0;
        }
        .hover-mask {
            position: absolute;
            top: 0; 
            left: 0;
            .transition-duration(@transition-duration);
            .transition-property(background-color);
            &:hover {
                background: @color-transparent-mask url('@{img-dir}/white-plus.png') center no-repeat;
            }
        }
        img, .hover-mask {
            display: block;
            height: 220px;
            width: 220px;
            .border-radius(11px);
        }
    }
    .photogallery-row {
        text-align: left;
        .photogallery-image {
            display: inline-block;
            vertical-align: middle;
            &.last {
                a {
                    margin-right: 0;
                }
            }
        }
    }
    &.small-thumbs {
        .photogallery-image {
            a {
            }
            .hover-mask {
            }
            img, .hover-mask {
                display: block;
                height: 120px;
                width: 120px;
                .border-radius(6px);
            }
        }
    }
}

// IMAGE PHOTOGALLERY
.image-photogallery {
    float: right;
    margin-left: 40px;
    margin-bottom: 20px;
    .main-image-right {
        margin: 0;
    }
    .photogallery-row {
        text-align: center;
        .clear;
        .photogallery-image {
            float: left;
            margin-top: 20px;
            margin-right: 20px;
            &.last {
                margin-right: 0px;
            }
            img {
                height: 60px;
                width: 60px;
            }
        }
    }
}

// REFERENCES SLIDER
.references-slider {
    .clear;
    margin-bottom: 52px;
    .bx-viewport {
        -moz-box-shadow: none;
        -webkit-box-shadow: none;
        box-shadow: none;
        border: none;
        left: 0;
        background: transparent;
    }
    .bx-controls-direction {
        .bx-prev {
            width: 39px;
            height: 80px;
            margin-top: -40px - 22px;
            background: url('@{img-dir}/arrow-left.png') left top no-repeat;
            left: 0;
        }
        .bx-next {
            width: 40px;
            height: 81px;
            margin-top: -40px - 22px;
            background: url('@{img-dir}/arrow-right.png') left top no-repeat;
            right: 0;
        }
    }
    a.bxslider-link {
        display: block;
        overflow: hidden;
    }
    ul {        
        margin: 0;
        padding: 0;
    }
    .text {
        max-width: 600px;
        margin-left: (116px + 382px + 97px) / @footer-center-width * 100%; // image margin left + image width + text padding left
        padding-top: 78px;
        padding-right: 50px /  @footer-center-width * 100%;
        //font-family: @font-family-2;
        font-size: 15px;
        letter-spacing: -0.2px;
        line-height: 1.67em;
        text-align: justify;
        color: @color-white;
        //background: url('@{img-dir}/quotes.png') left top no-repeat;
        a {
            color: @color-white;
        }
    }
    .image {
        float: left;
        position: relative; // .image-label
        margin-left: 116px / @footer-center-width * 100%;
        width: 382px;
        height: 382px;
        padding: 20px;
        border-radius: 9999px;
        border: 1px solid #474849;
        //background-color: rgba(220, 200, 135, 0.2);
        img {
            display: block;
            width: 340px / (382px - 40px - 2px) * 100%;
            height: 340px / (382px - 40px - 2px) * 100%;
            border-radius: 9999px;
        }
        .image-label {
            position: absolute;
            bottom: 20px;
            left: 20px;
            top: 20px;
            right: 20px;
            //width: 340px;
            //height: 340px;
            border-radius: 9999px;
            text-align: center;
            color: @color-white;
            background: url('@{img-dir}/label-shadow.png') center -16px no-repeat;
            overflow: hidden;
            .image-label-spacer {
                position: absolute;
                right: 0;
                bottom: 0;
                left: 0;
                padding-bottom: 30px;
                .name {
                    display: block;
                    padding: 0 50px;
                    font-size: 15px;
                    font-weight: 700;
                }
                .info {
                    display: block;
                    padding: 9px 85px 0 85px;
                    line-height: 1.2em;
                }
            }
        }
    }
}
.-cke-references-slider-text {
    width: 572px - 68px; // - padding-left
    min-width: 572px - 68px; // - padding-left
    //font-family: @font-family-2;
    font-size: 15px;
    letter-spacing: -0.2px;
    line-height: 1.67em;
    font-style: italic;
    text-align: justify;
    color: @color-white;
    background: @color-main;
}

// PARTNERS SLIDER
.manufacturers-slider,
.partners-slider {
    padding: 62px @grid-cols-gap-width 50px @grid-cols-gap-width;
    background-color: @color-light-cream;
    .intro {
        text-align: center;
        margin-bottom: 50px;
    }
    .heading {
        font-size: 40px;
        font-weight: 700;
        letter-spacing: -0.02em;
    }
    .text-button {
        margin-top: 20px;
    }
    .slider-wrapper {
        width: 100%;
        max-width: @center-width;
        margin: 0 auto;

    }
    .list {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .item {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .link {
        display: block;
        max-height: 100%;
    }
    .image {
        width: auto;
        max-height: 100%;
        transition: all @menu-transition-duration;
        //opacity: .5;
        filter: grayscale(100%);
        &:hover {
            opacity: 1;
            filter: grayscale(0%);
        }
    }
    .dots {
        text-align: center;
        height: 10px;
        margin-top: 50px;
        .owl-dot {
            display: inline-block;
            width: 10px;
            height: 10px;
            vertical-align: top;
            border: 1px solid @color-red;
            margin: 0 5px;
            cursor: pointer;
            &.active {
                background-color: @color-red;
            }
        }
    }
    .owl-carousel {
        .owl-stage { // generated <ul> element
            padding: 0;
        }
        .owl-item {
            display: flex;
            align-items: center;
            justify-content: center;
            img {
                width: auto;
            }
        }
    }
}

// PROFILE MENU
.user-profile-menu {
    .clear;
    .section-header;
    .item {
        float: left;
        width: 50%;
        opacity: 0.3;
        text-decoration: none;
        &:hover {
            opacity: 1;
        }
    }
    span.item {
        opacity: 1;
    }
}

//
// ESHOP
//

// PRODUCT SPECIAL OFFER LABEL MIXIN
.special-offer-label-mixin(@-top-offset: 5px, @-image-width: 30px) {
    .special-offer-label {
        @-label-offset: 10px;
        position: absolute;
        z-index: 5;
        top: @-top-offset;
        //filter: grayscale(90%);
        //transition-property: filter;
        //transition-duration: @menu-transition-duration;
        //transition-timing-function: @transition-timing-function;
        //transition-delay: initial;                
        &.discounting {
            right: 0px;
            transform: translate3d(40%, 0, 0);
        }
        &.promoting {
            left: 0px;
            transform: translate3d(-40%, 0, 0);
        }
        &:nth-child(2) {
            top: @-top-offset + @-label-offset;
            z-index: 4;
        }
        &:nth-child(3) {
            top: @-top-offset + 2 * @-label-offset;
            z-index: 3;
        }
        &:nth-child(4) {
            top: @-top-offset + 3 * @-label-offset;
            z-index: 2;
        }
        &:nth-child(5) {
            top: @-top-offset + 4 * @-label-offset;
            z-index: 1;
        }
        .check-mark {
            display: none;
            position: absolute;
            width: 14px / 30px * @-image-width;
            height: 14px / 30px * @-image-width;
            filter: grayscale(100%);
            opacity: 0.9;
            transition-property: opacity;
            transition-duration: @menu-transition-duration;
            transition-timing-function: @transition-timing-function;
            transition-delay: initial;                

            top: 50%;
            left: 50%;
            z-index: 2;
            margin-top: -7px / 30px * @-image-width;
            margin-left: -7px / 30px * @-image-width;
            background: url('@{img-dir}/check-mark-01.png') no-repeat top left;
            background-size: (14px / 30px * @-image-width) (14px / 30px * @-image-width);

            //top: 0%;
            //right: 0%;
            //background: url('@{img-dir}/check-mark-03.png') no-repeat top left;
            //background-size: 14px 14px;
        }
        &.applicable {
            //filter: grayscale(0%);
            &:after {
                content: '';
                position: absolute;
                top: -2px;
                bottom: -2px;
                left: -2px;
                right: -2px;
                z-index: 1;
                border-radius: 999px;
                //border: 3px solid #5C5C5C;
                border: 3px solid #008000;
                //border: 3px solid #FF7E00;
                //opacity: 0.5;
                //background: #FFF;
                
                transition-property: opacity;
                transition-duration: @menu-transition-duration;
                transition-timing-function: @transition-timing-function;
                transition-delay: initial;                
            }
        }
        &.applicated {
            //filter: grayscale(0%);
            .check-mark {
                filter: grayscale(0%);
            }
            &:after {
                border-color: #008000;
            }
        }
        &:hover {
            z-index: 5;
            //filter: grayscale(0%);
            &:after,
            .check-mark {
                opacity: 0;
            }
        }
        img {
            display: block;
            width: @-image-width;
        }
    }
}
.special-offer-banner-mixin() {
    .special-offer-banner {
        margin-bottom: 17px;
        .special-offer-banner-name {
            display: block;
            margin-bottom: 9px;
            font-weight: 700;
            font-size: 14px;
            color: @color-darkred;
            text-decoration: none;
        }
        .special-offer-banner-image {
            position: relative; // .check-mark
            display: block;
            margin-bottom: 9px;
            img {
                display: block;
                width: 100%;
            }
            .check-mark {
                position: absolute;
                width: 14px;
                height: 14px;
                filter: grayscale(100%);
                opacity: 0.9;

                top: 7px;
                left: 7px;
                background: url('@{img-dir}/check-mark-01.png') no-repeat top left;
                background-size: 14px 14px;
            }
        }
        .special-offer-banner-text {
            //font-family: @font-family-2;
            font-size: 13px;
            text-align: justify;
        }
        &.applicable {
        }
        &.applicated {
            .special-offer-banner-image {
                .check-mark {
                    filter: grayscale(0%);
                }
            }
        }
    }
}
// MINI CART
.mini-cart {
    text-decoration: none;
    margin-top: 34px;
    margin-right: 5px;
    white-space: nowrap;
    .mini-cart-icon{
        display: inline-block;
        vertical-align: middle;
        justify-content: center;
        align-items: center;
        width: 44px;
        height: 44px;
        border-radius: 50%;
        background: @color-dark-green;
        position: relative;
        img{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
    .mini-cart-count {
        color: @color-white;
        font-size: 11px;
        font-weight: 700;
        letter-spacing: 0;
        text-align: center;
        line-height: 19px;
        position: absolute;
        display: block;
        top: -6px;
        right: -6px;
        min-width: 19px;
        height: 19px;
        padding: 0 4px;
        border-radius: 9999px;
        background-color: @color-black;
    }
    .mini-cart-price {
        display: inline-block;
        position: relative;
        top: -2px;
        vertical-align: middle;
        margin-right: 7px;
        margin-top: 0px;
        height: 41px;
        color: @color-black;
        font-size: 14px;
        font-weight: 800;
        line-height: 41px;
    }
    &:hover,
    &:focus {
        color: inherit;
    }
}

// CART VIEW
#cart-view {
    margin-top: 20px;
    .cart {
        padding: 0 30px 0 30px;
        border-left: 1px solid #ccc;
        border-right: 1px solid #ccc;        
    }
    // .checkout-summary has similar styles (see below)
    .cart-summary {
        .clear;
        //margin-bottom: 30px;
        border-bottom: 1px solid #d8dada;
        padding-top: 30px;
        padding-bottom: 30px;
        .cart-total {
            float: right;
            width: 40%;
            text-align: right;
            > .bonus, > .total {
                clear: both;
            }
            > .bonus {
                margin-bottom: 10px;
            }
            .label {
                width: 49%;
                padding-right: 33px;
            }
            .price {
                float: right;
                width: 51%;
                text-align: left;
                white-space: nowrap;
                //  color: @color-highlighted;
            }
            > .total {
                .price {
                    margin-top: -8px;
                    font-size: 22px;
                }
            }
        }
        .cart-info {
            float: left; 
            width: 55%;
            .fa {
                margin-right: 20px;
            }
            .free-shipment {
                font-size: 18px;
                font-weight: 700;
                color: @color-green;
            }
        }
    }
    .cart-additional-discounts {
        border-bottom: 1px solid #d8dada;
        padding-top: 30px;
        padding-bottom: 30px;
        .voucher {
            & > * {
                display: inline-block;
                margin-bottom: 0;
                margin-right: 10px;
                &:last-child {
                    margin-right: 0;
                }
            }
            label {
                font-weight: 700;
                color: @color-dark-green;
            }
            .text-input {
                width: 160px;
                text-transform: uppercase;
            }
            button {
                font-size: 14px;
            }
        }
        .clear-voucher {
            font-weight: 700;
            .button {
                margin-left: 5px;
            }
        }
    }
    .special-offers {
        border-bottom: 1px solid #d8dada;
        padding-top: 30px;
        padding-bottom: 30px;
        .intro-text {
            padding-bottom: 30px;
            &:before {
                .font-awesome-icon('\f05a');
                margin-right: 20px;
            }
        }
        .special-offer-banner-mixin();
    }
}
.cart-products {
    border-bottom: 2px solid @color-black;
    .cart-products-header, .cart-product {
        display: table;
        width: 100%;
        > div {
            display: table-cell;
            vertical-align: middle;
            padding-right: 5px;
            height: 46px;
            > .label {
                display: none;
                color: @color-grey;
            }
        }
    }
    .product-overview {
        padding-left: 2px;
        width: 50%;
    }
    .product-unit-price {
        width: 15%;
    }
    .product-amount {
        width: 15%;
    }
    .product-total-price {
        width: 15%;
        white-space: nowrap;
    }
    .product-actions {
        width: 5%;
    }
    .cart-products-header {
        line-height: 1em;
        font-weight: 700;
        border-bottom: 2px solid @color-black;
    }
    .cart-product {
        margin-bottom: 2px;
        background: @color-white;
        //border-bottom: 1px solid #686868;
        min-height: 90px;
        &:last-child {
            margin-bottom: 0px;
        }
        .product-overview {
            > div {
                display: inline-block;
                vertical-align: middle;
            }
            .image {
                padding-right: 3%;
                .clear;
                .wrapper {
                    float: left; // to fit content width
                    position: relative; // .special-offer-label
                    .special-offer-label-mixin(5px, 24px)
                }
                a.product-image > img {
                    display: block;
                    max-height: 80px;
                }
            }
            .info {
                .name {
                    font-weight: 700;
                    a {
                        text-decoration: none;
                        color: @color-black;
                         &:hover {
                            text-decoration: underline;
                        }
                    }
                }
                .attributes, .disponibility, .available-from, .additional-services {
                    font-size: 12px;
                }
                .additional-services {
                    margin-top: 5px;
                    .additional-service {
                        display: block;
                        cursor: pointer;
                        //text-decoration: none;
                        input[type="checkbox"] {
                            position: relative;
                            top: 2px;
                            margin-right: 5px;
                            cursor: pointer;
                        }
                    }
                }
            }
        }
        .product-unit-price {
            white-space: nowrap;
        }
        .product-amount {
            .product-amount-change {
                display: flex;
                .product-amount-change-controls, 
                .product-amount-change-value {
                    float: left;
                }
                .product-amount-change-controls {
                    width: 30px;
                    height: 58px;
                    text-align: left;
                    display: inline-block;
                    .plus, .minus{
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background: @color-light-grey-6;
                        height: 50%;
                        width: 30px;
                        color: @color-black;
                        font-weight: 700;
                        position: relative;
                        cursor: pointer;
                        &:first-child{
                            border-radius: 6px 0 0 0;
                        }
                        &:last-child{
                            border-radius: 0 0 0 6px;
                        }
                        &::after {
                            font-family: FontAwesome;
                            position: absolute;
                            font-size: 12px;
                            font-weight: 700;
                        }
                    }
                    .plus::after {
                        content: "\f067";
                    }
                    .minus::after {
                        content: "\f068";
                    }
                }
                .product-amount-change-value{
                    .product-amount-input {
                        width: 35px;
                        height: 58px;
                        border: 2px solid @color-light-grey-6;
                        border-radius: 0 6px 6px 0;
                        text-align: center;
                    }
                }
            }
        }
        .product-total-price {
            white-space: nowrap;
        }
        .product-actions {
            .fa {
                &:hover {
                    color: @color-alert;
                }
            }
        }
        &.presale, &.at-supplier.with-available-from {
            .info {
                .disponibility, .available-from {
                    color: @color-highlighted;
                }
            }
        }
    }
}
// app confirmation message
.off-stock-products {
    ul {
        list-style: none;
        li {
            font-weight: bold;
            margin-bottom: 5px;
            &::before {
                content: "\2022";
                position: absolute;
                display: inline-block; 
                width: 1em;
                margin-left: -1em;
                color: @color-grey;
                font-weight: bold;
                font-size: 25px;
                line-height: 0.9;
            }
        }
    }
    b {
        color: #6D9430;
    }
}
    
// CHECKOUT
.checkout-header {
    .title {
        margin-bottom: 58px;
        border-bottom: 1px solid #d8dada;
        padding-bottom: 30px;
        font-size: 29px;
        font-weight: 700;
        text-align: center;
    }
    .progress-bar {
        .clear;
        .border-top-radius(@checkout-border-radius);
        border-bottom: 2px solid @color-dark-green;
        .checkout-step {
            float: left;
            border-right: 1px solid #686868;
            //.border-top-right-radius(@checkout-border-radius);
            width: 25%;
            height: 46px;
            line-height: 46px;
            padding: 0 5px;
            text-align: center;
            font-weight: 700;
            color: @color-grey;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            background-color: @color-light-cream;
            &:first-child {
                margin-left: 0;
                .border-top-left-radius(@checkout-border-radius);
            }
            &.previous {
            }
            &.active {
                color: @color-black;
                background: @color-white;
                color: @color-dark-green;
                background: @color-light-cream;
            }
            &:last-child {
                //border-right: none;
                .border-top-right-radius(@checkout-border-radius);
            }
        }
        a.checkout-step {
            color: @color-black;
        }
    }
}
.checkout-footer {
    clear: both;
    margin-bottom: 30px;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;        
    border-bottom: 1px solid #ccc;   
    .border-bottom-radius(@checkout-border-radius);
    padding-bottom: 30px;
    text-align: center;
    .back {
        display: inline-block;
        margin-top: 30px;
        margin-right: 30px;
        text-decoration: none;
        color: @color-dark-green;
        &:hover {
            text-decoration: underline;
        }
        .fa {
            margin-right: 10px;
            font-size: 12px;
        }
    }
    .continue {
        margin-top: 30px;
        padding-left: 16px;
        background: @color-dark-green;
        .fa {
            margin-right: 18px
        }
    }
}
.checkout {
    form {
        padding: 1px 30px 0 30px; // 1px to stop .title margin-top overflow
        border-left: 1px solid #ccc;
        border-right: 1px solid #ccc;
        .title {
            margin-top: 29px;
            margin-bottom: 30px;
            font-size: 20px;
            font-weight: 700;
            border-bottom: 2px solid @color-black;
        }
        .grid-col {
            .input-empty-text-wrapper, .text-input, .sc-wrapper {
                display: block;
                width: 100%;
            }
        }
        textarea.text-input {
            height: 5em;
        }
        .input-wrapper {
            margin-bottom: 15px;
        }
        .input-errors {
            display: block;
        }
    }
}
.checkout-address {
    .nice-radio {
        display: inline-block;
        margin-right: 16px;
    }
    .delivery-address-switch, 
    .subject-type-switch {
        margin-bottom: 16px;
        .input-wrapper {
            margin-bottom: 0;
        }
    }
    .delivery-address {
        .other-delivery-address {
            display: none;
            //padding-top: 24px;
        }
        .merchant-delivery-address {
            display: none;
            padding-top: 35px;
        }
    }
}
.checkout-shipment-and-payment {
    .shipment-method-item, .payment-method-item {
        .clear;
        margin-top: 13px;
        padding-bottom: 13px;
        border-bottom: 1px solid #CCC;
    }
    form .title {
        margin-bottom: 0 !important;
    }
    .info {
        display: block;
        font-size: 12px;
        padding-left: 18px;
        padding-top: 6px;        
        .payment-methods-menu {
            padding: 0 0 0 5px;
        }
        select.text-input {
            width: auto;
            max-width: 100%;
        }
    }
}
.checkout-confirmation {
    .header {
        margin-bottom: 12px;
    }
    .checkout-data {
        .clear;
        > div {
            float: left;
            width: 100% / 3;
        }
        .title {
            margin-bottom: 0;
            border-bottom: none;
            font-size: 18px;
            font-weight: 600;
        }
    }
    .change-button {
        margin-top: 10px;
        border-radius: 4px;
        //padding: 0 16px 0 17px;
        //font-size: 12px;
        //height: 24px;
        //line-height: 24px;
    }
    // .cart-summary has similar styles (see above)
    .checkout-summary {
        .clear;
        margin-top: 30px;
        border-top: 2px solid @color-black;
        border-bottom: 1px solid #d8dada;
        padding-top: 30px;
        padding-bottom: 30px;
        .checkout-total {
            float: right;
            width: 40%;
            .label {
                display: inline-block;
                width: 55%;
                text-align: right;
                vertical-align: top;
            }
            .price {
                display: inline-block;
                margin-top: 0px;
                margin-left: 7%;
                width: 38%;
                text-align: left;
                vertical-align: top;
                .price-info {
                    font-style: italic;
                    font-size: 0.8em;
                    color: @color-alert;
                }
            }
            .subtotals {
                .bonus-subtotal, 
                .products-subtotal, 
                .shipment-and-payment-subtotal {
                    .clear;
                }
            }
            .totals {
                margin-top: 15px;
                border-top: 1px solid #ccc;
                padding-top: 15px;
                .total-without-vat, 
                .total-with-vat, 
                .total-to-pay {
                    .clear;
                }
                .total-with-vat.total-with-vat-highlighted, 
                .total-to-pay {
                    .price {                        
                        margin-top: -8px;
                        font-size: 22px;
                    }
                }
                .total-without-vat {
                    font-size: 12px;
                }
                .total-to-pay {
                    margin-top: 15px;
                    border-top: 1px solid #ccc;
                    padding-top: 15px;
                }
            }
        }
        .checkout-info {
            float: left; 
            width: 55%;
            .fa {
                margin-right: 20px;
            }
            .free-shipment {
                font-size: 18px;
                font-weight: 700;
                color: @color-green;
            }
        }
    }    
    .checkout-confirmation-inputs {
        padding-top: 30px;
        .nice-checkbox {
            margin-bottom: 4px;
        }
        label {
            text-align: left;
            &.invalid, &.invalid * {
                color: @color-alert;
            }
        }
        textarea.text-input {
            height: 64px;
        }
        .gdpr-note {
            color: @color-grey;
            display: block;
            //font-size: 0.8em;
            //margin-left: 18px;
            margin-top: 4px;
            transition: color .2s;
            a {
                color: inherit;
                &:hover,
                &:focus {
                    color: @color-red;
                }
            }
        }
    }
}

// PAYMENT LINK
.payment-link {
    .button;
    margin-top: 16px;
    transition: color .2s, border-color .2s, background-color .2s;
    &:focus,
    &:hover {
        color: @color-white;
        border-color: @color-red;
        background-color: @color-red;
    }
}

// PAYMENT RESPONSE
.payment-response {
    text-align: center;
    padding: 100px 0;
    font-size: 20px;
    .button {
        margin-top: 30px;
    }
}

// ORDERS INDEX
.orders-index {
    margin-bottom: 70px;
    .orders-index-header {
        .section-header;
    }
    .orders-header, 
    .order, 
    .orders-footer {
        display: table;
        width: 100%;
        > div {
            display: table-cell;
            vertical-align: middle;
            padding-right: 5px;
            height: 46px;
        }
    }
    .order-actions {
        width: 5%;
        text-align: center;
        i {
            cursor: pointer;
        }
    }
    .order-number {
        width: 12%;
    }
    .order-created {
        width: 13%;
    }
    .order-products-price {
        width: 13%;
        text-align: right;
    }
    .order-shipment-price {
        width: 13%;
        text-align: right;
    }
    .order-total-price {
        width: 13%;
        text-align: right;
    }
    .order-status {
        width: 13%;
    }
    .order-shipment-and-payment-method {
        width: 18%;
    }
    .empty-before {
        width: 5%;
    }
    .orders-grand-total-label {
        width: 25%;
    }
    .orders-products-grand-total-price {
        width: 13%;
        text-align: right;
    }
    .orders-grand-total-price {
        width: 26%;
        text-align: right;
    }
    .empty-after {
        width: 35%;
    }
    .orders-header {
        line-height: 1em;
        font-weight: 700;
        border-bottom: 2px solid @color-black;
    }
    .order {
        border-bottom: 2px solid #f2f2f2;
        background: @color-white;
        //border-bottom: 1px solid #686868;
        &:last-child {
            margin-bottom: 0px;
        }
        > div {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }
    .orders-footer {
        height: 46px;
        line-height: 46px;
        font-weight: 700;        
        border-top: 2px solid @color-black;
    }
}
// ORDER OVERVIEW
.order-overview {
    display: none;
    border-bottom: 10px solid #f2f2f2;
    padding: 30px;
    background-color: @color-white;
    .order-overview-title {
        font-weight: 700;
        padding-top: 30px;
        padding-bottom: 30px;
        &:first-child {
            padding-top: 0;
        }
    }
    .products-header, 
    .product {
        display: table;
        width: 100%;
        > div {
            display: table-cell;
            vertical-align: middle;
            padding-right: 5px;
            height: 46px;
        }
    }  
    .products-header {
        line-height: 1em;
        font-style: italic;
        border-bottom: 1px solid @color-black;
    }
    .product {
        > div {
            padding-top: 10px;
            padding-bottom: 10px;
        }
    }
    .products {
        .product-code {
            width: 13%;
        }
        .product-name {
            width: 28%;
            font-weight: 700;
            a {
                text-decoration: none;
                color: @color-black;
                &:hover,
                &:focus {
                    text-decoration: underline;
                    color: @color-highlighted;
                }
            }
            .disponibility, .available-from {
                font-weight: 400;
                font-size: 12px;
            }
            &.presale, &.at-supplier.with-available-from {
                .disponibility, .available-from {
                    color: @color-highlighted;
                }
            }
        }
        .product-amount {
            width: 10%;
            text-align: right;
        }
        .product-reserved-amount {
            width: 10%;
            text-align: right;
            &.insufficient {
                color: @color-alert;
            }
        }
        .product-unit-price {
            width: 13%;
            text-align: right;
        }
        .product-total-price {
            width: 13%;
            text-align: right;
        }
        .product-attributes {
            width: 13%;
        }
    }
    .order-info {
        padding-top: 30px;
        .payment-link {
            height: 21px;
            line-height: 21px;
            padding: 0 13px 0 12px;
            font-size: 12px;
        }
    }  
}
.user-profile-menu + .orders-index {
    .orders-index-header {
        display: none;
    }
}

// PRODUCTS INDEX
@product-index-spacer-width: 90%;
.wishlist-products-index,
.products-index, 
.products-slider {
    //padding-bottom: 55px;
    > .section-title {
        font-size: 36px;
        line-height: 43/36;
        font-weight: 700;
        letter-spacing: -1.44px;
        color: @color-dark-green;
        margin-top: 0;
        margin-bottom: 0px;
    }
    .index-text-container {
        display: block;
        margin: 10px 0 22px 0;
        .index-text {
            position: relative;
            &:after {
                content: '';
                display: none;
                position: absolute;
                bottom: 0;
                width: 100%;
                height: 38px;
                background: linear-gradient(fade(@color-main-grey, 0), @color-main-grey);
                opacity: 1;
                transition: opacity .2s;
            }
        }
        .button-wrapper {
            //text-align: right;
        }
        .text {
            transition: max-height .2s;
            overflow: hidden;
        }
        .more-button {
            display: none;
            color: @color-dark-green;
            font-size: 14px;
            font-weight: 700;
            line-height: 32px;
            position: relative;
            height: 32px;
            padding-right: 20px;
            cursor: pointer;
            &:after {
                .font-awesome-icon('\f078');
                line-height: 13px;
                display: block;
                position: absolute;
                top: 11px;
                right: 0;
                width: 16px;
                height: 16px;
                transform: rotate3d(0, 0, 0, 0);
                transition: transform .2s;
            }
            &.active {
                &:after {
                    transform: rotate3d(0, 0, 1, 180deg);
                }
            }
        }
        &.expandable {
            .index-text {
                &:after {
                    display: block;
                }
            }
            .more-button {
                display: inline-block;
            }
            &.expanded {
                .index-text {
                    &:after {
                        display: none;
                    }
                }
                .more-button {
                    &:after {
                        top: 9px;
                        transform: rotate3d(0, 0, 1, 180deg);
                    }
                }
            }
        }
    }
    .index-controls {
        margin: 0 0 20px 0;
        padding: 5px 0px 0;
        .products-index-sort-and-type {
            margin-bottom: 5px;
            border-radius: 6px;
            background-color: #FFFDFA;
            > * {
                display: inline-block;
                vertical-align: middle;
            }
            .sort-switch {
                color: @color-black;
                font-size: 14px;
                letter-spacing: -0.28px;
                text-decoration: none;
                height: 35px;
                line-height: 35px;
                padding: 0 18px 0 10px;
                border-radius: 6px;
                &:hover, &:focus, &.active {
                    background: @color-light-grey-5;
                }
            }
            .filter-switch {
                color: @color-green; //@color-black;
                font-size: 14px;
                letter-spacing: -0.28px;
                text-decoration: none;
                padding: 0px 18px 0 10px;
                input[type="checkbox"] {
                    position: absolute;
                    top: 1px;
                }
                .image {
                    top: 1px;
                    background-color: transparent;
                    filter: brightness(0) saturate(100%) invert(58%) sepia(15%) saturate(1774%) hue-rotate(83deg) brightness(102%) contrast(87%);
                }
            }
            .products-index-type-select {
                float: right;
            }
        }
    }
    .section-title {
        & + .subcategories,
        & + .index-controls {
            margin-top: 21px;
        }
    }
    .index {
        //display: flex;
        //justify-content: space-between;
        //flex-wrap: wrap;
        //width: 100%;
        //.index-row {
        //    .clear;
            //border-bottom: 1px solid #ccc;
            //&:last-child {
            //    border-bottom: none;
            //}
            // if products are placed in rows then they are floated
        //    .product {
        //        display: block;
        //        float: left;
        //    }
        //}
        .product {
            @-product-width: 320px;
            //flex-basis: 160px;
            display: inline-block;
            //width: 120px;
            vertical-align: top;
            width: @-product-width / @center-width * 100%;
            padding: 0;
            text-align: left;
            margin-left: 10px / @center-width * 100%;
            margin-bottom: 32px / @center-width * 100%;
            border-radius: 6px;
            overflow: hidden;
            background: @color-white;
            &:nth-of-type(4n + 1) {
                margin-left: 0;
            }
            > .spacer {
                position: relative; //.discount-label
                //width: @product-index-spacer-width;
                .clear;
            }
            a {
                text-decoration: none;
                color: @color-black;
                &.button {
                    color: @color-white;
                    min-height: 58px;
                    line-height: 58px;
                }
            }
            .image {
                display: block;
                //height: 60.1%;
                .clear;
                .wrapper {
                    //float: left; // to fit content width
                    position: relative; // .special-offer-label
                    .special-offer-label-mixin()
                }
                a.product-image {
                    > img {
                        display: block;
                        width: 100%; 
                    }
                } 
            }
            .stickies {
                text-align: left;
                position: absolute;
                left: 9px;
                top: 8px;
                width: 100%;
                & > .stick {
                    display: block;
                    margin-bottom: -6px;
                    height: 40px;
                    width: 40px;
                    line-height: 40px;
                    border-radius: 999px;
                    font-size: 11px;
                    font-weight: 800;
                    letter-spacing: -0.33px;
                    color: @color-light-cream;
                    text-align: center;
                    &.savings-rate {
                        background: @color-red;
                    }
                    &.new {
                        font-size: 8px;
                        letter-spacing: -0.16px;
                        text-transform: uppercase;
                        background: @color-green;
                    }
                }
            }
            .texts {
                position: relative;
                //padding-top: 19px / @-product-width * 100%;
                padding-top: 19px;
                padding-right: 15px / @-product-width * 100%;
                padding-left: 25px / @-product-width * 100%;
            }
            .title {    
                font-size: 14px;
                font-weight: bold;
                line-height: 18/14;
                letter-spacing: -0.28px;
            }
            .subtitle-and-code {
                font-size: 12px;
                line-height: 15/12;
                letter-spacing: -0.24px;
                margin-top: -7px;
                .subtitle + .code:before {
                    content: ', ';
                    display: inline;
                }
            }
            .info {
                //margin-top: 24px / (@-product-width - 15px - 25px) * 100%;
                margin-top: 24px;
                font-size: 12px;
                font-weight: 400;
                .price-and-disponibility {
                    float: left;
                    margin-bottom: 15px;
                }
                .actual-price {    
                    font-family: @font-family;
                    font-weight: bold;
                    font-size: 14px;
                    line-height: 18px;
                    letter-spacing: -0.28px;
                    margin-bottom: 5px;
                    margin-right: 8px;
                    .currency {
                    }
                }
                .old-price {
                    display: none; //inline-block;
                    font-size: 14px;
                    line-height: 21px;
                    text-decoration: line-through;
                    color: @color-red;
                }
                .disponibility-wrapper {
                }
                .disponibility {
                    font-size: 10px;
                    font-weight: 700;
                    line-height: 12px;
                    letter-spacing: -0.2px;
                    text-transform: uppercase;
                    text-align: left;
                    padding: 0px;
                    &:before{
                        content: '';
                        display: block;
                        float: left;
                        width: 11px;
                        height: 11px;
                        margin-right: 5px;
                        border-radius: 50%;
                    }
                    &.on-stock {
                        color: @color-green;
                        &:before{
                            background: @color-green;
                        }
                    }
                    &.presale {
                        color: @color-red;
                        &:before{
                            background: @color-red;
                        }
                    }
                    &.on-demand {
                        color: @color-orange;
                        &:before{
                            background: @color-orange;
                        }
                    }
                    &.at-supplier {
                        color: @color-green;
                        &:before{
                            background: @color-green;
                        }
                    }
                    &.sold-out {
                        color: @color-dark-grey-2;
                        &:before{
                            background: @color-dark-grey-2;
                        }
                    }
                }
                .authors {
                    margin-bottom: 22px;
                    a {
                        font-weight: normal;
                        font-size: 12px;
                        line-height: 15px;
                        letter-spacing: -0.24px;
                        color: @color-black;
                        &:hover {
                        }
                    }
                }
                .manufacturer {
                    display: none;
                    color: @color-grey;
                    margin-bottom: 6px;
                    a {
                        color: @color-grey;
                        &:hover {
                            color: @color-highlighted;
                        }
                    }
                }
            }
            .discount-label, .serial-label {
                position: absolute;
                top: 0;
                left: 0;
                height: 52px;
                width: 50px;
                padding: 4px 0 0 5px;
                background: url('@{img-dir}/discount-label.png') no-repeat top left;
                font-size: 8px;
                font-weight: 800;
                color: @color-white;
                text-align: left;
                text-transform: uppercase;
            }
            .serial-label {
                height: 40px;
                width: 40px;
                padding-left: 7px;
                background: url('@{img-dir}/serial-label.png') no-repeat top left;
                font-size: 12px;
            }
            .controls {
                display: block;
                float: right;
                margin-left: 8px;
                margin-bottom: 15px;
                .add-to-cart {
                    display: flex;
                    .to-cart-change, .to-cart-count, .to-cart-button{
                        float: left;
                    }
                    .to-cart-change {
                        width: 30px;
                        height: 58px;
                        text-align: left;
                        display: inline-block;
                        .plus, .minus{
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            background: @color-light-grey-6;
                            height: 50%;
                            width: 30px;
                            color: @color-black;
                            font-weight: 700;
                            position: relative;
                            cursor: pointer;
                            &:first-child{
                                border-radius: 6px 0 0 0;     
                            }
                            &:last-child{
                                border-radius: 0 0 0 6px;     
                            }
                            &::after {
                                font-family: FontAwesome;
                                position: absolute;
                                font-size: 12px;
                                font-weight: 700;
                            }
                        }
                        .plus::after {
                            content: "\f067";
                        }
                        .minus::after {
                            content: "\f068";
                        }
                    }
                    .to-cart-count{
                        .number-of-pieces{
                            width: 35px;
                            height: 58px;
                            font-size: 14px;
                            text-align: center;
                            border: 2px solid @color-light-grey-6;
                        }
                    }
                    .to-cart-button{
                        width: 100%;
                        img{
                            height: 16px;
                            width: 16px;
                        }
                        .button{
                            height: 58px;
                            width: 100%;
                            max-width: 240px;
                            font-size: 15px;
                            padding-left: 34px;
                            cursor: pointer;
                            background: url('@{img-dir}/cart-icon-white.png')  18px center no-repeat @color-dark-green;
                            background: url('@{img-dir}/cart-icon-white.svg')  18px center no-repeat @color-dark-green;
                            background-size: 16px;
                            border-radius: 0 6px 6px 0;
                            transition: all 0.2s;
                            &:hover,
                            &:focus {
                            }
                        }
                    }
                }
            }
            &.represents-variants {
                .info {
                    margin-top: 44px;
                }
            }
        }
    }
    &.with-limited-image-height {
        .index {
            .product {
                .image {
                    a.product-image {
                        display: block;
                        //display: flex;
                        //justify-content: center; // horizontal
                        //align-items: center; // vertical
                        // limit images to following max height
                        height: 275px;
                        overflow: hidden;
                        img {
                            margin: auto;
                        }
                    }
                }
            }
        }
    }
}
#content-main {
    .wishlist-products-index,
    .products-index {
        .index {
            .product {
                // 4 products per row
                width: 270px / @main-content-width * 100%;
                margin-left: 10px / @main-content-width * 100%;
                margin-bottom: 32px / @main-content-width * 100%;
                &:nth-of-type(4n + 1) {
                    margin-left: 0;
                }  
            }
        }
    }
}
.products-slider {
    margin-bottom: 16px;
    .index {
        .product {
            width: 100%;
        }
    }
}
.owl-carousel {
    .owl-carousel();
}

// PRODUCTS FILTER
.products-filter {
    position: relative;
    > h1 {
        color: @color-main;
        font-size: 30px;
        font-weight: 700;
        padding-bottom: 15px;
    }
    .product-filters-content-spacer {
        padding-bottom: 16px;
    }
    .navigation {
        display: inline-block;
        position: relative;
        & > header {
            font-weight: 500;
            font-size: 14px;
            letter-spacing: -0.28px;
            padding: 0px 29px 0px 10px;
            position: relative;
            cursor: pointer;
            transition: background-color .2s;
            &:after {
                //.font-awesome-icon('\f105');
                //font-size: 14px;
                //line-height: 14/18;
                content: '';
                position: absolute;
                top: 9px;
                right: 8px;
                width: 12px;
                height: 7px;
                transition: transform .4s;
                transform: rotate(0deg);
                background: url('@{img-dir}/arrow-down.png') center no-repeat;
            }
            &.expanded {
                color: @color-highlighted;
                &:after {
                    color: @color-highlighted;
                    filter: @color-by-filter-highlighted;
                    transform: rotate(180deg);
                }
            }
            &:hover {
                color: @color-highlighted;
                &:after {
                    color: @color-highlighted;
                    filter: @color-by-filter-highlighted;
                }
            }
        }
    }
    .navigation section {
        display: none;
        overflow: auto;
        background-color: @color-light-grey-4;
        border-left: 1px solid @color-highlighted;
        position: absolute;
        top: 26px;
        z-index: 1;
        overflow: visible;
        white-space: nowrap;
        box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    }
    .navigation section a {
        text-decoration: none;
    }
    .navigation section a:hover,
    .navigation section a:focus {
        text-decoration: underline;
    }
    .navigation section ul.level-01 {
        border-radius: 0;
        border: none;
        border-top: none;
        box-shadow: none;
        list-style: none;
        padding: 10px 0;
        margin: 0;
        text-align: center;
    }
    .navigation section li {
        font-size: 14px;
        text-indent: 0;
        padding-left: 17px;
        padding-right: 10px;
        text-align: left;
    }
    .navigation section li .label {
        display: block;
        margin: 0;
        padding-top: 2px;
        font-size: 14px;
        line-height: 21px;
        min-height: 24px;
        color: @color-black;
    }
    .navigation section li .label input {
        left: -7px;
        position: relative;
        top: 1px;
    }
    .navigation section li.last .label {
        border-bottom: none;
    }
    .filter-price {
    }
    .filter-price section {
        padding: 10px 17px 11px 17px;
        background-color: #f0f0f0;
        min-width: 250px;
    }
    .navigation section .price-range-slider {
        padding: 0;
    }
    .navigation section .price-range-slider a.ui-slider-handle {
        margin: 0;
        top: -.4em;
        margin-left: -.6em;
        min-height: 0;
        border-bottom: 1px solid @color-highlighted;
        display: block;
        font-size: 0.875em;
        line-height: 1.6;
        padding-top: 0;
        width: 16px;
        height: 16px;
    }
    .navigation section .price-range-slider a.ui-slider-handle.ui-state-hover,
    .navigation section .price-range-slider a.ui-slider-handle.ui-state-focus,
    .navigation section .price-range-slider a.ui-slider-handle.ui-state-active {
        border: 1px solid #999;
    }
    .ui-widget-content .ui-state-default {
        background: @color-dark-green;
        border-color: @color-dark-green;
    }
    .ui-widget-content .ui-state-hover {
        background: @color-highlighted;
        border-color: @color-highlighted;
    }
    .ui-widget-content {
        border-color: @color-light-grey;
    }
    .ui-widget-header {
        background: @color-light-grey;
        border-color: @color-light-grey;
    }
    .ui-slider .ui-slider-handle {
        border-radius: 999px;
    }
    > .products-filter-content-spacer {
        padding: 0;
        //border: 1px solid #CCC;
        //border-top: none;
        border-radius: 5px;
        //box-shadow: 0 3px 0 #CCC;
        //overflow: hidden;
        //header{
        //    min-height: 10px !important;
        //}

    }
    .filter-reset-controls {
        section {
            ul.level-01 {
                text-align: left;
            }
            li {
                display: inline-block;
                a.reset {
                    position: relative;
                    top: -1px;
                    display: inline;
                    margin: 0;
                    font-size: 20px;
                    margin-right: 6px;
                    margin-left: -8px;
                    color: #C30404;
                    border-bottom: none;
                    text-decoration: none;
                }
            }
        }
    }
}

// DETAILED PRODUCT INDEX
.products-index {
    margin-bottom: 24px;
    &.detailed-products-index {
        .index {
            .product {
                width: 585 / @center-width * 100%;
                padding: 0;
                margin-bottom: 20px / @center-width * 100%;
                &:nth-of-type(n) {
                    margin-left: 20px / @center-width * 100%;
                }
                &:nth-of-type(2n + 1) {
                    margin-left: 0;
                }

                > .spacer {
                    padding: 15px;
                    width: auto;
                }

                .image {
                    display: inline-block;
                    margin-bottom: 0;
                    width: 180px;
                    img {
                        border-radius: 6px;
                    }
                }

                .stickies {
                    left: 15px;
                    top: 15px;
                    width: 180px;
                }

                .texts {
                    margin-left: 0px;
                    display: inline-block;
                    width: calc(~"100% - 180px");
                    vertical-align: top;
                    padding: 0 15px;
                    .title {
                        font-weight: bold;
                        font-size: 18px;
                        line-height: 22/18;
                    }
                }

                .info {
                    .origin {
                        margin: 4px 0;
                        > * {
                            display: inline;
                        }
                    }

                    .authors, .manufacturer {
                        color: @color-black;
                        font-weight: normal;
                        font-size: 16px;
                        line-height: 18px;

                        a {
                            font-size: 16px;
                            color: @color-black;
                        }
                    }

                    .authors + .manufacturer:before {
                        content: ' | ';
                    }
                    .attributes {
                        color: @color-grey;
                        font-size: 14px;
                        margin-bottom: 12px;
                        & > span {
                            &:not(:first-of-type){
                                &:before {
                                    content: '|';
                                    display: inline-block;
                                    margin: 0 4px;
                                }
                            }
                        }
                    }
                    .description {
                        font-size: 14px;
                        line-height: 18px;
                        margin-bottom: 16px;
                    }

                    .actual-price {
                        font-size: 18px;
                        display: inline-block;
                        margin-right: 10px;
                        margin-bottom: 6px;
                    }

                    .savings-rate {
                        display: inline-block;
                        margin-right: 10px;
                    }
                }
                .disponibility-wrapper {
                    display: block;
                    position: static;
                }
                .disponibility {
                    height: 18px;
                    line-height: 18px;
                    text-align: left;
                    padding: 0;
                    border-radius: 0;
                    &:nth-of-type(n){
                        background-color: transparent;
                    }
                    &.on-stock {
                        color: @color-green;
                    }
                    &.presale {
                        color: @color-red;
                    }
                    &.at-supplier {
                        color: @color-red;
                    }
                    &.sold-out {
                        color: @color-grey;
                    }
                }
                .controls {
                    display: block;
                    float: right;
                    width: 120px;
                    text-align: right;

                    .add-to-cart {
                        display: inline-block;
                        margin-right: 10px;
                    }

                    .add-to-wishlist {
                        display: none;
                        padding-left: 29px;
                        font-weight: 800;
                        font-size: 8px;
                        text-transform: uppercase;
                        height: 22px;
                        line-height: 22px;
                        letter-spacing: 0.7px;
                        color: @color-darkred;
                        text-decoration: none;
                        text-transform: uppercase;
                        background: url('@{img-dir}/wishlist-star.png') left center no-repeat;
                    }

                    .add-availability-alert {
                            .text-input {
                                font-size: 14px;
                                width: 100%;
                                border-radius: 4px 4px 0 0;
                                margin: 0;
                                & + button {
                                    border-radius: 0 0 4px 4px;
                                }
                            }
                            button {
                                width: 100%;
                                margin: 0 0 4px;
                                &:before{
                                    .font-awesome-icon('\f0f3');
                                    margin-right: 4px;
                                }
                            }
                        .note {
                            color: @color-grey;
                            text-align: left;
                        }
                    }
                }
            }
        }
    }
}

// PRODUCTS INDEX SUBCATEGORIES
.subcategories {
    .list {
        list-style: none;
        padding: 0;
        margin: 0 0 8px;
        overflow: hidden;
        transition: max-height .2s;
    }
    .item {
        line-height: 0;
        display: inline-block;
        margin: 0 8px 8px 0;
        & > .button {
            display: flex;
            height: 36px;
            line-height: 36px;
            color: @color-black;
            background-color: #FFFDFA;;
            transition: background-color .2s;
            &:hover,
            &:focus {
                color: @color-white;
                background-color: @color-dark-green;
            }
        }
        &.with-image {
            & > .button {
                padding-left: 10px;
            }
        }
    }
    .button-image {
        display: flex;
        align-items: center;
        padding-right: 3px;
        img {
            display: block;
            width: 30px;
            height: 30px;
        }
    }
    .button-text {
        display: inline-block;
        max-width: 252px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .expander {
        display: none;
        margin: 5px 0 10px;
    }
    .expand-button {
        font-weight: 700;
        text-align: center;
        text-overflow: ellipsis;
        display: inline-block;
        position: relative;
        padding: 0 39px 0 15px;
        border: 0;
        &:after {
            .font-awesome-icon('\f078');
            display: block;
            position: absolute;
            top: 13px;
            right: 16px;
            width: 16px;
            height: 16px;
            transform: rotate3d(0, 0, 0, 0);
            //transition: transform .2s;
        }
        //&:hover,
        //&:focus {
        //    background-color: darken(@color-orange, 10%);
        //    transition: background-color .2s;
        //}
        &.active {
            &:after {
                transform: rotate3d(0, 0, 1, 180deg);
            }
        }
    }
    &.expandable {
        .expander {
            display: block;
        }
        &.expanded {
            .expand-button {
                &:after {
                    transform: rotate3d(0, 0, 1, 180deg);
                }
            }
        }
    }
}

// INDEX TYPE SELECT
.products-index-type-select {
    .option {
        color: @color-grey;
        display: inline-block;
        cursor: pointer;
        font-size: 25px;
        margin: 1px 1px;
        padding: 3px 6px;
        line-height: 26px;
        &:hover {

        }
        &.active {
            color: @color-highlighted;
        }
    }
}

// SHOWCASE PRODUCTS INDEX
//.paginator { display: none; }
.showcase-products-index {
    width: @center-width;
    margin: 0 auto;
    padding: 34px 10px 15px 10px;
    text-align: center;
    overflow-x: hidden;
    .index {
        .product {
            width: 25%;
            text-align: center;
            &> .spacer {
                width: 90%;
                margin: 0 auto;
                overflow-x: visible;
            }
            .title {
                margin-bottom: 4px;
                a {
                    font-weight: 700;
                    font-size: 14px;
                    &:hover {
                        color: @color-highlighted;
                    }
                }
            }
            .info {
                .authors {
                    margin-bottom: 0;
                    a {
                        font-size: 12px;
                        color: @color-black;
                        font-weight: 600;
                        &:hover {
                            color: @color-highlighted;
                        }
                    }
                }
            }
            .image {
                width: 180px; 
                margin: 0 auto 11px auto;
                position: relative; //.shadow-spacer
                .clear;
                .shadow-spacer {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    .shadow {
                        visibility: hidden; // shadow is displayed by js only when images are loaded
                        position: relative;
                        bottom: -5px;
                        left: -2px;
                        background: url('@{img-dir}/book-shadow-short.png') left bottom no-repeat;
                        width: 273px;
                        height: 96px;
                    }
                }
                .wrapper {
                    float: left; // to fit content width
                    position: relative; // .special-offer-label
                    .special-offer-label-mixin(9px, 50px)
                }
                a.product-image > img {
                    position: relative;
                    width: 180px; 
                }
            }
        }
    }
}

// TOP 10 PRODUCTS INDEX
.products-index-small-product-mixin() {
    .image {
        margin-bottom: 11px;
        .clear;
        .wrapper {
            float: left; // to fit content width
            position: relative; // .special-offer-label
            .special-offer-label-mixin(20px)
        }
        a.product-image > img {
            display: block;
            width: 100% 
        }
    }            
    .title { 
        font-weight: 700;
        font-size: 12px;
        margin-bottom: 3px;
    }
    .info {
        font-size: 10px;
        .actual-price {    
            font-size: 12px;
        }
        .authors {
            margin-bottom: 7px;
        }
    }
}
.top-10-products-index {
    .index {
        display: block;
        padding: 16px;
        box-shadow: 0 0 14px rgba(0, 0, 0, .14);
        .product {
            width: 100%;
            position: relative;
            padding: 0;
            margin: 11px 0 16px 0;
            & > .spacer {
                .clear;
                box-shadow: none;
            }
            .image {
                float: left;
                width: 80px;
                img {
                    border-radius: 0 !important;
                }
            }
            .stickies {
                display: none;
            }
            .texts {
                float: right;
                position: static;
                width: ~"calc(100% - 80px)";
                padding: 0 0 0 12px;
            }
            .info {
                .manufacturer {
                    display: none;
                }
            }
            .serial-label {
                text-align: center;
                line-height: 22px;
                width: 32px;
                height: 22px;
                top: -11px;
                left: 24px;
                padding: 0 4px;
                border-radius: 9999px;
                background: @color-red;
                &:before {
                    content: "#";
                    display: inline-block;
                }
            }
        }
    }
}
// TOP 10 HOMEPAGE BLOCK
//.top-10 {
//    float: right;
//    width: 580px / @bottom-content-center-width * 100%;
//    padding-left: 0px;
//    h2.title {
//        color: @color-main;
//        margin-bottom: 4px; //21px;
//        border-bottom: 1px solid @color-darkred;
//        padding-bottom: 7px;
//        font-size: 14px;
//        font-weight: 700;
//        letter-spacing: 1.2px;
//        line-height: 120%;
//    }
//}
// while blog is not displayed class '.without-blog' adjusts top-10 styles
.top-10.without-blog {
    float: none;
    width: auto;
    h2.title {
        text-align: center;
    }
    .index {
        .product {
            //width: 10%;
            width: 120px / 1180px * 100%;
            &:nth-child(10n) {
                width: 100px / 1180px * 100%; // 580 px is divided to 4 x 125px + 1 x 80px
                > .spacer {
                    width: auto;
                }
            }   
        }
    }
}

// RECOMMENDED PRODUCTS INDEX
.recommended-products-index {
    .index {
        .product {
            //width: 100% / 7;
            width: 180px / 1180px * 100%;
            &:nth-child(7n) {
                width: 100px / 1180px * 100%; // 1180 px is divided to 6 x 180px + 1 x 100px
                > .spacer {
                    width: auto;
                }
            }
        }
    }
}

// PRODUCT VIEW
.product-view {
    margin-bottom: 87px;
    //padding-top: 10px;
    padding-bottom: 0;
    // padding-right: @narrow-center-indent / @center-width * 100%;
    // padding-left: @narrow-center-indent / @center-width * 100%;
    @product-view-width: 1170 - 2 * @narrow-center-indent;
    @left-col-width: 600px; //723px; //600px;
    @cols-gap: 48px;
    // .attributes-table is used both in .texts and in .attributes
    .attributes-table {
        tr {
            margin: 0;
            font-size: 14px;
            line-height: 18/14;
            letter-spacing: -0.28px;
            td {
                padding-bottom: 12px;
                &:nth-of-type(1) {
                    padding-right: 24px;
                    font-weight: 700;
                    text-align: right;
                }
                &:nth-of-type(2) {
                    font-weight: 500;
                }
            }
        }
    }
    // .section-title is used in .texts, .description, .attributes, .related-products, ...
    .section-title {
        padding-bottom: 23px;
        margin-bottom: 0;
        font-size: 25px;
        line-height: 28/25;
        font-weight: 700;
        color: @color-dark-green;
        letter-spacing: -.7px;
    }
    > .card {
        .clear;
        margin-bottom: 57px;
        > .images {
            position: relative; //.discount-label
            width: @left-col-width / @product-view-width * 100%; 
            margin-bottom: 25px;
            float: left;
            .image {
                display: block;
                position: relative;
                .wrapper {
                    //float: left; // to fit content width
                    position: relative; // .special-offer-label
                    border-radius: 6px;
                    background: white;
                    overflow: hidden;
                    .special-offer-label-mixin(20px, 50px);
                }
                a.product-image {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-height: 450px - 100px - 23px; // / @left-col-width * 100%;
                    & > img {
                        display: block;
                        //max-height: 450px;
                    }
                } 
            }
            .gallery {
                margin-top: 25px;
                .gallery-image {
                    display: block;
                    width: 100px;
                    max-width: 100px;
                    height: 100px;
                    //margin-right: 25px; // this is set by option of owlcarousel
                    & > img {
                        display: block;
                        max-height: 100px;
                        border-radius: 6px;
                    }
                }
            }
            .owl-carousel {
                .owl-carousel();
            }
            .labels {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                text-align: left;
            }
            .discount-label, .new-label {
                display: inline-block;
                padding: 0 20px;
                color: @color-light-cream;
                font-size: 12px;
                font-weight: 800;
                letter-spacing: -0.24px;
                text-transform: uppercase;
                line-height: 21px;
                margin: 12px 0 0 11px;
                background: @color-red;
            }
            .discount-label + .new-label {
                //margin-left: 4px;
            }
            .new-label {
                background: @color-green;
            }
        }
        > .texts {
            float: left;
            padding-left: @cols-gap / @product-view-width * 100%;
            width: 100% - @left-col-width / @product-view-width * 100%; 
            .clear();
            > * {
                display: inline-block;
                vertical-align: top;
            }
            > .info {
                width: 100%;
                .title {
                    font-size: 36px;
                    line-height: 43/36;
                    font-weight: 700;
                    letter-spacing: -1.44px;
                    color: @color-dark-green;
                    margin-top: -5px;
                    margin-bottom: 0px;
                }
                .subtitle {
                    font-size: 17px;
                    line-height: 27/17;
                    font-weight: 500;
                    letter-spacing: -0.17px;
                    margin: 1px 0 0 0;
                }
                .origin {
                    margin-bottom: 10px;
                    font-size: 17px;
                    font-family: @font-family;
                    line-height: 29px;
                    font-weight: 500;
                    color: @color-black;
                    letter-spacing: -.17px;
                    > * {
                        display: inline;
                    }
                    a {
                        text-decoration: none;
                        color: @color-black;
                    }
                    .manufacturer-range {
                        display: block;
                        font-weight: 700;
                        color: @color-black;
                        margin: 0;
                        > * {
                            display: inline;
                            margin: 0;
                        }
                        dt {
                            &:after {
                                content: ':';
                                display: inline;
                            }
                        }
                        a {
                            color: @color-black;
                        }
                    }
                    .authors,
                    .manufacturer,
                    .year {
                        transition: text-decoration .2s;
                        &:focus,
                        &:hover {
                            text-decoration: underline;
                        }
                    }
                    .authors + *:before {
                        content: ' | ';
                    }
                    .manufacturer + .year:before {
                        content: ' , ';
                    }
                }
                .prices {
                    margin: 15px 0 28px 0;
                    > * {
                        display: inline-block;
                    }
                }
                .actual-price { 
                    color: @color-black;
                    font-size: 25px; 
                    font-weight: 700;
                    line-height: 32px;
                    letter-spacing: -.5px;
                    margin-top: -3px;
                }
                .price {
                    color: @color-black;
                    font-size: 15px;
                    line-height: 19px;
                    text-decoration: line-through;
                    width: 100%;
                    letter-spacing: -.3px;
                }
                .savings-rate {
                    font-size: 12px;
                    color: @color-highlighted;
                    margin: 7px 0 0 0;
                }
                .savings {
                    font-size: 12px;
                    color: @color-highlighted;
                    margin: 1px 0 10px 0;
                    > * {
                        display: inline;
                        margin: 0;
                    }
                }
                .disponibility {
                    font-weight: 700;
                    font-size: 14px;
                    line-height: 25px;
                    text-transform: uppercase;
                    letter-spacing: .84px;
                    margin-bottom: 8px;
                    margin-top: 56px;
                    color: @color-black;
                    &.on-stock {
                        color: @color-green;
                    }
                    &.presale {
                        color: @color-red;
                    }
                    &.on-demand {
                        color: @color-orange;
                    }
                    &.at-supplier {
                        color: @color-green;
                    }
                    &.sold-out {
                        color: @color-dark-grey-2;
                    }
                    // .available-from {
                    // }
                     .shipment-time {
                         text-transform: none;
                         letter-spacing: -.28px;
                         line-height: 18px;
                         color: @color-black;
                     }
                }
                .controls {
                    .title {
                        font-size: 18px;
                        font-weight: 700;
                        margin-bottom: 13px;
                        padding-bottom: 6px;
                        border-bottom: 1px solid @color-light-grey-2;
                    }
                    .attribute {
                        margin-bottom: 9px;
                    }
                    .variants {
                        list-style: none;
                        width: 100%;
                        display: flex;
                        flex-wrap: wrap;
                        align-items: flex-start;
                        padding-top: 6px;
                        padding-left: 0px;
                        margin: 0px 0px 58px 6px;
                        li {
                            margin-right: 12px;
                            margin-bottom: 12px;
                            outline-color: transparent;
                            outline-width: 3px;
                            outline-style: solid;
                            transition: outline-color 0.2s;
                            &:hover, &.active {
                                outline-color: @color-black;
                            }
                            a {
                                display: flex;
                                @-item-per-full-row: 3;
                                max-width: (734px - @-item-per-full-row* 12px) / @-item-per-full-row;
                                padding-right: 5px;
                                font-size: 13px;
                                line-height: 14/13;
                                text-decoration: none;
                            }
                            img {
                                margin-right: 5px;
                                height: 33px;
                                border: 1px solid @color-light-grey-2;
                            }
                        }
                    }
                    .add-to-cart-button {
                        display: flex;
                        margin-bottom: 5px;
                        .to-cart-change, .to-cart-count, .to-cart-button{
                            float: left;
                        }
                        .to-cart-change {
                            width: 30px;
                            height: 58px;
                            text-align: left;
                            display: inline-block;
                            .plus, .minus{
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                background: @color-light-grey-6;
                                height: 50%;
                                width: 30px;
                                color: @color-black;
                                font-weight: 700;
                                position: relative;
                                cursor: pointer;
                                &:first-child{
                                    border-radius: 6px 0 0 0;
                                }
                                &:last-child{
                                    border-radius: 0 0 0 6px;
                                }
                                &::after {
                                    font-family: FontAwesome;
                                    position: absolute;
                                    font-size: 12px;
                                    font-weight: 700;
                                }
                            }
                            .plus::after {
                                content: "\f067";
                            }
                            .minus::after {
                                content: "\f068";
                            }
                        }
                        .to-cart-count{
                            .number-of-pieces{
                                width: 78px;
                                height: 58px;
                                font-size: 15px;
                                text-align: center;
                                border: 2px solid @color-light-grey-6;
                            }
                        }
                        .to-cart-button{
                            width: 100%;
                            img{
                                height: 16px;
                                width: 16px;
                            }
                            .button{
                                height: 58px;
                                width: 100%;
                                max-width: 240px;
                                font-size: 15px;
                                padding-left: 34px;
                                cursor: pointer;
                                background: url('@{img-dir}/cart-icon-white.png')  68px center no-repeat @color-dark-green;
                                background: url('@{img-dir}/cart-icon-white.svg')  68px center no-repeat @color-dark-green;
                                background-size: 16px;
                                border-radius: 0 6px 6px 0;
                                transition: all 0.2s;
                                &:hover,
                                &:focus {
                                    
                                }
                            }
                        }
                    }
                    .add-to-wishlist {
                        color: @color-black;
                        font-size: 14px;
                        line-height: 19/14;
                        text-decoration: none;
                        &:hover,
                        &:focus {
                            text-decoration: underline;
                            color: @color-highlighted;
                        }
                        .fas {
                            position: relative;
                            top: 1px;
                            left: 1px;
                            margin-right: 9px;
                            color: #95543D;
                            font-size: 16px;
                            line-height: 18/16;
                        } 
                    }
                    .add-availability-alert {
                        width: 160px;
                        .text-input {
                            font-size: 14px;
                            width: 100%;
                            border-radius: 4px 4px 0 0;
                            margin: 0;
                            & + button {
                                border-radius: 0 0 4px 4px;
                            }
                        }
                        button {
                            &:before{
                                .font-awesome-icon('\f0f3');
                                margin-right: 4px;
                            }
                            width: 100%;
                            margin: 0 0 4px;
                        }
                        .note {
                            color: @color-grey;
                            font-size: 12px;
                            text-align: left;
                        }
                    }
                }
            }
            .socials {
                margin-top: 8px;
            }
        }
        > .description {
            display: block;
            float: left;
            width: @left-col-width / @product-view-width * 100%; 
            font-size: 14px;
            line-height: 24/14;
            margin-top: 87px;
            .description-text {
                position: relative;
                letter-spacing: -.28px;
                .text{
                    position: relative;
                    transition: max-height .2s;
                    overflow: hidden;
                    &:after {
                        content: '';
                        display: none;
                        position: absolute;
                        bottom: 0;
                        width: 100%;
                        height: 17px;
                        background: linear-gradient(fade(@color-white, 0), @color-main-grey);
                        opacity: 1;
                        transition: opacity .2s;
                    }
                }
            }
            .more-button {
                display: none;
                color: @color-dark-green;
                font-size: 16px;
                font-weight: 700;
                line-height: 32px;
                position: relative;
                height: 32px;
                padding-right: 20px;
                cursor: pointer;
                &:after {
                    .font-awesome-icon('\f078');
                    line-height: 13px;
                    display: block;
                    position: absolute;
                    top: 11px;
                    right: 0;
                    width: 16px;
                    height: 16px;
                    transform: rotate3d(0, 0, 0, 0);
                    transition: transform .2s;
                }
                &:hover,
                &:focus {
                    color: @color-dark-green;
                    transition: color .2s;
                }
                &.active {
                    &:after {
                        transform: rotate3d(0, 0, 1, 180deg);
                    }
                }
            }
            &.expandable {
                .description-text {
                    .text {
                        &:after {
                            display: block;
                        }
                    }
                }
                .more-button {
                    display: inline-block;
                }
                &.expanded {
                    .description-text {
                        .text{
                            &:after {
                                opacity: 0;
                            }
                        }
                    }
                    .more-button {
                        &:after {
                            transform: rotate3d(0, 0, 1, 180deg);
                        }
                    }
                }
            }
            > .video {
                margin-top: 57px;
                > * {
                    display: block;
                    width: 100%;
                    max-width: 100%;
                }
            }
        }
        > .attributes {
            margin-top: 87px;
            float: left;
            padding-left: @cols-gap / @product-view-width * 100%;
            width: 100% - @left-col-width / @product-view-width * 100%; 
        }
    }
    > .recommended-products {
        margin-right: -@narrow-center-indent / (@center-width - 2 * @narrow-center-indent) * 100%;
        margin-left: -@narrow-center-indent / (@center-width - 2 * @narrow-center-indent) * 100%;
        .author-products,
        .bought-with-products,
        .related-products,
        .accessory-products {
            margin-bottom: 40px;
            .section-title {
                padding-bottom: 35px
            }
            .products-index {
                width: auto;
                .index {                
                    .product {
                        // copy of top-10-products-index
                        .products-index-small-product-mixin();
                    }
                }
            }
        }
    }
    > .special-offers {
        float: left;
        width: 580px / 890px * 100%;
        margin-bottom: 40px;
        .header {
            margin-bottom: 17px;
            border-bottom: 1px solid #d8dada;
            padding-bottom: 0;
            font-size: 14px;
            font-weight: 700;
            letter-spacing: 1.7px;
            text-transform: uppercase;
        }       
        .intro-text {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 17px;
        }
        .special-offer-banner-mixin();
    }
    > .comments {
        width: @left-col-width / @product-view-width * 100%;
        margin-bottom: 100px;
        > .internal-comments {
            .header {
                margin-bottom: 23px; 
                font-size: 25px;
                letter-spacing: -.7px;
                line-height: 28px;
            }
            > .card {
                form {
                    .form-fields {
                        .name-and-rating {
                            .name {
                                .input-wrapper {
                                    input[name="data[name]"] {
                                        max-width: 370px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        > .facebook-comments {
            float: right;
        }
    }
}
// set the products-index widths like this because of high specificity
#content-main {
    .product-view {
        .author-products,
        .bought-with-products,
        .accessory-products, 
        .related-products {
            .products-index {
                width: auto;
            }
        } 
    }
}

// SPECIAL OFFER VIEW
.special-offer-view {
    margin-top: 55px;
    margin-bottom: 55px;
    & > .title {
        font-size: 25px;
        font-weight: 700;
        color: @color-darkred;
        line-height: 1.2em;
    }
    & > .image {
        margin-bottom: 17px;
        img {
            display: block;
            width: 100%;
        }
    }
    & > .description {
    }
}

// SPECIAL OFFERS MENU
.special-offers-menu {
    .side-menu;
    .title {
        text-transform: uppercase;
        text-align: right;
        padding-right: 15px;
        color: @color-darkred;
    }
    &.without-title {
        margin-top: -45px;
    }
    .label > img {
        display: block;
    }
}

// CATEGORIES MENU
//.categories-menu {
//    .side-menu;
//    .title {
//        text-transform: uppercase;
//        text-align: right;
//        padding-right: 15px;
//        color: @color-darkred;
//    }
//}

// DEALERSHIP MENU
// MANUFACTURERS MENU
.dealership-menu,
.manufacturers-menu,
.product-groups-menu {
    .side-menu;
    .title {
        text-align: center;
        color: @color-darkred;
        text-transform: uppercase;
    }
}

// ESHOP BREADCRUMBS
.eshop-breadcrumbs {
}

// WISHLIST PRODUCTS
.wishlist-products-index {
    padding: 16px 0 24px;
    .wishlist-controls {
        margin-top: 4px;   
    }
    .text-input {
        width: 100%;
        border-radius: 6px;
        padding: 0 4px;
        margin: 0 0 4px 0;
        cursor: pointer;
        border: 1px solid transparent;
        &:hover{
            border: 1px solid @color-light-grey-2;
        }
    }
    .index {
        .product {
            .image {
                margin-bottom: 0;
            }
            .button {
                color: @color-white;
                white-space: nowrap;
                width: 100%;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }
}

// MANAGE WISHLIST
.manage-wishlist {
    background-color: @color-light-grey-2;
    border-radius: 6px;
    margin: 0 0 24px 0;
    padding: 8px 8px 0;
    .select-wishlist {
        max-width: 290px;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 16px;
        margin: 0 0 8px 0;
    }
    .setup-wishlist {
        .button {
            margin: 0 8px 8px 0;
        }
    }
    #addWishlistForm, 
    #editWishlistForm, 
    #shareWishlistForm {
        display: none;
        table {
            td {
                padding: 8px 8px 0 0;
                vertical-align: middle;
                &.label {
                    padding: 8px 16px 0 0;
                }
                &.submit {
                    text-align: right;
                }
                .text-input {
                    padding: 0 16px;
                    margin: 0;
                }
            }
        }
        input,
        select {
            max-width: 290px;
        }
        .whishlist-url {
            font-weight: 700;
            display: inline-block;
            max-width: 290px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    #shareWishlistForm {
        form {
            display: flex;
            flex-direction: row;
            .text-input {
                border-radius: 4px 0 0 4px;
            }
            .button {
                text-align: center;
                border-radius: 0 4px 4px 0;
                max-width: 290px;
            }
        }
    }
}

.wishlist-search {
    background-color: @color-main-grey;
    border-radius: 4px;
    box-shadow: 0px 0px 14px #ddd;
    margin: 0 0 24px 0;
    padding: 16px;
    .search-box {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        label {
            text-align: left;
            padding: 0;
            flex: 1 0 100%;
            margin-bottom: 4px;
        }
        .text-input {
            flex: 0 1 290px;
            border-radius: 4px 0 0 4px;
            margin: 0;
        }
        .button {
            text-align: center;
            border-radius: 0 4px 4px 0;
        }
    }
}
.owners-wishlists {
    font-size: 16px;
    .wishlist-link {
        font-weight: 700;
    }
}
//.users-wishlists {    
//    margin-bottom: 58px;
//    .section-header {
//        text-align: left;
//    }
//    table {
//        width: 100%;
//        td {
//            padding: 16px 0;
//            vertical-align: top;
//            &.user-wishlists-products {
//                text-align: right;
//                a {
//                    display: inline-block;
//                }
//                a + a {
//                    margin-left: 10px;
//                }
//            }
//        }
//    }
//}

.tag-cloud {
    a {
        line-height: 1.6;
        color: @color-dark-green;
        text-decoration: none;
        &:hover {
            text-decoration: underline;
        }
    }
}

// CKEDITOR CUSTOM STYLES
.-cke-no-horizontal-scrollbar {
    width: 100% !important;
    min-width: 0 !important;
    margin: 0 !important;
    padding: 10px !important;
}

.bx-wrapper {
    margin: 0 auto;
    .bx-viewport {
        left: 0;
        border: none;
        border-radius: 4px;
        margin-top: 16px;
        box-shadow: none;
    }
    .bx-controls-direction {
        a {
            .slider-arrow;
        }
    }
    .bx-pager {
        bottom: 8px;
        padding: 0;
        &.bx-default-pager {
            a {
                width: 10px;
                height: 10px;
                border: 1px solid @color-white;
                background-color: transparent;
                &.active {
                    background-color: @color-white;
                }
            }
        }
        
    }
    .bx-next {
        transform: rotate(180deg);
    }
}

// MENU ANNOUNCEMENT
.menu-announcement {
    min-height: 58px;
    border-radius: 4px;
    margin-top: 10px;
    background: @color-light-cream; //default
    .text {
        padding: 16px;
        display: block;
        margin: 0;
        &.link {
            color: inherit;
            text-decoration: none;
        }
    }
    &.lottery {
        .text {
            background: url('@{img-dir}/wheel-of-fortune.png') no-repeat; // svg fallback
            background: url('@{img-dir}/wheel-of-fortune.svg') no-repeat;
            background-size: 40px auto;
            background-position: 16px center;
            padding-left: 68px;
        }
    }
}

// verify voucher
.verify-voucher {
    margin-top: 50px;
    margin-bottom: 50px;
    .input-group-wrapper {
        .clear;
    }
    .label-wrapper,
    .input-wrapper {
        float: left;
        padding-left: 15px;
        padding-right: 15px;
    }
    .label-wrapper {
        width: 205px;
        label {
            float: none;
        }
        .sc-change {
            float: right;
            position: relative;
            top: -8px;
        }
    }
    .text-input {
        width: 220px;
        letter-spacing: 5px;
        font-weight: 700;
        font-size: 16px;
    }
    button {
        margin-left: 220px;
    }
    .result-wrapper {
        margin-top: 15px;
        margin-left: 220px;
    }
    .result {
        display: inline-block;
        padding: 13px 15px 14px;
        font-size: 16px;
        line-height: 1.25;
        font-weight: 700;
        color: @color-white;
        border-radius: @checkout-border-radius;
        background-color: @color-red;
        &.active {
            background-color: @color-green;
        }
        &.inactive {
            background-color: @color-red;
        }
    }
}

// CHRISTMASS STYLES
//@import "_vianoce.less";
// RESPONSIVE STYLES
@import "_responsive.less";

