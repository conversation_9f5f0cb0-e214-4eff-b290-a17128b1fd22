// Include this file at the end of main.less, before _resposive.less is included

#header-top {
    min-height: 79px + 51px;
    padding-bottom: 33px + 51px;
    background: url('@{img-dir}/vianoce/topobluk.png') center bottom no-repeat, url('@{img-dir}/vianoce/header-top-bg.png') center bottom repeat-x, url('@{img-dir}/vianoce/winter.png') center bottom no-repeat, url('@{img-dir}/vianoce/header-top-bottom-bg.png') center bottom repeat-x;
    background-color: #ded9d7;    
}
header.with-showcase {
    #header-top {
        background: url('@{img-dir}/vianoce/topobluk.png') center bottom no-repeat, url('@{img-dir}/vianoce/header-top-bg.png') center bottom repeat-x, url('@{img-dir}/vianoce/winter.png') center bottom no-repeat, url('@{img-dir}/vianoce/header-top-bottom-with-showcase-bg.png') center bottom repeat-x;
        background-color: #ded9d7;    
    }
}
#header-bottom {
    margin-top: -51px;
    background: url('@{img-dir}/vianoce/snow.png') center top repeat-x, #f1edea url('@{img-dir}/header-bottom-bg.png') center top repeat-x;
    #mini-cart {
        top: 30px;
    }
    #facebook-logo {
        top: 30px;
    }
}
header.with-showcase {
    #header-bottom {
        background: url('@{img-dir}/vianoce/snow.png') center 236px repeat-x, #e6ded9 url('@{img-dir}/header-bottom-high-bg.png') center top repeat-x;
    }    
}


