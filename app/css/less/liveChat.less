@import "_variables.less";
@import "_mixins.less";

@live-chat-color-border: #DAD4D2; //@color-border
// GENERAL
.footerColor {
    background: #FBFBF8;
}
.bodyColor {
    background: @color-white;
}
.txtColor {
    color: @color-black;
}
.linkColor {
    color: @color-black;
    text-decoration: underline;
    transition-duration: @menu-transition-duration;
    transition-property: color;
    &:hover, &:focus {
        text-decoration: none;
        color: @color-highlighted;
    }
    &:active, &:focus {
        outline: 0;
    }
}
.bordColor {
    border-color: @live-chat-color-border;
}
// CLOSED CHAT
#body {
    box-shadow: none;
    border: 1px solid @live-chat-color-border;
    background: @color-white;
    &.pageStartOnline, &.pageStartOffline {
        opacity: 0.8;
    }
}
.ques {
    width: 100%;
    box-shadow: none;
    border: 1px solid @live-chat-color-border;
    > a {
        right: 5px;
        top: -2px;
        font-size: 15px;
        color: @color-black;
        transition-duration: @menu-transition-duration;
        transition-property: color;
        &:hover { 
            color: @color-alert;
        }
    }
    .msgBox {
        img {
            display: none;
        }
        &:before {
            content: '';
            display: inline-block;
            height: 49px;
            width: 73px;
            background: url('@{img-dir}/logo.png') center no-repeat;
            background-size: 56px;
        }
    }
    .ilu {
        background: none;
        left: 10px;
        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 12px 14px 0 14px;
            border-color: @live-chat-color-border transparent transparent transparent;  
        }
        &:after {
            content: '';
            position: absolute;
            top: 0;
            left: 1px;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 11px 13px 0 13px;
            border-color: @color-white transparent transparent transparent;  
        }
    }
}
.footerStart .On {
    background-image: url('@{img-dir}/live-chat-status.png');
    color: #48A163;
}

// OPENED CHAT ONLINE
.infoBox {
    img {
        height: 30px;
        width: 30px;
    }
    .opInf {
        margin-top: 10px;
    }
}
.header {
    height: 35px; 
}
.window {
    > a {
        .minMaxRes {
            background-color: @color-black;
            transition-duration: @menu-transition-duration;
            transition-property: background-color;
        }
        .txtColor {
            color: @color-black;
            transition-duration: @menu-transition-duration;
            transition-property: color;
        }
        &:hover { 
            .minMaxRes {
                background-color: @color-alert;
            }
            .txtColor {
                color: @color-alert;
            }
        }
    }
}
.chatBox {
    box-shadow: none;
    border: 1px solid @live-chat-color-border;
}
.wraper {
    top: 40px;
}
.inputText {
    textarea {
        box-shadow: none;
        border: 1px solid @live-chat-color-border;
        background: none;
        &:focus {
            outline-style: none;
        }
    }
}
.op {
     li {
        filter: grayscale(100%);
        transition-duration: @menu-transition-duration;
        transition-property: filter;
        &:hover {
            filter: none; 
        }
     }
}
.footer {
    .home {
        display: none;
    }
    &:before {
        content: '';
        display: inline-block;
        height: 35px;
        width: 73px;
        background: url('@{img-dir}/logo.png') center no-repeat;
        background-size: 56px;
    }
}
// OPENED CHAT OFFLINE
.pageOffline {
    .infoBox {
        img {
            display: none;
        }
        .opInf {
            //margin-left: 0;
        }
    }
    .inputText {
        height: 76px;
        width: 233px;
        left: 16px;
        textarea {
            float: none;
            display: inline-block;
            height: 76px;
        }
        input {
            float: none;
            display: inline-block;
            width: 36px;
            height: 40px;
            border: none;
            filter: grayscale(100%);
            background: url('@{img-dir}/live-chat-send.png') center no-repeat;
            transition-duration: @menu-transition-duration;
            transition-property: filter;
            &:hover {
                border: none;
                filter: none;
                background-position: center;
            }
        }
    }
}
.offMess {
    margin-left: 16px;
    width: ~'calc(100% - 16px)';
    select, input{
        box-shadow: none;
        border: 1px solid @live-chat-color-border;
        background-color: @color-white;
        &:focus {
            outline-style: none;
        }
    }
} 
