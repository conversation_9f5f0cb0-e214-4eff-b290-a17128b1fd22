// Bootstrap core variables and mixins
@import "../../vendors/less/bootstrap/variables.less";
@import "../../vendors/less/bootstrap/mixins.less";
// local variables
@import "_variables.less";
@import "../_fonts.less";

.-run-file-manager {
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
    a {
        text-decoration: none;
        &:hover {
            text-decoration: none;
        }
    }
    .-run-fmg-header {
        padding: 8px 25px 0px 25px;
        color: @color-admin-header;
        background-color: white; //background-color: @color-admin-header-bg;
        border-bottom: 1px solid @color-admin-header-border; 
        // for all children elements set:
        & > * {
            height: 34px;
            line-height: 34px;
            margin-right: 20px;
            margin-bottom: 9px;
        }
        .-run-fmg-title {
            font-size: 21px;
            color: #262d34;
            font-weight: 300;
        }
    }
}