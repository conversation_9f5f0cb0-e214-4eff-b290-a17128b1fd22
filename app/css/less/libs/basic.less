//
// This stylesheet contains basic styles for stylin basic element used in each project in +- the same form
//
@import "../../less/_variables.less";
//@import "../../libs/less/_variables.less";
@import "../../less/_mixins.less";

// set border-box sizing
* {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
*:before,
*:after {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
input[type="search"] {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}

// assure autoresponsiveness of images
img {
    max-width: 100%;
    height: auto;
}

// COLORS
.color-white {
    color: @color-white !important;
    * {
        color: @color-main !important;
    }
}
.color-black {
    color: @color-white !important;
    * {
        color: @color-main !important;
    }
}
.color-highlighted {
    color: @color-highlighted !important;
    * {
        color: @color-highlighted !important;
    }
}
.color-alert {
    color: @color-alert !important;
    * {
        color: @color-alert !important;
    }
}

// APP MESSAGES
.app-messages {
    overflow: visible;
    position: fixed;
    top: 0;
    z-index: 999;
    width: 100%;
    * {
        box-sizing: content-box; // keep CSS 2.1 sizing
    }
    .spacer {
        overflow: visible;
        position: relative; // .panel 
        margin: 0 auto;
        width: 334px;
    }
    .panel {
        position: absolute;
        top: 0;
        left: 0;
        width: 334px;
        padding-bottom: 27px;
        background: url(/app/img/basic/app_message_bottom_bg.png) no-repeat bottom center;
        .bg {
            padding: 14px 31px 4px 31px;
            background: url(/app/img/basic/app_message_bg.png) repeat-y;

        }
        a.close {
            float: right;
            cursor: pointer;
            width: 22px;
            height: 22px;
            text-decoration: none;
            background: url(/app/img/basic/close.png) no-repeat center;    
            &:hover {
                text-decoration: none;
            }
        }
        .app-message {
            color: black;
            margin: 10px 0px;
            min-height: 16px;
            background-position: 0px 0px;
            background-repeat: no-repeat;
            font-size: 14px;
            font-weight: bold;
            padding: 0 0 5px 0;
            &.error {
                color: #A80D2D;
            }
        }
    }
}
// modal app messages (implemented by sweetalert)
// - sweetalert tweaks
.swal-overlay {
    overflow-y: auto;
    background-color: rgba(150, 150, 150, .5);
    &:before {
        height: auto;
    }
}
.swal-modal {
    vertical-align: text-bottom;
    box-shadow: 0 0 50px rgba(0,0,0,.15);
    margin: 30px auto;
    padding: 30px 50px 35px;
}
.swal-title {
    margin-bottom: 20px;
    padding-top: 15px;
    padding-right: 0;
    padding-left: 0;
    text-align: center;
}
.swal-text {
    margin-bottom: 20px;
    padding-right: 0;
    padding-left: 0;
    text-align: center;
}
.swal-content {
}
.swal-button {
    height: auto;
    line-height: normal;
    color: #fff;
    background-color: @color-button-bg;
    &:focus {
        // box-shadow: 0 0 0 1px #fff, 0 0 0 3px rgba(43,114,165,.29); // default
    }
    &:active {
        background-color: @color-button-bg;
    }    
}
.swal-footer {
    text-align: center;
    white-space: nowrap;
    padding-left: 0;
    padding-right: 0;
}
// - app modal messages
.app-messages-modal {
    text-align: left;
    .app-message {
        color: black;
        margin: 10px 0px;
        min-height: 16px;
        background-position: 0px 0px;
        background-repeat: no-repeat;
        font-size: 14px;
        font-weight: bold;
        padding: 0 0 5px 0;
        &.error {
            color: #A80D2D;
        }
        a {
            text-decoration: underline;
            &:hover {
                text-decoration: none;
            }
        }
    }
}

// COOKIES CONSENT
.cookies-consent {
    border-bottom: 2px solid @color-highlighted;
    background-color: #fff;
    > table {
        width: 100%;
        border: none;
        td {
            vertical-align: middle;
            padding: 20px 0;
            &.info {
                padding-left: 20px;
            }
            &.controls {
                padding-right: 20px;
                white-space: nowrap;
                .more-info, .submit {
                    margin-left: 20px;
                }
            }
        }
    }
    &.fixed-at-bottom {
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 9999;
        max-height: 100vh;
        overflow: auto;
        border-bottom: none;
        border-top-right-radius: 5px;
        > .side-label {
            //font-size: 1em;
            padding: 4px 10px 5px;
        }
        > table {
            display: none;
        }
        &:hover, &:focus, &:active, &.opened {
            right: 0;
            border-top: 2px solid @color-highlighted;
            border-top-right-radius: 0;
            > .side-label {
                display: none;
            }
            > table {
                display: table;
            }
        }
    }
}
@media screen and (max-width: 1000px) {
    .cookies-consent {
        > table {
            display: block;
            td {
                display: block;
                &.info {
                    padding-right: 20px;
                    padding-bottom: 0;
                }
                &.controls {
                    padding-left: 20px;
                    padding-top: 20px;
                    white-space: normal;
                }
            }
        }
    }
}
@media screen and (max-width: 500px) {
    .cookies-consent {
        > table {
            td {
                &.controls {
                    text-align: center;
                    .more-info {
                        display: block;
                        margin-left: 0;
                        margin-bottom: 10px;
                    }
                    .submit {
                        margin-left: 0;
                    }
                }
            }
        }
    }
}

.app-announcement {
    color: @color-white;
    background-color: @color-dark-green;
    & > .center {
        position: relative;
        padding: 12px 40px 16px 16px;
    }
    .close {
        color: @color-white;
        font-size: 32px;
        font-weight: 700;
        text-align: center;
        line-height: 24px;
        position: absolute;
        width: 24px;
        height: 24px;
        top: 11px;
        right: 8px;
        cursor: pointer;
        transition: color .2s;
        &:focus,
        &:hover {
            color: @color-red; 
        }
    }
    .title {
        font-size: 16px;
        font-weight: 700;
        transition: color .2s;
        &:focus,
        &:hover {
            color: @color-red;
        }
    }
    .text {
        margin-top: 8px;
    }
    .ellipsis {
        display: none;
    }
    &.with-hidden-text {
        .text {
            display: none;
        }
        .ellipsis {
            display: inline-block;
        }
    }
    &.with-title.with-text {
        cursor: pointer;
    }
}
@media screen and (max-width: 1000px) {
    .app-announcement {
        //position: fixed;
    }
}

// FLEXBOX WARNING
.flexbox-warning {
    display: none;
    position: fixed;
    left: 50%;
    z-index: 9999;
    width: 100%;
    max-width: 780px;
    padding: 0 24px;
    transform: translate3d(-50%, 0, 0);
    & > .wrapper {
        color: @color-white;
        position: relative;
        padding: 16px 48px 24px 24px;
        border-radius: 0 0 4px 4px;
        background-color: fade(@color-red, 90%);
        box-shadow: 0 0 24px @color-red;
    }
    .close {
        font-size: 32px;
        font-weight: 700;
        text-align: center;
        line-height: 24px;
        position: absolute;
        width: 24px;
        height: 24px;
        top: 16px;
        right: 16px;
        cursor: pointer;
        transition: color .2s;
        &:focus,
        &:hover {
            color: @color-light-cream;
        }
    }
    .title {
    }
    .text {
        font-size: 16px;
        //font-weight: 700;
        line-height: 1.5;
        margin: 0;
    }
}
.no-flexbox {
    .flexbox-warning {
        display: block;
    }
}

// EMPTY INDEX MESSAGE
.empty-index-message {
    text-align: center;
    //font-style: italic;
}

 // SCROLL TO TOP
.scroll-to-top {
    display: none;
    position: fixed;
    bottom: 13px;
    right: 15px;
    z-index: 100;    
    overflow: hidden;
    border: none;
    width: 24px;
    height: 26px;
    text-indent: -9999px;
    text-decoration: none;
    opacity: 0.4;
    background: url('@{img-dir}/basic/arrow-scroll-to-top.png') no-repeat;
}

// Mask, loader and progress
.-run-mask {
    /*background: #0389D2; //#AAA;*/
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin-right: auto;
    margin-left: auto;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: 1;
    text-align: center;
    background: #fff;
}
.-run-loader {
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);    
    width: 150px;
    height: 150px;
    border: 4px solid transparent;
    background: url(/app/img/libs/logo-run.png) no-repeat center center transparent;
    >span {
        position:absolute;
        top: -4px;
        left: -4px;
        width:150px;
        height:150px;
        border: 4px solid rgba(0, 0, 0, .18);
        animation: animateRunLoader 1s infinite linear;
        .border-radius(999px);
        >span {
            position:absolute;
            top: -4px;
            left: -4px;
            width:150px;
            height:150px;
            border: 4px solid transparent;
            border-top: 4px solid #0081c7;
            .border-radius(999px);
        }
    }
}
@keyframes animateRunLoader {
    0% {transform: rotate(0deg);}
    100% {transform: rotate(360deg);}
}
.-run-mask-progress {  
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);    
    width: 90%;
    height: 20px;
    margin-bottom: 20px;
    overflow: hidden;
    background-color: #f5f5f5;
    border-radius: 4px;
    box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
    .-run-mask-progress-bar {    
        float: left;
        width: 0;
        height: 100%;
        font-size: 12px;
        line-height: 20px;
        color: #fff;
        text-align: center;
        background-color: #0081c7;
        box-shadow: inset 0 -1px 0 rgba(0,0,0,.15);
        transition: width .6s ease;    
        background-image: linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);
        background-size: 40px 40px;    
        animation: animateRunProgressBar 2s linear infinite;    
    }
}
@keyframes animateRunProgressBar {
    from {background-position: 40px 0;}
    to {background-position: 0 0;}
}

.-run-smart-frame-wrapper {
    position: relative;
    .-run-mask {
        position: absolute;
    }
    .-run-loader {
        position: absolute;
    }
}

// Hints
.-run-hint {
    //vertical-align: super;
    position: relative;
    top: -0.35em;
    padding: 0 0.3em;
    font-size: smaller;
    cursor: help;
    opacity: 0.4;
    &:hover {
        opacity: 0.8;
    }
}
.tooltipster-run {
	border-radius: 5px;
	background: #FFF18F; //#fff;
    border: 1px solid #F2E37B;
	box-shadow: 0px 0px 14px rgba(0,0,0,0.3);
	color: #4F4B47; //#2c2c2c;
    .tooltipster-content {
        overflow: auto !important;
        max-height: 250px;
        padding: 8px 10px;
        line-height: 16px;
        font-size: 14px;
        font-family: 'Arial', sans-serif;
    }
}

// Image display input
.display-image-input {
    background-color: #EEE;
}
// Delete button wrapper of display and image display input
.display-input-wrapper,
.display-image-input-wrapper {
    display: inline-block;
    zoom: 1;
    position: relative;
    .display-input,
    .display-image-input {
        //padding-right: 28px;
        border-right: 25px solid #fff;
    }
    .display-delete-button {
        position: absolute;
        bottom: 0;
        right: 0;
        color: #000;
        font-size: 26px;
        line-height: 1em;
        font-weight: 700;
        text-decoration: none;
        opacity: 0.5;
        cursor: pointer;
        &:hover {
            opacity: 1;
            color: #C30404;
        }
    }
}

// Error screen
.-run-error-screen {
    padding: 0 10px;
    b {
        display: block;
        margin-bottom: 1em;
    }
    .app-message {
        float: left;
    }
}

// error page 404
.-run-error-page-404 {
    text-align: center;
    padding-top: 55px;
    padding-bottom: 135px;
    .status {
        font-size: 120px;
        font-weight: 400;
    }
    .title {
        font-weight: 700;
        margin-bottom: 6px;
    }
}

// Link for login to original user from switched user
#loginToOriginalUser {
    background-color: #006ba5;
    padding: 1px 5px 2px 8px;
    position: fixed;
    right: 0;
    top: 0;
    -webkit-border-bottom-left-radius: 12px;
    -moz-border-radius-bottomleft: 12px;
    border-bottom-left-radius: 12px;
    z-index: 1000;
    a {
        color: #fff;
        font-size: 13px;
        text-decoration: none;
    }
}
#admin-layout {
    #loginToOriginalUser {
        top: auto;
        bottom: 0;
        border-bottom-left-radius: 0px;
        border-top-left-radius: 12px;
        padding: 2px 5px 1px 8px;
    }
}

// JQUERY UI WIDGETS
.ui-widget-content.ui-datepicker,
.ui-widget-content.ui-autocomplete {
    border-color: #ddd;
    color: #333;
    .ui-widget-header {
        border-color: #777;
        background: #777;
        .ui-state-hover,
        .ui-state-focus {
            border-color: #777;
            background: #777;
            color: #333;
        }
        .ui-datepicker-prev,
        .ui-datepicker-next {
            cursor: pointer;
        }
    }
    .ui-state-default {
        border-color: #ddd;
        background: #ddd;
        color: #333;
    }
    .ui-state-hover,
    .ui-state-focus {
        border-color: #eee;
        background: #eee;
        color: #333;
    }
    .ui-state-highlight,
    .ui-state-active {
        border-color: #eee;
        background: #eee;
        color: #333;
    }
    .ui-state-active {
        border-color: #777;
    }
}
