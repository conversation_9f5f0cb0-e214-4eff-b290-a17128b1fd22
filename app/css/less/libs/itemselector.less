.itemselector {
    width: 100%;
    //float: left;
    overflow: hidden;
    padding: 10px;
    &.has-error {
        border-color: #C30404;
    }
    .break {
        width: 2%;
        float: left;
        height: 143px;
    }
    .input-group {
        margin: 6px 7px;
    }
    .search-message {
        font-size: 12px;
        padding: 0 9px;
        color: #0782C1;
    }
    fieldset {
        width: 49%; 
        height: 250px; 
        overflow: hidden; 
        float: left;
        margin-bottom: 0;
        legend {
            border: 0 none;
            font-size: inherit;
            margin: 0 6px;
            padding: 0 5px;
            width: auto;
            font-weight: bold;
        }
    }
    .sortable { 
        list-style-type: none; 
        margin: 0; 
        padding: 5px 7px; 
        display: block;
        overflow: auto; 
        float: left;
        width: 100%;
        height: 100%;
        li { 
            display: inline-block;
            float: left;
            padding: 1px 6px; 
            width: 100%; 
            cursor: pointer;
            margin: 2px 0;
            width: 100%;
        }
    }
}