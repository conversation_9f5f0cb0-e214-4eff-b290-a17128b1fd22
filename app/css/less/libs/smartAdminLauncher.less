// Bootstrap core variables and mixins
@import "../../vendors/less/bootstrap/variables.less";
@import "../../vendors/less/bootstrap/mixins.less";
// local variables
@import "../_variables.less";
@import "_variables.less";
@import "../_mixins.less";

.-run-sal-record {
    position: relative !important;
    > .-run-sal-trigger {
        cursor: pointer !important;
        display: none !important;
        position: absolute !important;
        top: 0;
        right: 0;
        z-index: 100 !important;
        margin: 0 !important;
        width: 15px !important;
        height: 15px !important;
        padding: 0 0 0 1px !important;
        line-height: 12px !important;
        text-align: center !important;
        //background: @color-admin-header-bg !important;
        background: #458fd2 !important;
        color: @color-admin-header-link !important;
        &:hover {
            color: @color-admin-header-link-focus !important;
        }
        &:before {
            display: inline-block !important;
            font: normal normal normal 11px/1 FontAwesome !important;
            text-rendering: auto !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
            content: '\f040' !important; //pencil
            //content: '\f246' !important; // cursor
        }
    }
    &:hover {
        //outline:1px dotted @color-admin-header-bg !important;
        outline: 2px dotted #458fd2 !important;
        > .-run-sal-trigger {
            display: block !important;
            &.-run-sal-trigger-hidden {
                display: none !important;
            }
        }
    }
}
#content-block-preview-layout {
    .-run-sal-record:hover {
        > .-run-sal-trigger {
            display: none !important;
        }
    }
}