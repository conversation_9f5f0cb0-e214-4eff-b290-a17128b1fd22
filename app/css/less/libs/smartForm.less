// Bootstrap core variables and mixins
@import "../../vendors/less/bootstrap/variables.less";
@import "../../vendors/less/bootstrap/mixins.less";
// local variables
@import "_variables.less";
@import "../_fonts.less";
@import "../_variables.less";
@import "../_mixins.less";


.-run-smart-form {
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
    .-run-sfo-header {
        .clearfix;
        padding: 8px 25px 0px 25px;
        color: @color-admin-header;
        background-color: @color-admin-header-bg;
        border-bottom: 1px solid @color-admin-header-border;
        // for all children elements set:
        & > * {
            float: left;
            height: 34px;
            line-height: 34px;
            margin-right: 20px;
            margin-bottom: 9px;
        }
        & > .btn {
            background: none;
            border-radius: 0;
            height: auto;
            line-height: 1.42857143;
            border: 1px solid #A5C1D1;
            color: #fff;
            padding: 5px 13px;
            .transition(all 0.2s);
            &:hover {
                background-color: rgba(255, 255, 255, 0.1);
                text-decoration: none;
                border-color: #fff;
            }
            &:focus {
                text-decoration: none;
                outline: 0 none;
            }
        }
        //        // button with fa icon
        //        & > .btn {
        //            background: none;
        //            border: none;
        //            padding: 0;
        //            color: @color-admin-header;
        //            .fa {
        //                font-size: 17px;
        //           }
        //        }
        
        a {
        //    border: 1px solid #A5C1D1;
            color: #fff;
        //    padding: 5px 13px;
        //    .transition(all 0.2s);
        //    margin-left: 9px;
            &:hover {
        //        background-color: rgba(255, 255, 255, 0.1);
                text-decoration: underline;
        //        border-color: #fff;
            }
            &:focus {
                text-decoration: underline;
                outline: 0 none;
            }
        }
        @media (min-width: @screen-md-min) { //@screen-xs-min, @screen-sm-min, @screen-lg-min
            & > .btn {
                float: right;
                margin-right: 0;
                margin-left: 9px;
            }
        }
        @media print {
            & > .btn {
                display: none;
            }
        }
        .-run-sfo-title {
            font-size: 21px;
        }
        .-run-sfo-actions {
            .-run-action {
                margin-right: 20px;
                text-decoration: none;
                .fa {
                    margin-right: 4px;
                }
            }
        }
        .app-message {
            height: auto;
            min-height: 34px;
            line-height: 1.6em;
            padding-top: 5px;
            padding-bottom: 5px;
            &.alert-dismissible {
                padding-right: 18px;
                .close {
                    top: 0px;
                    right: -8px;
                }
            }
        }
        .btn-group.-run-action-lang {
            margin-top: -2px;
            margin-bottom: -1px;
            .btn {
                padding: 0;
                border: none !important;
                background: none !important;
                box-shadow: none !important;
                .caret {
                    margin-left: 2px;
                    border-top-color: @color-admin-header;
                }
                &:hover {
                    .caret {
                        border-top-color: @color-admin-header-focus;
                    }
                }
            }
            .dropdown-menu {
                min-width: 0;
                left: -10px;
                & > li > a {
                    padding: 3px 20px 3px 9px;
                }
            }
            img {
                //height: 43px
                height: 32px;
                width: 32px;
                max-width: none;
            }
        }
    }
    .-run-sfo-fields {
        background-color: #f1f1f1; /*#e3eaf2;*/
        position: relative; //.-run-sfo-affix
        padding: 15px 18px 0 25px;
        label {
            text-align: left;
        }
        .radio, .checkbox {
            margin-top: -2px;
            margin-bottom: 18px;
        }    
        .radio-inline, .checkbox-inline {
            margin-right: 10px;
            margin-left: 0;
            label {
                padding-left: 0;
            }
        }    
        .required-mark {
            color: @color-alert;
        }
        .-run-action {
            margin-left: 4px;
        }
        .input-translation-mark-wrapper {
            position: relative;
            .translation-mark {
                position: absolute;
                top: -26px;
                right: 1px;
                img {
                    height: 22px;
                    border: 1px solid #DDD;
                    border-radius: 6px;
                }
            }
            .text-input.translated {
                //padding-right: 34px;
            }
        }
        .-run-sfo-affix {
            position: fixed;
            margin-right: 18px;
            .nav {
                & > li > a {
                    display: block;
                    padding: 4px 20px;
                    font-size: 14px;
                    font-weight: 500;
                    color: #767676;
                }   
                & > .active > a {
                    padding-left: 18px;
                    font-weight: 700;
                    color: @color-highlighted;
                    background-color: transparent;
                    border-left: 2px solid @color-highlighted;
                }        
                & > li > a:hover {
                    padding-left: 19px;
                    color: @color-highlighted;
                    text-decoration: none;
                    background-color: transparent;
                    border-left: 1px solid @color-highlighted;
                }
                .nav > li > a {
                    padding-top: 1px;
                    padding-bottom: 1px;
                    padding-left: 30px;
                    font-size: 13px;
                    font-weight: 400;
                }    
                .nav>.active>a {
                    padding-left: 28px;
                    font-weight: 500;
                }
                .nav>li>a:hover {
                    padding-left: 29px;
                }
            }
        }
        // treeselect & itemselect
        .treeselector, .itemselector {
            fieldset {
                legend {
                    font-weight: 400;
                    color: #555;
                }
                .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
                    border: 1px solid #475360; //#82D9EA; //#458fd2; //#35BAF6;
                    background: #475360; //#82D9EA; //#458fd2; //#35BAF6;
                    font-weight: normal;
                    color: #fff;
                }
            }
        }
        .treeselector {
            .sortable li  {   
                position: relative; // img, :after
                img {
                    opacity: 0;
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    z-index: 1;
                    height: 100%;
                    width: 31px;
                }
                &:after {
                    .font-awesome-icon('\f114');
                    float: right;
                    font-size: 18px;
                    margin: 1px 5px 0 0;
                }
            }
        }
    }
    .-run-sfo-heading {
        margin-bottom: 20px;
        padding: 2px 0 5px 5px;
        font-size: 21px;
        color: @color-admin-heading;
//        text-transform: lowercase;
//        background: @color-admin-heading-bg;
        .-run-hint {
            font-size: 0.5em;
            top: -0.8em;
        }
    }
    h1.-run-sfo-heading {
        border-bottom: 1px solid #BBB;
    }
    .-run-sfo-heading:first-child, 
    input[type="hidden"] + .-run-sfo-heading {
        margin-top: 0;
    }
    h2.-run-sfo-heading {
        font-size: 17px;
        //color: @color-admin-heading-bg;
        //border: 1px solid @color-admin-heading-bg;
        //background: @color-admin-bg;
        //color: darken(@color-admin-heading-bg, 20%);
        //background: lighten(@color-admin-heading-bg, 5%);
    }
    div.display-image-input {
        //padding-top: 25px;
    }
    // .edit-rights 
    &.edit-rights {
        .form-group {
            margin-top: -15px;
            .checkbox {
                float: left;
            }
            > label {
                font-weight: normal;
                margin-top: 10px;
                &.default-rights {
                    font-weight: bold;
                }
                &.changed-rights {
                    color: @color-alert;
                }
            }
        }
    }
}

// PROTHEME
@import (less) "_smartFormProtheme.less";
