// Bootstrap core variables and mixins
@import "../../vendors/less/bootstrap/variables.less";
@import "../../vendors/less/bootstrap/mixins.less";
// local variables
@import "_variables.less";

@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    src: local('Open Sans'), local('OpenSans'), url(../../fonts/OpenSans.woff) format('woff');
}
@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 700;
    src: local('Open Sans Bold'), local('OpenSans-Bold'), url(../../fonts/OpenSans-Bold.woff) format('woff');
}

.-run-smart-admin {
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
    //background: url('/app/modules/Admin/img/icons/logo.png') center top no-repeat;
    background-color: @color-admin-bg;
    .btn {
        .fa {
            margin-right: 5px;
        }
    }
    .dropdown-menu {
        .fa {
            margin-right: 5px;
        }
    }
    .-run-sad-menu-bar {
        .clearfix;
        padding: 0px 23px 6px 23px;
        .-run-sad-menu {
            float: left;
            > .btn {
                margin-right: 2px;
            }
            // toto som tu dall narychlo len aby som poslal screens grafikovi
            .btn > img {
                height: 28px; //debug
                margin: -5px 5px -5px -5px; //debug
                position: relative; //debug
                top: -1px; //debug
            }
        }
        .-run-sad-menu-secondary-menu, .-run-sad-menu-user-menu {
            float: right;
        }
        .btn-default {
            background: none;
            border-color: transparent;
            padding: 3px 12px;
            text-shadow: none;
            .box-shadow(none);
            font-weight: bold;
            color: #FFF;
            border-radius: 0 0 6px 6px;
            //#gradient > .vertical(@start-color: transparent; @end-color: darken(@color-admin-bg, 8%));
            //.reset-filter(); // Disable gradients for IE9 because filter bleeds through rounded corners
            background: url('/app/modules/Admin/img/module-btn-bg.png') top;
            background-repeat: repeat-x;
            &.open {
                .box-shadow(none);
            }
            &:hover {
                color: @btn-default-color;
                background-color: @btn-default-bg;
                border-color: @btn-default-border;
                border-top-color: transparent;
                background-image: none;
            }
            .caret {
                margin-left: 2px;
            }
        }
        .btn-group {
            margin-right: 2px;
            &.open .btn-default.dropdown-toggle {
                color: @btn-default-color;
                background-color: @btn-default-bg;
                border-color: @btn-default-border;
                border-top-color: transparent;
                background-image: none;
            }
            >.btn-default.dropdown-toggle:not(:first-child) {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }            
        }
        
    }
    .-run-sad-tabs {
        .nav-tabs {
            //border-bottom: none;
            padding: 0 23px;
            > li {
                > a {
                    color: #FFF; //lighten(@btn-default-color, 50%);
                    padding: 3px 12px;
                    border-radius: 6px 6px 0 0;
                    //background-color: darken(@color-admin-bg, 10%);
                    #gradient > .vertical(@start-color: darken(@color-admin-bg, 8%); @end-color: transparent);
                    .reset-filter(); // Disable gradients for IE9 because filter bleeds through rounded corners
                    background-repeat: repeat-x;                    
                    &:active, &:hover, &:focus {
                        color: @btn-default-color;
                        border-bottom-color: @nav-tabs-border-color;
                        background-image: none;
                        background-color: @nav-link-hover-bg;
                        .icon {
                            .opacity(1); 
                        }
                        .title {
                            .opacity(1); 
                        }
                        .close {
                            color: @btn-default-color;
                        }
                    }
                    .icon {
                        .opacity(0.5);
                        & > * {
                            margin-right: 5px;
                        }
                    }
                    .title {
                        .opacity(0.5);
                    }
                    .close {
                        outline: 0;
                        color: lighten(@btn-default-color, 50%);
                        margin-left: 12px;
                        margin-right: -3px;
                        text-shadow: none;
                        font-weight: normal;
                        .opacity(0.5);
                        &:hover { //, &:focus - do not use focus
                            .opacity(1);
                            color: darken(@color-alert, 10%);
                        }
                    }
                }
                &.active {
                    > a {
                        &, &:active, &:hover, &:focus {
                            color: @btn-default-color;
                            border-bottom-color: transparent;
                            background-image: none;
                            background-color: @nav-tabs-active-link-hover-bg;
                        }
                        .icon {
                            .opacity(1);
                        }
                        .title {
                            .opacity(1);
                        }
                        .close {
                            color: @btn-default-color;
                            &:hover { //, &:focus - do not use focus
                                .opacity(1);
                                color: darken(@color-alert, 10%);
                            }
                        }
                    }
                }
            }
        } 
        .tab-content {
            //border-top: 1px solid @nav-tabs-border-color;
            border-left: 1px solid @nav-tabs-border-color;
            border-right: 1px solid @nav-tabs-border-color;
            background-color: @color-tab-content-bg; 
            //background-image: url('/app/img/libs/logo-run.png');
            //background-position: center;
            //background-repeat: no-repeat;
        }
    }
}