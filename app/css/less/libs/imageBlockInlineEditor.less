.ibie-reset,  .ibie-reset * {
    position: static;
    float: none;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0;
    width: auto;
    height: auto;
    border-collapse: collapse;
    font: normal normal normal 12px Arial,Helvetica,Tahoma,Verdana,Sans-Serif;
    color: #484848;
    text-align: left;
    white-space: nowrap;
    vertical-align: baseline;
    text-decoration: none;
    cursor: auto;
    transition: none;
    background: transparent;
}
.ibie-editor {
    img {
        position: absolute;
        display: block;
    }
    &.ibie-dragover {
        outline: 2px dashed #66afe9 !important;
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
    }
}
@-ibie-toolbar-z-index: 0;
.ibie-toolbar {
    position: absolute;
    z-index: @-ibie-toolbar-z-index;
    display: block;
    border: 1px solid #d1d1d1;
    background: #f8f8f8;
    padding: 6px 8px;
    white-space: normal;
    .ibie-button {
        display: inline-block;
        padding: 4px 6px;
        outline: 0;
        cursor: default;
        border: 1px transparent solid;
        position: relative;
        &:hover, &:focus, &:active {
            background: #e5e5e5;
            border: 1px #bcbcbc solid;
        }
        &.ibie-active-button {
            background: #fff;
            border: 1px #bcbcbc solid;
        }
        &.ibie-disabled-button {
            opacity: 0.3;
            &:hover, &:focus, &:active {
                border: 1px transparent solid;
                background: transparent;
            }        
        }
        &.ibie-upload-button,
        &.ibie-cover-button,
        &.ibie-fit-button, 
        &.ibie-delete-button {
            font-family: FontAwesome;
            font-size: 16px;
        }
        &.ibie-upload-button {
            &:before {
                content: '\f03e';
            }
        }
        &.ibie-cover-button {
            &:before {
                content: '\f065';
            }
        }
        &.ibie-fit-button {
            &:before {
                content: '\f066';
            }
        }
        &.ibie-delete-button {
            &:before {
                content: '\f00d';
                //content: '\00d7';
            }
        }
    }
}
.ibie-mask {
    position: absolute;
    z-index: @-ibie-toolbar-z-index + 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;    
    background: #fff;
    .ibie-mask-loader {
        span {
            display: inline-block;
            font: normal normal normal 14px/1 FontAwesome;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;    
            //animation: fa-spin 2s infinite linear;
            animation: fa-spin 1s infinite steps(8);
            &:before {
                //content: '\f021'; //'\f1ce'
                content: '\f110';
                font-size: 31px;
                color: rgba(45, 45, 45, 0.7);
            }
        }
    }
    .ibie-progressbar {
        margin-top: 16px;
        width: 80%;
        height: 5px;
        border-radius: 999px;
        background: rgba(45, 45, 45, 0.2);
        span {
            display: block;
            height: 5px;
            border-radius: 999px;
            background: rgba(45, 45, 45, 0.5);
        }
    }
}
.ibie-errors {
    position: absolute;
    z-index: @-ibie-toolbar-z-index + 2;
    overflow: auto;
    padding: 5px;
    background: #c30404;
    color: #fff;
    border-radius: 5px;
    .ibie-delete-errors {
        float: right;
        cursor: pointer;
        margin: -5px;
        padding: 5px;
        //font-family: FontAwesome;
        &:before {
            //content: '\f00d';
            content: '\00d7';
            font-size: 20px;
            font-weight: bold;
            color: #fff;
        }
    }
    .ibie-error {
        font-weight: bold;
        color: #fff;
        white-space: normal;
        word-break: break-word;
        & + .ibie-error {
            margin-top: 3px;
        }
    }
    .ibie-warning {
        
    }
}

