//
// ADMIN DARK THEME
//
.-run-smart-form.-run-protheme {
    .-run-sfo-header {
        padding: 11px 25px 4px 25px;
        color: #000; 
        background-color: #fff;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
        & > .btn {
            line-height: 29px;
            border: none; 
            color: #555; 
            &:hover {
                color: #333;
            }
            * {
                
            }
            &.btn-default {
                &:before {
                    .font-awesome-icon('\f0c7');
                    font-size: 19px;
                    margin-right: 7px;
                }
                &.-run-sfo-delete {
                    color: @color-alert;
                    &:hover {
                        color: darken(@color-alert, 5%);
                    }
                    &:before {
                        .font-awesome-icon('\f1f8');
                        font-size: 19px;
                    }
                }
                &.-run-sfo-close {
                    &:before {
                        .font-awesome-icon('\f057');
                        font-size: 19px;
                    }
                }
                &.-run-sfo-save-and-close {
                    &:before {
                        .font-awesome-icon('\f0c7');
                        font-size: 19px;
                    }
                }
            }
        }
        .btn {
            border: 1px solid transparent;
            &:active {                
                box-shadow: none;
                border-color: #35baf6;
            }
        }
        a {
            color: #555;
            &:hover {
                color: #333;
            }
        }
        .-run-sfo-title {
            color: #555;
        }
        .btn-group.-run-action-lang {
            .btn {
                .caret {
                    border-top-color: #555;
                }
                &:hover {
                    .caret {
                        border-top-color: #333;
                    }
                }
            }
        }
    }
    .-run-sfo-fields {
        background-color: #f5f5f5;
        position: relative; //.-run-sfo-affix
        padding: 15px 15px 0 15px;
        .form-group {
            input[type="file"] {
                min-height: 34px;
            }
        }
        .form-control {
            color: #333;
            background-color: #eee;
            box-shadow: none;
            border: 1px solid #ddd;
            &.has-error {
                border-color: #a94442;
            }
        }
        .form-control[disabled] {
            color: #808080;
            border: 1px solid #eee;
        }
        label {
            color: #555;
            font-weight: 400;
        }
        .-run-sfo-affix {
            position: fixed;
            margin-right: 18px;
            margin-top: 7px;
            .nav {
                & > .active > a {
                    color: @color-alert;
                    border-left: 2px solid @color-alert;
                }        
                & > li > a:hover {
                    color: @color-alert;
                    border-left: 1px solid @color-alert;
                }
            }
        }
        .row {
            margin-right: 0;
            margin-left: 0;
            background-color: white;
            .row {
                margin-right: -(@grid-gutter-width / 2);
                margin-left: -(@grid-gutter-width / 2); 
            }
            .-run-sfo-fields-main {
                padding: 0;
                .row {
                    margin-right: 0;
                    margin-left: 0;
                    background-color: white;
                    .row {
                        margin-right: -(@grid-gutter-width / 2);
                        margin-left: -(@grid-gutter-width / 2); 
                    }
                }
            }
            .-run-sfo-fields-side {
                padding-right: 0;
            }
        }  
        // top level row without heading
        input[type="hidden"] + .row,
        > .row:first-child {
            padding-top: 15px;
        }
        &.with-affix {
            > .row:first-child {
                background-color: transparent;
                padding-top: 0;
                // top level row without heading
                > .col > input[type="hidden"] + .row,
                > .col > .row:first-child {
                    padding-top: 15px;
                }
            }
        }
    }
    .-run-sfo-heading {
        margin-bottom: 0px;
        padding: 9px 0 9px 10px; 
        color: #fff; 
    }
    h1.-run-sfo-heading {
        border-bottom: 20px solid #fff;
        background: #35baf6;
        //background: #579DDB;
        color: #f0f0f0;
    }
    //h1.-run-sfo-heading:nth-first-child(2) {
    //    background: #c8aac5;
    //}
    h2.-run-sfo-heading {
        border-top: 10px solid #fff;
        border-bottom: 10px solid #fff;
        font-size: 21px;
        background: #fff;
        margin-top: 0px;
        padding: 10px 0 10px 15px;
        color: #458fd2;
        font-weight: bold;
    }
    h3.-run-sfo-heading {
        border-top: 5px solid #fff;
        border-bottom: 5px solid #fff;
        font-size: 17px;
        background: #fff;
        margin-top: 0px;
        padding: 5px 0 5px 15px;
        color: #458fd2;
        font-weight: bold;
    }
}