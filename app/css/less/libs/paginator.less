// Bootstrap core variables and mixins
//@import "../../vendors/less/bootstrap/variables.less";
//@import "../../vendors/less/bootstrap/mixins.less";
// local variables
//@import "_variables.less";
.-run-pg-sort-switch {
    cursor: pointer;
    .-run-pg-sort-switch-icon {
        display: block;
        float: left;
        position: relative; //span
        height: 20px;
        margin-right: 5px;
        img {
            height: 77%;
        }
        span {
            position: absolute;
            top: 1px;
            right: 100%;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
            color: #FF6633;

        }
    }
}
