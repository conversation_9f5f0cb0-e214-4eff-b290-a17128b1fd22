// Bootstrap core variables and mixins
@import "../../vendors/less/bootstrap/variables.less";
@import "../../vendors/less/bootstrap/mixins.less";
// local variables
@import "_variables.less";
@import "../_mixins.less";

.content-blocks-input {
    position: relative; // .-run-mask
    border: 1px solid @btn-default-border;
    border-radius: 4px;
    padding: 6px 12px;
    .-run-mask {
        position: absolute;
        border-radius: 4px;
    }
    .-run-loader { 
        position: absolute;
    }
    .content-blocks-input-controls {
        position: sticky;
        top: -15px; // this is negative value of .-run-smart-form .-run-sfo-fields top padding
        z-index: 999; // keep it high because of conflict width CKE codemirror source 
        padding-top: 6px;
        padding-bottom: 15px;
        background: #fff;        
        .clear;
        .content-blocks-input-add-action {
            float: left;
            .add-dropdown {
                > ul {
                    overflow: auto;
                    min-width: 300px;
                    max-height: 75vh;
                    > li {
                        > .select-option {
                            padding: 5px 15px;
                            cursor: pointer;
                            .clear;
                            &:hover {
                                background: #f5f5f5;
                            }
                            > .image-spacer {   
                                float: left;
                                width: 100px;
                                margin-top: 5px;
                                > img {
                                    display: block;
                                    width: 100%;
                                }
                                //&:hover {
                                //    > img {
                                //        position: absolute;
                                //        z-index: 1;
                                //    }
                                //}
                            }
                            > .name {
                                white-space: nowrap;
                                font-weight: bold;
                            }
                            > .description {
                                margin-top: 5px;
                                font-size: 12px;
                            }
                            > .model {
                                font-size: 10px;
                                opacity: 0.1;
                                &:hover {
                                    opacity: 0.5;
                                }
                            }
                            > .image-spacer {
                                & + .name {
                                    margin-left: 110px;
                                    & + .description {
                                        margin-left: 110px;
                                        & + .model {
                                            margin-left: 110px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            > * {
                display: inline-block;
                vertical-align: middle;
            }
            .form-group {
                margin-right: 20px;
                margin-bottom: 0;
            }
        }
        .content-blocks-input-other-actions {
            float: right;
            > * {
                display: inline-block;
                vertical-align: middle;
                color: #555;
                &:hover {
                    color: #333;
                }
            }
            .toggle-preview {
                margin-right: 14px;
                margin-top: 6px;
                cursor: pointer;
                &:before {
                    .font-awesome-icon('\f03e');
                    font-size: 20px;
                }
            }
            .toggle-collapse {
                margin-right: 67px;
                margin-top: 7px;
                cursor: pointer;
                &:before {
                    .font-awesome-icon('\f150');
                    font-size: 20px;
                }
            }
        }
    }
    &.unsaved {
        .content-blocks-input-controls {
            .content-blocks-input-other-actions {
                .toggle-preview {
                    cursor: not-allowed;
                    &:before {
                        opacity: 0.3;
                    }
                }
            }
        }
    }
    &.previewed {
        .content-blocks-input-controls {
            .content-blocks-input-add-action {
                display: none;
            }
            .content-blocks-input-other-actions {
                .toggle-preview {
                    &:before {
                        .font-awesome-icon('\f040');
                        font-size: 20px;
                    }
                }
                .toggle-collapse {
                    visibility: hidden;
                }
            }
        }
    }
    .content-blocks-input-errors {
        display: none;
        color: @color-alert;
        font-weight: bold;
        margin-top: 6px;
    }
    .content-blocks-input-items {
        .content-blocks-input-item {
            background-color: transparent;
            .item-header {
                position: relative; // .move-handle
                padding: 7px 130px 7px 30px;
                margin-bottom: 9px;
                border-radius: 4px;
                font-size: 21px;
                line-height: 1.1;
                color: #fff;
                background-color: #999;
                .move-handle {
                    overflow: hidden;
                    position: absolute;
                    top: 0;
                    left: 0;
                    bottom: 0;
                    width: 30px;
                    padding: 7px 0 0 10px;
                    font-family: sans-serif;
                    font-size: 22px;
                    color: #eee;
                    line-height: 5px;
                    letter-spacing: -1px;
                    cursor: move;
                    display: flex;
                    align-items: center;
                    //text-shadow: 1px 0 1px black;
                    &:after {
                        content: '.. .. .. ..';
                        display: block;
                        height: 38px;
                    }
                    &:hover {
                        color: #fff;
                    }
                }
                .image {
                    display: inline-block;
                    img {
                        width: 150px;
                        margin-right: 9px;
                    }
                }
                input.name {
                    margin: 0;
                    border: none;
                    padding: 3px 0 0 0;
                    width: 100%;
                    line-height: 1.2;
                    background: transparent;
                    outline: none;
                }
                .image + input.name {
                    width: calc(100% - 159px); // 150px image width + 9px image right margin
                }
                .toggle-preview, .toggle-collapse, .toggle-active, .delete {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    cursor: pointer;
                    color: #eee;
                    &:hover {
                        color: #fff;
                    }
                }
                .toggle-preview {
                    display: none;
                    right: 97px;
                    &:before {
                        .font-awesome-icon('\f03e');
                        font-size: 20px;
                    }
                }
                .toggle-collapse {
                    right: 68px;
                    width: 15px;
                    text-align: center;
                    &:before {
                        .font-awesome-icon('\f0d7');
                        font-size: 20px;
                    }
                }
                .toggle-active { 
                    right: 39px;
                    span {
                        color: @color-alert;
                        &:before {
                            .font-awesome-icon('\f070');
                            font-size: 20px;
                            //opacity: 0.3;
                        }
                    }
                    input {
                        display: none;
                        &:checked + span {
                            color: #eee;
                            &:hover {
                                color: #fff;
                            }
                            &:before {
                                .font-awesome-icon('\f06e');
                                font-size: 20px;
                                //opacity: 1;
                            }
                        }
                    }
                }
                .delete {
                    right: 10px;
                    font-size: 26px;
                    font-weight: 700;
                    &:hover {
                        color: @color-alert;
                    }
                }
            } 
            .item-content {
                position: relative; //.item-info
                margin-top: -9px;
                padding-top: 9px;
                padding-bottom: 15px;
                background-color: #fff;
                .item-form {
                    .collapsible-section-heading {  
                        &:after {
                            .font-awesome-icon('\f0d7');
                            position: absolute;
                            width: 25px;
                            font-size: 20px;
                            text-align: center;
                        }
                        &.collapsed {
                            &:after {
                                .font-awesome-icon('\f0d9');
                            }
                        }
                    }
                    .collapsible-section {
                        &.collapsed {
                            display: none;
                        }
                    }
                }
                .item-info {
                    position: absolute;
                    bottom: 9px;
                    right: 2px;
                    font-size: 10px;
                    opacity: 0.1;
                    &:hover {
                        opacity: 0.5;
                    }
                    .original-name,
                    .model-name {
                        display: inline-block;
                    }
                }
            }
            h1.-run-sfo-heading {
                background-color: #999;
            }
            h2.-run-sfo-heading {
                color: #999;
            }
            hr {
                border-color: #eee;
            }
            &.previewable {                
                .item-header {
                    .toggle-preview {
                        display: block;
                    }
                }
            }
            &.unsaved {
                .item-header {
                    .toggle-preview {
                        cursor: not-allowed;
                        &:before {
                            opacity: 0.3;
                        }
                    }
                }
            }
            &.previewed {
                .item-header {
                    .toggle-preview {
                        &:before {
                            .font-awesome-icon('\f040');
                            font-size: 20px;
                        }
                    }
                }
            }
            &.collapsed {
                .item-header {
                    .toggle-collapse {
                        &:before {
                            .font-awesome-icon('\f0d9');
                            font-size: 20px;
                        }
                    }
                }
            }
        }
    }
}

