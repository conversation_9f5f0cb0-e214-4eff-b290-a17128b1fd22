// Bootstrap core variables and mixins
@import "../../vendors/less/bootstrap/variables.less";
@import "../../vendors/less/bootstrap/mixins.less";
// local variables
@import "_variables.less";
@import "../_mixins.less";

.gallery-input {
    position: relative; // .-run-mask
    border: 1px solid @btn-default-border;
    border-radius: 4px;
    padding: 6px 12px;
    .-run-mask {
        position: absolute;
        border-radius: 4px;
    }
    .-run-loader { 
        position: absolute;
    }
    .gallery-input-controls {
        display: none;
        margin-top: 6px;
        overflow: hidden;
        .form-group {
            float: left;
            margin-right: 20px;
        }
        .gallery-input-buttons {
            clear: left;
            text-align: left;
            a {
                margin-right: 5px;
            }
        }
    }
    .gallery-input-errors {
        display: none;
        color: @color-alert;
        font-weight: bold;
        margin-top: 6px;
    }
    .gallery-input-items {
        border-radius: 4px;
        padding: 3px;
        background-color: @gray-lighter;
        border: 2px dashed transparent;
        min-height: 21px;
        &.gallery-input-items-dragover {
            border-color: @gray-light;
        }
        .gallery-input-item {
            position: relative; // span
            overflow: hidden; // span
            display: inline-block;
            vertical-align: top;
            margin-right: 3px;
            margin-bottom: 3px;
            border: 1px solid @gray-light;
            .border-radius(3px);
            padding: 2px 2px 20px 2px;
            text-align: center;
            background-color: #FFF; 
            img {
                display: block;
                margin: 0 auto;
                
            }
            span {
                position: absolute;
                bottom: 0;
                left: 0;
                padding: 0 5px;
                width: 100%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                //background-color: rgba(255,255,255,0.5);
            }
            &.gallery-input-item-active {
                border-color: #66AFE9;
                background-color: #66AFE9; 
                outline: 0;
                -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
                box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);            }
        }
    }
}

