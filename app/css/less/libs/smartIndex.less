// Bootstrap core variables and mixins
@import "../../vendors/less/bootstrap/variables.less";
@import "../../vendors/less/bootstrap/mixins.less";
// local variables
@import "_variables.less";
@import "paginator.less";

//@table-bg-active: darken(@table-bg-hover, 10%);

@import "../../less/_fonts.less";

.-run-smart-index {
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
    a {
        text-decoration: none;
        &:hover {
            text-decoration: none;
        }
    }
    .-run-action {
        cursor: pointer;
    }
    .-run-six-header {
        padding: 8px 25px 0px 25px;
        color: @color-admin-header;
        background-color: @color-admin-header-bg;
        border-bottom: 1px solid @color-admin-header-border;
        // for all children elements set:
        & > * {
            float: left;
            height: 34px;
            line-height: 34px;
            margin-right: 20px;
            margin-bottom: 9px;
        }
        a {
            color: @color-admin-header-link;
            &:hover {
                color: @color-admin-header-link-focus;
            }
        }
        .fa {
            font-size: 20px;
        }
        input, button, select, textarea {
            color: @color-black;
        }
        .-run-action {
            cursor: pointer;
            margin-right: 20px;
            &.reset-action {
                text-decoration: line-through;
                color: @color-alert;
                &:hover {
                    color: lighten(@color-alert, 5%);    
                }
            }
            &.inactive {
                color: darken(@color-admin-header-bg, 5%);
                cursor: not-allowed;
                &:hover {
                    color: darken(@color-admin-header-bg, 5%);
                    cursor: not-allowed;
                }
            }
        }
        .-run-six-controls {
            margin-top: 2px;
            margin-bottom: 7px;
            .-run-six-paginator, .-run-six-actions, .-run-six-count-info {
                height: 34px;
                line-height: 34px;
                display: inline-block;
                padding: 0 3px 0 8px;
            }
            .-run-six-actions {
                .-run-action:last-child {
                    margin-right: 0px;
                }
            }
            .-run-six-actions.-run-six-actions-bulk {
                padding: 0;
            }
            .-run-six-count-info {
                margin-right: 7px;
            }
            .-run-six-paginator {
                //margin-right: 4px;
                .-run-pg-link, .-run-pg-actual-page {
                    margin-right: 5px;
                }
                .-run-pg-actual-page {
                    color: @color-admin-header-focus;
                    font-weight: bold;
                }
            }
        }
        @media (min-width: @screen-md-min) { //@screen-xs-min, @screen-sm-min, @screen-lg-min
            & > .-run-six-controls {
                float: right;
                margin-right: 0;
            }
        }
        .-run-six-title {
            font-size: 21px;
        }
        .app-message {
            padding-top: 0;
            padding-bottom: 0;
            &.alert-dismissible {
                padding-right: 18px;
                .close {
                    top: 6px;
                    right: -8px;
                }
            }
        }
        .btn-group.-run-action-lang {
            //margin-top: -8px;
            //margin-bottom: -7px;
            margin-top: -2px;
            margin-bottom: -1px;
            .btn {
                padding: 0;
                border: none !important;
                background: none !important;
                box-shadow: none !important;
                .caret {
                    margin-left: 2px;
                    border-top-color: @color-admin-header;
                }
                &:hover {
                    .caret {
                        border-top-color: @color-admin-header-focus;
                    }
                }
            }
            .dropdown-menu {
                min-width: 0;
                left: -10px;
                & > li > a {
                    padding: 3px 20px 3px 9px;
                    margin: 0;
                }
            }
            img {
                //height: 43px
                height: 32px;
                width: 32px;
                max-width: none;
            }
        }
    }
    .-run-six-index {
        .-run-six-cell-content {
            display: inline-block;
            zoom: 1;
            margin: 2px 8px 2px 8px; //8px;
            vertical-align: top;
        }
        tr.-run-six-has-record-actions {
            & > td {
                position: relative; // .-run-six-record-actions
                .-run-six-cell-content {
                    margin-bottom: 14px; //22px; //24px;
                    .-run-six-record-actions {
                        position: absolute;
                        bottom: 1px; //5px; 
                        z-index: 1;
                        display: none;
                        margin-top: -1px;
                        //margin-left: -5px;
                        margin-right: 1em;
                        border-radius: 5px;                        
                        padding: 0 5px;
                        white-space: nowrap;
                        font-size: 12/14em;
                        line-height: 1.3em;
                        font-weight: 400;
                        background: rgba(255,255,255,0.9);
                        .-run-action {
                            margin-right: 3px;
                            .fa {
                                margin-right: 4px;
                            }
                        }
                        .-run-action-delete {
                            color: @color-alert;
                        }
                        // separator
                        span {
                            color: @color-inactive;
                            margin-right: 3px;
                        }
                    }
                }
            }
        }
        tr:hover {
            //background-color: lighten(#FFF18F, 5%);
            //background-color: lighten(#B7E0A4, 0%);
            td {
                .-run-six-cell-content {
                    .-run-six-record-actions {
                        display: block;
                    }
                }
            }
        }
        tr.selected {
            background-color: @table-bg-active;
        }
        > .-run-six-index-head-spacer {
             background-color: rgb(241, 241, 241);
        }
        > table, 
        > .-run-six-index-head-spacer > table,
        > .-run-six-index-scroll > table {
            > thead {
                background-color: rgb(241, 241, 241);
                //background-color: #E0E0E0;
                .-run-six-cell-content {
                    position: relative;
                    margin-top: 2px;
                    margin-bottom: 2px;
                    .-run-hint {
                        position: absolute;
                        top: 0px;
                        right: 0px;
                    }
                }
                tr:first-child .-run-six-cell-content {
                    margin-top: 5px;
                }
                tr:last-child .-run-six-cell-content {
                    margin-bottom: 5px;
                }
                th {
                    border-width: 0px;
                    padding: 0;
                    vertical-align: top;
                }
                tr:last-child {
                    th {
                        border-bottom-width: 2px;
                    }
                }
                .text-input {
                    height: 24px;
                    padding: 2px 14px 2px 6px;
                    //font-weight: normal;
                    background: #fff;
                    width: 100%;
                }
                select.text-input {
                    padding-right: 16px;
                }
                .inactive {
                    color: @color-inactive;
                }
            }
            tr {
                td:first-child .-run-six-cell-content, 
                th:first-child .-run-six-cell-content {
                    margin-left: 25px;
                }
                td:last-child .-run-six-cell-content, 
                th:last-child .-run-six-cell-content {
                    margin-right: 18px;
                }
                &.-run-six-highlighted {
                    font-weight: bold;
                }
                &.-run-six-inactive {
                    color: lighten(#000, 46.7%);
                }
                &.-run-six-hidden {
                    background: lighten(#000, 80%);
                }
                &.default {
                    font-weight: bold;
                }
            }
            > tbody {
                > tr {
                    > td {
                        padding: 0;
                        border-top: 1px solid #EFEFEF; //#FAFAFA
                    }
                }
                .ui-sortable-helper {
                    background-color: @table-bg-hover;
                    border-bottom: 1px solid @table-border-color;
                }
            }
        }
        .-run-six-empty-index-message {
            text-align: center;
        }
    }

    //
    // TREE STYLES
    //
    
    // force no text wrap in tree
    .-run-six-tree {
        table {
            tbody {
                tr {
                    td {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        &:first-child {
                            overflow: visible;
                            .-run-six-cell-content {
                                margin-left: 1px;
                            }
                        }
                    }
                }
            }
        }
    }
    .-run-six-tree-nav {
        display: inline-block;
        zoom: 1;
        // it is not necessary to have 100% here, 1px and overflow:visble is enough 
        // (and even we do not need to set heights in js)
        height: 1px; 
        vertical-align: top;
    }
    .-run-six-tree-elbow-expander,
    .-run-six-tree-elbow-end-expander,
    .-run-six-tree-elbow,
    .-run-six-tree-elbow-line,
    .-run-six-tree-elbow-end,
    .-run-six-tree-elbow-empty,
    .-run-six-tree-node {
        display: block;
        float: left;
        width: 20px; //18px
        height: 100%;
        background-repeat: no-repeat;
        background-position: 0 center;
    }
    .-run-six-tree-elbow-expander,
    .-run-six-tree-elbow-end-expander,
    .-run-six-tree-node {
        &:before {            
            display: inline-block;
            font: normal normal normal 14px/1 FontAwesome;
            font-size: 16px;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            transform: translate(0, 0);
            padding-top: 10px;
            width: 15px;
            text-align: right;
            cursor: pointer;
        }
    }
    .-run-six-tree-elbow-expander {
        //background-image: url('@{img-dir}/tree/elbow-plus.png');
        &:before {
            content: "\f0da";
        }
    }
    .-run-six-tree-elbow-end-expander {
        //background-image: url('@{img-dir}/tree/elbow-end-plus.png');
        &:before {
            content: "\f0da";
        }
    }
    .-run-six-tree-node-expanded .-run-six-tree-elbow-expander {
        //background-image: url('@{img-dir}/tree/elbow-minus.png');
        &:before {
            content: "\f0d7";
        }
    }
    .-run-six-tree-node-expanded .-run-six-tree-elbow-end-expander {
        //background-image: url('@{img-dir}/tree/elbow-end-minus.png');
        &:before {
            content: "\f0d7";
        }
    }
    .-run-six-tree-elbow {
        //background-image: url('@{img-dir}/tree/elbow.png');
    }
    .-run-six-tree-elbow-line {
        //background-image: url('@{img-dir}/tree/elbow-line.png');
    }
    .-run-six-tree-elbow-end {
        //background-image: url('@{img-dir}/tree/elbow-end.png');
    }
    .-run-six-tree-node-branch {
        //background-image: url('@{img-dir}/tree/folder.png');
        &:before {
            content: "\f07b";
            content: "\f114";
            text-align: left;
            font-size: 15px;
        }
    }
    .-run-six-tree-node-expanded .-run-six-tree-node-branch {
        //background-image: url('@{img-dir}/tree/folder-opened.png');
        &:before {
            content: "\f07c";
            content: "\f115";
            text-align: left;
            font-size: 15px;
        }
    }
    .-run-six-tree-node-leaf {
        //background-image: url('@{img-dir}/tree/leaf.png');
        &:before {
            content: "\f15b"; 
            content: "\f016"; 
            text-align: left;
            font-size: 14px;
        }
    }
    .-run-six-tree-node {
        margin-left: 2px;
    }
}

// PROTHEME
@import (less) "_smartIndexProtheme.less";