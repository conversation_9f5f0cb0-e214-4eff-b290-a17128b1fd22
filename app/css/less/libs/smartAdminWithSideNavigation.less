// Bootstrap core variables and mixins
@import "../../vendors/less/bootstrap/variables.less";
@import "../../vendors/less/bootstrap/mixins.less";
// local variables
@import "_variables.less";

@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    src: local('Open Sans'), local('OpenSans'), url(../../fonts/OpenSans.woff) format('woff');
}
@font-face {
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 700;
    src: local('Open Sans Bold'), local('OpenSans-Bold'), url(../../fonts/OpenSans-Bold.woff) format('woff');
}

.-run-smart-admin {
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
    //background: url('/app/modules/Admin/img/icons/logo.png') center top no-repeat;
    background-color: @color-admin-bg;
    .fa {
        margin-right: 5px;
    }
    .-run-sad-tabs {
        @initial-side-nav-width: 200px;
        .-run-sad-side-nav {
            position: relative; //-run-sad-side-nav-resize-bar
            float: left;
            width: @initial-side-nav-width;
            padding: 0;
            color: @text-color;
            overflow-x: hidden;
            a {
                color: @text-color;
                cursor: pointer;
            }
            [data-run-st-activator] {
                cursor: pointer;
            }
            @resize-bar-width: 6px;
            .-run-sad-side-nav-resize-bar {
                position: absolute;
                right: 0;
                top: 0;
                z-index: 1;
                //float: right;
                height:100%;
                width: @resize-bar-width;
                cursor: col-resize;
                background-color: rgb(241, 241, 241);
                border-left: 1px solid #DDD;
                .-run-sad-side-nav-resize-bar-handle {
                    position: absolute;
                    bottom: 48%;
                    left: 0;
                    height: 20px;
                    width: 100%;
                    background: transparent url('@{img-dir}/libs/handle.png') repeat left 1px;
                }
            }
            .-run-sad-side-nav-scroll-window {
                overflow-x: hidden;
                overflow-y: auto;
                margin-right: @resize-bar-width;
            }
            .-run-sad-side-nav-header {
                padding: 8px 0px 9px 18px;
                font-size: 21px;
                line-height: 34px;
                color: @color-admin-header;
                background-color: @color-admin-header-bg;
                border-bottom: 1px solid @color-admin-header-border;
                white-space: nowrap;
                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
                .-run-sad-side-nav-close-button {
                    cursor: pointer;
                }
            }
            .-run-sad-menu {
                padding: 10px 0 0 0;
                list-style: none;
                margin-bottom: 0px;
                // nested levels
                .-run-sad-menu {
                    display: none;
                    padding: 0 0 0 15px;
                }
                &.-run-sad-menu-user-menu {
                    padding: 0;
                    border-bottom: 2px solid #DDD;
                    .-run-sad-menu-item {
                        padding: 0;
                        .-run-sad-menu-item-expander {
                            display: none;
                        }
                        a {
                            display: block;
                            padding: 5px 0px 5px 18px;
                            background-color: rgb(241, 241, 241);
                            &:focus, &:hover {
                                text-decoration: none;
                            }
                        }
                    }
                }
                .-run-sad-menu-item {
                    position: relative; // button.close
                    white-space: nowrap;
                    padding: 4px 0;
                    button.close {
                        display: none;
                        position: absolute;
                        left: 2px;
                        top: 4px;
                        padding-right: 3px;
                        color: @color-text;
                        &:hover {
                            .opacity(1);
                        }
                    }
                    &:hover {
                        button.close {
                            display: block;
                        }
                    }
                }
                .-run-sad-menu-item-expander {
                    display: inline-block;
                    font: normal normal normal 14px/1 FontAwesome;
                    font-size: inherit;
                    text-rendering: auto;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                    transform: translate(0, 0);
                    margin-right: 2px;
                    width: 14px;
                    text-align: right;
                    cursor: pointer;
                }
                .-run-sad-menu-item-title {  
                    white-space: nowrap;
                    padding: 0 3px;
                }
                a.-run-sad-menu-item-title, 
                a.-run-st-tab span.-run-sad-menu-item-title {
                    border-radius: 3px;
                    text-decoration: none;
                    &:hover {
                        background-color: @color-admin-tab-focus;
                        text-decoration: none;
                    }
                }
                a.-run-st-tab span.-run-sad-menu-item-title {
                    .fa {
                        position: relative;
                        font-size: 63%;
                        top: -1px;
                        margin-right: 8px;
                        &:before {
                            content: "\f096" !important;
                        }
                    }
                }
                a.-run-st-tab {
                    text-decoration: none;
                    &:hover {
                        text-decoration: none;
                        span.-run-sad-menu-item-title {
                            border-radius: 3px;
                            background-color: @color-admin-tab-focus;
                            text-decoration: none;
                        }
                    }
                }
                .active {
                    & > .-run-sad-menu-item .-run-sad-menu-item-title {
                        color: @color-admin-tab-active;
                        text-decoration: none;
                        &:hover {
                            text-decoration: none;
                        }
                    }
                }
                .has-subitems {
                    & > .-run-sad-menu-item > .-run-sad-menu-item-expander:before {
                          content: "\f0da";
                    }
                    &.expanded {
                        background-color: #2c3845;
                        & > .-run-sad-menu {
                            display: block;
                        }
                        &  > .-run-sad-menu-item > .-run-sad-menu-item-expander:before {
                              content: "\f0d7";
                        }
                    }
                }
            }
        }
        .tab-content {
            position: relative; //.-run-sad-side-nav-close-button
            margin-left: @initial-side-nav-width;
            border-left: 1px solid @nav-tabs-border-color;
            border-right: 1px solid @nav-tabs-border-color;
            background-color: @color-tab-content-bg; 
            .-run-sad-side-nav-close-button {
                display: none;
                position: absolute;
                //top: 4px;
                //left: 5px;
                top: 23px;
                left: 0px;                
                z-index: 1;
                font-size: 19px;
                color: @color-admin-header-bg;
                cursor: pointer;
            }
            .tab-pane {
                // remove bootstrap tab styles
                // These styles causes in Chrome that size of html document in 
                // contained iframe is autosized (results in 0x0) and so the scrollTop
                // restore fails.
                display: block;
                visibility: visible;
                // add new tab styles to remove the above described problem
                position: absolute;
                top: 0;
                right: 0;
                left: 0;
                z-index: -1;
                background-color: @color-tab-content-bg;
                &.active {
                    z-index: 0;
                }
            }
        }
        .-run-sad-side-nav.closed + .tab-content {
            .-run-sad-side-nav-close-button {
                display: block;
            }
        }
    }
}

// PROTHEME
@import (less) "_smartAdminWithSideNavigationProtheme.less";