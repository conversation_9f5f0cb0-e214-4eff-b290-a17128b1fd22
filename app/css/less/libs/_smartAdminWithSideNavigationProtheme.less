//
// ADMIN DARK THEME
//
.-run-smart-admin.-run-protheme {
    .fa {
        font-size: 19px;
        margin-right: 11px;
    }
    .-run-sad-tabs {
        .-run-sad-side-nav {
            .-run-sad-side-nav-scroll-window {
                background-color: #32404e;
            }
            .-run-sad-side-nav-header {
                color: #fff; //@color-admin-header-protheme;
                background-color: #35baf6; //@color-admin-header-bg-protheme;
                border-bottom: none;
                padding: 0 0 0 30px;
                height: 64px;
                line-height: 64px;
                a {
                    color: white;
                    text-decoration: none;
                    transition: color 0.2s;
                    &:hover {
                        color: #2c3845;
                    }
                }
            }
            .-run-sad-menu {
                padding: 0;
                // nested levels
                .-run-sad-menu {
                    padding: 0;
                }
                &.-run-sad-menu-user-menu {
                    border-bottom: none;
                    background-color: #262D34; //#32404e; //#262D34;
                    .-run-sad-menu-item {
                        a {
                            padding: 0 0 0 30px;
                            background-color: inherit;
                        }
                    }
                }
                .-run-sad-menu-item {
                    padding: 0;
                    height: 64px;
                    line-height: 64px;
                    button.close {
                        left: initial;
                        right: 3px;
                        top: 0;
                        padding: 0;
                        color: @color-alert;
                        opacity: 0.6;
                        &:hover {
                            opacity: 0.8;
                            text-shadow: 0 0px 5px #fff;
                        }
                    }
                    &:hover {
                        background-color: #3c4a57;
                        .-run-sad-menu-item-title {  
                            color: #d6d9dc;
                            &[href="/runlogout"] {
                                text-shadow: 0px 0px 6px @color-alert;
                            }
                        }
                    }
                }
                > li > .-run-sad-menu {
                    .-run-sad-menu-item-title {
                        padding-left: 50px;
                    }
                    > li > .-run-sad-menu {
                        .-run-sad-menu-item-title {
                            padding-left: 70px;
                        }
                    }
                }
                .-run-sad-menu-item-expander {
                    position: absolute;
                    top: 0;
                    left: 10px;
                    line-height: 64px;
                    height: 100%;
                    text-align: center;
                    color: #fff;
                    margin: 0;
                }
                .-run-sad-menu-item-title {  
                    display: block;
                    height: 64px;
                    padding: 0 0 0 30px;
                    color: #adb3b8;
                    transition: color 0.5s;
                }
                a.-run-sad-menu-item-title, 
                a.-run-st-tab span.-run-sad-menu-item-title {
                    outline: 0;
                    &:hover {
                        background-color: transparent;
                    }
                }
                a.-run-st-tab span.-run-sad-menu-item-title {
                    .icon {
                        display: none;
                    }
                }
                a.-run-st-tab {
                    &:hover {
                        span.-run-sad-menu-item-title {
                            background-color: transparent;
                        }
                    }
                }
                li {
                    &.active {
                        & > .-run-sad-menu-item {
                            background-color: #475360;
                            &:before {
                                content: " ";
                                position: absolute;
                                display: block;
                                left: 0;
                                top: 0;
                                width: 5px;
                                height: 100%;
                                background-color: #35baf6;
                            }
                            &:hover {
                                .-run-sad-menu-item-title {  
                                    color: #d6d9dc;
                                }
                            }
                            .-run-sad-menu-item-title {
                                color: #adb3b8;
                                text-decoration: none;
                                &:hover {
                                    background-color: transparent;
                                    text-decoration: none;
                                }
                            }
                        }
                    }
                }
            }
        }
        .tab-content {
            .-run-sad-side-nav-close-button {
                color: #DDD;
                &:hover {
                    color: #35baf6;
                }
            }
        }
    }
}