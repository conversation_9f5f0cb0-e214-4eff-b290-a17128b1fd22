//
// ADMIN DARK THEME
//
.-run-smart-index.-run-protheme {
    .-run-six-header {
        padding: 14px 25px 6px;
        color: #555; 
        background-color: #fff;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
        // for all children elements set:
        .-run-six-title {
            font-size: 21px;
            color: #555;
        }
        a {
            color: #555; 
            &:hover {
                color: #333; 
            }
        }
        .-run-action {
            &.inactive {
                color: darken(#FFF, 10%);
                &:hover {
                    color: darken(#FFF, 10%);
                }
            }
        }
        .-run-six-controls {
            .-run-six-paginator {
                .-run-pg-actual-page {
                    color: #000; //color: @color-admin-header-focus;
                }
            }
        }
        .btn-group.-run-action-lang {
            .btn {
                .caret {
                    border-top-color: #555;
                }
                &:hover {
                    .caret {
                        border-top-color: #333;
                    }
                }
            }
        }
    }
    .-run-six-index {
        background-color: rgb(241, 241, 241);
        a {
            color: #458fd2;
            &:hover {
                color: darken(#458fd2, 15%);
            }
        }
        tr.-run-six-has-record-actions {
            & > td {
                .-run-six-cell-content {
                    margin-top: 11px; //margin-bottom: 14px; //22px; //24px;
                    margin-left: 9px;
                }
            }
        }
        > .-run-six-index-head-spacer {
            background-color: #35baf6;
        }
        > table, 
        > .-run-six-index-head-spacer > table,
        > .-run-six-index-scroll > table {
            &.table {
                tbody {
                    background-color: #fff;
                }
            }
            > thead {
                background-color: #35baf6;
                color: #f0f0f0;
                .-run-six-cell-content {
                    .-run-hint {
                        .fa {
                            color: #adadad;
                        }
                    }
                    .text-input {
                        border-radius: 7px;
                        border: none;
                        //box-shadow: none;
                    }
                    .-run-pg-sort-switch {
                        color: white;
                    }
                }
                tr:last-child {
                    th {
                        border-bottom-width: 0;
                    }
                }
                //tr:first-child .-run-six-cell-content {
                //    margin-top: 12px;
                //}
                tr:last-child .-run-six-cell-content {
                    margin-bottom: 7px;
                }
                .inactive {
                    color: darken(#35baf6, 10%);
                    &:hover {
                        color: darken(#35baf6, 15%);
                    }
                }
            }
            tr {
                td:first-child .-run-six-cell-content, 
                th:first-child .-run-six-cell-content {
                    margin-left: 25px;
                }
                td:last-child .-run-six-cell-content, 
                th:last-child .-run-six-cell-content {
                    margin-right: 18px;
                }
            }
        }
    }

    //
    // TREE STYLES
    //
    
    // force no text wrap in tree
    .-run-six-tree {
        table {
            tbody {
                tr {
                    &:hover{
                        background-color: #EDF3F9;
                    }
                    td {
                        &:first-child {
                            .-run-six-cell-content {
                                margin-left: 9px;
                            }
                        }
                    }
                }
            }
        }
    }
    .-run-six-tree-elbow-expander {
        &:before {
            padding-top: 12px;
        }
    }
    .-run-six-tree-node-branch {
        &:before {
            font-size: 19px; 
        }
    }
    .-run-six-tree-node-expanded .-run-six-tree-node-branch {
        &:before {
            font-size: 19px;
        }
    }
    .-run-six-tree-node-leaf {
        &:before {
            font-size: 18px;
        }
    }
}