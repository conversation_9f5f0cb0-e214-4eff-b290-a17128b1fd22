// Bootstrap core variables and mixins
//@import "../vendors/less/bootstrap/variables.less";
//@import "../vendors/less/bootstrap/mixins.less";
// local variables
@import "_variables.less";
@import "_mixins.less";

// GRID
//
// grid compilation takes a few seconds so don't do it on each save but import .css
// file instead of .less file. If grid.css is changed then recompile also this file.
//@import (less) "grid.css";
@import "grid.less";

// TOGGLE BUTTON
.toggle-button {
    display: none;
    position: relative;
    cursor: pointer;
    padding: 9px 10px;
    //margin-top: 8px;
    margin-right: 15px;
    //margin-bottom: 8px;
    background-color: rgba(0, 0, 0, 0);
    background-image: none;
    border: 1px solid rgba(0, 0, 0, 0);
    //border-radius: 4px;   
    overflow: hidden;
    //.transition(all @transition-duration);
    &:hover, &.open {
        //background-color: #F9F9F9;
        //border-color: #F9F9F9;   
        .icon {
            .bar {
                background-color: @color-highlighted;
            }
        }
    }
    .icon {
        display: block;
        width: 22px;
        .bar {
            display: block;
            height: 3px;
            border-radius: 1px;
            background-color: @color-darkred;
            & + .bar {
                    margin-top: 3px;
            }
        }
    }
}

.products-index-slider-mixin() {
    overflow-y: visible;
    overflow-x: auto;
    margin-bottom: 30px;
    .index {
        white-space: nowrap;
        .product {
            white-space: normal;
            width: 168px;
            padding-bottom: 0px;
            > .spacer {
                width: @product-index-spacer-width;
            }
            &:nth-child(7n) {
                width: 168px;
                > .spacer {
                    width: @product-index-spacer-width;
                }
            }
        }
    }
}

//
// CUSTOM MEDIA QUERIES
//

// INITIAL QUERY AT CENTER-WIDTH
// @media screen and (max-width: 1140px)
@media screen and (max-width: (@center-width + @grid-cols-gap-width)) {
    body {
        min-width: 0;
    }
    .center {
        width: auto;
        max-width: none;
        .grid-full-width-container; 
        // nested .center blocks (e.g. in wrapping content blocks) 
        // should not add additional padding
        // ATTENTION: Important for nested content blocks implementation
        .center {
            padding-left: 0;
            padding-right: 0;      
        }
    }
    footer {        
        .footer-badges {
            .center {
                padding: 0 ((91px + @grid-cols-gap-width / 2) / (@center-width + @grid-cols-gap-width) * 100vw);
            }
        }
    }
    
    #header-top,
    #main-menu,
    .showcase-products-index,
    footer {
        width: auto;
    }
    #showcase {
        width: auto;
        & > img {
            width: auto;
        }
        .label {
            font-size: @font-size / @center-width * 100vw;
        }
    }
    #heurekaTabright {
        @-size-rate: 0.71;
        width: @-size-rate * 36px !important;
        height: @-size-rate * 188px !important;
        margin-bottom: 188px - (@-size-rate * 188px) !important;
        margin-top: 154px !important;
        background-size: (@-size-rate * 36px) (@-size-rate * 188px) !important;
        left: @-size-rate * (-36px) !important;
    }
    .categories-menu .level-02 {
        width: calc(100vw - @side-content-width - @grid-cols-gap-width);
    }
    #content {
        #content-main {
            width: calc(100% - @side-content-width - 15px);
        }
    }    
}
// @media screen and (max-width: 1140px) - same as above
@media screen and (max-width: (@bottom-content-center-width + @grid-cols-gap-width)) {
    
}
@media screen and (max-width: (@footer-center-width + @grid-cols-gap-width)) {
    .recommended-products-index {
        .products-index-slider-mixin();
    }
    
}
@media screen and (max-width: (@center-width - 2 * 91px)) {
    footer {        
        .footer-badges {
            .center {
                padding: 0 15px;
            }
        }
    }
}
@media screen and (max-width: 1150px) {
    .mini-search .keywords-wrapper {
        width: 300px;
    } 
    .top-categories-item {
        .child-items{
            & > li{
                width: 25%;
                &:nth-of-type(5n+1){
                    clear: none;
                }
                &:nth-of-type(4n+1){
                    clear: left;
                }
            }
        }
    }
}
@media screen and (max-width: 1100px) {
    .references-slider {
        .text {
            margin: 0 auto;
            padding-right: 0;
            padding-top: 60px;
        }
        .image {
            float: none;
            display: block;
            margin: 0 auto;
        }
    }
    .product-view {
        .special-offers {
            float: none;
            width: auto;
        }
    }
    body, #content-main {
        .wishlist-products-index,
        .products-index {
            .index {
                .product {
                    // 3 products per row
                    width: 363px / @main-content-width * 100%;
                    margin-left: 10px / @main-content-width * 100%;
                    margin-bottom: 32px / @main-content-width * 100%;
                    &:nth-of-type(n) {
                        margin-left: 10px / @main-content-width * 100%;
                    }
                    &:nth-of-type(3n + 1) {
                        margin-left: 0;
                    }
                }
            }
        }
    }
    .product-view{
        > .card{
            > .texts{
                > .info{
                    .controls{
                        .add-to-cart-button {
                            .to-cart-button{
                                .button{
                                    background: url('@{img-dir}/cart-icon-white.png') 18px center no-repeat @color-dark-green;
                                    background-size: 16px;
                                    width: 152px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
@media screen and (max-width: 1080px) {
    .mini-search .keywords-wrapper {
        width: 240px;
    }
}
@media screen and (max-width: 1023px) {
    .scroll-to-top {
        display: none !important;
    }
    .products-index.detailed-products-index {
        .index {
            justify-content: center;
            .product {
                flex-basis: 540px;
            }
        }
    }
}

@media screen and (max-width: 1000px) {
    .header-top {
        display: none;
    }
    .main-logo {
        margin: 2px auto 0 0;
        img.default {
            width: 200px;
        }
    }
    .mini-search {
        margin-top: 10px;
        .keywords-wrapper {
            width: 232px;
        }
        .products-index {
            left: 0;
            margin: 0;
            width: 100vw;
            .scroll-wrapper {
                padding-left: 50px / 1000px * 100vw;
                padding-right: 50px / 1000px * 100vw;
            }
        }
    }
    .categories-dropdown-and-breadcrumbs {
        .categories-dropdown-menu {
            display: none;
        }
        .-run-breadcrumbs {
            border-radius: 6px;
        }
    }
    .user-menu {
        margin: 10px 10px 0;
        .menu-button {
            width: 38px;
        }
        .menu-button-text {
            width: 25px;
        }
    }
    .bonus-info {
        margin: 10px 10px 0 0;
        .bonus-button-text .bonus-progress {
            margin-right: 5px;
        }
        .bonus-value {
            display: none;
        }
    }
    .mini-cart {
        margin-top: 8px;
        margin-right: 5px;
        .mini-cart-price {
            display: none;
        }
    }
    .hamburger {
        display: flex;
    }
    .header-middle {
        position: fixed;
        width: 100%;
        height: 68px;
        padding: 4px 15px 4px;
        z-index: 999;
        & > .center {
            padding: 0;
        }
    }
    .header-bottom {
        padding: 0;
        & > .center {
            &:first-of-type {
                height: 100%;
                padding: 0;
            }
        }
    }
    .main-navbar {
        display: none;
        position: static;
        width: 100%;
        height: 100%;
        margin: 0;
        border-radius: 0;
        background-color: @color-light-cream;
        &.active {
            display: block;
            position: fixed;
            top: 68px;
            width: 100vw;
            height: ~"calc(100vh - 60px)";
            z-index: 2;
        }
    }
    .all-categories {
        display: block;
        width: 100%;
    }
    .all-categories-button {
        width: 100%;
        padding: 0 16px;
        border-radius: 0;
        cursor: default;
        &:after {
            display: none;
        }
        &:hover,
        &:focus {
            background-color: @color-dark-green;
            transition: none;
        }
    }
    .categories-menu {
        display: block;
        position: static;
        height: ~"calc(100vh - 109px)";
        max-height: none;
        border-radius: 0;
        overflow-y: auto;
        &:before,
        &:after {
            display: none;
        }
        .level-01 {
            display: block;
            max-height: none;
            padding: 0;
            .expand-button {
                display: flex;
                position: absolute;
                width: 45px;
                height: 48px;
                &::after {
                    width: 9px;
                    height: 9px;
                    border-top: 3px solid #000;
                    border-left: 3px solid #000;
                    transform: rotate(225deg);
                }
                &:hover{
                    &::after {
                        border-top: 3px solid @color-dark-green;
                        border-left: 3px solid @color-dark-green;
                    }
                }
                &.active {
                    &:after {
                        transform: rotate3d(0, 0, 1, 45deg);
                    }
                }
            }
            & > li {
                display: block;
                padding: 0;
                &:hover{
                    .level-02{
                        display: none;
                    }
                }
                & > a {
                    color: @color-black;
                    line-height: 1.5;
                    height: auto;
                    position: relative;
                    padding: 14px 20px;
                    border-bottom: 1px solid @color-light-grey-5;
                    margin: 0;
                }
                &.has-subitems {
                    & > a {
                        padding: 14px 40px 14px 20px;
                    }
                }
                &.active, &.open {
                    background: none;
                }
            }
        }
        .level-02 {
            display: none;
            position: relative;
            left: 0;
            width: 100%;
            padding: 0 0 0 14px;
            border-left: none;
            a {
                display: block;
            }
            & > li {
                display: block;
                width: 100%;
                padding: 10px 20px 10px 45px;
                border-bottom: 1px solid @color-light-grey-5;
                &:nth-of-type(n){
                    &:after {
                        display: none;
                    }
                }
                & > a {
                    color: @color-black;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 1.5;
                    height: auto;
                    border: none;
                }
                .menu-img-wr {
                    top: -5px;
                }
            }
            .expand-button {
                top: -14px;
            }
            .heading01 {
                display: none;
            }
        }
        .level-03 {
            display: none;
        }
    }
    .products-index-menu-wrapper{
        .products-index-side-menu{
            display: none;
        }
        .subcategories-wrapper{
            width: 100%;
        }
    }
    .top-categories {
        display: none;
        width: 50%;
    }
    .top-categories-button {
        display: none;
    }
    .top-categories-list {
        height: ~"calc(100vh - 109px)";
        overflow-y: auto;
        display: block;
        width: initial;
    }
    .top-categories-item {
        float: none;
        height: auto;
        position: relative;
        .child-items {
            position: relative;
            border-radius: 0;
            //background: @color-light-grey-5;
            & > li {
                float: none;
                width: auto;
                padding: 0;
                .child-item-link {
                    padding: 10px 66px 10px 32px;
                    border-bottom: 1px solid @color-light-grey-5;
                    img {
                        position: static;
                        display: inline-block;
                        vertical-align: middle;
                        transform: none;
                    }
                    .child-item-name {
                        padding: 0;
                        color: @color-black;
                        line-height: 1.5;
                        font-weight: normal;
                        height: auto;
                        width: auto;
                    }
                }
                .grand-child-items{
                    display: none;
                    padding: 0;
                    margin: 0;
                    .grand-child-item-link {
                        padding: 10px 66px 10px 32px;
                        border-bottom: 1px solid @color-light-grey-5;
                    }
                    &.show-submenu{
                        display: block;
                    }
                }
            }
        }
        .expand-button {
            display: flex;
        }
    }
    .top-categories-link {
        position: relative; // .expand-button
        color: @color-black;
        line-height: 1.5;
        height: auto;
        padding: 14px 66px 14px 20px;
        border-radius: 0;
        border-bottom: 1px solid @color-white;
        background: @color-light-grey-5;
        text-align: left;
    }
    #showcase {
        margin: 60px 0 0;
    }
    #content {
        padding-top: 68px;
        #content-main {
            float: none;
            width: 100%;
        }
        #content-side {
            float: none;
            width: 100%;
            & > .spacer {
                padding-left: 0;
            }
            .categories-menu {
                display: none;
            }
        }
    }
    .wishlist-products-index,
    .products-index {
        &.detailed-products-index {
            .index {
                .product {
                    // 1 product per row
                    width: 100%;
                    margin-bottom: 2%;
                    &:nth-of-type(n) {
                        margin-left: 0;
                    }
                }
            }
        }
    }
    .with-menu-announcement {
        #showcase {
            margin: 0;
        }
        #content {
            &.without-side-content {
                padding-top: 68px;
            }
        }
    }
    .-run-breadcrumbs {
        font-size: 14px;
    }
    .product-view {
        padding-left: 0;
        padding-right: 0;
    }
    .articles-list, 
    .articles-review {
        padding-left: 0;
        padding-right: 0;        
    }
    .article-view {
        padding-left: 0;
        padding-right: 0;        
    }
    #content-bottom {
        .center {
            position: relative; // .top-10-products-index
        }
    }
    .top-10-products-index {
        .index {
            .product {
                display: inline-block;
                width: 25%;
                padding-right: 16px;
                margin: 11px 0 16px 0;
                .image {
                    margin-bottom: 0;
                }
                .texts {
                    padding: 0 0 0 8px;
                }
            }
        }
    }
    .manage-wishlist,
    .wishlist-search {
        width: 100vw;
        border-radius: 0;
        box-shadow: none;
        padding-right: 30px;
        margin-left: -15px;
    }
    .badges-menu li {
        display: flex;
        justify-content: center;
        padding: 0 16px 20px 16px;
        a{
            padding-right: 0;
            width: 80%;
        }
    }
    footer {
        .footer-main {
            .center {
                .spacer {
                    width: auto;
                }
            }
        }
    }
}
@media screen and (max-width: 960px) {
    .top-10-products-index {
        .index {
            .product {
                width: 33.33%; //100/3%;
                &:nth-of-type(n) {
                    margin-left: 0;
                }
            }
        }
    }
}
@media screen and (max-width: 900px) {
}
@media screen and (max-width: 880px) {
    .showcase-products-index {
        .index {
            .product {
                width: 33.33%; //1 / 3 * 100%;
                &:nth-child(4n) {
                    display: none;
                }            
            }
        }
    }   
    .article-view {
        .article-and-comments {
            float: none;
            width: 100%;
            margin-right: 0;
        }
        .next-articles-review {
            float: none;
            width: 100%;
            margin-top: 64px;
            .title {
                padding-bottom: 0;
                margin-bottom: 21px;
                border-bottom: none;
            }
            .article-item {
                // 2 articles per row
                display: inline-block;
                vertical-align: top;
                width: 48.05%; //370px / 770px * 100%;
                margin-left: 3.89%; //30px / 770px * 100%;
                margin-bottom: 3.89%; //30px / 770px * 100%;
                &:nth-of-type(n) {
                    margin-left: 3.89%; //30px / 770px * 100%;
                }
                &:nth-of-type(2n + 1) {
                    margin-left: 0;
                }
            }
            .button-wrapper {
                text-align: right;
            }
        }
    }
    .mini-search {
        .products-index {
            .index {
                .product {
                    width: 1 / 5 * 93%;
                }
            }
        }
    }
    .product-view {
        & > .comments {
            width: auto;
        }
    }
    footer .footer-badges {
        padding: 30px 0 30px;
    }
    .badges-menu {
        ul {             
            justify-content: space-around;
        }
        li {
            padding: 20px 16px;
            &:nth-of-type(n){
                width: 50%;
            }
        }
    }
}
@media screen and (max-width: 800px) {
    body, #content-main {
        .wishlist-products-index,
        .products-index {
            .index {
                .product {
                    // 2 products per row
                    width: 49.15%; //290px / 590px * 100%;
                    margin-left: 1.69%; //10px / 590px * 100%;
                    margin-bottom: 5.42%; //32px / 590px * 100%;
                    &:nth-of-type(n) {
                        margin-left: 1.69%; //10px / 590px * 100%;
                    }
                    &:nth-of-type(2n + 1) {
                        margin-left: 0;
                    }
                }
            }
        }
    }
    .articles-review,
    .articles-list {
        .article-item {
            // 2 articles per row
            width: 48.05%; //370px / 770px * 100%;
            margin-left: 3.89%; //30px / 770px * 100%;
            margin-bottom: 3.89%; //30px / 770px * 100%;
            &:nth-of-type(n) {
                margin-left: 3.89%; //30px / 770px * 100%;
            }
            &:nth-of-type(2n + 1) {
                margin-left: 0;
            }
        }        
    }
    .mini-search {
        .products-index {
            &:before {
                left: unset;
                right: 180px;
            }
            .index {
                .product {
                    width: 1 / 4 * 93%;
                }
            }
        }
    }
}
@media screen and (max-width: 790px) {    
    .header-bottom {
        & > .center {
            &:nth-of-type(2) {
                padding: 0;
            }
        }
    }
    .mini-search {
        margin-top: 11px;
        flex-direction: column;
        align-items: center;
        .mini-search-mobile-button {
            display: block;
            &.active {
                .mini-search-arrow {
                    opacity: 1;
                }
            }
        }
        .mini-search-arrow {
            .expanded-menu-arrow(16px);
            bottom: 2px;
        }
        .mini-search-form {
            display: none;
            position: absolute;
            top: 60px;
            width: 280px;
            padding: 15px;
            border-radius: 4px;
            background-color: @color-light-grey-5;
            margin-left: 40px;
        }
        .keywords-wrapper {
            width: 250px;
        }
        input[type="search"] {
            border-radius: 4px 4px 0 0;
        }
        .button-wrapper {
            width: 250px;
            border-radius: 0 0 4px 4px;
            background-image: none;
            &:before {
                display: none;
            }
        }
        input[type="submit"] {
            text-indent: 0;
            position: static;
            background-color: transparent;
        }
    }
    .bx-wrapper {
        .bx-viewport {
            border-radius: 0;
            margin: 0;
        }
        .bx-controls-direction {
            a {
                display: none;
            }
        }
    }
    .menu-announcement {
        border-radius: 0;
        .text {
            padding: 16px 10px;
        }
        &.lottery {
            .text {
                background-size: 32px auto;
                background-position: 10px center;
                padding-left: 54px;
            }
        }
    }
    #showcase {
        &.with-products {
            .label {
                padding-bottom: 50px;
            }
        }
    }
    //.special-offers-menu-toggle-button,
    .product-categories-menu-toggle-button,
    .product-groups-menu-toggle-button {
        display: block;
        position: static;
        margin: 0 ;
        width: auto;
        overflow: hidden;
        background-color: @color-darkred;
        border: none;
        border-bottom: 1px solid #FCFBF9;
        &:hover, &.open {                      
            background-color: @color-darkred;
            .icon {
                .bar {
                    background-color: #FCFBF9;
                }
            }
        }
        .label {
            float: left;
            line-height: 1em;
            color: #FCFBF9;
            font-weight: 700;
        }
        .icon {
            float: right;
            .bar {
                background-color: #FCFBF9;
            }
        }
    }
    .special-offers-menu,
    //.categories-menu,
    .product-groups-menu {
        display: none;
        max-width: none;
        margin-bottom: 0;
        .grid-no-side-padding;
        .title {
            display: none;
        }
        &.without-title {
            margin-top: 0;
        }
        .has-subitems {
            > a {
                .l {
                    display: inline-block;
                    top: 2px;
                    &:before {
                        font-size: 17px;
                    }
                }
            }
        }
        .level-01,
        .level-02,
        .level-03,
        .level-04,
        .level-05 {
            > li {
                overflow: hidden;
                > a {  
                    padding: 9px 10px;
                    text-align: center;
                }
            }
        }
        .level-01 {
            > li {
                border-bottom: 1px solid #ded9d7;
                &:last-child {
                    border-bottom: 1px solid @color-darkred;
                }
            }
        }
    }
    .special-offers-menu {
        display: block;
        font-size: @font-size;
        font-weight: 700;
    }
    .top-10-products-index {
        .index {
            padding: 0;
            box-shadow: none;
        }
    }
    #cart-view {
        .cart {
            padding: 0;
            border-left: none;
            border-right: none;
        }
    }   
    .checkout-header {
        .progress-bar {
            .checkout-step {
                .label {
                    display: none;
                }
                font-size: 1.5rem;
            }
            .checkout-step-01:before {
                .font-awesome-icon('\f07a');
            }
            .checkout-step-02:before {
                .font-awesome-icon('\f007');
            }
            .checkout-step-03:before {
                .font-awesome-icon('\f0d1');
            }
            .checkout-step-03.payment-only:before {
                .font-awesome-icon('\f283');
            }
            .checkout-step-04:before {
                .font-awesome-icon('\f00c');
            }
        }
    }
    .checkout-footer {
        border-left: none;
        border-right: none;
        border-bottom: none;
    }
    .checkout {
        form {
            padding-left: 0;
            padding-right: 0;
            border-left: none;
            border-right: none;
            .grid-row {
                .grid-col {
                    .grid-break;
                }
            }
            .title {
                margin-bottom: 15px;
            }
            .input-wrapper {
                margin-bottom: 15px;
            }
            label {
                text-align: left;
            }
        }
    }
    .checkout-address {
        .delivery-address {
            .delivery-address-switch {
                .toggle-input-wrapper {
                    float: none;
                    margin-bottom: 5px;
                }
            }
        }
    }  
    #login {
        .login-header {
            margin-bottom: 25px;
            font-size: 0;
            .title {
                font-size: 20px;
                margin-bottom: 0;
            }
        }
        .grid-row {
            .grid-col {
                .grid-break;
            }
        }
        .title {
            font-size: 20px;
            margin-bottom: 15px;
        }
    }
    .user-profile-menu {
        margin-bottom: 25px;
        .item {
            font-size: 0;
            .title {
                font-size: 20px;
                margin-bottom: 0;
            }
        }
    }
    .user-profile {
        .user-profile-header {
            margin-bottom: 25px;
            font-size: 0;
            .title {
                font-size: 20px;
                margin-bottom: 0;
            }
        }
    }
    .verify-voucher {
        .label-wrapper,
        .input-wrapper {
            float: none;
            padding: 0;
        }
        .label-wrapper {
            width: 250px;
            label {
                display: inline-block;
                text-align: left;
            }
            .sc-change {
                float: none;
                display: iniline-block;
                display: static;
                top: 0;
            }
        }
        button {
            margin-left: 0;
        }
        .result-wrapper {
            margin-left: 0;
        }
    }    
}
@media screen and (max-width: 740px) {    
    .user-menu {
        margin: 10px 6px 0 2px;
        .menu-list-arrow {
            bottom: -10px;
        }
    }
    .bonus-info {
        margin: 10px 6px 0 0;
        .bonus-menu-arrow {
            bottom: -10px;
        }
    }
    .hamburger {
        margin: 10px 0 0 6px;
    }
    .manage-wishlist {
        #addWishlistForm,
        #editWishlistForm,
        #shareWishlistForm {
            table {
                display: block;
                tbody, tr {
                    display: block;
                }
                td {
                    display: block;
                    padding-right: 0;
                    &.label {
                        &:after {
                            content: ':';
                        }
                    }
                    .text-input {
                        width: 100%;
                    }
                }
            }
        }
    }
}
@media screen and (max-width: 680px) {
    .showcase-products-index {
        .index {
            .product {
                width: 50%;
                &:nth-child(4n) {
                    display: inline-block;
                }            
            }
        }
    }   
    .top-10-products-index {
        .index {
            .product {
                width: 50%;
                &:nth-of-type(n) {
                    margin-left: 0;
                }
            }
        }
    }
    h1, .h1 {
        font-size: 48px;
    }
    .article-view {
        .item-title {
            font-size: 48px;
            margin-bottom: 25px;
        }
    }
}

@media screen and (max-width: 660px) {
    .slider {
        .slide {
            .smart-image-with-content {
                .content {
                    padding-top: 4.44%; //26px / 585px * 100%;
                    padding-right: 4.44%; //26px / 585px * 100%;
                    padding-bottom: 4.44%; //26px / 585px * 100%;
                    padding-left: 7.35%; //43px / 585px * 100%;
                }
            }    
        }
    }    
}
@media screen and (max-width: 640px) {
    .wishlist-products-index,
    .top-10-products-index {
        .index {
            .product {
                width: 49%;
                &:nth-of-type(n) {
                    margin-left: 2%;
                }
                &:nth-of-type(2n + 1) {
                    margin-left: 0;
                }
            }
        }
    }
}
@media screen and (max-width: 600px) {
    .main-navbar {
        height: ~"calc(100vh - 60px)";
        overflow-y: auto;
    }
    .all-categories,
    .top-categories {
        float: none;
        width: 100%;
    }
    .categories-menu,
    .top-categories-list {
        height: auto;
        overflow-y: visible;
    }
    .product-view {
        & > .card {
            & > .images, 
            & > .texts, 
            & > .description, 
            & > .attributes {
                float: none;
                width: 100%;
                padding-left: 0;
            }
            & > .texts, 
            & > .description, 
            & > .attributes {
                margin-top: 57px;
            }
        }
    }
}
@media screen and (max-width: 580px) {
    .checkout-confirmation {
        .checkout-data {
            > div {
                float: none;
                width: 100%;
                //display: table;
                //margin: 0 auto;
            }
        }
        .checkout-summary {
            .checkout-total {
                float: none;
                width: 100%;
            }
            .checkout-info {
                float: none;
                width: 100%;
                padding-top: 30px;
            }
        }
    }  
    .user-profile {
        .grid-row {
            .grid-col {
                .grid-break;
            }
        }
        .text-input {
            width: 100%;
        }
        label {
            text-align: left;
            color: #333;
        }
        .title {
            font-size: 20px;
            margin-bottom: 15px;
        }
        .form-section {
            margin-bottom: 15px;
        }
    }  
    .app-announcement {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
    }
    .mini-search {
        .products-index {
            .index {
                .product {
                    width: 1 / 2 * 95%;
                }
            }
            .scroll-wrapper {
                padding-right: 11vw;
            }
        }
    }
}
@media screen and (max-width: 540px) {
    body, #content-main {
        .wishlist-products-index,
        .products-index {
            .index {
                .product {
                    // 1 product per row
                    width: 100%;
                    margin-left: 0;
                    margin-bottom: 11.03%; //32px / 290px * 100%;
                    &:nth-of-type(n) {
                        margin-left: 0;
                    }
                }
            }
        }
    }
    .products-index {
        &.detailed-products-index {
            .index {
                .product {
                    .controls {
                        margin-bottom: 8px;
                    }
                }
            }
        }
    }
}
@media screen and (max-width: 520px) {
    .products-index {
        &.detailed-products-index {
            .index {
                .product {
                    .controls {
                        float: none;
                        margin-left: 0;
                    }
                }
            }
        }
    }
}
@media screen and (max-width: 500px) {
    #header-bottom {
        #logo {
            display: inline-block;
            padding-left: 18px;
            img {
                height: 54px;
            }
        }
    }
    #newsletter-subscription-form {
        width: 100%;
    }
    footer .footer-main {
        .col2, .col3, .col4 {
            width: 100%;
        }
    }
}
@media screen and (max-width: 480px) {
    .main-logo {
        padding-right: 6px;
        width: 164px;
        img {
            &.default {
                //display: none;
            }
            &.small {
                //display: block;
            }
        }
        /*/>
        width: 87px;
        overflow: hidden;
        img {
            width: 217px;
            max-width: none;
            height: 100%;
            margin: 0px 0 0 0px;
        }
        //*/
    }
    .showcase-products-index {
        .index {
            .product {
                width: 100%;
            }
        }
    }
    .multifilter a.display-extended-filter-btn {
        font-size: 0;
        padding: 0 28px 0 3px;
        margin: 0;
    }
    .top-10-products-index {
        .index {
            .product {
                width: 100%;
                max-width: none;
                padding-right: 0;
                &:nth-of-type(n) {
                    margin-left: 0;
                }
            }
        }
    }
    .references-slider {
        margin-bottom: 40px;
        .image {
            width: 285px;
            height: 285px;
        }
        .text {
            padding-top: 40px;
        }
    }
    .partners {
        margin-bottom: 19px;
    }
    #cart-view {
        .cart-summary {
            .cart-total {
                float: none;
                width: 100%;
                padding-bottom: 20px;
                .label,
                .price {
                    width: 100%;
                    display: block;
                    padding: 0;
                    text-align: center;
                }
            }
            .cart-info {
                float: none;
                width: 100%;
                padding-top: 30px;
                text-align: center;
            }
        }
    }
    .orders-index {
        .order-products-price,
        .order-shipment-price,
        .orders-products-grand-total-price {
            display: none !important;
        }
    }
    .order-overview {
        .product-unit-price {
            display: none !important;
        }
    }    
    .checkout-shipment-and-payment {
        .info {
            select.text-input {
                max-width: 100%;
            }
        }
    }
    .articles-review,
    .articles-list {
        .article-item {
            // 1 articles per row
            width: 100%;
            margin-left: 0;
            margin-bottom: 8.1%; //30px / 370px * 100%;
            &:nth-of-type(n) {
                margin-left: 0;
            }
        }        
    }
    h1, .h1 {
        font-size: 42px;
    }
    .article-view {
        .item-title {
            font-size: 42px;
        }
        .resume-text {
            font-size: 16px;
        }
        .next-articles-review {
            .article-item {
                // 1 articles per row
                width: 100%;
                margin-left: 0;
                margin-bottom: 8.1%; //30px / 370px * 100%;
                &:nth-of-type(n) {
                    margin-left: 0;
                }
            }
        }
    }
    .badges-menu {
        li {
            &:nth-of-type(n){
                width: 100%;
            }
        }
    }
    .comment-form  {
        .form-fields {
            .name-and-rating {
                display: block;
                margin-bottom: 10px;
                .name {
                    margin-bottom: 10px;
                }
                .rating {
                    label {
                        padding-left: 16px;
                    }
                }
            }
        }
    }
    .mini-search {
        .products-index {
            .index {
                .product {
                    width: 1 / 2 * 95%;
                }
            }
        }
    }
}
@media screen and (max-width: 440px) {
    .products-index.detailed-products-index {
        .index {
            .product {
                .texts {
                    width: 100%;
                    padding: 0;
                }
                .image {
                    width: 180px;
                    margin-left: calc(~"50% - 90px");
                    margin-bottom: 8px;
                }
                .controls {
                    float: right;
                    width: 120px;
                    text-align: right;
                    margin-bottom: 0;
                }
            }
        }
    }
    .cart-products {
        .cart-products-header {
            display: none;
        }
        .cart-product {
            display: block;
            > div {
                display: inline-block;
                height: auto;
                > .label {
                    display: block;
                }
            }
        }
        .cart-product {
            position: relative; //.product-actions
            padding-bottom: 16px;
            .product-overview {
                width: 100%;
                height: auto;
                padding-right: 37px;
            }
            .product-unit-price {
                width: 33%;
                padding-left: 2px
            }
            .product-amount {
                width: 33%;
            }
            .product-total-price {
                width: 33%;
            }
            .product-actions {
                position: absolute;
                width: 12px;
                top: 18px;
                right: 15px;
                padding: 0;
            }
        }
    }
}
@media screen and (max-width: 420px) {
    .multifilter {
        .filter-bar {
            .right-controls-wrapper {
                text-align: right;
                width: 100%;
            }
        }
    }
    .wishlist-search {
        .search-box {
            display: block;
            .text-input {
                display: block;
                width: 100%;
                border-radius: 4px 4px 0 0;
            }
            .button {
                display: block;
                width: 100%;
                border-radius: 0 0 4px 4px;
                margin: 0;
            }
        }
    }
    #newsletter-subscription-form {
        .form {
            .email-wrapper {
                button {
                    padding: 0 13px 0 18px;    
                }
            }
        }
    }
    .mini-search {
        .keywords-wrapper {
            width: 216px;
        }  
    }
}
@media screen and (max-width: 380px) {
    #content {
        .products-index {
            .index {
                justify-content: center;
            }
        }
    }
}
@media screen and (max-width: 360px) {
    #content-main {
        .section-title {
            text-align: center;
        }
        .products-index {
            & > .section-title {
                text-align: left;
            }
        }
    }
}
@media screen and (max-width: 340px) {
    .login-form, .forgotten-password-form {
        width: auto;
        .text-input {
            width: 100%;
        }
    }
    .manage-wishlist {
        #shareWishlistForm {
            padding-bottom: 8px;
            form {
                display: block;
                .text-input {
                    display: block;
                    border-radius: 4px 4px 0 0;
                }
                .button {
                    display: block;
                    border-radius: 0 0 4px 4px;
                    margin: 0;
                }
            }
        }
    }
}

// CHRISTMASS RESPONSIVE STYLES
//@import "_vianoce_responsive.less";
