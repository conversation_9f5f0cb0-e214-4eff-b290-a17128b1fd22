// Bootstrap core variables and mixins
//@import "../vendors/less/bootstrap/variables.less";
//@import "../vendors/less/bootstrap/mixins.less";
// local variables
@import "_variables.less";
@import "_mixins.less";

//
// RESPONSIVE GRID
//
//      <div class="grid-row grid-break-560">
//          <div class="grid-col grid-col-20-100 grid-center grid-hide-400">Stlpec 1</div>
//          <div class="grid-col grid-col-1-3 grid-break-780">Stlpec 2</div>
//          <div class="grid-col grid-col-2-7 grid-col-offset-10-100">Stlpec 3</div>
//      </div>
//      <div class="grid-row grid-break-560">
//          ...
//      </div>
//      ...
//
//  NOTE: The wrapping .grid-full-width-container is needed only when you would like to keep left and 
//  rigth padding of toplevel grid-row. When the grid stretches to full screen then 
//  either .grid-full-width-container must be used (applied as mixin on real grid container) 
//  or toplevel .grid-row left and right margin must be set to 0.

// - basic classes
.grid-row {
    margin-right: (@grid-cols-gap-width / -2);
    margin-left: (@grid-cols-gap-width / -2);
    &:before {
        display: table;
        content: " ";
    }
    &:after {
        display: table;
        content: " ";
        clear: both;
    }
}
.grid-col {
    position: relative;
    min-height: 1px;
    padding-right: (@grid-cols-gap-width / 2);
    padding-left: (@grid-cols-gap-width / 2);
    float: left;
}
.grid-h-scroll {
    overflow-x: auto;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    min-height: 0.01%; // Workaround for IE9 bug (see https://github.com/twbs/bootstrap/issues/14837)
}
// MIXIN classes to be used as mixins in @media queries of responsive.less
//
// apply .grid-break only as mixin on .grid-col class
.grid-break {
    float: none;
    clear: both;
    width: auto;
    margin-left: 0;
}
// apply .grid-full-width-container class to toplevel grid wrapper
.grid-full-width-container {
    padding-right: (@grid-cols-gap-width / 2);
    padding-left: (@grid-cols-gap-width / 2);
}
// apply .grid-no-side-padding to element contained in .grid-full-width-container
// which you would like to stretch to full screen width without padding
.grid-no-side-padding {
    margin-right: (@grid-cols-gap-width / -2);
    margin-left: (@grid-cols-gap-width / -2);
}
// - columns
.make-grid-cols(100);
.make-grid-cols(3);
.make-grid-cols(6);
.make-grid-cols(7);
.make-grid-cols(8);
.make-grid-cols(9);
.make-grid-cols(12);
// - column offsets
.make-grid-col-offsets(100);
.make-grid-col-offsets(3);
.make-grid-col-offsets(6);
.make-grid-col-offsets(7);
.make-grid-col-offsets(8);
.make-grid-col-offsets(9);
.make-grid-col-offsets(12);
// - events
.make-grid-events();

//
// RESPONSIVE GRID MIXINS
//

/**
 * Mixin to generate grid columns
 *
 * param integer @numCols 
 * param integer @minFraction
 * param integer @step
 * param string @classPrefix
 */
.make-grid-cols(
    @numCols: 100, 
    @minFraction: 1, 
    @step: 1, 
    @classPrefix: grid-col, 
    @fraction: @minFraction
) when (@fraction =< @numCols) {
    // render css
    .@{classPrefix}-@{fraction}-@{numCols} {
      width: (@fraction * 100% / @numCols);
    }
    // loop by recursion
    .make-grid-cols(@numCols, @minFraction, @step, @classPrefix, (@fraction + @step));
}

/**
 * Mixin to generate grid column offsets
 *
 * param integer @numCols 
 * param integer @minFraction
 * param integer @step
 * param string @classPrefix
 */
.make-grid-col-offsets(
    @numCols: 100, 
    @minFraction: 1, 
    @step: 1, 
    @classPrefix: grid-col-offset, 
    @fraction: @minFraction
) when (@fraction =< @numCols) {
    // render css
    .@{classPrefix}-@{fraction}-@{numCols} {
      margin-left: (@fraction * 100% / @numCols);
    }
    // loop by recursion
    .make-grid-col-offsets(@numCols, @minFraction, @step, @classPrefix, (@fraction + @step));
}

/**
 * Mixin to generate grid breaks
 *
 * param integer @minWidth 
 * param integer @maxWidth
 * param integer @step
 * param string @breakClassPrefix Prefix to be used for break classes. If 0 then no
        break classes are generated. Defaults to grid-break.
 * param string @hideClassPrefix Prefix to be used for hide classes. If 0 then no
        hide classes are generated. Defaults to grid-hide.
 * param string @showClassPrefix Prefix to be used for show classes. If 0 then no
        show classes are generated. Defauts to 0.
 * param string @scrollClassPrefix Prefix to be used for horizontal scroll classes. 
        If 0 then no horizontal scroll classes are generated. Defaults to grid-h-scroll.
 * param string @colClass
 * param string @centerClass
 * param string @centerTextClass
 * param string @leftTextClass
 */
.make-grid-events(
    @minWidth:300, 
    @maxWidth: 1000, 
    @step: 20, 
    @breakClassPrefix: grid-break, 
    @hideClassPrefix: grid-hide,
    @showClassPrefix: 0,
    @scrollClassPrefix: grid-h-scroll,
    @colClass: grid-col, 
    @centerClass: grid-center,
    @centerTextClass: grid-center-text,
    @leftTextClass: grid-left-text,
    @width: @minWidth
) when (@width =< @maxWidth) {
    // render css
    @media (max-width: unit(@width, px)) {
        .make-grid-break(
            @width,
            @breakClassPrefix,
            @colClass, 
            @centerClass,
            @centerTextClass,
            @leftTextClass
        );
        .make-grid-hide(
            @width,
            @hideClassPrefix 
        );
        .make-grid-show(
            @width,
            @showClassPrefix 
        );
        .make-grid-h-scroll(
            @width,
            @scrollClassPrefix 
        );
    }
    // loop by recursion
    .make-grid-events(
        @minWidth, 
        @maxWidth, 
        @step, 
        @breakClassPrefix, 
        @hideClassPrefix,
        @showClassPrefix,
        @scrollClassPrefix,
        @colClass, 
        @centerClass, 
        @centerTextClass, 
        @leftTextClass,
        (@width + @step)
    );
}

/**
 * Mixin to generate grid break
 *
 * param integer @width
 * param string @breakClassPrefix
 * param string @colClass
 * param string @centerClass
 * param string @centerTextClass
 */
.make-grid-break(
    @width,
    @breakClassPrefix: grid-break, 
    @colClass: grid-col, 
    @centerClass: grid-center,
    @centerTextClass: grid-center-text,
    @leftTextClass: grid-left-text,
) when not (isnumber(@breakClassPrefix)) {
    // render css
    .@{breakClassPrefix}-@{width} > .@{colClass}, 
    .@{colClass}.@{breakClassPrefix}-@{width} {
        float: none;
        clear: both;
        width: auto;
        margin-left: 0;
    }
    .@{breakClassPrefix}-@{width}.@{centerClass} > .@{colClass}, 
    .@{breakClassPrefix}-@{width} > .@{colClass}.@{centerClass}, 
    .@{centerClass} > .@{colClass}.@{breakClassPrefix}-@{width},
    .@{colClass}.@{breakClassPrefix}-@{width}.@{centerClass} {
        display: table;
        margin: 0 auto;
        word-wrap: initial;
    }
    .@{breakClassPrefix}-@{width}.@{centerTextClass} > .@{colClass}, 
    .@{breakClassPrefix}-@{width} > .@{colClass}.@{centerTextClass}, 
    .@{centerTextClass} > .@{colClass}.@{breakClassPrefix}-@{width},
    .@{colClass}.@{breakClassPrefix}-@{width}.@{centerTextClass} {
        text-align: center !important;
    }
    .@{breakClassPrefix}-@{width}.@{leftTextClass} > .@{colClass}, 
    .@{breakClassPrefix}-@{width} > .@{colClass}.@{leftTextClass}, 
    .@{leftTextClass} > .@{colClass}.@{breakClassPrefix}-@{width},
    .@{colClass}.@{breakClassPrefix}-@{width}.@{leftTextClass} {
        text-align: left !important;
    }
}

/**
 * Mixin to generate grid hide
 *
 * param integer @width
 * param string @hideClassPrefix
 */
.make-grid-hide(
    @width,
    @hideClassPrefix: grid-hide, 
) when not (isnumber(@hideClassPrefix)) {
    // render css
    .@{hideClassPrefix}-@{width} {
        display: none !important;
    }
}

/**
 * Mixin to generate grid show
 *
 * param integer @width
 * param string @showClassPrefix
 */
.make-grid-show(
    @width,
    @showClassPrefix: grid-show, 
) when not (isnumber(@showClassPrefix)) {
    // render css
    .@{showClassPrefix}-@{width} {
        display: block !important;
    }
    .@{showClassPrefix}-inline-@{width} {
        display: inline !important;
    }
    .@{showClassPrefix}-inline-block-@{width} {
        display: inline-block !important;
    }
}

/**
 * Mixin to generate grid horizontal scroll
 *
 * param integer @width
 * param string @scrollClassPrefix
 */
.make-grid-h-scroll(
    @width,
    @scrollClassPrefix: grid-h-scroll, 
) when not (isnumber(@scrollClassPrefix)) {
    // render css
    .@{scrollClassPrefix}-@{width} {
        > * {
            margin: 0;
        }
        > table {
            width: 100%;
            max-width: 100%;
            table-layout: auto;
            white-space: nowrap;
        }
    }
}