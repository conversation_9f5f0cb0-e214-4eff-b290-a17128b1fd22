// Include this file at the end of _resposive.less

@media screen and (max-width: 1100px) {
    #header-top {    
        padding-bottom: 33px + 51px + 12px;
    }
}
@media screen and (max-width: 780px) {
    #header-top {
        padding-bottom: 33px + 51px;
        background: url('@{img-dir}/vianoce/header-top-bg.png') center bottom repeat-x, url('@{img-dir}/vianoce/winter-narrow.png') center bottom no-repeat, url('@{img-dir}/vianoce/header-top-bottom-bg.png') center bottom repeat-x;
        background-color: #ded9d7;    
    }
    header.with-showcase {
        #header-top {
            background: url('@{img-dir}/vianoce/header-top-bg.png') center bottom repeat-x, url('@{img-dir}/vianoce/winter-narrow.png') center bottom no-repeat, url('@{img-dir}/vianoce/header-top-bottom-with-showcase-bg.png') center bottom repeat-x;
            background-color: #ded9d7;    
        }
    }
}
@media screen and (max-width: 500px) {
    #header-top {
        background: url('@{img-dir}/vianoce/header-top-bg.png') center bottom repeat-x, url('@{img-dir}/vianoce/winter-right.png') -53px bottom no-repeat, url('@{img-dir}/vianoce/header-top-bottom-bg.png') center bottom repeat-x;
        background-color: #ded9d7;    
    }
    header.with-showcase {
        #header-top {
            background: url('@{img-dir}/vianoce/header-top-bg.png') center bottom repeat-x, url('@{img-dir}/vianoce/winter-right.png') -53px bottom no-repeat, url('@{img-dir}/vianoce/header-top-bottom-with-showcase-bg.png') center bottom repeat-x;
            background-color: #ded9d7;    
        }
    }
}

