<?php
/**
 * Use Consent class in following way:
 * 
 * 1] To add a new consent:
 * 
 *      App::loadModel('App', 'Consent');
 *      $Consent = new Consent();
 *      $Consent->add('EshopTermsAndConditionsConsent', 'App.User', 25);
 * 
 * 2] To check for a consent:
 * 
 *      App::loadModel('App', 'Consent');
 *      $Consent = new Consent();
 *      if ($Consent->has('EshopTermsAndConditionsConsent', 'App.User', 25)) { 
 *          ...
 *      }
 * 
 * 3] To get list of all consent owner ids:
 * 
 *      App::loadModel('App', 'Consent');
 *      $Consent = new Consent();
 *      $ids = $Consent->getOwnerIds('EshopTermsAndConditionsConsent', 'App.User');
 * 
 * 4] To remove a consent:
 * 
 *      App::loadModel('App', 'Consent');
 *      $Consent = new Consent();
 *      $Consent->remove('EshopTermsAndConditionsConsent', 'App.User', 25);
 * 
 */
class Consent extends Model {
    
    protected $table = 'run_consents';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_consent_types_id' => array('type' => 'int', 'index' => 'index'),
        'owner_id' => array('type' => 'int', 'index' => 'index', 'comment' => 'Consent owner is always some record. This is the id of that record. For owner_model see the table of run_consent_types'),
        'created' => array('type' => 'datetime', 'default' => null),
    );
    
    protected $tableIndexes = array(
        array(
            'type' => 'unique',
            'fields' => array('run_consent_types_id', 'owner_id'),
            'name' => 'Owner consents index'
        ),
    );
    
    /**
     * Cache of resolved consents used by method Consent::has()
     * 
     * @var array 
     */
    protected static $consents = array();
    
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(
            'run_consent_types_id' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide a consent type id'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide a consent type id'),
                ),
            ),
            'owner_id' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide a owner id'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide a owner id'),
                ),
            ),
        );
    }
    
    /**
     * Adds a new consent
     * 
     * @param string $pid Consent type pid, e.g. NewsletterConsent, EshopTermsAndConditionsConsent
     * @param string $ownerModel Consent type owner model. Owner is always some record. This is the 
     *      model name that record belongs to, e.g. App.User, Mailer.MailerContact.
     * @param integer $ownerId Consent owner id. Owner is always some record. This is the id 
     *      of that record.
     */
    public function add($pid, $ownerModel, $ownerId) {
        App::loadModel('App', 'ConsentType');
        $ConsentType = new ConsentType();
        $consentTypeId = $ConsentType->findField('id', array(
            'conditions' => array(
                'pid' => $pid,
                'owner_model' => $ownerModel
            )
        ));
        if (empty($consentTypeId)) {
            throw new Exception(__e(__FILE__, 'Unexisting consent type %s, %s', $pid, $ownerModel));
        }
        $data = array(
            'run_consent_types_id' => $consentTypeId,
            'owner_id' => $ownerId,
        );
        try {
            if (!$this->save($data)) {
                $errors = $this->getErrors();
                $message = '';
                foreach ($errors as $field => $fieldErrors) {
                    $message .= $message ? '; ' : '';
                    $message .= $field . ' - ' . implode(', ', $fieldErrors);
                }
                throw new Exception(__e(__FILE__, 'Consent addition has failed with following errors: %s', $message));
            }
        } 
        catch (Throwable $e) {
            // if DB error is not ER_DUP_KEYNAME (http://dev.mysql.com/doc/refman/5.0/en/error-messages-server.html#error_er_dup_keyname)
            // then rethrow the exception
            if ($e->getCode() != 1062) {
                throw $e;
            }
        }
    }
    
    /**
     * Checks for existence of specified consent
     * 
     * NOTE: Results are internally cached.
     * 
     * @param string $pid Consent type pid, e.g. NewsletterConsent, EshopTermsAndConditionsConsent
     * @param string $ownerModel Consent type owner model. Owner is always some record. This is the 
     *      model name that record belongs to, e.g. App.User, Mailer.MailerContact.
     * @param integer $ownerId Consent owner id. Owner is always some record. This is the id 
     *      of that record.
     * 
     * @return boolean
     */
    public function has($pid, $ownerModel, $ownerId) {
        if (!isset(self::$consents[$pid][$ownerModel][$ownerId])) {
            App::loadModel('App', 'ConsentType');
            $ConsentType = new ConsentType();
            $consentTypeId = $ConsentType->findField('id', array(
                'conditions' => array(
                    'pid' => $pid,
                    'owner_model' => $ownerModel
                )
            ));
            if (empty($consentTypeId)) {
                $message = __e(__FILE__, 'Unexisting consent type %s, %s', $pid, $ownerModel);
                App::debug($message, $message);
                return false;
            }
            self::$consents[$pid][$ownerModel][$ownerId] =  (bool)$this->findField('id', array(
                'conditions' => array(
                    'run_consent_types_id' => $consentTypeId,
                    'owner_id' => $ownerId,
                )
            ));
        }
        return self::$consents[$pid][$ownerModel][$ownerId];
    }
    
    /**
     * Returns list of consent owner ids for specified consent type.
     * 
     * @param string $pid Consent type pid, e.g. NewsletterConsent, EshopTermsAndConditionsConsent
     * @param string $ownerModel Consent type owner model. Owner is always some record. This is the 
     *      model name that record belongs to, e.g. App.User, Mailer.MailerContact.
     * 
     * @return array List of consent owner ids containing pairs like {ownerId} => {ownerId}
     */
    public function getOwnerIds($pid, $ownerModel) {
        App::loadModel('App', 'ConsentType');
        $ConsentType = new ConsentType();
        $consentTypeId = $ConsentType->findField('id', array(
            'conditions' => array(
                'pid' => $pid,
                'owner_model' => $ownerModel
            )
        ));
        if (empty($consentTypeId)) {
            $message = __e(__FILE__, 'Unexisting consent type %s, %s', $pid, $ownerModel);
            App::debug($message, $message);
            return array();
        }
        return $this->findList(array(
            'key' => 'id',
            'fields' => array('id'),
            'conditions' => array(
                'run_consent_types_id' => $consentTypeId,
            )
        ));
    }
    
    /**
     * Removes specifies consent
     * 
     * @param string $pid Consent type pid, e.g. NewsletterConsent, EshopTermsAndConditionsConsent
     * @param string $ownerModel Consent type owner model. Owner is always some record. This is the 
     *      model name that record belongs to, e.g. App.User, Mailer.MailerContact.
     * @param integer $ownerId Consent owner id. Owner is always some record. This is the id 
     *      of that record.
     */
    public function remove($pid, $ownerModel, $ownerId) {
        App::loadModel('App', 'ConsentType');
        $ConsentType = new ConsentType();
        $consentTypeId = $ConsentType->findField('id', array(
            'conditions' => array(
                'pid' => $pid,
                'owner_model' => $ownerModel
            )
        ));
        if (empty($consentTypeId)) {
            $message = __e(__FILE__, 'Unexisting consent type %s, %s', $pid, $ownerModel);
            App::debug($message, $message);
            return;
        }
        $this->delete(array(
            'conditions' => array(
                'run_consent_types_id' => $consentTypeId,
                'owner_id' => $ownerId,
            )
        ));
    }
}
