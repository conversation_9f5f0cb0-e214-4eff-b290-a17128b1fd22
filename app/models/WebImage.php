<?php
class WebImage extends Model {

    var $table = 'run_web_images';   
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_web_contents_id' => array('type' => 'int', 'index' => 'index'),
        'file' => array('type' => 'varchar'),
        'alternative_file' => array('type' => 'varchar', 'default' => null),
        'name' => array('type' => 'varchar', 'default' => null),
        'sort' => array('type' => 'int'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
         
    protected $fileFields = array(
        'file' => array(
            'path' => 'galleryImages',
            'variants' => array(
                'originalThumb' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    'fit' => array(200, 200),
                    'lazy' => true,
                ),
                'squareThumb' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(220, 220),
                    'cover' => array(220),
                    'cropInMiddle' => array(220, 220),
                    'lazy' => true,
                ),
//                'squareThumbMedium' => array(
//                    'extension' => 'jpg',
//                    'pngGifToJpg' => array(array(255, 255, 255)),
//                    //'fit' => array(160, 160),
//                    'cover' => array(160),
//                    'crop' => array(160, 160),
//                    'lazy' => true,
//                ),
                'squareThumbSmall' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(120, 120),
                    'cover' => array(120),
                    'cropInMiddle' => array(120, 120),
                    'lazy' => true,
                ),
                'original' => array(
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(1920, 1080),
                    'source' => true,
                ),
            ),
        ),
        'alternative_file' => array(
            'path' => 'galleryImages',
            'variants' => array(
                'originalThumb' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    'fit' => array(200, 200),
                    'lazy' => true,
                ),
                'squareThumb' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(220, 220),
                    'cover' => array(220),
                    'cropInMiddle' => array(220, 220),
                    'lazy' => true,
                ),
//                'squareThumbMedium' => array(
//                    'extension' => 'jpg',
//                    'pngGifToJpg' => array(array(255, 255, 255)),
//                    //'fit' => array(160, 160),
//                    'cover' => array(160),
//                    'crop' => array(160, 160),
//                    'lazy' => true,
//                ),
                'squareThumbSmall' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    //'fit' => array(120, 120),
                    'cover' => array(120),
                    'cropInMiddle' => array(120, 120),
                    'lazy' => true,
                ),
                'original' => array(
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(1920, 1080),
                    'source' => true,
                ),
            ),
        )
    );
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        
        // if there is no name on creation so create implicit one
        if (
            $options['on'] === 'create'
            && !empty($data['file']['name'])
            && empty($data['name'])
        ) {
            $data['name'] = explode('.', $data['file']['name']);
            array_pop($data['name']);
            $data['name'] = implode('.', $data['name']);
            $data['name'] = Str::humanize($data['name']);
        }
        
        if (!empty($data['_delete_alternative_file'])) {
            $data['alternative_file'] = '';
        }
        
        return parent::normalize($data, $options);
    }
        
    /**
     * 
     * @param string $fileName
     * @param array $options
     */
    public function makeThumbs($fileName, $options = array()) {   
        $defaults = array(
            'width' => 123, //184, //86,
            'height' => 86, //129, //8
            'suffix' => 'thumb',
            // set to true to insert to database rather than render to screen or file (see below)
            'captureRawData' => false,
        );
        $options = array_merge($defaults, $options);
        
        $pathParts = File::getPathinfo($fileName);
      
        $phpThumb = new phpThumb();
        
        // this is very important when using a single object to process multiple images
        $phpThumb->resetObject();

        // set data source -- do this first, any settings must be made AFTER this call
        $phpThumb->setSourceFilename(ROOT . '/userfiles/images/galleries/' . $fileName);  // for static demo only
        //$phpThumb->setSourceFilename($_FILES['userfile']['tmp_name']);
        // or $phpThumb->setSourceData($binary_image_data);
        // or $phpThumb->setSourceImageResource($gd_image_resource);

        // PLEASE NOTE:
        // You must set any relevant config settings here. The phpThumb
        // object mode does NOT pull any settings from phpThumb.config.php
        //$phpThumb->setParameter('config_document_root', '/home/<USER>/p/ph/phpthumb/htdocs/');
        //$phpThumb->setParameter('config_cache_directory', '/tmp/persistent/phpthumb/cache/');

        // set parameters (see "URL Parameters" in phpthumb.readme.txt)
        $phpThumb->setParameter('w', $options['width']);
        $phpThumb->setParameter('h', $options['height']);
        $phpThumb->setParameter('q', 98);
        $phpThumb->setParameter('zc', 'C');        	
        //$phpThumb->setParameter('fltr', 'gam|1.2');
        //$phpThumb->setParameter('fltr', 'wmi|../watermark.jpg|C|75|20|20');

        // set options (see phpThumb.config.php)
        // here you must preface each option with "config_"
        //$phpThumb->setParameter('config_output_format', 'jpeg');
        //$phpThumb->setParameter('config_imagemagick_path', '/usr/local/bin/convert');
        //$phpThumb->setParameter('config_allow_src_above_docroot', true); // needed if you're working outside DOCUMENT_ROOT, in a temp dir for example

        // generate & output thumbnail
        // make dir if not exist
        if (!file_exists(ROOT . '/userfiles/images/galleries/_thumbs')) { 
            mkdir(ROOT . '/userfiles/images/galleries/_thumbs');
        }
        $output_filename = ROOT . '/userfiles/images/galleries/_thumbs/' . $pathParts['filename'] . '_' . $options['suffix'] . '.' . $pathParts['extension'];
        if ($phpThumb->GenerateThumbnail()) { // this line is VERY important, do not remove it!
////mojo: this is not used and where was an error on linux because of this
//        		$output_size_x = ImageSX($phpThumb->gdimg_output);
//        		$output_size_y = ImageSY($phpThumb->gdimg_output);
            if (
                $output_filename 
                || $options['captureRawData']
            ) {
                if (
                    $options['captureRawData'] 
                    && $phpThumb->RenderOutput()
                ) {
                    // RenderOutput renders the thumbnail data to $phpThumb->outputImageData, not to a file or the browser
                    DB::query("INSERT INTO `table` (`thumbnail`) VALUES ('".DB::escape($phpThumb->outputImageData)."') WHERE (`id` = '".$id."')");
                } elseif ($phpThumb->RenderToFile($output_filename)) {
                    // do something on success

                } else {
                    // do something with debug/error messages
                    echo 'Failed (size='.$thumbnail_width.'):<pre>'.implode("\n\n", $phpThumb->debugmessages).'</pre>';
                }
                $phpThumb->purgeTempFiles();
            } else {
                $phpThumb->OutputThumbnail();
            }
        } else {
            // do something with debug/error messages
            echo 'Failed (size='.$thumbnail_width.').<br>';
            echo '<div style="background-color:#FFEEDD; font-weight: bold; padding: 10px;">'.$phpThumb->fatalerror.'</div>';
            echo '<form><textarea rows="10" cols="60" wrap="off">'.htmlentities(implode("\n* ", $phpThumb->debugmessages)).'</textarea></form><hr>';
        }
    }
}
