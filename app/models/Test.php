<?php

class Test extends Model {
    protected $table = 'run_tests';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'key','autoIncrement' => true),
        'name' => array('type' => 'varchar', 'default' => null),
        'image_a' => array('type' => 'varchar', 'default' => null),
        'image_b' => array('type' => 'varchar', 'default' => null),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
    protected $fileFields = array(
        'image_a' => array(
            'path' => 'image_a',
            'extension' => 'jpg',
            'quality' => 85,
            'variants' => array(
                'small' => array(
                    'path' => 'small',
                    'fit' => array(85, 134),
                ),
                'large' => array(
                    'path' => 'large',
                    'fit' => array(200, 315),
                ),
            ),
        ),
        'image_b' => array(
            'path' => 'image_b',
        ),
    );
        
    //
    // CESAB
    //
    
    protected $store = null;
    protected $rangeStore = null;
    // url to login unto customer zone
    protected $loginUrl = 'http://www.cesab.it/shopcesab/abilita-EN.xml?home=www.cesab.it&username=Maklary&password=0ef9c542';
    // url with list of cesab manuals
    protected $listUrl = 'http://www.cesab.it/shopcesab/cesab/start-EN.xml';
    protected $rangeBaseUrl = 'http://www.cesab.it/shopcesab/cesab/';
    protected $imageBaseUrl = 'http://www.cesab.it/shopcesab/immagini/';
    protected $sectionBaseUrl = 'http://www.cesab.it/shopcesab/cesab/';
    protected $pdfBaseUrl = 'http://www.cesab.it/';
    protected $htmlTemplate = null;
    protected $rangeIndex = null;
    protected $rangeStartIndex = null;
    protected $sectionStartIndex = null;
    protected $microtimeReserve = null;
    
    /**
     * 
     * 
     *  http://www.cesab.it/gb/sol/sol.html 
     *  user:Maklary
     *  pwd: 0ef9c542
     * 
     * @param string $startIndexes String returned in case that proces is unfinished
     *      because of exec time limit. It should be passed here to allow the method
     *      skip what has been already done.
     * @return boolean|string FALSE on failure, TRUE on finishing process, string
     *      containing start indexes of manual and pdf file if process is left unfinished
     *      because of exec time limit
     */
    public function downloadCesabManuals($startIndexes = null) {
        ini_set('memory_limit', '512M');
        set_time_limit(1200);
        $this->microtimeReserve = 300000;
        $limit = -1; //debug: limit of ranges to be downloaded, use -1 to turn off limit
        
        // initialize
        $this->store = ROOT . DS . 'tmp' . DS . 'cesab';
        $this->htmlTemplate = 
              '<!doctype html>'
            . '<html lang="en">'
                . '<head>'
                    . '<meta charset="utf-8">'
                    . '<title>:title:</title>'
                    . '<link rel="stylesheet"  type="text/css" href="../styles.css" />'
                    . '<script type="text/javascript" src="../functions.js"></script>'
                . '</head>'
                . '<body>'
                    . '<h1>:header:</h1>'
                    . '<div class="content">:content:</div>'
                    . '<div class="images">:images:</div>'
                    . '<!-- :data: -->'
                . '</body>'
            . '</html>';
        
        // get manual and pdf start indexes
        $this->rangeStartIndex = 0;
        $this->sectionStartIndex = 0;
        if ($startIndexes) {
            $startIndexes = explode('-', $startIndexes);
            $this->rangeStartIndex = (int)$startIndexes[0];
            $this->sectionStartIndex = (int)$startIndexes[1];
        }
        
//        echo $_SERVER['HTTP_USER_AGENT'];
//        return;
        
        // login
        $loginContent = App::request($this->loginUrl, array('persist' => true));
////this does not works - try to debug that, returns: '<h1>Length Required</h1>'        
//        $loginContent = App::request('http://www.cesab.it/shopcesab/abilita-EN.xml?home=www.cesab.it', array(
//            'method' => 'post',
//            'data' => array(
//                'username' => 'Maklary',
//                'password' => '0ef9c542',
//            ),
//            'persist' => true
//        ));
        //App::log('manuals', '$loginContent', array('var' => $loginContent));
        
        // read list of ranges
        $listContent = App::request($this->listUrl, array('persist' => true));
        
        if (empty($listContent)) {
            App::setErrorMessage(Str::fill('Invalid list url'));
            App::log('manuals', 'ERROR: ' . Str::fill('Invalid list url'));
            return false;
        }
        
        // retrieve product ranges
        preg_match_all(
                '#<option[^>]+value="([^"]+)"[^>]*>([^<]+)#i',
                $listContent,
                $rangeMatches,
                PREG_SET_ORDER
        );
        //App::log('manuals', '$rangeMatches', array('var' => $rangeMatches));
        
        // reorder range matches according names
        $tmp = array();
        foreach($rangeMatches as $rangeMatch) {
            $tmp[$rangeMatch[2]] = $rangeMatch;
        }
        ksort($tmp);
        $rangeMatches = $tmp;
        unset($tmp);
        //App::log('manuals', '$rangeMatches', array('var' => array_values($rangeMatches)));
        
        // create ranges index.html
        $indexFile = $this->store . DS . 'index.html';
        if (!is_readable($indexFile)) {
            $contentHtml = '';
            $rangeNames = array_keys($rangeMatches);
            //App::log('manuals', '$rangeNames', array('var' => $rangeNames));
            foreach($rangeNames as $rangeName) {
                $rangeUrlPath = Str::slugize($rangeName, array('separator' => '_')) . '/index.html';
                $contentHtml .= '<a href="' . $rangeUrlPath . '">' . $rangeName . '</a>';
            }
            $indexHtml = Str::fill($this->htmlTemplate, array(
                'title' => 'Cesab katalóg',
                'header' => 'Cesab katalóg',
                'content' => $contentHtml,
                'images' => '',
                'data' => json_encode($rangeMatches),
            ));
            if (file_put_contents($indexFile, $indexHtml) === false) {
                App::setErrorMessage(Str::fill('Catalog index file creation failure'));
                App::log('manuals', 'ERROR: ' . Str::fill('Catalog index file creation failure'));
                return false;
            }
        }
        
        // read range catalogues one by one
        $this->rangeIndex = -1;
        foreach($rangeMatches as $rangeName => $rangeMatch) {
            $this->rangeIndex++;
            // check the time
            if (App::getFreeMicrotime() < $this->microtimeReserve) {
                App::log('manuals', Str::fill('REDIRECT on begining of range %s (%s)', $rangeName, $this->rangeIndex));
                App::redirect('/mvc/App/Tests/test/' . $this->rangeIndex . '-0', array(
                    'loopSafe' => false,
                ));
                return $this->rangeIndex . '-0';
            }
            // skip range if already processed
            if ($this->rangeIndex < $this->rangeStartIndex) {
                continue;
            }
            if ($limit-- === 0) {
                return;
            }
            App::log('manuals', 'Reading range ' . $rangeName);
            
            // read range page
            $rangeUrl = $this->rangeBaseUrl . str_replace(' ', '%20', $rangeMatch[1]);
            $rangeContent = App::request($rangeUrl, array('persist' => true));
            //App::log('manuals', '$rangeContent', array('var' => $rangeContent));
            
            // create range directory
            $rangeSlug = Str::slugize($rangeName, array('separator' => '_'));
            $this->rangeStore = $this->store . DS . $rangeSlug;
            if (!is_readable($this->rangeStore)) {
                File::ensurePath($this->rangeStore, true);
            }
            
            // retrieve range sections
            if (
                preg_match_all(
                    '#<img[^>]+src="([^"]+)"[^>]*></a></td><td[^>]+class="Category"[^>]*><a[^>]+href="([^"]+)"[^>]*>([^<]+)#i',
                    $rangeContent,
                    $sectionMatches,
                    PREG_SET_ORDER
                )
            ) {
                //App::log('manuals', '$sectionMatches', array('var' => $sectionMatches));                
                // create section index.html
                $indexFile = $this->rangeStore . DS . 'index.html';
                if (!is_readable($indexFile)) {
                    $contentHtml = '';
                    foreach($sectionMatches as $sectionIndex => $sectionMatch) {
                        $sectionNumber = explode('/', $sectionMatch[1]);
                        $sectionNumber = end($sectionNumber);
                        $sectionNumber = explode('-', $sectionNumber);
                        $sectionNumber = ltrim(reset($sectionNumber), 0);
                        $sectionName = $sectionNumber . ' - ' . $sectionMatch[3];
                        $sectionUrlPath = Str::slugize($sectionName, array('separator' => '_')) . '.html';
                        $contentHtml .= '<a href="' . $sectionUrlPath . '">' . $sectionName . '</a>';
                    }
                    // get range images
                    $imagesHtml = '';
                    if (
                        preg_match_all(
                            '#<a[^>]+href="javascript:OpenDettaglio\(\'([^\']+)\'[^>]*>#i',
                            $rangeContent,
                            $imageMatches,
                            PREG_SET_ORDER
                        )
                    ) {
                        //App::log('manuals', '$imageMatches', array('var' => $imageMatches));
                        foreach ($imageMatches as $imageIndex => $imageMatch) {
                            $imageUrl = $this->imageBaseUrl . str_replace(' ', '%20', $imageMatch[1]);
                            $imageName = Sanitize::filename($rangeSlug . '-' . $imageIndex . '.' . File::getPathinfo($imageMatch[1], PATHINFO_EXTENSION));
                            if (file_exists($this->rangeStore . DS . $imageName)) {
                                continue;
                            }
                            try {
                                if (
                                    !($localImageName = File::transfer(array($imageUrl), File::getRelativePath($this->rangeStore), array(
                                        'name' => $imageName,
                                        'unique' => true,
                                    )))
                                ) {
                                    App::log('manuals', 'ERROR: ' . Str::fill('Download of image %s for %s range home has failed', $imageUrl, $rangeName));
                                    continue;
                                }
                            } 
                            catch (Throwable $ex) {
                                    App::log('manuals', 'ERROR: ' . Str::fill('Download of image %s for %s range home has failed', $imageUrl, $rangeName));
                                    continue;
                            }
                            $imagesHtml .= '<img src="' . $localImageName . '" onclick="_imgOnClick(this)" onmouseover="_imgOnHover(this)">'
                                . '<a href="' . $localImageName . '" target="_blank">Zobraz</a>';
                        }
                    }
                    else {
                        App::log('manuals', 'ERROR: ' . Str::fill('No images found on page for %s: %s', $rangeName, $rangeUrl));
                    }
                    $indexHtml = Str::fill($this->htmlTemplate, array(
                        'title' => $rangeName,
                        'header' => $rangeName,
                        'content' => $contentHtml,
                        'images' => $imagesHtml,
                        'data' => json_encode(array(
                            'sectionMatches' => $sectionMatches,
                            'imageMatches' => $imageMatches,
                        )),
                    ));
                    if (file_put_contents($indexFile, $indexHtml) === false) {
                        App::setErrorMessage(Str::fill('Range %s index file creation failure', $rangeName));
                        App::log('manuals', 'ERROR: ' . Str::fill('Range %s index file creation failure', $rangeName));
                        return false;
                    }
                }
                
                $this->downloadSection($rangeName, $sectionMatches);
            }
            // if no sections found then check for entire .pdf (in iframe)
            elseif (
                preg_match_all(
                    '#<iframe[^>]+src="([^"]+)"[^>]*>#i',
                    $rangeContent,
                    $pdfMatches,
                    PREG_SET_ORDER
                )
            ) {
                App::log('manuals', Str::fill('Pdf(s) found on page for %s: %s', $rangeName, $rangeUrl));
                //App::log('manuals', '$pdfMatches', array('var' => $pdfMatches));
                foreach ($pdfMatches as $pdfIndex => $pdfMatch) {
                    $pdfUrl = $this->pdfBaseUrl . ltrim(str_replace(' ', '%20', $pdfMatch[1]), '/');
                    if (file_exists($this->rangeStore . DS . $rangeSlug . '.pdf')) {
                        continue;
                    }
                    try {
                        if (
                            !($localPdfName = File::transfer(array($pdfUrl), File::getRelativePath($this->rangeStore), array(
                                'name' => $rangeSlug,
                                'unique' => false,
                            )))
                        ) {
                            App::setErrorMessage(Str::fill('Range %s pdf file %s creation failure', $rangeName, $pdfUrl));
                            App::log('manuals', 'ERROR: ' . Str::fill('Range %s pdf file %s creation failure', $rangeName, $pdfUrl));
                        }
                    } 
                    catch (Throwable $ex) {
                        App::setErrorMessage(Str::fill('Range %s pdf file %s creation failure', $rangeName, $pdfUrl));
                        App::log('manuals', 'ERROR: ' . Str::fill('Range %s pdf file %s creation failure', $rangeName, $pdfUrl));
                    }
                }
            }
            elseif (
                strpos($rangeContent, 'ON GOING')
            ) {
                $contentHtml = 'PRIPRAVUJE SA :(';
                App::log('manuals', 'ONGOING: ' . Str::fill('%s: %s', $rangeName, $rangeUrl));
            }
            else {
                App::log('manuals', 'ERROR: ' . Str::fill('No sections or pdf found on page for %s: %s', $rangeName, $rangeUrl));
                continue;
            }
        }
        return true;
    }    
    
    protected function downloadSection($rangeName, $sectionMatches, $sectionNamePrefix = '') {
        // read sections one by one (create pages and download images)
        foreach($sectionMatches as $sectionIndex => $sectionMatch) {
            // check the time (but only on top level)
            if (
                empty($sectionNamePrefix)
                && App::getFreeMicrotime() < $this->microtimeReserve
            ) {
                App::log('manuals', Str::fill('REDIRECT on range %s (%s) - section %s (%s)', $rangeName, $this->rangeIndex, $sectionMatch[3], $sectionIndex));
                App::redirect('/mvc/App/Tests/test/' . $this->rangeIndex . '-' . $sectionIndex, array(
                    'loopSafe' => false,
                ));
                return $this->rangeIndex . '-' . $sectionIndex;
            }
            // skip section if already processed in already processed range (but only on top level)
            if (
                empty($sectionNamePrefix)
                && $this->rangeIndex <= $this->rangeStartIndex
                && $sectionIndex < $this->sectionStartIndex
            ) {
                continue;
            }
            
            $sectionNumber = explode('/', $sectionMatch[1]);
            $sectionNumber = end($sectionNumber);
            $sectionNumber = explode('-', $sectionNumber);
            $sectionNumber = ltrim(reset($sectionNumber), 0);
            $sectionName = $sectionNamePrefix . $sectionNumber . ' - ' . $sectionMatch[3];
            $sectionSlug = Str::slugize($sectionName, array('separator' => '_'));
            $sectionFile = $this->rangeStore . DS . $sectionSlug . '.html';
            $sectionUrl = $this->sectionBaseUrl . str_replace(' ', '%20', $sectionMatch[2]);
            $sectionContent = App::request($sectionUrl, array('persist' => true));
            App::log('manuals', 'Reading section ' . $sectionName);

            // check if we are on section end page or on subsection page
            $endPage = strpos($sectionContent, '<!--  **start "Product_list" ** -->') !== false;
            // for end page take existing content
            if ($endPage) {
                $contentHtml = preg_split('#<!--  \*\*(start|end) "Product_list" \*\* -->#i', $sectionContent);
                $contentHtml = $contentHtml[1];
                // replace image of group
                $contentHtml = preg_replace('#<img[^>]+src="[^"]+/shopcesab/cesab/images/0\.gif[^"]*"[^>]*>#i', '', $contentHtml);
                $contentHtml = preg_replace('#<img[^>]+src="[^"]+/shopcesab/cesab/images/1\.gif[^"]*"[^>]*>#i', '<img src="../complete.gif">', $contentHtml);
                $contentHtml = preg_replace('#<img[^>]+src="[^"]+/shopcesab/cesab/images/2\.gif[^"]*"[^>]*>#i', '<img src="../group.gif">', $contentHtml);
                $contentHtml = preg_replace('#<img[^>]+src="[^"]+/shopcesab/cesab/images/3\.gif[^"]*"[^>]*>#i', '<img src="../part.gif">', $contentHtml);
            }
            // for subsection - retrieve them and generate index content
            elseif (
                preg_match_all(
                    '#<img[^>]+src="([^"]+)"[^>]*></a></td><td[^>]+class="Category"[^>]*><a[^>]+href="([^"]+)"[^>]*>([^<]+)#i',
                    $sectionContent,
                    $subsectionMatches,
                    PREG_SET_ORDER
                )
            ) {
                $contentHtml = '';
                foreach($subsectionMatches as $subsectionIndex => $subsectionMatch) {
                    $subsectionNumber = explode('/', $subsectionMatch[1]);
                    $subsectionNumber = end($subsectionNumber);
                    $subsectionNumber = explode('-', $subsectionNumber);
                    $subsectionNumber = ltrim(reset($subsectionNumber), 0);
                    $subsectionName = $sectionNamePrefix . $sectionNumber . '_' . $subsectionNumber . ' - ' . $subsectionMatch[3];
                    $subsectionUrlPath = Str::slugize($subsectionName, array('separator' => '_')) . '.html';
                    $contentHtml .= '<a href="' . $subsectionUrlPath . '">' . $subsectionName . '</a>';
                }
            }       
            // if no subsections found then check for entire .pdf (in iframe)
            elseif (
                preg_match_all(
                    '#<iframe[^>]+src="([^"]+)"[^>]*>#i',
                    $sectionContent,
                    $pdfMatches,
                    PREG_SET_ORDER
                )
            ) {
                App::log('manuals', Str::fill('Pdf(s) found on page for %s: %s', $rangeName, $rangeUrl));
                //App::log('manuals', '$pdfMatches', array('var' => $pdfMatches));
                foreach ($pdfMatches as $pdfIndex => $pdfMatch) {
                    $pdfUrl = $this->pdfBaseUrl . ltrim(str_replace(' ', '%20', $pdfMatch[1]), '/');
                    if (file_exists($this->rangeStore . DS . $sectionSlug . '.pdf')) {
                        continue;
                    }
                    try {
                        if (
                            !($localPdfName = File::transfer(array($pdfUrl), File::getRelativePath($this->rangeStore), array(
                                'name' => $sectionSlug,
                                'unique' => false,
                            )))
                        ) {
                            App::setErrorMessage(Str::fill('Range %s pdf file %s creation failure', $rangeName, $pdfUrl));
                            App::log('manuals', 'ERROR: ' . Str::fill('Range %s pdf file %s creation failure', $rangeName, $pdfUrl));
                        }
                    } 
                    catch (Throwable $ex) {
                        App::setErrorMessage(Str::fill('Range %s pdf file %s creation failure', $rangeName, $pdfUrl));
                        App::log('manuals', 'ERROR: ' . Str::fill('Range %s pdf file %s creation failure', $rangeName, $pdfUrl));
                    }
                }
                continue;
            }
            // on going page
            elseif (
                strpos($sectionContent, 'ON GOING')
            ) {
                $contentHtml = 'PRIPRAVUJE SA :(';
                App::log('manuals', 'ONGOING: ' . Str::fill('%s: %s', $rangeName . ' > ' . $sectionName, $sectionUrl));
            }
            // page with only image(s)
            elseif (
                preg_match_all(
                    '#<a[^>]+href="javascript:OpenDettaglio\(\'([^\']+)\'[^>]*>#i',
                    $sectionContent,
                    $imageMatches,
                    PREG_SET_ORDER
                )
            ) {
                $contentHtml = '';
                App::log('manuals', 'ONLYIMAGE: ' . Str::fill('%s: %s', $rangeName . ' > ' . $sectionName, $sectionUrl));
            }
            else {
                App::log(
                    'manuals', 
                    'ERROR: ' . Str::fill('Invalid (neither end page nor subsection) section page for %s: %s', $rangeName . ' > ' . $sectionName, $sectionUrl)
                );
                continue;
            }

            // get section images
            $imagesHtml = '';
            if (
                preg_match_all(
                    '#<a[^>]+href="javascript:OpenDettaglio\(\'([^\']+)\'[^>]*>#i',
                    $sectionContent,
                    $imageMatches,
                    PREG_SET_ORDER
                )
            ) {
                //App::log('manuals', '$imageMatches', array('var' => $imageMatches));
                foreach ($imageMatches as $imageIndex => $imageMatch) {
                    $imageUrl = $this->imageBaseUrl . str_replace(' ', '%20', $imageMatch[1]);
                    $imageName = Sanitize::filename($sectionSlug . '-' . $imageIndex . '.' . File::getPathinfo($imageMatch[1], PATHINFO_EXTENSION));
                    if (file_exists($this->rangeStore . DS . $imageName)) {
                        continue;
                    }
                    try {
                        if (
                            !($localImageName = File::transfer(array($imageUrl), File::getRelativePath($this->rangeStore), array(
                                'name' => $imageName,
                                'unique' => true,
                            )))
                        ) {
                            App::log('manuals', 'ERROR: ' . Str::fill('Download of image %s for %s section has failed', $imageUrl, $rangeName . ' > ' . $sectionName));
                            continue;
                        }
                    } 
                    catch (Throwable $ex) {
                        App::log('manuals', 'ERROR: ' . Str::fill('Download of image %s for %s section has failed', $imageUrl, $rangeName . ' > ' . $sectionName));
                        continue;
                    }
                    $imagesHtml .= '<img src="' . $localImageName . '" onclick="_imgOnClick(this)" onmouseover="_imgOnHover(this)">'
                        . '<a href="' . $localImageName . '" target="_blank">Zobraz</a>';
                }
            }
            else {
                App::log('manuals', 'ERROR: ' . Str::fill('No images found on page for %s: %s', $rangeName . ' > ' . $sectionName, $sectionUrl));
            }
            if (!is_readable($sectionFile)) {
                $sectionHtml = Str::fill($this->htmlTemplate, array(
                    'title' => $rangeName . ' > ' . $sectionName,
                    'header' => $rangeName . ' > ' . $sectionName,
                    'content' => $contentHtml,
                    'images' => $imagesHtml,
                    'data' => json_encode(array(
                        'subsectionMatches' => @$subsectionMatches,
                        'imageMatches' => @$imageMatches,
                    )),
                ));
                if (file_put_contents($sectionFile, $sectionHtml) === false) {
                    App::setErrorMessage(Str::fill('Section %s file creation failure', $sectionFile));
                    App::log('manuals', 'ERROR: ' . Str::fill('Section %s file creation failure', $sectionFile));
                    return false;
                }
            }
            if (!$endPage && !empty($subsectionMatches)) {
                $this->downloadSection($rangeName . ' > ' . $sectionName, $subsectionMatches, $sectionNamePrefix . $sectionNumber . '_');
            }
        }      
    }
}

