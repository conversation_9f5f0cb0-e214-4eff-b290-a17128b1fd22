<?php
class User extends Model {

    protected $table = 'run_users';
    
    protected $primaryKey = 'id';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_groups_id' => array('type' => 'int', 'index' => 'index'),
        'username' => array('type' => 'varchar', 'length' => 100),
        'password' => array('type' => 'varchar', 'length' => 128, 'comment' => 'Length 128 is set according to the hash length'),
        'first_name' => array('type' => 'varchar', 'length' => 100),
        'last_name' => array('type' => 'varchar', 'length' => 100),
        'email' => array('type' => 'varchar', 'length' => 100),
        'rights' => array('type' => 'text', 'default' => null, 'comment' => 'Json encoded array of user specific rights'),
        'active' => array('type' => 'bool', 'default' => 1),
        'reset_code' => array('type' => 'varchar', 'length' => 32, 'default' => null),
        'reset_code_created' => array('type' => 'datetime', 'default' => null),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(
            'username' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím užívateľské meno'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím užívateľské meno'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Username must be unique'),
                ),
            ),
            'password' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím heslo'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'on' => 'create',
                    'message' => __v(__FILE__, 'Zadajte prosím heslo'),
                ),
                array(
                    'rule' => array('minLength', 8),
                    'message' => __v(__FILE__, 'Minimum length is %s characters', 8),
                ),
            ),
            'password2' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím potvrdenie hesla'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím potvrdenie hesla'),
                    'on' => 'create',
                ),
                array(
                    'rule' => array('equalsToField', 'password'),
                    'message' => __v(__FILE__, 'Passwords do not match'),
                    'force' => true
                ),
            ),
            'email' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím e-mail'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím e-mail'),
                ),
                array(
                    'rule' => 'email',
                    'message' => __v(__FILE__, 'E-mail is not valid'),
                ),
                array(
                    'rule' => array('unique', array(), true),
                    'message' => __v(__FILE__, 'E-mail must be unique'),
                ),
            ),
            'first_name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím meno'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím meno'),
                ),
                array(
                    'rule' => 'humanName',
                    'message' => __v(__FILE__, 'Zadajte prosím meno'),
                    'last' => true,
                ),
            ),
            'last_name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím priezvisko'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím priezvisko'),
                ),
                array(
                    'rule' => 'humanName',
                    'message' => __v(__FILE__, 'Zadajte prosím priezvisko'),
                    'last' => true,
                ),
            ),
        );
    }
        
    public function normalize($data, $options = array()) {
        $defaults = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defaults, $options);
        
        // sanitize html to text (remove HTML tags) to avoid XSS attacks
        foreach ($this->getFields() as $field) {
            if (
                isset($data[$field])
                && is_string($data[$field])
                // avoid following fields
                && $field !== 'id'
                && $field !== 'password'
            ) {
                $data[$field] = Sanitize::htmlTags($data[$field]);
            }
        }
        
        if (!empty($data['email'])) {
            $data['email'] = App::normalizeUsername($data['email']);
        }
        
        // on frontend
        if (in_array('frontend', (array)$options['alternative'])) {
            // set username to email
            if (!empty($data['email'])) {
                $data['username'] = Sanitize::value($data['email']);
            }
            // remove run_groups_id
            unset($data['run_groups_id']);
            // remove user id on frontend create
            if ($options['on'] === 'create') {
                unset($data['id']);
            }
            // set explicit user id on frontend update
            else {
                $data['id'] = App::getUser('id');
            }
        }
        
        if (!empty($data['username'])) {
            $data['username'] = App::normalizeUsername($data['username']);
        }
        
        // remove empty password
        if (empty($data['password'])) {
            unset($data['password']);
            unset($data['password2']);
        }
        
        return parent::normalize($data, $options);
    }
    
    public function updateRights($id, $rights) {        
        DB::reserveTables('User_updateRights', $this->table);
        // get editor and user data
        $editor = App::getUser();
        $editorGroup = $editor['Group']['pid'];
        $editorCustomRights = (array)$editor['rights'];
        $user = $this->findFirstBy('User.id', $id, array(
            'joins' => array(
                array(
                    'model' => 'Group',
                    'type' => 'left',
                ),
            ),
            'fields' => array(
                'User.rights',
                'Group.pid',
                'Group.hierarchy',
            ),
            'separate' => array('Group'),
        ));
        // if no editor or no user or if editor has no rights to edit this user then return false
        if (
            empty($editor)
            || empty($user)
            || $user['Group']['hierarchy'] <= $editor['Group']['hierarchy']
        ) {
            return false;
        }
        // get user data
        $userGroup = $user['Group']['pid'];
        $userCustomRights = array();
        if (!empty($user['rights'])) {
            $userCustomRights = (array)json_decode($user['rights'], true);
        }
        
        // normalize data:
        //  - convert subjectNameField to subjectName (replace '/' by '.')
        //  - remove all items the editor has nor rigths for 
        $tmp = array();
        foreach ($rights as $module => $subjectTypes) {
            // read rights
            try {
                $moduleRights = App::getModuleRights($module);
            } 
            // if module has no rights defined then continue to next one
            catch (Throwable $e) {
                continue;
            }
            // - editors rights defines rights range that can be edited by actual authenticated user (editor)
            $editorRights = Arr::mergeRecursive(
                (array)Sanitize::value($moduleRights[$editorGroup]),
                (array)Sanitize::value($editorCustomRights[$module])
            );
            // - user group rights defines which rights are default (implicit)
            $userGroupRights = (array)Sanitize::value($moduleRights[$userGroup]);
            foreach ($subjectTypes as $subjectType => $subjectNameFields) {
                foreach ($subjectNameFields as $subjectNameField => $hasRights) {
                    $subjectName = str_replace('/', '.', $subjectNameField);
                    // if editor has (explicitly) no rights for this subjectName or
                    // both editor has no rights and user has no implicit (group) rights
                    // then skip it. This should not normally happen but do not trust to anything comming from web
                    // NOTE: If you change this condition then change it also in Users::admin_editRights()
                    if (
                        empty($editorRights[$subjectType][$subjectName])
                        &&
                        (
                            isset($editorCustomRights[$module][$subjectType][$subjectName]) // = explicitly
                            || empty($userGroupRights[$subjectType][$subjectName])
                        )
                    ) {
                        continue;
                    }
                    $tmp[$module][$subjectType][$subjectName] = (bool)$hasRights;
                }
            }
        }
        $rights = $tmp;
        
        // merge new rights with existing
        $rights = Arr::mergeRecursive($userCustomRights, $rights);
        
        // remove from rights all which are default (means inherited from group rights)
        $tmp = array();
        foreach ($rights as $module => $subjectTypes) {
            // read rights
            try {
                $moduleRights = App::getModuleRights($module);
            } 
            catch (Throwable $e) {
                continue;
            }
            // get user group rights
            $userGroupRights = (array)Sanitize::value($moduleRights[$userGroup]);
            foreach ($subjectTypes as $subjectType => $subjectNames) {
                foreach ($subjectNames as $subjectName => $hasRights) {
                    // skip items which are defined in same manner in the user group
                    if (
                        isset($userGroupRights[$subjectType][$subjectName])
                        && $userGroupRights[$subjectType][$subjectName] === $hasRights
                        ||
                        empty($userGroupRights[$subjectType][$subjectName])
                        && $hasRights === false
                    ) {
                        continue;
                    }
                    $tmp[$module][$subjectType][$subjectName] = $hasRights;
                }
            }
        }
        $rights = $tmp;
        
        // normalize rigths value for db
        if (empty($rights)) {
            $rights = null;
        }
        else {
            $rights = json_encode($rights);
        }
        $this->updateBy('id', $id, array('rights' => $rights), array('validate' => false));
        
        DB::unreserveTables('User_updateRights');
        return true;     
    }
    
    /**
     * Creates new user and its profile
     * 
     * @param array $data User and user profile data contaning keys 'User' and
     *      'UserProfile' under which the respective models data are stored.
     * @param array $options Following options can be used:
     *          - 'login' (bool) If TRUE then the new registered user is also logged in. 
     *              Defaults to FALSE.
     *          - 'alternative' (string|array) Alternative(s) which should be used
     *              to normalize and validate data. Defaults to NULL.
     *          - 'groupPid' (string) Pid of group the new user should be created in.
     *              If omitted then run_users_id passed in data is used. Defaults to NULL.
     * 
     * @return bool TRUE on success. FALSE on validation error or other processing failure.
     *      Processing failures are stored under User::$errors['_processing']
     */
    public function createWithProfile($data, $options = array()) {
        $defaults = array(
            'login' => false,
            'alternative' => null,
            'groupPid' => null,
        );
        $options = array_merge($defaults, $options);
        
        $UserProfile = $this->loadModel('UserProfile', true);
                
        // normalize data structure
        $data['User'] = (array)Sanitize::value($data['User']);
        $data['UserProfile'] = (array)Sanitize::value($data['UserProfile']);
        
        // normalize and validate User data
        $data['User'] = $this->normalize($data['User'], array(
            'alternative' => $options['alternative'],
            'on' => 'create'
        ));
        $userIsValid = $this->validate($data['User'], array(
            'alternative' => $options['alternative'],
            'on' => 'create',
            'normalize' => false,
        ));
        // normalize and validate UserProfile data if required
        $data['UserProfile'] = $UserProfile->normalize($data['UserProfile'], array(
            'alternative' => $options['alternative'],
            'on' => 'create'
        ));
        $profileIsValid = $UserProfile->validate($data['UserProfile'], array(
            'alternative' => $options['alternative'],
            'on' => 'create',
            'normalize' => false,
        ));
        
        if (!$userIsValid || !$profileIsValid) {
            // set following messages to password and password2 to make it more comprehensible
            // for the user that he must enter them on each new for submit
            if (!$this->getErrors('password')) {
                $this->setError('password', __v(__FILE__, 'Zadajte prosím znovu'));
            }
            if (!$this->getErrors('password2')) {
                $this->setError('password2', __v(__FILE__, 'Zadajte prosím znovu'));
            }
            return false;
        }
        
        // keep password for possible login and hash it 
        $password = $data['User']['password'];
        $data['User']['password'] = App::hash($data['User']['password']);
        try {
            $this->reserveTables('User_createWithProfile', array(
                'User',
                'UserProfile',
            )); 
            DB::startTransaction('User_createWithProfile');
            // if group pid is provided then force the run_groups_id
            if (!empty($options['groupPid'])) {
                $Group = $this->loadModel('Group', true);
                $clientGroup = $Group->findFirstBy('pid', $options['groupPid']);
                if (empty($clientGroup)) {
                    throw new Exception(__e(__FILE__, 'Invalid group pid %s', $options['groupPid']));
                }
                $data['User']['run_groups_id'] = $clientGroup['id'];
            }
            if (!$this->save($data['User'], array(
                'normalize' => false, 
                'validate' => false, 
                'create' => true
            ))) {
                throw new Exception(__e(__FILE__, 'User data save has failed'));
            }
            $data['UserProfile']['run_users_id'] = $this->getPropertyId();
            if (!$UserProfile->save($data['UserProfile'], array(
                'normalize' => false, 
                'validate' => false, 
                'create' => true
            ))) {
                throw new Exception(__e(__FILE__, 'User profile data save has failed'));
            }
            if (!empty($data['UserProfile']['newsletters_agreement'])) {
                $MailerContact = App::loadModel('Mailer', 'MailerContact', true);
                $MailerContact->saveFromUser($this->getPropertyId(), $data);
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('User_createWithProfile');
            $this->unreserveTables('User_createWithProfile'); 
            $this->setError($e->getMessage());
            return false;
        }
        DB::commitTransaction('User_createWithProfile');
        $this->unreserveTables('User_createWithProfile'); 
        // log user in
        if ($options['login']) {
            if (!App::login($data['User']['username'], $password)) {
                $this->setError(__e(__FILE__, 'Registration login has failed'));
                return false;
            }
        }
        return true;
    }
    
    /**
     * Updates user and its profile
     * 
     * @param array $data User and user profile data contaning keys 'User' and
     *      'UserProfile' under which the respective models data are stored.
     * @param array $options Following options can be used:
     *          - 'alternative' (string|array) Alternative(s) which should be used
     *              to normalize and validate data. Defaults to NULL.
     *          - 'lang' (string) Defaults to NULL.
     * 
     * @return bool TRUE on success. FALSE on validation error or other processing failure.
     *      Processing failures are stored under User::$errors['_processing']
     */
    public function updateWithProfile($data, $options = array()) {
        $defaults = array(
            'alternative' => null,
            'lang' => true,
        );
        $options = array_merge($defaults, $options);
        
        $UserProfile = $this->loadModel('UserProfile', true);
        $MailerContact = App::loadModel('Mailer', 'MailerContact', true);
        
        // normalize data structure
        $data['User'] = (array)Sanitize::value($data['User']);
        $data['UserProfile'] = (array)Sanitize::value($data['UserProfile']);
        
        // normalize and validate data of User model
        $data['User'] = $this->normalize($data['User'], array(
            'alternative' => $options['alternative'],
        ));
        $userIsValid = $this->validate($data['User'], array(
            'alternative' => $options['alternative'],
            'normalize' => false,
        ));
        // normalize and validate data of UserProfile model
        $data['UserProfile'] = $UserProfile->normalize($data['UserProfile'], array(
            'alternative' => $options['alternative'],
        ));
        $profileIsValid = $UserProfile->validate($data['UserProfile'], array(
            'alternative' => $options['alternative'],
            'normalize' => false,
        ));
        
        if (!$userIsValid || !$profileIsValid) {
            return false;
        }
        
        if (!empty($data['User']['password'])) {
            $data['User']['password'] = App::hash($data['User']['password']);
        }
        
        // save into db
        try {
            $this->reserveTables('User_updateWithProfile', array(
                'User',
                'UserProfile',
            )); 
            DB::startTransaction('User_updateWithProfile');
            if (!$this->save($data['User'], array(
                'normalize' => false,
                'validate' => false,
                'lang' => $options['lang'],
            ))) {
                throw new Exception(__e(__FILE__, 'User data save has failed'));
            }
            // update profile
            if (!$UserProfile->update($data['UserProfile'], array(
                'normalize' => false,
                'validate' => false,
                'avoidFields' => array(
                    'id',
                    'run_users_id',
                ),
                'conditions' => array(
                    'run_users_id' => $data['User']['id']
                ),
                'lang' => $options['lang'],
            ))) {
                throw new Exception(__e(__FILE__, 'User profile data save has failed'));
            }
            // update mailer contact
            $MailerContact->saveFromUser($data['User']['id'], $data);
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('User_updateWithProfile');
            $this->unreserveTables('User_updateWithProfile'); 
            $this->setError($e->getMessage());
            return false;
        }
        DB::commitTransaction('User_updateWithProfile');
        $this->unreserveTables('User_updateWithProfile'); 
        return true;
    }
    
    /**
     * Generates new password for provided username
     * 
     * @param string $username
     * 
     * @return boolean TRUE on success. FALSE on failure.
     * 
     * @throws Exception_User_Unexisting
     * @throws Exception_User_NoEmail
     * @throws Exception_User_InvalidEmail
     * @throws Exception
     */
    public function generateNewPassword($username) {
        $User = $this->loadModel('User', true);
        
        // get User by email
        $user = $User->findFirstBy('username', $username, array(
            'fields' => array(
                'User.id',
                'User.first_name',
                'User.last_name',
                'User.email',
                'UserProfile.salutation'
            ),
            'joins' => array(
                array(
                    'model' => 'UserProfile',
                    'type' => 'left',
                )
            )
        ));
        if (empty($user)) {
            throw new Exception_User_Unexisting();
        }
        
        $email = $username;
        if (!Validate::email($email)) {
            $email = $user['email'];
        }
        if (empty($email)) {
            throw new Exception_User_NoEmail();
        }
        elseif (!Validate::email($email)) {
            throw new Exception_User_InvalidEmail();
        }
        
        // generate password
        $newPassword = Str::getRandom(8);
        
        // save into db 
        $updateData['id'] = $user['id'];
        $updateData['password'] = App::hash($newPassword);
        DB::startTransaction('User_generateNewPassword');
        try {
            $User->save($updateData, array(
                'normalize' => false,
                'validate' => false,
            ));
            // send email
            App::sendEmail(
                $this->loadView('User/forgotPasswordEmail', array('user' => $user, 'newPassword' => $newPassword)), 
                $email, 
                array(
                    'from' => App::getSetting('App', 'email.from'),
                    'subject' => __(__FILE__, 'subject:Forgotten password'),
                )
            );
        } 
        catch (Throwable $e) {
            App::log('forgottenPassword', 'Forgotten password email failure', array(
                'var' => $e,
                'email' => true,
            ));
            DB::rollbackTransaction('User_generateNewPassword');
            return false;
        }
        DB::commitTransaction('User_generateNewPassword');
        return true;
    }
    
    /**
     * Returns a tree-like array of user menu items which can be used for Html::menu()
     * Keys in array are alphabetic A] to avoid conflicts if merged together with other
     * menu items array, B] to avoid creation of SmartAdminLauncher links by Html::menu().
     * 
     * @return array 
     */
    public function getMenuItems() {
        $items = array();
        if (!($user = App::getUser())) {
            $items['a'] = array(
                'b' => array(
                    'label' => __(__FILE__, 'Create account'),
                    'locator' => App::getContentLocatorByPid('App.Users.register'),
                    'passive' => false,
                    'class' => 'user-menu-register',
                ),
                'c' => array(
                    'label' => __(__FILE__, 'Login'),
                    'locator' => App::getContentLocatorByPid('App.Users.login'),
                    'passive' => false,
                    'class' => 'user-menu-login',
                ),
            );
        }
        else {
            $items['a'] = array(
                'b' => array(
                    'label' => $user['first_name'] . ' ' . $user['last_name'],
                    'locator' => null,
                    'passive' => true,
                    'class' => 'user-menu-username',
                ),
            );
            $items['b'] = array(
                'c' => array(
                    'label' => __(__FILE__, 'My account'),
                    'locator' => App::getContentLocatorByPid('App.Users.update'),
                    'passive' => false,
                    'class' => 'user-menu-profile',
                ),
                'd' => array(
                    'label' => __(__FILE__, 'Logout'),
                    'locator' => App::getContentLocatorByPid('App.Users.logout'),
                    'passive' => false,
                    'class' => 'user-menu-logout',
                ),
            );
        }
        return $items;
    }
    
    /**
     * Returns obvious list of options for user selects
     * 
     * @param array $options Model::findList() options
     * 
     * @return array
     */
    public function getSelectList($options = array()) {
        $defaults = array(
            'key' => 'User.id',
            'fields' => array(
                'CONCAT(User.first_name, " ", User.last_name) AS name',
            ),
            'order' => array(
                'CONCAT(User.first_name, " ", User.last_name) ASC'
            ),
            'literals' => array(
                'fields' => array(
                    'CONCAT(User.first_name, " ", User.last_name) AS name',
                ),
                'order' => 'CONCAT(User.first_name, " ", User.last_name) ASC',
            )
        );
        $options = array_merge($defaults, $options);
        return $this->findList($options);
    }
}

// EXCEPTIONS
class Exception_User_Unexisting extends Exception {}
class Exception_User_NoEmail extends Exception {}
class Exception_User_InvalidEmail extends Exception {}
