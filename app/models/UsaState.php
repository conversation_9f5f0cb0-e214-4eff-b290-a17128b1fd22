<?php
/**
 * States of USA 
 */
class UsaState extends Model {
    
    protected $table = 'run_usa_states';
    
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'code' => array('type' => 'char', 'length' => 2, 'index' => 'index', 'State postal code/abbreviation'),
        'name' => array('type' => 'varchar', 'length' => 20),
    );
    
    
    public function getInitialRecords() {
        return array(
            array('id' => 1, 'code' => 'AL', 'name' => 'Alabama'),
            array('id' => 2, 'code' => 'AK', 'name' => 'Alaska'),
            array('id' => 3, 'code' => 'AZ', 'name' => 'Arizona'),
            array('id' => 4, 'code' => 'AR', 'name' => 'Arkansas'),
            array('id' => 5, 'code' => 'CA', 'name' => 'California'),
            array('id' => 6, 'code' => 'CO', 'name' => 'Colorado'),
            array('id' => 7, 'code' => 'CT', 'name' => 'Connecticut'),
            array('id' => 8, 'code' => 'DE', 'name' => 'Delaware'),
            array('id' => 9, 'code' => 'FL', 'name' => 'Florida'),
            array('id' => 10, 'code' => 'GA', 'name' => 'Georgia'),
            array('id' => 11, 'code' => 'HI', 'name' => 'Hawaii'),
            array('id' => 12, 'code' => 'ID', 'name' => 'Idaho'),
            array('id' => 13, 'code' => 'IL', 'name' => 'Illinois'),
            array('id' => 14, 'code' => 'IN', 'name' => 'Indiana'),
            array('id' => 15, 'code' => 'IA', 'name' => 'Iowa'),
            array('id' => 16, 'code' => 'KS', 'name' => 'Kansas'),
            array('id' => 17, 'code' => 'KY', 'name' => 'Kentucky'),
            array('id' => 18, 'code' => 'LA', 'name' => 'Louisiana'),
            array('id' => 19, 'code' => 'ME', 'name' => 'Maine'),
            array('id' => 20, 'code' => 'MD', 'name' => 'Maryland'),
            array('id' => 21, 'code' => 'MA', 'name' => 'Massachusetts'),
            array('id' => 22, 'code' => 'MI', 'name' => 'Michigan'),
            array('id' => 23, 'code' => 'MN', 'name' => 'Minnesota'),
            array('id' => 24, 'code' => 'MS', 'name' => 'Mississippi'),
            array('id' => 25, 'code' => 'MO', 'name' => 'Missouri'),
            array('id' => 26, 'code' => 'MT', 'name' => 'Montana'),
            array('id' => 27, 'code' => 'NE', 'name' => 'Nebraska'),
            array('id' => 28, 'code' => 'NV', 'name' => 'Nevada'),
            array('id' => 29, 'code' => 'NH', 'name' => 'New Hampshire'),
            array('id' => 30, 'code' => 'NJ', 'name' => 'New Jersey'),
            array('id' => 31, 'code' => 'NM', 'name' => 'New Mexico'),
            array('id' => 32, 'code' => 'NY', 'name' => 'New York'),
            array('id' => 33, 'code' => 'NC', 'name' => 'North Carolina'),
            array('id' => 34, 'code' => 'ND', 'name' => 'North Dakota'),
            array('id' => 35, 'code' => 'OH', 'name' => 'Ohio'),
            array('id' => 36, 'code' => 'OK', 'name' => 'Oklahoma'),
            array('id' => 37, 'code' => 'OR', 'name' => 'Oregon'),
            array('id' => 38, 'code' => 'PA', 'name' => 'Pennsylvania'),
            array('id' => 39, 'code' => 'RI', 'name' => 'Rhode Island'),
            array('id' => 40, 'code' => 'SC', 'name' => 'South Carolina'),
            array('id' => 41, 'code' => 'SD', 'name' => 'South Dakota'),
            array('id' => 42, 'code' => 'TN', 'name' => 'Tennessee'),
            array('id' => 43, 'code' => 'TX', 'name' => 'Texas'),
            array('id' => 44, 'code' => 'UT', 'name' => 'Utah'),
            array('id' => 45, 'code' => 'VT', 'name' => 'Vermont'),
            array('id' => 46, 'code' => 'VA', 'name' => 'Virginia'),
            array('id' => 47, 'code' => 'WA', 'name' => 'Washington'),
            array('id' => 48, 'code' => 'WV', 'name' => 'West Virginia'),
            array('id' => 49, 'code' => 'WI', 'name' => 'Wisconsin'),
            array('id' => 50, 'code' => 'WY', 'name' => 'Wyoming'),
        );
    }
}
