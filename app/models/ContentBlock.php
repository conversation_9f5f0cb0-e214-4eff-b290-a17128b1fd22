<?php
/**
 * ContentBlock model allows to implement content block definitions.
 * 
 * Rendering of content blocks admin and frontend views and relations of content 
 * blocks with owner models are in competence of ContentBlockInstance model so
 * see ContentBlockInstance class phpDoc.
 * 
 * ***********************
 * Add a new content block
 * ***********************
 * 
 * In general, it is the best to create new content block under module ContentBlock!
 * On other side, you can create it under any module, if it is so tight-related block.
 * 
 * You can use misc/templates/ContentBlockNew to create new content block or you 
 * can follow these steps:
 *
 * 1] Create new content block definition model under {Module}/models.
 * Your model must be a child class of ContentBlock class, e.g.:
 * 
 *      App::loadModel('App', 'ContentBlock');
 *      class ContentBlockNew extends ContentBlock {
 * 
 *          protected $adminView = 'ContentBlockNew/admin_view';
 *
 *          protected $view = 'ContentBlockNew/view';
 *
 *          protected $fields = array(
 *              'my_field' => '', // use __a() to translate
 *          );
 * 
 *          public function __construct() {
 *              $this->blockName = __a(__FILE__, 'My new content block');
 *              $this->description = __a(__FILE__, 'Just an example content block');
 *      
 *              $this->validations = array(
 *                  'my_field' => array(
 *                      array(
 *                          'rule' => 'required',
 *                          'message' => __v(__FILE__, 'Enter a value'),
 *                          // see phpDoc of ContentBlock::$fieldsValidationAlternative
 *                          //'alternative' => 'contentBlockFields'
 *                      ),
 *                      array(
 *                          'rule' => 'notEmpty',
 *                          'message' => __v(__FILE__, 'Enter a value'),
 *                          // see phpDoc of ContentBlock::$fieldsValidationAlternative
 *                          //'alternative' => 'contentBlockFields'
 *                      ),
 *                  ),
 *              );
 * 
 *              // call parent constructor at the end
 *              parent::__construct();
 *          }
 * 
 *      }
 * 
 * Set at least properties used in our example. These are all available config 
 * properties and methods:
 * 
 *      - $available             
 *      - $blockName             (mandatory)
 *      - $description           (mandatory)
 *      - $imageUrlPath
 *      - $adminView             (mandatory)
 *      - $view                  (mandatory)
 *      - $fields
 *      - $fileFields
 *      - $validations
 *      - $startModel
 *      - $endModel
 *      - $startDataField
 *      - $pairName
 *      - normalize()
 *      - prepareAdminViewData()
 *      - prepareViewData()
 * 
 * For more details on the above properties and methods see their phpDocs in
 * section 'CONFIG PROPERTIES & METHODS' of ContentBlock class.
 * 
 * As examples see existing content block definitions in app/modules/ContentBlock/models
 * and their views.
 * 
 * For creation of paired blocks see properties $startModel, $endModel, $startDataField
 * and $pairName. See also implementation of models ContentBlockBackgroundStart
 * and ContentBlockBackgroundEnd
 * 
 * 2] Create admin (backend) view on path defined in ContentBlockNew::$adminView. e.g.:
 * 
 *      /* @var $this Template * /
 *      $this->displayOriginComment = true;
 *      /* @var $Form FormHelper * /
 *      $Form = &$this->params['Form'];
 *      echo App::loadView('ContentBlock', 'admin_blockTitleForm', $this->params);
 *      ?><div class="grid-row grid-break-780"><?php 
 *          ?><div class="grid-col grid-col-100-100"><?php 
 *              echo $Form->text('my_field', array(
 *                  'label' => __a(__FILE__, 'My field'),
 *                  'hint' => __a(__FILE__, 'Write here something'),
 *              ));
 *          ?></div><?php
 *      ?></div><?php
 *      echo App::loadView('ContentBlock', 'admin_blockParamsForm', $this->params);
 *      App::setCssFiles(array(
 *          '/app/css/grid.css',
 *          //'/app/modules/{Module}/css/admin.css',
 *      ));
 * 
 * You can use any "css & js methods" of App class in view definition.
 * !!! Only fields defined in ContentBlockNew::$fields are saved !!!
 * There are following items in $this->params: 
 * - 'data' containing view data
 * - 'Form' containing instance of FormHelper class initialized by options 'data', 
 *      'required' and 'errors'. Use only this FormHelper instance to create inputs.
 * 
 * 3] Create frontend view on path defined in ContentBlockNew::$view, e.g.:
 * 
 *      /* @var $this Template * /
 *      $data = &$this->params;
 *      $uniqueClass = uniqid('cb-');
 *      ?><div class="cb-new <?php echo $uniqueClass ?>"><?php
 *          echo App::loadView('ContentBlock', 'blockTitle', $this->params);
 *          if (isset($data['my_field'])) {
 *              ?><div class="my-field-block"><?php 
 *                  echo $data['my_field'];
 *              ?></div><?php
 *          }
 *      ?></div><?php
 *      App::setCssFiles(array(
 *          '/app/modules/ContentBlock/css/main.css'
 *      ));
 *      App::loadView('ContentBlock', 'blockParamsCssAndJs', array_merge($this->params, array('uniqueClass' => $uniqueClass)));
 * 
 * You can use any "css & js methods" of App class in view definition.
 * If some text field(s) possibly contain text snippets it is up to you to load 
 * them, the best in ContentBlockDefinition::prepareViewData(). The $this->params 
 * are set to view data.
 * 
 * 4] Add new created ContentBlockNew to models of your content blocks input 
 * (FormHelper::contentBlocks() option 'contentBlockModels').
 * 
 * ATTENTION: Content blocks previews are rendered by FormHelper::contentBlocks() 
 * in iframes using contentBlockPreview layout. This is usefull to set specific 
 * styles (inside body#content-block-preview-layout) for content blocks. E.g. for 
 * blocks with height set by css vh units (in iframe 100vh = iframe height = previewed 
 * block/page height) the height must be restricted by max-height in px units.
 * 
 * *********************************************
 * Explicit loading of content blocks views HTML
 * *********************************************
 * 
 * You can use content block as a kind of "element" by loading its front-end view
 * in code. In such a case there are no instance data so you must provide explicit data.
 * Use one of following posibilities:
 * 
 *      App::loadContentBlockView('ContentBlock', 'ContentBlockMy', array(
 *          // explicitly provide data expected by content block, e.g.:
 *          'title' => __(__FILE__, 'My title'),
 *      ));
 * 
 * Or a bit longer version:
 * 
 *      App::loadModel('ContentBlock', 'ContentBlockMy');
 *      $ContentBlock = new ContentBlockMy();
 *      echo $ContentBlock->loadView(array(
 *          // explicitly provide data expected by content block, e.g.:
 *          'title' => __(__FILE__, 'My title'),
 *      ));
 * 
 * *******************************************
 * Loading content blocks instances views HTML
 * *******************************************
 * 
 * This is in competence of ContentBlockInstance model, see ContentBlockInstance class phpDoc.
 * 
 * ***************************************
 * Add content blocks to other owner model
 * ***************************************
 * 
 * This is in competence of ContentBlockInstance model, see ContentBlockInstance class phpDoc.
 * 
 */
abstract class ContentBlock extends Model {
        
    //
    // CONFIG PROPERTIES & METHODS
    //
    
    /**
     * If TRUE then the content block is available in dropdown menu of FormHelper::contentBlocks() input.
     * If you don't want to make it available for use in project then set it to FALSE.
     * ATTENTION: this does not influence activity of existing block instances.
     *
     * @var bool 
     */
    protected $available = true;
        
    /**
     * !!! MUST BE SPECIFIED IN CHILD CLASSES !!!
     * 
     * Name of content block to be used in dropdown menu of FormHelper::contentBlocks() input.
     * Can be set translated in __construct().
     * 
     * @var string 
     */
    protected $blockName;
    
    /**
     * !!! MUST BE SPECIFIED IN CHILD CLASSES !!!
     * 
     * Description of content block to be used in dropdown menu of FormHelper::contentBlocks() input.
     * Can be set translated in __construct().
     * 
     * @var string 
     */
    protected $description = null;
    
    /**
     * Image of content block to be used in dropdown menu of FormHelper::contentBlocks() input.
     * It is displayed with width of 100px in dropdown menu.
     * Use module img/ folder to store this image. 
     * 
     * @var string 
     */
    protected $imageUrlPath = null;
    
    /**
     * !!! MUST BE SPECIFIED IN CHILD CLASSES !!!
     * 
     * Content block admin (backend) view path relative to {Module}/views.
     * You can use any "css & js methods" of App class in view definition.
     * !!! Only fields defined in $fields property are saved !!!
     * There are following items in $this->params: 
     * - 'data' containing instance data
     * - 'Form' containing instance of FormHelper class initialized by options 'data', 
     *      'required' and 'errors'. Use only this FormHelper instance to create inputs.
     * 
     * @var string 
     */
    protected $adminView;
            
    /**
     * !!! MUST BE SPECIFIED IN CHILD CLASSES !!!
     * 
     * Content block frontend view path relative to {Module}/views.
     * You can use any "css & js methods" of App class in view definition.
     * If some text field(s) possibly contain text snippets it is up to you to load 
     * them, the best in prepareViewData() method. The $this->params 
     * of view are set to instance data.
     * 
     * @var string 
     */
    protected $view;
    
    /**
     * Is the single content block forontend view previewable?
     * E.g. the ContentBlockBackgroundStart and ContentBlockBackgroundStart are not
     * previewable as single content blocks.
     * 
     * @var bool
     */
    protected $previewable = true;
    
    /**
     * Content block instance custom data fields provided like pairs {fieldName} => {defaultValue}
     * !!! They may not contain fields defined in ContentBlockInstance::$schema !!!
     * !!! Only custom data fields specified here are saved for instance !!!
     * 
     * @var array 
     */
    protected $fields = array(); 
    
    /**
     * File fields of content block custom data. 
     * Definition is the same as for Model::$fileFields
     *
     * @var array 
     */
    protected $fileFields = array();
    
    /**
     * Validations of content block admin (backend) custom data. 
     * Definition is the same as for Model::$validations
     * 
     * @var array 
     */
    protected $validations = array();
    
    /**
     * Name of validation alternative for content block custom data (ContentBlock::$fields) 
     * validations. This is usefull if content block does also some other validations
     * (e.g. frontend form data validations) and we need to separate backend and frontend
     * data validations. If there is no need to separate validation cases then 
     * validations of content block custom data can be defined without alternatives.
     * 
     * @var array 
     */
    protected $fieldsValidationAlternative = 'contentBlockFields';
    
    /**
     * Name of paired content blocks start model. 
     * Should be defined in end model, e.g. 'ContentBlockBackgroundStart'
     * 
     * NOTE: According convention the start model name must end by "Start" and
     * end model name must end by "End"
     * 
     * @var string 
     */
    protected $startModel = null;
    
    /**
     * Name of paired content blocks end model. 
     * Should be defined in start model, e.g. 'ContentBlockBackgroundEnd'
     * 
     * NOTE: According convention the start model name must end by "Start" and
     * end model name must end by "End"
     * 
     * @var string 
     */
    protected $endModel = null;
    
    /**
     * Name of field where in case of paired blocks end block are stored the corresponding
     * start block data. It means this field is considered in end block and it is up
     * to user to add this block to ContentBlock::$fields (if not there then not 
     * saved and not available)
     * 
     * @var string 
     */
    protected $startDataField = 'block_start_data';
    
    /**
     * The name of blocks pair used in validation messages, e.g. 'Obsahové bloky pozadia'.
     * Must be specified if ContentBlock::$startModel or ::$endModel is specified.
     * Can be set translated in __construct().
     *
     * @var string 
     */
    protected $pairName = null;
    
    /**
     * Normalizes content block admin (backend) view data.
     *
     * @param array $data Data to be normalized.
     * @param array $options Options of Model::normalizeEmptyValues() plus following:
     *      - 'on' (string) Is the normalization done on 'create' or 'update'. 
     *      Defaults to NULL (not applied).
     *      - 'alternative' (string) Normalization alternative. Defaults to NULL (not applied).
     *          only in case that normalize() is called from instance. Defaults to NULL.
     * 
     * ATTENTION: Be carefull to apply Sanitize::value() on $data. Always consider 
     *      that e.g. $name = Sanitize::value($data['name']) will create $data['name'] === NULL
     *      in case that 'name' key is not present in $data array in tne moment of
     *      the check. And like this you can erase 'name' in DB record. To avoid
     *      this trap use either ternary expessions or make copy of $data to an internal variable.
     * 
     * @return array Normalized data
     */
    public function normalize($data, $options = array()) {
//        $defauts = array(
//            'on' => null,
//            'alternative' => null,
//        );
//        $options = array_merge($defauts, $options);
//        $options['alternative'] = (array)$options['alternative'];
//        
//        // do your custom normalization...
        
        return parent::normalize($data, $options);
    }
    
    /**
     * Prepares data for content block admin (backend) view, e.g. file fields url 
     * paths can be set here
     * 
     * NOTE: Instance id (if any) is contained in data: $data['run_content_block_instances_id']
     * 
     * @param array $data Content block raw data There are following 3 cases:
     *      - new (unsaved) content block of new (unsaved) content owner (e.g. of
     *          new webcontent page). In this case the $data contains ContentBlock::$fields
     *          with default values plus 'owner_model' and 'owner_id'.
     *      - new (unsaved) content block of existing (saved) content owner (e.g. of
     *          existin webcontent page). In this case the $data contains ContentBlock::$fields
     *          with submited values plus 'owner_model' and 'owner_id'.
     *      - existing (saved) content block of existing (saved) content owner (e.g. of
     *          existin webcontent page). In this case the $data contains ContentBlock::$fields
     *          with submited values plus all fields of ContentBlock instance (including 
     *          'owner_model' and 'owner_id').
     *      Use debuger to know exact $data items.
     * @prams bool $dataAreSubmitted Are the incoming data just retrieved from DB 
     *      or are they submitted form data?
     * 
     * @return array Prepared data
     */
    public function prepareAdminViewData($data, $dataAreSubmitted) {
//        // set a file field URL path
//        if (
//            !$dataAreSubmitted
//            && !empty($data['my_file_field'])
//        ) {
//            $data['my_file_field'] = $this->getFileFieldUrlPath('my_file_field', array(
//                'file' => $data['my_file_field'],
//                //'variant' => 'myVariant',
//            ));
//        }
        return $data;
    }    
    
    /**
     * Prepares data for content block frontend view, e.g. file fields url paths 
     * can be set here or text snippets loaded
     * 
     * NOTE: Instance id is contained in data: $data['run_content_block_instances_id']
     * 
     * @param array $data Content block raw data
     * @param array $options Following are available:
     *      - 'ownerModel' (string) Block owner model. To ensure uniquity the model name should 
     *          be qualified by its module, e.g. 'App.WebContent', 'Eshop.EshopProduct'. 
     *          It is up to each ContentBlock definition how this is used/processed 
     *          or maybe ignored here. Defaults to NULL.
     *      - 'ownerId' (string) Block owner id. It is up to each ContentBlock definition
     *          how this is used/processed or maybe ignored here. Defaults to NULL.
     *      - 'ownerRecord' (array) Block owner record data. It is up to each ContentBlock definition
     *          how this is used/processed or maybe ignored here. Defaults to NULL.
     * 
     * @return array Prepared data
     */
    public function prepareViewData($data, $options = array()) {
//        $defaults = array(
//            'ownerModel' => null,
//            'ownerId' => null,
//            'ownerRecord' => null,
//        );
//        $options = array_merge($defaults, $options);
//        // set a file field URL path
//        if (!empty($data['my_file_field'])) {
//            $data['my_file_field'] = $this->getFileFieldUrlPath('my_file_field', array(
//                'file' => $data['my_file_field'],
//                //'variant' => 'myVariant',
//            ));
//        }
//        // load text snippets
//        if (!empty($data['my_text_field'])) {
//            $snippetsOptions = array();
//            if ($options['ownerModel'] === 'App.WebContent') {
//                if (!empty($options['ownerRecord'])) {
//                    $snippetsOptions['params'] = array('_content' => $options['ownerRecord']);
//                }
//                elseif (!empty($options['ownerId'])) {
//                    $snippetsOptions['params'] = array('_content' => array('id' => $options['ownerId']));
//                }
//            }
//            $data['my_text_field'] = App::loadTextSnippets($data['my_text_field'], $snippetsOptions);
//        }
        return $data;
    }
    
    //
    // RUNTIME PROPERTIES
    //
    
    /**
     * Customized instance of ContentBlockInstance class
     * 
     * @var ContentBlockInstance
     */
    protected $Instance = null;
        
    public function __construct() {
        parent::__construct();

        if (empty($this->blockName)) {
            throw new Exception(__e(__FILE__, '%s.%s::$blockName must be specified', $this->module, $this->name));
        }
        if (empty($this->description)) {
            throw new Exception(__e(__FILE__, '%s.%s::$description must be specified', $this->module, $this->name));
        }
        if (empty($this->adminView)) {
            throw new Exception(__e(__FILE__, '%s.%s::$adminView must be specified', $this->module, $this->name));
        }
        if (empty($this->view)) {
            throw new Exception(__e(__FILE__, '%s.%s::$view must be specified', $this->module, $this->name));
        }
        $this->fields = (array)$this->fields;
        if (
            !empty($this->fields)
            && !Validate::assocArray($this->fields)
        ) {
            throw new Exception(__e(__FILE__, '%s.%s::$fields must be an associative array containing pairs {fieldName} => {defautValue}', $this->module, $this->name));
        }
        App::loadModel('App', 'ContentBlockInstance');
        $this->Instance = new ContentBlockInstance($this);
        if (
            ($result = array_intersect_key(
                $this->fields, 
                $this->Instance->getPropertySchema()
            ))
        ) {
            $fields = implode(', ', array_keys($result));
            throw new Exception(__e(
                __FILE__,
                '%s.%s::$fields may not contain fields defined in %s.%s::$schema: %s', 
                $this->module, $this->name, $this->Instance->getPropertyModule(), $this->Instance->getPropertyName(), $fields
            ));
        }
        $this->fileFields = (array)$this->fileFields;
        if (
            !empty($this->fileFields)
            && ($result = array_diff_key($this->fileFields, $this->fields))
        ) {
            $fields = implode(', ', array_keys($result));
            throw new Exception(__e(
                __FILE__,
                'There are file fields in %s.%s::$fileFields which are not defined in %s.%s::$fields: %s', 
                $this->module, $this->name, $this->module, $this->name, $fields
            ));
        }
        if (
            !empty($this->startModel)
            && !empty($this->endModel)
        ) {
            throw new Exception(__e(__FILE__, 'Specify either %s.%s::$startModel or %s.%s::$endModel. You can\'t specify both!', $this->module, $this->name, $this->module, $this->name));
        }
        if (!empty($this->startModel)) {
            if (!preg_match('/Start$/', $this->startModel)) {
                throw new Exception(__e(__FILE__, '%s.%s::$startModel is set to invalid value "%s"', $this->module, $this->name, $this->startModel));
            }
            if (!preg_match('/End$/', $this->name)) {
                throw new Exception(__e(__FILE__, 'End model %s.%s has invalid name. Its name must be finished by "End"!', $this->module, $this->name));
            }
        }
        if (!empty($this->endModel)) {
            if (!preg_match('/End$/', $this->endModel)) {
                throw new Exception(__e(__FILE__, '%s.%s::$endModel is set to invalid value "%s"', $this->module, $this->name, $this->endModel));
            }
            if (!preg_match('/Start$/', $this->name)) {
                throw new Exception(__e(__FILE__, 'Start model %s.%s has invalid name. Its name must be finished by "Start"!', $this->module, $this->name));
            }
        }
        if (
            (
                !empty($this->startModel)
                || !empty($this->endModel)
            )
            && empty($this->pairName)
        ) {
            throw new Exception(__e(__FILE__, '%s.%s::$pairName must be specified', $this->module, $this->name));
        }
        
        // add default block params validations
        if (
            isset($this->fields['block_top_padding'])
            && empty($this->validations['block_top_padding'])
        ) {
            $this->validations['block_top_padding'] = array(
                array(
                    'rule' => 'responsiveValue',
                ),
            );
        }
        if (
            isset($this->fields['block_bottom_padding'])
            && empty($this->validations['block_bottom_padding'])
        ) {
            $this->validations['block_bottom_padding'] = array(
                array(
                    'rule' => 'responsiveValue',
                ),
            );
        }
        if (
            isset($this->fields['block_left_padding'])
            && empty($this->validations['block_left_padding'])
        ) {
            $this->validations['block_left_padding'] = array(
                array(
                    'rule' => 'responsiveValue',
                ),
            );
        }
        if (
            isset($this->fields['block_right_padding'])
            && empty($this->validations['block_right_padding'])
        ) {
            $this->validations['block_right_padding'] = array(
                array(
                    'rule' => 'responsiveValue',
                ),
            );
        }
        
        // add url root and timestamp to imageUrlPath
        if (
            $this->imageUrlPath
            && ($absoluteImagePath = File::getAbsolutePath(File::normalizeDS($this->imageUrlPath)))
            && is_readable($absoluteImagePath)
        ) {
            $this->imageUrlPath = URL_ROOT . $this->imageUrlPath . '?ts=' . filemtime($absoluteImagePath);
        }
    }
    
    /**
     * Loads script placed under current module views/ folder and returns it's html output.
     * Use alternative signature to load ContentBlock model front-end view for provided
     * $data and $options.
     * 
     * @param string $name See ModuleObject::loadView()
     * @param array $params See ModuleObject::loadView()
     * @param array $data See ModuleObject::loadView()
     * @param string|array $catchVariables See ModuleObject::loadView()
     * @param array& $catchedVariables See ModuleObject::loadView()
     * 
     * An alternative signature is available in ContentBlock models:
     * 
     * @param array $data Content block raw data passed to ContentBlock::prepareViewData()
     *          and then to content block frontend view. This can be an instance
     *          data (in common case) or an explicit data to use the content block 
     *          view for some custom purpose (kind of "element"). There is no owner 
     *          in case of explicit data, so no "owner" options should be set in such a case.
     * @param array $options Options passed to ContentBlock::prepareViewData(). Following are available:
     *      - 'ownerModel' (string) Block owner model. To ensure uniquity the model name should 
     *          be qualified by its module, e.g. 'App.WebContent', 'Eshop.EshopProduct'. 
     *          It is up to each ContentBlock definition how this is used/processed 
     *          or maybe ignored here. This should be set only in case of instance data.
     *          Defaults to NULL.
     *      - 'ownerId' (string) Block owner id. It is up to each ContentBlock definition
     *          how this is used/processed or maybe ignored here. This should be set only 
     *          in case of instance data. Defaults to NULL.
     *      - 'ownerRecord' (array) Block owner record data. It is up to each ContentBlock definition
     *          how this is used/processed or maybe ignored here. This should be set only 
     *          in case of instance data. Defaults to NULL.
     * 
     * @return string Html output generated by the script.
     */
    public function loadView($name, $params = array(), $data = array(), $catchVariables = null, &$catchedVariables = array()) {
        if (is_string($name)) {
            return parent::loadView($name, $params, $data, $catchVariables, $catchedVariables);
        }
        // $data are in $name, $options are in $params
        $name = $this->prepareViewData($name, $params);
        return App::loadView(
            $this->module,
            $this->view, 
            $name
        );
    }
    
    /**
     * Is this a block start model?
     * 
     * @return bool
     */
    public function isStartModel() {
        return !empty($this->endModel);
    }
    
    /**
     * Is this a block end model?
     * 
     * @return bool
     */
    public function isEndModel() {
        return !empty($this->startModel);
    }
    
    /**
     * Returns start model alias, e.g. 'ContentBlock.ContentBlockBackgroundStart'
     * 
     * If no startModel is defined then an axception is thrown
     * 
     * @param array $options Following are available:
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     * 
     * @return string Alias described here above
     */
    public function getStartModelAlias($options = array()) {
        if ($this->isEndModel()) {
            $options['alias'] = $this->startModel;
            return $this->getAlias($options);
        }
        else {
            throw new Exception(__e(__FILE__, 'No %s.%s::$startModel is defined', $this->module, $this->name));
        }
    }
    
    /**
     * Returns end model alias, e.g. 'ContentBlock.ContentBlockBackgroundEnd'
     * 
     * If no endModel is defined then an axception is thrown
     * 
     * @param array $options Following are available:
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     * 
     * @return string Alias described here above
     */
    public function getEndModelAlias($options = array()) {
        if ($this->isStartModel()) {
            $options['alias'] = $this->endModel;
            return $this->getAlias($options);
        }
        else {
            throw new Exception(__e(__FILE__, 'No %s.%s::$endModel is defined', $this->module, $this->name));
        }
    }
    
    /**
     * Validates given data according to $this->validations rules or accodring logic 
     * implemented inside of overrided instances of this method.
     * 
     * Validation can be also launched as a part of save() and update() methods but it
     * these cases only fields from table schema are validated.
     * If you need to validate also fields which are not saved, (captcha, verification code, ...)
     * then launch the validation separately by direct call of validate() method.
     * 
     * To implement validations by overriding Model::validate() method use something like:
     * 
     *  MyClass extends Model {
     * 
     *      public function validate($data, $options = array()) {
     *          $defaults = array(
     *              'alternative' => null,
     *              'on' => null,
     *          )
     *          $options = array_merge($defaults, $options);
     *          $isValid = true;
     * 
     *          // call parent method to treat validations defined in MyClass::$validations 
     *          $isValid = parent::validate($data, $options);
     * 
     *          // implement your own validation logic, e.g. for usual email it would be:
     *          // - required (present in data) on create
     *          if (
     *              $options['on'] === 'create'
     *              && !array_key_exists('email', $data)
     *          ) {
     *              $isValid = false;
     *              $this->setError('email', __v(__FILE__, 'E-mail is mandatory'));
     *          }
     *          // - not empty if in data
     *          elseif (
     *              array_key_exists('email', $data)
     *              && empty($data['email'])
     *          ) {
     *              $isValid = false;
     *              $this->setError('email', __v(__FILE__, 'E-mail is mandatory'));
     *          }
     *          // - validate email format if nonempty in data
     *          elseif (
     *              !empty($data['email'])
     *              && !Validate::email($data['email'])
     *          ) {
     *              $isValid = false;
     *              $this->setError('email', __v(__FILE__, 'E-mail is not valid'));
     *          }
     *          // - validate that unique if nonempty in data
     *          elseif (
     *              !empty($data['email'])
     *              && !$this->validate_unique($data['email'], 'email', $data, null, array(), false)
     *          ) {
     *              $isValid = false;
     *              $this->setError('email', __v(__FILE__, 'E-mail must be unique'));
     *          }
     *           
     *          return $isValid;
     *      }
     *  }
     * 
     * NOTE: This method resets the value of property Model::$errors
     *
     * @param array $data Data to be validated. Passed by reference to give this function
     *      possibility to normalize data values
     * @param array $options Following are available:
     *      - 'allowValidations' (array) List of allowed validations. For sake of
     *          this options the validations must be named, e.g. array('fieldName' => array(
     *          'validationName01', 'validationName02', ...)). Defauts to NULL.
     *      - 'avoidValidations' (array) List of validations to avoid. For sake of
     *          this options the validations must be named, e.g. array('fieldName' => array(
     *          'validationName01', 'validationName02', ...)). Use array('fieldName' => TRUE) 
     *          to avoid all for given field. Defaults to NULL.
     *      - 'alternative' (string|array) Single validation altrnative name or an array
     *          of such names. If empty then only general validations are executed.
     *          Defaults to NULL.
     *      - 'on' (string|bool) When the validation hould be launched? Possible values are 
     *          'create', 'update' and TRUE. If TRUE the 'create' or 'update' is set implicitly.
     *      - 'allowFields' (string|array) Single field or list of fields for which validations are executed.
     *      - 'avoidFields' (string|array) Single field or list of fields for which validations are avoided.
     *      - 'normalize' (bool) Should the $data be normalized first using Model::normalize()?
     *          Defaults to TRUE.
     *      - 'resetErrors' (bool) Should the model errors by reset before validation.
     *          Defaults to TRUE.
     * 
     * @return boolean. If TRUE data are valid. If FALSE there are validations errors
     *      which can be retrieved by method $this->getErrors().
     */
    public function validate($data, $options = array()) {
        $valid = true;
        if ($this->isStartModel() || $this->isEndModel()) {
            $valid = $this->validateStartEndPairs();
        }
        return $valid && parent::validate($data, $options);
    }
    
    /**
     * Used in ContentBlock::validateStartEndPairs() to keep trace of models for 
     * which has been start - end pairs already validated to avoid unneccessary 
     * repetition. The start model name is used as trace for both start and end models.
     * 
     * @var array 
     */
    static protected $validatedStartEndPairsModels = array();
        
    /**
     * Validates pairing of starting and ending content blocks
     *  
     * @return boolean
     * @throws Exception
     */
    protected function validateStartEndPairs() {
        $isValid = true;
        if (
            !$this->isStartModel() 
            && !$this->isEndModel()
        ) {
            return $isValid;
        }
        App::loadModel('App', 'ContentBlockInstance');
        $Instance = new ContentBlockInstance();
        $dataKey = $Instance->getPropertyDataKey();
        $startModel = null;
        if (!empty(App::$data[$dataKey])) {
            if ($this->isStartModel()) {
                $startModel = $this->getAlias(array('moduleAlias' => true));
                $endModel = $this->getEndModelAlias(array('moduleAlias' => true));
            }
            else {
                $startModel = $this->getStartModelAlias(array('moduleAlias' => true));
                $endModel = $this->getAlias(array('moduleAlias' => true));
            }
            if (isset(self::$validatedStartEndPairsModels[$startModel])) {
                return self::$validatedStartEndPairsModels[$startModel];
            }
            $contentBlockModelField = $Instance->getPropertyContentBlockModelField();
            $instanceForeignKeyField = $Instance->getForeignKey();
            $instanceActiveField = $Instance->getPropertyActiveField();
            // get all existing instance content block models by one request
            $instanceIds = array();
            foreach (App::$data[$dataKey] as $instanceData) {
                if (!empty($instanceData[$instanceForeignKeyField])) {
                    $instanceIds[] = $instanceData[$instanceForeignKeyField];
                }
            }
            $contentBlockModels = array();
            if ($instanceIds) {
                $contentBlockModels = $Instance->findList(array(
                    'fields' => array($contentBlockModelField),
                    'conditions' => array('id' => $instanceIds)
                ));
            }
            $balance = 0;
            $invalidOrder = 0;
            $inactiveBalance = 0;
            $invalidInactiveOrder = 0;
            foreach (App::$data[$dataKey] as $instanceData) {
                if (!empty($instanceData[$contentBlockModelField])) {
                    $blockModel = $instanceData[$contentBlockModelField];
                }
                else if (!empty($instanceData[$instanceForeignKeyField])) {
                    $instanceId = $instanceData[$instanceForeignKeyField];
                    $blockModel = Sanitize::value($contentBlockModels[$instanceId]);
                }
                else {
                    throw new Exception(__e(__FILE__, 'Invalid content block instances data - they must contain either content block model  or content block instance foreign key'));
                }
                if (
                    $blockModel === $startModel
                ) {
                    $balance++;
                    if (empty($instanceData[$instanceActiveField])) {
                        $inactiveBalance++;
                    }
                }
                elseif (
                    $blockModel === $endModel                        
                ) {
                    $balance--;
                    if ($balance < 0) {
                        $invalidOrder++;
                    }
                    if (empty($instanceData[$instanceActiveField])) {
                        $inactiveBalance--;
                        if ($inactiveBalance < 0) {
                            $invalidInactiveOrder++;
                        }
                    }
                }
            }
            if ($balance > 0) {
                App::setErrorMessage(__v(__FILE__, 'Párové obsahové bloky "%s": skontrolujte, či každý začiatočný blok má aj príslušný párový koncový blok. %s začiatočným blokom chýba koncový blok', $this->pairName, $balance));
                $isValid = false;
            }
            elseif ($balance < 0) {
                App::setErrorMessage(__v(__FILE__, 'Párové obsahové bloky "%s": skontrolujte, či každý koncový blok má aj príslušný párový začiatočný blok. %s koncovým blokom chýba začiatočný blok', $this->pairName, abs($balance)));
                $isValid = false;
            }
            elseif ($invalidOrder) {
                App::setErrorMessage(__v(__FILE__, 'Párové obsahové bloky "%s": skontrolujte ich poradie. %s koncových blokov je umiestnených skôr ako začiatočný blok', $this->pairName, $invalidOrder));
                $isValid = false;
            }
            elseif ($inactiveBalance > 0) {
                App::setErrorMessage(__v(__FILE__, 'Párové obsahové bloky "%s": skontrolujte, či každý neaktívny začiatočný blok má zneaktívnený aj príslušný párový koncový blok. %s nektívnych začiatočných blokov nemá zneaktívnené koncové bloky', $this->pairName, $inactiveBalance));
                $isValid = false;
            }
            elseif ($inactiveBalance < 0) {
                App::setErrorMessage(__v(__FILE__, 'Párové obsahové bloky "%s": skontrolujte, či každý neaktívny koncový blok má zneaktívnený aj príslušný párový začiatočný blok. %s nektívnych koncových blokov nemá zneaktívnené začiatočné bloky', $this->pairName, abs($inactiveBalance)));
                $isValid = false;
            }
            elseif ($invalidInactiveOrder) {
                App::setErrorMessage(__v(__FILE__, 'Párové obsahové bloky "%s": skontrolujte poradie neaktívnych. %s nektívnych koncových blokov je umiestnených skôr ako nektívny začiatočný blok', $this->pairName, $invalidInactiveOrder));
                $isValid = false;
            }
        }
        self::$validatedStartEndPairsModels[$startModel] = $isValid;
        return $isValid;
    }
    
    /**
     * Validates responsive value syntax correctness
     * 
     * @param string $value
     * @param string $field
     * @param array $data
     * @param array& $validation
     * @param array $options Options passed to Html::parseResponsiveCssValue()
     * 
     * @return boolean
     */
    public function validate_responsiveValue($value, $field, $data, &$validation, $options = array()) {
        try {
            $options['exceptionOnFailure'] = true;
            Html::parseResponsiveCssValue($value, $options);
        } 
        catch (Throwable $e) {
            $validation['message'] = $e->getMessage();
            return false;
        }
        return true;
    }
    
    /**
     * Creates file for specified $field, $variant and $filename. 
     * 
     * This is used for creation of lazy variant files. See _getImage screen.
     * 
     * @param string $field
     * @param string $variant
     * @param string $filename
     * @param array $options Following are available
     *      - 'force' (bool) If TRUE then variant file is recreated even it exists already.
     *          If FALSE then variant file is created only if it does not exist.
     *          Defaults to TRUE.
     * 
     * @return string|boolean Created variant file app root relative path or FALSE
     *      on failure, it means if provided $field, $variant or $filename is invalid.
     *      An uncatched exceptions can be thrown.
     */
    public function createFileFieldVariantFile($field, $variant, $filename, $options = array()) {
        return $this->Instance->createFileFieldVariantFile($field, $variant, $filename, $options);
    }
    
    public function getPropertyAvailable() {
        return $this->available;
    }
                
    public function getPropertyBlockName() {
        return $this->blockName;
    }
    
    public function getPropertyDescription() {
        return $this->description;
    }
    
    public function getPropertyImageUrlPath() {
        return $this->imageUrlPath;
    }
    
    public function getPropertyView() {
        return $this->view;
    }
    
    public function getPropertyAdminView() {
        return $this->adminView;
    }
    
    public function getPropertyPreviewable() {
        return $this->previewable;
    }
    
    public function getPropertyFields() {
        return $this->fields;
    }
    
    public function getPropertyFieldsValidationAlternative() {
        return $this->fieldsValidationAlternative;
    }
    
    public function getPropertyStartModel() {
        return $this->startModel;
    }
    
    public function getPropertyEndModel() {
        return $this->endModel;
    }
    
    public function getPropertyStartDataField() {
        return $this->startDataField;
    }
    
    public function getPropertyPairName() {
        return $this->pairName;
    }
}
