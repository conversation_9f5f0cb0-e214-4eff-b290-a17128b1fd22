<?php
class DBScript extends Model {
    var $table = 'run_scripts';
    
    var $primaryKey = 'id';
    
    var $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'module' => array('type' => 'varchar'),
        'pid' => array('type' => 'varchar', 'index' => 'index'),
        'label' => array('type' => 'varchar', 'default' => null),
        'description' => array('type' => 'text', 'default' => null),
        'source' => array('type' => 'text'),
        'type' => array('type' => 'enum', 'values' => array('php', 'js'), 'default' => 'php'),
        'run_users_ids' => array('type' => 'int', 'default' => null),
        'run_groups_ids' => array('type' => 'int', 'default' => null),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
}


