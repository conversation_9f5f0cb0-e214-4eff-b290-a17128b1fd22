<?php
class Setting extends Model {
    protected $table = 'run_settings';
    
    protected $primaryKey = 'id';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'module' => array('type' => 'varchar', 'index' => 'index'),
        'pid' => array('type' => 'varchar'),
        'label' => array('type' => 'varchar'),
        'description' => array('type' => 'text', 'default' => null),
        'value' => array('type' => 'mediumtext', 'default' => null),
        'js_visible' => array('type' => 'bool', 'default' => 0),
        //'translate' => array('type' => 'bool', 'default' => 0, 'comment' => 'Translate setting value or not?'), //@todo
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
    protected $translatedFields = array(
        'value'
    );
        
    protected $fileFields = array(
        'App/home/<USER>' => array(
            'extension' => 'jpg',
            'quality' => 90,
            'placeholder' => '_placeholder.png',
            'variants' => array(
                'default' => array(
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    
//                    'fit' => array(220, 220),
                    
//                    'cover' => array(400, 300),
//                    'cropInMiddle' => array(400, 300),
                    
                    'scaleByX' => array(1280),
                ),
                'original' => array(
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(1920, 1080),
                ),
            ),
        ),
        'App/facebook/defaultImage' => array(
            'variants' => array(
                '' => array(
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(300, 300),
                ),
            ),
        ),
    );
    
    /**
     * Path separator replacing dot during setting editation (to not fall into problems 
     * with Arr::getPath()). E.g. App.smtp.host is changed to App/smtp/host
     * 
     * @var string 
     */
    protected $pathSeparator = '/';
    
    /**
     * Returns value of Setting::$pathSeparator
     * 
     * @return string
     */
    public function getPropertyPathSeparator() {
        return $this->pathSeparator;
    }
    
    public function __construct() {
        parent::__construct();
        
        // VALIDATIONS
        // 
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        // !!! ATTENTION: Setting names must be provided here with pathSeparator instead of dots !!!
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        $this->validations = array(
            'App/name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please site name'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please site name'),
                ),
            ),
            'App/seo/defaultTitle' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please default SEO title'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please default SEO title'),
                ),
            ),
            'App/seo/defaultDescription' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please default SEO description'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please default SEO description'),
                ),
            ),
            'App/smtp/username' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please SMTP username'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please SMTP username'),
                ),
            ),
            'App/smtp/password' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please SMTP password'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please SMTP password'),
                ),
            ),
            'App/smtp/host' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please SMTP host name'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please SMTP host name'),
                ),
            ),
            'App/smtp/port' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please SMTP port'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please SMTP port'),
                ),
            ),
            'App/slider/transitionTime' => array(
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please slider transition time'),
                ),
            ),
            'App/slider/slideTime' => array(
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please slide duration time'),
                ),
            ),
            'App/email/from' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please an e-mail address'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please an e-mail address'),
                ),
                array(
                    'rule' => 'email',
                    'message' => __v(__FILE__, 'Provide please a valid e-mail address'),
                ),
            ),
            'App/email/to' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide please an e-mail address'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide please an e-mail address'),
                ),
                array(
                    'rule' => 'email',
                    'message' => __v(__FILE__, 'Provide please a valid e-mail address'),
                ),
            ),
            'App/email/cc' => array(
                array(
                    'rule' => 'email',
                    'message' => __v(__FILE__, 'Provide please a valid e-mail address'),
                ),
            ),
            'App/smartsms/username' => array(
                array(
                    'rule' => 'smartsmsNotEmpty',
                    'message' => __v(__FILE__, 'Provide please SMS username'),
                    'force' => true,
                ),
            ),
            'App/smartsms/password' => array(
                array(
                    'rule' => 'smartsmsNotEmpty',
                    'message' => __v(__FILE__, 'Provide please SMS password'),
                    'force' => true,
                ),
            ),
            'App/smartsms/from' => array(
                array(
                    'rule' => 'smartsmsNotEmpty',
                    'message' => __v(__FILE__, 'Provide please SMS sender phone'),
                    'force' => true,
                ),
                array(
                    'rule' => 'phone',
                    'message' => __v(__FILE__, 'Provide please valid phone number'),
                ),
            ),
            'App/luigisBox/jsScriptName' => array(
                array(
                    'rule' => '/LBX-\d{5,}(\.js)?/',
                    'message' => __v(__FILE__, 'Zadajte prosím platný názov skriptu'),
                ),
            ),
            'App/cookiesPrivacyPolicyInfo' => array(
                array(
                    // check if App.cookiesPrivacyPolicyInfo content exists if 
                    // :infoPageUrl: insert is used
                    'rule' => function($value) {
                        if (
                            strpos($value, ':infoPageUrl:') !== false
                            && !App::getContentUrlByPid('App.cookiesPrivacyPolicyInfo')
                        ) {
                            return false;
                        }
                        return true;
                    },
                    'message' => __v(__FILE__, 'The :infoPageUrl: insert cannot be used as content App.cookiesPrivacyPolicyInfo does not exist'),
                ),
            ),
            'App/customCode/htmlHead' => array(
                array(
                    'rule' => 'html',
                    'message' => __v(__FILE__, 'Zadajte prosím validný HTML kód'),
                ),
                array(
                    'rule' => array('html', array(
                        'allowTags' => array('script', 'style', 'link', 'noscript', 'img', 'iframe')
                    )),
                    'message' => __v(__FILE__, 'Povolené sú len tagy &lt;script&gt;, &lt;style&gt;, &lt;link&gt;, &lt;noscript&gt;, &lt;img&gt; a &lt;iframe&gt;'),
                ),
            ),
            'App/customCode/htmlBodyStart' => array(
                array(
                    'rule' => 'html',
                    'message' => __v(__FILE__, 'Zadajte prosím validný HTML kód'),
                ),
                array(
                    'rule' => array('html', array(
                        'allowTags' => array('script', 'style', 'link', 'noscript', 'img', 'iframe')
                    )),
                    'message' => __v(__FILE__, 'Povolené sú len tagy &lt;script&gt;, &lt;style&gt;, &lt;link&gt;, &lt;noscript&gt;, &lt;img&gt; a &lt;iframe&gt;'),
                ),
            ),
            'App/customCode/htmlBodyEnd' => array(
                array(
                    'rule' => 'html',
                    'message' => __v(__FILE__, 'Zadajte prosím validný HTML kód'),
                ),
                array(
                    'rule' => array('html', array(
                        'allowTags' => array('script', 'style', 'link', 'noscript', 'img', 'iframe')
                    )),
                    'message' => __v(__FILE__, 'Povolené sú len tagy &lt;script&gt;, &lt;style&gt;, &lt;link&gt;, &lt;noscript&gt;, &lt;img&gt; a &lt;iframe&gt;'),
                ),
            ),
        );
    }
    
    public function normalize($data, $options = array()) {
        if (
            array_key_exists('App/smtp/port', $data)
            && !array_key_exists('App/smtp/encryption', $data)
        ) {
            if ($data['App/smtp/port'] == 587) {
                $data['App/smtp/encryption'] = 'TLS';
            }
            elseif ($data['App/smtp/port'] == 465) {
                $data['App/smtp/encryption'] = 'SSL';
            }
            else {
                $data['App/smtp/encryption'] = '';
            }
        }
        if (
            array_key_exists('App/smtp/encryption', $data)
            && !array_key_exists('App/smtp/port', $data)
        ) {
            if ($data['App/smtp/encryption'] == 'TLS') {
                $data['App/smtp/port'] = 587;
            }
            elseif ($data['App/smtp/encryption'] == 'SSL') {
                $data['App/smtp/port'] = 465;
            }
            else {
                $data['App/smtp/port'] = 25;
            }
        }
        if (isset($data['App/announcement/textIsHidden'])) {
            $data['App/announcement/textIsHidden'] = !empty($data['App/announcement/textIsHidden'])
                && !empty($data['App/announcement/title'])
                && !empty($data['App/announcement/text']);
        }
        if (!empty($data['App/slider/transitionTime'])) {
            $data['App/slider/transitionTime'] = str_replace(',', '.', $data['App/slider/transitionTime']);
        }
        if (!empty($data['App/slider/slideTime'])) {
            $data['App/slider/slideTime'] = str_replace(',', '.', $data['App/slider/slideTime']);
        }
        if (!empty($data['App/luigisBox/jsScriptName'])) {
            // validation ensures that this regex will match
            $match = array();
            preg_match('/(LBX-\d{5,})(\.js)?/', $data['App/luigisBox/jsScriptName'], $match);
            $data['App/luigisBox/jsScriptName'] = $match[1];
        }
        if (!empty($data['App/customCode/htmlHead'])) {
            $data['App/customCode/htmlHead'] = trim($data['App/customCode/htmlHead']);
        }
        if (!empty($data['App/customCode/htmlBodyStart'])) {
            $data['App/customCode/htmlBodyStart'] = trim($data['App/customCode/htmlBodyStart']);
        }
        if (!empty($data['App/customCode/htmlBodyEnd'])) {
            $data['App/customCode/htmlBodyEnd'] = trim($data['App/customCode/htmlBodyEnd']);
        }
        return $data;
    }
    
    /**
     * Updates settings provided in list '{settingName}' => '{value}'. Setting name 
     * is created from setting  module name and pid, e.g. App/smtp/host.
     * 
     * @param array $data Settings data
     * @param array $options Model::update() options. Defaults to empty array().
     * 
     * @return boolean TRUE on success. FALSE on validation failure
     */
    public function updateByName($data, $options = array()) {
        $defaults = array(
            'processFiles' => true,
            'cleanUpFiles' => true,
            'lang' => null,
            'normalize' => true,
            'validate' => true,
        );
        $options = array_merge($defaults, $options);
        // normalize
        if ($options['normalize']) {
            $data = $this->normalize($data, $options);
            // if once normalized, turn it off (to not make it duplicitly during validation)
            $options['normalize'] = false;
        }
        // validate
        if (
            $options['validate'] 
            && !$this->validate($data, $options)
        ) {
            return false;
        }
        // check for files
        $fileOptions = $options;
        if (
            $fileOptions['processFiles'] 
            && $this->fileFields
        ) {
            // turn off translations 
            $fileOptions['translate'] = false;
            $data = $this->prepareFiles($data, $fileOptions);
        }
        // save
        try {
            App::setSqlLogging(false);
            $options['normalize'] = false;
            $options['validate'] = false;
            $options['processFiles'] = false;
            $options['cleanUpFiles'] = false;
            foreach($data as $name => $value) {
                $nameParts = explode($this->pathSeparator, $name);
                $module = array_shift($nameParts);
                $pid = implode('.', $nameParts);
                $options['conditions'] = array(
                    'module' => $module,
                    'pid' => $pid,
                );
                // prepare obsolete files according to existing setting value
                if (isset($this->fileFields[$name])) {
                    $this->setObsoleteFiles($name, $options);
                }
                // update setting value
                $this->update(array('value' => $value), $options);
            }
            App::reloadSettings();
            App::setSqlLogging();
        }
        catch (Throwable $e) {
            // clean up files on failure
            if (
                $fileOptions['processFiles'] 
                && $fileOptions['cleanUpFiles'] 
                && $this->fileFields
            ) {
                $this->cleanUpFiles(false);
            }
            throw $e;
        }
        // clean up files on success
        if (
            $fileOptions['processFiles'] 
            && $fileOptions['cleanUpFiles'] 
            && $this->fileFields
        ) {
            $this->cleanUpFiles(true);
        }
        return true;
    }
    
    /**
     * Returns list of settings with setting names used as keys in list.
     * Setting name is created from setting  module name and pid, e.g. App/smtp/host.
     * Setting::$pathSeparator is used to replace dots in setting names (to not fall 
     * into problems with Arr::getPath()).
     * 
     * @param array $options Model::find() options. Defaults to empty array().
     * 
     * @return array
     */
    public function findListWithNameKeys($options = array()) {
        $options['fields'] = (array)Sanitize::value($options['fields']);
        $options['fields'] = array_unique(array_merge(
                $options['fields'], 
                array(
                    $this->name . '.module', 
                    $this->name . '.pid', 
                    $this->name . '.value'
                )
        ));
        $settings = $this->find($options);        
        $list = array();
        foreach ($settings as $setting) {
            // create name as module.pid
            $name = $setting['module'] . $this->pathSeparator 
                . str_replace('.', $this->pathSeparator, $setting['pid']);
            $list[$name] = $setting['value'];
        }
        // get file fields real path
        $fileFields = array_keys($this->getPropertyFileFields());
        foreach($fileFields as $fileField) {
            if (!empty($list[$fileField])) {
                $list[$fileField] = $this->getFileFieldUrlPath($fileField, array('file' => $list[$fileField]));
            }
        }
        
        return $list;
    }
    
    /**
     * Populates Model::$obsoleteFiles for given fields of record(s) retrieved according to 
     * provided options.
     * 
     * @param array|string $fields Array of file fields or a single file field to prepare 
     *      obsolete files for from retrieved records
     * @param array $options Options array passed to Model::save(), update() or delete().
     *      Options are used to retrieve records.
     * 
     * @return bool FALSE if no records were retrieved and so no files prepared.
     *      Othervise TRUE.
     */
    public function setObsoleteFiles($fields, $options) {
        $fields = (array)$fields;
        // retrieve the record
        $options['fields'] = array('value');
        $options['first'] = false;
        $options['order'] = null;
        $records = $this->find($options);
        // if no record found then there is no reason to prepare any files
        if (!$records) {
            return false;
        }
        // put current file names into array of obsolete files
        foreach ($fields as $field) {
            foreach ($records as $record) {
                $fileName = $record['value'];
                if (!$fileName) {
                    continue;
                }
                $files = $this->getFileFieldPaths($field, array('file' => $fileName));
                if (!$files) {
                    continue;
                }
                foreach ($files as $file) {
                    $this->obsoleteFiles[$file] = true;
                }
            }
        }
        return true;
    }
    
    /**
     * Serves to validate emptyness of smartsms data. The rule is: either all or nothing
     */
    public function validate_smartsmsNotEmpty($fieldValue, $fieldName, $data) {
        // either all or nothing
        if (
            empty($data['App/smartsms/username'])
            && empty($data['App/smartsms/password'])
            && empty($data['App/smartsms/from'])
            ||
            !empty($data['App/smartsms/username'])
            && !empty($data['App/smartsms/password'])
            && !empty($data['App/smartsms/from'])
        ) {
            return true;
        }
        return false;
    }
    
    /**
     * Sets the same value in all available langs for specified settings.
     * As a source value is considered the value in actual lang. Values in other
     * langs are set to this source value.
     * 
     * @param array $settings Specification of settings to synchronize translated values
     *          for. It is an array of arrays like:
     *              array(
     *                  array(
     *                      'module' => '...',
     *                      'pid' => '...',
     *                  ),
     *                  array(
     *                      'module' => '...',
     *                      'pid' => '...',
     *                  ),
     *                  ...
     *              )
     */
    public function synchronizeTranslatedValues($settings) {
        if (!$settings) {
            return;
        }
        $langs = App::getPropertyLangs();
        $sourceField = self::getTranslationField('value');
        $targetFields = array();
        foreach ($langs as $lang) {
            if ($lang === App::$lang) {
                continue;
            }
            $targetFields[] = self::getTranslationField('value', $lang);
        }
        if (!$targetFields) {
            return;
        }
        $data = array();
        foreach ($targetFields as $targetField) {
            $data[$targetField] = DB::encloseName($sourceField);
        }
        $conditions = array();
        foreach($settings as $setting) {
            if ($conditions) {
                $conditions[] = 'OR';
            }
            $conditions[] = $setting;
        }        
        DB::update($this->table, $data, array(
            'conditions' => $conditions,
            'literals' => array(
                'data' => true
            ),
        ));
    }
}
