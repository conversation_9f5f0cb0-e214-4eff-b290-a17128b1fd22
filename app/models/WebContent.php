<?php
class WebContent extends Model {

    protected $table = null;
    
    protected $primaryKey = 'id';
    
    protected $nameField = 'name';
    
    protected $schema = null;
    
    protected $tableIndexes = array(
        array(
            'type' => 'index', 
            'fields' => array('locator', 'lang', 'active'), 
            'name' => 'LocatorIndex'
        ),
        array(
            'type' => 'index', 
            'fields' => array('pid', 'lang', 'active'), 
            'name' => 'PidIndex'
        ),
    );
        
    protected $fileFields = array(
        'image_name' => array(
            'placeholder' => '_placeholder.png',
            'variants' => array(
                '' => array(
                    'scaleByX' => array(1190),
                ),
                'article' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    'fitX' => array(870),
                ),
            ),
        ),
        'menu_icon' => array(
            'variants' => array(
                'original' => array(
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(1920, 1080),
                ),
                /*/
                'reference' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    'cover' => array(340),
                    'cropInMiddle' => array(340, 340),
                ),
                /*/
                'article' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    'cover' => array(407, 264), // = 1.1 * array(370, 240),
                    'cropInMiddle' => array(407, 264), // = 1.1 * array(370, 240),
                ),
            )
        ),
        'background_image' => array(
            'fitX' => array(1190),
        ),
    );
    
    /**
     * List of sections pids which use in admin a specific edit form
     * 
     * @var array 
     */
    public $specificSections = array(
        'sliders',
        'news',   
        'articles',
        'references', //@deprecated - Use functionality defined in Reference model and References controller
    );
    
    public function __construct(){
        $this->table = App::getPropertyContentsTable();
        $this->schema = App::getPropertyContentsSchema();
        
        parent::__construct();
        
        // set validations
        
        $this->validations = array(
            'pid' => array(
                array(
                    'rule' => array('uniqueForLang'),
                    'message' => __v(__FILE__, 'Pid must be unique'),
                ),
            ),
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                ),
            ),
            'lang' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Lang is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Lang is mandatory'),
                ),
                // NOTE: lang must be the same as the parent has. This is assured 
                // by WebContent::normalize()
            ),
            'locator' => array(
                // NOTE: Locator is mandatory for pages and in case of slug the locator 
                // must be uniqe (this is not true for internal and absolute URL). 
                // This is not validated but explicitly forced by WebContent::normalize()
                array(
                    'rule' => 'relativeOrAbsoluteUrl',
                    'message' => __v(__FILE__, 'Provide relative (/my-page) or absolute (http://my-site.con/my-page) URL'),
                    'alternative' => array('references'),
                ),
            ),
            'seo_title' => array(
                // NOTE: Seo title is mandatory for pages. 
                // This is not validated but explicitly forced by WebContent::normalize()
            ),
            'menu_icon' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Choose reference image'),
                    'on' => 'create',
                    'alternative' => array('references'), //@deprecated - Use functionality defined in Reference model and References controller
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Choose reference image'),
                    'alternative' => array('references'), //@deprecated - Use functionality defined in Reference model and References controller
                ),
                array(
                    'rule' => array('uploadData', array('notEmpty' => true)),
                    'message' => __v(__FILE__, 'Choose reference image'),
                    'on' => 'create',
                    'alternative' => array('references'), //@deprecated - Use functionality defined in Reference model and References controller
                ),
                array(
                    'rule' => array('uploadData', array('notEmpty' => false)),
                    'message' => __v(__FILE__, 'Choose reference image'),
                    'alternative' => array('references'), //@deprecated - Use functionality defined in Reference model and References controller
                ),
            ),
            
            /**
             * Search fields
             */
            'keywords' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please enter search keywords'),
                    'alternative' => 'search',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please enter search keywords'),
                    'alternative' => 'search',
                ),
                array(
                    'rule' => 'searchKeywords',
                    'message' => __v(__FILE__, 'Keywords must be at least 2 chars long'),
                    'alternative' => 'search',
                ),
            ),
        );
    }
    
    /**
     * 
     * @param array $data
     * @param array $options Following are available:
     *      - 'on' (string) Possible values are 'create' and 'update'.
     *          Defaults to NULL.
     *      - 'alternative' (string) Possible values are 'container' and 'page'.
     *          Defaults to NULL.
     * 
     * @return array Normalized data
     */
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        //$data = parent::normalize($data, $options);
        
        // normalize date format
        if (!empty($data['date'])) {
            $data['date'] = Date::format($data['date'], 'Y-m-d');
        }
        elseif ($options['on'] === 'create') {
            $data['date'] = date('Y-m-d');
        }
        
        // set unprovided lang according to parent id lang
        if (
            empty($data['lang'])
            && !empty($data['parent_id'])
        ) {
            $data['lang'] = $this->findFieldBy('lang', 'id', $data['parent_id']);
        }
        
        // use content name to generate unprovided seo_title in case of page content
        if (
            in_array('page', $options['alternative'])
            && (
                // - on page creation if no seo_title provided   
                $options['on'] === 'create'
                && !array_key_exists('seo_title', $data)
////sometime it is ok to let seo_title empty to apply default seo title                    
//                ||
//                // - on page save if empty seo_title is provided
//                array_key_exists('seo_title', $data)
//                && $data['seo_title'] === ''
            )
        ) {
            $data['seo_title'] = empty($data['name']) ? '' : $data['name'];
        }
        
        // use content name to generate unprovided locator in case of page content (but not for references - VYDAVATEL SPECIFIC)
        // do it only on page creation to allow locator set empty on update e.g. for passive menu items
        if (
            in_array('page', $options['alternative'])
            && !in_array('references', $options['alternative'])
            && $options['on'] === 'create'
            && (
                !array_key_exists('locator', $data)
                || $data['locator'] === ''
            )
            // do not populate for references
            && !in_array('references', $options['alternative'])
            // do not populate for sliders
            && !in_array('sliders', $options['alternative'])
        ) {
            $data['locator'] = empty($data['name']) ? '' : Str::slugize($data['name']);
        }
        
        // normalize local absolute urls to local relative urls
        if (
            !empty($data['locator'])
            && Validate::absoluteUrl($data['locator'])
            && !Validate::externalUrl($data['locator'])
        ) {
            $locatorParts = explode('/', $data['locator'], 4);
            if (count($locatorParts) < 4) {
                $data['locator'] = '/';
            }
            else {
                $data['locator'] = '/' . $locatorParts[3];
            }
        }
        
        // if locator provided then force it to be unique if it is slug (means not internal or absolute URL)
        // and slugize it
        if (!empty($data['locator'])) {
            $data['locator'] = $this->getUniqueSlug($data['locator'], array(
                'id' => empty($data['id']) ? null : $data['id'],
                'lang' => empty($data['lang']) ? null : $data['lang'],
            ));
        }
        
        // normalize 'obfuscate' value 
        // In admin form 'obfuscate' and 'autodetect_obfuscate' are joined into 
        // one select box for user comfort like: 
        // '0' => 'do not obfuscate'
        // '1' => 'obfuscate'
        // 'auto' => 'autodetect'
        if (
            array_key_exists('obfuscate', $data)
            && !array_key_exists('autodetect_obfuscate', $data)
        ) {
            if (empty($data['obfuscate'])) {
                $data['obfuscate'] = false;
                $data['autodetect_obfuscate'] = false;
            }
            elseif ($data['obfuscate'] === 'auto') {
                $data['obfuscate'] = null;
                $data['autodetect_obfuscate'] = true;
            }
            else {
                $data['obfuscate'] = true;
                $data['autodetect_obfuscate'] = false;                
            }
        }
        // normalize 'autodetect_obfuscate'
        if (!array_key_exists('autodetect_obfuscate', $data)) {
            if ($options['on'] === 'create') {
                $data['autodetect_obfuscate'] = !empty($this->schema['autodetect_obfuscate']['default']);
            }
            elseif (!empty($data['id'])) {
                $data['autodetect_obfuscate'] = $this->findFieldBy('autodetect_obfuscate', 'id', $data['id']);
            }
        }
        if (!empty($data['autodetect_obfuscate'])) {
            // see App::getContent() for obfuscate autodetection
            $data['obfuscate'] = null;
        }
        
        return parent::normalize($data, $options);
    }
    
    /**
     * Checks if provided value is unique for actual lang specified by 'lang' in $data or otherwise by App::$lang.
     * 
     * Except of standart Model::validate_XYZ() method arguments following additional argument can be passed:
     * 
     * @param array $conditions Optional. Conditions to filter out unigue value records group.
     *      Defaults to empty array().
     */
    public function validate_uniqueForLang($fieldValue, $fieldName, $data, &$validation, $conditions = array()) {
        $conditions = DB::nestConditions((array)$conditions);
        $conditions[$fieldName] = $fieldValue;
        if (!empty($data['lang'])) {
            $conditions['lang'] = $data['lang'];
        }
        else {
            $conditions['lang'] = App::$lang;
        }
        if (!empty($data['id'])) {
            $conditions['id !='] = $data['id'];
        }
        $result = $this->findFirst(array('conditions' => $conditions));
        return empty($result);
    }   
    
    public function validate_relativeOrAbsoluteUrl($fieldValue) {
        return substr($fieldValue, 0, 1) === '/' || Validate::url($fieldValue);
    }
    
    /**
     * Returns unique slug for provided locator. If the locator is url (relative or
     * absolute) then it is returned unchanged. If the locator is slug then unique 
     * variation (adding numbers at the end) is returned.
     * 
     * NOTE: Screen names are considered too.
     * 
     * @param string $locator
     * @param array $options Following are available:
     *      - 'id' (int) Content id. If provided then specifiee content whose slug
     *          is excluded from comparison. Moreover 'lang' options is set according
     *          to specified content. This serves to get unique slugs for existing 
     *          contents. Defaults to NULL.
     *      - 'lang' (string) Lang for which the slug must be unique. If not provided
     *          then the slug is unique for all langs. If 'id' option is provided then
     *          lang is set according to specified content.
     * 
     * @return string
     */
    public function getUniqueSlug($locator, $options = array()) {
        $defaults = array(
            'lang' => null,
            'id' => null,
        );
        $options = array_merge($defaults, $options);
        // mail links and absolute URLs are returned unchanged (e.g. mailto:<EMAIL>, http://something.com, https://something.com)
        if (
            substr($locator, 0, 7) == 'mailto:' 
            || substr($locator, 0, 7) == 'http://' 
            || substr($locator, 0, 8) == 'https://'
        ) {
            return $locator;
        }
        // relative URLs are returned unchanged (e.g. /slug)
        elseif (substr($locator, 0, 1) == '/') {
            return $locator;
        }
        
        // find the slug unique value
        // - if id is specified then set lang explicitly
        if (
            $options['id']
            && ($lang = $this->findFieldBy('lang', 'id', $options['id']))
        ) {
            
            $options['lang'] = $lang;
        }
        $conditions = array();
        // - if id is specified then apply it
        if ($options['id']) {
            $conditions[$this->name . '.' . $this->primaryKey . ' !='] = $options['id'];
        }
        // - if lang is provided then apply it
        if ($options['lang']) {
            $conditions[$this->name . '.lang'] = $options['lang'];
        }
        // slug must be at least 3 chars long to not perplex it with lang code
        $locator = Str::slugize($locator);
        $locator = str_pad($locator, 3, '-');
        $locator = $this->getUniqueFieldValue($this->name . '.locator', $locator, array(
            'conditions' => $conditions,  
            // avoid screen names
            'avoidValues' => App::getScreens(),
        ));
                
        return $locator;
    }
    
    /**
     * Overrides Model::save() to allow tree node movement on save.
     * 
     * The node is moved if the $data contains 'id', 'parent_id', 'new_parent_id'
     * and the 'parent_id' does not equals to 'new_parent_id'.
     * 
     * For details and params see Model:.save()
     * 
     * @param array $data
     * @param array $options Model::save() options plus following are available
     *      - 'addRedirection' (bool) Adds redirection on change of locator. Defaults to !ON_LOCALHOST.
     * 
     * @return boolean array|bool Array of updated or created record data containing id of created record
     *      and other values after normalization or file uploads. FALSE if validation fails or 
     *      if the moved or new parent node does not exist or if the node tries to be moved under one of its childs.  
     */
    public function save($data, $options = array()) {
        $options = array_merge(array(
            'addRedirection' => !ON_LOCALHOST,
        ), $options);
        // get old record for redirection addition here below
        $oldRecord = array();
        if (
            $options['addRedirection']
            && !empty($data['id'])
            && array_key_exists('locator', $data)
        ) {
            $oldRecord = $this->findFirstBy('id', $data['id'], array(
                'fields' => array('locator'),
            ));
        }
        // move tree node 
        // - check for new_previous_sibling_id
        if (
            !empty($data['id']) // on update
            && !empty($data['new_previous_sibling_id'])
            && !empty($data['previous_sibling_id'])
            && $data['new_previous_sibling_id'] != $data['previous_sibling_id']
        ) {
            if (!$this->moveTreeNodeBehind($data['id'], $data['new_previous_sibling_id'])) {
                return false;
            }
            // remove other three fields to no preak tree structure
            unset($data['parent_id']);
            unset($data['path']);
            unset($data['sort']);
        }
        // - check for new_parent_id
        elseif (
            !empty($data['id']) // on update
            && !empty($data['new_parent_id'])
            && !empty($data['parent_id'])
            && $data['new_parent_id'] != $data['parent_id']
        ) {
            if (!$this->moveTreeNode($data['id'], $data['new_parent_id'])) {
                return false;
            }
            // remove other three fields to no preak tree structure
            unset($data['parent_id']);
            unset($data['path']);
            unset($data['sort']);
        }
        // update 'hidden' for whole tree branch
        if (
            isset($data['hidden'])
            && !empty($data['id'])
        ) {
            if(!$this->updateInTree($data['id'], array('hidden' => $data['hidden']))) {
                return false;
            }
        }
        // update 'deleted' for whole tree branch
        if (
            array_key_exists('deleted', $data)
            && !empty($data['id'])
        ) {
            if(!$this->updateInTree($data['id'], array_intersect_key($data, array(
                'deleted' => null,  // value does not matter here
                'active' => null,   // value does not matter here
            )))) {
                return false;
            }
        }
        App::loadModel('App', 'ContentBlockInstance');
        $BlockInstance = new ContentBlockInstance();
        if (
            !empty($data['id'])
            && !$BlockInstance->saveOwnerInstancesData($data, 'App.WebContent', $data['id'])
        ) {
            return false;
        }
        // save its data
        if (!($result = parent::save($data, $options))) {
            return false;
        }
        // add redirection if locator was changed
        if (
            $options['addRedirection']
            && !empty($oldRecord['locator'])
            && !empty($result['locator'])
            && $oldRecord['locator'] != $result['locator']
        ) {
            $oldLocator = App::getUrl(array(
                'locator' => $oldRecord['locator'],
            ));
            $newLocator = App::getUrl(array(
                'locator' => $result['locator'],
            ));
            App::loadModel('App', 'UrlRedirection');
            $UrlRedirection = new UrlRedirection();
            $UrlRedirection->add($oldLocator, $newLocator);
        }
        return $result;
    }
    
    /**
     * Deletes webcontent node and its childs with related webimages and content blocks
     * 
     * @param int $id Id of the node to be removed.
     * @param array $options Optional. Options used for deleting the node and its
     *      childs. See Model::delete() method.
     * 
     * @return bool|array Returns FALSE if the specified node does not exist. 
     *      Returns empty array() if node cannot be removed (it is permanent).
     *      Othervise returns array of removed nodes ids.
     * 
     * @throws Exception_DB_TablesReservationFailure
     */
    public function deleteTreeNode($id, $options = array()) {
        $defaults = array(
            'cleanUpFiles' => true,
            'reserve' => true,
            'softDelete' => true,
        );
        $options = array_merge($defaults, $options);
        // check if tree node is permanent
        if ($this->findFieldBy('permanent', 'id', $id)) {
            return array();
        }
        if ($options['reserve']) {
            $this->reserveTables('WebContent_deleteTreeNode', array(
                'WebContent',
                'WebImage',
                'ContentBlockInstance',
            ));
        }
        if (
            ($removedIds = parent::deleteTreeNode($id, array_merge($options, array(
                'reserve' => false,
            ))))
        ) {            
            App::loadModel('App', 'WebImage');
            $Image = new WebImage();
            $Image->delete(array(
                'conditions' => array(
                    'run_web_contents_id' => $removedIds,
                ),
                'cleanUpFiles' => $options['cleanUpFiles'],
                'reserve' => false,
                'softDelete' => $options['softDelete'],
            ));
            // delete content block instances
            App::loadModel('App', 'ContentBlockInstance');
            $Instance = new ContentBlockInstance();
            $ownerModel = $this->module . '.' . $this->name;
            $Instance->delete(array(
                'processContentBlockData' => true,
                'conditions' => array(
                    'owner_model' => $ownerModel,
                    'owner_id' => $removedIds,
                ),
                'cleanUpFiles' => $options['cleanUpFiles'],
                'reserve' => false,
                'softDelete' => $options['softDelete'],
            ));            
        }
        if ($options['reserve']) {
            $this->unreserveTables('WebContent_deleteTreeNode');
        }
        return $removedIds;
    }
                           
    /**
     * Converts provided $searchData into find options used by Model::find().
     * 
     * @param array $searchData Search data containing at least 'keywords' item.
     *      All other item are dependent by concrete implementation of this method on project.
     * @param array $options Following are avalible:
     *      - 'findOptions' (array) Find options to merge generated options into. 
     *          Defaults to empty array().
     *      - 'relevanceField' (bool) If TRUE then search relevance is returned 
     *          as field '_relevance'. Defaults to FALSE.
     * 
     * @return array|bool Find options created according to provided $searchData.
     *      If $findOptions is provided the new options are merged into existing.
     *      Returns FALSE if $searchData are invalid.
     */
    public function getSearchFindOptions($searchData, $options = array()) {
        $options = array_merge(array(
            'findOptions' => array(),
            'relevanceField' => false,
        ), $options);
        $findOptions = &$options['findOptions'];
        // normalize and validate
        $searchData = $this->normalize($searchData, array('alternative' => 'search'));
        $valid = $this->validate($searchData, array(
            'normalize' => false,
            'alternative' => 'search',
        ));
        if (!$valid) {
            return false;
        }
        
        // ensure existence of folowing items in find options
        $findOptions['joins'] = (array)Sanitize::value($findOptions['joins']);
        $findOptions['conditions'] = (array)Sanitize::value($findOptions['conditions']);
        $findOptions['conditions'] = DB::nestConditions($findOptions['conditions']);
        $findOptions['literals'] = (array)Sanitize::value($findOptions['literals']);
        $findOptions['literals']['conditions'] = (array)Sanitize::value($findOptions['literals']['conditions']);
        if ($options['relevanceField']) {
            $findOptions['fields'] = (array)Sanitize::value($findOptions['fields']);
            $findOptions['literals']['fields'] = (array)Sanitize::value($findOptions['literals']['fields']);
        }
        else {
            $findOptions['order'] = (array)Sanitize::value($findOptions['order']);
            $findOptions['literals']['order'] = (array)Sanitize::value($findOptions['literals']['order']);
        }
        
        // set default conditions
        $findOptions['conditions'][] = array(
            'WebContent.lang' => App::$lang,
            'WebContent.active' => true,
            'WebContent.pid' => array(
                null, '',
                'contacts',
                'App.cookiesPrivacyPolicyInfo',
            )
        ); 
        
        // if you order results also by some user selected custom sort (e.g. by price)
        // then strict search conditions (AND between keywords) must by generated. 
        // Loose search conditions (OR between keywords) can be generated only in case of
        // pure relevence order as in this case the results containing all keywords are
        // placed as first by relevance. Even in case of user custom sort the relevance order 
        // can be used too but only as secondary order.
        $strictSearchConditions = !empty($findOptions['order']);
        
        // keywords
        $keywords = self::parseSearchKeywords($searchData['keywords']);

        // check if we are searching for a single integer
        $singleIntegerSearch = (
            count($keywords) === 1
            && preg_match('/^[0-9]+$/', $keywords[0])
        );

        // check if we are searching for strings only (there is no integer)
        $stringOnlySearch = true;
        foreach ($keywords as $keyword) {
            if (preg_match('/^[0-9]+$/', $keyword)) {
                $stringOnlySearch = false;
                break;
            }
        }
        
        App::loadModel('App', 'ContentBlockInstance');
        $BlockInstance = new ContentBlockInstance();
        $findOptions['joins'] = array_merge($findOptions['joins'], array(
            $BlockInstance->getOwnerInstancesJoin('App.WebContent'),
        ));

        // prepare order by relevance (can be adjusted on project)
        // - define relevance levels from highest to lowest. Add any number 
        // of your own conditions here or adjust the existing conditions.
        // The default operator applied betweed relevance conditions for each 
        // keyword is OR - it means the relevance condition is TRUE if at least
        // one of keywords matches this condition. You can change the "between" operator
        // to 'AND' like {relevanceCondition} => 'AND' - it means you provide
        // the conditions like key and the value is 'AND'. By the way the 'AND'
        // is more relevant as the condition is TRUE only if it matches all keywords.
        // If you add fields from other models then actualize also the joins above
        $relevanceExpressions = array(
            // matchs whole name for phrase
            'WebContent.name = ":k:"' => 'PHRASE',
            // matchs beginning of name for phrase
            'WebContent.name LIKE ":k:%"' => 'PHRASE',
            // matchs anywhere in name for phrase
            'WebContent.name LIKE "%:k:%"' => 'PHRASE',
            // matchs anywhere in text for phrase
            'ContentBlockInstance.content_block_data LIKE "%:k:%"' => 'PHRASE',
            // matchs whole word in name for all keywords
            '(WebContent.name LIKE "% :k:" OR WebContent.name LIKE ":k: %" OR WebContent.name LIKE "% :k: %")' => 'AND',
            // matchs anywhere in name for all keywords
            'WebContent.name LIKE "%:k:%"' => 'AND',
            // matchs anywhere in text for all keywords
            'ContentBlockInstance.content_block_data LIKE "%:k:%"' => 'AND',
            // matchs whole name for one of keywords
            'WebContent.name = ":k:"',
            // matchs whole word in name for one of keywords
            '(WebContent.name LIKE "% :k:" OR WebContent.name LIKE ":k: %" OR WebContent.name LIKE "% :k: %")',
            // matchs begining of word in name for one of keywords
            '(WebContent.name LIKE ":k:%" OR WebContent.name LIKE "% :k:%")',
            // matchs anywhere in name for one of keywords
            'WebContent.name LIKE "%:k:%"',
            // matchs anywhere in text for one of keywords
            'ContentBlockInstance.content_block_data LIKE "%:k:%"',
        );
        // - get relevance order sql
        if (
            ($relevanceSql = self::getSearchRelevance(
                $relevanceExpressions,
                $searchData['keywords'],
                $keywords
            ))
        ) {                
            if ($options['relevanceField']) {
                $findOptions['fields'][] = $relevanceSql . ' AS `_relevance`';
                $findOptions['literals']['fields'][] = $relevanceSql . ' AS `_relevance`';
            }
            else {
                $findOptions['order'][] = $relevanceSql . ' DESC';
                $findOptions['literals']['order'][] = $relevanceSql . ' DESC';                    
            }
        }

        // search for keywords
        $searchFields = array(
            'WebContent.name',
            //'WebContent.locator',
            //'WebContent.seo_title',
            //'WebContent.seo_description',
            //'WebContent.seo_keywords',
            //'WebContent.text',
            'ContentBlockInstance.content_block_data',
        );
        if (
            ($searchConditions = self::getSearchConditions(
                $searchFields, 
                $keywords, 
                array(
                    'strict' => $strictSearchConditions
                )
            ))
        ) {
            $findOptions['conditions'][] = $searchConditions;
        }
        
        // because of joins with content blocks some contents can occure multiple times
        $findOptions['group'] = 'WebContent.id';
        
        return $findOptions;
    }    
                           
    /**
     * Searches for contents according to provided keywords
     * 
     * @param string|array $keywords Keywords to searched in web contents for.
     *      Either string containing words separated by space or array of words.
     * 
     * @return array Array of contents. Each content contains also item 'excerpt'
     *      with highlighted keywords.
     */
    public function search($keywords) {
        
        // normalize incoming keywords
        if (!is_array($keywords)) {
            $keywords = array_unique(array_filter(explode(' ', (string)Sanitize::value($keywords))));
            foreach($keywords as &$keyword) {
                $keyword = trim($keyword, '/\\\'%"');
            }
            // unset reference
            unset($keyword);
        }
        
        $literals = array();
        
        // prepare order by relevance
        $order = array();
        if (!empty($keywords)) {
            $fieldSqlA = '';
            $fieldSqlB = '';
            $fieldSqlC = '';
            foreach($keywords as $keyword) {
                $keyword = DB::escape($keyword);
                if (!empty($fieldSqlA)) {
                    $fieldSqlA .= ' OR ';
                    $fieldSqlB .= ' OR ';
                    $fieldSqlC .= ' OR ';
                }   
                $fieldSqlA .= 'WebContent.name LIKE "' . $keyword . '"';
                $fieldSqlB .= '(WebContent.name LIKE "% ' . $keyword . '" OR WebContent.name LIKE "' . $keyword . ' %" OR WebContent.name LIKE "% ' . $keyword . ' %")';
                $fieldSqlC .= 'WebContent.name LIKE "%' . $keyword . '%"';
            }
            array_unshift($order, 'IF (' . $fieldSqlA . ', 1, IF(' . $fieldSqlB . ', 0.8, IF(' . $fieldSqlC . ', 0.6, 0))) DESC');
            $literals['order'][] = 'IF (' . $fieldSqlA . ', 1, IF(' . $fieldSqlB . ', 0.8, IF(' . $fieldSqlC . ', 0.6, 0))) DESC';
        }        
        
        // prepare conditions
        $conditions = array(
            'WebContent.active' => true,
            'WebContent.locator !=' => null,
            'WebContent.locator !=' => '',
            'WebContent.lang' => App::$lang,
        );
        $searchFields = array(
            'WebContent.locator',
            'WebContent.seo_title',
            'WebContent.seo_description',
            'WebContent.seo_keywords',
            'WebContent.text',
            'WebContent.name',
        );
        $kewordsSql = '';
        foreach ($searchFields as $field) {
            $fieldSql = '';
            foreach($keywords as $keyword) {
                if ($fieldSql) {
                    $fieldSql .= ' AND ';
                }
                $fieldSql .= $field . ' LIKE \'%' . $keyword . '%\'';
            }
            if ($kewordsSql) {
                $kewordsSql .= ' OR ';
            }
            $kewordsSql .= $fieldSql;
        }
        $conditions[] = array($kewordsSql);
        $literals['conditions'] = $kewordsSql;
        
        // retrieve contents
        $contents = $this->find(array(
            'fields' => array(
                'WebContent.name',
                'WebContent.locator',
                'WebContent.text',
            ),
            'conditions' => $conditions,
            'literals' => $literals,
            'order' => $order,
        ));
        
        // create search resume
        foreach($contents as &$content) {
            $excerpt = '';
            if ($content['name']) {
                $excerpt .= $content['name'] . ': ' ;
            }
            if ($content['locator']) {
                $excerpt .= $content['locator'] . ' ' ;
            }
            $excerpt .= $content['text'];
            $excerpt = Sanitize::htmlToText($excerpt);
            $excerpt = Str::getExcerpt($excerpt, array('includedWords' => $keywords));
            $excerpt = Str::highlightWords($excerpt, $keywords);
            $content['excerpt'] = $excerpt;
        }
        
        return $contents;
    }    
    
    /**
     * Duplicates specified content, it means a copy of specified contentis created
     * under the same parent.
     * 
     * @param int $id Id of content to be duplicated.
     * @param array $options Following are available:
     *      - 'copyData' (array|callable) Array of data used to prepare copied nodes data (
     *          merged into).  It can be also a callable with original node data on input
     *          and returning data of copied node. Copy data can be used to tweak values
     *          of new created nodes. Defaults to empty array().
     *      - 'copyChilds' (bool) If TRUE then also children of specified content
     *          are copied under the content duplicate. In such a case see Model::copyTreeNode()
     *          form more $options. Defaults to FALSE.
     * 
     * @return array|bool Array of copy ids pairs like 'sourceNodeId' => 'targetNodeId'.
     *      FALSE if validation fails.
     * @throws Exception
     */
    public function duplicate($id, $options = array()) {
        $defaults = array(
            'copyData' => array(),
            'copyChilds' => false,
        );
        $options = array_merge($defaults, $options);
        $content = $this->findFirstBy('id', $id);
        if (!$content) {
            throw new Exception(__e(__FILE__, 'Unexisting content id %s', $id));
        }
        try {
            DB::startTransaction('WebContent_copyContent');            
            if (Validate::callableFunction($options['copyData'])) {
                $contentCopy = array_merge($content, call_user_func_array($options['copyData'], array($content)));
            }
            else {
                $contentCopy = array_merge($content, $options['copyData']);
            }
            $contentCopy = $this->prepareCopyData($contentCopy);
            if (!($contentCopy = $this->addTreeNode($contentCopy['parent_id'], $contentCopy, $options))) {
                $this->cleanUpFiles(false);
                DB::rollbackTransaction('WebContent_copyContent');
                return false;
            }
            if ($options['copyChilds']) {
                if (
                    !($copyIds = $this->copyTreeNode(
                        $content['id'],
                        $contentCopy['id'],
                        $options
                    ))
                ) {
                    DB::rollbackTransaction('WebContent_copyContent');
                    return false;
                }
                $copyIds[$content['id']] = $contentCopy['id'];
            }
            else {
                $copyIds = array($content['id'] => $contentCopy['id']);
            }
            // copy images
            App::loadModel('App', 'WebImage');
            $Image = new WebImage();
            $sourceIds = array_keys($copyIds);
            $images = $Image->findBy('run_web_contents_id', $sourceIds);
            foreach($images as $image) {
                $image['run_web_contents_id'] = $copyIds[$image['run_web_contents_id']];
                if (!$Image->copy($image)) {
                    DB::rollbackTransaction('WebContent_copyContent');
                    $this->setErrors($Image);
                    return false;
                }
            }
            // copy content block instances
            App::loadModel('App', 'ContentBlockInstance');
            $Instance = new ContentBlockInstance();
            $instances = $Instance->find(array(
                'conditions' => array(
                    'owner_model' => 'App.WebContent',
                    'owner_id' => $sourceIds
                )
            ));
            foreach($instances as $instance) {
                $instance['owner_id'] = $copyIds[$instance['owner_id']];
                if (!$Instance->copyInstance($instance)) {
                    DB::rollbackTransaction('WebContent_copyContent');
                    $this->setErrors($Instance);
                    return false;
                }
            }
        } 
        catch (Throwable $e) {
            DB::rollbackTransaction('WebContent_copyContent');
            throw $e;
        }
        DB::commitTransaction('WebContent_copyContent');
        return $copyIds;
    }    
    
    /**
     * Returns TRUE if the provided content record is page (not top level section)
     * 
     * Used to build smart inded record actions in WebContents::admin_index()
     * 
     * @param array $record Content record
     * @return boolean
     */
    public function isPage($record) {
        $parents = $this->findUpInTree($record, array(
            'order' => 'WebContent.sort ASC',
        ));
        $nodeLevel = count($parents);
        $sectionPid = Sanitize::value($parents[1]['pid'], false);
        return ($sectionPid === 'sliders' && $nodeLevel > 3 || $sectionPid !== 'sliders' && $nodeLevel > 2);
    }
    
    /**
     * Returns list of active sliders for provided lang
     * 
     * @param string $lang Lang to retrieve sliders for
     * @param int& $rootId Optional output. Passed by reference. Returns the provided root id.
     *      If no root is found according to provided $root specification then $rootId
     *      is set to NULL. ATTENTION: If $root is an integer number (e.g. 2 or '2'),
     *      then it is just copied to this output without checking its existence!
     * 
     * @return array Sliders list usable for selectbox
     */
    public function getSlidersSelectList($lang, &$rootId = null) {
        $sliders = $this->findTreeSelectList(
            array(
                'conditions' => array(
                    'pid' => 'sliders', 
                    'lang' => $lang
                ),
            ),
            array(
                // avoid content itself and its childs
                'conditions' => array(
                    'active' => true,
                ),
                'depth' => 1,
            ),
            $rootId  
        );   
        if (!empty($sliders)) {
            $sliders = array('' => '') + $sliders;
        }
        return $sliders;
    }
    
    /**
     * Returns list of pages for provided lang
     * 
     * @param string $lang Lang code to retrieve web contents for
     * @param array $options Options of Model::find() plus following:
     *      - 'sectionPid' (string|array) Single section pid or array of such pids.
     *          Only specified sections are included and option 'excludeSpecificSections'
     *          is ignored. Defaults to NULL.
     *      - 'excludeSpecificSections' (bool) If TRUE then sections specified in 
     *          WebContent::$specificSections are excluded. This option is ignored
     *          if 'sectionPid' option is specified. Defaults to FALSE.
     *      - 'excludeNode' (array) Data of node to be excluded. Must contain at least 'id' and 'path'.  
     *          Defaults to NULL.
     *      - 'labelField' (string) Name of field to be used for list labels.
     *          Qualifier is added implicitly according to model alias. Defaults to 'name'.
     *      - 'indent' (string) Indent to be used for nested labels indentation. 
     *          To make no indentation set this to '' or NULL. Defaults to 7 nonbreakable spaces (& nbsp;).
     *      - 'accumulate' (bool) If TRUE then labels are created accumulated, e.g. 
     *          '   Fruit > Apple'. Defaults to FALSE.
     *      - 'separator' (string) Separator used in accumulated labels. Defaults to ' > '.
     *      - 'depth' (integer) Number of nested levels to create list for. If NULL
     *          then list is created for all available levels in $tree data. Defaults to NULL.
     *      - 'template' (string|array) Template(s) to display label(s) on provided levels, e.g.
     *          array (1 => '>>>:l:'). If level has its template defined or there is 'default'
     *          template defined then that template is used to display the level label.
     *          Use the insert :l: to place label into template. Defaults to NULL.
     *      - 'prependRoot' (bool) If TRUE then root node is prepended on the top of the list.
     *          Defaults to FALSE.
     *      - 'firstPlaceholder' (bool|string) If nonepmty value then first items
     *          placeholders are created at the begining of each level. Use the insert :l: 
     *          to place parent node label into placeholder. If TRUE then '--- first in :l:' is used 
     *          as placeholder. Defaults to FALSE.
     *      - 'firstPlaceholderStart' (int) First placeholder start level. Defaults to 1.
     * @return type
     */
    public function getPagesSelectList($lang, $options = array()) {
        // get tree select list conditions
        $options['conditions'] = $this->getAdministratedRecordsConditions($lang, $options);
        $pages = $this->findTreeSelectList(
            array(
                'conditions' => array('parent_id' => null, 'lang' => $lang),
            ),
            $options
        );
        return $pages;
    }
        
    //
    // METHODS TO CREATE SMART FORM
    //
    
    /**
     * Returns conditions to retrieve records visible in admin methods: WebContents::admin_index(), ::admin_showTree() and
     * in tree select lists in edit forms
     * 
     * @param string $lang Lang code to retrieve web contents for
     * @param array $options Folloving are available:
     *      - 'sectionPid' (string|array) Single section pid or array of such pids.
     *          Only specified sections are included and option 'excludeSpecificSections'
     *          is ignored. Defaults to NULL.
     *      - 'excludeSpecificSections' (bool) If TRUE then sections specified in 
     *          WebContent::$specificSections are excluded. This option is ignored
     *          if 'sectionPid' option is specified. Defaults to FALSE.
     *      - 'excludeNode' (array) Data of node to be excluded. Must contain at least 'id' and 'path'.  
     *          Defaults to NULL.
     * @return array Array of conditions to be used in Model::find() options
     */
    public function getAdministratedRecordsConditions($lang, $options = array()) {
        $defaults = array(
            'sectionPid' => null,
            'excludeSpecificSections' => false,
            'excludeNode' => null,
        );
        $options = array_merge($defaults, $options);
        
        // specify tree conditions
        $conditions = array();
        $includePaths = array();
        $includeIds = array();
        $excludePaths = array();
        $excludeIds = array();
        // - include only specified specific section(s)
        if ($options['sectionPid']) {
            $includePaths = $this->findList(array(
                'fields' => array('CONCAT(`path`, `id`, \'-\') AS `path`'),
                'conditions' => array(
                    'pid' => $options['sectionPid'],
                    'lang' => $lang,
                ),
                'literals' => array(
                    'fields' => true,
                ),
            ));
            $includeIds = array_keys($includePaths);
        }
        // - exclude specific webcontents (sliders, news, ...) if general webcontent tree is shown
        elseif ($options['excludeSpecificSections']) {
            $excludePaths = $this->findList(array(
                'fields' => array('CONCAT(`path`, `id`, \'-\') AS `path`'),
                'conditions' => array(
                    'pid' => $this->specificSections,
                    'lang' => $lang,
                ),
                'literals' => array(
                    'fields' => true,
                ),
            ));
            $excludeIds = array_keys($excludePaths);
        }
        if ($options['excludeNode']) {
            $excludeIds[] = $options['excludeNode']['id'];
            $excludePaths[] = $options['excludeNode']['path'] . $options['excludeNode']['id'] . '-';
        }
        if (!empty($includeIds)) {   
            $conditions[] = array(
                'id' => $includeIds,
                'OR',
                'path ~%' => $includePaths,
            );
        }
        if (!empty($excludeIds)) {            
            $conditions['id !='] = $excludeIds;
            $conditions['path !~%'] = $excludePaths;
        }
        // - do not show hidden items for other than admin user 
        if (App::getUser('Group.pid') !== 'admins') {
            $conditions['hidden'] = false;
        }    
                
        return $conditions;
    }
    
    /**
     * Return starting common part of smart form used to edit contents
     * 
     * @return array Definition of 'fields' option for Html::smartForm()
     */
    public function getSmartFormCommonStartPart() {
        // common start fields
        $form = array(
            'id' => array('type' => 'hidden'),
            'parent_id' => array('type' => 'hidden'),
            'previous_sibling_id' => array('type' => 'hidden'),
            'lang' => array('type' => 'hidden'),
            'path' => array('type' => 'hidden'),
        );
        return $form;
    }
    
    /**
     * Return ending common part of smart form used to edit contents
     * 
     * @return array Definition of 'fields' option for Html::smartForm()
     */
    public function getSmartFormCommonEndPart() {
        // add admin user fields
        if (App::getUser('Group.pid') === 'admins') {
            $form = array(
                array('h1' => __a(__FILE__, 'Info')),
                array('row'),
                    array(
                        'field' => 'id', 
                        'label' => 'Id', 
                        'type' => 'display'
                    ),
                    array(
                        'field' => 'pid', 
                        'label' => 'Pid'
                    ),
                    array('field' => 'obfuscate', 
                        'label' => __a(__FILE__, 'Obfuscate email addresses in content text'), 
                        'hint' => __a(__FILE__, 'Use "autodetect" in case of emails created manually by user. Use "yes" in case of emails in content generated by snippet.'),
                        'type' => 'select',
                        'options' => array(
                            'auto' => __a(__FILE__, 'autodetect'),
                            '1' => __a(__FILE__, 'yes'),
                            '0' => __a(__FILE__, 'no'),
                        )
                    ),
                array('/row'),
                array('row'),
                    array(
                        'field' => 'permanent', 
                        'label' => 'Permanent',
                        'hint' => __a(__FILE__, 'Permanent contents cannot be deleted')
                    ),
                    array(
                        'field' => 'hidden', 
                        'label' => 'Hidden',
                        'hint' => __a(__FILE__, 'Hidden contents (and their child contents) are visible only for Admins users. Webmasters do not see hidden contents.')
                    ),
                array('/row'),
            );
        }
        else {
            $form = array(
                array('h1' => __a(__FILE__, 'Info')),
                array('row'),
                    array(
                        'field' => 'id', 
                        'label' => 'Id', 
                        'type' => 'display'
                    ),
                    array(
                        'field' => 'pid', 
                        'type' => 'hidden'
                    ),
                array('/row'),
            );            
        }
        return $form;
    }
    
    /**
     * Return smart form used to edit section (top level container) contents
     * 
     * @param array $data Content data retrieved from DB
     * 
     * @return array Definition of 'fields' option for Html::smartForm()
     */
    public function getSmartFormForSection($data) {
        // get tree select list conditions
        $conditions = $this->getAdministratedRecordsConditions($data['lang'], array(
            'excludeSpecificSections' => true,
            'excludeNode' => $data,
        ));
        $form = $this->getSmartFormCommonStartPart();
        // section fields
        $form = array_merge($form, array(
            array('h1' => __a(__FILE__, 'Basic parameters')),
            array('row', 'columns' => 2),
                array('col', 'columns' => 2, 'deviceSize' => 'md'),
                    array('row'),
                       array('field' => 'name', 'label' => __a(__FILE__, 'Section name')),
                       array('field' => 'active', 'label' => __a(__FILE__, 'Active')),
                    array('/row'),
//                        array('row', 'columns' => 2),
//                           array('field' => 'image_name', 'label' => __a(__FILE__, 'Section image')),
//                        array('/row'),
                    array('row'),
                        array(
 //                                        'field' => 'new_parent_id', 
                            'field' => 'new_previous_sibling_id', 
                            'label' => __a(__FILE__, 'Location in tree behind'),
                            'type' => 'select',
                            'options' => $this->findTreeSelectList(
                                array(
                                    'conditions' => array('parent_id' => null, 'lang' => $data['lang']),
                                ),
                                array(
                                    'firstPlaceholder' => true,
                                    'conditions' => $conditions,
                                )
                            ),
                        ),
                        array(
                            'field' => 'has_side_content', 
                            'label' => __a(__FILE__, 'Display side content'),
                        ),
                    array('/row'),
                array('/col'),
                array('col', 'style' => 'text-align: center;'),
//                        array(
//                            'field' => 'image_name', 
//                            'type' => 'image',
//                            'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/image_name/' . $this->data['id'],
//                            //'style' => 'height:195px',
//                            'style' => 'display:block;width:100%;',
//                        ),
                array('/col'),
            array('/row'), 
        ));
        // add admin user fields
        $form = array_merge($form, $this->getSmartFormCommonEndPart());
        return $form;
    }
    
    /**
     * Return smart form used to edit page contents
     * 
     * @param array $data Content data retrieved from DB
     * @param string $sectionPid Pid of actula section the page is placed in. Defaults to NULL.
     * 
     * @return array Definition of 'fields' option for Html::smartForm()
     */
    public function getSmartFormForPage($data, $sectionPid = null) {
        // get tree select list conditions
        $conditions = $this->getAdministratedRecordsConditions($data['lang'], array(
            'excludeSpecificSections' => true,
            'excludeNode' => $data,
        ));
        // common start fields
        $form = $this->getSmartFormCommonStartPart();
        // news page fields
        $sliders = $this->getSlidersSelectList($data['lang']);
        $form = array_merge($form, array(
            array('h1' => __a(__FILE__, 'Basic parameters')),
            array('row'),
                array('field' => 'name', 'label' => __a(__FILE__, 'Page name')), 
                array(
                    'field' => 'locator', 
                    'label' => __a(__FILE__, 'Page locator'),
                    'hint' => __a(
                        __FILE__, 
                        'Page URL can be provided in 3 ways:<ul><li><b>actual page URL name</b>, e.g. <code>this-page</code> - it means that this page is accesses at <kbd>:urlBase:/<b>this-page</b></kbd></li><li><b>reference to another page</b> starting by slash <code>/</code>, e.g. <code>/that-page</code> - it means that menu item "This page" points to <kbd>:urlBase:<b>/that-page</b></kbd> and content of "That page" will be displayed. This can be used to make different menu items to point to the same content</li><li><b>absolute address</b> starting by <code>http://</code>, e.g. <code>http://google.com</code> - it means that menu item "This page" points to <kbd><b>http://google.com</b></kbd>. This can be used to make menu items to point to external address. Absolute address is always opened in new browser tab</li></ul>',
                        array(
                            'urlBase' => App::$urlBase,
                        )
                    ),
                ),
                array('field' => 'active', 'label' => __a(__FILE__, 'Active')),
            array('/row'),
            array('if' => $data['pid'] === 'promotion'),
                array('row', 'columns' => 2),
                    array('field' => 'resume', 
                        'label' => __a(__FILE__, 'Promotion description'), 
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Text',
                            'bodyClass' => '-cke-promotion-resume',
                        ),
                    ),
                array('/row'),
            array('endif'), // array('if' => $data['pid'] === 'promotion')
            
            // PAGE CONTENT
            //array('h1' => __a(__FILE__, 'Page content')),
            array('if' => empty($data['has_side_content'])),
                array('row', 'columns' => 1),
                    array(
                        'field', 
                        'label' => __a(__FILE__, 'Page content'),
                        'type' => 'contentBlocks',
                        'ownerModel' => 'App.WebContent',
                        'ownerId' => Sanitize::value($data['id']),
                        //'contentBlockModels' => 'ContentBlock.*',
                        //'avoidContentBlockModels' => 'ContentBlock.ContentBlockSome',
                    ),
//                    array('field' => 'text', 
//                        'label' => __a(__FILE__, 'Page content'), 
//                        'type' => 'editor',
//                        'options' => array(
//                            'height' => '350px',
//                        )
//                    ),
                array('/row'),
            array('else'),
                array('row', 'columns' => array(3, 9)),
                    array('field' => 'side_text', 
                        'label' => __a(__FILE__, 'Side content'), 
                        'hint' => __a(__FILE__, 'If empty then default side content is displayed. To check that side content is really empty use "Source" mode of editor.'),
                        'type' => 'editor',
                        'options' => array(
                            'height' => '350px',
                            'bodyClass' => '-cke-content-side',
                        )
                    ),
                    array(
                        'field', 
                        'label' => __a(__FILE__, 'Main content'),
                        'type' => 'contentBlocks',
                        'ownerModel' => 'App.WebContent',
                        'ownerId' => Sanitize::value($data['id']),
                        //'contentBlockModels' => 'ContentBlock.*',
                        //'avoidContentBlockModels' => 'ContentBlock.ContentBlockSome',
                    ),
//                    array('field' => 'text', 
//                        'label' => __a(__FILE__, 'Main content'), 
//                        'type' => 'editor',
//                        'options' => array(
//                            'height' => '350px',
//                            'bodyClass' => '-cke-content-main',
//                        )
//                    ),
                array('/row'),
            array('endif'),
            array('row', 'columns' => 1),
                array(
                    'field' => 'has_side_content', 
                    'label' => __a(__FILE__, 'Display side content'),
                ),
            array('/row'),
            
            // IMAGE
            array('if' => $sectionPid === 'socialnetworksmenu' || $sectionPid === 'dealershipmenu' ),
                array('h1' => __a(__FILE__, 'Image')),
                array('row', 'columns' => 2),
                    array('col'),
                        array('row', 'columns' => 1),
                            array('col'),
                                array(
                                    'field' => 'menu_icon', 
                                    'label' => __a(__FILE__, 'Menu icon')
                                ),
                                array(
                                    'field' => 'menu_icon', 
                                    'type' => 'image',
                                    'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/menu_icon/' . $data['id'],
                                    'style' => 'display:block;width:100%;background-color:#EEE;',
                                ),
                            array('/col'),
                        array('/row'),
                    array('/col'),
    //                array('col'),
    //                    array('row', 'columns' => 1),
    //                        array('col'),
    //                            array(
    //                                'field' => 'image_name', 
    //                                'label' => __a(__FILE__, 'Header image'),
    //                                'hint' => __a(
    //                                    $this, 
    //                                    'Width at least :width:px to ensure the best quality. Height should be round :height:px.', 
    //                                    array('width' => 1200, 'height' => 805)
    //                                )
    //                            ),
    //                            array(
    //                                'field' => 'image_name', 
    //                                'type' => 'image',
    //                                'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/image_name/' . $data['id'],
    //                                //'style' => 'height:195px',
    //                                'style' => 'display:block;width:100%;',
    //                            ),
    //                            array('if' => !empty($data['image_name'])),
    //                                array('row', 'columns' => 1),
    //                                    array(
    //                                        'field' => 'header_text',
    //                                        'label' => __a(__FILE__, 'Text in header image'), 
    //                                        'type' => 'editor',
    //                                        'options' => array(
    //                                            //'toolbar' => 'Text',
    //                                            'bodyClass' => '-cke-showcase-label',
    //                                        ),
    //                                    ),
    //                                array('/row'),
    //                            array('endif'),
    //                        array('/col'),
    //                    array('/row'),
    //                array('/col'),
                array('/row'),
            array('endif'),
//            array('row', 'columns' => 2),
//                array('col'),
//                    array('row', 'columns' => 1),
//                        array('col'),
//                            array('field' => 'background_image', 'label' => __a(__FILE__, 'Background image') . ' (1800 x 640px)'),
//                            array(
//                                'field' => 'background_image', 
//                                'type' => 'image',
//                                'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/background_image/' . $data['id'],
//                                //'style' => 'height:195px',
//                                'style' => 'display:block;width:100%;',
//                            ),
//                        array('/col'),
//                    array('/row'),
//                array('/col'),
//            array('/row'),
            
            // PHOTOGALLERY
            array(
                'h1' => __a(__FILE__, 'Photogallery'), 
                'hint' => __a(
                    __FILE__, 
                    'Obrázky môžu byť pridané buď presunutím alebo kliknutím do plochy galérie. Keďže môže ísť pomerne veľké obrázky, tak aj z ohľadu voči mobilným datam návštevníkov webu, je vhodné ich pred nahratím uložiť vo formate JPG a minimalizovať ich veľkosť pomocou <a href="https://compressor.io/compress" target="_blank">online kompresora</a>'
                )
            ),
            array('row', 'columns' => 1),
                'WebImage.file' => array(
                    'type' => 'gallery',
                    'options' => array(
                        'foreignKey' => $data['id'],
                        'nameField' => 'WebImage.name',
                        'fields' => array(
                            'WebImage.file' => array('label' => __a(__FILE__, 'Image')),
                            'WebImage.name' => array('label' => __a(__FILE__, 'Name')),
                            array('field' => 'WebImage.alternative_file', 'label' => __a(__FILE__, 'Alternative thumb'), 'type' => 'file'),
                            'WebImage._delete_alternative_file' => array('label' => __a(__FILE__, 'Delete alternative thumb'), 'type' => 'checkbox'),
                            array('field' => 'WebImage.alternative_file', 'type' => 'image', 'style' => 'height:100px'),
                        ),
                        'actions' => array(
                            'load' => '/mvc/App/WebImages/admin_load',
                            'move' => '/mvc/App/WebImages/admin_move',
                            'add' => array(
                                'label' => __a(__FILE__, 'Add new image'),
                                'url' => '/mvc/App/WebImages/admin_add',
                            ),
                            'update' => array(
                                'label' => __a(__FILE__, 'Update image'),
                                'url' => '/mvc/App/WebImages/admin_update',
                            ),
                            'delete' => array(
                                'label' => __a(__FILE__, 'Delete image'),
                                'url' => '/mvc/App/WebImages/admin_delete',
                                'confirmMessage' => __a(__FILE__, 'Please confirm removal of image'),
                            ),
                        )
                    )
                ),
            array('/row'),   
            
            // SEO PARAMETERS
            array(
                'h1' => __a(__FILE__, 'SEO parameters'),
                'hint' => __a(__FILE__, 'Parameters for search engines, e.g. Google.com, Yahoo.com or Zoznam.cz')
            ),
            array('row', 'columns' => array(3,6,3)),
                array(
                    'field' => 'seo_title', 
                    'label' => __a(__FILE__, 'Page title'),
                    'hint' => __a(__FILE__, 'Title is displayed in browser tab and in Google search results. It is one of most important texts for Google on your page'),
                ),
                array('field' => 'seo_description', 
                    'label' => __a(__FILE__, 'Description'),
                    'type' => 'textarea', 
                    'style' => 'height: 34px',
                    'hint' => __a(__FILE__, 'This text is mostly used by Google in search results as text under result title. Sometimes Google decides to use some other text generated from page content'),
                ),
                array('field' => 'seo_keywords', 
                    'label' => __a(__FILE__, 'Keywords'),
                    'hint' => __a(__FILE__, 'Google does not consider much keywords. Other search engines may.')
                ),
            array('/row'),                
            array('row'),
                array('field' => 'seo_index', 
                    'label' => __a(__FILE__, 'Robots - index'),
                    'hint' => __a(__FILE__, 'Should Google include this page into search results? If you are not much sure then let it checked!')
                ),
                array('field' => 'seo_follow', 
                    'label' => __a(__FILE__, 'Robots - follow'),
                    'hint' => __a(__FILE__, 'Should Google follow links included in this page? If you are not much sure then let it checked!')
                ),
            array('/row'),
            
            // OTHER PARAMETERS
            array('h1' => __a(__FILE__, 'Other parameters')),
            array('row'),
                array(
//                    'field' => 'new_parent_id', 
                    'field' => 'new_previous_sibling_id', 
                    'label' => __a(__FILE__, 'Location in tree behind'),
                    'type' => 'select',
                    'options' => $this->findTreeSelectList(
                        array(
                            'conditions' => array('parent_id' => null, 'lang' => $data['lang']),
                        ),
                        array(
                            'firstPlaceholder' => true,
                            'conditions' => $conditions,
                        )
                    ),
                ),
                array('if' => !empty($sliders)),
                    array(
                        'field' => 'slider_id', 
                        'label' => __a(__FILE__, 'Slider'),
                        'type' => 'select',
                        'options' => $sliders
                    ),
                array('endif'),
//                array(
//                    'field' => 'layout', 
//                    'label' => __a(__FILE__, 'Page layout'),
//                    'type' => 'select',
//                    'options' => array(
//                        'App.default' => __a(__FILE__, 'Default layout'),
//                        'App.home' => __a(__FILE__, 'Home layout'),
//                    )
//                ),
            
            array('/row'),
     
        ));
        // add admin user fields
        $form = array_merge($form, $this->getSmartFormCommonEndPart());
        return $form;
    }
    
    /**
     * Return smart form used to edit slider (container) contents
     * 
     * @return array Definition of 'fields' option for Html::smartForm()
     */
    public function getSmartFormForSlider() {
        // common start fields
        $form = $this->getSmartFormCommonStartPart();
        // slider fields
        $form = array_merge($form, array(
            array('h1' => __a(__FILE__, 'Basic parameters')),
            array('row'),
               array('field' => 'name', 'label' => __a(__FILE__, 'Slider name')),
               array('field' => 'active', 'label' => __a(__FILE__, 'Active')),
            array('/row'),
//            array('row', 'columns' => 1),
//                array(
//                    'field' => 'header_text',
//                    'label' => __a(__FILE__, 'Header title'), 
//                    'type' => 'editor',
//                    'options' => array(
//                        //'toolbar' => 'Text',
//                        'bodyClass' => '-cke-showcase-label',
//                       ),
//                ),
//                array(
//                    'field' => 'resume', 
//                    'label' => __a(__FILE__, 'Header text'), 
//                    'type' => 'editor',
//                    'options' => array(
//                        'toolbar' => 'Text',
//                        'bodyClass' => '-cke-showcase-label-resume',
//                    ),
//                ),
//            array('/row'),
        ));
        // add admin user fields
        $form = array_merge($form, $this->getSmartFormCommonEndPart());
        return $form;
    }
    
    /**
     * Return smart form used to edit slide contents
     * 
     * @param array $data Content data retrieved from DB
     * 
     * @return array Definition of 'fields' option for Html::smartForm()
     */
    public function getSmartFormForSlide($data) {
        // get tree select list conditions
        $conditions = $this->getAdministratedRecordsConditions($data['lang'], array(
            'excludeSpecificSections' => false,
            'excludeNode' => $data,
        ));
        // common start fields
        $form = $this->getSmartFormCommonStartPart();
        // slide fields
        $form = array_merge($form, array(
            array('h1' => __a(__FILE__, 'Basic parameters')),
            array('row'),
                array(
                    'field' => 'name', 
                    'label' => __a(__FILE__, 'Slide name')
                ),
                array(
                    'field' => 'new_previous_sibling_id', 
                    'label' => __a(__FILE__, 'Location in tree behind'),
                    'type' => 'select',
                    'options' => $this->findTreeSelectList(
                        array(
                            'conditions' => array('pid' => 'sliders', 'lang' => $data['lang']),
                        ),
                        array(
                            'firstPlaceholder' => true,
                            'conditions' => $conditions,
                        )
                    ),
                ),
                array('field' => 'active', 'label' => __a(__FILE__, 'Active')),
            array('/row'),
            // this part of form keeps the same structure as admin forms of ContentBlockImageBanner,
            // ContentBlockTwoImagesBanner and ContentBlockTwoAsymmetricImagesBanner
            array('h1' => __a(__FILE__, 'Slajd')),
            array('row', 'columns' => 2),
                array(
                    'field' => 'image_name', 
                    'label' => __a(__FILE__, 'Image'),
                    'hint' => __a(
                        __FILE__, 
                        'Vyberte obrázok o šírke :width:px. Výška obrázku nie je stanovená no mala by byť pre všetky obrázky v prezentácii rovnaká', 
                        array('width' => 1190)
                    )
                ),
            array('/row'),
            array('row', 'columns' => 1),
                array('col'),
                    array(
                        'field' => 'image_name', 
                        'type' => 'image',
                        'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/image_name/' . $data['id'],
                        //'style' => 'height:195px',
                        'style' => 'display:block;width:100%;',
                    ),
                array('/col'),
            array('/row'),
            array('row', 'columns' => 2),
               array(
                   'field' => 'aux_02', // for veiled_image
                   'type' => 'checkbox',
                   'label' => __a(__FILE__, 'Stmaviť obrázok'),
                    'hint' => __a(__FILE__, 'Má sa obrázok stmaviť pomocou tmavého priehľadného závoja?. Toto je vhodné použiť v prípade keď biely text nie je dostatočne viditeľný nad svetlým obrázkom.'),
                ),
            array('/row'),
            array('row', 'columns' => 2),
                array(
                    'field' => 'locator', 
                    'label' => __a(__FILE__, 'URL'),
                    'hint' => __a(__FILE__, 'URL adresa, na ktorú je obrázok prelinkovaný'),
                    /*/
                    'label' => __a(__FILE__, 'Page locator'),
                    'hint' => __a(
                        __FILE__, 
                        'Page URL can be provided in 3 ways:<ul><li><b>actual page URL name</b>, e.g. <code>this-page</code> - it means that this page is accesses at <kbd>:urlBase:/<b>this-page</b></kbd></li><li><b>reference to another page</b> starting by slash <code>/</code>, e.g. <code>/that-page</code> - it means that menu item "This page" points to <kbd>:urlBase:<b>/that-page</b></kbd> and content of "That page" will be displayed. This can be used to make different menu items to point to the same content</li><li><b>absolute address</b> starting by <code>http://</code>, e.g. <code>http://google.com</code> - it means that menu item "This page" points to <kbd><b>http://google.com</b></kbd>. This can be used to make menu items to point to external address. Absolute address is always opened in new browser tab</li></ul>',
                        array(
                            'urlBase' => App::$urlBase,
                        )
                    ),
                    /*/
                ),
            array('/row'),
            array('row', 'columns' => 2),
               array(
                   'field' => 'resume', // for title
                   'type' => 'text',
                   'label' => __a(__FILE__, 'Nadpis'),
                ),
            array('/row'),
            array('row', 'columns' => 2),
               array(
                   'field' => 'header_text', // for text
                   'type' => 'textarea',
                   'label' => __a(__FILE__, 'Text'),
                ),
            array('/row'),
            array('row', 'columns' => 2),
               array(
                   'field' => 'aux_01', // for button_label
                   'type' => 'text',
                   'label' => __a(__FILE__, 'Text tlačidla'),
                   'hint' => __a(__FILE__, 'Toto tlačidlo má len vizuálny efekt ("call to action"). Na horezadané URL je prelinkovaný celý obrázok'),
                ),
            array('/row'),
            
//            // PAGE DATA
//            array('h1' => __a(__FILE__, 'Page data')),
//            array('row'),
//               array('field' => 'name', 'label' => __a(__FILE__, 'Page name')),
//               array(
//                   'field' => 'locator', 
//                   'label' => __a(__FILE__, 'Page locator'),
//                    'hint' => __a(
//                        $this, 
//                        'Page URL can be provided in 3 ways:<ul><li><b>actual page URL name</b>, e.g. <code>this-page</code> - it means that this page is accesses at <kbd>:urlBase:/<b>this-page</b></kbd></li><li><b>reference to another page</b> starting by slash <code>/</code>, e.g. <code>/that-page</code> - it means that menu item "This page" points to <kbd>:urlBase:<b>/that-page</b></kbd> and content of "That page" will be displayed. This can be used to make different menu items to point to the same content</li><li><b>absolute address</b> starting by <code>http://</code>, e.g. <code>http://google.com</code> - it means that menu item "This page" points to <kbd><b>http://google.com</b></kbd>. This can be used to make menu items to point to external address. Absolute address is always opened in new browser tab</li></ul>',
//                        array(
//                            'urlBase' => App::$urlBase,
//                        )
//                    ),
//                ),
//            array('/row'),
//                        
//            // PAGE CONTENT
//            //array('h1' => __a(__FILE__, 'Page content')),
//            array('if' => empty($data['has_side_content'])),
//                array('row', 'columns' => 1),
//                    array('field' => 'text', 
//                        'label' => __a(__FILE__, 'Page content'), 
//                        'type' => 'editor',
//                        'options' => array(
//                            'height' => '350px',
//                        )
//                    ),
//                array('/row'),
//            array('else'),
//                array('row', 'columns' => array(3, 9)),
//                    array('field' => 'side_text', 
//                        'label' => __a(__FILE__, 'Side content'),
//                        'hint' => __a(__FILE__, 'If empty then default side content is displayed. To check that side content is really empty use "Source" mode of editor.'),
//                        'type' => 'editor',
//                        'options' => array(
//                            'height' => '350px',
//                            'bodyClass' => '-cke-content-side',
//                        )
//                    ),
//                    array('field' => 'text', 
//                        'label' => __a(__FILE__, 'Main content'), 
//                        'type' => 'editor',
//                        'options' => array(
//                            'height' => '350px',
//                            'bodyClass' => '-cke-content-main',
//                        )
//                    ),
//                array('/row'),
//            array('endif'),
//            array('row', 'columns' => 1),
//                array(
//                    'field' => 'has_side_content', 
//                    'label' => __a(__FILE__, 'Display side content'),
//                ),
//            array('/row'),
//            
//            // PHOTOGALLERY
//            array(
//                'h1' => __a(__FILE__, 'Photogallery'), 
//                'hint' => __a(
//                    $this, 
//                    'Obrázky môžu byť pridané buď presunutím alebo kliknutím do plochy galérie. Keďže môže ísť pomerne veľké obrázky, tak aj z ohľadu voči mobilným datam návštevníkov webu, je vhodné ich pred nahratím uložiť vo formate JPG a minimalizovať ich veľkosť pomocou <a href="https://compressor.io/compress" target="_blank">online kompresora</a>'
//                )
//            ),
//            array('row', 'columns' => 1),
//                'WebImage.file' => array(
//                    'type' => 'gallery',
//                    'options' => array(
//                        'foreignKey' => $data['id'],
//                        'nameField' => 'WebImage.name',
//                        'fields' => array(
//                            'WebImage.file' => array('label' => __a(__FILE__, 'Image')),
//                            'WebImage.name' => array('label' => __a(__FILE__, 'Name')),
//                            array('field' => 'WebImage.alternative_file', 'label' => __a(__FILE__, 'Alternative thumb'), 'type' => 'file'),
//                            'WebImage._delete_alternative_file' => array('label' => __a(__FILE__, 'Delete alternative thumb'), 'type' => 'checkbox'),
//                            array('field' => 'WebImage.alternative_file', 'type' => 'image', 'style' => 'height:100px'),
//                        ),
//                        'actions' => array(
//                            'load' => '/mvc/App/WebImages/admin_load',
//                            'move' => '/mvc/App/WebImages/admin_move',
//                            'add' => array(
//                                'label' => __a(__FILE__, 'Add new image'),
//                                'url' => '/mvc/App/WebImages/admin_add',
//                            ),
//                            'update' => array(
//                                'label' => __a(__FILE__, 'Update image'),
//                                'url' => '/mvc/App/WebImages/admin_update',
//                            ),
//                            'delete' => array(
//                                'label' => __a(__FILE__, 'Delete image'),
//                                'url' => '/mvc/App/WebImages/admin_delete',
//                                'confirmMessage' => __a(__FILE__, 'Please confirm removal of image'),
//                            ),
//                        )
//                    )
//                ),
//            array('/row'),        
//            
//            // SEO PARAMETERS
//            array(
//                'h1' => __a(__FILE__, 'SEO parameters'),
//                'hint' => __a(__FILE__, 'Parameters for search engines, e.g. Google.com, Yahoo.com or Zoznam.cz')
//            ),
//            array('row', 'columns' => array(3,6,3)),
//                array(
//                    'field' => 'seo_title', 
//                    'label' => __a(__FILE__, 'Page title'),
//                    'hint' => __a(__FILE__, 'Title is displayed in browser tab. It is one of most important texts for Google on your page'),
//                ),
//                array('field' => 'seo_description', 
//                    'label' => __a(__FILE__, 'Description'),
//                    'type' => 'textarea', 
//                    'style' => 'height: 34px',
//                    'hint' => __a(__FILE__, 'This text is mostly used by Google in search results. Sometimes Google decides to use some other text generated from page content'),
//                ),
//                array('field' => 'seo_keywords', 
//                    'label' => __a(__FILE__, 'Keywords'),
//                    'hint' => __a(__FILE__, 'Google does not consider much keywords. Other search engines may.')
//                ),
//            array('/row'),                
//            array('row'),
//                array('field' => 'seo_index', 
//                    'label' => __a(__FILE__, 'Robots - index'),
//                    'hint' => __a(__FILE__, 'Should Google include this page into search results? If you are not much sure then let it checked!')
//                ),
//                array('field' => 'seo_follow', 
//                    'label' => __a(__FILE__, 'Robots - follow'),
//                    'hint' => __a(__FILE__, 'Should Google follow links included in this page? If you are not much sure then let it checked!')
//                ),
//            array('/row'),
//            
////            // OTHER PARAMETERS
////            array('h1' => __a(__FILE__, 'Other parameters')),
////            array('row'),
////                array(
//////                    'field' => 'new_parent_id', 
////                    'field' => 'new_previous_sibling_id', 
////                    'label' => __a(__FILE__, 'Location in tree behind'),
////                    'type' => 'select',
////                    'options' => $this->findTreeSelectList(
////                        array(
////                            'conditions' => array('parent_id' => null, 'lang' => $data['lang']),
////                        ),
////                        array(
////                            'firstPlaceholder' => true,
////                            'conditions' => $conditions,
////                        )
////                    ),
////                ),
////                array('if' => !empty($sliders)),
////                    array(
////                        'field' => 'slider_id', 
////                        'label' => __a(__FILE__, 'Slider'),
////                        'type' => 'select',
////                        'options' => $sliders
////                    ),
////                array('endif'),
////                array(
////                    'field' => 'layout', 
////                    'label' => __a(__FILE__, 'Page layout'),
////                    'type' => 'select',
////                    'options' => array(
////                        'App.default' => __a(__FILE__, 'Default layout'),
////                        'App.home' => __a(__FILE__, 'Home layout'),
////                    )
////                ),
////            array('/row'),
        ));
        // add admin user fields
        $form = array_merge($form, $this->getSmartFormCommonEndPart());
        return $form;
    }
    
    /**
     * Return smart form used to edit news page contents
     * 
     * @param array $data Content data retrieved from DB
     * 
     * @return array Definition of 'fields' option for Html::smartForm()
     */
    public function getSmartFormForNews($data) {
        // common start fields
        $form = $this->getSmartFormCommonStartPart();
        // news page fields
        $sliders = $this->getSlidersSelectList($data['lang']);
        $form = array_merge($form, array(
            array('h1' => __a(__FILE__, 'Basic parameters')),
            array('row', 'columns' => array(3, 2, 3, 3, 1)),
                array('field' => 'name', 'label' => __a(__FILE__, 'News item title')), 
                array(
                    'field' => 'date', 
                    'label' => __a(__FILE__, 'Date'),
                    'type' => 'date',
                ),
                array('col'),
                    array('row', 'columns' => 1),
                        array('col'),
                            array(
                                'field' => 'menu_icon', 
                                'label' => __a(__FILE__, 'News index image'),
                                'hint' => __a(
                                     __FILE__, 
                                     'Image dimensions :width: x :height:px.',
                                     array('width' => 180, 'height' => 130)
                                 )
                            ),
                            array(
                                'field' => 'menu_icon', 
                                'type' => 'image',
                                'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/menu_icon/' . $data['id'],
                                'style' => 'display:block;width:100%;background-color:#EEE;',
                            ),
                        array('/col'),
                    array('/row'),
                array('/col'),
                array('col'),
                    array('row', 'columns' => 1),
                        array('col'),
                            array(
                                'field' => 'image_name', 
                                'label' => __a(__FILE__, 'News detail image'),
                                'hint' => __a(
                                     __FILE__, 
                                     'Image with width :width:px.',
                                     array('width' => 180)
                                 )
                            ),
                            array(
                                'field' => 'image_name', 
                                'type' => 'image',
                                'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/image_name/' . $data['id'],
                                'style' => 'display:block;width:100%;background-color:#EEE;',
                            ),
                        array('/col'),
                    array('/row'),
                array('/col'),
                array('field' => 'active', 'label' => __a(__FILE__, 'Active')),
            array('/row'),
            array('row', 'columns' => 1),
                array(
                    'field' => 'resume', 
                    'label' => __a(__FILE__, 'Resume'), 
                    'hint' => __a(__FILE__, '2 or 3 sentences of news resume'),
                    'type' => 'editor',
                    'options' => array(
                        'toolbar' => 'Empty',
                        'bodyClass' => 'articles-resume-layout',
                    ),
                ),
            array('/row'),
            
            // PAGE DATA
            array('h1' => __a(__FILE__, 'Page data')),
            array('row'),
                array(
                    'field' => 'locator', 
                    'label' => __a(__FILE__, 'Page locator'),
                    'hint' => __a(
                        __FILE__, 
                        'Page URL can be provided in 3 ways:<ul><li><b>actual page URL name</b>, e.g. <code>this-page</code> - it means that this page is accesses at <kbd>:urlBase:/<b>this-page</b></kbd></li><li><b>reference to another page</b> starting by slash <code>/</code>, e.g. <code>/that-page</code> - it means that menu item "This page" points to <kbd>:urlBase:<b>/that-page</b></kbd> and content of "That page" will be displayed. This can be used to make different menu items to point to the same content</li><li><b>absolute address</b> starting by <code>http://</code>, e.g. <code>http://google.com</code> - it means that menu item "This page" points to <kbd><b>http://google.com</b></kbd>. This can be used to make menu items to point to external address. Absolute address is always opened in new browser tab</li></ul>',
                        array(
                            'urlBase' => App::$urlBase,
                        )
                    ),                    
                ),
            array('/row'),
            // PAGE CONTENT
            //array('h1' => __a(__FILE__, 'Page content')),
            array('if' => empty($data['has_side_content'])),
                array('row', 'columns' => 1),
                    array(
                        'field', 
                        'label' => __a(__FILE__, 'Page content'),
                        'hint' => __a(__FILE__, 'SEO advice: Do not forget to integrate into written text links of site parts which would you like to promote by this article (e.g. e-shop products)'),
                        'type' => 'contentBlocks',
                        'ownerModel' => 'App.WebContent',
                        'ownerId' => Sanitize::value($data['id']),
                        //'contentBlockModels' => 'ContentBlock.*',
                        //'avoidContentBlockModels' => 'ContentBlock.ContentBlockSome',
                    ),            
//                    array('field' => 'text', 
//                        'label' => __a(__FILE__, 'Page content'),
//                        'type' => 'editor',
//                        'options' => array(
//                            'height' => '350px',
//                        )
//                    ),
                array('/row'),
            array('else'),
                array('row', 'columns' => array(3, 9)),
                    array('field' => 'side_text', 
                        'label' => __a(__FILE__, 'Side content'), 
                        'hint' => __a(__FILE__, 'If empty then default side content is displayed. To check that side content is really empty use "Source" mode of editor.'),
                        'type' => 'editor',
                        'options' => array(
                            'height' => '350px',
                            'bodyClass' => '-cke-content-side',
                        )
                    ),
                    array(
                        'field', 
                        'label' => __a(__FILE__, 'Main content'),
                        'hint' => __a(__FILE__, 'SEO advice: Do not forget to integrate into written text links of site parts which would you like to promote by this article (e.g. e-shop products)'),
                        'type' => 'contentBlocks',
                        'ownerModel' => 'App.WebContent',
                        'ownerId' => Sanitize::value($data['id']),
                        //'contentBlockModels' => 'ContentBlock.*',
                        //'avoidContentBlockModels' => 'ContentBlock.ContentBlockSome',
                    ),            
//                    array('field' => 'text', 
//                        'label' => __a(__FILE__, 'Main content'), 
//                        'hint' => __a(__FILE__, 'SEO advice: Do not forget to integrate into written text links of site parts which would you like to promote by this article (e.g. e-shop products)'),
//                        'type' => 'editor',
//                        'options' => array(
//                            'height' => '350px',
//                            'bodyClass' => '-cke-content-main',
//                        )
//                    ),
                array('/row'),
            array('endif'),
            /*/
            array('row', 'columns' => 1),
                array(
                    'field' => 'has_side_content', 
                    'label' => __a(__FILE__, 'Display side content'),
                ),
            array('/row'),
            //*/
            
//            // IMAGE
//            array('h1' => __a(__FILE__, 'Image')),
//            array('row', 'columns' => 2),
//                array('col'),
//                    array('row', 'columns' => 1),
//                        array('col'),
//                            array(
//                                'field' => 'menu_icon', 
//                                'label' => __a(__FILE__, 'Menu icon')
//                            ),
//                            array(
//                                'field' => 'menu_icon', 
//                                'type' => 'image',
//                                'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/menu_icon/' . $data['id'],
//                                'style' => 'display:block;width:100%;background-color:#EEE;',
//                            ),
//                        array('/col'),
//                    array('/row'),
//                array('/col'),
//                array('col'),
//                    array('row', 'columns' => 1),
//                        array('col'),
//                            array(
//                                'field' => 'image_name', 
//                                'label' => __a(__FILE__, 'Header image'),
//                                'hint' => __a(
//                                    $this, 
//                                    'Width at least :width:px to ensure the best quality. Height should be round :height:px.', 
//                                    array('width' => 1200, 'height' => 805)
//                                )
//                            ),
//                            array(
//                                'field' => 'image_name', 
//                                'type' => 'image',
//                                'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/image_name/' . $data['id'],
//                                //'style' => 'height:195px',
//                                'style' => 'display:block;width:100%;',
//                            ),
//                            array('if' => !empty($data['image_name'])),
//                                array('row', 'columns' => 1),
//                                    array(
//                                        'field' => 'header_text',
//                                        'label' => __a(__FILE__, 'Text in header image'), 
//                                        'type' => 'editor',
//                                        'options' => array(
//                                            //'toolbar' => 'Text',
//                                            'bodyClass' => '-cke-showcase-label',
//                                        ),
//                                    ),
//                                array('/row'),
//                            array('endif'),
//                        array('/col'),
//                    array('/row'),
//                array('/col'),
//            array('/row'),
//            array('row', 'columns' => 2),
//                array('col'),
//                    array('row', 'columns' => 1),
//                        array('col'),
//                            array('field' => 'background_image', 'label' => __a(__FILE__, 'Background image') . ' (1800 x 640px)'),
//                            array(
//                                'field' => 'background_image', 
//                                'type' => 'image',
//                                'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/background_image/' . $data['id'],
//                                //'style' => 'height:195px',
//                                'style' => 'display:block;width:100%;',
//                            ),
//                        array('/col'),
//                    array('/row'),
//                array('/col'),
//            array('/row'),
            
            // PHOTOGALLERY
            array(
                'h1' => __a(__FILE__, 'Photogallery'), 
                'hint' => __a(
                    __FILE__, 
                    'Obrázky môžu byť pridané buď presunutím alebo kliknutím do plochy galérie. Keďže môže ísť pomerne veľké obrázky, tak aj z ohľadu voči mobilným datam návštevníkov webu, je vhodné ich pred nahratím uložiť vo formate JPG a minimalizovať ich veľkosť pomocou <a href="https://compressor.io/compress" target="_blank">online kompresora</a>'
                )
            ),
            array('row', 'columns' => 1),
                'WebImage.file' => array(
                    'type' => 'gallery',
                    'options' => array(
                        'foreignKey' => $data['id'],
                        'nameField' => 'WebImage.name',
                        'fields' => array(
                            'WebImage.file' => array('label' => __a(__FILE__, 'Image')),
                            'WebImage.name' => array('label' => __a(__FILE__, 'Name')),
                            array('field' => 'WebImage.alternative_file', 'label' => __a(__FILE__, 'Alternative thumb'), 'type' => 'file'),
                            'WebImage._delete_alternative_file' => array('label' => __a(__FILE__, 'Delete alternative thumb'), 'type' => 'checkbox'),
                            array('field' => 'WebImage.alternative_file', 'type' => 'image', 'style' => 'height:100px'),
                        ),
                        'actions' => array(
                            'load' => '/mvc/App/WebImages/admin_load',
                            'move' => '/mvc/App/WebImages/admin_move',
                            'add' => array(
                                'label' => __a(__FILE__, 'Add new image'),
                                'url' => '/mvc/App/WebImages/admin_add',
                            ),
                            'update' => array(
                                'label' => __a(__FILE__, 'Update image'),
                                'url' => '/mvc/App/WebImages/admin_update',
                            ),
                            'delete' => array(
                                'label' => __a(__FILE__, 'Delete image'),
                                'url' => '/mvc/App/WebImages/admin_delete',
                                'confirmMessage' => __a(__FILE__, 'Please confirm removal of image'),
                            ),
                        )
                    )
                ),
            array('/row'),   
            
            // SEO PARAMETERS
            array(
                'h1' => __a(__FILE__, 'SEO parameters'),
                'hint' => __a(__FILE__, 'Parameters for search engines, e.g. Google.com, Yahoo.com or Zoznam.cz')
            ),
            array('row', 'columns' => array(3,6,3)),
                array(
                    'field' => 'seo_title', 
                    'label' => __a(__FILE__, 'Page title'),
                    'hint' => __a(__FILE__, 'Title is displayed in browser tab. It is one of most important texts for Google on your page'),
                ),
                array('field' => 'seo_description', 
                    'label' => __a(__FILE__, 'Description'),
                    'type' => 'textarea', 
                    'style' => 'height: 34px',
                    'hint' => __a(__FILE__, 'This text is mostly used by Google in search results. Sometimes Google decides to use some other text generated from page content'),
                ),
                array('field' => 'seo_keywords', 
                    'label' => __a(__FILE__, 'Keywords'),
                    'hint' => __a(__FILE__, 'Google does not consider much keywords. Other search engines may.')
                ),
            array('/row'),                
            array('row'),
                array('field' => 'seo_index', 
                    'label' => __a(__FILE__, 'Robots - index'),
                    'hint' => __a(__FILE__, 'Should Google include this page into search results? If you are not much sure then let it checked!')
                ),
                array('field' => 'seo_follow', 
                    'label' => __a(__FILE__, 'Robots - follow'),
                    'hint' => __a(__FILE__, 'Should Google follow links included in this page? If you are not much sure then let it checked!')
                ),
            array('/row'),
            
            // OTHER PARAMETERS
            array('h1' => __a(__FILE__, 'Other parameters')),
            array('row'),
//                array(
////                    'field' => 'new_parent_id', 
//                    'field' => 'new_previous_sibling_id', 
//                    'label' => __a(__FILE__, 'Location in tree behind'),
//                    'type' => 'select',
//                    'options' => $this->findTreeSelectList(
//                        array(
//                            'conditions' => array('parent_id' => null, 'lang' => $data['lang']),
//                        ),
//                        array(
//                            'firstPlaceholder' => true,
//                            'conditions' => $conditions,
//                        )
//                    ),
//                ),
                array('if' => !empty($sliders)),
                    array(
                        'field' => 'slider_id', 
                        'label' => __a(__FILE__, 'Slider'),
                        'type' => 'select',
                        'options' => $sliders
                    ),
                array('endif'),
//                array(
//                    'field' => 'layout', 
//                    'label' => __a(__FILE__, 'Page layout'),
//                    'type' => 'select',
//                    'options' => array(
//                        'App.default' => __a(__FILE__, 'Default layout'),
//                        'App.home' => __a(__FILE__, 'Home layout'),
//                    )
//                ),
            
            array('/row'),              
        ));
        // add admin user fields
        $form = array_merge($form, $this->getSmartFormCommonEndPart());
        return $form;
    }
    
    /**
     * Return smart form used to edit (blog) article page contents
     * 
     * @param array $data Content data retrieved from DB
     * 
     * @return array Definition of 'fields' option for Html::smartForm()
     */
    public function getSmartFormForArticle($data) {
        // common start fields
        $form = $this->getSmartFormCommonStartPart();
        // article page fields
        $sliders = $this->getSlidersSelectList($data['lang']);
        $form = array_merge($form, array(
            array('h1' => __a(__FILE__, 'Basic parameters')),
            array('row'),
                array('col'),
                    array('field' => 'name', 'label' => __a(__FILE__, 'Article title')), 
                    array(
                        'field' => 'date', 
                        'label' => __a(__FILE__, 'Date'),
                        'type' => 'date',
                    ),
                array('/col'),
                array('col'),
                    array(
                        'field' => 'menu_icon', 
                        'label' => __a(__FILE__, 'Articles index image'),
                        'hint' => __a(
                             __FILE__, 
                             'Image dimensions :width: x :height:px.',
                             array('width' => 407, 'height' => 264)
                         )
                    ),
                    array(
                        'field' => 'menu_icon', 
                        'type' => 'image',
                        'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/menu_icon/' . $data['id'],
                        'style' => 'display:block;width:100%;background-color:#EEE;',
                    ),
                array('/col'),
                array('col'),
                    array(
                        'field' => 'image_name', 
                        'label' => __a(__FILE__, 'Article image'),
                        'hint' => __a(
                             __FILE__, 
                             'Image with width at least :width:px.',
                             array('width' => 870)
                         )
                    ),
                    array(
                        'field' => 'image_name', 
                        'type' => 'image',
                        'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/image_name/' . $data['id'],
                        'style' => 'display:block;width:100%;background-color:#EEE;',
                    ),
                array('/col'),
                array('field' => 'active', 'label' => __a(__FILE__, 'Active')),
            array('/row'),
            array('row'),
                array('col'),
                    array('field' => 'aux_01', 
                        'label' => __a(__FILE__, 'Category of article'), 
                        'type' => 'select',
                        'options' => array(
                            'Tipy na výlet' => __a(__FILE__, 'Tipy na výlet'),
                            'Návody' => __a(__FILE__, 'Návody'),
                            'Novinky' => __a(__FILE__, 'Novinky'),
                            'Udalosti' => __a(__FILE__, 'Udalosti'),
                        )
                    ),
                array('/col'),
            array('/row'),
            array('row', 'columns' => 1),
                array(
                    'field' => 'resume', 
                    'label' => __a(__FILE__, 'Resume'), 
                    'hint' => __a(__FILE__, '5 or 6 sentences of article resume'), 
                    'type' => 'editor',
                    'options' => array(
                        'toolbar' => 'Empty',
                        'bodyClass' => 'articles-resume-layout',
                    ),
                ),
            array('/row'),
            
            // PAGE DATA
            array('h1' => __a(__FILE__, 'Page data')),
            array('row'),
                array(
                    'field' => 'locator', 
                    'label' => __a(__FILE__, 'Page locator'),
                    'hint' => __a(
                        __FILE__, 
                        'Page URL can be provided in 3 ways:<ul><li><b>actual page URL name</b>, e.g. <code>this-page</code> - it means that this page is accesses at <kbd>:urlBase:/<b>this-page</b></kbd></li><li><b>reference to another page</b> starting by slash <code>/</code>, e.g. <code>/that-page</code> - it means that menu item "This page" points to <kbd>:urlBase:<b>/that-page</b></kbd> and content of "That page" will be displayed. This can be used to make different menu items to point to the same content</li><li><b>absolute address</b> starting by <code>http://</code>, e.g. <code>http://google.com</code> - it means that menu item "This page" points to <kbd><b>http://google.com</b></kbd>. This can be used to make menu items to point to external address. Absolute address is always opened in new browser tab</li></ul>',
                        array(
                            'urlBase' => App::$urlBase,
                        )
                    ),                    
                ),
            array('/row'),
            // PAGE CONTENT
            //array('h1' => __a(__FILE__, 'Page content')),
            array('if' => empty($data['has_side_content'])),
                array('row', 'columns' => 1),
                    array(
                        'field', 
                        'label' => __a(__FILE__, 'Page content'),
                        'hint' => __a(__FILE__, 'SEO advice: Do not forget to integrate into written text links of site parts which would you like to promote by this article (e.g. e-shop products)'),
                        'type' => 'contentBlocks',
                        'ownerModel' => 'App.WebContent',
                        'ownerId' => Sanitize::value($data['id']),
                        //'contentBlockModels' => 'ContentBlock.*',
                        //'avoidContentBlockModels' => 'ContentBlock.ContentBlockSome',
                    ),
//                    array('field' => 'text', 
//                        'label' => __a(__FILE__, 'Page content'), 
//                        'hint' => __a(__FILE__, 'SEO advice: Do not forget to integrate into written text links of site parts which would you like to promote by this article (e.g. e-shop products)'),
//                        'type' => 'editor',
//                        'options' => array(
//                            'height' => '350px',
//                        )
//                    ),
                array('/row'),
            array('else'),
                array('row', 'columns' => array(3, 9)),
                    array('field' => 'side_text', 
                        'label' => __a(__FILE__, 'Side content'), 
                        'hint' => __a(__FILE__, 'If empty then default side content is displayed. To check that side content is really empty use "Source" mode of editor.'),
                        'type' => 'editor',
                        'options' => array(
                            'height' => '350px',
                            'bodyClass' => '-cke-content-side',
                        )
                    ),
                    array(
                        'field', 
                        'label' => __a(__FILE__, 'Main content'),
                        'hint' => __a(__FILE__, 'SEO advice: Do not forget to integrate into written text links of site parts which would you like to promote by this article (e.g. e-shop products)'),
                        'type' => 'contentBlocks',
                        'ownerModel' => 'App.WebContent',
                        'ownerId' => Sanitize::value($data['id']),
                        //'contentBlockModels' => 'ContentBlock.*',
                        //'avoidContentBlockModels' => 'ContentBlock.ContentBlockSome',
                    ),
//                    array('field' => 'text', 
//                        'label' => __a(__FILE__, 'Main content'), 
//                        'hint' => __a(__FILE__, 'SEO advice: Do not forget to integrate into written text links of site parts which would you like to promote by this article (e.g. e-shop products)'),
//                        'type' => 'editor',
//                        'options' => array(
//                            'height' => '350px',
//                            'bodyClass' => '-cke-content-main',
//                        )
//                    ),
                array('/row'),
            array('endif'),
            /*/
            array('row', 'columns' => 1),
                array(
                    'field' => 'has_side_content', 
                    'label' => __a(__FILE__, 'Display side content'),
                ),
            array('/row'),
            /*/
            
//            // IMAGE
//            array('h1' => __a(__FILE__, 'Image')),
//            array('row', 'columns' => 2),
//                array('col'),
//                    array('row', 'columns' => 1),
//                        array('col'),
//                            array(
//                                'field' => 'menu_icon', 
//                                'label' => __a(__FILE__, 'Menu icon')
//                            ),
//                            array(
//                                'field' => 'menu_icon', 
//                                'type' => 'image',
//                                'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/menu_icon/' . $data['id'],
//                                'style' => 'display:block;width:100%;background-color:#EEE;',
//                            ),
//                        array('/col'),
//                    array('/row'),
//                array('/col'),
//                array('col'),
//                    array('row', 'columns' => 1),
//                        array('col'),
//                            array(
//                                'field' => 'image_name', 
//                                'label' => __a(__FILE__, 'Header image'),
//                                'hint' => __a(
//                                    $this, 
//                                    'Width at least :width:px to ensure the best quality. Height should be round :height:px.', 
//                                    array('width' => 1200, 'height' => 805)
//                                )
//                            ),
//                            array(
//                                'field' => 'image_name', 
//                                'type' => 'image',
//                                'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/image_name/' . $data['id'],
//                                //'style' => 'height:195px',
//                                'style' => 'display:block;width:100%;',
//                            ),
//                            array('if' => !empty($data['image_name'])),
//                                array('row', 'columns' => 1),
//                                    array(
//                                        'field' => 'header_text',
//                                        'label' => __a(__FILE__, 'Text in header image'), 
//                                        'type' => 'editor',
//                                        'options' => array(
//                                            //'toolbar' => 'Text',
//                                            'bodyClass' => '-cke-showcase-label',
//                                        ),
//                                    ),
//                                array('/row'),
//                            array('endif'),
//                        array('/col'),
//                    array('/row'),
//                array('/col'),
//            array('/row'),
//            array('row', 'columns' => 2),
//                array('col'),
//                    array('row', 'columns' => 1),
//                        array('col'),
//                            array('field' => 'background_image', 'label' => __a(__FILE__, 'Background image') . ' (1800 x 640px)'),
//                            array(
//                                'field' => 'background_image', 
//                                'type' => 'image',
//                                'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/background_image/' . $data['id'],
//                                //'style' => 'height:195px',
//                                'style' => 'display:block;width:100%;',
//                            ),
//                        array('/col'),
//                    array('/row'),
//                array('/col'),
//            array('/row'),
            
            // PHOTOGALLERY
            array(
                'h1' => __a(__FILE__, 'Photogallery'), 
                'hint' => __a(
                    __FILE__, 
                    'Obrázky môžu byť pridané buď presunutím alebo kliknutím do plochy galérie. Keďže môže ísť pomerne veľké obrázky, tak aj z ohľadu voči mobilným datam návštevníkov webu, je vhodné ich pred nahratím uložiť vo formate JPG a minimalizovať ich veľkosť pomocou <a href="https://compressor.io/compress" target="_blank">online kompresora</a>'
                )
            ),
            array('row', 'columns' => 1),
                'WebImage.file' => array(
                    'type' => 'gallery',
                    'options' => array(
                        'foreignKey' => $data['id'],
                        'nameField' => 'WebImage.name',
                        'fields' => array(
                            'WebImage.file' => array('label' => __a(__FILE__, 'Image')),
                            'WebImage.name' => array('label' => __a(__FILE__, 'Name')),
                            array('field' => 'WebImage.alternative_file', 'label' => __a(__FILE__, 'Alternative thumb'), 'type' => 'file'),
                            'WebImage._delete_alternative_file' => array('label' => __a(__FILE__, 'Delete alternative thumb'), 'type' => 'checkbox'),
                            array('field' => 'WebImage.alternative_file', 'type' => 'image', 'style' => 'height:100px'),
                        ),
                        'actions' => array(
                            'load' => '/mvc/App/WebImages/admin_load',
                            'move' => '/mvc/App/WebImages/admin_move',
                            'add' => array(
                                'label' => __a(__FILE__, 'Add new image'),
                                'url' => '/mvc/App/WebImages/admin_add',
                            ),
                            'update' => array(
                                'label' => __a(__FILE__, 'Update image'),
                                'url' => '/mvc/App/WebImages/admin_update',
                            ),
                            'delete' => array(
                                'label' => __a(__FILE__, 'Delete image'),
                                'url' => '/mvc/App/WebImages/admin_delete',
                                'confirmMessage' => __a(__FILE__, 'Please confirm removal of image'),
                            ),
                        )
                    )
                ),
            array('/row'),   
            
            // SEO PARAMETERS
            array(
                'h1' => __a(__FILE__, 'SEO parameters'),
                'hint' => __a(__FILE__, 'Parameters for search engines, e.g. Google.com, Yahoo.com or Zoznam.cz')
            ),
            array('row', 'columns' => array(3,6,3)),
                array(
                    'field' => 'seo_title', 
                    'label' => __a(__FILE__, 'Page title'),
                    'hint' => __a(__FILE__, 'Title is displayed in browser tab. It is one of most important texts for Google on your page'),
                ),
                array('field' => 'seo_description', 
                    'label' => __a(__FILE__, 'Description'),
                    'type' => 'textarea', 
                    'style' => 'height: 34px',
                    'hint' => __a(__FILE__, 'This text is mostly used by Google in search results. Sometimes Google decides to use some other text generated from page content'),
                ),
                array('field' => 'seo_keywords', 
                    'label' => __a(__FILE__, 'Keywords'),
                    'hint' => __a(__FILE__, 'Google does not consider much keywords. Other search engines may.')
                ),
            array('/row'),                
            array('row'),
                array('field' => 'seo_index', 
                    'label' => __a(__FILE__, 'Robots - index'),
                    'hint' => __a(__FILE__, 'Should Google include this page into search results? If you are not much sure then let it checked!')
                ),
                array('field' => 'seo_follow', 
                    'label' => __a(__FILE__, 'Robots - follow'),
                    'hint' => __a(__FILE__, 'Should Google follow links included in this page? If you are not much sure then let it checked!')
                ),
            array('/row'),
            
            // OTHER PARAMETERS
            array('h1' => __a(__FILE__, 'Other parameters')),
            array('row'),
//                array(
////                    'field' => 'new_parent_id', 
//                    'field' => 'new_previous_sibling_id', 
//                    'label' => __a(__FILE__, 'Location in tree behind'),
//                    'type' => 'select',
//                    'options' => $this->findTreeSelectList(
//                        array(
//                            'conditions' => array('parent_id' => null, 'lang' => $data['lang']),
//                        ),
//                        array(
//                            'firstPlaceholder' => true,
//                            'conditions' => $conditions,
//                        )
//                    ),
//                ),
                array('if' => !empty($sliders)),
                    array(
                        'field' => 'slider_id', 
                        'label' => __a(__FILE__, 'Slider'),
                        'type' => 'select',
                        'options' => $sliders
                    ),
                array('endif'),
//                array(
//                    'field' => 'layout', 
//                    'label' => __a(__FILE__, 'Page layout'),
//                    'type' => 'select',
//                    'options' => array(
//                        'App.default' => __a(__FILE__, 'Default layout'),
//                        'App.home' => __a(__FILE__, 'Home layout'),
//                    )
//                ),
            
            array('/row'),              
        ));
        // add admin user fields
        $form = array_merge($form, $this->getSmartFormCommonEndPart());
        return $form;
    }
    
    /**
     * @deprecated - Use functionality defined in Reference model and References controller
     * 
     * Return smart form used to edit reference slider contents
     * 
     * @param array $data Content data retrieved from DB
     * 
     * @return array Definition of 'fields' option for Html::smartForm()
     */
    public function getSmartFormForReference($data) {
        // common start fields
        $form = $this->getSmartFormCommonStartPart();
        // slide fields
        $form = array_merge($form, array(
            array('h1' => __a(__FILE__, 'Basic parameters')),
            array('row', 'columns' => 2),
                array('col', 'columns' => 2, 'deviceSize' => 'md'),
                    array('row'),
                       array('field' => 'name', 'label' => __a(__FILE__, 'Reference name')),
                       array('field' => 'aux_01', 'label' => __a(__FILE__, 'Reference info')),
                    array('/row'),
                    array('row'),
                       array('field' => 'active', 'label' => __a(__FILE__, 'Active')),
                       array(
                           'field' => 'menu_icon', 
                           'label' => __a(__FILE__, 'Image'),
                           'hint' => __a(
                                __FILE__, 
                                'Square image, the best :width: x :height:px.',
                                array('width' => 340, 'height' => 340)
                            )
                        ),
                    array('/row'),
                    array('row', 'columns' => 1),
                        array('field' => 'resume', 
                            'label' => __a(__FILE__, 'Reference text'), 
                            'type' => 'editor',
                            'options' => array(
                                'toolbar' => 'Basic',
                                //'height' => '350px',
                                'bodyClass' => '-cke-references-slider-text',
                            )
                        ),
                    array('/row'),
                    array('row', 'columns' => 1),
                        array('field' => 'locator', 
                            'label' => __a(__FILE__, 'Url'), 
                            'hint' => __a(
                                __FILE__, 
                                'URL can be provided in 2 ways:<ul><li><b>reference to another page</b> starting by slash <code>/</code>, e.g. <code>/that-page</code> - it means that item points to <kbd>:urlBase:<b>/that-page</b></kbd> and content of "That page" will be displayed.</li><li><b>absolute address</b> starting by <code>http://</code>, e.g. <code>http://google.com</code> - it means that item points to <kbd><b>http://google.com</b></kbd>. Absolute address is always opened in new browser tab</li></ul>',
                                array(
                                    'urlBase' => App::$urlBase,
                                )
                            ),
                        ),
                    array('/row'),
                array('/col'),
                array('col', 'style' => 'text-align: center;'),
                    array(
                        'field' => 'menu_icon', 
                        'type' => 'image',
                        'deleteImage' => '/mvc/App/WebContents/admin_deleteFile/menu_icon/' . $data['id'],
                        'style' => 'display:block;width:100%;background-color:#EEE;',
                    ),
                array('/col'),
            array('/row'),    
        ));
        // add admin user fields
        $form = array_merge($form, $this->getSmartFormCommonEndPart());
        return $form;
    }   
}
