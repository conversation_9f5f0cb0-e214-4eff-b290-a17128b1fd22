<?php
/**
 * To work with fonts use model Font
 */
class FontVariant extends Model {
    
    protected $table = 'run_font_variants';
    
    /**
     * ATTENTION: If fonts are used with wkhtmltopdf then font files must be downloaded, 
     * internal family name changed to an unique value and only these local files used with wkhtmltopdf.
     * The wkhtmltopdf has a bug when using multiple fonts together. To make it work correctly each 
     * font file must be  edited (e.g by FontForge) and unique family name (inside of font file, 
     * this name has not to be the same as css font-family) must be created to avoid conflicts
     * (e.g. between "Open Sans Regular" an "Open Sans Bold" if both have as family name 
     * "Open Sans" in their font files). Max length of unique family name is 8 characters.
     * To change font file internal family name edit it in FontForge and do following:
     *      1] Element > Font Info > PS Names > Family name > 'STUNIQ01'
     *      2] File > Generate Fonts... > Generate
     * 
     * Normally the local fonts files are created with suffix 'UFN?????' which can
     * be used as the unique family name in font file (see definition of 
     * FontVariant::$fileFields['local_file']['name'] in constructor). This suffix
     * has no other function, just to provide you quickly some really unique value.
     * 
     * For more details on wkhtmltopdf bug see:
     * - https://github.com/wkhtmltopdf/wkhtmltopdf/issues/2435
     * - https://makandracards.com/makandra/47300-how-to-fix-broken-font-collisions-in-wkhtmltopdf
     * 
     * @var array 
     */
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_fonts_id' => array('type' => 'int', 'index' => 'index'),
        'weight' => array('type' => 'enum', 'values' => array('100', '200', '300', '400', '500', '600', '700', '800', '900')),
        'italic' => array('type' => 'bool', 'default' => 0),
        'remote_file' => array('type' => 'varchar', 'default' => null, 'comment' => 'Font variant remote file (URL), e.g. http://fonts.gstatic.com/s/opensans/v13/IgZJs4-7SA1XX_edsoXWog.ttf. If fonts are used with wkhtmltopdf then font files must be downloaded, internal family name made unique and used locally. For more details see FontVariant::$schema phpDoc.'),
        'local_file' => array('type' => 'varchar', 'default' => null, 'comment' => 'Font variant local file'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
    protected $tableIndexes = array(
        array(
            'type' => 'unique', 
            'fields' => array('run_fonts_id', 'weight', 'italic'), 
            'name' => 'FontVariant'
        ),
    );
    
    public function __construct() {
        $this->fileFields = array(
            'local_file' => array(
                'name' => function($source, $data) {
                    // if not possible to get record then create file name in a default way
                    if (empty($data['id'])) {
                        return null;
                    }
                    if (empty($data['font_pid'])) {
                        $fontVariant = $this->findFirst(array(
                            'joins' => array(
                                array(
                                    'type' => 'left',
                                    'model' => 'Font',
                                )
                            ),
                            'conditions' => array(
                                'FontVariant.id' => $data['id'],
                            )
                        ));
                        $this->loadModel('Font');
                        $Font = new Font();
                        $fontPid = $Font->getFontPid($fontVariant);
                    }
                    else {
                        $fontPid = $data['font_pid'];
                    }
                    $name = $fontPid;
                    // if possible add an unique family name suffix to font filename. Its format
                    // is 'UFN?????' (8 chars) and it has no other function, just to provide you 
                    // quickly some really unique value for font file internal family name
                    // if you decide to make it unique. For more details see FontVariant::$schema phpDoc.
                    $uniqueFamilyName = strtoupper(Number::convertIntegerToAlphabetic($data['id']));
                    if (strlen($uniqueFamilyName) <= 5) {
                        $uniqueFamilyName = str_pad($uniqueFamilyName, 5, 'x', STR_PAD_LEFT);
                        $uniqueFamilyName = 'UFN' . $uniqueFamilyName;
                        $name .= '_' . $uniqueFamilyName;
                    }
                    $name .= '.' . File::getPathinfo($source[0], PATHINFO_EXTENSION);
                    return $name;
                },
            ),
        );
        parent::__construct();
    }
}
