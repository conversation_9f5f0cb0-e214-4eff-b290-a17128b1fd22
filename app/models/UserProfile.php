<?php

class UserProfile extends Model {
    protected $table = 'run_user_profiles';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_users_id' => array('type' => 'int', 'index' => 'index'),
        'birthdate' => array('type' => 'date', 'default' => null),
        'street' => array('type' => 'varchar', 'length' => 100, 'comment' => 'Street and number'),
        'city' => array('type' => 'varchar', 'length' => 50),
        'zip' => array('type' => 'varchar', 'length' => 10),
        'country' => array('type' => 'char', 'length' => 2, 'comment' =>'Country ISO code 2'),
        'phone' => array('type' => 'varchar', 'length' => 30, 'default' => null),
        'fax' => array('type' => 'varchar', 'length' => 30, 'default' => null),
        'company_fullname' => array('type' => 'varchar', 'length' => 100, 'default' => null),
        'company_id_number' => array('type' => 'varchar', 'length' => 20, 'default' => null, 'comment' => 'IČO'),
        'company_tax_number' => array('type' => 'varchar', 'length' => 20, 'default' => null, 'comment' => 'DIČ'),
        'company_vat_number' => array('type' => 'varchar', 'length' => 20, 'default' => null, 'comment' => 'DIČ DPH'),
        'another_delivery_address' => array('type' => 'bool'),
        'delivery_fullname' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Firstname and surname'),
        'delivery_street' => array('type' => 'varchar', 'length' => 100, 'default' => null, 'comment' => 'Street and number'),
        'delivery_city' => array('type' => 'varchar', 'length' => 50, 'default' => null),
        'delivery_zip' => array('type' => 'varchar', 'length' => 10, 'default' => null),
        'delivery_country' => array('type' => 'char', 'length' => 2, 'default' => null, 'comment' =>'Country ISO code 2'),
        'delivery_phone' => array('type' => 'varchar', 'length' => 30, 'default' => null),
        'delivery_email' => array('type' => 'varchar', 'length' => 100, 'default' => null),
        'company_vat_payer' => array('type' => 'bool', 'default' => 0, 'comment' => 'Platca DPH. If 1 then IČ DPH must be provided'),
        'terms_and_conditions_agreement' => array('type' => 'bool', 'default' => 0, 'comment' => 'Agreement with business conditions'),
        'newsletters_agreement' => array('type' => 'bool', 'default' => 0, 'comment' => 'Agreement with newsletters receiving'),
        'adulthood_declaration' => array('type' => 'bool', 'default' => 0, 'comment' => 'Declaration that person is adult and is more than 18 years old'),
        'reference_from' => array('type' => 'varchar', 'length' => 50, 'default' => null, 'comment' => 'Response on question: Where did you hear about us?'),
        'salutation' => array('type' => 'enum', 'values' => array('enum_mr', 'enum_mrs'), 'default' => null),
        'degree' => array('type' => 'varchar'),
        'subject_type' => array('type' => 'enum', 'values' => array('enum_person', 'enum_company'), 'default' => 'enum_person'),
        'bonus_points' => array('type' => 'decimal', 'length' => 14.2, 'default' => 0, 'comment' => 'Sum of bonus points. When it reaches Eshop.EshopOrder.applicableBonusPoints then the Eshop.EshopOrder.bonusDiscount is substracted from the actual order and bonus_points are reset to 0'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),        
    );
    
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(
            'street' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím ulicu a číslo domu/bytu'),
                    'on' => 'create',
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím ulicu a číslo domu/bytu'),
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'address',
                    'message' => __v(__FILE__, 'Zadajte prosím ulicu a číslo domu/bytu'),
                    'alternative' => 'frontend',
                ),
            ),
            'city' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím mesto'),
                    'on' => 'create',
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím mesto'),
                    'alternative' => 'frontend',
                ),
            ),
            'zip' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím PSČ'),
                    'on' => 'create',
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím PSČ'),
                    'alternative' => 'frontend',
                ),
            ),
            'city' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Vyberte prosím štát'),
                    'on' => 'create',
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Vyberte prosím štát'),
                    'alternative' => 'frontend',
                ),
            ),
//            'salutation' => array(
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Salutation is mandatory'),
//                    'on' => 'create',
//                    'alternative' => 'frontend',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Salutation is mandatory'),
//                    'alternative' => 'frontend',
//                ),
//            ),
            'phone' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím telefón'),
                    'on' => 'create',
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím telefón'),
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => array('phone', array('internationalFormat' => false)),
                    'message' => __v(__FILE__, 'Enter please a valid phone number'),
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => array('phone', array('internationalFormat' => true)),
                    'message' => __v(__FILE__, 'Zadajte prosím medzinárodný formát telefónneho čísla'),
                    'alternative' => 'frontend',
                ),
            ),
//            'birthdate' => array(
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Date of birth is mandatory'),
//                    'on' => 'create',
//                    'alternative' => 'frontend',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Date of birth is mandatory'),
//                    'alternative' => 'frontend',
//                ),
//                array(
//                    'rule' => array('date'),
//                    'message' => __v(__FILE__, 'Date of birth is invalid'),
//                    'alternative' => 'frontend',
//                ),
//            ),
            'company_fullname' => array(
//                array(
//                    'rule' => 'notEmptyIfCompany',
//                    'message' => __v(__FILE__, 'Enter please the company name'),
//                    'alternative' => 'frontend',
//                    'force' => true
//                ),
                array(
                    'rule' => 'onlyIfCompanyOrNotEmpty',
                    'force' => true,
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Enter please the company name'),
                    'on' => 'create',
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Enter please the company name'),
                    'alternative' => 'frontend',
                ),
            ),
            'company_id_number' => array(
//                array(
//                    'rule' => 'notEmptyIfCompany',
//                    'message' => __v(__FILE__, 'Enter please the company id number'),
//                    'alternative' => 'frontend',
//                    'force' => true
//                ),
                array(
                    'rule' => 'onlyIfCompanyOrNotEmpty',
                    'force' => true,
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Enter please the company id number'),
                    'on' => 'create',
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Enter please the company id number'),
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'ico',
                    'message' => __v(__FILE__, 'Please provide a valid company id number'),
                ),  
            ),
            'company_tax_number' => array(
//                array(
//                    'rule' => 'notEmptyIfCompany',
//                    'message' => __v(__FILE__, 'Enter please the company tax number'),
//                    'alternative' => 'frontend',
//                    'force' => true
//                ),
                array(
                    'rule' => 'onlyIfCompanyOrNotEmpty',
                    'force' => true,
                ),
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Enter please the company tax numbe'),
                    'on' => 'create',
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Enter please the company tax numbe'),
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'dic',
                    'message' => __v(__FILE__, 'Please provide a valid company tax number'),
                ),  
            ),
            'company_vat_number' => array(
                array(
                    'rule' => function($value, $field, $data) {
                        if (
                            !empty($data['company_vat_payer'])
                            && empty($value)
                        ) {
                            return false;
                        }
                        return true;
                    },
                    'message' => __v(__FILE__, 'Please provide a company VAT number'),
                    'force' => true,
                ),  
                array(
                    'rule' => array('dic', 'withCountryCode'),
                    'message' => __v(__FILE__, 'Please provide a valid company VAT number'),
                ),  
            ),
            'delivery_fullname' => array(
                array(
                    'rule' => 'notEmptyIfShippingAddress',
                    'message' => __v(__FILE__, 'Enter please firstname and lastname'),
                    'alternative' => 'frontend',
                    'force' => true
                ),
            ),
            'delivery_street' => array(
                array(
                    'rule' => 'notEmptyIfShippingAddress',
                    'message' => __v(__FILE__, 'Enter please street and house number/apartment'),
                    'alternative' => 'frontend',
                    'force' => true
                ),
            ),
            'delivery_city' => array(
                array(
                    'rule' => 'notEmptyIfShippingAddress',
                    'message' => __v(__FILE__, 'Zadajte prosím mesto'),
                    'alternative' => 'frontend',
                    'force' => true
                ),
            ),
            'delivery_zip' => array(
                array(
                    'rule' => 'notEmptyIfShippingAddress',
                    'message' => __v(__FILE__, 'Enter please ZIP'),
                    'alternative' => 'frontend',
                    'force' => true
                ),
            ),
            'delivery_country' => array(
                array(
                    'rule' => 'notEmptyIfShippingAddress',
                    'message' => __v(__FILE__, 'Vyberte prosím štát'),
                    'alternative' => 'frontend',
                    'force' => true
                ),
            ),
            'delivery_phone' => array(
                array(
                    'rule' => 'notEmptyIfShippingAddress',
                    'message' => __v(__FILE__, 'Enter please phone'),
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => 'phone',
                    'message' => __v(__FILE__, 'Enter please a valid phone number'),
                    'alternative' => 'frontend',
                ),
            ),
            'terms_and_conditions_agreement' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please confirm agreement to the Terms and Conditions'),
                    'on' => 'create',
                    'alternative' => 'frontend',
                ),
                array(
                    'rule' => array('notEmpty', true),
                    'message' => __v(__FILE__, 'Please confirm agreement to the Terms and Conditions'),
                    'alternative' => 'frontend',
                ),
            ),
//            'adulthood_declaration' => array(
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'You must be at least 18 years old'),
//                    'on' => 'create',
//                    'alternative' => 'frontend',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'You must be at least 18 years old'),
//                    'alternative' => 'frontend',
//                ),
//            ),
        );
    }
    
    public function validate_notEmptyIfCompany($value, $field, $data) {
        if (!empty($data['subject_type']) && $data['subject_type'] === 'enum_company') {
            return !empty($value);
        }
        return true;
    } 
    
    public function validate_onlyIfCompanyOrNotEmpty($value, $field, $data, &$validation) {
        $validation['exit'] = true;
        if (
            !empty($data['subject_type'])
            && $data['subject_type'] == 'enum_company'
            ||
            !empty($value)
        ) {
            $validation['exit'] = false;
        }
        return true;
    } 
    
    public function validate_notEmptyIfShippingAddress($value, $field, $data) {
        if (isset($data['another_delivery_address']) && $data['another_delivery_address'] == '1') {
            return !empty($value);
        }
        return true;
    } 
    
    public function normalize($data, $options = array()) {
        $defaults = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defaults, $options);
        
        // sanitize html to text (remove HTML tags) to avoid XSS attacks
        foreach ($this->getFields() as $field) {
            if (
                isset($data[$field])
                && is_string($data[$field])
                // avoid following fields
                && $field !== 'id'
            ) {
                $data[$field] = Sanitize::htmlTags($data[$field]);
            }
        }
        
        if (!empty($data['birthdate'])) {
            $data['birthdate'] = date('Y-m-d', strtotime($data['birthdate']));
        }
        
        if (isset($data['company_id_number'])) {
            $data['company_id_number'] = str_replace(array(' ', '-'), array('', ''), $data['company_id_number']);
        }
        
        if (isset($data['company_tax_number'])) {
            $data['company_tax_number'] = str_replace(array(' ', '-'), array('', ''), $data['company_tax_number']);
        }
        
        if (
            array_key_exists('company_vat_payer', $data)
            && !$data['company_vat_payer']
        ) {
            $data['company_vat_number'] = '';
        }
        if (isset($data['company_vat_number'])) {
            $data['company_vat_number'] = str_replace(array(' ', '-'), array('', ''), $data['company_vat_number']);
        }
        
        return parent::normalize($data, $options);
    }
    
    // Note: following functionality is part of Eshop but it is placed here because
    // it is related to "eshop side" of UserProfile. The clean solution would be to
    // have EshopUser model...
    
    /**
     * Returns actual bonus discount of specified user
     * 
     * @see implementationDetails > Bonusy
     * 
     * @param int $userId Optional. If not provided or NULL then defaults to logged user id. 
     *          If you need bonus discount for order created by unregistered user then pass 
     *          here 0 (to avoid use of actually logged user id)
     * 
     * @return float Actual bonus discount. If user has no bunus discount then 0.
     */
    public function getBonusDiscount($userId = null) {
        if (!isset($userId)) {
            $userId = App::getUser('id');
        }
        $actualBonusDiscount = 0.0;
        if (
            $userId
            && ($applicableBonusPoints = (float)App::getSetting('Eshop', 'EshopOrder.applicableBonusPoints'))
            && ($bonusDiscount = (float)App::getSetting('Eshop', 'EshopOrder.bonusDiscount'))
            && ($bonusPoints = $this->findFieldBy('bonus_points', 'run_users_id', $userId))
            && $bonusPoints >= $applicableBonusPoints
        ) {
            $actualBonusDiscount = $bonusDiscount;
        }
        return $actualBonusDiscount;
    }
        
    /**
     * Returns the bonus points of specified user
     * 
     * @see implementationDetails > Bonusy
     * 
     * @param int $userId Optional. If not provided or NULL then defaults to logged user id. 
     *          If you need bonus points for order created by unregistered user then pass 
     *          here 0 (to avoid use of actually logged user id)
     * 
     * @return int|NULL Bonus point amount for registered user (can be 0 too).
     *      Or NULL for unregistered user.
     */
    public function getBonusPoints($userId = null) {
        if (!isset($userId)) {
            $userId = App::getUser('id');
        }
        $points = null;
        if ($userId) {
            $points = (int)$this->findFieldBy('bonus_points', 'run_users_id', $userId);
        }
        return $points;
    }
    
    /**
     * Resets the bonus points of specified user
     * 
     * @see implementationDetails > Bonusy
     * 
     * @param int $userId Optional. If not provided or NULL then defaults to logged user id.
     */
    public function resetBonusPoints($userId = null) {
        if (!isset($userId)) {
            $userId = App::getUser('id');
        }
        if ($userId) {
            $this->update(
                array('bonus_points' => 0),
                array(
                    'validate' => false,
                    'normalize' => false,
                    'conditions' => array(
                        'run_users_id' => $userId,
                    )
                )
            );
        }
    }
    
    /**
     * Adds bonus points of specified user
     * 
     * @see implementationDetails > Bonusy
     * 
     * @param int $userId
     * @param float $points
     */
    public function addBonusPoints($userId, $points) {
        if (
            $userId
            && (float)App::getSetting('Eshop', 'EshopOrder.applicableBonusPoints')
        ) {
            $this->update(
                array('bonus_points' => 'bonus_points + ' . $points),
                array(
                    'validate' => false,
                    'normalize' => false,
                    'conditions' => array(
                        'run_users_id' => $userId,
                    ),
                    'literals' => array(
                        'fields' => array(
                            'bonus_points'
                        )
                    )
                )
            );
        }
    }
    
    /**
     * Removes bonus points of specified user
     * 
     * @see implementationDetails > Bonusy
     * 
     * @param int $userId
     * @param float $points
     */
    public function removeBonusPoints($userId, $points) {
        if (
            $userId
            && (float)App::getSetting('Eshop', 'EshopOrder.applicableBonusPoints')
        ) {
            $this->update(
                array('bonus_points' => 'bonus_points - ' . $points),
                array(
                    'validate' => false,
                    'normalize' => false,
                    'conditions' => array(
                        'run_users_id' => $userId,
                    ),
                    'literals' => array(
                        'fields' => array(
                            'bonus_points'
                        )
                    )
                )
            );
        }
    }
    
    /**
     * Returns the Eshop.EshopOrder.applicableBonusPoints back to UserProfile.bonus_points
     * of specified user after cancelling an order
     * 
     * @see implementationDetails > Bonusy
     * 
     * @param int $userId 
     */
    public function addApplicableBonusPoints($userId) {
        if (
            $userId
            ($applicableBonusPoints = (float)App::getSetting('Eshop', 'EshopOrder.applicableBonusPoints'))
        ) {
            $this->update(
                array('bonus_points' => 'bonus_points + ' . $applicableBonusPoints),
                array(
                    'validate' => false,
                    'normalize' => false,
                    'conditions' => array(
                        'run_users_id' => $userId,
                    ),
                    'literals' => array(
                        'fields' => array(
                            'bonus_points'
                        )
                    )
                )
            );
        }
    }    
    
    /**
     * Takes the Eshop.EshopOrder.applicableBonusPoints from UserProfile.bonus_points
     * of specified user after dis-cancelling an order
     * 
     * @see implementationDetails > Bonusy
     * 
     * @param int $userId 
     */
    public function removeApplicableBonusPoints($userId) {
        if (
            $userId
            ($applicableBonusPoints = (float)App::getSetting('Eshop', 'EshopOrder.applicableBonusPoints'))
        ) {
            $this->update(
                array('bonus_points' => 'bonus_points - ' . $applicableBonusPoints),
                array(
                    'validate' => false,
                    'normalize' => false,
                    'conditions' => array(
                        'run_users_id' => $userId,
                    ),
                    'literals' => array(
                        'fields' => array(
                            'bonus_points'
                        )
                    )
                )
            );
        }
    }    
}