<?php
/**
 * @info To create chained country > region > district selectboxes see http://www.appelsiini.net/projects/chained.
 */
class District extends Model {
    protected $table = 'run_districts';
    
    protected $primaryKey = 'id';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_regions_id' => array('type' => 'int', 'index' => 'index'),
        'name' => array('type' => 'varchar'),
    );
    
    /**
     * Return array of options for selectbox. 
     * 
     * Can be used with chained country > region > district selectboxes (see http://www.appelsiini.net/projects/chained)
     * 
     * @param string $regionId Region id
     * 
     * @return array Array of options for selectbox. If invalid region id then empty array().
     */
    public function getSelectOptionsByRegion($regionId) {
        $options = array();
        $districts = $this->findList(array(
            'key' => 'District.id',
            'fields' => array('District.name'),
            'conditions' => array(
                'District.run_regions_id' => $regionId
            ),
            'order' => 'District.id ASC'
        ));
        if (!empty($districts)) {
            $options = array('' => '') + $districts; // use + instead of array_merge to preserve keys
        }
        return json_encode($options);
    }
        
    public function getInitialRecords() {
        return array(
            // Slovenska republika
            array('id' => 1, 'run_regions_id' => 6, 'name' => 'Bánovce nad Bebravou'),
            array('id' => 2, 'run_regions_id' => 1, 'name' => 'Banská Bystrica'),
            array('id' => 3, 'run_regions_id' => 1, 'name' => 'Banská Štiavnica'),
            array('id' => 4, 'run_regions_id' => 5, 'name' => 'Bardejov'),
            array('id' => 5, 'run_regions_id' => 2, 'name' => 'Bratislava I'),
            array('id' => 6, 'run_regions_id' => 2, 'name' => 'Bratislava II'),
            array('id' => 7, 'run_regions_id' => 2, 'name' => 'Bratislava III'),
            array('id' => 8, 'run_regions_id' => 2, 'name' => 'Bratislava IV'),
            array('id' => 9, 'run_regions_id' => 2, 'name' => 'Bratislava V'),
            array('id' => 10, 'run_regions_id' => 1, 'name' => 'Brezno'),
            array('id' => 11, 'run_regions_id' => 8, 'name' => 'Bytča'),
            array('id' => 12, 'run_regions_id' => 8, 'name' => 'Čadca'),
            array('id' => 13, 'run_regions_id' => 1, 'name' => 'Detva'),
            array('id' => 14, 'run_regions_id' => 8, 'name' => 'Dolný Kubín'),
            array('id' => 15, 'run_regions_id' => 7, 'name' => 'Dunajská Streda'),
            array('id' => 16, 'run_regions_id' => 7, 'name' => 'Galanta'),
            array('id' => 17, 'run_regions_id' => 3, 'name' => 'Gelnica'),
            array('id' => 18, 'run_regions_id' => 7, 'name' => 'Hlohovec'),
            array('id' => 19, 'run_regions_id' => 5, 'name' => 'Humenné'),
            array('id' => 20, 'run_regions_id' => 6, 'name' => 'Ilava'),
            array('id' => 21, 'run_regions_id' => 5, 'name' => 'Kežmarok'),
            array('id' => 22, 'run_regions_id' => 4, 'name' => 'Komárno'),
            array('id' => 23, 'run_regions_id' => 3, 'name' => 'Košice I'),
            array('id' => 24, 'run_regions_id' => 3, 'name' => 'Košice II'),
            array('id' => 25, 'run_regions_id' => 3, 'name' => 'Košice III'),
            array('id' => 26, 'run_regions_id' => 3, 'name' => 'Košice IV'),
            array('id' => 27, 'run_regions_id' => 3, 'name' => 'Košice-okolie'),
            array('id' => 28, 'run_regions_id' => 1, 'name' => 'Krupina'),
            array('id' => 29, 'run_regions_id' => 8, 'name' => 'Kysucké Nové Mesto'),
            array('id' => 30, 'run_regions_id' => 4, 'name' => 'Levice'),
            array('id' => 31, 'run_regions_id' => 5, 'name' => 'Levoča'),
            array('id' => 32, 'run_regions_id' => 8, 'name' => 'Liptovský Mikuláš'),
            array('id' => 33, 'run_regions_id' => 1, 'name' => 'Lučenec'),
            array('id' => 34, 'run_regions_id' => 2, 'name' => 'Malacky'),
            array('id' => 35, 'run_regions_id' => 8, 'name' => 'Martin'),
            array('id' => 36, 'run_regions_id' => 5, 'name' => 'Medzilaborce'),
            array('id' => 37, 'run_regions_id' => 3, 'name' => 'Michalovce'),
            array('id' => 38, 'run_regions_id' => 6, 'name' => 'Myjava'),
            array('id' => 39, 'run_regions_id' => 8, 'name' => 'Námestovo'),
            array('id' => 40, 'run_regions_id' => 4, 'name' => 'Nitra'),
            array('id' => 41, 'run_regions_id' => 6, 'name' => 'Nové Mesto nad Váhom'),
            array('id' => 42, 'run_regions_id' => 4, 'name' => 'Nové Zámky'),
            array('id' => 43, 'run_regions_id' => 6, 'name' => 'Partizánske'),
            array('id' => 44, 'run_regions_id' => 2, 'name' => 'Pezinok'),
            array('id' => 45, 'run_regions_id' => 7, 'name' => 'Piešťany'),
            array('id' => 46, 'run_regions_id' => 1, 'name' => 'Poltár'),
            array('id' => 47, 'run_regions_id' => 5, 'name' => 'Poprad'),
            array('id' => 48, 'run_regions_id' => 6, 'name' => 'Považská Bystrica'),
            array('id' => 49, 'run_regions_id' => 5, 'name' => 'Prešov'),
            array('id' => 50, 'run_regions_id' => 6, 'name' => 'Prievidza'),
            array('id' => 51, 'run_regions_id' => 6, 'name' => 'Púchov'),
            array('id' => 52, 'run_regions_id' => 1, 'name' => 'Revúca'),
            array('id' => 53, 'run_regions_id' => 1, 'name' => 'Rimavská Sobota'),
            array('id' => 54, 'run_regions_id' => 3, 'name' => 'Rožňava'),
            array('id' => 55, 'run_regions_id' => 8, 'name' => 'Ružomberok'),
            array('id' => 56, 'run_regions_id' => 5, 'name' => 'Sabinov'),
            array('id' => 57, 'run_regions_id' => 2, 'name' => 'Senec'),
            array('id' => 58, 'run_regions_id' => 7, 'name' => 'Senica'),
            array('id' => 59, 'run_regions_id' => 7, 'name' => 'Skalica'),
            array('id' => 60, 'run_regions_id' => 5, 'name' => 'Snina'),
            array('id' => 61, 'run_regions_id' => 3, 'name' => 'Sobrance'),
            array('id' => 62, 'run_regions_id' => 3, 'name' => 'Spišská Nová Ves'),
            array('id' => 63, 'run_regions_id' => 5, 'name' => 'Stará Ľubovňa'),
            array('id' => 64, 'run_regions_id' => 5, 'name' => 'Stropkov'),
            array('id' => 65, 'run_regions_id' => 5, 'name' => 'Svidník'),
            array('id' => 66, 'run_regions_id' => 4, 'name' => 'Šaľa'),
            array('id' => 67, 'run_regions_id' => 4, 'name' => 'Topoľčany'),
            array('id' => 68, 'run_regions_id' => 3, 'name' => 'Trebišov'),
            array('id' => 69, 'run_regions_id' => 6, 'name' => 'Trenčín'),
            array('id' => 70, 'run_regions_id' => 7, 'name' => 'Trnava'),
            array('id' => 71, 'run_regions_id' => 8, 'name' => 'Turčianske Teplice'),
            array('id' => 72, 'run_regions_id' => 8, 'name' => 'Tvrdošín'),
            array('id' => 73, 'run_regions_id' => 1, 'name' => 'Veľký Krtíš'),
            array('id' => 74, 'run_regions_id' => 5, 'name' => 'Vranov nad Topľou'),
            array('id' => 75, 'run_regions_id' => 4, 'name' => 'Zlaté Moravce'),
            array('id' => 76, 'run_regions_id' => 1, 'name' => 'Zvolen'),
            array('id' => 77, 'run_regions_id' => 1, 'name' => 'Žarnovica'),
            array('id' => 78, 'run_regions_id' => 1, 'name' => 'Žiar nad Hronom'),
            array('id' => 79, 'run_regions_id' => 8, 'name' => 'Žilina'),
            // Česká republika
            array('id' => 80, 'run_regions_id' => 9, 'name' => 'Praha'),
            array('id' => 81, 'run_regions_id' => 10, 'name' => 'České Budejovice'),
            array('id' => 82, 'run_regions_id' => 10, 'name' => 'Český Krumlov'),
            array('id' => 83, 'run_regions_id' => 10, 'name' => 'Jindřichův Hradec'),
            array('id' => 84, 'run_regions_id' => 10, 'name' => 'Písek'),
            array('id' => 85, 'run_regions_id' => 10, 'name' => 'Prachatice'),
            array('id' => 86, 'run_regions_id' => 10, 'name' => 'Strakonice'),
            array('id' => 87, 'run_regions_id' => 10, 'name' => 'Tábor'),
            array('id' => 88, 'run_regions_id' => 11, 'name' => 'Blansko'),
            array('id' => 89, 'run_regions_id' => 11, 'name' => 'Břeclav'),
            array('id' => 90, 'run_regions_id' => 11, 'name' => 'Brno - město'),
            array('id' => 91, 'run_regions_id' => 11, 'name' => 'Brno - venkov'),
            array('id' => 92, 'run_regions_id' => 11, 'name' => 'Hodonín'),
            array('id' => 93, 'run_regions_id' => 11, 'name' => 'Vyškov'),
            array('id' => 94, 'run_regions_id' => 11, 'name' => 'Znojmo'),
            array('id' => 95, 'run_regions_id' => 12, 'name' => 'Cheb'),
            array('id' => 96, 'run_regions_id' => 12, 'name' => 'Karlovy Vary'),
            array('id' => 97, 'run_regions_id' => 12, 'name' => 'Sokolov'),
            array('id' => 98, 'run_regions_id' => 13, 'name' => 'Havlíčkův Brod'),            
            array('id' => 99, 'run_regions_id' => 13, 'name' => 'Jihlava'),            
            array('id' => 100, 'run_regions_id' => 13, 'name' => 'Pelhřimov'),            
            array('id' => 101, 'run_regions_id' => 13, 'name' => 'Třebíč'),            
            array('id' => 102, 'run_regions_id' => 13, 'name' => 'Žďár nad Sázavou'),            
            array('id' => 103, 'run_regions_id' => 14, 'name' => 'Hradec Králové'),            
            array('id' => 104, 'run_regions_id' => 14, 'name' => 'Jičín'),            
            array('id' => 105, 'run_regions_id' => 14, 'name' => 'Náchod'),            
            array('id' => 106, 'run_regions_id' => 14, 'name' => 'Rychnov nad Kněžnou'),            
            array('id' => 107, 'run_regions_id' => 14, 'name' => 'Trutnov'),            
            array('id' => 108, 'run_regions_id' => 15, 'name' => 'Česká Lípa'),            
            array('id' => 109, 'run_regions_id' => 15, 'name' => 'Jablonec nad Nisou'),            
            array('id' => 110, 'run_regions_id' => 15, 'name' => 'Liberec'),            
            array('id' => 111, 'run_regions_id' => 15, 'name' => 'Semily'),            
            array('id' => 112, 'run_regions_id' => 16, 'name' => 'Bruntál'),            
            array('id' => 113, 'run_regions_id' => 16, 'name' => 'Frýdek - Místek'),            
            array('id' => 114, 'run_regions_id' => 16, 'name' => 'Karviná'),            
            array('id' => 115, 'run_regions_id' => 16, 'name' => 'Nový Jičín'),            
            array('id' => 116, 'run_regions_id' => 16, 'name' => 'Opava'),            
            array('id' => 117, 'run_regions_id' => 16, 'name' => 'Ostrava'),            
            array('id' => 118, 'run_regions_id' => 17, 'name' => 'Jeseník'),            
            array('id' => 119, 'run_regions_id' => 17, 'name' => 'Olomouc'),            
            array('id' => 120, 'run_regions_id' => 17, 'name' => 'Přerov'),            
            array('id' => 121, 'run_regions_id' => 17, 'name' => 'Prostějov'),            
            array('id' => 122, 'run_regions_id' => 17, 'name' => 'Šumperk'),            
            array('id' => 123, 'run_regions_id' => 18, 'name' => 'Chrudim'),            
            array('id' => 124, 'run_regions_id' => 18, 'name' => 'Pardubice'),            
            array('id' => 125, 'run_regions_id' => 18, 'name' => 'Svitavy'),            
            array('id' => 126, 'run_regions_id' => 18, 'name' => 'Ústí nad Orlicí'),            
            array('id' => 127, 'run_regions_id' => 19, 'name' => 'Domažlice'),            
            array('id' => 128, 'run_regions_id' => 19, 'name' => 'Klatovy'),            
            array('id' => 129, 'run_regions_id' => 19, 'name' => 'Plzeň - jih'),            
            array('id' => 130, 'run_regions_id' => 19, 'name' => 'Plzeň - město'),            
            array('id' => 131, 'run_regions_id' => 19, 'name' => 'Plzeň - sever'),            
            array('id' => 132, 'run_regions_id' => 19, 'name' => 'Rokycany'),            
            array('id' => 133, 'run_regions_id' => 19, 'name' => 'Tachov'),            
            array('id' => 134, 'run_regions_id' => 20, 'name' => 'Benešov'),            
            array('id' => 135, 'run_regions_id' => 20, 'name' => 'Beroun'),            
            array('id' => 136, 'run_regions_id' => 20, 'name' => 'Kladno'),            
            array('id' => 137, 'run_regions_id' => 20, 'name' => 'Kolín'),            
            array('id' => 138, 'run_regions_id' => 20, 'name' => 'Kutná Hora'),            
            array('id' => 139, 'run_regions_id' => 20, 'name' => 'Mělník'),            
            array('id' => 140, 'run_regions_id' => 20, 'name' => 'Mladá Boleslav'),            
            array('id' => 141, 'run_regions_id' => 20, 'name' => 'Nymburk'),            
            array('id' => 142, 'run_regions_id' => 20, 'name' => 'Praha - východ'),            
            array('id' => 143, 'run_regions_id' => 20, 'name' => 'Praha - západ'),            
            array('id' => 144, 'run_regions_id' => 20, 'name' => 'Příbram'),            
            array('id' => 145, 'run_regions_id' => 20, 'name' => 'Rakovník'),            
            array('id' => 146, 'run_regions_id' => 21, 'name' => 'Děčín'),            
            array('id' => 147, 'run_regions_id' => 21, 'name' => 'Chomutov'),            
            array('id' => 148, 'run_regions_id' => 21, 'name' => 'Litoměřice'),            
            array('id' => 149, 'run_regions_id' => 21, 'name' => 'Louny'),            
            array('id' => 150, 'run_regions_id' => 21, 'name' => 'Most'),            
            array('id' => 151, 'run_regions_id' => 21, 'name' => 'Teplice'),            
            array('id' => 152, 'run_regions_id' => 21, 'name' => 'Ústí nad Labem'),            
            array('id' => 153, 'run_regions_id' => 22, 'name' => 'Kroměříž'),            
            array('id' => 154, 'run_regions_id' => 22, 'name' => 'Uherské Hradište'),            
            array('id' => 155, 'run_regions_id' => 22, 'name' => 'Vsetín'),            
            array('id' => 156, 'run_regions_id' => 22, 'name' => 'Zlín'),            
        );
    }
    
    
    
}