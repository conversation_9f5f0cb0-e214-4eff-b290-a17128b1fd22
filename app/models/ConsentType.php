<?php
/**
 * Use ConsentType class in following way:
 * 
 * 1] To add a new consent type:
 * 
 *      App::loadModel('App', 'ConsentType');
 *      $ConsentType = new Consent();
 *      $ConsentType->add('EshopTermsAndConditionsConsent', 'App.User');
 * 
 * Or
 * 
 *      $ConsentType->add('NewsletterConsent', 'Mailer.MailerContact');
 * 
 * The best place to add a new consent type is module install method, 
 * e.g. App::install(), Mailer::install(), Eshop::install()
 */
class ConsentType extends Model {
    protected $table = 'run_consent_types';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'pid' => array('type' => 'varchar', 'length' => 50, 'comment' => 'E.g. NewsletterConsent, EshopTermsAndConditionsConsent'),
        'owner_model' => array('type' => 'varchar', 'length' => 50, 'comment' => 'Consent owner is always some record. This is the model name that record belongs to. To ensure uniquity the model name should be qualified by its module, e.g. App.User, Mailer.MailerContact. For owner_id see the table of run_consents'),
        'description' => array('type' => 'varchar', 'default' => null),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
    protected $tableIndexes = array(
        array(
            'type' => 'unique',
            'fields' => array('pid', 'owner_model'),
            'name' => 'Owner consent types index'
        ),
    );
    
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(
            'pid' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide a pid'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide a pid'),
                ),
                array(
                    'rule' => array('maxLength', $this->schema['pid']['length']),
                    'message' => __v(__FILE__, 'Maximal allowed pid length is %s characters', $this->schema['pid']['length']),
                ),
            ),
            'owner_model' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Provide a owner model'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Provide a owner model'),
                ),
                array(
                    'rule' => array('maxLength', $this->schema['owner_model']['length']),
                    'message' => __v(__FILE__, 'Maximal allowed owner model length is %s characters', $this->schema['owner_model']['length']),
                ),
            ),
        );
    }
    
    /**
     * Adds a new consent type.
     * If specified consent type exists then nothing happens
     * 
     * @param string $pid Consent type pid, e.g. NewsletterConsent, EshopTermsAndConditionsConsent
     * @param string $ownerModel Consent type owner model. Owner is always some record. This is the 
     *      model name that record belongs to. To ensure uniquity the model name 
     *      should be qualified by its module, e.g. App.User, Mailer.MailerContact.
     * @param array $options Following are available:
     *      - 'description' (string) Defaults to NULL.
     * 
     * @throws Exception
     */
    public function add($pid, $ownerModel, $options = array()) {
        $defaults = array(
            'description' => null,
        );
        $options = array_merge($defaults, $options);
        $data = array(
            'pid' => $pid,
            'owner_model' => $ownerModel,
            'description' => $options['description'],
        );
        try {
            if (!$this->save($data)) {
                $errors = $this->getErrors();
                $message = '';
                foreach ($errors as $field => $fieldErrors) {
                    $message .= $message ? '; ' : '';
                    $message .= $field . ' - ' . implode(', ', $fieldErrors);
                }
                throw new Exception(__e(__FILE__, 'Consent type addition has failed with following errors: %s', $message));
            }
        } 
        catch (Throwable $e) {
            // if DB error is not ER_DUP_KEYNAME (http://dev.mysql.com/doc/refman/5.0/en/error-messages-server.html#error_er_dup_keyname)
            // then rethrow the exception
            if ($e->getCode() != 1062) {
                throw $e;
            }
        }
    }
    
////@todo    
//    /**
//     * Removes specified consent type and all related consents
//     * 
//     * @param string $pid Consent type pid, e.g. NewsletterConsent, EshopTermsAndConditionsConsent
//     * @param string $ownerModel Consent type owner model. Owner is always some record. This is the 
//     *      model name that record belongs to. To ensure uniquity the model name 
//     *      should be qualified by its module, e.g. App.User, Mailer.MailerContact.
//     */
//    public function remove($pid, $ownerModel) {
//        
//    }
    
}
