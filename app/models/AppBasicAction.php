<?php
class AppBasicAction extends Model {
    
    public function __construct() {
        parent::__construct();
        
        $this->validations = array(
//            'fullname' => array(
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Please, enter your firstname and lastname'),
//                    'alternative' => 'submitContactForm',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Please, enter your firstname and lastname'),
//                    'alternative' => 'submitContactForm',
//                ),
//            ),
            'email' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím e-mail'),
                    'alternative' => 'submitContactForm',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím e-mail'),
                    'alternative' => 'submitContactForm',
                ),
                array(
                    'rule' => 'email',
                    'message' => __v(__FILE__, 'Zadajte prosím platnú e-mailovú adresu'),
                    'alternative' => 'submitContactForm',
                ),
            ),
            'phone' => array(
                array(
                    'rule' => 'phone',
                    'message' => __v(__FILE__, 'Zadajte prosím platné telefónne číslo'),
                    'alternative' => 'submitContactForm',
                ),
            ),
//            'contact' => array(
//                array(
//                    'rule' => 'required',
//                    'message' => __v(__FILE__, 'Please, enter phone or e-mail'),
//                    'alternative' => 'submitContactForm',
//                ),
//                array(
//                    'rule' => 'notEmpty',
//                    'message' => __v(__FILE__, 'Please, enter phone or e-mail'),
//                    'alternative' => 'submitContactForm',
//                ),
//                array(
//                    'rule' => 'phoneOrEmail',
//                    'message' => __v(__FILE__, 'Please, enter valid phone number or e-mail address'),
//                    'alternative' => 'submitContactForm',
//                ),
//            ),
            'message' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please, enter message or question'),
                    'alternative' => 'submitContactForm',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please, enter message or question'),
                    'alternative' => 'submitContactForm',
                ),
            ),
            'sc' => array( // captcha
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please, copy the security code'),
                    'alternative' => 'submitContactForm',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' =>  __v(__FILE__, 'Please, copy the security code'),
                    'alternative' => 'submitContactForm',
                ),
                array(
                    'rule' => 'captcha',
                    'message' =>  __v(__FILE__, 'Security code is not valid'),
                    'alternative' => 'submitContactForm',
                ),
            )
        );
    }
    
    /**
     * Checks if the provided value is either valid phone number of valid email address
     * 
     * @param string $value
     * @return bool
     */
    public function validate_phoneOrEmail($value) {
        return Validate::phone($value) || Validate::email($value);
    }
    
    public function submitContactForm($data) {
        if (!$this->validate($data, array('alternative' => 'submitContactForm'))) {
            return false;
        }
        try {
            // get source content
            $sourceContent = '';
            $Content = App::loadModel('App', 'WebContent', true);
            if (
                !empty($data['contentId'])
                && ($content = $Content->findFirst(array(
                    'fields' => array('name', 'locator'),
                    'conditions' => array(
                        'id' => $data['contentId'],
                        'locator !=' => array(null, ''),
                    )
                )))
            ) {
                $contentUrl = App::getUrl(array(
                    'locator' => $content['locator'], 
                    'absolute' => true,
                ));
                $sourceContent = sprintf(
                    '<a href="%s" target="_blank">"%s" - %s</a>',
                    $contentUrl, $content['name'], $contentUrl
                );
                $sourceContent = __(__FILE__, 'Sent from page %s', $sourceContent);
            }
            $contact = '';
            if (!empty($data['contact'])) {
                $contact = $data['contact'];
            }
            $email = '';
            if (!empty($data['email'])) {
                $email = $data['email'];
            }
            if (empty($data['fullname'])) {
                if (!empty($email)) {
                    $data['fullname'] = $email;
                }
                elseif (Validate::email($contact)) {
                    $data['fullname'] = $contact;
                }
            }
            elseif (empty($contact)) {
                $contact = $data['fullname'];
            }
            $phone = '';
            if (!empty($data['phone'])) {
                $phone = $data['phone'];
            }
            App::sendEmail(
                nl2br(
                    $data['message'] . PHP_EOL . PHP_EOL . 
                    __(
                        __FILE__, 
                        'Answer to %s', 
                        trim($contact . ', '. $email . ', ' . $phone, ', ')
                    ) . 
                    PHP_EOL . $sourceContent
                ),
                App::getSetting('App', 'email.to'),
                array(
                    'subject' => __(__FILE__, 'Message from %s', $data['fullname']),
                    'from' => array(
                        App::getSetting('App', 'email.from') => App::getSetting('App', 'name'),
                    ),
                    'cc' => App::getSetting('App', 'email.cc'),
                    'replyTo' => $email,
                )
            );
        } 
        catch (Throwable $e) {
            $this->setError($e->getMessage());
            App::logError($e->getMessage(), array(
                'var' => $e,
                'email' => true,
            ));
            return false;
        }
        return true;
    }
}
