<?php
class Tool extends Model {
    
    public function __construct() {
        parent::__construct();
        
        $this->validations = array(
            // swapWebContentPids
            'pid1' => array(
                array(
                    'rule' => 'required',
                    'message' => __d(__FILE__, 'Enter pid'),
                    'alternative' => 'swapWebContentPids',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __d(__FILE__, 'Enter pid'),
                    'alternative' => 'swapWebContentPids',
                ),
            ),    
            'pid2' => array(
                array(
                    'rule' => 'required',
                    'message' => __d(__FILE__, 'Enter pid'),
                    'alternative' => 'swapWebContentPids',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __d(__FILE__, 'Enter pid'),
                    'alternative' => 'swapWebContentPids',
                ),
            ),    
            // changeEshopOrderNumbers
            'oldNumber' => array(
                array(
                    'rule' => 'required',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => 'changeEshopOrderNumbers',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => 'changeEshopOrderNumbers',
                ),
                array(
                    'rule' => 'intNumber',
                    'message' => __d(__FILE__, 'Zadajte celé číslo'),
                    'alternative' => 'changeEshopOrderNumbers',
                ),
                array(
                    'rule' => function ($oldNumber, $field, $data, &$validation) {
                        $Order = App::loadModel('Eshop', 'EshopOrder', true);
                        $created = null;
                        if (!($created = $Order->findFieldBy('created', 'number', $oldNumber))) {
                            $validation['message'] = __d(__FILE__, 'Zadajte číslo existujúcej objednávky');
                            return false;
                        }
                        if (Date::getDiff('d', $created, date('Y-m-d H:i:s')) > 7) {
                            $validation['message'] = __d(__FILE__, 'Prečíslovať je možné len objednávky, ktoré nie sú staršie ako 7 dni');
                            return false;
                        }
                        return true;
                    },
                    'alternative' => 'changeEshopOrderNumbers',
                ),
            ),    
            'newNumber' => array(
                array(
                    'rule' => 'required',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => 'changeEshopOrderNumbers',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => 'changeEshopOrderNumbers',
                ),
                array(
                    'rule' => 'intNumber',
                    'message' => __d(__FILE__, 'Zadajte celé číslo'),
                    'alternative' => 'changeEshopOrderNumbers',
                ),
                array(
                    'rule' => function($newNumber, $field, $data, &$validation) {
                        if (!empty($data['oldNumber'])) {
                            if ($newNumber === $data['oldNumber']) {                                
                                $validation['message'] = __d(__FILE__, 'Zadajte iné číslo ako staré číslo');
                                return false;
                            }
                            if (
                                $newNumber < $data['oldNumber']
                                && ($Order = App::loadModel('Eshop', 'EshopOrder', true))
                                && $Order->findFieldBy('number', 'number', $newNumber)
                            ) {                                
                                $validation['message'] = __d(__FILE__, 'Objednávka %s už existuje. Prečíslovanie nie je možné lebo by vznikli dve objednávky s rovnakým číslom', $newNumber);
                                return false;
                            }
                        }
                        return true;
                    },
                    'alternative' => 'changeEshopOrderNumbers',
                )
            ),    
            // refactorContentBlockField
            'contentBlockModel' => array(
                array(
                    'rule' => 'required',
                    'message' => __d(__FILE__, 'Vyberte'),
                    'alternative' => 'refactorContentBlockField',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __d(__FILE__, 'Vyberte'),
                    'alternative' => 'refactorContentBlockField',
                ),
            ),    
            'oldField' => array(
                array(
                    'rule' => 'required',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => 'refactorContentBlockField',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => 'refactorContentBlockField',
                ),
            ),    
            'newField' => array(
                array(
                    'rule' => 'required',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => 'refactorContentBlockField',
                ),
            ),    
            'removeOldField' => array(
                array(
                    'rule' => 'required',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => 'refactorContentBlockField',
                ),
            ),
            // getEshopOrdersStatistics
            'start_date' => array(
                array(
                    'rule' => 'required',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
                array(
                    'rule' => 'date',
                    'message' => __d(__FILE__, 'Zadajte platný dátum'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
            ),             
            'end_date' => array(
                array(
                    'rule' => 'required',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
                array(
                    'rule' => 'date',
                    'message' => __d(__FILE__, 'Zadajte platný dátum'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
                array(
                    'rule' => function($value, $data) {
                        if (
                            !empty($data['start_date'])
                            && Validate::date($data['start_date'])
                            && $value < $data['start_date']
                        ) {
                            return false;
                        }
                        return true;
                    },
                    'message' => __d(__FILE__, 'Zadajte dátum rovný alebo väčší ako dátum "od"'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
            ),             
            'compare_start_date' => array(
                array(
                    'rule' => array('notEmptyIf', 'compare_end_date'),
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => array('getEshopOrdersStatistics'),
                    'force' => true,
                ),
                array(
                    'rule' => 'date',
                    'message' => __d(__FILE__, 'Zadajte platný dátum'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
            ),             
            'compare_end_date' => array(
                array(
                    'rule' => array('notEmptyIf', 'compare_start_date'),
                    'message' => __d(__FILE__, 'Zadajte'),
                    'alternative' => array('getEshopOrdersStatistics'),
                    'force' => true,
                ),
                array(
                    'rule' => 'date',
                    'message' => __d(__FILE__, 'Zadajte platný dátum'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
                array(
                    'rule' => function($value, $data) {
                        if (
                            !empty($data['compare_start_date'])
                            && Validate::date($data['compare_start_date'])
                            && $value < $data['compare_start_date']
                        ) {
                            return false;
                        }
                        return true;
                    },
                    'message' => __d(__FILE__, 'Zadajte dátum rovný alebo väčší ako dátum "od"'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
            ),      
            'price_from' => array(
                array(
                    'rule' => '/[0-9]*\.?[0-9]+/',
                    'message' => __d(__FILE__, 'Zadajte celé alebo desatinné číslo'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
            ),             
            'price_to' => array(
                array(
                    'rule' => '/[0-9]*\.?[0-9]+/',
                    'message' => __d(__FILE__, 'Zadajte celé alebo desatinné číslo'),
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
            ),
            'exclude_order_numbers' => array(
                array(
                    'rule' => function($numbers, $field, $data, &$validation) {
                        $match = array();
                        foreach ($numbers as $number) {
                            if (
                                !preg_match('/^[1-9][0-9]{4,}$/', $number)
                                && (
                                    !preg_match('/^([1-9][0-9]{4,})-([1-9][0-9]{4,})$/', $number, $match)
                                    || $match[1] >= $match[2]
                                )
                            ) {
                                $validation['message'] = __d(__FILE__, 'V zozname sa nachádza neplatná položka "%s"', $number);
                                return false;
                            }
                        }
                        return true;
                    },
                    'alternative' => array('getEshopOrdersStatistics'),
                ),
            ),
        );
    }
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        
        if (in_array('getEshopOrdersStatistics', $options['alternative'])) {
            $data['start_date'] = trim($data['start_date']);
            if (!empty($data['start_date'])) {
                $data['start_date'] = Date::format($data['start_date'], 'Y-m-d');
            }
            $data['end_date'] = trim($data['end_date']);
            if (!empty($data['end_date'])) {
                $data['end_date'] = Date::format($data['end_date'], 'Y-m-d');
            }
            $data['compare_start_date'] = trim($data['compare_start_date']);
            if (!empty($data['compare_start_date'])) {
                $data['compare_start_date'] = Date::format($data['compare_start_date'], 'Y-m-d');
            }
            $data['compare_end_date'] = trim($data['compare_end_date']);
            if (!empty($data['compare_end_date'])) {
                $data['compare_end_date'] = Date::format($data['compare_end_date'], 'Y-m-d');
            }
            $data['price_from'] = trim($data['price_from']);
            if ($data['price_from'] !== '') {
                $data['price_from'] = str_replace(' ', '', $data['price_from']);
                $data['price_from'] = str_replace(',', '.', $data['price_from']);
            }
            $data['price_to'] = trim($data['price_to']);
            if ($data['price_to'] !== '') {
                $data['price_to'] = str_replace(' ', '', $data['price_to']);
                $data['price_to'] = str_replace(',', '.', $data['price_to']);
            }
            if (!empty($data['exclude_emails'])) {
                $data['exclude_emails'] = array_filter(array_map('trim', explode(',', $data['exclude_emails'])));
            }
            if (!empty($data['exclude_emails_containing'])) {
                $data['exclude_emails_containing'] = array_filter(array_map('trim', explode(',', $data['exclude_emails_containing'])));
            }
            if (!empty($data['exclude_comments_containing'])) {
                $data['exclude_comments_containing'] = array_filter(array_map('trim', explode(',', $data['exclude_comments_containing'])));
            }
            if (!empty($data['exclude_order_numbers'])) {
                $data['exclude_order_numbers'] = array_filter(explode(',', str_replace(' ', '', $data['exclude_order_numbers'])));
            }
        }
        
        return $data;
    }
    
    /**
     * Compiles all .po files found in project into .php or .js files
     * 
     * @return array Result messages array
     */
    public function compilePoFiles() {
        $modules = App::getModules();
        $result = array();
        foreach ($modules as $module) {
            $modulePath = App::getModulePath($module);
            $dir = $modulePath . DS . 'locale';
            $items = @scandir(ROOT . $dir);
            if (!empty($items)) {
                foreach ($items as $item) {
                    $file = $dir . DS . $item;
                    if (
                        is_dir(ROOT . $file)
                        ||
                        strtolower(File::getPathinfo($file, PATHINFO_EXTENSION)) !== 'po'
                    ) {
                        continue;
                    }
                    try {
                        App::compilePoFile($file);
                        $result[] = __d(__FILE__, 'File %s succesfully compiled', $file);
                    } 
                    catch (Throwable $e) {
                        $result[] = __d(__FILE__, 'FAILURE - compilation of file %s has failed with following error: %s', $file, $e->getMessage());
                    }
                }
            }
        }
        return $result;
    }
    
    public function updateSettings($module) {
        $modulePath = App::getModulePath($module);
        $settingsFile = ROOT . $modulePath . DS . 'config' . DS . 'settings.php';
        if (!is_readable($settingsFile)) {
            return false;
        }
        require $settingsFile;
        $settings = (array)Sanitize::value($settings);
        $Setting = App::loadModel('App', 'Setting', true);
        foreach ($settings as $pid => $data) {
            $setting = $Setting->findFirst(array(
                'conditions' => array(
                    'module' => $module,
                    'pid' => $pid,
                )
            ));
            if ($setting) {
                continue;
            }
            $data['module'] = $module;
            $data['pid'] = $pid;
            unset($data['id']);
            $Setting->save($data, array(
                'normalize' => false,
                'validate' => false,
            ));
        }
        return true;
    }
    
    /**
     * @param string $module Module name to update contents for, e.g. 'App', 'Eshop'
     * @param string $pid Optional. Content pid to update (create). The content
     *      must be defined in contents.php file of specified module. If provided
     *      then only this one contents is updated (created). Defaults to NULL, it
     *      means all $module contents are updated.
     * @param boolean $force Optional. Considered only if $pid is provided. If checked 
     *      then specified content is overwritten by its version defined in contents.php 
     *      of provided $module even in case that the specified content exists already. 
     *      If not checked then existing specified content is let untouched. Defaults 
     *      to FALSE.
     * 
     * @return array Result array containing 'success' (bool) and 'log' (array) items.
     */
    public function updateContents($module, $pid = null, $force = false) {
        $result = array(
            'success' => false,
            'log' => array(),
        );
        // load contents file
        $modulePath = App::getModulePath($module);
        $contentsFile = ROOT . $modulePath . DS . 'config' . DS . 'contents.php';
        if (!is_readable($contentsFile)) {
            $result['log'][] = __d(__FILE__, 'File %s does not exist or it is not readable', $contentsFile);
            return $result;
        }
        require $contentsFile;
        $contents = (array)Sanitize::value($contents);
        if ($pid) {
            if (empty($contents[$pid])) {
                $result['log'][] = __d(__FILE__, 'Unexisting content %s under module %s', $pid, $module);
                return $result;
            }
            $contents = array(
                $pid => $contents[$pid]
            );
        }
        elseif ($force) {
            $result['log'][] = __d(__FILE__, 'Only single content specified by pid can be overwritten');
            $force = false;
        }
        App::loadModel('App', 'WebContent');
        $Content = new WebContent();
        App::loadModel('App', 'ContentBlockInstance');
        $BlockInstance = new ContentBlockInstance();
        App::loadModel('ContentBlock', 'ContentBlockHtml');
        $HtmlBlock = new ContentBlockHtml();
        // get all active app langs
        $langs = App::getPropertyLangs();
        // get all existing content roots in db 
        $roots = $Content->findList(array(
            'key' => 'lang',
            'conditions' => array('path' => '-'),
            'fields' => array('id'),
        ));
        // create missing roots
        foreach ($langs as $lang) {
            if (isset($roots[$lang])) {
                continue;
            }
            $Content->addTreeNode(null, array(
                'pid' => 'root',
                'name' => 'root',
                'lang' => $lang,
            ));
            $roots[$lang] = $Content->getPropertyId();
        }
        // create loaded contents
        foreach ($contents as $pid => $data) {
            // normalize
            if (empty($data['name'])) {
                $data['name'] = $pid;
            }
            $data['pid'] = $pid;
            unset($data['id']);
            // validate
            if (empty($data['parent'])) {
                $result['log'][] = __d(__FILE__, 'Missing parent definition for content %s', $pid);
                continue;
            }
            // save the content text as ContentBlockHtml
            $text = '';
            if (
                isset($data['text'])
                && !Validate::emptyValue($data['text'])
            ) {
                $text = $data['text'];
                $data['text'] = '';
            }
            // get langs for which content should be created
            if (!empty($data['lang'])) {
                $contentLangs = array_map('trim', explode(',', $data['lang']));
                $contentLangs = array_intersect($contentLangs, $langs);
            }
            else {
                $contentLangs = $langs;
            }
            // check contens for $pid and $lang
            foreach ($contentLangs as $lang) {
                // skip existing undeleted contents
                $contentId = null;
                if (
                    ($contentId = $Content->findField('id', array(
                        'conditions' => array(
                            'pid' => $pid,
                            'lang' => $lang,
                        ),
                    )))
                    && !$force
                ) {
                    $result['log'][] = __d(__FILE__, 'Content %s exists already in lang %s', $pid, $lang);
                    continue;
                }
                // remove existing deleted items
                $deletedItems = $Content->find(array(
                    'conditions' => array(
                        'pid' => $pid,
                        'lang' => $lang,
                        'deleted !=' => null,
                    ),
                    'fields' => array('id'),
                    'ignoreSoftDeleted' => false,
                ));
                foreach ($deletedItems as $deletedItem) {
                    $deletedItemIds = $Content->deleteTreeNode($deletedItem['id'], array(
                        'softDelete' => false,
                    ));
                    $BlockInstance->delete(array(
                        'conditions' => array(
                            'owner_model' => 'App.WebContent',
                            'owner_id' => $deletedItemIds,
                        ),
                        'processContentBlockData' => true,
                    ));
                }
                if (!$contentId) {
                    // get parent id
                    $parent = $Content->findFirst(array(
                        'fields' => array('id', 'hidden'),
                        'conditions' => array(
                            'pid' => $data['parent'],
                            'lang' => $lang,
                        )
                    ));
                    if (!$parent) {
                        $result['log'][] = __d(__FILE__, 'Unexisting parent %s for content %s in lang %s', $data['parent'], $pid, $lang);
                        continue;
                    }
                    $data['lang'] = $lang;
                    $data['hidden'] = $parent['hidden'];
                    if (!empty($data['locator'])) {
                        $data['locator'] = $Content->getUniqueSlug($data['locator'], array('lang' => $lang));
                    }
                    if (!array_key_exists('active', $data)) {
                        $data['active'] = true;
                    }
                    $Content->addTreeNode($parent['id'], $data);
                    $contentId = $Content->getPropertyId();
                }
                else {
                    // update content data
                    $data['id'] = $contentId;
                    $Content->save($data);
                    // remove existing content blocks of opdated content ($force === true)
                    $BlockInstance->delete(array(
                        'conditions' => array(
                            'owner_model' => 'App.WebContent',
                            'owner_id' => $contentId,
                        ),
                        'processContentBlockData' => true,
                    ));
                }
                // convert content text to html content block
                if (!Validate::emptyValue($text)) {
                    $BlockInstance->setPropertyContentBlock($HtmlBlock);
                    $BlockInstance->save(
                        array(
                            'owner_model' => 'App.WebContent',
                            'owner_id' => $contentId,
                            'content_block_model' => 'ContentBlock.ContentBlockHtml',
                            'html' => $text,
                            'active' => true,
                        ),
                        array(
                            'processContentBlockData' => true
                        )
                    );
                }
                if (!empty($data['blocks'])) {
                    if (!is_array($data['blocks'])) {
                        $result['log'][] = __d(__FILE__, 'Invalid definition of blocks for content %s. Blocks must be defined as array.', $pid);
                        continue;
                    }
                    foreach ($data['blocks'] as $blockIndex => $block) {
                        if (!is_array($block)) {
                            $result['log'][] = __d(__FILE__, 'Invalid definition of block no. %s for content %s. Block must be defined as array.', $blockIndex, $pid);
                            continue;
                        }
                        if (empty($block['content_block_model'])) {
                            $result['log'][] = __d(__FILE__, 'Missing content_block_model for block no. %s of content %s', $blockIndex, $pid);
                            continue;
                        }
                        list($blockModuleName, $blockModelName) = explode(
                            '.', $block['content_block_model']
                        );
                        try {
                            $Block = App::loadModel(
                                $blockModuleName, 
                                $blockModelName, 
                                true
                            );
                            if (array_key_exists('content_block_data', $block)) {
                                if (is_array($block['content_block_data'])) {
                                    $block = array_merge($block, $block['content_block_data']);
                                }
                                unset($block['content_block_data']);
                            }
                            $block['owner_model'] = 'App.WebContent';
                            $block['owner_id'] = $contentId;
                            if (!array_key_exists('active', $block)) {
                                $block['active'] = true;
                            }
                            $BlockInstance->setPropertyContentBlock($Block);
                            $BlockInstance->save(
                                $block,
                                array(
                                    'processContentBlockData' => true
                                )
                            );
                        } 
                        catch (Throwable $e) {
                            $result['log'][] = __d(__FILE__, 'Invalid content_block_model for block no. %s of content %s', $blockIndex, $pid);
                            continue;
                        }
                    }
                }
            }
        }
        $result['success'] = true;
        return $result;
    }
    
    /**
     * 
     * @param type $module
     * @param type $model
     * @param type $rootId
     * @throws Exception
     */
    public function sortTreeByName($module, $model, $rootId) {
        if (empty($module)) throw new Exception('Module is not defined');
        if (empty($model)) throw new Exception('Model is not defined');
        if (empty($rootId)) throw new Exception('Root id is not defined');
        
        $Model = App::loadModel($module, $model, true);
        
        // get max depth of tree
        $maximumRecord = $Model->findFirst(array(
            'fields' => array(
                'MAX(LENGTH( path )  - LENGTH( REPLACE( path,  "-",  "" ) )) as maximum'
            ),
            'literals' => array(
                'fields' => array(
                    'MAX(LENGTH( path )  - LENGTH( REPLACE( path,  "-",  "" ) )) as maximum'
                )            
            )
        ));
        $maxDepth = $maximumRecord['maximum'] - 1;
        
        for ($depth = $maxDepth; $depth > 0; $depth--) {
            // get not empty parents of item in actual depth
            if ($depth == 1) {
                $conditions = array(
                    $model . '.id' => $rootId,
                );
            } else {
                $conditions = array(
                    'LENGTH( ' . $model . '.path ) - LENGTH( REPLACE( ' . $model . '.path,  "-",  "" ) ) = ' . $depth,
                    $model . '.path LIKE "%-' . $rootId . '-%"',
                );
            }
            $parents = $Model->find(array(
                'fields' => array(
                    $model . '.id',
                    'COUNT(Model2.id) as childsCount'
                ),
                'joins' => array(
                    array(
                        'type' => 'left',
                        'table' => $Model->getPropertyTable(),
                        'alias' => 'Model2',
                        'conditions' => array(
                            'Model2.parent_id = ' . $model . '.id' 
                        )
                    )
                ),
                'conditions' => $conditions,
                'having' => array(
                    'childsCount > 0'
                ),
                'literals' => array(
                    'fields' => array(
                        'COUNT(Model2.id) as childsCount'
                    ),
                    'conditions' => $conditions
                ),
                'group' => array(
                    $model . '.id'
                )
            ));
            // sort childs in each parent
            foreach ($parents as $parent) {
                // get childs of parent
                $childs = $Model->find(array(
                    'fields' => array(
                        'id',
                        'sort'
                    ),
                    'conditions' => array(
                        'parent_id' => $parent['id']
                    ),
                    'order' => array(
                        'sort'
                    )
                ));
                // create map of sort numbers
                $sortMap = array();
                foreach ($childs as $child) {
                    $sortMap[] = $child['sort'];
                }
                // get childs of parent by name
                $childsAgain = $Model->find(array(
                    'fields' => array(
                        'id',
                    ),
                    'conditions' => array(
                        'parent_id' => $parent['id']
                    ),
                    'order' => array(
                        'name'
                    )
                ));
                $ind = 0;
                // save sort numbers
                foreach ($childsAgain as $childAgain) {
                    $Model->save(array(
                        'id' => $childAgain['id'],
                        'sort' => $sortMap[$ind]
                    ));
                    $ind++;
                }
            }
        }
    }
        
    /**
     * Copies language branch of WebContent
     * 
     * @param string $sourceLang
     * @param string $targetLang
     */
    public function copyWebContentLangBranch($sourceLang, $targetLang) {
        App::setSqlLogging(false);
        try {
            DB::startTransaction('Tool_copyWebContentLangBranch');            
            App::loadModel('App', 'WebContent');
            $Content = new WebContent();
            if (
                !($copyIds = $Content->copyTreeNode(
                    array('conditions' => array('parent_id' => null, 'lang' => $sourceLang)),
                    array('conditions' => array('parent_id' => null, 'lang' => $targetLang)),
                    array('copyData' => array('lang' => $targetLang))
                ))
            ) {
                DB::rollbackTransaction('Tool_copyWebContentLangBranch');
                $this->setErrors($Content);  
                return false;
            }
            // copy images
            App::loadModel('App', 'WebImage');
            $Image = new WebImage();
            $sourceIds = array_keys($copyIds);
            $images = $Image->findBy('run_web_contents_id', $sourceIds);
            foreach($images as $image) {
                $image['run_web_contents_id'] = $copyIds[$image['run_web_contents_id']];
                if (!$Image->copy($image)) {
                    DB::rollbackTransaction('Tool_copyWebContentLangBranch');
                    $this->setErrors($Image);
                    return false;
                }
            }
            // copy content block instances
            App::loadModel('App', 'ContentBlockInstance');
            $Instance = new ContentBlockInstance();
            $instances = $Instance->find(array(
                'conditions' => array(
                    'owner_model' => 'App.WebContent',
                    'owner_id' => $sourceIds
                )
            ));
            foreach($instances as $instance) {
                $instance['owner_id'] = $copyIds[$instance['owner_id']];
                if (!$Instance->copyInstance($instance)) {
                    DB::rollbackTransaction('Tool_copyWebContentLangBranch');
                    $this->setErrors($Instance);
                    return false;
                }
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('Tool_copyWebContentLangBranch');
            throw $e;
        }
        DB::commitTransaction('Tool_copyWebContentLangBranch');
        App::setSqlLogging();
        return $copyIds;
    }    
    
    /**
     * Copies language branch of WebContent
     * 
     * @param string $sourceLang
     * @param string $targetLang
     */
    public function deleteWebContentLangBranch($lang) {
        $Content = App::loadModel('App', 'WebContent', true);
        $contentId = $Content->findField('id', array(
            'conditions' => array(
                'path' => '-',
                'lang' => $lang,
            )
        ));
        $deleteIds = $Content->deleteTreeNode($contentId, array(
            'softDelete' => false,
        ));
        // delete related records
        // do it separately, not by join in above query, to delete also files (not only records in DB)
        if ($deleteIds) {
            // delete images
            App::loadModel('App', 'WebImage');
            $Image = new WebImage();
            $Image->delete(array(
                'conditions' => array(
                    'run_web_contents_id' => $deleteIds,
                ),
                'softDelete' => false,
            ));
            // delete content block instances
            App::loadModel('App', 'ContentBlockInstance');
            $Instance = new ContentBlockInstance();
            $Instance->delete(array(
                'processContentBlockData' => true,
                'conditions' => array(
                    'owner_model' => 'App.WebContent',
                    'owner_id' => $deleteIds,
                ),
                'softDelete' => false,
            ));
        }
        return $deleteIds;
    }    
    
    /**
     * Swaps 2 pids in web contents
     * 
     * @param array $data Containing 'pid1', 'pid2' and optionally 'lang'
     * 
     * @return boolean
     */
    public function swapWebContentPids($data) {
        if (
            !$this->validate($data, array('alternative' => 'swapWebContentPids'))
        ) {
            return false;
        }
        $conditions = array();
        if (!empty($data['lang'])) {
            $conditions['lang'] = $data['lang'];
        }
        $Content = App::loadModel('App', 'WebContent', true);
        
        $tempPid = '-temp-pid-' . uniqid();
        $conditions['pid'] = $data['pid1'];
        $Content->update(array('pid' => $tempPid), array(
            'conditions' => $conditions
        ));
        
        $conditions['pid'] = $data['pid2'];
        $Content->update(array('pid' => $data['pid1']), array(
            'conditions' => $conditions
        ));
        
        $conditions['pid'] = $tempPid;
        $Content->update(array('pid' => $data['pid2']), array(
            'conditions' => $conditions
        ));
        
        return true;
    }
    
    /**
     * !!! viď @todo 191219
     */
    public function cleanUpEshopOldProducts($step, $data = null) {
        set_time_limit(3600);
        ini_set('memory_limit', '512M');
        
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        App::loadModel('Eshop', 'EshopOrderProduct');
        $OrderProduct = new EshopOrderProduct();
        if (empty($data['created_till_date'])) {
            $data = array(
                //@todo !!! verify & update this on each project
                'created_till_date' => date('Y-m-d', strtotime(date('Y-m-d') . ' -2 years')),
//                'created_till_date' => '2017-10-28',
            );
            //debug >
            if (ON_LOCALHOST) {
                $data = array( 
                    'created_till_date' => date('Y-m-d', strtotime(date('Y-m-d') . ' -2 years')),
//                    'created_till_date' => '2017-10-28',
                );
            }
            //< debug
        }
        $vydavatelId = 3; //HARDCODED
        $conditions = array(
            'EshopProduct.created <=' => Date::format($data['created_till_date']) . ' 23:59:59',
            array(
                'EshopProduct.unavailable' => true,
                'OR',
                'EshopProduct.active' => false,
            ),
            'EshopProduct.run_eshop_manufacturers_id !=' => $vydavatelId,
        );
        
//        App::setSqlLogging(false);
        $result = array(
            'info' => array(),
            'errors' => array(),
            'interrupted' => false,
        );
        // find count of all products to be removed
        if ($step === 'info') {            
            $productsCount = $Product->findCount();
            $oldProductsCount = $Product->findCount(array(
                'conditions' => $conditions,
                'ignoreSoftDeleted' => false,
            ));
            $result['info'][] = __d(
                __FILE__, 
                'Odstránených bude %s nedostupných a/alebo neaktívnych produktov (z celkového počtu %s všetkých produktov) vytvorených do %s (vrátane)', 
                $oldProductsCount, 
                $productsCount,
                Date::format($data['created_till_date'], 'j.n.Y')
            );
            App::log('Tool_cleanUpEshopOldProducts', sprintf('%s old products from %s will be removed', $oldProductsCount, $productsCount)); //debug
            if (!$oldProductsCount) {
                $result['errors'][] = __d(
                    __FILE__, 
                    'Nenašli sa žiadne nedostupné a/alebo neaktívne produkty vytvorene do %s (vrátane). Upravte prosím tento hraničný dátum.', 
                    Date::format($data['created_till_date'], 'j.n.Y')
                );
            }    
            // check if there are other EshopProduct related Order models except of EshopOrderProduct
            $productForeignKey = $Product->getForeignKey();
            $models = App::getModels('Eshop');
            foreach ($models as $model) {
                $Model = App::loadModel('Eshop', $model, true);
                $schema = $Model->getPropertySchema();
                if (
                    $model === 'EshopOrderProduct'
                    || 
                    !isset($schema[$productForeignKey])
                ) {
                    continue;
                }
                if (strpos($model, 'Order') !== false) {
                    $result['errors'][] = __d(__FILE__, 'Záznamy modelu %s patria modelu EshopProduct a sučasne sú datami objednávky a preto sa nemôžu odstrániť. Ošetrite prípad zmazaných starých produktov aj pre tento model (vo funkcionalite mazania starých produktov ale aj v projekte)', $model);
                }
            }
            return $result;
        }
        elseif ($step === 'backUp') {
            // backup exported products (eans and codes)
            // (file cleanedUpExportedProducts.php is used in EshopProductImport::importAddition()
            // and /_debug/setExportedEans)
            $exportedProducts = $Product->findList(array(
                'conditions' => array_merge($conditions, array(
                    'EshopProduct.exported !=' => null,
                )),
                'key' => 'ean',
                'fields' => array('code'),
                'ignoreSoftDeleted' => false,
            ));
            $backupFile = 
                'app' . DS . 'modules' . DS . 'Eshop' . DS . 'config' . DS . 'cleanedUpExportedProducts.php';
            if (is_readable(ROOT . DS . $backupFile)) {
                App::loadScript($backupFile, array('catchVariables' => 'exportedProducts'), $vars);
                if (!empty($vars['exportedProducts'])) {
                    $exportedProducts = $exportedProducts + $vars['exportedProducts'];
                }
            }
            file_put_contents(
                ROOT . DS . $backupFile, 
                '<?php $exportedProducts = ' . Arr::getLiteral($exportedProducts) . '; '
            );
            $result['info'][] = __d(__FILE__, 'EANy a katalógové čísla %s starých produktov exportovanych do MRP boli zálohované v súbore %s', count($exportedProducts), $backupFile);
            App::log('Tool_cleanUpEshopOldProducts', sprintf('%s old product eans and codes exported to MRP has been backuped in file %s', count($exportedProducts), $backupFile)); //debug            
            // backup order products data
            $orderProducts = $OrderProduct->findList(array(
                'joins' => array(
                    array(
                        'type' => 'left',
                        'model' => 'EshopProduct',
                    )
                ),
                'conditions' => array_merge($conditions, array(
                    'EshopProduct.id !=' => null,
                )),
                'key' => 'EshopOrderProduct.run_eshop_products_id',
                'fields' => array('EshopOrderProduct.id'),
                'accumulate' => true,
                'ignoreSoftDeleted' => false,
            ));
            $productIds = array_keys($orderProducts);
            $productNullifiedFields = array(
                'image' => null,
                'seo_title' => null,
                'seo_description' => null,
                'seo_keywords' => null,
            );
            $productSchema = $Product->getPropertySchema();
            foreach ($productSchema as $field => $fieldOptions) {
                if (
                    !empty($fieldOptions['type'])
                    && ($fieldType = strtolower($fieldOptions['type']))
                    && (
                        $fieldType === 'text'
                        || $fieldType === 'mediumtext'
                        || $fieldType === 'longtext'
                    )
                ) {
                    $productNullifiedFields[$field] = null;
                }
            }
            $detailsBatchSize = 1000;
            $updateBatchSize = 1000;
            $orderProductsUpdateBatch = array();
            $orderProductsCount = 0;
            while($productBatchIds = array_splice($productIds, 0, $detailsBatchSize)) {
                $productsDetails = $Product->getDetails($productBatchIds, array(
                    //@todo !!! verify & update this options on each project
                    'getManufacturer' => true,
                    'getManufacturerRange' => true,
                    'getAuthors' => true,
                    'publishedOnly' => false,
                    'ignoreSoftDeleted' => false,
                ));
                foreach ($productsDetails as $productId => $productDetails) {
                    // nullify columns with long texts to save data
                    $productDetails = array_merge($productDetails, $productNullifiedFields);
                    $productDetailsJson = json_encode($productDetails);
                    $orderProductIds = $orderProducts[$productId];
                    foreach ($orderProductIds as $orderProductId) {
                        $orderProductsUpdateBatch[] = array(
                            'id' => $orderProductId,
                            'cleaned_up_product_data' => $productDetailsJson,
                        );
                        $orderProductsCount++;
                        if (count($orderProductsUpdateBatch) >= $updateBatchSize) {
                            $OrderProduct->saveBatch(array(
                                'update' => array(
                                    'EshopOrderProduct' => &$orderProductsUpdateBatch,
                                )
                            ));
                            $orderProductsUpdateBatch = array();
                        }
                    }
                }
            }
            if ($orderProductsUpdateBatch) {
                $OrderProduct->saveBatch(array(
                    'update' => array(
                        'EshopOrderProduct' => &$orderProductsUpdateBatch,
                    )
                ));
                $orderProductsUpdateBatch = array();
            }
            $result['info'][] = __d(__FILE__, 'Údaje %s starých produktov boli zálohované v %s zaznamoch produktov objednávok', count($orderProducts), $orderProductsCount);
            App::log('Tool_cleanUpEshopOldProducts', sprintf('%s old products has been backuped in %s order products', count($orderProducts), $orderProductsCount)); //debug
            return $result;
        }
        elseif ($step === 'cleanUp') {
            // delete products
            $productIds = $Product->findList(array(
                'conditions' => $conditions,
                'fields' => array('id'),
                'ignoreSoftDeleted' => false,
            ));
            $microtimeReserve = 10000;
            $deleteBatchSize = 1000;
            $oldProductsCount = count($productIds);
            $deletedProductsCount = 0;
            while($productBatchIds = array_splice($productIds, 0, $deleteBatchSize)) {
                Utility::startTimer('deleteMicrotime');
                $Product->delete(array(
                    'conditions' => array(
                        'id' => $productBatchIds
                    ),
                    'softDelete' => false,
                ));
                $deletedProductsCount += count($productBatchIds);
                App::log('Tool_cleanUpEshopOldProducts', sprintf('%s old products has been removed', count($productBatchIds))); //debug
                $deleteMicrotime = Utility::getTimer('deleteMicrotime', false);
                if ($deleteMicrotime > ($microtimeReserve - 2000)) {
                    $microtimeReserve = $deleteMicrotime + 2000;
                }
                if (App::getFreeMicrotime() <= $microtimeReserve) {
                    $result['interrupted'] = true;
                    $result['info'][] = __d(
                        __FILE__, 
                        '%s starých produktov z %s bolo odstránených (aj s naviazanými súbormi)', 
                        $deletedProductsCount, 
                        $oldProductsCount
                    );
                    $result['info'][] = __d(__FILE__, 'Proces bol prerušený. Pokračujte prosím kliknutím na doleuvedený link');
                    App::log('Tool_cleanUpEshopOldProducts', sprintf('Old products deletion has been interrupted'));
                    return $result;
                }
            }
            $result['info'][] = __d(
                __FILE__, 
                'Všetkých %s starých produktov bolo odstránených (aj s naviazanými súbormi)', 
                $deletedProductsCount
            );
            App::log('Tool_cleanUpEshopOldProducts', sprintf('All %s old products has been removed', $deletedProductsCount)); //debug
            
            // delete related Eshop models
            $productForeignKey = $Product->getForeignKey();
            $models = App::getModels('Eshop');
            foreach ($models as $model) {
                try {
                    $Model = App::loadModel('Eshop', $model, true);
                    $schema = $Model->getPropertySchema();
                    if (
                        $model === 'EshopOrderProduct'
                        || 
                        !isset($schema[$productForeignKey])
                    ) {
                        continue;
                    }
                    if (strpos($model, 'Order') !== false) {
                        $result['errors'][] = __d(__FILE__, 'Záznamy modelu %s patria modelu EshopProduct a sučasne sú datami objednávky a preto sa nemôžu odstrániť. Ošetrite prípad zmazaných starých produktov aj pre tento model (vo funkcionalite mazania starých produktov ale aj v projekte)', $model);
                        continue;
                    }
                    Utility::startTimer('deleteMicrotime');
                    $recordsCount = $Model->findCount(array(
                        'joins' => array(
                            array(                                
                                'type' => 'left',
                                'model' => 'EshopProduct',
                            )
                        ),
                        'conditions' => array(
                            'EshopProduct.id' => null,
                        ),
                        'ignoreSoftDeleted' => false,
                    ));
                    $Model->delete(array(
                        'joins' => array(
                            array(                                
                                'type' => 'left',
                                'model' => 'EshopProduct',
                            )
                        ),
                        'conditions' => array(
                            'EshopProduct.id' => null,
                        ),
                        'softDelete' => false,
                    ));
                    $result['info'][] = __d(
                        __FILE__, 
                        '%s záznamov modelu %s priradených zmazaným starým produktom bolo odstránených', 
                        $recordsCount,
                        $model
                    );
                    App::log('Tool_cleanUpEshopOldProducts', sprintf('%s related records in model %s has been removed', $recordsCount, $model)); //debug
                } 
                catch (Exception $e) {
                    $result['errors'][] = __d(
                        __FILE__, 
                        'Odstraňovanie záznamov modelu %s priradených zmazaným starým produktom zlyhalo na nasledovnej chybe: %s', 
                        $model, $e->getMessage()
                    );
                    App::log('Tool_cleanUpEshopOldProducts', sprintf('Removal of related records in model %s has failed with following error: %s', $model, $e->getMessage())); //debug
                }
                $deleteMicrotime = Utility::getTimer('deleteMicrotime', false);
                if ($deleteMicrotime > ($microtimeReserve - 2000)) {
                    $microtimeReserve = $deleteMicrotime + 2000;
                }
                if (App::getFreeMicrotime() <= $microtimeReserve) {
                    $result['interrupted'] = true;
                    $result['info'][] = __d(__FILE__, 'Proces bol prerušený. Pokračujte prosím kliknutím na doleuvedený link');
                    App::log('Tool_cleanUpEshopOldProducts', sprintf('Related models processing has been interrupted')); //debug
                    return $result;
                }
            }
            // delete related models from other modules
            App::loadModel('App', 'Comment');
            $Comment = new Comment();
            $recordsCount = $Comment->findCount(array(
                'joins' => array(
                    array(                                
                        'type' => 'left',
                        'model' => 'EshopProduct',
                        'module' => 'Eshop',
                        'conditions' => array(
                            'Comment.foreign_id = EshopProduct.id', 
                            'Comment.foreign_model' => 'Eshop.EshopProduct', 
                        ),
                    )
                ),
                'conditions' => array(
                    'EshopProduct.id' => null,
                ),
                'ignoreSoftDeleted' => false,
            ));
            $Comment->delete(array(
                'joins' => array(
                    array(                                
                        'type' => 'left',
                        'model' => 'EshopProduct',
                        'module' => 'Eshop',
                        'conditions' => array(
                            'Comment.foreign_id = EshopProduct.id', 
                            'Comment.foreign_model' => 'Eshop.EshopProduct', 
                        ),
                    )
                ),
                'conditions' => array(
                    'EshopProduct.id' => null,
                ),
                'softDelete' => false,
            ));
            $result['info'][] = __d(
                __FILE__, 
                '%s záznamov modelu App.Comment priradených zmazaným starým produktom bolo odstránených', 
                $recordsCount
            );
            App::log('Tool_cleanUpEshopOldProducts', sprintf('%s related records in model App.Comment has been removed', $recordsCount)); //debug
            return $result;
        }
    }
    
    public function deleteEshopProductOrphanFiles() {
        set_time_limit(3600);
        ini_set('memory_limit', '512M');
        
        $microtimeReserve = 5000;
        
        App::loadModel('Eshop', 'EshopProduct');
        $Product = new EshopProduct();
        $fileFields = $Product->getFileFieldOptions();
        
        $result = array(
            'info' => array(),
            'interrupted' => false,
        );
        foreach ($fileFields as $field => $fieldOptions) {
            $dbFiles = $Product->findList(array(
                'key' => $field,
                'fields' => array('id'),
                'conditions' => array(
                    $field . ' !=' => array('', null),
                )
            ));
            foreach ($dbFiles as $basename => $v) {
                // remove extension (it can differ from the extension of existing files)
                $filename = File::getPathinfo($basename, PATHINFO_FILENAME);
                unset($dbFiles[$basename]);
                $dbFiles[$filename] = true;
            }
            
            foreach ($fieldOptions['variants'] as &$variantOptions) {
                // remove extension (it can differ from the extension of existing files)
                if (!empty($variantOptions['placeholder'])) {
                    $variantOptions['placeholderFilename'] = File::getPathinfo($variantOptions['placeholder'], PATHINFO_FILENAME);
                }
            }
            unset($variantOptions);
            
            $paths = $Product->getFileFieldPaths($field);
            foreach ($paths as &$path) {
                $path = ROOT . DS . $path;
            }
            unset($path);
            $deletedFilesCount = 0;
            foreach ($paths as $path) {
                $files = array(); // empty files on each new turn to save memory
                $files = scandir($path);
                if (!$files) {
                    continue;
                }
                foreach ($files as $file) {
                    if (is_dir($path . DS . $file)) {
                        continue;
                    }
                    // remove extension (it can differ from the extension stored in DB)
                    $filename = File::getPathinfo($file, PATHINFO_FILENAME);
                    if (!isset($dbFiles[$filename])) {
                        foreach ($paths as $variant => $v) {
                            // do not remove placeholders
                            if (
                                !empty($fieldOptions['variants'][$variant]['placeholderFilename'])
                                && $filename === $fieldOptions['variants'][$variant]['placeholderFilename']
                            ) {
                                continue;
                            }
                            $filePath = ROOT . DS . $Product->getFileFieldPath($field, array(
                                'file' => $file,
                                'variant' => $variant, 
                            ));
                            if (is_readable($filePath)) {
                                unlink($filePath);
                                $deletedFilesCount++;
                            }
                        }
                    }
                    if (App::getFreeMicrotime() <= $microtimeReserve) {
                        $result['interrupted'] = true;
                        $result['info'][] = __d(__FILE__, '%s súborov bolo zmazaných pre pole EshopProduct.%s', $deletedFilesCount, $field);
                        $result['info'][] = __d(__FILE__, 'Proces bol prerušený. Pokračujte prosím opätovným spustením');
                        return $result;
                    }
                }
            }
            $result['info'][] = __d(__FILE__, '%s súborov bolo zmazaných pre pole EshopProduct.%s', $deletedFilesCount, $field);
        }
        return $result;
    }
    
    /**
     * Changes provided oldNumber to newNumber and all numbers greater that oldNumber
     * are acordingly shifted
     * 
     * @param array $data Containing 'oldNumber', 'newNumber'
     * 
     * @return boolean
     */
    public function changeEshopOrderNumbers($data) {
        if (
            !$this->validate($data, array('alternative' => 'changeEshopOrderNumbers'))
        ) {
            return false;
        }
        App::log('Tool_changeEshopOrderNumbers', 'data', array('var' => $data));
        $diff = $data['newNumber'] - $data['oldNumber'];
        App::loadModel('Eshop', 'EshopOrder');
        $Order = new EshopOrder();
        $Order->update(
            array(
                'number' => $diff > 0 ? 'number + ' . $diff : 'number - ' . abs($diff)
            ),
            array(
                'normalize' => false,
                'validate' => false,
                'conditions' => array(
                    '(0 + number) >= ' . $data['oldNumber'],
                ),
                'order' => $diff > 0 ? '(0 + number) DESC' : '(0 + number) ASC',
                'literals' => array(
                    'data' => true,
                    'conditions' => array(
                        '(0 + number) >= ' . $data['oldNumber'],
                    ),
                    'order' => array(
                        $diff > 0 ? '(0 + number) DESC' : '(0 + number) ASC',
                    ),
                )
            )
        );
        return true;
    }
    
    public function refactorContentBlockField($data) {
        if (
            !$this->validate($data, array('alternative' => 'refactorContentBlockField'))
        ) {
            return false;
        }
        $this->reserveTables('Tool_refactorContentBlockField', array(
            'App.ContentBlockInstance'
        ));
        App::loadModel('App', 'ContentBlockInstance');
        $BlockInstance = new ContentBlockInstance();
        $blockInstances = $BlockInstance->find(array(
            'conditions' => array(
                'content_block_model' => $data['contentBlockModel']
            ),
            'fields' => array(
                'id',
                'content_block_data'
            )
        ));
        $progress = array(
            'processed' => 0,
            'renamed' => 0,
            'skipped' => 0,
            'removed' => 0,
        );
        foreach ($blockInstances as &$blockInstance) {
            $instanceData = json_decode($blockInstance['content_block_data'], true);
            $progress['processed']++;
            if (array_key_exists($data['oldField'], $instanceData)) {
                if ($data['newField']) {
                    if (array_key_exists($data['newField'], $instanceData)) {
                        $progress['skipped']++;
                    }
                    else {
                        $instanceData[$data['newField']] = $instanceData[$data['oldField']];
                        $progress['renamed']++;
                    }
                }
                if ($data['removeOldField']) {
                    unset($instanceData[$data['oldField']]);
                    $progress['removed']++;
                }
            }
            else {
                $progress['skipped']++;
            }
            $blockInstance['content_block_data'] = json_encode(
                $instanceData,
                JSON_UNESCAPED_UNICODE
            );
        }
        unset($blockInstance);
        $BlockInstance->saveBatch(
            array(
                'update' => array(
                    'ContentBlockInstance' => &$blockInstances
                )
            ), 
            array(
                'reserve' => false,
            )
        );
        $this->unreserveTables('Tool_refactorContentBlockField');
        return $progress;
    }
    
    public function getEshopOrdersStatistics ($data) {
        $data = $this->normalize($data, array(
            'alternative' => 'getEshopOrdersStatistics',
        ));
        if (!$this->validate($data, array(
            'alternative' => 'getEshopOrdersStatistics',
            'normalize' => false,
        ))) {
            return false;
        }
        
        App::loadModel('Eshop', 'EshopOrder');
        $Order = new EshopOrder();
        
        $queries = array();
        $statistics = array();
        $warnings = array();
        if (!$data['taxed_prices']) {
            $warnings[] = __d(__FILE__, 'Ak používate viacúčelové zľavové kódy s absolútnou zľavou alebo iný typ viacúčelovej absolútnej zľavy potom cena predaných produktov bez DPH nie je presná. Líši sa o odčítané absolútne zľavy, ktoré sa odčítavajú len od ceny predaných produktov s DPH. Cena predaných produktov bez DPH ostava nezmenená a teda vyššia.');
        }
        foreach (array('primary', 'compare') as $dataset) {
            $conditions = array();
            $literals = array('conditions' => array());
            $priceSql = 'products_price_actual_taxless';
            $priceSqlIsLiteral = false;
            if (!empty($data['taxed_prices'])) {
                $priceSql = 'products_price_to_pay';
            }
            $prefix = $dataset === 'compare' ? 'compare_' : '';
            $startDateField = $prefix . 'start_date';
            if (!empty($data[$startDateField])) {
                $conditions['created >='] = $data[$startDateField];
            }
            elseif ($dataset === 'compare') {
                break;
            }
            $endDateField = $prefix . 'end_date';
            if (!empty($data[$endDateField])) {
                $endDate = date('Y-m-d', strtotime($data[$endDateField] . ' +1 day'));
                $conditions['created <'] = $endDate;
            }
            elseif ($dataset === 'compare') {
                break;
            }
            if (isset($data['price_from']) && $data['price_from'] !== '') {
                $conditions[$priceSql . ' >='] = $data['price_from'];
                if ($priceSqlIsLiteral) {
                    $literals['conditions'][] = $priceSql . ' >=';
                }
            }
            if (isset($data['price_to']) && $data['price_to'] !== '') {
                $conditions[$priceSql . ' <='] = $data['price_to'];
                if ($priceSqlIsLiteral) {
                    $literals['conditions'][] = $priceSql . ' <=';
                }
            }
            if (!empty($data['status'])) {
                $conditions['status'] = $data['status'];
            }
            // exclude orders
            if (!empty($data['exclude_emails'])) {
                $conditions[] = array(
                    'email !=' => $data['exclude_emails'],
                    'OR',
                    'email' => null,
                );
            }
            if (!empty($data['exclude_emails_containing'])) {
                $conditions[] = array(
                    'email !*~*' => $data['exclude_emails_containing'],
                    'OR',
                    'email' => null,
                );
            }
            if (!empty($data['exclude_comments_containing'])) {
                $conditions[] = array(
                    'comment !*~*' => $data['exclude_comments_containing'],
                    'OR',
                    'comment' => null,
                );
            }
            if (!empty($data['exclude_order_numbers'])) {
                $excludeOrderNumbers = $data['exclude_order_numbers'];
                foreach ($excludeOrderNumbers as $i => $number) {
                    if (strpos($number, '-') !== false) {
                        $numbers = explode('-', $number);
                        $conditions[] = array(
                            'NOT' => array(
                                'number + 0 >=' => (int)reset($numbers),
                                'number + 0 <=' => (int)end($numbers),
                            )
                        );
                        unset($excludeOrderNumbers[$i]);
                    }
                }
                $conditions['number !='] = $excludeOrderNumbers;
            }
            App::setSqlLogging(true);
            $orders = $Order->find(array(
                'fields' => array(
                    'products_price_actual_taxless',
                    'products_price_to_pay',
                    'created',
                ),
                'conditions' => $conditions,
                'literals' => $literals,
            ));
            $sqlLog = DB::getSqlLog();
            $query = array_pop($sqlLog);
            $queries[$dataset] = $query['query'];
            App::setSqlLogging();

            $statistics[$dataset] = array();
            // initialize dataset statistics to be sure there are all time periods
            // even in case that for some period there are no data in DB
            $datetime = $data[$startDateField] . ' 00:00:00';
            do {
                if ($data['period'] === 'hour') {
                    $time = Date::format($datetime, 'Y-m-d');
                    $time .= ' ' . str_pad((Date::format($datetime, 'G') + 1), 2, '0', STR_PAD_LEFT) . ':00';
                }
                elseif ($data['period'] === 'day') {
                    $time = Date::format($datetime, 'Y-m-d');
                }
                elseif ($data['period'] === 'week') {
                    $time = Date::format($datetime, 'o-W');
                }
                elseif ($data['period'] === 'month') {
                    $time = Date::format($datetime, 'Y-m');
                }
                elseif ($data['period'] === 'year') {
                    $time = Date::format($datetime, 'Y');
                }
                $statistics[$dataset][$time] = 0;
                // go to the next smallest period (it means we go on by hours and
                // so in some cases we rewrites manytimes here above 0 by 0)
                $datetime = date('Y-m-d H:i:s', strtotime($datetime . ' +1 hour'));
            } while(Date::format($datetime, 'Y-m-d') <= $data[$endDateField]);
            // get period values
            foreach ($orders as $order) {
                if ($data['period'] === 'hour') {
                    $time = Date::format($order['created'], 'Y-m-d');
                    $time .= ' ' . str_pad((Date::format($order['created'], 'G') + 1), 2, '0', STR_PAD_LEFT) . ':00';
                }
                elseif ($data['period'] === 'day') {
                    $time = Date::format($order['created'], 'Y-m-d');
                }
                elseif ($data['period'] === 'week') {
                    $time = Date::format($order['created'], 'o-W');
                }
                elseif ($data['period'] === 'month') {
                    $time = Date::format($order['created'], 'Y-m');
                }
                elseif ($data['period'] === 'year') {
                    $time = Date::format($order['created'], 'Y');
                }
                // this should never happen (thanks to the above initialization)
                // but you never know...
                if (!isset($statistics[$dataset][$time])) {
                    $statistics[$dataset][$time] = 0;
                }
                if ($data['variable'] === 'orders_count') {
                    $statistics[$dataset][$time]++;
                }
                elseif ($data['variable'] === 'products_price') {
                    if (!$data['taxed_prices']) {
                        $statistics[$dataset][$time] += $order['products_price_actual_taxless'];
                    }
                    else {
                        $statistics[$dataset][$time] += $order['products_price_to_pay'];
                    }
                }
            }
            ksort($statistics[$dataset]);
        }
        
        return array(
            'statistics' => $statistics,
            'queries' => $queries,
            'warnings' => $warnings,
        );
    }
}
