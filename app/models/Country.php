<?php
/**
 * The best resource is https://github.com/mledoze/countries (https://github.com/mledoze/countries/blob/master/countries.json)
 * Other good resources are:
 *      - https://countrycode.org/
 *      - https://countrycode.org/customer/countryCode/downloadCountryCodes
 * 
 * Use .../_debug/loadCountryData to update/load desired countries data
 */
class Country extends Model {
    
    protected $table = 'run_countries';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'name' => array('type' => 'varchar', 'length' => 100),
        'endonym' => array('type' => 'varchar', 'length' => 100, 'comment' => 'Internal country name / country name in native language'),
        'iso_code_2' => array('type' => 'char', 'length' => 2, 'index' => 'index', 'comment' => 'Uppercased ISO code 2'),
        'iso_code_3' => array('type' => 'char', 'length' => 3, 'comment' => 'Uppercased ISO code 3'),
        'calling_code' => array('type' => 'int', 'default' => null),
        'flag' => array('type' => 'varchar', 'default' => null),
    );
    
    protected $nameField = 'name';
    
    protected $translatedFields = array(
        'name',
    );
    
    protected $fileFields = array(
        'flag' => array(
            'quality' => 90,
            'variants' => array(
                'small' => array(
                    'fitY' => 50,
                ),
                'large' => array(
                    'fitY' => 100,
                ),
            )
        )
    );
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        
        if (!empty($data['iso_code_2'])) {
            $data['iso_code_2'] = strtoupper($data['iso_code_2']);
        }
        if (!empty($data['iso_code_3'])) {
            $data['iso_code_3'] = strtoupper($data['iso_code_3']);
        }
        
        return parent::normalize($data, $options);
    }
        
    /**
     * Returns obvious list of options for country selects containing pairs {isoCode2} => {endonym}
     * 
     * @param array $options Model::findList() options
     * 
     * @return array
     */
    public function getSelectList($options = array()) {
        $defaults = array(
            'key' => 'iso_code_2',
            'fields' => array('endonym'),
            'order' => 'endonym ASC',
        );
        $options = array_merge($defaults, $options);
        return $this->findList($options);
    }
}
