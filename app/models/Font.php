<?php
/**
 * How to use this class... @todo
 * 
 * ATTENTION: See FontVariant::$schema phpDoc
 */
class Font extends Model {
    
    protected $table = 'run_fonts';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'source' => array('type' => 'enum', 'values' => array('local', 'google'), 'default' => 'local', 'comment' => 'Font source/origin'),
        'name' => array('type' => 'varchar', 'length' => 100, 'index' => 'index', 'comment' => 'Font name used in styling selectboxes. This is just "display text", you can put here anything'),
        'family' => array('type' => 'varchar', 'length' => 100, 'index' => 'index', 'comment' => 'Font family name. It is the best if there are no whitespaces in family name. Family name is used to generate font pid. Font pid used as css family-name'),
        'category' => array('type' => 'enum', 'values' => array('serif', 'sans-serif', 'display', 'handwriting', 'monospace')),
        'diacritics' => array('type' => 'bool', 'default' => 0, 'comment' => 'If 1 then latin extended chars are available'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
    protected $tableIndexes = array(
        array(
            'type' => 'unique', 
            'fields' => array('source', 'family'), 
            'name' => 'FontSourceFamily'
        ),
    );
    
    protected $familySpecificity = 'variant';
    
    protected $conditions = array();
    
    protected $order = array('Font.name ASC', 'FontVariant.weight ASC', 'FontVariant.italic ASC');
    
    /**
     * 
     * @param array $options Define default values for following options used in class methods:
     *      - 'familySpecificity' (string) One of values are 'none', 'weight', 'variant'.
     *          The 'none' means that font family is used as defined in 'family' field, without any change.
     *          This is usefull if in your HTML editor you have a possibility (selectbox) to 
     *          change font weight (100, 200, ..., 900 - this is very rare case) and
     *          font style (normal/italic - this is quite common). The 'weight' means
     *          that font weight is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight but you can change font style. The 'variant' means
     *          that font weight and style is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400i'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight neither you can change font style. Variant specific family name
     *          equals to font virtual pid. Defaults to 'variant'.
     *      - 'conditions' (array) Find conditions to constrain returned fonts. You can use 
     *          fields of Font and FontVariant model and potencionaly qualify them
     *          by model name. Defaults to empty array(), it means all fonts are returned.
     *      - 'order' (string|array) Order of returned fonts. You can use fields of Font and 
     *          FontVariant model and potencionaly qualify them by model name. 
     *          Defaults to array('Font.family ASC', 'FontVariant.weight ASC', 'FontVariant.italic ASC').
     */
    public function __construct($options = array()) {
        $defaults = array(
            'familySpecificity' => $this->familySpecificity,
            'conditions' => $this->conditions,
            'order' => $this->order,
        );
        $options = array_merge($defaults, $options);
        $this->familySpecificity = $options['familySpecificity'];
        $this->conditions = $options['conditions'];
        $this->order = $options['order'];
        parent::__construct();
    }
    
    /**
     * Returns list of fonts like:
     * 
     *      array(
     *          '{fontPid}' => array(
     *              'source' => 'local'|'google'|...,
     *              'name' => 'OpenSans',
     *              'family' => 'Open_Sans',
     *              'category' => 'serif'|'sans-serif'|'display'|'handwriting'|'monospace',
     *              'diacritics' => 1|0,
     *              'weight' => 100|200|300|400|500|600|700|800|900,
     *              'italic' => 1|0,
     *              'remote_file' => 'http://fonts.gstatic.com/s/opensans/v13/IgZJs4-7SA1XX_edsoXWog.ttf'
     *              'local_file' => '/userfiles/FontVariant/local_file/myFont.ttf' // url path
     *          ),
     *          'Open_Sans_400' => array(
     *              ...
     *          ),
     *          ...
     *      );
     * 
     * Font weight numbers have following meanings:
     * 
     *      100 - Thin
     *      200 - Extra-Light
     *      300 - Light
     *      400 - Regular
     *      500 - Medium
     *      600 - Semi-Bold
     *      700 - Bold
     *      800 - Extra-Bold
     *      900 - Black
     * 
     * @param array $options Following are available:
     *      - 'conditions' (array) Find conditions to constrain returned fonts. You can use 
     *          fields of Font and FontVariant model and potencionaly qualify them
     *          by model name. Defaults to Font::$conditions set in constructor.
     *      - 'order' (string|array) Order of returned fonts. You can use fields of Font and 
     *          FontVariant model and potencionaly qualify them by model name. 
     *          Defaults to Font::$order set in constructor.
     * 
     * @return array The above described array
     */
    public function getList($options = array()) {
        $defaults = array(
            'conditions' => $this->conditions,
            'order' => $this->order,
        );
        $options = array_merge($defaults, $options);
        $fonts = $this->find(array(
            'joins' => array(
                array(                    
                    'type' => 'left',
                    'model' => 'FontVariant',
                ),
            ),
            'fields' => array(
                'Font.source',
                'Font.name',
                'Font.family',
                'Font.category',
                'Font.diacritics',
                'FontVariant.weight',
                'FontVariant.italic',
                'FontVariant.remote_file',
                'FontVariant.local_file',
            ),
            'conditions' => $options['conditions'],
            'order' => $options['order'],
        ));
        $this->loadModel('FontVariant');
        $Variant = new FontVariant();
        $tmp = array();
        foreach($fonts as $font) {
            $font['weight'] = (int)$font['weight'];
            $font['local_file'] = $Variant->getFileFieldUrlPath('local_file', array(
                'file' => $font['local_file']
            ));
            $pid = $this->getFontPid($font);
            $tmp[$pid] = $font;
        }
        return $tmp;
    }
    
    /**
     * Returns font for provided $pid, e.g. for 'Cormorant_Infant_500i' returns:
     * 
     *      array(
     *          'source' => 'google',
     *          'name' => 'Cormorant Infant',
     *          'family' => 'Cormorant_Infant',
     *          'category' => 'serif',
     *          'diacritics' => true,
     *          'weight' => '500',
     *          'italic' => true,
     *          'remote_file' => 'http://fonts.gstatic.com/s/cormorantinfant/v3/PK34LKusK6SSQFR2m5-LZq9x-au7fLBTFpfuT52_G64.ttf',
     *          'local_file' => null,
     *      )
     * 
     * @param string $pid Font virtual pid, e.g. e.g. 'Cormorant_Infant_500i'
     * 
     * @return array|bool The above described array of FALSE if no font has been 
     *      found for provided $pid
     */
    public function getFontByPid($pid) {
        list($family, $weight, $italic) = $this->parseFontPid($pid, true);
        $font = $this->findFirstBy('family', $family);
        if (empty($font)) {
            return false;
        }
        $this->loadModel('FontVariant');
        $Variant = new FontVariant();
        $variant = $Variant->findFirst(array(
            'conditions' => array(
                'run_fonts_id' => $font['id'],
                'weight' => $weight,
                'italic' => $italic,
            )
        ));
        if (empty($variant)) {
            return false;
        }
        return array(
            'source' => $font['source'],
            'name' => $font['name'],
            'family' => $font['family'],
            'category' => $font['category'],
            'diacritics' => $font['diacritics'],
            'weight' => (int)$variant['weight'],
            'italic' => $variant['italic'],
            'remote_file' => $variant['remote_file'],
            'local_file' => $Variant->getFileFieldUrlPath('local_file', array(
                'file' => $variant['local_file']
            )),
        );
    }    
    
    /**
     * Returns array of options which can be used for fonts selectbox like:
     *  
     *      array(
     *          '{fontFamily}' => '{fontName}'
     *          // if option 'familySpecificity' === 'none'    
     *          'Open_Sans' => 'Open Sans Regular Italic',
     *          // if option 'familySpecificity' === 'weight'    
     *          'Open_Sans_400' => 'Open Sans Regular Italic',
     *          // if option 'familySpecificity' === 'variant'    
     *          'Open_Sans_400i' => 'Open Sans Regular Italic',
     *          //...
     *      )
     * 
     * @param array $options Following are available:
     *      - 'fonts' (array) Explicit list of fonts (returned by Font::getList())
     *           to be used for creation of selectbox options. If provided then 
     *           'conditions' and 'order' options are ignored. Defaults to NULL.
     *      - 'familySpecificity' (string) One of values are 'none', 'weight', 'variant'.
     *          The 'none' means that font family is used as defined in 'family' field, without any change.
     *          This is usefull if in your HTML editor you have a possibility (selectbox) to 
     *          change font weight (100, 200, ..., 900 - this is very rare case) and
     *          font style (normal/italic - this is quite common). The 'weight' means
     *          that font weight is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight but you can change font style. The 'variant' means
     *          that font weight and style is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400i'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight neither you can change font style. Variant specific family name
     *          equals to font virtual pid. Defaults to Font::$familySpecificity set in constructor.
     *      - 'addFallback' (bool) If TRUE then a fallback font is added to css font family name,
     *          e.g. 'serif', 'sans_serif', 'monospace'. Defaults to FALSE.
     *      - 'details' (bool|int) Level of font name details. If FALSE (or any empty value)
     *          then no details are attached to font name. If 1 then weight info is
     *          attached. If 2 then also info about italic is attached. If 3 then 
     *          also info about category and diacritics is attached. E.g. for CKEditor
     *          it is necessary to have different names for each different font. Defaults to 2.
     *      - 'conditions' (array) Find conditions to constrain returned fonts. You can use 
     *          fields of Font and FontVariant model and potencionaly qualify them
     *          by model name. Ignored if explicit 'fonts' are provided. Defaults 
     *          to Font::$conditions set in constructor.
     *      - 'order' (string|array) Order of returned fonts. You can use fields of Font and 
     *          FontVariant model and potencionaly qualify them by model name. 
     *          Ignored if explicit 'fonts' are provided. Defaults to Font::$order 
     *          set in constructor.
     * 
     * @return array
     */
    public function getSelectOptions($options = array()) {
        $defaults = array(
            'fonts' => null,
            'familySpecificity' => $this->familySpecificity,
        );
        $options = array_merge($defaults, $options);
        if (is_array($options['fonts'])) {
            $fonts = $options['fonts'];
        }
        else {
            $fonts = $this->getList($options);
        }
        $fontOptions = array();
        foreach ($fonts as $font) {
            if (
                // if family specificity is 'none' then keep in the fonts selectbox 
                // list only font-weight: normal (400) and font-style: normal (not italic)
                // all other possibilities should be available by HTML editor menu    
                $options['familySpecificity'] === 'none'
                && ($font['weight'] !== 400 || $font['italic'])
                ||
                // if family specificity is 'weight' then keep in the fonts selectbox 
                // list only font-style: normal (not italic) as the change to italic
                // style should be available by HTML editor menu
                $options['familySpecificity'] === 'weight'
                && $font['italic']
            ) {
                continue;
            }
            $fontFamily = $this->getCssFontFamily($font, $options);
            $fontOptions[$fontFamily] = $this->getFontName($font, $options);
        }
        return $fontOptions;
    }
    
    /**
     * Returns virtual pid for provided $font
     * 
     * @param array $font record Font array, e.g. returned by Font::getFontByPid() 
     *          or one of items in list returned by Font::getList(). The record must
     *          contain at leat fields 'family', 'weight' and 'italic'.
     * 
     * @return string Font virtual pid, e.g. 'Open_Sans_400i'
     */
    public function getFontPid($font) {
        return $font['family'] . '_' . $font['weight'] . ($font['italic'] ? 'i' : '');
    }
    
    /**
     * Parses provided font $pid, e.g. for 'Cormorant_Infant_500i' returns:
     * 
     *      array(
     *          'family' => 'Cormorant_Infant',
     *          'weight' => '500',
     *          'italic' => true,
     *      )
     * 
     * @param string $pid Font virtual pid, e.g. e.g. 'Cormorant_Infant_500i'
     * @param bool $returnList Optional. If TRUE then return value has alist version,
     *          it means list($family, $weight, $italic) can be used to catch output.
     *          Defaults to FALSE.
     * 
     * @return array The above described array
     */
    public function parseFontPid($pid, $returnList = false) {
        $pidParts = explode('_', $pid);
        $weight = array_pop($pidParts);
        $family = implode('_', $pidParts);
        $italic = substr($weight, -1) === 'i';
        $weight = (int)$weight;
        if ($returnList) {
            return array($family, $weight, $italic);
        }
        return array(
            'family' => $family,
            'weight' => $weight,
            'italic' => $italic,
        );
    }
    
    /**
     * Returns font name for provided $font 
     * 
     * @param array|string $font Font can be specified in two ways:
     *      - record array, e.g. returned by Font::getFontByPid() or one of items 
     *          in list returned by Font::getList()
     *      - font pid, e.g. 'Open_Sans_400i'
     * @param array $options Following are available:
     *      - 'details' (bool|int) Level of font name details. If FALSE (or any empty value)
     *          then no details are attached to font name. If 1 then weight info is
     *          attached. If 2 then also info about italic is attached. If 3 then 
     *          also info about category and diacritics is attached. E.g. for CKEditor
     *          it is necessary to have different names for each different font. Defaults to 2.
     * 
     * @return string
     */
    public function getFontName($font, $options = array()) {
        $defaults = array(
            'details' => 2,
        );
        $options = array_merge($defaults, $options);
        if ($options['details'] === true) {
            $options['details'] = 3;
        }
        if (!array($font)) {
            $font = $this->getFontByPid($font);
        }
        $name = $font['name'];
        if ($options['details'] > 1) {
            $name .= ' ' . $this->getFontWeightName($font['weight'], $font['italic']);
        }
        elseif ($options['details'] > 0) {
            $name .= ' ' . $this->getFontWeightName($font['weight']);
        }
        if ($options['details'] > 2) {
            $info = '';
            switch($font['category']) {
                case 'serif':
                    $info .= ' ' . __(__FILE__, 'serif');
                    break;
                case 'sans-serif':
                    $info .= ' ' . __(__FILE__, 'sans serif');
                    break;
                case 'handwriting':
                    $info .= ' ' . __(__FILE__, 'handwriting');
                    break;
                case 'monospace':
                    $info .= ' ' . __(__FILE__, 'monospace');
                    break;
            }
            if (empty($font['diacritics'])) {
                $info .=  ', ' . __(__FILE__, 'no diacritics');
            }
            if (!empty($info)) {
                $info = trim($info, ', ');
                $name .= ' (' . $info . ')';
            }
        }        
        return $name;
    }
    
    /**
     * Returns weigth name for provided weight number.
     * Font weight numbers have following meanings:
     * 
     *      100 - Thin
     *      200 - Extra-Light
     *      300 - Light
     *      400 - Regular
     *      500 - Medium
     *      600 - Semi-Bold
     *      700 - Bold
     *      800 - Extra-Bold
     *      900 - Black
     * 
     * @param integer $weight One of weight integer numbers 100, 200, ..., 900
     *      or its string version (e.g. '100')
     * @param bool $italic Optional. If provided then info about italic version 
     *      is added to weight name. Default to FALSE.
     * 
     * @return string
     */
    protected function getFontWeightName($weight, $italic = false) {
        $name = '';
        switch((int)$weight) {
            case 100:
                $name = __(__FILE__, 'Thin');
                break;
            case 200:
                $name = __(__FILE__, 'Extra-light');
                break;
            case 300:
                $name = __(__FILE__, 'Light');
                break;
            case 400:
                $name = __(__FILE__, 'Regular');
                break;
            case 500:
                $name = __(__FILE__, 'Medium');
                break;
            case 600:
                $name = __(__FILE__, 'Semi-bold');
                break;
            case 700:
                $name = __(__FILE__, 'Bold');
                break;
            case 800:
                $name = __(__FILE__, 'Extra-bold');
                break;
            case 900:
                $name = __(__FILE__, 'Black');
                break;
        }
        if ($italic) {
            $name .=  ' ' . __(__FILE__, 'Italic');
        }
        return $name;
    }
    
    /**
     * Returns font family name for provided $font and required specificity
     * 
     * @param array $font record Font array, e.g. returned by Font::getFontByPid() 
     *          or one of items in list returned by Font::getList(). The record must
     *          contain at leat fields 'family', 'weight' and 'italic'.
     * @param array $options Following are available:
     *      - 'familySpecificity' (string) One of values are 'none', 'weight', 'variant'.
     *          The 'none' means that font family is used as defined in 'family' field, without any change.
     *          This is usefull if in your HTML editor you have a possibility (selectbox) to 
     *          change font weight (100, 200, ..., 900 - this is very rare case) and
     *          font style (normal/italic - this is quite common). The 'weight' means
     *          that font weight is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight but you can change font style. The 'variant' means
     *          that font weight and style is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400i'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight neither you can change font style. Variant specific family name
     *          equals to font virtual pid. Defaults to Font::$familySpecificity set in constructor.
     * 
     * @return string Font family name, e.g. 'Open_Sans', 'Open_Sans_400' or 'Open_Sans_400i'
     */
    public function getFontFamily($font, $options = array()) {
        $defaults = array(
            'familySpecificity' => $this->familySpecificity,
        );
        $options = array_merge($defaults, $options);
        $family = $font['family'];
        if ($options['familySpecificity'] === 'weight') {
            $family .=  '_' . $font['weight'];
        }
        elseif ($options['familySpecificity'] === 'variant') {
            $family .=  '_' . $font['weight'] . ($font['italic'] ? 'i' : '');
        }
        return $family;
    }
    
    /**
     * Returns value of css font-family rule for provided $font 
     * 
     * @param array|string $font Font can be specified in two ways:
     *      - record array, e.g. returned by Font::getFontByPid() or one of items 
     *          in list returned by Font::getList()
     *      - font pid, e.g. 'Open_Sans_400i'
     * @param array $options Following are available:
     *      - 'familySpecificity' (string) One of values are 'none', 'weight', 'variant'.
     *          The 'none' means that font family is used as defined in 'family' field, without any change.
     *          This is usefull if in your HTML editor you have a possibility (selectbox) to 
     *          change font weight (100, 200, ..., 900 - this is very rare case) and
     *          font style (normal/italic - this is quite common). The 'weight' means
     *          that font weight is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight but you can change font style. The 'variant' means
     *          that font weight and style is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400i'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight neither you can change font style. Variant specific family name
     *          equals to font virtual pid. Defaults to Font::$familySpecificity set in constructor.
     *      - 'addFallback' (bool) If TRUE then a fallback font is added to css font family name,
     *          e.g. 'serif', 'sans_serif', 'monospace'. Defaults to FALSE.
     * 
     * @return string E.g. 'Open_Sans, sans-serif'. It is not finished by semicolon (;)!
     */
    public function getCssFontFamily($font, $options = array()) {
        $defaults = array(
            'addFallback' => false,
        );
        $options = array_merge($defaults, $options);
        
        if (!is_array($font)) {
            $font = $this->getFontByPid($font);
        }
        $family = $this->getFontFamily($font, $options);
        if ($options['addFallback']) {
            $family .= ', ' . $font['category'];
        }
        return $family;
    }
    
    /**
     * Returns fontface css definition for provided $font 
     * 
     * @param array|string $font Font can be specified in two ways:
     *      - record array, e.g. returned by Font::getFontByPid() or one of items 
     *          in list returned by Font::getList()
     *      - font pid, e.g. 'Open_Sans_400i'
     * @param array $options Following are available:
     *      - 'familySpecificity' (string) One of values are 'none', 'weight', 'variant'.
     *          The 'none' means that font family is used as defined in 'family' field, without any change.
     *          This is usefull if in your HTML editor you have a possibility (selectbox) to 
     *          change font weight (100, 200, ..., 900 - this is very rare case) and
     *          font style (normal/italic - this is quite common). The 'weight' means
     *          that font weight is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight but you can change font style. The 'variant' means
     *          that font weight and style is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400i'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight neither you can change font style. Variant specific family name
     *          equals to font virtual pid. Defaults to Font::$familySpecificity set in constructor.
     * 
     * @return string Css code for fontface definition. It is pure css code without
     *      surrounding style tags (<style type="text/css"> ... </style>).
     */
    public function getCssFontface($font, $options = array()) {
        $defaults = array(
            'familySpecificity' => $this->familySpecificity,
        );
        $options = array_merge($defaults, $options);
        
        if (!is_array($font)) {
            $font = $this->getFontByPid($font);
        }
        $family = $this->getFontFamily($font, $options);
        $style = $font['italic'] ? 'italic' : 'normal';
        $weight = $font['weight'];
        $fontface = 
        '@font-face {' .
            'font-family: \'' . $family . '\';';
            if ($options['familySpecificity'] === 'none') {                
                $fontface .=    
                'font-style: ' . $style . ';' . 
                'font-weight: ' . $weight . ';';
            }
            elseif ($options['familySpecificity'] === 'weight') {
                $fontface .=    
                'font-style: ' . $style . ';';
            }
            foreach (array('local_file', 'remote_file') as $field) {
                if (!empty($font[$field])) {
                    $extension = File::getPathinfo($font[$field], PATHINFO_EXTENSION);
                    if ($extension === 'ttf') {
                        $format = 'truetype';
                    }
                    else {
                        $format = $extension;
                    }
                    // use absolute URL even for local files
                    if ($field === 'local_file') {
                        $font[$field] = App::getUrl(array(
                            'locator' => $font[$field],
                            'lang' => false,
                            'absolute' => true,
                        ));
                    }
                    // if on https then force https also on font urls, otherwise
                    // they will not get loaded
                    if (ON_HTTPS) {
                        $font[$field] = preg_replace('/^http:\/\//i', 'https://', $font[$field]);
                    }
                    $fontface .=
                    'src: url(' . $font[$field] . ') format(\'' . $format . '\');';
                    break;
                }
            }
        $fontface .=
        '}' ;
        return $fontface;
    }
    
    /**
     * Adds fontface css definition to html head for provided $font 
     * 
     * @param array|string $font Font can be specified in two ways:
     *      - record array, e.g. returned by Font::getFontByPid() or one of items 
     *          in list returned by Font::getList()
     *      - font pid, e.g. 'Open_Sans_400i'
     * @param array $options Following are available:
     *      - 'familySpecificity' (string) One of values are 'none', 'weight', 'variant'.
     *          The 'none' means that font family is used as defined in 'family' field, without any change.
     *          This is usefull if in your HTML editor you have a possibility (selectbox) to 
     *          change font weight (100, 200, ..., 900 - this is very rare case) and
     *          font style (normal/italic - this is quite common). The 'weight' means
     *          that font weight is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight but you can change font style. The 'variant' means
     *          that font weight and style is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400i'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight neither you can change font style. Variant specific family name
     *          equals to font virtual pid. Defaults to Font::$familySpecificity set in constructor.
     */
    public function addCssFontface($font, $options = array()) {
        App::setCss($this->getCssFontface($font, $options));
    }
    
    /**
     * Creates css file with provided $filemane for fonts specified by $options.
     * The file is created on path {modelModule}/css/{modelName}/{$filename}.
     * 
     * @param string $filename Name of css file, e.g. 'myFonts.css'. 
     * @param array $options Following are available:
     *      - 'fonts' (array) Explicit list of fonts (returned by Font::getList())
     *          to be used for creation of fontface css file. If provided then 
     *          'conditions' and 'order' options are ignored. Defaults to NULL.
     *      - 'force' (bool) If TRUE then the file is created/overwritten even
     *          in case that it already exists and it content has not changed.
     *          Defaults to FALSE, means file is created/overwritten only if it
     *          does not exist or if its content has changed (to keep the file timestamp).
     *      - 'familySpecificity' (string) One of values are 'none', 'weight', 'variant'.
     *          The 'none' means that font family is used as defined in 'family' field, without any change.
     *          This is usefull if in your HTML editor you have a possibility (selectbox) to 
     *          change font weight (100, 200, ..., 900 - this is very rare case) and
     *          font style (normal/italic - this is quite common). The 'weight' means
     *          that font weight is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight but you can change font style. The 'variant' means
     *          that font weight and style is added to font family name to make it more specific, e.g.
     *          'Open_Sans_400i'. This is usefull if in your HTML editor you have no possibility (selectbox)
     *          to change font weight neither you can change font style. Variant specific family name
     *          equals to font virtual pid. Defaults to Font::$familySpecificity set in constructor.
     *      - 'conditions' (array) Find conditions to constrain processed fonts. You can use 
     *          fields of Font and FontVariant model and potencionaly qualify them
     *          by model name. Ignored if explicit 'fonts' are provided. Defaults 
     *          to Font::$conditions set in constructor.
     *      - 'order' (string|array) Order of processed fonts. You can use fields of Font and 
     *          FontVariant model and potencionaly qualify them by model name. 
     *          Ignored if explicit 'fonts' are provided. Defaults to Font::$order 
     *          set in constructor.
     * 
     * @return string URL path of created css file
     * 
     * @throws Exception on failure
     */
    public function createCssFonfaceFile($filename, $options = array()) {
        $defaults = array(
            'fonts' => null,
            'force' => false,
        );
        $options = array_merge($defaults, $options);
        if (is_array($options['fonts'])) {
            $fonts = $options['fonts'];
        }
        else {
            $fonts = $this->getList($options);
        }
        App::getModulePath($this->module);
        $path = ROOT . DS . App::getModulePath($this->module) . DS . 'css' . DS . $this->name;
        if (!File::ensurePath($path, true)) {
            throw new Exception(__e(__FILE__, 'Creation of fontface file has failed: creation of directory %s has failed', $path));
        }
        $filenameInfo = File::getPathinfo($filename);
        if (empty($filenameInfo['extension'])) {
            $filenameInfo['extension'] = '.css';
        }
        $tmpFilename = uniqid($filenameInfo['filename'] . '-tmp-') . '.' . $filenameInfo['extension'];
        $filename = $filenameInfo['filename'] . '.' . $filenameInfo['extension'];
        if (!($fh = fopen($path . DS . $tmpFilename, 'w'))) {
            throw new Exception(__e(__FILE__, 'Creation of fontface file has failed: temporary file open failure'));
        }
        foreach ($fonts as $font) {
            fwrite($fh, $this->getCssFontface($font, $options));
        }
        if (!fclose($fh)) {
            throw new Exception(__e(__FILE__, 'Creation of fontface file has failed: temporary file close failure'));
        }
        if (
            !$options['force']
            && file_exists($path . DS . $filename)
            && sha1_file($path . DS . $tmpFilename) === sha1_file($path . DS . $filename)
        ) {
            // if file content has not changed then do not rewrite it (to keep the time signature of css file)
            unlink($path . DS . $tmpFilename);
        }
        elseif (!rename($path . DS . $tmpFilename, $path . DS . $filename)) {
            throw new Exception(__e(__FILE__, 'Creation of fontface file has failed: rename of %s to %s has failed', $path . DS . $tmpFilename, $path . DS . $filename));
        }
        return str_replace(DS, '/', DS . App::getModulePath($this->module) . DS . 'css' . DS . $this->name . DS . $filename);
    }
    
    /**
     * Updates fonts from google
     * 
     * @param array $options Following are available:
     *      - 'googleApiKey' (string) Google API key with allowed access to fonts.
     *          Defaults to App::getSetting('App', 'google.apiKey').
     * 
     * @throws Exception on failure
     */
    public function updateGoogleFonts($options = array()) {
        $defaults = array(
            'googleApiKey' => App::getSetting('App', 'google.apiKey'),
//            'fonts' => array(), /@todo
        );
        $options = array_merge($defaults, $options);
        // load fonts from google
        if (empty($options['googleApiKey'])) {
            throw new Exception(__e(__FILE__, 'Google fonts update failure: no API key provided'));
        }
        $url = App::getUrl(array(
            'locator' => 'https://www.googleapis.com/webfonts/v1/webfonts',
            'get' => array(
                'key' => $options['googleApiKey'],
                'sort' => 'alpha',
            )
        ));
        $response = App::request($url);
        $responseData = json_decode($response, true);
        if (
            empty($responseData)
            || !is_array($responseData)
        ) {
            throw new Exception(__e(__FILE__, 'Google fonts update failure: invalid response "%s" for request URL "%s"', $response, $url));
        }
        if (!empty($responseData['error'])) {
            throw new Exception(__e(__FILE__, 'Google fonts update failure: %s', $responseData['error']['message']));
        }
        unset($response);
        // get existing google fonts
        $existingFonts = $this->findList(array(
            'key' => 'family',
            'fields' => array('id'),
            'conditions' => array('source' => 'google')
        ));
        $existingVariants = $this->findList(array(
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'FontVariant'
                )
            ),
            'key' => 'Font.family',
            'fields' => array(
                'FontVariant.id',
                'FontVariant.weight',
                'FontVariant.italic',
            ),
            'conditions' => array('source' => 'google'),
            'accumulate' => true,
        ));
        $tmp = array();
        foreach ($existingVariants as $family => $variants) {
            foreach ($variants as $variant) {
                $pid = $this->getFontPid($variant + array('family' => $family));
                $tmp[$pid] = $variant['id'];
            }
            unset($existingVariants[$family]); // save memory
        }
        $existingVariants = $tmp;
        // parse fonts array and update existing google fonts or add new ones
        $createFonts = array();
        $updateFonts = array();
        $createVariants = array();
        $updateVariants = array();
        foreach($responseData['items'] as $i => $item) {
            if (!in_array('latin', $item['subsets'])) {
                continue;
            }
            $font = array(
                'source' => 'google',
                'name' => $item['family'],
                // avoid whitespaces in font families
                'family' => str_replace(' ', '_', $item['family']),
                'category' => $item['category'],
                'diacritics' => (int)in_array('latin-ext', $item['subsets']),
                'modified' => null,
            );
            if (($existingFont = isset($existingFonts[$font['family']]))) {
                $font['id'] = $existingFonts[$font['family']];
                $updateFonts[] = $font;
            }
            else {
                $font['created'] = null;
                $createFonts[] = $font;
            }
            $createVariants[$font['family']] = array();
            foreach($item['files'] as $weight => $remoteFile) {
                if ($weight === 'regular') {
                    $weight = '400';
                    $italic = false;
                }
                elseif ($weight === 'italic') {
                    $weight = '400';
                    $italic = true;
                }
                elseif (substr($weight, -6) === 'italic') {
                    $weight = substr($weight, 0, -6);
                    $italic = true;
                }
                else {
                    $italic = false;
                }
                $variant = array(
                    'weight' => $weight,
                    'italic' => $italic,
                    'remote_file' => $remoteFile,
                    'modified' => null,
                );
                $pid = $this->getFontPid($variant + $font);
                if (isset($existingVariants[$pid])) {
                    $variant['id'] = $existingVariants[$pid];
                    $updateVariants[] = $variant;
                }
                else {
                    $variant['created'] = null;
                    $createVariants[$font['family']][] = $variant;
                }
            }
            unset($responseData['items'][$i]); // save memory
        }
        App::setSqlLogging(false);
        try {
            $this->saveBatch(array(
                'create' => array(
                    'Font' => &$createFonts,
                ),
                'update' => array(
                    'Font' => &$updateFonts,
                    'FontVariant' => &$updateVariants,
                )
            ));
            // save memory
            unset($createFonts);
            unset($updateFonts);
            unset($updateVariants);
            unset($existingFonts);
            unset($existingVariants);
            // get font ids for new variants
            $existingFonts = $this->findList(array(
                'key' => 'family',
                'fields' => array('id'),
                'conditions' => array(
                    'source' => 'google',
                    'family' => array_keys($createVariants)
                )
            ));
            $tmp = array();
            foreach ($createVariants as $family => $variants) {
                foreach ($variants as $variant) {
                    $variant['run_fonts_id'] = $existingFonts[$family];
                    $tmp[] = $variant;
                }
                unset($createVariants[$family]); // save memory
            }
            $this->saveBatch(array(
                'create' => array(
                    'FontVariant' => &$tmp,
                ),
            ));
        }
        catch (Throwable $e) {
            throw new Exception(__e(__FILE__, 'Google fonts update failure: %s', $e->getMessage()), null, $e);
        }
        App::setSqlLogging();
    }    
    
    /**
     * Downloads remote font files to local files
     * Check Font_downloadRemoteFilesToLocalFiles.log file for error logs
     * 
     * @param array $options Following are available:
     *      - 'conditions' (array) Find conditions to constrain processed fonts. You can use 
     *          fields of Font and FontVariant model and potencionaly qualify them
     *          by model name. Defaults to Font::$conditions set in constructor.
     */
    public function downloadRemoteFilesToLocalFiles($options = array()) {
        $defaults = array(
            'conditions' => $this->conditions,
        );
        $options = array_merge($defaults, $options);
        $options['conditions'] = DB::nestConditions($options['conditions']);
        $options['conditions']['FontVariant.remote_file !='] = null;
        $options['conditions']['FontVariant.local_file'] = null;
        $variants = $this->find(array(
            'joins' => array(
                array(                    
                    'type' => 'left',
                    'model' => 'FontVariant',
                ),
            ),
            'fields' => array(
                'FontVariant.id',
                'Font.name',
                'Font.family',
                'FontVariant.weight',
                'FontVariant.italic',
                'FontVariant.remote_file',
                'FontVariant.local_file',
            ),
            'conditions' => $options['conditions'],
        ));
        $this->loadModel('FontVariant');
        $Variant = new FontVariant();    
        foreach ($variants as $variant) {
            $data = array(
                'id' => $variant['id'],
                'local_file' => array($variant['remote_file']),
                // virtual field used in FontVariant::$fileFields['local_file']['name'] anonymous function
                'font_pid' => $this->getFontPid($variant),
            );
            try {                
                $Variant->save($data, array(
                    'normalize' => false, 
                    'validate' => false,
                ));
            } 
            catch (Throwable $e) {
                App::log('Font_downloadRemoteFilesToLocalFiles', 'Font download failed with following exception', array(
                    'var' => $e
                ));
            }
        }
    }
}
