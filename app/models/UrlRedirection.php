<?php

class UrlRedirection extends Model {
    protected $table = 'run_url_redirections';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'old_locator' => array('type' => 'varchar', 'index' => 'unique', 'comment' => 'Old locator to redirect from'),
        'old_locator_lang' =>  array('type' => 'varchar', 'length' => 5, 'comment' => 'Lang of old locator to redirect from'),
        'old_locator_slug' =>  array('type' => 'varchar', 'comment' => 'Slug of old locator to redirect from'),
        'old_locator_arg_0' =>  array('type' => 'varchar', 'default' => null, 'comment' => 'The first arg (on index 0) of old locator to redirect from'),
        'new_locator' =>  array('type' => 'varchar', 'index' => 'index', 'comment' => 'New locator to redirect to'),
        'status' => array('type' => 'smallint', 'comment' => 'HTTP status code'),
        'creation_type' => array('type' => 'enum', 'values' => array('enum_automatic', 'enum_manual')),
        'run_users_id' => array('type' => 'int', 'default' => null, 'comment' => 'Creator/editor user id'),
        'user_ip' => array('type' => 'varchar', 'length' => 39, 'comment' => 'Creator/editor user IP address'),
        'applications_count' => array('type' => 'int', 'default' => 0, 'comment' => 'How many times ha been this redirection applied?'),
        'last_applications' => array('type' => 'text', 'default' => null, 'comment' => 'JSON encoded plain array of applications "{datetime} >> {crawlerName} >> {ip} >> {refererUrl} >> {userAgent}". The max length of array is constrained and overflowing items are removed.'),
        'crawled_by' => array('type' => 'text', 'default' => null, 'comment' => "JSON encoded assoc array of {crawlerName} => {ip} => array('first' => {redirectionFirstCrawlDatetime}, 'last' => {redirectionLastCrawlDatetime}. In case of human visitor crawler the assoc item looks like {humanVisitorCrawlerName} => TRUE."),
        'created' => array('type' => 'datetime'),
        'modified' => array('type' => 'datetime'),
    );

    protected $tableIndexes = array(
        array(
            'type' => 'unique',
            'fields' => array(
                'old_locator_lang',
                'old_locator_slug',
                'old_locator_arg_0',
            ),
        ),
    );
    
    /**
     * Max length of last applications history in the field last_applications
     */
    const LAST_APPLICATIONS_MAX_LENGTH = 100;

    /**
     * Human visitor - means "not creator or editor"
     */
    const CRAWLER_HUMAN_VISITOR = 'HumanVisitor';

    const CRAWLER_GOOGLEBOT = 'Googlebot';

    public function __construct() {
        parent::__construct();
        
        $this->validations = array(
            'old_locator' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                ),
                array(
                    'rule' => 'relativeUrl',
                    'message' => __v(__FILE__, 'Zadajte prosím relatívne url')
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Presmerovanie pre url už existuje'),
                ),
            ),    
            'old_locator_slug' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                ),
            ),
            'old_locator_lang' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                ),
            ),
            'new_locator' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                ),
                array(
                    'rule' => 'relativeUrl',
                    'message' => __v(__FILE__, 'Zadajte prosím relatívne url'),
                ),
                array(
                    'rule' => 'notEqualButConsistentUrls',
                ),
            ),
            'status' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                ),
                array(
                    'rule' => 'number',
                    'message' => __v(__FILE__, 'Zadajte prosím číslo'),
                ),
                array(
                    'rule' => array('gt', 0),
                    'message' => __v(__FILE__, 'Zadajte nenulovú hodnotu'),
                ),
            ),
            'creation_type' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zadajte prosím'),
                ),
                array(
                    'rule' => 'fieldAllowedValues',
                ),
            ),
        );
    }

    public function normalize($data, $options = array()) {
        $options = array_merge(array(
            'on' => null,
            'alternative' => null,
        ), $options);
        $options['alternative'] = (array)$options['alternative'];
        
        if (isset($data['old_locator'])) {
            $data['old_locator'] = trim($data['old_locator']);
            $data['old_locator'] = rtrim($data['old_locator'], '/');
            $oldLocatorParsed = App::parseUrl($data['old_locator']);
            if (empty($oldLocatorParsed['lang'])) {
                $langs = App::getPropertyLangs();
                $oldLocatorParsed['lang'] = $langs['default'];
            }
            $data['old_locator_slug'] = $oldLocatorParsed['slug'];
            $data['old_locator_lang'] = $oldLocatorParsed['lang'];
            $data['old_locator_arg_0'] = Sanitize::value($oldLocatorParsed['args'][0]);
        }

        if (isset($data['new_locator'])) {
            $data['new_locator'] = trim($data['new_locator']);
            $data['new_locator'] = rtrim($data['new_locator'], '/');
        }

        if (
            isset($data['creation_type'])
            && substr($data['creation_type'], 0, 5) !== 'enum_'
        ) {
            $data['creation_type'] = 'enum_' . $data['creation_type'];
        }
        
        return parent::normalize($data, $options);
    }

    /**
     * Adds new URL redirection
     * 
     * @param string $oldLocator Locator to redirect from
     * @param string $newLocator Locator to redirect to
     * @param array $options Following are available:
     *      - 'status' (int) Http status. Defaults to 301.
     *      - 'userId' (int) Creator user id. Defaults to actual user.
     *      - 'creationType' (string) Values of UrlRedirection.creation_type enum can be used
     *          or their shortcuts ('automatic', 'manual'). Defaults to 'automatic'
     *      - 'deletedLoopRedirection' (array&) Aux output, pass by reference. Record of deleted
     *          loop redirection if any.
     *      - 'mergedChainedRedirection' (array&) Aux output, pass by reference. Record of merged
     *          chained redirection if any.
     * 
     * @return array|null|false Following return values are possible:
     *      - Array of saved redirection record data. This may be a merged chained redirection (not the new one).
     *          Check 'mergedChainedRedirection' aux output for this additional info.
     *      - NULL if no redirection was added. For 'manual' creation type this is
     *          possible only in case of deleted loop redirection. For 'automatic'
     *          creation type this is possible also in case of equal old and new locator.
     *          Check 'deletedLoopRedirection' aux output for this additional info.
     *      - FALSE if in case of validation error for 'manual' creation type.
     * 
     * @throws Exception in case of validation error for 'automatic' creation type.
     */
    public function add($oldLocator, $newLocator, $options = array()) {
        $options = array_merge(array(
            'status' => 301,
            'userId' => App::getUser('id'),
            'creationType' => 'automatic',
        ), $options);
        // normalize creation type to 'automatic', 'manual'
        $options['creationType'] = str_replace('enum_', '', $options['creationType']);
        // initialize aux outputs
        $options['deletedLoopRedirection'] = null;
        $options['mergedChainedRedirection'] = null;

        // redirection is kept for this number of days, then it is removed
        $lifetimeDays = 365;
        // clean up all redirections older than lifetime
        $this->delete(array(
            'conditions' => array(
                'created <' => Date::getMySqlDatetime(
                    strtotime(sprintf('-%s days', $lifetimeDays))
                ),
            ),
        ));

        // populate new redirection record and normalize and validate it
        $newRedirection = array(
            'old_locator' => $oldLocator,
            'new_locator' => $newLocator,
            'status' => $options['status'],
            'creation_type' => $options['creationType'],
            'run_users_id' => $options['userId'],
            'user_ip' => Sanitize::value($_SERVER['REMOTE_ADDR']),
        );
        $newRedirection = $this->normalize($newRedirection);
        if (!$this->validate($newRedirection)) {
            // in case of manual addition treat the validation failur in common way
            if ($options['creationType'] === 'manual') {
                return false;
            }
            // in case of automatic addition treat validation failure on qual URLs
            // by quietly doing nothing
            if ($newRedirection['old_locator'] === $newRedirection['new_locator']) {
                return null;
            }
            // throw an exception for all other validation failures occured during automatic addition
            $message = __e(__FILE__, 'URL redirection addition has failed with following validation errors');
            $errors = $this->getErrors();
            App::logError($message, array(
                'var' => $errors,
                'mail' => true,
            ));
            throw new Exception($message . ': ' . json_encode($errors, JSON_UNESCAPED_UNICODE));
        }

        // before saving a new redirection record check for redirections chaining:
        //  - redirection 1: /my-locator1 -> /my-locator2
        //  - redirection 2: /my-locator2 -> /my-locator3
        if (($existingRedirection = $this->findFirstBy('new_locator', $newRedirection['old_locator']))) {
            // if we are creating loop (redirection 2: /my-locator2 -> /my-locator1)
            // then remove the redirection 1 (it is no more needed, we changed back to old locator)
            if (
                $existingRedirection['old_locator'] == $newRedirection['new_locator']
                && $existingRedirection['new_locator'] == $newRedirection['old_locator']
            ) {
                // set aux output
                $options['deletedLoopRedirection'] = $existingRedirection;
                $this->deleteBy('id', $existingRedirection['id']);
                // if redirection 1 has not been crawled yet (nobody knows about it) then do not save redirection 2
                if (!$this->isCrawledBy($existingRedirection, array(
                    self::CRAWLER_HUMAN_VISITOR,
                    self::CRAWLER_GOOGLEBOT,
                ))) {
                    return null;
                }
            }
            // if not loop and redirection 1 has not been crawled yet (nobody knows about it) 1 
            // then avoid creation of redirections chain. Merge redirection 1 and redirection 2 
            // into single one (/my-locator1 -> /my-locator3). Do it by modifying and savinng 
            // existing record of redirection 1
            elseif (!$this->isCrawledBy($existingRedirection, array(
                self::CRAWLER_HUMAN_VISITOR,
                self::CRAWLER_GOOGLEBOT,
            ))) {
                // set merged chained redirection to aux output before it is updated by the new one
                $options['mergedChainedRedirection'] = $existingRedirection;
                $existingRedirection['new_locator'] = $newRedirection['new_locator'];
                $existingRedirection['status'] = $newRedirection['status'];
                $existingRedirection['creation_type'] = $newRedirection['creation_type'];
                $existingRedirection['run_users_id'] = $newRedirection['run_users_id'];
                $existingRedirection['user_ip'] = $newRedirection['user_ip'];
                $result = $this->save($existingRedirection, array(
                    'normalize' => false,
                    'validate' => false,
                ));
                return $result;
            }
            // if not loop and redirection 1 has been already crawled (some human visitors and bots 
            // already know about the new /my-locator2, even bots already know 
            // that /my-locator1 should be crawled as /my-locator2, but who knows you know :D) 
            // then save redirection 2 (here below) and wait for deletion of redirection 1 after its lifetime expires
        }
        
        $result = $this->save($newRedirection, array(
            'normalize' => false,
            'validate' => false,
        ));

        return $result;
    }

    /**
     * Checks if provided redirection has been crawled (applied) by all specified crawlers.
     * 
     * @param array $redirection Redirection record containing at least
     *      'crawled_by' field.
     * @param string|array $crawlers Single crawler name or array of such name. Use class
     *      constants self::CRAWLER_... to provide crawler names.
     * @param array $options Following are available:
     *      - 'requireAll' (bool) Are all specified $crawlers required to do crawling or
     *          single crawler crawling is enough? Defaults to FALSE.
     * 
     * @return bool TRUE if ALL specified crawlers has checked the provided 
     *      redirection. Otherwise FALSE.
     */
    protected function isCrawledBy($redirection, $crawlers, $options = array()) {
        $options = array_merge(array(
            'requireAll' => false,
        ), $options);
        // if whole redirection record is provided
        if (
            is_array($redirection)
            && array_key_exists('crawled_by', $redirection)
        ) {
            $crawledBy = $redirection['crawled_by'];
        }
        else {
            throw new Exception(__e(__FILE__, 'Missing crawled_by field in redirection record.'));
        }
        // if JSON string is provided
        if (!is_array($crawledBy)) {
            $crawledBy = (array)json_decode($crawledBy, true);
        }
        $crawlers = (array)$crawlers;

        foreach ($crawlers as $crawler) {
            if (empty($crawledBy[$crawler])) {
                if ($options['requireAll']) {
                    return false;
                }
                continue;
            }
            // there are no ips to check for human visitor crawler
            // it is enough that there is a record
            if ($crawler === self::CRAWLER_HUMAN_VISITOR) {
                if ($options['requireAll']) {
                    continue;
                }
                return true;
            }
            // check IPs for other crawlers to very them
            $hasValidCrawlerIp = false;
            $crawlerIps = array_keys($crawledBy[$crawler]);
            foreach ($crawlerIps as $crawlerIp) {
                //$domainName = gethostbyaddr($crawlerIp);//@todo
                if (
                    $crawler === self::CRAWLER_GOOGLEBOT
                    //@todo - replace ??? by proper domain
                    //&& strpos($domainName, '???') !== false
                ) {
                    $hasValidCrawlerIp = true;
                    break;
                }
            }
            if (!$hasValidCrawlerIp) {
                if ($options['requireAll']) {
                    return false;
                }
                continue;
            }
            elseif ($options['requireAll']) {
                continue;
            }
            return true;
        }
        if ($options['requireAll']) {
            return true;
        }
        return false;
    }

    /**
     * Checks for url redirection.
     * 
     * @param string $locator actual locator
     */
    public function check($locator) {
        $parsedLocator = App::parseUrl($locator);
        if (empty($parsedLocator['lang'])) {
            $langs = App::getPropertyLangs();
            $parsedLocator['lang'] = $langs['default'];
        }
        if (empty($parsedLocator['slug'])) {
            return;
        }

        if (!($redirection = $this->findFirst(array(
            'conditions' => array(
                'old_locator_slug' => $parsedLocator['slug'],
                'old_locator_lang' => $parsedLocator['lang'],
                'old_locator_arg_0' => array(Sanitize::value($parsedLocator['args'][0]), null),
            ),
            // take first the older redirections, see this example:
            // - redirection 1 (older one): /product/map-1 -> /product/map-2
            // - redirection 2 (newer one): /product -> /book
            // - now imagine if somebody comes from URL /product/map-1
            //      - if the redirection 1 will be applied as the first the user will be redirected 
            //          to /product/map-2 which will be redirected to /book/map-2 (success)
            //      - if the redirection 2 will be applied as the first the user will be redirected 
            //          to /book/map-1 which stays unresoleved (failure - there is no redirection /book/map-1 -> /book/map-2)
            'order' => 'id ASC',
        )))) {
            return;
        }
        // if redirection then save applications data and do the redirection:
        $datetime = date('Y-m-d H:i:s');
        $ip = (string)Sanitize::value($_SERVER['REMOTE_ADDR'], '???');
        $referer = (string)Sanitize::value($_SERVER['HTTP_REFERER'], '???');
        $userAgent = (string)Sanitize::value($_SERVER['HTTP_USER_AGENT'], '???');
        // - update application data only if this is not a testing by creator or editor user
        if ($redirection['user_ip'] !== $ip) {
            // increase applications count
            $redirection['applications_count']++;
            // resove application crawler name:
            $crawlerName = null;
            // - googlebot
            if (
                strpos($userAgent, 'Googlebot') !== false
                && strpos($userAgent, '+http://www.google.com/bot.html') !== false
            ) {
                $crawlerName = self::CRAWLER_GOOGLEBOT;
            }
            // - human visitor (not creator or editor - see the above condition)
            // The list below is populated by bots listed in app/screens/_robots
            elseif (
                ($normalizedUserAgent = strtolower($userAgent))
                && strpos($normalizedUserAgent, 'bot') === false
                && strpos($normalizedUserAgent, 'crawl') === false
                && strpos($normalizedUserAgent, 'sistrix') === false
                && strpos($normalizedUserAgent, 'megaindex') === false
                && strpos($normalizedUserAgent, 'spider') === false
                && strpos($normalizedUserAgent, 'seoscanners') === false
                && strpos($normalizedUserAgent, 'linkstats') === false
                && strpos($normalizedUserAgent, 'plista') === false
                && strpos($normalizedUserAgent, 'lipperhey') === false
                && strpos($normalizedUserAgent, 'coccoc') === false
                && strpos($normalizedUserAgent, 'um-ic') === false
                && strpos($normalizedUserAgent, 'sg-orbiter') === false
                && strpos($normalizedUserAgent, 'qwantify') === false
                && strpos($normalizedUserAgent, 'thumbsniper') === false
                && strpos($normalizedUserAgent, 'r6_commentreader') === false
            ) {
                $crawlerName = self::CRAWLER_HUMAN_VISITOR;
            }
            // add crawler application to crawled_by
            if ($crawlerName) {
                $redirection['crawled_by'] = (array)json_decode($redirection['crawled_by'], true);
                if ($crawlerName === self::CRAWLER_HUMAN_VISITOR) {
                    $redirection['crawled_by'][$crawlerName] = true;
                }
                elseif (empty($redirection['crawled_by'][$crawlerName][$ip]['first'])) {
                    $redirection['crawled_by'][$crawlerName][$ip]['first'] = $datetime;
                }
                else {
                    $redirection['crawled_by'][$crawlerName][$ip]['last'] = $datetime;
                }
                $redirection['crawled_by'] = json_encode($redirection['crawled_by']);
            }
            // add application to history
            $redirection['last_applications'] = json_decode($redirection['last_applications'], true);
            if (!$redirection['last_applications']) {
                $redirection['last_applications'] = array();
            }
            array_unshift($redirection['last_applications'], sprintf('%s >> %s >> %s >> %s >> %s', 
                $datetime,
                $crawlerName ? $crawlerName : '???',
                $ip,
                $referer,
                $userAgent
            ));
            $redirection['last_applications'] = array_slice($redirection['last_applications'], 0, self::LAST_APPLICATIONS_MAX_LENGTH);
            $redirection['last_applications'] = json_encode($redirection['last_applications']);
            // save application data
            $this->save($redirection, array(
                'validate' => false,
                'normalize' => false,
            ));
        }
        // - redirect
        $redirectUrl = $redirection['new_locator'];
        if (
            isset($parsedLocator['args'][0])
            && empty($redirection['old_locator_arg_0'])
        ) {
            // if the old locator has no args then also new locator should be without args
            // BUT records ca be added also manually so be careful
            $parsedNewLocator = App::parseUrl($redirection['new_locator']);
            if (isset($parsedNewLocator['args'][0])) {
                App::logError(
                    __e(
                        __FILE__,
                        'Inconsistent redirection locators: "%s" -> "s"',
                        $redirection['old_locator'],
                        $redirection['new_locator']
                    ),
                    array(
                        'email' => true,
                    )
                );
                // do not do redirection
                return;
            }
            $parsedNewLocator['args'] = array($parsedLocator['args'][0]);
            $redirectUrl = App::getUrl($parsedNewLocator);
        }
        App::redirect($redirectUrl, array(
            'status' => $redirection['status'],
        ));
    }

    public function validate_relativeUrl($fieldValue) {
        if (substr($fieldValue, 0, 1) === '/') {
            return true;
        }
        return false;
    }

    /**
     * Validates if old_locator and new_locator are not equal but consistent:
     *  - must be different
     *  - both are relative or both are absolute
     *  - both have the same lang
     *  - both have slug
     *  - both have the same number of args
     */
    public function validate_notEqualButConsistentUrls($fieldValue, $fieldName, $data, &$validation) {
        if (
            !array_key_exists('old_locator', $data)
            && !array_key_exists('new_locator', $data)
        ) {
            return true;
        }
        if (!empty($data['id'])) {
            if (
                array_key_exists('new_locator', $data)
                && !array_key_exists('old_locator', $data)
            ) {
                $data['old_locator'] = $this->findFieldBy('old_locator', 'id', $data['id']);
            }
            elseif (
                array_key_exists('old_locator', $data)
                && !array_key_exists('new_locator', $data)
            ) {
                $data['new_locator'] = $this->findFieldBy('new_locator', 'id', $data['id']);
            }
        }
        $oldLocator = (string)Sanitize::value($data['old_locator']);
        $newLocator = (string)Sanitize::value($data['new_locator']);
        if ($oldLocator === $newLocator) {
            $validation['message'] = __v(__FILE__, 'Zadajte rôzne URL', $newLocator);
            return false;
        }
        $parsedOldLocator = App::parseUrl($oldLocator);
        $parsedNewLocator = App::parseUrl($newLocator);
        if (
            $parsedOldLocator['absolute'] !== $parsedNewLocator['absolute']
            || $parsedOldLocator['lang'] !== $parsedNewLocator['lang']
            || empty($parsedOldLocator['slug'])
            || empty($parsedNewLocator['slug'])
            || count($parsedOldLocator['args']) !== count($parsedNewLocator['args'])
        ) {
            if ($fieldName === 'old_locator') {
                $validation['message'] = __v(__FILE__, 'URL nie je konzistentné s "%s"', $newLocator);
            }
            elseif ($fieldName === 'new_locator') {
                $validation['message'] = __v(__FILE__, 'URL nie je konzistentné s "%s"', $oldLocator);
            }
            else {
                $validation['message'] = __v(__FILE__, 'URL "%s" nie je konzistentné s URL "%s"', $oldLocator, $newLocator);
            }
            return false;
        }
        return true;
    }
}
