<?php
class Reference extends Model {
    
    protected $table = 'run_references';
        
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'name' => array('type' => 'varchar'),
        'locator' => array('type' => 'varchar', 'default' => null, 'comment' => 'Reference slug or an absolute URL'),
        'text' => array('type' => 'text', 'default' => null),
        'image' => array('type' => 'varchar', 'default' => null),
        'active' => array('type' => 'bool'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
        'deleted' => array('type' => 'datetime', 'default' => null, 'index' => 'index'),
    );
    
    protected $nameField = 'name';
    
    protected $fileFields = array(
        'image' => array(
            'variants' => array(
                '' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    'fit' => array(150, 100),
                ),
                'original' => array(
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(300, 200),
                ),
            )
        ),
    );
    
    public function __construct() {
        parent::__construct();
        // set validations
        $this->validations = array(
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                ),
            ),
            'image' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Choose reference image'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Choose reference image'),
                ),
                array(
                    'rule' => array('uploadData', array('notEmpty' => true)),
                    'message' => __v(__FILE__, 'Choose reference image'),
                    'on' => 'create',
                ),
                array(
                    'rule' => array('uploadData', array('notEmpty' => false)),
                    'message' => __v(__FILE__, 'Choose reference image'),
                ),
            ),
        );
    }
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        
        if (!empty($data['locator'])) {
            if ($options['on'] == 'create') {
                $data['locator'] = $this->getUniqueSlug($data['locator']);
            }
            else {
                $data['locator'] = $this->getUniqueSlug($data['locator'], array(
                    'id' => Sanitize::value($data['id']),
                ));
            }
        }
        
        return parent::normalize($data, $options);
    }
    
    /**
     * Returns unique slug for provided locator. If the locator is absolute url 
     * then it is returned unchanged. If the locator is slug then unique 
     * variation (adding numbers at the end) is returned.
     * 
     * @param string $locator
     * @param array $options Following are available:
     *      - 'id' (int) Content id. If provided then specifies reference whose slug
     *          is excluded from comparison. This serves to get unique slugs for existing 
     *          references. Defaults to NULL.
     * 
     * @return string
     */
    public function getUniqueSlug($locator, $options = array()) {
        $defaults = array(
            'id' => null,
        );
        $options = array_merge($defaults, $options);
        // mail links and absolute URLs are returned unchanged (e.g. mailto:<EMAIL>, http://something.com, https://something.com)
        if (
            substr($locator, 0, 7) == 'mailto:' 
            || substr($locator, 0, 7) == 'http://' 
            || substr($locator, 0, 8) == 'https://'
        ) {
            return $locator;
        }
        
        // find the slug unique value
        $conditions = array();
        // - if id is specified then apply it
        if ($options['id']) {
            $conditions[$this->name . '.' . $this->primaryKey . ' !='] = $options['id'];
        }
        // slug must be at least 3 chars long to not perplex it with lang code
        $locator = Str::slugize($locator);
        $locator = str_pad($locator, 3, '-');
        $locator = $this->getUniqueFieldValue($this->name . '.locator', $locator, array(
            'conditions' => $conditions,  
        ));
                
        return $locator;
    }
}
