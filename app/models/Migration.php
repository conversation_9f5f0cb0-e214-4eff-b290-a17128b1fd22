<?php
/**
 * Use this model for methods to migrate old project DB into new one.
 * Functionality here should be updated per project basis.
 * 
 * Migrations to be done on vydavatel.sk from cakePHP to fajnwork:
 * 
 *      (done) users + eshop_profiles + groups -> run_users + run_user_profiles
 *      (done) eshop_categories -> run_eshop_product_categories
 *      (done) eshop_products_eshop_categories -> run_eshop_product_category_products
 *      (done) eshop_authors -> run_eshop_authors
 *      (done) eshop_products_eshop_authors -> run_eshop_product_authors
 *      (done) eshop_manufacturers -> run_eshop_manufacturers, added columns import_group (= group) and import_discount_rate
 *      (done) eshop_product_series -> run_eshop_manufacturer_ranges
 *      (done) cashier_payment_methods -> run_payment_methods
 *      (done) eshop_orders + eshop_carts + eshop_carts_eshop_products + eshop_statuses -> run_eshop_orders + run_eshop_order_products
 *      (done) eshop_shipping_methods -> run_eshop_shipment_methods
 *      (done) eshop_shipping_methods_cashier_payment_methods -> run_eshop_shipment_method_payment_methods
 *      (done) eshop_sales + eshop_product_groups -> run_eshop_product_groups
 *      (done) eshop_products_eshop_product_groups + eshop_products_eshop_sales -> run_eshop_product_group_products
 *      (done) eshop_product_types -> run_eshop_product_types
 *      (done) comments_comments -> run_comments (but only items with model = 'EshopProduct')
 *      (done) eshop_products + eshop_product_i18ns + eshop_product_attribute_types + eshop_product_attributes + eshop_product_attribute_i18ns + eshop_taxes + attachments -> run_eshop_products
 *      (done) attachments -> eshop_product_images
 *      (done) eshop_wishlists -> run_eshop_wishlists - NOVE, viď todo.txt > (160426A)
 *      (done) eshop_products_eshop_wishlists -> run_eshop_wishlist_products - NOVE, viď todo.txt > (160426A)
 *      (done) eshop_vouchers -> run_eshop_vouchers - NOVE, viď todo.txt > (160426B)
 *      (done) newsletter_templates -> vyriešené ako snippet na vygenerovanie zoznamu produktov
 *      (done) newsletter_recipients -> run_mailer_contacts
 *      (done) settings_settings (manually) -> run_settings, setting Vydavatel.VydavatelExport.lastUnregUserMrpAddressId set as last id in EshopMrpQuickOrderAddressUniqueId
 * 
 *      contents (manually) -> run_web_contents
 * 
 */
class Migration extends Model {
    
    /**
     * Number of records to be migrated for sake of debuging.
     * Defaults to FALSE, means all records are migrated.
     * Used as default value for 'debug' option of Migration::migrate().
     *
     * @var int|bool
     */
    static public $debug = false;
    
    /**
     * Source database name. If empty then considered to be the same as the actual 
     * project database. Populated in constructor.
     * 
     * NOTE: If the source and target DB have the same access user, password and 
     * host then you can read both with the same DB connection
     *
     * @var string
     */
    protected $sourceDatabase;
    
    public function __construct() {
        parent::__construct();
        
        set_time_limit(600);
        ini_set('memory_limit', '512M');
        
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        // ATTENTION: If some of migrations fails down with "EXCEPTION: MySQL server has gone away"
        // then very probably there is error "Got a packet bigger than 'max_allowed_packet' bytes"
        // in DB. To avoid this error set a smaller 'batchSize' option for problematic 
        // migration (look for the mentioned DB error here below)
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        
        if (ON_LOCALHOST) {
            $this->sourceDatabase = 'alterego';
        }
        else {
            $this->sourceDatabase = 'c18_alterego';
        }
    }
    
    protected function qualifyBySorceDatabase($name) {
        if (!empty($this->sourceDatabase)) {
            $name = $this->sourceDatabase . '.' . $name;
        }
        return $name;
    }
    
    protected function qualifyByTargetDatabase($name) {
        $name = DB::getPropertyDatabaseName() . '.' . $name;
        return $name;
    }
    
    /**
     * Ensures existence of an admin user with password 'runforrestrun'
     */
    protected function ensureAdminUser() {
        $User = App::loadModel('App', 'User', true);
        $UserProfile = App::loadModel('App', 'UserProfile', true);
        $adminId = -123;
        $adminData = array(
            'id' => $adminId,
            'run_groups_id' => 1,
            'username' => 'admin@migration',
            'password' => App::hash('runforrestrun'),
            'first_name' => 'Admin',
            'last_name' => 'Istrátor',
            'email' => 'test+'. uniqid() . '@run.sk',
            'active' => 1,
            'lang' => 'sk',
        );
        if($User->findFirstBy('id', $adminId)) {
            $User->save($adminData, array(
                'validate' => false,
            ));
        }
        else {            
            $User->save($adminData, array(
                'create' => true,
                'validate' => false,
            ));
        }
        if (!$UserProfile->findFirstBy('run_users_id', $adminId)) {            
            $UserProfile->save(
                array(
                    'id' => $adminId,
                    'run_users_id' => $adminId,
                ),
                array(
                    'create' => true,
                    'validate' => false,
                )
            );
        }
    }
        
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      users + eshop_profiles -> run_users + run_user_profiles
     * 
     * @param $options Following are available:
     *      - 'action' (string) 'clear' or 'migrate'. Defaults to 'migrate'
     *      - 'limit' (integer) Applies only for action 'migrate'. Defaults to NULL.
     *      - 'offset' (integer) Applies only for action 'migrate'. Defaults to NULL.
     *      - 'batchSize' (integer) Defaults to 1000.
     * 
     * @return string|array
     */
    public function migrateAppUsers($options = array()) {  
        $defaults = array(
            'action' => 'migrate',
            'limit' => null,
            'offset' => null,
            'batchSize' => 1000,
        );
        $options = array_merge($defaults, $options);
        $options['batchSize'] = (int)$options['batchSize'];
        $result = array();
        $User = App::loadModel('App', 'User', true);
        $UserProfile = App::loadModel('App', 'UserProfile', true);
        
        // clear tables
        if ($options['action'] === 'clear') {
            $User->truncateTable();
            $UserProfile->truncateTable();
            $this->ensureAdminUser();
            $result[] = 'App.User and App.UserProfile tables has been truncated';
            return $result;
        }
        
        // migrate
        $this->ensureAdminUser();
        
        $sourceUsers = DB::select($this->qualifyBySorceDatabase('users'), array(
            'alias' => 'u',
            'fields' => array(
                'u.id',
                'u.username',
                'u.password',
                'u.group_id',
                'u.created',
                'u.modified',
                'u.email',
                'u.reset_code',
                'u.reset_code_created',
                'ep.id AS ep_id',
                //'ep.user_id',
                'ep.first_name',
                'ep.last_name',
                'ep.address_street',
                'ep.address_city',
                'ep.address_zip',
                'ep.country_id',
                'ep.phone',
                'ep.fax',
                'ep.company_name',
                'ep.company_id_number',
                'ep.company_tax_number',
                'ep.company_vat_tax_number',
                'ep.bonus_base',
                //'ep.created',
                //'ep.modified',
            ),
            'joins' => array(
                array(
                    'table' => $this->qualifyBySorceDatabase('eshop_profiles'),
                    'alias' => 'ep',
                    'type' => 'left',
                    'conditions' => array(
                         'u.id = ep.user_id',
                    )
                )
            ),
            'order' => 'u.id ASC',
            'limit' => $options['limit'],
            'offset' => $options['offset'],
        ));
        
        $groupConvertion = array(
            // group.id => run_group.id
            1 => 1,
            2 => 3,
            3 => 3,
            4 => 4,
            5 => 2,
            6 => 3,
        );
        $countries = DB::select($this->qualifyBySorceDatabase('countries'), array(
            'fields' => array(
                'id', 'iso_code_2'
            )
        ));
        $countryConversion = array();
        foreach ($countries as $country) {
            $countryConversion[$country['id']] = strtoupper($country['iso_code_2']);
        }
        $count = 0;
        $users = array();
        $profiles = array();
        $newProfileId = -124; // id used to create profiles for users without profile
        App::setSqlLogging(false);
        foreach ($sourceUsers as $sourceUser) {
            $count++;
            $groupId = $groupConvertion[$sourceUser['group_id']];
            $user = array(
                'id' => $sourceUser['id'],
                'run_groups_id' => $groupId,
                'username' => $sourceUser['username'],
                'password' => $sourceUser['password'],
                'first_name' => $sourceUser['first_name'],
                'last_name' => $sourceUser['last_name'],
                'email' => $sourceUser['email'],
                'active' => true,
                'lang' => 'sk',
                'created' => $sourceUser['created'],
                'modified' => $sourceUser['modified'],
            );
            $profile = array(
                'id' => $sourceUser['ep_id'],
                'run_users_id' => $sourceUser['id'],
                'street' => $sourceUser['address_street'],
                'city' => $sourceUser['address_city'],
                'zip' => $sourceUser['address_zip'],
                'country' => '',
                'phone' => $sourceUser['phone'],
                'fax' => $sourceUser['fax'],
//                    'another_delivery_address' => $user[''],
//                    'delivery_fullname' => $user[''],
//                    'delivery_street' => $user[''],
//                    'delivery_city' => $user[''],
//                    'delivery_zip' => $user[''],
//                    'delivery_country' => $user[''],
//                    'delivery_phone' => $user[''],
//                    'delivery_email' => $user[''],
                'company_fullname' => $sourceUser['company_name'],
                'company_id_number' => $sourceUser['company_id_number'],
                'company_tax_number' => $sourceUser['company_tax_number'],
                'company_vat_payer' => !empty($sourceUser['company_vat_tax_number']),
                'company_vat_number' => $sourceUser['company_vat_tax_number'],
                'terms_and_conditions_agreement' => 1,
//                    'newsletters_agreement' => $user[''],
//                    'adulthood_declaration' => $user[''],
//                    'reference_from' => $user[''],
//                    'salutation' => $user[''],
//                    'degree' => $user[''],
                'subject_type' => 
                    !empty($sourceUser['company_name']) 
                    && !empty($sourceUser['company_id_number'])
                    && !empty($sourceUser['company_tax_number'])
                    ?
                    'enum_company'
                    :
                    'enum_person',
                'bonus_points' => $sourceUser['bonus_base'],
                'created' => $sourceUser['created'],
                'modified' => $sourceUser['modified'],
            );
            // some users do not have profiles
            if (!empty($sourceUser['ep_id'])) {
                $profile['country'] = Sanitize::value($countryConversion[$sourceUser['country_id']], 'SK');
            }
            else {
                $profile['id'] = $newProfileId--;
                $profile['country'] = 'SK';
            }       
            
            if ($options['batchSize'] > 1) {
                $users[] = $user;
                $profiles[] = $profile;
                if ($count % $options['batchSize'] === 0) {
                    $User->saveBatch(
                        array(
                            'create' => array(
                                'App.User' => &$users,
                                'App.UserProfile' => &$profiles,
                            )
                        ),
                        array(
                            'reserve' => false,
                        )
                    );
                    $users = array();
                    $profiles = array();
                }
            }
            else {
                $User->save(
                    $user,
                    array(
                        'create' => true,
                        'validate' => false,
                        'reserve' => false,
    //                    'ignore' => true,
                    )
                );
                $UserProfile->save(
                    $profile, 
                    array(
                        'create' => true,
                        'validate' => false,
                        'reserve' => false,
    //                    'ignore' => true,
                    )
                );
            }
        }
        // save the batch rest
        if (
            $options['batchSize'] > 1
            && !empty($users)
        ) {
            $User->saveBatch(
                array(
                    'create' => array(
                        'App.User' => &$users,
                        'App.UserProfile' => &$profiles,
                    )
                ),
                array(
                    'reserve' => false,
                )
            );
        }
        
       $result[] = Str::fill('%s items has been migrated to App.User and App.UserProfile', $count);
                
       return $result;
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_categories -> run_eshop_product_categories
     * 
     * @param $options Following are available:
     *      - 'action' (string) 'clear' or 'migrate'. Defaults to 'migrate'
     *      - 'limit' (integer) Applies only for action 'migrate'. Defaults to NULL.
     *      - 'offset' (integer) Applies only for action 'migrate'. Defaults to NULL.
     *      - 'batchSize' (integer) Defaults to 1000.
     * 
     * @return string|array
     */
    public function migrateEshopProductCategories($options = array()) {  
        $defaults = array(
            'action' => 'migrate',
            'limit' => null,
            'offset' => null,
            'batchSize' => 1000,
        );
        $options = array_merge($defaults, $options);
        $options['batchSize'] = (int)$options['batchSize'];
        $result = array();
        $Category = App::loadModel('Eshop', 'EshopProductCategory', true);
        
        // clear tables
        if ($options['action'] === 'clear') {
            $Category->truncateTable();
            $result[] = 'Eshop.EshopProductCategory table has been truncated';
            return $result;
        }
        
        // ensure top level root
        $sourceMaxId = DB::select($this->qualifyBySorceDatabase('eshop_categories'), array(
            'fields' => 'MAX(id) AS max_id',
            'literals' => array('fields' => true),
            'first' => true,
        ));
        $rootCategoryId = $sourceMaxId['max_id'] + 1;
        if (!$Category->findFirstBy('id', $rootCategoryId)) {
            $Category->truncateTable();
            $result[] = 'Eshop.EshopProductCategory table has been truncated';
            $Category->save(
                array(
                    'id' => $rootCategoryId,
                    'pid' => 'categories',
                    'name' => 'Kategórie',
                    'path' => '-',
                    'parent_id' => NULL,
                    'sort' => 0,
                ),
                array(
                    'create' => true,
                    'validate' => false,
                    'reserve' => false,
//                    'ignore' => true,
                )
            );
        }
        
        // migrate
        $sourceCategories = DB::select($this->qualifyBySorceDatabase('eshop_categories'), array(
            'alias' => 'ec',
            'joins' => array(
                array(
                    'table' => $this->qualifyBySorceDatabase('i18n'),
                    'alias' => 'i18n_name',
                    'type' => 'left',
                    'conditions' => array(
                        'ec.id = i18n_name.foreign_key',
                        'i18n_name.model' => 'EshopCategory',
                        'i18n_name.locale' => 'slo',
                        'i18n_name.field' => 'name',
                    )
                ),  
                array(
                    'table' => $this->qualifyBySorceDatabase('i18n'),
                    'alias' => 'i18n_meta_title',
                    'type' => 'left',
                    'conditions' => array(
                        'ec.id = i18n_meta_title.foreign_key',
                        'i18n_meta_title.model' => 'EshopCategory',
                        'i18n_meta_title.locale' => 'slo',
                        'i18n_meta_title.field' => 'meta_title',
                    )
                ),  
                array(
                    'table' => $this->qualifyBySorceDatabase('i18n'),
                    'alias' => 'i18n_meta_description',
                    'type' => 'left',
                    'conditions' => array(
                        'ec.id = i18n_meta_description.foreign_key',
                        'i18n_meta_description.model' => 'EshopCategory',
                        'i18n_meta_description.locale' => 'slo',
                        'i18n_meta_description.field' => 'meta_description',
                    )
                ),  
                array(
                    'table' => $this->qualifyBySorceDatabase('i18n'),
                    'alias' => 'i18n_meta_keyword',
                    'type' => 'left',
                    'conditions' => array(
                        'ec.id = i18n_meta_keyword.foreign_key',
                        'i18n_meta_keyword.model' => 'EshopCategory',
                        'i18n_meta_keyword.locale' => 'slo',
                        'i18n_meta_keyword.field' => 'meta_keyword',
                    )
                ),  
            ),
            'fields' => array(
                'ec.id',
                'ec.uid',
                'ec.parent_id',
                'ec.lft',
                'ec.rght',
                'ec.slug',
                'i18n_name.content AS name',
                'ec.active',
                'ec.eshop_product_type_id',
                'i18n_meta_title.content AS meta_title',
                'i18n_meta_description.content AS meta_description',
                'i18n_meta_keyword.content AS meta_keyword',
            ),
            'limit' => $options['limit'],
            'offset' => $options['offset'],
            'order' => 'ec.lft ASC',
        ));
        
        $count = 0;
        $categories = array();
        $paths = array(
            $rootCategoryId => '-',
        );
        App::setSqlLogging(false);
        foreach ($sourceCategories as $sourceCategory) {
            $count++;
            $parentId = $sourceCategory['parent_id'];
            if (empty($parentId)) {
                $parentId = $rootCategoryId;
            }
            $path = $paths[$parentId] . $parentId . '-';
            $paths[$sourceCategory['id']] = $path;
            $sort = $count;
            $category = array(
                'id' => $sourceCategory['id'],
//                'pid' => $sourceCategory[''],
                'code' => $sourceCategory['uid'],
                'slug' => $sourceCategory['slug'],
                'seo_title' => $sourceCategory['meta_title'],
                'seo_description' => $sourceCategory['meta_description'],
                'seo_keywords' => $sourceCategory['meta_keyword'],
                'name' => $sourceCategory['name'],
//                'heureka_name' => $sourceCategory[''],
//                'description' => $sourceCategory[''],
//                'show_description' => $sourceCategory[''],
                'active' => $sourceCategory['active'],
                'parent_id' => $parentId,
                'path' => $path,
//                'image' => $sourceCategory[''],
                'sort' => $sort,
//                'created' => $sourceCategory[''],
//                'modified' => $sourceCategory[''],
//                'deleted' => $sourceCategory[''],
//                'zbozicz_name' => $sourceCategory[''],
            );            
            if ($options['batchSize'] > 1) {
                $categories[] = $category;
                if ($count % $options['batchSize'] === 0) {
                    $Category->saveBatch(
                        array(
                            'create' => array(
                                'Eshop.EshopProductCategory' => &$categories,
                            )
                        ),
                        array(
                            'reserve' => false,
                        )
                    );
                    $categories = array();
                }
            }
            else {
                $Category->save(
                    $category,
                    array(
                        'create' => true,
                        'validate' => false,
                        'reserve' => false,
    //                    'ignore' => true,
                    )
                );
            }
        }
        // save the batch rest
        if (
            $options['batchSize'] > 1
            && !empty($categories)
        ) {
            $Category->saveBatch(
                array(
                    'create' => array(
                        'Eshop.EshopProductCategory' => &$categories,
                    )
                ),
                array(
                    'reserve' => false,
                )
            );
        }
        
        $result[] = Str::fill('%s items has been migrated to Eshop.EshopProductCategory', $count);
                
        return $result;
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_products_eshop_categories -> run_eshop_product_category_products
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopProductCategoryProducts($options = array()) {
        return $this->migrate(
            'eshop_products_eshop_categories',
            'Eshop.EshopProductCategoryProduct',
            array(
                'run_eshop_products_id' => 'eshop_product_id',
                'run_eshop_product_categories_id' => 'eshop_category_id',
            ),
            $options
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_authors -> run_eshop_authors
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopAuthors($options = array()) {
        return $this->migrate(
            'eshop_authors',
            'Eshop.EshopAuthor',
            array(
                'id' => 'id',
                'slug' => function($record) {
                    static $slugs = array();
                    $slug = $slugBase = Str::slugize($record['name']);
                    $i = 1;
                    while(isset($slugs[$slug])) {
                        $slug = $slugBase . '-' . $i++;
                    }
                    $slugs[$slug] = true;
                    return $slug;
                },
                'seo_title' => 'name',
                'name' => 'name',
                'description' => 'description',
                'created' => 'created',
                'modified' => 'modified',
            ),
            array_merge($options, array(
                'createTargetTable' => true,
                'sourceSelectOptions' => array(
                    'alias' => 'ea',
                    'joins' => array(
                        array(
                            'table' => 'i18n',
                            'alias' => 'i18n_description',
                            'type' => 'left',
                            'conditions' => array(
                                'ea.id = i18n_description.foreign_key',
                                'i18n_description.model' => 'EshopAuthor',
                                'i18n_description.locale' => 'slo',
                                'i18n_description.field' => 'description',
                            )
                        ),  
                    ),
                    'fields' => array(
                        'ea.id',
                        'ea.name',
                        'i18n_description.content AS description',
                        'ea.created',
                        'ea.modified',
                    )
                )
            ))
        );
    }
        
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_products_eshop_authors -> run_eshop_product_authors
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopProductAuthors($options = array()) {
        return $this->migrate(
            'eshop_products_eshop_authors',
            'Eshop.EshopProductAuthor',
            array(
                'run_eshop_products_id' => 'eshop_product_id',
                'run_eshop_authors_id' => 'eshop_author_id',
                'author_unified' => 'author_unified',
            ),
            $options
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_manufacturers -> run_eshop_manufacturers, added columns import_group (= group) and import_discount_rate
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopManufacturers($options = array()) {
        $options['updateTargetTableFields'] = true;
        return $this->migrate(
            'eshop_manufacturers',
            'Eshop.EshopManufacturer',
            array(
                'id' => 'id',
                'slug' => function($record) {
                    static $slugs = array();
                    $slug = $slugBase = Str::slugize($record['name']);
                    $i = 1;
                    while(isset($slugs[$slug])) {
                        $slug = $slugBase . '-' . $i++;
                    }
                    $slugs[$slug] = true;
                    return $slug;
                },
                'seo_title' => 'name',
                'name' => 'name',
                'description' => 'description',
                'import_group' => null,
                'import_discount_rate' => null,
                'active' => 1,
                'created' => 'created',
                'modified' => 'modified',
            ),
            array_merge($options, array(
                'createTargetTable' => true,
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_product_series -> run_eshop_manufacturer_ranges
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopManufacturerRanges($options = array()) {
        return $this->migrate(
            'eshop_product_series',
            'Eshop.EshopManufacturerRange',
            array(
                'id' => 'id',
                'run_eshop_manufacturers_id' => 'eshop_manufacturer_id',
                'slug' => function($record) {
                    static $slugs = array();
                    $slug = $slugBase = Str::slugize($record['name']);
                    $i = 1;
                    while(isset($slugs[$slug])) {
                        $slug = $slugBase . '-' . $i++;
                    }
                    $slugs[$slug] = true;
                    return $slug;
                },
                'seo_title' => 'name',
                'seo_description' => null,
                'seo_keywords' => null,
                'name' => 'name',
                'description' => 'description',
                'created' => 'created',
                'modified' => 'modified',        
            ),
            array_merge($options, array(
                'createTargetTable' => true,
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      cashier_payment_methods -> run_payment_methods
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migratePaymentMethods($options = array()) {
        return $this->migrate(
            'cashier_payment_methods',
            'Payment.PaymentMethod',
            array(
                'id' => 'id',
                'pid' => function($record) {
                    if ($record['category'] === 'csobwebpay') {
                        return 'gpwebpay';
                    }
                    return $record['category'];
                },
                'name' => 'name',
                'bank_name' => 'bank_name',
                'bank_url' => 'bank_url',
                'merchant_id' => 'merchant_id',
                'key' => 'key',
                'password' => 'password',
                'certificate' => 'certificate',
                'bank_account' => 'bank_account',
                'bank_account_prefix' => 'bank_account_prefix',
                'currency' => 'currency',
                'online' => 'online',
                'card_payment' => function($record) {
                    return (
                        $record['category'] === 'cardpaydes'
                        || $record['category'] === 'trustpay'
                        || $record['category'] === 'csobwebpay'
                        || $record['category'] === 'cardpay'
                    );
                },
                'used_for_lang' => function() {return 'sk';},
                'active' => 'active',
                'created' => 'created',
                'modified' => 'modified',        
            ),
            array_merge($options, array(
                'createTargetTable' => true,
                'sourceSelectOptions' => array(
                    'alias' => 'cpm',
                    'joins' => array(
                        array(
                            'table' => 'i18n',
                            'alias' => 'i18n_name',
                            'type' => 'left',
                            'conditions' => array(
                                'cpm.id = i18n_name.foreign_key',
                                'i18n_name.model' => 'CashierPaymentMethod',
                                'i18n_name.locale' => 'slo',
                                'i18n_name.field' => 'name',
                            )
                        ),  
                    ),
                    'fields' => array(
                        'cpm.id',
                        'i18n_name.content AS name',
                        'cpm.bank_name',
                        'cpm.insurance_provider_id',
                        'cpm.category',
                        'cpm.active',
                        'cpm.bank_account',
                        'cpm.bank_account_prefix',
                        'cpm.currency',
                        'cpm.created',
                        'cpm.modified',
                        'cpm.online',
                        'cpm.merchant_id',
                        'cpm.key',
                        'cpm.password',
                        'cpm.certificate',
                        'cpm.bank_url',
                    )
                )
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_shipping_methods -> run_eshop_shipment_methods
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopShipmentMethods($options = array()) {
        return $this->migrate(
            'eshop_shipping_methods',
            'Eshop.EshopShipmentMethod',
            array(
                'id' => 'id',
                'pid' => function ($record) {
                    if ($record['id'] == 1) {
                        return 'localPickup';
                    }
                    if (
                        $record['pid'] === 'zasielkovnaSk'
                        || $record['pid'] === 'zasielkovnaCz'
                    ) {
                        return 'zasielkovna';
                    }
                    if (
                        $record['pid'] === 'zasielkovnaSkCod'
                        || $record['pid'] === 'zasielkovnaCzCod'
                    ) {
                        return 'zasielkovnaCod';
                    }
                    return null;
                },
                'name' => 'name',
                'description' => function ($record){
                    return preg_replace('/\<br\s*\/?\>/i', "\n", str_replace("\n", '', $record['description']));
                },
                'price' => 'price',
                'delivery_time' => 'transport_time',
                'products_total_price_alternatives' => 'price_alternative',
                'package_weight_price_alternatives' => 'weight_price_alternative',
                'free_shipment_allowed' => 'free_shipping_allowed',
                'pickup' => function ($record) {
                    if (
                        $record['id'] == 1
                        || !empty($record['pickup_places'])
                    ) {
                        return 1;
                    }
                    return 0;
                },
                'pickup_places' => function ($record) {
                    return empty($record['pickup_places']) ? null : $record['pickup_places'];
                },
                'sort' => 'weight',
                'used_for_lang' => function() {return 'sk';},
                'delivery_country' => function($record) {
                    if (
                        $record['pid'] === 'zasielkovnaCz'
                        || $record['pid'] === 'zasielkovnaCzCod'
                    ) {
                        return 'CZ';
                    }
                    return null;
                },
                'list_color' => 'list_color',
                'active' => 1,
                'created' => 'created',
                'modified' => 'modified',        
            ),
            array_merge($options, array(
                'createTargetTable' => true,
                'sourceSelectOptions' => array(
                    'alias' => 'esm',
                    'joins' => array(
                        array(
                            'table' => 'i18n',
                            'alias' => 'i18n_name',
                            'type' => 'left',
                            'conditions' => array(
                                'esm.id = i18n_name.foreign_key',
                                'i18n_name.model' => 'EshopShippingMethod',
                                'i18n_name.locale' => 'slo',
                                'i18n_name.field' => 'name',
                            )
                        ),  
                    ),
                    'fields' => array(
                        'esm.id',
                        'esm.pid',
                        'i18n_name.content AS name',
                        'esm.description',
                        'esm.price',
                        'esm.price_alternative',
                        'esm.weight_price_alternative',
                        'esm.weight',
                        'esm.free_shipping_allowed',
                        'esm.pickup_places',
                        'esm.transport_time',
                        'esm.list_color',
                        'esm.created',
                        'esm.modified',
                    )
                )
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_shipping_methods_cashier_payment_methods -> run_eshop_shipment_method_payment_methods
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopShipmentMethodPaymentMethods($options = array()) {
        return $this->migrate(
            'eshop_shipping_methods_cashier_payment_methods',
            'Eshop.EshopShipmentPaymentMethod',
            array(
                'id' => 'id',
                'run_eshop_shipment_methods_id' => 'eshop_shipping_method_id',
                'run_payment_methods_id' => 'cashier_payment_method_id',
                'sort' => function($record) {
                    static $i = array();
                    if (!isset($i[$record['eshop_shipping_method_id']])) {
                        $i[$record['eshop_shipping_method_id']] = 0;
                    }
                    return $i[$record['eshop_shipping_method_id']]++;
                },
            ),
            array_merge($options, array(
                'createTargetTable' => true,
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_sales + eshop_product_groups -> run_eshop_product_groups
     *      eshop_products_eshop_product_groups + eshop_products_eshop_sales -> run_eshop_product_group_products
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopProductGroups($options = array()) {
        $slugs = array();
        $newGroupId = 1;
        $groupsOldToNewIds = array();
        $salesOldToNewIds = array();
        $groupsResult = $this->migrate(
            'eshop_product_groups',
            'Eshop.EshopProductGroup',
            array(
                'id' => function($record) use (&$newGroupId, &$groupsOldToNewIds) {
                    $id = $newGroupId++;
                    $groupsOldToNewIds[$record['id']] = $id;
                    return $id;
                },
                'slug' => function($record) use ($slugs) {
                    $slug = $slugBase = Str::slugize($record['name']);
                    $i = 1;
                    while(isset($slugs[$slug])) {
                        $slug = $slugBase . '-' . $i++;
                    }
                    $slugs[$slug] = true;
                    return $slug;
                },
                'seo_title' => 'name',
                'name' => 'name',
                'description' => 'description',
                'type' => function($record) {
                    if ($record['sale']) {
                        return 'sale';
                    }
                    return  null;
                },
                'active' => 1,
                'created' => 'created',
                'modified' => 'modified',     
            ),
            array_merge($options, array(
                'createTargetTable' => true,
            ))
        );
        $salesResult = $this->migrate(
            'eshop_sales',
            'Eshop.EshopProductGroup',
            array(
                'id' => function($record) use (&$newGroupId, &$salesOldToNewIds) {
                    $id = $newGroupId++;
                    $salesOldToNewIds[$record['id']] = $id;
                    return $id;
                },
                'slug' => function($record) use ($slugs) {
                    $slug = $slugBase = Str::slugize($record['name']);
                    $i = 1;
                    while(isset($slugs[$slug])) {
                        $slug = $slugBase . '-' . $i++;
                    }
                    $slugs[$slug] = true;
                    return $slug;
                },
                'seo_title' => 'name',
                'name' => 'name',
                'description' => 'description',
                'type' => function() {return 'sale';},
                'active_from' => 'start',
                'active_to' => 'end',
                'active' => 1,
                'created' => 'created',
                'modified' => 'modified',     
            ),
            $options
        );
        $groupProductsResult = $this->migrate(
            'eshop_products_eshop_product_groups',
            'Eshop.EshopProductGroupProduct',
            array(
                'run_eshop_product_groups_id' => function ($record) use ($groupsOldToNewIds) {
                    if (!empty($groupsOldToNewIds[$record['eshop_product_group_id']])) {
                        return $groupsOldToNewIds[$record['eshop_product_group_id']];
                    }
                    return null;
                },
                'run_eshop_products_id' => 'eshop_product_id',
                //'sort' => 'weight', // EshopProductGroupProduct.sort is not really used
            ),
            array_merge($options, array(
                'createTargetTable' => true,
            ))
        );
        $saleProductsResult = $this->migrate(
            'eshop_products_eshop_sales',
            'Eshop.EshopProductGroupProduct',
            array(
                'run_eshop_product_groups_id' => function ($record) use ($salesOldToNewIds) {
                    if (!empty($salesOldToNewIds[$record['eshop_sale_id']])) {
                        return $salesOldToNewIds[$record['eshop_sale_id']];
                    }
                    return null;
                },
                'run_eshop_products_id' => 'eshop_product_id',
                /*/ // EshopProductGroupProduct.sort is not really used
                'sort' => function($record) {
                    static $i = array();
                    if (!isset($i[$record['eshop_sale_id']])) {
                        $i[$record['eshop_sale_id']] = 0;
                    }
                    return $i[$record['eshop_sale_id']]++;
                },
                /*/
            ),
            $options
        );
        return array_merge($groupsResult, $salesResult, $groupProductsResult, $saleProductsResult);
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_orders + eshop_carts -> run_eshop_orders
     * 
     * @param $options See Migration::migrate() options
     * 
     * 
eshop_order:
    id
    uid
    price
    tax
bonus
in_bonus_base
    eshop_cart_id
    eshop_status_id
    eshop_voucher_code
paid_amount
    eshop_shipping_method_id
    shipping_price
gift_package_price
    cashier_payment_method_id
    user_id
    ship_fullname
    ship_line_1
    ship_line_2
    ship_city
    ship_state
    country_id
    ship_zip
    ship_phone
    ship_email
    pickup_place
    pickup_place_id
    client_message
provide_email_and_phone_to_slovak_post_agreement
    suhlas_so_zaslanim_reklamnych_materialov
    business_conditions_agreement
    created
    modified
    exported
     * 
     * @return string|array
     */
    public function migrateEshopOrders($options = array()) {
        // prepare aux variables
        $statusConversion = array(
            // Pending  (Nezaplatený) - 497
            1 => array('status' => 'enum_new_order', 'payment_status' => 'enum_payment_none', 'note' => 'Nezaplatený'), 
            // Paid (Zaplatený) - 97 ???
            2 => array('status' => 'enum_new_order', 'payment_status' => 'enum_payment_paid', 'note' => 'Zaplatený'), 
            // Shipped (Vyexpedovaný) - 70 - používali to pre dobierky (t.j. expedované ale ešte nezaplatené)
            3 => array('status' => 'enum_shipped_order', 'payment_status' => 'enum_payment_none', 'note' => 'Vyexpedovaný'), 
            // Cancelled (Zrušený) - 1577
            4 => array('status' => 'enum_canceled_order', 'payment_status' => 'enum_payment_none', 'note' => 'Zrušený'), 
            // Open (Otvorený) - 2 ???
            6 => array('status' => 'enum_opened_order', 'payment_status' => 'enum_payment_none', 'note' => 'Otvorený'), 
            // Closed (Closed) - 27836
            9 => array('status' => 'enum_closed_order', 'payment_status' => 'enum_payment_paid', 'note' => 'Closed'), 
            // Tout (Platba neprebehla!) - 2
            11 => array('status' => 'enum_opened_order', 'payment_status' => 'enum_payment_tout', 'note' => 'Platba neprebehla!'), 
            // Failed (Zlyhala platba) - 31
            12 => array('status' => 'enum_opened_order', 'payment_status' => 'enum_payment_failed', 'note' => 'Zlyhala platba'), 
            // On hold (Zadržaný) - 35 ???
            13 => array('status' => 'enum_suspended_order', 'payment_status' => 'enum_payment_none', 'note' => 'Zadržaný'), 
            // Unconfirmed epayment (Nepotvrdená e-platba) - 2
            14 => array('status' => 'enum_suspended_order', 'payment_status' => 'enum_payment_none', 'note' => 'Nepotvrdená e-platba'), 
            // Partially paid (Čiastočne zaplatený) - 7
            15 => array('status' => 'enum_suspended_order', 'payment_status' => 'enum_payment_partially_paid', 'note' => 'Čiastočne zaplatený'), 
        );
        $taxRate = App::getSetting('Eshop', 'defaultTaxRate');
        // - shipping methods
        $shippingMethods = DB::select($this->qualifyBySorceDatabase('eshop_shipping_methods'), array(
            'alias' => 'esm',
            'joins' => array(
                array(
                    'table' => $this->qualifyBySorceDatabase('i18n'),
                    'alias' => 'i18n_name',
                    'type' => 'left',
                    'conditions' => array(
                        'esm.id = i18n_name.foreign_key',
                        'i18n_name.model' => 'EshopShippingMethod',
                        'i18n_name.locale' => 'slo',
                        'i18n_name.field' => 'name',
                    )
                ),  
            ),
            'fields' => array('esm.id', 'i18n_name.content AS name')
        ));
        $tmp = array();
        foreach ($shippingMethods as $method) {
            $tmp[$method['id']] = $method['name'];
        }
        $shippingMethods = $tmp;
        // - payment methods
        $paymentMethods = DB::select($this->qualifyBySorceDatabase('cashier_payment_methods'), array(
            'alias' => 'cpm',
            'joins' => array(
                array(
                    'table' => $this->qualifyBySorceDatabase('i18n'),
                    'alias' => 'i18n_name',
                    'type' => 'left',
                    'conditions' => array(
                        'cpm.id = i18n_name.foreign_key',
                        'i18n_name.model' => 'CashierPaymentMethod',
                        'i18n_name.locale' => 'slo',
                        'i18n_name.field' => 'name',
                    )
                ),  
            ),
            'fields' => array('cpm.id', 'i18n_name.content AS name')
        ));
        $tmp = array();
        foreach ($paymentMethods as $method) {
            $tmp[$method['id']] = $method['name'];
        }
        $paymentMethods = $tmp;
        // - contries
        // -- repair countries record for slovakia in source table
        DB::update($this->qualifyBySorceDatabase('countries'), array('iso_code_2' => 'SK'), array(
            'conditions' => array('iso_code_3' => 'SLO'),
        ));
        $countries = DB::select($this->qualifyBySorceDatabase('countries'), array(
            'fields' => array(
                'id', 'iso_code_2'
            )
        ));
        $countryConversion = array();
        foreach ($countries as $country) {
            $countryConversion[$country['id']] = strtoupper($country['iso_code_2']);
        }
        // - comments
        $comments = DB::select($this->qualifyBySorceDatabase('comments_comments'), array(
            'conditions' => array(
                'model' => 'EshopOrder',
            ),
            'fields' => array(
                'foreign_key',
                'body',
                'author',
                'created',
            ),
            'order' => 'id ASC'
        ));
        $tmp = array();
        foreach ($comments as $comment) {
            if (empty($tmp[$comment['foreign_key']])) {
                $tmp[$comment['foreign_key']] = array();
            }
            $tmp[$comment['foreign_key']][] = $comment;
        }
        $comments = $tmp;
        // - bonuses
        App::loadModel('App', 'UserProfile');
        $UserProfile = new UserProfile();
        // migrate
        return $this->migrate(
            'eshop_orders',
            'Eshop.EshopOrder',
            array(
                'id' => 'id',
                'number' => 'uid',
                'token' => function() {
                    return Str::getRandom(40);
                },
                //'cart_id' => null,
                'run_users_id' => 'user_id',
                //'specific' => 0,
                'status' => function($record) use ($statusConversion) {
                    return Sanitize::value($statusConversion[$record['eshop_status_id']]['status'], 'enum_new_order');
                },
                'payment_status' => function($record) use ($statusConversion) {
                    return Sanitize::value($statusConversion[$record['eshop_status_id']]['payment_status'], 'enum_payment_none');
                },
                //'payment_signed' => null,
                //'payment_result_text' => null, 
                //'run_payment_request_logs_id' => null, 
                //'advance_rate' => null,
                     
                // price fields are ordered so that existing part of targetRecord
                // can be used in later price fields
                'bonus_discount'=> 'bonus',
                'in_bonus_points'=> 'in_bonus_base',
                        
                // this is not exact, discount is ignored here (just bonus is applied)
                // it can be updated even later according to order produtcs table
                'products_price_taxless' => 'price', 
                'products_tax' => 'tax', 
                'products_price_actual_taxless' => 'price',
                'products_tax_actual' => 'tax',
                        
                'shipment_price_taxless' => function($record) use ($taxRate) {
                    return Number::getTaxlessPrice($record['shipping_price'], $taxRate, 2);
                },
                'shipment_tax' => function($record) use ($taxRate) {
                    return round($record['shipping_price'] - Number::getTaxlessPrice($record['shipping_price'], $taxRate, 2), 2);
                },
                'shipment_price_actual_taxless' => function($record) use ($taxRate) {
                    return Number::getTaxlessPrice($record['shipping_price'], $taxRate, 2);
                },
                'shipment_tax_actual' => function($record) use ($taxRate) {
                    return round($record['shipping_price'] - Number::getTaxlessPrice($record['shipping_price'], $taxRate, 2), 2);
                },

                'payment_price_taxless' => 0,
                'payment_tax' => 0,
                'payment_price_actual_taxless' => 0,
                'payment_tax_actual' => 0,
                        
                // this is not exact, discount is ignored here (just bonus is applied)
                'order_price_taxless' => function($record, $messages, $targetRecord) {
                    return $targetRecord['products_price_taxless'] + $targetRecord['shipment_price_taxless'];
                }, 
                'order_tax' => function($record, $messages, $targetRecord) {
                    return $targetRecord['products_tax'] + $targetRecord['shipment_tax'];
                },
                'order_price_actual_taxless' => function($record, $messages, $targetRecord) {
                    return $targetRecord['products_price_actual_taxless'] + $targetRecord['shipment_price_actual_taxless'];
                },
                'order_tax_actual' => function($record, $messages, $targetRecord) {
                    return $targetRecord['products_tax_actual'] + $targetRecord['shipment_tax_actual'];
                },                        
                        
                'run_eshop_shipment_methods_id' => 'eshop_shipping_method_id',
                'shipment_method_name' => function ($record) use ($shippingMethods) {
                    return Sanitize::value($shippingMethods[$record['eshop_shipping_method_id']]);
                },
                'run_payment_methods_id' => 'cashier_payment_method_id',
                'payment_method_name' => function ($record) use ($paymentMethods) {
                    return Sanitize::value($paymentMethods[$record['cashier_payment_method_id']]);
                },

                'fullname' => 'ship_fullname',
                'street' => 'ship_line_1',
                'city' => 'ship_city',
                'zip' => 'ship_zip',
                'country' => function($record) use ($countryConversion) {
                    return Sanitize::value($countryConversion[$record['country_id']]);
                },
                'phone' => 'ship_phone',
                'email' => 'ship_email',
                //'company_fullname' => null,
                //'company_id_number' => null,
                //'company_tax_number' => null,
                //'company_vat_number' => null,

                'delivery_fullname' => 'ship_fullname',
                'delivery_street' => 'ship_line_1',
                'delivery_city' => 'ship_city',
                'delivery_zip' => 'ship_zip',
                'delivery_country' => function($record) use ($countryConversion) {
                    return Sanitize::value($countryConversion[$record['country_id']]);
                },
                'delivery_phone' => 'ship_phone',
                'delivery_email' => 'ship_email',

                'comment' => 'client_message',
                'notes' => function ($record) use ($comments, $statusConversion) {
                    $string = '';
                    if (!empty($comments[$record['id']])) {
                        foreach ($comments[$record['id']] as $comment) {
                            $string .= Date::format($comment['created'], 'd.m.Y H:i') . ', ' . $comment['author'] . ': ' . $comment['body'] . PHP_EOL;
                        }
                    }
                    if (!empty($statusConversion[$record['eshop_status_id']]['note'])) {
                        $string .= __(__FILE__, 'Original status on conversion') . ': ' . $statusConversion[$record['eshop_status_id']]['note'] . PHP_EOL;
                    }
                    if (empty($string)) {
                        return null;
                    }
                    return $string;
                },
                //'invoice_pdf' => null,
                'terms_and_conditions_agreement' => 'business_conditions_agreement',
                //'prolonged_delivery_time_agreement' => 0,
                'newsletters_agreement' => 'suhlas_so_zaslanim_reklamnych_materialov',
                //'adulthood_declaration' => 0,
                //'reference_from' => null,
                'eshop_voucher_code' => 'eshop_voucher_code', 
                'new_order_email_success' => 1,
                'successful_payment_email_success' => 1,
                //'lang' => null,
                'pickup_place' => 'pickup_place',
                'pickup_place_id' => 'pickup_place_id',
                'created' => 'created',
                'modified' => 'modified',
                'exported' => 'exported',
            ),
            array_merge($options, array(
                'batchSize' => 5000, // to avoid mysql error "Got a packet bigger than 'max_allowed_packet' bytes"
                'createTargetTable' => true,
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_carts_eshop_products -> run_eshop_order_products
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopOrderProducts($options = array()) {
        // - orders
        $orders = DB::select($this->qualifyBySorceDatabase('eshop_orders'), array(
            'fields' => array(
                'id',
                'eshop_cart_id',
            ),
            'resource' => true,
        ));
        $cartIdToOrderId = array();
        while ($order = DB::fetchArray($orders)) {
            $cartIdToOrderId[$order['eshop_cart_id']] = $order['id'];
        }
        unset($orders);
        return $this->migrate(
            'eshop_carts_eshop_products',
            'Eshop.EshopOrderProduct',
            array(
                'id' => 'id',
                'number' => function($record) {
                    static $i = array();
                    if (!isset($i[$record['eshop_cart_id']])) {
                        $i[$record['eshop_cart_id']] = 0;
                    }
                    return ++$i[$record['eshop_cart_id']];
                },
                'run_eshop_orders_id' => function($record) use ($cartIdToOrderId) {
                    return $cartIdToOrderId[$record['eshop_cart_id']];
                },
                'run_eshop_products_id' => 'eshop_product_id',
                'amount' => 'amount',
                //'stock' => 0,
                //'reserved_amount' => '',
                //'availability' => '',
                'price_taxless' => function($record) {
                    if (empty($record['savings_rate'])) {
                        return $record['price'];
                    }
                    return round($record['price'] * 100 / (100 - (float)$record['savings_rate']), 2);
                },
                'tax' => function ($record) {
                    if (empty($record['savings_rate'])) {
                        return $record['tax'];
                    }
                    return round($record['tax'] * 100 / (100 - (float)$record['savings_rate']), 2);
                },
                'price_actual_taxless' => 'price',
                'tax_actual' =>'tax',
                'discount_rate' => function($record) {
                    return (float)$record['savings_rate'];
                },
                'tax_rate' => 'tax_rate',
                'eshop_voucher_discount_rate' => 'eshop_voucher_discount_rate',
                'static_attributes' => function() {
                    return '[]';
                },
                'dynamic_attributes' => function() {
                    return '[]';
                },
                //'cleaned_up_product_data' => null,
            ),
            array_merge($options, array(
                'createTargetTable' => true,
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_product_types -> run_eshop_product_types
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopProductTypes($options = array()) {
        return $this->migrate(
            'eshop_product_types',
            'Eshop.EshopProductType',
            array(
                'id' => 'id',
                'pid' => 'pid',
                'name' => 'name',
                'description' => 'description',
                'created' => 'created',
                'modified' => 'modified',        
            ),
            array_merge($options, array(
                'createTargetTable' => true,
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      comments_comments -> run_comments (but only items with model = 'EshopProduct')
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopProductComments($options = array()) {
        return $this->migrate(
            'comments_comments',
            'App.Comment',
            array(
                'id' => 'id',
                'run_users_id' => 'user_id',
                'foreign_model' => function () { return 'Eshop.EshopProduct'; },
                'foreign_id' => 'foreign_key',
                'status' => function($record) {
                    $conversions = array(
                        'Awaiting Approval' => 'enum_submitted_comment',
                        'Approved' => 'enum_approved_comment',
                        'Spam' => 'enum_rejected_comment',
                    );
                    return $conversions[$record['status']];
                },
                'name' => 'author',
                'text' => 'body',
                'rating' => 5,
                'created' => 'created',
                'modified' => 'modified',    
            ),
            array_merge($options, array(
                'createTargetTable' => true,
                'sourceSelectOptions' => array(
                    'conditions' => array(
                        'model' => 'EshopProduct',
                    ),
                    'order' => 'id ASC'
                )
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      eshop_products + eshop_product_i18ns + eshop_product_attribute_types + eshop_product_attributes + eshop_product_attribute_i18ns + eshop_taxes + attachments -> run_eshop_products
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopProducts($options = array()) {
        $lastMigratedProductId = 0; // update this from 0 to xxx if the migration of products is not finished on one request
        $taxRates = array();
        $publishingYears = array();
        if ($options['action'] !== 'clear') {
            $taxRates = DB::select($this->qualifyBySorceDatabase('eshop_taxes'), array(
                'fields' => array(
                    'id', 'rate'
                )
            ));
            $tmp = array();
            foreach ($taxRates as $taxRate) {
                $tmp[$taxRate['id']] = $taxRate['rate'];
            }
            $taxRates = $tmp;
            $publishingYears = DB::select($this->qualifyBySorceDatabase('eshop_product_attributes'), array(
                'alias' => 'epa',
                'joins' => array(
                    array(
                        'table' => $this->qualifyBySorceDatabase('eshop_product_attribute_i18ns'),
                        'alias' => 'i18n_value',
                        'type' => 'left',
                        'conditions' => array(
                            'epa.id = i18n_value.foreign_key',
                            'i18n_value.model' => 'EshopProductAttribute',
                            'i18n_value.locale' => 'slo',
                            'i18n_value.field' => 'value',
                        )
                    ),  
                ),
                'fields' => array(
                    'epa.eshop_product_id', 'i18n_value.content AS value'
                ),
                'conditions' => array(
                    // hardcoded year attr type id
                    'eshop_product_attribute_type_id' => 8, 
                    'i18n_value.content !=' => array('', null)
                ),
            ));
            $tmp = array();
            foreach ($publishingYears as $i => $publishingYear) {
                $tmp[$publishingYear['eshop_product_id']] = $publishingYear['value'];
                unset($publishingYears[$i]);
            }
            $publishingYears = $tmp;
            unset($tmp);
        }
        $result = $this->migrate(
            'eshop_products',
            'Eshop.EshopProduct',
            // fields map 
            array(
                'id' => 'id',
                //'parent_id' => null,
                'code' => 'uid',
                //'ean' => null, // migrated here below with other attributes
                'slug' => 'slug',
                'seo_title' => 'name',
                //'seo_description' => null,
                //'seo_keywords' => null,
                'name' => 'name',
                'description' => 'description',
                //'short_description' => null,
                //'image' => null,
                'run_eshop_manufacturers_id' => 'eshop_manufacturer_id',
                'run_eshop_manufacturer_ranges_id' => 'eshop_product_series_id',
                //'manufacturer_group' => null,
                'run_eshop_product_types_id' => 'eshop_product_type_id',
                'price' => 'price',
                'discount_price' => 'price_discounted',
                'discount_rate' => 'discount_rate',
                //'discount_from' => null,
                'discount_to' => 'price_discounted_end',
                'tax_rate' => function($record) use ($taxRates) {
                    return (int)$taxRates[$record['eshop_tax_id']];
                },
                //'synchronize_price_with_suppliers' => 1,
                'stock' => 'stock',
                //'units' => 'enum_piece',
                //'stock_reserved' => 0,
                //'stock_location_code' => null,
                //'shop_location_code' => null,
                //'supplier_info' => null,
                //'supplier_pid' => null,
                'availability' => function (&$record) use ($publishingYears) {
                    // used here below in 'reprint' => ...
                    $record['reprint'] = false;
                    if (
                        empty($record['available'])
                        || Date::getDiff('days', date('Y-m-d'), $record['available']) < 0

                    ) {
                        if ($record['sold_out']) {
                            return 'enum_soldout';
                        }
                        else {
                            return 'enum_available';
                        }
                    }
                    elseif (
                        empty($publishingYears[$record['id']])
                        || !($publishingYear = (int)$publishingYears[$record['id']])
                        || !($availableYear = (int)date('Y', strtotime($record['available'])))
                        || $availableYear === $publishingYear
                        || ($availableYear - 1) === $publishingYear
                    ) {
                        return 'enum_presale';
                    }
                    else {
                        $record['reprint'] = true;
                        return 'enum_presale';
                    }
                },
                //'long_delivery_time' => 0,       
                'reprint' => function($record) {
                    // $record['reprint'] is set here above in 'availability' => ...
                    return $record['reprint'];
                },     
                'available_from' => 'available',
                'shipment_time_off_stock' => 'shipment_time_off_stock',
                'weight' => 'weight',
                //'width' => null, // migrated here below with other attributes
                //'height' => null, // migrated here below with other attributes
                //'length' => null, // migrated here below with other attributes
                //'dimensions' => null, // migrated here below with other attributes
                //'note' => null,
                'active' => 'active',
                'created' => 'created',
                'modified' => 'modified',        
                'exported' => 'exported', 
                //'image_exported' => null, 
                //'deleted' => null, 
                //'variant' => null,
                //'ebook_url' => null,
                //'ebook_url_2' => null,
                //'binding' => null, // migrated here below with other attributes
                //'language' => null, // migrated here below with other attributes
                //'pages' => null, // migrated here below with other attributes
                //'isbn' => null, // migrated here below with other attributes
                //'subtitle' => null, // migrated here below with other attributes
                //'original_name' => null, // migrated here below with other attributes
                'year' => function ($record) use ($publishingYears) {
                    if (!empty($publishingYears[$record['id']])) {
                        return $publishingYears[$record['id']];
                    }
                    return null;
                },
                //'time_length' => null, // migrated here below with other attributes
                //'starring' => null, // migrated here below with other attributes
                //'dubbing' => null, // migrated here below with other attributes
                //'subtitles' => null, // migrated here below with other attributes
                //'media_type' => null, // migrated here below with other attributes
                'pdf_overview' => 'pdf_overview',
                        
            ),
            // options
            array_merge($options, array (
                'batchSize' => 3000, // to avoid mysql error "Got a packet bigger than 'max_allowed_packet' bytes"
                'updateTargetTableFields' => true,
                'sourceSelectOptions' => array(
                    'conditions' => array(
                        'ep.id >' => $lastMigratedProductId,
                    ),
                    'alias' => 'ep',
                    'joins' => array(
                        array(
                            'table' => 'eshop_product_i18ns',
                            'alias' => 'i18n_description',
                            'type' => 'left',
                            'conditions' => array(
                                'ep.id = i18n_description.foreign_key',
                                'i18n_description.model' => 'EshopProduct',
                                'i18n_description.locale' => 'slo',
                                'i18n_description.field' => 'description',
                            )
                        ),  
                    ),
                    'fields' => array(
                        'ep.id',
                        'ep.uid',
                        'ep.slug',
                        'ep.name',
                        'i18n_description.content AS description',
                        'ep.price',
                        'ep.price_discounted',
                        'ep.discount_rate',
                        'ep.price_discounted_end',
                        'ep.eshop_tax_id',
                        'ep.stock',
                        'ep.sold_out',
                        'ep.unavailable',
                        'ep.available',
                        'ep.shipment_time_off_stock',
                        'ep.weight',
                        'ep.active',
                        'ep.eshop_manufacturer_id',
                        'ep.eshop_product_type_id',
                        'ep.eshop_product_series_id',
                        'ep.pdf_overview',
                        'ep.created',
                        'ep.modified',
                        'ep.exported',
                    )
                )
            ))
        );  
        if ($options['action'] === 'clear') {
            return $result;
        }
        // move old dynamic attributes into new static attributes:
        // 
        // format -> binding: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(1) ORDER BY LENGTH(value) DESC
        // language: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(2) ORDER BY LENGTH(value) DESC
        // pages: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(3) ORDER BY LENGTH(value) DESC
        // dimensions: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(4) ORDER BY LENGTH(value) DESC
        // weight: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(5) ORDER BY LENGTH(value) DESC
        // isbn: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(6) ORDER BY LENGTH(value) DESC
        // original_name: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(7, 13, 21) ORDER BY LENGTH(value) DESC
        // year: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(8, 15, 23) ORDER BY LENGTH(value) DESC
        // subtitle: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(12) ORDER BY LENGTH(value) DESC
        // length -> time_length: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(14, 22) ORDER BY LENGTH(value) DESC
        // starring: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(16, 24) ORDER BY LENGTH(value) DESC
        // dubbing: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(17, 25) ORDER BY LENGTH(value) DESC
        // subtitles: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(18, 26) ORDER BY LENGTH(value) DESC
        // media_type: SELECT value, LENGTH(value) FROM `eshop_product_attributes` WHERE `eshop_product_attribute_type_id` IN(28, 29) ORDER BY LENGTH(value) DESC    
        $productsTable = $this->qualifyByTargetDatabase('run_eshop_products');
        $attributesTable = $this->qualifyBySorceDatabase('eshop_product_attributes');
        $attributeI18nsTable = $this->qualifyBySorceDatabase('eshop_product_attribute_i18ns');
        foreach (
            array(
                'binding' => array(1),
                'language' => array(2),
                'pages' => array(3),
                'dimensions' => array(4),
                'weight' => array(5),
                'isbn' => array(6),
                'original_name' => array(7),
                //'year' => array(8), // treated in the main migration here above
                'ean' => array(10),
                'subtitle' => array(12),
                'time_length' => array(32),
                'starring' => array(16),
                'dubbing' => array(17),
                'subtitles' => array(18),
                'media_type' => array(34),
            )
            as  $targetField => $sourceAttributeTypeIds
        ) {
            if ($targetField === 'weight') {
                $this->migrate(
                    'eshop_product_attributes',
                    'Eshop.EshopProduct',
                    array(
                        'id' => 'eshop_product_id',
                        'weight' => 'weight', // normalized already by 'validate' option
                    ),
                    // options
                    array_merge($options, array(
                        'update' => true,
                        'sourceSelectOptions' => array(
                            'alias' => 'epa',
                            'joins' => array(
                                array(
                                    'table' => 'eshop_product_attribute_i18ns',
                                    'alias' => 'i18n',
                                    'type' => 'left',
                                    'conditions' => array(
                                        'epa.id = i18n.foreign_key',
                                        'i18n.locale' => 'slo',
                                        'i18n.field' => 'value',
                                    )
                                ),  
                            ),
                            'fields' => array(
                                'epa.eshop_product_id',
                                'i18n.content AS weight',
                            ),
                            'conditions' => array(
                                'epa.eshop_product_attribute_type_id' => $sourceAttributeTypeIds,
                            ),
                        ),
                        'preprocess' => function($record) {
                            // validate
                            if (
                                empty($record['weight'])
                                || !preg_match('/[0-9]/', $record['weight'])
                            ) {
                                return false;
                            }
                            // normalize to float number in kg
                            $record['weight'] = str_replace(',', '.', $record['weight']);
                            $units = 'kg';
                            if (
                                !preg_match('/kg/i', $record['weight'])
                                && preg_match('/g/i', $record['weight'])
                            ) {
                                $units = 'g';
                            }
                            $record['weight'] = preg_replace('/[^0-9\.]/i', '', $record['weight']);
                            // change value from g to kg
                            if ($units === 'g') {
                                $record['weight'] = $record['weight'] / 1000;
                            }
                            return $record;
                        },
                    ))
                );
            }
            else {
                $sourceAttributeTypeIds = implode(',', $sourceAttributeTypeIds);
                DB::query("UPDATE {$productsTable} a LEFT JOIN {$attributesTable} b ON (a.id = b.eshop_product_id AND b.eshop_product_attribute_type_id IN ({$sourceAttributeTypeIds})) LEFT JOIN {$attributeI18nsTable} c ON (b.id = c.foreign_key AND c.field = 'value' AND c.locale = 'slo') SET a.{$targetField} = c.content;");
            }
            $result[] = Str::fill(
                'Product %s has been migrated', 
                $targetField
            );
        }
        
        // move main images
        $attachmentsTable = $this->qualifyBySorceDatabase('attachments');
        DB::query("UPDATE {$productsTable} a LEFT JOIN {$attachmentsTable} b ON (a.id = b.foreign_key AND b.model = 'EshopProduct' AND b.weight = 1) SET a.image = b.basename WHERE b.id IS NOT NULL;");
        $result[] = 'Product images has been migrated';
        
        return $result;      
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *      attachments -> eshop_product_images
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopProductImages($options = array()) {
        return $this->migrate(
            'attachments',
            'Eshop.EshopProductImage',
            array(
                'run_eshop_products_id' => 'foreign_key',
                'file' => 'basename',
                'name' => 'basename',
                'sort' => function ($record) {
                    return $record['weight'] - 2;
                },
                'created' => 'created',
            ),
            array_merge($options, array(
                'createTargetTable' => true,
                'sourceSelectOptions' => array(
                    'conditions' => array(
                        'model' => 'EshopProduct',
                        'weight >' => 1,
                    )
                )
            ))
        );
    }
    
    /**
     * Moves product gallery images from folder of EshopProduct.image file field
     * to folder of EshopProductImage.file file field.
     * 
     * NOTE: Launch this method only after Migrate::migrateEshopProducts() and :.migrateEshopProductImages()
     * has been launched.
     */
    public function moveEshopProductImages() {
        $Product = App::loadModel('Eshop', 'EshopProduct', true);
        $ProductImage = App::loadModel('Eshop', 'EshopProductImage', true);
        $images = $ProductImage->find(array('fields' => array('file')));
        $result = array();
        foreach($images as $image) {
            $sourceFile = ROOT . DS . $Product->getFileFieldPath('image', array(
                'file' => $image['file'],
                'variant' => 'original',
            ));
            if (!is_readable($sourceFile)) {
                continue;
            }
            $targetFile = ROOT . DS . $ProductImage->getFileFieldPath('file', array(
                'file' => $image['file'],
                'variant' => 'original'
            ));
            $result[] = 'Moving "' . $sourceFile . '" to "' . $targetFile . '"';
            rename($sourceFile, $targetFile);
        }
        return $result;
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *     eshop_wishlists -> run_eshop_wishlists
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopWhishlists($options = array()) {
        return $this->migrate(
            'eshop_wishlists',
            'Eshop.EshopWishlist',
            array(                        
                'id' => 'id',
                'run_users_id' => 'user_id',
                //'email' => null,
                'name' => 'name',
                'privacy_level' => 'privacy_level',
                'access_token' => 'access_token',
                'from_cart' => 'cart_wishlist',
                'default' => 'default',
                'created' => 'created',
                'modified' => 'modified', 
            ),
            array_merge($options, array(
                'createTargetTable' => true,
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *     eshop_products_eshop_wishlists -> run_eshop_wishlist_products
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopWhishlistProducts($options = array()) {
        return $this->migrate(
            'eshop_products_eshop_wishlists',
            'Eshop.EshopWishlistProduct',
            array(                        
                'id' => 'id',
                'run_eshop_wishlists_id' => 'eshop_wishlist_id',
                'run_eshop_products_id' => 'eshop_product_id',
                'amount' => 'amount',
                'attributes' => 'attributes',
                'watchdog' => 'watchdog',
                'created' => 'created',
            ),
            array_merge($options, array(
                'createTargetTable' => true,
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *     eshop_vouchers -> run_eshop_vouchers
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateEshopVouchers($options = array()) {
        return $this->migrate(
            'eshop_vouchers',
            'Eshop.EshopVoucher',
            array(    
                'id' => 'id',
                'code' => 'code',
                //'run_users_id' => null,
                //'run_eshop_products_ids' => null,
                'discount_rate' => 'discount_rate',
                //'special_discount_rate' => null,
                'active_from' => 'active_from',
                'active_to' => 'active_to',
                //'applications_limit' => null,
                //'applications_count' => null,
                'active' => 'active',
                'created' => 'created',
                'modified' => 'modified',        
            ),
            array_merge($options, array(
                'createTargetTable' => true,
            ))
        );
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *     newsletter_recipients -> run_mailer_contacts
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateMailerContacts($options = array()) {
        $defaults = array(
            'action' => 'migrate',
        );
        $options = array_merge($defaults, $options);
        $result = $this->migrate(
            'newsletter_recipients',
            'Mailer.MailerContact',
            array(    
                'id' => 'id',
                'token' => 'deactivation_code',
                //'run_users_id' => null,
                'email' => 'email',
                //'first_name' => null,
                //'last_name' => null,
                //'company_name' => null,
                //'street' => null,
                //'city' => null,
                //'zip' => null,
                //'phone' => null,
                //'birthday' => null,
                //'run_regions_id' => null,
                //'vip' => null,
                'status' => function() {return 'enum_approved_contact';},
                'created' => 'created',
                'modified' => 'modified',        
            ),
            array_merge($options, array(
                'createTargetTable' => true,
                'sourceSelectOptions' => array(
                    'conditions' => array(
                        'active' => true,
                    )
                )                
            ))
        );
                
        if ($options['action'] !== 'clear') {
            // add all contacts to default group
            App::loadModel('Mailer', 'MailerContact');
            $Contact = new MailerContact();
            $contactIds = $Contact->findList(array(
                'fields' => array('id')
            ));
            App::loadModel('Mailer', 'MailerGroup');
            $Group = new MailerGroup();
            $groupId = $Group->findFieldBy('id', 'default', 1);
            $batch = array();
            foreach ($contactIds as $contactId) {
                $batch[] = array(
                    'run_mailer_groups_id' => $groupId,
                    'run_mailer_contacts_id' => $contactId,
                );
            }   
            App::loadModel('Mailer', 'MailerGroup');
            $Group->saveBatch(array(
                'delete' => array(
                    'Mailer.MailerGroupContact' => array(
                        'id >=' => 0 // delete them all
                    )
                ),
                'create' => array(
                    'Mailer.MailerGroupContact' => $batch,
                )
            ));
        }
        
        return $result;
    }
    
    /**
     * Migrates vydavatel cakePHP tables to fajnwork tables:
     * 
     *     settings_settings -> run_settings
     * 
     * @param $options See Migration::migrate() options
     * 
     * @return string|array
     */
    public function migrateSettings($options = array()) {
        $defaults = array(
            'action' => 'migrate',
        );
        $options = array_merge($defaults, $options);
        if ($options['action'] === 'clear') {
            return array(
                'Clear does not apply on settings table. Please use only migrate',
            );
        }
        $result = array();
        
        $Setting = App::loadModel('App', 'Setting', true);
        $settings = $Setting->findList(array(
            'key' => 'id',
            'fields' => array(
                'CONCAT(`module`, ".", `pid`) AS module_pid',
            ),
            'order' => 'CONCAT(`module`, ".", `pid`) ASC',
            'literals' => array(
                'fields' => true,
                'order' => true,
            )
        ));
        $settings = array_flip($settings);
//        echo '<pre>' . print_r($settings, true) . '</pre>'; return; //debug
        $sourceSettings = DB::select($this->qualifyBySorceDatabase('settings_settings'), array(
            'fields' => array(
                'id',
                'name',
                'value',
            ),
        ));        
        
        $records = array();
        $convertInserts = function ($string) {
            $convertions = array(
                ':siteName' => ':eshopName:',
                ':orderNumber' => ':orderNumber:',
                ':commentBody' => ':commentText:',
                ':commentAuthor' => ':commentAuthor:',
                ':userName' => ':userName:',
                ':orderCreated' => ':orderCreated:',
                ':orderProducts' => ':orderProducts:',
                ':shippingCost' => ':shipmentCost:',
                ':shippingMethod' => ':shipmentMethod:',
                ':paymentMethod' => ':paymentMethod:',
                ':orderBonus' => ':orderBonus:',
                ':orderTotal' => ':orderTotal:',
                ':shippingAddress' => ':deliveryAddress:',
                ':userMessage' => ':userComment:',
                ':bankAccount' => ':bankAccount:',
                ':variableSymbol' => ':variableSymbol:',
                ':constantSymbol' => ':constantSymbol:',
                ':userEmail' => ':userEmail:',
                ':userPhone' => ':userPhone:',
                ':unsubscribeLink' => ':unsubscribeUrl:',
                ':giftPackageCost' => '', // remove
                ':paymentLink' => ':paymentLink:',
                ':suhlasSoZaslanimReklamnychMaterialov' => '', // remove
                ':companyInfo' => ':companyInfo:',
                ':deliveryDate' => ':deliveryDate:',
                ':linkOrder' => ':ordersLink:',
                ':orderStatus' => ':orderStatus:',
                ':wishlistName' => ':wishlistName:',
                ':wishlistUrl' => ':wishlistUrl:',
                ':productName' => ':productName:',
                ':productUrl' => ':productUrl:',
            );
            return str_replace(array_keys($convertions), array_values($convertions), $string);
        };
        App::setSqlLogging(false);        
        foreach ($sourceSettings as $sourceSetting) {
            switch ($sourceSetting['name']) {
                case 'Cashier.CashierPaymentMethod.constantSymbol':
                    $records[] = array(
                        'id' => $settings['Payment.constantSymbol'],
                        'value' => $sourceSetting['value'],
                    );
                    break;
                case 'Cashier.CashierPaymentMethod.description':
                    $records[] = array(
                        'id' => $settings['Payment.paymentDescription'],
                        'value' => $sourceSetting['value'],
                    );
                    break;
                case 'Cashier.CashierPaymentMethod.specificSymbol':
                    $records[] = array(
                        'id' => $settings['Payment.specificSymbol'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Cashier.CashierPaymentMethod.returnEmail':
                    $records[] = array(
                        'id' => $settings['Payment.returnEmail'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Cashier.CashierPaymentMethod.returnSMS':
                    $records[] = array(
                        'id' => $settings['Payment.returnSms'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Core.All.emailAdmin':
                    $records[] = array(
                        'id' => $settings['App.email.cc'],
                        'value' => $sourceSetting['value'],                        
                    );
                    $records[] = array(
                        'id' => $settings['App.email.to'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Core.All.emailNoReply':
                    $records[] = array(
                        'id' => $settings['App.email.from'],
                        'value' => $sourceSetting['value'],                        
                    );
                    $records[] = array(
                        'id' => $settings['Eshop.email.from'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Core.All.emailBcc':
                    $records[] = array(
                        'id' => $settings['App.email.bcc'],
                        'value' => $sourceSetting['value'],                        
                    );
                    $records[] = array(
                        'id' => $settings['Eshop.email.bcc'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Core.All.siteDescription':
                    $records[] = array(
                        'id' => $settings['App.seo.defaultDescription'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Core.All.siteKeywords':
                    $records[] = array(
                        'id' => $settings['App.seo.defaultKeywords'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Core.All.siteName':
                    $records[] = array(
                        'id' => $settings['App.name'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Core.All.smtpHost':
                    $records[] = array(
                        'id' => $settings['App.smtp.host'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Core.All.smtpPassword':
                    $records[] = array(
                        'id' => $settings['App.smtp.password'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Core.All.smtpPort':
                    $records[] = array(
                        'id' => $settings['App.smtp.port'],
                        'value' => $sourceSetting['value'],                        
                    );
                    if ($sourceSetting['value'] == 465) {
                        $records[] = array(
                            'id' => $settings['App.smtp.encryption'],
                            'value' => 'SSL',                        
                        );
                    }
                    elseif ($sourceSetting['value'] == 587) {
                        $records[] = array(
                            'id' => $settings['App.smtp.encryption'],
                            'value' => 'TLS',                        
                        );
                    }
                    else {
                        $records[] = array(
                            'id' => $settings['App.smtp.encryption'],
                            'value' => '',                        
                        );
                    }
                    break;
                case 'Core.All.smtpUsername':
                    $records[] = array(
                        'id' => $settings['App.smtp.username'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopOrder.authRequired':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.allowQuickOrder'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopOrder.bodyPaymentFailed':
                    $result[] = '!!! Check the Eshop.EshopOrder.msgBodyPaymentFailed';
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.msgBodyPaymentFailed'],
                        'value' => $convertInserts($sourceSetting['value']),
                    );
                    break;
                case 'Eshop.EshopOrder.subjectPaymentFailed':
                    $result[] = '!!! Check the Eshop.EshopOrder.msgSubjectPaymentFailed';
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.msgSubjectPaymentFailed'],
                        'value' => $convertInserts($sourceSetting['value']),
                    );
                    break;
                case 'Eshop.EshopOrder.bodyPaymentSuccessful':
                    $result[] = '!!! Check the Eshop.EshopOrder.msgBodyPaymentSuccessful';
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.msgBodyPaymentSuccessful'],
                        'value' => $convertInserts($sourceSetting['value']),
                    );
                    break;
                case 'Eshop.EshopOrder.subjectPaymentSuccessful':
                    $result[] = '!!! Check the Eshop.EshopOrder.msgSubjectPaymentSuccessful';
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.msgSubjectPaymentSuccessful'],
                        'value' => $convertInserts($sourceSetting['value']),
                    );
                    break;
                case 'Eshop.EshopOrder.msgBodyNewOrder':
                    $result[] = '!!! Check the Eshop.EshopOrder.msgBodyNewOrder';
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.msgBodyNewOrder'],
                        'value' => $convertInserts($sourceSetting['value']),
                    );
                    break;
                case 'Eshop.EshopOrder.msgSubjectNewOrder':
                    $result[] = '!!! Check the Eshop.EshopOrder.msgSubjectNewOrder';
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.msgSubjectNewOrder'],
                        'value' => $convertInserts($sourceSetting['value']),
                    );
                    break;
                case 'Eshop.EshopOrder.maxDiscountRate':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopProduct.additionalDiscountRateLimit'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopOrder.maxVydavatelDiscountRate':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopProduct.additionalDiscountRateLimitForVydavatel'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopOrder.smartsmsMerchantPhone':
                    $records[] = array(
                        'id' => $settings['App.smartsms.from'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopOrder.smartsmsMessageInStore':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.smartsmsMessageA'],
                        'value' => $convertInserts($sourceSetting['value']),                        
                    );
                    break;
                case 'Eshop.EshopOrder.smartsmsMessageShipped':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.smartsmsMessageB'],
                        'value' => $convertInserts($sourceSetting['value']),                        
                    );
                    break;
                case 'Eshop.EshopOrder.smartsmsPassword':
                    $records[] = array(
                        'id' => $settings['App.smartsms.password'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopOrder.smartsmsUsername':
                    $records[] = array(
                        'id' => $settings['App.smartsms.username'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopProduct.pagingLimit':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopProduct.pagingLimit'],
                        'value' => 12, //$sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopProduct.priceIsTaxed':
                    $records[] = array(
                        'id' => $settings['Eshop.pricesAreTaxed'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopProduct.shipmentTimeOffStock':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopProduct.shipmentTimeOffStock'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopProduct.shipmentTimeOnStock':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopProduct.shipmentTimeOnStock'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopProductAttribute.pricesSeparator':
                    $records[] = array(
                        'id' => $settings['App.attributePricesSeparator'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopProductAttribute.valuesSeparator':
                    $records[] = array(
                        'id' => $settings['App.attributeValuesSeparator'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopShippingMethod.freeShippingLimit':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopShipment.freeShipmentProductsTotal'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopWishlist.msgBodyShareWishlist':
                    $result[] = '!!! Check the Eshop.EshopWishlist.msgBodyShareWishlist';
                    $records[] = array(
                        'id' => $settings['Eshop.EshopWishlist.msgBodyShareWishlist'],
                        'value' => $convertInserts($sourceSetting['value']),
                    );
                    break;
                case 'Eshop.EshopWishlist.msgSubjectShareWishlist':
                    $result[] = '!!! Check the Eshop.EshopWishlist.msgSubjectShareWishlist';
                    $records[] = array(
                        'id' => $settings['Eshop.EshopWishlist.msgSubjectShareWishlist'],
                        'value' => $convertInserts($sourceSetting['value']),
                    );
                    break;
                case 'Vydavatel.VydavatelExport.lastUnregUserMrpAddressId':
                    App::loadLib('Eshop', 'EshopMrpQuickOrderAddressUniqueId');
                    $UniqueId = new EshopMrpQuickOrderAddressUniqueId();
                    $UniqueId->setLast($sourceSetting['value']);
                    break;
                
                case 'Eshop.EshopOrder.bonusAmount':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.bonusDiscount'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopOrder.bonusableTotal':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.applicableBonusPoints'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopOrder.bankAccount':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.bankAccount'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopOrder.bankTransferConstantSymbol':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopOrder.bankTransferConstantSymbol'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Core.All.facebookAppId':
                    $records[] = array(
                        'id' => $settings['App.facebook.appId'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopShipmentMethod.zasielkovnaApiKey':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopShipmentMethod.zasielkovnaApiKey'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopShipmentMethod.zasielkovnaPickupPlaces':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopShipmentMethod.zasielkovnaPickupPlaces'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
                case 'Eshop.EshopWishlist.msgBodyWishlistWatchdog':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopWishlist.msgBodyWishlistWatchdog'],
                        'value' => $convertInserts($sourceSetting['value']),                        
                    );
                    break;
                case 'Eshop.EshopWishlist.msgSubjectWishlistWatchdog':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopWishlist.msgSubjectWishlistWatchdog'],
                        'value' => $convertInserts($sourceSetting['value']),                        
                    );
                    break;
                case 'Alterego.PemicImport.ignoredPublishers':
                    $records[] = array(
                        'id' => $settings['Eshop.EshopSupplierProduct.ignoredPublishers'],
                        'value' => $sourceSetting['value'],                        
                    );
                    break;
            }
            if (!end($records)['id']) {
                // remove invalid record without id
                array_pop($records);
                $result[] = '!!! Invalid target setting name for source setting "' . $sourceSetting['name'] . '" !!!';
                
            }
        }
        // add some explicit setting values
        $records[] = array(
            'id' => $settings['App.seo.defaultTitle'],
            'value' => 'Internetové kníhkupectvo',                        
        );
        $records[] = array(
            'id' => $settings['App.seo.titleSuffix'],
            'value' => ' | Alterego.sk',                        
        );
//        echo '<pre>' . print_r($records, true) . '</pre>'; return $result; //debug
        // save the batch
        if (!empty($records)) {
            $Setting->saveBatch(
                array(
                    'update' => array(
                        'App.Setting' => &$records,
                    )
                ),
                array(
                    'reserve' => false,
                )
            );
        } 
        return $result;
    }
    
    /**
     * Returns list of inserts actually used in existing source settings
     * 
     * @return array
     */
    public function getSourceSettingsInserts() {
        $sourceSettings = DB::select($this->qualifyBySorceDatabase('settings_settings'), array(
            'fields' => array(
                'value',
            ),
            'conditions' => array(
                'value REGEXP ":[a-zA-Z]+"'
            ),
            'literals' => array(
                'conditions' => true,
            )
        ));
        $inserts = array();
        $matches = null;
        foreach ($sourceSettings as $sourceSetting) {
            if (preg_match_all('/:[a-z]+/i', $sourceSetting['value'], $matches)) {
                foreach ($matches[0] as $match) {
                    $inserts[$match] = true;
                }
            }
        }
        return $inserts;
    }
    
    /**
     * General migration method to be used for simple migrations
     * 
     * @param string $sourceTable Source table name. This must be table name from source DB.
     *      It can be aliased e.g.: 'my_source_table AS mst'. Or better you can alias it 
     *      through option 'sourceSelectOptions' > 'alias'
     * @param string $targetModel Target model name
     * @param array $fieldsMap Conversion array containing pairs '{targetTableField}' => '{sourceTableField}|int|float|bool|function'.
     *      - If source is defined as nonempty string then it is considered to be 
     *          sourceTableField name, e.g. 'my_source_field'.
     *      - If source is defined as integer|float|bool|''(empty string) then it 
     *          is taken for explicit target field value. To provide explicit string 
     *          value use "function(){ return 'myStringValue'; }"
     *      - If source is defined as anonymous function then it gets the actual source record,
     *          result messages array and existing part of target record on its input and it must return value of 
     *          target table field. If you define anonymous function like "function ($record, &$result) {...}" 
     *          then you can add result messages. 
     *      E.g.:
     * 
     *          array(
     *              'id' => 'id',
     *              'name' => 'name',
     *              'seo_title' => 'name',
     *              'slug' => function($record) { return Str::slugize($record['name']); },
     *              'lang' => function(){ return 'sk'; },
     *              'active' => 1,
     *          )
     * 
     * @param $options Following are available:
     *      - 'action' (string) 'clear' or 'migrate'. Defaults to 'migrate'
     *      - 'createTargetTable' (bool) If TRUE then on 'migrate' action the target 
     *          table is created according $targetModel schema and on 'clear' action 
     *          the target table is dropped. ATTENTION: When dropping tables be aware that 
     *          also dropped table triggers are removed and they must be recreated 
     *          manually. Defaults to FALSE.
     *      - 'updateTargetTableFields' (bool) If TRUE then on 'migrate' action the 
     *          target table fields are updated according $targetModel schema. 
     *          Defaults to FALSE.
     *      - 'update' (bool) If TRUE then migration updates existing target data.
     *          In such case $fieldsMap must contain primary key of $targetModel.
     *          Defaults to FALSE it means that new records are inserted to $targetModel table.
     *      - 'batchSize' (integer) Defaults to 5000.
     *      - 'sourceSelectOptions' (array) DB::select() options to get source records, e.g.
     *          'alias', 'fields', 'joins' or 'conditions'. Eventual 'joins' must be defined
     *          of course by table syntax. Options 'page', 'limit' and 'resource' are forced internally. 
     *      - 'preprocess' (function) Anonymous function to preprocess each source record.
     *          It must return either array of adjusted record or TRUE to accept
     *          source record without change or FALSE (eventually any kind of empty 
     *          value) if the record should be skipped. The anonymous function args 
     *          are source record and result. Both of them can be passed by reference 
     *          according your needs. Defaults to NULL.
     *      - 'ignore' (bool) If TRUE then INSERT IGNORE queries are created. Defaults to FALSE.
     *      - 'debug' (bool|int) Number of records to be migrated for sake of debuging.
     *          If FALSE then all records are migrated. Defaults to Migration::$debug
     *          which defaults to FALSE.
     * 
     * @return array Array of processing messages. You can add any other message like $result[] = "My additional message"
     */
    public function migrate($sourceTable, $targetModel, $fieldsMap, $options = array()) {  
        $defaults = array(
            'action' => 'migrate',
            'createTargetTable' => false,
            'updateTargetTableFields' => false,
            'update' => false, 
            'batchSize' => 5000, // to avoid mysql error "Got a packet bigger than 'max_allowed_packet' bytes"
            'sourceSelectOptions' => array(),
            'preprocess' => null,
            'ignore' => false,
            'debug' => self::$debug,
        );
        $options = array_merge($defaults, $options);
        $options['batchSize'] = (int)$options['batchSize'];
        if ($options['debug']) {
            $options['debug'] = (int)$options['debug'];
        }
        if (
            !empty($options['preprocess'])
            && !Validate::anonymousFunction($options['preprocess'])
        ) {
            throw new Exception(Str::fill('Option \'preprocess\' must be anonymous function'));
        }
        if (!array_key_exists('fields', $options['sourceSelectOptions'])) {
            $options['sourceSelectOptions']['fields'] = array();
            foreach ($fieldsMap as $source) {
                if (is_string($source)) {
                    $options['sourceSelectOptions']['fields'][] = $source;
                }
                // if there is anonymous function we do not know which fields we need so retrieve them all
                elseif (Validate::anonymousFunction($source)) {
                    $options['sourceSelectOptions']['fields'] = null;
                    break;
                }
            }
        }
        $options['sourceSelectOptions']['resource'] = true;
        if (!empty($options['sourceSelectOptions']['joins'])) {
            foreach ($options['sourceSelectOptions']['joins'] as &$join) {
                $join['table'] = $this->qualifyBySorceDatabase($join['table']);
            }
            unset($join);
        }
        $result = array();
        $targetModelParts = explode('.', $targetModel);
        $Model = App::loadModel($targetModelParts[0], $targetModelParts[1], true);
        
        try {
            // clear tables
            if ($options['action'] === 'clear') {
                if ($options['createTargetTable']) {
                    $Model->dropTable();
                    $result[] = Str::fill('%s table has been dropped', $targetModel);
                }
                else {
                    $Model->truncateTable();
                    $result[] = Str::fill('%s table has been truncated', $targetModel);
                }   
                return $result;
            }

            // migrate
            if (
                $options['update']
                && empty($fieldsMap[$Model->getPropertyPrimaryKey()])
            ) {
                throw new Exception(Str::fill('If option \'update\' is TRUE hen field \'%s\' must be in $fieldsMap', $Model->getPropertyPrimaryKey()));
            }
            if (
                $options['createTargetTable']
                && $Model->createTable()
            ) {
                $result[] = Str::fill('%s table has been created', $targetModel);
            }
            if (
                $options['updateTargetTableFields']
                && ($addedFields = $Model->updateTableFields())
            ) {
                $result[] = Str::fill('%s table fields has been updated. Following fields has been added: %s', $targetModel, implode(', ', $addedFields));
            }
            // get fields to be migrated
            // retrieve source records in chunks (see the $sourceRecordsLimit below)
            // to avoid memory overflow. The $sourceRecordsLimit is hardcoded as
            // there is not must to change on it.
            App::setSqlLogging(false);
            $sourceRecordsCount = DB::select(
                $this->qualifyBySorceDatabase($sourceTable), 
                Arr::mergeRecursive($options['sourceSelectOptions'], array(
                    'fields' => 'COUNT(*) AS `_count`',
                    'literals' => array(
                        'fields' => array(
                            'COUNT(*) AS `_count`',
                        )
                    ),
                    'resource' => false,
                    'first' => true,
                ))
            );
            $sourceRecordsCount = $sourceRecordsCount['_count'];
            $sourceRecordsLimit = 20000;
            $sourceRecordsPages = (int)ceil($sourceRecordsCount / $sourceRecordsLimit);
            $count = 0;
            $records = array();
            $options['sourceSelectOptions']['limit'] = $sourceRecordsLimit;
            for ($page = 1; $page <= $sourceRecordsPages; $page++) {
                $options['sourceSelectOptions']['page'] = $page;
                $sourceRecords = DB::select(
                    $this->qualifyBySorceDatabase($sourceTable), 
                    $options['sourceSelectOptions']
                );
                while ($sourceRecord = DB::fetchArray($sourceRecords)) {
                    if (
                        $options['debug'] !== false 
                        && $options['debug'] == $count
                    ) {
                        break 2;
                    }
                    if (!empty($options['preprocess'])) {
                        if (!($preprocessResult = $options['preprocess']($sourceRecord, $result))) {
                            continue;
                        }
                        elseif (is_array($preprocessResult)) {
                            $sourceRecord = $preprocessResult;
                        }
                    }
                    $count++;
                    $record = array();
                    foreach ($fieldsMap as $field => $source) {
                        if (
                            !empty($source)
                            && is_string($source)
                        ) {
                            if(array_key_exists($source, $sourceRecord)) {
                                $record[$field] = $sourceRecord[$source];
                            }
                            else {
                                $record[$field] = '';
                            }
                        }
                        elseif (Validate::anonymousFunction($source)) {
                            $record[$field] = $source($sourceRecord, $result, $record);
                        }
                        else {
                            $record[$field] = $source;
                        }
                    }
                    if ($options['batchSize'] > 1) {
                        $record = $Model->normalize($record, array(
                            'on' => $options['update'] ? 'update' : 'create',
                        ));
                        $records[] = $record;
                        if ($count % $options['batchSize'] === 0) {
                            $batchAction = $options['update'] ? 'update' : 'create';
                            $Model->saveBatch(
                                array(
                                    $batchAction => array(
                                        $targetModel => &$records,
                                    )
                                ),
                                array(
                                    'reserve' => false,
                                    'ignore' => $options['ignore'],
                                )
                            );
                            $records = array();
                        }
                    }
                    else {
                        $Model->save(
                            $record,
                            array(
                                'create' => !$options['update'],
                                'validate' => false,
                                'reserve' => false,
                                'ignore' => $options['ignore'],
                            )
                        );
                    }
                }
            }
            // save the batch rest
            if (
                $options['batchSize'] > 1
                && !empty($records)
            ) {
                $batchAction = $options['update'] ? 'update' : 'create';
                $Model->saveBatch(
                    array(
                        $batchAction => array(
                            $targetModel => &$records,
                        )
                    ),
                    array(
                        'reserve' => false,
                        'ignore' => $options['ignore'],
                    )
                );
            }

            $result[] = Str::fill(
                '%s items has been migrated (%s) to %s', 
                $count, 
                $options['update'] ? 'updated' : 'created', 
                $targetModel
            );
        } 
        catch (Throwable $e) {
            if ($options['action'] === 'clear') {
                $result[] = Str::fill(
                    'EXCEPTION on %s clear: %s', 
                    $targetModel,
                    $e->getMessage()
                );
            }
            else {   
                $result[] = Str::fill(
                    'EXCEPTION on %s migration: %s', 
                    $targetModel,
                    $e->getMessage()
                );
            }
        }
                
        return $result;
    }
}

