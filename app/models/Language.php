<?php
class Language extends Model {
    protected $table = 'run_languages';
    
    protected $primaryKey = 'id';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'name' => array('type' => 'varchar'),
        //@link: for both fields below see: http://www.loc.gov/standards/iso639-2/php/code_list.php
        'lang' => array('type' => 'varchar', 'comment' => 'ISO 639-1 language code or "{ISO 3166-1 alpha-2 country code}/{ISO 639-1 language code}" if localized version is used'),
        'code' => array('type' => 'varchar', 'comment' => 'ISO 639-2 code for language'),
        //@link: see: http://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
        //@link: see: http://www.nationsonline.org/oneworld/country_code_list.htm
        'locale' => array('type' => 'varchar', 'comment' => 'Combination of ISO 639-1 language code _ uppercased ISO 3166-1 alpha-2 country code'),
        'currency_code' => array('type' => 'varchar', 'length' => 3, 'default' => 'EUR', 'comment' => 'Currency ISO 4217 code, e.g. EUR, CZK'),
        'icon' => array('type' => 'varchar', 'comment' => 'Language icon image'),
        'default' => array('type' => 'bool'),
        'localized_default' => array('type' => 'bool', 'comment' => 'Default lang in case of localized languages. Used during lang autodetection when only locale part of lang is given'),
        'active' => array('type' => 'bool', 'comment' => 'Is this language used in actual project?'),
        'published' => array('type' => 'bool', 'comment' => 'Is this language published for frontend?'),
        'sort' => array('type' => 'int'),
    );
        
    protected $fileFields = array(
        'icon' => array(
            'path' => 'icons',
        ),
    );
    
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Name is mandatory'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Name must be unique'),
                ),
            ),
            'lang' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Lang is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Lang is mandatory'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Lang must be unique'),
                ),
            ),
            'locale' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Locale is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Locale is mandatory'),
                ),
                array(
                    'rule' => 'unique',
                    'message' => __v(__FILE__, 'Locale must be unique'),
                ),
            ),
        );
    }    
    
    //
    // TESTING METHODS FOR SMART INDEX DEVELOPMENT
    // 
    
    public function renderFieldDefault($value) {
        if ($value) {
            return 'Yes';
        }
        return 'No';
    }
    
    public function renderRow($data) {
        $attributes = array();
        if (
            isset($data['default'])
            && $data['default'] === '1'
        ) {
            $attributes['style'] = 'color: #0F0';
        }
        return $attributes;
    }
    
}