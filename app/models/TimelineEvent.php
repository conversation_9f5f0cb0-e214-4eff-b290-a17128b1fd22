<?php
class TimelineEvent extends Model {
    protected $table = 'run_timeline_events';
    
    //.../mvc/App/Tools/createTable/App/TimelineEvent
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_timelines_id' => array('type' => 'int'),
        'name' => array('type' => 'varchar', 'default' => null),
        'text' => array('type' => 'text'),
        'start' => array('type' => 'int', 'comment' => 'Event start year'),
        'end' => array('type' => 'int', 'default' => null, 'comment' => 'Event end year'),
        'position' => array('type' => 'enum', 'values' => array('enum_left', 'enum_right'), 'default' => 'enum_left'),
        'lang' => array('type' => 'varchar', 'length' => 5),
        'active' => array('type' => 'bool', 'default' => 0),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(
            'text' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Text is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Text is mandatory'),
                ),
            ),
            'start' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Start year is mandatory'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Start year is mandatory'),
                ),
                array(
                    'rule' => '/[\d]{4}/',
                    'message' => __v(__FILE__, 'Start year must be an 4 digits integer'),
                ),
            ),
        );
    }
            
    public function renderTextInIndex($value) {
        $value = Sanitize::htmlToText($value, false);
        $value = Str::truncate($value, 50);
        return $value;
    }

}
