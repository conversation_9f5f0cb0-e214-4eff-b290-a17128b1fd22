<?php
/**
 * @info To create chained country > region > district selectboxes see http://www.appelsiini.net/projects/chained.
 */
class Region extends Model {
    protected $table = 'run_regions';
    
    protected $primaryKey = 'id';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_countries_id' => array('type' => 'int', 'index' => 'index'),
        'name' => array('type' => 'varchar'),
    );
    
    /**
     * Return array of options for selectbox. 
     * 
     * Can be used with chained country > region > district selectboxes (see http://www.appelsiini.net/projects/chained)
     * 
     * @param string $countryIsoCode2 Country iso code 2, e.g. 'SK', 'CZ'.
     * 
     * @return array Array of options for selectbox. If invalid country id provided then empty array.
     */
    public function getSelectOptionsByCountry($countryIsoCode2) {
        $options = array();
        $Country = App::loadModel('App', 'Country', true);
        $countryId = $Country->findFieldBy('id', 'iso_code_2', $countryIsoCode2);
        if (empty($countryId)) {
            return $options;
        }
        $regions = $this->findList(array(
            'key' => 'Region.id',
            'fields' => array('Region.name'),
            'conditions' => array(
                'Region.run_countries_id' => $countryId
            ),
            'order' => 'Region.id ASC'
        ));
        if (!empty($regions)) {
            $options = array('' => '') + $regions; // use + instead of array_merge to preserve keys
        }
        return $options;
    }
    
    public function getInitialRecords() {
        return array(
            // Slovenska republika
            array('id' => 1, 'run_countries_id' => 189, 'name' => 'Banskobystrický kraj'),
            array('id' => 2, 'run_countries_id' => 189, 'name' => 'Bratislavský kraj'),
            array('id' => 3, 'run_countries_id' => 189, 'name' => 'Košický kraj'),
            array('id' => 4, 'run_countries_id' => 189, 'name' => 'Nitriansky kraj'),
            array('id' => 5, 'run_countries_id' => 189, 'name' => 'Prešovský kraj'),
            array('id' => 6, 'run_countries_id' => 189, 'name' => 'Trenčiansky kraj'),
            array('id' => 7, 'run_countries_id' => 189, 'name' => 'Trnavský kraj'),
            array('id' => 8, 'run_countries_id' => 189, 'name' => 'Žilinský kraj'),
            // Česká republika
            array('id' => 9, 'run_countries_id' => 56, 'name' => 'Praha'),
            array('id' => 10, 'run_countries_id' => 56, 'name' => 'Jihočeský kraj'),
            array('id' => 11, 'run_countries_id' => 56, 'name' => 'Jihomoravský kraj'),
            array('id' => 12, 'run_countries_id' => 56, 'name' => 'Karlovarský kraj'),
            array('id' => 13, 'run_countries_id' => 56, 'name' => 'Kraj Vysočina'),
            array('id' => 14, 'run_countries_id' => 56, 'name' => 'Královéhradecký kraj'),
            array('id' => 15, 'run_countries_id' => 56, 'name' => 'Liberecký kraj'),
            array('id' => 16, 'run_countries_id' => 56, 'name' => 'Moravskoslezský kraj'),
            array('id' => 17, 'run_countries_id' => 56, 'name' => 'Olomoucký kraj'),
            array('id' => 18, 'run_countries_id' => 56, 'name' => 'Pardubický kraj'),
            array('id' => 19, 'run_countries_id' => 56, 'name' => 'Plzeňský kraj'),
            array('id' => 20, 'run_countries_id' => 56, 'name' => 'Středočeský kraj'),
            array('id' => 21, 'run_countries_id' => 56, 'name' => 'Ústecký kraj'),
            array('id' => 22, 'run_countries_id' => 56, 'name' => 'Zlínský kraj'),
        );
    }
}