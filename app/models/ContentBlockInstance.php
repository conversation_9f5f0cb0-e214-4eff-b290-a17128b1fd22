<?php
/**
 * ContentBlockInstance class takes care of content blocks admin and frontend views
 * rendering and of and content blocks relations with owner models.
 * 
 * Definition of content blocks is in competence of ContentBlock model so see 
 * ContentBloc class phpDoc.
 * 
 * ***********************
 * Add a new content block
 * ***********************
 * 
 * This is in competence of ContentBlock model, see ContentBlock class phpDoc.
 * 
 * *********************************************
 * Explicit loading of content blocks views HTML
 * *********************************************
 * 
 * This is in competence of ContentBlock model, see ContentBlock class phpDoc.
 * 
 * *******************************************
 * Loading content blocks instances views HTML
 * *******************************************
 * 
 * To load views HTML of all content blocks instances belonging to some owner use:
 * 
 *      App::loadModel('App', 'ContentBlockInstance');
 *      $BlockInstance = new ContentBlockInstance();
 *      $viewHtml = $BlockInstance->loadOwnerInstancesViews({ownerModel}, {ownerId}, array(
 *          'ownerRecord' => {ownerRecord}
 *      ));
 * 
 * Content blocks are by default used for App.WebContent model. In this case you do not need
 * to do anything because content block views loading is already implemented in App::getContent().
 * In case that you need to load WebContent model content blocks views on your own
 * you can do it by:
 * 
 *      App::loadModel('App', 'ContentBlockInstance');
 *      $BlockInstance = new ContentBlockInstance();
 *      $viewHtml = $BlockInstance->loadOwnerInstancesViews('App.WebContent', $webContent['id'], array(
 *          'ownerRecord' => $webContent
 *      ));
 * 
 * If you have many web contents, you must do iteration and load content blocks views 
 * HTML separately for each one.
 * 
 * ***************************************
 * Add content blocks to other owner model
 * ***************************************
 * 
 * Content blocks are by default used for App.WebContent model - see 'contentBlocks' input 
 * in admin form of web content, loads of ContentBlockInstance model in WebContent 
 * model and ContentBlockInstance::loadOwnerInstancesViews() in App::getContent(). To implement 
 * content blocks for other owner models we must do the same actions as for App.WebContent.
 * E.g. if we would like to add content blocks to EshopProduct and display them 
 * in product detail as part of product description (or instead of description):
 * 
 * 1] Add FormHelper::contentBlocks() input to product admin form:
 * 
 *      $Form->contentBlocks(array(
 *          'ownerModel' => 'Eshop.EshopProduct',
 *          'ownerId' => Sanitize::value($data['id']),
 *          'contentBlockModels' => 'Eshop.*',
 *      );
 * 
 * If you create the input by FormHelper::input() or by Html::smartForm() then use
 * 'type' => 'contentBlocks' and set 'field' name to NULL.
 * 
 * ATTENTION: Content blocks previews are rendered by FormHelper::contentBlocks() 
 * in iframes using contentBlockPreview layout. This is usefull to set specific 
 * styles (inside body#content-block-preview-layout) for content blocks. E.g. for 
 * blocks with height set by css vh units (in iframe 100vh = iframe height = previewed 
 * block/page height) the height must be restricted by max-height in px units.
 * 
 * 2] Add ContentBlockInstance::saveOwnerInstancesData() to EshopProduct::saveAll():
 * 
 *      App::loadModel('App', 'ContentBlockInstance');
 *      $BlockInstance = new ContentBlockInstance();
 *      if (
 *          !$BlockInstance->saveOwnerInstancesData($data, 'Eshop.EshopProduct', $productId)
 *      ) {
 *          // ...potentionally unreserve tables/processing and/or rollback transaction here
 *          return false;
 *      }
 * 
 * 3] Add ContentBlockInstance::delete() to deletion logic of EshopProduct model:
 * 
 *      App::loadModel('App', 'ContentBlockInstance');
 *      $BlockInstance = new ContentBlockInstance();
 *      $BlockInstance->delete(array(
 *          'processContentBlockData' => true,
 *          'conditions' => array(
 *              'owner_model' => 'Eshop.EshopProduct',
 *              'owner_id' => $deletedProductIds,
 *          ),
 *      ));
 * 
 * 4] Add ContentBlockInstance::loadOwnerInstancesViews() to EshopProducts::view()
 *  (or to EshopProduct::getDetails()):
 * 
 *      App::loadModel('App', 'ContentBlockInstance');
 *      $BlockInstance = new ContentBlockInstance();
 *      $product['description'] .= $BlockInstance->loadOwnerInstancesViews('Eshop.EshopProduct', $id, array(
 *          'ownerRecord' => $product,
 *      ));
 *      // of course, generated content blocks can be also stored as a separate 
 *      // item in product data - it is up to you and your needs
 *      //$product['blocks'] .= $BlockInstance->loadOwnerInstancesViews('Eshop.EshopProduct', $id, array(
 *      //    'ownerRecord' => $product,
 *      //));
 * 
 * 5] If your model has also copy logic (e.g. see in WebContent::duplicate()) then use there
 *  ContentBlockInstance::copyInstance() to make copies of instances belonging to new
 *  record copy. In case of EshopProduct it could be something like:
 * 
 *      App::loadModel('App', 'ContentBlockInstance');
 *      $BlockInstance = new ContentBlockInstance();
 *      $instances = $BlockInstance->find(array(
 *          'conditions' => array(
 *              'owner_model' => 'Eshop.EshopProduct',
 *              'owner_id' => $originalProductId
 *          )
 *      ));
 *      foreach($instances as $instance) {
 *          $instance['owner_id'] = $copiedProductId; // id of product duplicate
 *          if (
 *              !$BlockInstance->copyInstance($instance)
 *          ) {
 *              $this->setErrors($BlockInstance);
 *              return false;
 *          }
 *      }
 * 
 * 6] Consider other places where you work with your model records...
 * 
 * 7] If you decide to not use EshopProduct.description field at all (whole 
 *  product description will be created by content blocks) then comment description
 *  input in product admin form and convert existing descriptions into html blocks
 *  (see screens/debug.php > convertWebContentTextsToHtmlContentBlocks or 
 *  convertEshopProductDescriptionsToHtmlContentBlocks)
 */
class ContentBlockInstance extends Model {
    
    protected $table = 'run_content_block_instances';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'owner_model' => array('type' => 'varchar', 'length' => 50, 'comment' => 'Block owner is always some record. This is the model name that record belongs to. To ensure uniquity the model name should be qualified by its module, e.g. \'App.WebContent\', \'Eshop.EshopProduct\'. If the content blocks are related only to some field then also the field name can be part of this name, e.g. \'Eshop.EshopProduct.description\''),
        'owner_id' => array('type' => 'int', 'comment' => 'Block owner is always some record. This is the id of that record'),
        'content_block_model' => array('type' => 'varchar', 'length' => 100, 'comment' => 'Content block definition model name. The model name must be qualified by its module, e.g. ContentBlocks.ContentBlockHtml.'),
        'content_block_data' => array('type' => 'text', 'comment' => 'Json encoded content block view custom data specified in content block definition'),
        'name' => array('type' => 'varchar'),
        'active' => array('type' => 'bool', 'default' => 0),
        'sort' => array('type' => 'int', 'default' => 0),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
//        'deleted' => array('type' => 'datetime', 'default' => null, 'index' => 'index'),
    );
    
    protected $tableIndexes = array(
        array(
            'type' => 'index',
            'fields' => array('owner_model', 'owner_id'),
            'name' => 'Owners index'
        ),
    );
    
    protected $contentBlockModelField = 'content_block_model';
    
    protected $nameField = 'name';
        
    protected $activeField = 'active';
    
    /**
     * Instance of content block definition class used to customize content block instance
     * 
     * @var ContentBlock
     */
    protected $ContentBlock = null;
    
    /**
     * Backup of initial ContentBlockInstance::$ContentBlock while replaced
     * 
     * @var array
     */
    protected $InitialContentBlock = null;
    
    /**
     * Backup of initial fileFields while fileFields are replaced by 
     * ContentBlockInstance::$ContentBlock::$fileFields
     * 
     * @var array
     */
    protected $initialFileFields = null;
    
    /**
     * Validation errors of ContentBlockInstance::$ContentBlock set by ContentBlockInstance::save().
     * Use ContentBlockInstance::getContentBlockErrors() to get errors.
     *
     * @var array
     */
    protected $contentBlockErrors = array();
        
    /**
     * Key under which are in owner data stored (sub)data of owner content block
     * instances. Populated in constructor.
     * 
     * @var string
     */
    protected $dataKey = null;
    
    /**
     * Creates new instance 
     * 
     * @param ContentBlock $Block Optional. Instance of content block model used 
     *          to customize content block instance. If provided then used as default 
     *          value of ContentBlockInstance::$ContentBlock, it means if ::resetPropertyContentBlock()
     *          is called then ContentBlockInstance::$ContentBlock is set back to this value.
     */
    public function __construct(ContentBlock $Block = null) {
        parent::__construct();
        
        $this->initialFileFields = $this->fileFields;
        
        if (!empty($Block)) {
            $this->setPropertyContentBlock($Block);
        }
        $this->InitialContentBlock = $this->ContentBlock;
        
        if (empty($this->dataKey)) {
            $this->dataKey = $this->name;
        }
        
        $this->validations = array(      
            'owner_model' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Enter a value'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Enter a value'),
                ),
            ),     
            'owner_id' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Enter a value'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Enter a value'),
                ),
                array(
                    'rule' => 'intNumber',
                    'message' => __v(__FILE__, 'Enter an integer'),
                ),
            ),     
            'content_block_model' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Enter a value'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Enter a value'),
                ),
                array(
                    'rule' => function ($value, $name, $data, &$validation) {
                        $parts = explode('.', $value);
                        $valid = true;
                        if (count($parts) !== 2) {
                            $validation['message'] = __v(__FILE__, 'Invalid content block model "%s"', $value);
                            $valid = false;
                        }
                        elseif (!App::hasModule($parts[0])) {
                            $validation['message'] = __v(__FILE__, 'Invalid content block module "%s"', $parts[0]);
                            $valid = false;                            
                        }
                        elseif (!App::hasModel($parts[0], $parts[1])) {
                            $validation['message'] = __v(__FILE__, 'Invalid content block model "%s"', $value);
                            $valid = false;                            
                        }
                        return $valid;
                    },
                ),
            ),  
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Enter a value'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Enter a value'),
                ),
            ),  
        );
    }
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
            'processContentBlockData' => false,
        );
        $options = array_merge($defauts, $options);
        $options['alternative'] = (array)$options['alternative'];
        
        // if no name provided then normalize it to block name
        if (empty($data[$this->nameField])) {
            if (!empty($this->ContentBlock)) {
                $data[$this->nameField] = $this->ContentBlock->getPropertyBlockName();
            }
            elseif (!empty($data['content_block_model'])) {
                list($contentBlockModuleName, $contentBlockModelName) = explode(
                    '.', $data['content_block_model']
                );
                try {
                    $ContentBlock = App::loadModel(
                        $contentBlockModuleName, 
                        $contentBlockModelName, 
                        true
                    );
                    $data[$this->nameField] = $ContentBlock->getPropertyBlockName();
                } 
                catch (Throwable $e) {}
            }
        }
        
        // nullify in content block data all upload data which are not stored under
        // a fileField item. This can be caused by a forgotten file input of field 
        // (in admin_view) which has been removed from ContentBlock::$fileFields.
        // As the data are stored in json, the untreated array of upload data would
        // be stored as it is and cause a perplex situation. It is better to set them to NULL
        if (
            $options['processContentBlockData']
            && !empty($this->ContentBlock)
            && ($contentBlockFields = $this->ContentBlock->getPropertyFields())
        ) {
            $contentBlockFileFields = $this->ContentBlock->getPropertyFileFields();
            foreach ($data as $field => $value) {
                if (
                    isset($contentBlockFields[$field])
                    && !isset($contentBlockFileFields[$field])
                    && Validate::uploadData($value)
                ) {
                    $data[$field] = null;
                }
            }
        }
            
        return parent::normalize($data, $options);
    }
    
    /**
     * Sets ContentBlockInstance::$ContentBlock and accordingly related internal 
     * properties
     * 
     * @param ContentBlock $Block
     */
    public function setPropertyContentBlock(ContentBlock $Block) {
        $this->ContentBlock = $Block;
        $this->fileStore = $this->fileStoreRoot . 
            DS . $this->ContentBlock->getPropertyModule() . 
            DS . $this->ContentBlock->getPropertyName();
        $this->fileFields = $this->ContentBlock->getPropertyFileFields();
        $this->fileFieldOptions = array();
    }
    
    /**
     * Resets ContentBlockInstance::$ContentBlock back to its initial value 
     * and also accordingly resets related internal properties
     */
    public function resetPropertyContentBlock() {
        $this->ContentBlock = $this->InitialContentBlock;
        $this->fileStore = $this->fileStoreRoot . DS . $this->module . DS . $this->name;
        $this->fileFields = $this->initialFileFields;
        $this->fileFieldOptions = array();
    }
        
    /**
     * Returns value of ContentBlock::$dataKey property
     * 
     * @return string
     */
    public function getPropertyDataKey() {
        return $this->dataKey;
    }
    
    /**
     * Returns value of ContentBlock::$contentBlockModelField property
     * 
     * @return string
     */
    public function getPropertyContentBlockModelField() {
        return $this->contentBlockModelField;
    }
    
    /**
     * Returns value of ContentBlock::$activeField property
     * 
     * @return string
     */
    public function getPropertyActiveField() {
        return $this->activeField;
    }
    
    /**
     * Returns content block data error messsages for given field or 
     * all content block data error messages
     * 
     * @param string $field Optional. Content block data field name to get errors for.
     * 
     * @return array Array contaning validation error messages. The nesting of array
     *      can change according to input:
     *      - If $field name is provided then array contains just the messages of the field.
     *      - If the method is called without any input then the array contains only 
     *          current model errors
     * 
     *      If no messages then an empty array is returned.
     */
    public function getContentBlockErrors($field = null) {
        if (is_string($field)) {
            return (array)Sanitize::value($this->contentBlockErrors[$field]);
        }
        return (array)$this->contentBlockErrors;
    }
    
    /**
     * Returns array of content block model like:
     * 
     *      array(
     *          'ContentBlock.ContentBlockHtml',
     *          '{contentBlockModel}',
     *          ...
     *      )
     * 
     * @param array $options Following are available:
     *      - 'contentBlockModels' (string|array) Content blocks to create list for.
     *          They are specified by their definition qualified model name. 
     *          Array of many content blocks or string of single content block.
     *          E.g. 'App.HtmlContentBlock' or array('App', 'Eshop.MyContentBlock').
     *          You can use wildcard notations: '*' - to get all content blocks in project,
     *          '*.MyContentBlock' - to get all 'MyContentBlock'-s under any module,
     *          'MyModule.*' - to get all content blocks from 'MyModule'.
     *          Models MUST be qualified by module like {Module}.{Model}. Defaults to '*'.
     *      - 'avoidContentBlockModels' (string|array) Content blocks to be omitted in returned
     *          list. The specification posibilities are the same as for 'contentBlockModels'
     *          option. Defaults to NULL.
     * 
     * @return array The above specified array
     */
    public function getContentBlockModels($options = array()) {
        $defaults = array(
            'contentBlockModels' => '*',
            'avoidContentBlockModels' => null,
        );
        $options = array_merge($defaults, $options);
        $contentBlockModels = $options['contentBlockModels'];
        if (empty($contentBlockModels)) {
            $contentBlockModels = '*';
        }
        // validate and normalize
        $tmp = array();
        $contentBlockModels = (array)$contentBlockModels;
        $contentBlockParentClass = 'ContentBlock';
        App::loadModel('App', $contentBlockParentClass);
        foreach ($contentBlockModels as $contentBlockModel) {
            $contentBlockModel = trim($contentBlockModel);
            if ($contentBlockModel === '*') {
                $precedence = 0;
                $modules = App::getModules(array('initialized' => true));
                foreach ($modules as $module) {
                    $models = App::getModels($module);
                    foreach ($models as $model) {
                        try {
                            App::loadModel($module, $model);
                        } catch (Throwable $e) {
                            continue;
                        }
                        $Class = new ReflectionClass($model);
                        if ($Class->isAbstract() || !$Class->isSubclassOf($contentBlockParentClass)) {
                            continue;
                        }
                        $qualifiedModel = $module . '.' . $model;
                        if (
                            isset($tmp[$qualifiedModel])
                            && $tmp[$qualifiedModel] <= $precedence
                        ) {
                            unset($tmp[$qualifiedModel]);
                        }
                        $tmp[$qualifiedModel] = $precedence;
                    }
                }
            }
            elseif (
                ($contentBlockModelParts = explode('.', $contentBlockModel))
                && count($contentBlockModelParts) !== 2
            ) {
                throw new Exception(__e(__FILE__, 'Invalid contentBlockModels value "%s"', $contentBlockModel));
            }
            elseif ($contentBlockModelParts[0] === '*') {
                $precedence = 2;
                $modules = App::getModules(array('initialized' => true));
                $model = $contentBlockModelParts[1];
                foreach ($modules as $module) {
                    if (!App::hasModel($module, $model)) {
                        continue;
                    }
                    try {
                        App::loadModel($module, $model);
                    } catch (Throwable $e) {
                        continue;
                    }
                    $Class = new ReflectionClass($model);
                    if ($Class->isAbstract() || !$Class->isSubclassOf($contentBlockParentClass)) {
                        continue;
                    }
                    $qualifiedModel = $module . '.' . $model;
                    if (
                        isset($tmp[$qualifiedModel])
                        && $tmp[$qualifiedModel] <= $precedence
                    ) {
                        unset($tmp[$qualifiedModel]);
                    }
                    $tmp[$qualifiedModel] = $precedence;
                }
            }
            elseif ($contentBlockModelParts[1] === '*') {
                $precedence = 1;
                $module = $contentBlockModelParts[0];
                if (!App::hasModule($module)) {
                    throw new Exception(__e(__FILE__, 'Unexisting module "%s" in contentBlockModels specification "%s"', $module, $contentBlockModel));
                }
                if (!App::hasModule($module, array('initialized' => true))) {
                    throw new Exception(__e(__FILE__, 'Uninitialized module "%s" in contentBlockModels specification "%s"', $module, $contentBlockModel));
                }
                $models = App::getModels($module);
                foreach ($models as $model) {
                    try {
                        App::loadModel($module, $model);
                    } catch (Throwable $e) {
                        continue;
                    }
                    $Class = new ReflectionClass($model);
                    if ($Class->isAbstract() || !$Class->isSubclassOf($contentBlockParentClass)) {
                        continue;
                    }
                    $qualifiedModel = $module . '.' . $model;
                    if (
                        isset($tmp[$qualifiedModel])
                        && $tmp[$qualifiedModel] <= $precedence
                    ) {
                        unset($tmp[$qualifiedModel]);
                    }
                    $tmp[$qualifiedModel] = $precedence;
                }
            }
            else {
                $precedence = 3;
                $module = $contentBlockModelParts[0];
                $model = $contentBlockModelParts[1];
                if (!App::hasModel($module, $model)) {
                    throw new Exception(__e(__FILE__, 'Unexisting content block model "%s"', $contentBlockModel));
                }
                try {
                    App::loadModel($module, $model);
                } catch (Throwable $e) {
                    continue;
                }
                $Class = new ReflectionClass($model);
                if ($Class->isAbstract()) {
                    throw new Exception(__e(__FILE__, 'Invalid content block model specification "%s". It may not be an abstract class', $contentBlockModel));
                }
                if (!$Class->isSubclassOf($contentBlockParentClass)) {
                    throw new Exception(__e(
                        __FILE__, 
                        'Invalid content block model specification "%s". It must be an instance of "%s" class', 
                        $contentBlockModel, 
                        $contentBlockParentClass
                    ));
                }
                $qualifiedModel = $module . '.' . $model;
                if (
                    isset($tmp[$qualifiedModel])
                    && $tmp[$qualifiedModel] <= $precedence
                ) {
                    unset($tmp[$qualifiedModel]);
                }
                $tmp[$qualifiedModel] = $precedence;
            }
        }
        // remove models specified in avoidContentBlockModels 
        if ($options['avoidContentBlockModels']) {
            $contentBlockModels = (array)$options['avoidContentBlockModels'];
            foreach ($contentBlockModels as $contentBlockModel) {
                $contentBlockModel = trim($contentBlockModel);
                if ($contentBlockModel === '*') {
                    $tmp = array();
                }
                elseif (
                    ($contentBlockModelParts = explode('.', $contentBlockModel))
                    && count($contentBlockModelParts) !== 2
                ) {
                    throw new Exception(__e(__FILE__, 'Invalid avoidContentBlockModels value "%s"', $contentBlockModel));
                }
                elseif ($contentBlockModelParts[0] === '*') {
                    $modules = App::getModules();
                    $model = $contentBlockModelParts[1];
                    foreach ($modules as $module) {
                        $qualifiedModel = $module . '.' . $model;
                        unset($tmp[$qualifiedModel]);
                    }
                }
                elseif ($contentBlockModelParts[1] === '*') {
                    $module = $contentBlockModelParts[0];
                    if (!App::hasModule($module)) {
                        throw new Exception(__e(__FILE__, 'Unexisting module "%s" in avoidContentBlockModels specification "%s"', $module, $contentBlockModel));
                    }
                    $models = App::getModels($module);
                    foreach ($models as $model) {
                        $qualifiedModel = $module . '.' . $model;
                        unset($tmp[$qualifiedModel]);
                    }
                }
                else {
                    $module = $contentBlockModelParts[0];
                    $model = $contentBlockModelParts[1];
                    $qualifiedModel = $module . '.' . $model;
                    unset($tmp[$qualifiedModel]);
                }            
            }
        }
        $contentBlockModels = array_keys($tmp);
        return $contentBlockModels;
    }
    
    /**
     * Returns list of items which can be used as selectbox options like:
     * 
     *      array(
     *          'ContentBlock.ContentBlockHtml' => array(
     *              'name' => 'HTML',
     *              'description' => ...,
     *              'imageUrlPath' => ...,
     *          ),
     *          '{contentBlockModel}' => array(
     *              'name' => ...,
     *              'description' => ...,
     *              'imageUrlPath' => ...,
     *          ),
     *          ...
     *      )
     * 
     * If option 'plain' is set to TRUE then the returned list is:
     * 
     *      array(
     *          'ContentBlock.ContentBlockHtml' => 'HTML',
     *          '{contentBlockModel}' => {contentBlockName},
     *          ...
     *      )
     * 
     * @param array $options Following are available:
     *      - 'contentBlockModels' (string|array) Content blocks to create list for.
     *          They are specified by their definition qualified model name. 
     *          Array of many content blocks or string of single content block.
     *          E.g. 'App.HtmlContentBlock' or array('App', 'Eshop.MyContentBlock').
     *          You can use wildcard notations: '*' - to get all content blocks in project,
     *          '*.MyContentBlock' - to get all 'MyContentBlock'-s under any module,
     *          'MyModule.*' - to get all content blocks from 'MyModule'.
     *          Models MUST be qualified by module like {Module}.{Model}. Defaults to '*'.
     *      - 'avoidContentBlockModels' (string|array) Content blocks to be omitted in returned
     *          list. The specification posibilities are the same as for 'contentBlockModels'
     *          option. Defaults to NULL.
     *      - 'avoidContentBlockEndModels' (bool) If TRUE then content block end models
     *          are omitted in returned list and only start model are included. Defaults to FALSE.
     *      - 'plain' (bool) If TRUE the returned list contains only block names as values.
     *          Defaults to FALSE.
     *      - 'sort' (bool) If TRUE then returned models are ordered by name (ContentBlock::$blockName).
     *          Defaults to TRUE.
     * 
     * @return array The above specified array. Throws an exception on invalid content
     *      block specification
     */
    public function getContentBlocksList($options = array()) {
        $defaults = array(
            'avoidContentBlockEndModels' => false,
            'plain' => false,
            'sort' => true,
        );
        $options = array_merge($defaults, $options);
        $contentBlockModels = $this->getContentBlockModels($options);
        // create list
        $list = array();
        foreach ($contentBlockModels as $contentBlockModel) {
            list($contentBlockModuleName, $contentBlockModelName) = explode(
                '.', $contentBlockModel
            );
            $ContentBlock = App::loadModel(
                $contentBlockModuleName, 
                $contentBlockModelName, 
                true
            );
            if (
                !$ContentBlock->getPropertyAvailable()
                ||
                $options['avoidContentBlockEndModels']
                && $ContentBlock->isEndModel()
            ) {
                continue;
            }
            if (
                $options['avoidContentBlockEndModels']
                && $ContentBlock->isStartModel()
            ) {
                $name = $ContentBlock->getPropertyPairName();
            }
            else {
                $name = $ContentBlock->getPropertyBlockName();
            }
            if ($options['plain']) {
                $list[$contentBlockModel] = $name;
            }
            else {                
                $list[$contentBlockModel] = array(
                    'name' => $name,
                    'description' => $ContentBlock->getPropertyDescription(),
                    'imageUrlPath' => $ContentBlock->getPropertyImageUrlPath(),
                );
            }
        }
        if ($options['sort']) {
            $sortList = array();
            foreach ($list  as $contentBlockModel => $contentBlock) {
                if (is_array($contentBlock)) {
                    $name = $contentBlock['name'];
                }
                else {
                    $name = $contentBlock;
                }
                $sortList[$contentBlockModel] = $name;
            }
            Arr::sortStrings($sortList, array('associative' => true));
            $tmp = array();
            foreach ($sortList as $contentBlockModel => $name) {
                $tmp[$contentBlockModel] = $list[$contentBlockModel];
            }
            $list = $tmp;
        }
        return $list;
    }
    
    /**
     * Returns admin view data of specified content block
     * 
     * @param array $options Following are available:
     *      - 'id' (int) Content block instance id. If provided then used to resolve
     *          content block module & model and instance data (containing owner_model
     *          and owner_id) are loaded into view inputs and inputs are validated. 
     *          Defaults to NULL.
     *      - 'contentBlockModel' (string) New instance content block model. The model 
     *          name must be qualified by its module, e.g. ContentBlocks.ContentBlockHtml.
     *          Defaults to NULL.
     *      - 'contentBlockData' (array) Submitted data to be loaded into view 
     *          inputs and inputs are validated. They have the highest priority 
     *          (over default data and instance data). Defaults to NULL.
     *      - 'ownerModel' (string) Block owner is always some record. This is the model
     *          name that record belongs to. To ensure uniquity the model name should 
     *          be qualified by its module, e.g. 'App.WebContent', 'Eshop.EshopProduct'. 
     *          If the content blocks are related only to some field then also the field 
     *          name can be part of this name, e.g. 'Eshop.EshopProduct.description'.
     *          If provided then used as value fo 'owner_model' field in internally resolved
     *          instance data. It is used only in case that there is no 'owner_model'
     *          field in instance data yet (or it is empty). Must be provided if
     *          'ownerId' is provided (otherwise an exception is thrown). 
     *          Defaults to NULL.
     *      - 'ownerId' (int) Block owner is always some record. This is the id of that record.
     *          If provided then used as value fo 'owner_id' field in internally resolved
     *          instance data. It is used only in case that there is no 'owner_id'
     *          in instance data yet (or it is empty). If provided then also
     *          'ownerModel' must be provided (otherwise an exception is thrown). 
     *          Defaults to NULL.
     * 
     * @return array Admin view data containing string items 'name', 'description',
     *      'imageUrlPath', 'previewable', 'originalName', 'model', 'active', 'endModel' 
     *      and 'viewHtml'. Throws exception on content block or view loading failure
     * 
     * @throws Exception
     */
    public function loadAdminView($options = array()) {
        $defaults = array(
            'id' => null,
            'contentBlockModel' => null,
            'contentBlockData' => null,
            'ownerModel' => null,
            'ownerId' => null,
        );
        $options = array_merge($defaults, $options);
        
        if (
            !empty($options['ownerId'])
            && empty($options['ownerModel'])
        ) {
            throw new Exception(__e(__FILE__, 'Both ownerModel and ownerId must be provided'));
        }
        
        $instanceForeignKey = $this->getForeignKey();
        $contentBlockModelField = $this->contentBlockModelField;
        
        if (!empty($options['id'])) {
            $instance = $this->findFirstBy('id', $options['id'], array(
                'fields' => array(
                    $contentBlockModelField,
                    'content_block_data',
                )
            ));
            if (empty($instance)) {
                throw new Exception(__e(__FILE__, 'Unexisting content block instance id "%s"', $options['id']));
            }
            if (empty($options['contentBlockModel'])) {
                $options['contentBlockModel'] = $instance[$contentBlockModelField];
            }
            elseif ($options['contentBlockModel'] !== $instance[$contentBlockModelField]) {
                throw new Exception(__e(__FILE__, 'Content block models do not match: "%s" vs "%s"', $options['contentBlockModel'], $instance[$contentBlockModelField]));
            }
        }
        elseif (empty($options['contentBlockModel'])) {
            throw new Exception(__e(__FILE__, 'Specify either content block instance id or content block module & model in options'));
        }
        
        list($contentBlockModuleName, $contentBlockModelName) = explode(
            '.', $options['contentBlockModel']
        );
        $ContentBlock = App::loadModel(
            $contentBlockModuleName, 
            $contentBlockModelName, 
            true
        );
        // resolve & prepare data
        $data = array();
        $defaultData = $ContentBlock->getPropertyFields();
        // - form data
        if (!empty($options['contentBlockData'])) {
            $data = (array)$options['contentBlockData'];
            if (!empty($data[$instanceForeignKey])) {
                $options['id'] = $data[$instanceForeignKey];
            }
            // validate all custom fields in submitted data
            $validateFields = array_keys($defaultData);
        }
        // - instance data
        elseif (!empty($options['id'])) {
            if (
                ($instanceData = $this->findFirstBy('id', $options['id']))
            ) {
                $data = array_merge(
                    $instanceData, 
                    (array)json_decode($instanceData['content_block_data'], true)
                );
            }
            else {
                $data = $defaultData;
            }
            // avoid validation of file fields for data retrieved from DB
            $validateFields = array_keys(array_diff_key(
                $defaultData,
                $ContentBlock->getPropertyFileFields()
            ));
        }
        // - default data
        else {
            $data = $defaultData;
            $data[$this->activeField] = $this->schema[$this->activeField]['default'];
            $data[$this->nameField] = $ContentBlock->getPropertyBlockName();
            // avoid validation of any fields for default data
            $validateFields = null;
        }
        // add owner model and id to data if provided and not present in data yet
        if (
            !empty($options['ownerModel'])
            && !empty($options['ownerId'])
            && empty($data['owner_model'])
            && empty($data['owner_id'])
        ) {
            $data['owner_model'] = $options['ownerModel'];
            $data['owner_id'] = $options['ownerId'];
        }
        // - validate data
        if (!empty($validateFields)) {
            $on = empty($data[$instanceForeignKey]) ? 'create' : 'update';
            $ContentBlock->validate($data, array(
                'allowFields' => $validateFields,
                'alternative' => $ContentBlock->getPropertyFieldsValidationAlternative(),
                'on' => $on,
            ));
        }
        // - ensure block instance foreign key in data
        if (!empty($options['id'])) {
            $data[$instanceForeignKey] = $options['id'];
        }
        // - prepare data
        $data = $ContentBlock->prepareAdminViewData(
            $data, 
            !empty($options['contentBlockData'])
        );
        try {
            App::loadLib('App', 'FormHelper');
            $Form = new FormHelper(array(
                'data' => $data,
                'errors' => $ContentBlock->getErrors(),
                'required' => $ContentBlock->getNotEmptyFields(array(
                    'data' => $options['contentBlockData'],
                )),
                'useDataWrapper' => 'data[' . $this->dataKey . '][' . uniqid() . ']',
                'compatibility' => 'bootstrap',
            ));
            $view = App::loadView(
                $contentBlockModuleName,
                $ContentBlock->getPropertyAdminView(), 
                array(
                    'Form' => $Form,
                    'data' => $data,
                )
            );
        } 
        catch (Throwable $e) {
            throw new Exception(
                __e(__FILE__, 'Content block admin view load has failed with following error: %s', $e->getMessage()), 
                null, 
                $e
            );
        }
        if (!empty($options['id'])) {
            $view .= $Form->hidden($instanceForeignKey, array(
                'explicitValue' => $options['id'],
            ));
        }
        else {
            $view .= $Form->hidden($contentBlockModelField, array(
                'explicitValue' => $options['contentBlockModel'],
            ));
        }
        $view .= $Form->hidden($this->nameField);
        $view .= $Form->hidden($this->activeField);
        // if allowed then on localhost generate originComment
        if (
            App::getPropertyAllowOriginComments()
            && ON_LOCALHOST
            && !empty($view)
        ) {            
            $view = 
                '<!--  ' . $contentBlockModuleName . DS . 'models' . DS . $contentBlockModelName . ' -->' 
                . $view
                . '<!--/ ' . $contentBlockModuleName . DS . 'models' . DS . $contentBlockModelName . ' -->';
        }
        return array(
            'name' => $data[$this->nameField],
            'description' => $ContentBlock->getPropertyDescription(),
            'imageUrlPath' => $ContentBlock->getPropertyImageUrlPath(),
            'previewable' => $ContentBlock->getPropertyPreviewable(),
            'originalName' => $ContentBlock->getPropertyBlockName(),
            'model' => $contentBlockModuleName . '.' . $contentBlockModelName,
            'active' => $data[$this->activeField],
            'startModel' => $ContentBlock->isEndModel() ? 
                $ContentBlock->getStartModelAlias(array('moduleAlias' => true)) : null,
            'endModel' => $ContentBlock->isStartModel() ? 
                $ContentBlock->getEndModelAlias(array('moduleAlias' => true)) : null,
            'viewHtml' => $view,
        );
    }
    
    /**
     * Returns content blocks instances admin view data of specified owner
     * 
     * @param string $ownerModel Block owner is always some record. This is the model
     *          name that record belongs to. To ensure uniquity the model name should 
     *          be qualified by its module, e.g. 'App.WebContent', 'Eshop.EshopProduct'. 
     *          If the content blocks are related only to some field then also the field 
     *          name can be part of this name, e.g. 'Eshop.EshopProduct.description'
     * @param int $ownerId Block owner is always some record. This is the id of that record
     * @param int $formData Optional. Submitted data of form used to edit instances.
     *          If provided then admin views are created according these data.
     * 
     * @return array Array of admin views data. Each item itself is an array containing 
     *      string items 'name', 'description', 'imageUrlPath', 'previewable', 'originalName', 
     *      'model', 'active', 'endModel' and 'viewHtml'. Throws exception on block or 
     *      view loading failure
     * 
     * @throws Exception
     */
    public function loadOwnerInstancesAdminViews($ownerModel, $ownerId, $formData = null) {
        $instanceForeignKey = $this->getForeignKey();
        $contentBlockModelField = $this->contentBlockModelField;
        // get instances data from form data (on form POST)
        if (!empty($formData[$this->dataKey])) {
            $instanceIds = array();
            foreach ($formData[$this->dataKey] as $instance) {
                if (!empty($instance[$instanceForeignKey])) {
                    $instanceIds[] = $instance[$instanceForeignKey];
                }
            }
            $instancesModels = $this->findList(array(
                'key' => 'id',
                'fields' => array($contentBlockModelField),
                'conditions' => array(
                    'owner_model' => $ownerModel,
                    'owner_id' => $ownerId,
                    'id' => $instanceIds,
                )
            ));
            $instances = array();
            foreach ($formData[$this->dataKey] as $instance) {
                if (!empty($instance[$contentBlockModelField])) {
                    $instances[] = array(
                        'contentBlockModel' => $instance[$contentBlockModelField],
                        'contentBlockData' => $instance,
                        'ownerModel' => $ownerModel,
                        'ownerId' => $ownerId,
                    );
                }
                elseif (
                    !empty($instance[$instanceForeignKey])
                    && !empty($instancesModels[$instance[$instanceForeignKey]])
                ) {
                    $instances[] = array(
                        'contentBlockModel' => $instancesModels[$instance[$instanceForeignKey]],
                        'contentBlockData' => $instance,
                        'ownerModel' => $ownerModel,
                        'ownerId' => $ownerId,
                    );
                }
            }
        }
        // get owner instances data (on form GET)
        else {
            $instances = $this->find(array(
                'conditions' => array(
                    'owner_model' => $ownerModel,
                    'owner_id' => $ownerId,
                ),
                'fields' => array(
                    $contentBlockModelField . ' AS contentBlockModel',
                    'id',
                ),
                'order' => 'sort ASC'
            ));
        }
        // get instances views
        $views = array();
        foreach ($instances as $instance) {
            $views[] = $this->loadAdminView($instance);
        }
        return $views;
    }
    
    /**
     * Saves submited instances data for specified owner
     * 
     * @param array $data
     * @param string $ownerModel Block owner is always some record. This is the model
     *          name that record belongs to. To ensure uniquity the model name should 
     *          be qualified by its module, e.g. 'App.WebContent', 'Eshop.EshopProduct'. 
     *          If the content blocks are related only to some field then also the field 
     *          name can be part of this name, e.g. 'Eshop.EshopProduct.description'
     * @param int $ownerId Block owner is always some record. This is the id of that record
     * 
     * @return boolean|array
     * 
     * @throws Exception
     */
    public function saveOwnerInstancesData($data, $ownerModel, $ownerId) {
        if (!array_key_exists($this->dataKey, $data)) {
            return $data;
        }
        // this happens in case that there are no content blocks in input
        if (empty($data[$this->dataKey])) {
            $data[$this->dataKey] = array();
        }
        elseif (!is_array($data[$this->dataKey])) {
            throw new Exception(__e(__FILE__, 'Invalid content block instances data. Data must be an array'));
        }
        // prepare, validate & normalize content block & instance data
        $instanceForeignKey = $this->getForeignKey();
        $contentBlockModelField = $this->contentBlockModelField;
        // get all existing instance content block models by one request
        $instanceIds = array();
        foreach ($data[$this->dataKey] as $instanceData) {
            if (!empty($instanceData[$instanceForeignKey])) {
                $instanceIds[] = $instanceData[$instanceForeignKey];
            }
        }
        $contentBlockModels = array();
        if ($instanceIds) {
            $contentBlockModels = $this->findList(array(
                'fields' => array($contentBlockModelField),
                'conditions' => array('id' => $instanceIds)
            ));
        }
        $sort = 0;
        $ContentBlocks = array();
        foreach ($data[$this->dataKey] as $i => &$instanceData) {
            $contentBlockModel = $instanceId = null;
            if (!empty($instanceData[$contentBlockModelField])) {
                $contentBlockModel = $instanceData[$contentBlockModelField];
            }
            elseif (!empty($instanceData[$instanceForeignKey])) {
                $instanceId = $instanceData[$instanceForeignKey];
                $contentBlockModel = Sanitize::value($contentBlockModels[$instanceId]);
                if (!$contentBlockModel) {
                    if ($contentBlockModel === null) {
                        throw new Exception(__e(__FILE__, 'Invalid content block instances data. Content block instance id %s does not exist', $instanceId));
                    }
                    else {
                        throw new Exception(__e(__FILE__, 'Invalid content block instances data. Content block instance id %s has no content block model defined', $instanceId));
                    }
                }
            }
            else {
                throw new Exception(__e(__FILE__, 'Invalid content block instances data. Data must contain either content block model or content block instance foreign key'));
            }
            list($contentBlockModuleName, $contentBlockModelName) = explode(
                '.', $contentBlockModel
            );
            $ContentBlock = App::loadModel(
                $contentBlockModuleName, 
                $contentBlockModelName, 
                true
            );
            $ContentBlocks[$i] = $ContentBlock;
            $on = empty($instanceId) ? 'create' : 'update';
            if (
                !$ContentBlock->validate($instanceData, array(
                    'alternative' => $ContentBlock->getPropertyFieldsValidationAlternative(),
                    'on' => $on,
                ))
            ) {
                return false;
            }
            $instanceData['id'] = $instanceId;
            $instanceData['owner_model'] = $ownerModel;
            $instanceData['owner_id'] = $ownerId;
            $instanceData[$contentBlockModelField] = $contentBlockModel;
            $instanceData['sort'] = $sort++;
            if (
                !$this->validate($instanceData, array(
                    'on' => $on,
                ))
            ) {
                $errors = json_encode($this->getErrors());
                throw new Exception(__e(__FILE__, 'Save of content block instance data has failed with following validation errors: %s', $errors));
            }
        }
        unset($instanceData);
        
        // save instance data and delete obsolete instances
        $savedInstanceIds = array();
        DB::startTransaction('ContentBlockInstance_saveOwnerInstancesData');
        try {
            $this->reserveTable('ContentBlockInstance_saveOwnerInstancesData');
            $startData = array();
            foreach ($data[$this->dataKey] as $i => &$instanceData) {
                /* @var $ContentBlock ContentBlock */
                $ContentBlock = $ContentBlocks[$i];
                if ($ContentBlock->isStartModel()) {
                    $startModelAlias = $ContentBlock->getAlias(array(
                        'moduleAlias' => true
                    ));
                    if (empty($startData[$startModelAlias])) {
                        $startData[$startModelAlias] = array();
                    }
                    array_push($startData[$startModelAlias], $instanceData);
                }
                elseif ($ContentBlock->isEndModel()) {
                    $startModelAlias = $ContentBlock->getStartModelAlias(array(
                        'moduleAlias' => true
                    ));
                    if (!empty($startData[$startModelAlias])) {
                        $startDataField = $ContentBlock->getPropertyStartDataField();
                        $instanceData[$startDataField] = array_pop($startData[$startModelAlias]);
                    }
                }
                $this->setPropertyContentBlock($ContentBlock);
                $instanceData = $this->save($instanceData, array(
                    'validate' => false,
                    'processContentBlockData' => true,
                    'reserve' => false,
                ));
                $savedInstanceIds[$this->id] = true;
            }
            unset($instanceData);
            $this->resetPropertyContentBlock();
            // delete all unsaved (obsolete) instances
            $existingInstances = $this->find(array(
                'fields' => array(
                    'id',
                    $contentBlockModelField,
                ),
                'conditions' => array(
                    'owner_model' => $ownerModel,
                    'owner_id' => $ownerId,
                ),
            ));
            foreach ($existingInstances as $instanceData) {
                // if instance is not among saved instances then it is obsolete
                if (empty($savedInstanceIds[$instanceData['id']])) {
                    list($contentBlockModuleName, $contentBlockModelName) = explode(
                        '.', $instanceData[$contentBlockModelField]
                    );
                    $ContentBlock = App::loadModel(
                        $contentBlockModuleName, 
                        $contentBlockModelName, 
                        true
                    );
                    $this->setPropertyContentBlock($ContentBlock);
                    $this->deleteBy('id', $instanceData['id']);
                }
            }
            $this->resetPropertyContentBlock();
        } 
        catch (Throwable $e) {
            $this->unreserveTables('ContentBlockInstance_saveOwnerInstancesData');
            DB::rollbackTransaction('ContentBlockInstance_saveOwnerInstancesData');
            throw $e;
        }
        $this->unreserveTables('ContentBlockInstance_saveOwnerInstancesData');
        DB::commitTransaction('ContentBlockInstance_saveOwnerInstancesData');
        
        return $data;
    }
    
    /**
     * Deletes file of specified file field and content block instance
     * 
     * @param string $fileField
     * @param int $id Content block instance id
     * 
     * @return boolean TRUE on success. FALSE on invalid $fileField or $id
     */
    public function deleteInstanceFile($fileField, $id) {
        $contentBlockModelField = $this->contentBlockModelField;
        $instance = $this->findFirst(array(
            'fields' => array(
                $contentBlockModelField,
            ),
            'conditions' => array(
                'id' => $id,
            ),
        ));
        if (
            empty($instance)
            || empty($instance[$contentBlockModelField])        
        ) {
            return false;
        }
        list($contentBlockModuleName, $contentBlockModelName) = explode(
            '.', $instance[$contentBlockModelField]
        );
        $ContentBlock = App::loadModel(
            $contentBlockModuleName, 
            $contentBlockModelName, 
            true
        );
        $fileFields = $ContentBlock->getPropertyFileFields();
        if (!isset($fileFields[$fileField])) {
            return false;
        }
        $this->setPropertyContentBlock($ContentBlock);
        $this->save(
            array(
                'id' => $id, 
                $fileField => ''
            ),
            array(
                'normalize' => false, 
                'validate' => false,
                'processContentBlockData' => true,
            )
        );
        $this->resetPropertyContentBlock();
        return true;
    }
    
    /**
     * Returns view html of specified content block instance
     * 
     * @param int|array $instance Block instance id integer or an array of
     *      block instance record containing at least fields 'id', 'content_block_model' 
     *      and 'content_block_data'. 
     * @param array $options Following are available:
     *      - 'ownerModel' (string) Block owner model to be passed to method
     *          ContentBlock::prepareViewData(). To ensure uniquity the model name should 
     *          be qualified by its module, e.g. 'App.WebContent', 'Eshop.EshopProduct'. 
     *          It is up to each ContentBlock definition how this is used/processed 
     *          or maybe ignored. Defaults to NULL.
     *      - 'ownerId' (string) Block owner id to be passed to method
     *          ContentBlock::prepareViewData(). It is up to each ContentBlock definition
     *          how this is used/processed or maybe ignored. Defaults to NULL.
     *      - 'ownerRecord' (array) Block owner record data to be passed to method
     *          ContentBlock::prepareViewData(). It is up to each ContentBlock definition
     *          how this is used/processed or maybe ignored. Defaults to NULL.
     * 
     * @return string View html. Throws exception on invalid instance data and block 
     *      instance or view loading failure
     * 
     * @throws Exception
     */    
    public function loadInstanceView($instance, $options = array()) {
        $defaults = array(
            'ownerModel' => null,
            'ownerId' => null,
            'ownerRecord' => null,
        );
        $options = array_merge($defaults, $options);
        $contentBlockModelField = $this->contentBlockModelField;
        if (is_array($instance)) {
            $mandatoryFields = array(
                'id' => true, 
                $contentBlockModelField => true, 
                'content_block_data' => true
            );
            if (($missingFields = array_diff_key($mandatoryFields, $instance))) {
                $missingFields = implode(', ', array_keys($missingFields));
                throw new Exception(__e(__FILE__, 'Insufficient content block instance data. Following fields are missing: %s', $missingFields));
            }
            $id = $instance['id'];
        }
        else {            
            $id = $instance;
            $instance = $this->findFirst(array(
                'fields' => array(
                    $contentBlockModelField,
                    'content_block_data',
                ),
                'conditions' => array(
                    'id' => $id,
                ),
            ));
        }
        if (empty($instance)) {
            throw new Exception(__e(__FILE__, 'Unexisting content block instance id "%s"', $id));
        }
        if (empty($instance[$contentBlockModelField])) {
            throw new Exception(__e(__FILE__, 'Invalid content block assigned to instance id "%s"', $id));
        }
        list($contentBlockModuleName, $contentBlockModelName) = explode(
            '.', $instance[$contentBlockModelField]
        );
        $ContentBlock = App::loadModel(
            $contentBlockModuleName, 
            $contentBlockModelName, 
            true
        );
        $data = (array)json_decode($instance['content_block_data'], true);
        $data[$this->getForeignKey()] = $id;
        try {
            $view = $ContentBlock->loadView($data, $options);
        } 
        catch (Throwable $e) {
            throw new Exception(
                __e(__FILE__, 'Content block view load has failed with following error: %s', $e->getMessage()), 
                null, 
                $e
            );
        }
        // if allowed then on localhost generate originComment
        if (
            App::getPropertyAllowOriginComments()
            && ON_LOCALHOST
            && !empty($view)
        ) {            
            $view = 
                '<!--  ' . $contentBlockModuleName . DS . 'models' . DS . $contentBlockModelName . ' -->' 
                . $view
                . '<!--/ ' . $contentBlockModuleName . DS . 'models' . DS . $contentBlockModelName . ' -->';
        }
        return $view;
    }    
    
    /**
     * Returns content block instances views html of specified owner
     * 
     * @param string $ownerModel Block owner is always some record. This is the model
     *          name that record belongs to. To ensure uniquity the model name should 
     *          be qualified by its module, e.g. 'App.WebContent', 'Eshop.EshopProduct'. 
     *          If the content blocks are related only to some field then also the field 
     *          name can be part of this name, e.g. 'Eshop.EshopProduct.description'
     * @param int $ownerId Block owner is always some record. This is the id of that record
     * @param array $options Following are available:
     *      - 'ownerRecord' (array) Block owner record data to be passed to method
     *          ContentBlock::prepareViewData(). It is up to each ContentBlock definition
     *          how this is used/processed or maybe ignored. Defaults to NULL.
     * 
     *      Any other options can be passed here and will be passed to ContentBlock::prepareViewData().
     * 
     * @return string
     */
    public function loadOwnerInstancesViews($ownerModel, $ownerId, $options = array()) {
        $defaults = array(
            'ownerRecord' => null,
        );
        $options = array_merge($defaults, $options);
        $options['ownerModel'] = $ownerModel;
        $options['ownerId'] = $ownerId;
        $contentBlockModelField = $this->contentBlockModelField;
        $instances = $this->find(array(
            'fields' => array(
                'id',
                $contentBlockModelField,
                'content_block_data',
            ),
            'conditions' => array(
                'owner_model' => $ownerModel,
                'owner_id' => $ownerId,
                $this->activeField => true,
            ),
            'order' => 'sort ASC'
        ));
        // get instances views html
        $viewsHtml = '';
        foreach ($instances as $instance) {
            $viewsHtml .= $this->loadInstanceView($instance, $options);
        }
        return $viewsHtml;        
    }   
    
    /**
     * Saves provided data. If data contains a primary key field then the given record is
     * updated. If data does not contain primary key or primary key has an empty value
     * then a new record is created.
     * 
     * Only one record can be provided and only one model data can be saved. 
     * Translations are done automatically.
     * 
     * @param array $data
     * @param array $options See options of Model::save() plus following:
     *      - 'processContentBlockData' (bool) If TRUE then content block data are 
     *          extracted from instance data, possiblly normalized and validated
     *          and after being json encoded they are saved in content_block_data
     *          field. Condidered only if ContentBlockInstance::$ContentBlock is set
     *          and has nonempty fields. Defaults to FALSE.
     *      - 'normalizeContentBlockData' (bool) Considered only if 'processContentBlockData'
     *          is set (and considered - see there). Defaults to NULL, it means its value is
     *          inherited from 'normalize' option.
     *      - 'validateContentBlockData' (bool) Considered only if 'processContentBlockData'
     *          is set (and considered - see there). Defaults to NULL, it means its value is
     *          inherited from 'validate' option.
     * 
     * @return array|bool Array of updated or created record data containing id of created record
     *      and other values after normalization or file uploads. FALSE if validation fails.  
     * 
     * @throws Exception_DB_TablesReservationFailure
     * @throws Exception on file tranfer/transform failure or DB request failure
     */
    public function save($data, $options = array()) {
        $defaults = array(
            'normalize' => true,
            'validate' => true,
            'processContentBlockData' => false,
            'normalizeContentBlockData' => null,
            'validateContentBlockData' => null,
        );
        $options = array_merge($defaults, $options);
        // validate and normalize content block data
        if (($processContentBlockData = (
            $options['processContentBlockData']
            && !empty($this->ContentBlock)
            && ($contentBlockFields = $this->ContentBlock->getPropertyFields())
        ))) {
            if ($options['normalizeContentBlockData'] === null) {
                $options['normalizeContentBlockData'] = $options['normalize'];
            }
            if ($options['validateContentBlockData'] === null) {
                $options['validateContentBlockData'] = $options['validate'];
            }
            $options['on'] = null; // to make Model::isCreation() resolve correctly
            $options['create'] = $this->isCreation($data, $options); 
            $options['on'] = $options['create'] ? 'create' : 'update';
            $validationAlternative = $this->ContentBlock->getPropertyFieldsValidationAlternative();
            // normalize
            if ($options['normalizeContentBlockData']) {
                $data = $this->ContentBlock->normalize($data, array_merge($options, array(
                    'alternative' => $validationAlternative,
                )));
                // if once normalized, turn it off (to not make it duplicitly during validation)
                $options['normalizeContentBlockData'] = false;
            }
            // validate
            if (
                $options['validateContentBlockData'] 
                && !$this->ContentBlock->validate($data, array_merge($options, array(
                    'normalize' => $options['normalizeContentBlockData'],
                    'alternative' => $validationAlternative,
                )))
            ) {
                $this->contentBlockErrors = $this->ContentBlock->getErrors();
                return false;
            }
            DB::startTransaction('ContentBlockInstance_save');
        }
        // save this model data
        if (!($result = parent::save($data, $options))) {
            if ($processContentBlockData) {
                DB::rollbackTransaction('ContentBlockInstance_save');
            }
            return false;
        }
        // save content block data
        if ($processContentBlockData) {
            $contentBlockData = array($this->primaryKey => $this->id);
            if ($options['create']) {
                $contentBlockData['content_block_data'] = array_intersect_key(
                    $result, 
                    $contentBlockFields
                );
            }
            else {
                $oldContentBlockData = json_decode(
                    $this->findFieldBy('content_block_data', $this->primaryKey, $this->id),
                    true
                );
                $contentBlockData['content_block_data'] = array_merge(
                    (array)array_intersect_key(
                    $oldContentBlockData, 
                        $contentBlockFields
                    ),
                    (array)array_intersect_key(
                        $result, 
                        $contentBlockFields
                    )
                );
            }
            $contentBlockData['content_block_data'] = json_encode(
                $contentBlockData['content_block_data'],
                JSON_UNESCAPED_UNICODE
            );
            parent::save($contentBlockData, array(
                'validate' => false,
                'normalize' => false,
            ));
            $result['content_block_data'] = $contentBlockData['content_block_data'];
            DB::commitTransaction('ContentBlockInstance_save');
        }
        return $result;
    }
    
    /**
     * Deletes all records according to provided conditions.
     * 
     * ATTENTION: There are two ways how to cleaned up files belonging to content 
     * block models fileFields:
     *  - set options 'processContentBlockData' to TRUE (see option doc)
     *  - in case of single instance delete you can use following pattern for optimization
     *      purposes (see e.g. in ContentBlockInstance::saveOwnerInstanceData()):
     * 
     *          $ContentBlockInstance->setPropertyContentBlock($MyContentBlock);
     *          $ContentBlockInstance->deleteBy('id', {myContentBlockInstanceId});
     * 
     * ATTENTION: If option 'processContentBlockData' is not set TRUE then files
     * belonging to content block models fileFields are not cleaned up and will stay
     * orphaned.
     * 
     * @param array $options Options of Model::delete() plus following:
     *      - 'processContentBlockData' (bool) If TRUE then files of stored in content_block_data
     *          are cleaned up. Internally is found content_block_model of each deleted record
     *          and fileFields options are set accordingly. Defaults to FALSE.
     * 
     * @return bool TRUE on success
     * 
     * @throws Exception_DB_RecoverableDeadlock
     * @throws Exception_DB_TablesReservationFailure
     */
    public function delete($options = array()) {
        $defaults = array(
            'processContentBlockData' => false,
            'cleanUpFiles' => true,
            'softDelete' => true,
        );
        $options = array_merge($defaults, $options);
        if (
            $options['processContentBlockData']
            && $options['cleanUpFiles']
            && (
                !$this->allowsSoftDelete
                || !$options['softDelete']
            )
        ) {
            $findOptions = $options;
            $findOptions['key'] = $this->getAlias($findOptions) . '.' . $this->contentBlockModelField;
            $findOptions['fields'] = $this->getAlias($findOptions) . '.' . $this->primaryKey;
            $findOptions['qualify'] = false;
            $findOptions['inflate'] = false;
            $findOptions['separate'] = false;
            $findOptions['accumulate'] = true;
            $instances = $this->findList($findOptions);
            foreach ($instances as $contentBlockModel => $ids) {
                list($contentBlockModuleName, $contentBlockModelName) = explode(
                    '.', $contentBlockModel
                );
                $ContentBlock = App::loadModel(
                    $contentBlockModuleName, 
                    $contentBlockModelName, 
                    true
                );
                $this->setPropertyContentBlock($ContentBlock);
                parent::deleteBy($this->primaryKey, $ids);
            }
            $this->resetPropertyContentBlock();
            return true;
        }
        else {
            return parent::delete($options);
        }
    }
    
    /**
     * Finds all records according to given options. 
     * 
     * ATTENTION: If you use ContentBlockInstance::find() on customized instance
     * (ContentBlockInstance::$ContentBlock is set) then you can search only for 
     * compatible records having the content_block_model the same as ContentBlockInstance::$ContentBlock
     * 
     * @param array $options See Model::find()
     *          
     * @return array|resource Array of retrieved record(s) or a resource if required
     */
    public function find($options = array()) {
        $hasContentBlockFields = false;
        if (
            !empty($this->ContentBlock)
            && ($contentBlockFields = $this->ContentBlock->getPropertyFields())
            && !empty($options['fields'])
        ) {
            $options['fields'] = (array)$options['fields'];
            $contentBlockFields = array_keys($contentBlockFields);
            if (array_intersect($options['fields'], $contentBlockFields)) {
                $hasContentBlockFields = true;
                $options['fields'] = array_diff($options['fields'], $contentBlockFields);
                $options['fields'][] = 'content_block_data';
                $options['fields'][] = 'content_block_model';
                $options['fields'] = array_unique($options['fields']);
            }
        }
        $result = parent::find($options);
        if (
            $hasContentBlockFields
            && !empty($result)
        ) {
            $contentBlockModel = $this->ContentBlock->getPropertyModule() . '.' . 
                $this->ContentBlock->getPropertyName();
            $emptyContentBlockData = array();
            foreach ($contentBlockFields as $field) {
                $emptyContentBlockData[$field] = '';
            }
            if (!empty($options['first'])) {
                if ($result['content_block_model'] !== $contentBlockModel) {
                    throw new Exception(__e(__FILE__, 'If you use ContentBlockInstance::find() on customized instance (ContentBlockInstance::$ContentBlock is set) then you can search only for compatible records having the content_block_model the same as ContentBlockInstance::$ContentBlock'));
                }
                $result = array_merge($result, $emptyContentBlockData, json_decode($result['content_block_data'], true));
            }
            else {
                foreach ($result as &$record) {
                    if ($record['content_block_model'] !== $contentBlockModel) {
                        throw new Exception(__e(__FILE__, 'If you use ContentBlockInstance::find() on customized instance (ContentBlockInstance::$ContentBlock is set) then you can search only for compatible records having the content_block_model the same as ContentBlockInstance::$ContentBlock'));
                    }
                    $record = array_merge($record, $emptyContentBlockData, json_decode($record['content_block_data'], true));
                }
                unset($record);
            }
        }
        return $result;
    }    
    
    /**
     * Creates a new copy of provided content block $instance.
     * 
     * @param int|array $instance Block instance id integer or an array of
     *          block instance record data containing all fields. Fields 'id', 'sort' 
     *          and timestamp files ('created', ...) are not required. 
     * @param array $options Model::save() options plus following:
     *          - 'copyData' (array|callable) Array of data used to prepare copied instance data (
     *              merged into).  It can be also a callable with original node data on input
     *              and returning data of copied node. Copy data can be used to tweak values
     *              of new created $instance (e.g. in case that $instace is provided as id). 
     *              Defaults to empty array().
     * 
     * @return array|bool Array of created copy data containing id of created record
     *          and other values after normalization or files copying. FALSE if validation fails.
     * @throws Exception
     */
    public function copyInstance($instance, $options = array()) {
        $defaults = array(
            'copyData' => array(),
            'avoidValidations' => null,
        );
        $options = array_merge($defaults, $options);
        if (is_array($instance)) {
            $mandatoryFields = $this->schema;
            unset($mandatoryFields[$this->primaryKey]);
            unset($mandatoryFields['sort']);
            unset($mandatoryFields['created']);
            unset($mandatoryFields['modified']);
            unset($mandatoryFields[$this->deletedField]);
            if (($missingFields = array_diff_key($mandatoryFields, $instance))) {
                $missingFields = implode(', ', array_keys($missingFields));
                throw new Exception(__e(__FILE__, 'Insufficient content block instance data. Following fields are missing: %s', $missingFields));
            }
            $data = &$instance;
        }
        else{
            if (!($data = $this->findFirstBy('id', $instance))) {
                throw new Exception(__e(__FILE__, 'Unexisting instance id %s', $instance));
            }
        } 
        list($contentBlockModuleName, $contentBlockModelName) = explode(
            '.', $data['content_block_model']
        );
        $ContentBlock = App::loadModel(
            $contentBlockModuleName, 
            $contentBlockModelName, 
            true
        );
        $data = array_merge($data, json_decode($data['content_block_data'], true));
        if (Validate::callableFunction($options['copyData'])) {
            $data = array_merge($data, call_user_func_array($options['copyData'], array($data)));
        }
        else {
            $data = array_merge($data, $options['copyData']);
        }
        $this->reserveTable('ContentBlockInstance_copyInstance');
        try {
            $data = $ContentBlock->prepareCopyData($data);
            $newOwnerLastSort = $this->findField('sort', array(
                'conditions' => array(
                    'owner_model' => $data['owner_model'],
                    'owner_id' => $data['owner_id'],
                ),
                'order' => 'sort DESC'
            ));
            if ($newOwnerLastSort === null) {
                $data['sort'] = 0;
            }
            else {
                $data['sort'] = $newOwnerLastSort + 1;
            }
            // disable file fields validations (files are treated differently when copied)
            $fileFields = array_keys($ContentBlock->getPropertyFileFields());
            $options['avoidValidations'] = (array)$options['avoidValidations'];
            foreach ($fileFields as $fileField) {
                $options['avoidValidations'][$fileField] = true;
            }
            // save new content block instance data
            $this->setPropertyContentBlock($ContentBlock);
            $options['processContentBlockData'] = true;
            if (!($data = $this->save($data, $options))) {
                $this->resetPropertyContentBlock();
                $ContentBlock->cleanUpFiles(false);
                $this->unreserveTables('ContentBlockInstance_copyInstance');
                return false;
            }
        }
        catch (Throwable $e) {
            $this->resetPropertyContentBlock();
            $ContentBlock->cleanUpFiles(false);
            $this->unreserveTables('ContentBlockInstance_copyInstance');
            throw $e;
        }
        $this->resetPropertyContentBlock();
        $ContentBlock->cleanUpFiles(true);
        $this->unreserveTables('ContentBlockInstance_copyInstance');
        return $data;
    }
    
    /**
     * Returns content block instances join definition array for specified owner model
     * 
     * @param string $ownerModel Block owner is always some record. This is the model
     *          name that record belongs to. To ensure uniquity the model name should 
     *          be qualified by its module, e.g. 'App.WebContent', 'Eshop.EshopProduct'. 
     *          If the content blocks are related only to some field then also the field 
     *          name can be part of this name, e.g. 'Eshop.EshopProduct.description'
     * @param array $options Following are available:
     *      - 'activeOnly' (bool) If TRUE then only active instances are joined.
     *          If FALSE then all instances are joined. Defaults to TRUE.
     * 
     * @return array
     */
    public function getOwnerInstancesJoin($ownerModel, $options = array()) {
        $options = array_merge(array(
            'activeOnly' => true,
        ), $options);
        $ownerModelParts = Str::explode('.', $ownerModel);
        if (count($ownerModelParts) < 2) {
            throw new Exception(__e(
                __FILE__, 
                'Invalid content block owner model "%s". Model name must be qualified by its module.', 
                $ownerModel
            ));
        }
        $toModule = $ownerModelParts[0];
        $toModel = $ownerModelParts[1];
        $ToModel = App::loadModel($toModule, $toModel, true);
        $join = array(
            'type' => 'left',
            'module' => $this->module,
            'model' => $this->name,
            'toModule' => $toModule,
            'toModel' => $toModel,
            'conditions' => array(
                $this->name . '.owner_model' => $ownerModel,
                $this->name . '.owner_id = ' . $toModel . '.' . $ToModel->getPropertyPrimaryKey(),
            )
        );
        if ($options['activeOnly']) {
            $join['conditions'][$this->name . '.' . $this->activeField] = true;
        }
        return $join;
    }
}
