<?php
class Group extends Model {

    protected $table = 'run_groups';
    
    protected $primaryKey = 'id';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'pid' => array('type' => 'varchar', 'length' => 50),
        'name' => array('type' => 'varchar', 'length' => 100),
        'hierarchy' => array('type' => 'int', 'default' => 999, 'comment' => 'Group with lower value is superior to group with higher value. Some groups can have the same values if they are on the same level. This is considered mainly when allowing to edit users on backend.'),
        'created' => array('type' => 'datetime'),
        'modified' => array('type' => 'datetime'),
    );
    
    /**
     * List of pairs {pid} => {hierarchy} populated in constructor and
     * used by Group::gerHierarchy()
     *  
     * @var array 
     */
    static protected $hierarchies = null;

    public function __construct() {
        parent::__construct();
        
        // set validations
        $this->validations = array(            
        );
        
        if (self::$hierarchies === null) {
            self::$hierarchies = $this->findList(array(
                'key' => 'pid',
                'fields' => array('hierarchy'),
                'order' => 'hierarchy ASC'
            ));
        }
    }
    
    /**
     * Returns group hierarchy integer for provided group pid
     * 
     * @param string $pid Group pid
     * 
     * @return int Group hierarchy. If provided group $pid does not exist then PHP_INT_MAX.
     */
    public function getHierarchy($pid) {
        if (isset(self::$hierarchies[$pid])) {
            return self::$hierarchies[$pid];
        }
        return PHP_INT_MAX;
    }
}
