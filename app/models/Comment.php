<?php
class Comment extends Model {
    protected $table = 'run_comments';
    
    protected $schema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'run_users_id' => array('type' => 'int', 'default' => null, 'index' => 'index', 'comment' => 'Creator user id, NULL for comments created by unregistered users'),
        'foreign_model' => array('type' => 'varchar', 'length' => 50, 'comment' => 'Comment subject model name qualified by its module, e.g. App.WebContent, Eshop.EshopProduct'),
        'foreign_id' => array('type' => 'int', 'comment' => 'Comment subject id'),
        'status' => array('type' => 'enum', 'values' => array('enum_submitted_comment', 'enum_approved_comment', 'enum_rejected_comment'), 'default' => 'enum_submitted_comment'),
        'name' => array('type' => 'varchar', 'default' => null, 'comment' => 'Comment author name'),
        'text' => array('type' => 'text', 'default' => null),
        'email' => array('type' => 'varchar', 'length' => 100, 'default' => null),
        'rating' => array('type' => 'int', 'default' => null),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
    );
    
    protected $tableIndexes = array(
        array(
            'type' => 'index', 
            'fields' => array('foreign_model', 'foreign_id'), 
            'name' => 'Foreign record index'
        ),
    );
    
    /**
     * Returns array of conditions for comments published on frontend
     * 
     * @return array
     */
    static public function getPublishedConditions() {
        if (App::getSetting('App', 'Comment.autoApprove')) {
            $conditions = array(
                'status !=' => 'enum_rejected_comment'
            );
        }
        else {
            $conditions = array(
                'status' => 'enum_approved_comment'
            );
        }
        return $conditions;
    }
    
    /**
     * Rating options definition
     * Populated here belove in constructor
     * 
     * @var array 
     */
    protected $ratingOptions = array();
    
    public function __construct() {
        parent::__construct();
        
        $this->ratingOptions = array(
            1 => __(__FILE__, 'Very poor'),
            2 => __(__FILE__, 'Not that bad'),
            3 => __(__FILE__, 'Average'),
            4 => __(__FILE__, 'Good'),
            5 => __(__FILE__, 'Perfect'),
        );
        
        $this->validations = array(
            'foreign_model' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide foreign model name'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide foreign model name'),
                ),
            ),     
            'foreign_id' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide foreign id'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide foreign id'),
                ),
            ),     
            'name' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please provide your name'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please provide your name'),
                ),
                array(
                    'rule' => 'humanName',
                    'message' => __v(__FILE__, 'Please provide valid name'),
                ),
            ),
            'rating' => array(
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Zvoľte hodnotenie'),
                ),
            ),
            'text' => array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please write a comment'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Please write a comment'),
                ),
                array(
                    'rule' => 'plainText',
                    'message' => __v(__FILE__, 'Please use only plain text without URLs in review text'),
                    'alternative' => 'submit',
                ),
                array(
                    'rule' => 'literaryText',
                    'message' => __v(__FILE__, 'Please provide literary text'),
                    'alternative' => 'submit',
                ),
            ),     
            'email' => array(
                array(
                    'rule' => 'email',
                    'message' => __v(__FILE__, 'Zadajte prosím platný email'),
                    'alternative' => 'submit',
                ),
            ),     
            'sc' => array( // captcha
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Please, copy the security code'),
                    'alternative' => 'submit',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' =>  __v(__FILE__, 'Please, copy the security code'),
                    'alternative' => 'submit',
                ),
                array(
                    'rule' => array('captcha', array('name' => 'comment')),
                    'message' =>  __v(__FILE__, 'Security code is not valid'),
                    'alternative' => 'submit',
                ),
            )            
        );
    }
    
    /**
     * {@inheritdoc}
     */
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        
        // sanitize html to text (remove HTML tags) to avoid XSS attacks
        foreach ($this->getFields() as $field) {
            if (
                isset($data[$field])
                && is_string($data[$field])
                // avoid following fields
                && $field !== 'id'
            ) {
                $data[$field] = Sanitize::htmlTags($data[$field]);
            }
        }
        
        if (
            empty($data['name'])
            && (
                $options['on'] === 'create'
                ||
                array_key_exists('name', $data)
            )
        ) {
            if (!empty($data['run_users_id'])) {
                $User = App::loadModel('App', 'User', true);
                $user = $User->findFirstBy('id', $data['run_users_id'], array(
                    'fields' => array(
                        'first_name',
                        'last_name',
                    ),
                ));
                if (!empty($user)) {
                    $data['name'] = trim($user['first_name'] . ' ' . $user['last_name']);
                }
            }
            if (empty($data['name'])) {
                $user = App::getUser();
                if (!empty($user)) {
                    $data['name'] = trim($user['first_name'] . ' ' . $user['last_name']);
                }
            }
        }
        
        return parent::normalize($data, $options);
    }
    
    public function getPropertyRatingOptions() {
        return $this->ratingOptions;
    }
    
    public function submit($foreignModel, $foreignId, $data) {
        // normalize
        $data['foreign_model'] = $foreignModel;
        $data['foreign_id'] = $foreignId;
        $user = App::getUser();
        if (!empty($user)) {
            $data['run_users_id'] = $user['id'];
        }
        // normalize and validate
        $data = $this->normalize($data, array(
            'on' => 'create',
            'alternative' => 'submit',
        ));
        if (!$this->validate($data, array(
            'on' => 'create',
            'alternative' => 'submit',
        ))) {
            return false;
        }
        // save
        return $this->save($data, array(
            'normalize' => false,
            'validate' => false,
            'create' => true,
            'alternative' => 'submit',
        ));
    }
    
    function validate_plainText($value) {
        return !preg_match('/<[^>]*>/i', $value)
            && strpos($value, 'http') === false;
    }
    
}
