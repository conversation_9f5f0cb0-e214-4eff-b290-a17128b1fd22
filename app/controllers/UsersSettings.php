<?php
/**
 * @class UsersSettings
 * A simple application controller extension
 */
class UsersSettings extends Controller {
    
    public function __construct(){
        parent::__construct();
        $this->loadModel('UserSetting');
    }
        
    public function admin_load() {
        $Res = new ExtResponse();
        $Res->success = true;
        $Res->message = "Loaded data";
        
        $user = App::getUser();
        $UserSetting = new UserSetting();              
        $Res->data = $UserSetting->findFirst(array(
            'fields' => $_POST,
            'conditions' => array(
                'run_users_id' => $user['id'],
            )
        ));        
        return $Res->toJson();
    }
    
    public function admin_update() {
        $Res = new ExtResponse();
        $Res->success = true;
        $Res->message = "Updated data";
        
        $data = $_POST['data'];
        
        $user = App::getUser();
        $UserSetting = new UserSetting();
        $Res->data = $UserSetting->updateBy('run_users_id', $user['id'], $_POST);
        return $Res->toJson();
    }    
    
}

