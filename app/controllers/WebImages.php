<?php
class WebImages extends Controller {
    
    /**
     * Loads gallery items for provided content id.
     * Used by AJAX
     * 
     * @param int $contentId
     * 
     * @return string JSON encoded list of images records
     */
    public function admin_load($contentId = null) {
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        // normalize parent id
        $Response->data = array();
        if (!empty($contentId)) {
            $Image = $this->loadModel('WebImage', true);
            $images = $Image->findBy('run_web_contents_id', $contentId, array(
                'fields' => array('id', 'file', 'alternative_file', 'name'),
                'order' => 'sort ASC',
            ));
            foreach ($images as &$image) {
                $image['file'] = $Image->getFileFieldUrlPath('file', array('file' => $image['file']));
                $image['alternative_file'] = $Image->getFileFieldUrlPath('alternative_file', array('file' => $image['alternative_file']));
            }
            $Response->data = $images;
        }
        $this->setLayout('json');
        return $Response->getJson();
    }
    
    /**
     * Add new gallery item for provided content id.
     * Used by AJAX
     * 
     * @param int $contentId
     * 
     * @return string JSON encoded array of new image record
     */
    public function admin_add($contentId = null) {
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        // normalize parent id
        $Response->data = array();
        if (!empty($contentId) && !empty($this->data)) {
            $Image = $this->loadModel('WebImage', true);
            $this->data['run_web_contents_id'] = $contentId;
            if (
                $Image->addOrderedItem($this->data, array(
                    'groupConditions' => array('run_web_contents_id' => $contentId),
                ))
            ) {
                $data = $Image->findFirstBy('id', $Image->getPropertyId());
                if (!empty($data['file'])) {
                    $data['file'] = $Image->getFileFieldUrlPath('file', array('file' => $data['file']));
                }
                if (!empty($data['alternative_file'])) {
                    $data['alternative_file'] = $Image->getFileFieldUrlPath('alternative_file', array('file' => $data['alternative_file']));
                }
                $Response->data = $data;
            }
            else {
                $Response->success = false;
                $Response->errors = $Image->getErrors();
            }
        }
        $this->setLayout('json');
        return $Response->getJson();
    }
    
    /**
     * Updates gallery item for provided item id (id is present in data).
     * Used by AJAX
     * 
     * @return string JSON encoded array of updated image record
     */
    public function admin_update() {
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        if ($this->data) {
            $Image = $this->loadModel('WebImage', true);
            if ($Image->save($this->data)) {
                $data = $Image->findFirstBy('id', $Image->getPropertyId());
                if (!empty($data['file'])) {
                    $data['file'] = $Image->getFileFieldUrlPath('file', array('file' => $data['file']));
                }
                if (!empty($data['alternative_file'])) {
                    $data['alternative_file'] = $Image->getFileFieldUrlPath('alternative_file', array('file' => $data['alternative_file']));
                }
                $Response->data = $data;
            }
            else {
                $Response->success = false;
                $Response->errors = $Image->getErrors();
            }
        }
        $this->setLayout('json');
        return $Response->getJson();
    }
    
    /**
     * Updates gallery item for provided item id (id is present in data).
     * Used by AJAX
     * 
     * @param int $contentId
     * @param int $id
     * @param int $orderIndex
     * 
     * @return string JSON success response
     */
    public function admin_move($contentId = null, $id = null, $orderIndex = null) { // debug
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        if (!empty($contentId) && !empty($id)) {
            $Image = $this->loadModel('WebImage', true);
            if (!$Image->moveOrderedItem($id, array(
                'groupConditions' => array('run_web_contents_id' => $contentId), 
                'newOrderIndex' => $orderIndex
            ))) {
                $Response->success = false;
                $Response->message = __a(__FILE__, 'Node move has failed');
            }
        }
        $this->setLayout('json');
        return $Response->getJson();
    }
            
    public function admin_delete($contentId = null, $id = null) {
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        if (!empty($contentId) && !empty($id)) {
            $Image = $this->loadModel('WebImage', true);
            if (!$Image->deleteOrderedItem($id, array(
                'groupConditions' => array('run_web_contents_id' => $contentId), 
            ))) {
                $Response->success = false;
                $Response->message = __a(__FILE__, 'Node delete has failed');
            }
        }
        $this->setLayout('json');
        return $Response->getJson();
    }
}

