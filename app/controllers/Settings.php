<?php
class Settings extends Controller {
          
    /**
     * Creates and saves html form to edit settings
     * 
     * @return string
     */
    public function admin_edit() {   
        $this->displayOriginComment = true;
        $Setting = $this->loadModel('Setting', true);
        $UserSetting = $this->loadModel('UserSetting', true);
        
        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        // save settings
        if (!empty($this->data)) {
            // save settings
            $settingsResult = $userSettingsResult = true;
            if(!empty($this->data['Setting'])) {
                $settingsResult = $Setting->updateByName($this->data['Setting'], array(
                    'lang' => $lang,
                ));
            }
            // save user settings
            if(!empty($this->data['UserSetting'])) {
                $userSettingsResult = $UserSetting->updateBy(
                    'run_users_id', 
                    App::getUser('id'), 
                    $this->data['UserSetting']
                );            
            }    
            if ($settingsResult && $userSettingsResult) {
                App::setSuccessMessage(__a(__FILE__, 'Settings has been succesfully updated'));
                App::redirect(App::$url);
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
        }
        // load settings
        else {
            $this->data['Setting'] = $Setting->findListWithNameKeys(array(
                'lang' => $lang,
            ));
        }
                        
        // render view
        App::setSeoTitle(__a(__FILE__, 'System settings'));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'System settings'),
            'data' => $this->data,
            'errors' => array(
                'Setting' => $Setting->getErrors(),
                'UserSetting' => $UserSetting->getErrors(),
            ),
            'required' => array(
                'Setting' => $Setting->getNotEmptyFields(array(
                    'data' => Sanitize::value($this->data['Setting']),
                )),
                'UserSetting' => $UserSetting->getNotEmptyFields(array(
                    'data' => Sanitize::value($this->data['UserSetting']),
                )),
            ),
            'columns' => 4,
            'actions' => array(
                'lang' => true,
            ),
            'showAffix' => true,
            'fields' => array(
                // System settings
                    
                array('h1' => __a(__FILE__, 'Basic settings')),
                array('row'),
                    'Setting.App/name' => array(
                        'label' => __a(__FILE__, 'Site name'),
                        'hint' => __a(__FILE__, 'Site name is used for different purposes in application, e.g. MySite.com'),
                    ),
                    /*/
                    'Setting.App/headerText' => array(
                        'label' => __a(__FILE__, 'Header text')
                    ),
                    /*/
//                array('/row'),
//                array('row', 'columns' => 1),
//                    'Setting.App/footerText' => array(
//                        'label' => __a(__FILE__, 'Footer text'),
//                        'type' => 'editor',
//                        'options' => array(
//                            'bodyClass' => 'footer-text',
//                        )
//                    ),
                array('/row'),
                array(
                    'h1' => __a(__FILE__, 'Default SEO parameters'), 
                    'hint' => __a(__FILE__, 'Parameters for search engines, e.g. Google.com, Yahoo.com or Zoznam.cz. This SEO parameters are used for all pages which have no own SEO parameters defined. ')
                ),
                array('row'),
                    'Setting.App/seo/defaultTitle' => array(
                        'label' => __a(__FILE__, 'SEO title'),
                        'hint' => __a(__FILE__, 'Title is displayed in browser tab and in Google search results. It is one of most important texts for Google on your page'),
                    ),
                    'Setting.App/seo/titlePrefix' => array(
                        'label' => __a(__FILE__, 'SEO title prefix'),
                        'hint' => __a(__FILE__, 'This prefix will be prepended to each SEO title'),
                    ),
                    'Setting.App/seo/titleSuffix' => array(
                        'label' => __a(__FILE__, 'SEO title suffix'),
                        'hint' => __a(__FILE__, 'This suffix will be appended to each SEO title')
                    ),
                array('/row'),
                array('row'),
                    'Setting.App/seo/defaultDescription' => array(
                        'label' => __a(__FILE__, 'SEO description'),
                        'hint' => __a(__FILE__, 'This text is mostly used by Google in search results as text under result title. Sometimes Google decides to use some oter text generated from page content'),
                    ),
                    'Setting.App/seo/defaultKeywords' => array(
                        'label' => __a(__FILE__, 'SEO keywords'),
                        'hint' => __a(__FILE__, 'Comma separated list of site keywords. Google does not consider much keywords. In each case put here something because other seach engines may use this'),
                    ),
                array('/row'),
                array(
                    'h1' => __a(__FILE__, 'Rýchly kontakt'), 
                    'hint' => __a(__FILE__, 'Údaje rýchleho kontaktu zobrazené v hlavičke'),
                    'columns' => 4
                ),
                array('row'),
//                    'Setting.App/company/address' => array(
//                        'label' => __a(__FILE__, 'Company address'),
//                        'type' => 'editor',
//                        'options' => array(
//                            'toolbar' => 'Basic',
//                        )
//                    ),
//                    'Setting.App/company/gpsLatitude' => array(
//                        'label' => __a(__FILE__, 'Company GPS latitude'),
//                    ),
//                    'Setting.App/company/gpsLongitude' => array(
//                        'label' => __a(__FILE__, 'Company GPS longitude'),
//                    ),
                    'Setting.App/company/email' => array(
                        'label' => __a(__FILE__, 'E-mail'),
                    ),
                    'Setting.App/company/phone' => array(
                        'label' => __a(__FILE__, 'Telefónne číslo'),
                        'hint' => __a(__FILE__, 'Môžete zadať viac telefónnych čísiel oddelených čiarkou, , napr. <code>043/4270445,043/4270446</code>'),
                    ),
                    'Setting.App/company/openingHours' => array(
                        'label' => __a(__FILE__, 'Otváracie hodiny'),
                    ),
                array('/row'),
//                array(
//                    'h1' => __a(__FILE__, 'Sliders settings'),
//                    'hint' => __a(__FILE__, 'Nastavenia slajdrov v hlavičke stránky. Používajú sa tiež ako východzie nastavenia pre obsahový blok "Slajder"')
//                ),
//                array('row'),
////                    // effect applies only to Html::wowSlider()    
////                    'Setting.App/slider/effect' => array(                
////                        'label' => __a(__FILE__, 'Effect'),
////                        'type' => 'select',
////                        'options' => array(
////                            'basic' => __a(__FILE__, 'Basic'),
////                            'blur' => __a(__FILE__, 'Blur'),
////                            'book' => __a(__FILE__, 'Book'),
////                            'brick' => __a(__FILE__, 'Brick'),
////                            'cube' => __a(__FILE__, 'Cube'),
////                            'domino' => __a(__FILE__, 'Domino'),
////                            'fade' => __a(__FILE__, 'Fade'),
////                            'parallax' => __a(__FILE__, 'Parallax'),
////                            'slices' => __a(__FILE__, 'Slices'),
////                            'stack' => __a(__FILE__, 'Stack from left'),
////                            'stack_vertical' => __a(__FILE__, 'Stack from top'),
////                        ),
////                    ),
//                    'Setting.App/slider/transitionTime' => array(
//                        'label' => __a(__FILE__, 'Transition time [s]'),
//                        'hint' => __a(__FILE__, 'Time in seconds the transition between two slides lasts'),
//                    ),
//                    'Setting.App/slider/slideTime' => array(
//                        'label' => __a(__FILE__, 'Slide time [s]'),
//                        'hint' => __a(__FILE__, 'Time in seconds the slider pauses on each slide'),
//                    ),
//                array('/row'),
//                array('row'),
//                    'Setting.App/slider/autoPlay' => array(
//                        'label' => __a(__FILE__, 'Autoplay'),
//                        'hint' => __a(__FILE__, 'Should the sliders start automatically?'),
//                        'type' => 'checkbox',
//                    ),
//                    'Setting.App/slider/stopOnHover' => array(
//                        'label' => __a(__FILE__, 'Stop on hover'),
//                        'hint' => __a(__FILE__, 'Should the slider stop when cursor is over it?'),
//                        'type' => 'checkbox',
//                    ),
//                array('/row'),
                array(
                    'h1' => __a(__FILE__, 'SMTP settings'),
                    'hint' => __a(__FILE__, 'Settings of connection to mail server')
                ),
                array('row'),
                    'Setting.App/smtp/username' => array(
                        'label' => __a(__FILE__, 'SMTP username')
                    ),
                    'Setting.App/smtp/password' => array(
                        'label' => __a(__FILE__, 'SMTP password')
                    ),
//                array('/row'),
//                array('row'),
                    'Setting.App/smtp/host' => array(
                        'label' => __a(__FILE__, 'SMTP host')
                    ),
////port is resolved implicitly by Setting::normalize()                
//                    // M.Tausk:
//                    // 465/ssl (SMTPS) - znamy tiez ako submission port >> klienti by mali pouzivat tento.
//                    // 587/tls (MSA / STARTTLS) >> message agenti by mali pouzivat tento port
//                    'Setting.App/smtp/port' => array(
//                        'label' => __a(__FILE__, 'SMTP port'),
//                        'hint' => __a(__FILE__, 'Use port <code>465</code> for "SMTP encryption" <code>SSL encryption</code>, port <code>587</code> for <code>TLS encryption</code> and port <code>25</code> for <code>No encryption</code>'),
//                    ),
                    'Setting.App/smtp/encryption' => array(
                        'label' => __a(__FILE__, 'SMTP encryption'),
                        'type' => 'select',
                        'options' => array(
                            '' => __a(__FILE__, 'No encryption'),
                            'SSL' => __a(__FILE__, 'SSL encryption'),
                            'TLS' => __a(__FILE__, 'TLS encryption'),
                        )
                    ),
                array('/row'),
                array('h1' => __a(__FILE__, 'Email addresses')),
                array('row'),
                    'Setting.App/email/from' => array(
                        'label' => __a(__FILE__, 'Sender of system emails')
                    ),
                    'Setting.App/email/to' => array(
                        'label' => __a(__FILE__, 'Recipient for system emails')
                    ),
                    'Setting.App/email/cc' => array(
                        'label' => __a(__FILE__, 'Copy recipient for system emails')
                    ),
                array('/row'),
                array(
                    'h1' => __a(__FILE__, 'SMS settings'),
                    'hint' => __a(__FILE__, 'SMS account must be created on <a href="http://www.smartsms.sk" target="_blank">www.smartsms.sk</a>'),
                ),
                array('row'),
                    'Setting.App/smartsms/username' => array(
                        'label' => __a(__FILE__, 'Username'),
                        'hint' => __a(__FILE__, 'This is the same username and password as used for login on www.smartsms.sk. If you change your login on www.smartsms.sk, you must change it here too!'),
                    ),
                    'Setting.App/smartsms/password' => array(
                        'label' => __a(__FILE__, 'Password'),
                        'hint' => __a(__FILE__, 'This is the same username and password as used for login on www.smartsms.sk. If you change your login on www.smartsms.sk, you must change it here too!'),
                    ),
                    'Setting.App/smartsms/from' => array(
                        'label' => __a(__FILE__, 'Sender phone'),
                        'hint' => __a(__FILE__, 'Sender phone must be allowed in account on <a href="http://www.smartsms.sk" target="_blank">www.smartsms.sk</a> and must be provided here in the same form'),
                    ),
                array('/row'),
                array('h1' => __a(__FILE__, 'Comments')),
                array('row'),
                    'Setting.App/Comment/onlyByRegisteredUsers' => array(
                        'label' => __a(__FILE__, 'Only by registered users'),
                        'type' => 'checkbox',
                    ),
                    'Setting.App/Comment/autoApprove' => array(
                        'label' => __a(__FILE__, 'Auto approve'),
                        'type' => 'checkbox',
                    ),
                    'Setting.App/Comment/allowRatings' => array(
                        'label' => __a(__FILE__, 'Allow ratings'),
                        'type' => 'checkbox',
                    ),
                array('/row'),
                array(
                    'h1' => __a(__FILE__, 'Announcement'),
                    'hint' => __a(__FILE__, 'Application annoncement shown in header'),
                ),
                array('row', 'columns' => 1),
                    'Setting.App/announcement/title' => array(
                        'label' => __a(__FILE__, 'Title'),
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Setting.App/announcement/text' => array(
                        'label' => __a(__FILE__, 'Text'),
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Basic',
                        )
                    ),
                array('/row'),
                array('row', 'columns' => array(3, 9)),
                    'Setting.App/announcement/homePageOnly' => array(
                        'label' => __a(__FILE__, 'Display only on homepage'),
                        'hint' => __a(__FILE__, 'If checked then the announcement is displayed only on home page'),
                        'type' => 'checkbox',
                    ),
                    array('if' => 
                        !empty($this->data['Setting']['App/announcement/title']) 
                        && !empty($this->data['Setting']['App/announcement/text'])
                    ),
                        'Setting.App/announcement/textIsHidden' => array(
                            'label' => __a(__FILE__, 'Text is hidden'),
                            'hint' => __a(__FILE__, 'If checked then the text is at the begining hidden and it gets diyplayed only after click on announcement. This is applicable only if both title and text is provided'),
                            'type' => 'checkbox',
                        ),
                    array('endif'),
                array('/row'),
                array(
                    'h1' => __a(__FILE__, 'Doplnkový oznam'),
                    'hint' => __a(__FILE__, 'It serves for example as a lottery notification.'),
                    ),
                array('row', 'columns' => 1),
                    'Setting.App/menuAnnouncement/text' => array(
                        'label' => __a(__FILE__, 'Text'),
                        'hint' => __a(__FILE__, 'If empty announcement will not show.'),
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Text',
                        )
                    ),
                array('/row'),
                array('row'),
                    'Setting.App/menuAnnouncement/active' => array(
                        'label' => __a(__FILE__, 'Active'),
                        'type' => 'checkbox',
                    ),
                    // 'Setting.App/menuAnnouncement/lottery' => array(
                    //     'label' => __a(__FILE__, 'Lottery'),
                    //     'type' => 'checkbox',
                    //     'hint' => __a(__FILE__, 'If checked, wheel of fortune will be displayed before text.')
                    // ),
                    'Setting.App/menuAnnouncement/locator' => array(
                        'label' => __a(__FILE__, 'Locator'),
                        'hint' => __a(__FILE__, 'Link at site with detailed info about announcement.') . '<br>' . 
                            __a(
                            __FILE__,
                            'Page URL can be provided in 3 ways:<ul><li><b>actual page URL name</b>, e.g. <code>this-page</code> - it means that this page is accesses at <kbd>:urlBase:/<b>this-page</b></kbd></li><li><b>reference to another page</b> starting by slash <code>/</code>, e.g. <code>/that-page</code> - it means that menu item "This page" points to <kbd>:urlBase:<b>/that-page</b></kbd> and content of "That page" will be displayed. This can be used to make different menu items to point to the same content</li><li><b>absolute address</b> starting by <code>http://</code>, e.g. <code>http://google.com</code> - it means that menu item "This page" points to <kbd><b>http://google.com</b></kbd>. This can be used to make menu items to point to external address. Absolute address is always opened in new browser tab</li></ul>',
                            array(
                                'urlBase' => App::$urlBase,
                            )
                        ),
                    ),
                    // 'Setting.App/menuAnnouncement/background' => array(
                    //     'label' => __a(__FILE__, 'Background color'),
                    //     'type' => 'select',
                    //     'options' => array(
                    //         '#DCAF0E' => __a(__FILE__, 'yellow'),
                    //         '#DC140E' => __a(__FILE__, 'red'),
                    //         '#E75905' => __a(__FILE__, 'orange'),
                    //         '#158F29' => __a(__FILE__, 'green'),
                    //         '#0275D8' => __a(__FILE__, 'blue'),
                    //         '#C56546' => __a(__FILE__, 'brown'),
                    //     )
                    // ),
                array('/row'),
                array('h1' => __a(__FILE__, 'Cookies info')),
                array('row', 'columns' => 1),
                        'Setting.App/cookiesPrivacyPolicyInfo' => array(
                            'label' => __a(__FILE__, 'Cookies info'),
                            'hint' => __a(__FILE__, 'Text used for cookies info banner. You can use <code>:infoPageUrl:</code> insert, the best as <code>&lt;a href=\":infoPageUrl:\"&gt;...&lt;/a&gt;</code>. If not provided then the cookies info banner is not displayed.'),
                            'type' => 'editor',
                            'options' => array(
                                'toolbar' => 'Basic',
                            )
                        ),
                array('/row'),
                array('row'),
                        'Setting.App/cookiesPrivacyPolicyInfoPosition' => array(
                            'label' => __a(__FILE__, 'Cookies info position'),
                            'type' => 'select',
                            'options' => array(
                                'fixedAtBottom' => __a(__FILE__, 'Fixed at bottom'),
                                'scrollableAtTop' => __a(__FILE__, 'Scrollable at top'),
                            )
                        ),
                array('/row'),
                array('h1' => __a(__FILE__, 'Užívateľské HTML')),
                array('row', 'columns' => 1),
                    'Setting.App/customCode/htmlHead' => array(
                        'label' => __a(__FILE__, 'HTML na začiatku <code>&lt;head&gt;</code> tagu'),
                        'hint' => __a(__FILE__, 'Užívateľský HTML kód ktorý môže obsahovať JS a/alebo CSS skripty alebo linky na ne. Tento kód, sa vloží na začiatok tagu <code>&lt;head&gt;</code>. Väčšinou ide o analitické JS skripty. JS a CSS skripty musia byť uvedené v príslušných HTML tagoch, t.j. JS v <code>&lt;script type="text/javascript"&gt;...&lt;/script&gt;</code> a CSS v <code>&lt;style type="text/css"&gt;...&lt;/style&gt;</code>. <b>POZOR: Vloženie chybného JS skriptu môže spôsobiť nefunkčnosť niektorých častí webovej stránky!</b>'),
                        'type' => 'textarea',
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Setting.App/customCode/htmlBodyStart' => array(
                        'label' => __a(__FILE__, 'HTML na začiatku <code>&lt;body&gt;</code> tagu'),
                        'hint' => __a(__FILE__, 'Užívateľský HTML kód ktorý môže obsahovať JS a/alebo CSS skripty alebo linky na ne. Tento kód, sa vloží tesne za začiatočný tag <code>&lt;body&gt;</code>. Väčšinou ide o analitické JS skripty. JS a CSS skripty musia byť uvedené v príslušných HTML tagoch, t.j. JS v <code>&lt;script type="text/javascript"&gt;...&lt;/script&gt;</code> a CSS v <code>&lt;style type="text/css"&gt;...&lt;/style&gt;</code>. <b>POZOR: Vloženie chybného JS skriptu môže spôsobiť nefunkčnosť niektorých častí webovej stránky!</b>'),
                        'type' => 'textarea',
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    'Setting.App/customCode/htmlBodyEnd' => array(
                        'label' => __a(__FILE__, 'HTML na konci <code>&lt;body&gt;</code> tagu'),
                        'hint' => __a(__FILE__, 'Užívateľský HTML kód ktorý môže obsahovať JS a/alebo CSS skripty alebo linky na ne. Tento kód, sa vloží tesne pred koncový tag <code>&lt;/body&gt;</code>. Väčšinou ide o analitické JS skripty. JS a CSS skripty musia byť uvedené v príslušných HTML tagoch, t.j. JS v <code>&lt;script type="text/javascript"&gt;...&lt;/script&gt;</code> a CSS v <code>&lt;style type="text/css"&gt;...&lt;/style&gt;</code>. <b>POZOR: Vloženie chybného JS skriptu môže spôsobiť nefunkčnosť niektorých častí webovej stránky!</b>'),
                        'type' => 'textarea',
                    ),
                array('/row'),
                array('h1' => __a(__FILE__, 'Other settings')),
                array('row'),
                    /*/
//                    'Setting.App/home/<USER>' => array(
//                        'label' => __a(__FILE__, 'Home video'),
//                        'hint' => __a(__FILE__, 'URL or code of Youtube video shown on home page'),
//                    ),
//                    'Setting.App/home/<USER>' => array(
//                        'label' => __a(__FILE__, 'Home video title'),
//                        'hint' => __a(__FILE__, 'Title text used for home video'),
//                    ),
                    /*/
                    'Setting.App/liveChat/account' => array(
                        'label' => __a(__FILE__, 'Live chat account'),
                        'hint' => __a(__FILE__, 'Názov účtu vytvoreného na <a href="https://www.livechatoo.com" target="_blank">livechatoo.com</a>. Po prihlásení do administrácie Livechatoo účtu ho nájdete na záložke <i>Nastavenia</i> > <i>Názov účtu</i>. Ak sa stáva, že operátor je prihlásený a napriek tomu je chat na verejných stránkach offline, tak nech sa operátor prihlási v osobitnom okne alebo nech použije prehliadač FireFox.'),
                    ),
                    'Setting.App/google/analyticsCode' => array(
                        'label' => __a(__FILE__, 'Google analytics code'),
                        'hint' => __a(__FILE__, 'Kód sa použije na vygenerovanie Google analytics a Google e-commerce. Google analytics sa negeneruje, ak sa už nachádza v niektorom z užívateľských HTML (viď vyššie).'),
                    ),
//                    'Setting.App/google/tagManagerCode' => array(
//                        'label' => __a(__FILE__, 'Google tag manager code'),
//                        'hint' => __a(__FILE__, 'Kód začínajúci <i>GTM-</i>. Kód sa použije na vygenerovanie Google tag managera. GTM sa negeneruje, ak sa už nachádza v niektorom z užívateľských HTML (viď vyššie).'),
//                    ),
                    'Setting.App/google/apiKey' => array(
                        'label' => __a(__FILE__, 'Google API key'),
                        'hint' => __a(
                            __FILE__, 
                            'Kľuč na prístup ku <a href="%s" target="_blank">google webovým službám</a> použitým na tejto stránke, napr. google mapy, preklady, zisťovanie vzdialeností, atď. Na vytvorenie nového kľúča použite <a href="%s" target="_blank">vývojársku konzolu google</a>. Na použitie google webových služieb je potrebné <a href="%s" target="_blank">google účet pripojiť k platobnej karte</a>. Každý mesiac je však nastavený úvodný kredit $200 a až po prečerpaní tohoto kreditu je potrebné platiť. Pri bežnej prevádzke k tomu väčšinou vôbec nedôjde alebo sa platia len nízke ceny. <b>Vytvorenému API kľúču povoľte len služby použité na tejto stránke a nastave im limity</b>, aby kľúč nebolo možné zneužiť, t.j. použiť inou osobou na vaše náklady.',
                            'https://developers.google.com/apis-explorer',
                            'https://console.developers.google.com',
                            'https://console.cloud.google.com/billing'
                        )
                    ),
                    // @deprecated - use setting App.customCode.html??? / App.element html???CustomCode instead
                    'Setting.App/luigisBox/jsScriptName' => array(
                        'label' => __a(__FILE__, 'Názov Luigi\'s Box JS skriptu'),
                        'hint' => __a(__FILE__, 'Napríklad pri skripte <code>https://scripts.luigisbox.com/LBX-12345.js</code> je názov <code>LBX-12345</code>')
                    ),
                array('/row'),
                array('row'),
                    'Setting.App/facebook/appId' => array(
                        'label' => __a(__FILE__, 'Facebook App Id'),
                        'hint' => __a(
                            __FILE__, 
                            'Id facebook aplikácie. Ak je zadané, tak napríklad umožňuje na facebooku sledovať štatistiky návštevnosti alebo spravovať komentáre stránky (ak sú na nej použité). Vygenerovať je možné ho <a href="%s" target="_blank">tu</a>.',
                            'https://developers.facebook.com/apps/redirect/dashboard'
                        )
                    ),
                    array('col'),
                        array(
                            'field' => 'Setting.App/facebook/defaultImage',
                            'type' => 'file',
                            'label' => __a(__FILE__, 'Facebook východzí obrázok'),
                            'hint' => __a(
                                __FILE__, 
                                'Východzí obrázok obsahu stránky zdieľaného na facebooku (napr. cez like). Nahrajte ho v rozmeroch a formáte (.jpg/.png), tak ako chcete, aby bol zobrazený na facebooku.'
                            )
                        ),
                        array(
                            'field' => 'Setting.App/facebook/defaultImage',
                            'type' => 'image',
                            'style' => 'height: 118px',
                            'deleteImage' => '/mvc/App/Settings/admin_deleteFile/App.facebook.defaultImage',
                        ),
                    array('/col'),
                array('/row'),                
            ),
        ));
    }
    
    /**
     * Deletes file of specified setting.
     * 
     * @param string $fileField The setting name to remove file for. Can by provided 
     *      either separated by pathSeparator or dots, e.g. 'App.facebook.defaultImage'.
     */
    public function admin_deleteFile($fileField = null) {
        if (!$fileField) {
            App::setErrorMessage(__a(__FILE__, 'Missing file field name'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        $Setting = $this->loadModel('Setting', true);
        
        // normalize $fileField
        $fileField = str_replace('.' , $Setting->getPropertyPathSeparator(), $fileField);
        
        $fileFields = $Setting->getPropertyFileFields();
        if (!isset($fileFields[$fileField])) {
            App::setErrorMessage(__a(__FILE__, 'Invalid file field'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        $Setting->updateByName(
            array($fileField => ''),
            array(
                'normalize' => false,
                'validate' => false,
            )
        );
        App::setSuccessMessage(__a(__FILE__, 'File has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }
    
    /**
     * MVC ELEMENT
     * Contacts displayed in the main header
     */
    public function mainContacts() {
        return $this->loadView('Settings/mainContacts', array(
            'email' => $this->getSetting('company.email'),
            'phone' => $this->getSetting('company.phone'),
            'hours' => $this->getSetting('company.openingHours'),
            'contactUrl' => App::getContentUrlByPid('contact'),
        ));
    }
    
    /**
     * MVC ELEMENT
     * Contacts displayed in the footer
     */
    public function footerContacts() {
        return $this->loadView('Settings/footerContacts');
    }
}

