<?php
class TimelineEvents extends Controller {
    
    public function admin_index() {
        
        // get lang to retrieve timeline events for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;

        $TimelineEvent = $this->loadModel('TimelineEvent', true);
        $records = $TimelineEvent->find(array(
            'conditions' => array('TimelineEvent.lang' => $lang),
            'order' => 'TimelineEvent.start ASC',
            'paginate' => true,
        ));
        
        $langInfo = '';
        if ($lang !== DEFAULT_LANG) {
            $langInfo = ' (' . strtoupper($lang) . ')';
        }
        App::setSeoTitle(__a(__FILE__, 'Timeline events') . $langInfo);
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'List timeline events') . $langInfo,
            'records' => $records,
            'columns' => array(
//                'name' => __a(__FILE__, 'Name'),
                'text' => __a(__FILE__, 'Text'),
                'start' => __a(__FILE__, 'Start'),
                'end' => __a(__FILE__, 'End'),
                'lang' => __a(__FILE__, 'Lang'),
                'position' => __a(__FILE__, 'Position'),
            ),
            'renderFields' => array(
                'text' => array($TimelineEvent, 'renderTextInIndex'),
                'position' => $TimelineEvent->getEnumValues('position'),
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('active' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                )
            ),
            'Paginator' => $TimelineEvent->Paginator,
            'actions' => array(                
                'add' => array(
                    'url' => '/mvc/App/TimelineEvents/admin_add',
                ),
                'lang' => true
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => '/mvc/App/TimelineEvents/admin_edit',
                ),
                'delete' => array(
                    'url' => '/mvc/App/TimelineEvents/admin_delete',
                    'confirmMessage' => __a(__FILE__, 'Please, comfirm removal of timeline event'),
                ),
            )
        ));
    }
    
    public function admin_add() {
        $TimelineEvent = $this->loadModel('TimelineEvent', true);
        
        if ($this->data) {
            if ($TimelineEvent->save($this->data, array('create' => true))) {
                App::setSuccessMessage(__a(__FILE__, 'New record has been succesfully created'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                    'args' => array($TimelineEvent->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
        }
        else {
            $this->data = array();
        }
        
        App::setSeoTitle(__a(__FILE__, 'New timeline event'));

        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Add new timeline event'),
            'data' => $this->data,
            'Model' => $TimelineEvent,
            'columns' => 4,
            'fields' => array(
//                'name' => array('label' => __a(__FILE__, 'Name')),
                array('row'),
                    'start' => array('label' => __a(__FILE__, 'Start year')),
                    'end' => array('label' => __a(__FILE__, 'End year')),
                array('/row'),
                array('row', 'columns' => 2),
                    'text' => array(
                        'label' => __a(__FILE__, 'Text'),
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Text',
                            'height' => '250px',
                            'bodyClass' => 'timeline-event-layout',
                        ),
                    ),
                array('/row'),
                'position' => array(
                    'label' => __a(__FILE__, 'Position'),
                    'options' => $TimelineEvent->getEnumValues('position'),
                ),
                'active' => array('label' => __a(__FILE__, 'Active')),
            )
        ));
    }
    
    public function admin_edit($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '_error',
                'source' => App::$requestSource,
            )));
        }
        
        $TimelineEvent = $this->loadModel('TimelineEvent', true);
        
        if ($this->data) {
            if ($TimelineEvent->save($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                App::redirect(App::$url);
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        else {
            $this->data = $TimelineEvent->findFirstBy('id', $id);
        }
        
        App::setSeoTitle(__a(__FILE__, 'Timeline event &quot;%s&quot;', $this->data['start']));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Edit timeline event &quot;%s&quot;', Sanitize::value($this->data['start'])),
            'data' => $this->data,
            'Model' => $TimelineEvent,
            'columns' => 4,
            'fields' => array(
                'id' => array('type' => 'hidden'),
//                'name' => array('label' => __a(__FILE__, 'Name')),
                array('row'),
                    'start' => array('label' => __a(__FILE__, 'Start year')),
                    'end' => array('label' => __a(__FILE__, 'End year')),
                array('/row'),
                array('row', 'columns' => 2),
                    'text' => array(
                        'label' => __a(__FILE__, 'Text'),
                        'type' => 'editor',
                        'options' => array(
                            'toolbar' => 'Text',
                            'height' => '250px',
                            'bodyClass' => 'timeline-event-layout',
                        ),
                    ),
                array('/row'),
                'position' => array(
                    'label' => __a(__FILE__, 'Position'),
                    'options' => $TimelineEvent->getEnumValues('position'),
                ),
                'active' => array('label' => __a(__FILE__, 'Active')),
            )
        ));
    }
          
    public function admin_delete($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        $TimelineEvent = $this->loadModel('TimelineEvent', true);
        $TimelineEvent->deleteBy('id', $id);
        App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }  
    
    /**
     * MVC ELEMENT
     * 
     * Generates timeline
     * 
     * @return string
     */
    public function timeline() {
        $this->displayOriginComment = true;
        $TimelineEvent = $this->loadModel('TimelineEvent', true);
        $items = $TimelineEvent->find(array(
            'conditions' => array(
                'TimelineEvent.active' => true, 
                'TimelineEvent.lang' => App::$lang
            ),
            'order' => 'TimelineEvent.start ASC',
        ));
        $minYear = 9999; 
        $maxYear = 0;
        foreach ($items as $item) {
            if ($item['start'] > $maxYear) {
                $maxYear = $item['start'];
            }
            if ($item['start'] < $minYear) {
                $minYear = $item['start'];
            }
        }
        return $this->loadView('TimelineEvents/timeline', array(
            'items' => $items,
            'minYear' => $minYear,
            'maxYear' => $maxYear,
        ));        
    }
    
}
