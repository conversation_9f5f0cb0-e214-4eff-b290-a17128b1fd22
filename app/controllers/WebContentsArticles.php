<?php
App::loadController('App', 'WebContents');
class WebContentsArticles extends WebContents {
    // It is the class name what is here important as WebContents::$sectionPid is
    // set according to it, see WebContents::__construct()
    //
    // All this serves to correct matching web content records and sections in Admin
    
    /**
     * MVC ELEMENT
     * 
     * Generates articles review pan 
     * 
     * @return string
     */
    public function review() {
        $this->displayOriginComment = true;
        $Content = $this->loadModel('WebContent', true);
        $root = $Content->findFirst(array(
            'conditions' => array(
                'pid' => 'articles',
                'lang' => App::$lang,
            ),
            'fields' => array('WebContent.id', 'WebContent.name')
        ));
        if (!$root) {
            return '';
        }
        $items = $Content->find(array(
            'conditions' => array(
                'WebContent.parent_id' => $root['id'],
                'WebContent.active' => true
            ),
            'fields' => array(
                'WebContent.id',
                'WebContent.name',
                'WebContent.locator',
                'WebContent.resume',
                'WebContent.date',
                'WebContent.menu_icon',
                'WebContent.aux_01',
            ),
            'order' => array('WebContent.sort DESC'),
            'limit' => 3,
        ));
        $count = $Content->findCount(array(
            'conditions' => array(
                'WebContent.parent_id' => $root['id'],
                'WebContent.active' => true
            ),
        ));
        // set image url paths
        foreach ($items as &$item) {
            if (!empty($item['menu_icon'])) {
                $item['menu_icon'] = $Content->getFileFieldUrlPath('menu_icon', array(
                    'file' => $item['menu_icon'], 
                    'variant' =>  'article'
                ));
            }
        }
        unset($item);
        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContentsArticles/review', array(
            'title' => $root['name'],
            'items' => $items,
            'count' => $count,
            'indexLocator' => App::getContentLocatorByPid('App.WebContentsArticles.index'),
            'viewLocator' => App::getContentLocatorByPid('App.WebContentsArticles.view'),
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit article'),
                )
            ),
        ));        
    }  
    
    /**
     * MVC ELEMENT
     * 
     * Generates articles list
     * 
     * @return string
     */
    public function index($slug = null) {
        $this->displayOriginComment = true;
        $Content = $this->loadModel('WebContent', true);
        $root = $Content->findFirst(array(
            'conditions' => array(
                'pid' => 'articles',
                'lang' => App::$lang,
            ),
            'fields' => array('WebContent.id', 'WebContent.name')
        ));
        if (!$root) {
            return '';
        }
        $items = $Content->find(array(
            'conditions' => array(
                'WebContent.parent_id' => $root['id'],
                'WebContent.active' => true,
            ),
            'fields' => array(
                'WebContent.id',
                'WebContent.name',
                'WebContent.locator',
                'WebContent.resume',
                'WebContent.date',
                'WebContent.menu_icon',
                'WebContent.aux_01',
            ),
            'order' => array('WebContent.sort DESC'),
            'paginate' => true,
            'limit' => 12,
        ));
        // load snippets and set image full paths
        foreach ($items as &$item) {
            if (!empty($item['menu_icon'])) {
                $item['menu_icon'] = $Content->getFileFieldUrlPath('menu_icon', array(
                    'file' => $item['menu_icon'], 
                    'variant' =>  'article'
                ));
            }
        }
        unset($item);
        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContentsArticles/index', array(
            'items' => $items,
            'slug' => $slug,
            'viewLocator' => App::getContentLocatorByPid('App.WebContentsArticles.view'),
            'Paginator' => $Content->Paginator,
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit article'),
                )                    
            ),
        ));        
    }      
    
    public function view($slug = null) {
        $this->displayOriginComment = true;
        $Content = $this->loadModel('WebContent', true);

        $item = $Content->findFirst(array(
            'conditions' => array(
                'WebContent.locator' => $slug,
                'WebContent.active' => true,
                'WebContent.lang' => App::$lang,
            ),
            'fields' => array(
                'WebContent.id',
                'WebContent.name',
                'WebContent.locator',
                'WebContent.resume',
                'WebContent.text',
                'WebContent.date',
                'WebContent.image_name',
                'WebContent.seo_title',
                'WebContent.seo_description',
                'WebContent.seo_keywords',
                'WebContent.seo_index',
                'WebContent.seo_follow',
                'WebContent.aux_01',
                'WebContent.sort',
            ),
        ));
        if (empty($item)) {
            return App::loadScreen('_404');
        }
        if (!empty($item['image_name'])) {
            $item['image_name'] = $Content->getFileFieldUrlPath('image_name', array(
                'file' => $item['image_name'], 
                'variant' =>  'article'
            ));
        }
//        $item['text'] = App::loadTextSnippets($item['text'], array('params' => array('_content' => $item)));
        $BlockInstance = App::loadModel('App', 'ContentBlockInstance', true);
        $item['text'] = $BlockInstance->loadOwnerInstancesViews('App.WebContent', $item['id']);
        
        // get next items placed after the actual article, if there are no after-items
        // (or not enough) then add some before-items (till the limit is reached)
        $root = $Content->findFirst(array(
            'conditions' => array(
                'WebContent.pid' => 'articles',
                'WebContent.lang' => App::$lang,
            ),
            'fields' => array('WebContent.id'),    
        )); 
        $limit = 2;
        $sortConditions = array(
            'WebContent.sort >' => $item['sort'],
            'WebContent.sort <' => $item['sort'],
        );
        $nextItems = array();
        foreach ($sortConditions as $sortConditionField => $sortConditionValue) {
            $nextItems = array_merge($nextItems, $Content->find(array(
                'conditions' => array(
                    'WebContent.parent_id' => $root['id'],
                    'WebContent.active' => true,
                    'WebContent.id !=' => $item['id'],
                    $sortConditionField => $sortConditionValue,

                ),
                'fields' => array(
                    'WebContent.id',
                    'WebContent.name',
                    'WebContent.locator',
                    'WebContent.resume',
                    'WebContent.date',
                    'WebContent.menu_icon',
                    'WebContent.aux_01',
                ), 
                'order' => 'WebContent.sort DESC',
                'limit' => $limit,
            )));
        }
        $nextItems = array_slice($nextItems, -$limit);
        foreach ($nextItems as &$nextItem) {
            if (!empty($nextItem['menu_icon'])) {
                $nextItem['menu_icon'] = $Content->getFileFieldUrlPath('menu_icon', array(
                    'file' => $nextItem['menu_icon'], 
                    'variant' =>  'article'
                ));                
            }
        }
        unset($nextItem);
        
        App::setSeoTitle($item['seo_title']);
        App::setSeoDescription($item['seo_description']);
        App::setSeoKeywords($item['seo_keywords']);
        App::setSeoIndex($item['seo_index']);
        App::setSeoFollow($item['seo_follow']);
        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContentsArticles/view', array(
            'item' => $item,
            'nextItems' => $nextItems,
            'viewLocator' => App::getContentLocatorByPid('App.WebContentsArticles.view'),
            'indexLocator' => App::getContentLocatorByPid('App.WebContentsArticles.index'),
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit article'),
                )                    
            ),
        ));        
    }
}
