<?php
class Regions extends Controller {
    
    /**
     * Return json encoded array of options for selectbox. 
     * 
     * Can be used with chained country > region > district selectboxes (see http://www.appelsiini.net/projects/chained)
     * 
     * @param string $countryIsoCode2 Country iso code 2, e.g. 'SK', 'CZ'.
     * 
     * @return string Json encoded array of options for selectbox
     */
    public function getSelectOptionsByCountry($countryIsoCode2 = null) {
        App::setLayout(false);
        $options = array();
        if (!empty($this->data['UserProfile']['country'])) {
            $countryIsoCode2 = $this->data['UserProfile']['country'];
        }
        if (empty($countryIsoCode2)){
            return json_encode($options);
        }
        $Region = App::loadModel('App', 'Region', true);
        $options = $Region->getSelectOptionsByCountry($countryIsoCode2);
        return json_encode($options);
    }
}