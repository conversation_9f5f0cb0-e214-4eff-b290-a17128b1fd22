<?php
class Users extends SmartController {

    /** @var User */
    protected $Model;
    
    /**
     * Index users
     */
    public function admin_index() {
        $User = $this->loadModel('User', true);
        $user = App::getUser();
        // do not show to users accounts of hierarchically higher
        $conditions = array(
            'Group.hierarchy >=' => $user['Group']['hierarchy'],
        );
        // account with negative ids are visible only for admins
        if ($user['Group']['pid'] != 'admins') {
            $conditions[] = 'User.id > 0';
        }
        $users = $User->find(array(
            'joins' => array(
                array(
                    'model' => 'Group',
                    'type' => 'left',
                ),
                array(
                    'model' => 'UserProfile',
                    'type' => 'left',
                )
            ),
            'fields' => array(
                'User.id',
                'User.username',
                'User.first_name',
                'User.last_name',
                'User.email',
                'UserProfile.phone',
                'User.run_groups_id',
                'User.rights',
                'User.active',
                'User.created',
                'User.modified',
                'Group.hierarchy',
            ),
            'conditions' => $conditions,
            'order' => 'User.id',
            'paginate' => true,
            'separate' => true,
        ));
        // get groups list
        $Group = $this->loadModel('Group', true);
        $groups = $Group->findList(array(
            'fields' => array('name'),
            'conditions' => array(
                'hierarchy >=' => $user['Group']['hierarchy']
            ),
            'order' => array('hierarchy ASC'),
        ));
        App::setSeoTitle(__a(__FILE__, 'Users'));
        App::setTabClass('st-app-languages-index');
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'List of users'),
            'records' => $users,
            'primaryKey' => 'User.id',
            'columns' => array(
                'User.username' => __a(__FILE__, 'Username'),
                'User.first_name' => __a(__FILE__, 'Firstname'),
                'User.last_name' => __a(__FILE__, 'Lastname'),
                'User.email' => __a(__FILE__, 'E-mail'),
                'UserProfile.phone' => __a(__FILE__, 'Phone'),
                'User.run_groups_id' => __a(__FILE__, 'Group'),
//                'User.rights' => __a(__FILE__, 'Custom rights'),
                'User.active' => __a(__FILE__, 'Active'),
                'User.created' => __a(__FILE__, 'Created'),
                'User.modified' => __a(__FILE__, 'Modified'),
            ),
            'renderFields' => array(
                'User.run_groups_id' => $groups,
                'User.active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'User.active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
            ),
            'Paginator' => $User->Paginator,
            'actions' => array(
                'add' => array(
                    'url' => '/mvc/App/Users/<USER>',
                ),
                'export' => array(
                    'url' => array(
                        'locator' => '/mvc/App/Users/<USER>',
                        'inherit' => array('get' => array(
                            $User->Paginator->getPropertyFilterUrlParam(),
                            $User->Paginator->getPropertySortUrlParam(),
                        )),
                    ),
                ),
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => '/mvc/App/Users/<USER>',
                ),
                'editRights' => array(
                    'url' => '/mvc/App/Users/<USER>',
                    'rights' => array(
                        'User.id !=' => $user['id'],
                        'Group.hierarchy >' => $user['Group']['hierarchy']
                    ),
                    'label' => __a(__FILE__, 'Edit rights'),
                    'title' => __a(__FILE__, 'Edit rights'),
                    'target' => '_blank',
                    'icon' => '<i class="fa fa-key"></i>',
                ),
//                'copy' => array(
//                    'url' => '/mvc/App/Users/<USER>',
//                ),
                'delete' => array(
                    'url' => '/mvc/App/Users/<USER>',
                    'rights' => array('User.id !=' => $user['id']),
                    'confirmMessage' => __a(__FILE__, 'Prosím, potvrďte zmazanie užívateľa :User.username:. POZOR: Všetky naviazané záznamy daného užívateľa (komentáre, wishlisty, ...) budú zmazané tiež, s výnimkou objednávok. Objednávky užívateľa sa zachovajú, no zruší sa ich náväznosť na zmazaného užívateľa.'),
                ),
                'login' => array(
                    'url' => '/mvc/App/Users/<USER>',
                    'label' => __a(__FILE__, 'Login'),
                    'target' => '_blank',
                    'rights' => array(
                        'User.id !=' => $user['id'],
                        'User.active' => true,
                        'Group.hierarchy >' => $user['Group']['hierarchy']
                    ),
                )
            )
        ));
    }

    public function admin_export() {
        $User = $this->loadModel('User', true);

        // do not show to users accounts of hierarchically higher
        $user = App::getUser();
        $conditions = array(
            'Group.hierarchy >=' => $user['Group']['hierarchy'],
        );
        // account with negative ids are visible only for admins
        if ($user['Group']['pid'] != 'admins') {
            $conditions[] = 'User.id > 0';
        }

        // prepare fields
        $fields = $User->getFields(array('qualify' => true));
        $fields[] = 'Group.name as group_name';

        $User->export(
            array(
                'fields' => $fields,
                'avoidFields' => array(
                    'User.password',
                    'User.rights',
                    'User.run_groups_id',
                    'User.reset_code',
                    'User.reset_code_created',
                    'User.modified',
                ),
                'joins' => array(
                    array(
                        'model' => 'Group',
                        'type' => 'left',
                    )
                ),
                'conditions' => $conditions,
                'order' => 'User.id',
                // allow paginator filtering & sorting without limit
                'paginate' => true,
                'limit' => false,
            ),
            array(
                'file' => $this->name,
                //'format' => 'xlsx',
            )
        );
    }

    public function admin_getSelectorItems($keywordOrParentId = null, $ids = null, $ignoreSoftDeletedIds = false) {
        $this->findOptions['fields'] = array(
            'CONCAT(`first_name`, " ", `last_name`) AS `name`',
        );
        $this->findOptions['literals']['fields'] = array(
            'CONCAT(`first_name`, " ", `last_name`) AS `name`',
        );
        $this->findOptions['order'] = 'CONCAT(`first_name`, " ", `last_name`) ASC';
        $this->findOptions['literals']['order'] = 'CONCAT(`first_name`, " ", `last_name`) ASC';
        return parent::admin_getSelectorItems($keywordOrParentId, $ids, $ignoreSoftDeletedIds);
    }

    /**
     * Action to add new user with possible profile
     *
     * @param int $copyId Id of user record to populate copy data. Defaults to NULL.
     */
    public function admin_add($copyId = null) {
        $User = $this->loadModel('User', true);
        $UserProfile = $this->loadModel('UserProfile', true);
        $user = App::getUser();

        if ($this->data) {
            if ($User->createWithProfile($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'New record has been succesfully created'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_update',
                    'args' => array($User->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
//            unset($this->data['User']['password']);
//            unset($this->data['User']['password2']);
        }
        // if copy id is provided then populate new record data from copy record
        elseif ($copyId) {
            $this->data = $User->findFirstBy('User.id', $copyId, array(
                'fields' => array(
                    '*',
                ),
                'joins' => array(
                    array(
                        'model' => 'UserProfile',
                        'type' => 'left',
                    )
                ),
                'separate' => true,
            ));
        }
        else {
            $this->data = array();
        }

        $Group = $this->loadModel('Group', true);
        $groups = $Group->findList(array(
            'fields' => array('name'),
            'conditions' => array(
                'hierarchy >=' => $user['Group']['hierarchy']
            ),
            'order' => array('hierarchy ASC'),
        ));

        App::setSeoTitle(__a(__FILE__, 'New user'));
        return $this->loadView('Users/admin_form', array(
            'title' => __a(__FILE__, 'Add new user'),
            'data' => $this->data,
            'Model' => array(
                'User' => $User,
                'UserProfile' => $UserProfile
            ),
            'lang' => DEFAULT_LANG,
            'actions' => array(
                //'lang' => true
            ),
            'groups' => $groups,
        ));
    }

    public function admin_update($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }

        // get lang of translated fields (if any)
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;

        $User = $this->loadModel('User', true);
        $UserProfile = $this->loadModel('UserProfile', true);
        $user = App::getUser();

        if ($this->data) {
            if ($User->updateWithProfile($this->data, array('lang' => $lang))) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                App::redirect(App::$url);
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
//            unset($this->data['User']['password']);
//            unset($this->data['User']['password2']);
        }
        // if copy id is provided then populate new record data from copy record
        else {
            $this->data = $User->findFirstBy('User.id', $id, array(
                'fields' => array(
                    '*',
                ),
                'joins' => array(
                    array(
                        'model' => 'UserProfile',
                        'type' => 'left',
                    )
                ),
                'separate' => true,
                'lang' => $lang,
            ));
        }

        $Group = $this->loadModel('Group', true);
        $groups = $Group->findList(array(
            'fields' => array('name'),
            'conditions' => array(
                'hierarchy >=' => $user['Group']['hierarchy']
            ),
            'order' => array('hierarchy ASC'),
        ));

        // add username to title and tabTitle
        if (!empty($this->data['User']['username'])) {
            $name = $this->data['User']['username'];
        }
        // - name should ot be empty, if so then it is error so use actual name
        else {
            $name = $User->findFieldBy('username', 'id', $this->data['User']['id']);
        }
        App::setSeoTitle($name);
        return $this->loadView('Users/admin_form', array(
            'title' => __a(__FILE__, 'Edit user &quot;%s&quot;', Sanitize::value($name)),
            'data' => $this->data,
            'Model' => array(
                'User' => $User,
                'UserProfile' => $UserProfile
            ),
            'lang' => $lang,
            'actions' => array(
                //'lang' => true
            ),
            'groups' => $groups,
        ));
    }

    public function admin_updateProfile() {

        $User = $this->loadModel('User', true);
        $UserProfile = $this->loadModel('UserProfile', true);
        $id = App::getUser('id');
        
        // get lang of translated fields (if any)
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;

        if ($this->data) {
            // force id and remove run_groups_id
            $this->data['User']['id'] = $id;
            unset($this->data['User']['run_groups_id']);
            if ($User->updateWithProfile($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'Your profile has been succesfully updated'));
                App::redirect(App::$url);
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
//            unset($this->data['User']['password']);
//            unset($this->data['User']['password2']);
        }
        // if copy id is provided then populate new record data from copy record
        else {
            $this->data = $User->findFirstBy('User.id', $id, array(
                'fields' => array(
                    '*',
                ),
                'joins' => array(
                    array(
                        'model' => 'UserProfile',
                        'type' => 'left',
                    )
                ),
                'separate' => true,
                'lang' => $lang,
            ));
        }

        App::setSeoTitle(__a(__FILE__, 'My profile'));
        App::setTabIcon('<i class="fa fa-user"></i>');
        return $this->loadView('Users/admin_form', array(
            'title' => $this->data['User']['first_name'] . ' ' . $this->data['User']['last_name'],
            'data' => $this->data,
            'Model' => array(
                'User' => $User,
                'UserProfile' => $UserProfile
            ),
            'lang' => $lang,
            'actions' => array(
                //'lang' => true
            ),
            'editMy' => true,
        ));
    }

    public function admin_editRights($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        $User = $this->loadModel('User', true);
        if ($this->data) {
            if ($User->updateRights($id, $this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'User rights has been succesfully updated'));
                App::redirect(App::$url);
            }
            App::setErrorMessage(__a(__FILE__, 'Update of user rights has failed'));
        }
        // get editor group
        $editor = App::getUser();
        $editorGroup = $editor['Group']['pid'];
        $editorCustomRights = (array)$editor['rights'];
        // get the user group pid and rights
        $user = $User->findFirstBy('User.id', $id, array(
            'joins' => array(
                array(
                    'model' => 'Group',
                    'type' => 'left',
                ),
            ),
            'fields' => array(
                'User.first_name',
                'User.last_name',
                'User.rights',
                'Group.pid',
                'Group.hierarchy',
            ),
            'separate' => array('Group'),
        ));
        if (
            empty($user)
            // do not allow to edit user rights for the same and lower hierarchy
            || $user['Group']['hierarchy'] <= $editor['Group']['hierarchy']
        ) {
            App::setErrorMessage(__a(__FILE__, 'Invalid record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        $userGroup = $user['Group']['pid'];
        $userCustomRights = array();
        if (!empty($user['rights'])) {
            $userCustomRights = (array)json_decode($user['rights'], true);
        }
        // generate smart form fields and data
        $modules = App::getModules();
        $fields = array();
        $this->data = array();
        foreach ($modules as $module) {
            // read rights
            try {
                $labeledRights = App::getModuleRights($module, true);
                $rights = App::getModuleRights($module);
            }
            // if module has no rights defined then continue to next one
            catch (Throwable $e) {
                continue;
            }
            // - admin labeled rights defines headers and labels
            $adminRights = $labeledRights['admins'];
            // - editors rights defines rights range that can be edited by actual authenticated user (editor)
            $editorRights = Arr::mergeRecursive(
                (array)Sanitize::value($rights[$editorGroup]),
                (array)Sanitize::value($editorCustomRights[$module])
            );
            // - user rights defines which rights are default and which of the edited
            // rights are set and which are not set
            $userGroupRights = (array)Sanitize::value($rights[$userGroup]);
            $userRights = Arr::mergeRecursive(
                $userGroupRights,
                (array)Sanitize::value($userCustomRights[$module])
            );
            foreach ($adminRights as $subjectType => $subjectNames) {
                // if editor has no rights defined in this subjectType
                // (means he cannot edit these rights too) then skip it
                if (empty($editorRights[$subjectType])) {
                    continue;
                }
                foreach ($subjectNames as $subjectName => $label) {
                    // nonassociative items are headers definitions
                    if (is_int($subjectName)) {
                        if (!is_array($label)) {
                            $label = array('h2' => $label);
                        }
                        // check previos item if it is heading of the same or higher (more nested)
                        // level and if so then remove it
                        if (!empty($fields)) {
                            $previousHeading = array_intersect_key(
                                end($fields),
                                array('h1' => true, 'h2' => true, 'h3' => true, 'h4' => true, 'h5' => true, 'h6' => true)
                            );
                            if (!empty($previousHeading)) {
                                $previousHeadingLevel = array_keys($previousHeading);
                                $previousHeadingLevel = $previousHeadingLevel[0];
                                $headingLevel = array_keys($label);
                                $headingLevel = $headingLevel['0'];
                                if ($headingLevel <= $previousHeadingLevel) {
                                    array_pop($fields);
                                }
                            }
                        }
                        $fields[] = $label;
                        continue;
                    }
                    // skip following cases
                    if (
                        // only rights with defined labels in admin rights are exposed for editing
                        !is_string($label)
                        ||
                        // if editor has (explicitly) no rights for this subjectName or
                        // both editor has no rights and user has no implicit (group) rights
                        // then skip it. BUT if the user has implicit (group) rights for subjectName
                        // and editor has no rights BUT not explicitly disallowed
                        // then let the editor to edit it. This can happen if user group has rights
                        // for some weaker/constrained method and the editor group does not have rights
                        // for it (to not bother him in admin) as editor group has rights for full functionality method
                        // NOTE: If you change this condition then change it also in User::updateRights()
                        empty($editorRights[$subjectType][$subjectName])
                        &&
                        (
                            isset($editorCustomRights[$module][$subjectType][$subjectName]) // = explicitly
                            || empty($userGroupRights[$subjectType][$subjectName])
                        )
                    ) {
                        continue;
                    }
                    // check what is the actual rights state (allowed or disabled)
                    $subjectNameField = str_replace('.', '/', $subjectName);
                    $this->data[$module][$subjectType][$subjectNameField] =
                        isset($userRights[$subjectType][$subjectName])
                        && !empty($userRights[$subjectType][$subjectName]);
                    // check if this rights are set TRUE in user group rights
                    // (means these are default rights if no customization is done)
                    $labelClass = '';
                    if (!empty($userGroupRights[$subjectType][$subjectName])) {
                        $labelClass = 'default-rights';
                    }
                    if (
                        empty($userGroupRights[$subjectType][$subjectName]) !== empty($userRights[$subjectType][$subjectName])
                    ) {
                        $labelClass .= ' changed-rights';
                    }
                    // set form field
                    $fields[] = array(
                        'label' => array(
                            'text' => $label,
                            'class' => trim($labelClass),
                        ),
                        'field' => $module . '.' . $subjectType . '.' . $subjectNameField,
                        'template' => ':i::l:',
                        'type' => 'checkbox',
                    );
                }
            }
        }
        // check last item in fields if it is heading and if so then remove it
        if (!empty($fields)) {
            $lastItem = array_intersect_key(
                end($fields),
                array('h1' => true, 'h2' => true, 'h3' => true, 'h4' => true, 'h5' => true, 'h6' => true)
            );
            if (!empty($lastItem)) {
                array_pop($fields);
            }
        }

        App::setSeoTitle(__a(__FILE__, 'Rights of user %s', $user['first_name'] . ' ' . $user['last_name']));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Rights of user %s', $user['first_name'] . ' ' . $user['last_name']),
            'class' => 'edit-rights',
            'columns' => 1,
            'data' => $this->data,
            'showAffix' => array('autogenerate' => array('h1')),
            'fields' => $fields,
            'tabsToReloadAfterSave' => null,
        ));
    }

    public function admin_delete($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }

        $User = $this->loadModel('User', true);
        $User->deleteRecursively($id, array(
            'nullifyModels' => array(
                'Eshop.EshopOrder',
            )
        ));
        App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }

    /**
     * Switches actually logged user to provided $userId
     *
     * Typical usage is when admin need to be logged as another user
     *
     * @param integer $userId  Id of user to switch. Actual user is replaced by
     *      another user with $userId.
     */
    public function admin_switchToAnother($userId = null) {
        if (Validate::intNumber($userId)) {
            if (App::reinitUser($userId)) {
                $refererUrl = App::parseUrl(App::getRefererUrl('/'));
                $redirectLocator = '/';
                if (
                    !App::$adminOnly // in case of adminOnly = true just stay on '/'
                    && (
                        $refererUrl['type'] === 'slug'
                        && $refererUrl['slug'] === App::$adminSlug
                        ||
                        $refererUrl['type'] === 'mvc'
                    )
                    && App::getUserRights('App', 'screen', App::$adminSlug)
                ) {
                    $redirectLocator = '/' . App::$adminSlug;
                }
                App::redirect(App::getUrl($redirectLocator), array('js' => 'top'));
            }
            App::setErrorMessage(__a(__FILE__, 'User switch has failed'));
        }
        else {
            App::setErrorMessage(__a(__FILE__, 'User identificator is not valid'));
        }
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/_error',
            'source' => App::$requestSource,
        ))), array('js' => 'top'));
    }

    /**
     * Switches actually logged user to original one (if any).
     *
     * Typical usage is when admin need to be logged as another user and then he
     * needs to swith back into original user
     */
    public function switchToOriginal() {
        if (App::reinitUser('original')) {
            $redirectLocator = '/';
            if (
                !App::$adminOnly // in case of adminOnly = true just stay on '/'
                && App::getUserRights('App', 'screen', App::$adminSlug)
            ) {
                $redirectLocator = '/' . App::$adminSlug;
            }
            App::redirect(App::getUrl($redirectLocator), array('js' => 'top'));
        }
        App::setErrorMessage(__a(__FILE__, 'User switch has failed'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/_error',
            'source' => App::$requestSource,
        ))), array('js' => 'top'));
    }

    /**
     * Generates switch link html to login back from switched user to original user
     */
    public function switchToOriginalLink() {
        if (!App::getOriginalUser()) {
            return '';
        }
        return $this->loadView('Users/switchToOriginalLink');
    }

    public function login() {
        $this->displayOriginComment = true;
        App::requireHttps();
        return $this->loadView('Users/login', null, $this->data);
    }

    public function logout() {
        App::logout();
        App::redirect(App::getUrl(App::getLoginSlug()));
    }

    public function register() {
        $this->displayOriginComment = true;
        App::requireHttps();
        $this->loadModel('User');
        $User = new User();
        $UserProfile = $this->loadModel('UserProfile', true);

        if (!empty($this->data)) {
            if ($User->createWithProfile($this->data, array(
                'login' => true,
                'alternative' => 'frontend',
                'groupPid' => 'clients',
            ))) {
                App::setMessage(__(__FILE__, 'Registration was successful.'));
                App::redirect(App::getLoginTargetUrl(array(
                    'remove' => true,
                    'defaultUrl' => App::getContentLocatorByPid('App.Users.update'),
                )));
            }
            $userErrors = $User->getErrors();
            if (!empty($userErrors['_processing'])) {
                App::logError('Registration has failed because of following processing error(s)', array(
                    'var' => $userErrors['_processing'],
                    'email' => true,
                ));
                App::setMessage(__(__FILE__, 'Registration has failed. Please try again or contact administrator.'));
            }
            else {
                App::setMessage(__(__FILE__, 'Registration has failed. Check form for error messages.'));
            }
        }

        if (!empty($this->data['UserProfile']['birthdate'])) {
            $this->data['UserProfile']['birthdate'] = date('d.m.Y', strtotime($this->data['UserProfile']['birthdate']));
        }
        return $this->loadView('Users/profileForm', array(
            'data' => $this->data,
            'required' => array(
                'User' => $User->getNotEmptyFields(array(
                    'alternative' => 'frontend', 
                    'on' => 'create',
                    'data' => Sanitize::value($this->data['User']),
                )),
                'UserProfile' => $UserProfile->getNotEmptyFields(array(
                    'alternative' => 'frontend', 
                    'on' => 'create',
                    'data' => Sanitize::value($this->data['UserProfile']),
                ))
            ),
            'errors' => array(
                'User' => $User->getErrors(),
                'UserProfile' => $UserProfile->getErrors()
            ),
        ));
    }

    public function update() {
        $this->displayOriginComment = true;
        $this->loadModel('User');
        $User = new User();
        $UserProfile = $this->loadModel('UserProfile', true);
        $userId = App::getUser('id');

        if (!empty($this->data)) {
            if ($User->updateWithProfile($this->data, array(
                'alternative' => 'frontend',
            ))) {
                App::setMessage(__(__FILE__, 'Update was successful.'));
                App::redirect(App::$url);
            }
            $userErrors = $User->getErrors();
            if (!empty($userErrors['_processing'])) {
                App::setMessage(__(__FILE__, 'Update has failed. Please try again or contact administrator.'));
            }
            else {
                App::setMessage(__(__FILE__, 'Update has failed. Check form for error messages.'));
            }
        }
        else {
            $this->data = $User->findFirstBy('User.id', $userId, array(
                'fields' => '*',
                'joins' => array(
                    array(
                        'model' => 'UserProfile',
                        'type' => 'left'
                    )
                ),
                'separate' => true,
            ));
            unset($this->data['User']['password']);
            $MailerContact = App::loadModel('Mailer', 'MailerContact', true);
            $mailerContactData = $MailerContact->findAll(array(
                'conditions' => array('MailerContact.run_users_id' => $userId),
                'first' => true,
                'fields' => array(
                    'MailerContact.id',
                    'MailerContact.first_name',
                ),
            ));
            if (!empty($mailerContactData)) {
                $this->data['MailerContact']['group_ids'] = $mailerContactData['group_ids'];
            }
        }
        // id is not sent in form data so set it always explicitly
        $this->data['User']['id'] = $userId;

        if (!empty($this->data['UserProfile']['birthdate'])) {
            $this->data['UserProfile']['birthdate'] = date('d.m.Y', strtotime($this->data['UserProfile']['birthdate']));
        }

        // get groups list (if user can be under more mailer groups)
        if (App::getConfig('Mailer', 'multipleSubscribe') && !empty($mailerContactData)) {
            $MailerGroup = App::loadModel('Mailer', 'MailerGroup', true);
            $mailerGroups = $MailerGroup->findList(array(
                'fields' => array('name'),
                'order' => array('id'),
            ));
        } else {
            $mailerGroups = array();
        }

        return $this->loadView('Users/profileForm', array(
            'data' => $this->data,
            'mailerGroups' => $mailerGroups,
            'required' => array(
                'User' => $User->getNotEmptyFields(array(
                    'alternative' => 'frontend', 
                    'on' => 'update',
                    'data' => Sanitize::value($this->data['User']),
                )),
                'UserProfile' => $UserProfile->getNotEmptyFields(array(
                    'alternative' => 'frontend', 
                    'on' => 'update',
                    'data' => Sanitize::value($this->data['UserProfile']),
                )),
            ),
            'errors' => array(
                'User' => $User->getErrors(),
                'UserProfile' => $UserProfile->getErrors()
            ),
        ));
    }

    public function forgotPassword() {
        $this->displayOriginComment = true;
        $this->loadModel('User');
        $User = new User();

        if (!empty($this->data)) {
            try {
                if ($User->generateNewPassword($this->data['username'])) {
                    App::setMessage(__(__FILE__, 'New password was sent to you e-mail.'));
                }
                else {
                    App::setErrorMessage(__(__FILE__, 'Error. Please try again or contact administrator.'));
                }
            }
            catch (Exception_User_Unexisting $ex) {
                App::setMessage(__(__FILE__, 'Provided user does not exist in system.'));
            }
            catch (Exception_User_NoEmail $ex) {
                App::setMessage(__(__FILE__, 'Provided user has no e-mail address specifed. We can not send you new generated password.'));
            }
            catch (Exception_User_InvalidEmail $ex) {
                App::setMessage(__(__FILE__, 'Provided user has invalid e-mail address specifed. We can not send you new generated password.'));
            }
        }

        return $this->loadView(
            'Users/forgotPassword',
            array(
                'errors' => $User->getErrors(),
                'data' => $this->data
            )
        );
    }
}

