<?php

class ContentBlockInstances extends SmartController {
    
    protected $model = 'ContentBlockInstance';

    /**
     * Allow the model methods hinting in IDE
     * @var ContentBlockInstance
     */
    protected $Model;
    
    /**
     * AJAX
     * 
     * @param string $contentBlockModel Content block qualified model name
     * @param string $ownerModel 
     * @param int $ownerId
     * 
     * @return string JSON
     */
    public function admin_loadAdminView($contentBlockModel = null, $ownerModel = null, $ownerId = null) {
        App::setLayout('App', 'json');
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        $Response->data = array();
        try {
            $view = $this->Model->loadAdminView(array(
                'contentBlockModel' => $contentBlockModel,
                'ownerModel' => $ownerModel,
                'ownerId' => $ownerId,
            ));
            $view['viewHtml'] .= 
                App::getCssLinks() .
                App::getCssCode(true) .
                App::getJsConfigCode(true) .
                App::getJsI18nLinks() .
                App::getHeadJsLinks() .
                App::getHeadHtmlCode() .
                App::getJsLinks() .
                App::getJsCode(true) .
                App::getBodyEndHtmlCode();
            $Response->data = $view;
        } 
        catch (Throwable $e) {
            $Response->success = false;
            $Response->message = $e->getMessage();
            App::logError('Content block admin view load failure', array(
                'var' => $e,
                'email' => true,
            ));
        }
        return $Response->getJson();
    }  
    
    /**
     * Generates frontend preview of specified content block instance.
     * 
     * ATTENTION: This should be inserted to admin views by iframe - to avoid css conflicts.
     * 
     * ATTENTION: Content blocks previews are rendered by FormHelper::contentBlocks() 
     * in iframes using contentBlockPreview layout. This is usefull to set specific 
     * styles (inside body#content-block-preview-layout) for content blocks. E.g. for 
     * blocks with height set by css vh units (in iframe 100vh = iframe height = previewed 
     * block/page height) the height must be restricted by max-height in px units.
     * 
     * NOTE: This is an admin action but the 'admin_' prefix is not used - to avoid
     * implicit set of admin files (css) and so to make it display as an frontend action
     * as it serves to display frontend preview of content block instance
     * 
     * @param int $id Content block instance id
     * 
     * @return string HTML
     */
    public function loadInstanceView($id = null) {
        App::setLayout('App', 'contentBlockPreview');
        App::setCssFiles(array(
            '/app/css/main.css',
        ));
        $instance = $this->Model->findFirstBy($this->Model->getPropertyPrimaryKey(), $id, array(
            'fields' => array('owner_model', 'owner_id'),
        ));
        list($ownerModuleName, $ownerModelName) = explode('.', $instance['owner_model']);
        $OwnerModel = App::loadModel($ownerModuleName, $ownerModelName, true);
        return $this->Model->loadInstanceView($id, array(
            'ownerModel' => $instance['owner_model'],
            'ownerId' => $instance['owner_id'],
            'ownerRecord' => $OwnerModel->findFirstBy($OwnerModel->getPropertyPrimaryKey(), $instance['owner_id']),
        ));
    }
    
    /**
     * Generates frontend preview of content block instance of specified owner.
     * 
     * ATTENTION: This should be inserted to admin views by iframe - to avoid css conflicts.
     * 
     * ATTENTION: Content blocks previews are rendered by FormHelper::contentBlocks() 
     * in iframes using contentBlockPreview layout. This is usefull to set specific 
     * styles (inside body#content-block-preview-layout) for content blocks. E.g. for 
     * blocks with height set by css vh units (in iframe 100vh = iframe height = previewed 
     * block/page height) the height must be restricted by max-height in px units.
     * 
     * NOTE: This is an admin action but the 'admin_' prefix is not used - to avoid
     * implicit set of admin files (css) and so to make it display as an frontend action
     * as it serves to display frontend preview of owner content block instances
     * 
     * @param string $ownerModel 
     * @param int $ownerId
     * 
     * @return string HTML
     */
    public function getOwnerInstancesView($ownerModel = null, $ownerId = null) {
        App::setLayout('App', 'contentBlockPreview');
        App::setCssFiles(array(
            '/app/css/main.css',
        ));
        list($ownerModuleName, $ownerModelName) = explode('.', $ownerModel);
        $OwnerModel = App::loadModel($ownerModuleName, $ownerModelName, true);
        return $this->Model->loadOwnerInstancesViews($ownerModel, $ownerId, array(
            'ownerRecord' => $OwnerModel->findFirstBy($OwnerModel->getPropertyPrimaryKey(), $ownerId),
        ));        
    }
    
    /**
     * Deletes file of specified file field and content block instance
     * 
     * @param string $fileField
     * @param int $id Content block instance id
     */
    public function admin_deleteInstanceFile($fileField = null, $id = null) {
        if (!$fileField || !$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record fileField and/or content block instance id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        if (!$this->Model->deleteInstanceFile($fileField, $id)) {
            App::setErrorMessage(__a(__FILE__, 'Invalid file field or content block instance id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        App::setSuccessMessage(__a(__FILE__, 'File has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(App::$adminSlug)));
    }
}
