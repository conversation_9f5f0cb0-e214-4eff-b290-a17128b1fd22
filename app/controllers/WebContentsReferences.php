<?php
App::loadController('App', 'WebContents');
/**
 * @deprecated - Use functionality defined in Reference model and References controller
 */
class WebContentsReferences extends WebContents {
    // It is the class name what is here important as WebContents::$sectionPid is
    // set according to it, see WebContents::__construct()
    //
    // All this serves to correct matching web content records and sections in Admin
    
    /**
     * @deprecated - Use functionality defined in Reference model and References controller
     * 
     * MVC ELEMENT
     */
    public function slider() {
        $this->displayOriginComment = true;
        $Content = App::loadModel('App', 'WebContent', true);
        $items = $Content->findInTree(
            array(
                'conditions' => array(
                    'WebContent.pid' => 'references',
                    'WebContent.lang' => App::$lang,
                ),
            ),
            array(
                'conditions' => array(
                    'WebContent.active' => true
                ),
                'fields' => array(
                    'WebContent.id',
                    'WebContent.name',
                    'WebContent.locator',
                    'WebContent.aux_01 AS info',
                    'WebContent.resume AS text',
                    'WebContent.menu_icon AS image',
                ),
                'order' => 'sort DESC',
                'depth' => 1,
            )
        );
        
        foreach ($items as &$item) {
            if (!empty($item['image'])) {
                $item['image'] = $Content->getFileFieldUrlPath('menu_icon', array('file' => $item['image'], 'variant' =>  'reference'));
            }
            $item['info'] = nl2br($item['info']);
        }
        unset($item);
        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContentsReferences/slider', array(
            'items' => $items,
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit reference'),
                )
            ),
        ));        
    }    
    
    /**
     * @deprecated - Use functionality defined in Reference model and References controller
     * 
     * MVC ELEMENT
     * 
     * Generates slider
     * 
     * @param int $this->params['sliderId']|$this->params['slider-id'] Id of slider content. 
     *      Defaults to actual content['slider_id']
     * @param string $this->params['type'] Type slider to be used. Possible values are 'bxslider', 'wowslider' and 'owlcarousel'.
     *      Defaults to 'bxslider'.
     * @param string $this->params['class'] Additional css class. Defaults to NULL.
     * @param $this->params The rest of params is passed as options to Html slider method
     *      specified by $this->params['type'].
     * 
     * @return string
     */
    public function sliderMenu() {
        $this->displayOriginComment = true;
        $defaults = array(
            'sliderId' => null,
            'type' => 'bx',
            'class' => null,
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        // keep options for Html::xyzSlider() aside of other options  
        $sliderId = $options['sliderId'];
        unset($options['sliderId']);
        $type = strtolower($options['type']);
        unset($options['type']);
        $class = $options['class'];
        unset($options['class']);
        // normalize id
        if (empty($sliderId)) {
            if (!empty($this->params['_content']['slider_id'])) {
                $sliderId = $this->params['_content']['slider_id'];
            }
            else {                
                $content = App::getPropertyContent();
                $sliderId = Sanitize::value($content['slider_id']);
            }
        }
        // if no slider found then do nothing
        if (empty($sliderId)) {
            return '';
        }
        $Content = App::loadModel('App', 'WebContent', true);
        $slides = $Content->findInTree(
            $sliderId,
            array(
                'conditions' => array(
                    'WebContent.active' => true,
                    'WebContent.menu_icon IS NOT NULL',
                ),
                'fields' => array(
                    'WebContent.id',
                    'WebContent.menu_icon AS image',
                    'WebContent.locator',
                    'WebContent.name',
                ),
                'order' => array('WebContent.sort ASC'),
                'depth' => 1,
            )
        );
        // if no slides found then do nothing
        if (empty($slides)) {
            return '';
        }
        // load snippets and set image full paths
        foreach ($slides as &$slide) {
            if (!empty($slide['image'])) {
                $slide['image'] = $Content->getFileFieldUrlPath('menu_icon', array(
                    'file' => $slide['image'],
                    'variant' => 'reference'
                ));
            }
        }
        unset($slide);
        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContentsReferences/sliderMenu', array(
            'items' => $slides,
            'type' => $type,
            'class' => $class,
            'options' => $options,
            'sliderId' => $sliderId,
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit reference'),
                )
            ),
        ));
    }    
}
