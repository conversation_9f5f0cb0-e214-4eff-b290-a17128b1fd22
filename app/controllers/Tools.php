<?php
/**
 * @class Tools
 * A simple application controller extension
 */
class Tools extends Controller {
    
    public function __construct(){
        parent::__construct();
        App::setLayout('App', 'admin');
        
        // set icon for tabs in admin
        // 
        // Tools methods does not have prefix admin_ for sake of simplicity as they can be called also from url.
        // But all of them are used in admin so apply admin icon to all methods.
        $icon = App::getAdminIcon($this->module, $this->name);
        if (!empty($icon)) {
            App::setTabIcon($icon);
        }
    }
    
    public function menu() {
        return $this->loadView('Tools/menu'); //@todo - make this part of System menu where tools menu will be generated automatically
    }
        
    
    public function displayInfo() {
        $Cart = null;
        try {
            $Cart = App::loadModel('Eshop', 'EshopCart', true);
        }
        catch (Throwable $e) {}
        $info = 'PHP_VERSION_ID: ' . PHP_VERSION_ID . '<br>';
        $info .= 'ROOT: ' . ROOT . '<br>';
        $info .= 'Max execution time: ' . ini_get('max_execution_time') . '<br>';
        $time = ini_get('max_execution_time');
        $time1 = $time + 1;
        ini_set('max_execution_time', $time1);
        $info .= 'New max execution time (' . $time1 . '): ' . ini_get('max_execution_time') . '<br>';
        $time2 = $time - 1;
        ini_set('max_execution_time', $time2);
        $info .= 'New max execution time (' . $time2 . '): ' . ini_get('max_execution_time') . '<br>';
        $info .= 'Memory limit: ' . ini_get('memory_limit') . '<br>';
        $memory = ini_get('memory_limit');
        preg_match('/(\d+)(\w?)/', $memory, $match);
        $memory1 = ($match[1] + 1) . $match[2];
        ini_set('memory_limit', $memory1);
        $info .= 'New memory limit (' . $memory1 . '): ' . ini_get('memory_limit') . '<br>';
        $memory2 = ($match[1] - 1) . $match[2];
        ini_set('memory_limit', $memory2);
        $info .= 'New memory limit (' . $memory2 . '): ' . ini_get('memory_limit') . '<br>';
        $info .= 'Server IP: ' . $_SERVER['SERVER_ADDR'] . '<br>';
        $info .= 'Remote IP: ' . $_SERVER['REMOTE_ADDR'] . '<br>';
        $cartProducts = $Cart ? $Cart->getProducts() : 'Model EshopCart sa nepodarilo načítať';
        $info .= 'Obsah košíka: <pre>' . print_r($cartProducts, true) . '</pre><br>';
        $info .= '$_COOKIE: <pre>' . print_r($_COOKIE, true) . '</pre><br>';
        $info .= '$_SESSION: <pre>' . print_r($_SESSION, true) . '</pre><br>';
        $info .= '$_SERVER: <pre>' . print_r($_SERVER, true) . '</pre><br>';
        Html::startCapture();
        phpinfo();
        $info .= Html::endCapture();
        App::setSeoTitle(__d(__FILE__, 'Info'));
        return $info;
    }
    
    public function createTable() {
        if (!empty($this->data['model'])) { 
            $model = Str::explode('.', $this->data['model'], array('keys' => 'module.name'));
            $Model = App::loadModel($model['module'], $model['name'], true);
            try {
                if ($Model->createTable()) {
                    App::setSuccessMessage(__d(__FILE__, 'Table of :model: has been succesfully created', $this->data));
                }
                else {
                    App::setErrorMessage(__d(__FILE__, 'Table creation of :model: has failed', $this->data));
                }
            } 
            catch (Throwable $e) {
                App::setErrorMessage(
                    __d(
                        __FILE__, 
                        'Table creation of :model: has failed with following exception: %s', 
                        $this->data, 
                        $e->getMessage()
                    )
                );
            }
            if (!empty($this->data['loadInitialRecords'])) {
                try {
                    $Model->loadInitialRecords();
                    App::setSuccessMessage(__d(__FILE__, 'Records of :model: has been succesfully loaded', $this->data));
                } 
                catch (Throwable $e) {
                    App::setErrorMessage(__d(__FILE__, 'Records loading of :model: has failed because of following error %s', $this->data, $e->getMessage()));
                }
            }
        }
        // create list of model select box options
        $options = array();
        $models = App::getModels();
        $errors = array();
        foreach ($models as $module => $moduleModels) {
            if (!App::hasModule($module, array('initialized' => true))) {
                continue;
            }
            foreach ($moduleModels as $model) {
                // maybe some model need some params in __construct()
                // catch exceptions and skip models where it is not possible 
                // to get instance
                try {
                    App::loadModel($module, $model);
                    $Class = new ReflectionClass($model);
                    if ($Class->isAbstract()) {
                        $errors[] = '>> ' . __d(__FILE__, 'Model %s is defined as abstract class', $module . '.' . $model);
                        continue;
                    }
                    $Model = new $model;
                } 
                catch (Throwable $e) {
                    $errors[] = '>> ' . __d(__FILE__, 'Loading of model %s has failed with exception: %s', $module . '.' . $model, $e->getMessage());
                    continue;
                }
                // skip classes which are not derived from Model class
                if (!$Model instanceof Model) {
                    $errors[] = '>> ' . __d(__FILE__, 'Model %s is not derived from Model class', $module . '.' . $model);
                    continue;
                }
                // skip models without defined table and scheme
                if (
                    !$Model->getPropertyTable()
                    || !$Model->getPropertySchema()
                ) {
                    $errors[] = '>> ' . __d(__FILE__, 'Model %s has either no table and/or schema defined', $module . '.' . $model);
                    continue;
                }
                $options[$module . '.' . $model] = $module . '.' . $model;
            }
        }
        App::setSeoTitle(__d(__FILE__, 'Create table'));
        return Html::smartForm(array(
            'method' => 'get',
            'data' => $this->data,
            'title' => __d(__FILE__, 'Create table'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => 4,
            'fields' => array(
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    'model' => array(
                        'label' => __d(__FILE__, 'Model'),
                        'type' => 'select',
                        'options' => $options,
                    ),
                    'loadInitialRecords' => array(
                        'label' => __d(__FILE__, 'Load records'),
                        'type' => 'checkbox',
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                'errors' => array(
                    'label' => __d(__FILE__, 'Excluded models'),
                    'type' => 'display',
                    'explicitValue' => implode('<br>', $errors),
                ),
                array('/row'),
            )
        ));
    }
    
    public function updateTableFields() {
        if (isset($this->data['model'])) { 
            // get model(s) to update table(s)
            if (empty($this->data['model'])) {
                $models = App::getModels();
            }
            else {
                $modelParts = Str::explode('.', $this->data['model']);
                if (count($modelParts) === 1) {
                    $models = array($modelParts[0] => App::getModels($modelParts[0]));
                }
                else {
                    $models = array($modelParts[0] => array($modelParts[1]));
                }
            }
            // update model(s) table(s)
            foreach ($models as $module => $moduleModels) {
                if (!App::hasModule($module, array('initialized' => true))) {
                    continue;
                }
                foreach ($moduleModels as $model) {
                    try {
                        // maybe some model need some params in __construct()
                        // catch exceptions and skip models where it is not possible 
                        // to get instance
                        App::loadModel($module, $model);
                        $Class = new ReflectionClass($model);
                        if ($Class->isAbstract()) {
                            App::setErrorMessage(__d(__FILE__, 'Model %s is defined as abstract class', $module . '.' . $model));
                            continue;
                        }
                        $Model = new $model;
                        // skip classes which are not derived from Model class
                        if (!$Model instanceof Model) {
                            App::setErrorMessage(__d(__FILE__, 'Model %s is not derived from Model class', $module . '.' . $model));
                            continue;
                        }
                        // skip models without defined table and scheme
                        if (
                            !$Model->getPropertyTable()
                            || !$Model->getPropertySchema()
                        ) {
                            App::setErrorMessage(__d(__FILE__, 'Model %s has either no table and/or schema defined', $module . '.' . $model));
                            continue;
                        }
                        $addedFields = $Model->updateTableFields(!empty($this->data['autofill']));
                    } 
                    catch (Throwable $e) {
                        App::setErrorMessage(__d(__FILE__, 'Updating of model %s has failed with exception: %s', $module . '.' . $model, $e->getMessage()));
                        continue;
                    }
                    if (empty($addedFields)) {
                        App::setInfoMessage(__d(__FILE__, 'No new fields were added in table %s', $Model->getPropertyTable()));
                    }
                    else {
                        App::setSuccessMessage(__d(__FILE__, 'In table %s were added fields: %s', $Model->getPropertyTable(), implode(', ', $addedFields)));
                    }
                }
            }
        }
        // create list of model select box options
        $options = array();
        $models = App::getModels();
        $errors = array();
        foreach ($models as $module => $moduleModels) {
            if (!App::hasModule($module, array('initialized' => true))) {
                continue;
            }
            $options[$module] = __d(__FILE__, 'All in %s', $module);
            foreach ($moduleModels as $model) {
                // maybe some model need some params in __construct()
                // catch exceptions and skip models where it is not possible 
                // to get instance
                try {
                    App::loadModel($module, $model);
                    $Class = new ReflectionClass($model);
                    if ($Class->isAbstract()) {
                        $errors[] = '>> ' . __d(__FILE__, 'Model %s is defined as abstract class', $module . '.' . $model);
                        continue;
                    }
                    $Model = new $model;
                } 
                catch (Throwable $e) {
                    $errors[] = '>> ' . __d(__FILE__, 'Loading of model %s has failed with exception: %s', $module . '.' . $model, $e->getMessage());
                    continue;
                }
                // skip classes which are not derived from Model class
                if (!$Model instanceof Model) {
                    $errors[] = '>> ' . __d(__FILE__, 'Model %s is not derived from Model class', $module . '.' . $model);
                    continue;
                }
                // skip models without defined table and scheme
                if (
                    !$Model->getPropertyTable()
                    || !$Model->getPropertySchema()
                ) {
                    $errors[] = '>> ' . __d(__FILE__, 'Model %s has either no table and/or schema defined', $module . '.' . $model);
                    continue;
                }
                $options[$module . '.' . $model] = $module . '.' . $model;
            }
        }
        App::setSeoTitle(__d(__FILE__, 'Update table fields'));
        return Html::smartForm(array(
            'method' => 'get',
            'data' => $this->data,
            'title' => __d(__FILE__, 'Update table fields'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => array(3, 1, 8),
            'fields' => array(
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    'model' => array(
                        'label' => __d(__FILE__, 'Model'),
                        'type' => 'select',
                        'options' => $options,
                    ),
                    'autofill' => array(
                        'label' => __d(__FILE__, 'Auto fill'),
                        'type' => 'checkbox',
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                'errors' => array(
                    'label' => __d(__FILE__, 'Excluded models'),
                    'type' => 'display',
                    'explicitValue' => implode('<br>', $errors),
                ),
                array('/row'),
            )
        ));
    }
    
    public function updateSettings() {
        $Tool = $this->loadModel('Tool', true);
        if (!empty($this->data['module'])) { // @todo - make the validation in model Tool
            if ($Tool->updateSettings($this->data['module'])) {
                App::setSuccessMessage(__d(__FILE__, 'Settings of module :module: has been succesfully updated', $this->data));
            }
            else {
                App::setErrorMessage(__d(__FILE__, 'Settings update of module :module: has failed', $this->data));
            }
        }
        App::setSeoTitle(__d(__FILE__, 'Update settings'));
        return Html::smartForm(array(
            'method' => 'get',
            'data' => $this->data,
            'title' => __d(__FILE__, 'Update settings'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => 4,
            'fields' => array(
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    'module' => array(
                        'label' => __d(__FILE__, 'Module'),
                        'type' => 'select',
                        'options' => App::getModules(),
                    ),
                array('/row'),
            )
        ));
    }
    
    public function updateContents() {
        $Tool = $this->loadModel('Tool', true);
        $log = '';
        if (!empty($this->data['module'])) {  // @todo - make the validation in model Tool
            $result = $Tool->updateContents($this->data['module'], $this->data['pid'], $this->data['force']);
            if ($result['success']) {
                if (empty($this->data['pid'])) {
                    App::setSuccessMessage(__d(__FILE__, 'Contents of :module: have been succesfully updated', $this->data));    
                }
                else {
                    App::setSuccessMessage(__d(__FILE__, 'Content :pid: from :module: have been succesfully updated', $this->data));    
                }
            }
            else {
                if (empty($this->data['pid'])) {
                    App::setErrorMessage(__d(__FILE__, 'Update of :module: contents has failed', $this->data));
                }
                else {
                    App::setErrorMessage(__d(__FILE__, 'Update of content :pid: from :module: has failed', $this->data));
                }
            }
            if (!empty($result['log'])) {
                App::setWarningMessage(__d(__FILE__, 'See the log messages below', $this->data));
                $log = '<pre>' . print_r($result['log'], true) . '</pre>';
            }
        }
        App::setSeoTitle(__d(__FILE__, 'Update contents'));
        return Html::smartForm(array(
            'method' => 'get',
            'data' => $this->data,
            'title' => __d(__FILE__, 'Update contents'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => 4,
            'fields' => array(
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    'module' => array(
                        'label' => __d(__FILE__, 'Module'),
                        'type' => 'select',
                        'options' => App::getModules(),
                    ),
                    // @todo - create here selectbox of content pids from selected module
                    'pid' => array( 
                        'label' => __d(__FILE__, 'Pid'),
                        'hint' => __d(__FILE__, 'Optional pid of content from specified module. If provided then only this specified content is updated (loaded). If empty then all contents of module are updated.')
                    ),
                    'force' => array( 
                        'type' => 'checkbox',
                        'label' => __d(__FILE__, 'Overwrite existing'),
                        'hint' => __d(__FILE__, 'Considered only if <code>Pid</code> is provided. If checked then specified content is overwritten by its version defined in <code>contents.php</code> of selected <code>Module</code> even in case that the specified content exists already. If not checked then existing specified content is let untouched and only new (unexisting) contents are created.')
                    ),
                array('/row'),
                array('if' => !empty($log)),
                    array('h2' => __d(__FILE__, 'Log messages')),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => $log),
                        array('/col'),
                    array('/row'),
                array('endif'),
            )
        ));
    }
    
    public function compilePoFiles() {
        App::setSeoTitle(__d(__FILE__, 'Compile Po Files'));
        $Tool = $this->loadModel('Tool', true);
        return implode('<br>', $Tool->compilePoFiles());
    }
    
    /**
     * Sends test email
     * 
     * @return string
     */
    public function sendTestEmail() {
        $result = '';
        if (!empty($this->data['to'])) {
            try {
                if (App::sendEmail(
                    $this->data['body'],
                    $this->data['to'],
                    array(
                        'subject' => 'Test email send from ' . $_SERVER['HTTP_HOST'],
                    )
                )) {
                    $result = 'OK';
                }
                else {
                    $result = 'FAILED';
                }
            }
            catch (Throwable $e) {
                $result = 'EXCEPTION: ' . $e->getMessage();
            }
        }
        return Html::smartForm(array(
            'data' => $this->data,
            'title' => __d(__FILE__, 'Send email'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => array(3, 3, 6),
            'fields' => array(
                'body' => array(
                    'label' => __d(__FILE__, 'Body'),
                    'type' => 'text',
                    'value' => 'Test email send from ' . $_SERVER['HTTP_HOST'],
                ),
                'to' => array(
                    'label' => __d(__FILE__, 'To'),
                    'type' => 'text',
                ),
                'result' => array(
                    'type' => 'display',
                    'explicitValue' => $result,
                )
            )
        ));
    }
    
    public function testLogEmail() {
        $logMessage = __d(__FILE__, 'Test log created by Tools::testLogEmail()');
        $logEmail = App::getConfig('App', '_appInit.logEmail');
        if (!$logEmail) {
            $message =  __d(__FILE__, 'The config "_appInit.logEmail" is empty. No log e-mail delivery address is specified!');
        }
        elseif (!empty($this->data)) {
            $message = __d(
                __FILE__, 
                'Now go to inbox of %s and check if the log e-mail has come. It should contain messsage "%s"',
                $logEmail,
                $logMessage
            );
            App::logDebug($logMessage, array(
               'email' => true, 
            ));
        }
        else {
            $message =  __d(__FILE__, 'Submit the form to make a test log which sends log e-mail');
        }
        return Html::smartForm(array(
            'data' => $this->data,
            'title' => __d(__FILE__, 'Test log e-mail'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => 1,
            'fields' => array(
                'message' => array(
                    'type' => 'display',
                    'explicitValue' => $message,
                )
            )
        ));
    }
        
    /*
     * Resorting tree nodes
     * 
     * @param $module - Module of tree model
     * @param $model - Tree model
     * @param $rootId - Id of tree root. All childs will be resorted to all depths
     * 
     */
    public function sortTreeByName($module = null, $model = null, $rootId = null) {
        $Tool = $this->loadModel('Tool', true);
        $Tool->sortTreeByName($module, $model, $rootId);
        return $this->loadView('Tools/returnToMenu');
    }
    
    public function recoverTree($module, $model) {
        $Model = App::loadModel($module, $model, true);
        $Model->recoverTree();
        return $this->loadView('Tools/returnToMenu');
    }
    
    /**
     * Regenerate images variants from source dir to destination dir
     * 
     */
    public function regenerateImages() {
        $processed = 0;
        if (!empty($this->data)) {
            // sanitize data
            $this->data['sourceDir'] = trim(str_replace('\\', DS, $this->data['sourceDir']), DS);
            $this->data['destinationDir'] = trim(str_replace('\\', DS, $this->data['destinationDir']), DS);
            $this->data['pngGifToJpgColor'] = trim(str_replace(' ', '', $this->data['pngGifToJpgColor']));
            
            $validExtensions = array('png', 'gif', 'jpg', 'jpeg', 'bmp');
            
            // look for all images in source folder
            if (($handle = opendir($this->data['sourceDir']))) {
                while (($entry = readdir($handle)) !== false) {
                    if ($entry != "." && $entry != "..") {
                        // check if entry is image
                        if (!in_array(strtolower(pathinfo($entry, PATHINFO_EXTENSION)), $validExtensions)) {
                            continue;
                        }
                        
                        $options = array(
                            'path' => $this->data['destinationDir'],
                            'extension' => $this->data['extension']
                        );
                        $options[$this->data['function']] = array($this->data['x'], $this->data['y']);
                        if ($this->data['pngGifToJpg'] == '1') {
                            $colorTmp = explode(',', $this->data['pngGifToJpgColor']);
                            $options['pngGifToJpg'] = array(
                                array($colorTmp[0], $colorTmp[1], $colorTmp[2])
                            );
                        }
                        if (File::transform($this->data['sourceDir'] . DS . $entry, $options, array(
                            'unique' => false,
                        ))) {
                            $processed++;
                        }
                    }
                }
                closedir($handle);
            }
        }
        return $this->loadView('Tools/regenerateImages', array(
            'data' => $this->data,
            'processed' => $processed
        ));
    }
    
    public function copyWebContentLangBranch() {
        $Tool = $this->loadModel('Tool', true);
        if (!empty($this->data['sourceLang']) && !empty($this->data['targetLang'])) {  // @todo - make the validation in model Tool
            try {
                if (($copyIds = $Tool->copyWebContentLangBranch($this->data['sourceLang'], $this->data['targetLang']))) {
                    App::log('copyIds', json_encode($copyIds)); //debug
                    App::setSuccessMessage(__d(__FILE__, 'Web content from :sourceLang: branch has beee succesfully copied to :targetLang: branch', $this->data));
                }
                else {
                    $errors = $Tool->getErrors(true);
                    App::setErrorMessage(__d(__FILE__, 'Copying of web content from :sourceLang: branch to :targetLang: branch has failed. Validation errors: %s', $this->data, print_r($errors, true)));
                }
            } 
            catch (Throwable $e) {
                App::setErrorMessage(__d(__FILE__, 'Copying of web content from :sourceLang: branch to :targetLang: branch has failed. Error: "%s"', $this->data, $e->getMessage()));
            }
        }
        $Language = App::loadModel('App', 'Language', true);
        $langs = $Language->findList(array(
            'fields' => array('lang'),
            'order' => 'default DESC',
        ));
        App::setSeoTitle(__d(__FILE__, 'Copy web contents language branch'));
        return Html::smartForm(array(
            'method' => 'get',
            'data' => $this->data,
            'title' => __d(__FILE__, 'Copy web contents language branch'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => 4,
            'fields' => array(
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    'sourceLang' => array(
                        'label' => __d(__FILE__, 'Source language'),
                        'type' => 'select',
                        'options' => array_combine($langs, $langs)
                    ),
                    'targetLang' => array(
                        'label' => __d(__FILE__, 'Target language'),
                        'type' => 'select',
                        'options' => array_combine($langs, $langs)
                    ),
                array('/row'),
            )
        ));
    }
    
    public function deleteWebContentLangBranch() {
        $Tool = $this->loadModel('Tool', true);
        if (!empty($this->data['lang'])) {  // @todo - make the validation in model Tool
            if (($deleteIds = $Tool->deleteWebContentLangBranch($this->data['lang']))) {
                App::log('deleteIds', json_encode($deleteIds)); //debug
                App::setSuccessMessage(__d(__FILE__, 'Web contents for :lang: branch has beee succesfully deleted', $this->data));
            }
            else {
                App::setErrorMessage(__d(__FILE__, 'Deleting of web content from :lang: branch has failed', $this->data));
            }
        }
        $Language = App::loadModel('App', 'Language', true);
        $langs = $Language->findList(array(
            'conditions' => array(
                // avoid deletion of default lang
                'default' => false,
            ),
            'fields' => array('lang'),
        ));
        App::setSeoTitle(__d(__FILE__, 'Delete web contents language branch'));
        return Html::smartForm(array(
            'method' => 'get',
            'data' => $this->data,
            'title' => __d(__FILE__, 'Delete web contents language branch'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => 4,
            'fields' => array(
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    'lang' => array(
                        'label' => __d(__FILE__, 'Language'),
                        'type' => 'select',
                        'options' => array_combine($langs, $langs)
                    ),
                array('/row'),
            )
        ));
    }
    
    public function swapWebContentPids() {
        $Tool = $this->loadModel('Tool', true);
        if ($this->data) { 
            if ($Tool->swapWebContentPids($this->data)) {
                App::setSuccessMessage(__d(__FILE__, 'Pids has been succesfully swapped'));
            }
            else {
                App::setErrorMessage(__d(__FILE__, 'Please, correct errors'));
            }            
        }
        $Language = App::loadModel('App', 'Language', true);
        $langs = $Language->findList(array(
            'conditions' => array(
                // avoid deletion of default lang
                'default' => false,
            ),
            'fields' => array('lang'),
        ));        
        App::setSeoTitle(__d(__FILE__, 'Swap web contents pids'));
        return Html::smartForm(array(
            'method' => 'get',
//            'Model' => array($Tool, 'swapWebContentPids'), // BUGFIX - this should work
            'Model' => $Tool,
            'data' => $this->data,
            'title' => __d(__FILE__, 'Swap web contents pids'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => 4,
            'fields' => array(
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    'pid1' => array(
                        'label' => __d(__FILE__, 'Pid 1'),
                    ),
                    'pid2' => array(
                        'label' => __d(__FILE__, 'Pid 2'),
                    ),
                    'lang' => array(
                        'label' => __d(__FILE__, 'Language'),
                        'type' => 'select',
                        'options' => array_combine($langs, $langs),
                        'empty' => true,
                    ),
                array('/row'),
            )
        ));
    }
    
    /**
     * Deletes all session files older than provided number of days
     * 
     * @param integer $olderThan Number of days 
     */
    public function deleteSessionFiles($olderThan = 0) {
        $deleteTime = time() - 60*60*24*(int)$olderThan;
        clearstatcache();
        $skippedCount = 0;
        $deletedCount = 0;
        // delete sessions
        $sessionPath = TMP . DS . 'sessions';
        if (is_dir($sessionPath)) {
            $dh = opendir($sessionPath);
            while(($file = readdir($dh)) !== false) {
                // skip nonsession files
                if (substr($file, 0, 5) !== 'sess_') {
                    continue;
                }
                $file = $sessionPath . DS . $file;
                // skip nonreadable files and files which are young (less than month)
                if (filemtime($file) > $deleteTime) {
                    $skippedCount++;
                    continue;
                }
                $deletedCount++;
                @unlink($file);
            }
            closedir($dh);
        }
        // delete permanent sessions
        $permanentSessionPath = $sessionPath . DS . 'permanent';
        if (is_dir($permanentSessionPath)) {
            $dh = opendir($permanentSessionPath);
            while(($file = readdir($dh)) !== false) {
                // skip nonsession files
                if (substr($file, 0, 5) !== 'sess_') {
                    continue;
                }
                $file = $permanentSessionPath . DS . $file;
                // skip nonreadable files and files which are young (less than month)
                if (filemtime($file) > $deleteTime) {
                    $skippedCount++;
                    continue;
                }
                $deletedCount++;
                @unlink($file);
            }
            closedir($dh);
        }
        App::setSuccessMessage(__d(__FILE__, '%s files has been deleted. %s files has been preserved', $deletedCount, $skippedCount));
    }
    
    public function setSessionPath() {
        $Tool = $this->loadModel('Tool', true);
        if ($this->data) { 
            if (!empty($this->data['path'])) {
                if (!empty($this->data['delete'])) {
                    Arr::unsetPath($_SESSION, $this->data['path']);
                    App::setSuccessMessage(__d(__FILE__, 'SESSION path has been succesfully deleted'));
                }
                else {
                    if ($this->data['value'] === 'NULL') {
                        $this->data['value'] = null;
                    }
                    elseif ($this->data['value'] === 'array()') {
                        $this->data['value'] = array();
                    }
                    Arr::setPath($_SESSION, $this->data['path'], $this->data['value']);
                    App::setSuccessMessage(__d(__FILE__, 'SESSION path has been succesfully set'));
                }
            }
            else {
                App::setErrorMessage(__d(__FILE__, 'Please, provide path'));
            }            
        }
        App::setSeoTitle(__d(__FILE__, 'Set SESSION path'));
        return Html::smartForm(array(
            'method' => 'get',
//            'Model' => array($Tool, 'swapWebContentPids'), // BUGFIX - this should work
            'Model' => $Tool,
            'data' => $this->data,
            'title' => __d(__FILE__, 'Set SESSION path'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => 1,
            'fields' => array(
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    'path' => array(
                        'label' => __d(__FILE__, 'Path'),
                        'hint' => __d(__FILE__, 'E.g. _app.EshopProducts.indexType'),
                    ),
                array('/row'),
                array('row', 'columns' => array(1, 11)),
                    'delete' => array(
                        'type' => 'checkbox',
                        'label' => __d(__FILE__, 'Delete'),
                        'hint' => __d(__FILE__, 'If checked then specified path will be removed from SESSION'),
                    ),
                    'value' => array(
                        'label' => __d(__FILE__, 'Value'),
                        'hint' => __d(__FILE__, 'Any new value. Let empty for empty string value. Write "NULL" for null value, Write "array()" for empty array value'),
                    ),
                array('/row'),
                array('row'),
                    array('col'),
                        array('html' => '$_SESSION: <pre>' . print_r($_SESSION, true) . '</pre><br>'),
                    array('/col'),
                array('/row'),
            )
        ));
    }    
    
    public function setCookie() {
        $Tool = $this->loadModel('Tool', true);
        if ($this->data) { 
            $data = &$this->data;
            if (!empty($data['name'])) {
                if (!empty($data['delete'])) {
                    setcookie($data['name'], '', time() - 3600, '/', '', 0);
                    App::setSuccessMessage(__d(__FILE__, 'Cookie has been succesfully deleted'));
                }
                else {
                    setcookie($data['name'], rawurldecode($data['value']), time() + (int)$data['expire'], '/', '', 0);
                    App::setSuccessMessage(__d(__FILE__, 'Cookie has been succesfully set'));
                }
                App::setInfoMessage(__d(__FILE__, 'Refresh the page (CTRL+F5) to see the new $_COOKIE content'));
            }
            else {
                App::setErrorMessage(__d(__FILE__, 'Please, provide name'));
            }            
        }
        App::setSeoTitle(__d(__FILE__, 'Set cookie'));
        return Html::smartForm(array(
            'method' => 'get',
//            'Model' => array($Tool, 'swapWebContentPids'), // BUGFIX - this should work
            'Model' => $Tool,
            'data' => $this->data,
            'title' => __d(__FILE__, 'Set cookie'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => 4,
            'fields' => array(
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    'name' => array(
                        'label' => __d(__FILE__, 'Name'),
                        'hint' => __d(__FILE__, 'E.g. <code>_eshop_EshopCart_products_</code>'),
                    ),
                    'value' => array(
                        'label' => __d(__FILE__, 'Value'),
                        'hint' => __d(__FILE__, 'String value of the cookie'),
                    ),
                    'expire' => array(
                        'label' => __d(__FILE__, 'Expire'),
                        'hint' => __d(__FILE__, 'Cookie will expire in provided amount of seconds'),
                        'value' => 3600,
                    ),
                    'delete' => array(
                        'type' => 'checkbox',
                        'label' => __d(__FILE__, 'Delete'),
                        'hint' => __d(__FILE__, 'If checked then specified cookie will be deleted (expiration is ignored)'),
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    array('col'),
                        array('html' => '$_COOKIE: <pre>' . print_r($_COOKIE, true) . '</pre><br>'),
                    array('/col'),
                array('/row'),
            )
        ));
    }    
    
    public function displayCookieStorage() {
        $Tool = $this->loadModel('Tool', true);
        $cookieStorage = null;
        if ($this->data) { 
            $data = &$this->data;
            if (!empty($data['name'])) {
                App::loadLib('App', 'CookieStorage');
                $CookieStorage = new CookieStorage($data['name']);
                $cookieStorage = $CookieStorage->get();
            }
            else {
                App::setErrorMessage(__d(__FILE__, 'Please, provide name'));
            }            
        }
        App::setSeoTitle(__d(__FILE__, 'Display cookie storage'));
        return Html::smartForm(array(
            'method' => 'get',
            'Model' => $Tool,
            'data' => $this->data,
            'title' => __d(__FILE__, 'Display cookie storage'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => 4,
            'fields' => array(
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    'name' => array(
                        'label' => __d(__FILE__, 'Name'),
                        'hint' => __d(__FILE__, 'E.g. <code>_eshop_EshopCart_products_</code>'),
                    ),
                array('/row'),
                array('if' => !empty($this->data['name'])),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => 'Cookie storage "' . 
                                Sanitize::value($this->data['name']) . 
                                '": <pre>' . print_r($cookieStorage, true) . 
                                '</pre><br>'
                            ),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row', 'columns' => 1),
                    array('col'),
                        array('html' => '$_COOKIE: <pre>' . print_r($_COOKIE, true) . '</pre><br>'),
                    array('/col'),
                array('/row'),
            )
        ));
    }    
    
    public function cleanUpEshopDatabase() {
        App::setDebug(true, 'log');
        if (!ON_LOCALHOST) {
            App::setErrorMessage(__d(__FILE__, 'This tool can be launched only on local projects'));
        }
        elseif ($this->data) { 
            if (empty($this->data['productsAmount'])) {
                App::setErrorMessage(__d(__FILE__, 'Please, provide products amount'));
            }            
            elseif (!Validate::intNumber($this->data['productsAmount'])) {
                App::setErrorMessage(__d(__FILE__, 'Please, provide products amount as integer number'));
            }            
            else {
                $Product = App::loadModel('Eshop', 'EshopProduct', true);
                $lastId = $Product->findField('id', array('order' => 'id DESC')) - $this->data['productsAmount'];
                $id = 0;
                $batchSize = 10000;
                do {
                    $id += $batchSize;
                    if ($id > $lastId) {
                        $id = $lastId;
                    }
                    $Product->delete(array(
                        'conditions' => array(
                            'id <=' => $id,
                        ),
                        'softDelete' => false,
                    ));
                    App::log('Tools_cleanUpEshopDatabase', Str::fill('%s EshopProducts cleaned...', $id));
                } while ($id < $lastId);
                App::log('Tools_cleanUpEshopDatabase', 'EshopProducts cleaned');
                // remove records in following related Models
                $relatedModels = array(
                    'EshopProductAuthor',
                    'EshopManufacturer',
                    'EshopManufacturerRange',
                    'EshopProductCategoryProduct',
                    'EshopProductGroupProduct',
                    'EshopAccessoryProduct',
                    'EshopRelatedProduct',
                    'EshopWishlistProduct',
                    'EshopProductAttribute',
                    'EshopProductImage',
                );
                foreach ($relatedModels as $model) {
                    try {
                        $Model = App::loadModel('Eshop', $model, true);
                        $Model->delete(array(
                            'joins' => array(
                                array(                                
                                    'model' => 'EshopProduct',
                                    'type' => 'left',
                                )
                            ),
                            'conditions' => array(
                                'EshopProduct.id' => null,
                            ),
                            'softDelete' => false,
                        ));
                        App::log('Tools_cleanUpEshopDatabase', $model . 's cleaned');
                    }
                    catch (Throwable $e) {
                        App::log('Tools_cleanUpEshopDatabase', $model . 's cleaning failed: ' . $e->getMessage());
                    }
                }
                // authors
                $Author = App::loadModel('Eshop', 'EshopAuthor', true);
                $Author->delete(array(
                    'joins' => array(
                        array(     
                            'model' => 'EshopProductAuthor',
                            'type' => 'left',
                        )
                    ),
                    'conditions' => array(
                        'EshopProductAuthor.id' => null,
                    ),
                    'softDelete' => false,
                ));
                App::log('Tools_cleanUpEshopDatabase', 'EshopAuthors cleaned');
                // orders
                $OrderProduct = App::loadModel('Eshop', 'EshopOrderProduct', true);
                $invalidOrderIds = $OrderProduct->findList(array(
                    'key' => 'EshopOrderProduct.run_eshop_orders_id',
                    'fields' => array('EshopOrderProduct.run_eshop_orders_id'),
                    'joins' => array(
                        array(     
                            'model' => 'EshopProduct',
                            'type' => 'left',
                        )
                    ),
                    'conditions' => array(
                        'EshopProduct.id' => null,
                    )
                ));
                $OrderProduct->deleteBy('EshopOrderProduct.run_eshop_orders_id', $invalidOrderIds, array(
                    'softDelete' => false,
                ));
                App::log('Tools_cleanUpEshopDatabase', 'EshopOrderProducts cleaned');
                $Order = App::loadModel('Eshop', 'EshopOrder', true);
                $Order->delete(array(
                    'joins' => array(
                        array(     
                            'model' => 'EshopOrderProduct',
                            'type' => 'left',
                        )
                    ),
                    'conditions' => array(
                        'EshopOrderProduct.id' => null,
                    ),
                    'softDelete' => false,
                ));
                App::log('Tools_cleanUpEshopDatabase', 'EshopOrders cleaned');
                // users
                $User = App::loadModel('App', 'User', true);
                $User->delete(array(
                    'joins' => array(
                        array(     
                            'module' => 'Eshop',
                            'model' => 'EshopOrder',
                            'type' => 'left',
                        ),
                        array(     
                            'module' => 'App',
                            'model' => 'Group',
                            'type' => 'left',
                        ),
                    ),
                    'conditions' => array(
                        'EshopOrder.id' => null,
                        'Group.pid' => 'clients',
                    ),
                    'softDelete' => false,
                ));
                App::log('Tools_cleanUpEshopDatabase', 'Users cleaned');
                $UserProfile = App::loadModel('App', 'UserProfile', true);
                $UserProfile->delete(array(
                    'joins' => array(
                        array(     
                            'model' => 'User',
                            'type' => 'left',
                        ),
                    ),
                    'conditions' => array(
                        'User.id' => null,
                    ),
                    'softDelete' => false,
                ));
                App::log('Tools_cleanUpEshopDatabase', 'UserProfiles cleaned');
                // wishlists
                $Wishlist = App::loadModel('Eshop', 'EshopWishlist', true);
                $Wishlist->delete(array(
                    'joins' => array(
                        array(     
                            'model' => 'EshopWishlistProduct',
                            'type' => 'left',
                        )
                    ),
                    'conditions' => array(
                        'EshopWishlistProduct.id' => null,
                    ),
                    'softDelete' => false,
                ));
                App::log('Tools_cleanUpEshopDatabase', 'EshopWishlists cleaned');
                // mailer contacts
                $Contact = App::loadModel('Mailer', 'MailerContact', true);
                $Contact->delete(array(
                    'conditions' => array(
                        'email !%~' => '@run.sk',
                    ),
                    'softDelete' => false,
                ));
                App::log('Tools_cleanUpEshopDatabase', 'MailerContacts cleaned');
                App::setSuccessMessage(__d(__FILE__, 'Database has been succesfully cleaned'));
            }
        }
        return Html::smartForm(array(
            'method' => 'get',
//            'Model' => $Tool,
            'data' => $this->data,
            'title' => __d(__FILE__, 'Clean Eshop database'),
            'buttonLabel' => __d(__FILE__, 'Submit'),
            'columns' => 4,
            'fields' => array(
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    array('col'),
                        'productsAmount' => array(
                            'label' => __d(__FILE__, 'Products amount'),
                            'hint' => __d(__FILE__, 'Products amount to be preserved in database'),
                            'value' => 1000,
                        ),
                    array('/col'),
                    array('col'),
                        array('html' => __d(__FILE__, 'Check file /tmp/logs/Tools_cleanUpEshopDatabase.log for progress logs and debug.log for SQL log')),
                    array('/col'),
                array('/row'),
            )
        ));
    }
    
    public function cleanUpEshopOldProducts($step = 'info') {
        $this->loadModel('Tool');
        $Tool = new Tool();
        $result = $Tool->cleanUpEshopOldProducts($step);
        return $this->loadView('Tools/cleanUpEshopOldProducts', array(
            'step' => $step,
            'result' => $result,
        ));
    }
    
    public function deleteEshopProductOrphanFiles() {
        $this->loadModel('Tool');
        $Tool = new Tool();
        $result = $Tool->deleteEshopProductOrphanFiles();
        return '<pre>' . print_r($result, true) . '</pre>';
    }
    
    public function changeEshopOrderNumbers() {
        $this->loadModel('Tool');
        $Tool = new Tool();
        $hasError = false;
        if ($this->data) { 
            if ($Tool->changeEshopOrderNumbers($this->data)) {
                App::setSuccessMessage(__d(__FILE__, 'Objednávky boli úspešne prečíslované'));
            }
            else {
                $hasError = true;
                App::setErrorMessage(__d(__FILE__, 'Opravte chyby'));
            }            
        }
        App::setSeoTitle(__d(__FILE__, 'Prečíslovať objednávky'));
        return Html::smartForm(array(
            'method' => 'get',
            'Model' => array(array($Tool, 'changeEshopOrderNumbers')),
            'data' => $this->data,
            'title' => __d(__FILE__, 'Prečíslovať objednávky'),
            'buttonLabel' => __d(__FILE__, 'Uložiť'),
            'columns' => 4,
            'fields' => array(
                /*
                array('if' => !empty($this->data) && !$hasError),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                 */
                array('row'),
                    'oldNumber' => array(
                        'label' => __d(__FILE__, 'Staré číslo'),
                        //'hint' => __d(__FILE__, 'Staré číslo sa zmení na nové číslo. Čísla objednávok väčšie ako staré číslo sa prečíslujú tak, aby plynulo nadväzovali na nové číslo.'),
                    ),
                    'newNumber' => array(
                        'label' => __d(__FILE__, 'Nové číslo'),
                        //'hint' => __d(__FILE__, 'Staré číslo sa zmení na nové číslo. Čísla objednávok väčšie ako staré číslo sa prečíslujú tak, aby plynulo nadväzovali na nové číslo.'),
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    array('col'),
                        array(
                            'html' => __d(__FILE__, 'Staré číslo sa zmení na nové číslo. Čísla objednávok väčšie ako staré číslo sa prečíslujú tak, aby plynulo nadväzovali na nové číslo.') . '<br><br>'
                        ),
                    array('/col'),
                array('/row'),
            )
        ));        
    }
    
    public function normalizeText() {
        return $this->loadView('Tools/normalizeText');
    }
    
    public function sumTimeEstimates() {
        $sum = null;
        $highlightedText = null;
        if (!empty($this->data['text'])) {
            $matches = null;
            $sum = 0.0;
            $regex = '/\[(\d+\*)?(\d+(?:[,.]\d+)?)h?\]/i';
            if (preg_match_all($regex, $this->data['text'], $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    if ($match[1]) {
                        $sum += (float)rtrim($match[1], '*') * (float)str_replace(',' , '.', $match[2]);
                    }
                    else {
                        $sum += (float)str_replace(',' , '.', $match[2]);
                    }
                }
            }
            $tag = 'b';
            //$tag = 'strong';
            $highlightedText = preg_replace($regex, "<$tag style=\"color:blue\">$0</$tag>", $this->data['text']);
            // create regex for invalid time estimation specifications:
            $invalidRegex = '';
            // - all which contains illegal characters (other than "01234567489h.,*")
            $invalidRegex .= '\[[^\]]*[^0-9h.,*\]]+[^\]]*\]';
            // - all with invalid multiplication, e.g. [*5h] or [b*3h]
            $invalidRegex .= '|\[\*[^\]]*\]|\[\d*\D\d*\*[^\]]*\]';
            // - add delimiter and flags
            $invalidRegex = '/' . $invalidRegex . '/i';
            //preg_match_all($invalidRegex, $highlightedText, $matches); //debug
            $highlightedText = preg_replace($invalidRegex, "<$tag style=\"color:red\">$0</$tag>", $highlightedText);
            $highlightedText = preg_replace('/-[\?\!]+/i', "<$tag style=\"color:red\">$0</$tag>", $highlightedText);
        }
        return $this->loadView('Tools/sumTimeEstimates', array(
            'data' => $this->data,
            'sum' => $sum,
            'highlightedText' => $highlightedText,
        ));
    }
    
    public function checkContentBlocksDefinitions() {
        App::loadModel('App', 'ContentBlockInstance');
        $BlockInstance = new ContentBlockInstance();
        $contentBlocks = $BlockInstance->getContentBlockModels();
        $errors = array();
        foreach ($contentBlocks as $contentBlock) {
            try {
                list($contentBlockModuleName, $contentBlockModelName) = explode(
                    '.', $contentBlock
                );
                App::loadModel(
                    $contentBlockModuleName, 
                    $contentBlockModelName, 
                    true
                );
            } 
            catch (Throwable $e) {
                $errors[$contentBlock] = $e->getMessage();
            }
        }
        $output = '';
        if ($errors) {
            $output .= '<strong style="color:red">' . __d(__FILE__, 'There are errors in definitions of following content blocks') . ':</strong>';
            foreach ($errors as $contentBlock => $error) {
                $output .= '<br><strong>' . $contentBlock . '</strong>: ' . $error;
            }
            $output .= '<br><br><strong>' . __d(__FILE__, 'Repeat this check after repairing the above errors!') . '</strong>';
        }
        else {
            $output .=  __d(__FILE__, 'Definitions of all content block in project are ok');
        }
        return $output;
    }
    
    public function refactorContentBlockField() {
        $this->loadModel('Tool');
        $Tool = new Tool();
        $hasError = false;
        $progress = array();
        if ($this->data) { 
            if (($progress = $Tool->refactorContentBlockField($this->data))) {
                App::setSuccessMessage(__d(__FILE__, 'Pole obsahového bloku úspešne premenované a/alebo zmazané'));
            }
            else {
                $hasError = true;
                App::setErrorMessage(__d(__FILE__, 'Opravte chyby'));
            }            
        }
        App::loadModel('App', 'ContentBlockInstance');
        $BlockInstance = new ContentBlockInstance();
        $contentBlocks = $BlockInstance->getContentBlockModels();
        $contentBlocks = array_combine($contentBlocks, $contentBlocks);
        App::setSeoTitle(__d(__FILE__, 'Premenovať a/alebo zmazať pole obsahového bloku'));
        return Html::smartForm(array(
            'method' => 'get',
            'Model' => array(array($Tool, 'refactorContentBlockField')),
            'data' => $this->data,
            'title' => __d(__FILE__, 'Premenovať a/alebo zmazať pole obsahového bloku'),
            'buttonLabel' => __d(__FILE__, 'Uložiť'),
            'columns' => 4,
            'fields' => array(
                array('if' => !empty($this->data) && !$hasError),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                array('row'),
                    'contentBlockModel' => array(
                        'label' => __d(__FILE__, 'Obsahový blok'),
                        'type' => 'select',
                        'options' => $contentBlocks,
                        'empty' => true,
                    ),
                    'oldField' => array(
                        'label' => __d(__FILE__, 'Pôvodný názov poľa'),
                    ),
                    'newField' => array(
                        'label' => __d(__FILE__, 'Nový názov poľa'),
                    ),
                    'removeOldField' => array(
                        'label' => __d(__FILE__, 'Odstrániť pôvodné pole'),
                        'type' => 'checkbox',
                    ),
                array('/row'),
                array('if' => !empty($progress)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => '<pre>' . print_r($progress, true) . '</pre>'),
                        array('/col'),
                    array('/row'),
                array('endif'),
            )
        ));        
    }
    
    public function allowUnpublishedLangsLocally() {
        $allowed = !empty($_COOKIE['_AllowUnpublishedLangsLocally_']);
        if ($this->data) { 
            if ($allowed) {
                setcookie('_AllowUnpublishedLangsLocally_', '', time() - 3600, '/', '', 0);
                App::setSuccessMessage(__d(__FILE__, 'Unpublished languages were succesfully disallowed'));
            }
            else {
                setcookie('_AllowUnpublishedLangsLocally_', '1', time() + 60 * 60 * 24 * 365 * 99, '/', '', 0);
                App::setSuccessMessage(__d(__FILE__, 'Unpublished languages were succesfully allowed'));
            }
            $allowed = !$allowed;
        }
        App::setSeoTitle(__d(__FILE__, 'Allow unpublished languages locally'));
        return Html::smartForm(array(
            'method' => 'get',
            'title' => __d(__FILE__, 'Allow unpublished languages locally'),
            'buttonLabel' => $allowed ? __d(__FILE__, 'Disallow') : __d(__FILE__, 'Allow'),
            'columns' => 1,
            'fields' => array(
                /*/
                array('if' => !empty($this->data)),
                    array('row', 'columns' => 1),
                        array('col'),
                            array('html' => __d(__FILE__, '<label>Last request:</label> %s', $this->getRequestUrl())),
                        array('/col'),
                    array('/row'),
                array('endif'),
                /*/
                '_dataSubmitted_' => array(
                    'type' => 'hidden',
                    'explicitValue' => true,
                ),
                array('row', 'columns' => 1),
                    array('col'),
                        array('html' => 
                            $allowed 
                            ? 
                            '<b style="color:green">' . __d(__FILE__, 'Unpublished languages are locally allowed') . '</b><br><br>'
                            : 
                            '<b style="color:red">' . __d(__FILE__, 'Unpublished languages are not locally allowed') . '</b><br><br>',
                        ),
                    array('/col'),
                array('/row'),
            )
        ));        
    }
    
    public function viewEshopOrdersStatistics() {
        $this->loadModel('Tool');
        $Tool = new Tool();
        App::loadModel('Eshop', 'EshopOrder');
        $Order = new EshopOrder();
        $result = array(
            'statistics' => null,
            'queries' => null,
        );
        if ($this->data) {
            if (!($result = $Tool->getEshopOrdersStatistics($this->data))) {
                App::setErrorMessage(__d(__FILE__, 'Opravte chyby'));
            }
            else {
                $storedData = array_intersect_key($this->data, array_flip(array(
                    'exclude_emails',
                    'exclude_emails_containing',
                    'exclude_comments_containing',
                    'exclude_order_numbers',
                )));
                $this->setSetting('Tools.viewEshopOrdersStatistics.data', json_encode($storedData));
                if ($result['warnings']) {
                    foreach ($result['warnings'] as $warning) {
                        App::setWarningMessage($warning);
                    }
                }
            }
        }
        elseif (($storedData = $this->getSetting('Tools.viewEshopOrdersStatistics.data'))) {
            $this->data = json_decode($storedData, true);
        }
        App::setSeoTitle(__d(__FILE__, 'Štatistiky objednávok'));
        return $this->loadView('Tools/viewEshopOrdersStatistics', array(
            'data' => $this->data,
            'Model' => array(array($Tool, 'viewEshopOrdersStatistics')),
            'statistics' => $result['statistics'],
            'queries' => $result['queries'],
            'errors' => $Tool->getErrors(),
            'statuses' => $Order->getEnumValues('status'),
        ));
    }
    
    
    public function checkForCleanedUpProduct() {
        $productInfo = '';
        if ($this->data) {
            $cleanedUpExportedProducts = array();
            $backupFile = 
                'app' . DS . 'modules' . DS . 'Eshop' . DS . 'config' . DS . 'cleanedUpExportedProducts.php';
            if (is_readable(ROOT . DS . $backupFile)) {
                App::loadScript($backupFile, array('catchVariables' => 'exportedProducts'), $vars);
                if (!empty($vars['exportedProducts'])) {
                    $cleanedUpExportedProducts = &$vars['exportedProducts'];
                }
                if (!$cleanedUpExportedProducts) {
                    App::setErrorMessage(__d(__FILE__, 'Zoznam začistených produktov je prázdny'));
                }
            }
            else {
                App::setErrorMessage(__d(__FILE__, 'Súbor %s sa nenašiel', $backupFile));
            }
            $conditions = array();
            if (!empty($this->data['ean'])) {
                $conditions['ean'] = $this->data['ean'];
                if (isset($cleanedUpExportedProducts[$this->data['ean']])) {
                    App::setInfoMessage(__d(
                        __FILE__, 
                        'Produkt s EAN-om %s bol nájdený medzi začistenými produktami a jeho pôvodné katalógové číslo bolo %s',
                        $this->data['ean'],
                        $cleanedUpExportedProducts[$this->data['ean']]
                    ));
                }
                else {
                    App::setInfoMessage(__d(
                        __FILE__, 
                        'Produkt s EAN-om %s sa medzi začistenými produktami nenašiel',
                        $this->data['ean']
                    ));
                }
            }
            elseif (!empty($this->data['code'])) {
                $conditions['code'] = $this->data['code'];
                if (($ean = array_search($this->data['code'], $cleanedUpExportedProducts)) !== false) {
                    App::setInfoMessage(__d(
                        __FILE__, 
                        'Produkt s katalógovým číslom %s bol nájdený medzi začistenými produktami a jeho pôvodný EAN bol %s',
                        $this->data['code'],
                        $ean
                    ));
                }
                else {
                    App::setInfoMessage(__d(
                        __FILE__, 
                        'Produkt s katalógovým číslom %s sa medzi začistenými produktami nenašiel',
                        $this->data['code']
                    ));
                }
            }
            if ($conditions) {
                App::loadModel('Eshop', 'EshopProduct');
                $Product = new EshopProduct();
                $products = $Product->find(array(
                    'conditions' => $conditions,
                    'ignoreSoftDeleted' => false,
                ));
                if ($products) {
                    foreach ($products as &$product) {
                        unset($product['description']);
                        unset($product['short_description']);
                    }
                    unset($product);
                    $productInfo = '<xmp>' . print_r($products, true) . '</xmp>';
                }
                else {
                    $productInfo = __d(__FILE__, 'Produkt sa v databáze nenašiel');
                }
            }
        }
        App::setSeoTitle(__d(__FILE__, 'Skontrolovať začistené (zmazané) produkty'));
        return Html::smartForm(array(
            'method' => 'get',
            'data' => $this->data,
            'title' => __d(__FILE__, 'Skontrolovať začistené (zmazané) produkty'),
            'buttonLabel' => __d(__FILE__, 'Skontrolovať'),
            'columns' => 4,
            'fields' => array(
                'ean' => array(
                    'label' => __d(__FILE__, 'EAN'),
                ),
                'code' => array(
                    'label' => __d(__FILE__, 'Katalógové číslo'),
                ),
                array('if' => $productInfo),
                    array('row'),
                        array('html' => $productInfo),
                    array('/row'),
                array('endif'),
            )
        ));
    }
    
    protected function getRequestUrl() {
        $url = App::$parsedUrl;
        if (isset($url['get']['data']['errors__transfer__'])) {
            unset($url['get']['data']['errors__transfer__']);
        }
        return rawurldecode(App::getUrl($url));
    }    
}
