<?php
class References extends SmartController {
    
    protected $model = 'Reference';
    
    /** @var Reference */
    protected $Model;
    
    public function admin_index() {
        $this->viewOptions['columns'] = array(
            'name' => __a(__FILE__, 'Názov'),
            'locator' => __a(__FILE__, 'URL'),
            'active' => __a(__FILE__, 'Aktívna'),
        );
        $this->seoTitle = __a(__FILE__, 'Firemné referencie');
        $this->viewOptions['title'] = __a(__FILE__, 'Firemné  referencie');
        
        return parent::admin_index();
    }
    
    public function admin_add() {
        $this->view = 'References/admin_form';
        $this->seoTitle = __a(__FILE__, 'Nová referencia');
        $this->viewOptions['title'] = __a(__FILE__, 'Nová referencia');
        return parent::admin_add();
    }
    
    public function admin_edit($id = null) {
        $this->view = 'References/admin_form';
        $this->viewOptions['title'] = __a(__FILE__, 'Upraviť referenciu "%s"');
        return parent::admin_edit($id);
    }

    public function indexSlider() {
        $this->displayOriginComment = true;
        $defaults = array(
            'title' => __(__FILE__, 'Naše referencie'),
            'buttonText' => __(__FILE__, 'Všetky referencie'),
        );
        $inputOptions = Arr::camelizeKeys($this->params, array('separator' => '-'));
        $options = array_merge($defaults, $inputOptions);
        $Content = $this->loadModel('Reference', true);
        $references = $Content->find(array(
            'conditions' => array(
                'Reference.active' => true
            ),
            'fields' => array(
                'Reference.id',
                'Reference.name',
                'Reference.locator',
                'Reference.image',
            ),
        ));
        // set image url paths
        foreach ($references as &$reference) {
            if (!empty($reference['image'])) {
                $reference['image'] = $Content->getFileFieldUrlPath('image', array(
                    'file' => $reference['image'],
                    'variant' =>  ''
                ));
            }
        }
        unset($reference);

        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('References/indexSlider', array(
            'title' => $options['title'],
            'buttonText' => $options['buttonText'],
            'references' => $references,
            'indexLocator' => App::getContentLocatorByPid('App.References.index'),
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit reference'),
                )
            ),
        ));
    }
    
}
