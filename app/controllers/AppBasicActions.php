<?php
class AppBasicActions extends Controller {
    
    /**
     * MVC ELEMENT
     * 
     * Generates contact form 
     * 
     * @param string $this->params['layout'] Possible values are 'vertical' and horizontal.
     *      Defaults to 'vertical'.
     * 
     * @return string
     */
    public function contactForm () {
        $this->displayOriginComment = true;
        $Action = $this->loadModel('AppBasicAction', true);
        if ($this->data) {
            // get content id
            if (($content = App::getContentByLocator(SLUG, array(
                'fields' => array('id')
            )))) {
                $this->data['contentId'] = $content['id'];
            }
            // - in case of popup
            elseif (!empty($_GET['_contentId_'])) {
                $this->data['contentId'] = $_GET['_contentId_'];
            }
            // - in case of snippet
            elseif (!empty($this->params['content']['id'])) {
                $this->data['contentId'] = $this->params['content']['id'];
            }
            // - in case of mvc element
            elseif (
                ($content = App::getPropertyContent())
                && !empty($content['id'])
            ) {
                $this->data['contentId'] = $content['id'];
            }
            if ($Action->submitContactForm($this->data)) {
                App::setMessage(__(__FILE__, 'Your message has been succesfully submited'));
                App::redirect(App::getRefererUrl('/'));
            }
            else {
                $errors = $Action->getErrors();
                if (!empty($errors['_processing'])) {
                    App::setMessage(__(__FILE__, 'Your message submition has failed. Contact us please by email or phone'));
                    App::redirect(App::getRefererUrl('/'));
                }
                else {
                    App::setMessage(__(__FILE__, 'Please, correct contact form errors'));
                }
            }
        }
        return $this->loadView('/AppBasicActions/contactForm', array(
            'data' => $this->data,
            'errors' => $Action->getErrors(),
            'required' => $Action->getNotEmptyFields(array('alternative' => 'submitContactForm')),
            'layout' => Sanitize::value($this->params['layout'])
        ));
    }
    
    /**
     * MVC ELEMENT
     * 
     * Generates popup button
     * 
     * NOTE: There are GET params _requestSource_=frame and _contentId_ added in loaded url.
     * The loaded url should/could use them, especially _requestSource_=frame.
     * 
     * @param int|string $this->params['source'] Source mvc url or id or locator of content to load
     * @param string $this->params['label'] Button label
     * @param string $this->params['_content']['id'] Actual content id is set to the load url as _contentId_ get param
     * 
     * @return string
     */
    public function popupButton () {
        $this->displayOriginComment = true;
        $defaults = array(
            'source' => null,
            'label' => null,
            '_content' => null,
            
            // this is here just to remove it from options when separating options for js lib
            '_text' => null,
            '_snippet' => null,
            '_snippet_generic' => null,
            '_snippet_name' => null,
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        // get source
        $source = $options['source'];
        if (Validate::intNumber($source)) {
            $Content = App::loadModel('App', 'WebContent', true);
            $source = $Content->findField('locator', array(
                'conditions' => array(
                    'id' => $source,
                    'active' => true,
                )
            ));
        }
        if (empty($source)) {
            return '';
        }
        // create url with requestSource = frame and contentId set to actual content
        if (!empty($options['_content']['id'])) {
            $content = $options['_content'];
        }
        else {
            $content = App::getPropertyContent();
        }
        $url = App::getUrl(array(
            'locator' => $source,
            'source'=> 'frame',
            'get' => array('_contentId_' => $content['id'])
        ));
        $jsOptions = array_diff_key($options, $defaults);
        $jsDefaults = array(
            //'maxWidth'	=> 800,
            //'maxHeight'	=> 600,
            //'minWidth'	=> 800,
            //'minHeight'	=> 600,
            'fitToView'	=> false,
            'width' => '80%',
            'height' => '80%',
            'autoSize' => false,
            'closeClick' => false,
            'openEffect' => 'none',
            'closeEffect' => 'none',
        );
        $jsOptions = array_merge($jsDefaults, Arr::camelizeKeys($jsOptions, array(
            'separator' => '-',
            'depth' => 1,
        )));
        return $this->loadView('/AppBasicActions/popupButton', array(
            'url' => $url,
            'label' => Sanitize::value($this->params['label'], '...', true),
            'jsOptions' => $jsOptions
        ));
    }
}
