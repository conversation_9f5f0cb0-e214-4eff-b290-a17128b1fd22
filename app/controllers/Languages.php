<?php
class Languages extends Controller {
    
    public function admin_index() {
        $Language = $this->loadModel('Language', true);
        $conditions = null;
        if (App::getUser('Group.pid') !== 'admins') {
            $conditions = array(
                'active' => true,
            );
        }
        $languages = $Language->find(array(
            'conditions' => $conditions,
            'order' => 'sort ASC',
            'paginate' => true,
        ));
        App::setSeoTitle(__a(__FILE__, 'Languages'));
        // set columns - display 'active' only to admins. (this could be repaced by 'if' in column definition)
        $columns = array(
            'name' => __a(__FILE__, 'Name'),
            'lang' => __a(__FILE__, 'Lang'),
            'code' => __a(__FILE__, 'Code'),
            'locale' => __a(__FILE__, 'Locale'),
            'active' => __a(__FILE__, 'Active'),
            'published' => __a(__FILE__, 'Published'),
            'default' => __a(__FILE__, 'Default'),
        );
        if (App::getUser('Group.pid') !== 'admins') {
            unset($columns['active']);
        }
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'List of site languages'),
            'records' => $languages,
            'columns' => $columns,
            'renderFields' => array(
                'active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'published' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'default' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
//                'default' => array($Language, 'renderFieldDefault'),
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('default' => 1),
                    'attributes' => array('class' => 'default'),
                ),
                array(
                    'conditions' => array('published' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                ),
                array(
                    'conditions' => array('active' => 0),
                    'attributes' => array('class' => '-run-six-hidden'),
                ),
            ),
//            'renderRow' => array($Language, 'renderRow'),
            'Paginator' => $Language->Paginator,
            'actions' => array(                
                'add' => array(
                    'url' => '/mvc/App/Languages/admin_add',
                ),
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => '/mvc/App/Languages/admin_edit',
                ),
                'view' => array(
                    'url' => '/mvc/App/Languages/admin_view',
                ),
                'copy' => array(
                    'url' => '/mvc/App/Languages/admin_add',
                ),
                'delete' => array(
                    'url' => '/mvc/App/Languages/admin_delete',
                    'confirmMessage' => __a(__FILE__, 'Please, comfirm removal of :name: language'),
                ),
                'move' => array(
                    'url' => '/mvc/App/Languages/admin_move',
                )
            )
        ));
    }
    
    public function admin_add($copyId = null) {
        $Language = $this->loadModel('Language', true);
        
        if ($this->data) {
            if ($Language->addOrderedItem($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'New record has been succesfully created'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                    'args' => array($Language->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
        }
        else {
            $this->data = array();
        }
        
        App::setSeoTitle(__a(__FILE__, 'New language'));

        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Add new language'),
            'data' => $this->data,
            'Model' => $Language,
            'columns' => 4,
            'fields' => array(
                array('row'),
                    array('field' => 'name', 'label' => __a(__FILE__, 'Name')),
                    array('field' => 'lang', 'label' => __a(__FILE__, 'Lang code')),
                    array('field' => 'icon', 'label' => __a(__FILE__, 'Icon')),
                array('/row'),
                array('if' => App::getUser('Group.pid') === 'admins'),
                    array('row'),
                        array('field' => 'code', 'label' => __a(__FILE__, 'Code')),
                        array('field' => 'locale', 'label' => __a(__FILE__, 'Locale')),
                        array('field' => 'localized_default', 'label' => __a(__FILE__, 'Localized default')),
                        array('field' => 'active', 'label' => __a(__FILE__, 'Active')),
                    array('/row'),
                array('endif'),
                array('row'),
                    array('field' => 'default', 'label' => __a(__FILE__, 'Default')),
                    array('field' => 'published', 'label' => __a(__FILE__, 'Published')),
                array('/row'),
            )
        ));
    }
    
    public function admin_edit($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        
        $Language = $this->loadModel('Language', true);
        
        if ($this->data) {
            if ($Language->save($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                App::redirect(App::$url);
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        else {
            $this->data = $Language->findFirstBy('id', $id);
            // get file fields real path
            if (!empty($this->data['icon'])) {
                $this->data['icon'] = $Language->getFileFieldUrlPath('icon', array('file' => $this->data['icon']));
            }
        }
        
        // add name to title and tabTitle
        if (!empty($this->data['name'])) {
            $name = $this->data['name'];
        }
        // - name should not be empty, if so then it is error so use actual name
        else {
            $name = $Language->findFieldBy('name', 'id', $this->data['id']);
        }
        App::setSeoTitle(__a(__FILE__, '&quot;%s&quot;', $name));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Edit language &quot;%s&quot;', $name),
            'data' => $this->data,
            'Model' => $Language,
            'columns' => 4,
            'fields' => array(
                array('field' => 'id', 'type' => 'hidden'),
                array('row'),
                    array('field' => 'name', 'label' => __a(__FILE__, 'Name')),
                    array('field' => 'lang', 'label' => __a(__FILE__, 'Lang code'), 'type' => 'display'),
                    array('field' => 'icon', 'label' => __a(__FILE__, 'Icon')),
                    array(
                        'field' => 'icon', 
                        'type' => 'image',
                        'deleteImage' => '/mvc/App/Languages/admin_deleteFile/icon/' . $this->data['id'],
                        'style' => 'display:block;padding-right:22px;' // to display delete button out off the image     itself
                    ),
                array('/row'),
                array('if' => App::getUser('Group.pid') === 'admins'),
                array('row'),
                        array('field' => 'code', 'label' => __a(__FILE__, 'Code')),
                        array('field' => 'locale', 'label' => __a(__FILE__, 'Locale')),
                        array('field' => 'localized_default', 'label' => __a(__FILE__, 'Localized default')),
                        array('field' => 'active', 'label' => __a(__FILE__, 'Active')),
                    array('/row'),
                array('endif'),
                array('row'),
                    array('field' => 'default', 'label' => __a(__FILE__, 'Default')),
                    array('field' => 'published', 'label' => __a(__FILE__, 'Published')),
                array('/row'),
            )
        ));
    }
              
    public function admin_move($id = null, $orderIndex = null) { 
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        $Language = $this->loadModel('Language', true);
        if (!$Language->moveOrderedItem($id, array('newOrderIndex' => $orderIndex))) {
            $Response->success = false;
            App::setErrorMessage(__a(__FILE__, 'Item move has failed'), true);
        }
        $this->setLayout('json');
        return $Response->getJson();
    }
    
    public function admin_delete($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        $Language = $this->loadModel('Language', true);
        $Language->deleteOrderedItem($id);
        App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }
    
    public function admin_deleteFile($fileField = null, $id = null) {
        if (!$fileField || !$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record fileField and/or id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        $Language = $this->loadModel('Language', true);
        $fileFields = $Language->getPropertyFileFields();
        if (!isset($fileFields[$fileField])) {
            App::setErrorMessage(__a(__FILE__, 'Invalid file field'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/',
                'source' => App::$requestSource,
            ))));
        }
        $Language->save(array('id' => $id, $fileField => ''));
        App::setSuccessMessage(__a(__FILE__, 'File has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }
    
    /**
     * MVC ELEMENT
     * 
     * Generates language menu 
     * 
     * @return string
     */
    public function menu() {
        $this->displayOriginComment = true;
        // retrieve lang menu
        $languages = App::getPropertyLanguages();
        $defaultLang = null;
        $menuItems = array();
        foreach ($languages as $k => $language) {
            if (!$language['published']) {
                continue;
            }
            if ($k === 'default') {
                $defaultLang = $language['lang'];
            }
            $menuItems[$language['id']] = array(
                'label' => $language['lang'],
                'locator' => $language['lang'],
            );
        }
        // display nothing if there is just 1 language
        if (count($menuItems) < 2) {
            return '';
        }
        // simulate tree data
        $menuItems = array($menuItems); 
        
        return $this->loadView('Languages/menu', array(
            'menuItems' => $menuItems,
            'defaultLang' => $defaultLang,
        ));        
    }
    
    /**
     * MVC ELEMENT
     * 
     * Generates language dropdown menu 
     * 
     * @return string
     */
    public function dropdownMenu() {
        $this->displayOriginComment = true;
        // retrieve lang menu
        $languages = App::getPropertyLanguages();
        $Language = $this->loadModel('Language', true);
        $defaultLang = null;
        $items = array();
        $activeItem = array();
        $defaultLang = $languages['default'];
        foreach ($languages as $language) {
            if (!$language['published']) {
                continue;
            }
            if ($language['lang'] === $defaultLang) {
                $url = '/';
            }
            else {
                $url = '/' . $language['lang'];
            }
            $item = array(
                'url' => $url,
                'label' => $language['lang'],
                'lang' => $language['lang'],
                'icon' => $Language->getFileFieldUrlPath('icon', array('file' => $language['icon'])),
            );
            // if active lang then take it aside and prepend it as the first
            if ($language['lang'] === App::$lang) {
                $activeItem[$language['id']] = $item;
            }
            else {
                $items[$language['id']] = $item;
            }
        }     
        $items = $activeItem + $items;
        return $this->loadView('Languages/dropdownMenu', array(
            'items' => $items,
        ));        
    }
        
}