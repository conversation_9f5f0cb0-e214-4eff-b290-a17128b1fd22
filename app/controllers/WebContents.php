<?php
class WebContents extends Controller {
    
    /**
     * Web contents cection pid the controller is destinated for.
     * This can be explicitly set or let to guessed from cotroller class name by
     * __construct(). 
     * 
     * @var string 
     */
    protected $sectionPid = null;
    
    public function __construct() {
        parent::__construct();
        
        // guess sectionPid from class name and normalize it
        if (
            empty($this->sectionPid)
            && substr($this->name, 0, 11) === 'WebContents'
        ) {
            $this->sectionPid = lcfirst(substr($this->name, 11));
        }
        if (empty($this->sectionPid)) {
            $this->sectionPid = null;
        }
    }
    
    /**
     * Return index of all contents (regardles to section pid) for provided lang.
     * The lang is received by $_GET['lang'] from select action. 
     * If no lang provided then DEFAULT_LANG is used.
     * 
     * @param string $_GET['lang'] Optional. Defaults to app DEFAULT_LANG.
     * 
     * @return string Html view
     */
    public function admin_indexAllContents() {        
        $Content = $this->loadModel('WebContent', true);
        
        // get lang to retrieve contents for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        // define top level root conditions
        $root = array(
            'conditions' => array (
                'lang' => $lang,
                'parent_id' => null,
            ),
        );
        // get tree select list conditions
        $conditions = $Content->getAdministratedRecordsConditions($lang);
        // get records
        $webcontents = $Content->findInTree(
            $root,
            array(
                'fields' => array(
                    'id',
                    'pid',
                    'name',
                    'lang',
                    'locator',
                    'layout',
                    'active',
                    'permanent',
                    'hidden',
                    'path',
                ),
                'conditions' => $conditions,
                'order' => 'sort',
                'paginate' => true,
            )
        );
                
        $langInfo = '';
        if ($lang !== DEFAULT_LANG) {
            $langInfo = ' - ' . strtoupper($lang);
        }
        App::setSeoTitle(__a(__FILE__, 'Web content - index') . $langInfo);
        // render view
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'Web content'),
            'records' => $webcontents,
            'columns' => array(
                'name' => __a(__FILE__, 'Name'),
                'locator' => __a(__FILE__, 'Page locator'),
                'pid' => __a(__FILE__, 'PID'),
                'id' => __a(__FILE__, 'ID'),
                'active' => __a(__FILE__, 'Active'),
            ),
            'renderFields' => array(
                'active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'locator' => function($value) use ($lang) {
                    return Html::hyperlinkTag($value, $value, array(
                        'lang' => $lang,
                        'attributes' => array('target' => '_blank')
                    ));
                },
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('active' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                ),
                array(
                    'conditions' => array('hidden' => 1),
                    'attributes' => array('class' => '-run-six-hidden'),
                ),
            ),
            'Paginator' => $Content->Paginator,
            'actions' => array(
                'export' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_export',
                        'inherit' => array('get' => 'lang'),
                    ),
                ),
                'lang' => true
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_edit',
                    ),
                ),
            )
        ));
    }
    
    /**
     * Return index of contents for provided lang.
     * The lang is received by $_GET['lang'] from select action. 
     * If no lang provided then DEFAULT_LANG is used.
     * 
     * @param string $_GET['lang'] Optional. Defaults to app DEFAULT_LANG.
     * @param string $sectionPid Optional. Section root node pid. Defaults to NULL, 
     *      means the specified lang root node is used.
     * 
     * @return string Html view
     */
    public function admin_index($sectionPid = null) {
        $sectionPid = Sanitize::value($sectionPid, $this->sectionPid);
        
        $Content = $this->loadModel('WebContent', true);
        
        // get lang to retrieve contents for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        // define top level root conditions
        $root = array(
            'conditions' => array (
                'lang' => $lang,
            ),
        );
        if (empty($sectionPid)) {
            $root['conditions']['parent_id'] = null;
        }
        else {
            $root['conditions']['pid'] = $sectionPid;
        }
        // get tree select list conditions
        $conditions = $Content->getAdministratedRecordsConditions($lang, array(
            'excludeSpecificSections' => empty($sectionPid),
        ));
        // get records
        $webcontents = $Content->findInTree(
            $root,
            array(
                'fields' => array(
                    'id',
                    'pid',
                    'name',
                    'lang',
                    'locator',
                    'layout',
                    'active',
                    'permanent',
                    'hidden',
                    'path',
                ),
                'conditions' => $conditions,
                'order' => 'sort',
                'paginate' => true,
            )
        );
                
        $langInfo = '';
        if ($lang !== DEFAULT_LANG) {
            $langInfo = ' - ' . strtoupper($lang);
        }
        App::setSeoTitle(__a(__FILE__, 'Web content - index') . $langInfo);
        // render view
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'Web content'),
            'records' => $webcontents,
            'columns' => array(
                'name' => __a(__FILE__, 'Name'),
                'locator' => __a(__FILE__, 'Page locator'),
                'pid' => __a(__FILE__, 'PID'),
                'id' => __a(__FILE__, 'ID'),
//                'layout' => __a(__FILE__, 'Page layout'),
                'active' => __a(__FILE__, 'Active'),
            ),
            'renderFields' => array(
                'active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'locator' => function($value) use ($lang) {
                    return Html::hyperlinkTag($value, $value, array(
                        'lang' => $lang,
                        'attributes' => array('target' => '_blank')
                    ));
                },
//                'layout' => array(
//                    '' => __a(__FILE__, 'Undefined layout'),
//                    'App.default' => __a(__FILE__, 'Default layout'),
//                    'App.home' => __a(__FILE__, 'Home layout'),
//                ),
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('active' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                ),
                array(
                    'conditions' => array('hidden' => 1),
                    'attributes' => array('class' => '-run-six-hidden'),
                ),
            ),
            'Paginator' => $Content->Paginator,
            'actions' => array(
                'showTree' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_showTree',
                        'inherit' => array('get' => 'lang'),
                    ),
                ),   
                'export' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_export',
                        'inherit' => array('get' => 'lang'),
                    ),
                ),
                'lang' => true
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_edit',
                    ),
                ),
            )
        ));
    }
    
    /**
     * Exports filtered contents of specified section to specified depth
     * 
     * @param string $sectionPid Defaults to NULL.
     * @param int $depth Defaults to NULL
     */
    public function admin_export($sectionPid = null, $depth = null) {
        $sectionPid = Sanitize::value($sectionPid, $this->sectionPid);
        
        $Content = $this->loadModel('WebContent', true);
        
        // get lang to retrieve contents for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        // define top level root conditions
        $root = array(
            'conditions' => array (
                'lang' => $lang,
            ),
        );
        if (empty($sectionPid)) {
            $root['conditions']['parent_id'] = null;
        }
        else {
            $root['conditions']['pid'] = $sectionPid;
        }
        // get tree select list conditions
        $conditions = $Content->getAdministratedRecordsConditions($lang, array(
            'excludeSpecificSections' => empty($sectionPid),
        ));
        
        if (App::getUser('Group.pid') !== 'admins') {
            $conditions['hidden'] = false;
        }
                
        $contents = $Content->findInTree(
            $root,
            array(
                'fields' => array('*'),
                'avoidFields' => array(
                    'WebContent.hidden',
                    'WebContent.permanent',
                ),
                'conditions' => $conditions,
                'order' => 'sort',
                'depth' => !empty($depth) && Validate::intNumber($depth) ? $depth : null,
                // allow paginator filtering & sorting without limit
                'paginate' => true,
                'limit' => false,
            )
        );
        
        $Content->export($contents, array(
            'file' => $this->name . ucfirst($sectionPid),
            'format' => 'xlsx',
        ));
    }    
    
    /**
     * Shows tree single level of contents for provided parentPid and lang.
     * The lang is received by $_GET['lang'] from select action. 
     * If no lang provided then DEFAULT_LANG is used.
     * 
     * @param int $parentPid Pid of parent to display childs for. If NULL then 
     *      top level is shown
     * @param string $_GET['lang'] Optional
     * 
     * @return string View html
     */
    public function admin_showTreeLevel($parentPid = null) {
        $parentPid = Sanitize::value($parentPid, $this->sectionPid);
        
        $Content = $this->loadModel('WebContent', true);
        
        // get lang to retrieve contents for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;

        // define top level root conditions
        $parent = array(
            'conditions' => array (
                'lang' => $lang,
            ),
        );
        if (empty($parentPid)) {
            $parent['conditions']['parent_id'] = null;
        }
        else {
            $parent['conditions']['pid'] = $parentPid;
        }
        // get tree select list conditions
        $conditions = $Content->getAdministratedRecordsConditions($lang, array(
            'excludeSpecificSections' => empty($parentPid),
        ));
        $order = 'sort ASC';
        $descendingOrder = false;
        if (
            $parentPid === 'news'
            || $parentPid === 'articles'
            || $parentPid === 'references'
        ) {
            $order = 'sort DESC';
            $descendingOrder = true;
        }
        // get the parent direct childs
        $contents = $Content->findInTree(
            $parent,
            array(
                'fields' => array(
                    'id',
                    'pid',
                    'name',
                    'lang',
                    'locator',
                    'layout',
                    'active',
                    'permanent',
                    'hidden',
                    'path',
                ),
                'conditions' => $conditions,
                'order' => $order,
                'depth' => 1,
                'paginate' => true,
                'limit' => false,
            ),
            // get root parent id in case 
            $parentId
        );
        
        // validate root
        if (empty($parentId)) {
            App::setErrorMessage(__a(__FILE__, 'Invalid tree root specification'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        
        if ($parentPid === 'sliders') {
            $tabTitle = __a(__FILE__, 'Sliders');
            $title = __a(__FILE__, 'Sliders');
            $addActionTitle = __a(__FILE__, 'Add slider');
        }
        elseif ($parentPid === 'news') {
            $tabTitle = __a(__FILE__, 'News');
            $title = __a(__FILE__, 'News');
            $addActionTitle = __a(__FILE__, 'Add news');
        }
        elseif ($parentPid === 'articles') {
            $tabTitle = __a(__FILE__, 'Blog articles');
            $title = __a(__FILE__, 'Blog articles');
            $addActionTitle = __a(__FILE__, 'Add article');
        }
        elseif ($parentPid === 'references') {
            $tabTitle = __a(__FILE__, 'References');
            $title = __a(__FILE__, 'References');
            $addActionTitle = __a(__FILE__, 'Add reference');
        }
        else {
            $tabTitle = __a(__FILE__, 'Web content');
            $title = __a(__FILE__, 'Web content');
            $addActionTitle = __a(__FILE__, 'Add section');
        }
        
        $langInfo = '';
        if ($lang !== DEFAULT_LANG) {
            $langInfo = ' - ' . strtoupper($lang);
        }
        $tabTitle .= $langInfo;
        
        App::setSeoTitle($tabTitle);
        // render view
        return Html::smartIndex(array(
            'title' => $title,
            'records' => $contents,
            'columns' => array(
                'name' => __a(__FILE__, 'Name'),
                'locator' => __a(__FILE__, 'Page locator'),
                'pid' => __a(__FILE__, 'PID'),
                'id' => __a(__FILE__, 'ID'),
//                'layout' => __a(__FILE__, 'Page layout'),
//                'lang' => __a(__FILE__, 'Lang'),
                'active' => __a(__FILE__, 'Active'),
            ),
            'renderFields' => array(
                'active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'locator' => function($value) use ($lang) {
                    return Html::hyperlinkTag($value, $value, array(
                        'lang' => $lang,
                        'attributes' => array('target' => '_blank')
                    ));
                },
//                'layout' => array(
//                    '' => __a(__FILE__, 'Undefined layout'),
//                    'App.default' => __a(__FILE__, 'Default layout'),
//                    'App.home' => __a(__FILE__, 'Home layout'),
//                ),
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('active' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                ),
                array(
                    'conditions' => array('hidden' => 1),
                    'attributes' => array('class' => '-run-six-hidden'),
                ),
            ),
            'Paginator' => $Content->Paginator,
            'actions' => array(
                'add' => array(
                    'title' => $addActionTitle,
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_add',
                        'args' => array($parentId),
                    ),
                ),
                'export' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_export',
                        'args' => array($parentPid, 1),
                        'inherit' => array('get' => 'lang'),
                    ),
                ),
                'lang' => true,
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_edit',
                    ),
                ),
                'copy' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_copy',
                    ),
                ),
                'delete' => array(
                    'if' => array('permanent' => 0),
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_delete',
                    ),
                    'confirmMessage' => __a(__FILE__, 'Please, comfirm removal of :name: web content'),
                ),
                'move' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_move',
                        'args' => array($parentId),
                    ),
                    'descendingOrder' => $descendingOrder,
                ),
            ),
            'recordDefaultAction' => 'edit'
        ));
    }
    
    /**
     * Shows tree of contents for provided lang.
     * The lang is received by $_GET['lang'] from select action. 
     * If no lang provided then DEFAULT_LANG is used.
     * 
     * @param string $_GET['lang'] Optional. Defaults to app DEFAULT_LANG.
     * @param string $sectionPid Optional. Section root node pid. Defaults to NULL, 
     *      means the specified lang root node is used.
     * 
     * @return string View html
     */
    public function admin_showTree($sectionPid = null) {
        $sectionPid = Sanitize::value($sectionPid, $this->sectionPid);
        
        $Content = $this->loadModel('WebContent', true);
        
        // get lang to retrieve contents for
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        // define top level root conditions
        $root = array(
            'conditions' => array (
                'lang' => $lang,
            ),
        );
        if (empty($sectionPid)) {
            $root['conditions']['parent_id'] = null;
        }
        else {
            $root['conditions']['pid'] = $sectionPid;
        }
        // get tree select list conditions
        $conditions = $Content->getAdministratedRecordsConditions($lang, array(
            'excludeSpecificSections' => empty($sectionPid),
        ));
        // get the tree
        $contents = $Content->findInTree(
            $root, 
            array(
                'fields' => array(
                    'pid',
                    'name',
                    'lang',
                    'locator',
                    'layout',
                    'active',
                    'permanent',
                    'hidden',
                    'path',
                ),
                'conditions' => $conditions,
                'order' => 'sort',
            ),
            $rootId
        );
        // validate root
        if (empty($rootId)) {
            App::setErrorMessage(__a(__FILE__, 'Invalid tree root specification'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        
        if ($sectionPid === 'sliders') {
            $tabTitle = __a(__FILE__, 'Sliders');
            $title = __a(__FILE__, 'Sliders');
            $addActionTitle = __a(__FILE__, 'Add slider');
        }
        elseif ($sectionPid === 'news') {
            $tabTitle = __a(__FILE__, 'News');
            $title = __a(__FILE__, 'News');
            $addActionTitle = __a(__FILE__, 'Add news');
        }
        elseif ($sectionPid === 'articles') {
            $tabTitle = __a(__FILE__, 'Blog articles');
            $title = __a(__FILE__, 'Blog articles');
            $addActionTitle = __a(__FILE__, 'Add article');
        }
        elseif ($sectionPid === 'references') {
            $tabTitle = __a(__FILE__, 'References');
            $title = __a(__FILE__, 'References');
            $addActionTitle = __a(__FILE__, 'Add reference');
        }
        else {
            $tabTitle = __a(__FILE__, 'Web content');
            $title = __a(__FILE__, 'Web content');
            $addActionTitle = __a(__FILE__, 'Add section');
        }
        
        $langInfo = '';
        if ($lang !== DEFAULT_LANG) {
            $langInfo = ' - ' . strtoupper($lang);
        }
        $tabTitle .= $langInfo;
        
        App::setSeoTitle($tabTitle);
        // render view
        return Html::smartIndex(array(
            'title' => $title,
            'tree' => array(
                'column' => 'name',
                'status' => 'collapsed', //'expanded'
                'showNodeType' => true,
            ),
            'records' => $contents,
            'columns' => array(
                'name' => __a(__FILE__, 'Name'),
                'locator' => __a(__FILE__, 'Page locator'),
                'pid' => __a(__FILE__, 'PID'),
//                'layout' => __a(__FILE__, 'Page layout'),
//                'lang' => __a(__FILE__, 'Lang'),
//                'active' => __a(__FILE__, 'Active'),
            ),
            'renderFields' => array(
                'active' => array(
                    0 => __a(__FILE__, 'No'),
                    1 => __a(__FILE__, 'Yes'),
                ),
                'locator' => function($value) use ($lang) {
                    return Html::hyperlinkTag($value, $value, array(
                        'lang' => $lang,
                        'attributes' => array('target' => '_blank')
                    ));
                },
//                'layout' => array(
//                    '' => __a(__FILE__, 'Undefined layout'),
//                    'App.default' => __a(__FILE__, 'Default layout'),
//                    'App.home' => __a(__FILE__, 'Home layout'),
//                ),
            ),
            'renderRow' => array(
                array(
                    'conditions' => array('active' => 0),
                    'attributes' => array('class' => '-run-six-inactive'),
                ),
                array(
                    'conditions' => array('hidden' => 1),
                    'attributes' => array('class' => '-run-six-hidden'),
                ),
            ),
            'actions' => array(
                'showIndex' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_index',
                        'inherit' => array('get' => 'lang'),
                    ),
                ),
                'add' => array(
                    'title' => $addActionTitle,
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_add',
                        'args' => array($rootId),
                    ),
                ),
                'export' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_export',
                        'args' => array($sectionPid),
                        'inherit' => array('get' => 'lang'),
                    ),
                ),
                'lang' => true,
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_edit',
                    ),
                ),
                'copy' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_copy',
                    ),
                ),
                'delete' => array(
                    'if' => array('permanent' => 0),
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_delete',
                    ),
                    'confirmMessage' => __a(__FILE__, 'Please, comfirm removal of :name: web content'),
                ),
                'addChild' => array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_add',
                    ),
                ),
            )
        ));
    }
    
    /**
     * Action to add new web content under provided parentId and possibly copying
     * specfied web content
     * 
     * @param int $parentId
     * @param int $copyId
     * 
     * @return string View html
     */
    public function admin_add($parentId = null) {
        $Content = $this->loadModel('WebContent', true);
        
        // check if the new created node is top level node (its parent is root)
        if ($parentId) {
            //rblb//$nodeLevel = $Content->findLevelInTree($parentId) + 1;
            $parents = $Content->findUpInTree($parentId, array(
                'order' => 'WebContent.sort ASC',
            ));
            $nodeLevel = count($parents) + 1;
            $sectionPid = Sanitize::value($parents[1]['pid'], false);
        }
        // or if no parent id provided then set it to top level node in actual App::$lang
        else {
            $nodeLevel = 2;
            $parents = $Content->findUpInTree(
                array(
                    'conditions' => array(
                        'parent_id' => null, 
                        'lang' => App::$lang
                    )
                ),             
                $parentId, 
                array(
                    'order' => 'WebContent.sort ASC',
                ),
                $parent
            );
            $sectionPid = Sanitize::value($parents[1]['pid'], false);
            $parentId = $parent['id'];
        }
        
        // resolve nodeType and its title and tabTitle
        $parent = $Content->findFirstBy('id', $parentId, array('fields' => array('name', 'lang')));
        if ($nodeLevel === 2) {
            $nodeType = 'container';
            $tabTitle = __a(__FILE__, 'New section');
            $title = __a(__FILE__, 'New section');
        }
        elseif (
            $sectionPid === 'sliders'
            && $nodeLevel === 3
        ) {
            $nodeType = 'container';
            $tabTitle = __a(__FILE__, 'New slider');
            $title = __a(__FILE__, 'New slider');
        }
        elseif (
            $sectionPid === 'sliders'
            && $nodeLevel > 3
        ) {
            $nodeType = 'page';
            $tabTitle = __a(__FILE__, 'New slide');
            $title = __a(__FILE__, 'New slide under &quot;%s&quot;', $parent['name']);
        }
        elseif ($sectionPid === 'news') {
            $nodeType = 'page';
            $tabTitle = __a(__FILE__, 'Add news item');
            $title = __a(__FILE__, 'Add news item');
        }
        elseif ($sectionPid === 'articles') {
            $nodeType = 'page';
            $tabTitle = __a(__FILE__, 'New article');
            $title = __a(__FILE__, 'New article');
        }
        elseif ($sectionPid === 'references') {
            $nodeType = 'page';
            $tabTitle = __a(__FILE__, 'New reference');
            $title = __a(__FILE__, 'New reference');
        }
        else {
            $nodeType = 'page';
            $tabTitle = __a(__FILE__, 'New web page');
            $title = __a(__FILE__, 'New web page under &quot;%s&quot;', $parent['name']);
        }
        
        if ($this->data) {
            $this->data['parent_id'] = $parentId;
            $alternative = array($nodeType);
            if (!empty($sectionPid)) {
                $alternative[] = $sectionPid;
            }
            if ($Content->addTreeNode($this->data['parent_id'], $this->data, array(
                'create' => true,
                'alternative' => $alternative,
            ))) {
                App::setSuccessMessage(__a(__FILE__, 'New record has been succesfully created'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                    'args' => array($Content->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
        }
                
        $lang = $parent['lang'];
        $langInfo = '';
        if ($parent['lang'] !== DEFAULT_LANG) {
            $langInfo = ' - ' . strtoupper($parent['lang']);
        }
        $tabTitle .= $langInfo;
        
        App::setSeoTitle($tabTitle);
        return Html::smartForm(array(
            'title' => $title,
            'data' => $this->data,
            'Model' => $Content,
            'columns' => 4,
            'tabsToReloadAfterSave' => array(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_index',
                )),
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_showTree',
                )),
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_showTreeLevel',
                )),
            ),
            'actions' => array(
                // a bit of hack to diplay default lang icon in form header
                'lang' => array(
                    'options' => array_combine(array($lang), array($lang)),
                )
            ),
            'fields' => array(
                array(
                    'field' => 'name', 
                    'label' => __a(__FILE__, 'Name'),
                    'autofocus' => true,
                ),
                array('if' => $sectionPid === 'references'),
                    array(
                        'field' => 'menu_icon', 
                        'label' => __a(__FILE__, 'Image'),
                        'hint' => __a(
                             __FILE__, 
                             'Square image, the best :width: x :height:px.',
                             array('width' => 340, 'height' => 340)
                         )
                     ),
                array('endif'),
            )
        ));
    }
    
    public function admin_copy($id = null) {
        $this->loadModel('WebContent');
        $Content = new WebContent();
        try {
            if (
                ($result = $Content->duplicate($id, array(
                    'copyData' => function($data) {
                        if (array_key_exists('name', $data)) {
                            $data['name'] = __a(__FILE__, 'Copy of %s', $data['name']);
                        }
                        // if webmaster (or lower group) makes a copy of permanent content
                        // he must have a chance to delete it
                        $data['permanent'] = 0;
                        // pids must be unique (in lang branch) so there is no need to copy it
                        $data['pid'] = null;
                        return $data;
                    }
                )))
            ) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succefully copied'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                    'args' => $result,
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'The record copying has failed'));
            App::logError(__e(__FILE__, 'Web content copying has failed with following validation errors', array(
                'var' => $Content->getErrors(true),
                'email' => true,
            )));
        } 
        catch (Throwable $e) {
            App::setErrorMessage(__a(__FILE__, 'The record copying has failed'));
            App::logError(__e(__FILE__, 'Web content copying has failed with following exception', array(
                'var' => $e,
                'email' => true,
            )));
            
        }
        App::redirect(App::getUrl(array(
            'locator' => '/_error',
            'source' => App::$requestSource,
        )));
    }
    
    public function admin_edit($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        
        $Content = $this->loadModel('WebContent', true);
        
        $parents = $Content->findUpInTree($id, array(
            'order' => 'WebContent.sort ASC',
        ));
        $nodeLevel = count($parents);
        $sectionPid = Sanitize::value($parents[1]['pid'], false);
        
        // resolve nodeType and its title and tabTitle (string is filled here below after data are retrieved)
        if ($nodeLevel === 2) {
            $nodeType = 'container';
            $tabTitle = __a(__FILE__, '&quot;%s&quot; - section');
            $title = __a(__FILE__, 'Edit section &quot;%s&quot;');
        }
        elseif (
            $sectionPid === 'sliders'
            && $nodeLevel === 3
        ) {
            $nodeType = 'container';
            $tabTitle = __a(__FILE__, '&quot;%s&quot; - slider');
            $title = __a(__FILE__, 'Edit slider &quot;%s&quot;');
        }
        elseif (
            $sectionPid === 'sliders'
            && $nodeLevel > 3
        ) {
            $nodeType = 'page';
            $tabTitle = __a(__FILE__, '&quot;%s&quot; - slide');
            $title = __a(__FILE__, 'Edit slide &quot;%s&quot;');
        }
        elseif ($sectionPid === 'news') {
            $nodeType = 'page';
            $tabTitle = __a(__FILE__, '&quot;%s&quot;');
            $title = __a(__FILE__, 'Edit news item &quot;%s&quot;');
        }
        elseif ($sectionPid === 'articles') {
            $nodeType = 'page';
            $tabTitle = __a(__FILE__, '&quot;%s&quot;');
            $title = __a(__FILE__, 'Edit article &quot;%s&quot;');
        }
        elseif ($sectionPid === 'references') {
            $nodeType = 'page';
            $tabTitle = __a(__FILE__, '&quot;%s&quot;');
            $title = __a(__FILE__, 'Edit reference &quot;%s&quot;');
        }
        else {
            $nodeType = 'page';
            $tabTitle = __a(__FILE__, '&quot;%s&quot;');
            $title = __a(__FILE__, 'Edit page &quot;%s&quot;');
        }
        
        if ($this->data) {
            $alternative = array($nodeType);
            if (!empty($sectionPid)) {
                $alternative[] = $sectionPid;
            }
            if ($Content->save($this->data, array(
                'alternative' => $alternative,
            ))) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                App::redirect(App::$url);
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
        }
        else {
            $this->data = $Content->findFirstBy('id', $id);
            // if product does not exist then redirect to referer
            if (empty($this->data)) {
                App::setErrorMessage(__a(__FILE__, 'Invalid record id %s', (string)$id));
                App::redirect(App::getUrl(array(
                    'locator' => '/_error',
                    'source' => App::$requestSource,
                )));
            }
            // get file fields real path
            $fileFields = array_keys($Content->getPropertyFileFields());
            foreach($fileFields as $fileField) {
                if (!empty($this->data[$fileField])) {
                    $this->data[$fileField] = $Content->getFileFieldUrlPath($fileField, array('file' => $this->data[$fileField]));
                }
            }
            // preset new previous sibling id to actual one
            $previousSiblingIdConditions = array(
                array(   
                    'pid' => null,
                    'OR',
                    'pid !=' => $Content->specificSections,
                )
            );
            if (empty($this->data['hidden'])) {
                $previousSiblingIdConditions['hidden'] = false;
            }
            $this->data['previous_sibling_id'] = $Content->getPreviousSiblingId(
                $this->data, $previousSiblingIdConditions
            );
            $this->data['new_previous_sibling_id'] = $this->data['previous_sibling_id'];
            
            // put together 'obfuscate' and 'autodetect_obfuscate'
            if ($this->data['autodetect_obfuscate']) {
                $this->data['obfuscate'] = 'auto';
            }
        }
        
        // display seo index and follow warnings
        if (isset($this->data['seo_index']) && empty($this->data['seo_index'])) {
            App::setWarningMessage(__a(__FILE__, 'Robots index is off! Page will not be included in Google search results.'));
        }
        if (isset($this->data['seo_follow']) && empty($this->data['seo_follow'])) {
            App::setWarningMessage(__a(__FILE__, 'Robots follow is off! Google will not follow links included in this page.'));
        }
        
        // add name to title and tabTitle
        if (!empty($this->data['name'])) {
            $name = $this->data['name'];
        }
        // - name should not be empty, if so then it is error so use actual name
        else {
            $name = $Content->findFieldBy('name', 'id', $this->data['id']);
        }
        $tabTitle = Str::fill($tabTitle, $name);
        $title = Str::fill($title, $name);
                
        $lang = !empty($this->data['lang']) ? $this->data['lang'] : DEFAULT_LANG;
        $langInfo = '';
        if ($this->data['lang'] !== DEFAULT_LANG) {
            $langInfo = ' - ' . strtoupper($this->data['lang']);
        }
        $tabTitle .= $langInfo;
        App::setSeoTitle($tabTitle);
        
        // create form version according sectionPid and nodeType:
        // - slider
        if (
            $sectionPid === 'sliders'
            && $nodeType === 'container'
        ) {
            $form = $Content->getSmartFormForSlider();
        }
        // - slide
        elseif (
            $sectionPid === 'sliders'
            && $nodeType === 'page'
        ) {
            $form = $Content->getSmartFormForSlide($this->data);
        }
        // - news
        elseif (
            $sectionPid === 'news'
        ) {
            if (
                !empty($this->data['menu_icon'])
                && is_string($this->data['menu_icon'])
            ) {
                $this->data['menu_icon'] = $Content->getFileFieldUrlPath('menu_icon', array('file' => $this->data['menu_icon'], 'variant' =>  'article'));
            }
            $form = $Content->getSmartFormForNews($this->data);
        }
        // - articles
        elseif (
            $sectionPid === 'articles'
        ) {
            if (
                !empty($this->data['menu_icon'])
                && is_string($this->data['menu_icon'])
            ) {
                $this->data['menu_icon'] = $Content->getFileFieldUrlPath('menu_icon', array('file' => $this->data['menu_icon'], 'variant' =>  'article'));
            }
            $form = $Content->getSmartFormForArticle($this->data);
        }
        // - reference
        elseif (
            $sectionPid === 'references'
        ) {
            if (
                !empty($this->data['menu_icon'])
                && is_string($this->data['menu_icon'])
            ) {
                $this->data['menu_icon'] = $Content->getFileFieldUrlPath('menu_icon', array('file' => $this->data['menu_icon'], 'variant' =>  'reference'));
            }
            $form = $Content->getSmartFormForReference($this->data);
        }
        // - section
        elseif (
            $nodeType === 'container'
        ) {
            $form = $Content->getSmartFormForSection($this->data);
        }
        // - page
        else {
            $form = $Content->getSmartFormForPage($this->data, $sectionPid);
        }
        // render view
        return Html::smartForm(array(
            'title' => $title,
            'data' => $this->data,
            'Model' => $Content,
            'columns' => 4,
            'showAffix' => $nodeLevel !== 2,
            'tabsToReloadAfterSave' => array(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_index',
                )),
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_showTree',
                )),
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_showTreeLevel',
                )),
            ),
            'actions' => array(
                'lang' => array(
                    'options' => array_combine(array($lang), array($lang)),
                )
            ),
            'fields' => $form,
        ));
    }
        
    public function admin_view($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        $Content = $this->loadModel('WebContent', true);
        $content = $Content->findFirstBy('id', $id, array(
            'fields' => array(
                'WebContent.locator',
                'WebContent.lang',
            ),
        ));
        if (empty($content['locator'])) {
            // @todo
            return '';
        }
        App::redirect(App::getUrl($content), array(
            //'js' => true,
        ));
    }
        
    public function admin_delete($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        
        $this->loadModel('WebContent');
        $Content = new WebContent();
        try {
            $Content->deleteTreeNode($id, array(
                'softDelete' => false,
            ));
            App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        } 
        catch (Exception_DB_TablesReservationFailure $e) {
            App::setErrorMessage(__a(__FILE__, 'Deletion has failed because data are used by other process. Try later.'));
        }
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }
    
    public function admin_deleteFile($fileField = null, $id = null) {
        if (!$fileField || !$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record fileField and/or id'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        $Content = $this->loadModel('WebContent', true);
        $fileFields = $Content->getPropertyFileFields();
        if (!isset($fileFields[$fileField])) {
            App::setErrorMessage(__a(__FILE__, 'Invalid file field'));
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            ))));
        }
        $Content->save(array('id' => $id, $fileField => ''));
        App::setSuccessMessage(__a(__FILE__, 'File has been succesfully deleted'));
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'locator' => '/',
            'source' => App::$requestSource,
        ))));
    }
    
    public function admin_move($parentId = null, $id = null, $orderIndex = null) { 
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        // normalize parent id
        if (empty($parentId)) {
            $parentId = null;
        }
        $Content = $this->loadModel('WebContent', true);
        if (!$Content->moveTreeNode($id, $parentId, array('newOrderIndex' => $orderIndex))) {
            $Response->success = false;
            App::setErrorMessage(__a(__FILE__, 'Node move has failed'), true);
        }
        App::setLayout('App', 'json');
        return $Response->getJson();
    }
            
    /**
     * Action to display mini search element
     */
    public function miniSearch() {
        $this->displayOriginComment = true;
        // load view
        return $this->loadView(
            'WebContents/miniSearch', array(
                'searchTarget' => 'App.WebContents.indexSearch',  
                'data' => $this->data
            )
        );
    }
    
    public function indexSearch() {
        $this->displayOriginComment = true;
        $contents = array();
        if (!empty($this->data['keywords'])) {
            $Content = $this->loadModel('WebContent', true);
            $contents = $Content->search($this->data['keywords']);
        }
                
        return $this->loadView('WebContents/indexSearch', array(
            'contents' => $contents,
        ));
    }
    
    /**
     * @deprecated - use instead WebContents::menu()!
     * 
     * MVC ELEMENT
     * 
     * Generates main menu 
     * 
     * Following params (Controller::$params) are used by this method:
     *      - 'depth' (int) Defaults to 1.
     * 
     * @return string
     */
    public function mainMenu() {
        $this->displayOriginComment = true;
        $defaults = array(
            'pid' => 'mainmenu',
            'fields' => array(),
            'view' => 'WebContents/mainMenu',
            'class' => null,
            'title' => null,
            'openable' => true,
            'prependItems' => array(),
            'appendItems' => array(),
            
            // Html::menu() options
            'activeItem' => SLUG,
            'homeSlug' => HOME_SLUG,
            'depth' => 1,
            'lang' => URL_LANG,
            'labelWrappers' => array(),
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        // openable needs left label wrapper
        if ($options['openable']) {
            $options['labelWrappers'] = array_unique(
                array_merge((array)$options['labelWrappers'], array('l'))
            );
         }
        $Content = $this->loadModel('WebContent', true);
        $fields = array(
            'WebContent.name AS label',
            'WebContent.locator AS locator',
            'WebContent.locator IS NULL AS passive',
        );
        if (!empty($options['fields'])) {
            $fields = array_unique(
                array_merge($fields, (array)$options['fields'])
            );
        }
        $items = $Content->findTree(
            array(
                'conditions' => array(
                    'WebContent.pid' => $options['pid'],
                    'WebContent.lang' => App::$lang,
                ),
            ),
            array(
                'conditions' => array(
                    'WebContent.active' => true
                ),
                'fields' => $fields,
                'depth' => $options['depth'],
            )
        );     
        if (!empty($options['prependItems'])) {
            $items = Arr::mergeTree($options['prependItems'], $items);
        }
        if (!empty($options['appendItems'])) {
            $items = Arr::mergeTree($items, $options['appendItems']);
        }
        
        // populate subcategories for fist-level items pointing to 
        // category products index. Two levels of subcategories are populated:
        // child and grandChild categories. Ids of categories added to contents menu tree
        // are prepended by 'c' to avoid conflicts with content ids and also to 
        // distinguish them for CategorySmartAdminLauncher
        App::loadModel('Eshop', 'EshopProductCategory');
        $Category = new EshopProductCategory();
        $categoryIds = $Category->findList(array(
            'key' => 'EshopProductCategory.slug',
            'fields' => array('EshopProductCategory.id'),
        ));
        $categoriesTree = $Category->findTree('categories', array(
            'fields' => array(
                'EshopProductCategory.name AS label',
                'EshopProductCategory.slug AS locator',
                'EshopProductCategory.slug IS NULL AS passive',
                'EshopProductCategory.image AS image',
            ),
            'conditions' => array(
                'EshopProductCategory.active' => true,
            ),
            'depth' => 3,
        ));
        $categoryProductsSlug = App::getContentLocatorByPid('Eshop.EshopProducts.indexCategory');
        $tmp = array();
        $firstLevel = true;
        foreach ($items as $levelId => $levelItems) {
            if ($firstLevel && !empty($levelItems)) {
                $tmp[$levelId] = array();
                foreach ($levelItems as $itemId => $item) {
                    $tmp[$levelId][$itemId] = $item;
                    $itemUrl = App::parseUrl($item['locator']);
                    if (
                        $itemUrl['slug'] === $categoryProductsSlug
                        && !empty($itemUrl['args'])
                        && ($categorySlug = reset($itemUrl['args']))
                        && ($categoryId = Sanitize::value($categoryIds[$categorySlug]))
                        && ($childCategories = Sanitize::value($categoriesTree[$categoryId]))
                    ) {
                        $tmp[$itemId] = array();
                        foreach ($childCategories as $childCatregoryId => $childCategory) {
                            $childCategory['locator'] = App::getUrl(array(
                                'locator' => $categoryProductsSlug,
                                'args' => array($childCategory['locator']),
                            ));
                            // resolve default variant of child category image
                            if (!empty($childCategory['image'])) {
                                $childCategory['image'] = $Category->getFileFieldUrlPath('image', array(
                                    'file' => $childCategory['image'],
                                ));
                            }
                            $tmp[$itemId]['c' . $childCatregoryId] = $childCategory;
                            if (($grandChildCategories = Sanitize::value($categoriesTree[$childCatregoryId]))) {
                                $tmp['c' . $childCatregoryId] = array();
                                foreach ($grandChildCategories as $grandChildCategoryId => $grandChildCategory) {
                                    $grandChildCategory['locator'] = App::getUrl(array(
                                        'locator' => $categoryProductsSlug,
                                        'args' => array($grandChildCategory['locator']),
                                    ));
                                    // resolve default variant of grand child category image
                                    if (!empty($grandChildCategory['image'])) {
                                        $grandChildCategory['image'] = $Category->getFileFieldUrlPath('image', array(
                                            'file' => $grandChildCategory['image'],
                                        ));
                                    }
                                    $tmp['c' . $childCatregoryId]['c' . $grandChildCategoryId] = $grandChildCategory;
                                }
                            }
                        }
                    }
                }
            }
            else {
                $tmp[$levelId] = $levelItems;
            }
            // do this only for top-level items
            $firstLevel = false;
        }
        $items = $tmp;
        
        // add smart admin launcher to Html::menu() options
        App::loadLib('App', 'SmartAdminLauncher');
        $options['SmartAdminLauncher'] = new SmartAdminLauncher(
            App::getUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => 'admin_edit',
            )),
            array(
                'triggerTitle' => __a(__FILE__, 'Edit page'),
            )
        );
        $options['CategorySmartAdminLauncher'] = new SmartAdminLauncher(
            App::getUrl(array(
                'module' => 'Eshop',
                'controller' => 'EshopProductCategories',
                'action' => 'admin_edit',
            )),
            array(
                'triggerTitle' => __a(__FILE__, 'Upraviť kategóriu'),
            )
        );
        return $this->loadView($options['view'], array(
            'items' => $items,
            'options' => $options,
            'WebContent' => $Content,
        ));
    }
    
    /**
     * @deprecated - use instead WebContents::menu()!
     * 
     * MVC ELEMENT
     * 
     * Generates footer menu 
     * 
     * Following params (Controller::$params) are used by this method:
     *      - 'depth' (int) Defaults to 1.
     * 
     * @return string
     */
    public function footerMenu() {
        $this->displayOriginComment = true;
        $defaults = array(
            'depth' => 1,
        );
        $options = array_merge($defaults, $this->params);
        $Content = $this->loadModel('WebContent', true);
        $items = $Content->findTree(
            array(
                'conditions' => array(
                    'WebContent.pid' => 'footermenu',
                    'WebContent.lang' => App::$lang,
                ),
            ),
            array(
                'conditions' => array(
                    'WebContent.active = 1',
                ),
                'fields' => array(
                    'WebContent.name AS label',
                    'WebContent.locator AS locator',
                    'WebContent.locator IS NULL AS passive',
                ),
                'depth' => $options['depth'],
            )
        );
        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContents/footerMenu', array(
            'items' => $items,
            'depth' => $options['depth'],
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit page'),
                )
            ),
        ));        
    }
    
    /**
     * MVC ELEMENT
     * 
     * Generates menu from items stored under node specified by provided pid
     * 
     * Following params (Controller::$params) are used by this method:
     *      - all options of Html::menu(). Options 'activeItem', 'homeSlug', 'depth', 
     *          'lang' have defined default values by this method.
     *      - 'pid' (string) Defaults to 'sidemenu'.
     *      - 'fields' (array|string) Additional field(s) to be retrieved for menu items.
     *      - 'view' (string) Defaults to 'WebContents/menu'. Keys 'items', 'options', 
     *          'WebContent' (model instance) are set in view params
     *      - 'class' (int) Manu wrapping block CSS class. Defaults to NULL.
     *      - 'title' (int) Menu title. Used mainly for side menus. Defaults to NULL.
     *      - 'openable' (bool) If TRUE then it is possible to open menu items by clicking
     *          (+) / (-) icon on the left of label. This is used mainly for side menus. 
     *          Defaults to TRUE.
     *      - 'prependItems' (array) Array of items to be prepended to items which are
     *          under specified node. Array must have an "tree structure", see Model::findTree().
     *          Top-level sub-array (the first one) is merged with toplevel sub-array 
     *          of retieved items. ATTENTION: Be careful about array keys to not come 
     *          into conflict (duplicities) with keys in retrieved items array. To avoid
     *          creation of invalid SmartAdminLauncher links use alphabetic keys as these
     *          are ignored by SmartAdminLauncher in Html:menu().
     *      - 'appendItems' (array) Array of items to be appended to items which are
     *          under specified node. Array must have an "tree structure", see Model::findTree().
     *          Top-level sub-array (the first one) is merged with toplevel sub-array 
     *          of retieved items. ATTENTION: Be careful about array keys to not come 
     *          into conflict (duplicities) with keys in retrieved items array. To avoid
     *          creation of invalid SmartAdminLauncher links use alphabetic keys as these
     *          are ignored by SmartAdminLauncher in Html:menu().
     * 
     * @return string
     */
    public function menu() {
        $this->displayOriginComment = true;
        $defaults = array(
            'pid' => 'sidemenu',
            'fields' => array(),
            'view' => 'WebContents/menu',
            'class' => null,
            'title' => null,
            'openable' => true,
            'prependItems' => array(),
            'appendItems' => array(),
            
            // Html::menu() options
            'activeItem' => SLUG,
            'homeSlug' => HOME_SLUG,
            'depth' => 1,
            'lang' => URL_LANG,
            'labelWrappers' => array(),
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        // openable needs left label wrapper
        if ($options['openable']) {
            $options['labelWrappers'] = array_unique(
                array_merge((array)$options['labelWrappers'], array('l'))
            );
         }
        $Content = $this->loadModel('WebContent', true);
        $fields = array(
            'WebContent.name AS label',
            'WebContent.locator AS locator',
            'WebContent.locator IS NULL AS passive',
        );
        if (!empty($options['fields'])) {
            $fields = array_unique(
                array_merge($fields, (array)$options['fields'])
            );
        }
        $items = $Content->findTree(
            array(
                'conditions' => array(
                    'WebContent.pid' => $options['pid'],
                    'WebContent.lang' => App::$lang,
                ),
            ),
            array(
                'conditions' => array(
                    'WebContent.active' => true
                ),
                'fields' => $fields,
                'depth' => $options['depth'],
            )
        );     
        if (!empty($options['prependItems'])) {
            $items = Arr::mergeTree($options['prependItems'], $items);
        }
        if (!empty($options['appendItems'])) {
            $items = Arr::mergeTree($items, $options['appendItems']);
        }
        // add smart admin launcher to Html::menu() options
        App::loadLib('App', 'SmartAdminLauncher');
        $options['SmartAdminLauncher'] = new SmartAdminLauncher(
            App::getUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => 'admin_edit',
            )),
            array(
                'triggerTitle' => __a(__FILE__, 'Edit page'),
            )
        );
        return $this->loadView($options['view'], array(
            'items' => $items,
            'options' => $options,
            'WebContent' => $Content,
        ));
    }
    
    /**
     * MVC ELEMENT & SNIPPET
     * 
     * Generates submenu of actual content or if used as snippet then following
     * params can be used too:
     * 
     * @param $this->params['source'] (string) Id or locator of source to get submenu for.
     * @param $this->params['_content']['id'] (int) Content id to get submenu for.
     * 
     * Params are considered in order as the are listed above.
     * 
     * @return string
     */
    public function submenu() {
        $this->displayOriginComment = true;
        $parentContent = App::getPropertyContent();
        // @todo - remove 'source-locator' and 'source-id' (replaced by 'source')
        if (!empty($this->params['source-locator'])) {
            $root = array(
                'conditions' => array(
                    'WebContent.locator' => $this->params['source-locator'],
                    'WebContent.lang' => App::$lang,
                )
            );
        }
        elseif (!empty($this->params['source-id'])) {
            $root = $this->params['source-id'];
        }
        elseif (!empty($this->params['source'])) {
            if (Validate::intNumber($this->params['source'])) {
                $root = $this->params['source'];
            }
            else {
                $root = array(
                    'conditions' => array(
                        'WebContent.locator' => $this->params['source'],
                        'WebContent.lang' => App::$lang,
                    )
                );
            }
        }
        elseif (!empty($this->params['_content']['id'])) {
            $root = $this->params['_content']['id'];
            
        }
        elseif (!empty($parentContent['id'])) {
            $root = $parentContent['id'];
        }
        else {
            return '';
        }
        $Content = $this->loadModel('WebContent', true);
        $items = $Content->findTree(
            $root,
            array(
                'conditions' => array(
                    'WebContent.active = 1',
                ),
                'fields' => array(
                    'WebContent.name AS label',
                    'WebContent.locator AS locator',
                    'WebContent.locator IS NULL AS passive',
                ),
                'depth' => 1,
            ),
            $parentId
        );
        if (empty($items[$parentId])) {
            return '';
        }
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContents/submenu', array(
            'items' => $items,
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit page'),
                )
            ),
        ));        
    }
    
    /**
     * MVC ELEMENT
     * 
     * Generates social networks menu 
     * 
     * @return string
     */
    public function socialNetworksMenu() {
        $this->displayOriginComment = true;
        $defaults = array(
            'floating' => false,
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        $Content = $this->loadModel('WebContent', true);
        $items = $Content->findInTree(
            array(
                'conditions' => array(
                    'pid' => 'socialnetworksmenu',
                    'lang' => App::$lang,
                ),
            ),
            array(
                'conditions' => array(
                    'WebContent.active' => true
                ),
                'fields' => array(
                    'WebContent.id',
                    'WebContent.name',
                    'WebContent.locator',
                    'WebContent.menu_icon',
                ),
                'order' => array('WebContent.sort ASC'),
                'depth' => 1,
            )
        );
        // set image url paths
        foreach ($items as &$item) {
            $item['menu_icon'] = $Content->getFileFieldUrlPath('menu_icon', array('file' => $item['menu_icon']));
            $item['color'] = null;
            $item['icon'] = null;
//            if ($options['floating']) {
                if (preg_match('/[\/\.]facebook\.com/', $item['locator'])) {
                    $item['color'] = '#3b5998';
                    $item['icon'] = '<i class="fa fa-facebook-square"></i>';
                }
                elseif (preg_match('/\/plus\.google\.com/', $item['locator'])) {
                    $item['color'] = '#DD1739';
                    $item['icon'] = '<i class="fa fa-google-plus"></i>';
                }
                elseif (preg_match('/[\/\.]instagram\.com/', $item['locator'])) {
                    $item['color'] = '#8E5A4E';
                    $item['icon'] = '<i class="fa fa-instagram"></i>';
                }
                elseif (preg_match('/[\/\.]linkedin\.com/', $item['locator'])) {
                    $item['color'] = '#1469A9';
                    $item['icon'] = '<i class="fa fa-linkedin"></i>';
                }
                elseif (preg_match('/[\/\.]pinterest\.com/', $item['locator'])) {
                    $item['color'] = '#BB0023';
                    //$item['icon'] = '<i class="fa fa-pinterest-p"></i>';
                    $item['icon'] = '<i class="fa fa-pinterest-square"></i>';
                }
                elseif (preg_match('/[\/\.]quora\.com/', $item['locator'])) {
                    $item['color'] = '#B6002A';
                    $item['icon'] = '<i class="fa fa-quora"></i>';
                }
                elseif (preg_match('/[\/\.]skype\.com/', $item['locator'])) {
                    $item['color'] = '#08A6E9';
                    $item['icon'] = '<i class="fa fa-skype"></i>';
                }
                elseif (preg_match('/[\/\.]twitter\.com/', $item['locator'])) {
                    $item['color'] = '#2793EA';
                    $item['icon'] = '<i class="fa fa-twitter-square"></i>';
                }
                elseif (preg_match('/[\/\.]youtube\.com/', $item['locator'])) {
                    //$item['color'] = '#cc181e';
                    $item['color'] = '#FF001F';
                    //$item['icon'] = '<i class="fa fa-youtube"></i>';
                    $item['icon'] = '<i class="fa fa-youtube-square"></i>';
                }
//            }
        }
        unset($item);
        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContents/socialNetworksMenu', array(
            'items' => $items,
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit social network page'),
                )
            ),
            'floating' => $options['floating'],
        ));        
    }    
    
    /**
     * MVC ELEMENT
     * 
     * Generates certificates menu 
     * 
     * @return string
     */
    public function certificatesMenu() {
        $this->displayOriginComment = true;
        $Content = $this->loadModel('WebContent', true);
        $root = $Content->findFirst(array(
            'conditions' => array(
                'pid' => 'certificatesmenu',
                'lang' => App::$lang,
            ),
            'fields' => array('WebContent.id', 'WebContent.name')
        ));
        if (!$root) {
            return '';
        }
        $items = $Content->findInTree(
            $root['id'],
            array(
                'conditions' => array(
                    'WebContent.active' => true
                ),
                'fields' => array(
                    'WebContent.name',
                    'WebContent.locator',
                    'WebContent.menu_icon',
                ),
                'order' => array('WebContent.sort ASC'),
                'limit' => 4,
                'depth' => 1,
            )
        );
        // set image url paths
        foreach ($items as &$item) {
            $item['menu_icon'] = $Content->getFileFieldUrlPath('menu_icon', array('file' => $item['menu_icon']));
        }
        unset($item);
        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContents/certificatesMenu', array(
            'title' => $root['name'],
            'items' => $items,
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit certificate page'),
                )
            ),
        ));        
    }    
    
    /**
     * MVC ELEMENT
     * 
     * Generates breadcrumbs html
     * 
     * @return string
     */
    public function breadcrumbs($arg = null) {
        $this->displayOriginComment = true;
        $content = App::getPropertyContent();
        if (
            empty($content['id'])
            && !empty($this->params['_content'])
        ) {
            $content = $this->params['_content'];
        }
        // do not generate if no content is loaded or on home page
        if (
            empty($content['id'])
            || SLUG === HOME_SLUG
        ) {
            return '';
        }
        
        // get breadcumbs contents ids
        $Content = App::loadModel('App', 'WebContent', true);
        $ids = array();
        // - if there is arg then we must be in some specific section (Blog, News, ...)
        $onView = false;
        if (!empty($arg)) {
            $parentPid = null;
            if (SLUG === App::getContentLocatorByPid('App.WebContentsArticles.view')) {
                $parentPid = 'App.WebContentsArticles.index';
                $onView = true;
            }
            elseif (SLUG === App::getContentLocatorByPid('App.WebContentsNews.view')) {
                $parentPid = 'App.WebContentsNews.index';
                $onView = true;
            }
            if (!empty($parentPid)) {
                $ids = $Content->findList(array(
                    'fields' => array('id'),
                    'conditions' => array(
                        'lang' => App::$lang,
                        array(
                            'pid' => $parentPid,
//// do not add article / news title to breadcrumbs                            
//                            'OR',
//                            'locator' => $arg,
                        )
                    ),
                ));
                $order = 'IF (WebContent.pid = "' . $parentPid . '", 0, 1) ASC';
                $literals = array('order' => true);
            }
        }
        // - if we have not succeeded above then use for breadcrumbs just parent contents 
        // of actual content
        if (empty($ids)) {
            $ids = array_slice($content['parent_ids'], 2);
            $ids[] = $content['id'];
            $order = array('sort ASC');
            $literals = null;
        }
        
        // get breadcrumbs contents data
        $items = $Content->find(
            array(
                'conditions' => array(
                    'WebContent.id' =>  $ids,
                ),
                'fields' => array(
                    'WebContent.name AS label',
                    'WebContent.locator',
                ),
                'order' => $order,
                'literals' => $literals,
            )
        );
        
        // set url to each item
        foreach ($items as &$item) {
            $item['url'] = null;
            if (!empty($item['locator'])) {
                $item['url'] = App::getUrl($item['locator']);
            }
        }
        unset($item);
        
        // unset 'url' of last item to make it inactive (but only if not in article / news detail)
        if (!$onView) {
            $lastIndex = count($items) - 1;
            unset($items[$lastIndex]['url']);
        }
        
        return $this->loadView('WebContents/breadcrumbs', array(
            'items' => $items,
            'content' => $content,
        ));
    }
        
    /**
     * MVC SNIPPET
     * 
     * Generates menu of the content text it is placed in as snippet.
     * ATTENTION: This snippet changes the source text it is placed in.
     * 
     * @param $this->params['_text'] (string) Text the snippet will generate a menu for.
     *       The $this->params['_text'] is changed.
     * @param $this->params['affix'] If TRUE then menu is fixed and menu items are activated 
     *      according to page scroll to corresponding headings. Defaults to TRUE.
     * 
     * @return string
     */
    public function textMenu() {
        $defaults = array(
            'affix' => true,
        );
        $this->displayOriginComment = true;
        // you can provide any options of Html::textMenu()
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        unset($options['_content']);
        unset($options['_text']);
        if (empty($this->params['_text'])) {
            return '';
        }
        return $this->loadView('WebContents/textMenu', array(
            // this must be done here (not in view) because the $this->params['_text'] itself is changed
            'menu' => Html::textMenu($this->params['_text'], array_merge($options, array(
                'wrapper' => 'text-menu-wrapper'
            ))),
            'affix' => $options['affix']
        ));
    }
    
    /**
     * MVC SNIPPET
     * 
     * Generates child contents accordion
     * 
     * @param $this->params['source'] (int|string) Id or locator of source to get childs from.
     * @param $this->params['_content']['id'] (int) Content id to get childs from.
     * @param $this->params['open-first'] (bool) If set to TRUE (1) then the first accordion item 
     *      is opened.
     * @param $this->params['heading-tag'] (string) Optional. Tag name used to create heading in accordion.
     *          If empty value then no eadings are generated. Defaults to 'h3'.
     * @param $this->params['column-navigation'] (bool) Optional. If TRUE (1) then accordion 
     *          navigation items are not floated but each one is on new line.
     * 
     * @return string
     */
    public function childsAccordion() {
        $this->displayOriginComment = true;
        // @todo - remove 'source-locator' and 'source-id' (replaced by 'source')
        if (!empty($this->params['source-locator'])) {
            $root = array(
                'conditions' => array(
                    'WebContent.locator' => $this->params['source-locator'],
                    'WebContent.lang' => App::$lang,
                )
            );
        }
        elseif (!empty($this->params['source-id'])) {
            $root = $this->params['source-id'];
        }
        elseif (!empty($this->params['source-pid'])) {
            $root = $this->params['source-pid'];
        }
        elseif (!empty($this->params['source'])) {
            if (Validate::intNumber($this->params['source'])) {
                $root = $this->params['source'];
            }
            else {
                $root = array(
                    'conditions' => array(
                        'WebContent.locator' => $this->params['source'],
                        'WebContent.lang' => App::$lang,
                    )
                );
            }
        }
        elseif (!empty($this->params['_content']['id'])) {
            $root = $this->params['_content']['id'];
        }
        else {
            return '';
        }
        $defaults = array(
            'heading-tag' => 'h3',
            'open-first' => false,
            'column-navigation' => false,
        );
        $options = array_merge($defaults, $this->params);
        $Content = App::loadModel('App', 'WebContent', true);
        $items = $Content->findInTree(
            $root,
            array(
                'conditions' => array(
                    'WebContent.active' => true,
                ),
                'fields' => array(
                    'WebContent.id',
                    'WebContent.locator',
                    'WebContent.name',
                    'WebContent.text',
                ),
                'order' => array('WebContent.sort ASC'),
                'depth' => 1,
            )
        );
        // add content blocks
        $BlockInstance = App::loadModel('App', 'ContentBlockInstance', true);
        foreach ($items as $i => &$item) {
            $item['text'] .= $BlockInstance->loadOwnerInstancesViews('App.WebContent', $item['id'], array(
                'ownerRecord' => $item
            ));
            if (empty($item['text'])) {
                unset($items[$i]);
            }
            //HARDCODED: remove content blocks animations
            $matches = array();
            if (preg_match_all('/class="[^"]*(?:wow|animate)[^"]*"/', $item['text'], $matches)) {
                $replacements = array();
                foreach($matches[0] as $match) {
                    if (!isset($replacements[$match])) {
                        $replacements[$match] = preg_replace('/wow|animate/', '', $match);
                    }
                }
                $item['text'] = str_replace(array_keys($replacements), $replacements, $item['text']);
            }
        }
        unset($item);
        if (empty($items)) {
            return '';
        }
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContents/childsAccordion', array(
            'items' => $items,
            'heading-tag' => $options['heading-tag'],
            'open-first' => (bool)$options['open-first'],
            'column-navigation' => (bool)$options['column-navigation'],
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit page'),
                )
            ),
        ));
    }
    
    /**
     * MVC SNIPPET
     * 
     * Generates content accordion
     * 
     * @param $this->params['source'] (int|string) Id or locator of source to get content from.
     * @param $this->params['open'] (bool) If set to TRUE (1) then the accordion is opened.
     * @param $this->params['heading-tag'] (string) Optional. Tag name used to create heading in accordion.
     *          Defaults to 'h2'.
     * 
     * @return string
     */
    public function contentAccordion() {
        $this->displayOriginComment = true;
        if (!empty($this->params['source'])) {
            if (Validate::intNumber($this->params['source'])) {
                $conditions = array(
                    'id' => $this->params['source'],
                    'active' => true,
                );
            }
            else {
                $conditions = array(
                    'locator' => $this->params['source'],
                    'lang' => App::$lang,
                    'active' => true,
                );
            }
        }
        else {
            return '';
        }
        $defaults = array(
            'open' => false,
            'heading-tag' => 'h2',
        );
        $options = array_merge($defaults, $this->params);
        $item = App::getContent(array('conditions' => $conditions, 'obfuscate' => false));
        if (empty($item)) {
            return '';
        }
        return $this->loadView('WebContents/contentAccordion', array(
            'item' => $item,
            'heading-tag' => $options['heading-tag'],
            'open' => (bool)$options['open'],
        ));
    }
    
    /**
     * MVC SNIPPET
     * 
     * Generates photogallery
     * 
     * @param $this->params['source'] (int|string) Id or locator of source content to get images from.
     * @param $this->params['_content']['id'] (int) Content id to get images from.
     * @param $this->params['type'] (string) Optional. Type of photogallery. Possible 
     *          values are 'squareThumb', 'smallThumbs', 'text'. Defaults to NULL, means default 
     *          photogallery is generated.
     * 
     * @return string
     */
    public function photogallery() {
        $this->displayOriginComment = true;
        $this->loadModel('WebContent');
        $Content = new WebContent();
        $defaults = array(
            'source' => null,
            '_content' => null,
            'type' => null,
            
            // this is here just to remove it from options when separating options for Html::photogallery()
            '_text' => null,
            '_snippet' => null,
            '_snippet_generic' => null,
            '_snippet_name' => null,
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        // get options for Html::photogallery() - remove expected options of this method
        // because Html::photogallery() pass all unknown options to js library instance.
        $htmlOptions = array_diff_key($options, $defaults);
        // check for special case of text gallery
        if ($options['type'] === 'text') {
            return App::loadView('App', 'WebContents/textPhotogallery', $htmlOptions);
        }
        // continue in processing for other types of galleries
        if (!empty($options['source'])) {
            if (Validate::intNumber($options['source'])) {
                $contentId = $options['source'];
            }
            else {
                $contentId = $Content->findField('id', array(
                    'conditions' => array(
                        'locator' => $options['source'],
                        'lang' => App::$lang,
                    ),
                ));                
            }
        }
        elseif (!empty($options['_content']['id'])) {
            $contentId = $options['_content']['id'];
        }
        else {
            return '';
        }
        // set thumb variant and view for Html::photogallery() according to type 
        // and thumb dimesion
        $thumbVariant = 'squareThumb';
        $htmlOptions['imagesView'] = array(
            'module' => $this->module, 
            'name' => 'WebContents/photogallery'
        );
        if ($options['type'] === 'smallThumbs') {
            $thumbVariant = 'squareThumbSmall';
            $htmlOptions['class'] = 'photogallery small-thumbs';
        }
//        elseif ($options['type'] === 'image') {
//            $thumbVariant = 'squareThumb';
//            $htmlOptions['view'] = 'WebContents/imagePhotogallery';
//            $htmlOptions['class'] = 'image-photogallery';
//        }
        // get images (
        $this->loadModel('WebImage');
        $Image = new WebImage();
        $images = $Image->find(array(
            'fields' => array(
                'file',
                'alternative_file',
                'name',
            ),
            'conditions' => array(
                'WebImage.run_web_contents_id' => $contentId,
            ),
            'order' => array('WebImage.sort ASC')
        ));
        // set url paths, thumbs and apply alternative thumbs
        foreach ($images as &$image) {
            $fileVariants = $Image->getFileFieldUrlPaths('file', array('file' => $image['file']));
            $image['file'] = $fileVariants['original'];
            if (!empty($image['alternative_file'])) {
                $image['thumb'] = $Image->getFileFieldUrlPath('alternative_file', array('file' => $image['alternative_file'], 'variant' =>  $thumbVariant));
            }
            else {
                $image['thumb'] = $fileVariants[$thumbVariant];
            }
        }
        unset($image); // unset reference
        App::loadLib('App', 'SmartAdminLauncher');
        $Launcher = new SmartAdminLauncher(
            App::getUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => 'admin_edit',
            )),
            array(
                'triggerTitle' => __a(__FILE__, 'Edit photogallery page'),
            )
        );
        $htmlOptions['smartAdminLauncherRecordAttribute'] = $Launcher->markRecord($contentId);        
        return Html::photogallery($images, $htmlOptions);
    }
    
    /**
     * MVC ELEMENT
     * 
     * @return string
     */
    public function backgroundImageStyle() {
        $content = App::getPropertyContent();
        // if no content found then do nothing
        if (empty($content['id'])) {
            return '';
        }
        // retrieve background image
        $Content = $this->loadModel('WebContent', true);
        $image = $Content->findFirstUpInTree(
            $content,
            array(
                'conditions' => array(
                    'WebContent.active = 1',
                    'WebContent.background_image !=' => null,
                    'WebContent.background_image !=' => '',
                ),
                'fields' => array(
                    'WebContent.background_image',
                ),
                'order' => array(
                    'WebContent.sort ASC'
                )
            )
        ); 
        // if no background_image found then do nothing
        if (empty($image['background_image'])) {
            return '';
        }
        $image = $Content->getFileFieldUrlPath('background_image', array('file' => $image['background_image']));
        return ' style="background: url(' . $image. ') top center repeat-x "';
    }    
}

