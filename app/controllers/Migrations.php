<?php
/**
 * User this controller for actions to migrate old project DB into new one
 * Functionality here should be updated per project basis.
 */
class Migrations extends Controller {
    
    public function __construct() {
        parent::__construct();
        $this->loadModel('Migration');
        Migration::$debug = ON_LOCALHOST ? 1000 : false;
    }
    
    /**
     * .../mvc/App/Migrations/admin_index
     */
    public function admin_index() {
        return $this->loadView('Migrations/index');
    }
        
    public function admin_migrateAppUsers($action = null, $limit = null, $offset = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateAppUsers(array(
                'action' => $action,
                'limit' => $limit,
                'offset' => $offset,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'App users',
            'action' => $this->action,
            'process' => $process
        ));
    }
    
    public function admin_migrateEshopProductCategories($action = null, $limit = null, $offset = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopProductCategories(array(
                'action' => $action,
                'limit' => $limit,
                'offset' => $offset,
            ));
            $process = array_merge($process, (array)$result);
            $result = $Migration->migrateEshopProductCategoryProducts(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop product categories and categories products',
            'action' => $this->action,
            'process' => $process
        ));
    }

    public function admin_migrateEshopAuthors($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopAuthors(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
            $result = $Migration->migrateEshopProductAuthors(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop authors and product authors',
            'action' => $this->action,
            'process' => $process
        ));
    }

    public function admin_migrateEshopManufacturers($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopManufacturers(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop manufacturers',
            'action' => $this->action,
            'process' => $process
        ));
    }

    public function admin_migrateEshopManufacturerRanges($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopManufacturerRanges(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop manufacturer ranges',
            'action' => $this->action,
            'process' => $process
        ));
    }

    public function admin_migratePaymentMethods($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migratePaymentMethods(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Payment methods',
            'action' => $this->action,
            'process' => $process
        ));
    }

    public function admin_migrateEshopShipmentMethods($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopShipmentMethods(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
            $result = $Migration->migrateEshopShipmentMethodPaymentMethods(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop shipment methods and shipment method payment methods',
            'action' => $this->action,
            'process' => $process
        ));
    }

    public function admin_migrateEshopProductGroups($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopProductGroups(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop product groups',
            'action' => $this->action,
            'process' => $process
        ));
    }

    public function admin_migrateEshopOrders($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopOrders(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
            $result = $Migration->migrateEshopOrderProducts(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop orders and order products',
            'action' => $this->action,
            'process' => $process
        ));
    }
    
    public function admin_migrateEshopProductTypes($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopProductTypes(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop product types',
            'action' => $this->action,
            'process' => $process
        ));
    }
    
    public function admin_migrateEshopProductComments($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopProductComments(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop product comments',
            'action' => $this->action,
            'process' => $process
        ));
    }
    
    public function admin_migrateEshopProducts($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopProducts(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop products',
            'action' => $this->action,
            'process' => $process
        ));
    }
    
    public function admin_migrateEshopProductImages($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopProductImages(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop product images',
            'action' => $this->action,
            'process' => $process
        ));
    }
    
    public function admin_moveEshopProductImages() {
        $Migration = new Migration();
        $result = $Migration->moveEshopProductImages();
        return '<pre>' . print_r($result, true) . '</pre>';
    }
    
    public function admin_migrateEshopWishlists($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopWhishlists(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
            $result = $Migration->migrateEshopWhishlistProducts(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop wishlists and wishlist products',
            'action' => $this->action,
            'process' => $process
        ));
    }
    
    public function admin_migrateEshopVouchers($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateEshopVouchers(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Eshop vouchers',
            'action' => $this->action,
            'process' => $process
        ));
    }
    
    public function admin_migrateMailerContacts($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateMailerContacts(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Mailer contacts',
            'action' => $this->action,
            'process' => $process
        ));
    }
    
    public function admin_migrateSettings($action = null) {
        $Migration = new Migration();
        $process = array();
        if (!empty($action)) {
            $result = $Migration->migrateSettings(array(
                'action' => $action,
            ));
            $process = array_merge($process, (array)$result);
        }
        return $this->loadView('Migrations/generic', array(
            'title' => 'Settings',
            'action' => $this->action,
            'process' => $process
        ));
    }
}

