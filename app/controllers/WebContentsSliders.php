<?php
App::loadController('App', 'WebContents');
class WebContentsSliders extends WebContents {
    // It is the class name what is here important as WebContents::$sectionPid is
    // set according to it, see WebContents::__construct()
    //
    // All this serves to correct matching web content records and sections in Admin
    
    /**
     * MVC ELEMENT
     * 
     * Generates slider
     * 
     * @param int $this->params['sliderId']|$this->params['slider-id'] Id of slider content. 
     *      Defaults to actual content['slider_id']
     * @param string $this->params['type'] Type slider to be used. Possible values are 'bxslider', 'wowslider' and 'owlcarousel'.
     *      Defaults to 'bxslider'.
     * @param string $this->params['class'] Additional css class. Defaults to NULL.
     * @param $this->params The rest of params is passed as options to Html slider method
     *      specified by $this->params['type'].
     * 
     * @return string
     */
    public function slider() {
        $this->displayOriginComment = true;
        $defaults = array(
            'sliderId' => null,
            'type' => 'bx',
            'class' => null,
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        // keep options for Html::xyzSlider() aside of other options  
        $sliderId = $options['sliderId'];
        unset($options['sliderId']);
        $type = strtolower($options['type']);
        unset($options['type']);
        $class = $options['class'];
        unset($options['class']);
        // normalize id
        if (empty($sliderId)) {
            if (!empty($this->params['_content']['slider_id'])) {
                $sliderId = $this->params['_content']['slider_id'];
            }
            else {                
                $content = App::getPropertyContent();
                $sliderId = Sanitize::value($content['slider_id']);
            }
        }
        // if no slider found then do nothing
        if (empty($sliderId)) {
            return '';
        }
        $Content = App::loadModel('App', 'WebContent', true);
        $slides = $Content->findInTree(
            $sliderId,
            array(
                'conditions' => array(
                    'WebContent.active' => true,
                    'WebContent.image_name IS NOT NULL',
                ),
                'fields' => array(
                    'WebContent.id',
                    'WebContent.image_name AS image',
                    // rename locator to url and so avoid default adition of link
                    // in Html::owlSlider(). The link is created as a part of slideTemplate
                    // (see the view)
                    'WebContent.locator AS url', 
                    'WebContent.resume AS title',
                    'WebContent.header_text AS text',
                    'WebContent.aux_01 AS button_label',
                    'WebContent.aux_02 AS veiled_image',
                ),
                'order' => array('WebContent.sort ASC'),
                'depth' => 1,
            )
        );
        // if no slides found then do nothing
        if (empty($slides)) {
            return '';
        }
        // load snippets and set image full paths
        foreach ($slides as &$slide) {
            if (!empty($slide['image'])) {
                $slide['image'] = $Content->getFileFieldUrlPath('image_name', array('file' => $slide['image']));
            }
            $slide['text'] = App::loadTextSnippets($slide['text'], array('params' => array('_content' => $slide)));
        }
        unset($slide);
        
        App::loadLib('App', 'SmartAdminLauncher');
        $options['SmartAdminLauncher'] = new SmartAdminLauncher(
            App::getUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => 'admin_edit',
            )),
            array(
                'triggerTitle' => __a(__FILE__, 'Edit slide'),
            )
        );
        return $this->loadView('WebContentsSliders/slider', array(
            'slides' => $slides,
            'type' => $type,
            'class' => $class,
            'options' => $options,
            'sliderId' => $sliderId,
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit slider'),
                )
            ),
        ));
    }
}

