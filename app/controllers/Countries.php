<?php

class Countries extends SmartController {
    protected $model = 'Country';

    public function admin_index() {
        $Country = $this->loadModel('Country', true);
        $countries = $Country->find(array(
            'fields' => '*',
            'order' => 'id',
            'paginate' => true,
        ));
        
        App::setSeoTitle(__a(__FILE__, 'Countries'));
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'List of countries'),
            'records' => $countries,
            'Paginator' => $Country->Paginator,
            'paginatorOptions' => array(
                //'allowMultiSort' => false,
            ),
            'columns' => array(),
            'actions' => array(                
                'add' => array(
                    'url' => '/mvc/App/Countries/admin_add',
                ),
            ),
            'recordActions' => array(
                'edit' => array(
                    'url' => '/mvc/App/Countries/admin_edit',
                ),
//                'view' => array(
//                    'url' => '/mvc/App/Countries/admin_view',
//                    'title' => __a(__FILE__, 'View'),
//                ),
//                'copy' => array(
//                    'url' => '/mvc/App/Countries/admin_copy',
//                ),
//                'delete' => array(
//                    'url' => '/mvc/App/Countries/admin_delete',
//                    'title' => __a(__FILE__, 'Delete'),
//                    'confirmMessage' => __a(__FILE__, 'Please, comfirm removal of :name: country'),
//                ),
            )
        ));
    }    
    
    public function admin_add() {
        
        $Country = $this->loadModel('Country', true);
        
        if ($this->data) {
            if ($Country->save($this->data)) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully created'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                    'args' => array($Country->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        
        App::setSeoTitle(__a(__FILE__, 'Add new country'));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Add new country'),
            'data' => $this->data,
            'Model' => $Country,
            'lang' => DEFAULT_LANG,
            'columns' => 4,
            'actions' => array(
                'lang' => array(
                    'options' => array_combine(array(DEFAULT_LANG), array(DEFAULT_LANG)),
                )
            ),
            'fields' => array(
                array('row'),
                    array(
                        'field' => 'name', 
//                        'type' => 'editor', 
                        'label' => __a(__FILE__, 'Name')
                    ),
                array('/row'),
                array('row'),
                    array('field' => 'iso_code_2', 'label' => __a(__FILE__, 'Iso Code 2')),
                    array('field' => 'iso_code_3', 'label' => __a(__FILE__, 'Iso Code 3')),
                array('/row'),
            )
        ));
    }    
    
    public function admin_edit($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        
        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        $Country = $this->loadModel('Country', true);
        
        if ($this->data) {
            if ($Country->save($this->data, array('lang' => $lang))) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => $this->action,
                    'args' => array($id),
                    'source' => App::$requestSource,
                    'inherit' => array('get' => array('lang'))
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            
        }
        else {
            $this->data = $Country->findFirstBy('id', $id, array('lang' => $lang));
            // get file fields real path
            if (!empty($this->data['flag'])) {
                $this->data['flag'] = $Country->getFileFieldUrlPath('flag', array('file' => $this->data['flag']));
            }
        }
        
        // add name to title and tabTitle
        if (!empty($this->data['name'])) {
            $name = $this->data['name'];
        }
        // - name should not be empty, if so then it is error so use actual name
        else {
            $name = $Country->findFieldBy('name', 'id', $this->data['id']);
        }
        App::setSeoTitle(__a(__FILE__, '&quot;%s&quot;', $name));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Edit country &quot;%s&quot;', $name),
            'data' => $this->data,
            'Model' => $Country,
            'lang' => $lang,
//            'nonTranslatedProcessing' => 'ignore',
            'columns' => 4,
            'actions' => array(
                'lang' => true
            ),
            'fields' => array(
                array('field' => 'id', 'type' => 'hidden'),
                array('row', 'columns' => 2),
                    array('col'),
                        array('row'),
                            array(
                                'field' => 'name', 
                                'label' => __a(__FILE__, 'Name'), 
                            ),
                            array(
                                'field' => 'flag', 
                                'label' => __a(__FILE__, 'Flag'), 
                            ),
                        array('/row'),
                        array('row'),
                            array(
                                'field' => 'iso_code_2', 
                                'label' => __a(__FILE__, 'Iso Code 2')
                            ),
                            array(
                                'field' => 'iso_code_3', 
                                'label' => __a(__FILE__, 'Iso Code 3')
                            ),
                        array('/row'),
                    array('/col'),
                    array('col'),
                        array('row'),
                            array(
                                'field' => 'flag', 
                                'type' => 'image',
                                'deleteImage' => '/mvc/App/Countries/admin_deleteFile/flag/' . $id,
                            ),
                        array('/row'),
                    array('/col'),
                array('/row'),
            )
        ));
    }

    public function find($options = null) {
        $this->findOptions['allowFields'] = array(
            'id',
            'name',
            'Country.id',
            'Country.name',
        );
//        $this->findOptions['allowFields'] = array();
//        $this->findOptions['allowJoins'] = true;
//        $this->findOptions['allowLiterals'] = true;
        return parent::find($options);
    }
    
    public function findFirst($options = null) {
        $this->findOptions['allowFields'] = array(
            'id',
            'name',
            'Country.id',
            'Country.name',
        );
        return parent::findFirst($options);
    }
    
    public function findList($options = null) {
        $this->findOptions['allowFields'] = array(
            'id',
            'name',
            'Country.id',
            'Country.name',
        );
        return parent::findList($options);
    }
}
