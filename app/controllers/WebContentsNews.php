<?php
App::loadController('App', 'WebContents');
class WebContentsNews extends WebContents {
    // It is the class name what is here important as WebContents::$sectionPid is
    // set according to it, see WebContents::__construct()
    //
    // All this serves to correct matching web content records and sections in Admin
    
    /**
     * MVC ELEMENT
     * 
     * Generates news review pan 
     * 
     * @return string
     */
    public function review() {
        $this->displayOriginComment = true;
        $defaults = array(
            'title' => null,
            'titleTag' => 'h1',
            'limit' => 2,
            'slider' => false,
        );
        $options = array_merge(
            $defaults, 
            Arr::camelizeKeys($this->params, array(
                'separator' => array('-', '_'),
                'depth' => 1,
            )
        ));
        $Content = $this->loadModel('WebContent', true);
        $root = $Content->findFirst(array(
            'conditions' => array(
                'pid' => 'news',
                'lang' => App::$lang,
            ),
            'fields' => array('WebContent.id', 'WebContent.name')
        ));
        if (!$root) {
            return '';
        }
        $items = $Content->find(array(
            'conditions' => array(
                'WebContent.parent_id' => $root['id'],
                'WebContent.active' => true
            ),
            'fields' => array(
                'WebContent.id',
                'WebContent.name',
                'WebContent.locator',
                'WebContent.resume',
                'WebContent.date',
                'WebContent.menu_icon',
            ),
            'order' => array('WebContent.sort DESC'),
            'limit' => $options['limit'],
        ));
        $count = $Content->findCount(array(
            'conditions' => array(
                'WebContent.parent_id' => $root['id'],
                'WebContent.active' => true
            ),
        ));
        // set image url paths
        foreach ($items as &$item) {
            if (!empty($item['menu_icon'])) {
                $item['menu_icon'] = $Content->getFileFieldUrlPath('menu_icon', array(
                    'file' => $item['menu_icon'],
                    'variant' => 'article'
                ));
            }
        }
        unset($item);
        App::loadLib('App', 'SmartAdminLauncher');
        $view = $options['slider'] ? 'WebContentsNews/reviewSlider' : 'WebContentsNews/review';
        return $this->loadView($view, array(
            'title' => $options['title'] === null ? $root['name'] : $options['title'],
            'titleTag' => $options['titleTag'],
            'items' => $items,
            'count' => $count,
            'indexLocator' => App::getContentLocatorByPid('App.WebContentsNews.index'),
            'viewLocator' => App::getContentLocatorByPid('App.WebContentsNews.view'),
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit news page'),
                )
            ),
        ));        
    }  
    
    /**
     * MVC ELEMENT
     * 
     * Generates news list
     * 
     * @return string
     */
    public function index($slug = null) {
        $this->displayOriginComment = true;
        $Content = $this->loadModel('WebContent', true);
        $root = $Content->findFirst(array(
            'conditions' => array(
                'pid' => 'news',
                'lang' => App::$lang,
            ),
            'fields' => array('WebContent.id', 'WebContent.name')
        ));
        if (!$root) {
            return '';
        }
        $items = $Content->find(array(
            'conditions' => array(
                'WebContent.parent_id' => $root['id'],
                'WebContent.active' => true,
            ),
            'fields' => array(
                'WebContent.id',
                'WebContent.name',
                'WebContent.locator',
                'WebContent.resume',
                'WebContent.date',
                'WebContent.menu_icon',
            ),
            'order' => array('WebContent.sort DESC'),
            'paginate' => true,
        ));
        // load snippets, content blocks and set image full paths
        $BlockInstance = App::loadModel('App', 'ContentBlockInstance', true);
        foreach ($items as &$item) {
            if (!empty($item['menu_icon'])) {
                $item['menu_icon'] = $Content->getFileFieldUrlPath('menu_icon', array('file' => $item['menu_icon']));
            }
            $item['text'] .= $BlockInstance->loadOwnerInstancesViews('App.WebContent', $item['id'], array(
                'ownerRecord' => $item
            ));
        }
        unset($item);
        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContentsNews/index', array(
            'items' => $items,
            'slug' => $slug,
            'viewLocator' => App::getContentLocatorByPid('App.WebContentsNews.view'),
            'Paginator' => $Content->Paginator,
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit news page'),
                )
            ),
        ));        
    }
    
    public function view($slug = null) {
        $this->displayOriginComment = true;
        $Content = $this->loadModel('WebContent', true);

        $item = $Content->findFirst(array(
            'conditions' => array(
                'WebContent.locator' => $slug,
                'WebContent.active' => true,
                'WebContent.lang' => App::$lang,
            ),
            'fields' => array(
                'WebContent.id',
                'WebContent.name',
                'WebContent.locator',
                'WebContent.resume',
                'WebContent.text',
                'WebContent.date',
                'WebContent.image_name',
                'WebContent.seo_title',
                'WebContent.seo_description',
                'WebContent.seo_keywords',
                'WebContent.seo_index',
                'WebContent.seo_follow',
            ),
        ));
        if (empty($item)) {
            return App::loadScreen('_404');
        }
        if (!empty($item['image_name'])) {
            $item['image_name'] = $Content->getFileFieldUrlPath('image_name', array('file' => $item['image_name']));
        }
        $BlockInstance = App::loadModel('App', 'ContentBlockInstance', true);
        $item['text'] = $BlockInstance->loadOwnerInstancesViews('App.WebContent', $item['id'], array(
            'ownerRecord' => $item
        ));
        $item['text'] = App::loadTextSnippets($item['text'], array('params' => array('_content' => $item)));
        
        App::setSeoTitle($item['seo_title']);
        App::setSeoDescription($item['seo_description']);
        App::setSeoKeywords($item['seo_keywords']);
        App::setSeoIndex($item['seo_index']);
        App::setSeoFollow($item['seo_follow']);
        
        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('WebContentsNews/view', array(
            'item' => $item,
            'indexLocator' => App::getContentLocatorByPid('App.WebContentsNews.index'),
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Edit news page'),
                )                    
            ),
        ));        
    }
}
