<?php
class Districts extends Controller {
    
    /**
     * Return json encoded array of options for selectbox. 
     * 
     * Can be used with chained country > region > district selectboxes (see http://www.appelsiini.net/projects/chained)
     * 
     * @param string $regionId Region id
     * 
     * @return string Json encoded array of options for selectbox
     */
    public function getSelectOptionsByRegion($regionId = null) {
        App::setLayout(false);
        $options = array();
        if (!empty($this->data['UserProfile']['kraj'])) {
            $regionId = $this->data['UserProfile']['kraj'];
        }
        if (empty($regionId)){
            return json_encode($options);
        }
        $District = App::loadModel('App', 'District', true);
        $options = $District->getSelectOptionsByRegion($regionId);
        return json_encode($options);
    }
    
    
}