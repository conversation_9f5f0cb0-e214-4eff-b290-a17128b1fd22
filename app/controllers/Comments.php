<?php

class Comments extends Controller {
    
    public function index($foreignModel = null, $foreignId = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'view' => 'Comments/index',
            'allowRatings' => App::getSetting('App', 'Comment.allowRatings'),
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        // normalize view
        if (is_string($options['view'])) {
            $options['view'] = array('module' => 'App', 'name' => $options['view']);
        }
        
        $user = App::getUser();
        $Comment = $this->loadModel('Comment', true);
        if (empty($foreignId) || empty($foreignModel)) {
            throw new Exception(__e(__FILE__, 'Comments::index(): missing foreign model and/or id.'));
        }
        if (
            App::getSetting('App', 'Comment.onlyByRegisteredUsers')
            && empty($user)
        ) {
            return '';
        }
        
        $comments = $Comment->find(array(
            'fields' => '*',
            'conditions' => array(
                'foreign_id' => $foreignId,
                'foreign_model' => $foreignModel,
                Comment::getPublishedConditions(),
            ),
            'order' => 'id ASC',
        ));
        
        if (empty($comments)) {
            return '';
        }
        
        return App::loadView(
            $options['view']['module'], 
            $options['view']['name'], 
            array(
                'comments' => $comments,
                'allowRatings' => $options['allowRatings'],
                'ratingOptions' => $Comment->getPropertyRatingOptions(),
            )
        );
    }
    
    /**
     * Add new comment related to specified $foreignId and $foreignModel
     * 
     * @param string $foreignModel Comment subject model name qualified by its module, 
     *      e.g. 'App.WebContent', 'Eshop.EshopProduct'.
     * @param int $foreignId Comment subject id
     * 
     * @return string
     */
    public function add($foreignModel = null, $foreignId = null) {
        $this->displayOriginComment = true;
        $defaults = array(
            'view' => 'Comments/add',
            'allowRatings' => App::getSetting('App', 'Comment.allowRatings'),
        );
        $options = array_merge($defaults, Arr::camelizeKeys($this->params, array(
            'separator' => '-',
            'depth' => 1,
        )));
        // normalize view
        if (is_string($options['view'])) {
            $options['view'] = array('module' => 'App', 'name' => $options['view']);
        }
        
        $user = App::getUser();
        $Comment = $this->loadModel('Comment', true);
        if ($this->data) {
            if (empty($foreignId) || empty($foreignModel)) {
                App::setErrorMessage(__(__FILE__, 'The comment addition has failed. Missing foreign model and/or id.'));
            }
            elseif (
                App::getSetting('App', 'Comment.onlyByRegisteredUsers')
                && empty($user)
            ) {
                App::setErrorMessage(__(__FILE__, 'The comment addition has failed. Only registered users can add comments.'));
            }
            elseif (!$Comment->submit($foreignModel, $foreignId, $this->data)) {
                $_SESSION['_app']['Comment']['data'] = $this->data;
                $_SESSION['_app']['Comment']['errors'] = $Comment->getErrors();
                $_SESSION['_app']['Comment']['required'] = $Comment->getNotEmptyFields(array('alternative' => 'submitCommentForm'));
                App::setErrorMessage(__(__FILE__, 'The comment addition has failed. Please check the errors'));
            } 
            else {
                App::setSuccessMessage(__(__FILE__, 'The comment has been added.'));
                $autoApprove = App::getSetting('App', 'Comment.autoApprove');
                if (!$autoApprove) {
                    App::setSuccessMessage(__(__FILE__, 'It will be published after approval.'));
                    $body = __(__FILE__, '<b>New comment (awaiting for approval) from user %s on <a href="%s">%s</a>:</b><br>%s');
                    $subject = __(__FILE__, 'New comment (awaiting for approval) from user %s on %s');
                }
                else {
                    $body = __(__FILE__, '<b>New comment from user %s on <a href="%s">%s</a>:</b><br>%s');
                    $subject = __(__FILE__, 'New comment from user %s on %s');
                }
                App::sendEmail(
                    Str::fill(
                        $body,
                        $this->data['name'],
                        App::getUrl(array('locator' => App::getRefererUrl('/'), 'absolute' => true)),
                        App::getUrl(array('locator' => App::getRefererUrl('/'), 'absolute' => true)),
                        $this->data['text']
                    ),
                    App::getSetting('App', 'email.to'),
                    array(
                        'subject' => Str::fill($subject, $this->data['name'], App::getSetting('App', 'name'))
                    )
                );
            }
            App::redirect(App::getRefererUrl('/'));
        }
        elseif (empty($foreignId) || empty($foreignModel)) {
            throw new Exception(__e(__FILE__, 'Comments::add(): missing foreign model and/or id.'));
        }
        if (
            App::getSetting('App', 'Comment.onlyByRegisteredUsers')
            && empty($user)
        ) {
            return '';
        }
        
        // if rating is shown then find number of ratings and count average rating
        $ratingsCount = null;
        $averageRating = null;
        if ($options['allowRatings']) {
            $conditions = array(
                'foreign_id' => $foreignId,
                'foreign_model' => $foreignModel,
            );
            if (App::getSetting('App', 'Comment.autoApprove')) {
                $conditions['status !='] = 'enum_rejected_comment';
            }
            else {
                $conditions['status'] = 'enum_approved_comment';
            }
            $ratings = $Comment->findList(array(
                'fields' => array(
                    'rating',
                ),
                'conditions' => $conditions,
            ));
            $ratingsCount = count($ratings);
            $averageRating = 0;
            if ($ratingsCount > 0) {
                $averageRating = round(array_sum($ratings) / $ratingsCount);
            }
        }
        
        if (!empty($user)) {
            $this->data['name'] = $user['first_name'] . ' ' . $user['last_name'];
        }

        $data = Sanitize::value($_SESSION['_app']['Comment']['data'], $this->data);
        $errors = Sanitize::value($_SESSION['_app']['Comment']['errors'], array());
        unset($_SESSION['_app']['Comment']);
        return App::loadView(
            $options['view']['module'], 
            $options['view']['name'], 
            array(
                'user' => $user,
                'data' => $data,
                'errors' => $errors,
                'required' => $Comment->getNotEmptyFields(array(
                    'alternative' => 'submitCommentForm',
                    'data' => $data,
                )),
                'urlAddComment' => App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => $this->action,
                    'args' => array(
                        $foreignModel,
                        $foreignId,
                    )
                )),
                'allowRatings' => $options['allowRatings'],
                'ratingOptions' => $Comment->getPropertyRatingOptions(),
                'averageRating' => $averageRating,
            )
        );
    }
}
