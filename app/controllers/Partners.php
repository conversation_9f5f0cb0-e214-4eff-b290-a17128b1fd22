<?php
class Partners extends SmartController {
    
    protected $model = 'Partner';
    
    /** @var Partner */
    protected $Model;
    
    public function admin_index() {
        $this->viewOptions['columns'] = array(
            'name' => __a(__FILE__, 'Názov'),
            'locator' => __a(__FILE__, 'URL'),
            'active' => __a(__FILE__, 'Aktívny'),
        );
        $this->seoTitle = __a(__FILE__, 'Partneri');
        $this->viewOptions['title'] = __a(__FILE__, 'Partneri');
        
        return parent::admin_index();
    }
    
    public function admin_add() {
        $this->view = 'Partners/admin_form';
        $this->seoTitle = __a(__FILE__, 'Nový partner');
        $this->viewOptions['title'] = __a(__FILE__, 'Nový partner');
        return parent::admin_add();
    }
    
    public function admin_edit($id = null) {
        $this->view = 'Partners/admin_form';
        $this->viewOptions['title'] = __a(__FILE__, 'Upraviť partnera "%s"');
        return parent::admin_edit($id);
    }

    public function indexSlider() {
        $this->displayOriginComment = true;
        $defaults = array(
            'title' => null, // __(__FILE__, 'Naši partneri'),
            'buttonText' => null, //__(__FILE__, 'Všetci partneri'),
        );
        $inputOptions = Arr::camelizeKeys($this->params, array('separator' => '-'));
        $options = array_merge($defaults, $inputOptions);
        $Partner = $this->loadModel('Partner', true);
        $partners = $Partner->find(array(
            'conditions' => array(
                'Partner.active' => true
            ),
            'fields' => array(
                'Partner.id',
                'Partner.name',
                'Partner.locator',
                'Partner.image',
            ),
        ));
        // set image url paths
        foreach ($partners as &$partner) {
            if (!empty($partner['image'])) {
                $partner['image'] = $Partner->getFileFieldUrlPath('image', array(
                    'file' => $partner['image'],
                    'variant' =>  ''
                ));
            }
        }
        unset($partner);

        App::loadLib('App', 'SmartAdminLauncher');
        return $this->loadView('Partners/indexSlider', array(
            'title' => $options['title'],
            'buttonText' => $options['buttonText'],
            'partners' => $partners,
            'indexLocator' => App::getContentLocatorByPid('App.Partners.index'),
            'SmartAdminLauncher' => new SmartAdminLauncher(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                )),
                array(
                    'triggerTitle' => __a(__FILE__, 'Upraviť partnera'),
                )
            ),
        ));
    }
    
}
