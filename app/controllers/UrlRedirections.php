<?php

class UrlRedirections extends SmartController {
    
    protected $model = 'UrlRedirection';
    
    /**
    * Allow the model methods hinting in IDE
    * @var UrlRedirection
    */
    protected $Model;

    public function admin_index() {
        $urlRedirections = $this->Model->find(array(
            'fields' => array(
                'UrlRedirection.id',
                'UrlRedirection.old_locator',
                'UrlRedirection.new_locator',
                'UrlRedirection.status',
                'UrlRedirection.creation_type',
                'UrlRedirection.applications_count',
                'UrlRedirection.created',
                'UrlRedirection.modified',
                'User.username',
            ),
            'order' => 'UrlRedirection.id',
            'paginate' => true,
            'joins' => array(
                array(
                    'model' => 'User',
                    'type' => 'left',
                )
            ),
            'separate' => array('User'),
        ));

        App::setSeoTitle(__a(__FILE__, 'URL Presmerovania'));
        return Html::smartIndex(array(
            'title' => __a(__FILE__, 'Zoznam URL presmerovaní'),
            'records' => $urlRedirections,
            'Paginator' => $this->Model->Paginator,
            'columns' => array(
                'old_locator' => __a(__FILE__, 'Pôvodné URL'),
                'new_locator' => __a(__FILE__, 'Nové URL'),
                'status' => __a(__FILE__, 'Status'),
                'applications_count' => __a(__FILE__, 'Počet uplatnení'),
                'User.username' => __a(__FILE__, 'Vytvorené užívateľom'),
                'creation_type' => __a(__FILE__, 'Spôsob vytvorenia'),
                'created' => __a(__FILE__, 'Dátum vytvorenia'),
                'modified' => __a(__FILE__, 'Dátum úpravy'),
            ),
            'renderFields' => array(
                'creation_type' => $this->Model->getEnumValues('creation_type'),
            ),
            'actions' => array(                
                'add' => array(
                    'url' => '/mvc/App/UrlRedirections/admin_add',
                ),
            ),
            'recordActions' => array(
                'view' => array(
                    'url' => '/mvc/App/UrlRedirections/admin_view',
                ),
                'delete' => array(
                    'url' => '/mvc/App/UrlRedirections/admin_delete',
                    'confirmMessage' => __a(__FILE__, 'Prosím, potvrďte zmazanie presmerovania'),
                ),
            )
        ));
    }    
    
    public function admin_add() {

        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;

        if ($this->data) {
            $deletedLoopRedirection = null;
            $mergedChainedRedirection = null;
            $result = $this->Model->add(
                $this->data['old_locator'],
                $this->data['new_locator'],
                array(
                    'status' => $this->data['status'],
                    'creationType' => 'manual',
                    'deletedLoopRedirection' => &$deletedLoopRedirection,
                    'mergedChainedRedirection' => &$mergedChainedRedirection,
                )
            );
            if ($result) {
                if ($mergedChainedRedirection) {
                    App::setSuccessMessage(__a(
                        __FILE__,
                        'Vaše presmerovanie bolo spojené s predchádzajúcim naväzným presmerovaním "%s" -> "%s"',
                        $mergedChainedRedirection['old_locator'],
                        $mergedChainedRedirection['new_locator']
                    ));
                }
                else {
                    App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully created'));
                }
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_view',
                    'args' => array($this->Model->getPropertyId()),
                    'source' => App::$requestSource,
                )));
            }
            elseif ($result === null) {
                if ($deletedLoopRedirection) {
                    App::setSuccessMessage(__a(
                        __FILE__,
                        'Vaše presmerovanie nebolo pridané len sa odstránilo predchádzajúce presmerovanie opačným smerom "%s" -> "%s"',
                        $deletedLoopRedirection['old_locator'],
                        $deletedLoopRedirection['new_locator']
                    ));
                }
                else {
                    App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully created'));
                }
                App::redirect(App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_index',
                    'source' => App::$requestSource,
                )));
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
        }
        
        App::setSeoTitle(__a(__FILE__, 'Pridať nové URL presmerovanie'));
        return Html::smartForm(array(
            'title' => __a(__FILE__, 'Pridať nové URL presmerovanie'),
            'data' => $this->data,
            'Model' => $this->Model,
            'lang' => $lang,
            'columns' => 4,
            'fields' => array(
                array('field' => 'id', 'type' => 'hidden'),
                array('row'),
                    array(
                        'field' => 'old_locator', 
                        'label' => __a(__FILE__, 'Pôvodné URL'),
                        'hint' => __a(__FILE__, 'URL z ktorého sa vykoná presmerovanie. URL musí byť zadané v relatívnom tvare <code>/slug/arg</code> alebo aj s jazykom <code>/en/slug/arg</code>'),
                    ),
                    array(
                        'field' => 'new_locator', 
                        'label' => __a(__FILE__, 'Nové URL'),
                        'hint' => __a(__FILE__, 'URL na ktoré sa presmeruje. URL musí byť zadané v relatívnom tvare <code>/slug/arg</code> alebo aj s jazykom <code>/en/slug/arg</code>'),
                    ),
                    array(
                        'field' => 'status', 
                        'label' => __a(__FILE__, 'Status'),
                        'hint' => __a(__FILE__, 'HTTP status presmerovania. napr. <code>301</code>'),
                    ),
                array('/row'),
            )
        ));
    }    
    
    public function admin_view($id = null) {
        if (!$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            App::redirect(App::getUrl(array(
                'locator' => '/_error',
                'source' => App::$requestSource,
            )));
        }
        
        // get lang of translated fields
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        
        $this->data = $this->Model->findFirst(array(
            'fields' => array(
                'UrlRedirection.id',
                'UrlRedirection.old_locator',
                'UrlRedirection.new_locator',
                'UrlRedirection.status',
                'UrlRedirection.creation_type',
                'UrlRedirection.run_users_id',
                'UrlRedirection.applications_count',
                'UrlRedirection.last_applications',
                'UrlRedirection.crawled_by',
                'UrlRedirection.created',
                'UrlRedirection.modified',
                'User.username',
            ),
            'conditions' => array(
                'UrlRedirection.id' => $id,
            ),
            'joins' => array(
                array(
                    'type' => 'left',
                    'model' => 'User'
                )
            ),
        ));
        
        // format last_applications and crawled_by
        $this->data['last_applications'] = echoReadable(json_decode($this->data['last_applications'], true), array(
            'json' => true,
            'return' => true,
        ));
        $this->data['crawled_by'] = echoReadable(json_decode($this->data['crawled_by'], true), array(
            'json' => true,
            'return' => true,
        ));
    
        App::setSeoTitle(__a(
            __FILE__,
            '&quot;%s&quot; -> &quot;%s&quot;',
            $this->data['old_locator'],
            $this->data['new_locator']
        ));
        return Html::smartForm(array(
            'title' => __a(
                __FILE__, 
                'URL presmerovanie &quot;%s&quot; -> &quot;%s&quot;', 
                $this->data['old_locator'],
                $this->data['new_locator']
            ),
            'data' => $this->data,
            'lang' => $lang,
            'columns' => 4,
            'headerTemplate' => '<div class="-run-sfo-title">:title:</div>:actions::messages::deleteButton:<span class="btn btn-default -run-sfo-close">:closeButtonLabel:</span>',
            'fields' => array(
                array('field' => 'id', 'type' => 'hidden'),
                array('row', 'columns'),
                    array(
                        'field' => 'old_locator', 
                        'label' => __a(__FILE__, 'Pôvodné URL'),
                        'hint' => __a(__FILE__, 'URL z ktorého sa vykoná presmerovanie. URL musí byť zadané v relatívnom tvare <code>/slug/arg</code> alebo aj s jazykom <code>/en/slug/arg</code>'),
                        'disabled' => true,
                    ),
                    array(
                        'field' => 'new_locator', 
                        'label' => __a(__FILE__, 'Nové URL'),
                        'hint' => __a(__FILE__, 'URL na ktoré sa presmeruje. URL musí byť zadané v relatívnom tvare <code>/slug/arg</code> alebo aj s jazykom <code>/en/slug/arg</code>'),
                        'disabled' => true,
                    ),
                    array(
                        'field' => 'status', 
                        'label' => __a(__FILE__, 'Status'),
                        'hint' => __a(__FILE__, 'HTTP status presmerovania. napr. <code>301</code>'),
                        'disabled' => true,
                    ),
                    array(
                        'field' => 'applications_count',
                        'label' => __a(__FILE__, 'Počet uplatnení'),
                        'hint' => __a(__FILE__, 'Koľko krát sa toto presmerovanie už uplatnilo? Ako uplatnenia sa nepočítajú presmerovania z tej istej IP, z ktorej sa presmerovanie vytvorilo (t.j. prípady, keď admin uživateľ skúša, či presmerovanie funguje).'),
                        'disabled' => true,
                    ),
                array('/row'),
                array('row'),
                    array(
                        'field' => 'username',
                        'label' => 'Vytvorené užívateľom',
                        'disabled' => true,
                    ),
                    array(
                        'field' => 'creation_type', 
                        'label' => __a(__FILE__, 'Spôsob vytvorenia'),
                        'disabled' => true,
                    ),
                    array(
                        'field' => 'created',
                        'label' => 'Vytvorené',
                        'disabled' => true,
                    ),
                    array(
                        'field' => 'modified',
                        'label' => 'Upravené',
                        'disabled' => true,
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    array(
                        'field' => 'crawled_by', 
                        'label' => __a(__FILE__, 'Crawleri'),
                        'type' => 'display',
                        'hint' => __a(
                            __FILE__, 
                            'Zoznam sledovaných bot-crawlerov, pre ktorých sa presmerovanie uplatnilo. Ako uplatnenia sa nepočítajú presmerovania z tej istej IP, z ktorej sa presmerovanie vytvorilo (t.j. prípady, keď admin uživateľ skúša, či presmerovanie funguje). Zoznam má nasledovný tvar: <code>"{crawlerName}" => "{ip}" => array("first" => "{redirectionFirstCrawlDatetime}", "last" => "{redirectionLastCrawlDatetime}")</code>. Špeciálny typ crawlera je <code>"humanVisitor"</code> pre všetky iné ako bot-uplatnenia.' 
                        ),
                    ),
                array('/row'),
                array('row', 'columns' => 1),
                    array(
                        'field' => 'last_applications', 
                        'label' => __a(__FILE__, 'Posledné uplatnenia'),
                        'type' => 'display',
                        'hint' => __a(
                            __FILE__,
                            'Zoznam posledných %s uplatnení pre sledovaných bot-crawlerov. Ako uplatnenia sa nepočítajú presmerovania z tej istej IP, z ktorej sa presmerovanie vytvorilo (t.j. prípady, keď admin uživateľ skúša, či presmerovanie funguje). Jednotlivé položky zoznamu majú nasledovný tvar: <code>"{datetime} >> {crawlerName} >> {ip} >> {refererUrl} >> {userAgent}"</code>. Špeciálny typ crawlera je <code>"humanVisitor"</code> pre všetky iné ako bot-uplatnenia.',
                            UrlRedirection::LAST_APPLICATIONS_MAX_LENGTH
                        ),
                    ),
                array('/row'),
            )
        ));
    }
}
