<?php
/**
 * @class Groups
 * A simple application controller extension
 */
class Groups extends Controller {
    
    public function __construct(){
        parent::__construct();
        $this->loadModel('Group');    
    }
    
    /**
     * view
     * Retrieves rows from database.
     */
    public function admin_index() {
        $Res = new ExtResponse();
        $Res->success = true;
        
        $Group = new Group();
        
        $options = ExtRequest::parsePaging();
        $options['order'][] = 'Group.id ASC';
        
        $Res->data = $Group->find(array(
            'fields' => array(
                'Group.id', 
                'Group.pid', 
                'Group.name', 
                'Group.hierarchy', 
                'Group.created',
                'Group.modified',
            ),
            'offset' => $options['offset'],
            'limit' => $options['limit'],
            'order' => $options['order'],
            
        ));
        // find count
        $Res->total = $Group->findCount();
        return $Res->toJson();
    }
    
}

