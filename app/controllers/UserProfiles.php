<?php
/**
 * @class UserProfileProfiles
 * A simple application controller extension
 */
class UserProfiles extends SmartController {
    
    public function __construct(){
        parent::__construct();
        $this->loadModel('UserProfile');
    }
    
    /**
     * view
     * Retrieves rows from database.
     */
    public function admin_load() {
        $Res = new ExtResponse();
        $Res->success = true;
        $UserProfile = new UserProfile();
        
        $userId = @$_POST['userId'];
        if (empty($userId) || !is_numeric($userId)) {
            $Res->success = false;
            $Res->message = array(
                'text' => __a(__FILE__, 'Wrong identifikator of user id'),
                'type' => 'error'
            );
            return $Res->toJson();
        }

        $Res->data = $UserProfile->findFirst(array(
            'conditions' => array(
                'run_users_id' => $_POST['userId']
            ),
        ));
        return $Res->toJson();
    }
    
    public function admin_update() {
        $Res = new ExtResponse();
        $Res->success = true;
        $UserProfile = new UserProfile();
        
        if ($UserProfile->save($this->data)) {
            $Res->success = true;
            $Res->message = array(
                'text' => __a(__FILE__, 'Update of user profile has success'),
                'type' => 'info'
            );
        } else {
            $Res->message = array(
                'text' => __a(__FILE__, 'Update of user profile has failed'),
                'type' => 'error',
                'errors' => $UserProfile->getErrors()
            );
        }
        return $Res->toJson();
    }
    
    /**
     * MVC ELEMENT
     */
    public function bonusPointsInfo() {
        $this->loadModel('UserProfile');
        $Profile = new UserProfile();
        return $this->loadView('UserProfiles/bonusPointsInfo', array(
            'actualBonusPoints' => $Profile->getBonusPoints(),
            'actualBonusDiscount' => $Profile->getBonusDiscount(),
            'applicableBonusPoints' => App::getSetting('Eshop', 'EshopOrder.applicableBonusPoints'),
            'bonusDiscount' => App::getSetting('Eshop', 'EshopOrder.bonusDiscount'),
            'bonusPointsInfoUrl' => App::getContentUrlByPid('Eshop.bonusPointsInfo'),
        ));
    }
    
    /**
     * MVC ELEMENT
     * 
     * $this->params['userId'] int Id of user to get bonus summary for. If not provided
     *          or NULL then defaults to logged user id. If you need bonus summary 
     *          for order created by unregistered user then pass here 0 (to avoid 
     *          use of actually logged user id)
     */
    public function bonusPointsSummary() {
        $params = array_merge(array(
            'userId' => null
        ), $this->params);
        if (!isset($params['userId'])) {
            $params['userId'] = App::getUser('id');
        }
        $this->loadModel('UserProfile');
        $Profile = new UserProfile();
        return $this->loadView('UserProfiles/bonusPointsSummary', array(
            'userId' => $params['userId'],
            'actualBonusPoints' => $Profile->getBonusPoints($params['userId']),
            'actualBonusDiscount' => $Profile->getBonusDiscount($params['userId']),
            'applicableBonusPoints' => App::getSetting('Eshop', 'EshopOrder.applicableBonusPoints'),
            'bonusDiscount' => App::getSetting('Eshop', 'EshopOrder.bonusDiscount'),
            'bonusPointsInfoUrl' => App::getContentUrlByPid('Eshop.bonusPointsInfo'),
        ));
    }
}

