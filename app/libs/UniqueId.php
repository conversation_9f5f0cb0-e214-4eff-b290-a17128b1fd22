<?php
/**
 * UniqueId class serves to generate unique sequentional integer ids even if called
 * by many processes at different times. Specify the sequence name to create a different 
 * independent sequences of ids.
 * 
 * Internally a database table is used so uniquity and continuity of ids sequence is kept in 
 * measure of existence of the same table in DB. If needed then you can use UniqueId::setLast() 
 * to initialize the new table to some starting id or UniqueId::reset() to reset the sequence
 * to start again from 1.
 * 
 * To generate one single unique id:
 * 
 *      $UniqueId = new UniqueId();
 *      $id = $UniqueId->get();
 * 
 * If you need to generate a sequence of ids then consider following approaches.
 * Generation of 1000 ids takes:
 * 
 *  - round 3500 miliseconds without the processing reservation:
 * 
 *      $UniqueId = new UniqueId();
 *      for (...) {
 *          $id = $UniqueId->get();
 *          //...
 *      }
 * 
 *  - round 1400 miliseconds with processing reservation:
 * 
 *      $UniqueId = new UniqueId();
 *      $UniqueId->reserveProcessing();
 *      for (...) {
 *          $id = $UniqueId->get();
 *          //...
 *      }
 *      $UniqueId->unreserveProcessing();
 * 
 *  - round 2 miliseconds with processing reservation and getting only the first id 
 *    and setting the last id:
 * 
 *      $UniqueId = new UniqueId();
 *      $UniqueId->reserveProcessing();
 *      $id = $UniqueId->get() - 1;
 *      for (...) {
 *          $id++;
 *          //...
 *      }
 *      $UniqueId->setLast($id);
 *      $UniqueId->unreserveProcessing();
 * 
 * You can easily create your own local unique id generator (if you need to have you own 
 * continual sequence of ids not shared with others) in following two ways:
 * 
 *  - by specifying sequence name (convenient if you use your own sequence on 
 *    a single place in project):
 * 
 *      $MyUniqueId = new UniqueId('MyClass_myMethod');
 * 
 *  - by extending UniqueId class (convenient if you use your own sequence on 
 *    more than one single place in project):
 * 
 *      App::loadLib('App', 'UniqueId');
 *      class MyUniqueId extends UniqueId {}
 * 
 *      App::loadLib('MyModule', 'MyUniqueId');
 *      $MyUniqueId = new MyUniqueId();
 * 
 * These two approaches can be combined, so you can have your own class and use
 * a sequence name with it:
 * 
 *      $MyUniqueId = new MyUniqueId('MyClass_myMethod');
 */
class UniqueId {
    
    private $table = 'run_unique_ids';
    
    private $schema = array(
        'sequence_name' => array('type' => 'varchar', 'index' => 'primary', 'comment' => 'Name of ids sequence'),
        'id' => array('type' => 'int', 'comment' => 'Last id of the sequence'),
    );
    
    private $sequenceName = '';

    private $reservedExternally = false;
    
    static private $externalReservationNames = array();
    
    /**
     * Returns new UniqueId instance
     * 
     * ATTENTION: If on instance creation you get an exception "Table creation inside 
     * transaction XYZ. Table creation autocommits transaction" then just create
     * a new instance out of transaction (e.g. in _debug screen). This can happen only 
     * when class instance is created for very first time in project and the background 
     * DB table does not exist yet (a that is why it is created on instance creation)
     * 
     * @param string $sequenceName Optional. Name of ids sequence. If you would like to get
     *      a separate continuous sequence of ids then provide this name and use the same name 
     *      to access this sequence on different places in project. To assure uniquity use following 
     *      pattern to create sequence name: {className}::{methodName}()[/{processingSpecificity}]
     *      If not provided then default sequence is used and very probably you cannot 
     *      expect that the next id you will get will be the prevoius +1. Defaults to NULL.
     */
    public function __construct($sequenceName = null) {
        if (!DB::hasTable($this->table)) {
            DB::createTable($this->table, $this->schema, array(
                // much more faster than InnoDB and we do not want transactions (ids 
                // must be update immediatelly and not only after transaction commit)
                'engine' => 'MyISAM'
            ));
        }
        
        // create the internal sequence name
        $this->sequenceName = get_class($this);
        $sequenceName = (string)$sequenceName;
        if ($sequenceName !== '') {
            $this->sequenceName .= '_' . $sequenceName;
        }
        if (strlen($this->sequenceName) > 255) {
            throw new Exception(
                __e(
                    __FILE__, 
                    'Too long sequence name "%s". The maximal length of sequence name can be %s ASCII characters', 
                    $sequenceName, 
                    255 - strlen(get_class($this))
                )
            );
        }
    }
    
    /**
     * Returns next unique integer id in current sequence. The very first returned 
     * id is always 1.
     * 
     * To generate one single unique id:
     * 
     *      $UniqueId = new UniqueId();
     *      $id = $UniqueId->get();
     * 
     * To generate a sequence of ids (optimal approach):
     * 
     *      $UniqueId = new UniqueId();
     *      $UniqueId->reserveProcessing();
     *      $id = $UniqueId->get() - 1;
     *      for (...) {
     *          $id++;
     *          //...
     *      }
     *      $UniqueId->setLast($id);
     *      $UniqueId->unreserveProcessing();
     * 
     * @param array $options Optional. Following options can be specified:
     *      - 'tries' (int) Number of tries to reserve the tables in case that it does 
     *          not succeed on first attempt. Defaults to 100, means 100 attempts are made.
     *      - 'retryTime' (int) Number of miliseconds to wait between reservations attempts.
     *          Defaults to 200.
     * 
     * @return int Unique id in current sequence
     * 
     * @throws Exception_UniqueId_ProcessingReservationFailure if the reservation of 
     *      unique id processing fails
     */
    public function get($options = array()) {
        $this->reserveProcessingInternally($options);
        $sequence = DB::select($this->table, array(
            'conditions' => array('sequence_name' => $this->sequenceName),
            'fields' => array('id'),
            'first' => true,
        ));
        if (empty($sequence)) {
            $id = 1;
            DB::insert(
                $this->table,
                array(
                    'sequence_name' => $this->sequenceName,
                    'id' => $id,
                ), 
                array(
                    'reserve' => false
                )
            );
        }
        else {
            $id = $sequence['id'] + 1;
            DB::update(
                $this->table,
                array(
                    'id' => $id,
                ), 
                array(
                    'conditions' => array(
                        'sequence_name' => $this->sequenceName,
                    ),
                    'reserve' => false
                )
            );
        }
        $this->unreserveProcessingInternally();
        return $id;
    }
    
    /**
     * Sets new last unique id in current sequence.
     * 
     * @param int $id
     * @param array $options Optional. Following options can be specified:
     *      - 'exceptionOnDescending' (bool) If TRUE then an exception is thrown 
     *          if the new $id is lower that existing last id. If FALSE you can set 
     *          the last id to any integer value. Defaults to TRUE.
     *      - 'tries' (int) Number of tries to reserve the tables in case that it does 
     *          not succeed on first attempt. Defaults to 100, means 100 attempts are made.
     *      - 'retryTime' (int) Number of miliseconds to wait between reservations attempts.
     *          Defaults to 200.
     * 
     * @throws Exception_UniqueId_ProcessingReservationFailure if the reservation of 
     *      unique id processing fails
     * @throws Exception
     */
    public function setLast($id, $options = array()) {
        $defaults = array(
            'exceptionOnDescending' => true,
        );
        $options = array_merge($defaults, $options);
        $this->reserveProcessingInternally($options);
        $sequence = DB::select($this->table, array(
            'conditions' => array('sequence_name' => $this->sequenceName),
            'fields' => array('id'),
            'first' => true,
        ));
        if (empty($sequence)) {
            DB::insert(
                $this->table,
                array(
                    'sequence_name' => $this->sequenceName,
                    'id' => $id,
                ), 
                array(
                    'reserve' => false
                )
            );
        }
        else {
            if (
                $options['exceptionOnDescending']
                && $id < $sequence['id']
            ) {
                $this->unreserveProcessingInternally();
                throw new Exception(__e(__FILE__, 'Invalid last id %s. New last id cannot be lower than %s', $id, $sequence['id']));
            }
            DB::update(
                $this->table,
                array(
                    'id' => $id,
                ), 
                array(
                    'conditions' => array(
                        'sequence_name' => $this->sequenceName,
                    ),
                    'reserve' => false
                )
            );
        }
        $this->unreserveProcessingInternally();
    }
    
    /**
     * Resets the current sequence (in fact it is deleted from background table)
     * so the next call of UniqueId::get() will return 1.
     * 
     * @param int $id
     * @param array $options Optional. Following options can be specified:
     *      - 'tries' (int) Number of tries to reserve the tables in case that it does 
     *          not succeed on first attempt. Defaults to 100, means 100 attempts are made.
     *      - 'retryTime' (int) Number of miliseconds to wait between reservations attempts.
     *          Defaults to 200.
     * 
     * @throws Exception_UniqueId_ProcessingReservationFailure if the reservation of 
     *      unique id processing fails
     */
    public function reset($options = array()) {
        $this->reserveProcessingInternally($options);
        DB::delete($this->table, array(
            'conditions' => array(
                'sequence_name' => $this->sequenceName,
            ),
            'reserve' => false
        ));
        $this->unreserveProcessingInternally();
    }
    
    /**
     * Reserves processing of unique id creation for current sequence name 
     * 
     * @param array $options Optional. Following options can be specified:
     *      - 'tries' (int) Number of tries to reserve the tables in case that it does 
     *          not succeed on first attempt. Defaults to 100, means 100 attempts are made.
     *      - 'retryTime' (int) Number of miliseconds to wait between reservations attempts.
     *          Defaults to 200.
     * 
     * @throws Exception_UniqueId_ProcessingReservationFailure if the reservation of 
     *      unique id processing fails
     */
    private function reserveProcessingInternally($options = array()) {
        // do nothing if reserved externally
        if ($this->reservedExternally) {
            return;
        }
        try {
            App::reserveProcessing($this->sequenceName, $options);
        } catch (Exception_App_ProcessingReservationFailure $e) {
            throw new Exception_UniqueId_ProcessingReservationFailure(
                __e(__FILE__, 'Reservation of %s unique id processing has failed', $this->sequenceName),
                0,
                $e
            );
        }
    }
    
    /**
     * Unreserves processing of unique id creation for current sequence name 
     */
    private function unreserveProcessingInternally() {
        // do nothing if reserved externally
        if ($this->reservedExternally) {
            return;
        }
        App::unreserveProcessing($this->sequenceName);
    }
    
    /**
     * Reserves processing of unique id creation for current sequence name. 
     * In case of UniqueId class use it is better to use this method instead
     * of App::reserveProcessing() because it ensures that even other uses of the
     * same sequence will respect this reservation.
     * 
     * @param array $options Optional. Following options can be specified:
     *      - 'tries' (int) Number of tries to reserve the tables in case that it does 
     *          not succeed on first attempt. Defaults to 100, means 100 attempts are made.
     *      - 'retryTime' (int) Number of miliseconds to wait between reservations attempts.
     *          Defaults to 200.
     * 
     * @throws Exception_UniqueId_ProcessingReservationFailure if the reservation of 
     *      unique id processing fails
     * @throws Exception if called nested
     */
    public function reserveProcessing($options = array()) {
        if (!empty(self::$externalReservationNames[$this->sequenceName])) {
            throw new Exception(
                __e(__FILE__, 'Calls of %s::reserveProcessing() cannot be nested', get_class($this))
            );
        }
        self::$externalReservationNames[$this->sequenceName] = true;
        $this->reserveProcessingInternally($options);
        $this->reservedExternally = true;
    }
    
    /**
     * Unreserves processing of unique id creation for current sequence name 
     * 
     * @throws Exception if called without previous reservation
     */
    public function unreserveProcessing() {
        if (empty(self::$externalReservationNames[$this->sequenceName])) {
            throw new Exception(
                __e(__FILE__, 'Call of %s::unreserveProcessing() without previous reservation', get_class($this))
            );
        }
        $this->reservedExternally = false;
        $this->unreserveProcessingInternally();
        unset(self::$externalReservationNames[$this->sequenceName]);
    }
}
class Exception_UniqueId_ProcessingReservationFailure extends Exception {}
