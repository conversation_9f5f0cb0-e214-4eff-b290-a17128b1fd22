<?php
/**
 * GalleryModel class (together with SmartController) simplifies implementation 
 * of FormHelper::gallery() input. Child class implementation details:
 *      - GalleryModel::$galleryOwnerModel MUST be defined
 *      - there MUST be 'sort' field in GalleryModel::$schema (= child model must be ordered list)
 *      - if there are more that one file field in GalleryModel::$fileFields then the
 *        image field MUST be defined as the first field
 *      - it is fine to define GalleryModel::$nameField
 * 
 * Following code should be sufficient to implement a new gallery input:
 *
 * Model file:
 *      
 *      App::loadLib('App', 'GalleryModel');
 *      class ProductImage extends GalleryModel {
 *          protected $table = 'run_product_images';
 *          protected $schema = array(
 *              'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
 *              'run_eshop_product_stops_id' => array('type' => 'int', 'index' => 'index'),
 *              'file' => array('type' => 'varchar'),
 *              'name' => array('type' => 'varchar', 'default' => null),
 *              'sort' => array('type' => 'int', 'default' => null),
 *              'created' => array('type' => 'datetime', 'default' => null),
 *          );
 *          protected $galleryOwnerModel = 'Product';
 *          protected $nameField = 'name';
 *      }
 * 
 * Controller file:
 * 
 *      class ProductImages extends SmartController {
 *          protected $model = 'ProductImage';
 *      } 
 * 
 * Rights file:
 * 
 *      'ProductImages.admin_getGalleryImages' => true,
 *      'ProductImages.admin_addGalleryImage' => true,
 *      'ProductImages.admin_updateGalleryImage' => true,
 *      'ProductImages.admin_moveGalleryImage' => true,
 *      'ProductImages.admin_deleteGalleryImage' => true,
 * 
 * View with gallery input generation:
 * 
 *      App::loadLib('App', 'FormHelper');
 *      $Form = new FormHelper(array(...));
 *      echo $Form->gallery('ProductImage.file', array(
 *          'options' => array(
 *              'foreignKey' => $owner['id'],
 *              'nameField' => 'ProductImage.name',
 *              'fields' => array(
 *                  'ProductImage.file' => array('label' => 'Image'),
 *                  'ProductImage.name' => array('label' => 'Name'),
 *              ),
 *              'actions' => array(
 *                  'load' => '/mvc/Eshop/ProductImages/admin_getGalleryImages',
 *                  'move' => '/mvc/Eshop/ProductImages/admin_moveGalleryImage',
 *                  'add' => array(
 *                      'label' => __a(__FILE__, 'Add new image'),
 *                      'url' => '/mvc/Eshop/ProductImages/admin_addGalleryImage',
 *                  ),
 *                  'update' => array(
 *                      'label' => __a(__FILE__, 'Update image'),
 *                      'url' => '/mvc/Eshop/ProductImages/admin_updateGalleryImage',
 *                  ),
 *                  'delete' => array(
 *                      'label' => __a(__FILE__, 'Delete image'),
 *                      'url' => '/mvc/Eshop/ProductImages/admin_deleteGalleryImage',
 *                      'confirmMessage' => __(__FILE__, 'Please confirm removal of image'),
 *                  ),
 *              ),
 *          )
 *      ));
 */
abstract class GalleryModel extends Model {
    
//    protected $table = 'run_my_gallery_model_images';
//    
//    protected $schema = array(
//        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
//        'run_my_gallery_owner_id' => array('type' => 'int', 'index' => 'index'),
//        'file' => array('type' => 'varchar'),
//        'name' => array('type' => 'varchar', 'default' => null),
//        'sort' => array('type' => 'int', 'default' => null),
//        'created' => array('type' => 'datetime', 'default' => null),
//    );
    
    /**
     * Definition of gallery owner model name.
     * The owner model must be from the same module as the child gallery model class
     * 
     * @var string
     */
    protected $galleryOwnerModel = null;
    
    /**
     * Instance of gallery owner model
     *
     * @var Model
     */
    protected $GalleryOwnerModel = null;
    
    protected $fileFields = array(
        'file' => array(
            'extension' => 'jpg',
            'quality' => 90,
            'variants' => array(
                'originalThumb' => array(
                    'extension' => 'jpg',
                    'pngGifToJpg' => array(array(255, 255, 255)),
                    'scaleByX' => array(200),
                    'lazy' => true,
                ),
                'small' => array(
                    'pngGifToJpg' => array(array(255, 255, 255)),
//                    'fit' => array(200, 200),
                    'cover' => array(200, 200),
                    'cropInMiddle' => array(200, 200),
//                    'scaleByX' => array(200),
                    'lazy' => true,
                ),
                'large' => array(
                    'pngGifToJpg' => array(array(255, 255, 255)),
//                    'fit' => array(300, 300),
                    'cover' => array(300, 300),
                    'cropInMiddle' => array(300, 300),
//                    'scaleByX' => array(300),
                    'lazy' => true,
                ),
                'original' => array(
                    'source' => true,
                    'extension' => null, // implicit extension inherited from file
                    'quality' => null, // implicit quality
                    'fit' => array(1920, 1080),
                ),
            ),
        ),
    ); 
    
    public function __construct() {
        parent::__construct();
        
        if (empty($this->galleryOwnerModel)) {
            throw new Exception(__e(__FILE__, 'Undefined %s::$galleryOwnerModel', $this->name));
        }
        if (empty($this->schema['sort'])) {
            throw new Exception(__e(__FILE__, 'Undefined field "sort" in %s::$chema', $this->name));
        }
        $this->GalleryOwnerModel = $this->loadModel($this->galleryOwnerModel, true);
        $galleryOwnerForeignKeyField = $this->getGalleryOwnerForeignKeyField();
        if (empty($this->schema[$galleryOwnerForeignKeyField])) {
            throw new Exception(__e(__FILE__, 'Undefined field "%s" in %s::$chema', $galleryOwnerForeignKeyField, $this->name));
        }
        
        // set validations
        $this->validations = array();
        if (!empty($this->nameField)) {
            $this->validations[$this->nameField] = array(
                array(
                    'rule' => 'required',
                    'message' => __v(__FILE__, 'Enter a value'),
                    'on' => 'create',
                ),
                array(
                    'rule' => 'notEmpty',
                    'message' => __v(__FILE__, 'Enter a value'),
                ),
            );
        }
        $imageField = $this->getGalleryImageField();
        $this->validations[$imageField] = array(
            array(
                'rule' => 'required',
                'message' => __v(__FILE__, 'Choose a file'),
                'on' => 'create',
            ),
            array(
                'rule' => 'notEmpty',
                'message' => __v(__FILE__, 'Choose a file'),
            ),
            array(
                'rule' => array('uploadData', array('noErrors' => true)),
                'message' => __v(__FILE__, 'File upload error has occured'),
            ),
            array(
                'rule' => array('uploadData', array('type' => '/^image\//')),
                'message' => __v(__FILE__, 'File is not an image'),
            ),                
        );
        $galleryOwnerForeignKeyField = $this->getGalleryOwnerForeignKeyField();
        $this->validations[$galleryOwnerForeignKeyField] = array(
            array(
                'rule' => 'required',
                'message' => __v(__FILE__, 'Enter a value'),
                'on' => 'create',
            ),
            array(
                'rule' => 'notEmpty',
                'message' => __v(__FILE__, 'Enter a value'),
            ),
        );
    }
    
    public function normalize($data, $options = array()) {
        $defauts = array(
            'on' => null,
            'alternative' => null,
        );
        $options = array_merge($defauts, $options);
        
        // set missing name
        if (
            !empty($this->nameField)
            && empty($data[$this->nameField])
            && ($imageField = $this->getGalleryImageField())
            && !empty($data[$imageField])
            && is_array($data[$imageField])
        ) {
            $data[$this->nameField] = $data[$imageField]['name'];
        }
        
        return parent::normalize($data, $options);
    }

    /**
     * Returns name of file field to store gallery images
     * 
     * @return string
     */
    public function getGalleryImageField() {
        $fileFields = array_keys($this->fileFields);
        return reset($fileFields);
    }
    
    /**
     * Returns name of foreign key field of gallery owner model
     * 
     * @return string
     */
    public function getGalleryOwnerForeignKeyField() {
        return $this->GalleryOwnerModel->getForeignKey();
    }   
}
