<?php
/**
 * Google utilities class
 * 
 * Implements:
 *      - Google Distance Matrix API, see https://developers.google.com/maps/documentation/distance-matrix/overview
 *      - Google Translate API, see https://cloud.google.com/translate/docs/reference/api-overview
 */
class Google {

    /**
     * Google API key
     *
     * @var string
     */
    protected $apiKey = null;

    function __construct($options = array()) {
        $options = array_merge(array(
            'apiKey' => App::getSetting('App', 'google.apiKey'),
        ), $options);

        $this->apiKey = $options['apiKey'];
    }

    /**
     * Does API request for specified url
     *
     * @param string $apiUrl
     * @param array $options App::request options
     *
     * @return array Api response
     *
     * @throws Exception on failure
     */
    protected function requestApi($apiUrl, $options = array()) {
        $options['getInfo'] = true;
        $responseJson = App::request($apiUrl, $options, $info);
        if (
            $info['http_code'] === 200
            && $responseJson === 'null'
        ) {
            return null;
        }
        if (
            !$responseJson
            || !is_array($response = json_decode($responseJson, true))
        ) {
            throw new Exception(__e(
                __FILE__, 'Invalid respose for API url "%s": "%s"', $apiUrl, $responseJson
            ));
        }
        return (array)$response;
    }
    
    /**
     * Uses Google Distance Matrix API to find distance between two places. 
     * See: https://developers.google.com/maps/documentation/distance-matrix/overview
     * 
     * @param string $origin Full address or coordinates of origin
     * @param $destination Full address or coordinates of destination
     * 
     * @return float Distance between origin and destination.
     * 
     * @throws Exception on failure
     */
    public function getDistance($origin, $destination, $options = array()) {
        $options = array_merge(array(
            'allowCache' => true,
        ), $options);
        if (
            empty($origin)
            || !is_string($origin)
            || empty($destination)
            || !is_string($destination)
        ) {
            throw new Exception(__e(__FILE__, 'Invalid origin or destination value.'));
        }
        if ($options['allowCache']) {
            $cachedDistance = $this->getDistanceFromCache($origin, $destination);
            if ($cachedDistance !== null) {
                return $cachedDistance;
            }
        }
        $response = $this->requestApi(
            'https://maps.googleapis.com/maps/api/distancematrix/json', 
            array(
                'get' => array(
                    'destinations' => $destination,
                    'origins' => $origin,
                    'key' => $this->apiKey,
                )
            )
        );
        if (!isset($response['rows'][0]['elements'][0]['distance']['value'])) {
            throw new Exception(__e(__FILE__, 'Invalid response: %s', json_encode($response, JSON_UNESCAPED_UNICODE)));
        }
        $distance = (float)$response['rows'][0]['elements'][0]['distance']['value'];
        if ($options['allowCache']) {
            $this->addDistanceToCache($origin, $destination, $distance);
        }
        return $distance;
    }
    
    /**
     * Returns app root relative filepath to file (including it) keeping distances cache, 
     * e.g. /tmp/google_distance_cache_json
     * 
     * @return string
     */
    protected function getDistancesCacheFilePath() {
        $name = Str::underscore(get_class($this) . '_distance_cache_json');
        return File::getRelativePath(TMP . DS . $name);
    }
    
    /**
     * Return distances cache array like:
     * 
     *      array(
     *          {originPid01} => array(
     *              {destinationPid01} => 139000,
     *              {destinationPid02} => 65000,
     *              ...,
     *          ),
     *          {originPid02} => array(
     *              ...,
     *          ),
     *          ....
     *      )
     * 
     * @return array
     */
    protected function loadDistancesCache() {
        $filePath = ROOT . DS . $this->getDistancesCacheFilePath();
        if (is_readable($filePath)) {
            return (array)json_decode(file_get_contents($filePath), true);
        }
        return array();
    }
    
    /**
     * Stores distances cache
     * 
     * @param array $distancesCache
     */
    protected function storeDistancesCache($distancesCache) {
        $filePath = ROOT . DS . $this->getDistancesCacheFilePath();
        $tmpFilePath = $filePath . 'tmp';
        file_put_contents($tmpFilePath, json_encode($distancesCache));
        rename($tmpFilePath, $filePath);
    }
    
    /**
     * Returns cached value of distance between origin and destination
     * 
     * @param string $origin Full address or coordinates of origin
     * @param $destination Full address or coordinates of destination
     * 
     * @return float|NULL Distance in meters or NULL if no chached value has been found
     */
    protected function getDistanceFromCache($origin, $destination) {
        $originPid = Str::slugize($origin);
        $destinationPid = Str::slugize($destination);
        $distancesCache = $this->loadDistancesCache();
        if (isset($distancesCache[$originPid][$destinationPid])) {
            return $distancesCache[$originPid][$destinationPid];
        }
        return null;
    }
    
    /**
     * Adds cached value of distance between origin and destination
     * 
     * @param string $origin Full address or coordinates of origin
     * @param string $destination Full address or coordinates of destination
     * @param float $distance Distance in meters
     */
    protected function addDistanceToCache($origin, $destination, $distance) {
        $reservationName = __METHOD__ . '()';
        try {
            App::reserveProcessing($reservationName);
            $originPid = Str::slugize($origin);
            $destinationPid = Str::slugize($destination);
            $distancesCache = $this->loadDistancesCache();
            $distancesCache[$originPid][$destinationPid] = (float)$distance;
            $this->storeDistancesCache($distancesCache);
            App::unreserveProcessing($reservationName);
        } 
        catch (Exception_App_ProcessingReservationFailure $e) {
            App::catchThrowable($e);
        }
    }
    
    /**
     * Uses google translate API to translate given text.
     * 
     * @param string|array $text Text to translate or an array of such texts.
     * @param string $targetLang Target lang code
     * @param array $options Following are available:
     *      - 'sourceLang' (string) Defalts to App::$lang.
     *      - 'version' (string) Possible values are 'basic' (or 'v2') and 'advanced' (or 'v3).
     *          Defaults to 'basic'.
     * 
     * @return string|array Translated text or an array of translated texts. Texts
     *      are provided under the same keys as in input array.
     * 
     * @throws Exception_Google_TranslateFailure
     */
    public function translate($text, $targetLang, $options = array()) {
        $options = array_merge(array(
            'sourceLang' => App::$lang,
            'version' => 'basic'
        ), $options);
        $options['version'] = strtolower($options['version']);
        if (
            $options['version'] === 'basic'
            || $options['version'] === 'v2'
        ) {
            //*/>
            // see https://packagist.org/packages/google/cloud-translate#v1.13.1
            $GoogleTranslate = new Google\Cloud\Translate\V2\TranslateClient(array(
                'key' => $this->apiKey,
            ));
            $params = array(
                'target' => $targetLang,
                'format' => 'html',
                'model' => 'nmt',
            );
            if ($options['sourceLang']) {
                $params['source'] = $options['sourceLang'];
            }
            if (is_array($text)) {
                $results = null;
                try {
                    $results = $GoogleTranslate->translateBatch(array_values($text), $params);
                } 
                catch (Throwable $e) {
                    throw new Exception_Google_TranslateFailure(
                        $e->getMessage(),
                        $e->getCode(),
                        $e
                    );
                }
                if (
                    !is_array($results)
                    || count($results) !== count($text)
                ) {
                    throw new Exception_Google_TranslateFailure(__e(__FILE__, 'Invalid Google translate API response: %s', json_encode($results)));
                }
                $textKeys = array_keys($text);
                $translatedTexts = array();
                foreach ($results as $key => $result) {
                    if (empty($result['text'])) {
                        throw new Exception_Google_TranslateFailure(__e(__FILE__, 'Invalid Google translate API response (item %s): %s', $key, json_encode($results)));
                    }
                    $translatedTexts[$textKeys[$key]] = $result['text'];
                }
                return $translatedTexts;
            }
            else {
                $result = null;
                try {
                    $result = $GoogleTranslate->translate($text, $params);
                } 
                catch (Throwable $e) {
                    throw new Exception_Google_TranslateFailure(
                        $e->getMessage(),
                        $e->getCode(),
                        $e
                    );
                }
                if (empty($result['text'])) {
                    throw new Exception_Google_TranslateFailure(__e(__FILE__, 'Invalid Google translate API response: %s', json_encode($result)));
                }
                return $result['text'];
            }
            /*/
            $params = array(
                'target' => $targetLang,
                'key' => $this->apiKey,
            );
            if ($options['sourceLang']) {
                $params['source'] = $options['sourceLang'];
            }
            // if text is longer than 5000 characters then split it into chunks by 4000 chars
            // but keep whole sentences.
            $translatedText = '';
            while ($text) {
                if (strlen(rawurlencode($text)) > 5000) {
                    //@todo - use regex /\S\.\s/ to detect end of sentence
                    $splitPosition = strrpos(substr($text, 4000), '.') + 1; 
                    $textChunk = substr($text, 0, $splitPosition);
                    $text = substr($text, $splitPosition);
                }
                else {
                    $textChunk = $text;
                    $text = '';
                }
                $params['q'] = $textChunk;
                $result = $this->requestApi(
                    'https://translation.googleapis.com/language/translate/v2',
                    array(
                        'get' => $params,
                    )
                );
                if (empty($result['data']['translations'][0]['translatedText'])) {
                    throw new Exception_Google_TranslateFailure(__e(__FILE__, 'Invalid response: %s', json_encode($result, JSON_UNESCAPED_UNICODE)));
                }
                $translatedText .= $result['data']['translations'][0]['translatedText'];
            }
            return $translatedText;
            //*/
        }
        elseif (
            $options['version'] === 'advanced'
            || $options['version'] === 'v3'
        ) {
            // to implement see https://packagist.org/packages/google/cloud-translate#v1.13.1
            throw new Exception_Google_TranslateFailure(__e(__FILE__, 'Version "%s" is not implemented yet', $options['version']));
        }
        else {
            throw new Exception_Google_TranslateFailure(__e(__FILE__, 'Invalid version "%s"', $options['version']));
        }
    }
}
class Exception_Google_TranslateFailure extends Exception {}