<?php
/**
 * Paginator class help with implementation of pagination.
 * 
 * It can be used like e.g.:
 * 
 *      //
 *      // IN CONTROLLER (DEPRECATED - see IN MODEL section)
 *      //
 * 
 *      $MyPagedModel = $this->loadModel('MyPagedModel');
 * 
 *      // prepare/get your find options
 *      $findOptions = array(....) 
 * 
 *      // load paginator and create new instance
 *      App::loadVendor('App', 'Paginator'); 
 *      $Paginator = new Paginator(
 *          array(
 *              'limit' => $this->getSetting('pagingLimit'),              // set the paging limit
 *              'parseRequest' => true,                                   // parse paging params in request
 *              'resolveCount' => true,                                   // resolve count of paged records
 *          ),
 *          $MyPagedModel,                                                // this is used to resolve count
 *          $findOptions                                                  // this is used to resolve count
 *      );
 * 
 *      // update find options by paging params ('limit', 'page' and 'order')
 *      $findOptions = $Paginator->getFindOptions($findOptions);
 *      
 *      $records = $MyPagedModel->find($findOptions);
 *      
 *      // load view
 *      return $this->loadView('index', array(
 *          'records' => $records,
 *          'paginator' => $Paginator,
 *      ));
 * 
 * 
 *      //
 *      // IN MODEL
 *      // 
 * 
 *      The above logic from controller can be placed to model. Even it is recommended
 *      to place this logic into model!
 * 
 *      The code will be the same or very similar as in the controller but instead of
 *      $MyPagedModel the $this will be used.
 * 
 *      The Model class itself already implements paging and it can be used like e.g.:
 * 
 *      $records = $MyPagedModel->find(
 *          array(
 *              'conditions' => array(...),
 *              'paginate' => TRUE,
 *              'limit' => $this->getSetting('MyPagedModel.pagingLimit'),
 *          ), 
 *      );
 * 
 *      // load view (if the code is in controller)
 *      return $this->loadView('index', array(
 *          'records' => $records,
 *          'paginator' => $MyPagedModel->Paginator,
 *      ));
 * 
 * 
 *      //
 *      // IN VIEW
 *      //
 * 
 *      // optionaly you can call at first $this->params['Paginator']->setLinksOptions() to customize
 *      <div class="paginator">< ?php echo $this->params['Paginator']->getLinks() ? ></div>
 */
class Paginator {
    
    /**
     * OPTIONS PROPERTIES 
     */
    
    /**
     * Page limit
     * 
     * @var int
     */
    protected $limit = 25;
    
    protected $page = 1;
    
    /**
     * Value of sort URL param
     * 
     * @var string 
     */
    protected $sort = null;
    
    /**
     * Value of filter URL param 
     *
     * @var string 
     */
    protected $filter = null;
    
    /**
     * It is not desirable to put into url
     * sort like /my-products?sort=EshopProduct.name and reveal your structure.
     * Much better is to use some nicks like /my-products?sort=name and
     * then to convert them to proper field names. This is the sort conversions
     * serves to. It is an array of pairs '{fieldNick}' => '{fieldName}', e.g.:
     * 
     *      array(
     *          'name' => 'EshopProduct.name',
     *          'manufacturer_name' => 'EshopManufacturer.name',
     *      )     
     * 
     * @var array
     */
    protected $sortFieldsConversions = array();
        
    protected $direction = 'ASC';
    
    protected $limitUrlParam = 'limit';
    
    protected $pageUrlParam = 'page';
    
    protected $sortUrlParam = 'sort';
    
    protected $directionUrlParam = 'direction';
    
    /**
     * Character used to separate many fields passed by sort param, e.g.:
     *      
     *      /my/url?sort=ModelA.fieldA,ModelB.fieldB DESC
     * 
     * ATTENTION: If Paginator::$sortFieldsSeparator is empty then fields are 
     * supposed to be stored in array structure of sort param:
     * 
     *      /my/url?sort[ModelA.fieldA]=&sort[ModelB.fieldB]=DESC
     * 
     * @var string
     */
    protected $sortFieldsSeparator = null; //';';
    
    /**
     * Character used to separate direction passed together with field in sort param, e.g.:
     *      
     *      /my/url?sort=ModelA.fieldA ASC,ModelB.fieldB DESC
     * 
     * ATTENTION: This property is considered only if Paginator::$sortFieldsSeparator is not empty.
     * 
     * @var string
     */
    protected $sortDirectionSeparator = ' ';
    
    protected $filterUrlParam = 'filter';   
    
    /**
     * Character used to separate many fields passed by filter param, e.g.:
     *      
     *      /my/url?filter=ModelA.fieldA:my value,ModelB.fieldB:2
     * 
     * ATTENTION: This character must be escaped with '\' if used in filtering 
     * expression, e.g.:
     * 
     *      /my/url?filter=ModelA.fieldA:my value with \, (comma),ModelB.fieldB:2
     * 
     * ATTENTION: If Paginator::$filterFieldsSeparator is empty then fields are 
     * supposed to be stored in array structure of filter param:
     * 
     *      /my/url?filter[ModelA.fieldA]=my value&filter[ModelB.fieldB]=2
     * 
     * @var string
     */
    protected $filterFieldsSeparator = null; //';'; 
    
    /**
     * Character used to separate filtering expression of field in filter param, e.g.:
     *      
     *      /my/url?filter=ModelA.fieldA:my value,ModelB.fieldB:2
     * 
     * ATTENTION: This property is considered only if Paginator::$filterFieldsSeparator is not empty.
     * 
     * @var string
     */
    protected $filterExpressionSeparator = ':';
    
    /**
     * Character used as logical AND in filtering expression of field in filter param, e.g.:
     *      
     *      /my/url?filter=ModelA.fieldA:>=0&<50
     * 
     *      or (if Paginator::$filterFieldsSeparator is empty and braces syntax is used):
     * 
     *      /my/url?filter[ModelA.fieldA]=>=0&<50
     * 
     * 
     * @var string
     */
    protected $filterExpressionAnd = '&';
    
    /**
     * Character used as logical OR in filtering expression of field in filter param, e.g.:
     *      
     *      /my/url?filter=ModelA.fieldA:>=0+<50
     * 
     *      or (if Paginator::$filterFieldsSeparator is empty and braces syntax is used):
     * 
     *      /my/url?filter[ModelA.fieldA]=>=0+<50
     * 
     * @var string
     */
    protected $filterExpressionOr = '+';
    
    /**
     * Character used to separate multiple values in filter expression, e.g.:
     * 
     *      /my/url/?filter[ModelA.id]=23;25;78
     * 
     * This can be written also osing OR like:
     * 
     *      /my/url/?filter[ModelA.id]=23+25+78
     * 
     * But there is difference in resulting query. The first url will result into:
     * 
     * ... WHERE `ModelA`.`id` IN (23,25,78) ...
     * 
     * while the second url will result into:
     *
     * ... WHERE `ModelA`.`id` = 23 OR `ModelA`.`id` = 25 OR `ModelA`.`id` = 78 ...
     * 
     * @var string
     */
    protected $filterExpressionValuesSeparator = ';';
    
    protected $resetUrlParam = 'reset';
    
    /**
     * Total count of paged records
     *
     * @var int 
     */
    protected $count = null;
    
    /**
     * Instance of Paginator primary model
     * 
     * @var Model 
     */
    protected $PrimaryModel = null;    
    
    
    /**
     * READONLY PROPERTIES
     */
    
    /**
     * Property Paginator::$sort is parsed into this order definition in format
     * of Model::find() 'order' option.
     * 
     * @var array 
     */
    protected $order = array();
    
    /**
     * Joins generated by resolving order
     * 
     * @var array 
     */
    protected $orderJoins = array();
    
    /**
     * Property Paginator::$filter is parsed into this conditions definition in format
     * of Model::find() 'conditions' option.
     * 
     * @var array 
     */
    protected $conditions = array();
    
    /**
     * Property Paginator::$filter is parsed into this conditions definition in format
     * of Model::find() 'having' option.
     * 
     * @var array 
     */
    protected $having = array();
    
    /**
     * Joins generated by resolving conditions
     * 
     * @var array 
     */
    protected $conditionsJoins = array();
    
    /**
     * Literals conditions generated by resolving conditions
     * 
     * @var array 
     */
    protected $literalsConditions = array();
            
    /**
     * Literals having generated by resolving conditions
     * 
     * @var array 
     */
    protected $literalsHaving = array();
            
    /**
     * Total count of pages
     * 
     * @var int 
     */
    protected $pagesCount = null;
    
    /**
     * If TRUE then rawurlencode() is used for encoding of attached params values 
     * instead of urlencode()
     * 
     * @var bool 
     */
    protected $rawurlencode = true;
    
    /**
     * If TRUE then paginator request options (limit, sort, direction 
     * and filter) are inherited.
     *  
     * @var bool 
     */
    protected $inheritRequestOptions = true;
    
    
    /**
     * INTERNAL PROPERTIES
     */
    
    /**
     * Cache of used models in array like:
     *      array(
     *          'ModelA' => {ModelAInstance},
     *          'ModelB' => {ModelBInstance},
     *          ...,
     *      )
     * 
     * @var array 
     */
    protected $RelatedModels = array();
    
    protected $Pager = null;
    
    protected $pagerOptions = array();
    
    protected $pagerLinks = null;
    
    protected $primaryModelName = null;
            
    /**
     * Creates new paginator instance
     * 
     * @param array $options Following can be used:
     *      - 'parseRequest' (bool) If TRUE then app request is parsed to load paging params on new instance
     *          creation. Defaults to FALSE.
     *      - 'resolveCount' (bool) If TRUE then total count of paged records is resolved on new instance
     *          creation. In such a case $Model and $findOptions must be provided. Defaults to FALSE.
     *      - 'findParams' (array) Optional. Can/should be provided if 'resolveCount' options is TRUE.
     *          Used as arg for Paginator::getCount().
     *      - 'PrimaryModel' (Model) Primary model of Paginator instance. If the 
     *          Paginator instance is intended to use Paginator::getCount(),
     *          and Paginator::getConditions()
     *      - 'inheritRequestOptions' (bool) Should be Paginator options which can
     *          be changet by request GET params (limit, sort, direction, filter) 
     *          inherited? If TRUE then inherited values are loaded from cookie storage.
     *          Defaults to TRUE.
     */
    public function __construct($options = array()) {
        $defaults = array(
            'parseRequest' => false,
            'resolveCount' => false,
            'PrimaryModel' => null,
            'findParams' => array(),
            'inheritRequestOptions' => $this->inheritRequestOptions,
        );
        $hasOptions = !empty($options);
        $options = array_merge($defaults, $options);
        
        $this->inheritRequestOptions = $options['inheritRequestOptions'];
        
        if ($options['parseRequest']) {
            $this->parseRequest($options);
        }
        elseif ($hasOptions) {
            $this->setOptions($options);
        }
        
        if ($options['resolveCount']) {
            $this->getCount($options['findParams']);
        }
    }
    
    /**
     * Merges previous request params with actual one. The merge is done by redirecting
     * to URL containing all params.
     * 
     * Used internally in Paginator::parseRequest()
     * 
     * @param array $requestOptions Array of paginator options contained in request URL
     * 
     * @return void
     */
    protected function inheritRequestOptions($requestOptions) {
        // do nothing if no options are inherited
        if (empty($this->inheritRequestOptions)) {
            return;
        }
        $path = '_app.Paginator.options.';
        if (App::$requestType === 'slug') {
            $path .= 'slug.' . SLUG;
        }
        else {
            $path .= 'mvc.' . App::$module . '.' . App::$controller . '.' . App::$action;
        }
        $inheritedRequestOptions = (array)Arr::getPath($_SESSION, $path);
        if (isset($requestOptions['reset'])) {
            $parsedUrl = App::$parsedUrl;
            if (!empty($parsedUrl['get'])) {
                $parsedUrl['get'] = array_diff_key($parsedUrl['get'], $inheritedRequestOptions, $requestOptions);
            }
            Arr::unsetPath($_SESSION, $path);
            // redirect
            App::redirect(App::getUrl($parsedUrl));
        }
        // if some of inherited request options miss in actual request then 
        // add them to URL and redirect to this new URL containing all options
        $missingOptions = array_diff_key($inheritedRequestOptions, $requestOptions);
        if (!empty($missingOptions)) {
            $requestOptions = array_merge($inheritedRequestOptions, $requestOptions);
            // get new url to redirect here below
            $parsedUrl = Arr::mergeRecursive(App::$parsedUrl, array('get' => $requestOptions));
            // save actual options before redirect (to avoid looping here) but 
            // do not inherit page option and save only nonempty options as empty 
            // options are used in URL just to turn off the option
            unset($requestOptions['page']);
            $requestOptions = array_filter($requestOptions);
            Arr::setPath($_SESSION, $path, $requestOptions);
            // redirect
            App::redirect(App::getUrl($parsedUrl));
        }
        // save actual options if no redirect occurs or resave the same after redirect
        // but do not inherit page option and save only nonempty options as empty 
        // options are used in URL just to turn off the option
        unset($requestOptions['page']);
        $requestOptions = array_filter($requestOptions);
        Arr::setPath($_SESSION, $path, $requestOptions);
    }
    
    /**
     * Parses the paging parameters from url get params
     * 
     * @param array $options Optional. Initial options to merge parsed paging params into.
     *      Defaults to empty array.
     */
    public function parseRequest($options = array()) {
        // get options contained in request
        $requestOptions = array();
        if (isset($_GET[$this->pageUrlParam])) {
            $requestOptions['page'] = $_GET[$this->pageUrlParam];
        }
        if (isset($_GET[$this->limitUrlParam])) {
            $requestOptions['limit'] = $_GET[$this->limitUrlParam];
        }
        if (isset($_GET[$this->sortUrlParam])) {
            $requestOptions['sort'] = $_GET[$this->sortUrlParam];
        }
        if (isset($_GET[$this->directionUrlParam])) {
            $requestOptions['direction'] = $_GET[$this->directionUrlParam];
        }
        if (isset($_GET[$this->filterUrlParam])) {
            $requestOptions['filter'] = $_GET[$this->filterUrlParam];
        }
        if (isset($_GET[$this->resetUrlParam])) {
            $requestOptions['reset'] = $_GET[$this->resetUrlParam];
        }
        // assure that options from previous requests are inherited (added to URL)
        $this->inheritRequestOptions($requestOptions);
        // request options have higher priority than provided options
        $options = array_merge($options, $requestOptions);
        $this->setOptions($options);
    }
    
    public function setOptions($options = array()) {
        if (array_key_exists('limit', $options)) {
            $this->setPropertyLimit($options['limit']);
        }    
        if (array_key_exists('page', $options)) {
            $this->setPropertyPage($options['page']);
        }    
        if (array_key_exists('sort', $options)) {
            $this->setPropertySort($options['sort']);
        }    
        if (array_key_exists('sortFieldsConversions', $options)) {
            if (!$this->setPropertySortFieldsConversions($options['sortFieldsConversions'])) {
                trigger_error('Paginator sort conversions must be an associative or empty array', E_USER_WARNING);
            }
        }    
        if (array_key_exists('direction', $options)) {
            $this->setPropertyDirection($options['direction']);
        }    
        if (array_key_exists('limitUrlParam', $options)) {
            if (!$this->setPropertyLimitUrlParam($options['limitUrlParam'])) {
                trigger_error('Paginator limit Url param must be not empty string', E_USER_WARNING);
            }    
        }    
        if (array_key_exists('pageUrlParam', $options)) {
            if (!$this->setPropertyPageUrlParam($options['pageUrlParam'])) {
                trigger_error('Paginator page Url param must be not empty string', E_USER_WARNING);
            }    
        }    
        if (array_key_exists('sortUrlParam', $options)) {
            if (!$this->setPropertySortUrlParam($options['sortUrlParam'])) {
                trigger_error('Paginator sort Url param must be not empty string', E_USER_WARNING);
            }
        }    
        if (array_key_exists('directionUrlParam', $options)) {
            if (!$this->setPropertyDirectionUrlParam($options['directionUrlParam'])) {
                trigger_error('Paginator direction Url param must be not empty string', E_USER_WARNING);
            }
        }    
        if (array_key_exists('filterUrlParam', $options)) {
            if (!$this->setPropertyFilterUrlParam($options['filterUrlParam'])) {
                trigger_error('Paginator filter Url param must be not empty string', E_USER_WARNING);
            }
        }    
        if (array_key_exists('resetUrlParam', $options)) {
            if (!$this->setPropertyResetUrlParam($options['resetUrlParam'])) {
                trigger_error('Paginator reset Url param must be not empty string', E_USER_WARNING);
            }
        }    
        if (array_key_exists('count', $options)) {
            if (!$this->setPropertyCount($options['count'])) {
                trigger_error('Paginator count must be an integer number greater than zero or NULL', E_USER_WARNING);
            }
        }    
        if (array_key_exists('PrimaryModel', $options)) {
            if (!$this->setPropertyPrimaryModel($options['PrimaryModel'])) {
                trigger_error('Primary model must be an instance of Model class', E_USER_WARNING);
            }
        }    
        if (array_key_exists('filter', $options)) {
            $this->setPropertyFilter($options['filter']);
        }    
        if (array_key_exists('inheritRequestOptions', $options)) {
            $this->inheritRequestOptions = (bool)$options['inheritRequestOptions'];
        }    
    }
    
    public function resetOptions($options = array()) {
        $defaults = array(
            'limit' => 25,
            'page' => 1,
            'sort' => null,
            'filter' => null,
            'sortFieldsConversions' => array(),
            'direction' => null,
            'limitUrlParam' => 'limit',
            'pageUrlParam' => 'page',
            'sortUrlParam' => 'sort',
            'directionUrlParam' => 'direction',
            'filterUrlParam' => 'filter',
            'resetUrlParam' => 'reset',
            'count' => null,
        );
        $options = array_merge($defaults, $options);
        $this->setOptions($options);
    }
    
    /**
     * Gets pagination options
     * 
     * @param array $which Optional. List of options to be returned
     * @return array
     */
    public function getOptions($which = array()) {
        $options = array(
            'limit' => $this->limit,
            'page' => $this->page,
            'sort' => $this->sort,
            'filter' => $this->filter,
            'sortFieldsConversions' => $this->sortFieldsConversions,
            'direction' => $this->direction,
            'limitUrlParam' => $this->limitUrlParam,
            'pageUrlParam' => $this->pageUrlParam,
            'sortUrlParam' => $this->sortUrlParam,
            'directionUrlParam' => $this->directionUrlParam,
            'sortFieldsSeparator' => $this->sortFieldsSeparator,
            'sortDirectionSeparator' => $this->sortDirectionSeparator,
            'filterUrlParam' => $this->filterUrlParam,
            'filterFieldsSeparator' => $this->filterFieldsSeparator,
            'filterExpressionSeparator' => $this->filterExpressionSeparator,
            'filterExpressionOr' => $this->filterExpressionOr,
            'resetUrlParam' => $this->resetUrlParam,
            'count' => $this->count,
            'order' => $this->order,
            'orderJoins' => $this->orderJoins,
            'conditions' => $this->conditions,
            'having' => $this->having,
            'conditionsJoins' => $this->conditionsJoins,
            'literalsConditions' => $this->literalsConditions,
            'pagesCount' => $this->pagesCount,
            'inheritRequestOptions' => $this->inheritRequestOptions,
        );
        if ($which) {
            $options = array_intersect_key($options, array_flip($which));
        }
        return $options;
    }
    
    /**
     * 
     * @param array $findOptions
     * @return array
     */
    public function getFindOptions($findOptions = array()) {
        // explicit offset breaks paging so unset it if exits in provided find options 
        unset($findOptions['offset']);
        $findOptions['limit'] = $this->limit;
        $findOptions['page'] = $this->page;
        // prepare existing joins in case that there are joins in find options and 
        // some order or conditions joins are defined too
        $existingJoins = array();
        if (
            !empty($findOptions['joins'])
            && (
                $this->orderJoins 
                || $this->conditionsJoins
            )
        ) {
            // avoid to define the same join twice
            foreach ($findOptions['joins'] as $join) {
                $joinPid = $this->getJoinPid($join);
                if ($joinPid) {
                    $existingJoins[$joinPid] = true;
                }
            }
        }
        // merge order
        if ($this->order) {
            if (!empty($findOptions['order'])) {
                $findOptions['order'] = array_merge($this->order, (array)$findOptions['order']);
            }
            else {
                $findOptions['order'] = $this->order;
            }
        }
        // merge order joins
        if ($this->orderJoins) {
            if (empty($findOptions['joins'])) {
                $findOptions['joins'] = $this->orderJoins;
            }
            else {
                foreach ($this->orderJoins as $join) {
                    $joinPid = $this->getJoinPid($join);
                    if (!isset($existingJoins[$joinPid])) {
                        $findOptions['joins'][] = $join;
                        // actualize existing joins for merging conditionsJoins 
                        // here below
                        $existingJoins[$joinPid] = true;
                    }
                }
            }
        }
        // merge conditions
        if ($this->conditions) {
            if (empty($findOptions['conditions'])) {
                $findOptions['conditions'] = array();
            }           
            else {
                $findOptions['conditions'] = DB::nestConditions($findOptions['conditions']);
            }
            // enclose the filter conditions into parenthesis (to avoid conflicts 
            // with existing or later added conditions)
            $findOptions['conditions'][] = $this->conditions;
        }
        // merge having
        if ($this->having) {
            if (empty($findOptions['having'])) {
                $findOptions['having'] = array();
            }           
            // enclose the filter conditions into parenthesis (to avoid conflicts 
            // with existing or later added conditions)
            $findOptions['having'][] = $this->having;
        }
        // merge literals conditions
        if ($this->literalsConditions) {
            if (empty($findOptions['literals']['conditions'])) {
                $findOptions['literals']['conditions'] = array();
            }           
            // enclose the filter conditions into parenthesis (to avoid conflicts 
            // with existing or later added conditions)
            $findOptions['literals']['conditions'] = array_merge($findOptions['literals']['conditions'], $this->literalsConditions);
        }
        // merge literals having
        if ($this->literalsHaving) {
            if (empty($findOptions['literals']['having'])) {
                $findOptions['literals']['having'] = array();
            }           
            // enclose the filter conditions into parenthesis (to avoid conflicts 
            // with existing or later added having)
            $findOptions['literals']['having'] = array_merge($findOptions['literals']['having'], $this->literalsHaving);
        }
        // merge conditions joins
        if ($this->conditionsJoins) {
            if (empty($findOptions['joins'])) {
                $findOptions['joins'] = $this->conditionsJoins;
            }
            else {
                foreach ($this->conditionsJoins as $join) {
                    $joinPid = $this->getJoinPid($join);
                    if (!isset($existingJoins[$joinPid])) {
                        $findOptions['joins'][] = $join;
                    }
                }
            }
        }
        return $findOptions;
    }
    
    /**
     * Resolves join pid for join defined by 'model' syntax
     * 
     * @param array $join Join array to create pid for, e.g. array('module' => 'App', 'model' => 'User', 'type' => 'left)
     * @return string Join pid, e.g. 'App.User.left'.
     */
    protected function getJoinPid($join) {
        $joinPid = '';
        if (!empty($join['module'])) {
            $joinPid .= $join['module'] . '.';
        }
        else {
            $joinPid .= $this->PrimaryModel->getPropertyModule() . '.';
        }
        if (!empty($join['alias'])) {
            $joinPid .= $join['alias'] . '.';
        }
        elseif (!empty($join['model'])) {
            $joinPid .= $join['model'] . '.';
        }
        if (!empty($join['type'])) {
            $joinPid .= $join['type'];
        }
        else {
            $joinPid = rtrim($joinPid, '.');
        }
        return $joinPid;
    }
            
    /**
     * Sets paginator limit
     * . 
     * @param int|bool $limit Paginator page size. If FALSE then page has no limit
     *      and paginator is used only for filtering and sorting.
     */
    public function setPropertyLimit($limit) {
        if (
            Validate::intNumber($limit)
            && $limit > 0
        ) {
            $this->limit = $limit;
        }
        else {
            $this->limit = PHP_INT_MAX;
        }
        $this->getPagesCount();
    }
    
    public function getPropertyLimit() {
        return $this->limit;
    }
    
    public function setPropertyPage($page) {
        if(
            Validate::intNumber($page)
            && $page > 0
        ) {
            $this->page = $page;
        }
        if ($this->page > 1) {
            App::setSeoIndex(false);
        }
    }
       
    public function getPropertyPage() {
        return $this->page;
    }
    
    /**
     * Returns offset for actual page and limit
     * 
     * @return int
     */
    public function getOffset() {
        return ($this->page - 1) * $this->limit;
    }
    
    public function setPropertySort($sort) {
        if (
            !empty($sort)
            && (
                is_string($sort)
                || is_array($sort)
            )
        ) {
            $this->sort = $sort;
        }
        else {
            $this->sort = null;
        }
        $this->getOrder();
    }
    
    public function getPropertySort() {
        return $this->sort;
    }
    
    public function setPropertySortFieldsConversions($conversions) {
        $conversions = (array)$conversions;
        if (
            $conversions === array()
            || Validate::assocArray($conversions)
        ) {
            $this->sortFieldsConversions = $conversions;
            $this->getOrder();
            return true;
        }
        return false;
    }
        
    public function setPropertyDirection($direction) {
        $direction = strtoupper($direction);
        if ($direction !== 'ASC' && $direction !== 'DESC') {
            $direction = 'ASC';
        }
        $this->direction = $direction;
        $this->getOrder();
    }
    
    public function setPropertyLimitUrlParam($name) {
        if (!empty($name) && is_string($name)) {
            $this->limitUrlParam = Str::slugize($name);
            return true;
        }
        return false;
    }

    public function setPropertyPageUrlParam($name) {
        if (!empty($name) && is_string($name)) {
            $this->pageUrlParam = Str::slugize($name);
            return true;
        }
        return false;
    }

    public function getPropertyPageUrlParam() {
        return $this->pageUrlParam;
    }
    
    public function setPropertySortUrlParam($name) {
        if (!empty($name) && is_string($name)) {
            $this->sortUrlParam = Str::slugize($name);
            return true;
        }
        return false;
    }
    
    public function getPropertySortUrlParam() {
        return $this->sortUrlParam;
    }
    
    public function setPropertyDirectionUrlParam($name) {
        if (!empty($name) && is_string($name)) {
            $this->directionUrlParam = Str::slugize($name);
            return true;
        }
        return false;
    }
    
    public function getPropertySortFieldsSeparator() {
        return $this->sortFieldsSeparator;
    }
    
    public function getPropertySortDirectionSeparator() {
        return $this->sortDirectionSeparator;
    }
    
    public function setPropertyFilterUrlParam($name) {
        if (!empty($name) && is_string($name)) {
            $this->filterUrlParam = Str::slugize($name);
            return true;
        }
        return false;
    }
    
    public function getPropertyFilterFieldsSeparator() {
        return $this->filterFieldsSeparator;
    }
    
    public function getPropertyFilterExpressionSeparator() {
        return $this->filterExpressionSeparator;
    }
    
    public function getPropertyFilterUrlParam() {
        return $this->filterUrlParam;
    }
    
    public function setPropertyResetUrlParam($name) {
        if (!empty($name) && is_string($name)) {
            $this->resetUrlParam = Str::slugize($name);
            return true;
        }
        return false;
    }
    
    public function getPropertyResetUrlParam() {
        return $this->resetUrlParam;
    }
    
    public function setPropertyCount($count) {
        if (
            $count === null
            || (
                Validate::intNumber($count)
                && $count > 0
            )
        ) {
            $this->count = $count;
            $this->getPagesCount();
            return true;
        }
        return false;
    }
    
    public function getPropertyCount() {
        return $this->count;
    }
        
    public function setPropertyPrimaryModel($PrimaryModel) {
        if ($PrimaryModel instanceof Model) {
            $this->PrimaryModel = $PrimaryModel;
            $this->primaryModelName = $this->PrimaryModel->getPropertyName();
            return true;
        }
        return false;
    }
    
    public function setPropertyFilter($filter) {
        if (
            !empty($filter)
            && (
                is_string($filter)
                || is_array($filter)
            )
        ) {
            $this->filter = $filter;
        }
        else {
            $this->filter = null;
        }
        $this->getConditions();            
    }
    
    public function getPropertyFilter() {
        return $this->filter;
    }
    
    /**
     * Resolves total count of records of provided Model for provided find options
     * 
     * @param array $findOptions Optional. Find options to get total count fo records for.
     * @param Model $PrimaryModel Optional. Primary model to resolve count for. 
     *      Defaults to Paginator::$PrimaryModel.
     * 
     * @return int Total count of records of provided Model for given find options
     */
    public function getCount($findOptions = array(), Model $PrimaryModel = null) {
        if (!$PrimaryModel) {
            $PrimaryModel = $this->PrimaryModel;
        }
        if (!$PrimaryModel) {
            throw new Exception ('Invalid Model');
        }
        // turn off pagination to avoid infinite loop in Model::find()
        $findOptions['paginate'] = false;
        
        $count = $PrimaryModel->findCount($findOptions);
        $this->setPropertyCount($count);
        return $count;
    }
    
    protected function getPagesCount() {
        if (
            Validate::intNumber($this->count) && $this->count > 0
            && Validate::intNumber($this->limit) && $this->limit > 0
        ) {
            $this->pagesCount = ceil((float)$this->count/(float)$this->limit);
        }
        else {
            $this->pagesCount = null;
        }
        return $this->pagesCount;
    }
    
    /**
     * Parses order provided through sort GET param in URL into find options.
     * 
     * The general syntax of order GET param in URL is:
     * 
     *      sort[{fieldName01}]=[asc|desc]&sort[{fieldName02}]=[asc|desc]&...
     * 
     * where:
     *      - {fieldNameXY} can be a nonqualified or qualified field name. If nonqualified 
     *          field name is used then it is considered to be field of Paginator::$PrimaryModel.
     *          Qualified field name can contain also module qualifier and also "join to" specifications.
     *          E.g. 'name', 'EshopAuthor.name', 'Eshop.EshopAuthor.name', 'EshopProductAuthor>EshopAuthor.name'
     *      
     * @throws Exception
     */
    protected function getOrder() {
        $PrimaryModel = $this->PrimaryModel;
        if (!$PrimaryModel) {
            throw new Exception ('Invalid Model');
        }
        // - if Paginator::$sortFieldsSeparator is defined then get fields by parsing param value 
        if (!empty($this->sortFieldsSeparator)) {
            $sortParts = explode($this->sortFieldsSeparator, $this->sort);
        }
        // - if Paginator::$sortFieldsSeparator is not defined then fields match with $this->sort array
        else {
            $sortParts = (array)$this->sort;
            $sortParts = Arr::deflate($sortParts, array('separator' => '.', 'ignorePlain' => false));
        }
        $this->order = array();
        $this->orderJoins = array();
        foreach ($sortParts as $k => $v) {
            // get field name and direction
            // - if Paginator::$sortFieldsSeparator is defined then get them by parsing param value 
            if (
                !empty($this->sortFieldsSeparator)
                || is_int($k)
            ) {
                $fieldParts = explode($this->sortDirectionSeparator, $v);
                $field = array_shift($fieldParts);
                $direction = array_shift($fieldParts);
            }
            // - if Paginator::$sortFieldsSeparator is not defined then key is field name
            // and value is direction
            else {
                $field = $k;
                $direction = $v;
            }
            if (!empty($this->sortFieldsConversions[$field])) {
                $field = $this->sortFieldsConversions[$this->sort];
            }
            if ($direction !== 'ASC' && $direction !== 'DESC') {
                $direction = $this->direction;
            }
            list($fieldName, $modelName, $moduleName, $joins, $Model) = $this->parseFieldName($field);
            $this->orderJoins = array_merge($this->orderJoins, $joins);
            $schema = $Model->getPropertySchema();
            // check if field is in schema; if not (virtualField), then do not use model qualification
            $virtualField = !isset($schema[$fieldName]);
            // enclose field
            if ($virtualField) {
                $this->order[] = DB::encloseName($fieldName) . ' ' . $direction;
            }
            else {
                $this->order[] = DB::encloseName($modelName . '.' . $fieldName) . ' ' . $direction;
            }
            
        }
        return $this->order;
    }
    
    /**
     * Parses conditions provided through filter GET param in URL into find options.
     * 
     * The general syntax of filter GET param in URL is:
     * 
     *      filter[{fieldName01}]={fieldConditions01}&filter[{fieldName02}]={fieldConditions02}&...
     * 
     * where:
     *      - {fieldNameXY} can be a nonqualified or qualified field name. If nonqualified 
     *          field name is used then it is considered to be field of Paginator::$PrimaryModel.
     *          Qualified field name can contain also module qualifier and also "join to" specifications.
     *          E.g. 'name', 'EshopAuthor.name', 'Eshop.EshopAuthor.name', 'EshopProductAuthor>EshopAuthor.name'
     * 
     *      - {fieldConditionsXY} single condition or many conditions joined by logical operators.
     *          Each of conditions has following syntax: {comparisonOperator}{value}.
     *          Possible comparison operators are: '' (LIKE), '=', '!=', '!' (NOT), '>', '<',
     *          '>=', '<='. Operators are interpereted according field type. The {value}
     *          can be any possibele field value to compare against or 'NULL'.
     *          Possibly it can contain one or more asterixes ('*') which are interpreted like '%' 
     *          for sql LIKE but only if comparison operator is '' or '!'. For other
     *          operators there are taken literarily.
     *      
     * E.g. to select all records WHERE (language IS NULL OR language LIKE 'slove%') AND created > '2015-01-01'
     * use foloving GET params in URL: ?filter[language]=NULL+slove*&filter[created]=>2015-01-01
     * 
     * @throws Exception
     */
    protected function getConditions() {
        $PrimaryModel = $this->PrimaryModel;
        if (!$PrimaryModel) {
            throw new Exception ('Invalid Model');
        }
        $primaryModelName = $PrimaryModel->getPropertyName();
        $primaryModuleName = $PrimaryModel->getPropertyModule();
        $orLength = strlen($this->filterExpressionOr);
        $andLength = strlen($this->filterExpressionAnd);
        $escChr = chr(27);
        // get fields
        // - if Paginator::$filterFieldsSeparator is defined then get fields by parsing param value 
        if (!empty($this->filterFieldsSeparator)) {
            //replace all escaped field separators
            $filter = str_replace('\\' . $this->filterFieldsSeparator, $escChr, $this->filter);
            // get filter fields
            $filterFields = explode($this->filterFieldsSeparator, $filter);
        }
        // - if Paginator::$filterFieldsSeparator is not defined then filterFields match with $this->filter array
        else {
            $filterFields = (array)$this->filter;
            $filterFields = Arr::deflate($filterFields, array('separator' => '.', 'ignorePlain' => false));
        }
        $this->having = array();
        $this->conditions = array();
        $this->literalsHaving = array();
        $this->literalsConditions = array();
        $this->conditionsJoins = array();
        foreach ($filterFields as $k => $v) {
            // get field name and expression
            // - if Paginator::$filterFieldsSeparator is defined then get them by parsing param value 
            if (!empty($this->filterFieldsSeparator)) {
                // replace escaped field separators back
                $filterField = str_replace($escChr, $this->filterFieldsSeparator, $v);
                $fieldParts = explode($this->filterExpressionSeparator, $filterField);
                $field = array_shift($fieldParts);
                $expression = implode($this->filterExpressionSeparator, $fieldParts);
            }
            // - if Paginator::$filterFieldsSeparator is not defined then key is field name
            // and value is expression
            else {
                $field = $k;
                $expression = (string)$v;
            }
            $expression = trim($expression);
            if (
                $field === '' 
                || $expression === ''
            ) {
                continue;
            }
            
            list($fieldName, $modelName, $moduleName, $joins, $Model) = $this->parseFieldName($field);
            $isPrimaryModel = $primaryModuleName . ' . ' . $primaryModelName === $moduleName . '.' . $modelName;
            $this->conditionsJoins = array_merge($this->conditionsJoins, $joins);
            $schema = $Model->getPropertySchema();
            // check if field is in schema; if not (virtualField), then put field into having instead of conditions
            $isVirtualField = !isset($schema[$fieldName]);
            $fieldType = empty($schema[$fieldName]['type']) ? null : strtoupper($schema[$fieldName]['type']);
            $isNullableField = $this->PrimaryModel->isNullableField($fieldName, $schema);
            // enclose field
            if ($isVirtualField) {
                $solvedField = DB::encloseName($fieldName);
            }
            elseif ($moduleName === $primaryModuleName) {
                $solvedField = DB::encloseName($modelName . '.' . $fieldName);
            }
            else {
                $solvedField = DB::encloseName($moduleName . '.' . $modelName . '.' . $fieldName);
            }
            
            // check for field logical operator preceeding the comparison operator (fieldA=56,fieldB+=23)
            $logicalOperator = 'AND';
            if (substr($expression, 0, $orLength) === $this->filterExpressionOr) {
                $expression = ltrim(substr($expression, $orLength));
                $logicalOperator = 'OR';
            }
            if (substr($expression, 0, $andLength) === $this->filterExpressionAnd) {
                $expression = ltrim(substr($expression, $andLength));
                $logicalOperator = 'AND';
            }
            $conditions = array();
            // parse the field expression (we are exploding here only non-empty 
            // expressions - see the above condition)
            $orParts = explode($this->filterExpressionOr, $expression);
            foreach ($orParts as $orKey => $orPart) {
                $andParts = explode($this->filterExpressionAnd, $orPart);
                $addToLiterals = false;
                foreach ($andParts as $andKey => $value) {
                    $value = trim($value);
                    // separate and normalize operator and value
                    $operator = '';
                    if (
                        substr($value, 0, 2) === '>='
                        || substr($value, 0, 2) === '=>'
                    ) {
                        $value = ltrim(substr($value, 2));
                        $operator = '>= ';
                    }
                    elseif (
                        substr($value, 0, 2) === '<='
                        || substr($value, 0, 2) === '=<'
                    ) {
                        $value = ltrim(substr($value, 2));
                        $operator = '<= ';
                    }
                    elseif (
                        substr($value, 0, 2) === '<>'
                        || substr($value, 0, 2) === '><'
                        || substr($value, 0, 2) === '!='
                        || substr($value, 0, 2) === '=!'
                    ) {
                        $value = ltrim(substr($value, 2));
                        $operator = '!= ';
                    }
                    elseif (
                        substr($value, 0, 1) === '!'
                    ) {
                        $value = ltrim(substr($value, 1));
                        $operator = 'NOT ';
                    }
                    elseif (substr($value, 0, 1) === '>') {
                        $value = ltrim(substr($value, 1));
                        $operator = '> ';
                    }
                    elseif (substr($value, 0, 1) === '<') {
                        $value = ltrim(substr($value, 1));
                        $operator = '< ';
                    }
                    elseif (substr($value, 0, 1) === '=') {
                        $value = ltrim(substr($value, 1));
                        $operator = '= ';
                    }
                    // check for multiple values but only for equal/notequal operators
                    if (
                        (
                            $operator === '' 
                            || $operator === '= ' 
                            || $operator === '!= '
                            || $operator === 'NOT '
                        )
                        && strpos($value, $this->filterExpressionValuesSeparator) !== false
                    ) {
                        //preserve all escaped values separators
                        $value = str_replace('\\' . $this->filterExpressionValuesSeparator, $escChr, $value);
                        if (strpos($value, $this->filterExpressionValuesSeparator) !== false) {
                            // get multiple values
                            $value = explode($this->filterExpressionValuesSeparator, $value);
                            foreach ($value as &$subValue) {
                                $subValue = str_replace($escChr, $this->filterExpressionValuesSeparator, $subValue);
                            }
                            unset($subValue);
                        }
                    }
                    // escape value
                    if (is_array($value)) {
                        $value = array_map(array('DB', 'escape'), $value);
                    }
                    else {
                        $value = DB::escape($value);
                    }
                    // create condition
                    if (is_array($value)) {
                        $value = '\'' . implode('\',\'', $value) . '\'';
                        if (
                            $operator === '' 
                            || $operator === '= ' 
                        ) {
                            $value = 'IN (' . $value . ')';
                        }
                        else {
                            $value = 'NOT IN (' . $value . ')';
                        }
                    }
                    elseif (
                        $value === 'NULL'
                        && (
                            $operator === ''
                            || $operator === 'NOT '
                        )
                    ) {
                        if ($operator === '') {
                            $value = 'IS NULL';
                        }
                        else {
                            $value = 'IS NOT NULL';
                        }
                    }
                    elseif (
                        strpos($value, '*') !== false
                        && (
                            $operator === ''
                            || $operator === 'NOT '
                        )
                    ) {
                        $value = str_replace('*', '%', $value);
                        $value = $operator . 'LIKE \'' . $value . '\'';
                    }
                    elseif (
                        $fieldType === null
                    ) {
                        if ($operator === '') {
                            $value = 'LIKE \'%' . $value . '%\'';
                        }
                        else {
                            if ($operator === 'NOT ') {
                                $operator = '!= ';
                            }
                            $value = $operator . '\'' . $value . '\'';
                        }
                    }
                    // for all NULLable numeric columns treat empty string value as NULL
                    elseif (
                        $value === ''
                        && (
                            $isNullableField
                            // in case of non primary model the value is NULL 
                            // if there is no joined record. 
                            || !$isPrimaryModel
                        )
                        && (
                            $fieldType === 'INTEGER'
                            || $fieldType === 'INT'
                            || $fieldType === 'BOOL'
                            || $fieldType === 'BOOLEAN'
                            || substr($fieldType, -3) === 'INT'
                            || $fieldType === 'FLOAT'
                            || $fieldType === 'DOUBLE'
                            || $fieldType === 'REAL'
                            || $fieldType === 'DECIMAL'
                            || $fieldType === 'NUMERIC'
                            || $fieldType === 'DATE'
                            || $fieldType === 'TIME'
                            || $fieldType === 'DATETIME'
                        )
                    ) {
                        if (
                            $operator === ''
                            || $operator === '= '
                        ) {
                            $value = 'IS NULL';
                            if (!$isNullableField) {
                                $value = array(
                                    $value,
                                    'OR',
                                    '= \'\''
                                );
                            }
                        }
                        elseif (
                            $operator === '!= '
                            || $operator === 'NOT '
                        ) {
                            $value = 'IS NOT NULL';
                            if (!$isNullableField) {
                                $value = array(
                                    $value,
                                    '!= \'\''
                                );
                            }
                        }
                        else {
                            $value = $operator . 'NULL';
                            if (!$isNullableField) {
                                $value = array(
                                    $value,
                                    $operator . '\'\''
                                );
                            }
                            $addToLiterals = true;
                        }
                    }
                    elseif (
                        $fieldType === 'INTEGER'
                        || $fieldType === 'INT'
                        || $fieldType === 'BOOL'
                        || $fieldType === 'BOOLEAN'
                        || substr($fieldType, -3) === 'INT'
                    ) {
                        if ($operator === '') {
                            $operator = '= ';
                        }
                        elseif ($operator === 'NOT ') {
                            $operator = '!= ';
                        }
                        if (Validate::intNumber($value)) {
                            $value = $operator . (int)$value;
                        }
                        else {
                            $value = $operator . '\'' . $value . '\'';
                        }
                    }
                    elseif (
                        $fieldType === 'FLOAT'
                        || $fieldType === 'DOUBLE'
                        || $fieldType === 'REAL'
                        || $fieldType === 'DECIMAL'
                        || $fieldType === 'NUMERIC'
                    ) {
                        if ($operator === '') {
                            $operator = '= ';
                        }
                        elseif ($operator === 'NOT ') {
                            $operator = '!= ';
                        }
                        $value = str_replace(',', '.', $value);
                        if (is_numeric($value)) {
                            $value = $operator . (float)$value;
                        }
                        else {
                            $value = $operator . '\'' . $value . '\'';
                        }
                    }
                    elseif ($fieldType === 'DATE') {
                        if ($operator === '') {
                            $operator = '= ';
                        }
                        elseif ($operator === 'NOT ') {
                            $operator = '!= ';
                        }
                        $value = date('Y-m-d', strtotime($value));
                        $value = $operator . '\'' . $value . '\'';
                    }
                    elseif ($fieldType === 'TIME') {
                        if ($operator === '') {
                            $operator = '= ';
                        }
                        elseif ($operator === 'NOT ') {
                            $operator = '!= ';
                        }
                        $value = date('H:i:s', strtotime($value));
                        $value = $operator . '\'' . $value . '\'';
                    }
                    elseif ($fieldType == 'DATETIME') {
                        if ($operator === '') {
                            $operator = '= ';
                        }
                        elseif ($operator === 'NOT ') {
                            $operator = '!= ';
                        }
                        if (strpos($value, ':')) {
                            $value = date('Y-m-d H:i:s', strtotime($value));
                        }
                        else {
                            $value = date('Y-m-d', strtotime($value));
                            $solvedField = 'DATE_FORMAT(' . $solvedField . ', \'%Y-%m-%d\')';                            
                            $addToLiterals = true;
                        }
                        $value = $operator . '\'' . $value . '\'';
                    }
                    else { // VARCHAR, CHAR, TEXT and all others types not named above
                        if (
                            $value === ''
                            && (
                                $isNullableField 
                                // in case of non primary model the value is NULL 
                                // if there is no joined record. 
                                || !$isPrimaryModel 
                            )
                        ) {
                            if (
                                $operator === ''
                                || $operator === '= '
                            ) {
                                $value = 'IS NULL';
                                if (!$isNullableField) {
                                    $value = array(
                                        $value,
                                        'OR',
                                        'LIKE \'\''
                                    );
                                }
                            }
                            elseif (
                                $operator === '!= '
                                || $operator === 'NOT '
                            ) {
                                $value = 'IS NOT NULL';
                                if (!$isNullableField) {
                                    $value = array(
                                        $value,
                                        'NOT LIKE \'\''
                                    );
                                }
                            }
                            else {
                                $value = $operator . 'NULL';
                                if (!$isNullableField) {
                                    $value = array(
                                        $value,
                                        $operator . '\'\''
                                    );
                                }
                                $addToLiterals = true;
                            }
                        }
                        elseif ($operator === '') {
                            $value = 'LIKE \'%' . $value . '%\'';
                        }
                        elseif ($operator === '= ') {
                            $value = 'LIKE \'' . $value . '\'';
                        }
                        elseif ($operator === 'NOT ') {
                            $value = 'NOT LIKE \'%' . $value . '%\'';
                        }
                        else {
                            $value = $operator . '\'' . $value . '\'';
                        }
                    }
                    if (is_array($value)) {
                        $condition = array();
                        $literal = array();
                        foreach ($value as $subValue)  {
                            if ($subValue === 'OR' || $subValue === 'AND') {
                                $condition[] = $subValue;
                            }
                            else {
                                $condition[] = $solvedField . ' ' . $subValue;
                                $literal[] = $solvedField . ' ' . $subValue;
                            }
                        }
                    }
                    else {
                        $condition = $solvedField . ' ' . $value;
                        $literal = array($condition);
                    }
                    $andParts[$andKey] = $condition;
                    if ($addToLiterals) {
                        if ($isVirtualField) {
                            $this->literalsHaving = array_merge($this->literalsHaving, $literal);
                        }
                        else {
                            $this->literalsConditions = array_merge($this->literalsConditions, $literal);
                        }
                    }
                }
                if (count($andParts) > 1) {
                    $orParts[$orKey] = $andParts;
                }
                else {
                    $orParts[$orKey] = $andParts[0];
                }
            }
            if (count($orParts) > 1) {
                $conditions = array();
                foreach($orParts as $orPart) {
                    if (!empty($conditions)) {
                        $conditions[] = 'OR';
                    }
                    $conditions[] = $orPart;
                }
            }
            else {
                $conditions = $orParts[0];
            }
            if ($conditions) {
                // add logical OR if specified
                if ($logicalOperator === 'OR') {
                    if ($isVirtualField) {
                        $this->having[] = 'OR';
                    } 
                    else {
                        $this->conditions[] = 'OR';
                    }
                }
                // put to having or to conditions
                if ($isVirtualField) {
                    $this->having[] = $conditions;
                } 
                else {
                    $this->conditions[] = $conditions;
                }
            }
        }
    }
    
    /**
     * Parses provided $fieldName into array containing info about field in form like:
     * 
     *      array(
     *          'field' => ...,
     *          'model' => ...,
     *          'module' => ...,
     *          'joins' => ...,
     *          'Model' => ...,
     *      )
     * 
     * NOTE: Used in Paginator::getConditions() and ::getOrder()
     * 
     * @param string $fieldName
     * 
     * @return array Array described here above. You can also use list().
     */
    protected function parseFieldName ($fieldName) {
        $primaryModel = $this->PrimaryModel->getPropertyName();
        $primaryModule = $this->PrimaryModel->getPropertyModule();
        // separate joins and fieldName
        $joinModels = explode('>', $fieldName);
        $fieldName = array_pop($joinModels);
        // get field data
        $fieldParts = explode('.', $fieldName);
        $field = array_pop($fieldParts);
        $fieldModel = array_pop($fieldParts);
        $fieldModule = array_pop($fieldParts);
        if (!$fieldModel) {
            $fieldModel = $primaryModel;
        }
        if (!$fieldModule) {
            $fieldModule = $primaryModule;
        }
        // get joins data
        $joins = array();
        $joinToModel = null;
        $joinToModule = null;
        foreach ($joinModels as $joinModel) {
            $joinModelParts = explode('.', $joinModel);
            $joinModel = array_pop($joinModelParts);
            $joinModule = array_pop($joinModelParts);
            if (!$joinModel) {
                $joinModel = $primaryModel;
            }
            if (!$joinModule) {
                $joinModule = $primaryModule;
            }
            $joins[] = array(
                'model' => $joinModel,
                'module' => $joinModule,
                'type' => 'left',
                'toModel' => $joinToModel,
                'toModule' => $joinToModule,
            );
            $joinToModel = $joinModel;
            $joinToModule = $joinModule;
        }
        // get field Model and add field joins
        if ($fieldModule . '.' . $fieldModel === $primaryModule . '.' . $primaryModel) {
            $Model = $this->PrimaryModel;
        }
        else {
            $Model = App::loadModel($fieldModule, $fieldModel, true);
            $joins[] = array(
                'model' => $fieldModel,
                'module' => $fieldModule,
                'type' => 'left',
                'toModel' => $joinToModel,
                'toModule' => $joinToModule,
            );
        }
        
        return array(
            $field,
            $fieldModel,
            $fieldModule,
            $joins,
            $Model,
            'field' => $field,
            'model' => $fieldModel,
            'module' => $fieldModule,
            'joins' => $joins,
            'Model' => $Model,
        );
    }
    
    /**
     * VIEW METHODS
     */
    
    /**
     * Sets paginator links options
     * 
     * @param array $options See http://pear.php.net/manual/en/package.html.pager.factory.php
     *      for list of all options. NOTE: Following options should not be changed:
     *      'perPage', 'totalItems', 'httpMethod', 'importQuery', 'currentPage', 
     *      'urlVar', 'path', 'fileName', 'fixFileName', 'append', 'excludeVars'.
     */
    public function setLinksOptions($options = array()) {
        $defaults = array(
            // this should be not changed
            'perPage' => $this->limit,
            'totalItems' => $this->count,
            'httpMethod' => 'GET',
            'importQuery' => true,
            'currentPage' => $this->page,
            'urlVar' => $this->pageUrlParam,
            'path' => URL_PATH,
            'fileName' => '',
            'fixFileName' => false,
            'append' => true,
            'excludeVars' => array('_urlPath_'),
            // this can be tuned up
            'mode' => 'Sliding',
            'delta' => 4, // Number of page numbers to display before and after the current one.
            'linkClass' => '-run-pg-link',
            'altFirst' => __(__FILE__, 'First page'),
            'altPrev' => __(__FILE__, 'Previous page'),
            'altNext' => __(__FILE__, 'Next page'),
            'altLast' => __(__FILE__, 'Last page'),
            'altPage' => __(__FILE__, 'Page %d'),
            'prevImg' => '&laquo;',
            'nextImg' => '&raquo;',
            'separator' => '',
            'spacesBeforeSeparator' => 0,
            'spacesAfterSeparator' => 0,
            'curPageLinkClassName' => '-run-pg-actual-page',
            'curPageSpanPre' => '',
            'curPageSpanPost' => '',
            'firstPagePre' => '',
            'firstPageText' => '|&laquo;',
            'firstPagePost' => '',
            'lastPagePre' => '',
            'lastPageText' => '&raquo;|',
            'lastPagePost' => '',
            'clearIfVoid' => true,
            
        );
        $this->pagerOptions = array_merge($defaults, $options);
        
        if ($this->Pager === null) {
            App::loadVendor('App', 'Pager/Pager');
            $this->Pager = Pager::factory($this->pagerOptions);
        }
        else {
            $this->Pager->setOptions($this->pagerOptions);
            $this->Pager->build();
        }
    }
    
    /**
     * Normalizes the provided link in two ways:
     * 
     * 1] Removes first page GET param from URL (because of SEO reasons) but only
     * in case that it is the only GET param in URL
     * 
     * E.g. link /my-product?page=1 is returned as /my-product
     * but links /my-product?page=11 and link /my-product?page=1&sort=name are 
     * returned unchanged
     * 
     * 2] Simulates rawurlencode if it is required (Paginator::$rawurlencode is TRUE)
     * 
     * 3] Adds specific css classes to 'first', 'back', 'next', 'last' links if $name
     * is provided
     * 
     * @param string $link Link html code
     * @param string $name Optional. Key under which is stored link in Paginator::$pagerLinks array.
     *      If provided then also specific classes are added to 'first', 'back', 'next', 'last' links.
     * 
     * @return string with removed firts page URL param and properly rawurlencoded
     */
    protected function normalizeLink($link, $name = null) {
        // some items in $this->pagerLinks are arrays of links so make recursion
        if (is_array($link)) {
            foreach ($link as &$value) {
                $value = $this->normalizeLink($value, $name);
            }
            return $link;
        }
        // get urls (value of href attribute)
        if (!preg_match_all('/href="([^"]*)"/i', $link, $matches, PREG_PATTERN_ORDER)) {
            return $link;
        }
        foreach ($matches[1] as $match) {
            $url = $match;
            $newUrl = htmlspecialchars_decode($url);
            // remove first page param if it is an alone GET param in URL
            $newUrl = preg_replace(
                '/(\?|\&)' . $this->pageUrlParam . '=1$/',
                '',    
                $newUrl,
                -1,
                $count
            );
            if (!$count) {
                $newUrl = preg_replace(
                    '/(\?|\&)' . $this->pageUrlParam . '=1\&/',
                    '$1',    
                    $newUrl,
                    -1,
                    $count
                );
            }
            if ($this->rawurlencode) {
                // Pager used urlencode() method.
                // If rawurlencode is required (better copatibility with browsers) then simulate it
                $newUrl = str_replace('+', '%20', $newUrl);
            }
            // apply App::getUrl() to normalize trailing slash
            $newUrl = App::getUrl($newUrl);

            $newUrl = htmlspecialchars($newUrl);
            $link = str_replace('href="' . $url . '"', 'href="' . $newUrl . '"', $link);
        }
        // add classes to 'first', 'back', 'next', 'last' links
        if (
            $name === 'first'
            || $name === 'back'
            || $name === 'next'
            || $name === 'last'
        ) {
            $class = '-run-pg-link-' . $name;
            $link = preg_replace('/class="([^"]+)"/i', 'class="\1 ' . $class . '"', $link);
        }
        
        return $link;
    }
        
    protected function prepareLinks() {
        if ($this->Pager === null) {
            $this->setLinksOptions();
        }
        $this->pagerLinks = $this->Pager->getLinks();
        foreach ($this->pagerLinks as $key => &$link) {
            $link = $this->normalizeLink($link, $key);
        }
        unset($link);
    }
        
    public function getFirstLink() {
        if ($this->pagerLinks === null) {
            $this->prepareLinks();
        }
        return $this->pagerLinks['first'];
    }
    
    public function getPreviousLink() {
        if ($this->pagerLinks === null) {
            $this->prepareLinks();
        }
        return $this->pagerLinks['back'];
    }
    
    public function getPagesLinks() {
        if ($this->pagerLinks === null) {
            $this->prepareLinks();
        }
        return $this->pagerLinks['pages'];
    }
    
    public function getNextLink() {
        if ($this->pagerLinks === null) {
            $this->prepareLinks();
        }
        return $this->pagerLinks['next'];
    }
    
    public function getLastLink() {
        if ($this->pagerLinks === null) {
            $this->prepareLinks();
        }
        return $this->pagerLinks['last'];
    }
    
    public function getLinks() {
        if ($this->pagerLinks === null) {
            $this->prepareLinks();
        }
        return $this->pagerLinks['first'] . $this->pagerLinks['back'] 
            . $this->pagerLinks['pages'] 
            . $this->pagerLinks['next'] . $this->pagerLinks['last'];
    }
        
    /**
     * Generates page select html
     * 
     * @param array $options All options which can be passed to FormHelper::select()
     * 
     * @return string Select input html
     */
    public function getPageSelect($options = array()) {
        if ($this->pagesCount <= 1) {
            return '';
        }
        $defaults = array(
            'id' => uniqid('-run-pg-page-select-'),
            'value' => $this->page,
        );
        $options = array_merge($defaults, $options);
        // set options if not provided
        if (empty($options['options'])) {
            $pages = range(1, $this->pagesCount, 1);
            $options['options'] = array_combine($pages, $pages);
        }
        // If request options are inherited then do not remove empty params.
        // E.g. if you will work over the same URL on 2 browser tabs and in one of
        // them you would filter and on other not then removing of param and
        // consequentional inheritance from other tab would disturb you
        $options['removeEmptyParam'] = !$this->inheritRequestOptions;
        return Html::urlParamSelect($this->pageUrlParam, $options);
    }

    /**
     * Generates limit select html
     * 
     * @param array $options All options which can be passed to FormHelper::select()
     * 
     * @return string Select input html
     */
    public function getLimitSelect($options = array()) {
        if ($this->pagesCount <= 1) {
            return '';
        }
        $defaults = array(
            'id' => uniqid('-run-pg-limit-select-'),
            'value' => $this->limit,
            'options' => array(
                12 => 12,
                24 => 24,
                48 => 48,
                96 => 96,
            ),
            'resetParams' => array(
                $this->pageUrlParam,
            )
        );
        $options = array_merge($defaults, $options);
        // If request options are inherited then do not remove empty params.
        // E.g. if you will work over the same URL on 2 browser tabs and in one of
        // them you would filter and on other not then removing of param and
        // consequentional inheritance from other tab would disturb you
        $options['removeEmptyParam'] = !$this->inheritRequestOptions;
        return Html::urlParamSelect($this->limitUrlParam, $options);
    }
    
    /**
     * Generates sort select html
     * 
     * @param array $options All options which can be passed to FormHelper::select()
     *      and Html::urlParamSelect().
     * 
     * @return string Select input html
     */
    public function getSortSelect($options = array()) {
        $defaults = array(
            'id' => uniqid('-run-pg-sort-select-'),
            'resetParams' => array(),
        );
        $options = array_merge($defaults, $options);
        $options['resetParams'] = array_unique(array_merge(
            (array)$options['resetParams'], 
            array(
                $this->pageUrlParam,
            )
        ));
        // If request options are inherited then do not remove empty params.
        // E.g. if you will work over the same URL on 2 browser tabs and in one of
        // them you would filter and on other not then removing of param and
        // consequentional inheritance from other tab would disturb you
        $options['removeEmptyParam'] = !$this->inheritRequestOptions;
        return Html::urlParamSelect($this->sortUrlParam, $options);
    }
    
    /**
     * Generates sort switch link html
     * 
     * @param string $field Field name to be sorted, e.g. 'name'. Provide empty 
     *          string '' to reset sort.
     * @param string $direction Sort direction, e.g. 'ASC', 'DESC' or empty to reset.
     * @param array $options All options which can be passed to Html::urlParamSwitch()
     * 
     * @return string Link html
     */
    public function getSortSwitch($field, $direction, $options = array()) {
        $defaults = array(
            'id' => uniqid('-run-pg-sort-switch-'),
            'resetParams' => array(),
            'separator' => '.',
        );
        $options = array_merge($defaults, $options);
        $options['resetParams'] = array_unique(array_merge(
            (array)$options['resetParams'], 
            array(
                $this->pageUrlParam,
            )
        ));
        $param = $this->sortUrlParam;
        if ($field) {
            $param .= $options['separator'] . $field;
        }
        // If request options are inherited then do not remove empty params.
        // E.g. if you will work over the same URL on 2 browser tabs and in one of
        // them you would filter and on other not then removing of param and
        // consequentional inheritance from other tab would disturb you
        $options['removeEmptyParam'] = !$this->inheritRequestOptions;
        $options['paramPrefix'] = $this->sortUrlParam;
        return Html::urlParamSwitch($param, $direction, $options);
    }
    
    /**
     * Generates filter select html
     * 
     * @param array $options All options which can be passed to FormHelper::select()
     *      and Html::urlParamSelect() plus following:
     *      - 'options' (array) Array containig value and label pairs like {value} => {label}. 
     *      The GET $param will be set to some of values once it is choosen in select input. 
     *      E.g. for 'page' $param this can be array(1 => 1, 2 => 2, ...). Values can contain 
     *      field name separated by ':' from final value of param, e.g.  this can be 
     *      array('run_users_id:25' => 'Records belonging to user XYZ', 'run_users_id:26' => 'Records belonging to user ABC').
     *      If 'Records belonging to user ABC' is choosen then the value of $_GET['filter']['run_users_id'] is set to 26.
     *      This allows to filter different fields from one filter select. Defaults to empty array().
     *      - 'field' (string) Filter field name. If provided then all values in 'options' are
     *      applied to this field. In such a case option values should not contain fiel names (in normal case, 
     *      you can still use it in some ver specific cases when you need 3 levels of nesting).
     *      E.g. the above example can be rewriten like array('field' => 'run_users_id', 
     *      'options' =>  array('25' => 'Records belonging to user XYZ', '26' => 'Records belonging to user ABC'))
     *      - 'template'  (string) Defaults to '< div class=":c:">:l::i::e:< /div>'
     *      - 'resetParams' (string|array) Single GET param name or an array of GET param names
     *      which should be reset after the value of select is changed. Defaults to array('page').
     *      - 'separator' (string) Field path separator used to separate nested field levels, e.g. 'filter.active'. 
     *      Sometimes it is necessry to change it if the field name itself contains the separator character,
     *      e.g. 'filter/Users.active'. Defaults to '.'. 
     * 
     * @return string Select input html
     */
    public function getFilterSelect($options = array()) {
        $defaults = array(
            'field' => null,
            'id' => uniqid('-run-pg-filter-select-'),
            'resetParams' => array(),
            'separator' => '.',
        );
        $options = array_merge($defaults, $options);
        $options['resetParams'] = array_unique(array_merge(
            (array)$options['resetParams'], 
            array(
                $this->pageUrlParam,
            )
        ));
        $param = $this->filterUrlParam;
        if (!empty($options['field'])) {
            $param .= $options['separator'] . $options['field'];
        }
        // If request options are inherited then do not remove empty params.
        // E.g. if you will work over the same URL on 2 browser tabs and in one of
        // them you would filter and on other not then removing of param and
        // consequentional inheritance from other tab would disturb you
        $options['removeEmptyParam'] = !$this->inheritRequestOptions;
        return Html::urlParamSelect($param, $options);
    }  

    /**
     * Generates filter switch link html
     * 
     * @param string $field Field name to be filtered, e.g. 'active'.
     * @param string $value Filtering value.
     * @param array $options All options which can be passed to Html::urlParamSwitch():
     *      - 'label' (string) Switch label (plain text or html). Defaults to NULL.
     *      - 'class' (string) Css additional class applied on wrapper div. Defaults to NULL.
     *      - 'showCheckbox' (bool) If TRUE then a checkbox is shown to see if switch 
     *          is active or not. Defaults to FALSE.
     *      - 'template' (string) Switch html template. It must be < a> tag. Following inserts can
     *      be used: :a: - attributes (generated from items in options which are not
     *      standard options), :c: - css class, :l: - label, :u: - generated url,
     *      :ch: - checkbox (if option 'showCheckbox' is FALSE then empty string).
     *      Defaults to '< a class=":c:" href=":u:":a:>:ch:&nbsp;:l:< /a>'.
     *      - 'resetParams' (string|array) Single GET param path or an array of GET param paths
     *      which should be reset after the value of select is changed. E.g. 'page' or
     *      array('page', 'filter.active'). Defaults to array('page').
     *      - 'separator' (string) Field path separator used to serarate nested field levels, e.g. 'filter.active'. 
     *      Sometimes it is necessry to change it if the field name itself contains the separator character,
     *      e.g. 'filter/Users.active'. Defaults to '.'. 
     * 
     * @return string Link html
     */
    public function getFilterSwitch($field, $value, $options = array()) {
        $defaults = array(
            'id' => uniqid('-run-pg-filter-switch-'),
            'resetParams' => array(),
            'separator' => '.',
        );
        $options = array_merge($defaults, $options);
        $options['resetParams'] = array_unique(array_merge(
            (array)$options['resetParams'], 
            array(
                $this->pageUrlParam,
            )
        ));
        $param = $this->filterUrlParam . $options['separator'] . $field;
        // If request options are inherited then do not remove empty params.
        // E.g. if you will work over the same URL on 2 browser tabs and in one of
        // them you would filter and on other not then removing of param and
        // consequentional inheritance from other tab would disturb you
        $options['removeEmptyParam'] = !$this->inheritRequestOptions;
        $options['paramPrefix'] = $this->filterUrlParam;
        return Html::urlParamSwitch($param, $value, $options);
    }

    /**
     * Returns html code of info about displayed records
     * For example: 1 - 10 / 25
     *
     * @return string
     */
    public function getCountOfDisplayed() {
        $options = $this->getOptions();
        $totalCount = $options['count'];
        $from = 1;
        $to = $totalCount;
        $limit = $options['limit'];

        if (empty($totalCount)) {
            return '';
        }
        if ($totalCount > $limit) {
            $actualPage = $options['page'];
            $pagesCount = $options['pagesCount'];
            $from = (($actualPage - 1) * $limit) + 1;
            if ($actualPage < $pagesCount) {
                $to = $from + $limit - 1;
            }
        }

        return $from . ' - ' . $to . ' / ' . $totalCount;
    }
    
    /**
     * Returns HTML code of "Load more items" button used by infinite scroll (Paginator.js)
     * 
     * @param array $options Following are available:
     *      - 'label' (string) Button label. efaults to translation of 'Load more items'.
     *      - 'class' (string) Css class. Defaults to NULL.
     *      - 'id' (string) Css id. Defaults to NULL.
     * 
     * @return string
     */
    public function getLoadMoreButton($options = array()) {
        $defaults = array(
            'label' => __(__FILE__, 'Load more items'),
            'class' => null,
            'id' => null,
        );
        $options = array_merge($defaults, $options);
        if (!($nextLinkHtml = $this->getNextLink())) {
            return '';
        }
        // it is important to make it as link to allow google to load products
        // of next pages
        $match = array();
        $url = null;
        if (preg_match('/href=["\']([^"\']+)["\']/', $nextLinkHtml, $match)) {
            $url = $match[1];
        }
        $buttonHtml = '<a';
        if ($url) {
            $buttonHtml .= ' href="' . $url . '"';
        }
        if ($url) {
            $buttonHtml .= ' href="' . $url . '"';
        }
        if ($options['class']) {
            $buttonHtml .= ' class="' . $options['class'] . '"';
        }
        if ($options['id']) {
            $buttonHtml .= ' id="' . $options['id'] . '"';
        }
        $buttonHtml .= '>';
        $buttonHtml .= $options['label'];
        $buttonHtml .= '<span class="loading-mask"></span></a>';
        return $buttonHtml;
    }
}
