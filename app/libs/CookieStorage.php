<?php
/**
 * See http://www.php.net/manual/en/function.setcookie.php#94390
 */
class CookieStorage {
    
    protected $name;
    
    protected $expire = 0;
        
    protected $compress = true;
        
    /**
     * Decoded cookies data
     * 
     * @var array 
     */
    static protected $cookies = array();
        
    const COOKIE_PARTS = '_part_'; 
    
    /**
     * Get instance of CookieStorage for specified name.
     * 
     * @param string $name Cookie name
     * @param array $options Following are available:
     *      - 'expire' (integer) The time the cookie expires. This is a Unix timestamp 
     *          so is in number of seconds since the epoch. In other words, you'll most 
     *          likely set this with the time() function plus the number of seconds before 
     *          you want it to expire, e.g. time()+60*60*24*30 will set the cookie to 
     *          expire in 30 days
     *      - 'compress' (bool) Compress cookie content or not. Defaults to TRUE.
     */
    public function __construct($name, $options = array()) {
        $defaults = array(
            'expire' => $this->expire,
            'compress' => $this->compress,
        );
        $options = array_merge($defaults, $options);
        
        $this->name = $name;
        $this->expire = $options['expire'];
        $this->compress = $options['compress'];
        
        if (!array_key_exists($this->name, self::$cookies)) {
            $this->readCookies();
        }
        // rewrite the storage data just after loading them to renew cookies expiration
        // time and ensure that even new created empty storage data are written into 
        // cookies and hold the same id accross the page calls
        $this->writeCookies();
    }
    
    /**
     * Returns partial cookie name for provided index
     * 
     * @param integer $index
     * 
     * @return string Name of partial cookie
     */
    protected function getCookieName($index) {
        return ($index > 0) ? $this->name.self::COOKIE_PARTS.$index : $this->name;
    }
    
    /**
     * Returns array which can be used as new storage data. Returned data has new 
     * unique 'id'.
     * 
     * @param mixed $value New data 'value'. Defaults to NULL.
     * 
     * @return array
     */
    protected function getNewData($value = null) {
        return array(
            'id' => session_id() . uniqid(),
            'value' => $value,
        );
    }
        
    /**
     * Reads the storage data from cookies
     */
    protected function readCookies() {
        // if there is no CookieStorage with specified name then create empty one
        if (!isset($_COOKIE[$this->name])) {
            self::$cookies[$this->name] = $this->getNewData();
        }
        else {
            $cookie = ''; 
            for (
                $index = 0 ; 
                $name = $this->getCookieName($index), 
                    array_key_exists($name, $_COOKIE); 
                $index += 1
            ) { 
                $cookie .= $_COOKIE[$name]; 
            } 

            $cookie = base64_decode($cookie); 
            if ($this->compress) {
                $cookie = gzuncompress($cookie); 
            }
            $cookie = json_decode($cookie, true);

            // keep backward compatibility and convert old version of cookie 
            // (without id) to new one. This can be removed on projet once it 
            // uses new cookie storage for more than 30 day
            if (
                !array_key_exists('id', $cookie)
                && !array_key_exists('value', $cookie)
            ) {
                $cookie = $this->getNewData($cookie);
            }

            self::$cookies[$this->name] = $cookie;
        }
    }
    
    /**
     * Writes the storage data into cookies
     * 
     * @throws Exception if output headers has been sent aôready
     */
    protected function writeCookies() {
        $cookie = self::$cookies[$this->name];
        
        // set storage cookies
        $cookie = json_encode($cookie); 
        if ($this->compress) {
            $cookie = gzcompress($cookie); 
        }
        $cookie = base64_encode($cookie); 
        
        // split to 3k pieces (make it smaller that 4k max cookie size as some 
        // browsers do not reach even 4k)
        $split = str_split($cookie, 3000); 
        $count = count($split); 

        for ($index = 0 ; $index < $count ; $index += 1) {
            $name = $this->getCookieName($index);
            $value = $split[$index];
            if (!setcookie($name, $value, $this->expire, '/', '', 0)) {
                throw new Exception('Cookie storage cannot be used more for output as headers were sent already');
            }
            $_COOKIE[$name] = $value;
        } 

        $this->deleteCookies($count);
    }

    /**
     * Deletes the storage data cookies
     * 
     * @param integer $fromIndex Index to delete partial cookies from. Defalts to 0, 
     *      it means all cookies are deleted
     * 
     * @throws Exception if output headers has been sent aôready
     */
    protected function deleteCookies($fromIndex = 0) { 
        $expire = time() - 3600; 
        for (
            $index = $fromIndex; 
            $name = $this->getCookieName($index),
            array_key_exists($name, $_COOKIE); 
            $index += 1
        ) { 
            if (!setcookie($name, '', $expire, '/', '', 0)) {
                throw new Exception('Cookie storage cannot be used more for output as headers were sent already');
            }
            unset($_COOKIE[$name]); 
        } 
    } 

    /**
     * Sets value of cookie storage
     * 
     * ATTENTION: CookieStorage::set() will rewrite storage data regardless to exisiting 
     * keys in it. So eithger do not use together CookieStorage::set() and ::setKey()
     * methods or be aware of what you are doing.
     * 
     * NOTE: If CookieStorage::set() is used before CookieStorage::get() then 
     * the original cookie content will never be retrieved as it is replaced by new value.
     * 
     * @param mixed $value
     * 
     * @throws Exception if headers has been sent already
     */
    public function set($value) { 
        self::$cookies[$this->name]['value'] = $value;
        $this->writeCookies();
    } 
    
    /**
     * Sets $value of cookie storage $key
     * 
     * ATTENTION: CookieStorage::setKey() will rewrite storage data regardless to exisiting 
     * keys in it. So eithger do not use together CookieStorage::set() and ::setKey()
     * methods or be aware of what you are doing.
     * 
     * NOTE: If CookieStorage::setKey() is used before CookieStorage::getKey() then 
     * the original cookie $key content will never be retrieved as it is replaced by new value.
     * 
     * @param string $key Key name to set the $value in cookie storage for
     * @param mixed $value
     * 
     * @throws Exception if headers has been sent already
     */
    public function setKey($key, $value) { 
        if (!is_array(self::$cookies[$this->name]['value'])) {
            self::$cookies[$this->name]['value'] = (array)self::$cookies[$this->name]['value'];
        }
        self::$cookies[$this->name]['value'][$key] = $value;
        $this->writeCookies();
    } 
    
    /**
     * Sets $value of cookie storage $path
     * 
     * ATTENTION: CookieStorage::setPath() will rewrite storage data regardless to exisiting 
     * paths in it. So eithger do not use together CookieStorage::set() and ::setPath()
     * methods or be aware of what you are doing.
     * 
     * NOTE: If CookieStorage::setPath() is used before CookieStorage::getPath() then 
     * the original cookie $path content will never be retrieved as it is replaced by new value.
     * 
     * @param string $path Path specified like 'keyA.keyB.keyC' to set the $value in cookie storage for
     * @param mixed $value
     * 
     * @throws Exception if headers has been sent already
     */
    public function setPath($path, $value) { 
        if (!is_array(self::$cookies[$this->name]['value'])) {
            self::$cookies[$this->name]['value'] = (array)self::$cookies[$this->name]['value'];
        }
        Arr::setPath(self::$cookies[$this->name]['value'], $path, $value);
        $this->writeCookies();
    } 
    
    /**
     * Unsets $value of cookie storage $key
     * 
     * NOTE: If CookieStorage::unsetKey() is used before CookieStorage::getKey() then 
     * the original cookie $key content will never be retrieved as it is replaced by new value.
     * 
     * @param string $key Key name to be unset in cookie storage
     * 
     * @throws Exception if headers has been sent already
     */
    public function unsetKey($key) { 
        if (is_array(self::$cookies[$this->name]['value'])) {
            unset(self::$cookies[$this->name]['value'][$key]);
            $this->writeCookies();
        }
    } 
    
    /**
     * Unsets $value of cookie storage $path
     * 
     * NOTE: If CookieStorage::unsetPath() is used before CookieStorage::getPath() then 
     * the original cookie $path content will never be retrieved as it is replaced by new value.
     * 
     * @param string $path Path specified like 'keyA.keyB.keyC' to be unset in cookie storage
     * 
     * @throws Exception if headers has been sent already
     */
    public function unsetPath($path) { 
        if (is_array(self::$cookies[$this->name]['value'])) {
            Arr::unsetPath(self::$cookies[$this->name]['value'], $path);
            $this->writeCookies();
        }
    } 
    
    /**
     * Gets value of cookie storage
     * 
     * NOTE: If CookieStorage::set() is used before CookieStorage::get() then 
     * the original cookie content will never be retrieved as it is replaced by new value.
     * 
     * @return mixed
     */
    public function get() { 
        return self::$cookies[$this->name]['value'];
    } 
    
    /**
     * Gets value of cookie storage $key
     * 
     * NOTE: If CookieStorage::setKey() is used before CookieStorage::getKey() then 
     * the original cookie $key content will never be retrieved as it is replaced by new value.
     * 
     * @param string $key Key name to get the $value in cookie storage for.
     * 
     * @return mixed
     */
    public function getKey($key) { 
        if (
            is_array(self::$cookies[$this->name]['value'])
            && array_key_exists($key, self::$cookies[$this->name]['value'])
        ) {
            return self::$cookies[$this->name]['value'][$key];
        }
        return null;
    } 
    
    /**
     * Gets value of cookie storage $path
     * 
     * NOTE: If CookieStorage::setPath() is used before CookieStorage::getPath() then 
     * the original cookie $path content will never be retrieved as it is replaced by new value.
     * 
     * @param string $path Path specified like 'keyA.keyB.keyC' to get the $value in cookie storage for.
     * 
     * @return mixed
     */
    public function getPath($path) { 
        if (is_array(self::$cookies[$this->name]['value'])) {
            return Arr::getPath(self::$cookies[$this->name]['value'], $path);
        }
        return null;
    } 
    
    /**
     * Gets id of cookie storage.
     * Id is changed only when CookieStorage::clear() is called and new instance of 
     * CookieStorage is created.
     * 
     * @return string Id of cookie storage which is unique for given storage name 
     * and actual session
     */
    public function getId() {
        return self::$cookies[$this->name]['id'];
    }
    
    /**
     * Clears value of cookie storage
     */
    public function clear() { 
        $this->deleteCookies(); 
        // prepare new empty data for case that CookieStorage::get/set() methods 
        // would be used after ::clear() call
        self::$cookies[$this->name] = $this->getNewData();
    } 
    
    /**
     * Changes storage data id.
     * Usefull for debuging purposes.
     */
    public function changeId() {
        self::$cookies[$this->name]['id'] = session_id() . uniqid();
        $this->writeCookies();
    }
}