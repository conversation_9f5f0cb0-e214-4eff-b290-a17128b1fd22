<?php
/**
 * FormHelper class helps with form inputs creation (besides of input HTML generation
 * it alos resolves input value, creates connested label, resolves errors, 
 * checks if input required, invalid, disabled, translated, ...)
 * 
 * It can be used like, e.g.:
 * 
 *      // create models
 *      $User = new User();
 *      $Profile = new Profile();
 * 
 *      // create form instance
 *      $Form = new FormHelper(array(
 *          'data' => @$_POST['data'], // = Controller::$data
 *          'errors' => array (
 *              'User' => $User->getErrors(),
 *              'Profile' => $Profile->getErrors(),
 *          ),
 *          'required' => array(
 *              'User' => $User->getNotEmptyFields(),
 *              'Profile' => $Profile->getNotEmptyFields(),
 *          ),
 *          'defaultValues' => array(
 *              'Profile' => array(
 *                  'newsletter' => true,
 *                  ...
 *              ),
 *          ),
 *          'inputDefaults' => array(
 *              'template' => '<div>:l::i::e:</div>'
 *              'requiredMark' => '*' ,
 *          ),
 *          'labelDefaults' => array(
 *              'template' => ':l::s:'
 *          )
 *          ...
 *      )); 
 * 
 *      // create form inputs 
 *      // (<form>...</form> tags and submit buttons must be created manually as 
 *      // there is nothing to automate with these tags) 
 *      //
 *      // Set data '_target' to controller action the data are used by
 *      echo $Form->hidden('_target', array(
 *          'explicitValue' => 'MyModule.MyController.myTargetAction'
 *      )); 
 *      echo $Form->hidden('User.id');
 *      echo $Form->text('User.name', array(
 *          'label' => 'Login',
 *      ));
 *      echo $Form->password('User.email', array(
 *          'label' => 'Heslo',
 *      ));
 *      echo $Form->text('User.email', array(
 *          'label' => 'E-mail',
 *      ));
 *      echo $Form->hidden('Profile.id' );
 *      echo $Form->text('Profile.name', array(
 *          'label' => 'Meno',
 *      ));
 *      echo $Form->checkbox('Profile.newsletter', array(
 *          'label' => 'Novinky',
 *      ));
 *      echo $Form->select('Profile.country', array(
 *          'label' => 'Krajina',
 *          'options' => $myCountries, // array('optionValue' => 'optionLabel')
 *      ));
 *      echo $Form->checkbox('Profile.hobby', array(
 *          'label' => 'Hobby',
 *          'options' => $myHobbies, // array('optionValue' => 'optionLabel')
 *      ));
 *      echo $Form->radio('Profile.sex', array(
 *          'label' => 'Pohlavie',
 *          'options' => array('m' => 'Muž', 'f' => 'Žena'),
 *      )); * 
 *      ...
 * 
 *      // there is also possibility to use FormHelper::input() method and 'type' option
 *      //
 *      // Set data '_target' to controller action the data are used by
 *      echo $Form->input('_target', array(
 *          'type' => 'hidden'
 *          'explicitValue' => 'MyModule.MyController.myTargetAction'
 *      )); 
 *      echo $Form->input('User.id', array(
 *          'type' => 'hidden'
 *      ));
 *      echo $Form->input('User.name', array(
 *          'label' => 'Login',
 *      ));
 *      echo $Form->input('User.password', array(
 *          'type' => 'password',
 *          'label' => 'Heslo',
 *      ));
 *      echo $Form->input('User.email', array(
 *          'label' => 'E-mail',
 *      ));
 *      echo $Form->input('Profile.id', array(
 *          'type' => 'hidden'
 *      ));
 *      echo $Form->input('Profile.name', array(
 *          'label' => 'Meno',
 *      ));
 *      echo $Form->input('Profile.newsletter', array(
 *          'type' => 'checkbox',
 *          'label' => 'Novinky',
 *      ));
 *      echo $Form->input('Profile.country', array(
 *          'type' => 'select',
 *          'label' => 'Krajina',
 *          'options' => $myCountries, // array('optionValue' => 'optionLabel')
 *      ));
 *      echo $Form->input('Profile.hobby', array(
 *          'type' => 'checkbox',
 *          'label' => 'Hobby',
 *          'options' => $myHobbies, // array('optionValue' => 'optionLabel')
 *      ));
 *      echo $Form->input('Profile.sex', array(
 *          'type' => 'radio',
 *          'label' => 'Pohlavie',
 *          'options' => array('m' => 'Muž', 'f' => 'Žena'),
 *      ));
 *      ...
 * 
 *      // another possibility is to create label, input and errors separately
 *      $class = $Form->ridt('User.name');
 *      ?><td class="<?php echo $class ?>"><?php
 *          echo $Form->label('User.name', 'Login');
 *      ?></td><?php 
 *      ?><td class="<?php echo $class ?>"><?php
 *          echo $Form->text('User.name', array('attachErrors' => false));
 *      ?></td><?php 
 *      ?><td class="<?php echo $class ?>"><?php
 *          echo $Form->errors('User.name');
 *      ?></td><?php
 *      ...
 * 
 * Following field names results into following input names:
 *      - my_field          ->  data[my_field]                // Data is wrapping array of form fields
 *      - my.nested.field   ->  data[my][nested][field]       // Use dot to create nested structure
 *      - my.nested.field.  ->  data[my][nested][field][]     // Use end dot to create new array item
 *      - my.nested.field.1 ->  data[my][nested][field][1]    // You can of course force the array indexes
 *      - my.nested..field  ->  data[my][nested][][field]     // Use 2 dots (dot nothing dot) to create empty brackets (bracket nothing bracket)
 *      - my.nested.1.field  ->  data[my][nested][1][field]   // You can of course force the array indexes on any possition in array
 * 
 */
class FormHelper {
    
    /**
     * Form inputs data stored in nested structure according to field names. 
     * 
     * @var array 
     */
    protected $data = array();
    
    /**
     * Form inputs default values stored in nested structure according to field names.
     * 
     * @var array
     */
    protected $defaultValues = array();
    
    
    /**
     * Form inputs errors stored in nested structure according to field names. 
     * 
     * @var array 
     */
    protected $errors = array();
    
    /**
     * Form inputs warnings stored in nested structure according to field names. 
     * 
     * @var array 
     */
    protected $warnings = array();
    
    /**
     * Form required fields stored in nested structure according to field names. 
     * 
     * @var array 
     */
    protected $required = array();
    
    /**
     * Form translated fields stored in nested structure according to field names. 
     * 
     * @var array 
     */
    protected $translated = array();
    
    /**
     * Lang of form translated fields. 
     * 
     * @var string 
     */
    protected $lang = null;
    
    /**
     * Field path separator used to serarate nested field levels, e.g. 'User.name'. 
     * Sometimes it is necessry to change it if the field name itself contains the separator character,
     * e.g. in case of setting (Setting/App.email) or right names (App.Settings.admin_edit). 
     * Defaults to '.'. 
     * 
     * @var string 
     */
    protected $separator = '.';
    
    /**
     * Form disabled fields stored in nested structure according to field names.
     * 
     * @var array 
     */
    protected $disabled = array();
    
    /**
     * Default input options for all created inputs
     * @var array 
     */
    protected $inputDefaults = array(
        /**
         * Default created input type
         * @var string
         */
        'type' => 'text',
        
        /**
         * CSS class assigned to all created inputs
         * @var string
         */
        'class' => null,
        
        /**
         * CSS class assigned to text inputs (input[type=text|file|password], textarea, select)
         * @var string
         */
        'textClass' => 'text-input',
        
        /**
         * CSS class assigned to toggle inputs (input[type=checkbox|radio])
         * @var string
         */
        'toggleClass' => 'toggle-input',
        
        /**
         * CSS class assigned to display inputs
         * @var string
         */
        'displayClass' => 'display-input',
        
        /**
         * CSS class assigned to display image inputs
         * @var string
         */
        'imageClass' => 'display-image-input',
        
        /**
         * CSS class assigned to editor inputs
         * @var string
         */
        'editorClass' => 'editor-input',
        
        /**
         * CSS class assigned to gallery inputs
         * @var string
         */
        'galleryClass' => 'gallery-input',
        
        /**
         * CSS class assigned to content blocks inputs
         * @var string
         */
        'contentBlocksClass' => 'content-blocks-input',
        
        /**
         * Html template to render form inputs.
         * 
         * Use ':l:', ':i:', ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, 
         * input, errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into template html, e.g.:
         * '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
         * If empty value then label is generated like ':l::i::e:'. Defaults to NULL.
         * @var string 
         */
        'template' => null,
        
        /**
         * Html template to render checkbox groups and radio groups partial inputs.
         * 
         * Use :l: and :i: inserts to place the label and input html into template html, e.g.:
         * '< div class="my-checkbox">:i::l:< /div>'
         * If nothing defined then input is generated like ':i::l:' 
         * Defaults to NULL.
         * @var string 
         */
        'toggleTemplate' => null,
        
        /**
         * Required mark to be used for inputs of required fields. 
         * If empty value then it is not generated.
         * @var string 
         */
        'requiredMark' => '*',
        
        /**
         * Translated mark to be used for inputs of translated fields. Use :lang: insert
         * to adjust the mark for actual lang, e.g. < img src="/app/img/lang/flag-:lang:.png/>.
         * If empty value then it is not generated.
         * @var string 
         */
        'translatedMark' => null,
        
        /**
         * Url path to itemselector css file. Path must be app root relative.
         * This file is added to css dynamic files on creation of the first itemselector input.
         * Defaults to /app/css/libs/itemselector.css.
         * 
         * 
         * @var string
         */
        'itemselectorCssFile' => '/app/css/libs/itemselector.css',
        
        /**
         * Url path to itemselector js file. Path must be app root relative.
         * This file is added to js dynamic files on creation of the first itemselector input.
         * Defaults to /app/js/libs/Itemselector.js.
         * 
         * 
         * @var string
         */
        
        'itemselectorJsFile' => '/app/js/libs/Itemselector.js',
                
        /**
         * Url path to treeselector css file. Path must be app root relative.
         * This file is added to css dynamic files on creation of the first treeselector input.
         * Defaults to /app/css/libs/treeselector.css.
         * 
         * 
         * @var string
         */
        'treeselectorCssFile' => '/app/css/libs/treeselector.css',
        
        /**
         * Url path to treeselector js file. Path must be app root relative.
         * This file is added to js dynamic files on creation of the first treeselector input.
         * Defaults to /app/js/libs/Treeselector.js.
         * 
         * 
         * @var string
         */
        
        'treeselectorJsFile' => '/app/js/libs/Treeselector.js',
        
        /**
         * Url path to datepicker js file. Path must be app root relative.
         * Defaults to /app/js/libs/Datepicker.js.
         * 
         * 
         * @var string
         */
        
        'datepickerJsFile' => '/app/js/libs/Datepicker.js',
                
        /**
         * Html template to render HTML editor into
         * 
         * Use :class:, :id: and :editor: inserts to place respective items to the template.
         * Defaults to '<div id=":id:" class=":class:">:editor:</div>'.
         * 
         * @var string
         */
        'editorTemplate' => '<div id=":id:" class=":class:">:editor:</div>',
                
        /**
         * Html definition of js script to be attached to editor input to launch
         * the editor 
         * 
         * Use :id: and :options: inserts to place respective items.
         * Defaults to 'jQuery(function(){CKEDITOR.replace(\':id:\', :options:);});'
         * 
         * If you need to do something on focus/blur use following:
         * 'jQuery(function(){var editor = CKEDITOR.replace(\':id:\', :options:); editor.on(\'focus\', function(e){...})});'
         * 
         * @var string
         */
        'editorScript' =>
            // a bit of hack to make CKE load properly when admin form with
            // content blocks input is opened in fancybox by SmartAdminLauncher...
            'jQuery(function(){' .
                'if (jQuery(\'#:id:\').is(\':visible\')) {' .
                    'CKEDITOR.replace(\':id:\', :options:);' .
                '}' .
                // ...if the content blocks input is closed (it means contained 
                // CKE background textarea is not visible)...
                'else {' .
                    // ...then check each 10ms if background textarea is already 
                    // visible. If yes or if bulgarian 1s has aready ellapsed
                    // then load CKE and turn off the checks
                    'var startTime = Date.now();' .
                    'var intervalId = setInterval(function(){' .
                        'if (' . 
                            'jQuery(\'#:id:\').is(\':visible\') ' .
                            '|| (Date.now() - startTime) > 1000' .  // bulgarian 1s
                        ') {' .
                            'CKEDITOR.replace(\':id:\', :options:);' .
                            'clearInterval(intervalId);' .
                        '}' .
                    '}, 10);' . // 10ms
                '}' .
            '});',
        
        /**
         * Url path to editor js library script. Path must be app root relative.
         * This file is added to js dynamic files on creation of the first editor input.
         * Defaults to /app/vendors/ckeditor/ckeditor.js.
         * 
         * 
         * @var string
         */
        'editorJsFile' => '/app/vendors/ckeditor/ckeditor.js',
                
        /**
         * If TRUE then existing errors are attached automatically to created inputs.
         * If FALSE then existing errors are not attached to created inputs.
         * If 'scrollOnly' then only scroll to errors js script is generated 
         * but errors are not attached.
         * @var bool|string
         */
        'attachErrors' => true,
        
        /**
         * If TRUE then existing warnings are attached automatically to created inputs.
         * If FALSE then existing warnings are not attached to created inputs.
         * @var bool
         */
        'attachWarnings' => true,
        
    );
    
    /**
     * Default label options for all created labels
     * @var array 
     */
    protected $labelDefaults = array(
        /**
         * Label CSS class
         * @var string
         */
        'class' => null,
        
        /**
         * Separator to be placed after label.
         * @var string
         */
        'separator' => ':',
        
        /**
         * Required mark to be used for labels of required fields. 
         * If empty value then it is not generated.
         * @var string 
         */
        'requiredMark' => null,
        
        /**
         * Translated mark to be used for labels of translated fields. Use :lang: insert
         * to adjust the mark for actual lang, e.g. < img src="/app/img/lang/flag-:lang:.png/>.
         * If empty value then it is not generated.
         * @var string 
         */
        'translatedMark' => null,
        
        /**
         * Html template to render form labels.
         * 
         * Use ':l:', ':s:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label text, 
         * separator, hint, requiredMark, translatedMark html and ridt css classes into template html, e.g.:
         * '< div class="my-label :ridt:">:l::h:< /div>'
         * If empty value then label is generated like ':l::s::r::t::h:'. Defaults to NULL.
         * @var string 
         */
        'template' => null,
    );    
    
    /**
     * Default errors options for all created input errors 
     * @var array 
     */
    protected $errorsDefaults = array(
        /**
         * If TRUE then the error messages are returned in plain text, 
         * concatenating all error messages for given field,  without any html markup.
         * @var bool
         */
        'plainText' => false,
        
        /**
         * Errors div wrapper CSS class
         * @var string
         */
        'class' => 'input-errors',
        
        /**
         * Error div wrapper CSS class
         * @var string
         */
        'errorClass' => 'input-error',
    );    
    
    /**
     * Default warnings options for all created input warnings 
     * @var array 
     */
    protected $warningsDefaults = array(
        /**
         * If TRUE then the warning messages are returned in plain text, 
         * concatenating all warning messages for given field,  without any html markup.
         * @var bool
         */
        'plainText' => false,
        
        /**
         * Warnings div wrapper CSS class
         * @var string
         */
        'class' => 'input-warnings',
        
        /**
         * Warning div wrapper CSS class
         * @var string
         */
        'warningClass' => 'input-warning',
    );    
    
    /**
     * Default empty text options for all created input empty texts
     * @var array 
     */
    protected $emptyTextDefaults = array(
        
        /**
         * Empty text div CSS class
         * @var string
         */
        'class' => 'input-empty-text',
        
        /**
         * If TRUE the input and its empty text are wrapped into wrapper to allow 
         * right positioning of empty text over input
         * @var bool
         */
        'wrapper' => true,
        
        /**
         * Empty text div wrapper CSS class
         * @var string
         */
        'wrapperClass' => 'input-empty-text-wrapper',
    ); 
    
    /**
     * Should the form be scrolled automatically to first occurence of validation error?.
     * Use FormHelper::setPropertyScrollToError() to set the value.
     * 
     * @var bool
     */
    protected $scrollToError = true;
    
    /**
     * Possible values are 'bootstrap', 'mdc' or any empty value.
     * 
     * If 'bootstrap' then there are added classes of bootstrap css (see http://getbootstrap.com/). 
     * Classes are added to text inputs ('form-control'), label ('control-label'), 
     * ridt classes ('has-error') and to errors ('help-block'). There is also added 
     * wrapping block <div class="form-group {ridtClasses}"><div>.
     * 
     * If 'mdc' then there are added classes of material design components css (see https://material.io/components/web/). 
     * Classes are added to text inputs (...), label (...), 
     * ridt classes (...) and to errors (...). There is also added 
     * wrapping block ....
     * 
     * @var bool 
     */
    protected $compatibility = null;
    
    /**
     * Should the nontranslated fields be disabled in other langs than default?
     * How should be nontranslated fields be processed in langs than default?
     * Possible values are 'ignore' and 'disable' or empty value (NULL, FALSE).
     * If set to 'ignore' then the field is ignored (nothing is generated)
     * If set to disabled then the field is disabled. If an empty value then the 
     * field is trated normally.
     * 
     * @var string
     */
    protected $nonTranslatedProcessing = false;
        
    /**
     * If TRUE then the input name is namespaced by 'data' prefix,
     * e.g. for field 'User.name' the input name data[User][name] is created.
     * If a string, e.g. 'my[wrapper]' then the input name is namespaced by provided 
     * prefix, e.g. input name my[wrapper][User][name] is created.
     * If FALSE then the input name is not namespaced, e.g. input name User[name] 
     * is created.
     * 
     * @var bool|string
     */
    protected $useDataWrapper = true;
    
    /**
     * Url path to jQuery js library script. Path must be app root relative.
     * This file is added to js dynamic files when needed.
     * Defaults to /app/js/vendors/jquery.min.js
     * 
     * @var string
     */
    protected $jQueryJsFile = '/app/js/vendors/jquery.min.js';
        
    /**
     * Url path to jQueryUI js library script. Path must be app root relative.
     * This file is added to js dynamic files when needed.
     * Defaults to /app/js/vendors/jquery.min.js
     * 
     * @var string
     */
    protected $jQueryUiJsFile = '/app/js/vendors/jquery-ui/jquery-ui.min.js';
    protected $jQueryUiCssFile = '/app/js/vendors/jquery-ui/jquery-ui.css';
    
    /**
     * INTERNALLY USED PROPERTIES
     */
    
    /**
     * Form fields with corresponding input names stored in array like:
     * 
     *      array(
     *          // under index 0 are stored field names created without data vrapper
     *          0 => array (                
     *              {field} => {inputName},
     *              'User.name' => 'User[name]'
     *              ...,
     *          ),
     *          // under index 1 are stored field names created with data vrapper
     *          1 => array (
     *              {field} => {dataInputName},
     *              'User.email' => 'data[User][email]'
     *              ...,
     *          )
     *      )
     * 
     * Used as a cache to find out quickly inputNames for fields which have been
     * already treated.
     * 
     * Used internally.
     *
     * @var array 
     */
    protected $fields = array();
    
    /**
     * Register of all existing inputs names with counts of corresponding ids creates
     * for them:  
     * 
     *      {inputName} => {idsCount}
     * 
     * Serves to know how many ids are created for inputs with same name.
     * 
     * Used internally.
     * 
     * @var array 
     */
    protected $inputNameIdsCounts = array();
    
    /**
     * Register of input counts for each input name:
     * 
     *      {inputName} => {inputsCount} 
     * 
     * Serves to know how many inputs are created with same name.
     * 
     * Used internally.
     *     
     * @var array 
     */
    protected $inputsCounts = array();
    
    /**
     * Register of all created ids which are not assigned to any input for the moment
     * 
     *      {inputName} => array(
     *          {inputId1},
     *          {inputId2},
     *          ...
     *      )
     * 
     * Used internally.
     * 
     * @var array 
     */
    protected $freeInputIds = array();
    
    /**
     * Register of all created ids which are not assigned to any label for the moment
     * as its for parameter and also used to as its id_label
     * 
     *      {inputName} => array(
     *          {inputId1},
     *          {inputId2},
     *          ...
     *      )
     * 
     * Used internally.
     * 
     * @var array 
     */
    protected $freeLabelInputIds = array();
    
    /**
     * Register of all created ids which are not assigned to any errors block for the moment
     * 
     *      {inputName} => array(
     *          {inputId1},
     *          {inputId2},
     *          ...
     *      )
     * 
     * Used internally.
     * 
     * @var array 
     */
    protected $freeErrorsInputIds = array();

    /**
     * Register of all created ids which are not assigned to any warnings block for the moment
     * 
     *      {inputName} => array(
     *          {inputId1},
     *          {inputId2},
     *          ...
     *      )
     * 
     * Used internally.
     * 
     * @var array 
     */
    protected $freeWarningsInputIds = array();

    /**
     * Register of all used inputIds, like:
     * 
     *      array(
     *          {inputNameA} => array(
     *              {inputIdA1}
     *              {inputIdA2}
     *          )
     *          {inputNameB} => array(
     *              {inputIdB1}
     *              {inputIdB2}
     *          ),
     *          ...
     *      )
     * 
     * Serves to retrieve id of created input.
     * 
     * @var array
     */
    protected $inputIds = array();
    
    /**
     * Cache of normalized fields used by FormHelper::normalizeField()
     * 
     * Used internally.
     *
     * @var array 
     */
    protected $normalizedFields = array();
        
    /**
     * Count of all instances of Form class.
     * Serves to assign unique $formId for each of created instances
     * Used Internally.
     * 
     * @static
     * @var int
     */
    static protected $formsCount = 0;
    
    /**
     * Form class instance unique id.
     * Serves to create unique ids for conflicting input names in two diferent forms
     * Used Internally.
     * 
     * @var int 
     */
    protected $formId = null;
    
    /**
     * List of empty text css classes for which js event initialization scripts has
     * been generated like: 
     * 
     *      array(
     *          'input-empty-text' => true,
     *          'my-input-empty-text' => true,
     *          ...
     *      )
     * 
     * @var array 
     */
    static protected $emptyTextJs = array();
    
    /**
     * List of files (css,js) which has been already attached like:
     * 
     *      array(
     *          '/app/vendors/ckeditor/ckeditor.js' => true,
     *          ...
     *      )
     * 
     * @var array
     */
    static protected $attachedFiles = array();
    
    /**
     * List of scripts (css,js) which has been already attached like:
     * 
     *      array(
     *          'jQuery(function(){new Run.App.Hints({selector:".input-hint"});});' => true,
     *          ...
     *      )
     * 
     * @var array
     */
    static protected $attachedScripts = array();
    
    /**
     * Suffix used to change names of transfer field hidden inputs serving just to 
     * transfer the value of display and image inputs, which do not submit its values and
     * they need some auxiliary field name to transfer their value between
     * requests. The original field name can not be used because of conflicts
     * with possible regular input for the concerned field.
     */
    protected $transferFieldSuffix = '__transfer__';
    
    /**
     * List of allowed attributes used by method FormHelper::attributes()
     * 
     * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input
     * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/select
     * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/textarea
     * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes
     * 
     * @var array 
     */
    protected $allowedAttributes = array(
        // input, select and textarea  attributes
        'accept' => true, 'accesskey' => true, 'autocapitalize' => true, 'autocomplete' => true, 'autofocus' => true, 'capture' => true, 'checked' => true, 'class' => true, 'cols' => true, 'contenteditable' => true, 'contextmenu' => true, 'dir' => true, 'disabled' => true, 'draggable' => true, 'dropzone' => true, 'form' => true, 'formaction' => true, 'formenctype' => true, 'formmethod' => true, 'formnovalidate' => true, 'formtarget' => true, 'height' => true, 'hidden' => true, 'id' => true, 'inputmode' => true, 'is' => true, 'itemid' => true, 'itemprop' => true, 'itemref' => true, 'itemscope' => true, 'itemtype' => true, 'lang' => true, 'list' => true, 'max' => true, 'maxlength' => true, 'min' => true, 'minlength' => true, 'multiple' => true, 'name' => true, 'pattern' => true, 'placeholder' => true, 'readonly' => true, 'required' => true, 'rows' => true, 'size' => true, 'slot' => true, 'spellcheck' => true, 'src' => true, 'step' => true, 'style' =>  true, 'tabindex' => true, 'title' => true, 'translate' => true, 'type' => true, 'usemap' => true, 'value' => true, 'width' => true, 'wrap' => true,
        // events
        'onabort' => true, 'onautocomplete' => true, 'onautocompleteerror' => true, 'onblur' => true, 'oncancel' => true, 'oncanplay' => true, 'oncanplaythrough' => true, 'onchange' => true, 'onclick' => true, 'onclose' => true, 'oncontextmenu' => true, 'oncuechange' => true, 'ondblclick' => true, 'ondrag' => true, 'ondragend' => true, 'ondragenter' => true, 'ondragexit' => true, 'ondragleave' => true, 'ondragover' => true, 'ondragstart' => true, 'ondrop' => true, 'ondurationchange' => true, 'onemptied' => true, 'onended' => true, 'onerror' => true, 'onfocus' => true, 'oninput' => true, 'oninvalid' => true, 'onkeydown' => true, 'onkeypress' => true, 'onkeyup' => true, 'onload' => true, 'onloadeddata' => true, 'onloadedmetadata' => true, 'onloadstart' => true, 'onmousedown' => true, 'onmouseenter' => true, 'onmouseleave' => true, 'onmousemove' => true, 'onmouseout' => true, 'onmouseover' => true, 'onmouseup' => true, 'onmousewheel' => true, 'onpause' => true, 'onplay' => true, 'onplaying' => true, 'onprogress' => true, 'onratechange' => true, 'onreset' => true, 'onresize' => true, 'onscroll' => true, 'onseeked' => true, 'onseeking' => true, 'onselect' => true, 'onshow' => true, 'onsort' => true, 'onstalled' => true, 'onsubmit' => true, 'onsuspend' => true, 'ontimeupdate' => true, 'ontoggle' => true, 'onvolumechange' => true, 'onwaiting' => true,        
    );
        
    
    /**
     * Assignes a value to FormHelper::$data
     * 
     * NOTE: To launch easily the form test data loading use folowing bookmarklet:
     * javascript:(function(){var g='_FormHelperUseTestData_=1';if(location.href.split('?').length>1){g='&'+g;}else{g='?'+g;}location.href+=g;})();
     * 
     * @param array $data 
     * @param array $testData Optional. Test data to be used if $_GET['_FormHelperUseTestData_'] 
     *              is set to nonempty value. Defaults to NULL.
     */
    public function setPropertyData($data, $testData = null) {
        if ($data !== null && !is_array($data)) {
            throw new Exception('Data must be an array');
        }
        if ($testData !== null && !is_array($testData)) {
            throw new Exception('Test data must be an array');
        }
        // apply test data if required
        if (!empty($_GET['_FormHelperUseTestData_'])) {
            foreach ($testData as $k => $v) {
                if (!array_key_exists($k, $data)) {
                    $data[$k] = $v;
                }
            }
        }
        $this->data = (array)$data;
    }
    
    /**
     * Assignes a value to FormHelper::$data
     * 
     * @param array $values 
     */
    public function setPropertyDefaultValues($values) {
        if ($values !== null && !is_array($values)) {
            throw new Exception('Default values must be an array');
        }
        $this->defaultValues = (array)$values;
    }
    
    /**
     * Assignes a value to FormHelper::$errors
     * 
     * @param array $errors 
     */
    public function setPropertyErrors($errors) {
        if ($errors !== null && !is_array($errors)) {
            throw new Exception('Errors must be an array');
        }
        $this->errors = (array)$errors;
    }
    
    /**
     * Assignes a value to FormHelper::$warnings
     * 
     * @param array $warnings 
     */
    public function setPropertyWarnings($warnings) {
        if ($warnings !== null && !is_array($warnings)) {
            throw new Exception('Warnings must be an array');
        }
        $this->warnings = (array)$warnings;
    }
    
    /**
     * Assignes a value to FormHelper::$disabled
     * 
     * @param array $disabled 
     */
    public function setPropertyDisabled($disabled) {
        if ($disabled !== null && !is_array($disabled)) {
            throw new Exception('Disabled must be an array');
        }
        $this->disabled = (array)$disabled;
    }
        
    /**
     * Assignes a value to FormHelper::$required
     * 
     * @param array $required 
     */
    public function setPropertyRequired($required) {
        if ($required !== null && !is_array($required)) {
            throw new Exception('Required must be an array');
        }
        $this->required = (array)$required;
    }
    
    /**
     * Assignes a value to FormHelper::$translated
     * 
     * @param array $translated 
     */
    public function setPropertyTranslated($translated) {
        if ($translated !== null && !is_array($translated)) {
            throw new Exception('Translated must be an array');
        }
        $this->translated = (array)$translated;
    }
    
    /**
     * Assignes a value to FormHelper::$lang
     * 
     * @param string $lang 
     */
    public function setPropertyLang($lang) {
        if ($lang !== null && !is_string($lang)) {
            throw new Exception('Lang must be a string');
        }
        $this->lang = $lang;
    }
    
    /**
     * Assignes a value to FormHelper::$separator
     * 
     * @param string $separator 
     */
    public function setPropertySeparator($separator) {
        if ($separator !== null && !is_string($separator)) {
            throw new Exception('Separator must be a string');
        }
        $this->separator = $separator;
    }
    
    /**
     * Assignes a value to FormHelper::$inputDefaults
     * 
     * @param array $defaults 
     */
    public function setPropertyInputDefaults($defaults) {
        if (!is_array($defaults)) {
            throw new Exception('Input defaults must be an array');
        }
        if (
            array_key_exists('type', $defaults)
            && !is_string($defaults['type'])
        ) {
            throw new Exception('Default input type must be a string');
        }
        if (
            array_key_exists('class', $defaults)
            && !is_string($defaults['class'])
        ) {
            throw new Exception('Default input class must be a string');
        }
        if (
            array_key_exists('textClass', $defaults)
            && !is_string($defaults['textClass'])
        ) {
            throw new Exception('Default textClass must be a string');
        }
        if (
            array_key_exists('toggleClass', $defaults)
            && !is_string($defaults['toggleClass'])
        ) {
            throw new Exception('Default toggleClass must be a string');
        }
        if (
            array_key_exists('attachErrors', $defaults)
            && !empty($defaults['attachErrors'])
            && !is_bool($defaults['attachErrors'])
            && $defaults['attachErrors'] !== 'scrollOnly'
        ) {
            throw new Exception('Input attachErrors must be a boolean or empty value or string "scrollOnly"');
        }
        if (
            array_key_exists('attachWarnings', $defaults)
            && !empty($defaults['attachWarnings'])
            && !is_bool($defaults['attachWarnings'])
        ) {
            throw new Exception('Input attachWarnings must be a boolean or empty value');
        }
        if (
            array_key_exists('template', $defaults)
            && !empty($defaults['template'])
            && !is_string($defaults['template'])
        ) {
            throw new Exception('Input template must be a string or empty value');
        }
        if (
            array_key_exists('toggleTemplate', $defaults)
            && !empty($defaults['toggleTemplate'])
            && !is_string($defaults['toggleTemplate'])
        ) {
            throw new Exception('Input toggleTemplate must be a string or empty value');
        }
        if (
            array_key_exists('requiredMark', $defaults)
            && !empty($defaults['requiredMark'])
            && !is_string($defaults['requiredMark'])
        ) {
            throw new Exception('Input requiredMark must be a string or empty value');
        }
        if (
            array_key_exists('translatedMark', $defaults)
            && !empty($defaults['translatedMark'])
            && !is_string($defaults['translatedMark'])
        ) {
            throw new Exception('Input translatedMark must be a string or empty value');
        }
        $this->inputDefaults = array_merge($this->inputDefaults, $defaults);
    }
                
    /**
     * Assignes a value to FormHelper::$labelDefaults
     * 
     * @param array $defaults 
     */
    public function setPropertyLabelDefaults($defaults) {
        if (!is_array($defaults)) {
            throw new Exception('Label defaults must be an array');
        }
        if (
            array_key_exists('class', $defaults)
            && !is_string($defaults['class'])
        ) {
            throw new Exception('Label class must be a string');
        }
        if (
            array_key_exists('separator', $defaults)
            && !empty($defaults['separator'])
            && !is_string($defaults['separator'])
        ) {
            throw new Exception('Label separator must be a string or empty value');
        }
        if (
            array_key_exists('requiredMark', $defaults)
            && !empty($defaults['requiredMark'])
            && !is_string($defaults['requiredMark'])
        ) {
            throw new Exception('Label requiredMark must be a string or empty value');
        }
        if (
            array_key_exists('translatedMark', $defaults)
            && !empty($defaults['translatedMark'])
            && !is_string($defaults['translatedMark'])
        ) {
            throw new Exception('Label translatedMark must be a string or empty value');
        }
        if (
            array_key_exists('template', $defaults)
            && !empty($defaults['template'])
            && !is_string($defaults['template'])
        ) {
            throw new Exception('Label template must be a string or empty value');
        }
        $this->labelDefaults = array_merge($this->labelDefaults, $defaults);
    }
    
    /**
     * Assignes a value to FormHelper::$errorsDefaults
     * 
     * @param array $defaults 
     */
    public function setPropertyErrorsDefaults($defaults) {
        if (!is_array($defaults)) {
            throw new Exception('Input defaults must be an array');
        }
        if (
            array_key_exists('plainText', $defaults)
            && !is_bool($defaults['plainText'])
        ) {
            throw new Exception('Errors plainText option must be a boolean');
        }
        if (
            array_key_exists('class', $defaults)
            && !is_string($defaults['class'])
        ) {
            throw new Exception('Default errors div class must be a string');
        }
        if (
            array_key_exists('errorClass', $defaults)
            && !is_string($defaults['errorClass'])
        ) {
            throw new Exception('Default error div class must be a string');
        }
        $this->errorsDefaults = array_merge($this->errorsDefaults, $defaults);
    }
    
    /**
     * Assignes a value to FormHelper::$warningsDefaults
     * 
     * @param array $defaults 
     */
    public function setPropertyWarningsDefaults($defaults) {
        if (!is_array($defaults)) {
            throw new Exception('Input defaults must be an array');
        }
        if (
            array_key_exists('plainText', $defaults)
            && !is_bool($defaults['plainText'])
        ) {
            throw new Exception('Warnings plainText option must be a boolean');
        }
        if (
            array_key_exists('class', $defaults)
            && !is_string($defaults['class'])
        ) {
            throw new Exception('Default warnings div class must be a string');
        }
        if (
            array_key_exists('warningClass', $defaults)
            && !is_string($defaults['warningClass'])
        ) {
            throw new Exception('Default warning div class must be a string');
        }
        $this->warningsDefaults = array_merge($this->warningsDefaults, $defaults);
    }
    
    /**
     * Assignes a value to FormHelper::$emptyTextDefaults
     * 
     * @param array $defaults 
     */
    public function setPropertyEmptyTextDefaults($defaults) {
        if (!is_array($defaults)) {
            throw new Exception('Input defaults must be an array');
        }
        if (
            array_key_exists('class', $defaults)
            && !is_string($defaults['class'])
        ) {
            throw new Exception('Default empty text div class must be a string');
        }
        if (
            array_key_exists('wrapper', $defaults)
            && !is_bool($defaults['wrapper'])
        ) {
            throw new Exception('Empty text wrapper option must be a boolean');
        }
        if (
            array_key_exists('wrapperClass', $defaults)
            && !is_string($defaults['wrapperClass'])
        ) {
            throw new Exception('Default empty text wrapper div class must be a string');
        }
        $this->emptyTextDefaults = array_merge($this->emptyTextDefaults, $defaults);
    }
    
    /**
     * Assignes a value to FormHelper::$scrollToError
     * 
     * @param bool $scroll 
     */
    public function setPropertyScrollToError($scroll) {
        if (!is_bool($scroll)) {
            throw new Exception('Scroll to error must be a boolean value');
        }
        $this->scrollToError = $scroll;
    }
    
    /**
     * Assignes a value to FormHelper::$compatibility
     * 
     * @param string $compatibility 
     */
    public function setPropertyCompatibility($compatibility) {
        $availableValues = array('bootstrap' => true, 'mdc' => true);
        if (!empty($compatibility) && !isset($availableValues[$compatibility])) {
            throw new Exception(__e(__FILE__, 'Compatibility must be one of %s or an empty value', implode(', ', array_keys($availableValues))));
        }
        $this->compatibility = $compatibility;
    }
    
    /**
     * Assignes a value to FormHelper::$nonTranslatedProcessing
     * 
     * @param string $processing Possible values are 'ignore' and 'disable' or empty value (NULL, FALSE).
     *      If set to 'ignore' then the field is ignored (nothing is generated)
     *      If set to disabled then the field is disabled. If an empty value then the 
     *      field is trated normally.
     */
    public function setPropertyNonTranslatedProcessing($processing) {
        if (!empty($processing)) {
            if (!is_string($processing)) {
                throw new Exception('Non translated processing must be a string or en empty value');
            }
            $processing = strtolower($processing);
            if (
                $processing !== 'ignore'
                && $processing !== 'disable'
            ) {
                throw new Exception('Invalid value of non translated processing. Use either \'ignore\' or \'disable\'');
            }
        }
        else {
            $processing = null;
        }
        $this->nonTranslatedProcessing = $processing;
    }
        
    /**
     * Assignes a value to FormHelper::$useDataWrapper
     * 
     * @param bool|string $useDataWrapper 
     */
    public function setPropertyUseDataWrapper($useDataWrapper) {
        if (
            !is_bool($useDataWrapper)
            && !is_string($useDataWrapper)
        ) {
            throw new Exception('Property useDataWrapper must be either a boolean or a string value');
        }
        $this->useDataWrapper = $useDataWrapper;
    }
    
    /**
     * Initilizates new instance of Form object
     * 
     * @param type $options Following options can be provided on object construction:
     *      - 'data' (array) Form data. If set then it is used as default 'data' option
     *      - 'testData' (array) Test data to be used if $_GET['_FormHelperUseTestData_'] is set to nonempty value. 
     *      - 'errors' (array) Form errors. If set then it is used as default 'errors' option
     *      - 'warnings' (array) Form warnings. If set then it is used as default 'warnings' option
     *      - 'disabled' (array) Disabled form fields
     *      - 'required' (array) Form required fields. If set then it is used as default 'required' option
     *      - 'translated' (array) Form translated fields. These icons are marked by language marker
     *          according to actual value of 'lang' option (here below). If the 'lang' option
     *          is empty then language markers are not created and 'translated' options is ignored.
     *      - 'lang' (string) Lang of form translated fields. See also 'translated' option
     *          here above.
     *      - 'separator' (string) Field path separator used to serarate nested field levels, e.g. 'User.name'. 
     *          Sometimes it is necessry to change it if the field name itself contains the separator character,
     *          e.g. in case of setting (Setting/App.email) or right names (App.Settings.admin_edit). 
     *          Defaults to '.'. 
     *      - 'defaultValues' (array) Default values of form inputs to be used to initialize
     *          inputs on the form opening (when the form normaly is empty)
     *      - 'inputDefaults' (array) Array of input default options which are: 'type',
     *          'class', 'textClass', 'toggleClass', 'template', 'toggleTemplate', 'attachErrors',
     *          'attachWarnings'
     *      - 'labelDefaults' (array) Array of label default options which are: 'class', 
     *          'requiredMark', 'separator'
     *      - 'errorsDefaults' (array) Array of errors default options which are: 'class', 
     *          'errorClass', 'plainText'
     *      - 'warningsDefaults' (array) Array of warnings default options which are: 'class', 
     *          'warningClass', 'plainText'
     *      - 'emptyTextDefaults' (array) Array of errors default options which are: 'class'
     *      - 'scrollToError' (bool) If TRUE then form is autoscrolled to first occured error
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value.
     *          If 'bootstrap' then there are added classes of bootstrap css (see http://getbootstrap.com/). 
     *          Classes are added to text inputs ('form-control'), label ('control-label'), 
     *          ridt classes ('has-error') and to errors ('help-block'). There is also added 
     *          wrapping block <div class="form-group {ridtClasses}"><div>.
     *          If 'mdc' then there are added classes of material design components css (see https://material.io/components/web/). 
     *          Classes are added to text inputs, labe, ridt classes  and to errors. 
     *          There is also added wrapping block.
     *      - 'nonTranslatedProcessing' (string) Possible values are 'ignore' and 'disable' or FALSE.
     *          If set to 'ignore' then the field is ignored (nothing is generated)
     *          If set to disabled then the field is disabled. If FASE then the field is treated normally.
     *      - 'useDataWrapper' (bool|string) If TRUE then the input name is namespaced by 'data' prefix,
     *          e.g. for field 'User.name' the input name data[User][name] is created.
     *          If a string, e.g. 'my[wrapper]' then the input name is namespaced by provided 
     *          prefix, e.g. input name my[wrapper][User][name] is created.
     *          If FALSE then the input name is not namespaced, e.g. input name User[name] 
     *          is created. Defaults to TRUE.
     * 
     *          
     * @return void
     */
    public function __construct($options = array()) {
        $defaults = array(
            'data' => array(),
            'testData' => array(),
            'defaultValues' => array(),
            'errors' => array(),
            'warnings' => array(),
            'disabled' => array(),
            'required' => array(),
            'translated' => array(),
            'lang' => null,
            'separator' => null,
            'inputDefaults' => array(),
            'labelDefaults' => array(),
            'errorsDefaults' => array(),
            'warningsDefaults' => array(),
            'emptyTextDefaults' => array(),
            'scrollToError' => null,
            'compatibility' => null,
            'nonTranslatedProcessing' => null,
            'useDataWrapper' => null,
            'jQueryJsFile' => null,
            'jQueryUiJsFile' => null,
        );
        $options = array_merge($defaults, $options);
        
        if (!empty($options['data']) || !empty($options['testData'])) {
            $this->setPropertyData($options['data'], $options['testData']);
        }
        if ($options['defaultValues']) {
            $this->setPropertyDefaultValues($options['defaultValues']);
        }
        if ($options['errors']) {
            $this->setPropertyErrors($options['errors']);
            if (App::getPropertyDebug()) {
                App::debug($options['errors'], 'FORM VALIDATION ERRORS');
            }
        }
        if ($options['warnings']) {
            $this->setPropertyWarnings($options['warnings']);
            if (App::getPropertyDebug()) {
                App::debug($options['warnings'], 'FORM VALIDATION WARNINGS');
            }
        }
        if ($options['disabled']) {
            $this->setPropertyDisabled($options['disabled']);
        }
        if ($options['required']) {
            $this->setPropertyRequired($options['required']);
        }
        if ($options['translated']) {
            $this->setPropertyTranslated($options['translated']);
        }
        if ($options['lang']) {
            $this->setPropertyLang($options['lang']);
        }
        if ($options['separator']) {
            $this->setPropertySeparator($options['separator']);
        }
        if ($options['inputDefaults']) {
            $this->setPropertyInputDefaults($options['inputDefaults']);
        }
        if ($options['labelDefaults']) {
            $this->setPropertyLabelDefaults($options['labelDefaults']);
        }
        if ($options['errorsDefaults']) {
            $this->setPropertyErrorsDefaults($options['errorsDefaults']);
        }
        if ($options['warningsDefaults']) {
            $this->setPropertyWarningsDefaults($options['warningsDefaults']);
        }
        if ($options['emptyTextDefaults']) {
            $this->setPropertyEmptyTextDefaults($options['emptyTextDefaults']);
        }
        if ($options['scrollToError'] !== null) {
            $this->setPropertyScrollToError($options['scrollToError']);
        }
        $this->setPropertyCompatibility($options['compatibility']);
        if ($options['nonTranslatedProcessing'] !== null) {
            $this->setPropertyNonTranslatedProcessing($options['nonTranslatedProcessing']);
        }
        if ($options['useDataWrapper'] !== null) {
            $this->setPropertyUseDataWrapper($options['useDataWrapper']);
        }
        if ($options['jQueryJsFile']) {
            $this->jQueryJsFile = $options['jQueryJsFile'];
        }
        if ($options['jQueryUiJsFile']) {
            $this->jQueryUiJsFile = $options['jQueryUiJsFile'];
        }
        
        // assign an unique id to this Form instance
        self::$formsCount++;
        $this->formId = self::$formsCount;
    }
        
    /**
     * Returns field input name for given $field like this:
     *      - my_field            ->  data[my_field]              // data is wrapping array
     *      - my.nested.field   ->  data[my][nested][field]  // Use dot to create nested structure
     *      - my.nested.field.   ->  data[my][nested][field][]  // Use end dot to create new array item
     *      - my.nested.field.1   ->  data[my][nested][field][1]  // You can of course force the array indexes
     *      - my.nested.field   ->  my[wrapper][my][nested][field]  // You can set FormHelper::$useDataWrapper to your own wrapper
     * 
     * NOTE: The field name cannot contain '[' and ']' to not confuse the name array structure
     * 
     * @param string $field
     *        
     * @return string 
     */
    protected function createInputName($field) {
        // check the cache
        if (isset($this->fields[(int)$this->useDataWrapper][$field])) {
            return $this->fields[(int)$this->useDataWrapper][$field];
        }
        // create input name for a new field
        $inputName = $field;
        if (!empty($this->useDataWrapper)) {
            if (is_string($this->useDataWrapper)) {
                $inputName = $this->useDataWrapper . '[' . str_replace($this->separator, '][', $inputName) . ']';
            }
            else {
                $inputName = 'data[' . str_replace($this->separator, '][', $inputName) . ']';
            }
        }
        else {
            $parts = explode($this->separator, $inputName);
            $inputName = array_shift($parts);
            if (!empty($parts)) {
                $inputName .= '[' . implode('][', $parts) . ']';
            }
        }
        $this->fields[(int)$this->useDataWrapper][$field] = $inputName;
        return $inputName;
    }
    
    /**
     * Return name of transfer input for given field like this:
     *      - my_field            ->  data[my_field__transfer__]              
     *      - my.nested.field   ->  data[my][nested][field__transfer__]  
     *      - my.nested.field.1   ->  data[my][nested][field][1__transfer__]  
     *      - my.nested.field   ->  my[wrapper][my][nested][field__transfer__]
     * 
     * This is used in case of display inputs (display, image) to transfer displayed
     * value between form submitions
     * 
     * @param string $field
     * 
     * @return string
     */
    protected function createTransferInputName ($field) {
        return $this->createInputName($field . $this->transferFieldSuffix);
    }
    
    /**
     * Creates a new css id from given $inputName 
     * 
     * @param string $inputName
     * 
     * @return string 
     */
    protected function createInputId($inputName) {
        if (empty($this->inputNameIdsCounts[$inputName])) {
            $this->inputNameIdsCounts[$inputName] = 0;
        }
        $this->inputNameIdsCounts[$inputName]++;
        // replace all non alphanumeric charactes by '_' to avoid the same id for data[key] and data[key][]
        $id = '_' . rtrim(strtolower(preg_replace('/[^\d\w]/', '_', $inputName)), '_')
            . '_input_' . $this->inputNameIdsCounts[$inputName]
            . '_' . $this->formId;
        return $id;
    }
        
    /**
     * Returns input css id
     * 
     * @param string $inputName Input name
     * @param int $id Explicit id to be used for input. Default to NULL, means the 
     *      id is autocreated.
     * 
     * @return string Input tag css id 
     */
    protected function assignInputId($inputName, $id = null) {
        // store explicit id to ids waiting for label
        if ($id) {
            $this->freeLabelInputIds[$inputName][] = $id;
            $this->freeErrorsInputIds[$inputName][] = $id;
            $this->freeWarningsInputIds[$inputName][] = $id;
        }
        // look for id created by label and waiting for input
        elseif (!empty($this->freeInputIds[$inputName])) {
            $id = array_shift($this->freeInputIds[$inputName]);
        }
        // assign a brand new id to input and store it to ids waiting for label
        else {
            $id = $this->createInputId($inputName);
            $this->freeLabelInputIds[$inputName][] = $id;
            $this->freeErrorsInputIds[$inputName][] = $id;
            $this->freeWarningsInputIds[$inputName][] = $id;
        }
        return $id;
    }
    
    /**
     * Returns label corresponding input css id which can be used as a for attribute of 
     * label tag
     * 
     * @param string $inputName Name of corresponding input
     * 
     * @return string Label tag corresponding input css id
     */
    protected function assignLabelInputId($inputName) {
        if (!empty($this->freeLabelInputIds[$inputName])) {
            $for = array_shift($this->freeLabelInputIds[$inputName]);
        }
        else {
            $for = $this->createInputId($inputName);
            $this->freeInputIds[$inputName][] = $for;
            $this->freeErrorsInputIds[$inputName][] = $for;
            $this->freeWarningsInputIds[$inputName][] = $for;
        }
        return $for;
    }
    
    /**
     * Returns errors block corresponding input css id
     * 
     * @param string $inputName Name of corresponding input
     * 
     * @return string Errors block corresponding input css id
     */
    protected function assignErrorsInputId($inputName) {
        if (!empty($this->freeErrorsInputIds[$inputName])) {
            $id = array_shift($this->freeErrorsInputIds[$inputName]);
        }
        else {
            $id = $this->createInputId($inputName);
            $this->freeLabelInputIds[$inputName][] = $id;
            $this->freeInputIds[$inputName][] = $id;
            $this->freeWarningsInputIds[$inputName][] = $id;
        }
        return $id;
    }
    
    /**
     * Returns warnings block corresponding input css id
     * 
     * @param string $inputName Name of corresponding input
     * 
     * @return string Warnings block corresponding input css id
     */
    protected function assignWarningsInputId($inputName) {
        if (!empty($this->freeWarningsInputIds[$inputName])) {
            $id = array_shift($this->freeWarningsInputIds[$inputName]);
        }
        else {
            $id = $this->createInputId($inputName);
            $this->freeLabelInputIds[$inputName][] = $id;
            $this->freeInputIds[$inputName][] = $id;
            $this->freeErrorsInputIds[$inputName][] = $id;
        }
        return $id;
    }
    
    /**
     * Returns a count of existing inputs for given $inputName
     * 
     * @param string $inputName Input name
     * 
     * @return int 
     */
    protected function getInputsCount($inputName) {
        //rblb//return (int)(Sanitize::value($this->inputNameIdsCounts[$inputName]));
        return (int)(Sanitize::value($this->inputsCounts[$inputName]));
    }
    
    /**
     * Returns id of existing input for provided field.
     * If more than one input is created for given field (radios, chceckboxes)
     * then the ids are retrieved in FIFO order
     * 
     * @param string $field
     * @return string If input does not exist then NULL is returned.
     */
    public function inputId($field) {
        $id = null;
        $inputName = $this->createInputName($field);
        if (!empty($this->inputIds[$inputName])) {
            $id = array_shift($this->inputIds[$inputName]);
        }
        return $id;
    }
        
    /**
     * Checks if given field has error messages in FormHelper::$errors and if yes then 
     * returns them.
     * 
     * @param string $field Name of field to check error messages for.
     * 
     * @return mixed Array containing the field error messages or FALSE if nothing
     *      found for given field.
     */
    public function fieldErrors($field) {
        if (
            !empty($this->errors)
            && is_array($this->errors) 
            && ($fieldErrors = Arr::getPath($this->errors, $field, $this->separator))
        ) {
            return $fieldErrors;
        }
        return false;
    }
    
    /**
     * Checks if given field has warning messages in FormHelper::$warnings and if yes then 
     * returns them.
     * 
     * @param string $field Name of field to check warning messages for.
     * 
     * @return mixed Array containing the field warning messages or FALSE if nothing
     *      found for given field.
     */
    public function fieldWarnings($field) {
        if (
            !empty($this->warnings)
            && is_array($this->warnings) 
            && ($fieldWarnings = Arr::getPath($this->warnings, $field, $this->separator))
        ) {
            // if some of messages are placed also in errors then remove them from warnings
            if (($fieldErrors = $this->fieldErrors($field))) {
                $fieldWarnings = array_diff($fieldWarnings, $fieldErrors);
            }
            return $fieldWarnings;
        }
        return false;
    }
    
    /**
     * Checks if the field is included in FormHelper::$disabled array.
     * 
     * @param string $field
     * @param bool $options Input options plus following:
     *      - 'isTranslated' (bool) Is the field translated?. If not provided or 
     *          NULL then it's value is resolved by FormHelper::isTranslated().
     *          This argument is processed only if FormHelper::$nonTranslatedProcessing property
     *          is set to 'disable'. Defaults to NULL.
     * 
     * @return bool 
     */
    public function isDisabled($field, $options = array()) {
        $options = array_merge(array(
            'isTranslated' => null,
        ), $options);
        if (
            $this->nonTranslatedProcessing === 'disable'
            && $options['isTranslated'] === null
        ) {
            $options['isTranslated'] = $this->isTranslated($field);
        }
        return (
            !empty($options['disabled'])
            ||
            !empty($this->disabled)
            && is_array($this->disabled) 
            && Arr::getPath($this->disabled, $field, $this->separator)
            ||
            $this->nonTranslatedProcessing === 'disable'
            && $options['isTranslated'] === false
            && !$this->hasDefaultLang()
        );
    }
            
    /**
     * Checks if the field is included in FormHelper::$required array.
     * 
     * @param string $field
     * 
     * @return bool 
     */
    public function isRequired($field) {
        $field = $this->normalizeField($field);
        return (
            !empty($this->required)
            && is_array($this->required) 
            && Arr::getPath($this->required, $field, $this->separator)
        );
    }
    
    /**
     * Returns normalized version of field.
     * 
     * E.g. 'MyModel..my_field', 'MyModel.2.my_field', 'MyModel.my_field.1'
     * are normalized to 'MyModel.my_field'
     * 
     * @param string $field
     * 
     * @return string Normalized field
     */
    protected function normalizeField($field) {
        if (!isset($this->normalizedFields[$field])) {
            $normalizedField = trim($field, $this->separator);
            $normalizedFieldParts = explode($this->separator, $normalizedField);
            $tmp = array();
            foreach ($normalizedFieldParts as $part) {
                if (
                    !empty($part)
                    && !Validate::intNumber($part)
                ) {
                    $tmp[] = $part;
                }
            }
            $normalizedField = implode($this->separator, $tmp);
            $this->normalizedFields[$field] = $normalizedField;
        }
        return $this->normalizedFields[$field];
    }
    
    /**
     * Checks if the field is included in FormHelper::$translated array and if it
     * is actually translated according to FormHelper::$lang. If the FormHelper::$lang 
     * is empty then it is not translated.
     * 
     * @param string $field
     * 
     * @return bool 
     */
    public function isTranslated($field) {
        return (
            !empty($this->lang)
            && !empty($this->translated)
            && (
                $this->translated === true
                ||
                is_array($this->translated) 
                && Arr::getPath($this->translated, $field, $this->separator)
            )
        );
    }
    
    /**
     * Is the actual form labg set to a default one (DEFAULT_LANG)
     * 
     * @return boolean
     */
    protected function hasDefaultLang() {
        return empty($this->lang) || $this->lang === DEFAULT_LANG;
    }
    
    /**
     * Retrieves html element attributes from $options array and creates
     * a string which can be directly inserted to generated html.
     *  
     * As html element attributes are considered all items in input $options 
     * which are not defined in $defaults array and they have scalar value and their 
     * name is either introduced in FormHelper::$allowedAttributes or starts by 'data-'
     * or 'aria-'.
     * 
     * E.g. 
     *      $defaults = array('required' => false, 'data' => array())
     *      $options = array('required' => true, 'class' => 'my-class', 'id' => 'myid')
     * 
     *      return value: ' class="my-class" id="myid"' (notice the leading space!)
     * 
     * @param array $options
     * @param array $defaults
     * @param array $explicit Explicit attributes array
     * 
     * @return string Attributes string containing leading space. If no attributes are
     *      generated then empty string.
     */
    protected function attributes($options, $defaults, $explicit = array()) {
        // check for additional attributes
        $attributes = array_diff_key($options, $defaults);
        if (!empty($explicit)) {
            $attributes = Html::mergeAttributes($attributes, $explicit);
        }
        $attributesHtml = '';
        if (!empty($attributes)) {
            foreach ($attributes as $name => $value) {
                if (
                    !is_scalar($value) 
                    && $value !== null
                    ||
                    !isset($this->allowedAttributes[$name])
                    && ($prefix = substr($name, 0, 5)) !== 'data-'
                    && $prefix !== 'aria-'
                ) {
                    continue;
                }
                $attributesHtml .= ' ' . $name;
                if ($value !== null) {
                    $attributesHtml .= '="' . $value . '"';
                }
            }
        }
        return $attributesHtml;
    }
    
    /**
     * Escapes html special chars in provided string. Used e.g. to escape string
     * in input value attributes
     * 
     * @param string $string
     * @return string
     */
    public function escapeHtmlSpecialChars($string) {
        return htmlspecialchars($string, ENT_COMPAT, 'UTF-8', true);
    }
    
    /**
     * Finds out if the given field is required, invalid or disabled and creates 
     * coresponding CSS classes string
     * 
     * ATTENTION: Found bool values are set back to those of $isRequired, $isInvalid and $isDisabled
     * inputs which are NULL at the call of method.
     * 
     * @param string $field
     * @param array $options Options of form input (checked for 'disabled' key)
     * @param bool& $isRequired Optional. Return value. TRUE if the provided field is required.
     * @param bool& $isInvalid Optional. Return value. TRUE if the provided field is invalid.
     * @param bool& $isDisabled Optional. Return value. TRUE if the provided field is disabled.
     * @param bool& $isTranslated Optional. Return value. TRUE if the provided field is translated.
     * 
     * @return string CSS classes string
     */
    protected function ridt($field, $options = array(), &$isRequired = null, &$isInvalid = null, &$isDisabled = null, &$isTranslated = null, &$hasWarning = null) {
        if ($isRequired === null) {
            $isRequired = $this->isRequired($field);
        }
        if ($isInvalid === null) {
            $isInvalid = (bool)$this->fieldErrors($field);
        }
        if ($hasWarning === null) {
            $hasWarning = (bool)$this->fieldWarnings($field);
        }
        if ($isTranslated === null) {
            $isTranslated = $this->isTranslated($field);
        }
        if ($isDisabled === null) {
            $isDisabled = $this->isDisabled($field, array_merge((array)$options, array(
                'isTranslated' => $isTranslated,
            )));
        }
        
        // set the classes
        $classes = '';
        if ($isRequired) {
            $classes .= ' required';
        }
        if ($isInvalid) {
            $classes .= ' invalid';
            if ($this->compatibility === 'bootstrap') {
                $classes .= ' has-error';
            }
        }
        if ($hasWarning) {
            if ($this->compatibility === 'bootstrap') {
                $classes .= ' has-warning';
            }
        }
        if ($isDisabled) {
            $classes .= ' disabled';
        }
        if ($isTranslated) {
            $classes .= ' translated translated-' . $this->lang;
        }
        
        return $classes;
    }
    
    /**
     * Creates html of error messages for given field
     * 
     * @param string $field Name of field to generate error messages for.
     * 
     * @param array $options Available options:
     *      - 'plainText' (bool) If TRUE then the error messages are returned in plain 
     *          text, concatenating all error messages for given field,  without any html markup.
     *      - 'class' (string) Errors block css class. Defaults to FormHelper::errorsDefaults['class'].
     *      - 'errorClass' (string) Error block css class. Defaults to FormHelper::errorsDefaults['errorClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'scrollOnly' (bool) If TRUE then ony scroll to error script is generated 
     *          (of course if allowed by FormHelper::$scrollToError). Defaults to FALSE.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *          
     * 
     * @return string Html code
     */
    public function errors($field, $options = array()) {
        $html = '';
        if (($fieldErrors = $this->fieldErrors($field))) {
            $defaults = array(
                'plainText' => false,
                'class' => null,   
                'errorClass' => null,   
                'id' => null,
                'scrollOnly' => false,
                'compatibility' => $this->compatibility,
                
                // Internal use:
                // used to pass what is known already and so optimize
                'inputId' => null,
                'inputName' => null,
            );     
            $options = array_merge($defaults, $this->errorsDefaults, $options);
            
            $html = '';
            if ($options['scrollOnly'] === false) {
                // check for plain text version
                if ($options['plainText']) {
                    $text = '';
                    foreach ((array)$fieldErrors as $error) {
                        $text .= trim($error) . ' ';
                    }
                    return $text;
                }

                if ($options['id'] === null) {
                    // find input id to create the errors block id
                    if ($options['inputId'] === null) {
                        // find input name
                        if (empty($options['inputName'])) {
                            $options['inputName'] = $this->createInputName($field);
                        }
                        $options['inputId'] = $this->assignErrorsInputId($options['inputName']);
                    }
                    $options['id'] = $options['inputId'] . '_errors';
                }

                // prepare erros id html
                $idHtml = ' id="' . $options['id'] . '"';

                // prepare css classes html
                $classHtml = '';
                if ($options['compatibility'] === 'bootstrap') {
                    $options['class'] = rtrim('help-block ' . $options['class']);
                }
                if (!empty($options['class'])) {
                    $classHtml = ' class="' . $options['class'] . '"';
                }
                $errorClassHtml = '';
                if (!empty($options['errorClass'])) {
                    $errorClassHtml = ' class="' . $options['errorClass'] . '"';
                }

                // prepare attributes html
                $attrsHtml = $this->attributes($options, $defaults);

                // create errors block html
                $html .= '<div' . $classHtml . $idHtml . $attrsHtml . '>';
                foreach ((array)$fieldErrors as $error) {
                    $html .= '<div' . $errorClassHtml . '>' . $error . '</div>';
                }
                $html .= "</div>";
            }
            $this->setScrollToErrorJs($options['inputId']);
        }
        return $html;
    }    
    
    /**
     * Creates html of warning messages for given field
     * 
     * @param string $field Name of field to generate warning messages for.
     * 
     * @param array $options Available options:
     *      - 'plainText' (bool) If TRUE then the warning messages are returned in plain 
     *          text, concatenating all warning messages for given field,  without any html markup.
     *      - 'class' (string) Warnings block css class. Defaults to FormHelper::warningsDefaults['class'].
     *      - 'warningClass' (string) Warning block css class. Defaults to FormHelper::warningsDefaults['warningClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *          
     * 
     * @return string Html code
     */
    public function warnings($field, $options = array()) {
        $html = '';
        if (($fieldWarnings = $this->fieldWarnings($field))) {
            $defaults = array(
                'plainText' => false,
                'class' => null,   
                'warningClass' => null,   
                'id' => null,
                'scrollOnly' => false,
                'compatibility' => $this->compatibility,
                
                // Internal use:
                // used to pass what is known already and so optimize
                'inputId' => null,
                'inputName' => null,
            );     
            $options = array_merge($defaults, $this->warningsDefaults, $options);
            
            $html = '';
            if ($options['scrollOnly'] === false) {
                // check for plain text version
                if ($options['plainText']) {
                    $text = '';
                    foreach ((array)$fieldWarnings as $warning) {
                        $text .= trim($warning) . ' ';
                    }
                    return $text;
                }

                if ($options['id'] === null) {
                    // find input id to create the warnings block id
                    if ($options['inputId'] === null) {
                        // find input name
                        if (empty($options['inputName'])) {
                            $options['inputName'] = $this->createInputName($field);
                        }
                        $options['inputId'] = $this->assignWarningsInputId($options['inputName']);
                    }
                    $options['id'] = $options['inputId'] . '_warnings';
                }

                // prepare erros id html
                $idHtml = ' id="' . $options['id'] . '"';

                // prepare css classes html
                $classHtml = '';
                if ($options['compatibility'] === 'bootstrap') {
                    $options['class'] = rtrim('help-block ' . $options['class']);
                }
                if (!empty($options['class'])) {
                    $classHtml = ' class="' . $options['class'] . '"';
                }
                $warningClassHtml = '';
                if (!empty($options['warningClass'])) {
                    $warningClassHtml = ' class="' . $options['warningClass'] . '"';
                }

                // prepare attributes html
                $attrsHtml = $this->attributes($options, $defaults);

                // create warnings block html
                $html .= '<div' . $classHtml . $idHtml . $attrsHtml . '>';
                foreach ((array)$fieldWarnings as $warning) {
                    $html .= '<div' . $warningClassHtml . '>' . $warning . '</div>';
                }
                $html .= "</div>";
            }
            $this->setScrollToErrorJs($options['inputId']);
        }
        return $html;
    }    
    
    /**
     * @param string $inputId
     */
    protected function setScrollToErrorJs($inputId) {
        if ($this->scrollToError) {
            // attach jQuery
            if (empty(self::$attachedFiles[$this->jQueryJsFile])) {
                App::setJsFiles($this->jQueryJsFile);
                self::$attachedFiles[$this->jQueryJsFile] = true;
            }
            // scroll also to inputId for case that error messages are hidden
            App::setJs(
                "if (typeof jQuery !== 'undefined') {" .
                    "(function($) {" .
                        "function scrollToError() {" .
                            "var context = window.document," .
                                "input = $('#{$inputId}', context)," .
                                "label = $('#{$inputId}_label', context)," .
                                "errors = $('#{$inputId}_errors', context)," .
                                "warnings = $('#{$inputId}_warnings', context)," .
                                "tops = []," .
                                "scrollTop," .
                                "i," .
                                "smartFormFields = $('.-run-sfo-fields', context);" .
                            "if (input.length && input.css('display') !== 'none') {" .
                                "tops.push(input.offset().top)" .
                            "}" .
                            "if (label.length && label.css('display') !== 'none') {" .
                                "tops.push(label.offset().top)" .
                            "}" .
                            "if (errors.length && errors.css('display') !== 'none') {" .
                                "tops.push(errors.offset().top)" .
                            "}" .
                            "if (warnings.length && warnings.css('display') !== 'none') {" .
                                "tops.push(warnings.offset().top)" .
                            "}" .
                            "if (tops.length) {" .
                                "scrollTop = tops.pop();" .
                                "for (i = 0; i < tops.length; i++) {" .
                                    //"console.log(tops[i]);" .   
                                    "if (scrollTop > tops[i]) {" .   
                                        "scrollTop = tops[i];" .   
                                    "}" .
                                "}" .
                                "if (smartFormFields.length) {" .
                                    "smartFormFields.animate({" .
                                        "scrollTop: scrollTop + smartFormFields.offset().top" .
                                    "}, 1000);" .                        
                                "}" .
                                "else {" .
                                    "$('html, body', context).animate({" .
                                        "scrollTop: scrollTop - Math.round($(window).height() / 5)" .
                                    "}, 1000);" .                        
                                "}" .
                            "}" .
                         "};".
                         "window.addEventListener('load', function(){scrollToError();});".
                         "if (window.document.readyState === 'complete') {".
                            "scrollToError();".
                         "}".
                    "})(jQuery);" .
                "} " .
                "else {" .
                    "document.getElementById('{$inputId}').scrollIntoView(true);" .
                "}",
                array('last' => true)
            );
            // turn off as only first error can be scrolled
            $this->scrollToError = false;
        }
    }
    
    /**
     * Adds empty text html to provided input html
     * 
     * @param string $inputHtml Html of the input the empty text is added to.
     * @param string $inputId Id of the input the empty text is added to.
     * @param string $inputValue Value of the input the empty text is added to.
     *      If not NULL and empty string then input text is created with style="display:none".
     * @param string $text Text to be displayed when the input is empty.
     * @param array $options Available options:
     *      - 'class' (string) Empty text block css class. Defaults to FormHelper::emptyTextDefaults['class'].
     *      - 'wrapper' (bool) If TRUE the input and its empty text are wrapped into wrapper
     *          to allow right positioning of empty text over input. Defaults to FormHelper::emptyTextDefaults['wrapper'].
     *      - 'wrapperClass' (string) Css class of wrapped Defaults to FormHelper::emptyTextDefaults['wrapperClass'].
     * 
     * @return string Html code
     */
    protected function addEmptyText($inputHtml, $inputId, $inputValue, $text, $options = array()) {
        $defaults = array(
            'class' => null,   
            'wrapper' => true,
            'wrapperClass' => null,
        );     
        $options = array_merge($defaults, $this->emptyTextDefaults, $options);
        
        // prepare id html
        $id = $inputId . '_empty_text';
        $idHtml = ' id="' . $id . '"';

        // prepare css class html
        $classHtml = '';
        if (!empty($options['class'])) {
            $classHtml = ' class="' . $options['class'] . '"';
        }

        // prepare attributes html
        $attrsHtml = $this->attributes($options, $defaults);
        
        // prepare style html
        $styleHtml = '';
        if ($inputValue !== null && $inputValue !== '') {
            $styleHtml = ' style="display:none"';
        }
        
        $html = $inputHtml;
        $html .= '<div' . $classHtml . $idHtml . $attrsHtml . $styleHtml . '>' . $text . '</div>';
        
        if (empty(self::$emptyTextJs[$options['class']])) {
            // attach jQuery
            if (empty(self::$attachedFiles[$this->jQueryJsFile])) {
                App::setJsFiles($this->jQueryJsFile);
                self::$attachedFiles[$this->jQueryJsFile] = true;
            }
            App::setJs("
                (function($) {
                    var context = window.document;
                    $(function(){
                        // treat input change by paste or by js
                        $('input, textarea', context).each(function() {
                            this.onpaste = function() {
                                var emptyTextId = this.id + '_empty_text';
                                $('#' + emptyTextId, context).hide();
                            }
                        });
                        $('input, textarea', context).change(function() {
                            var emptyTextId = this.id + '_empty_text';
                            if (this.value === '') {
                                $('#' + emptyTextId, context).show();
                            }
                            else {
                                $('#' + emptyTextId, context).hide();
                            }
                        });
                        // treat input change by keyboard
                        $('input, textarea', context).focus(function() {
                            var emptyTextId = this.id + '_empty_text';
                            $(this).on('keypress.Run.FormHelper.emptyText', function() {
                                $('#' + emptyTextId, context).hide();
                            });
                            $(this).on('keyup.Run.FormHelper.emptyText', function() {
                                if (this.value === '') {
                                    $('#' + emptyTextId, context).show();
                                }
                            });
                        });
                        $('input, textarea', context).blur(function() {
                            var emptyTextId = this.id + '_empty_text';
                            $(this).off('keypress.Run.FormHelper.emptyText');
                            $(this).off('keyup.Run.FormHelper.emptyText');
                        });
                        $('.{$options['class']}', context).click(function() {
                            var inputId = this.id.substr(0, this.id.length - 11);
                            $('#' + inputId, context).focus();
                        });
                    });
                })(jQuery);
            ");
            self::$emptyTextJs[$options['class']] = true;
        }
        
        // add wrapper
        if ($options['wrapper']) {
            $wrapperClassHtml = '';
            if (!empty($options['wrapperClass'])) {
                $wrapperClassHtml = ' class="' . $options['wrapperClass'] . '"';
            }
            $html = '<div' . $wrapperClassHtml . '>' . $html . '</div>';
        }

        // create html
        return $html;
    }    
    
    /**
     * Returns required mark if the $field is among $required fields
     * 
     * @param string $field Name of data field
     * @param string $mark Optional. Defaults to $this->inputDefaults['requiredMark'].
     * 
     * @return string Required mark enclosed in span.required-mark tag or empty string.
     */
    public function requiredMark($field, $mark = null, /*internal use:*/ $isRequired = null) {
        if ($isRequired === null) {
            $isRequired = $this->isRequired($field);
        }
        if ($isRequired) {
            if ($mark === null) {
                $mark = $this->inputDefaults['requiredMark'];
            }
            if (!empty($mark)) {
                return '<span class="required-mark">' . $mark . '</span>';
            }
        }
        return '';
    }  
    
    /**
     * Returns translated mark if the $field is among $translated fields
     * 
     * @param string $field Name of data field
     * @param string $mark Optional. Defaults to $this->inputDefaults['translatedMark'].
     * 
     * @return string Translation mark enclosed in span.translated-mark tag or empty string.
     */
    public function translatedMark($field, $mark =null, /*internal use:*/ $isTranslated = null) {
        if ($isTranslated === null) {
            $isTranslated = $this->isTranslated($field);
        }
        if ($isTranslated) {
            if ($mark === null) {
                $mark = $this->inputDefaults['translatedMark'];
            }
            if (!empty($mark)) {
                return '<span class="translated-mark">' . str_replace(':lang:', $this->lang, $mark) . '</span>';
            }
        }
        return '';
    }  
    
    /**
     * Creates html for field label. Label is automatically assigned to existing input
     * for given field.
     * 
     * @param string $field Name of data field
     * @param string $label Label text
     * @param array $options Available options:
     *      - 'class' (string) Html element class. Defaults to FormHelper::$labelDefaults['class'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'for' (string) Label for attribute which connect the input and label together. 
     *          Defaults to NULL, means will be genarated automatically.
     *      - 'separator' (string) Separator to be placed after label. 
     *          Defaults to FormHelper::$labelDefaults['separator'].
     *      - 'requiredMark' (string) Required mark to be used for labels of required fields. 
     *          Defaults to FormHelper::$labelDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for labels of translated fields. 
     *          Defaults to FormHelper::$labelDefaults['translatedMark'].
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'template' (string) Html template to render created label. Use ':l:', ':s:', 
     *          ':h:', ':r:', ':t:', ':ridt:' inserts to place the label text, separator, 
     *          hint, requiredMark, translatedMark html and ridt css classes into template 
     *          html, e.g.: '< div class="my-label :ridt:">:l::h:< /div>'. If empty value 
     *          then label is generated like ':l::s::r::t::h:'. Defaults to FormHelper::$labelDefaults['template'].
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     * 
     * NOTE: Any other items (placeholder, onclick, ...) in $options are considered
     *      to be attributes. As attribute values are encolsed to double quotes
     *      so use aphostrophes for string literals (e.g. in js events).
     * 
     * @return string Label html code
     */
    public function label($field, $label, $options = array()) {
        $defaults = array(
            'class' => null,   
            'id' => null,   
            'for' => null,
            'separator' => null,  
            'requiredMark' => null,   
            'translatedMark' => null,   
            'hint' => null,  
            'template' => null,
            'compatibility' => $this->compatibility,
            
            // Internal use:
            // used to pass what is known already and so optimize
            'inputId' => null,
            'inputName' => null,
            'hintHtml' => null,
            'isRequired' => null,
            'requiredMarkHtml' => null,
            'isTranslated' => null,
            'translatedMarkHtml' => null,
            'ridtClasses' => null,
        );
        $options = array_merge($defaults, $this->labelDefaults, $options);
        
        
        // find input id to create the label id and for if necessary
        if (
            $options['for'] === null
            || $options['id'] === null
        ) {
            if ($options['inputId'] === null) {
                // find input name
                if (empty($options['inputName'])) {
                    $options['inputName'] = $this->createInputName($field);
                }
                $options['inputId'] = $this->assignLabelInputId($options['inputName']);
            }
            // set label for
            if ($options['for'] === null) {
                $options['for'] = $options['inputId'];
            }
            // set label id
            if ($options['id'] === null) {
                $options['id'] = $options['inputId'] . '_label';
            }
        }
        // prepare for and id html
        $forHtml = ' for="' . $options['for'] . '"';
        $idHtml = ' id="' . $options['id'] . '"';
        
        // prepare classes html
        $classHtml = '';
        if ($options['ridtClasses'] === null) {
            $options['ridtClasses'] = $this->ridt($field, $options, $options['isRequired']);
        }
        if ($options['compatibility'] === 'bootstrap') {
            $options['class'] = rtrim('control-label ' . $options['class']);
        }
        $options['class'] = ltrim($options['class'] . $options['ridtClasses']);
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        
        // prepare required mark html
        $requiredMarkHtml = $options['requiredMarkHtml'];
        if (!empty($options['requiredMark'])) {
            $requiredMarkHtml = $this->requiredMark($field, $options['requiredMark'], $options['isRequired']);
        }
                
        // prepare translated mark html
        $translatedMarkHtml = $options['translatedMarkHtml'];
        if (!empty($options['translatedMark'])) {
            $translatedMarkHtml = $this->requiredMark($field, $options['translatedMark'], $options['isTranslated']);
        }
                
        // prepare hint html
        $hintHtml = $options['hintHtml'];
        if (!empty($options['hint'])) {
            $hintOptions = array();
            if (is_array($options['hint'])) {
                $hintText = Sanitize::value($options['hint']['text']);
                unset($options['hint']['text']);
                $hintOptions = $options['hint'];
            }
            else {
                $hintText = $options['hint'];
            }
            if (!empty($hintText)) {
                $hintOptions['id'] = $options['id'] . '_hint';
                $hintOptions['class'] = $options['ridtClasses'];
                $hintHtml = Html::hint($hintText, $hintOptions);
            }
        }   
        
        // prepare attributes html
        $attrsHtml = $this->attributes($options, $defaults);
        
        // apply template
        if (empty($options['template'])) {
            //:l::s::r::t::h:
            $label = $label . $options['separator'] . $requiredMarkHtml . $translatedMarkHtml . $hintHtml;
        }
        else {
            $label = str_replace(
                array(':l:', ':s:', ':h:', ':r:', ':t:', ':ridt:'),
                array($label, $options['separator'], $hintHtml, $requiredMarkHtml, $translatedMarkHtml, $options['ridtClasses']),
                $options['template']
            );
        }
        
        return '<label' . $idHtml . $forHtml . $classHtml . $attrsHtml . '>' . $label . '</label>';
    }
    
    /**
     * Returns value of specified input
     * 
     * @param string $inputType Input type, e.g. 'text', 'textarea', 'select', etc.
     * @param string $field Name of data field.
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. If provided than 
     *          'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     * 
     * @return string Soecified input value
     */
    protected function value($inputType, $field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
        );
        $options = array_merge($defaults, $options);       
        
        $transferred = false;
        // apply explicit value if provided
        if ($options['explicitValue'] !== null) {
            $options['value'] = $options['explicitValue'];
        }
        // otherwise retrieve it from data, transfer or default values
        else {
            // - data value
            if (
                !empty($this->data)
                && is_array($this->data)
                && ($value = Arr::getPath($this->data, $field, $this->separator)) !== null
                // in case of display and image inputs the $value must not be array
                // (it means not $_FILES data from file input) to allow the possible
                // transfer value be applied (see below)
                && (
                    $inputType !== 'display'
                    && $inputType !== 'image'
                    || 
                    !is_array($value)
                )
            ){
                $options['value'] = $value;
            }
            // - in case in display and image inputs check for transfer field value
            elseif (
                (
                    $inputType === 'display'
                    || $inputType === 'image'
                )
                && !empty($this->data)
                && is_array($this->data)
                && ($value = $this->transferValue($field)) !== null
            ) {
                $options['value'] = $value;
                $transferred = true;
            }
            // - default value
            elseif (
                $options['value'] === null
                && !empty($this->defaultValues)
                && is_array($this->defaultValues)
                && ($value = Arr::getPath($this->defaultValues, $field, $this->separator)) !== null
            ) {
                $options['value'] = $value;
            }
        }
        if (
            !$transferred
            && !empty($options['renderValue'])
            && Validate::callableFunction($options['renderValue'])
        ) {
            $options['value'] = $options['renderValue']($options['value'], $field, $this->data);
        }        
        return $options['value'];
    }
    
    /**
     * This method contains common logic of all inputs finilization (adding label, 
     * hint, error & warning messages and some compatibility html if required)
     * 
     * @param string $type Input type, e.g. 'text', 'textarea', 'checkbox, etc.
     * @param string $field
     * @param string $inputName
     * @param string $inputHtml
     * @param string $ridtClasses
     * @param bool $isRequired
     * @param bool $isDisabled
     * @param bool $isTranslated
     * @param array $options Options passed to input method
     * 
     * @return string Input final HTML
     */
    protected function finalizeInput($type, $field, $inputName, $inputHtml, $ridtClasses, $isRequired, $isDisabled, $isTranslated, $options) {
        $defaults = array(
            'id' => null,
            'label' => null,
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => null,
            'forceSubmit' => false,
        );
        $options = array_merge($defaults, $options);
        $type = strtolower($type);
        
        // add hidden input for disabled inputs with 'forceSubmit' set to TRUE
        // or in case that it is disabled implicitly to avoid translation
        if (
            $isDisabled 
            && $type !== 'file'
            && $type !== 'password'
            && $type !== 'display'
            && $type !== 'image'
            && $type !== 'contentBlocks'
            && $type !== 'gallery'
            && (
                $options['forceSubmit'] 
                ||
                $isTranslated === false
                && !$this->hasDefaultLang()
            )
        ) {
            $explicitValue = isset($options['value']) ? $options['value'] : null;
            if (is_array($explicitValue)) {
                $explicitValue = implode(';', $explicitValue);
            }
            $inputHtml .= $this->hidden($field, array(
                'explicitValue' => $explicitValue,
            ));
        }
        // prepare required mark html
        $requiredMarkHtml = '';
        if (!empty($options['requiredMark'])) {
            $requiredMarkHtml = $this->requiredMark($field, $options['requiredMark'], $isRequired);
        }
        
        // prepare translation mark html
        $translatedMarkHtml = '';
        if (!empty($options['translatedMark'])) {
            $translatedMarkHtml = $this->translatedMark($field, $options['translatedMark'], $isTranslated);
        }
        
        // prepare hint html
        $hintHtml = '';
        if (!empty($options['hint'])) {
            $hintOptions = array();
            if (is_array($options['hint'])) {
                $hintText = Sanitize::value($options['hint']['text']);
                unset($options['hint']['text']);
                $hintOptions = $options['hint'];
            }
            else {
                $hintText = $options['hint'];
            }
            if (!empty($hintText)) {
                $hintOptions['id'] = $options['id'] . '_hint';
                $hintOptions['class'] = $ridtClasses;
                $hintHtml = Html::hint($hintText, $hintOptions);
            }
        }
        
        // prepare errors and warnings html 
        $errorsHtml = '';
        if ($options['attachErrors']) {
            $errorsOptions = array(
                'inputId' => $options['id'],
                'inputName' => $inputName,
                'compatibility' => $options['compatibility'],
            );
            if ($options['attachErrors'] === 'scrollOnly') {
                $errorsOptions['scrollOnly'] = true;
            }
            if (is_array($options['attachErrors'])) {
                $errorsOptions = array_merge($errorsOptions, $options['attachErrors']);
            }
            $errorsHtml .= $this->errors($field, $errorsOptions);
        }   
        if ($options['attachWarnings']) {
            $warningsOptions = array(
                'inputId' => $options['id'],
                'inputName' => $inputName,
                'compatibility' => $options['compatibility'],
            );
            if (is_array($options['attachWarnings'])) {
                $warningsOptions = array_merge($warningsOptions, $options['attachWarnings']);
            }
            $errorsHtml .= $this->warnings($field, $warningsOptions);
        }   
        
        // prepare label html
        $labelHtml = '';
        if ($options['label'] !== null) {
            $labelOptions = array(
                'inputId' => $options['id'],
                'inputName' => $inputName,
                'hintHtml' => $hintHtml,
                'isRequired' => $isRequired,
                'requiredMarkHtml' => $requiredMarkHtml,
                'isTranslated' => $isTranslated,
                'translatedMarkHtml' => $translatedMarkHtml,
                'ridtClasses' => $ridtClasses,
                'compatibility' => $options['compatibility'],
            );
            if (is_array($options['label'])) {
                $labelText = Sanitize::value($options['label']['text']);
                unset($options['label']['text']);
                $labelOptions = array_merge($labelOptions, $options['label']);
            }
            else {
                $labelText = $options['label'];
            }
            if ($labelText !== null) {
                $labelHtml = $this->label($field, $labelText, $labelOptions);
            }
        }
        
        // render the input, label and errors
        if (empty($options['template'])) {
            $html = $labelHtml . $inputHtml . $errorsHtml;
        }
        else {
            $html = str_replace(
                array(':l:', ':i:', ':e:', ':h:', ':r:', ':t:', ':ridt:'),
                array($labelHtml, $inputHtml, $errorsHtml, $hintHtml, $requiredMarkHtml, $translatedMarkHtml, $ridtClasses),
                $options['template']
            );
        }
        if (!empty($options['inserts'])) {
            $html = Str::fill($html, (array)$options['inserts']);
        }
        if ($options['compatibility'] === 'bootstrap') {
            $html = '<div class="form-group' . $ridtClasses . '">' . $html . '</div>';
        }
            
        return $html;          
    }
    
    /**
     * Returns transfer value for given field
     * 
     * This is used in case of display inputs (display, image) to transfer displayed
     * value between form submitions
     * 
     * @param string $field
     * 
     * @return string
     */
    public function transferValue($field) {
        return Arr::getPath($this->data, $field . $this->transferFieldSuffix, $this->separator);
    }
        
    /**
     * Creates input html
     * 
     * @param string $field Name of data field, e.g. 'my_table_field', 'MyModel.my_table_field'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          In case of special inputs, which does not use name (e.g. FormHelper::contentBlocks()),
     *          provide here any value, the best NULL. For more details, how to create 
     *          field names, see the end of FormHelper class phpDoc.
     * 
     * @param array $options For exact list of options see the input specific method.
     *          Generally used options are following (with few exceptions):
     *      - 'type' (string) Input type: 'hidden', 'text', textarea', 'password', 
     *          'search', 'email', 'tel', 'url', 'time', 'week', 'month', 'number', 
     *          'range', 'color', 'date', 'file', 'checkbox', 'radio', 'select', 
     *          'itemSelector', 'treeSelector', 'display', 'image', 'editor', 'gallery', 
     *          'contentBlocks'. Defaults to FormHelper::$inputDefaults['type'].
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Ignored for password and file inputs.
     *          Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. Ignored for password and file inputs.
     *          If provided than 'value' option is ignored. Defaults to NULL.
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *      - 'forceSubmit' (bool) Considered only in case of disabled inputs. Values 
     *          of disabled inputs are not submitted (https://www.w3.org/TR/html401/interact/forms.html#disabled).
     *          To disable an input and keep its value to be submitted set both 'disabled'
     *          and 'forceSubmit' to TRUE. Then there is created a hidden input to
     *          ensure the value submition. This option is NOT considered in case 
     *          of 'file', 'itemSelector', 'treeSelector', 'display', 'image', 'gallery',
     *          'contentBlocks' and 'hidden' inputs. Defaults to FALSE.
     *      - 'options' (array) In case 'select' input a list of pairs {optionValue} => {optionLabel} 
     *          which will be translated to input <option> items.  Use nested arrays to define <optgroup>.
     *          In case of 'editor', 'date', 'gallery' and 'contentBlocks' inputs 
     *          these are special options of these inputs.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     * 
     * @return string Html code
     */
    public function input($field, $options = array()) {
        $defaults = array(
            'type' => 'text',
        );
        $options = array_merge($defaults, $options);
        $options['type'] = strtolower($options['type']);
        switch ($options['type']) {
            case 'date': return $this->date($field, $options);
            case 'file': return $this->file($field, $options);
            case 'checkbox': return $this->checkbox($field, $options);
            case 'radio': return $this->radio($field, $options);
            case 'select': return $this->select($field, $options);
            case 'itemselector': return $this->itemSelector($field, $options);
            case 'treeselector': return $this->treeSelector($field, $options);
            case 'display': return $this->display($field, $options);
            case 'image': return $this->image($field, $options);
            case 'editor': return $this->editor($field, $options);
            case 'gallery': return $this->gallery($field, $options);
            case 'contentblocks': return $this->contentBlocks($options);
            case 'hidden': return $this->hidden($field, $options);
            default: return $this->text($field, $options);            
        }       
    }

    /**
     * Creates HTML for <input type=hidden>
     * 
     * @param string $field Name of data field, e.g. 'id', 'User.id'. Field name
     *          is used to find out input value in FormHelper::$data. For more details, 
     *          how to create field names, see the end of FormHelper class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. If provided than 
     *          'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *
     * @return string Html code
     */
    public function hidden($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'id' => null,  
            
            // Internal use: avoid following to create as attributes
            'type' => null,
            'class' => null,
            'title' => null,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);          
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
        
        // input value
        $options['value'] = $this->value('hidden', $field, $options);
        
        // create input properties html
        // - input name
        $nameHtml = ' name="' . $inputName . '"'; 
        // - input id
        $idHtml = ' id="' . $options['id'] . '"';
        // - value
        $valueHtml = '';
        if ($options['value'] !== null) {
            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        // - other attrs
        $attrsHtml = $this->attributes($options, $defaults);
        
        // hidden field has no label, errors, warnings and template
        $inputHtml = '<input type="hidden"';
        $inputHtml .= $nameHtml . $idHtml . $valueHtml . $attrsHtml;
        $inputHtml .= '/>';
        return $inputHtml;
    }

    /**
     * Creates HTML for <input type=text>
     * 
     * This method can be also used to create all "text-like" inputs (textarea, 
     * password, search, email, tel, url, time, week, month, number, range, color) 
     * specifying the 'type' option.
     * 
     * @param string $field Name of data field, e.g. 'email', 'User.email'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. If provided than 
     *          'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *      - 'empty' (string|array) Empty input text (variant of placeholder). 
     *          It can be specified also as array and the text itself must be 
     *          specified under 'text' item of array. Defaults to NULL.
     *      - 'type' (string) One of text-like input type: 'text', textarea', 'password', 
     *          'search', 'email', 'tel', 'url', 'time', 'week', 'month', 'number', 
     *          'range', 'color'. Defaults to 'text'.
     *      - 'forceSubmit' (bool) Considered only in case of disabled inputs. Values 
     *          of disabled inputs are not submitted (https://www.w3.org/TR/html401/interact/forms.html#disabled).
     *          To disable an input and keep its value to be submitted set both 'disabled'
     *          and 'forceSubmit' to TRUE. Then there is created a hidden input to
     *          ensure the value submition. Defaults to FALSE.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     *  
     * @return string Html code
     */
    public function text($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            'empty' => null,
            'type' => 'text',
            'forceSubmit' => false,
            
            // Internal use: avoid following to create as attributes
            
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);       
        
        // input type
        $options['type'] = strtolower($options['type']);
        // input css class
        // - assign ridt class
        $isRequired = $isInvalid = $isDisabled = $isTranslated = null;
        $ridtClasses = $this->ridt($field, $options, $isRequired, $isInvalid, $isDisabled, $isTranslated);
        // - check for ignored nontranslated fields
        if (
            $this->nonTranslatedProcessing === 'ignore'
            && $isTranslated === false
            && !$this->hasDefaultLang()
        ) {
            return '';
        }
        // - update class by ridt classes
        $options['class'] = ltrim($options['class'] . $ridtClasses);
        // - assign input specific classes
        if ($this->inputDefaults['textClass']) {
            $options['class'] = rtrim($this->inputDefaults['textClass'] . ' ' . $options['class']);
        }
        // - add bootstrap classes
        if ($options['compatibility'] === 'bootstrap') {
            $options['class'] = rtrim('form-control '  . $options['class']);
        }
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // input value
        if ($options['type'] === 'password') {
            $options['value'] = null;
        }
        else {
            $options['value'] = $this->value($options['type'], $field, $options);
        }
        // - normalize 'color' input value, it must be in hexadecimal form with leading #
        if ($options['type'] === 'color') {
            //set default value to white
            if (empty($options['value'])) {
                $options['value'] = '#ffffff';
            }
            elseif (substr($options['value'], 0, 1) !== '#') {
                $options['value'] = '#' . $options['value'];
            }
        }
        
        // create input properties html
        // - css class 
        $classHtml = '';
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        // - disabled
        $disabledHtml = '';
        if ($isDisabled) {
            $disabledHtml= ' disabled="disabled"';
        }
        // - input name
        $nameHtml = ' name="' . $inputName . '"'; 
        // - input id
        $idHtml = ' id="' . $options['id'] . '"';
        // - value
        $valueHtml = '';
        if ($options['value'] !== null) {
            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        // - other attrs
        $attrsHtml = $this->attributes($options, $defaults);
        
        // create input html
        switch ($options['type']) {
            case 'text':
            // FormHelper::text() is internally used in following input specific methods:   
            case 'password':
            case 'search':
            case 'email':
            case 'tel':
            case 'url':
            case 'time':
            case 'week':
            case 'month':
            case 'number':
            case 'range':
            case 'color':
                $inputHtml = '<input type="'  . $options['type'] . '"';
                $inputHtml .= $nameHtml . $idHtml . $valueHtml . $classHtml . $attrsHtml . $disabledHtml;
                $inputHtml .= '/>';
                break;
            
            case 'textarea':
                $inputHtml = '<textarea';
                $inputHtml .= $nameHtml . $idHtml . $classHtml . $attrsHtml . $disabledHtml;
                $inputHtml .= '>';
                $inputHtml .= $options['value'];
                $inputHtml .= '</textarea>';
                break;
            
            default:
                $inputHtml = '<input type="'  . $options['type'] . '"';
                $inputHtml .= $nameHtml . $idHtml . $valueHtml . $classHtml . $attrsHtml . $disabledHtml;
                $inputHtml .= '/>';
                break;
        }
        
        // prepare empty text (~placeholder) html if specified
        if ($options['empty']) {
            $emptyTextOptions = array();
            if (is_array($options['empty'])) {
                $emptyText = Sanitize::value($options['empty']['text']);
                unset($options['empty']['text']);
                $emptyTextOptions = array_merge($emptyTextOptions, $options['empty']);
            }
            else {
                $emptyText = $options['empty'];
            }
            if ($emptyText !== null) {
                $inputHtml = $this->addEmptyText(
                    $inputHtml, 
                    $options['id'], 
                    $options['value'], 
                    $emptyText, 
                    $emptyTextOptions
                );
            }
        }
        
        return $this->finalizeInput(
            $options['type'], 
            $field, 
            $inputName, 
            $inputHtml, 
            $ridtClasses, 
            $isRequired, 
            $isDisabled, 
            $isTranslated, 
            $options
        );
    }

    /**
     * Creates HTML for <textarea>
     * 
     * @param string $field Name of data field, e.g. 'note', 'Order.note'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. If provided than 
     *          'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *      - 'empty' (string|array) Empty input text (variant of placeholder). 
     *          It can be specified also as array and the text itself must be 
     *          specified under 'text' item of array. Defaults to NULL.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     *  
     * @return string Html code
     */
    public function textarea($field, $options = array()) {
        $options['type'] = 'textarea';
        return $this->text($field, $options);            
    }

    /**
     * Creates HTML for <input type=password>
     * 
     * @param string $field Name of data field, e.g. 'password', 'User.password'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *      - 'empty' (string|array) Empty input text (variant of placeholder). 
     *          It can be specified also as array and the text itself must be 
     *          specified under 'text' item of array. Defaults to NULL.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     *  
     * @return string Html code
     */
    public function password($field, $options = array()) {
        $options['type'] = 'password';
        return $this->text($field, $options);            
    }

    /**
     * Creates HTML for <input type="search">
     * 
     * @param string $field Name of data field
     * @param array $options See options of FormHelper::text()
     *  
     * @return string Html code
     */
    public function search($field, $options = array()) {
        $options['type'] = 'search';
        return $this->text($field, $options);            
    }

    /**
     * Creates HTML for <input type="email">
     * 
     * @param string $field Name of data field
     * @param array $options See options of FormHelper::text()
     *  
     * @return string Html code
     */
    public function email($field, $options = array()) {
        $options['type'] = 'search';
        return $this->text($field, $options);            
    }

    /**
     * Creates HTML for <input type="tel">
     * 
     * @param string $field Name of data field
     * @param array $options See options of FormHelper::text()
     *  
     * @return string Html code
     */
    public function tel($field, $options = array()) {
        $options['type'] = 'tel';
        return $this->text($field, $options);            
    }

    /**
     * Creates HTML for <input type="url">
     * 
     * @param string $field Name of data field
     * @param array $options See options of FormHelper::text()
     *  
     * @return string Html code
     */
    public function url($field, $options = array()) {
        $options['type'] = 'url';
        return $this->text($field, $options);            
    }

    /**
     * Creates HTML for <input type="time">
     * 
     * @param string $field Name of data field
     * @param array $options See options of FormHelper::text()
     *  
     * @return string Html code
     */
    public function time($field, $options = array()) {
        $options['type'] = 'time';
        return $this->text($field, $options);            
    }

    /**
     * Creates HTML for <input type="week">
     * 
     * @param string $field Name of data field
     * @param array $options See options of FormHelper::text()
     *  
     * @return string Html code
     */
    public function week($field, $options = array()) {
        $options['type'] = 'week';
        return $this->text($field, $options);            
    }

    /**
     * Creates HTML for <input type="month">
     * 
     * @param string $field Name of data field
     * @param array $options See options of FormHelper::text()
     *  
     * @return string Html code
     */
    public function month($field, $options = array()) {
        $options['type'] = 'month';
        return $this->text($field, $options);            
    }

    /**
     * Creates HTML for <input type="number">
     * 
     * @param string $field Name of data field
     * @param array $options See options of FormHelper::text()
     *  
     * @return string Html code
     */
    public function number($field, $options = array()) {
        $options['type'] = 'number';
        return $this->text($field, $options);            
    }

    /**
     * Creates HTML for <input type="range">
     * 
     * @param string $field Name of data field
     * @param array $options See options of FormHelper::text()
     *  
     * @return string Html code
     */
    public function range($field, $options = array()) {
        $options['type'] = 'range';
        return $this->text($field, $options);            
    }

    /**
     * Creates HTML for <input type="color">
     * 
     * @param string $field Name of data field
     * @param array $options See options of FormHelper::text()
     *  
     * @return string Html code
     */
    public function color($field, $options = array()) {
        $options['type'] = 'color';
        return $this->text($field, $options);            
    }
    
    /**
     * Creates HTML for date input
     * 
     * @param string $field Name of data field, e.g. 'birthdate', 'User.birthdate'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Ignored for password and file inputs.
     *          Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. Ignored for password and file inputs.
     *          If provided than 'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'format' (string) Date format. Defaults to 'd.m.Y'. If empty value then
     *          no formating is applied to input value.
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *      - 'options' (array) Options which will be passed to new created instance 
     *          of jQuery datepicker. For case of jQuery datepicker following options can be used: @todo
     *      - 'empty' (string|array) Empty input text (variant of placeholder). 
     *          This can be specified also as array and the text itself must be 
     *          specified under 'text' item of array. Defaults to NULL.
     *      - 'forceSubmit' (bool) Considered only in case of disabled inputs. Values 
     *          of disabled inputs are not submitted (https://www.w3.org/TR/html401/interact/forms.html#disabled).
     *          To disable an input and keep its value to be submitted set both 'disabled'
     *          and 'forceSubmit' to TRUE. Then there is created a hidden input to
     *          ensure the value submition. Defaults to FALSE.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     * 
     * @return string Html code
     */
    public function date($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'format' => 'd.m.Y',
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'datepickerJsFile' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            'options' => array(),       
            'empty' => null,
            'forceSubmit' => false,
            
            // Internal use: avoid following to create as attributes
            'type' => null,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);
        
        // input css class
        // - assign ridt class
        $isRequired = $isInvalid = $isDisabled = $isTranslated = null;
        $ridtClasses = $this->ridt($field, $options, $isRequired, $isInvalid, $isDisabled, $isTranslated);
        // - check for ignored nontranslated fields
        if (
            $this->nonTranslatedProcessing === 'ignore'
            && $isTranslated === false
            && !$this->hasDefaultLang()
        ) {
            return '';
        }
        // - update class by ridt classes
        $options['class'] = ltrim($options['class'] . $ridtClasses);
        // - assign input specific classes
        $options['class'] = trim(str_replace('date-picker', '', $options['class'])) . ' date-picker';
        $uniqueClass = uniqid('date-picker-');
        $options['class'] .= ' ' . $uniqueClass;
        if ($this->inputDefaults['textClass']) {
            $options['class'] = rtrim($this->inputDefaults['textClass'] . ' ' . $options['class']);
        }
        // - add bootstrap classes
        if ($options['compatibility'] === 'bootstrap') {
            $options['class'] = rtrim('form-control '  . $options['class']);
        }
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // input value
        $options['value'] = $this->value('date', $field, $options);
        // - normalize input value to specified format
        if (
            !empty($options['format'])
            && !empty($options['value'])
            && ($normalizedValue = Date::format($options['value'], $options['format']))
        ) {
            $options['value'] = $normalizedValue;
        }
        
        // create input properties html
        // - css class 
        $classHtml = '';
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        // - disabled
        $disabledHtml = '';
        if ($isDisabled) {
            $disabledHtml= ' disabled="disabled"';
        }
        // - input name
        $nameHtml = ' name="' . $inputName . '"'; 
        // - input id
        $idHtml = ' id="' . $options['id'] . '"';
        // - value
        $valueHtml = '';
        if ($options['value'] !== null) {
            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        // - other attrs
        $attrsHtml = $this->attributes($options, $defaults);
        
        // create input html
        if (empty(self::$attachedFiles[$this->jQueryJsFile])) {
            App::setJsFiles($this->jQueryJsFile);
            self::$attachedFiles[$this->jQueryJsFile] = true;
        }
        if (empty(self::$attachedFiles[$this->jQueryUiJsFile])) {
            App::setJsFiles($this->jQueryUiJsFile);
            self::$attachedFiles[$this->jQueryUiJsFile] = true;
        }
        if (empty(self::$attachedFiles[$this->jQueryUiCssFile])) {
            App::setCssFiles($this->jQueryUiCssFile);
            self::$attachedFiles[$this->jQueryUiCssFile] = true;
        }
        $localizationFile = '/app/js/vendors/jquery-ui/datepicker/jquery.ui.datepicker-' . App::$lang . '.js';
        if (
            empty(self::$attachedFiles[$localizationFile])
            && is_readable(File::getAbsolutePath(File::normalizeDS($localizationFile)))
        ) {
            App::setJsFiles($localizationFile);
            self::$attachedFiles[$localizationFile] = true;
        }
        if (empty(self::$attachedFiles[$options['datepickerJsFile']])) {
            App::setJsFiles($options['datepickerJsFile']);
            self::$attachedFiles[$options['datepickerJsFile']] = true;
        }
        $datepickerOptions = array_merge(array(
            'selector' => '.' . $uniqueClass,
            // the first day of the week: Sunday is 0, Monday is 1, etc.
            'firstDay' => 1, 
        ), $options['options']);
        $inputHtml = '<input type="text"';
        $inputHtml .= $nameHtml . $idHtml . $valueHtml . $classHtml . $attrsHtml . $disabledHtml;
        $inputHtml .= '/>';
        $inputHtml .= App::loadView('App', 'Form/datepicker', $datepickerOptions);
        
        // prepare empty text (~placeholder) html if specified
        if ($options['empty']) {
            $emptyTextOptions = array();
            if (is_array($options['empty'])) {
                $emptyText = Sanitize::value($options['empty']['text']);
                unset($options['empty']['text']);
                $emptyTextOptions = array_merge($emptyTextOptions, $options['empty']);
            }
            else {
                $emptyText = $options['empty'];
            }
            if ($emptyText !== null) {
                $inputHtml = $this->addEmptyText(
                    $inputHtml, 
                    $options['id'], 
                    $options['value'], 
                    $emptyText, 
                    $emptyTextOptions
                );
            }
        }
        
        return $this->finalizeInput(
            'date', 
            $field, 
            $inputName, 
            $inputHtml, 
            $ridtClasses, 
            $isRequired, 
            $isDisabled, 
            $isTranslated, 
            $options
        );
    }
    
    /**
     * Creates HTML for <input type=file>
     * 
     * @param string $field Name of data field, e.g. 'photo', 'User.photo'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'multiple' (bool) If TRUE then multiple files can be choosen (e.g. for an upload)
     *          Defaults to FALSE.
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     * 
     * @return string Html code
     */
    public function file($field, $options = array()) {
        $defaults = array(
            'multiple' => false,
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            
            // Internal use: avoid following to create as attributes
            'type' => null,
            'forceSubmit' => false,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);
        // turn off this option for this type of input
        $options['forceSubmit'] = false;
        
        // input css class
        // - assign ridt class
        $isRequired = $isInvalid = $isDisabled = $isTranslated = null;
        $ridtClasses = $this->ridt($field, $options, $isRequired, $isInvalid, $isDisabled, $isTranslated);
        // - check for ignored nontranslated fields
        if (
            $this->nonTranslatedProcessing === 'ignore'
            && $isTranslated === false
            && !$this->hasDefaultLang()
        ) {
            return '';
        }
        // - update class by ridt classes
        $options['class'] = ltrim($options['class'] . $ridtClasses);
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // create input properties html
        // - css class 
        $classHtml = '';
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        // - disabled
        $disabledHtml = '';
        if ($isDisabled) {
            $disabledHtml= ' disabled="disabled"';
        }
        // - input name
        // NOTE: multiple file input creates its own version of nameHtml
        if (empty($options['multiple'])) {
            $multipleHtml = '';
            $nameHtml = ' name="' . $inputName . '"';
        }
        else {
            $multipleHtml = ' multiple="multiple"';
            $nameHtml = ' name="' . $inputName . '[]"';
        }
        // - input id
        $idHtml = ' id="' . $options['id'] . '"';
        // - other attrs
        $attrsHtml = $this->attributes($options, $defaults);
        
        // create input html
        $inputHtml = '<input type="file"';
        $inputHtml .= $nameHtml . $idHtml . $classHtml . $attrsHtml . $disabledHtml . $multipleHtml;
        $inputHtml .= '/>';
                
        return $this->finalizeInput(
            'file', 
            $field, 
            $inputName, 
            $inputHtml, 
            $ridtClasses, 
            $isRequired, 
            $isDisabled, 
            $isTranslated, 
            $options
        );
    }

    /**
     * Creates HTML for <input type=checkbox>
     * 
     * @param string $field Name of data field, e.g. 'active', 'User.active'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. If provided than 
     *          'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'toggleValue' (string|int|float) The value of single checkbox input (but submited 
     *          only if the input is checked!!!). Applies only to single checkbox inputs.
     *          Defaults to 1.
     *      - 'checked' (bool) Initial 'checked' state of the single checkbox input.
     *          This can be also accomplished using 'value' option or FormHelper::$defaultValues.
     *          The checkbox input will be checked if its 'toggleValue' equals to 
     *          'value' or to corresponding value in FormHelper::$defaultValues.
     *      - 'options' (array) List of pairs {checkboxValue} => {checkboxLabel}.
     *          If specified then a checkbox group is created. Defaults to empty array().
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'toggleTemplate' (string) Html template to render a partial input of 
     *          checkbox group, e.g.: '<div class="my-checkbox">:i::l:</div>
     *          Defaults to FormHelper::$inputDefaults['toggleTemplate'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *      - 'forceSubmit' (bool) Considered only in case of disabled inputs. Values 
     *          of disabled inputs are not submitted (https://www.w3.org/TR/html401/interact/forms.html#disabled).
     *          To disable an input and keep its value to be submitted set both 'disabled'
     *          and 'forceSubmit' to TRUE. Then there is created a hidden input to
     *          ensure the value submition. Defaults to FALSE.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     * 
     * @return string Html code
     */
    public function checkbox($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'toggleTemplate' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            'forceSubmit' => false,
            // this option is only for single checkbox
            'checked' => false,
            'toggleValue' => 1,
            // these options apply only to checkbox group
            'options' => array(),
            
            // Internal use: avoid following to create as attributes
            'type' => null,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);     
        
        // input css class 
        // - assign ridt class
        $isRequired = $isInvalid = $isDisabled = $isTranslated = null;
        $ridtClasses = $this->ridt($field, $options, $isRequired, $isInvalid, $isDisabled, $isTranslated);
        // - check for ignored nontranslated fields
        if (
            $this->nonTranslatedProcessing === 'ignore'
            && $isTranslated === false
            && !$this->hasDefaultLang()
        ) {
            return '';
        }
        // - update class by ridt classes
        $options['class'] = ltrim($options['class'] . $ridtClasses);
        // - assign inut specific classes
        if ($this->inputDefaults['toggleClass']) {
            $options['class'] = rtrim($this->inputDefaults['toggleClass'] . ' ' . $options['class']);
        }
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // value of checkbox group (single checkbox is treated separately, see below on input
        // html creation)
        if (!empty($options['options'])) {
            $options['value'] = $this->value('checkbox', $field, $options);
        }
        
        // create input properties html
        // - css class 
        $classHtml = '';
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        // - disabled
        $disabledHtml = '';
        if ($isDisabled) {
            $disabledHtml= ' disabled="disabled"';
        }
        // - input name
        $nameHtml = ' name="' . $inputName . '"'; 
        // - input id
       $idHtml = ' id="' . $options['id'] . '"';
        // - value
        $valueHtml = '';
        if (empty($options['options'])) {
            // in the case of sigle checkbox the value is set by toggleValue
            $escapedValue = $this->escapeHtmlSpecialChars($options['toggleValue']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        elseif (
            $options['value'] !== null
            // skip checkbox groups
            && !is_array($options['value']) 
        ) {
            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        // - other attrs
        $attrsHtml = $this->attributes($options, $defaults);
        
        // create input html
        $inputHtml = '';
        // - single checkbox
        if (empty($options['options'])) {
            // separatelly created checkbox from checkbox group
            if (substr($field, -1) === $this->separator) {
                $hiddenField = substr($field, 0, -1);
                // data value                        
                if (
                    !empty($this->data)
                    && is_array($this->data)
                    && ($value = Arr::getPath($this->data, $hiddenField, $this->separator)) !== null
                ) {
                    $options['value'] = $value;
                }
                // default value
                elseif (
                    $options['value'] === null
                    && !empty($this->defaultValues)
                    && is_array($this->defaultValues)
                    && ($value = Arr::getPath($this->defaultValues, $hiddenField, $this->separator)) !== null
                ) {
                    $options['value'] = $value;
                }
                if (
                    !empty($options['renderValue'])
                    && Validate::callableFunction($options['renderValue'])
                ) {
                    $options['value'] = $options['renderValue']($options['value'], $field, $this->data);
                }
                // prepare checked property
                if ($options['value'] !== null) {
                    $options['checked'] = (
                        in_array((string)$options['toggleValue'], (array)$options['value'])
                    );
                }
                // hidden input name must be restricted to "base" of array
                $hiddenName = substr($inputName, 0, -2);
                // display hidden input to force the input name presence in form data
                // Make this only for the first occurence of the input otherwise 
                // hidden inputs will override previous regular inputs values
                if (
                    $this->getInputsCount($hiddenName) == 0
                    && $this->getInputsCount($inputName) == 1
                ) {
                    $inputHtml .= '<input type="hidden" name="' . $hiddenName . '" id="' . $options['id'] . '_hidden" value=""' . $disabledHtml . '/>';
                }
            }
            // real single checkbox
            else {
                // data value                            
                if (
                    !empty($this->data)
                    && is_array($this->data)
                    && ($value = Arr::getPath($this->data, $field, $this->separator)) !== null
                ) {
                    $options['value'] = $value;                        
                }
                // default value
                elseif (
                    $options['value'] === null
                    && !empty($this->defaultValues)
                    && is_array($this->defaultValues)
                    && ($value = Arr::getPath($this->defaultValues, $field, $this->separator)) !== null
                ) {
                    $options['value'] = $value;
                }
                if (
                    !empty($options['renderValue'])
                    && Validate::callableFunction($options['renderValue'])
                ) {
                    $options['value'] = $options['renderValue']($options['value'], $field, $this->data);
                }
                // prepare checked property
                if ($options['value'] !== null) {
                    $options['checked'] = (
                        (string)$options['toggleValue'] == (string)$options['value']
                    );
                }
                // display hidden input to force the input name presence in form data
                // Make this only for the first occurence of the input otherwise 
                // hidden inputs will override previous regular inputs values
                if ($this->getInputsCount($inputName) == 1) {
                    $inputHtml .= '<input type="hidden" name="' . $inputName . '" id="' . $options['id'] . '_hidden" value=""' . $disabledHtml . '/>';
                }
            }
            // prepare checked html
            $checkedHtml = '';
            if ($options['checked']) { // the default checked value can be applyed here too
                $checkedHtml = ' checked="checked"';
            }
            $inputHtml .= '<input type="checkbox"';
            $inputHtml .= $nameHtml . $idHtml . $valueHtml . $classHtml . $attrsHtml . $disabledHtml . $checkedHtml;
            $inputHtml .= '/>';
            if ($options['compatibility'] === 'bootstrap') {
                $inputHtml = '<div class="checkbox"><label>' . $inputHtml . '</label></div>';
            }
        }
        // - checkbox group
        else {
            // display hidden input to force the input name presence in form data
            // Make this only for the first occurence of the input otherwise 
            // hidden inputs will override previous regular inputs values
            if ($this->getInputsCount($inputName) == 1) {
                $inputHtml .= '<input type="hidden" name="' . $inputName . '" id="' . $options['id'] . '_hidden" value=""' . $disabledHtml . '/>';
            }
            // prepare checkbox name and its html
            $checkboxName = $inputName . '[]';
            $checkboxNameHtml = 'name="' . $checkboxName . '"';
            $i = 1;
            foreach($options['options'] as $optionValue => $optionLabel) {
                // prepare checked html
                $checkedHtml = '';
                if (in_array((string)$optionValue, (array)$options['value'])) {
                    $checkedHtml = ' checked="checked"';
                }
                // prepare checkbox id and its html
                $checkboxId = $options['id']. '_' . $i++;
                $checkboxIdHtml = ' id="' . $checkboxId . '"';
                // prepare value html
                $escapedValue = $this->escapeHtmlSpecialChars($optionValue);
                $valueHtml = ' value="' . $escapedValue . '"';
                // create checkbox
                $checkboxHtml = '<input type="checkbox"';
                $checkboxHtml .= $checkboxNameHtml . $checkboxIdHtml . $valueHtml . $classHtml . $attrsHtml . $disabledHtml . $checkedHtml;
                $checkboxHtml .= '/>';
                // create checkbox label
                $checkboxLabelHtml = $this->label($field, $optionLabel, array(
                    'separator' => '',
                    'inputId' => $checkboxId,
                    'inputName' => $inputName,
                    'ridtClasses' => $ridtClasses, // ridt classes may be usefull in case of disabled input
                    'requiredMark' => null, // rm is applyed on main label
                    'compatibility' => $options['compatibility'],
                ));
                // render the partial input, label and errors
                if (!$options['toggleTemplate']) {
                    $checkboxHtml = $checkboxHtml . $checkboxLabelHtml;
                }
                else {
                    $checkboxHtml = str_replace(
                        array(':l:', ':i:'),
                        array($checkboxLabelHtml, $checkboxHtml),
                        $options['toggleTemplate']
                    );
                }
                if ($options['compatibility'] === 'bootstrap') {
                    $checkboxHtml = '<span class="checkbox-inline">' . $checkboxHtml . '</span>';
                }
                $inputHtml .= $checkboxHtml;
            }
            if ($options['compatibility'] === 'bootstrap') {
                $inputHtml = '<div class="checkbox">' . $inputHtml . '</div>';
            }
        }
        
        return $this->finalizeInput(
            'checkbox', 
            $field, 
            $inputName, 
            $inputHtml, 
            $ridtClasses, 
            $isRequired, 
            $isDisabled, 
            $isTranslated, 
            $options
        );
    }

    /**
     * Creates HTML for <input type=radio>
     * 
     * @param string $field Name of data field, e.g. 'sex', 'User.sex'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. If provided than 
     *          'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'toggleValue' (string|int|float) The value of single radio input (but submited 
     *          only if the input is checked!!!). Applies only to single radio inputs.
     *          Defaults to 1.
     *      - 'checked' (bool) Initial 'checked' state of the single radio input.
     *          This can be also accomplished using 'value' option or FormHelper::$defaultValues.
     *          The radio input will be checked if its 'toggleValue' equals to 
     *          'value' or to corresponding value in FormHelper::$defaultValues.
     *      - 'options' (array) List of pairs {radioValue} => {radioLabel}.
     *          If specified then a radio group is created. Defaults to empty array().
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'toggleTemplate' (string) Html template to render a partial input of 
     *          radio group, e.g.: '<div class="my-radio">:i::l:</div>
     *          Defaults to FormHelper::$inputDefaults['toggleTemplate'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *      - 'forceSubmit' (bool) Considered only in case of disabled inputs. Values 
     *          of disabled inputs are not submitted (https://www.w3.org/TR/html401/interact/forms.html#disabled).
     *          To disable an input and keep its value to be submitted set both 'disabled'
     *          and 'forceSubmit' to TRUE. Then there is created a hidden input to
     *          ensure the value submition. Defaults to FALSE.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     * 
     * @return string Html code
     */
    public function radio($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'toggleTemplate' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            'forceSubmit' => false,
            // this option is only for single radio
            'checked' => false,
            'toggleValue' => 1,
            // these options apply only to radio group
            'options' => array(),
            
            // Internal use: avoid following to create as attributes
            'type' => null,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);
        
        // input css class
        // - assign ridt class
        $isRequired = $isInvalid = $isDisabled = $isTranslated = null;
        $ridtClasses = $this->ridt($field, $options, $isRequired, $isInvalid, $isDisabled, $isTranslated);
        // check for ignored nontranslated fields
        if (
            $this->nonTranslatedProcessing === 'ignore'
            && $isTranslated === false
            && !$this->hasDefaultLang()
        ) {
            return '';
        }
        // update class by ridt classes
        $options['class'] = ltrim($options['class'] . $ridtClasses);
        // assign inut specific classes
        if ($this->inputDefaults['toggleClass']) {
            $options['class'] = rtrim($this->inputDefaults['toggleClass'] . ' ' . $options['class']);
        }
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // input value
        $options['value'] = $this->value('radio', $field, $options);
        
        // create input properties html
        // - css class 
        $classHtml = '';
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        // - disabled
        $disabledHtml = '';
        if ($isDisabled) {
            $disabledHtml= ' disabled="disabled"';
        }
        // - input name
        $nameHtml = ' name="' . $inputName . '"';
        // - input id
        $idHtml = ' id="' . $options['id'] . '"';
        // - value
        $valueHtml = '';
        if (empty($options['options'])) {
            // in the case of sigle radio the value is set by toggleValue
            $escapedValue = $this->escapeHtmlSpecialChars($options['toggleValue']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        elseif (
            $options['value'] !== null
            // skip radio groups
            && !is_array($options['value']) 
        ) {
            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        // - other attrs
        $attrsHtml = $this->attributes($options, $defaults);
        
        // create input html
        $inputHtml = '';
        // - separatelly created radio from radio group
        if (empty($options['options'])) {
            // prepare checked html
            $options['checked'] = (
                $options['value'] !== null
                && (string)$options['toggleValue'] == (string)$options['value']
            );
            $checkedHtml = '';
            if ($options['checked']) { // the default checked value can be applyed here too
                $checkedHtml = ' checked="checked"';
            }
            // display hidden input to force the input name presence in form data
            // Make this only for the first occurence of the input otherwise 
            // hidden inputs will override previous regular inputs values
            if ($this->getInputsCount($inputName) == 1) {
                $inputHtml .= '<input type="hidden" name="' . $inputName . '" id="' . $options['id'] . '_hidden" value=""' . $disabledHtml . '/>';
            }
            $inputHtml .= '<input type="radio"';
            $inputHtml .= $nameHtml . $idHtml . $valueHtml . $classHtml . $attrsHtml . $disabledHtml . $checkedHtml;
            $inputHtml .= '/>';
            if ($options['compatibility'] === 'bootstrap') {
                $inputHtml = '<div class="radio"><label>' . $inputHtml . '</label></div>';
            }
        }
        // - radio group
        else {
            // display hidden input to force the input name presence in form data
            // Make this only for the first occurence of the input otherwise 
            // hidden inputs will override previous regular inputs values
            if ($this->getInputsCount($inputName) == 1) {
                $inputHtml .= '<input type="hidden" name="' . $inputName . '" id="' . $options['id'] . '_hidden\ value=""' . $disabledHtml . '/>';
            }
            // prepare radio group html
            $i = 1;
            foreach($options['options'] as $optionValue => $optionLabel) {
                // prepare checked html
                $checkedHtml = '';
                if (in_array((string)$optionValue, (array)$options['value'])) {
                    $checkedHtml = ' checked="checked"';
                }
                // prepare radio id and its html
                $radioId = $options['id']. '_' . $i++;
                $radioIdHtml = ' id="' . $radioId . '"';
                // prepare value html
                $escapedValue = $this->escapeHtmlSpecialChars($optionValue);
                $valueHtml = ' value="' . $escapedValue . '"';
                // create radio
                $radioHtml = '<input type="radio"';
                $radioHtml .= $nameHtml . $radioIdHtml . $valueHtml . $classHtml . $attrsHtml . $disabledHtml . $checkedHtml;
                $radioHtml .= '/>';
                // create radio label
                $radioLabelHtml = $this->label($field, $optionLabel, array(
                    'separator' => '',
                    'inputId' => $radioId,
                    'inputName' => $inputName,
                    'ridtClasses' => $ridtClasses, // ridt classes may be usefull in case of disabled input
                    'requiredMark' => false, // rm is applyed on main label
                    'compatibility' => $options['compatibility'],
                ));
                // render the partial input, label and errors
                if (!$options['toggleTemplate']) {
                    $radioHtml = $radioHtml . $radioLabelHtml;
                }
                else {
                    $radioHtml = str_replace(
                        array(':l:', ':i:'),
                        array($radioLabelHtml, $radioHtml),
                        $options['toggleTemplate']
                    );
                }
                if ($options['compatibility'] === 'bootstrap') {
                    $radioHtml = '<span class="radio-inline">' . $radioHtml . '</span>';
                }
                $inputHtml .= $radioHtml;
            }
            if ($options['compatibility'] === 'bootstrap') {
                $inputHtml = '<div class="radio">' . $inputHtml . '</div>';
            }
        }        
        
        return $this->finalizeInput(
            'radio', 
            $field, 
            $inputName, 
            $inputHtml, 
            $ridtClasses, 
            $isRequired, 
            $isDisabled, 
            $isTranslated, 
            $options
        );
    }
    
    /**
     * Creates HTML for <select>
     * 
     * @param string $field Name of data field, e.g. 'country', 'User.country'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Ignored for password and file inputs.
     *          Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. Ignored for password and file inputs.
     *          If provided than 'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'options' (array) In case 'select' input a list of pairs {optionValue} => {optionLabel} 
     *          which will be translated to <option value="{optionValue}">{optionLabel}</option>.
     *          Use nested arrays to define <optgroup>: 
     *              array(
     *                  'Small numbers' => array(
     *                      '1' => 'one',
     *                      '2' => 'two'
     *                  ),
     *                  'Big numbers' => array(
     *                      '100' => 'hundred',
     *                      '1000' => 'thousand'
     *                  )
     *              ),
     *      - 'empty' (bool|string|array) If TRUE then creates an empty item on the top of select input options 
     *          list. If text provided then this is used for empty item label in select 
     *          input options list. Defaults to NULL.
     *      - 'multiple' (bool) If TRUE then multiple options can be selected 
     *          in select. Defaults to FALSE.
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *      - 'forceSubmit' (bool) Considered only in case of disabled inputs. Values 
     *          of disabled inputs are not submitted (https://www.w3.org/TR/html401/interact/forms.html#disabled).
     *          To disable an input and keep its value to be submitted set both 'disabled'
     *          and 'forceSubmit' to TRUE. Then there is created a hidden input to
     *          ensure the value submition. Defaults to FALSE.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     * 
     * @return string Html code
     */
    public function select($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            'options' => array(),       
            'multiple' => false,        
            'empty' => null,
            'forceSubmit' => false,
            
            // Internal use: avoid following to create as attributes
            'type' => null,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);
        
        // input css class
        // - assign ridt class
        $isRequired = $isInvalid = $isDisabled = $isTranslated = null;
        $ridtClasses = $this->ridt($field, $options, $isRequired, $isInvalid, $isDisabled, $isTranslated);
        // - check for ignored nontranslated fields
        if (
            $this->nonTranslatedProcessing === 'ignore'
            && $isTranslated === false
            && !$this->hasDefaultLang()
        ) {
            return '';
        }
        // - update class by ridt classes
        $options['class'] = ltrim($options['class'] . $ridtClasses);
        // - assign input specific classes
        if ($this->inputDefaults['textClass']) {
            $options['class'] = rtrim($this->inputDefaults['textClass'] . ' ' . $options['class']);
        }
        // - add bootstrap classes
        if ($options['compatibility'] === 'bootstrap') {
            $options['class'] = rtrim('form-control '  . $options['class']);
        }
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // input value
        $options['value'] = $this->value('select', $field, $options);
        
        // create input properties html
        // - css class 
        $classHtml = '';
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        // - disabled
        $disabledHtml = '';
        if ($isDisabled) {
            $disabledHtml= ' disabled="disabled"';
        }
        // - input name
        // NOTE: multiple select input creates its own version of nameHtml
        if (empty($options['multiple'])) {
            $multipleHtml = '';
            $nameHtml = ' name="' . $inputName . '"';
        }
        else {
            $multipleHtml = ' multiple="multiple"';
            $nameHtml = ' name="' . $inputName . '[]"';
        }
        // - input id
        $idHtml = ' id="' . $options['id'] . '"';
        // - value
        $valueHtml = '';
        if ($options['value'] !== null) {
            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        // - other attrs
        $attrsHtml = $this->attributes($options, $defaults);    
       
        // create input html
        $inputHtml = '<select';
        $inputHtml .= $nameHtml . $idHtml . $classHtml . $attrsHtml . $disabledHtml . $multipleHtml;
        $inputHtml .= '>';
        // add options
        // - empty option
        if ($options['empty']) {
            $emptyOptionLabel = '';
            if (is_string($options['empty'])) {
                $emptyOptionLabel = $options['empty'];
            }
            $inputHtml .= '<option value="">' . $emptyOptionLabel . '</option>';
        }
        // - regular options
        $options['options'] = (array)$options['options'];
        foreach ($options['options'] as $optGroupLabel => $optGroupItems) {
            if (is_array($optGroupItems)) {
                if (empty($optGroupItems)) {
                    continue;
                }
                $isOptGroup = true;
                $inputHtml .= '<optgroup label="' . $optGroupLabel . '">';
            }
            else {
                $optGroupItems = array($optGroupLabel => $optGroupItems);
                $isOptGroup = false;
            }
            foreach ($optGroupItems as $optionValue => $optionLabel) {
                $selectedHtml = '';
                if (
                    $options['multiple']
                    && in_array((string)$optionValue, (array)$options['value'])   
                    ||
                    // ATTENTION: any int number string key is converted to integer in foreach ($a as $k => $v)!
                    // ATTENTION: ('somestr' == 0) === (0 == 'somestr') === TRUE!
                    // Because (see http://au.php.net/manual/en/language.operators.comparison.php):
                    // "If you compare a number with a string or the comparison involves numerical strings, 
                    // then each string is converted to a number and the comparison performed numerically."
                    (string)$options['value'] === (string)$optionValue
                    && $options['value'] !== null // form GET
                    && $options['value'] !== '' // empty form POST
                ) {
                    $selectedHtml = ' selected="selected"';
                }
                $escapedValue = $this->escapeHtmlSpecialChars($optionValue);
                $inputHtml .= '<option value="' . $escapedValue . '"' . $selectedHtml . '>' . $optionLabel . '</option>';
            }
            if (!empty($isOptGroup)) {
                $inputHtml .= '</optgroup>';
            }
        }
        $inputHtml .= '</select>';        
        
        return $this->finalizeInput(
            'select', 
            $field, 
            $inputName, 
            $inputHtml, 
            $ridtClasses, 
            $isRequired, 
            $isDisabled, 
            $isTranslated, 
            $options
        );
    }
    
    /**
     * Creates HTML for itemselector
     * 
     * @param string $field Name of data field, e.g. 'run_eshop_related_products_id', 
     *          'EshopRelatedProduct.run_eshop_related_products_id'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Ignored for password and file inputs.
     *          Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. Ignored for password and file inputs.
     *          If provided than 'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'options' (array|string) Array of selector available items (id they are 
     *          only few) or a string of URL address to load available options from.
     *          The URL address response must be a json created by instance of AjaxRequest class.
     *          There can be included also params (myParam:myValue) in provided URL.
     *          There must be items 'total', 'message' and 'data' in the response.
     *      - 'ignoreUnexistingItems' (bool) If TRUE then selected unexisting items 
     *          are ignored. Defaults to FALSE.
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     * 
     * @return string Html code
     */
    public function itemSelector($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'options' => array(),
            'ignoreUnexistingItems' => false,
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'toggleTemplate' => null,
            'itemselectorCssFile' => null,
            'itemselectorJsFile' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            
            // Internal use: avoid following to create as attributes
            'type' => null,
            'forceSubmit' => false,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);
        // turn off this option for this type of input
        $options['forceSubmit'] = false;
        
        // input css class
        // - assign ridt class
        $isRequired = $isInvalid = $isDisabled = $isTranslated = null;
        $ridtClasses = $this->ridt($field, $options, $isRequired, $isInvalid, $isDisabled, $isTranslated);
        // - check for ignored nontranslated fields
        if (
            $this->nonTranslatedProcessing === 'ignore'
            && $isTranslated === false
            && !$this->hasDefaultLang()
        ) {
            return '';
        }
        // - update class by ridt classes
        $options['class'] = ltrim($options['class'] . $ridtClasses);
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // input value
        $options['value'] = $this->value('itemselector', $field, $options);
        
        // create input properties html
        // - css class 
        $classHtml = '';
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        // - disabled
        $disabledHtml = '';
        if ($isDisabled) {
            $disabledHtml= ' disabled="disabled"';
        }
        // - input name
        $nameHtml = ' name="' . $inputName . '"'; 
        // - input id
        $idHtml = ' id="' . $options['id'] . '"';
        // - value
        $valueHtml = '';
        if (is_array($options['value'])) {
            $escapedValue = $this->escapeHtmlSpecialChars(implode(';', $options['value']));
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        elseif ($options['value'] !== null) {
            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        // - other attrs
        $attrsHtml = $this->attributes($options, $defaults);  
        
        // create input html
        $inputHtml = '';
        // - attach itemselector js and css files (if defined and not attached yet)
        if (empty(self::$attachedFiles[$options['itemselectorCssFile']])) {
            App::setCssFiles($options['itemselectorCssFile']);
            self::$attachedFiles[$options['itemselectorCssFile']] = true;
        }
        if (empty(self::$attachedFiles[$this->jQueryJsFile])) {
            App::setJsFiles($this->jQueryJsFile);
            self::$attachedFiles[$this->jQueryJsFile] = true;
        }
        if (empty(self::$attachedFiles[$this->jQueryUiJsFile])) {
            App::setJsFiles($this->jQueryUiJsFile);
            self::$attachedFiles[$this->jQueryUiJsFile] = true;
        }
        if (empty(self::$attachedFiles[$options['itemselectorJsFile']])) {
            App::setJsFiles($options['itemselectorJsFile']);
            self::$attachedFiles[$options['itemselectorJsFile']] = true;
        }
        // - prepare data for itemselector view
        if (is_array($options['value'])) {
            $inputItemValues = $options['value'];
        } 
        else {
            $inputItemValues = explode(';', $options['value']);
        }
        $inputItems = array();
        $optionItems = array();
        $showSearch = false;
        $searchMessage = '';
        $layout = App::getLayout();
        $total = 0;
        if (is_array($options['options'])) {
            // option values are listed in array
            $optionItems = $options['options'];
            if (!empty($inputItemValues)) {
                foreach ($inputItemValues as $inputItemValue) {
                    if ($inputItemValue == '') {
                        continue;
                    }
                    if (isset($optionItems[$inputItemValue])) {
                        $inputItems[$inputItemValue] = $optionItems[$inputItemValue];
                    } else {
                        $inputItems[$inputItemValue] = $inputItemValue;
                    }
                }
            }
        } 
        elseif (!empty($options['options'])) {
            // option values are on remote url
            $urlArray = App::parseUrl($options['options']);
            // get list of all items. if count of items is too big, response contain only that count
            $response = App::loadControllerAction($urlArray['module'], $urlArray['controller'], $urlArray['action'], $urlArray['params']);
            $response = json_decode($response, true);
            if ($response['total'] < App::getSetting('App', 'itemselector.maxItemsToShow')) {
                $optionItems = $response['data'];
            } else {
                $optionItems = array();
                $showSearch = true;
                $searchMessage = $response['message'];
                $total = $response['total'];
            }
            if (!empty($inputItemValues)) {
                // get labels of ids(values) of record
                $response = App::loadControllerAction($urlArray['module'], $urlArray['controller'], $urlArray['action'], 
                   $urlArray['params'], null, array(null, $inputItemValues, $options['ignoreUnexistingItems'])
                );
                $response = json_decode($response, true);
                $inputItemsLabels = $response['data'];
                foreach ($inputItemValues as $inputItemValue) {
                    if ($inputItemValue == '') {
                        continue;
                    }
                    if (isset($inputItemsLabels[$inputItemValue])) {
                        $inputItemLabel = $inputItemsLabels[$inputItemValue];
                    } 
                    elseif (empty($options['ignoreUnexistingItems'])) {
                        $inputItemLabel = $inputItemValue;
                    }
                    else {
                        continue;
                    }
                    $inputItems[$inputItemValue] = $inputItemLabel;
                }
            }
        }

        foreach ($inputItems as $itemValue => $itemLabel) {
            unset($optionItems[$itemValue]);
        }
        $inputHtml .= '<input type="hidden"';
        $inputHtml .= $nameHtml . $idHtml . $valueHtml . $attrsHtml;
        $inputHtml .= '/>';
        $inputHtml .= App::loadView('App', 'Form/itemselector', array(
            'inputItems' => $inputItems,
            'optionItems' => $optionItems,
            'inputId' => $options['id'],
            'inputClass' => $options['class'],
            'showSearch' => $showSearch,
            'remoteUrl' => (!is_array($options['options'])) ? $options['options'] : '',
            'searchMessage' => $searchMessage,
            'total' => $total
        ));
        App::setLayout($layout['module'], $layout['name']);        
        
        return $this->finalizeInput(
            'itemselector', 
            $field, 
            $inputName, 
            $inputHtml, 
            $ridtClasses, 
            $isRequired, 
            $isDisabled, 
            $isTranslated, 
            $options
        );
    }
    
    /**
     * Creates HTML for treeselector
     * 
     * @param string $field Name of data field, e.g. 'run_eshop_product_categories_id', 
     *          'EshopProductCategoryProduct.run_eshop_product_categories_id'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Ignored for password and file inputs.
     *          Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. Ignored for password and file inputs.
     *          If provided than 'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'options' (array|string) Array of selector available items (id they are 
     *          only few) or a string of URL address to load available options from.
     *          The URL address response must be a json created by instance of AjaxRequest class.
     *          There must be items 'total', 'message' and 'data' in the response.
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     * 
     * @return string Html code
     */
    public function treeSelector($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'options' => array(),
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'toggleTemplate' => null,
            'itemselectorCssFile' => null,
            'itemselectorJsFile' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            
            // Internal use: avoid following to create as attributes
            'type' => null,
            'forceSubmit' => false,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);
        // turn off this option for this type of input
        $options['forceSubmit'] = false;
        
        // input css class
        // - assign ridt class
        $isRequired = $isInvalid = $isDisabled = $isTranslated = null;
        $ridtClasses = $this->ridt($field, $options, $isRequired, $isInvalid, $isDisabled, $isTranslated);
        // - check for ignored nontranslated fields
        if (
            $this->nonTranslatedProcessing === 'ignore'
            && $isTranslated === false
            && !$this->hasDefaultLang()
        ) {
            return '';
        }
        // - update class by ridt classes
        $options['class'] = ltrim($options['class'] . $ridtClasses);
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // input value
        $options['value'] = $this->value('treeselector', $field, $options);
        
        // create input properties html
        // - css class 
        $classHtml = '';
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        // - disabled
        $disabledHtml = '';
        if ($isDisabled) {
            $disabledHtml= ' disabled="disabled"';
        }
        // - input name
        $nameHtml = ' name="' . $inputName . '"'; 
        // - input id
        $idHtml = ' id="' . $options['id'] . '"';
        // - value
        $valueHtml = '';
        if (is_array($options['value'])) {
            $escapedValue = $this->escapeHtmlSpecialChars(implode(';', $options['value']));
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        elseif ($options['value'] !== null) {
            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        // - other attrs
        $attrsHtml = $this->attributes($options, $defaults);  
        
        // create input html
        $inputHtml = '';
        // - attach itemselector js and css files (if defined and not attached yet)
        if (empty(self::$attachedFiles[$options['treeselectorCssFile']])) {
            App::setCssFiles($options['treeselectorCssFile']);
            self::$attachedFiles[$options['treeselectorCssFile']] = true;
        }
        if (empty(self::$attachedFiles[$this->jQueryJsFile])) {
            App::setJsFiles($this->jQueryJsFile);
            self::$attachedFiles[$this->jQueryJsFile] = true;
        }
        if (empty(self::$attachedFiles[$this->jQueryUiJsFile])) {
            App::setJsFiles($this->jQueryUiJsFile);
            self::$attachedFiles[$this->jQueryUiJsFile] = true;
        }
        if (empty(self::$attachedFiles[$options['treeselectorJsFile']])) {
            App::setJsFiles($options['treeselectorJsFile']);
            self::$attachedFiles[$options['treeselectorJsFile']] = true;
        }
        // - prepare data for treeselector view
        if (is_array($options['value'])) {
            $inputItemValues = $options['value'];
        } else {
            $inputItemValues = explode(';', $options['value']);
        }
        $inputItems = array();
        $searchMessage = '';
        $layout = App::getLayout();
        $total = 0;
        if (is_array($options['options'])) {
            // option values are listed in array
            $optionItems = $options['options'];
            if (!empty($inputItemValues)) {
                foreach ($inputItemValues as $inputItemValue) {
                    if ($inputItemValue == '') {
                        continue;
                    }
                    if (isset($optionItems[$inputItemValue])) {
                        $inputItems[$inputItemValue] = $optionItems[$inputItemValue];
                    } else {
                        $inputItems[$inputItemValue] = $inputItemValue;
                    }
                }
            }
        } 
        elseif (!empty($options['options'])) {
            // option values are on remote url
            $urlArray = App::parseUrl($options['options']);
            // get list of all items. if count of items is too big, response contain only that count
            $response = App::loadControllerAction($urlArray['module'], $urlArray['controller'], $urlArray['action'], $urlArray['params']);
            $response = json_decode($response, true);
            $optionItems = $response['data']['records'];

            if (!empty($inputItemValues)) {
                // get labels of ids(values) of record
                $response = App::loadControllerAction($urlArray['module'], $urlArray['controller'], $urlArray['action'], 
                   $urlArray['params'], null, array(null, $inputItemValues)
                );
                $response = json_decode($response, true);
                $inputItemsLabels = $response['data']['records'];
                foreach ($inputItemValues as $inputItemValue) {
                    if ($inputItemValue == '') {
                        continue;
                    }
                    if (isset($inputItemsLabels[$inputItemValue])) {
                        $inputItems[$inputItemValue] = $inputItemsLabels[$inputItemValue];
                    } else {
                        $inputItems[$inputItemValue] = $inputItemValue;
                    }
                }
            }
        }

        foreach ($inputItems as $itemValue => $itemLabel) {
            unset($optionItems[$itemValue]);
        }
        $inputHtml .= '<input type="hidden"';
        $inputHtml .= $nameHtml . $idHtml . $valueHtml . $attrsHtml;
        $inputHtml .= '/>';
        $inputHtml .= App::loadView('App', 'Form/treeselector', array(
            'inputItems' => $inputItems,
            'optionItems' => $optionItems,
            'inputId' => $options['id'],
            'inputClass' => $options['class'],
            'remoteUrl' => (!is_array($options['options'])) ? $options['options'] : '',
            'searchMessage' => $searchMessage,
            'total' => $total
        ));
        App::setLayout($layout['module'], $layout['name']);
                
        return $this->finalizeInput(
            'treeselector', 
            $field, 
            $inputName, 
            $inputHtml, 
            $ridtClasses, 
            $isRequired, 
            $isDisabled, 
            $isTranslated, 
            $options
        );
    }
    
    /**
     * Creates HTML for display field
     * 
     * @param string $field Name of data field, e.g. 'message', 'User.message'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. If provided than 
     *          'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'deleteValue' (string|array|bool) Value delete action definition. Can be 
     *          provided as string URL or as array of action options or as TRUE to
     *          create a passive delete button to be treated (even confirm message) 
     *          by js. If provided as string or array and user has rights for URL 
     *          then delete button is generated. If provided as TRUE then passive 
     *          button is generated always. Defaults to empty array().
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     *  
     * @return string Html code
     */
    public function display($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'deleteValue' => array(),
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'template' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            
            // Internal use: avoid following to create as attributes
            'type' => null,
            'forceSubmit' => false,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);
        // turn off this option for this type of input
        $options['forceSubmit'] = false;
        
        // prepare input properties
        // input css class (not used for hidden fields)
        if ($this->inputDefaults['displayClass']) {
            $options['class'] = rtrim($this->inputDefaults['displayClass'] . ' ' . $options['class']);
        }
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // value
        $options['value'] = $this->value('display', $field, $options);
        
        // create input properties html
        // - css class 
        $classHtml = '';
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        // - input name
        $nameHtml = ' data-name="' . $inputName . '"'; 
        // - input id
        $idHtml = ' id="' . $options['id'] . '"';
        // - value
        $valueHtml = '';
        if ($options['value'] !== null) {
            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        // - other attrs
        $attrsHtml = $this->attributes($options, $defaults);
        
        // ignore delete action if field is mandatory (you cannot delete 
        // required value without finishing with validation error)    
        if ($this->isRequired($field)) {
            $options['deleteValue'] = array();
        }
        // check rights for delete action if specified
        if (
            $options['deleteValue'] 
        ) {
            $deleteActionDefaults = array(
                'label' => '&times;',
                'title' => __(__FILE__, 'Delete'),
                'confirmMessage' => __(__FILE__, 'Please confirm removal'),
                'url' => null,
            );
            if (!is_array($options['deleteValue'])) {
                $options['deleteValue'] = array('url' => $options['deleteValue']);
            }
            $options['deleteValue'] = array_merge($deleteActionDefaults, $options['deleteValue']);
            // accept / refuse action 
            if (!is_string($options['deleteValue']['url'])) {
                $options['deleteValue']['url'] = null;
                $options['deleteValue']['confirmMessage'] = null;
            }
            elseif (!Html::acceptAction($options['deleteValue'])) {
                $options['deleteValue'] = array();
            }
        }
        // create input html
        $transferInputName = $this->createTransferInputName($field);
        $inputHtml = '<textarea name="' . $transferInputName . '" id="' . $options['id'] . '_hidden" style="display:none">';
        $inputHtml .= $options['value'];
        $inputHtml .= '</textarea>';
        if ($options['deleteValue']) {
            $wrapperId = $options['id'] . '_delete_button_wrapper';
            $wrapperIdHtml = ' id="' . $wrapperId . '"';
            $wrapperClass = $this->inputDefaults['displayClass'] . '-wrapper';
            $wrapperClassHtml = ' class="' . $wrapperClass . '"';
            $wrapperStyleHtml = empty($options['value']) ? ' style="display:none"' : '';
            $inputHtml .= '<div' . $wrapperIdHtml . $wrapperClassHtml . $wrapperStyleHtml . '>';
        }
        $inputHtml .= '<div';
        $inputHtml .= $nameHtml . $idHtml . $classHtml . $attrsHtml;
        $inputHtml .= '>';
        $inputHtml .= $options['value'];
        $inputHtml .= '</div>';
        if ($options['deleteValue']) {
            $inputHtml .= Html::action(
                $options['deleteValue'],
                array('attributes' => array('class' => 'display-delete-button'))
            );
            $inputHtml .= '</div>';
        }
        
        $options['attachErrors'] = false;
        $options['attachWarnings'] = false;
        return $this->finalizeInput(
            'display', 
            $field, 
            $inputName, 
            $inputHtml, 
            '', 
            false, 
            false, 
            false, 
            $options
        );
    }
    
    /**
     * Creates HTML for display image field
     * 
     * @param string $field Name of data field, e.g. 'photo', 'User.photo'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. If provided than 
     *          'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'deleteValue' (string|array|bool) Value delete action definition. Can be 
     *          provided as string URL or as array of action options or as TRUE to
     *          create a passive delete button to be treated (even confirm message) 
     *          by js. If provided as string or array and user has rights for URL 
     *          then delete button is generated. If provided as TRUE then passive 
     *          button is generated always. Defaults to empty array().
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     *      - 'autofocus' (bool) If TRUE then input get automatically focus when page with form loads.
     *  
     * @return string Html code
     */
    public function image($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            //'deleteImage' => array(), //@deprecated
            'deleteValue' => array(),
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'template' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            
            // Internal use: avoid following to create as attributes
            'type' => null,
            'forceSubmit' => false,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);
        // turn off this option for this type of input
        $options['forceSubmit'] = false;
        // keep backward compatibility of deprecated option 'deleteImage'
        if (!empty($options['deleteImage']) && empty($options['deleteValue'])) {
            $options['deleteValue'] = $options['deleteImage'];
        }
        
        // prepare input properties
        // input css class (not used for hidden fields)
        if ($this->inputDefaults['imageClass']) {
            $options['class'] = rtrim($this->inputDefaults['imageClass'] . ' ' . $options['class']);
        }
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // value
        $options['value'] = $this->value('image', $field, $options);
        
        // create input properties html
        // - css class 
        $classHtml = '';
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        // - input name
        $nameHtml = ' data-name="' . $inputName . '"'; 
        // - input id
        $idHtml = ' id="' . $options['id'] . '"';
        // - value
        $valueHtml = '';
        if ($options['value'] !== null) {
            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        // - other attrs
        $explicitAttributes = empty($options['value']) ? array('style' => 'display:none') : array();
        $attrsHtml = $this->attributes($options, $defaults, $explicitAttributes);
        
        // ignore delete action if image is mandatory (you cannot delete 
        // required value without finishing with validation error)    
        if ($this->isRequired($field)) {
            $options['deleteValue'] = array();
        }
        // check rights for delete action if specified
        if (
            $options['deleteValue'] 
        ) {
            $deleteActionDefaults = array(
                'label' => '&times;',
                'title' => __(__FILE__, 'Delete image'),
                'confirmMessage' => __(__FILE__, 'Please confirm removal of image'),
                'url' => null,
            );
            if (!is_array($options['deleteValue'])) {
                $options['deleteValue'] = array('url' => $options['deleteValue']);
            }
            $options['deleteValue'] = array_merge($deleteActionDefaults, $options['deleteValue']);
            // accept / refuse action 
            if (!is_string($options['deleteValue']['url'])) {
                $options['deleteValue']['url'] = null;
                $options['deleteValue']['confirmMessage'] = null;
            }
            elseif (!Html::acceptAction($options['deleteValue'])) {
                $options['deleteValue'] = array();
            }
        }
        // create input html
        $transferInputName = $this->createTransferInputName($field);
        $inputHtml = '<textarea name="' . $transferInputName . '" id="' . $options['id'] . '_hidden" style="display:none">' . $options['value'] . '</textarea>';
        if ($options['deleteValue']) {
            $wrapperId = $options['id'] . '_delete_button_wrapper';
            $wrapperIdHtml = ' id="' . $wrapperId . '"';
            $wrapperClass = $this->inputDefaults['imageClass'] . '-wrapper';
            $wrapperClassHtml = ' class="' . $wrapperClass . '"';
            $wrapperStyleHtml = empty($options['value']) ? ' style="display:none"' : '';
            $inputHtml .= '<div' . $wrapperIdHtml . $wrapperClassHtml . $wrapperStyleHtml . '>';
        }
        $inputHtml .= '<img';
        $inputHtml .= $nameHtml . $idHtml . $classHtml . $attrsHtml;
        $inputHtml .= ' alt="" src="' . $options['value'] . '"/>';
        if ($options['deleteValue']) {
            $inputHtml .= Html::action(
                $options['deleteValue'],
                array('attributes' => array('class' => 'display-delete-button'))
            );
            $inputHtml .= '</div>';
        }
        
        $options['attachErrors'] = false;
        $options['attachWarnings'] = false;
        return $this->finalizeInput(
            'image', 
            $field, 
            $inputName, 
            $inputHtml, 
            '', 
            false, 
            false, 
            false, 
            $options
        );
    }
    
    /**
     * Creates HTML for editor input
     * 
     * @param string $field Name of data field, e.g. 'description', 'EshopProduct.description'. Field name
     *          is used to find out input value in FormHelper::$data, input errors 
     *          in FormHelper::$errors, input warnings in FormHelper::$warnings and required flag in FormHelper::$required.
     *          For more details, how to create field names, see the end of FormHelper
     *          class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Ignored for password and file inputs.
     *          Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. Ignored for password and file inputs.
     *          If provided than 'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'options' (array) Options which will be passed to new created instance 
     *          of Html editor (after beeing json encoded). For case of ckeditor 
     *          see http://docs.cksource.com/ckeditor_api/symbols/CKEDITOR.config.html
     *          for available options, e.g. array('height' => '500px').
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'editorTemplate' (string) Html template to render HTML editor into.
     *          Use :class:, :id: and :editor: inserts to place respective items to the template.
     *          Defaults to FormHelper::$inputDefaults['editorTemplate'].
     *      - 'editorScript' (string) Html definition of js script to be attached to 
     *          editor input to launch the editor. Use :id: and :options: inserts to 
     *          place respective items. Defaults to FormHelper::$inputDefaults['editorScript'].
     *      - 'editorJsFile' (string) Url path to editor js library script. Path must 
     *          be app root relative. This file is added to js dynamic files on creation 
     *          of the first editor input. Defaults to FormHelper::$inputDefaults['editorJsFile'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *      - 'forceSubmit' (bool) Considered only in case of disabled inputs. Values 
     *          of disabled inputs are not submitted (https://www.w3.org/TR/html401/interact/forms.html#disabled).
     *          To disable an input and keep its value to be submitted set both 'disabled'
     *          and 'forceSubmit' to TRUE. Then there is created a hidden input to
     *          ensure the value submition. Defaults to FALSE.
     * 
     * NOTE: Any other items (placeholder, onclick, autofocus, ...) in $options are 
     *      considered to be attributes. As attribute values are encolsed to double 
     *      quotes (") so use apostrophes for string literals (e.g. in js events). 
     *      Some usefull attributes:
     *      - 'placeholder' (string) Empty input "background text"
     * 
     * @return string Html code
     */
    public function editor($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'options' => array(),       
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'editorTemplate' => null,
            'editorScript' => null,
            'editorJsFile' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            'forceSubmit' => false,
            
            // Internal use: avoid following to create as attributes
            'type' => null,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);
        
        // input css class
        // - assign ridt class
        $isRequired = $isInvalid = $isDisabled = $isTranslated = null;
        $ridtClasses = $this->ridt($field, $options, $isRequired, $isInvalid, $isDisabled, $isTranslated);
        // - check for ignored nontranslated fields
        if (
            $this->nonTranslatedProcessing === 'ignore'
            && $isTranslated === false
            && !$this->hasDefaultLang()
        ) {
            return '';
        }
        // - update class by ridt classes
        $options['class'] = ltrim($options['class'] . $ridtClasses);
        // - assign input specific classes
        if ($this->inputDefaults['textClass']) {
            $options['class'] = rtrim($this->inputDefaults['textClass'] . ' ' . $options['class']);
        }

        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // input value
        $options['value'] = $this->value('editor', $field, $options);
        
        // create input properties html
        // - css class 
        $classHtml = '';
        if ($options['class']) {
            $classHtml = ' class="' . $options['class'] . '"';
        }
        // - disabled
        $disabledHtml = '';
        if ($isDisabled) {
            $disabledHtml= ' disabled="disabled"';
        }
        // - input name
        $nameHtml = ' name="' . $inputName . '"'; 
        // - input id
        $idHtml = ' id="' . $options['id'] . '"';
        // - value
        $valueHtml = '';
        if ($options['value'] !== null) {
            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
            $valueHtml = ' value="' . $escapedValue . '"';
        }
        // - other attrs
        $attrsHtml = $this->attributes($options, $defaults);
        
        // create input html
        // - attach editor js files (if defined and not attached yet)
        if (empty(self::$attachedFiles[$this->jQueryJsFile])) {
            App::setJsFiles($this->jQueryJsFile);
            self::$attachedFiles[$this->jQueryJsFile] = true;
        }
        if (empty(self::$attachedFiles[$options['editorJsFile']])) {
            App::setJsFiles($options['editorJsFile']);
            // hide toolbar and show it only for active editor
            App::setCss('.cke_toolbox,.cke_resizer,.cke_path{display:none;}');
            App::setCss('.cke_focus .cke_toolbox{display:inline;}');
            App::setCss('.cke_focus .cke_resizer,.cke_focus .cke_path{display:block;}');
            App::setCss('.cke_maximized .cke_toolbox{display:inline;}');
            App::setCss('.cke_maximized .cke_resizer,.cke_focus .cke_path{display:block;}');
            // when toolbar is at bottom:
            // - make top border nicer
            App::setCss('.cke{padding-top: 3px!important;}');
            // - show "element path" label and place it under toolbar 
            App::setCss('.cke_focus [id$="_path_label"]{display:block!important;float:left!important;clear:both!important;padding-left:2px!important;}');
            App::setCss('[id$="_path_label"]:after{content:": ";display:inline;}');
            // - place correctly the resizer to bottom right corner
            App::setCss('.cke_resizer{position:absolute!important;bottom:0!important;right:0!important;}');
            // - do extra bottom padding if CKE is maximized
            App::setCss('.cke_maximized [id$="_path_label"]{display:block!important;float:left!important;clear:both!important;padding-left:2px!important;}');
            App::setCss('.cke_maximized .cke_bottom{padding-bottom:23px!important;}');
            self::$attachedFiles[$options['editorJsFile']] = true;
        }
        App::setJsI18nFiles('App');
        // - create text area
        $inputHtml = '<textarea';
        $inputHtml .= $nameHtml . $idHtml . $classHtml . $attrsHtml . $disabledHtml;
        $inputHtml .= '>';
        $inputHtml .= $options['value'];
        $inputHtml .= '</textarea>';
        $inputHtml = str_replace(
            array(':editor:', ':class:', ':id:'),
            array($inputHtml, $options['class'], $options['id'] . '_editor'),
            $options['editorTemplate']
        );
        // - attach launching script
        App::setJs(str_replace(
            array(':id:', ':options:'),
            array($options['id'], json_encode($options['options'])),
            $options['editorScript']
        ));
                
        return $this->finalizeInput(
            'editor', 
            $field, 
            $inputName, 
            $inputHtml, 
            $ridtClasses, 
            $isRequired, 
            $isDisabled, 
            $isTranslated, 
            $options
        );
    }
    
    /**
     * Creates HTML for gallery input
     * 
     * @param string $field Name of data field. In case of gallery this is the file field
     *          of images model, e.g. 'file', 'EshopProductImage.file'. For more details, 
     *          how to create field names, see the end of FormHelper class phpDoc.
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Ignored for password and file inputs.
     *          Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. Ignored for password and file inputs.
     *          If provided than 'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *      - 'options' (array) For case of images gallery following options can be used:
     *          - 'foreignKey' (integer) Images owner id. Mandatory, an exception 
     *              is thrown if not provided.
     *          - 'nameField' (string) Name of field with image name. Defaults to 'name'.
     *          - 'idField' (string) Name of image primary key field. Defaults to 'id'.
     *          - 'Model' (Model) Images model instance. ??? Seems to be unused. Defaults to NULL.
     *          - 'fields' (array) Array of image fields to create inputs for. 
     *              Defaults to empty array().
     *          - 'actions' (array) Images actions definitions. Following actions are available:
     *              'load', 'add', 'update', 'move', 'delete'. For action definition details see Html::action().
     *              Defaults to array('load' => array(), 'add' => array(), 'update' => array(), 
     *              'move' => array(), 'delete' => array()),
     *          - 'itemThumbHeight' (string) Height of image thumb. Defaults to '100px'.
     *          - 'itemsContainerMaxHeight' (string) Max height of image container.
     *              Defaults to '300px'.
     *          - 'fileMaxSize' (integer|string) Max allowed uplod filesize.
     *              Can be provided as integer number of bytes or as filesize string
     *              e.g. '20MB'. Defaults to 5242880 (= '5MB').
     *          - 'template' (string) Gallery input template. Defaults to '<div id=":id:" class=":class:"><div class="gallery-input-items"></div><div class="gallery-input-errors"></div><div class="gallery-input-controls">:fields:<div class="gallery-input-buttons">:update::delete::deleteMany:</div></div></div>'.
     *              ATTENTION: jQuery.sortable needs at least one initial list 
     *              item to be correctly initialized - see <div style="display:none"></div> 
     *              in .gallery-input-items
     *          - 'controlsContainerClass' (string) Defaults to 'gallery-input-controls'.
     *          - 'itemsContainerClass' (string) Defaults to 'gallery-input-items'.
     *          - 'errorsContainerClass' (string) Defaults to 'gallery-input-errors'.
     *          - 'itemTemplate' (string) Defaults to '<div class="gallery-input-item"><img src=":_file:"><span>:_name:</span></div>'.
     *          - 'cssFile' (string) Defaults to '/app/css/libs/galleryInput.css'.
     *          - 'jsFile' (string) Defaults to '/app/js/libs/GalleryInput.js'.
     *          
     * @return string Html code
     */
    public function gallery($field, $options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            'options' => array(),       
            
            // Internal use: avoid following to create as attributes
            'type' => null,
            'forceSubmit' => false,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);
        // turn off this option for this type of input
        $options['forceSubmit'] = false;
        $galleryDefaults = array(
            'foreignKey' => null,
            'nameField' => 'name',
            'idField' => 'id',
            'Model' => null,
            'fields' => array(),
            'actions' => array(
                'load' => array(),
                'add' => array(),
                'update' => array(),
                'move' => array(),
                'delete' => array(),
                //'deleteMany' => array(),
            ),
            'itemThumbHeight' => '100px',
            'itemsContainerMaxHeight' => '300px',
            'fileMaxSize' => 5242880,
            // ATTENTION: jQuery.sortable needs at least one initial list item to be correctly initialized 
            // See <div style="display:none"></div> in .gallery-input-items
            'template' => '<div id=":id:" class=":class:"><div class="gallery-input-items"></div><div class="gallery-input-errors"></div><div class="gallery-input-controls">:fields:<div class="gallery-input-buttons">:update::delete::deleteMany:</div></div></div>',
            'controlsContainerClass' => 'gallery-input-controls',
            'itemsContainerClass' => 'gallery-input-items',
            'errorsContainerClass' => 'gallery-input-errors',
            'itemTemplate' => '<div class="gallery-input-item"><img src=":_file:"><span>:_name:</span></div>',
            'cssFile' => '/app/css/libs/galleryInput.css',
            'jsFile' => '/app/js/libs/GalleryInput.js',
        );
        $galleryOptions = Arr::mergeRecursive($galleryDefaults, (array)$options['options']);
        // validate foreign key
        if (empty($galleryOptions['foreignKey'])) {
            throw new Exception(__e(__FILE__, 'Undefined gallery foreign key'));
        }
        // normalize fileMaxSize
        if (empty($galleryOptions['fileMaxSize'])) {
            $uploadMaxSize = Utility::convertToBytes(ini_get('upload_max_filesize'));
            $postMaxSize = Utility::convertToBytes(ini_get('post_max_size'));
            // choose the smaller one
            $galleryOptions['fileMaxSize'] = $postMaxSize < $uploadMaxSize ? $postMaxSize : $uploadMaxSize;
        }
        else {
            $galleryOptions['fileMaxSize'] = Utility::convertToBytes($galleryOptions['fileMaxSize']);
        }
        // - 5MB as the last possibilty if nothing else above
        if (empty($galleryOptions['fileMaxSize'])) {
            $galleryOptions['fileMaxSize'] = $galleryDefaults['fileMaxSize']; 
        }
        
        // input css class
        // - assign ridt class
        $isRequired = $isInvalid = $isDisabled = $isTranslated = null;
        $ridtClasses = $this->ridt($field, $options, $isRequired, $isInvalid, $isDisabled, $isTranslated);
        // - check for ignored nontranslated fields
        if (
            $this->nonTranslatedProcessing === 'ignore'
            && $isTranslated === false
            && !$this->hasDefaultLang()
        ) {
            return '';
        }
        // - update class by ridt classes
        $options['class'] = ltrim($options['class'] . $ridtClasses);
        // - assign input specific classes
        if ($this->inputDefaults['galleryClass']) {
            $options['class'] = rtrim($this->inputDefaults['galleryClass'] . ' ' . $options['class']);
        }
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // input value
        $options['value'] = $this->value('gallery', $field, $options);
        
////mojo: not used in gallery input        
//        // create input properties html
//        // - css class 
//        $classHtml = '';
//        if ($options['class']) {
//            $classHtml = ' class="' . $options['class'] . '"';
//        }
//        // - disabled
//        $disabledHtml = '';
//        if ($isDisabled) {
//            $disabledHtml= ' disabled="disabled"';
//        }
//        // - input name
//        $nameHtml = ' name="' . $inputName . '"'; 
//        // - input id
//        $idHtml = ' id="' . $options['id'] . '"';
//        // - value
//        $valueHtml = '';
//        if ($options['value'] !== null) {
//            $escapedValue = $this->escapeHtmlSpecialChars($options['value']);
//            $valueHtml = ' value="' . $escapedValue . '"';
//        }
//        // - other attrs
//        $attrsHtml = $this->attributes($options, $defaults);
        
        // create input html
        // - attach css and js
        if (empty(self::$attachedFiles[$galleryOptions['cssFile']])) {
            App::setCssFiles($galleryOptions['cssFile']);
            self::$attachedFiles[$galleryOptions['cssFile']] = true;
        }
        if (empty(self::$attachedFiles[$this->jQueryJsFile])) {
            App::setJsFiles($this->jQueryJsFile);
            self::$attachedFiles[$this->jQueryJsFile] = true;
        }
        if (empty(self::$attachedFiles[$this->jQueryUiJsFile])) {
            App::setJsFiles($this->jQueryUiJsFile);
            self::$attachedFiles[$this->jQueryUiJsFile] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/Validate.js'])) {
            App::setJsFiles('/app/js/libs/Validate.js');
            self::$attachedFiles['/app/js/libs/Validate.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/Utility.js'])) {
            App::setJsFiles('/app/js/libs/Utility.js');
            self::$attachedFiles['/app/js/libs/Utility.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/PhpJs.js'])) {
            App::setJsFiles('/app/js/libs/PhpJs.js');
            self::$attachedFiles['/app/js/libs/PhpJs.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/Number.js'])) {
            App::setJsFiles('/app/js/libs/Number.js');
            self::$attachedFiles['/app/js/libs/Number.js'] = true;
        }
        if (empty(self::$attachedFiles[$galleryOptions['jsFile']])) {
            App::setJsFiles($galleryOptions['jsFile']);
            self::$attachedFiles[$galleryOptions['jsFile']] = true;
        }
        App::setJsI18nFiles('App');
        // - generate gallery actions html
        $actionsHtml = array(
            'update' => '',
            'delete' => '',
            'deleteMany' => '',
        );
        foreach ($galleryOptions['actions'] as $actionName => &$action  ) {
            if (Validate::intNumber($actionName)) {
                throw new Exception(__e(__FILE__, 'Invalid action definition'));
            }
            // skip empty actions
            if (empty($action)) {
                unset($galleryOptions['actions'][$actionName]);
                continue;
            }
            if ($action && !is_array($action)) {
                $action = array('url' => $action);
            }
            // accept / refuse action 
            if (!Html::acceptAction($action)) {
                unset($galleryOptions['actions'][$actionName]);
                continue;
            }
            if (
                $actionName === 'update'
                || $actionName === 'delete'
                || $actionName === 'deleteMany'
            ) {
                $actionsHtml[$actionName] = Html::action($action, array('attributes' => array(
                    'class' => $options['compatibility'] === 'bootstrap' ? 'btn btn-default' : null,
                )));
            }
            // normalize url
            $action['url'] = App::getUrl($action['url']);
        }
        unset($action); // unset reference
        // - if user has no rights to see the gallery items then avoid gallery html creation
        if (empty($galleryOptions['actions']['load'])) {
            return '';
        }
        // - generate gallery fields html
        $fieldsHtml = '';
        foreach ($galleryOptions['fields'] as $k => $v) {
            if (!empty($v['field'])) {
                $fieldName = $v['field'];
                unset($v['field']);
                $fieldOptions = $v;
            }
            elseif (is_string($k)) {
                $fieldName = $k;
                $fieldOptions = $v;
            }
            else {
                throw new Exception(__e(__FILE__, 'Invalid gallery input definitions %s (missing field name)', print_r($v, true)));
            }
            // gallery input field must be of type 'file' and must not be multiple (just one image per item)
            // and must accept just images
            if ($fieldName === $field) {
                $fieldOptions['type'] = 'file';
                unset($fieldOptions['multiple']);
                $fieldOptions['accept'] = 'image/*';
            }
            $fieldsHtml .= $this->input($fieldName, $fieldOptions);
        }
        $inputHtml = str_replace(
            array(':class:', ':id:', ':fields:', ':update:', ':delete:', ':deleteMany:'),
            array($options['class'], $options['id'], $fieldsHtml, $actionsHtml['update'], $actionsHtml['delete'], $actionsHtml['deleteMany']),
            $galleryOptions['template']
        );         
        $galleryOptions['id'] = $options['id'];
        // - get file field
        $galleryOptions['fileField'] = explode($this->separator, $field);
        $galleryOptions['fileField'] = end($galleryOptions['fileField']);
        // - get name field
        if ($galleryOptions['nameField']) {
            $galleryOptions['nameField'] = explode($this->separator, $galleryOptions['nameField']);
            $galleryOptions['nameField'] = end($galleryOptions['nameField']);
        }
        else {
            $galleryOptions['nameField'] = null;
        }
        // - get id field
        if ($galleryOptions['idField']) {
            $galleryOptions['idField'] = explode($this->separator, $galleryOptions['idField']);
            $galleryOptions['idField'] = end($galleryOptions['idField']);
        }
        $inputHtml .= App::loadView('App', 'Form/gallery', $galleryOptions);
        
        return $this->finalizeInput(
            'gallery', 
            $field, 
            $inputName, 
            $inputHtml, 
            $ridtClasses, 
            $isRequired, 
            $isDisabled, 
            $isTranslated, 
            $options
        );
    }
        
    /**
     * Creates HTML for contentBlocks input
     * 
     * @param array $options Available options:
     *      - 'value' (string) Value of the input. Default value if there is no field value 
     *          in provided data. Ignored for password and file inputs.
     *          Defaults to NULL.
     *      - 'explicitValue' (string) Explicit value of the input. This value is 
     *          used regardless to field value in provided data. Ignored for password and file inputs.
     *          If provided than 'value' option is ignored. Defaults to NULL.
     *      - 'renderValue' (function) Anonymous function which gets $fieldValue, 
     *          $fieldName and $data as input arguments and returns rendered field value.
     *          Defaults to NULL.
     *      - 'class' (string) Html element class. Defaults to FormHelper::$inputDefaults['class'] plus
     *          FormHelper::$inputDefaults['textClass'] or FormHelper::$inputDefaults['toggleClass'].
     *      - 'id' (string) Html element id. Defaults to NULL, means will be genarated automatically.
     *      - 'label' (string|array) Field label. If you need to specify some label options then 
     *          use array of options. In such case the label text itself must be supplied under key 'text'
     *          of options array and if it is not specified then the label will NOT be displayed
     *          even other options are provided. Defaults to NULL (no label generated).
     *      - 'hint' (string|array) Hint text for input. If you need to specify some hint options
     *          then use array of options. In such case hint text itself must be supplied under key 'text' 
     *          and all other array items are passed as options to Html::hint(). 
     *          Defaults to NULL.
     *      - 'requiredMark' (string) Required mark to be used for inputs of required fields. 
     *          Defaults to FormHelper::$inputDefaults['requiredMark'].
     *      - 'translatedMark' (string) Translated mark to be used for inputs of translated fields. 
     *          Defaults to FormHelper::$inputDefaults['translatedMark'].
     *      - 'template' (string) Html template to render created input. Use ':l:', ':i:', 
     *          ':e:', ':h:', ':r:', ':t:', ':ridt:' inserts to place the label, input, 
     *          errors & warnings, hint, requiredMark, translatedMark html and ridt css classes into 
     *          template html, e.g.: '< div class="my-input :ridt:">:l:< div class="input-spacer">:i::e:< /div>< /div>'
     *          If empty value then label is generated like ':l::i::e:'. Defaults to 
     *          FormHelper::$inputDefaults['template'].
     *      - 'attachErrors' (bool|string|array) If TRUE then error messages are attached directly 
     *          to generated input html. If FALSE error messages are omitted. If 'scrollOnly' 
     *          then only scroll to errors js script is generated but errors are not attached.
     *          If you need to specify some errors options then use array of options.
     *          Defaults to FormHelper::$inputDefaults['attachErrors'].
     *      - 'attachWarnings' (bool|string|array) If TRUE then warning messages are attached directly 
     *          to generated input html. If FALSE warning messages are omitted. If you need to s
     *          pecify some warnings options then use array of options. Defaults to 
     *          FormHelper::$inputDefaults['attachWarnings'].
     *      - 'inserts' (array) Custom inserts which are applied on resulting input html.
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value. 
     *          See phpDoc of FormHelper::$compatibility for more info.
     *          Defaults to FormHelper::$compatibility.
     *      - 'ownerModel' (string) Content blocks owner model name qualified 
     *          by module name. e.g. 'App.WebContent'. Mandatory, an exception 
     *          is thrown if not provided.
     *      - 'ownerId' (integer) Content blocks owner id. Mandatory, an exception 
     *          is thrown if not provided.
     *      - 'contentBlockModels' (string|array) Content blocks to create list for.
     *          They are specified by their definition qualified model name. 
     *          Array of many content blocks or string of single content block.
     *          E.g. 'App.HtmlContentBlock' or array('App', 'Eshop.MyContentBlock').
     *          You can use wildcard notations: '*' - to get all content blocks in project,
     *          '*.MyContentBlock' - to get all 'MyContentBlock'-s under any module,
     *          'MyModule.*' - to get all content blocks from 'MyModule'.
     *          Models MUST be qualified by module like {Module}.{Model}. Defaults to '*'.
     *      - 'avoidContentBlockModels' (string|array) Content blocks to be omitted in list. 
     *          The specification posibilities are the same as for 'contentBlockModels'
     *          option. Defaults to NULL.
     *      - 'sortContentBlockModels' (bool) If TRUE then returned models are ordered by name 
     *          (ContentBlock::$blockName). Defaults to TRUE.
     *      - 'deleteButtonConfirmMessage' (string) Defaults to translation of 'Please confirm removal of content block'.
     *      - 'actions' (array) Actions to add and preview and previewAll content blocks.
     *          ATTENTION: Content blocks previews are rendered in iframes using contentBlockPreview layout.
     *          This is usefull to set specific styles (inside body#content-block-preview-layout)
     *          for content blocks. E.g. for blocks with height set by css vh units (in iframe 100vh = 
     *          iframe height = previewed block/page height) the height must be restricted by
     *          max-height in px units. See the method implementation for defaults.
     * 
     * @return string Html code
     */
    public function contentBlocks($options = array()) {
        $defaults = array(
            'value' => null,     
            'explicitValue' => null,      
            'renderValue' => null,
            'class' => null,               
            'id' => null,  
            'label' => null, 
            'hint' => null,
            'requiredMark' => null,
            'translatedMark' => null,
            'template' => null,
            'attachErrors' => null,
            'attachWarnings' => null,
            'inserts' => null,
            'compatibility' => $this->compatibility,
            'ownerModel' => null,
            'ownerId' => null,
            'contentBlockModels' => '*',
            'avoidContentBlockModels' => null,
            'sortContentBlockModels' => true,
            'deleteButtonConfirmMessage' => __(__FILE__, 'Please confirm removal of content block'),
            'actions' => array(
                'add' => '/mvc/App/ContentBlockInstances/admin_loadAdminView',
                'preview' => '/mvc/App/ContentBlockInstances/loadInstanceView',
                'previewAll' => '/mvc/App/ContentBlockInstances/getOwnerInstancesView',
            ),
            
            // Internal use: avoid following to create as attributes
            'type' => null,
            'forceSubmit' => false,
        );
        $options = array_merge($defaults, $this->inputDefaults, $options);
        // turn off this option for this type of input
        $options['forceSubmit'] = false;
        if (empty($options['ownerModel'])) {
            throw new Exception(__e(__FILE__, 'Undefined content blocks owner model'));
        }
        if (!array_key_exists('ownerId', $options)) {
            throw new Exception(__e(__FILE__, 'Missing option ownerId (with content blocks owner record id if any)'));
        }
        
        // input css class
        // - assign ridt class
        // $field name is used only formally (=> ridt would result to FALSE)
        $field = uniqid(); 
        $isRequired = $isInvalid = $isDisabled = $isTranslated = null;
        $ridtClasses = '';
        // - assign input specific classes
        if ($this->inputDefaults['contentBlocksClass']) {
            $options['class'] = rtrim($this->inputDefaults['contentBlocksClass'] . ' ' . $options['class']);
        }
        // input name
        $inputName = $this->createInputName($field);
        // input id
        $options['id'] = $this->assignInputId($inputName, $options['id']);
        // increment number of inputs for the created name
        if(empty($this->inputsCounts[$inputName])) {
            $this->inputsCounts[$inputName] = 1;
        }
        else {
            $this->inputsCounts[$inputName]++;
        }
        // store used input id
        $this->inputIds[$inputName][] = $options['id'];
            
        // normalize actions
        foreach ($options['actions'] as $actionName => &$action  ) {
            if (Validate::intNumber($actionName)) {
                throw new Exception(__e(__FILE__, 'Invalid action definition'));
            }
            // skip empty actions
            if (empty($action)) {
                unset($options['actions'][$actionName]);
                continue;
            }
            if ($action && !is_array($action)) {
                $action = array('url' => $action);
            }
            // accept / refuse action 
            if (!Html::acceptAction($action)) {
                unset($options['actions'][$actionName]);
                continue;
            }
            // normalize url
            $action['url'] = App::getUrl($action['url']);
        }
        unset($action);
        // attach css and js
        if (empty(self::$attachedFiles['/app/css/libs/contentBlocksInput.css'])) {
            App::setCssFiles('/app/css/libs/contentBlocksInput.css');
            self::$attachedFiles['/app/css/libs/contentBlocksInput.css'] = true;
        }
        if (empty(self::$attachedFiles[$this->jQueryJsFile])) {
            App::setJsFiles($this->jQueryJsFile);
            self::$attachedFiles[$this->jQueryJsFile] = true;
        }
        if (empty(self::$attachedFiles[$this->jQueryUiJsFile])) {
            App::setJsFiles($this->jQueryUiJsFile);
            self::$attachedFiles[$this->jQueryUiJsFile] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/Validate.js'])) {
            App::setJsFiles('/app/js/libs/Validate.js');
            self::$attachedFiles['/app/js/libs/Validate.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/WindowStateManager.js'])) {
            App::setJsFiles('/app/js/libs/WindowStateManager.js');
            self::$attachedFiles['/app/js/libs/WindowStateManager.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/Arr.js'])) {
            App::setJsFiles('/app/js/libs/Arr.js');
            self::$attachedFiles['/app/js/libs/Arr.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/SmartFrame.js'])) {
            App::setJsFiles('/app/js/libs/SmartFrame.js');
            self::$attachedFiles['/app/js/libs/SmartFrame.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/App.js'])) {
            App::setJsFiles('/app/js/libs/App.js');
            self::$attachedFiles['/app/js/libs/App.js'] = true;
            // js config for App.js
            App::setJsConfig('App', array(
                'urlRoot' => App::$urlRoot,
                'urlLang' => App::$urlLang,
                'urlBase' => App::$urlBase,
                'homeSlug' => App::$homeSlug,
            ));
        }
        if (empty(self::$attachedFiles['/app/js/libs/Utility.js'])) {
            App::setJsFiles('/app/js/libs/Utility.js');
            self::$attachedFiles['/app/js/libs/Utility.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/PhpJs.js'])) {
            App::setJsFiles('/app/js/libs/PhpJs.js');
            self::$attachedFiles['/app/js/libs/PhpJs.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/String.js'])) {
            App::setJsFiles('/app/js/libs/String.js');
            self::$attachedFiles['/app/js/libs/String.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/Sanitize.js'])) {
            App::setJsFiles('/app/js/libs/Sanitize.js');
            self::$attachedFiles['/app/js/libs/Sanitize.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/Number.js'])) {
            App::setJsFiles('/app/js/libs/Number.js');
            self::$attachedFiles['/app/js/libs/Number.js'] = true;
        }
        if (empty(self::$attachedFiles['/app/js/libs/ContentBlocksInput.js'])) {
            App::setJsFiles('/app/js/libs/ContentBlocksInput.js');
            self::$attachedFiles['/app/js/libs/ContentBlocksInput.js'] = true;
        }
        App::setJsI18nFiles('App');
        App::loadModel('App', 'ContentBlockInstance');
        $Instance = new ContentBlockInstance();
        // if ownerId is empty it means that we are in new owner record administration 
        // and so there are no related content blocks yet
        if (!empty($options['ownerId'])) {
            $options['ownerAdminViews'] = $Instance->loadOwnerInstancesAdminViews(
                $options['ownerModel'],
                $options['ownerId'],
                $this->data
            );
        }
        if (!empty($options['actions']['add'])) {
            $options['contentBlockModels'] = $Instance->getContentBlocksList(array(
                'contentBlockModels' => $options['contentBlockModels'],
                'avoidContentBlockModels' => $options['avoidContentBlockModels'],
                'avoidContentBlockEndModels' => true,
                'sort' => $options['sortContentBlockModels'],
            ));
        }
        $options['id'] = $options['id'];
        $options['class'] = $options['class'];
        $options['Form'] = $this;
        $options['instanceIdField'] = $Instance->getForeignKey();
        $options['instanceNameField'] = $Instance->getPropertyNameField();
        $options['instanceActiveField'] = $Instance->getPropertyActiveField();
        $inputHtml = '<input type="hidden" name="';
        $inputHtml .= $this->createInputName($Instance->getPropertyDataKey());
        $inputHtml .= '"/>';
        $inputHtml .= App::loadView('App', 'Form/contentBlocks', $options);
                
        return $this->finalizeInput(
            'contentBlocks', 
            $field, 
            $inputName, 
            $inputHtml, 
            $ridtClasses, 
            $isRequired, 
            $isDisabled, 
            $isTranslated, 
            $options
        );
    }
        
    /**
     * Resolves FormHelper::input() simple options from provided model field schema.
     * 
     * @param array $schema Model schema field definition (means not the entire schema,
     *      only the single field definition, name of field is unknown)
     * 
     * @return array Options array containing 'type' and in case of select
     *      also 'options' item.
     */
    public static function getInputOptionsFromModelFieldSchema($schema) {
        $inputOptions = array();
        switch (strtoupper($schema['type'])) {
            case 'STRING': case 'STR': case 'VARCHAR': case 'CHAR': case 'TINYTEXT':
            case 'TINYINT': case 'INT': case 'INTEGER':
            case 'BIT': case 'SMALLINT': case 'MEDIUMINT': case 'BIGINT':
            case 'REAL': case 'DOUBLE': case 'FLOAT': case 'DECIMAL': case 'NUMERIC':
            case 'DATE': case 'TIME': case 'TIMESTAMP': case 'DATETIME':
            case 'YEAR': 
                $inputOptions['type'] = 'text';
                break;
            case 'BINARY': case 'VARBINARY': case 'TINYBLOB': case 'BLOB': 
            case 'MEDIUMBLOB': case 'LONGBLOB': 
                $inputOptions['type'] = 'file';
                break;
            case 'BOOL': case 'BOOLEAN':
                $inputOptions['type'] = 'checkbox';
                break;
            case 'TEXT': case 'MEDIUMTEXT': case 'LONGTEXT':
                $inputOptions['type'] = 'textarea';
                break;
            case 'ENUM': case 'SET':
                $inputOptions['type'] = 'select';
                $values = Sanitize::value($schema['values'], array());
                $inputOptions['options'] = array_combine($values, $values);
                break;
            default:
                $inputOptions['type'] = 'text';
        }
        return $inputOptions;
    }
}
