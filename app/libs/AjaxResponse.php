<?php
class AjaxResponse {
    
    /**
     * Was the request succesfully accomplished or no
     * 
     * @var bool 
     */
    public $success = true; 
     
    /**
     * Message to comment/announce the result of request
     * 
     * @var string
     */
    public $message = null;
    
    /**
     * Validation errors
     * 
     * @var array 
     */
    public $errors = null;
    
    /**
     * Response data
     * 
     * @var array 
     */
    public $data = null;
    
    /**
     * Total count or records passed in data
     *
     * @var int 
     */
    public $total = null;
            
    public function __construct($options = array()) {
        $defaults = array(
            'success' => $this->success,
            'message' => $this->message,
            'errors' => $this->errors,
            'data' => $this->data,
            'total' => $this->total,
        );
        $options = array_merge($defaults, $options);
        $this->success = $options['success'];
        $this->message = $options['message'];
        $this->errors = $options['errors'];
        $this->data = $options['data'];
        $this->total = $options['total'];
    }

    /**
     * Returns response json
     * 
     * @return string
     */
    public function getJson() {
        return json_encode(array(
            'success'   => (bool)$this->success,
            'message'   => $this->message,
            'errors'    => $this->errors,
            'data'      => $this->data,
            'total'      => $this->total,
        ));
    }
}
