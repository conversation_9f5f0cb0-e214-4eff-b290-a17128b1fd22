<?php
/**
 * Captcha generator class
 * 
 * Generate captcha image and verify with user input after sending form
 * 
 */
class Captcha {
    
    /**
     * Generates a captcha image and sends it to output (without using echo!) 
     * with proper headers according given image type.
     * 
     * In the same time it stores the captcha code in session. Use Captcha::getCode()
     * to get this code.
     * 
     * Use it like:
     * Create a php script generating the captcha. (e.g. see app/screens/_sc.php)
     * In your form place captcha image with src attribute set to "/path/to/_sc.php"
     * (e.g. "/_sc" - which will use app/screens/_sc.php)
     * 
     * @param array $options Following are available:
     *      - 'name' (string) Name of generated captcha to be used when retrieving the code from session.
     *          Defaults to 'default'.
     *      - 'length' (int) Captcha word length. Defaults to 5.
     *      - 'bgColor' (string) Background color. Defaults to '#FFFFFF'.
     *      - 'color' (string) Text color. Defauts to '#000000'.
     *      - 'font' (string) Absolute path to .ttf file. Defaults to ROOT . 'fonts/acidic.ttf'.
     *      - 'sizeRatio' (float) Ratio by which are multiplied all following options 
     *          which are given in pixels. Defaults to  1.0.
     *      - 'height' (int) Captcha image height given in pixels. Defaults to 45.
     *      - 'width'  (int) Captcha image width given in pixels. Defaults to 175.
     *      - 'fontSize' (int) Captcha image font size given in pixels. Defaults to 24.
     *      - 'xStart' (int) Start position (in x axes direction) of text in 
     *          captcha image given in pixels. Defaults to 8.
     *      - 'minDistance' (int) Minimal random distance between two characters in captcha 
     *          image given in pixels. Defaults to 30.
     *      - 'maxDistance' (int) Maximal random distance between two characters in captcha 
     *          image given in pixels. Defaults to 33.
     *      - 'minAngle' (int) Minimal random angle of a letter in captcha image
     *          given in degrees. Defaults to -20 (degrees).
     *      - 'maxAngle' (int) Minimal random angle of a letter in captcha image 
     *          given in degrees. Defaults to 20 (degrees).
     *      - 'transparency' (int) Text transparency in captcha image given in percents.
     *          Defaults to 15.
     *      - 'charset' (string) List of characters to be used in captcha word.
     *          Defaults to 'abcdeghijkmnprstuvxyz23456789'.
     *      - 'addBackgroundText' (bool) If TRUE then background text is added to 
     *          make it more difficult to read captcha for spam bots. Defaults to TRUE.
     *      - 'backgroundTextTransparency' (int) Background Text transparency in
     *          captcha image given in percents. Defaults to 80.
     *      - 'backgroundTextCharset' (string) List of characters to be used in 
     *          background text. Defaults to '#'.
     *      - 'randomTransparency' (bool) If TRUE then 'transparency' and 'backgroundTextTransparency'
     *          values are randomly changed for each generated character by +-15. Defaults to TRUE.
     *      - 'imageType' (string) Type of generated captcha image. One of
     *           'gif', 'jpg', 'png'. Defaults to 'gif'.
     * 
     * @return void
     */
    static public function createCaptcha($options = array()){
        $defaults = array(
            'name' => 'default',
            'length' => 5,
            'bgColor' => '#FFFFFF',
            'color' => '#000000',
            'font' => ROOT . App::getModulePath('App') . DS . 'fonts' . DS . 'libs' . DS . 'acidic.ttf',
            'sizeRatio' => 1.0,
            'height' => 45, //px
            'width' => 175, //px
            'fontSize' => 24, //px
            'xStart' => 8, //px
            'minDistance' => 30, //px
            'maxDistance' => 33, //px
            'minAngle' => -20, //deg
            'maxAngle' => 20, //deg
            'transparency' => 15, //%
            //'charset' => 'ABCDEFGHJKLMNPRSTUVXYZ23456789',
            'charset' => 'abcdeghijkmnprstuvxyz23456789',
            'addBackgroundText' => true,
            'backgroundTextTransparency' => 80,
            //'backgroundTextCharset' => 'abcdeghijkmnprstuvxyz23456789',
            'backgroundTextCharset' => '#',
            'randomTransparency' => true,
            'imageType' => 'gif', // 'gif', 'jpg', 'png'
        );
        $options = array_merge($defaults, $options);
        
        // apply size ratio
        $options['sizeRatio'] = (float)$options['sizeRatio'];
        $options['height'] = (int)($options['height'] * $options['sizeRatio']); //px
        $options['width'] = (int)($options['width'] * $options['sizeRatio']); //px
        $options['fontSize'] = (int)($options['fontSize'] * $options['sizeRatio']); //px
        $options['xStart'] = (int)($options['xStart'] * $options['sizeRatio']); //px
        $options['minDistance'] = (int)($options['minDistance'] * $options['sizeRatio']); //px
        $options['maxDistance'] = (int)($options['maxDistance'] * $options['sizeRatio']); //px
        
        // create code and background text
        $code = '';
        $backgroundText= '';
        $charsetLength =  strlen($options['charset']);
        $backgroundTextCharsetLength =  strlen($options['backgroundTextCharset']);
        for($i = 0; $i < $options['length']; $i++){
            $code .= $options['charset'][rand(0, $charsetLength - 1)];
            // make the background text 3 times longer
            $backgroundText .= $options['backgroundTextCharset'][rand(0, $backgroundTextCharsetLength - 1)]
                . $options['backgroundTextCharset'][rand(0, $backgroundTextCharsetLength - 1)]
                . $options['backgroundTextCharset'][rand(0, $backgroundTextCharsetLength - 1)];
        }
        
        // create image from the code
        $imageResource = imagecreatetruecolor($options['width'], $options['height']);
        $backgroundColor = imagecolorallocate(
        $imageResource, 
            hexdec(substr($options['bgColor'], 1, 2)), 
            hexdec(substr($options['bgColor'], 3, 2)), 
            hexdec(substr($options['bgColor'], 5, 2))
        );
        imagefilledrectangle($imageResource, 0, 0, imagesx($imageResource), imagesy($imageResource), $backgroundColor);
        
        $textMinY = ($options['height'] / 2) + ($options['fontSize'] / 2) - 2;
        $textMaxY = ($options['height'] / 2) + ($options['fontSize'] / 2) + 2;

        if ($options['addBackgroundText']) {
            $transparencyMin = $options['backgroundTextTransparency'] - 15; // -15%
            $transparencyMin = $transparencyMin < 0 ? 0 : $transparencyMin;
            $transparencyMax = $options['backgroundTextTransparency'] + 15; // +15%
            $transparencyMax = $transparencyMax > 100 ? 100 : $transparencyMax;
            $alphaMin = (int)($transparencyMin / 100 * 127);
            $alphaMax = (int)($transparencyMax / 100 * 127);
            $alpha = (int)($options['backgroundTextTransparency'] / 100 * 127);
            
            $textX = $options['xStart'];
            for($i = 0; $i < strlen($backgroundText); ++$i){
                $textColor = imagecolorallocatealpha(
                    $imageResource, 
                    hexdec(substr($options['color'], 1, 2)), 
                    hexdec(substr($options['color'], 3, 2)), 
                    hexdec(substr($options['color'], 5, 2)), 
                    $options['randomTransparency'] ? rand($alphaMin, $alphaMax) : $alpha
                );
                $angle = rand($options['minAngle'], $options['maxAngle']);
                $textY = rand($textMinY, $textMaxY);
                imagettftext(
                    $imageResource, 
                    $options['fontSize'], 
                    $angle, 
                    $textX, 
                    $textY, 
                    $textColor,
                    $options['font'],
                    $backgroundText[$i]
                );
                $textX += round(
                    rand($options['minDistance'], $options['maxDistance']) / 3, // background text is 3 times longer (see  here above)
                    0, 
                    PHP_ROUND_HALF_EVEN
                );
            }
        }
        
        $transparencyMin = $options['transparency'] - 15; // -15%
        $transparencyMin = $transparencyMin < 0 ? 0 : $transparencyMin;
        $transparencyMax = $options['transparency'] + 15; // +15%
        $transparencyMax = $transparencyMax > 100 ? 100 : $transparencyMax;
        $alphaMin = (int)($transparencyMin / 100 * 127);
        $alphaMax = (int)($transparencyMax / 100 * 127);
        $alpha = (int)($options['transparency'] / 100 * 127);
        
        $textX = $options['xStart'];
        for($i = 0; $i < strlen($code); ++$i){
            $textColor = imagecolorallocatealpha(
                $imageResource, 
                hexdec(substr($options['color'], 1, 2)), 
                hexdec(substr($options['color'], 3, 2)), 
                hexdec(substr($options['color'], 5, 2)), 
                $options['randomTransparency'] ? rand($alphaMin, $alphaMax) : $alpha
            );
            $angle = rand($options['minAngle'], $options['maxAngle']);
            $textY = rand($textMinY, $textMaxY);
            imagettftext(
                $imageResource, 
                $options['fontSize'], 
                $angle, 
                $textX, 
                $textY, 
                $textColor,
                $options['font'],
                $code[$i]
            );
            $textX += rand($options['minDistance'], $options['maxDistance']);
        }

        // send the created image to output
        header("Expires: Sun, 1 Jan 2000 12:00:00 GMT");
        header("Last-Modified: " . gmdate("D, d M Y H:i:s") . "GMT");
        header("Cache-Control: no-store, no-cache, must-revalidate");
        header("Cache-Control: post-check=0, pre-check=0", false);
        header("Pragma: no-cache");
        switch(strtolower($options['imageType'])){
            case 'jpg':
                header("Content-Type: image/jpeg");
                imagejpeg($imageResource, null, 90);
                break;
            case 'png':
                header("Content-Type: image/png");
                imagepng($imageResource);
                break;
            case 'gif':
            default:
                header("Content-Type: image/gif");
                imagegif($imageResource);
                break;
        }
        imagedestroy($imageResource); 
        
        // store the created code to session
        $_SESSION['_captcha'][$options['name']] = $code;
    }
    
    /**
     * Gets last generated captcha code for given captcha name 
     * 
     * @param string $name Captcha name. Defaults to 'default'.
     * 
     * @return string Captcha code 
     */
    static public function getCode($name = 'default') {
        return Sanitize::value($_SESSION['_captcha'][$name]);
    }  
}

