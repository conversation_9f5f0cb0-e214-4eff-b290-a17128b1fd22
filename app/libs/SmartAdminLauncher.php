<?php
/**
 * Class enabling an easy implementation of interactive launching of administration
 * from site frontend. Common usage pattern is (mostly in any kind of frontend index file,
 * e.g. we would like to launch eshop products admin_edit from frontend products index):
 * 
 *      App::loadLib('App', 'SmartAdminLauncher');
 *      $Launcher = new SmartAdminLauncher('/mvc/Eshop/EshopProducts/admin_edit', array(
 *          'triggerTitle' => __a(__FILE__, 'Edit product'),
 *      ));
 *      foreach ($products as $products) {
 *          < div class="product"<?php echo $Launcher->markRecord($product['id']) ?>>
 *          ...
 *          < /div>
 *      }
 */
class SmartAdminLauncher {
    
    static private $instancesCounter = 0;
    
    protected $url = null;
    
    protected $if = true;
    
    protected $launchWholeAdmin = true;
    
    protected $adminUrl = '/run';
    
    protected $triggerTitle = null;
    
    protected $hasRights = false;
    
    protected $cssAndJsSet = false;
    
    protected $attributeName = null;
    
    protected $callableIf = false;
    
    /**
     * 
     * @param string $url MVC url path to be launched administration. The 'lang' 
     *          GET param is added to $url because of field translations. In the
     *          case that model in translated by records (and not by field) then 
     *          the 'lang' GET param is not considered by launched edit action.
     * @param array $options Following are available:
     *      - 'urlRightsChecker' (callable) Method to check the rights of actual 
     *          user to provided $url. Method gets provided $url as input and must 
     *          return boolean value. Defaults to array('App', 'getUserUrlRights').
     *      - 'if' (bool|callable) Should the launcher be active? Explicit possibility
     *          activate/deactivate launcher by setting TRUE/FALSE. If provided as 
     *          callable then this is evaluated for each record. Specified method gets 
     *          record id or whole record as input (see SmartAdminLauncher::markRecord())
     *          amd must return boolean value. BUT whatever the 'if' value is if
     *          the user has no rights for $urls the launcher stays inactive. 
     *          Defaults to TRUE.
     *      - 'launchWholeAdmin' (bool) Should the launcher launch whole admin with
     *          provided $url opened in tab or only provided $url? Defaults to FALSE.
     *      - 'adminUrl' (string) URL to launch whole admin if 'launchWholeAdmin' is
     *          set to TRUE. Defaults to '/run'.
     *      - 'triggerTitle' (string) Html title to be displayed hovering trigger
     *          button/icon. Defaults to NULL.
     */
    public function __construct($url, $options = array()) {
        $defaults = array(
            'urlRightsChecker' => array('App', 'getUserUrlRights'),
            'if' => $this->if,
            'launchWholeAdmin' => $this->launchWholeAdmin,
            'adminUrl' => $this->adminUrl,
            'triggerTitle' => $this->triggerTitle,
        );
        $options = array_merge($defaults, $options);
        
        self::$instancesCounter++;
        
        if (
            isset($options['urlRightsChecker'])
            && Validate::callableFunction($options['urlRightsChecker'])
            && ($this->hasRights = call_user_func_array($options['urlRightsChecker'], array($url)))
        ) {
            $this->url = $url;
            // add 'lang' GET param to each administration URL
            $this->url = App::rebuildUrl($this->url, array(
                'lang' => null,
                'get' => array('lang' => App::$lang)
            ));
            $this->if = $options['if'];
            $this->callableIf = Validate::callableFunction($options['if']);
            $this->launchWholeAdmin = $options['launchWholeAdmin'];
            $this->adminUrl = $options['adminUrl'];
            $this->triggerTitle = $options['triggerTitle'];
            $this->attributeName = 'data-sal-' . self::$instancesCounter;
        }
    }
    
    /**
     * Returns HTML attribute by which launcher recognizes HMTL blocks which can
     * launch actual SmartAdminLauncher::$url.
     * 
     * Common usage pattern is (mostly in any kind of frontend index file, e.g. 
     * we would like to launch eshop products admin_edit from frontend products index):
     * 
     *      App::loadLib('App', 'SmartAdminLauncher');
     *      $Launcher = new SmartAdminLauncher('/mvc/Eshop/EshopProducts/admin_edit');
     *      foreach ($products as $products) {
     *          < div class="product"<?php echo $Launcher->markRecord($product['id']) ?>>
     *          ...
     *          < /div>
     *      }
     * 
     * @param interger $id Optional. If provided the it is appended to SmartAdminLauncher::$url
     *      as argument 
     * @param interger $record Optional. Record array. This argument is used only if
     *      SmartAdminLauncher::__construct() 'if' option is set to callable (see 
     *      constructor phpDoc). If provided then it is passed on callable input.
     *      If not provided the $id is passed on callable input.
     * 
     * @return string Launcher attribute (with leading whitespace) or empty string
     *      if user has no rights for SmartAdminLauncher::$url
     */
    public function markRecord($id = null, $record = null) {
        // be inactive if no rights
        if (
            empty($this->hasRights)
            || empty($this->if)
        ) {
            return '';
        }
        // be inactive if callable 'if' returns false
        if ($this->callableIf) {
            if (!empty($record)) {
                $args = array($record);
            }
            else {
                $args = array($id);
            }
            if (!call_user_func_array($this->if, $args)) {
                return '';
            }
        }
        if (empty($this->cssAndJsSet)) {
            $this->setCssAndJs();
        }
        // get record html attribute
        return ' ' . $this->attributeName . '="' . $id . '"';
    }
    
    /**
     * Sets css and js code and files required on browser side
     */
    protected function setCssAndJs() {
        if (empty($this->cssAndJsSet)) {
            $jsOptions = array(
                'launchWholeAdmin' => $this->launchWholeAdmin,
                'adminUrl' => $this->adminUrl,
                'triggerTitle' => $this->triggerTitle,
            );
            App::setJs(
                'jQuery(function(){'
                    . 'new Run.App.SmartAdminLauncher('
                        . '\'' . $this->url . '\', '
                        . '\'' . $this->attributeName . '\', '
                        . json_encode($jsOptions)
                    . ');'
                . '});'
            );
            App::setJsFiles(array(
                '/app/js/vendors/jquery.min.js',
                '/app/js/vendors/jquery.mousewheel-3.0.6.pack.js',
                '/app/js/vendors/fancybox/jquery.fancybox.pack.js',
                '/app/js/libs/Validate.js',
                '/app/js/libs/Arr.js',
                '/app/js/libs/WindowStateManager.js',
                '/app/js/libs/SmartAdminLauncher.js',
            )); 
            App::setCssFiles(array(
                '/app/css/vendors/font-awesome/css/font-awesome.min.css',
                '/app/js/vendors/fancybox/jquery.fancybox.css',
                '/app/css/libs/smartAdminLauncher.css',
            )); 
            
            $this->cssAndJsSet = true;
        }
    }
}