<?php

class XmlRecordsReader {
    
    /**
     * Absolute path to XML file
     * 
     * @var string 
     */
    protected $file = null;
    
    /**
     * Path under which records (record tags) are placed. 
     * It is composed from XML tag names separated by slashes, e.g. 'MY/PATH/TO/RECORDS'.
     * If records can be clearly found without providing path (e.g. record tag name
     * is unique in Xml file), then it is not necessary to provide it.
     * 
     * @var string 
     */
    protected $recordsPath = null;
    
    /**
     * Tag name used for records
     * 
     * @var string 
     */
    protected $recordTagName = 'ITEM';
    
    /**
     * If TRUE then it is not required that recordTagName is present in XML file. 
     * If FALSE then recordTagName must be present in XML file.
     *
     * @var bool 
     */
    protected $allowNoRecords = false;
    
    /**
     * Decimal point used in float numbers. Defaults to '.'.
     * 
     * @var string 
     */
    protected $decimalPoint = '.';
    
    /**
     * Thousand separator used in float numbers. Defaults to ','.
     * 
     * @var string 
     */
    protected $thousandsSeparator = ',';

    /**
     * Instance of XML reader class used to process the XML file
     * 
     * @var XMLReader 
     */
    protected $Xml = null;
    
    /**
     * Actual record offset in XML file. Defaults to 0.
     * 
     * @var int 
     */
    protected $recordOffset = 0;
    
    /**
     * Count of XML records in file.
     * 
     * @var int 
     */
    protected $recordsCount = null;
    
    /**
     * Opens XML file
     * 
     * @param string $file App root relative path to XML file
     * @param array $options Following are available:
     *      - 'recordsPath' (string) Path under which records (record tags) are 
     *          placed. It is composed from XML tag names separated by slashes, 
     *          e.g. 'MY/PATH/TO/RECORDS'. If records can be clearly found without 
     *          providing path (e.g. record tag name is unique in Xml file), then 
     *          it is not necessary to provide it. Defaults XmlRecordsReader::$recordsPath (NULL).
     *      - 'recordTagName' (string) Tag name used for records. Defaults XmlRecordsReader::$recordTagName ('ITEM').
     *      - 'allowNoRecords' (bool) If TRUE then it is not required that recordTagName
     *          is present in XML file. If FALSE then recordTagName must be present in
     *          XML file otherwise Exception_XmlRecordsReader_NoRecordTag is thrown.
     *          Defaults to XmlRecordsReader::$allowNoRecords (FALSE).
     *      - 'decimalPoint' (string) Decimal point used in float numbers. Defaults tXmlRecordsReader::$decimalPoint ('.').
     *      - 'thousandsSeparator' (string) Thousand separator used in float numbers. 
     *          Defaults to NULL mlRecordsReader::$thousandsSeparator (',').
     *      - 'recordOffset' (int) Record offset to initialize the csv file to. 
     *          Defaults to 0.
     * 
     * @throws Exception on invalid XML file
     */
    public function __construct($file, $options = array()) {
        $defaults = array(
            'recordsPath' => $this->recordsPath,
            'recordTagName' => $this->recordTagName,
            'allowNoRecords' => $this->allowNoRecords,
            'decimalPoint' => $this->decimalPoint,
            'thousandsSeparator' => $this->thousandsSeparator,
        );
        $options = array_merge($defaults, $options);
        $this->recordsPath = $options['recordsPath'];
        $this->recordTagName = $options['recordTagName'];
        $this->allowNoRecords = $options['allowNoRecords'];
        $this->decimalPoint = $options['decimalPoint'];
        $this->thousandsSeparator = $options['thousandsSeparator'];
        $this->file = ROOT . DS . ltrim($file, DS);
        $this->open($options);
    }
    
    /**
     * Opens XML file
     * 
     * @param array $options Following are available:
     *      - 'recordOffset' (int) Record offset to initialize the csv file to. 
     *          Defaults to 0.
     * 
     * @throws Exception on XML file open error
     * @throws Exception_XmlRecordsReader_NoRecordTag
     * @throws Exception_XmlRecordsReader_InvalidXmlStructure
     */
    public function open($options = array()) {
        $defaults = array(
            'recordOffset' => 0,
        );
        $options = array_merge($defaults, $options);
                
        if ($this->Xml) {
            throw new Exception(__e(__FILE__, 'The XML %s file is already opened.', $this->file));
        }
                                
        // validate file
        if (!is_file($this->file) || !is_readable($this->file)) {
            throw new Exception(__e(__FILE__, 'File %s does not exist or is not readable', $this->file));
        }
        
        // open the XML file
        $this->Xml = new XMLReader();
        $this->Xml->open($this->file);
        
        if (!empty($this->recordsPath)) {
            $this->recordsPath = trim($this->recordsPath, '/');
            $pathTagNames = Str::explode('/', $this->recordsPath);
            foreach($pathTagNames as $tagName) {
                while ($this->Xml->read() && $this->Xml->name !== $tagName);
            }
            if ($this->Xml->name !== $tagName) {
                throw new Exception_XmlRecordsReader_InvalidRecordsPath(__e(
                    __FILE__, 
                    'Records path "%s" was not found in XML file', 
                    $this->recordsPath
                ));
            }
        }
        
        // move to the first record node
        while ($this->Xml->read() && $this->Xml->name !== $this->recordTagName);
        
        // throw exception on invalid XML file
        // - if there was no record tag found in provided XML file and it is not allowed
        if ($this->Xml->name !== $this->recordTagName) {
            if ($this->allowNoRecords) {
                // if no records are allowed then skipt the rest of processing
                return;
            }
            elseif (empty($this->recordsPath)) {
                throw new Exception_XmlRecordsReader_NoRecordTag(__e(__FILE__, 'Record tag "%s" was not found in XML file', $this->recordTagName));
            }
            else {
                throw new Exception_XmlRecordsReader_NoRecordTag(__e(
                    __FILE__, 
                    'Record tag "%s" was not found in XML file under path %s', 
                    $this->recordTagName, 
                    $this->recordsPath
                ));
            }
        }
        // - or if the xml record is not valid
        if (
            !$this->validateRecord(new SimpleXMLElement($this->Xml->readOuterXML()))
        ) {
            throw new Exception_XmlRecordsReader_InvalidXmlStructure(__e(__FILE__, 'Invalid XML structure'));
        }
        
        // set the specified recordOffset 
        if ($options['recordOffset'] > 0) {
            $this->setRecordOffset($options['recordOffset']);
        }
    }
    
    /**
     * Validates the first XML record in provided file. This validation is made on 
     * XML file open. 
     * 
     * ATTENTION: This method should be overriden in each child class of XmlRecordsReader 
     * class. In its basic implementation it always returns TRUE.
     *  
     * @param SimpleXMLElement $XmlRecord XML record to be validated
     * 
     * @return boolean
     */
    public function validateRecord($XmlRecord) {
        return true;
    }
    
    /**
     * Normalizes provided number string to php valid form, e.g. '1,000.23' to '1000.23'
     * or '45,78' to '45.78'. Normalization is made according to values of 
     * XmlRecordsReader::$thousandsSeparator and ::$decimalPoint prorepties.
     * 
     * @param string $number Number string to be normalized.
     * 
     * @return string Normalized number string
     */
    public function normalizeNumber($number) {
        $number = trim($number);
        if ($this->thousandsSeparator !== '') {
            $number = str_replace($this->thousandsSeparator, '', $number);
        }
        if ($this->decimalPoint !== '.') {
            $number = str_replace($this->decimalPoint, '.', $number);
        }
        // allways perform this fallback
        $number = str_replace(' ', '', $number);
        $number = str_replace(',', '.', $number);
        return $number;
    }
    
    /**
     * Reads next record and returns it.
     * 
     * @return null|SimpleXMLElement NULL if there is no next record. Record object 
     *      parsed by SimpleXMLElement class. The properties of returned record object 
     *      depends on XML record structure.
     */
    public function readRecord() {
        // if there is no more record node then return NULL 
        if ($this->Xml->name !== $this->recordTagName) {
            return null;
        }
        // parse the content of record node
        $XmlRecord = new SimpleXMLElement($this->Xml->readOuterXML());
        
        // prepare the next read request and go to the next record node
        $this->Xml->next($this->recordTagName);
        
        // update record offset
        $this->recordOffset++;
        
        return $XmlRecord;
    }
    
    /**
     * Gets offset of record which will be read as next
     * 
     * @return int
     */
    public function getRecordOffset() {
        return $this->recordOffset;
    }
    
    public function setRecordOffset($offset) {
        if (!$this->Xml) {
            throw new Exception("XML file {$this->file} has not been opened yet");
        }
        
        if ($this->recordOffset == $offset) {
            return true;
        }
////@todo - only offsets higher than actual offset can be set for now        
        if ($this->recordOffset > $offset) {
            throw new Exception(__e(__FILE__, 'Only offsets higher than actual offset can be set for now'));
            //rbla//$this->reset();
        }
        $offsetDiff = $offset - $this->recordOffset;
        // go to required record offset
        while (
            $this->Xml->name === $this->recordTagName 
            && $offsetDiff > 0
        ) {
            $offsetDiff--;
            $this->recordOffset++;
            $this->Xml->next($this->recordTagName);
        }
        // check if offset has been reached (maybe there is no such offset)
        if ($this->recordOffset != $offset) {
            return false;
        }
        return true;
    }
    
    public function getRecordsCount() {
        if ($this->recordsCount === null) {
            $recordsCount = $recordOffset = $this->recordOffset;
            while ($this->Xml->name === $this->recordTagName) {
                $recordsCount++;
                $this->Xml->next($this->recordTagName);
            }
            $this->recordsCount = $recordsCount;
            $this->close();
            $this->open(array('recordOffset' => $recordOffset));
        }
        return $this->recordsCount;
    }
        
    /**
     * Closes the opened XML file
     */
    public function close() {
        if (!$this->Xml) {
            return;
        }
        $this->Xml->close();
        $this->Xml = null;
    }
    
    public function __destruct() {
        $this->close();
    }
    
}
class Exception_XmlRecordsReader_InvalidRecordsPath extends Exception {}
class Exception_XmlRecordsReader_NoRecordTag extends Exception {}
class Exception_XmlRecordsReader_InvalidXmlStructure extends Exception {}
