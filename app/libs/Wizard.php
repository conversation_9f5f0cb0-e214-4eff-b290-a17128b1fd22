<?php

class Wizard {
    
    /**
     * Wizard name. This is used to identify wizard data in session/cookie.
     * Proposition: use combination '{ClassName}_{methodName}' to create an unique wizard name.
     * 
     * @var string 
     */
    protected $name = null;
    
    /**
     * Definition of wizard steps in order as they should occure.
     * The steps are defined in array containing pairs '{slug}' => '{step}'.
     * 
     * @var array
     */
    protected $steps = array();
    
    /**
     * Slug of actual wizard step
     *
     * @var string
     */
    protected $slug = null;
    
    /**
     * Actual wizard step
     * 
     * @var string 
     */
    protected $step = null;
    
    /**
     * Wizard data storage type.
     * Possible values are 'session' and 'cookie' (@todo)
     *
     * @var string 
     */
    protected $storage = 'session';
    
    /**
     * Cookie storage expire time
     *
     * @var int 
     */
    protected $expire = 0;
    
    /**
     * 
     * @param string $name Wizard name. This is used to identify wizard data in session/cookie.
     *      Proposition: use combination '{ClassName}_{methodName}' to create an unique wizard name.
     * @param array $steps Wizard steps definition. It is an array containing pairs '{slug}' => '{step}'.
     * @param string $slug Optional. Actual wizard step slug. If not provided or an nonexisting slug 
     *      provided then the wizard is set to the first slug
     * @param array $options Following options can be used:
     *      - 'storage' (string) One of 'session' or 'cookie'. Defaults to 'session'.
     *      - 'expire' (int) Cookie expire time. Applies only for 'cookie' storage.
     *          Defaults to 0 (ends with session on borwser close).
     */
    public function __construct($name, $steps, $slug = null, $options = array()) {
        $defaults = array(
            'storage' => 'session',
            'expire' => 0,
        );
        $options = array_merge($defaults, $options);
        // set name
        if (!is_string($name)) {
            throw new Exception("Wizard name must be a string");
        }
        $this->name = $name;
        // set steps
        if (!is_array($steps)) {
            throw new Exception("Wizard steps must be provided in form of an array");
        }
        if (empty($steps)) {
            throw new Exception("Wizard steps must not be empty array");
        }
        foreach($steps as $v) {
            if (!is_string($v)) {
                throw new Exception("Wizard step must be a string");
            }
        }
        $this->steps = $steps;
        // set slug
        if (!isset($this->steps[$slug])) {
            $slug = array_keys($this->steps);
            $slug = reset($slug);
        }
        $this->slug = $slug;
        // set step
        $this->step = $this->steps[$slug];
        // set storage
        $options['storage'] = strtolower($options['storage']);
        if ($options['storage'] !== 'session' && $options['storage'] !== 'cookie') {
            throw new Exception("Invalid wizard storage type");
        }
        $this->storage = $options['storage'];
        // set expire
        $options['expire'] = strtolower($options['expire']);
        if (!Validate::intNumber($options['expire'])) {
            throw new Exception("Wizard storage expire must be an integer");
        }
        $this->expire = $options['expire'];
        // keep track of absolved steps by initializing the step in storage
        // but do it only in case that it is not initialized yet and this step is in 
        // called in valid progress sequence (all previous steps are absolved)
        if (
            !isset($_SESSION['_wizard'][$this->name][$this->step])
            && $this->validateProgress()
        ) {
            if ($this->storage == 'session') {
                $_SESSION['_wizard'][$this->name][$this->step] = array();
            }
            else {
                // cookie ...
            }
        }
    }
    
    /**
     * Gets list of all wizard steps
     * 
     * @return string
     */
    public function getPropertySteps() {
        return $this->steps;
    }
    
    /**
     * Gets list of all wizard slugs
     * 
     * @return string
     */
    public function getSlugs() {
        return array_flip($this->steps);
    }
    
    /**
     * Gets slug of actual or provided step.
     * 
     * @param string $step Optional. Step to get the slug for. If not provided then 
     *      slug of actual step is returned.
     * 
     * @return string|bool Slug corresponding to give step. If no slug is found then FALSE.
     */
    public function getSlug($step = null) {
        if (!$step) {
            $step = $this->step;
        }
        $slugs = array_flip($this->steps);
        if (isset($slugs[$step])) {
            return $slugs[$step];
        }
        return false;
    }
    
    /**
     * Gets wizard previous slug relative to actual or provided step.
     * 
     * @param string $step Optional. Step to get the previous slug for. If not provided then 
     *      previous slug relative to actual step is returned.
     * 
     * @return string|bool Previous slug. If no previous slug found then FALSE.
     */
    public function getPreviousSlug($step = null) {
        if (!$step) {
            $step = $this->step;
        }
        $prevSlug = false;
        foreach ($this->steps as $slug => $s) {
            if ($step == $s) {
                return $prevSlug;
            }
            $prevSlug = $slug;
        }
        return false;
    }
        
    /**
     * Gets wizard next slug relative to actual or provided step.
     * 
     * @param string $step Optional. Step to get the next slug for. If not provided then 
     *      next slug relative to actual step is returned.
     * 
     * @return string|bool Next slug. If no next slug found then FALSE.
     */
    public function getNextSlug($step = null) {
        if (!$step) {
            $step = $this->step;
        }
        $stepFound = false;
        foreach ($this->steps as $slug => $s) {
            if ($stepFound) {
                return $slug;
            }
            $stepFound = ($step == $s);
        }
        return false;
    }
    
    /**
     * Gets the first slug.
     * 
     * @return string The first slug
     */
    public function getFirstSlug() {
        $slug = array_keys($this->steps);
        return reset($slug);
    }
    
    /**
     * Gets the last slug.
     * 
     * @return string The last slug
     */
    public function getLastSlug() {
        return end(array_keys($this->steps));
    }
    
    /**
     * Gets step of actual or provided slug.
     * 
     * @param string $slug Optional. Slug to get the step for. If not provided then 
     *      step of actual slug is returned.
     * 
     * @return string|bool Step corresponding to give slug. If no step is found then FALSE.
     */
    public function getStep($slug = null) {
        if (!$slug) {
            $slug = $this->slug;
        }
        if (isset($this->steps[$slug])) {
            return $this->steps[$slug];
        }
        return false;
    }
    
    /**
     * Gets wizard previous step relative to actual or provided step.
     * 
     * @param string $step Optional. Step to get the previous step for. If not provided then 
     *      previous step relative to actual step is returned.
     * 
     * @return string|bool Previous step. If no previous step found then FALSE.
     */
    public function getPreviousStep($step = null) {
        if (!$step) {
            $step = $this->step;
        }
        $prevStep = false;
        foreach ($this->steps as $s) {
            if ($step == $s) {
                return $prevStep;
            }
            $prevStep = $s;
        }
        return false;
    }
        
    /**
     * Gets wizard next step relative to actual or provided step.
     * 
     * @param string $step Optional. Step to get the next step for. If not provided then 
     *      next step relative to actual step is returned.
     * 
     * @return string|bool Next step. If no next step found then FALSE.
     */
    public function getNextStep($step = null) {
        if (!$step) {
            $step = $this->step;
        }
        $stepFound = false;
        foreach ($this->steps as $s) {
            if ($stepFound) {
                return $s;
            }
            $stepFound = ($step == $s);
        }
        return false;
    }
    
    /**
     * Gets the first step.
     * 
     * @return string The first step
     */
    public function getFirstStep() {
        return reset($this->steps);
    }
    
    /**
     * Gets the last step.
     * 
     * @return string The last step
     */
    public function getLastStep() {
        return end($this->steps);
    }
    
    /**
     * Sets the wizard parameter value.
     * 
     * @param string $parameter
     * @param mixed $value
     * @param string $step Optional. Step to set parameter value for. If not provided then 
     *      actual step is used.
     * 
     * @return mixed Old value of parameter in given step
     */
    public function setParameter($parameter, $value, $step = null) {
        if (!$step) {
            $step = $this->step;
        }
        $oldValue = $this->getParameter($parameter, $step);
        if ($this->storage == 'session') {
            $_SESSION['_wizard'][$this->name][$step][$parameter] = $value;
        }
        else {
            // cookie ...
        }
        return $oldValue;
    }
    
    /**
     * Gets the wizard parameter value.
     * 
     * @param string $parameter
     * @param string $step Optional. Step to get parameter value for. If not provided then 
     *      actual step is used.
     * 
     * @return array Value of wizard parameter in given step
     */
    public function getParameter($parameter, $step = null) {
        if (!$step) {
            $step = $this->step;
        }
        if ($this->storage == 'session') {
            // avoid the use of App::getProperty because this creates unexisting keys in arrays
            // and then breaks validation of progress
            if (isset($_SESSION['_wizard'][$this->name][$step][$parameter])) {
                return $_SESSION['_wizard'][$this->name][$step][$parameter];
            }
            return null;
        }
        else {
            // cookie...
        }
    }
    
    /**
     * Gets wizard parameter values for all steps
     * 
     * @param string $parameter
     * @param bool $merge Optional. If TRUE then parameter values are merged from all steps.
     *      Otherwise they are kept separately under steps pids. Defaults to FALSE.
     * 
     * @return mixed
     */
    public function getParameterAllSteps($parameter, $merge = false) {
        if ($merge) {
            $value = null;
        }
        else {
            $value = array();
        }
        foreach ($this->steps as $step) {
            $v = $this->getParameter($parameter, $step);
            if ($v === null) {
                continue;
            }
            if ($merge) {
                if (is_array($v)) {
                    $value = array_merge((array)$value, $v);
                }
                else {
                    $value = $v;
                }
            }
            else {
                $value[$step] = $v;
            }
        }
        return $value;
    }
        
   /**
    * Clears all wizard properties
    */
   public function clearProperties() {
        if ($this->storage == 'session') {
            unset($_SESSION['_wizard'][$this->name]);
        }
        else {
            // cookie...
        }
    }   
    
    /**
     * Validates that wizard has absolved all previous steps and that its
     * data are available
     * 
     * @param bool $stepsPresence If TRUE then all previous steps presence is checked.
     *      If FALSE then only wizard data presence is checked. Defaults to TRUE.
     * @param bool $stepsNotEmpty If TRUE then all previous steps presence is checked.
     *      and steps must not be empty (there must be some properties in steps).
     *      If FALSE then only steps presence is checked. This is considered only in case that
     *      $stepsPresence is set TRUE. Defaults to TRUE.
     * 
     * @return boolean On success TRUE. If FALSE the the wizard should be redirected 
     *      to the first step.
     */
    public function validateProgress($stepsPresence = true, $stepsNotEmpty = true) {
        if ($this->storage == 'session') {
            // make nonstrict check based on wizard presence in session
            if (!$stepsPresence) {
                return isset($_SESSION['_wizard'][$this->name]);
            }
            // make strict check of previous steps presence in data
            foreach ($this->steps as $step) {
                // check only previous steps
                if ($step == $this->step) {
                    break;
                }
                if (
                    $stepsNotEmpty && empty($_SESSION['_wizard'][$this->name][$step])
                    || !isset($_SESSION['_wizard'][$this->name][$step])
                ) {
                    return false;
                }
            }
            return true;
        }
        else {
            // cookie ...
        }
    }
    
    
} 
