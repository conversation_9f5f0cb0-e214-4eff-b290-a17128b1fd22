<?php

class Template extends ModuleObject {
    
    /**
     * Type of template script. 
     * One of 'element', 'layout', 'doctype', 'view', 'layout', 'screen', 'dbscript'.
     *  
     * @var string
     */
    protected $type = null;
    
    /**
     * Name of the template script
     *
     * @var string
     */
    protected $template = null;
    
    /**
     * Filename path of the template script.
     * It is app root relative
     * 
     * @var string 
     */
    protected $script = null;
    
    /**
     * Type directory used for displaying of origin comments.
     * E.g. 'views', 'views/elements', ...
     *
     * @var string
     */
    protected $typeDir = null;
    
    /**
     * Template module name
     *
     * @var string
     */
    public $module = null;
        
    /**
     * Template params 
     * 
     * @var array
     */
    public $params = array();
    
    /**
     * Template data
     * 
     * @var array
     */
    public $data = array();
    
    /**
     * Template args 
     * 
     * Note: Used only for screens
     * 
     * @var array
     */
    public $args = array();
    
    /**
     * If TRUE then origin comment of loaded template is displayed. It is intended to 
     * be set directly by the template itself like $this->displayOriginComment = TRUE.
     *
     * @var bool
     */
    public $displayOriginComment = false;
    
    /**
     * Creates new istance on Template class
     * 
     * @param string $module Module name to of template script
     * @param string $type Type of template script to be loaded. One of 'element', 
     *      'layout', 'view', 'screen', 'dbscript'
     * @param string $template Template script name or type folder relative name path.
     *      In case of 'dbscript' type this is the db script pid.
     * @param array $params Optional. Params to be passed to  template script on load(). 
     *          Defaults to empty array().
     * @param array $data Optional. Data to be passed to  template script on load(). 
     *          Defaults to empty array().
     * @param array $args Optional. Args to be passed to  template script on load(). 
     *      Defaults to empty array().
     * 
     * @throws Exception on invalid type
     */
    public function __construct($module, $type, $template, $params = array(), $data = array(), $args = array()) {
        // set $module explicitly (parent constructor is not loaded)
        $this->module = $module;
        // validate type and get typeDir
        switch ($type) {
            case 'view':
                $typeDir = 'views';
                break;
            case 'element':
                $typeDir = 'views' . DS . 'elements';
                break;
            case 'layout':
                $typeDir = 'views' . DS . 'layouts';
                break;
            case 'doctype':
                $typeDir = 'views' . DS . 'doctypes';
                break;
            case 'screen':
                if($module !== 'App') {
                    throw new Exception("Invalid module '{$module}' for screen. Screens are placed only under App module.");
                }
                $typeDir = 'screens';
                break;
            case 'dbscript':
                $typeDir = 'dbscript';
                break;
            default:
                throw new Exception("Invalid template type: '$type'");
                break;
        }
        $this->type = $type;
        $this->typeDir = $typeDir;
        $this->template = ltrim($template, DS);
        $this->params = $params;    
        $this->data = $data;    
        $this->args = (array)$args;   
        if ($this->type !== 'dbscript') {
            $script = File::normalizePath($template, 'php');
            $modulePath = App::getModulePath($module);
            $this->script = $modulePath . DS . $typeDir . DS . $script;
        }
    }
    
    /**
     * Loads the template script and returns its output
     * 
     * @param bool $allowOriginComments Optional. If TRUE then template origin comments are
     *      allowed. To display origin comment set property $this->displayOriginComment to TRUE.
     *      Defaults to TRUE.
     * @param string|array $catchVariables Optional. A single variable name or an array
     *      of such names to be catched after the template is loaded. They
     *      are returned by aux output $catchedVariables. If the specified variable
     *      does not exist (is not created by template) then it is not present
     *      in $catchedVariables. If this option is any empty value then nothing is 
     *      catched. Defaults to NULL.
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return string Html output generated by template script. 
     * @throws Exception on read error of template script
     */
    public function load($allowOriginComments = true, $catchVariables = null, &$catchedVariables = array()) {
        // reset origin comment switch
        $this->displayOriginComment = false;
        // check for invalid capture endings - intitial and final capture state must be 
        // the same. If the template was not loaded inside capture then no capture should be 
        // detected after loading the script
        $_openedCssCapturesCount = App::getPropertyOpenedCssCapturesCount();
        $_openedJsCapturesCount = App::getPropertyOpenedJsCapturesCount();
        $_openedHeadHtmlCapturesCount = App::getPropertyOpenedHeadHtmlCapturesCount();
        $_openedBodyEndHtmlCapturesCount = App::getPropertyOpenedBodyEndHtmlCapturesCount();
        $_openedHtmlCapturesCount = Html::getPropertyOpenedCapturesCount();
        $this->displayOriginComment = false;
        if ($this->type === 'dbscript') {
            $this->script = DB::select('run_scripts', array(
                'conditions' => array('module' => $this->module,'pid' => $this->template),
                'field' => array('source'),
                'first' => true,
            ));
            if (!$this->script) {
                throw new Exception("Invalid DB script '{$this->module}.{$this->template}' under module '{$this->module}'");
            }
            $this->script = $this->script['source'];
            ob_start();
            try {
                eval($this->script);
            }
            catch (Throwable $e) {
                App::catchThrowable($e);
            }
        }
        else {
            // check if script exists
            if (!is_readable(ROOT . $this->script)) {
                throw new Exception("Invalid {$this->type} script '{$this->script}' under module '{$this->module}'");
            }
            // catch the template script output
            ob_start();
            try {
                require ROOT . $this->script;
            }
            catch (Throwable $e) {
                App::catchThrowable($e);
            }
        }
        $output = ob_get_contents();
        ob_end_clean();
        if ($allowOriginComments && !empty($this->displayOriginComment) && !empty($output)) {
            $output = 
                '<!--  ' . $this->module . DS . $this->typeDir . DS . $this->template . ' -->' 
                . $output
                . '<!--/ ' . $this->module . DS . $this->typeDir . DS . $this->template . ' -->';
        }
        if (!empty($catchVariables)) {
            $catchedVariables = compact((array)$catchVariables);
        }
        // check for invalid capture endings (see the comment above)
        if ($_openedCssCapturesCount < App::getPropertyOpenedCssCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Missing end of css capture block in %s', $this->module . DS . $this->type . DS . $this->template));
        }
        if ($_openedCssCapturesCount > App::getPropertyOpenedCssCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Redundant end of css capture block in %s', $this->module . DS . $this->type . DS . $this->template));
        }
        if ($_openedJsCapturesCount < App::getPropertyOpenedJsCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Missing end of js capture block in %s', $this->module . DS . $this->type . DS . $this->template));
        }
        if ($_openedJsCapturesCount > App::getPropertyOpenedJsCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Redundant end of js capture block in %s', $this->module . DS . $this->type . DS . $this->template));
        }
        if ($_openedHeadHtmlCapturesCount < App::getPropertyOpenedHeadHtmlCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Missing end of html head capture block in %s', $this->module . DS . $this->type . DS . $this->template));
        }
        if ($_openedHeadHtmlCapturesCount > App::getPropertyOpenedHeadHtmlCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Redundant end of html head capture block in %s', $this->module . DS . $this->type . DS . $this->template));
        }
        if ($_openedBodyEndHtmlCapturesCount < App::getPropertyOpenedBodyEndHtmlCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Missing end of html body end capture block in %s', $this->module . DS . $this->type . DS . $this->template));
        }
        if ($_openedBodyEndHtmlCapturesCount > App::getPropertyOpenedBodyEndHtmlCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Redundant end of html body end capture block in %s', $this->module . DS . $this->type . DS . $this->template));
        }
        if ($_openedHtmlCapturesCount < Html::getPropertyOpenedCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Missing end of html capture block in %s', $this->module . DS . $this->type . DS . $this->template));
        }
        if ($_openedHtmlCapturesCount > Html::getPropertyOpenedCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Redundant end of html capture block in %s', $this->module . DS . $this->type . DS . $this->template));
        }
        return $output;
    }
    
    //
    // Make following methods (inherited from Module) public in Template class
    // to make them visible in view scripts
    //
    
    /**
     * Gets config values from current module config.php file, e.g.:
     *      - App::getConfig(); // will return whole current module $config array
     *      - App::getConfig('fbApiId'); // will return value of current module $config['facebookUrl']
     *      - App::getConfig('google.analytics'); // will return value of current module $config['google']['analytics']
     * 
     * @param string $name Optional. Name of config item. If omitted (NULL) then 
     *      whole config array is returned. Defaults to NULL.
     * @param string $file Explicit name of config source file. The given path 
     *      must be relative to ROOT. If omitted (NULL) then the implicit config 
     *      file of given module is used. This has effect only on 
     *      the first call of this method for given $module, as once the config 
     *      file is read its content is cached under App::$configs[$module]. 
     *      Defaults to NULL.
     * 
     * @return mixed 
     */
    public function getConfig($name = null, $file = null) {
        return parent::getConfig($name, $file);
    }
        
    /**
     * Gets a value of specified current module setting
     * 
     * @param string $pid 
     * @param array $options Following are available
     *      - 'translate' (bool) If TRUE then setting value is retrieved for provided lang.
     *          If FALSE then translation is turned off, means default lang values are used. 
     *          Defaults to TRUE.
     *      - 'lang' (string) Lang specification for value translation. 
     *          Defaults to actual app lang.
     * 
     * @return string Setting value
     */
    public function getSetting($pid, $options = array()) {
        return parent::getSetting($pid, $options);
    }    
        
    /**
     * Gets global value of current module
     * 
     * @param string $name
     * @return mixed 
     */
    public function getGlobal($name = null) {  
        return parent::getGlobal($name);
    }
        
    /**
     * Sets the App::$layoutModule to current module and App::$layoutName property to 
     * provided value
     * 
     * NOTE: If $name is FALSE then the layout is turned off.
     * 
     * @param string|FALSE $name Optional. Name of layout file. If not provided then 
     *      the last set layout name is preserved and just module is changed.
     *      If FALSE then layout is turned off. Defaults to NULL (= not provided). 
     */
    public function setLayout($name = null) {
        parent::setLayout($name);
    }
    
    /**
     * Sets the App::$doctypeModule to current module and App::$doctypeName property to 
     * provided value
     * 
     * NOTE: If $name is FALSE then the doctype is turned off. Moreover, doctype is used 
     *      only if layout is used. It means that if the layout is turned off then also
     *      no doctype will be loaded (= it's turned off).
     * 
     * @param string|FALSE $name Optional. Name of doctype file. If not provided then 
     *      the last set doctype name is preserved and just module is changed.
     *      If FALSE then doctype is turned off. Defaults to NULL (= not provided). 
     */
    public function setDoctype($name = null) {
        parent::setDoctype($name);
    }
    
    /**
     * Loads specified library under current module libs/ folder.
     * 
     * @param string $name Path to library script placed under libs folder. 
     */
    public function loadLib($name) {
        parent::loadLib($name);
    }    
    
    /**
     * Loads specified vendor (= external) library under current module vendors/ folder.
     * 
     * @param string $name Path to library script placed under vendors folder. 
     */
    public function loadVendor($name) {
        parent::loadVendor($name);
    }    
    
    /**
     * Loads specified model under current module.
     * 
     * Use controller __construct() method to preload controller model(s), 
     * e.g. in Users conroller:
     * 
     *      public function __construct(){
     *          parent::__construct();
     *          $this->loadModel('MyModuleModel');
     *      }
     * 
     * @param string $name Model class name. 
     * @param bool $getInstance It TRUE then an model instance is returned.
     *      Defaults to FALSE.
     * 
     * @return void|Model If $getInstance is TRUE the a model instance is returned.
     *      Otherwise void.
     */
    public function loadModel($name, $getInstance = false) {
        return parent::loadModel($name, $getInstance);
    }
        
    /**
     * Loads script placed under current module views/ folder and returns it's html output.
     *
     * @param string $name Name of script name placed under views folder.
     * @param array $params Optional. Associaltive array of view params available as $this->params in view. 
     *      This can be easily created by compact() function, e.g.: compact('paramForScript1', 'paramForScript2', ...)
     *      or it can be created manualy, e.g.: array('param1' => 'its value', 'param2' => 'its value' , ...)
     *      Defaults to empty array().
     * @param array $data Optional. View data available as $this->data in view. 
     *      Defaults to empty array().
     * @param string|array $catchVariables Optional. A single variable name or an array
     *      of such names to be catched after the view is loaded. They
     *      are returned by aux output $catchedVariables. If the specified variable
     *      does not exist (is not created by view) then it is not present
     *      in $catchedVariables. If this option is any empty value then nothing is 
     *      catched. Defaults to NULL.
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return string Html output generated by the script. 
     */
    public function loadView($name, $params = array(), $data = array(), $catchVariables = null, &$catchedVariables = array()) {
        return parent::loadView($name, $params, $data, $catchVariables, $catchedVariables);
    }
    
    /**
     * Loads script placed under current module elements/ folder and returns it's
     * html output.
     * 
     * @param string $name Name of script name placed under elements folder.
     * @param array $params Optional. Associaltive array of element params available as $this->params in element. 
     *      This can be easily created by compact() function, e.g.: compact('paramForScript1', 'paramForScript2', ...)
     *      or it can be created manualy, e.g.: array('param1' => 'its value', 'param2' => 'its value' , ...)
     *      Defaults to empty array() - means no params are passed.
     * @param array $data Optional. Element data available as $this->data in element. 
     *      If TRUE then data are retrieved by App::getSubjectData() from request data. 
     *      Defaults to TRUE.
     * @param string|array $catchVariables Optional. A single variable name or an array
     *      of such names to be catched after the element is loaded. They
     *      are returned by aux output $catchedVariables. If the specified variable
     *      does not exist (is not created by element) then it is not present
     *      in $catchedVariables. If this option is any empty value then nothing is 
     *      catched. Defaults to NULL.
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return string Html output generated by the script. 
     */
    public function loadElement($name, $params = array(), $data = true, $catchVariables = null, &$catchedVariables = array()) {
        return parent::loadElement($name, $params, $data, $catchVariables, $catchedVariables);
    }
    
    /**
     * Loads specified Controller::action() under current module and returns its output.
     * 
     * @param string $controller Controller class name.
     * @param string $action Action name must correspond exactly with required action() name.
     * @param array $params Optional. Action dispatch params. 
     * @param array $data Optional. Action dispatch data. If TRUE then data are retrieved 
     *      by App::getSubjectData() from request data. Defaults to TRUE.
     * @param array|mixed $args Controller dispatch args or single arg int|float|string. 
     *      They are used as input arguments for $action method
     * 
     * @return mixed Controller action output.
     */
    public function loadControllerAction($controller, $action, $params = array(), $data = true, $args = array()) {
        return parent::loadControllerAction($controller, $action, $params, $data, $args);
    }
    
    /**
     * Sets the App::$cssFiles property, containing .css files dynamically attached to html header
     * in layout
     * 
     * @param string|array $files Single .css filename (string) or an array of 
     *      .css filenames to be attached in html document header (see App/elements/cssHeader). 
     *      Filename must be specified with full path starting from current module /css folder.
     *      In the most cases, the simple name of .css file will be enough, as the most of .css
     *      files are placed directly under /css folder. To specify attributes of <link> 
     *      tag use syntax: array('my.css' => array('disabled' => true, ...)).
     *      To specify media attribute a shorthand array('my.css' => 'print') can be used
     *      instead of array('my.css' => array('media' => 'print')).
     */
    public function setCssFiles($files) {
        parent::setCssFiles($files);
    }
    
    /**
     * Sets the App::$jsFiles property, containing .js files dynamically attached html header
     * in layout
     * 
     * @param string|array $files Single .js filename (string) or an array of 
     *      .js filenames to be attached in html document header (see App/elements/jsHeader). 
     *      Filename must be specified with full path starting from current module /js folder.
     *      In the most cases, the simple name of .js file will be enough, as the most of .js
     *      files are placed directly under /js folder. To specify attributes of <script> tag 
     *      use syntax: array('my.js' => array('async' => true, 'type' => 'module', ...)).
     *      To specify type attribute a shorthand array('my.js' => 'module') can be used
     *      instead of array('my.js' => array('type' => 'module')).
     */
    public function setJsFiles($files) {
        parent::setJsFiles($files);
    }
    
    /**
     * Sets new js config and it's value
     * 
     * @param string $path Config path relative to module, e.g. 'urlRoot' or 'MyClass.myProperty'
     * @param mixed $value Config value.
     * 
     * An alternative signature can be used:
     * 
     * @param array $config List of pairs 'cofigPath' => 'value', e.g. array('urlRoot' => '',  'MyClass.myProperty' => '2')
     */
    public function setJsConfig($path, $value = null) {
        parent::setJsConfig($path, $value);
    }    
    
    /**
     * Returns a url path to specified file in current module. The path is either
     * URL_ROOT relative or is prefixed by URL_ROOT depending on value od $prefixByUrlRoot.
     * 
     * @param string $file Module relative url path to file.
     * @param bool $prefixByUrlRoot If TRUE then path is prefixed by URL_ROOT. Defaults
     *          to FALSE, means the returned path is URL_ROOT relative.
     * 
     * @return string
     */
    public function getFileUrlPath($file, $prefixByUrlRoot = false) {
        return parent::getFileUrlPath($file, $prefixByUrlRoot);
    }
    
    /**
     * Sets admin smartTabs icon for layout
     * 
     * @param string $icon Icon filename specified with full path starting from 
     *      current module /img folder, e.g. '/silk/my_icon.png'.
     */
    public function setTabIcon($icon) {
        parent::setTabIcon($icon);
    }
}

