<?php
class Validate {
    
    /**
     * Return TRUE if the provided value is NO one of following:
     *  - NULL
     *  - FALSE
     *  - empty string '' 
     *  - (!) string containing only whitespaces (e.g. '    ')
     *  - empty array()
     * 
     * @param mixed $value
     * @param mixed $emptyValues Array of alternative values considered as empty.
     *      If TRUE then php empty() is used to detect empty value
     * 
     * @return bool 
     */
    public static function notEmpty($value, $emptyValues = array()) {
        if ($emptyValues === true) {
            $isEmptyValue = empty($value);
        }
        else {
            $isEmptyValue = in_array($value, (array)$emptyValues, true);
        }
        return $value !== null
            && $value !== false
            && (!is_string($value) || trim($value) !== '') 
            && $value !== array() 
            && !$isEmptyValue;
    }
    
    /**
     * Return TRUE if the provided value is one of following:
     *  - NULL
     *  - FALSE
     *  - empty string '' 
     *  - (!) string containing only whitespaces (e.g. '    ') 
     *  - empty array()
     * 
     * @param mixed $value
     * @param mixed $emptyValues Array of alternative values considered as empty.
     *      If TRUE then php empty() is used to detect empty value
     * 
     * @return bool 
     */
    public static function emptyValue($value, $emptyValues = array()) {
        if ($emptyValues === true) {
            $isEmptyValue = empty($value);
        }
        else {
            $isEmptyValue = in_array($value, (array)$emptyValues, true);
        }
        return $value === null
            || $value === false
            || (is_string($value) && trim($value) === '') 
            || $value === array()
            || $isEmptyValue;
    }
    
    /**
     * Checks if provided value is an integer number or a string representing integer number
     * or if it can be replaced by integer number. E.g. all following are resolved as integer
     * numbers: 10, '10', 10.0, '10.0'.
     * 
     * ATTENTION: Some numbers can be validated both as float and integer: 10.0, '10.0'.
     * 
     * Used for validation in models.
     * 
     * @param mixed $value
     * 
     * @return bool 
     */
    public static function intNumber($value) {
        return (
            is_numeric($value) 
            // remove trailing zeros
            && (
                strpos($value, '.') === false
                || 
                ($value = rtrim(rtrim($value, '0'), '.')) || true
            )
            && (string)$value === (string)(int)$value
        );
    }
    
    /**
     * Checks if provided value is an float number or a string representing float number
     * or if it can be replaced by integer number. E.g. all following are resolved as float
     * numbers: 10.0, '10.0', 10.1, '10.1'.
     * 
     * ATTENTION: Some numbers can be validated both as float and integer: 10.0, '10.0'.
     * 
     * Used for validation in models.
     * 
     * @param mixed $value
     * 
     * @return bool 
     */
    public static function floatNumber($value) {
        return (
            // avoid 10.00 to resolve as integer by strpos($value, '.') !== false
            // as `(string)10.00` resolves to `10` (!)
            is_float($value) 
            || 
            is_numeric($value) 
            && strpos($value, '.') !== false
            ||
            $value < 1
            && $value != 0
            && $value > -1
        );
    }
    
    /**
     * Checks if provided value is a number
     * 
     * @param mixed $value
     * 
     * @return bool
     */
    public static function number($value) {
        return is_numeric($value);
    }
        
    /**
     * Checks if provided value has a valid format of email address.
     * If required then email domain existence is validated too.
     * 
     * @param string $value
     * @param bool $validateDomainExistence Optional. Defaults to FALSE.
     * 
     * @return bool 
     * 
     * @todo - the check of existing domain is very slow
     */
    public static function email($value, $validateDomainExistence = false) {
        // validate format
        $hostnameRegex = '(?:[a-z0-9][-a-z0-9]*\.)*(?:[a-z0-9][-a-z0-9]{0,62})\.(?:(?:[a-z]{2}\.)?[a-z]{2,4}|museum|travel)';
        $emailRegex = '/^[a-z0-9!#$%&\'*+\/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&\'*+\/=?^_`{|}~-]+)*@' . $hostnameRegex . '$/i';
        $formatResult = preg_match($emailRegex, $value);
        if (!$formatResult || !$validateDomainExistence) {
            return (bool)$formatResult;
        }
        
        // validate domain existence
        $domain = substr($value, strrpos($value, '@') + 1); 
        if (function_exists('getmxrr') && getmxrr($domain, $mxhosts)) {
            return true;
        }
        //rblb//if (function_exists('checkdnsrr') && (checkdnsrr($domain, 'MX') || checkdnsrr($domain,"A"))) {
        if (function_exists('checkdnsrr') && checkdnsrr($domain, 'MX')) {
            return true;
        }
        //rblb//return is_array(gethostbynamel($domain)); // seems to not work properly, returns an IP for any provided domain, even unexisting
        return false;
    }
    
    /**
     * Validates domain name
     * 
     * @param string $value
     * @param bool $validateExistence Optional. Defaults to FALSE.
     * 
     * @return boolean
     */
    public static function domain($value, $validateExistence = false) {
        $domainRegex = '^(?!\-)(?:[a-zA-Z\d\-]{0,62}[a-zA-Z\d]\.){1,126}(?!\d+)[a-zA-Z\d]{1,63}$';
        $formatResult = preg_match($domainRegex, $value);
        if (!$formatResult || !$validateExistence) {
            return (bool)$formatResult;
        }
        
        // validate domain existence
        if (function_exists('getmxrr') && getmxrr($value, $mxhosts)) {
            return true;
        }
        //rblb//if (function_exists('checkdnsrr') && (checkdnsrr($value, 'MX') || checkdnsrr($value,"A"))) {
        if (function_exists('checkdnsrr') && checkdnsrr($value, 'MX')) {
            return true;
        }
        //rblb//return is_array(gethostbynamel($value)); // seems to not work properly, returns an IP for any provided domain, even unexisting
        return false;
    }
    
    /**
     * Checks if provided value contains at least one email address.
     * 
     * @param string $value
     * 
     * @return bool 
     */
    public static function stringWithEmail($value) {
        // validate format
        $hostnameRegex = '(?:[a-z0-9][-a-z0-9]*\.)*(?:[a-z0-9][-a-z0-9]{0,62})\.(?:(?:[a-z]{2}\.)?[a-z]{2,4}|museum|travel)';
        $emailRegex = '/[a-z0-9!#$%&\'*+\/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&\'*+\/=?^_`{|}~-]+)*@' . $hostnameRegex . '/i';
        $formatResult = preg_match($emailRegex, $value);
        return (bool)$formatResult;
    }
    
    /**
     * Checks if provided value is alphanumeric.
     * 
     * @param mixed $value
     * @return bool 
     */
    public static function alphanumeric($value) {
        return (bool)preg_match('/^[\p{Ll}\p{Lm}\p{Lo}\p{Lt}\p{Lu}\p{Nd}]+$/mu', $value);
    }
    
    /**
     * Checks if provided value contains characters possible in human name
     * 
     * @param string $value
     * @return bool 
     */
    public static function humanName($value) {
        // there are 2 different spaces, normal and nbsp (AltGr + space)
        return (bool)preg_match('/^[\p{Ll}\p{Lm}\p{Lo}\p{Lt}\p{Lu}  \'\.\-]+$/mu', $value);
    }
    
    /**
     * Checks if provided value contains characters possible in human name and if 
     * there is at least first and last name
     * 
     * @param string $value
     * @return bool 
     */
    public static function humanFullname($value) {
        $value = trim($value);
        // there are 2 different spaces, normal and nbsp (AltGr + space)
        return self::humanName($value) && count(preg_split('/[  ]/', $value)) > 1;
    }
        
    /**
     * Checks if provided value is literary text. E.g. 'Toto je spisovný text, pretože
     * neobsahuje žiadne kliky-háky', 'Toto <b>ni3je spisovný</b>#text,čo_mislýš prečo?'
     * 
     * @param string $value
     * @return bool 
     */
    public static function literaryText($value) {
        // explode text by whitespaces
        // there are 2 different spaces, normal and nbsp (AltGr + space)
        $words = preg_split('/[\s ]+/', $value);
        // check word by word
        foreach ($words as $word) {
            if (!($word = trim($word, '"\':\-,.!?0123456789()[]'))) {
                continue;
            }
            if (!preg_match('/^[\p{Ll}\p{Lm}\p{Lo}\p{Lt}\p{Lu}\'\-\/]+$/mu', $word)) {
                return false;
            }
        }
        return true;
    }
        
    /**
     * Checks if provided value could be an address (means it contains only allowed 
     * address characters, at least one word, at least one number)
     * 
     * @param string $value
     * @return bool 
     */
    public static function address($value) {
        // check if provided value:
        // - contains only allowed address characters
        // - contains at least one word
        // - contains at least one number
        // there are 2 different spaces, normal and nbsp (AltGr + space)
        return (bool)preg_match('/^[\p{Ll}\p{Lm}\p{Lo}\p{Lt}\p{Lu}\p{Nd}\s \'\.\/\,\-\:\(\)\"]+$/mu', $value)
                && (bool)preg_match('/[\p{Ll}\p{Lm}\p{Lo}\p{Lt}\p{Lu}]+/mu', $value)
                && (bool)preg_match('/[\p{Nd}]+/mu', $value);
    }
        
    /**
     * Checks if provided value is PSČ
     * 
     * @param string $value
     * @return bool 
     */
    public static function psc($value) {
        // there are 2 different spaces, normal and nbsp (AltGr + space)
        return (bool)preg_match('/^\d\d\d[\s ]?\d\d$/', $value);
    }
    
    /**
     * Checks if provided value is IČO
     * 
     * @see https://sk.wikipedia.org/wiki/Identifika%C4%8Dn%C3%A9_%C4%8D%C3%ADslo_organiz%C3%A1cie
     * 
     * @param string $value
     * @return bool 
     */
    public static function ico($value) {
        // there are 2 different spaces, normal and nbsp (AltGr + space)
        $value = str_replace(array(' ', ' ', '-'), array('', '', ''), $value);
        return 
            (bool)preg_match('/^[0-9]+$/', $value)
            && (
                // regular IČO (e.g. 00179027)
                ($l = strlen($value)) >= 6 
                && $l <= 8
                ||
                // regular IČO + 4 digits extension (e.g. 001790270403 - IČO organizačnej zložky)
                $l >= 10
                && $l <= 12
            );
    }
    
    /**
     * Checks if provided value is DIČ (with or without country code - see $countryCode param)
     * 
     * @see https://cs.wikipedia.org/wiki/Da%C5%88ov%C3%A9_identifika%C4%8Dn%C3%AD_%C4%8D%C3%ADslo
     * 
     * @param string $value
     * @param string $countryCode Optional. Possible values are 'optionalCountryCode', 
     *          'withCountryCode', 'withoutCountryCode' or use explicit country code
     *          to be required (2 characters long): 'SK', 'CZ', 'DE', 'HU', ....
     *          Defaults to 'optionalCountryCode'.
     *          
     * 
     * @return bool 
     */
    public static function dic($value, $countryCode = 'optionalCountryCode') {
        $countryCode = strtolower($countryCode);
        // there are 2 different spaces, normal and nbsp (AltGr + space)
        $value = str_replace(array(' ', ' ', '-'), array('', '', ''), $value);
        if ($countryCode === 'optionalcountrycode') {
            return (bool)preg_match('/^[A-Z]{2}[UX]?[0-9]+X?$/i', $value) && ($l = strlen($value)) >= 10 && $l <= 17
                || (bool)preg_match('/^[UX]?[0-9]+X?$/i', $value) && ($l = strlen($value)) >= 8 && $l <= 15;
        }
        elseif ($countryCode === 'withcountrycode') {
            return (bool)preg_match('/^[A-Z]{2}[UX]?[0-9]+X?$/i', $value) && ($l = strlen($value)) >= 10 && $l <= 17;
        }
        elseif ($countryCode === 'withoutcountrycode') {
            return (bool)preg_match('/^[UX]?[0-9]+X?$/i', $value) && ($l = strlen($value)) >= 8 && $l <= 15;
        }
        else {
            if (!preg_match('/^[A-Z]{2}$/i', $countryCode)) {
                throw new Exception(__e(__FILE__, 'Invalid DIC countryCode: %s', $countryCode));
            }
            return (bool)preg_match('/^' . $countryCode . '[UX]?[0-9]+X?$/i', $value) && ($l = strlen($value)) >= 10 && $l <= 17;
        }
    }
    
    /**
     * Checks if provided value is valid phone number
     * 
     * @todo Phone number validity is possible to verify using https://numverify.com/ (250/month for free)
     * 
     * @param string $value
     * @param array $options Following are available:
     *      - 'minNumbersCount' (int) Minimal count of numbers. If NULL
     *          then min numbers count is not restricted. Defaults to 9.
     *      - 'maxNumbersCount' (int) Maximal count of numbers. If NULL
     *          then max numbers count is not restricted. Defaults to 12.
     *      - 'internationalFormat' (bool) If TRUE then an international format
     *          of phone number must be provided (starting by +). Defaults to FALSE
     *          it means it is not required but is accepted if provided.
     * 
     * An alternative signature can be used:
     * 
     * @param string $value
     * @param int $minNumbersCount Optional. Minimal count of numbers. If NULL
     *      then min numbers count is not restricted. Defaults to 9.
     * @param int $maxNumbersCount Optional. Maximal count of numbers. If NULL
     *      then max numbers count is not restricted. Defaults to 13.
     * 
     * @return bool 
     */
    public static function phone($value, $minNumbersCount = 9, $maxNumbersCount = 13) {
        // implement the $options signature
        $defaults = array(
            'minNumbersCount' => 9,
            'maxNumbersCount' => 13,
            'internationalFormat' => false,
        );
        if (!is_array($minNumbersCount)) {
            $options = array(
                'minNumbersCount' => $minNumbersCount,
                'maxNumbersCount' => $maxNumbersCount,
            );
        }
        else {
            $options = $minNumbersCount;
        }
        $options = array_merge($defaults, $options);
        // there are 2 different spaces here, normal and nbsp (AltGr + space)
        $regex = $options['internationalFormat'] ? '/^(?:\+|00)[\d\/  \-]{7,}$/' : '/^(?:\+|00)?[\d\/  \-]{7,}$/';
        if (!preg_match($regex, $value)) {
            return false;
        }
        $numbersCount = strlen(ltrim(preg_replace('/[^0-9]/', '', $value), '0'));
        if (
            $options['minNumbersCount'] !== null 
            && self::intNumber($options['minNumbersCount'])
            && $numbersCount < $options['minNumbersCount']
        ) {
            return false;
        }
        if (
            $options['maxNumbersCount'] !== null 
            && self::intNumber($options['maxNumbersCount'])
            && $numbersCount > $options['maxNumbersCount']
        ) {
            return false;
        }
        return true;
    }
    
    /**
     * Checks if provided value is variable symbol
     * 
     * @param string $value
     * @return bool 
     */
    public static function vs($value) {
        return (bool)preg_match('/^\d{10}$/', $value);
    }
    
    /**
     * Checks if provided value is a date in given format(s).
     * 
     * @param mixed $value
     * @param string|array|NULL $format Optional. Format string or array of format 
     *      strings. If NULL then any possible date format is accepted. Defaults to 'ymd'.
     * @return bool 
     */
    public static function date($value, $format = 'ymd') {
        if (!empty($format)) {
            $regex['dmy'] = '%^(?:(?:31(\\/|-|\\.|\\x20)(?:0?[13578]|1[02]))\\1|(?:(?:29|30)(\\/|-|\\.|\\x20)(?:0?[1,3-9]|1[0-2])\\2))(?:(?:1[6-9]|[2-9]\\d)?\\d{2})$|^(?:29(\\/|-|\\.|\\x20)0?2\\3(?:(?:(?:1[6-9]|[2-9]\\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:0?[1-9]|1\\d|2[0-8])(\\/|-|\\.|\\x20)(?:(?:0?[1-9])|(?:1[0-2]))\\4(?:(?:1[6-9]|[2-9]\\d)?\\d{2})$%';
            $regex['mdy'] = '%^(?:(?:(?:0?[13578]|1[02])(\\/|-|\\.|\\x20)31)\\1|(?:(?:0?[13-9]|1[0-2])(\\/|-|\\.|\\x20)(?:29|30)\\2))(?:(?:1[6-9]|[2-9]\\d)?\\d{2})$|^(?:0?2(\\/|-|\\.|\\x20)29\\3(?:(?:(?:1[6-9]|[2-9]\\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:(?:0?[1-9])|(?:1[0-2]))(\\/|-|\\.|\\x20)(?:0?[1-9]|1\\d|2[0-8])\\4(?:(?:1[6-9]|[2-9]\\d)?\\d{2})$%';
            $regex['ymd'] = '%^(?:(?:(?:(?:(?:1[6-9]|[2-9]\\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00)))(\\/|-|\\.|\\x20)(?:0?2\\1(?:29)))|(?:(?:(?:1[6-9]|[2-9]\\d)?\\d{2})(\\/|-|\\.|\\x20)(?:(?:(?:0?[13578]|1[02])\\2(?:31))|(?:(?:0?[1,3-9]|1[0-2])\\2(29|30))|(?:(?:0?[1-9])|(?:1[0-2]))\\2(?:0?[1-9]|1\\d|2[0-8]))))$%';
            $regex['dMy'] = '/^((31(?!\\ (Feb(ruary)?|Apr(il)?|June?|(Sep(?=\\b|t)t?|Nov)(ember)?)))|((30|29)(?!\\ Feb(ruary)?))|(29(?=\\ Feb(ruary)?\\ (((1[6-9]|[2-9]\\d)(0[48]|[2468][048]|[13579][26])|((16|[2468][048]|[3579][26])00)))))|(0?[1-9])|1\\d|2[0-8])\\ (Jan(uary)?|Feb(ruary)?|Ma(r(ch)?|y)|Apr(il)?|Ju((ly?)|(ne?))|Aug(ust)?|Oct(ober)?|(Sep(?=\\b|t)t?|Nov|Dec)(ember)?)\\ ((1[6-9]|[2-9]\\d)\\d{2})$/';
            $regex['Mdy'] = '/^(?:(((Jan(uary)?|Ma(r(ch)?|y)|Jul(y)?|Aug(ust)?|Oct(ober)?|Dec(ember)?)\\ 31)|((Jan(uary)?|Ma(r(ch)?|y)|Apr(il)?|Ju((ly?)|(ne?))|Aug(ust)?|Oct(ober)?|(Sept|Nov|Dec)(ember)?)\\ (0?[1-9]|([12]\\d)|30))|(Feb(ruary)?\\ (0?[1-9]|1\\d|2[0-8]|(29(?=,?\\ ((1[6-9]|[2-9]\\d)(0[48]|[2468][048]|[13579][26])|((16|[2468][048]|[3579][26])00)))))))\\,?\\ ((1[6-9]|[2-9]\\d)\\d{2}))$/';
            $regex['My'] = '%^(Jan(uary)?|Feb(ruary)?|Ma(r(ch)?|y)|Apr(il)?|Ju((ly?)|(ne?))|Aug(ust)?|Oct(ober)?|(Sep(?=\\b|t)t?|Nov|Dec)(ember)?)[ /]((1[6-9]|[2-9]\\d)\\d{2})$%';
            $regex['my'] = '%^(((0[123456789]|10|11|12)([- /.])(([1][9][0-9][0-9])|([2][0-9][0-9][0-9]))))$%';

            $format = (is_array($format)) ? array_values($format) : array($format);
            foreach ($format as $key) {
                if (preg_match($regex[$key], $value)) {
                    return true;
                }
            }
        }
        elseif (
            ($timestamp = strtotime($value)) !== false 
            && $timestamp !== -1
        ) {
            return true;
        }
		return false;
    } 
    
    /**
     * Checks if provided value is a time like hh:mm or h:mm
     * 
     * @param mixed $value
     * 
     * @return bool
     */
    public static function time($value) {
        return preg_match('/^([01]?[0-9]|2[0123])\:[012345][0-9]$/', $value);
    }
    
    /**
     * Validates provided captcha code with captcha code stored in session.
     * 
     * @param string $code Captcha code to be validated.
     * @param array $options Following are available:
     *      - 'name' (string) Captcha name 
     *      - 'caseSensitive' (bool) Defaults to FALSE.
     * 
     * @return bool 
     */
    static public function captcha($code, $options = array()) {
        $defaults = array(
            'name' => 'default',
            'caseSensitive' => false,
        );
        $options = array_merge($defaults, $options);
        App::loadLib('App', 'Captcha');
        $captcha = Captcha::getCode($options['name']);
        if (!$options['caseSensitive']) {
            $captcha = strtolower($captcha);
            $code = strtolower($code);
        }
        return $captcha == $code;
    }
    
    /**
     * Checks length of provided string.
     */
    static public function minLength($value, $length) {
        return strlen($value) >= (int)$length;
    }
    
    /**
     * Checks length of provided string.
     */
    static public function maxLength($value, $length) {
        return strlen($value) <= (int)$length;
    }
    
    /**
     * Checks length of provided array.
     */
    static public function arrayMinLength($value, $length) {
        return is_array($value) && count($value) >= (int)$length;
    }
    
    /**
     * Checks length of provided array.
     */
    static public function arrayMaxLength($value, $length) {
        return is_array($value) && count($value) <= (int)$length;
    }
    
    /**
     * Checks if provided value is less than given limit.
     */
    static public function lt($value, $limit) {
        return $value < $limit;
    }
    
    /**
     * Checks if provided value is less or equal to given limit.
     */
    static public function lte($value, $limit) {
        return $value <= $limit;
    }
    
    /**
     * Checks if provided value is greater than given limit.
     */
    static public function gt($value, $limit) {
        return $value > $limit;
    }
    
    /**
     * Checks if provided value is greater or equal to given limit.
     */
    static public function gte($value, $limit) {
        return $value >= $limit;
    } 
    
    /**
     * Checks if provided value is between given $bottom and $top limits.
     */
    static public function between($value, $bottom, $top) {
        return $value >= $bottom && $value <= $top;
    }
    
    /**
     * Checks if provided value is in given array
     */
    static public function in($value, $array, $strict = false) {
        return in_array($value, $array, $strict);
    }  
    
    /**
     * Checks that value has a valid file extension.
     *
     * @param string $value
     * @param array $extensions Optional. File extenstions to validate. Defaults
     *      to array('gif', 'png', 'jpg', 'jpeg', 'jpe').
     * 
     * @return bool
     */
	static function extension($value, $extensions = array('gif', 'png', 'jpg', 'jpeg', 'jpe')) {
        $extension = explode('.', $value);
		$extension = strtolower(array_pop($extension));
		return in_array($extension, $extensions);
	}
        
    /**
     * Checks that a value is a valid URL.
     * URL availability can be checked too.
     *
     * Following component parts are validated:
     *
     * - a valid, optional, HTTP or HTTPS scheme
     * - a valid, optional username and optional password
     * - a valid ip address OR a valid domain name with an optional port number
     * - an optional valid path
     * - an optional query string (get parameters)
     * - an optional fragment (anchor tag)
     * 
     * @link http://stackoverflow.com/questions/2280394/check-if-an-url-exists-in-php
     * @link http://stackoverflow.com/questions/981954/how-can-one-check-to-see-if-a-remote-file-exists-using-php/982045#982045
     *
     * @param string $value Value to validate
     * @param array $options Optional. Following options can be used:
     *      - 'scheme' (string) One of 'http', 'ftp', 'file', 'news', 'gopher', 'any'.
     *          In case of 'http' and 'ftp' also https and ftps schemes are considered 
     *          to be valid. Defautls to 'http'.
     *      - 'strict' (bool) If TRUE then the URL must be prefixed by scheme 
     *          (one of http(s)/ftp(s)/file/news/gopher). If FALSE then the scheme 
     *          is not required. Defaults to TRUE.
     *      - 'exists' (bool) If TRUE then availability of URL is verified. 
     *          Defaults to FALSE.
     * 
     * @return bool
     */
	static public function url($value, $options = array()) {
        $defaults = array(
            'scheme' => 'http', // 'http', 'ftp', 'file', 'news', 'gopher', 'any'
            'strict' => true,
            'exists' => false,
        );
        $options = array_merge($defaults, $options);
        $options['scheme'] = strtolower($options['scheme']);
        
        // create regex
        if (
            $options['scheme'] == 'http' 
            || $options['scheme'] == 'ftp'
        ) {
            $schemeRegex = '(?:' . $options['scheme'] . 's?:\/\/)';
        }
        elseif (
            $options['scheme'] == 'file'
            || $options['scheme'] == 'news'
            || $options['scheme'] == 'gopher'
        ) {
            $schemeRegex = '(?:' . $options['scheme'] . ':\/\/)';
        }
        elseif ($options['scheme'] == 'any') {
            $schemeRegex = '(?:(?:https?|ftps?|file|news|gopher):\/\/)';
        }
        else {
            throw new Exception(__e(__FILE__, 'Invalid url scheme %s', $options['scheme']));
        }
        if (!$options['strict']) {
            $schemeRegex .= '?';
        }
        $loginRegex = '(?:[a-z0-9\_\-\.\@]+(?:\:[a-z0-9' . preg_quote('!"$&\'()*+,-._;=~') . ']+)?\@)?'; // 'username:password@', username can be an email (containing @)!
        $hostnameRegex = '(?:[a-z0-9][-a-z0-9]*\.)*(?:[a-z0-9][-a-z0-9]{0,62})\.(?:(?:[a-z]{2}\.)?[a-z]{2,4}|museum|travel|cloud)';
        $ipv6Regex  = '((([0-9A-Fa-f]{1,4}:){7}(([0-9A-Fa-f]{1,4})|:))|(([0-9A-Fa-f]{1,4}:){6}'
            . '(:|((25[0-5]|2[0-4]\d|[01]?\d{1,2})(\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})){3})'
            . '|(:[0-9A-Fa-f]{1,4})))|(([0-9A-Fa-f]{1,4}:){5}((:((25[0-5]|2[0-4]\d|[01]?\d{1,2})'
            . '(\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})){3})?)|((:[0-9A-Fa-f]{1,4}){1,2})))|(([0-9A-Fa-f]{1,4}:)'
            . '{4}(:[0-9A-Fa-f]{1,4}){0,1}((:((25[0-5]|2[0-4]\d|[01]?\d{1,2})(\.(25[0-5]|2[0-4]\d|[01]?\d{1,2}))'
            . '{3})?)|((:[0-9A-Fa-f]{1,4}){1,2})))|(([0-9A-Fa-f]{1,4}:){3}(:[0-9A-Fa-f]{1,4}){0,2}'
            . '((:((25[0-5]|2[0-4]\d|[01]?\d{1,2})(\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})){3})?)|'
            . '((:[0-9A-Fa-f]{1,4}){1,2})))|(([0-9A-Fa-f]{1,4}:){2}(:[0-9A-Fa-f]{1,4}){0,3}'
            . '((:((25[0-5]|2[0-4]\d|[01]?\d{1,2})(\.(25[0-5]|2[0-4]\d|[01]?\d{1,2}))'
            . '{3})?)|((:[0-9A-Fa-f]{1,4}){1,2})))|(([0-9A-Fa-f]{1,4}:)(:[0-9A-Fa-f]{1,4})'
            . '{0,4}((:((25[0-5]|2[0-4]\d|[01]?\d{1,2})(\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})){3})?)'
            . '|((:[0-9A-Fa-f]{1,4}){1,2})))|(:(:[0-9A-Fa-f]{1,4}){0,5}((:((25[0-5]|2[0-4]'
            . '\d|[01]?\d{1,2})(\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})){3})?)|((:[0-9A-Fa-f]{1,4})'
            . '{1,2})))|(((25[0-5]|2[0-4]\d|[01]?\d{1,2})(\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})){3})))(%.+)?';
        $ipv4Regex = '(?:(?:25[0-5]|2[0-4][0-9]|(?:(?:1[0-9])?|[1-9]?)[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|(?:(?:1[0-9])?|[1-9]?)[0-9])';
		$pathValidCharsRegex = '(?:[' . preg_quote('~!@$&*()_-+=[]:;"\'<>,.') . '\/0-9a-z\p{Ll}\p{Lm}\p{Lo}\p{Lt}\p{Lu} ]|(?:%[0-9a-f]{2}))';
		$getValidCharsRegex = '(?:[' . preg_quote('~!@$&*()_-+=[]:;"\'<>,.?') . '\/0-9a-z\p{Ll}\p{Lm}\p{Lo}\p{Lt}\p{Lu} ]|(?:%[0-9a-f]{2}))';
		$hashValidCharsRegex = '(?:[' . preg_quote('~!@$&*()_-+=[]:;"\'<>,.?#') . '\/0-9a-z\p{Ll}\p{Lm}\p{Lo}\p{Lt}\p{Lu} ]|(?:%[0-9a-f]{2}))';
        
		$regex = '/^' . $schemeRegex . $loginRegex .
			'(?:' . $ipv4Regex . '|\[' . $ipv6Regex . '\]|' . $hostnameRegex . ')' .
			'(?::[1-9][0-9]{0,4})?' .
			'(?:\/?|\/' . $pathValidCharsRegex . '*)?' .
			'(?:\?' . $getValidCharsRegex . '*)?' .
			'(?:#' . $hashValidCharsRegex . '*)?$/iu';
        
        // check url format
        $valid = (bool)preg_match($regex, $value);
        
        // check url availability if required
        if ($valid && $options['exists']) {
            $ch = curl_init($value);
            // don't fetch the actual page, you only want to check the connection is ok
            curl_setopt($ch, CURLOPT_NOBODY, true);
            // bypass SSL certificate validation
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            // do request
            $result = curl_exec($ch);
            $valid = false;
            // if request did not fail
            if ($result !== false) {
                // if request was ok, check response code
                $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);  
                if ($statusCode == 200) {
                    $valid = true;   
                }
            }
            curl_close($ch);
        }
		return $valid;
	}
    
    /**
     * Checks that the provided path (of file or dir) is really app root relative path.
     * It means that it does not try to step out from app root and it does not start by directory separator. 
     * Path locale root, type and existence can be checked optionally too.
     *  
     * @param string $path App root relative path
     * @param array $options Following options can be used:
     *      - 'root' (string) App root relative path the provided $path should point into (start by).
     *          Defaults to NULL (the check is not done).
     *      - 'file' (bool) If TRUE then it is checked that provided path is a file path.
     *          File existence is examined only in the case that 'exists' option is TRUE.
     *          Otherwise anything with extension is considered for file path.
     *          Defaults to FALSE (the check is not done).
     *      - 'dir' (bool) If TRUE then it is checked that provided path is a directory path (not a file).
     *          Directory existence is examined only in the case that 'exists' option is TRUE.
     *          Otherwise anything without extension is considered for directory path.
     *          Defaults to FALSE (the check is not done).
     *      - 'extension' (string|array) Extension or array of extensions the provided
     *          path must comply. Defaults to NULL (the check is not done).
     *      - 'exists' (bool) If TRUE then the existence of the path is verified.
     *          Defaults to FALSE (the check is not done).
     * 
     * @return bool
     * @throws Exception If both options 'file' and 'dir' are set TRUE.
     */
    static public function path($path, $options = array()) {
        $defaults = array(
            'root' => null,
            'file' => false,
            'dir' => false,
            'extension' => null,
            'exists' => false,
        );
        $options = array_merge($defaults, $options);
        if ($options['file'] && $options['dir']) {
            throw new Exception("It is not possible to validate path to be file and directory at the same time");
        }
        // normalize path
        $path = ltrim($path, DS);
        // check that it stays app root relative
        if (
            !is_string($path)
            || substr($path, 0, 1) === DS
            || strpos($path, '..') !== false
        ) {
            return false;
        }
        // check root
        if ($options['root']) {
            $rootWithoutDS = trim($options['root'], DS);
            $rootWithDS = $rootWithoutDS . DS;
            $rootLength = strlen($rootWithDS);
            if ( 
                $path !== $rootWithoutDS
                && substr($path, 0, $rootLength) !== $rootWithDS
            ) {
                return false;
            }
        }
        // for following checks we need absolute path
        
        $path = ROOT . DS . $path;
        // check file existence together with 'file' and 'dir' options
        if (
            $options['exists'] 
            && (
                !file_exists($path)
                || $options['file'] && !is_file($path)
                || $options['dir'] && !is_dir($path)
            )
        ) {
            return false;
        }
        // check file extension together with 'file' and 'dir' options
        if (    
            $options['extension']
            || $options['file']
            || $options['dir']
        ) {
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            if ($options['dir'] && $extension) {
                return false;
            }
            if (
                $options['extension']
                && !in_array(strtolower($extension), (array)$options['extension'])
            ) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Checks that the provided data are valid upload data.
     *  
     * @param array $data Upload data in form without nested fields 
     * @param array $options Following options can be used. All of them are considered only
     *      in the case that the file was successfully uploaded ($data['error'] == UPLOAD_ERR_OK):
     *      - 'type' (string) Required mime type of uploaded file. Can be provided 
     *          also by regular expression. If upload tmp file is readable then 
     *          mime type is checked by content, otherwise by extension. Defaults to NULL (no check).
     *      - 'size' (int) Max allowed size (in bytes) of uploaded file. Defaults to NULL (no check) 
     *      - 'exists' (bool) If TRUE then the existence of temp upload file is verified.
     *          Defaults to FALSE.
     *      - 'empty' (bool) If TRUE then upload data must have error code 
     *          equal to UPLOAD_ERR_NO_FILE. Defaults to FALSE, means not considered.
     *      - 'notEmpty' (bool) If TRUE then upload data may not have error code 
     *          equal to UPLOAD_ERR_NO_FILE. Defaults to FALSE, means not considered.
     *      - 'noErrors' (bool) If TRUE then upload data may not have error code 
     *          equal to one of UPLOAD_ERR_INI_SIZE, UPLOAD_ERR_FORM_SIZE, UPLOAD_ERR_PARTIAL,
     *          UPLOAD_ERR_NO_TMP_DIR, UPLOAD_ERR_CANT_WRITE, UPLOAD_ERR_EXTENSION. 
     *          Defaults to FALSE, means not considered.
     * 
     * @return bool
     */
    static public function uploadData($data, $options = array()) {
        $defaults = array(
            'type' => null,
            'size' => null,
            'exists' => false,
            'empty' => false,
            'notEmpty' => false,
            'noErrors' => false,
        );
        $options = array_merge($defaults, $options);
        if (
            !is_array($data)
            || !isset($data['name'])
            || !isset($data['type'])
            || !isset($data['tmp_name'])
            || !isset($data['error'])
            || !isset($data['size'])
        ) {
            return false;
        }
        if (
            $data['error'] != UPLOAD_ERR_OK
            && $data['error'] != UPLOAD_ERR_INI_SIZE
            && $data['error'] != UPLOAD_ERR_FORM_SIZE
            && $data['error'] != UPLOAD_ERR_PARTIAL
            && $data['error'] != UPLOAD_ERR_NO_FILE
            && $data['error'] != UPLOAD_ERR_NO_TMP_DIR
            && $data['error'] != UPLOAD_ERR_CANT_WRITE
            && $data['error'] != UPLOAD_ERR_EXTENSION
        ) {
            return false;
        }
        if ($data['error'] == UPLOAD_ERR_OK) {
            if (
                !empty($options['exists'])
                && !is_uploaded_file($data['tmp_name'])
            ) {
                return false;
            }
            if (
                !empty($options['size'])
                && $data['size'] > $options['size']
            ) {
                return false;
            }
            if (!empty($options['type'])) {
                if (
                    !empty($options['exists'])
                    || is_readable($data['tmp_name'])
                ) {
                    $mimeType = File::getMimeType($data['tmp_name'], array(
                        'absolutePath' => true,
                        'byExtension' => false,
                    ));
                }
                else {
                    $mimeType = $data['type'];
                }
                if (self::regex($options['type'])) {
                    if (!preg_match($options['type'], $mimeType)) {
                        return false;
                    }
                }
                elseif ($data['type'] != $mimeType) {
                    return false;
                }
            }
        }
        if (
            !empty($options['empty'])
            && $data['error'] != UPLOAD_ERR_NO_FILE
        ) {
            return false;
        }
        if (
            !empty($options['notEmpty'])
            && $data['error'] == UPLOAD_ERR_NO_FILE
        ) {
            return false;
        }
        if (
            !empty($options['noErrors'])
            && $data['error'] != UPLOAD_ERR_OK
            && $data['error'] != UPLOAD_ERR_NO_FILE
        ) {
            return false;
        }
        return true;
    }
    
    /**
     * Checks that the provided data are valid download data (download file source).
     * 
     * @param array $data E.g. array('http://my.file.source/image.png')
     * @param array $options Following options can be used:
     *      - 'exists' (bool) If TRUE then the existence of download file is verified.
     *          Defaults to FALSE.
     * 
     * @return bool
     */
    static public function downloadData($data, $options = array()) {
        $options['scheme'] = 'http';
        return is_array($data) 
            && count($data) === 1
            && Validate::url(reset($data), $options);
    }
    
    /**
     * Checks that the provided data are valid FTP data (FTP file source).
     * 
     * @param array $data E.g. array('ftp://my.file.source/image.png')
     * @param array $options Following options can be used:
     *      - 'exists' (bool) If TRUE then the existence of FTP file is verified.
     *          Defaults to FALSE.
     * 
     * @return bool
     */
    static public function ftpData($data, $options = array()) {
        $options['scheme'] = 'ftp';
        return is_array($data) 
            && count($data) === 1
            && Validate::url(reset($data), $options);
    }
    
    /**
     * Checks that the provided data are valid copy data (copy file source).
     * 
     * @param array $data E.g.array('userfiles/import/image.png')
     * 
     * @return bool
     */
    static public function copyData($data) {
        // check for existence here to avoid mix copy case with case when there is 
        // just saved filename in fileSource (which is get as fileField value)
        return is_array($data)
            && count($data) === 1
            && Validate::path(reset($data), array('file' => true, 'exists' => true));
    }
    
    /**
     * Checks if provided form or DB data are empty
     * 
     * @param array $data Form or DB data.
     * 
     * @return boolean
     */
    static public function emptyData($data) {
        if (empty($data)) {
            return true;
        }
        $data = (array)$data;
        foreach($data as $v) {
            if (
                // not
                ! ( 
                    // empty
                    $v === ''
                    || 
                    $v === null
                    || 
                    is_array($v) 
                    && self::uploadData($v, array('empty' => true)) 
                )
            ) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Checks if array is associative
     * 
     * @param array $array
     * 
     * @return bool 
     */
    static public function assocArray($array) {
        if (!is_array($array)) {
            return false;
        }
        $arrayKeys = array_keys($array);
        foreach ($arrayKeys as $key => $value) {
            if ($key !== $value) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Checks if provided value is valid EAN code
     */
    static public function ean($value) {
        return strlen($value) === 13
            && ctype_digit((string)$value)
            && (substr($value, -1, 1) == Utility::getEanChecksum($value));
    }
    
    /**
     * Checks if provided data satisfy given conditions
     * 
     * @param array $data Data array to satisfy the conditions
     * @param array $conditions Conditions to be checked agains the given data.
     *      They are defined similar way as Model::find() condiiotns, e.g.:
     * 
     *          array(
     *              'username =' => 'admin',
     *              'Profile.firstname ~' => 'adminko',
     *              array(
     *                  array(
     *                      'group_id' => array(2, 9),          // group_id == 2 OR group_id == 9
     *                      'OR',
     *                      'age >' => 5,
     *                  ),
     *                  'AND',                                  // 'AND' is optional
     *                  'active' => 1,          
     *                  'OR',
     *                  'group_id !' => 3,
     *                  'AND',                                  // 'AND' is optional
     *                  'Profile.lastname ~' => 'adminovic',
     *              )
     *              'NOT' => array(
     *                  'email' => null,
     *                  'phone' => null,
     *              )
     *          )
     * 
     *      Possible comparison operators are:
     *          - equals: 'name' => 'Some Name', 'name =' => 'Some Name', 'name' => array('Some Name', 'Some Other Name')
     *          - equals not: 'name !' => 'Some Name', 'name !=' => 'Some Name', 'name <>' => 'Some Name', ...
     *          - resemble: 'name ~' => 'some náme'
     *          - resemble not: 'name !~' => 'some náme', 'name ~!' => 'some náme'
     *          - greater than, less than: 'count >' => 3, 'count >=' => 3, 'count <' => 3, ...
     *      When resemble operator is used then trimmed slugized forms of values are compared.
     * 
     * @param array $options Following are available:
     *      - 'strictCompare' (bool) If TRUE then equations ('=' and '!=') are evaluated with 
     *          strict equation (===, !==). Defaults to FALSE. 
     *      - 'flatData' (bool) If TRUE then data are considered to be flat, e.g. array('User.id' => 5, ...). 
     *          If FALSE data are considered to be inflated, e.g. array('User' => array('id' => 5, ...)).
     *          Defaults to FALSE. 
     *      
     * @return bool
     */
    static public function dataConditions($data, $conditions, $options = array()) {
        $defaults = array(
            'strictCompare' => false,
            'flatData' => false,
        );
        $options = array_merge($defaults, $options);
        $orOperands = array(true);
        $orCount = 0;
        $logicalOperator = '';
        $conditions = (array)$conditions;
        foreach ($conditions as $field => $value) {
            if (
                is_int($field)
                || ($negation = ($field === 'NOT' || $field === '!'))
            ) {
                // check for possible logical operator specificaton (negation is ignored here)
                if ($value === 'OR' || $value == '||') {
                    $logicalOperator = 'OR';
                    continue;
                }
                elseif ($value === 'AND' || $value == '&&') {
                    $logicalOperator = 'AND';
                    continue;
                }
                elseif (is_array($value)) {
                    if (empty($value)) {
                        continue;
                    } 
                    $conditionResult = self::dataConditions($data, $value, $options);
                    if ($negation) {
                        $conditionResult = !$conditionResult;
                    }
                }
            }
            else {
                // resolve comparison operator
                $field = trim($field);
                if (
                    substr($field, -2) === '>='
                    || substr($field, -2) === '=>'
                ) {
                    $field = rtrim(substr($field, 0, -2));
                    $operator = '>=';
                }
                elseif (
                    substr($field, -2) === '<='
                    || substr($field, -2) === '=<'
                ) {
                    $field = rtrim(substr($field, 0, -2));
                    $operator = '<=';
                }
                elseif (
                    substr($field, -2) === '!~'
                ) {
                    $field = rtrim(substr($field, 0, -2));
                    $operator = '!~';
                }
                elseif (
                    substr($field, -2) === '<>'
                    || substr($field, -2) === '!='
                ) {
                    $field = rtrim(substr($field, 0, -2));
                    $operator = '!=';
                }
                elseif (substr($field, -1) === '!') {
                    $field = rtrim(substr($field, 0, -1));
                    $operator = '!=';
                }
                elseif (substr($field, -1) === '>') {
                    $field = rtrim(substr($field, 0, -1));
                    $operator = '>';
                }
                elseif (substr($field, -1) === '<') {
                    $field = rtrim(substr($field, 0, -1));
                    $operator = '<';
                }
                elseif (substr($field, -1) === '~') {
                    $field = rtrim(substr($field, 0, -1));
                    $operator = '~';
                }
                elseif (substr($field, -1) === '=') {
                    $field = rtrim(substr($field, 0, -1));
                    $operator = '=';
                }
                else {
                    $operator = '=';
                }
                // get field value in data
                if ($options['flatData']) {
                    $fieldValue = isset($data[$field]) ? $data[$field] : null;
                }
                else {
                    $fieldValue = Arr::getPath($data, $field);
                }
                // resolve actual condition result
                if ($operator === '=') {   
                    if (!is_array($value)) {
                        $conditionResult = !$options['strictCompare'] && $fieldValue == $value
                            || $options['strictCompare'] && $fieldValue === $value;
                    }
                    else {
                        $conditionResult = in_array($fieldValue, $value, $options['strictCompare']);
                    }
                }
                elseif ($operator === '!=') {
                    if (!is_array($value)) {
                        $conditionResult = !$options['strictCompare'] && $fieldValue != $value
                            || $options['strictCompare'] && $fieldValue !== $value;
                    }
                    else {
                        $conditionResult = !in_array($fieldValue, $value, $options['strictCompare']);
                    }
                }
                elseif ($operator === '~') {
                    $conditionResult = Str::slugize(trim($fieldValue)) === Str::slugize(trim($value));
                }
                elseif ($operator === '!~') {
                    $conditionResult = Str::slugize(trim($fieldValue)) !== Str::slugize(trim($value));
                }
                elseif ($operator === '>=') {
                    $conditionResult = $fieldValue >= $value;
                }
                elseif ($operator === '<=') {
                    $conditionResult = $fieldValue <= $value;
                }
                elseif ($operator === '>') {
                    $conditionResult = $fieldValue > $value;
                }
                elseif ($operator === '<') {
                    $conditionResult = $fieldValue < $value;
                }
            }
            // process the logical operator
            // - logical OR
            if (
                $logicalOperator === 'OR'
                && $field !== 0 // ignore OR as very first item in conditions array (AND is ignored implicitly)
            ) {
                $orCount++;
                $orOperands[$orCount] = $conditionResult;
            }
            // - logical AND
            else {
                $orOperands[$orCount] = $orOperands[$orCount] && $conditionResult;
            }
            // reset logical operator
            $logicalOperator = '';
        }
        return in_array(true, $orOperands);
    }

    /**
     * Is the provided url absolute or not?
     * 
     * @param string $url
     * 
     * @return bool TRUE if the provided url is absolute. FALSE otherwise
     */
    static public function absoluteUrl($url) {
        return (
            substr($url, 0, 7) === 'http://'
            || substr($url, 0, 8) === 'https://'
        );
    }
    
    /**
     * Is the provided url external or not?
     * 
     * @param string $url
     * 
     * @return bool TRUE if the provided url (even absolute) points out of the site. FALSE otherwise
     */
    static public function externalUrl($url) {
        // resolve domain name
        static $domainName = null;
        static $domainLength = null;
        if ($domainName === null) {
            if (substr(App::$urlBaseWithRoot, 0, 7) === 'http://') {
                $domainName = substr(App::$urlBaseWithRoot, 7);
            }
            elseif (substr(App::$urlBaseWithRoot, 0, 8) === 'https://') {
                $domainName = substr(App::$urlBaseWithRoot, 8);
            }
            else {
                $domainName = App::$urlBaseWithRoot;
            }
            $domainLength = strlen($domainName);
        }
        return (
            substr($url, 0, 7) === 'http://'
            && substr($url, 7, $domainLength) !== $domainName
            || 
            substr($url, 0, 8) === 'https://'
            && substr($url, 8, $domainLength) !== $domainName
        );
    }
    
    /**
     * Checks if provided $value is regular expression
     * 
     * @param string $value
     * @param array $options Following are available:
     *      - 'delimiter' (string) Regex delimiter character. Defaults to '/'.
     * 
     * @return bool
     */
    static public function regex($value, $options = array()) {
        $defaults = array(
            'delimiter' => '/',
        );
        $options = array_merge($defaults, $options);
        return is_string($value) 
            && $value[0] === $options['delimiter'] 
            && preg_match('/\\'. $options['delimiter'] . '[imsxeADSUXJu]*$/', $value);
    }
    
    /**
     * Checks if provided value is anonymous function.
     * 
     * NOTE: It is recommended to use more general Validate::callableFunction()
     * which accepts both anonymous functions and callables.
     * 
     * ATTENTION: PHP confuses (mixes together) anonymous functions and closures
     * as both are created as instances of \Closure class. But closure is just a
     * special case of anonymous function use (when it is closed in some scope but
     * used out of this scope).
     * 
     * @param mixed $value
     * 
     * @return bool
     */
    static public function anonymousFunction($value) {
        // see http://stackoverflow.com/questions/7101469/determining-if-a-variable-is-a-valid-closure-in-php
        return $value instanceof \Closure;
//        // see http://php.net/manual/en/reflectionfunctionabstract.isclosure.php
//        $r = new ReflectionFunction($value);
//        return $r->isClosure();
    }
    
    /**
     * Checks if provided value is a callable method of class/object or an annonymous function.
     * Globally defined functions (provided as strings, e.g. 'date') are not considered (returns FALSE).
     * 
     * @param mixed $value
     * @param array $options Following are available:
     *      - 'allowGlobalFunctions' (bool) If TRUE then also globally defined 
     *          functions are considered. Defaults to FALSE.
     * 
     * @return bool
     */
    static public function callableFunction($value, $options = array()) {
        return is_callable($value) && (
            !is_string($value) 
            || !empty($options['allowGlobalFunctions'])
        );
    }
    
    /**
     * Checks if provided value is a valid HTML code, it means if all paired tags 
     * have corresponding start and end pairs.
     * 
     * @param mixed $value
     * @param array $options Following are available:
     *      - 'allowTags' (bool) Plain array of allowed tag names. Defaults to NULL.
     *      - 'avoidTags' (bool) Plain array of forbiden tag names. Defaults to NULL.
     * 
     * @return bool
     */
    static public function html($value, $options = array()) {
        $options = array_merge(array(
            'allowTags' => null,
            'avoidTags' => null,
        ), $options);
        foreach (array('allowTags', 'avoidTags') as $optionName) {            
            if ($options[$optionName]) {
                $options[$optionName] = (array)$options[$optionName];
                $tmp = array();
                foreach($options[$optionName] as $tagName) {
                    $tagName = strtolower(trim($tagName));
                    $tmp[$tagName] = true;
                }
                $options[$optionName] = $tmp;
            }
        }
        $matches = null;
        if (preg_match_all('/<(\/?)([a-z]+)(?:>|[^a-z>][^>]*>)/i', $value, $matches, PREG_SET_ORDER)) {
            $emptyTags = array(
                'area' => true,
                'base' => true,
                'br' => true,
                'col' => true,
                'embed' => true,
                'hr' => true,
                'img' => true,
                'input' => true,
                'keygen' => true, // HTML 5.2 Draft removed
                'link' => true,
                'meta' => true,
                'param' => true,
                'source' => true,
                'track' => true,
                'wbr' => true,
            );
            $tags = array(); // {tagName} => {startVsEndBalance}
            foreach($matches as $match) {
                $isEndTag = !empty($match[1]);
                $tagName = strtolower($match[2]);
                if (
                    $options['allowTags']
                    && empty($options['allowTags'][$tagName])
                    ||
                    $options['avoidTags']
                    && !empty($options['avoidTags'][$tagName])
                ) {
                    return false;
                }
                if (isset($emptyTags[$tagName])) {
                    continue;
                }
                if (!isset($tags[$tagName])) {
                    $tags[$tagName] = 0;
                }
                if ($isEndTag) {
                    $tags[$tagName]--;
                }
                else {
                    $tags[$tagName]++;
                }
                // there is tag end without its start
                if ($tags[$tagName] < 0) {
                    return false;
                }
            }
            $tags = array_filter($tags);
            if ($tags) {
                return false;
            }
        }
        return true;
    }
}
