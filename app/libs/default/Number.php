<?php
class Number {
    
    /**
     * Rounds a float number up for specified precision
     * 
     * @param float $value
     * @param int $precision
     * @return float 
     */
    static public function roundUp($value, $precision) {
////this implementation fails, e.g. Number::roundUp(2.22, 2) returns 2.23!  
//        $pow = pow(10, $precision);
//        return (ceil($pow * $value)) / $pow;
        
        $value = (float)$value;
        $precision = (int)$precision;
        if ($precision < 0) { 
            $precision = 0;
        }
        $decPointPosition = strpos($value, '.');
        if ($decPointPosition === false) { 
            return $value;
        }
        $floorValue = (float)substr($value, 0, $decPointPosition + $precision + 1);
        $followingDecimals = (int)substr($value, $decPointPosition + $precision + 1);
        if ($followingDecimals) {
            $ceilValue = $floorValue + pow(10, -$precision);
        }
        else {
            $ceilValue = $floorValue;
        }
        return $ceilValue;                
    } 
    
    /**
     * Rounds a float number down for specified precision
     * 
     * @param float $value
     * @param int $precision
     * @return float 
     */
    static public function roundDown($value, $precision) {
////this implementation fails, e.g Number::roundDown(2.05, 2) returns 2.04!  
//        $pow = pow(10, $precision);
//        return (floor($pow * $value)) / $pow;
        
        $value = (float)$value;
        $precision = (int)$precision;
        if ($precision < 0) { 
            $precision = 0;
        }
        $decPointPosition = strpos($value, '.');
        if ($decPointPosition === false) { 
            return $value;
        }
        return (float)(substr($value, 0, $decPointPosition + $precision + 1));        
    } 
                        
    /**
     * Applies given rate (percents) to provided number.
     * 
     * @param float $number 
     * @param float $rate Rate in percents, e.g. 20 or 65.1
     * @param int|bool $precision Integer number of result decimal precision. If FALSE
     *          then no rounding is made. Defaults to 2. 
     * 
     * @return float Result rounded to provided precision.
     */
    static public function applyRate($number, $rate, $precision = 2) {
        $number = (float)$number * ((float)$rate / 100);
        if ($precision !== false) {
            $number = round($number, $precision);
        }
        return $number;
    }

    /**
     * Calculates price increased by the applicable tax.
     * 
     * @param float $price 
     * @param float $taxRate Tax rate in percents, e.g. 20
     * @param int|bool $precision Integer number of result decimal precision. If FALSE
     *          then no rounding is made. Defaults to 2. 
     * 
     * @return float Taxed price rounded to provided precision.
     */
    static public function getTaxedPrice($price, $taxRate, $precision = 2) {
        if (empty($taxRate)) {
            $taxedPrice = (float)$price;
        }
        else {
            $taxedPrice = (float)$price * ((float)$taxRate / 100 + 1);
        }
        if ($precision !== false) {
            $taxedPrice = round($taxedPrice, $precision);
        }
        return $taxedPrice;
    }

    /**
     * Calculates price decreased by the applicable tax.
     * 
     * @param float $price 
     * @param float $taxRate Tax rate in percents, e.g. 20
     * @param int|bool $precision Integer number of result decimal precision. If FALSE
     *          then no rounding is made. Defaults to 2. 
     * 
     * @return float Taxless price rounded to provided precision.
     */
    static public function getTaxlessPrice($price, $taxRate, $precision = 2) {
        if (empty($taxRate)) {
            $taxlessPrice = (float)$price;
        }
        else {
            $taxlessPrice = (float)$price * 100  / ((float)$taxRate + 100);
        }
        if ($precision !== false) {
            $taxlessPrice = round($taxlessPrice, $precision);
        }
        return $taxlessPrice;
    }    
    
    /**
     * Removes from number its trailing zero decimals.
     * 
     * @param mixed $number Number to remove trailing zero decimals from. If $number 
     *          is 25.000 then after removing it is 25, but if the $number is 25.010 
     *          then after removing it is 25.01
     * 
     * @return string String version of number
     */
    static public function removeTrailingZeroDecimals($number) {
        $number = (string)$number;
        if (strpos($number, '.') !== false) {
            $number = rtrim(rtrim($number, '0'), '.');
        }
        return $number;
    }
    
    /**
     * Converts provided integer number to Roman letter number notation, e.g. 8 -> VIII
     * 
     * @param int|string $number Integer number or string
     * 
     * @return string
     */
////This version generated watnings
//    static public function convertToRoman($number) {
//        $c='IVXLCDM'; 
//        for($a = 5, $b = $s = ''; $number; $b++, $a ^= 7) {
//            for($o=$number%$a, $number = $number/$a^0; $o--; $s = $c[$o > 2 ? $b + $number - ($number &= -2) + $o = 1 : $b] . $s) {}
//        }
//        return $s;
//    }
    static public function convertToRoman($number) {
        $number = (int)$number;
        $table = array('M'=>1000, 'CM'=>900, 'D'=>500, 'CD'=>400, 'C'=>100, 'XC'=>90, 'L'=>50, 'XL'=>40, 'X'=>10, 'IX'=>9, 'V'=>5, 'IV'=>4, 'I'=>1); 
        $return = '';
        while($number > 0) { 
            foreach($table as $rom => $arb){ 
                if($number >= $arb) { 
                    $number -= $arb; 
                    $return .= $rom; 
                    break; 
                } 
            } 
        }
        return $return;
    }
    
    /**
     * Converts provided integer to alphabetic sequence. If the integer is negative then
     * the alphabetic sequence start also by '-'
     * 
     * @param int $integer
     * 
     * @return string
     */
    static public function convertIntegerToAlphabetic($integer) {
        static $letters = array();
        if (empty($letters)) {
            for ($i = 1; $i <= 25; $i++) {
                $letters[$i] = chr(96 + $i);
            }
            $letters[0] = chr(122); // z = 0 (zero :)
        }
        $negative = $integer < 0;
        $integer = abs($integer);
        $alphabetic = '';
        do {
            $modulo = $integer % 26;
            $alphabetic = $letters[$modulo] . $alphabetic;
            $times = floor($integer / 26);
        } while (
            $times > 0
            && ($integer = $times)
        );
        if ($negative) {
            $alphabetic = '-' . $alphabetic;
        }
        return $alphabetic;
    }
                
    /**
     * Adjusts provided $number by specified $adjustment.
     * This method is usefull for dynamicaly specified adjustment.
     * 
     * @param integer|float $number Number to be adjusted. It is up to user to 
     *          do proper typecasting to integer or float before its value is passed to method.
     * @param string|array $adjustment Single adjustment case string like '[{condition}]{operator}{number}', 
     *          e.g. '+6.5', '-4', '*0.23', '>20:/5', '<6:=6'. Or single adjustment case
     *          array containing keys 'adjustmentOperator', 'adjustmentNumber', 'conditionOperator' and 
     *          'conditionNumber', e.g. array('adjustmentOperator' => '+', 'adjustmentNumber' => 6.5) or
     *          array('conditionOperator' = '>', 'conditionNumber' => 20, 'adjustmentOperator' => '+', 'adjustmentNumber' => 5).
     *          Keys 'conditionOperator' and 'conditionNumber' are optional.
     *          Unspecified 'adjustmentOperator' defaults to '='. The 'adjustmentNumber' is
     *          typecasted either to integer or float according to result of Validate::intNumber().
     *          You can specify many adjustment cases. Many adjustment cases string
     *          '>5:+2;>15:*3;>18:/5'. Many adjustment cases array is an unassociative
     *          array containing many single adjustment arrays. The $number is adjusted
     *          by first adjustment whose condition returns TRUE for provided $number.
     *          It means always only one conditioned adjustment is applied. If there
     *          are many unconditioned adjustments then all of them are applied.
     * @param array $options Following are available:
     *      - 'applyCasesInReverseOrder' (bool) Defaults to FALSE.
     *      - 'conditionSeparator' (string) Any non-whitespace character to define 
     *          separator of $adjustment condition. Defaults to ':'. Applies only
     *          if $adjustment is defined as string
     *      - 'caseSeparator' (string) Any non-whitespace character to define 
     *          separator of $adjustment cases. Defaults to ';'. Applies only
     *          if $adjustment is defined as string
     *      - 'comparisonNumber' (integer|float) The number used for adjustment
     *          conditions comparisons. It is considered only if there are conditional
     *          adjustments defined. It is up to user to do proper typecasting 
     *          to integer or float before its value is passed to method. Defaults to $number.
     *      - 'exceptionOnInvalidCondition' (bool) Defaults to FALSE.
     * 
     * 
     * @return integer|float
     */
    static public function adjust($number, $adjustment, $options = array()) {
        $defaults = array(
            'applyCasesInReverseOrder' => false,
            'conditionSeparator' => ':',
            'caseSeparator' => ';',
            'comparisonNumber' => $number,
            'exceptionOnInvalidCondition' => false,
        );
        $options = array_merge($defaults, $options);
        $adjustmentDefaults = array(
            'conditionOperator' => null,
            'conditionNumber' => null,
            'adjustmentOperator' => null,
            'adjustmentNumber' => 0,
        );
        $comparisonOperators = array(
            '=' => '=', '==' => '=', '===' => '=', '>' => '>', '!>' => '<=', '>!' => '<=',
            '>=' => '>=', '!>=' => '<', '>!=' => '<', '>=!' => '<', '=>' => '>=', '!=>' => '<',
            '=!>' => '<', '=>!' => '<', '<' => '<', '!<' => '>=', '<!' => '>=', '<=' => '<=',
            '!<=' => '>', '<!=' => '>', '<=!' => '>', '=<' => '<=', '!=<' => '>', '=!<' => '>',
            '=<!' => '>', '!=' => '!=', '=!' => '!=', '!' => '!=',
        );
        if (!is_array($adjustment)) {

            // BEGINING OF FUNCTION
            $parseSingleAdjustment = function($adjustment) use ($options, $adjustmentDefaults, $comparisonOperators) {
                $operators = array(
                    '+' => true,
                    '-' => true,
                    '*' => true,
                    '/' => true,
                    '=' => true,
                );
                $tmp = $adjustmentDefaults;
                if (strpos($adjustment, $options['conditionSeparator'])) {
                    $adjustmentParts = explode($options['conditionSeparator'], $adjustment);
                    if (count($adjustmentParts) > 2) {
                        if ($options['exceptionOnInvalidCondition']) {
                            throw new Exception(__e(__FILE__, 'Invalid adjustment conditions "%s"', $adjustment));
                        }
                        return false;
                    }
                    $condition = $adjustmentParts[0];
                    if (
                        preg_match_all('/[<=>!]+/', $condition, $matches) !== 1
                        || !isset($comparisonOperators[$matches[0][0]])
                    ) {
                        if ($options['exceptionOnInvalidCondition']) {
                            throw new Exception(__e(__FILE__, 'Invalid adjustment conditions "%s"', $adjustment));
                        }
                        return false;
                    }
                    $tmp['conditionOperator'] = $comparisonOperators[$matches[0][0]];
                    $tmp['conditionNumber'] = str_replace($matches[0][0], '', $condition);
                    $adjustment = $adjustmentParts[1];
                }
                $operator = substr($adjustment, 0, 1);
                if (isset($operators[$operator])) {
                    $tmp['adjustmentNumber'] = substr($adjustment, 1);
                    $tmp['adjustmentOperator'] = $operator;
                }
                else {
                    $tmp['adjustmentNumber'] = $adjustment;
                }
                $adjustment = $tmp;
                return $adjustment;
            };            
            // END OF FUNCTION
            
            // parse $adjustment
            $adjustment = preg_replace('/\s/', '', (string) $adjustment);
            if (strpos($adjustment, $options['caseSeparator']) !== false) {
                $cases = explode($options['caseSeparator'], $adjustment);
                $tmp = array();
                foreach ($cases as $caseAdjustement) {
                    if (!($result = $parseSingleAdjustment($caseAdjustement))) {
                        continue;
                    }
                    $tmp[] = $result;
                }
                $adjustment = $tmp;
            }
            else {
                $adjustment = $parseSingleAdjustment($adjustment);
            }
        }
        
        // normalize the single adjustment to case of many adjustments with one case
        if (Validate::assocArray($adjustment)) {
            $adjustment = array($adjustment);
        }
        if (!empty($options['applyCasesInReverseOrder'])) {
            $adjustment = array_reverse($adjustment);
        }
        foreach ($adjustment as $case) {
            $case = array_merge($adjustmentDefaults, $case);
            if (!empty($case['conditionOperator'])) {
                if (
                    !isset($comparisonOperators[$case['conditionOperator']])
                ) {
                    if ($options['exceptionOnInvalidCondition']) {
                        throw new Exception(__e(__FILE__, 'Invalid adjustment operator "%s"', $case['conditionOperator']));
                    }
                    continue;
                }
                $conditionOperator = $comparisonOperators[$case['conditionOperator']];
                if (Validate::intNumber($case['conditionNumber'])) {
                    $conditionNumber = (int)$case['conditionNumber'];
                }
                else {
                    $conditionNumber = (float)$case['conditionNumber'];
                }
                if (
                    ! (
                        $conditionOperator === '='
                        && $options['comparisonNumber'] == $conditionNumber
                        ||
                        $conditionOperator === '>'
                        && $options['comparisonNumber'] > $conditionNumber
                        ||
                        $conditionOperator === '>='
                        && $options['comparisonNumber'] >= $conditionNumber
                        ||
                        $conditionOperator === '<'
                        && $options['comparisonNumber'] < $conditionNumber
                        ||
                        $conditionOperator === '<='
                        && $options['comparisonNumber'] <= $conditionNumber
                        ||
                        $conditionOperator === '!='
                        && $options['comparisonNumber'] != $conditionNumber
                    )
                ) {
                    continue;
                }
            }
            if (Validate::intNumber($case['adjustmentNumber'])) {
                $case['adjustmentNumber'] = (int)$case['adjustmentNumber'];
            }
            else {
                $case['adjustmentNumber'] = (float)$case['adjustmentNumber'];
            }
            if (empty($case['adjustmentOperator'])) {
                $case['adjustmentOperator'] = '=';   
            }
            switch ($case['adjustmentOperator']) {
                case '+':
                    $number = $number + $case['adjustmentNumber'];
                    break;
                case '-':
                    $number = $number - $case['adjustmentNumber'];
                    break;
                case '*':
                    $number = $number * $case['adjustmentNumber'];
                    break;
                case '/':
                    $number = $number / $case['adjustmentNumber'];
                    break;
                case '=':
                    $number = $case['adjustmentNumber'];
                    break;
            }
            break;
        }
        return $number;
    }
    
    /**
     * Converts length provided in $pixels to length in millimeters
     * 
     * @param int $pixels Length in pixels
     * @param int $dpi Dots per inch (number of pixels to fill 1 inch), e.g. 300
     * 
     * @return float Length in millimeters
     */
    static public function convertPixelsToMillimeters($pixels, $dpi) {
        return $pixels / $dpi * 25.4; // * 25.3999991872
    }
    
    /**
     * Converts length provided in $millimeters to length in pixels
     * 
     * @param float $millimeters Length in milimeters
     * @param int $dpi Dots per inch (number of pixels to fill 1 inch), e.g. 300
     * 
     * @return int Length in pixels
     */
    static public function convertMillimetersToPixels($millimeters, $dpi) {
        return round($millimeters * $dpi / 25.4); // * 25.3999991872
    }
    
    /**
     * Returns array of parsed dimensions like:
     * 
     *      array(
     *          'width' => ...,     // [mm]
     *          'height' => ...,    // [mm]
     *          'length' => ...,    // [mm]
     *      )
     * 
     * Returned dimensions are in millimeters.
     * 
     * @param string $dimensions E.g. '25,5x16 cm'
     * @param array $options Fallowing are available:
     *      - 'thousandsSeparator' (string) Defaults to App::getActualThousandsSeparator().
     *      - 'decimalPoint' (string) Defaults to App::getActualDecimalPoint().
     * 
     * @return array The above described array
     */
    static public function parseDimensions($dimensions, $options = array()) {
        $options = array_merge(array(
            'thousandsSeparator' => App::getActualThousandsSeparator(),
            'decimalPoint' => App::getActualDecimalPoint(),
        ), $options);
        $parsedDimensions = array(
            'width' => null,
            'height' => null,
            'length' => null,
        );
        $dimensionNames = array(
            0 => 'width',
            1 => 'height',
            2 => 'length',
        );
        $unitsRates = array(
            'm' => 1000,
            'cm' => 10,
            'mm' => 1,
        );
        if (empty($dimensions)) {
            return $parsedDimensions;
        }
        // normalize dimmensions
        if ($options['thousandsSeparator'] !== '') {
            $dimensions = str_replace($options['thousandsSeparator'], '', $dimensions);
        }
        if ($options['decimalPoint'] !== '.') {
            $dimensions = str_replace($options['decimalPoint'], '.', $dimensions);
        }
        $dimensions = str_replace(' ', '', $dimensions);
        $dimensions = str_replace(',', '.', $dimensions);
        $dimensions = strtolower($dimensions);
        $dimensions = preg_replace('/[^0-9\. ]+/', ' $0 ', $dimensions);
        $dimensions = preg_replace('/([^0-9\.])\s+([^0-9\.])/', '$1$2', $dimensions);
        $dimensions = preg_replace('/\s*x\s*/', 'x', $dimensions);
        $dimensions = preg_replace('/\s+/', ' ', $dimensions);
        $dimensions = trim($dimensions);
        $dimensionsParts = Str::explode('x', $dimensions);
        $defaultUnits = 'mm';
        foreach ($dimensionsParts as $i => $dimensionsPart) {
            $dimensionParts = Str::explode(' ', $dimensionsPart);
            $value = $dimensionParts[0];
            $units = null;
            if (
                count($dimensionParts) > 1
                && (
                    $dimensionParts[1] === 'm'
                    || $dimensionParts[1] === 'cm'
                    || $dimensionParts[1] === 'mm'
                )
            ) {
                $units = $dimensionParts[1];
            }
            if ($units) {
                $defaultUnits = $units;
            }
            if ($i > 2) {
                // keep iterating till the end of parts for sake of default units detection
                continue;
            }
            $name = $dimensionNames[$i];
            $parsedDimensions[$name] = array(
                'value' => $value,
                'units' => $units,
            );
        }
        foreach ($parsedDimensions as &$parsedDimension) {
            if ($parsedDimension) {
                $units = $parsedDimension['units'];
                if (!$units) {
                    $units = $defaultUnits;
                }
                $parsedDimension = (float)$parsedDimension['value'] * $unitsRates[$units];
            }
        }
        unset($parsedDimension);
        
        return $parsedDimensions;
    }
    
    /**
     * Compares two provided numbers in the same manner as spaceship operator.
     * 
     * This is especially usefull to check for equalty of float numbers as not all 
     * of them can be represented precisely using finite number of bites and not
     * all finite decimal number sin base 10 are also finite in base 2 (it means
     * in binary representation).
     * See:
     *  - https://www.php.net/manual/en/language.types.float.php
     *  - https://stackoverflow.com/q/3148937/1245149
     *  - https://stackoverflow.com/q/21895756/1245149
     * 
     * @param float|int|string $number1 Float or integer number or a string representing float or integer number.
     * @param float|int|string $number2 Float or integer number or a string representing float or integer number.
     * @param float $epsilon Optional. The largest difference between $number1 and $number2
     *          to consider them as the same. Defaults to PHP_FLOAT_EPSILON.
     * 
     * @return int Returns -1, 0 or 1 when $number1 is respectively less than, equal to, 
     *      or greater than $number2
     */
    static public function compare($number1, $number2, $epsilon = PHP_FLOAT_EPSILON) {
        $difference = (float)$number1 - (float)$number2;
        if (
            empty($epsilon)
            && empty($difference)
            ||
            abs($difference) < $epsilon
        ) {
            return 0;
        }
        return $difference < 0 ? -1 : 1;
    }    
}
