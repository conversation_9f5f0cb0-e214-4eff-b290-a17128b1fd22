<?php 
/**
 * Kind of "Html helper" class. 
 * Put here all reusable methods for creating of html code.
 * 
 * For generation of atypic pieces of html use rather elements 
 */
class Html {
    
    /**
     * Used by Html::startCapture(), Html::endCapture() to control/validate 
     * sequence of mentioned methods calls.
     * 
     * @var int
     */
    static protected $openedCapturesCount = 0;
    
    static public function getPropertyOpenedCapturesCount() {
        return self::$openedCapturesCount;
    }
    
    /**
     * Allows in cooperation with Html::endCapture() to capture html code 
     * into string. 
     * 
     * The common pattern to use the method is following:
     * 
     *      < ?php Html::startCapture() ? >
     *      < div>Some html code< /div>
     *      < ?php $html = Html::endCapture() ? >
     * 
     * ATTENTION: Nesting of capturing blocks is not allowed (see also App::start/endCss/JsCapture()).
     * 
     * @throws Exception on capturing blocks nesting (see also App::start/endCss/JsCapture()).
     */
    static public function startCapture() {
        self::$openedCapturesCount++;
        ob_start();
    }
    
    /**
     * Allows in cooperation with Html::startCapture() to capture html code 
     * into string.
     * 
     * The common pattern to use the method is following:
     * 
     *      < ?php Html::startCapture() ? >
     *      < div>Some html code< /div>
     *      < ?php $html = Html::endCapture() ? >
     * 
     * ATTENTION: Nesting of capturing blocks is not allowed (see also App::start/endCss/JsCapture()).
     * 
     * @return string Captured html code
     * @throws Exception on capturing blocks nesting (see also App::start/endCss/JsCapture()).
     */
    static public function endCapture() {
        if (self::$openedCapturesCount === 0) {
            throw new Exception('You cannot call Html::endCapture() without calling at first Html::startCapture()');
        }
        $html = ob_get_contents();
        ob_end_clean();
        self::$openedCapturesCount -= 1;
        return $html;
    }
    
    /**
     * Generates links (<link> tags) for provided css $files
     * 
     * @param array $files Array of .css filenames to generate <link> tags for. 
     *      Filename must be specified with full path starting from app root (e.g. '/app/js/libs/basic.css') 
     *      or with external link starting either by 'http://' or 'https://' or '//'.
     *      To specify attributes of <link> tag use syntax: array('my.css' => array('disabled' => true, ...)).
     *      To specify media attribute a shorthand array('my.css' => 'print') can be used
     *      instead of array('my.css' => array('media' => 'print')).
     * @param array $options Following are available:
     *      - 'timestamp' (bool) If TRUE then timestamp GET parameter is attached to 
     *          file, to avoid use of cached file after it has been changed. Defaults
     *          to !ON_LOCALHOST, it means timestamps are used only on production
     *          server (not on localhost).
     *      - 'normalize' (bool) If TRUE then $files can be provided as described here
     *          above. If FALSE then they must be already (pre)normalized to form
     *          {file} => {attributes|FALSE}. Defaults to TRUE.
     * 
     * @return string
     */
    static public function cssLinks($files, $options = array()) {
        $defaults = array(
            'timestamp' => !ON_LOCALHOST,
            'normalize' => true,
        );
        $options = array_merge($defaults, $options);
        // create html for css links
        $html = '';
        foreach ($files as $key => $value) {
            if (!$options['normalize']) {
                $file = $key; 
                $attributes = $value;
            }
            elseif (is_int($key)) {
                $file = $value; 
                $attributes = false;
            }
            elseif (is_array($value)) {
                $file = $key; 
                $attributes = $value;
            }
            else {
                $file = $key; 
                $attributes = array('media' => $value);
            }
            // add timestamp and url root
            if (
                substr($file, 0, 2) !== '//'
                && substr($file, 0, 7) !== 'http://' 
                && substr($file, 0, 8) !== 'https://'
            ) {
                $ts = '';
                $absolutFilePath = File::getAbsolutePath(File::normalizeDS($file));
                if ($options['timestamp'] && is_readable($absolutFilePath)) {
                    $ts = '?ts=' . filemtime($absolutFilePath);
                }
                $file = URL_ROOT . $file . $ts;
            }
            $attributesHtml = ' ';
            if ($attributes) {
                foreach ($attributes as $k => $v) {
                    if (is_int($k)) {
                        $attributesHtml .= $v . ' ';
                    }
                    else{
                        $attributesHtml .= $k . '="' . $v . '" ';
                    }
                }
            }
            $attributesHtml = rtrim($attributesHtml);
            $html .= PHP_EOL . '<link rel="stylesheet"  type="text/css" href="' . $file . '"' . $attributesHtml . '/>';
        }
        return $html;
    }
    
    /**
     * Returns one css code compiled from provided $cssCodes
     * 
     * @param array $cssCodes Plain array of css codes possibly containing wrapping <style>
     *      tags (e.g. when captured by Html::startCapture() - Html::endCapture()).
     * @param array $options Following are available:
     *      - 'lastCssCodes' (array) Plain array of css codes possibly containing 
     *          wrapping <style> tags (e.g. when captured by Html::startCapture() - Html::endCapture()).
     *          These css codes are placed after $cssCodes. Defaults to empty array().
     *      - 'inStyleTags' (bool) If TRUE then css code is returned wrapped
     *          in <style> tags (but only if there is any code). If FALSE then pure css 
     *          code is returned without wrapping <style> tags. Defaults to TRUE.
     *      - 'normalize' (bool) If TRUE then $cssCodes and 'lastCssCodes' can be 
     *          provided as described here above. If FALSE then they must be already 
     *          (pre)normalized to form {cssCode} => TRUE. Defaults to TRUE.
     * 
     * @return string
     */
    public static function cssCode($cssCodes, $options = array()) {
        $defaults = array(
            'lastCssCodes' => array(),
            'inStyleTags' => true,
            'normalize' => true,
        );
        $options = array_merge($defaults, $options);
        if ($options['normalize']) {
            $cssCodes = array_flip($cssCodes);
            $options['lastCssCodes'] = array_flip($options['lastCssCodes']);
        }
        $code = '';
        $css = array_keys(array_merge($cssCodes, $options['lastCssCodes']));
        if (!empty($css)) {            
            // remove <style type="text/css">...</style> tags
            $css = preg_replace('/<\/?style(?:>|[^a-z>][^>]*>)/i', '', $css);
            // trim whitespaces
            $css = array_map('trim', $css);
            // remove empty items
            $css = array_filter($css);
            // create one block of css code
            $code = trim(implode(PHP_EOL, $css));
            if ($code && $options['inStyleTags']) {
                $code  = PHP_EOL . '<style type="text/css">' . PHP_EOL . $code . PHP_EOL . '</style>';
            }
        }
        return $code;
    }
        
    /**
     * Generates links (<script> tags) for provided js $files
     * 
     * @param array $files Array of .js filenames to generate <script> tags for. 
     *      Filename must be specified by full path starting from app root (e.g. '/app/js/vendors/jquery.min.js')
     *      or by external link starting either by 'http://' or 'https://' or '//'. 
     *      To specify attributes of <script> tag use syntax: 
     *      array('my.js' => array('async' => true, 'type' => 'module', ...)).
     *      To specify type attribute a shorthand array('my.js' => 'module') can be used
     *      instead of array('my.js' => array('type' => 'module')).
     * @param array $options Following are available:
     *      - 'timestamp' (bool) If TRUE then timestamp GET parameter is attached to 
     *          file, to avoid use of cached file after it has been changed. Defaults
     *          to !ON_LOCALHOST, it means timestamps are used only on production
     *          server (not on localhost).
     *      - 'normalize' (bool) If TRUE then $files can be provided as described here
     *          above. If FALSE then they must be already (pre)normalized to form
     *          {file} => {attributes|FALSE}. Defaults to TRUE.
     * 
     * @return string
     */
    static public function jsLinks($files, $options = array()) {
        $defaults = array(
            'timestamp' => !ON_LOCALHOST,
            'normalize' => true,
        );
        $options = array_merge($defaults, $options);
        // create html for js links
        $html = '';
        foreach ($files as $key => $value) {
            if (!$options['normalize']) {
                $file = $key; 
                $attributes = $value;
            }
            elseif (is_int($key)) {
                $file = $value; 
                $attributes = false;
            }
            elseif (is_array($value)) {
                $file = $key; 
                $attributes = $value;
            }
            else {
                $file = $key; 
                $attributes = array('type' => $value);
            }
            // add timestamp and url root
            if (
                substr($file, 0, 2) !== '//'
                && substr($file, 0, 7) !== 'http://' 
                && substr($file, 0, 8) !== 'https://'
            ) {
                $ts = '';
                $absolutFilePath = File::getAbsolutePath(File::normalizeDS($file));
                if ($options['timestamp'] && is_readable($absolutFilePath)) {
                    $ts = '?ts=' . filemtime($absolutFilePath);
                }
                $file = URL_ROOT . $file . $ts;
            }
            $attributes = array_merge(array(
                'type' => 'text/javascript',
                // just to place it as the second attribute,
                // its value is forced here below to $file
                'src' => null, 
            ), $attributes ? $attributes : array());
            $attributes['src'] = $file;
            $attributesHtml = ' ';
            foreach ($attributes as $k => $v) {
                if (is_int($k)) {
                    $attributesHtml .= $v . ' ';
                }
                else{
                    $attributesHtml .= $k . '="' . $v . '" ';
                }
            }
            $attributesHtml = rtrim($attributesHtml);
            $html .= PHP_EOL . '<script' . $attributesHtml . '></script>';
        }
        return $html;   
    }
    
    /**
     * Returns one js code compiled from provided $jsCodes
     * 
     * @param array $jsCodes Plain array of js codes possibly containing wrapping <script>
     *      tags (e.g. when captured by Html::startCapture() - Html::endCapture()).
     * @param array $options Following are available:
     *      - 'lastJsCodes' (array) Plain array of js codes possibly containing 
     *          wrapping <script> tags (e.g. when captured by Html::startCapture() - Html::endCapture()).
     *          These js codes are placed after $jsCodes. Defaults to empty array().
     *      - 'inScriptTags' (bool) If TRUE then js code is returned wrapped
     *          in <script> tags (but only if there is any code). If FALSE then pure js 
     *          code is returned without wrapping <script> tags. Defaults to TRUE.
     *      - 'normalize' (bool) If TRUE then $jsCodes and 'lastJsCodes' can be 
     *          provided as described here above. If FALSE then they must be already 
     *          (pre)normalized to form {jsCode} => TRUE. Defaults to TRUE.
     * 
     * @return string
     */
    public static function jsCode($jsCodes, $options = array()) {
        $defaults = array(
            'lastJsCodes' => array(),
            'inScriptTags' => true,
            'normalize' => true,
        );
        $options = array_merge($defaults, $options);
        if ($options['normalize']) {
            $jsCodes = array_flip($jsCodes);
            $options['lastJsCodes'] = array_flip($options['lastJsCodes']);
        }
        $code = '';
        $js = array_keys(array_merge($jsCodes, $options['lastJsCodes']));
        if (!empty($js)) {            
            // remove <scrit type="text/javascript">...</script> tags
            $js = preg_replace('/<\/?script(?:>|[^a-z>][^>]*>)/i', '', $js);
            // trim whitespaces
            $js = array_map('trim', $js);
            // remove empty items
            $js = array_filter($js);
            // create one block of js code
            $code = trim(implode(PHP_EOL, $js));
            if ($code && $options['inScriptTags']) {
                $code  = PHP_EOL . '<script type="text/javascript">' . PHP_EOL . $code . PHP_EOL . '</script>';
            }
        }
        return $code;
    }    
    
    /**
     * Parses responsive css value string into array.
     * 
     * E.g. string '1/3; 768px:0.5; 480px:100%' can represent responsive widths
     * and it is parsed into array like:
     * 
     *      array(
     *          // the default (if provided) value has no screen width
     *          0 => '33.333333333%',
     *          768 => '50%',
     *          480 => '100%',
     *      )
     * 
     * E.g. string 'transparent; 768px:rgba(255,255,255,0.5)' can represent responsive backgrounds
     * and it is parsed into array like:
     * 
     *      array(
     *          // the default (if provided) value has no screen width
     *          0 => 'transparent',
     *          768 => 'rgba(255,255,255,0.5)',
     *      )
     * 
     * @param string $responsiveValue Responsive value string, e.g. '1/3;768px:0.5;480px:100%'.
     *      Description of responsive value string syntax see in hint of the responsive_width 
     *      field in view ContentBlockColumnStart/admin_view. If provided string is invalid 
     *      then an exception is thrown. If an empty string is provided then an empty array()
     *      is returned.
     * @param array $options Following are available:
     *      - 'allowValues' (bool|string|array) If TRUE then any value is allowed. 
     *          If FALSE then no value is allowed (this can used together with 'allowNumericValues'
     *          option to allow only numeric values). To specify concrete values 
     *          you can provide a single value regex string or an array of value regex strings. 
     *          Defaults to TRUE. ATTENTION: In regex strings do not use delimiters! 
     *          They are set internaly to slashes '/', so if you use slash character 
     *          in your regex, you must to escape it. ATTENTION: Do logical OR of 
     *          'allowValues' and 'allowNumericValues' to get resulting allowed values.
     *      - 'allowNumericValues' (bool|string|array) If TRUE then any numeric value is allowed.
     *          If FALSE then numeric values are not allowed (still they can be allowed by 
     *          'allowValues' set to TRUE). If numeric values are allowed to have only 
     *          some units then specify them here as a single unit string or an array
     *          of unit strings, e.g. array('%', 'px', 'mm'). To allow values with 
     *          some specified units but also without units then use empty string '' item 
     *          in array, e.g. array('%', 'px', 'mm', ''). If '%' units are allowed 
     *          and values without unit not (there is no '' in array), then numeric 
     *          values without units are converted to percentage like: value * 100%.
     *          Defaults to FALSE, it means values are allowed only by 'allowValues' option.
     *          ATTENTION: Do logical OR of 'allowValues' and 'allowNumericValues' 
     *          to get resulting allowed values.
     *      - 'exceptionOnFailure' (bool) If TRUE then on failure an exception is thrown.
     *          If FALSE then invalid items are skipped. Defaults to FALSE.
     * 
     * @return array The above described array.
     * @throws Exception
     */
    static public function parseResponsiveCssValue($responsiveValue, $options = array()) {
        // ATTENTION: Keep this code in synchro with Utility::parseConditionedValue()
        $defaults = array(
            'allowValues' => true,
            'allowNumericValues' => false,
            'exceptionOnFailure' => false,
        );
        $options = array_merge($defaults, $options);
        // normalize option
        if (
            $options['allowNumericValues']
            && $options['allowNumericValues'] !== true
        ) {
            $options['allowNumericValues'] = (array)$options['allowNumericValues'];
            //$options['allowNumericValues'] = array_map('strtolower', array_map('trim', $options['allowNumericValues']));
            $options['allowNumericValues'] = array_flip($options['allowNumericValues']);
        }
        if (
            $options['allowValues']
            && $options['allowValues'] !== true
        ) {
            $options['allowValues'] = (array)$options['allowValues'];
            // convert to regex
            $options['allowValues'] = '/' . implode('|', $options['allowValues']) . '/i';
        }
        
        $parsedValue = array();
        $valuesParts = array_map('trim', Str::explode(';', $responsiveValue));
        $defaultValueIndex = null;
        foreach ($valuesParts as $i => $valuesPart) {
            if ($valuesPart === '') {
                if (!$options['exceptionOnFailure']) {
                    continue;
                }
                elseif ($i === 0) {
                    throw new Exception(__e(__FILE__, 'Prvá položka je prázdna, na začiatku je bodkočiarka navyše'));
                }
                elseif ($i === (count($valuesParts) - 1)) {
                    throw new Exception(__e(__FILE__, 'Posledná položka je prázdna, na konci je bodkočiarka navyše', $i + 1));
                }
                throw new Exception(__e(__FILE__, '%s. položka je prázdna, nasledujú dve bodkočiarky za sebou', $i + 1));
            }
            if (strpos($valuesPart, ':') === false) {
                if ($defaultValueIndex !== null) {
                    if (!$options['exceptionOnFailure']) {
                        continue;
                    }
                    throw new Exception(__e(
                        __FILE__, 
                        'Nesprávne zadaná %s. položka "%s". Zadajte širku obrazovky a za dvojbodkou hodnotu.', 
                        $i + 1,
                        $valuesPart
                    ));
                }
                $defaultValueIndex = $i;
                $screenWidth = null;
                $value = $valuesPart;
            }
            else {
                $valueParts = array_map('trim', explode(':', $valuesPart));
                $screenWidth = array_shift($valueParts);
                $value = implode(':', $valueParts);
            }
            // parse screen width
            if ($screenWidth !== null) {
                // screen can be provided only as integer optionally with 'px' units
                $screenWidthMatch = array();
                if (
                    !preg_match(
                        '/^([1-9][0-9]*)([^0-9]*)$/', 
                        $screenWidth, 
                        $screenWidthMatch
                    )
                ) {
                    if (!$options['exceptionOnFailure']) {
                        continue;
                    }
                    elseif (
                        strpos($screenWidth, ';') === false
                        && strpos($screenWidth, ',') !== false
                    ) {
                        throw new Exception(__e(
                            __FILE__, 
                            'Nesprávne zadaná šírka obrazovky "%s" v %s. položke "%s". Na oddelenie položiek sa používa bodkočiarka', 
                            $screenWidth, 
                            $i + 1,
                            $valuesPart
                        ));
                    }
                    throw new Exception(__e(
                        __FILE__, 
                        'Nesprávne zadaná šírka obrazovky "%s" v %s. položke "%s"', 
                        $screenWidth, 
                        $i + 1,
                        $valuesPart
                    ));
                }
                $screenWidthNumber = $screenWidthNormalizedNumber = $screenWidthMatch[1];
                $screenWidthUnits = $screenWidthMatch[2];
                $screenWidthNormalizedUnits = strtolower(trim($screenWidthUnits));
                if ($screenWidthNormalizedUnits === '') {
                    $screenWidthNormalizedUnits = 'px';
                    $screenWidthNumber = (float)$screenWidthNumber * 100;
                }
                if ($screenWidthNormalizedUnits !== 'px') {
                    if (!$options['exceptionOnFailure']) {
                        continue;
                    }
                    throw new Exception(__e(
                        __FILE__, 
                        'Šírka obrazovky "%s" v %s. položke "%s" ma nesprávne zadané jednotky "%s"', 
                        $screenWidth, 
                        $i + 1, 
                        $valuesPart,
                        $screenWidthUnits
                    ));
                }
                if ($screenWidthNormalizedNumber < 320) {
                    if (!$options['exceptionOnFailure']) {
                        continue;
                    }
                    throw new Exception(__e(
                        __FILE__, 
                        'Šírka obrazovky "%s" v %s. položke "%s" musí byť väčšia ako 320px', 
                        $screenWidth, 
                        $i + 1, 
                        $valuesPart,
                        $screenWidthUnits
                    ));
                }
                $screenWidth = $screenWidthNormalizedNumber;
            }
            // parse numeric value
            $numericValueSuccess = false;
            if ($options['allowNumericValues']) {
                // numeric value can be provided as '0.5', '50%', '50px' or with any 
                // other specified units
                $valueMatch = array();
                if (
                    preg_match(
                        '/^((?:[1-9][0-9]*\/[1-9][0-9]*)|(?:[0-9]+(?:\.[0-9]+)?))([^0-9\.\/]*)$/', 
                        $value, 
                        $valueMatch
                    )
                ) {
                    $numericValueSuccess = true;
                    $valueNumber = $valueNormalizedNumber = $valueMatch[1];
                    $isFraction = false;
                    if (strpos($valueNumber, '/')) {
                        $isFraction = true;
                        $valueNumberParts = explode('/', $valueNumber);
                        $valueNormalizedNumber = $valueNumberParts[0] / $valueNumberParts[1];
                    }
                    $valueUnits = $valueMatch[2];
                    $valueNormalizedUnits = strtolower(trim($valueUnits));
                    if (
                        $valueNormalizedUnits === ''
                        && isset($options['allowNumericValues']['%'])
                        && !isset($options['allowNumericValues'][''])
                    ) {
                        $valueNormalizedUnits = '%';
                        if ($valueNormalizedNumber > 1) {
                            if (!$options['exceptionOnFailure']) {
                                continue;
                            }
                            elseif ($isFraction) {
                                throw new Exception(__e(
                                    __FILE__, 
                                    'Nesprávne zadaná hodnota "%s" v %s. položke "%s", vysledná hodnota zlomku musí byť menšia alebo rovné 1', 
                                    $value, 
                                    $i + 1,
                                    $valuesPart
                                ));
                            }
                            throw new Exception(__e(
                                __FILE__, 
                                'Nesprávne zadaná hodnota "%s" v %s. položke "%s", číslo bez jednotiek musí byť menšie alebo rovné 1', 
                                $value, 
                                $i + 1,
                                $valuesPart
                            ));                        
                        }
                        $valueNormalizedNumber = (float)$valueNormalizedNumber * 100;
                    }
                    if (
                        $options['allowNumericValues'] === true
                        && $valueNormalizedUnits !== ''
                        || 
                        $options['allowNumericValues'] !== true //is array
                        && !isset($options['allowNumericValues'][$valueNormalizedUnits])
                    ) {
                        if (!$options['exceptionOnFailure']) {
                            continue;
                        }
                        throw new Exception(__e(
                            __FILE__, 
                            'Hodnota "%s" v %s. položke "%s" ma nesprávne zadané jednotky "%s"', 
                            $value, 
                            $i + 1, 
                            $valuesPart, 
                            $valueUnits
                        ));
                    }
                    $value = $valueNormalizedNumber . $valueNormalizedUnits;
                }
            }
            // parse non-numeric value
            $nonNumericValueSuccess = false;
            if (
                !$numericValueSuccess
                && $options['allowValues']
                && (
                    $options['allowValues'] === true
                    || preg_match($options['allowValues'], $value)
                )
            ) {
                $nonNumericValueSuccess = true;
            }
            // check success
            if (
               !$numericValueSuccess
               && !$nonNumericValueSuccess
            ) {               
                if (!$options['exceptionOnFailure']) {
                    continue;
                }
                elseif (
                    strpos($value, ':') !== false
                    && strpos($value, ',') !== false
                ) {
                    throw new Exception(__e(
                        __FILE__, 
                        'Nesprávne zadaná hodnota "%s" v %s. položke "%s". Na oddelenie položiek sa používa bodkočiarka', 
                        $value, 
                        $i + 1,
                        $valuesPart
                    ));
                }
                throw new Exception(__e(
                    __FILE__, 
                    'Nesprávne zadaná hodnota "%s" v %s. položke "%s"', 
                    $value, 
                    $i + 1,
                    $valuesPart
                ));
            }
            // create array of parsed values
            if ($screenWidth === null) {
                $parsedValue[0] = $value;
            }
            else {
                $parsedValue[$screenWidth] = $value;
            }
        }
        return $parsedValue;
    }
    
    /**
     * Returns media queries css code for provided selector and array of responsive 
     * values. E.g. if $selector is '.my-block' and array of responsive values is:
     * 
     *      array(
     *          'width' => array(
     *              0 => '50%',
     *              768 => '100%',
     *          ),
     *          'background' => array(
     *              768 => 'rgba(255,255,255,0.5)',
     *          ),
     *      )
     * 
     * then following media queries css code is returned:
     * 
     *      '.my-block{width:50%;}@media screen and (max-width:768px){.my-block{width:100%;background:rgba(255,255,255,0.5)}}'
     * 
     * @param string $selector Css selector to apply styles to, e.g. '.my-block'
     * @param array $parsedResponsiveValues Array of pairs {valueName} => {parsedResponsiveValue},
     *          where {valueName} can be e.g. 'width' or 'padding-top' and {parsedResponsiveValue}
     *          is an array returned by Html::parseResponsiveCssValue()
     * 
     * @return string The above described output css code
     */
    static public function cssMediaQueries($selector, $parsedResponsiveValues) {
        $mediaQueries = array();
        $defaultMediaQuery = array();
        foreach ($parsedResponsiveValues as $valueName => $parsedResponsiveValue) {
            foreach ($parsedResponsiveValue as $screenWidth => $value) {
                if (empty($screenWidth)) {
                    $defaultMediaQuery[$valueName] = $value;
                }
                else {
                    if (empty($mediaQueries[$screenWidth])) {
                        $mediaQueries[$screenWidth] = array();
                    }
                    $mediaQueries[$screenWidth][$valueName] = $value;
                }
            }
        }
        krsort($mediaQueries);
        if (!empty($defaultMediaQuery)) {
            // use "+" to preserve numeric keys from $mediaQueries array
            $mediaQueries = array($defaultMediaQuery) + $mediaQueries;
        }
        $css = '';
        foreach ($mediaQueries as $screenWidth => $mediaQuery) {
            if (empty($screenWidth)) {
                $css .= $selector . '{';
                foreach ($mediaQuery as $property => $value) {                    
                    $css .= $property . ':' . $value . ';';
                }
                $css .= '}';
            }
            else {
                $css .= '@media screen and (max-width:' . $screenWidth . 'px){';
                $css .= $selector . '{';
                foreach ($mediaQuery as $property => $value) {                    
                    $css .= $property . ':' . $value . ';';
                }
                $css .= '}';
                $css .= '}';
            }
        }
        return $css;
    }    
    
    /**
     * Generates html for attributes
     * 
     * @param array $attributes List of attribute names and values which can use
     *      nested form for common prefixed, e.g.:
     * 
     *          array(
     *              'data-' => array(               // if attributes are nested then parent key are used as prefixes
     *                  'title' => 'My title'
     *                  'product-id' => '52'
     *                  'unique' => null            // if value is NULL then there is no value generated in html
     *              ),
     *              'src' = '/my/src.jpg'
     *              
     *          )
     * 
     *      will produce following html (notice the leading space):
     * 
     *          ' data-title="My title" data-product-id="52" data-unique src="/my/src.jpg"'
     * 
     * @return string Attributes string containing leading space. If no attributes are
     *      generated then empty string.
     */
    public static function attributes($attributes) {
        $attributes = Arr::deflate($attributes, array('separator' => ''));
        $attributesHtml = '';
        foreach ($attributes as $name => $value) {
            if (!is_scalar($value) && $value !== null) {
                continue;
            }
            $attributesHtml .= ' ' . $name;
            if ($value !== null) {
                $attributesHtml .= '="' . $value . '"';
            }
        }
        return $attributesHtml;
    }
    
    /**
     * Merges provided attributes arrays to one accumulating specified attributes to 
     * one value.
     * 
     * @param array $attributesA E.g. array('class' => 'my-class', 'title' => 'My title')
     * @param array $attributesB E.g. array('class' => 'your-class', 'title' => 'Your title')
     * @param array $accumulate List of attributes whose values must be accumulated containing
     *          pairs like {attrName} => {valuesSeparator}. Defaults to an array like array('class' => ' ', 'style' => ';').
     *         
     * @return array resulting merged array fo attributes, e.g. array('class' => 'my-class your-class', 'title' => 'Your title')
     */
    public static function mergeAttributes($attributesA, $attributesB, $accumulate = array('class' => ' ', 'style' => ';')) {
        $attributes = Arr::mergeRecursive($attributesA, $attributesB);
        foreach ($accumulate as $attr => $separator) {
            if (
                (isset($attributesB[$attr]) || array_key_exists($attr, $attributesB))
                && 
                (isset($attributesA[$attr]) || array_key_exists($attr, $attributesA))
            ) {
                // merge attribute values but ensure unique values
                $attributes[$attr] = implode($separator, array_unique(array_merge(
                    explode($separator, rtrim($attributesA[$attr], $separator)), 
                    explode($separator, $attributes[$attr])
                )));
            }
        }
        return $attributes;
    }
    
    /**
     * Returns html (style attribute) to hide tag
     * 
     * @param bool $hide Optional. To hide or not to hide? Defaults to TRUE.
     * @param string $html Html to be returned if $hide is TRUE. Defaults to ' style="dislay:none;"'
     * 
     * @return string Returns $html if $hide is TRUE otherwise empty string
     */
    public static function hide($hide = true, $html = ' style="display:none;"') {
        if ($hide) {
            return $html;
        }
        else {
            return '';
        }
    }
    
    /**
     * Generates robots meta tag < meta name="robots" content="index,follow">
     * 
     * @param bool $index Optional. If TRUE or NULL then 'index' is used. If FALSE 
     *      (or any empty value except NULL) then 'noindex' is used. Defaults to TRUE.
     * @param bool $follow Optional. If TRUE or  NULL then 'follow' is used. If FALSE 
     *      (or any empty value except NULL) then 'nofollow' is used. Defaults to TRUE.
     * 
     * @return string Tag html code
     */
    public static function robotsTag($index = true, $follow = true) {
        $indexVal = 'index';
        $followVal = 'follow';
        if (isset($index) && empty($index)) {
            $indexVal = 'noindex';
        }
        if (isset($follow) && empty($follow)) {
            $followVal = 'nofollow';
        }
        return '<meta name="robots" content="' . $indexVal . ',' . $followVal . '"/>';
    }
    
    /**
     * Generates canonical URL tag <link rel="canonical" href="{url}"/>
     * 
     * Canonical URL (of returned content) is the URL which is the correct
     * one (from point of SEO view) to avoid the same/duplicit/similar content on different URLs 
     * (see the below listed links). In general it can be absolute or relative.
     * In case of fajnwork website the relative URLs are ok as the absolute URLs
     * normalization (www vs no www, http vs https) is done in .htaccess.
     * ATTENTION: If no normalization is done in .htaccess (it means e.g. http://example.com/my-page
     * is not redirected to https://www.example.com/my-page and both return
     * the same content) then the <base> tag MUST be used to specify normalized URL base
     * for all relative URLs in a document (e.g. <base href="https://www.example.com/">).
     * 
     * See:
     * - https://developers.google.com/search/blog/2009/02/specify-your-canonical
     * - https://developers.google.com/search/docs/advanced/crawling/consolidate-duplicate-urls
     * - https://support.google.com/webmasters/answer/6080548
     * - https://www.semrush.com/blog/canonical-url-guide/
     * 
     * @param string $url URL string. If empty value then no tag html is returned.
     * 
     * @return string Tag html code
     */
    public static function canonicalUrlTag($url) {
        if (!$url) {
            return '';
        }
        return '<link rel="canonical" href="' . $url . '" />';
    }
    
    /**
     * Generates hyperlink tag (< a href="{urlFromLocatorAnOptions}" {attributes}>{label}< /a>).
     * Can be used to render items locator in admin indexes (Html::smartIndex() > 'renderFields')
     * 
     * @param string $locator
     * @param string $label
     * @param array $options Following are available:
     *      - 'lang' (string) Defaults to App::$lang (actual lang) 
     *      - 'attributes' (array) See Html::attributes() phpDoc. Defaults to NULL.
     *      - 'ignoreEmptyLocator' (boolean) If TRUE then no hyperlink tag is returned
     *          for empty $locator. Defaults to TRUE.
     * 
     * @return string
     */
    public static function hyperlinkTag($locator, $label, $options = array()) {
        $options = array_merge(array(
            'lang' => App::$lang,
            'attributes' => array(),
            'ignoreEmptyLocator' => true,
        ), $options);
        $html = '';
        if (!empty($locator) || !$options['ignoreEmptyLocator']) {
            $url = $locator;
            // if not absolute URL
            if (!Validate::url($url)) {                            
                $url = App::getUrl(array(
                    'locator' => $url,
                    'lang' => $options['lang'] === DEFAULT_LANG ? '' : $options['lang'],
                ));
            }
            $attributes = '';
            if ($options['attributes']) {
                $attributes = self::attributes((array)$options['attributes']);
            }
            $html = '<a href="' . $url . '"' . $attributes . '>' . $label . '</a>';
        }
        return $html;
    }
    
    /**
     * Creates toggle button to show/hide specified target block.
     * Target block is slided up and down. 
     * 
     * NOTE: Toggle button is intended for use in responsive/mobile designs to 
     * show hide navigation bar after it is hidden on mobile devices.  It is up to 
     * developer to set proper css for toggle button and related navigation block;
     * 
     * @param string $targetSelector Selector of target element to be toggled.
     *      E.g. '.my-menu' or '#your-menu'.
     * @param array $options Following options are available:
     *      - 'class' (string) Aditional css class of button main container. 
     *          Defaults to NULL, means button has only basic class 'toggle-button'.
     *      - 'label' (string) Toggle button label. Defaults to NULL.
     *      - 'tag' (string) Toggle button tag name. It is better to use 'div' instead 
     *          of 'button'. Tag 'button' with style 'display: block; width: auto;' 
     *          do not stretch to full container width but behaves like inline element.
     *          Defaults to 'div'.
     * 
     * @return string Toggle button html
     */
    public static function toggleButton($targetSelector, $options = array()) {
        $defaults = array(
            'class' => null,
            'label' => null,
            'tag' => 'div',
        );
        $options = array_merge($defaults, $options);
        return App::loadView('App', 'Html/toggleButton', array(
            'targetSelector' => $targetSelector,
            'class' => $options['class'],
            'label' => $options['label'],
            'tag' => $options['tag'],
        ));
    }
    
    /**
     * Generates html of select input by which it is possible to choose value 
     * of GET $param in url
     * 
     * @param string $param GET root param name, e.g. 'filter ' in  $_GET['filter']['active'].
     * @param array $options All options which can be passed to FormHelper::select()
     *      plus following:
     *      - 'options' (array) Array containig value and label pairs like {value} => {label}. 
     *      The GET $param will be set to some of values once it is choosen in select input. 
     *      E.g. for 'page' $param this can be array(1 => 1, 2 => 2, ...). Values can contain 
     *      aditional subparam name separated by ':' from final value of param, e.g. 
     *      for 'sort' $param this can be array('name:ASC' => 'Name ascending', 'price:DESC' => 'Price descending').
     *      If 'Name ascending' is choosen then the value of $_GET['sort']['name'] is set to 'ASC'.
     *      Defaults to empty array().
     *      - 'class' (string) Css class applied on wrapper div. If the template is 
     *      provided explicitly then :c: can be used to place it in template.
     *      - 'template'  (string) Defaults to '< div class=":c:">:l::i::e:< /div>'
     *      - 'resetParams' (string|array) Single GET param name or an array of GET param names
     *      which should be reset after the value of select is changed. Defaults to empty array().
     *      - 'separator' (string) Field path separator used to separate nested field levels, e.g. 'filter.active'. 
     *      Sometimes it is necessry to change it if the field name itself contains the separator character,
     *      e.g. 'filter/Users.active'. Defaults to '.'. 
     *      - 'labelSeparator' (string) Label separator string. Defaults to ':'.
     *      - 'removeEmptyParam' (bool) If TRUE then param is removed from URL if 
     *      its value is empty (= empty string!). E.g. if removeEmptyParam is TRUE 
     *      then URL '/products/of-mycategory?filter[stock]=' will be changed to 
     *      '/products/of-mycategory'. But URL '/products/of-mycategory?filter[stock]=0'
     *      will not be changed as only empty string is considered to be empty value.
     *      Defaults to FALSE.
     * 
     * @return string Select input html
     */
    public static function urlParamSelect($param, $options = array()) {
        $defaults = array(
            'options' => array(),
            'id' => uniqid('-run-url-param-select-'),
            'class' => null,
            'template' => '<div class=":c:">:l::i::e:</div>',
            'resetParams' => array(),
            'separator' => '.',
            'labelSeparator' => ':',
            'removeEmptyParam' => false,
        );
        $options = array_merge($defaults, $options);
        $options['options'] = (array)$options['options'];
        
        $parsedUrl = App::$parsedUrl;
        $parsedUrl['get'] = (array)$parsedUrl['get'];
        
        // set data according to select options
        $data = array();
        $actualValue = Arr::getPath($parsedUrl['get'], $param, $options['separator']);
        if (!is_array($actualValue)) {
            Arr::setPath($data, $param, (string)$actualValue, $options['separator']);
        }
        foreach ($options['options'] as $value => $label) {
            if (strpos($value, ':') !== false) {
                $subValue = explode(':', $value);
                $subParam = array_shift($subValue);
                $subValue = implode(':', $subValue);
                $fullParam = $param . $options['separator'] . $subParam;
                $actualValue = (string)Arr::getPath($parsedUrl['get'], $fullParam, $options['separator']);
                if ($subValue === $actualValue) {
                    $data[$param] = $value;
                    break;
                }
            }
        }
                
        App::loadLib('App', 'FormHelper');
        $Form = new FormHelper(array(
            'data' => $data,
            'useDataWrapper' => false,
            'separator' => $options['separator'],
            'labelDefaults' => array(
                'separator' => $options['labelSeparator'],
            )
        ));
        $class = '-run-url-param-select';
        if (!empty($options['class'])) {
            $class .= ' ' . $options['class'];
        }
        unset($options['class']);
        
        $options['template'] = str_replace(':c:', $class, $options['template']);
        
        // prepare input options (remove some of this metho doptions as they would be 
        // transformed into input attributes
        $inputOptions = $options;
        unset($inputOptions['separator']);
        unset($inputOptions['resetParams']);
        
        return App::loadView('App', 'Html/urlParamSelect', array(
            'id' => $options['id'],
            'resetParams' => $options['resetParams'],
            'separator' => $options['separator'],
            'removeEmptyParam' => $options['removeEmptyParam'],
            'selectHtml' => $Form->select($param, $inputOptions),
        ));
    }
    
    /**
     * Generates html of link by which it is possible to switch (add/remove) $value of GET $param in url
     * 
     * @param string $param GET param name path, e.g. 'page' or 'filter.active'.
     * @param string $value GET param value.
     * @param array $options Following are available:
     *      - 'label' (string) Switch label (plain text or html). Defaults to NULL.
     *      - 'class' (string) Css additional class applied on wrapper div. Defaults to NULL.
     *      - 'showCheckbox' (bool) If TRUE then a checkbox is shown to see if switch 
     *          is active or not. Defaults to FALSE.
     *      - 'template' (string) Switch html template. It must be < a> tag. Following inserts can
     *      be used: :a: - attributes (generated from items in options which are not
     *      standard options), :c: - css class, :l: - label, :u: - generated url,
     *      :ch: - checkbox (if option 'showCheckbox' is FALSE then empty string).
     *      Defaults to '< a class=":c:" href=":u:":a:>:ch:&nbsp;:l:< /a>'.
     *      - 'resetParams' (string|array) Single GET param path or an array of GET param paths
     *      which should be reset after the value of select is changed. E.g. 'page' or
     *      array('page', 'filter.active'). Defaults to empty array().
     *      - 'separator' (string) Field path separator used to serarate nested field levels, e.g. 'filter.active'. 
     *      Sometimes it is necessry to change it if the field name itself contains the separator character,
     *      e.g. 'filter/Users.active'. Defaults to '.'. 
     *      - 'paramPrefix' (string) Prefix used in param to prepend field name, e.g. 'sort' or
     *          'filter'. This is used to identify case of empty field & value name.
     *          Defaults to ''.
     *      - 'removeEmptyParam' (bool) If TRUE then param is removed from URL if 
     *      its value is empty (= empty string!). E.g. if removeEmptyParam is TRUE 
     *      then URL '/products/of-mycategory?filter[stock]=' will be changed to 
     *      '/products/of-mycategory'. But URL '/products/of-mycategory?filter[stock]=0'
     *       will not be changed as only empty string is considered to be empty value.
     *     
     * @return string Link html
     */
    public static function urlParamSwitch($param, $value, $options = array()) {
        $defaults = array(
            'label' => null,
            'class' => null,
            'showCheckbox' => false,
            'template' => '<a class=":c:" href=":u:":a:>:ch:&nbsp;:l:</a>',
            'resetParams' => array(),
            'separator' => '.',
            'paramPrefix' => '',
            'removeEmptyParam' => false,
        );
        $options = array_merge($defaults, $options);
        // normalize
        $value = (string)$value;
        $options['resetParams'] = (array)$options['resetParams'];
        
        $parsedUrl = App::$parsedUrl;
        $parsedUrl['get'] = (array)$parsedUrl['get'];
        // remove reset params
        if (!empty($options['resetParams'])) {
            foreach ($options['resetParams'] as $resetParam) {
                Arr::unsetPath($parsedUrl['get'], $resetParam, $options['separator']);
            }
        }
        // resolve new value
        $actualValue = Arr::getPath($parsedUrl['get'], $param, $options['separator']);
        if ($value === $actualValue) {
            $active = true;
            if ($options['removeEmptyParam']) {
                Arr::unsetPath($parsedUrl['get'], $param, $options['separator']);
            }
            else {
                Arr::setPath($parsedUrl['get'], $param, '', $options['separator']);
            }
        }
        // if the param...
        elseif (
            // ...is not found in GET parameters
            $actualValue === null
            // ...there is no value ('')
            && $value === ''
            // ...there is no field name ('')
            && $param === $options['paramPrefix']
        ) {
            // ...then resolve this case as active
            $active = true;
        }
        else {
            $active = false;
            Arr::setPath($parsedUrl['get'], $param, $value, $options['separator']);
        }
        
        $class = '-run-url-param-switch';
        if (!empty($options['class'])) {
            $class .= ' ' . $options['class'];
        }
        if ($active) {
            $class .= ' active';
        }
        // do not inherit nothing on url creation - we have already inheritted here above
        $parsedUrl['inherit'] = array();
        $url = App::getUrl($parsedUrl);
        // 
        $checkbox = '';
        if (!empty($options['showCheckbox'])) {
            App::loadLib('App', 'FormHelper');
            $Form = new FormHelper(array(
                'useDataWrapper' => false,
            ));
            $inputOptions = array(
                'onclick' => "window.location = jQuery(this).closest('a').attr('href')",
            );
            if ($active) {
                $inputOptions['checked'] = 'checked';
            }
            App::setJsFiles(array(
                '/app/js/vendors/jquery.min.js',
            ));
            $checkbox = $Form->checkbox(null, $inputOptions);
        }
        return str_replace(
            array(
                ':a:', 
                ':c:', 
                ':l:', 
                ':u:', 
                ':ch:', 
            ),
            array(
                '', //@todo attributes - all items in options which are not defaults
                $class, 
                $options['label'],
                $url,
                $checkbox
            ),
            $options['template']
        );
    }
    
    /**
     * Returns html code for hint
     * 
     * @param string $hint Hint text
     * @param array $options Following are available:
     *      - 'class' (string) Hint label class. Default class '-run-hint' is always
     *          attached to classes defined here. Defaults to NULL, means only the default
     *          class '-run-hint' applies.
     *      - 'id' (string) Hint label id. If empty then it is autogenerated.
     *          Defaults to NULL.
     *      - 'label' (string) Hint label content. Defaults to question mark icon 
     *          '< i class="fa fa-question-circle">< /i>'
     *      - 'jsOptions' (array) Options which are in json_encoded form provided
     *          to Run.App.Hints js library. Check the lib for all possibilities.
     *          Defautls to following:
     *              - - 'selector' (string) Selector to initialize hints for. If empty
     *                  then it is set to 'id' option. Defaults to '.-run-hint'. 
     *              - - 'interactive' (bool) Defaults to TRUE (see Run.App.Hints.js).
     *              - - 'maxWidth' (int) Defaults to 350 (see Run.App.Hints.js).
     *              - - 'contentAsHTML' (bool) Defaults to TRUE (see Run.App.Hints.js).
     *              - - 'theme' (string) Hint js lib theme. Defaults to 'tooltipster-run' (defined in app/css/libs/basic.css, see Run.App.Hints.js).
     *      - 'cssFiles' (string|array) Css file(s) to display hints. For defaults see the method definition.
     *      - 'jsFiles' (string|array) Js file(s) to display hints. For defaults see the method definition.
     *      - 'script' (string) Launching js script. The insert ':options:' is used to 
     *          place the 'jsOptions' option. Defaults to jQuery(function(){new Run.App.Hints(:options:);});
     *      
     * 
     * @return string
     */
    public static function hint($hint, $options = array()) {
        $defaults = array(
            'class' => null,
            'id' => null,
            'label' => '<i class="fa fa-question-circle"></i>',
            'jsOptions' => array(
                'selector' => '.-run-hint',
            ),
            'cssFiles' => array(
                '/app/css/vendors/font-awesome/css/font-awesome.min.css',
                '/app/js/vendors/tooltipster/css/tooltipster.css',
            ),
            'jsFiles' => array(
                '/app/js/vendors/jquery.min.js',
                '/app/js/vendors/tooltipster/js/jquery.tooltipster.min.js',
                '/app/js/libs/Hints.js',
            ),
            'script' => 'jQuery(function(){new Run.App.Hints(:options:);});',
        );
        $options = Arr::mergeRecursive($defaults, $options);   
        // normalize
        $options['class'] = rtrim('-run-hint ' . ltrim($options['class']));
        if (empty($options['id'])) {
            $options['id'] = uniqid();
        }
        if (empty($options['jsOptions']['selector'])) {
            $options['jsOptions']['selector'] = '#' . $options['id'];
        }
        
        // attach js and css files
        App::setJsFiles($options['jsFiles']);
        App::setCssFiles($options['cssFiles']);
        
        // attach launching script
        static $attachedScripts = array();
        $script = str_replace(
            array(':options:'),
            array(json_encode($options['jsOptions'])),
            $options['script']
        );
        if (empty($attachedScripts[$script])) {
            App::setJs($script);           
            $attachedScripts[$script] = true;
        }
        
        // prepare id html
        $idHtml = " id=\"{$options['id']}\"";
        $contentIdHtml = " id=\"{$options['id']}_content\"";
        
        // prepare classes html
        $classHtml = '';
        if ($options['class']) {
            $classHtml = " class=\"{$options['class']}\"";
        }
        
        // prepare attributes html
        $attrsHtml = self::attributes(array_diff_key($options, $defaults));
        
        return '<span' . $idHtml . $classHtml . $attrsHtml .'>' . $options['label'] . '</span>' . 
            '<div style="display:none;"' . $contentIdHtml . '>'  . nl2br($hint) . '</div>';
    }
    
    /**
     * Creates HTML for video embed
     * 
     * See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/video
     * 
     * @param string $url Video file URL. Youtube URL or code can be provided too.
     * @param array $options Following options are available (all other keys introduced 
     *      in  options are used as attributes of main container):
     *      - 'width' (int) Width in pixels (without px). Use CSS to make the width responsive
     *           e.g.: `.my-video > * {display:block; width:100%; max-width: 100%}`.
     *          Defaults to NULL.
     *      - 'height' (int) Height in pixels (without px). Defaults to NULL.
     *      - 'autoplay' (bool) If TRUE then video is started automatically. Defaults to FALSE.
     *      - 'allowfullscreen' (bool) If TRUE fullscreen mode is available. Defaults to TRUE.
     *      - 'placeholder' (string) Placeholder image url path. If provided then the video
     *      is not included directly on page but instead of the video the placeholder is 
     *      placed there and the video is displayed in fancybox only after clicking 
     *      the placeholder image. Defaults to NULL.
     *      - 'placeholderText' (string) Text placed over placoholder image. Applies
     *      only if 'placeholder' image is provided. Defaults to NULL.
     * 
     * @return string
     */
    public static function video($url, $options = array()) {
        // detect youtube video and use Html::youtubeVideo() to create its HTML
        if (preg_match('#/(?:youtu\.be|(?:www\.)?youtube\.com)/#i', $url)) {
            return self::youtubeVideo($url, $options);
        }
        
        // detect vimeo video and use Html::vimeoVideo() to create its HTML
        if (preg_match('#/(?:player\.)?vimeo\.com/#i', $url)) {
            return self::vimeoVideo($url, $options);
        }
        
        $defaults = array(
            'width' => null,
            'height' => null,
            'autoplay' => false,
            'allowfullscreen' => true,
            'placeholder' => null,
            'placeholderText' => null,
            // list of keys which should not be provided as attributes
            'id' => null,
            
        );
        $options = array_merge($defaults, $options);
        $options['width'] = (int)$options['width'];
        $options['height'] = (int)$options['height'];
        
        // prepare attributes
        $attributes = array_diff_key($options, $defaults);
        $attributes = Html::attributes($attributes);
        
        return App::loadView('App', 'Html/video', array(
            'url' => $url,
            'options' => $options,
            'attributes' => $attributes,
        ));
    }
    
    /**
     * Creates HTML for youtube video embed
     * 
     * See https://developers.google.com/youtube/player_parameters
     * 
     * @param string $urlOrCode Youtube video URL or just the code from URL
     * @param array $options Following options are available (all other keys introduced 
     *      in  options are used as attributes of main container):
     *      - 'width' (int) Width in pixels (without px). Use CSS to make the width responsive
     *           e.g.: `.my-video > * {display:block; width:100%; max-width: 100%}`.
     *          Defaults to NULL.
     *      - 'height' (int) Height in pixels (without px). Defaults to NULL.
     *      - 'autoplay' (bool) If TRUE then video is started automatically. Defaults to FALSE.
     *      - 'allowfullscreen' (bool) If TRUE fullscreen mode is available. Defaults to TRUE.
     *      - 'placeholder' (string) Placeholder image url path. If provided then the video
     *      is not included directly on page but instead of the video the placeholder is 
     *      placed there and the video is displayed in fancybox only after clicking 
     *      the placeholder image. Defaults to NULL.
     *      - 'placeholderText' (string) Text placed over placoholder image. Applies
     *      only if 'placeholder' image is provided. Defaults to NULL.
     * 
     * @return string
     */
    public static function youtubeVideo($urlOrCode, $options = array()) {
        $defaults = array(
            'width' => null,
            'height' => null,
            'autoplay' => false,
            'allowfullscreen' => true,
            'placeholder' => null,
            'placeholderText' => null,
            // list of keys which should not be provided as attributes
            'id' => null,
            
        );
        $options = array_merge($defaults, $options);
        $options['width'] = (int)$options['width'];
        $options['height'] = (int)$options['height'];
        
//        if (
//            empty(App::$useJsEmbroidery) 
//            && empty($options['placeholder'])
//            ||
//            empty($code)
//        ) {
//            return '';
//        }

        // retrieve code from url and prepare youtube url
        if (preg_match('/[^a-z0-9\-_]/i', $urlOrCode)) {
            if (preg_match('/(?:youtu\.be|v|embed)(?:\/|=)([a-z0-9\-_]+)/i', $urlOrCode, $matches)) {
                $code = $matches[1];
            }
            // if failed the return 
            else {
                return '';
            }
        }
        else {
            $code = $urlOrCode;
        }
        $url = '//www.youtube.com/embed/' . $code . '?autoplay=' . (int)$options['autoplay'];

        // prepare attributes
        $attributes = array_diff_key($options, $defaults);
        $attributes = Html::attributes($attributes);
        
        return App::loadView('App', 'Html/youtubeVideo', array(
            'url' => $url,
            'options' => $options,
            'attributes' => $attributes,
        ));
    }
    
    /**
     * Creates HTML for vimeo video embed
     * 
     * See https://developer.vimeo.com/api/oembed/videos
     * 
     * @param string $urlOrCode Vimeo video URL or just the code from URL
     * @param array $options Following options are available (all other keys introduced 
     *      in  options are used as attributes of main container):
     *      - 'width' (int) Width in pixels (without px). Use CSS to make the width responsive
     *           e.g.: `.my-video > * {display:block; width:100%; max-width: 100%}`.
     *          Defaults to NULL.
     *      - 'height' (int) Height in pixels (without px). Defaults to NULL.
     *      - 'autoplay' (bool) If TRUE then video is started automatically. Defaults to FALSE.
     *      - 'allowfullscreen' (bool) If TRUE fullscreen mode is available. Defaults to TRUE.
     *      - 'placeholder' (string) Placeholder image url path. If provided then the video
     *      is not included directly on page but instead of the video the placeholder is 
     *      placed there and the video is displayed in fancybox only after clicking 
     *      the placeholder image. Defaults to NULL.
     *      - 'placeholderText' (string) Text placed over placoholder image. Applies
     *      only if 'placeholder' image is provided. Defaults to NULL.
     * 
     * @return string
     */
    public static function vimeoVideo($urlOrCode, $options = array()) {
        $defaults = array(
            'width' => null,
            'height' => null,
            'autoplay' => false,
            'allowfullscreen' => true,
            'placeholder' => null,
            'placeholderText' => null,
            // list of keys which should not be provided as attributes
            'id' => null,
            
        );
        $options = array_merge($defaults, $options);
        $options['width'] = (int)$options['width'];
        $options['height'] = (int)$options['height'];
        
//        if (
//            empty(App::$useJsEmbroidery) 
//            && empty($options['placeholder'])
//            ||
//            empty($code)
//        ) {
//            return '';
//        }

        // retrieve code from url and prepare vimeo url
        if (preg_match('/[^0-9]/i', $urlOrCode)) {
            if (preg_match('/(?:player\.vimeo\.com\/video|vimeo\.com)\/([0-9]+)/i', $urlOrCode, $matches)) {
                $code = $matches[1];
            }
            // if failed the return 
            else {
                return '';
            }
        }
        else {
            $code = $urlOrCode;
        }
        $url = '//player.vimeo.com/video/' . $code . '?autoplay=' . (int)$options['autoplay'];

        // prepare attributes
        $attributes = array_diff_key($options, $defaults);
        $attributes = Html::attributes($attributes);
        
        return App::loadView('App', 'Html/vimeoVideo', array(
            'url' => $url,
            'options' => $options,
            'attributes' => $attributes,
        ));
    }
    
    /**
     * Creates js countdown.
     * Implemented from http://hilios.github.io/jQuery.countdown/
     * 
     * @param string $finalTime Final date to countdown for. It can be provided 
     *      in following formats 'YYYY/MM/DD hh:mm:ss' or 'YYYY/MM/DD'.
     * 
     * @return string
     */
    public static function countdown($finalTime, $options = array()) {
        $defaults = array(
        );
        $options = array_merge($defaults, $options);
        $options['finalTime'] = $finalTime;
        return App::loadView('App', 'Html/countdown', $options);
    }
    
    /**
     * Generates html for breadcrumbs specified
     * 
     * @param array $items Array of breadcrumbs items. Each item must have defined 'label'. 
     *      Items which have defined 'url' will be created as active.
     * @param array $options Following are available:
     *      - 'class' (string) Additional css class. Defaults to NULL.
     *      - 'label' (string) Breadcrumbs label. Defaults to NULL.
     *      - 'homeItem' (string) Label of home item. Defaults to setting App.name.
     *      - 'separator' (string) Separator string to be placed between breadcrumbs items.
     *      Defaults to NULL.
     *      
     * @return string
     */
    public static function breadcrumbs($items, $options = array()) {
        $defaults = array(
            'class' => null,
            'label' => null,
            'homeItem' => App::getSetting('App', 'name'),
            'separator' => null,
        );
        $options = array_merge($defaults, $options);
        $options['class'] = rtrim('-run-breadcrumbs ' . ltrim($options['class']));
        $options['items'] = $items;
        return App::loadView('App', 'Html/breadcrumbs', $options);
    }
    
    /**
     * Creates html code of menu from $menuData generated by method Model::findTree()
     *
     * @param array $menuData - generated by method Model::findTree(). Each item can 
     *          contain following items:
     *              - string 'label'
     *              - string 'locator' Optional. If not defined then item id is used in url.
     *                  If defined then this exact url is used. 
     *                  'urlBase' is applied, if url is http url (not slug)
     *              - boolean 'passive' Optional. If TRUE then menu item is not created as 
     *                  live link with label in < a> tag but <s pan> tag is used for label
     *              - integer 'count' Optional. If set then menu item will be created with
     *                  count info e.g. for count 24: "Encyklopedies (24)", but only
     *                  if $options['showCount'] is TRUE. Count info is enclosed in <span class="c">()</span>. 
     *              - string 'icon' Optional. If set then < span class="icon" style="background-image: url('/my_icon.png');">< /span> 
     *                  is add as the very first item in < a> ... < /a>.
     *              - string 'class' Optional. Css class to be set on < li>.
     * 
     * @param array $options: there are following keys to use:
     *          - mixed 'activeItem' Slug or id of active item in $menuData (the currently 
     *              selected item in menu) Serves to highlight given menu item and 
     *              open menu parent levels. Defaults to NULL.
     *          - mixed 'passiveActiveItem' If TRUE then label of active item is not created as 
     *              live link with label in < a> tag but < span> tag is used for label. 
     *              Defaults to FALSE.
     *          - mixed 'openedItems' List of ids of items which should be opened (means
     *              .open css class will be assigned to them). Defaults to NULL.
     *          - string 'urlBase' Url part which will be prepended to menu item
     *              slugs or ids. E.g. parent slug can be passed here. This is not 
     *              applied on item absolute 'locator'. Defaults to NULL.
     *          - string 'homeSlug' Default slug of website. If set then this serves 
     *              to ommit slug for menu item which is pointing to home page,
     *              e.g. instead of www.mysite.com/home the home item will point to www.mysite.com
     *              The aim is to prevent an access of the same content from 2 
     *              different urls and so add good SEO points. Defaults to NULL
     *          - array 'labelWrappers' Array containing 'l', 'w', 'r' items to
     *              define which < span> wrappers will be used around the label text.
     *              Defaults to array();
     *          - bool 'listWrapper' If true then div.w tag wraps items of < ul> tag.
     *              Defaults to FALSE;
     *          - array 'listWrapperLevels' Array of level (1, 2, ...) to which
     *              listWrappers should be applied. Defaults to array(), means all
     *              levels
     *          - int 'depth' Number of nested submenu levels. Defaults to NULL, 
     *              mean all levels are created. 
     *          - string 'lang' Lang code to be used in url. If any empty value then 
     *              the lang is explicitly turned off. Defaults to URL_LANG.
     *          - mixed 'wrapper' If TRUE then < div> wrapper is used for the menu.
     *              If string then wrapper is created with css class given by string.
     *              Defaults to FALSE.
     *          - bool 'setCssId' If TRUE then css ids are set on li level like
     *              {cssIdPrefix}-{menuItemId}. Defaults to FALSE
     *          - string 'cssIdPrefix' Prefix used when 'setCssIds' is TRUE. Defaults
     *              to 'menu-item'
     *          - bool 'showCount' If TRUE then counts are displayed for all items which
     *              have them set.
     *          - SmartAdminLauncher 'SmartAdminLauncher' Instance of SmartAdminLauncher class 
     *              to enable launching admin for menu items. Only menu items with integer ids
     *              are enabled to launch admin. Defaults to NULL.
     *          - bool 'overflow' If TRUE then menu overflown items are are moved to 
     *              overflow dropdown menu at th end of menu (see e.g. https://codepen.io/johnmears/pen/vhJKL)
     *              See also option 'allowOverflowFromScreenWidth'. Default to FALSE.
     *          - int 'allowOverflowFromScreenWidth' Minimal screen width the overflow 
     *              is allowed from. This is useful if the  menu is changed to hamburger menu 
     *              at some screen width. This should be set to that width. Defaults to 0 
     *              it means that overflow is always alloved.
     * 
     * @param int $node Defaults to NULL on the 0-th level. Contains valid node id 
     *      on all other levels. This parameter is set during the recursion. Never pass it 
     *      when calling this method.
     *
     * @return string - html code of multilevel menu:
     * 
     *          <div class="{$options['wrapper']}">                                         <-  if $options['wrapper'] is defined  then <div> wrapper is used with class $options['wrapper']
     *              <ul class="level-01>                                                    <- 'level-01', 'level-02', ... are assigned to <ul>
     *                  <li class="first open has-subitems">                                <- 'first', 'last', 'has-subitems' and 'open' or 'active' are classes of <li>
     *                      <a href="..." class="label">                                    <- 'label' is class of <a>
     *                          <span class="l"></span>                                     <- 'l', 'w' and 'r' are assigned to possible <span> tags inside label tag
     *                          <span class="w">label 123</span>
     *                          <span class="r"></span>
     *                      </a>
     *                      <ul class="level-02>
     *                          <div class="w">                                             <- 'w' is assigned to possible <div> tag inside <ul> tag
     *                              <li class="first"><a href="...">label 456</a></li>
     *                              <li class="active"><a href="...">label 456</a></li>
     *                              ...
     *                              <li class="last"><a href="...">label 456</a></li>
     *                          </div>
     *                      </ul>
     *                   </li>
     *                   ...
     *                  
     *              </ul>
     *          </div>
     */
    public static function menu(&$menuData, $options = array(), /*internal use:*/ $node = null, $level = 0) {
        static $activeBranch; 
        
        // if empty (invalid tree root node) then stop
        if(empty($menuData)) {
            return '';
        }
        
        $default = array(
            'activeItem' => null,
            'passiveActiveItem' => false,
            'openedItems' => array(),
            'urlBase' => null,
            'homeSlug' => null,
            'labelWrappers' => array(), // 'r', 'w' or 'l'
            'listWrapper' => false,
            'listWrapperLevels' => array(),
            'depth' => null,
            'visibleOnly' => false, //@todo - only nodes which are visible are created - to reduce amount of html sent to the client
            'lang' => URL_LANG,
            'wrapper' => false,
            'setCssId' => false,
            'cssIdPrefix' => 'menu-item',
            'showCount' => false,
            'iconUrlPath' => '',
            'iconAsImage' => false,
            'SmartAdminLauncher' => null,
            'showExpandButtons' => false,
            'overflow' => false,
            'level03ButtonMore' => true,
            'level02ParentHeading' => null,
            'allowOverflowFromScreenWidth' => 0,
        );
        $options = array_merge($default, $options);
                
        // make some initializations on a new call of the function
        if($level == 0) {
            // get active item url
            $options['activeItemUrl'] = null;
            if ($options['activeItem'] !== null) {
                $options['activeItemUrl'] = App::getUrl(array(
                    'locator' => $options['activeItem'],
                    'lang' => $options['lang']
                ));
            }
            // reset static variable
            $activeBranch = false; 
            // prepare opened items
            $options['openedItems'] = (array)$options['openedItems'];
            if (!empty($options['openedItems'])) {
                $options['openedItems'] = array_flip($options['openedItems']);
            }
            // prepare label wrappers
            $options['labelWrappers'] = (array)$options['labelWrappers'];
            $options['labelWrappers']['l'] = '';
            if (in_array('l', $options['labelWrappers'])) {
                $options['labelWrappers']['l'] = '<span class="l"></span>';
            }
            $options['labelWrappers']['r'] = '';
            if (in_array('r', $options['labelWrappers'])) {
                $options['labelWrappers']['r'] = '<span class="r"></span>';
            }
            $options['labelWrappers']['w'] = in_array('w', $options['labelWrappers']);
            // prepare list wrappers levels
            if (!empty($options['listWrapperLevels'])) {
                $options['listWrapperLevels'] = array_flip((array)$options['listWrapperLevels']);
            }
            // prepare url lang
            $options['urlLang'] = null;
            if (!empty($options['lang'])) {
                $options['urlLang'] = trim($options['lang'], '/') . '/'; 
            }
            // normalize urlBase
            if (!empty($options['urlBase'])) {
                $options['urlBase'] = trim($options['urlBase'], '/') . '/';
            }
            // normalize iconUrlPath
            if (!empty($options['iconUrlPath'])) {
                $options['iconUrlPath'] = '/' . ltrim($options['iconUrlPath'], '/');
                $options['iconUrlPath'] = rtrim($options['iconUrlPath'], '/') . '/';
            }
            // validate 'SmartAdminLauncher'
            if (
                !empty($options['SmartAdminLauncher'])
                && ! $options['SmartAdminLauncher'] instanceof SmartAdminLauncher
            ) {
                throw new Exception(__e(__FILE__, 'The \'SmartAdminLauncher\' option must be an instance of SmartAdminLauncher() class'));
            }
        }
        
        // increase the level of recursion (0 is the first level)
        $level++;
        
        // retrieve this level menu items 
        if ($node === null) {
            // $node is NULL on the 0-th level
            // get the first element
            $items = reset($menuData);
            // if empty (root without childs) then stop
            if (empty($items)) {
                return '';
            }
        } 
        else {
            $items = $menuData[$node];
        }
        
        // start to create output
        $html = '';
        if (
            $options['listWrapper'] 
            && (
                empty($options['listWrapperLevels']) 
                || isset($options['listWrapperLevels'][$level])
            )
        ) {
            $html .= '<div class="w">';
        }
        
        // prevent childrens of active node to be set as active
        $activeItemFound = $activeBranch; 
        
        $itemsCount = count($items);
        $itemIndex = 0;
        foreach($items as $id => $item) {
            // create <li>
            $itemIndex++;
            $class = '';
            if ($itemIndex == 1) {
                $class .= 'first ';
            }
            // if there is only one item in menu it has both .first and .last class
            if ($itemIndex == $itemsCount) {
                $class .= 'last ';
            }
            
            // check if the item is an active item
            if (
                (
                    !empty($item['locator'])
                    && (
                        $item['locator'] == $options['activeItem']
                        ||
                        $options['activeItemUrl'] !== null
                        && App::getUrl(array(
                            'locator' => $item['locator'], 
                            'lang' => $options['lang']
                        )) === $options['activeItemUrl']
                    )
                )
                ||
                (
                    empty($item['locator'])
                    && $id == $options['activeItem']
                )
            ) {
                $class .= 'active ';
                $item['passive'] = $options['passiveActiveItem'];
                $activeBranch = true;
                // prevent all other siblings on this level and descendants from 
                // being marked 'open' or 'active'
                $activeItemFound = true;
            }
            
            // check if the item is set explicitly as opened
            $isOpened = isset($options['openedItems'][$id]);
            
            // if isOpened then assign the css class
            if($isOpened) {
                $class .= 'open ';
            }
            if (isset($options['level02ParentHeading'])) {
                $heading01 = '';
                if($level == 1) {
                    $heading01 = $item['label'];
                    $options['heading01'] = $heading01;
                }    
            }
            
            // check for possible submenu
            $innerHtml = '';
            $hasSubitems = array_key_exists($id, $menuData);
            if ($hasSubitems) {
                $class .= 'has-subitems ';
                $innerHtml = self::menu($menuData, $options, $id, $level);
                if (!empty($options['depth']) && $options['depth'] <= $level) {
                    $innerHtml = '';
                }
                // if active item was found on lower levels, set this item active as well
                // Dont set 'open' on other siblings on this level nor on any further
                // sub-levels.
                if ($activeBranch && !$activeItemFound) {
                    if (!$isOpened) { // because if isOpened, then this class is assigned here above
                        $class .= 'open ';
                    }
                    // prevent siblings of active node parent to be set as active
                    $activeItemFound = true; 
                }
            }
            
            $cssIdHtml = '';
            if ($options['setCssId']) {
                $cssIdHtml =  ' id="' . $options['cssIdPrefix'] . '-' . $id. '"';
            }

            if (!empty($item['class'])) {
                $class .= $item['class'];
            }
            if (isset($options['level02ParentHeading'])) {
                if($options['heading01'] && $itemIndex == 1  && $level == 2){
                    $html .= '<li class="heading01">' . $options['heading01'] . '</li>';
                }
            }
            if (
                $options['level03ButtonMore'] 
                && $level == 3 
                && $itemIndex >= 5
            ) {
                $class .= " level-03-items-hidden";
            }
            $class = trim($class);
            $classHtml = '';
            if ($class) {
                $classHtml = ' class="' . $class . '"';
            }
            $html .= '<li' . $cssIdHtml . $classHtml . '>';
            // create label
            // - encode html entities to pass html validation
////mojo: this disallows to use images as label            
//            $item['label'] = htmlentities($item['label'], ENT_COMPAT, 'UTF-8');
            if (empty($item['passive'])) {
                // label as <a> on live items
                $target = null;
                if (!empty($item['locator'])){
                    if (substr($item['locator'], 0, 7) == 'mailto:') {
                        $href = $item['locator'];
                    }
                    // check if url is hash (#my-anchor) or http or https url
                    elseif(
                        substr($item['locator'], 0, 7) == 'http://' 
                        || substr($item['locator'], 0, 8) == 'https://'
                        || substr($item['locator'], 0, 1) == '#'
                    ) {
                        $href = $item['locator'];
                        if (Validate::externalUrl($href)) {
                            $target = '_blank';
                        }
                    } 
                    else {
                        if ($item['locator'] == $options['homeSlug']) {
                            // apply possible parent url
                            $href = URL_ROOT . '/' . rtrim($options['urlLang'], '/');
                        }
                        else {
                            // apply possible parent url
                            $href = $options['urlBase'] . ltrim($item['locator'], '/');
                            // add lang only in case that where is not a lang prefix in relative 
                            // url already, e.g. /en/my-page
                            if (substr($href, 2, 1) !== '/') {
                                $href = $options['urlLang'] . $href;
                            }
                            $href = URL_ROOT . '/' . $href;
                        }
                    }
                }
                else {
                    // make url from id
                    $href = $id;
                    // apply possible parent url
                    $href = URL_ROOT . '/' . $options['urlLang'] . $options['urlBase'] . $href;
                }
                // apply App::getUrl() to normalize trailing slash
                $href = App::getUrl(array(
                    'locator' => $href, 
                    'lang' => $options['lang']
                ));
                // build the link
                $html .= '<a href ="' . $href . '"';
                if ($target) {
                    $html .= ' target="' . $target . '"';
                }
                $html .= ' class ="label"';
                if (
                    !empty($options['SmartAdminLauncher'])
                    && Validate::intNumber($id)
                ) {
                    $html .= $options['SmartAdminLauncher']->markRecord($id);
                }
                $html .= '>';
                if (!empty($item['icon'])) {
                    if ($options['iconAsImage']) {
                        $html .= '<span class="menu-img-wr"><img class="icon" src="' . $options['iconUrlPath'] . $item['icon'] . '"/></span>';
                    }
                    else {
                        $html .= '<span class="icon" style="background-image: url(\'' . $options['iconUrlPath'] . $item['icon'] . '\')"></span>';
                    }
                }
                $html .= $options['labelWrappers']['l'];
                if ($options['labelWrappers']['w']) {
                    $html .= '<span class="w">' . $item['label'] . '</span>';
                }
                else {
                    $html .= $item['label'];
                }
                $html .= $options['labelWrappers']['r'];
                if (
                    $options['showCount']
                    && isset($item['count'])
                ) {
                    $html .= '<span class="c">(' . $item['count'] . ')</span>';
                }
                if ($hasSubitems && $options['showExpandButtons']) {
                    $html .= '<span class="expand-button"></span>';
                }
                $html .= '</a>';
            }
            else {
                // label as <span> on passive items
                $html .= '<span class ="label"';
                if (
                    !empty($options['SmartAdminLauncher'])
                    && Validate::intNumber($id)
                ) {
                    $html .= $options['SmartAdminLauncher']->markRecord($id);
                }
                $html .= '>';
                $html .= $options['labelWrappers']['l'];
                if ($options['labelWrappers']['w']) {
                    $html .= '<span class="w">' . $item['label'] . '</span>';
                }
                else {
                    $html .= $item['label'];
                }
                $html .= $options['labelWrappers']['r'];
                if (
                    $options['showCount']
                    && isset($item['count'])
                ) {
                    $html .= '<span class="c">(' . $item['count'] . ')</span>';
                }
                $html .= '</span>';
            }
            $html .= $innerHtml;
            $html .= '</li>';
            // if there is multiple third level subcategories add more categories button to the last position
            if (
                $options['level03ButtonMore'] 
                && $level == 3 
                && $itemsCount >= 5
                && $itemIndex == $itemsCount
            ) {
                $html .= '<li class="level-03-button-more">' . __(__FILE__, 'Ďalšie kategórie') . '</li>';
            }
        }
        
        // add overflow item label and submenu 
        if (
            $options['overflow']
            && $level === 1
        ) {
            $html .= '<li class="overflow" style="display:none;">';
            // label
            $overflowLabel = '&hellip;'; //'&vellip;'
            if (is_string($options['overflow'])) {
                $overflowLabel = $options['overflow'];
            }
            $html .= '<span class ="label" >';
            $html .= $options['labelWrappers']['l'];
            if ($options['labelWrappers']['w']) {
                $html .= '<span class="w">' . $overflowLabel . '</span>';
            }
            else {
                $html .= $overflowLabel;
            }
            $html .= $options['labelWrappers']['r'];
            if ($options['showCount']) {
                $html .= '<span class="c">()</span>';
            }
            $html .= '</span>';
            // submenu
            $html .= '<ul class="level-02">';
            if (
                $options['listWrapper'] 
                && (
                    empty($options['listWrapperLevels']) 
                    || isset($options['listWrapperLevels'][$level])
                )
            ) {
                $html .= '<div class="w"></div>';
            }
            $html .= '</ul>';
            $html .= '</li>';
        }
        
        // close ul and its possible wrapper
        if (
            $options['listWrapper'] 
            && (
                empty($options['listWrapperLevels']) 
                || isset($options['listWrapperLevels'][$level])
            )
        ) {
            $html .= '</div>';
        }

        // add the ul round the generated list content
        $uniqueClass = '';
        $ulClass = 'level-' . sprintf('%02d', $level);
        if ($level === 1) {
            if ($activeBranch === false) {
                $ulClass .= ' inactive';
            }
            if ($options['overflow']) {
                $uniqueClass = uniqid('menu-');
                $ulClass .= ' ' . $uniqueClass;
            }
        }
        $html = '<ul class="' . $ulClass . '">' . $html . '</ul>';
        
        if ($level > 1) {
            // this is returned from non-zero levels (during the recursion)
            return $html;
        }

        // this is returned from the 0-th level only
        if ($options['wrapper']) {
            $class = 'nav';
            if (is_string($options['wrapper'])) {
                $class = $options['wrapper'];
            }
            $html = '<div class="' . $class . '">' . $html . '</div>';
        }
        
        // add overflow menu script
        if (
            $options['overflow']
            && $level === 1
        ) {
            App::setJsFiles(array(
                '/app/js/vendors/jquery.min.js',
                '/app/js/libs/Validate.js',
                '/app/js/libs/OverflowMenu.js',
            ));
            $jsOptions = json_encode(array(
                'selector' => '.' . $uniqueClass,
                'allowOverflowFromScreenWidth' => $options['allowOverflowFromScreenWidth'],
            ));
            App::setJs(
                'jQuery(function() {' .
                    'try {' .
                        'new Run.App_.OverflowMenu(' . $jsOptions . ');' .
                    '} ' .
                    'catch(error) {' .
                        'console.error(error);' .
                    '}' .
                '});'
            );
        }
        
        return $html;
    }    
    
    /**
     * Generates menu for provided text. The menu is generated accordint to hierarchy 
     * of h1-h6 tags included in text. It can be created also flat (ignoring the 
     * hierarchy of h1-h6 tags).
     * 
     * ATTENTION: The text itself is changed, there are anchors inserted
     * for each of h1-h6 tags. If the tag content contains an anchor already, then
     * that existing anchor name is used and no new is generated.
     * 
     * @param string& $text Text for which the menu is generated. Passed by reference.
     *      It is changed by the method
     * @param array $options Options passed to function Html::menu() plus following:
     *      - 'headingTagsRange' (string) Range of heading tags to be considered for menu.
     *          E.g. '1-3' means that only tags h1, h2, h3 are considered, '1' means
     *          that only tags h1 are considered. If invalid specification provided 
     *          then it is set to default value. Defaults to '1-6'.
     *      - 'headingTagsClass' (string) Only headings having this class are 
     *          considered for menu. E.g. 'text-menu-included' could be used.
     *          If empty then not considered. Defaults to NULL.
     *      - 'ignoredHeadingTagsClass' (string) Headings having this class are 
     *          NOT considered for menu, they are ignored.  If empty then not 
     *          considered. Defaults to 'text-menu-ignored'.
     *      - 'headingLabelAttribute' (string) Name of attribute with an alternative 
     *          heading label. If heading tag has this attribute then it is included
     *          in text menu with label (text) provided as value of attribute. 
     *          If heading tag has not this attribute then heading text itself is
     *          used as label in text menu. Defaults to 'data-text-menu-label'.
     *          ATTENTION: HTML entities (including double quotes) in value of the 
     *          attribute must be encoded by htmlentities().
     *      - 'flat' (bool) If TRUE then there are no nested levels for h2-h6.
     *          All headings are created as items of the first level. Defaults to FALSE.
     * 
     * @return string Html code of menu
     */
    public static function textMenu(&$text, $options = array()) {
        $defaults = array(
            'headingTagsRange' => '1-6',
            'headingTagsClass' => null,
            'ignoredHeadingTagsClass' => 'text-menu-ignored',
            'headingLabelAttribute' => 'data-text-menu-label',
            'flat' => false,
        );
        $options = array_merge($defaults, $options);
        if (!preg_match('/(?:[1-5]-)?[1-6]/', $options['headingTagsRange'])) {
            $options['headingTagsRange'] = $defaults['headingTagsRange'];
        }
        $regex = '/(<h([' . $options['headingTagsRange'] . '])(?:>|[^a-z>][^>]*>))(.*?)<\/h[' . $options['headingTagsRange'] . ']>/is';
        $matches = array();
        preg_match_all($regex, $text, $matches, PREG_SET_ORDER | PREG_OFFSET_CAPTURE);
        if (empty($matches)) {
            return '';
        }
        // insert anchors into content and create menu data
        $maxIndex = count($matches) - 1;
        $names = array();
        $menu = array();
        $classRegex = '/\sclass="([^"]*)"/i';
        $headingTagsClassRegex = '';
        if (!empty($options['headingTagsClass'])) {
            $headingTagsClassRegex = '/(?:^|\s)' . preg_quote($options['headingTagsClass'], '/') . '(?:\s|$)/';
        }
        $ignoredHeadingTagsClassRegex = '';
        if (!empty($options['ignoredHeadingTagsClass'])) {
            $ignoredHeadingTagsClassRegex = '/(?:^|\s)' . preg_quote($options['ignoredHeadingTagsClass'], '/') . '(?:\s|$)/';
        }
        $headingLabelAttributeRegex = '';
        if (!empty($options['headingLabelAttribute'])) {
            $headingLabelAttributeRegex = '/' . preg_quote($options['headingLabelAttribute'], '/') . '="([^"]+)"/';
        }
        for ($i = $maxIndex; $i >= 0; $i--) {
            $match = $matches[$i];
            $classMatch = array();
            $hasClass = false;
            if (
                !empty($options['headingTagsClass'])
                || !empty($options['ignoredHeadingTagsClass'])
            ) {
                $hasClass = preg_match($classRegex, $match[1][0], $classMatch);
            }
            // include only headings with specified class
            if (
                !empty($options['headingTagsClass'])
                && (
                    !$hasClass
                    || !preg_match($headingTagsClassRegex, $classMatch[1])
                )
            ) {
                continue;
            }
            // igmore headings with specified class
            if (
                !empty($options['ignoredHeadingTagsClass'])
                && $hasClass
                && preg_match($ignoredHeadingTagsClassRegex, $classMatch[1])
            ) {
                continue;
            }
            // get label
            $label = '';
            $labelMatch = array();
            if (
                !empty($options['headingLabelAttribute'])
                && preg_match($headingLabelAttributeRegex, $match[1][0], $labelMatch)
            ) {
                $label = trim($labelMatch[1]);
            }
            if (!$label) {
                $label = Sanitize::htmlToText($match[3][0], false);
            }
            // add anchor into content
            // - try to find explicit anchor in label
            $anchorMatch = array();
            if (
                preg_match('/<a[^a-z][^>]*name="([^"]*)"[^>]*>/i', $match[3][0], $anchorMatch)
                && $anchorMatch[1] !== ''
            ) {
                $name = $anchorMatch[1];
            }
            // - otherwise create implicit unique anchor name
            else {
                $name = $nameRoot = 'text-' . Str::slugize($label);
                $j = 0;
                while (isset($names[$name])) {
                    $name = $nameRoot . '-' . ++$j;
                }
                // - insert the implicit anchor
                $text = substr($text, 0, $match[3][1]) 
                    . '<a name="' . $name . '"></a>' 
                    . substr($text, $match[3][1]);
            }
            $names[$name] = true;
            // create menu data
            $menu[$i] = array(
                'label' => $label,
                'name' => $name, //anchor name
                'locator' => '#' . $name,
                'level' => $options['flat'] ? 1 : (int)$match[2][0]
            );
        }
        if (empty($menu)) {
            return '';
        }
        $menu = array_reverse($menu);
        // normalize menu levels to start from 1
        $topLevel = $previousLevel = $menu[0]['level'];
        $level = 1;
        foreach ($menu as &$item) {
            // normalize level
            $actualLevel = $item['level'];
            if (
                $actualLevel == $previousLevel
                || 
                $actualLevel < $previousLevel 
                && $actualLevel > $level
            ) {
                $item['level'] = $level;
            }
            elseif ($actualLevel < $previousLevel) {
                if ($actualLevel < $topLevel) {
                    $topLevel = $actualLevel;
                }
                if ($actualLevel == $topLevel) {
                    $item['level'] = $level = 1;
                }
                else {
                    $level--;
                    if ($level < 1) {
                        $level = 1;
                    }
                    $item['level'] = $level;
                }
            }
            else {
                $item['level'] = ++$level; 
            }
            $previousLevel = $actualLevel;
        }
        unset($item); // unset reference
        // create nested menu data (use fragments as ids of tree data, only the top level store under id 1)
        $menuData = array();
        $levelIds = array(1 => 1);
        foreach ($menu as $item) {
            $menuData[$levelIds[$item['level']]][$item['name']] = $item;
            $levelIds[$item['level'] + 1] = $item['name'];
        }
        return self::menu($menuData, $options);
    }
        
    /**
     * Creates HTML code of menucoolSlider
     * 
     * @see http://www.menucool.com/javascript-image-slider
     * 
     * @param array $slides List of slider items specified by following properties:
     *      - 'image' (string) Image url path
     *      - 'locator' (string) Optional. Slide link url. If provided then slide is created 
     *          with
     *      - 'text' (string) Optional. Slide text
     * 
     * @todo - this need some bugfix of styles/js/display - is broken on screen
     */
    public static function menucoolSlider($slides, $options = array()) {
        $defaults = array(
            'class' => 'menucoolslider',
            'sliderId' => uniqid('menucool-slider-'),
            'startSlide' => 0,
            'effect' => "1,4,5,6,7,8,9,12,13,14,15,17", //@todo App::getSetting('App', 'slider.effect') - translate to number
            'effectRandom' => false,
            'pauseTime' => (float)App::getSetting('App', 'slider.slideTime') * 1000,
            'transitionTime' => (float)App::getSetting('App', 'slider.transitionTime') * 1000,
            'slices' => 11,
            'boxes' => 7,
            'hoverPause' => (bool)App::getSetting('App', 'slider.stopOnHover') ? 10 : 0,
            'autoAdvance' => (bool)App::getSetting('App', 'slider.autoPlay'),
            'captionOpacity' => 0.6,
            'captionEffect' => 'rotate',
            'thumbnailsWrapperId' => 'thumbs',
            'm' => false,
            'license' => 'b6t80'
        );
        $options = array_merge($defaults, $options);
        $slideDefaults = array(
            'image' => null,
            'locator' => null,
            'text' => null,
            // internal
            'textId' => null,
        );
        // normalize slides
        $slides = (array)$slides;
        $tmp = array();
        foreach ($slides as $slide) {
            if (empty($slide['image'])) {
                continue;
            }
            $slide = array_merge($slideDefaults, $slide);
            if ($slide['text']) {
                $slide['textId'] = uniqid('text-');
            }
            $tmp[] = $slide;
        }
        $slides = $tmp;
        return App::loadView('App', 'Html/menucoolSlider', array(
            'slides' => $slides,
            'options' => $options,
        ));
    }
    
    /**
     * Creates HTML code of wowSlider
     * 
     * @see http://wowslider.com/
     * 
     * To add more wowslider effects or other properties, download windows wizard
     * from http://wowslider.com/, create a new slider with required effect and
     * copy from generated /engine1/script.js file to /app/js/vendors/wowslider/ws_{effect}.js
     * 
     * 
     * @param array $slides List of slider items specified by following properties:
     *      - 'image' (string) Image url path
     *      - 'thumb' (string) Slide thumb url path. If provided then it is used 
     *          on slider navigation bullets mouse hover.
     *      - 'locator' (string) Optional. Slide link url. If provided then slide is 
     *          created with link.
     *      - 'title' (string) Optional. Slide title
     *      - 'text' (string) Optional. Slide text
     *      - 'id' (integer) Optional. Needed only in case that option 'SmartAdminLauncher'
     *          is provided. If 'id' is not provided then 'SmartAdminLauncher' is ignored.
     * 
     * @param array $options Following are available:
     *      - 'class' (string) Additional css class.
     *      - 'startHtml' (string) Html code inserted just at the start of the main container block.
     *      - 'endHtml' (string) Html code inserted just at the end of the main container block.
     *          Used mostly for custom backgrounds under title/label/navigation and 
     *          keep possibility to set z-index
     *      - 'SmartAdminLauncher' (SmartAdminLauncher) Instance of SmartAdminLauncher class 
     *          to enable launching admin for menu items. If provided $slides must
     *          have 'id' defined, otherwise 'SmartAdminLauncher' is ignored. Defaults to NULL.
     *      - 'id' (string) //@todo
     *      - 'effect' (string) Avaliabele are 'basic', 'blur', 'book', 'brick',
     *          'cube', 'domino', 'fade', 'parallax', 'slices', 'stack', 'stack_vertical'. 
     *          Defaults to setting App.slider.efect.
     *      - 'prev' => '',
     *      - 'next' => '',
     *      - 'duration' => defaults to setting App.slider.transitionTime,
     *      - 'delay' => defaults to setting App.slider.slideTime,
     *      - 'width' => 940,
     *      - 'height' => 314,
     *      - 'autoPlay' => defaults to setting App.slider.autoPlay,
     *      - 'autoPlayVideo' => false,
     *      - 'playPause' => false,
     *      - 'stopOnHover' => defaults to setting App.slider.stopOnHover,
     *      - 'loop' => false,
     *      - 'bullets' => true,
     *      - 'caption' => true,
     *      - 'captionEffect' (string) Available are 'fade', 'parallax'. Defaults to 'fade'.
     *      - 'controls' => false,
     *      - 'responsive' => 1,
     *      - 'fullScreen' => false,
     *      - 'onBeforeStep' => 0,
     *      - 'images' => 0
     */
    public static function wowSlider($slides, $options) {
        $defaults = array(
            'class' => null,
            'startHtml' => null,
            'endHtml' => null,
            'SmartAdminLauncher' => null,
            //'id' => 'wowslider-container1', //uniqid('wow-slider-'),
            'effect' => App::getSetting('App', 'slider.effect'), // 'cube', 'slices'
            'prev' => '',
            'next' => '',
            'duration' => (float)App::getSetting('App', 'slider.transitionTime') * 1000,
            'delay' => (float)App::getSetting('App', 'slider.slideTime') * 1000,
            'width' => 940,
            'height' => 314,
            'autoPlay' => (bool)App::getSetting('App', 'slider.autoPlay'),
            'autoPlayVideo' => false,
            'playPause' => false,
            'stopOnHover' => (bool)App::getSetting('App', 'slider.stopOnHover'),
            'loop' => false,
            'bullets' => true,
            'caption' => true,
            'captionEffect' => 'fade', // 'parallax', 'fade'
            'controls' => false,
            'responsive' => 1,
            'fullScreen' => false,
            'onBeforeStep' => 0,
            'images' => 0
        );
        $options = array_merge($defaults, $options);
        // validate 'SmartAdminLauncher'
        if (
            !empty($options['SmartAdminLauncher'])
            && ! $options['SmartAdminLauncher'] instanceof SmartAdminLauncher
        ) {
            throw new Exception(__e(__FILE__, 'The \'SmartAdminLauncher\' option must be an instance of SmartAdminLauncher() class'));
        }
        $slideDefaults = array(
            'image' => null,
            'thumb' => null,
            'locator' => null,
            'title' => null,
            'text' => null,
        );
        // normalize slides
        $slides = (array)$slides;
        $tmp = array();
        foreach ($slides as $slide) {
            if (empty($slide['image'])) {
                continue;
            }
            $slide = array_merge($slideDefaults, $slide);
            $tmp[] = $slide;
        }
        $slides = $tmp;
        // get js file of required effect
        //rblb//$jsFile = '/app/js/vendors/wowslider/ws_' . $options['effect'] . '.js';
        $jsFile = '/app/js/vendors/wowslider/' . $options['effect'] . '_script.js';
        if (!is_readable(ROOT . $jsFile)) {
            throw new Exception('Missing js file for wowSlider effect %s', $options['effect']);
        }
        return App::loadView('App', 'Html/wowSlider', array(
            'slides' => $slides,
            'options' => $options,
            'jsFile' => $jsFile,
        ));
    }
    
    /**
     * Creates HTML code of bxSlider
     * 
     * @see http://bxslider.com/
     * 
     * @param array $slides List of slider items containing fields used as inserts 
     *      in 'sliderTemplate' option (see default value of this option) plus following field:
     *      specified by following properties:
     *      - 'locator' (string) Optional. Slide link url. If provided then slide is 
     *          created with link.
     *      - 'id' (integer) Optional. Needed only in case that option 'SmartAdminLauncher'
     *          is provided. If 'id' is not provided then 'SmartAdminLauncher' is ignored.
     * 
     * @param array $options Following are available:
     *      - 'class' (string) Additional css class. Defaults to NULL.
     *      - 'id' (string) Slider css id. Defaults to random unuique id created by uniqid('bxslider-').
     *      - 'jsVariable' (string) Name for JS variable to store the slider instance into.
     *          The variable is created in global scope as window['my-variable']. Its good
     *          practise to access also this way, e.g. window['your-variable-123']. Defaults
     *          to NULL.
     *      - 'slideTemplate' (string|function) Slide template html string or a anonymous function
     *          getting slide data on input and returning slide template html string.
     *          Use keys present in $slides as inserts in template html. Locator wrapper 
     *          is added implicitly if locator is nonempty. 
     *          Defaults to '<img src=":image:"><div class="slider-text">:text:< /div>'.
     *      - 'SmartAdminLauncher' (SmartAdminLauncher) Instance of SmartAdminLauncher class 
     *          to enable launching admin for menu items. If provided $slides must
     *          have 'id' defined, otherwise 'SmartAdminLauncher' is ignored. Defaults to NULL.
     *      - any other option which can be found on http://bxslider.com/options (150601).
     *          Following are predefined: 'autoStart' = 'auto' = setting App.slider.autoPlay, 
     *          'pause' = setting App.slider.slideTime, 'speed' = setting App.slider.transitionTime,
     *          'autoHover' = setting App.slider.stopOnHover, 'pager' = FALSE, 
     *          'adaptiveHeight' = TRUE
     * 
     * @return type
     */
    public static function bxSlider($slides, $options) {
        $defaults = array(
            'class' => null,
            'id' => uniqid('bxslider-'),
            'jsVariable' => null,
            'slideTemplate' => '<img src=":image:"><div class="slider-text">:text:</div>',
            'SmartAdminLauncher' => null,
            // js options
            'autoStart' => (bool)App::getSetting('App', 'slider.autoPlay'),
            'auto' => (bool)App::getSetting('App', 'slider.autoPlay'),
            'pause' => (float)App::getSetting('App', 'slider.slideTime') * 1000,
            'speed' => (float)App::getSetting('App', 'slider.transitionTime') * 1000,
            'autoHover' => (bool)App::getSetting('App', 'slider.stopOnHover'),
            'pager' => false,
            'adaptiveHeight' => true,
        );
        $options = array_merge($defaults, $options);
        // validate 'SmartAdminLauncher'
        if (
            !empty($options['SmartAdminLauncher'])
            && ! $options['SmartAdminLauncher'] instanceof SmartAdminLauncher
        ) {
            throw new Exception(__e(__FILE__, 'The \'SmartAdminLauncher\' option must be an instance of SmartAdminLauncher() class'));
        }
        $slideDefaults = array(
            'locator' => null,
        );
        // normalize slides
        $slides = (array)$slides;
        $tmp = array();
        foreach ($slides as $slide) {
            $slide = array_merge($slideDefaults, $slide);
            $tmp[] = $slide;
        }
        $slides = $tmp;
        if (empty($slides)) {
            return '';
        }
        elseif (count($slides) === 1 ) {
            $options['auto'] = false;
            $options['pager'] = false;
        }
        return App::loadView('App', 'Html/bxSlider', array(
            'slides' => $slides,
            'options' => $options,
        ));
    }    
    
    /**
    /**
     * Creates HTML code of owlCarousel
     * 
     * @see https://owlcarousel2.github.io/OwlCarousel2/
     * 
     * @param array $slides List of slider items containing fields used as inserts 
     *      in 'sliderTemplate' option (see default value of this option) plus following field:
     *      specified by following properties:
     *      - 'locator' (string) Optional. Slide link url. If provided then slide is 
     *          created with link.
     *      - 'id' (integer) Optional. Needed only in case that option 'SmartAdminLauncher'
     *          is provided. If 'id' is not provided then 'SmartAdminLauncher' is ignored.
     * 
     * @param array $options Following are available:
     *      - 'class' (string) Additional css class. Defaults to NULL.
     *      - 'id' (string) Slider css id. Defaults to random unuique id created by uniqid('owl-carousel-').
     *      - 'slideTemplate' (string|function) Slide template html string or a anonymous function
     *          getting slide data on input and returning slide template html string.
     *          Use keys present in $slides as inserts in template html. Generated slide html is
     *          wrapped by <div class="slide">{slideHtml}</div>, if 'locator' is 
     *          nonempty, then <a class"slide">{slideHtml}</a>. Ignored if 'slideView' is provided.
     *          Defaults to '<div class="slide-image" style="background-image: url(\':image:\')"></div><div class="slide-text">:text:</div>'.
     *      - 'slideView' (string|array) Name of view for slide rendering. If string 
     *          then the view is searched under App module. To provide views from 
     *          other modules use array like array('module' => ..., 'name' => ...).
     *          If provided then option 'slideTemplate' is ignored. Both $slides and
     *          $options are passed to view as 'slides' and 'options' params. Generated slide html is
     *          wrapped by <div class="slide">{slideHtml}</div>, if 'locator' is 
     *          nonempty, then <a class"slide">{slideHtml}</a>. Defaults to NULL.
     *      - 'SmartAdminLauncher' (SmartAdminLauncher) Instance of SmartAdminLauncher class 
     *          to enable launching admin for menu items. If provided $slides must
     *          have 'id' defined, otherwise 'SmartAdminLauncher' is ignored. Defaults to NULL.
     *      - any other option which can be found on https://owlcarousel2.github.io/OwlCarousel2/docs/api-options.html (171009).
     *          Following are predefined: 'autoStart' = 'auto' = setting App.slider.autoPlay, 
     *          'pause' = setting App.slider.slideTime, 'speed' = setting App.slider.transitionTime,
     *          'autoHover' = setting App.slider.stopOnHover, 'pager' = FALSE, 
     *          'adaptiveHeight' = TRUE
     * 
     * @return type
     */
    public static function owlCarousel($slides, $options) {
        $defaults = array(
            'class' => null,
            'id' => uniqid('owl-carousel-'),
            'slideTemplate' => '<div class="slide-image" style="background-image: url(\':image:\')"></div><div class="slide-content"><div class="slide-text">:text:</div></div>',
            'slideView' => null,
            'SmartAdminLauncher' => null,
            // js options
            'items' => 1,
            //'animateOut' => 'fadeOut',
            'autoplay' => (bool)App::getSetting('App', 'slider.autoPlay'),
            'autoplayTimeout' => (float)App::getSetting('App', 'slider.transitionTime') * 1000 
                + (float)App::getSetting('App', 'slider.slideTime') * 1000,
            'autoplaySpeed' => (float)App::getSetting('App', 'slider.transitionTime') * 1000,
            'autoplayHoverPause' => (bool)App::getSetting('App', 'slider.stopOnHover'),
            'loop' => true,
        );
        $options = array_merge($defaults, $options);
        // normalize view
        if (
            !empty($options['slideView'])
            && is_string($options['slideView'])
        ) {
            $options['slideView'] = array('module' => 'App', 'name' => $options['slideView']);
        }
        // validate 'SmartAdminLauncher'
        if (
            !empty($options['SmartAdminLauncher'])
            && ! $options['SmartAdminLauncher'] instanceof SmartAdminLauncher
        ) {
            throw new Exception(__e(__FILE__, 'The \'SmartAdminLauncher\' option must be an instance of SmartAdminLauncher() class'));
        }
        $slideDefaults = array(
            'locator' => null,
        );
        // normalize slides
        $slides = (array)$slides;
        $tmp = array();
        foreach ($slides as $slide) {
            $slide = array_merge($slideDefaults, $slide);
            $tmp[] = $slide;
        }
        $slides = $tmp;
        if (empty($slides)) {
            return '';
        }
        elseif (count($slides) === 1 ) {
            $options['auto'] = false;
            $options['pager'] = false;
        }
        return App::loadView('App', 'Html/owlCarousel', array(
            'slides' => $slides,
            'options' => $options,
        ));
    }    
    
    /**
     * Creates HTML code of photogallery
     * 
     * @param array $images Array of image records containing items 'file', 'thumb' and 'name'.
     *      Where 'file' and 'thumb' must contain entire valid url path. If you create 
     *      your own view you can provide any other items of course and display them.
     * @param array $options Following are available.
     *      - 'class' (string) Css class of top container. Defaults to 'photogallery'.
     *      - 'id' (string) Css id of top container. Defaults to uniqid('photogallery-').
     *      - 'view' (string|array) Name of view for photogallery rendering. If string 
     *          then the view is searched under App module. To provide views from 
     *          other modules use array like array('module' => ..., 'name' => ...)
     *          Defaults to 'Html/photogallery'
     *      - 'imagesView' (string|array) Name of view for photogallery images rendering. 
     *          If provided then used to generate photogallery images code. If omitted
     *          then default code for images is generated. If provided as string 
     *          then the view is searched under App module. To provide views from 
     *          other modules use array like array('module' => ..., 'name' => ...)
     *          Defaults to NULL.
     *      - 'columns' (int) @deprecated Number of columns to be created in gallery layout.
     *          Each column is enclosed into row element to ensure proper behaviour.
     *          If 0 then ignored and gallery items are created without separating
     *          row elements. Defaults to 0.
     *      - 'thumbWidth' (string) Thumbs width definition, e.g. '150px', '10em'.
     *          If empty then it is ignored and thumbs are sized according
     *          styles. Defaults to NULL.
     *      - 'thumbHeight' (string) Thumbs height definition, e.g. '150px', '10em'.
     *          If empty then it is ignored and thumbs are sized according
     *          styles. Defaults to NULL.
     *      - 'showTitle' (bool) If TRUE then each gallery items has title created
     *          from image name. Defaults to FALSE.
     *      - 'smartAdminLauncherRecordAttribute' (string) Attribute string returned by
     *          SmartAdminLauncher::markRecord(). Use this to allow interactive 
     *          editing of photogallery source from frontend. Defaults to NULL.
     * 
     *      NOTE: All other items set in options are forwarder to js library (fancybox).
     *          Dot syntax can be used to nest, e.g. helpers.buttons.
     *      
     * @return string Html
     */
    public static function photogallery($images, $options = array()) {
        $defaults = array(
            'class' => 'photogallery',
            'id' => uniqid('photogallery-'),
            'view' => 'Html/photogallery',
            'imagesView' => null,
            'columns' => 0, //@deprecated - kept just for backward compatibility
            'thumbWidth' => null,
            'thumbHeight' => null,
            'showTitle' => false,
            'smartAdminLauncherRecordAttribute' => null, 
        );
        $options = array_merge($defaults, $options);
        // normalize views
        if (is_string($options['view'])) {
            $options['view'] = array('module' => 'App', 'name' => $options['view']);
        }
        if (is_string($options['imagesView'])) {
            $options['imagesView'] = array('module' => 'App', 'name' => $options['imagesView']);
        }
        // set at least some defaults to force js object ({}) after conversion to json 
        $jsDefaults = array(
            'openEffect'  => 'elastic',
            'closeEffect' => 'elastic',
        );
        $options['jsOptions'] = array_merge($jsDefaults, array_diff_key($options, $defaults));
        $options['images'] = $images;
        return App::loadView($options['view']['module'], $options['view']['name'], $options);
    }
    
    //
    // SMART INDEX PROPERTIES AND METHODS
    //

    /**
     * Smart index options
     * 
     * Serves to share Html::smartIndex() options between smart index methods
     *
     * @var array
     */
    protected static $options = array();
    
    /**
     * Generates smartIndex html
     * 
     * @param array $options Folowing options can be provided:
     *      - 'view' (string|array) Name of view for smartIndex rendering. If string 
     *          then the view is searched under App module. To provide views from 
     *          other modules use array like array('module' => ..., 'name' => ...)
     *          Defaults to 'Html/smartIndex'
     *      - 'recordsView' (string|array) Name of view for smartIndex records rendering. If string 
     *          then the view is searched under App module. To provide views from 
     *          other modules use array like array('module' => ..., 'name' => ...)
     *          Defaults to 'Html/smartIndexRecords'
     *      - 'tree' (bool|string|array) Config array to initialize tree behaviour in smart index.
     *          It means recods can be moved, opened/closed, dropped into. In such a case 
     *          provided records must contain 'path' tree structure field otherwise 
     *          an exception is thrown. The array may contain following items:
     *              - 'column' (string) Name of column to display tree structure in.
     *              - 'openIcon' (string) Path to open icon. Default to @todo.
     *              - 'openClass' (string) Path to open icon. Default to @todo.
     *              - 'closeIcon' (string) Path to close icon. Default to @todo.
     *              - 'closeClass' (string) Path to close icon. Default to @todo.
     *              - 'lineIcon' (string) Path to close icon. Default to @todo.
     *              - 'lineClass' (string) Path to close icon. Default to @todo.
     *              - 'elbowIcon' (string) Path to close icon. Default to @todo.
     *              - 'elbowClass' (string) Path to close icon. Default to @todo.
     *              - 'endElbowIcon' (string) Path to close icon. Default to @todo.
     *              - 'endElbowClass' (string) Path to close icon. Default to @todo.
     *              - 'nodeClass' (string) Path to close icon. Default to @todo.
     *              - 'containerNodeIcon' (string) Path to close icon. Default to @todo.
     *              - 'containerNodeClass' (string) Path to close icon. Default to @todo.
     *              - 'leafNodeIcon' (string) Path to close icon. Default to @todo.
     *              - 'leafNodeClass' (string) Path to close icon. Default to @todo.
     *              - 'childsIndent' (string) Indent of childrens in any common css 
     *                  measure. Defaults to '10px'.
     *          If provided as a simple string then it is taken for 'column'. Defaults to FALSE. 
     *      - 'title' (string) Index title displayed in header
     *      - 'class' (string) Smart index wrapper div class (additional). Defaults to
     *          NULL.
     *      - 'records' (array) Records to be displayed. If multiple models fields are passed
     *          then they should be separated by model name qualifiers, e.g.: 'Product.id', 
     *          'Product.name', 'Manufacturer.name' (the 'primaryKey' param must be set to 
     *          'Product.id' in such case). This can be easy accomplished by option 'qualify' 
     *          of Model::find() or by option 'separate' of Model::find() and Utility::defalteArray().
     *      - 'Model' (Model) Serves to simplify index specification. If provided then
     *          'primaryKey', 'columns' and 'Paginator' are populated from provided Model.
     *          Only empty items are populated to ensure that you can set them explicitly.
     *          Defaults to NULL.
     *      - 'primaryKey' (string) Primary key field name. Defaults to 'id'.
     *      - 'columns' (array) Definition of columns like:
     * 
     *              array(
     *                  '{fieldName01}' => array(
     *                      'label' => 'My field 01 label',
     *                      'hidden' => FALSE                       //@todo
     *                      'width' => '15%'                        //@todo
     *                  ),
     *                  '{fieldName02}' => 'My field 02 label',     // 'hidden' defaults to FALSE
     *                  '{fieldName03}',                            // 'label' defaults to field name, 'hidden' to FALSE
     *                  ...
     *              )
     * 
     *          If 'columns' are not specified then they are autodetected according the 
     *          first record.
     *      - 'renderFields' (array) Array of field renderers. Each field renderer 
     *          can be defined in three ways:
     *              - as string template which is compiled by Str::fill($renderField, $record, $fieldValue).
     *              E.g. '<img src="/userfiles/eshop/product/images/%s"/'> or '<img src="/userfiles/eshop/product/images/:image_name:"/'>
     *              - as callable|function, the called method receives following arguments: 
     *              fieldValue, fieldName and recordData and must return interpreted 
     *              field value
     *              - as conversion array, defining relation between field real values
     *              and display values
     * 
     *              array(
     *                  // field render defined by callable
     *                  'order_status' => array($MyModel, 'renderOrderStatus')
     *                  // field render defined by conversion array
     *                  'active' => array(
     *                      '0' => 'No',
     *                      '1' => 'Yes',
     *                  )
     *                  ...,
     *              )
     * 
     *      - 'renderRow' (array|callable) Row render defined in two ways:
     *              - as callable with actual row record on input and returning 
     *              array of HTML attributes to be set to the row, e.g.:
     * 
     *                  array($MyModel, 'renderRow')
     *          
     *              - as an array of arrays with items 'conditions' and 'attributes'.
     *              Each of arrays is checked by its conditions agains actual row record.
     *              If the conditions are satisfied then HTML attributes are applied 
     *              to the row (see Validate::dataConditions()). E.g.:
     * 
     *                  array(
     *                      array(
     *                          'conditions' => array('order_status' => 'enum_new'),
     *                          'attributes' => array('class' => 'new'),
     *                      ),
     *                      array(
     *                          'conditions' => array('order_status' => 'enum_open'),
     *                          'attributes' => array('class' => 'open'),
     *                      ),
     *                      ...,
     *                  ),   
     * 
     *      - 'actions' (array) Definition of smart index general actions whose controls
     *          are displayed in header. Predefined are 'refresh', 'resetSort', 'resetFilter', 
     *          'lang', 'add', 'showIndex', 'showTree', 'toggleTree', 'indexParents' actions. 
     *          You can add any custom actions. Actions are displayed in the order
     *          they are defined. Action names are used as keys of associative array, 
     *          under which the action options are provided. All possible options of action 
     *          definition see below at option 'recordActions'. The 'lang' action is a bit special.
     *          You can use also TRUE value to display it with default options. 
     *      - 'recordActions' (array) Definition of record specific actions which controls
     *          are displayed in each row. Predefined are 'copy', 'edit', 'view', 'delete', 
     *          'move', 'addChild', 'indexChilds', 'moveBySelect', 'export' actions. You can add 
     *          any custom actions. Actions are displayed in the order they are defined. 
     *          Action names are used as keys of associative array, under which 
     *          the action options are provided:
     * 
     *              array(
     *                  'add' => array(
     *                      ... // list of action options (see below)
     *                  ),
     *                  'edit' => array(...),
     *                  'view' => 'url/to/view,
     *                  ...,
     *              )
     *              
     *          Urls can be defined in string form (locator) or in array form. 
     *          The primary key value is added to 'recordActions' urls in each record rows 
     *          (except of general 'actions' which does not deal with a concrete record)
     *          Following action options are available to define action:
     *              - 'url' (string|array|callable@todo) Action URL. MANDATORY for 'add', 'copy', 
     *                  'edit', 'view', 'delete', 'export'. Optional for 'refresh', 'resetSort', 
     *                  'resetFilter'. Action is displayed only if user has rights for 'url'.
     *                  If provided as callable (@todo) then url is at first generated and only after processed.
     *                  Callable method gets the record array on input and must return 
     *                  generated url either in string or array form (see App::getUrl()).
     *                  If 'url' is empty then the rights check is skipped. Defaults to NULL.
     *              - 'rights' (bool|array|callable) Has the current user rights for the action?
     *                  Here can be provided any other point of view on rights than 
     *                  "has rights on url" (which is checked if 'url' is provided).  
     *                  Can be provided as generaly applyed boolean value or can be checked
     *                  record by record if provided as data conditions array (see Validate::dataConditions()) 
     *                  or callable (both with actual record on input and returning boolean). 
     *                  The two last possibilities can be used only in case of 'recordActions'.
     *                  By default this option is not present in default at all (because 
     *                  if present, then its value is casted to boolean and if NULL...)
     *              - 'if' (bool|array|callable) If defined then action is displayed only in case that
     *                  value of this option is TRUE. If value is FALSE the action is
     *                  ignored. It can be provided also as callable or data conditions (see rights).
     *                  This is a bit duplicit to 'rights' option. Use according 
     *                  semantic meaning in situation. By default this option is not present 
     *                  in default at all (because if present, then its value is casted 
     *                  to boolean and if NULL...)
     *              - 'target' (string) Action link target value. Defauts to '_blank' for
     *                  'add', 'copy', 'edit', 'view' actions, to NULL for others.
     *                  If the link target is '_blank' then links are opened in new tabs
     *                  if the smartIndex is loaded into SmartTabFrame.
     *              - 'label' (string) Action link label (text). Inserts from record data
     *                  can be used (see Str::fill()). Defaults to NULL.
     *              - 'title' (string) action link title. Inserts from record data
     *                  can be used (see Str::fill()). Defauts to different 
     *                  titles defined in each action defaults.
     *              - 'class' (string) Action link css class. Inserts from record data
     *                  can be used (see Str::fill()). Defauts to different 
     *                  classes defined in each action defaults.
     *              - 'icon' (string) Action link icon HTML. Inserts from record data
     *                  can be used (see Str::fill()). Defaults to NULL.
     *              - 'confirmMessage' Action link confirm message. If set then 
     *                  it displays after clicking on action button and requires
     *                  confirmation. For the moment applicable only to 'delete'.
     *                  Defaults to generic confirmation message.
     *              - 'options' (array) Array of select options. If provided then action
     *                  is created as select input instead of default anchor <a>.
     *                  After change of the select input value the window.location
     *                  is changed to action URL with GET param of action name set
     *                  to new select value. If action has no url specified then 
     *                  actual window.location is used.
     *              - 'optionsTemplate' (string) Template used to render action 'options'.
     *                  Use :value: and :label: if 'options' are provided as 1D assoc. array
     *                  in form array('{value}' => '{label}'). If there is an array 
     *                  instead of {label} then use inserts in form of array path in that array
     *                  like Str::fill() does. If this option is provided then 
     *                  the action is created as bootstrap dropdown component 
     *                  instead of default anchor <a>.
     *              - 'inherit' (array) Array of actual URL options to be inherited
     *                  into action 'url'. For the moment this is used only for GET
     *                  params like: 'inherit' => array('get' => array('lang')).
     * 
     *              Following options are applicable only for actions which opens
     *              new tab in SmartTabs (e.g. 'add', 'copy', 'edit', 'view') :
     *              - 'tabTitle' (string) Inserts from record data can be used 
     *                  (see Str::fill()). Defauts to different titles defined 
     *                  in each action defaults.
     *              - 'tabClass' (string) Inserts from record data can be used 
     *                  (see Str::fill()). Defauts to different titles defined 
     *                  in each action defaults.
     *              - 'tabIcon' (string) Inserts from record data can be used 
     *                  (see Str::fill()). Defauts to different titles defined 
     *                  in each action defaults.
     *              - 'tabSingle (bool) If TRUE then single tab is created for given 
     *                  action. If FALSE then new tab is opened for each action link click.
     *                  Defaults to TRUE for 'copy', 'edit', 'view' and to FALSE for 'add'.
     *              - 'tabFrameType' (string) Name of SmartTabFrame js subclass to create
     *                  instance  for new tab smart frame. @todo
     * 
     *              Following option is applicable only for 'move' action:
     *              - 'descendingOrder' (bool) Are list items ordered descending (ORDER BY sort DESC)? 
                        Considered for drang&drop in ordered list to resolve correctly the newOrderIndex.  
     *                  Defaults to FALSE.
     *              - 'allowOnFieldFilter' (string) Field name. Moving is allowed if 
     *                  provided field name is filtered. Defaults to NULL.
     * 
     *      - 'recordDefaultAction' (string|array) Action to be launched after doubleclick on index row.
     *          It can be defined in following ways:
     *              a) name (string) of one of actions defined in 'recordActions' option
     *              b) array of such names, to cover situations then user has insuficient
     *                  rights for some more 'powerfull' action. In such a case the first
     *                  of listed actions the user has rights is used.
     *              c) array with fully defined action using options here above.
     *              d) array of such array where the first action user has rights for 
     *                  is used.
     *          Defaults to array('edit', 'view').
     *      - 'bulkActions' (array) Definition of bulk actions which are applied to 
     *          all selected records. Comma separated list of selected records ids
     *          is attached as 'ids' GET parameter to action url (GET param is prefered 
     *          over URL path ARG because of data amount it can contain). Bulk actions controls are displayed 
     *          in header. Predefined are 'editMany', 'deleteMany' actions. You can add 
     *          any custom bulk actions. They are displayed in the order they are defined. 
     *          Action names are used as keys of associative array, under which the action 
     *          options are provided. All possible options of action definition see above 
     *          at option 'recordActions'.
     *      - 'Paginator' (Paginator) Instance of Paginator class taking care of
     *          parsing paging, sorting and filtering data. In most cases it enough
     *          to set it to $Model->Paginator after calling $MyModel->find() with 
     *          option 'paginate' set to TRUE. If not set then no pagination, sort 
     *          and filter controls are generated. Defaults to NULL. 
     *      - 'paginatorOptions' (array) Options to create paginator controls and
     *          Run.App.Paginator js class instance. Any of Run.App.Paginator class
     *          options can be set here plus following:
     *              - 'filterSelectInputs' (array) List of fieds for which a selectbox
     *                  should be generated for filtering instead of default text input.
     *                  Field name are used as array keys pointing to array of select 
     *                  options, e.g.:
     * 
     *                      array(
     *                          'order_status' => arrray(
     *                              'enum_new' => __(__FILE__, 'New'),
     *                              'enum_open' => __(__FILE__, 'Open'),
     *                              ...
     *                          ),
     *                          'active' => array(
     *                              0 => __(__FILE__, 'No'),
     *                              1 => __(__FILE__, 'Yes'),
     *                          ),
     *                          ...,
     *                      ) 
     *              - 'infiniteScroll' (bool) Should be infinite scroll turned on?
     *                  If 'tree' option is nonempty then 'infiniteScroll' option
     *                  is not considered. Defaults to TRUE.
     *              - 'recordsSourceUrl' (string) URL to load infinite 
     *                  scroll records from. Defaults to NULL, means actual window 
     *                  location URL is used.
     *          Defaults to array (
     *              'allowMultisort' => true,
     *              'filterSelectInputs' => array(),
     *              'infiniteScroll' => true,
     *              'recordsSourceUrl' => null,
     *              'restoreRecords' => true,
     *              'sortUrlParam' => $options['Paginator']->getPropertySortUrlParam(),
     *              'sortDirectionSeparator' => $options['Paginator']->getPropertySortDirectionSeparator(),
     *              'sortFieldsSeparator' => $options['Paginator']->getPropertySortFieldsSeparator(),
     *              'sortResetSelector' => '.' . Sanitize::value($options['actions']['resetSort']['class']),
     *              'filterUrlParam' => $options['Paginator']->getPropertyFilterUrlParam(),
     *              'filterFieldsSeparator' => $options['Paginator']->getPropertyFilterFieldsSeparator(),
     *              'filterExpressionSeparator' => $options['Paginator']->getPropertyFilterExpressionSeparator(),
     *              'filterResetSelector' => '.' . Sanitize::value($options['actions']['resetFilter']['class']),
     *          )
     *      - 'recordsOnly' (bool) If TRUE then only records rows for infinite
     *          scroll are returned. These records are without header, index wrapper
     *          and application layout is turned off too. This is used for infinite 
     *          scrolling when 'paginatorOptions' > 'infiniteScroll' option is set TRUE
     *          (otherwise this option is ignored). Defaults to !empty($_REQUEST['recordsOnly']).
     *      - 'emptyIndexMessage' (string) Message displayed when there are no records
     *          to display.
     *      - 'headerTemplate' (string) Html template to display header. Inserts 
     *          :title:, :paginator: and :actions: can be used to insert corresponding
     *          items into header. For default value see methods $defaults array.
     */
    public static function smartIndex($options = array()) {
        $defaults = array(
            'view' => 'Html/smartIndex',
            'recordsView' => 'Html/smartIndexRecords',
            'tree' => false,
            'title' => null,
            'class' => null,
            'striped' => false,
            'records' => array(),
            'Model' => null,
            'primaryKey' => 'id',
            'columns' => array(),
            'renderFields' => array(),
            'renderRow' => array(), // array(array(array('status' => 'paid'), 'style' => 'color: #0F0', 'class' => 'paid'), array()
            'actions' => array(),
            'recordActions' => array(),
            'recordDefaultAction' => array('edit', 'view'),
            'bulkActions' => array(),
            'Paginator' => null,
            'paginatorOptions' => array(
                'allowMultiSort' => true,
                'filterSelectInputs' => array(), // options for secectbox filters: array('{field}' => array(/*options*/), ...)
                'infiniteScroll' => true,
                'recordsSourceUrl' => null,
                'restoreRecords' => true,
            ),
            'recordsOnly' => !empty($_REQUEST['recordsOnly']),
            'emptyIndexMessage' => __(__FILE__, 'No records have been found'),
            'headerTemplate' => '<div class="-run-six-title">:title:</div>:actionsLeft::messages:<div class="-run-six-controls">:paginator::actionsRight:</div>',
        );
        $options = array_merge($defaults, $options);
        $treeDefaults = array(
            'column' => null,
            'status' => 'collapsed', // expanded, collapsed
            'class' => '-run-six-tree',
            'navigationClass' => '-run-six-tree-nav',
            'elbowExpanderClass' => '-run-six-tree-elbow-expander',
            'elbowEndExpanderClass' => '-run-six-tree-elbow-end-expander',
            'elbowOpenClass' => '-run-six-tree-elbow-open',
            'elbowEndOpenClass' => '-run-six-tree-elbow-end-open',
            'elbowCloseClass' => '-run-six-tree-elbow-close',
            'elbowEndCloseClass' => '-run-six-tree-elbow-end-close',
            'elbowClass' => '-run-six-tree-elbow',
            'elbowLineClass' => '-run-six-tree-elbow-line',
            'elbowEndClass' => '-run-six-tree-elbow-end',
            'elbowEmptyClass' => '-run-six-tree-elbow-empty',
            'showNodeType' => false,
            'nodeClass' => '-run-six-tree-node',
            'expandedNodeClass' => '-run-six-tree-node-expanded',
            // 2 implicit node types (in DB node_type: enum_tree_node_leaf, enum_tree_node_branch
            'branchNodeIcon' => null,
            'branchNodeClass' => '-run-six-tree-node-branch',
            'leafNodeIcon' => null,
            'leafNodeClass' => '-run-six-tree-node-leaf',
            // if you have defined your own node type e.g. enum_tree_node_my_slide
            //'mySlideNodeIcon' => '/img/my-slide-node.png',
            //'mySlideNodeClass' => '-run-six-tree-node-my-slide',
            
            // INTERNAL USE:
            // tree root level nesting (displayed tree is mostly just subtree of bigger tree)
            'rootLevel' => 0, 
            // array of counts for each path occurence like array('-' => 6, '-1-8-' => 3, '-1-8-12-' => 7, ...)
            'pathsCounts' => array(), 
            // similar as pathsCounts but acumulates progress counts during tree building
            'pathsProgress' => array(), 
        );
        // actions defaults
        $actionsDefaults = array(
            'refresh' => array(),
            'resetSort' => array(),
            'resetFilter' => array(),
        );
        $treeActionsDefaults = array(
            'refresh' => array(),
            'resetSort' => array(),
            'resetFilter' => array(),
            'toggleTree' => array(),
        );
        $refreshActionDefaults = array(
            'url' => '',
            'label' => null,
            'title' => __(__FILE__, 'Refresh'),
            'class' => '',
            'icon' => '<i class="fa fa-refresh"></i>',
        );
        $resetSortActionDefaults = array(
            'label' => null,
            'title' => __(__FILE__, 'Reset sort'),
            'class' => 'reset-action',
            //'icon' => '<i class="fa fa-sort"></i>',
            'icon' => '<s><i class="fa fa-sort-amount-asc"></i></s>',
        );
        $resetFilterActionDefaults = array(
            'label' => null,
            'title' => __(__FILE__, 'Reser filter'),
            'class' => 'reset-action',
            'icon' => '<s><i class="fa fa-filter"></i></s>',
            //'icon' => '<span class="fa-stack"><i class="fa fa-ban fa-stack-2x"></i><i class="fa fa-filter fa-stack-1x"></i></span>',
        );
        $langActionDefaults = array(
            'ulr' => App::$urlPath,
            'options' => array_combine(App::getPropertyLangs(), App::getPropertyLangs()),
            'optionTemplate' => '<img src="/app/img/libs/lang/:label:.png" title=":label:"/>',
        );
        $addActionDefaults = array(
            'url' => null,
            'target' => '_blank',
            'label' => null,
            'title' => __(__FILE__, 'Add new'),
            'class' => '',
            //'icon' => '<i class="fa fa-plus"></i>',
            'icon' => '<i class="fa fa-plus-circle"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => false,
            'tabFrameType' => null,
        );
        $showIndexActionDefaults = array(
            'url' => null,
            'label' => null,
            'title' => __(__FILE__, 'Switch to index'),
            'class' => '',
            'icon' => '<i class="fa fa-align-justify"></i>',
            //'icon' => '<i class="fa fa-list-alt"></i>',
            //'icon' => '<i class="fa fa-list"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => false,
            'tabFrameType' => null,
        );
        $showTreeActionDefaults = array(
            'url' => null,
            'label' => null,
            'title' => __(__FILE__, 'Switch to tree'),
            'class' => '',
            'icon' => '<i class="fa fa-sitemap"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => false,
            'tabFrameType' => null,
        );
        $toggleTreeActionDefaults = array(
            'label' => null,
            'title' => __(__FILE__, 'Toggle tree'),
            'class' => '',
            'icon' => '<i class="fa fa-toggle-down"></i>',
        );
        $indexParentsActionDefaults = array(
            'url' => null,
            'label' => null,
            'title' => __(__FILE__, 'Index parents'),
            'class' => '',
//            'icon' => '<i class="fa fa-level-up"></i>',
//            'icon' => '<i class="fa fa-arrow-up"></i>',
            'icon' => '<i class="fa fa-reply fa-rotate-90"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => false,
            'tabFrameType' => null,
        );
        $exportActionDefaults = array(
            'url' => null,
            'target' => '_blank',
            'label' => null,
            'title' => __(__FILE__, 'Export'),
            'class' => '',
            'icon' => '<i class="fa fa-download"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => false,
            'tabFrameType' => null,
            'tabCreator' => false,
        );
        $importActionDefaults = array(
            'url' => null,
            'target' => '_blank',
            'label' => null,
            'title' => __(__FILE__, 'Import'),
            'class' => '',
            'icon' => '<i class="fa fa-upload"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => false,
            'tabFrameType' => null,
        );
        
        // record actions defaults
        $copyActionDefaults = array(
            'url' => null,
            'target' => '_blank',
            'label' => __(__FILE__, 'Copy'),
            'title' => __(__FILE__, 'Copy'),
//            'class' => 'glyphicon glyphicon-plus-sign',
            'icon' => '<i class="fa fa-copy"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => true,
            'tabFrameType' => null,
        );
        $duplicateActionDefaults = array(
            'url' => null,
            'target' => '_blank',
            'label' => __(__FILE__, 'Duplicate'),
            'title' => __(__FILE__, 'Duplicate'),
//            'class' => 'glyphicon glyphicon-plus-sign',
            'icon' => '<i class="fa fa-copy"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => true,
            'tabFrameType' => null,
        );
        $editActionDefaults = array(
            'url' => null,
            'target' => '_blank',
            'label' => __(__FILE__, 'Edit'),
            'title' => __(__FILE__, 'Edit'),
//            'class' => 'glyphicon glyphicon-edit',
            'icon' => '<i class="fa fa-pencil"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => true,
            'tabFrameType' => null,
        );
        $viewActionDefaults = array(
            'url' => null,
            'target' => '_blank',
            'label' => __(__FILE__, 'View'),
            'title' => __(__FILE__, 'View'),
//            'class' => 'glyphicon glyphicon-zoom-in',
            'icon' => '<i class="fa fa-eye"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => true,
            'tabFrameType' => null,
        );
        $deleteActionDefaults = array(
            'url' => null,
            'label' => __(__FILE__, 'Delete'),
            'title' => __(__FILE__, 'Delete'),
//            'class' => 'glyphicon glyphicon-remove',
            'icon' => '<i class="fa fa-trash"></i>',
            'confirmMessage' => __(__FILE__, 'Please, comfirm removal of the record'),
        );
        $moveActionDefaults = array(
            'url' => null,
            'descendingOrder' => false,
            'allowOnFieldFilter' => null,
        );
        $addChildActionDefaults = array(
            'url' => null,
            'target' => '_blank',
            'label' => __(__FILE__, 'Add child item'),
            'title' => __(__FILE__, 'Add new child'),
//            'class' => 'glyphicon glyphicon-plus-sign',
            'icon' => '<i class="fa fa-plus-circle"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => false,
            'tabFrameType' => null,
        );
        $indexChildsActionDefaults = array(
            'url' => null,
            'label' => __(__FILE__, 'Child items'),
            'title' => __(__FILE__, 'Index child items'),
//            'class' => 'glyphicon glyphicon-arrow-down',
            'icon' => null,
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => false,
            'tabFrameType' => null,
        );
        $moveBySelectActionDefaults = array(
            'url' => null,
            'label' => null,
            'title' => __(__FILE__, 'Index'),
            'class' => 'glyphicon glyphicon-new-window',
            'icon' => null,
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => false,
            'tabFrameType' => null,
        );
        
        // bulk actions
        $editManyActionDefaults = array(
            'url' => null,
            'target' => '_blank',
            'label' => null,
            'title' => __(__FILE__, 'Edit selected'),
//            'class' => 'glyphicon glyphicon-edit',
            'icon' => '<i class="fa fa-pencil"></i>',
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            'tabSingle' => true,
            'tabFrameType' => null,
        );
        $deleteManyActionDefaults = array(
            'url' => null,
            'label' => null,
            'title' => __(__FILE__, 'Delete selected'),
//            'class' => 'glyphicon glyphicon-remove',
            'icon' => '<i class="fa fa-trash"></i>',
            'confirmMessage' => __(__FILE__, 'Please, comfirm removal of seleted records'),
        );
        
        // normalize Paginator
        if (
            $options['Paginator'] === null
            && !empty($options['Model'])
        ) {
            $options['Paginator'] = $options['Model']->Paginator;
        }
        // normalize view
        if (is_string($options['view'])) {
            $options['view'] = array('module' => 'App', 'name' => $options['view']);
        }
        // normalize recordsView
        if (is_string($options['recordsView'])) {
            $options['recordsView'] = array('module' => 'App', 'name' => $options['recordsView']);
        }
        // normalize tree
        if ($options['tree']) {
            if (is_string($options['tree'])) {
                $options['tree'] = array('column' => $options['tree']);
            }
            $options['tree'] = array_merge($treeDefaults, $options['tree']);
            $options['tree']['status'] = strtolower($options['tree']['status']);
        }
        // validate tree
        if (!empty($options['tree']) && empty($options['tree']['column'])) {
            throw new Exception(__e(__FILE__, 'Unspecified tree column'));
        }
        // normalize class
        if ($options['class']) {
            $options['class'] = ' ' . $options['class'];
        }        
        // normalize records
        $options['records'] = Arr::deflate($options['records'], array('ignorePlain' => 1));
        // validate records (according to the first)
        $record = reset($options['records']);
        if ($record !== false && empty($record[$options['primaryKey']])) {
            throw new Exception(__e(__FILE__, 'Missing primary key field in records'));
        }
        // In case of tree:
        // - check for record 'path' tree structrure field and 
        // - for tree column presence
        // - find root level nesting
        // - find path counts
        if ($options['tree']) {
            if ($record !== false && empty($record['path'])) {
                throw new Exception(__e(__FILE__, 'Missing path field in records'));
            }
            if ($record !== false && !array_key_exists($options['tree']['column'], $record)) {
                throw new Exception(__e(__FILE__, 'Missing tree column field'));
            }
            $options['tree']['rootLevel'] = count(Model::getParentIdsFromTreePath($record['path']));
            $options['tree']['pathsCounts'] = self::getSmartIndexTreePathsCounts($options['records']);
        }
        // autodetect columns if not specified
        if (empty($options['columns'])) {
            if (!empty($options['Model'])) {
                $options['columns'] = array_keys($options['Model']->getPropertySchema());
            }
            elseif ($record !== false) {
                $options['columns'] = array_keys($record);
            }
        }
        // normalize columns
        $tmp = array();
        foreach ($options['columns'] as $k => $v) {
            // 'field_name' => array('label' => 'Column Label', 'hidden' => false)
            if (!Validate::intNumber($k) && is_array($v)) {
                $tmp[$k] = array(
                    'label' => Sanitize::value($v['label'], $k),
                    'hidden' => Sanitize::value($v['hidden'], false), 
                );
            }
            // 'field_name' => 'Column Label'
            elseif (!Validate::intNumber($k) && !Validate::intNumber($v)) {
                $tmp[$k] = array(
                    'label' => $v,
                    'hidden' => false, 
                );
            }
            // 0 => 'field_name'
            else {
                $tmp[$v] = array(
                    'label' => Str::humanize($v, array('_', '.')),
                    'hidden' => false, 
                );
            }
        }
        $options['columns'] = $tmp;
        // normalize general actions
        if (!$options['tree']) {
            $options['actions'] = array_merge($actionsDefaults, (array)$options['actions']);
        }
        else {
            $options['actions'] = array_merge($treeActionsDefaults, (array)$options['actions']);
        }
        $tmp = array();
        foreach ($options['actions'] as $actionName => $action) {
            if ($action === false) {
                continue;
            }
            if (Validate::intNumber($actionName)) {
                throw new Exception(__e(__FILE__, 'Invalid action definition'));
            }
            if ($action && !is_array($action)) {
                // treat lang action separately
                if (
                    $actionName === 'lang'
                    && $action === true
                ) {
                    $action = array();
                }
                else {
                    $action = array('url' => $action);
                }
            }
            $action['name'] = $actionName;
            if ($actionName === 'refresh') {
                $action = array_merge($refreshActionDefaults, $action);
            }
            elseif ($actionName === 'resetSort') {
                if (empty($options['Paginator'])) {
                    continue;
                }
                $action = array_merge($resetSortActionDefaults, $action);
            }
            elseif ($actionName === 'resetFilter') {
                if (empty($options['Paginator'])) {
                    continue;
                }
                $action = array_merge($resetFilterActionDefaults, $action);
            }
            elseif ($actionName === 'lang') {
                $action = array_merge($langActionDefaults, $action);
            }
            elseif ($actionName === 'add') {
                $action = array_merge($addActionDefaults, $action);
            }
            elseif ($actionName === 'showIndex') {
                $action = array_merge($showIndexActionDefaults, $action);
            }
            elseif ($actionName === 'showTree') {
                $action = array_merge($showTreeActionDefaults, $action);
            }
            elseif ($actionName === 'toggleTree') {
                $action = array_merge($toggleTreeActionDefaults, $action);
            }
            elseif ($actionName === 'indexParents') {
                $action = array_merge($indexParentsActionDefaults, $action);
            }
            elseif ($actionName === 'export') {
                $action = array_merge($exportActionDefaults, $action);
            }
            elseif ($actionName === 'import') {
                $action = array_merge($importActionDefaults, $action);
            }
            // accept / refuse action 
            if (!self::acceptAction($action)) {
                continue;
            }
            $tmp[$actionName] = $action;
        }
        $options['actions'] = $tmp;
        // normalize record actions
        $tmp = array();
        foreach ($options['recordActions'] as $actionName => $action) {
            if ($action === false) {
                continue;
            }
            if (Validate::intNumber($actionName)) {
                throw new Exception(__e(__FILE__, 'Invalid action definition'));
            }
            if ($action && !is_array($action)) {
                $action = array('url' => $action);
            }
            $action['name'] = $actionName;
            if ($actionName === 'addChild') {
                $action = array_merge($addChildActionDefaults, $action);
            }
            elseif ($actionName === 'copy') {
                $action = array_merge($copyActionDefaults, $action);
            }
            elseif ($actionName === 'duplicate') {
                $action = array_merge($duplicateActionDefaults, $action);
            }
            elseif ($actionName === 'edit') {
                $action = array_merge($editActionDefaults, $action);
            }
            elseif ($actionName === 'view') {
                $action = array_merge($viewActionDefaults, $action);
            }
            elseif ($actionName === 'delete') {
                $action = array_merge($deleteActionDefaults, $action);
            }
            elseif ($actionName === 'move') {
                $action = array_merge($moveActionDefaults, $action);
            }
            elseif ($actionName === 'addChild') {
                $action = array_merge($addChildActionDefaults, $action);
            }
            elseif ($actionName === 'indexChilds') {
                $action = array_merge($indexChildsActionDefaults, $action);
            }
            elseif ($actionName === 'moveBySelect') {
                $action = array_merge($moveBySelectActionDefaults, $action);
            }
            // accept / refuse action 
            if (!self::acceptAction($action)) {
                continue;
            }
            $tmp[$actionName] = $action;
        }
        $options['recordActions'] = $tmp;
        
        // normalize row action
        $options['recordDefaultAction'] = (array)$options['recordDefaultAction'];
        $recordDefaultAction = array();
        foreach ($options['recordDefaultAction'] as $k => $v) {
            // just the name of action provided in $options['recordActions'] is provided
            if (Validate::intNumber($k) && is_string($v)) {
                $actionName = $v;
                // if action is not defined in recordActions or user has no rights (rights have been
                // already checked here above) then continue
                if (empty($options['recordActions'][$actionName])) {
                    continue;
                }
                $action = $options['recordActions'][$actionName];
            }
            // action options are provided
            elseif (is_array($v))  {
                // action name is provided as key of assoc array
                if (!Validate::intNumber($k)) {
                    $actionName = $k;
                    $action = $v;
                }
                // action name is provided as item 'actionName' of the array
                else {
                    $actionName = Sanitize::value($v['actionName']);
                    if (!$actionName) {
                        throw new Exception(__e(__FILE__, 'Invalid row action definition - missing actionName'));
                    }
                    $action = $v;
                }
                // accept / refuse action 
                if (!self::acceptAction($action)) {
                    continue;
                }
            }
            else {
                throw new Exception(__e(__FILE__, 'Invalid row action definition'));
            }
            // ok, finally we have found some row action the user has rights for
            $recordDefaultAction = $action;
            $recordDefaultAction['name'] = $actionName;
            if (
                !empty($recordDefaultAction['target'])
                && $recordDefaultAction['target'] === '_blank'
            ) {
                $recordDefaultAction['tabCreator'] = true;
                $recordDefaultAction['tabUrl'] = $recordDefaultAction['url'];
                $recordDefaultAction['tabEvent'] = 'dblclick';
            }
            else {
                $recordDefaultAction['tabCreator'] = false;
                $recordDefaultAction['actionUrl'] = $recordDefaultAction['url'];
                $recordDefaultAction['actionEvent'] = 'dblclick';
            }
            unset($recordDefaultAction['target']);
            unset($recordDefaultAction['url']);
            $recordDefaultAction['class'] = '';
            break;
        }
        $options['recordDefaultAction'] = $recordDefaultAction;
        
        // normalize bulk actions
        $tmp = array();
        foreach ($options['bulkActions'] as $actionName => $action) {
            if ($action === false) {
                continue;
            }
            if (Validate::intNumber($actionName)) {
                throw new Exception(__e(__FILE__, 'Invalid action definition'));
            }
            if ($action && !is_array($action)) {
                $action = array('url' => $action);
            }
            $action['name'] = $actionName;
            if ($actionName === 'editMany') {
                $action = array_merge($editManyActionDefaults, $action);
            }
            elseif ($actionName === 'deleteMany') {
                $action = array_merge($deleteManyActionDefaults, $action);
            }
            // accept / refuse action 
            if (!self::acceptAction($action)) {
                continue;
            }
            $tmp[$actionName] = $action;
        }
        $options['bulkActions'] = $tmp;
        
        // populate filterSelectInputs with renderFields defined by noempty convesion arrays
        // but only in case that filterSelectInputs is not explicitly defined for corresponding field
        foreach ($options['renderFields'] as $field => $renderer) {
            if (
                empty($options['paginatorOptions']['filterSelectInputs'][$field])
                && !empty($renderer)
                && is_array($renderer)
                && !Validate::callableFunction($renderer)
            ) {
                $tmp = array();
                foreach ($renderer as $k => $v) {
                    $tmp['=' . $k] = $v;
                }
                $options['paginatorOptions']['filterSelectInputs'][$field] = $tmp;
            }
        }
        
        // resolve paginator options
        // - if Paginator option is defined then add some more default paginator options
        if ($options['Paginator']) {
            $defaults['paginatorOptions'] = array_merge(
                $defaults['paginatorOptions'], 
                $options['Paginator']->getOptions(array(
                    'sortUrlParam',
                    'sortDirectionSeparator',
                    'sortFieldsSeparator',
                    'filterUrlParam',
                    'filterFieldsSeparator',
                    'filterExpressionSeparator',     
                    'pageUrlParam',                
                    'inheritRequestOptions',                
                )),
                array(
                    'sortResetSelector' => '.-run-action-reset-sort',
                    'filterResetSelector' => '.-run-action-reset-filter',
                )
            );
        }
        $options['paginatorOptions'] = array_merge($defaults['paginatorOptions'], (array)$options['paginatorOptions']);
        if (!empty($options['tree'])) {
            $options['paginatorOptions']['infiniteScroll'] = false;
        }
        if (empty($options['paginatorOptions']['infiniteScroll'])) {
            $options['recordsOnly'] = false;
        }
        
        self::$options = $options;
        
        if (!empty($options['recordsOnly'])) {
            App::setLayout(false);
            if (!empty($options['records'])) {
                return App::loadView($options['recordsView']['module'], $options['recordsView']['name'], $options);
            }
            else {
                return '';
            }
        }
        
        return App::loadView($options['view']['module'], $options['view']['name'], $options);
    }
    
    /**
     * Accepts / refuses action according to action 'if', 'rights' and rights to 'url'.
     *  
     * @param array $action Action definition possibly having items 'url', 'if' and 'rights'.
     * 
     * @return boolean
     */
    public static function acceptAction($action) {
        // refuse invalid actions
        if (!is_array($action)) {
            return false;
        }
        // if 'if' options is defined then accept the action only if it is TRUE
        if (
            array_key_exists('if', $action)
            && empty($action['if'])
        ) {
            return false;
        }
        // if 'rights' options is defined then accept the action only if it is TRUE
        if (
            array_key_exists('rights', $action)
            && empty($action['rights'])
        ) {
            return false;
        }
        // check user rights for url (supposing there are MVC URLs used)
        if (
            !empty($action['url'])
            && !App::getUserUrlRights($action['url'])
        ) {
            return false;
        }
        return true;
    }
            
    /**
     * Returns array containing attributes and content to be used for action element like:
     * 
     *      list($attributes, $content) = Html::getActionAttributesAndContent(
     *          $action, 
     *          array('data' => $record, 'attributes' => $explicitAttrs)
     *      );
     *      $actionHtml = '<a' . $attributes . '>' . $content . '</a>'
     * 
     * @param array $action Action definition. See e.g. Html::smartIndex() options 'actions' and 'recordActions'
     *      and/or Html::smartAdmin() options 'primaryMenu', 'secondaryMenu' and 'tabs'.
     * @param array $options Additional options to build action. Following are available:
     *      - 'data' (array) Data to create action attributes and content for (e.g. row record). 
     *          If data are qualified, means nested under model names then it must be deflated.
     *          E.g. instead of array('User' => array('name' => ...)) the array('User.name' => ...))
     *          should be used. Defaults to empty array().
     *      - 'dataPrimaryKey' (string) Primary key field name in provided data. 
     *          If the data are qualified (see bove) then qualified primary key must 
     *          be provided, e.g. 'User.id'. Defaults to 'id'.
     *      - 'attributes' (array) Array of explicit attributes to be merged with
     *          attributes based on $action. Defaults to empty array().
     * 
     * @return array Array containing 'attributes' and 'content' items. It can be asigned also 
     *      to list($attributes, $content). Item 'attributes' itself is an array of attributes.
     *      Use Html::attributes() to convert it to string form. Use Html::mergeAttributes()
     *      to merge it with another existing attributes.
     */
    public static function getActionAttributesAndContent($action, $options = array()) {
        $defaults = array(
            'data' => array(),
            'dataPrimaryKey' => 'id',
            'attributes' => array(),
        );
        $options = array_merge($defaults, $options);
        $attributes = array();
        $content = '';
        // normalize action name
        if (empty($action['name'])) {
            $action['name'] = null;
        }
        // validate action options
        if (is_array($action)) {
            $id = Sanitize::value($options['data'][$options['dataPrimaryKey']]);
            // create action attributes
            if (!empty($action['url'])) {
                if ($action['name'] === 'move') {
                    $attributes['data-run-record-id'] = $id;
                }
                else {
                    // normalize url
                    if (is_string($action['url'])) {
                        $action['url'] = array('locator' => $action['url']);
                    }
                    // inherit request source of the content page
                    $action['url']['source'] = App::$requestSource;
                    // add id if provided
                    if (!empty($id)) {
                        $action['url'] = array_merge($action['url'], array('args' => array($id)));
                    }
                    $attributes['href'] = App::getUrl($action['url']);
                }
            }
            elseif ($action['name'] === 'refresh') {
                $attributes['href'] = '';
            }
            if (!empty($action['target'])) {
                $attributes['target'] = $action['target'];
            }
            if (!empty($action['title'])) {
                $attributes['title'] = Str::fill($action['title'], $options['data']);
            }
            $class = '-run-action';
            if (!empty($action['name'])) {
                $class .= ' -run-action-' . Str::slugize(Str::underscore($action['name']));
            }
            if (!empty($action['class'])) {
                $class .= ' ' . Str::fill($action['class'], $options['data']);
            }
            if (!empty($attributes['class'])) {
                $class .= ' ' . $attributes['class'];
            }
            if (!empty($class)) {
                $attributes['class'] = trim($class);
            }
            if (!empty($action['confirmMessage'])) {
                $confirmMessage = str_replace('"', '&quot;', Str::fill($action['confirmMessage'], $options['data']));
                $attributes['onclick'] = "if(confirm('{$confirmMessage}') !== true) {return false;}";
            }
            if (!empty($action['actionUrl'])) {
                if ($action['name'] !== 'move') {
                    // normalize url
                    if (is_string($action['actionUrl'])) {
                        $action['actionUrl'] = array('locator' => $action['actionUrl']);
                    }
                    // inherit request source of the content page
                    $action['actionUrl']['source'] = App::$requestSource;
                    // add id if provided
                    if (!empty($id)) {
                        $action['actionUrl'] = array_merge($action['actionUrl'], array('args' => array($id)));
                    }
                    $attributes['data-run-action-url'] = App::getUrl($action['actionUrl']);
                }
            }
            if (!empty($action['actionEvent'])) {
                $attributes['data-run-action-event'] = $action['actionEvent'];
            }
            // set tab attributes 
            $smartTabsAttributes = array();
            // - set tab creators attributes
            if (
                (
                    empty($action['target'])
                    && !empty($action['tabCreator'])
                    ||
                    !empty($action['target'])
                    && $action['target'] === '_blank'
                    && (
                        !isset($action['tabCreator'])
                        ||
                        !empty($action['tabCreator'])
                    )
                )
                && (
                    !empty($action['url'])  
                    ||
                    !empty($action['tabUrl'])  
                )
            ) {
                $smartTabsAttributes['creator'] = null;
                if (!empty($action['tabUrl'])) {
                    if (is_string($action['tabUrl'])) {
                        $action['tabUrl'] = array('locator' => $action['tabUrl']);
                    }
                    $smartTabsAttributes['url'] = App::getUrl(array_merge($action['tabUrl'], array('args' => array($id))));
                }
                if (!empty($action['tabTitle'])) {
                    $smartTabsAttributes['title'] = Str::fill($action['tabTitle'], $options['data']);
                }
                if (!empty($action['tabClass'])) {
                    $smartTabsAttributes['class'] = Str::fill($action['tabClass'], $options['data']);
                }
                if (!empty($action['tabIcon'])) {
                    $smartTabsAttributes['icon'] = Str::fill($action['tabIcon'], $options['data']);
                }
                if (!empty($action['tabSingle'])) {
                    $smartTabsAttributes['single'] = null;
                }
                if (!empty($action['tabEvent'])) {
                    $smartTabsAttributes['event'] = $action['tabEvent'];
                }
                if (!empty($action['tabFrameType'])) {
                    $smartTabsAttributes['frame-type'] = $action['tabFrameType'];
                }
            }
            // - set tab activators attributes
            if (!empty($action['tabActivator'])) {
                $smartTabsAttributes['activator'] = $action['tabActivator'];
                if (!empty($action['tabEvent'])) {
                    $smartTabsAttributes['event'] = $action['tabEvent'];
                }
            }
            if (!empty($smartTabsAttributes)) {
                $attributes['data-run-st-'] = $smartTabsAttributes;
            }
            // create content
            // - create icon
            $icon = '';
            if (!empty($action['icon'])) {
                $icon = Str::fill($action['icon'], $options['data']);
                // if icon is specified literally (by tags) then uses it as it is
                // e.g.: '<img src="/path/to/my-icon.png" class="my-icon">' or '<i class="fa fa-camera-retro"></i>'
                if (substr($action['icon'], 0, 1) === '<') {
                    $icon = $action['icon'];
                }
                else {
                    $icon = '<img src="' . URL_ROOT . $icon . '"> ';
                }
            }
            // - create label
            $label = '';
            if (!empty($action['label'])) {
                $label = Str::fill($action['label'], $options['data']);
            }
            $content = trim($icon . $label);
        }
        if (!empty($options['attributes'])) {
            $attributes = self::mergeAttributes((array)$options['attributes'], $attributes);
        }
        return array(
            $attributes,
            $content,
            'attributes' => $attributes,
            'content' => $content,
        );
    }
    
    /**
     * Returns html of provided action
     * 
     * @param array $action Action definition. See e.g. Html::smartIndex() options 'actions' and 'recordActions'
     *      and/or Html::smartAdmin() options 'primaryMenu', 'secondaryMenu' and 'tabs'.
     * @param array $options Additional options to build action. Following are available:
     *      - 'data' (array) Data to create action attributes and content for (e.g. row record). 
     *          If data are qualified, means nested under model names then it must be deflated.
     *          E.g. instead of array('User' => array('name' => ...)) the array('User.name' => ...))
     *          should be used. Defaults to empty array().
     *      - 'dataPrimaryKey' (string) Primary key field name in provided data. 
     *          If the data are qualified (see bove) then qualified primary key must 
     *          be provided, e.g. 'User.id'. Defaults to 'id'.
     *      - 'attributes' (array) Array of explicit attributes to be merged with
     *          attributes based on $action. Defaults to empty array().
     *      - 'tag' (string) Tag name used for pasive actions (if 'url' is not present 
     *          in action definition or it is NULL). If empty value provided (NULL, FALSE) then 
     *          no tag is generated and just action content is returned. In such a case 
     *          attributes are omitted as they can be applyed only by tag. Defaults to 'span'.
     * 
     * @return string
     */
    public static function action($action, $options = array()) {
        $defaults = array(
            'tag' => 'span',
            'attributes' => array(),
        );
        $options = array_merge($defaults, $options);
        // render dropdown action
        if (
            !empty($action['options'])
            && !empty($action['optionTemplate'])
        ) {
            // normalize action url
            if (empty($action['url'])) {
                $action['url'] = array('locator' => App::$urlPath);
            }
            if (!is_array($action['url'])) {
                $action['url'] = array('locator' => $action['url']);
            }
            // resolve active value and label
            $value = null;
            if (isset($_REQUEST[$action['name']])) {
                $value = $_REQUEST[$action['name']];
            }
            if (!array_key_exists($value, $action['options'])) {
                $value = array_keys($action['options']);
                $value = reset($value);
            }
            $label = $action['options'][$value];
            unset($action['options'][$value]);
            // create active option html
            if (($labelIsArray = is_array($label))) {
                $activeOptionlHtml = Str::fill($action['optionTemplate'], $label);
            }
            else {
                $activeOptionlHtml = str_replace(
                    array(':value:', ':label:'),
                    array($value, $label),
                    $action['optionTemplate']
                );
            }
            // create drobdown html
            $optionsHtml = '';
            foreach ($action['options'] as $value => $label ) {
                if ($labelIsArray) {
                    $optionHtml = Str::fill($action['optionTemplate'], $label);
                }
                else {
                    $optionHtml = str_replace(
                        array(':value:', ':label:'),
                        array($value, $label),
                        $action['optionTemplate']
                    );
                }
                $action['label'] = $optionHtml;
                $action['url']['get'][$action['name']] = $value;
                list($attributes, $content) = self::getActionAttributesAndContent($action, $options);
                $optionsHtml .= '<li><a' . self::attributes($attributes) . '>' . $content . '</a></li>';
            }
            // get toplevel attributes
            $action['url'] = null;
            $action['label'] = null;
            $options['attributes'] = self::mergeAttributes($options['attributes'], array('class' => 'btn-group'));
            list($attributes, $content) = self::getActionAttributesAndContent($action, $options);
            // render bootstrap dropdown html
            if (!empty($optionsHtml)) {
                $actionHtml = 
                    '<div'. self::attributes($attributes) . '>' .
                        '<button class="btn btn-default dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="true">' .
                            $activeOptionlHtml .
                            '<span class="caret"></span>' .
                        '</button>' . 
                        '<ul class="dropdown-menu" role="menu">' . 
                            $optionsHtml . 
                        '</ul>' .
                    '</div>';
            }
            else {
                $actionHtml = 
                    '<div'. self::attributes($attributes) . '>' .
                        $activeOptionlHtml .
                    '</div>';
            }
            App::setCssFiles(array(
                '/app/css/vendors/bootstrap.css',
                '/app/css/vendors/bootstrap-theme.css',
            ));
            App::setJsFiles(array(
                '/app/js/vendors/bootstrap.min.js',
            ));
        }
        // render select action
        elseif (!empty($action['options'])) {
            if (!class_exists('FormHelper')) {
                App::loadLib('App', 'FormHelper');
            }
            $Form = new FormHelper(array(
                'useDataWrapper' => false,
            ));
            $selectOptions = self::mergeAttributes($action, (array)$options['attributes']);
            $selectOptions = array_merge($selectOptions, array('explicitValue' => Sanitize::value($_GET[$action['name']])));
            unset($selectOptions['url']);
            unset($selectOptions['if']);
            $actionHtml = $Form->select($action['name'], $selectOptions);
            $jsOptions = array_intersect_key($action, array('url' => null));
            $jsOptions['id'] = $Form->inputId($action['name']);
            // normalize url if provided
            if (!empty($jsOptions['url'])) {
                if (!is_array($jsOptions['url'])) {
                    $jsOptions['url'] = array('locator' => $jsOptions['url']);
                }
                // - inherit request source of the content page
                $jsOptions['url']['source'] = App::$requestSource;
                $jsOptions['url'] = App::getUrl($jsOptions['url']);
            }
            App::setJs('jQuery(function(){new Run.App.SelectAction(' . json_encode($jsOptions) . ');});');
            // js config for App.js
            App::setJsConfig('App', array(
                'urlRoot' => App::$urlRoot,
                'urlLang' => App::$urlLang,
                'urlBase' => App::$urlBase,
                'homeSlug' => App::$homeSlug,
            ));
            App::setJsFiles(array(
                '/app/js/vendors/jquery.min.js',
                '/app/js/libs/globalFunctions.js',
                '/app/js/libs/App.js',
                '/app/js/libs/PhpJs.js',
                '/app/js/libs/String.js',
                '/app/js/libs/Number.js',
                '/app/js/libs/Utility.js',
                '/app/js/libs/Validate.js',
                '/app/js/libs/Sanitize.js',
                '/app/js/libs/SelectAction.js',
            ));
        }
        // render custom tag action
        elseif (!isset($action['url'])) {
            list($attributes, $content) = self::getActionAttributesAndContent($action, $options);
            if (empty($options['tag'])) {
                $actionHtml = $content;
            }
            else {
                $actionHtml = '<' . $options['tag'] . self::attributes($attributes) . '>' . $content . '</' . $options['tag'] . '>';
            }
        }
        // render anchor tag action
        else {
            list($attributes, $content) = self::getActionAttributesAndContent($action, $options);
            $actionHtml = '<a' . self::attributes($attributes) . '>' . $content . '</a>';
        }
        return $actionHtml;
    }
    
    /**
     * Resolves attributes for smartIndex row according provided renderRow definition
     * and record data
     * 
     * @param array|callable $renderRow Row renderer definition. See Html::smartIndex() 
     *      renderRow option.
     * @param array $record Data of actual smartIndex row record
     * @param array $attributes Optional. Array of explicit attributes to be 
     *      merged with attributes based on $renderRow result. Defaults to empty array().
     * 
     * @return array List of attributes generated by renderRow definition
     */
    public static function getSmartIndexRenderRowAttributes($renderRow, $record, $attributes = array()) {
        $attributes = (array)$attributes;
        if (Validate::callableFunction($renderRow)) {
            $attributes = self::mergeAttributes(
                $attributes,
                call_user_func_array($renderRow, array($record))
            );
        }
        elseif (!empty($renderRow) && is_array($renderRow)) {
            foreach ($renderRow as $render) {
                if (
                    empty($render['attributes'])
                    || empty($render['conditions'])
                    || !is_array($render['conditions'])
                    || !Validate::dataConditions($record, $render['conditions'], array('flatData' => true))
                ) {
                    continue;
                }
                $attributes = self::mergeAttributes(
                    $attributes,
                    $render['attributes']
                );
            }
        }
        return $attributes;
    }
    
    /**
     * Resolves value according to provided field renderer, value, name and the entire
     * record the value is retrieved from.
     *  
     * @param string|array|callable|function $renderField Field renderer definition. See Html::smartIndex() 
     *      renderFields option.
     * @param mixed $value Actual value of the field
     * @param string $field Field name
     * @param array $record Record from which the value of the field was retrieved
     * 
     * @return mixed Rendered/interpreted value of the field
     */
    public static function getSmartIndexRenderFieldValue($renderField, $value, $field, $record) {
        if (
            isset($renderField)
            && Validate::callableFunction($renderField)
        ) {
            return call_user_func_array($renderField, array($value, $field, $record));
        }
        elseif (is_string($renderField)) {
            return Str::fill($renderField, $record, $value);
        }
        elseif (isset($renderField[$value])) {
            return $renderField[$value];
        }
        else {
            return $value;
        }
    }
    
    /**
     * Returns tree path counts generated from provided records
     * 
     * @param array $records
     * @return int
     */
    public static function getSmartIndexTreePathsCounts($records) {
        $pathsCounts = array();
        foreach ($records as $record) {
            if (!isset($record['path'])) {
                continue;
            }
            $path = $record['path'];
            if (!isset($pathsCounts[$path])) {
                $pathsCounts[$path] = 1;
            }
            else {
                $pathsCounts[$path]++;
            }
        }
        return $pathsCounts;
    }
    
    /**
     * 
     * @param int $recordIndex Record index to generate attribites and navigation for
     * @param array $attributes Optional. Array of explicit attributes to be 
     *      merged with attributes based on $renderRow result. Defaults to empty array().
     * 
     * @return array Array containing 'attributes' and 'content' items. It can be asigned also 
     *      to list($attributes, $content). Item 'attributes' itself is an array of attributes.
     *      Use Html::attributes() to convert it to string form. Use Html::mergeAttributes()
     *      to merge it with another existing attributes.
     */
    public static function getSmartIndexRecordTreeAttributesAndNavigation($recordIndex, $attributes = array()) {
        // create shorthand for options
        $options = & self::$options;
        
        $record = $options['records'][$recordIndex];
        $nextRecord = !empty($options['records'][$recordIndex + 1]) 
            ? $options['records'][$recordIndex + 1] : null;
        $id = Sanitize::value($record[$options['primaryKey']]);
        // get record path and actualize its progress
        $path = Sanitize::value($record['path']);
        if (!isset($options['tree']['pathsProgress'][$path])) {
            $options['tree']['pathsProgress'][$path] = 1;
        }
        else {
            $options['tree']['pathsProgress'][$path]++;
        }
        // check if node has subitems
        $hasSubitems = true;
        if(
           // if the very last record
           empty($nextRecord)
           // if the next record is on the same level (sibling)
           || $record['path'] === $nextRecord['path']
           // if the next record is on parent level
           || strlen($record['path']) > strlen($nextRecord['path'])
        ) {
            $hasSubitems = false;
        }
        // get record node type
        // - check for explicit type
        $explicitNodeType = false;
        if (!empty($record['node_type'])) {
            $nodeType = $record['node_type'];
            $explicitNodeType = true;
        }
        // - check for implicit branch (has subitems)
        elseif($hasSubitems) {
            $nodeType = 'enum_tree_node_branch';
        }
        // - otherwise implicit leaf
        else {
            $nodeType = 'enum_tree_node_leaf';
        }
        // get record nesting level (relative to tree top level nesting)
        $parentIds = Model::getParentIdsFromTreePath($path);
        $level = count($parentIds);
//        $relativeLevel = $level - $options['tree']['rootLevel'];
        
        // create tree attributes
        $treeAttributes = array();
        $treeAttributes['data-run-record-tree-path'] = $path;
        $treeAttributes['data-run-record-id'] = $id;
//// tree is collapsed by js (because of proper navigation display)        
//        if (
//            $options['tree']['status'] != 'expanded'
//            && $relativeLevel != 0
//        ) {
//            $treeAttributes['style'] = 'display:none';
//        }
//        elseif (
        if (
            $options['tree']['status'] === 'expanded'
            && $nodeType === 'enum_tree_node_branch'
            && $hasSubitems
        ) {
            $treeAttributes['class'] = $options['tree']['expandedNodeClass'];
        }
        if ($hasSubitems) {
            $treeAttributes['data-run-six-tree-has-subitems'] = null;
        }
        if ($explicitNodeType) {
            $treeAttributes['data-run-six-tree-explicit-node-type'] = null;
        }
        if ($nodeType === 'enum_tree_node_branch') {
            $treeAttributes['data-run-six-tree-branch'] = null;
        }
        // create tree navigation
        $navigation = '';
        $levelPath = '';
        // - prepend parent ids by null (this is parent id of top level)
        array_unshift($parentIds, null); 
        // - prepare root level path
        for ($i = 0; $i < $options['tree']['rootLevel']; $i++) {
            $levelPath .= $parentIds[$i] . '-';
        }
        // - set navigation icons and classes for each level
        for ($i = $options['tree']['rootLevel']; $i <= $level; $i++) {
            // check if the actual levelPath is on the last node already
            $levelPath .= $parentIds[$i] . '-';
            $isLevelLastNode = $options['tree']['pathsProgress'][$levelPath] == $options['tree']['pathsCounts'][$levelPath];
            // pass through navigation related to higher levels
            if ($i < ($level)) {
                // show line elbow if there is some next node on this level
                if (!$isLevelLastNode) {
                    $navigation .= self::getSmartIndexRecordTreeNavigationPart(
                        $options['tree']['elbowLineClass']
                    );
                }
                // show empty elbow if there is no other node to continue line for
                else {
                    $navigation .= self::getSmartIndexRecordTreeNavigationPart(
                        $options['tree']['elbowEmptyClass']
                    );
                }
            }
            // treat actual record level navigation
            else {
                if (!$isLevelLastNode) {
                    if ($hasSubitems) {
                        $navigation .= self::getSmartIndexRecordTreeNavigationPart(
                            $options['tree']['elbowExpanderClass']
                        );
                    }
                    else {
                        $navigation .= self::getSmartIndexRecordTreeNavigationPart(
                            $options['tree']['elbowClass']
                        );
                    }
                }
                else {
                    if ($hasSubitems) {
                        $navigation .= self::getSmartIndexRecordTreeNavigationPart(
                            $options['tree']['elbowEndExpanderClass']
                        );
                    }
                    else {
                        $navigation .= self::getSmartIndexRecordTreeNavigationPart(
                            $options['tree']['elbowEndClass']
                        );
                    }
                }
            }
        }
        if ($options['tree']['showNodeType']) {
            // get icon and class 
            // - treat two standard node types
            if ($nodeType === 'enum_tree_node_branch') {
                $icon = $options['tree']['branchNodeIcon'];
                $class = $options['tree']['branchNodeClass'];
            }
            elseif ($nodeType === 'enum_tree_node_leaf') {
                $icon = $options['tree']['leafNodeIcon'];
                $class = $options['tree']['leafNodeClass'];                    
            }
            // - treat custom node types
            else {
                // remove 'enum_tree_node_'
                $prefix = Str::camelize(substr($nodeType, 15));
                $icon = Sanitize::value($options['tree'][$prefix . 'NodeIcon']);
                $class = Sanitize::value($options['tree'][$prefix . 'NodeClass']);
            }
            if (!empty($options['tree']['nodeClass'])) {
                $class = $options['tree']['nodeClass'] . ' ' . $class;
            }
            $navigation .= self::getSmartIndexRecordTreeNavigationPart(
                $class,
                $icon
            );
        }
        $navigation = '<div class="' . $options['tree']['navigationClass'] .'">' . $navigation . '</div>';
                
        $attributes = self::mergeAttributes($attributes, $treeAttributes);
        
        return array(
            $attributes,
            $navigation,
            'attributes' => $attributes,
            'navigation' => $navigation,
        );
    }
    
    /**
     * Returns navigation part for provided icon and css class
     * 
     * @param string $icon
     * @param string $class
     * @return string
     */
    protected static function getSmartIndexRecordTreeNavigationPart($class, $icon = null) {
        
        $navigation = '';
        if (!empty($icon)) {
            $navigation .= '<img src="' . URL_ROOT . '/' . ltrim($icon, '/') . '"';
        }
        else {
            $navigation .= '<div';
        }
        if (!empty($class)) {
            $navigation .= ' class="' . $class . '"';
        }
        if (!empty($icon)) {    
            $navigation .= '>';
        }
        else {
            $navigation .= '></div>';
        }
        
        return $navigation;
    }
    
    /**
     * Smart form affix items
     * 
     * Serves to bulid affix for Html::smartForm(). It has following form:
     * 
     *      array(
     *          array(
     *              'label' => 'My label'
     *              'anchor' => 'my-id'
     *              'object' => 'h1'
     *              'level' => '1' // starting from 1 for toplevel
     *          ),
     *          ...,
     *      )
     *
     * @var array
     */
    protected static $affixItems = array();
    
    /**
     * Return smart form html
     * 
     * @param array $options Following options can be used:
     * 
     * 1] All options which can be passed to Form class constructor:
     *      - 'data' (array) Form data. If set then it is used as default 'data' option
     *      - 'testData' (array) Test data to be used if $_GET['_FormHelperUseTestData_'] is set to nonempty value. 
     *      - 'errors' (array) Form errors. If set then it is used as default 'errors' option
     *      - 'warnings' (array) Form warnings. If set then it is used as default 'warnings' option
     *      - 'required' (array) Form required fields. If set  then it is used as default 'required' option
     *      - 'defaultValues' (array) Default values of form inputs to be used to initialize
     *          inputs on the form opening (when the form normaly is empty)
     *      - 'disabled' (array) Disabled form fields
     *      - 'inputDefaults' (array) Array of input default options which are: 'type',
     *          'class', 'textClass', 'toggleClass', 'template', 'toggleTemplate', 'attachErrors',
     *          'hint', 'translatedMark', 'requiredMark', ...
     *      - 'labelDefaults' (array) Array of label default options which are: 'class', 
     *          'hint', 'translatedMark', 'requiredMark', 'separator', 'template'.
     *      - 'errorsDefaults' (array) Array of errors default options which are: 'class', 
     *          'errorClass', 'plainText'
     *      - 'scrollToError' (bool) If TRUE then form is autoscrolled to first occured error
     *      - 'compatibility' (string) Possible values are 'bootstrap', 'mdc' or any empty value.
     *          If 'bootstrap' then there are added classes of bootstrap css (see http://getbootstrap.com/). 
     *          Classes are added to text inputs ('form-control'), label ('control-label'), 
     *          ridt classes ('has-error') and to errors ('help-block'). There is also added 
     *          wrapping block <div class="form-group {ridtClasses}"><div>.
     *          If 'mdc' then there are added classes of material design components css (see https://material.io/components/web/). 
     *          Classes are added to text inputs, labe, ridt classes  and to errors. 
     *          There is also added wrapping block. Defaults to 'bootstrap'.
     *      - 'nonTranslatedProcessing' (string) Possible values are 'ignore' and 'disable' or FALSE.
     *          If set to 'ignore' then the field is ignored (nothing is generated)
     *          If set to disabled then the field is disabled. If FASE then the field is treated normally.
     * 
     * 2] And following additional:
     *      - 'schema' (array) For the very simple case of single model the Model::$schema can be passed
     *          as 'schema' option and schema 'type' is converted to apropriate input 'type'.
     *          In such a case option 'fields' must be empty. Defaults to empty array.
     *      - 'fileFields' (array) File fieds array retrieved by Model::getPropertyFileFields()
     *          can be passed to guess 'file' inputs types for fields with uspecified input type.
     *      - 'Model' (Model|array) Serves to simplify form specification. If provided then
     *          'errors', 'required', 'schema' and 'fileFields' are populated from provided Model(s).
     *          Only empty items are populated to ensure that you can set them explicitly.
     *          It can be provided as single Model instance (in case of single model data form)
     *          or as array of participating models. The data nesting must correspond 
     *          to nesting in this array, e.g. array($User, 'UserProfile' => $UserProfile) 
     *          or array('User' => $User, 'UserProfile' => array($UserProfile, 'fronend'))
     *          If the model item is provided itself as an array so the 2nd item
     *          is alternative definition and the 3rd item is 'on' (this is used 
     *          to retrieve required fields, and alternative itself can be an array). 
     *          To provide single non-nested model with alternative use array(array($UserProfile, 'fronend')).
     *          Defaults to NULL.
     *      - 'view' (string|array) Name of view for smartForm rendering. If string 
     *          then the view is searched under App module. To provide views from 
     *          other modules use array like array('module' => ..., 'name' => ...)
     *          Defaults to 'Html/smartForm'
     *      - 'title' (string) Form title displayed in header
     *      - 'fieldsView' (string|array) Name of view with defined html for fields. If string 
     *          then the view is searched under App module. To provide views from 
     *          other modules use array like array('module' => ..., 'name' => ...).
     *          All options are forvarder to this view. Form instance is alredy created
     *          under 'Form' key. If provided then 'fields' option is ignored. Defaults to NULL.
     *      - 'fields' (array) Contains 3 types of items:
     * 
     *          a) Form fields definitions by associative pairs '{fieldName}' => array({FieldOptions})
     *              or as (non)associative items containing nonempty 'field' => '{fieldName}' item
     *              in field options. If multiple models are passed then field names should be prefixed 
     *              by model name qualifiers, e.g.: 'Product.id', 'Product.name', 'Manufacturer.name' 
     *              (the 'primaryKey' option must be set to 'Product.id' in such case).
     *              Each of field options array can contain all options which are available for
     *              FormHelper::$input() method plus the mentioned 'field' option. 
     * 
     *          b) Form conditions by nonassociative items provided either as strings
     *              or arrays. Following conditions keywords are available: 'if',
     *              'elseif', 'else' and 'endif'. Keywords 'if' and 'elseif' must 
     *              be provided as associative items with value, e.g. array('if' => $user['Group']['name'] === 'admins'),
     *              or array('endif').
     * 
     *          c) Form structure controls by nonassociative items provided either as strings
     *              or arrays. There are 2 types of them: OBJECTS (row, column, fieldset, ...) and
     *              PROPERTIES which redefines some of global properties (columns, deviceSize, ...).
     *              Many consequent properties can be merged into one array, e.g 
     *              array('columns' => 3, 'deviceSize' => 'lg') and can be provided in this
     *              way also in object definition, e.g. array('fieldset' => 'My fieldset', 'columns' => '3', 'deviceSize' => 'lg).
     *              Many consequent objects cannot be merged like this. Properties 
     *              provided in object definition or inside of object are used only in scope
     *              of that object. If provided in object definition then they apply
     *              for object inner objects but not to the object itself.
     * 
     *                  c1) Objects:
     *                  - 'row' (void) Nonassociative. Starts a new explicit row.
     *                      Optional. Rows starts and ends are generated implicitly
     *                      according defined number of columns.
     *                  - '/row' (void) Nonassociative. Explicilty closes the current row.
     *                      Each explicitly opened row must be explicitly closed. 
     *                  - 'col' (void) Nonassociative. Starts a new explicit column.
     *                      Optional. Columns starts and ends are generated implicitly
     *                      one column per form input. Each explicitly opened col must 
     *                      be explicitly closed.
     *                  - '/col' (void) Nonassociative. Explicitly closes the current column.
     *                      Each explicitly opened col must be explicitly closed.
     *                  - 'fieldset' (string) Defines start of fieldset. Value is 
     *                      used as fieldset label. Can be provided also as nonassociative 
     *                      item, in that case fieldset has no label, e.g.: array('fieldset' => 'Address')
     *                  - '/fieldset' (void) Nonassociative. Defines end of fieldset, 
     *                      e.g.: array('/fieldset'),
     *                  - 'accordion' (string) Defines start of accordion. Value is 
     *                      used as accordion label. Can be provided also as nonassociative 
     *                      item, in that case accordion has no label, e.g.: array('accordion' => 'Attributes')
     *                  - '/accordion' (void) Nonassociative. Defines end of accordion, 
     *                      e.g.: array('/accordion'),
     *                  - 'h1-6' (string) Defines heading. Value is used as heading text. 
     *                      Can be provided also as nonassociative item, in that case 
     *                      heading has no text, e.g.: array('h3' => 'Address')
     *                  - 'html' (string) Explicit html to be iserted to form html
     *                      on actual position. No validity is checked, it's let to 
     *                      responsibility of user. If you would like to nest provides html
     *                      to row or column then they must be defined explicitly. Otherwise
     *                      the html is created on level of rows. See the example below.
     *                      There is no html closing object.
     *                  - 'field' (string) Another form like to define form field.
     *                      The value is used as field name and the rest of items in array
     *                      is used as field options.
     * 
     *                  c2) Properties:
     *                  - 'deviceSize' (string) Changes deviceSize from actual position
     *                      forward. If used in object start definition then it applies 
     *                      only for nested objects e.g. array('fiedsetStart' => 'Address', 'deviceSize' => 'xs') 
     *                  - 'columns' (int) Changes number of columns from actual position 
     *                      forward. If used in object start definition then it applies 
     *                      only for nested objects e.g. array('fiedsetStart' => 'Address', 'columns' => 3) 
     *                  -'fieldsetTemplate' Changes fieldsetTemplate from actual position
     *                      forward. If used in object start definition then it applies 
     *                      only for nested objects.
     *                  -'accordionTemplate' Changes accordionTemplate from actual position
     *                      forward. If used in object start definition then it applies 
     *                      only for nested objects.
     *                  - 'affix' (bool|string|array) If TRUE then object is included into side navigation.
     *                      This property can be used only for following objects: 'row', 'col', 
     *                      'fieldset', 'accordion', 'tab', 'div', 'h1' - 'h6', 'field'.
     *                  - 'hint' (string|array) Hint text for object. If you need to specify some hint options
     *                      then use array of options. In such case hint text itself must be supplied under key 'text' 
     *                      and all other array items are passed as options to Html::hint(). 
     *                      Hint is placed into object template by ':hint:' insert.
     * 
     *              All object options which are not valid properties are attached as object html attributes.
     * 
     *              Objects 'fieldset', 'accordion' and property 'columns' force to start new row of inputs.
     * 
     *              Nesting of conditions is independent from nesting of structure
     *              controls. It's up to developer to nest correctly (the same like building html with php)
     * 
     *          An example of of 'fields' definition:
     * 
     *              array(
     *                  'User.firstname' => array('label' => '...'),
     *                  array('field' => 'User.lastname', 'label' => '...'),
     *                  array('row'),                                                               // or 'row'
     *                      'UserProfile.single => array('type' => 'checkbox', label => 'I am single'),
     *                  array('/row'),                                                               // or '/row'
     *                  array('fieldset' => 'Address', 'columns' => 3),
     *                     'UserProfile.street' => array('label' => '...'),
     *                     'UserProfile.city' => array('label' => '...'),
     *                     'UserProfile.zip' => array('label' => '...'),
     *                     'UserProfile.country' => array('label' => '...'),
     *                  array('/fieldset'),                                                         // or '/fieldset', implicitly ends up the row
     *                  array('html' => '<div class="containing-all-subsequent-rows">'),            // all following form code (starting by rows) is  included in
     *                      'UserProfile.age' => array('label' => '...'),
     *                      'UserProfile.agreement' => array('label' => '...'),
     *                      array('if' => $user['Group']['name'] === 'admins'),
     *                          'UserProfile.internal_note' => array('label' => '...'),
     *                      array('endif'),
     *                  array('html' => '</div>'),                                                  // implicitly ends up the row
     *                  array('row', 'columns' => 1),
     *                     'col',                                                                   // or array('col')
     *                         array('html' => $this->loadView('Photogallery')),                    // nested in explicitly defined objects
     *                     '/col',                                                                  // or array('/col')
     *                  '/row',
     *                  'row',
     *                      array('field', 'type' => 'contentBlock', ...),                          // if input does not use name then use 'field' as first nonassoc item
     *                  '/row',
     *                  ...,    
     *              )
     *          
     *          If 'fields' are not provided they can be generated from 'schema' (see below)
     *          or from 'data'. If 'schema' is provided then missing field input 'type' 
     *          guessed from provided 'schema'.
     *      - 'substitutions' (array) Array of object substitutions for quick changes of form objects 
     *          defined in 'fields' option, e.g. array('tab' => 'h1', 'fieldset' => 'h2').
     *          ATTENTION: If substituting block objects (tab, fieldset, ...) by nonblock (h1-6, html)
     *          then nesting of properties gets broken. More over it is not possible to
     *          substitute nonblock objects by block objects.
     *      - 'buttonLabel' (string|FALSE) Form submit button label. If FALSE then the button is omitted.
     *          Defaults to translation of 'Save'.
     *      - 'closeButtonLabel' (string|FALSE) Form close button label. If FALSE then the button is omitted.
     *          Defaults to translation of 'Close'.
     *      - 'saveAndCloseButtonLabel' (string|FALSE) Form close button label. If FALSE then the button is omitted.
     *          Defaults to translation of 'Save & Close'.
     *      - 'deleteButtonLabel' (string|FALSE) Form delete button label. If FALSE then the button is omitted.
     *          Defaults to translation of 'Delete'.
     *      - 'deleteButtonTemplate' (string) Html to generate delete button. Defaults to 
     *          '< span class="btn btn-default -run-sfo-delete">:deleteButtonLabel:< /span>'.
     *      - 'deleteButtonUrl' (string) URL address to be launched by delete button.
     *          Defaults to NULL.
     *      - 'id' (string) Smart form container css id. If not provided or empty then autogenerated.
     *      - 'class' (string) Smart form top level css class (additional). Defaults to
     *      - 'primaryKey' (string) Primary key field name. Defaults to 'id'.
     *      - 'action' (string|array) URL the form will be submited to. Default to NULL,
     *          means the form is submited to actual URL.
     *      - 'method' (string) HTTP method to use when sending form-data. Defaults to 'post'.
     *      - 'enctype' (string) Form enctype. Mosltly not necessara to provide. 
     *          Its value is forced to 'multipart/form-data' if there is a file 
     *          input in form. Defaults to 'multipart/form-data'. 
     *      - 'target' (string|array) Simple target or array of targets the data of form are
     *          addressed to.
     *      - 'maxColumns' (int) Max number of columns (bootstrap less files give possibility to change this).
     *          Defaults to 12 (default bootstrap value).
     *      - 'deviceSize' (string) One of Bootstrap device size abbreviations: 'xs', 'sm', 'md', 'lg'.
     *          Defaults to 'sm'. See http://getbootstrap.com/css/#grid-options.
     *      - 'columns' (int|array) Number of columns the inputs are aranged into. Can be 
     *          a value from 1 to 'maxColumns'. Or an array specifying spans of columns, 
     *          e.g. array(5, 3, 4) - there are 3 columns, with respective spans 5, 3 and 4 columns.
     *          This is default value which can be changed by control item 'columns'
     *          if 'fields' option (see below). Defaults to 1.
     *      - 'actions' (array) Definition of smart form actions whose controls
     *          are displayed in header. Actions are displayed in the order
     *          they are defined. Action names are used as keys of associative array, 
     *          under which the action options are provided. All possible options 
     *          of action definition see on Html::smartIndex() at option 'recordActions'.
     *          One predefined action is 'lang'. The 'lang' action is a bit special.
     *          You can use also TRUE value to display it with default options.
     *      - 'fieldsetTemplate' (string) Html used to generate fieldsets in form.
     *          Inserts ':label:' and ':content:' to place label and fieldset content. Defaults to
     *          '<fieldset><caption>:label:</caption>:content:</fieldset>'.
     *      - 'accordionTemplate' (string) Html used to generate accordions in form.
     *          Inserts ':label:' and ':content:' to place label and form fields. Defautls to
     *          '<div class="-run-sfo-accordion"><div class="-run-sfo-accordion-title">:label:</div>:content:</div>'.
     *      - 'rowTemplate' (string) Possible values are 'bootstrap', 'table' or 
     *          an explicit HTML template which will be used for single 
     *          row HTML (placed by insert ':r:'). This is used only if 'columns' 
     *          option is set to nonzero positive integer. Defaults to 'bootstrap'.
     *      - 'headerTemplate' (string) Html template to display header. Inserts 
     *          :title:, :buttonLabel: and :actions: can be used to insert corresponding 
     *          items into header. Use class '-run-sfo-close' for Close button. 
     *          Use class '-run-sfo-save-and-close' for Save&Close button. 
     *          For default value see methods $defaults array.
     *      - 'showAffix' (bool|array) If TRUE then affix navigation is generated
     *          according to existing fieldsets, headings (h1-6) and also all objects 
     *          with property 'affix' set explicitily to TRUE. It is possible to specify 
     *          affix navigation options explicitly as array(). Following options are available:
     *              - 'autogenerate' (bool|array) If FALSE then navigation is created only 
     *              from object which have property 'affix' set explicitly to TRUE.
     *              If an array of objects in form then for all named objects it
     *              the navigation generated, e.g. ('fieldset','h1'). If TRUE then 
     *              the property is set internaly to array('fieldset', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6').
     *              Defaults to FALSE.
     *              - 'width' (int) Width if the affix navigation column in bootstrap grid.
     *              Should be a number between 1 and 11. Defaults to 2.
     *          Defaults to FALSE, means array('autogenerate' => FALSE, 'width' => 2) is used.
     *      - 'tabsToReloadAfterSave' (string|array) Specification of SmartTabs (= their SmartTabFrame instances) 
     *          which should be reloaded after data has been saved (see 'saved' option). 
     *          Following values are accepted: 'creator' - to reload creator SmartTabFrame, 
     *          or array of url paths like array('/url/path/1', * '/url/path/2', ...) - to specify 
     *          exact list of SmartTabFrames by url paths their contentWindows are set to.
     *          This option is passed to instance of js class Run.App.SmartForm.
     *      - 'hasErrors' (bool) Has the form validation failed and are there validation errors?
     *          This option is used to ignore scroll preserving and let the first 
     *          error message to be scrolled into view. If not provided then resolved internally.
     *      - 'saved' (bool) Has been the form data changed (after record save or delete)?
     *          This option is used together with 'tabsToReloadAfterSave'.
     *          If not provided then it is set TRUE if data are not empty and there are no errors.
     * 
     * @return string Form html
     * 
     * @throws Exception In case of invalid control definition
     */
    public static function smartForm($options = array()) {
        $defaults = array(
            // Form class options
            'data' => array(),
            'testData' => array(),
            'defaultValues' => array(),
            'errors' => array(),
            'warnings' => array(),
            'disabled' => array(),
            'required' => array(),
            'translated' => array(),
            'lang' => null,
            'inputDefaults' => array(),
            'labelDefaults' => array(
                'separator' => '',
            ),
            'errorsDefaults' => array(),
            'emptyTextDefaults' => array(),
            'scrollToError' => null,
            'compatibility' => 'bootstrap',
            'nonTranslatedProcessing' => 'disable',
            // smartForm options
            'schema' => array(),
            'fileFields' => array(),
            'Model' => null,
            
            'view' => 'Html/smartForm',
            'title' => null,
            'fieldsView' => null,
            'fields' => array(),
            'substitutions' => array(),
            'buttonLabel' => __(__FILE__, 'Save'),
            'closeButtonLabel' => __(__FILE__, 'Close'),
            'saveAndCloseButtonLabel' => __(__FILE__, 'Save & close'),
            'deleteButtonLabel' => __(__FILE__, 'Delete'),
            'deleteButtonTemplate' => '<span class="btn btn-default -run-sfo-delete">:deleteButtonLabel:</span>',
            'deleteButtonUrl' => null,
            'id' => null,
            'class' => null,
            'primaryKey' => 'id',
            'action' => null,
            'method' => 'post',
            'enctype' => 'multipart/form-data',
            'target' => array(),
            'maxColumns' => 12,
            'deviceSize' => 'sm',
            'columns' => 1,
            'actions' => array(),
            'rowTemplate' => '<div:attributes:>:content:</div>',
            'colTemplate' => '<div:attributes:>:content:</div>',
            'fieldsetTemplate' => '<fieldset:attributes:><legend>:label:</legend>:content:</fieldset>',
            'accordionTemplate' => '<div:attributes:><div class="-run-sfo-accordion-title">:label:</div>:content:</div>',
            'tabsTemplate' => '<div id=":id:"><ul class="nav nav-tabs">:anchors:</ul><div class="tab-content">:contents:</div></div>',
            'tabAnchorTemplate' => '<li class="active"><a href="#:id:" data-toggle="tab"><button class="close" type="button">×</button>:label:</a></li>',
            'tabContentTemplate' => '<div class="tab-pane fade active in" id=":id:">:content:</div>',
            'divTemplate' => '<div:attributes:>:content:</div>',
            'h1Template' => '<h1 class="-run-sfo-heading":attributes:>:label::hint:</h1>',
            'h2Template' => '<h2 class="-run-sfo-heading":attributes:>:label::hint:</h2>',
            'h3Template' => '<h3 class="-run-sfo-heading":attributes:>:label::hint:</h3>',
            'h4Template' => '<h4 class="-run-sfo-heading":attributes:>:label::hint:</h4>',
            'h5Template' => '<h5 class="-run-sfo-heading":attributes:>:label::hint:</h5>',
            'h6Template' => '<h6 class="-run-sfo-heading":attributes:>:label::hint:</h6>',
            'headerTemplate' => '<div class="-run-sfo-title">:title:</div>:actions::messages::deleteButton:<span class="btn btn-default -run-sfo-close">:closeButtonLabel:</span><button type="submit" class="btn btn-default -run-sfo-save-and-close">:saveAndCloseButtonLabel:</button><button type="submit" class="btn btn-default">:buttonLabel:</button>',
//            'headerTemplate' => '<div class="-run-sfo-title">:title:</div>:actions::messages:<input type="submit" class="btn btn-default" value=":buttonLabel:"/>',
//            'headerTemplate' => '<div class="-run-sfo-title">:title:</div>:actions::messages:<button type="submit" class="btn">:buttonLabel:</button>',
            'showAffix' => false,
            'tabsToReloadAfterSave' => 'creator',
            'hasErrors' => null,
            'saved' => null,
        );
            $options = array_merge($defaults, $options);
        // actions defaults
        $langActionDefaults = array(
            'ulr' => App::$urlPath,
            'options' => array_combine(App::getPropertyLangs(), App::getPropertyLangs()),
            'optionTemplate' => '<img src="/app/img/libs/lang/:label:.png" title=":label:"/>',
        );
        // normalize target
        $options['target'] = (array)$options['target'];
        // normalize view
        if (is_string($options['view'])) {
            $options['view'] = array('module' => 'App', 'name' => $options['view']);
        }
        // normalize fieldsView
        if (
            !empty($options['fieldsView']) 
            && is_string($options['fieldsView'])
        ) {
            $options['fieldsView'] = array('module' => 'App', 'name' => $options['fieldsView']);
        }
        // apply Model(s) if provided
        if (!empty($options['Model'])) {
            // normalize Model
            if (is_object($options['Model'])) {
                $options['Model'] = array($options['Model']);
            }
            else {
                $options['Model'] = (array)$options['Model'];
            }
            // populate 'required', 'schema' and 'fileFields' from provided Model(s)
            // if they are not set yet
            $populateErrors = empty($options['errors']);
            $populateWarnings = empty($options['warnings']);
            $populateRequired = empty($options['required']);
            $populateTranslated = empty($options['translated']);
            $populateSchema = empty($options['schema']);
            $populateFileFields = empty($options['fileFields']);
            if ($populateErrors) {    
                $options['errors'] = array();
            }
            if ($populateWarnings) {    
                $options['warnings'] = array();
            }
            if ($populateRequired) {
                $options['required'] = array();
            }
            if ($populateTranslated) {
                $options['translated'] = array();
            }
            if ($populateSchema) {
                $options['schema'] = array();
            }
            if ($populateFileFields) {
                $options['fileFields'] = array();
            }
            foreach ($options['Model'] as $k => $v) {
                // get Model and alternative
                if (is_array($v)) {
                    $Model = array_shift($v);
                    $alternative = array_shift($v);
                    $on = array_shift($v);
                }
                else {
                    $Model = $v;
                    $alternative = null;
                    $on = null;
                }
                if (!($Model instanceof Model)) {
                    throw new Exception('Invalid value provided in Model option on key %s', $k);
                }
                if (is_int($k)) {
                    if ($populateErrors) {
                        $options['errors'] = array_merge($options['errors'], $Model->getErrors());
                    }
                    if ($populateWarnings) {
                        $options['warnings'] = array_merge($options['warnings'], $Model->getWarnings());
                    }
                    if ($populateRequired) {
                        $options['required'] = array_merge($options['required'], $Model->getNotEmptyFields(array(
                            'alternative' => $alternative, 
                            'on' => $on,
                            'data' => $options['data'],
                        )));
                    }
                    if ($populateTranslated) {
                        $options['translated'] = array_merge($options['translated'], $Model->getTranslatedFields(array('combine' => true)));
                    }
                    if ($populateSchema) {
                        $options['schema'] = array_merge($options['schema'], $Model->getPropertySchema());
                    }
                    if ($populateFileFields) {
                        $options['fileFields'] = array_merge($options['fileFields'], $Model->getPropertyFileFields());
                    }
                }
                elseif (is_string($k)) {
                    $modelName = $k;
                    if ($populateErrors) {
                        $options['errors'][$modelName] = $Model->getErrors();
                    }
                    if ($populateWarnings) {
                        $options['warnings'][$modelName] = $Model->getWarnings();
                    }
                    if ($populateRequired) {
                        $options['required'][$modelName] = $Model->getNotEmptyFields(array(
                            'alternative' => $alternative, 
                            'on' => $on,
                            'data' => $options['data'],
                        ));
                    }
                    if ($populateTranslated) {
                        $options['translated'][$modelName] = $Model->getTranslatedFields(array('combine' => true));
                    }
                    if ($populateSchema) {
                        $options['schema'][$modelName] = $Model->getPropertySchema();
                    }
                    if ($populateFileFields) {
                        $options['fileFields'][$modelName] = $Model->getPropertyFileFields();
                    }
                }
                else {
                    throw new Exception('Invalid key %s', print_r($k, true));
                }
            }
        }
        // normalize schema, file fields 
        $options['schema'] = Arr::deflateData($options['schema']);
        $options['fileFields'] = Arr::deflateData($options['fileFields']);
        // normalize hasErrors
        if ($options['hasErrors'] === null) {
            // check if there are any errors (the errors array can contain many models)
            $hasErrors = false;
            foreach ($options['errors'] as $k => $v) {
                if (!empty($v)) {
                    $hasErrors = true;
                    break;
                }
            }
            foreach ($options['warnings'] as $k => $v) {
                if (!empty($v)) {
                    $hasErrors = true;
                    break;
                }
            }
            $options['hasErrors'] = $hasErrors;
        }
        // normalize saved
        if ($options['saved'] === null) {
            $options['saved'] = !empty($options['data']) && !$options['hasErrors'];
        }
        
        // normalize deleteButtonTemplate and deleteButtonUrl
        if (!App::getUserUrlRights($options['deleteButtonUrl'])) {
            $options['deleteButtonTemplate'] = null;
            $options['deleteButtonUrl'] = null;
        }
        
        // normalize actions
        $tmp = array();
        foreach ($options['actions'] as $actionName => $action) {
            if ($action === false) {
                continue;
            }
            if (Validate::intNumber($actionName)) {
                throw new Exception(__e(__FILE__, 'Invalid action definition'));
            }
            if ($action && !is_array($action)) {
                // treat lang action separately
                if (
                    $actionName === 'lang'
                    && $action === true
                ) {
                    $action = array();
                }
                else {
                    $action = array('url' => $action);
                }
            }
            $action['name'] = $actionName;
            // apply actions defaults
            if ($actionName === 'lang') {
                $action = array_merge($langActionDefaults, $action);
            }
            // accept / refuse action 
            if (!self::acceptAction($action)) {
                continue;
            }
            $tmp[$actionName] = $action;
        }
        $options['actions'] = $tmp;
        
        // normalize showAffix
        if (!empty($options['showAffix'])) {
            $affixDefaults = array(
                'autogenerate' => true,
                'width' => 2,
            );
            if (!is_array($options['showAffix'])) {
                $options['showAffix'] = $affixDefaults;
            }
            else {
                $options['showAffix'] = array_merge($affixDefaults, $options['showAffix']);
            }
            if ($options['showAffix']['autogenerate'] === true) {
                $options['showAffix']['autogenerate'] = array(
                    'fieldset', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                );
            }
            if (!empty($options['showAffix']['autogenerate'])) {
                $options['showAffix']['autogenerate'] = (array)$options['showAffix']['autogenerate'];
                $options['showAffix']['autogenerate'] = array_flip($options['showAffix']['autogenerate']);
            }
        }
        
        // create Form instance
        if (!class_exists('FormHelper')) {
            App::loadLib('App', 'FormHelper');
        }
        $Form = new FormHelper(array(
            'data' => $options['data'],
            'errors' => $options['errors'],
            'warnings' => $options['warnings'],
            'required' => $options['required'],
            'translated' => $options['translated'],
            'lang' => $options['lang'],
            'defaultValues' => $options['defaultValues'],
            'disabled' => $options['disabled'],
            'inputDefaults' => $options['inputDefaults'],
            'labelDefaults' => $options['labelDefaults'],
            'errorsDefaults' => $options['errorsDefaults'],
            'scrollToError' => $options['scrollToError'],
            'compatibility' => $options['compatibility'],
            'nonTranslatedProcessing' => $options['nonTranslatedProcessing'],
        ));
        
        if (!empty($options['fieldsView'])) {
            $options['Form'] = $Form;
            $options['fields'] = App::loadView($options['fieldsView']['module'], $options['fieldsView']['name'], $options);
        }
        else {
            // if no fields are provided then generate then either from provided 'schema' or 'data'
            if (empty($options['fields'])) {
                $options['fields'] = self::generateSmartFormFields($options);
            }

            // compile fields 
            $availableObjects = array(
                'row' => true,        
                '/row' => true,        
                'col' => true,        
                '/col' => true,        
                'fieldset' => '',
                '/fieldset' => true,
                'accordion' => '',
                '/accordion' => true,
                'tab' => '',
                '/tab' => true,
                'div' => true,
                '/div' => true,
                'h1' => '',
                'h2' => '',
                'h3' => '',
                'h4' => '',
                'h5' => '',
                'h6' => '',
                'field' => true,
                'html' => '',        
            );
            $availableProperties = array(
                'columns' => true,
                'deviceSize' => true,
                'rowTemplate' => true,
                'colTemplate' => true,
                'fieldsetTemplate' => true,
                'accordionTemplate' => true,
                'tabsTemplate' => true,
                'tabAnchorTemplate' => true,
                'tabContentTemplate' => true,
                'divTemplate' => true,
                'h1Template' => true,
                'h2Template' => true,
                'h3Template' => true,
                'h4Template' => true,
                'h5Template' => true,
                'h6Template' => true,
            );
            $blockStartObjects = array(
                'row' => true,        
                'col' => true,        
                'fieldset' => true,
                'accordion' => true,
                'tab' => true,
                'div' => true,
            );
            $blockEndObjects = array(
                '/row' => true,        
                '/col' => true,        
                '/fieldset' => true,
                '/accordion' => true,
                '/tab' => true,
                '/div' => true,
            );
            $nestingIncrementObjects = array(
                'fieldset' => true,
                'accordion' => true,
                'tab' => true,
            );
            $nestingDecrementObjects = array(
                '/fieldset' => true,
                '/accordion' => true,
                '/tab' => true,
            );
            $affixObjects = array(
                'row' => true,        
                'col' => true,        
                'fieldset' => true,
                'accordion' => true,
                'tab' => true,
                'div' => true,
                'h1' => true,
                'h2' => true,
                'h3' => true,
                'h4' => true,
                'h5' => true,
                'h6' => true,
                'field' => true,
            );
            $availableConditionParts = array(
                'if' => true,
                'elseif' => true,
                'else' => false,
                'endif' => false,
            );
            // init level properties by top level values
            $properties = array();
            $properties = self::setSmartFormProperties(
                $properties, 
                $options, 
                $availableProperties, 
                $options
            );
            $properties = self::resertSmartFormLevelProperties($properties);
            $parentProperties = array();
            $conditionNesting = 0; // 0 - there is no condition
            $falseConditionNesting = 0; // 0 - there is no nesting in false condition
            // init condition value:
            // - NULL - there in no started condition
            // - TRUE - there is executed condition
            // - FALSE - there is omitted condition
            // - 0 - there is omitted condition whose one branch has been already executed (used for elseif)
            $conditionValue = null; 
            // compiled fields contain fields definitions (just copied from $options['fields'])
            // and html chunks (noassoc items) like:
            //      array(
            //          '<div class="row">',
            //          '<div class="col-sm-2">',
            //          '<div class="form-group required"><label id="_data_User_firstname_input_1_1_label"...',
            //          '</div>',
            //          '<div class="col-sm-2">',
            //          '<div class="form-group required"><label id="_data_User_lastname_input_1_1_label"...',
            //          '</div>',
            //          '</div>',
            //          '<div class="row">',
            //          '<div class="form-group required"><label id="_data_UserProfile_single_input_1_1_label"...',
            //          ...
            //      )
            $compiledFields = array();
            $parentProperties; //debug
            $nestingLevel = 0;
            self::$affixItems = array();
            foreach ($options['fields'] as $k => $v) {
                $controls = array();
                $fieldName = null;
                $fieldInput = array();
                // separate field inputs from controls
                // - if control definition
                if (
                    is_int($k) 
                    && (
                        !is_array($v)
                        || 
                        empty($v['field'])
                        && reset($v) !== 'field'
                    )
                ) {
                    $controls = (array)$v;
                    // normalize nonassociative objects and condition parts to associative for easier processing
                    foreach ($controls as $k1 => $v1) {
                        if (!is_int($k1)) {
                            continue;
                        }
                        if (isset($availableObjects[$v1])) {
                             $controls[$v1] = $availableObjects[$v1];
                        }
                        if (isset($availableConditionParts[$v1])) {
                            if ($availableConditionParts[$v1] === true) {
                                throw new Exception(__e(__FILE__, 'Invalid definition of condition "%s". Must be defined as associative item with boolean value'), $v1);
                            }
                            $controls[$v1] = null; // else, endif
                        }
                        // remove any nonassociative items
                        unset($controls[$k1]);
                    }
                    // check for conditions 'if', 'elseif', 'else', 'endif'
                    foreach ($controls as $k1 => $v1) {
                        // if not condition part then continue
                        if (!isset($availableConditionParts[$k1])) {
                            continue;
                        }
                        // get part name and for associative items new value
                        $conditionPart = $k1;
                        if ($availableConditionParts[$k1] === true) { // if, elseif
                            $conditionNewValue = (bool)$v1;
                        }
                        // remove from controls for further processing
                        unset($controls[$k1]);
                        // process conditions
                        if ($conditionPart === 'if') {
                            // if no condition opened
                            if ($conditionValue === null) {
                                $conditionValue = $conditionNewValue;
                                $conditionNesting++;
                            }
                            // if in executed condition branch
                            elseif ($conditionValue === true) {
                                $conditionValue = $conditionNewValue;
                                $conditionNesting++;
                            }
                            else {
                                $falseConditionNesting++;
                            }
                        }
                        // process 'elseif' only in executed branches, skip it in omitted 
                        elseif (
                            $falseConditionNesting === 0 
                            && $conditionPart === 'elseif'
                        ) {
                            // if no condition opened
                            if ($conditionValue === null) {
                                throw new Exception(__e(__FILE__, 'Invalid definition of "elseif" without opening "if"'));
                            }
                            // if an alternative to executed condition branch (direct or in queue of previous)
                            elseif (
                                $conditionValue === true
                                || $conditionValue === 0
                            ) {
                                $conditionValue = 0;
                            }
                            // if an alternative to omitted condition branch
                            else {
                                $conditionValue = $conditionNewValue;
                            }
                        }
                        // process 'else' only in executed branches, skip it in omitted 
                        elseif (
                            $falseConditionNesting === 0 
                            && $conditionPart === 'else'
                        ) {
                            // if no condition opened
                            if ($conditionValue === null) {
                                throw new Exception(__e(__FILE__, 'Invalid definition of "else" without opening "if"'));
                            }
                            // if an alternative to executed condition branch (direct or in queue of previous)
                            elseif (
                                $conditionValue === true
                                || $conditionValue === 0
                            ) {
                                $conditionValue = false;
                            }
                            // if an alternative to omitted condition branch
                            else {
                                $conditionValue = true;
                            }
                        }
                        elseif ($conditionPart === 'endif') {
                            // if no condition opened
                            if ($conditionValue === null) {
                                throw new Exception(__e(__FILE__, 'Invalid definition of "endif" without opening "if"'));
                            }
                            // if an valid condition ending on proper false conditions nesting
                            elseif ($falseConditionNesting === 0) {
                                $conditionNesting--;
                                // we ended top level condition
                                if ($conditionNesting === 0) {
                                    $conditionValue = null;
                                }
                                // we ended nested condition, the parent condition branch is
                                // executed (we are here) so the parent $conditionValue must be TRUE
                                else {
                                    $conditionValue = true;
                                }
                            }
                            // if omitted branch then just decrease $falseConditionNesting
                            if (
                                $conditionValue !== null
                                && $conditionValue !== true
                                && $falseConditionNesting > 0
                            ) {
                                $falseConditionNesting--;
                            }
                        }
                        break;
                    }
                    // if the actual condition value is FALSE then skip further processing of fields
                    // till next condition part
                    if (
                        $conditionValue !== null
                        && $conditionValue !== true
                    ) {
                        continue;
                    }
                    // throw exception in case of invalid object definition
                    if (count($definedObjects = array_intersect_key($controls, $availableObjects)) > 1) {
                        throw new Exception(__e(
                            __FILE__, 
                            'Multiple objects (%s) provided in one definition. Please define objects separately', 
                            implode(', ', array_keys($definedObjects))
                        ));
                    }
                    // get actual object name (if any)
                    $object = null;
                    if ($definedObjects) {
                        $definedObjects = array_keys($definedObjects);
                        $object = reset($definedObjects);
                        // check for object substitution
                        if (!empty($options['substitutions'])) {
                            $startObject = ltrim($object, '/');
                            if (!empty($options['substitutions'][$startObject])) {
                                $substitution = $options['substitutions'][$startObject];
                                if (isset($availableObjects[$substitution])) {
                                    if (substr($object, 0, 1) === '/') {
                                        if(isset($availableObjects['/' . $substitution])) {
                                            $substitution = '/' . $substitution;
                                        }
                                        else {
                                            $substitution = null;
                                        }
                                    }
                                    $controls[$substitution] = $controls[$object];
                                    $object = $substitution;
                                }
                            }
                        }
                    }

                    // check for affix
                    if ($options['showAffix']) {
                        // increase/descrease nesting level (used only for affix)
                        if (isset($nestingIncrementObjects[$object])) {
                            $nestingLevel++;
                        }
                        elseif (isset($nestingDecrementObjects[$object])) {
                            $nestingLevel--;
                        }
                        // check for affix autogeneration
                        if (isset($options['showAffix']['autogenerate'][$object])) {
                            $controls['affix'] = true;
                        }
                        // add affix item if the control affix property is set
                        if (
                            isset($affixObjects[$object])
                            && isset($controls['affix'])
                        ) {
                            self::addAffixItem($controls, $object, $nestingLevel);
                        }
                    }
                    unset($controls['affix']);

                    // treat explicit objects in outgoing scope contenxt
                    // - on explicit block start
                    if (isset($blockStartObjects[$object])) {
                        // if we are in opened implicit row then create an implicit row end before
                        // any block start except of 'col'
                        if (
                            $object !== 'col'
                            && !empty($properties['openedRow'])
                            && !$properties['explicitRow'] 
                        ) {
                            $compiledFields[] = self::getSmartFormObjectTemplatePart('/row', $properties, $controls, $availableObjects, $availableConditionParts);
                            $properties['colIndex'] = 0;
                            $properties['openedRow'] = false;
                        }
                        // - if col start and we are not in explicit row 
                        // then create an implicit row start if columnIndex is 0
                        if (
                            $object === 'col'
                            && !$properties['explicitRow']
                            && $properties['colIndex'] === 0
                        ) {
                            $compiledFields[] = self::getSmartFormObjectTemplatePart('row', $properties, $controls, $availableObjects, $availableConditionParts);
                            $properties['openedRow'] = true;
                        }       
                        // set explicit template starts 
                        $compiledFields[] = self::getSmartFormObjectTemplatePart($object, $properties, $controls, $availableObjects, $availableConditionParts);
                        // if col then update col properties
                        if ($object === 'col') {
                            $properties['colIndex']++;
                            $properties['openedCol'] = true;
                        }
                        $properties['previousObject'] = $object;
                        // store this scope properties
                        array_push($parentProperties, $properties);
                    }
                    // - on explicit block end
                    elseif (isset($blockEndObjects[$object])) {
                        // if we are in opened implicit row then create an implicit row end before
                        // any block end except of '/col' and '/row'
                        if (
                            $object !== '/col'
                            && $object !== '/row'
                            && $properties['openedRow']
                            && !$properties['explicitRow'] 
                        ) {
                            $compiledFields[] = self::getSmartFormObjectTemplatePart('/row', $properties, $controls, $availableObjects, $availableConditionParts);
                            $properties['colIndex'] = 0;
                            $properties['openedRow'] = false;
                        }
                        // set template ends 
                        $compiledFields[] = self::getSmartFormObjectTemplatePart($object, $properties, $controls, $availableObjects, $availableConditionParts);
                        // restore properties values of parent scope 
                        $properties = array_pop($parentProperties);
                        $properties['previousObject'] = $object;
                        // reset columnIndex after row, fieldset, accordion and tab end
                        if ($object !== '/col') {
                            $properties['colIndex'] = 0;
                        }
                        // - if col end and we are not in explicit row
                        // then create an implicit row end if columnIndex equals to columnsCount
                        if (
                            $object === '/col'
                            && !$properties['explicitRow']
                            && $properties['colIndex'] === $properties['colCount']
                        ) {
                            $compiledFields[] = self::getSmartFormObjectTemplatePart('/row', $properties, $controls, $availableObjects, $availableConditionParts);
                            $properties['colIndex'] = 0;
                            $properties['openedRow'] = false;
                        }    
                    }

                    // treat objects starts in nested scope context (reset scope properties)
                    if (isset($blockStartObjects[$object])) {                    
                        // reset level properties
                        $properties = self::resertSmartFormLevelProperties($properties);

                        // set auxiliary properties
                        // - set _explictRow flag on explicit row start 
                        if ($object === 'row') {
                            $properties['explicitRow'] = true;
                            $properties['openedRow'] = true;
                        }
                        // - set _explictColumn flag on explicit column start
                        if ($object === 'col') {
                            $properties['explicitCol'] = true;
                            $properties['openedCol'] = true;
                        }
                        $properties['templateLabel'] = $controls[$object];                    
                    }
                    // put html into compiled fields
                    elseif ($object === 'html') {
                        $compiledFields[] = $controls[$object];
                    }
                    // put headings into compiled fields
                    elseif (
                        $object === 'h1'
                        || $object === 'h2'
                        || $object === 'h3'
                        || $object === 'h4'
                        || $object === 'h5'
                        || $object === 'h6'
                    ) {
                        $compiledFields[] = 
                            self::getSmartFormObjectTemplatePart($object, $properties, $controls, $availableObjects, $availableConditionParts);
                    }

                    // update properties values for nested scope
                    $properties = self::setSmartFormProperties(
                        $properties, 
                        $controls, 
                        $availableProperties, 
                        $options
                    );
                }
                // - if field input definition
                elseif (is_array($v)) {
                    // if the actual condition value is FALSE then skip further processing of fields
                    // till next condition part
                    if (
                        $conditionValue !== null
                        && $conditionValue !== true
                    ) {
                        continue;
                    }

                    if (!empty($v['field'])) {
                        $fieldName = $v['field'];
                    }
                    elseif (reset($v) === 'field') {
                        $fieldName = null;
                        array_shift($v);
                    }
                    else {
                        $fieldName = $k;
                    }
                    $fieldInput = $v;
                    $compiledField = array();

                    // check for affix
                    if (
                        $options['showAffix']
                        && isset($fieldInput['affix'])
                        ||
                        // check for affix autogeneration
                        isset($options['showAffix']['autogenerate']['field'])
                    ) {
                        self::addAffixItem($fieldInput, 'field', $nestingLevel);
                    }
                    unset($fieldInput['affix']);

                    // normalize field input type
                    if (
                        empty($fieldInput['type'])
                    ) {
                        // if primary key has no type specified then set it to hidden
                        if ($fieldName === $options['primaryKey']) {
                            $fieldInput['type'] = 'hidden';
                        }
                        // if present in file fields then set to 'file' input type
                        elseif (!empty($options['fileFields'][$fieldName])) {
                            $fieldInput['type'] = 'file';
                        }
                        elseif (!empty($options['schema'][$fieldName]['type'])) {
                            $inputOptions = FormHelper::getInputOptionsFromModelFieldSchema($options['schema'][$fieldName]);
                            $fieldInput['type'] = $inputOptions['type'];
                        }
                        else {
                            $fieldInput['type'] = 'text';
                        }
                    }
                    // normalize select input options
                    if (
                        !empty($fieldInput['type'])
                        && $fieldInput['type'] === 'select'
                        && empty($fieldInput['options'])
                        && !empty($options['schema'][$fieldName]['values'])
                    ) {
                        $fieldInput['options'] = array_combine(
                            (array)$options['schema'][$fieldName]['values'], 
                            (array)$options['schema'][$fieldName]['values']
                        );
                    }
                    // set enctype to 'multipart/form-data' if there is an file input
                    if (!empty($fieldInput['type']) && $fieldInput['type'] === 'file') {
                        $options['enctype'] = 'multipart/form-data';
                    }               

                    // treat implicit objects (row, col) before visible field input 
                    if ($fieldInput['type'] !== 'hidden') {
                        // - if we are not in explicit row and not in explicit col 
                        // then create an implicit row start if columnIndex is 0
                        if (
                            !$properties['explicitRow'] 
                            && !$properties['explicitCol']
                            && $properties['colIndex'] === 0
                        ) {
                            $compiledField[] = self::getSmartFormObjectTemplatePart('row', $properties, $controls, $availableObjects, $availableConditionParts);
                            $properties['openedRow'] = true;
                        }
                        // - if we are not in explicit column then create an implicit column start for each new input
                        if (!$properties['explicitCol']) {
                            $compiledField[] = self::getSmartFormObjectTemplatePart('col', $properties, $controls, $availableObjects, $availableConditionParts);
                            $properties['colIndex']++;
                            $properties['openedCol'] = true;
                        }
                    }

                    $fieldInput = array_diff_key($fieldInput, $availableProperties, $availableObjects, $availableConditionParts);
                    $compiledField[] = $fieldHtml = $Form->input($fieldName, $fieldInput);

                    // treat implicit objects (row, col) after visible field input 
                    if ($fieldInput['type'] !== 'hidden') {
                        // - if we are not in explicit column then create an implicit column end for each new input
                        if (!$properties['explicitCol']) {             
                            $compiledField[] = self::getSmartFormObjectTemplatePart('/col', $properties, $controls, $availableObjects, $availableConditionParts);
                            $properties['openedCol'] = false;
                        }
                        // - if we are not in explicit row and not in explicit col 
                        // then create an implicit row end if columnIndex equals to columnsCount
                        if (
                            !$properties['explicitRow'] 
                            && !$properties['explicitCol']
                            && $properties['colIndex'] === $properties['colCount']
                        ) {
                            $compiledField[] = self::getSmartFormObjectTemplatePart('/row', $properties, $controls, $availableObjects, $availableConditionParts);
                            $properties['colIndex'] = 0;
                            $properties['openedRow'] = false;
                        }    
                    }

                    // if no field html is generated (if the nontranslated field is ignored)
                    // then omit it also in smartform (together with its implicit col)
                    if (!empty($fieldHtml)) {    
                        $compiledFields = array_merge($compiledFields, $compiledField);
                    }

                    $properties['previousObject'] = 'fieldInput';
                } 
                else {
                    throw new Exception(__e(__FILE__, 'Invalid definition of item "%s" => "%s" in fields. It is neither control block neither field.', $k, $v));
                }
            }
            // close unclosed col (this should not happen)
            if ($properties['openedCol']) {
                $compiledFields[] = self::getSmartFormObjectTemplatePart('/col', $properties, $controls, $availableObjects, $availableConditionParts);
                $properties['openedCol'] = false;
            }
            // close unclosed row
            if ($properties['openedRow']) {
                $compiledFields[] = self::getSmartFormObjectTemplatePart('/row', $properties, $controls, $availableObjects, $availableConditionParts);
                $properties['openedRow'] = false;
            }

            $options['fields'] = implode('', $compiledFields);
        }
        
        // normalize form action, method, enctype and class
        if ($options['action']) {
            $options['action'] = ' action="' . App::getUrl($options['action']) . '"';
        }
        if ($options['method']) {
            $options['method'] = ' method="' . $options['method'] . '"';
        }
        if ($options['enctype']) {
            $options['enctype'] = ' enctype="' . $options['enctype'] . '"';
        }
        if ($options['class']) {
            $options['class'] = ' ' . $options['class'];
        }
        $options['affixItems'] = self::$affixItems;
        return App::loadView($options['view']['module'], $options['view']['name'], $options);
    }
    
    /**
     * Normalizes provided 'columns' option to column spans form.
     *  
     * @param int|array $columns
     * @param int $maxColumns Max number of columns
     * 
     * @return array Column spans array. If columns is provided as array then it is
     *      considered to be column spans array and it is returned without change.
     */
    protected static function normalizeSmartFormColumns($columns, $maxColumns) {
        if (Validate::intNumber($columns)) {
            $span = (float)$maxColumns / (float)$columns;
            // if the span is integer
            if ($span == floor($span)) {
                $columnSpans = array_fill(0, $columns, (int)$span);
            }
            else {
                $span = (int)floor($span);
                $rest = $maxColumns - $columns * $span;
                $columnSpans = array_fill(0, $columns, $span);
                foreach($columnSpans as &$columnSpan) {
                    $columnSpan++;
                    $rest--;
                    if ($rest === 0) {
                        break;
                    }
                }
                $columnSpans[0] = $span + 1;
            }
            $columns = $columnSpans;
        }
        return $columns;
    } 
    
    /**
     * Generates smart form fields from options passed to smartForm.
     * Options 'schema', 'data' and 'primaryKey' are used to generate fields.
     * 
     * @param array $options
     * 
     * @return array Fields array
     * 
     * @throws Exception if neither schema nor data are defined in provided options. 
     */
    public static function generateSmartFormFields($options) {
        $defaults = array(
            'primaryKey' => 'id',
            'schema' => array(),
            'fileFields' => array(),
            'data' => array(),
        );
        $options = array_merge($defaults, $options);   
        // normalize options
        $options['schema'] = Arr::deflateData($options['schema']);
        $options['fileFields'] = Arr::deflateData($options['fileFields']);
        $options['data'] = Arr::deflateData($options['data']);
        
        $fields = array();
        if ($options['schema']) {
            // if data are passed then generate inputs just for data fields
            if ($options['data']) {
                $fieldNames = array_keys($options['data']);
            }
            // otherwise generate inputs for all schema fields 
            else {
                $fieldNames = array_keys($options['schema']);
            }
            if (!class_exists('FormHelper')) {
                App::loadLib('App', 'FormHelper');
            }
            foreach ($fieldNames as $fieldName) {
                // set field input label
                $fields[$fieldName] = array('label' => Str::humanize($fieldName, array('_', '.')));
                // set input type
                // - primary key field must be 'hidden' input type
                if ($fieldName === $options['primaryKey']) {
                    $fields[$fieldName]['type'] = 'hidden';
                    continue;
                }
                // if present in file fields then set to 'file' input type
                if (!empty($options['fileFields'][$fieldName])) {
                    $fields[$fieldName]['type'] = 'file';
                    continue;
                }
                // - if field type is not defined in schema then set input type to 'text'
                if (empty($options['schema'][$fieldName]['type'])) {
                    $fields[$fieldName]['type'] = 'text';
                    continue;
                }
                // - otherwise set field input options according to field schema
                $fields[$fieldName] = array_merge(
                    $fields[$fieldName], 
                    FormHelper::getInputOptionsFromModelFieldSchema($options['schema'][$fieldName])
                );
            }
        }
        elseif ($options['data']) {
            foreach ($options['data'] as $fieldName => $v) {
                // set field input label
                $fields[$fieldName] = array('label' => Str::humanize($fieldName, array('_', '.')));
                // set input type
                // - primary key field must be 'hidden' input type
                if ($fieldName === $options['primaryKey']) {
                    $fields[$fieldName]['type'] = 'hidden';
                    continue;
                }
                // if present in file fields then set to 'file' input type
                if (!empty($options['fileFields'][$fieldName])) {
                    $fields[$fieldName]['type'] = 'file';
                    continue;
                }
                // - for all others inputs set type to 'text'
                $fields[$fieldName]['type'] = 'text';
            }

        }
        else {
            throw new Exception(__e(__FILE__, "Impossible to generate fields. Provide at least one of options 'schema' or 'data'."));
        }    
        
        return $fields;
    }
    
    /**
     * Sets properties new values
     * 
     * @param array $properties Actual properties
     * @param array $newValues New values for properties
     * @param array $availableProperties List of available properties
     * @param array $options Options passed to Html::smartForm()
     * 
     * @return array Properties with actualized values
     */
    protected static function setSmartFormProperties($properties, $newValues, $availableProperties, $options) {
        $properties = array_merge($properties, array_intersect_key($newValues, $availableProperties));
        
        if (isset($newValues['columns'])) {
            $properties['columns'] = self::normalizeSmartFormColumns($properties['columns'], $options['maxColumns']);
            $properties['colCount'] = count($properties['columns']); 
        }
        if (isset($newValues['rowTemplate'])) {
            $properties['rowTemplateParts'] = explode(':content:', $properties['rowTemplate']);
        }
        if (isset($newValues['colTemplate'])) {
            $properties['colTemplateParts'] = explode(':content:', $properties['colTemplate']);
        }
        if (isset($newValues['fieldsetTemplate'])) {
            $properties['fieldsetTemplateParts'] = explode(':content:', $properties['fieldsetTemplate']);
        }
        if (isset($newValues['accordionTemplate'])) {
            $properties['accordionTemplateParts'] = explode(':content:', $properties['accordionTemplate']);
        }
        if (isset($newValues['tabsTemplate'])) {
            $properties['tabsTemplateParts'] = explode(':contents:', $properties['tabsTemplate']);
        }
        if (isset($newValues['tabAnchorTemplate'])) {
            $properties['tabAnchorTemplateParts'] = explode(':label:', $properties['tabAnchorTemplate']);
        }
        if (isset($newValues['tabContentTemplate'])) {
            $properties['tabContentTemplateParts'] = explode(':content:', $properties['tabContentTemplate']);
        }
        if (isset($newValues['divTemplate'])) {
            $properties['divTemplateParts'] = explode(':content:', $properties['divTemplate']);
        }
        if (isset($newValues['h1Template'])) {
            $properties['h1TemplateParts'] = explode(':content:', $properties['h1Template']);
        }
        if (isset($newValues['h2Template'])) {
            $properties['h2TemplateParts'] = explode(':content:', $properties['h2Template']);
        }
        if (isset($newValues['h3Template'])) {
            $properties['h3TemplateParts'] = explode(':content:', $properties['h3Template']);
        }
        if (isset($newValues['h4Template'])) {
            $properties['h4TemplateParts'] = explode(':content:', $properties['h4Template']);
        }
        if (isset($newValues['h5Template'])) {
            $properties['h5TemplateParts'] = explode(':content:', $properties['h5Template']);
        }
        if (isset($newValues['h6Template'])) {
            $properties['h6TemplateParts'] = explode(':content:', $properties['h6Template']);
        }
        return $properties;
    }
    
    /**
     * Resets properties which must be reset on each new scope start
     * 
     * @param array $properties Actual properties
     * 
     * @return array Properties with reseted level items
     */
    protected static function resertSmartFormLevelProperties($properties) {
        $properties['colIndex'] = 0; 
        $properties['explicitRow'] = false; 
        $properties['openedRow'] = false; 
        $properties['explicitCol'] = false; 
        $properties['openedCol'] = false; 
        $properties['templateLabel'] = null;
        // previous object, e.g. '/fieldset', '/tab', ... 'fieldInput' for input definition
        // NULL if there was no previous object (we are on begining of new scope)
        $properties['previousObject'] = null; 
        $properties['tabCount'] = null; // last item 
        
        return $properties;
    }
    
    /**
     * Returns template part for provided object
     * 
     * @param string $object One of 'col', 'row', 'fieldset', 'accordion', 'tab',
     *      '/col', '/row', '/fieldset', '/accordion', '/tab'
     * @param array $properties
     * @param array $controls Actual smart form controls array
     * @param array $availableObjects Smart form $availableConditionParts array
     * @param array $availableConditionParts Smart form $availableConditionParts array
     * 
     * @return string
     * 
     * @throws Exception on columns overflow (e.g. creating 3rd column start in 2 columns grid)
     */
    protected static function getSmartFormObjectTemplatePart($object, $properties, $controls, $availableObjects, $availableConditionParts) {
        // set template starts 
        if ($object === 'tab') {
            // treat tab separately - @todo
        }
        elseif (substr($object, 0, 1) !== '/') {
            // check for hint
            $hintHtml = '';
            if (!empty($controls['hint'])) {
                $hintOptions = array();
                if (is_array($controls['hint'])) {
                    $hintText = Sanitize::value($controls['hint']['text']);
                    unset($controls['hint']['text']);
                    $hintOptions = $controls['hint'];
                }
                else {
                    $hintText = $controls['hint'];
                }
                if (!empty($hintText)) {
                    $hintHtml = self::hint($hintText, $hintOptions);
                }
                unset($controls['hint']);
            }
            // get attributes
            $customAttributes = array_diff_key($controls, $properties, $availableObjects, $availableConditionParts);
            $attributes = array();
            if ($object === 'col') {
                if (!isset($properties['columns'][$properties['colIndex']])) {
                    throw new Exception(__e(__FILE__, 'Columns overflow in %s columns grid', (isset($properties['columns']) ? count($properties['columns']) : '???')));
                }
                $attributes['class'] = 'col-' . $properties['deviceSize'] . '-' . $properties['columns'][$properties['colIndex']];
            }
            elseif ($object === 'row') {
                $attributes['class'] = 'row';
            }
            elseif ($object === 'accordion') {
                $attributes['class'] = '-run-sfo-accordion';
            }
            $attributesHtml = '';
            if (
                !empty($attributes) 
                || !empty($customAttributes)
            ) {
                $attributes = self::mergeAttributes($attributes, $customAttributes);
                $attributesHtml = self::attributes($attributes);
            }
            // return template part
            $templatePartsName = $object . 'TemplateParts';
            return str_replace(
                array(':attributes:', ':label:', ':hint:'), 
                //rblb//array($attributesHtml, $properties['templateLabel']), // $properties['templateLabel'] is not set yet when calling for template starts
                array($attributesHtml, (!isset($availableObjects[$object]) || $availableObjects[$object] === true) ? '' : $controls[$object], $hintHtml), 
                $properties[$templatePartsName][0]
            );
        }
        // set template ends 
        elseif ($object === '/tab') {
            // treat tab separately - @todo
        }
        else {
            $templatePartsName = substr($object, 1) . 'TemplateParts';
            if (!empty($properties[$templatePartsName][1])) {
                return str_replace(':label:', $properties['templateLabel'], $properties[$templatePartsName][1]);
            }
            return '';
        }
    }
    
    /**
     * 
     * @param array& $controls
     * @param string $object
     */
    protected static function addAffixItem(&$controls, $object, $nestingLevel) {
        static $idBase = null;
        static $idCount = 0;
        // if there is no affix property set in $controls then do nothing
        if (
            !isset($controls['affix'])
            || $controls['affix'] === false
        ) {
            return;
        }
        // create base for affix ids on very first call of this method
        if ($idBase === null) {
            $idBase = uniqid('affix_');
        }
        // get affix id
        // - if the object itself has specified id then use that
        if (!empty($controls['id'])) {
            $id = $controls['id'];
        }
        // - if object has no id specified than generate random one
        else {
            $id = $controls['id'] = $idBase . $idCount++;
        }
        // - if creating affix for form field then attach it to field label
        if ($object === 'field') {
            $id .= '_label';
        }
        // normalize affix item definition
        $affix = $controls['affix'];
        if ($affix === true) {
            if ($object === 'field' && !empty($controls['label'])) {
                $affix = array('level' => null, 'label' => $controls['label']);
            }
            elseif (!empty($controls[$object])) {
                $affix = array('level' => null, 'label' => $controls[$object]);
            }
            else {
                $affix = array('level' => null, 'label' => '???');
            }
        }
        elseif (Validate::intNumber($affix)) {
            $affix = array('level' => $affix, 'label' => null);
        }
        elseif (!is_array($affix)) {
            $affix = array('level' => null, 'label' => (string)$affix);
        }
        // - add affix anchor according the target object id
        $affix['anchor'] = $id;
        // - keep track of object the affix item is targeting
        $affix['object'] = $object;
        // - resolve label if not specified
        if (empty($affix['label'])) {
            if (
                $object === 'field'
                && !empty($controls['label'])
            ) {
                $affix['label'] = $controls['label'];
            }
            elseif (!empty($controls[$object])) {
                $affix['label'] = $controls[$object];
            }
            else {
                $affix['label'] = __e(__FILE__, 'Missing affix label');
            }
        }
        // - resolve level if not specified
        $previousAffix = end(self::$affixItems);
        if (
            !isset($affix['level'])
            || !Validate::intNumber($affix['level'])
        ) {
            // set affix level to actual nesting level
            $affix['level'] = $nestingLevel;
            // for case of headings add the heading level to nesting level
            if ($object === 'h1') {
                $affix['level'] += 1;
            }
            elseif ($object === 'h2') {
                $affix['level'] += 2;
            }
            elseif ($object === 'h3') {
                $affix['level'] += 3;
            }
            elseif ($object === 'h4') {
                $affix['level'] += 4;
            }
            elseif ($object === 'h5') {
                $affix['level'] += 5;
            }
            elseif ($object === 'h6') {
                $affix['level'] += 6;
            }
            // in case of fields add 1 to prev
            elseif ($object === 'field') {
                if (!$previousAffix) {
                    $affix['level'] = 1;
                }
                elseif ($previousAffix['object'] === 'field') {
                    $affix['level'] = $previousAffix['level'];
                }
                else {
                    $affix['level'] = $previousAffix['level'] + 1;
                }
            }
        }
        // restrain affix levels to be increased always only by 1
        if (!$previousAffix) {
            $affix['level'] = 1;
        }
        elseif ($affix['level'] > $previousAffix['level'] + 1) {
            $affix['level'] = $previousAffix['level'] + 1;
        }
        self::$affixItems[] = $affix;
    }
    
    /**
     * Returns affix html
     * 
     * @param array $items Array of affix items with following structure:
     *      array(
     *          array(
     *              'label' => 'Label 1'
     *              'anchor' => 'id1'
     *              'level' => '1' // starting from 1 for toplevel
     *          ),
     *          array(
     *              'label' => 'Label 2'
     *              'anchor' => 'id2'
     *              'level' => '2'
     *          ),
     *          array(
     *              'label' => 'Label 3'
     *              'anchor' => 'id3'
     *              'level' => '1' 
     *          ),
     *          ...,
     *      )
     * @param array $options Follooving options are available:
     *      - 'id' (string) Affix main container id. Defaults to NULL, means 
     *          an random id is generated.
     *      - 'class' (string) Affix main container class. Defaults to NULL.
     *      - 'effectColor' (string) Effect color of affixed element. Possible 
     *          values are any CSS color format or 'inherit' to inherit color 
     *          of affix link. If empty then color is not changed by effect. 
     *          Defaults to NULL.
     *      - 'effectColor' (string) Effect background-color of affixed element.
     *          Possible values are any CSS color format or 'inherit' to inherit 
     *          background-color of affix link. If empty then background-color is 
     *          not changed by effect. Defaults to '#FFF575'.
     *      - 'effectTime' (int) Effect time in miliseconds of affixed element.
     *          Defaults to 1000.
     *  
     * @return Affix html
     */
    public static function affix($items, $options = array()) {
        $defaults = array(
            'id' => null,
            'class' => null,
            'contentSelector' => 'body',
            'effectColor' => null,
            'effectBgColor' => '#e50031',
            'effectTime' => 1000,
        );
        $options = array_merge($defaults, $options);
        // normalize options
        if (empty($options['id'])) {
            $options['id'] = uniqid('affixNav_');
        }
        // convert items format to tree format
        $options['tree'] = Arr::buildTree($items);
        return App::loadView('App', 'Html/affix', $options);
    }
    
    /**
     * Returns html code of smart admin
     * 
     * @param array $options Following are available:
     *      - 'navigationType' (string) Possible values are 'top' or 'side'. Defaults to 'side'.
     *      - 'navigationHeader' (string) Html code to be displayed in side navigation header. 
     *          This options applied only if 'navifationType' is set to 'side'.
     *          Defaults to setting 'App.name'.
     *      - 'navigationHeaderLink' (boolean) If TRUE then 'navigationHeader' is wrapped into
     *          link targeting to fronend homepage. Defaults to FALSE. 
     *      - 'view' (string|array) Name of view for smartAdmin rendering. If string 
     *          then the view is searched under App module. To provide views from 
     *          other modules use array like array('module' => ..., 'name' => ...)
     *          Defaults to 'Html/smartAdmin' for 'top' navigation type and to 
     *          'Html/smartAdminWithSideNavigation' for 'side' navigation type.
     *      - 'menuTypes' (string|array) Plain list of available menu types. Menus
     *          are generated in order they are listed here. Special menu type is 
     *          'search' (see below) Defaults to array('userMenu', 'search', 'primaryMenu', 'secondaryMenu').
     *      - 'userMenu' | 'primaryMenu' | 'secondaryMenu' | ... (array) Definition of menu 
     *          specified in 'menuTypes' option in form of array like:
     * 
     *              array(
     *                   array(
     *                      'label => 'Eshop',
     *                      'rights' => FALSE // explicitly turn off the module menu
     *                      'icon' => '/app/img/admin/system.png'
     *                      'tabActivator' => '/mvc/Eshop/EshopProducts/admin_index',
     *                      'submenu' => array(
     *                          array(
     *                              'label' => 'Products',
     *                              'url' => '/mvc/Eshop/EshopProducts/admin_index',
     *                              'rights' => App::getUser('Group.pid') === 'storekeepers',
     *                              'target' => '_blank',
     *                              'title' => '...'
     *                              'icon' => '/app/img/admin/languages.png'
     *                              'tabTitle' => null,
     *                              'tabClass' => null,
     *                              'tabIcon' => null,
     *                              'tabSingle' => false,
     *                              'tabFrameType' => null,
     *                          ),
     *                          array(
     *                              'label' => 'Orders'
     *                              'url' => '/mvc/Eshop/EshopOrders/admin_index'
     *                              'if' => App::getUser('Group.pid') === 'acountants', // use 'rights' or 'if' according the meaning
     *                          ),
     *                          // any nonassociative item with nonarray value is considered to be divider 
     *                          '---', 
     *                          array(...),
     *                          ...,
     *                      ),
     *                  ),
     *                  // item without submenu
     *                  array(
     *                      'label' => 'Logout,
     *                      'url' => '/runlogout',
     *                      'target' => null, // cancel default 'target' => '_blank' to not launche logout action in new tab but in main window
     *                  )   
     *                  ...,
     *              ),
     * 
     *          Only menu items the user has rights for are generated.
     * 
     *      - 'search' (string) Search input html. Normally an action defining search input
     *          and search logis should be loaded here by App::loadControllerAction().
     *          This is considered only if mentioned in 'menuTypes' option.
     * 
     *      - 'homeTab' (array) Home tab definition array. Home tab is a tab which 
     *          is not necessarily loaded if there are other opened tabs. If defined 
     *          then it is opened always when no other tabs are opened (when option 
     *          'tabs' is empty) or when all tabs get closed. Array structure is:
     * 
     *              array(
     *                  'url' => '/mvc/App/Users/<USER>'
     *                  'rights' => App::getUser('Group.pid') === 'admins',
     *                  'title' => null,
     *                  'class' => null,
     *                  'icon' => null,
     *                  'single' => true,
     *                  'frameType' => null,
     *                  'closeButton' => true,
     *                  'show' => true,
     *              ),
     * 
     *      - 'tabs' (array) Definition of tabs which should be opened on admin creation
     *          in form of array of arrays like:
     *              
     *              array(
     *                  array(
     *                      'url' => '/mvc/App/Users/<USER>'
     *                      'rights' => App::getUser('Group.pid') === 'admins',
     *                      'title' => null,
     *                      'class' => null,
     *                      'icon' => null,
     *                      'single' => true,
     *                      'frameType' => null,
     *                      'closeButton' => true,
     *                      'show' => true,
     *                  ),
     *                  array(...),
     *                  ...,
     *              )
     *                  
     *          Only tabs the user has rights for are generated.
     */             
    public static function smartAdmin($options = array()) {
        $defaults = array(
            'navigationType' => 'side',
            'navigationHeader' => App::getSetting('App', 'name'),
            'navigationHeaderLink' => false,
            'view' => null,
            'class' => null,
            'id' => null,
            'menuTypes' => array('userMenu', 'search', 'primaryMenu', 'secondaryMenu'),
            'homeTab' => null,
            'tabs' => array(),
        );
        $options = array_merge($defaults, $options);      
        $menuDefaults = array(
            'url' => null,
            'target' => '_blank',
            'label' => null,
            'title' => null,
            'class' => null,
            'icon' => null,
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            // ATTENTION: for side navigationType all tabs are forced to be single (see below)
            'tabSingle' => false,
            'tabFrameType' => null,
            'submenu' => array(),
        );
        $submenuDefaults = array(
            'url' => null,
            'target' => '_blank',
            'label' => null,
            'title' => null,
            'class' => null,
            'icon' => null,
            'tabTitle' => null,
            'tabClass' => null,
            'tabIcon' => null,
            // ATTENTION: for side navigationType all tabs are forced to be single (see below)
            'tabSingle' => false,
            'tabFrameType' => null,
        );
        $tabDefaults = array(
            'url' => null,
            'title' => '&nbsp;',
            'class' => null,
            'icon' => null,
            // ATTENTION: for side navigationType all tabs are forced to be single (see below)
            'single' => false,
            'frameType' => null,
            'closeButton' => true,
            'show' => true,
        );
        // normalize navigation type
        $options['navigationType'] = strtolower($options['navigationType']);
        // normalize view
        if (empty($options['view'])) {
            if ($options['navigationType'] === 'side') {
                $options['view'] = 'Html/smartAdminWithSideNavigation';
            }
            else {
                $options['view'] = 'Html/smartAdmin';
            }
        }
        if (is_string($options['view'])) {
            $options['view'] = array('module' => 'App', 'name' => $options['view']);
        }
        // normalize menu types
        $options['menuTypes'] = (array)$options['menuTypes'];
        // process menu by menu
        foreach ($options['menuTypes'] as $k0 => $menuType) {
            // if menu type is not defined or empty then remove it and skip to next
            if (empty($options[$menuType])) {
                unset($options['menuTypes'][$k0]);
                continue;
            }
            // let the search as it is
            if ($menuType === 'search') {
                continue;
            }
            // normalize menu
            $options[$menuType] = (array)$options[$menuType];
            // normalize and check rights for menu items and subitems
            foreach ($options[$menuType] as $k1 => &$menuItem) {
                // if divider then normalize it and skip to next item
                if (is_int($k1) && !is_array($menuItem)) {
                    $menuItem = '---';
                    continue;
                }
                // normalize menu item
                $menuItem = array_merge($menuDefaults, $menuItem);
                // force tabs to be single for side navigation
                if ($options['navigationType'] === 'side') {
                    $menuItem['tabSingle'] = true;
                }
                // accept / refuse action 
                if (!self::acceptAction($menuItem)) {
                    unset($options[$menuType][$k1]);
                    continue;
                }
                $submenuSeparatorsCount = 0;
                foreach($menuItem['submenu'] as $k2 => &$submenuItem) {
                    // if divider then normalize it and skip to next item
                    if (is_int($k2) && !is_array($submenuItem)) {
                        $submenuSeparatorsCount++;
                        $submenuItem = '---';
                        continue;
                    }
                    // normalize submenu item
                    $submenuItem = array_merge($submenuDefaults, $submenuItem);
                    // force tabs to be single for side navigation
                    if ($options['navigationType'] === 'side') {
                        $submenuItem['tabSingle'] = true;
                    }
                    // accept / refuse action 
                    if (!self::acceptAction($submenuItem)) {
                        unset($menuItem['submenu'][$k2]);
                        continue;
                    }
                }
                unset($submenuItem); // uset reference
                // remove divider item if it is placed as first or last item in submenu
                while (reset($menuItem['submenu']) === '---') {
                    array_shift($menuItem['submenu']);
                }
                while (end($menuItem['submenu']) === '---') {
                    array_pop($menuItem['submenu']);
                }
                // if the submenu rests empty and menu has no url defined then remove it
                if (
                    empty($menuItem['url'])
                    && (
                        empty($menuItem['submenu'])
                        || count($menuItem['submenu']) == $submenuSeparatorsCount
                    )
                ) {
                    unset($options[$menuType][$k1]);
                }
            }
            unset($menuItem); // uset reference
        }
        
        // check rights for homeTab and generate tabs js options
        if (
            !empty($options['homeTab'])
            // ignore tab with undefined url
            && !empty($options['homeTab']['url'])
            // accept / refuse action 
            && self::acceptAction($options['homeTab'])
        ) {
            // normalize tab
            $options['homeTab'] = array_merge($tabDefaults, $options['homeTab']);
            // force tab to be single for side navigation
            if ($options['navigationType'] === 'side') {
                $options['homeTab']['single'] = true;
            }
            // normalize url
            $options['homeTab']['url'] = App::getUrl($options['homeTab']['url']);
            if ($options['homeTab']['single']) {
                $options['homeTab']['singleTabId'] = explode('?', $options['homeTab']['url']);
                $options['homeTab']['singleTabId'] = reset($options['homeTab']['singleTabId']);
            }
        }
        else {
            $options['homeTab'] = null;
        }
        
        // check rights for tabs items and generate tabs js options
        foreach ($options['tabs'] as $k => &$tab) {
            // normalize tab
            $tab = array_merge($tabDefaults, $tab);
            // remove tabs with undefined url or with url the user has no rights for
            if (
                empty($tab['url'])
                || !self::acceptAction($tab)
            ) {
                unset($options['tabs'][$k]);
                continue;
            }
            // force tabs to be single for side navigation
            if ($options['navigationType'] === 'side') {
                $tab['single'] = true;
            }
            // normalize url
            $tab['url'] = App::getUrl($tab['url']);
            if ($tab['single']) {
                $tab['singleTabId'] = explode('?', $tab['url']);
                $tab['singleTabId'] = reset($tab['singleTabId']);
            }
        }
        // reindex tabs from 0 to ensure that json_encode will generate array of 
        // objects and not an object of objects
        $options['tabs'] = array_values($options['tabs']);
        
        return App::loadView($options['view']['module'], $options['view']['name'], $options);
    }
    
    /**
     * Returns HTML code of run.sk back link for actual URL_PATH
     * 
     * To implement this functionality on existing older sites projects:
     *  - copy screen _bl 
     *  - set public rights on _bl screen
     *  - copy method Html::backLink() 
     *  - add call of Html::backLink() to page footer
     * 
     * @return string HTML of run.sk back link. If back link retrieval fails
     *      or if back link is turned off (@todo) then empty string is returned.
     */
    public static function backLink() {        
        $_bl = 1; 
        ob_start();
        try {
            include ROOT. DS . 'app' . DS . 'screens' .DS .'_bl.php';
        }
        catch (Throwable $e) {
            App::catchThrowable($e);
        }
        // catch the script output if return is required
        $output = ob_get_contents();
        ob_end_clean();
        return $output;
    }
    
    /**
     * Comments out provided HTML correctly, it means that before adding
     * comment tag <!-- ... --> all existing comments are escaped like <esc!-- ... --esc>
     * 
     * @param string $html
     * @return string Commented HTML
     */
    public static function comment($html) {
        // escape contained html comments
        // It is not necessary to escape html comment start
        // but we do it to make it comprehensible that this comment is escaped
        $html = str_replace('<!--', '<esc!--', $html); 
        $html = str_replace('-->', '--esc>', $html);
        return '<!--' . PHP_EOL . $html . PHP_EOL . '-->';
    }
}