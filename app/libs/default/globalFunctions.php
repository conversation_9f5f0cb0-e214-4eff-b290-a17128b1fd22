<?php 

/**
 * Escape all html special chars in provided $value to avoid cross-site-scripting (XSS) attacks 
 * 
 * @param mixed $value
 * 
 * @return string Value with escaped html special chars
 */
function e($value) {
    return  htmlspecialchars($value, ENT_QUOTES, 'UTF-8', false);
}

/**
 * Escaped echo
 * 
 * This is wrapper for echo() with that difference that all html special chars are
 * escaped in output to avoid cross-site-scripting (XSS) attacks 
 * 
 * @param mixed $value
 */
function eecho($value) {
    echo  htmlspecialchars($value, ENT_QUOTES, 'UTF-8', false);
}

/**
 * Echoes human-readable information about a $variable.
 * Mostly used for debugging.
 * 
 * @param mixed& $variable
 * @param array $options Following are available:
 *      - 'return' (bool) If TRUE then function will return the information 
 *          rather than echo it. Defaults to FALSE.
 *      - 'json' (bool) If TRUE then the information will be echoed/returned in 
 *          JSON format. Defaults to FALSE.
 * 
 * @return string Information about a $variable if $return is TRUE.
 */
function echoReadable(&$variable, $options = array()) {
    $options = array_merge(array(
        'return' => false,
        'json' => false,
    ), $options);
    if ($options['json']) {
        $output = 
            '<pre>' . 
                json_encode(
                    $variable,
                    JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE
                ) . 
            '</pre>';
    }
    else {
        $output = 
            '<pre>' . 
                print_r($variable, true) . 
            '</pre>';
    }
    if ($options['return']) {
        return $output;
    }
    echo $output;
}

/**
 * Increments provided $variable by 1
 * 
 * @param int& $variable Passed by reference
 */
function i(&$variable) {
    if (empty($variable)) {
        $variable = 0;
    }
    $variable++;
}

/**
 * Translation function using .po file of specified module.
 * Use this function to translate any FRONTEND TEXTS except of errors/exceptions
 * and validation messages which should be translated by __e() and __v().
 * 
 * NOTE: The method works ~5 times slower when used with format elements and ~10 times slower
 * when used with inserts, e.g. testing it by 1000 repetitions loop following times
 * were acquired: ~5ms plain text, ~25ms with format elements and ~50ms with inserts.
 * If ever you need high performance in 2nd and 3rd case use following patterns:
 *      - sprintf(__(__FILE__, 'My string with %s format paterns'), 'some')
 *      - str_replace(array(:insert:), array('some'), __(__FILE__, 'My string with :insert: inserts'))
 * 
 * NOTE: Use msgcat to merge more .po files into one and avoid duplicities. In list of 
 * msgcat input files introduce at first newer file(s): 
 * msgcat --use-first inputfile01.po inputfile02.po -o outputfile.po
 * NOTE: Use msguniq to remove duplicit messages definitions in .po file:
 * msguniq --use-first myfile.po -o myfile.po
 * For more utilities see http://www.gnu.org/software/gettext/manual/gettext.html#Manipulating
 *
 * @param string|ModuleObject $module Name of module to use .po file from. Can be specified by
 *          - explicit module name, e.g. 'App'
 *          - absolute filepath of calling script. Use always __FILE__ constant in this case
 *          - (deprecated) ModuleObject instance. Inside of ModuleObject instance just pass $this here.
 *              This option is deprecated as it does not work together with inheritance 
 *              among modules
 * @param string $singular String to be translated. Format elements (%d, %s, ...)
 *      and inserts (array('age' => 25)) can be used.
 * @param array $inserts Optional. Array of pairs '{insert}' => '{value}'. See Str::fill() for details.
 * @param string|array $args Optional. Arguments for format elements.
 * 
 * An alternative signature can be used in case that $string does not contain inserts:
 * 
 * @param string|ModuleObject $module Name of module to use .po file from. Can be specified by
 *          - explicit module name, e.g. 'App'
 *          - absolute filepath of calling script. Use always __FILE__ constant in this case
 *          - (deprecated) ModuleObject instance. Inside of ModuleObject instance just pass $this here.
 *              This option is deprecated as it does not work together with inheritance 
 *              among modules
 * @param string $singular String to be translated. Format elements (%d, %s, ...)
 *      can be used.
 * @param string|array $args Optional. Arguments for format elements.
 * 
 * @return string Translation.
 */
function __($module, $singular, $args = null) {
    $translated = App::translate($module, '', $singular);
    if (count($funcArgs = func_get_args()) < 3) {
        return $translated;
    } 
    $args = array_slice($funcArgs, 1);
    $args[0] = $translated;
    return call_user_func_array(array('Str', 'fill'), $args);
}

/**
 * Translation function for Admin texts group of specified module.
 * Use this function to translate any BACKEND TEXTS except of errors/exceptions
 * and validation messages which should be translated by __e() and __v().
 * For more details see __()
 */
function __a($module, $singular, $args = null) {
    $translated = App::translate($module, 'Admin', $singular);
    if (count($funcArgs = func_get_args()) < 3) {
        return $translated;
    } 
    $args = array_slice($funcArgs, 1);
    $args[0] = $translated;
    return call_user_func_array(array('Str', 'fill'), $args);
}

/**
 * Translation function for Errors texts group of specified module.
 * Use this function to translate any internal ERROR/EXCEPTION MESSAGES. 
 * For more details see __()
 */
function __e($module, $singular, $args = null) {
    $translated = App::translate($module, 'Errors', $singular);
    if (count($funcArgs = func_get_args()) < 3) {
        return $translated;
    } 
    $args = array_slice($funcArgs, 1);
    $args[0] = $translated;
    return call_user_func_array(array('Str', 'fill'), $args);
}

/**
 * Translation function for Validations texts group of specified module.
 * Use this function to translate any VALIDATION MESSAGES.
 * For more details see __()
 */
function __v($module, $singular, $args = null) {
    $translated = App::translate($module, 'Validations', $singular);
    if (count($funcArgs = func_get_args()) < 3) {
        return $translated;
    } 
    $args = array_slice($funcArgs, 1);
    $args[0] = $translated;
    return call_user_func_array(array('Str', 'fill'), $args);
}

/**
 * Translation function for Development texts group of specified module.
 * Use this function to translate any DEVELOPMENT TEXTS (even errors/exceptions 
 * and validation messages), e.g. in Tools and Tool classes or in install script.
 * For more details see __()
 */
function __d($module, $singular, $args = null) {
    $translated = App::translate($module, 'Development', $singular);
    if (count($funcArgs = func_get_args()) < 3) {
        return $translated;
    } 
    $args = array_slice($funcArgs, 1);
    $args[0] = $translated;
    return call_user_func_array(array('Str', 'fill'), $args);
}

if(!function_exists('lcfirst')) {
    /**
     * Make a string's first character lowercase
     *
     * @param string $str
     * @return string the resulting string.
     */
    function lcfirst( $str ) {
        $str[0] = strtolower($str[0]);
        return (string)$str;
    }
}