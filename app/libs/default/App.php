<?php
class App {
    
    /**
     * CONFIG PROPERTIES
     * 
     * This values are overwritten by values defined in app/config/config.php
     */
    
    /**
     * Debug ON/OFF. 
     * 
     * If TRUE then all App::debug() occurences are executed regardles to output type.
     * To preserve working only some kind of output use array of allowed outputs,
     * e.g. array ('log', 'fb').
     * 
     * FirePHP class is loaded if App::$debug is not entirely turned off and if
     * initial debugOutput is set to 'fb'.
     * 
     * This is checked by App::debug() basic function.
     * 
     * Use App::setDebug() / App::getPropertyDebug() to set & get this property 
     * 
     * @var bool|array
     */
    static protected $debug = false;
    
    /**
     * Debug output
     * 
     * This property is checked by App::debug() function.
     * 
     * Switches the output to:
     *      - 'echo': browser screen
     *      - 'hidden': browser screen with css display:none. Use showDebug() from JS console.
     *      - 'console': JS console.
     *      - 'fb': FireBug console. FirePHP class is loaded even if debugEnabled is FALSE.
     *      - 'log': debug.log file
     * 
     * @var string
     */
    static protected $debugOutput = 'fb';
    
    /**
     * Layouts in which the App::debug() should avoid to use html output type ('console', 'echo', 'hidden').
     * Instead of it the 'log' output is used. Layout are specified in array like:
     * 
     *      array(
     *          array('module' => 'MyModule', 'name' => 'myLayout1'),
     *          array('name' => 'myLayout2'),                           // layout with provided name in any module
     *          'myLayout3',                                            // layout with provided name in any module
     *          false,                                                  // Use FALSE if html debug should be avoided also in case of no layout used
     *      )
     *
     * @var array 
     */
    static protected $avoidHtmlDebugLayouts = array(
        false,
        array('module' => 'App', 'name' => 'plaintext'),
    );
    
    /**
     * Options to be used for method App::sendEmail() during debuging/developing
     * the project. All options of App::sendEmail() can be used plus following additional:
     *      - 'debug' (bool) If TRUE then corresponding options will be rewrited
     *      by values defined here in all occurences of App::sendEmail(). If FALSE
     *      then App::sendEmail() work in production mode.
     *      - 'to' (string|array) Specification of $to argument of App::sendEmail()
     *      - 'smtp' (array) Specification of smtp options to be used if 'engine'
     *      is set to 'smtp' value. These options are used instead of standard 
     *      App smtp settings ('smtp.host', ..., 'smtp.encryption'). The array is like:
     * 
     *          array(
     *              'host' => '',
     *              'port' => '',
     *              'username' => '',
     *              'password' => '',
     *              'encryption' => '', // NULL, 'tls', 'ssl'
     *          )
     * 
     * @var array
     */
    static protected $debugEmailOptions = array(
        'debug' => false,
    );
    
    /**
     * Email address or array of addresses where the log emails are sent by App::log(). 
     * Messages are send only under condition that 'email' option of App::log() is TRUE.
     * If FALSE then the sending of log emails is turned off.
     *
     * @var string|array|bool 
     */
    static protected $logEmail = false;
    
    /**
     * Options passed to FB::setOptions() method of FirePHP.
     * This is done in App::debug() when TRUE is on input.
     * 
     * If this is not an array then FB::setOptions() method is not launched, which
     * results in default FirePHP options.
     * 
     * @var array
     */
    static protected $fbOptions = array(
        'maxObjectDepth' => 10,
        'maxArrayDepth' => 10,
        'maxDepth' => 10,
        'useNativeJsonEncode' => true,
        'includeLineNumbers' => true
    );
    
    /**
     * Sql logging on/off.
     * Can be TRUE/FALSE or DB::LOG_WITH_BACKTRACE.
     * ATTENTION: Do not use DB::LOG_WITH_BACKTRACE for production!
     *
     * @var bool|int 
     */
    static protected $sqlLogging = true;
    
    /**
     * Error reporting ON/OFF
     * TRUE/FALSE can be used to simply turn reporting on/off.
     * PHP error constans can be used for precise specification of errors
     * which should be reported:
     * 
     * E_ERROR | E_WARNING | E_PARSE // Report simple running errors
     * E_ERROR | E_WARNING | E_PARSE | E_NOTICE // Reporting E_NOTICE can be good too (to report uninitialized variables or catch variable name misspellings ...)
     * E_ALL ^ E_NOTICE // Report all errors except E_NOTICE
     * E_ALL & ~E_NOTICE // The same as above
     * 
     * @var bool|int
     */
    static protected $errorReporting = false;
    
    /**
     * Sets which PHP errors are catched by App::catchError()
     * TRUE/FALSE can be used to simply turn catching on/off.
     * PHP error constans can be used for precise specification of errors
     * which should be caught:
     * 
     * E_ERROR | E_WARNING | E_PARSE // Report simple running errors
     * E_ERROR | E_WARNING | E_PARSE | E_NOTICE // Reporting E_NOTICE can be good too (to report uninitialized variables or catch variable name misspellings ...)
     * E_ALL ^ E_NOTICE // Report all errors except E_NOTICE
     * E_ALL & ~E_NOTICE // The same as above
     * 
     * @var bool|int
     */
    static protected $errorCatching = false;
    
    /**
     * Initialized at beginning of App::init() to ob_get_level.
     * Used in App::startPhpErrorsHtmlCapture().
     * 
     * @var int 
     */
    static protected $outputBufferInitialLevel = null;
    
    /**
     * Used by App::startPhpErrorsHtmlCapture(), App::endPhpErrorsHtmlCapture() to control/validate 
     * sequence of mentioned methods calls.
     * 
     * @var NULL|'started'|'ended'
     */
    static protected $phpErrorsHtmlCaptureStatus = null;
    
    /**
     * Allows to turn off all js 'embroidery', e.g. facebook, google and twitter
     * social plugins, google analytics, ... and so make debuging of local js
     * much more easier (=quicker). 
     * 
     * It is up to developer to use this property in any new js 'embroidery' (mostly 
     * implemented as App module elements) and to keep posibility to switch off 
     * all of them at once ... and breath a fresh air :)
     * 
     * @var bool 
     */
    static public $useJsEmbroidery = true;
    
    /**
     * If TRUE then origin comments in templates are allowed.
     * 
     * @var bool
     */
    static protected $allowOriginComments = false;
    
    /**
     * Name of database connection config (in app/config/database.php) which 
     * will be used for database connection
     * 
     * @var type 
     */
    static protected $dbConfigName = 'default';
    
    /**
     * Name of DB table where users are defined. 
     * 
     * This table must contain at least following columns:
     * 
     * - 'id' int(11) - primary key
     * - 'username' varchar(255)
     * - 'password' varchar(255)
     * - 'active' bool(1)
     * - '{groupsTable}_id' int(11) - foreign key to user group
     * 
     * @var string
     */
    static protected $usersTable = 'run_users';
    
    /**
     * Name of DB table where groups are defined. 
     * 
     * This table must contain at least following columns:
     * 
     * - 'id' int(11) - primary key
     * - 'pid' varchar(20) - internal name of group refered in program code
     * - 'name' varchar(100)
     * - 'hierarchy' int(11) - Group with lower value is superior to group with higher value. Some groups can have the same values if they are on the same level
     * 
     * @var string
     */
    static protected $groupsTable = 'run_groups';
    
    /**
     * Name of DB table where settings are defined. 
     * 
     * This table must contain at least following columns:
     * 
     * - 'id' int(11) - primary key
     * - 'module' varchar(255) - module name the setting belongs to
     * - 'pid' varchar(255) - internal name of setting refered in program code
     * - 'name' varchar(255) - label used to describe settings form field
     * - 'description' text - detailed setting description of settings form field
     * - 'value' text - setting value
     * - 'js_visible' bool(1) - If TRUE the the setting value is sent also to js scripts (using js config object)
     * 
     * @var string
     */
    static protected $settingsTable = 'run_settings';
    
    /**
     * Name of DB table where contents are defined. 
     * 
     * This table must contain at least following columns:
     * @todo
     * 
     * @var strings 
     */
    static protected $contentsTable = 'run_web_contents';
    
    /**
     * Name of DB schema where contents are defined. 
     * 
     * @var array 
     */
    static protected $contentsSchema = array(
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'pid' => array('type' => 'varchar', 'default' => null),
        'parent_id' => array('type' => 'int', 'index' => 'index', 'default' => null),
        'path' => array('type' => 'varchar', 'index' => 'index'),
        'sort' => array('type' => 'int', 'index' => 'index'),
        'lang' => array('type' => 'varchar', 'length' => 5),
        'locator' => array('type' => 'varchar', 'default' => null, 'comment' => 'Content slug, internal reference or an absolute URL'),
        'name' => array('type' => 'varchar'),
        'header_text' =>  array('type' => 'text', 'default' => null, 'comment' => 'Text displayed in page header'),
        'resume' => array('type' => 'text', 'default' => null, 'comment' => 'Resume of page text used in contents reviews'),
        'slider_id' => array('type' => 'int', 'index' => 'index', 'default' => null, 'comment' => 'Id of slider under sliders section in contents'),
        'text' => array('type' => 'longtext', 'default' => null),
        'has_side_content' => array('type' => 'bool', 'default' => 0, 'comment' => 'Does this content has side content block? This cann be used to use one layout for 2 common alternatives.'),
        'side_text' => array('type' => 'longtext', 'default' => null, 'comment' => 'Side content text if has_side_content is 1'),
        'seo_title' => array('type' => 'varchar', 'default' => null),
        'seo_description' => array('type' => 'varchar', 'default' => null),
        'seo_keywords' => array('type' => 'varchar', 'default' => null),
        'seo_index' => array('type' => 'bool', 'default' => 1, 'comment' => 'Robots meta tag index/noindex part'),
        'seo_follow' => array('type' => 'bool', 'default' => 1, 'comment' => 'Robots meta tag follow/nofollow part'),
        'image_name' => array('type' => 'varchar', 'default' => null),
        'menu_icon' => array('type' => 'varchar', 'default' => null),
        'background_image' => array('type' => 'varchar', 'default' => null),
        'active' => array('type' => 'bool'),
        'permanent' => array('type' => 'bool', 'default' => 0, 'comment' => 'If 1 then item is not deletable. This allows to protect important items in tree from removal'),
        'hidden' => array('type' => 'bool', 'default' => 0, 'comment' => 'If 1 then item is not visible in the tree (except for admin). This allows to hide unused items in tree'),
        'obfuscate' => array('type' => 'bool', 'default' => 0, 'null' => true, 'comment' => 'Should the content text obfuscation be done? If autodetect_obfuscate is 1 then after each save is the value is reset to NULL. It is up to user then to do the detection before displaying the content'),
        'autodetect_obfuscate' => array('type' => 'bool', 'default' => 1, 'comment' => 'If 1 then need of content text obfuscation is autodetected on content update'),
        'date' => array('type' => 'date', 'default' => null),
        'layout' => array('type' => 'varchar', 'length' => 30, 'default' => null),
        'attach_comments' => array('type' => 'bool', 'default' => 0, 'comment' => 'If 1 then FB comments will be attached at the end of content'),
        'aux_01' => array('type' => 'text', 'default' => null, 'comment' => 'Auxilairy content data. Can be used on different parts of content tree for different purposes'),
        'aux_02' => array('type' => 'text', 'default' => null, 'comment' => 'Auxilairy content data. Can be used on different parts of content tree for different purposes'),
        'aux_03' => array('type' => 'text', 'default' => null, 'comment' => 'Auxilairy content data. Can be used on different parts of content tree for different purposes'),
        'created' => array('type' => 'datetime', 'default' => null),
        'modified' => array('type' => 'datetime', 'default' => null),
        'deleted' => array('type' => 'datetime', 'default' => null, 'index' => 'index'),
    );
    
    /**
     * Name of DB table where languages are defined. 
     * 
     * This table must contain at least following
     * columns:
     * - 'name' varchar(50) - language name
     * - 'lang' varchar(2) - ISO 639-1 code for language
     * - 'code' varchar(3) - ISO 639-2 code for language
     * - 'locale' varchar(5) - IETF language tag
     * - 'currenca_code' varchar(3) - Currency ISO 4217 code, e.g. EUR, CZK
     * - 'icon' varchar(255)
     * - 'sort' int(10)
     * - 'default' bool(1)
     * - 'localized_default' bool(1)
     * - 'active' bool(1)
     * - 'published' bool(1)
     * 
     * Alternatively the languages table can be defined directly here as array
     * like:
     * 
     *      array(
     *          array(
     *              'name' => 'Slovak'
     *              'lang' => 'sk',
     *              'code' => 'slo',
     *              'locale' => 'sk_SK',
     *              'currency_code' => 'EUR',
     *              'published' => true,
     *              'localized_default' => 0,
     *          ),
     *          array(
     *              'name' => 'English'
     *              'lang' => 'en',
     *              'code' => 'eng',
     *              'locale' => 'en_GB',
     *              'currency_code' => 'EUR',
     *              'published' => false,
     *              'localized_default' => 0,
     *          ),
     *          ...
     *      )
     * 
     * Default language must be placed as the first, and only active languages are
     * listed. That is why in this case 'default' and 'active' fields are not included. 
     * If 'published' is TRUE then the language version is visible on frontend.
     * If FALSE then the language version is visible only in admin for editing
     * 
     * @var string|array
     */
    static protected $languagesTable = 'run_languages';
    
    /**
     * If TRUE then lang code is localized and it is composed from country code
     * and language code separated by slash, e.g. 'us/en', 'us/es'. If FALSE then
     * the lang code is composed just from language code, e.g. 'en', 'es'.
     * 
     * NOTE: 
     * Country codes: http://www.iso.org/iso/country_names_and_code_elements
     * Language codes: http://www.loc.gov/standards/iso639-2/php/code_list.php
     *
     * @var bool 
     */
    static protected $localizedLang = false;
    
    /**
     * If TRUE then in case of localized lang (App::$localizedLang is TRUE) the language part
     * can be autodetected from information sent by browser. It means that the localized 
     * lang can be incomplete, lang part can be omitted ('us/my-page' instead of 'us/en/my-page').
     * In such a case the lang part is autodetected.
     *
     * @var bool 
     */
    static protected $autodetectLang = false;
            
    /**
     * If TRUE then default lang does not appear in urls
     * (App::urlLang and URL_LANG is set to NULL)
     * 
     * @var bool
     */
    static protected $defaultLangHidden = true;
    
    /**
     * If TRUE then urls are created with trailing slash at the end of url path. 
     * If FALSE then urls are created without trailing slash at the end of url path.
     * If NULL then trailing slash is let as it is.
     * 
     * ATTENTION: If you change this then update .htaccess accordingly
     */
    static protected $urlPathTrailingSlash = false;
    
    /**
     * Single URL option or array of URL options to be inherited by default 
     * from app actual url into each generated url, e.g. array('get' => array('lang')) 
     * to inherit GET param 'lang' from actual URL to new created. You can write this also
     * as array('get/lang'). If you use the path syntax then the specified
     * path can be as long as you need: 'get/filter/User.name'. Array syntax
     * allows only 2 levels (see the 1st example) but is faster. Defaults to empty array().
     * 
     * @var string|array
     */
    static protected $urlInherit = array();
    
    /**
     * Options for App::hash()
     * @var array
     */
    static protected $hashOptions = array(
        /**
         * Secret key used for generating the HMAC variant of the message digest.
         * 
         * @var string
         */
        'key' => '',
        
        /**
         * String appended to the string before hashing.
         *  
         * @var string
         */
        'suffix' => '',
    );
    
    /**
     * Options for App::formatNumber()
     * Default number format properties are defined for each of used langs
     * in associative arrays like this:
     * 
     *      array(
     *          '{lang01}' => '{propertyValue01}',
     *          '{lang02}' => '{propertyValue02}',
     *          ...
     *      )
     */
    
    /**
     * Default number of decimal points
     * @var array of integers 
     */
    static protected $decimals = array(
        'sk' => 2,
        'en' => 2,
        'de' => 2,
        'fr' => 2,
    );
    
    /**
     * Default separator for the decimal point
     * @var array of strings 
     */
    static protected $decimalPoint = array(
        'sk' => ',',
        'en' => '.',
        'de' => ',',
        'fr' => ',',
    ); 
    
    /**
     * Default thousands separator
     * @var array of strings 
     */

    static protected $thousandsSeparator = array(
        'sk' => ' ',
        'en' => ',',
        'de' => ' ',
        'fr' => ' ',
    );
    
    /**
     * Url root of the project, e.g. '/my_project'.
     * 
     * URL_ROOT constant is defined according to this.
     * 
     * It does not contain trailing slash or it is just empty.
     * 
     * It is not possible to retrieve url root so easily as ROOT taking into account
     * different apache configurations on different hostings.
     * That's why it is up to developer to set this properly here.
     *
     * @var string 
     */
    static public $urlRoot = '';
    
    /**
     * Home slug 
     * 
     * HOME_SLUG constant is defined according to this.
     *
     * @var string
     */
    static public $homeSlug = 'home';
    
    /**
     * List of modules to be loaded in admin
     * 
     * @var array
     */
    static public $adminModules = array();   
            
    /**
     * Default layout name.
     * If string then the layout is searched under App module. To provide layouts 
     * from other modules use array like array('module' => ..., 'name' => ...)
     * 
     * @var string|array
     */
    static public $defaultLayout = 'default';
    
    /**
     * Home layout name.
     * If string then the layout is searched under App module. To provide layouts 
     * from other modules use array like array('module' => ..., 'name' => ...)
     * 
     * @var string|array
     */
    static public $homeLayout = 'home';

    /**
     * Admin login slug
     * 
     * Slug on which is accessible login to administration
     * 
     * @var string
     */
    static public $adminLoginSlug = 'runlogin';
    
    /**
     * Admin slug
     * 
     * Slug on which is accessible administration
     * 
     * @var string
     */
    static public $adminSlug = 'run';

    /**
     * Does the actual application use also frontend or is it pure backend 
     * (intranet) application?
     * 
     * @var bool
     */
    static public  $adminOnly = false;
        

    /**
     * Login page pid
     * 
     * Page on which is accessible frontend login for registered users
     * 
     * @var string
     */
    static public $userLoginPid = 'App.Users.login';
    
    /**
     * Frontend login default target content pid
     * 
     * Login target URL is stored on implicit login to session.
     * 
     * In case that nothing is there (frontend login page is called explicitly by user)
     * then slug of this content is used to for target URL. 
     * 
     * This value or the value of login target URL (using App::setLoginTargetUrl())
     * can be tweaked e.g. in app/configs/init.php.
     * 
     * The default login target from admin login page is always admin slug!
     * 
     * Use App::getLoginTargetUrl() to get the final value of login target URL
     * 
     * @var string 
     */
    static public $userLoginTargetPid = '';
    
    /**
     * Permanent session lifetime
     * 
     * Lifetime of pernanent sessions used for permanent logins. In seconds.
     * e.g. 14 days = 1209600
     * 
     * @var int
     */
    static public $permanentSessionLifetime = 1209600;
    
    /**
     * Default mail engine used by method App::sendEmail().
     * 
     * Possible valuse are 'mail', 'smtp'.
     *
     * @var string 
     */
    static protected $mailEngine = 'smtp';
    
    /**
     * Image processing engine (used by File class).
     * 
     * Possible values are 'gd', 'imagick', 'imlib'
     * 
     * @var string 
     */
    static protected $imageEngine = 'imagick';
    
    /**
     * Is the https protocol available?
     * 
     * NOTE: This is set by logical OR of config value 'httpsIsAvailable' and App::$onHttps
     * 
     * @var bool 
     */
    static protected $httpsIsAvailable = false;
    
    /**
     * Is switching between http and https protocol allowed?
     *  
     * For case http-to-https switch this applies only if config 'httpsIsAvailable' is TRUE.
     * For case https-to-http switch this applies always.
     * Set this FALSE if you would like to keep your site only on http or on https.
     * 
     * @var bool 
     */
    static protected $allowSwitchBetweenHttpAndHttps = false;
    
    /**
     * Definition of css files which should be placed as first (if attached)
     * and in here defined order. Its form is:
     * 
     *      array(
     *          '/app/css/my.css',
     *          '/app/css/libs/my-lib.css',
     *          ...
     *      )
     * 
     * Files which are not mentioned in App::$firstCssFiles nor in App::$lastCssFiles 
     * are placed between these two groups of files (in order as they occure in App::$cssFiles)
     * 
     * NOTE: Also modules css files must be defined here
     */
    static protected $firstCssFiles = array();

    /**
     * Definition of css files which should be placed as last (if attached)
     * and in here defined order. Its form is:
     * 
     *      array(
     *          '/app/css/my.css',
     *          '/app/css/libs/my-lib.css',
     *          ...
     *      )
     * 
     * Files which are not mentioned in App::$firstCssFiles nor in App::$lastCssFiles 
     * are placed between these two groups of files (in order as they occure in App::$cssFiles)
     * 
     * NOTE: Also modules css files must be defined here
     */
    static protected $lastCssFiles = array();   
    
    /**
     * List of substitute files for attached css files like
     * 
     *      array(
     *          '/atached/file/path.css' => '/substitute/file/path.css',
     *          '/app/css/my.css' => '/app/css/my.min.css',
     *          ...
     *      )
     * 
     * Is suposed for fast replacements of used files by thier new / other version (e.g CDN) 
     * 
     * @var type 
     */
    static protected $cssFilesSubstitutions = array();
    
    /**
     * Should be css files compiled to assets?
     * 
     * @var bool 
     */
    static protected $compileCss = false;
    
    /**
     * Definition of js files which should be placed in html head (if attached)
     * and in here defined order. Its form is:
     * 
     *      array(
     *          '/app/js/my.js',
     *          '/app/js/libs/my-lib.js',
     *          ...
     *      )
     * 
     * Use App::getHeadJsLinks() to create html for these group of js files.
     * Links for files which are not mentioned here are created by App::getJsLinks().
     * 
     * NOTE: Also modules js files must be defined here
     */
    static protected $headJsFiles = array();

    /**
     * Definition of js files which should be placed as first (if attached)
     * and in here defined order. Its form is:
     * 
     *      array(
     *          '/app/js/my.js',
     *          '/app/js/libs/my-lib.js',
     *          ...
     *      )
     * 
     * Files which are not mentioned in App::$headJsFiles, App::$firstJsFiles nor 
     * in App::$lastJsFiles are placed between App::$firstJsFiles and App::$lastJsFiles 
     * in order as they occure in App::$jsFiles.
     * 
     * NOTE: Also modules js files must be defined here
     */
    static protected $firstJsFiles = array();

    /**
     * Definition of js files which should be placed as last (if attached)
     * and in here defined order. Its form is:
     * 
     *      array(
     *          '/app/js/my.js',
     *          '/app/js/libs/my-lib.js',
     *          ...
     *      )
     * 
     * Files which are not mentioned in App::$headJsFiles, App::$firstJsFiles nor 
     * in App::$lastJsFiles are placed between App::$firstJsFiles and App::$lastJsFiles 
     * in order as they occure in App::$jsFiles.
     * 
     * NOTE: Also modules js files must be defined here
     */
    static protected $lastJsFiles = array();
    
    /**
     * List of substitute files for attached js files like
     * 
     *      array(
     *          '/atached/file/path.js' => '/substitute/file/path.js',
     *          '/app/js/my.js' => '/app/js/my.min.js',
     *          ...
     *      )
     * 
     * Is suposed for fast replacements of used files by thier new / other version (e.g CDN) 
     * 
     * @var array 
     */
    static protected $jsFilesSubstitutions = array();
    
    /**
     * What is the used javacript module implementation?
     * Possible values are:
     *      - 'system' for SystemJs (see https://github.com/systemjs/systemjs)
     *      - 'native' for native modules implementation in browsers (ES6 / ES2015, 
     *          see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Modules)
     * This is set to 'system' as SystemJS allows to define importmap - see phpDoc 
     * for 'jsImportmap' config.
     * 
     * ATTENTION: Keep this in synchro with options in tsconfig.json! 
     * For 'system' set following compiler options in tsconfig.json:
     *      "compilerOptions": {
     *          "target": "ES5",
     *          "module": "system",
     *      }
     * For 'native' set following compiler options in tsconfig.json:
     *      "compilerOptions": {
     *          "target": "ES2015",
     *          "module": "ES2015",
     *      }
     * 
     * @var string 
     */
    static protected $jsModuleImplementation = 'system';
    
    /**
     * Importmap used when 'jsModuleImplementation' is set to 'system' (SystemJS).
     * 
     * This allows to write in .ts files following module imports, e.g. for lodash:
     * 
     *      import _ from 'lodash'
     * 
     * This import is ok for typescript (under condition that you have installed
     * types for lodash like `npm install @types/lodash --save-dev`). But we need
     * to know the real path to get the lodash from. The SystemJS allows to do this
     * substitutions by defining importmap. ATTENTION: The real source must be 
     * a plain (non-modular) javascript! That is why we can import the whole
     * library ("default import" in words of ES6 modules), but it is not possible
     * to import only some members/parts of the library.
     * 
     * The another possibility (instead of doing imports for used libraries)
     * is to load libs globaly by App::setJsFiles() - just in the same way
     * as jQuery is loaded and use them in js modules without imports.
     * 
     * ATTENTION: All fajnwork modules must define their mappings in app module config!
     * 
     * @var array
     */
    static protected $jsModuleImportmap = array();
    
    /**
     * Should be js files compiled to assets?
     * 
     * @var bool 
     */
    static protected $compileJs = false;
        
    /**
     * Name of table used to store css and js assets
     * 
     * ATTENTION: MyISAM engine must be used for case that assets are created inside 
     * of a transaction to ensure that the table recordings are saved immediatelly 
     * and not only after transaction commit
     * 
     * @var string 
     */
    static protected $assetsTable = '_run_assets';
    
    /**
     * Schema of App::$assetsTable
     * 
     * ATTENTION: There must be set unique index created from 'files' and 'files_modified' columns
     *
     * @var array 
     */
    static protected $assetsTableSchema = array(
        'table' => array('type' => 'int', 'index' => 'primary'),
        'files' => array('type' => 'text', 'comment' => 'String created by concatenating app root relative paths of included files with character ;'),
        'files_modified' => array('type' => 'datetime', 'comment' => 'Datetime of the last modification in included files'),
        'custom' => array('type' => 'varchar', 'default' => null, 'comment' => 'Explicit name of asset file generated manually.'),
    );
    
    /**
     * Has been already existence of assets table ensured?
     * 
     * @var bool 
     */
    static protected $assetsTableEnsured = false;
    
    /**
     * The default key used by Str::crypt() and String:decrypt().
     * Use any random ASCII string. Internally it is hashed by md5() 
     * to 32-character hexadecimal number.
     * 
     * $var string
     */
    static public $cryptKey = '';
    
    /**
     * Switch for obfuscation of actual content text. Possible values are: 
     * - 'allow' - content text obfuscation is allowed and done according to value of 'obfuscate' field
     * - TRUE - all content texts are obfuscated regardless to value of 'obfuscate' field
     * - FALSE - content text is obfuscation is denied regardless to value of 'obfuscate' field
     * 
     * @var string|bool
     */
    static protected $obfuscateContentText = 'allow';
    
    /**
     * Name of session cookie.
     * 
     * NOTE: If you seriously refactor/repair session functionality then it is 
     * the best to change session cookie name to ignore all existing session cookies 
     * and so to start again.
     * 
     * @var string 
     */
    static protected $sessionCookieName = 'FAJNPHPSESSID';
    
    /**
     * PUBLIC RUNTIME PROPERTIES (for the moment all of them are actual request related)
     */
    
    /**
     * Actual request url composed from url path + url GET params, e.g.:
     * 
     *      /slug?param1=val1&param2=val2
     * 
     * Is set to value of $_SERVER['REQUEST_URI']
     *
     * @var string 
     */
    static public $url = null;
    
    /**
     * Parsed version of App::$url.
     *
     * @var array 
     */
    static public $parsedUrl = null;
    
    /**
     * Actual request url path (path without GET params), e.g.:
     * 
     *      /slug
     *  
     * @var string
     */
    static public $urlPath;
    
    /**
     * Url base contains the leading part of url address contaning 
     * the protocol, host. E.g. if the request url is: 'http://mysite.sk/my/site/script.php'
     * then App::$urlBase equals to 'http://mysite.sk'. It does not contain trailing slash
     * 
     * ATTENTION: If App::$initialLang is resolved from site domain by App::getLangByDomain()
     * then App::$urlBase very probably must be updated by App::setI18n().
     * In such a case DO NOT change value of this property directly but use App::setI18n().
     * 
     * @var string
     */
    static public $urlBase = null;
    
    /**
     * Url base with root contains the leading part of url address contaning 
     * the protocol, host and URL_ROOT. E.g. if the request url is: 'http://mysite.sk/my_url_root/my/site/script.php'
     * then App::$urlBaseWithRoot equals to 'http://mysite.sk/my_url_root'. 
     * It does not contain trailing slash
     * 
     * ATTENTION: If App::$initialLang is resolved from site domain by App::getLangByDomain()
     * then App::$urlBaseWithRoot very probably must be updated by App::setI18n().
     * In such a case DO NOT change value of this property directly but use App::setI18n().
     * 
     * @var string
     */
    static public $urlBaseWithRoot = null;
    
    /**
     * Actual request http method, e.g. GET, POST, PUT, ...
     * 
     * Always uppercased 
     *  
     * @var string 
     */
    static public $requestMethod = null;    
    
    /**
     * Actual request dispatch type
     * 
     * Possible values are 'mvc' and 'slug'
     * 
     * The 'mvc' dispatching is launched by urls like: 
     *      [/{lang}]/mvc/{module}/{controller}/{action}[/{arg}[/...[/{param}:{value}[/...]]]]]
     * 
     * The 'slug' dispatching is launched by urls like (also empty url is interpreted
     * as slug request and the App::$homeSlug is used for slug):
     *      [/{lang}][/{slug}[/{arg}[/...[/{param}:{value}[/...]]]]]]
     * 
     * The meaning of {lang}, {slug}, {module}, {controller}, {action}, 
     * {arg} and {param} see here below
     * 
     * @var string 
     */
    static public $requestType = null;    
    
    /**
     * Where/how the actual request was launched? Is it launched normally from
     * browser address bar, or is ti launched as ajax request or by SmartFrame?
     * 
     * Possible values are NULL, 'ajax' and 'frame' or any you will add and
     * implement into project. There is no automagic behind. The request itself must 
     * contain _requestSource_ get parameter with the value 'ajax', frame' ,....
     * If there is no such a param then the requst source is set to NULL (undefined).
     * 
     * @var string 
     */
    static public $requestSource = null;
    
    /**
     * Actual app lang
     * 
     * ATTENTION: DO NOT change value of this property directly but use App::setI18n().
     *
     * @var string 
     */
    static public $lang = null;
    
    /**
     * App initial lang retrieved from incoming request
     *
     * @var string 
     */
    static protected $initialLang = null;
        
    /**
     * If TRUE then lang provided in URL does not exists among available active languages
     *
     * @var bool 
     */
    static public $langIsInvalid = false;
    
    /**
     * If TRUE then lang provided in URL is not published yet
     *
     * @var bool 
     */
    static public $langIsNotPublished = false;
    
    /**
     * Actual request lang exactly as in request.
     * This differs from $lang in the case of default language when $defaultlangHidden
     * is TRUE. In such a case $urlLang is empty string ('').
     * 
     * Use URL_LANG constant to read this property value outside of App class
     *
     * @var string
     */
    static public $urlLang = null;
    
    /**
     * Actual app locale set according to App::$lang
     * 
     * ATTENTION: DO NOT change value of this property directly but use App::setI18n().
     * 
     * @var string
     */
    static public $locale = null;
    
    /**
     * Actual request slug
     *
     * @var string
     */
    static public $slug = null;
    
    /**
     * Actual request module, controller and action 
     * Action is used either in mvc dispatching or as a screen action for slug dispatching.
     * 
     * @var string
     */
    static public $module = null;
    static public $controller = null;
    static public $action = null; 
    
    /**
     * Actual request data
     * 
     * As a data are stored data in $_REQUEST['data'] 
     * There are also more special data submit possibilities (see App::parseRequest())
     * 
     * This is "data to save".
     *
     * @var array 
     */
    static public $data = array();
    
    /**
     * Actual request params.
     * 
     * In App::$params there are placed named params (/myParamA:23/myparamB:45/...) 
     * of actual request. This corresponds with $params['named'] in CakePHP.
     * 
     * @var array 
     */
    static public $params = array();
    
    /**
     * Actual request args.
     * 
     * In App::$params there are placed unnamed params (/23/45/...) 
     * of actual request. This corresponds with $params['passed'] in CakePHP.
     * 
     * @var array 
     */
    static public $args = array();
    
    /**
     * Action type prefix, e.g. if action name starts with 'admin_' prefix, 
     * then 'actionType' is 'admin'. If method has no prefix then NULL.
     *
     * @var string 
     */
    static public $actionType  = null;
    
    
    /**
     * READ & WRITE RUNTIME PROPERTIES
     */
    
    /**
     * Name module to us the App::$layoutName from.
     * 
     * If it is set to an empty value (NULL, FALSE, 0,  '') then no layout
     * is used
     * 
     * Use App::setLayout() / App::getLayout() to set & get this property 
     *
     * @var string 
     */
    static protected $layoutModule = 'App';
    
    /**
     * Name of layout file to be used for html page.
     * The file extension is optional. If omitted then .php file is supposed.
     * 
     * If it is set to an empty value (NULL, FALSE, 0,  '') then no layout
     * is used
     * 
     * Use App::setLayout() / App::getLayout() to set & get this property 
     *
     * @var string 
     */
    static protected $layoutName = 'default';
    
    /**
     * Dynamically created list of variables passed to layout and to doctype
     * 
     * Use App::setPropertyLayoutParams() / App::getLayoutParams to set & get this property 
     * 
     * NOTE: There are following default layout params: 'seoTitle', 'seoKeywords', 
     * 'seoDescription', 'seoIndex', 'seoFollow', 'seoCanonicalUrl' for seo meta tags and 'tabIcon', 
     * 'tabClass', 'tabColor', 'tabBgColor' for smartTabs in admin. 
     * To set these params use methods App::setSeoTitle(), 
     * App::setSeoKeywords(), App::setSeoDesctiption(), App::setTabIcon(), App::setTabClass(), 
     * App::setTabColor(), App::setTabBgColor(). 
     * These values should be inserted in layout to respective meta tags using methods App::getSeoTitle(), 
     * App::getSeoKeywords(), App::getSeoDesctiption(), App::getTabIcon(), App::getTabClass(), 
     * App::getTabColor(), App::getTabBgColor().
     * 
     * @var array 
     */
    static protected $layoutParams = array(
        'seoTitle' => null,
        'seoKeywords' => null,
        'seoDescription' => null,
        'seoIndex' => true,
        'seoFollow' => true,
        'seoCanonicalUrl' => null,
        'tabIcon' => null,
        'tabClass' => null,
        'tabColor' => null,
        'tabBgColor' => null,
    );
    
    /**
     * Name module to us the App::$doctypeName from.
     * 
     * If it is set to an empty value (NULL, FALSE, 0,  '') then no doctype
     * is used
     * 
     * Doctype is used only if layout is used. It means that if the layout is turned 
     * off then also no doctype will be loaded (= it's turned off).
     *  
     * Use App::setDoctype() / App::getDoctype() to set & get this property 
     *
     * @var string 
     */
    static protected $doctypeModule = 'App';
    
    /**
     * Name of doctype file to be used for html page.
     * The file extension is optional. If omitted then .php file is supposed.
     * 
     * If it is set to an empty value (NULL, FALSE, 0,  '') then no doctype
     * is used
     * 
     * Doctype is used only if layout is used. It means that if the layout is turned 
     * off then also no doctype will be loaded (= it's turned off).
     * 
     * Use App::setDoctype() / App::getDoctype() to set & get this property 
     *
     * @var string 
     */
    static protected $doctypeName = 'html5';
            
    /**
     * Dynamically created list of css files to be attached to html header in layout
     * in structure like:
     * 
     *      array(
     *          '/app/css/file01.css' => false,
     *          '/app/modules/MyModule/css/file02.css' => array('media' => 'print'),
     *          ...
     *      )
     * 
     * Relative filepaths are prefixed already by url root. 
     * Use App::setCssFiles() / App::getCssFilesProperty() to set & get this property 
     * Use App::getCssLinks() to generate the html code for attached files
     * 
     * @var array
     */
    static protected $cssFiles = array();
    
    /**
     * Dynamically created array of css scripts to be attached in layout.
     * Use App::startCssCapture() - App::endCssCapture() to extract css code from templates 
     * to this array.
     * 
     * @var array
     */
    static protected $css = array();
    
    /**
     * Dynamically created array of css scripts to be attached in layout after all 
     * other css scripts are attached. Use App::startCssCapture() - App::endCssCapture(TRUE) 
     * to extract css code from templates to this array.
     *
     * @var array 
     */
    static protected $lastCss = array();
    
    /**
     * Dynamically created list of js files to be attached to html header in layout
     * in structure like:
     * 
     *      array(
     *          '/app/js/file01.js' => false,
     *          '/app/modules/MyModule/js/file02.js' => false,
     *          ...
     *      )
     * 
     * Relative filepaths are prefixed already by url root. 
     * Use App::setJsFiles() / App::getJsFiles() to set & get this property 
     * Use App::getJsLinks() to generate the html code for attached files
     * 
     * @var array
     */
    static protected $jsFiles = array();
    
    /**
     * Should be the SystemJS library and its importmap attached?
     * 
     * @var boolean
     */
    static protected $attachSystemJsCode = false;
    
    /**
     * Dynamically created array of js scripts to be attached in layout.
     * Use App::startJsCapture() - App::endJsCapture() to extract js code from templates 
     * to this array.
     *
     * @var array 
     */
    static protected $js = array();
    
    /**
     * Dynamically created array of js scripts to be attached in layout after all 
     * other js scripts are attached. Use App::startJsCapture() - App::endJsCapture(TRUE) 
     * to extract js code from templates to this array.
     *
     * @var array 
     */
    static protected $lastJs = array();
    
    /**
     * Dynamically created list of js I18n files to be attached to html header in layout
     * in structure like:
     * 
     *      array(
     *          'Module01' => array('sk_SK'),
     *          'Module02' => array('sk_SK', 'en_EN'),
     *          ...
     *      )
     * 
     * Use App::setJsI18nFiles() to set this property 
     * Use App::getJsI18nLinks() to generate the html code for attached files
     * 
     * @var array 
     */
    static protected $jsI18nFiles = array();
    
    /**
     * Dynamically created list of js config to be attached to html header in layout
     * in structure like:
     * 
     *      array(
     *          'Module01' => array(
     *              'my.config01' => 'myValue',
     *              'yourConfig01' => 'yourValue',
     *              ...
     *          ),
     *          'Module02' => array(
     *              ...
     *          ),
     *          ...
     *      )
     * 
     * Use App::setJsConfig() to set this property 
     * Use App::getJsConfigCode() to generate the js code
     * 
     * @var array 
     */
    static protected $jsConfig = array();
    
    /**
     * Dynamically created array of codes to be attached in html head.
     * Use App::startHeadHtmlCapture() - App::endHeadHtmlCapture() to 
     * extract html head code from templates to this array.
     *
     * @var array 
     */
    static protected $headHtml = array();
    
    /**
     * Dynamically created array of codes to be attached in html head after all 
     * other codes are attached. Use App::startHeadHtmlCapture() - App::endHeadHtmlCapture(array('last' => true)) 
     * to extract html head code from templates to this array.
     *
     * @var array 
     */
    static protected $lastHeadHtml = array();
        
    /**
     * Dynamically created array of codes to be attached in html body end.
     * Use App::startBodyEndHtmlCapture() - App::endBodyEndHtmlCapture() to 
     * extract html body end code from templates to this array.
     *
     * @var array 
     */
    static protected $bodyEndHtml = array();
    
    /**
     * Dynamically created array of codes to be attached in html body end after all 
     * other codes are attached. Use App::startBodyEndHtmlCapture() - App::endBodyEndHtmlCapture(array('last' => true)) 
     * to extract html body end code from templates to this array.
     *
     * @var array 
     */
    static protected $lastBodyEndHtml = array();
        
    /**
     * Keeps actual retrieved content according to slug. 
     * It contains all content items retrieved in App::getContent().
     * 
     * ATTTENTION: It changes temporarily in method App::getContent() to make the
     * the owner content available for loaded content blocks and snippets (via App::getPropertyContent())
     * 
     * Use App::setPropertyContent() / App::getPropertyContent() to set & get this property.
     * 
     * @var array 
     */
    static protected $content = array();
    
    /**
     * Keeps app and modules settings in structure like:
     * 
     *      array(
     *          '{mudule}' =>  array(
     *              '{lang}' => array(
     *                  '{pid}' => '{value}', 
     *                   ...
     *              ),
     *              ...
     *          ),
     *          ...
     *      )
     *
     * @var array
     */
    static protected $settings = array(); 
    
    /**
     * Keeps application global variables
     *
     * @var array
     */
    static protected $globals = array(); 
    
    
    /**
     * READ ONLY RUNTIME PROPERTIES
     */
    
    /**
     * Keeps app and modules configs retrieved from config files and stored like:
     * 
     *      array(
     *          'App' => array(
     *              // default app config array (default is the first one read for module)
     *              'default' => array(...), 
     *              '{otherFileConfig} => array(...),
     *              ...
     *          ),
     *          'MyModule' => array(
     *              // default MyModule config array (default is the first one read for module)
     *              'default' => array(...), 
     *              '{otherFileConfig} => array(...),
     *              ...
     *          ),
     *          ...
     *      )
     * 
     * @var array
     */
    static protected $configs = array();
        
    /**
     * Keeps app and modules rights retrieved from rights files and stored like:
     * 
     *      array(
     *          'App' => array() // app rights array
     *          '{moduleA}' => array(...) // moduleA rights array
     *          '{moduleB}' => array(...) // moduleB rights array
     *          ...
     *      )
     * 
     * @var array
     */
    static protected $rights = array();
        
    /**
     * Array of all active languages retrieved from $languagesTable
     * Contains all items defined for languages. Default language
     * is stored under 'default' key, all other languages are under 
     * integer indexes, as retrieved from DB.
     * 
     * Use App::getPropertyLanguages() to read this property.
     * 
     * @var array
     */
    static protected $languages = array();
    
    /**
     * List of all active langs retrieved from $languagesTable.
     * Default lang is stored under 'default' key, all other langs are under 
     * integer indexes, as retrieved from DB:
     *  
     *      array (
     *          'default' => 'sk',
     *          'en',
     *          'de',
     *          ...
     *      )
     * 
     * Use App::getPropertyLangs() to read this property.
     *
     * @var array 
     */
    static protected $langs = array();
    
    /**
     * Is the application launched on local host?
     * 
     * Use ON_LOCALHOST constant to read this property value outside of App class
     *
     * @var bool
     */
    static protected $onLocalhost = false;
    
    /**
     * Is the application launched by request through HTTPS protocol?
     *
     * @var bool 
     */
    static protected $onHttps = false;
    
    /**
     * Has been the application installation already done?
     *
     * @var bool 
     */
    static protected $isInstalled = false;
    
    /**
     * Application local repository developer name
     * 
     * Use DEVELOPER constant to read this property value outside of App class
     * 
     * ATTENTION: This is populated only on localhost and its value is resolved 
     * by user of default ssh path in .hg/hgrc or if not found then by value stored
     * in file tmp/developer. Otherwise NULL.
     *
     * @var string
     */
    static protected $developer = null;
  
    /**
     * INTERNAL RUNTIME PROPERTIES
     */
    
    /**
     * Keeps microtime retrieved at the very begining of App processing.
     * 
     * Used to control time requiring processes
     * 
     * @var float
     */
    static protected $startMicrotime = null;
    
    /**
     * @todo
     * List of file handles get by fopen() function.
     * This serves to avoid duplicit fopen of the same file in the same mode.
     * The structure is:
     *      array(
     *          'my/opened/file.txt' => array(
     *              '{mode}' => {fileHandle}
     *          )
     *      )
     * 
     * All handles here are closed in shutdown() method
     *
     * @var array 
     */
    static protected $fileHandles = array();
    
    /**
     * Were application texts already loaded from file or no?
     * 
     * @var bool 
     */
    static protected $textsLoaded = false;
    
    /**
     * Was application already initalized by App::init()?
     * 
     * @var bool 
     */
    static protected $isInitialized = false;
    
    /**
     * Array of application translation strings for actual application lang stored
     * like:
     * 
     *      array(
     *          '{locale1}' => array(
     *              '{domain1}' => array(
     *                  'My string to translate 1' => 'Moj preložený reťazec 1',
     *                  'My string to translate 2' => 'Moj preložený reťazec 2',
     *                  ...
     *              )
     *              '{domain2} => array(),
     *              ...
     *          ),
     *          '{locale}' => array(
     *              ...
     *          ),
     *          ...
     *      )
     *
     * @var array 
     */
    static protected $translations = null;
    
    /**
     * List of module names in form like:
     * 
     *      array(
     *          '{Module01}' => '{Module01}',
     *          '{Module02}' => '{Module02}',
     *          ...,
     *      )
     * 
     * @var array 
     */
    static protected $modules = null;
    
    /**
     * List of module which have been already initialized or there was an attempt to 
     * do the initialization (and as the module has no initialization defined nothing has been done).
     * The list has following form:
     * 
     *      array(
     *          '{Module01}' => array('success' => true),
     *          '{Module02}' => array('success' => true, 'shutdown' => array('\Fajn\App\App::shutdown()'),
     *          '{Module03}' => array('success' => false),
     *          ...,
     *      )
     * 
     * @var array 
     */
    static protected $initializedModules = null;
    
    /**
     * List of models names in form like:
     * 
     *      array(
     *          '{Module01}' => array(
     *              '{Model01}' => {Model01},
     *              '{Model02}' => {Model02},
     *              ...
     *          ),
     *          '{Module01}' => array(
     *              '{Model03}' => {Model03},
     *              '{Model04}' => {Model04},
     *              ...
     *          ),
     *          ...,
     *      )
     * 
     * @var array 
     */
    static protected $models = null;
    
    /**
     * List of models instances serving to optimize collaboration between models:
     * 
     *      array(
     *          '{moduleName01}' => array(
     *              '{modelName01}' => {ModelInstance01},
     *              '{modelName02}' => {ModelInstance02},
     *              ...
     *          )
     *          '{moduleName02}' => array(
     *              ...
     *          )
     *          ...
     *      )
     * 
     * This is used for joining (it allows to define joins specifying model name
     * instead of table name) and for translations (= hidden creation of joins)
     * 
     * Any existing model is placed into this static array on its creation 
     * as it is supposed that there are no static relations between any models.
     * 
     * Use static method App::setModel() to set model
     * 
     * @var array 
     */
    static protected $Models = array();    
    
    /**
     * List of controllers instances serving to optimize collaboration between models:
     * 
     *      array(
     *          '{moduleName01}' => array(
     *              '{controllerName01}' => {ControllerInstance01},
     *              '{controllerName02}' => {ControllerInstance02},
     *              ...
     *          )
     *          '{moduleName02}' => array(
     *              ...
     *          )
     *          ...
     *      )
     * 
     * This is used for joining (it allows to define joins specifying controller name
     * instead of table name) and for translations (= hidden creation of joins)
     * 
     * Any existing controller is placed into this static array on its creation 
     * as it is supposed that there are no static relations between any controllers.
     * 
     * Use static method App::setController() to set controller
     * 
     * @var array 
     */
    static protected $Controllers = array();
    
    /**
     * Should the App::debug() avoid to use html output types ('console', 'echo' and 'hidden')?
     * 
     * This property is set by method App::setLayout() according to property
     * App::$avoidHtmlDebugLayouts
     * 
     * @var bool 
     */
    static protected $avoidHtmlDebug = false;
    
    /**
     * Was there a request for change to https protocol?
     * This property is set by App::requireHttps() and used in App::switchToHttp().
     * 
     * @var bool 
     */
    static protected $httpsIsRequired = false;
    
    /**
     * Curl resource handle returned by curl_init(). Used by App::request() for 
     * persistent requests.
     * 
     * @var resource 
     */
    static protected $requestHandle = null;
    
    /**
     * Absolute filepath to cookie file used by curl resource handle. Used by App::request() 
     * for persistent requests and requests following locations in protected zones 
     * after login which creates cookie.
     * 
     * @var string 
     */
    static protected $requestHandleCookieFile = null;
    
    /**
     * Used by App::startCssCapture(), App::endCssCapture() to control/validate 
     * sequence of mentioned methods calls.
     * 
     * @var int
     */
    static protected $openedCssCapturesCount = 0;
    
    /**
     * Used by App::startJsCapture(), App::endJsCapture() to control/validate 
     * sequence of mentioned methods calls.
     * 
     * @var int
     */
    static protected $openedJsCapturesCount = 0;
    
    /**
     * Used by App::startHeadHtmlCapture(), App::endHeadHtmlCapture() to control/validate 
     * sequence of mentioned methods calls.
     * 
     * @var int
     */
    static protected $openedHeadHtmlCapturesCount = 0;
    
    /**
     * Used by App::startBodyEndHtmlCapture(), App::endBodyEndHtmlCapture() to control/validate 
     * sequence of mentioned methods calls.
     * 
     * @var int
     */
    static protected $openedBodyEndHtmlCapturesCount = 0;
    
    /**
     * Path to flag file which signs if the application has been or not installed.
     * This is set in App::init()
     * 
     * @var string 
     */
    static protected $isInstalledFlagFilepath = null;
    
    /**
     * GET & SET METHODS
     */
    
    /**
     * Adds a model instance to App::$Models array
     * 
     * @param string $module Name of module. Use 'App' for application level or
     *      name of folder placed under /app/modules/.
     * @param string $name Model class name. 
     * @param Model $Model Model instance to be stored in models cache
     */
    static public function setModel($module, $name, Model $Model) {
        if (!isset(self::$Models[$module][$name])) {
            self::$Models[$module][$name] = $Model;
        }
    }
        
    /**
     * Adds a controller instance to App::$Controllers array
     * 
     * @param string $module Name of module. Use 'App' for application level or
     *      name of folder placed under /app/modules/.
     * @param string $name Controller class name. 
     * @param Controller $Controller Controller instance to be stored in controllers cache
     */
    static public function setController($module, $name, Controller $Controller) {
        if (!isset(self::$Controllers[$module][$name])) {
            self::$Controllers[$module][$name] = $Controller;
        }
    }
        
    /**
     * Set & get the App::$debug property.
     * It makes also initialization of FB class if TRUE on input.
     * It disallows FB class if FALSE on input.
     *  
     * @param bool $value New value of App::$debug property
     * 
     * @return bool Current value of App::$debug
     */
    static public function setDebug($value, $debugOutput = null, $fbOptions = null) {
        if (
            is_array($value)
            || (
                is_string($value)
                && !Validate::intNumber($value)
            )
        ) {
            self::$debug = (array)$value;
        }
        else {
            self::$debug = (bool)$value;
        }
        if (func_num_args() > 1) {
            self::setPropertyDebugOutput($debugOutput);
            if (func_num_args() > 2) {
                self::setPropertyFbOptions($fbOptions);
            }
        }
        if (class_exists('FB')) {
            FB::setEnabled((bool)self::$debug);
        }
    }
    static public function getPropertyDebug() {
        return self::$debug;
    }
    
    /**
     * Set the App::$debugOutput property.
     * 
     * @param string $value
     * 
     * @return string 
     */
    static public function setPropertyDebugOutput($value) {
        self::$debugOutput = strtolower($value);
    }
    
    /**
     * Set the App::$debugOutput property.
     * 
     * @param string $value
     * 
     * @return string 
     */
    static public function setPropertyFbOptions($value) {
        self::$fbOptions = $value;
        if (class_exists('FB')) {
            if (!empty(self::$fbOptions) && is_array(self::$fbOptions)) {
                FB::setOptions(self::$fbOptions);
            }
        }
    }
    
    /**
     * Returns email address or array of addresses where the log emails are sent by App::log(). 
     * If FALSE then the sending of log emails is turned off.
     *
     * @var string|array|bool 
     */
    static public function getPropertyLogEmail() {
        return self::$logEmail;
    }
    
    /**
     * Sets the App::$layoutModule and App::$layoutName property
     * 
     * NOTE: If either $module or $name is FALSE then the layout is turned off.
     *      Something like App::setLayout('App', FALSE) or App::(FALSE, 'default')
     *      make no sense. Layout is turned off in both cases. 
     *      Prefered way to turn off layout is App::setLayout(FALSE).
     * 
     * @param string|FALSE $module Name of layout module. If FALSE then layout is turned off.
     * @param string|FALSE $name Optional. Name of layout file. If not provided then 
     *      the last set layout name is preserved and just module is changed.
     *      If FALSE then layout is turned off. Defaults to NULL (= not provided).
     */
    static public function setLayout($module, $name = null) {
        if ($module === false || $name === false) {
            self::$layoutModule = false;
            self::$layoutName = false;
        }
        else {
            self::$layoutModule = $module;
            if ($name !== null) {
                self::$layoutName = $name;
            }
        }
        // update App::$avoidHtmlDebug property
        self::$avoidHtmlDebug = false;
        foreach(self::$avoidHtmlDebugLayouts as $layout) {
            if (
                is_array($layout)
                && (
                    !isset($layout['module'])
                    || $layout['module'] == self::$layoutModule
                )
                && isset($layout['name']) 
                && $layout['name'] == self::$layoutName
                || $layout == self::$layoutName
            ) {
                self::$avoidHtmlDebug = true;
                break;
            }
        }
    }
    
    /**
     * Returns layout module and name packed in an associative array with keys 
     * 'module' and 'name'. It can be used also like:
     * 
     * list($module, $name) = App::getLayout()
     * 
     * @return array
     */
    static public function getLayout() {
        return array(
            self::$layoutModule,
            self::$layoutName,
            'module' => self::$layoutModule,
            'name' => self::$layoutName,
        );
    }
    
    /**
     * Sets the App::$doctypeModule and App::$doctypeName property
     * 
     * NOTE: If either $module or $name is FALSE then the doctype is turned off.
     *      Something like App::setDoctype('App', FALSE) or App::(FALSE, 'default')
     *      make no sense. Doctype is turned off in both cases. 
     *      Prefered way to turn off doctype is App::setDoctype(FALSE).
     * 
     *      Moreover, doctype is used only if layout is used. It means that if the 
     *      layout is turned off then also no doctype will be loaded (= it's turned off).
     * 
     * @param string|FALSE $module Name of doctype module. If FALSE then doctype is turned off.
     * @param string|FALSE $name Optional. Name of doctype file. If not provided then 
     *      the last set doctype name is preserved and just module is changed.
     *      If FALSE then doctype is turned off. Defaults to NULL (= not provided).
     */
    static public function setDoctype($module, $name = null) {
        if ($module === false || $name === false) {
            self::$doctypeModule = false;
            self::$doctypeName = false;
        }
        else {
            self::$doctypeModule = $module;
            if ($name !== null) {
                self::$doctypeName = $name;
            }
        }
    }
    
    /**
     * Returns doctype module and name packed in an associative array with keys 
     * 'module' and 'name'. It can be used also like:
     * 
     * list($module, $name) = App::getDoctype()
     * 
     * @return array
     */
    static public function getDoctype() {
        return array(
            self::$doctypeModule,
            self::$doctypeName,
            'module' => self::$doctypeModule,
            'name' => self::$doctypeName,
        );
    }
    
    /**
     * Set & get the App::$layoutParams property, containing variables 
     * dynamically passed to layout and doctype
     * 
     * @param array $params Optional. Associaltive array of params to be passed to layout script. 
     *      This can be easily created by compact() function, e.g.: compact('varForScript1', '$varForScript2', ...)
     *      or it can be created manualy, e.g.: array('var1' => 'its value', 'var2' => 'its value' , ...)
     *      Defaults to empty array() - means no params are passed.
     * 
     * @return array Array of params for layout.
     */
    static public function setPropertyLayoutParams($params) {
        if (is_array($params)) {
            self::$layoutParams = array_merge(self::$layoutParams, $params);
        }
    }
    static public function getPropertyLayoutParams() {
        return self::$layoutParams;
    }
    
    /**
     * Get the App::$urlPathTrailingSlash property
     * 
     * @return bool
     */
    static public function getPropertyUrlPathTrailingSlash() {
        return self::$urlPathTrailingSlash;
    }

    /**
     * Sets seo title for layout
     * 
     * @param string $title
     */
    static public function setSeoTitle($title) {
        self::$layoutParams['seoTitle'] = $title;
    }

    /**
     * Gets seo title set for layout
     * 
     * @param bool $attachPrefixAndSuffix If TRUE then App settings fo seo title 
     *      prefix and suffix are applied to the seo tilte. Defaults to FALSE.
     * 
     * @return string
     */
    static public function getSeoTitle($attachPrefixAndSuffix = false) {
        $seoTitle = self::$layoutParams['seoTitle'];
        if ($attachPrefixAndSuffix) {
            if (($seoTitlePrefix = App::getSetting('App', 'seo.titlePrefix'))) {
                $seoTitle = $seoTitlePrefix . $seoTitle;
            }
            if (($seoTitleSuffix = App::getSetting('App', 'seo.titleSuffix'))) {
                $seoTitle .= $seoTitleSuffix;
            }
        }
        return $seoTitle;
    }
    
    /**
     * Sets seo keywords for layout
     * 
     * @param string $keywords
     */
    static public function setSeoKeywords($keywords) {
        self::$layoutParams['seoKeywords'] = $keywords;
    }
    
    /**
     * Gets seo keywords set for layout
     * 
     * @return string
     */
    static public function getSeoKeywords() {
        return self::$layoutParams['seoKeywords'];
    }
    
    /**
     * Sets seo description for layout
     * 
     * @param string $description
     */
    static public function setSeoDescription($description) {
        self::$layoutParams['seoDescription'] = $description;
    }
    
    /**
     * Gets seo description set for layout
     * 
     * @return string
     */
    static public function getSeoDescription() {
        return self::$layoutParams['seoDescription'];
    }
    
    /**
     * Sets seo index for layout. 
     * This is used to set robots meta tag index/noindex part.
     * 
     * ATTENTION: This method uses logical AND to accumulate boolean values.
     * So once index is set to FALSE it stays FALSE.
     * 
     * NOTE: This method is called implicitly in Paginator::__constuct() to turn off
     * indexing for page 2 and higher. Only first page sjould be indexed. Higher pages are
     * just followed.
     * 
     * @param bool $index If TRUE then 'index' is used in robots meta tag. If FALSE 
     *      (or any empty value except NULL) then 'noindex' is used. If NULL then no change is done.
     */
    static public function setSeoIndex($index) {
        if ($index === null) {
            return;
        }
        self::$layoutParams['seoIndex'] = self::$layoutParams['seoIndex'] && (bool)$index;
    }
    
    /**
     * Gets seo index set for layout.
     * This is used to set robots meta tag index/noindex part.
     * 
     * @return bool
     */
    static public function getSeoIndex() {
        return self::$layoutParams['seoIndex'];
    }
    
    /**
     * Sets seo follow for layout. 
     * This is used to set robots meta tag follow/nofollow part.
     * 
     * ATTENTION: This method uses logical AND to accumulate boolean values.
     * So once follow is set to FALSE it stays FALSE.
     * 
     * @param bool $follow If TRUE then 'follow' is used in robots meta tag. If FALSE 
     *      (or any empty value except NULL) then 'nofollow' is used. If NULL then no change is done.
     */
    static public function setSeoFollow($follow) {
        if ($follow === null) {
            return;
        }
        self::$layoutParams['seoFollow'] = self::$layoutParams['seoFollow'] && (bool)$follow;
    }
    
    /**
     * Gets seo follow set for layout.
     * This is used to set robots meta tag follow/nofollow part.
     * 
     * @return bool
     */
    static public function getSeoFollow() {
        return self::$layoutParams['seoFollow'];
    }
    
    /**
     * Sets seo canonical URL for layout. 
     * This is used to set <link rel="canonical" href="{url}"/> tag.
     * 
     * ATTENTION: Once canonical URL is set it cannot be erased by an empty value
     * (this method ignores empty values), BUT it can be rewritten by another nonempty
     * URL. It means the last set URL applies.
     * 
     * Canonical URL (of returned content) is the URL which is the correct
     * one (from point of SEO view) to avoid the same/duplicit/similar content on different URLs 
     * (see the below listed links). In general it can be absolute or relative.
     * In case of fajnwork website the relative URLs are ok as the absolute URLs
     * normalization (www vs no www, http vs https) is done in .htaccess.
     * ATTENTION: If no normalization is done in .htaccess (it means e.g. http://example.com/my-page
     * is not redirected to https://www.example.com/my-page and both return
     * the same content) then the <base> tag MUST be used to specify normalized URL base
     * for all relative URLs in a document (e.g. <base href="https://www.example.com/">).
     * 
     * See:
     * - https://developers.google.com/search/blog/2009/02/specify-your-canonical
     * - https://developers.google.com/search/docs/advanced/crawling/consolidate-duplicate-urls
     * - https://support.google.com/webmasters/answer/6080548
     * - https://www.semrush.com/blog/canonical-url-guide/
     * 
     * @param string|array $url URL string or an array of App::getUrl() options.
     * @param array $options An array of App::rebuildUrl() options
     */
    static public function setSeoCanonicalUrl($url, $options = array()) {
        if (!$url) {
            return;
        }
        $options['outputType'] = 'string';
        $options['absolute'] = true;
        self::$layoutParams['seoCanonicalUrl'] = self::rebuildUrl($url, $options);
    }
    
    /**
     * Gets seo canonical URL for layout. 
     * This is used to set <link rel="canonical" href="{url}"/> tag.
     * 
     * @return bool
     */
    static public function getSeoCanonicalUrl() {
        return self::$layoutParams['seoCanonicalUrl'];
    }
    
    /**
     * Sets admin smartTabs icon for layout
     * 
     * @param string $module Name of module. Use 'App' for application level or
     * name of folder placed under /app/modules/
     * @param string $icon Icon filename specified with full path starting from 
     *      provided $module/img folder, e.g. '/silk/my_icon.png'.
     * 
     * An alternative signature can be used:
     * 
     * @param string $icon Icon filename specified with full path starting from app root 
     *      (e.g. '/app/img/silk/my_icon.png') or with external link starting either by 'http://' or 'https://'. 
     *      Or explicit html of icon, e.g '< i class="fa fa-globe">< /i>'.
     */
    static public function setTabIcon($module, $icon = null) {
        // if direct icon signature
        if (($numArgs = func_num_args()) === 1) {
            $icon = $module;
        }
        // if module icon signature
        else {
            $icon = self::getModuleUrlPath($module) . '/img/' . ltrim($icon, '/');
        }
        self::$layoutParams['tabIcon'] = $icon;
    }
    
    /**
     * Gets admin smartTabs icon for layout
     * 
     * @return string Url root relative path to icon file, e.g. '/app/img/my_icon.png'
     */
    static public function getTabIcon() {
        return self::$layoutParams['tabIcon'];
    }
    
    /**
     * Returns icon defined in specifieed module config file for specified subject
     * 
     * @param string $module Module name, e.g. 'App', 'Eshop'.
     * @param string $subject Optional. Subject under module, e.g. 'Languages', 
     *      'Users', 'FileManager', 'Tools', ... Defaults to 'Module', means the
     *      module icon is returned. Default to 'Module'.
     * @param bool $ignoreModuleIcon Optional. If TRUE then module icon is igniored on 
     *      icon resolving. Defaults to FALSE, means if subject has no icon defined 
     *      then module icon is returned (if there is any defined)
     * 
     * @return string Icon or NULL if no icon found
     */
    static public function getAdminIcon($module, $subject = 'Module', $ignoreModuleIcon = false) {
        $icon = self::getConfig($module, 'adminIcons.' . $subject);
        if (empty($icon) && !$ignoreModuleIcon) {
            $icon = self::getConfig($module, 'adminIcons.Module');
        }
        return $icon;
    }
    
    /**
     * Sets admin smartTabs css class for layout
     * 
     * @param string $class Css class name, e.g. 'my-class'
     */
    static public function setTabClass($class) {
        self::$layoutParams['tabClass'] = $class;
    }
    
    /**
     * Gets admin smartTabs css class for layout
     * 
     * @return string Css class name, e.g. 'my-class'
     */
    static public function getTabClass() {
        return self::$layoutParams['tabClass'];
    }
    
    /**
     * Sets admin smartTabs text color.
     * 
     * @param string $color Tab text color. Any valid css color specification 
     *      can be used, e.g.: '#FFF', 'rgb(255,255,255)', 'rgba(255,255,255,0.5)'
     */
    static public function setTabColor($color) {
        self::$layoutParams['tabColor'] = $color;
    }
    
    /**
     * Gets admin smartTabs text color.
     * 
     * @return string Tab text color. e.g.  '#FFF', 'rgb(255,255,255)', 'rgba(255,255,255,0.5)'
     */
    static public function getTabColor() {
        return self::$layoutParams['tabColor'];
    }
    
    /**
     * Sets admin smartTabs background color.
     * 
     * @param string $color Tab background color. Any valid css color specification 
     *      can be used, e.g.: '#FFF', 'rgb(255,255,255)', 'rgba(255,255,255,0.5)'
     */
    static public function setTabBgColor($color) {
        self::$layoutParams['tabBgColor'] = $color;
    }
    
    /**
     * Gets admin smartTabs background color.
     * 
     * @return string Tab background color. e.g.  '#FFF', 'rgb(255,255,255)', 'rgba(255,255,255,0.5)'
     */
    static public function getTabBgColor() {
        return self::$layoutParams['tabBgColor'];
    }
    
    /**
     * Generates html for smartTabs data attributes
     * 
     * @return string Attributes string containing leading space. If no attributes are
     *      generated then empty string.
     */
    static public function getTabDataAttributes() {
        $attrs = '';
        if (!empty(self::$layoutParams['tabIcon'])) {
            // if the icon is defined by explicit html then replace quotes by apostrophe as 
            // value of data attribute cannot contain quotes
            if (substr(self::$layoutParams['tabIcon'], 0, 1) === '<') {
                $icon = str_replace('"', '\'', self::$layoutParams['tabIcon']);
                $attrs .= ' data-run-st-icon="' . $icon . '"';
            }
            else {
                $attrs .= ' data-run-st-icon="' . URL_ROOT . self::$layoutParams['tabIcon'] . '"';
            }
        }
        if (!empty(self::$layoutParams['tabClass'])) {
            $attrs .= ' data-run-st-class="' . self::$layoutParams['tabClass'] . '"';
        }
        if (!empty(self::$layoutParams['tabColor'])) {
            $attrs .= ' data-run-st-color="' . self::$layoutParams['tabColor'] . '"';
        }
        if (!empty(self::$layoutParams['tabBgColor'])) {
            $attrs .= ' data-run-st-bg-color="' . self::$layoutParams['tabBgColor'] . '"';
        }
        return $attrs;
    }
        
    /**
     * Sets the App::$cssFiles property, containing .css files dynamically attached to html header
     * in layout
     * 
     * @param string $module Name of module. Use 'App' for application level or
     * name of folder placed under /app/modules/
     * @param string|array $files Single .css filename (string) or an array of 
     *      .css filenames to be attached in html document header (see App/elements/bodyHead). 
     *      Filename must be specified with full path starting from provided $module/css folder.
     *      In the most cases, the simple name of .css file prefixed by slash will be enough, 
     *      as the most of .css files are placed directly under $module/css folder. 
     *      To specify attributes of <link> tag use syntax: array('my.css' => array('disabled' => true, ...)).
     *      To specify media attribute a shorthand array('my.css' => 'print') can be used
     *      instead of array('my.css' => array('media' => 'print')).
     *      
     * An alternative signature can be used:
     * 
     * @param string|array $files Single .css filename (string) or an array of 
     *      direct .css filenames to be attached in html document header (see App/elements/bodyHead). 
     *      Filename must be specified with full path starting from app root (e.g. '/app/css/libs/basic.css') 
     *      or with external link starting either by 'http://' or 'https://' or '//'. 
     *      To specify attributes of <link> tag use syntax: array('my.css' => array('disabled' => true, ...)).
     *      To specify media attribute a shorthand array('my.css' => 'print') can be used
     *      instead of array('my.css' => array('media' => 'print')).
     */
    static public function setCssFiles($module, $files = null) {
        // if direct files signature
        if (($numArgs = func_num_args()) === 1) {
            $files = $module;
            // set $moduleCssPath to FALSE to mark direct files
            $moduleCssPath = false;
        }
        // if module files signature
        else {
            $moduleCssPath = self::getModuleUrlPath($module) . '/css';
        }
        $files = (array)$files;
        foreach ($files as $key => $value) {
            if (is_int($key)) {
                $file = $value; 
                $attributes = false;
            }
            elseif (is_array($value)) {
                $file = $key; 
                $attributes = $value;
            }
            else {
                $file = $key; 
                $attributes = array('media' => $value);
            }
            // if direct files are provided
            if (!$moduleCssPath) {
                $file = URL_ROOT . $file;
            }
            // if module files are provided
            else {
                $file = URL_ROOT . $moduleCssPath . $file;
            }
            self::$cssFiles[$file] = $attributes;
        }      
    }
    
    /**
     * Allows in cooperation with App::endCssCapture() to capture inline css code 
     * through the project. The captured css code can be placed on desired place 
     * in layout using App::getCssCode().
     * 
     * The common pattern to use the method is following:
     * 
     *      < ?php App::startCssCapture() ? >
     *      <style type="text/css">
     *          /* a piece of css code in a template * /
     *      </style>
     *      < ?php App::endCssCapture() ? >
     * 
     * NOTE: It is up to you if the captured css code is wrapped in <style> tags to
     * profit from syntax highlighting in IDE. All style tags are removed by App::getCssCode().
     * 
     * ATTENTION: Identical css codes (including formating and whitespaces) are considered only once!
     */
    static public function startCssCapture() {
        ob_start();
        self::$openedCssCapturesCount++;
    }
    
    /**
     * Allows in cooperation with App::startCssCapture() to capture inline css code 
     * through the project. The captured css code can be placed on desired place 
     * in layout using App::getCssCode().
     * 
     * The common pattern to use the method is following:
     * 
     *      < ?php App::startCssCapture() ? >
     *      <style type="text/css">
     *          /* a piece of css code in a template * /
     *      </style>
     *      < ?php App::endCssCapture() ? >
     * 
     * NOTE: It is up to you if the captured css code is wrapped in <style> tags to
     * profit from syntax highlighting in IDE. All style tags are removed by App::getCssCode().
     * 
     * ATTENTION: Identical css codes (including formating and whitespaces) are considered only once!
     * 
     * @param array $options Following are available:
     *      - 'last' (bool)  If TRUE then the css code is placed after all other dynamic css codes.
     *      Defaults to FALSE.
     * 
     * @throws Exception on using capture end without correspondent capture start.
     */
    static public function endCssCapture($options = array()) {
        if (self::$openedCssCapturesCount === 0) {
            throw new Exception('You cannot call App::endCssCapture() without calling at first App::startCssCapture()');
        }
        self::setCss(ob_get_contents(), $options);
        ob_end_clean();
        self::$openedCssCapturesCount -= 1;
    }
    
    /**
     * Allows to set explicit css code through the project and to place it on desired place 
     * in layout using App::getCssCode().
     * 
     * NOTE: App::startCssCapture() with App::endCssCapture() is used to capture bigger
     * blocks of inline css code, profiting from syntax highlighting in IDE. 
     * App::setCss() is used to set small css codes which can be written in one small string,
     * e.g. App::setCss('#my-block{display:none;}')
     * 
     * @param string $code Css code string. ATTENTION: Identical css codes (including 
     *      formating and whitespaces) are considered only once!
     * @param array $options Following are available:
     *      - 'last' (bool) If TRUE then the js code is placed after all other dynamic css codes.
     *      Defaults to FALSE.
     */
    static public function setCss($code, $options = array()) {
        $defaults = array(
            'last' => false,
        );
        $options = array_merge($defaults, $options);
        $code = trim($code);
        if ($options['last']) {
            self::$lastCss[$code] = true;
            unset(self::$css[$code]);
        }
        elseif (!isset(self::$lastCss[$code])) {
            self::$css[$code] = true;
        }
    }
    
    /**
     * Removes all catched/set app css code accoridng options.
     * 
     * @param array $options Following are available:
     *      - 'last' (bool) If TRUE then the "last" css code is removed too.
     *          Defaults to TRUE.
     */
    static public function clearCss($options = array()) {
        $defaults = array(
            'last' => true,
        );
        $options = array_merge($defaults, $options);
        self::$css = array();
        if ($options['last']) {            
            self::$lastCss = array();
        }
    }
    
    /**
     * Sets the App::$jsFiles property, containing .js files dynamically attached html header
     * in layout
     * 
     * @param string $module Name of module. Use 'App' for application level or
     * name of folder placed under /app/modules/
     * @param string|array $files Single .js filename (string) or an array of 
     *      .js filenames to be attached in html document header (see App/elements/bodyHead). 
     *      Filename must be specified with full path starting from provided $module/js folder.
     *      In the most cases, the simple name of .js file prefixed by slash will be enough, 
     *      as the most of .js files are placed directly under $module/js folder.
     *      To specify attributes of <script> tag use syntax: 
     *      array('my.js' => array('async' => true, 'type' => 'module', ...)).
     *      To specify type attribute a shorthand array('my.js' => 'module') can be used
     *      instead of array('my.js' => array('type' => 'module')).
     * 
     * An alternative signature can be used:
     * 
     * @param string|array $files Single .js filename (string) or an array of 
     *      direct .js filenames to be attached in html document header (see App/elements/bodyHead). 
     *      Filename must be specified with full path starting from app root (e.g. '/app/js/vendors/jquery.min.js')
     *      or with external link starting either by 'http://' or 'https://' or '//'.
     *      To specify attributes of <script> tag use syntax: 
     *      array('my.js' => array('async' => true, 'type' => 'module', ...)).
     *      To specify type attribute a shorthand array('my.js' => 'module') can be used
     *      instead of array('my.js' => array('type' => 'module')).
     */
    static public function setJsFiles($module, $files = null) {
        // if direct files signature
        if (($numArgs = func_num_args()) === 1) {
            $files = $module;
            // set moduleJsPath to FALSE to mark direct files
            $moduleJsPath = false;
        }
        // if module files signature
        else {
            $moduleJsPath = self::getModuleUrlPath($module) . '/js';
        }
        $files = (array)$files;
        foreach ($files as $key => $value) {
            if (is_int($key)) {
                $file = $value; 
                $attributes = false;
            }
            elseif (is_array($value)) {
                $file = $key; 
                $attributes = $value;
            }
            else {
                $file = $key; 
                $attributes = array('type' => $value);
            }
            // When SystemJS is used then modules must be loaded in script tags 
            // with type set to 'systemjs-module'
            if (
                self::$jsModuleImplementation === 'system'
                && !empty($attributes)
                && !empty($attributes['type'])
                && $attributes['type'] === 'module'
            ) {
                self::$attachSystemJsCode = true;
                $attributes['type'] = 'systemjs-module';
            }
            // if direct files are provided
            if (!$moduleJsPath) {
                $file = $file;
            }
            // if module files are provided
            else {
                $file = $moduleJsPath . $file;
            }
            self::$jsFiles[$file] = $attributes;
        }       
    }
    
    /**
     * Sets the App::$jsI18nFiles property, containing .js I18n files dynamically attached html header
     * in layout
     * 
     * @param string|array $modules Single module name or list of modules to attach I18n files for.
     *      If provided as an array then its structure should be like:
     * 
     *          array(
     *              'Module01',
     *              'Module02' => 'sk_SK',
     *              'Module03' => array('sk_SK', 'en_EN'),
     *              ...,
     *          )
     */
    static public function setJsI18nFiles($modules) {
        $modules = (array)$modules;
       foreach ($modules as $key => $value) {
            if (is_int($key)) {
                $module = $value; 
                $locale = self::$locale;
            }
            else {
                $module = $key; 
                $locale = $value;
            }
            $locale = (array)$locale;
            if (isset(self::$jsI18nFiles[$module])) {
                self::$jsI18nFiles[$module] = array_unique(array_merge(self::$jsI18nFiles[$module], $locale));
            }
            else {
                self::$jsI18nFiles[$module] = $locale;
            }
       }
    }
    
    /**
     * Sets new js config and it's value, e.g.:
     * 
     *      App::setJsConfig('App', array(
     *          'urlRoot' => App::$urlRoot,
     *          'urlLang' => App::$urlLang,
     *          'urlBase' => App::$urlBase,
     *          'homeSlug' => App::$homeSlug,
     *      ));
     * 
     * @param string $module Name of module. Use 'App' for application level or
     * name of folder placed under /app/modules/
     * @param string $path Config path relative to module, e.g. 'urlRoot' or 'MyClass.myProperty'
     * @param mixed $value Config value.
     * 
     * An alternative signature can be used:
     * 
     * @param string $module Name of module. Use 'App' for application level or
     * name of folder placed under /app/modules/
     * @param array $config List of pairs 'cofigPath' => 'value', e.g. array('urlRoot' => '',  'MyClass.myProperty' => '2')
     */
    static public function setJsConfig($module, $path, $value = null) {
        // if direct files signatures
        if (($numArgs = func_num_args()) === 3) {
            $config = array($path => $value);
        }
        elseif ($numArgs === 2 && is_array($path)) {
            $config = $path;
        }
        else {
            throw new Exception('Missing third argument of the method');
        }
        if (!isset(self::$jsConfig[$module])) {
            self::$jsConfig[$module] = array();
        }
        foreach ($config as $path => $value) {
            if (is_int($path)) {
                throw new Exception('Invalid js config path');
            }
            $oldValue = Arr::setPath(self::$jsConfig[$module], $path, $value);
            if ($oldValue !== null && $oldValue != $value) {
                trigger_error("Js config '{$module}.{$path}' has been already set to value {$oldValue}. You changed it to {$value}", E_USER_WARNING);
            }
        }
    }
    
    /**
     * Allows in cooperation with App::endJsCapture() to capture inline js code 
     * through the project. The captured js code can be placed on desired place 
     * in layout using App::getJsCode().
     * 
     * The common pattern to use the method is following:
     * 
     *      < ?php App::startJsCapture() ? >
     *      <scrit type="text/javascript">
     *          // a piece of js code in a template
     *      </script>
     *      < ?php App::endJsCapture() ? >
     * 
     * NOTE: It is up to you if the captured js code is wrapped in <script> tags to
     * profit from syntax highlighting in IDE. All script tags are removed by App::getJsCode().
     * 
     * ATTENTION: Identical js codes (including formating and whitespaces) are considered only once!
     */
    static public function startJsCapture() {
        ob_start();
        self::$openedJsCapturesCount++;
    }
    
    /**
     * Allows in cooperation with App::startJsCapture() to capture inline js code
     * through the project. The captured js code can be placed on desired place 
     * in layout using App::getJsCode().
     * 
     * The common pattern to use the method is following:
     * 
     *      < ?php App::startJsCapture() ? >
     *      <scrit type="text/javascript">
     *          // a piece of js code in a template
     *      </script>
     *      < ?php App::endJsCapture() ? >
     * 
     * NOTE: It is up to you if the captured js code is wrapped in <script> tags to
     * profit from syntax highlighting in IDE. All script tags are removed by App::getJsCode().
     * 
     * ATTENTION: Identical js codes (including formating and whitespaces) are considered only once!
     * 
     * @param array $options Following are available:
     *      - 'last' (bool) If TRUE then the js code is placed after all other dynamic js codes.
     *      Defaults to FALSE.
     *      - 'permanent' (bool) If TRUE then js code is preserved even over redirections,
     *      and kept till it is retrieved by App::getJsCode(). This is usefull to 
     *      launch general js scripts (not attached to page html, e.g. calling API 
     *      urls by XHR) and the js is created by page redirected to other page. 
     *      Defaults to FALSE.
     *      - 'return' (bool) If TRUE then the captured js code is returned (wrapping 
     *      <script> tags are removed) and in such a case it is not stored into 
     *      application js scripts. Defaults to FALSE.
     * 
     * @throws Exception on using capture end without correspondent capture start.
     */
    static public function endJsCapture($options = array()) {
        $defaults = array(
            'return' => false,
        );
        $options = array_merge($defaults, $options);
        if (self::$openedJsCapturesCount === 0) {
            throw new Exception('You cannot call App::endJsCapture() without calling at first App::startJsCapture()');
        }
        $code = ob_get_contents();
        ob_end_clean();
        self::$openedJsCapturesCount -= 1;
        if ($options['return']) {
            return trim(preg_replace('/<\/?script(?:>|[^a-z>][^>]*>)/i', '', $code));
            
        }
        self::setJs($code, $options);
    }
    
    /**
     * Allows to set explicit js code through the project and to place it on desired place 
     * in layout using App::getJsCode().
     * 
     * NOTE: App::startJsCapture() with App::endJsCapture() is used to capture bigger
     * blocks of inline js code, profiting from syntax highlighting in IDE. 
     * App::setJs() is used to set small js codes which can be written in one small string,
     * e.g. App::setJs('jQuery(function(){CKEDITOR.replace("_data_text_input_1_1", {"height":"600px"});});')
     * 
     * @param string $code Js code string. ATTENTION: Identical js codes (including 
     *      formating and whitespaces) are considered only once!
     * @param array $options Following are available:
     *      - 'last' (bool) If TRUE then the js code is placed after all other dynamic js codes.
     *      Defaults to FALSE.
     *      - 'permanent' (bool) If TRUE then js code is preserved even over redirections,
     *      and kept till it is retrieved by App::getJsCode(). This is usefull to 
     *      launch general js scripts (not attached to page html, e.g. calling API 
     *      urls by XHR) and the js is created by page redirected to other page. 
     *      Defaults to FALSE.
     */
    static public function setJs($code, $options = array()) {
        $defaults = array(
            'last' => false,
            'permanent' => false,
        );
        $options = array_merge($defaults, $options);
        $code = trim($code);
        if ($options['last']) {
            if ($options['permanent']) {
                $_SESSION['_app']['lastJs'][$code] = true;
            }
            self::$lastJs[$code] = true;
            unset($_SESSION['_app']['js'][$code]);
            unset(self::$js[$code]);
        }
        elseif (!isset(self::$lastJs[$code])) {
            if ($options['permanent']) {
                $_SESSION['_app']['js'][$code] = true;
            }
            self::$js[$code] = true;
        }
    }
    
    /**
     * Removes all catched/set app js code accoridng options.
     * 
     * @param array $options Following are available:
     *      - 'last' (bool) If TRUE then the "last" js code is removed too.
     *          Defaults to TRUE.
     *      - 'permanent' (bool) If TRUE then the permanent js code is removed too.
     *          Defaults to TRUE.
     */
    static public function clearJs($options = array()) {
        $defaults = array(
            'last' => true,
            'permanent' => true,
        );
        $options = array_merge($defaults, $options);
        self::$js = array();
        if ($options['permanent']) {
            unset($_SESSION['_app']['js']);
        }
        if ($options['last']) {            
            self::$lastJs = array();
            if ($options['permanent']) {
                unset($_SESSION['_app']['lastJs']);
            }
        }
    }
    
    /**
     * Allows in cooperation with App::endHeadHtmlCapture() to capture html head code 
     * through the project. The captured code can be placed on desired place 
     * in layout using App::getHeadHtmlCode().
     * 
     * The common pattern to use the method is following:
     * 
     *      < ?php App::startHeadHtmlCapture() ? >
     *      <meta name="format-detection" content="telephone=no" />
     *      < ?php App::endHeadHtmlCapture() ? >
     */
    static public function startHeadHtmlCapture() {
        ob_start();
        self::$openedHeadHtmlCapturesCount++;
    }
    
    /**
     * Allows in cooperation with App::startHeadHtmlCapture() to capture html head code 
     * through the project. The captured code can be placed on desired place 
     * in layout using App::getHeadHtmlCode().
     * 
     * The common pattern to use the method is following:
     * 
     *      < ?php App::startHeadHtmlCapture() ? >
     *      <meta name="format-detection" content="telephone=no" />
     *      < ?php App::endHeadHtmlCapture() ? >
     * 
     * @param array $options Following are available:
     *      - 'last' (bool)  If TRUE then the code is placed after all other dynamic codes.
     *      Defaults to FALSE.
     * 
     * @throws Exception on using capture end without correspondent capture start.
     */
    static public function endHeadHtmlCapture($options = array()) {
        $defaults = array(
            'last' => false,
        );
        $options = array_merge($defaults, $options);
        if (self::$openedHeadHtmlCapturesCount === 0) {
            throw new Exception('You cannot call App::endHeadHtmlCapture() without calling at first App::startHeadHtmlCapture()');
        }
        self::setHeadHtml(ob_get_contents(), $options['last']);
        ob_end_clean();
        self::$openedHeadHtmlCapturesCount -= 1;
    }
    
    /**
     * Allows to set explicit html head code through the project and to place it on desired place 
     * in layout using App::getHeadHtmlCode().
     * 
     * NOTE: App::startHeadHtmlCapture() with App::endHeadHtmlCapture() is used to capture bigger
     * blocks of inline html head code, profiting from syntax highlighting in IDE. 
     * App::setHeadHtml() is used to set small html head codes which can be written in one small string,
     * e.g. App::setHeadHtml('<meta name="format-detection" content="telephone=no" />')
     * 
     * @param string $code Html head code string.
     * @param bool $last If TRUE then the html head code is placed after all other 
     *      dynamic html head codes. Defaults to FALSE.
     */
    static public function setHeadHtml($code, $last = false) {
        if ($last) {
            self::$lastHeadHtml[] = trim($code);
        }
        else {
            self::$headHtml[] = trim($code);
        }
    }
        
    /**
     * Allows in cooperation with App::endBodyEndHtmlCapture() to capture html body end code 
     * through the project. The captured code can be placed on desired place 
     * in layout using App::getBodyEndHtmlCode().
     * 
     * The common pattern to use the method is following:
     * 
     *      < ?php App::startBodyEndHtmlCapture() ? >
     *      <meta name="format-detection" content="telephone=no" />
     *      < ?php App::endBodyEndHtmlCapture() ? >
     */
    static public function startBodyEndHtmlCapture() {
        ob_start();
        self::$openedBodyEndHtmlCapturesCount++;
    }
    
    /**
     * Allows in cooperation with App::startBodyEndHtmlCapture() to capture html body end code 
     * through the project. The captured code can be placed on desired place 
     * in layout using App::getBodyEndHtmlCode().
     * 
     * The common pattern to use the method is following:
     * 
     *      < ?php App::startBodyEndHtmlCapture() ? >
     *      <meta name="format-detection" content="telephone=no" />
     *      < ?php App::endBodyEndHtmlCapture() ? >
     * 
     * @param array $options Following are available:
     *      - 'last' (bool)  If TRUE then the code is placed after all other dynamic codes.
     *      Defaults to FALSE.
     * 
     * @throws Exception on using capture end without correspondent capture start.
     */
    static public function endBodyEndHtmlCapture($options = array()) {
        $defaults = array(
            'last' => false,
        );
        $options = array_merge($defaults, $options);
        if (self::$openedBodyEndHtmlCapturesCount === 0) {
            throw new Exception('You cannot call App::endBodyEndHtmlCapture() without calling at first App::startBodyEndHtmlCapture()');
        }
        self::setBodyEndHtml(ob_get_contents(), $options['last']);
        ob_end_clean();
        self::$openedBodyEndHtmlCapturesCount -= 1;
    }
    
    /**
     * Allows to set explicit html body end code through the project and to place it on desired place 
     * in layout using App::getBodyEndHtmlCode().
     * 
     * NOTE: App::startBodyEndHtmlCapture() with App::endBodyEndHtmlCapture() is used to capture bigger
     * blocks of inline html body end code, profiting from syntax highlighting in IDE. 
     * App::setBodyEndHtml() is used to set small html body end codes which can be written in one small string,
     * e.g. App::setBodyEndHtml('<meta name="format-detection" content="telephone=no" />')
     * 
     * @param string $code Html body end code string.
     * @param bool $last If TRUE then the html body end code is placed after all other 
     *      dynamic html body end codes. Defaults to FALSE.
     */
    static public function setBodyEndHtml($code, $last = false) {
        if ($last) {
            self::$lastBodyEndHtml[] = trim($code);
        }
        else {
            self::$bodyEndHtml[] = trim($code);
        }
    }
    
    /**
     * Allows to set application JSON-LD microdata.
     * See https://schema.org/ or https://en.wikipedia.org/wiki/JSON-LD or 
     * https://developers.google.com/search/docs/data-types/product
     * 
     * @param array $microdata
     * @param array $options Following are available:
     *      - 'id' (string) Generated script tag css id. Defaults to NULL.
     */
    static public function setMicrodata($microdata, $options = array()) {
        $options = array_merge(array(
            'id' => null,
        ), $options);
        $idHtml = '';
        if ($options['id']) {
            $idHtml = ' id="' . $options['id'] . '"';
        }
        self::setBodyEndHtml(
            '<script type="application/ld+json"' . $idHtml . '>' . 
                json_encode($microdata) . 
            '</script>'
        );
    }
        
    /**
     * Returns a url path to specified file in specified module. The path is either
     * URL_ROOT relative or is prefixed by URL_ROOT depending on value od $prefixByUrlRoot.
     * 
     * @param string $module
     * @param string $file Module relative url path to file.
     * @param bool $prefixByUrlRoot If TRUE then path is prefixed by URL_ROOT. Defaults
     *          to FALSE, means the returned path is URL_ROOT relative.
     * 
     * @return string
     */
    static public function getFileUrlPath($module, $file, $prefixByUrlRoot = false) {
        $file = self::getModuleUrlPath($module) . '/' . ltrim($file, '/');
        if ($prefixByUrlRoot) {
            $file = URL_ROOT . $file;
        }
        return $file;
    }
    
    /**
     * Get App::$languages property
     * 
     * @return array 
     */
    static public function getPropertyLanguages() {
        return self::$languages;
    }
    
    /**
     * Get App::$langs property
     * 
     * @return array 
     */
    static public function getPropertyLangs() {
        return self::$langs;
    }
    
    /**
     * Returns a list of all active and published langs. Default lang is stored 
     * under 'default' key, all other langs are under integer indexes.
     * 
     * @return array
     */
    static public function getPublishedLangs() {
        $langs = array();
        foreach (self::$languages as $key => $language) {
            if (!empty($language['published'])) {
                if ($key === 'default') {
                    $langs[$key] = $language['lang'];
                }
                else {
                    $langs[] = $language['lang'];
                }
            }
        }
        return $langs;
    }

    static public function setPropertyContent($value) {
        self::$content = $value;
    }
    
    static public function getPropertyContent() {
        return self::$content;
    }
    
    static public function getPropertyContentsTable() {
        return self::$contentsTable;
    }
    
    static public function getPropertyContentsSchema() {
        return self::$contentsSchema;
    }
    
    static public function getPropertyDecimals() {
        return self::$decimals;
    }
        
    static public function getActualDecimals() {
        return Sanitize::value(self::$decimals[self::$lang], 0);
    }
        
    static public function getPropertyDecimalPoint() {
        return self::$decimalPoint;
    }
        
    static public function getActualDecimalPoint() {
        return Sanitize::value(self::$decimalPoint[self::$lang], '.');
    }
        
    static public function getPropertyThousandsSeparator() {
        return self::$thousandsSeparator;
    }
        
    static public function getActualThousandsSeparator() {
        return Sanitize::value(self::$thousandsSeparator[self::$lang], ',');
    }
        
    static public function setPropertyMailEngine($value) {
        $value = strtolower(trim($value));
        if ($value != 'smtp' && $value != 'mail') {
            throw new Exception ("Invalid value {$value} for App::\$mailEngine");
        }
        self::$mailEngine = $value;
    }
    
    static public function setPropertyImageEngine($value) {
        $value = strtolower(trim($value));
        if ($value != 'gd' && $value != 'imagick' && $value != 'imlib') {
            throw new Exception ("Invalid value {$value} for App::\$imageEngine");
        }
        self::$imageEngine = $value;
    }
    
    static public function getPropertyImageEngine() {
        return self::$imageEngine;
    }
    
    static public function getPropertyOpenedCssCapturesCount() {
        return self::$openedCssCapturesCount;
    }
    
    static public function getPropertyOpenedJsCapturesCount() {
        return self::$openedJsCapturesCount;
    }
    
    static public function getPropertyOpenedHeadHtmlCapturesCount() {
        return self::$openedHeadHtmlCapturesCount;
    }
    
    static public function getPropertyOpenedBodyEndHtmlCapturesCount() {
        return self::$openedBodyEndHtmlCapturesCount;
    }
    
    static public function getPropertyAllowOriginComments() {
        return self::$allowOriginComments;
    }
    
    /**
     * OTHER METHODS
     */
        
    /**
     * Inits all the necessary things for further application processing
     * 
     * THIS METHOD SHOULD BE LAUNCHES AS THE SECOND THING YOU DO!
     * JUST AFTER REQUIRING APP CLASS!
     * 
     * IT SHOULD BE LAUNCHED ONLY ONCE PER APPLICATION EXECUTION! 
     * DUPLICIT INITIALIZATION HAS NO SENSE.
     * 
     * @param array $options Optional. Options of application initialization:
     *      - 'appCongif' (string) Explicit application config file name to load 
     *          configs for App module. The given path must be relative to ROOT. 
     *          Defaults to NULL, means implicit App module config file is used.
     *      - 'exceptionOnDuplicitCall' (bool) If TRUE then an exception is thrown
     *          on duplicit call of this method. If FALSE then the method just silently
     *          returns without doing anything on duplicit calls. Defaults to TRUE.
     * 
     * @throws Exception
     */
    static public function init($options = array()) {
        $defaults = array(
          'appConfig' => null,  
          'exceptionOnDuplicitCall' => true,
        );
        $options = array_merge($defaults, $options);
        
        // check for duplicit initialization of application
        if (self::$isInitialized) {
            if ($options['exceptionOnDuplicitCall']) {
                throw new Exception("Duplicit launch of App::init()");
            }
            return;
        }
        
        // Store start microtime to be able to check timeline of time critical operations
        // Use App::getElapsedMicrotime() and App::getFreeMicrotime() to make this check
        self::$startMicrotime = microtime(true);
        
        // set toplevel exception handler (defined in basic functions)
        set_exception_handler(array('App', 'catchThrowable'));
        
        // set shutdown function (a kind of destructor)
        register_shutdown_function(array('App', 'shutdown'));
        
        self::$outputBufferInitialLevel = ob_get_level();
        self::startPhpErrorsHtmlCapture();
        
        $inDocker = getenv('IN_DOCKER');
        // IN_DOCKER
        if (!defined('IN_DOCKER')) {
            /**
             * IN_DOCKER - Is the application launched in local Docker?
             * Possible values are FALSE, 'onLocalhost' and 'inProduction'.
             */
            define('IN_DOCKER', $inDocker);
        }

        // set timezone in Docker
        if (
            $inDocker
            && ($timezone = getenv('TZ'))
        ) {
            date_default_timezone_set($timezone);
        }

        self::$onLocalhost =
            // if in docker on localhost
            $inDocker === 'onLocalhost'
            // case of test suite launching from console, it means there is no server involved
            || !isset($_SERVER['SERVER_ADDR'])
            // IPv4  reserve for localhost the entire range from 127.0.0.1 to ***************
            // 127.0.0.1 is the default address. Check also REMOTE_ADDR as some servers 
            // have SERVER_ADDR set to 127.0.0.1 even in production. REMOTE_ADDR always 
            // starts by 127 for request to localhost server and is set to client public IP 
            // for request to production server
            || substr($_SERVER['SERVER_ADDR'], 0, 4) == '127.'
            && substr($_SERVER['REMOTE_ADDR'], 0, 4) == '127.'
            // IPv6 has just single localhost address ::1. For use of REMOTE_ADDR see
            // the above comment
            || $_SERVER['SERVER_ADDR'] == '::1'
            && $_SERVER['REMOTE_ADDR'] == '::1';
        // ON_LOCALHOST
        if (!defined('ON_LOCALHOST')) {
            /**
             * ON_LOCALHOST - Is the application launched on local host?
             */
            define('ON_LOCALHOST', self::$onLocalhost);
        }
        
        self::$onHttps = 
            // PROTO is created by .htaccess, in $_SERVER it appears lowercased (proto)
            isset($_SERVER['proto']) && $_SERVER['proto'] === 'https'
            ||
            isset($_SERVER['PROTO']) && $_SERVER['PROTO'] === 'https'
            ||
            isset($_SERVER['REQUEST_SCHEME']) && $_SERVER['REQUEST_SCHEME'] === 'https'
            ||
            isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
        // ON_HTTPS
        if (!defined('ON_HTTPS')) {
            /**
             * ON_HTTPS - Is the application launched by request through HTTPS protocol?
             */
            define('ON_HTTPS', self::$onHttps);
        }
        
        // DS
        if (!defined('DS')) {
            /**
             * DS constant is a shortcut to DIRECTORY_SEPARATOR
             */
            define('DS', DIRECTORY_SEPARATOR);
        }
        
        // ROOT
        if (!defined('ROOT')) {
            /**
             *  ROOT constant points to this project directory, e.g. /var/www/my_project.
             *  It does not contain trailing slash!
             */
            define('ROOT', dirname(dirname(dirname(dirname(__FILE__)))));
        }
        
        // TMP 
        if (!defined('TMP')) {
            /**
             * TMP constant contains absolute path to this project tmp directory.
             * It does not contain trailing slash.
             */
            define('TMP', ROOT . DS . 'tmp');
        }
        
        // url base
        $s = null;
        if (self::$onHttps) {
            $s ='s';
        }
        $httpHost = (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : null);
        if ($httpHost) {
            $urlBase = 'http' . $s . '://' . $httpHost;
        }
        else {
            $urlBase = ''; // when calling App::init() from phpunit test
        }
        self::$urlBase = $urlBase;
        
        // E_DEPRECATED constant should be defined before errorReporting is set
        if (!defined('E_DEPRECATED')) {
            define('E_DEPRECATED', 8192);
        }  
        
        // PHP_VERSION_ID
        if (!defined('PHP_VERSION_ID')) {
            $version = explode('.', PHP_VERSION);
            /**            
             * PHP_VERSION_ID is defined as a number, where the higher the number 
             * is, the newer a PHP version is used. It's defined as used in the above 
             * expression:
             * 
             * $version_id = $major_version * 10000 + $minor_version * 100 + $release_version;
             * 
             * E.g. for PHP 5.2.7 the PHP_VERSION_ID is 50207.
             * 
             * Now with PHP_VERSION_ID we can check for features this PHP version 
             * may have, this doesn't require to use version_compare() everytime 
             * you check if the current PHP version may not support a feature.
             */
            define('PHP_VERSION_ID', ($version[0] * 10000 + $version[1] * 100 + $version[2]));
        }
        
        $match = null;
        if (
            self::$onLocalhost
            && (
                ($hgrcFile = ROOT . DS . '.hg' . DS . 'hgrc')
                && is_readable($hgrcFile)
                && ($hgrcContent = file_get_contents($hgrcFile))
                && preg_match('/^default\s*=\s*ssh:\/\/([^@]+)@/im', $hgrcContent, $match)
                && ($hgUser = trim($match[1]))
                ||
                ($developerFile = TMP . DS . 'developer')
                && is_readable($developerFile)
                && ($developer = trim(file_get_contents($developerFile)))
            )
        ) {
            if ($hgUser) {
                self::$developer = $hgUser;
            }
            else {
                self::$developer = $developer;
            }
        }
        // DEVELOPER
        if (!defined('DEVELOPER')) {
            /**
             * DEVELOPER - Application local repository developer name
             * 
             * ATTENTION: This is populated only on localhost and its value is resolved 
             * by user of default ssh path in .hg/hgrc or if not found then by value stored
             * in file tmp/developer. Otherwise NULL.
             */
            define('DEVELOPER', self::$developer);
        }
        
        // check if already installed
        self::$isInstalledFlagFilepath = TMP . DS . 'installed';
        self::$isInstalled = file_exists(self::$isInstalledFlagFilepath) 
            && (filemtime(self::$isInstalledFlagFilepath) > strtotime(self::$installModificationDatetime));
        // IS_INSTALLED
        if (!defined('IS_INSTALLED')) {
            /**
             * IS_INSTALLED - Has the application been already installed?
             */
            define('IS_INSTALLED', self::$isInstalled);
        }
        
        // include composer vendors autoload file
        $composerAutoloadFile = ROOT . DS . 'vendor' . DS . 'autoload.php';
        if (file_exists($composerAutoloadFile)) {
            require_once $composerAutoloadFile;
        }
        
        // define paths to core libs and vendors
        $defaultLibsDir = ROOT . DS . 'app' . DS . 'libs' . DS . 'default' . DS;
        $vendorsDir = ROOT . DS . 'app' . DS . 'vendors' . DS;

        // load basic functions and Utility class
        require_once $defaultLibsDir . 'globalFunctions.php';
        require_once $defaultLibsDir . 'Str.php';
        require_once $defaultLibsDir . 'Number.php';
        require_once $defaultLibsDir . 'Arr.php';
        require_once $defaultLibsDir . 'Date.php';
        require_once $defaultLibsDir . 'File.php';
        require_once $defaultLibsDir . 'Utility.php';
        require_once $defaultLibsDir . 'Validate.php';
        require_once $defaultLibsDir . 'Sanitize.php';
        require_once $defaultLibsDir . 'Html.php';
        require_once $defaultLibsDir . 'DB.mysql.php';
        
        // apply _appInit config values to App class config properties
        $appInit = self::getConfig('App', '_appInit', $options['appConfig']);
        if (isset($appInit['debug'])) {
            self::setDebug($appInit['debug']);
        }
        if (isset($appInit['debugOutput'])) {
            self::setPropertyDebugOutput($appInit['debugOutput']);
        }
        if (isset($appInit['avoidHtmlDebugLayouts'])) {
            self::$avoidHtmlDebugLayouts = (array)$appInit['avoidHtmlDebugLayouts'];
        }
        if (isset($appInit['debugEmailOptions'])) {
            self::$debugEmailOptions = array_merge(
                self::$debugEmailOptions, 
                $appInit['debugEmailOptions']
            );
        }
        if (isset($appInit['logEmail'])) {
            self::$logEmail = $appInit['logEmail'];
        }
        if (isset($appInit['fbOptions'])) {
            self::setPropertyFbOptions($appInit['fbOptions']);
        }
        if (isset($appInit['sqlLogging'])) {
            self::$sqlLogging = $appInit['sqlLogging'];
        }
        if (isset($appInit['errorReporting'])) {
            self::$errorReporting = $appInit['errorReporting'];
        }
        if (isset($appInit['errorCatching'])) {
            self::$errorCatching = $appInit['errorCatching'];
        }
        if (isset($appInit['useJsEmbroidery'])) {
            self::$useJsEmbroidery = (bool)$appInit['useJsEmbroidery'];
        }
        if (isset($appInit['allowOriginComments'])) {
            self::$allowOriginComments = (bool)$appInit['allowOriginComments'];
        }
        if (isset($appInit['dbConfigName'])) {
            self::$dbConfigName = (string)$appInit['dbConfigName'];
        }
        if (isset($appInit['usersTable'])) {
            self::$usersTable = (string)$appInit['usersTable'];
        }
        if (isset($appInit['groupsTable'])) {
            self::$groupsTable = (string)$appInit['groupsTable'];
        }
        if (isset($appInit['settingsTable'])) {
            self::$settingsTable = (string)$appInit['settingsTable'];
        }
        if (isset($appInit['contentsTable'])) {
            self::$contentsTable = (string)$appInit['contentsTable'];
        }
        if (isset($appInit['contentsSchema'])) {
            self::$contentsSchema = (string)$appInit['contentsSchema'];
        }
        if (isset($appInit['languagesTable'])) {
            self::$languagesTable = $appInit['languagesTable']; // array or string
        }
        if (isset($appInit['localizedLang'])) {
            self::$localizedLang = (bool)$appInit['localizedLang'];
        }
        if (isset($appInit['autodetectLang'])) {
            self::$autodetectLang = (bool)$appInit['autodetectLang'];
        }
        if (isset($appInit['defaultLangHidden'])) {
            self::$defaultLangHidden = (bool)$appInit['defaultLangHidden'];
        }
        if (array_key_exists('urlPathTrailingSlash', $appInit)) {
            self::$urlPathTrailingSlash = (bool)$appInit['urlPathTrailingSlash'];
        }
        if (isset($appInit['urlInherit'])) {
            self::$urlInherit = (array)$appInit['urlInherit'];
        }
        if (isset($appInit['hashOptions'])) {
            self::$hashOptions = (array)$appInit['hashOptions'];
        }
        if (isset($appInit['decimals'])) {
            self::$decimals = (array)$appInit['decimals'];
        }
        if (isset($appInit['decimalPoint'])) {
            self::$decimalPoint = (array)$appInit['decimalPoint'];
        }
        if (isset($appInit['thousandsSeparator'])) {
            self::$thousandsSeparator = (array)$appInit['thousandsSeparator'];
        }
        if (isset($appInit['urlRoot'])) {
            self::$urlRoot = (string)$appInit['urlRoot'];
        }
        self::$urlRoot = rtrim(self::$urlRoot, '/');
        if (isset($appInit['homeSlug'])) {
            self::$homeSlug = (string)$appInit['homeSlug'];
        }
        if (isset($appInit['adminModules'])) {
            self::$adminModules = (array)$appInit['adminModules'];
        }
        if (isset($appInit['defaultLayout'])) {
            if (is_array($appInit['defaultLayout'])) {
                self::$defaultLayout = $appInit['defaultLayout'];
            }
            else {
                self::$defaultLayout = (string)$appInit['defaultLayout'];
            }
        }
        if (isset($appInit['homeLayout'])) {
            if (is_array($appInit['homeLayout'])) {
                self::$homeLayout = $appInit['homeLayout'];
            }
            else {
                self::$homeLayout = (string)$appInit['homeLayout'];
            }
        }
        if (isset($appInit['adminLoginSlug'])) {
            self::$adminLoginSlug = trim((string)$appInit['adminLoginSlug'], '/');
        }
        if (isset($appInit['adminSlug'])) {
            self::$adminSlug = trim((string)$appInit['adminSlug'], '/');
        }
        if (isset($appInit['adminOnly'])) {
            self::$adminOnly = (bool)$appInit['adminOnly'];
        }
        if (isset($appInit['userLoginPid'])) {
            self::$userLoginPid = trim((string)$appInit['userLoginPid']);
        }
        if (isset($appInit['userLoginTargetPid'])) {
            self::$userLoginTargetPid = trim((string)$appInit['userLoginTargetPid']);
        }
        if (isset($appInit['permanentSessionLifetime'])) {
            self::$permanentSessionLifetime = (int)$appInit['permanentSessionLifetime'];
        }
        if (isset($appInit['mailEngine'])) {
            self::setPropertyMailEngine($appInit['mailEngine']);
        }
        if (isset($appInit['imageEngine'])) {
            self::setPropertyImageEngine($appInit['imageEngine']);
        }
        if (isset($appInit['httpsIsAvailable'])) {
            self::$httpsIsAvailable = (bool)$appInit['httpsIsAvailable'];
        }
        self::$httpsIsAvailable = (self::$httpsIsAvailable || self::$onHttps);
        if (isset($appInit['allowSwitchBetweenHttpAndHttps'])) {
            self::$allowSwitchBetweenHttpAndHttps = (bool)$appInit['allowSwitchBetweenHttpAndHttps'];
        }
        if (!empty($appInit['firstCssFiles'])) {
            self::$firstCssFiles = (array)$appInit['firstCssFiles'];
        }
        if (!empty($appInit['lastCssFiles'])) {
            self::$lastCssFiles = (array)$appInit['lastCssFiles'];
        }
        if (!empty($appInit['cssFilesSubstitutions'])) {
            self::$cssFilesSubstitutions = (array)$appInit['cssFilesSubstitutions'];
        }
        if (isset($appInit['compileCss'])) {
            self::$compileCss = (bool)$appInit['compileCss'];
        }
        if (!empty($appInit['headJsFiles'])) {
            self::$headJsFiles = (array)$appInit['headJsFiles'];
        }
        if (!empty($appInit['firstJsFiles'])) {
            self::$firstJsFiles = (array)$appInit['firstJsFiles'];
        }
        if (!empty($appInit['lastJsFiles'])) {
            self::$lastJsFiles = (array)$appInit['lastJsFiles'];
        }
        if (!empty($appInit['jsFilesSubstitutions'])) {
            self::$jsFilesSubstitutions = (array)$appInit['jsFilesSubstitutions'];
        }
        if (!empty($appInit['jsModuleImplementation'])) {
            self::$jsModuleImplementation = (string)$appInit['jsModuleImplementation'];
        }
        if (!empty($appInit['jsModuleImportmap'])) {
            self::$jsModuleImportmap = (array)$appInit['jsModuleImportmap'];
        }
        if (isset($appInit['compileJs'])) {
            self::$compileJs = (bool)$appInit['compileJs'];
        }
        if (!empty($appInit['cryptKey'])) {
            self::$cryptKey = (string)$appInit['cryptKey'];
        }
        if (isset($appInit['obfuscateContentText'])) {
            self::$obfuscateContentText = $appInit['obfuscateContentText'];
        }
        
        // Set error reporting acording to App::errorReporting property
        self::setErrorReporting();
        
        // URL_ROOT 
        if (!defined('URL_ROOT')) {
            /**
             * URL_ROOT constant points to URL directory in which is the actual project placed.
             * It does not contain trailing slash or it is empty
             */
            define('URL_ROOT', self::$urlRoot);
        }  
        
        // url base with root
        $urlBaseWithRoot = '';
        if ($urlBase) {
            $urlBaseWithRoot = $urlBase . self::$urlRoot;
        }
        else {
            $urlBaseWithRoot = ''; // when calling App::init() from phpunit test
        }
        self::$urlBaseWithRoot = $urlBaseWithRoot;

        // PROJECT_PID
        if (!defined('PROJECT_PID')) {
            $projectRootBasename = getenv('DOCKER_WORKDIR_LOCAL_BASENAME');
            if (!$projectRootBasename) {
                $projectRootBasename = basename(ROOT);
            }
            $projectPid = $projectRootBasename;
            /**
             * PROJECT_PID
             */
            define('PROJECT_PID', $projectPid);
        }
        
        // set and normalize value debugOutput
        self::setPropertyDebugOutput(self::$debugOutput);
        
        // load firephp
        if (self::$debug || self::$debugOutput == 'fb') {
            // load original class if debug is on or if debugOutput is set to 'fb'
            require_once $vendorsDir . 'firephp' . DS . 'FirePHPCore' . DS . 'FirePHP.class.php';
            require_once $vendorsDir . 'firephp' . DS . 'FirePHPCore' . DS . 'fb.php';
        }
        else {
            // otherwise load a fake class
            require_once $vendorsDir . 'firephp' . DS . 'fakefb.php';
        }
        
        // set Debug and fbOptions and make settings round firebug
        self::setDebug(self::$debug);
        self::setPropertyFbOptions(self::$fbOptions);
                
        // undo magic_quotes
        if (
            function_exists('get_magic_quotes_gpc') 
            && get_magic_quotes_gpc()
        ) {
            $_GET = Arr::stripSlashesRecursive($_GET);
            $_POST = Arr::stripSlashesRecursive($_POST);
            $_COOKIE = Arr::stripSlashesRecursive($_COOKIE);
            $_REQUEST = Arr::stripSlashesRecursive($_REQUEST);
        }

        // MAGIC_QUOTES constant keeps actual state regardless to get_magic_quotes_gpc()
        // As the mq are undone here above so it is set to FALSE.
        // It is this constant which should be checked when you need to find out
        // magic quotes state in the project
        if (!defined('MAGIC_QUOTES')) {
            define('MAGIC_QUOTES', false);
        }
        
        // parse the incoming requests
        self::parseRequest();
        
        // Define request related constants
        // URL
        if (!defined('URL')) {
            /**
             * URL constant keeps actual request url composed from url path + url GET params
             */
            define('URL', self::$url);
        }        
        // URL_PATH
        if (!defined('URL_PATH')) {
            /**
             * URL_PATH constant keeps actual request url path (path without GET params)
             */
            define('URL_PATH', self::$urlPath);
        }       
        
        // connect to database before further processing
        self::setSqlLogging();
        // read db config
        $configFile = DS . 'app' . DS . 'config' . DS . 'database.php';
        if (!is_readable(ROOT . $configFile)) {
            $message = __e(__FILE__, 'Missing database connection config file in App module');
            echo $message;
            throw new Exception($message);
        }
        self::loadScript($configFile, array('catchVariables' => 'dbConfig'), $vars);
        if (!isset($vars['dbConfig'])) {
            $message = __e(__FILE__, 'Missing $dbConfig variable in database.php file of App module');
            echo $message;
            throw new Exception($message);
        }
        if (!isset($vars['dbConfig'][self::$dbConfigName])) {
            $message = __e(__FILE__, 'Invalid config name %s', self::$dbConfigName);
            echo $message;
            throw new Exception($message);
        }
        // connect to DB
        $dbConfig = $vars['dbConfig'][self::$dbConfigName];
        $dbConfig['install'] = !self::$isInstalled;
        DB::connect($dbConfig);
        
        // do application installation processing
        if (!self::$isInstalled) {
            self::install();
        }
        
        // init language settings
        self::initI18n();
        
        // Define i18n related constants
        // URL_LANG 
        if (!defined('URL_LANG')) {
            /**
             * URL_LANG constant keeps lang code to create links href. This can be an empty
             * string if the actual lang is default and should be hidden
             */
            define('URL_LANG', self::$urlLang);
        }       
        // DEFAULT_LANG 
        if (!defined('DEFAULT_LANG')) {
            /**
             * DEFAULT_LANG constant keeps default lang code.
             */
            define('DEFAULT_LANG', self::$langs['default']);
        }       
        // INITIAL_LANG 
        if (!defined('INITIAL_LANG')) {
            /**
             * INITIAL_LANG constant keeps the lang code the app has started with.
             */
            define('INITIAL_LANG', self::$initialLang);
        }       
        
        // Define slugs related constants
        // HOME_SLUG
        if (!defined('HOME_SLUG')) {
            /**
             * HOME_SLUG constant keeps App::$homeSlug
             */
            define('HOME_SLUG', self::$homeSlug);
        }
        // SLUG
        if (!defined('SLUG')) {
            /**
             * SLUG constant keeps incoming request slug
             */
            define('SLUG', self::$slug);
        }
        
        // load other basic classes
        require_once $defaultLibsDir . 'ModuleObject.php';
        require_once $defaultLibsDir . 'Model.php';
        require_once $defaultLibsDir . 'Controller.php';
        require_once $defaultLibsDir . 'SmartController.php';
        require_once $defaultLibsDir . 'Template.php';
        
        // set default layouts
        // - for requests form frame
        if (self::$requestSource === 'frame') {
            self::setLayout('App', 'frame');
        }
        // - for ajax requests
        elseif (self::$requestSource === 'ajax') {
            self::setLayout('App', 'ajax');
        }
        // - for admin actions
        elseif (self::$actionType === 'admin') {
            //require_once $vendorsDir . 'phpthumb' . DS . 'phpthumb.class.php';
            self::setLayout('App', 'admin');
        }
        // - for frontend home page
        elseif (self::$slug == self::$homeSlug) {
            if (is_array(self::$homeLayout)) {
                self::setLayout(self::$homeLayout['module'], self::$homeLayout['name']);
            }
            else {
                self::setLayout('App', self::$homeLayout);
            }
        }
        // - for frontend other pages
        else {
            if (is_array(self::$defaultLayout)) {
                self::setLayout(self::$defaultLayout['module'], self::$defaultLayout['name']);
            }
            else {
                self::setLayout('App', self::$defaultLayout);
            }
        }
        
        // set include paths
        // - add the libs path to include paths (Some PEAR libraries need this level, e.g. Pager)
        $includePaths = $vendorsDir;
        // - PEAR library include path
        $includePaths .= PATH_SEPARATOR . $vendorsDir . 'pear';
        // - Image_Transform PEAR package include path
        $includePaths .= PATH_SEPARATOR . $vendorsDir . 'imagetransform';
        // - add paths
        set_include_path($includePaths . PATH_SEPARATOR . get_include_path());
        
        // init session
        self::initSession();  

        // display a warning about possible cookies incompatibilities
        if (
            IN_DOCKER
            && ON_LOCALHOST
        ) {
            if (
                array_key_exists('PROJECT_PID', $_COOKIE)
                && $_COOKIE['PROJECT_PID'] !== PROJECT_PID
            ) {
                self::setMessage(
                    __d(
                        __FILE__,
                        'Je možné, že cookies nie sú kompatibilné s projektom "%s" pretože sa naposledy používali v projekte "%s". Ak by sa projekt správal chybovo, tak vymaž cookies pre doménu "%s" v konzole prehliadača. Najproblematickejšia je cookie košíka "_eshop_EshopCart_products_". Na systematické riešenie tohto problému je potrebné <a href="%s" target="_blank">spušťať projekt na lokálnej doméne</a> (u:test, h: test).',
                        PROJECT_PID,
                        $_COOKIE['PROJECT_PID'],
                        preg_replace('/:[0-9]+$/', '', $_SERVER['HTTP_HOST']),
                        'http://fajnbook.run.sk/how-to/devcontainers.html#develop-on-domain'
                    ),
                    array(
                        'modal' => true,
                    )
                );
            }
            setcookie(
                'PROJECT_PID', 
                PROJECT_PID, 
                time() + 60 * 60 * 24 * 365, 
                '/',
                '',
                false,
                // set session cookie to be HttpOnly to avoid its access from JS
                // and so avoid XSS attack trying to get to session id
                true 
            );
        }
        
        // actualize user data
        self::reinitUser();
        
        // write here your own code
        // ...
        // load eshop module class and init eshop module - @todo refactor this according todo @150612
        require_once ROOT . DS . 'app' . DS . 'modules' . DS . 'Eshop' . DS . 'libs' . DS . 'default' . DS . 'Eshop.php';
        Eshop::init();
        // load Helpdesk module class and init Helpdesk module
        if (ON_LOCALHOST) {
            require_once ROOT . DS . 'app' . DS . 'modules' . DS . 'Helpdesk' . DS . 'libs' . DS . 'default' . DS . 'Helpdesk.php';
            Helpdesk::init();
        }
        // load Logger module class and init Logger module
        if (ON_LOCALHOST) {
            require_once ROOT . DS . 'app' . DS . 'modules' . DS . 'Logger' . DS . 'libs' . DS . 'default' . DS . 'Logger.php';
            Logger::init();
        }
        // load Music module class and init Music module
        if (ON_LOCALHOST) {
            require_once ROOT . DS . 'app' . DS . 'modules' . DS . 'Music' . DS . 'libs' . DS . 'default' . DS . 'Music.php';
            Music::init();
        }
        // load Game module class and init Game module
        if (ON_LOCALHOST) {
            require_once ROOT . DS . 'app' . DS . 'modules' . DS . 'Game' . DS . 'libs' . DS . 'default' . DS . 'Game.php';
            Game::init();
        }
        
        // !!! ALTEREGO SPECIFIC
        self::redirectOldToNewUrl();
        
        // do warning about low error reporting level on localhost (to avoid situation 
        // when many NOTICEs are let unresolved because of forgotten turned off errorReporting)
        if (
            ON_LOCALHOST
            && (
                !(self::$errorReporting & E_PARSE)
                || !(self::$errorReporting & E_ERROR)
                || !(self::$errorReporting & E_WARNING)
                || !(self::$errorReporting & E_NOTICE)
            )
        ) {
            $message = __e(
                __FILE__, 
                'ATTENTION: Error reporting on localhost is either turned off or constrained! Please set it at least to E_ALL & ~E_STRICT & ~E_DEPRECATED'
            );
            if (self::$debug) {
                self::debug($message, $message);
            }
            else {
                self::setWarningMessage($message);
            }
        }

        // set canonical URL for actual app URL
        // To set more precise form of canonical URL in concrete context
        // you can (re)use App::setSeoCanonicalUrl() on concrete place in the project
        self::loadLib('App', 'Paginator');
        $Paginator = new Paginator();
        self::setSeoCanonicalUrl(self::$parsedUrl, array(
            // inherit actual url options except of following
            'inheritExcept' => array(
                'args', 'params', 'get', 'anchor'
            ),
            // but force inheritance in following cases:
            'inherit' => array(
                // inherit possible non-web-content (e.g. product, category, ...)
                // slug (args.0)
                'args.0',
                // inherit pagination
                'get.' . $Paginator->getPropertyPageUrlParam(),
                // inherit search keywords
                'get.data.keywords',
            )
        ));

        // keep track that the initialization has been made already
        self::$isInitialized = true;
    }
    
    /**
     * Redirects from old (160913) vydavatel.sk URLs:
     * 
     *      /produkty/popularno-naucna-pre-deti/page:1/sort:product_name/direction:asc/onStock:1/lang:slovak
     * 
     * to new ones:
     * 
     *      /produkty/popularno-naucna-pre-deti?page=1&sort[name]=ASC&filter[stock]=>0&filter[language]=slovak
     */
    static protected function redirectOldToNewUrl() {
        $changed = false;
        $url = self::$parsedUrl;
        // redirect bots coming on old lowercased URLS to 404 screen
        if (
            $url['type'] === 'mvc'
            && (
                empty($url['module'])
                ||
                substr($url['module'], 0, 1) > 'Z'
            )
        ) {
            self::redirect('/_404');
        }
        // redirected if slug was changed
        if (!empty(self::$urlPath)) {
            self::loadModel('App', 'UrlRedirection');
            $UrlRedirection = new UrlRedirection();
            $UrlRedirection->check(self::$urlPath);
        }
    }
    
    /**
     * Change this property to last modification timestamp of App::install() to
     * force automatic reinstall
     *
     * @var string 
     */
    static protected $installModificationDatetime = '2015-12-02 08:52:00';
    
    /**
     * Installs the application, means this method does some processing
     * which sould be done on the very first launch of application, like:
     * - creation of DB tables which existence is required by app (todo)
     * - regenerates cryptKey' in App config.php (todo)
     */
    static protected function install() {
        if (self::$isInstalled) {
            return;
        }
        
        // ensure userfiles/ and its subfolders
        File::ensurePath('/userfiles');
        File::ensurePath('/userfiles/files');
        File::ensurePath('/userfiles/files/import');
        File::ensurePath('/userfiles/files/import/images');
        File::ensurePath('/userfiles/images');
        // ensure tmp/ and its subfolders
        File::ensurePath('/tmp/sessions');
        File::ensurePath('/tmp/sessions/permanent');
        File::ensurePath('/tmp/logs');
        File::ensurePath('/tmp/swiftmailer');
        // create htaccess file under tmp/
        file_put_contents(
            TMP . DS . '.htaccess',
            'Order Allow,Deny' . PHP_EOL .
            'Allow from none' . PHP_EOL .
            'Deny from all'
        );
        // include default.hgrc into .hg/hgrc
        $defaultHgrcFile = ROOT . DS . 'default.hgrc';
        $hgrcFile = ROOT . DS . '.hg' . DS . 'hgrc';
        if (
            is_readable($defaultHgrcFile)
            && is_readable($hgrcFile)
            && is_writable($hgrcFile)
        ) {
            $hgrcContent = file_get_contents($hgrcFile);
            $hgrcInclude = '%include ..' . DS . 'default.hgrc';
            if (strpos($hgrcContent, $hgrcInclude) === false) {
                $hgrcContent .= PHP_EOL . '### autogenerated include by App::install() ###' . PHP_EOL . $hgrcInclude;
                file_put_contents($hgrcFile, $hgrcContent);
            }
        }
        
        // create install flag file
        file_put_contents(
            self::$isInstalledFlagFilepath, 
            'This file marks that application installation has been already done. If you will delete it, application will repeat install processing.'
        );
    }
    
    /**
     * Inits session
     * 
     * ATTENTION: If the session is already initialized then this method will do nothing.
     * Use session_write_close() before calling this method if session_id() is already 
     * nonempty - it mean if session is already initialized. If you get session initialized 
     * at very beginning of app then make sure your php configuration php.ini has 
     * this configured like: 'session.auto_start = 0' or you can add: 'php_flag session.auto_start 0' 
     * to your .htaccess file
     * 
     * @param $permanent Optional. If TRUE then permanent session is initialized with
     *      lifetime defined by App::$permanentSessionLifetime (but only if permanent 
     *      session functionality is available). If FALSE the normal session is initialized 
     *      with lifetime of browser session. If NULL then autodetection of actually used 
     *      session type is made. Defaults to NULL.
     */
    static protected function initSession($permanent = null) {
////allow session re-initialization        
//        // if session has been already initialized then do nothing
//        if (session_id()) {
//            return;
//        }
        // autodetect
        if ($permanent === null) {
            $permanent = self::hasPermanentSession();
        }
        // check for availabilit of permanent session
        $permanent = $permanent && self::hasPermanentSessionAvailable();
        // init
        ini_set('session.use_trans_sid', 0);
        ini_set('url_rewriter.tags', '');
        ini_set('session.serialize_handler', 'php');
        ini_set('session.use_cookies', 1);
        ini_set('session.name', self::$sessionCookieName);
        ini_set('session.cookie_path', '/');
        ini_set('session.auto_start', 0);        
        // init permanent session
        if ($permanent) {
            ini_set('session.save_path', TMP . DS . 'sessions' . DS . 'permanent');
            ini_set('session.cookie_lifetime', self::$permanentSessionLifetime);
            ini_set('session.gc_maxlifetime', self::$permanentSessionLifetime);
            // 10% chance to run GC on session_start() call
            //ini_set('session.gc_divisor', 10); 
            //ini_set('session.gc_divisor', 1); //debug - run the gc on each session start 
        } 
        // init normal session
        else {            
            ini_set('session.save_path', TMP . DS . 'sessions');
            ini_set('session.cookie_lifetime', 0);
            //ini_set('session.gc_divisor', 1); //debug - run the gc on each session start 
        }
        // start session (hide the warning which is triggered in case of permanent 
        // session when the session cookie is lost because of its expiration)
        @session_start();  
        if ($permanent) {
            // set lifetime of permanent session cookie and update it after each access of website
            // (see http://php.net/manual/en/function.session-set-cookie-params.php#100657)
            setcookie(
                self::$sessionCookieName, 
                session_id(), 
                time() + self::$permanentSessionLifetime, 
                '/',
                '',
                false,
                // set session cookie to be HttpOnly to avoid its access from JS
                // and so avoid XSS attack trying to get to session id
                true 
            );
        }
        else {
            // set lifetime of normal session cookie to default value = cookie expire with session
            setcookie(
                self::$sessionCookieName, 
                session_id(), 
                0, 
                '/',
                '',
                false,
                // set session cookie to be HttpOnly to avoid its access from JS
                // and so avoid XSS attack trying to get to session id
                true
            );
        }
    }
    
    /**
     * Starts permanent session (if functionality is available)
     */
    static protected function startPermanentSession() {
        // close session and move session file to folder with sessions of permanent logins
        $sessionId = session_id();
        session_write_close();
        $sessionFile = self::getSessionFile($sessionId);
        $permanentSessionFile = self::getSessionFile($sessionId, true);
        // there are following cases when cookie files will not exist:
        // - if session id is tweaked (e.g. manually in browser console)
        // - if it is not allowed to set 'session.save_path' programmatically by ini_set()
        if (is_writable($sessionFile)) {
            copy(
                $sessionFile, 
                $permanentSessionFile
            );
        }
        // init permanent session
        self::initSession(true);
        // remove old session file
        if (is_writable($sessionFile)) {
            unlink($sessionFile);
        }
    }
    
    /**
     * Has the application started permanent session?
     * 
     * @return bool
     */
    static public function hasPermanentSession() {
        // do not use here session_name() as the method can be called before the session
        // is started and so session_name() will return nothing
        if (empty($_COOKIE[self::$sessionCookieName])) {
            return false;
        }
        return self::hasSessionFile($_COOKIE[self::$sessionCookieName], true);
    }
    
    /**
     * Is the permanent session functionality available?
     * The permanent session functionality is available if the normal session file
     * exists already (there is session id cookie) and if it is possible to set 'session.save_path' 
     * programmatically by ini_set().
     * 
     * The 'session.save_path' cannot be set programmatically by ini_set() e.g. 
     * if apache directive php_admin_value[session.save_path] is set to explicit path. 
     * To allow it use: php_admin_value[session.save_path] = php_value
     * 
     * Returns FALSE also on very first call when no session file exists yet (there 
     * is no session id cookie yet).
     * 
     * @return bool
     */
    static public function hasPermanentSessionAvailable() {
        // do not use here session_name() as the method can be called before the session
        // is started and so session_name() will return nothing
        if (empty($_COOKIE[self::$sessionCookieName])) {
            return false;
        }
        return self::hasSessionFile($_COOKIE[self::$sessionCookieName]);
    }
    
    /**
     * Returns absolute path of session file for specified session id.
     * This method does not checks for existence of file.
     * 
     * @param string $sessionId Session id to get the session file for
     * @param bool $permanent Optional. If TRUE then path to permanent session file
     *      is returned. If FALSE then path to plain session file is returned. Defaults to FALSE.
     * 
     * @return string Absolute path for session file
     */
    static protected function getSessionFile($sessionId, $permanent = false) {
        if ($permanent) {
            return TMP . DS . 'sessions' . DS . 'permanent' . DS . 'sess_' . $sessionId;
        }
        else {
            return TMP . DS . 'sessions' . DS . 'sess_' . $sessionId;
        }
    }
        
    /**
     * Has the application session file for provided session id?
     * 
     * @param string $sessionId Session id to check for existence of session file
     * @param bool $permanent Optional. If TRUE then it is checked for existence of permanent
     *      session file. If FALSE then it is checked for existence of plain session file.
     *      If not provided (NULL) then both possibilities are checked. Defaults to NULL.
     * 
     * @return bool
     */
    static public function hasSessionFile($sessionId, $permanent = null) {
        if ($permanent === true) {
            return is_writable(self::getSessionFile($sessionId, true));
        }
        elseif ($permanent === false) {
            return is_writable(self::getSessionFile($sessionId));
        }
        return is_writable(self::getSessionFile($sessionId)) 
            || is_writable(self::getSessionFile($sessionId, true));
    }
    
    /**
     * Closes permanent session (if any and functionality is available)
     */
    static protected function closePermanentSession() {
        // close session and move session file to folder with sessions of normal logins
        $sessionId = session_id();
        session_write_close();
        $sessionFile = self::getSessionFile($sessionId);
        $permanentSessionFile = self::getSessionFile($sessionId, true);
        // there are following cases when cookie files will not exist:
        // - if session id is tweaked (e.g. manually in browser console)
        // - if it is not allowed to set 'session.save_path' programmatically by ini_set()
        if (is_writable($permanentSessionFile)) {
            copy(
                $permanentSessionFile, 
                $sessionFile
            );
        }
        // init normal session
        self::initSession(false);
        // remove old session file
        if (is_writable($permanentSessionFile)) {
            unlink($permanentSessionFile);
        }
    }
    
    /**
     * This function is executed on the application shutdown (means after script 
     * execution finishes or exit() is called)
     * Put here all necessary code to be done on that moment.
     * 
     * NOTE: 
     * This function must be public for sake of registering by register_shutdown_function()
     * 
     * @return void
     */
    static public function shutdown() {
        
        // close all file handles
        foreach (self::$fileHandles as $modeHandles) {
            foreach ($modeHandles as $handle) {
                if (is_resource($handle)) {
                    fclose($handle);
                }
            }
        }
        
        // close curl handle and delete its cookie file 
        if (!empty(self::$requestHandle)) {
            curl_close(self::$requestHandle);
            self::$requestHandle = null;
        }
        if (
            !empty(self::$requestHandleCookieFile)
            && file_exists(self::$requestHandleCookieFile)
        ) {
            @unlink(self::$requestHandleCookieFile);
            self::$requestHandleCookieFile = null;
        }
        
        // close and save session
		if (function_exists('session_write_close')) {
			session_write_close();
		}
        
        // check for invalid capture endings
        if (self::$openedCssCapturesCount > 0) {
            $message = __e(__FILE__, 'Missing end of css capture block');
            self::debug($message, $message);
            self::logError($message, array(
                'email' => true,
            ));
        }
        if (self::$openedJsCapturesCount > 0) {
            $message = __e(__FILE__, 'Missing end of js capture block');
            self::debug($message, $message);
            self::logError($message, array(
                'email' => true,
            ));
        }
        if (self::$openedHeadHtmlCapturesCount > 0) {
            $message = __e(__FILE__, 'Missing end of html head capture block');
            self::debug($message, $message);
            self::logError($message, array(
                'email' => true,
            ));
        }
        if (self::$openedBodyEndHtmlCapturesCount > 0) {
            $message = __e(__FILE__, 'Missing end of html body end capture block');
            self::debug($message, $message);
            self::logError($message, array(
                'email' => true,
            ));
        }
        if (Html::getPropertyOpenedCapturesCount() > 0) {
            $message = __e(__FILE__, 'Missing end of html capture block');
            self::debug($message, $message);
            self::logError($message, array(
                'email' => true,
            ));
        }
        
        // here write your own code
        // ...
        
        // catch errors & warnings
        self::catchError();
    }
    
    /**
     * Gets php raw input reading content of php://input.
     * 
     * Can be used e.g. to read PUT data
     * 
     * @return string
     */
    static public function getRawInput() {
        $rawInput  = '';
        $fh = fopen('php://input', 'r');
        while ($kb = fread($fh, 1024)) {
            $rawInput .= $kb;
        }
        fclose($fh);
        return $rawInput;
        // alternative version of method body:
        //$rawInput  = file_get_contents("php://input");
        //return $rawInput;
    }
    
    /**
     * Parses the incoming request and populates request properties
     */
    static protected function parseRequest() {
        self::$initialLang = null;
        self::$requestMethod = strtoupper(Sanitize::value($_SERVER['REQUEST_METHOD']));
        self::$requestType = null;
        self::$requestSource = null;
        self::$slug = null;
        self::$module = null;
        self::$controller = null;
        self::$action = null;
        self::$actionType = null;
        self::$data = array();
        self::$params = array();
        self::$args = array();
        // treat PUT method (used probably by ExtJS on RESTfull requests)
        if (self::$requestMethod === 'PUT') {
            if (($rawInput  = self::getRawInput())) {
                $parsedInput = array();
                parse_str($rawInput, $parsedInput);
                if (!empty($parsedInput['data'])) {
                    if (is_array($parsedInput['data'])) {
                        self::$data = $parsedInput['data'];
                    }
                    // check for json encoded data
                    elseif (($parsedInputData = json_decode($parsedInput['data'], true))) {
                        self::$data =  $parsedInputData;
                    }
                } 
                // check for json encoded data
                elseif (
                    ($parsedInput = json_decode($rawInput, true))
                    && !empty($parsedInput['data'])
                ) {
                    self::$data = $parsedInput['data'];
                }
            }
        } 
        // treat POST, GET and DELETE
        else {
            // grab array data
            if (!empty($_REQUEST['data'])) {
                if (is_array($_REQUEST['data'])) {
                    self::$data = $_REQUEST['data'];
                }
                // check for json encoded data
                elseif (($parsedInputData = json_decode($_REQUEST['data'], true))) {
                    self::$data =  $parsedInputData;
                }
            } 
            // check for json encoded data
            elseif (
                ($rawInput = self::getRawInput())
                && ($parsedInput = json_decode($rawInput, true))
                && !empty($parsedInput['data'])
            ) {
                self::$data = $parsedInput['data'];
            }
        }
        // look for data in $_FILES (three nested levels are examined)
        if (isset($_FILES['data'])) {
            foreach ($_FILES['data'] as $uploadKey => $fields1) {
                foreach ($fields1 as $field1 => $fields2) {
                    if (is_array($fields2)) {
                        foreach ($fields2 as $field2 => $fields3) {
                            if (is_array($fields3)) {
                                foreach ($fields3 as $field3 => $value) {
                                    self::$data[$field1][$field2][$field3][$uploadKey] = $value;
                                }
                            } 
                            else {
                                self::$data[$field1][$field2][$uploadKey] = $fields3;
                            }
                        }
                    } 
                    else {
                        self::$data[$field1][$uploadKey] = $fields2;
                    }
                }
            }
        }
        // parse url
        //if (isset($_SERVER['PATH_INFO'])){ // seems that PATH_INFO does not work on all servers
        if (
            isset($_GET['_urlPath_']) 
            && $_GET['_urlPath_'] !== ''
        ) {
            $urlPath = $_GET['_urlPath_'];
            // remove artificially added GET param
            unset($_GET['_urlPath_']);
        }
        else {
            $urlPath = '/';
        }
        self::$parsedUrl = self::parseUrl($urlPath, array( // there are no GET params in _urlPath_
            'localizedLang' => self::$localizedLang,
            'autodetectLang' => self::$autodetectLang,
        ));
        // populate get params of parsed url from $_GET array
        self::$parsedUrl['get'] = $_GET;
        self::$urlPath = self::$parsedUrl['path'];
        self::$initialLang = self::$parsedUrl['lang'];
        self::$requestType = self::$parsedUrl['type'];
        if (self::$requestType == 'mvc') {
            // check if mvc request is formally valid
            if (empty(self::$parsedUrl['module'])) {
                throw new Exception ("Invalid request. Module name is not provided");
            }
            if (empty(self::$parsedUrl['controller'])) {
                throw new Exception ("Invalid request. Controllers name is not provided");
            }
            if (empty(self::$parsedUrl['action'])) {
                throw new Exception ("Invalid request. Action name is not provided");
            }
            // populate mvc request properties
            self::$module = self::$parsedUrl['module'];
            self::$controller = self::$parsedUrl['controller'];
            self::$action = self::$parsedUrl['action'];
            self::$actionType = self::$parsedUrl['actionType'];
        }
        else {
            // set slug 
            self::$slug = self::$parsedUrl['slug'];
            if (empty(self::$slug)) {
                self::$slug = self::$homeSlug;
            }
        }
        // populate params
        self::$params = self::$parsedUrl['params'];
        self::$args = self::$parsedUrl['args'];
        // resolve request source
        if (!empty($_GET['_requestSource_'])){
            self::$requestSource = (string)$_GET['_requestSource_'];
        }

        self::$url = Sanitize::value($_SERVER['REQUEST_URI']);
        if (empty(self::$url)) {
            self::$url = '/';
        }
    }
    
    /**
     * Makes HTTP request using curl.
     * NOTE: See also functions file_get_contets() and get_headers()
     * 
     * NOTE: For the difference between POST and PUT see: 
     * http://jcalcote.wordpress.com/2008/10/16/put-or-post-the-rest-of-the-story/
     * 
     * NOTE: How to get response headers without mixing them with response body: 
     * http://stackoverflow.com/questions/9183178/can-php-curl-retrieve-response-headers-and-body-in-a-single-request
     * 
     * @param string|array $url Request URL provided in the same way as for App:getUrl().
     *      It means that params, args and get parameters can be specified here.
     * @param array $options Following can be used:
     *      - 'method' (string) One of 'GET', 'POST', 'PUT', 'DELETE'. On invalid method
     *          an exceptions is thrown. Defaults to 'GET'.
     *      - 'persist' (bool) If TRUE then cURL handle is not closed after request is done
     *          but it persist for next request(s). This allows profit from SESSION
     *          shared between the requests and also to save processing time if many 
     *          requests are launched. The first request launged with 'persist'
     *          set to FALSE will reset the cURL handle. Defaults to FALSE.
     *      - 'timeout' (int|float) Integer or float number of seconds to wait for response. 
     *          If empty then no timeout is set and the method waits for response such long 
     *          as needed. The shortest timeout is 0.001 (1ms). ATTENTION: To launch
     *          nonblocking (quasi "async") request use small timeout (cca 0.5-1s) and set
     *          'returnResponse' to FALSE. But be aware that very short timeouts 
     *          can cause exception if the DNS host lookup is not achieved in provided time
     *          (see https://segment.com/blog/how-to-make-async-requests-in-php/ or
     *          https://stackoverflow.com/q/14359926/1245149)! If the 'returnResponse' is TRUE
     *          then an Exception_App_RequestFailure with code 28 is thrown. Defaults to NULL.
     *      - 'params' (array) Array of App::$params parameters. Notice that params can
     *          be defined also by $url['params']. If option 'mergeParams' is TRUE then $url['params'] 
     *          are merged with $options['params']. Othervise $url['params'] are replaced 
     *          by $options['params'].
     *      - 'args' (array) Array of App::$args parameters. Notice that args can
     *          be defined also by $url['args']. If option 'mergeArgs' is TRUE then $url['args'] 
     *          are merged with $options['args']. Othervise $url['args'] are replaced 
     *          by $options['args'].
     *      - 'get' (array) Array of GET parameters. Notice that GET parameters can
     *          be defined also by $url['get']. If option 'mergeGet' is TRUE then $url['get'] 
     *          are merged with $options['get']. Othervise $url['get'] are replaced 
     *          by $options['get'].
     *      - 'data' (string|array|bool) String of POST, PUT or DELETE data. They are considered only 
     *          when the 'method' is set to 'POST', 'PUT' or 'DELETE'. Normally you should provide 
     *          it using http_build_query($myData). In such a case the 'Content-Type' 
     *          header must be set to 'application/x-www-form-urlencoded' (but if 'Content-Type'
     *          is not specified in 'header' option then this is set automatically).
     *          Except of usual query string you can pass here any kind of string data
     *          (e.g. XML or JSON string). In such a case do not forget set the 'Content-Type: ...'
     *          header field accordingly. ATTENTION: If provided as array in case of PUT and DELETE
     *          then it is internally converted to string by http_build_query(). If provided as array 
     *          in case of POST and there is no explicit 'Content-Type' header other
     *          than 'multipart/form-data' then it is considered for file upload and the 'Content-Type' header 
     *          will be set to 'multipart/form-data' (if not specified by 'header' option yet).
     *          The data are sent to server in multipart form (using boundaries). 
     *          To upload a file use POST method and 'data' set to array(..., 'my_file' => '@/path/to/my_file.png', ...). 
     *          If no data should be send then set to FALSE. Defaults to FALSE.
     *      - 'header' (array|string) An array of HTTP request header fields e.g. 
     *          array('Content-type: text/plain', 'Content-length: 100') or a single
     *          field string e.g. 'Content-Type: text/xml'. Defaults to NULL
     *      - 'userAgent' (string) The contents of the "User-Agent: " header to be used 
     *          in a HTTP request. See http://www.useragentstring.com for real browsers 
     *          useragent strings. If any empty value then it is not set. Defaults to 
     *          'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.124 Safari/537.36'.
     *      - 'mergeParams' (bool) If TRUE then $options['params'] are merged with
     *          existing $url['params']. Defaults to TRUE.
     *      - 'mergeArgs' (bool) If TRUE then $options['args'] are merged with
     *          existing $url['args']. Defaults to TRUE.
     *      - 'mergeGet' (bool) If TRUE then $options['get'] are merged with
     *          existing $url['get']. Defaults to TRUE.
     *      - 'returnResponse' (bool) If TRUE then the method returns request result.
     *          If FALSE then the method returns only TRUE/FALSE. Defaults to TRUE.
     *      - 'includeHeader' (bool) If TRUE then header is included in the output response.
     *          Defaults to FALSE.
     *      - 'includeBody' (bool) If TRUE then body is included in the output response.
     *          Defaults to TRUE.
     *      - 'getInfo' (bool|int) If TRUE or one of CURLINFO_??? constants - see
     *          curl_getinfo() - then request info is returned in $info aux output.
     *          Defaults to FALSE.
     *      - 'logFile' (string|resource) App root relative file path to save the request log 
     *          into or a file handle resource returned by fopen(). 
     *          If provided by file path then file is opened internally and if it 
     *          exists already a new log is appended at the end. To make this work
     *          the 'getInfo' option must be FALSE. Defaults to NULL.
     *      - 'followLocation' (bool) If TRUE then any "Location: " header that 
     *          the server sends as part of the HTTP header is followed (recursively).
     *          Defaults to TRUE.
     *      - 'outputFile' (string|resource) App root relative file path to save 
     *          the request output into or a file handle resource returned by fopen(). 
     *          If provided by file path then file is opened internally and if it 
     *          exists already it is rewriten. Defaults to NULL. 
     *      - 'headersReader' (callable) Callable or anonymous function which is
     *          called for each of response headers items. It gets cURL handle and 
     *          response header item string on its input and must return the length
     *          of header item string like strlen($headerItem). It is called for 
     *          each header item separately. Defaults to NULL.
     * @param mixed& $info passed by reference. Auxiliary output used in case that 
     *          option 'getInfo' is set. If 'getInfo' is TRUE then $info is array()
     *          of all curl info options. If If 'getInfo' is one of CURLINFO_??? constants
     *          then $info has value of specified curl info option. See function curl_getinfo()
     *          for list of all curl info options.
     * 
     * @return string|bool The request response string or FALSE on failure. If option
     *      'returnResponse' is FALSE or if response is writen to 'outputFile' then 
     *       only TRUE/FALSE values are returned.
     * 
     * @throws Exception on invalid options
     * @throws Exception_App_RequestFailure on http request (cURL) error
     */
    static public function request($url, $options = array(), &$info = null) {
        // params, args, get must no be set explicitly in defaults
        $defaults = array(
            'method' => 'get',
            'persist' => false,
            'timeout' => null,
            'data' => false,
            'header' => null,
            'userAgent' => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.124 Safari/537.36',
            'mergeParams' => true,
            'mergeArgs' => true,
            'mergeGet' => true,
            'returnResponse' => true,
            'includeHeader' => false,
            'includeBody' => true,
            'getInfo' => false,
            'logFile' => null,
            'followLocation' => true,
            'outputFile' => null,
            'headersReader' => null,
        );
        $options = array_merge($defaults, $options);
        // normalize and validate options
        $options['method'] = strtoupper($options['method']);
        if (
            $options['method'] !== 'GET'
            && $options['method'] !== 'POST'
            && $options['method'] !== 'PUT'
            && $options['method'] !== 'DELETE'
        ) {
            throw new Exception(__e(__FILE__, 'Invalid request method "%s"', $options['method']));
        }
        if (empty($options['data'])) {
            $options['data'] = false;
        }
        $options['header'] = (array)$options['header'];
        $explicitContentType = null;
        foreach($options['header'] as $header) {
            $match = array();
            if (preg_match('/^\s*Content-type\s*:(.+)$/', $header, $match)) {
                $explicitContentType = strtolower(trim($match[1]));
            }
        }
        if (
            is_array($options['data'])
            && (
                $options['method'] === 'PUT' 
                ||
                $options['method'] === 'DELETE' 
                ||
                $options['method'] === 'POST'
                && $explicitContentType
                && $explicitContentType !== 'multipart/form-data'
            )
        ) {
            $options['data'] = http_build_query($options['data']);
        }
        $logFileHandle = null;
        if (!empty($options['logFile'])) {
            if (is_string($options['logFile'])) {
                $logFileHandle = fopen(ROOT . DS . File::normalizePath($options['logFile']), 'a');
            }
            elseif (is_resource($options['logFile'])) {
                if (($resourceType = get_resource_type($options['logFile'])) !== 'stream') {
                    throw new Exception(__e(__FILE__, 'Invalid log file resource "%s" provided. Provide file resource.', $resourceType));
                }
                $logFileHandle = $options['logFile'];
            }
            else {
                throw new Exception(__e(__FILE__, 'Invalid log file "%s" provided', $options['logFile']));
            }
            if (empty($logFileHandle)) {
                throw new Exception(__e(__FILE__, 'Invalid log file handle'));
            }
        }
        $outputFileHandle = null;
        if (!empty($options['outputFile'])) {
            if (is_string($options['outputFile'])) {
                $outputFileHandle = fopen(ROOT . DS . File::normalizePath($options['outputFile']), 'w');
            }
            elseif (is_resource($options['outputFile'])) {
                if (($resourceType = get_resource_type($options['outputFile'])) !== 'stream') {
                    throw new Exception(__e(__FILE__, 'Invalid output file resource "%s" provided. Provide file resource.', $resourceType));
                }
                $outputFileHandle = $options['outputFile'];
            }
            else {
                throw new Exception(__e(__FILE__, 'Invalid output file "%s" provided', $options['outputFile']));
            }
            if (empty($outputFileHandle)) {
                throw new Exception(__e(__FILE__, 'Invalid output file handle'));
            }
        }
        // validate method
        // make the request
        if (empty(self::$requestHandle)) {
            self::$requestHandle = curl_init();
            self::$requestHandleCookieFile = TMP . DS . 'cookie_' . Str::getRandom(10, 'abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ') . '.txt';
            curl_setopt(self::$requestHandle, CURLOPT_COOKIEJAR, self::$requestHandleCookieFile);
        }
        // if params, args or get are provided then use App::rebuildUrl() to get url
        if (
            isset($options['params'])
            || isset($options['args'])
            || isset($options['get'])
        ) {
            // force outputType
            $options['outputType'] = 'string';
            curl_setopt(self::$requestHandle, CURLOPT_URL, self::rebuildUrl($url, $options));
        }
        else {
            curl_setopt(self::$requestHandle, CURLOPT_URL, self::getUrl($url, $options));
        }
        curl_setopt(self::$requestHandle, CURLOPT_HEADER, $options['includeHeader']);
        curl_setopt(self::$requestHandle, CURLOPT_NOBODY, empty($options['includeBody']));
        curl_setopt(self::$requestHandle, CURLOPT_FOLLOWLOCATION, $options['followLocation']); 
        curl_setopt(self::$requestHandle, CURLOPT_RETURNTRANSFER, $options['returnResponse']);
        if (!empty($options['userAgent'])) {
            curl_setopt(self::$requestHandle, CURLOPT_USERAGENT, $options['userAgent']);
        }
        curl_setopt(self::$requestHandle, CURLOPT_CUSTOMREQUEST, $options['method']);
        if (
            $options['method'] === 'POST'
            || $options['method'] === 'PUT'
            || $options['method'] === 'DELETE'
        ) {
            curl_setopt(self::$requestHandle, CURLOPT_POSTFIELDS, $options['data']);
        }
        if (!empty($options['header'])) {
            curl_setopt(self::$requestHandle, CURLOPT_HTTPHEADER, (array)$options['header']);
        }
        if (!empty($options['timeout'])) {
            if (Validate::intNumber($options['timeout'])) {
                // delay ???s
                curl_setopt(self::$requestHandle, CURLOPT_TIMEOUT, (int)$options['timeout']); 
            }
            elseif (PHP_VERSION_ID >= 50200) {
                // delay ???ms - works only from PHP 5.2, and cURL 1.7
                curl_setopt(self::$requestHandle, CURLOPT_TIMEOUT_MS, (int)(1000 * (float)$options['timeout'])); 
            }
            else {
                // fallback delay converted from float to seconds
                curl_setopt(self::$requestHandle, CURLOPT_TIMEOUT, (int)ceil((float)$options['timeout'])); 
            }
        }
        if (!empty($logFileHandle)) {
            curl_setopt(self::$requestHandle, CURLOPT_VERBOSE, true);
            curl_setopt(self::$requestHandle, CURLOPT_STDERR, $logFileHandle);
        }
        if (!empty($outputFileHandle)) {
            curl_setopt(self::$requestHandle, CURLOPT_FILE, $outputFileHandle);
        }
        if (Validate::callableFunction($options['headersReader'])) {
            curl_setopt(self::$requestHandle, CURLOPT_HEADERFUNCTION, $options['headersReader']);
        }
        // bypass SSL certificate validation
        curl_setopt(self::$requestHandle, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt(self::$requestHandle, CURLOPT_SSL_VERIFYPEER, false);
        // CURLINFO_HEADER_OUT must be set in specific way, not by curl_getinfo()
        if ($options['getInfo'] === CURLINFO_HEADER_OUT) {
            curl_setopt(self::$requestHandle, CURLINFO_HEADER_OUT, true);
        }
        
        ob_start();
        $response = curl_exec(self::$requestHandle);
        ob_end_clean();
        if (curl_errno(self::$requestHandle)) { 
            $errno = curl_errno(self::$requestHandle); 
            $error = curl_error(self::$requestHandle); 
            // close the handle only if nonpersistent request is done
            if (!$options['persist']) {
                curl_close(self::$requestHandle);
                self::$requestHandle = null;
                if (is_string($options['logFile'])) {
                    fclose($logFileHandle);
                }
                if (is_string($options['outputFile'])) {
                    fclose($outputFileHandle);
                }
            }
            // quasi "async" request specified by 'timeout' and 'returnResponse'set to FALSE
            if (
                !empty($options['timeout'])
                && empty($options['returnResponse'])
                && (int)$errno === 28
                // detect the message "Operation timed out after XXX milliseconds with 0 bytes received"
                && strtolower(substr($error, 0, 25)) === 'operation timed out after'
            ) {
                return true;
            }
            throw new Exception_App_RequestFailure($error, $errno);
        }
        if ($options['getInfo'] === true) {
            $info = curl_getinfo(self::$requestHandle);
        }
        elseif (!empty($options['getInfo'])) {
            $info = curl_getinfo(self::$requestHandle, $options['getInfo']);
        }
        // close the handle and delete its cookie file only if nonpersistent request is done
        if (empty($options['persist'])) {
            curl_close(self::$requestHandle);
            self::$requestHandle = null;
            if (is_string($options['logFile'])) {
                fclose($logFileHandle);
            }
            if (is_string($options['outputFile'])) {
                fclose($outputFileHandle);
            }
            if (file_exists(self::$requestHandleCookieFile)) {
                @unlink(self::$requestHandleCookieFile);
            }
            self::$requestHandleCookieFile = null;
        }
        return $response;
    }

    /**
     * Parses urls like:
     *      - mvc request: [base][/{root}][/{lang}]/mvc/{module}/{controller}/{action}[/{arg01}[/...[/{param01}:{value}[/...]]]]]
     *      - slug request: [base][/{root}][/{lang}][/{slug}[/{arg01}[/...[/{param01}:{value}[/...]]]]]]
     * 
     * For example:
     *      [http://my-site.sk][/myApp][/sk]/mvc/MyModule/MyController/myAction[/arg01[/param01:xyz[?getParam01=abc]]]
     *      [http://my-site.sk][/myApp][/en][/mySlug[/arg01[/param01:xyz[?getParam01=abc]]]]
     * 
     * @param string $url
     * @param array $options Optional. Array containing following possible options:
     *      - 'hasGetParams' (bool) If TRUE url is considered to have GET params attached
     *          and is processed accordingly. Defaults to TRUE.
     *      - 'parseGetParams' (bool) If TRUE GET params in url are parsed. This applies 
     *          only when $hasGetParams is set to TRUE. Defaults to FALSE.
     *      - 'localizedLang' (bool) If TRUE then url is considered to contain 
     *          lang in localized form, means country code / language code, e.g. 'us/en'. 
     *          If FALSE the the lang is just lang code, e.g. 'en'. Defaults to FALSE.
     *      - 'autodetectLang' (bool) If TRUE then autodetection of language
     *          is turned on and even incomplete localized lang will be grabbed for 
     *          further processing. It means that in url 'us/my-page' the 'us' will 
     *          be grabbed as lang. This is considered only when 'localizedLang' option 
     *          is TRUE. Returned 'lang' may be incomplete and will be finally set by 
     *          App::initI18n() method.
     *      - 'base' (string) Url base explicit definition. In case that provided url
     *          has its own defined base then this is ignored. It does not contain 
     *          trailing slash or it is just empty. Defaults to App::$urlBase.
     *      - 'root' (string) Url root explicit definition. This is path to project root
     *          included in url (normaly it is all hidden in url base (schema + host).
     *          It does not contain trailing slash or it is just empty. Defaults to URL_ROOT.
     * 
     * @return array Parsed url array containing following items:
     *      - 'path' (string) Part of url without get params, e.g. /sk/mySlug/arg01/param01:xyz
     *          or /en/mvc/MyModule/MyController/myAction/arg01/param01:xyz
     *      - 'absolute' (bool) TRUE if provided $url is absolute (starting with http(s)://)
     *      - 'base' (string) If provided url is absolute then base part of url (schema + host), e.g. 'http://my-site.sk'
     *      - 'root' (string) Copy of root provided in options, e.g. '/myApp'
     *      - 'lang' (string) Lang code, e.g. en, sk or empty
     *      - 'type' (string) possible values are 'mvc' or 'slug'
     *      - 'source' (string) possible values are NULL, 'ajax', 'frame'. 
     *          ATTENTION: This is populated only if GET params are parsed!
     *      - 'slug' (string)
     *      - 'module' (string)
     *      - 'controller' (string)
     *      - 'action' (string)
     *      - 'actionType' (string) Action type prefix, e.g. if action name starts 
     *          with 'admin_' prefix, then 'actionType' is 'admin'. If method has no
     *          prefix then NULL.
     *      - 'locator' (string) Set to '/{slug}' or '/{mvcPartOfPath}' to ensure compatibility 
     *          with input of App::getUrl() method. E.g. e.g. /sk/mySlug/arg01/param01:xyz
     *          has locator '/mySlug' and /en/mvc/MyModule/MyController/myAction/arg01/param01:xyz
     *          has locator '/mvc/MyModule/MyController/myAction'.
     *      - 'params' (array)
     *      - 'args' (array)
     *      - 'get' (array)
     *      - 'anchor' (string) Url anchor/hash (the part after #)
     * 
     * NOTE: This method does not examine a validity of parsed url. Its up to 
     * further processing to do it if necessary.
     */
    static public function parseUrl($url, $options = array()) {
        $defaults = array(
            'hasGetParams' => true,
            'parseGetParams' => false,
            'localizedLang' => false,
            'autodetectLang' => false,
            'base' => self::$urlBase,
            'root' => URL_ROOT,
        );
        $options = array_merge($defaults, $options);
        $options['root'] = rtrim($options['root'], '/');
        
        // preset output
        $parsedUrl = array(
            'path' => null,
            'absolute' => false,
            'base' => $options['base'],
            'root' => $options['root'],
            'lang' => null,
            'type' => null,
            'source' => null,
            'slug' => null,
            'module' => null,
            'controller' => null,
            'action' => null,
            'actionType' => null,
            'locator' => null,
            'params' => array(),
            'args' => array(),
            'get' => array(),
            'anchor' => null,
        );
        
        // check for anchor/hash
        $parts = explode('#', $url);
        if (count($parts) > 1) {
            $parsedUrl['anchor'] = array_pop($parts);
        }
        $url = implode('#', $parts);
        // remove url base if provided url is absolute
        if (
            substr($url, 0, 7) === 'http://' 
            || substr($url, 0, 8) === 'https://'
        ) {
            $parsedUrl['absolute'] = true;
            $parsedUrl['base'] = $parts[0];
            $parts = explode('?', $parsedUrl['base']);
            $parsedUrl['base'] = $parts[0];
            $parts = explode('/', $parsedUrl['base']);
            $parsedUrl['base'] = $parts[0] . '//' . $parts[2];
            $url = preg_replace('/^' . preg_quote($parsedUrl['base'], '/') . '/', '', $url);
        }
        // remove url root (make it separately from removing the url base for case of relative url addresses)
        if ($options['root']) {
            $url = preg_replace('/^' . preg_quote($options['root'], '/') . '/', '', $url);
        }
        // separate url path from get params
        $rawGetParams = null;
        if ($options['hasGetParams']) {
            $parts = explode('?', $url);
            $url = array_shift($parts);
            if ($parts) {
                $rawGetParams = implode('?', $parts);
            }
        }
        // trim leading and trailing slashes
        $url = trim($url, '/');
        // store url path (starting always with leading slash)
        $parsedUrl['path'] = '/' . $url;
        // parse url path
        $parts = explode('/', $url);
        $partsLength = count($parts);
        $ic = 0; // index correction of following items in path
        // read lang
        if (
            !$options['localizedLang'] 
            && strlen(Sanitize::value($parts[0])) == 2
        ) {
            $parsedUrl['lang'] = $parts[0];
            $ic = 1;
        }
        elseif (
            $options['localizedLang'] 
            && strlen(Sanitize::value($parts[0])) == 2 
            && strlen(Sanitize::value($parts[1])) == 2
        ) {
            $parsedUrl['lang'] = $parts[0] . '/' . $parts[1];
            $ic = 2;
        }
        elseif (
            $options['localizedLang'] 
            && $options['autodetectLang']
            && strlen(Sanitize::value($parts[0])) == 2 
        ) {
            $parsedUrl['lang'] = $parts[0];
            $ic = 1;
        }
        // read mvc or slug properties
        $mvcIndex = 0 + $ic;
        if (strtolower(Sanitize::value($parts[$mvcIndex])) == 'mvc') {
            $parsedUrl['type'] = 'mvc';
            $moduleIndex = 1 + $ic;
            $controllerIndex = 2 + $ic;
            $actionIndex = 3 + $ic;
            $paramsStartIndex = 4 + $ic;
            $parsedUrl['module'] = Sanitize::value($parts[$moduleIndex]);
            $parsedUrl['controller'] = Sanitize::value($parts[$controllerIndex]);
            $parsedUrl['action'] = Sanitize::value($parts[$actionIndex]);
            // get action type
            if (($underscorePos = strpos($parsedUrl['action'], '_')) !== false) {
                $parsedUrl['actionType'] = substr($parsedUrl['action'], 0, $underscorePos);
            }
            // set locator
            $parsedUrl['locator'] = '/mvc/' . $parsedUrl['module'] . '/' . $parsedUrl['controller'] . '/' . $parsedUrl['action'];
        }
        else {
            $parsedUrl['type'] = 'slug';
            $slugIndex = 0 + $ic;
            $paramsStartIndex = 1 + $ic;
            $parsedUrl['slug'] = Sanitize::value($parts[$slugIndex]);
            // set locator
            $parsedUrl['locator'] = '/' . $parsedUrl['slug'];
        }
        // read params
        for ($i = $paramsStartIndex; $i < $partsLength; $i++) {
            $param = Sanitize::value($parts[$i]);
            // named params
            if (strpos($param, ':') !== false) {
                $param = explode(':', $param);
                $paramName = array_shift($param);
                $paramValue = implode(':', $param);
                $parsedUrl['params'][$paramName] = $paramValue;
            }
            // passed params
            else {
                $parsedUrl['args'][] = $param;
            }
        }
        // parse get params
        if ($options['parseGetParams'] && !empty($rawGetParams)) {
            parse_str($rawGetParams, $parsedUrl['get']);
            if (!empty($parsedUrl['get']['_requestSource_'])) {
                $parsedUrl['source'] = $parsedUrl['get']['_requestSource_'];
            }
        }
        return $parsedUrl;
    } 
    
    /**
     * Autodetecs lang from info sent by browser
     * 
     * @param string $locale Optional. The locale part od localized lang, e.g. 'us'
     *      from  'us/en' or 'us/zh'. If the App::$localizedLang is TRUE then pass 
     *      the locale part here.
     *  
     * @return string Detected lang. If nothing else is detected then default lang is 
     *      returned.
     */
    static public function autodetectLang($locale = null) {
        $requestLangs = Utility::getRequestLangs();
        $requestLangs = array_values($requestLangs);
        $localePrefix = '';
        if ($locale) {
            $localePrefix = $locale . '/';
        }
        // search for an existing language
        foreach ($requestLangs as $requestLang) {
            $requestLang = explode('-', $requestLang);
            $requestLang = reset($requestLang);
            $requestLang = $localePrefix . $requestLang;
            foreach (self::$languages as $language) {
                if ($requestLang == $language['lang']) {
                    return $language['lang'];
                }
            }
        }
        // if locale is given then search for localized default
        if ($localePrefix) {
            foreach (self::$languages as $language) {
                if (
                    $language['localized_default']
                    && $localePrefix === substr($language['lang'], 0, 3)
                ) {
                    return $language['lang'];
                }
            }
        }
        // if nothing found till here then return default lang
        return self::$languages['default']['lang'];
    }
    
    /**
     * Resolves lang by domain name
     * 
     * NOTE: Implementation of this method can be different on each project.
     * 
     * @return string|NULL Resolved lang code ('en', 'sk', 'cs', ...)
     *      or NULL if no available lang is resolved
     */
    static function getLangByDomain() { 
        $lang = null;
        /*/
        $domain = $httpHost = (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : null);
        $domainExtension = substr($domain, -3);
        // cs
        if (
            $domainExtension === '.cz'
            // for local testing
            || $domainExtension === '-cz'
        ) {
            $lang = 'cs';
        }
        // check if resolved language is really available
        if (!in_array($lang, self::$langs)) {
            $lang = null;
        }
        /*/
        return $lang;
    }
    
    /**
     * OTHER METHODS
     */
    
    /**
     * Resolves the final url according to provided options. 
     * 
     * Proper lang and url root is set. The lang is set only in case that it is 
     * not already present in url (it does not start by '/??/'). This method 
     * does not ensure the existence or validity of created url.
     *  
     * @param string|array $options Locator string or an array of following options:
     *      - 'locator' (string) Use it to provide slug ('my-slug'), internal 
     *          reference ('/my-reference') or an absolute URL ('http://mysite.com').
     *          In case of absolute url this is returned as it is (no lang, url root,
     *          url base or protocol is considered) with attached params and getParams. Defaults to NULL.
     *      - 'module' (string) Use it to create mvc url. Defaults to 'App'.
     *      - 'controller' (string) Use it to create mvc url. Defaults to NULL.
     *      - 'action' (string) Use it to create mvc url. Defaults to NULL.
     *      - 'lang' (string) Lang code to be used in url. If any empty value then 
     *          the lang is explicitly turned off. Defaults to URL_LANG.
     *      - 'absolute' (bool) If TRUE then an absolute url ('http://...') is returned 
     *          even for internal urls (e.g. for links in mailer campaigns). Defaults to FALSE.
     *      - 'protocol' (string) There are following possibilities: 'http', 'https' 
     *          and NULL (to keep protocol of actual request). The 'https' protocol 
     *          is applied only in case that config property App::$httpsIsAvailable is TRUE. 
     *          Defauts to NULL.
     *      - 'base' (string) Url base to be used in case of absolute URL. This option
     *          is considered only in case that 'absolute' option is TRUE and that 
     *          provided 'locator' is not already in absolute form. Defauts to App::$urlBase.
     *      - 'root' (string) Url path root. It is not possible to guess url root from
     *          provided url form so it must be defined explicitly if any. It does not 
     *          contain trailing slash or it is just empty. This option is considered 
     *          only in case that 'absolute' option is TRUE and that provided 'locator' 
     *          is not already in absolute form. Defaults to URL_ROOT.
     *      - 'args' (array) Array of passed args to be attached to url. Both sssociative 
     *          and nonassociative items are attached as passed args. Values are url encoded.
     *          The 'args' option is processed before 'params' option. Defaults to empty array.
     *      - 'params' (array) Associative array of named params to be attached to url. 
     *          Associative items are attached as named params. Nonassociative items are attached
     *          as passed params. Params are created exactly in provided order. Values are url encoded.
     *          The 'params' option is processed after 'args' option. Defaults to empty array.
     *      - 'get' (array) Associative array of GET params to be attached to url.
     *          Values are url encoded. Defaults to empty array.
     *      - 'allowNestedGetParams' (bool|int) If FALSE or 0 then GET params must not be nested.
     *          It means param values are either int|string|bool|float or also flat plain array
     *          of values is allowed. E.g. array('p1' => 'v1', 'p2' => array('v2', 'v3'))
     *          will produce following URL query "?p1=v1&p2[]=v2&p2[]=v3" for FALSE
     *          and "?p1=v1&p2=v2&p2=v3" for 0. If TRUE then the produced URL query 
     *          would be "?p1=v1&p2[0]=v2&p2[1]=v3" and in such a case any depth 
     *          of nesting including associative arrays are allowed. Defaults to TRUE.
     *      - 'anchor' (string) Anchor string put at the end of URL after hash #. 
     *          Defaults to NULL.
     *      - 'source' (string) Request source specification. Possible values are NULL, 'ajax', 'frame'. 
     *          ATTENTION: This is set as GET param. Defaults to NULL.
     *      - 'rawurlencode' (bool) If TRUE then rawurlencode() is used for encoding
     *          of attached params values instead of urlencode(). Defaults to TRUE.
     *      - 'inherit' (string|array) Single URL option or array of URL options to be inherited 
     *          from app actual url into generated url, e.g. array('get' => array('lang')) 
     *          to inherit GET param 'lang' from actual URL to new created. You can write this also
     *          as array('get/lang'). If you use the path syntax then the specified
     *          path can be as long as you need: 'get/filter/User.name'. Array syntax
     *          allows only 2 levels (see the 1st example) but is faster. To inherit just 
     *          all params, args and get params use array('params', 'args', 'get'). 
     *          Defaults to empty App::$urlInherit.
     *      - 'urlPathTrailingSlash' (bool) If TRUE then urls are created with trailing 
     *          slash at the end of url path. If FALSE then urls are created without 
     *          trailing slash at the end of url path. If NULL then trailing slash is let 
     *          as it is. Change this only in very specific cases because all app
     *          urls should be created in the same way and .htaccess reflects this too.
     *          Defaults to config property App::$urlPathTrailingSlash.
     * 
     *      If the $options are provided as a string then it is taken for 'locator'.
     *      If the $options does not contain neither 'locator' nor 'controller' + 'action'
     *      then an exception is thrown.
     * 
     * @return string Final url string
     * @throws Exception on invalid $options means if the $options do not contain 
     *      neither 'locator' nor 'controller' + 'action'
     */
    static public function getUrl($options) {
        // normalize input
        if (is_string($options)) {
            $options = array('locator' => $options);
        }
        // validate input
        if (
            !is_array($options)
            || 
            !isset($options['locator'])
            && (empty($options['controller']) || empty($options['action']))
        ) {
            throw new Exception(__e(__FILE__, 'Invalid url options. Provide either nonempty locator or module, controler and action name'));
        }
        $defaults = array(
            'locator' => null,
            'module' => 'App',
            'controller' => null,
            'action' => null,
            'lang' => URL_LANG,
            'absolute' => false,
            'base' => self::$urlBase,
            'protocol' => null,
            'root' => URL_ROOT,
            'params' => array(),
            'args' => array(),
            'get' => array(),
            'allowNestedGetParams' => true,
            'anchor' => null,
            'source' => null,
            'rawurlencode' => true,
            'inherit' => self::$urlInherit,
            'urlPathTrailingSlash' => self::$urlPathTrailingSlash,
        );
        $options = array_merge($defaults, $options);
        $options['root'] = trim($options['root'], '/');
        if (!$options['base']) {
            $options['base'] = self::$urlBase;
        }
        $options['base'] = rtrim($options['base'], '/');
        // inherit properties from app actual url
        if (!empty($options['inherit'])) {
            $options['inherit'] = (array)$options['inherit'];
            foreach ($options['inherit'] as $k => $v) {
                if (is_int($k)) {
                    $urlOption = $v;
                    $urlOptionValue = true;
                }
                else {
                    $urlOption = $k;
                    $urlOptionValue = $v;
                }
                $isPlainUrlOption = (strpos($urlOption, '/') === false);
                if (
                    $isPlainUrlOption
                    && !isset(self::$parsedUrl[$urlOption])
                    ||
                    !$isPlainUrlOption
                    && Arr::getPath(self::$parsedUrl, $urlOption, '/') === null    
                ) {
                    continue;
                }
                // if value is true then inherit the url option as it is
                if ($urlOptionValue === true) {
                    if ($isPlainUrlOption) {
                        $options[$urlOption] = self::$parsedUrl[$urlOption];
                    }
                    else {
                        $value = Arr::getPath(self::$parsedUrl, $urlOption, '/');
                        if ($value !== null) {
                            Arr::setPath($options, $urlOption, $value, '/');
                        }
                    }
                }
                // if value is array then it is single params or get param name
                // or list of such a names
                else {
                    $urlOptionValue = (array)$urlOptionValue;
                    foreach ($urlOptionValue as $urlParamName) {
                        if ($isPlainUrlOption) {
                            if (isset(self::$parsedUrl[$urlOption][$urlParamName])) {
                                $options[$urlOption][$urlParamName] = self::$parsedUrl[$urlOption][$urlParamName];
                            }
                        }
                        else {
                            $path = $urlOption . '/' . $urlParamName;
                            $value = Arr::getPath(self::$parsedUrl, $path, '/');
                            if ($value !== null) {
                                Arr::setPath($options, $path, $value, '/');
                            }                            
                        }
                    }
                }
            }
        }
        // create url
        $relative = true;
        if ($options['locator'] !== null) { // locator can be even empty (for home page)
            if (strlen($options['locator']) > 1) {
                $options['locator'] = rtrim($options['locator'], '/');
            }
            if (
                substr($options['locator'], 0, 7) == 'mailto:'
                || substr($options['locator'], 0, 4) == 'tel:'
            ) {
                return $options['locator'];
            }
            elseif (
                substr($options['locator'], 0, 2) == '//'
                || substr($options['locator'], 0, 7) == 'http://'
                || substr($options['locator'], 0, 8) == 'https://'    
                || substr($options['locator'], 0, 7) == 'file://'    
            ) {
                $url = $options['locator'];
                $relative = false;
            }
            elseif (
                substr($options['locator'], 0, 1) == '/'
            ) {
                if ($options['locator'] === '/' . HOME_SLUG) {
                    $options['locator'] = '/';
                }
                $url = $options['locator'];
            }
            else {
                if ($options['locator'] === HOME_SLUG) {
                    $options['locator'] = '';
                }
                $url = '/' . $options['locator'];
            }            
        }
        elseif (
            $options['controller']
            && $options['action']
        ) {
            $url = '/mvc/' . $options['module'] . '/' . $options['controller'] . '/' . $options['action'];
        }
        else {
            throw new Exception(__e(__FILE__, 'Invalid url options: %s', preg_replace('/\n/', '', print_r($options, true))));
        }
        // add url args
        if (!empty($options['args'])) {
            $urlEnd = $match = null;
            if (preg_match('/[?#]/', $url, $match)) {
                $urlParts = explode($match[0], $url);
                $url = rtrim(array_shift($urlParts), '/');
                $urlEnd = implode($match[0], $urlParts);
            }
            $options['args'] = (array)$options['args'];
            foreach ($options['args'] as $v) {
                $v = $options['rawurlencode'] ? rawurlencode($v) : urlencode($v);
                $url .= '/' . $v;
            }
            if (isset($urlEnd)) {
                $url .= $match[0] . $urlEnd; 
            }
        }
        // add url params
        if (!empty($options['params'])) {
            $urlEnd = $match = null;
            if (preg_match('/[?#]/', $url, $match)) {
                $urlParts = explode($match[0], $url);
                $url = rtrim(array_shift($urlParts), '/');
                $urlEnd = implode($match[0], $urlParts);
            }
            $options['params'] = (array)$options['params'];
            foreach ($options['params'] as $k => $v) {
                $v = $options['rawurlencode'] ? rawurlencode($v) : urlencode($v);
                if (is_int($k)) {
                    $url .= '/' . $v;
                }
                else {
                    $url .= '/' . $k . ':' . $v;
                }
            }
            if (isset($urlEnd)) {
                $url .= $match[0] . $urlEnd; 
            }
        }
        // add get params
        if (!empty($options['source'])) {
            $options['get'] = (array)$options['get'];
            $options['get']['_requestSource_'] = $options['source'];
        }
        if (!empty($options['get'])) {
            $urlEnd = null;
            if (strpos($url, '#') !== false) {
                $urlParts = explode('#', $url);
                $url = array_shift($urlParts);
                $urlEnd = implode('#', $urlParts);
            }
            $options['get'] = (array)$options['get'];
            $amp = '&';
            if (strpos($url, '?') === false) {
                $url .= '?';
                $amp = '';
            }
            // if nested get params are allowed then deflate get params to correctly 
            // process nested get params
            if ($options['allowNestedGetParams']) {
                $options['get'] = Arr::deflateGetParams($options['get']);
            }
            // build get params query string
            foreach ($options['get'] as $k => $v) {
                if (is_string($k)) {
                    // this can happen if option allowNestedGetParams is FALSE or 0 and
                    // the param has many values (see the phpDoc of allowNestedGetParams option)
                    if (is_array($v)) {
                        if ($options['allowNestedGetParams'] === false) {
                            $k .= '[]';
                        }
                        $k = $options['rawurlencode'] ? rawurlencode($k) : urlencode($k);
                        foreach ($v as $vv) {
                            $vv = $options['rawurlencode'] ? rawurlencode($vv) : urlencode($vv);
                            $url .= $amp . $k . '=' . $vv;
                            $amp = '&';
                        }
                    }
                    else {
                        $v = $options['rawurlencode'] ? rawurlencode($v) : urlencode($v);
                        $k = $options['rawurlencode'] ? rawurlencode($k) : urlencode($k);
                        $url .= $amp . $k . '=' . $v;
                    }
                }
                $amp = '&';
            }
            if (isset($urlEnd)) {
                $url .= '#' . $urlEnd; 
            }
        }
        // add lang, url root and/or url base to relative url
        if ($relative) {
            // add lang only in case that where is not a lang prefix in relative 
            // url already, e.g. /en/my-page (this works for both lang code versions: /en/... and /us/en/...)
            // or /en (for home page)
            if (
                !empty($options['lang'])
                && $url !== '/' . $options['lang']
                && substr($url, 3, 1) !== '/'
            ) {
                $url = '/' . $options['lang'] . $url;
            }
            if ($options['root']) {
                $url = '/' . $options['root'] . $url;
            }
            // normalize protocol
            if ($options['protocol'] !== null) {
                $options['protocol'] = strtolower($options['protocol']);
                if ($options['protocol'] !== 'http' && $options['protocol'] !== 'https') {
                    $options['protocol'] = null;
                } 
            }
            if ($options['absolute'] || $options['protocol']) {
                $url = $options['base'] . $url;
            }
            if (
                $options['protocol'] === 'http'
                || $options['protocol'] === 'https' && self::$httpsIsAvailable
            ) {
                $url = $options['protocol'] . ':' . preg_replace('/^https?\:/i', '', $url);
            }
        }
        // add or remove trailing slash
        if (
            $options['urlPathTrailingSlash'] !== null
            && !Validate::externalUrl($url)
        ) {
            // get url path
            if (
                ($pos = strpos($url, '?')) !== false
                ||
                ($pos = strpos($url, '#')) !== false    
            ) {
                $urlPath = substr($url, 0, $pos);
            }
            else {
                $urlPath = $url;
            }
            // add trailing slash to url path
            if ($options['urlPathTrailingSlash']) {
                if (
                    // - which does not have tailing slash yet
                    substr($urlPath, -1) !== '/'
                    // - and which does not ends by extension
                    && !preg_match('/\.[a-z]{2,4}$/i', $urlPath)
                ) {
                    $urlPath .= '/';
                    if ($pos !== false) {
                        $url = $urlPath . substr($url, $pos);
                    }
                    else {
                        $url = $urlPath;
                    }
                }
            }
            // remove trailing slash from url path
            else {
                if (
                    // - which has tailing slash
                    substr($urlPath, -1) === '/'
                    // - but is not only slash (home url) 
                    && $urlPath !== '/'
                ) {
                    $urlPath = substr($urlPath, 0, -1);
                    if ($pos !== false) {
                        $url = $urlPath . substr($url, $pos);
                    }
                    else {
                        $url = $urlPath;
                    }
                }
            }
        }
        // add anchor
        if (!empty($options['anchor'])) {
            if (strpos($url, '#') !== false) {
                $urlParts = explode('#', $url);
                $url = reset($urlParts);
            }
            $url .= '#' . ($options['rawurlencode'] ? rawurlencode($options['anchor']) : urlencode($options['anchor']));
        }
    
        return $url;
    }
    
    /**
     * Rebuilds provided string URL according to provided options
     * 
     * @param string|array $url Url to be rebuilt given either as a string or as an
     *      array of App::getUrl() options.
     * @param string|array $options Options to be changed in rebuilded url. If string then
     *      considered to be 'locator'. See App::getUrl() options. Moreover there 
     *      are following additional options:
     *      - 'inheritExcept' (array) If provided then all options of rebuilt $url 
     *          EXCEPT of here specified are inherited. Array paths can be used 
     *          (separator "."). e.g. 'args.1'. ATTENTION: If not provided or empty 
     *          then by default everything is inherited. 
     *      - 'inherit' (array)  If provided then all here specified options of rebuilt 
     *          $url are inherited. Array paths can be used (separator "."). e.g. 'args.1'.
     *          ATTENTION: If not provided or empty then by default everything is inherited.
     *          If both 'inheritExcept' and 'inherit' are provided then 'inheritExcept' 
     *          is applied first and then 'inherit' is applied.
     *      - 'mergeParams' (bool) If TRUE then $options['params'] are merged with
     *          params in rebuilt $url. Defaults to FALSE.
     *      - 'mergeArgs' (bool) If TRUE then $options['args'] are merged with
     *          args in rebuilt $url. Defaults to FALSE.
     *      - 'mergeGet' (bool) If TRUE then $options['get'] are merged with
     *          get params in rebuilt $url. Defaults to FALSE.
     *      - 'outputType' (string) Defines the type of returned rebuilt url. 
     *          Possible values are 'string', 'array' or 'inherit'. If inherit
     *          then the output url has the same type as input url. Defauts to 
     *          'string'
     * 
     * @return string|array Rebuilt url returned in the form (string or array) as it was provided.
     */
    static public function rebuildUrl($url, $options) {
        // normalize input
        if (!is_array($options)) {
            $options = array('locator' => $options);
        }
        $defaults = array(
            'inheritExcept' => null,
            'inherit' => null,
            'mergeParams' => false,
            'mergeArgs' => false,
            'mergeGet' => false,
            'outputType' => 'string',
        );
        $options = array_merge($defaults, $options);
        $options['outputType'] = strtolower($options['outputType']);

        if (is_array($url)) {
            $parsedUrl = $url;
        }
        else {
            $parsedUrl = self::parseUrl($url, array(
                'parseGetParams' => true,
            ));
        }
        
        // apply inheritance
        $inheritedParsedUrl = $parsedUrl;
        if ($options['inheritExcept']) {
            $options['inheritExcept'] = (array)$options['inheritExcept'];
            foreach ($options['inheritExcept'] as $path) {
                Arr::unsetPath($inheritedParsedUrl, $path);
            }
        }
        if ($options['inherit']) {
            $options['inherit'] = (array)$options['inherit'];
            if (!$options['inheritExcept']) {
                $inheritedParsedUrl = array();
            }
            foreach ($options['inherit'] as $path) {
                $value = Arr::getPath($parsedUrl, $path);
                if (
                    is_string($value) 
                    && $value !== ''
                    ||
                    is_numeric($value)
                ) {
                    Arr::setPath($inheritedParsedUrl, $path, $value);
                }
            }
        }
        $parsedUrl = $inheritedParsedUrl;
        
        // keep params, args and get for merging
        $parsedUrlParams = (array)Sanitize::value($parsedUrl['params']);
        $parsedUrlArgs = Sanitize::value($parsedUrl['args'], array());
        // - normalize all empty values in the same way as `(array)null` resutls to 
        // `array()` but `(array)0` results to `array(0)`
        if (!is_array($parsedUrlArgs)) { 
            $parsedUrlArgs = array($parsedUrlArgs);
        }
        $parsedUrlGet = (array)Sanitize::value($parsedUrl['get']);
        
        // merge
        $parsedUrl = array_merge($parsedUrl, $options);
        // merge params, args and get if required
        if ($options['mergeParams']) {
            $parsedUrl['params'] = array_merge($parsedUrlParams, Sanitize::value($options['params'], array()));
        }
        if ($options['mergeArgs']) {
            $parsedUrl['args'] = array_merge($parsedUrlArgs, Sanitize::value($options['args'], array()));
        }
        if ($options['mergeGet']) {
            $parsedUrl['get'] = array_merge($parsedUrlGet, Sanitize::value($options['get'], array()));
        }

        // resolve return type
        if ($options['outputType'] === 'array') {
            return $parsedUrl;
        }
        elseif ($options['outputType'] === 'string') {
            return self::getUrl($parsedUrl);
        }
        elseif (is_array($url)) {
            return $parsedUrl;
        }
        else {
            return self::getUrl($parsedUrl);
        }
    }
    
    /**
     * Returns url of specified content. 
     * 
     * @param string $pid 
     * @param array $options An array of App::getUrl() options plus following:
     *      - 'exceptionOnFailure' (bool) If TRUE then an exception is thrown if
     *          specified content does not exist or if its locator is empty. 
     *          If FALSE then FALSE is returned instead of throwing exception.
     *          Defaults to FALSE.
     * 
     *      Option 'locator' is set explicitly.
     *  
     * @return string|bool Url of specified content or FALSE if option exceptionOnFailure 
     *      is set to FALSE and specified content does not exist or its locator is empty.
     * @throws Exception if content with provided pid does not exist.
     */
    static public function getContentUrlByPid($pid, $options = array()) {
        $defaults = array(
            'lang' => URL_LANG,
            'exceptionOnFailure' => false,
        );
        $options = array_merge($defaults, $options);
        // if $options['lang'] is empty then use App::$lang
        $lang = $options['lang'] ? $options['lang'] : self::$lang;
        $options['locator'] = self::getContentLocatorByPid($pid, array('lang' => $lang));
        if (empty($options['locator'])) {
            if (!empty($options['exceptionOnFailure'])) {
                throw new Exception(__e(__FILE__, 'Content with pid "' . $pid . '" does not exist!'));
            }
            return false;
        }
        return self::getUrl($options);
    }
    
    /**
     * Redirects to given $url. Script execution is halted after the redirect.
     *
     * @param string $url An URL address to redirect.
     * @param array $options Following options can be used:
     *      - 'status' (int) HTTP status code (eg: 404). Defaults to NULL.
     *      - 'exit' (bool) If TRUE then execution of current php process is stopped. 
     *          Defaults to TRUE.
     *      - 'loopSafe' (bool|int) If TRUE then redirection is made in redirection loop 
     *          safe mode using session flag (see App::incrementRedirectCounter()). If integer
     *          then it is max amount of allowed redirection loops before the looping is
     *          broken. This can be done only in case that option 'exit' is TRUE.
     *          Defaults to 10.
     *      - 'loopBreakUrl' (string) The when the app is redirected then it seems
     *          that it get to redirection loop. Defaults to '/' (home page).
     *      - 'js' (bool|string|null) If TRUE or 'window' then the redirection is made by javascript 
     *          in browser by changing actual window location - means if we are in iframe
     *          then the iframe content window location is changed. If 'parent' then
     *          the parent window location is changed. If 'top' then top window location
     *          is changed - this is usefull in case that php script called by iframe 
     *          would like to redirect the main window content (not only content of iframe 
     *          which would result of normal redirect). All output buffers are cleaned here 
     *          and only the javascript needed for redirection is send to output. 
     *          The 'exit' option must be TRUE to use this option. If NULL then js redirection
     *          is used as fallback solution in case that http redirection cannot be used
     *          because headers has been sent already. If FALSE then http redirection is forced.
     *          Defauts to NULL.
     *      - 'data' (array|string) Data to be sent to redirection $url by POST method.
     *          It can be provided either as array of pairs {inputName} => {inputValue},
     *          or as a html string of generated inputs like '< input name="data[myInput1]" value="2"/>
     *          < input name="data[myInput2]" value="3"/>'. If provided in form of array
     *          then the array keys are used for input names precisely as the are (they are not 
     *          wrapped to data[]). Defaults to NULL.
     */
	static public function redirect($url, $options = array()) {
        $defaults = array(
            'status' => null,
            'exit' => true,
            'loopSafe' => 10,
            'loopBreakUrl' => '/',
            'js' => null,
            'data' => null,
        );
        $options = array_merge($defaults, $options);
        
        // data can be sent only by js redirection
        if (
            !empty($options['data'])
            && empty($options['js'])
        ) {
            throw new Exception(__e(__FILE__, 'To submit data on redirect you must do js redirect, means set \'js\' option to TRUE'));
        }
        
        // if headers has been already sent and js redirection can beused as fallback 
        // then do it instead of http redirection
        if (
            $options['js'] === null
            && $options['exit']
            && headers_sent()
        ) {
            $options['js'] = true;
        }
        
        // redirection by headers cannot be done if headers has been sent already
        if (
            empty($options['js'])
            && headers_sent()
        ) {
            throw new Exception(__e(__FILE__, 'Redirection to %s cannot be done as headers has been sent already (check for php generated error messages)', $url));
        }
        
        // check for redirection loops
        if (Validate::externalUrl($url)) {
            // if redirected to external site then do not increment redirections counter
            // because if the processing is redirected back to local site (e.g. after payment)
            // and there is another redirect after the comeback then this second local 
            // redirect is considered to be redirection loop
            $redirectCounter = self::getRedirectCounter();
        }
        else {
            $redirectCounter = self::incrementRedirectCounter();
        }
        if ($options['loopSafe']) {
            if (!$options['exit']) {
                throw new Exception(__e(__FILE__, 'It is not possible to make loop safe redirection with exit option turned off'));
            }
            if (
                $redirectCounter 
                && (
                    $options['loopSafe'] === true 
                    || $redirectCounter >= (int)$options['loopSafe']
                )    
            ) {
                // if even the loopBreakUrl is problematic then change it to _redirectionLoopEscape screen
                if ($url === $options['loopBreakUrl']) {
                    $options['loopBreakUrl'] = '/_redirectionLoopEscape';
                }
                $url = $options['loopBreakUrl'];
            }
            
        }
        
        // close and save session
        if (function_exists('session_write_close')) {
                session_write_close();
        }
        
        // do logs but only if they will not break the redirect
        if (
            !empty($options['js']) 
            || self::$debugOutput === 'fb'
            || self::$debugOutput === 'log'
        ) {
            self::getElapsedMicrotime(true);
            DB::debugSqlLog();
        }
        
        // do the js redirect from browser
        if (!empty($options['js'])) {
            if (!$options['exit']) {
                throw new Exception(__e(__FILE__, 'It is not possible to make js redirection with exit option turned off'));
            }
            self::endPhpErrorsHtmlCapture(array(
                'closeOpenedOutputBuffers' => true
            ));
            $window = 'window';
            if (strtolower($options['js']) === 'parent') {
                $window .= '.parent';
            }
            elseif (strtolower($options['js']) === 'top') {
                $window .= '.top';
            }
            if (empty($options['data'])) {
                // echo the js code 
                echo '<script type="text/javascript">'. $window . '.location.href = "' . $url . '";</script>';
            }
            else {
                $formId = uniqid('_post_redirect_data_');
                $lang = substr(self::$lang, -2);
                $form = '<!doctype html><html lang="' . $lang . '"><head><meta charset="utf-8"></head><body>';
                $form .= '<form action="' . $url . '" method="post" id="' . $formId . '" style="display:none;">';
                if (is_array($options['data'])) {
                    foreach($options['data'] as $name => $value) {
                        $name = htmlspecialchars($name, ENT_COMPAT, 'UTF-8', true);
                        $value = htmlspecialchars($value, ENT_COMPAT, 'UTF-8', true);
                        $form .= '<input type="hidden" name="' . $name . '" value="' . $value . '">';
                    }
                }
                else {
                    $form .= $options['data'];
                }
                $form .= '</form>';
                $form .= '<script type="text/javascript">';
                $form .= $window . '.document.getElementById(\'' . $formId . '\').submit();';
                $form .= '</script>';
                $form .= '</body></html>';
                // echo the form with launching js code
                echo $form;
            }
            // stop execution of current script
            exit();
        }
        
        // do the normal redirect
        static $httpCodes = array(
            100 => 'Continue', 101 => 'Switching Protocols',
            200 => 'OK', 201 => 'Created', 202 => 'Accepted',
            203 => 'Non-Authoritative Information', 204 => 'No Content',
            205 => 'Reset Content', 206 => 'Partial Content',
            300 => 'Multiple Choices', 301 => 'Moved Permanently',
            302 => 'Found', 303 => 'See Other',
            304 => 'Not Modified', 305 => 'Use Proxy', 307 => 'Temporary Redirect',
            308 => 'Permanent Redirect',
            400 => 'Bad Request', 401 => 'Unauthorized', 402 => 'Payment Required',
            403 => 'Forbidden', 404 => 'Not Found', 405 => 'Method Not Allowed',
            406 => 'Not Acceptable', 407 => 'Proxy Authentication Required',
            408 => 'Request Time-out', 409 => 'Conflict', 410 => 'Gone',
            411 => 'Length Required', 412 => 'Precondition Failed',
            413 => 'Request Entity Too Large', 414 => 'Request-URI Too Large',
            415 => 'Unsupported Media Type', 416 => 'Requested range not satisfiable',
            417 => 'Expectation Failed', 500 => 'Internal Server Error',
            501 => 'Not Implemented', 502 => 'Bad Gateway',
            503 => 'Service Unavailable', 504 => 'Gateway Time-out'
        );

        // set status
        $status = $options['status'];
		if (
            $status
            && isset($httpCodes[$status])
        ) {
            $msg = $httpCodes[$status];
            header($_SERVER['SERVER_PROTOCOL'] . " {$status} {$msg}");
            header('Status:' . " {$status} {$msg}");            
		}

        // redirect
        header('Location: ' . $url);
        
        // stop execution of current script if required
        if ($options['exit']) {
            self::endPhpErrorsHtmlCapture(array(
                'closeOpenedOutputBuffers' => true
            ));
            exit();
        }
    }    
    
    /**
     * Turns on/off errors reporting
     *
     * @param bool|int $reportErrors Optional. If TRUE then all errors are reported.
     *      If FALSE then errors are not displayed. 
     *      If set to an integer then error reporting is set exactly according to this value.
     *      If ommited then the error reporting is set according to App::$errorReporting 
     */
    static public function setErrorReporting($reportErrors = null) {
        if ($reportErrors === null) {
            $reportErrors = self::$errorReporting;
        }
        $reporting = 0;
        if ($reportErrors) {
            if ($reportErrors === true) {
                $reporting = E_ALL & ~E_DEPRECATED;
            }
            else {
                $reporting = $reportErrors;
            }
            if (function_exists('ini_set')) {
                ini_set('display_errors', 1);
            }
        }
        elseif (function_exists('ini_set')) {
            ini_set('display_errors', 0);
        }
        return error_reporting($reporting);
    }
    
    /**
     * Turns on/off sql logging
     * 
     * @param bool|int $log Optional. If TRUE or DB::LOG_WITH_BACKTRACE then database 
     *      logs are turned on. If FALSE then database logs are turned off. If omitted 
     *      then database loging is set according to initial value of App::$logSql
     */
    static public function setSqlLogging($log = null) {
        if ($log === null) {
            $log = self::$sqlLogging;
        }
        DB::$log = $log;
    }
    
    /**
     * Turns on/off origin comments
     * 
     * @param bool $allow Optional. If TRUE then origin comments are tuned on.
     *      If FALSE tnen origin comments are turned off. If not provided or NULL
     *      then App::$allowOriginComments is set to its initial value.
     */
    static public function setAllowOriginComments($allow = null) {
        static $initialAllow = null;
        if ($initialAllow === null) {
            $initialAllow = self::$allowOriginComments;
        }
        if ($allow === null) {
            $allow = $initialAllow;
        }
        self::$allowOriginComments = $allow;
    }
        
    /**
     * Debugs given variable.
     * 
     * @param mixed $var Optional. Variable to show debug information for.
     * @param string $message Optional. Label of displayed debug info
     * @param array $options Following can be used:
     *      - 'type' (int) Debug type. Possible values are 'throwable', 'sqllog' 
     *          and NULL (for standard debugging).  If 'throwable' then provided $var 
     *          should be an instance of Throwable (Exception or Error). If 'sqllog' 
     *          then provided $var should be an sql log array returned by DB::getSqlLog(). 
     *          If empty and the 'var' is instance of Throwable then type is autoset
     *          to value 'throwable'. Defaults to NULL.
     *      - 'escapeHtml' (bool) If TRUE, then the method prints the 
     *          debug data in a screen-friendly way. Defaults to FALSE.
     *      - 'showUrl' (bool) If TRUE, then actual app url is appended to message.
     *          Defaults to FALSE.
     *      - 'backtraceLevel' (int) Which level of debug_backtrace() output should be displayed. 
     *          Defaults to 0 - means the most nested level is displayed.
     *      - 'file' (string) Explicit backtrace file. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     *      - 'line' (string) Explicit backtrace line. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     */
    static function debug($var = false, $message = '', $options = array()) {
        // if debug is not on then return
        if (
            self::$debug !== true 
            && (
                !self::$debug 
                || !in_array(self::$debugOutput, self::$debug)
            )
        ) {
            return;
        }
        $defaults = array(
            'type' => null,
            'escapeHtml' => false,
            'showUrl' => false,
            'backtraceLevel' => 0,
            'file' => null,
            'line' => null,
        );
        $options = array_merge($defaults, $options);
        $type = $options['type'];
        $escapeHtml = $options['escapeHtml'];
        $backtraceLevel = $options['backtraceLevel'];
        $file = $options['file'];
        $line = $options['line'];
        if (!$type && $var instanceof Throwable) {
            $type = 'throwable';
        }
        // debug
        if ($message) {
            $message = ': ' . $message;
        }
        if ($type == 'throwable') {
            if ($file === null || $line === null) {
                $file = $var->getFile();
                $line = $var->getLine();
            }
            if ($var instanceof Error) {
                $message = "[ERROR$message] $file (line $line)";
            }
            else {
                $message = "[EXCEPTION$message] $file (line $line)";
            }
        }
        else {
            if ($file === null || $line === null) {
                if (PHP_VERSION_ID >= 50400) {
                    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, $backtraceLevel + 1);
                }
                elseif (PHP_VERSION_ID >= 50306) {
                    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
                }
                else {
                    $backtrace = debug_backtrace();
                }
                $file = substr($backtrace[$backtraceLevel]['file'], strlen(ROOT) + 1);
                $line = $backtrace[$backtraceLevel]['line'];
            }
            if ($type == 'sqllog') {
                $message = "[SQLLOG$message] $file (line $line)";
            }
            else {
                $message = "[DEBUG$message] $file (line $line)";
            }
        }
        static $outputJsScript = true;
        $jsScript = '';
        $displayStyle = 'display:none;';
        $debugOutput = self::$debugOutput;
        // normalize debug output:
        // - avoid error trying to write into headers (FB) after they has been sent
        if ($debugOutput == 'fb' && headers_sent()) {
            $message .= ' (FB cannot be used as headers has been sent already. Console is used instead.)'; 
            $debugOutput = 'console';
        }
        // - avoid html debug into empty layout (mostly used to json responses)
        if (
            self::$avoidHtmlDebug == true
            && (
               $debugOutput == 'console'
               || $debugOutput == 'echo'
               || $debugOutput == 'hidden'
            )
        ) {
            $message .= ' (Html debug output cannot be used in this layout. Log is used instead.)';
            $debugOutput = 'log';
        }
        if (
            $options['showUrl']
            && $debugOutput !== 'log'
        ) {
            $message .= ' (' . self::$url . ')';
        }
        switch (true) {
            // use FB only if headers are not sent yet, otherwise exception will rise
            case $debugOutput == 'fb':
                FB::group($message, array('Collapsed' => true));   
                if ($type == 'sqllog') {
                    foreach($var as $log) {
                        FB::log($log);
                    }
                }
                elseif (
                    $type === 'throwable'
                    && $var instanceof Error
                ) {
                    FB::log($var->__toString());
                }
                else {
                    FB::log($var);
                }
                FB::groupEnd();
                break;
            case $debugOutput == 'log':
                self::log('debug', $message, array(
                    'var' => $var,
                    'type' => $type,
                    'file' => $file,
                    'line' => $line,
                ));
                break;
            case $debugOutput == 'echo':
                $displayStyle = 'display:block;';
            case $debugOutput == 'hidden':
                if (
                    $debugOutput != 'echo' // avoid this script if echo is used as $debugOutput
                    && $outputJsScript
                ) {
                    $jsScript = "
                        <script type=\"text/javascript\">
                        console.log('DEBUG: run \"showDebug();\" from JS command line to display debug blocks on page')
                        function showDebug() {
                            var debugs = getElementsByClass('_debug', 'div');
                            if (typeof debugs != 'undefined') {
                                for (var i = 0; i < debugs.length; i++) {
                                    debugs[i].style.display = 'block';
                                }
                            }
                            function getElementsByClass(classname, tagname, root) {
                                if (!root) root = document;
                                else if (typeof root == 'string') root = document.getElementById(root);
                                if (!tagname) tagname = '15-';
                                var all = root.getElementsByTagName(tagname);
                                if (!classname) return all;
                                var elements = []; 
                                for(var i = 0; i < all.length; i++) {
                                    var element = all[i];
                                    if (isMember(element, classname)) 
                                        elements.push(element);       
                                }
                                return elements;
                                function isMember(element, classname) {
                                    var classes = element.className;  
                                    if (!classes) return false;             
                                    if (classes == classname) return true;  
                                    var whitespace = /\s+/;
                                    if (!whitespace.test(classes)) return false;
                                    var c = classes.split(whitespace);  
                                    for(var i = 0; i < c.length; i++) { 
                                        if (c[i] == classname) return true;  
                                    }
                                    return false;  
                                }
                            }
                        }    
                        </script>
                        ";
                    $outputJsScript = false; // output the script only once
                }
                if ($type == 'throwable') {
                    $var = $var->__toString();
                }
                elseif ($type == 'sqllog') {
                    $var = implode('<br /><br />', $var);
                }
                else {
                    $var = print_r($var, true);
                }
                if ($escapeHtml) {
                    $var = str_replace('<', '&lt;', str_replace('>', '&gt;', $var));
                }
                echo "\n<div class=\"_debug\" style=\"{$displayStyle}margin:5px 10px;text-align:left;color:#000;background:#FFF;\">";
                    echo "\n<div style=\"background: #DDD; cursor: pointer;\" onclick=\"e = this; do {e = e.nextSibling;} while(e && e.nodeType !== 1/*ELEMENT_NODE*/); if(e) {e.style.display = e.style.display == 'none' ? 'block' : 'none';}\">";
                        echo "\n$message";
                    echo "\n</div>";
                    echo "\n<pre style=\"white-space: pre; overflow: auto; padding: 10px; display: none;\">";
                        echo "\n$var";
                    echo "\n</pre>";
                    echo $jsScript;
                echo "\n</div>";
                break;
            case $debugOutput == 'console':
            default:
                if ($type == 'sqllog') {
                    $var = implode("\n\n", $var);
                }
                $var = json_encode($var);
                // message is placed in single quotes in js, so remove any single quotes to not cause error
                $message = str_replace('\'', '', $message);
                $message = str_replace('\\', '\\\\', $message);
                echo "\n<script type=\"text/javascript\">(function(){console.log('$message:');var v=$var;console.log(v);})()</script>";
                break;

        }
    }
    
    /**
     * Returns backtrace info array containing trace strings for each level, e.g.:
     * 
     *      array(
     *          'app/libs/default/DB.mysql.php (line 2597) DB::query()',
     *          'app/libs/default/App.php (line 9133) DB::select()',
     *          'app/libs/default/App.php (line 3278) App::reinitUser()',
     *          'index.php (line 8) App::init()',
     *      )
     * 
     * @param array $options Following are available:
     *      - 'depth' (int) How many levels back should be returned? If 0 or any 
     *          empty value then all levels are returned. Defaults to 0.
     *      - 'type' (string) Possible values are 'fileOnly', 'functionOnly' or NULL.
     *          If NULL then the backtrace string contains both files (with lines) 
     *          and function (with possible owner classes). Otherwise only one of them 
     *          is contained. Defaults to NULL.
     *  
     * @return array The abowe described array
     */
    static public function getBacktrace($options = array()) {
        $defaults = array(
            'depth' => 0,
            'type' => null
        );
        $options = array_merge($defaults, $options);
        $options['depth'] = (int)$options['depth'];
        if (
            PHP_VERSION_ID >= 50400
            && $options['depth'] > 0
        ) {
            $rawBacktrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, $options['depth'] + 1);
        }
        elseif (PHP_VERSION_ID >= 50306) {
            $rawBacktrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
        }
        else {
            $rawBacktrace = debug_backtrace();
        }
        // remove the first item pointing to App::getBacktrace()
        array_shift($rawBacktrace);
        $backtrace = array();
        $rootLength = strlen(ROOT) + 1;
        foreach ($rawBacktrace as $rawTrace) {
            $trace = '';
            if (
                $options['type'] != 'functionOnly'
                && !empty($rawTrace['file'])
            ) {
                $trace .= substr($rawTrace['file'], $rootLength);
            }
            if (
                $options['type'] != 'functionOnly'
                && !empty($rawTrace['line'])
            ) {
                $trace .= ' (line ' . $rawTrace['line'] . ')';
            }
            if (
                $options['type'] != 'fileOnly'
                && !empty($rawTrace['class'])
            ) {
                $trace .= ' ' . $rawTrace['class'];
                if (!empty($rawTrace['type'])) {
                    $trace .= $rawTrace['type'];
                }
                else {
                    $trace .= '::';
                }
            }
            if (
                $options['type'] != 'fileOnly'
                && !empty($rawTrace['function'])
            ) {
                if (empty($rawTrace['class'])) {
                    $trace .= ' ';
                }
                $trace .= $rawTrace['function'] . '()';
            }
            $backtrace[] = ltrim($trace);
            if (--$options['depth'] === 0) {
                break;
            }
        }
        return $backtrace;
    }
    
    /**
     * Logs a messsage and variable to a log file.
     * The log file is placed under projects tmp/logs/ folder.
     * 
     * NOTE: User agent string is part of log record. To analyse it use http://browscap.org/ua-lookup.
     *
     * @param string $logfile Name of log file to write your message into (without any extension).
     * @param string $message The message you want to log.
     * @param array $options Following can be used:
     *      - 'var' (mixed) Variable you want to log. Defaults to NULL.
     *      - 'type' (int) Debug type. Possible values are 'throwable', 'sqllog' 
     *          and NULL (for standard debugging).  If 'throwable' then provided $var 
     *          should be an instance of Throwable (Exception or Error). If 'sqllog' 
     *          then provided $var should be an sql log array returned by DB::getSqlLog(). 
     *          If empty and the 'var' is instance of Throwable then type is autoset
     *          to value 'throwable'. Defaults to NULL.
     *      - 'email' (bool) If TRUE then the sending of log message is allowed.
     *          The message is sent only in case that config App::$logEmail address is set.
     *          Defaults to FALSE.
     *      - 'backtraceLevel' (int) Which level of debug_backtrace() output should be displayed. 
     *          Defaults to 0 - means the most nested level is displayed.
     *      - 'file' (string) Explicit backtrace file. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     *      - 'line' (string) Explicit backtrace line. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     *      - 'maxSize' (integer|string) Log file max size limit. Can be set either
     *          by integer number of bytes e.g. 1024 or by human readable file size
     *          string, e.g. '1k'. If file is larger than 90% of here specified limit,
     *          then a warning email is sent to App::$logEmail (if any address provided).
     *          If log file is larger than specified limit then it is stopped to be written.
     *          ATTENTION: If no limit specified then a warning email is sent on each log
     *          but nothing is written. Defaults to '100MB'.
     *      - 'data' (bool|array) Request data to be added to log in JSON encoded 
     *          version. If TRUE then set to $_POST superglobal. It can be set explicitly 
     *          to any other array value (e.g. App::$data). If any empty (or non-array)
     *          value then omitted (to avoid log some sensitive data, e.g. password).
     *          ATTENTION: Do not use this to log variable value which should be
     *          added to log by 'var' option. Defaults to TRUE.
     */
	static function log($logfile, $message, $options = array()) {
        $defaults = array(
            'var' => null,
            'type' => null,
            'email' => false,
            'backtraceLevel' => 0,
            'file' => null,
            'line' => null,
            'maxSize' => '100MB',
            'data' => true,
        );
        $options = array_merge($defaults, $options);
        $var = $options['var'];
        $type = $options['type'];
        $backtraceLevel = $options['backtraceLevel'];
        $email = $options['email'];
        $file = $options['file'];
        $line = $options['line'];
        $maxSize = $options['maxSize'];
        if (!$type && $var instanceof Throwable) {
            $type = 'throwable';
        }
        if (is_string($maxSize)) {
            $maxSize = Utility::convertToBytes($maxSize);
        }
        $maxSize = (int)$maxSize;
        // prepare log text
        if ($type === 'throwable') {
            if ($file === null || $line === null) {
                $file = $var->getFile();
                $line = $var->getLine();
            }
            if ($message) {
                $message = ': ' . $message;
            }
            else {
                $message = ': ' . $var->getMessage();
            }
            if (($code = $var->getCode()) !== 0) {
                $message = ' (code ' . json_encode($code) . ')' . $message;
            }
            if ($var instanceof Error) {
                $message = '[ERROR' . $message . '] ';
            }
            else {
                $message = '[EXCEPTION' . $message . '] ';
            }
        }
        else {
            if ($file === null || $line === null) {
                if (PHP_VERSION_ID >= 50400) {
                    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, $backtraceLevel + 1);
                }
                elseif (PHP_VERSION_ID >= 50306) {
                    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
                }
                else {
                    $backtrace = debug_backtrace();
                }
                $file = substr($backtrace[$backtraceLevel]['file'], strlen(ROOT));
                $line = $backtrace[$backtraceLevel]['line'];
            }
            if ($type === 'sqllog') {
                if ($message) {
                    $message = ': ' . $message;
                }
                $message = '[SQLLOG' . $message . '] ';
            }
            elseif ($message) {
                $message = '[' . $message . '] ';
            }
        }
        if ($var !== null) {
            if ($type === 'sqllog') {
                $var = implode("\n\n", $var);
            }
            elseif ($type === 'throwable') {
                $var = $var->__toString();
            }
            else {
                $var = print_r($var, true);
            }
            $var = ": \n    " . str_replace("\n", "\n    ", trim($var));
        }
        $request = Sanitize::value($_SERVER['REQUEST_URI']);
        if ($request) {
            $request = ' (' . $request . ')';
        }
        $executionMicrotime  = ' ' . self::getElapsedMicrotime() . '/' . self::getMaxExecutionMicrotime() . 'ms';
        $ip = Sanitize::value($_SERVER['REMOTE_ADDR']);
        if ($ip) {
            $ip = ' ' . $ip;
        }
        $agent = Sanitize::value($_SERVER['HTTP_USER_AGENT']);
        if ($agent) {
            $agent = ' ' . $agent;
        }
        $data = '';
        if (
            $options['data'] === true
            && !empty($_POST)
        ) {
            $data = json_encode($_POST);
        }
        elseif (
            is_array($options['data'])
            && !empty($options['data'])
        ) {
            $data = json_encode($options['data']);
        }
        $message .=  $file . ' (line ' . $line . ')';
        $message = '>> ' . date('Y-m-d H:i:s') . $executionMicrotime . $ip . $agent . $request . $data . "\n - " . $message . $var . "\n";
        // log to file
        $logpath = str_replace('..', '', $logfile); // do not allow to go out from controllers folder
        $logpath = str_replace(array('/', '\\'), DS, $logpath); // make it windows compatibile
        if (strlen($logpath) < 4 || substr($logpath, -4) != '.log') {
            $logpath .= '.log';
        }
        $logpath = TMP . DS . 'logs' . DS . ltrim($logpath, DS);
        $logsize = file_exists($logpath) ? filesize($logpath) : 0;
        if (($isWritten = $logsize < $maxSize)) {
            $handle = fopen($logpath, 'a');
            if (is_resource($handle)) {
                if (fwrite($handle, $message) === false) {
                    return false;
                }
                fclose($handle);
            }
        }
        // send log email
        $sendWarning = $logsize > ($maxSize * 0.9);
        if (self::$logEmail && ($email || !$isWritten || $sendWarning)) {
            $humanMaxSize = Utility::formatBytes($maxSize);
            $humanLogSize = Utility::formatBytes($logsize);
            $subject = __d(__FILE__, '%s: new log in %s.log (%s/%s)', self::$urlBaseWithRoot, $logfile, $humanLogSize, $humanMaxSize);
            if (!$isWritten) {
                $subject = '!!!' . __d(__FILE__, 'FULL LOG') . '!!! ' . $subject;
                $message = 
                    '<b style="color:red">!!!' . 
                    __d(__FILE__, 'LOG FILE %s.log ON %s IS FULL (%s/%s), NO WRITE IS DONE', $logfile, self::$urlBaseWithRoot, $humanLogSize, $humanMaxSize) . 
                    '!!!</b><br><br>' .  $message;
            }
            elseif ($sendWarning) {
                if ($email) {
                    $subject = '!!!' . __d(__FILE__, 'ALMOST FULL LOG') . '!!! ' . $subject;
                    $message = 
                        '<b style="color:red">!!!' . 
                        __d(__FILE__, 'LOG FILE %s.log ON %s IS GETTING FULL (%s/%s)', $logfile, self::$urlBaseWithRoot, $humanLogSize, $humanMaxSize) . 
                        '!!!</b><br><br>' .  $message;
                }
                else {
                    $subject = '!!!' . __d(__FILE__, 'ALMOST FULL LOG') . '!!! ' .  __d(__FILE__, '%s: %s.log (%s/%s)', self::$urlBaseWithRoot, $logfile, $humanLogSize, $humanMaxSize);
                    $message = 
                        '<b style="color:red">!!!' . 
                        __d(__FILE__, 'LOG FILE %s.log ON %s IS GETTING FULL (%s/%s)', $logfile, self::$urlBaseWithRoot, $humanLogSize, $humanMaxSize) . 
                        '!!!</b>';
                }
            }
            try {
                // set from
                // - if possible use unexisting noreply.net domain to avoid block
                // of site mail domain. On some newer mail servers (e.g. smtp.office365.com)
                // this does not work, email address must exist.
                $from = '<EMAIL>';
//                // - alternative 1. On some newer mail servers (e.g. smtp.office365.com)
//                // this does not work, email address must exist.
//                $hostNameParts = explode('.', !empty($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : $_SERVER['SERVER_NAME']);
//                $hostName = array_pop($hostNameParts);
//                if (!empty($hostNameParts)) {
//                    $hostName = array_pop($hostNameParts) . '.' . $hostName;
//                }
//                $from = 'noreply@' . $hostName;
//                // - alternative 2
//                $from = App::getSetting('App', 'email.from');
                
                self::sendEmail('<pre>' . $message . '</pre>', self::$logEmail, array(
                    'from' => $from,
                    'subject' => $subject,
                ));
            }
            catch (Throwable $e) {
                // avoid cycling in sending emails for email failure exceptions or errors
                // just log the error
                self::logError(__e(__FILE__, 'Log e-mail has failed'), array(
                    'var' => $e
                ));
            }
        }
        elseif (!self::$logEmail && (!$isWritten || $sendWarning)) {
            $humanMaxSize = Utility::formatBytes($maxSize);
            $humanLogSize = Utility::formatBytes($logsize);
            if (!$isWritten) {
                $message = '!!!' . __d(__FILE__, 'FULL LOG %s.log (%s/%s)', $logfile, $humanLogSize, $humanMaxSize) . '!!!';
            }
            elseif ($sendWarning) {
                $message = '!!!' . __d(__FILE__, 'ALMOST FULL LOG %s.log (%s/%s)', $logfile, $humanLogSize, $humanMaxSize) . '!!!';
            }
            App::debug($message, $message);
        }
	}
    
    /**
     * Logs the errror log file.
     * The log file is placed under projects tmp/logs/ folder.
     *
     * @param string $message The message you want to log.
     * @param array $options Following can be used:
     *      - 'var' (mixed) Variable you want to log. Defaults to NULL.
     *      - 'type' (int) Debug type. Possible values are 'throwable', 'sqllog' 
     *          and NULL (for standard debugging).  If 'throwable' then provided $var 
     *          should be an instance of Throwable (Exception or Error). If 'sqllog' 
     *          then provided $var should be an sql log array returned by DB::getSqlLog(). 
     *          If empty and the 'var' is instance of Throwable then type is autoset
     *          to value 'throwable'. Defaults to NULL.
     *      - 'email' (bool) If TRUE then the sending of log message is allowed.
     *          The message is sent only in case that config App::$logEmail address is set.
     *      - 'backtraceLevel' (int) Which level of debug_backtrace() output should be displayed. 
     *          Defaults to 0 - means the most nested level is displayed.
     *      - 'file' (string) Explicit backtrace file. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     *      - 'line' (string) Explicit backtrace line. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     *      - 'maxSize' (integer|string) Log file max size limit. Can be set either
     *          by integer number of bytes e.g. 1024 or by human readable file size
     *          string, e.g. '1k'. If file is larger than 90% of here specified limit,
     *          then a warning email is sent to App::$logEmail (if any address provided).
     *          If log file is larger than specified limit then it is stopped to be written.
     *          ATTENTION: If no limit specified then a warning email is sent on each log
     *          but nothing is written. Defaults to '100MB'.
     *      - 'data' (bool|array) Request data to be added to log in JSON encoded 
     *          version. If TRUE then set to $_POST superglobal. It can be set explicitly 
     *          to any other array value (e.g. App::$data). If any empty (or non-array)
     *          value then omitted (to avoid log some sensitive data, e.g. password).
     *          ATTENTION: Do not use this to log variable value which should be
     *          added to log by 'var' option. Defaults to TRUE.
     */
    static function logError($message, $options = array()) {
        $defaults = array(
            'backtraceLevel' => 0,
        );
        $options = array_merge($defaults, $options);
        $options['backtraceLevel']++;
        return self::log('error', $message, $options);
    }

    /**
     * Logs to the warning log file.
     * The log file is placed under projects tmp/logs/ folder.
     *
     * @param string $message The message you want to log.
     * @param array $options Following can be used:
     *      - 'var' (mixed) Variable you want to log. Defaults to NULL.
     *      - 'type' (int) Debug type. Possible values are 'throwable', 'sqllog' 
     *          and NULL (for standard debugging).  If 'throwable' then provided $var 
     *          should be an instance of Throwable (Exception or Error). If 'sqllog' 
     *          then provided $var should be an sql log array returned by DB::getSqlLog(). 
     *          If empty and the 'var' is instance of Throwable then type is autoset
     *          to value 'throwable'. Defaults to NULL.
     *      - 'email' (bool) If TRUE then the sending of log message is allowed.
     *          The message is sent only in case that config App::$logEmail address is set.
     *      - 'backtraceLevel' (int) Which level of debug_backtrace() output should be displayed. 
     *          Defaults to 0 - means the most nested level is displayed.
     *      - 'file' (string) Explicit backtrace file. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     *      - 'line' (string) Explicit backtrace line. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     *      - 'maxSize' (integer|string) Log file max size limit. Can be set either
     *          by integer number of bytes e.g. 1024 or by human readable file size
     *          string, e.g. '1k'. If file is larger than 90% of here specified limit,
     *          then a warning email is sent to App::$logEmail (if any address provided).
     *          If log file is larger than specified limit then it is stopped to be written.
     *          ATTENTION: If no limit specified then a warning email is sent on each log
     *          but nothing is written. Defaults to '100MB'.
     *      - 'data' (bool|array) Request data to be added to log in JSON encoded 
     *          version. If TRUE then set to $_POST superglobal. It can be set explicitly 
     *          to any other array value (e.g. App::$data). If any empty (or non-array)
     *          value then omitted (to avoid log some sensitive data, e.g. password).
     *          ATTENTION: Do not use this to log variable value which should be
     *          added to log by 'var' option. Defaults to TRUE.
     */
    static function logWarning($message, $options = array()) {
        $defaults = array(
            'backtraceLevel' => 0,
        );
        $options = array_merge($defaults, $options);
        $options['backtraceLevel']++;
        return self::log('warning', $message, $options);
    }
    
    /**
     * Logs to the info log file.
     * The log file is placed under projects tmp/logs/ folder.
     *
     * @param string $message The message you want to log.
     * @param array $options Following can be used:
     *      - 'var' (mixed) Variable you want to log. Defaults to NULL.
     *      - 'type' (int) Debug type. Possible values are 'throwable', 'sqllog' 
     *          and NULL (for standard debugging).  If 'throwable' then provided $var 
     *          should be an instance of Throwable (Exception or Error). If 'sqllog' 
     *          then provided $var should be an sql log array returned by DB::getSqlLog(). 
     *          If empty and the 'var' is instance of Throwable then type is autoset
     *          to value 'throwable'. Defaults to NULL.
     *      - 'email' (bool) If TRUE then the sending of log message is allowed.
     *          The message is sent only in case that config App::$logEmail address is set.
     *      - 'backtraceLevel' (int) Which level of debug_backtrace() output should be displayed. 
     *          Defaults to 0 - means the most nested level is displayed.
     *      - 'file' (string) Explicit backtrace file. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     *      - 'line' (string) Explicit backtrace line. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     *      - 'maxSize' (integer|string) Log file max size limit. Can be set either
     *          by integer number of bytes e.g. 1024 or by human readable file size
     *          string, e.g. '1k'. If file is larger than 90% of here specified limit,
     *          then a warning email is sent to App::$logEmail (if any address provided).
     *          If log file is larger than specified limit then it is stopped to be written.
     *          ATTENTION: If no limit specified then a warning email is sent on each log
     *          but nothing is written. Defaults to '100MB'.
     *      - 'data' (bool|array) Request data to be added to log in JSON encoded 
     *          version. If TRUE then set to $_POST superglobal. It can be set explicitly 
     *          to any other array value (e.g. App::$data). If any empty (or non-array)
     *          value then omitted (to avoid log some sensitive data, e.g. password).
     *          ATTENTION: Do not use this to log variable value which should be
     *          added to log by 'var' option. Defaults to TRUE.
     */
    static function logInfo($message, $options = array()) {
        $defaults = array(
            'backtraceLevel' => 0,
        );
        $options = array_merge($defaults, $options);
        $options['backtraceLevel']++;
        return self::log('info', $message, $options);
    }
    
    /**
     * Logs to the debug log file.
     * The log file is placed under projects tmp/logs/ folder.
     *
     * @param string $message The message you want to log.
     * @param array $options Following can be used:
     *      - 'var' (mixed) Variable you want to log. Defaults to NULL.
     *      - 'type' (int) Debug type. Possible values are 'throwable', 'sqllog' 
     *          and NULL (for standard debugging).  If 'throwable' then provided $var 
     *          should be an instance of Throwable (Exception or Error). If 'sqllog' 
     *          then provided $var should be an sql log array returned by DB::getSqlLog(). 
     *          If empty and the 'var' is instance of Throwable then type is autoset
     *          to value 'throwable'. Defaults to NULL.
     *      - 'email' (bool) If TRUE then the sending of log message is allowed.
     *          The message is sent only in case that config App::$logEmail address is set.
     *      - 'backtraceLevel' (int) Which level of debug_backtrace() output should be displayed. 
     *          Defaults to 0 - means the most nested level is displayed.
     *      - 'file' (string) Explicit backtrace file. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     *      - 'line' (string) Explicit backtrace line. This is use internaly for 
     *          optimization in the case that backtrace info has been already retrieved. 
     *          Defaults to NULL.
     *      - 'maxSize' (integer|string) Log file max size limit. Can be set either
     *          by integer number of bytes e.g. 1024 or by human readable file size
     *          string, e.g. '1k'. If file is larger than 90% of here specified limit,
     *          then a warning email is sent to App::$logEmail (if any address provided).
     *          If log file is larger than specified limit then it is stopped to be written.
     *          ATTENTION: If no limit specified then a warning email is sent on each log
     *          but nothing is written. Defaults to '100MB'.
     *      - 'data' (bool|array) Request data to be added to log in JSON encoded 
     *          version. If TRUE then set to $_POST superglobal. It can be set explicitly 
     *          to any other array value (e.g. App::$data). If any empty (or non-array)
     *          value then omitted (to avoid log some sensitive data, e.g. password).
     *          ATTENTION: Do not use this to log variable value which should be
     *          added to log by 'var' option. Defaults to TRUE.
     */
    static function logDebug($message, $options = array()) {
        $defaults = array(
            'backtraceLevel' => 0,
        );
        $options = array_merge($defaults, $options);
        $options['backtraceLevel']++;
        return self::log('debug', $message, $options);
    }
    
    /**
     * Used to catch uncatched exceptions or errors
     *
     * @param Throwable $Throwable
     */
    static function catchThrowable(Throwable $Throwable) {
        $message = $Throwable->getMessage();
        self::debug($Throwable, $message);
        self::logError($message, array(
            'var' => $Throwable,
            'email' => true,
        ));
    }
    
    /**
     * Catchs php errors, warnings, notices, ...
     */
    static function catchError() {
        if(
            function_exists('error_get_last')
            && ($error = error_get_last()) !== null
            && (
                self::$errorCatching === true 
                || self::$errorCatching & $error['type']
            )
        ) {
            switch (Sanitize::value($error['type'])) {
                case E_ERROR:
                    $errorType = 'E_ERROR';
                    break;
                case E_WARNING:
                    $errorType = 'E_WARNING';
                    break;
                case E_PARSE:
                    $errorType = 'E_PARSE';
                    break;
                case E_NOTICE:
                    $errorType = 'E_NOTICE';
                    break;
                case E_CORE_ERROR:
                    $errorType = 'E_CORE_ERROR';
                    break;
                case E_CORE_WARNING:
                    $errorType = 'E_CORE_WARNING';
                    break;
                case E_COMPILE_ERROR:
                    $errorType = 'E_COMPILE_ERROR';
                    break;
                case E_COMPILE_WARNING:
                    $errorType = 'E_COMPILE_WARNING';
                    break;
                case E_USER_ERROR:
                    $errorType = 'E_USER_ERROR';
                    break;
                case E_USER_WARNING:
                    $errorType = 'E_USER_WARNING';
                    break;
                case E_USER_NOTICE:
                    $errorType = 'E_USER_NOTICE';
                    break;
                case E_STRICT:
                    $errorType = 'E_STRICT';
                    break;
                case E_RECOVERABLE_ERROR:
                    $errorType = 'E_RECOVERABLE_ERROR';
                    break;
                case E_DEPRECATED:
                    $errorType = 'E_DEPRECATED';
                    break;
                case E_USER_DEPRECATED:
                    $errorType = 'E_USER_DEPRECATED';
                    break;
                case E_ALL:
                    $errorType = 'E_ALL';
                    break;
                default:
                    $errorType = Sanitize::value($error['type']);
            }
            
            $message = $errorType . ': ' . Sanitize::value($error['message']);
            self::debug($message, $message, array(
                'file' => $error['file'],
                'line' => $error['line'],
            ));
            self::logError($message, array(
                'file' => $error['file'],
                'line' => $error['line'],
                'email' => true,
            ));
        }
    }
    
    /**
     * Allows in cooperation with App::endPhpErrorsHtmlCapture() to capture html 
     * code generated by php errors, warnings and notices. This code (sanitized from html)
     * is logged to error.log by method App::endPhpErrorsHtmlCapture()
     * 
     * This method must be called App::init() before any other start of output buffering.
     * 
     * @throws Exception if not called as very first capturing method or if called more than once.
     */
    static protected function startPhpErrorsHtmlCapture() {
        if (self::$outputBufferInitialLevel === null) {
            throw new Exception('You must call App::startPhpErrorsHtmlCapture() after initialization of App::$outputBufferInitialLevel');
        }
        if (ob_get_level() > self::$outputBufferInitialLevel) {
            throw new Exception('You must call App::startPhpErrorsHtmlCapture() before any other start of output buffering');
        }
        if (self::$phpErrorsHtmlCaptureStatus) {
            throw new Exception('You can call App::startPhpErrorsHtmlCapture() only once');
        }
        ob_start();
        self::$phpErrorsHtmlCaptureStatus = 'started';
    }
    
    /**
     * Allows in cooperation with App::startPhpErrorsHtmlCapture() to capture html 
     * code generated by php errors, warnings and notices. This code (sanitized from html)
     * is logged to error.log by method App::endPhpErrorsHtmlCapture()
     * 
     * This method must be called App::init() as very last one before outputing of request html in index.php
     * or before redirecting.
     * 
     * @param array $options Following are available:
     *          - 'closeOpenedOutputBuffers' (bool) If TRUE then all output buffers are closed before and after catching
     *              the errors html. If FALSE then the method can be called only after 
     *              the nested buffers are closed manually, otherwise an exception is thrown.
     *              Defaults to FALSE.
     * 
     * @throws Exception if not called properly.
     */
    static public function endPhpErrorsHtmlCapture($options = array()) {
        $options = array_merge(array(
            'closeOpenedOutputBuffers' => false,
        ), $options);
        $levels = ob_get_level();
        if (self::$phpErrorsHtmlCaptureStatus === 'ended') {
            throw new Exception('You can call App::endPhpErrorsHtmlCapture() only once');
        }
        if (self::$phpErrorsHtmlCaptureStatus !== 'started') {
            throw new Exception('You cannot call App::endPhpErrorsHtmlCapture() without calling at first App::startPhpErrorsHtmlCapture()');
        }
        if (
            !$options['closeOpenedOutputBuffers']
            && $levels > (self::$outputBufferInitialLevel + 1)
        ) {
            throw new Exception('You must call App::endPhpErrorsHtmlCapture() after any other end of output buffering or set the option \'closeOpenedOutputBuffers\' to TRUE');
        }
        if ($levels <= self::$outputBufferInitialLevel) {
            throw new Exception('Something went wrong. App::endPhpErrorsHtmlCapture() is called for output buffer level equal or lower to App::$outputBufferInitialLevel');
        }
        // close buffers opened after the errors-buffer
        if ($options['closeOpenedOutputBuffers']) {
            for ($i = self::$outputBufferInitialLevel + 1; $i < $levels; $i++) {
                $errorsHtml = ob_get_contents();
                ob_end_clean();
            }
        }
        // get errors-buffer content
        $errorsHtml = ob_get_contents();
        if ($errorsHtml) {
            $errors = Sanitize::htmlToText($errorsHtml, true);
            self::debug($errors, 'PHP errors, warnings and notices');
            self::logError('PHP errors, warnings and notices', array(
                'var' => $errors,
                'email' => true,
            ));
        }
        ob_end_clean();
        // close buffers opened before the errors-buffer (there should be max one)
        if ($options['closeOpenedOutputBuffers']) {
            $levels = ob_get_level();
            for ($i = 0; $i < $levels; $i++) {
                ob_end_clean();
            }
        }
        self::$phpErrorsHtmlCaptureStatus = 'ended';
    }
    
    /**
     * Initializes all the necessary stuff round translations to make
     * translation functions __(), __a(), __js(), __jsa() work correctly.
     * 
     * This is launched in App::init(). App::parseRequest() must be launched before
     * launching App::initI18n()
     * 
     * Sets also property App::$langIsInvalid to TRUE if lang provided in URL does 
     * not exists among available active languages
     * 
     * @throws Exception on missing or invalid languages table
     */
    static protected function initI18n() {
        
        // check if App::parseRequest() was already launched
        if (empty(self::$requestType)) {
            throw new Exception('App::initI18n() can be executed only after App::parseRequest()');
        }
        // retrieve App::$languages
        if (empty(self::$languagesTable)) {
            throw new Exception('App::$languagesTable is undefined');
        }
        // - from DB table named App::$languagesTable
        elseif (is_string(self::$languagesTable)) {
            self::$languages = DB::select(self::$languagesTable, array(
                'conditions' => array(
                    'active' => true,
                ),
                'order' => array(
                    'default DESC',
                    'sort ASC',
                ),
                'fields' => array(
                    'id',
                    'name',
                    'lang',
                    'code',
                    'locale',
                    'currency_code',
                    'icon',
                    'sort',
                    'localized_default',
                    'published',
                ),
            ));
        }
        // - from array App::$languagesTable
        elseif (is_array(self::$languagesTable)) {
            self::$languages = self::$languagesTable;
        }
        else {
            throw new Exception('Invalid App::$languagesTable');
        }
        // mark default language
        if (empty(self::$languages)) {
            throw new Exception('No App::$languages found');
        }
        $defaultLanguage = array_shift(self::$languages);
        self::$languages = array_merge(array('default' => $defaultLanguage), self::$languages);
           
        // prepare App::$langs
        self::$langs = array();
        $languagesByCode = array();
        foreach (self::$languages as $key => $language) {
            self::$langs[$key] = $language['lang'];
            $languagesByCode[$language['lang']] = $language;
        }
        
        // set App::$lang if empty
        $hasLangByDomain = false;
        if (empty(self::$initialLang)) {
            // set language by domain (e.g. on my-domain.cz the languge will be set to cs
            // even if there is no /cs/... language specified in URL)
            if (($langByDomain = self::getLangByDomain())) {
                $hasLangByDomain = true;
                self::$initialLang = $langByDomain;
            }
            // if autodetecting is on 
            elseif (self::$autodetectLang) {
                $locale = null;
                if (
                    self::$localizedLang
                    && strlen(self::$initialLang) == 2
                ) {
                    $locale = self::$initialLang;
                }
                self::$initialLang = self::autodetectLang($locale);
                
            }
            // otherwise just use default lang
            else {
                self::$initialLang = self::$langs['default'];
            }
        }
        
        // if App::$initialLang does not exist then remember it for further processing (e.g. display 404 screen)
        if (!isset($languagesByCode[self::$initialLang])) {
            self::$langIsInvalid = true;
            self::$initialLang = self::$langs['default'];
        }
        elseif (
            empty($languagesByCode[self::$initialLang]['published'])
            && empty($_COOKIE['_AllowUnpublishedLangsLocally_'])
        ) {
            self::$langIsNotPublished = true;
            self::$initialLang = self::$langs['default'];
        }
        
        // set App::$urlLang
        if (
            self::$defaultLangHidden 
            && (
                self::$initialLang === self::$langs['default']
                || $hasLangByDomain
            )
        ) {
            self::$urlLang = '';
        }
        else {
            self::$urlLang = self::$initialLang;
        }
        
        // set App::$lang and App::$locale
        self::setI18n();
    }
    
    /**
     * Sets app internationalization according to provided lang. If no lang specified 
     * then the app intenationalization is reset to initial lang.
     * 
     * ATTENTION: DO NOT set App::$lang and App::$locale directly. Always use this method.
     * 
     * The best pattern how to use this method is following
     * 
     *      $originalLang = App::setI18n('en');
     *      // code executed in EN
     *      // ...
     *      App::setI18n($originalLang);
     * 
     * @param string $lang Optional. Defaults to App::$initialLang
     * 
     * @return string Returns the actually set lang
     */
    static public function setI18n($lang = null) {
        if (empty($lang)) {
            $lang = self::$initialLang;
        }
        $langKey = array_search($lang, self::$langs);
        if ($langKey === false) {
            throw new Exception(sprintf('Invalid lang "%s"', $lang));
        }
        $originalLang = self::$lang;
        self::$lang = $lang;
        self::$locale = self::$languages[$langKey]['locale'];
        
//// If App::$initialLang is resolved from site domain by App::getLangByDomain()
//// then App::$urlBase very probably must be updated by App::setI18n().
        // set url base according to App::$lang
        if (empty(self::$urlBase)) {
            // when calling App::init() from phpunit test then App::$urlBase and 
            // App::$urlBaseWithRoot are empty
        }
        else {
            $allowedDomains = array(
                'cz', 'at', 'hu', 'ua', 'pl',
            );
            $localhostRegex = '/-(?:' . implode ('|', $allowedDomains) . ')$/i';
            $productionRegex = '/\.(?:' . implode ('|', $allowedDomains) . ')$/i';
            if (self::$lang === 'cs') {
                if (ON_LOCALHOST) {
                    if (preg_match($localhostRegex, self::$urlBase)) {
                        self::$urlBase = preg_replace($localhostRegex, '-cz', self::$urlBase);
                    }
                    else {
                        self::$urlBase .= '-cz';
                    }
                }
                else {
                    self::$urlBase = preg_replace($productionRegex, '.cz', self::$urlBase);
                }
            } 
            else {
                if (ON_LOCALHOST) {
                    if (preg_match($localhostRegex, self::$urlBase)) {
                        self::$urlBase = preg_replace($localhostRegex, '', self::$urlBase);
                    }
                }
                else {
                    self::$urlBase = preg_replace($productionRegex, '.sk', self::$urlBase);
                }
            }
            self::$urlBaseWithRoot = self::$urlBase . self::$urlRoot;
        }
        
        return $originalLang;
    }
    
    /**
     * Generic translation method used by __() and __() functions
     * 
     * NOTE: Use msgcat to merge more .po files into one and avoid duplicities. In list of 
     * msgcat input files introduce at first newer file(s): 
     * msgcat --use-first inputfile01.po inputfile02.po -o outputfile.po
     * NOTE: Use msguniq to remove duplicit messages definitions in .po file:
     * msguniq --use-first myfile.po -o myfile.po
     * For more utilities see http://www.gnu.org/software/gettext/manual/gettext.html#Manipulating
     * 
     * @param string|ModuleObject $module Name of module to use .po file from. Can be specified by
     *          - explicit module name, e.g. 'App'
     *          - absolute filepath of calling script. Use always __FILE__ constant in this case
     *          - (deprecated) ModuleObject instance. Inside of ModuleObject instance just pass $this here.
     *              This option is deprecated as it does not work together with inheritance 
     *              among modules
     * @param string $domain Transpation file domain definition under the specified module.
     *      E.g. Admin, Errors, Validations, ... Use NULL or empty string to omit the domain. 
     * @param string $singular String to be translated.
     * @param string $plural Optional. @todo - not implemented yet
     * @param int $count Optional. @todo - not implemented yet
     * @return string 
     */
    static public function translate($module, $domain, $singular, $plural = null, $count = null) {
        if ($module instanceof ModuleObject) {
            $module = $module->getPropertyModule();            
        }
        elseif (strpos($module, DS) !== false) {
            $module = self::getFileModule($module);
        }
        $domain = $module . $domain;
        $locale = self::$locale;
        // read translations from .po file
        if (!isset(self::$translations[$locale][$domain])) {
            if ($module == 'App') {
                $poFile = DS . 'app' . DS . 'locale' . DS . $domain . '_' . $locale . '.po';
                $phpFile = DS . 'app' . DS . 'locale' . DS . 'compiled' . DS . $domain . '_' . $locale . '.php';
            }
            else {
                $poFile = DS . 'app' . DS . 'modules' . DS . $module . DS . 'locale' . DS . $domain . '_' . $locale . '.po';
                $phpFile = DS . 'app' . DS . 'modules' . DS . $module . DS . 'locale' . DS . 'compiled' . DS . $domain . '_' . $locale . '.php';
            }
            $translations = array();
            if (is_readable(ROOT . $poFile)) {
                if (
                    !is_readable(ROOT . $phpFile)
                    || filemtime(ROOT . $poFile) >= filemtime(ROOT . $phpFile)
                ) {
                    self::compilePoFile($poFile);
                }
                self::loadScript($phpFile, array('catchVariables' => 'translations'), $vars);
                if (!empty($vars['translations'])) {
                    $translations = $vars['translations'];
                }
            }
            self::$translations[$locale][$domain] = $translations;
        }
        // return
        if (isset(self::$translations[$locale][$domain][$singular])) {
            return self::$translations[$locale][$domain][$singular];
        }
        return $singular;
    }
    
    /**
     * Compiles po file into .php or .js file. If .po filename ends by '_js' it
     * is compiled to .js file, e.g. AppErrors_sk_SK_js.po. Otherwise it is compiled to
     * .php file, e.g. AppErrors_sk_SK.po.
     * 
     * @param string $file App root relative path to .po file
     */
    static public function compilePoFile($file) {
        $compiledFile = File::getPathinfo($file);
        // resolve format, domain and locale
        $format = 'php';
        if (substr($compiledFile['filename'], -3) === '_js') {
            $format = 'js';
            $locale = substr($compiledFile['filename'], -8, -3);
            $domain = substr($compiledFile['filename'], 0, -9);
        }
        $compiledContent = File::compilePo($file, $format);
        // get compiled content
        if ($format === 'js') {
            $module = self::getPathModule($compiledFile['dirname']);
            $namespace = 'window.Run=window.Run||{};';
            $namespace .= 'window.Run.' . $module . '=window.Run.' . $module . '||{};';
            $namespace .= 'window.Run.' . $module . '.i18n=window.Run.' . $module . '.i18n||{};';
            $namespace .= 'window.Run.' . $module . '.i18n.' . $domain;
            $compiledContent = $namespace . '=' . $compiledContent . ';';
        }
        else {   
            $compiledContent = '<?php $translations=' . $compiledContent . '; ';
        }
        // write compiled content into compiled file
        $compiledFilePath = trim($compiledFile['dirname'], DS) . DS . 'compiled';
        $compiledFilePath = File::getAbsolutePath($compiledFilePath);
        if (!File::ensurePath($compiledFilePath, true)) {
            throw new Exception(__e(__FILE__, 'Compilation of %s to .%s has failed on creation of path %s', $file, $format, $compiledFilePath));
        }
        if ($format === 'js') {
            $compiledFile['filename'] = $domain . '_' . $locale;
        }
        $tmpFile = TMP . DS . $compiledFile['filename'] . uniqid('_tmp_') . '.' . $format;
        $compiledFile = $compiledFilePath . DS . $compiledFile['filename'] . '.' . $format;
        if (!file_put_contents($tmpFile, $compiledContent)) {
            throw new Exception(__e(__FILE__, 'Compilation of %s to .%s has failed on temporary file contents write', $file, $format, $compiledFilePath));
        }
        if (!rename($tmpFile, $compiledFile)) {
            throw new Exception(__e(__FILE__, 'Compilation of %s to .%s has failed on temporary file renaming', $file, $format, $compiledFilePath));
        }
    }
    
    /**
     * Gets config values from app or module config.php file, e.g.:
     *      - App::getConfig('App'); // will return whole app $config array
     *      - App::getConfig('MyModule', 'fbApiId'); // will return value of my_module $config['facebookUrl']
     *      - App::getConfig('MyModule', 'google.analytics'); // will return value of my_module $config['google']['analytics']
     * 
     * @param string $module Module name to read config for.
     * @param string $name Optional. Name of config item. If omitted (NULL) then 
     *      whole config array is returned. Defaults to NULL.
     * @param string $file Explicit name of config source file. The given path 
     *      must be relative to ROOT. If omitted (NULL) or empty then the default 
     *      config of given module is supposed. The default config is the first one 
     *      required for module. Defaults to NULL.
     * 
     * @return mixed 
     */
    static public function getConfig($module, $name = null, $file = null) {
        $pid = !isset(self::$configs[$module]) || empty($file) ? 'default' : $file;
        
        if (!isset(self::$configs[$module][$pid])) {
            if ($file) {
                $configFile = DS . ltrim($file, DS);
            }
            elseif ($module == 'App') {
                $configFile = DS . 'app' . DS . 'config' . DS . 'config.php';
            }
            else {
                $configFile = DS . 'app' . DS . 'modules' . DS . $module . DS . 'config' . DS . 'config.php';
            }
            if (is_readable(ROOT . $configFile)) {
                $vars = array();
                self::loadScript($configFile, array('catchVariables' => 'config'), $vars);
                if (!isset($vars['config'])) {
                    throw new Exception(__e(__FILE__, 'Missing $config variable in config file of module %s', $module));
                }
                self::$configs[$module][$pid] = $vars['config'];
            }
            elseif ($file) {
                throw new Exception(__e(__FILE__, 'Invalid config file %s for module %s', $file, $module));
            }
            else {
                throw new Exception(__e(__FILE__, 'Missing config file in module %s', $module));
            }
        }
                
        return Arr::getPath(self::$configs[$module][$pid], $name);
    }
        
    /**
     * Gets a value of specified app or module setting
     * 
     * @param string $module
     * @param string $pid 
     * @param array $options Following are available
     *      - 'translate' (bool) If TRUE then setting value is retrieved for provided lang.
     *          If FALSE then translation is turned off, means default lang values are used. 
     *          Defaults to TRUE.
     *      - 'lang' (string) Lang specification for value translation. 
     *          Defaults to actual app lang.
     * 
     * @return string Setting value
     */
    static public function getSetting($module, $pid, $options = array()) {
        $defaults = array(
            'translate' => true,
            'lang' => self::$lang,
        );
        $options = array_merge($defaults, $options);
        // if no translation then set the lang to default app lang
        if (!$options['translate']) {
            $options['lang'] = DEFAULT_LANG;
        }
        if (!isset(self::$settings[$module][$options['lang']])) {
            // get translation value field name
            // App class avoids to use Model related to basic app tables.
            // Translations are implemented by Model so App mimic the model 
            // behaviour here but even in beter way as it is made in Model
            // as we know the query exactly
            $valueField = 'value';
            if ($options['lang'] !== DEFAULT_LANG) {
                $enc = DB::getPropertyNameEnclosure();
                $valueField = $enc . Model::getTranslationField($valueField, $options['lang']) . $enc . ' AS ' . $enc . $valueField . $enc;
            }
            $settings = DB::select(
                self::$settingsTable,
                array(
                    'fields' => array('module','pid', $valueField),
                    'conditions' => array('module' => $module),
                )
            );
            self::$settings[$module][$options['lang']] = array();
            foreach ($settings as $setting) {
                self::$settings[$setting['module']][$options['lang']][$setting['pid']] = $setting['value'];
            }
        }
        
        if (isset(self::$settings[$module][$options['lang']][$pid])) {
            return self::$settings[$module][$options['lang']][$pid];
        }
        return null;
    }    
    
    /**
     * Sets a value of specified app or module setting
     * 
     * @param string $module
     * @param string $pid 
     * @param string $value
     * @param array $options Following are available
     *      - 'translate' (bool) If TRUE then setting value is set for provided lang.
     *          If FALSE then translation is turned off, means default lang values are set. 
     *          Defaults to TRUE.
     *      - 'lang' (string) Lang specification for value translation. 
     *          Defaults to actual app lang.
     * 
     * @return return The old setting value is returned
     */
    static public function setSetting($module, $pid, $value, $options = array()) {    
        $defaults = array(
            'translate' => true,
            'lang' => self::$lang,
        );
        $options = array_merge($defaults, $options);
        // if no translation then set the lang to default app lang
        if (!$options['translate']) {
            $options['lang'] = DEFAULT_LANG;
        }
        
        // if module setting for given lang are not loaded then load them
        // by inquiring provided setting
        if (!isset(self::$settings[$module][$options['lang']])) {
            self::getSetting($module, $pid, $options);
        }
        
        // get translation value field name
        // App class avoids to use Model related to basic app tables.
        // Translations are implemented by Model so App mimic the model 
        // behaviour here but even in beter way as it is made in Model
        // as we know the query exactly
        $valueField = 'value';
        if ($options['lang'] !== DEFAULT_LANG) {
            $valueField = Model::getTranslationField($valueField, $options['lang']);
        }            
        if (array_key_exists($pid, self::$settings[$module][$options['lang']])) {
            $settingValue = self::$settings[$module][$options['lang']][$pid];
            DB::update(
                self::$settingsTable,
                array($valueField => $value),
                array(
                    'conditions' => array(
                        'module' => $module,
                        'pid' => $pid,
                    )
                )
            );
        }
        else {
            $settingValue = null;
            DB::insert(
                self::$settingsTable,
                array(
                    'module' => $module,
                    'pid' => $pid,
                    $valueField => $value,
                )
            );
        }
        self::$settings[$module][$options['lang']][$pid] = $value;
        
        return $settingValue;
    }    
    
    /**
     * Reloads all settings or reloads settings of provided module(s)
     * 
     * @param string|array $modules Optional. Single module name or array of module names 
     *      to reload settings for. Defaults to NULL.
     */
    static public function reloadSettings($modules = null) {
        if (empty($modules)) {
            self::$settings = array();
        }
        else {
            $modules = (array)$modules;
            foreach ($modules as $module) {
                if (
                    is_string($module)
                    && isset(self::$settings[$modules])
                ) {
                    self::$settings[$modules] = null;
                }
            }
        }
    }
    
    /**
     * Returns rights array defined in rights.php for specified $module
     * 
     * @param string $module
     * @param bool $edit Optional. If TRUE then rights are read with defined headers 
     *      and labels. This is used in case for rights editing.In this case rights 
     *      are not stored in cache. Defaults to FALSE.
     * 
     * @return array Rights array
     * 
     * @throws Exception if module has no right.php file
     */
    static public function getModuleRights($module, $edit = false) {
        if (!empty($edit) || !isset(self::$rights[$module])) {
            if ($module == 'App') {
                $rightsFile = DS . 'app' . DS . 'config' . DS . 'rights.php';
            }
            else {
                $rightsFile = DS . 'app' . DS . 'modules' . DS . $module . DS . 'config' . DS . 'rights.php';
            }
            if (!is_readable(ROOT . $rightsFile)) {
                throw new Exception(__e(__FILE__, 'Missing rights file in module %s', $module));
            }
            self::loadScript(
                $rightsFile, 
                array(
                    'catchVariables' => 'rights', 
                    'variables' => array('edit' => $edit),
                ), 
                $vars
            );
            if (!isset($vars['rights'])) {
                throw new Exception(__e(__FILE__, 'Missing $rights variable in rights file of module %s', $module));
            }
            // store the rights to cache only in case that they are retrieved in non-editable version
            if (empty($edit)) {
                self::$rights[$module] = $vars['rights'];
            }
        }
        if (!empty($edit)) {
            return $vars['rights'];
        }
        return self::$rights[$module];
    }
    
    /**
     * Check if $requestor has rights on given subject
     * 
     * Params listed here below are represented in module rights.php files like:
     *      array(
     *          {requestorGroup} => array(
     *              {subjectType} => array(
     *                  {subjectName} => TRUE,
     *                  {subjectName} => array(
     *                      {subjectAction} => TRUE,
     *                  )
     *              )
     *          )
     *      )
     * 
     * @param array|string $requestor Requestor user array (returned by App::getUser())
     *      or just group pid ('admins', 'webmasters', ...) string. In case of dbscripts 
     *      subject type only array form is accepted. If you would like to apply user custom 
     *      rights then pass the array returned by App::getUser(). If no user is authenticated 
     *      then use an empty value for requestor (it will be internally set to 'public').
     * @param string $module Module name, e.g.: 'App', 'Eshop'
     * @param string $subjectType Accessed subject type, one of: 'screen', 'screenaction', 'element'
     *      'controlleraction', 'dbscript', 'setting' or 'content'.
     * @param string $subjectName Accessed subject name, e.g.: 'google_map', 'Users.amin_create'
     *      or 'my-content-slug'.
     * @param string $subjectAction | $lang Optional. Accessed subject action, one of: 'create',
     *      'read', 'update', 'delete'. In case of 'content' $subjectType the content lang 
     *      can be passed here. Defaults to NULL (in case of $lang defaults to App::$lang)
     * 
     * @return boolean
     * @throws Exception
     */
    static public function getRights($requestor, $module, $subjectType, $subjectName, $subjectAction = null) {
        if ($subjectType === 'dbscript') {
            // admin has rights for everything
            if (
                $requestor['Group']['pid'] === 'admins'
                || $requestor['Group']['pid'] === 'administrators'
            ) {
                return true;
            }
            // get the script    
            $script = DB::select('run_scripts', array(
                'conditions' => array('module' => $module,'pid' => $subjectName),
                'field' => array('users', 'groups'),
                'first' => true,
            ));
            if ($script) {
                $usernames = trim($script['users'], ', ');
                $groupPids = trim($script['groups'], ', ');
                if ($usernames || $groupPids) {
                    $usernames = array_filter(array_map(Str::explode(',', $usernames), array('trim')));
                    $groupPids = array_filter(array_map(Str::explode(',', $groupPids), array('trim')));
                    // check for case when no restrictions are defined
                    if (!$usernames && !$groupPids) {
                        return true;
                    }
                    // check if requestor has rights for script
                    elseif (
                        !empty($requestor)
                        && (
                            in_array($requestor['username'], $usernames)
                            || in_array($requestor['Group']['pid'], $groupPids)
                        )
                    ) {
                        return true;
                    }
                }
            }
        }
        elseif ($subjectType === 'content') {
            $lang = empty($subjectAction) ? self::$lang : $subjectAction;
            // retrieve content form DB according to provided slug ($subjectName)
            $content = self::getContentByLocator($subjectName, array('lang' => $lang));
            // if no content found then return FALSE
            if (!$content) {
                return false;
            }
            // load snippets
            $snippets = self::parseTextSnippets($content['text']);
            if (!empty($snippets)) {
                $processedSnippets = array();
                foreach ($snippets as $snippet) {
                    // do not load snippet if it was already loaded
                    if (!isset($processedSnippets[$snippet[0]])) {
                        // Pass retrieved page content to each snippet as param '_content'
                        // (whole is under index '0', snippet body (name + params) is under index '1')
                        $parsedSnippet = self::parseSnippet($snippet[1]);
                        if (empty($parsedSnippet)) {
                            continue;
                        }
                        if (!self::getRights(
                            $requestor, 
                            $parsedSnippet['module'], 
                            $parsedSnippet['type'], 
                            $parsedSnippet['name']
                        )) {
                            return false;
                        }
                        $processedSnippets[$snippet[0]] = true;
                    }
                }
            }
            return true;
        }
        else {
            // normalize requestor and check for requestor custom rights
            $customRights = null;
            if (empty($requestor)) {
                $requestor = 'public';
            }
            elseif (is_array($requestor)) {
                if (
                    !empty($requestor['rights'])
                    && is_array($requestor['rights'])
                ) {
                    $customRights = $requestor['rights'];
                }
                $requestor = Sanitize::value($requestor['Group']['pid']);
            }
     
            // load module rights from file if not yet
            if (!isset(self::$rights[$module])) {
                self::getModuleRights($module);
            }

            // get the rights
            // validate subject type
            $subjectTypes = array(
                'element' => true,
                'screen' => true,
                'screenaction' => true,
                'controlleraction' => true,
                'setting' => true,
            );
            if (!isset($subjectTypes[$subjectType])) {
                throw new Exception("Invalid subject type {$subjectType}");
            }
            if ($subjectAction === null) {
                // requestor has rights if...
                if (
                    // subject is public...
                    !empty(self::$rights[$module]['public'][$subjectType][$subjectName])
                    || 
                    // or there are no custom rights but group has rights for subject...
                    $customRights === null
                    && !empty(self::$rights[$module][$requestor][$subjectType][$subjectName])
                    || 
                    // or there are custom rights and...
                    $customRights !== null
                    && (
                        // requestor has custom rights for subject...
                        !empty($customRights[$module][$subjectType][$subjectName])
                        ||
                        // or custom rights does not contain subject but group has rights for subject
                        !isset($customRights[$module][$subjectType][$subjectName])
                        && !empty(self::$rights[$module][$requestor][$subjectType][$subjectName])    
                    )
                ) {
                    return true;
                }
            }
            else {
                // validate subject actions
                $subjectActions = array(
                    'create' => true,
                    'read' => true,
                    'update' => true,
                    'delete' => true,
                );
                if (!isset($subjectActions[$subjectAction])) {
                    throw new Exception("Invalid subject action {$subjectAction}");
                }
                if (
                    !empty(self::$rights[$module]['public'][$subjectType][$subjectName][$subjectAction])
                    || 
                    $customRights === null
                    && !empty(self::$rights[$module][$requestor][$subjectType][$subjectName][$subjectAction])  
                    ||
                    $customRights !== null
                    && (
                        !empty($customRights[$module][$subjectType][$subjectName][$subjectAction])
                        ||
                        !isset($customRights[$module][$subjectType][$subjectName][$subjectAction])
                        && !empty(self::$rights[$module][$requestor][$subjectType][$subjectName][$subjectAction])    
                    )
                ) {
                    return true;
                }
            }   
        }
        return false;
    }
    
    /**
     * Check if current app user has rights on given subject.
     * 
     * This is shortcut for App::getRights(App::getUser(), ...)
     * 
     * Params listed here below are represented in module rights.php files like:
     *      array(
     *          {requestorGroup} => array(
     *              {subjectType} => array(
     *                  {subjectName} => TRUE,
     *                  {subjectName} => array(
     *                      {subjectAction} => TRUE,
     *                  )
     *              )
     *          )
     *      )
     * 
     * @param string $module Module name, e.g.: 'App', 'Eshop'
     * @param string $subjectType Accessed subject type, one of: 'screen', 'screenaction', 'element'
     *      'controlleraction', 'dbscript', 'setting' or 'content.
     * @param string $subjectName Accessed subject name, e.g.: 'google_map', 'Users.amin_create'
     * @param string $subjectAction Optional. Accessed subject action, one of: 'create',
     *      'read', 'update', 'delete',
     * 
     * @return boolean
     * @throws Exception
     */
    static public function getUserRights($module, $subjectType, $subjectName, $subjectAction = null) {
        return self::getRights(self::getUser(), $module, $subjectType, $subjectName, $subjectAction);
    }
    
    /**
     * Check if current app user has rights on provided URL.
     * 
     * ATTENTION: This method returns TRUE for all external URLs.
     * 
     * @param string|array $url URL to check rights for
     * 
     * @return bool
     */
    static public function getUserUrlRights($url) {
        if (!is_array($url)) {
            $url = self::parseUrl($url);
        }
        elseif (!empty($url['locator'])) {
            $url = self::parseUrl($url['locator']);
        }
        if (
            !empty($url['base'])
            && Validate::externalUrl($url['base'])
        ) {
            return true;
        }
        elseif (
            !empty($url['type'])
            && $url['type'] === 'mvc'
            ||
            empty($url['type'])
            && !empty($url['module'])
            && !empty($url['controller'])
            && !empty($url['action'])
        ) {
            return self::getUserRights($url['module'], 'controlleraction', $url['controller'] . '.' . $url['action']);
        }
        // else 'type' === 'slug'
        elseif (!empty($url['slug'])) { 
            if (self::hasScreen($url['slug'])) {
                return self::getUserRights('App', 'screen', $url['slug']);
            }
            else {
                return self::getUserRights('App', 'content', $url['slug']);
            }
        }
        return false;
    }
    
    /**
     * Checks if given subject is public
     * 
     * @param string $module Module name, e.g.: 'App', 'App'
     * @param string $subjectType Accessed subject type, one of: 'screen', 'screenaction', 'element'
     *      'controlleraction', 'dbscript', 'setting'
     * @param string $subjectName Accessed subject name, e.g.: 'google_map', 'Users.amin_create'
     * 
     * @return bool
     * @throws Exception on invalid $subjectType value
     */
    static public function hasPublicRights($module, $subjectType, $subjectName) {
        // validate subject type
        $subjectTypes = array(
            'element' => true,
            'screen' => true,
            'screenaction' => true,
            'dbscript' => true,
            'controlleraction' => true,
            'setting' => true,
        );
        if (!isset($subjectTypes[$subjectType])) {
            throw new Exception(__e(__FILE__, 'Invalid subject type %s', $subjectType));
        }
        // load module rights form file if not yet
        if (!isset(self::$rights[$module])) {
            if ($module == 'App') {
                $rightsFile = DS . 'app' . DS . 'config' . DS . 'rights.php';
            }
            else {
                $rightsFile = DS . 'app' . DS . 'modules' . DS . $module . DS . 'config' . DS . 'rights.php';
            }
            if (!is_readable(ROOT . $rightsFile)) {
                throw new Exception(__e(__FILE__, 'Missing rights file in module %s', $module));
            }
            self::loadScript($rightsFile, array('catchVariables' => 'rights'), $vars);
            if (!isset($vars['rights'])) {
                throw new Exception(__e(__FILE__, 'Missing $rights variable in rights file of module %s', $module));
            }
            self::$rights[$module] = $vars['rights'];
        }
        // check if provided subject is public
        return !empty(self::$rights[$module]['public'][$subjectType][$subjectName]);
    }
    
    /**
     * Returns data of provided subject
     * 
     * @param string $module Module name, e.g.: 'App', 'App'
     * @param string $subjectType Accessed subject type, one of: 'screen', 'screenaction', 'element'
     *      'controlleraction', 'dbscript', 'setting'
     * @param string $subjectName Accessed subject name, e.g.: 'google_map', 'Users.amin_create'
     * 
     * @return array Data of provided subject. If App::data has other _target then provided
     *      subject then an empty array is returned.
     */
    static public function getSubjectData($module, $subjectType, $subjectName) {
        switch ($subjectType) {
            case 'screen':
            case 'screenaction':
                $subjectName = 's.' . $subjectName;
                break;
            case 'element':
                $subjectName = 'e.' . $module . '.' . $subjectName;
                break;
            case 'dbscript':
                $subjectName = 'd.' . $module . '.' . $subjectName;
                break;
            case 'controlleraction':
                $subjectName = $module . '.' . $subjectName;
                break;
            default:
                throw new Exception("Invalid subjectType $subjectType");
        }
        if (
            empty(self::$data['_target']) 
            || in_array($subjectName, (array)self::$data['_target'])
        ) {
            return self::$data;
        }
        return array();
    }
    
    /**
     * Gets application global value
     * 
     * @param string $module Name of module which uses specified global value, e.g. 'App', 'Eshop'. 
     * @param string $name Global value name
     * @return mixed 
     */
    static public function getGlobal($module, $name = null) {  
        if (!isset(self::$globals[$module])) {
            self::$globals[$module] = array();
        }
        return Arr::getPath(self::$globals[$module], $name);
    }
    
    /**
     * Sets application global value
     * 
     * @param string $module Name of module which uses specified global value, e.g. 'App', 'Eshop'. 
     * @param string $name Global value name
     * @param mixed $value Global value
     * @return mixed The old glbal value is returned
     */
    static public function setGlobal($module, $name, $value) {
        if (!isset(self::$globals[$module])) {
            self::$globals[$module] = array();
        }
        
        return Arr::setPath(self::$globals[$module], $name, $value);
    }
    
    /**
     * Resolves the module name for given file path. If no file path is
     * given then the actual script path is considered.
     * 
     * NOTE: Differences between App::getPathModule() and App::getFileModule() are:
     * - App::getPathModule() accepts both directory and file paths. It does not 
     *      use cache internally.
     * - App::getFileModule() accepts only file paths (for directories it may return
     *      invalid results). It uses cache and App::getPathModule() internally.
     *      Is is optimized to resolve module in App::translate() which is called
     *      many times during application execution.
     * 
     * @param string $file Optional. Absolute or app root relative path to resolve 
     *      the module name. If omitted then backtrace is used to retrieve actual 
     *      script path. To optimize you can use App::getFileModule(__FILE__).
     * 
     * @return mixed Module name (string) or FALSE on failure.
     */
    static public function getFileModule($file = null) {
        static $modules = array(); //cache
        
        if ($file === null) {
            if (PHP_VERSION_ID >= 50400) {
                $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3);
            }
            elseif (PHP_VERSION_ID >= 50306) {
                $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
            }
            else {
                $backtrace = debug_backtrace();
            }
            $file = Sanitize::value($backtrace[0]['file']);
        }
        // cut out the file itself (to make the path more reusable in caching)
        $filePath = dirname($file) . DS;
        if (!isset($modules[$filePath])) {
            $modules[$filePath] = self::getPathModule($filePath);
        }
        return $modules[$filePath];
    }
    
    /**
     * Resolves the module name for given class name
     * 
     * @param string $className Name of class to resolve module name
     * 
     * @return string Module name
     * 
     * @throws Exception if ReflectionClass does not exist
     */
    static public function getClassModule($className) {
        if (!class_exists('ReflectionClass')) {
            throw new Exception("ReflectionClass does not exists");
        }
        $reflector = new ReflectionClass($className);
        $file = $reflector->getFileName();
        // cut out the file itself (to make the path more reusable in caching)
        $filePath = substr($file, 0, strrpos($file, DS));
        return self::getFileModule($filePath);
    }
    
    /**
     * Launches (includes) a script and possibly can return it output.
     * 
     * NOTE: This method can be used also to catch variables created by loaded script.
     * Advance of this method agains to plain include/require is that this method
     * can catch also script output and so prevent from adding undesired content to
     * output buffer.
     * 
     * @param string $script Path to script to be launched. The path must be 
     *      app root relative, e.g.: '/screens/home.php'
     * @param array $options Array which can contain some of following options:
     *      - 'variables' (array) Associaltive array of variables to be passed to script. 
     *          This can be easily created by compact() function, e.g.: compact('varForScript1', '$varForScript2', ...)
     *          or it can be created manualy, e.g.: array('var1' => 'its value', 'var2' => 'its value' , ...)
     *          Defaults to NULL - means no variables are passed.
     *      - 'catchOutput' (bool) If TRUE then html code generated by script 
     *          is catched and returned. If FALSE then the created html is send standardly 
     *          directly to output. Defaults to TRUE.
     *      - 'catchVariables' (string|array) A single variable name or an array
     *          of such names to be catched after the $script is loaded. They
     *          are returned by aux output $catchedVariables. If the specified variable
     *          does not exist (is not created by $script) then it is not present
     *          in $catchedVariables. If this option is any empty value then nothing is 
     *          catched. Defaults to NULL.
     *      - 'once' (bool) If TRUE then the script is loaded using require_once(),
     *          otherwise require() is used. Defaults to FALSE.
     *      - 'normalize' (bool) If TRUE then the script path is normalized. 
     *          Defaults to TRUE.
     *      - 'extension' (string) Extension to be forced on provided script name.
     *          Defaults to NULL. 
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return void|string Void if input $options['catchOutput'] is FALSE.  String if 
     *      input $options['catchOutput'] is TRUE.
     */
    static public function loadScript($script, $options = array(), &$catchedVariables = array()) {
        $defaults = array(
            'variables' => null,
            'catchOutput' => true,
            'catchVariables' => null,
            'once' => false,
            'normalize' => true,
            'extension' => null,
        );
        $_options = array_merge($defaults, $options);
        $_script = $script;
        if ($_options['normalize']) {
            $_script = File::normalizePath($_script, $_options['extension']);
        }
        $_script = ROOT . DS . $_script;
        // check if script exists
        if (!is_readable($_script)) {
            throw new Exception(__e(__FILE__, 'Invalid script name: \'%s\'', $_script));
        }
        // prepare script params
        if (!empty($_options['variables']) && is_array($_options['variables'])) {
            // avoid possible overwrite of loaded script name or options
            if (array_key_exists('_script', $_options['variables'])) {
                unset($_options['variables']['_script']);
            }
            if (array_key_exists('_options', $_options['variables'])) {
                unset($_options['variables']['_options']);
            }
            extract($_options['variables']);
        }
        // check for invalid capture endings - intitial and final capture state must be 
        // the same. If the script was not loaded inside capture then no capture should be 
        // detected after loading the script
        $_openedCssCapturesCount = self::$openedCssCapturesCount;
        $_openedJsCapturesCount = self::$openedJsCapturesCount;
        $_openedHeadHtmlCapturesCount = self::$openedHeadHtmlCapturesCount;
        $_openedBodyEndHtmlCapturesCount = self::$openedBodyEndHtmlCapturesCount;
        $_openedHtmlCapturesCount = Html::getPropertyOpenedCapturesCount();
        // catch the script output if it is required
        if ($_options['catchOutput']) {
            ob_start();
        }
        try {
            if ($_options['once']) {
                require_once $_script;
            }
            else {
                require $_script;
            }
        }
        catch (Throwable $e) {
            self::catchThrowable($e);
        }
        // catch the script output if it is required
        if ($_options['catchOutput']) {
            $output = ob_get_contents();
            ob_end_clean();
        }
        else {
            $output = null;
        }
        if (!empty($_options['catchVariables'])) {
            $catchedVariables = compact((array)$_options['catchVariables']);
        }
        // check for invalid capture endings (see the comment above)
        if ($_openedCssCapturesCount < self::$openedCssCapturesCount) {
            throw new Exception(__e(__FILE__, 'Missing end of css capture block in %s', $_script));
        }
        if ($_openedCssCapturesCount > self::$openedCssCapturesCount) {
            throw new Exception(__e(__FILE__, 'Redundant end of css capture block in %s', $_script));
        }
        if ($_openedJsCapturesCount < self::$openedJsCapturesCount) {
            throw new Exception(__e(__FILE__, 'Missing end of js capture block in %s', $_script));
        }
        if ($_openedJsCapturesCount > self::$openedJsCapturesCount) {
            throw new Exception(__e(__FILE__, 'Redundant end of js capture block in %s', $_script));
        }
        if ($_openedHeadHtmlCapturesCount < self::$openedHeadHtmlCapturesCount) {
            throw new Exception(__e(__FILE__, 'Missing end of html head capture block in %s', $_script));
        }
        if ($_openedHeadHtmlCapturesCount > self::$openedHeadHtmlCapturesCount) {
            throw new Exception(__e(__FILE__, 'Redundant end of html head capture block in %s', $_script));
        }
        if ($_openedBodyEndHtmlCapturesCount < self::$openedBodyEndHtmlCapturesCount) {
            throw new Exception(__e(__FILE__, 'Missing end of html BodyEnd capture block in %s', $_script));
        }
        if ($_openedBodyEndHtmlCapturesCount > self::$openedBodyEndHtmlCapturesCount) {
            throw new Exception(__e(__FILE__, 'Redundant end of html BodyEnd capture block in %s', $_script));
        }
        if ($_openedHtmlCapturesCount < Html::getPropertyOpenedCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Missing end of html capture block in %s', $_script));
        }
        if ($_openedHtmlCapturesCount > Html::getPropertyOpenedCapturesCount()) {
            throw new Exception(__e(__FILE__, 'Redundant end of html capture block in %s', $_script));
        }
        return $output;
    }
    
    /**
     * Resolves module name for provided path
     * 
     * NOTE: Differences between App::getPathModule() and App::getFileModule() are:
     * - App::getPathModule() accepts both directory and file paths. It does not 
     *      use cache internally.
     * - App::getFileModule() accepts only file paths (for directories it may return
     *      invalid results). It uses cache and App::getPathModule() internally.
     *      Is is optimized to resolve module in App::translate() which is called
     *      many times during application execution.
     * 
     * @param string $path Absolute or app root relative path
     * 
     * @return string Module name or FALSE if nothing resolved
     */
    static public function getPathModule($path) {
        // if absolute path then get app root relative part
        if (substr($path, 0, ($rootLen = strlen(ROOT))) === ROOT) {
            $path = substr($path, $rootLen);
        }
        $appPath = DS . 'app' . DS;
        $modulesPath = $appPath . 'modules' . DS;
        if (($pos = strpos($path, $modulesPath)) !== false) {
            $module = substr($path, $pos + 13);
            $module = explode(DS, $module);
            $module = reset($module);
        }
        elseif (strpos($path, $appPath) !== false) {
            $module = 'App';
        }
////mojo: test - use this to implement test modules, comment it out in production env  
//        elseif (
//            ($testsPath = DS . 'tests' . DS)
//            && strpos($path, $testsPath) !== false
//        ) {
//            $module = '_' . $module;
//        }
        else {
            $module = false;
        }
        return $module;
    }
    
    /**
     * Returns a path to given module. The path is ROOT relative, without trailing 
     * slash, e.g. for module 'App' the path is '/app', for module
     * 'MyModule' the path is '/app/modules/MyModule'.
     * 
     * @param string $module
     * @return string
     */
    static public function getModulePath($module) {
        if ($module == 'App') {
            $path = DS . 'app';
        }
        else {
            $path = DS . 'app' . DS . 'modules' . DS . $module;
        }
        return $path;
    }
    
    /**
     * Returns a url path to given module. The path is URL_ROOT relative, without 
     * trailing slash, e.g. for module 'App' the path is '/app', for module
     * 'MyModule' the path is '/app/modules/MyModule'.
     * 
     * Diference between App::getModulePath() and App::getUrlModulePath() is in
     * separators. Url path has only '/' as separator, filepath can be separated 
     * both by '/' (linux) and '/' windows
     * 
     * @param string $module
     * @return string
     */
    static public function getModuleUrlPath($module) {
        if ($module == 'App') {
            $path = '/app';
        }
        else {
            $path = '/app/modules/' . $module;
        }
        return $path;
    }
    
    /**
     * Returns a namespace full qualificator for given module. , e.g. for module 'App' 
     * the namespace is '\Fajn\App\', for module 'MyModule' the namespace is '\Fajn\MyModule\'.
     * Namespace contains the trailing backslash.
     * 
     * @param string $module
     * @return string
     */
    static public function getModuleNamespace($module) {
        return '\Fajn\\' . $module . '\\';
    }
        
    /**
     * Returns list of application modules like:
     * 
     *      array(
     *          '{Module01}' => '{Module01}',
     *          '{Module02}' => '{Module02}',
     *          ...,
     *      )
     * 
     * @param array $options Following are available:
     *      - 'initialized' (bool) If TRUE then only initialized modules are returned. 
     *          Module is considered to be initialized if it has module class and 
     *          the class is loaded (the class exists) or if it has no module class 
     *          (it means it does not require any initialization). Defaults to FALSE
     *          it means all modules are returned.
     * 
     * @return array The array described here above
     * 
     * @throws Exception if modules dir is not readable
     */
    static public function getModules($options = array()) {
        $options = array_merge(array(
            'initialized' => false,
        ), $options);
        if (self::$modules === null) {
            self::$modules = array('App' => 'App');
            $modulesDir = ROOT . rtrim(self::getModulePath(''), DS);
            if (!($dh = opendir($modulesDir))) {
                throw new Exception(__e(__FILE__, 'Unreadable modules dir %s', $modulesDir));
            }
            while (($entry = readdir($dh)) !== false) {
                if (
                    is_dir($modulesDir . DS . $entry)
                    && $entry !== '.' 
                    && $entry !== '..'
                ) {          
                    self::$modules[$entry] = $entry;
                }      
            }
            closedir($dh);
            ksort(self::$modules);
        }
        if (!$options['initialized']) {
            return self::$modules;
        }
        return array_filter(self::$modules, function($module) {
            return self::hasModule($module, array('initialized' => true));
        });
    }
    
    /**
     * Check if application has specified $module
     * 
     * @param string $module
     * @param array $options Following are available:
     *      - 'initialized' (bool) If TRUE then it is checked if the module is 
     *          initialized. Module is considered to be initialized if it has 
     *          module class and the class is loaded (the class exists) or if it has 
     *          no module class (it means it does not require any initialization). 
     *          Defaults to FALSE.
     * 
     * @return bool
     */
    static public function hasModule($module, $options = array()) {
        $options = array_merge(array(
            'initialized' => false,
        ), $options);
        $moduleDir = ROOT . self::getModulePath($module);
        return 
            // check for existence of the module
            is_dir($moduleDir) && is_readable($moduleDir) 
            // check if it is initialized (if required)
            && (
                !$options['initialized'] 
                || !file_exists($moduleDir  . DS . 'libs' . DS . 'default' . DS . $module . '.php')
                || class_exists($module)
            );
    }
    
    /**
     * Checks if application has either namespaced or plain version of provided $class 
     * in specified $module. Using auxiliary output $name you can get final version
     * of its name (it either contains or not the namespace part). Using auxiliary 
     * output $namespaced you can find out if the $name is namespaced or not.
     * 
     * NOTE: Use plain class_exists() for all other cases.
     * 
     * @param string $module Module name to check namespaced version of class for.
     * @param string $class Class name WITHOUT namespace, e.g. if class is namespaced 
     *          like \Fajn\App\Form() then enter here 'Form'.
     * @param string& $name Optional. Passed by reference. Auxiliary output to get the 
     *      final name of the class. Consider this output only if the method returns TRUE.
     * @param bool& $namespaced Optional. Passed by reference. Auxiliary output to get 
     *      info if the specified class is namespaced or not. Consider this output only 
     *      if the method returns TRUE.
     * 
     * @return bool Returns FALSE if class does not exist. Returns TRUE if class 
     * exists (either plain or namespaced).
     */
    static public function hasClass($module, $class, &$name = null, &$namespaced = null) {
        if (
            PHP_VERSION_ID >= 50300
            && class_exists(($name = self::getModuleNamespace($module) . $class))
        ) {
            $namespaced = true;
            return true;
        }
        elseif (class_exists($class)) {
            $name = $class;
            $namespaced = false;
            return true;
        }
        $name = null;
        $namespaced = null;
        return false;
    }
    
    /**
     * Loads the module class (if any) and calls the module initialization method 
     * {Module}::init() defined in file {Module}/libs/default/{Module}.php
     * The initialization of each module is done only once.
     * 
     * @param string $module
     * 
     * @return bool Has been the module class succesfully loaded or no?
     */
    static public function loadModule($module) {
        if (!isset(self::$initializedModules[$module])) {
            self::$initializedModules[$module] = array('success' => true);
            $modulePath = self::getModulePath($module);
            $moduleClassFile = ltrim($modulePath . DS . 'libs' . DS . 'default' . DS . $module . '.php', DS);
            try {
                self::loadScript($moduleClassFile, array(
                    'once' => true, 
                    'normalize' => false, 
                )); 
            }
            catch (Throwable $e) {
                // module has no module class script - silently do nothing
                self::$initializedModules[$module]['success'] = false;
            } 
            if (self::$initializedModules[$module]['success']) {
                if (self::hasClass($module, $module, $class)) {
                    // launch init method of module class
                    if (method_exists($class, 'init')) {
                        call_user_func(array($class, 'init'));
                    }
                    // register shutdown method of module class
                    if (method_exists($class, 'shutdown')) {
                        self::$initializedModules[$module]['shutdown'] = array($class, 'shutdown');
                    }
                    // register login method of module class
                    if (method_exists($class, 'login')) {
                        self::$initializedModules[$module]['login'] = array($class, 'login');
                    }
                    // register logout method of module class
                    if (method_exists($class, 'logout')) {
                        self::$initializedModules[$module]['logout'] = array($class, 'logout');
                    }
                }
            }
        }
        return self::$initializedModules[$module]['success'];
    }
    
    
    /**
     * Returns list of application models in array like:
     * 
     *      array(
     *          '{Module01}' => array(
     *              '{Model01}' => {Model01},
     *              '{Model02}' => {Model02},
     *              ...
     *          ),
     *          '{Module01}' => array(
     *              '{Model03}' => {Model03},
     *              '{Model04}' => {Model04},
     *              ...
     *          ),
     *          ...,
     *      )
     * 
     * If the module is specified then form of retirned array is:
     * 
     *      array(
     *          '{Model01}' => {Model01},
     *          '{Model02}' => {Model02},
     *          ...
     *      ),
     * 
     * @params string $module Optional. Module to return models for
     * 
     * @return array The array described here above. If provided modules has no
     *      models or the module name is invalid then empty array is returned.
     * 
     * @throws Exception if modules dir is not readable
     */
    static public function getModels($module = null) {
        if (self::$models === null) {
            self::$models = array();
            // ensure that modules are loaded
            self::getModules();
            foreach(self::$modules as $m) {
                self::$models[$m] = array();
                $modelsDir = ROOT . self::getModulePath($m) . DS . 'models';
                if (!is_dir($modelsDir)) {
                    continue;
                }
                if (!($dh = opendir($modelsDir))) {
                    throw new Exception(__e(__FILE__, 'Unreadable models dir %s', $modelsDir));
                }
                while (($entry = readdir($dh)) !== false) {
                    // take only files with first letter uppercased and having extension .php 
                    if (
                        is_file($modelsDir . DS . $entry)
                        && $entry[0] === strtoupper($entry[0])
                        && ($pathInfo = File::getPathInfo($entry))
                        && strtolower($pathInfo['extension']) === 'php'
                    ) {       
                        $model = $pathInfo['filename'];
                        self::$models[$m][$model] = $model;
                    }      
                }
                closedir($dh);
                ksort(self::$models[$m]);
            }
        }
        if ($module) {
            return (array)Sanitize::value(self::$models[$module]);
        }
        return self::$models;
    }
    
    /**
     * Check if application has specified $model under provided $module
     * 
     * @param string $module
     * @param string $model
     * 
     * @return bool
     */
    static public function hasModel($module, $model) {
        $modelFile = ROOT . self::getModulePath($module) . DS . 'models' . DS . $model . '.php';
        return is_file($modelFile) && is_readable($modelFile);
    }
    
    /**
     * Returns model name for provided controller name.
     * E.g. for 'Users' returns 'User', for 'Contries' returns 'Country', for
     * 'MySpecialController' returns FALSE.
     * 
     * ATTENTION: Existence of model having returned name is not verified.
     * 
     * @param string $controllerName
     * 
     * @return string|boolean Model name or FALSE if midel name resolution has
     *      failed (atypical controller class name)
     */
    static public function getModelNameFromControllerName($controllerName) {
        if (substr($controllerName, -3) === 'ies') {
            return substr($controllerName, 0, -3) . 'y';
        }
        if (substr($controllerName, -1) === 's') {
            return substr($controllerName, 0, -1);
        }
        return false;
    }
    
    /**
     * Loads specified library from /app/libs/ or /app/modules/{Module}/libs/ folder.
     * 
     * This method can be used anywhere you need to load a new library. If the library
     * has been already loaded then App::loadLib() will do nothing.
     * 
     * @param string $module Name of module. Use 'App' for application level or 
     *      name of folder placed under /app/modules/
     * 
     * @param string $script Path to library script placed under libs folder. 
     */
    static public function loadLib($module, $script) {
        $script = File::normalizePath($script, 'php');
        $modulePath = self::getModulePath($module);
        $script = ltrim($modulePath . DS . 'libs' . DS . $script, DS);
        try {
            self::loadScript($script, array(
                'once' => true, 
                'normalize' => false, 
            )); 
        }
        catch (Throwable $e) {
            throw new Exception("Invalid lib script name: '$script'");
        } 
    }
    
    /**
     * Loads specified vendor (= external) library from /app/vendors/ or /app/modules/{Module}/vendors/ folder.
     * 
     * This method can be used anywhere you need to load a new vendor library. 
     * If the library has been already loaded then App::loadVendor() will do nothing.
     * 
     * @param string $module Name of module. Use 'App' for application level or 
     *      name of folder placed under /app/modules/
     * 
     * @param string $script Path to vendor library script placed under vendors folder. 
     */
    static public function loadVendor($module, $script) {
        $script = File::normalizePath($script, 'php');
        $modulePath = self::getModulePath($module);
        $script = ltrim($modulePath . DS . 'vendors' . DS . $script, DS);
        try {
            self::loadScript($script, array(
                'once' => true, 
                'normalize' => false, 
            )); 
        }
        catch (Throwable $e) {
            throw new Exception("Invalid vendor script name: '$script'");
        } 
    }
    
    /**
     * Loads specified model from /app/models/ or /app/modules/{Module}/models/ folder.
     * 
     * Use controller __construct() method to preload controller model(s), 
     * e.g. in Users conroller:
     * 
     *      public function __construct(){
     *          parent::__construct();
     *          App::loadModel('App', 'User');
     *      }
     * 
     * This method can be used anywhere you need to load a new model. If the model
     * has been already loaded then App::loadModel() will do nothing.
     * 
     * @param string $module Name of module. Use 'App' for application level or
     * name of folder placed under /app/modules/
     * @param string $name Model class name. It can be given also with path under 
     *      models/ folder, e.g. 'SpecialModels/MySpecialModel'.
     * @param bool $getInstance If TRUE then a model instance is returned.
     *      ATTENTION: Always the same (singleton) instance of model is returned!
     *      Defaults to FALSE.
     * 
     * @return void|Model If $getInstance is TRUE the a singleton instance of model is returned.
     *      Otherwise void. On invalid model specification an Exception is thrown.
     * 
     * @throws Exception
     */
    static public function loadModel($module, $name, $getInstance = false) {
        if (!isset(self::$Models[$module][$name])) { // singleton
            $className = explode(DS, $name);
            $className = array_pop($className);
            if (!self::hasModel($module, $name)) {
                throw new Exception("Invalid model '$name' under module '$module'");
            }
            if (!class_exists($className)) {
                $script = File::normalizePath($name, 'php');
                $modulePath = self::getModulePath($module);
                $script = ltrim($modulePath . DS . 'models' . DS . $script, DS);
                try {
                    self::loadScript($script, array(
                        'once' => true, 
                        'normalize' => false, 
                    )); 
                }
                catch (Throwable $e) {
                    throw new Exception("Invalid model '$name' under module '$module'");
                } 
            }
        }
        if ($getInstance) { 
            if (!isset(self::$Models[$module][$name])) { // singleton
                if (!class_exists($className)) {
                    throw new Exception("Model {$className} class does not exist");
                }
                self::setModel($module, $name, new $className); // singleton
            }
            return self::$Models[$module][$name];
        }
    }
    
    /**
     * Loads specified controller from /app/controllers/ or /app/modules/{Module}/controllers/ folder.
     * 
     * This method can be used anywhere you need to load a new controller. If the controller
     * has been already loaded then App::loadController() will do nothing.
     * 
     * @param string $module Name of module. Use 'App' for application level or
     * name of folder placed under /app/modules/
     * @param string $name Controller class name. It can be given also with path under 
     *      controllers/ folder, e.g. 'SpecialControllers/MySpecialController'.
     * @param bool $getInstance If TRUE then a controller instance is returned.
     *      ATTENTION: Always the same (singleton) instance of controller is returned!
     *      Defaults to FALSE.
     * 
     * @return void|Controller If $getInstance is TRUE the a singleton instance 
     *      of controller is returned. Otherwise void.
     */
    static public function loadController($module, $name, $getInstance = false) {
        if (!isset(self::$Controllers[$module][$name])) { // singleton
            $className = explode(DS, $name);
            $className = array_pop($className);
            if (!class_exists($className)) {
                $script = File::normalizePath($name, 'php');
                $modulePath = self::getModulePath($module);
                $script = ltrim($modulePath . DS . 'controllers' . DS . $script, DS);
                try {
                    self::loadScript($script, array(
                        'once' => true, 
                        'normalize' => false, 
                    )); 
                }
                catch (Throwable $e) {
                    throw new Exception("Invalid controller '$name' under module '$module'");
                } 
            }
        }
        if ($getInstance) {
            if (!isset(self::$Controllers[$module][$name])) { // singleton
                if (!class_exists($className)) {
                    throw new Exception("Controller {$className} class does not exist");
                }
                self::setController($module, $name, new $className); // singleton
            }
            return self::$Controllers[$module][$name];
        }
    }
    
    /**
     * Loads script placed under /app/views/ or /app/modules/{Module}/views/ folder
     * and returns it's html output.
     * 
     * NOTE: Use $this->displayOriginComment = TRUE; to display origin comments of view 
     * if they are allowed (see App config 'allowOriginComments')
     *
     * @param string $module Name of module. Use 'App' for application level or
     * name of folder placed under /app/modules/
     * @param string $name Name of script name placed under views folder.
     * @param array $params Optional. Associaltive array of view params available as $this->params in view. 
     *      This can be easily created by compact() function, e.g.: compact('paramForScript1', 'paramForScript2', ...)
     *      or it can be created manualy, e.g.: array('param1' => 'its value', 'param2' => 'its value' , ...)
     *      Defaults to empty array().
     * @param array $data Optional. View data available as $this->data in view. 
     *      Defaults to empty array().
     * @param string|array $catchVariables Optional. A single variable name or an array
     *      of such names to be catched after the view is loaded. They
     *      are returned by aux output $catchedVariables. If the specified variable
     *      does not exist (is not created by view) then it is not present
     *      in $catchedVariables. If this option is any empty value then nothing is 
     *      catched. Defaults to NULL.
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return string Html output generated by the script. 
     */
    static public function loadView($module, $name, $params = array(), $data = array(), $catchVariables = null, &$catchedVariables = array()) {
        $Template = new Template($module, 'view', $name, $params, $data);
        return $Template->load(self::$allowOriginComments, $catchVariables, $catchedVariables); 
    }
    
    /**
     * Loads script placed under /app/elements/ or /app/modules/{Module}/elements/ folder and returns it's
     * html output.
     * 
     * NOTE: Use $this->displayOriginComment = TRUE; to display origin comments of element 
     * if they are allowed (see App config 'allowOriginComments')
     *
     * @param string $module Name of module. Use 'App' for application level or 
     *      name of folder placed under /app/modules/
     * @param string $name Name of script name placed under elements folder.
     * @param array $params Optional. Associaltive array of element params available as $this->params in element. 
     *      This can be easily created by compact() function, e.g.: compact('paramForScript1', 'paramForScript2', ...)
     *      or it can be created manualy, e.g.: array('param1' => 'its value', 'param2' => 'its value' , ...)
     *      Defaults to empty array() - means no params are passed.
     * @param array $data Optional. Element data available as $this->data in element. 
     *      If TRUE then data are retrieved by App::getSubjectData() from request data. 
     *      Defaults to TRUE.
     * @param string|array $catchVariables Optional. A single variable name or an array
     *      of such names to be catched after the element is loaded. They
     *      are returned by aux output $catchedVariables. If the specified variable
     *      does not exist (is not created by element) then it is not present
     *      in $catchedVariables. If this option is any empty value then nothing is 
     *      catched. Defaults to NULL.
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return string Html output generated by the script. 
     */
    static public function loadElement($module, $name, $params = array(), $data = true, $catchVariables = null, &$catchedVariables = array()) {
        if ($data === true) {
            $data = self::getSubjectData($module, 'element', $name);
        }
        $Template = new Template($module, 'element', $name, $params, $data);
        return $Template->load(self::$allowOriginComments, $catchVariables, $catchedVariables); 
    }
    
    /**
     * Loads specified Controller::action() and returns its output.
     * 
     * @param string $module Name of controller module. Use name of folder placed under /app/modules/
     * @param string $controller Controller class name.
     * @param string $action Action name must correspond exactly with required action() name.
     * @param array $params Optional. Associaltive array of action dispatch params. 
     * @param array $data Optional. Action dispatch data. If TRUE then data are retrieved 
     *      by App::getSubjectData() from request data. Defaults to TRUE.
     * @param array|mixed $args Controller dispatch args or single arg int|float|string. 
     *      They are used as input arguments for $action method
     * 
     * @return mixed Controller action output.
     */
    static public function loadControllerAction($module, $controller, $action, $params = array(), $data = true, $args = array()) {
        if ($data === true) {
            $data = self::getSubjectData($module, 'controlleraction', $controller . '.' . $action);
        }
        $Controller = self::loadController($module, $controller, true);
        return $Controller->loadAction($action, $params, $data, $args, self::$allowOriginComments);
    }
    
    /**
     * Loads specified Content block front-end view.
     * 
     * @param string $module Name of content block module. Mostly this will be 'ContentBlock' module.
     * @param string $contentBlock Content block model class name.
     * @param array $data Content block raw data passed to ContentBlock::prepareViewData()
     *          and then to content block frontend view. This can be an instance
     *          data (in common case) or an explicit data to use the content block 
     *          view for some custom purpose (kind of "element"). There is no owner 
     *          in case of explicit data, so no "owner" options should be set in such a case.
     * @param array $options Options passed to ContentBlock::prepareViewData(). Following are available:
     *      - 'ownerModel' (string) Block owner model. To ensure uniquity the model name should 
     *          be qualified by its module, e.g. 'App.WebContent', 'Eshop.EshopProduct'. 
     *          It is up to each ContentBlock definition how this is used/processed 
     *          or maybe ignored here. This should be set only in case of instance data.
     *          Defaults to NULL.
     *      - 'ownerId' (string) Block owner id. It is up to each ContentBlock definition
     *          how this is used/processed or maybe ignored here. This should be set only 
     *          in case of instance data. Defaults to NULL.
     *      - 'ownerRecord' (array) Block owner record data. It is up to each ContentBlock definition
     *          how this is used/processed or maybe ignored here. This should be set only 
     *          in case of instance data. Defaults to NULL.
     * 
     * @return string Content block front-end view html.
     */
    static public function loadContentBlockView($module, $contentBlock, $data = array(), $options = array()) {
        $ContentBlock = App::loadModel($module, $contentBlock, true);
        if (
            !class_exists('ContentBlock')
            || !($ContentBlock instanceof ContentBlock)
        ) {
            throw new Exception (__e(__FILE__, 'Model %s must be a child class of ContentBlock', $contentBlock));
        }
        return $ContentBlock->loadView($data, $options);
    }
    
    /**
     * Loads script form DB and returns its HTML output.
     * 
     * NOTE: Use $this->displayOriginComment = TRUE; to display origin comments of db script 
     * if they are allowed (see App config 'allowOriginComments')
     *
     * @param string $module Name of module. Use 'App' for application level or 
     *      name of folder placed under /app/modules/
     * @param string $name
     * @param array $params DBScript params available as $this->params in script.
     *      Defaults to empty array().
     * @param array $data Optional. DBScript data available as $this->data in view. 
     *      If TRUE then data are retrieved by App::getSubjectData() from request data. 
     *      Defaults to TRUE.
     * @param string|array $catchVariables Optional. A single variable name or an array
     *      of such names to be catched after the dbscript is loaded. They
     *      are returned by aux output $catchedVariables. If the specified variable
     *      does not exist (is not created by dbscript) then it is not present
     *      in $catchedVariables. If this option is any empty value then nothing is 
     *      catched. Defaults to NULL.
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return string Html string output generated by the script. 
     */
    static public function loadDBScript($module, $name, $params = array(), $data = true, $catchVariables = null, &$catchedVariables = array()) {
        if ($data === true) {
            $data = self::getSubjectData($module, 'dbscript', $name);
        }
        $Template = new Template($module, 'dbscript', $name, $params, $data);
        return $Template->load(self::$allowOriginComments, $catchVariables, $catchedVariables); 
    }
    
    /**
     * Returns list of existing App screens
     * 
     * Only filenames are returned (dirnames and extensions are ommited).
     * 
     * @return array
     */
    static public function getScreens() {
        $files = scandir(ROOT . DS . 'app' . DS . 'screens');
        $screens = array();
        foreach($files as $file) {
            if (is_file(ROOT . DS . 'app' . DS . 'screens' . DS . $file)) {
                $screens[] = File::getPathinfo($file, PATHINFO_FILENAME);
            }
        }
        return $screens;
    }
    
    /**
     * Checks if provided screen name exists
     * 
     * @param string $name Name of screen
     * @return bool
     */
    static public function hasScreen($name) {
        $script = File::normalizePath($name, 'php');
        return is_readable(ROOT . DS . 'app' . DS . 'screens' . DS . $script);
    }
    
    /**
     * Loads script placed under /app/screens/ folder and returns it's
     * html output.
     * 
     * NOTE: Use $this->displayOriginComment = TRUE; to display origin comments of screen 
     * if they are allowed (see App config 'allowOriginComments')
     * 
     * @param string $name Name of script name placed under /app/screens/ folder.
     * @param array $params Optional. Associaltive array of screen params available as $this->params in screen. 
     *      This can be easily created by compact() function, e.g.: compact('varForScript1', '$varForScript2', ...)
     *      or it can be created manualy, e.g.: array('var1' => 'its value', 'var2' => 'its value' , ...)
     *      Defaults to empty array().
     * @param array $data Optional. Screen data available as $this->data in screen. 
     *      If TRUE then data are retrieved by App::getSubjectData() from request data. 
     *      Defaults to TRUE.
     * @param array $args Optional. Screen args available as $this->args in screen.
     *      Defaults to empty array().
     * @param string|array $catchVariables Optional. A single variable name or an array
     *      of such names to be catched after the screen is loaded. They
     *      are returned by aux output $catchedVariables. If the specified variable
     *      does not exist (is not created by screen) then it is not present
     *      in $catchedVariables. If this option is any empty value then nothing is 
     *      catched. Defaults to NULL.
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return string Html string output generated by the script.
     */
    static public function loadScreen($name, $params = array(), $data = true, $args = array(), $catchVariables = null, &$catchedVariables = array()) {
        if ($data === true) {
            $data = self::getSubjectData('App', 'screen', $name);
        }
        $scriptName = str_replace('..', '', $name); // do not allow to go out from controllers folder
        $scriptName = str_replace(array('/', '\\'), DS, $scriptName); // make it windows compatibile
        if (strlen($scriptName) < 4 || substr($scriptName, -4) != '.php') {
            $scriptName .= '.php';
        }
        $scriptPath = DS . 'app' . DS . 'screens' . DS . $scriptName;
        if (!is_readable(ROOT . $scriptPath)) {
            throw new Exception("Invalid screen script: '{$name}'");
        }
        // load the screen
        $Template = new Template('App', 'screen', $name, $params, $data, $args);
        try {
            return $Template->load(self::$allowOriginComments, $catchVariables, $catchedVariables); 
        }
        catch (Throwable $e) {
            throw new Exception("Invalid screen script name: '$name'");
        }   
    }
        
    /**
     * Loads content from DB according to provided $options
     * 
     * ATTENTION: Do not use model names to qualify fields in conditions and order.
     * This method do not know anything about WebContent model. Fields are qualified 
     * by table name.
     * 
     * ATTTENTION: This method changes temporarily App::$content property to make the
     * the owner content available for loaded content blocks and snippets (via App::getPropertyContent())
     * 
     * @param string $id
     * @param string|array $options An array of following options:
     *      - 'conditions' (array) 
     *      - 'order' (string|array)
     *      - 'ignoreSoftDeleted' (bool) If TRUE then specified content may not be 
     *          soft deleted. Defaults to TRUE.
     *      - 'loadSnippets' (bool) If TRUE then returned text of content will contain loaded snippets. Defaults to TRUE.
     *      - 'obfuscate' (string|bool) If 'allow' then content text obfuscation is allowed 
     *          and done according to value of 'obfuscate' field. If TRUE then all 
     *          content texts are obfuscated regardless to value of 'obfuscate' field. 
     *          If FALSE then content text obfuscation is denied regardless to value 
     *          of 'obfuscate' field. Obfuscation is done after snippets are loaded. 
     *          Defaults to config App.obfuscateContentText.
     * 
     * @return array|bool Retrieved content record array or FALSE if no content found
     */
    static public function getContent($options = array()) {
        $defaults = array(
            'conditions' => array(),
            'ignoreSoftDeleted' => true,
            'loadSnippets' => true,
            'obfuscate' => self::$obfuscateContentText,
        );
        $options = array_merge($defaults, $options);
        
        if ($options['ignoreSoftDeleted']) {
            $options['conditions'] = (array)$options['conditions'];
            $options['conditions'] = DB::nestConditions($options['conditions']);
            $options['conditions'][self::$contentsTable . '.deleted'] = null;
        }
        // force first
        $options['first'] = true;
        $content = DB::select(self::$contentsTable, $options);
        
        // if to content fount then return false
        if (!$content) {
            return false;
        }
        
        // parse parent ids of actual content
        if (!empty($content['path'])) {
            $content['parent_ids'] = Model::getParentIdsFromTreePath($content['path']);
        }
        
        // add content blocks
        if (
            !empty($content['id'])
            && array_key_exists('text', $content)
        ) {
            $contentBackup = self::$content;
            self::$content = $content;
            /** @var ContentBlockInstance $BlockInstance */
            $BlockInstance = App::loadModel('App', 'ContentBlockInstance', true);
            $content['text'] .= $BlockInstance->loadOwnerInstancesViews('App.WebContent', $content['id'], array(
                'ownerRecord' => $content,
            ));
            self::$content = $contentBackup;
        }
        
        // load snippets
        if (
            $options['loadSnippets']
            && !empty($content['text'])
        ) {
            $contentBackup = self::$content;
            self::$content = $content;
            // Pass retrieved page content to each snippet as param '_content'
            $content['text'] = self::loadTextSnippets($content['text'], array('params' => array('_content' => $content)));
            self::$content = $contentBackup;
        }
        if (
            $options['loadSnippets']
            && !empty($content['has_side_content'])
            && !empty($content['side_text'])
        ) {
            $contentBackup = self::$content;
            self::$content = $content;
            $content['side_text'] = self::loadTextSnippets($content['side_text'], array('params' => array('_content' => $content)));
            self::$content = $contentBackup;
        }
        
        // obfuscate
        $autodetectionDone = false;
        if (
            !empty($content['text'])
            && (
                $options['obfuscate'] === true
                ||
                $options['obfuscate'] === 'allow'
                && (
                    !empty($content['obfuscate'])
                    ||
                    ($autodetectionDone = (
                        !empty($content['autodetect_obfuscate'])
                        && array_key_exists('obfuscate', $content)
                        && $content['obfuscate'] === null)
                    )
                    && ($content['obfuscate'] = Validate::stringWithEmail($content['text']))
                )
            )
        ) {
            $content['text'] = Str::obfuscateHtml($content['text']);
        }
        // save the result of autodetection to make it only the first time after 
        // the content has been changed
        if ($autodetectionDone) {
            DB::update(
                self::$contentsTable,
                array(
                    'obfuscate' => $content['obfuscate']
                ),
                array(
                    'conditions' => array(
                        'id' => $content['id']
                    ),
                    'reserve' => false,
                )
            );
        }
        
        return $content;
    }
        
    /**
     * Loads content from DB according to provided id
     * 
     * ATTTENTION: This method changes temporarily App::$content property to make the
     * the owner content available for loaded content blocks and snippets (via App::getPropertyContent())
     * 
     * @param string $id
     * @param string|array $options An array of following options:
     *      - 'lang' (string) Lang code to retrieve content for. Defaults to App::$lang.
     *      - 'loadSnippets' (bool) If TRUE then returned text of content will contain loaded snippets. Defaults to TRUE.
     *      - 'obfuscate' (string|bool) If 'allow' then content text obfuscation is allowed 
     *          and done according to value of 'obfuscate' field. If TRUE then all 
     *          content texts are obfuscated regardless to value of 'obfuscate' field. 
     *          If FALSE then content text obfuscation is denied regardless to value 
     *          of 'obfuscate' field. Obfuscation is done after snippets are loaded. 
     *          Defaults to config App.obfuscateContentText.
     *      - 'publishedOnly' (bool) If TRUE then specified content must be active. 
     *          Defaults to TRUE.
     *      - 'ignoreSoftDeleted' (bool) If TRUE then specified content may not be 
     *          soft deleted. Defaults to TRUE.
     * 
     * @return array|bool Retrieved content record array or FALSE if no content found
     */
    static public function getContentById($id, $options = array()) {
        $defaults = array(
            'publishedOnly' => true,
        );
        $options = array_merge($defaults, $options);
        // create conditions
        $options['conditions'] = array(
            self::$contentsTable . '.id' => $id,
        );
        if ($options['publishedOnly']) {
            $options['conditions'][self::$contentsTable . '.active'] = true;
        }
        return self::getContent($options);
    }
        
    /**
     * Loads content from DB according to provided pid and lang
     * 
     * ATTTENTION: This method changes temporarily App::$content property to make the
     * the owner content available for loaded content blocks and snippets (via App::getPropertyContent())
     * 
     * @param string $pid
     * @param string|array $options An array of following options:
     *      - 'lang' (string) Lang code to be used in URL. If FALSE then the lang 
     *          is explicitly turned off. Defaults to App::$lang.
     *      - 'loadSnippets' (bool) If TRUE then returned text of content will contain loaded snippets. Defaults to TRUE.
     *      - 'obfuscate' (string|bool) If 'allow' then content text obfuscation is allowed 
     *          and done according to value of 'obfuscate' field. If TRUE then all 
     *          content texts are obfuscated regardless to value of 'obfuscate' field. 
     *          If FALSE then content text obfuscation is denied regardless to value 
     *          of 'obfuscate' field. Obfuscation is done after snippets are loaded. 
     *          Defaults to config App.obfuscateContentText.
     *      - 'publishedOnly' (bool) If TRUE then specified content must be active. 
     *          Defaults to TRUE.
     *      - 'ignoreSoftDeleted' (bool) If TRUE then specified content may not be 
     *          soft deleted. Defaults to TRUE.
     * 
     * @return array|bool Retrieved content record array or FALSE if no content found
     */
    static public function getContentByPid($pid, $options = array()) {
        $defaults = array(
            'lang' => self::$lang,
            'publishedOnly' => true,
        );
        $options = array_merge($defaults, $options);
        // retrieve content form DB according to provided $slug
        $options['conditions'] = array(
            self::$contentsTable . '.pid' => $pid,
            self::$contentsTable . '.lang' => $options['lang'],
        );
        if ($options['publishedOnly']) {
            $options['conditions'][self::$contentsTable . '.active'] = true;
        }
        return self::getContent($options);
    }
        
    /**
     * Loads content from DB according to provided slug and lang
     * 
     * ATTTENTION: This method changes temporarily App::$content property to make the
     * the owner content available for loaded content blocks and snippets (via App::getPropertyContent())
     * 
     * @param string $locator
     * @param string|array $options An array of following options:
     *      - 'lang' (string) Lang code to retrieve content for. Defaults to App::$lang.
     *      - 'loadSnippets' (bool) If TRUE then returned text of content will contain loaded snippets. 
     *          Defaults to TRUE.
     *      - 'obfuscate' (string|bool) If 'allow' then content text obfuscation is allowed 
     *          and done according to value of 'obfuscate' field. If TRUE then all 
     *          content texts are obfuscated regardless to value of 'obfuscate' field. 
     *          If FALSE then content text obfuscation is denied regardless to value 
     *          of 'obfuscate' field. Obfuscation is done after snippets are loaded. 
     *          Defaults to config App.obfuscateContentText.
     *      - 'publishedOnly' (bool) If TRUE then specified content must be active. 
     *          Defaults to TRUE.
     *      - 'ignoreSoftDeleted' (bool) If TRUE then specified content may not be 
     *          soft deleted. Defaults to TRUE.
     * 
     * @return array|bool Retrieved content record array or FALSE if no content found
     */
    static public function getContentByLocator($locator, $options = array()) {
        $defaults = array(
            'lang' => self::$lang,
            'publishedOnly' => true,
        );
        $options = array_merge($defaults, $options);
        // retrieve content form DB according to provided $slug
        $options['conditions'] = array(
            self::$contentsTable . '.locator' => $locator,
            self::$contentsTable . '.lang' => $options['lang'],
        );
        if ($options['publishedOnly']) {
            $options['conditions'][self::$contentsTable . '.active'] = true;
        }
        return self::getContent($options);
    }
    
    /**
     * Retrieves snippet codes contained in provided $text and returns then in array 
     * like (for 'tag' syntax):
     * 
     *      array(
     *          array(
     *              // snippet html code
     *              0 => '<object _snippet="{snippetName01}" args.0="{arg}" {param}="{value}" ...></object>',
     *              // snippet params code
     *              1 => '_snippet="{snippetName01}" args.0="{arg}" {param}="{value}" ...',
     *          ),
     *          array(
     *              0 => '<object _snippet="{snippetName02}" args.0="{arg}" {param}="{value}" ...></object>',
     *              1 => '_snippet="{snippetName02}" args.0="{arg}" {param}="{value}" ...',
     *          ),
     *          ...,
     *      )
     * 
     * or (for 'brackets' syntax):
     * 
     *      array(
     *          array(
     *              // snippet html code
     *              0 => '[[{snippetName01}?{arg}/{param}:{value}/...]]',
     *              // snippet params code
     *              1 => '{snippetName01}?{arg}/{param}:{value}/...',
     *          ),
     *          array(
     *              0 => '[[{snippetName02}?{arg}/{param}:{value}/...]]',
     *              1 => '{snippetName02}?{arg}/{param}:{value}/...',
     *          ),
     *          ...,
     *      )
     * 
     * @param string $text Text containing snippets to be loaded.
     * @param array $options Following are available
     *      - 'syntax' (string) Type of snippets syntax. If 'tag' then snippets are 
     *          defined like <object _snippet="{snippetName}" args.0="{arg}" {param}="{value}" ...></object>. 
     *          If 'brackets' then snippets are defined like [[{snippetName}?{arg}/{param}:{value}/...]]
     *          Defaults to 'tag'.
     * 
     * @return array Array of retrieved snippet codes like above
     */
    static public function parseTextSnippets($text, $options = array()) {
        $defaults = array(
            'syntax' => 'tag',
        );
        $options = array_merge($defaults, $options);
        if ($options['syntax'] == 'tag') {
            $regex = '/<object +([^>]*?_snippet="[^>]+)>[^<]*<\/object>/i';
        }
        elseif ($options['syntax'] == 'brackets') {
            $regex = '/\[\[([^\]]+)\]\]/';
        }
        $snippets = array();
        preg_match_all($regex, $text, $snippets, PREG_SET_ORDER);
        return $snippets;
    }
    
    /**
     * Parses provided element, screen, dbscript or controlleraction snippets and returns 
     * an array contaning snippet parameters 
     * 
     * @param string $snippet Snippet definition with following conventions:
     * 
     *          {snippetSubject}[?{arg}[/{param}:{value}[/...]]]
     * 
     *      or as attributes retrieved from snippet tag (the real name of tag is not important):
     * 
     *          _snippet="{snippetSubject}" [passed.0="{arg}" [{param}="{value}" [...]]]
     * 
     *      Snippet subject specifies the source and has following conventions 
     *      based on the type of the source:
     *  
     *      1] Subjects of snippets requesting to load an element, screen or a database script
     *      have following form:
     * 
     *          e.{ModuleName}.{element_script_name}
     *          s.{screen_script_name}
     *          d.{ModuleName}.{database_script_name}
     * 
     *      Script names are WITHOUT extension.
     *      If no source is defined then database script (d.) is the default source.
     *  
     *      2] Subjects of MVC snippets requesting to load a controller::action() 
     *      have following form:
     * 
     *          {ModuleName}.{ControllerName}.{actionName}
     *       
     *      Passed params can be provided in first syntax just as unnamed params 
     *      and in second syntax using 'args' attributes, e.g. args.0="1" args.1="123". 
     *      If they are defined then they are used instead of App::$args
     *      If no passed params are defined then App::$args are used.
     *      
     *      Named params are created merging provided $params, snippet params 
     *      and App::$params (in order as listed here).
     *      App::$data are passed as controller action data, but only in the case
     *      that App::$data['_target'] equals to snippet subject or it is empty or not defined.
     * 
     *      Moreover snippet definition can use following snippet behaviour params:
     *          - '_snippet_ignore_on_no_rights' (bool) If the user has no rights on
     *              snippet subject and this param is set to 1 then rather than redirect user
     *              to login page the snippet is simply ignored.
     *          - '_snippet_after_others_loaded' (int) If set then snippet is loaded only
     *              after other snippets in text are already parsed and loaded into text.
     *              Mostly this is useful for snippets which analyse the text they are
     *              placed in. You can provide the order the snippets are loaded 
     *              by integer value of this param. Be aware that any value is converted 
     *              to integer. This param is considered in App::loadTextSnippets()
     *          - '_snippet_name' (string) Used in js to show snippet name in editor.
     *      
     * @param array $options Following are available
     *      - 'syntax' (string) If 'tag' then $snippet is provided in form of 
     *          tag attributes like: _snippet="{snippetSubject}" args.0="{arg}" {param}="{value}" .... 
     *          If 'brackets' the $snippet is provided in form of path like: {snippetSubject}?{arg}/{param}:{value}/...
     *          Defaults to 'tag'.
     * 
     * @return array Parsed snippet array contaning snippet specifications parameters.
     *      There are 2 types of returned array according to snippet type. For element,
     *      screen and dbscript snippets the array looks like:
     * 
     *          array(
     *              'type' => 'element', // 'screen', 'dbscript'
     *              'module' => 'MyModule',
     *              'name' => 'googleMap',
     *              'params' => array('longitude' => '...', ...),
     *              'ignoreOnNoRights' => FALSE,
     *          )
     * 
     *      For controlleraction (MVC) snippets the array looks like:
     * 
     *          array(
     *              'type' => 'controlleraction',
     *              'module' => 'MyModule',
     *              'name' => 'EshopOrders.checkout',
     *              'controller' => 'EshopOrders',
     *              'action' => 'checkout',
     *              'params' => array(...),
     *              'args' => array(...),
     *              'ignoreOnNoRights' => FALSE,
     *          )
     * 
     *      If the snippet parsing fails then an empty array is returned.
     */
    static public function parseSnippet($snippet, $options = array()) {
        $defaults = array(
            'syntax' => 'tag',
        );
        $options = array_merge($defaults, $options);
        $parsedSnippet = array();
        // analyze snippet defined like 
        // _snippet="{snippetSubject}" [args.0="{argParam}" [{param}="{value}" [...]]]
        if ($options['syntax'] == 'tag') {
            // retrieve params (including snippet name)
            $rawParams = explode('"', $snippet);
            $paramsCount = count($rawParams) - 1;
            if ($paramsCount < 2) {
                return $parsedSnippet;
            }
            $snippetParams = array();
            for ($i = 0; $i < $paramsCount; $i += 2) {
                $paramName = trim($rawParams[$i], ' =');
//                // exclude _snippet_name, _snippet_generic, ...
//                if (substr($paramName, 0, 9) === '_snippet_') {
//                    continue;
//                }
                $paramValue = trim($rawParams[$i+1]);
                $snippetParams[$paramName] = $paramValue;
            }
            // find snippet name
            if (!empty($snippetParams['_snippet'])) {
                $name = $snippetParams['_snippet'];
//                unset($snippetParams['_snippet']);
            }
            else {
                return $parsedSnippet;
            }
        }
        // analyze snippet defined like 
        // {snippetSubject}[?{argParam}[/{param}:{value}[/...]]]
        elseif ($options['syntax'] == 'brackets') {
            // retrieve snippet name
            $snippet = explode('?', $snippet);
            $name = array_shift($snippet);
            // retrieve snippet params
            $snippet = implode('?', $snippet);
            $snippet = explode('/', $snippet);
            $snippetParams = array();
            $snippetArgs = array();
            foreach ($snippet as $rawParam) {
                if (trim($rawParam)) {
                    // get named params
                    if (strpos($rawParam, ':') !== false) {
                        $rawParam = explode(':', $rawParam);
                        $paramName = trim(array_shift($rawParam));
                        $paramValue = implode(':', $rawParam);
                        if ($paramName) {
                            $snippetParams[$paramName] = $paramValue;
                        }
                    }
                    // get passed params
                    else {
                        $snippetArgs[] = $rawParam;
                    }
                }
            }
        }
        // check for ignoreOnNoRights param
        $ignoreOnNoRights = false;
        if (!empty($snippetParams['_snippet_ignore_on_no_rights'])) {
            $ignoreOnNoRights = true;
//            unset($snippetParams['_snippet_ignore_on_no_rights']);
        }
        $snippetParams = Arr::inflate($snippetParams);
        // in case of tag syntax args are passed as part of params so separate them 
        // this must be done after params are inflated
        if ($options['syntax'] == 'tag') {
            $snippetArgs = array();
            if (isset($snippetParams['args'])) {
                $snippetArgs = (array)$snippetParams['args'];
                unset($snippetParams['args']);
            }
        }
        // if no args are provided then set then to null for further processing 
        // (if they are null then further processing sets then to App::$args)
        if (empty($snippetArgs)) {
            $snippetArgs = null;
        }
        // dispatch snippet loading according to specified source
        $nameParts = explode('.', $name);
        if (count($nameParts) == 1) {
            array_unshift($nameParts, 'd');
        }
        switch (Sanitize::value($nameParts[0])) {
            case 'e':
                $parsedSnippet = array(
                    'type' => 'element',
                    'module' => Sanitize::value($nameParts[1]),
                    'name' => Sanitize::value($nameParts[2]),
                    'params' => $snippetParams,
                    'ignoreOnNoRights' => $ignoreOnNoRights,
                );
                break;

            case 's':
                $parsedSnippet = array(
                    'type' => 'screen',
                    'module' => 'App',
                    'name' => Sanitize::value($nameParts[1]),
                    'params' => $snippetParams,
                    'ignoreOnNoRights' => $ignoreOnNoRights,
                );
                break;

            case 'd':
                $parsedSnippet = array(
                    'type' => 'dbscript',
                    'module' => Sanitize::value($nameParts[1]),
                    'name' => Sanitize::value($nameParts[2]),
                    'params' => $snippetParams,
                    'ignoreOnNoRights' => $ignoreOnNoRights,
                );
                break;

            // mvc snippet
            default:
                $parsedSnippet = array(
                    'type' => 'controlleraction',
                    'module' => Sanitize::value($nameParts[0]),
                    'name' => Sanitize::value($nameParts[1]) . '.' . Sanitize::value($nameParts[2]),
                    'controller' => Sanitize::value($nameParts[1]),
                    'action' => Sanitize::value($nameParts[2]),
                    'params' => $snippetParams,
                    'args' => $snippetArgs,
                    'ignoreOnNoRights' => $ignoreOnNoRights,
                );
                break;
        }
        return $parsedSnippet;
    }
               
    /**
     * Loads snippets contained in $text.
     * 
     * NOTE: Snippet which have param '_snippet_after_others_loaded' are loaded only after
     * others snippets (without this param) are parsed and loaded into text.
     * 
     * @param string $text Text containing snippets to be loaded.
     * 
     * @param array $options Following are available
     *      - 'params' (array) Associaltive array of $params to be passed to script as
     *          additional parameters. If both, snippet params and $params, are defined then
     *          snippet param 'x' overrides the $params['x'] with the same name.
     *          Params (merged with snippet params) are passed to snippets into
     *          snippets as $params arg of App::loadElement(), loadScreen() 
     *          loadScript() and loadControllerAction() methods.
     *          Variables defined like array('myVar1' => '...', 'myVar2' => '...') are
     *          accessible like $this->params['myVar1'], $this->params['myVar2'].
     *          ATTENTION: Provided $text is passed by reference to each loaded snippet as param '_text'.
     *          If the snippet changes this param then the processed text itsefl is changed.
     *      - 'syntax' (string) Type of snippets syntax. If 'tag' then snippets are 
     *          defined like <object _snippet="{snippetName}" args.0="{arg}" {param}="{value}" ...></object>. 
     *          If 'brackets' then snippets are defined like [[{snippetName}?{arg}/{param}:{value}/...]]
     *          Defaults to 'tag'.
     * 
     * @return string The $text string. In $text all the snippets are
     *      replaced by corresponding contents.
     */
    static public function loadTextSnippets($text, $options = array()) {
        $defaults = array(
            'params' => array(),
            'syntax' => 'tag',
        );
        $options = array_merge($defaults, $options);
        $snippets = self::parseTextSnippets($text, $options);
        if (!empty($snippets)) {
            // Pass the provided $text by reference to each snippet as param '_text'
            $options['params']['_text'] = &$text;
            $snippetOutputs = array();
            $afterSnippets = array();
            foreach ($snippets as $snippet) {
                // check if snippet should be loaded after other snippets are loaded
                if ($options['syntax'] === 'tag') {
                    $lastSnippet = preg_match('/_snippet_after_others_loaded="([0-9]*)"/', $snippet[1], $match);
                }
                else {
                    $lastSnippet = preg_match('/_snippet_after_others_loaded:([0-9]*)/', $snippet[1], $match);
                }
                if ($lastSnippet) {
                    // - if yes then store them under provided weights
                    if (empty($afterSnippets[(int)$match[1]])) {
                        $afterSnippets[(int)$match[1]] = array();
                    }
                    $afterSnippets[(int)$match[1]][] = $snippet;
                    continue;
                }
                // do not load snippet if it was already loaded
                if (!isset($snippetOutputs[$snippet[0]])) {
                    // whole snippet is under index '0', snippet body (name + params) is under index '1'
                    $snippetOutputs[$snippet[0]] = self::loadSnippet($snippet[1], $options);
                }
            }
            // insert snippet outputs to the text
            $text = str_replace(array_keys($snippetOutputs), $snippetOutputs, $text);     
            // check if there are some snippets to be loaded after other are done
            if (!empty($afterSnippets)) {
                // sort them according provided weights ascending
                ksort($afterSnippets, SORT_NUMERIC);
                foreach ($afterSnippets as $weightAfterSnippets) {
                    // snippets with the same weight are processed at once (without inserting them to the text)
                    $snippetOutputs = array();
                    foreach ($weightAfterSnippets as $snippet) {
                        // do not load snippet if it was already loaded
                        if (!isset($snippetOutputs[$snippet[0]])) {
                            // whole snippet is under index '0', snippet body (name + params) is under index '1'
                            $snippetOutputs[$snippet[0]] = self::loadSnippet($snippet[1], $options);
                        }
                    }
                    // insert snippet outputs to the text
                    $text = str_replace(array_keys($snippetOutputs), $snippetOutputs, $text);     
                }
            }
        }
        return $text;
    }
    
    /**
     * Loads element, screen, database script or controller::action() according to
     * provided snippet name and returns the script output. 
     * 
     * @param string $snippet Snippet definition. See App::parseSnippet() for details.
     * @param array $options Following are available
     *      - 'params' (array) Associaltive array of $params to be passed to script as
     *          additional parameters. If both, snippet params and $params, are defined then
     *          snippet param 'x' overrides the $params['x'] with the same name.
     *          Params (merged with snippet params) are passed to snippets into
     *          snippets as $params arg of App::loadElement(), loadScreen() 
     *          loadScript() and loadControllerAction() methods.
     *          Variables defined like array('myVar1' => '...', 'myVar2' => '...') are
     *          accessible like $this->params['myVar1'], $this->params['myVar2'].
     *      - 'syntax' (string) If 'tag' then $snippet is provided in form of 
     *          tag attributes like: _snippet="{snippetName}" args.0="{arg}" {param}="{value}" .... 
     *          If 'brackets' the $snippet is provided in form of path like: {snippetName}?{arg}/{param}:{value}/...
     *          Defaults to 'tag'.
     * 
     * @return string Html output generated by the corresponding snippet source. 
     *      If an error occurs while loading the snippet then an empty string is returned.
     */
    static public function loadSnippet($snippet, $options = array()) {
        $defaults = array(
            'params' => array(),
            'syntax' => 'tag',
        );
        $options = array_merge($defaults, $options);
        $output = '';
        // do not crash if the snippet parsing fails or if specified scipt name does not exist
        try {
            $parsedSnippet = self::parseSnippet($snippet, $options);
            // if snippet parsing has failed then thow exception
            if (empty($parsedSnippet)) {
                throw new Exception(__e(__FILE__, 'Parsing of snippet \'%s\' has failed', $snippet));
            }
            $parsedSnippet['params'] = array_merge($options['params'], $parsedSnippet['params']);
            // authenticate first
            if (empty($parsedSnippet['ignoreOnNoRights'])) {
                self::authenticate($parsedSnippet['module'], $parsedSnippet['type'], $parsedSnippet['name']);
            }
            // if ignoreOnNoRights then just check rights and on no return empty string
            elseif (
                !self::getUserRights($parsedSnippet['module'], $parsedSnippet['type'], $parsedSnippet['name'])
            ) {
                return $output;
            }
            // get output
            switch ($parsedSnippet['type']) {
                case 'element':
                    $output = self::loadElement(
                        $parsedSnippet['module'], 
                        $parsedSnippet['name'], 
                        $parsedSnippet['params'], 
                        true
                    ); 
                    break;

                case 'screen':
                    // merge snippet params with actual params of application
                    $parsedSnippet['params'] = array_merge($parsedSnippet['params'], self::$params);
                    $output = self::loadScreen(
                        $parsedSnippet['name'], 
                        $parsedSnippet['params'], 
                        true
                    ); 
                    break;

                case 'dbscript':
                    $output = self::loadDBScript(
                        $parsedSnippet['module'], 
                        $parsedSnippet['name'], 
                        $parsedSnippet['params'], 
                        true
                    ); 
                    break;

                // controlleraction snippet
                default:
                    // merge snippet args and params with actual params of application
                    if (!isset($parsedSnippet['args'])) {
                        $parsedSnippet['args'] = self::$args;
                    }
                    $parsedSnippet['params'] = array_merge($parsedSnippet['params'], self::$params);
                    $output = self::loadControllerAction(
                        $parsedSnippet['module'], 
                        $parsedSnippet['controller'], 
                        $parsedSnippet['action'], 
                        $parsedSnippet['params'], 
                        true, 
                        $parsedSnippet['args']
                    ); 
                    break;
            }
        }
        catch(Throwable $e) {
            self::catchThrowable($e);
        }
                
        return $output;
    }
       
    /**
     * Loads script placed under /app/layouts/ or /app/modules/{Module}/layouts/ folder and returns it's
     * html output.
     * 
     * NOTE: Use $this->displayOriginComment = TRUE; to display origin comments of layout 
     * if they are allowed (see App config 'allowOriginComments')
     *
     * @param string $module Name of module. Use 'App' for application level or 
     *      name of folder placed under /app/modules/
     * @param string $name Name of script name placed under layouts folder.
     * @param array $params Optional. Associaltive array of layout params available as $this->params in layout. 
     *      This can be easily created by compact() function, e.g.: compact('paramForScript1', 'paramForScript2', ...)
     *      or it can be created manualy, e.g.: array('param1' => 'its value', 'param2' => 'its value' , ...)
     *      Defaults to empty array().
     * @param array $data Optional. Layout data available as $this->data in layout. 
     *      Defaults to empty array().
     * @param string|array $catchVariables Optional. A single variable name or an array
     *      of such names to be catched after the layout is loaded. They
     *      are returned by aux output $catchedVariables. If the specified variable
     *      does not exist (is not created by layout) then it is not present
     *      in $catchedVariables. If this option is any empty value then nothing is 
     *      catched. Defaults to NULL.
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return string Html output generated by the script. 
     */
    static public function loadLayout($module, $name, $params = array(), $data = array(), $catchVariables = null, &$catchedVariables = array()) {
        $Template = new Template($module, 'layout', $name, $params, $data);
        return $Template->load(self::$allowOriginComments, $catchVariables, $catchedVariables); 
    }
           
    /**
     * Loads script placed under /app/doctypes/ or /app/modules/{Module}/doctypes/ folder and returns it's
     * html output.
     * 
     * @param string $module Name of module. Use 'App' for application level or 
     *      name of folder placed under /app/modules/
     * @param string $name Name of script name placed under doctypes folder.
     * @param array $params Optional. Associaltive array of doctype params available as $this->params in doctype. 
     *      This can be easily created by compact() function, e.g.: compact('paramForScript1', 'paramForScript2', ...)
     *      or it can be created manualy, e.g.: array('param1' => 'its value', 'param2' => 'its value' , ...)
     *      Defaults to empty array().
     * @param array $data Optional. Doctype data available as $this->data in doctype. 
     *      Defaults to empty array().
     * @param string|array $catchVariables Optional. A single variable name or an array
     *      of such names to be catched after the doctype is loaded. They
     *      are returned by aux output $catchedVariables. If the specified variable
     *      does not exist (is not created by doctype) then it is not present
     *      in $catchedVariables. If this option is any empty value then nothing is 
     *      catched. Defaults to NULL.
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return string Html output generated by the script. 
     */
    static public function loadDoctype($module, $name, $params = array(), $data = array(), $catchVariables = null, &$catchedVariables = array()) {
        $Template = new Template($module, 'doctype', $name, $params, $data);
        return $Template->load(self::$allowOriginComments, $catchVariables, $catchedVariables); 
    }
    
    /**
     * Adds provided files to App::$firstCssFiles
     * 
     * @param string|array $file Single file URL path or array of such paths
     * @param type $prepend If TRUE then provided files are prepended to existing App::$firstCssFiles.
     *      Defaults to FALSE, means provided files are appended.
     */
    static public function addFirstCssFiles($file, $prepend = false) {
        if ($prepend) {
            self::$firstCssFiles = array_merge((array)$file, self::$firstCssFiles);
        }
        else {
            self::$firstCssFiles = array_merge(self::$firstCssFiles, (array)$file);
        }
    }
    
    /**
     * Adds provided files to App::$lastCssFiles
     * 
     * @param string|array $file Single file URL path or array of such paths
     * @param type $prepend If TRUE then provided files are prepended to existing App::$lastCssFiles.
     *      Defaults to FALSE, means provided files are appended.
     */
    static public function addLastCssFiles($file, $prepend = false) {
        if ($prepend) {
            self::$lastCssFiles = array_merge((array)$file, self::$lastCssFiles);
        }
        else {
            self::$lastCssFiles = array_merge(self::$lastCssFiles, (array)$file);
        }
    }
    
    /**
     * Compiles provided css files into assets and returns the array of corresponding asset files.
     * There can be more than one asset file on output because of IE conditions.
     * 
     * @param array $files Array of files in form like '{filepath}' => '{IE conditions}'
     * 
     * @return array Array of asset files in form like '{filepath}' => '{IE conditions}'
     */
    public static function compileCssFiles($files) {
        if (!self::$assetsTableEnsured) {
            self::ensureAssetsTable();
            self::$assetsTableEnsured = true;
        }
        // @todo
        // ...
        return $files;
    }
           
    /**
     * Creates html <link> tags to attach .css files dynamicaly attached by
     * App::setCssFiles()
     * 
     * @return string Html code 
     */
    public static function getCssLinks() {       
        $cssFiles = self::$cssFiles;
        $normalizedFiles = array();
        // get first css files
        foreach (self::$firstCssFiles as $file) {
            if (!isset($cssFiles[$file])) {
                continue;
            }
            $attributes = $cssFiles[$file];
            unset($cssFiles[$file]);
            // substitute file
            if (!empty(self::$cssFilesSubstitutions[$file])) {
                $file = self::$cssFilesSubstitutions[$file];
            }   
            $normalizedFiles[$file] = $attributes;
        }
        // get last css files
        $normalizedLastFiles = array();
        foreach (self::$lastCssFiles as $file) {
            if (!isset($cssFiles[$file])) {
                continue;
            }
            $attributes = $cssFiles[$file];
            unset($cssFiles[$file]);
            // substitute file
            if (!empty(self::$cssFilesSubstitutions[$file])) {
                $file = self::$cssFilesSubstitutions[$file];
            }         
            $normalizedLastFiles[$file] = $attributes;
        }
        // get the resting css files
        foreach ($cssFiles as $file => $attributes) {
            // substitute file
            if (!empty(self::$cssFilesSubstitutions[$file])) {
                $file = self::$cssFilesSubstitutions[$file];
            }        
            $normalizedFiles[$file] = $attributes;
        }
        // put all files together
        $normalizedFiles = array_merge($normalizedFiles, $normalizedLastFiles);
        // compile files to assets if required
        if (self::$compileCss) {
            $normalizedFiles = self::compileCssFiles($normalizedFiles);
        }
        return Html::cssLinks($normalizedFiles, array(
            'normalize' => false,
        ));
    }
    
    /**
     * Returns one css code compiled from css codes captured by App::startCssCapture() 
     * and App::endCssCapture() through the project.
     * 
     * @param bool $inStyleTags Optional. If TRUE then css code is returned wrapped
     *      in <style> tags (but only if there is any code). If FALSE then pure css 
     *      code is returned without wrapping <style> tags. Defaults to TRUE.
     * 
     * @return string
     */
    public static function getCssCode($inStyleTags = true) {
        return Html::cssCode(self::$css, array(
            'lastCssCodes' => self::$lastCss,
            'inStyleTags' => $inStyleTags,
            'normalize' => false,
        ));
    }
    
    /**
     * Compiles provided js files into assets and returns the array of corresponding asset files.
     * There can be more than one asset file on output because of IE conditions.
     * 
     * @param array $files Array of files in form like '{filepath}' => '{IE conditions}'
     * 
     * @return array Array of asset files in form like '{filepath}' => '{IE conditions}'
     */
    public static function compileJsFiles($files) {
        if (!self::$assetsTableEnsured) {
            self::ensureAssetsTable();
            self::$assetsTableEnsured = true;
        }
        // @todo
        // ...
        return $files;
    }
    
    /**
     * Creates html <script> tags to attach .js files dynamicaly set by App::setJsFiles(). 
     * These js files should are defined in config App::$headJsFiles.
     * 
     * @return string Html code 
     */
    public static function getHeadJsLinks() {
        $normalizedFiles = array();
        // get head js files
        foreach (self::$headJsFiles as $file) {
            if (!isset(self::$jsFiles[$file])) {
                continue;
            }
            $attributes = self::$jsFiles[$file];
            // substitute file
            if (!empty(self::$jsFilesSubstitutions[$file])) {
                $file = self::$jsFilesSubstitutions[$file];
            }      
            $normalizedFiles[$file] = $attributes;
        }
        // compile files to assets if required
        if (self::$compileJs) {
            $normalizedFiles = self::compileJsFiles($normalizedFiles);
        }
        return Html::jsLinks($normalizedFiles, array(
            'normalize' => false,
        ));
    }
            
    /**
     * Creates html <script> tags to attach .js files dynamicaly attached by
     * App::setJsFiles()
     * 
     * @return string Html code 
     */
    public static function getJsLinks() {
        $html = '';
        // attach SystemJS if required
        if (self::$attachSystemJsCode) {
            $html .= self::getSystemJsCode();
            self::$attachSystemJsCode = false;
        }
        
        $jsFiles = self::$jsFiles;
        $normalizedFiles = array();
        // get first js files
        foreach (self::$firstJsFiles as $file) {
            if (!isset($jsFiles[$file])) {
                continue;
            }
            $attributes = $jsFiles[$file];
            unset($jsFiles[$file]);
            // substitute file
            if (!empty(self::$jsFilesSubstitutions[$file])) {
                $file = self::$jsFilesSubstitutions[$file];
            }         
            $normalizedFiles[$file] = $attributes;
        }
        // get last js files
        $normalizedLastFiles = array();
        foreach (self::$lastJsFiles as $file) {
            if (!isset($jsFiles[$file])) {
                continue;
            }
            $attributes = $jsFiles[$file];
            unset($jsFiles[$file]);
            // substitute file
            if (!empty(self::$jsFilesSubstitutions[$file])) {
                $file = self::$jsFilesSubstitutions[$file];
            }      
            $normalizedLastFiles[$file] = $attributes;
        }
        // get resting js files
        $headJsFiles = array_flip(self::$headJsFiles);
        foreach ($jsFiles as $file => $attributes) {
            // skip head js files
            if (isset($headJsFiles[$file])) {
                continue;
            }
            // substitute file
            if (!empty(self::$jsFilesSubstitutions[$file])) {
                $file = self::$jsFilesSubstitutions[$file];
            }            
            $normalizedFiles[$file] = $attributes;
        }
        // put all files together
        $normalizedFiles = array_merge($normalizedFiles, $normalizedLastFiles);
        // compile files to assets if required
        if (self::$compileJs) {
            $normalizedFiles = self::compileJsFiles($normalizedFiles);
        }
        $html .= Html::jsLinks($normalizedFiles, array(
            'normalize' => false,
        ));
        return $html;
    }
    
    /**
     * Returns HTML code to attach SystemJS library and its importmap
     * 
     * @return string Html code
     */
    protected static function getSystemJsCode() {
        $html = '';
        if (!empty(self::$jsModuleImportmap)) {
            $html .= 
                '<script type="systemjs-importmap">' .
                    json_encode(
                        array('imports' => self::$jsModuleImportmap),
                        JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE
                    ) . 
                '</script>';
        }
        $html .= Html::jsLinks(
            array(
                '/app/js/vendors/system.min.js' => false,
            ), 
            array(
                'normalize' => false,
            )
        );
        return $html;
    }
    
    /**
     * Creates html of <script> tags to attach locales .js files dynamicaly attached by
     * App::setJsI18nFiles()
     * 
     * @return string Html code 
     */
    public static function getJsI18nLinks() {        
        $files = array();
        foreach (self::$jsI18nFiles as $module => $locales) {
            // recompile
            $locales = array_flip($locales);
            if ($module == 'App') {
                $poDir = DS . 'app' . DS . 'locale';
                $jsDir = DS . 'app' . DS . 'locale' . DS . 'compiled';
                $urlPath = URL_ROOT . '/app/locale/compiled/';
            }
            else {
                $poDir = DS . 'app' . DS . 'modules' . DS . $module . DS . 'locale';
                $jsDir = DS . 'app' . DS . 'modules' . DS . $module . DS . 'locale' . DS . 'compiled';
                $urlPath = URL_ROOT . '/app/modules/' . $module . '/locale/compiled/';
            }
            $items = scandir(ROOT . $poDir);
            foreach ($items as $item) {
                $poFile = $poDir . DS . $item;
                if (
                    is_dir(ROOT . $poFile)
                    || substr($item, -6) !== '_js.po'
                    || !isset($locales[substr($item, -11, -6)])
                ) {
                    continue;
                }
                $jsFileName = substr($item, 0, -6) . '.js';
                $jsFile = $jsDir . DS . $jsFileName;
                if (is_readable(ROOT . $poFile)) {
                    if (
                        !is_readable(ROOT . $jsFile)
                        || filemtime(ROOT . $poFile) >= filemtime(ROOT . $jsFile)
                    ) {
                        self::compilePoFile($poFile);
                    }
                }
                $files[$urlPath . $jsFileName] = false;
            }
        }
        return Html::jsLinks($files, array(
            'normalize' => false,
            'timestamp' => true,
        ));
    }
    
    /**
     * Creates js code of js config dynamicaly attached by App::setJsConfig().
     * 
     * @param bool $inScriptTags Optional. If TRUE then js code is returned wrapped
     *      in <script> tags (but only if there is any code). If FALSE then pure js 
     *      code is returned without wrapping <script> tags. Defaults to TRUE.
     * 
     * @return string Js code
     */
    public static function getJsConfigCode($inScriptTags = true) {
        $code = '';
        if (!empty(self::$jsConfig)) {
            $code .= 'window.Run=window.Run||{};';
            foreach (self::$jsConfig as $module => $config) {
                $code .= 'window.Run.' . $module . '=window.Run.' . $module . '||{};';
                $code .= 'window.Run.' . $module . '.config=' . json_encode($config) . ';';
            }
            if ($code && $inScriptTags) {
                $code  = PHP_EOL . '<script type="text/javascript">' . PHP_EOL . $code . PHP_EOL . '</script>';
            }
        }
        return $code;
    }
    
    /**
     * Returns one js code compiled from js codes captured by App::startJsCapture() 
     * and App::endJsCapture() through the project.
     * 
     * @param bool $inScriptTags Optional. If TRUE then js code is returned wrapped
     *      in <script> tags (but only if there is any code). If FALSE then pure js 
     *      code is returned without wrapping <script> tags. Defaults to TRUE.
     * 
     * @return string
     */
    public static function getJsCode($inScriptTags = true) {
        if (!empty($_SESSION['_app']['js'])) {
            self::$js = array_merge(self::$js, $_SESSION['_app']['js']);
            unset($_SESSION['_app']['js']);
        }
        if (!empty($_SESSION['_app']['lastJs'])) {
            self::$lastJs = array_merge(self::$lastJs, $_SESSION['_app']['lastJs']);
            unset($_SESSION['_app']['lastJs']);            
        }
        return Html::jsCode(self::$js, array(
            'lastJsCodes' => self::$lastJs,
            'inScriptTags' => $inScriptTags,
            'normalize' => false,
        ));
    }
    
    /**
     * Returns one html head code compiled from codes captured by App::startHeadHtmlCapture() 
     * and App::endHeadHtmlCapture() through the project.
     * 
     * @return string
     */
    public static function getHeadHtmlCode() {
        $code = '';
        $headHtml = array_merge(self::$headHtml, self::$lastHeadHtml);
        if (!empty($headHtml)) {            
            // trim whitespaces
            $headHtml = array_map('trim', $headHtml);
            // create one block of html head code
            $code = trim(implode(PHP_EOL . PHP_EOL, $headHtml));
        }
        return $code;
    }
    
    /**
     * Returns one html body end code compiled from codes captured by App::startBodyEndHtmlCapture() 
     * and App::endBodyEndHtmlCapture() through the project.
     * 
     * @return string
     */
    public static function getBodyEndHtmlCode() {
        $code = '';
        $bodyEndHtml = array_merge(self::$bodyEndHtml, self::$lastBodyEndHtml);
        if (!empty($bodyEndHtml)) {            
            // trim whitespaces
            $bodyEndHtml = array_map('trim', $bodyEndHtml);
            // create one block of html body end code
            $code = trim(implode(PHP_EOL . PHP_EOL, $bodyEndHtml));
        }
        return $code;
    }
    
    /**
     * Returns max allowed execution number of miliseconds.
     *  
     * @param mixed $debug Optional. If TRUE then microtime is also displayed using App::debug().
     *      If string then this is used as a message for debug label.
     * 
     * @return float Number of max execution miliseconds.
     */
    static public function getMaxExecutionMicrotime($debug = false) {
        $time = 1000 * (float)ini_get('max_execution_time');
        // if  max_execution_time is set to 0 (If you have not done it explicitly, 
        // then it is done by e.g. Xdebug) then return some huge value
        if (empty($time)) {
            $time = (float)PHP_INT_MAX;
        }
        if ($debug){
            $message = 'Max execution time';
            if (is_string($debug)) {
                $message .= " - $debug";
            }
            $message .= ': ';
            self::debug($time, $message . $time . ' ms', array(
                'showUrl' => true,
                'backtraceLevel' => 1,
            ));
        }
        return $time;
    }
    
    /**
     * Returns number of miliseconds from start of the application execution.
     *  
     * @param mixed $debug Optional. If TRUE then microtime is also displayed using App::debug().
     *      If string then this is used as a message for debug label.
     * 
     * @return float Number of elapsed miliseconds.
     */
    static public function getElapsedMicrotime($debug = false) {
        $time = round(1000 * (microtime(true) - self::$startMicrotime), 2);
        if ($debug){
            $message = 'Execution time';
            if (is_string($debug)) {
                $message .= " - $debug";
            }
            $message .= ': ';
            self::debug($time, $message . $time . ' ms', array(
                'showUrl' => true,
                'backtraceLevel' => 1,
            ));
        }
        return $time;
    }
    
    /**
     * Returns number of miliseconds which are still free till the end of execution
     * which is defined by 'max_execution_time'.
     *  
     * @param mixed $debug Optional. If TRUE then microtime is also displayed using App::debug().
     *      If string then this is used as a message for debug label.
     * 
     * @return float Number of free miliseconds.
     */
    static public function getFreeMicrotime($debug = false) {
        $time = round(self::getMaxExecutionMicrotime() - 1000 * ((microtime(true) - self::$startMicrotime)), 2);
        if ($debug){
            $message = 'Execution time';
            if (is_string($debug)) {
                $message .= " - $debug";
            }
            $message .= ': ';
            self::debug($time, $message . $time . ' ms', array(
                'showUrl' => true,
                'backtraceLevel' => 1,
            ));
        }
        return $time;
    }
    
    /**
     * Hashes the given string
     * 
     * @param string $string
     * 
     * @return string 
     */
    static public function hash($string, $options = array()) {
        $defaults = array(
            'key' => 'jOISDmmdl68dmnsdo4ksdnjsd0',
            'suffix' => 'dkaP4q5UaXqanLld89cnwMu',
        );
        // do not allow empty values for key or suffix
        $hashOptions = array_filter((array)self::$hashOptions);
        $options = array_filter((array)$options);
        $options = array_merge($defaults, $hashOptions, $options);
        return hash_hmac('sha512', $string . $options['suffix'], $options['key']);
    }
    
    /**
     * Formats number for display using default values for: number of decimal points,
     * decimal point separator and  thousands separator. These are defined as protected
     * properties of the App class and each lang must have defined its own default values.
     * If the actual lang has no defined default values for number formating then 
     * the default values of number_format() function are used.
     * 
     * NOTE: Use this method only for formating number just before passing them to views!
     * The use of formated numbers in calculations can lead to erroneous results!
     * 
     * @param mixed $number Integer or float number
     * @param int $decimals Number of decimal points. If not defined or NULL
     *      then the default number defined by App::$decimals is used. Defaults to NULL.
     * @param bool $nbsp If TRUE then spaces are replaced by &nbsp; html entity.
     *      Defaults to TRUE.
     * 
     * @return string 
     */
    static public function formatNumber($number, $decimals = null, $nbsp = true) {
        // retireve default values
        if (!isset($decimals)) {
            $decimals = self::getActualDecimals();
        }
        $decimalPoint = self::getActualDecimalPoint();
        $thousandsSeparator = self::getActualThousandsSeparator();
        // format
        $number = number_format($number, $decimals, $decimalPoint, $thousandsSeparator);
        if ($nbsp) {
            $number = str_replace(' ', '&nbsp;', $number);
        }
        return $number;
    }
    
    /**
     * Converts formatted number to correct numeric format in php
     * 
     * @param string $formattedNumber String of formatted number. e.g. '12,50'.
     * @param array $options Following are available:
     *      - 'decimalPoint' (string) Defaults to Apps::getActualDecimalPoint().
     *      - 'thousandsSeparator' (string) Defaults to Apps::getActualThousandsSeparator().
     *      - 'typeCast' (bool) If TRUE then the number is typecasted to integer
     *          or float automatically. Defaults to FALSE (anyway, in php everything 
     *          is string, even numbers).
     * 
     * @return string|int|float parsed number
     */
    static public function parseNumber($formattedNumber, $options = array()) {
        $options = array_merge(array(
            'decimalPoint' => self::getActualDecimalPoint(),
            'thousandsSeparator' => self::getActualThousandsSeparator(),
            'typeCast' => false,
        ), $options);
                
        $number = str_replace($options['thousandsSeparator'], '', $formattedNumber);
        $number = str_replace($options['decimalPoint'], '.', $number);
        if ($options['typeCast']) {
            if (strpos($number, '.') === false) {
                $number = (int)$number;
            }
            else {
                $number = (float)$number;
            }
        }
        return $number;
    }
    
    /**
     * Manages the authentication process
     * - checks user rights
     * - if insufficient rights and no user logged then redirects to login screen
     * - after successfull login redirects user to previously required url or to default url
     * - ...
     * 
     * @param string $module Module name, e.g.: 'App', 'App'
     * @param string $subjectType Accessed subject type, one of: 'screen', 'screenaction', 'element'
     *      'controlleraction', 'dbscript', 'setting'
     * @param string $subjectName Accessed subject name, e.g.: 'google_map', 'Users.amin_create'
     * @param array $options Following options can be used:
     *      - 'loginSlug' (string) Explicit value of login slug to be used in authentication process.
     *          Defaults to value resolved by App::getLoginSlug().
     *      - 'hasRights' (bool) Possibility to set subject rights on runtime.
     *          If NULL then rights are retrieved using App::getRigths(). 
     *          Defaults to NULL.
     *          NOTE: Subject for which you would like to determine rights on runtime must 
     *          be set as public to avoid default authentication failure.
     *      - 'loginMessage' (string) Login message displayed when user is redirected 
     *          to login page. If empty then no message is displayed. 
     *          Defaults to __(__FILE__, 'Please log in').
     *      - 'loginErrors' (string) Message displayed when user submits 
     *          invalid login data. If empty then no message is displayed. 
     *          Defaults to __(__FILE__, 'Login has failed').
     *      - 'rightsError' (string) Message displayed when there is an authenticated user
     *          but he has no rights for requested action. If empty then no message is displayed. 
     *          Defaults to __(__FILE__, 'You are not authorized for this action').
     * 
     * @return void
     */
    static public function authenticate($module, $subjectType, $subjectName, $options = array()) {
        $defaults = array(
            'loginSlug' => self::getLoginSlug(),
            'hasRights' => null,
            'loginMessage' => __(__FILE__, 'Please log in'),
            'loginError' => __(__FILE__, 'Login has failed'), // maybe this get never used as this is displayed only on implicit processing of login data
            'rightsError' => __(__FILE__, 'You are not authorized for this action'),
        );
        $options = array_merge($defaults, $options);
        // if on a login page 
        if (self::$slug == $options['loginSlug']) {
            // require https for login page
            self::requireHttps();
            // check for auth error and display it (debug reasons)
            if (!empty($_SESSION['_app']['auth']['error'])) {
                self::debug($_SESSION['_app']['auth']['error'], 'AUTH');
                // unset error message after displaying it
                unset($_SESSION['_app']['auth']['error']);
            }
            // if just receiving posted login data then try to login
            if (
                self::$requestMethod == 'POST'
                && isset(self::$data['username']) 
                && isset(self::$data['password'])
            ) {
                // on successfull login redirect user back to page he started the login from
                if (self::login(self::$data['username'], self::$data['password'], !empty(self::$data['_permanentLogin']))) {
                    self::redirect(self::getLoginTargetUrl(array('remove' => true)));
                }
                // if the login failed then let the user on login page and give him error message
                if ($options['loginError']) {
                    self::setErrorMessage($options['loginError']);
                }
                return;
            }
        }
        try {
            // in case of any other page checks for rights
            // get user group pid 
            $user = self::getUser();
            $userGroup = !empty($user['Group']['pid']) ? $user['Group']['pid'] : null;
            $userName = !empty($user['username']) ? $user['username'] : null;
            // find out user rights if not set explicitly
            if ($options['hasRights'] === null) {
                $explicitRightsMessage = '';
                $options['hasRights'] = self::getRights($user, $module, $subjectType, $subjectName);
            }
            else {
                $explicitRightsMessage = ' ' . __e(__FILE__, 'Rights has been set explicitly!');
            }
            // if user has rights then return and continue in processing
            if ($options['hasRights']) {
                // if non public requestor (requestor group is not empty) 
                // then require https protocol
                if (!self::hasPublicRights($module, $subjectType, $subjectName)) {
                    self::requireHttps();
                }
                // OK, go ahead to requested subject
                return;
            }
        }
        catch (Throwable $e) {
            // failure of rights evaluation = user has no rights
            // This is caused mostly by calling invalid mvc URLs, often by bot crawlers 
            // (they call e.g. lowercased mvc URLs). Avoid application crash and 
            // treat it as "has no rights" case. 
            $options['hasRights'] = false;
            $message = __e(__FILE__, 'Rights evaluation has failed with following exception: %s', $e->getMessage());
            self::debug($message, $message);
            self::log('rights', $message);
        }
        // !!! user has no rights for login page - THIS SHOULD NOT HAPPEN
        if (self::$slug == $options['loginSlug']) {
            if (empty($user)) {
                $message = __e(__FILE__, 'Login page must be public! The \'%s\' from \'%s/%s\' is not public!', $subjectName, $module, $subjectType) . $explicitRightsMessage;
                self::log('rights', $message);
                throw new Exception($message);
            }
            else {
                $message = __e(__FILE__, 'Login page must be accessible to user \'%s\' (\'%s\')! The \'%s\' from \'%s/%s\' is not!', $userName, $userGroup, $subjectName, $module, $subjectType) . $explicitRightsMessage;
                self::log('rights', $message);
                throw new Exception($message);
            }
        }
        // if user is logged but does not have rights then set App::rightsError messsage 
        // and return to referer page
        if (!empty($user)) {
            $message = __e(__FILE__, 'User \'%s\' (\'%s\') has insuficcient rights for \'%s\' from \'%s/%s\'.', $userName, $userGroup, $subjectName, $module, $subjectType) . $explicitRightsMessage;
            $_SESSION['_app']['auth']['error'] = $message;
            self::log('rights', $message);
            if ($options['rightsError']) {
                self::setErrorMessage($options['rightsError']);
            }
            self::redirect(self::getRefererUrl('/'));
        }
        // if no user is logged in (user group is empty) then redirect user to login page
        // and display him App::loginMessage
        $_SESSION['_app']['auth']['error'] = __e(__FILE__, 'You must login to access \'%s\' from \'%s/%s.\'', $subjectName, $module, $subjectType) . $explicitRightsMessage;
        self::setLoginTargetUrl(self::$url);
        if ($options['loginMessage']) {
            self::setInfoMessage($options['loginMessage']);
        }
        self::redirect(self::getUrl(array(
            'locator' => $options['loginSlug'],
            'protocol' => 'https',
        )));
    }
    
    /**
     * Tries to log the gived user in.
     * 
     * On succes create also session data of logged user. To access these 
     * data of actually logged user use App::getUser() method.
     * 
     * @param string $username
     * @param string $password
     * @param bool $permanent Optional. If TRUE then the login sessionise started 
     *      as permanent with lifetime defined by config option App::$permanentSessionLifetime. 
     *      Defaults to FALSE.
     * 
     * An alternative signature can be used:
     * 
     * @param int $userId If of user to be logged in
     * @param bool $permanent Optional. If TRUE then the login sessionise started 
     *      as permanent with lifetime defined by config option App::$permanentSessionLifetime. 
     *      Defaults to FALSE.
     * 
     * @return boolean TRUE on success. FALSE on login fail.
     */
    static public function login($username, $password = false, $permanent = false){
        $success = false;
        $username = self::normalizeUsername($username);
        if (empty($username)) {
            return $success;
        }
        
        $conditions = array(
            'User.active' => true,
        );
        // signature $userId + $permanent
        if (
            is_bool($password)
            && (
                func_num_args() === 1 
                || func_num_args() === 2
            )
        ) {
            $checkPassword = false;
            $conditions['User.id'] = $username;
            $permanent = $password;
        }
        // signature $username + $password + $permanent
        else {
            $checkPassword = true;
            $conditions['User.username'] = $username;
        }
        // find user
        $gt = self::$groupsTable;
        $user = DB::select(self::$usersTable, array(
            'alias' => 'User',
            'joins' => array(
                array(
                    'table' => $gt,
                    'alias' => 'Group',
                    'type' => 'left',
                    'conditions' => array(
                        "User.{$gt}_id = Group.id"
                    ),
                ),
            ),
            'conditions' => $conditions,
            'fields' => array(
                'User.id',
                'User.username',
                'User.password',
                'User.first_name',
                'User.last_name',
                'User.email',
                'User.rights',
                'Group.id',
                'Group.pid',
                'Group.name',
                'Group.hierarchy',
            ),
            'qualify' => true,
            'inflate' => true,
            'first' => true,
        ));

        // if user found and passwords match then login succeeded
        if (
            !empty($user)
            && (
                $checkPassword === false
                || $password !== '' && $user['User']['password'] === self::hash($password)
                || ON_LOCALHOST && $user['User']['password'] === $password 
            )
        ) {
            $success = true;
            // unset password
            unset($user['User']['password']);
            // parse user rights
            if (!empty($user['User']['rights'])) {
                $user['User']['rights'] = (array)json_decode($user['User']['rights'], true);
            }
            else {
                $user['User']['rights'] = array();
            }
            // put the login user data to session
            $_SESSION['_app']['auth']['User'] = $user['User'];
            $_SESSION['_app']['auth']['User']['Group'] = $user['Group'];
            // check for permanent login
            if ($permanent) {
                self::startPermanentSession();
            }
            if (!self::hasPermanentSessionAvailable()) {
                self::logError('Permanent session is not available', array(
                    'email' => true,
                    'data' => false,
                ));
            }
        }
        
        // here write your own code
        // ...
        
        return $success;
    }
    
    /**
     * Logs out currently logged user
     * 
     * Script logout.php is launched at the beginning ot this method
     */
    static public function logout() {
        
        // here write your own code
        // ...
        
        // unset logged user data and possible original user data
        unset($_SESSION['_app']['auth']['User']);
        unset($_SESSION['_app']['auth']['OriginalUser']);

        // close permanent session (if any)
        self::closePermanentSession();
    }
    
    /**
     * Returns normalized username (with trimmed whitespaces + nbsp and lowercased)
     * 
     * NOTE: Lowercasing is done just to make it nicer, in database are e.g. characters
     * a, A, á, Á, ä, Ä all considered as the same character (due to general collation)
     * 
     * @param string $username
     * @return string
     */
    static public function normalizeUsername($username) {
        // default trim() chars (whitespaces) + nbsp
        $trimmedChars = " \t\n\r\0\x0B" . html_entity_decode('&nbsp;');
        return mb_strtolower(trim($username, $trimmedChars));
    }
    
    /**
     * Resolves actual login slug
     * 
     * This can be set to folowing value in folowing priority order:
     *      - current App::$slug if there is a key _login in App::$data set to 1.
     *          This possibility serves to adjust login url when login is made from
     *          custom page (slug) and App::authenticate() needs to know that the incoming
     *          data come from login page and it should attempt to login user.
     *      - App::$adminLoginSlug if you are on admin url (App::adminSlug) or on
     *          admin login url (App::$adminLoginSlug)
     *      - slug of page specified by pid App::$userLoginPid if you are on any other than admin url
     * 
     * @var string Actual login slug
     */
    static public function getLoginSlug() {
        if (!empty(self::$data['_login'])) {
            return self::$slug;
        }
        elseif (
            self::$slug === self::$adminSlug
            || 
            self::$slug === self::$adminLoginSlug
            || 
            self::$requestType === 'mvc'
            && self::$actionType === 'admin'
        ) {
            return self::$adminLoginSlug;
        }
        if (
            empty(self::$userLoginPid)
            || !($userLoginSlug = self::getContentLocatorByPid(self::$userLoginPid))
        ) {
            throw new Exception(__e(__FILE__, 'No login page has been found for pid %s', self::$userLoginPid));
        } 
        return $userLoginSlug;
    }
    
    /**
     * Sets login target url in session
     * 
     * This method allows you to tweak exact address of login target in init.php 
     * 
     * @param string $url
     */
    static public function setLoginTargetUrl($url) {
        $_SESSION['_app']['auth']['loginTargetUrl'] = $url;
    }
    
    /**
     * Resolves login target url according to url stored in session and default
     * login target url
     * 
     * @param array $options Following options can be used:
     *      - 'remove' (bool) If TRUE then login target url is removed from session.
     *          Defaults to FALSE.
     *      - 'defaultUrl' (string) Default url if no other is resolved.
     *          Defaults to '/'.
     * 
     * @return string
     */
    static public function getLoginTargetUrl($options = array()) {
        $defaults = array(
            'remove' => false,
            'defaultUrl' => '/',
        );
        $options = array_merge($defaults, $options);
        
        // normalize default target url
        if (self::$slug == self::$adminLoginSlug) {
            $defaultUrl = '/' . self::$adminSlug;
        }
        else {
            if (empty(self::$userLoginTargetPid)) {
                $defaultUrl = $options['defaultUrl'];
            } 
            else {
                // get content locator by userLoginTargetPid
                $contentLocator = self::getContentLocatorByPid(self::$userLoginTargetPid);
                if (empty($contentLocator)) {
                    $defaultUrl = $options['defaultUrl'];
                } 
                else {
                    $defaultUrl = self::getUrl($contentLocator);
                }
            }
        }
        
        // get url from session
        $loginTargetUrl = Sanitize::value($_SESSION['_app']['auth']['loginTargetUrl']);
        
        // unset session key if required
        if (
            $options['remove']
            && isset($_SESSION['_app']['auth']['loginTargetUrl'])
        ) {
            unset($_SESSION['_app']['auth']['loginTargetUrl']);
        }
        
        // check for use of default url
        if (
            // if no login target url stored in session
            empty($loginTargetUrl)
            // or if stored login target url equals to login url
            || $loginTargetUrl == '/' . self::getLoginSlug()
        ) {
            $loginTargetUrl = $defaultUrl;
        }
        
        return $loginTargetUrl;
    }
            
    /**
     * Returns data of currently logged user in structure:
     * 
     *      array(
     *          'id' => ...,
     *          'username' => ...,
     *          'first_name' => ...,
     *          'last_name' => ...,
     *          'email' => ...,
     *          'Group' => array(
     *              'id' => ...,
     *              'pid' => ...,
     *              'name' => ...,
     *              'hierarchy' => ...,
     *          )
     *      )
     * 
     * @param string $field Optional. Field to be returned from user data, e.g. 'id'
     *      or 'Group.pid' (array path can be used for nested data). If not provided
     *      then whole user data array is returned. Defaults to NULL.
     * 
     * @return array|string Logged user data. If no user is logged in then empty array.
     *      If $field is provided then the field value (string). If no user is logged in
     *      or an unexisting field is provided then NULL.
     */
    static public function getUser($field = null) {
        if (!empty($_SESSION['_app']['auth']['User'])) {
            if ($field) {
               return Arr::getPath($_SESSION['_app']['auth']['User'], (string)$field);
            }
            return $_SESSION['_app']['auth']['User'];
        }
        elseif ($field) {
            return null;
        }
        else {
            return array();
        }
    }
    
    /**
     * Returns data of original user. Original user is the one you have provided 
     * password for on login page. If you switch to another user (see App::reinit()
     * or Users::admin_switchToAnother()), then the original user is stored in session 
     * to allow reswitch back. If this method returns nothing then it means that actual 
     * user is the original one or that there is no logged user. Returned data structure is:
     * 
     *      array(
     *          'id' => ...,
     *          'username' => ...,
     *          'first_name' => ...,
     *          'last_name' => ...,
     *          'email' => ...,
     *          'Group' => array(
     *              'id' => ...,
     *              'pid' => ...,
     *              'name' => ...,
     *              'hierarchy' => ...,
     *          )
     *      )
     * 
     * @param string $field Optional. Field to be returned from original user data, e.g. 'id'
     *      or 'Group.pid' (array path can be used for nested data). If not provided
     *      then whole user data array is returnd. Defaults to NULL.
     * 
     * @return array|string Logged user data. If no original user user is available 
     *      (it means now we are logged as original user) then empty array. If $field 
     *      is provided then the field value (string). If no original user is available 
     *      or an unexisting field is provided then NULL.
     */
    public static function getOriginalUser($field = null) {
        if (!empty($_SESSION['_app']['auth']['OriginalUser'])) {
            if ($field) {
               return Arr::getPath($_SESSION['_app']['auth']['OriginalUser'], (string)$field);
            }
            return $_SESSION['_app']['auth']['OriginalUser'];
        }
        elseif ($field) {
            return null;
        }
        else {
            return array();
        }
    }
    
    /**
     * Actualizes data of logged user. If no user is loged in, then nothing is done
     * 
     * @param int|string $id Optional. Explicit id of another user to reinitialize to or 
     *      string 'original' to reinitialize back the originally logged user. This can 
     *      be used to switch users, see Users::admin_switchToAnother(). If not provided 
     *      or empty then actual logged (original or another) user is reinitialized. 
     *      Defauts to NULL.
     * 
     * @return bool Success of done reinitialization. Possible failures occurres when:
     *      - there is no logged user
     *      - actual logged user has been deactivated or deleted
     *      - switched user (original or another) has not been found (deactivated or deleted)
     *      - another user has group hierarchy equal or higher than original user
     */
    static public function reinitUser($id = null) {
        // if no user logged in then do nothing
        if (empty($_SESSION['_app']['auth']['User'])) {
            return false;
        }
        // resolve the reinitialized user id and type of reinitialization ($switch)
        if (
            empty($id)
            || $id == $_SESSION['_app']['auth']['User']['id']
        ) {
            $switch = false;
            $id = $_SESSION['_app']['auth']['User']['id'];
        }
        elseif ($id === 'original') {
            if (($id = self::getOriginalUser('id'))) {
                $switch = 'toOriginal';
            }
            else {
                $switch = false;
                $id = $_SESSION['_app']['auth']['User']['id'];
            }
        }
        else {
            $switch = 'toAnother';
        }
        $return = true;
        // get user data
        $gt = self::$groupsTable;
        while(true) {
            $user = DB::select(self::$usersTable, array(
                'alias' => 'User',
                'joins' => array(
                    array(
                        'table' => $gt,
                        'alias' => 'Group',
                        'type' => 'left',
                        'conditions' => array(
                            "User.{$gt}_id = Group.id"
                        ),
                    ),
                ),
                'conditions' => array(
                    "User.active" => 1,
                    "User.id" => $id,
                ),
                'fields' => array(
                    'User.id',
                    'User.username',
                    'User.first_name',
                    'User.last_name',
                    'User.email',
                    'User.rights',
                    'Group.id',
                    'Group.pid',
                    'Group.name',
                    'Group.hierarchy',
                ),
                'qualify' => true,
                'inflate' => true,
                'first' => true,
            )); 
            // if no user found (deactivated, deleted, ...) then...
            if (empty($user)) {
                // ...logout if we are not switching
                if (!$switch) {
                    self::logout();
                    return false;
                }
                // ...stay at actual user if we are switching and continue next loop
                // to reinitialize actual user
                else {
                    $switch = false;
                    $id = $_SESSION['_app']['auth']['User']['id'];
                    $return = false;
                }
            }
            // if original user found then unset session record and break out the loop
            // to continue in processing
            elseif ($switch === 'toOriginal') {
                unset($_SESSION['_app']['auth']['OriginalUser']);
                break;
            }
            // if another user is found then...
            elseif ($switch === 'toAnother') {
                // inherit original user or if no available then set it to actual one
                if (!($originalUser = self::getOriginalUser())) {
                    $originalUser = $_SESSION['_app']['auth']['User'];
                }
                // ...if its hierarchy is lower or equal than original user then 
                // stay on actual (original) user
                if ($user['Group']['hierarchy'] <= $originalUser['Group']['hierarchy']) {
                    $switch = false;
                    $id = $_SESSION['_app']['auth']['User']['id'];
                    $return = false;
                }
                // ...if its hierarchy is higher than original user then create session 
                // record with original user id and break the loop to continue in processing
                else {
                    $_SESSION['_app']['auth']['OriginalUser'] = $originalUser;
                    break;
                }
            }
            // if actual user is found then break the loop to continue in processing
            else {
                break;
            }
        }
        // parse user rights
        if (!empty($user['User']['rights'])) {
            $user['User']['rights'] = (array)json_decode($user['User']['rights'], true);
        }
        else {
            $user['User']['rights'] = array();
        }
        // actualize the login user data in session
        $_SESSION['_app']['auth']['User'] = $user['User'];
        $_SESSION['_app']['auth']['User']['Group'] = $user['Group'];
        
        return $return;
    }

    /**
     * Increments a session counter that counts the number of successive app redirects.
     * This is used in App::redirect() to avoid redirection loops.
     * 
     * This session flag must be cleared at the end of common app execution (at
     * the end of index.php) and it is noticable only in case that execution 
     * of app process is exited on redirect (means on redirect the flag is not cleared
     * as the process will not reach the end of index.php).
     * 
     * @return int Old value of redirect counter
     */
    static public function incrementRedirectCounter() {
        $redirectCounter = self::getRedirectCounter();
        if (!$redirectCounter) {
            $_SESSION['_app']['redirectCounter'] = 1;
        }
        else {
            $_SESSION['_app']['redirectCounter']++;
        }
        return $redirectCounter;
    }
    
    /**
     * Gets the session counter that counts the number of successive app redirects.
     * This is used in App::redicrect() to avoid redirection loops.
     * 
     * @return int Value of redirect counter - number of successive app redirections
     */
    static public function getRedirectCounter() {
        return Sanitize::value($_SESSION['_app']['redirectCounter'], 0);
    }
    
    /**
     * Clears the session counter that counts the number of successive app redirects.
     * This is used in App::redicrect() to avoid redirection loops.
     * 
     * This method should be called at the end of index.php script
     * 
     * @return int Cleared value of redirect counter
     */
    static public function clearRedirectCounter() {
        $redirectCounter = self::getRedirectCounter();
        unset($_SESSION['_app']['redirectCounter']);
        return $redirectCounter;
    }
    
    /**
     * Requires https protocol to be used for functionality placed after call of this
     * method.
     * 
     * This method is taken into account only if config properties App::$httpsIsAvailable
     * and App::$allowSwitchBetweenHttpAndHttps are set to TRUE.
     * 
     * The change of http to https is made by redirect. If the request is already made
     * through https then only the internal property App::$httpsIsRequired is set TRUE
     * to let know method App::switchToHttp() to not make http fallback in case 
     * that https is not required.
     */
    static public function requireHttps() {
        // if https is not available or redirecting between http/https is not allowed
        // then do nothing
        if (
            !self::$httpsIsAvailable 
            || !self::$allowSwitchBetweenHttpAndHttps
        ) {
            return;
        }
        // if we are already on https then just set App::$httpsIsRequired to TRUE to 
        // let know App::switchToHttp() that requested functionality required https
        if (self::$onHttps) {
            self::$httpsIsRequired = true;
            return;
        }
        // if we are not on https then make redirect to https
        self::redirect(
            self::getUrl(array(
                'locator' => self::$url,
                'lang' => false,
                'root' => '',
                'absolute' => true,
                'protocol' => 'https',
            )), 
            array(
                'status' => 302, // 302 - Found
                'loopSafe' => false, // as this is just an implicit redirect so do not check loops
            )
        );
    }
    
    /**
     * Switch to http protocol if the actual protocol is https and it is not required.
     * 
     * This method is taken into account only if config property
     * App::$allowSwitchBetweenHttpAndHttps is set to TRUE.
     * 
     * This is used at the end of index.php
     */
    static public function switchToHttp() {
        // if not on https or if https is required or redirecting between http/https 
        // is not allowed then do nothing
        if (
            !self::$onHttps 
            || self::$httpsIsRequired
            || !self::$allowSwitchBetweenHttpAndHttps
        ) {
            return;
        }
        // if on https and it is not required then redirect to http
        // if we are not on https then make redirect to https
        self::redirect(
            self::getUrl(array(
                'locator' => self::$url,
                'lang' => false,
                'root' => '',
                'absolute' => true,
                'protocol' => 'http',
            )), 
            array(
                'status' => 302, // 302 - Found
                'loopSafe' => false, // as this is just an implicit redirect so do not check loops
            )
        );
    }
        
    /**
     * Sets a new application message.
     * 
     * @param string $message Message
     * @param array $options Following are available:
     *      - 'type' (string) Message type ('message', 'info', 'success', 'warning', 'error', ...)
     *      Defaults to 'message'.
     *      - 'modal' (bool) If TRUE then a modal window is displayed with messages.
     *      In default implementation if there is at leat one message set as modal, 
     *      then all messages as shown in modal window at once.
     *      - 'permanent' (bool) If TRUE then the message is not cleaned 
     *      by App::clearMessages() at the end of index.php but only App::getMessages()
     *      removes the permanent messages. Defaults to FALSE.
     * 
     * An alternative signature can be used (type defaults to 'message' in this case):
     * 
     * @param string $message Message
     * @param string $type Optional. Message type ('message', 'info', 'success', 'warning', 'error', ...)
     *      Defaults to 'message'.
     * @param bool $permanent Optional. If TRUE then the message is not cleaned 
     *      by App::clearMessages() at the end of index.php but only App::getMessages()
     *      removes the permanent messages. Defaults to FALSE.
     * 
     * An alternative signature can be used:
     * 
     * @param string $message Message
     * @param bool $permanent If TRUE then the message is not cleaned 
     *      by App::clearMessages() at the end of index.php but only App::getMessages()
     *      removes the permanent messages. Defaults to FALSE.
     */
    static public function setMessage($message, $type = 'message', $permanent = false) {
        // implement the third signature
        if (func_num_args() === 2 && is_bool($type)) {
            $permanent = $type;
            $type = 'message';
        }
        // implement the $options signature
        $defaults = array(
            'type' => 'message',
            'modal' => false,
            'permanent' => false,
        );
        if (!is_array($type)) {
            $options = array('type' => $type,'permanent' => $permanent);
        }
        else {
            $options = $type;
        }
        $options = array_merge($defaults, $options);
        if ($options['permanent']) {
            $_SESSION['_app']['permanentMessages'][$options['type']][] = $message;
            if ($options['modal']) {
                $_SESSION['_app']['modalPermanentMessages'][$options['type']] = true;
            }
        }
        else {
            $_SESSION['_app']['messages'][$options['type']][] = $message;
            if ($options['modal']) {
                $_SESSION['_app']['modalMessages'][$options['type']] = true;
            }
        }
    }
    
    /**
     * Sets a new application success message.
     * 
     * @param string $message Message
     * @param array $options Following are available:
     *      - 'modal' (bool) If TRUE then a modal window is displayed with messages.
     *      In default implementation if there is at leat one message set as modal, 
     *      then all messages as shown in modal window at once.
     *      - 'permanent' (bool) If TRUE then the message is not cleaned 
     *      by App::clearMessages() at the end of index.php but only App::getMessages()
     *      removes the permanent messages. Defaults to FALSE.
     * 
     * An alternative signature can be used:
     * 
     * @param string $message Message
     * @param bool $permanent Optional. If TRUE then the message is not cleaned 
     *      by App::clearMessages() at the end of index.php but only App::getMessages()
     *      removes the permanent messages. Defaults to FALSE.
     */
    static public function setSuccessMessage($message, $permanent = false) {
        // implement the $options signature
        $defaults = array(
            'modal' => false,
            'permanent' => false,
        );
        if (!is_array($permanent)) {
            $options = array('permanent' => $permanent);
        }
        else {
            $options = $permanent;
        }
        $options = array_merge($defaults, $options);
        // force type
        $options['type'] = 'success';
        self::setMessage($message, $options);
    }
    
    /**
     * Sets a new application info message.
     * 
     * @param string $message Message
     * @param array $options Following are available:
     *      - 'modal' (bool) If TRUE then a modal window is displayed with messages.
     *      In default implementation if there is at leat one message set as modal, 
     *      then all messages as shown in modal window at once.
     *      - 'permanent' (bool) If TRUE then the message is not cleaned 
     *      by App::clearMessages() at the end of index.php but only App::getMessages()
     *      removes the permanent messages. Defaults to FALSE.
     * 
     * An alternative signature can be used:
     * 
     * @param string $message Message
     * @param bool $permanent Optional. If TRUE then the message is not cleaned 
     *      by App::clearMessages() at the end of index.php but only App::getMessages()
     *      removes the permanent messages. Defaults to FALSE.
     */
    static public function setInfoMessage($message, $permanent = false) {
        // implement the $options signature
        $defaults = array(
            'modal' => false,
            'permanent' => false,
        );
        if (!is_array($permanent)) {
            $options = array('permanent' => $permanent);
        }
        else {
            $options = $permanent;
        }
        $options = array_merge($defaults, $options);
        // force type
        $options['type'] = 'info';
        self::setMessage($message, $options);
    }
    
    /**
     * Sets a new application warning message.
     * 
     * @param string $message Message
     * @param array $options Following are available:
     *      - 'modal' (bool) If TRUE then a modal window is displayed with messages.
     *      In default implementation if there is at leat one message set as modal, 
     *      then all messages as shown in modal window at once.
     *      - 'permanent' (bool) If TRUE then the message is not cleaned 
     *      by App::clearMessages() at the end of index.php but only App::getMessages()
     *      removes the permanent messages. Defaults to FALSE.
     * 
     * An alternative signature can be used:
     * 
     * @param string $message Message
     * @param bool $permanent Optional. If TRUE then the message is not cleaned 
     *      by App::clearMessages() at the end of index.php but only App::getMessages()
     *      removes the permanent messages. Defaults to FALSE.
     */
    static public function setWarningMessage($message, $permanent = false) {
        // implement the $options signature
        $defaults = array(
            'modal' => false,
            'permanent' => false,
        );
        if (!is_array($permanent)) {
            $options = array('permanent' => $permanent);
        }
        else {
            $options = $permanent;
        }
        $options = array_merge($defaults, $options);
        // force type
        $options['type'] = 'warning';
        self::setMessage($message, $options);
    }
    
    /**
     * Sets a new application error message.
     * 
     * @param string $message Message
     * @param array $options Following are available:
     *      - 'modal' (bool) If TRUE then a modal window is displayed with messages.
     *      In default implementation if there is at leat one message set as modal, 
     *      then all messages as shown in modal window at once.
     *      - 'permanent' (bool) If TRUE then the message is not cleaned 
     *      by App::clearMessages() at the end of index.php but only App::getMessages()
     *      removes the permanent messages. Defaults to FALSE.
     * 
     * An alternative signature can be used:
     * 
     * @param string $message Message
     * @param bool $permanent Optional. If TRUE then the message is not cleaned 
     *      by App::clearMessages() at the end of index.php but only App::getMessages()
     *      removes the permanent messages. Defaults to FALSE.
     */
    static public function setErrorMessage($message, $permanent = false) {
        // implement the $options signature
        $defaults = array(
            'modal' => false,
            'permanent' => false,
        );
        if (!is_array($permanent)) {
            $options = array('permanent' => $permanent);
        }
        else {
            $options = $permanent;
        }
        $options = array_merge($defaults, $options);
        // force type
        $options['type'] = 'error';
        self::setMessage($message, $options);
    }
    
    /**
     * Gets application messages from session.
     * Retrieved messages are deleted from session.
     * 
     * @param string|array $type Messages type ('info', 'warning', 'error') or 
     *      an array of types to retrieve. Defaults to NULL, means that all 
     *      messsages are retrieved
     * @params bool& $modal Optional. Passed by reference. Aux output set to TRUE 
     *      if returned messages should be displayed as modal. It is up to messages
     *      display implementation to consider this flag (see default implementation
     *      in app/views/App/messages).
     *  
     * @return array Retrieved application messages 
     */
    static public function getMessages($type = null, &$modal = false) {
        $persistenceTypes = array(
            'permanentMessages' => 'modalPermanentMessages', 
            'messages' => 'modalMessages',
        );
        $messages = array();
        if (empty($type)) {
            foreach($persistenceTypes as $persistenceType => $modalFlag) {
                $messages = Arr::mergeRecursive(
                    $messages,
                    (array)Sanitize::value($_SESSION['_app'][$persistenceType])
                );
                // make the messages unique
                foreach ($messages as $type => &$typeMessages) {
                    $typeMessages = array_unique($typeMessages);
                }
                unset($typeMessages);
                $_SESSION['_app'][$persistenceType] = array();
                $modal = !empty($_SESSION['_app'][$modalFlag]);
                unset($_SESSION['_app'][$modalFlag]);
            }
            
        }
        else {
            $type = (array)$type;
            foreach ($type as $t) {
                $messages[$t] = array();
                foreach($persistenceTypes as $persistenceType => $modalFlag) {
                    $messages[$t] = array_merge(
                        $messages[$t],
                        (array)Sanitize::value($_SESSION['_app'][$persistenceType][$t])
                    );
                    // make the messages unique
                    $messages[$t] = array_unique($messages[$t]);
                    $_SESSION['_app'][$persistenceType][$t] = array();
                    $modal = $modal || !empty($_SESSION['_app'][$modalFlag][$t]);
                    unset($_SESSION['_app'][$modalFlag][$t]);
                }
            }
        }
        return $messages;
    }
        
    /**
     * Removes all existing application messages.
     * Used at the end if index.php.
     */
    static public function clearMessages() {
        unset($_SESSION['_app']['messages']);
        unset($_SESSION['_app']['modalMessages']);
    }
    
    /**
     * ELEMENT to display application messages
     * 
     * @param array $options Following are available:
     *      - 'type' (string|array) Messages type ('info', 'warning', 'error') or 
     *          an array of types to retrieve. Defaults to NULL, means that all 
     *          messsages are retrieved
     *      - 'separate' (bool) If TRUE then messsages are separated, e.g. they can 
     *          be closed one by one. Defaults to FALSE, means all messages are closed 
     *          together. 
     *      - 'timeout' (int|float) Specifies timeout in seconds after which the 
     *          messages will be autoclosed. If empty then messages autoclosing is off.
     *          Defaults to setting App.messages.timeout.
     *      - 'closeButton' (bool|string) If FALSE then it is not possible to close message(s),
     *          there is no close button(s) generated. If string then specifies icon for
     *          button. Icon can be specified either by '/path/to/icon.png' or by 
     *          whole htm '<img src="/path/to/icon.pg">'. If TRUE then no icon html is 
     *          inserted and icon can be created only by css background. Defaults to TRUE.
     *      - 'class' (string) Css class of messages top container. Defaults to 
     *          'app-messages'. In case of modal display of messages the class is
     *          changed by '-modal' suffix, e.g. 'app-messages-modal'.
     *      - 'view' (string|array) Name of view for messages rendering. If string 
     *          then the view is searched under App module. To provide views from 
     *          other modules use array like array('module' => ..., 'name' => ...)
     *          There are 2 prepared views 'App/messages' (~ frontend) and
     *          'App/bootstrapMessages' (~ backend). Defaults to 'App/messages'.
     * 
     * @return string Html code
     */
    static public function messages($options = array()) {
        $defaults = array(
            'type' => null,
            'separate' => false,
            'timeout' => self::getSetting('App', 'messages.timeout'),
            'closeButton' => true,
            'class' => 'app-messages',
            'view' => 'App/messages',
        );
        $options = array_merge($defaults, $options);
        // normalize
        if ($options['timeout']) {
            $options['timeout'] = 1000 * (float)$options['timeout'];
        }
        if (empty($options['class'])) {
            $options['class'] = 'app-messages';
        }
        if (is_string($options['view'])) {
            $options['view'] = array('module' => 'App', 'name' => $options['view']);
        }
        if (
            is_string($options['closeButton']) 
            && substr($options['closeButton'], 0, 1) !== '<'
        ) {
            $options['closeButton'] = '<img src="' . URL_ROOT . $options['closeButton'] . '"> ';
        }
        // get messages and render them
        $messages = self::getMessages($options['type'], $modal);
        return self::loadView($options['view']['module'], $options['view']['name'], array(
            'messages' => $messages,
            'modal' => $modal,
            'timeout' => $options['timeout'],
            'separate' => $options['separate'],
            'closeButton' => $options['closeButton'],
            'class' => $options['class'],
        ));
    }
    
    /**
     * Resolves http referer URL.
     * 
     * NOTE: GET params are part of referer URL.
     * 
     * @param string $default Optional. Referer url if no referer found. Defaults to '/'.
     * 
     * @return string
     */
    static public function getRefererUrl($default = '/') {
        if (!empty($_SERVER['HTTP_REFERER'])) {
            return $_SERVER['HTTP_REFERER'];
        }
        return $default;
    }

    /**
     * Sends a single email message
     * 
     * NOTE: To debug application emails (e.g. in case of debugging production DB
     * with production SMTP and email addresses on local machine) use App config
     * 'debugEmailOptions'.
     * 
     * @param string $body Html body
     * @param string|array $to Single recipient email or array of recipient emails.
     *      Use associative array to specify both email and name for single or many recipients, 
     *      e.g. array('<EMAIL>' => 'Test User', ...)
     * @param array $options Following can be used:
     *      - 'bcc' (string|array) Email(s) to send hidden copy. Same input format as $to.
     *      - 'cc' (string|array) Email(s) to send copy. Same input format as $to.
     *      - 'from' (string|array) If multiple From addresses are used, you 
     *          SHOULD set the Sender address and according to RFC 2822, MUST 
     *          set the sender address. Use associative array to specify both email and name, 
     *          e.g. array('<EMAIL>' => 'Test User'). 
     *          Defaults to combination of settings array('{App.email.from}' => '{App.name}').
     *      - 'replyTo' (string|array) Any replies from the receiver will be sent 
     *          to this address. It is permissible for multiple reply-to addresses 
     *          to be set using an array. Use associative array to specify both email and name, 
     *          e.g. array('<EMAIL>' => 'Test User', ...)
     *      - 'returnPath' (string) The return-path (bounce-detect) address
     *      - 'sender' (string|array) If multiple addresses are present in the From field, this SHOULD be set.
     *      - 'subject' (array)
     *      - 'inserts' (array) Array of pairs '{insert}' => '{value}'. See Str::fill() for details.
     *      - 'engine' (string) Explicit mail engine. Possible values are 'smtp' or 'mail'. 
     *          Defaults to config App::$mailEngine.
     *      - 'embedImages' (bool) If TRUE images are embeded. Only images provided by
     *          relative URLs are embeded. Images provided by absolute URLs are not
     *          embeded. Defaults to FALSE.
     *      - 'textBody' (string) Plain text version of email body. If not provided
     *          (NULL) then it is autogenerated from html $body. Defaults to NULL.
     *      - 'attachments' (array) Array of app root relative paths to files to be attached
     *          or array of arrays containing items 'path' (app root relative) and 'contentType'
     *          or array of arrays containing items 'data', 'filename' and 'contentType' to
     *          specify attachments from dynamic data. E.g.:
     *              array(
     *                  '/userfiles/my-file-to-attach.txt',
     *                  array(
     *                      'path' => '/userfiles/my-file-to-attach-2.txt',
     *                      'contentType' => 'text/plain',
     *                  )
     *                  array(
     *                      'data' => $myPdfData,
     *                      'filename' => 'my-contract.pdf',
     *                      'contentType' => 'application/pdf',
     *                  )
     *              )
     *          NOTE: for proper content types see http://webdesign.about.com/od/multimedia/a/mime-types-by-content-type.htm .
     *          Defaults to empty array().
     *      - 'log' (&string) Aux output to get log of message sent. It helps to
     *          find fault reason when method returns FALSE. Provide it by reference
     *          like e.g. 'log' => &$myLog. Log is returned only if option 'logLength'
     *          is set to value greater than 0.
     *      - 'logLength' (integer) Length of log returned in 'log' option. If empty
     *          value then no log is returned. Recommended value is 50 and more.
     *          Defaults to 0.
     * 
     * @return int The number of recipients who were accepted for delivery.
     * 
     * @throws Exception on invalid body (empty) and inserts (not assoc array). 
     *      SwiftMailer can also raise exceptions.
     */
    static public function sendEmail($body, $to, $options = array()) {
        $defaults = array(
            'bcc' => null,
            'cc' => null,
            'from' => array(self::getSetting('App', 'email.from') => self::getSetting('App', 'name')),
            'replyTo' => null,
            'returnPath' => null,
            'sender' => null,
            'subject' => null,
            'inserts' => null,
            'engine' => self::$mailEngine,
            'embedImages' => false,
            'textBody' => null,
            'attachments' => array(),
            'log' => null,
            'logLength' => 0,
        );
        $options = array_merge($defaults, $options);
        
        // apply debug mail options if turned on
        if (!empty(self::$debugEmailOptions['debug'])) {
            $debugInfo = 
                '<div style="color:#AAA"><b>Original mail options:</b><pre>' . 
                str_replace(PHP_EOL , '', nl2br(trim(print_r(array_merge(array('to' => $to), $options), true)))) . '</pre>' .
                '<b>Original mail body:</b></div><br/>';
            // resolve from email as 'from' can be provided also in form of associative array,e.g. array('<EMAIL>' => Test User')
            $fromEmail = $options['from'];
            if (is_array($fromEmail)) {
                if (Validate::assocArray($fromEmail)) {
                    $fromEmail = array_keys($fromEmail);
                }
                $fromEmail = reset($fromEmail);
            }
            $options['subject'] = '[DEBUG from ' . $fromEmail . '] ' . $options['subject'];
            $options = array_merge($options, array_intersect_key(self::$debugEmailOptions, $options));
            if (!empty(self::$debugEmailOptions['to'])) {
                $to = self::$debugEmailOptions['to'];
            }
        }
        
        if (empty($body)) {
            throw new Exception(__e(__FILE__, 'Email body must not be empty'));
        }
        
        // apply inserts
        if ($options['inserts']) {
            $body = Str::fill($body, $options['inserts']);
            if ($options['subject']) {
                $options['subject'] = Str::fill($options['subject'], $options['inserts']);
            }
            if ($options['textBody']) {
                $options['textBody'] = Str::fill($options['textBody'], $options['inserts']);
            }
        }

        // add debug info to body only after body inserts are applied 
        if (!empty(self::$debugEmailOptions['debug'])) {
            $body = $debugInfo . $body;
        }
        
        // autogenerate textBody if not provided
        if ($options['textBody'] === null) {
            $options['textBody'] = $body;
        }
        // remove any HTML markup from text body
        $options['textBody'] = Sanitize::htmlToText($options['textBody']);

        // include SwiftMailer (cca + 19 ms)
        self::loadVendor('App', 'swiftmailer/lib/swift_required.php');

        // Create the Transport
        if ($options['engine'] == 'smtp') {
            // apply debug mail smtp options if turned on
            if (
                !empty(self::$debugEmailOptions['debug'])
                && !empty(self::$debugEmailOptions['smtp'])
            ) {
                $host = Sanitize::value(self::$debugEmailOptions['smtp']['host']);
                $port = Sanitize::value(self::$debugEmailOptions['smtp']['port']);
                $username = Sanitize::value(self::$debugEmailOptions['smtp']['username']);
                $password = Sanitize::value(self::$debugEmailOptions['smtp']['password']);
                $encryption = Sanitize::value(self::$debugEmailOptions['smtp']['encryption']);
            }
            else {
                $host = self::getSetting('App', 'smtp.host');
                $port = self::getSetting('App', 'smtp.port');
                $username = self::getSetting('App', 'smtp.username');
                $password = self::getSetting('App', 'smtp.password');
                $encryption = self::getSetting('App', 'smtp.encryption');
            }
            // normalize encryption
            $encryption = strtolower($encryption);
            if ($encryption !== 'ssl' && $encryption !== 'tls') {
                $encryption = null;
            }
            $Transport = Swift_SmtpTransport::newInstance(
                $host, $port, $encryption
            );
            if (!Validate::emptyValue($username)) {
                $Transport->setUsername($username);
            }
            if (!Validate::emptyValue($password)) {
                $Transport->setPassword($password);
            }
        }
        else {
            $Transport = Swift_MailTransport::newInstance();
        }
        // create the Mailer using your created Transport
        $Mailer = Swift_Mailer::newInstance($Transport);
                
        // add logger to get log, this helps mainly when if $Mailer->send() returns FALSE
        $Logger = null;
        if ($options['logLength'] > 0) {
            $Logger = new Swift_Plugins_Loggers_ArrayLogger($options['logLength']);
            $Mailer->registerPlugin(new Swift_Plugins_LoggerPlugin($Logger));
        }

        // create a message
        $Message = Swift_Message::newInstance();
        
        // set subject (make it here to allow Decorater application if ever Decorater will be used)
        if($options['subject']) {
            $Message->setSubject($options['subject']);
        }
        
        // embed images in html version of message
        if ($options['embedImages']) {
            $HtmlDoc = new DOMDocument();
            $HtmlDoc->loadHTML($body);
            $Images = $HtmlDoc->getElementsByTagName('img');
            $images = array();
            $cids = array();
            foreach ($Images as $Image) {
                $image = $Image->getAttribute('src');
                $imagePath = rawurldecode($image);
                $imagePath = explode('?', $imagePath);
                $imagePath = $imagePath[0];
                if (preg_match('/^(?:https?:)?\/\//i', $imagePath)) {
                    continue;
                }
                $imagePath = ROOT . DS . ltrim(File::normalizeDS($imagePath), DS);
                if (
                    is_readable($imagePath)
                    && !in_array($image, $images)
                ) {
                    $images[] = $image;
                    $cids[] = $Message->embed(Swift_Image::fromPath($imagePath));
                }
            }    
            $body = str_replace($images, $cids, $body);
        }
        
        // add <body> and <html> tags to html body
        if (!preg_match('/<\/?body(?:>|[^a-z>][^>]*>)/i', $body)) {
            $body = '<body>' . $body . '</body>';
        }
        if (!preg_match('/<\/?html(?:>|[^a-z>][^>]*>)/i', $body)) {
            $body = '<html>' . $body . '</html>';
        }
        $Message->setBody($body, 'text/html');
        $Message->addPart($options['textBody'], 'text/plain');
       
////mojo: it is not necessary to use a decorater for a single message sent
////        In a case of single message the str_replace() is ok (see above)
//        // create decorator for user specific inserts
//        $inserts = array( 
//            $to => array(
//                ':firstname:' => 'Test',
//                ':lastname:' => 'User',
//            )
//        );
//        $Decorator = new Swift_Plugins_DecoratorPlugin($inserts);
//        $Mailer->registerPlugin($Decorator);
//        // decorator plugin personalizes the message when setTo() is called
//        $Message->setTo(array($to => "Test User"));

        /*/debug>>
        self::log('sendMail', 'Options of App::sendMail()', array(
            'var' => array(
                'host' => $host,
                'port' => $port,
                'username' => $username,
                'password' => $password,
                'encryption' => $encryption,
                'options' => $options,
            ),
        ));
        //*///<<debug

        // set to, bcc, cc, ...
        $Message->setTo($to);
        if($options['bcc']) {
            $Message->setBcc($options['bcc']);
        }
        if($options['cc']) {
            $Message->setCc($options['cc']);
        }
        if($options['from']) {
            $Message->setFrom($options['from']);
        }
        if($options['replyTo']) {
            $Message->setReplyTo($options['replyTo']);
        }
        if($options['returnPath']) {
            $Message->setReturnPath($options['returnPath']);
        }
        if($options['sender']) {
            $Message->setSender($options['sender']);
        }
        elseif ($options['engine'] === 'smtp') {
            if (Validate::email($username)) {
                $Message->setSender($username);
            }
            elseif (Validate::email(($sender = App::getSetting('App', 'email.from')))) {
                $Message->setSender($sender);
            }
            else {
                throw new Exception(__e(__FILE__, 'No sender has been set'));
            }
        }
        
        // set attachments
        if($options['attachments']){
            // normalize
            $options['attachments'] = (array)$options['attachments'];
            if (
                isset($options['attachments']['data'])
                && isset($options['attachments']['filename'])
                && isset($options['attachments']['contentType'])
                ||
                isset($options['attachments']['path'])
                && isset($options['attachments']['contentType'])
            ) {
                $options['attachments'] = array($options['attachments']);
            }
            // attach
            foreach ($options['attachments'] as $attachment) {
                if (is_string($attachment)) {
                    $attachment = ROOT . DS . ltrim($attachment, DS);
                    if (!is_readable($attachment)) {
                        continue;
                    }
                    $Message->attach(
                        Swift_Attachment::fromPath($attachment)
                    );
                }
                elseif (
                    isset($attachment['path'])
                    && isset($attachment['contentType'])
                ) {
                    $attachment['path'] = ROOT . DS . ltrim($attachment['path'], DS);
                    if (!is_readable($attachment['path'])) {
                        continue;
                    }
                    $Message->attach(
                        Swift_Attachment::fromPath($attachment['path'], $attachment['contentType'])
                    );
                }
                elseif (
                    isset($attachment['data'])
                    && isset($attachment['filename'])
                    && isset($attachment['contentType'])
                ) {
                    $Message->attach(
                        Swift_Attachment::newInstance($attachment['data'], $attachment['filename'], $attachment['contentType'])
                    );
                }
                else {
                    throw new Exception(__e(__FILE__, 'Invalid attachment format'));
                }
            }
        }
        
        // send it
        $result = $Mailer->send($Message);
        if ($Logger) {
            $options['log'] = $Logger->dump();
        }
        return $result;
    }
    
    /**
     * Sends a single sms using webservice http://www.smartsms.sk to slovak phone numbers
     * 
     * ATTENTION: If both 'username' and 'password' options are empty the the method
     * does silently nothing and FALSE is returned.
     * 
     * @param string $message Message to be send
     * @param string|int $to Recipient phone number
     * @param array $options Following are available
     *      - 'username' (string) Username of smartsms account. Defaults to setting 
     *          App.smartsms.username
     *      - 'password' (string) Password of smartsms account. Defaults to setting
     *           App.smartsms.password
     *      - 'from' (string|int) Sender phone number. It must be allowed in smartsms 
     *          account and must be provided here in the same form. Defaults to setting 
     *          App.smartsms.from
     *      - 'inserts' (array) Array of pairs '{insert}' => '{value}'. See Str::fill() for details.
     *          Defaults to NULL.
     * 
     * @return bool TRUE if message was successfuly sent. FALSE if both smartsms username 
     *      and password are not provided or are both empty and the method does silently 
     *      nothing. Throws an exception on invalid inputs or on sms send failure.
     * 
     * @throws Exception_App_SendSmsFailure on invalid inputs or on sms send failure.
     *      See exception codes in Exception_App_SendSmsFailure phpDoc.
     */
    static public function sendSms($message, $to, $options = array()) {
        $defaults = array(
            'username' => self::getSetting('App', 'smartsms.username'),
            'password' => self::getSetting('App', 'smartsms.password'),
            'from' => self::getSetting('App', 'smartsms.from'),
            'inserts' => null,
        );
        $options = array_merge($defaults, $options);   
        $username = $options['username'];
        $password = $options['password'];
        $from = $options['from'];
        
        if (empty($username) &&  empty($password)) {
            return false;
        }
        
        if (empty($message)) {
            throw new Exception_App_SendSmsFailure(
                __e(__FILE__, 'Sms message must not be empty'),
                1000
            );
        }
        if (empty($from)) {
            throw new Exception_App_SendSmsFailure(
                __e(__FILE__, 'Sms sender phone must not be empty'),
                1001
            );
        }
        // normalize and validate phone number (let only last 9 numbers)
        $to = preg_replace('/([^0-9])/', '', $to);
        if (
            strlen($to) > 9
            && ($prefix = substr($to, 0, -9)) !== '0'
            && $prefix !== '421'
            && $prefix !== '0421'
            && $prefix !== '00421'
        ) {
            throw new Exception_App_SendSmsFailure(
                __e(__FILE__, 'Sms receiver phone must be a slovak mobile phone number'),
                1002
            );
        }
        $to = substr($to, -9);
        if (
            strlen($to) != 9
            || substr($to, 0, 1) != '9'
        ) {
            throw new Exception_App_SendSmsFailure(
                __e(__FILE__, 'Invalid receiver phone'),
                1003
            );        
        }
        $to = '421' . $to;
        $from = preg_replace('/([^0-9])/', '', $from);
        $password = md5($password);
        if ($options['inserts']) {
            $message = Str::fill($message, $options['inserts']);
        }
        // according to documentation UTF-8 should work but ...
        $message = Sanitize::nonAscii($message);
        $message = urlencode($message);
        $params = '?username=' . $username . '&password=' . $password
            . '&to=' . $to . '&message=' . $message . '&from=' . $from;
        $url = fopen('http://www.smartsms.sk/api/send.do' . $params, 'r');
        $result = fread($url, 1024);
        fclose($url);
        if (substr($result, 0, 2) != 'OK') {
            $errorMessages = array(
                11 => 'Správa je príliš dlhá',
                13 => 'Nesprávne číslo príjemcu',
                14 => 'Nesprávne prihlasovacie meno odosielateľa',
                15 => 'Neoverený odosielateľ (tel. číslo)',
                16 => 'Neoverený odosielateľ (11 alfanumerických znakov)',
                17 => 'Flash SMS nemôžete odosielať so špeciálnymi znakmi',
                18 => 'Nesprávny počet parametrov',
                19 => 'Príliš veľa príjemcov (maximum je 100)',
                101 => 'Nesprávna autorizácia',
                102 => 'Nesprávne prihlasovacie meno alebo heslo',
                103 => 'Nedostatok kreditu',
                301 => 'ID SMS neexistuje',
                400 => 'Nesprávne ID SMS, ktorej zisťujete status',
                999	 => 'Interná chyba (kontaktujte nás prosím) ',
            );
            $errorCode = explode(':', $result);
            if (!empty($errorCode[1]) && !empty($errorMessages[$errorCode[1]])) {
                $errorMessage = $errorMessages[$errorCode[1]];
            }
            else {
                $errorMessage = $result;
            }
            throw new Exception_App_SendSmsFailure(
                __e(__FILE__, 'Send of sms has failed because of following error: %s', $errorMessage),
                Sanitize::value($errorCode[1])
            );
        }
        return true;
    }

    /**
     * Gets locator of content specified by pid and lang
     * 
     * @param string $pid Content pid
     * @param array $options An array of following options:
     *      - 'lang' (string) Lang code to retrieve content for. Defaults to App::$lang.
     *      - 'publishedOnly' (bool) If TRUE then specified content must be active. 
     *          Defaults to TRUE.
     * 
     * @return string Content locator. NULL if content with specified pid and lang does not exist.
     */
    static public function getContentLocatorByPid($pid, $options = array()) {
        $defaults = array(
            'lang' => self::$lang,
            'publishedOnly' => true,
        );
        $options = array_merge($defaults, $options);
        $options['fields'] = array(self::$contentsTable . '.locator');
        // retrieve content form DB according to provided $pid
        $options['conditions'] = array(
            self::$contentsTable . '.pid' => $pid,
            self::$contentsTable . '.lang' => $options['lang'],
        );
        if ($options['publishedOnly']) {
            $options['conditions'][self::$contentsTable . '.active'] = true;
        }
        $content = self::getContent($options);
        return Sanitize::value($content['locator']);
    }    
    
    /**
     * Ensures existence of assets table used by methods App::getJsLinks(), App::getHeadJsLinks() 
     * and App::getCssLinks()
     */
    static protected function ensureAssetsTable() {
        if (!self::$assetsTableEnsured) {
            DB::createTable(
                self::$assetsTable, 
                self::$assetsTableSchema,
                array(
                    // use MyISAM engine for case that assets are reserved inside 
                    // of a transaction to ensure that the table recordings are
                    // saved immediatelly and not only after transaction commit
                    'engine' => 'MyISAM',
                )
            );
            // @todo - this will not work on column of type TEXT
            //DB::query('ALTER TABLE  `_run_assets` ADD UNIQUE (`files` ,`files_modified`);');
            self::$assetsTableEnsured = true;
        }
    }
    
    /**
     * Reserves execution of code just for one process and so avoid possible parallel
     * collisions. Use it like:
     * 
     *      // code which can be executed paralelly
     *      $file = TMP . DS . 'counterOfSomethingInFile.txt';
     *      App::reserveProcessing('CounterIncrement');
     *      // code which can be executed only by one process at the moment
     *      $counterValue = (int)file_get_contents($file);
     *      $counterValue++;
     *      file_put_contents($file, $counterValue);
     *      App::unreserveProcessing('CounterIncrement');
     *      // code which can be executed paralelly
     * 
     * @param string $name Unique name of processing reservation. To assure uniquity 
     *          use following pattern to create name: {className}::{methodName}()[/{processingSpecificity}]. 
     *          E.g. if you would like reserve processing for imports of new items
     *          then reservation name in method Import::addtion() would be
     *          'Import::addition()'. But if in case of update import only updates of 
     *          the same type may not be parallel then in Import::update('myType')
     *          the reservation name would be 'Import::update()/myType'.
     * @param string|array $tables Single table name or an array of such names
     * @param array $options Optional. Following options can be specified:
     *      - 'tries' (int) Number of tries to reserve the tables in case that it does 
     *          not succeed on first attempt. Defaults to 100, means 100 attempts are made.
     *      - 'retryTime' (int) Number of miliseconds to wait between reservations attempts.
     *          Defaults to 200.
     * 
     * @throws Exception_App_ProcessingReservationFailure if the reservation of 
     *      specified processing $name has failed
     */
    static public function reserveProcessing($name, $options = array()) {
        try {
            $options['exceptionOnDuplicitReservation'] = false;
            $name = '_processingReservation_' . $name;
            DB::reserveTables($name, $name, $options);
        } 
        catch (Exception_DB_TablesReservationFailure $e) {
            throw new Exception_App_ProcessingReservationFailure(
                __e(__FILE__, 'Reservation of %s processing has failed', $name),
                0,
                $e
            );
        }
    }
    
    /**
     * Unreserves execution of code reserved by App::reserveProcessing() for provided $name
     * 
     * @param string $name Unique name of processing reservation.
     */
    static public function unreserveProcessing($name) {
        $name = '_processingReservation_' . $name;
        DB::unreserveTables($name);
    }
    
    /**
     * Closes all opend output buffers to allow direct write to output. 
     * Can be used e.g. for huge XML files generation directly into output.
     * Output buffering can must be restored by App::reopenClosedOutputBuffers().
     * E.g. in some view file:
     * 
     *      echo 'my buffered output';
     *      $buffersData = App::closeOpenedOutputBuffers();
     *      echo 'my direct output';
     *      App::reopenClosedOutputBuffers($buffersData);
     *      echo 'another buffered output';
     *      
     * @return array Closed buffers data to be passed into App::reopenClosedOutputBuffers()
     */
    static public function closeOpenedOutputBuffers() {
        $outputs = array();
        $levels = ob_get_level();
        for ($i = 0; $i < $levels; $i++) {
            $outputs[$i] = ob_get_contents();
            ob_end_clean();
        }
        return array('outputs' => $outputs, 'levels' => $levels);
    }
    
    /**
     * Restores output buffers closed by App::closeOpenedOutputBuffers()
     * 
     * @param array $closedBuffersData Output of App::closeOpenedOutputBuffers()
     * 
     * @throws Exception on invalid close handle
     */
    static public function reopenClosedOutputBuffers(&$closedBuffersData) {
        if (
            !is_array($closedBuffersData)
            || !array_key_exists('levels', $closedBuffersData)
            || !array_key_exists('outputs', $closedBuffersData)
            || $closedBuffersData['levels'] !== count($closedBuffersData['outputs'])
        ) {
            throw new Exception(__e(__FILE__, 'Invalid $closedBuffersData'));
        }
        for ($i = $closedBuffersData['levels'] - 1; $i >= 0; $i--) {
            ob_start();
            echo $closedBuffersData['outputs'][$i];
        }
        // invalidate $closedBuffersData to avoid duplicit usage
        unset($closedBuffersData['levels']);
        unset($closedBuffersData['outputs']);
    }
}
class Exception_App_ProcessingReservationFailure extends Exception {}
class Exception_App_RequestFailure extends Exception {}

/**
 * There are following exception codes:
 *  - 1000 - empty message
 *  - 1001 - empty receiver phone number
 *  - 1002 - non-slovak receiver phone number
 *  - 1003 - invalid receiver phone number
 *  - 11-999 - error codes of sms service provider (smartsms). Their 
 *      meanings see at the end of App::sendSms().
 */
class Exception_App_SendSmsFailure extends Exception {}
