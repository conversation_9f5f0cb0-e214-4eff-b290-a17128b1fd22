<?php
/**
 * ModuleObject class implements methods used by Controller, Model and Template classes.
 * Those classes extends ModuleObject class.
 * 
 * For translations use __(__FILE__, 'My text to translate) inside ModuleObject instances.
 */
class ModuleObject {
    
    /**
     * Set on runtime
     * 
     * Name of module
     * 
     * @var string
     */
    protected $module = null;
    
    /**
     * Set on runtime
     * 
     * Name of the class
     *
     * @var string
     */
    protected $name = null;
    
    public function __construct($module = null) {
        // set class name
        $this->name = get_class($this);
        // set module name
        if ($module === null) {
            $this->module = App::getClassModule($this->name);
        }
        else {
            $this->module =$module;
        }
    }
    
    /**
     * Returns class name
     * 
     * @return string
     */
    public function getPropertyModule() {
        return $this->module;
    }
    
    /**
     * Returns class name
     * 
     * @return string
     */
    public function getPropertyName() {
        return $this->name;
    }
    
    /**
     * Returns list of current module models in array like:
     * 
     *      array(
     *          '{Model01}' => {Model01},
     *          '{Model02}' => {Model02},
     *          ...
     *      ),
     * 
     * @return array The array described here above. If current module has no
     *      models  then empty array is returned.
     * 
     * @throws Exception if module dir is not readable
     */
    public function getModels() {
        return App::getModels($this->module);
    }
    
    /**
     * Gets config values from current module config.php file, e.g.:
     *      - $MyModule->getConfig(); // will return whole current module $config array
     *      - $MyModule->getConfig('fbApiId'); // will return value of current module $config['facebookUrl']
     *      - $MyModule->getConfig('google.analytics'); // will return value of current module $config['google']['analytics']
     * 
     * @param string $name Optional. Name of config item. If omitted (NULL) then 
     *      whole config array is returned. Defaults to NULL.
     * @param string $file Explicit name of config source file. The given path 
     *      must be relative to ROOT. If omitted (NULL) or empty then the default 
     *      config of given module is supposed. The default config is the first one 
     *      required for module. Defaults to NULL.
     * 
     * @return mixed 
     */
    protected function getConfig($name = null, $file = null) {
        return App::getConfig($this->module, $name, $file);
    }
        
    /**
     * Gets a value of specified current module setting
     * 
     * @param string $pid 
     * @param array $options Following are available
     *      - 'translate' (bool) If TRUE then setting value is retrieved for provided lang.
     *          If FALSE then translation is turned off, means default lang values are used. 
     *          Defaults to TRUE.
     *      - 'lang' (string) Lang specification for value translation. 
     *          Defaults to actual app lang.
     * 
     * @return string Setting value
     */
    protected function getSetting($pid, $options = array()) {
        return App::getSetting($this->module, $pid, $options);
    }    
    
    /**
     * Sets a value of specified current module setting
     * 
     * @param string $pid 
     * @param string $value
     * @param array $options Following are available
     *      - 'translate' (bool) If TRUE then setting value is set for provided lang.
     *          If FALSE then translation is turned off, means default lang values are set. 
     *          Defaults to TRUE.
     *      - 'lang' (string) Lang specification for value translation. 
     *          Defaults to actual app lang.
     *  
     * @return return The old setting value is returned
     */
    protected function setSetting($pid, $value, $options = array()) {  
        return App::setSetting($this->module, $pid, $value, $options);
    }    
    
    /**
     * Reloads current module settings
     */
    protected function reloadSettings() {  
        return App::reloadSettings($this->module);
    }    
    
    /**
     * Gets global value of current module
     * 
     * @param string $name
     * @return mixed 
     */
    protected function getGlobal($name = null) {  
        return App::getGlobal($this->module, $name);
    }
    
    /**
     * Sets global value of current module
     * 
     * @param string $name
     * @param mixed $value
     * @return mixed The old glbal value is returned
     */
    protected function setGlobal($name, $value) {
        return App::setGlobal($this->module, $name, $value);
    }
    
    /**
     * Sets the App::$layoutModule to current module and App::$layoutName property to 
     * provided value
     * 
     * NOTE: If $name is FALSE then the layout is turned off.
     * 
     * @param string|FALSE $name Optional. Name of layout file. If not provided then 
     *      the last set layout name is preserved and just module is changed.
     *      If FALSE then layout is turned off. Defaults to NULL (= not provided). 
     */
    protected function setLayout($name = null) {
        App::setLayout($this->module, $name);
    }
    
    /**
     * Sets the App::$doctypeModule to current module and App::$doctypeName property to 
     * provided value
     * 
     * NOTE: If $name is FALSE then the doctype is turned off. Moreover, doctype is used 
     *      only if layout is used. It means that if the layout is turned off then also
     *      no doctype will be loaded (= it's turned off).
     * 
     * @param string|FALSE $name Optional. Name of doctype file. If not provided then 
     *      the last set doctype name is preserved and just module is changed.
     *      If FALSE then doctype is turned off. Defaults to NULL (= not provided). 
     */
    protected function setDoctype($name = null) {
        App::setDoctype($this->module, $name);
    }
    
    /**
     * Loads specified library under current module libs/ folder.
     * 
     * @param string $name Path to library script placed under libs folder. 
     */
    protected function loadLib($name) {
        App::loadLib($this->module, $name);
    }    
    
    /**
     * Loads specified vendor (= external) library under current module vendors/ folder.
     * 
     * @param string $name Path to library script placed under vendors folder. 
     */
    protected function loadVendor($name) {
        App::loadVendor($this->module, $name);
    }    
    
    /**
     * Loads specified model under current module.
     * 
     * Use controller __construct() method to preload controller model(s), 
     * e.g. in Users conroller:
     * 
     *      public function __construct(){
     *          parent::__construct();
     *          $this->loadModel('MyModuleModel');
     *      }
     * 
     * @param string $name Model class name. 
     * @param bool $getInstance If TRUE then a model instance is returned.
     *      ATTENTION: Always the same (singleton) instance of model is returned!
     *      Defaults to FALSE.
     * 
     * @return void|Model If $getInstance is TRUE the a singleton instance of model is returned.
     *      Otherwise void. On invalid model specification an Exception is thrown.
     */
    protected function loadModel($name, $getInstance = false) {
        return App::loadModel($this->module, $name, $getInstance);
    }
    
    /**
     * Loads specified controller under current module.
     * 
     * @param string $name Controller class name. 
     * @param bool $getInstance If TRUE then a controller instance is returned.
     *      ATTENTION: Always the same (singleton) instance of controller is returned!
     *      Defaults to FALSE.
     * 
     * @return void|Controller If $getInstance is TRUE the a singleton instance 
     *      of controller is returned. Otherwise void.
     */
    protected function loadController($name, $getInstance = false) {
        return App::loadController($this->module, $name, $getInstance); 
    }    
    
    /**
     * Loads script placed under current module views/ folder and returns it's html output.
     *
     * @param string $name Name of script name placed under views folder.
     * @param array $params Optional. Associaltive array of view params available as $this->params in view. 
     *      This can be easily created by compact() function, e.g.: compact('paramForScript1', 'paramForScript2', ...)
     *      or it can be created manualy, e.g.: array('param1' => 'its value', 'param2' => 'its value' , ...)
     *      Defaults to empty array().
     * @param array $data Optional. View data available as $this->data in view. 
     *      Defaults to empty array().
     * @param string|array $catchVariables Optional. A single variable name or an array
     *      of such names to be catched after the view is loaded. They
     *      are returned by aux output $catchedVariables. If the specified variable
     *      does not exist (is not created by view) then it is not present
     *      in $catchedVariables. If this option is any empty value then nothing is 
     *      catched. Defaults to NULL.
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return string Html output generated by the script. 
     */
    protected function loadView($name, $params = array(), $data = array(), $catchVariables = null, &$catchedVariables = array()) {
        return App::loadView($this->module, $name, $params, $data, $catchVariables, $catchedVariables);
    }
    
    /**
     * Loads script placed under current module elements/ folder and returns it's
     * html output.
     * 
     * @param string $name Name of script name placed under elements folder.
     * @param array $params Optional. Associaltive array of element params available as $this->params in element. 
     *      This can be easily created by compact() function, e.g.: compact('paramForScript1', 'paramForScript2', ...)
     *      or it can be created manualy, e.g.: array('param1' => 'its value', 'param2' => 'its value' , ...)
     *      Defaults to empty array() - means no params are passed.
     * @param array $data Optional. Element data available as $this->data in element. 
     *      If TRUE then data are retrieved by App::getSubjectData() from request data. 
     *      Defaults to TRUE.
     * @param string|array $catchVariables Optional. A single variable name or an array
     *      of such names to be catched after the element is loaded. They
     *      are returned by aux output $catchedVariables. If the specified variable
     *      does not exist (is not created by element) then it is not present
     *      in $catchedVariables. If this option is any empty value then nothing is 
     *      catched. Defaults to NULL.
     * @param array& $catchedVariables Optional. Auxiliary output used to return
     *      catched variables if option 'catchVariables' is set. If no one of specified 
     *      variables exists then this can be an empty array.
     * 
     * @return string Html output generated by the script. 
     */
    protected function loadElement($name, $params = array(), $data = true, $catchVariables = null, &$catchedVariables = array()) {
        return App::loadElement($this->module, $name, $params, $data, $catchVariables, $catchedVariables);
    }
    
    /**
     * Loads specified Controller::action() under current module and returns its output.
     * 
     * @param string $controller Controller class name.
     * @param string $action Action name must correspond exactly with required action() name.
     * @param array $params Optional. Action dispatch params. 
     * @param array $data Optional. Action dispatch data. If TRUE then data are retrieved 
     *      by App::getSubjectData() from request data. Defaults to TRUE.
     * @param array|mixed $args Controller dispatch args or single arg int|float|string. 
     *      They are used as input arguments for $action method
     * 
     * @return mixed Controller action output.
     */
    protected function loadControllerAction($controller, $action, $params = array(), $data = true, $args = array()) {
        return App::loadControllerAction($this->module, $controller, $action, $params, $data, $args);
    }
    
    /**
     * Loads specified Content block front-end view under current module.
     * 
     * @param string $contentBlock Content block model class name.
     * @param array $data Content block raw data passed to ContentBlock::prepareViewData()
     *          and then to content block frontend view. This can be an instance
     *          data (in common case) or an explicit data to use the content block 
     *          view for some custom purpose (kind of "element"). There is no owner 
     *          in case of explicit data, so no "owner" options should be set in such a case.
     * @param array $options Options passed to ContentBlock::prepareViewData(). Following are available:
     *      - 'ownerModel' (string) Block owner model. To ensure uniquity the model name should 
     *          be qualified by its module, e.g. 'App.WebContent', 'Eshop.EshopProduct'. 
     *          It is up to each ContentBlock definition how this is used/processed 
     *          or maybe ignored here. This should be set only in case of instance data.
     *          Defaults to NULL.
     *      - 'ownerId' (string) Block owner id. It is up to each ContentBlock definition
     *          how this is used/processed or maybe ignored here. This should be set only 
     *          in case of instance data. Defaults to NULL.
     *      - 'ownerRecord' (array) Block owner record data. It is up to each ContentBlock definition
     *          how this is used/processed or maybe ignored here. This should be set only 
     *          in case of instance data. Defaults to NULL.
     * 
     * @return string Content block front-end view html.
     */
    static public function loadContentBlockView($contentBlock, $data = array(), $options = array()) {
        return App::loadContentBlockView($this->module, $contentBlock, $data, $options);
    }
    
    /**
     * Sets the App::$cssFiles property, containing .css files dynamically attached to html header
     * in layout
     * 
     * @param string|array $files Single .css filename (string) or an array of 
     *      .css filenames to be attached in html document header (see App/elements/cssHeader). 
     *      Filename must be specified with full path starting from current module /css folder.
     *      In the most cases, the simple name of .css file will be enough, as the most of .css
     *      files are placed directly under /css folder. To specify attributes of <link> 
     *      tag use syntax: array('my.css' => array('disabled' => true, ...)).
     *      To specify media attribute a shorthand array('my.css' => 'print') can be used
     *      instead of array('my.css' => array('media' => 'print')).
     */
    protected function setCssFiles($files) {
        App::setCssFiles($this->module, $files);
    }
    
    /**
     * Sets the App::$jsFiles property, containing .js files dynamically attached html header
     * in layout
     * 
     * @param string|array $files Single .js filename (string) or an array of 
     *      .js filenames to be attached in html document header (see App/elements/jsHeader). 
     *      Filename must be specified with full path starting from current module /js folder.
     *      In the most cases, the simple name of .js file will be enough, as the most of .js
     *      files are placed directly under /js folder. To specify attributes of <script> tag 
     *      use syntax: array('my.js' => array('async' => true, 'type' => 'module', ...)).
     *      To specify type attribute a shorthand array('my.js' => 'module') can be used
     *      instead of array('my.js' => array('type' => 'module')).
     */
    protected function setJsFiles($files) {
        App::setJsFiles($this->module, $files);
    }
    
    /**
     * Sets new js config and it's value
     * 
     * @param string $path Config path relative to module, e.g. 'urlRoot' or 'MyClass.myProperty'
     * @param mixed $value Config value.
     * 
     * An alternative signature can be used:
     * 
     * @param array $config List of pairs 'cofigPath' => 'value', e.g. array('urlRoot' => '',  'MyClass.myProperty' => '2')
     */
    protected function setJsConfig($path, $value = null) {
        App::setJsConfig($this->module, $path, $value);
    }    
    
    /**
     * Returns a url path to specified file in current module. The path is either
     * URL_ROOT relative or is prefixed by URL_ROOT depending on value od $prefixByUrlRoot.
     * 
     * @param string $file Module relative url path to file.
     * @param bool $prefixByUrlRoot If TRUE then path is prefixed by URL_ROOT. Defaults
     *          to FALSE, means the returned path is URL_ROOT relative.
     * 
     * @return string
     */
    protected function getFileUrlPath($file, $prefixByUrlRoot = false) {
        return App::getFileUrlPath($this->module, $file, $prefixByUrlRoot);
    }
    
    /**
     * Sets admin smartTabs icon for layout
     * 
     * @param string $icon Icon filename specified with full path starting from 
     *      current module /img folder, e.g. '/silk/my_icon.png'.
     */
    protected function setTabIcon($icon) {
        App::setTabIcon($this->module, $icon);
    }
}
