<?php
class Sanitize {
    
    /**
     * Converts to ascii and removes from string all characters which are not 
     * allowed in php script name. Possible extension is converted to lowercase.
     * 
     * @param string $string
     * @return string 
     */
    public static function filename($string) {
        $nameParts = File::getPathinfo($string);
        // convert to ascii
        $nameParts['filename'] = self::nonAscii($nameParts['filename']);
        // remove entities
	    $nameParts['filename'] = preg_replace("/(&(amp;)?[a-z]+;?)|(&#[0-9]{1,2};?)/", "", $nameParts['filename']);
        // replace spaces by underscores
        $nameParts['filename'] = preg_replace('/\s+/', '_', $nameParts['filename']);
	    // remove all forbidden characters
        $string = preg_replace('/[^a-zA-Z0-9\_\-\.]/', '', $nameParts['filename']);
        // normalize extension
        if (!empty($nameParts['extension'])) {
            $string .= '.' . File::normalizeExtension($nameParts['extension']);
        }
        return $string;
    }
    
    /**
     * Removes from string all characters which are not alphanumeric.
     * 
     * @param string $string
     * @return string 
     */
    public static function alphanumeric($string) {
        return preg_replace('/[^a-zA-Z0-9]/', '', $string);
    }
    
    /**
    * Replace accented UTF-8 characters by unaccented ASCII-7 "equivalents".
    * The purpose of this function is to replace characters commonly found in Latin
    * alphabets with something more or less equivalent from the ASCII range. This can
    * be useful for converting a UTF-8 to something ready for a filename, for example.
    * Following the use of this function, you would probably also pass the string
    * through utf8_strip_non_ascii to clean out any other non-ASCII chars
    *
    * For a more complete implementation of transliteration, see the utf8_to_ascii package
    * available from the phputf8 project downloads:
    * http://prdownloads.sourceforge.net/phputf8
    *
    * @param string $str UTF-8 string with accented characters replaced by ASCII chars
    * @return string accented chars replaced with ascii equivalents
    * <AUTHOR> Gohr <<EMAIL>>
    * @see http://sourceforge.net/projects/phputf8/
    */
    static function nonAscii($str) {
        static $translationTable = null;

        if ($translationTable === null) {
            $translationTable = array(
              // lowercases
              'à' => 'a', 'ô' => 'o', 'ď' => 'd', 'ḟ' => 'f', 'ë' => 'e', 'š' => 's', 'ơ' => 'o',
              'ß' => 'ss', 'ă' => 'a', 'ř' => 'r', 'ț' => 't', 'ň' => 'n', 'ā' => 'a', 'ķ' => 'k',
              'ŝ' => 's', 'ỳ' => 'y', 'ņ' => 'n', 'ĺ' => 'l', 'ħ' => 'h', 'ṗ' => 'p', 'ó' => 'o',
              'ú' => 'u', 'ě' => 'e', 'é' => 'e', 'ç' => 'c', 'ẁ' => 'w', 'ċ' => 'c', 'õ' => 'o',
              'ṡ' => 's', 'ø' => 'o', 'ģ' => 'g', 'ŧ' => 't', 'ș' => 's', 'ė' => 'e', 'ĉ' => 'c',
              'ś' => 's', 'î' => 'i', 'ű' => 'u', 'ć' => 'c', 'ę' => 'e', 'ŵ' => 'w', 'ṫ' => 't',
              'ū' => 'u', 'č' => 'c', 'ö' => 'oe', 'è' => 'e', 'ŷ' => 'y', 'ą' => 'a', 'ł' => 'l',
              'ų' => 'u', 'ů' => 'u', 'ş' => 's', 'ğ' => 'g', 'ļ' => 'l', 'ƒ' => 'f', 'ž' => 'z',
              'ẃ' => 'w', 'ḃ' => 'b', 'å' => 'a', 'ì' => 'i', 'ï' => 'i', 'ḋ' => 'd', 'ť' => 't',
              'ŗ' => 'r', 'ä' => 'a', 'í' => 'i', 'ŕ' => 'r', 'ê' => 'e', 'ü' => 'ue', 'ò' => 'o',
              'ē' => 'e', 'ñ' => 'n', 'ń' => 'n', 'ĥ' => 'h', 'ĝ' => 'g', 'đ' => 'd', 'ĵ' => 'j',
              'ÿ' => 'y', 'ũ' => 'u', 'ŭ' => 'u', 'ư' => 'u', 'ţ' => 't', 'ý' => 'y', 'ő' => 'o',
              'â' => 'a', 'ľ' => 'l', 'ẅ' => 'w', 'ż' => 'z', 'ī' => 'i', 'ã' => 'a', 'ġ' => 'g',
              'ṁ' => 'm', 'ō' => 'o', 'ĩ' => 'i', 'ù' => 'u', 'į' => 'i', 'ź' => 'z', 'á' => 'a',
              'û' => 'u', 'þ' => 'th', 'ð' => 'dh', 'æ' => 'ae', 'µ' => 'u', 'ĕ' => 'e',
              // uppercases
              'À' => 'A', 'Ô' => 'O', 'Ď' => 'D', 'Ḟ' => 'F', 'Ë' => 'E', 'Š' => 'S', 'Ơ' => 'O',
              'Ă' => 'A', 'Ř' => 'R', 'Ț' => 'T', 'Ň' => 'N', 'Ā' => 'A', 'Ķ' => 'K',
              'Ŝ' => 'S', 'Ỳ' => 'Y', 'Ņ' => 'N', 'Ĺ' => 'L', 'Ħ' => 'H', 'Ṗ' => 'P', 'Ó' => 'O',
              'Ú' => 'U', 'Ě' => 'E', 'É' => 'E', 'Ç' => 'C', 'Ẁ' => 'W', 'Ċ' => 'C', 'Õ' => 'O',
              'Ṡ' => 'S', 'Ø' => 'O', 'Ģ' => 'G', 'Ŧ' => 'T', 'Ș' => 'S', 'Ė' => 'E', 'Ĉ' => 'C',
              'Ś' => 'S', 'Î' => 'I', 'Ű' => 'U', 'Ć' => 'C', 'Ę' => 'E', 'Ŵ' => 'W', 'Ṫ' => 'T',
              'Ū' => 'U', 'Č' => 'C', 'Ö' => 'Oe', 'È' => 'E', 'Ŷ' => 'Y', 'Ą' => 'A', 'Ł' => 'L',
              'Ų' => 'U', 'Ů' => 'U', 'Ş' => 'S', 'Ğ' => 'G', 'Ļ' => 'L', 'Ƒ' => 'F', 'Ž' => 'Z',
              'Ẃ' => 'W', 'Ḃ' => 'B', 'Å' => 'A', 'Ì' => 'I', 'Ï' => 'I', 'Ḋ' => 'D', 'Ť' => 'T',
              'Ŗ' => 'R', 'Ä' => 'A', 'Í' => 'I', 'Ŕ' => 'R', 'Ê' => 'E', 'Ü' => 'Ue', 'Ò' => 'O',
              'Ē' => 'E', 'Ñ' => 'N', 'Ń' => 'N', 'Ĥ' => 'H', 'Ĝ' => 'G', 'Đ' => 'D', 'Ĵ' => 'J',
              'Ÿ' => 'Y', 'Ũ' => 'U', 'Ŭ' => 'U', 'Ư' => 'U', 'Ţ' => 'T', 'Ý' => 'Y', 'Ő' => 'O',
              'Â' => 'A', 'Ľ' => 'L', 'Ẅ' => 'W', 'Ż' => 'Z', 'Ī' => 'I', 'Ã' => 'A', 'Ġ' => 'G',
              'Ṁ' => 'M', 'Ō' => 'O', 'Ĩ' => 'I', 'Ù' => 'U', 'Į' => 'I', 'Ź' => 'Z', 'Á' => 'A',
              'Û' => 'U', 'Þ' => 'Th', 'Ð' => 'Dh', 'Æ' => 'Ae', 'Ĕ' => 'E',
              // special chars
              // @todo Finish this method and put here all possible chars which are invalid for
              // XML tag content. See http://www.fileformat.info/info/charset/UTF-8/list.htm
              // and http://en.wikipedia.org/wiki/Valid_characters_in_XML to achieve this 
              "\xC2\xA0" => ' ', // nbsp (194 160)
              "\xE2\x80\x93" => '-', // long dash (226 128 147)
              "\xC2\xB4" => '\'' // apostrophe (194 180)
                
            );
        }
        
        return strtr($str, $translationTable);
    }     
    
    static function accents($string, $encoding = "utf-8") {
    	if (strtolower($encoding) === "utf-8") {
    		static $utf8 = array(
                "\xC3\x81" => "A", // Á
                "\xC3\x84" => "A", // Ä
                "\xC4\x86" => "C", // Ć
                "\xC4\x8C" => "C", // Č
                "\xC4\x8E" => "D", // Ď
                "\xC3\x89" => "E", // É
                "\xC4\x9A" => "E", // Ě
                "\xC3\x8D" => "I", // Í
                "\xC4\xBB" => "L", // Ĺ
                "\xC4\xBD" => "L", // Ľ
                "\xC5\x87" => "N", // Ň
                "\xC3\x93" => "O", // Ó
                "\xC3\x94" => "O", // Ô
                "\xC3\x96" => "O", // Ö
                "\xC5\x94" => "R", // Ŕ
                "\xC5\x98" => "R", // Ř
                "\xC5\xA0" => "S", // Š
                "\xC5\xA4" => "T", // Ť
                "\xC3\x9A" => "U", // Ú
                "\xC5\xAE" => "U", // Ů
                "\xC3\x9C" => "U", // Ü
                "\xC3\x9D" => "Y", // Ý
                "\xC5\xBD" => "Z", // Ž
                "\xC3\x9F" => "ss", // ß
                "\xC3\xA1" => "a", // á
                "\xC3\xA4" => "a", // ä
                "\xC4\x87" => "c", // ć
                "\xC4\x8D" => "c", // č
                "\xC4\x8F" => "d", // ď
                "\xC3\xA9" => "e", // é
                "\xC4\x9B" => "e", // ě
                "\xC3\xAD" => "i", // í
                "\xC4\xBA" => "l", // ĺ
                "\xC4\xBE" => "l", // ľ
                "\xC5\x88" => "n", // ň
                "\xC3\xB3" => "o", // ó
                "\xC3\xB4" => "o", // ô
                "\xC3\xB6" => "o", // ö
                "\xC5\x95" => "r", // ŕ
                "\xC5\x99" => "r", // ř
                "\xC5\xA1" => "s", // š
                "\xC5\xA5" => "t", // ť
                "\xC3\xBA" => "u", // ú
                "\xC5\xAF" => "u", // ů
                "\xC3\xBC" => "u", // ü
                "\xC3\xBD" => "y", // ý
                "\xC5\xBE" => "z", // ž
    		);
    		$output = $utf8;
    	}
    	else {
    		static $universal = array(
                "\xC1" => "A", // Á
                "\xC4" => "A", // Ä
                "\xC6" => "C", // Ć
                "\xC8" => "C", // Č
                "\xCF" => "D", // Ď
                "\xC9" => "E", // É
                "\xCC" => "E", // Ě
                "\xCD" => "I", // Í
                "\xC5" => "L", // Ĺ
                "\xD2" => "N", // Ň
                "\xD3" => "O", // Ó
                "\xD4" => "O", // Ô
                "\xD6" => "O", // Ö
                "\xC0" => "R", // Ŕ
                "\xD8" => "R", // Ř
                "\xDA" => "U", // Ú
                "\xD9" => "U", // Ů
                "\xDC" => "U", // Ü
                "\xDD" => "Y", // Ý
                "\xDF" => "ss", // ß
                //"\xE1" => "a", // á
                "\xE4" => "a", // ä
                "\xE6" => "c", // ć
                "\xE8" => "c", // č
                "\xEF" => "d", // ď
                "\xE9" => "e", // é
                "\xEC" => "e", // ě
                "\xED" => "i", // í
                "\xE5" => "l", // ĺ
                "\xF2" => "n", // ň
                "\xF3" => "o", // ó
                "\xF4" => "o", // ô
                "\xF6" => "o", // ö
                "\xE0" => "r", // ŕ
                "\xF8" => "r", // ř
                "\xFA" => "u", // ú
                "\xF9" => "u", // ů
                "\xFC" => "u", // ü
                "\xFD" => "y", // ý
    		);
    		if (strtolower($encoding) === "iso-8859-2") {
    			static $iso88592 = array(
                    "\xA5" => "L", // Ľ
                    "\xA9" => "S", // Š
                    "\xAB" => "T", // Ť
                    "\xAE" => "Z", // Ž
                    "\xB5" => "l", // ľ
                    "\xB9" => "s", // š
                    "\xBB" => "t", // ť
                    "\xBE" => "z", // ž
    			);
    			$output= array_merge($universal, $iso88592);
    		}
    		else {
    			static $win1250 = array(
                    "\xBC" => "L", // Ľ
                    "\x8A" => "S", // Š
                    "\x8D" => "T", // Ť
                    "\x8E" => "Z", // Ž
                    "\xBE" => "l", // ľ
                    "\x9A" => "s", // š
                    "\x9D" => "t", // ť
                    "\x9E" => "z", // ž
    			);
    			$output = array_merge($universal, $win1250);
    		}
    	}
    	return strtr($string, $output);       
    }    
    
    /**
     * Converts html to plain text
     * 
     * @param string $html
     * @param bool $keepFormat If TRUE then HTML formating is considered and the 
     *      returned plain text is similarly formated. Defaults to TRUE.
     * 
     * @return string Plain text
     */
    public static function htmlToText($html, $keepFormat = true) {
        // replace all whitespaces (single or acumulated) by single plain space
        $html = preg_replace('/\s+/i', ' ', $html);
        // remove scripts
        $html = preg_replace('/<script(?:>|[^a-z>][^>]*>).*?<\/script>/si', PHP_EOL, $html);
        // remove styles
        $html = preg_replace('/<style(?:>|[^a-z>][^>]*>).*?<\/style>/si', PHP_EOL, $html);
        // remove comments
        $html = preg_replace('/<\!--.*?-->/si', PHP_EOL, $html);
        if ($keepFormat) {
            // replace all ocurences of <p>, </p>, <div>, </div>, <h1-6>, </h1-6>, 
            // <section>, </section>, <article>, </article>, <ul>, </ul>, <table>, </table>
            // <tr>, </tr>, <br>, <aside>, </aside>, <ol>, </ol>, <dl>, </dl> by new line character
            $html = preg_replace('/<\/?(?:p|div|h[1-6]|section|article|ul|table|tr|br|aside|ol|dl)(?:>|[^a-z>][^>]*>)/i', PHP_EOL, $html);
            // replace <li>, <dt> by '    - '
            $html = preg_replace('/<(?:li|dt)(?:>|[^a-z>][^>]*>)/i', '    - ', $html);
            // replace </dt> by ': '
            $html = preg_replace('/<\/dt(?:>|[^a-z>][^>]*>)/i', ': ', $html);
            // replace </li>, </dd> by new line character
            $html = preg_replace('/<\/(?:li|dd)(?:>|[^a-z>][^>]*>)/i', PHP_EOL, $html);
            // replace </td>, </th> by four tabs
            $html = preg_replace('/<\/(?:td|th)(?:>|[^a-z>][^>]*>)/i', "\t\t\t\t", $html);
            // replace <a href="http://...">Click here</a> by Click here [http://....]
            $html = preg_replace('/<a[^>]*?href="([^"]+)"(?:>|[^a-z>][^>]*>)(.*?)<\/a>/i', '$2 [$1]', $html);
            $html = preg_replace("/<a[^>]*?href='([^']+)'(?:>|[^a-z>][^>]*>)(.*?)<\/a>/i", '$2 [$1]', $html);
        }
        else {
            // replace all ocurences of <p>, </p>, <div>, </div>, <h1-6>, </h1-6>, 
            // <section>, </section>, <article>, </article>, <ul>, </ul>, <li>, </li>, <table>, </table>
            // <tr>, </tr>, <td>, </td>, <th>, </th>, <br>, <aside>, </aside>, <ol>, </ol>, <dl>, </dl> by space character
            $html = preg_replace('/<\/?(?:p|div|h[1-6]|section|article|ul|li|table|tr|td|th|br|aside|ol|dl)(?:>|[^a-z>][^>]*>)/i', ' ', $html);
            // replace all whitespaces (single or acumulated) by single plain space
            $html = preg_replace('/\s+/i', ' ', $html);
        }
        // remove all resting html tags
        $html = preg_replace('/<[\/\!a-z][^<>]*>/i', '', $html);
        // convert html entities to applicable characters
        $html = html_entity_decode($html);
        // trim leading and trailing whitespaces
        $html = trim($html);
        
        return $html;
    }
    
    /**
     * Removes invalid tags (< html>, < head>, < body>) from html which is provided 
     * by an  user as some web content (e.g. WebContent.text, EshopProduct.description, ...)
     * 
     * @param string $html
     * 
     * @return string
     */
    public static function contentHtml($html) {
        return self::htmlTags($html, array(
            'avoid' => array(
                'html', 'head', 'body'
            )
        ));
    }
    
    /**
     * Removes html tags from provided html
     * 
     * @param string $html
     * @param array $options Following are available:
     *      - 'allow' (string|array) Array of html tag names to be preserved in provided $html. 
     *          Single tag name can be provided as string. Defaults to empty array(), 
     *          it means all html tags are removed.
     *      - 'avoid' (string|array) Array of html tag names to be removed in provided $html.
     *          Single tag name can be provided as string. Defaults to empty array(), 
     *          it means all html tags are removed.
     *      - 'allowAttributes' (string|array) Array of html tag attribute names to be preserved in provided $html. 
     *          Single attribute name can be provided as string. Defaults to empty array(), 
     *          it means all html attributes are preserved.
     * 
     * @return string
     */
    public static function htmlTags($html, $options = array()) {
        $options = array_merge(array(
            'allow' => array(),
            'avoid' => array(),
            'allowAttributes' => array(),
        ), $options);
        $options['allow'] = (array)$options['allow'];
        $options['avoid'] = (array)$options['avoid'];
        $options['allowAttributes'] = (array)$options['allowAttributes'];
        // escape allow tags
        if ($options['allow']) {
            foreach ($options['allow'] as &$item) {
                $item = preg_quote($item, '#');
            }
            unset($item);
            $regex = implode('|', $options['allow']);
            $regex = '#(</?)(' . $regex . ')(>|[^a-z>][^>]*>)#i';
            $html = preg_replace($regex, '$1_$2$3', $html);
        }
        // remove either specified html tags...
        if ($options['avoid']) {
            foreach ($options['avoid'] as &$item) {
                $item = preg_quote($item, '#');
            }
            unset($item);
            $regex = implode('|', $options['avoid']);
            $regex = '#</?(?:' . $regex . ')(?:>|[^a-z>][^>]*>)#i';
            $html = preg_replace($regex, '', $html);
        }
        // ... or all html tags except allowed ones (if any)
        else {
            $html = preg_replace('#</?[a-z]+(?:>|[^a-z>][^>]*>)#i', '', $html);
        }
        // restore (unescape) allowed html tags
        if ($options['allow']) {
            $regex = implode('|', $options['allow']);
            $regex = '#(</?)_(' . $regex . ')(>|[^a-z>][^>]*>)#i';
            $html = preg_replace($regex, '$1$2$3', $html);
        }
        // keep only allowed attributes
        $tagMatches = array();
        if (
            $options['allowAttributes']
            && preg_match_all(
                '#(<[a-z]+[^a-z>])([^>]{2,})(>)#i', $html, $tagMatches, PREG_SET_ORDER
            )
        ) {
            foreach ($options['allowAttributes'] as &$item) {
                $item = strtolower($item);
            }
            unset($item);
            $options['allowAttributes'] = array_flip($options['allowAttributes']);
            $replacements = array();
            foreach ($tagMatches as $tagMatch) {
                $attributes = $tagMatch[2];
                $attributesParts = explode('" ', $attributes . ' ');
                $tmp = array();
                foreach ($attributesParts as $attributesPart) {
                    $attributeParts = explode('=', $attributesPart);
                    $attributeName = strtolower(trim($attributeParts[0]));
                    if (isset($options['allowAttributes'][$attributeName])) {
                        $tmp[] = $attributesPart;
                    }
                }
                if ($tmp) {
                    $tmp[] = '';
                }
                $attributesParts = $tmp;
                $search = $tagMatch[0];
                $replace = $tagMatch[1] . trim(implode('" ', $attributesParts)) . $tagMatch[3];
                $replacements[$search] = $replace;
            }
            $html = str_replace(array_keys($replacements), $replacements, $html);
        }
        return $html;
    }
    
    /**
     * Removes leading and trailing spaces of each line in provided $text
     * 
     * @param string $text
     * @param string $which Optional. Possible values are 'leading', 'trailing', 
     *          'both'. Defaults to 'both'.
     * 
     * @return string Text with removed trailing spaces
     */
    static public function wrappingSpaces($text, $which = 'both') {
        $text = explode("\n", $text);
        if ($which === 'leading') {
            $callback = 'ltrim';
        }
        elseif ($which === 'trailing') {
            $callback = 'rtrim';
        }
        else {
            $callback = 'trim';
        }
        $text = array_map($callback, $text);
        return implode("\n", $text);
    }
    
    /**
     * Shortcut method to for:
     *      $a = isset($b) ? $b : '';
     * Instead you can write:
     *      $a = Sanitize::value($b, '');
     * 
     * In case that $notEmpty is TRUE the shortcuted ternary expresion is:
     *      $a = isset($b) && $b !== '' && $b !== null ? $b : '';
     *       
     * ATTENTION: If the variable $b does not exist, the use of $a = Sanitize::value($b)
     * will create it with NULL value (unlike to $a = isset($b) ? $b : null). If
     * your functionality depends on existence of array key, e.g. array_key_exists('myKey', $a),
     * then do not use Sanitize::value($a['myKey']). Especially be carefull with 
     * using Sanitize::value() on superglobal arrays ($_COOKIE, $_SERVER, $_GET, $_POST, ...)!
     *       
     * @param mixed $variable
     * @param mixed $defaultValue Optional. Defaults to NULL.
     * @param bool|array $notEmpty Optional. If TRUE then $defaultValue is returned also in case
     *      that $variable is NULL or empty string. Zero value (0), empty array 
     *      and 0000-00-00 00:00:00 are NOT considered to be empty values. Use
     *      array to specify your own list of empty values. Defaults to FALSE.
     * 
     * @return mixed 
     */
    static public function value(&$variable, $defaultValue = null, $notEmpty = false) {
        if ($notEmpty) {
            if (is_array($notEmpty)) {
                return !in_array($variable, $notEmpty, true) ? $variable : $defaultValue;
            }
            return isset($variable) && $variable !== '' && $variable !== null ? $variable : $defaultValue;
        }
        return isset($variable) ? $variable : $defaultValue;
    } 
    
    /**
     * Removes invalid XMl characters (common european, surrogate blocks are not removed - see below)
     * 
     * Valid characters are: #x9 | #xA | #xD | [#x20-#xD7FF] | [#xE000-#xFFFD] | [#x10000-#x10FFFF]	 
     * Means any Unicode character, excluding the surrogate blocks, FFFE, and FFFF. 
     * See here: http://www.w3.org/TR/xml/#charsets
     * 
     * ATTENTION: Some valid XML characters must be still escaped. Use either <![CDATA[...]]>
     * blocks or htmlspecialchars($string, ENT_XML1 | ENT_QUOTES, 'UTF-8') to escape customer provided data.
     * 
     * @param string $string
     */
    static public function invalidXmlChars($string) {
        $invalid = array(
            "\x0", "\x1", "\x2", "\x3", "\x4", "\x5", "\x6",  "\x7",  "\x8",
            "\xB", "\xC", "\xE", "\xF", "\x10", "\x11", "\x12",  "\x13",
            "\x14", "\x15", "\x16", "\x17", "\x18", "\x19", "\x1A",  "\x1B",
            "\x1C", "\x1D", "\x1E", "\x1F",
            
        );
        return str_replace($invalid, '', $string);
        // or could be implemented like
        //$string = preg_replace('/[\x00-\x08\x0B-\x0C\x0E-\x1F]/', '', $string);
    }
    
    /**
     * Sanitizes keys in provided array according to provided options.
     * Only top level keys are sanitized.
     * 
     * @param array $array Associative array
     * @param array $options Following are available:
     *      - 'allowKeys' (string|array) Single key or plain array of keys which 
     *          are alloved to be in array. Defaults to empty, means all keys are allowed.
     *      - 'avoidKeys' (string|array) Single key or plain array of keys which 
     *          have to be removed from array. Defaults to empty, means no keys are removed.
     * 
     * @return array Containing only allowed keys
     */
    static public function arrayKeys($array, $options = array()) {
        $array = (array)$array;
        if (!empty($options['allowKeys'])) {
            $options['allowKeys'] = array_flip((array)$options['allowKeys']);
            $array = array_intersect_key($array, $options['allowKeys']);
        }
        if (!empty($options['avoidKeys'])) {
            $options['avoidKeys'] = array_flip((array)$options['avoidKeys']);
            $array = array_diff_key($array, (array)$options['avoidKeys']);
        }
        return $array;
    }
}