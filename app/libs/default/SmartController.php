<?php
/**
 * SmartController implements following backend actions:
 * 
 *      admin_index()
 *      admin_showTree()
 *      admin_showTreeLevel() @todo
 *      admin_add()
 *      admin_copy() @todo
 *      admin_edit()
 *      admin_view()
 *      admin_delete()
 *      admin_deleteFile()
 *      admin_move()
 *      admin_export()
 * 
 * and backend AJAX actions:
 * 
 *      // to be used both with FormHelper::itemselector() and ::treeselector()
 *      // See the method phpDoc for details
 *      admin_getSelectorItems() 
 *
 *      // to be used with FormHelper::gallery() input
 *      // see also GalleryModel lib phpDoc
 *      admin_getGalleryImages()
 *      admin_addGalleryImage()
 *      admin_updateGalleryImage()
 *      admin_moveGalleryImage()
 *      admin_deleteGalleryImage()
 * 
 * Following JSON API AJAX actions are implemented:
 * 
 *      find()
 *      findFirst()
 *      findList()
 * 
 * Furthermore SmartController implements following frontend actions:
 * 
 *      menu() @todo
 *      index() @todo
 * 
 * All mentioned methods cover plain models (not tree nor ordered list), trees, ordered lists.
 * All of mentioned can be translated either by fields (the most common, e.g. EshopProduct)
 * or by records (used rarely, e.g. WebContent)
 * 
 * USAGE
 * 
 * BASIC:
 * 1] To make all the above functionality available in its default (implicit) form 
 * you just extend SmartController class and define SmartController::$model property
 * (if you omit this step then model name is guessed from controller name):
 * 
 *      class MyController extends SmartController {
 *          protected $model = 'MyModel';
 *      }
 * 
 * In case of models translated by records it is good to specify it explicitly
 * by setting also SmartController::$modelIsTranslatedByRecords to TRUE.
 * 
 * 2] Set rights for actions you would like to use. The only exception are JSON API
 * AJAX actions which must be overwritten in child classes with public visibility
 * (for sake of security). See the ADVANCED section.
 * 
 * 3] Do not forget to specify MyModel::$nameField to make the default version nice.
 * 
 * ADVANCED:
 * SmartController class has following properties to customize actions it implements:
 * 
 * - $findOptions (array) options passed to Model::find/findInTree() in admin_index(),
 *      admin_showTree(), admin_showTreeLevel(), admin_export(), index(), menu() actions.
 * 
 * - $viewOptions (array) options passed to Html::smartIndex() in admin_index(), 
 *      admin_showTree(), admin_showTreeLevel() actions and to Html::smartForm() 
 *      in admin_add(), admin_edit() actions.
 * 
 * - $rootOptions (array) options to get root in admin_index(), admin_showTree(), 
 *      admin_showTreeLevel(), index(),  menu() actions
 *  
 * - $seoTitle (string) SEO title for all implemented actions. SEO title is used in
 *      admin_ methods to set SmartTab name.   
 * 
 * Each of mentioned properties should be customized directly in overriden action.
 * E.g. to customize limit, columns and seo title in admin_index(), fields and seo 
 * title in admin_add() and save and find methods together with fields and seo title 
 * in admin_edit():
 * 
 *      class MyController extends SmartController {
 *          protected $model = 'MyModel';
 * 
 *          /**
 *           * Allow the model methods hinting in IDE
 *           * @var MyModel
 *           * /
 *          protected $Model;
 * 
 *          public function admin_index($root = null) {
 *              // set custom find options
 *              $this->findOptions['limit'] = false;
 *              // set custom view options (after find options are set)
 *              $this->viewOptions['columns'] = array(
 *                  'name' => __a(__FILE__, 'Name'),
 *                  'active' => __a(__FILE__, 'Active'),
 *                  ...
 *              );
 *              ...
 *              $this->viewOptions['title'] = __a(__FILE__, 'Somethings');
 *              $this->seoTitle = __a(__FILE__, 'Somethings');
 *              return parent::admin_index($root);
 *          }
 * 
 *          public function admin_add($parentId = null) {
 *              // set custom view options
 *              $this->viewOptions['fields'] = array(
 *                  array(
 *                      'field' => 'name',
 *                      'label' => __a(__FILE__, 'Name'),
 *                  ),
 *              );
 *              ...
 *              $this->viewOptions['title'] = __a(__FILE__, 'New something');
 *              $this->seoTitle = __a(__FILE__, 'New something');
 *              return parent::admin_add($parentId);
 *          }
 * 
 *          public function admin_edit($id = null) {
 *              // use custom save method in following way
 *              if ($this->data) {
 *                  $this->saveResult = $this->Model->saveAll($this->data, array(
 *                      'lang' => $this->lang,
 *                  ));
 *                  // possible redirection on success can be done here. If not
 *                  // then it is done by parent::admin_edit()
 *              }
 *              // use custom find method in following way
 *              else {
 *                  $this->viewOptions['data'] = $this->Model->findAll($id, array(
 *                      'lang' => $this->lang,
 *                  ));
 *                  if (empty($this->viewOptions['data'])) {
 *                      App::setErrorMessage(__a(__FILE__, 'Invalid record id %s', (string)$id));
 *                      return App::loadScreen('_error');
 *                  }            
 *              }
 *              // set custom view options
 *              $this->viewOptions['fields'] = array(
 *                  array('field' => 'id', 'type' => 'hidden'),
 *                  array('h1' => __a(__FILE__, 'Basic parameters')),
 *                  array(
 *                      'field' => 'name',
 *                      'label' => __a(__FILE__, 'Name'),
 *                  ),
 *                  array(
 *                      'field' => 'description',
 *                      'label' => __a(__FILE__, 'Description'),
 *                      'type' => 'editor',
 *                  ),
 *                  ...
 *              );
 *              ...
 *              $this->viewOptions['title'] = __a(__FILE__, 'Edit something "%s"');
 *              return parent::admin_edit($id);
 *          }
 * 
 *          //
 *          // SmartController::find()/findFirst()/findList() use examples
 *          //
 *
 *          /**
 *           * Just changing the action visibility from PROTECTED to PUBLIC.
 *           * From production (live project) point of view this is THE WORST SOLUTION.
 *           * From development point of view this is acceptable for fast prototyping.
 *           * but must be refactored before deploing to production.
 *           * /
 *          public function find($options = null) {
 *              // and EVEN WORSE giving full access to fields, joins and literals:
 *              //$this->findOptions['allowFields'] = array();
 *              //$this->findOptions['allowJoins'] = true;
 *              //$this->findOptions['allowLiterals'] = true;
 *              return parent::find($options); 
 *          }
 * 
 *          /**
 *           * You can sanitize find options passed by AJAX request by 'allowJoins', 
 *           * 'allowLiterals' and 'allowFields' set in $this->findOptions
 *           * Be aware of this rewritten version if calling $this->find() in some 
 *           * other action.
 *           * /
 *          public function find($options = null) {
 *              // consider use both unqualified and qualified names to be sure
 *              // that both cases are covered
 *              $this->findOptions['allowFields'] = array(
 *                  'id',
 *                  'name',
 *                  'number',
 *                  'User.id',
 *                  'User.name',
 *                  'EshopOrder.number',
 *              );
 *              $this->findOptions['allowJoins'] = array(
 *                  array(
 *                      array(
 *                          'type' => 'left',
 *                          'model' => 'EshopOrder',
 *                      ),
 *                      array(
 *                          'type' => 'left',
 *                          'model' => 'User',
 *                          'module' => 'App',
 *                      ),
 *                  )
 *              );
 *              return parent::find($options); 
 *          }
 * 
 *          /**
 *           * You can sanitize find options passed by AJAX request manually
 *           * Be aware of this rewritten version if calling $this->find() in some 
 *           * other action.
 *           * /
 *          public function find($options = null) {
 *              // $this->findOptions can be set here...
 *              $options = $this->getRequestFindOptions($options);
 *              // some manual sanitization of $options here...
 *              return parent::find($options);
 *          }
 * 
 *          /**
 *           * THE BEST AND TOP SECURE SOLUTION: You can write dedicated action 
 *           * and accept only some of comming options passed by AJAX request
 *           * /
 *          public function findIndexProducts() {
 *              // $this->findOptions can be set here...
 *              $options = $this->getRequestFindOptions();
 *              // set explicit values fo following options
 *              $options['fields'] = array(...);
 *              $options['joins'] = null;
 *              $options['literals'] = null;
 *              return parent::find($options);
 *          }
 * 
 *          /**
 *           * You can use request options in some other find actions.
 *           * There can be passed also any other options (not only ones used by 
 *           * Model::find())
 *           * /
 *          public function findIndexProducts() {
 *              // $this->findOptions can be set here...
 *              $options = $this->getRequestFindOptions();
 *              $this->viewOptions['data'] = $this->Model->filter($options);
 *              return parent::find();
 *          }
 *      }
 * 
 * If the default implementation of some SmartController action does not suit your
 * needs then just override it and write your onw implementation from scratch.
 * 
 * SmartController uses some internal properties to implement default functionality.
 * You could need/use them too when ovverriding actions. Check them in SmartController
 * class definition in section 'RUNTIME READONLY PROPERTIES'
 */
abstract class SmartController extends Controller {
    
    //
    // CONFIG PROPERTIES
    //
    
    /**
     * Model name used by this controller.
     * 
     * ATTENTION: If not provided then it is guessed from controller name. If the resolution fails 
     * then it must be set explicitly manually. If not provided (resolved or explicit) then 
     * no of SmartController functionality is available and call of predefined actions will 
     * end by exception - see SmartController::allowSmartAction()
     *
     * @var string
     */
    protected $model = '';
    
    /**
     * Model is (MAYBE) translated by records when
     *      - it has defined no Model::$translatedFields
     *      - it has 'lang' field in Model::$schema
     * 
     * E.g. WebContent model is translated by records.
     * On other side there are models which satisfy the above conditions but they
     * are not records translated (Language, EshopOrders)
     * 
     * If NULL the this property is guessed in __construct(). But it is still possible
     * set it to explicit value.
     *
     * @var bool
     */
    protected $modelIsTranslatedByRecords = null;
    
    
    //
    // RUNTIME CONFIG PROPERTIES
    // 
    // They are set in each method to proper values
    // They are reset on each call of SmartController::loadAction(), which is internally
    // used by App::loadControllerAction().
    //
    
    /**
     * Find options for admin_index(), admin_showTree(), admin_showTreeLevel(),
     * admin_export(), index(), menu() and find() actions. See Model::find() options. Options 
     * which are not specified explicitly are set implicitly according to model properties.
     *
     * @var array
     */
    protected $findOptions = array();
    
    /**
     * Html::smartIndex() options for admin_index(), admin_showTree(), admin_showTreeLevel()
     * actions and Html::smartForm() options for admin_add(), admin_edit() actions. Options 
     * which are not specified explicitly are set implicitly according to model properties.
     *
     * @var array
     */
    protected $viewOptions = array();
    
    /**
     * Find options to get tree root(s) in admin_index(), admin_showTree(), admin_showTreeLevel() 
     * actions. If empty then set implicitly according to model properties.
     *
     * @var array 
     */
    protected $rootOptions = array();
                
    /**
     * Seo title for any of methods returning html.
     *
     * @var string
     */
    protected $seoTitle = null;
    
    /**
     * View to be used in admin_index(), admin_showTree(), admin_showTreeLevel(), 
     * admin_add(), admin_edit() action. Defaults to NULL, it means no view is used
     * and Html::smartIndex()/smartForm() is called directly. Must be specified in 
     * case of admin_view(). SmartController::$viewOptions are passed to view as params. 
     *
     * @var string 
     */
    protected $view = null;
    
    /**
     * Save options passed to Model::save/addOrderedItem/addTreeNode() in admin_add() 
     * and to Model::save() in admin_edit(). Defaults to array('alternative' => 'backend')
     * for admin_add() and array('alternative' => 'backend', 'lang' => $this->lang)
     * for admin_edit()
     * 
     * @var array
     */
    protected $saveOptions = array();
    
    /**
     * Keeps result of used save method in admin_add() and admin_edit() actions. 
     * 
     * This can be used in case of custom save method to announce the default functionality
     * in parent::admin_add/edit()admin_edit() actions, that data has been already
     * saved ($this->saveResult is not NULL). If you do not do redirecions in your 
     * overriden admin_add/edit() actions then these are still done according the 
     * value of $this->saveResult in parent::admin_add/edit()admin_edit() actions.
     *
     * @var NULL|bool 
     */
    protected $saveResult = null;
                
    /**
     * Name of action to redirect to after successful processing in admin_add(), 
     * admin_delete() and admin_deleteFile()
     * 
     * @var string
     */
    protected $redirectionAction = null;
                
    //
    // RUNTIME READONLY PROPERTIES
    //
    
    /**
     * Instance of model specified by SmartController::$model
     *
     * @var Model
     */
    protected $Model = null;
    
    /**
     * Value returned by SmartController::$Model->getPropertySchema()
     * 
     * @var array
     */
    protected $modelSchema = array();
    
    /**
     * Value returned by SmartController::$Model->getPropertyNameField()
     * 
     * @var string|NULL
     */
    protected $modelNameField = null;
    
    /**
     * Value returned by SmartController::$Model->getPropertyPrimaryKey()
     * 
     * @var string
     */
    protected $modelPrimaryKeyField = null;
    
    
    /**
     * Value returned by SmartController::$Model->getPropertyDeletedField()
     * 
     * @var string
     */
    protected $modelDeletedField = null;
    
    /**
     * Value returned by SmartController::$Model->getPropertyTranslatedFields()
     * 
     * @var array
     */
    protected $modelTranslatedFields = array();
    
    /**
     * Value returned by SmartController::$Model->getPropertyFileFields()
     * 
     * @var array
     */
    protected $modelFileFields = array();
    
    
    protected $modelIsTranslatedByFields = false;
    
    protected $modelIsTree = false;
    
    protected $modelIsOrderedList = false;
    
    protected $modelIsGallery = false;
    
    protected $modelAllowsSoftDelete = false;
    
    /**
     * Actual translation lang
     * 
     * @var string 
     */
    protected $lang = null;
    
    //
    // METHODS
    //
    
    public function __construct() {
        parent::__construct();
        
        // if not provided then try to guess model name 
        if (
            empty($this->model)
            && ($model = App::getModelNameFromControllerName($this->name))
            && App::hasModel($this->module, $model)        
        ) {
            $this->model = $model;
        }
        if (empty($this->model)) {
            $message = __e(__FILE__, '%s::$model has not been resolved. Please set it manually for this SmartController child class', $this->name);
            App::debug($message, $message);
        }
        // if model name provide then set related properties
        if (!empty($this->model)) {
            $this->Model = $this->loadModel($this->model, true);
            $this->modelSchema = $this->Model->getPropertySchema();
            $this->modelPrimaryKeyField = $this->Model->getPropertyPrimaryKey();
            $this->modelNameField = $this->Model->getPropertyNameField();
            $this->modelDeletedField = $this->Model->getPropertyDeletedField();
            $this->modelIsTree = $this->Model->getTreeType() !== false;
            $this->modelIsOrderedList = !$this->modelIsTree && isset($this->modelSchema['sort']);
            $this->modelIsGallery = method_exists($this->Model, 'getGalleryImageField');
            $this->modelFileFields = $this->Model->getPropertyFileFields();
            $this->modelTranslatedFields = $this->Model->getTranslatedFields(array('combine' => true));
            $this->modelIsTranslatedByFields = !empty($this->modelTranslatedFields);
            if ($this->modelIsTranslatedByRecords === null) {
                $this->modelIsTranslatedByRecords = !$this->modelIsTranslatedByFields 
                    && !empty($this->modelSchema['lang'])
                    //till now (160123) only trees has been translated by records (WebContent)
                    && $this->modelIsTree;
            }
            $this->modelAllowsSoftDelete = $this->Model->allowsSoftDelete();
            if (App::$actionType === 'admin') {
                $this->lang = $this->getActionLang();
            }
            else {
                // in case of non-admin (frontend, api) actions:
                // - avoid redirect
                // - prefer App::$lang over DEFAULT_LANG
                $this->lang = !empty($_GET['lang']) ? $_GET['lang'] : App::$lang;
            }
        }
    }
    
    /**
     * Loads specified action with provided params and data
     * 
     * @param string $action Controller action method name.
     * @param array $params Controller dispatch params.
     * @param array $data Controller dispatch data supposed to be saved.
     * @param array|mixed $args Controller dispatch args or single arg int|float|string. 
     *      They are used as input arguments for $action method
     * @param bool $allowOriginComments Optional. If TRUE then action origin comments are
     *      allowed. To display origin comment set property $this->displayOriginComment to TRUE in action.
     *      Defaults to TRUE.
     * 
     * @return mixed Returns the output of launched controller->action()
     */
    public function loadAction($action, $params, $data, $args, $allowOriginComments = true) {
        // reset the runtime config properties on each new action load
        $this->findOptions = array();
        $this->viewOptions = array();
        $this->rootOptions = array();
        $this->seoTitle = null;
        $this->view = null;
        $this->saveOptions = array();
        $this->saveResult = null;        
        return parent::loadAction($action, $params, $data, $args, $allowOriginComments);
    }
    
    /**
     * Checks id SmartController::$model is specified. If not then an exception is thrown.
     * This method must be called at the begigning of each SmartController action.
     * 
     * @param string $type Optional. Available values are NULL, 'gallery'. Defaults to NULL.
     * 
     * @throws Exception
     */
    protected function allowSmartAction($type = null) {
        if (empty($this->model)) {
            throw new Exception(__e(__FILE__, 'Action :controller:(extends SmartController):::action:() is not allowed because property :controller:::$model is not defined. Specify this property to use inherited predefined SmartController:::action:() or write your own :controller::::action:()', array('controller' => $this->name, 'action' => $this->action)));
        }
        if (
            $type === 'gallery'
            && !$this->modelIsGallery
        ) {
            throw new Exception(__e(__FILE__, 'Action :controller:(extends SmartController):::action:() is not allowed because :model:, defined in property :controller:::$model, is not a child class of GalleryModel. To use inherited predefined SmartController:::action:() define :model: as GalleryModel child class or write your own :controller::::action:()', array('controller' => $this->name, 'action' => $this->action, 'model' => $this->model)));
        }
    }

    /**
     * Returns smart index of model records
     * 
     * @param int|string $root Optional. Id or pid of root (parent node) to index childs for. 
     *      If NULL then $_GET['root'] is checked and if no $_GET['root'] then 
     *      first top level root is shown. Considered only if $this->modelIsTree.
     * 
     * @return string
     */
    public function admin_index($root = null) {
        $this->allowSmartAction();
        $this->displayOriginComment = true;
        
        // admin_index can be used also to display index of tree nodes
        $roots = null;
        $rootId = null;
        if ($this->modelIsTree) {
            if (($root = $this->getRoot($root, $roots))) {
                $this->setImplicitFindOptions();
                $this->viewOptions['records'] = $this->Model->findInTree(
                    $root,
                    $this->findOptions,
                    // get root id to check if valid root has been specified
                    // it is also used for admin_add action
                    $rootId
                );
                if (empty($rootId)) {
                    App::setErrorMessage(__a(__FILE__, 'Invalid tree root specification'));
                    return App::loadScreen('_error');
                }        
            }
            // this can happen when the tree has no records yet
            else {
                $this->viewOptions['records'] = array();
            }
        }
        elseif (!isset($this->viewOptions['records'])) {            
            $this->setImplicitFindOptions();
            $this->viewOptions['records'] = $this->Model->find($this->findOptions);
        }

        $this->setImplicitSmartIndexOptions($rootId, $roots);

        //
        // set seoTitle (but only if it is not yet set explicitly)
        //
        if (empty($this->seoTitle)) {
            $this->seoTitle = Str::humanize($this->name, '');
        }
        App::setSeoTitle($this->seoTitle);
        
        if (!empty($this->view)) {
            return $this->loadView($this->view, $this->viewOptions);
        }
        return Html::smartIndex($this->viewOptions);
    }
    
    /**
     * Returns smart index of model records tree
     * 
     * @param int|string $root Optional. Id or pid of root (parent node) to display childs for. 
     *      If NULL then $_GET['root'] is checked and if no $_GET['root'] then 
     *      first top level root is shown. 
     * 
     * @return string
     */
    public function admin_showTree($root = null) {
        $this->allowSmartAction();
        $this->displayOriginComment = true;
        
        if (!$this->modelIsTree) {
            App::setErrorMessage(__a(__FILE__, 'Model %s is not tree', $this->model));
            return App::loadScreen('_error');
        }
        
        $roots = null;
        $rootId = null;
        if (($root = $this->getRoot($root, $roots))) {
            $this->setImplicitFindOptions();
            $this->viewOptions['records'] = $this->Model->findInTree(
                $root,
                $this->findOptions,
                // get root id to check if valid root has been specified
                // it is also used for admin_add action
                $rootId
            );
            if (empty($rootId)) {
                App::setErrorMessage(__a(__FILE__, 'Invalid tree root specification'));
                return App::loadScreen('_error');
            }
        }
        // this can happen when the tree has no records yet
        else {
            $this->viewOptions['records'] = array();
        }

        $this->setImplicitSmartIndexOptions($rootId, $roots);
        
        //
        // set seoTitle (but only if it is not yet set explicitly)
        //
        if (empty($this->seoTitle)) {
            $this->seoTitle = Str::humanize($this->name, '');
        }
        App::setSeoTitle($this->seoTitle);
        
        if (!empty($this->view)) {
            return $this->loadView($this->view, $this->viewOptions);
        }
        return Html::smartIndex($this->viewOptions);
    }
    
    /**
     * Exports filtered records
     * 
     * @param int|string $root Optional. Id or pid of root (parent node) to index childs for. 
     *      If NULL then $_GET['root'] is checked and if no $_GET['root'] then 
     *      first top level root is shown. Considered only if $this->modelIsTree.
     * @param string $format Possible values are 'csv', 'xml', 'json', 'xls' and 'xlsx'. 
     *          Defaults to 'xlsx'.
     * 
     */
    public function admin_export($root = null, $format = 'xlsx') {
        $this->allowSmartAction();
        if ($this->modelIsTree) {
            if (($root = $this->getRoot($root))) {
                $this->setImplicitFindOptions();
                $records = $this->Model->findInTree(
                    $root,
                    $this->findOptions,
                    // get root id to check if valid root has been specified
                    // it is also used for admin_add action
                    $rootId
                );
                if (empty($rootId)) {
                    App::setErrorMessage(__a(__FILE__, 'Invalid tree root specification'));
                    return App::loadScreen('_error');
                }     
            }
            // this can happen when the tree has no records yet
            else {
                $this->viewOptions['records'] = array();
            }
        }
        else {            
            $this->setImplicitFindOptions();
            $records = $this->Model->find($this->findOptions);
        }
        
        $this->Model->export(
            $records,
            array(
                'file' => $this->name,
                'format' => $format,
            )
        );
    }    
    
    /**
     * 
     * @param int $parentId Optional. Considered only in case of tree models
     * 
     * @return string
     */
    public function admin_add($parentId = null) {
        $this->allowSmartAction();
        $this->displayOriginComment = true;
        
        if ($this->data) {
            $this->saveOptions = array_merge(
                array(
                    'alternative' => 'backend',
                ), 
                (array)$this->saveOptions
            );
            $this->saveOptions['create'] = true;
            if (
                // either has been saved by method overriding this method...
                !empty($this->saveResult)
                ||
                // ...or save it here 
                $this->saveResult === null
                && (
                    !$this->modelIsTree 
                    && !$this->modelIsOrderedList
                    && ($this->saveResult = $this->Model->save($this->data, $this->saveOptions))
                    ||
                    $this->modelIsTree
                    && ($this->saveResult = $this->Model->addTreeNode($parentId, $this->data, $this->saveOptions))
                    ||
                    $this->modelIsOrderedList
                    && ($this->saveResult = $this->Model->addOrderedItem($this->data, $this->saveOptions))
                )
            ) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully created'));
                if (empty($_GET['_closeTab_'])) {
                    if (empty($this->redirectionAction)) {
                        $this->redirectionAction = 'admin_edit';
                    }
                    App::redirect(App::getUrl(array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => $this->redirectionAction,
                        'args' => array($this->Model->getPropertyId()),
                        'source' => App::$requestSource,
                    )));
                }
            }
            else {
                App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            }
        }
        
        //
        // set viewOptions (but only those which are not yet set explicitly)
        //
        $langInfo = $this->setImplicitSmartFormOptions($parentId);
        
        //
        // set seoTitle (but only if it is not yet set explicitly)
        //
        if (empty($this->seoTitle)) {
            $this->seoTitle = __a(__FILE__, 'New %s', Str::humanize($this->model, ''));
        }
        $this->seoTitle .= $langInfo;
        App::setSeoTitle($this->seoTitle);
        
        if (!empty($this->view)) {
            return $this->loadView($this->view, $this->viewOptions);
        }
        return Html::smartForm($this->viewOptions);
    }   
    
    /**
     * 
     * @param integer $id Optional. Single product id. Required only in case that
     *      you would like to use default SmartController::$Model::find() method.
     *      If SmartController::$viewOptions['data'] are set by child method then
     *      it is not considered and required.
     * 
     * @return string
     */
    public function admin_edit($id = null) {
        $this->allowSmartAction();
        $this->displayOriginComment = true;
        
        if (
            // if we are not already saving
            empty($this->data)
            // and thare are no data loaded in custom way
            && empty($this->viewOptions['data'])
            // and there is no id to load data in defauilt way
            && empty($id) 
        ) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            return App::loadScreen('_error');
        }
        
        if ($this->data) {
            $this->saveOptions = array_merge(
                array(
                    'alternative' => 'backend',
                    // this is considered only if $this->modelIsTranslatedByFields
                    'lang' => $this->lang, 
                ), 
                (array)$this->saveOptions
            );
            $this->saveOptions['create'] = false;
            if (
                // either has been saved by method overriding this method...
                !empty($this->saveResult)
                ||
                // ...or save it here 
                $this->saveResult === null
                && ($this->saveResult = $this->Model->save($this->data, $this->saveOptions))
            ) {
                App::setSuccessMessage(__a(__FILE__, 'The record has been succesfully updated'));
                // warnings can be set even in case of successful save
                if (($processingWarnings = $this->Model->getProcessingWarnings())) {
                    foreach ($processingWarnings as $processingWarning) {
                        App::setWarningMessage($processingWarning);
                    }
                }
                App::redirect(App::$url);
            }
            App::setErrorMessage(__a(__FILE__, 'Please, correct errors'));
            if (($processingErrors = $this->Model->getProcessingErrors())) {
                foreach ($processingErrors as $processingError) {
                    App::setErrorMessage($processingError);
                }
            }
            if (($processingWarnings = $this->Model->getProcessingWarnings())) {
                foreach ($processingWarnings as $processingWarning) {
                    App::setWarningMessage($processingWarning);
                }
            }
        }
        elseif (empty($this->viewOptions['data'])) {
            $this->data = $this->Model->findFirstBy($this->modelPrimaryKeyField, $id, array(
                // this is considered only if $this->modelIsTranslatedByFields
                'lang' => $this->lang
            ));
            // if product does not exist then redirect to referer
            if (empty($this->data)) {
                App::setErrorMessage(__a(__FILE__, 'Invalid record id %s', (string)$id));
                return App::loadScreen('_error');
            }
            // get file fields real path
            $fileFields = array_keys($this->modelFileFields);
            foreach($fileFields as $fileField) {
                if (!empty($this->data[$fileField])) {
                    $this->data[$fileField] = $this->Model->getFileFieldUrlPath($fileField, array('file' => $this->data[$fileField]));
                }
            }
            $this->viewOptions['data'] = $this->data;
        }
        elseif (!empty($this->viewOptions['data'])) {
            $this->data = $this->viewOptions['data'];
        }
        
        //
        // set viewOptions (but only those which are not yet set explicitly)
        //
        $langInfo = $this->setImplicitSmartFormOptions();
        
        //
        // set seoTitle (but only if it is not yet set explicitly)
        //
        $name = $this->getRecordName();
        if (empty($this->seoTitle)) {
            if (!empty($name)) {
                $this->seoTitle = __a(__FILE__, '"%s"', $name);
            }
            elseif (!empty($this->data[$this->modelPrimaryKeyField])) {
                $this->seoTitle = __a(__FILE__, 'Edit %s "%s"', Str::humanize($this->model, ''), $this->data[$this->modelPrimaryKeyField]);
            }
            else {
                $this->seoTitle = __a(__FILE__, 'Edit %s', Str::humanize($this->model, ''));
            }
        }
        else {
            $this->seoTitle = sprintf($this->seoTitle, $name);
        }
        $this->seoTitle .= $langInfo;
        App::setSeoTitle($this->seoTitle);
        
        if (!empty($this->view)) {
            return $this->loadView($this->view, $this->viewOptions);
        }
        return Html::smartForm($this->viewOptions);
    }     
    
    /**
     * 
     * @param integer $id Optional. Single product id. Required only in case that
     *      you would like to use default SmartController::$Model::find() method.
     *      If SmartController::$viewOptions['data'] are set by child method then
     *      it is not considered and required.
     * 
     * @return string
     */
    public function admin_view($id = null) {
        $this->allowSmartAction();
        $this->displayOriginComment = true;
        
        if (
            empty($id) 
            && empty($this->viewOptions['data'])
        ) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            return App::loadScreen('_error');
        }
        
        if (empty($this->viewOptions['data'])) {
            $this->data = $this->Model->findFirstBy($this->modelPrimaryKeyField, $id, array(
                // this is considered only if $this->modelIsTranslatedByFields
                'lang' => $this->lang
            ));
            // if product does not exist then redirect to referer
            if (empty($this->data)) {
                App::setErrorMessage(__a(__FILE__, 'Invalid record id %s', (string)$id));
                return App::loadScreen('_error');
            }
            // get file fields real path
            $fileFields = array_keys($this->modelFileFields);
            foreach($fileFields as $fileField) {
                if (!empty($this->data[$fileField])) {
                    $this->data[$fileField] = $this->Model->getFileFieldUrlPath($fileField, array('file' => $this->data[$fileField]));
                }
            }
            $this->viewOptions['data'] = $this->data;
        }
        elseif (!empty($this->viewOptions['data'])) {
            $this->data = $this->viewOptions['data'];
        }
        
        $lang = $this->getRecordLang();
        $langInfo = $this->getRecordLangInfo($lang);
        
        //
        // set seoTitle (but only if it is not yet set explicitly)
        //
        $name = $this->getRecordName();
        if (empty($this->seoTitle)) {
            if (!empty($name)) {
                $this->seoTitle = __a(__FILE__, '"%s"', $name);
            }
            elseif (!empty($this->data[$this->modelPrimaryKeyField])) {
                $this->seoTitle = __a(__FILE__, 'Edit %s "%s"', Str::humanize($this->model, ''), $this->data[$this->modelPrimaryKeyField]);
            }
            else {
                $this->seoTitle = __a(__FILE__, 'Edit %s', Str::humanize($this->model, ''));
            }
        }
        else {
            $this->seoTitle = sprintf($this->seoTitle, $name);
        }
        $this->seoTitle .= $langInfo;
        App::setSeoTitle($this->seoTitle);
        App::setTabIcon('<i class="fa fa-eye"></i>');
        
        if (empty($this->view)) {
            throw new Exception(__e(__FILE__, 'Please specify property :controller:::$view', array('controller' => $this->name)));
        }
        return $this->loadView($this->view, $this->viewOptions);
    }     
    
    /**
     * Deletes specified single record or many records
     * 
     * @param int $id Single product id
     * @param string $_GET['ids'] Comma separated list of product ids. If provided 
     *      then $id param is not considered.
     */
    public function admin_delete($id = null) {
        $this->allowSmartAction();
        if (empty($id) && !$this->hasBulkActionIds()) {
            App::setErrorMessage(__a(__FILE__, 'Missing record id'));
            return App::loadScreen('_error');
        }
        // check for bulk delete
        if (($ids = $this->getBulkActionIds()) === false) {
            $bulkAction = false;
            $ids = (array)$id;
        }
        else {
            $bulkAction = true;
        }
        if ($this->modelIsOrderedList) {
            foreach ($ids as $id) {
                $this->data = $this->Model->deleteOrderedItem($id);
            }
        }
        elseif ($this->modelIsTree) {
            foreach ($ids as $id) {
                $this->Model->deleteTreeNode($id);
            }
        }
        else {
            $this->Model->deleteBy($this->modelPrimaryKeyField, $ids);
        }
        if ($bulkAction) {
            App::setSuccessMessage(__a(__FILE__, 'Records has been succesfully deleted'));
        }
        else {
            App::setSuccessMessage(__a(__FILE__, 'Record has been succesfully deleted'));
        }
        if (empty($_GET['_closeTab_'])) {
            if (empty($this->redirectionAction)) {
                $this->redirectionAction = 'admin_index';
                if ($this->modelIsTree) {
                    $this->redirectionAction = 'admin_showTree';
                }
            }
            App::redirect(App::getRefererUrl(App::getUrl(array(
                'module' => $this->module,
                'controller' => $this->name,
                'action' => $this->redirectionAction,
                'source' => App::$requestSource,
            ))));
        }
        else {
            exit();
        }
    }
    
    /**
     * Deletes file asociated with specified fileField on specified record
     * 
     * @param string $fileField
     * @param int $id
     */
    public function admin_deleteFile($fileField = null, $id = null) {
        $this->allowSmartAction();
        if (!$fileField || !$id) {
            App::setErrorMessage(__a(__FILE__, 'Missing record fileField and/or id'));
            return App::loadScreen('_error');
        }
        $fileFields = $this->Model->getPropertyFileFields();
        if (!isset($fileFields[$fileField])) {
            App::setErrorMessage(__a(__FILE__, 'Invalid file field'));
            return App::loadScreen('_error');
        }
        $this->Model->save(
            array($this->modelPrimaryKeyField => $id, $fileField => ''),
            array('validate' => false)
        );
        App::setSuccessMessage(__a(__FILE__, 'File has been succesfully deleted'));
        if (empty($this->redirectionAction)) {
            $this->redirectionAction = 'admin_edit';
        }
        App::redirect(App::getRefererUrl(App::getUrl(array(
            'module' => $this->module,
            'controller' => $this->name,
            'action' => $this->redirectionAction,
            'source' => App::$requestSource,
        ))));
    }
    
    /**
     * Moves specified record to specified $orderIndex in ordered list
     * 
     * @param int $id
     * @param int $orderIndex
     * 
     * @return string Json encoded response
     * 
     * @todo add second signature ($parentId = null, $id = null, $orderIndex = null) to allow move also in trees. 
     * See WebContents::move(), ::show_TreeLevel()
     */
    public function admin_move($id = null, $orderIndex = null) { 
        $this->allowSmartAction();
        if (
            !$this->modelIsOrderedList
//            && !$this->modelIsTree
        ) {
            App::setErrorMessage(__a(__FILE__, 'Model %s is not ordered list', $this->model));
//            App::setErrorMessage(__a(__FILE__, 'Model %s is neither ordered list nor a tree', $this->model));
            return App::loadScreen('_error');
        }
        
        App::setLayout('App', 'json');
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        if (
            $this->modelIsOrderedList
            && !$this->Model->moveOrderedItem($id, array('newOrderIndex' => $orderIndex))
//            ||
//            $this->modelIsTree
//            && !$this->Model->moveTreeNode($id, $parentId, array('newOrderIndex' => $orderIndex))
        ) {
            $Response->success = false;
            App::setErrorMessage(__a(__FILE__, 'Item move has failed'), true);
        }
        return $Response->getJson();
    }
    
    /**
     * Returns Json encoded list which can be used with both FormHelper::itemselector() 
     * and ::treeselector(), see $keywordOrParentId phpDoc.
     * 
     * ATTENTION: If $this->modelIsTree then it is up to developer who overrides 
     * this method to reasonably resolve the default value for empty $keywordOrParentId (parent id).
     * 
     * ATTENTION: $this->findOptions['fields'] are set explicitly by this method.
     * 
     * @param string|int $keywordOrParentId Meaning of this arg changes. If $this->modelIsTree
     *      then it is considered to be a parenId (int). Otherwise it is considered to be
     *      keyword (string) to filter records according to $this->modelNameField.
     * @param array|string $ids Optional. Ids of records provided either as string 
     *      separated by ";" or as array. If specified then $keywordOrParentId is ignored
     *      and method returns labels of this ids.
     * @param bool $ignoreSoftDeletedIds Optional. If TRUE then soft deleted $ids are
     *      ignored. If FALSE then even soft deleted $ids are retrieved. Defaults to FALSE.
     * 
     * @return string Json encoded data
     */
    public function admin_getSelectorItems($keywordOrParentId = null, $ids = null, $ignoreSoftDeletedIds = false) {
        $this->allowSmartAction();
        if (
            !isset($this->findOptions['fields'])
            && empty($this->modelNameField)
        ) {
            throw new Exception(__e(__FILE__, 'Undefined nameField in model :model:', array('model' => $this->model)));
        }
        // assure that soft deleted are retrieved only if they are allowed (to simplify the below processing)
        if ($this->Model->allowsSoftDelete() && empty($ignoreSoftDeletedIds)) {
            $ignoreSoftDeletedIds = false;
        }
        else {
            $ignoreSoftDeletedIds = true;
        }
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        App::setLayout('App', 'json');
        
        //
        // set findOptions
        //
        // - implicit conditions
        if (!isset($this->findOptions['conditions'])) {
            if (
                // in case that we get connected items then we display also inactive ones 
                !isset($ids)
                && isset($this->modelSchema['active'])
            ) {
                $this->findOptions['conditions'][$this->Model->qualifyField('active', $this->findOptions)] = true;
            }
        }
        // - nest conditions as here below an explicit part is added
        if (isset($this->findOptions['conditions'])) {
            $this->findOptions['conditions'] = DB::nestConditions($this->findOptions['conditions']);
        }
        // - implicit order
        if (
            !isset($this->findOptions['order'])
            && !empty($this->modelNameField)
        ) {
            $this->findOptions['order'] = $this->Model->qualifyField($this->modelNameField, $this->findOptions) . ' ASC';
        }
        // - implicit fields
        if (!isset($this->findOptions['fields'])) {
            $this->findOptions['fields'] = array(
                $this->Model->qualifyField($this->modelNameField, $this->findOptions)
            );
        }
        // - explicit fields
        if ($this->modelIsTree) {
            $this->findOptions['fields'][] = $this->Model->qualifyField($this->modelPrimaryKeyField, $this->findOptions);
            $this->findOptions['fields'][] = $this->Model->qualifyField('path', $this->findOptions);
        }
////@todo (170418A)        
//        if (empty($ignoreSoftDeletedIds)) {
//            $this->findOptions['fields'][] = $this->Model->qualifyField($this->modelDeletedField, $this->findOptions);
//        }
        // - implicit key but only for non-tree model
        if (
            !isset($this->findOptions['key'])
            && !$this->modelIsTree
        ) {
            $this->findOptions['key'] = $this->Model->qualifyField($this->modelPrimaryKeyField, $this->findOptions);
        }
        
        if (isset($ids)) {
            // set explicit part of conditions in findOptions
            if (!is_array($ids)) {
                $ids = explode(';', $ids);
            }
            $this->findOptions['conditions'][$this->Model->qualifyField($this->modelPrimaryKeyField, $this->findOptions)] = $ids;   
            $this->findOptions['ignoreSoftDeleted'] = $ignoreSoftDeletedIds;
        } 
        else {
            if ($this->modelIsTree) {
                // get default parent id (!!!this is just fallback and it should be 
                // reasonably resolved by developer who overrides this method)
                if (empty($keywordOrParentId)) {
                    $options = array();
                    if (isset($this->modelSchema['active'])) {
                        $options['conditions']['active'] = true;
                    }
                    if ($this->modelIsTranslatedByRecords) {
                        $options['conditions']['lang'] = $this->lang;
                    }
                    $options['order'] = 'sort ASC';
                    $keywordOrParentId = $this->Model->findField($this->modelPrimaryKeyField, $options);
                }
                // set explicit conditions in findOptions
                $this->findOptions['conditions'][$this->Model->qualifyField('parent_id', $this->findOptions)] = $keywordOrParentId;
                // get parent ancestor id and parent name prefixied by its ancestor names
                $parent = $this->Model->findFirstBy($this->modelPrimaryKeyField, $keywordOrParentId, array(
                    'fields' => array($this->modelNameField, 'path', 'parent_id')
                ));
                $ancestorIds = Model::getParentIdsFromTreePath($parent['path']);
                array_shift($ancestorIds); // throw out top level root name
                $ancestorNames = $this->Model->findList(array(
                    'fields' => array($this->modelNameField),
                    'order' => 'sort ASC',
                    'conditions' => array(
                        $this->modelPrimaryKeyField => $ancestorIds
                    )
                ));
                $Response->data['parentName'] = implode(' - ', $ancestorNames) . 
                    (!empty($ancestorNames) ? ' - ' : '') . $parent[$this->modelNameField];
                $Response->data['parentId'] = $parent['parent_id'];
                $Response->total = $this->Model->findCount($this->findOptions);
//this seems no logic to be used for tree - @todo check it                
//                if ($Response->total > App::getSetting('App', 'itemselector.maxItemsToShow')) {
//                    return $Response->getJson();
//                }
            }
            else {
                // see the comment in app/js/sources/libs/Itemselector.js > loadListFromAjax()
                if (isset($_REQUEST['keyword'])) {
                    $keywordOrParentId = $_REQUEST['keyword'];
                }
                // set explicit conditions in findOptions
                if (
                    isset($keywordOrParentId)
                    && $keywordOrParentId !== ''
                ) {   
                    $keywords = preg_split('/\s+/', trim($keywordOrParentId));
                    foreach($keywords as $keyword) {                    
                        $this->findOptions['conditions'][] = array(
                            $this->Model->qualifyField($this->modelNameField, $this->findOptions) . ' %~%' => $keyword
                        );
                    }
                }
                $Response->total = $this->Model->findCount($this->findOptions);
                if ($Response->total > App::getSetting('App', 'itemselector.maxItemsToShow')) {
                    return $Response->getJson();
                }
            }
        }
        
        // find items
        if ($this->modelIsTree) {
            $items = $this->Model->find($this->findOptions);
            $list = array();
            foreach ($items as $item) {
                $ancestorIds = Model::getParentIdsFromTreePath($item['path']);
                array_shift($ancestorIds); // throw out top level root name
                $ancestorNames = $this->Model->findList(array(
                    'fields' => array($this->modelNameField),
                    'order' => 'sort ASC',
                    'conditions' => array(
                        $this->modelPrimaryKeyField => $ancestorIds
                    )
                ));
                $list[$item[$this->modelPrimaryKeyField]] = implode(' - ', $ancestorNames) . 
                    (!empty($ancestorNames) ? ' - ' : '') . $item[$this->modelNameField];
            }
            $Response->data['records'] = $list;
        }
        else {
            $Response->data = $this->Model->findList($this->findOptions);
        }
        $Response->success = true;

        return $Response->getJson();
    }
        
    /**
     * Loads gallery items for provided owner id.
     * Used by AJAX
     * 
     * @param int $ownerId
     * 
     * @return string JSON encoded list of images records
     */
    public function admin_getGalleryImages($ownerId = null) {
        $this->allowSmartAction('gallery');
        App::setLayout('App', 'json');
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        $Response->data = array();
        if (!empty($ownerId)) {
            $this->setImplicitFindOptions();
            $imageFile = $this->Model->getGalleryImageField();
            $ownerForeignKeyField = $this->Model->getGalleryOwnerForeignKeyField();
            $images = $this->Model->findBy($ownerForeignKeyField, $ownerId, $this->findOptions);
            foreach ($images as &$image) {
                $image[$imageFile] = $this->Model->getFileFieldUrlPath($imageFile, array('file' => $image[$imageFile]));
            }
            $Response->data = $images;
        }
        return $Response->getJson();
    }
    
    /**
     * Add new gallery item for provided owner id.
     * Used by AJAX
     * 
     * @param int $ownerId
     * 
     * @return string JSON encoded array of new image record
     */
    public function admin_addGalleryImage($ownerId = null) {
        $this->allowSmartAction('gallery');
        App::setLayout('App', 'json');
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        $Response->data = array();
        if (!empty($ownerId) && !empty($this->data)) {
            $this->saveOptions = (array)$this->saveOptions;
            $this->saveOptions['create'] = true;
            $ownerForeignKeyField = $this->Model->getGalleryOwnerForeignKeyField();
            $this->saveOptions['groupConditions'] = array($ownerForeignKeyField => $ownerId);
            $this->data[$ownerForeignKeyField] = $ownerId;
            if (
                // either has been saved by method overriding this method...
                !empty($this->saveResult)
                ||
                // ...or save it here 
                $this->saveResult === null
                && ($this->saveResult = $this->Model->addOrderedItem($this->data, $this->saveOptions))
            ) {
                $this->saveResult = $this->Model->findFirstBy($this->modelPrimaryKeyField, $this->Model->getPropertyId());
                $imageFile = $this->Model->getGalleryImageField();
                if (!empty($this->saveResult[$imageFile])) {
                    $this->saveResult[$imageFile] = $this->Model->getFileFieldUrlPath($imageFile, array('file' => $this->saveResult[$imageFile]));
                }
                $Response->data = $this->saveResult;
            }
            else {
                $Response->success = false;
                $Response->errors = $this->Model->getErrors();
            }
        }
        return $Response->getJson();
    }
    
    /**
     * Updates gallery item for provided item id (id is present in data).
     * Used by AJAX
     * 
     * @return string JSON encoded array of updated image record
     */
    public function admin_updateGalleryImage() {
        $this->allowSmartAction('gallery');
        App::setLayout('App', 'json');
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        if ($this->data) {
            $this->saveOptions = (array)$this->saveOptions;
            $this->saveOptions['create'] = false;
            if (
                // either has been saved by method overriding this method...
                !empty($this->saveResult)
                ||
                // ...or save it here 
                $this->saveResult === null
                && ($this->saveResult = $this->Model->save($this->data, $this->saveOptions))
            ) {
                $this->saveResult = $this->Model->findFirstBy($this->modelPrimaryKeyField, $this->Model->getPropertyId());
                $imageFile = $this->Model->getGalleryImageField();
                if (!empty($this->saveResult[$imageFile])) {
                    $this->saveResult[$imageFile] = $this->Model->getFileFieldUrlPath($imageFile, array('file' => $this->saveResult[$imageFile]));
                }
                $Response->data = $this->saveResult;
            }
            else {
                $Response->success = false;
                $Response->errors = $this->Model->getErrors();
            }
        }
        return $Response->getJson();
    }
    
    /**
     * Moves specified gallery item
     * Used by AJAX
     * 
     * @param int $ownerId
     * @param int $id
     * @param int $orderIndex
     * 
     * @return string JSON success response
     */
    public function admin_moveGalleryImage($ownerId = null, $id = null, $orderIndex = null) {
        $this->allowSmartAction('gallery');
        App::setLayout('App', 'json');
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        if (!empty($ownerId) && !empty($id)) {
            $ownerForeignKeyField = $this->Model->getGalleryOwnerForeignKeyField();
            if (!$this->Model->moveOrderedItem($id, array(
                'groupConditions' => array($ownerForeignKeyField => $ownerId), 
                'newOrderIndex' => $orderIndex
            ))) {
                $Response->success = false;
                $Response->message = __a(__FILE__, 'Node move has failed');
            }
        }
        return $Response->getJson();
    }          
    
    /**
     * Delete specified gallery item
     * Used by AJAX
     * 
     * @param int $ownerId
     * @param int $id
     * 
     * @return string JSON success response
     */
    public function admin_deleteGalleryImage($ownerId = null, $id = null) {
        $this->allowSmartAction('gallery');
        App::setLayout('App', 'json');
        App::loadLib('App', 'AjaxResponse');
        $Response = new AjaxResponse();
        if (!empty($ownerId) && !empty($id)) {
            $ownerForeignKeyField = $this->Model->getGalleryOwnerForeignKeyField();
            if (!$this->Model->deleteOrderedItem($id, array(
                'groupConditions' => array($ownerForeignKeyField => $ownerId), 
            ))) {
                $Response->success = false;
                $Response->message = __a(__FILE__, 'Node delete has failed');
            }
        }
        return $Response->getJson();
    }      
    
    //
    // JSON API METHODS
    // 
    
    /**
     * Gives access to Model::find() for external requests (JSON API). 
     * Used by AJAX
     * 
     * ATTENTION: This action is intentionally defined as PROTECTED to force its
     * overwriting in child classes for sake of security. If this action would be defined as
     * public then it is enough to give public rights for its inherited copy in child classes 
     * and anybody can launch (e.g. from JS console) queries in table of $this->Model 
     * (see also following NOTE). When overwriting this action in child classes, set its 
     * visibility to PUBLIC and options 'allowFields', 'allowJoins' and 'allowLiterals' 
     * in $this->findOptions set to safe values. The TOP SECURE SOLUTION is to create 
     * a dedicated action (e.g. ::findIndexProducts()) which does not accept any options 
     * from request (or only very small set of them, e.g. 'limit', 'page', ...) and most of crucial 
     * options ('fields', 'joins', 'literals', ...) are defined inside of this dedicated method.
     * 
     * NOTE: In case of this action default values of options 'allowFields', 'allowJoins' 
     * and 'allowLiterals' in $this->findOptions are set to:
     *      - FALSE for 'allowJoins', 'allowLiterals'. It means no joins and no
     *          literals are allowed in Model::find() options by default.
     *      - array('{primaryKeyField}', '{qualifiePrimaryKeyField}') for 'allowFields'. 
     *          It means only primary key field (id) is allowed in Model::find() options by default.
     * These 3 options are ignored in options received in request. Values of all other
     * options (whose defaults can be defined in $this->findOptions) can be overwritten by
     * values received in request.
     * 
     * NOTE: For more information see SmartController class phpDoc and corresponding
     * chapter on http://fajnbook.run.sk
     * 
     * @param array $options Optional. Explicit Model::find() options. If provided
     *      then options comming in request are ignored(!). If NULL then request options
     *      are get by method $this->getRequestFindOptions(). This argument is designed
     *      for calls of parent::find($options) in child classes. Defaults to NULL.
     * 
     * @return string JSON response including keys:
     *      - 'data' array of record(s)
     *      - 'total' number of returned record(s)
     */
    protected function find($options = null) {
        $this->allowSmartAction();
        App::setLayout('App', 'json');
        App::loadLib('App', 'AjaxResponse');
        $options = $this->getRequestFindOptions($options);
        if (empty($this->viewOptions['data'])) {
            $this->viewOptions['data'] = $this->Model->find($options);
        }
        if (!empty($options['first'])) {
            $this->viewOptions['total'] = 1;
        }
        else {
            $this->viewOptions['total'] = count($this->viewOptions['data']);
        }
        if (!empty($this->view)) {
            return $this->loadView($this->view, $this->viewOptions);
        }
        $Response = new AjaxResponse($this->viewOptions);
        return $Response->getJson();
    }
    
    /**
     * Gives access to Model::findFirst() for external requests (JSON API). 
     * Used by AJAX
     * 
     * ATTENTION: This action is intentionally defined as PROTECTED to force its
     * overwriting in child classes for sake of security. If this action would be defined as
     * public then it is enough to give public rights for its inherited copy in child classes 
     * and anybody can launch (e.g. from JS console) queries in table of $this->Model 
     * (see also following NOTE). When overwriting this action in child classes, set its 
     * visibility to PUBLIC and options 'allowFields', 'allowJoins' and 'allowLiterals' 
     * in $this->findOptions set to safe values. The TOP SECURE SOLUTION is to create 
     * a dedicated action (e.g. ::findViewProduct()) which does not accept any options 
     * from request (or only very small set of them, e.g. 'limit', 'page', ...) and most of crucial 
     * options ('fields', 'joins', 'literals', ...) are defined inside of this dedicated method.
     * 
     * NOTE: In case of this action default values of options 'allowFields', 'allowJoins' 
     * and 'allowLiterals' in $this->findOptions are set to:
     *      - FALSE for 'allowJoins', 'allowLiterals'. It means no joins and no
     *          literals are allowed in Model::findFirst() options by default.
     *      - array('{primaryKeyField}', '{qualifiePrimaryKeyField}') for 'allowFields'. 
     *          It means only primary key field (id) is allowed in Model::findFirst() options by default.
     * These 3 options are ignored in options received in request. Values of all other
     * options (whose defaults can be defined in $this->findOptions) can be overwritten by
     * values received in request.
     * 
     * NOTE: For more information see SmartController class phpDoc and corresponding
     * chapter on http://fajnbook.run.sk
     * 
     * @param array $options Optional. Explicit Model::findFirst() options. If provided
     *      then options comming in request are ignored(!). If NULL then request options
     *      are get by method $this->getRequestFindOptions(). This argument is designed
     *      for calls of parent::find($options) in child classes. Defaults to NULL.
     * 
     * @return string JSON response including keys:
     *      - 'data' array of record(s)
     *      - 'total' number of returned record(s) (always 1)
     */
    protected function findFirst($options = null) {
        $this->allowSmartAction();
        App::setLayout('App', 'json');
        App::loadLib('App', 'AjaxResponse');
        $options = $this->getRequestFindOptions($options);
        $options['first'] = true;
        if (empty($this->viewOptions['data'])) {
            $this->viewOptions['data'] = $this->Model->find($options);
        }
        $this->viewOptions['total'] = 1;
        if (!empty($this->view)) {
            return $this->loadView($this->view, $this->viewOptions);
        }
        $Response = new AjaxResponse($this->viewOptions);
        return $Response->getJson();
    }
    
    /**
     * Gives access to Model::findList() for external requests (JSON API). 
     * Used by AJAX
     * 
     * ATTENTION: This action is intentionally defined as PROTECTED to force its
     * overwriting in child classes for sake of security. If this action would be defined as
     * public then it is enough to give public rights for its inherited copy in child classes 
     * and anybody can launch (e.g. from JS console) queries in table of $this->Model 
     * (see also following NOTE). When overwriting this action in child classes, set its 
     * visibility to PUBLIC and options 'allowFields', 'allowJoins' and 'allowLiterals' 
     * in $this->findOptions set to safe values. The TOP SECURE SOLUTION is to create 
     * a dedicated action (e.g. ::findProductsList()) which does not accept any options 
     * from request (or only very small set of them, e.g. 'limit', 'page', ...) and most of crucial 
     * options ('fields', 'joins', 'literals', ...) are defined inside of this dedicated method.
     * 
     * NOTE: In case of this action default values of options 'allowFields', 'allowJoins' 
     * and 'allowLiterals' in $this->findOptions are set to:
     *      - FALSE for 'allowJoins', 'allowLiterals'. It means no joins and no
     *          literals are allowed in Model::findList() options by default.
     *      - array('{primaryKeyField}', '{qualifiePrimaryKeyField}') for 'allowFields'. 
     *          It means only primary key field (id) is allowed in Model::findList() options by default.
     * These 3 options are ignored in options received in request. Values of all other
     * options (whose defaults can be defined in $this->findOptions) can be overwritten by
     * values received in request.
     * 
     * NOTE: For more information see SmartController class phpDoc and corresponding
     * chapter on http://fajnbook.run.sk
     * 
     * @param array $options Optional. Explicit Model::findList() options. If provided
     *      then options comming in request are ignored(!). If NULL then request options
     *      are get by method $this->getRequestFindOptions(). This argument is designed
     *      for calls of parent::find($options) in child classes. Defaults to NULL.
     * 
     * @return string JSON response including keys:
     *      - 'data' array of record(s)
     *      - 'total' number of returned record(s)
     */
    protected function findList($options = null) {
        $this->allowSmartAction();
        App::setLayout('App', 'json');
        App::loadLib('App', 'AjaxResponse');
        $options = $this->getRequestFindOptions($options);
        if (empty($this->viewOptions['data'])) {
            $this->viewOptions['data'] = $this->Model->findList($options);
        }
        $this->viewOptions['total'] = count($this->viewOptions['data']);
        if (!empty($this->view)) {
            return $this->loadView($this->view, $this->viewOptions);
        }
        $Response = new AjaxResponse($this->viewOptions);
        return $Response->getJson();
    }
    
    //
    // INTERNAL METHODS
    //
    
    /**
     * Returns find options passed to application by external (e.g. AJAX) request.
     * 
     * ATTENTION: Values of options 'allowFields', 'allowJoins' and 'allowLiterals' 
     * in $this->findOptions are set to:
     *      - FALSE for 'allowJoins', 'allowLiterals'. It means no joins and no
     *          literals are allowed in Model::findList() options by default.
     *      - array('{primaryKeyField}', '{qualifiePrimaryKeyField}') for 'allowFields'. 
     *          It means only primary key field (id) is allowed in Model::findList() options by default.
     * These 3 options are ignored in options received in request. Values of all other
     * options (whose defaults can be defined in $this->findOptions) can be overwritten by
     * values received in request.
     * 
     * @param array $explicitOptions Optional. If provided and not NULL then this 
     *      options are returned. Otherwise request options are resolved as described above.
     *      Defaults to NULL.
     * 
     * @return array
     */
    protected function getRequestFindOptions($explicitOptions = null) {
        if ($explicitOptions !== null) {
            if (!is_array($explicitOptions)) {
                $explicitOptions = array();
            }
            return $explicitOptions;
        }
        $requestOptions = App::getRawInput();
        $requestOptions = json_decode($requestOptions, true);
        if (
            empty($requestOptions)
            || !is_array($requestOptions)
        ) {
            $requestOptions = array();
        }
        // disallow following options in request
        unset($requestOptions['allowLiterals']);
        unset($requestOptions['allowJoins']);
        unset($requestOptions['allowFields']);
        
        $requestOptions = array_merge(
            $this->findOptions, 
            $requestOptions
        );
        return array_merge(
            array(
                'allowLiterals' => false,
                'allowJoins' => false,
                'allowFields' => array(
                    $this->modelPrimaryKeyField,
                    $this->Model->qualifyField(
                        $this->modelPrimaryKeyField, 
                        $requestOptions
                    ),
                ),
            ),    
            $requestOptions
        );
    }
        
    /**
     * Sets SmartController::$findOptions to implicit values for Model::find().
     * It is supposed to be used in 'admin_index', 'admin_showTree', 'admin_showTreeLevel', 'admin_export' methods.
     */
    protected function setImplicitFindOptions() {
        //
        // set findOptions (but only those which are not yet set explicitly)
        // 
        // - conditions
        if (!isset($this->findOptions['conditions'])) {
            $this->findOptions['conditions'] = array();
            if (
                $this->action === 'admin_export'
                && $this->modelIsTranslatedByRecords
            ) {
                $this->findOptions['conditions'][$this->Model->qualifyField('lang', $this->findOptions)] = $this->lang;
            }
        }
        // - fields
        if (!isset($this->findOptions['fields'])) {
            if ($this->action === 'admin_showTree') {
                $this->findOptions['fields'] = array();
                foreach ($this->modelSchema as $field => $definition) {
                    $type = strtolower($definition['type']);
                    $qualifiedField = $this->Model->qualifyField($field, $this->findOptions);
                    if (
                          isset($this->modelFileFields[$field])
                          || $field === 'created'  
                          || $field === 'modified'  
                          || $field === 'deleted'  
                          || $field === 'lang' && $this->modelIsTranslatedByRecords  
                          || $type === 'tinytext'  
                          || $type === 'text'  
                          || $type === 'mediumtext'  
                          || $type === 'longtext'  
                    ) {
                        continue;
                    }
                    $this->findOptions['fields'][] = $qualifiedField;
                }
            }
            else {
                $this->findOptions['fields'] = '*';
            }
        }
        // - order
        if (!isset($this->findOptions['order'])) {
            if (isset($this->modelSchema['sort'])) {
                $this->findOptions['order'] = $this->Model->qualifyField('sort', $this->findOptions) . ' ASC';
            }
            else {
                $this->findOptions['order'] = $this->Model->qualifyField($this->modelPrimaryKeyField, $this->findOptions) . ' DESC';
            }
        }
        // - paginate
        if (!isset($this->findOptions['paginate'])) {
            if ($this->action === 'admin_index') {
                $this->findOptions['paginate'] = true;
            }
        }
        // -- allow paginator filtering & sorting without limit
        if ($this->action === 'admin_export') {
            $this->findOptions['paginate'] = true;
            $this->findOptions['limit'] = false;
        }
        // - lang
        if (
            $this->modelIsTranslatedByFields
            && !isset($this->findOptions['lang'])
        ) {
            $this->findOptions['lang'] = $this->lang;
        }
        // - resource
        if ($this->action === 'admin_export') {
            $this->findOptions['resource'] = true;
        }
    }
    
    /**
     * Sets SmartController::$viewOptions to implicit values for Html::smartIndex.
     * It is supposed to be used in 'admin_index', 'admin_showTree', 'admin_showTreeLevel' methods.
     * 
     * @param int $rootId Optional. It is considered only in case of 'admin_showTree' $action
     * @param array $roots Optional. It is considered only in case of 'admin_showTree' $action
     */
    protected function setImplicitSmartIndexOptions($rootId = null, $roots = null) {
        $separated = !empty($this->findOptions['separate']);
        $nameField = $this->modelNameField;
        if (empty($nameField)) {
            $nameField = $this->modelPrimaryKeyField;
        }
        //
        // set viewOptions (but only those which are not yet set explicitly)
        //
        // - title
        if (empty($this->viewOptions['title'])) {
            $this->viewOptions['title'] = Str::humanize($this->name, '');
        }
        // - tree
        if ($this->action === 'admin_showTree') {
            if (empty($this->viewOptions['tree'])) {
                $this->viewOptions['tree'] = array();
            }
            if (!isset($this->viewOptions['tree']['column'])) {
                if ($separated) {
                    $this->viewOptions['tree']['column'] = $this->Model->qualifyField($nameField, $this->findOptions);
                }
                else {
                    $this->viewOptions['tree']['column'] = $nameField;
                }
            }
            if (empty($this->viewOptions['tree']['status'])) {
                $this->viewOptions['tree']['status'] = 'collapsed';
            }
            if (!isset($this->viewOptions['tree']['showNodeType'])) {
                $this->viewOptions['tree']['showNodeType'] = true;
            }        
        }
        // - primaryKey
        if (!array_key_exists('primaryKey', $this->viewOptions)) {
            if ($separated) {
                $this->viewOptions['primaryKey'] = $this->Model->qualifyField($this->modelPrimaryKeyField, $this->findOptions);
            }
            else {
                $this->viewOptions['primaryKey'] = $this->modelPrimaryKeyField;
            }
        }
        // - columns
        if (empty($this->viewOptions['columns'])) {
            if ($this->action === 'admin_showTree') {
                if ($separated) {
                    $this->viewOptions['columns'] = array(
                        $this->Model->qualifyField($nameField, $this->findOptions) 
                            => Str::humanize($nameField),
                    );
                }
                else {
                    $this->viewOptions['columns'] = array(
                        $nameField 
                            => Str::humanize($nameField),
                    );                    
                }
            }
            else {
                $this->viewOptions['columns'] = array();
                foreach ($this->modelSchema as $field => $definition) {
                    $type = strtolower($definition['type']);
                    $qualifiedField = $field;
                    if ($separated) {
                        $qualifiedField = $this->Model->qualifyField($qualifiedField, $this->findOptions);
                    }
                    if (
                          isset($this->modelFileFields[$field])
                          ||$field === 'parent_id'  
                          || $field === 'path'  
                          || $field === 'sort'  
                          || $field === 'created'  
                          || $field === 'modified'  
                          || $field === 'deleted'  
                          || $field === 'lang' && $this->modelIsTranslatedByRecords  
                          || $type === 'tinytext'  
                          || $type === 'text'  
                          || $type === 'mediumtext'  
                          || $type === 'longtext'  
                    ) {
                        continue;
                    }
                    $this->viewOptions['columns'][$qualifiedField] = Str::humanize($field);
                }
            }
        }
        // - Model
        $this->viewOptions['Model'] = $this->Model;
        // - renderFields and renderRow
        $extendRenderFields = false;
        if (
            !isset($this->viewOptions['renderFields'])
            || ($extendRenderFields = (
                is_array($this->viewOptions['renderFields'])
                && reset($this->viewOptions['renderFields']) === '*' 
                && key($this->viewOptions['renderFields']) === 0
            ))
            || !isset($this->viewOptions['renderRow'])
            // extendRenderRow (you cannot assign it to variable as this may not get executed)
            || (
                is_array($this->viewOptions['renderRow'])
                && reset($this->viewOptions['renderRow']) === '*' 
                && key($this->viewOptions['renderRow']) === 0
            )
        ) {
            $renderFields = array();
            $renderRow = array();
            foreach ($this->modelSchema as $field => $definition) {
                $type = strtolower($definition['type']);
                $qualifiedField = $field;
                if ($separated) {
                    $qualifiedField = $this->Model->qualifyField($qualifiedField, $this->findOptions);
                }
                if (
                    $type === 'bool'
                    || $type === 'boolean'
                ) {
                    $renderFields[$qualifiedField] = array(
                        0 => __a(__FILE__, 'No'),
                        1 => __a(__FILE__, 'Yes'),
                    );
                    if ($field === 'active') {
                        $renderRow[] = array(
                            'conditions' => array($qualifiedField => 0),
                            'attributes' => array('class' => '-run-six-inactive'),
                        );
                    }
                    elseif ($field === 'hidden') {
                        $renderRow[] = array(
                            'conditions' => array($qualifiedField => 1),
                            'attributes' => array('class' => '-run-six-hidden'),
                        );  
                    }
                }
                elseif (
                    $type === 'enum'
                ) {
                    $renderFields[$qualifiedField] = $this->Model->getEnumValues($field);
                }
            }
            if (!isset($this->viewOptions['renderFields'])) {
                $this->viewOptions['renderFields'] = $renderFields;
            }
            elseif ($extendRenderFields) {
                array_shift($this->viewOptions['renderFields']);
                $this->viewOptions['renderFields'] = array_merge(
                    $renderFields, $this->viewOptions['renderFields']
                );
            }
            if (!isset($this->viewOptions['renderRow'])) {
                $this->viewOptions['renderRow'] = $renderRow;
            }
            // extendRenderRow
            elseif (
                is_array($this->viewOptions['renderRow'])
                && reset($this->viewOptions['renderRow']) === '*' 
                && key($this->viewOptions['renderRow']) === 0
            ) {
                array_shift($this->viewOptions['renderRow']);
                $this->viewOptions['renderRow'] = Arr::getUniqueValues(array_merge(
                    $this->viewOptions['renderRow'], $renderRow
                ));
            }
        }
        // - actions
        $extendActions = false;
        if (
            !isset($this->viewOptions['actions'])
            || ($extendActions = (
                is_array($this->viewOptions['actions'])
                && reset($this->viewOptions['actions']) === '*' 
                && key($this->viewOptions['actions']) === 0
            ))                   
        ) {
            $actions = array();
            if ($this->modelIsTree) {   
                if ($this->action === 'admin_index') {                    
                    $actions['showTree'] = array(
                        'url' => array(
                            'module' => $this->module,
                            'controller' => $this->name,
                            'action' => 'admin_showTree',
                        ),
                    );
                }
                elseif ($this->action === 'admin_showTree') {
                    $actions['showIndex'] = array(
                        'url' => array(
                            'module' => $this->module,
                            'controller' => $this->name,
                            'action' => 'admin_index',
                        ),
                    );                    
                }
            }
            if (
                $this->action === 'admin_showTree'
                || $this->action === 'admin_showTreeLevel'
                || $this->action === 'admin_index' && !$this->modelIsTree
            ) {
                $actions['add'] = array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_add',
                    ),
                );
                if ($this->modelIsTree) {
                    $actions['add']['url']['args'] = array($rootId);
                }
            }            
            $actions['export'] = array(
                'url' => array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_export',
                    // inherit is set here below
                    'inherit' => array(), 
                ),
            );
            if (!empty($this->Model->Paginator)) {
                $actions['export']['url']['inherit']['get'][] = 
                    $this->Model->Paginator->getPropertyFilterUrlParam(); 
                $actions['export']['url']['inherit']['get'][] = 
                    $this->Model->Paginator->getPropertySortUrlParam(); 
            }
            if (
                $this->action === 'admin_showTree' 
                || $this->action === 'admin_showTreeLevel'
            ) {
                $actions['export']['url']['args'] = array($rootId);
            }
            if (
                !empty($roots)
                && count($roots) > 1
            ) {
                $actions['root'] = array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => $this->action,
                    ),
                    'options' => $roots,
                );
                $actions['export']['url']['inherit']['get'][] = 'root';
                $actions['showIndex']['url']['inherit']['get'][] = 'root';
            }
            if (
                $this->modelIsTranslatedByFields
                || $this->modelIsTranslatedByRecords
            ) {
                if ($this->modelIsTree) {
                    if ($this->action === 'admin_index') {
                        $actions['showTree']['url']['inherit']['get'][] = 'lang'; 
                    }
                    elseif ($this->action === 'admin_showTree') {
                        $actions['showIndex']['url']['inherit']['get'][] = 'lang';
                    }
                }
                $actions['export']['url']['inherit']['get'][] = 'lang'; 
                $actions['lang'] = true;        
            }
            if ($extendActions) {
                array_shift($this->viewOptions['actions']);
                $this->viewOptions['actions'] = array_merge(
                    $actions, $this->viewOptions['actions']
                );
            }
            else {
                $this->viewOptions['actions'] = $actions;
            }
        }
        // - record actions
        $extendRecordActions = false;
        if (
            !isset($this->viewOptions['recordActions'])
            || ($extendRecordActions = (
                is_array($this->viewOptions['recordActions'])
                && reset($this->viewOptions['recordActions']) === '*' 
                && key($this->viewOptions['recordActions']) === 0
            ))                  
        ) {
            $recordActions = array();
            $recordActions['view'] = array(
                'url' => array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_view',
                ),
            );
            $recordActions['edit'] = array(
                'url' => array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_edit',
                ),
            );
            if (
                $this->action === 'admin_showTree'
                || $this->action === 'admin_showTreeLevel'
                || $this->action === 'admin_index' && !$this->modelIsTree
            ) {
                $recordActions['delete'] = array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_delete',
                    ),
                    'confirmMessage' => __a(__FILE__, 'Please, confirm removal of item :name:'),
                );
            }
            if ($this->action === 'admin_showTree') {
                $recordActions['addChild'] = array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_add',
                    ),
                );
            }
            if (
                $this->modelIsOrderedList
                || $this->action === 'admin_showTreeLevel'
            ) {
                $recordActions['move'] = array(
                    'url' => array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_move',
                    ),
                );
                if ($this->action === 'admin_showTreeLevel') {
                    $recordActions['move']['url']['args'] = array($rootId);
                }
            }
            if ($extendRecordActions) {
                array_shift($this->viewOptions['recordActions']);
                $this->viewOptions['recordActions'] = array_merge(
                    $recordActions, $this->viewOptions['recordActions']
                );
            }
            else {
                $this->viewOptions['recordActions'] = $recordActions;
            }
        }        
    }
    
    /**
     * Sets SmartController::$viewOptions to implicit values for Html::smartForm.
     * It is supposed to be used in admin_add() and admin_edit() methods.
     * 
     * @param int $parentId Optional. Parent id of new added tree node. It is considered
     *      only in case of tree model translated by records
     * 
     * @return string|NULL Added/edited record lang info. It is created only if 
     *      $recordLang differs from DEFAULT_LANG and if SmartController::$Model 
     *      is tree translated by records.
     */
    protected function setImplicitSmartFormOptions($parentId = null) {
        $lang = $this->getRecordLang($parentId);
        $langInfo = $this->getRecordLangInfo($lang);
        //
        // set viewOptions (but only those which are not yet set explicitly)
        //
        // - data
        if (empty($this->viewOptions['data'])) {
            $this->viewOptions['data'] = $this->data;
        }
        // - title
        $name = $this->getRecordName();
        if (empty($this->viewOptions['title'])) {
            if ($this->action === 'admin_add') {
                $this->viewOptions['title'] = __a(__FILE__, 'New %s', Str::humanize($this->model, ''));
            }
            elseif ($this->action === 'admin_edit') {
                if (!empty($name)) {
                    $this->viewOptions['title'] = __a(__FILE__, 'Edit %s "%s"', Str::humanize($this->model, ''), $name);
                }
                elseif (!empty($this->data[$this->modelPrimaryKeyField])) {
                    $this->viewOptions['title'] = __a(__FILE__, 'Edit %s "%s"', Str::humanize($this->model, ''), $this->data[$this->modelPrimaryKeyField]);
                }
                else {
                    $this->viewOptions['title'] = __a(__FILE__, 'Edit %s', Str::humanize($this->model, ''));
                }
            }
        }
        else {
            if (!empty($name)) {
                $this->viewOptions['title'] = sprintf($this->viewOptions['title'], $name); 
            }
            elseif (!empty($this->data[$this->modelPrimaryKeyField])) {
                $this->viewOptions['title'] = sprintf($this->viewOptions['title'], $this->data[$this->modelPrimaryKeyField]);
            }
            else {
                $this->viewOptions['title'] = sprintf($this->viewOptions['title'], '');
            }
        }
        $this->viewOptions['title'] .= $langInfo;
        // - Model
        if (empty($this->viewOptions['Model'])) {
            if ($this->action === 'admin_add') {
                $this->viewOptions['Model'] = array(array($this->Model, 'backend', 'create'));
            }
            elseif ($this->action === 'admin_edit') {
                $this->viewOptions['Model'] = array(array($this->Model, 'backend', 'update'));
            }
        }
        // - columns
        if (empty($this->viewOptions['columns'])) {
            $this->viewOptions['columns'] = 4;
        }
        // - lang
        if (
            empty($this->viewOptions['lang'])
            && $this->modelIsTranslatedByFields
        ) {
            $this->viewOptions['lang'] = $lang;
        }
        // - actions
        if (empty($this->viewOptions['actions']['lang'])) {
            if (
                $this->modelIsTranslatedByRecords
                && (
                    $this->action === 'admin_add'
                    || $this->action === 'admin_edit'        
                )
                || 
                $this->modelIsTranslatedByFields
                && $this->action === 'admin_add'
            ) {                
                $this->viewOptions['actions']['lang'] = array(
                    'options' => array_combine(array($lang), array($lang))
                );
            }
            elseif (
                $this->modelIsTranslatedByFields
                && $this->action === 'admin_edit'
            ) {
                $this->viewOptions['actions']['lang'] = true;
            }
        }
        // - tabsToReloadAfterSave
        if (empty($this->viewOptions['tabsToReloadAfterSave'])) {
            $this->viewOptions['tabsToReloadAfterSave'] = array(
                App::getUrl(array(
                    'module' => $this->module,
                    'controller' => $this->name,
                    'action' => 'admin_index',
                )),
            );
            if ($this->modelIsTree) {
                $this->viewOptions['tabsToReloadAfterSave'][] = 
                    App::getUrl(array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_showTree',
                    ));
                $this->viewOptions['tabsToReloadAfterSave'][] = 
                    App::getUrl(array(
                        'module' => $this->module,
                        'controller' => $this->name,
                        'action' => 'admin_showTreeLevel',
                    ));
            }
        }
        // - fields
        if (
            empty($this->viewOptions['fields'])
            && empty($this->viewOptions['fieldsView'])
        ) {
            if ($this->action === 'admin_add') {
                $fields = $this->Model->getNotEmptyFields(array(
                    'alternative' => 'backend', 
                    'on' => 'create'
                ));
                if (
                    empty($fields)
                    && !empty($this->modelNameField)
                ) {
                    $fields = array($this->modelNameField);
                }
            }
            if (empty($fields)) {
                $fields = array_keys($this->modelSchema);
            }
            $this->viewOptions['fields'] = array();
            foreach ($fields as $field) {
                if (
                    $this->action === 'admin_add'
                    && $field === $this->modelPrimaryKeyField
                    ||
                    $this->modelIsTranslatedByRecords
                    && $field === 'lang'
                    ||
                    $field === 'path'
                    || $field === 'parent_id'
                    || $field === 'sort'
                    || $field === 'created'
                    || $field === 'modified'
                    || $field === 'deleted'
                ) {
                    continue;
                }
                $this->viewOptions['fields'][] = array(
                    'label' => Str::humanize($field),
                    'field' => $field,
                );
                if (
                    $this->action === 'admin_edit'
                    && isset($this->modelFileFields[$field])
                    && !empty($this->data[$this->modelPrimaryKeyField])
                ) {
                    $this->viewOptions['fields'][] = array(
                        'field' => $field, 
                        'type' => 'image',
                        'deleteImage' => App::getUrl(array(
                            'module' => $this->module,
                            'controller' => $this->name,
                            'action' => 'admin_deleteFile',
                            'args' => array($field, $this->data[$this->modelPrimaryKeyField])
                        )),
                    );
                }
            }
        }
        
        return $langInfo;
    }
    
    /**
     * Sets SmartController::$rootOptions to implicit values for Html::smartIndex.
     * It is supposed to be used in admin_index(), admin_showTree(), admin_showTreeLevel(), 
     * admin_export() methods.
     */
    protected function setImplicitRootOptions() {
        //
        // set rootOptions (but only if they are not yet set explicitly)
        // 
        if (empty($this->rootOptions)) {
            $nameField = $this->modelNameField;
            if (empty($nameField)) {
                $nameField = $this->modelPrimaryKeyField;
            }
            $this->rootOptions = array(
                'fields' => $this->Model->qualifyField($nameField),
                'conditions' => array('parent_id' => null),
                'order' => 'sort ASC',
            );
            if ($this->modelIsTranslatedByFields) {
                $this->rootOptions['lang'] = $this->lang;
            }
            elseif ($this->modelIsTranslatedByRecords) {
                $this->rootOptions['conditions']['lang'] = $this->lang;
            }
        }
    }
    
    /**
     * Resolves actual root of tree model
     * 
     * @param int|string $root Id or pid of root (parent node) to index childs for. 
     *      If empty then $_GET['root'] is checked and if no $_GET['root'] then 
     *      first top level root is shown. Considered only if $this->modelIsTree.
     * @param array& $roots Optional aux output. List of existing tree roots. This
     *      is used in admin_index(), admin_showTree(), admin_showTreeLevel()
     * 
     * @return int|string Resolved root id or pid
     */
    protected function getRoot($root, &$roots = null) {
        $this->setImplicitRootOptions();
        // - find roots and the actual displayed tree root
        $roots = $this->Model->findList($this->rootOptions);
        $rootIds = array_keys($roots);
        if (
            !empty($_GET['root'])
        ) {
            $root = $_GET['root'];
        }
        if (empty($root) && !empty($roots)) {
            $root = reset($rootIds);
        }
        return $root;
    }
    
    /**
     * Returns added/edited record valid name.
     * 
     * @return string|NULL Valid, not empty record name or NULL if SmartController::$Model 
     * has no name field defined or if there are validation errors for actual value 
     * of name field in SmartController::$data and ther is no primary key value there
     */
    protected function getRecordName() {
        $name = null;
        if (!empty($this->modelNameField)) {
            if (
                !empty($this->data[$this->modelNameField])
                && !$this->Model->getErrors($this->modelNameField)
            ) {
                $name = $this->data[$this->modelNameField];
            }
            elseif (
                !empty($this->data[$this->modelPrimaryKeyField])
            ) {
                $name = $this->Model->findFieldBy(
                    $this->modelNameField, 
                    $this->modelPrimaryKeyField, 
                    $this->data[$this->modelPrimaryKeyField]
                );
            }
        }
        return $name;
    }
    
    /**
     * Returns added/edited record lang
     * 
     * @param int $parentId Optional. Parent id of new added tree node. It is considered
     *      only in case of tree model translated by records
     * 
     * @return string
     */
    protected function getRecordLang($parentId = null) {
        $lang = DEFAULT_LANG;
        if (
            $this->action === 'admin_add'
            && $this->modelIsTranslatedByRecords
            && !empty($parentId)
        ) {
            $lang = $this->Model->findFieldBy('lang', $this->modelPrimaryKeyField, $parentId);
        }
        elseif ($this->action === 'admin_edit') {
            if (!empty($this->data['lang'])) {
                $lang = $this->data['lang'];
            }
            else {
                $lang = $this->lang;
            }
        }
        return $lang;
    }
    
    /**
     * Returns added/edited record lang info
     * 
     * @param string $recordLang Record lang returned by SmartController::getRecordLang()
     * 
     * @return string|NULL Record lang info is created only if $recordLang differs from 
     *      DEFAULT_LANG and if SmartController::$Model is tree translated by records.
     */
    protected function getRecordLangInfo($recordLang) {
        $langInfo = null;
        if (
            $this->modelIsTranslatedByRecords
            && $recordLang !== DEFAULT_LANG
        ) {
            $langInfo = ' - ' . strtoupper($recordLang);
        }
        return $langInfo;
    }
}
