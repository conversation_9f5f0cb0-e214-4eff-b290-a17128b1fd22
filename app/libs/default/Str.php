<?php
class Str {
    
    /**
     * The cache of camelized words
     * 
     * @var array
     */
    protected static $camelizeCache = array();
    
    /**
     * The cache of underscored words
     * 
     * @var array
     */
    protected static $underscoreCache = array();
    
    /**
     * The cache of dasherized words
     * 
     * @var array
     */
    protected static $dasherizeCache = array();
    
    /**
     * Returns string with replaced inserts (:myInsert:, ...) and format elements (%s, %f, ...)
     * 
     * ATTENTION: Method has two signatures depending if the $string contains inserts or not.
     * 
     * @param string $string String containing inserts like ':myInsert:', ':myNested.insert:', ...
     *      and possibly also format elements like %s, %f, ....
     * @param array $inserts Array of pairs '{insert}' => '{value}'. It can be also
     *      nested, e.g.:
     * 
     *          array(
     *              'myInsert' => 'My insert value'
     *              'myNested' => array(
     *                  'insert' => 'My nested insert value'
     *              ),
     *              'myPseudoNested.insert' => 'It is not necessary that each insert containing dot is nested',
     *          )
     * 
     *      ATTENTION: Only inserts present in $inserts array are replaced in $string.
     *      If in $string there are inserts which have no pair in $inserts array
     *      then these inserts are not resolved and they are let untouched, to be replaced 
     *      in further processing.
     *      ATTENTION: Allowed characters in insert names are: [a-z0-9_\-\.]
     * 
     * @param string|array $args Optional. Arguments for formating elements.
     * 
     * An alternative signature can be used in case that $string does not contain inserts:
     * 
     * @param string $string String containing format elements like %s, %f, ....
     * @param string|array $args Arguments for formating elements.
     * 
     * @return string
     */
    static public function fill($string, $inserts, $args = null) {
        if (($hasInserts = preg_match_all('/:([a-z0-9_\-][a-z0-9_\-\.]*):/i', $string, $matches))) {
            $inserts = (array)$inserts;
            $flatInserts = Arr::deflate($inserts);
            $replacements = array();
            foreach ($matches[1] as $insert) {
                if (array_key_exists($insert, $flatInserts)) {
                    $replacements[':' . $insert . ':'] = $flatInserts[$insert];
                } 
                elseif (array_key_exists($insert, $inserts)) {
                    $replacements[':' . $insert . ':'] = (string)$inserts[$insert];
                }
            }
            $string = str_replace(array_keys($replacements), $replacements, $string);
        }
        if (
            count($funcArgs = func_get_args()) < 3
            && $hasInserts
        ) {
            return $string;
        } 
        if (!$hasInserts) {
            if (is_array($inserts)) {
                $args = $inserts;
            }
            else {
                $args = array_slice($funcArgs, 1);
            }
        }
        elseif (!is_array($args)) {
            $args = array_slice($funcArgs, 2);
        }
        if (($result = vsprintf($string, $args)) === false) {
            return $string;
        }
        return $result;
    }
    
    /**
     * Returns string with replaced inserts (:myInsert:, ...)
     * 
     * @param string $string String containing inserts like ':myInsert:', ':myNested.insert:', ...
     *      and possibly also format elements like %s, %f, ....
     * @param array $inserts Array of pairs '{insert}' => '{value}'. It can be also
     *      nested, e.g.:
     * 
     *          array(
     *              'myInsert' => 'My insert value'
     *              'myNested' => array(
     *                  'insert' => 'My nested insert value'
     *              ),
     *              'myPseudoNested.insert' => 'It is not necessary that each insert containing dot is nested',
     *          )
     * @param bool& Optional aux output. Info if there were some inserts found in string.
     * 
     * @return string
     */
    static public function insert($string, $inserts, &$hasInserts = null) {
        if (($hasInserts = preg_match_all('/:([a-z0-9_-][^:\s]*):/i', $string, $matches))) {
            $inserts = (array)$inserts;
            $inserts = Arr::deflate($inserts);
            $replacements = array();
            foreach ($matches[1] as $insert) {
                if (isset($inserts[$insert])) {
                    $replacements[':' . $insert . ':'] = $inserts[$insert];
                } 
                else {
                    $replacements[':' . $insert . ':'] = null;
                }
            }
            $string = str_replace(array_keys($replacements), $replacements, $string);
        }
        return $string;
    }
    
    /**
     * Converts string word separated by provided separator to camelized version,
     * e.g. the lower_case_and_underscored_string is converted to lowerCaseAndUnderscoredString
     * or lower-case-and-dasherized-string to lowerCaseAndDasherizedString.
     * 
     * @param string $string
     * @param array $options Following are available:
     *      - 'first' (boolean) If FALSE then starting char of the first word
     *      is lowercased. If TRUE then starting char of the first word is 
     *      uppercased like starting chars of all other words. Defaults to FALSE.
     *      - 'separator' (string|array) Words separator or array of possible separators. 
     *      Defaults to '_'. NOTE: All spaces are processed as implicit separators.
     * 
     * @return string 
     */
    static public function camelize($string, $options = array()) {
        $defaults = array(
            'first' => false,
            'separator' => '_',
        );
        $options = array_merge($defaults, $options);
        // use cache only in case of single string separator
        if (($stringSeparator = is_string($options['separator']))) {
            $key = $string;
            if (isset(self::$camelizeCache[$key][$options['separator']])) {
                return self::$camelizeCache[$key][$options['separator']];
            }
        }
        $string = str_replace(' ', '', ucwords(str_replace($options['separator'], ' ', $string)));
        if (!$options['first']) {
            $string = lcfirst($string);
        }
        if ($stringSeparator) {
            self::$camelizeCache[$key][$options['separator']] = $string;
        }
        return $string;
    }
    
    /**
     * Returns the given camelCasedWord as a camel_cased_word.
     * 
     * @param sring $string
     * @return string 
     */
    static public function underscore($string) {
        $key = $string;
        if (isset(self::$underscoreCache[$key])) {
            return self::$underscoreCache[$key];
        }
        $string = strtolower(preg_replace('/(?<=\\w)([A-Z])/', '_\\1', $string));
        return self::$underscoreCache[$key] = $string;
    }
    
    /**
     * Returns the given camelCasedWord as a camel-cased-word.
     * 
     * @param sring $string
     * @return string 
     */
    static public function dasherize($string) {
        $key = $string;
        if (isset(self::$dasherizeCache[$key])) {
            return self::$dasherizeCache[$key];
        }
        $string = strtolower(preg_replace('/(?<=\\w)([A-Z])/', '-\\1', $string));
        return self::$dasherizeCache[$key] = $string;
    }
    
    /**
     * Creates a slug from given $string. 
     * Under slug is meant a string converted to plain lowercased ASCII and 
     * sanitized from all non alphabetic characters with spaces (and other 
     * separating chars) replaced by provided separator.
     * 
     * NOTE: Difference between this method and Str::underscore() or Str::dasherize()
     * is that the both other suppose camelized input while this method can treat
     * any kind of input string.
     * 
     * @param string $string String to create the slug for
     * @param array $options Following are available:
     *      - 'separator' (string)  String to be used as normalized separator which will 
     *      replaces following separating characters: ' -_+/\.:,;'. Pass empty string to not use 
     *      any separator (you will get one long word). Defaults to '-'.
     *      - 'preserve' (string|array) Single ASCII character or an array of such characters
     *      which should be preserved. E.g. pass here '/' if you would like to preserve
     *      slashes in URL.
     * 
     * @return string
     */
    static public function slugize($string, $options = array()) {
        $defaults = array(
            'separator' => '-',
            'preserve' => null,
        );
        $options = array_merge($defaults, $options); 
        $separator = (string)$options['separator'];
        // define which chars should be not removed and define separators which 
        // should be normalized by specified 'separator'
        $separatingChars = array(' ', '-', '_', '+', '/', '\\', '.', ':', ',', ';');
        $separatingCharsRegex = implode('\\', $separatingChars);
        if ($options['preserve'] !== null) {
            $options['preserve'] = (array)$options['preserve'];
            // add preserved chars to keepRegexp
            $separatingCharsRegex .= implode('\\', array_diff($options['preserve'], $separatingChars));
            // remove preserved chars from chars which are normalized to 'separator'
            $separatingChars = array_diff($separatingChars, $options['preserve']);
        }
        //remove accent and lowercase
        $string = strtolower(Sanitize::nonAscii($string));
        // remove html entities
        $string = preg_replace('/(&(amp;)?[a-z]+;?)|(&#[0-9]{1,2};?)/', '', $string);
        // remove all chars which are not alphanumeric or one of separating chars
        $string = preg_replace('/[^a-z0-9' . $separatingCharsRegex . ']/', '', $string); 
        // replace separating chars with normalized separator 
        $string = str_replace($separatingChars, $separator, $string);
        // replace multiple occurences of separator by a single separator char
        // and trim leading and trailing separator characters
        if ($separator !== '') {
            $string = preg_replace('/' . $separator . '+/', $separator, $string);
            $string = trim($string, $separator);
        }
        
        if (Validate::intNumber($string)) {
            $string = 'n' . $string;
        }
	    
        return $string;
    }
    
    /**
     * Returns the given string in humanized form.
     * The provided string can be given un underscored, slugized or camelized form.
     * 
     * @param string $string
     * @param string|array $separator Separator or array of separators used for 
     *      underscored or slugized form of string. If empty then the string is 
     *      considered to be in camelized form. Defaults to '_'.
     * 
     * @return string
     */
    static public function humanize($string, $separator = '_') {
        if (!$separator) {
            return ucfirst(preg_replace('/(?<=\\w)([A-Z])/', ' \\1', $string));
        }
        return ucwords(str_replace($separator, ' ', $string));
    }
    
    /**
     * Visual paragraphs in plain text are replaced by HTML paragraphs. E.g. string 
     * like (including the new lines):
     * 
     *      Some first row
     *      Some second row
     * 
     *      Some third row, but first in visual paragraph
     * 
     * is paragraphized into string:
     *      
     *      < p>Some first row
     *      Some seconf row< /p>< p>Some third row, but first in visual paragraph< /p>
     * 
     * See that the single new line is preserved, because it does not create visually new paragraph.
     * 
     * @param string $string
     * @param array $options Following are available:
     *      - 'html' (bool) If TRUE then HTML new lines (< br >) are considered to
     *          create paragraphs (string is treated as HTML code). Defaults to FALSE.
     * 
     * @return string
     */
    static public function paragraphize($string, $options = array()) {
        $defaults = array(
            'html' => false,
        );
        $options = array_merge($defaults, $options);
        if ($options['html']) {
            return '<p>' . preg_replace('/<br\s*\/?>\s*<br\s*\/?>/iu', '</p><p>', $string) . '</p>';
        }
        return '<p>' . preg_replace('/\R\R/u', '</p><p>', $string) . '</p>';
    }
    
    
    /**
     * Re-encodes provided string into specified output encoding. 
     * 
     * @param string $string 
     * @param array $options Following are available:
     *      - 'inputEncoding' (string) If not provided or empty, the input encoding will be guessed.
     *          For the list of encodings which can be guessed see http://php.net/manual/en/mbstring.supported-encodings.php
     *      - 'outputEncoding' (string) Defaults to UTF-8.
     *      - 'transliterate' (bool) Defaults to TRUE.
     * 
     * @return string Converted string. On failure an exception is thrown.
     * @throws Exception on failure
     */
    static public function encode($string, $options = array()) {
        if (empty($string)) {
            return $string;
        }
        $defaults = array(
            'inputEncoding' => NULL,
            'outputEncoding' => 'UTF-8',
            'transliterate' => true,
        );
        $options = array_merge($defaults, $options);
        if (empty($options['inputEncoding'])) {
            $detectOrder = array(
                'ASCII',
                'UTF-8',
                'ISO-8859-1',
                'ISO-8859-2',
            );
            $inputEncoding = mb_detect_encoding($string, $detectOrder, true);
            if (empty($inputEncoding)) {
                $inputEncoding = mb_detect_encoding($string);
            }
            if (empty($inputEncoding)) {
                throw new Exception(__e(__FILE__, 'Autodetection of input encoding has failed'));
            } 
            $options['inputEncoding'] = $inputEncoding;
        }
        if ($options['transliterate']) {
            $options['outputEncoding'] .= '//TRANSLIT';
        }
        
        $converted = iconv($options['inputEncoding'], $options['outputEncoding'], $string);
        if ($converted === FALSE || $converted === NULL) {
            throw new Exception(__e(__FILE__, 'String encoding conversion has failed'));
        }
        return $converted;
    }
    
    /**
     * Enhanced version of explode() solving case where explode(',', '') returns
     * array(''). Str::explode(',', '') returns array(). More over it is
     * possible to define keys for array of exploded string parts.
     *
     * @param string $delimiter The boundary string.
     * @param string $string The input string.
     * @param array $options Following are available
     *      - 'limit' (int) If limit is set and positive, the returned array will 
     *          contain a maximum of limit elements with the last element containing 
     *          the rest of string. If the limit parameter is negative, all components 
     *          except the last -limit are returned. If the limit parameter is zero, 
     *          then this is treated as 1. If NULL then it is ignored. Default to NULL.
     *      - 'keys' (array|string) Array of keys to be used for exploded string parts.
     *          Keys are applied in the order as they are provided. If provided as string
     *          then it is converted to array exploding with provided $delimiter.
     *          Defaults to NULL.
     * 
     * @return array|bool If delimiter is an empty string (""), explode() will return FALSE. 
     *      If delimiter contains a value that is not contained in string and a negative 
     *      limit is used, then an empty array will be returned, otherwise an array containing 
     *      string will be returned.
     */
    public static function explode($delimiter, $string, $options = array()) {
        $defaults = array(
            'limit' => null,
            'keys' => null,
        );
        $options = array_merge($defaults, $options);
        if ($string == '') {
            return array();
        }
        if ($options['limit'] !== null) {
            $parts = explode($delimiter, $string, (int)$options['limit']);
        }
        $parts = explode($delimiter, $string);
        // add keys if provided
        if ($options['keys']) {
            if (!is_array($options['keys'])) {
                $options['keys'] = explode($delimiter, $options['keys']);
            }
            $tmp = array();
            foreach ($parts as $i => $part) {
                if (isset($options['keys'][$i])) {
                    $tmp[$options['keys'][$i]] = $part;
                }
                else {
                    $tmp[$i] = $part;
                }
            }
            $parts = $tmp;
        }
        return $parts;
    }
    
    
    /**
     * Truncates provided string to given max limit of characters.
     * Leading and trailing whitespaces are trimed.
     * 
     * @param string $string String to truncate
     * @param int $limit Max final length (it is up to you to consider $pad length)
     * @param string $pad Optional. Defalts to '...'
     * @param string $encoding Optional. Charset of provided string (UTF-8, CP1250, ...).
     *      If NULL then string is considered to be plain ASCII and plain string
     *      functions (not the mb_... versions) are used to treat it. Defaults to UTF-8.
     * 
     * @return string Truncated string
     */
    static public function truncate($string, $limit, $pad = '...', $encoding = 'UTF-8') {
        $string = trim($string);
        if ($encoding === null) {
            // return with no change if string is shorter than $limit
            if(strlen($string) <= $limit) {
                return $string;
            }
            // make first raw truncation (take one char after to check if there is a space char after truncated string)
            $string = substr($string, 0, $limit + 1);
            // find the first space char from the end of raw truncated string
            if(preg_match('/[\s,;\-]+[^\s,;\-]*$/D', $string, $match, PREG_OFFSET_CAPTURE)) {
                $string = substr($string, 0, $match[0][1]);
            }
        }
        else {
            // normailize encoding
            $encoding = strtoupper($encoding);
            // return with no change if string is shorter than $limit
            if(mb_strlen($string, $encoding) <= $limit) {
                return $string;
            }
            // make first raw truncation (take one char after to check if there is a space char after truncated string)
            $string = mb_substr($string, 0, $limit + 1, $encoding);
            // find the first space char from the end of raw truncated string
            if($encoding == 'UTF-8' || $encoding == 'UTF8'){
                if(preg_match('/[\s,;\-]+[^\s,;\-]*$/Du', $string, $match, PREG_OFFSET_CAPTURE)){
                    // use of substr (not mb_) is ok here, offset in match is 
                    // calculated such that substr should be used
                    $string = substr($string, 0, $match[0][1]); 
                }
            }
            elseif(preg_match('/[\s,;\-]+[^\s,;\-]*$/D', $string, $match, PREG_OFFSET_CAPTURE)) {
                // use of substr (not mb_) is ok here, offset in match is 
                // calculated such that substr should be used
                $string = substr($string, 0, $match[0][1]);
            }
        }
        return $string . $pad;
    }    
        
    /**
     * Convert the given string to uppercase.
     * Version of strtoupper() for multibyte encodings
     *
     * @param string $string
     * @param string $encoding String encoding. Defaults to 'UTF-8'
     * 
     * @return string
     */
    public static function uppercase($string, $encoding = 'UTF-8') {
        if ($encoding) {
            return mb_strtoupper($string, $encoding);
        }
        return mb_strtoupper($string);
    }
    
    /**
     * Convert the given string to lowercase.
     * Version of strtolower() for multibyte encodings
     *
     * @param string $string
     * @param string $encoding String encoding. Defaults to 'UTF-8'
     * 
     * @return string
     */
    public static function lowercase($string, $encoding = 'UTF-8') {
        if ($encoding) {
            return mb_strtolower($string, $encoding);
        }
        return mb_strtolower($string);
    }
    
    /**
     * Converts the first character to uppercase.
     * Version of ucfirst() for multibyte encodings
     * 
     * @param string $string
     * @param string $encoding String encoding. Defaults to 'UTF-8'
     * 
     * @return string
     */
    public static function uppercaseFirst($string, $encoding = 'UTF-8') {
        return mb_strtoupper(mb_substr($string, 0, 1, $encoding), $encoding) . mb_substr($string, 1, mb_strlen($string, $encoding) - 1, $encoding);
    }
    
    /**
     * Version of lcfirst() for multibyte encodings
     * 
     * @param string $string
     * @param string $encoding String encoding. Defaults to 'UTF-8'
     * 
     * @return string
     */
    public static function lowercaseFirst($string, $encoding = 'UTF-8') {
        return mb_strtolower(mb_substr($string, 0, 1, $encoding), $encoding) . mb_substr($string, 1, mb_strlen($string, $encoding) - 1, $encoding);
    }
    
    /**
     * Creates excerpt from provided text
     * 
     * ATTENTION: Do not use this method to do excertps from HTML code (It is imposible
     * to do something like that in general case). If you need to do an excerpt from
     * HTML then convert it at first to text by Sanitize::htmlToText($myHtml).
     * 
     * @param string $text Text to create excerpt from
     * @param array $options Following options can be used:
     *      - 'length' (int) Final excerpt max length. Length meaning is precised
     *          by 'unit' option. Defaults to 150.
     *      - 'unit' (string) What we are limiting by 'length'? Characters or words?
     *          Possible values are 'character', 'word'. Defaults to 'character'. 
     *      - 'suffix' (string) Excerpt suffix. Defaults to '...'.
     *      - 'encoding' (string) Text and words encoding. Defaults to 'UTF-8'.
     *      - 'includedWords' (string|array) Single word or array of words to be 
     *          included in excerpt. Defaults to NULL, means it is not considered.
     *      - 'prefix' (string) Excerpt prefix. Considered only if 'includedWords'
     *          are provided. Defaults to '...'.
     * 
     * @return string
     */
    static public function getExcerpt($text, $options = array()) {
        $defaults = array(
            'length' => 150,
            'unit' => 'character', //@todo implement this option also if 'includedWords' are provided.
            'suffix' => '...',
            'encoding' => 'UTF-8',
            'includedWords' => null, // @todo
            'prefix' => '...', // @todo
            // score modifiers
            'exactCaseBonus'  => 2,
            'exactWordBonus'  => 3,
            'absLengthWeight' => 0.0,
            'relLengthWeight' => 1.0,
            'debug' => false
        );
        $options = array_merge($defaults, $options);
        // normalize unit to 'c' or 'w'
        $unit = substr(strtolower($options['unit']), 0, 1);
        
        // normalize text
        $text = preg_replace('/\s{2,}/', ' ', $text);
        $text = html_entity_decode($text, ENT_QUOTES, $options['encoding']);
        
        if (mb_strlen($text, $options['encoding']) <= $options['length']) {
          return $text;
        }
        
        //
        // Do excerpt without 'includedWords'
        // 
        $options['includedWords'] = array_filter((array)$options['includedWords']);
        if (empty($options['includedWords'])) {
            $excerpt = '';
            $words = explode(' ', $text);
            $maxLength = $options['length'];
            if ($unit === 'c') {
                $suffixLength = mb_strlen($options['suffix'], $options['encoding']);
                $maxLength -= $suffixLength;
                if ($maxLength <= 0) {
                    throw new Exception('Invalid excerpt length %s. It must be higher than suffix (%s) length.', $options['length'], $suffixLength);
                }
            }
            foreach ($words as $i => $word) {
                if ($i !== 0) {
                    $word = ' ' . $word;
                }
                if (
                    $unit === 'c'
                    && (mb_strlen($excerpt, $options['encoding']) 
                    + mb_strlen($word, $options['encoding'])) > $maxLength
                    ||
                    $unit === 'w'
                    && $i >= $maxLength
                ) {
                    break;
                }
                $excerpt .= $word;
            }
            $excerpt .= $options['suffix'];

            return $excerpt;
        }
        
        //
        // Do excerpt with 'includedWords'
        // 
        
        // Build the event list
        // [also calculate maximum word length for relative weight bonus]
        $events = array();
        $maxWordLength = 0;

        foreach ($options['includedWords'] as $word) {

          if (mb_strlen($word, $options['encoding']) > $maxWordLength) {
            $maxWordLength = mb_strlen($word, $options['encoding']);
          }

          $i = -1;
          while (($i = mb_stripos($text, $word, $i+1, $options['encoding'])) !== false ) {

            // Basic score for a match is always 1
            $score = 1;

            // Apply modifiers
            if (mb_substr($text, $i, mb_strlen($word, $options['encoding']), $options['encoding']) == $word) {
              // Case matches exactly
              $score += $options['exactCaseBonus'];
            }
            if ($options['absLengthWeight'] != 0.0) {
              // Absolute length weight (longer words count for more)
              $score += mb_strlen($word, $options['encoding']) * $options['absLengthWeight'];
            }
            if ($options['relLengthWeight'] != 0.0) {
              // Relative length weight (longer words count for more)
              $score += mb_strlen($word, $options['encoding']) / $maxWordLength * $options['relLengthWeight'];
            }
            if (preg_match('/\W/', mb_substr($text, $i-1, 1, $options['encoding']))) {
              // The start of the word matches exactly
              $score += $options['exactWordBonus'];
            }
            if (preg_match('/\W/', mb_substr($text, $i+mb_strlen($word, $options['encoding']), 1, $options['encoding']))) {
              // The end of the word matches exactly
              $score += $options['exactWordBonus'];
            }

            // Push event occurs when the word comes into range
            $events[] = array(
              'type'  => 'push',
              'word'  => $word,
              'pos'   => max(0, $i + mb_strlen($word, $options['encoding']) - $options['length']),
              'score' => $score
            );
            // Pop event occurs when the word goes out of range
            $events[] = array(
              'type' => 'pop',
              'word' => $word,
              'pos'  => $i + 1,
              'score' => $score
            );
            // Bump event makes it more attractive for words to be in the
            // middle of the excerpt [@todo: this needs work]
            $events[] = array(
              'type' => 'bump',
              'word' => $word,
              'pos'  => max(0, $i + floor(mb_strlen($word, $options['encoding'])/2) - floor($options['length']/2)),
              'score' => 0.5
            );

          }
        }

        // If nothing is found then just truncate from the beginning
        if (empty($events)) {
          return mb_substr($text, 0, $options['length'], $options['encoding']) . $options['suffix'];
        }

        // We want to handle each event in the order it occurs in
        // [i.e. we want an event queue]
        $events = Arr::sortByKey($events, 'pos');

        $scores = array();
        $score = 0;
        $current_words = array();

        // Process each event in turn
        foreach ($events as $idx => $event) {
          $thisPos = floor($event['pos']);

          $word = mb_strtolower($event['word'], $options['encoding']);

          switch ($event['type']) {
          case 'push':
            if (empty($current_words[$word])) {
              // First occurence of a word gets full value
              $current_words[$word] = 1;
              $score += $event['score'];
            }
            else {
              // Subsequent occurrences mean less and less
              $current_words[$word]++;
              $score += $event['score'] / sizeof($current_words[$word]);
            }
            break;
          case 'pop':
            if (($current_words[$word])==1) {
              unset($current_words[$word]);
              $score -= ($event['score']);
            }
            else {
              $current_words[$word]--;
              $score -= $event['score'] / sizeof($current_words[$word]);
            }
            break;
          case 'bump':
            if (!empty($event['score'])) {
              $score += $event['score'];
            }
            break;
          default:
          }

          // Close enough for government work...
          $score = round($score, 2);

          // Store the position/score entry
          $scores[$thisPos] = $score;

          // For use with debugging
          $debugWords[$thisPos] = $current_words;

          // Remove score bump
          if ($event['type'] == 'bump') {
              $score -= $event['score'];
          }
        }

        // Calculate the best score
        // Yeah, could have done this in the main event loop
        // but it's better here
        $bestScore = 0;
        foreach ($scores as $pos => $score) {
            if ($score > $bestScore) {
              $bestScore = $score;
            }
        }


        if ($options['debug']) {
          // This is really quick, really tatty debug information
          // (but it works)
          echo "<table border>";
          echo "<caption>Events</caption>";
          echo "<tr><th>Pos</th><th>Type</th><th>Word</th><th>Score</th>";
          foreach ($events as $event) {
            echo "<tr>";
            echo "<td>{$event['pos']}</td><td>{$event['type']}</td><td>{$event['word']}</td><td>{$event['score']}</td>";
            echo "</tr>";
          }
          echo "</table>";

          echo "<table border>";
          echo "<caption>Positions and their scores</caption>";
          $idx = 0;
          foreach ($scores as $pos => $score) {
            $excerpt = mb_substr($text, $pos, $options['length'], $options['encoding']);
            $style = ($score == $bestScore) ? 'background: #ff7;' : '';

            //$score = floor($score + 0.5);

            echo "<tr>";
            echo "<th style=\"$style\">" . $idx . "</th>";
            echo "<td style=\"$style\">" . $pos . "</td>";
            echo "<td style=\"$style\"><div style=\"float: left; width: 2em; margin-right: 1em; text-align right; background: #ddd\">" . $score . "</div><code>" . str_repeat('*', $score) . "</code></td>";
            echo "<td style=\"$style\"><table border>";
            foreach ($debugWords[$pos] as $word => $count) {
              echo "<tr><td>$word</td><td>$count</td></tr>";
            }
            echo "</table></td>";
            echo "<td style=\"$style\">" . (preg_replace('/(' . implode('|', $options['includedWords']) . ')/i', '<b style="border: 1px solid red;">\1</b>', htmlentities($excerpt))) . "</td>";
            echo "</tr>";
            $idx++;
          }
          echo "</table>";
        }


        // Find all positions that correspond to the best score
        $positions = array();
        foreach ($scores as $pos => $score) {
          if ($score == $bestScore) {
            $positions[] = $pos;
          }
        }

        if (sizeof($positions) > 1) {
          // Scores are tied => do something clever to choose one
          // @todo: Actually do something clever here
          $pos = $positions[0];
        }
        else {
          $pos = $positions[0];
        }

        // Extract the excerpt from the position, (pre|ap)pend the (pre|suf)fix
        $excerpt = mb_substr($text, $pos, $options['length'], $options['encoding']);
        if ($pos > 0) {
          $excerpt = $options['prefix'] . $excerpt;
        }
        if ($pos + $options['length'] < mb_strlen($text, $options['encoding'])) {
          $excerpt .= $options['prefix'];
        }

        return $excerpt;
    }

    /**
     * Highlights specified $words in provided $string enclosing them by < b > ... < / br> tags
     * 
     * @param string $string 
     * @param string|array $words Single word string or an array of such word strings.
     * @param array $options Following are available:
     *      - 'accentsInsensitive' (bool) If TRUE then accents are ignored. It means
     *          that e.g. letters 'a', 'á' , 'ä' are all considered as 'a' (both in
     *          $string and in $words). Defaults to FALSE.
     *      - 'excerptOnly' (bool) If TRUE then only highlighted words excerpt is 
     *          returned. Defaults to FALSE.
     *      - 'excerptLeadingWordsMaxCount' (int) Defaults to 2.
     *      - 'excerptTrailingWordsMaxCount' (int) Defaults to 2.
     * 
     * @return string
     */
    static public function highlightWords($string, $words, $options = array()) {
        static $accentPatterns = array();
        static $accentReplacements = array();
        $defaults = array(
            'accentsInsensitive' => false,
            'excerptOnly' => false,
            'excerptLeadingWordsMaxCount' => 2,
            'excerptTrailingWordsMaxCount' => 2,
        );
        $options = array_merge($defaults, $options);
        $words = (array)$words;
        if (!empty($options['accentsInsensitive'])) {
            if (empty($accentPatterns)) {
                $translationTable = array(
                  // lowercases
                  'à' => 'a', 'ô' => 'o', 'ď' => 'd', 'ḟ' => 'f', 'ë' => 'e', 'š' => 's', 'ơ' => 'o',
                  'ß' => 'ss', 'ă' => 'a', 'ř' => 'r', 'ț' => 't', 'ň' => 'n', 'ā' => 'a', 'ķ' => 'k',
                  'ŝ' => 's', 'ỳ' => 'y', 'ņ' => 'n', 'ĺ' => 'l', 'ħ' => 'h', 'ṗ' => 'p', 'ó' => 'o',
                  'ú' => 'u', 'ě' => 'e', 'é' => 'e', 'ç' => 'c', 'ẁ' => 'w', 'ċ' => 'c', 'õ' => 'o',
                  'ṡ' => 's', 'ø' => 'o', 'ģ' => 'g', 'ŧ' => 't', 'ș' => 's', 'ė' => 'e', 'ĉ' => 'c',
                  'ś' => 's', 'î' => 'i', 'ű' => 'u', 'ć' => 'c', 'ę' => 'e', 'ŵ' => 'w', 'ṫ' => 't',
                  'ū' => 'u', 'č' => 'c', 'ö' => 'oe', 'è' => 'e', 'ŷ' => 'y', 'ą' => 'a', 'ł' => 'l',
                  'ų' => 'u', 'ů' => 'u', 'ş' => 's', 'ğ' => 'g', 'ļ' => 'l', 'ƒ' => 'f', 'ž' => 'z',
                  'ẃ' => 'w', 'ḃ' => 'b', 'å' => 'a', 'ì' => 'i', 'ï' => 'i', 'ḋ' => 'd', 'ť' => 't',
                  'ŗ' => 'r', 'ä' => 'a', 'í' => 'i', 'ŕ' => 'r', 'ê' => 'e', 'ü' => 'ue', 'ò' => 'o',
                  'ē' => 'e', 'ñ' => 'n', 'ń' => 'n', 'ĥ' => 'h', 'ĝ' => 'g', 'đ' => 'd', 'ĵ' => 'j',
                  'ÿ' => 'y', 'ũ' => 'u', 'ŭ' => 'u', 'ư' => 'u', 'ţ' => 't', 'ý' => 'y', 'ő' => 'o',
                  'â' => 'a', 'ľ' => 'l', 'ẅ' => 'w', 'ż' => 'z', 'ī' => 'i', 'ã' => 'a', 'ġ' => 'g',
                  'ṁ' => 'm', 'ō' => 'o', 'ĩ' => 'i', 'ù' => 'u', 'į' => 'i', 'ź' => 'z', 'á' => 'a',
                  'û' => 'u', 'þ' => 'th', 'ð' => 'dh', 'æ' => 'ae', 'µ' => 'u', 'ĕ' => 'e',
                  // uppercases
                  'À' => 'A', 'Ô' => 'O', 'Ď' => 'D', 'Ḟ' => 'F', 'Ë' => 'E', 'Š' => 'S', 'Ơ' => 'O',
                  'Ă' => 'A', 'Ř' => 'R', 'Ț' => 'T', 'Ň' => 'N', 'Ā' => 'A', 'Ķ' => 'K',
                  'Ŝ' => 'S', 'Ỳ' => 'Y', 'Ņ' => 'N', 'Ĺ' => 'L', 'Ħ' => 'H', 'Ṗ' => 'P', 'Ó' => 'O',
                  'Ú' => 'U', 'Ě' => 'E', 'É' => 'E', 'Ç' => 'C', 'Ẁ' => 'W', 'Ċ' => 'C', 'Õ' => 'O',
                  'Ṡ' => 'S', 'Ø' => 'O', 'Ģ' => 'G', 'Ŧ' => 'T', 'Ș' => 'S', 'Ė' => 'E', 'Ĉ' => 'C',
                  'Ś' => 'S', 'Î' => 'I', 'Ű' => 'U', 'Ć' => 'C', 'Ę' => 'E', 'Ŵ' => 'W', 'Ṫ' => 'T',
                  'Ū' => 'U', 'Č' => 'C', 'Ö' => 'Oe', 'È' => 'E', 'Ŷ' => 'Y', 'Ą' => 'A', 'Ł' => 'L',
                  'Ų' => 'U', 'Ů' => 'U', 'Ş' => 'S', 'Ğ' => 'G', 'Ļ' => 'L', 'Ƒ' => 'F', 'Ž' => 'Z',
                  'Ẃ' => 'W', 'Ḃ' => 'B', 'Å' => 'A', 'Ì' => 'I', 'Ï' => 'I', 'Ḋ' => 'D', 'Ť' => 'T',
                  'Ŗ' => 'R', 'Ä' => 'A', 'Í' => 'I', 'Ŕ' => 'R', 'Ê' => 'E', 'Ü' => 'Ue', 'Ò' => 'O',
                  'Ē' => 'E', 'Ñ' => 'N', 'Ń' => 'N', 'Ĥ' => 'H', 'Ĝ' => 'G', 'Đ' => 'D', 'Ĵ' => 'J',
                  'Ÿ' => 'Y', 'Ũ' => 'U', 'Ŭ' => 'U', 'Ư' => 'U', 'Ţ' => 'T', 'Ý' => 'Y', 'Ő' => 'O',
                  'Â' => 'A', 'Ľ' => 'L', 'Ẅ' => 'W', 'Ż' => 'Z', 'Ī' => 'I', 'Ã' => 'A', 'Ġ' => 'G',
                  'Ṁ' => 'M', 'Ō' => 'O', 'Ĩ' => 'I', 'Ù' => 'U', 'Į' => 'I', 'Ź' => 'Z', 'Á' => 'A',
                  'Û' => 'U', 'Þ' => 'Th', 'Ð' => 'Dh', 'Æ' => 'Ae', 'Ĕ' => 'E',                
                );
                // prepare accent replacements regular expressions
                $letterGroups = array();
                foreach ($translationTable as $accentedLetter => $asciiLetter) {
                    if (strlen($asciiLetter) > 1) {
                        continue;
                    }
                    if (!isset($letterGroups[$asciiLetter])) {
                        $letterGroups[$asciiLetter] = $asciiLetter;
                    }
                    $letterGroups[$asciiLetter] .= $accentedLetter;
                }
                $accentPatterns = $accentReplacements = array();
                foreach ($letterGroups as $letterGroup) {
                    $accentPatterns[] = '/[' . $letterGroup . ']/u';
                    $accentReplacements[] = '[' . $letterGroup . ']';
                }
            }
            // transform words to accent insensitive paterns
            foreach ($words as &$word) {
                $word = preg_replace($accentPatterns, $accentReplacements, $word);
            }
            unset($word);
        }
        // highlight words
        $wordPatterns = array();
        $replacements = array();
        foreach ($words as $word) {
            $wordPatterns[] = '/(' . $word . ')/ui';
            $replacements[] = '<b>$0</b>';
        }
        $string = preg_replace($wordPatterns, $replacements, $string);
        if ($options['excerptOnly']) {
            $excerpt = '';
            if (($count = preg_match_all(
                sprintf(
                    '/(?:\S+\s*){0,%s}\S*?<b>.+?<\/b>\S*?(?:\s*\S+){0,%s}/s',
                    $options['excerptLeadingWordsMaxCount'],
                    $options['excerptTrailingWordsMaxCount']
                ), 
                $string, 
                $matches
            ))) {
                $maxIndex = $count - 1;
                foreach ($matches[0] as $i => $match) {
                    if (
                        $i !== 0 
                        || substr($string, 0, strlen($match)) !== $match
                    ) {
                        $excerpt .= '... ';
                    }
                    $excerpt .= $match;
                    if (
                        $i === $maxIndex 
                        && substr($string, -strlen($match)) !== $match
                    ) {
                        $excerpt .= '... ';
                    }
                }
            }
            $string = $excerpt;
        }
        return $string;
    }
    
    /**
     * Generates random string. This can be used for password or any other kind 
     * of random identifier/token.
     * 
     * If it is used as token to identify e.g. records in DB it is up to user 
     * to check if the token is unique (not yet used for any of existing records)
     * and regenerate it till the unique one is get.
     * 
     * @param int $length String length.
     * @param string $chars Optional. String of characters to build the random string from.
     *          If not provided or empty then 'abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ123456789'
     *          is used. Defaults NULL.
     * 
     * @return string
     */
    static public function getRandom($length, $chars = null) {
        if (mb_strlen((string)$chars) === 0) {
            $chars = 'abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ123456789';
        }
        $token = '';
        $lastCharIndex = mb_strlen($chars)-1;
        for ($i = 0; $i < $length; $i++) {
            $index = mt_rand(0, $lastCharIndex);
            $token .= mb_substr($chars, $index, 1);
        }
        return $token;
    }
    
    /**
     * Generates random unique alphanumeric string at least 36 characters long
     * 
     * @param string $prefix
     * @return string
     */
    static public function getUnique($prefix = '') {
        return $prefix . str_replace('.', '', uniqid(microtime(true), true));
    }    
    
    /**
     * Encrypt string to secure cipher.
     * 
     * Resulting cipher contains only base64 characters. If used in url it should
     * be (raw)urlencoded.
     * 
     * Cipher can be decoded by Str::decrypt() method.
     * 
     * @param string $string String to encrypt
     * @param string $key Any random ASCII string. Internally it is hashed by md5() 
     *          to 32-character hexadecimal number. If undefined then App::$cryptKey 
     *          config is used.
     * 
     * @return string
     * 
     * @throws Exception on empty $key provided. This can happen only in case that 
     *      App::$cryptKey is empty.
     */
    static public function encrypt($string, $key = null) {
        if ($key === null) {
            $key = App::$cryptKey;
        }
        $key = (string)$key;
        if ($key === '') {
            throw new Exception('Invalid (empty) key used for encryption');
        }
        $method = 'AES-256-CBC';
        $ivSize = openssl_cipher_iv_length($method);
        $iv = openssl_random_pseudo_bytes($ivSize);
        return base64_encode(
            $iv .
            openssl_encrypt(
                $string, 
                $method, 
                hash('sha256', $key), 
                OPENSSL_RAW_DATA, 
                $iv
            )
        );
    }
    
    /**
     * Decrypt string cipher, that was encoded by Str::encrypt() method.
     * 
     * @param string $string String to decrypt
     * @param string $key Any random ASCII string, but the same as used for encryption. 
     *          Internally it is hashed by md5() to 32-character hexadecimal number.
     *          If undefined then App::$cryptKey config is used.
     * 
     * @return string
     * 
     * @throws Exception on empty $key provided. This can happen only in case that 
     *      App::$cryptKey is empty.
     */
    static public function decrypt($string, $key = null) {
        if ($key === null) {
            $key = App::$cryptKey;
        }
        $key = (string)$key;
        if ($key === '') {
            throw new Exception('Invalid (empty) key used for decryption');
        }
        $method = 'AES-256-CBC';
        $ivSize = openssl_cipher_iv_length($method);
        $string = base64_decode($string);
        $iv = substr($string, 0, $ivSize);
        return openssl_decrypt(
            substr($string, $ivSize), 
            'AES-256-CBC', 
            hash('sha256', $key), 
            OPENSSL_RAW_DATA, 
            $iv
        );
    }
    
    /**
     * Normalizes end of line characters to specified eol char in UTF8 string
     * 
     * @param string $string
     * @param string $eol Normalized end of line character - all other EOL forms
     *      are replaced by this one. Possible common values are "\n", "\r\n" or
     *      "\r" (notice the double quotes), Defaults to PHP_EOL.
     *  
     * @return string
     */
    static public function normalizeEndOfLine($string, $eol = PHP_EOL) {
        // @see http://stackoverflow.com/questions/7836632/how-to-replace-different-newline-styles-in-php-the-smartest-way
        return preg_replace('/\R/u', $eol, $string);
        //return preg_replace('/(*BSR_ANYCRLF)\R/', $eol, $string);
    }   
    
    /**
     * Returns UTF8 char code
     * 
     * @param string $char A single UTF8 character
     * 
     * @return int|boolean UTF8 char code or FALSE for invalid characters
     */
    static public function getUtf8Code($char) {
        // @see http://stackoverflow.com/questions/9361303/can-i-get-the-unicode-value-of-a-character-or-vise-versa-with-php
        // NOTE: in case of strings $string[$i] is the same as $string{$i}
        if (ord($char[0]) >=0 && ord($char[0]) <= 127) {
            return ord($char[0]);
        }
        if (ord($char[0]) >= 192 && ord($char[0]) <= 223) {
            return (ord($char[0])-192)*64 + (ord($char[1])-128);
        }
        if (ord($char[0]) >= 224 && ord($char[0]) <= 239) {
            return (ord($char[0])-224)*4096 + (ord($char[1])-128)*64 + (ord($char[2])-128);
        }
        if (ord($char[0]) >= 240 && ord($char[0]) <= 247) {
            return (ord($char[0])-240)*262144 + (ord($char[1])-128)*4096 
                + (ord($char[2])-128)*64 + (ord($char[3])-128);
        }
        if (ord($char[0]) >= 248 && ord($char[0]) <= 251) {
            return (ord($char[0])-248)*16777216 + (ord($char[1])-128)*262144 
                + (ord($char[2])-128)*4096 + (ord($char[3])-128)*64 + (ord($char[4])-128);
        }
        if (ord($char[0]) >= 252 && ord($char[0]) <= 253) {
            return (ord($char[0])-252)*1073741824 + (ord($char[1])-128)*16777216 
                + (ord($char[2])-128)*262144 + (ord($char[3])-128)*4096 + (ord($char[4])-128)*64 
                + (ord($char[5])-128);
        }
        if (ord($char[0]) >= 254 && ord($char[0]) <= 255) {
            //  error
            return FALSE;
        }
        return 0;
    } 
    
    /**
     * Returns UTF8 char from its code
     *
     * @param int $code UTF8 char code
     * 
     * @return string UTF8 character
     */
    static public function getUtf8Char($code) {
        // @see http://php.net/manual/en/function.chr.php#88611
        return mb_convert_encoding('&#' . intval($code) . ';', 'UTF8', 'HTML-ENTITIES');
    }    
    
    /**
     * Obfuscates specified subject(s) in provided html string. Obfuscated 
     * are text nodes and href attributes of < a> tags. Obfuscation is made by
     * JavaScript using in pair Str::obfuscate() and inline version of Run.App.String.deobfuscate().
     * 
     * ATTENTION: If the method is not sure to obfuscate $html correctly (see
     * the bug below) then it returns unchanged $html.
     * 
     * NOTE (150418): There is a php bug related to use of preg_match(_all) with flag 
     * PREG_OFFSET_CAPTURE with nonascii (e.g. UTF-8) strings. This method checks 
     * for bug accurence and workarounds it. 
     * 
     * @param string $html Html string to be obfuscated.
     * @param array $options Following are available:
     *      - 'subject' (string|array) Specification of subject(s) to be obfuscated.
     *          This can be a regular expression string or one of 'emailAddresses', 
     *          'phoneNumbers' to use predefined regular expresions. It can be also 
     *          array of above mentioned possibilities. ATTENTION: In case of regular 
     *          expression do not use delimiters ('/') because they are attached 
     *          automatically together with flags ('iu'). Defaults to 'emailAddresses'.
     *      - 'encoding' (string) Encoding of provided $html. Defaults to 'UTF-8'.
     *      - 'attachJs' (bool) If TRUE then generated deobfuscation js code is attached 
     *          to returned obfuscated html code. If FALSE then generated deobfuscation 
     *          js code is added to App::$js and rendered at the end of body block.
     *          Defaults to FALSE.
     * 
     * @return string Obfuscated html string
     * @throws Exception on invalid regex used in $subject specification
     */
    static public function obfuscateHtml($html, $options = array()) {
        // @see http://techblog.tilllate.com/2008/07/20/ten-methods-to-obfuscate-e-mail-addresses-compared/
        static $nonAsciiPregOffsetBug = null;
        $defaults = array(
            'subject' => 'emailAddresses',
            'encoding' => 'UTF-8',
            'attachJs' => false,
        );
        $options = array_merge($defaults, $options);
        if (empty($options['subject'])) {
            return $html;
        }
        $encoding = $options['encoding'];
        
        // PHPBUGFIX: Check if there is a bug with offset capture in UTF8 strings.
        // If yes then use workarounds by using ASCII version of $html and subject
        // to find offsets. See also http://php.net/manual/en/function.preg-match.php#95828 (
        // but that solution does not work on my (mojo) PC
        if ($nonAsciiPregOffsetBug === null) {
            preg_match('/x/iu', 'čx', $match, PREG_OFFSET_CAPTURE);
            $nonAsciiPregOffsetBug = $match[0][1] !== 1;
        }
        if ($nonAsciiPregOffsetBug) {                                   //phpbugfix
            $asciiHtml = Sanitize::nonAscii($html);                     //phpbugfix
            if (strlen($asciiHtml) !== mb_strlen($html, $encoding)) {   //phpbugfix
                return $html;                                           //phpbugfix
            }                                                           //phpbugfix
        }                                                               //phpbugfix
            
        $options['subject'] = (array)$options['subject'];
        $regex = '';
        foreach ($options['subject'] as $item) {
            if ($regex !== '') {
                $regex .= '|';
            }
            if ($item === 'emailAddresses') {
                // this is not about valid email addresses - just obfuscate everything that resambles an email
                $hostnameRegex = '(?:[a-z0-9][-a-z0-9]*\.)*(?:[a-z0-9][-a-z0-9]{0,62})\.(?:(?:[a-z]{2}\.)?[a-z]{2,4}|museum|travel)';
                $emailRegex = '[a-z0-9!#$%&\'*+\/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&\'*+\/=?^_`{|}~-]+)*@' . $hostnameRegex . '';
                $regex .= '(?:' . $emailRegex . ')';
            }
            elseif ($item === 'phoneNumbers') {
                $regex .= '(?:\+?\d[\d\/ \-]{7,}\d)'; 
            }
            else {
                $regex .= '(?:' . $item . ')';
            }
        }
        $regex = '/' . $regex . '/iu';
        
        $result = preg_match_all($regex, $html, $matches, PREG_OFFSET_CAPTURE);
        
        if ($nonAsciiPregOffsetBug) {                                                                     //phpbugfix
            $asciiRegex = Sanitize::nonAscii($regex);                                                     //phpbugfix
            $asciiResult = preg_match_all($asciiRegex, $asciiHtml, $asciiMatches, PREG_OFFSET_CAPTURE);   //phpbugfix
        }                                                                                                 //phpbugfix
        
        if ($result === false) {
            throw new Exception(__(__FILE__, $regex));
        }
        elseif (
            $result === 0
            || $nonAsciiPregOffsetBug && $asciiResult !== $result //phpbugfix
        ) {
            return $html;
        }
        
        // check if there is some inline js or css code (script and style tags) 
        // in provided $html and avoid obfuscation of that code 
        if (!$nonAsciiPregOffsetBug) {
            $codeStartsCount = preg_match_all('/<(?:script|style) [^>]+>/iu', $html, $codeStartMatches, PREG_OFFSET_CAPTURE);
            $codeEndsCount = preg_match_all('/<\/(?:script|style)[^>a-z]*>/iu', $html, $codeEndMatches, PREG_OFFSET_CAPTURE);
        }
        else {                                                                                                                      //phpbugfix
            $codeStartsCount = preg_match_all('/<(?:script|style) [^>]+>/iu', $asciiHtml, $codeStartMatches, PREG_OFFSET_CAPTURE);  //phpbugfix
            $codeEndsCount = preg_match_all('/<\/(?:script|style)[^>a-z]*>/iu', $asciiHtml, $codeEndMatches, PREG_OFFSET_CAPTURE);  //phpbugfix
        }                                                                                                                           //phpbugfix
        // if starts and ends do not correspond then do not obfuscate the $html
        if ($codeStartsCount !== $codeEndsCount) {
            return $html;
        }
        
        // put the starts and end offsets into pairs like {startOffset} => {endOffset}
        $codeStartMatches = $codeStartMatches[0];
        $codeEndMatches = $codeEndMatches[0];
        $codeOffsets = array();
        foreach ($codeStartMatches as  $i => $codeStartMatch) {
            // if something wrong then do not obfuscate the $html
            if ($codeStartMatch[1] >= $codeEndMatches[$i][1]) {
                return $html;
            }
            $codeOffsets[$codeStartMatch[1]] = $codeEndMatches[$i][1];
        }
        // subject matches are processed form the end (to not break offsets) so 
        // prepare accordingly the code offsets
        $codeOffsets = array_reverse($codeOffsets, true);

        // use full matches for processing in reversed order
        $uniqid = uniqid();
        $jsTextFunctionName = '_' . $uniqid . 't';
        $jsAttributeFunctionName = '_' . $uniqid . 'a';
        $cssDisplayNoneClassName = '_' . $uniqid . 'h';
        $cssRevertClassName = '_' . $uniqid . 'r';
        $matches = array_reverse($matches[0]);
        if ($nonAsciiPregOffsetBug) {                           //phpbugfix
            $asciiMatches = array_reverse($asciiMatches[0]);    //phpbugfix
        }                                                       //phpbugfix
        $js = '';
        $idBase = '-so-' . $uniqid;
        $hasTextSubject = false;
        $hasAttributeSubject = false;
        foreach($matches as $i => $match) {
            // there is a bug in mb_substr() - if $length is provided as NULL then it is taken for 0.
            // so use some explicit length which will always suite
            $htmlLength = mb_strlen($html, $encoding);
            $subjectString = $match[0];
            $offset = $match[1];
            if ($nonAsciiPregOffsetBug) {                           //phpbugfix
                $offset = $asciiMatches[$i][1];                     //phpbugfix
            }                                                       //phpbugfix
            // do not obfuscate subject strings which are in js or css code
            foreach ($codeOffsets as $codeStartOffset => $codeEndOffset) {
                // remove code offsets which are behind the actual subject (processing optimization)
                if ($codeStartOffset > $offset) {
                    unset($codeOffsets[$codeStartOffset]);
                }
                // skip obfuscation if actual subject is placed between code start and end
                elseif ($offset < $codeEndOffset) {
                    continue 2;
                }
                // continue in subject obfuscation if code offsets are in front of actual subject
                else {
                    break;
                }
            }
            // obfuscate
            $htmlStart = mb_substr($html, 0, $offset, $encoding);
            $htmlEnd = mb_substr($html, $offset + mb_strlen($subjectString, $encoding), $htmlLength, $encoding);
            $id = $idBase . $i;
            // check if subject is placed in tag
            $inTag = preg_match('/<([a-z]+)[^<>]+$/iu', $htmlStart, $tagMatch);
            if ($inTag) {
                $tagName = strtolower($tagMatch[1]);
                // obfuscate only values in <a> tags
                if ($tagName !== 'a') {
                    continue;
                }
                // check if it is the tag attribute value
                $doubleQuotedAttribute = true;
                $inAttribute = preg_match('/([a-z0-9\-\_]+)="([^"]*)$/iu', $tagMatch[0], $attributeStartMatch);
                if (!$inAttribute) {
                    $inAttribute = preg_match('/([a-z0-9\-\_]+)=\'([^\']*)$/iu', $tagMatch[0], $attributeStartMatch);
                    // if the match is part of tag but not included in attribute value
                    // then do not touch it because otherwise HTML code will get broken
                    if (!$inAttribute) {
                        continue;
                    }
                    $doubleQuotedAttribute = false;
                }
                // get the attribute name and set $subjectString to whole attribute value 
                // (we will obfuscate the whole value contaning found subject string to make it easier)
                $attributeName = strtolower($attributeStartMatch[1]);
                // obfuscate only values of href attribute
                if ($attributeName !== 'href') {
                    continue;
                }
                $subjectString = $attributeStartMatch[2] . $subjectString;
                if ($doubleQuotedAttribute) {
                    $hasAttributeEnd = preg_match('/^([^"]*)"/iu', $htmlEnd, $attributeEndMatch);
                }
                else {
                    $hasAttributeEnd = preg_match('/^([^\']*)\'/iu', $htmlEnd, $attributeEndMatch);
                }
                // if no attribute end value then do not touch the $html to not break HTML code
                // (this should not happen normally)
                if (!$hasAttributeEnd) {
                    continue;
                }
                $subjectString .= $attributeEndMatch[1];
                // correct $htmlStart and $htmlEnd according to attribute value start and end
                $htmlStart = mb_substr($htmlStart, 0, -mb_strlen($attributeStartMatch[0], $encoding), $encoding);
                $htmlEnd = mb_substr($htmlEnd, mb_strlen($attributeEndMatch[0], $encoding), $htmlLength, $encoding);
                // add tag selector (as data{$id}) and call of js function
                $htmlStart .= ' data' . $id . ' ';
            }
            // subject is plain text node
            else {
                // add placeholder tag for subject
                $htmlStart .= '<span id="' . $id . '" style="display:none"></span>';
            }
            // set new $html value without $subjectString
            $html = $htmlStart . $htmlEnd;
            // get obfustated version of $subjectString
            $obfuscatedSubject = self::obfuscate($subjectString, $encoding);
            // add js de-obfuscation function call
            if ($inTag) {
                $js .= $jsAttributeFunctionName . "('" . $id . "'," . $obfuscatedSubject . ",'" . $attributeName . "');";
                $hasAttributeSubject = true;
            }
            else {
                $js .= $jsTextFunctionName . "('" . $id . "'," . $obfuscatedSubject . ");";
                $hasTextSubject = true;
            }
        }
        
        // create js functions
        $jsFunctions = '';
        // - add de-obfuscation js function which creates from obfuscated subject its original version
        $jsDeobfuscationFunctionName = '_' . $uniqid . 'd';
        $jsFunctions .= 
            "function " . $jsDeobfuscationFunctionName . "(b){" . //PHP_EOL .
                "var s='',e='',c,a=[9,8,7,6,5,4].length*10+[3,5,7,9].length;" . //PHP_EOL .
                "while(b.length){" . //PHP_EOL .
                    "c=b.shift();" . //PHP_EOL .
                    "if(c===''){" . //PHP_EOL .
                        "c=String.fromCharCode(a);" . //PHP_EOL .
                    "}" . //PHP_EOL .
                    "s+=c;" . //PHP_EOL .
                    "if (b.length) {" . //PHP_EOL .
                        "c=b.shift();" . //PHP_EOL .
                        "if(c===''){" . //PHP_EOL .
                            "c=String.fromCharCode(a);" . //PHP_EOL .
                        "}" . //PHP_EOL .
                        "e=c+e;" . //PHP_EOL .
                    "}" . //PHP_EOL .
                "}" . //PHP_EOL .
                "return s+e;" . //PHP_EOL .
            "}"// . PHP_EOL
                ;
        // - add js function to de-obfuscate text subjects
        if ($hasTextSubject) {
            $jsFunctions .= 
                "function " . $jsTextFunctionName . "(a,b){" . //PHP_EOL .
                    "b=" . $jsDeobfuscationFunctionName . "(b);" . //PHP_EOL .
                    "a=document.getElementById(a);" . //PHP_EOL .
                    "if(a===null){" . //PHP_EOL .
                        "return;" . //PHP_EOL .
                    "}" . //PHP_EOL .
                    "b=document.createTextNode(b);" . //PHP_EOL .
                    "a.parentNode.insertBefore(b, a);" . //PHP_EOL .
                    "a.parentNode.removeChild(a);" . //PHP_EOL .
                "}"// . PHP_EOL
                ;
        }
        // - add js function to de-obfuscate subjects in attribute values
        if ($hasAttributeSubject) {
            $jsFunctions .= 
                "function " . $jsAttributeFunctionName . "(a,b,c){" . //PHP_EOL .
                    "var e;" . //PHP_EOL .
                    "b=" . $jsDeobfuscationFunctionName . "(b);" . //PHP_EOL .
                    "a='data' + a;" . //PHP_EOL .
                    "e=document.querySelector('[' + a + ']');" . //PHP_EOL .
                    "if(e===null){" . //PHP_EOL .
                        "return;" . //PHP_EOL .
                    "}" . //PHP_EOL .
                    "if(typeof(e.setAttribute)==='function'){" . //PHP_EOL .
                        "e.setAttribute(c,b);" . //PHP_EOL .
                    "}" . //PHP_EOL .
                    "else{" . //PHP_EOL .
                        "e.setAttribute[c]=b;" . //PHP_EOL .
                    "}" . //PHP_EOL .
                    "if(typeof(e.removeAttribute)==='function'){" . //PHP_EOL .
                        "e.removeAttribute(a);" . //PHP_EOL .
                    "}" . //PHP_EOL .
                "}"// . PHP_EOL
                ;
        }
        // put defined functions together with their calls gathered here above
        $js = $jsFunctions . $js// . PHP_EOL
            ;
        // attach gnerated js to html if required
        if ($options['attachJs']) {
            // add js code to remove itself after everything is done
            $scriptId = $idBase . 's';
            $js .= "document.getElementById('" . $scriptId . "').parentNode.removeChild(document.getElementById('" . $scriptId . "'));";
            $js = '<script type="text/javascript" id="' . $scriptId . '">' . $js . '</script>';
            $html .= $js;
        }
        // add generated js to App::$js
        else {
            App::setJs($js);
        }
        // return obfuscated $html
        return $html;
    }
    
    /**
     * Obfuscates provided string, e.g. '<EMAIL>' is obfuscated to 
     * ["h","m","e","o","l","c","l",".","o","e",".","t","w","i","o","s","r","-","l","y","d","m",""].
     * 
     * NOTE: This method should be used in pair with js method Run.App.String.deobfuscate().
     * Its main aim is to obfuscate sensible strings (emails) in produced html 
     * and let the browser to deobfuscate them by js. Use Str::obfuscateHtml() 
     * to obfuscate specified strings in a bunch of hmtl code.
     * 
     * @param string $string String to obfuscate
     * @param string $encoding Optional. Encoding of $string. Defaults to 'UTF-8'.
     * 
     * @return string Obfuscated string
     */
    static public function obfuscate($string, $encoding = 'UTF-8') {
        $lastIndex = mb_strlen($string, $encoding) - 1;
        $obfuscatedString = array();
        for ($i = 0; $i <= $lastIndex; $i++) {
            $headIndex = $i;
            $tailIndex = $lastIndex - $i;
            if ($headIndex > $tailIndex) {
                break;
            }
            $char = mb_substr($string, $headIndex, 1, $encoding);
            if ($char === '@') {
                $char = '';
            }
            $obfuscatedString[] = $char;
            if ($headIndex === $tailIndex) {
                break;
            }
            $char = mb_substr($string, $tailIndex, 1, $encoding);
            if ($char === '@') {
                $char = '';
            }
            $obfuscatedString[] = $char;
        }
        $obfuscatedString = json_encode($obfuscatedString);
        return $obfuscatedString;
    }
    
    /**
     * Normalizes string to for nonstrict comparison with others string. E.g.:
     * 
     *      ' Toto je  ÚŽasný reťazec'
     * 
     * will be normalized to:
     * 
     *      'toto je úžasný reťazec'
     * 
     * @param string $string
     * 
     * @return string
     */
    static public function normalizeForNonstrictComparison($string) {
        return mb_strtolower(preg_replace('/\s+/', ' ', trim($string)), 'UTF-8');
    }
    
    /**
     * Formats part of string, which is enclosed in asterixes
     * 
     * @param string $string
     * @param string $partBetweenAsterixesFormat Format of string part between asterixes.
     *          Use insert ':...:' for part between asterixes. Defaults to ':...:',
     *          it means asterixes are removed from string.
     * 
     * @return string
     */
    public static function formatPartBetweenAsterixes($string, $partBetweenAsterixesFormat = ':...:') {
        $partBetweenAsterixesFormat = str_replace(':...:', '$1', $partBetweenAsterixesFormat);
        return preg_replace('/\*([^*]*)\*/', $partBetweenAsterixesFormat, $string);
    }

    /**
     * Translates given text into target language.
     * 
     * @param string|array $text Text to translate or an array of such texts.
     * @param string $targetLang Target lang code, e.g 'en'.
     * @param array $options Following are available:
     *      - 'sourceLang' (string) Defalts to App::$lang.
     * 
     * @return string|array Translated text or an array of translated texts. Texts
     *      are provided under the same keys as in input array.
     * 
     * @throws Exception_Str_TranslateFailure
     */
    public static function translate($text, $targetLang, $options = array()) {
        $options = array_merge(array(
            'sourceLang' => App::$lang,
        ), $options);
        
        App::loadLib('App', 'Google');
        $Google = new Google();
        $result = null;
        try {
            $result = $Google->translate($text, $targetLang, $options);
        } 
        catch (Throwable $e) {
            throw new Exception_Str_TranslateFailure(
                $e->getMessage(),
                $e->getCode(),
                $e
            );
        }
        return $result;
    }
}
class Exception_Str_TranslateFailure extends Exception {}
