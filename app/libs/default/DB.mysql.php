<?php
class DB {
    
    /**
     * Read only info about actual DB engine
     * 
     * Use DB::getPropertyEngine() method to get this property
     * 
     * @var string 
     */
    private static $engine = 'mysql';
    
    /**
     * Enclosure char used to enclose names in query, e.g. `my_table`.`my_col`
     * 
     * @var string 
     */
    private static $nameEnclosure = '`';
    
    /**
     * Enclosure char used to enclose values in query, e.g. `my_col` = 'abc'
     * 
     * @var string 
     */
    private static $valueEnclosure = '\'';
    
    /**
     * Link to connected DB
     * 
     * @var resource 
     */
    private static $link = false;
    
    /**
     * Name of config used to connect to DB
     * 
     * @var string
     */
    private static $configName = null;
    
    /**
     * Name of conneted database
     * 
     * @var string
     */
    private static $databaseName = null;
    
    /**
     * If TRUE then a query seed is added to each db query to avoid query caching
     * and see real query execution time
     * 
     * Use DB::setPropertyAvoidCache() to set this property
     * 
     * @var int interpreted as a boolean value. The int value itself is incremented on
     * each new query and used as a seed appendix to create easily an unique seed for
     * each query.
     */
    private static $avoidCache = false;
    
    /**
     * Use as a possible value for DB::$log
     */
    const LOG_WITH_BACKTRACE = 1;
    
    /**
     * If TRUE then all executed sql queries are logged together with execution 
     * microtimes. If set to DB::LOG_WITH_BACKTRACE then each log contains also backtrace.
     * ATTENTION: Do not use DB::LOG_WITH_BACKTRACE for production!
     * 
     * @var bool|int
     */
    public static $log = false;
    
    /**
     * Log of sql queries like:
     * 
     *      array(
     *          0 => array(
     *              'query' => 'SELECT ...'
     *              'error' => FALSE, 
     *              'microtime' => '2.3', // miliseconds
     *              'backtrace' => array(...),
     *          )
     *          ...
     *      )
     * 
     * Sql log is created if DB::log is TRUE
     * 
     * @var array
     */
    private static $sqlLog = array();
    
    /**
     * Contains name of started toplevel transaction or FALSE if no transaction is started.
     * 
     * The transaction name is set by method DB::startTransaction().
     * The FALSE is set by methods DB::commitTransaction() and DB::rollbackTransaction().
     *  
     * @var bool|string 
     */
    private static $inTransaction = false;
    
    /**
     * List of actual transaction savepoints, see https://dev.mysql.com/doc/refman/5.6/en/savepoint.html . 
     * 
     * It contains pairs {name} => true. Savepoints are created by nested calls 
     * of DB::startTransaction(). For details see implementation of DB::startTransaction(), 
     * DB::commitTransaction() and DB::rollbackTransaction()
     * 
     * @var array 
     */
    private static $transactionSavePoints = array();
    
    /**
     * Name of table keeping tables reserved by all processes.
     * It's schema is defined in DB::$reservationTableSchema
     * 
     * ATTENTION: MyISAM engine must be used for case that tables are reserved inside 
     * of a transaction to ensure that the table recordings are saved immediatelly 
     * and not only after transaction commit
     * 
     * @var string 
     */
    private static $reservationTable = '_run_reserved_tables';
    
    /**
     * Schema of DB::$reservationTable to enable dynamic creation of the table
     *
     * @var string 
     */
    private static $reservationTableSchema = array(
        'table' => array('type' => 'varchar', 'index' => 'primary'),
        'reservation_name' => array('type' => 'varchar', 'default' => null, 'comment' => 'Only for info purposes'),
        'created' => array('type' => 'datetime', 'default' => null),
    );
    
    /**
     * List of tables reserved by actual process. It contains pairs 'table_name' => 'reservation_name',
     * like:
     * 
     *      array(
     *          'my_table_01' => 'reservation_name_01,
     *          'my_table_02' => 'reservation_name_01,
     *          'my_table_03' => 'reservation_name_02,
     *          ...,
     *      )
     * 
     * @var array 
     */
    private static $reservedTables = array();
    
    /**
     * Max time interval (in seconds) a table can be reserved. After this interval
     * any table reservations are ignored (removed). This serves to avoid deadlocks 
     * caused by orphaned table reservations caused by DB server outage. If empty 
     * then table reservations cleaning is not done.
     * 
     * @var NULL|int 
     */
    private static $tableReservationTimeLimit = null;
        
    /**
     * Has been already registered the shutdown handler to unreserve tables
     * registered by actual process?
     * 
     * @var bool 
     */
    private static $unreserveTablesShutdownHandlerRegistered = false;
    
    /**
     * Has been already existence of reservation table ensured?
     * 
     * @var bool 
     */
    private static $reservationTableEnsured = false;
        
    /**
     * Gets property DB::$engine
     * 
     * @return string
     */
    public static function getPropertyEngine() {
        return self::$engine;
    }
    
    /**
     * Gets property DB::$nameEnclosure
     * 
     * @return string
     */
    public static function getPropertyNameEnclosure() {
        return self::$nameEnclosure;
    }
    
    /**
     * Gets property DB::$valueEnclosure
     * 
     * @return string
     */
    public static function getPropertyValueEnclosure() {
        return self::$valueEnclosure;
    }
    
    /**
     * Gets property DB::$databaseName
     * 
     * @return string
     */
    public static function getPropertyDatabaseName() {
        return self::$databaseName;
    }
    
    /**
     * Sets DB::$avoidCache flag
     * @param bool $avoid If TRUE then the cache is turned off. If false the cache
     * is turned on.
     */
    public static function setPropertyAvoidCache($avoid) {
        if ($avoid) {
            self::$avoidCache = (int)(10000 * microtime(true));
        }
        else {
            self::$avoidCache = 0;
        }
    }
    
    /**
     * Adds "no cache seed" to conditions of MySql WHERE if cache is turned off by DB::setPropertyAvoidCache()
     *
     * @param string $conditionsSql conditions of MySql WHERE clause to add no cache seed. 
     * @return string conditions of WHERE clause with added no cache seed
     */
    protected static function addAvoidCacheSeed($conditionsSql) {
        if (self::$avoidCache) {
            self::$avoidCache++;
            if ($conditionsSql) {
                $avoidCacheSeed = ' OR 1=' . self::$avoidCache;
            }
            else {
                $avoidCacheSeed = self::$avoidCache . '=' . self::$avoidCache;
            }
            return $conditionsSql . $avoidCacheSeed;
        }
        else {
            return $conditionsSql;
        }
    }
    
    /**
     * Gets $sqlLog array
     * 
     * @param bool $compiled Optional. If TRUE the the SLQ log is not returned in 
     *      its raw form but its items are compiled into comprehensive strings.
     *      Defaults to FALSE.
     * 
     * @return array
     */
    public static function getSqlLog($compiled = false) {
        if (!$compiled) {
            return self::$sqlLog;
        }
        $compiledSqlLog = array();
        $totalTime = 0;
        foreach(self::$sqlLog as $i => $log) {
            $i++;
            $time = round($log['microtime'], 2);
            $totalTime += $time;
            $error = '';
            if (!empty($log['error'])) {
                $error = ' (ERROR) ';
            }
            $compiledLog = $i . '] ' . $error . $time . ' ms / ' . $totalTime . ' ms - ' . $log['query'];
            if (!empty($log['backtrace'])) {
                $compiledLog .= PHP_EOL . implode(PHP_EOL, $log['backtrace']);
            }
            $compiledSqlLog[] = $compiledLog;
        }
        return $compiledSqlLog;
    }
    
    /**
     * Debugs sql log
     * 
     * @param string $message
     */
    public static function debugSqlLog($message = '') {
        if (
            !self::$log
            && !self::$sqlLog
        ) {
            return;
        }
        
        App::debug(self::getSqlLog(true), $message, array(
            'type' => 'sqllog',
            'showUrl' => true,
            'backtraceLevel' => 1, 
        )); 
    }
                
    /**
     * Connects to DB using specified $config
     * 
     * @param array $config Database config array like:
     * 
     *      array(
     *          'host' => 'localhost',
     *          'username' => 'my_user',
     *          'password' => 'my_pwd',
     *          'database' => 'my_db_name',
     *          'encoding' => 'utf8',
     *          ['port' => ...,]
     *          ['socket' => ...,]
     *          ['install' => true/false] //should be set TRUE on very first connection to DB to make some instalation processing in DB 
     *          ['tableReservationTimeLimit' => null/{integer}] // Number of seconds to delete reserved tables older that. If empty then this "auto cleaning" is not done
     *      )
     *      
     * @return resource DB link
     */
    public static function connect($config) {
        // connect only if connection was not established yet
        if (!self::$link) {
            // validate connection params
            if (
                !isset($config['host']) 
                || !isset($config['username']) 
                || !isset($config['password'])
            ) {
                $configType = ON_LOCALHOST ? __e(__FILE__, 'local (development) DB') : __e(__FILE__, 'remote (production) DB');
                $message = __e(__FILE__, 'Connecting to %s...', $configType);
                $message .= '<br>' . __e(__FILE__, 'SQL CONNECT ERROR: Either host or username or password is not specified in database connection config');
                echo $message;
                throw new Exception($message);
            }
            // connect to db
            $port = isset($config['port']) ? $config['port'] : null;
            $socket = isset($config['socket']) ? $config['socket'] : null;
            self::$link = mysqli_connect($config['host'], $config['username'], $config['password'], $port, $socket);
            if (!self::$link)  {
                $configType = ON_LOCALHOST ? __e(__FILE__, 'local (development) DB') : __e(__FILE__, 'remote (production) DB');
                $message = __e(__FILE__, 'Connecting to %s...', $configType);
                $message .= '<br>' . __e(
                    __FILE__,
                    'SQL CONNECT ERROR: Could not establish connection to the database. Please check host, username and password specified in database connection config'
                );
                echo $message;
                throw new Exception($message);
            }
            // validate 'database' param
            if (!isset($config['database'])) {
                $configType = ON_LOCALHOST ? __e(__FILE__, 'local (development) DB') : __e(__FILE__, 'remote (production) DB');
                $message = __e(__FILE__, 'Connecting to %s...', $configType);
                $message .= '<br>' . __e(__FILE__, 'SQL CONNECT ERROR: No database is specified in database connection config');
                echo $message;
                throw new Exception($message);
            }
            self::$databaseName = $config['database'];
            // select db
            if (!mysqli_select_db(self::$link, $config['database'])) {
                $configType = ON_LOCALHOST ? __e(__FILE__, 'local (development) DB') : __e(__FILE__, 'remote (production) DB');
                $message = __e(__FILE__, 'Connecting to %s...', $configType);
                $message .= '<br>' . __e(
                    __FILE__,
                    'SQL CONNECT ERROR: Could not select the database. Please check database specified in database connection config'
                );
                echo $message;
                throw new Exception($message);
            }
            // set charset
            if (isset($config['encoding'])) {
                $config['encoding'] = self::escape($config['encoding']);
                mysqli_query(self::$link, 'SET NAMES \'' . $config['encoding'] . '\'');
                mysqli_query(self::$link, 'SET CHARACTER SET ' . $config['encoding']);		
                mysqli_query(self::$link, 'SET character_set_results = ' . $config['encoding']);
                mysqli_query(self::$link, 'SET character_set_connection = ' . $config['encoding']);
                mysqli_query(self::$link, 'SET character_set_client = ' . $config['encoding']);
            }
            
            // do installation processing
            if (!empty($config['install'])) {
                // ensure existence of reservation table
                // NOTE: Make it here because it is not possible to do it in lazy way in DB::reserveTables().
                // The DB::reserveTables() can be called in transaction and the tables creation 
                // autocommits transactions
                self::ensureReservationTable();
            }
            
            // set table resevation time limit
            if (
                !empty($config['tableReservationTimeLimit'])
                && (int)$config['tableReservationTimeLimit'] > 0
            ) {
                self::$tableReservationTimeLimit = (int)$config['tableReservationTimeLimit'];
            }
        }
        
        return self::$link;
    }
    
    /**
     * Escapes special SQL characters in the $string
     * 
     * @param string $string
     * @return string 
     */
    public static function escape($string) {
        // MAGIC_QUOTES is used because mq are undone is bootstrap and this constant
        // keeps actual state regardless to get_magic_quotes_gpc()
        if (MAGIC_QUOTES) {
            return $string;
            //?// $string = stripslashes($string);
        }
        return mysqli_real_escape_string(self::$link, $string);
    }    
    
    /**
     * Encloses names in most common SQL expressions, e.g. '`table`.field = 1'
     * It is expected that SQL expression starts with table or/and field name. If both are present
     * then they are connected by dot. These are followed by possible operator and operand.
     * All items are separated by spaces. Only these table or/and field name are enclosed here.
     * If there is some table or/and field name in operand too, then these must be 
     * enclosed manually if necessary.
     * 
     * Enclosing makes query creation approx. 2 times slower, but still 
     * its is enough efficient. Construction of 1000 complex select queries 
     * takes ~142ms without enclosing and ~297ms with enclosing.
     * 
     * @param string $string Sql expression starting with table or field name separated by space from 
     *      the rest of statement
     * 
     * @return string Sql expression with correctly enclosed names
     * 
     */
    public static function encloseName($string) {
        if (!empty($string)) {
            // explode '`table`.field AS `Table.field`' to '`table`.field', 'AS', '`Table.field`'
            $spaceParts = explode(' ', $string);
            // take '`table`.field'
            $backtickPart = array_shift($spaceParts);
            // remove existing backticks: 'table.field'
            $backtickPart = str_replace('`', '', $backtickPart);
            // explode it to 'table', 'field'
            $dotParts = explode('.', $backtickPart);
            // apply backticks: '`table`.`field`'
//            $backtickPart = '`' . implode('`.`', $dotParts) . '`'; //rb4lb//
            $backtickPart = '`' . array_pop($dotParts) . '`';
            if (!empty($dotParts)) {
                $backtickPart = '`' . implode('.', $dotParts) . '`.' . $backtickPart;
            }
            // reconstruct the input expession: '`table`.`field` AS `Table.field`'
            $string = $backtickPart;
            if (!empty($spaceParts)) {
                // check if there is something after AS or after an operator
                if (
                    // if there is something after operator
                    !empty($spaceParts[1])
                    // ...but nothing more 
                    && !isset($spaceParts[2])  
                    //  and it is not a number
                    && !is_numeric($spaceParts[1])
                ) {
                    // check the type of operator and for possible function or 
                    // expression enclosed by braces
                    $operator = strtoupper($spaceParts[0]);
                    if (
                        $operator === 'IS' 
                        || $operator === 'IN'
                        || substr($spaceParts[1], -1) === ')'
                    ) {
                        // do nothing
                    }
                    elseif ($operator == 'AS') {
                        $spaceParts[1] = self::encloseAlias($spaceParts[1]);
                    }
                    // if one of =, <>, !=, <, >, <=, >=
                    // but not in front of an mysql string value (so as it is neither
                    // number - see above - it can be only name of another column
                    elseif($spaceParts[1][0] != '\'') {
                        $spaceParts[1] = self::encloseName($spaceParts[1]);
                    }
                }
                // reconstruct the expression
                $string .= ' ' . implode(' ', $spaceParts);
            }
        }
        return $string;
    }
    
    /**
     * Encloses alias. Alias is taken as it is, wihout any parsing.
     * The only thing done is the removing of any existing enclosures
     * before adding the new ones.
     * 
     * @param string $alias
     * 
     * @return string 
     */
    public static function encloseAlias($alias) {
        if (!empty($alias)) {
            $alias = '`' . str_replace('`', '', $alias) . '`';
        }
        return $alias;
    }
    
    /**
     * Normalizes value of $options['literals'] option used in DB::select(), ::insert() and many
     * other methods of DB class
     * 
     * @param array $literals
     * 
     * @return array 
     */
    public static function normalizeLiterals($literals) {
        $defaults = array(
            'fields' => array(),    // field names if find conditions or in insert/update data
            'data' => array(),      // data values in in insert/update data
            'joins' => array(),
            'conditions' => array(),
            'group' => array(),
            'having' => array(),
            'order' => array(),
        );
        $literals = array_merge($defaults, (array)$literals);
        foreach($literals as &$literal) {
            if ($literal !== true) {
                $literal = (array)$literal;
            }
        }
        return $literals;
    }
    
    /**
     * Merges value of two $options['literals'] arrays taking into account possible
     * TRUE values.
     * 
     * @param array $literalsA
     * @param array $literalsB
     * @return array 
     */
    public static function mergeLiterals($literalsA, $literalsB) {
        $literalsA = (array)$literalsA;
        $literalsB = (array)$literalsB;
        foreach($literalsB as $key => $value) {
            if (
                !isset($literalsA[$key])
                || $literalsB[$key] === true
            ) {
                $literalsA[$key] = $literalsB[$key];
            }
            elseif($literalsA[$key] !== true) {
                $literalsA[$key] = array_merge((array)$literalsA[$key], (array)$literalsB[$key]);
            }
        }
        return $literalsA;
    }
    
    /**
     * Nests provided conditions one level deeper, it means conditions are enclosed 
     * by array(). Only nonempty conditions are nested. This can be used to build 
     * conditions merging/adding them during the processing on different places:
     * 
     *      $options['conditions'] = array(
     *          'name' => 'test',
     *          'OR',
     *          'id' => '5',
     *      );
     * 
     *      // ...some processing...
     * 
     *      $options['conditions'] = DB::nestConditions($options['conditions']);
     *      $options['conditions']['active'] = 1;
     * 
     *      // ...some more processing...
     * 
     *      $options['conditions'] = DB::nestConditions($options['conditions']);
     *      $options['conditions'][] = array(
     *          'slug' => 'my-slug',
     *          'OR',
     *          'id' => '2',
     *      )
     * 
     * And you will get:
     * 
     *      $options['conditions'] = array(
     *          array(
     *              array(
     *                  'name' => 'test',
     *                  'OR',
     *                  'id' => '5',
     *              )
     *              'active' => 1,
     *          ),
     *          array(
     *              'slug' => 'my-slug',
     *              'OR',
     *              'id' => '2',
     *          )
     *      )
     * 
     * @param array $conditions Conditions to nest
     * 
     * @return array Nested conditions
     */
    public static function nestConditions($conditions) {
        $conditions = (array)$conditions;
        if (!empty($conditions)) {
            $conditions = array($conditions);
        }
        return $conditions;
    }
        
    /**
     * Finds out data fields which can be validated, saved, updated, ... according 
     * to $options['allowFields'] and $options['avoidFields']
     * 
     * @param array $data Asociative array with field names used as keys
     * @param array $options Following are available:
     *      - 'allowFields' (string|array) Single field or list of fields to be accepted.
     *      - 'avoidFields' (string|array) Single field or list of fields to be avoided.
     * 
     * @return array List of fields to be processed (saved, updated, validated, ...)
     */
    public static function getDataFields($data, $options) {
        $fields = array_keys((array)$data);
        if (!empty($options['allowFields'])) {
            $fields = array_intersect($fields, (array)$options['allowFields']);
        }
        if (!empty($options['avoidFields'])) {
            $fields = array_diff($fields, (array)$options['avoidFields']);
        }
        return array_unique($fields);
    }
    
    /**
     * Resolves indexes from given table fields definitions and merges them into 
     * given existing indexes
     * 
     * @param array $fields Array of fields definitions containing pairs fieldName => fieldsOptions.
     *      In fieldOptions there can be defined an index (under 'index' key) for given filedName. 
     * @param array $indexes Optional. Array of existing indexes (e.g. defined  
     *      in table $options['indexes']) like:
     *          array(
     *              array(
     *                  'type' => 'index', 
     *                  'fields' => array('fieldName01', 'fieldName02'), 
     *                  'name' => 'MyIndex01' // optional
     *              ),
     *              array(
     *                  'type' => 'primary', 
     *                  'fields' => array('id'), 
     *                  'name' => 'PRIMARY' // optional
     *              ),
     *              ...
     *          );
     * 
     * @return array Indexes array with merged indexes from fields definitions. 
     *      Original indexes in field definitions are preserved.
     * 
     * NOTE: This methos does not validate anything (duplicit indexes, concurent indexes,...)!
     * It's up to user to know what he is doing.
     */
    public static function getIndexes($fields, $indexes = array()) {
        foreach ($fields as $fieldName => $fieldOptions) {
            if (empty($fieldOptions['index'])) {
                continue;
            }
            $indexes[] = array(
                'type' => $fieldOptions['index'], 
                'fields' => $fieldName, 
            );
        }
        return $indexes;
    }
    
    /**
     * Finds out the name(s) of table(s) or fied(s) to be used in or returned by SELECT query
     * according to defined table(s) / field(s) alias. E.g. for array input:
     * 
     *      array(
     *          'my_table.my_field1', 
     *          '`my_table`.`my_field2`', 
     *          '`my_field3`', 
     *          '`my_field4` AS myField4', 
     *      )
     * 
     * the output will be:
     * 
     *      array(
     *          'my_table.my_field1'        => 'my_field1', 
     *          '`my_table`.`my_field2`'    => 'my_field2', 
     *          '`my_field3`'               => 'my_field3', 
     *          '`my_field4` AS myField4'   => 'myField4', 
     *      )
     * 
     * The same for table aliases.
     *
     * @param mixed $names Single table / field name (string) or
     *      array of table / fied names
     * @params array $options Following are available:
     *      - 'qualify' (bool|array) This can be used if there are field(s) in $names.
     *          If TRUE then aliases are created for all qualified fields. Means, 
     *          field `my_table`.`field_1` is aliased as `my_table`.`field_1` AS `my_table.field_1`. 
     *          If an array of qualifiers when only fields with given qualifiers 
     *          will be aliased. This can be used to inflate the resulting array. 
     *          Fields which are already aliased are skipped. Defaults to FALSE.
     * 
     * @return mixed String if $names is string. Array if $names is array.
     */
    public static function getName($names, $options = array()) {
        $options = array_merge(array(
            'qualify' => false,
        ), $options);
        $stringInput = false;
        if (!is_array($names)) {
            $stringInput = true;
            $names = (array)$names;
        }
        
        $resolvedNames = array();
        $qualifyIsArray = is_array($options['qualify']);
        foreach ($names as $name) {
            // aliased item
            if (
                ($pos = strpos($name, ' as ')) !== false
                || ($pos = strpos($name, ' AS ')) !== false
            ) {
                $resolvedName = trim(str_replace('`', '', substr($name, $pos + 4)));
            }
            // plain item
            else {
                $nameParts = explode('.', $name);
                $resolvedName = trim(str_replace('`', '', array_pop($nameParts)));
                if (
                    $options['qualify']
                    && (
                        ($qualifier = trim(str_replace('`', '', implode('.', $nameParts))))
                        && (
                            !$qualifyIsArray
                            || in_array($qualifier, $options['qualify'], true)   
                        )
                    )
                ) {
                    $resolvedName = $qualifier . '.' . $resolvedName;
                }
            }
            $resolvedNames[$name] = $resolvedName;
        }
        
        if ($stringInput) {
            return array_shift($resolvedNames);
        }
        return $resolvedNames;
    }
    
    /**
     * Executes SQL query.
     * 
     * ATTENTION: If Exception_DB_RecoverableDeadlock is thrown then the easiest way
     * to resolve the deadlock is to rollback and repeat the whole transaction again.
     *
     * @param string|array $query SQL query string or array or SQL query partial
     *          strings which can be joined using single space (' ') to get SQL query 
     *          string.
     * 
     * @param bool $resource Optional. If TRUE then the raw resource is returned.
     *      If FALSE then array is fetched from resource. Defaults to FALSE.
     * 
     * @return bool|array|resource Boolean TRUE on success of INSERT, UPDATE, DELETE, 
     *      DROP, etc. Array or resource on success of SELECT, SHOW, DESCRIBE, EXPLAIN 
     *      and other statements returning resultset. ATTENTION: This method does not
     *      return boolean FALSE.
     * 
     * @throws Exception_DB_RecoverableDeadlock
     * @throws Exception_DB_QueryError on mysql error. The exception message is set to mysql error
     *      message and the exception code is set mysql error number.
     */
    public static function query($query, $resource = false) {
        if (is_array($query)) {
            $query = implode(' ', $query);
        }
        if (self::$log) {
            $t = microtime(true);
            $result = mysqli_query(self::$link, $query);
            $backtrace = array();
            if (self::$log === self::LOG_WITH_BACKTRACE) {
                $backtrace = App::getBacktrace();
            }
            self::$sqlLog[] = array(
                'query' => $query,
                'error' => ($result === false),
                'microtime' => 1000 * (microtime(true) - $t),
                'backtrace' => $backtrace,
            );
        }
        else {
            $result = mysqli_query(self::$link, $query);
        }
        if ($result === false) {
            $errno = mysqli_errno(self::$link);
            if (
                // find all "try restarting transaction" on https://dev.mysql.com/doc/refman/5.5/en/error-messages-server.html
                // - "Lock wait timeout exceeded; try restarting transaction"
                $errno === 1205
                // - "Deadlock found when trying to get lock; try restarting transaction"
                || $errno === 1213
            ) {
                $error = mysqli_error(self::$link);
                if (($result = mysqli_query(self::$link, 'SHOW ENGINE INNODB STATUS;'))) {
                    $error = rtrim($error, '. ') . '. INNODB STATUS:' . 
                        PHP_EOL . 
                        reset(mysqli_fetch_assoc($result));
                }
                throw new Exception_DB_RecoverableDeadlock($error, $errno);
            }
            throw new Exception_DB_QueryError(mysqli_error(self::$link), $errno);
        }
        if (self::isResource($result) && !$resource) {
            $records = array();
            while($row = mysqli_fetch_assoc($result)){
                $records[] = $row;
            }   
            $result = $records;
        }
        return $result;
    }
    
    /**
     * Checks whether a variable is a resource (btw = instanceof mysqli_result)
     * 
     * @param mixed $var
     * 
     * @return bool Returns TRUE if var is a resource, FALSE otherwise.
     */
    public static function isResource($var) {
        return $var instanceof mysqli_result;
    }
    
    /**
     * Fetch a result row as an associative array
     * 
     * @param resource $resource
     * @return array
     */
    public static function fetchArray($resource) {
        return mysqli_fetch_assoc($resource);
    }
    
    /**
     * Gets the number of rows in a result
     * 
     * @param resource $resource
        * @return integer
     */
    public static function getNumberOfRows($resource) {
        return mysqli_num_rows($resource);
    }
    
    /**
     * Adjusts the result pointer to an arbitrary $row in the $resource
     * 
     * @param resource $resource
     * @param int $row Must be between zero and the total number of rows minus one
     * 
     * @return bool Returns TRUE on success or FALSE on failure.
     */
    public static function seekRow($resource, $row) {
        return mysqli_data_seek($resource, $row);
    }
    
    /**
     * Starts transaction with provided $name.
     * 
     * NOTE: The default SQL behaviour is that each new transaction start causes
     * an autocommit of previously started transaction. This is not the case if
     * using this method. This method allows nested transactions and they are implemented 
     * by use of SAVEPOINT (https://dev.mysql.com/doc/refman/5.6/en/savepoint.html).
     * 
     * NOTE: Transaction is implicitly ended by data definition SQL statements, e.g.:
     * CREATE TABLE, CREATE DATABASE DROP DATABASE, TRUNCATE TABLE, ALTER TABLE, 
     * CREATE INDEX, DROP INDEX, DROP TABLE, RENAME TABLE, ...
     * That is why DB::createTable(), DB::dropTables(), DB::truncateTables(), ...
     * are not allowed inside transaction (will throw Exception).
     * For detailed list of statements see http://dev.mysql.com/doc/refman/5.0/en/implicit-commit.html.
     * 
     * @param string $name Transaction name used to pair transaction start with
     *          corresponding transaction commit and so allow nested transactions 
     *          creation. NOTE: To make this work transaction names must be unique. To assure this 
     *          use following pattern to create name: {className}::{methodName}(). 
     *          E.g. if you use transaction in Model::addTreeNode() then the transaction
     *          name is 'Model::addTreeNode()'. Defaults to TRUE (no name is specified, 
     *          but transaction is started).
     * 
     * @throws Exception 
     *          - on empty value provided as transaction name
     *          - if duplicit transaction is started
     */
    public static function startTransaction($name) {
        if (empty($name)) {
            throw new Exception('Transaction name must not be an empty value');
        }
        if ($name === self::$inTransaction) {
            throw new Exception(
                __e(__FILE__, 'Database transaction "%s" has been already started', $name)
            ); 
        }
        // if not in transaction then start one
        if (!self::$inTransaction) {
            self::query('START TRANSACTION;');
            self::$inTransaction = $name;
            return;
        }
        // if in transaction then start a savepoint
        if (isset(self::$transactionSavePoints[$name])) {
            throw new Exception(
                __e(
                    __FILE__, 
                    'Database transaction savepoint "%s" has been already started', 
                    $name
                )
            ); 
        }
        self::query('SAVEPOINT ' . self::encloseAlias($name) . ';');
        self::$transactionSavePoints[$name] = true;
    }
    
    /**
     * Commits the started transaction with corresponding name 
     * 
     * @param string $name Transaction name used to pair transaction start with 
     *          corresponding transaction commit and so allow nested transactions creation. 
     *          NOTE: To make this work transaction names must be unique. To assure this 
     *          use following pattern to create name: {className}::{methodName}(). 
     *          E.g. if you use transaction in Model::addTreeNode() then the transaction
     *          name is 'Model::addTreeNode()'. Defaults to TRUE (no name is specified, 
     *          but transaction is started).
     * @param bool $exceptionOnNoTransaction Optional. If TRUE then an exception is throw
     *          if no transaction has been started and you are trying to commit it. 
     *          If FALSE then nothing happens in such a case. Defaults to FALSE.
     * 
     * @throws Exception if commit is launched without starting any transaction and 
     *      $exceptionOnNoTransaction is TRUE.
     */
    public static function commitTransaction($name, $exceptionOnNoTransaction = false) {
        if (!self::$inTransaction) {
            $message = __e(
                __FILE__, 
                'Nothing to commit. No database transaction has be started'
            );
            if ($exceptionOnNoTransaction) {
                throw new Exception($message); 
            }
            App::logError($message, array('email' => true));
            return;
        }
        // if transaction name then commit it
        if ($name === self::$inTransaction) {
            self::query('COMMIT;');
            self::$inTransaction = false;
            self::$transactionSavePoints = array();
            return;
        }
        // if savepoint name then release it
        if (isset(self::$transactionSavePoints[$name])) {
            self::query('RELEASE SAVEPOINT '  . self::encloseAlias($name) . ';');
            unset(self::$transactionSavePoints[$name]);
            return;
        }
        // otherwise error
        $message = __e(
            __FILE__, 
            'Nothing to commit. No database transaction nor savepoint with name "%s" has be started', 
            $name
        );
        if ($exceptionOnNoTransaction) {
            throw new Exception($message); 
        }
        App::logError($message, array('email' => true));
    }
    
    /**
     * Rolls back any started transaction
     * 
     * @param string $name Transaction name used to pair transaction start with 
     *          corresponding transaction rollback and so allow nested transactions creation. 
     *          NOTE: To make this work transaction names must be unique. To assure this 
     *          use following pattern to create name: {className}::{methodName}(). 
     *          E.g. if you use transaction in Model::addTreeNode() then the transaction
     *          name is 'Model::addTreeNode()'. Defaults to TRUE (no name is specified, 
     *          but transaction is started).
     * @param bool $exceptionOnNoTransaction Optional. If TRUE then an exception is throw
     *          if no transaction has been started and you are trying to roll bacc it. 
     *          If FALSE then nothing happens in such a case. Defaults to FALSE.
     * 
     * @throws Exception if rollback is launched without starting any transaction and 
     *      $exceptionOnNoTransaction is TRUE.
     */
    public static function rollbackTransaction($name, $exceptionOnNoTransaction = false) {
        if (!self::$inTransaction) {
            $message = __e(
                __FILE__, 
                'Nothing to rollback. No database transaction has be started'
            );
            if ($exceptionOnNoTransaction) {
                throw new Exception($message); 
            }
            App::logError($message, array('email' => true));
            return;
        }
        // if transaction name then commit it
        if ($name === self::$inTransaction) {
            self::query('ROLLBACK;');
            self::$inTransaction = false;
            self::$transactionSavePoints = array();
            return;
        }
        // if savepoint name then release it
        if (isset(self::$transactionSavePoints[$name])) {
            self::query('ROLLBACK TO SAVEPOINT '  . self::encloseAlias($name) . ';');
            unset(self::$transactionSavePoints[$name]);
            return;
        }
        // otherwise error
        $message = __e(
            __FILE__, 
            'Nothing to rollback. No database transaction nor savepoint with name "%s" has be started', 
            $name
        );
        if ($exceptionOnNoTransaction) {
            throw new Exception($message); 
        }
        App::logError($message, array('email' => true));
    }
    
    /**
     * Ensures existence of reservation table used by methods DB::reserveTables() and DB::unreserveTables()
     */
    protected static function ensureReservationTable() {
        if (!self::$reservationTableEnsured) {
            self::createTable(
                self::$reservationTable, 
                self::$reservationTableSchema,
                array(
                    // use MyISAM engine for case that tables are reserved inside 
                    // of a transaction to ensure that the table recordings are
                    // saved immediatelly and not only after transaction commit
                    'engine' => 'MyISAM',
                )
            );
            self::$reservationTableEnsured = true;
        }
    }
    
    /**
     * Reserves provided tables for writing for actual process (= actual DB session/connection).
     * Under writing are meant DB::insert(), DB::update(), DB::delete(), DB::truncateTables() and DB::dropTables()
     * 
     * @param string $name Reservation name used to pair table reservation with corresponding 
     *          table unreservation and so allow nested reservations of the same table 
     *          by the actual process. 
     *          NOTE: To make this work reservation names must be unique. To assure this 
     *          use following pattern to create name: {className}::{methodName}(). 
     *          E.g. if you use reservation in Model::addTreeNode() then the reservation
     *          name is 'Model::addTreeNode()'.
     * @param string|array $tables Single table name or an array of such names
     * @param array $options Optional. Following options can be specified:
     *      - 'tries' (int) Number of tries to reserve the tables in case that it does 
     *          not succeed on first attempt. Defaults to 100, means 100 attempts are made.
     *      - 'retryTime' (int) Number of miliseconds to wait between reservations attempts.
     *          Defaults to 200.
     *      - 'exceptionOnDuplicitReservation' (bool) If TRUE then the method throws 
     *          exception in case that the table has been already reserved by actual process.
     *          Defaults to FALSE.
     * 
     * @throws Exception_DB_TablesDuplicitReservation if $options['exceptionOnDuplicitReservation'] is TRUE and
     *      actual process makes nested reservations of the same table
     * @throws Exception_DB_TablesReservationFailure if the reservation of provided tables
     *      fails
     */
    public static function reserveTables($name, $tables, $options = array()) {
        $defaults = array(
            'tries' => 100,
            'retryTime' => 200,
            'exceptionOnDuplicitReservation' => false,
        );
        $options = array_merge($defaults, $options);
        // normalize tries
        if (
            !Validate::intNumber($options['tries'])
            || $options['tries'] < 1
        ) {
            $options['tries'] = 1;
        }
        // normalize retryTime
        $options['retryTime'] = (int)$options['retryTime'] * 1000;
        
        // register shutdown handler to ensure unreservation of all unreserved 
        // tables which has been reserved by actual process and so avoid deadlock
        if (!self::$unreserveTablesShutdownHandlerRegistered) {
            register_shutdown_function(array('DB', 'unreserveTables'));
            self::$unreserveTablesShutdownHandlerRegistered = true;
        }
        
        // prepare tables data and check the tables reserved by actual process
        $data = array();
        $tablesToReserve = array();
        $tables = (array)$tables;
        foreach ($tables as $table) {
            if (
                empty(self::$reservedTables[$table])
                // avoid duplicit reservation of the same table
                && empty($tablesToReserve[$table])
            ) {
                $data[] = array(
                    'table' => $table,
                    'reservation_name' => $name,
                    'created' => null,
                );
                $tablesToReserve[$table] = $name;
            }
            elseif ($options['exceptionOnDuplicitReservation']) {
                throw new Exception_DB_TablesDuplicitReservation(
                    __e(__FILE__, 'Table %s has been already reserved by reservation named %s', $table, self::$reservedTables[$table])
                ); 
            }
        }
        // all provided tables are already reserved by actual process
        if (empty($tablesToReserve)) {
            return;
        }
        // remove orphan reservations - tables unreserved because of some inconvenience (e.g. DB server outage) 
        if (!empty(self::$tableReservationTimeLimit)) {
            self::delete(self::$reservationTable, array(
                'conditions' => array(
                    'table' => array_keys($tablesToReserve),
                    'created <' => date('Y-m-d H:i:s', time() - self::$tableReservationTimeLimit)
                ),
                'reserve' => false,
            ));
        }
        // try to reserve tables in global scope, means check the tables reserved by other processes
        $reserved = false;
        for(;$options['tries'] > 0; $options['tries']--) {
            try {
                self::insert(self::$reservationTable, $data, array(
                    'multiple' => true, 
                    'reserve' => false,
                ));
                $reserved = true;
                break;
            } 
            catch (Throwable $e) {
                if (!empty($options['retryTime'])) {
                    usleep($options['retryTime']);
                }
            }
        }
        // if successfully reserved then update list of tables reserved by actual process
        if ($reserved) {
            self::$reservedTables = array_merge(self::$reservedTables, $tablesToReserve);
        }
        // otherwise throw reservation exception
        else {
            throw new Exception_DB_TablesReservationFailure(
                __e(__FILE__, 'Reservation of %s tables has failed', implode(', ', $tables))
            );
        }
    }
    
    /**
     * Unreserves tables reserved by actual process under provided reservation name.
     * 
     * @param string $name Optional. Reservation name to unreserve tables for. 
     *      If no reservation name is provided then all tables reserved by actual process
     *      are unreserved.
     */
    public static function unreserveTables($name = null) {
        $tables = array();
        foreach(self::$reservedTables as $table => $reservationName) {
            if (empty($name) || $reservationName === $name) {
                $tables[] = $table;
                unset(self::$reservedTables[$table]);
            }
        }
        if (!empty($tables)) {
            self::delete(self::$reservationTable, array(
                'conditions' => array('table' => $tables),
                'reserve' => false,
            ));
        }
    }
    
    /**
     * Return list of enum field values
     * 
     * @param string $table
     * @param string $field
     * 
     * @return array Plain list of enum field values
     */
    public static function getEnumValues($table, $field) {
        $enumValues = array();
        $fieldOptions = self::getFields($table, $field);
        if ($fieldOptions[$field]['type'] == 'enum') {
            $enumValues = $fieldOptions[$field]['values'];
        }
        return $enumValues;
    }
    
    /**
     * Return table field default value
     * 
     * @param string $table
     * @param string $field
     * 
     * @return string|NULL Default value
     */
    public static function getFieldDefaultValue($table, $field) {
        $fields = self::getFields($table, $field);
        return $fields[$field]['default'];
    }
    
    /**
     * Returns table field options in an array like:
     * 
     *      array(
     *          // options used by method DB::getQueryFieldDefinition() 
     *          'type' => ..., 
     *          'length' => ...,
     *          'values' => ..., 
     *          'null' => ...,
     *          'default' => ..., 
     *          'autoIncrement' => ...,
     *          'index' => ...,
     *          'collation' => null,
     *          'comment' => null,
     *          // the original data retrieved from DB
     *          '_Field' => ...,
     *          '_Type' => ...,
     *          '_Null' => ...,
     *          '_Key' => ...,
     *          '_Default' => ...,
     *          '_Extra' => ...,
     *      ),
     * 
     * NOTE: The 'index' cannot be parsed exactly (see http://dev.mysql.com/doc/refman/5.0/en/show-columns.html)
     * Items 'collation' and 'comment' cannot be retrieved from DB.
     * 
     * @param string $table
     * @param string $field
     * 
     * @return array|NULL Array described here above. NULL if specified $field does 
     *      not exists in $table
     */
    public static function getFieldOptions($table, $field) {
        $fields = self::getFields($table, $field);
        if (isset($fields[$field])) {
            return $fields[$field];
        }
        return null;
    }
    
    /**
     * Returns table fields and their options in an array like:
     * 
     *      array(
     *          '{fieldName01}' => array(
     *              // options used by method DB::getQueryFieldDefinition() 
     *              'type' => ..., 
     *              'length' => ...,
     *              'values' => ..., 
     *              'null' => ...,
     *              'default' => ..., 
     *              'autoIncrement' => ...,
     *              'index' => ...,
     *              'collation' => null,
     *              'comment' => null,
     *              // the original data retrieved from DB
     *              '_Field' => ...,
     *              '_Type' => ...,
     *              '_Null' => ...,
     *              '_Key' => ...,
     *              '_Default' => ...,
     *              '_Extra' => ...,
     *          ),
     *          '{fieldName02}' => array(...),
     *          ...
     *      )
     * 
     * NOTE: The 'index' cannot be parsed exactly (see http://dev.mysql.com/doc/refman/5.0/en/show-columns.html)
     * Items 'collation' and 'comment' cannot be retrieved from DB.
     * 
     * @todo this can be refactored by direct reading of information_schema, the question is
     * if information_schema is readable on public hostings
     *      
     * @param string $table
     * @param string $field Optional. Explicit field name to get. If provided then resulting
     *      array contains just one item. Defaults to NULL, means all fields are retrieved
     * 
     * @return array Array described here above
     * 
     * @throws Exception on invalid field name
     */
    public static function getFields($table, $field = null) {
        // create query
        $table = self::encloseName($table);
        $query = 'SHOW COLUMNS FROM ' . $table;
        if ($field) {
            $field = trim($field, '`');
            $query .= ' LIKE \'' . $field . '\'';
        }
        $query .= ';';
        // execute
        $rawFields = self::query($query, false);
        if (empty($rawFields) && $field) {
            throw new Exception("Invalid field name {$field}");
        }
        $fields = array();
        foreach((array)$rawFields as $rawField) {
            // normalize retrieved data to options used by DB::getQueryFieldDefinition()
            // - null
            $null = ($rawField['Null'] == 'YES');
            // -default
            if ($null && $rawField['Default'] === null) {
                $default = null;
            }
            else {
                $default = (string)$rawField['Default'];
            }
            // - type, length, values
            if (preg_match('/^([a-z]+)\(([0-9]+)\)$/i', $rawField['Type'], $matches)) {
                $type = $matches[1];
                $length = $matches[2] ? $matches[2] : null;
                $values = null;
            }
            elseif (preg_match("/^([a-z]+)\(((?:'[^']+',?)+)\)$/i", $rawField['Type'], $matches)) {
                $type = $matches[1];
                $length = null;
                $values = explode(',', $matches[2]);
                $values = str_replace("''", "'", $values);
                foreach ($values as &$value) {
                    $value = substr($value, 1, -1);
                }
                unset($value);
            }
            else {
                $type = $rawField['Type'];
                $length = null;
                $values = null;
            }
            // - index (!!! this is not exact - see http://dev.mysql.com/doc/refman/5.0/en/show-columns.html)
            if ($rawField['Key'] == 'PRI') {
                $index = 'primary';
            }
            elseif ($rawField['Key'] == 'UNI') {
                $index = 'unique';
            }
            elseif ($rawField['Key'] == 'MUL') {
                $index = 'index';
            }
            else {
                $index = null;
            }
            // - autoIncrement
            if (preg_match("/auto_increment/i", $rawField['Extra'])) {
               $autoIncrement = true; 
            }
            else {
                $autoIncrement = false;
            }
            // create new item in $fields
            $fields[$rawField['Field']] = array(
                'type' => $type, 
                'length' => $length,
                'values' => $values, 
                'null' => $null,
                'default' => $default, 
                'autoIncrement' => $autoIncrement,
                'index' => $index,
                'collation' => null,
                'comment' => null,
                // preserve also the original retrieved values from DB
                '_Field' => $rawField['Field'],
                '_Type' => $rawField['Type'],
                '_Null' => $rawField['Null'],
                '_Key' => $rawField['Key'],
                '_Default' => $rawField['Default'],
                '_Extra' => $rawField['Extra'],
            );
        }
        return $fields;
    }
    
    /**
     * Returns a list of all table names in conected database.
     * 
     * @return array All tables fro currently connected database
     */
    public static function getTables() {
        $rawTables = self::query('SHOW TABLES;');
        $tables = array();
        foreach ($rawTables as $rawTable) {
            $tables[] = reset($rawTable);
        }
        return $tables;
    }
    
    /**
     * Gets folowing table status properties:
     *    - 'Name'
     *    - 'Engine'
     *    - 'Version'
     *    - 'Row_format'
     *    - 'Rows'
     *    - 'Avg_row_length'
     *    - 'Data_length'
     *    - 'Max_data_length'
     *    - 'Index_length'
     *    - 'Data_free'
     *    - 'Auto_increment'
     *    - 'Create_time'
     *    - 'Update_time'
     *    - 'Check_time'
     *    - 'Collation'
     *    - 'Checksum'
     *    - 'Create_options'
     *    - 'Comment'
     * 
     * @return array Array containing the items listed here above
     */
    public static function getTableStatus($table) {
        $status = self::query('SHOW TABLE STATUS LIKE \'' . $table . '\';');
        return reset($status);
    }
    
    /**
     * Checks if there is a table with given name in database
     * 
     * @param string $table Table name to be checked for
     * 
     * @return bool TRUE if table exists, otherwise FALSE
     */
    public static function hasTable($table) {
        return (bool)self::query('SHOW TABLES LIKE \'' . $table . '\';');
    }
    
    /**
     * Checks if the given field exists in provided table
     * 
     * @param string $table
     * @param string $field
     * @param array $options Optional. Array of options to be verified if 
     *      the $field has the same. E.g. to verify if the field has unique index
     *      use array('index' => array('unique', 'primary')). To verify if the 
     *      field has primary index use array('index' => 'primary'). To verify
     *      if the field has no index use array('index' => false). Defaults to 
     *      empty array(). For other possible options and their values see DB::getFields().
     * 
     * @return boolean
     */
    public static function hasField($table, $field, $options = array()) {
        try {
            $fields = self::getFields($table, $field);
        }
        catch (Throwable $e) {
            return false;
        }
        if (!empty($options)) {
            $fieldOptions = $fields[$field];
            $options = (array)$options;
            foreach ($options as $option => $value) {
                $hasOption = array_key_exists($option, $fieldOptions);
                $arrayValue = is_array($value);
                if ($hasOption) {
                    if (
                        $arrayValue
                        && !in_array($fieldOptions[$option], $value, true)
                        ||
                        !$arrayValue
                        && $value != $fieldOptions[$option]
                    ) {
                        return false;
                    }
                }
                elseif ($value !== false) {
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * Truncates provided tables.
     * 
     * Not allowed to use in transaction (throws exception) because causes 
     * transaction autocommit.
     * 
     * @param string|array $tables Single table name (string) or an array of table
     *      names to be truncated.
     * @params array $options Following can be used:
     *      - 'reserve' (bool|array) If TRUE then the query is executed only if $table 
     *          reservation succeed - means if the table is succesfully or has been already 
     *          reserved by actual process. If array then passed as options to DB::reserveTables().
     *          Defaults to TRUE.
     * 
     * @return bool TRUE on success. An Exception is raised on reservation failure, 
     *      on SQL error or if used in transaction.
     * 
     * @throws Exception_DB_TablesReservationFailure
     * @throws Exception Exception If used inside transaction
     */
    public static function truncateTables($tables, $options = array()) {
        $defaults = array(
            'reserve' => true,
        );
        $options = array_merge($defaults, $options);
        // truncate table breaks transaction so check if this is executed in transaction
        if (self::$inTransaction) {
            throw new Exception(__e(__FILE__, 'Table truncation inside transaction %s. Table truncation autocommits transaction', self::$inTransaction));
        }
        // reserve if required
        if ($options['reserve']) {
            self::reserveTables('DB_truncateTables', $tables, (array)$options['reserve']);
        }
        // execute queries
        $tables = (array)$tables;
        foreach ($tables as $table) {
            $table = self::encloseName($table);
            // it is not possible to truncate many tables in one query
            try {
                self::query('TRUNCATE TABLE ' . $table . ';', false);
            } 
            catch (Throwable $e) {
                // unreserve
                if ($options['reserve']) {
                    self::unreserveTables('DB_truncateTables');
                }
                throw $e;
            }
        }
        // unreserve
        if ($options['reserve']) {
            self::unreserveTables('DB_truncateTables');
        }
        return true;
    }
    
    /**
     * Truncates all tables from the database
     * 
     * @return bool TRUE on success
     */
    public static function truncateAllTables() {
        $tables = self::getTables();
        return self::truncateTables($tables);
    }
    
    /**
     * Drops provided tables.
     * 
     * Not allowed to use in transaction (throws exception) because causes 
     * transaction autocommit.
     * 
     * @param string|array $tables Single table name (string) or an array of table
     *      names to be dropped.
     * @params array $options Following can be used:
     *      - 'reserve' (bool|array) If TRUE then the query is executed only if $table 
     *          reservation succeed - means if the table is succesfully or has been already 
     *          reserved by actual process. If array then passed as options to DB::reserveTables().
     *          Defaults to TRUE.
     * 
     * @return bool TRUE on success. An Exception is raised on reservation failure, 
     *      on SQL error or if used inside transaction.
     * 
     * @throws Exception_DB_TablesReservationFailure
     * @throws Exception if used inside transaction
     */
    public static function dropTables($tables, $options = array()) {
        $defaults = array(
            'reserve' => true,
        );
        $options = array_merge($defaults, $options);
        // drop table breaks transaction so check if this is executed in transaction
        if (self::$inTransaction) {
            throw new Exception(__e(__FILE__, 'Table drop inside transaction %s. Table drop autocommits transaction', self::$inTransaction));
        }
        // reserve if required
        if ($options['reserve']) {
            self::reserveTables('DB_dropTables', $tables, (array)$options['reserve']);
        }
        $tablesSQL = self::getQueryTables($tables);
        // execute query
        try {
            self::query('DROP TABLE IF EXISTS ' . $tablesSQL . ';', false);
        }
        catch (Throwable $e) {
            // unreserve
            if ($options['reserve']) {
                self::unreserveTables('DB_dropTables');
            }
            throw $e;
        }
        // unreserve
        if ($options['reserve']) {
            self::unreserveTables('DB_dropTables');
        }
        return true;
    }
    
    /**
     * Drops all tables from the database
     * 
     * @return bool TRUE on success
     */
    public static function dropAllTables() {
        $tables = self::getTables();
        return self::dropTables($tables);
    }
    
    /**
     * Creates a table with given $fields and according to given $options.
     * 
     * Not allowed to use in transaction (throws exception) because causes 
     * transaction autocommit.
     * 
     * @param string $table
     * @param array $fields Fields definition array like:
     * 
     *          array(
     *              '{fieldName01}' => array(
     *                  'type' => 'int',
     *                  'length' => 11,
     *                  ...
     *              ),
     *              '{fieldName02}' => array(...),
     *              ...
     *          )
     * 
     *      See DB::getQueryFieldDefinition() for all possible field options.
     * 
     * @param array $options See defaults of DB::getCreateTableQuery() and
     *      DB::getQueryTableOptions()
     * 
     * @return bool TRUE on success. Throws exception if used in transaction.
     * 
     * @throws Exception If used inside transaction
     */
    public static function createTable($table, $fields, $options = array()) {  
        // create table breaks transaction so check if this is executed in transaction
        if (self::$inTransaction) {
            throw new Exception(__e(__FILE__, 'Table creation inside transaction %s. Table creation autocommits transaction', self::$inTransaction));
        }
        // get query
        $query = self::getCreateTableQuery($table, $fields, $options);
        // execute query
        return self::query($query, false);
    }
    
    /**
     * Creates sql for create table query
     * 
     * @param string $table
     * @param array $fields Array of field and their options. For available field options
     *          see DB::getQueryFieldDefinition().
     * @param array $options See also options for DB::getQueryTableOptions()
     * @return string
     */
    public static function getCreateTableQuery($table, $fields, $options = array()) {
        $defaults = array(
            'indexes' => array(), //array(array('type' => 'index', 'fields' => array('fieldA', 'fieldB'), 'name' => 'MyIndex'), array(...), ...)
            'closed' => true,
        );
        $options = array_merge($defaults, $options);
        
        // build query parts
        $table = self::encloseName($table);
        // - merge the fields indexes into table indexes and create indexes sql
        $indexes = self::getIndexes($fields, $options['indexes']);
        $indexesSQL = self::getQueryIndexes($indexes);
        $fieldDefinitionsSQL = array();
        foreach ($fields as $fieldName => $fieldOptions) {
            // erase index, it has been merged here above into table indexes
            $fieldOptions['index'] = null; 
            $fieldDefinitionsSQL[] = self::getQueryFieldDefinition($fieldName, $fieldOptions);
        }
        $fieldDefinitionsSQL = implode(', ', $fieldDefinitionsSQL);
        $tableOptionsSQL = self::getQueryTableOptions($options);
                        
        // build query string
        $query = 'CREATE TABLE IF NOT EXISTS ' . $table . ' (' . $fieldDefinitionsSQL;
        if ($indexesSQL) {
            $query .= ', ' . $indexesSQL;
        }
        $query .= ')';
        if ($tableOptionsSQL) {
            $query .= ' ' . $tableOptionsSQL;
        }
        if ($options['closed']) {
            $query .= ';';
        }
        
        return $query;                
    }
    
    /**
     * @param string $table
     * @param string $fieldName
     * @param array $fieldOptions Options of DB::getQueryFieldDefinition()
     * @param array $options Following are available:
     *      - 'after' (string|FALSE|NULL) Name of field to place the new created field
     *          after. If empty (except of NULL) then the new created field is placed as the
     *          first one. If NULL then the new created field is placed as the last one.
     *          Defaults to NULL.
     */
    public static function createTableField($table, $fieldName, $fieldOptions, $options = array()) {
        $defaults = array(
            'after' => null,
            'closed' => true,
        );
        $options = array_merge($defaults, $options);
        
        $table = self::encloseName($table);
        
        $indexes = self::getIndexes(array($fieldName => $fieldOptions));
        $indexesSQL = self::getQueryIndexes($indexes);
        
        $fieldDefinitionsSQL = array();
        // index set to null, because we have indexes in ĹindexesSQL
        $fieldOptions['index'] = null;
        $fieldDefinitionsSQL[] = self::getQueryFieldDefinition($fieldName, $fieldOptions);
        $fieldDefinitionsSQL = implode(', ', $fieldDefinitionsSQL);
        $query = 'ALTER TABLE ' . $table . ' ADD ' . $fieldDefinitionsSQL . '';
        
        if ($options['after']) {
            $query .= ' AFTER ' . self::encloseName($options['after']);
        }
        elseif ($options['after'] !== null) {
            $query .= ' FIRST';
        }
        
        if ($indexesSQL) {
            $query .= ', ADD ' . $indexesSQL;
        }
        
        if ($options['closed']) {
            $query .= ';';
        }
        // execute query
        return self::query($query, false);
        
        //ALTER TABLE  `run_mailer_groups` ADD  `test` VARCHAR( 255 ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL
        // ALTER TABLE  `run_mailer_groups` CHANGE  `test`  `test` VARCHAR( 255 ) CHARACTER SET cp1251 COLLATE cp1251_bulgarian_ci NULL DEFAULT NULL
        
        // query generated by PMA on adding a new column with defined index         
        //ALTER TABLE  `run_test_x` ADD  `testa` INT NOT NULL AFTER  `pid` ,
        //ADD INDEX (  `testa` )       

        // query generated by PMA on adding a new column with defined unique index         
        //ALTER TABLE  `run_test_x` ADD  `testb` INT NULL AFTER  `testa` ,
        //ADD UNIQUE (`testb`) 

        // query generated by PMA on adding a new column with defined as primary key        
        //ALTER TABLE  `run_test_x` ADD  `id` INT NOT NULL FIRST ,
        //ADD PRIMARY KEY ( `id` )       

        // query generated by PMA on adding a new column with defined as primary key with auto_increment
        // autoincrement must be added only on primary key column so creation of such a column must be done in one step       
        //ALTER TABLE  `run_test_x` ADD  `id` INT NOT NULL AUTO_INCREMENT PRIMARY KEY        
        //
        // BUT!!! SEEMS that even previous version composed from two queries should work:
        //ALTER TABLE  `test` ADD  `id` INT NOT NULL AUTO_INCREMENT FIRST, ADD PRIMARY KEY ( `id` );   
    }
    
    /**
     * 
     * @param string $table
     * @param string $fieldName
     * @param array $fieldOptions See DB::getQueryFieldDefinition()
     * @param array $options Following are available:
     *      - 'after' (string)
     *      - 'first' (boolean)
     *      - 'last' (boolean)
     * 
     * @todo
     */
    public static function getCreateTableFieldQuery($table, $fieldName, $fieldOptions, $options = array()) {
        $defaults = array(
            'after' => null,
            'first' => false,
            'last' => true,
        );
        $options = array_merge($defaults, $options);
        
    }
    
    /**
     * Creates SQL string which can be used in CREATE TABLE or ALTER TABLE for field definition 
     *  
     * @param string $fieldName
     * @param array $fieldOptions Following are available:
     *      - 'type' (string) One of mySQL available data types, e.g. 'int', 'varchar', 
     *          'text', 'boolean', 'enum', 'decimal', 'float', etc. The name is 
     *          caseinsensitive so it can be introduced also as 'INT', 'VARCHAR', etc.
     *      - 'length' (int|float|string) Field length applied e.g. to 'char', 'varchar', 
     *          'int', 'float', 'decimal' field types. In case of floating point types (e.g.
     *          'float', 'decimal') it can be provided as float number (e.g. 8.2) or as 
     *          string (e.g. '8,2') to specify the whole length (8) and number of 
     *          decimals (2). The default value varies according to field type: 
     *          'int': 11, 'varchar': 255, 'boolean': 1, otherwise NULL.
     *      - 'values' (array) List of values for 'enum' and 'set' field types.
     *          Defaults to NULL.
     *      - 'default' (mixed) Field default value. If NULL then option 'null'
     *          is implicitly turned on to TRUE.
     *      - 'null' (boolean) Are NULL values allowed for field or no? Defaults 
     *          to FALSE but if option 'default' is set to NULL then this option
     *          is implicitly turned on to TRUE.
     *      - 'collation' (string) Text fields charset collation. Defaults to 'utf8_general_ci' for
     *          text fields otherwise NULL (not applicable).
     *      - 'index' (string) Field index type, possible values are 'index', 'unique'
     *          and 'primary'. Defaults to NULL, it means to index is defined for the file. 
     *      - 'autoIncrement' (boolean) Should be primary key field value autoincremented
     *          or no? Applies only if 'index' option is set to 'primary'. Defaults 
     *          to FALSE.
     *      - 'comment' (string) Field comment. Defaults to NULL.
     * 
     * @return string
     * 
     * @throws Exception on invalid $fieldName, invalid type and values definitions in $fieldOptions
     */
    protected static function getQueryFieldDefinition($fieldName, $fieldOptions) {
        $defaultField = array(
            //'type' => null, // required item
            'length' => null,
            'values' => null, // applies only for ENUM or SET type
            //'default' => null, // this is not in defaults to allow interpret properly null value
            'null' => null,  // TRUE/FALSE, if NULL then implicitly according default NULL value
            'collation' => null,
            'index' => null, // primary, unique, index
            'autoIncrement' => false,
            'comment' => null
        );
        $stringField = array(
            'length' => 255,
            'collation' => 'utf8_general_ci',
        );
        $textField = array(
            'collation' => 'utf8_general_ci',
        );
        $integerField = array(
            'length' => 11,
        );
        $booleanField = array(
            'length' => 1,
        );
        
        // check for valid field name
        if (is_int($fieldName) || !preg_match('/^\w[a-z0-9_]*$/i', $fieldName)) {
            throw new Exception("Invalid field name {$fieldName} provided for new field definition");
        }
        // 'type' propertie of field definitions is required
        if (empty($fieldOptions['type'])) {
            throw new Exception("Missing type in definition of field {$fieldName}");
        }
        
        // resolve the proper DB type
        $type = strtoupper($fieldOptions['type']);
        switch ($type) {
            case 'STRING': case 'STR': case 'VARCHAR':
                $fieldOptions = array_merge($defaultField, $stringField, $fieldOptions);
                $type = 'VARCHAR';
                break;
            case 'CHAR':
                $fieldOptions = array_merge($defaultField, $stringField, $fieldOptions);
                break;
            case 'INT': case 'INTEGER':
                $fieldOptions = array_merge($defaultField, $integerField, $fieldOptions);
                $type = 'INT';
                break;
            case 'TINYINT': case 'BOOL': case 'BOOLEAN':
                $fieldOptions = array_merge($defaultField, $booleanField, $fieldOptions);
                $type = 'TINYINT';
                break;
            case 'TINYTEXT': case 'TEXT': case 'MEDIUMTEXT': case 'LONGTEXT':
                $fieldOptions = array_merge($defaultField, $textField, $fieldOptions);
                break;
            case 'BIT': case 'SMALLINT': case 'MEDIUMINT': case 'BIGINT':
            case 'REAL': case 'DOUBLE': case 'FLOAT': case 'DECIMAL': case 'NUMERIC':
            case 'DATE': case 'TIME': case 'TIMESTAMP': case 'DATETIME':
            case 'YEAR': case 'BINARY': case 'VARBINARY': case 'TINYBLOB':
            case 'BLOB': case 'MEDIUMBLOB': case 'LONGBLOB': case 'ENUM': case 'SET':
                $fieldOptions = array_merge($defaultField, $fieldOptions);
                break;
            default:
                throw new Exception("Invalid type {$fieldOptions['type']} in definition of field {$fieldName}");
        }
        
        $fieldDefinitionSQL = self::encloseName($fieldName) . ' ' . $type;
        if ($fieldOptions['length']) {
            $fieldDefinitionSQL .= '('  . str_replace('.', ',', $fieldOptions['length']) . ')';
        }
        elseif ($fieldOptions['values']) {
            if ($type != 'ENUM' && $type != 'SET') {
                throw new Exception("Invalid definition of values for {$type} in definition of field {$fieldName}. Values can be defined only for ENUM or SET type.");
            }
            if (is_array($fieldOptions['values'])) {
                $fieldOptions['values'] = '\'' . implode('\',\'', str_replace("'", "\'", $fieldOptions['values'])) . '\'';
            }
            $fieldDefinitionSQL .= '('  . $fieldOptions['values'] . ')';
        }
        if (
            $fieldOptions['collation']
            && ($collationSQL = self::getQueryCollation($fieldOptions['collation'], $type))
        ) {            
            $fieldDefinitionSQL .= ' ' . $collationSQL;
        }
        if (
            !empty($fieldOptions['null'])
            // or implicitly according default NULL value
            || (
                $fieldOptions['null'] === null
                && array_key_exists('default', $fieldOptions)
                && $fieldOptions['default'] === null
            )
        ) {
            $fieldDefinitionSQL .= ' NULL';
        }
        else {
            $fieldDefinitionSQL .= ' NOT NULL';
        }
        if (array_key_exists('default', $fieldOptions)) {
            if ($fieldOptions['default'] === null) {
                $fieldDefinitionSQL .= ' DEFAULT NULL';
            }
            else {
                $fieldDefinitionSQL .= ' DEFAULT \'' . $fieldOptions['default'] . '\'';
            }
        }
        if ($fieldOptions['autoIncrement']) {
            $fieldDefinitionSQL .= ' AUTO_INCREMENT';
        }
        if ($fieldOptions['comment']) {
            $fieldDefinitionSQL .= ' COMMENT \'' . str_replace("'", "\'", $fieldOptions['comment']) . '\'';
        }
        if ($fieldOptions['index']) {
            $fieldDefinitionSQL .= ', ' . self::getQueryIndex($fieldName, $fieldOptions['index']);
        }
        return $fieldDefinitionSQL;
    }
    
    /**
     * Checks if provided type of table field is a numeric data type.
     * E.g for 'bool', 'int' and 'float' returns TRUE but for 'varchar' returns FALSE.
     * 
     * @param string $fieldType
     * @return boolean
     */
    public static function isNumericDataType($fieldType) {
        $fieldType = strtoupper($fieldType);
        switch ($fieldType) {
            case 'INT': case 'INTEGER':
            case 'TINYINT': case 'BOOL': case 'BOOLEAN':
            case 'BIT': case 'SMALLINT': case 'MEDIUMINT': case 'BIGINT':
            case 'REAL': case 'DOUBLE': case 'FLOAT': case 'DECIMAL': case 'NUMERIC':
                return true;
        }
        return false;
    }
    
    /**
     * Checks if provided type of table field is a string data type.
     * E.g for 'bool', 'int' and 'float' returns FALSE but for 'varchar' returns TRUE.
     * 
     * @param string $fieldType
     * @return boolean
     */
    public static function isStringDataType($fieldType) {
        $fieldType = strtoupper($fieldType);
        switch ($fieldType) {
            case 'STRING': case 'STR':
            case 'VARCHAR': case 'CHAR':
            case 'TINYTEXT': case 'TEXT': case 'MEDIUMTEXT':
            case 'LONGTEXT': case 'ENUM': case 'SET':
                return true;
        }
        return false;
    }
    
    /**
     * Creates SQL string of collation which can be used in field or table definition.
     * 
     * @param string $collation Collation string,  e.g. 'utf8_general_ci'.
     * @param string $type Optional. Field data type can be provided in case that collation is 
     *      created for a field. If the given data type does not use any collation (e.g. INT)
     *      then an empty string is returned.
     * 
     * @return string Collation string, e.g. 'CHARACTER SET utf8 COLLATE utf8_general_ci'
     *      If an invalid $collation is provided or invalid type, then empty string is returned.
     */
    protected static function getQueryCollation($collation, $type = null) {
        $collationSQL = '';
        // validate the collation string - it must contain at least one underscore
        if (strpos($collation, '_') === false) {
            throw new Exception("Invalid collation {$collation}");
        }
        // check for proper type
        if (
            empty($type) || self::isStringDataType($type)
        ) {
            $collation = strtolower($collation);
            $collationParts = explode('_', $collation);
            $charset = array_shift($collationParts);
            $collationSQL = 'CHARACTER SET ' . $charset . ' COLLATE ' . $collation;
        }
        return $collationSQL;
    }
    
    /**
     * Creates SQL string of index, e.g. PRIMARY KEY (`id`), UNIQUE KEY (`email`), INDEX `name_index` (`name`)
     * 
     * @param string|array $fields A sigle field name or an array of fields names
     *      to create the index for.
     * @param string $type Index type, allowed values are 'primary', 'unique' 
     *      and 'index' (case insensitive). On invalid type an exception is thrown.
     * @param string $name Optional. Index name. Defaults to NULL (no name).
     * 
     * @return string SLQ index string which can be used in query CREATE TABLE
     *        or in a query ALTER TABLE
     * 
     * @throws Exception on invalid index $type.
     */
    protected static function getQueryIndex($fields, $type, $name = null) {
        $indexSQL = '';
        if ($fields) {
            // get fields string
            $fields = '`' . implode('`, `', (array)$fields) . '`';
            // normalize type
            $type = strtoupper($type);
            switch ($type) {
                case 'PRIMARY KEY':
                case 'PRIMARY':
                case 'KEY':
                    $type = 'PRIMARY KEY';
                    break;
                case 'UNIQUE KEY':
                case 'UNIQUE':
                    $type = 'UNIQUE';
                    break;
                case 'INDEX':
                    break;
                default:
                    throw new Exception("Unknown index type {$type}");
            }
            // create the index sql
            $indexSQL = $type;
            if ($name) {
                $indexSQL .= ' `' . $name . '`';
            }
            $indexSQL .= ' (' . $fields . ')';
        }
        return $indexSQL;
    }
    
    /**
     * Creates SQL for many indexes defined in one query
     * 
     * @param array $indexes Definition of indexes in an array like:
     *          array(
     *              array(
     *                  'type' => 'index', 
     *                  'fields' => array('fieldName01', 'fieldName02'), 
     *                  'name' => 'MyIndex01' // optional
     *              ),
     *              array(
     *                  'type' => 'primary', 
     *                  'fields' => array('id'), 
     *                  'name' => 'PRIMARY' // optional
     *              ),
     *              ...
     *          );
     * 
     * @return string SQL string of indexes which can be used in CREATE TABLE or
     *      ALTER TABLE query
     * 
     * @throws Exception on missing 'type' or/and 'fields' items in index definition.
     */
    protected static function getQueryIndexes($indexes) {
        $indexesSQL = '';
        if ($indexes) {
            $indexesSQL = array();
            foreach ($indexes as $index) {
                if (empty($index['fields'])) {
                    throw new Exception("Undefined/missing fields in index definition");
                }
                if (empty($index['type'])) {
                    throw new Exception("Undefined/missing type in index definition");
                }
                $indexesSQL[] = self::getQueryIndex($index['fields'], $index['type'], Sanitize::value($index['name']));
                
            }
            $indexesSQL = implode(', ', $indexesSQL);
        }
        return $indexesSQL;
    }
    
    /**
     * Creates SQL for table options in CREATE TABLE query
     * 
     * @param array $options Array of table options
     * 
     * @return string SQL string which can be used in CREATE TABLE
     * 
     * @throws Exception on invalid engine name specified in table options
     */
    protected static function getQueryTableOptions($options) {
        $defaults = array(
            'engine' => 'InnoDB', // MyISAM
            'collation' => 'utf8_general_ci',
            'autoIncrement' => null,
        );
        $options = array_merge($defaults, $options);
        
        $tableOptionsSQL = array();
        if ($options['engine']) {
            switch (strtolower($options['engine'])) {
                case 'myisam':
                    $options['engine'] = 'MyISAM';
                    break;
                case 'innodb':
                    $options['engine'] = 'InnoDB';
                    break;
                default:
                    throw new Exception("Invalid storage engine {$options['engine']}");
            }
            $tableOptionsSQL[] = 'ENGINE=' . $options['engine'];
        }
        if ($options['collation']) {            
            $tableOptionsSQL[] = self::getQueryCollation($options['collation']);
        }
        if ($options['autoIncrement']) {
            $tableOptionsSQL[] = 'AUTO_INCREMENT=' . $options['autoIncrement'];
        }
        return implode(' ', $tableOptionsSQL);
    }
    
    /**
     * Executes sql delete query with given conditions. 
     * 
     * ATTENTION: If Exception_DB_RecoverableDeadlock is thrown then the easiest way
     * to resolve the deadlock is to rollback and repeat the whole transaction again.
     *
     * @param string $table Table name to insert data
     * @param array $options All options of DB::getDeleteQuery() plus following:
     *      - 'reserve' (bool|array) If TRUE then the query is executed only if $table 
     *          reservation succeed - means if the table is succesfully or has been already 
     *          reserved by actual process. If array then passed as options to DB::reserveTables().
     *          Defaults to TRUE.
     *      - 'query' (string|array) SQL query string or array or SQL query partial
     *          strings which can be joined using single space (' ') to get SQL query 
     *          string. ATTENTION: If 'query' is provided then all other options are ignored
     *          and resulting SQL query can something totally different than expected DELETE.
     *          So be carefull when providing $options and consider possible presence
     *          of 'query' option in them!
     * 
     * @return bool TRUE on success. An Exception is raised on reservation failure and on SQL error.
     * 
     * @throws Exception_DB_RecoverableDeadlock
     * @throws Exception_DB_TablesReservationFailure
     */
    public static function delete($table, $options = array()) {
        $defaults = array(
            'query' => null, // explicit query, used for translations
            'reserve' => true,
        );
        $options = array_merge($defaults, $options);
        
        // get query
        if (empty($options['query'])) {
            $query = self::getDeleteQuery($table, $options);
        }
        else {
            $query = $options['query'];
        }
        // reserve if required
        if ($options['reserve']) {
            self::reserveTables('DB_delete', $table, (array)$options['reserve']);
        }
        // execute query
        try {
            self::query($query, false);
        } 
        catch (Throwable $e) {
            // unreserve
            if ($options['reserve']) {
                self::unreserveTables('DB_delete');
            }
            throw $e;
        }
        // unreserve
        if ($options['reserve']) {
            self::unreserveTables('DB_delete');
        }
        return true;
    }
    
    /**
     * Creates sql delete query. 
     *
     * @param string $table Table name to insert data
     * @param array $options Options 'conditions', 'limit', 'offset', 'order' , 'joins',
     *      'alias', 'literals', 'closed' and 'array' of DB::select() plus following:
     *      - 'joins' (array) See DB::getQueryJoins(). In addition to 'table',
     *          'type' and 'conditions' there can be specified also boolean value
     *          'delete'. If set to TRUE then also the corresponding record(s) in 
     *          joined table are deleted. 'delete' defaults to FALSE, means that only
     *          records in primary table are deleted.
     * 
     * @return string SQL delete query. 
     */
    public static function getDeleteQuery($table, $options = array()) {
        $defaults = array(
            'alias' => null,
            'joins' => null,
            'conditions' => null,
            'order' => null,
            'limit' => null,
            'offset' => null,
            'literals' => null,
            'closed' => true,
            'array' => false,
        );
        $options = array_merge($defaults, $options);
        
        // build query parts
        $literals = self::normalizeLiterals($options['literals']);
        $table = self::encloseName($table);
        if (!empty($options['alias'])) {
            $table .= ' AS ' . self::encloseAlias($options['alias']);
        }
        $joinsSQL = self::getQueryJoins($options['joins'], $literals['joins'], $deleteFromTables);
        $conditionsSQL = self::getQueryConditions($options['conditions'], $literals['conditions']);
        if (self::$avoidCache) {
            $conditionsSQL = self::addAvoidCacheSeed($conditionsSQL);
        }
        $orderSQL = self::getQueryOrder($options['order'], $literals['order']);
        $limitSQL = self::getQueryLimit($options['limit'], $options['offset']);
        
        // check for joined tables participation in delete operation
        array_unshift($deleteFromTables, self::getName($table));
        $deleteFromTablesSQL = implode(', ', $deleteFromTables);
        
        // do not allow to delete all records just because of forgotten conditions
        if (empty($conditionsSQL)) {
            throw new Exception("There are no conditions for sql DELETE");
        }
                
        // build query array
        if ($options['array']) {
            $query = array(
                'delete' => 'DELETE ' . $deleteFromTablesSQL,
                'from' => 'FROM ' . $table,
            );
            if (!empty($joinsSQL)) {
                $query['joins'] = $joinsSQL;    
            }
            $query['where'] = 'WHERE ' . $conditionsSQL;
            if (!empty($orderSQL)) {
                $query['order'] = $orderSQL;    
            }
            if (!empty($limitSQL)) {
                $query['limit'] = $limitSQL;    
            }
            if ($options['closed']) {
                $query['closed'] = ';';
            }
        }
        // build query string
        else {
            $query = 'DELETE ' . $deleteFromTablesSQL;
            $query .= ' FROM ' . $table;
            if (!empty($joinsSQL)) {
                $query .= ' ' . $joinsSQL;    
            }
            $query .= ' WHERE ' . $conditionsSQL;
            if (!empty($orderSQL)) {
                $query .= ' ' . $orderSQL;    
            }
            if (!empty($limitSQL)) {
                $query .= ' ' . $limitSQL;    
            }
            if ($options['closed']) {
                $query .= ';';
            }
        }
        
        return $query;                
    }
    
    /**
     * Executes update query according to given data and conditions
     * 
     * ATTENTION: If Exception_DB_RecoverableDeadlock is thrown then the easiest way
     * to resolve the deadlock is to rollback and repeat the whole transaction again.
     *
     * @param string $table Table name to insert data
     * @param array $data Associative array of pairs 'field' => 'value' to be saved.
     * @param array $options All options of DB::getUpdateQuery() plus following:
     *      - 'reserve' (bool|array) If TRUE then the query is executed only if $table 
     *          reservation succeed - means if the table is succesfully or has been already 
     *          reserved by actual process. If array then passed as options to DB::reserveTables().
     *          Defaults to TRUE.
     *      - 'query' (string|array) SQL query string or array or SQL query partial
     *          strings which can be joined using single space (' ') to get SQL query 
     *          string. ATTENTION: If 'query' is provided then all other options are ignored
     *          and resulting SQL query can something totally different than expected DELETE.
     *          So be carefull when providing $options and consider possible presence
     *          of 'query' option in them!
     * 
     * @return bool TRUE on success. An Exception is raised on reservation failure and on SQL error.
     * 
     * @throws Exception_DB_RecoverableDeadlock
     * @throws Exception_DB_TablesReservationFailure
     */
    public static function update($table, $data, $options = array()) {
        $defaults = array(
            'query' => null, // explicit query, used for translations
            'reserve' => true,
        );
        $options = array_merge($defaults, $options);
        
        // get query
        if (empty($options['query'])) {
            $query = self::getUpdateQuery($table, $data, $options);
        }
        else {
            $query = $options['query'];
        }
        // reserve if required
        if ($options['reserve']) {
            self::reserveTables('DB_update', $table, (array)$options['reserve']);
        }
        // execute query
        try {
            self::query($query, false);
        } 
        catch (Throwable $e) {
            // unreserve
            if ($options['reserve']) {
                self::unreserveTables('DB_update');
            }
            throw $e;
        }
        // unreserve
        if ($options['reserve']) {
            self::unreserveTables('DB_update');
        }
        return true;
    }
    
    /**
     * Creates sql update query.
     * 
     * @param string $table Table name to insert data
     * @param array $data Associative array of pairs 'field' => 'value' to be saved.
     * @param array $options Options 'conditions', 'limit', 'offset', 'order' , 'joins',
     *          'alias', 'closed' and 'array' of DB::select() plus following:
     *      - 'allowFields' (array) List of field names which are allowed to be saved.
     *      - 'avoidFields' (array) List of field names which will be excluded from save.
     *      - 'literals' (array) Associative array of literals used in other options.
     *          It can contain following keys: 'fields', 'data', 'conditions', 'order','joins'.
     *          Each of items is either TRUE to process whole definition of that item as literals 
     *          or string to provide single literal or a plain array of such strings, 
     *          e.g.: array('fields' => array('MyModel01.modified', 'MyModel02.modified)) or
     *          array('data' => array('NOW()', 'CONCAT(`first_name`, " ", `last_name`)')).
     *          In 'fields' literals there can be specified data fields names. In 'data' 
     *          literals there can be specified data values. It is prefered to use 'fields'
     *          literals over 'data' literals as 'fields' are more specific. Defaults to NULL.
     * 
     * @return string SQL update query
     */
    public static function getUpdateQuery($table, $data, $options = array()) {
        $defaults = array(
            'allowFields' => null,
            'avoidFields' => null,
            'alias' => null,
            'joins' => null,
            'conditions' => null,
            'order' => null,
            'limit' => null,
            'offset' => null,
            'literals' => null,
            'closed' => true,
            'array' => false,
        );
        $options = array_merge($defaults, $options);
        
        // build query parts
        $literals = self::normalizeLiterals($options['literals']);
        $table = self::encloseName($table);
        if (!empty($options['alias'])) {
            $table .= ' AS ' . self::encloseAlias($options['alias']);
        }
        $setSQL = self::getQuerySet($data, $options, $literals['fields'], $literals['data']);
        $joinsSQL = self::getQueryJoins($options['joins'], $literals['joins']);
        $conditionsSQL = self::getQueryConditions($options['conditions'], $literals['conditions']);
        if (self::$avoidCache) {
            $conditionsSQL = self::addAvoidCacheSeed($conditionsSQL);
        }
        $orderSQL = self::getQueryOrder($options['order'], $literals['order']);
        $limitSQL = self::getQueryLimit($options['limit'], $options['offset']);
        
        // do not allow to make update if no fields are going to be updated
        if (empty($setSQL)) {
            throw new Exception("There are no allowed fields in provided data for sql UPDATE");
        }
        // do not allow to update all records just because of forgotten conditions
        if (empty($conditionsSQL)) {
            throw new Exception("There are no conditions for sql UPDATE");
        }
        
        // build query array
        if ($options['array']) {
            $query = array(
                'update' => 'UPDATE ' . $table
            );
            if (!empty($joinsSQL)) {
                $query['joins'] = $joinsSQL;    
            }
            $query['set'] = 'SET ' . $setSQL;
            $query['where'] = 'WHERE ' . $conditionsSQL;
            if (!empty($orderSQL)) {
                $query['order'] = $orderSQL;    
            }
            if (!empty($limitSQL)) {
                $query['limit'] = $limitSQL;    
            }
            if ($options['closed']) {
                $query['closed'] = ';';
            }
        }
        // build query string
        else {
            $query = 'UPDATE ' . $table;
            if (!empty($joinsSQL)) {
                $query .= ' ' . $joinsSQL;    
            }
            $query .= ' SET ' . $setSQL;
            $query .= ' WHERE ' . $conditionsSQL;
            if (!empty($orderSQL)) {
                $query .= ' ' . $orderSQL;    
            }
            if (!empty($limitSQL)) {
                $query .= ' ' . $limitSQL;    
            }
            if ($options['closed']) {
                $query .= ';';
            }
        }
                
        return $query;        
    }
    
    /**
     * Get the ID generated in the last query
     * 
     * @return mixed The ID generated for an AUTO_INCREMENT column by the previous 
     * query on success, 0 if the previous query does not generate an AUTO_INCREMENT 
     * value, or FALSE if no MySQL connection was established.
     */
    public static function getInsertId() {
        return mysqli_insert_id(self::$link);
    }
    
    /**
     * Executes sql insert for provided data
     * 
     * ATTENTION: If multiple records are saved (see 'multiple' option of DB::getInsertQuery()) 
     * then all records fields are set by the first of provided records.
     * 
     * ATTENTION: If Exception_DB_RecoverableDeadlock is thrown then the easiest way
     * to resolve the deadlock is to rollback and repeat the whole transaction again.
     * 
     * @param string $table Table name to insert data
     * @param array $data Associative array of pairs 'field' => 'value' to be saved.
     * @param array $options All options of DB::getInsertQuery() plus following:
     *      - 'reserve' (bool|array) If TRUE then the query is executed only if $table 
     *          reservation succeed - means if the table is succesfully or has been already 
     *          reserved by actual process. If array then passed as options to DB::reserveTables().
     *          Defaults to TRUE.
     * 
     * @return int|TRUE Id of new created record. If $options['multiple'] is TRUE
     *      then the id of the first inserted record is returned. TRUE if the 
     *      query has not generated an AUTO_INCREMENT value (happens when inserting 
     *      records with specified ids which are lower than actual AUTO_INCREMENT value).
     *      An Exception is raised on reservation failure ans on SQL error.
     * 
     * @throws Exception_DB_RecoverableDeadlock
     * @throws Exception_DB_TablesReservationFailure
     * @throws Exception
     */
    public static function insert($table, $data, $options = array()) {
        $defaults = array(
            'reserve' => true,
        );
        $options = array_merge($defaults, $options);
        $query = self::getInsertQuery($table, $data, $options);
        // reserve if required
        if ($options['reserve']) {
            self::reserveTables('DB_insert', $table, (array)$options['reserve']);
        }
        // execute query
        try {
            self::query($query, false);
        } 
        catch (Throwable $e) {
            // unreserve
            if ($options['reserve']) {
                self::unreserveTables('DB_insert');
            }
            throw $e;
        }
        $id = self::getInsertId();
        // if query has not generated an AUTO_INCREMENT value 
        // then return TRUE to keep success output logic
        if (empty($id)) {
            $id = true;
        }
        // unreserve
        if ($options['reserve']) {
            self::unreserveTables('DB_insert');
        }
        return $id;
    }
    
    /**
     * Creates sql insert query for provided $data
     * 
     * @param string $table Table name to insert data
     * @param array $data Associative array of pairs 'field' => 'value' to be saved.
     * @param array $options Optional. Following options can be specified:
     *      - 'allowFields' (array) List of field names which are allowed to be saved.
     *      - 'avoidFields' (array) List of field names which will be excluded from save.
     *      - 'multiple' (bool) If TRUE then $data are considered for list of records.
     *          Defaults to FALSE, means data are considered to be just a single record.
     *      - 'ignore' (bool) If TRUE then INSERT IGNORE is created. Defaults to FALSE.
     *      - 'literals' (array) Associative array of literals used in other options.
     *          It can contain following keys: 'data', 'conditions', 'having', 'joins'.
     *          Each of items is either TRUE to process whole definition of that item as literals 
     *          or string to provide single literal or a plain array of such strings, 
     *          e.g.: array('data' => 'CONCAT(`first_name`, ' ', `last_name`)')
     *      - 'closed' (bool) If TRUE then query is created with ending ';'. If
     *          FALSE then ending ';' is omitted. Defaults to TRUE.
     * 
     * @return string SQL insert query. Throws an exception if multiple records 
     *      query is created and not all records have the same fields.
     * 
     * @throws Exception if multiple records query is created and not all records 
     *      have the same fields
     */
    public static function getInsertQuery($table, $data, $options = array()) {
        $defaults = array(
            'allowFields' => null,
            'avoidFields' => null,
            'literals' => null,
            'multiple' => false,
            'ignore' => false,
            'closed' => true,
        );
        $options = array_merge($defaults, $options);
        if (!$options['multiple']) {
            $data = array($data);
        }
        $record = reset($data);
        $fields = self::getDataFields($record, $options);
        
        $table = self::encloseName($table);
        
        // find out fields to assign literal values
        $literals = self::normalizeLiterals($options['literals']);
        $literalFields = $literals['fields'];
        
        $fieldsSQL = array();
        $valuesSQL = array();
        $getfieldsSQL = true;
        foreach ($data as $i => $record) {
            if (
                !empty($options['multiple'])
                && (($recordFields = self::getDataFields($record, $options)) || true) // do this always just to get $recordFields
                && (
                    ($diff = array_diff($recordFields, $fields))
                    || ($diff = array_diff($fields, $recordFields))
                )
            ) {
                throw new Exception(__e(__FILE__, 'Multiple records have different fields. Record #%s differs from the first one in following fields: %s', $i + 1, implode(', ', $diff)));
            }
            $valuesSQL[$i] = array();
            foreach($fields as $field) {
                $value = $record[$field];
                if (is_string($field) && !is_array($value)) {
                    switch(true) {
                        case $field == 'created' && empty($value):
                        case $field == 'modified' && empty($value):
                        case $field == 'deleted' && !empty($value):
                            $valuesSQL[$i][] = 'NOW()';
                            break;

                        case $value === null:
                            $valuesSQL[$i][] = 'NULL';
                            break;

                        case is_bool($value):
                            $value = (int)$value;
                            $valuesSQL[$i][] = '\'' . $value . '\'';
                            break;

                        case $literalFields === true || in_array($field, $literalFields, true):
                            $valuesSQL[$i][] = $value;
                            break;

                        default:
                            $value = self::escape((string)$value);
                            $valuesSQL[$i][] = '\'' . $value . '\'';
                            break;
                    }
                    if ($getfieldsSQL) {
                        $field = self::encloseName($field);
                        $fieldsSQL[] =  self::escape($field);
                    }
                }
            }
            // stop grabbing of fields after the first turn
            $getfieldsSQL = false;
            // create values sql string
            $valuesSQL[$i] = '(' . implode(', ', $valuesSQL[$i]) . ')';
        }
        
        // do not allow to make insert if no fields are going o be inserted
        if (empty($fieldsSQL)) {
            throw new Exception("There are no allowed fields in provided data for sql INSERT");
        }
        
        // build query string
        $insert = 'INSERT ';
        if ($options['ignore']) {
            $insert .= 'IGNORE ';
        }
        $insert .= 'INTO ';
        $fieldsSQL = implode(', ', $fieldsSQL);
        $valuesSQL = implode(', ', $valuesSQL);
        $query = $insert . $table. ' (' . $fieldsSQL . ') VALUES ' . $valuesSQL;
        if ($options['closed']) {
            $query .= ';';
        }
        
        return $query;
    }
    
    /**
     * Executes SQL SELECT according to provided options array like:
     * 
     *     $result = DB::select('my_table', array(
     *          'fields' => array('id', 'first_name'),
     *          'conditions' => array(
     *              'run_groups_id' => 1,
     *              array(
     *                  'username' => 'mojo',
     *                  'OR',
     *                  'id' => array(1, 2, 32),
     *              ),
     *              'first_name' => 'Mojo',
     *          ),
     *      ));
     * 
     * @param string $table Table name to select records from
     * @param array $options All options of DB::getSelectQuery() plus following:
     *      - 'inflate' (bool) If TRUE then resulting array is inflated, means 
     *          array('User.name' => 'Admin', 'User.age' => 25, 'Group.pid' => 'admins')
     *          is inflated to following one: array('User' => array('name' => 'Admin', 'age' => 25), 'Group' => array('pid' => 'admins')).
     *          This can be used together with 'qualify' option (see DB::getSelectQuery() options)
     *          but it is not required for 'qualify' to be TRUE,  because qualified fields aliases
     *          can be set also explicitly in 'fields'.
     *      - 'first' (bool) If TRUE then just first record is returned from all retrieved.
     *          This can be used only if 'resource' option is NPT TRUE. Defaults to FALSE.
     *      - 'resource' (bool) If TRUE then instead of array a resource handle is
     *          returned. This can be used in case that a huge amount of records
     *          is retrieved. The resource can be processed using DB::fetchArray($resource).
     *          Defaults to FALSE.
     *      - 'query' (string|array) SQL query string or array or SQL query partial
     *          strings which can be joined using single space (' ') to get SQL query 
     *          string. ATTENTION: If 'query' is provided then all other options are ignored
     *          and resulting SQL query can something totally different than expected DELETE.
     *          So be carefull when providing $options and consider possible presence
     *          of 'query' option in them!
     * 
     * @return array|resource Array of retrieved records of a resource handle 
     *      if 'resource' option is set to TRUE.
     */
    public static function select($table, $options = array()) {
        $defaults = array(
            'resource' => false,
            'first' => false,
            'qualify' => false,
            'inflate' => false,
            'query' => null, // explicit query, used for translations
        );
        $options = array_merge($defaults, $options);
        
        // force limit 1 if first is TRUE
        if ($options['first']) {
            $options['limit'] = 1;
        }
        // get query
        if (empty($options['query'])) {
            $query = self::getSelectQuery($table, $options);
        }
        else {
            $query = $options['query'];
        }
        // execute query
        $result = self::query($query, $options['resource']);
        if (is_array($result) && $options['first']) {
            $result = array_shift($result);
            if ($result === null) {
                $result = array();
            }
        }
        if (is_array($result) && $options['inflate']) {
            $result = Arr::inflate($result);
        }
        return $result;
    }
    
    /**
     * Creates SQL SELECT query from provided options array like:
     * 
     *     $query = DB::getSelectQuery('my_table', array(
     *          'fields' => array('id', 'first_name'),
     *          'conditions' => array(
     *              'run_groups_id' => 1,
     *              array(
     *                  'username' => 'mojo',
     *                  'OR',
     *                  'id' => array(1, 2, 32),
     *              ),
     *              'first_name' => 'Mojo',
     *          ),
     *      ));
     * 
     * @param string $table Table name. Table name can be provided also with its 
     *      alias using AS, e.g. '`my_table AS `mt`'. It's up to user to use backticks (`),
     *      the method itself do not add them automagicaly.
     * 
     * @param array $options Array containing following items:
     *      - 'alias' (string) Table alias. If empty value then no table alias is used.
     *          Default to NULL.
     *      - 'fields' (string|array) Single field name or an array of field names. 
     *          They can be qualified or/and with aliases. If not provided or empty then 
     *          SQL query is build with '*' to retrieve all existing fields in DB tables.
     *          For sake of performance it is the best to provide fields explicitly.
     *      - 'conditions' (array) Array of conditions definition. See DB::getQueryConditions().
     *          Example:
     *              'conditions' => array(
     *                  'run_groups_id' => 1,                           // `run_groups_id` = '1'
     *                  'my_table.username !=' => 'mojo',               // '`my_table`.`username` != 'mojo'
     *                  array(                                          // open brackets here
     *                      'id' => array(1, 2, 32),                    // `id` IN ('1','2','32')
     *                      'OR',                                       // 'OR'
     *                      'pid !=' => array('x', 'y', 'z')            // `pid` NOT IN ('x', 'y', 'z')
     *                  ),                                              // close brackets here
     *                  'email' => null,                                // `email` IS NULL
     *                  'image !=' => null,                             // `image` IS NOT NULL
     *                  'first_name ~%' => 'A'                          // `first_name` LIKE 'A%'
     *                  'last_name !%~' => array('ova', 'ska')          // (`last_name` LIKE NOT '%ova' AND `last_name` LIKE NOT '%ska')
     *                  'CONCAT(`first_name`, ' ', `last_name`)="..."'  // SQL literal, must be present in 'literals' => array ('conditions' => ...)
     *              )
     *      - 'having' (array) Array of having definition. The same syntax as for 'conditions' option.
     *      - 'order' (string|array) Single field order definition, e.g. 'last_name' (will be order ASC by default)
     *          or 'last_name DESC'. Or an array of orders for multiple fields like: 
     *              array(
     *                  'last_name',            // will be order ASC by default
     *                  'first_name ASC',
     *                  'age' => 'DESC',           
     *                  ...
     *              );
     *      - 'group' (string|array) Single field name or array of such names to group
     *          records by. Array for looks like:
     *              array(
     *                  'last_name',        
     *                  'age',           
     *                  ...
     *              );
     *      - 'limit' (int) Integer to specify limit of retrieved records amount.
     *          Defaults to NULL so no limit is imposed.
     *      - 'offset' (int) Integer to specify offset of retrieved records. Defaults to NULL
     *          so no offset is imposed.
     *      - 'page' (int) If specified then 'offset' is counted using like ('page' - 1) * 'limit'.
     *          This option is taken in to account only if 'offset' is NULL.
     *      - 'distinct' (bool) If TRUE then SELECT DISTINCT is created. Defaults to FALSE.
     *          ATTENTION: DISTINCT combined with ORDER BY needs a temporary table in many cases. 
     *          Use of GROUP BY can be in such cases better. See https://dev.mysql.com/doc/refman/5.0/en/distinct-optimization.html
     *      - 'literals' (array) Associative array of literals used in other options.
     *          It can contain following keys: 'fields', 'conditions', 'having', 'order', 'group', 'joins'.
     *          Each of items is either TRUE to process whole definition of that item as literals 
     *          or string to provide single literal or a plain array of such strings, 
     *          e.g.: array('conditions' => 'CONCAT(`first_name`, ' ', `last_name`)="..."', 'order' => TRUE)
     *      - 'qualify' (bool|array) If TRUE then aliases are created for 'fields'
     *          from qualified fields. Means, field `my_table`.`field_1` is aliased
     *          as `my_table`.`field_1` AS `my_table.field_1`. If an array of qualifiers when
     *          only fields with given qualifiers will be aliased. This can be used to inflate the 
     *          resulting array. Fields which are already aliased are skipped. Defaults to FALSE.
     *      - 'joins' (array) Array of joins definition (!each of joins is an array itself).
     *          See DB::getQueryJoins(). Example:
     *              array(
     *                  // !!! Each join is another nested array !!!
     *                  array(
     *                      'type' => 'left',
     *                      'table' => 'my_table_3',             
     *                      'alias' => 't3',                             
     *                      'conditions' => array(
     *                          'my_table_2.id = `t3`.`my_table_2_id`',  
     *                      ),
     *                  ),
     *                  ...
     *              )
     *      - 'closed' (bool) If TRUE then query is created with ending ';'. If
     *          FALSE then ending ';' is omitted. Defaults to TRUE.
     *      - 'array' (bool) If TRUE then the resulting query is returned as array
     *          contaning query clauses:
     *              array(
     *                  'select' => 'SELECT field01, field02, ...'
     *                  'from' => 'FROM table'
     *                  'joins' => '...'
     *                  'where' => '...'
     *                  'group' => '...'
     *                  'having' => '...'
     *                  'order' => '...'
     *                  'limit' => '...'
     *                  'page' => '...'
     *                  'closed' => ';'
     *              )
     *          Only that clauses are contained which are defined by options.
     *          Resulting query can be easily created by implode(' ', $queryArray).
     *          It is used for implementation of translations in Model class.
     *      - 'avoidCache' (bool) If TRUE then MySQL server neither checks the query cache 
     *          to see whether the result is already cached, nor does it cache the 
     *          query result. This feature is removed from MySQL 8+ 
     *          (https://mysqlserverteam.com/mysql-8-0-retiring-support-for-the-query-cache/)
     *          MariaDB supports it for the moment. ATTENTION: This is not working in the 
     *          same way as DB::$avoidCache = TRUE. Defaults to FALSE.
     *              
     * @return string SQL SELECT query 
     */
    public static function getSelectQuery($table, $options = array()) {
        $defaults = array(
            'alias' => null,
            'fields' => null,
            'joins' => null,
            'conditions' => null,
            'group' => null,
            'having' => null,
            'order' => null,
            'limit' => null,
            'offset' => null,
            'page' => null,
            'distinct' => false,
            'literals' => null,
            'qualify' => false,
            'closed' => true,
            'array' => false,
            'avoidCache' => false,
        );
        $options = array_merge($defaults, $options); 
        
        // build query parts
        $literals = self::normalizeLiterals($options['literals']);
        $table = self::encloseName($table);
        if (!empty($options['alias'])) {
            $table .= ' AS ' . self::encloseAlias($options['alias']);
        }
        $fieldsSQL = self::getQueryFields($options['fields'], $literals['fields'], $options['qualify']);
        $joinsSQL = self::getQueryJoins($options['joins'], $literals['joins']);
        $conditionsSQL = self::getQueryConditions($options['conditions'], $literals['conditions']);
        if (self::$avoidCache) {
            $conditionsSQL = self::addAvoidCacheSeed($conditionsSQL);
        }
        $groupSQL = self::getQueryGroup($options['group'], $literals['group']);
        $havingSQL = self::getQueryConditions($options['having'], $literals['having']);
        $orderSQL = self::getQueryOrder($options['order'], $literals['order']);
        $limitSQL = self::getQueryLimit($options['limit'], $options['offset'], $options['page']);
        
        // apply DISTINCT to SELECT keyword
        $select = 'SELECT ';
        if ($options['avoidCache']) {
            $select .= 'SQL_NO_CACHE ';
        }
        if ($options['distinct']) {
            $select .= 'DISTINCT ';
        }
        // build query array
        if ($options['array']) {
            $query = array(
                'select' => $select . $fieldsSQL,
                'from' => 'FROM ' . $table,
            );
            if (!empty($joinsSQL)) {
                $query['joins'] = $joinsSQL;    
            }
            if (!empty($conditionsSQL)) {
                $query['where'] = 'WHERE ' . $conditionsSQL;    
            }
            if (!empty($groupSQL)) {
                $query['group'] = $groupSQL;    
            }
            if (!empty($havingSQL)) {
                $query['having'] = 'HAVING ' . $havingSQL;    
            }
            if (!empty($orderSQL)) {
                $query['order'] = $orderSQL;    
            }
            if (!empty($limitSQL)) {
                $query['limit'] = $limitSQL;    
            }
            if ($options['closed']) {
                $query['closed'] = ';';
            }
        }
        // build query string
        else {
            $query = $select . $fieldsSQL;
            $query .= ' FROM ' . $table;
            if (!empty($joinsSQL)) {
                $query .= ' ' . $joinsSQL;    
            }
            if (!empty($conditionsSQL)) {
                $query .= ' WHERE ' . $conditionsSQL;    
            }
            if (!empty($groupSQL)) {
                $query .= ' ' . $groupSQL;    
            }
            if (!empty($havingSQL)) {
                $query .= ' HAVING ' . $havingSQL;    
            }
            if (!empty($orderSQL)) {
                $query .= ' ' . $orderSQL;    
            }
            if (!empty($limitSQL)) {
                $query .= ' ' . $limitSQL;    
            }
            if ($options['closed']) {
                $query .= ';';
            }
        }

        return $query;
    }
        
    /**
     * Creates SQL string of fields ( ...for SELECT query)
     * 
     * @param array $fields Array of field names. They can be qualified or/and with aliases.
     * @param mixed $literalFields Optional. If TRUE then all fields are used as they
     *      are passed (without backtick and escape them). If array of field names 
     *      then only these fields are used literally.
     * @param bool|array $qualifyFields Optional. If TRUE then aliases are created for fields
     *      from qualified fields. Means, field `my_table`.`field_1` is aliased
     *      as `my_table`.`field_1` AS `my_table.field_1`. If an array of qualifiers when
     *      only fields with given qualifiers will be aliased. This can be used to inflate the 
     *      resulting array. Fields which are already aliased are skipped. Defaults to FALSE.
     * 
     * @return string 
     */
    protected static function getQueryFields($fields, $literalFields = array(), $qualifyFields = false){        
        $fields = (array)$fields;
        $literalFieldsIsArray = is_array($literalFields);
        $qualifyFieldsIsArray = is_array($qualifyFields);
        foreach($fields as &$field) { // make this by iterating (not by array_diff, _map, ...) to keep fields order
            if (
                !$literalFields
                ||
                $literalFieldsIsArray 
                && !in_array($field, $literalFields, true)
            ) {
                $field = self::encloseName($field);
                $field = self::escape($field);
                if ($qualifyFields) {
                    if (
                        $qualifyFieldsIsArray
                        && strpos(strtoupper($field), ' AS ') === false
                        && strpos($field, '*') === false
                        && strpos($field, '.') !== false // this is not neccessary
                    ) {
//                        $qualifier = explode('.', $field); //rb4lb//
//                        $qualifier = trim($qualifier[0], '`'); //rb4lb//
                        $qualifier = str_replace('`', '', $field);
                        $qualifier = explode('.', $qualifier);
                        array_pop($qualifier);
                        $qualifier = implode('.', $qualifier);
                        if (in_array($qualifier, $qualifyFields, true)) {
                            $field .= ' AS ' . self::encloseAlias($field);
                        }
                    }
                    elseif (
                        !$qualifyFieldsIsArray 
                        && strpos(strtoupper($field), ' AS ') === false
                        && strpos($field, '*') === false
                        && strpos($field, '.') !== false // this is not neccessary
                    ) {
                        $field .= ' AS ' . self::encloseAlias($field);
                    }
                }
            }
        }
        if ($fields) {
            $fieldsSQL = implode(', ', $fields);
            // correct backticked asterixes
            $fieldsSQL = str_replace('`*`', '*', $fieldsSQL);
        }
        else {
            $fieldsSQL = '*';
        }
        return $fieldsSQL;        
    }
    
    /**
     * Creates SQL string of conditions which can be used in WHERE, HAVING or ON clauses from
     * array of conditions definition like:
     * 
     *      'conditions' => array(
     *          'run_groups_id' => 1,                           // `run_groups_id` = '1'
     *          'my_table.username !=' => 'mojo',               // '`my_table`.`username` != 'mojo'
     *          array(                                          // open brackets here
     *              'id' => array(1, 2, 32),                    // `id` IN ('1','2','32')
     *              'OR',                                       // 'OR'
     *              'pid !=' => array('x', 'y', 'z', null)      // `pid` NOT IN ('x', 'y', 'z') AND `pid` IS NOT NULL
     *          ),                                              // close brackets here
     *          'NOT' => array(                                 // negation of nested condition
     *              'first_name' => null,
     *              'last_name' => null,
     *          )
     *          'email' => null,                                // `email` IS NULL
     *          'image !=' => null,                             // `image` IS NOT NULL
     *          'first_name ~*' => 'A'                          // `first_name` LIKE 'A%'
     *          'last_name !*~' => array('ova', 'ska')          // (`last_name` LIKE NOT '%ova' AND `last_name` LIKE NOT '%ska')
     *          'CONCAT(`first_name`, ' ', `last_name`)="..."'      // condition SQL literal
     *                                                              // must be present in $literalConditions
     * 
     *          'CONCAT(`first_name`, ' ', `last_name`)=' => '...'  // field SQL literal
     *                                                              // must be present in $literalConditions
     *                                                              // the operator must be included 
     *      )
     * 
     * @param array $conditions Array of conditions definition. Following items
     *      can be used in conditions definition:
     *          - '[{alias}.]{field}[ {operator}]' => '{value}', e.g.: 'User.name' => 'admin', 'age >' => 25
     *          - '[{alias}.]{field}[ {sql}]', e.g.: 'User.name IS NOT NULL'
     *          - '{sqlLiteral}', e.g.: 'CONCAT(`first_name`, ' ', `last_name`)'
     *          - array() to place items to brackets, e.g.: array('name' => 'admin', 'OR', 'name' => 'ADMIN')
     *          - 'AND' or '&&', 'OR' or '||' for logical operators. Default operator is 'AND'.
     *          - 'NOT' => {nestedConditionsArrayOrSqlLiteralString} for negation operator.
     *              The '!' can beused instead of 'NOT'. E.g.: 'NOT' => array('name' => 'admin', 'OR', 'name' => 'ADMIN')
     * 
     *      Following operators are available:  
     *          - '!=' - not equals, can be used also for NULL value (IS NOT NULL) or
     *              array value (NOT IN)
     *          - '>', '<', '>=', '<='
     *          - '~', '*~*', '*~', '~*' - LIKE '{value}', LIKE '%{value}%', LIKE '%{value}',
     *              LIKE '{value}%'. If the value is array, then LIKE is generated for each of
     *              array values and logical OR is used to connect generated LIKEs.
     *          - '!~', '!*~*', '!*~', '!~*' - NOT LIKE, NOT LIKE '%{value}%', NOT LIKE '%{value}',
     *              NOT LIKE '{value}%'. If the value is array, then NOT LIKE is generated for each of
     *              array values and logical AND is used to connect generated NOT LIKEs.
     *          - If no perator used then it defaults to equals operator '=' 
     * 
     *      If value is provided in form of array then IN/NOT IN is used instead of =/!=.
     * 
     * @param bool|array $literalConditions Optional. Either TRUE to process all condition 
     *      SQL literals as literals (ATTENTION: it means TRUE does not apply to field SQL literals!)
     *      or an plain array of literals (both condition and field SQL literals) in conditions. 
     *      E.g.: array('CONCAT(`first_name`, ' ', `last_name`)="..."') or 
     *      array('CONCAT(`first_name`, ' ', `last_name`)=')
     * 
     * @return string Conditions SQL
     */
    public static function getQueryConditions($conditions, $literalConditions = array()){
        $conditionsSQL = '';
        if (!empty($conditions)) {
            $conditions = (array)$conditions;
            foreach ($conditions as $field => $value) {
                // if not an assoc. item 
                // or if negation of nested conditions: 'NOT' => array(...) or 'NOT' => '{stringOfLiteralConditions}'
                $negation = false;
                if (
                    is_int($field) 
                    || ($negation = ($field === 'NOT' || $field === '!'))
                ) {
                    // check for possible logical operator specificaton (negation is ignored here)
                    if ($value === 'OR' || $value == '||') {
                        $logicalOperator = 'OR';
                        continue;
                    }
                    elseif ($value === 'AND' || $value == '&&') {
                        $logicalOperator = 'AND';
                        continue;
                    }
                    // brackets - treat it as separated set od conditions
                    elseif (is_array($value)) {
                        // if the brackets does not contain anything then skip them
                        // This can happen the most probably when implicit conditions are added
                        // and user given conditions are nested into array, but they are 
                        // empty (user provides no conditions)
                        if (empty($value)) {
                            continue;
                        } 
                        $condition = self::getQueryConditions($value, $literalConditions);
                        $condition = '(' . $condition . ')';
                        if ($negation) {
                            $condition = 'NOT' . $condition;
                        }
                    }
                    // direct condition expression kept in its literal form
                    elseif ($literalConditions === true || in_array($value, $literalConditions, true)) {
                        $condition = $value;
                        if ($negation) {
                            $condition = 'NOT(' . $condition . ')';
                        }
                    }
                    // direct condition expression (backticks are applied)
                    else {
                        $condition = self::encloseName((string)$value);
                        if ($negation) {
                            $condition = 'NOT(' . $condition . ')';
                        }
                    }
                }
                // pair field name => required value
                else {
                    // literal field name
                    if (
                        $literalConditions
                        && is_array($literalConditions)
                        && (in_array($field, $literalConditions, true))
                    ) {
                        // treat array value
                        if (is_array($value)) {
                            $value = array_map(array('self', 'escape'), $value);
                            $value = '\'' . implode('\',\'', $value) . '\'';
                            $condition = $field . ' (' . $value . ')';
                        }
                        // treat NULL value
                        elseif ($value === null) {
                            $condition = $field . ' NULL';
                        }
                        // treat boolean TRUE and FALSE
                        elseif (is_bool($value)) {
                            $value = (int)$value;
                            $condition = $field . ' \'' . $value . '\'';
                        }
                        // treat other values
                        else {
                            $value = self::escape((string)$value);
                            $condition = $field . ' \'' . $value . '\''; // !!! the value is always enclosed to '' - as we don't know column data type
                        }
                    }
                    // non-literal field name with operator, e.g. '`my_column` !='
                    elseif (strpos($field, ' ') !== false) {
                        // get and interpret operator
                        $operator = explode(' ', $field);
                        $field = array_shift($operator);
                        $operator = implode(' ', $operator);
                        // enclose and escape field
                        $field = self::encloseName($field);
                        $field = self::escape($field);
                        if (
                            $value === null
                            && $operator === '!='
                        ) {
                            $condition = $field . ' IS NOT NULL';
                        }
                        elseif (
                            is_array($value)
                            && $operator === '!='
                        ) {
                            if (($nullKey = array_search(null, $value, true)) !== false) {
                                unset($value[$nullKey]);
                            }
                            $condition = '';
                            if (!empty($value)) {
                                $value = array_map(array('self', 'escape'), $value);
                                $value = '\'' . implode('\',\'', $value) . '\'';
                                $condition = $field . ' NOT IN (' . $value . ')';
                            }
                            // there is empty array of forbidden values => each value 
                            // can be accepted and this must return TRUE for any value
                            // so simulate it by explicit TRUE condition
                            else {
                                $condition = 'TRUE';
                            }
                            if ($nullKey !== false) {
                                if (empty($condition)) {
                                    $condition = $field . ' IS NOT NULL';
                                }
                                else {
                                    $condition = $condition . ' AND ' . $field . ' IS NOT NULL';
                                }
                            }
                        }
                        else {
                            $multivalueLogicalOperator = 'OR';
                            if ($operator === '~') {
                                $leftOperator = 'LIKE \'';
                                $rightOperator = '\'';
                            }
                            elseif (
                                $operator === '%~' //@deprecated
                                || $operator === '*~'
                            ) {
                                $leftOperator = 'LIKE \'%';
                                $rightOperator = '\'';
                            }
                            elseif (
                                $operator === '~%' //@deprecated
                                || $operator === '~*'
                            ) {
                                $leftOperator = 'LIKE \'';
                                $rightOperator = '%\'';
                            }
                            elseif (
                                $operator === '%~%' //@deprecated
                                || $operator === '*~*'
                            ) {
                                $leftOperator = 'LIKE \'%';
                                $rightOperator = '%\'';
                            }
                            elseif ($operator === '!~') {
                                $leftOperator = 'NOT LIKE \'';
                                $rightOperator = '\'';
                                $multivalueLogicalOperator = 'AND';
                            }
                            elseif (
                                $operator === '!%~' //@deprecated
                                || $operator === '!*~'
                            ) {
                                $leftOperator = 'NOT LIKE \'%';
                                $rightOperator = '\'';
                                $multivalueLogicalOperator = 'AND';
                            }
                            elseif (
                                $operator === '!~%' //@deprecated
                                || $operator === '!~*'
                            ) {
                                $leftOperator = 'NOT LIKE \'';
                                $rightOperator = '%\'';
                                $multivalueLogicalOperator = 'AND';
                            }
                            elseif (
                                $operator === '!%~%' //@deprecated
                                || $operator === '!*~*'
                            ) {
                                $leftOperator = 'NOT LIKE \'%';
                                $rightOperator = '%\'';
                                $multivalueLogicalOperator = 'AND';
                            }
                            else {
                                $leftOperator = $operator . ' \'';
                                $rightOperator = '\'';
                            }
                            // check for multivalue case (= similar case like the IN() case)
                            if (is_array($value)) {
                                $multivalueLogicalOperator = ' ' . $multivalueLogicalOperator . ' ';
                                $condition = '';
                                $value = array_map(array('self', 'escape'), $value);
                                foreach ($value as $subValue) {
                                    if ($condition !== '') {
                                        $condition .= $multivalueLogicalOperator;
                                    }
                                    $condition .= $field . ' ' . $leftOperator . $subValue . $rightOperator;
                                }
                                // if no condition generated (empty array) then skip it
                                if ($condition === '') {
                                    continue;
                                }
                                $condition = '(' . $condition . ')';
                            }
                            else {
                                $value = self::escape((string)$value);
                                $condition = $field . ' ' . $leftOperator . $value . $rightOperator;
                            }
                        }
                    }
                    // non-literal field name without operator
                    else {
                        $field = self::encloseName($field);
                        $field = self::escape($field);
                        // create IN () clause
                        if (is_array($value)) {
                            if (($nullKey = array_search(null, $value, true)) !== false) {
                                unset($value[$nullKey]);
                            }
                            $condition = '';
                            if (!empty($value)) {
                                $value = array_map(array('self', 'escape'), $value);
                                $value = '\'' . implode('\',\'', $value) . '\'';
                                $condition = $field . ' IN (' . $value . ')';
                            }
                            // there is empty array of acceptable values => no value 
                            // can be accepted and this must return FALSE for any value
                            // so simulate it by explicit FALSE condition
                            else {
                                $condition = 'FALSE';
                            }
                            if ($nullKey !== false) {
                                if (empty($condition)) {
                                    $condition = $field . ' IS NULL';
                                }
                                else {
                                    $condition = '(' . $condition . ' OR ' . $field . ' IS NULL)';
                                }
                            }
                        }
                        // create IS NULL
                        elseif ($value === null) {
                            $condition = $field . ' IS NULL';
                        }
                        // treat boolean TRUE and FALSE
                        elseif (is_bool($value)) {
                            $value = (int)$value;
                            $condition = $field . ' = \'' . $value . '\'';
                        }
                        // create plain condition
                        else {
                            $value = self::escape((string)$value);
                            $condition = $field . ' = \'' . $value . '\''; // !!! the value is always enclosed to '' - as we don't know column data type
                        }
                    }
                }
                // set an actual logical operator if not set yet
                if (empty($logicalOperator)) {
                    $logicalOperator = 'AND';
                }
                if ($conditionsSQL === '') {
                    $logicalOperator = '';
                }
                // add condition item
                $conditionsSQL .= $logicalOperator . ' ' . $condition . ' ';
                // reset operator
                $logicalOperator = '';
            }
            $conditionsSQL = trim($conditionsSQL);
        }
        return $conditionsSQL;
    }
    
    
    /**
     * Creates SQL for ORDER BY clause
     * 
     * @param string|array $order Single field order definition, e.g. 'last_name' 
     *      (will be order ASC by default) or 'last_name DESC'. Or an array of orders 
     *      for multiple fields like:
     * 
     *          array(
     *              'last_name',                    // will be order ASC by default
     *              'first_name ASC',
     *              'age' => 'DESC',           
     *              'id' => array(18, 75, ...),     // will be ordered as provided in array       
     *              ...
     *          );
     * 
     * @return string SQL for ORDER BY clause
     */
    protected static function getQueryOrder($order, $literalOrder = array()){
        $order = (array)$order;
        $orderSQL = array();
        foreach ($order as $key => $value) {
            if (is_string($key)) {
                if (is_array($value)) {
                    if (empty($value)) {
                        continue;
                    }
                    $value = array_map(array('self', 'escape'), $value);
                    $value = array_reverse($value);
                    $value = 'FIELD(' . self::encloseName($key) . ', \'' . implode('\', \'', $value) . '\') DESC';
                }
                else {
                    $value = $key . ' ' . $value;
                    $value = self::encloseName($value);
                    $value = self::escape($value);
                }
            }
            elseif ($literalOrder !== true && !in_array($value, $literalOrder, true)) {
                $value = self::encloseName($value);
                $value = self::escape($value);
            }
            $orderSQL[] = $value;
        }
        // if there is multiple ordering then remove duplicit orderings
        if (isset($orderSQL[1])) {
            $orderSQL = array_unique($orderSQL);
        }
        $orderSQL = implode(', ', $orderSQL);
        
        if (!empty($orderSQL)) {
            $orderSQL = 'ORDER BY ' . $orderSQL;
        }
                
        return $orderSQL;
    }
    
    /**
     * Creates SQL for GROUP BY clause
     * 
     * @param string|array $group Single field name or array of such names to group
     *      records by. Array for looks like:
     *          array(
     *              'last_name',        
     *              'age',           
     *              ...
     *          );
     * 
     * @return string SQL for GROUP BY clause
     */
    protected static function getQueryGroup($group, $literalGroup = array()){
        $group = (array)$group;
        $groupSQL = array();
        foreach ($group as $field) {
            if ($literalGroup !== true && !in_array($field, $literalGroup, true)) {
                $field = self::encloseName($field);
                $field = self::escape($field);
            }
            $groupSQL[] = $field ;
        }
        $groupSQL = implode(', ', $groupSQL);
           
        if (!empty($groupSQL)) {
            $groupSQL = 'GROUP BY ' . $groupSQL;
        }
        
        return $groupSQL;
    }
    
    /**
     * Creates SQL for query joins
     * 
     * @param array $joins Array of associative arrays containing joins definitions like: 
     *      array(
     *          array(
     *              'type' => 'left',
     *              'table' => 'my_table_2',
     *              'conditions' => array(
     *                  'my_table.id = `my_table_2`.`my_table_id`', // this must be one string otherwise would be 
     *                                                              // `my_table_2`.`my_table_id` enclosed to aphostrophes
     *                                                              // Backtick must be inserted manually.
     *                  'my_table.locale' => $locale,
     *              ),
     *          ),
     *          array(
     *              'type' => 'left',
     *              'table' => 'my_table_3 AS `t3`',             // alias can be used. Backtick must be inserted manually.
     *              'conditions' => array(
     *                  'my_table_2.id = `t3`.`my_table_2_id`',  // table names and aliases must be set manually in conditions
     *              ),
     *          )
     *          array(
     *              'type' => 'left',
     *              'table' => 'my_table_3',             
     *              'alias' => 't3',                             // alias can be provided separately
     *              'conditions' => array(
     *                  'my_table_2.id = `t3`.`my_table_2_id`',  // table names and aliases must be set manually in conditions
     *              ),
     *          )
     *          array(
     *              'type' => 'left',
     *              'table' => 'my_table_3',             
     *              'alias' => 't3',                             // alias can be provided separately
     *              'select' => array('fields' => ..., 'conditions' => ..., ...) // options to define join sub-select
     *              'conditions' => array(
     *                  'my_table_2.id = `t3`.`my_table_2_id`',  // table names and aliases must be set manually in conditions
     *              ),
     *          )
     *          ...
     *      );
     * @param array $deleteFromTables Optional. If $deleteFromTables is provided, 
     *      then it is filled with all table names / aliases which will participate 
     *      in delete operation. This is done by checking boolean 'delete' parameter 
     *      which is specific for joins definition in DB::getDeleteQuery().
     * 
     * @return string SQL for join clauses
     */
    protected static function getQueryJoins($joins, $literalJoins = array(), &$deleteFromTables = array()){
        $joinsSQL = '';
        $deleteFromTables = array();
        if (!empty($joins)) {
            $joins = (array)$joins;
            // check for a correct joins structure
            if (isset($joins['table'])) {
                throw new Exception("Incorrectly defined joins. Must be defined as array of arrays.");
            }
            foreach ($joins as $i => $join) {
                // get type
                if (empty($join['type'])) {
                    $type = 'JOIN';
                }
                else {
                    $type = strtoupper(trim($join['type'])) . ' JOIN';
                }
                // get table
                if (empty($join['table'])) {
                    throw new Exception("Missing table in join on array index $i");
                }
                if (!empty($join['select'])) {
                    if (empty($join['alias'])) {
                        throw new Exception("Missing sub-select alias in join on array index $i");
                    }
                    $table = '(' . self::getSelectQuery($join['table'], $join['select']) . ')';
                }
                else {
                    $table = self::encloseName($join['table']);
                }
                if (!empty($join['alias'])) {
                    $table .= ' AS ' . self::encloseAlias($join['alias']);
                }
                // get conditions
                if (empty($join['conditions'])) {
                    throw new Exception("Missing conditions in join for table {$joins['table']}");
                }
                $conditions = self::getQueryConditions($join['conditions'], $literalJoins);
                // create join clause
                $joinSQL = $type . ' ' . $table . ' ON (' . $conditions . ')';
                // attach it to all joins
                if ($joinsSQL != '') {
                    $joinsSQL .= ' ';
                }
                $joinsSQL .= $joinSQL;
                // check for 'delete' option
                if (isset($join['delete']) && $join['delete'] === true) {
                    $deleteFromTables[] = self::getName($table);
                }
            }
        }
        return $joinsSQL;
    }
    
    /**
     * Creates limit clause of SQL query
     * 
     * @param int $limit Optional. Defaults to NULL.
     * @param int $offset Optional. Defaults to NULL.
     * @param int $page Optional. This is taken into account only if offset is NULL.
     *          Defaults to NULL.
     * 
     * @return string SQL for LIMIT clause
     */
    protected static function getQueryLimit($limit = null, $offset = null, $page = null) {
        $limitSQL = '';
        if (!empty($limit)) {
            if (!Validate::intNumber($limit)) {
                throw new Exception("Limit must be integer");
            }
            if (!empty($offset)) {
                if (!Validate::intNumber($offset)) {
                    throw new Exception("Offset must be integer");
                }
                $limitSQL = 'LIMIT ' . $offset . ',' . $limit;
            }
            elseif (!empty($page)) {
                if (!Validate::intNumber($page)) {
                    throw new Exception("Page must be integer");
                }
                $offset = ($page - 1) * $limit;
                $limitSQL = 'LIMIT ' . $offset . ',' . $limit;
            }
            else {
                $limitSQL = 'LIMIT ' . $limit;
            }
        }
        return $limitSQL;
    }
    
    /**
     * 
     * @param array $data
     * @param array $options
     * @param array|bool $literalFields
     * @param array|bool $literalData
     * 
     * @return string
     */
    protected static function getQuerySet($data, $options, $literalFields = array(), $literalData = array()) {
        $setSQL = array();
        $fields = self::getDataFields($data, $options);
        foreach($fields as $field) {
            $value = $data[$field];
            if (is_string($field) && !is_array($value)) {
                switch(true) {
                    case $field === 'modified' && empty($value):
                    case $field === 'deleted' && !empty($value):
                        $value = 'NOW()';
                        break;
                    
                    case $value === null:
                        $value = 'NULL';
                        break;
                    
                    case is_bool($value):
                        $value = (int)$value;
                        $value = '\'' . $value . '\'';
                        break;
                    
                    case $literalFields === true || in_array($field, $literalFields, true):
                    case $literalData === true || in_array($value, $literalData, true):
                        break;
                                        
                    default:
                        $value = self::escape((string)$value);
                        $value = '\'' . $value . '\'';
                        break;
                }
                $field = self::encloseName($field);
                $field = self::escape($field);
                $setSQL[] = $field . ' = ' . $value;
            }
        }   
        $setSQL = implode(', ', $setSQL);
        return $setSQL;
    }
    
    /**
     * Creates tables string which can be used in DROP TABLE query
     * 
     * @param string|array $tables Single table name (string) or an array of table
     *      names to create SQL for.
     * 
     * @return string SQL string which can be used in DROP TABLE query
     */
    protected static function getQueryTables($tables) {
        $tables = (array)$tables;
        foreach ($tables as &$table) {
            $table = self::encloseName($table);
        }
        unset($table); // unset reference
        $tablesSQL = implode(', ', $tables);
        return $tablesSQL;
    }
}
class Exception_DB_TablesDuplicitReservation extends Exception {}
class Exception_DB_TablesReservationFailure extends Exception {}
class Exception_DB_QueryError extends Exception {}

/**
 * Exception thrown by DB::query() on recoverable deadlocks. The easest way to resolve 
 * recoverable deadlock is to rollback and repeat the entire transaction again (the whole 
 * transaction! not only the failed statement). 
 * 
 * @see https://dev.mysql.com/doc/refman/5.7/en/innodb-deadlocks-handling.html
 * @see https://dev.mysql.com/doc/refman/5.5/en/error-messages-server.html (find "try restarting transaction")
 */
class Exception_DB_RecoverableDeadlock extends Exception_DB_QueryError {}
