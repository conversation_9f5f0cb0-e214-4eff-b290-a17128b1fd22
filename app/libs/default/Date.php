<?php
class Date {
    
    /**
     * Calculate difference between 2 dates / datetimes (timestamps).
     *
     * @param string $interval Interval the date difference is calculated in. It can be 
     *      one of following values:
     *          - 'yyyy' - Number of full years
     *          - 'q' - Number of full quarters
     *          - 'm' - Number of full months
     *          - 'y' - Difference between day numbers (eg 1st Jan 2004 is "1", 
     *            the first day. 2nd Feb 2003 is "33". The datediff is "-32".)
     *          - 'd' - Number of full days
     *          - 'w' - Number of full weekdays
     *          - 'ww' - Number of full weeks
     *          - 'h' - Number of full hours
     *          - 'n' - Number of full minutes
     *          - 's' - Number of full seconds
     * @param mixed $datefrom Date, datetime string or timestamp integer
     * @param mixed $dateto Date, datetime string or timestamp integer
     * @param boolean $using_timestamps True if $datefrom and $dateto are timestamps
     *
     * @return integer Result is positive integer if $datefrom is older then $dateto,
     *      e.g. Date::getDiff('d', '11.5.2011', '13.5.2011') returns 2. Result is 
     *      negative integer if $datefrom is newer then $dateto. e.g. Date::getDiff('d', '2011-05-13', '11.5.2011')
     *      returns -2.
     */
    public static function getDiff($interval, $datefrom, $dateto, $using_timestamps = false) {
    
        if (!$using_timestamps) {
            $datefrom = strtotime($datefrom, 0);
            $dateto = strtotime($dateto, 0);
        }
        $difference = $dateto - $datefrom; // Difference in seconds
        
        switch($interval) {
        
        case 'yyyy': // Number of full years

            $years_difference = floor($difference / 31536000);
            if (mktime(date("H", $datefrom), date("i", $datefrom), date("s", $datefrom), date("n", $datefrom), date("j", $datefrom), date("Y", $datefrom)+$years_difference) > $dateto) {
                $years_difference--;
            }
            if (mktime(date("H", $dateto), date("i", $dateto), date("s", $dateto), date("n", $dateto), date("j", $dateto), date("Y", $dateto)-($years_difference+1)) > $datefrom) {
                $years_difference++;
            }
            $datediff = $years_difference;
            break;

        case "q": // Number of full quarters

            $quarters_difference = floor($difference / 8035200);
            while (mktime(date("H", $datefrom), date("i", $datefrom), date("s", $datefrom), date("n", $datefrom)+($quarters_difference*3), date("j", $dateto), date("Y", $datefrom)) < $dateto) {
                $months_difference++;
            }
            $quarters_difference--;
            $datediff = $quarters_difference;
            break;

        case "m": // Number of full months

            $months_difference = floor($difference / 2678400);
            while (mktime(date("H", $datefrom), date("i", $datefrom), date("s", $datefrom), date("n", $datefrom) + ($months_difference), date("j", $dateto), date("Y", $datefrom)) < $dateto) {
                $months_difference++;
            }
            $months_difference--;
            $datediff = $months_difference;
            break;

        case 'y': // Difference between day numbers

            $datediff = date("z", $dateto) - date("z", $datefrom);
            break;

        case "d": // Number of full days

            $datediff = floor($difference / 86400);
            break;

        case "w": // Number of full weekdays

            $days_difference = floor($difference / 86400);
            $weeks_difference = floor($days_difference / 7); // Complete weeks
            $first_day = date("w", $datefrom);
            $days_remainder = floor($days_difference % 7);
            $odd_days = $first_day + $days_remainder; // Do we have a Saturday or Sunday in the remainder?
            if ($odd_days > 7) { // Sunday
                $days_remainder--;
            }
            if ($odd_days > 6) { // Saturday
                $days_remainder--;
            }
            $datediff = ($weeks_difference * 5) + $days_remainder;
            break;

        case "ww": // Number of full weeks

            $datediff = floor($difference / 604800);
            break;

        case "h": // Number of full hours

            $datediff = floor($difference / 3600);
            break;

        case "n": // Number of full minutes

            $datediff = floor($difference / 60);
            break;

        default: // Number of full seconds (default)

            $datediff = $difference;
            break;
        }    

        return $datediff;
    }    
    
    /**
     * Converts between typical date formats.
     * 
     * @param string $date
     * @param string $fromFormat One of 'dmy', 'mdy' or 'ymd'. Defaults to 'dmy'.
     * @param string $toFormat One of 'dmy', 'mdy' or 'ymd'. Defaults to 'ymd'.
     * @param string $separator Output date parts separator. Defaults to '-'.
     * @param bool $strict If TRUE then the $date must be exact date string, e.g. '12.1.2010'.
     *          If FALSE then the $date can be provided together with other text, e.g. 'On sunday 12.1.2010 at 12:00'
     *          Defaults to TRUE.
     * 
     * @return mixed Normalized date string. FALSE on failure
     */
    public static function convertDate($date, $fromFormat = 'dmy', $toFormat = 'ymd', $separator = '-', $strict = true) {
        switch ($fromFormat) {
            case 'dmy': 
                $regex = '(\d{1,2})[^\d]*(\d{1,2})[^\d]*(\d{4})';
                $d = 1;
                $m = 2;
                $y = 3;
                break;
            
            case 'mdy': 
                $regex = '(\d{1,2})[^\d]*(\d{1,2})[^\d]*(\d{4})';
                $d = 2;
                $m = 1;
                $y = 3;
                break;
            
            case 'ymd': 
                $regex = '(\d{4})[^\d]*(\d{1,2})[^\d]*(\d{1,2})';
                $d = 3;
                $m = 2;
                $y = 1;
                break;
            
            default:
                throw new Exception("Invalid \$fromFormat $fromFormat");
                break;
        }
        if ($strict) {
            $regex = "^{$regex}$";
        }
        $regex = "/{$regex}/";
        
        if (!preg_match($regex, trim($date), $match)) {
            return false;
        } 
        
        switch ($toFormat) {
            case 'dmy': 
                $date = str_pad($match[$d], 2, '0', STR_PAD_LEFT) 
                    . $separator . str_pad($match[$m], 2, '0', STR_PAD_LEFT) 
                    . $separator . $match[$y];
                break;
            
            case 'mdy': 
                $date =  str_pad($match[$m], 2, '0', STR_PAD_LEFT)
                    . $separator . str_pad($match[$d], 2, '0', STR_PAD_LEFT) 
                    . $separator . $match[$y];
                break;
            
            case 'ymd': 
                $date = $match[$y] 
                    . $separator . str_pad($match[$m], 2, '0', STR_PAD_LEFT) 
                    . $separator . str_pad($match[$d], 2, '0', STR_PAD_LEFT);
                break;
            
            default:
                throw new Exception("Invalid \$toFormat $toFormat");
                break;
        }
        
        return $date;
    }    
    
    /**
     * Converts typical datetime formats to normalized form 'yyyy-mm-dd hh:ii:ss'
     * Possible datetime formats are 2011-02-03T12:05:45 or 2011-02-03 12:05:45.
     * 
     * @param string $datetime
     * @param string $output One of 'datetime', 'date' or 'time'. Defaults to 'datetime'.
     * 
     * @return mixed Normalized datetime, date or time string. FALSE on failure
     */
    public static function normalize($datetime, $output = 'datetime') {
        $regex = '/^(\d{4}-\d\d-\d\d)(?:T|\s)(\d\d:\d\d:\d\d)/';
        
        if (!preg_match($regex, $datetime, $match)) {
            return false;
        }
        
        switch ($output) {
            case 'datetime': 
                return "{$match[1]} {$match[2]}";
                        
            case 'date': 
                return $match[1];
                        
            case 'time': 
                return $match[2];
                        
            default:
                throw new Exception("Invalid output $output");
                break;
        }
    }    
    
    /**
     * Formates $date according to provided $format.
     * 
     * ATTENTION: If formating to 'Y-m-d' you will get following output:
     * '28.2.2015' -> '2015-02-28';
     * '29.2.2015' -> '2015-03-01';
     * '30.2.2015' -> '2015-03-02';
     * '31.2.2015' -> '2015-03-03';
     * '32.2.2015' -> NULL;
     * In case of time formating (H:i) you will get NULL for any invalid input:
     * '25:01' -> NULL;
     * '15:60' -> NULL;
     * If you combine invalid date and/or time to datetime, the result is also NULL.
     * 
     * NOTE: You can use following hack to get a month-last-day date: Date::format('{anyDateInTheMonth}', 'Y-m-t').
     * E.g. Date::format('1.2.2020', 'Y-m-t') returns '2020-02-29'. The 't' gives days count in specified month.
     * 
     * @param string|int $date Date to be formated. If an integer (strictly) then 
     *          considered for Unix timestamp.
     * @param string $format Optional. Output date format. Use e.g. 'Y-m-d H:i:s' for
     *          MySQL datetime format, or 'j.n.Y G:i' for slovak datetime. For more formating
     *          options see date() php manual. Defaults to 'Y-m-d'.
     * 
     * @return string Formated date. If invalid date provided then NULL.
     */
    public static function format($date, $format = 'Y-m-d') {
        if (!is_int($date)) {
            $date = strtotime($date);
        }
        // if invalid date then return NULL
        if ($date === false || $date === -1) {
            return null;
        }
        return date($format, $date);
    }
    
    /**
     * Returns MySQL datetime string for actual or for provided date (and time)
     * 
     * @param string|int $date Optional. Date to be formated to MySQL datetime.
     *          If not provided then actual date (and time) is used.
     *          If an integer (strictly) then considered for Unix timestamp.
     *          Defaults to NULL
     * 
     * @return string
     */
    public static function getMySqlDatetime($date = null) {
        if (!$date) {
            $date = time();
        }
        return self::format($date, 'Y-m-d H:i:s');
    }
    
    /**
     * Returns MySQL date string for actual or for provided date (and time)
     * 
     * @param string|int $date Optional. Date to be formated to MySQL date.
     *          If not provided then actual date (and time) is used.
     *          If an integer (strictly) then considered for Unix timestamp.
     *          Defaults to NULL
     * 
     * @return string
     */
    public static function getMySqlDate($date = null) {
        if (!$date) {
            $date = time();
        }
        return self::format($date, 'Y-m-d');
    }
    
    /**
     * Returns date of the specified day for specified week
     * 
     * @param integer $year
     * @param integer $week
     * @param array $options Following are available:
     *      - 'dayOfWeek' (integer). Values 0 - 6 (Sunday - Saturday). 
     *          Defaults to 1 (Monday).
     *      - 'format' (string) Defaults to 'Y-m-d'.
     * 
     * @return string
     */
    public static function getWeekDate($year, $week, $options = array()) {
        $options = array_merge(array(
            'dayOfWeek' => 1,
            'format' => 'Y-m-d',
        ), $options);
        $date = new DateTime();
        $date->setISODate($year, $week, $options['dayOfWeek']);
        return $date->format($options['format']);
    }
    
    /**
     * Returns slovak date string (with month name, e.g. "1. jún 2022") for actual 
     * or for provided date (and time)
     * 
     * @param string|int $date Optional. Date to be formated to slovak date.
     *          If not provided then actual date (and time) is used.
     *          If an integer (strictly) then considered for Unix timestamp.
     *          Defaults to NULL
     * 
     * @return string
     */
    public static function getSlovakDate($date = null) {
        static $slovakMonths = null;
        if (!$slovakMonths) {
            $slovakMonths = array(
                1 => __(__FILE__, 'január'),
                2 => __(__FILE__, 'február'),
                3 => __(__FILE__, 'marec'),
                4 => __(__FILE__, 'apríl'),
                5 => __(__FILE__, 'máj'),
                6 => __(__FILE__, 'jún'),
                7 => __(__FILE__, 'júl'),
                8 => __(__FILE__, 'august'),
                9 => __(__FILE__, 'september'),
                10 => __(__FILE__, 'október'),
                11 => __(__FILE__, 'november'),
                12 => __(__FILE__, 'december'),
            );
        }
        if (!$date) {
            $date = time();
        }
        $date = self::format($date, 'j.n.Y');
        if ($date) {
            $dateParts = explode('.', $date);
            $dateParts[0] = $dateParts[0] . '.';
            $dateParts[1] = $slovakMonths[$dateParts[1]];
            $date = implode(' ', $dateParts);
        }
        return $date;
    }
}
