<?php
/**
 * @class Utility
 */
class Utility {
                        
    /**
     * Used by startTimer() and getTimer()
     * @var array 
     */
    private static $timer;
    
    /**
     * Used by startTimer() and getTimer()
     * @var string 
     */
    private static $timerKey = null;
    
    /**
     * Starts timer. Useful for debugging and performance analysis. 
     * 
     * @param string $key Optional. If specified, timer will be keyed with this string.
     *      Useful for running multiple timers at once. 
     * @return void
     */
    static public function startTimer($key = null) {
        if ($key === null) {
            $key = uniqid();
        }
        if (self::$timerKey) {
            unset(self::$timer[self::$timerKey]);
        }
        self::$timerKey = $key;
        self::$timer[$key] = microtime(true);
        return true;
    }
    
    /**
     * This is a companion method to startTimer().
     * 
     * @param string $key Optional. If specified, this is the timer that will be stopped and
     *      printed. Otherwise, the last timer that was started will be shown.
     * @param mixed $debug Optional. Bool or string message to print in debug. 
     *      Defaults to TRUE.
     * @return mixed Number of milliseconds elapsed from starting the timer or NULL
     *      if no timer has been started.
     */
    static public function getTimer($key = null, $debug = true) {
        if ($key === null) {
            $key = self::$timerKey;
        }
        if (empty(self::$timer[$key])) {
            App::debug('You did not start this timer yet (' . $key . ').');
            return NULL;
        }
        $time = round(1000 * (microtime(true) - self::$timer[$key]), 2);
        
        if ($debug) {
            $message = 'Timer';
            $timeMessage = $time . ' millisecs';
            if (is_string($debug)) {
                $message .= " ($debug)";
                
            }
            $message .= ': ';
            App::debug($timeMessage, $message . $time . ' millisec', array('backtraceLevel' => 1));
        }
        return $time;
    }
    
    /**
     * Formats a number in bytes into appropriate units so it is more
     * readable. The unit symbol is attached.
     * 
     * @param int $bytes 
     * @static
     * @access public
     * @return string
     */
    static function formatBytes($bytes) {
        $units = array('B','kB','MB','GB','TB','PB');
        $c = 0;
        while ($bytes >= 1024) {
            $c++;
            $bytes = $bytes/1024;
        }
        return Number::removeTrailingZeroDecimals(
                App::formatNumber($bytes, ($c ? 1 : 0), '.')
        ) . ' ' . $units[$c];
    }
    
    /**
     * Converts the bytes size string with provided units to plain bytes number,
     * e.g. 40MB to 41943040. If an integer number is provided then it is considered 
     * to be bytes number and it is returned (after casting to int) as it is.
     * 
     * @param string|int $size Bytes size string with provided units or plain integer.
     * 
     * @return int Number on bytes. If the provided string cannot be parsed as bytes
     *      size with units then NULL is returned.
     */
    static function convertToBytes($size) {
        if (!Validate::intNumber($size)) {
            $size = preg_replace('/\s/', '', $size);
            if (preg_match('/^([0-9]+(?:[\.\,][0-9]+)?)([BKMGTP])[a-z]*$/i', $size, $match)) {
                $size = (float)str_replace(',', '.', $match[1]);
                $unit = strtoupper($match[2]);
                foreach (array('B', 'K', 'M', 'G', 'T', 'P') as $u) {
                    if ($u === $unit) {
                        break;
                    }
                    $size *= 1024;
                }
                $size = round($size);
            }
            else {
                $size = null;
            }
        }
        return (int)$size;
    }
    
    /**
     * Returns humanized version of time period. E.g. (for default 'periodInterval'
     * and 'declension'):
     * 
     *      0.5   -> 12 hodín
     *      1     -> 24 hodín
     *      4     -> 4 dni
     *      5     -> 5 dní
     *      14    -> 2 týždne
     *      38.55 -> 5 týždňov a 3 dni a 13 hodín a 12 minút
     *      36    -> 5 týždňov a 24 hodín (@todo -> 5 týždňov a 1 deň)
     * 
     * @param int|float $period Time period, e.g. 23 (days)
     * @param array $options Following are available:
     *      - 'periodInterval' (string) Interval (units) of the provided $period.
     *          Possible values are 'weeks', 'days', 'hours', 'minutes', 'seconds'.
     *          Defaults to 'days'.
     *      - 'declension' (int) Gramatical declension. Possible values are 1-7
     *          (nominative - instrumental). Defaults to 1 (nominative).
     * 
     * @return string Humanized version of provided time period
     * 
     * @throws Exception on invalid 
     */
    public static function humanizeTimePeriod($period, $options = array()) {
        $options = array_merge(array(
            'periodInterval' => 'days',
            'declension' => 1,
        ), $options);
        $periodInterval = strtolower($options['periodInterval']);
        $periodInterval = substr($periodInterval, 0, 2);
        $declension = (int)$options['declension'];
        if ($declension > 2) {
            throw new Exception('Unimplemented declension %s', $declension);
        }
        $humanizedPeriod = '';
        // days
        if ($periodInterval === 'da') {
            $days = $period;
            if ($days <= 2) {
                $hours = $days * 24;
                $wholeHours = (int)$hours;
                $minutesFraction = $hours - $wholeHours;
                if ($wholeHours > 4) {
                    if ($declension === 1) {
                        $humanizedPeriod = __(__FILE__, '%s hodín', $wholeHours);
                    }
                    elseif ($declension === 2) {
                        $humanizedPeriod = __(__FILE__, '%s hodín', $wholeHours);
                    }
                }
                elseif ($wholeHours > 1) {
                    if ($declension === 1) {
                        $humanizedPeriod = __(__FILE__, '%s hodiny', $wholeHours);
                    }
                    elseif ($declension === 2) {
                        $humanizedPeriod = __(__FILE__, '%s hodín', $wholeHours);
                    }
                }
                else {
                    if ($declension === 1) {
                        $humanizedPeriod = __(__FILE__, 'hodina', $wholeHours);
                    }
                    elseif ($declension === 2) {
                        $humanizedPeriod = __(__FILE__, 'hodiny', $wholeHours);
                    }
                }
                if ($minutesFraction) {
                    $wholeMinutes = round($minutesFraction * 60);
                    if ($wholeMinutes > 4) {
                        if ($declension === 1) {
                            $humanizedMinutes = __(__FILE__, '%s minút', $wholeMinutes);
                        }
                        elseif ($declension === 2) {
                            $humanizedMinutes = __(__FILE__, '%s minút', $wholeMinutes);
                        }
                    }
                    elseif ($wholeMinutes > 1) {
                        if ($declension === 1) {
                            $humanizedMinutes = __(__FILE__, '%s minúty', $wholeMinutes);
                        }
                        elseif ($declension === 2) {
                            $humanizedMinutes = __(__FILE__, '%s minút', $wholeMinutes);
                        }
                    }
                    else {
                        if ($declension === 1) {
                            $humanizedMinutes = __(__FILE__, 'minúta', $wholeMinutes);
                        }
                        elseif ($declension === 2) {
                            $humanizedMinutes = __(__FILE__, 'minúty', $wholeMinutes);
                        }
                    }
                    $humanizedPeriod .= ' ' . __(__FILE__, 'a') . ' ' . $humanizedMinutes;
                }
            }
            elseif (fmod($days, 7) == 0) {
                $wholeWeeks = $days / 7; 
                if ($wholeWeeks > 4) {
                    if ($declension === 1) {
                        $humanizedPeriod = __(__FILE__, '%s týždňov', $wholeWeeks);
                    }
                    elseif ($declension === 2) {
                        $humanizedPeriod = __(__FILE__, '%s týždňov', $wholeWeeks);
                    }
                }
                elseif ($wholeWeeks > 1) {
                    if ($declension === 1) {
                        $humanizedPeriod = __(__FILE__, '%s týždne', $wholeWeeks);
                    }
                    elseif ($declension === 2) {
                        $humanizedPeriod = __(__FILE__, '%s týždňov', $wholeWeeks);
                    }
                }
                else {
                    if ($declension === 1) {
                        $humanizedPeriod = __(__FILE__, 'týždeň', $wholeWeeks);
                    }
                    elseif ($declension === 2) {
                        $humanizedPeriod = __(__FILE__, 'týždňa', $wholeWeeks);
                    }
                }
            }
            else {
                $wholeDays = (int)$days;
                $hoursFraction = $days - $wholeDays;
                if ($wholeDays > 30) {
                    $weeks = $wholeDays / 7;
                    $wholeWeeks = (int)$weeks;
                    $wholeDaysFraction = $wholeDays - $wholeWeeks * 7;
                    $humanizedPeriod = self::humanizeTimePeriod($wholeWeeks * 7, $options);
                    if ($wholeDaysFraction) {
                        $humanizedDays = self::humanizeTimePeriod($wholeDaysFraction, $options);
                        $humanizedPeriod .= ' ' . __(__FILE__, 'a') . ' ' . $humanizedDays;
                    }
                }
                elseif ($wholeDays > 4) {
                    if ($declension === 1) {
                        $humanizedPeriod = __(__FILE__, '%s dní', $wholeDays);
                    }
                    elseif ($declension === 2) {
                        $humanizedPeriod = __(__FILE__, '%s dní', $wholeDays);
                    }
                }
                elseif ($wholeDays > 1) {
                    if ($declension === 1) {
                        $humanizedPeriod = __(__FILE__, '%s dni', $wholeDays);
                    }
                    elseif ($declension === 2) {
                        $humanizedPeriod = __(__FILE__, '%s dní', $wholeDays);
                    }
                }
                else {
                    if ($declension === 1) {
                        $humanizedPeriod = __(__FILE__, 'deň', $wholeDays);
                    }
                    elseif ($declension === 2) {
                        $humanizedPeriod = __(__FILE__, 'dňa', $wholeDays);
                    }
                }
                if ($hoursFraction) {
                    $humanizedHours = self::humanizeTimePeriod($hoursFraction, $options);
                    $humanizedPeriod .= ' ' . __(__FILE__, 'a') . ' ' . $humanizedHours;
                }
            }
        }
        // weeks
        elseif ($periodInterval === 'we') {
            $humanizedPeriod = self::humanizeTimePeriod($period * 7, array_merge(
                $options, array(
                    'periodInterval' => 'days',
            )));
        }
        // hours
        elseif ($periodInterval === 'ho') {
            $humanizedPeriod = self::humanizeTimePeriod($period / 24, array_merge(
                $options, array(
                    'periodInterval' => 'days',
            )));
        }
        // minutes
        elseif ($periodInterval === 'mi') {
            $humanizedPeriod = self::humanizeTimePeriod($period / (24 * 60), array_merge(
                $options, array(
                    'periodInterval' => 'days',
            )));
        }
        // seconds
        elseif ($periodInterval === 'se') {
            $humanizedPeriod = self::humanizeTimePeriod($period / (24 * 60 * 60), array_merge(
                $options, array(
                    'periodInterval' => 'days',
            )));
        }
        else {
            throw new Exception('Unimplemented period interval %s', $options['periodInterval']);
        }
        
        return $humanizedPeriod;
    }
               
    /**
     * Gets langs of current request ordered accoding to weights.
     * Most browsers send an Accept-Language header, e.g.: 
     * 
     *      en-ca,en;q=0.8,en-us;q=0.6,de-de;q=0.4,de;q=0.2
     * 
     * See $_SERVER['HTTP_ACCEPT_LANGUAGE'] in php manual.
     * 
     * @return array Array of detected languages like:
     * 
     *      array (
     *          'en-ca' => 1,
     *          'en' => 0.8,
     *          'en-us' => 0.6,
     *          'de-de' => 0.4,
     *          'de' => 0.2,
     *      )
     */
    static public function getRequestLangs() {
        $langs = array();
        if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            // break up string into pieces (languages and q factors)
            preg_match_all('/([a-z]{1,8}(-[a-z]{1,8})?)\s*(;\s*q\s*=\s*(1|0\.[0-9]+))?/i', $_SERVER['HTTP_ACCEPT_LANGUAGE'], $matches);
            if (count($matches[1])) {
                // create a list like "en" => 0.8
                $matches[1] = array_map('strtolower', $matches[1]);
                $langs = array_combine($matches[1], $matches[4]);
                // set default to 1 for any without q factor
                foreach ($langs as $lang => $val) {
                    if ($val === '') {
                        $langs[$lang] = 1;
                    }
                }
                // sort list based on value	
                arsort($langs, SORT_NUMERIC);
            }
        }
        return $langs;
    }    
                 
    /**
     * Converts ISBN to EAN
     * 
     * @param string $isbn ISBN10 or ISBN13 code
     *  
     * @return string EAN
     */
    static public function convertIsbnToEan($isbn) {
        $isbn = trim($isbn);
        $isbn = str_replace('-', '', $isbn);
        $isbn = str_replace(' ', '', $isbn);
        $isbn = str_pad($isbn, 10, '0', STR_PAD_LEFT);
        if (
            !preg_match('/^[0-9]{10,}$/', $isbn)
            || strlen($isbn) === 13
        ) {
            return $isbn;
        }
        if (strlen($isbn) === 12) { // if number is UPC just add zero
            $ean = '0' . $isbn;
        }
        else {
            $isbn2 = substr("978" . trim($isbn), 0, -1);
            $sum13 = self::getEanChecksum($isbn2);
            $ean = $isbn2 . $sum13;
        }
        return $ean;
    }   

    /**
     * Gets EAN code checksum 
     * 
     * @param string $ean
     * 
     * @return int
     */
    static public function getEanChecksum($ean) {
        $ean = trim($ean);
        if (strlen($ean) == 13) {
            $ean = substr($ean, 0, 12);
        }
        $tb = 0;
        for ($i = 0; $i <= 12; $i++) {
            $tc = (int)substr($ean, -1, 1);
            $ean = (string)substr($ean, 0, -1);
            $ta = ($tc * 3);
            $tci = (int)substr($ean, -1, 1);
            $ean = (string)substr($ean, 0, -1);
            $tb = $tb + $ta + $tci;
        }
        $tg = ($tb / 10);
        $tint = intval($tg);
        if ($tint == $tg) {
            return 0;
        }
        $ts = substr($tg, -1, 1);
        $tsum = (10 - $ts);
        return $tsum;
    }
           
    /**
     * Adds get params to URLs in provided html code.
     * 
     * This can be used e.g. to add UTM tags to URLs in mailer campaign 
     * (see http://webova-analytika.robertnemec.com/analytics-tagovani-newsletteru/)
     * 
     * @param string $html
     * @param array $getParams
     * @return string
     */
    static public function addGetParamsToUrlsInHtml($html, $getParams) {
        preg_match_all('/<a[^a-z>][^>]*href=[\'"]([^\'"]+)[\'"][^>]*>/i', $html, $matches, PREG_SET_ORDER);
        $replaces = array();
        foreach ($matches as $match) {
            $url = $match[1];
            // skip inserts 
            if (preg_match('/^:[a-z0-9_]+:$/i', $url)) {
                continue;
            }
            $parsedUrl = App::parseUrl($url, array(
                'parseGetParams' => true,
            ));
            $parsedUrl['get'] = array_merge($parsedUrl['get'], $getParams);
            $replaces[$url] = App::getUrl($parsedUrl);
        }
        return str_replace(array_keys($replaces), $replaces, $html);
    }
    
    /**
     * Parses conditioned value string into array.
     * 
     * E.g. string '12;slovart:5;albatros:6' can represent markup rates according 
     * to suppliers and it is parsed into array like:
     * 
     *      array(
     *          // the default (if provided) value has no condition
     *          0 => '12',
     *          'slovart' => '5',
     *          'albatros' => '6',
     *      )
     * 
     * E.g. string '1/3; 768px:0.5; 480px:100%' can represent responsive widths
     * and it is parsed into array like:
     * 
     *      array(
     *          // the default (if provided) value has no condition
     *          0 => '1/3',
     *          '768' => '0.5',
     *          '480' => '100%',
     *      )
     * 
     * @param string $conditionedValue Conditioned value string, e.g. '12;slovart:5;albatros:6'
     *      or '1/3; 768px:0.5; 480px:100%'. If provided string is invalid then an exception 
     *      is thrown. If an empty string is provided then an empty array() is returned.
     * @param array $options Following are available:
     *      - 'allowConditions' (string|array) To specify concrete conditions you can provide 
     *          a regex string or an array of such regex strings. Defaults to NULL, 
     *          it means any condition is allowed. ATTENTION: In regex strings do not use delimiters! 
     *          They are set internaly to slashes '/', so if you use slash character in your regex, 
     *          you must to escape it.
     *      - 'allowValues' (string|array) To specify concrete values you can provide 
     *          a regex string or an array of such regex strings. Defaults to NULL, 
     *          it means any value is allowed. ATTENTION: In regex strings do not use delimiters! 
     *          They are set internaly to slashes '/', so if you use slash character in your regex, 
     *          you must to escape it.
     *      - 'exceptionOnFailure' (bool) If TRUE then on failure an exception is thrown.
     *          If FALSE then invalid items are skipped. Defaults to FALSE.
     * 
     * @return array The above described array.
     * @throws Exception
     */
    static public function parseConditionedValue($conditionedValue, $options = array()) {
        // ATTENTION: Keep this code in synchro with Html::parseResponsiveCssValue()
        $defaults = array(
            'allowConditions' => null,
            'allowValues' => null,
            'exceptionOnFailure' => false,
        );
        $options = array_merge($defaults, $options);
        
        if ($options['allowConditions'] !== null) {
            $options['allowConditions'] = (array)$options['allowConditions'];
            // convert to regex
            $options['allowConditions'] = '/' . implode('|', $options['allowConditions']) . '/i';
        }
        if ($options['allowValues'] !== null) {
            $options['allowValues'] = (array)$options['allowValues'];
            // convert to regex
            $options['allowValues'] = '/' . implode('|', $options['allowValues']) . '/i';
        }
        $parsedValue = array();
        $valuesParts = array_map('trim', Str::explode(';', $conditionedValue));
        $defaultValueIndex = null;
        foreach ($valuesParts as $i => $valuesPart) {
            if ($valuesPart === '') {
                if (!$options['exceptionOnFailure']) {
                    continue;
                }
                elseif ($i === 0) {
                    throw new Exception(__e(__FILE__, 'Prvá položka je prázdna, na začiatku je bodkočiarka navyše'));
                }
                elseif ($i === (count($valuesParts) - 1)) {
                    throw new Exception(__e(__FILE__, 'Posledná položka je prázdna, na konci je bodkočiarka navyše', $i + 1));
                }
                throw new Exception(__e(__FILE__, '%s. položka je prázdna, nasledujú dve bodkočiarky za sebou', $i + 1));
            }
            if (strpos($valuesPart, ':') === false) {
                if ($defaultValueIndex !== null) {
                    if (!$options['exceptionOnFailure']) {
                        continue;
                    }
                    throw new Exception(__e(
                        __FILE__, 
                        'Nesprávne zadaná %s. položka "%s". Zadajte podmienku a za dvojbodkou hodnotu.', 
                        $i + 1,
                        $valuesPart
                    ));
                }
                $defaultValueIndex = $i;
                $condition = null;
                $value = $valuesPart;
            }
            else {
                $valueParts = array_map('trim', explode(':', $valuesPart));
                $condition = array_shift($valueParts);
                $value = implode(':', $valueParts);
            }
            // parse screen width
            if ($condition !== null) {
                if ($options['allowConditions']) {
                    if (!preg_match($options['allowConditions'], $condition)) {
                        if (!$options['exceptionOnFailure']) {
                            continue;
                        }
                        elseif (
                            strpos($condition, ';') === false
                            && strpos($condition, ',') !== false
                        ) {
                            throw new Exception(__e(
                                __FILE__, 
                                'Nesprávne zadaná podmienka "%s" v %s. položke "%s". Na oddelenie položiek sa používa bodkočiarka', 
                                $condition, 
                                $i + 1,
                                $valuesPart
                            ));
                        }
                        throw new Exception(__e(
                            __FILE__, 
                            'Nesprávne zadaná podmienka "%s" v %s. položke "%s"', 
                            $condition, 
                            $i + 1,
                            $valuesPart
                        ));
                    }
                }
            }
            if ($options['allowValues']) {
                if (!preg_match($options['allowValues'], $value)) {
                    if (!$options['exceptionOnFailure']) {
                        continue;
                    }
                    elseif (
                        strpos($value, ':') !== false
                        && strpos($value, ',') !== false
                    ) {
                        throw new Exception(__e(
                            __FILE__, 
                            'Nesprávne zadaná hodnota "%s" v %s. položke "%s". Na oddelenie položiek sa používa bodkočiarka', 
                            $value, 
                            $i + 1,
                            $valuesPart
                        ));
                    }
                    throw new Exception(__e(
                        __FILE__, 
                        'Nesprávne zadaná hodnota "%s" v %s. položke "%s"', 
                        $value, 
                        $i + 1,
                        $valuesPart
                    ));
                }
            }
            // create array of parsed values
            if ($condition === null) {
                $parsedValue[0] = $value;
            }
            else {
                $parsedValue[$condition] = $value;
            }
        }
        return $parsedValue;
    }

    /**
     * Conveniece method solving case where explode(',', '') returns
     * array(''). This method returns array()
     *
     * @param string $delimiter
     * @param string $string
     * @param int $limit Optional
     * 
     * @return array|bool See explode() 
     */
    public static function explode($delimiter, $string, $limit = null) {
        if (trim($string) == '') {
            return array();
        }
        if (func_num_args() > 2) {
            return explode($delimiter, $string, $limit);
        }
        return explode($delimiter, $string);
    }
}