<?php
/**
 * Model class 
 * 
 * TRANSLATIONS:
 * For proper functionality of translations the field names contained in fields/conditions/order/group
 * options of find/save/update/delete methods must be qualified. It means that 
 * instead of 'fields' => array('name', 'description') you must use 'fields' => array('MyModel.name', 'MyModel.description')
 * or instead of 'conditions' => array('name LIKE %xxx%') you must use 'conditions' => array('MyModel.name LIKE %xxx%').
 * Multiple models can be used in select/update/delete methods, e.g. fields' => array('MyModelA.name', 'MyModelB.name').
 * Non standard fields/conditions/order/group must be created manually
 * 
 * To treat special translation cases use method Model::getTranslationField() to get
 * the translation field name, then create your query part manually and put it to
 * literals.
 */
class Model extends ModuleObject {
    
    /**
     * CONFIGURATION PROPERTIES
     */
    
    /**
     * Name of database table which is used by model.
     * 
     * Set this in all models which are extending this class!
     * 
     * If nothing defined then the model DB functions will not work.
     * But even then the model can be used for validation, normalization and 
     * any other custom functionality related to modeled object or process.
     *
     * @var string 
     */
    protected $table = null;
    
    /**
     * Database table fields definitions array like:
     *      array(
     *          '{fieldName01}' => array(
     *              'type' => 'float', (string)
     *              'length' => 6.2 (int|float)
     *              'values' => array('emun_fist_value', 'enum_second_value, ...) (array)
     *              'default' => null (mixed)
     *              'null' => true (bool)
     *              'autoIncrement' => false (bool)
     *              'index' => 'primary'|'unique'|'index' (string)
     *              'comment' => 'My comment for table field' (string)
     *              'collation' => 'utf8_general_ci' (string)
     *          ),
     *          '{fieldName01}' => array(...),
     *          ...
     *      )
     * 
     * Field type can be any of SQL data types (INT, CHAR, VARCHAR, DATE, ... )
     * 
     * NOTE: It is normalized in constructor, e.g. if 'default' is NULL then 'null' is set to TRUE.
     * 
     * @var array 
     */
    protected $schema = array();
    
    /**
     * Database table indexes definitions array like:
     * 
     *      array(
     *          array(
     *              'type' => 'index', 
     *              'fields' => array('fieldName01', 'fieldName02'), 
     *              'name' => 'MyIndex01' // optional
     *          ),
     *          array(
     *              'type' => 'primary', 
     *              'fields' => array('id'), 
     *              'name' => 'PRIMARY' // optional
     *          ),
     *          ...
     *      );
     * 
     * The 'type' can be set to 'primary', 'unique' and 'index'
     * 
     * NOTE: Some one-column indexes can be defined directly in schema
     *
     * @var array 
     */
    protected $tableIndexes = array();
    
    /**
     * Primary key name of database table
     * 
     * Defaults to 'id'.
     * 
     * @var string 
     */
    protected $primaryKey = 'id';
    
    /**
     * Field which keeps record name.
     * 
     * NOTE: Not all models must have defined name field, e.g. HABTM model don't need it.
     * 
     * Defaults to NULL.
     * 
     * @var string
     */
    protected $nameField = null;
    
    /**
     * Name of field used to do soft delete of records (= to mark records which are deleted).
     * Soft delete is possible only if the Model::$deletedField is present is Model::$schema.
     * See also Model::$allowsSoftDelete. If soft delete is not allowed by model (
     * deleted field is not present in model schema) then value of this property is set
     * by constructor to NULL.
     * 
     * If this field is present in schema then it must be a DATETIME field which 
     * defaults to NULL value.
     *
     * @var string|NULL
     */
    protected $deletedField = 'deleted';
            
    /**
     * Name of field which keeps track of automatic translation of records (= to mark 
     * records which were automaticly translated).
     * Automatic translation is possible only if the Model::$translatedField is present is Model::$schema
     * and in Model::$translatedFields to keep track of each lang translation separately.
     * The default lang field value serves to keep track of default lang field translation in case 
     * that default lang texts are imported in other than default lang.
     * 
     * If this field is present in schema then it must be a BOOLEAN which defaults to 1.
     *
     * @var string
     */
    protected $translatedField = 'translated';
    
    /**
     * Is automatic translation allowed by this model?
     * This property is set in constructor according presence of Model::$translatedField
     * in Model::$schema and Model::$translatedFields
     * 
     * @var bool 
     */
    protected $allowsAutomaticTranslation = false;    
            
    /**
     * Data validations
     * 
     * Contains validation of database table fields but also validations of fields
     * which are not saved (e.g. captcha, verification code, ...). Validation rule 
     * are applied only only on non empty fields present in data. To validate also 
     * missing and empty fields use 'required' and 'notEmpty' rules or set 'force'
     * option to TRUE . Each field validation is terminated after first failed rule. 
     * To avoid this termination set 'break' option to FALSE.
     * 
     *  array(
     *      'field_01' => array(
     *          'required' => array(          // validation case name is up to user ...
     *              'rule' => 'required',
     *              'error' => 'The fields is required',    // both 'error' and 'message' key are used for the same purpose
     *                                                      // to provide validation error message.
     *                                                      // This error is appended to existing validation errors.
     *                                                      // See here bellow also 'errors'/'messages' keys.
     *              'on' => 'create',
     *          ),                            
     *          array(                        // ... even optional (MOSTLY WITHOUT NAME)
     *              'rule' => 'notEmpty',
     *              'error' => 'Please enter a value',
     *          ),
     *          array(
     *              'rule' => '/[0-9]{3}\s?[0-9]{2}/i',
     *              'error' => 'Please enter a valid ZIP number'
     *              'break' => false,           // do not terminate field validations 
     *                                          // here if this validation fails.
     *                                          // THIS WILL BE USED VERY RARELY 
     *          ),
     *          array(
     *              'rule' => 'uniqueZIP',   // you can define custom method like
     *                                       // MyModel::validate_{MethodName} or Validate::{MethodName}
     *                                       // or anonymous function
     * 
     *              'error' => 'Please enter an unique ZIP number'
     * 
     *              'alternative' => 'backend' // if rule is defined with 'alternative'
     *                                         // then it is applied only when validate()
     *                                         // is called for this alternative
     * 
     *              'force' => true,           // the rule will be launched even in case that the field is not set or is empty
     *                                         // normally the rules are launched only in case that the field is set and not empty
     * 
     *              'exit' => true,            // terminate field validations here (in any case, fails or not)
     *                                         // it is supposed that this is changed by custom validation rule
     *          ),
     *          array(
     *              'rule' => 'uniqueZIP',
     *              'error' => 'Please enter an unique ZIP number'
     *                                        // general rule (without defined alternative)
     *                                        // must be placed after rule alternatives 
     *          ),
     *          array(
     *              'rule' => function ($fieldValue, $fieldName, $data, &$validation) { // annonymous function can be also used as rule
     *                  return preg_match('/[0-9]{3}\s?[0-9]{2}/i', $fieldValue')
     *              }
     *              'error' => 'Please enter a valid ZIP number'
     *              'avoidAlternative' => 'mySpecialCase' // if validate() is called with this alternative then this rule is skipped
     *          ),
     *          array(
     *              'rule' => function ($fieldValue, $fieldName, $data, &$validation) {
     *                  $errors = array();
     *                  // ...
     *                  // ...some complex processing to get many validation errors of $fieldValue...
     *                  // ...
     *                  if ($errors) {
     *                      $validation['errors'] = $errors;    // 'errors' (or 'messages') key can be used
     *                                                          // in complex validation rules to set
     *                                                          // the whole array of validation error messages
     *                                                          // These errors are merged with existing validation errors
     *                      return false;
     *                  }
     *                  return true;
     *              }
     *          )
     *      ),
     *  )
     * 
     * NOTE: 
     * Validation 'message', 'break' and 'exit' can be changed dynamically inside the validation 'rule'.
     * 
     * NOTE: 
     * The best place to define validations is the constructor method because
     * here validation messages can be directly localized:
     * 
     *      public function __construct($options = array()) {
     *          parent::__construct();
     *          $this->validations = array(
     *              'field01' => array(
     *                  array(
     *                      'rule' => 'email',
     *                      'error' => __v(__FILE__, 'Please provide a valid email address'),
     *                  ),
     *              ),
     *              //...
     *          );
     *      }
     * 
     * @var array 
     */
    protected $validations = array();
    
    /**
     * List of field names to be translated
     * 
     * Defaults to empty array, means no fields are translated.
     *
     * @var array
     */
    protected $translatedFields = array();
    
    /**
     * Name of translation table where translated values for fields defined in 
     * $this->translatedFields are kept.
     * 
     * This can be set to the same value as $this->table or some other table name
     * pointing with foreign key to $this->table. If $this->translationTable is not 
     * defined then it is considered to be $this->table and it is so set in constructor.
     * 
     * Translated fields are distinguished by names with language code appendix,
     * e.g.: slovak translations for 'my_field' are kept in column 'my_field_sk'.
     * 
     * @var string 
     */
    protected $translationTable = null;
    
    /**
     * List of languages choosen from all available languages, e.g. array('sk', 'en').
     * If set then the translations are made only for these languages. 
     * If not set then it is set on runtime to value of App::$langs.
     * 
     * This serves to restrain languages in some cases, e.g. the frontend is in
     * 5 lang verions but the backend status tables are translated only to 'sk' and 'en'.
     * 
     * Mostly this will not be set and translations are made for all active languages
     * 
     * @var array
     */
    protected $translationLangs = array();
    
    /**
     * Path to directory where all models files are stored. It is app root relative.
     * Path for concrete file field and its saved variants is created like 
     * {Model::$fileStoreRoot}/{module}/{model}/{field}[/{variant}]
     *
     * @var string 
     */
    protected $fileStoreRoot = 'userfiles';
    
    /**
     * Path to directory where temporary files during file transfers are placed
     *
     * @var string 
     */
    protected $fileTmpStore = 'tmp/files';

    /**
     * List of fields which contain name of stored file and for which should be
     * made an upload during save/update or for which the file shoulds be deleted 
     * on record deletion. Each file field must be entered as a key under which
     * are stored file field options like:
     * 
     *      array(
     *          'product_image' => array(
     *  
     *              // Path relative to Model::$fileStore.
     *              // @deprecated Path is set implicitly like {module}/{model}/{field}[/{variant}]
     *              'path' => 'productImages',
     * 
     *              // Stored file name can be set explicitly by callable or anonymous function 
     *              // function (with original file source and record data on its input). If not provided 
     *              // or if returns NULL then original file name is used. In any case an unique version 
     *              // of name is created. This option can be defined only on top level
     *              // because all variants must have the same name.
     *              'name' => array('MyModel', 'myStaticMethod')
     * 
     *              // If TRUE then an unique filename is searched in case of naming colision.
     *              // If FALSE then in case of naming colision the existing file is overwritten by a new one.
     *              'unique' => true, // not implemented
     * 
     *              // If TRUE then file and its variants are deleted if the corresponding record is deleted.
     *              // If FALSE then the file is not deleted together with corresponding record.
     *              'delete' => true, // not implemented
     * 
     *              // Format of stored file (if variants are defined then default for all variants).
     *              // If not defined the the file is saved with same extension as acquired.
     *              'extension' => 'jpg',
     * 
     *              // Quality rate of stored file (if variants are defined then default for all variants).
     *              // Integer between 1 and 100. If not defined then image is saved in full quality with max quality.
     *              'quality' => 90,
     * 
     *              // @todo Name of db field in which a file source is stored. File source 
     *              // can be an URL to dowload file from or a local path to copy file from.
     *              // File name can be specified from actual model like 'my_field'
     *              // or from other model like 'MyModel.my_field' or 'MyModule.MyModel.my_field'
     *              'sourceField' => 'product_image_source' // @todo
     * 
     *              // Placeholder file used in url paths in case of missing file 
     *              // (if variants are defined then default for all variants).
     *              // NOTE: The easiest way to create an placeholder file for all
     *              // variants at once is to save it as a file for one of records (in admin)
     *              // then to clear its name in DB manually (in phpMyAdmin, so you
     *              // will let it as orphan file - it must be) and then you add it 
     *              // to repository by: hg add userfiles/MyModule/MyModel/MyField/MyVariant/my-image.jpg
     *              // Do this for each existing variant of placeholder, they can have different extensions.
     *              // To change it for all variants at once add the placeholder name
     *              // to some record in DB manually and repeat the above instructions.
     *              // Prefered name of placeholder is _placeholder.jpg (or .png).
     *              'placeholder' => '_placeholder.jpg'
     * 
     *              // Variants to be created for each stored file.
     *              // If no variants are defined then files are stored just in 
     *              // one variant directly under Model::$fileFields['my_picture']['path'].
     *              // The first defined variant is used as default variant by Model::getFileFieldPath()
     *              // and Model::getFileFieldUrlPath().
     *              'variants' => array(
     * 
     *                  // variant names are up to user
     *                  'small' => array(
     *                      // path relative to Model::$fileFields['my_picture']['path']
     *                      // @deprecated Path is set implicitly like {module}/{model}/{field}[/{variant}]
     *                      'path' => 's',
     * 
     *                      // extension of stored file
     *                      'extension' => 'jpg',
     * 
     *                      // quality of stored file, integer between 1 and 100.
     *                      'quality' => 90,
     * 
     *                      // placeholder file used in url paths in case of missing file
     *                      'placeholder' => '_placeholder.jpg',
     * 
     *                      // If TRUE then variant file is created only when requested
     *                      // In this case one of variants must be set as 'source' otherwise an Exception is thrown
     *                      'lazy' => true,
     * 
     *                      // Array of args for required method of Image_Transform class. See class definition
     *                      // in app/vendors/imagetransform/Image/Transform.php for all
     *                      // possible methods and their args. Method 'pngGifToJpg' shoud be used 
     *                      // to convert transparent png/gif into jpg and replace transparent pixels 
     *                      // by specified color (if transformed file is not png then nothing happens).
     *                      // Methods are processed in order they are listed in transform options.
     *                      'fit' => array(160, 160), //'crop', 'flip', 'resize', 'rotate', 'pngGifToJpg' ... 
     *                  ),
     * 
     *                  // possible other variants
     *                  //'medium' => array(),
     *                  //'large' => array(),
     * 
     *                  // keep original file but restrain its max size. To keep 
     *                  // original file without any change use just empty array()
     *                  'original' => array(
     *                      'extension' => null, // implicit extension inherited from file
     *                      'quality' => null, // implicit quality
     *                      'fit' => array(1920, 1080),
     *                      // If TRUE then variant file is used as a source for lazy or missing variants.
     *                      // Must be defined if there are some lazy variants and it cannot be lazy
     *                      // otherwise an Exception is thrown. It can be defined even when there are
     *                      // no lazy variants and in such case it is used to create missing
     *                      // variant files - e.g. after adding new variant or after changing
     *                      // existing variant size and deleting old bad-sized images.
     *                      'source' => true,
     *                  ),
     *              ),
     *          ),
     *          'product_pdf' => array(
     *              // @deprecated Path is set implicitly like {module}/{model}/{field}[/{variant}]
     *              'path' => 'productPdfs'
     *          ),
     *          'product_xls_specification' => true
     *          ...
     *      ) 
     *
     * @var array
     */
    protected $fileFields = array();
    
    /**
     * List of fields which are model static attributes (placed directly as columns
     * in table), especially that which are selective or selective priced like:
     * 
     *      array(
     *          '{fieldName1}' => array('selective' => true, 'priced' => false),
     *          '{fieldName2}' => array('selective' => true, 'priced' => true),
     *          ...
     *      )
     * 
     * This property is not included in existing Model class logic. Its just
     * suggestion for further use if needed. Values of selective attributes 
     * should be parsed using settings App.All.attributeValuesSeparator and
     * App.All.attributePricesSeparator.
     *
     * @var array 
     */
    protected $attributeFields = array();
    
    /**
     * Options used for Paginator instance in case of paging. 
     * See Paginator::setOptions() for all available options.
     * 
     * @var array 
     */
    protected $paginatorOptions = array(
        'limit' => 25,
    );
    
    /**
     * Specification of paginator lib class to get Paginator instance and store it
     * in Model::$Paginator
     *
     * @var array 
     */
    protected $paginator = array('module' => 'App', 'lib' => 'Paginator');
        
    /**
     * RUNTIME PROPERTIES
     */
        
    /**
     * Set on runtime 
     * 
     * Is the Validate() class loaded?
     * 
     * Used as info for validation process.
     * 
     * This is set on runtime on first instantion of Model class and is shared 
     * by all derived models. 
     *
     * @var bool 
     */
    private static $validateClassExists = null; // NULL means "not checked yet"
        
    /**
     * Set on runtime
     * 
     * Last validation errors stored in array like:
     * 
     *      array(
     *          '{ModelName01}' => array(
     *              '{fieldName01}' => array('Error 01 message', 'Error 01 message', ...),
     *              '{fieldName02}' => array('Error 01 message', 'Error 01 message', ...),
     *              ...,
     *              '_processing' => array('Error 01 message', 'Error 01 message', ...),
     *          ),
     *          '{ModelName02}' => array(...),
     *          ...,
     *      )
     *          
     * NOTE: Errors are stored under model names for sake of related models errors capture.
     * See Model::setError() 
     *              
     * @var array 
     */
    protected $errors = array();
        
    /**
     * Set on runtime
     * 
     * Last validation warnings stored in array like:
     * 
     *      array(
     *          '{ModelName01}' => array(
     *              '{fieldName01}' => array('Warning 01 message', 'Warning 01 message', ...),
     *              '{fieldName02}' => array('Warning 01 message', 'Warning 01 message', ...),
     *              ...,
     *              '_processing' => array('Warning 01 message', 'Warning 01 message', ...),
     *          ),
     *          '{ModelName02}' => array(...),
     *          ...,
     *      )
     *          
     * NOTE: Warnings are stored under model names for sake of related models warnings capture.
     * See Model::setWarning() 
     *              
     * @var array 
     */
    protected $warnings = array();
        
    /**
     * Set on runtime
     * 
     * List of required fields created according to defined validations for each
     * validation alternative or alternatives combination. Stored like:
     *      array(
     *          {alternativesSlug} => array(
     *              'field01' => 'field01'
     *              'field02' => 'field02'
     *              ...
     *          )
     *          ...
     *      )
     * 
     * This array is populated by method Model::getRequiredFields() and serves as a cache
     * for this method.
     * 
     * @var array 
     */
    protected $requiredFields = null;
        
    /**
     * Set on runtime
     * 
     * List of notEmpty fields created according to defined validations for each
     * validation alternative or alternatives combination. Stored like:
     *      array(
     *          {alternativesSlug} => array(
     *              'field01' => 'field01'
     *              'field02' => 'field02'
     *              ...
     *          )
     *          ...
     *      )
     * 
     * This array is populated by method Model::getNotEmptyFields() and serves as a cache
     * for this method.
     * 
     * @var array 
     */
    protected $notEmptyFields = null;
        
    /**
     * Set on runtime
     * 
     * Id of last saved (created or updated) record.
     * Set by method Model::save().
     * 
     * @var int 
     */
    protected $id = null;
        
    /**
     * Set on runtime
     * 
     * Array of replacements used to convert a DB query to 'translated' DB query
     * It has following form:
     * 
     *      array(
     *          'sk' => array(
     *              'select' => array(          // select clause replacements
     *                  'search' => array(),
     *                  'replace' => array(),
     *              ),
     *              'others' => array(          // other clauses replacements
     *                  'search' => array(),
     *                  'replace' => array(),
     *              ),
     *          ),
     *          'en' => ...       
     *      )
     * 
     *
     * @var array
     */
    protected $translationReplacements = array();
    
    /**
     * Set on runtime
     * 
     * Path to directory where the model files are stored. It is populated in model
     * constructor like {Model::$fileStoreRoot}/{module}/{model}. It is ROOT relative
     * without leading and trailing slashes. Path for concrete file field and its 
     * saved variants is created like {Model::$fileStore}/{field}[/{variant}]
     *
     * @var string 
     */
    protected $fileStore = null;
    
    /**
     * Set on runtime
     * 
     * List of compiled file fields options from Model::$fileFields. 
     * It is very simirlar to Model::fileFields array (file field name is a key under which
     * are stored file field options) with following differences:
     * - The field store path is prefixed by Model::$fileStore path. 
     * - Variant store paths are prefixed by field store path. 
     * - Field options are used as defaults for variants. 
     * - All paths are app root relative and without leading and trailing slash.
     * - If no variants are defined then field options are stored as '' (empty string) variant
     *      to facilitate futher processing which works mainly with variants
     * 
     * This array is populated and used as cache by method Model::getFileFieldOptions()
     * 
     * @var array
     */
    protected $fileFieldOptions = array();
    
    /**
     * Set on runtime
     * 
     * List of new incoming files during file fields save/update preparation
     * Files are stored with app root relative path like:
     * 
     *      array(
     *          'userfiles/images/mynewimage_01.jpg' => true
     *          'userfiles/images/mynewimage_02.jpg' => true
     *      )
     * 
     * NOTE: Files are stored as keys for sake of uniquity. Values are not considered in processing
     * 
     * @var array 
     */
    protected $incomingFiles = array();
    
    /**
     * Set on runtime
     * 
     * List of obsolete files to be deleted after successfull file fields save/update/delete.
     * Files are stored with app root relative path like:
     * 
     *      array(
     *          'userfiles/images/myoldimage_01.jpg' => true
     *          'userfiles/images/myoldimage_02.jpg' => true
     *      )
     * 
     * NOTE: Files are stored as keys for sake of uniquity. Values are not considered in processing
     * 
     * @var array 
     */
    protected $obsoleteFiles = array();
    
    /**
     * Set on runtime
     * 
     * Paginator instance used for last pagination find
     * 
     * @var Paginator 
     */
    public $Paginator = null;
    
    /**
     * Is soft delete allowed by this model?
     * This property is set in constructor according presence of Model::$deletedField
     * in Model::$schema
     * 
     * @var bool 
     */
    protected $allowsSoftDelete = false;
    
    /**
     * CONSTANTS
     */
    
    /**
     * Tree type constant returned by method Model::getTreeType()
     * 
     * This constant is returned for trees which use 'path', 'sort', 'parent_id' and possibly 'level'
     * columns to create tree structure.
     */
    const PSP_TREE_TYPE = 1;
    
    /**
     * Tree type constant returned by method Model::getTreeType()
     * 
     * This constant is returned for trees which use 'lft', 'rght' and 'parent_id'
     * columns to create tree structure.
     */
    const LRP_TREE_TYPE = 2;
    
    
    /**
     * Initilizates new instance of Model object
     */
    public function __construct() {
        parent::__construct();
        
////mojo: let adding models into app cache only to method App::loadModel(). Some models 
//          can be created with params in construct method and they cannot be recycled for
//          general use. Method App::loadModel() will never pass an arg to construct method
//          and it can be used only to get unparametrized instances.
//          
//        // cache the first model instance for later cooperation between models
//        App::setModel($this->module, $this->name, $this);
        
        // validate and normalize schema
        if (!empty($this->schema)) {            
            foreach ($this->schema as $field => &$fieldOptions) {
                if (
                    array_key_exists('default', $fieldOptions)
                    && $fieldOptions['default'] === null
                ) {
                    if (
                        isset($fieldOptions['null'])
                        && empty($fieldOptions['null'])
                    ) {
                        throw new Exception(sprintf('Invalid definition of field \'%s\' - defaults to NULL but NULL value is explicitly forbidden', $field));
                    }
                    else {
                        $fieldOptions['null'] = true;
                    }
                }
            }
            unset($fieldOptions);
        }
                        
        if (self::$validateClassExists === null) {
            self::$validateClassExists = class_exists('Validate');
        }
        
        $this->allowsSoftDelete = !empty($this->schema) 
                && !empty($this->deletedField) 
                && isset($this->schema[$this->deletedField]);
        if ($this->allowsSoftDelete) {
            $deletedFieldOptions = $this->schema[$this->deletedField];
            if (strtoupper($deletedFieldOptions['type']) !== 'DATETIME') {
                throw new Exception (__e(__FILE__, '%s::$deletedField "%s" must be of type DATETIME', $this->name, $this->deletedField));
            }
            if ($deletedFieldOptions['default'] !== null) {
                throw new Exception (__e(__FILE__, '%s::$deletedField "%s" must have default value NULL', $this->name, $this->deletedField));
            }
        }
        else {
            $this->deletedField = null;
        }
        
        // set translationLangs
        // this should be set also if the actual model has no translated fields as
        // translation can be made only for joinde models
        if ($this->translationLangs) {
            if (!is_array($this->translationLangs)) {
                throw new Exception('Model::$translationLangs must be defined as an array.');
            }
            $this->translationLangs = array_intersect(App::getPropertyLangs(), $this->translationLangs);
            if(empty($this->translationLangs['default'])) {
                throw new Exception('Model::$translationLangs must contain default lang');
            }
        }
        else {
            $this->translationLangs = App::getPropertyLangs();
        }
        
        // set translationTable if empty
        if (empty($this->translationTable)) {
            $this->translationTable = $this->table;
        }
        
        // validate translated fields
        if (!is_array($this->translatedFields)) {
            throw new Exception('Model::$translatedFields must be defined as an array.');
        }
        if (
            $this->translatedFields 
            && isset($this->schema['lang'])
        ) {
            throw new Exception(__e(
                __FILE__, 'It is not possible to define $translatedFields and $schema[\'lang\'] in the same Model. Model::$translatedFields is used in models translated by fields. Model::$schema[\'lang\'] is used in models translated by records. They cannot be used together - model cannot be both translated by fields and by records.',
                array('Model' => $this->name)
            ));
        }
        $this->allowsAutomaticTranslation = !empty($this->schema) 
            && !empty($this->translatedField) 
            && isset($this->schema[$this->translatedField]);
        if ($this->allowsAutomaticTranslation) {
            $translatedFieldOptions = $this->schema[$this->translatedField];
            if (
                strtoupper($translatedFieldOptions['type']) !== 'BOOL'
                && strtoupper($translatedFieldOptions['type']) !== 'BOOLEAN'
            ) {
                throw new Exception (__e(__FILE__, '%s::$translatedField "%s" must be of type BOOLEAN', $this->name, $this->translatedField));
            }
            if (empty($translatedFieldOptions['default'])) {
                throw new Exception (__e(__FILE__, '%s::$translatedField "%s" must have default value 1', $this->name, $this->deletedField));
            }
            if (!in_array($this->translatedField, $this->translatedFields)) {
                throw new Exception (__e(__FILE__, '%s::$translatedField "%s" must be present in %s::$translatedFields', $this->name, $this->deletedField, $this->name));
            }
        }        
        
        // set implicit nameField to 'name' if exists in schema
        if (
            empty($this->nameField)
            && isset($this->schema['name'])
            && isset($this->schema['name']['type'])
            && $this->schema['name']['type'] === 'varchar'
        ) {
            $this->nameField = 'name';
        }
        // validate name field
        elseif (
            !empty($this->nameField)
            && !isset($this->schema[$this->nameField])
        ) {
            throw new Exception(__e(__FILE__, 'Model::$nameField "%s" does not exist in Model::$schema', $this->nameField));
        }
        
        // normalize
        $this->fileStoreRoot = trim($this->fileStoreRoot, DS);
        $this->fileTmpStore = trim($this->fileTmpStore, DS);
        // set fileStore
        $this->fileStoreDeprecated = $this->fileStore; // @deprecated
        $this->fileStore = $this->fileStoreRoot . DS . $this->module . DS . $this->name;
    }
            
    /**
     * Gets id of last created record.
     * 
     * @return int 
     */
    public function getPropertyId() {
        return $this->id;
    }
    
    /**
     * Gets id which will be used for next new created record in model table
     * 
     * @return int
     */
    public function getNextId() {
        $status = DB::getTableStatus($this->table);
        return (int)$status['Auto_increment'];
    }
    
    /**
     * Gets model database table name.
     * 
     * @return string 
     */
    public function getPropertyTable() {
        return $this->table;
    }
    
    /**
     * Gets primary key field name.
     * 
     * @return string 
     */
    public function getPropertyPrimaryKey() {
        return $this->primaryKey;
    }
    
    /**
     * Gets name field property value
     * 
     * NOTE: Not all models must have defined name field, e.g. HABTM model don't need it.
     * 
     * @return string|NULL
     */
    public function getPropertyNameField() {
        return $this->nameField;
    }
    
    /**
     * Gets $deletedField property value
     * 
     * @return string|NULL
     */
    public function getPropertyDeletedField() {
        return $this->deletedField;
    }
    
    /**
     * Returns database table fields according to defined model schema.
     * 
     * @return array 
     */
    public function getPropertySchema() {
        return (array)$this->schema;
    }  
    
    /**
     * Returns validations array.
     * 
     * @return array 
     */
    public function getPropertyValidations() {
        return $this->validations;
    }  
    
    /**
     * Returns file store folder.
     * 
     * @return array 
     */
    public function getPropertyFileStore() {
        return $this->fileStore;
    }  
    
    /**
     * Returns file fields array.
     * 
     * @return array 
     */
    public function getPropertyFileFields() {
        return (array)$this->fileFields;
    }  
    
    /**
     * Sets value of file fields
     * 
     * @param array $fileFields
     */
    public function setPropertyFileFields($fileFields) {
        $this->fileFields = $fileFields;
        $this->fileFieldOptions = array();
    }  
    
    /**
     * Sets value of paginator options
     * 
     * @param array $paginatorOptions Options of used Paginator class. See Paginator::setOptions()
     * @param bool $merge If TRUE then provided $paginatorOptions are merged
     *              with existing Model::$paginatorOptions. Defaults to FALSE.
     */
    public function setPropertyPaginatorOptions($paginatorOptions, $merge = false) {
        if ($merge) {
            $this->paginatorOptions = array_merge($this->paginatorOptions, $paginatorOptions);
        }
        else {
            $this->paginatorOptions = $paginatorOptions;
        }
    }  
    
    /**
     * Returns translation table.
     * 
     * @return string 
     */
    public function getPropertyTranslationTable() {
        return $this->translationTable;
    }
    
    /**
     * Returns foreign key field name which sould be used in other tables to reference
     * this model table items.
     * 
     * @return string 
     */
    public function getForeignKey() {
        return $this->table . '_' . $this->primaryKey;
    }
    
    /**
     * Is soft delete allowed by this model?
     * 
     * @return bool
     */
    public function allowsSoftDelete() {
        return $this->allowsSoftDelete;
    }
    
    /**
     * Returns database table fields of actual and joined models.
     * 
     * @param array $options Following are available:
     *      - 'allowFields' (array|string) Fields to be allowed in returned list.
     *          It is possible to provide variants of the same field (qualified, unqualified, ...). 
     *          Defaults to NULL, means all are allowed.
     *      - 'avoidFields' (array|string) Fields to be avoided in returned list. 
     *          It is possible to provide variants of the same field (qualified, unqualified, ...). 
     *          Defaults to NULL, means no are avoided.
     *      - 'qualify' (bool) If TRUE the field names are qualified by provided 'alias'.
     *          Defaults to FALSE.
     *      - 'alias' (string) Explicit alias. If not provided or empty then actual 
     *          model name is used. Defaults to NULL.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     *      - 'module' (string) Module name. If not provided or empty then actual 
     *          model module is used. Applied only if option 'moduleAlias' is set. 
     *          Defaults to NULL.
     * @param array& $joins Optional. Passed by reference. Array of joins. Use here 
     *          the 'joins' option of Model::find() method. If possible then provide 
     *          them already normalized for sake of optimization. If not normalized 
     *          then they must be defined by 'model' (not by 'table') Defaults to NULL, 
     *          means joins are not considered.
     * 
     * @return array
     */
    public function getFields($options = array(), &$joins = null) {
        $defaults = array(
            'allowFields' => null,
            'avoidFields' => null,
            'qualify' => false,
            'alias' => null,
            'moduleAlias' => false,
            'module' => null,
        );
        $options = array_merge($defaults, $options);
        // get actual model fields
        $fields = array_keys((array)$this->schema);
        if ($options['qualify']) {
            $fields = $this->qualifyFields($fields, $options);
        }
        // get joined models fields
        if ($joins) {
            // normalize joins
            $joins = $this->normalizeJoins($joins, $options);
            foreach ($joins as $join) {
                $modelFields = $join['Model']->getFields(array(
                    'qualify' => true,
                    'moduleAlias' => false, // $join['alias'] is already moduleAlias-ed
                    'alias' => $join['alias'],
                ));
                $fields = array_merge($fields, $modelFields);
            }
        }
        if (!empty($options['allowFields'])) {
            $fields = array_intersect($fields, (array)$options['allowFields']);
        }
        if (!empty($options['avoidFields'])) {
            $fields = array_diff($fields, (array)$options['avoidFields']);
        }
        return $fields;
    }  
    
    /**
     * Returns model alias used e.g. as fields qualifier in DB queries. Possibly
     * it can be prefixed by module name.
     * 
     * @param array $options Following are available:
     *      - 'alias' (string) Explicit alias. If not provided or empty then actual 
     *          model name is used. Defaults to NULL.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     *      - 'module' (string) Module name. If not provided or empty then actual 
     *          model module is used. Applied only if option 'moduleAlias' is set. 
     *          Defaults to NULL.
     * 
     * @return string Alias described here above
     */
    public function getAlias($options = array()) {
        $defaults = array(
            'moduleAlias' => false,
            'alias' => null,
            'module' => null,
        );
        $options = array_merge($defaults, $options);
        $alias = $options['alias'];
        // if no alias specified then actual model name is used
        $defaultAlias = false;
        if (empty($alias)) {
            $defaultAlias = true;
            $alias = $this->name;
        }
        if (empty($options['module'])) {
            $options['module'] = $this->module;
        }
        // prefix alias by module name if required
        if (
            !empty($options['moduleAlias'])
            && (
                $options['moduleAlias'] === true
                || in_array($options['module'], (array)$options['moduleAlias'])
            )
            // avoid duplicit prefixing by module name
            && (
                !empty($defaultAlias)
                || substr($alias, 0, strlen($options['module']) + 1) !== ($options['module'] . '.')
            )
        ) {
            $alias = $options['module'] . '.' . $alias;
        }
        return $alias;
    }
    
    /**
     * Returns translated fields of actual model and models specified by joins.
     * 
     * @param array $options Following are available:
     *      - 'combine' (bool) If TRUE then fields are also set as keys in resulting array. 
     *          Defaults to FALSE.
     *      - 'qualify' (bool) If TRUE the field names are qualified by provided 'alias'.
     *          Defaults to FALSE.
     *      - 'alias' (string) Explicit alias. If not provided or empty then actual 
     *          model name is used. Defaults to NULL.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     *      - 'module' (string) Module name. If not provided or empty then actual 
     *          model module is used. Applied only if option 'moduleAlias' is set. 
     *          Defaults to NULL.
     * @param array& $joins Optional. Passed by reference. Array of joins. Use here 
     *          the 'joins' option of Model::find() method. If possible then provide 
     *          them already normalized for sake of optimization. If not normalized 
     *          then they must be defined by 'model' (not by 'table') Defaults to NULL, 
     *          means joins are not considered.
     * 
     * @return array
     */
    public function getTranslatedFields($options = array(), &$joins = array()) {
        $defaults = array(
            'combine' => false,
            'qualify' => false,
            'alias' => null,
            'moduleAlias' => false,
            'module' => null,
        );
        $options = array_merge($defaults, $options);
        $fields = $this->translatedFields;
        if ($options['qualify']) {
            $fields = $this->qualifyFields($fields, $options);
        }
        if ($joins) {
            // normalize joins
            $joins = $this->normalizeJoins($joins, $options);
            foreach ($joins as $join) {
                $modelFields = $join['Model']->getTranslatedFields(array(
                    'qualify' => true,
                    'moduleAlias' => false, // $join['alias'] is already moduleAlias-ed
                    'alias' => $join['alias'],
                ));
                $fields = array_merge($fields, $modelFields);
            }
        }
        if ($options['combine'] && !empty($fields)) {
            $fields = array_combine($fields, $fields);
        }
        return $fields;
    }  
    
    /**
     * Normalizes data. This is done also on save(), update() and validate() 
     * if $option['normalize'] is TRUE. By default $option['normalize'] is TRUE.
     * 
     * By defaut it does only normalization of empty values and it is up to your model to implement 
     * further functionality if required.
     *
     * @param array $data Data to be normalized.
     * @param array $options Options of Model::normalizeEmptyValues() plus following:
     *      - 'on' (string) Is the normalization done on 'create' or 'update'. 
     *      Defaults to NULL (not applied).
     *      - 'alternative' (string) Normalization alternative. Defaults to NULL (not applied).
     * 
     * ATTENTION: Be carefull to apply Sanitize::value() on $data. Always consider 
     *      that e.g. $name = Sanitize::value($data['name']) will create $data['name'] === NULL
     *      in case that 'name' key is not present in $data array in tne moment of
     *      the check. And like this you can erase 'name' in DB record. To avoid
     *      this trap use either ternary expessions or make copy of $data to an internal variable.
     * 
     * @return array Normalized data
     */
    public function normalize($data, $options = array()) {
////if needed use this in your implementation of this method        
//        $defauts = array(
//            'on' => null,
//            'alternative' => null,
//        );
//        $options = array_merge($defauts, $options);
//        $options['alternative'] = (array)$options['alternative'];
//        
//        // do your custom normalization...
//        
//        return parent::normalize($data, $options);

        // normalize empty values ('', NULL) according to Model::$schema
        $data = $this->normalizeEmptyValues($data, $options);
        
        return $data;
    }
    
    /**
     * Normalizes empty values according to Model::$schema in following way:
     * - empty string values (or any custom defined empty value) are normalized to:
     *      - NULL if the field can be set to NULL according to Model::$schema
     *      - 0 if the field cannot be NULL and it is of a numeric type (integet, 
     *          float, decimal, ...) according to Model::$schema
     * - NULL values are normalized to:
     *      - 0 if the field is of a numeric type (integet, float, decimal, ...) 
     *          according to Model::$schema and it cannot contain NULL values
     *      - empty string if the field is of a nonnumeric type and it cannot contain 
     *          NULL values according to Model::$schema
     * 
     * @param array $data Data to normalize empty values in
     * @param array $options Following are available
     *      - 'allowFields' (array) List of field names which are allowed to be normalized.
     *      - 'avoidFields' (array) List of field names which will be excluded from normalization.
     *      - 'emptyValues' (array) Custom list of empty values which should be normalized to NULL
     *          if possible according to Model::$schema. Defaults to array ('').
     * 
     * @return array Data with normalized empty values
     */
    public function normalizeEmptyValues($data, $options = array()) {
        $defaults = array(
            'allowFields' => null,
            'avoidFields' => null,
            'emptyValues' => null,
        );
        $options = array_merge($defaults, $options);
        if (empty($this->schema)) {
            return $data;
        }
        $customEmptyValues = is_array($options['emptyValues']);
        $data = (array)$data;
        $fields = DB::getDataFields($data, $options);
        // normalize all empty strings to NULL if the DB field value can be NULL
        foreach ($fields as $field) {
            if (empty($this->schema[$field])) {
                continue;
            }
            $fieldType = strtolower($this->schema[$field]['type']);
            if (
                !$customEmptyValues 
                && (
                    $data[$field] === ''
                    ||
                    $fieldType === 'date'
                    && $data[$field] === '0000-00-00'
                    ||
                    $fieldType === 'time'
                    && $data[$field] === '00:00:00'
                    ||
                    $fieldType === 'datetime'
                    && $data[$field] === '0000-00-00 00:00:00'
                )
                ||
                $customEmptyValues 
                && in_array($data[$field], $options['emptyValues'], true)
            ) {
                if ($this->isNullableField($field)) {
                    $data[$field] = null;
                }
                elseif (DB::isNumericDataType($fieldType)) {
                    $data[$field] = 0;
                }
            }
            if (
                $data[$field] === null
                && !$this->isNullableField($field)
            ) {
                if (DB::isNumericDataType($fieldType)) {
                    $data[$field] = 0;
                }
                else {
                    $data[$field] = '';
                }
            }
        }
        
        return $data;
    }    
    
    /**
     * Nullifies empty values in provided data.
     * Empty values are set to NULL only in case that they can be NULL
     * according to defined Model::$schema.
     * 
     * @param array $data Data to nullify empty values
     * @param array $options Following are available
     *      - 'allowFields' (array) List of field names which are allowed to be nullified.
     *      - 'avoidFields' (array) List of field names which will be excluded from nullify.
     *      - 'emptyValues' (array) Custom list of empty values which should be nullified to
     *          if possible according to Model::$schema. Defaults to array ('').
     * 
     * @return array Data with nullified values
     */
    public function nullifyEmpty($data, $options = array()) {
        $defaults = array(
            'allowFields' => null,
            'avoidFields' => null,
            'emptyValues' => null,
        );
        $options = array_merge($defaults, $options);
        $customEmptyValues = is_array($options['emptyValues']);
        $fields = DB::getDataFields($data, $options);
        // normalize all empty strings to NULL if the DB field value can be NULL
        foreach ($fields as $field) {
            if (
                (
                    $customEmptyValues && in_array($data[$field], $options['emptyValues'], true)
                    ||
                    !$customEmptyValues && $data[$field] === ''
                )
                && $this->isNullableField($field)
            ) {
                $data[$field] = null;
            }
        }
        
        return $data;
    }
    
    public function isNullableField($field) {
        return (
            !empty($this->schema[$field])
            && (
                !empty($this->schema[$field]['null'])
                ||
                array_key_exists('default', $this->schema[$field])
                && $this->schema[$field]['default'] === null
            )
        );
    }

    /**
     * Returns max available length of specified field.
     * It tries to be resolved at first from DB and then from Model::$schema[$field]['length'].
     * 
     * @param string $field Field name
     * 
     * @return mixed
     */
    public function getFieldLength($field) {
        $length = null;
        $fieldOptions = DB::getFieldOptions($this->table, $field);
        if (isset($fieldOptions['length'])) {
            $length = $fieldOptions['length'];
        }
        elseif (!empty($this->schema[$field]['length'])) {
            $length = $this->schema[$field]['length'];
        }
        return $length;
    }
            
    /**
     * Qualifies provided field with alias (which is used as DB table alias)
     * 
     * @param array $field
     * @param array $options Following are available:
     *      - 'alias' (string) Explicit alias. If not provided or empty then actual 
     *          model name is used. Defaults to NULL.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     *      - 'module' (string) Module name. If not provided or empty then actual 
     *          model module is used. Applied only if option 'moduleAlias' is set. 
     *          Defaults to NULL.
     * 
     * @return array
     */
    public function qualifyField($field, $options = array()) {
        return $this->getAlias($options) . '.' . $field;
    }
    
    /**
     * Qualifies provided fields with alias (which is used as DB table alias)
     * 
     * @param array $fields
     * @param array $options Following are available:
     *      - 'alias' (string) Explicit alias. If not provided or empty then actual 
     *          model name is used. Defaults to NULL.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     *      - 'module' (string) Module name. If not provided or empty then actual 
     *          model module is used. Applied only if option 'moduleAlias' is set. 
     *          Defaults to NULL.
     * 
     * @return array
     */
    public function qualifyFields($fields, $options = array()) {
        $alias = $this->getAlias($options);
        foreach ($fields as &$field) {
            $field = $alias . '.' . $field;
        }
        return $fields;
    }
    
    /**
     * Normalizes literals, it means that 'allowLiterals' option is applied
     * 
     * @param array $literals
     * @param array $options Following are available:
     *      - 'allowLiterals' (bool|array) Array of allowed literals. This is usefull
     *          in case that literals can be generated by user input and you would like
     *          to make some constrains for generated literals (for security reasons).
     *          It has the same format as 'literals' option. If any empty value is provided
     *          then no literals are allowed. If any non-empty and non-array value
     *          is provided then any literals are allowed. Defaults to TRUE, it means 
     *          any literals are allowed.
     * 
     * @return array
     */
    public function normalizeLiterals($literals, $options = array()) {
        $defaults = array(
            'allowLiterals' => true,
        );
        $options = array_merge($defaults, $options);
        if (
            empty($options['allowLiterals'])
        ) {
            $literals = array();
        }
        elseif (is_array($options['allowLiterals'])) {
            foreach($literals as $key => &$literal) {
                if (
                    empty($options['allowLiterals'][$key])
                    || empty($literal)
                ) {
                    unset($literals[$key]);
                }
                elseif ($literal === true) {
                    $literal = $options['allowLiterals'][$key];
                }
                elseif ($options['allowLiterals'][$key] !== true) {
                    $literal = array_intersect(
                        (array)$literal,
                        (array)$options['allowLiterals'][$key]
                    );
                }
            }  
            unset($literal);
        }
        return $literals;
    }
    
    /**
     * Normalizes defined joins in case that join is defined by 'model' and 'module'.
     * Each normalized join contains following items: 'model', 'module', 'Model', 'table', 'alias',
     * 'toModel', 'toModule', 'ToModel', 'toTable', 'toAlias', 'ignoreSoftDeleted', 'conditions'
     * 
     * If 'model' and 'table' occur both in join definition, then 'model' is has precedence
     * and 'table' and 'alias' are set according to 'model'.
     * If 'ToModel' / 'toModel' is not defined then it is set to actual model. 
     * If 'module' or 'toModule' is not defined then then it is set to module of actual model.
     * 
     * Moreover in duplicit joins definitions are removed in normalized joins.
     *
     * @param array $joins Raw joins possibly defined also by 'model' or 'Model'
     * @param array $options Following are available:
     *      - 'ignoreSoftDeleted' (bool) If TRUE and Model::$allowsSoftDelete is TRUE
     *          then soft deleted records are excluded in joins conditions. 
     *          Defaults to TRUE.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE.
     *      - 'allowJoins' (bool|array) Array of allowed joins. This is usefull
     *          in case that joins can be generated by user input and you would like
     *          to make some constrains for generated joins (for security reasons).
     *          It has the same format as 'joins' option. If any empty value is provided
     *          then no joins are allowed. If any non-empty and non-array value
     *          is provided then any joins are allowed. Defaults to TRUE, it means 
     *          any joins are allowed.
     * 
     * @return array Normalized joins described here above.
     */
    public function normalizeJoins($joins, $options = array()) {
        $defaults = array(
            'ignoreSoftDeleted' => true,
            'moduleAlias' => false,
            'allowJoins' => true,
        );
        $options = array_merge($defaults, $options);
        if (!empty($joins)) {
            // filter joins according to allowed ones
            if (empty($options['allowJoins'])) {
                $joins = array();
            }
            elseif (is_array($options['allowJoins'])) {
                foreach($joins as $key => $join) {  
                    if (!in_array($join, $options['allowJoins'], false)) {  
                        unset($joins[$key]);
                    }  
                }  
            }
            foreach ($joins as &$join) {
                // if join 'model' is defined then then set the join accordingly
                if (
                    !empty($join['model'])
                    || !empty($join['Model'])
                ) {
                    // normalize Model data
                    if (empty($join['Model'])) {                        
                        if (empty($join['module'])) {
                            $join['module'] = $this->module;
                        }
                        // set the join Model
                        $join['Model'] = App::loadModel($join['module'], $join['model'], true);
                    }
                    else {
                        $join['model'] = $join['Model']->getPropertyName();
                        $join['module'] = $join['Model']->getPropertyModule();
                    }
                    $join['table'] = $join['Model']->getPropertyTable();
                    if (empty($join['alias'])) {
                        $join['alias'] = $this->getAlias(array(
                            'moduleAlias' => $options['moduleAlias'],
                            'alias' => $join['model'],
                            'module' => $join['module'],
                        ));
                    }
                    // normalize toModel data
                    if (
                        empty($join['ToModel'])
                        && empty($join['toModel'])
                    ) {
                        $join['ToModel'] = $this;
                        $join['toModel'] = $this->name;
                        $join['toModule'] = $this->module;
                    }
                    elseif (empty($join['ToModel'])) {
                        if (empty($join['toModule'])) {
                            $join['toModule'] = $this->module;
                        }
                        // set the join table
                        $join['ToModel'] = App::loadModel($join['toModule'], $join['toModel'], true);
                    }
                    else {
                        $join['toModel'] = $join['ToModel']->getPropertyName();
                        $join['toModule'] = $join['ToModel']->getPropertyModule();
                    }
                    $join['toTable'] = $join['ToModel']->getPropertyTable();
                    if (empty($join['toAlias'])) {
                        $join['toAlias'] = $this->getAlias(array(
                            'moduleAlias' => $options['moduleAlias'],
                            'alias' => $join['toModel'],
                            'module' => $join['toModule'],
                        ));
                    }
                    if (!array_key_exists('ignoreSoftDeleted', $join)) {
                        $join['ignoreSoftDeleted'] = $options['ignoreSoftDeleted'];
                    }
                    // create/extend conditions between Model and ToModel
                    $extendConditions = false;
                    if (
                        empty($join['conditions'])
                        || ($extendConditions = (
                            is_array($join['conditions']) 
                            && reset($join['conditions']) === '*' 
                            && key($join['conditions']) === 0
                        ))
                    ) {
                        $fk = $join['Model']->getForeignKey();
                        $toSchema = $join['ToModel']->getPropertySchema();
                        if (isset($toSchema[$fk])) {
                            $condition = 
                                $join['alias'] . '.' . $join['Model']->getPropertyPrimaryKey() . ' = ' . $join['toAlias'] . '.' . $fk;
                        }
                        else {
                            $toFk = $join['ToModel']->getForeignKey();
                            $schema = $join['Model']->getPropertySchema();
                            if (isset($schema[$toFk])) {
                                $condition = 
                                    $join['alias'] . '.' . $toFk . ' = ' . $join['toAlias'] . '.' . $join['ToModel']->getPropertyPrimaryKey();
                            }
                            else {
                                throw new Exception(__e(
                                    __FILE__, 
                                    "Invalid definition of join %s. It is not possible to generate 'conditions'. Please specify them manually.", 
                                    json_encode($join)
                                ));
                            }
                        }
                        if ($extendConditions) {
                            array_shift($join['conditions']); 
                            array_unshift($join['conditions'], $condition); 
                        }
                        else {
                            $join['conditions'] = array($condition);
                        }
                    }
                    $join['conditions'] = $join['Model']->normalizeConditions(
                        $join['conditions'], 
                        array_merge($options, array(
                            'ignoreSoftDeleted' => $join['ignoreSoftDeleted'],
                            'moduleAlias' => $options['moduleAlias'],
                            'alias' => $join['alias'],
                            'module' => $join['module'],
                        ))
                    );
                }
                // if still no table is set then the join definition is invalid
                if (empty($join['table'])) {
                    throw new Exception(__e(
                        __FILE__, 
                        "Invalid join definition, neither 'model' nor 'table' is provided: %s",
                        json_encode($join)
                    ));
                }                
            }
            unset($join);
            // remove duplicit join definitions
            $joins = Arr::getUniqueValues($joins, false);
        }
        return $joins;
    }
    
    /**
     * Normalizes provided fields. If $fields is empty or equals to '*' then 
     * fields are autogenerated for actual model and all provided joined models. 
     * There is a possibility to extend autogenerated fields by following syntax:
     * 
     *      array(
     *          '*',
     *          'CONCAT(User.firstname, " ", User.lastname) AS fullname'
     *      )
     * 
     * Plus all available options are applied on fields.
     * 
     * @param array|string $fields Fields to be normalized. Use here the 'fields' option 
     *          of Model::find() method.
     * 
     * @param array $options Following are available:
     *      - 'allowFields' (array|string) Fields to be allowed in returned list.
     *          It is possible to provide variants of the same field (qualified, unqualified, ...).
     *          Defaults to NULL, means all are allowed.
     *      - 'avoidFields' (array|string) Fields to be avoided in returned list. 
     *          It is possible to provide variants of the same field (qualified, unqualified, ...).
     *          Defaults to NULL, means no are avoided.
     *      - 'alias' (string) Explicit alias. If not provided or empty then actual 
     *          model name is used. Defaults to NULL.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     * @param array& $joins Optional. Passed by reference. Array of joins. Use here 
     *          the 'joins' option of Model::find() method. If possible then provide 
     *          them already normalized for sake of optimization. If not normalized 
     *          then they must be defined by 'model' (not by 'table') Defaults to NULL, 
     *          means joins are not considered.
     * 
     * @return array Array of normalized fields as described above. If the joins are
     *          provided then they are changed to normalized form (as they are passed by reference)
     */
    public function normalizeFields($fields, $options = array(), &$joins = null) {
        $defaults = array(
            'allowFields' => null,
            'avoidFields' => null,
            'alias' => null,
            'moduleAlias' => false,
        );
        $options = array_merge($defaults, $options);
        $extendFields = false;
        if (
            empty($fields)
            || $fields === '*'
            || ($extendFields = (
                is_array($fields)
                && reset($fields) === '*' 
                && key($fields) === 0
            ))
        ) {
            $normalizedFields = $this->getFields(
                array(
                    'qualify' => true,
                    'alias' => $options['alias'],
                    'moduleAlias' => $options['moduleAlias'],
                ),
                $joins
            );
            // consider extended fields
            if ($extendFields && count($fields) > 1) {
                array_shift($fields); 
                $fields = array_unique(array_merge($normalizedFields, $fields));
            }
            else {
                $fields = $normalizedFields;
            }
        }
        $fields = (array)$fields;
        if (!empty($options['allowFields'])) {
            $fields = array_intersect($fields, (array)$options['allowFields']);
        }
        if (!empty($options['avoidFields'])) {
            $fields = array_diff($fields, (array)$options['avoidFields']);
        }
        return $fields;
    }
    
    /**
     * Normalizes provided conditions. For the moment it means addition of condition
     * to exclude soft deleted records.
     * 
     * @param NULL|array $conditions Conditions to normalized
     * @param array $options Following are available:
     *      - 'ignoreSoftDeleted' (bool) If TRUE and Model::$allowsSoftDelete is TRUE
     *          then soft deleted records are excluded from returned results. 
     *          Defaults to TRUE.
     *      - 'alias' (string) Explicit alias. If not provided or empty then actual 
     *          model name is used. Defaults to NULL.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     * 
     * @return array Normalized conditions array
     */
    public function normalizeConditions($conditions, $options = array()) {
        $defaults = array(
            'ignoreSoftDeleted' => true,
            'alias' => null,
            'moduleAlias' => false,
        );
        $options = array_merge($defaults, $options);
        if (
            $this->allowsSoftDelete 
            && !empty($options['ignoreSoftDeleted'])
        ) {
            $conditions = DB::nestConditions($conditions);
            $conditions[$this->qualifyField($this->deletedField, $options)] = null;
        }
        return $conditions;
    }
    
    /**
     * Normalizes find options. This is done in Model::find() if options['normalizeFindOptions'] is TRUE
     * after Paginator find options are merged, joins and fields are normalized 
     * and before translation processing starts.
     * 
     * By defaut it does nothing because it is up to your model to implement 
     * this functionality if required. 
     *
     * @param array $options Find options to be normalized
     * 
     * @return array Normalized find options
     */
    public function normalizeFindOptions($options) {
        return $options;
    }
    
    /**
     * Loads and initializes the Model::$Paginator. Initialization is done 
     * according to paginator params in actual URL and according to provided 
     * options.
     * 
     * Normally this method is used only in Model::find(). Another possible use case
     * is e.g. when loading search result from third-party API (e.g. luigis box)
     * and you wold like to paginate items returned by the API. 
     * 
     * @param array $options See Paginator::setOptions() for available options.
     * 
     * @return array Updated $options - 'limit' option can be changed according to
     *      Model::$paginatorOptions['limit'] (if any).
     */
    public function loadPaginator($options = array()) {
        $options = array_merge(array(
            'limit' => null,
        ), $options);
        // load paginator if not loaded yet
        if (empty($this->Paginator)) {
            App::loadLib($this->paginator['module'], $this->paginator['lib']);
            $this->Paginator = new $this->paginator['lib'](array('PrimaryModel' => $this));
        }
        // set paginator options taking the explicit limit from $options if defined
        $paginatorOptions = $this->paginatorOptions;
        if ($options['limit'] || $options['limit'] === false) {
            $paginatorOptions['limit'] = $options['limit'];
        }
        // if the explicit limit is not defined then use implicit limit from 
        // paginator options (if defined)
        elseif (!empty($paginatorOptions['limit'])) {
            $options['limit'] = $paginatorOptions['limit'];
        }
        $this->Paginator->setOptions($paginatorOptions);
        $this->Paginator->parseRequest($options);
        
        return $options;
    }
        
    /**
     * Finds all records of the model and its related models
     * 
     * Its up to Model childs to implement this method!
     * 
     * @param array $options Optional. Create any new options to parametrize the
     *      processing. To parametrize included models the 'contain' option is recommended:
     *      - 'contain' (array) List of model names which should be included in processing
     *     
     * @return array|resource Array of retrieved record(s) or a resource if required
     */
    public function findAll($options = array()) {
        throw new Exception('Please implement this method');
    }
        
    /**
     * Finds all records according to given options. 
     * 
     * @param array $options Array of following options:
     *      - 'fields' (string|array) Single field name or an array of field names. 
     *          They can be qualified or/and with aliases. If not provided or empty then 
     *          SQL query is build with '*' to retrieve all existing fields in DB tables.
     *          If provided as '*' then they are autogenerated for actual and all joined models. 
     *          Moreover, there is a possibility to extend autogenerated fields by following syntax:
     *          array('*', 'CONCAT(User.firstname, " ", User.lastname) AS fullname')
     *          where '*' is used as the firts item in array. For sake of optimality
     *          it is the best to name fields explicitly.
     *      - 'allowFields' (string|array) List of allowed fields. This is usefull
     *          in case that fields can be generated by user input and you would like
     *          to make some constrains for generated fields (for security reasons).
     *          It has the same format as 'fields' option. If any empty value is provided
     *          then any fields are allowed. It is possible to provide variants of the 
     *          same field (qualified, unqualified, ...). Defaults to NULL, it means any 
     *          fields are allowed.
     *      - 'avoidFields' (string|array) List of fields to be avoided. This is usefull
     *          in case that fields can be generated by user input or 'fields' are specified like '*' 
     *          (it means thea are autogenerated) and you would like 
     *          to make some constrains for generated fields (for security reasons).
     *          It has the same format as 'fields' option. If any empty value is provided
     *          then no fields are avoided. It is possible to provide variants of the 
     *          same field (qualified, unqualified, ...). Defaults to NULL, means no are avoided.
     *      - 'conditions' (array) Array of conditions definition. See DB::getQueryConditions().
     *          Example:
     *              'conditions' => array(
     *                  'run_groups_id' => 1,                           // `run_groups_id` = '1'
     *                  'User.username !=' => 'mojo',                   // '`User`.`username` != 'mojo'
     *                  array(                                          // open brackets here
     *                      'id' => array(1, 2, 32),                    // `id` IN ('1','2','32')
     *                      'OR',                                       // 'OR'
     *                      'pid !=' => array('x', 'y', 'z')            // `pid` NOT IN ('x', 'y', 'z')
     *                  ),                                              // close brackets here
     *                  'email' => null,                                // `email` IS NULL
     *                  'image !=' => null,                             // `image` IS NOT NULL
     *                  'first_name ~%' => 'A'                          // `first_name` LIKE 'A%'
     *                  'last_name !%~' => array('ova', 'ska')          // (`last_name` LIKE NOT '%ova' AND `last_name` LIKE NOT '%ska')
     *                  'CONCAT(`first_name`, ' ', `last_name`)="..."'      // condition SQL literal
     *                                                                      // must be present in 'literals' => array ('conditions' => ...)
     * 
     *                  'CONCAT(`first_name`, ' ', `last_name`)=' => '...'  // field SQL literal
     *                                                                      // must be present in 'literals' => array ('conditions' => ...)
     *                                                                      // the operator must be included 
     *              )
     *      - 'having' (array) Array of having definition. Use the same syntax as 
     *          for 'conditions' option. In contrary to 'conditions' (SQL WHERE) 
     *          the 'having' applies after select and aggregation is done.  That is why
     *          it is used to create conditions for aliased fields (SELECT surname as name FROM users WHERE active = 1 HAVING name = 'Novak')
     *          and for agregated fields (SELECT surname FROM users WHERE active = 1 GROUP BY surname HAVING COUNT(*) > 3)
     *          Use 'having' only for this 2 purposes as in other situation the 'conditions' is more efficient. 
     *      - 'order' (string|array) Single field order definition, e.g. 'last_name' (will be order ASC by default)
     *          or 'last_name DESC'. Or an array of orders for multiple fields like: 
     *              array(
     *                  'last_name',            // will be order ASC by default
     *                  'first_name ASC',
     *                  'age' => 'DESC',           
     *                  ...
     *              );
     *      - 'group' (string|array) Single field name or array of such names to group
     *          records by. Array for looks like:
     *              array(
     *                  'last_name',        
     *                  'age',           
     *                  ...
     *              );
     *          ATTENTION: Be careful when using group with aggregation function (COUNT(), SUM(), ...).
     *          In such a case agregations are applied to each of groups separatelly instead to 
     *          the whole set of selected records.
     *      - 'limit' (int|bool) Limit of records to retrieve. Used also for paging 
     *          when 'paginate' is TRUE. To use paginator only for sorting and filtering
     *          set 'paginate' to TRUE and 'limit' to FALSE. Defaults to NULL, it means no 
     *          limit is set.
     *      - 'offset' (int) Integer to specify offset of retrieved records. Defaults to NULL
     *          so no offset is imposed.
     *      - 'page' (int) If specified then 'offset' is counted using like ('page' - 1) * 'limit'.
     *          This option is taken in to account only if 'offset' is NULL.
     *      - 'distinct' (bool) If TRUE then SELECT DISTINCT is created. Defaults to FALSE.
     *          ATTENTION: DISTINCT combined with ORDER BY needs a temporary table in many cases. 
     *          Use of GROUP BY can be in such cases better. See https://dev.mysql.com/doc/refman/5.0/en/distinct-optimization.html
     *      - 'literals' (array) Associative array of literals used in other options.
     *          It can contain following keys: 'fields', 'conditions', 'having', 'order', 'group', 'joins'.
     *          Each of items is either TRUE to process whole definition of that item as literals 
     *          or string to provide single literal or a plain array of such strings, 
     *          e.g.: array('conditions' => 'CONCAT(`first_name`, ' ', `last_name`)="..."', 'order' => TRUE)
     *      - 'joins' (array) Array of joins definition (!each of joins is an array itself).
     *          Duplicities in definitions are removed. E.g.:
     *              array(
     *                  // !!! Each join is another nested array !!!
     *                  // low level join definition (see DB::getQueryJoins() for more details)
     *                  array(
     *                      'type' => 'left',
     *                      'table' => 'my_model_table',             
     *                      'alias' => 'MyModelA',                             
     *                      'conditions' => array(
     *                          'MyModelA.run_users_id = User.id', 
     *                          // ...any other conditions to pair records of two tables
     *                      ),
     *                  ),
     *                  // model level join definition (see Model::normalizeJoins() for more details)
     *                  array(
     *                      'type' => 'left'
     *                      'model' => 'MyModelB',
     *                      // you can specify ignoring of soft deleted records for each join separately
     *                      'ignoreSoftDeleted' => false, 
     *                  ),
     *                  // definition of join to another than actual model (see Model::normalizeJoins() for more details)
     *                  array(
     *                      'type' => 'left'
     *                      'model' => 'MyModelC',
     *                      'toModel' => 'MyModelA',
     *                  ),
     *                  ...
     *              ) 
     *          ATTENTION: If defining join conditions manually then the best practise
     *          is to write them like '{JoinedModel.field} = {ToModel.field}'. This is
     *          how they are created implicitly by Model::normalizeJoins() and if
     *          they are defined in the same way manually then duplicit joins can be removed.
     *      - 'qualify' (bool|array) If TRUE then aliases are created for 'fields'
     *          from qualified fields. Means, field `MyModel`.`field_1` is aliased
     *          as `MyModel`.`field_1` AS `MyModel.field_1`. If an array of qualifiers when
     *          only fields with given qualifiers will be aliased. This can be used to inflate the 
     *          resulting array. Fields which are already aliased are skipped. Defaults to FALSE.
     *      - 'inflate' (bool) If TRUE then resulting array is inflated, means 
     *          array('User.name' => 'Admin', 'User.age' => 25, 'Group.pid' => 'admins')
     *          is inflated to following one: array('User' => array('name' => 'Admin', 'age' => 25), 'Group' => array('pid' => 'admins')).
     *          This can be used together with 'qualify' option but it is not required 
     *          for 'qualify' to be TRUE,  because qualified fields aliases can be set 
     *          also explicitly in 'fields'.
     *      - 'separate' (bool|array) This is shortcut for 'qualify' = 'separate' and 'inflate' = TRUE.
     *          Defaults to FALSE.
     *      - 'first' (bool) If TRUE then just first record is returned from all retrieved.
     *          This can be used only if 'resource' option is NOT TRUE. Defaults to FALSE.
     *      - 'resource' (bool) If TRUE then instead of array a resource handle is
     *          returned. This can be used in case that a huge amount of records
     *          is retrieved. The resource can be processed using DB::fetchArray($resource).
     *          Defaults to FALSE.
     *      - 'paginate' (bool) If TRUE then Paginator class is loaded and is used to 
     *          set paging params and paginate retrieved results. The paginator instance 
     *          is stored in public property Model::$Paginator. If FALSE then pagination 
     *          is turned off. To use paginator only for sorting and filtering
     *          set 'paginate' to TRUE and 'limit' to FALSE. Defaults to FALSE.
     *      - 'translate' (bool) If TRUE then translation is made implicitly if actual 
     *          model or some of joined models have translated fields.
     *          If FALSE then translation is turned off. Defaults to TRUE.
     *      - 'lang' (string) Explicit lang to be used for translations. Applies
     *          only if 'translate' option is TRUE. Defaults to NULL, means lang
     *          is set to App::$lang.
     *      - 'moduleAlias (bool|array) If TRUE then table alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     *      - 'normalizeFindOptions' (bool|function) If TRUE then passed find options 
     *          are normalized by method Model::normalizeFindOptions(). If a anonymous
     *          function then passed find options are normalized by provided function.
     *          In that case it is up to user to call eventually Model::normalizeFindOptions()
     *          if needed. Anonymous function gets $options on input and must return normalized 
     *          options. Normalization is done between Paginator->getFindOptions()
     *          and Paginator->findCount(). Defaults to FALSE as it can be quite 
     *          hardcoded and is used only in very specific cases when 'paginate' 
     *          option is TRUE to resolve options for atypical filters or sorts 
     *          coming from Paginator.
     *      - 'ignoreSoftDeleted' (bool) If TRUE and Model::$allowsSoftDelete is TRUE
     *          then soft deleted records are excluded from returned results. 
     *          Defaults to TRUE.
     *      - 'allowJoins' (bool|array) Array of allowed joins. This is usefull
     *          in case that joins can be generated by user input and you would like
     *          to make some constrains for generated joins (for security reasons).
     *          It has the same format as 'joins' option. If any empty value is provided
     *          then no joins are allowed. If any non-empty and non-array value
     *          is provided then any joins are allowed. Defaults to TRUE, it means 
     *          any joins are allowed.
     *      - 'allowLiterals' (bool|array) Array of allowed literals. This is usefull
     *          in case that literals can be generated by user input and you would like
     *          to make some constrains for generated literals (for security reasons).
     *          It has the same format as 'literals' option. If any empty value is provided
     *          then no literals are allowed. If any non-empty and non-array value
     *          is provided then any literals are allowed. Defaults to TRUE, it means 
     *          any literals are allowed.
     *          
     * @return array|resource Array of retrieved record(s) or a resource if required
     * 
     * 
     * NOTE: This method is the Model class basic find method. All other find methods 
     * (findFirst, findList, findCount, findTree, ...) call find() method with properly
     * configured options. Translations are implemented throught this method.
     * That is why, if ever you will add a new findXYZ() method keep this on mind :)
     * 
     * NOTE: The order of statement execution in mySQL si following (not sure about DISTINCT): 
     * FROM > WHERE > SELECT > GROUP BY > HAVING > DISTINCT > ORDER BY
     */
    public function find($options = array()) {
        // check for model table
        if (empty($this->table)) {
            throw new Exception(
                'This method cannot be used as the model doesn\'t use any database table. ' 
                . 'Define your own version of this method or use this model only for data validations.'
            );
        }
        $defaults = array(
            'moduleAlias' => false,
            'fields' => null,
            'allowFields' => null,
            'avoidFields' => null,
            'joins' => array(),
            'conditions' => null,
            'group' => null,
            'having' => null,
            'order' => null,
            'distinct' => false,
            'literals' => null,
            'limit' => null,
            'offset' => null,
            'page' => null,
            'paginate' => false,
            'resource' => false,
            'first' => false,
            'inflate' => false,
            'qualify' => false,
            'separate' => false,
            'lang' => null,
            'translate' => true,
            'normalizeFindOptions' => false,
            'ignoreSoftDeleted' => true,
            'allowLiterals' => true,
            'allowJoins' => true,
        );
        $options = array_merge($defaults, $options);
        // translations use explicit 'query' option which has higher precedence 
        // than other options, see DB::select/update/delete(), so remove this option
        // on each new call of this method. The 'array' option is related to 'query'
        // and also added by translations functionality
        unset($options['query']);
        unset($options['array']);
        // separate = TRUE is a shortcut for qualify = TRUE + inflate = TRUE
        if ($options['separate']) {
            $options['qualify'] = $options['separate'];
            $options['inflate'] = true;
        }
        // set the table alias
        $options['alias'] = $this->getAlias($options);
        // check for pagination
        if ($options['paginate']) {
            $options = $this->loadPaginator($options);
            $options = $this->Paginator->getFindOptions($options);
            // normalize find options
            if (Validate::callableFunction($options['normalizeFindOptions'])) {
                $options = $options['normalizeFindOptions']($options);
            }
            elseif ($options['normalizeFindOptions']) {
                $options = $this->normalizeFindOptions($options);
            }
            // resolve count
            $this->Paginator->getCount($options);
        }
        elseif (Validate::callableFunction($options['normalizeFindOptions'])) {
            $options = $options['normalizeFindOptions']($options);
        }
        elseif ($options['normalizeFindOptions']) {
            $options = $this->normalizeFindOptions($options);
        }
        // normalize literals
        if ($options['literals']) {
            $options['literals'] = $this->normalizeLiterals($options['literals'], $options);
        }
        // normalize conditions
        $options['conditions'] = $this->normalizeConditions($options['conditions'], $options);
        // normalize joins
        if ($options['joins']) {
            $options['joins'] = $this->normalizeJoins($options['joins'], $options);
        }
        // normalize fields
        $options['fields'] = $this->normalizeFields(
            $options['fields'], 
            array(
                'allowFields' => $options['allowFields'],
                'avoidFields' => $options['avoidFields'],
                'alias' => $options['alias'],
                'moduleAlias' => $options['moduleAlias'],
            ), 
            $options['joins']
        );
        if (empty($options['fields'])) {
            throw new Exception(__e(__FILE__, 'No fields has been resolved. Check \'allowFields\' and \'avoidFields\' options'));
        }
        // check for translations
        if (
            $options['translate'] 
            // translate only if there are some translated fields defined 
            && $this->getTranslatedFields(array(), $options['joins'])
            // translate only if non-default lang is required
            && ($lang = $this->getTranslationLang($options)) != $this->translationLangs['default'] 
        ) {
            // add 'lang' fields for all translated models
            $langFields = $this->getTranslationLangFields(
                $lang, 
                array(
                    'qualify' => $options['qualify'],
                    'alias' => $options['alias'],
                    'moduleAlias' => $options['moduleAlias'],
                ), 
                $options['joins']
            );
            $options['fields'] = array_merge($options['fields'], $langFields);
            $options['literals'] = DB::mergeLiterals(
                $options['literals'], 
                array('fields' => $langFields)
            );
            // add translation joins to translation tables
            $translationJoins = $this->getTranslationJoins(
                    $lang, 
                    array(
                        'alias' => $options['alias'],
                        'moduleAlias' => $options['moduleAlias'],
                    ), 
                    $options['joins']
            );
            $options['joins'] = array_merge($options['joins'], $translationJoins);
            // force array return type
            $options['array'] = true;
            // get the translation select query
            $query = DB::getSelectQuery($this->table, $options);
            $options['query'] = $this->getTranslationQuery(
                $query, 
                $lang, 
                array(
                    'alias' => $options['alias'],
                    'moduleAlias' => $options['moduleAlias'],
                ), 
                $options['joins']
            );
        }
        return DB::select($this->table, $options);
    }
    
    /**
     * Finds all records where specified condition field equals to given condition value.
     * 
     * 
     * @param string $conditionField Field name to be used for find condition
     * @param mixed $conditionValue Field value to be used for find condition
     * @param array $options See Model::find() options. The 'conditions' option
     *      is set explicitly by this method.
     * 
     * @return array|resource Array of retrieved record(s) or a resource if required
     */
    public function findBy($conditionField, $conditionValue, $options = array()) {
        // set conditions explicitly according to provided $field and $value
        $options['conditions'] = array($conditionField => $conditionValue);
        return $this->find($options);
    }
    
    /**
     * Finds the first record according to given options. 
     * 
     * @param array $options See Model::find() options. The 'first' option
     *      is set explicitly by this method.
     * 
     * @return array 
     */
    public function findFirst($options = array()) {
        // force find first
        $options['first'] = true;
        return $this->find($options);
    }
    
    /**
     * Finds first record where specified condition field equals to given condition value.
     * 
     * 
     * @param string $conditionField Field name to be used for find condition
     * @param mixed $conditionValue Field value to be used for find condition
     * @param array $options See Model::find() options. The 'conditions' and 'first' options
     *      are set explicitly by this method.
     * 
     * @return array Array of retrieved record fields
     */
    public function findFirstBy($conditionField, $conditionValue, $options = array()) {
        // set conditions explicitly according to provided $field and $value
        $options['conditions'] = array($conditionField => $conditionValue);
        return $this->findFirst($options);
    }
        
    /**
     * Finds the list of all record according to given options. 
     * The list is created according to provided 'key' and 'fields' 
     * 
     * If 'key' is 'id' and 'fields' contain 1 item e.g. array('id') and $options['plain'] is TRUE 
     * then the result looks like:
     *      array(
     *          1 => 1, //both 1 are ids
     *          2 => 2, 
     *          ...
     *      )
     * 
     * If 'key' is 'id' and 'fields' contain 2 and more items e.g. array('name', 'age', ...) 
     * or $options['plain] is FALSE then the result looks like:
     *      array(
     *          1 => array('name' => 'John', 'age' => 32, ...),
     *          2 => array('name' => 'Paul', 'age' => 26, ...), 
     *          ...
     *      )
     * 
     * If no fields are provided then fields of model schema are used and primary
     * key is used for list keys.
     *  
     * @param array $options All options of Model::find() plus following:
     *      - 'key' (string) Field name to be used for generated list keys. Defaults 
     *          to Model::$primaryKey.
     *      - 'fields' (array) List of fields names to be included in list items. 
     *          If you use some literals to define field then set an alias for
     *          the literal, e.g. 'CONCAT(firstname, " ", lastname) AS fullnamme'.
     *          Defautls to NULL.
     *      - 'accumulate' (bool) If TRUE then items are writen under key into array
     *          which allows to accumulate multiple items placed on the same key.
     *          Defaults to FALSE.
     *      - 'plain' (bool) If TRUE then in the case of single field the plain list 
     *          is returned (see above). If FALSE then even in case of single field
     *          the nested list is used (in the same manner as in case of many fields).
     *          This option is not considered in case of many fields. Defaults to TRUE.
     * 
     * @return array 
     */
    public function findList($options = array()) {
        $defaults = array(
            // only options used directly in this method have default values here
            'key' => $this->primaryKey,
            'moduleAlias' => false,
            'fields' => null,
            'allowFields' => null,
            'avoidFields' => null,
            'joins' => array(),
            'qualify' => false,
            'inflate' => false,
            'separate' => false,
            'translate' => true,
            'accumulate' => false,
            'plain' => true,
        );
        $options = array_merge($defaults, $options);
        // separate = TRUE is a shortcut for qualify = TRUE + inflate = TRUE
        if ($options['separate']) {
            $options['qualify'] = $options['separate'];
            $options['inflate'] = true;
        }
        // force find all
        $options['first'] = false;
        // force resource (to save time)
        $options['resource'] = true;
        $options['fields'] = (array)$options['fields'];
        // set the table alias
        $options['alias'] = $this->getAlias($options);
        // normalize fields
        $options['fields'] = $this->normalizeFields(
            $options['fields'], 
            array(
                'allowFields' => $options['allowFields'],
                'avoidFields' => $options['avoidFields'],
                'alias' => $options['alias'],
                'moduleAlias' => $options['moduleAlias'],
            ), 
            $options['joins']
        );
        if (empty($options['fields'])) {
            throw new Exception(__e(__FILE__, 'No fields has been resolved. Check \'allowFields\' and \'avoidFields\' options'));
        }
        $fields = $options['fields'];
        $key = $options['key'];
        
        $options['fields'][] = $key;
        $options['fields'] = array_unique($options['fields']);
        $result = $this->find($options);
        // create the list
        $list = array();
        if ($result) {
            $fields = DB::getName($fields, array(
                'qualify' => $options['qualify']
            ));
            $key = DB::getName($key, array(
                'qualify' => $options['qualify']
            ));
            if (count($fields) == 1 && $options['plain']) {
                $valueField = array_shift($fields);
                if ($options['accumulate']) {
                    while($record = DB::fetchArray($result)){
                        $list[$record[$key]][] = $record[$valueField];
                    }
                }
                else {
                    while($record = DB::fetchArray($result)){
                        $list[$record[$key]] = $record[$valueField];
                    }
                }
            }
            else {
                $fields = array_flip($fields);
                if ($options['accumulate']) {
                    while($record = DB::fetchArray($result)){
                        $list[$record[$key]][] = array_intersect_key($record, $fields);
                    }
                }
                else {
                    while($record = DB::fetchArray($result)){
                        $list[$record[$key]] = array_intersect_key($record, $fields);
                    }
                }
            }
        }
        if ($options['inflate']) {
            $list = Arr::inflate($list);
        }
        return $list;
    }
    
    /**
     * Finds the list of all record according to given options where specified 
     * condition field equals to given condition value.
     * The list is created according to provided 'key' and 'fields' 
     * 
     * If 'key' is 'id' and 'fields' contain 1 item e.g. array('id') and $options['plain'] is TRUE 
     * then the result looks like:
     *      array(
     *          1 => 1, //both 1 are ids
     *          2 => 2, 
     *          ...
     *      )
     * 
     * If 'key' is 'id' and 'fields' contain 2 and more items e.g. array('name', 'age', ...) 
     * or $options['plain] is FALSE then the result looks like:
     *      array(
     *          1 => array('name' => 'John', 'age' => 32, ...),
     *          2 => array('name' => 'Paul', 'age' => 26, ...), 
     *          ...
     *      )
     * 
     * If no fields are provided then fields of model schema are used and primary
     * key is used for list keys.
     * 
     * @param string $conditionField Field name to be used for find condition
     * @param mixed $conditionValue Field value to be used for find condition
     * @param array $options See Model::findList() options. The 'conditions' and 'first' options
     *      are set explicitly by this method.
     * 
     * @return array Array of retrieved record fields
     */
    public function findListBy($conditionField, $conditionValue, $options = array()) {
        // set conditions explicitly according to provided $field and $value
        $options['conditions'] = array($conditionField => $conditionValue);
        return $this->findList($options);
    }
    
    /**
     * Finds the count of records according to given options. 
     * 
     * ATTENTION: This method finds count for distinct primary key occurrences.
     * The 'group', 'order, 'limit', 'offset', 'page', 'paginate', 'distinct', 'resource'
     * 'inflate', 'qualify', 'separate' option are ignored.
     * 
     * @param array $options See Model::find() for options 'conditions' and 'having'.
     *          All other conditions are ignored or set explicitly by this method.
     * 
     * @return int Number of records satisfying provided 'conditions' or 'having'
     */
    public function findCount($options = array()) {
        // ignore following options
        $options['order'] = null;
        $options['limit'] = null;
        $options['offset'] = null;
        $options['page'] = null;
        $options['paginate'] = false;
        $options['distinct'] = false;
        $options['resource'] = false;
        $options['inflate'] = false;
        $options['qualify'] = false;
        $options['separate'] = false;
        $options['allowFields'] = null;
        $options['avoidFields'] = null;
        $options['allowLiterals'] = true;
        // force first value
        $options['first'] = true; 
        // optimize fields
        $fields = array();
        // - if there are defined HAVING we must keep fields because having refers to aliases defined in fields
        // To optimize we will keep just fields containing ' AS ' or ' as '
        if (!empty($options['having'])) {
            foreach($options['fields'] as $field) {
                if (strpos(strtolower($field), ' as ') !== false) {
                    $fields[] = $field;
                }
            }
            // this happens only when 'having' has incorrect value, e.g. array(array())
            if (!$fields) {
                $fields[] = $this->name . '.' . $this->primaryKey;
            }
            // in this case we must load all records but load then as resource
            $options['first'] = false; 
            $options['resource'] = true;
        }
        // if there is defined GROUP BY then we cannot use COUNT() as it is aggregation
        // function and together with GROUP BY it would return count inside each aggregated group.
        // To optimize we will keep just primary key field
        elseif (!empty($options['group'])) {
            $fields[] = $this->name . '.' . $this->primaryKey;
            // in this case we must load all records but load then as resource
            $options['first'] = false; 
            $options['resource'] = true;
        }
        else {
            // - add to fields the _count field (it is not possible to use GROUP BY as COUNT is aggregate function)
            $fields[] = 'COUNT(DISTINCT `' . $this->name . '`.`' . $this->primaryKey . '`) AS `_count`';
            $options['literals']['fields'] = true;
        }
////mojo: when refactored by converting 'having' to 'conditions' then let only this field (as it was implemented before)   
//        // - add to fields the _count field
//        $fields[] = 'COUNT(DISTINCT `' . $this->name . '`.`' . $this->primaryKey . '`) AS `_count`';
        // - overwrite the fields by optimized version (there is just count field and in the case
        // of nonempty 'having' there are preserved also all fields with aliases
        $options['fields'] = $fields;
        $result = $this->find($options);
        if (
            !empty($options['having'])
            || !empty($options['group'])
        ) {
            $count = (int)DB::getNumberOfRows($result);
        }
        else {
            $count = (int)$result['_count'];
        }
        return $count;
    }
    
    /**
     * Finds the count of records according to given options. 
     * 
     * ATTENTION: This method finds count for distinct primary key occurrences.
     * The 'group', 'order, 'limit', 'offset', 'page', 'paginate', 'distinct', 'resource'
     * 'inflate', 'qualify', 'separate' option are ignored. The 'conditions' option
     * is set explicitly.
     * 
     * @param string $conditionField Field name to be used for find condition
     * @param mixed $conditionValue Field value to be used for find condition
     * @param array $options See Model::find() options for 'having'.
     *          All other options are ignored or set explicitly by this method.
     * 
     * @return int Number of records satisfying provided 'conditions' or 'having'
     */
    public function findCountBy($conditionField, $conditionValue, $options = array()) {
        // set conditions explicitly according to provided $field and $value
        $options['conditions'] = array($conditionField => $conditionValue);
        return $this->findCount($options);
    }
    
    /**
     * Finds all duplicit values in provided field according to provided options
     * in following form:
     * 
     *      array (
     *          '{duplicitValue1}' => {count1},
     *          '{duplicitValue2}' => {count2},
     *          ...
     *      );
     * 
     * @param string $field Name of field to check for duplicities
     * @param array $options See Model::find() options plus following can be used:
     *      - 'rows' (bool) If TRUE, then all records containing duplicit values are returned
     * 
     * @return array 
     */
    public function findDuplicit($field, $options = array()) {
        // the target query, e.g.: SELECT COUNT(`email`) AS `_duplicities` FROM users GROUP BY `email` HAVING _duplicities > 1
        $defaults = array(
            'conditions' => null,
            'having' => null,
            'rows' => false,
        );
        $options = array_merge($defaults, $options);
        // look for duplicities
        $duplicitOptions = $options;
        // force group by
        $duplicitOptions['group'] = $field;
        // force fields and literals
        $backtickedField = DB::encloseName($field);
        $duplicitOptions['key'] = $field;
        $duplicitOptions['fields'] = array("COUNT($backtickedField) AS `_duplicities`");
        $duplicitOptions['allowFields'] = null;
        $duplicitOptions['avoidFields'] = null;
        $duplicitOptions['literals'] = array('fields' => "COUNT($backtickedField) AS `_duplicities`");
        $duplicitOptions['allowLiterals'] = true;
        // add having
        $duplicitOptions['having'] = array_merge((array)$duplicitOptions['having'], array('_duplicities > 1'));
        $result = $this->findList($duplicitOptions);
        if ($result && $options['rows']) {
            $duplicitValues = array_keys($result);
            $options['conditions'] = array_merge(
                array((array)$options['conditions']), 
                array($field => $duplicitValues)
            );
            $result = $this->find($options);
        }
        return $result;
    }
    
    /**
     * Finds a value of given field in first found record according to provided
     * $options
     * 
     * @param string $field
     * @param array $options See Model::find() options. The 'fields', 'separate'
     *      and 'qualify' options are set explicitly by this method.
     * 
     * @return string Field value. If nothing found then NULL. ATTENTION: Be aware 
     *      that any value returned from DB is always string (except of NULL)!
     */
    public function findField($field, $options = array()) {
        // set conditions explicitly according to provided $field and $value
        $options['fields'] = array($field);
        $options['allowFields'] = null;
        $options['avoidFields'] = null;
        $options['separate'] = false;
        $options['qualify'] = true;
        $result = $this->findFirst($options);
        return Sanitize::value($result[$field]);
    }
    
    /**
     * Finds a value of given field in first found record where specified 
     * condition field equals to given condition value.
     * 
     * @param string $field Field name, e.g. 'my_field' or 'MyModel.my_field'
     * @param string $conditionField Field name to be used for find condition
     * @param mixed $conditionValue Field value to be used for find condition
     * 
     * @param array $options See Model::find() options. The 'fields', 'separate',
     *      'qualify', 'conditions' and 'first' options are set explicitly by this method.
     * 
     * @return mixed Field value. If nothing found then NULL. ATTENTION: Be aware 
     *      that any value returned from DB is always string (except of NULL)!
     */
    public function findFieldBy($field, $conditionField, $conditionValue, $options = array()) {
        // set conditions explicitly according to provided $field and $value
        $options['fields'] = array($field);
        $options['allowFields'] = null;
        $options['avoidFields'] = null;
        $options['separate'] = false;
        $options['qualify'] = true;
        $result = $this->findFirstBy($conditionField, $conditionValue, $options);
        return Sanitize::value($result[$field]);
    }
    
    /**
     * Finds an unique value for given field
     * 
     * ATTENTION: If 'reserve' is set FALSE then it is up to user to ensure that 
     * the returned value will stay unique till it is written to DB. The common
     * pattern to do this is:
     * 
     *      DB::reserveTables('MyModel_myMethod', $MyModel->getPropertyTable)
     *      $slug = $MyModel->getUniqueFieldValue('MyModel.slug', 'my-slug')
     *      $MyModel->save(array('slug' => $slug, ...));
     *      DB::unreserveTables('MyModel_myMethod');
     *
     * @param string $field Field name to search the unique value for. Use qualified 
     *          name if the field is translated.
     * @param string|int|array $value String value to be made unique by numeric suffix
     *      or integer value (not integer string '123' but 123) to be made unique 
     *      by increment or if provided as array('length' => ...[,'chars' => ...]) 
     *      then the random value is generated by Str::getRandom() method.
     * @param array $options Optional. Standard find options can be used + following:
     *      - 'tries' (int) Number of tries. If 0 then number of tries is not limited
     *          and the method search till the first free name is found. Defaults to 0.
     *      - 'conditions' (array) Conditions to filter out unigue value records group. 
     *          Defaults to empty array.
     *      - 'avoidValues' (array) Values which should not be used. Defaults to empty array.
     *      - 'separator' (string) Separator to be used for numeric suffix. This is used
     *          only in case that $value is provided as string. Defaults to '-'.
     *      - 'reserve' (bool|string) If not empty value then an empty record is created with the unique value
     *          in specified $field column. Use Model::getPropertyId() to get id of new created record. 
     *          If 'reserve' is set to 'byUniqueIndex' then the reservation is done by $field unique
     *          index (if the field has defined unique index). Othervise 
     *          it is done by search and table reservation to ensure atomicity.
     *          In case of reservation the $field must be defined in actual model schema.
     *          If 'reserve' is FALSE the the $field can be also from joined models.
     *          Defaults to FALSE. ATTENTION: if reservation is done (and really processed) by unique index
     *          then discontinuites in primary key can occur as each new attempt 
     *          to insert new record to table will increase autoIncrement value
     *          regardless to success of insert. 
     * 
     * @return string|bool An unique value version. FALSE if number of tries were exceeded  
     *      or if 'reserve' option was TRUE and tables reservation has failed.
     *      If so then make tables reservation outside of this method and set more tries.
     *      NOTE: If the $field has unique index then table reservation is not done.
     */
    public function getUniqueFieldValue($field, $value, $options = array()) {
        $defaults = array(
            'tries' => 0,
            'conditions' => array(),
            'avoidValues' => array(),
            'separator' => '-',
            'reserve' => false,
        );
        $options = array_merge($defaults, $options);
        $options['avoidValues'] = (array)$options['avoidValues'];
        $options['fields'] = array($field);
        $options['allowFields'] = null;
        $options['avoidFields'] = null;
        $separator = $options['separator'];
        $tries = $options['tries'];
        if (
            is_array($value) 
            && isset($value['length'])
        ) {
            $valueType = 'a'; //array
            $value = array_merge(array('chars' => null), $value);
            $uniqueValue = Str::getRandom($value['length'], $value['chars']);
        }
        elseif (is_int($value)) {
            $valueType = 'i'; //int
            $uniqueValue = $value;
        }
        elseif (!is_array($value)) {
            $valueType = 's'; //string
            $uniqueValue = (string)$value;
        }
        else {
            throw new Exception(__e(__FILE__, 'Invalid value provided. Value must be either string or integer or array(\'length\' => ...[,\'chars\' => ...])'));
        }
        // check for field value reservation
        if ($options['reserve']) {
            // get field name without qualifier
            $fieldName = DB::getName($field);
            // verify if $field is defined in the schema
            if (!isset($this->schema[$fieldName])) {
                throw new Exception(
                    __e(__FILE__, 'If you would like to reserve field unique value then the field %s must be defined in %s model schema', $fieldName, $this->name));
            }
            $hasUniqueIndex = false;
            if (strtolower($options['reserve']) === 'byuniqueindex') {
                $hasUniqueIndex = DB::hasField($this->table, $fieldName, array(
                    'index' => array('primary', 'unique'),
                ));
            }
            // if the field has not unique index then the table must be reserved
            // The search of the unique value is done here below
            if (!$hasUniqueIndex) {
                try {
                    DB::reserveTables('Model_getUniqueFieldValue', $this->table);
                }
                catch (Exception_DB_TablesReservationFailure $e) {
                    return false;
                }
            }
            // if the field has unique index then there is no need to do table reservation
            // as the uniquity check and the creation of the record can be done in one step directly here
            else {
                for($count = 1;;$count++) {
                    if ($tries != 0 && $count > $tries) {
                        return false;
                    }
                    if (!in_array($uniqueValue, $options['avoidValues'])) {
                        $data[$fieldName] = $uniqueValue;
                        try {
                            $result = $this->save($data, array('validate' => false, 'normalize' => false, 'reserve' => false));
                        } 
                        catch (Throwable $e) {
                            // if DB error is not ER_DUP_KEYNAME (http://dev.mysql.com/doc/refman/5.0/en/error-messages-server.html#error_er_dup_keyname)
                            // then rethrow the exception
                            if ($e->getCode() != 1062) {
                                throw $e;
                            }
                            $result = false;
                        }
                        if ($result) {
                            return $uniqueValue;
                        }
                    }
                    if ($valueType === 'a') {
                        $uniqueValue = Str::getRandom($value['length'], $value['chars']);
                    }
                    elseif ($valueType === 'i') {
                        $uniqueValue++;
                    }
                    else {
                        $uniqueValue = $value . $separator . $count;
                    }
                }
            }
        }
        // put actual conditions into braces
        $options['conditions'] = DB::nestConditions($options['conditions']);
        $options['conditions'][$field] = $uniqueValue;
        for (
            $count = 1; 
            in_array($uniqueValue, $options['avoidValues']) || $this->findFirst($options); 
            $count++
        ) {
            if ($tries != 0 && $count > $tries) {
                return false;
            }
            if ($valueType === 'a') {
                $uniqueValue = Str::getRandom($value['length'], $value['chars']);
            }
            elseif ($valueType === 'i') {
                $uniqueValue++;
            }
            else {
                $uniqueValue = $value . $separator . $count;
            }
            $options['conditions'][$field] = $uniqueValue;
        }
        // check for field value reservation
        if (
            $options['reserve'] 
            && !$hasUniqueIndex
        ) {
            $data[$fieldName] = $uniqueValue;
            $result = $this->save($data, array('validate' => false, 'normalize' => false));
            DB::unreserveTables('Model_getUniqueFieldValue');
            if (!$result) {
                return false;
            }
        }
        return $uniqueValue;
    }
    
    /**
     * Returns value of specified $field in provided $data. If the $field is not
     * present in $data then value is retrieved from database record (according to
     * primary key value in $data). If there is no primary key in data or there is
     * no corresponding record in database then a default value defined in Model::$schema 
     * is returned.
     * 
     * @param string $field Field name
     * @param array $data Record data
     * 
     * @return mixed Resolved field value
     * 
     * @throws Exception on invalid $field name (undefined in Model::$schema)
     */
    public function getFieldValue($field, $data) {
        if (array_key_exists($field, $data)) {
            return $data[$field];
        }
        if (!empty($data[$this->primaryKey])) {
            $result = $this->findFirstBy($this->primaryKey, $data[$this->primaryKey], array(
                'fields' => array($field)
            ));
            if ($result) {
                return $result[$field];
            }
        }
        if (!empty($this->schema[$field])) {
            if (array_key_exists('default', $this->schema[$field])) {
                return $this->schema[$field]['default'];
            }
            $result = $this->normalizeEmptyValues(array($field => null));
            return $result[$field];
        }
        throw new Exception(__e(__FILE__, 'Field "%s" is not defined in %s::$schema', $field, $this->name));
    }
    
    /**
     * Checks if a new record should be created or an existing updated according
     * to provided save $data and $options
     * 
     * @param array $data Data to be passed to Model::save()
     * @param array $options Optional. Options to be passed to Model::save()
     * 
     * @return bool
     */
    public function isCreation($data, $options = array()) {
        return (
            !empty($options['create'])
            || (
                empty($data[$this->primaryKey]) 
                && (
                    !isset($options['on'])
                    || $options['on'] !== 'update'
                )
            )
        );
    }
    
    /**
     * Saves data of the model and its related models
     * 
     * Its up to Model childs to implement this method!
     * 
     * @param array $data
     * 
     * @param array $options Optional. Create any new options to parametrize the
     *      processing. To parametrize included models the 'contain' option is recommended:
     *      - 'contain' (array) List of model names which should be included in processing
     *     
     * @return array|bool Array of updated or created record data containing id of created record
     *      and other values after normalization or file uploads. FALSE if validation fails.  
     */
    public function saveAll($data, $options = array()) {
        throw new Exception('Please implement this method');
    }
        
    /**
     * Saves provided data. If data contains a primary key field then the given record is
     * updated. If data does not contain primary key or primary key has an empty value
     * then a new record is created.
     * 
     * Only one record can be provided and only one model data can be saved. 
     * Translations are done automatically.
     * 
     * ATTENTION: If Exception_DB_RecoverableDeadlock is thrown then the easiest way
     * to resolve the deadlock is to rollback and repeat the whole transaction again.
     * 
     * @param array $data
     * @param array $options Following options are available:
     *      - 'create' (bool) If TRUE then creation is forced. Otherwise decided 
     *          according to presence of Model::$primaryKey in data
     *      - 'allowFields' (string|array) Single field or list of fields which 
     *          are validated and saved. Default to empty, means all data fields are
     *          validated and all those which are in DB table are saved.
     *      - 'avoidFields' (string|array) Single field or list of fields which 
     *          are not validated neither saved. Default to empty, means all data fields are
     *          validated and all those which are in DB table are saved.
     *      - 'normalize' (bool) If TRUE then $data are normalized before they are 
     *          validated (if 'validate' is TRUE) and saved. Model::normalize() is used for this purpose.
     *          Defaults to TRUE.
     *      - 'validate' (bool) If TRUE then $data are validated before they are saved.
     *          Defaults to TRUE.
     *      - 'allowValidations' (array) List of allowed validations. For sake of
     *          this options the validations must be named, e.g. array('fieldName' => array(
     *          'validationName01', 'validationName02', ...)). Defauts to NULL.
     *      - 'avoidValidations' (array) List of validations to avoid. For sake of
     *          this options the validations must be named, e.g. array('fieldName' => array(
     *          'validationName01', 'validationName02', ...)). Use array('fieldName' => TRUE) 
     *          to avoid all for given field. Defaults to NULL.
     *      - 'alternative' (string|array) Single validation alternative name or an array
     *          of such names. If empty then only general validations are executed.
     *          Defaults to NULL.
     *      - 'resetErrors' (bool) If TRUE then model validation errors are reset 
     *          before validation. Defaults to TRUE.
     *      - 'translate' (bool) If TRUE then translation is made if actual model 
     *          has translated fields. If FALSE then translation is turned off. Defaults to TRUE.
     *      - 'lang' (string) Explicit lang to be used for translations. Applies
     *          only if 'translate' option is TRUE. Defaults to NULL, means lang
     *          is searched in $data['lang'] and if not found then set to App::$lang.
     *      - 'processFiles' (bool) If TRUE then file fields contained in data are
     *          processed. It means that incoming and obsolete files are prepared
     *          and after successful save they are cleaned up. If FALSE all 
     *          this processing is skipped and its up to user to do it. Defaults to TRUE.
     *      - 'cleanUpFiles' (bool) If FALSE then the clean up part of files processing 
     *          is skipped. This can be usefull in case of DB transactions when the 
     *          files should be cleaned up only after definitive result of transaction. 
     *          Defaults to TRUE.
     *      - 'literals' (array) Associative array of literals used in other options.
     *          It can contain following keys: 'data', 'conditions', 'having', 'joins'.
     *          Each of items is either TRUE to process whole definition of that item as literals 
     *          or string to provide single literal or a plain array of such strings, 
     *          e.g.: array('data' => 'CONCAT(`first_name`, ' ', `last_name`)')
     *      - 'ignore' (bool) This applies only if new record is created. If in such a
     *          case this option is set to TRUE then INSERT IGNORE query is created. 
     *          Defaults to FALSE.
     *      - 'reserve' (bool|array) If TRUE then the query is executed only if $table 
     *          reservation succeed - means if the table is succesfully or has been already 
     *          reserved by actual process. If array then passed as options to DB::reserveTables().
     *          Defaults to TRUE.
     * 
     * @return array|bool Array of updated or created record data containing id of created record
     *      and other values after normalization or file uploads. FALSE if validation fails.  
     * 
     * @throws Exception_DB_RecoverableDeadlock
     * @throws Exception_DB_TablesReservationFailure
     * @throws Exception on file tranfer/transform failure or DB request failure
     */
    public function save($data, $options = array()) {
        // check for model table
        if (empty($this->table)) {
            throw new Exception(
                "This method cannot be used as the model doesn't use any database table. " 
                . "Define your own version of this method or use this model only for data validations."
            );
        }
        $defaults = array(
            'create' => false,  
            'allowFields' => null,
            'avoidFields' => null,
            'normalize' => true,
            'validate' => true,
            'allowValidations' => null, // '{fieldName}' => array('{validationName01}', '{validationName02}', ...)
            'avoidValidations' => null, // '{fieldName}' => array('{validationName01}', '{validationName02}', ...)
            'alternative' => null, // '{alternative}' or array('{alternative01}', '{alternative02}', ...)
            'lang' => null,
            'translate' => true,
            'processFiles' => true,
            'cleanUpFiles' => true,
        );
        $options = array_merge($defaults, $options);
        // translations use explicit 'query' option which has higher precedence 
        // than other options, see DB::select/update/delete(), so remove this option
        // on each new call of this method. The 'array' option is related to 'query'
        // and also added by translations functionality
        unset($options['query']);
        unset($options['array']);
        // sanitize data fields
        $data = Sanitize::arrayKeys($data, array(
            'allowKeys' => $options['allowFields'],
            'avoidKeys' => $options['avoidFields'],
        ));
        // decide between insert and update
        $options['on'] = null; // to make Model::isCreation() resolve correctly
        $options['create'] = $this->isCreation($data, $options); 
        $options['on'] = $options['create'] ? 'create' : 'update';
        // normalize
        if ($options['normalize']) {
            $data = $this->normalize($data, $options);
            // if once normalized, turn it off (to not make it duplicitly during validation)
            $options['normalize'] = false;
        }
        // validate
        if (
            $options['validate'] 
            && !$this->validate($data, $options)
        ) {
            return false;
        }
        // after validation constrain allowFields only to table fields
        $tableFields = $this->getFields();
        if (empty($options['allowFields'])) {
            $options['allowFields'] = $tableFields;
        }
        else {
            $options['allowFields'] = array_intersect($tableFields, (array)$options['allowFields']);
        }        
        // add modified if relevant
        if (in_array('modified', $tableFields)) { //@todo - check if field type is DATE or DATETIME
            $data = array_merge(array('modified' => null), $data);
            if (!in_array('modified', $options['allowFields'])) {
                $options['allowFields'][] = 'modified';
            }
        }
        // INSERT
        if ($options['create']) {
            // remove empty primaryKey
            if (empty($data[$this->primaryKey])) {
                unset($data[$this->primaryKey]);
            }
            // add created if relevant
            if (in_array('created', $tableFields)) { //@todo - check if field type is DATE or DATETIME
                $data = array_merge(array('created' => null), $data);
                if (!in_array('created', $options['allowFields'])) {
                    $options['allowFields'][] = 'created';
                }
            }
            // check for translations
            if (
                $options['translate'] 
                && $this->translatedFields
                && ($lang = $this->getTranslationLang($options, $data)) != $this->translationLangs['default']
            ) {
                $translationData = $this->getTranslationData($data, $options, $lang);
                // if translations are kept in same table as original data 
                // then merge translation data into original data
                if ($this->translationTable == $this->table) {
                    $data = array_merge($data, $translationData);
                }
            }
            // check for files
            if (
                $options['processFiles'] 
                && $this->fileFields
            ) {
                // turn off translations but keep the original options for further processing
                $data = $this->prepareFiles($data, array_merge(
                    $options,
                    array('translate' => false)
                ));
            }
            // insert
            try {
                $this->id = DB::insert($this->table, $data, $options);
            }
            catch (Throwable $e) {
                // clean up files on failure
                if (
                    $options['processFiles'] 
                    && $options['cleanUpFiles'] 
                    && $this->fileFields
                ) {
                    $this->cleanUpFiles(false);
                }
                throw $e;
            }
            // clean up files on success
            if (
                $options['processFiles'] 
                && $options['cleanUpFiles'] 
                && $this->fileFields
            ) {
                $this->cleanUpFiles(true);
            }
            // set new primary key in data
            $data[$this->primaryKey] = $this->id;
            // finish translation: 
            // Insert the translation if translations are kept in separate table.
            // Make the insertion also in case of default lang, to prepare empty
            // translation record for future updates of non-default langs
            if (
                $options['translate'] 
                && $this->translatedFields 
                && $this->translationTable != $this->table
            ) {
                $foreignKey = $this->getForeignKey();
                $translationData[$foreignKey] = $this->id;
                $options['allowFields'][] = $foreignKey;
                DB::insert($this->translationTable, $translationData, $options);
            }
        }
        // UPDATE
        else {
            // set the table alias
            $options['alias'] = $this->name;
            // force update conditions based on primaryKey value
            $this->id = $data[$this->primaryKey];
            $options['conditions'] = array($this->primaryKey => $this->id);
////mojo: primaryKey is missing in returned data, so keep it           
//            // remove primary key from data, to not rewrite it uselessly
//            unset($data[$this->primaryKey]);
            // check for translations
            if (
                $options['translate'] 
                && $this->translatedFields
                && ($lang = $this->getTranslationLang($options, $data)) != $this->translationLangs['default']   
            ) {
                $options['joins'] = $this->getTranslationJoins($lang);
                $translationData = $this->getTranslationData($data, $options, $lang);
                $data = array_merge($data, $translationData);
            }
            // check for files
            if (
                $options['processFiles'] 
                && $this->fileFields
            ) {
                // turn off translations 
                $options['translate'] = false;
                $data = $this->prepareFiles($data, $options);
            }
            // update
            try {
                DB::update($this->table, $data, $options);
            }
            catch (Throwable $e) {
                // clean up files on failure
                if (
                    $options['processFiles'] 
                    && $options['cleanUpFiles'] 
                    && $this->fileFields
                ) {
                    $this->cleanUpFiles(false);
                }
                throw $e;
            }
            // clean up files on success
            if (
                $options['processFiles'] 
                && $options['cleanUpFiles'] 
                && $this->fileFields
            ) {
                $this->cleanUpFiles(true);
            }
        }
        return $data;
    }
    
    /**
     * Saves provided batch in database (deletes, creates and updates).
     * 
     * ATTENTION: Batch actions are executed in order specified by 'actionsOrder' option (defaults 
     * to 'delete', 'create', 'update') regardless to order in which they are placed in $batch array.
     * Delete is mostly needed to update HABTM relation when old ones are deleted 
     * and the new ones are created.
     *  
     * ATTENTION: Batch create and update data are not normalized nor validated nor translated(@todo). 
     * To normalize and/or validate batch records use the processed records model ::normalize()
     * and ::validate() methods. Apply this methods to each record separatelly during
     * batch creation. !!!BUT in case of 'update' batch action, the empty values ('', NULL) 
     * in batch records are normalized similary to Model::normalizeEmptyValues() 
     * (except of empty datetimes '0000-00-00 00:00:00' which are very rare) so these 
     * empty values you do not need to normalize on batch creation.
     * 
     * ATTENTION: In case of 'create' batch action there can be also translation fields
     * included in batch records, e.g. array('name' => 'Juraj', '_name_en' => 'George').
     * 
     * ATTENTION: Turn off SQL logs by App::setSqlLogging(false) when saving large 
     * batches to not get error ERR_RESPONSE_HEADERS_TOO_BIG
     * 
     * ATTENTION: If an 'update' batch is provided then this method creates temporary
     * tables. This breaks (autocommits) existing transactions in database. It means
     * you cannot use batch update inside transaction. On other side internally (after
     * the temporary tables are created) the batch is saved in a transaction (either
     * everything is ok or otherwise nothing is changed)
     * 
     * @param array $batch Array containing batch records to be saved
     *      in structure like:
     * 
     *      array(
     *          'create' =>  array(      // records to be created
     *              'Model01' => array(  // batch for Model01
     *                  array(...),      // record 1
     *                  array(...),      // record 2
     *                  array(...),      // record 3
     *                  ...
     *              ),
     *              'ModuleXY.Model02' => array(  // batch for Model02 in ModuleXY
     *                  array(...),      // record 1
     *                  array(...),      // record 2
     *                  array(...),      // record 3
     *                  ...
     *              ),
     *              ...
     *          )   
     *          'update' =>  array(      // records to be updated (must contain primary keys)
     *              'Model03' => array(  // batch for Model03
     *                  array(...),      // record 1
     *                  array(...),      // record 2
     *                  array(...),      // record 3
     *                  ...
     *              ),
     *              ...
     *          )   
     *          'delete' =>  array(          // records to be deleted (specified by conditions)
     *              'Model04' => array(      // batch for Model04
     *                  'my_col' => 2,       // conditions delete records
     *                  'id' => 3,           // conditions delete record
     *              ),
     *              ...
     *          )   
     * 
     * NOTE: In saved data set manually 'created' and/or 'modified' fields to NULL
     * to ensure actualization of these fields in DB to actual datetime.
     * 
     * NOTE: To save the memory provide record batches by reference. e.g.:
     * 
     *      array(
     *          'update' => array(
     *              'Model03' => &updateBatchRecords,
     *              ...
     *          )
     *      )
     * 
     * @param array $options Following can be used:
     *      - 'actionsOrder' (array) Array of action names 'delete', 'create' and
     *          'update' to specify their order. If some action names are missing
     *          then they are internally added to the end (in order 'delete', 'create', 'update').
     *          Defaults to array('delete', 'create', 'update').
     *      - 'reserve' (bool|array) If TRUE then the query is executed only if $table 
     *          reservation succeed - means if the table is succesfully or has been already 
     *          reserved by actual process. If array then passed as options to DB::reserveTables().
     *          Defaults to TRUE.
     *      - 'ignoreNewValueIf' (array|NULL|string|float|int|FALSE) Allows to define 
     *          value(s) or conditions when the new values will be ignored for
     *          specified fields. This applies only to 'update' batch. 
     *          If the new value of a field is the specified one or if the specified 
     *          conditions are met then the field value is not updated but preserved. 
     *          E.g. you are importing product 'stock' and 'price' in one file. If 
     *          the 'price' is provided as special value -1 then only 'stock' amount 
     *          is updated. The structure of provided array is: 
     * 
     *              array(
     *                  null,                                                   // default ignored value for all unlisted fields
     *                  'Model01.my_field_01' => {specialValue},                // do not update value of Model01.my_field_01 if its new value is {specialValue}   
     *                  'Model01.name' => null,
     *                  'ModuleXY.Model02.description' => array('', null),      // list of ignored new values for field ModuleXY.Model02.description
     *                  'ModuleXY.Model02.price' => -1
     *                  'ModuleXY.Model02.availability' => array(               // conditions to ignore new value for field ModuleXY.Model02.availability
     *                      array(
     *                          'old.active' => 0,
     *                          'OR',
     *                          'new.availability' => array('', null),
     *                      )
     *                      'old.priority < new.priority',
     *                  )
     *                  ...,
     *              )
     * 
     *          If scalar value (NULL|string|float|int) or nonassociative array of 
     *          scalar values is provided then it is used as default ignored new value(s) 
     *          for all updated fields. The ignored new value can be any string value
     *          as all new values are stored as strings (in temporary update table). 
     *          It means that even for non-string columns (integer, float, ...) you can 
     *          use string ignored value (e.g. '_UNKNOWN_'). The only exception are blob columns, 
     *          which type is not changed in temporary update table. Another special values available
     *          for all collumns is NULL because field of any type can be set to NULL 
     *          (regardless if in updated table schema the field is or is not nullable). 
     *          Defaults to FALSE means this option is not considered.
     *          Use 'debugUpdateQuery' option to verify the resulting SQL query(ies).
     *      - 'updateOldValueIf' (array|FALSE) Allows to define 
     *          value(s) or conditions when the old values will be updated for 
     *          specified fields. This applies only to 'update' batch.
     *          If old value of a field is the specified one or if the specified 
     *          conditions are met then the field value is updated. The structure 
     *          of provided array is: 
     * 
     *              array(
     *                  'Model01.my_field_01' => {specialValue},                // update value of Model01.my_field_01 if its old value is {specialValue}   
     *                  'Model01.name' => null,
     *                  'ModuleXY.Model02.description' => array('', null),      // list of updated old values for field ModuleXY.Model02.description
     *                  'ModuleXY.Model02.price' => 0
     *                  'ModuleXY.Model02.availability' => array(               // conditions to update old value for field ModuleXY.Model02.availability
     *                      'old.active' => 1,
     *                      'new.availability !=' => array('', null),
     *                      'old.priority < new.priority',
     *                  )
     *                  ...,
     *              )
     * 
     *          Defaults to FALSE means this option is not considered.
     *          ATTENTION: You must consider type casting if comparing values of 
     *          different types. E.g. 0 in INTEGER column gives TRUE compared to any string (empty or not empty). 
     *          Use 'debugUpdateQuery' option to verify the resulting SQL query(ies).
     *      - 'ignore' (bool) If TRUE then INSERT IGNORE queries are done in case of 
     *          'create' and 'update' batch actions. Defaults to FALSE.
     *      - 'recoverableDeadlocksRetries' (int) How many retries should be done on
     *          recoverable deadlock? See the phpDoc of Exception_DB_RecoverableDeadlock class.
     *          Defaults to 1, it means that on recoverable deadlock failure 1 retry is done.
     *      - 'debugUpdateQuery' (bool) If TRUE then update query of each updated model 
     *          is sent to debug output using App::debug(). Defaults to FALSE.
     *      - 'deleteUpdateTables' (bool) If TRUE then temporary update table of each updated model 
     *          is deleted after the batch is processed. If FALSE then these tables are 
     *          preserved containing batch data. Defaults to TRUE.
     * 
     * @return bool TRUE on success. An exception on any kind of batch failure or if batch contains 'update'
     *      and used inside transaction. This last exception is caused by dropping
     *      and creating batch update temporary tables which breaks transaction. 
     *      The possible solution is to create these tables out of transaction by method Model::createBatchUpdateTables() (@todo)
     * 
     * @throws Exception on any kind of batch failure or if batch contains 'update'
     *      and used inside transaction. This last exception is caused by dropping
     *      and creating batch update temporary tables which breaks transaction. 
     *      The possible solution is to create these tables out of transaction by method Model::createBatchUpdateTables() (@todo)
     */
    public function saveBatch($batch, $options = array()) {
        $defaults = array(
            'actionsOrder' => array('delete', 'create', 'update'),
            'reserve' => true,
            'ignoreNewValueIf' => false,
            'updateOldValueIf' => false,
            'ignore' => false,
            'recoverableDeadlocksRetries' => 1,
            'debugUpdateQuery' => false,
            'deleteUpdateTables' => true,
        );
        $options = array_merge($defaults, $options);
        // reorder batch actions according order specified in options
        // and remove empty batch items
        $actionsOrder = array_reverse(array_keys(
            array_flip((array)$options['actionsOrder']) + array_flip(array('delete', 'create', 'update'))
        ));
        foreach ($actionsOrder as $action) {
            if (
                !empty($batch[$action])
                && ($batch[$action] = array_filter($batch[$action]))
            ) {
                $batch = array($action => $batch[$action]) + $batch;
            }
            else {
                unset($batch[$action]);
            }
        }
        // finish if there is nothing to save
        if (empty($batch)) {
            return true;
        }
        // reserve tables and get update temporary tables and normalize ignoreNewValueIf
        $defaultIgnoreNewValue = null;
        $hasDefaultIgnoreNewValue = false;
        if (
            ($hasIgnoreNewValue = 
                array_key_exists('ignoreNewValueIf', $options) 
                && $options['ignoreNewValueIf'] !== false)
        ) {
            // single scalar value or nonassoc array of scalar values
            if (
                !is_array($options['ignoreNewValueIf'])
                ||
                !Validate::assocArray($options['ignoreNewValueIf'])    
            ) {
                $hasDefaultIgnoreNewValue = true;
                $defaultIgnoreNewValue = $options['ignoreNewValueIf'];
                // prepare ignoreNewValueIf for normalization to array form here below
                $options['ignoreNewValueIf'] = array();
            }
            // associative array with default value on nonassociative key 0
            elseif (array_key_exists(0, $options['ignoreNewValueIf'])) {
                $hasDefaultIgnoreNewValue = true;
                $defaultIgnoreNewValue = $options['ignoreNewValueIf'][0];
                // prepare ignoreNewValueIf for normalization to array form here below
                unset($options['ignoreNewValueIf'][0]);
            }
        }
        $tablesToReserve = array();
        $updateTables = array();
        foreach ($batch as $batchAction => $models) {
            foreach ($models as $modelName => $actionData) {
                if (empty($actionData)) {
                    continue;
                }
                // load model
                $model = explode('.', $modelName);
                if (count($model) == 1) {
                    $Model = $this->loadModel($model[0], true);
                }
                elseif (count($model) == 2) {
                    $Model = App::loadModel($model[0], $model[1], true);
                }
                else {
                    throw new Exception(__e(__FILE__, 'Invalid model name %s', $modelName));
                }
                $table = $Model->getPropertyTable();
                $tablesToReserve[] = $table;
                if ($batchAction === 'update') {
                    $updateTable = '_' . $table . '_batch_update_tmp';
                    $tablesToReserve[] = $updateTable;
                    $primaryKeyField = $Model->getPropertyPrimaryKey();
                    $record = reset($actionData);
                    if (empty($record[$primaryKeyField])) {
                        throw new Exception(__e(__FILE__, 'Missing primary key field (%s) in update records', $primaryKeyField));
                    }
                    $recordFields = DB::getDataFields($record, array(
                        'allowFields' => $Model->getFields()
                    ));
                    if (($diff = array_diff(array_keys($record), $recordFields))) {
                        throw new Exception(__e(__FILE__, 'Update records contain unexisting fields. Fields %s are not defined in schema of %s model', implode(', ', $diff), $modelName));
                    }
                    $schema = $Model->getPropertySchema();
                    $updateSchema = array();
                    foreach ($recordFields as $field) {
                        $fieldOptions = $schema[$field];
                        if ($field !== $primaryKeyField) { 
                            // use VARCHAR type as substitute for all types except of
                            // TEXT and BLOB types. This is done to allow normalization of 
                            // empty values and to not convert empty string to 0
                            // in case of non-text fields (integer, float, ...)
                            $fieldType = strtoupper($fieldOptions['type']);
                            if (
                                strpos($fieldType, 'TEXT') === false
                                && strpos($fieldType, 'BLOB') === false
                            ) {
                                $fieldOptions['type'] = 'varchar';
                                $fieldOptions['length'] = 255;
                            }
                            // remove indexes (except primary) from update table schema
                            if (isset($fieldOptions['index'])) {
                                unset($fieldOptions['index']);
                            }
                            // remove non NULL default values
                            if (
                                array_key_exists('default', $fieldOptions)
                                && $fieldOptions['default'] !== null
                            ) {
                                unset($fieldOptions['default']);
                            }
                            if ($hasIgnoreNewValue) {
                                $qualifiedField = $modelName . '.' . $field;
                                if (
                                    $hasDefaultIgnoreNewValue
                                    && !array_key_exists($qualifiedField, $options['ignoreNewValueIf'])
                                ) {
                                    $options['ignoreNewValueIf'][$qualifiedField] = $defaultIgnoreNewValue;
                                }
                                // allow NULLs if awaited as ignored value and set accordingly
                                // default values 
                                if (array_key_exists($qualifiedField, $options['ignoreNewValueIf'])) {
                                    if (
                                        $options['ignoreNewValueIf'][$qualifiedField] === null
                                        ||
                                        is_array($options['ignoreNewValueIf'][$qualifiedField])
                                        && in_array(null, $options['ignoreNewValueIf'][$qualifiedField], true)
                                    ) {
                                        $fieldOptions['null'] = true;
                                        $fieldOptions['default'] = null;
                                    }
                                }
                            }
                        }
                        $updateSchema[$field] = $fieldOptions;
                    }
                    $updateTables[] = array(
                        'table' => $updateTable,
                        'schema' => $updateSchema
                    );
                }
            }
        }
        // reserve if required
        if ($options['reserve']) {
            DB::reserveTables('Model_saveBatch', $tablesToReserve, (array)$options['reserve']);
        }
        // create possible update temporary tables. Do it out of transaction as 
        // DB::createTable() and DB::dropTables() autocommits transaction
        $deleteUpdateTables = function($recreate = false) use ($updateTables) {
            foreach ($updateTables as $updateTable) {
                // drop the tables (instead of truncating them) to keep always the actual schema
                try {
                    DB::dropTables($updateTable['table']);
                } 
                catch (Throwable $e) {
                    // ignore exception if table does not exist
                }
                if ($recreate) {
                    DB::createTable($updateTable['table'], $updateTable['schema']);
                }
            }
        };
        $deleteUpdateTables(true);
        // save batch
        $recoverableDeadlocksCount = 0;
        do {
            DB::startTransaction('Model_saveBatch');
            try {
                foreach ($batch as $batchAction => $models) {
                    foreach ($models as $modelName => $actionData) {
                        if (empty($actionData)) {
                            continue;
                        }
                        // load model
                        $model = explode('.', $modelName);
                        if (count($model) == 1) {
                            $Model = $this->loadModel($model[0], true);
                        }
                        elseif (count($model) == 2) {
                            $Model = App::loadModel($model[0], $model[1], true);
                        }
                        else {
                            throw new Exception(__e(__FILE__, 'Invalid model name %s', $modelName));
                        }
                        // process the batch actions
                        // - delete - $actionData are actually conditions to delete data in DB
                        if ($batchAction === 'delete') {
                            DB::delete($Model->getPropertyTable(), array(
                                'conditions' => $actionData, 
                                'reserve' => false
                            ));
                        }
                        // - create - $actionData are actually records to be inseted into DB
                        elseif ($batchAction === 'create') {
                            $allowFields = $Model->getFields();
                            // add also translation fields to allowed fields
                            foreach ($Model->getTranslatedFields() as $translatedField) {
                                foreach (App::getPropertyLangs() as $lang) {
                                    if ($lang !== DEFAULT_LANG) {
                                        $allowFields[] = $Model->getTranslationField($translatedField, $lang);
                                    }
                                }
                            }
                            DB::insert($Model->getPropertyTable(), $actionData, array(
                                'allowFields' => $allowFields,
                                'multiple' => true, 
                                'reserve' => false,
                                'ignore' => $options['ignore'],
                            ));  
                        }
                        // - update - $actionData are actually records to be updated in DB
                        elseif ($batchAction === 'update') {
                            $table = $Model->getPropertyTable();
                            $updateTable = '_' . $table . '_batch_update_tmp';
                            $primaryKeyField = $Model->getPropertyPrimaryKey();
                            DB::insert($updateTable, $actionData, array(
                                'allowFields' => $Model->getFields(),
                                'multiple' => true, 
                                'reserve' => false,
                                'ignore' => $options['ignore'],
                            ));   
                            // create update query
                            $updateQuery = "UPDATE `{$updateTable}` AS `new`"
                                . " LEFT JOIN `{$table}` AS `old`"
                                . " ON (`old`.`{$primaryKeyField}` = `new`.`{$primaryKeyField}`)"
                                . " SET ";
                            $record = reset($actionData);
                            foreach ($record as $field => $v) {
                                if ($field == $primaryKeyField) {
                                    continue;
                                }
                                $qualifiedField = $modelName . '.' . $field;
                                $hasIgnoreValue = (
                                    !empty($options['ignoreNewValueIf'])
                                    && array_key_exists(
                                        $qualifiedField, 
                                        $options['ignoreNewValueIf']
                                    )
                                );
                                $hasUpdateValue = (
                                    !empty($options['updateOldValueIf'])
                                    && array_key_exists(
                                        $qualifiedField, 
                                        $options['updateOldValueIf']
                                    )
                                );
                                $newField = "`new`.`{$field}`";
                                // normalize new empty values (see Model::normalizeEmptyValues())
                                // regardless to ignored values (maybe NULL is ignored
                                // and we are normalizing it here to '' - nevermind, make it simple and universal)
                                if ($Model->isNullableField($field)) {
                                    // use CAST() to avoid 0 == '' (any string is autocasted to 0 when compared to integer)
                                    //rblb//$newField = "IF(CAST({$newField} AS CHAR) = '', NULL, {$newField})";
                                    //rblb// casting is no more necessary as all new values are stored as strings (varchar)
                                    //rblb// See here above the comment "// use VARCHAR"
                                    $newField = "IF({$newField} = '', NULL, {$newField})";
                                }
                                else {
                                    //rblb//$newField = "IF({$newField} IS NULL, '', {$newField})";
                                    $newField = "COALESCE({$newField}, '')";
                                }
                                if ($hasIgnoreValue || $hasUpdateValue) {
                                    $conditions = array();
                                    if ($hasIgnoreValue) {
                                        // if assoc. array the take it as explicit conditions
                                        if (Validate::assocArray($options['ignoreNewValueIf'][$qualifiedField])) {
                                            $conditions[] = $options['ignoreNewValueIf'][$qualifiedField];
                                        }
                                        // otherwise take it as ignored value
                                        else {
                                            $conditions['new.' . $field] = $options['ignoreNewValueIf'][$qualifiedField];
                                        }
                                    }
                                    if ($hasUpdateValue) {
                                        if ($hasIgnoreValue) {
                                            $conditions[] = 'OR';
                                        }
                                        // if assoc. array the take it as explicit conditions
                                        if (Validate::assocArray($options['updateOldValueIf'][$qualifiedField])) {
                                            $conditions['NOT'] = $options['updateOldValueIf'][$qualifiedField];
                                        }
                                        // otherwise take it as updated value
                                        else {
                                            $conditions['old.' . $field . ' !='] = $options['updateOldValueIf'][$qualifiedField];
                                        }

                                    }
                                    $conditions = DB::getQueryConditions($conditions);
                                    $updateQuery .= "`old`.`{$field}` = IF({$conditions}, `old`.`{$field}`, {$newField}),";
                                }
                                else {
                                    $updateQuery .= "`old`.`{$field}` = {$newField},";
                                }
                            }
                            $updateQuery = rtrim($updateQuery, ',');
                            $updateQuery .= " WHERE `old`.`{$primaryKeyField}` IS NOT NULL;";
                            if ($options['debugUpdateQuery']) {
                                App::debug($updateQuery, $this->name . '::saveBatch() - update query of model ' . $modelName);
                            }
                            // execute update query
                            DB::query($updateQuery);
                        }
                    }
                }
                // if everything is successfully saved then reset $recoverableDeadlocksCount
                // to 0 to stop while() loop. Do it for the case that this is not the first attempt 
                // to save the batch but a successfull attempt after recoverable deadlock
                $recoverableDeadlocksCount = 0;
            }
            catch (Exception_DB_RecoverableDeadlock $e) {
                $recoverableDeadlocksCount++;
                DB::rollbackTransaction('Model_saveBatch');
                //App::log('ExportsImportsDebug', $e->getMessage());
                if ($recoverableDeadlocksCount > $options['recoverableDeadlocksRetries']) {
                    if ($options['reserve']) {
                        DB::unreserveTables('Model_saveBatch');
                    }
                    //App::log('ExportsImportsDebug', 'Last Exception_DB_RecoverableDeadlock');
                    if ($options['deleteUpdateTables']) {
                        $deleteUpdateTables();
                    }
                    throw $e;                    
                }
            }
            catch (Throwable $e) {
                DB::rollbackTransaction('Model_saveBatch');
                if ($options['reserve']) {
                    DB::unreserveTables('Model_saveBatch');
                }
                if ($options['deleteUpdateTables']) {
                    $deleteUpdateTables();
                }
                throw $e;
            }
        } while($recoverableDeadlocksCount);
        DB::commitTransaction('Model_saveBatch');
        if ($options['deleteUpdateTables']) {
            $deleteUpdateTables();
        }
        if ($options['reserve']) {
            DB::unreserveTables('Model_saveBatch');
        }
        
        return true;
    }   
        
    /**
     * Updates all records according to provided conditions.
     * Translations are done automatically for all fields of primary model.
     * The best practise is to provide only unqualified data of primary model.
     * In such a case normalization and validation apply easy (as they are mostly defined
     * for unqualified fields). If needed (in some special cases), then you can join 
     * other model. But be aware of following:
     *      - all fields must be qualified (even in $data - use Arr::deflate() for nested data from form)
     *      - 'modified' of all updated  models must be set manually. To do so: 
     *          set 'updateModificationDatetime' to FALSE, 
     *          set qualified name of 'XYZ.modified' to 'NOW()',
     *          add these fields to 'literals' > 'fields' or 'NOW' to 'literals' > 'data'.
     *      - normalization and validation must be done for each of updated models separately
     *          before update() is launched. It is because qualified fields are (normally) 
     *          not recognized by normalization and validations rules,
     *       
     * E.g. in case of MyModel01 and MyModel02:
     * 
     *      // MyModel01.php ($this is MyModel01)
     *      // normalize and validate MyModel01 data
     *      $data['MyModel01'] = $this->normalize($data['MyModel01']);
     *      $myModel01IsValid = $this->validate($data['MyModel01']);
     *      // normalize and validate MyModel02 data
     *      $data['MyModel02'] = $MyModel02->normalize($data['MyModel02']);
     *      if (!($myModel02IsValid = $MyModel02->validate($data['MyModel02']))) {
     *          $this->setErrors($MyModel02);
     *      }
     *      // is total validation ok?
     *      if (!$myModel01IsValid || !$myModel02IsValid) {
     *          return false;
     *      }
     *      $this->update(
     *          // $data
     *          array(
     *              // in case of joined models qualify all fields (both non-translated and translated)
     *              // of course you can treat this by Arr::deflate($data)
     *              'MyModel01.active' => $data['MyModel01']['active'],
     *              'MyModel02.active' => $data['MyModel02']['active'],
     *              'MyModel01.name' => $data['MyModel01']['name'],
     *              'MyModel02.name' => $data['MyModel02']['name'],
     *              // field 'modified' is almost in all models so it must be qualified,
     *              // set manually for all updated models (not necessarily all joined,
     *              // some models can be joined only for conditions) and added to 'literals'
     *              'MyModel01.modified' => 'NOW()', // alternatively date('Y-m-d H:i:s') and no literals
     *              'MyModel02.modified' => 'NOW()', // alternatively date('Y-m-d H:i:s') and no literals
     *          ),
     *          // $options
     *          array(
     *              'joins' => array(
     *                  array(
     *                      'type' => 'left',
     *                      'model' => 'MyModel02',
     *                  ),
     *              ),
     *              // options 'conditions', 'limit', 'offset', 'order' and 'moduleAlias' of Model::find()
     *              // can be used here
     *              'conditions' => array(
     *                  'MyModel01.id' => '...',
     *              ),
     *              'literals' => array(
     *                  // this is prefered over 'data' as it is more specific
     *                  'fields' => array(
     *                      'MyModel01.modified',
     *                      'MyModel02.modified',
     *                  ),
     *                  // or you can specifiy it by value:
     *                  //'data' => array(
     *                  //    'NOW()', // literal for value of 'MyModel01.modified' and 'MyModel02.modified'
     *                  //),
     *              ),
     *              // modified field is updated explicitly here above so do not use 
     *              // automatic update which would end up with field name conflict
     *              'updateModificationDatetime' => false,
     *          )
     *      );
     * 
     * ATTENTION: If Exception_DB_RecoverableDeadlock is thrown then the easiest way
     * to resolve the deadlock is to rollback and repeat the whole transaction again.
     * 
     * @param array $data
     * @param array $options Options 'conditions', 'limit', 'offset', 'order' , 'joins'
     *          and 'moduleAlias' of Model::find() plus following:
     *      - 'allowFields' (string|array) Single field or list of fields which 
     *          are validated and updated. Default to empty, means all data fields are
     *          validated and all those which are in DB table are updated.
     *      - 'avoidFields' (string|array) Single field or list of fields which 
     *          are not validated neither updated. Default to empty, means all data fields are
     *          validated and all those which are in DB table are updated.
     *      - 'normalize' (bool) If TRUE then $data are normalized before they are 
     *          validated (if 'validate' is TRUE) and saved. Model::normalize() is used for this purpose.
     *          Defaults to TRUE.
     *      - 'validate' (bool) If TRUE then $data are validated before they are saved.
     *          Defaults to TRUE.
     *      - 'allowValidations' (array) List of allowed validations. For sake of
     *          this options the validations must be named, e.g. array('fieldName' => array(
     *          'validationName01', 'validationName02', ...)). Defauts to NULL.
     *      - 'avoidValidations' (array) List of validations to avoid. For sake of
     *          this options the validations must be named, e.g. array('fieldName' => array(
     *          'validationName01', 'validationName02', ...)). Use array('fieldName' => TRUE) 
     *          to avoid all for given field. Defaults to NULL.
     *      - 'alternative' (string|array) Single validation altrnative name or an array
     *          of such names. If empty then only general validations are executed.
     *          Defaults to NULL.
     *      - 'resetErrors' (bool) If TRUE then model validation errors are reset 
     *          before validation. Defaults to TRUE.
     *      - 'translate' (bool) If TRUE then translation is made if actual model 
     *          has translated fields. If FALSE then translation is turned off. Defaults to TRUE.
     *      - 'lang' (string) Explicit lang to be used for translations. Applies
     *          only if 'translate' option is TRUE. Defaults to NULL, means lang
     *          is searched in $data['lang'] and if not found then set to App::$lang.
     *      - 'processFiles' (bool) If TRUE then file fields contained in data are
     *          processed. It means that incoming and obsolete files are prepared
     *          and after successful save they are cleaned up. If FALSE all 
     *          this processing is skipped and its up to user to do it. Defaults to TRUE.
     *      - 'cleanUpFiles' (bool) If FALSE then the clean up part of files processing 
     *          is skipped. This can be usefull in case of DB transactions when the 
     *          files should be cleaned up only after definitive result of transaction. 
     *          Defaults to TRUE.
     *      - 'literals' (array) Associative array of literals used in other options.
     *          It can contain following keys: 'fields', 'data', 'conditions', 'order','joins'.
     *          Each of items is either TRUE to process whole definition of that item as literals 
     *          or string to provide single literal or a plain array of such strings, 
     *          e.g.: array('fields' => array('MyModel01.modified', 'MyModel02.modified)) or
     *          array('data' => array('NOW()', 'CONCAT(`first_name`, " ", `last_name`)')).
     *          In 'fields' literals there can be specified data fields names. In 'data' 
     *          literals there can be specified data values. It is prefered to use 'fields'
     *          literals over 'data' literals as 'fields' are more specific. Defaults to NULL.
     *      - 'reserve' (bool|array) If TRUE then the query is executed only if $table 
     *          reservation succeed - means if the table is succesfully or has been already 
     *          reserved by actual process. If array then passed as options to DB::reserveTables().
     *          Defaults to TRUE.
     *      - 'ignoreSoftDeleted' (bool) If TRUE and Model::$allowsSoftDelete is TRUE
     *          then soft deleted records are excluded from update. Defaults to TRUE.
     *      - 'updateModificationDatetime' (bool) If TRUE then modification datetime
     *          (value of field 'modified') is updated. Defaults to TRUE.
     * 
     * @return bool TRUE if updated. FALSE if validation fails. 
     * 
     * @throws Exception_DB_RecoverableDeadlock
     * @throws Exception_DB_TablesReservationFailure
     */
    public function update($data, $options = array()) {
        // check for model table
        if (empty($this->table)) {
            throw new Exception(
                "This method cannot be used as the model doesn't use any database table. " 
                . "Define your own version of this method or use this model only for data validations."
            );
        }
        $defaults = array(
            'moduleAlias' => false,
            'joins' => array(),
            'conditions' => null,
            'order' => null,
            'limit' => null,
            'offset' => null,
            'allowFields' => null,
            'avoidFields' => null,
            'normalize' => true,
            'validate' => true,
            'allowValidations' => null, // '{fieldName}' => array('{validationName01}', '{validationName02}', ...)
            'avoidValidations' => null, // '{fieldName}' => array('{validationName01}', '{validationName02}', ...)
            'alternative' => null, // '{alternative}' or array('{alternative01}', '{alternative02}', ...)
            'lang' => null,
            'translate' => true,
            'processFiles' => true,
            'cleanUpFiles' => true,
            'ignoreSoftDeleted' => true,
            'updateModificationDatetime' => true,
        );
        $options = array_merge($defaults, $options);
        // if no conditions then throw exception
        if (empty($options['conditions'])) {
            throw new Exception('Missing conditions');
        }
        // translations use explicit 'query' option which has higher precedence 
        // than other options, see DB::select/update/delete(), so remove this option
        // on each new call of this method. The 'array' option is related to 'query'
        // and also added by translations functionality
        unset($options['query']);
        unset($options['array']);
        // sanitize data fields
        $data = Sanitize::arrayKeys($data, array(
            'allowKeys' => $options['allowFields'],
            'avoidKeys' => $options['avoidFields'],
        ));
        $options['create'] = false; // to make Model::isCreation() resolve correctly
        $options['on'] = 'update';
        // set the table alias and on
        $options['alias'] = $this->getAlias($options);
        // remove primary key from data if set, because this cannot be updated
        if (array_key_exists($this->primaryKey, $data)) {
            unset($data[$this->primaryKey]);
        }
        // normalize conditions
        $options['conditions'] = $this->normalizeConditions($options['conditions'], $options);
        // normalize joins
        if ($options['joins']) {
            $options['joins'] = $this->normalizeJoins($options['joins'], $options);
        }
        // normalize
        if ($options['normalize']) {
            $data = $this->normalize($data, $options);
            // if once normalized, turn it off for possible validation
            $options['normalize'] = false;
        }
        // validate
        if (
            $options['validate'] 
            && !$this->validate($data, $options)
        ) {
            return false;
        }
        // after validation constrain empty allowFields only to table fields
        $tableFields = $this->getFields();
        if (empty($options['allowFields'])) {
            $options['allowFields'] = array_merge(
                $tableFields, 
                $this->getFields(
                    array(
                        'qualify' => true,
                        'alias' => $options['alias'],
                        'moduleAlias' => false, // $options['alias'] is already moduleAlias-ed
                    ), 
                    $options['joins']
                )
            );
        }
        // add modified if relevant
        if (
            $options['updateModificationDatetime']
            && in_array('modified', $tableFields)
        ) { //@todo - check if field type is DATE or DATETIME
            $data = array_merge(array('modified' => null), $data);
            if (!in_array('modified', $options['allowFields'])) {
                $options['allowFields'][] = 'modified';
            }
        }
        // check for translations
        if (
            $options['translate'] 
            // translate only if there are some translated fields defined
            && $this->getTranslatedFields(array(), $options['joins'])
            // translate only if non-default lang is updated
            && ($lang = $this->getTranslationLang($options, $data)) != $this->translationLangs['default']
        ) {
            $translationJoins = $this->getTranslationJoins($lang, array(
                'alias' => $options['alias'],
                'moduleAlias' => $options['moduleAlias'],
            ));
            $options['joins'] = array_merge($options['joins'], $translationJoins);
            // translate unqualified primary model data
            $translationData = $this->getTranslationData($data, $options, $lang);
            $data = array_merge($data, $translationData);
            // translate qualified fields in query
            // - force array return type
            $options['array'] = true;
            // - get the translation update query
            $query = DB::getUpdateQuery($this->table, $data, $options);
            $options['query'] = $this->getTranslationQuery(
                $query, 
                $lang, 
                array(
                    'alias' => $options['alias'],
                    'moduleAlias' => $options['moduleAlias'],
                ),
                $options['joins']
            );
        }
        // check for files
        if (
            $options['processFiles'] 
            && $this->fileFields
        ) {
            // turn off translations 
            $options['translate'] = false;
            $data = $this->prepareFiles($data, $options);
        }
        // update
        try {
            $result = DB::update($this->table, $data, $options);
        }
        catch (Throwable $e) {
            // clean up files on failure
            if (
                $options['processFiles'] 
                && $options['cleanUpFiles'] 
                && $this->fileFields
            ) {
                $this->cleanUpFiles(false);
            }
            throw $e;
        }
        // clean up files on success
        if (
            $options['processFiles'] 
            && $options['cleanUpFiles'] 
            && $this->fileFields
        ) {
            $this->cleanUpFiles(true);
        }
        return $result;
    }
    
    /**
     * Updates all records where specified condition field equals to given condition value.
     * Only one model can be updated. Translations are done automatically.
     * 
     * ATTENTION: If Exception_DB_RecoverableDeadlock is thrown then the easiest way
     * to resolve the deadlock is to rollback and repeat the whole transaction again.
     * 
     * @param string $conditionField Field name to be used for update condition
     * @param mixed $conditionValue Field value to be used for update condition
     * @param array $data Update data
     * @param array $options See Model::update() options. The 'conditions' option
     *      is set explicitly by this method.
     * 
     * @return bool. TRUE if updated. FALSE if validation fails. 
     * 
     * @throws Exception_DB_RecoverableDeadlock
     * @throws Exception_DB_TablesReservationFailure
     */
    public function updateBy($conditionField, $conditionValue, $data, $options = array()) {
        // set conditions explicitly according to provided field and value
        $options['conditions'] = array($conditionField => $conditionValue);
        // update
        return $this->update($data, $options);
    }
    
    /**
     * Creates copy of provided record
     * 
     * ATTENTION: If Exception_DB_RecoverableDeadlock is thrown then the easiest way
     * to resolve the deadlock is to rollback and repeat the whole transaction again.
     * 
     * @param array $data Record data to save copy for
     * @param array $options See Model::save() options
     * 
     * @return array|bool Array of created copy data containing id of created record
     *      and other values after normalization or files copying. FALSE if validation fails.  
     * 
     * @throws Exception_DB_RecoverableDeadlock
     * @throws Exception_DB_TablesReservationFailure
     */
    public function copy($data, $options) {
        $defaults = array(
            'avoidValidations' => null,
        );
        $options = array_merge($defaults, $options);
        // disable file fields validations (files are treated differently when copied)
        $options['avoidValidations'] = (array)$options['avoidValidations'];
        foreach ($this->fileFields as $fileField => $v) {
            $options['avoidValidations'][$fileField] = true;
        }
        try {
            $data = $this->prepareCopyData($data);
            if (!($data = $this->save($data, $options))) {
                $this->cleanUpFiles(false);
                return false;
            }
        } 
        catch (Throwable $e) {
            $this->cleanUpFiles(false);
            throw $e;
        }
        $this->cleanUpFiles(true);
        return $data;
    }
    
    /**
     * Deletes all records of the model and its related models
     * 
     * Its up to Model childs to implement this method!
     * 
     * @param array $options Optional. Create any new options to parametrize the
     *      processing. To parametrize included models the 'contain' option is recommended:
     *      - 'contain' (array) List of model names which should be included in processing
     * 
     * @return bool Success 
     */
    public function deleteAll($options = array()) {
        $defaults = array(
            'contain' => array(), // if not defined then everything is deleted
        );
        $options = array_merge($defaults, $options);
        throw new Exception('Please implement this method');
    }
    
    /**
     * Deletes all records according to provided conditions.
     * 
     * ATTENTION: If Exception_DB_RecoverableDeadlock is thrown then the easiest way
     * to resolve the deadlock is to rollback and repeat the whole transaction again.
     * 
     * @param array $options Options 'conditions', 'limit', 'offset', 'order' , 'joins'
     *          and 'moduleAlias' of Model::find() plus following:
     *      - 'joins' (array) Array of joins definition (!each of joins is an array itself).
     *          Duplicities in definitions are removed. In addition to standard join
     *          options ('type', 'model', ...) there can be specified also boolean
     *          'delete' option. If it is set to TRUE then also the corresponding record(s) in 
     *          joined table are deleted. Option 'delete' defaults to FALSE, means that only
     *          records in primary table are deleted. E.g.:
     *              array(
     *                  // !!! Each join is another nested array !!!
     *                  // low level join definition (see DB::getQueryJoins() for more details)
     *                  array(
     *                      'type' => 'left',
     *                      'table' => 'my_model_table',             
     *                      'alias' => 'MyModelA',             
     *                      'delete' => true,                
     *                      'conditions' => array(
     *                          'User.id = `MyModelA`.`run_users_id`',  
     *                      ),
     *                  ),
     *                  // model level join definition (see Model::normalizeJoins() for more details)
     *                  array(
     *                      'type' => 'left'
     *                      'model' => 'MyModelB',
     *                  ),
     *                  // definition of join to another than actual model (see Model::normalizeJoins() for more details)
     *                  array(
     *                      'type' => 'left'
     *                      'model' => 'MyModelC',
     *                      'toModel' => 'MyModelA',
     *                      'delete' => true,
     *                  ),
     *                  ...
     *              ) 
     *      - 'cleanUpFiles' (bool) If FALSE then the clean up part of files processing 
     *          is skipped. This can be usefull in case of DB transactions when the 
     *          files should be cleaned up only after definitive result of transaction. 
     *          Defaults to TRUE.
     *      - 'reserve' (bool|array) If TRUE then the query is executed only if $table 
     *          reservation succeed - means if the table is succesfully or has been already 
     *          reserved by actual process. If array then passed as options to DB::reserveTables().
     *          Defaults to TRUE.
     *      - 'softDelete' (bool) If TRUE and Model::$allowsSoftDelete is TRUE then 
     *          records are not deleted but only marked as deleted by datetime value 
     *          in Model::$deletedField. Files are not cleaned in this case. Defaults to TRUE
     * 
     * @return bool TRUE on success
     * 
     * @throws Exception_DB_RecoverableDeadlock
     * @throws Exception_DB_TablesReservationFailure
     */
    public function delete($options = array()) {
        // check for model table
        if (empty($this->table)) {
            throw new Exception(
                "This method cannot be used as the model doesn't use any database table. " 
                . "Define your own version of this method or use this model only for data validations."
            );
        }
        $defaults = array(
            'moduleAlias' => false,
            'joins' => array(),
            'conditions' => null,
            'order' => null,
            'limit' => null,
            'offset' => null,
            'lang' => null,
            'cleanUpFiles' => true,
            'softDelete' => true,
        );
        $options = array_merge($defaults, $options);
        // translations use explicit 'query' option which has higher precedence 
        // than other options, see DB::select/update/delete(), so remove this option
        // on each new call of this method. The 'array' option is related to 'query'
        // and also added by translations functionality
        unset($options['query']);
        unset($options['array']);
        // if soft delete is required and alloved then just update records deletedField
        if ($this->allowsSoftDelete && !empty($options['softDelete'])) {
            $options['normalize'] = false;
            $options['validate'] = false;
            return $this->update(array($this->deletedField => 'NOW()'), $options);
        }
        // set the table alias
        $options['alias'] = $this->getAlias($options);
        // normalize joins
        if ($options['joins']) {
            $options['joins'] = $this->normalizeJoins($options['joins'], $options);
        }
        // if no conditions then throw exception
        if (empty($options['conditions'])) {
            throw new Exception('Missing conditions');
        }
        // check for translations
        if ($this->getTranslatedFields(array(), $options['joins'])) {
            // the lang is not important, joins must be created for any lang if
            // the translations are stored in separated table. If a record is deleted
            // its translations must be deleted too
            $translationJoins = $this->getTranslationJoins(
                null, 
                array(
                    'alias' => $options['alias'],
                    'moduleAlias' => $options['moduleAlias'],
                ), 
                $options['joins']
            );
            $options['joins'] = array_merge($options['joins'], $translationJoins);
////mojo> this allows translation of conditions and order clauses            
            // force array return type
            $options['array'] = true;
            // get the translation update query
            $query = DB::getDeleteQuery($this->table, $options);
            $lang = $this->getTranslationLang($options);
            $options['query'] = $this->getTranslationQuery($query, $lang, array(
                'alias' => $options['alias'],
                'moduleAlias' => $options['moduleAlias'],
            ));
////<mojo            
        }
        // check for files
        if ($this->fileFields) {
            // turn off translations
            $options['translate'] = false;
            $this->setObsoleteFiles(array_keys($this->fileFields), $options);
        }
        // delete
        try {
            $result = DB::delete($this->table, $options);
        }
        catch (Throwable $e) {
            // clean up files on failure
            if (
                $options['cleanUpFiles'] 
                && $this->fileFields
            ) {
                $this->cleanUpFiles(false);
            }
            throw $e;
        }
        // clean up files on success
        if (
            $options['cleanUpFiles'] 
            && $this->fileFields
        ) {
            $this->cleanUpFiles(true);
        }
        return $result;
    }
    
    /**
     * Deletes all records where specified condition field equals to given condition value.
     * 
     * ATTENTION: If Exception_DB_RecoverableDeadlock is thrown then the easiest way
     * to resolve the deadlock is to rollback and repeat the whole transaction again.
     * 
     * @param string $conditionField Field name to be used for delete condition
     * @param mixed $conditionValue Field value to be used for delete condition
     * @param array $options See Model::delete() options. The 'conditions' option
     *      is set explicitly by this method.
     * 
     * @return bool TRUE on success
     * 
     * @throws Exception_DB_RecoverableDeadlock
     * @throws Exception_DB_TablesReservationFailure
     */
    public function deleteBy($conditionField, $conditionValue, $options = array()) {
        // set conditions explicitly according to provided field and value
        $options['conditions'] = array($conditionField => $conditionValue);
        // delete
        return $this->delete($options);
    }
    
    /**
     * Deletes all records according to specified $ids and all related records which
     * belong to specified records are recursively deleted too.
     * 
     * ATTENTION: The hard delete is done, it means 'softDelete' option is intenally
     * forced to FALSE!
     * 
     * @param int|array $ids Single record id or an array of such ids.
     * @param array $options Following are available:
     *      - 'nullifyModels' (string|array) Single qualified model name or an array
     *          of such model names whose related records are not deleted, but just the 
     *          relation is nullified (it means the foreign key is set to NULL).
     *          E.g. array('Eshop.EshopOrder'). Defaults to NULL.
     *      - 'avoidModels' (string|array) Single qualified model name or an array
     *          of such model names whose related records are not deleted. Defaults to NULL.
     * @throws Throwable
     */
    public function deleteRecursively($ids, $options = array()) {
        $options = array_merge(array(
            'nullifyModels' => null,
            'avoidModels' => null,
        ), $options);
        $options['nullifyModels'] = (array)$options['nullifyModels'];
        $options['avoidModels'] = (array)$options['avoidModels'];
        if (!$ids) {
            throw new Exception('Missing ids');
        }
        $transactionName = $this->module . '.' . $this->name . '::deleteRecursively()';
        try {
            DB::startTransaction($transactionName);
            $this->deleteBy($this->primaryKey, $ids, array(
                'softDelete' => false,
            ));
            $foreignKey = $this->getForeignKey();
            $models = App::getModels();
            foreach ($models as $module => $moduleModels) {
                if (!App::hasModule($module, array('initialized' => true))) {
                    continue;
                }
                foreach ($moduleModels as $model) {
                    $qualifiedModel = $module . '.' . $model;
                    // skip this model
                    if (
                        $module === $this->module
                        && $model === $this->name
                    ) {
                        continue;
                    }
                    // skip models listed in option 'avoidModels'
                    if (
                        in_array($qualifiedModel, $options['avoidModels'], true)
                    ) {
                        continue;
                    }
                    // maybe some model need some params in __construct()
                    // catch exceptions and skip models where it is not possible 
                    // to get instance
                    try {
                        App::loadModel($module, $model);
                        $Class = new ReflectionClass($model);
                        if ($Class->isAbstract()) {
                            continue;
                        }
                        $Model = new $model;
                    } 
                    catch (Throwable $e) {
                        continue;
                    }
                    // skip classes which are not derived from Model class
                    if (!$Model instanceof Model) {
                        continue;
                    }
                    // skip models without defined table and scheme
                    if (
                        !$Model->getPropertyTable()
                        || !($modelSchema = $Model->getPropertySchema())
                    ) {
                        continue;
                    }
                    // skip models which do not belong to User model
                    if (empty($modelSchema[$foreignKey])) {
                        continue;
                    }
                    // skip models which has no related records
                    try {
                        if (!($modelIds = $Model->findList(array(
                            'fields' => $Model->getPropertyPrimaryKey(),
                            'conditions' => array(
                                $foreignKey => $ids,
                            ),
                            'ignoreSoftDeleted' => false,
                        )))) {
                            continue;
                        }
                    }
                    catch (Exception $e) {
                        // skip model whose tables are not created in DB
                        if (preg_match("/^Table '[^']+' doesn't exist/", $e->getMessage())) {
                            continue;
                        }
                        throw $e;
                    }
                    if (
                        in_array($qualifiedModel, $options['nullifyModels'], true) 
                    ) {
                        $Model->update(
                            array(
                                $foreignKey => null
                            ),
                            array(
                                'validate' => false,
                                'conditions' => array(
                                    $Model->getPropertyPrimaryKey() => $modelIds,
                                ),
                                'ignoreSoftDeleted' => false,
                            )
                        );
                    }
                    else {
                        $modelOptions = $options;
                        $modelOptions['avoidModels'][] = $this->module . '.' . $this->name;
                        $Model->deleteRecursively($modelIds, $modelOptions);
                    }
                }
            }
            DB::commitTransaction($transactionName);
        } 
        catch (Throwable $e) {
            DB::rollbackTransaction($transactionName);
            throw $e;
        }
    }
    
    /**
     * Returns list of enum field values like:
     * 
     *      array(
     *          'enum_pending_request' => 'Pending request',
     *          'enum_approved_request' => 'Approved request',
     *          ...
     *      )
     * 
     * or an array of simulated records containing 'value' and 'label' fields: 
     * 
     *      array(
     *          array(
     *              'value' => 'enum_pending_request',
     *              'label' => 'Pending request',
     *          ),
     *          array(
     *              'value' => 'enum_approved_request',
     *              'label' => 'Approved request',
     *          ),
     *          ...
     *      )
     * 
     * Enum values are translated using __() function. It means that enum values 
     * should have bogus translations somewhere, e.g. __(__FILE__, 'enum_pending_request')
     * to be included in .po file extracted by poedit. The best place to include these
     * bogus translations is some not used script e.g. MyModule/config/translations.php
     * 
     * @param string $field
     * @param bool $simulateRecords If TRUE then values and their translations are stored in
     *      form of simulated records with fields 'value' and 'label'. Defaults to FALSE.
     * 
     * @return array containing enum values as key and their translations as values.
     *      If $simulateRecords is TRUE then values and their translations are stored in
     *      form of simulated records with fields 'value' and 'label'.
     */
    public function getEnumValues($field, $simulateRecords = false) {
        static $enumFields = array(); // cache
        
        // check for model table
        if (empty($this->table)) {
            throw new Exception(
                "This method cannot be used as the model doesn't use any database table. " 
                . "Define your own version of this method or use this model only for data validations."
            );
        }
        
        if (empty($enumFields[$field])) {
            $enumValues = DB::getEnumValues($this->table, $field);
            $enumFields[$field] = array('list' => array(), 'records' => array());
            foreach ($enumValues as $value) {
                $enumFields[$field]['list'][$value] = __($this, $value);
                $enumFields[$field]['records'][] = array(
                    'value' => $value,
                    'label' => __($this, $value),
                );
            }
        }
            
        if ($simulateRecords) {
            return $enumFields[$field]['records'];
        }
        else {
            return $enumFields[$field]['list'];
        }
    }
    
    /**
     * Return field default value
     * 
     * @param string $field
     * 
     * @return string|NULL Default value
     */
    public function getFieldDefaultValue($field) {
        static $defaultValueFields = array(); // cache
        
        // check for model table
        if (empty($this->table)) {
            throw new Exception(
                "This method cannot be used as the model doesn't use any database table. " 
                . "Define your own version of this method or use this model only for data validations."
            );
        }
        
        if (!array_key_exists($field, $defaultValueFields)) {
            $defaultValueFields[$field] = DB::getFieldDefaultValue($this->table, $field);
        }
        
        return $defaultValueFields[$field];
    }
        
    /**
     * Exports provided or specified records in required format
     * 
     * NOTE: If csv cells contain new line characters then set 'csvOptions' > 'forExcel'
     * to TRUE. See $options for details.
     * 
     * ATTENTION: If option 'output' is set to 'download' or 'inline' then this 
     * method set App::$debug to FALSE and turns off layout.
     * 
     * @param array|resource $records Records to export can be specified like:
     *      - plain array of records arrays
     *      - associative array of find options
     *      - resource returned by Model::find(array('resource' => true));
     * @param array $options Following are available:
     *      - 'file' (string) File name or app root relative filename path to 
     *          save export into. If only filename ('myfile.csv') is provided 
     *          then it is saved under /app/tmp/export directory ('/app/tmp/export/myfile.csv').
     *          Use leading slash to save file directly under app root, e.g. '/my-file.csv'.
     *          If extension is omitted then it is set according to 'format' option.
     *          Defaults to 'export'.
     *      - 'output' (string) Output type of generated file. Possible values are 
     *          'download' (file is saved by browser), 'inline' (file is displayed by browser)
     *          or 'file' (file is saved). If 'download' or 'inline' then filepath 
     *          is ignored and only filename is used from 'file' option. Defaults to 'download'.
     *      - 'format' (string) Possible values are 'csv', 'xml', 'xls' and 'xlsx'. Defaults to 'csv'.
     *      - 'headers' (bool|array) Applies only if 'format' is 'csv', 'xls' or 'xlsx'.
     *          If array of headers to be set in first row of exported file. If TRUE then 
     *          headers are guessed from keys of first record. Defaults to TRUE.
     *      - 'csvDelimiter' (string) Csv delimiter. Applies when option 'format' is 
     *          set to 'csv'. Defaults to ','.
     *      - 'csvEnclosure' (string) Csv enclosure. Applies when option 'format' is 
     *          set to 'csv'. Defaults to '"'.
     *      - 'csvForceEnclosure' (bool) Applies when option 'format' is 
     *          set to 'csv'. If TRUE then all fields values are enclosed by
     *          enclosure. If FALSE then only values which need enclosure are enclosed. 
     *          Defaults to FALSE.
     *      - 'csvEncoding' (string) Csv encoding. Applies when option 'format' is 
     *          set to 'csv'. Charset encoding of csv file. Defaults to 'UTF-8'.
     *      - 'csvForExcel' (bool) If TRUE then options values are set so that csv
     *          file can be opened by doubleclick in MS Excel and display correctly
     *          even in case that there are new line characters inside fields values.
     *          In fact, this is the only possibility how to open such a csv correctly in MS Excel.
     *          Following options are forced: 'csvEncoding' (CP1250), 'csvDelimiter' (';')
     *          'csvEnclosure' ('"'), 'csvForceEnclosure (TRUE). Defaults to FALSE.
     *      - 'xmlRootElement' (string) Applies only if 'format' is set to 'xml'.
     *          Name of root element containing all records items. Defaults to 'items'.
     *      - 'xmlItemElement' (string) Applies only if 'format' is set to 'xml'.
     *          Name of element containing single record item. Defaults to 'item'.
     *      - 'xmlFormatted' (bool) Applies only if 'format' is set to 'xml'.
     *          If TRUE then the returned XML is formated (each items and their 
     *          fields start on new lines). Defaults to FALSE.
     *      - 'excelView' (string|array) Applies when option 'format' is set to 'xls'
     *          or 'xlsx'. Name of view for excel worksheet rendering. If string 
     *          then the view is searched under App module. To provide views from 
     *          other modules use array like array('module' => ..., 'name' => ...)
     *          Defaults to 'Model/exportExcel'
     *      - 'excelTitle' (string) Applies when option 'format' is set to 'xls'
     *          or 'xlsx'. Worksheet title propery value. If NULL then set to 
     *          filename. Defaults to NULL.
     *      - 'renderFields' (array) Array of field renderers. Each field renderer 
     *          must be defined as callable or annonymou function. The called function 
     *          receives following arguments: fieldValue, fieldName and recordData 
     *          and must return interpreted field value. Defaults to empty array().
     * 
     * @throws Exception
     */
    public function export($records, $options = array()) {     
        $defaults = array(
            'file' => 'export',
            'output' => 'download',
            'format' => 'csv', // 'csv', 'xls'
            'headers' => true, // bool|array
            'csvEncoding' => 'UTF-8',
            'csvDelimiter' => ',',
            'csvEnclosure' => '"',
            'csvForceEnclosure' => false,
            'csvForExcel' => false,
            'xmlRootElement' => 'items',
            'xmlItemElement' => 'item',
            'xmlFormatted' => false,
            'excelView' => 'Model/exportExcel',
            'excelTitle' => null,
            'renderFields' => array(),
        );
        $options = array_merge($defaults, $options);
        $options['renderFields'] = (array)$options['renderFields'];
        $inEncoding = 'UTF-8';
        
        // normalize and validate records
        if (!DB::isResource($records)) {
            $records = (array)$records;
            if (Validate::assocArray($records)) {
                $records['resource'] = true;
                if (empty($records['fields'])) {
                    $records['fields'] = '*';
                }
                $records['resource'] = true;
                $records = $this->find($records);
            }
            elseif (
                !empty($records) 
                && !is_array(reset($records))
            ) {
                throw new Exception(__e(__FILE__, 'Invalid records. Records must be an array of arrays'));
            }
        }
        // normalize and validate format
        $options['format'] = strtolower($options['format']);
        if (!in_array($options['format'], array('csv', 'xml', 'xls', 'xlsx'))) {
            throw new Exception(__e(__FILE__, 'Invalid format %s', $options['format']));
        }
        // normalize output
        $options['output'] = strtolower($options['output']);
        // normalize headers
        if ($options['format'] === 'xml') {
            $options['xmlRootElement'] = strtolower($options['xmlRootElement']);
            if (empty($options['xmlRootElement'])) {
                throw new Exception(__e(__FILE__, 'Option "xmlRootElement" cannot be empty'));
            }
            $options['xmlItemElement'] = strtolower($options['xmlItemElement']);
            if (empty($options['xmlItemElement'])) {
                throw new Exception(__e(__FILE__, 'Option "xmlItemElement" cannot be empty'));
            }
        }
        elseif ($options['headers']) {
            if (!is_array($options['headers'])) {
                $options['headers'] = array();
                if (DB::isResource($records)) {
                    if (($record = DB::fetchArray($records))) {
                        $options['headers'] = array_keys($record);
                    }
                    DB::seekRow($records, 0);
                }
                elseif (!empty($records)) {
                    $options['headers'] = array_keys(reset($records));
                }
            }
        }
        // get filename
        $path = parse_url($options['file'], PHP_URL_PATH);
        $fileInfo = File::getPathinfo($path);
        $filename = 'export';
        if ($fileInfo['filename']) {
            $filename = $fileInfo['filename'];
        }
        if (empty($fileInfo['extension'])) {
            $filename = rtrim($filename, '.') . '.' . $options['format'];
        }
        // get output file
        if (
            $options['output'] === 'download'
            || $options['output'] === 'inline'
        ) {
            $outputFile = 'php://output';
        }
        else {
            if (empty($fileInfo['dirname']) || $fileInfo['dirname'] === '.') {
                $dirname = 'tmp' . DS . 'export';
            }
            else {
                $dirname = trim($fileInfo['dirname'], DS);
            }
            File::ensurePath($dirname);
            $outputFile = ROOT . DS . $dirname . DS . $filename . Str::getUnique('_tmp_');
            $finalOutputFile = ROOT . DS . $dirname . DS . $filename;
        }
        // export csv
        if (
            $options['format'] === 'csv'
            || $options['format'] === 'xml'
        ) {
            if (!($fh = fopen($outputFile, 'w'))) {
                throw new Exception(__e(__FILE__, 'Opening of %s has failed', $outputFile));
            }       
            if (
                $options['output'] === 'download'
                || $options['output'] === 'inline'
            ) {
                $buffersData = App::closeOpenedOutputBuffers();
            }
            $encode = false;
            if ($options['format'] === 'csv') {
                // normalize encoding and check if encoding is should be applied
                $options['csvEncoding'] = strtoupper($options['csvEncoding']);
                $encode = $inEncoding !== $options['csvEncoding'];
                // normalize options for excel if required
                if ($options['csvForExcel']) {
                    $options['csvEncoding'] = 'CP1250';
                    $options['csvDelimiter'] = ';';
                    $options['csvEnclosure'] = '"';
                    $options['csvForceEnclosure'] = true;
                }
                $encode = $inEncoding !== $options['csvEncoding'];
                if ($options['csvForceEnclosure']) {
                    App::loadLib('App', 'Csv');
                }
            }
            if (
                $options['output'] === 'download'
                || $options['output'] === 'inline'
            ) {
                header('Content-Description: File Transfer');
                if ($options['format'] === 'csv') {                    
                    header('Content-Type: text/csv; charset=' . strtolower($options['csvEncoding']));
                }
                else {
                    header('Content-Type: application/xml; charset=utf-8');
                }
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                header('Cache-Control: public, must-revalidate, max-age=0'); 
//                // if you're serving to IE 9, then the following may be needed
//                header('Cache-Control: max-age=1');
//                // if you're serving to IE over SSL, then the following may be needed
//                header ('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
//                header ('Last-Modified: '.gmdate('D, d M Y H:i:s').' GMT'); // always modified
//                header ('Cache-Control: cache, must-revalidate'); // HTTP/1.1
//                header ('Pragma: public'); // HTTP/1.0
            }
            
            if ($options['format'] === 'xml') {
                fwrite($fh, '<?xml version="1.0" encoding="utf-8"?>');
                if ($options['xmlFormatted']) {
                    fwrite($fh, PHP_EOL);
                }
                fwrite($fh, '<' . $options['xmlRootElement'] . '>');
            }
            elseif ($options['headers']) {
                if ($encode) {
                    foreach ($options['headers'] as &$field) {
                        $field = iconv($inEncoding, $options['csvEncoding'], $field);
                    }
                    unset($field);
                }
                if ($options['csvForceEnclosure']) {
                    $csvString = Csv::getRecordString(
                        $options['headers'],
                        $options['csvDelimiter'], 
                        $options['csvEnclosure']
                    );
                    $csvString .= PHP_EOL;
                    fwrite($fh, $csvString);
                }
                else {
                    fputcsv(
                        $fh, 
                        $options['headers'], 
                        $options['csvDelimiter'], 
                        $options['csvEnclosure']
                    );
                }
            }
            
            $getRecordXml = function($record) use (&$options) {
                $xml = '';
                if ($options['xmlFormatted']) {
                    $xml .= PHP_EOL . str_repeat(' ', 4);
                }
                $xml .= '<' . $options['xmlItemElement'] . '>';
                foreach ($record as $field => $value) {
                    if ($options['xmlFormatted']) {
                        $xml .= PHP_EOL . str_repeat(' ', 8);
                    }
                    $xml .= '<' . $field . '>';
                    if (Validate::number($value)) {
                        $xml .= $value;
                    }
                    else {
                        $xml .= '<![CDATA[' . Sanitize::invalidXmlChars($value) . ']]>';
                    }
                    $xml .= '</' . $field . '>';
                }
                if ($options['xmlFormatted']) {
                    $xml .= PHP_EOL . str_repeat(' ', 4);
                }
                $xml .= '</' . $options['xmlItemElement'] . '>';
                return $xml;
            };
            
            if (DB::isResource($records)) {
                while ($record = DB::fetchArray($records)) {
                    if ($encode || $options['renderFields']) {
                        foreach ($record as $field => &$fieldValue) {
                            if (
                                !empty($options['renderFields'][$field])
                                && Validate::callableFunction($options['renderFields'][$field])
                            ) {
                                $fieldValue = call_user_func_array(
                                    $options['renderFields'][$field], array($fieldValue, $field, $record)
                                );
                            }
                            if ($encode) {
                                $fieldValue = iconv($inEncoding, $options['csvEncoding'], $fieldValue);
                            }
                        }
                        unset($fieldValue);
                    }
                    if ($options['format'] === 'xml') {
                        fwrite($fh, $getRecordXml($record));
                    }
                    elseif ($options['csvForceEnclosure']) {
                        $csvString = Csv::getRecordString(
                            $record,
                            $options['csvDelimiter'], 
                            $options['csvEnclosure']
                        );
                        $csvString .= PHP_EOL;
                        fwrite($fh, $csvString);
                    }
                    else {
                        fputcsv(
                            $fh, 
                            $record, 
                            $options['csvDelimiter'], 
                            $options['csvEnclosure']
                        );
                    }
                }
            }
            else {
                foreach ($records as $record) {
                    if ($encode || $options['renderFields']) {
                        foreach ($record as $field => &$fieldValue) {
                            if (
                                !empty($options['renderFields'][$field])
                                && Validate::callableFunction($options['renderFields'][$field])
                            ) {
                                $fieldValue = call_user_func_array(
                                    $options['renderFields'][$field], array($fieldValue, $field, $record)
                                );
                            }
                            if ($encode) {
                                $fieldValue = iconv($inEncoding, $options['csvEncoding'], $fieldValue);
                            }
                        }
                        unset($fieldValue);
                    }
                    if ($options['format'] === 'xml') {
                        fwrite($fh, $getRecordXml($record));
                    }
                    if ($options['csvForceEnclosure']) {
                        $csvString = Csv::getRecordString(
                            $record,
                            $options['csvDelimiter'], 
                            $options['csvEnclosure']
                        );
                        $csvString .= PHP_EOL;
                        fwrite($fh, $csvString);
                    }
                    else {
                        fputcsv(
                            $fh, 
                            $record, 
                            $options['csvDelimiter'], 
                            $options['csvEnclosure']
                        );
                    }
                }
            }
            if ($options['format'] === 'xml') {
                if ($options['xmlFormatted']) {
                    fwrite($fh, PHP_EOL);
                }
                fwrite($fh, '</' . $options['xmlRootElement'] . '>');
            }
            if (
                $options['output'] === 'download'
                || $options['output'] === 'inline'
            ) {
                App::reopenClosedOutputBuffers($buffersData);
            }
            // close file handle
            fclose($fh);
        }
        // export xls
        elseif (
            $options['format'] === 'xls'
            || $options['format'] === 'xlsx'
        ) {
            // normalize excelView
            if (is_string($options['excelView'])) {
                $options['excelView'] = array('module' => 'App', 'name' => $options['excelView']);
            }
            // normalize excelTitle
            if ($options['excelTitle'] === null) {
                $options['excelTitle'] = $filename;
            }
            // error reporting
//            error_reporting(E_ALL);
//            ini_set('display_errors', TRUE);
//            ini_set('display_startup_errors', TRUE);
            date_default_timezone_set('Europe/London');

            App::loadVendor('App', 'phpexcel/Classes/PHPExcel.php');
            $PHPExcel = new PHPExcel();
            $user = App::getUser();
            
            App::loadView(
                $options['excelView']['module'],
                $options['excelView']['name'], 
                array(
                    'Model' => $this,
                    'PHPExcel' => $PHPExcel,
                    'user' => $user,
                    'title' => $options['excelTitle'],
                    'headers' => $options['headers'],
                    'records' => $records,
                    'renderFields' => $options['renderFields'],
                )
            );
            
            if ($options['format'] === 'xls') {
                $writerType = 'Excel5';
                $contentType = 'application/vnd.ms-excel';
            }
            else {
                $writerType = 'Excel2007';
                $contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';                
            }
            $PHPExcelWriter = PHPExcel_IOFactory::createWriter($PHPExcel, $writerType);
            
            if (
                $options['output'] === 'download'
                || $options['output'] === 'inline'
            ) {
                header('Content-Description: File Transfer');
                header('Content-Type: ' . $contentType);
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                header('Cache-Control: public, must-revalidate, max-age=0'); 
//                // if you're serving to IE 9, then the following may be needed
//                header('Cache-Control: max-age=1');
//                // if you're serving to IE over SSL, then the following may be needed
//                header ('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
//                header ('Last-Modified: '.gmdate('D, d M Y H:i:s').' GMT'); // always modified
//                header ('Cache-Control: cache, must-revalidate'); // HTTP/1.1
//                header ('Pragma: public'); // HTTP/1.0
            }

            $PHPExcelWriter->save($outputFile);
        }
        // turn off debug to avoid generation of js code into output in case of download
        if (
            $options['output'] === 'download'
            || $options['output'] === 'inline'
        ) {
            App::setDebug(false);
            App::setLayout(false);
        }
        elseif (!@rename($outputFile, $finalOutputFile)) {
            throw new Exception(__e(__FILE__, 'Export to %s has failed', $finalOutputFile));
        }
    }
    
    /**
     * TREE METHODS
     */
    
    /**
     * Gets the tree type implemented by current model
     * 
     * @param bool $exceptionOnNoTree Optional. If TRUE then an exception is thrown
     *       instead of returning FALSE. Defaults to FALSE.
     * 
     * @return mixed Returns:
     *      - Model::PSP_TREE_TYPE constant if model implements tree using 'path', 'sort', 'parent_id' and possibly 'level' fields. 
     *      - Model::LRP_TREE_TYPE constant if model implements tree using 'lft', 'rght' and 'parent_id' fields.
     *      - FALSE if model does not implement any tree. If $exceptionOnNoTree is TRUE
     *          then an exception is thrown instead of returning FALSE.
     * 
     * @throws Exception if the current model does not implement tree and input 
     *      $exceptionOnNoTree is TRUE. 
     */
    public function getTreeType($exceptionOnNoTree = false) {
        if (
            isset($this->schema['path'])
            && isset($this->schema['sort'])
            && isset($this->schema['parent_id'])
        ) {
            return self::PSP_TREE_TYPE;
        }
        elseif (
            isset($this->schema['lft'])
            && isset($this->schema['rght'])
            && isset($this->schema['parent_id'])
        ) {
            return self::LRP_TREE_TYPE;
        }
        elseif ($exceptionOnNoTree) {
            throw new Exception ("Table '{$this->name}' does not implemet tree. There are no 'parent_id', 'path', 'sort' or 'parent_id', 'lft', 'rght' columns");
        }
        else {
            return false;
        }
    }
    
    /**
     * Find id of record specified by options
     * 
     * @param int|string|array $options Specification of record like:
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for record id.
     *          is such a case it is just returned. 
     *      - If non integer value is provided (e.g. 'myMenu') then it is considered  for 
     *          record pid. It's up to user consider if pid is unique. If pid is not unique
     *          then following possibility can be used.
     *      - If an array then it is taken for complete find options to retrieve record.
     * 
     * @return int Record id. If no record found accoding provided pid or find options
     *      then NULL is returned. ATTENTION: If an invalid id is on input then 
     *      it is just returned - there is no check of record existece in such a case.
     */
    public function findId($options) {
        // retrieve root if provided like a string pid or an array of find options
        if (
            !is_int($options)
            && !Validate::intNumber($options)
        ) {
            $idOptions = array();
            // find options
            if (is_array($options)) {
                $idOptions = $options; 
            }
            // pid
            else {
                $idOptions['conditions'] = array(
                    $this->name . '.pid' => (string)$options
                ); 
            }
            // force folloving values in root options
            $idOptions['fields'] = array($this->name . '.' . $this->primaryKey);
            $idOptions['allowFields'] = null;
            $idOptions['avoidFields'] = null;
            $idOptions['first'] = true;
            $idOptions['resource'] = false;
            $options = $this->find($idOptions);
            if (!$options) {
                // unexisting record so return NULL
                return null;
            }
            if (!empty($options[$this->primaryKey])) {
                $options = $options[$this->primaryKey];
            }
            // treat cases then the fields are qualified and inflated
            elseif (!empty($options[$this->name . '.' . $this->primaryKey])) {
                $options = $options[$this->name . '.' . $this->primaryKey];
            }
            else {
                $options = $options[$this->name][$this->primaryKey];
            }
        }
        return $options;
    }
    
    /**
     * Returns conditions array to find/update/delete items in tree under specified root
     * 
     * @param int|string|array $root Specification of root node of required (sub)tree.
     *      This is not root of whole tree, but parent node of (sub)tree we are looking for. 
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for root node id. 
     *      - If non integer value is provided (e.g. 'myMenu') then it is considered  for 
     *      root node pid. It's up to user consider if pid is unique. If pid is not unique
     *      then following possibility can be used.
     *      - If an array then it is taken for complete find options to retrieve root.
     * @param array $options Following options are available:
     *      - 'depth' (int) Depth of subnodes to find/update. Defaults to NULL, means all 
     *          sublevels are considered.
     *      - 'includeRoot' (bool) If TRUE then also root is included in conditions.
     *          Defaults to FALSE.
     * @param int& $rootId Optional output. Passed by reference. Returns the provided root id.
     *      If no root is found according to provided $root specification then $rootId
     *      is set to NULL. ATTENTION: If $root is an integer number (e.g. 2 or '2'),
     *      then it is just copied to this output without checking its existence!
     * 
     * @return array|boolean Conditions array which is already properly nested (necessary 
     *      in case that 'includeRoot' is TRUE) so you do not need to use DB::nestConditions().
     *      FALSE in case that root specified by pid or find options does not exist.
     */
    public function getTreeConditions($root, $options = array(), &$rootId = null) {
        $defaults = array(
            // only options used directly in this method have default values here
            'depth' => null,
            'includeRoot' => false,
        );
        $options = array_merge($defaults, $options);
        if (empty($root)) {
            return false;
        }
        $root = $this->findId($root);
        $rootId = $root;
        if (empty($root)) {
            return false;
        }
        // check for the type of tree (throw exception if not tree)
        $treeType = $this->getTreeType(true);
        // PWP tree
        if ($treeType == self::PSP_TREE_TYPE) {
            if ($options['depth'] && Validate::intNumber($options['depth'])) {
                $levelsUnderRoot = $options['depth'] - 1;
                if ($levelsUnderRoot > 0) {
                    $treeConditions = array(
                        $this->name . '.path REGEXP \'-' . $root . '-([0-9]+\-){0,' . $levelsUnderRoot . '}$\'',
                    );
                }
                else {
                    $treeConditions = array(
                        $this->name . '.parent_id' => $root,
                    );
                }
            }
            else {
                $treeConditions = array($this->name . '.path LIKE' => '%-' . $root . '-%');
            }
        }
        // LRP tree
        else {
            // tree consructed by 'parent_id', 'lft' and 'rght'
            // To find all iems of tree:
            //  a] Find lft and rght of root
            //  b] Find all items in table WHERE item.lft > root.lft AND item.rght < root.rght ORDER BY root.lft ASC.
            //     SELECT fields must contain at least primary key and 'parent_id'.
            //  c] The rest should be the same as for version 'parent_id' and 'path' and is shared here below...
        }
        if ($options['includeRoot']) {
            $treeConditions[] = 'OR';
            $treeConditions[$this->name . '.' . $this->primaryKey] = $root;
            $treeConditions = array($treeConditions);
        }
        return $treeConditions;
    }
          
    /**
     * Retrieves all items of tree according to given conditions. Unlike to findTree()
     * method the retrieved items are not organized in tree structure, but it's just
     * a plain list of items same as returned by find():
     * 
     * @param int|string|array $root Specification of root node of required (sub)tree.
     *      This is not root of whole tree, but parent node of (sub)tree we are looking for. 
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for root node id. 
     *      - If non integer value is provided (e.g. 'myMenu') then it is considered  for 
     *      root node pid. It's up to user consider if pid is unique. If pid is not unique
     *      then following possibility can be used.
     *      - If an array then it is taken for complete find options to retrieve root.
     * @param array $options See Model::find() and Model::getTreeConditions() options. 
     *      The 'conditions' and 'fields' options are extended by this method.
     * @param int& $rootId Optional output. Passed by reference. Returns the provided root id.
     *      If no root is found according to provided $root specification then $rootId
     *      is set to NULL. ATTENTION: If $root is an integer number (e.g. 2 or '2'),
     *      then it is just copied to this output without checking its existence!
     *      
     * 
     * @return array 
     */
    public function findInTree($root, $options = array(), &$rootId = null) {
        $defaults = array(
            // only options used directly in this method have default values here
            'fields' => null,
            'conditions' => null,
        );
        $options = array_merge($defaults, $options);
        
        // check for the type of tree (throw exception if not tree)
        $treeType = $this->getTreeType(true);
        // PWP tree
        if ($treeType == self::PSP_TREE_TYPE) {
            $treeConditions = $this->getTreeConditions($root, $options, $rootId);
            if (!$treeConditions) {
                return array();
            }
//// array_unique() with SORT_REGULAR  works only from php version 5.2.9            
//            $options['conditions'] = array_unique(
//                array_merge($treeConditions, (array)$options['conditions']), 
//                SORT_REGULAR // compare items normally (don't change types)
//            );            
            $options['conditions'] = array_merge($treeConditions, DB::nestConditions((array)$options['conditions']));    
            $treeFields = array($this->name . '.' . $this->primaryKey, $this->name . '.parent_id');
            $options['fields'] = array_unique(array_merge((array)$options['fields'], $treeFields));
            if (!empty($options['allowFields'])) {
                $options['allowFields'] = array_unique(array_merge((array)$options['allowFields'], $treeFields));
            }
            if (!empty($options['avoidFields'])) {
                $options['avoidFields'] = array_diff((array)$options['avoidFields'], $treeFields);
            }
        }
        // LRP tree
        else {
            // tree consructed by 'parent_id', 'lft' and 'rght'
            // To find all iems of tree:
            //  a] Find lft and rght of root
            //  b] Find all items in table WHERE item.lft > root.lft AND item.rght < root.rght ORDER BY root.lft ASC.
            //     SELECT fields must contain at least primary key and 'parent_id'.
            //  c] The rest should be the same as for version 'parent_id' and 'path' and is shared here below...
        }
        
        return $this->find($options);
    }
    
    /**
     * Retrieves the first item of tree according to given conditions.
     * 
     * @param int|string|array $root Specification of root node of required (sub)tree.
     *      This is not root of whole tree, but parent node of (sub)tree we are looking for. 
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for root node id. 
     *      - If non integer value is provided ('myMenu') then it is considered  for 
     *      root node pid (It's up to user consider if pid is unique. If pid is not unique
     *      then following possibility can be used).
     *      - If an array then it is taken for complete find options to retrieve root.
     * 
     * @param array $options See Model::find() and Model::getTreeConditions() options. 
     *      The 'conditions' and 'fields' options are extended by this method and 
     *      'first' is set explicitly.
     * 
     * @return array 
     */
    public function findFirstInTree($root, $options = array()) {
        $options['first'] = true;
        return $this->findInTree($root, $options);
    }
    
    /**
     * Finds nodes up in the tree starting from specified $node and corresponding 
     * to provided find $options.
     * 
     * @param int|string|array $node Specification of node to search from up in the tree:
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for node id. 
     *      - If non integer value is provided ('myMenu') then it is considered  for 
     *      node pid (It's up to user consider if pid is unique. If pid is not unique
     *      then the last possibility can be used).
     *      - If an array containing items 'id' and 'path' then it is taken for node record data.
     *      - If other array than mentioned above then it is taken for complete find options to retrieve node.
     * 
     * @param array $options Options of Model::find() plus following to make slice 
     *      from candidate nodes (not from found nodes) up in the tree:
     *      - 'start' (int) Serves to constrain candidate nodes. 
     *          If non-negative, the sequence will start at that index in the array. 
     *          If negative, the sequence will start that far from the end of the array. 
     *          Defaults to 0, means all candidate nodes starting by $node are considered.
     *      - 'length' (int) Serves to constrain candidate nodes. 
     *          If length is given and is positive, then the sequence will 
     *          have up to that many elements in it. If the array is shorter than the length, 
     *          then only the available array elements will be present. If length is given 
     *          and is negative then the sequence will stop that many elements from the end 
     *          of the array. If it is omitted, then the sequence will have everything from 
     *          offset up until the end of the array. Defaults to NULL, means all candidate
     *          nodes are considered.
     *      - 'order' (string|array) See Model::find(). This option is set implicitly
     *          to order the found nodes from nested to top level. You can change it.
     * 
     * @param array& $nodeRecord Optional output. Passed by reference. Returns the 
     *      specified $node record data containing 'id' and 'path'. Can be used 
     *      for optimization purposes, when the method is called more than once with 
     *      the same $node. In such case only the first call of method is made with 
     *      e.g. $node id and all the rest can be made directly with $node record data 
     *      which is optimal. NOTE: If the $node is provided as record data then 
     *      it is just copied to $nodeRecord.
     * 
     * @return array Array of found nodes up in the tree. By default the nodes are
     *      ordered from nested to top levels and the $node itself can be contained
     *      if it fullfils the provided find $options.
     */
    public function findUpInTree($node, $options = array(), &$nodeRecord = null) {
        $defaults = array(
            'conditions' => array(),
            'order' => $this->name . '.sort DESC',
            'start' => 0,
            'length' => null,
        );
        $options = array_merge($defaults, $options);
        
        if (
            !is_array($node)
            || empty($node[$this->primaryKey]) 
            || empty($node['path'])
        ) {
            // find the node path and id
            $nodeOptions = array();
            // - if node specified by id
            if (
                is_int($node)
                || Validate::intNumber($node)
            ) {
                $nodeOptions['conditions'] = array(
                    $this->name . '.' . $this->primaryKey => $node 
                ); 
            }
            // - if node specified by find options
            elseif (is_array($node)) {
                $nodeOptions = $node; 
            }
            // - if node specified by pid
            else {
                $nodeOptions['conditions'] = array(
                    $this->name . '.pid' => (string)$node 
                ); 
            }
            // - force folloving values in root options
            $nodeOptions['fields'] = array(
                $this->name . '.' . $this->primaryKey,
                $this->name . '.path',
            );
            $nodeOptions['first'] = true;
            $nodeOptions['resource'] = false;
            $node = $this->find($nodeOptions);
            if (!$node) {
                // unexisting node so return array()
                return array();
            }
        }
        // get node id
        $id = null;
        if (!empty($node[$this->primaryKey])) {
            $id = $node[$this->primaryKey];
        }
        // - treat cases then the fields are qualified and inflated
        elseif (!empty($node[$this->name . '.' . $this->primaryKey])) {
            $id = $node[$this->name . '.' . $this->primaryKey];
        }
        elseif (!empty($node[$this->name][$this->primaryKey])) {
            $id = $node[$this->name][$this->primaryKey];
        }
        // get node path
        $path = null;
        if (!empty($node['path'])) {
            $path = $node['path'];
        }
        // - treat cases then the fields are qualified and inflated
        elseif (!empty($node[$this->name . '.path'])) {
            $path = $node[$this->name . '.path'];
        }
        elseif (!empty($node[$this->name]['path'])) {
            $path = $node[$this->name]['path'];
        }
        // set the aux output
        $nodeRecord = array();
        if ($id) {
            $nodeRecord [$this->primaryKey] = $id;
        }
        if ($path) {
            $nodeRecord ['path'] = $path;
        }
        
        // find up the tree
        // - get parent ids
        $ids = Model::getParentIdsFromTreePath($path);
        $ids[] = $id;
        $ids = array_reverse($ids);
        $ids = array_slice($ids, $options['start'], $options['length']);
        // - if there are no ids to search then return array()
        if (empty($ids)) {
            return array();
        }
        // - nest actual conditions
        $options['conditions'] = DB::nestConditions((array)$options['conditions']);
        // - add "up tree" condition (as first one - to optimize)
        $options['conditions'] = array_merge(
            array(
                $this->name . '.' . $this->primaryKey => $ids,
            ), 
            $options['conditions']
        );
        // - find
        return $this->find($options);
    }
    
    /**
     * Finds first node up in the tree starting from specified $node and corresponding 
     * to provided find $options.
     * 
     * @param int|string|array $node Specification of node to search from up in the tree:
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for node id. 
     *      - If non integer value is provided ('myMenu') then it is considered  for 
     *      node pid (It's up to user consider if pid is unique. If pid is not unique
     *      then the last possibility can be used).
     *      - If an array containing items 'id' and 'path' then it is taken for node record data.
     *      - If other array than mentioned above then it is taken for complete find options to retrieve node.
     * 
     * @param array $options Options of Model::find() plus following to make slice 
     *      from candidate nodes (not from found nodes) up in the tree:
     *      - 'start' (int) Serves to constrain candidate nodes. 
     *          If non-negative, the sequence will start at that index in the array. 
     *          If negative, the sequence will start that far from the end of the array. 
     *          Defaults to 0, means all candidate nodes starting by $node are considered.
     *      - 'length' (int) Serves to constrain candidate nodes. 
     *          If length is given and is positive, then the sequence will 
     *          have up to that many elements in it. If the array is shorter than the length, 
     *          then only the available array elements will be present. If length is given 
     *          and is negative then the sequence will stop that many elements from the end 
     *          of the array. If it is omitted, then the sequence will have everything from 
     *          offset up until the end of the array. Defaults to NULL, means all candidate
     *          nodes are considered.
     *      - 'order' (string|array) See Model::find(). This option is set implicitly
     *          to order the found nodes from nested to top level. You can change it.
     * 
     * @param array& $nodeRecord Optional output. Passed by reference. Returns the 
     *      specified $node record data containing 'id' and 'path'. Can be used 
     *      for optimization purposes, when the method is called more than once with 
     *      the same $node. In such case only the first call of method is made with 
     *      e.g. $node id and all the rest can be made directly with $node record data 
     *      which is optimal. NOTE: If the $node is provided as record data then 
     *      it is just copied to $nodeRecord.
     * 
     * @return array Array of firts found node up in the tree according to $options. 
     */
    public function findFirstUpInTree($node, $options = array(), &$nodeRecord = null) {
        $options['first'] = true;
        return $this->findUpInTree($node, $options, $nodeRecord);
    }
    
    /**
     * Finds the specified $node nesting level/depth in tree
     * 
     * @param int|string|array $node Specification of node to find level for in the tree:
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for node id. 
     *      - If non integer value is provided ('myMenu') then it is considered  for 
     *      node pid (It's up to user consider if pid is unique. If pid is not unique
     *      then the last possibility can be used).
     *      - If an array containing item 'path' then it is taken for node record data.
     *      - If other array than mentioned above then it is taken for complete find options to retrieve node.
     * 
     * @param array& $nodeRecord Optional output. Passed by reference. Returns the 
     *      specified $node record data containing 'id' and 'path'. Can be used 
     *      for optimization purposes, when the method is called more than once with 
     *      the same $node. In such case only the first call of method is made with 
     *      e.g. $node id and all the rest can be made directly with $node record data 
     *      which is optimal. NOTE: If the $node is provided as record data then 
     *      it is just copied to $nodeRecord.
     * 
     * @return int Node nesting level starting from 1 for root nodes. Returns 0 for
     *      unexisting $node. The same result can be obtained by count(Model::findUpInTree($node)).
     */
    public function findLevelInTree($node, &$nodeRecord = null) {
        
        if (
            !is_array($node)
            || empty($node[$this->primaryKey]) 
            || empty($node['path'])
        ) {
            // find the node path and id
            $nodeOptions = array();
            // - if node specified by id
            if (
                is_int($node)
                || Validate::intNumber($node)
            ) {
                $nodeOptions['conditions'] = array(
                    $this->name . '.' . $this->primaryKey => $node 
                ); 
            }
            // - if node specified by find options
            elseif (is_array($node)) {
                $nodeOptions = $node; 
            }
            // - if node specified by pid
            else {
                $nodeOptions['conditions'] = array(
                    $this->name . '.pid' => (string)$node 
                ); 
            }
            // - force folloving values in root options
            $nodeOptions['fields'] = array(
                $this->name . '.' . $this->primaryKey,
                $this->name . '.path',
            );
            $nodeOptions['first'] = true;
            $nodeOptions['resource'] = false;
            $node = $this->find($nodeOptions);
            if (!$node) {
                // unexisting node so return array()
                return 0;
            }
        }        
        // get node id
        $id = null;
        if (!empty($node[$this->primaryKey])) {
            $id = $node[$this->primaryKey];
        }
        // - treat cases then the fields are qualified and inflated
        elseif (!empty($node[$this->name . '.' . $this->primaryKey])) {
            $id = $node[$this->name . '.' . $this->primaryKey];
        }
        elseif (!empty($node[$this->name][$this->primaryKey])) {
            $id = $node[$this->name][$this->primaryKey];
        }
        // get node path
        $path = null;
        if (!empty($node['path'])) {
            $path = $node['path'];
        }
        // - treat cases then the fields are qualified and inflated
        elseif (!empty($node[$this->name . '.path'])) {
            $path = $node[$this->name . '.path'];
        }
        elseif (!empty($node[$this->name]['path'])) {
            $path = $node[$this->name]['path'];
        }
        // set the aux output
        $nodeRecord = array();
        if ($id) {
            $nodeRecord [$this->primaryKey] = $id;
        }
        if ($path) {
            $nodeRecord ['path'] = $path;
        }
        
        // get the level
        return count(Model::getParentIdsFromTreePath($path)) + 1;
    }
    
    /**
     * Retrieves tree in following array structure:
     * 
     *      array(
     *          '{rootId}' => array(
     *              '{id01}' => array(...),
     *              '{id02}' => array(...),
     *              '{id03}' => array(...),
     *          )
     *          '{id02}' => array(
     *              '{id04}' => array(...),
     *          )
     * 
     *      )
     * 
     * E.g.:
     * 
     *      array ( 
     *          [24] => array ( 
     *              [37] => array('name' => Fruit), 
     *              [25] => array('name' => Vegetable), 
     *          ), 
     *          [37] => array ( 
     *              [39] => array('name' => Apple), 
     *              [35] => array('name' => Banana), 
     *          ), 
     *          [25] => array ( 
     *              [68] => array('name' => Cauliflower), 
     *              [72] => array('name' => Pumpkin), 
     *          ), 
     *      ) 
     * 
     * If the specified root has no childs then:
     * 
     *      array (
     *          [24] => array(),
     *      )
     * 
     * If the specified root does not exist then emty array().
     * 
     *
     * @param int|string|array $root Specification of root node of required (sub)tree.
     *      This is not root of whole tree, but parent node of (sub)tree we are looking for. 
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for root node id. 
     *      - If non integer value is provided ('myMenu') then it is considered  for 
     *      root node pid (It's up to user consider if pid is unique. If pid is not unique
     *      then following possibility can be used).
     *      - If an array then it is taken for complete find options to retrieve root.
     * @param array $options See Model::find() and Model::getTreeConditions() options.
     *      The 'order' and 'resorce' options are set explicitly by this method.
     * @param int& $rootId Optional output. Passed by reference. Returns the provided root id.
     *      If no root is found according to provided $root specification then $rootId
     *      is set to NULL. ATTENTION: If $root is an integer number (e.g. 2 or '2'),
     *      then it is just copied to this output without checking its existence!
     *      
     * 
     * @return array The array described here above. Be aware that there are 2 'empty' possibilites
     *      for case of no child under given root and nonexisting root.
     */

    public function findTree($root, $options = array(), &$rootId = null) {
        $defaults = array(
            'literals' => null,
            'allowLiterals' => true,
        );
        $options = array_merge($defaults, $options);
        // check for the type of tree (throw exception if not tree)
        $treeType = $this->getTreeType(true);
        // PSP tree constructed by 'parent_id', 'path', 'sort' and possibly 'level'
        if ($treeType == self::PSP_TREE_TYPE) {
            // if there is set order then apply it on each tree level separately
            if (!empty($options['order'])) {
                $options['order'] = (array)$options['order'];
                // if 'level' field exists then use it
                if (!empty($this->schema['level'])) {
                    array_unshift($options['order'], $this->name . '.level ASC');
                }
                // if 'level' field does not exist then simulate it
                else {
                    //@see http://stackoverflow.com/questions/12344795/count-the-number-of-occurences-of-a-string-in-a-varchar-field
                    $orderSQL = 'ROUND((LENGTH(`' . $this->name . '`.`path`)-LENGTH(REPLACE(`' . $this->name . '`.`path`,"-","")))/LENGTH("-")) ASC';
                    array_unshift($options['order'], $orderSQL);
                    $options['literals'] = (array)$options['literals'];
                    $options['literals'] = DB::mergeLiterals(
                        $options['literals'], 
                        array('order' => $orderSQL)
                    );
                    if ($options['allowLiterals'] !== true) {
                        $options['allowLiterals'] = (array)$options['allowLiterals'];
                        $options['allowLiterals'] = DB::mergeLiterals(
                            $options['allowLiterals'], 
                            array('order' => $orderSQL)
                        );
                    }
                }
            }
            // if no explicit order is set then order the tree by 'sort' field
            else {
                $options['order'] = "{$this->name}.sort ASC";
            }
        }
        // LRP tree consructed by 'parent_id', 'lft' and 'rght'
        else {
            // set Order by lft and call $this->findAllInTree()
        }
        
        // force output type to array
        $options['resource'] = false;
        $result = $this->findInTree($root, $options, $rootId);
        
        $tree = $this->buildTree($result, $root, $rootId);
        
        return $tree;
    }   
    
    /**
     * Builds tree from output returned by method Model::findInTree().
     * 
     * @param array $nodes Result of Model::findInTree()
     * @param int|string|array $root Optional. Input $root of Model::findInTree(). Defaults to NULL.
     * @param int $rootId Optional. Auxiliary output $rootId of Model::findInTree(). 
     *      If $root is provided then this input must be provided too. Defaults to NULL.
     */
    public function buildTree($nodes, $root = null, $rootId = null) {
        // create the tree structure
        $tree = array();
        if ($nodes) {
            foreach ($nodes as $node) {
                // get id
                if (isset($node[$this->name][$this->primaryKey])) {
                    $id = $node[$this->name][$this->primaryKey];
                    unset($node[$this->name][$this->primaryKey]);
                }
                elseif (isset($node[$this->name . '.' . $this->primaryKey])) {
                    $id = $node[$this->name . '.' . $this->primaryKey];
                    unset($node[$this->name . '.' . $this->primaryKey]);
                }
                else {
                    $id = $node[$this->primaryKey];
                    unset($node[$this->primaryKey]);
                }
                // get parent id
                if (isset($node[$this->name]['parent_id'])) {
                    $parentId = $node[$this->name]['parent_id'];
                    unset($node[$this->name]['parent_id']);
                }
                elseif (isset($node[$this->name . '.parent_id'])) {
                    $parentId = $node[$this->name . '.parent_id'];
                    unset($node[$this->name . '.parent_id']);
                }
                else {
                    $parentId = $node['parent_id'];
                    unset($node['parent_id']);
                }
                // create the tree
                $tree[$parentId][$id] = $node;
            }
        }
        // root exists but there are no childs
        elseif (
            $rootId !== null
            && (
                !Validate::intNumber($root)
                // existence of integer root must be done here - see phpdoc for Model::findInTree()
                || $this->findFirstBy($this->primaryKey, $root)
            )
        ) {
            $tree = array($rootId => array());
        }
        
        return $tree;
    }
    
    /**
     * Retrieves parents of specified child tree node. Parents are ordered from 
     * the top node to direct parent of the child.
     * 
     * @param int|string|array $child Specification of child node to find parents for.
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for child node id. 
     *      - If nonnumerical string is provided ('myMenu') then it is considered  for 
     *      child node pid (It's up to user consider if pid is unique. If pid is not unique
     *      then following possibility can be used).
     *      - If an array then it is taken for complete find options to retrieve child.
     * 
     *      If more than one chiold can be retrieved using specified options then 
     *      the first retrieved is used
     * 
     * @param array $options See Model::find() options. The 'conditions' and 'order' options
     *      are set explicitly by this method.
     * 
     * @return array 
     */
    public function findParentsInTree($child, $options = array()) {
        $childDefaults = array(
            // only options used directly in this method have default values here
        );
        $defaults = array(
            // only options used directly in this method have default values here
            'conditions' => null,
            'order' => null,
            'literals' => null,
            'allowLiterals' => true,
        );
        $options = array_merge($defaults, $options);
        // force DB::select() method to return array
        $options['resource'] = false;
        
        // find child options
        $childOptions = array();
        // check for child specified by id, pid or by complete options
        if (
            !is_array($child) 
            || empty($child['path'])
        ) {
            if (Validate::intNumber($child)) {
                $childOptions['conditions'] = array($this->primaryKey => $child); // id
            }
            elseif (is_array($child)) {
                $childOptions = $child; // options
            }
            else {
                $childOptions['conditions'] = array('pid' => $child); // pid
            }
            $childOptions = array_merge($childDefaults, $childOptions);
            // force following values in child options
            $childOptions['fields'] = array("{$this->name}.path");
            $childOptions['first'] = true;
            $childOptions['limit'] = 1;
            $child = $this->find($childOptions);
            if (!$child) {
                // unexisting child so return empty result array
                return array();
            }
            // @todo - if the fields are qualified and inflated then $child['path']
            // should be retrived here
        }
        
        // check for the type of tree (throw exception if not tree)
        $treeType = $this->getTreeType(true);
        // PWP tree
        if ($treeType == self::PSP_TREE_TYPE) {
            $parentConditions = array("'{$child['path']}' LIKE CONCAT('%-',`{$this->name}`.`{$this->primaryKey}`, '-%')");
            $parentOrder = array("`{$this->name}`.`path` ASC");  
            $parentLiterals = array('conditions' => $parentConditions[0]);
            // merge it into passed conditions
            $options['conditions'] = array_merge($parentConditions, array((array)$options['conditions']));     
            $options['order'] = array_merge($parentOrder, (array)$options['order']);   
            $options['literals'] = (array)$options['literals'];
            $options['literals'] = DB::mergeLiterals(
                $options['literals'], 
                $parentLiterals
            );
            if ($options['allowLiterals'] !== true) {
                $options['allowLiterals'] = (array)$options['allowLiterals'];
                $options['allowLiterals'] = DB::mergeLiterals(
                    $options['allowLiterals'], 
                    $parentLiterals
                );
            }
        }
        // LRP tree
        else {
            //...
        }     
        
        return $this->find($options);
    }
    
    /**
     * Retrieves siblings of specified tree node.
     * 
     * @param int|string|array $node Specification of node to search siblings for in the tree:
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for node id. 
     *      - If non integer value is provided ('myMenu') then it is considered  for 
     *      node pid (It's up to user consider if pid is unique. If pid is not unique
     *      then the last possibility can be used).
     *      - If an array containing items 'path' then it is taken for node record data.
     *      If option 'includeNode' is TRUE then also 'id' must be present.  If option 'filter' 
     *      is set to 'previous' or 'next' then also 'sort' must be present. 
     *      - If other array than mentioned above then it is taken for complete find options to retrieve node.
     * 
     * @param array $options Options of Model::find() plus following to make slice 
     *      from candidate nodes (not from found nodes) up in the tree:
     *      - 'order' (string|array) See Model::find(). Defaults to 'sort ASC'.
     *      - 'filter' (bool|string) Possible values are 'previous' and 'next'. 
     *          If 'previous' then only siblings with 'sort' smaller than $node are returned. 
     *          If 'next' then only siblings with 'sort' greater than $node are returned. 
     *          For any other value all siblings are returned. Defaults to FALSE.
     *      - 'includeNode' (bool) If TRUE then also the node itself is included
     *          in result siblings array. Defaults to FALSE.
     * 
     * @param array& $nodeRecord Optional output. Passed by reference. Returns the 
     *      specified $node record data containing 'id', 'path' and 'sort'. Can be used 
     *      for optimization purposes, when the method is called more than once with 
     *      the same $node. In such case only the first call of method is made with 
     *      e.g. $node id and all the rest can be made directly with $node record data 
     *      which is optimal. NOTE: If the $node is provided as record data then 
     *      it is just copied to $nodeRecord.
     * 
     * @return array Array of found sibling nodes up in the tree. An empty array
     *      if $node does not exist or if has no siblings. By default sibling nodes 
     *      have the same order as in tree.
     */
    public function findSiblingsInTree($node, $options = array(), &$nodeRecord = null) {
        $defaults = array(
            'conditions' => array(),
            'order' => $this->name . '.sort ASC',
            'filter' => false, // 'previous', 'next'
            'includeNode' => false,
        );
        $options = array_merge($defaults, $options);
        $options['filter'] = strtolower($options['filter']);
        
        if (
            !is_array($node)
            || empty($node['path'])
            || (
                !$options['includeNode'] 
                && empty($node[$this->primaryKey])
            )
            || (
                (
                    $options['filter'] === 'previous' 
                    || $options['filter'] === 'next'
                )
                && empty($node['sort'])
            )
        ) {
            // find the node path and id
            $nodeOptions = array();
            // - if node specified by id
            if (
                is_int($node)
                || Validate::intNumber($node)
            ) {
                $nodeOptions['conditions'] = array(
                    $this->name . '.' . $this->primaryKey => $node 
                ); 
            }
            // - if node specified by find options
            elseif (is_array($node)) {
                $nodeOptions = $node; 
            }
            // - if node specified by pid
            else {
                $nodeOptions['conditions'] = array(
                    $this->name . '.pid' => (string)$node 
                ); 
            }
            // - force folloving in node options
            $nodeOptions['fields'] = array(
                $this->name . '.' . $this->primaryKey,
                $this->name . '.path',
                $this->name . '.sort',
            );
            $nodeOptions['first'] = true;
            $nodeOptions['resource'] = false;
            $node = $this->find($nodeOptions);
            if (!$node) {
                // unexisting node so return array()
                return array();
            }
        }
        // get node id
        $id = null;
        if (!empty($node[$this->primaryKey])) {
            $id = $node[$this->primaryKey];
        }
        // - treat cases then the fields are qualified and inflated
        elseif (!empty($node[$this->name . '.' . $this->primaryKey])) {
            $id = $node[$this->name . '.' . $this->primaryKey];
        }
        elseif (!empty($node[$this->name][$this->primaryKey])) {
            $id = $node[$this->name][$this->primaryKey];
        }
        // get node path
        $path = null;
        if (!empty($node['path'])) {
            $path = $node['path'];
        }
        // - treat cases then the fields are qualified and inflated
        elseif (!empty($node[$this->name . '.path'])) {
            $path = $node[$this->name . '.path'];
        }
        elseif (!empty($node[$this->name]['path'])) {
            $path = $node[$this->name]['path'];
        }
        // get node sort
        $sort = null;
        if (!empty($node['sort'])) {
            $sort = $node['sort'];
        }
        // - treat cases then the fields are qualified and inflated
        elseif (!empty($node[$this->name . '.sort'])) {
            $sort = $node[$this->name . '.sort'];
        }
        elseif (!empty($node[$this->name]['sort'])) {
            $sort = $node[$this->name]['sort'];
        }
        // set the aux output
        $nodeRecord = array();
        if ($id) {
            $nodeRecord [$this->primaryKey] = $id;
        }
        if ($path) {
            $nodeRecord ['path'] = $path;
        }
        if ($sort !== null) {
            $nodeRecord ['sort'] = $sort;
        }
        
        // find siblings
        // - nest actual conditions
        $options['conditions'] = DB::nestConditions((array)$options['conditions']);
        // - add "siblings" condition (as first one - to optimize)
        $conditions = array(
            $this->name . '.path' => $path,
        ); 
        if ($options['filter'] === 'previous') {
            $conditions[$this->name . '.sort <='] = $sort;
        }
        elseif ($options['filter'] === 'next') {
            $conditions[$this->name . '.sort >='] = $sort;
        }
        if (!$options['includeNode']) {
            $conditions[$this->name . '.' . $this->primaryKey. ' !='] = $id;
        }
        $options['conditions'] = array_merge(
            $conditions, 
            $options['conditions']
        );
        // - find
        return $this->find($options);
    }
    
    /**
     * Returns previous sibling id to be used as key in list returned by Model::findTreeSelectList()
     * with 'firstPlaceholder' option TRUE.
     * 
     * @param array|int $node Specification of node to get previous sibling id for:
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for node id. 
     *      - If an array then considered to be node record data containing at least
     *          'id', 'path', 'sort' and 'parent_id'. 
     * @param array $conditions Conditions to constrain candidate sibling records,
     *     Defaults to empty array().
     * 
     * @return int|string Either integer of previous sibling id or if no previous 
     *      sibling found then '{parentId}-' or just '-' if no (NULL) parent_id found (on top level).
     *      This means that the node is placed directly behind the parent but not
     *      on the level of parent but as the first child of parent.
     *      The same syntax is accepted by method Model::moveTreeNodeBehind().
     * 
     */
    public function getPreviousSiblingId($node, $conditions = array()) {
        $previousSibling = $this->findSiblingsInTree($node, array(
            'order' => 'sort DESC',
            'filter' => 'previous',
            'first' => true,
            'fields' => array($this->primaryKey),
            'conditions' => $conditions,
        ));
        if (!empty($previousSibling[$this->primaryKey])) {
            return $previousSibling[$this->primaryKey];
        }
        if (!empty($node['parent_id'])) {
            return $node['parent_id'] . '-';
        }
        if (!empty($node[$this->primaryKey])) {
            $id = $node[$this->primaryKey];
        }
        elseif (is_int($node) || Validate::intNumber($node)) {
            $id = $node;
        }
        else {
            return '';
        }
        return $this->findFieldBy('parent_id', $this->primaryKey, $id) . '-';
    }
        
    /**
     * Creates tree selectbox list from provided tree data like:
     *  
     *      array(
     *          '1' => 'Fruit',                     // '{nodeId}' => '{nodeLabel}'
     *          '2' => '   Apple',
     *          '3' => '   Pear',
     *          '4' => 'Vegetable',
     *          '5' => '   Carrot',
     *          '6' => '   Onion',
     *      )
     * 
     * or if the option 'firstPlaceholder' is true then:
     * 
     *      array(
     *          '-'  => --- first in
     *          '1'  => 'Fruit',                    // '{nodeId}' => '{nodeLabel}'
     *          '1-' => '--- first in Fruit'        // '{parentId}-' => '{nodeLabel}'
     *          '2'  => '   Apple',
     *          '2-' => '   --- first in Apple'
     *          '3'  => '   Pear',
     *          '3-' => '   --- first in Pear'
     *          '4'  => 'Vegetable',
     *          '4-' => '--- first in Vegetable'
     *          '5'  => '   Carrot',
     *          '5-' => '   --- first in Carrot'
     *          '6'  => '   Onion',
     *          '6-' => '   --- first in Onion'
     *      )
     * 
     * @param array& $tree Tree array retrieved by Model::findTree() to construct
     *          tree list for. Passed by reference.
     * @param array $options Following options can be used:
     *      - 'labelField' (string) Name of field to be used for list labels.
     *          It can be qualified, e.g. 'MyModel.name'. Defaults to 'name'.
     *      - 'indent' (string) Indent to be used for nested labels indentation. 
     *          To make no indentation set this to '' or NULL. Defaults to 7 nonbreakable spaces (& nbsp;).
     *      - 'accumulate' (bool) If TRUE then labels are created accumulated, e.g. 
     *          '   Fruit > Apple'. Defaults to FALSE.
     *      - 'separator' (string) Separator used in accumulated labels. Defaults to ' > '.
     *      - 'depth' (integer) Number of nested levels to create list for. If NULL
     *          then list is created for all available levels in $tree data. Defaults to NULL.
     *      - 'template' (string|array) Template(s) to display label(s) on provided levels, e.g.
     *          array (1 => '>>>:l:'). If level has its template defined or there is 'default'
     *          template defined then that template is used to display the level label.
     *          Use the insert :l: to place label into template. Defaults to NULL.
     *      - 'firstPlaceholder' (bool|string) If nonepmty value then first items
     *          placeholders are created at the begining of each level. Use the insert :l: 
     *          to place parent node label into placeholder. If TRUE then '--- first in :l:' is used 
     *          as placeholder. Defaults to FALSE.
     *      - 'firstPlaceholderStart' (int) First placeholder start level. Defaults to 1.
     *      - 'activeField' (string) Name of field to be used identify inactivity of tree items.
     *          Qualifier is added implicitly according to model alias. Must be ptovided if 
     *          option 'markInactive' is non-empty. Defaults to 'active'.
     *      - 'markInactive' (bool|string) If TRUE then inactive items are marked
     *          by appendix ' (!)'. If string then used as appendix for inactive items 
     *          (leading space must be included). If non-empty then option 'activeField' 
     *          must be provided too. Defaults to FALSE.
     *          
     * @return array Tree list usable for selectbox
     */
    static public function getTreeSelectList(&$tree, $options = array(), /*internal use:*/ $parentId = null, $parentLabel = '', $level = 0) {
        static $qualifiedLabelField = false;
        static $qualifiedActiveField = false;
        
        $defaults = array(
            'labelField' => 'name',
            'indent' => '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;',
            'accumulate' => false,
            'separator' => ' > ',
            'depth' => null,
            'template' => null,
            'firstPlaceholder' => false,
            'firstPlaceholderStart' => 1,
            'activeField' => 'active',
            'markInactive' => false,
        );
        $options = array_merge($defaults, $options);
        if (
            !empty($options['firstPlaceholder'])
            && !is_string($options['firstPlaceholder'])
        ) {
            $options['firstPlaceholder'] = __(__FILE__, '--- 1st in :l:');
        }
        
        // initialize
        $recurse = true;
        // - on first level
        if ($level === 0) {
            $nodes = (array)reset($tree);
            $parentId = (array)array_keys($tree);
            $parentId = reset($parentId);
            $qualifiedLabelField = strpos('.', $options['labelField']) !== false;
            if ($options['markInactive']) {
                $qualifiedActiveField = strpos('.', $options['activeField']) !== false;
            }
        }
        // - on other levels
        elseif (!empty($tree[$parentId])) {
            $nodes = $tree[$parentId];
        }
        else {
            $nodes = array();
            $recurse = false;
        }
        // - on all levels
        $indent = str_repeat((string)$options['indent'], $level);
        $level++;
        if (is_array($options['template'])) {
            if (!empty($options['template'][$level])) {
                $template = $options['template'][$level];
            }
            elseif (!empty($options['template']['default'])) {
                $template = $options['template']['default'];
            }
            else {
                $template = '';
            }
        }
        else {
            $template = (string)$options['template'];
        }
        
        $list = array();
        if (
            !empty($options['firstPlaceholder'])
            && $level >= $options['firstPlaceholderStart']
        ) {
            if ($qualifiedLabelField) {
                $firstNode = array();
                Arr::setPath($firstNode, $options['labelField'], str_replace(':l:', $parentLabel, $options['firstPlaceholder']));
            }
            else {
                $firstNode = array($options['labelField'] => str_replace(':l:', $parentLabel, $options['firstPlaceholder']));
            }
            $nodes = array((int)$parentId . '-' => $firstNode) + $nodes;
        }
        foreach ($nodes as $nodeId => $node) {
            if ($qualifiedLabelField) {
                $label = Arr::getPath($node, $options['labelField']);
            }
            elseif (isset($node[$options['labelField']])) {
                $label = $node[$options['labelField']];
            }
            else {
                $label = '';
            }
            if (
                $options['accumulate']
                && !empty($parentLabel)
                && substr($nodeId, -1) !== '-'
            ) {
                $label = $parentLabel . $options['separator'] . $label;
            }
            $levelLabel = $label;
            // apply level template
            if (!empty($template)) {
                $levelLabel = str_replace(':l:', $levelLabel, $template);
            }
            // apply inactive appendix
            if ($options['markInactive']) {
                $active = true;
                if ($qualifiedActiveField) {
                    $active = Arr::getPath($node, $options['activeField']);
                }
                elseif (isset($node[$options['activeField']])) {
                    $active = $node[$options['activeField']];
                }
                elseif (substr($nodeId, -1) !== '-') {
                    $active = false;
                }
                if (!$active) {
                    if (is_string($options['markInactive'])) {
                        $levelLabel .= $options['markInactive'];
                    }
                    else {
                        $levelLabel .= ' (!)';
                    }
                }
            }
            $list[$nodeId] = $indent . $levelLabel;
            if (
                (
                    !empty($tree[$nodeId])
                    ||
                    !empty($options['firstPlaceholder'])
                    && $recurse
                )
                && (
                    empty($options['depth']) 
                    || $options['depth'] > $level
                )
            ) {
                // array_merge() would reindex from 0, we need keep numeric keys (ids)
                $list = $list + self::getTreeSelectList($tree, $options, $nodeId, $label, $level); 
            }
        }
        return $list;
    }
    
    /**
     * Finds tree list for provided $root like:
     *  
     *      array(
     *          '1' => 'Fruit',                     // '{nodeId}' => '{nodeLabel}'
     *          '2' => '   Apple',
     *          '3' => '   Pear',
     *          '4' => 'Vegetable',
     *          '5' => '   Carrot',
     *          '6' => '   Onion',
     *      )
     * 
     * or if the option 'firstPlaceholder' is true then:
     * 
     *      array(
     *          '-'  => --- first in
     *          '1'  => 'Fruit',                    // '{nodeId}' => '{nodeLabel}'
     *          '1-' => '--- first in Fruit'        // '{parentId}-' => '{nodeLabel}'
     *          '2'  => '   Apple',
     *          '2-' => '   --- first in Apple'
     *          '3'  => '   Pear',
     *          '3-' => '   --- first in Pear'
     *          '4'  => 'Vegetable',
     *          '4-' => '--- first in Vegetable'
     *          '5'  => '   Carrot',
     *          '5-' => '   --- first in Carrot'
     *          '6'  => '   Onion',
     *          '6-' => '   --- first in Onion'
     *      )
     * 
     * @param int|string|array $root Specification of root node of required (sub)tree.
     *      This is not root of whole tree, but parent node of (sub)tree we are looking for. 
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for root node id. 
     *      - If non integer value is provided (e.g. 'myMenu') then it is considered  for 
     *      root node pid. It's up to user consider if pid is unique. If pid is not unique
     *      then following possibility can be used.
     *      - If an array then it is taken for complete find options to retrieve root.
     * @param array $options Options of Model::find() and Model::getTreeConditions() plus following:
     *      - 'labelField' (string) Name of field to be used for list labels.
     *          Qualifier is added implicitly according to model alias. Defaults 
     *          to $this->nameField ?: 'name'.
     *      - 'indent' (string) Indent to be used for nested labels indentation. 
     *          To make no indentation set this to '' or NULL. Defaults to 7 nonbreakable spaces (& nbsp;).
     *      - 'accumulate' (bool) If TRUE then labels are created accumulated, e.g. 
     *          '   Fruit > Apple'. Defaults to FALSE.
     *      - 'separator' (string) Separator used in accumulated labels. Defaults to ' > '.
     *      - 'depth' (integer) Number of nested levels to create list for. If NULL
     *          then list is created for all available levels in $tree data. Defaults to NULL.
     *      - 'template' (string|array) Template(s) to display label(s) on provided levels, e.g.
     *          array (1 => '>>>:l:'). If level has its template defined or there is 'default'
     *          template defined then that template is used to display the level label.
     *          Use the insert :l: to place label into template. Defaults to NULL.
     *      - 'prependRoot' (bool) If TRUE then root node is prepended on the top of the list.
     *          Defaults to FALSE.
     *      - 'firstPlaceholder' (bool|string) If nonepmty value then first items
     *          placeholders are created at the begining of each level. Use the insert :l: 
     *          to place parent node label into placeholder. If TRUE then '--- first in :l:' is used 
     *          as placeholder. Defaults to FALSE.
     *      - 'firstPlaceholderStart' (int) First placeholder start level. Defaults to 1.
     *      - 'activeField' (string) Name of field to be used identify inactivity of tree items.
     *          Qualifier is added implicitly according to model alias. Must be ptovided if 
     *          option 'markInactive' is non-empty. Defaults to 'active'.
     *      - 'markInactive' (bool|string) If TRUE then inactive items are marked
     *          by appendix ' (!)'. If string then used as appendix for inactive items 
     *          (leading space must be included). If non-empty then option 'activeField' 
     *          must be provided too. Defaults to FALSE.
     * 
     * @param int& $rootId Optional output. Passed by reference. Returns the provided root id.
     *      If no root is found according to provided $root specification then $rootId
     *      is set to NULL. ATTENTION: If $root is an integer number (e.g. 2 or '2'),
     *      then it is just copied to this output without checking its existence!
     *       
     * @return array Tree list usable for selectbox
     */
    public function findTreeSelectList($root, $options = array(), &$rootId = null) {
        $defaults = array(
            'labelField' => $this->nameField ?: 'name',
            'indent' => '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;',
            'accumulate' => false,
            'separator' => ' > ',
            'depth' => null,
            'template' => null,
            'prependRoot' => false,
            'firstPlaceholder' => false,
            'activeField' => 'active',
            'markInactive' => false,
        );
        $options = array_merge($defaults, $options);
        $options['indent'] = (string)$options['indent'];
                
        $list = array();
        
        // force order and fields
        $fields = array(
            $this->name . '.' . $options['labelField'],
            $this->name . '.' . $this->primaryKey,
            $this->name . '.path',
        );
        if ($options['markInactive']) {
            $fields[] = $this->name . '.' . $options['activeField'];
        }
        $options['fields'] = $fields;
        $options['allowFields'] = null;
        $options['avoidFields'] = null;
        $options['order'] = array($this->name . '.sort ASC');
        $options['inflate'] = false;
        $options['qualify'] = false;
        $options['separate'] = false;
        if (empty($options['firstPlaceholder'])) {
            $nodes = $this->findInTree($root, $options, $rootId);
        }
        // if first placeholder is required then we use tree to build list, see below
        else {
            $fields[] = $this->name . '.parent_id';
            $nodes = $this->findTree($root, $options, $rootId);
        }
        if (empty($nodes)) {
            return $list;
        }
        // prepend root if required
        if ($options['prependRoot']) {
            $rootNode = $this->findFirstBy($this->name . '.' . $this->primaryKey, $rootId, array(
                'fields' => $fields
            ));
            if (empty($options['firstPlaceholder'])) {
                array_unshift($nodes, $rootNode);
            }
            // if first placeholder is required then we use tree to build list, see below
            else {
                $nodes = array($rootNode['parent_id'] => array($rootNode[$this->primaryKey] => $rootNode)) + $nodes;
            }
        }
        // if first placeholder is required then we use tree to build list
        if (!empty($options['firstPlaceholder'])) {
            return self::getTreeSelectList($nodes, $options);
        }
        
        // get top level 
        $topLevel = reset($nodes);
        $topLevel = trim($topLevel['path'], '-');
        if ($topLevel === '') {
            $topLevel = 0;
        }
        else {
            $topLevel = count(explode('-', $topLevel));
        }
        // create list
        $accumulatedLabels = array();
        foreach ($nodes as $node) {
            $id = $node[$this->primaryKey];
            $label = $node[$options['labelField']];
            $path =  trim($node['path'], '-');
            if ($path === '') {
                $parentIds = array();
            }
            else {
                $parentIds = explode('-', $path);
            }
            $level = count($parentIds) - $topLevel;
            // get level template
            if (is_array($options['template'])) {
                if (!empty($options['template'][$level])) {
                    $template = $options['template'][$level];
                }
                elseif (!empty($options['template']['default'])) {
                    $template = $options['template']['default'];
                }
                else {
                    $template = '';
                }
            }
            else {
                $template = (string)$options['template'];
            }
            // check for accumulated label
            if ($options['accumulate']) {
                if (!empty($parentIds)) {
                    $parentId = end($parentIds);
                    if (!empty($accumulatedLabels[$parentId])) {
                        $label = $accumulatedLabels[$parentId] . $options['separator'] . $label;
                    }
                    $accumulatedLabels[$id] = $label;
                }
            }
            // apply level template
            if (!empty($template)) {
                $label = str_replace(':l:', $label, $template);
            }
            // apply inactive appendix
            if (
                $options['markInactive']
                && empty($node[$options['activeField']])
            ) {
                if (is_string($options['markInactive'])) {
                    $label .= $options['markInactive'];
                }
                else {
                    $label .= ' (!)';
                }
            }
            // apply indent
            $indent = str_repeat($options['indent'], $level);
            $list[$id] = $indent . $label;
        }

        return $list;
    }
    
    /**
     * Adds a new node into the tree under specified parent. New node data can
     * be optionally specified.
     * 
     * @param int $parentId If NULL (or empty value) then the node is created on top level,
     *      means new root is created
     * @param array $data Optional. Data of new created node.
     * @param array $options See Model::save() options.
     * 
     * @return array|bool Array of created record data containing id of created record
     *      and other values after normalization or file uploads. FALSE if validation fails.  
     */
    public function addTreeNode($parentId, $data = array(), $options = array()) {
        // start transaction
        DB::startTransaction('Model_addTreeNode');
        try {
            // check for the type of tree (throw exception if not tree)
            $treeType = $this->getTreeType(true);
            // PWP tree
            if ($treeType == self::PSP_TREE_TYPE) {
                // new root node on top level
                if (empty($parentId)) {
                    // find the sort weight of last node in tree
                    $lastNode = DB::select($this->table, array(
                        'fields' => array('sort'),
                        'order' => array('sort DESC'),
                        'first' => true,
                    ));
                    // set the sort, path and parent id of the new root node
                    if ($lastNode) {
                        $data['sort'] = $lastNode['sort'] + 1;
                    }
                    else {
                        $data['sort'] = 0;
                    }
                    $data['path'] = '-';
                    $data['parent_id'] = null;
                }
                // new child node
                else {
                    // find the parent node
                    $parent = DB::select($this->table, array(
                        'fields' => array('path', 'sort'),
                        'conditions' => array($this->primaryKey => $parentId),
                        'first' => true,
                    ));

                    if (empty($parent)) {
                        DB::rollbackTransaction('Model_addTreeNode');
                        return false;
                    }
                    // find the sort weight of parent last child
                    $lastChild = DB::select($this->table, array(
                        'fields' => array('sort'),
                        'conditions' => array('path LIKE' => '%-' . $parentId . '-%'),
                        'order' => array('sort DESC'),
                        'first' => true,
                    ));
                    // set the sort, path and parent id of the new node
                    if (empty($lastChild)) {
                        $data['sort'] = $parent['sort'] + 1;
                    }
                    else {
                        $data['sort'] = $lastChild['sort'] + 1;
                    }
                    $data['path'] = $parent['path'] . $parentId . '-';
                    $data['parent_id'] = $parentId;
                    // update sort weights of existing nodes which will be pushed up by new one
                    DB::update(
                        $this->table, 
                        array('sort' => '`sort` + 1'),
                        array(
                            'conditions' => array('sort >= ' . $data['sort']),
                            'literals' => array('fields' => true),
                        )
                    );
                }
                // save the new node
                // force the creation of new node
                $options['create'] = true;
                if (!$result = $this->save($data, $options)) {
                    DB::rollbackTransaction('Model_addTreeNode');
                    return false;
                }
            }
            // LRP tree
            else {
                //...
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('Model_addTreeNode');
            throw $e;
        }
        // commit transaction
        DB::commitTransaction('Model_addTreeNode');
        return $result;
    }      
    
    /**
     * Deletes specified tree node with all its childs
     * 
     * @param int $id Id of the node to be removed.
     * @param array $options See Model::delete() options.
     * 
     * @return bool|array Returns FALSE if the specified node does not exist. 
     *      Othervise returns array of removed nodes ids.
     */
    public function deleteTreeNode($id, $options = array()) {
        $defaults = array(
            'softDelete' => true,
        );
        $options = array_merge($defaults, $options);
        // start transaction
        DB::startTransaction('Model_deleteTreeNode');
        try {
            // check for the type of tree (throw exception if not tree)
            $treeType = $this->getTreeType(true);
            // PWP tree
            if ($treeType == self::PSP_TREE_TYPE) {
                // get sort weight of removed node
                // and check if node with given id exists
                $node = DB::select($this->table, array(
                    'fields' => array('sort'),
                    'conditions' => array($this->primaryKey => $id),
                    'first' => true,
                ));
                if (empty($node)) {
                    DB::rollbackTransaction('Model_deleteTreeNode');
                    return false;
                }
                // find ids of the removed node childs
                $childsIds = $this->findList(array(
                    'fields' => array($this->primaryKey),
                    'conditions' => array('path LIKE' => '%-' . $id . '-%'),
                    'translate' => false,
                ));
                // find removed nodes count
                $removedNodesCount = count($childsIds) + 1;
                // find removed nodes ids
                $removedNodesIds = array_merge(array($id), $childsIds);
                // force conditions in delete $options
                $options['conditions'] = array($this->name . '.' . $this->primaryKey => $removedNodesIds);
                $this->delete($options);
                // if real delete (not soft delete) is done then actualize sort weights
                if (empty($this->allowsSoftDelete) || empty($options['softDelete'])) {
                    // decreas sort weights of nodes placed after removed nodes                        
                    DB::update(
                        $this->table, 
                        array('sort' => '`sort` - ' . $removedNodesCount),
                        array(
                            'conditions' => array('sort >' => $node['sort']),
                            'literals' => array('fields' => true),
                        )
                    );
                }
            }
            // LRP tree
            else {
                //...
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('Model_deleteTreeNode');
            throw $e;
        }
        // commit transaction
        DB::commitTransaction('Model_deleteTreeNode');
        return $removedNodesIds;
    }    
    
    /**
     * Moves the specified node under specified new parent node on specified position.
     * 
     * @param int $id Moved node id
     * @param int $newParentId New parent id to move the node under
     * @param array $options Following are available
     *      - 'newOrderIndex' (int) Order index of moved node under new parent.
     *          It is an integer value starting from 0 (0 to place it as the first child).
     *          If NULL then the moved node is placed as the last child of new parent.
     *          If 'softDeletedIncluded' is TRUE then also soft deleted records
     *          must be included in 'newOrderIndex'. Defaults to NULL.  
     *      - 'softDeletedIncluded' (bool) Are soft deleted records included in
     *          'newOrderIndex'? Defaults to FALSE.  
     * 
     * @return boolean TRUE on success. FALSE if the moved or new parent node does
     *      not exist or if the node tries to be moved under one of its childs
     */
    public function moveTreeNode($id, $newParentId, $options = array()) {
        $defaults = array(
            'newOrderIndex' => null,
            'softDeletedIncluded' => false,
        );
        $options = array_merge($defaults, $options);
        $newOrderIndex = $options['newOrderIndex'];
        $softDeletedIncluded = $options['softDeletedIncluded'];
        // start transaction
        DB::startTransaction('Model_moveTreeNode');
        // check for the type of tree (throw exception if not tree)
        $treeType = $this->getTreeType(true);
        // PWP tree
        if ($treeType == self::PSP_TREE_TYPE) {
            // check if moved node with given id exists
            // and find its old sort weight and path
            $node = DB::select($this->table, array(
                'fields' => array('sort', 'path'),
                'conditions' => array($this->primaryKey => $id),
                'first' => true,
            ));
            if (empty($node)) {
                DB::rollbackTransaction('Model_moveTreeNode');
                return false;
            }
            $oldWeight = $node['sort'];
            // find node childs count (and so if node is branch or leaf)
            $nodeChildsCount = DB::select($this->table, array(
                'fields' => array('COUNT(*) AS `_count`'),
                'conditions' => array('path LIKE ' => '%-' . $id . '-%'), 
                'literals' => array('fields' => true),
                'first' => true,
            ));
            $nodeChildsCount = (int)$nodeChildsCount['_count'];
            $movedNodesCount = $nodeChildsCount + 1;
            // check if new parent exists 
            // and find its path and sort weight
            $newParentNode = DB::select($this->table, array(
                'fields' => array('path', 'sort'),
                'conditions' => array($this->primaryKey => $newParentId),
                'first' => true,
            ));
            if (empty($newParentNode)) {
                DB::rollbackTransaction('Model_moveTreeNode');
                return false;
            }
            // moving the node under its child is not possible
            if (in_array($id, self::getParentIdsFromTreePath($newParentNode['path']))) {
                DB::rollbackTransaction('Model_moveTreeNode');
                return false;
            }
            // find the new sort weight of moved node
            // - find new siblings under new parent
            $newSiblingsFields = array($this->primaryKey, 'sort');
            if (
                $newOrderIndex !== null
                && $this->allowsSoftDelete 
                && !$softDeletedIncluded
            ) {
                $newSiblingsFields[] = $this->deletedField;
            }
            $newSiblings = DB::select($this->table, array(
                'conditions' => array(
                    'parent_id' => $newParentId,
                    // skip the node itself
                    $this->primaryKey . ' !=' => $id, 
                ),
                'fields' => $newSiblingsFields,
                'order' => array('sort ASC'),
            ));
            // - if no siblings found then new sort weight is new parent weigth + 1
            if (empty($newSiblings)) {
                $newWeight = $newParentNode['sort'] + 1;
            }
            else {
                // if soft delete is allowed but soft deleted records are not 
                // included in newOrderIndex then make correction of newOrderIndex
                // by the amount of previous soft deleted siblings
                if (
                    $newOrderIndex !== null
                    && $this->allowsSoftDelete 
                    && !$softDeletedIncluded
                ) {
                    foreach ($newSiblings as $i => $newSibling) {
                        if (empty($newSibling[$this->deletedField])) {
                            $newOrderIndex -= 1;
                        }
                        if ($newOrderIndex <= -1) {
                            break;
                        }
                    }
                    $newOrderIndex = $i + $newOrderIndex + 1;
                }
                // - if there are siblings and node is placed on position of one of them
                // then use the sort weight of 'pushed' sibling
                if (
                    $newOrderIndex !== null 
                    && count($newSiblings) >= ($newOrderIndex + 1)
                ) {
                    $newWeight = $newSiblings[$newOrderIndex]['sort'];
                }
                // - if there are siblings but our node is placed as the last one (this is 
                // also when $newOrderIndex is NULL) then find out if last sibling has 
                // some childs. If yes then new sort weight is last child sort + 1. 
                // If no (last sibling is leaf) then new sort weight is last sibling sort + 1
                else {
                    $lastSibling = array_pop($newSiblings);
                    // find last siblin childs
                    $lastChildWeight = DB::select($this->table, array(
                        'fields' => array('MAX(sort) AS sort'),
                        'conditions' => array('path LIKE \'%-' . $lastSibling[$this->primaryKey] . '-%\''), 
                        'literals' => array('fields' => true),
                        'first' => true,
                    ));
                    if (empty($lastChildWeight['sort'])) {
                        $newWeight = $lastSibling['sort'] + 1;
                    } 
                    else {
                        $newWeight = $lastChildWeight['sort'] + 1;
                    }
                }
            }
            // if node keeps its sort weight even after replacement (this happens when
            // the node is placed as the last child of moved node current sibling, 
            // which is placed just before the moved node)
            if ($newWeight == $oldWeight) {
                $movedDiff = 0;
                $movedDiffOperator = '+';
            }
            else {
                // if moving node towards the beginning of tree
                if ($newWeight < $oldWeight) {
                    // increase sort weight of pushed items (placed between new node sort and old node sort)
                    // by the increment which equals to number of moved nodes (childs + node itself)
                    $pushedDiff = $movedNodesCount;
                    $pushedDiffOperator = '+';
                    $pushedTopWeight = $oldWeight;
                    $pushedBottomWeight = $newWeight - 1;
                    // decrease sort weights of moved items by difference between new and old sort
                    $movedDiff = $oldWeight - $newWeight;
                    $movedDiffOperator = '-';
                } 
                // if moving node towards the end of tree
                else {
                    // decrease sort weight of pushed items (placed between new node sort weight and old node weight)
                    // by the decrement which equals to number of moved nodes (childs + node itself)
                    $pushedDiff = $movedNodesCount;
                    $pushedDiffOperator = '-';
                    $pushedTopWeight = $newWeight;
                    $pushedBottomWeight = $oldWeight + $nodeChildsCount;
                    // if we are moving the node up the tree then the new sort weight must be
                    // decreased by amount of moved nodes
                    $newWeight = $newWeight - $movedNodesCount;
                    // increase sort weights of moved items by difference between new and old weight
                    $movedDiff = $newWeight - $oldWeight;
                    $movedDiffOperator = '+';
                }
                // update sort weights of pushed nodes (nodes which must change weights because of moved node and its childs)
                DB::update(
                    $this->table, 
                    array(
                        'sort' => '`sort` ' . $pushedDiffOperator . ' ' . $pushedDiff, //"`sort` $pushedDiffOperator $pushedDiff",
                    ),
                    array(
                        'conditions' => array(
                            'sort >' => $pushedBottomWeight,
                            'sort <' => $pushedTopWeight,
                        ),
                        'literals' => array('fields' => true),
                    )
                );
            }
            // update sort weights of moved node and all its childs
            $data = array(
                'sort' => '`sort` ' . $movedDiffOperator . ' ' . $movedDiff, //"`sort` $movedDiffOperator $movedDiff",
                'path' => 'CONCAT(\'' . $newParentNode['path'] . '\',(REPLACE(`path`,\'' . $node['path'] . '\',\'' . $newParentId . '-\')))', //"CONCAT('{$newParentNode['path']}',(REPLACE(`path`,'{$node['path']}','{$newParentId}-')))"
            );
            $conditions = array(
                $this->primaryKey => $id,
            );
            if (!$nodeChildsCount) {
                $data['parent_id'] = $newParentId;
            }
            else {
                $conditions[] = 'OR';
                $conditions['path LIKE'] = '%-' . $id . '-%';
            }
            DB::update(
                $this->table, 
                $data,
                array(
                    'conditions' => $conditions,
                    'literals' => array(
                        'fields' => array('sort', 'path'),
                        'conditions' => array('path LIKE' => '%-' . $id . '-%'), 
                    ),
                )
            );
            // update node parent id (if node has no childs it was already updated here above)   
            if ($nodeChildsCount) {
                DB::update(
                    $this->table, 
                    array('parent_id' => $newParentId),
                    array('conditions' => array($this->primaryKey => $id))
                );
            }
        }
        // LRP tree
        else {
            //...
        }
        // commit transaction
        DB::commitTransaction('Model_moveTreeNode');
        return true;
    }     
    
    /**
     * Moves the node behind specified new previous sibling.
     * 
     * @param int $id Moved node id
     * @param int|string $newPreviousSiblingId New sibling id to move the node behind.
     *      ATTENTION: This input can use special syntax to place the node as the first
     *      sibling by providing '{parentId}-' or just '-' if no parent (on top level).
     *      This means that the node will be moved directly behind the parent but not
     *      on the level of parent but as the first child of parent
     * 
     * @return boolean TRUE on success. FALSE if the moved or new previous sibling node 
     *      does not exist or if the node tries to be moved under one of its childs
     */
    public function moveTreeNodeBehind($id, $newPreviousSiblingId) {
        // check for case of '{parentId}_'
        if (substr($newPreviousSiblingId, -1) === '-') {
            $newParentId = substr($newPreviousSiblingId, 0, -1);
            $newOrderIndex = 0;
        }
        else {
            $node = $this->findFirstBy($this->primaryKey, $id, array('fields' => array('parent_id', 'sort')));
            if (empty($node)) {
                return false;
            }
            $previousSiblings = $this->findSiblingsInTree($newPreviousSiblingId, array(
                'filter' => 'previous',
                'includeNode' => true,
                'fields' => array($this->primaryKey, 'parent_id', 'sort'),
                'ignoreSoftDeleted' => false,
            ));
            if (empty($previousSiblings)) {
                return false;
            }
            $newParentId = Sanitize::value($previousSiblings[0]['parent_id']);
            $newOrderIndex = count($previousSiblings);
            // if the previous sibling is under the same parent as node then
            // the node itself is counted in to previous siblings if the sibling
            // has greater (or equal) sort as node. So decrement orderIndex by one
            // in such a case.
            $previousSibling = end($previousSiblings);
            if (
                $previousSibling['parent_id'] == $node['parent_id']
                && $previousSibling['sort'] >= $node['sort']
            ) {
                $newOrderIndex -= 1;
            }
        }
        if (empty($newParentId)) {
            $newParentId = null;
        }
        return $this->moveTreeNode($id, $newParentId, array(
            'newOrderIndex' => $newOrderIndex,
            'softDeletedIncluded' => true,
        ));
    }
    
    /**
     * Copies tree from specified source node to target node.
     * 
     * ATTENTION: Target node cannot be child of source node and it cannot be identical with source node.
     * 
     * @param int|array $source Source node can use two types of specification:
     *      - If an integer then it is considered to be a source node id
     *      - If an array then it is considered to be Model::find() method options
     *          to find the source node. Do not use 'separate' in find options.
     * @param int|array $target Target node can use two types of specification:
     *      - If an integer then it is considered to be a target node id
     *      - If an array then it is considered to be Model::find() method options
     *          to find the target node. Do not use 'separate' in find options.
     *          NOTE: If the target node is not found using provided find options 
     *          then there is created a new target node using find options conditions 
     *          and $options['copyData'] and source node data to create data of new created target node. 
     *          If you suppose that the target node does not exist (and it will be autocreated) 
     *          then use only associative condition definitions without fields qualification, 
     *          e.g. array('parent_id' => null, 'lang' => 'en'), BUT NOT array('parent_id IS NULL', 'WebContent.lang' => 'en').
     *          If you will not specify parent_id in find conditions then it is inherited
     *          from source node data.
     * @param array $options See Model::find() options (to retrieve source tree nodes) 
     *      and Model::save () (to save target nodes) options plus folowing:
     *      - 'copyData' (array|callable) Array of data used to prepare copied nodes data (
     *          merged into).  It can be also a callable with original node data on input
     *          and returning data of copied node. Copy data can be used to tweak values
     *          of new created nodes. Defaults to empty array().
     *      - 'targetEmpty' (bool) If TRUE then target must be empty to allow copying.
     *          Defaults to TRUE.
     * 
     * @return array|bool Array of copy ids pairs like 'sourceChildNodeId' => 'targetChildNodeId'.
     *      FALSE if validation fails.
     * 
     * @throws Exception
     */
    public function copyTreeNode($source, $target, $options = array(), /* internal use */ $sourceTree = null) {
        // keep track of itinial recursion level
        $topLevelCall = false;
        try {
            // this should be run just on top level call of method
            if (empty($sourceTree)) {
                $topLevelCall = true;
                // check if we are in tree
                $treeType = $this->getTreeType(true);
                if (
                    $treeType !== self::PSP_TREE_TYPE
                    && $treeType !== self::LRP_TREE_TYPE
                ) {
                    throw new Exception(__e(__FILE__, 'Model %s is not a tree, you cannot copy nodes', $this->name));
                }
                $defaults = array(
                    'depth' => null,
                    'copyData' => array(),
                    'targetEmpty' => true,
                    'avoidValidations' => null,
                );
                $options = array_merge($defaults, $options);
                // disable file fields validations (files are treated differently when copied)
                $options['avoidValidations'] = (array)$options['avoidValidations'];
                foreach ($this->fileFields as $fileField => $v) {
                    $options['avoidValidations'][$fileField] = true;
                }
                // force options concerning files as they are treate by this method
                $options['processFiles'] = false;
                $options['cleanUpFiles'] = false;
                // start transaction
                DB::startTransaction('Model_copyTreeNode');
                // normalize source provided by find options to id
                if (is_array($source)) {
                    $source['fields'] = array($this->primaryKey);
                    $source['allowFields'] = null;
                    $source['avoidFields'] = null;
                    // if no source found then return empty array - there is nothing to copy
                    if (!($sourceNode = $this->findFirst($source))) {
                        return array();
                    }
                    $source = $sourceNode[$this->primaryKey];
                }
                // retrieve source tree
                $options['fields'] = array_keys($this->schema);
                $options['allowFields'] = null;
                $options['avoidFields'] = null;
                $sourceTree = $this->findTree($source, $options);
                // if target is specified by id then check for it existentce
                if (!is_array($target)) {
                    $targetNode = $this->findFirstBy($this->primaryKey, $target, array(
                        'fields' => array($this->primaryKey, 'path'),
                    ));
                }
                // normalize target provided by find options to id
                else {
                    $target['fields'] = array($this->primaryKey, 'path');
                    $target['allowFields'] = null;
                    $target['avoidFields'] = null;
                    $targetNode = $this->findFirst($target);
                    // if the target node is not found by provided find options then create 
                    // a new one according to provided find options
                    if (empty($targetNode)) {
                        $sourceNode = $this->findFirstBy($this->primaryKey, $source, array(
                            'fields' => array_keys($this->schema)
                        ));
                        if (Validate::callableFunction($options['copyData'])) {
                            $data = array_merge($sourceNode, call_user_func_array($options['copyData'], array($sourceNode)));
                        }
                        else {
                            $data = array_merge($sourceNode, $options['copyData']);
                        }
                        $data = array_merge($data, (array)Sanitize::value($target['conditions']));
                        $data = $this->prepareCopyData($data);
                        $parentId = Sanitize::value($data['parent_id']);
                        // if target creation fails (validation errors) then return FALSE
                        if (!($targetNode = $this->addTreeNode($parentId, $data, $options))) {
                            $this->cleanUpFiles(false);
                            DB::rollbackTransaction('Model_copyTreeNode');
                            return false;
                        }
                    }
                    $target = $targetNode[$this->primaryKey];                
                }
                // check that target node is not child of source node
                if (strpos($targetNode['path'], '-' . $source . '-') !== false) {
                    throw new Exception(__e(__FILE__, 'Target node must not by child of source node'));
                }
                // check that source and target are not the same node
                if ($target === $source) {
                    throw new Exception(__e(__FILE__, 'Source and target must be two different nodes. Now they are identical.'));
                }
                // check that target node is empty if required
                if ($options['targetEmpty']) {
                    $targetChilds = $this->findInTree($target, array('depth' => 1));
                    if (!empty($targetChilds)) {
                        throw new Exception('Target node is required to be empty. It is not.');
                    }
                }
            }

            // create copy of the tree node by node
            $copyIds = array();
            $childs = (array)Sanitize::value($sourceTree[$source]);
            $callableCopyData = Validate::callableFunction($options['copyData']);
            foreach($childs as $sourceChildId => $childData){
                // save copy of content category
                if ($callableCopyData) {
                    $childData = array_merge($childData, call_user_func_array($options['copyData'], array($childData)));
                }
                else {
                    $childData = array_merge($childData, $options['copyData']);
                }
                $childData = $this->prepareCopyData($childData);
                if (!($childData = $this->addTreeNode($target, $childData, $options))) {
                    $this->cleanUpFiles(false);
                    DB::rollbackTransaction('Model_copyTreeNode');
                    return false;
                }
                $targetChildId = $childData[$this->primaryKey];
                $copyIds[$sourceChildId] = $targetChildId;
                // recurse into childs of current node if there are some
                if (!empty($sourceTree[$sourceChildId])) {
                    $childCopyIds = $this->copyTreeNode($sourceChildId, $targetChildId, $options, $sourceTree);
                    if (!$childCopyIds) {
                        DB::rollbackTransaction('Model_copyTreeNode');
                        return false;
                    }
                    $copyIds += $childCopyIds;
                }
            }
        }
        catch (Throwable $e) {
            $this->cleanUpFiles(false);
            DB::rollbackTransaction('Model_copyTreeNode');
            throw $e;
        }
        // if on top level end then commit transaction and clean files
        if ($topLevelCall) {
            $this->cleanUpFiles(true);
            DB::commitTransaction('Model_copyTreeNode');
        }
        
        return $copyIds;
    }      
    
    /**
     * Updates data of all nodes under specified root. If option 'includeRoot' is TRUE
     * then also root is updated.
     * 
     * @param int|string|array $root Specification of root node of required (sub)tree.
     *      This is not root of whole tree, but parent node of (sub)tree we are looking for. 
     *      - If an integer (e.g. 2 or '2') is provided then it is taken for root node id. 
     *      - If non integer value is provided (e.g. 'myMenu') then it is considered  for 
     *      root node pid. It's up to user consider if pid is unique. If pid is not unique
     *      then following possibility can be used.
     *      - If an array then it is taken for complete find options to retrieve root.
     * @param array $data
     * @param array $options See Model::update() and Model::getTreeConditions() options plus:
     *      - 'includeRoot' (bool) If TRUE then also root is updated.
     *          Defaults to FALSE.
     * @param int& $rootId Optional output. Passed by reference. Returns the provided root id.
     *      If no root is found according to provided $root specification then $rootId
     *      is set to NULL. ATTENTION: If $root is an integer number (e.g. 2 or '2'),
     *      then it is just copied to this output without checking its existence!
     * 
     * @return bool. TRUE if updated. FALSE if validation fails or in case that 
     *      root specified by pid or find options does not exist. 
     */
    public function updateInTree($root, $data, $options = array(), &$rootId = null) {
        $defaults = array(
            // only options used directly in this method have default values here
            'conditions' => null,
        );
        $options = array_merge($defaults, $options);
        $treeConditions = $this->getTreeConditions($root, $options, $rootId);
        if (!$treeConditions) {
            return false;
        }
        $options['conditions'] = array_merge($treeConditions, DB::nestConditions((array)$options['conditions']));
        return $this->update($data, $options);
    }
    
    /**
     * Retrieves parent ids from PWP tree path
     * 
     * @param string $path Path string used in PWP trees to build up the tree structure
     * 
     * @return array List of parent ids. Ids are in the same order as they are ordered 
     *      in path, means from top to nested levels.
     */
    static public function getParentIdsFromTreePath($path) {
        return Str::explode('-', trim($path, '-'));
    }
    
    /**
     * Generates 'names_path' item for all provided records.
     * The 'names_path'  itself is an array like array('Top Category', 'Second Level', ..., 'Target Category'). 
     * 
     * 
     * @param array $records Records array (retrieved e.g. by Model::find())
     *       where each record array contains at least 'id', 'parent_id', 'name' items.
     * @param array $options Optional. Following are available:
     *      - 'rootName' (string) Root name used as top level name for all created names_path-s. 
     *          Defaults to NULL, means no root name is used.
     * 
     * @return array Updated categories containing 'names_path' item
     */
    public static function generateNamesPath($records, $options = array()) {
        $defaults = array(
            'rootName' => null,
        );
        $options = array_merge($defaults, $options);
        
        // set categorie ids for keys
        $tmp = array();
        foreach ($records as $i => $record) {
            $tmp[$record[$this->primaryKey]] = $record;
            unset($records[$i]);
        }
        $records = $tmp;
        unset($tmp);
        
        // set paths
        if (empty($options['rootName'])) {
            $defaultPath = array();
        }
        else {
            $defaultPath = array($options['rootName']);
        }
        foreach ($records as &$record) {
            $path = $defaultPath;
            $parentId = $record['parent_id'];
            while (
                !empty($parentId)
                && !empty($records[$parentId])
            ) {
                array_unshift($path, $records[$parentId]['name']);
                $parentId = $records[$parentId]['parent_id'];
            }
            array_unshift($path, $record['name']);
            $record['names_path'] = array_reverse($path);
        }
        
        return $records;
    } 
    
    /**
     * Recovers tree with broken weights. Weights must be ok at least on the same level between 
     * siblings.
     * 
     * Params have only internal use in recursion
     */
    public function recoverTree(/* internal use: */$nodes = null, $parentId = null, $lastWeight = null) {
        if ($nodes === null) {
            $roots = $this->find(array(
                'fields' => array($this->primaryKey),
                'conditions' => array('path' => '-'),
            ));
            $lastWeight = 0;
            foreach ($roots as $root) {
                $lastWeight++;
                $rootId = $root[$this->primaryKey];
                // set the root weight
                $this->update(
                    array('sort' => $lastWeight),
                    array('conditions' => array($this->primaryKey => $rootId))
                );
                $result = $this->findInTree(
                    $rootId, 
                    array(
                        'fields' => array($this->primaryKey, 'name'),
                        'order' => array(
                            $this->name . '.path ASC',
                            $this->name . '.sort ASC',
                        ),
                    ),
                    $returnRootId
                );
                $nodes = $this->buildTree($result, $rootId, $returnRootId);
                $lastWeight = $this->recoverTree($nodes, $rootId, $lastWeight);
            }
            return true;
        }
        else {
            $childs = $nodes[$parentId];
            foreach ($childs as $childId => $childData) {
                $lastWeight++;
                $this->update(
                    array('sort' => $lastWeight),
                    array('conditions' => array($this->primaryKey => $childId))
                );
                if (isset($nodes[$childId])) {
                    $lastWeight = $this->recoverTree($nodes, $childId, $lastWeight);
                }
            }
            return $lastWeight;
        }
    }
    
    /**
     * ORDERED GROUP METHODS
     */
        
    /**
     * Adds a new item into the ordered group. New item data can be optionally specified.
     * 
     * @param array $data Data of new created item.
     * @param array $options Model::save() options plus following:
     *      - 'groupConditions' (array) Conditions to specify ordered group items. All must
     *      be provided in form '{field}' => '{value}'. Condition like 'my_field = 2'
     *      is invalid. Correct form is 'my_field' => 2. Orderer group can be selected 
     *      only by inclusive conditions (=, IN, IS) and any form of inclusive 
     *      condition can be written as required. If no conditions are specified 
     *      (NULL or empty array()) then all the table items are considered to be 
     *      in one group. Defaults to NULL.
     * 
     * @return array|bool Array of created record data containing id of created record
     *      and other values after normalization and/or file uploads. FALSE if validation fails.  
     * 
     * @throws Exception if 'groupConditions' are not provided in required form
     */
    public function addOrderedItem($data, $options = array()) {
        $defaults = array(
            'groupConditions' => null,
        );
        $options = array_merge($defaults, $options);
        // normalize group conditions
        $groupConditions = (array)$options['groupConditions'];
        // start transaction
        DB::startTransaction('Model_addOrderedItem');
        try {
            // find the sort weight of last item in ordered group
            $lastItem = DB::select($this->table, array(
                'fields' => array('sort'),
                'conditions' => $groupConditions,
                'order' => array('sort DESC'),
                'first' => true,
            ));
            // set the sort, path and parent id of the new node
            if ($lastItem) {
                $data['sort'] = $lastItem['sort'] + 1;
            }
            else {
                $data['sort'] = 0;
            }  
            // force values of group fields in $data according provided $groupConditions 
            // (but only for fields which are not already defined explicitly in $data)
            foreach ($groupConditions as $fieldName => $fieldValue) {
                if (is_int($fieldName)) {
                    throw new Exception("Please use notation 'field' => 'value' for \$groupConditions. Invalid item '{$fieldName}' => '{$fieldValue}'");
                }
                $fieldName = DB::getName($fieldName);
                if (!array_key_exists($fieldName, $data)) {
                    $data[$fieldName] = $fieldValue;
                }
            }
            // save the new item (force the creation)
            $options['create'] = true;
            if (!$result = $this->save($data, $options)) {
                DB::rollbackTransaction('Model_addOrderedItem');
                return false;
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('Model_addOrderedItem');
            throw $e;
        }
        // commit transaction
        DB::commitTransaction('Model_addOrderedItem');
        return $result;
    }
    
    /**
     * Deletes specified ordered group item
     * 
     * @param int $id Id of the item to be removed.
     * @param array $options Model::delete() options plus following:
     *      - 'groupConditions' (array) Conditions to specify ordered group items. 
     *          If no conditions are specified (NULL or empty array()) then all 
     *          the table items are considered to be in one group. Defaults to NULL.
     * 
     * @return boolean TRUE on success, FALSE if the specified item does not exist.
     */
    public function deleteOrderedItem($id, $options = array()) {
        $defaults = array(
            'groupConditions' => null,
            'softDelete' => true,
        );
        $options = array_merge($defaults, $options);
        // normalize group conditions
        $groupConditions = (array)$options['groupConditions'];        
        // start transaction
        DB::startTransaction('Model_deleteOrderedItem');
        try {
            // get sort weight of removed item
            // and check if node with given id exists
            $item = DB::select($this->table, array(
                'fields' => array('sort'),
                'conditions' => array($this->primaryKey => $id),
                'first' => true,
            ));
            if (empty($item)) {
                DB::rollbackTransaction('Model_deleteOrderedItem');
                return false;
            }
            // delete the item (force conditions to delete the specified item)
            $options['conditions'] = array($this->name . '.' . $this->primaryKey => $id);
            $this->delete($options);
            // if real delete (not soft delete) is done then actualize sort weights
            if (empty($this->allowsSoftDelete) || empty($options['softDelete'])) {
                // decreas sort weights of items placed after removed item
                $conditions = DB::nestConditions($groupConditions);
                $conditions[] = array('sort >' => $item['sort']);
                DB::update(
                    $this->table, 
                    array('sort' => '`sort` - 1'),
                    array(
                        'conditions' => $conditions,
                        'literals' => array('fields' => true),
                    )
                );
            }
        }
        catch (Throwable $e) {
            DB::rollbackTransaction('Model_deleteOrderedItem');
            throw $e;
        }
        // commit transaction
        DB::commitTransaction('Model_deleteOrderedItem');
        return true;
    }
    
    /**
     * Moves the specified item of ordered group on specified order index.
     * 
     * @param int $id Id of the item to be moved.
     * @param array $options Following are available:
     *      - 'groupConditions' (array) Conditions to specify ordered group items.
     *          If no conditions are specified (NULL or empty array()) then all 
     *          the table items are considered to be in one group. Defaults to NULL.
     *      - 'newOrderIndex' (int) New order index to move the specified 
     *          item on. It is an integer value starting from 0 (0 to place it as the first in group).
     *          If NULL then the moved item is placed as the last one in group.
     *          If 'softDeletedIncluded' is TRUE then also soft deleted records
     *          must be included in 'newOrderIndex'. Defaults to NULL.  
     *      - 'softDeletedIncluded' (bool) Are soft deleted records included in
     *          'newOrderIndex'? Defaults to FALSE.  
     * 
     * @return boolean TRUE on success. FALSE if the moved or new parent node does
     *      not exist or if the node tries to be moved under one of its childs
     */
    public function moveOrderedItem($id, $options = array()) {
        $defaults = array(
            'groupConditions' => null,
            'newOrderIndex' => null,
            'softDeletedIncluded' => false,
        );
        $options = array_merge($defaults, $options);
        $groupConditions = $options['groupConditions'];
        $newOrderIndex = $options['newOrderIndex'];
        $softDeletedIncluded = $options['softDeletedIncluded'];
        // normalize group conditions
        $groupConditions = (array)$groupConditions;
        // start transaction
        DB::startTransaction('Model_moveOrderedItem');
        // check if moved node with given id exists
        // and find its old sort weight and path
        $item = DB::select($this->table, array(
            'fields' => array('sort'),
            'conditions' => array($this->primaryKey => $id),
            'first' => true,
        ));
        if (empty($item)) {
            DB::rollbackTransaction('Model_moveOrderedItem');
            return false;
        }
        $oldOrderIndex = $item['sort'];
        // if soft delete is allowed but soft deleted records are not 
        // included in newOrderIndex then make correction of newOrderIndex
        // by the amount of previous soft deleted siblings
        if (
            $newOrderIndex !== null
            && $this->allowsSoftDelete 
            && !$softDeletedIncluded
        ) {
            $groupConditions = DB::nestConditions($groupConditions);
            $groupConditions[$this->primaryKey . ' !='] = $id;
            $groupItems = DB::select($this->table, array(
                'conditions' => $groupConditions,
                'fields' => array($this->deletedField),
                'order' => array('sort ASC'),
            ));
            foreach ($groupItems as $i => $groupItem) {
                if (empty($groupItem[$this->deletedField])) {
                    $newOrderIndex -= 1;
                }
                if ($newOrderIndex === -1) {
                    break;
                }
            }
            $newOrderIndex = $i + $newOrderIndex + 1;
        }
        if (
            $newOrderIndex === null
            || $newOrderIndex > $oldOrderIndex
        ) {
            // find last item weight
            $lastItem = DB::select($this->table, array(
                'fields' => array('MAX(sort) AS sort'),
                'conditions' => $groupConditions,
                'literals' => array('fields' => true),
                'first' => true,
            ));
            if (
                $newOrderIndex === null
                || $lastItem['sort'] < $newOrderIndex
            ) {
                $newOrderIndex = $lastItem['sort'];
            }
        }
        elseif ($newOrderIndex < 0) {
            $newOrderIndex = 0;
        }
        
        // if item changes its sort weight
        if ($newOrderIndex != $oldOrderIndex) {
            // if moving item towards the beginning of ordered list
            if ($newOrderIndex < $oldOrderIndex) {
                // increase sort weight of pushed items (placed between new sort and old sort of moved item)
                $pushedDiffOperator = '+';
                $pushedTopWeight = $oldOrderIndex - 1;
                $pushedBottomWeight = $newOrderIndex;
            } 
            // if moving node towards the end of tree
            else {
                // decrease sort weight of pushed items (placed between new sort and old sort of moved item)
                $pushedDiffOperator = '-';
                $pushedTopWeight = $newOrderIndex;
                $pushedBottomWeight = $oldOrderIndex + 1;
            }
            // update sort weights of pushed items (placed between new sort and old sort of moved item)
            $conditions = DB::nestConditions($groupConditions);
            $conditions[] = array(
                'sort >=' => $pushedBottomWeight,
                'sort <=' => $pushedTopWeight,
            );
            DB::update(
                $this->table, 
                array('sort' => '`sort` ' . $pushedDiffOperator . ' 1'),
                array(
                    'conditions' => $conditions,
                    'literals' => array('fields' => true),
                )
            );
            // update sort weights of moved item
            DB::update(
                $this->table, 
                array('sort' => $newOrderIndex),
                array('conditions' => array($this->primaryKey => $id))
            );            
        }
        // commit transaction
        DB::commitTransaction('Model_moveOrderedItem');
        return true;
    }
     
    /**
     *  TRANSLATION METHODS
     */
    
    /**
     * Resolves translation lang according $options['lang'], $data['lang'] or $default.
     * 
     * @param array $options Options passed to find/save/update/delete methods
     * @param array $data Optional. Data passed to save/update methods
     * @param string $default Optional. Default lang returned if both $options and
     *      $data have no lang defined. Defaults to App::$lang value.
     * 
     * @return string Lang code
     */
    public function getTranslationLang($options, $data = array(), $default = null) {
        $default = $default ? $default : App::$lang;
        if (!empty($options['lang'])) {
            return $options['lang'];
        }
        elseif (!empty($data['lang'])) {
            return $data['lang'];
        }
        else {
            return $default;
        }
    }    
    
    /**
     * Return translation field name for given field and lang
     * 
     * This method does not check if the provided field is defined as translated or
     * not. For this use method Model::resolveTranslationField().
     * 
     * @param string $field Field name to get translation field name for
     * @param string $lang Optional. Language to be used for translation field name. 
     *      Default to App::$lang (actual request lang).
     * @return string Translation field name
     */
    public static function getTranslationField($field, $lang = null) {
        $lang = $lang ? $lang : App::$lang;
        if ($lang === DEFAULT_LANG) {
            return $field;
        }
        return '_' . $field . '_' . $lang;
    }
    
    /**
     * Resolves the translation field name, e.g. 'description' is resolved to 
     * 'products_i18ns.description_en'. 
     * 
     * The language code suffix is added to field name and the translation field name
     * is qualified by translation table name. If the translation table name is 
     * the same as table name then the translation field is not qualified 
     * by translation table name.
     * 
     * If the field is not in $this->translatedFields then it is returned unchanged.
     * 
     * This method is supposed to provide you inline translations of fields 
     * in too complex queries.
     * 
     * @param string $field Field name to resolve translation field name for
     * @param string $lang Optional. Language to be used for translation field name. 
     *      Default to App::$lang (actual request lang).
     * 
     * @return string Resolved translation field name
     */
    public function resolveTranslationField($field, $lang = null) {
        $lang = $lang ? $lang : App::$lang;
        if (
            $this->translationLangs['default'] != $lang
            && in_array($field, $this->translatedFields)
        ) {
            $field = self::getTranslationField($field, $lang);
            if ($this->translationTable != $this->table) {
                $field = $this->translationTable . '.' . $field;
            }
        }
        return $field; 
    }
    
    /**
     * Returns translations queary for given query. Means that all translated 
     * fields are replaced by fields keeping the required lang version. In addition
     * these fields are aliased in such a way that translated content is retrieved
     * under original names of fields.
     * 
     * @param string|array $query Query to create translation query for.
     * @param string $lang Lang to create translation query for.
     * @param array $options Following are available:
     *      - 'alias' (string) Explicit alias. If not provided or empty then actual 
     *          model name is used. Defaults to NULL.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     *      - 'module' (string) Module name. If not provided or empty then actual 
     *          model module is used. Applied only if option 'moduleAlias' is set. 
     *          Defaults to NULL.
     * @param array& $joins Optional. Passed by reference. Array of joins. Use here 
     *          the 'joins' option of Model::find() method. If possible then provide 
     *          them already normalized for sake of optimization. If not normalized 
     *          then they must be defined by 'model' (not by 'table') Defaults to NULL, 
     *          means joins are not considered.
     * 
     * @return string|array Translation query
     */
    protected function getTranslationQuery($query, $lang, $options = array(), &$joins = array()) {
        $replacements = $this->getTranslationReplacements($lang, $options, $joins);
        if ($replacements) {
            // treat query passed in form of array
            if (is_array($query)) {
                // check for select clause
                if (!empty($query['select'])) {
                    // if there are no brace parts in select
                    if (strpos($query['select'], '(') === false) {
                        $query['select'] = str_replace(
                            $replacements['select']['search'],
                            $replacements['select']['replace'],
                            $query['select']
                        );
                    }
                    // if there are brace parts in select then treat code closed in braces separatelly
                    else {
                        $braceParts = explode('(', $query['select']);
                        $level = 0;
                        foreach ($braceParts as &$part) {
                            $level++;
                            if (($closedCount = substr_count($part, ')'))) {
                                $level -= $closedCount;
                            }
                            if ($level === 1) {
                                if ($closedCount !== 0) {
                                    $subparts = explode(')', $part);
                                    $lastSubpart = array_pop($subparts);
                                    $part = implode(')', $subparts);
                                    $part = str_replace(
                                        $replacements['others']['search'],
                                        $replacements['others']['replace'],
                                        $part
                                    );
                                    $lastSubpart = str_replace(
                                        $replacements['select']['search'],
                                        $replacements['select']['replace'],
                                        $lastSubpart
                                    );
                                    $part .= ')' . $lastSubpart;
                                }
                                else {
                                    $part = str_replace(
                                        $replacements['select']['search'],
                                        $replacements['select']['replace'],
                                        $part
                                    );
                                }
                            }
                            else {
                                $part = str_replace(
                                    $replacements['others']['search'],
                                    $replacements['others']['replace'],
                                    $part
                                );
                            }
                        }
                        $query['select'] = implode('(', $braceParts);
                    }
                }
                if (!empty($query['set'])) {
                    $query['set'] = str_replace(
                        $replacements['others']['search'],
                        $replacements['others']['replace'],
                        $query['set']
                    );
                }
                if (!empty($query['where'])) {
                    $query['where'] = str_replace(
                        $replacements['others']['search'],
                        $replacements['others']['replace'],
                        $query['where']
                    );
                }
                if (!empty($query['group'])) {
                    $query['group'] = str_replace(
                        $replacements['others']['search'],
                        $replacements['others']['replace'],
                        $query['group']
                    );
                }
                if (!empty($query['order'])) {
                    $query['order'] = str_replace(
                        $replacements['others']['search'],
                        $replacements['others']['replace'],
                        $query['order']
                    );
                }
            }
            // treat string query
            else {
                $query = str_replace(
                    $replacements['others']['search'],
                    $replacements['others']['replace'],
                    $query
                );
            }
        }
        return $query;
    }
    
    /**
     * Returns traslation replacements array for specified lang and joins 
     * 
     * @param string $lang Lang to create the translation joins for
     * @param array $options Following are available:
     *      - 'alias' (string) Explicit alias. If not provided or empty then actual 
     *          model name is used. Defaults to NULL.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     *      - 'module' (string) Module name. If not provided or empty then actual 
     *          model module is used. Applied only if option 'moduleAlias' is set. 
     *          Defaults to NULL.
     * @param array& $joins Optional. Passed by reference. Array of joins. Use here 
     *          the 'joins' option of Model::find() method. If possible then provide 
     *          them already normalized for sake of optimization. If not normalized 
     *          then they must be defined by 'model' (not by 'table') Defaults to NULL, 
     *          means joins are not considered.
     * 
     * @return array Array of translation replacements to replace translated fields
     *      names by fields keeping required lang version.
     */
    protected function getTranslationReplacements($lang, $options = array(), &$joins = array()) {
        $defaults = array(
            'moduleAlias' => false,
            'alias' => null,
            'module' => null,
        );
        $options = array_merge($defaults, $options);
        $replacements = array();
        if (
            $this->translatedFields
            && $this->translationLangs['default'] != $lang
        ) {
            if (empty($this->translationReplacements[$lang])) {
                $enc = DB::getPropertyNameEnclosure();
                $alias = $this->getAlias($options);
                $searchQualifier = $enc . $alias . $enc;
                if ($this->translationTable == $this->table) {
                    $replaceQualifier = $searchQualifier;
                }
                else {
                    $replaceQualifier  = $enc . $this->translationTable . $enc;
                }
                $this->translationReplacements[$lang] = array(
                    'select' => array(
                        'search' => array(),
                        'replace' => array(),
                    ),
                    'others' => array(
                        'search' => array(),
                        'replace' => array(),
                    ),
                );
                foreach ($this->translatedFields as $field) {
                    $translationField = self::getTranslationField($field, $lang);
                    
                    // create aliased replacement for select clause (the order is important)
                    $this->translationReplacements[$lang]['select']['search'][] = 
                        $searchQualifier . '.' . $enc . $field . $enc . ' AS ';
                    $this->translationReplacements[$lang]['select']['replace'][] = 
                        $replaceQualifier . '.' . $enc . $translationField . $enc . ' AS ';
                    
                    $this->translationReplacements[$lang]['select']['search'][] = 
                        $searchQualifier . '.' . $enc . $field . $enc . ' as ';
                    $this->translationReplacements[$lang]['select']['replace'][] = 
                        $replaceQualifier . '.' . $enc . $translationField . $enc . ' AS ';
                    
                    $this->translationReplacements[$lang]['select']['search'][] = 
                        $searchQualifier . '.' . $enc . $field . $enc;
                    $this->translationReplacements[$lang]['select']['replace'][] = 
                        $replaceQualifier . '.' . $enc . $translationField . $enc . ' AS ' . $enc . $field . $enc;
                    
                     // create not aliased replacement for general 
                    $this->translationReplacements[$lang]['others']['search'][] = 
                        $searchQualifier . '.' . $enc . $field . $enc;
                    $this->translationReplacements[$lang]['others']['replace'][] = 
                        $replaceQualifier . '.' . $enc . $translationField . $enc;
                }
            }
            $replacements = $this->translationReplacements[$lang];
        }
        // retrieve replacements of joined models
        if ($joins) {
            // normalize joins
            $joins = $this->normalizeJoins($joins, $options);
            foreach ($joins as $join) {
                $modelReplacements = $join['Model']->getTranslationReplacements($lang, array(
                    'moduleAlias' => false, // $join['alias'] is already moduleAlias-ed
                    'alias' => $join['alias'],
                ));
                $replacements = array_merge_recursive($replacements, $modelReplacements);
            }
        }
        return $replacements;
    }
    
    /**
     * Returns translation lang virtual fields of actual and joined models.
     * 
     * @param string $lang Lang to create the translation joins for
     * @param array $options Following are available:
     *      - 'qualify' (bool) If TRUE the field names are qualified by provided 'alias'.
     *          Defaults to FALSE.
     *      - 'alias' (string) Explicit alias. If not provided or empty then actual 
     *          model name is used. Defaults to NULL.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     *      - 'module' (string) Module name. If not provided or empty then actual 
     *          model module is used. Applied only if option 'moduleAlias' is set. 
     *          Defaults to NULL.
     * @param array& $joins Optional. Passed by reference. Array of joins. Use here 
     *          the 'joins' option of Model::find() method. If possible then provide 
     *          them already normalized for sake of optimization. If not normalized 
     *          then they must be defined by 'model' (not by 'table') Defaults to NULL, 
     *          means joins are not considered.
     * 
     * @return array Array of translation lang virtual fields to be merged into find fields.
     */
    protected function getTranslationLangFields($lang, $options = array(), &$joins = array()) {
        $defaults = array(
            'qualify' => false,
            'moduleAlias' => false,
            'alias' => null,
            'module' => null,
        );
        $options = array_merge($defaults, $options);
        $langFields = array();
        if (
            $this->translatedFields
            && $this->translationLangs['default'] != $lang
        ) {
            $nEnc = DB::getPropertyNameEnclosure();
            $vEnc = DB::getPropertyValueEnclosure();
            if ($options['qualify']) {
                $alias = $this->getAlias($options);
                $langFields[] = $vEnc . $lang . $vEnc . ' AS ' . $nEnc . $alias . '.lang' . $nEnc;
            }
            else {
                $langFields[] = $vEnc . $lang . $vEnc . ' AS ' . $nEnc . 'lang' . $nEnc;
            }
        }
        if ($joins) {
            // normalize joins
            $joins = $this->normalizeJoins($joins, $options);
            foreach ($joins as $join) {
                $modelLangFields = $join['Model']->getTranslationLangFields($lang, array(
                    'qualify' => true,
                    'moduleAlias' => false, // $join['alias'] is already moduleAlias-ed
                    'alias' => $join['alias'],
                ));
                $langFields = array_merge($langFields, $modelLangFields);
            }
        }
        return $langFields;
    }
    
    /**
     * Returns translation joins in find/save/update/delete options
     * 
     * @param string $lang Lang to create translation joins for.
     * @param array $options Following are available:
     *      - 'alias' (string) Explicit alias. If not provided or empty then actual 
     *          model name is used. Defaults to NULL.
     *      - 'moduleAlias' (bool|array) If TRUE then alias (normally set to 
     *          model name) is moreover prefixed by module name, e.g. 'App.User'.
     *          If array of module names then only provided modules models aliases
     *          are prefixed by module name. Defaults to FALSE. NOTE: This option
     *          is necesarry to be used (only) in case of filtering different
     *          modules models in the same index. Defaults to FALSE.
     *      - 'module' (string) Module name. If not provided or empty then actual 
     *          model module is used. Applied only if option 'moduleAlias' is set. 
     *          Defaults to NULL.
     * @param array& $joins Optional. Passed by reference. Array of joins. Use here 
     *          the 'joins' option of Model::find() method. If possible then provide 
     *          them already normalized for sake of optimization. If not normalized 
     *          then they must be defined by 'model' (not by 'table') Defaults to NULL, 
     *          means joins are not considered.
     * 
     * @return array Array of joins with translation table(s). If there are $joins
     *      passed then translation joins contain all neccessary translation tables.
     */
    protected function getTranslationJoins($lang, $options = array(), &$joins = array()) {
        $defaults = array(
            'moduleAlias' => false,
            'alias' => null,
            'module' => null,
            /* internal use */
            'delete' => true,
        );
        $options = array_merge($defaults, $options);
        $translationJoins = array();
        if (
            $this->translatedFields
            && $this->translationLangs['default'] != $lang
            && $this->translationTable != $this->table
        ) {
            $foreignKey = $this->getForeignKey();
            $alias = $this->getAlias($options);
            $translationJoins = array(
                array(
                    'table' => $this->translationTable,
                    'type' => 'left',
                    'conditions' => array(
                        $alias . '.' . $this->primaryKey . ' = ' . $this->translationTable . '.' . $foreignKey,
                    ),
                    // this applies only for delete() and deleteAll()
                    'delete' => (bool)$options['delete'], 
                ),
            );
        }
        if ($joins) {
            // normalize joins
            $joins = $this->normalizeJoins($joins, $options);
            foreach ($joins as $join) {
                $modelJoins = $join['Model']->getTranslationJoins($lang, array(
                    'delete' => (bool)Sanitize::value($join['delete']),
                    'moduleAlias' => false, // $join['alias'] is already moduleAlias-ed
                    'alias' => $join['alias'],
                ));
                $translationJoins = array_merge($translationJoins, $modelJoins);
            }
        }
        return $translationJoins;
    }
                
    /**
     * Returns translation data and updates $data and $options accordingly
     * 
     * @param array& $data Data passed to save/update methods. Passed by reference
     *      and modified by method!
     * @param array& $options Options passed to save/update methods. Passed by reference
     *      and modified by method!
     * @param string $lang Lang to create translation date for.
     * 
     * @return array Data of translated fields stored under fields keeping 
     *      required lang version. Original fields data are unset from $data input array.
     *      In $options input the 'allowFields' option is changed according fields replaced
     *      from original data to translation data. 
     */
    protected function getTranslationData(&$data, &$options, $lang) {
        $translationData = array();
        if ($this->translationLangs['default'] != $lang) {
            $fields = DB::getDataFields($data, $options);
            $translationFields = array();
            $unsetFields = array();
            foreach($fields as $field) {
                if (in_array($field, $this->translatedFields)) {
                    $translationField = self::getTranslationField($field, $lang);
                    $translationData[$translationField] = $data[$field];
                    unset($data[$field]);
                    $translationFields[] = $translationField;
                    $unsetFields[] = $field;
                }
            }
            if (!empty($options['allowFields'])) {
                $options['allowFields'] = array_diff((array)$options['allowFields'], $unsetFields);
                $options['allowFields'] = array_merge($options['allowFields'], $translationFields);
            }
        }
        return $translationData;
    }
    
    /**
     * FILE METHODS
     */
    
    /**
     * @deprecated (160421) Use Model::getFileFieldOptions() instead. Keep it till
     *      file stores are not converted on projects using core_v1 (Musictech/La-butik/
     *      Fatraski/Autolekar/Ideamade/???...). For conversion use /_debug/convertFileStores     
     */
    protected $fileStoreDeprecated = null;
        
    /**
     * @deprecated (160421) Use Model::getFileFieldOptions() instead. Keep it till
     *      file stores are not converted on projects using core_v1 (Musictech/La-butik/
     *      Fatraski/Autolekar/Ideamade/???...). For conversion use /_debug/convertFileStores     
     */
    protected $fileFieldOptionsDeprecated = array();
        
    /**
     * Gets file field options compiling data in Model::$fileFields.
     * 
     * The field store path is prefixed by Model::$fileStore path. Variant store 
     * paths are prefixed by field store path. Field options are used as defaults
     * for variants. All paths are app root relative and without leading and trailing slash.
     * 
     * @param string $field Optional. File field name. If not specified (NULL) then
     *      options of all file fields are returned
     * 
     * @return bool|array FALSE if the provided field is not defined as file field
     * in Model::$fileFields. Otherwise an array of resulting options. On invalid
     * definition of fileField option 'name' an exception is thrown.
     * 
     * @deprecated (160421) Use Model::getFileFieldOptions() instead. Keep it till
     *      file stores are not converted on projects using core_v1 (Musictech/La-butik/
     *      Fatraski/Autolekar/Ideamade/???...). For conversion use /_debug/convertFileStores
     */
    public function getFileFieldOptionsDeprecated($field = null) {
        // if no field specified then return all 
        if ($field === null) {
            foreach ($this->fileFields as $fileField => $options) {
                $this->getFileFieldOptionsDeprecated($fileField);
            }
            return $this->fileFieldOptionsDeprecated;
        }
        if (!isset($this->fileFieldOptionsDeprecated[$field])) {
            $defaults = array(
                'path' => null,
                'name' => null,
//                'unique' => true, // not implemented
//                'delete' => true, // not implemented
                'extension' => null, 
                'quality' => null,  
                'placeholder' => null,  
                'variants' => array()
            );    
            // if not a file field then return FALSE
            if (!array_key_exists($field, $this->fileFields)) {
                return false;
            }
            // merge field options with defaults
            $options = array_merge($defaults, (array)$this->fileFields[$field]);
            if (
                !empty($options['name'])
                && !Validate::callableFunction($options['name'])
            ) {
                throw new Exception(__e(__FILE__, "Invalid value of '%s' fileField option 'name'. Provide please callable or anonymous function.", $field));
            }
            // resolve final path and variant paths (both app root relative).
            $options['path'] = trim(trim($this->fileStoreDeprecated, DS) . DS . trim($options['path'], DS), DS);
            $options['path'] = str_replace('/', DS, $options['path']);
            $options['urlPath'] = URL_ROOT . '/' . str_replace(DS, '/', $options['path']);
            // apply field options as defaults to variants.
            foreach ($options['variants'] as &$variant) {
                if (!empty($variant['path'])) {
                    $variant['path'] = $options['path'] . DS . str_replace('/', DS, trim($variant['path'], DS));
                    $variant['urlPath'] = URL_ROOT . '/' . str_replace(DS, '/', $variant['path']);
                }
                else {
                    $variant['path'] = $options['path'];
                    $variant['urlPath'] = $options['urlPath'];
                }
                $variant = array_merge($options, $variant);
                // 'name' option can be defined only on top level because all variants 
                // must have the same name.
                unset($variant['name']);
                // this options must be same for all variants and are inherited from options
//                $variant['delete'] = $options['delete']; // not implemented
//                $variant['unique'] = $options['unique']; // not implemented
                unset($variant['variants']);
            }
            unset($variant); // reference
            // if no variants are defined then store the field options as '' variant
            if (empty($options['variants'])) {
                $options['variants'][''] = $options;
                unset($options['variants']['']['variants']);
            }
            $this->fileFieldOptionsDeprecated[$field] = $options;
        }
        return $this->fileFieldOptionsDeprecated[$field];
    }
    
    /**
     * Gets file field options compiling data in Model::$fileFields.
     * 
     * The 'path' option is set implicitly for each variant like {Model::$fileStoreRoot}/{module}/{model}/{field}[/{variant}].
     * Variant is not present if field has no variants defined. All paths are app 
     * root relative and without leading and trailing slash. All other field options 
     * are used as defaults for variants.
     * 
     * @param string $field Optional. File field name. If not specified (NULL) then
     *      options of all file fields are returned
     * 
     * @return bool|array FALSE if the provided field is not defined as file field
     * in Model::$fileFields. Otherwise an array of resulting options. On invalid
     * definition of fileField option 'name' an exception is thrown.
     */
    public function getFileFieldOptions($field = null) {
        // if no field specified then return all 
        if ($field === null) {
            foreach ($this->fileFields as $fileField => $options) {
                $this->getFileFieldOptions($fileField);
            }
            return $this->fileFieldOptions;
        }
        if (!isset($this->fileFieldOptions[$field])) {
            $defaults = array(
                'name' => null,
//                'unique' => true, // not implemented
//                'delete' => true, // not implemented
                'extension' => null, 
                'quality' => null,  
                'placeholder' => null,  
                'lazy' => false,
                'source' => false,
                'variants' => array(),
            );    
            // if not a file field then return FALSE
            if (!array_key_exists($field, $this->fileFields)) {
                return false;
            }
            // merge field options with defaults
            $options = array_merge($defaults, (array)$this->fileFields[$field]);
            if (
                !empty($options['name'])
                && !Validate::callableFunction($options['name'])
            ) {
                throw new Exception(__e(__FILE__, "Invalid value of '%s' fileField option 'name'. Provide please callable or anonymous function.", $field));
            }
            // resolve final path and variant paths (both app root relative).
            $options['path'] = trim($this->fileStore, DS) . DS . $field;
            $options['path'] = str_replace('/', DS, $options['path']);
            $options['urlPath'] = URL_ROOT . '/' . str_replace(DS, '/', $options['path']);
            $lazyVariants = array();
            $sourceVariants = array();
            // apply field options as defaults to variants.
            foreach ($options['variants'] as $variantName => &$variantOptions) {
                if ($variantName !== '') {
                    $variantOptions['path'] = $options['path'] . DS . $variantName;
                    $variantOptions['urlPath'] = $options['urlPath'] . '/' . $variantName;
                }
                else {
                    $variantOptions['path'] = $options['path'];
                    $variantOptions['urlPath'] = $options['urlPath'];                    
                }
                $variantOptions = array_merge($options, $variantOptions);
                if ($variantOptions['lazy']) {
                    $lazyVariants[] = $variantName;
                }
                if ($variantOptions['source']) {
                    $sourceVariants[] = $variantName;
                }
                if (!empty($variantOptions['placeholder'])) {
                    // check if variant placeholder exists as provided
                    if (is_readable(
                        ROOT . DS . $variantOptions['path'] . DS . $variantOptions['placeholder']
                    )) {
                        // do nothing, use placeholder as provided
                    }
                    // check if placeholder exists with 'extension' of variant
                    elseif (
                        $variantOptions['extension']
                        && ($placeholderParts = File::getPathinfo($variantOptions['placeholder']))
                        && $placeholderParts['extension'] !== $variantOptions['extension']
                        && ($placeholder = $placeholderParts['filename'] . '.' . $variantOptions['extension'])
                        && is_readable(
                            ROOT . DS . $variantOptions['path'] . DS . $placeholder
                        )
                    ) {
                        $variantOptions['placeholder'] = $placeholder;
                    }
                    else {
                        $variantOptions['placeholder'] = null;
                    }
                }
                // 'name' option can be defined only on top level because all variants 
                // must have the same name.
                unset($variantOptions['name']);
                // this options must be same for all variants and are inherited from options
//                $variantOptions['delete'] = $options['delete']; // not implemented
//                $variantOptions['unique'] = $options['unique']; // not implemented
                unset($variantOptions['variants']);
            }
            unset($variantOptions); // reference
            // if no variants are defined then store the field options as '' (empty string) 
            // variant to facilitate further processing which works mainly with variants
            if (empty($options['variants'])) {
                $options['variants'][''] = $options;
                unset($options['variants']['']['variants']);
                if ($options['lazy']) {
                    $lazyVariants[] = '';
                }
                if ($options['source']) {
                    $sourceVariants[] = '';
                }
            }
            // validate fileFieldOptions
            $error = '';
            if (count($sourceVariants) > 1)  {
                $error .= __e(
                    __FILE__, 
                    "The '%s' file field has many source variants defined: '%s'. There must be a single source variant.",
                    $field,
                    implode("', '", $lazyVariants)
                );
            }
            if (!empty($lazyVariants)) {
                if (empty($sourceVariants)) {
                    $error .= ' ' . __e(
                        __FILE__, 
                        "The '%s' file field has no source variant. There must be a source variant defined as there are lazy variants defined: '%s'.", 
                        $field,
                        implode("', '", $lazyVariants)
                    );
                }
                elseif (
                    ($invalidVariants = array_intersect($lazyVariants, $sourceVariants))
                ) {
                    $error .= ' ' . __e(
                        __FILE__, 
                        "The '%s' file field variants '%s' are both as lazy and source. It is not allowed to define variant to be both lazy and source.",
                        $field,
                        implode("', '", $invalidVariants)
                    );
                }
                if (count($options['variants']) === 1) {
                    $error .= ' ' . __e(
                        __FILE__, 
                        "The '%s' file field single variant cannot be defined as lazy", 
                        $field
                    );
                }
            }
            if (!empty($error)) {
                throw new Exception(trim($error));
            }
            $this->fileFieldOptions[$field] = $options;
        }
        return $this->fileFieldOptions[$field];
    }
    
    /**
     * Gets file field variants from file field options.
     *  
     * @param string $field Name of field
     * @return bool|array FALSE if the provided field is not defined as file field
     * in Model::$fileFields. Otherwise an array of resulting variants.
     */
    public function getFileFieldVariants($field) {
        $options = $this->getFileFieldOptions($field);
        if (!$options) {
            return false;
        }
        return $options['variants'];
    }
    
    /**
     * Gets file field paths from file field options in array like:
     * 
     *      array(
     *          '{variantName01}' => 'path/to/variant/01',
     *          '{variantName02}' => 'path/to/variant/02',
     *          ...
     *      )
     * 
     * or if the 'file' option is provided:
     * 
     *      array(
     *          '{variantName01}' => 'path/to/variant/01/file.jpg',
     *          '{variantName02}' => 'path/to/variant/02/file.jpg',
     *          ...
     *      )
     * 
     * @param string $field Name of file field to get paths for
     * @param array $options Following are available:
     *      - 'file' (string|bool) Value of file field which should be a name 
     *          (filename or basename) of the stored file. File path or URL path can be used too. If provided then paths 
     *          are created including file name. The extension is forced from definition
     *          of variant. If variant has no extension defined then the extension of provided 
     *          filename is used. If provided empty (but not FALSE) then only paths for variants 
     *          with defined placeholder are set. Other variants are set to NULL. 
     *          If not provided or FALSE then only directory paths are returned for variants.
     *          Defaults to FALSE.
     * 
     * @return bool|array FALSE if the provided field is not defined as file field
     *      in Model::$fileFields. Otherwise an array of resulting variant paths.
     *      Paths are app root relative and without leading and trailing slash.
     */
    public function getFileFieldPaths($field, $options = array()) {
        $defaults = array(
            'file' => false,
        );
        $options = array_merge($defaults, $options);
        $fieldOptions = $this->getFileFieldOptions($field);
        if (!$fieldOptions) {
            return false;
        }
        $paths = array();
        if ($options['file'] !== false) {
            if (!empty($options['file'])) {
                $fileParts = File::getPathinfo(File::normalizeDS($options['file']));
                foreach ($fieldOptions['variants'] as $variantName => $variantOptions) {
                    $filepath = $variantOptions['path'] . DS . $fileParts['filename'];
                    if ($variantOptions['extension']) {
                        $filepath .= '.' . $variantOptions['extension'];
                    }
                    elseif (!empty($fileParts['extension'])) {
                        $filepath .= '.' . $fileParts['extension'];
                    }
                    $paths[$variantName] = $filepath;
                }
            }
            else {
                foreach ($fieldOptions['variants'] as $variantName => $variantOptions) {
                    if ($variantOptions['placeholder']) {
                        $paths[$variantName] = $variantOptions['path'] . DS . $variantOptions['placeholder'];
                    }
                    else {
                        $paths[$variantName] = null;
                    }
                }
            }
        }
        else {
            foreach ($fieldOptions['variants'] as $variantName => $variantOptions) {
                $paths[$variantName] = $variantOptions['path'];
            }
        }
        return $paths;
    }
    
    /**
     * Gets path of specified file field variant
     * 
     * @param string $field Name of file field to get path for
     * @param array $options Following are available:
     *      - 'file' (string|bool) Value of file field which should be a name 
     *          (filename or basename) of the stored file. File path or URL path can be used too. If provided then paths 
     *          are created including file name. The extension is forced from definition
     *          of variant. If variant has no extension defined then the extension of provided 
     *          filename is used. If provided empty (but not FALSE) then only paths for variants 
     *          with defined placeholder are set. Other variants are set to NULL. 
     *          If not provided or FALSE then only directory paths are returned for variants.
     *          Defaults to FALSE.
     *      - 'variant' (string) Variant name to get the path for. If not specified 
     *          then the first defined variant path is returned. Defaults to NULL.
     * 
     * @return bool|array FALSE if the provided field is not defined as file field
     *      in Model::$fileFields or if the specified variant does not exist. Otherwise 
     *      variant path. Path is app root relative and without leading and trailing slash.
     */
    public function getFileFieldPath($field, $options = array()) {
        $defaults = array(
            'file' => false,
            'variant' => null,
        );
        $options = array_merge($defaults, $options);
        $paths = $this->getFileFieldPaths($field, array('file' => $options['file']));
        if (empty($options['variant'])) {
            if (empty($paths)) {
                return false;
            }
            return reset($paths);
        }
        return Sanitize::value($paths[$options['variant']], false);
    }
    
    /**
     * Gets url paths of all file field variants in array like if the 'file' option is FALSE:
     *      
     *      array(
     *          '{variantName01}' => '/url/path/to/variant/01',
     *          '{variantName02}' => '/url/path/to/variant/02',
     *          ...
     *      )
     * 
     * If nonempty 'file' option is provided then url paths are created including file name:
     * 
     *      array(
     *          '{variantName01}' => '/url/path/to/variant/01/file.jpg',
     *          '{variantName02}' => '/url/path/to/variant/02/file.jpg',
     *          ...
     *      )
     * 
     * If the 'file' option is empty (but other then FALSE) then only url paths for variants 
     * with defined placeholder are set. Other variants are set to NULL.
     * 
     *      array(
     *          '{variantName01}' => null,
     *          '{variantName02}' => '/url/path/to/variant/02/placeholder.jpg',
     *          ...
     *      )
     * 
     * @param string $field Name of file field to get url paths for
     * @param array $options Following are available:
     *      - 'file' (string|bool) Value of file field which should be a name 
     *          (filename or basename) of the stored file. File path or URL path can be used too. If provided then paths 
     *          are created including file name. The extension is forced from definition
     *          of variant. If variant has no extension defined then the extension of provided 
     *          filename is used. If provided empty (but not FALSE) then only url paths for variants 
     *          with defined placeholder are set. Other variants are set to NULL. 
     *          If not provided or FALSE then only url directory paths are returned for variants.
     *          Defaults to FALSE.
     *      - 'timestamp' (bool) If TRUE then timestamp GET param is added to URL path
     *          If FALSE then URL path is generated without timestamp. Defaults to TRUE.
     * 
     * @return bool|array FALSE if the provided field is not defined as file field
     *      in Model::$fileFields. Otherwise an array of resulting variant url paths.
     *      Paths contain URL_ROOT and are without trailing slash.
     */
    public function getFileFieldUrlPaths($field, $options = array()) {
        $defaults = array(
            'file' => false,
            'timestamp' => true,
        );
        $options = array_merge($defaults, $options);
        $fieldOptions = $this->getFileFieldOptions($field);
        if (!$fieldOptions) {
            return false;
        }
        $urlPaths = array();
        if ($options['file'] !== false) {
            if (!empty($options['file'])) {
                $fileParts = File::getPathinfo(File::normalizeDS($options['file']));
                $timestamp = null;
                foreach ($fieldOptions['variants'] as $variantName => $variantOptions) {
                    $fileUrlPath = $variantOptions['urlPath'] . '/' . $fileParts['filename'];
                    if ($variantOptions['extension']) {
                        $fileUrlPath .= '.' . $variantOptions['extension'];
                    }
                    elseif (!empty($fileParts['extension'])) {
                        $fileUrlPath .= '.' . $fileParts['extension'];
                    }
                    // get timestamp - do it for each variant as they can be created in different
                    // times (source vs lazy variants)
                    //if ($timestamp === null) { // see @todo 200305
                        $filePath = ROOT . DS . $variantOptions['path'] . DS . $fileParts['filename'];
                        if ($variantOptions['extension']) {
                            $filePath .= '.' . $variantOptions['extension'];
                        }
                        elseif (!empty($fileParts['extension'])) {
                            $filePath .= '.' . $fileParts['extension'];
                        }
                        if (empty($options['timestamp'])) {
                            // do nothing
                        }
                        elseif (is_readable($filePath)) {
                            $timestamp = '?ts=' . filemtime($filePath);
                        }
                        else {
                            // get fake timestamp for unexisting (should be lazy) variants
                            $timestamp = '?ts=' . time();
                        }
                    //}
                    $fileUrlPath .= $timestamp;
                    $urlPaths[$variantName] = $fileUrlPath;
                }
            }
            else {
                $timestamp = null;
                foreach ($fieldOptions['variants'] as $variantName => $variantOptions) {
                    if ($variantOptions['placeholder']) {
                        $fileUrlPath = $variantOptions['urlPath'] . '/' . $variantOptions['placeholder'];
                        $filePath = ROOT . DS . $variantOptions['path'] . DS . $variantOptions['placeholder'];
                        if (empty($options['timestamp'])) {
                            // do nothing
                        }
                        elseif (is_readable($filePath)) {
                            $timestamp = '?ts=' . filemtime($filePath);
                        }
                        else {
                            // get fake timestamp for unexisting (should be lazy) variants
                            $timestamp = '?ts=' . time();
                        }
                        $fileUrlPath .= $timestamp;
                        $urlPaths[$variantName] = $fileUrlPath;
                    }
                    else {
                        $urlPaths[$variantName] = null;
                    }
                }
            }
        }
        else {
            foreach ($fieldOptions['variants'] as $variantName => $variantOptions) {
                $urlPaths[$variantName] = $variantOptions['urlPath'];
            }
        }
        return $urlPaths;
    }
    
    /**
     * Gets url path of specified file field variant
     * 
     * @param string $field Name of file field to get url path for
     * @param array $options Following are available:
     *      - 'file' (string|bool) Value of file field which should be a name 
     *          (filename or basename) of the stored file. File path or URL path can be used too. If provided then paths 
     *          are created including file name. The extension is forced from definition
     *          of variant. If variant has no extension defined then the extension of value
     *          is used. If provided empty (but not FALSE) then only url paths for variants 
     *          with defined placeholder are set. Other variants are set to NULL. 
     *          If not provided or FALSE then only url directory paths are returned for variants.
     *          Defaults to FALSE.
     *      - 'timestamp' (bool) If TRUE then timestamp GET param is added to URL path
     *          If FALSE then URL path is generated without timestamp. Defaults to TRUE.
     *      - 'variant' (string) Variant name to get the url path for. If not
     *          specified then the first defined variant path is returned. Defaults to NULL.
     * 
     * @return bool|string|NULL FALSE if the provided field is not defined as file field
     *      in Model::$fileFields or if the specified variant does not exist. Otherwise 
     *      variant url path. Path contains URL_ROOT and is without trailing slash.
     */
    public function getFileFieldUrlPath($field, $options = array()) {
        $defaults = array(
            'file' => false,
            'timestamp' => true,
            'variant' => null,
        );
        $options = array_merge($defaults, $options);
        $urlPaths = $this->getFileFieldUrlPaths($field, array(
            'file' => $options['file'],
            'timestamp' => $options['timestamp'],
        ));
        if (empty($options['variant'])) {
            if (empty($urlPaths)) {
                return false;
            }
            return reset($urlPaths);
        }
        return Sanitize::value($urlPaths[$options['variant']], false);
    }
    
    /**
     * Prepares incoming files according file sources in data file fields.
     * In case that data/options are set for update, then also obsolete files are set. 
     * 
     * @param array $data Save or update data
     * @param array $options Options array passed to Model::save() or update().
     *      Options are used to check if the provided data are going to be inseted/created
     *      or updated. In case of update the updated records are retrieved to set obsolete files.
     * 
     * @return array Data with updated values of file fields. File fields are set 
     *      to incoming file names.
     */
    public function prepareFiles($data, $options = array()) {
        // check if there are file fields in current model
        if (!$this->fileFields) {
            return $data;
        }
        // check if there are file fields in provided data
        $fileFields = array_intersect_key($data, $this->fileFields);
        if (!$fileFields) {
            return $data;
        }
        // transfer and transform files
        foreach ($fileFields as $field => $value) {
            $result = File::transfer($value, $this->fileTmpStore);
            // in case that field $value is not a file source then let it untouched 
            // then save it as it is provided, just prepare obsolete fields here below
            if ($result === File::NOT_SOURCE) {
                // do nothing
            }
            // if the $value is file source but transfer of file has failed then 
            // unset the field in data to avoid overwriting of existing field value
            // and remove it from $fileFields to not put the existing field file into  
            // obsolete files
            elseif ($result === false) {
                unset($data[$field]);
                unset($fileFields[$field]);
            }
            // otherwise process transfered file
            else {
                $tmpFile = $this->fileTmpStore . DS . $result;
                $fileFieldOptions = $this->getFileFieldOptions($field);
                $name = null;
                if (Validate::callableFunction($fileFieldOptions['name'])) {
                    $name = call_user_func_array($fileFieldOptions['name'], array($value, $data));
                }
                if (File::isTransformable($tmpFile)) {   
                    $nonLazyVariants = array();
                    foreach ($fileFieldOptions['variants'] as $variantName => $variantOptions) {
                        if (empty($variantOptions['lazy'])) {
                            $nonLazyVariants[$variantName] = $variantOptions;
                        }
                    }
                    $result = File::transform($tmpFile, $nonLazyVariants, array(
                        'multiple' => true, 
                        'name' => $name,
                    ));
                    if ($result) {
                        // result is without extension so use the original file 
                        // extension as the default extension which will be used 
                        // if variant has no own extension defined
                        $tmpFileParts = pathinfo($tmpFile);
                        $defaultExtension = Sanitize::value($tmpFileParts['extension']);
                        if ($defaultExtension) {
                            $result .= '.' . $defaultExtension;
                        }
                    }
                }
                else {
                    $path = $this->getFileFieldPath($field);
                    // result has extension because it is just copy of original file
                    $result = File::copy(array($tmpFile), $path, array(
                        'name' => $name,
                    ));
                }
                unlink($tmpFile);
                if ($result) {
                    $data[$field] = $result;
                    $this->setIncomingFiles($field, $result);
                }
            }            
        }
        // if updating an existing record then we must find out current filenames
        // and set them as obsolete files (to be deleted after the successful save)
        if (
            // $fileFields are unset here above in some cases so check if there are 
            // still some of them    
            !empty($fileFields) 
            && !$this->isCreation($data, $options)
        ) { 
            $this->setObsoleteFiles(array_keys($fileFields), $options);
        }
        return $data;
    }
    
    /**
     * Creates file for specified $field, $variant and $filename. 
     * 
     * This is used for creation of lazy or missing variant files. 
     * See _getImage screen and Model::$fileFields phpDoc.
     * 
     * @param string $field
     * @param string $variant
     * @param string $filename
     * @param array $options Following are available
     *      - 'force' (bool) If TRUE then variant file is recreated even it exists already.
     *          If FALSE then variant file is created only if it does not exist.
     *          Defaults to TRUE.
     * 
     * @return string|boolean Created variant file app root relative path or FALSE
     *      on failure, it means if provided $field, $variant or $filename is invalid.
     *      An uncatched exceptions can be thrown.
     */
    public function createFileFieldVariantFile($field, $variant, $filename, $options = array()) {
        $defaults = array(
            'force' => true,
        );
        $options = array_merge($defaults, $options);
        if (
            !($fileFieldOptions = $this->getFileFieldOptions($field))
            || empty($fileFieldOptions['variants'][$variant])
        ) {
            return false;
        }
        $variantFile = $this->getFileFieldPath($field, array('file' => $filename, 'variant' =>  $variant));
        // if 'force' is FALSE and variant exists already then just return it
        if (
            empty($options['force']) 
            && is_readable(ROOT . DS . $variantFile)
        ) {
            return $variantFile;
        }
        // create from source variant file
        $sourceVariantFile = null;
        foreach ($fileFieldOptions['variants'] as $variantName => $variantOptions) {
            if (!empty($variantOptions['source'])) {
                $sourceVariantFile = $this->getFileFieldPath($field, array('file' => $filename, 'variant' =>  $variantName));
                break;
            }
        }
        // if variant extension is inherited then it can differ from extension of 
        // $filename. If this is the case then look for original extension in DB
        if (
            !empty($sourceVariantFile)
            && !is_readable(ROOT . DS . $sourceVariantFile)
            && empty($variantOptions['extension'])
        ) {
            $name = File::getPathinfo($filename, PATHINFO_FILENAME);
            // escape LIKE wildcards
            $name = str_replace(array('%', '_'), array('\%', '\_'), $name);
            $filename = $this->findField($field, array(
                'conditions' => array(
                    array($field . ' ~' => $name . '.___'),
                    'OR',
                    array($field . ' ~' => $name . '.____'),
                )
            ));
            if (!empty($filename)) {
                $sourceVariantFile = $this->getFileFieldPath($field, array('file' => $filename, 'variant' =>  $variantName));
            }
            else {
                $sourceVariantFile = null;
            }
        }
        if (
            !empty($sourceVariantFile)
            && is_readable(ROOT . DS . $sourceVariantFile)
        ) {
            File::transform(
                $sourceVariantFile, 
                $fileFieldOptions['variants'][$variant],
                array(
                    'unique' => false,
                )
            );
            return $variantFile;
        }
        return false;
    }
    
    /**
     * Prepares data to create record copy. Following processing is done:
     *      - primary key field, 'created' and 'modified' are removed
     *      - existing files of file fieds are copied so that new record has its
     *      own copy if file
     * 
     * @param array $data Record data to prepare copy for
     * 
     * @return array Record copy data
     * 
     * @throws Exception on file copying failure
     */
    public function prepareCopyData($data) {
        $copyData = array();
        foreach ($data as $field => $value) {
            // skip following fields
            if (
                $field === $this->primaryKey
                || $field === 'created'
                || $field === 'modified'
                || $field === 'deleted'
            ) {
                continue;
            }
            // make copy of notempty file fields ($value is a file name)
            elseif (
                isset($this->fileFields[$field])
                && !empty($value)
            ) {
                // get the unique name for file copy 
                $variants = $this->getFileFieldVariants($field);
                // if creation of file copy unique name has failed then it means that 
                // variant paths do not exist. In such case there is nothing to copy
                // so skip the field
                try {
                    $copyValue = File::getTransformsUniqueName($value, $variants, array(
                        'reserve' => true,
                    ));
                } 
                catch (Throwable $e) {
                    continue;
                }
                // use the original file extension as the default extension 
                // which will be used if variant has no own extension defined
                $valueParts = pathinfo($value);
                $valueExtension = Sanitize::value($valueParts['extension']);
                if ($valueExtension) {
                    $copyValue .= '.' . $valueExtension;
                }
                // if the copy value equals to value means that the real file
                // behind does not exist (DB is not actual)
                if ($copyValue === $value) {
                    // in such a case remove reservations and skip the field
                    foreach ($variants as $variant => $v) {
                        unlink($this->getFileFieldPath($field, array('file' => $copyValue, 'variant' =>  $variant)));
                    }
                    continue;
                }
                // copy the files
                $variantFiles = array();
                foreach ($variants as $variant => $v) {
                    $valueFile = $this->getFileFieldPath($field, array('file' => $value, 'variant' =>  $variant));
                    $copyFile = $this->getFileFieldPath($field, array('file' => $copyValue, 'variant' =>  $variant));
                    if (!is_readable(ROOT . DS . $valueFile)) {
                        continue;
                    }
                    if (!copy(ROOT . DS . $valueFile, ROOT . DS . $copyFile)) {
                        foreach ($variantFiles as $varianFile) {
                            unlink(ROOT . DS . $varianFile);
                        }
                        throw new Exception("File copy of '{$valueFile}' to '{$copyFile}' has failed");
                    }
                    $variantFiles[] = $copyFile;
                }
                // set the value to found copy value
                $value = $copyValue;
                $this->setIncomingFiles($field, $value);
            }
            $copyData[$field] = $value;
        }
        
        return $copyData;
    }
    
    /**
     * Cleans up obsolete and incoming files at the end of their processing according
     * to result of processing
     * 
     * @param bool $success Optional. Result of files processing. If TRUE then 
     *      processing succeeded, obsolete files are deleted and incoming emptied.
     *      If FALSE then processing has failed, obsolete files are emptied and
     *      incoming files are deleted. Defaults to TRUE.
     */
    public function cleanUpFiles($success = true) {
        if ($success) {
            $this->deleteObsoleteFiles();
            $this->emptyIncomingFiles();
        }
        else {
            $this->deleteIncomingFiles();
            $this->emptyObsoleteFiles();
        }
    }
    
    /**
     * Sets incoming files paths into App::$incomingFiles property for given field
     * and its (new) value 
     *  
     * @param string $field Name of file field to set incoming files for
     * @param string $value Value of file field which should be a name 
     *          (filename or basename) of the stored file. File path or URL path can be used too.
     */
    public function setIncomingFiles($field, $value) {
        $files = $this->getFileFieldPaths($field, array('file' => $value));
        if (!$files) {
            return;
        }
        $this->incomingFiles =  (array)$this->incomingFiles;
        foreach ($files as $file) {
            $this->incomingFiles[$file] = true;
        }
    }
    
    /**
     * Empties list of incoming files stored in Model::$incomingFiles
     */
    public function emptyIncomingFiles() {
        $this->incomingFiles = array();
    }
    
    /**
     * Deletes all files listed in Model::$incomingFiles array and sets this array
     * empty.
     */
    public function deleteIncomingFiles() {
        foreach ($this->incomingFiles as $file => $v) {
            // if file is not under fileStore directory or it is not a file
            // then do nothing
            if (
                !Validate::path(
                    $file, 
                    array(
                        'root' => $this->fileStore,
                        'file' => true,
                        'exists' => true,
                    )
                )
            ) {
                continue;
            }
            unlink(ROOT . DS . $file);
        }
        $this->emptyIncomingFiles();
    }
                   
    /**
     * Populates Model::$obsoleteFiles for given fields of record(s) retrieved according to 
     * provided options.
     * 
     * @param array|string $fields Array of file fields or a single file field to prepare 
     *      obsolete files for from retrieved records
     * @param array $options Options array passed to Model::save(), update() or delete().
     *      Options are used to retrieve records.
     * 
     * @return bool FALSE if no records were retrieved and so no files prepared.
     *      Othervise TRUE.
     */
    public function setObsoleteFiles($fields, $options) {
        $fields = (array)$fields;
        // retrieve the record
        $options['fields'] = $fields;
        $options['first'] = false;
        $options['order'] = null;
        $options['allowFields'] = null;
        $options['avoidFields'] = null;
        $records = $this->find($options);
        // if no record found then there is no reason to prepare any files
        if (!$records) {
            return false;
        }
        // put current file names into array of obsolete files
        $this->obsoleteFiles = (array)$this->obsoleteFiles;
        foreach ($records as $record) {
            foreach ($fields as $field) {
                if (empty($record[$field])) {
                    continue;
                }
                $files = $this->getFileFieldPaths($field, array('file' => $record[$field]));
                if (!$files) {
                    continue;
                }
                foreach ($files as $file) {
                    $this->obsoleteFiles[$file] = true;
                }
            }
        }
        return true;
    }
    
    /**
     * Empties list of obsolete files stored in Model::$obsoleteFiles
     */
    public function emptyObsoleteFiles() {
        $this->obsoleteFiles = array();
    }
    
    /**
     * Deletes all files listed in Model::$obsoleteFiles array and sets this array
     * empty.
     */
    public function deleteObsoleteFiles() {
        foreach ($this->obsoleteFiles as $file => $v) {
            // if file is not under fileStore directory or it is not a file
            // then do nothing
            if (
                !Validate::path(
                    $file, 
                    array(
                        'root' => $this->fileStore,
                        'file' => true,
                        'exists' => true,
                    )
                )
            ) {
                continue;
            }
            unlink(ROOT . DS . $file);
        }
        $this->emptyObsoleteFiles();
    }
    
    /**
     * Deletes files of specified record(s)
     * 
     * ATTENTION: The method works only over file fields of primary model. 
     * File fields of joined models cannot be listed in $fields input.
     * 
     * @param array|string $fields Array of file fields or a single file field to 
     *      delete files for from retrieved records. Only file fields of primary
     *      model can be provided here.
     * @param array $options Options array passed to Model::find(). Options are 
     *      used to retrieve records.
     * 
     * @return integer Number od records for which files has been deleted.
     */
    public function deleteFiles($fields, $options) {
        $fields = (array)$fields;
        // retrieve the record
        $options['fields'] = $fields;
        $options['allowFields'] = null;
        $options['avoidFields'] = null;
        $options['key'] = $this->name . '.' . $this->primaryKey;
        $options['plain'] = false;
        $records = $this->findList($options);
        // if no record found then there is no reason to prepare any files
        if (!$records) {
            return 0;
        }
        // put current file names into array of obsolete files
        foreach ($fields as $field) {
            // get field empty value
            $fieldSchema = Sanitize::value($this->schema[$field]);
            $emptyValue = '';
            if (
                !empty($fieldSchema['null'])
                || 
                array_key_exists('default', $fieldSchema) 
                && $fieldSchema['default'] === null
            ) {
                $emptyValue = null;
            }
            // delete actual field files over all retrieved records
            foreach ($records as $id => $record) {
                $fileName = $record[$field];
                if (!$fileName) {
                    continue;
                }
                $files = $this->getFileFieldPaths($field, array('file' => $fileName));
                if (!$files) {
                    continue;
                }
                foreach ($files as $file) {
                    // if file is not under fileStore directory or it is not a file
                    // then do nothing
                    if (
                        !Validate::path(
                            $file, 
                            array(
                                'root' => $this->fileStore,
                                'file' => true,
                                'exists' => true,
                            )
                        )
                    ) {
                        continue;
                    }
                    unlink(ROOT . DS . $file);
                }
                // erase field value
                $this->update(array($field => $emptyValue), array(
                    'conditions' => array($this->primaryKey => $id)
                ));
            }
        }
        return count($records);
    }
    
    /**
     * ATTRIBUTE METHODS
     */
    
    /**
     * Parses the provided selective attribute value "metal;bronze:+1;ag=silver:+2;au=gold:+3"
     * to folloving array:
     * 
     *      array(
     *          'values' => array('metal', 'bronze', 'ag' => 'silver', 'au' => 'gold', ...)
     *      ) 
     * 
     * If the attribute is priced then the array looks like:
     * 
     *      array(
     *          'values' => array('metal', 'bronze', 'ag' => 'silver', 'au' => 'gold', ...)
     *          'prices' => array(
     *              'metal' => array('price' => 0, 'operator' => '+'),
     *              'bronze' => array('price' => 1, 'operator' => '+'),
     *              'silver' => array('price' => 2, 'operator' => '+'),
     *              'gold' => array('price' => 3, 'operator' => '+'),
     *              ...
     *              // in  general
     *              '{value}' => {priceAdjustmentArray}
     *      ) 
     * 
     * @param string $rawValue Raw string value of attribute, e.g. metal;bronze:+1;silver:+2;gold:+3
     * @param array $options Following options can be used:
     *      - 'priced' (bool) If TRUE then values of attriute are priced. Defaults to TRUE.
     *      - 'valuesSeparator' (string) Defaults to value of setting App.attributeValuesSeparator.
     *      - 'pricesSeparator' (string) Defaults to value of setting App.attributePricesSeparator.
     *      - 'pidsSeparator' (string) Defaults to value of setting App.attributePidsSeparator.
     * 
     * @return array Array of attribute values or in case of priced attribute a list
     *          of price adjustment arrays with attribute values as keys.
     */
    public function parseSelectiveAttribute($rawValue, $options = array()) {
        static $defaultValuesSeparator = null;
        if ($defaultValuesSeparator === null) {
            $defaultValuesSeparator = App::getSetting('App', 'attributeValuesSeparator');
        }
        static $defaultPricesSeparator = null;
        if ($defaultPricesSeparator === null) {
            $defaultPricesSeparator = App::getSetting('App', 'attributePricesSeparator');
        }
        static $defaultPidsSeparator = null;
        if ($defaultPidsSeparator === null) {
            $defaultPidsSeparator = App::getSetting('App', 'attributePidsSeparator');
        }
        $defaults = array(
            'priced' => true,
            'valuesSeparator' => $defaultValuesSeparator,
            'pricesSeparator' => $defaultPricesSeparator,
            'pidsSeparator' => $defaultPidsSeparator,
        );
        $options = array_merge($defaults, $options);
        $rawValues = Str::explode($options['valuesSeparator'], $rawValue);
        $return['values'] = array();
        foreach ($rawValues as $value) {
            $value = explode($options['pidsSeparator'], $value);
            $pid = null;
            if (count($value) > 1) {
                $pid = array_shift($value);
            }
            $value = implode($options['pidsSeparator'], $value);
            if ($pid !== null) {
                $return['values'][$pid] = $value;
            }
            else {
                $return['values'][] = $value;
            }
        }
        if ($options['priced']) {
            $return['prices'] = array();
            foreach ($return['values'] as &$value) {
                $value = explode($options['pricesSeparator'], $value);
                if (count($value) > 1) {
                    $price = array_pop($value);
                    if ($price[0] == '+') {
                        $operator = '+';
                        $price = substr($price, 1);
                    }
                    elseif ($price[0] == '-') {
                        $operator = '-';
                        $price = substr($price, 1);
                    }
                    elseif ($price[0] == '*') {
                        $operator = '*';
                        $price = substr($price, 1);
                    }
                    elseif (substr($price, -1) == '%') {
                        $operator = '*';
                        $price = (float)substr($price, 0, -1) / 100;
                    }
                    else {
                        $operator = '='; 
                        //$operator = '+';
                    }
                }
                else {
                    $operator = '+';
                    $price = 0;
                }
                $value = implode($options['pricesSeparator'], $value);
                $return['prices'][$value] = array(
                    'operator' => $operator,
                    'price' => $price,
                );
            }
            unset($value);
        }
        return $return;
    }
    
    /**
     * Acumulates price adjustments. The acumulation must be done in cycle, 
     * like e.g.:
     * 
     *      $myPrice = 123.45;
     *      // initialize acumulated adjustment
     *      $acumulativeAdjustment = array();
     *      foreach ($pricedSelAttr as $attr) {
     *          // get attribute price adjustment 
     *          $adjustment = $attr[{actualValueOfAttr}]
     *          // acumulate it with previous price adjustments
     *          $acumulatedAdjustment = acumulatePriceAdjustments(
     *              $myPrice, $adjustment, $acumulativeAdjustment
     *          );
     *      }
     * 
     * @param float $price The original price which is going to be updated
     * @param array $adjustment Price adjustment array containing kes 'price' and 'operator'
     *          to be added to acumulative adjustment.
     * @param array $acumulativeAdjustment Actual acumulative adjustment. It is updated 
     *          and returned on output of method. Use empty array to initialize this value. 
     *          Defaults to empty array.
     * 
     * @return array Price adjustment array to be applyed to original price. It is an array
     *      containing items 'price' and 'operator'.
     */
    public function acumulatePriceAdjustments($price, $adjustment, $acumulativeAdjustment) {
        if (!isset($acumulativeAdjustment['price'])) {
            $acumulativeAdjustment['price'] = 0.0;
        }
        if (empty($acumulativeAdjustment['operator'])) {
            $acumulativeAdjustment['operator'] = '+';
        }
        switch (Sanitize::value($adjustment['operator'])) {
            case '+':
                $acumulativeAdjustment['price'] += (float)Sanitize::value($adjustment['price']);
                break;
            case '-':
                $acumulativeAdjustment['price'] -= (float)Sanitize::value($adjustment['price']);
                break;
            case '*':
                if ($acumulativeAdjustment['operator'] == '=') {
                    throw new Exception ('Explicit prices adjustment conflict');
                }
                $acumulativeAdjustment['price'] += (float)$price * (float)Sanitize::value($adjustment['price']);
                $acumulativeAdjustment['operator'] = '=';
                break;
            case '=':
                if ($acumulativeAdjustment['operator'] == '=') {
                    throw new Exception ('Explicit prices adjustment conflict');
                }
                $acumulativeAdjustment['price'] += (float)Sanitize::value($adjustment['price']);
                $acumulativeAdjustment['operator'] = '=';
                break;
        }
        return $acumulativeAdjustment;
    } 
    
    /**
     * Updates given price by adjustment
     * 
     * @param float $price The original price which is going to be updated
     * @param array $adjustment Price adjustment array containing kes 'price' 
     *          and 'operator'.
     * 
     * @return float Updated price
     */
    public function adjustPrice($price, $adjustment) {
        switch (Sanitize::value($adjustment['operator'])) {
            case '+':
                $price += (float)Sanitize::value($adjustment['price']);
                break;
            case '-':
                $price -= (float)Sanitize::value($adjustment['price']);
                break;
            case '*':
                $price = (float)$price * (float)Sanitize::value($adjustment['price']);
                break;
            case '=':
                $price = (float)Sanitize::value($adjustment['price']);
                break;
        }
        return $price;
    } 
                
    /**
     * VALIDATION METHODS
     */
    
    /**
     * Validates given data according to $this->validations rules or accodring logic 
     * implemented inside of overrided instances of this method.
     * 
     * Validation can be also launched as a part of save() and update() methods but it
     * these cases only fields from table schema are validated.
     * If you need to validate also fields which are not saved, (captcha, verification code, ...)
     * then launch the validation separately by direct call of validate() method.
     * 
     * To implement validations by overriding Model::validate() method use something like:
     * 
     *  MyClass extends Model {
     * 
     *      public function validate($data, $options = array()) {
     *          $defaults = array(
     *              'alternative' => null,
     *              'on' => null,
     *          )
     *          $options = array_merge($defaults, $options);
     *          $isValid = true;
     * 
     *          // call parent method to treat validations defined in MyClass::$validations 
     *          $isValid = parent::validate($data, $options);
     * 
     *          // implement your own validation logic, e.g. for usual email it would be:
     *          // - required (present in data) on create
     *          if (
     *              $options['on'] === 'create'
     *              && !array_key_exists('email', $data)
     *          ) {
     *              $isValid = false;
     *              $this->setError('email', __v(__FILE__, 'E-mail is mandatory'));
     *          }
     *          // - not empty if in data
     *          elseif (
     *              array_key_exists('email', $data)
     *              && empty($data['email'])
     *          ) {
     *              $isValid = false;
     *              $this->setError('email', __v(__FILE__, 'E-mail is mandatory'));
     *          }
     *          // - validate email format if nonempty in data
     *          elseif (
     *              !empty($data['email'])
     *              && !Validate::email($data['email'])
     *          ) {
     *              $isValid = false;
     *              $this->setError('email', __v(__FILE__, 'E-mail is not valid'));
     *          }
     *          // - validate that unique if nonempty in data
     *          elseif (
     *              !empty($data['email'])
     *              && !$this->validate_unique($data['email'], 'email', $data, null, array(), false)
     *          ) {
     *              $isValid = false;
     *              $this->setError('email', __v(__FILE__, 'E-mail must be unique'));
     *          }
     *           
     *          return $isValid;
     *      }
     *  }
     * 
     * NOTE: This method resets the value of property Model::$errors
     *
     * @param array $data Data to be validated. Passed by reference to give this function
     *      possibility to normalize data values
     * @param array $options Following are available:
     *      - 'allowValidations' (array) List of allowed validations. For sake of
     *          this options the validations must be named, e.g. array('fieldName' => array(
     *          'validationName01', 'validationName02', ...)). Defauts to NULL.
     *      - 'avoidValidations' (array) List of validations to avoid. For sake of
     *          this options the validations must be named, e.g. array('fieldName' => array(
     *          'validationName01', 'validationName02', ...)). Use array('fieldName' => TRUE) 
     *          to avoid all for given field. Defaults to NULL.
     *      - 'alternative' (string|array) Single validation altrnative name or an array
     *          of such names. If empty then only general validations are executed.
     *          Defaults to NULL.
     *      - 'on' (string|bool) When the validation hould be launched? Possible values are 
     *          'create', 'update' and TRUE. If TRUE the 'create' or 'update' is set implicitly.
     *      - 'allowFields' (string|array) Single field or list of fields for which validations are executed.
     *      - 'avoidFields' (string|array) Single field or list of fields for which validations are avoided.
     *      - 'normalize' (bool) Should the $data be normalized first using Model::normalize()?
     *          Defaults to TRUE.
     *      - 'resetErrors' (bool) Should the model errors by reset before validation.
     *          Defaults to TRUE.
     * 
     * @return boolean. If TRUE data are valid. If FALSE there are validations errors
     *      which can be retrieved by method $this->getErrors().
     */
    public function validate($data, $options = array()) {
        $defaults = array(
            'allowValidations' => null, // '{fieldName}' => array('{validationName01}', '{validationName02}', ...) 
            'avoidValidations' => null, // '{fieldName}' => array('{validationName01}', '{validationName02}', ...) // use '{fieldName}' => true to disallow all
            'alternative' => null, // '{alternative}' or array('{alternative01}', '{alternative02}', ...)
            'on' => null, // 'create', 'update', TRUE (if TRUE the 'create' or 'update' is set implicitly)
            'allowFields' => null,
            'avoidFields' => null,
            'normalize' => true,
            'resetErrors' => true, 
        );
        $options = array_merge($defaults, $options);
        if ($options['resetErrors']) {
            $this->resetErrors();
        }
        $isValid = true;
        // do nothing if no validations are defined
        if ($this->validations) {
            // set implicitly $options['on'] if TRUE
            if ($options['on'] === true) {
                $options['on'] = ($this->isCreation($data, $options)) ? 'create' : 'update';
            }
            // check for data normalization
            if ($options['normalize']) {
                $data = $this->normalize($data, $options);
            }
            // normalize alternative
            $options['alternative'] = (array)$options['alternative'];
            // retrieve fields which have to be validated
            $fields = DB::getDataFields($this->validations, $options);            
            // check validations
            $fieldOptions = array(
                'on' => $options['on'],
                'alternative' => $options['alternative']
            );
            // NOTE: it is necessary to pass throught validations in order they are defined !!!
            foreach ($fields as $field) {
                // prepare field options
                if (!empty($options['allowValidations'][$field])) {
                    $fieldOptions['allowValidations'] = $options['allowValidations'][$field];
                }
                else {
                    $fieldOptions['allowValidations'] = null;
                }
                if (!empty($options['avoidValidations'][$field])) {
                    $fieldOptions['avoidValidations'] = $options['avoidValidations'][$field];
                }
                else {
                    $fieldOptions['avoidValidations'] = null;
                }
                // validatate                
                $result = $this->validateField($data, $field, $fieldOptions);
                if ($result !== true) {
                    $isValid = false;
                    $this->errors[$this->name][$field] = $result;
                }
            }
        }
        
        return $isValid;
    }
    
    /**
     * Validates given $field in provided $data.
     * 
     * @param array $data Data array containing validated field
     * @param string $field Field name
     * @param array $options Following are available:
     *      - 'allowValidations' (array) List of allowed validations for field. For sake of
     *          this options the validations must be named, e.g. array('validationName01', 'validationName02', ...). 
     *          Defauts to NULL.
     *      - 'avoidValidations' (array) List of validations to avoid for field. For sake of
     *          this options the validations must be named, e.g. array('validationName01', 'validationName02', ...). 
     *          Use TRUE to avoid all for given field. Defaults to NULL.
     *      - 'alternative' (string|array) Single validation alternative name or an array
     *          of such names. If empty then only general validations are executed.
     *          Defaults to NULL.
     *      - 'on' (string|bool) When the validation hould be launched? Possible values are 
     *          'create', 'update' and TRUE. If TRUE the 'create' or 'update' is set implicitly.
     * 
     * @return mixed Returns TRUE if fields is valid. Returns array contaning all
     * error messages if field is not valid. Use '===' operator to check for TRUE.
     */
    public function validateField($data, $field, $options = array()) {
        $errors = array();
        $isValid = true;
        // do nothing if no field validations are defined
        if (!empty($this->validations[$field])) {
            $defaults = array(
                'allowValidations' => null, // array('{validationName01}', '{validationName02}', ...)
                'avoidValidations' => null, // array('{validationName01}', '{validationName02}', ...)
                'alternative' => null, // '{alternative}' or array('{alternative01}', '{alternative02}', ...)
                'on' => null, // 'create', 'update', TRUE (if true the 'create' or 'update' is set implicitly)
            );
            $options = array_merge($defaults, $options);
            // normalize alternative
            $options['alternative'] = (array)$options['alternative'];

            //make field validation
            if (is_array($data) && array_key_exists($field, $data)) {
                $fieldValue = $data[$field];
                $notFilled = (
                    $fieldValue === null 
                    || 
                    is_string($fieldValue) && trim($fieldValue) === ''
                );
                $notPresent = false;
            }
            else {
                $fieldValue = null;
                $notFilled = null;
                $notPresent = true;
            }
            $checkedAlternativeRules = array();
            foreach ($this->validations[$field] as $validationName => $validation) { 
                $ruleSatisfied = true;
                // consider 'allowValidations', 'avoidValidations', 'on' and 'alternative'
                if ((
                        !empty($options['allowValidations']) 
                        && !in_array($validationName, (array)$options['allowValidations'])
                    )
                    || (
                        !empty($options['avoidValidations']) 
                        && (
                            $options['avoidValidations'] === true   
                            || in_array($validationName, (array)$options['avoidValidations'])
                        )
                    )
                    || (
                        !empty($validation['on'])
                        && $options['on'] != $validation['on']
                    )
                    || (
                        !empty($validation['alternative'])
                        && (
                            empty($options['alternative'])
                            || !array_intersect((array)$validation['alternative'], $options['alternative'])
                        )
                    )
                    || (
                        !empty($validation['avoidAlternative'])
                        && !empty($options['alternative'])
                        && array_intersect((array)$validation['avoidAlternative'], $options['alternative'])
                    )
                    || (
                        in_array($validation['rule'], $checkedAlternativeRules, true)
                    )
                ) {
                    continue;
                }
                // validate
                if (!empty($validation['rule'])) {
                    $rule = $validation['rule'];
                    // keep track of rules checked already by alternative validations
                    // to not repeat them in general validations
                    if (!empty($validation['alternative'])) {
                        $checkedAlternativeRules[] = $rule;
                    }
                    // find out rule method and args
                    if (is_array($rule)) {
                        $method = array_shift($rule);
                        $args = $rule;
                    }
                    else {
                        $method = $rule;
                        $args = array();
                    }                    
                    // check for missing field
                    if (
                        $notPresent
                        && (
                            empty($validation['force'])
                            || $method == 'required'
                        )
                    ) {
                        // do nothing just wait for 'required' rule
                        if ($method == 'required') {
                            $ruleSatisfied = false;
                        }
                    }
                    elseif (
                        $notFilled 
                        && (
                            empty($validation['force'])
                            || $method == 'notEmpty' // notEmpty is launched always not only in a case when
                        )
                    ) {
                        // do nothing just wait for 'notEmpty' rule
                        if ($method == 'notEmpty') {
                            $ruleSatisfied = false;
                        }                        
                    }
                    // check for regex rule
                    elseif(Validate::regex($method)) {
                        $ruleSatisfied = preg_match($method, $fieldValue);
                    }
                    // call a method
                    else {
                        $hasStringMethodName = is_string($method);
                        // check for 'required' and 'notEmpty' which are not real methods
                        if ($method == 'required') {
                            // the field is present (checked alredy above), it's ok, let's continue
                            continue;
                        }
                        // check for anonymous function or callables
                        elseif (
                            !$hasStringMethodName 
                            && is_callable($method)
                        ) {
                            $ruleSatisfied = $method($fieldValue, $field, $data, $validation);
                        }
                        // check for validate_{$method} of Model() class
                        elseif (
                            $hasStringMethodName
                            && method_exists($this, 'validate_' . $method)
                        ) {
                            // pass $fieldValue, $field, $data, &$validation + rule args to $this->validate_{$method}()
                            // NOTE: $validation is passed by reference to allow change 'message' and 'break' dynamically 
                            $args = array_merge(array($fieldValue, $field, $data, &$validation), $args);
                            $ruleSatisfied = call_user_func_array(array($this, 'validate_' . $method), $args);
                        }
                        // check for static $method of Validate() class
                        elseif (
                            self::$validateClassExists
                            && $hasStringMethodName
                            && method_exists('Validate', $method)
                        ) {
                            // pass $fieldValue + rule args to Validate::{$method}()
                            $args = array_merge(array($fieldValue), $args);
                            $ruleSatisfied = call_user_func_array(array('Validate', $method), $args);
                        }
                        else {
                            throw new Exception("Invalid method '$method' given in '$validationName' validation rule for field '{$this->name}.$field'.");
                        }
                    }
                    // consider rule result
                    if (!$ruleSatisfied) {
                        // set error message(s)
                        if (!empty($validation['messages'])) {
                            // do not use array_merge() but use + to preserve integer indexes
                            $errors = (array)$validation['messages'] + $errors;
                        }
                        elseif (!empty($validation['errors'])) {
                            // do not use array_merge() but use + to preserve integer indexes
                            $errors = (array)$validation['errors'] + $errors;
                        }
                        elseif (!empty($validation['message'])) {
                            $errors[] = $validation['message'];
                        }
                        elseif (!empty($validation['error'])) {
                            $errors[] = $validation['error'];
                        }
                        else {
                            $errors[] = __(__FILE__, 'Invalid value');
                        }
                        $isValid = false;
                        // check for return if break is set
                        if (
                            !isset($validation['break']) // ~ defaults to TRUE
                            || !empty($validation['break'])
                        ) {
                            break;
                        }
                    }
                    // check for forced exit of field validation
                    if (!empty($validation['exit'])) {
                        break;
                    }
                }
                else {
                    throw new Exception("Missing validation rule for field '{$this->name}.$field'.");
                }
            }
        }
        
        // return
        if ($isValid) {
            return true;
        }
        return $errors;            
    }
    
    /**
     * Sets a new error message to specified field.
     * 
     * This method is used by validation functionality to set validation errors.
     * But it can be used also to set some other types of errors raised during 
     * processing. In such a case use the second signature.
     * 
     * The field name need not to be an existing DB field necessarily.
     * It is possible to use a virtual field name for some more
     * complex error type which does not concent exactly just one field.
     * 
     * Eventually you can capture related models errors by using the third signature. 
     * 
     * @param string $field Field name or a virtual field name. It can be a dot
     *      separated path e.g. 'email.1'
     * @param string $error Field validation error message
     * 
     * An alternative signature can be used to set processing errors (deprecated, 
     * use setProcessingError() instead):
     * 
     * @param string $error Processing error message
     */
    public function setError($field, $error = null) {
        // add field error
        if (func_num_args() === 2) {
            if (strpos($field, '.') === false) {
                $this->errors[$this->name][$field][] = $error;
            }
            else {
                $field = $this->name . '.' . $field;
                $errors = (array)Arr::getPath($this->errors, $field);
                $errors[] = $error;
                Arr::setPath($this->errors, $field, $errors);
            }
        }
        // add processing errors ($field contains message)
        else {
            $this->errors[$this->name]['_processing'][] = $field;
        }
    }
    
    /**
     * Sets error messages to specified field. Provided errors are merged into existing errors.
     * 
     * The field name need not to be an existing DB field necessarily.
     * It is possible to use a virtual field name for some more
     * complex error type which does not concern exactly just one field.
     * 
     * It can be used also to set some other types of errors raised during 
     * processing. In such a case use the second signature.
     * 
     * Eventually you can capture related models errors by using the third signature. 
     * 
     * @param string $field Field name or a virtual field name. It can be a dot
     *      separated path e.g. 'email.1'
     * @param array $errors Array of error messages of specified field
     * 
     * An alternative signature can be used to set processing errors (deprecated, 
     * use setProcessingErrors() instead):
     * 
     * @param array $errors Processing errors array
     * 
     * An alternative signature can be used to capture errors of related models used
     * in this model method:
     * 
     * @param Model $model Model to capture errors from
     * 
     * @throws Exception
     */
    public function setErrors($field, $errors = null) {
        // add field errors
        if (func_num_args() === 2) {
            $errors = (array)$errors;
            if (strpos($field, '.') === false) {
                if (!empty($this->errors[$this->name][$field])) {
                    $errors = array_merge((array)$this->errors[$this->name][$field], $errors);
                }
                $this->errors[$this->name][$field] = $errors;
            }
            else {
                $field = $this->name . '.' . $field;
                $errors = array_merge((array)Arr::getPath($this->errors, $field), $errors);
                Arr::setPath($this->errors, $field, $errors);
            }
        }
        // add Model errors ($field contains Model)
        elseif ($field instanceof Model) {
            $this->errors = Arr::mergeRecursive($this->errors, $field->getErrors(true));
        }
        // add processing errors ($field contains errors)
        else {
            $errors = (array)$errors;
            if (!empty($this->errors[$this->name]['_processing'])) {
                $errors = array_merge((array)$this->errors[$this->name]['_processing'], $errors);
            }
            $this->errors[$this->name]['_processing'] = $errors;
        }
    }
    
    /**
     * Returns error messsages for given field or all error messages of actual model
     * or all models (see below) 
     * 
     * @param string $field Optional. Field name to get errors for. It can be a dot
     *      separated path e.g. 'email.1'.
     * 
     * An alternative signature can be used to get errors of all models:
     * 
     * @param bool $allModels If TRUE then all errors of all models are returned.
     *          Defaults to FALSE means only errors of actual model are returned.
     * 
     * @return array Array contaning validation error messages. The nesting of array
     *      can change according to input:
     *      - If $field name is provided then array contains just the messages of the field.
     *      - If the method is called without any input then the array contains only 
     *          current model errors
     *      - If the second signature is used and TRUE is passed on method input then 
     *          all errors of all models are returned (see Model::$errors structure)
     * 
     *      If no messages then an empty array is returned.
     */
    public function getErrors($field = null) {
        if (is_string($field)) {
            // if field name provided without qualifier the look into current model errors
            if (strpos($field, '.') === false) {
                return (array)Sanitize::value($this->errors[$this->name][$field]);
            }
            // if field provided with qualifier
            else {
                $field = $this->name . '.' . $field;
                return (array)Arr::getPath($this->errors, $field);
            }
        }
        // return all errors of all models if required (second signature)
        elseif ($field === true) {
            return $this->errors;
        }
        return (array)Sanitize::value($this->errors[$this->name]);
    }
    
    /**
     * Returns the first error messsage for given field or the first error message 
     * from all errors (and all models) if no field name is provided.
     * 
     * @param string $field Optional. Field name.
     * 
     * @return string Validation error message.
     */
    public function getError($field = null) {
        if (is_string($field)) {
            $error = $this->getErrors($field);
        }
        // if no field is specified then get all errors of all models
        else {
            $error = $this->getErrors(true);
        }
        do {
            $error = array_shift($error);
        } while (is_array($error));
        return $error;
    }
    
    /**
     * Sets a new processing error message.
     * 
     * @param array $error Processing error message
     */
    public function setProcessingError($error) {
        $this->setError('_processing', $error);
    }
    
    /**
     * Sets processing error messages. Provided errors are merged into existing errors.
     * 
     * @param array $errors Processing errors array
     * 
     * @throws Exception
     */
    public function setProcessingErrors($errors) {
        $this->setErrors('_processing', $errors);
    }
    
    /**
     * Returns processing error messsages. 
     * 
     * @return array Array contaning processing error messages. If no messages 
     * then an empty array is returned.
     */
    public function getProcessingErrors() {
        return $this->getErrors('_processing');
    }
    
    /**
     * Returns the first processing error messsage.
     * 
     * @return string Processing error message.
     */
    public function getProcessingError() {
        return $this->getError('_processing');
    }
    
    /**
     * Resets Model::$errors to empty array
     */
    public function resetErrors() {
        $this->errors = array();
    }
        
    /**
     * Sets a new warning message to specified field.
     * 
     * This method can used by validation functionality to set validation warnings.
     * But it can be used also to set some other types of warnings raised during 
     * processing. In such a case use the second signature.
     * 
     * The field name need not to be an existing DB field necessarily.
     * It is possible to use a virtual field name for some more
     * complex warning type which does not concent exactly just one field.
     * 
     * Eventually you can capture related models warnings by using the third signature. 
     * 
     * @param string $field Field name or a virtual field name. It can be a dot
     *      separated path e.g. 'email.1'
     * @param string $message Field validation warning message
     * 
     * An alternative signature can be used to set processing warnings (deprecated, 
     * use setProcessingWarning() instead):
     * 
     * @param string $message Processing warning message
     */
    public function setWarning($field, $message = null) {
        // add field warning
        if (func_num_args() === 2) {
            if (strpos($field, '.') === false) {
                $this->warnings[$this->name][$field][] = $message;
            }
            else {
                $field = $this->name . '.' . $field;
                $warnings = (array)Arr::getPath($this->warnings, $field);
                $warnings[] = $message;
                Arr::setPath($this->warnings, $field, $warnings);
            }
        }
        // add processing warnings ($field contains message)
        else {
            $this->warnings[$this->name]['_processing'][] = $field;
        }
    }
    
    /**
     * Sets warning messages to specified field. Provided warnings are merged into existing warnings.
     * 
     * The field name need not to be an existing DB field necessarily.
     * It is possible to use a virtual field name for some more
     * complex warning type which does not concern exactly just one field.
     * 
     * It can be used also to set some other types of warnings raised during 
     * processing. In such a case use the second signature.
     * 
     * Eventually you can capture related models warnings by using the third signature. 
     * 
     * @param string $field Field name or a virtual field name. It can be a dot
     *      separated path e.g. 'email.1'
     * @param array $warnings Array of warning messages of specified field
     * 
     * An alternative signature can be used to set processing warnings (deprecated, 
     * use setProcessingWarnings() instead):
     * 
     * @param array $warnings Processing warnings array
     * 
     * An alternative signature can be used to capture warnings of related models used
     * in this model method:
     * 
     * @param Model $model Model to capture warnings from
     * 
     * @throws Exception
     */
    public function setWarnings($field, $warnings = null) {
        // add field warnings
        if (func_num_args() === 2) {
            $warnings = (array)$warnings;
            if (strpos($field, '.') === false) {
                if (!empty($this->warnings[$this->name][$field])) {
                    $warnings = array_merge((array)$this->warnings[$this->name][$field], $warnings);
                }
                $this->warnings[$this->name][$field] = $warnings;
            }
            else {
                $field = $this->name . '.' . $field;
                $warnings = array_merge((array)Arr::getPath($this->warnings, $field), $warnings);
                Arr::setPath($this->warnings, $field, $warnings);
            }
        }
        // add Model warnings ($field contains Model)
        elseif ($field instanceof Model) {
            $this->warnings = Arr::mergeRecursive($this->warnings, $field->getWarnings(true));
        }
        // add processing warnings ($field contains warnings)
        else {
            $warnings = (array)$warnings;
            if (!empty($this->warnings[$this->name]['_processing'])) {
                $warnings = array_merge((array)$this->warnings[$this->name]['_processing'], $warnings);
            }
            $this->warnings[$this->name]['_processing'] = $warnings;
        }
    }
    
    /**
     * Returns warning messsages for given field or all warning messages of actual model
     * or all models (see below) 
     * 
     * @param string $field Optional. Field name to get warnings for. It can be a dot
     *      separated path e.g. 'email.1'.
     * 
     * An alternative signature can be used to get warnings of all models:
     * 
     * @param bool $allModels If TRUE then all warnings of all models are returned.
     *          Defaults to FALSE means only warnings of actual model are returned.
     * 
     * @return array Array contaning validation warning messages. The nesting of array
     *      can change according to input:
     *      - If $field name is provided then array contains just the messages of the field.
     *      - If the method is called without any input then the array contains only 
     *          current model warnings
     *      - If the second signature is used and TRUE is passed on method input then 
     *          all warnings of all models are returned (see Model::$warnings structure)
     * 
     *      If no messages then an empty array is returned.
     */
    public function getWarnings($field = null) {
        if (is_string($field)) {
            // if field name provided without qualifier the look into current model warnings
            if (strpos($field, '.') === false) {
                return (array)Sanitize::value($this->warnings[$this->name][$field]);
            }
            // if field provided with qualifier
            else {
                $field = $this->name . '.' . $field;
                return (array)Arr::getPath($this->warnings, $field);
            }
        }
        // return all warnings of all models if required (second signature)
        elseif ($field === true) {
            return $this->warnings;
        }
        return (array)Sanitize::value($this->warnings[$this->name]);
    }
    
    /**
     * Returns the first warning messsage for given field or the first warning message 
     * from all warnings (and all models) if no field name is provided.
     * 
     * @param string $field Optional. Field name.
     * 
     * @return string Validation warning message.
     */
    public function getWarning($field = null) {
        if (is_string($field)) {
            $warning = $this->getWarnings($field);
        }
        // if no field is specified then get all warnings of all models
        else {
            $warning = $this->getWarnings(true);
        }
        do {
            $warning = array_shift($warning);
        } while (is_array($warning));
        return $warning;
    }
    
    /**
     * Sets a new processing warning message.
     * 
     * @param array $warning Processing warning message
     */
    public function setProcessingWarning($warning) {
        $this->setWarning('_processing', $warning);
    }
    
    /**
     * Sets processing warning messages. Provided warnings are merged into existing warnings.
     * 
     * @param array $warnings Processing warnings array
     * 
     * @throws Exception
     */
    public function setProcessingWarnings($warnings) {
        $this->setWarnings('_processing', $warnings);
    }
    
    /**
     * Returns processing warning messsages. 
     * 
     * @return array Array contaning processing warning messages. If no messages 
     * then an empty array is returned.
     */
    public function getProcessingWarnings() {
        return $this->getWarnings('_processing');
    }
    
    /**
     * Returns the first processing warning messsage.
     * 
     * @return string Processing warning message.
     */
    public function getProcessingWarning() {
        return $this->getWarning('_processing');
    }
    
    /**
     * Resets Model::$warnings to empty array
     */
    public function resetWarnings() {
        $this->warnings = array();
    }
        
    /**
     * Returns array of required fields according to defined model validations and
     * provided validation alternative(s) and on. Field names are set to both array values 
     * and keys, for sake of search convenience.
     * 
     * @param array $options Following are available:
     *      - 'alternative' (string|array) Single validation alternative (string)
     *      or array of alternatives. Defaults to NULL.
     *      - 'on' (string) Validation 'on' property. The most common values 
     *      are 'create' or 'update'. Defaults to NULL.
     *      - 'data' (array) Array of data to resolve required fields for. Defaults to NULL.
     * 
     * @return array
     */
    public function getRequiredFields($options = array()) {
        $defaults = array(
            'alternative' => null,
            'on' => null,
            'data' => null,
        );
        $options = array_merge($defaults, $options);
        $options['alternative'] = (array)$options['alternative'];
        $options['data'] = (array)$options['data'];
        // get model nested data
        if (
            isset($options['data'][$this->name])
            && (
                empty($options['data'][$this->name])
                || is_array($options['data'][$this->name])
            )
        ) {
            $options['data'] = $options['data'][$this->name];
        }
        // create alternative slug
        $alernativeSlug = implode('+', $options['alternative']);
        $alernativeSlug .= ':' . $options['on'];
        if ($options['data']) {
            $alernativeSlug .= ':' . sha1(json_encode($options['data']));
        }
        if (!isset($this->requiredFields[$alernativeSlug])) {
            $this->requiredFields[$alernativeSlug] = array();
            $errorsBak = $this->errors;
            $fields = array_keys($this->validations);
            foreach($fields as $field) {  
                $testData = $options['data'];
                unset($testData[$field]);
                $this->validate($testData, array_merge($options, array(
                    'resetErrors' => true,
                )));
                if ($this->getErrors($field)) {
                    $this->requiredFields[$alernativeSlug][$field] = $field;
                }
            }
            $this->errors = $errorsBak;
        }
        return $this->requiredFields[$alernativeSlug];
    }
    
    /**
     * Returns array of notEmpty fields according to defined model validations and
     * provided validation alternative(s) and on. Field names are set to both array values 
     * and keys, for sake of search convenience.
     * 
     * @param array $options Following are available:
     *      - 'alternative' (string|array) Single validation alternative (string)
     *      or array of alternatives. Defaults to NULL.
     *      - 'on' (string) Validation 'on' property. The most common values 
     *      are 'create' or 'update'. Defaults to NULL.
     *      - 'data' (array) Array of data to resolve notEmpty fields for. Defaults to NULL.
     * 
     * @return array
     */
    public function getNotEmptyFields($options = array()) {
        $defaults = array(
            'alternative' => null,
            'on' => null,
            'data' => null,
        );
        $options = array_merge($defaults, $options);
        $options['alternative'] = (array)$options['alternative'];
        $options['data'] = (array)$options['data'];
        // get model nested data
        if (
            isset($options['data'][$this->name])
            && (
                empty($options['data'][$this->name])
                || is_array($options['data'][$this->name])
            )
        ) {
            $options['data'] = $options['data'][$this->name];
        }
        // create alternative slug
        $alernativeSlug = implode('+', $options['alternative']);
        $alernativeSlug .= ':' . $options['on'];
        if ($options['data']) {
            $alernativeSlug .= ':' . sha1(json_encode($options['data']));
        }
        if (!isset($this->notEmptyFields[$alernativeSlug])) {
            $this->notEmptyFields[$alernativeSlug] = array();
            $errorsBak = $this->errors;
            $fields = array_keys($this->validations);
            foreach($fields as $field) {  
                $testData = array_merge($options['data'], array(
                    $field => '',
                ));
                $this->validate($testData, array_merge($options, array(
                    'resetErrors' => true,
                )));
                if ($this->getErrors($field)) {
                    $this->notEmptyFields[$alernativeSlug][$field] = $field;
                }
            }
            $this->errors = $errorsBak;
        }
        return $this->notEmptyFields[$alernativeSlug];
    }
    
    /**
     * Reserves tables for provided models for writing for actual process (= actual DB session/connection).
     * Under writing are meant Model::save(), Model::update(), DB::insert(), DB::update(), DB::delete(), DB::truncateTables() and DB::dropTables()
     * 
     * @param string $name Reservation name used to pair table reservation with corresponding 
     *          table unreservation and so allow nested reservations of the same table 
     *          by the actual process. 
     *          NOTE: To make this work reservation names must be unique. To assure this 
     *          use following pattern to create name: {className}::{methodName}(). 
     *          E.g. if you use reservation in Model::addTreeNode() then the reservation
     *          name is 'Model::addTreeNode()'.
     * @param string|array $models Single model name or an array of such names. If
     *      model is from other than actual module then prefix it by module name, 
     *      e.g.: array('User', 'Eshop.EshopProduct')
     * @param array $options Optional. Following options can be specified:
     *      - 'tries' (int) Number of tries to reserve the tables in case that it does 
     *          not succeed on first attempt. Defaults to 100, means 100 attempts are made.
     *      - 'retryTime' (int) Number of miliseconds to wait between reservations attempts.
     *          Defaults to 200.
     *      - 'exceptionOnDuplicitReservation' (bool) If TRUE then the method throws 
     *          exception in case that the table has been already reserved by actual process.
     *          Defaults to FALSE.
     *  
     * @throws Exception on invalid model name
     * @throws Exception_DB_TablesDuplicitReservation if $options['exceptionOnDuplicitReservation'] is TRUE and
     *      actual process makes nested reservations of the same table
     * @throws Exception_DB_TablesReservationFailure if the reservation of provided tables
     *      fails
     */
    public function reserveTables($name, $models, $options = array()) {
        $models = (array)$models;
        $tables = array();
        foreach($models as $model) {
            $model = explode('.', $model);
            if (count($model) == 1) {
                $Model = $this->loadModel($model[0], true);
            }
            elseif (count($model) == 2) {
                $Model = App::loadModel($model[0], $model[1], true);
            }
            else {
                throw new Exception(__e(__FILE__, 'Invalid model name %s', $model));
            }
            $tables[] = $Model->getPropertyTable();
        }
        DB::reserveTables($name, $tables, $options);
    }
    
    /**
     * Reserves table of actual model for writing for actual process (= actual DB session/connection).
     * Under writing are meant Model::save(), Model::update(), DB::insert(), DB::update(), DB::delete(), DB::truncateTables() and DB::dropTables()
     * 
     * @param string $name Reservation name used to pair table reservation with corresponding 
     *          table unreservation and so allow nested reservations of the same table 
     *          by the actual process. 
     *          NOTE: To make this work reservation names must be unique. To assure this 
     *          use following pattern to create name: {className}::{methodName}(). 
     *          E.g. if you use reservation in Model::addTreeNode() then the reservation
     *          name is 'Model::addTreeNode()'.
     * @param array $options Optional. Following options can be specified:
     *      - 'tries' (int) Number of tries to reserve the tables in case that it does 
     *          not succeed on first attempt. Defaults to 100, means 100 attempts are made.
     *      - 'retryTime' (int) Number of miliseconds to wait between reservations attempts.
     *          Defaults to 200.
     *      - 'exceptionOnDuplicitReservation' (bool) If TRUE then the method throws 
     *          exception in case that the table has been already reserved by actual process.
     *          Defaults to FALSE.
     *  
     * 
     * @throws Exception_DB_TablesDuplicitReservation if $options['exceptionOnDuplicitReservation'] is TRUE and
     *      actual process makes nested reservations of the same table
     * @throws Exception_DB_TablesReservationFailure if the reservation of provided tables
     *      fails
     */
    public function reserveTable($name, $options = array()) {
        $this->reserveTables($name, $this->name, $options);
    }
    
    /**
     * Unreserves tables reserved by actual process under provided reservation name.
     * 
     * NOTE: this is just the same as DB::unreserveTables()
     * 
     * @param string $name Optional. Reservation name to unreserve tables for. 
     *      If no reservation name is provided then all tables reserved by actual process
     *      are unreserved.
     */
    public function unreserveTables($name = null) {
        DB::unreserveTables($name);
    }    
    
    /**
     * VALIDATION RULES
     * 
     * Following default arguments are passed to each rule:
     * 
     * @param mixed $fieldValue
     * @param string $fieldName
     * @param array $data
     * @param array &$validation - Reference to validation the rule belongs to.
     *      This allows to change 'message' and 'break' properties of the validation. 
     *      Change of other validation properties is ignored.
     * 
     * If the rule itself contains some arg(s) then these are passed after defaul
     * arguments and in the same order as provided in validation definition. E.g.
     * 'rule' => array('minLength', 8) calls method $this->minLength($fieldValue, $fieldName, $data, $length)
     * 
     * @return Validation rule must return boolean TRUE or FALSE.
     */
            
    /**
     * Checks if provided value is unique.
     * 
     * Except of standart Model::validate_XYZ() method arguments following additional argument can be passed:
     * 
     * @param array $conditions Optional. Conditions to filter out unigue value records group.
     *      Defaults to empty array().
     * @param bool $ignoreUnchanged Optional. If TRUE then record duplicity is ignored if
     *      the record already exists and $fieldValue is not changed. It means TRUE is returned as if the 
     *      $fieldValue was unique. This can be used in some special cases when 
     *      validation changes after time and there are already existing records 
     *      with existing duplicities in DB. E.g. to accounts with same email - you
     *      cannot bother existing user that his email already exists and he cannot save
     *      his profile without changing his email. Defaults to FALSE.
     */
    public function validate_unique($fieldValue, $fieldName, $data, &$validation, $conditions = array(), $ignoreUnchanged = false) {
        $conditions = DB::nestConditions((array)$conditions);
        $conditions[$fieldName] = $fieldValue;
        $duplicitiesConditions = $conditions;
        if (!empty($data[$this->primaryKey])) {
            $duplicitiesConditions[$this->primaryKey . ' !='] = $data[$this->primaryKey];
        }
        $result = $this->findFirst(array('conditions' => $duplicitiesConditions));
        $isUnique = empty($result);
        // if the record is not unique and it is required to ignore unchanged cases
        if (
            $ignoreUnchanged
            && !$isUnique 
            && !empty($data[$this->primaryKey]) 
        ) {
            $unchangedConditions = $conditions;
            $unchangedConditions[$this->primaryKey] = $data[$this->primaryKey];
            $result = $this->findFirst(array('conditions' => $unchangedConditions));
            // if the record is unchanged then ignore that it is not unique
            $isUnique = !empty($result);
        }
        return $isUnique;
    }    
    
    /**
     * Checks if provided value equals to value of reference field name in validated data.
     * 
     * Except of standart Model::validate_XYZ() method arguments following additional argument must be passed:
     * 
     * @param string $referenceFieldName Field name to compare value with.
     */
    public function validate_equalsToField($fieldValue, $fieldName, $data, &$validation, $referenceFieldName) {
        return ($fieldValue == Sanitize::value($data[$referenceFieldName]));
    }  
    
    /**
     * Checks provided string value length.
     * 
     * NOTE: If validation has no message then this method sets the default one.
     * If the validation has a message then this method replaces :length: insert
     * by the resolved length.
     * 
     * Except of standart Model::validate_XYZ() method arguments following additional argument can be passed:
     * 
     * @param int $length Optional. Maximal allowed length. If not provided or empty
     *      then it is retrieved from DB or from schema. 
     */
    public function validate_fieldMaxLength($fieldValue, $fieldName, $data, &$validation, $length = null) {
        if (empty($length)) {
            $length = null;
            $fieldOptions = DB::getFieldOptions($this->table, $fieldName);
            if (isset($fieldOptions['length'])) {
                $length = $fieldOptions['length'];
            }
            elseif (!empty($this->schema[$fieldName]['length'])) {
                $length = $this->schema[$fieldName]['length'];
            }
        }
        if (
            !empty($length)
            && mb_strlen($fieldValue, 'UTF-8') > (int)$length
        ) {
            if (empty($validation['message'])) {
                $validation['message'] = __v(__FILE__, 'Max allowed number of characters is %s', $length);
            }
            else {
                $validation['message'] = str_replace(':length:', $length, $validation['message']);
            }
            return false;
        }
        return true;
    }  
    
    /**
     * Checks if provided value is one of allowed values. Comparison of values is nonstrict.
     * 
     * NOTE: If validation has no message then this method sets the default one.
     * If the validation has a message then this method replaces :allovedValues: insert
     * by the resolved allowed values.
     * 
     * Except of standart Model::validate_XYZ() method arguments following additional argument can be passed:
     * 
     * @param array $allowedValues Optional. Array of allowed field values. If not provided or empty
     *      then it is retrieved from DB or from schema. 
     */
    public function validate_fieldAllowedValues($fieldValue, $fieldName, $data, &$validation, $allowedValues = null) {
        if (empty($allowedValues)) {
            $allowedValues = null;
            $fieldOptions = DB::getFieldOptions($this->table, $fieldName);
            if (isset($fieldOptions['values'])) {
                $allowedValues = $fieldOptions['values'];
            }
            elseif (!empty($this->schema[$fieldName]['values'])) {
                $allowedValues = $this->schema[$fieldName]['values'];
            }
        }
        if (
            !empty($allowedValues)
            && !in_array($fieldValue, $allowedValues)
        ) {
            if (empty($validation['message'])) {
                $validation['message'] = __v(__FILE__, 'Invalid value');
            }
            else {
                $allowedValues = '\''. implode('\', \'', $allowedValues) . '\'';
                $validation['message'] = str_replace(':allovedValues:', $allowedValues, $validation['message']);
            }
            return false;
        }
        return true;
    }  
    
    /**
     * Checks if at least one of existing records has $requiredValue (e.g. at least one must be dafault)
     * 
     * @param mixed $requiredValue
     */
    public function validate_hasAtLeastOne($fieldValue, $fieldName, $data, &$validation, $requiredValue) {
        if ($fieldValue == $requiredValue) {
            return true;
        }
        $conditions = array();
        $conditions[$fieldName] = $requiredValue;
        if (!empty($data[$this->primaryKey])) {
            $conditions[$this->primaryKey . ' !='] = $data[$this->primaryKey];
        }
        $default = $this->findFirst(array('conditions' => $conditions));
        return !empty($default);
    }  
        
    /**
     * Checks if provide value is nonempty if specified $ifField has specified $ifFieldValue.
     * 
     * NOTE: This method will be used as specific replacement for 'notEmpty' validation rule
     * so do not forget set 'force' => true in validation specification, because defaultly 
     * on empty fields are no validations done
     * 
     * @param string $ifField Field name to check its value defined in $ifFieldValue. 
     *          If $ifFieldValue is not provided or NULL then it is checked if value 
     *          of $ifField is not empty.
     * @param string $ifFieldValue Optional. If provided and not NULL then this is
     *          checked field value. Defaults to NULL.
     */
    public function validate_notEmptyIf($fieldValue, $fieldName, $data, &$validation, $ifField, $ifFieldValue = null) {
        if (
            array_key_exists($ifField, $data)
            && (
                $ifFieldValue === null
                && !Validate::emptyValue($data[$ifField])
                ||
                $ifFieldValue !== null
                && $data[$ifField] == $ifFieldValue
            )
            && Validate::emptyValue($fieldValue)
        ) {
            return false;
        }
        return true;
    }
    
    /**
     * Checks if provided search keywords are valid (see the implementation)
     * 
     * @param string|array $keywords Search keywords string or array of parsed keywords
     * 
     * @return boolean
     */
    public function validate_searchKeywords($keywords) {
        if (!is_array($keywords)) {
            $keywords = self::parseSearchKeywords($keywords);
        }
        if (count($keywords) > 1) {
            return true;
        }
        foreach ($keywords as $keyword) {
            if (strlen($keyword) < 2) {
                return false;
            }
        }
        return true;
    }    
    
    /**
     * Creates the model table in database according to model table name and schema.
     * If the model is translated the translation table is created too.
     * 
     * @return bool TRUE on success, otherwise FALSE
     */
    public function createTable() {
        // check for model table
        if (empty($this->table)) {
            throw new Exception(
                'This method cannot be used as the model doesn\'t use any database table. ' 
                . 'Define your own version of this method or use this model only for data validations.'
            );
        }
        // check for creation of translation table
        $schema = $this->schema;
        $i18nSchema = array();
        if ($this->translatedFields) {
            // get translation table fields definitions array
            $i18nSchema = $this->getI18nSchema();
            // check for translations kept in the main table
            if ($this->translationTable == $this->table) {
                $schema = array_merge($schema, $i18nSchema);
                $i18nSchema = array();
            }
            else {
                $i18nSchema = array_merge(
                    array($this->table . '_' . $this->primaryKey => $this->schema[$this->primaryKey]),
                    $i18nSchema
                );
            }
        }
        $result = DB::createTable($this->table, $schema, array(
            'indexes' => $this->tableIndexes,
        ));
        // create translations table
        if ($i18nSchema) {
            $result = $result && DB::createTable($this->translationTable, $i18nSchema);
        }
        return $result;
    }
    
    /**
     * Generates table schema of translated fields and returns as array
     * 
     * @return array
     */
    public function getI18nSchema() {
        $i18nSchema = array();
        if ($this->translatedFields) {
            foreach ($this->translatedFields as $field) {
                foreach ($this->translationLangs as $i => $lang) {
                    if ($i === 'default') { //!!! $i == 'default' works wrong: 0 == 'default' is TRUE !!! because (int)'default' is 0
                        continue;
                    }
                    $translationField = self::getTranslationField($field, $lang);
                    $i18nSchema[$translationField] = $this->schema[$field];
//// according to experience on drinkcentrum.sk it is better to keep original schema properties    
//// See also Model::updateTableFields() > creation on translation table schema                
//                    // force following options on translation fields
//                    $i18nSchema[$translationField]['null'] = true;
//                    $i18nSchema[$translationField]['default'] = null;
//                    $i18nSchema[$translationField]['comment'] = null;
                }
            }
        }
        return $i18nSchema;
    }
    
    /**
     * Add table fields, which is not in db table and is defined in schema, with
     * translation fields included
     * 
     * @param type $autoFillTranslatedFields - if TRUE, method fill translation 
     *      fields with values from default language fields
     * 
     * @return array - new created field 
     */
    public function updateTableFields($autoFillTranslatedFields = false) {
        // check for model table
        if (empty($this->table)) {
            throw new Exception(
                'This method cannot be used as the model doesn\'t use any database table. ' 
                . 'Define your own version of this method or use this model only for data validations.'
            );
        }
        $actualTableFieldsSchema = DB::getFields($this->table);
        $actualTableFields = array_keys($actualTableFieldsSchema);
        $schema = $this->getPropertySchema();
        $i18nSchema = array();
        if ($this->translatedFields) {
            // get translation table fields definitions array
            $i18nSchema = $this->getI18nSchema();
            // check for translations kept in the main table
            if ($this->translationTable === $this->table) {
                $schema = array_merge($schema, $i18nSchema);
                $i18nSchema = array();
            }
            else {
                $actualI18nTableFieldsSchema = DB::getFields($this->translationTable);
                $actualI18nTableFields = array_keys($actualI18nTableFieldsSchema);
                $i18nSchema = array_merge(
                    array($this->table . '_' . $this->primaryKey => $this->schema[$this->primaryKey]),
                    $i18nSchema
                );
            }
        }
        
        $schemaFields = array_keys($schema);
        $i18nSchemaFields = array_keys($i18nSchema);
        
        // if table is empty, create table by classic method
        if (empty($actualTableFields)) {
            if ($this->createTable()) {
                return array_merge($schemaFields, $i18nSchemaFields);
            }
        }
        
        // add fields in main table
        $previousFields = array();
        $previousField = false; // if no previous field then place it as the first one
        foreach ($schemaFields as $fieldName) {
            $previousFields[$fieldName] = $previousField;
            $previousField = $fieldName;
        }
        $newSchemaFields = array_diff($schemaFields, $actualTableFields);
        if (!empty($newSchemaFields)) {
            foreach ($newSchemaFields as $fieldName) {
                $fieldOptions = $schema[$fieldName];
                DB::createTableField($this->table, $fieldName, $fieldOptions, array(
                    'after' => $previousFields[$fieldName],
                ));
            }
        }
        
        // add fields in translation table
        $newI18nSchemaFields = array();
        if (!empty($i18nSchema)) {
            $previousFields = array();
            $previousField = false; // if no previous field then place it as the firts one
            foreach ($schemaFields as $fieldName) {
                $previousFields[$fieldName] = $previousField;
                $previousField = $fieldName;
            }
            $newI18nSchemaFields = array_diff($i18nSchemaFields, $actualI18nTableFields);
            if (!empty($newI18nSchemaFields)) {
                foreach ($newI18nSchemaFields as $fieldName) {
                    $fieldOptions = $i18nSchema[$fieldName];
                    // erase index, it has been merged here above into table indexes
                    $fieldOptions['index'] = null; 
                    DB::createTableField($this->translationTable, $fieldName, $fieldOptions, array(
                        'after' => $previousFields[$fieldName],
                    ));
                }
            }
        }
        
        // if auto fill is set
        if ($autoFillTranslatedFields && !empty($this->translatedFields)) {
            foreach ($this->translatedFields as $field) {
                foreach ($this->translationLangs as $i => $lang) {
                    if ($i === 'default') {
                        continue;
                    }
                    $translationField = self::getTranslationField($field, $lang);
                    DB::query(
                        'UPDATE `' . $this->table . '` ' .
                        'SET `' . $translationField . '` = `' . $field . '` ' .
                        'WHERE `' . $translationField . '` IS NULL OR `' . $translationField . '` = "";'
                    );
                }
            }
        }
        
        return array_merge($newSchemaFields, $newI18nSchemaFields);
    }
    
    /**
     * Truncates the model table in database according to model table name.
     * If the model is translated the translation table is truncated too.
     * 
     * @return bool TRUE on success, otherwise FALSE
     */
    public function truncateTable() {
        $tables = array($this->table);
        // check for translation table
        if (
            $this->translatedFields 
            && $this->translationTable != $this->table
        ) {
            $tables[] = $this->translationTable;
        }
        return DB::truncateTables($tables);
    }
    
    /**
     * Drops the model table in database according to model table name.
     * If the model is translated the translation table is dropped too.
     * 
     * @return bool TRUE on success, otherwise FALSE
     */
    public function dropTable() {
        $tables = array($this->table);
        // check for translation table
        if (
            $this->translatedFields 
            && $this->translationTable != $this->table
        ) {
            $tables[] = $this->translationTable;
        }
        return DB::dropTables($tables);
    }
    
    /**
     * Returns array of inital model records to be used by method Model::loadInitialRecords()
     * 
     * @return array
     */
    public function getInitialRecords() {
        return array();
    }
    
    /**
     * Loads initial records returned by Model::getInitialRecords() into database table.
     * 
     * @param array $records Optional. Explicit records to be inserted. If not 
     *      defined then records returned by Model::getInitialRecords() are used.
     * @param array $options Model::save() options. Defaults to empty array().
     * 
     * @throws Exception if there is an validation error on insertion
     */
    public function loadInitialRecords($records = array(), $options = array()) {
        if (empty($records)) {
            $records = $this->getInitialRecords();
        }
        $options = (array)$options;
        $options['create'] = true;
        foreach($records as $i => $data) {
            if (!$this->save($data, $options)) {
                throw new Exception("Insertion of records has failed because of following error on record number {$i}: " . reset(reset($this->getErrors())));
            }
        }
    }
    
    /**
     * Inserts test records defined in Model:$testRecords property into database table
     * 
     * @param array $records Optional. Explicit records to be inserted. If not 
     *      defined then Model:testRecords are used.
     * @param array $options Model::save() options. Defaults to empty array().
     * 
     * @return bool TRUE on success
     * 
     * @throws Exception if $records are not specified and no $testRecords are defined in current model
     *      or if there is an validation error on insertion
     */
    public function loadTestRecords($records = array(), $options = array()) {
        if (empty($records)) {
            if (empty($this->testRecords)) {
                throw new Exception("No test records specified in model {$this->name}");
            }
            $records = $this->testRecords;
        }
        $options = (array)$options;
        $options['create'] = true;
        foreach($records as $i => $data) {
            if (!$this->save($data, $options)) {
                throw new Exception("Insertion of test records has failed because of following error on record number {$i}: " . reset(reset($this->getErrors())));
            }
        }
        return true;
    }
    
    /**
     * Sanitizes provided $keywordsString, means characters \/'"%; are removed
     * 
     * @param string $keywordsString
     * 
     * @return string Sanitized string
     */
    public static function sanitizeSearchKeywords($keywordsString) {
        return preg_replace('/[\/\\\'\"\%\;]/', '', (string)$keywordsString);
    }
    
    /**
     * Parses provided $keywordsString into array of keywords
     * 
     * @param string $keywordsString
     * 
     * @return array Array of parsed keywords
     */
    public static function parseSearchKeywords($keywordsString) {
////keywords sanitization brokes some searches (e.g. search for code "123/456")
////finaly it is up to user to provide correct keywords      
//        $keywordsString = self::sanitizeSearchKeywords($keywordsString);
        $keywords = array_unique(array_filter(explode(' ', $keywordsString)));
        return $keywords;
    }
    
    /**
     * Returns relevance SQL used in search queries. Input relevance expressions
     * must have following format:
     * 
     *      array(
     *          '{relevenceExpression01}'[ => {logicalOperator} | 'PHRASE'],
     *          '{relevenceExpression01}'[ => {logicalOperator} | 'PHRASE'],
     *          ...
     *      )
     * 
     * The logicalOperator ('OR', 'AND') or 'PHRASE' is optional. If not provided then defaults to 'OR'.
     * Logical operator is used between relevance expression generated for many keywords.
     * If relevance expression contains another logical operators then it is up to 
     * user to place brackets round this expression. If 'PHRASE' then the relevance 
     * expression applies for the original search phrase (not parsed to keywords). 
     * There is no need to distinguish between 'AND' and 'OR' logical operator as 
     * the phrase is just a single "keyword", so expressions are not joined by operator.
     * In relevance expressions all inserts ":k:" will be replaced by provided phrase
     * or keywords. E.g.
     * 
     *      array(
     *          // matchs whole name for phrase
     *          'EshopProduct.name = ":k:"' => 'PHRASE',
     *          // matchs beginning of name for phrase
     *          'EshopProduct.name LIKE ":k:%"' => 'PHRASE',
     *          // matchs whole word in name for all keywords
     *          '(EshopProduct.name LIKE "% :k:" OR EshopProduct.name LIKE ":k: %" OR EshopProduct.name LIKE "% :k: %")' => 'AND',
     *          // matchs anywhere in name for all keywords
     *          'EshopProduct.name LIKE "%:k:%"' => 'AND',
     *          // matchs whole name for one of keywords
     *          'EshopProduct.name = ":k:"',
     *          // matchs whole word in name for one of keywords
     *          '(EshopProduct.name LIKE "% :k:" OR EshopProduct.name LIKE ":k: %" OR EshopProduct.name LIKE "% :k: %")',
     *      );
     * 
     * ATTENTION: 'PHRASE' relevance expressions are considered only if $phrase is not 
     * empty string and there is more than one keyword. In a case of one keyword the phrase 
     * equals to the single keyword and this sould be resolved by some lower 'OR' relevance expession.
     * 
     * ATTENTION: 'AND' relevance expressions are considered only if there is more than one keyword. 
     * In a case of a single keyword no logical 'AND' is applied and this sould be resolved by some 
     * lower 'OR' relevance expession.
     * 
     * @param array $relevanceExpressions Relevance expressions as described here above.
     * @param string $phrase The original search phrase used as value for ":k:" inserts
     *          in relevence expresions marked as 'PHRASE'.
     * @param array $keywords Keywords used as value for ":k:" inserts in relevance
     *          expressions which are NOT marked as 'PHRASE'.
     * 
     * @return string Relevance SQL which can be used for 'order' or 'fields' find option.
     *      If used in 'fields' option then an alias should be added to returned SQL,
     *      e.g. `$relevanceSql . ' AS _relevance'`. If used in 'order' option then 
     *      a direction ('DESC' for ordering from most relevant) should be added to returned SQL,
     *      e.g. `$relevanceSql . ' DESC'`.
     *      ATTENTION: It is a SQL literal so you must set it not only as 'order' or 'fields'
     *      in find options but also add in 'literals' > 'order' or 'literals' > 'fields' option.
     */
    public static function getSearchRelevance(
        $relevanceExpressions,
        $phrase,
        $keywords
    ) {        
        $relevanceExpressions = (array)$relevanceExpressions;
        $phrase = trim($phrase);
        $keywords = (array)$keywords;
        $keywordsCount = count($keywords);
        if (
            $keywordsCount > 1
            && $phrase !== ''
        ) {
            $phrase = DB::escape($phrase);                
        }
        
        // apply phrase and keywords on relevance expressions
        $finalRelevanceExpressions = array();
        $i = 0;
        foreach ($relevanceExpressions as $k => $v) {
            if (is_int($k)) {
                $expression = $v;
                $operator = 'OR';
            }
            else {
                $expression = $k;
                $operator = strtoupper($v);
            }
            if (
                $operator === 'PHRASE'
                && $keywordsCount > 1
                && $phrase !== ''
            ) {
                $expression = preg_replace(
                    '/([a-z_]+)\.([a-z_]+)/i',
                    str_replace(
                        ':e:', 
                        DB::getPropertyNameEnclosure(), 
                        ':e:$1:e:.:e:$2:e:'
                    ),
                    $expression
                );
                $finalRelevanceExpressions[$i] = str_replace(':k:', $phrase, $expression);
                $i++;
            }
            elseif (
                $operator === 'OR'
                ||
                $operator === 'AND'
                && $keywordsCount > 1
            ) {
                foreach($keywords as $keyword) {
                    $keyword = DB::escape($keyword);
                    if (!empty($finalRelevanceExpressions[$i])) {
                        $finalRelevanceExpressions[$i] .= ' ' . $operator . ' ';
                    }
                    else {
                        $finalRelevanceExpressions[$i] = '';
                    }
                    $expression = preg_replace(
                        '/([a-z_]+)\.([a-z_]+)/i',
                        str_replace(
                            ':e:', 
                            DB::getPropertyNameEnclosure(), 
                            ':e:$1:e:.:e:$2:e:'
                        ),
                        $expression
                    );
                    $finalRelevanceExpressions[$i] .= str_replace(':k:', $keyword, $expression);
                    // in case of 'OR' the later keywors has lower relevance
                    if ($operator === 'OR') {
                        $i++;
                    }
                }
                // skip 'OR' as it is treated here above
                if ($operator !== 'OR') {
                    $i++;
                }
            }
        }
        $relevanceSql = '';
        if ($finalRelevanceExpressions) {
            $relevenaceLevelsCount = count($finalRelevanceExpressions);
            foreach($finalRelevanceExpressions as $i => $expression) {
                $relevanceSql .= 'IF (' . $expression . ', ' . ($relevenaceLevelsCount - $i) . ', ';
            }
            $relevanceSql .= '0' . str_repeat(')', $relevenaceLevelsCount);
        }
        
        return $relevanceSql;
    }
    
    /**
     * Returns search conditions which can be used in search queries.
     * 
     * @param array $searchFields Array field names the provided keywords should 
     *      be searched in. It is up to user to provide properly qualified field names.
     * @param array $keywords Array of keywords to be searched in specified search fields.
     * @param array $options Following are available:
     *      - 'strict' (bool) If TRUE then each single search field must contain 
     *          all provided keywords (AND). If FALSE then it is enough if each 
     *          single search field contains only one of provided keywords (OR).
     *          This case is applicable esspecially together with relevance order 
     *          (see ::getSearchRelevance()) when the results containing all
     *          keawords are placed by relevance as first (of course if relevances are
     *          defined well). Defaults to TRUE.
     * 
     * @return array Array of find conditions which can be used as 'conditions' find option.
     */
    public static function getSearchConditions($searchFields, $keywords, $options = array()) {
        $options = array_merge(array(
            'strict' => true,
        ), $options);
        
        $conditions = array();
        foreach ($searchFields as $field) {
            $keywordConditions = array();
            foreach($keywords as $keyword) {
                if (
                    !$options['strict']
                    && $keywordConditions
                ) {
                    $keywordConditions[] = 'OR';
                }
                // avoid overwriting of the same keys
                $keywordConditions[] = array($field . ' *~*' => $keyword);
            }
            if ($conditions) {
                $conditions[] = 'OR';
            }
            $conditions[] = $keywordConditions;
        }
        
        return $conditions;
    }
    
    /**
     * Translates translated fields of specified records.
     * 
     * ATTENTION: Translated records are neither normalized nor validated except of
     * custom normalization available through 'normalize' option.
     * 
     * @param string $targetLang Target lang code, e.g 'en'.
     * @param array $options Following are available:
     *      - 'sourceLang' (string) Defalts to App::$lang.
     *      - 'rewrite' (bool) If TRUE then target lang version of translated fields
     *          are considered to be also the source and their values in sourceLang
     *          are rewritten by translations in targetLang. Or in other words: When rewriting
     *          then the source texts are stored already in target fields and they just 
     *          need to be translated. Defalts to FALSE.
     *      - Almost all options of Model::find() method are available, above all: 'conditions',
     *          'fields', 'allowFields', 'avoidFields', 'order'. ATTENTION: 'fields', 
     *          'allowFields', 'avoidFields' must be provided in qualified form.
     *          ATTENTION: Options 'lang' and 'translate' are forced internally.
     *          ATTENTION: If no 'conditions' are provided then "automatic" translation
     *          conditions are genetated based on Model::$translatedField. If the
     *          actual model does not have this field in schema then an exception is raised.
     *      - 'normalize' (function) Anonymous function having translated record on its input
     *          and returning normalized translated record. Defaults to NULL (no normalization
     *          is done)
     *      - 'microtimeReserve' (int) Time reserve in miliseconds to stop the processing 
     *          if free microtime is under this value. Defaults to 20000.
     *      - 'reserve' (bool|string) If TRUE then the translation is executed only if process 
     *          reservation succeed. If a tring then passed as reservation name to App::reserveProcessing().
     *          Defaults to TRUE.
     *      - 'progress' (array&) Aux output. Must be provided by reference.
     *          Returns progress array containing keys 'count', 'translatedCount',
     *          'translatedIds', 'skippedCount', 'skippedIds', 'failedCount', 'failedIds',
     *          'errors', 'interrulted'.
     * 
     * @return bool TRUE if all specified records has been translated. FALSE if not
     *      (if the process has been interrupted).
     * 
     * @throws Exception
     */
    public function translate($targetLang, $options = array()) {
        $options = array_merge(array(
            'sourceLang' => App::$lang,
            'rewrite' => false,
            'fields' => null,
            'conditions' => null,
            'normalize' => null,
            'microtimeReserve' => 20000,
            'reserve' => true,
        ), $options);
        
        // reserve processing
        $reservationName = null;
        if ($options['reserve']) {
            $reservationName = $this->name . '::' . 'translate()';
            if (is_string($options['reserve'])) {
                $reservationName = $options['reserve'];
            }
            App::reserveProcessing($reservationName);
        }
        
        try {
            if (!$options['sourceLang']) {
                throw new Exception(__e(__FILE__, 'Provide source lang'));
            }
            if (!$targetLang) {
                throw new Exception(__e(__FILE__, 'Provide target lang'));
            }

            // set aux output
            $options['progress'] = array(
                'count' => 0,
                'translatedCount' => 0,
                'translatedIds' => array(),
                'skippedCount' => 0,
                'skippedIds' => array(),
                'failedCount' => 0,
                'failedIds' => array(),
                'errors' => array(),
                'interrupted' => false,
            );
            // make a shortcut reference
            $progress = &$options['progress'];

            // get fields to translate (take only text (string) fields but not ENUM and SET).
            // Fields are qualified by model name to allow automatic generation of lang versions 
            // of field names behind the scenes
            $options['fields'] = (array)$options['fields'];
            if (!$options['fields']) {
                $options['fields'] = array();
                foreach ($this->translatedFields as $translatedField) {
                    if (
                        ($fieldOptions = $this->schema[$translatedField])
                        && !empty($fieldOptions['type'])
                        && DB::isStringDataType($fieldOptions['type'])
                        && strtoupper($fieldOptions['type']) !== 'ENUM'
                        && strtoupper($fieldOptions['type']) !== 'SET'
                    ) {
                        $options['fields'][] = $this->name . '.' . $translatedField;
                    }
                }
                if (!$options['fields']) {
                    if ($reservationName) {
                        App::unreserveProcessing($reservationName);
                    }
                    return $progress['interrupted'] === false;
                }
            }

            // get conditions
            if (!$options['conditions']) {
                if (!$this->allowsAutomaticTranslation) {
                    throw new Exception(__e(
                        __FILE__, 
                        'It is not possible to generate automatic translation conditions on model %s. To allow this functionality add field "%s" to %s::$schema and %s::$translatedFields.',
                        $this->name,
                        $this->translatedField,
                        $this->name,
                        $this->name
                    ));
                }
                $translatedFieldName = $this->translatedField;
                if ($options['rewrite']) {
                    $translatedFieldName = $this->name . '.' . $translatedFieldName;
                }
                else {
                    // translatedField name is converted directly to its lang version to have 
                    // possibility address two langs in a single query:
                    // - 'fields' values are returned in sourceLang
                    // - 'conditions' are considered in targetLang
                    $translatedFieldName = self::getTranslationField($translatedFieldName, $options['targetLang']);
                }
                $options['conditions'] = array(
                    $translatedFieldName => 0,
                );
            }

            // if we are going to rewrite then the source texts are stored already 
            // in target fields and they just need to be translated
            if ($options['rewrite']) {
                $originalLang = App::setI18n($targetLang);
            }
            else {
                $originalLang = App::setI18n($options['sourceLang']);
            }
            // check for primary key (id) field presence and if not present then add it
            if (
                !in_array($this->name . '.' . $this->primaryKey, $options['fields'])
                && !in_array($this->primaryKey, $options['fields'])
            ) {
                $options['fields'][] = $this->name . '.' . $this->primaryKey;
            }
            // force defaults (unset) for following
            unset($options['lang']);
            unset($options['translate']);
            $records = $this->find($options);
            $progress['count'] = count($records);
            if (!$options['rewrite']) {
                App::setI18n($targetLang);
            }
            foreach ($records as $record) {
                // check remaining time - finish the processing before php 
                // would be interrupted by execution time constrain
                if (App::getFreeMicrotime() < $options['microtimeReserve']) {
                    $progress['interrupted'] = true;
                    break;
                }

                $id = $record[$this->primaryKey];
                try {
                    // remove from record primary key and all empty values
                    unset($record[$this->primaryKey]);
                    $record = array_filter($record);
                    // go to next if there is nothing to translate
                    if (!$record) {
                        $progress['skippedCount']++;
                        $progress['skippedIds'][] = $id;
                        continue;
                    }
                    $translatedRecord = Str::translate($record, $targetLang, $options);
                    $translatedRecord[$this->primaryKey] = $id;
                    $translatedRecord[$this->translatedField] = 1;
                    if (Validate::callableFunction($options['normalize'])) {
                        $translatedRecord = $options['normalize']($translatedRecord);
                    }                
                    $this->save($translatedRecord, array(
                        'normalize' => false,
                        'validate' => false,
                    ));
                    $progress['translatedCount']++;
                    $progress['translatedIds'][] = $id;
                } 
                catch (Throwable $e) {
                    if ($e instanceof Exception_Str_TranslateFailure) {
                        App::setI18n($originalLang);
                        throw $e;
                    }
                    $progress['failedCount']++;
                    $progress['failedIds'][] = $id;
                    $progress['errors'][] = $e->getMessage();
                }
            }
            App::setI18n($originalLang);
        } 
        catch (Throwable $e) {
            if ($reservationName) {
                App::unreserveProcessing($reservationName);
            }
            throw $e;
        }
        if ($reservationName) {
            App::unreserveProcessing($reservationName);
        }
        return $progress['interrupted'] === false;
    }
}