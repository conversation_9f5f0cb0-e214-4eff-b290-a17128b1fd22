<?php
class Controller extends ModuleObject {
       
    /**
     * Controller dispatch action 
     * 
     * @var string
     */
    protected $action;
        
    /**
     * Controller dispatch params 
     * 
     * (see App::params)
     * 
     * @var array
     */
    protected $params;
    
    /**
     * Controller dispatch data 
     * 
     * (see App::$data)
     * 
     * @var array
     */
    protected $data;
                
    /**
     * Controller dispatch args 
     * 
     * (see App::args)
     * 
     * @var array
     */
    protected $args;
    
    /**
     * If TRUE then origin comment of loaded controller action is displayed. It is intended to 
     * be set directly by the action itself like $this->displayOriginComment = TRUE.
     *
     * @var bool
     */
    protected $displayOriginComment = false;
    
    /**
     * If TRUE then HTML code returned by an action (and its corresponding CSS
     * and JS code) is sent directly and exclusively to output. "Directly" means 
     * that the returned HTML is echoed to default output buffer and the script exits. 
     * "Exclusively" means that there is no other HTML generated to default output buffer as:
     *      - all previously generated output HTML is ignored (thrown away) 
     *      - generation of following HTML code is stopped (the script exits)
     *      - there is no layout applied (the script exits)
     * 
     * It is intended to be set directly by the action itself like $this->forceExclusiveDirectOutput().
     * It must be called just before the exclusive HTML output is generated (to 
     * clear all previously generated CSS and JS code). Only one action per page 
     * (web content) can use this possibility.
     * 
     * @var bool 
     */
    private $forceExclusiveDirectOutput = false;
    
    /**
     * If called then HTML code returned by an action (and its corresponding CSS
     * and JS code) is sent directly and exclusively to output. "Directly" means 
     * that the returned HTML is echoed to default output buffer and the script exits. 
     * "Exclusively" means that there is no other HTML generated to default output buffer as:
     *      - all previously generated output HTML is ignored (thrown away) 
     *      - generation of following HTML code is stopped (the script exits)
     *      - there is no layout applied (the script exits)
     * 
     * It is intended to be set directly by the action itself like $this->forceExclusiveDirectOutput().
     * It must be called just before the exclusive HTML output is generated (to 
     * clear all previously generated CSS and JS code). Only one action per page 
     * (web content) can use this possibility.
     */
    protected function forceExclusiveDirectOutput() {
        $this->forceExclusiveDirectOutput = true;
        App::clearCss();
        App::clearJs();
    }
    
    public function __construct() {
        parent::__construct();
        
        // set icon for tabs in admin
        if (App::$actionType === 'admin') {
            $icon = App::getAdminIcon($this->module, $this->name);
            if (!empty($icon)) {
                App::setTabIcon($icon);
            }
        }
        
////mojo: let adding controllers into app cache only to method App::loadController(). Some controllers 
//          can be created with params in construct method and they cannot be recycled for
//          general use. Method App::loadController() will never pass an arg to construct method
//          and it can be used only to get unparametrized instances.     
//          
//        // cache the first model instance for later cooperation between models
//        App::setController($this->module, $this->name, $this);
    }

    /**
     * Loads specified action with provided params and data
     * 
     * @param string $action Controller action method name.
     * @param array $params Controller dispatch params.
     * @param array $data Controller dispatch data supposed to be saved.
     * @param array|mixed $args Controller dispatch args or single arg int|float|string. 
     *      They are used as input arguments for $action method
     * @param bool $allowOriginComments Optional. If TRUE then action origin comments are
     *      allowed. To display origin comment set property $this->displayOriginComment to TRUE in action.
     *      Defaults to TRUE.
     * 
     * @return mixed Returns the output of launched controller->action()
     */
    public function loadAction($action, $params, $data, $args, $allowOriginComments = true) {
        $this->action = $action;
        $this->params = (array)$params;
        $this->data = (array)$data;
        $this->args = (array)$args;
        // check if provided action exists
        if (!method_exists($this, $this->action)) {
            throw new Exception(__e(
                __FILE__, 
                'Action %s does not exist.',
                "{$this->module}/controllers/{$this->name}.{$this->action}()"
            ));
        }
        $ActionReflection = new ReflectionMethod($this, $this->action);
        if ($ActionReflection->isStatic()) {
            throw new Exception(__e(
                __FILE__, 
                'Action %s must not be defined as static method.',
                "{$this->module}/controllers/{$this->name}.{$this->action}()"
            ));
        }
        if (!$ActionReflection->isPublic()) {
            throw new Exception(__e(
                __FILE__, 
                'Action %s must be defined as public method.',
                "{$this->module}/controllers/{$this->name}.{$this->action}()"
            ));
        }
        // call the action with passed params on its input 
        //ob_start(); - DO NOT catch output buffer because generation of data for downloading will not work
        $this->displayOriginComment = false;
        try {
            $output = call_user_func_array(array($this, $this->action), (array)Sanitize::value($this->args));
        }
        catch (Throwable $e) {
            App::catchThrowable($e);
            $output = '';
        }
        if ($this->forceExclusiveDirectOutput  && !empty($output)) {
            $output .= App::getCssCode(true) . App::getJsCode(true);
        }
        //ob_end_clean();
        if ($allowOriginComments && !empty($this->displayOriginComment) && !empty($output)) {
            $output = 
                '<!--  ' . $this->module . DS . 'controllers'. DS . $this->name . '::' . $this->action . '() -->'
                . $output
                . '<!--/ ' . $this->module . DS . 'controllers'. DS . $this->name . '::' . $this->action . '() -->';
        }
        if ($this->forceExclusiveDirectOutput) {
            App::endPhpErrorsHtmlCapture(array(
                'closeOpenedOutputBuffers' => true
            ));
            echo $output;
            exit();
        }
        return $output;
    }
    
    /**
     * Checks if there are bulk action record ids passed in $_GET['ids'] param.
     * 
     * ATTENTION: This method is intended for internal use by controller admin bulk actions.
     * 
     * @param string $_GET['ids'] Comma separated list of product ids. 
     * 
     * @return bool 
     */
    protected function hasBulkActionIds() {
        return !empty($_GET['ids']);
    }
    
    /**
     * Returns list of bulk action record ids parsed from $_GET['ids'] param.
     * 
     * ATTENTION: This method is intended for internal use by controller admin bulk actions.
     * 
     * @param string $_GET['ids'] Comma separated list of product ids. 
     * 
     * @return array|bool List of record ids. FALSE if $_GET['ids'] is empty.
     */
    protected function getBulkActionIds() {
        if (empty($_GET['ids'])) {
            return false;
        }
        $ids = explode(',', $_GET['ids']);
        $ids = array_map('trim', $ids);
        return array_filter($ids);
    }
    
    /**
     * Returns action lang from $_GET['lang'] param. 
     * 
     * ATTENTION: If $_GET['lang'] is not set or empty then a redirect is made to 
     * the actual URL with GET param 'lang' set to actual App::$lang. This is made
     * to make lang dropdown in Html::smartIndex() and smartForm() work correctly.
     * 
     * ATTENTION: This method is intended for internal use by controller admin actions.
     * 
     * @param string $_GET['lang'] Comma separated list of product ids. 
     * 
     * @return string Lang .
     */
    protected function getActionLang() {
        if (
            empty($_GET['lang'])
            && App::$lang !== DEFAULT_LANG
        ) {
            App::redirect(App::rebuildUrl(App::$url, array(
                'get' => array(
                    'lang' => App::$lang,
                ),
                'mergeGet' => true,
            )));
        }
        $lang = !empty($_GET['lang']) ? $_GET['lang'] : DEFAULT_LANG;
        return $lang;
    }
}
