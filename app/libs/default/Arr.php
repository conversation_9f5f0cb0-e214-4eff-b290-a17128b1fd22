<?php
class Arr {
    /**
     * Strips slashes recursively from all string keys and values in provided array.
     * 
     * @param array $array
     * @return array 
     */
    static function stripSlashesRecursive($array) {
        foreach($array as $key => $value) {
            if (is_string($key)) {
                $key = stripslashes($key);
            }
            if (is_string($value)) {
                $value = stripslashes($value);
            }
            elseif (is_array($value)) {
                $value = self::stripslashesRecursive($value);
            }
            unset($array[$key]);
            $array[$key] = $value;
        }
        return $array;
    }
    
    /**
     * Merges recursively two provided arrays. Recursively are merged only corresponding 
     * associative items which values are arrays on both sides. If one of corresponding 
     * associative items is not an array then a plain merge is done, means the value of $arrayB is used.
     * 
     * NOTE: Its behaviour is different from array_merge_recursive() which merges all 
     * associative items (regardless of their value type).
     * 
     * @param array $arrayA
     * @param array $arrayB
     * 
     * @return array 
     */
    static function mergeRecursive($arrayA, $arrayB) {
        $result = array_merge($arrayA, $arrayB);
        foreach($result as $k => &$v) {
            if (
                is_string($k)
                && isset($arrayA[$k])
                && isset($arrayB[$k])
                && is_array($arrayA[$k])
                && is_array($arrayB[$k])
            ) {
                $v = self::mergeRecursive($arrayA[$k], $arrayB[$k]);
            }
        }
        return $result;
    }
    
    /**
     * Filters elements of an array recursively using a callback function
     * 
     * @param array $array
     * @param callable $callback See https://www.php.net/manual/en/function.array-filter.php
     * @param int $mode See https://www.php.net/manual/en/function.array-filter.php
     * 
     * @return array
     */
    static function filterRecursive($array, $callback = null, $mode = 0) {
        foreach ($array as &$value) { 
            if (is_array($value)) { 
                $value = self::filterRecursive($value, $callback, $mode); 
            } 
        } 
        if (empty($callback)) {
            return array_filter($array);         
        }
        return array_filter($array, $callback, $mode);         
    }
    
    /**
     * Inflates an array e.g.:
     * 
     *      array(
     *          'EshopProduct.name' => '...',
     *          'EshopProduct.id' => '...',
     *          'EshopAuthor.name' => '...',
     *          'EshopAttribute.0.id' => '...',
     *          array(
     *              'EshopGroup.name' => '...' 
     *          )
     *          ...
     *      )   
     * 
     * to its nested form:
     * 
     *      array(
     *          'EshopProduct' => array(
     *             'name' => '...',
     *             'id' => '...',
     *          ),
     *          'EshopAuthor' => array(
     *              'name' => '...',
     *          ),
     *          'EshopAttribute' => array(
     *              array(
     *                  'id' => '...',
     *              )
     *          ),
     *          array(
     *              'EshopGroup' => array(
     *                  'name' => '...'
     *              )
     *          )
     *          ...
     *      )  
     * 
     * Any number of nested levels is possible. Method is recursing till first associative
     * level. This allows to comfortably inflate array of deflated arrays.
     * 
     * NOTE: This is oposite method to Arr::deflate().
     *  
     * @param array& $flatArray Passed by reference. Flat array to be inflated
     * @param string $options Followinf are available:
     *       - 'separator' (string) separator between nested leves in 1D array keys.
     *          Defaults to '.'.
     * 
     * @return array 
     */
    static public function inflate(&$flatArray, $options = array()) {
        $defaults = array(
            'separator' => '.'
        );
        $options = array_merge($defaults, $options);
        $inflatedArray = array();
        foreach($flatArray as $flatKeys => $value){
            // if the key is numeric then recurse into deeper level
            // !!! numeric keys created using string, e.g. array('2' => ...) are 
            // converted to int values means the real created array looks like array(2 => ...)
            if (is_int($flatKeys) && is_array($value)) {
                $inflatedArray[$flatKeys] = self::inflate($value, $options);
            }
            else {
                // create possible nested array
                $keys = explode($options['separator'], $flatKeys);
                $subArray = &$inflatedArray;
                foreach($keys as $key) {
                    if(!array_key_exists($key, $subArray)){
                        $subArray[$key] = array();
                    }
                    $subArray = &$subArray[$key];
                }
                // assign value to created array item
                $subArray = $value;
            }
        }
        return $inflatedArray;
    }   
    
    /**
     * Deflates nested array e.g.:
     * 
     *      array(
     *          'EshopProduct' => array(
     *             'name' => 'Product name',
     *             'id' => 25,
     *          ),
     *          'EshopAuthor' => array(
     *              'name' => 'Author name',
     *          ),
     *          'EshopAttribute' => array(
     *              array(                      // this will be deflated even if $ignorePlain is TRUE because it is nested in associative item
     *                  'id' => 45,
     *              )
     *          ),
     *          array(                          // plain item (nonasociative) will not be deflated if $ignorePlain is TRUE
     *              'EshopGroup' => array(
     *                  'name' => 'Group name'
     *              )
     *          ),
     *          'justAnEmptyArray' => array(),  // end-point empty arrays are let untouched in flat array
     *          array(),
     *          ...
     *      )  
     * 
     * to its flat form if option 'ignorePlain' is TRUE:
     * 
     *      array(
     *          'EshopProduct.name' => 'Product name',
     *          'EshopProduct.id' => 25,
     *          'EshopAuthor.name' => 'Author name',
     *          'EshopAttribute.0.id' => 45,
     *          0 => array(
     *              'EshopGroup.name' => 'Group name' 
     *          ),
     *          'justAnEmptyArray' => array(),
     *          1 => array(),
     *          ...
     *      )   
     * 
     * or to its flat form if option 'ignorePlain' is FALSE:
     * 
     *      array(
     *          'EshopProduct.name' => 'Product name',
     *          'EshopProduct.id' => 25,
     *          'EshopAuthor.name' => 'Author name',
     *          'EshopAttribute.0.id' => 45,
     *          '0.EshopGroup.name' => 'Group name',
     *          'justAnEmptyArray' => array(),
     *          1 => array(),
     *          ...
     *      )   
     * 
     * Any number of nested levels is possible. 
     * 
     * ATTENTION: End-point empty arrays are let untouched in flat array to get
     * the same $array after Arr::inflate() is applied on resulting flat array.
     * There is no new nesting level in empty array so there nothing to deflate there.
     * 
     * NOTE: This is oposite method to Arr::inflate().
     * 
     * @param array& $array Passed by reference. Array to be deflated
     * @param array $options Following are available:
     *      - 'separator' (string) Separator to create flat keys. Defaults to '.'.
     *      - 'ignorePlain' (bool|int) If TRUE then plain (nonassociative) items
     *          are not deflated. If FALSE the all items are deflated. If integer
     *          then it defines number of plain levels from top to be ignored.
     *          ATTENTION: once the plain item is nested under associative item, then
     *          it is deflated always. Defaults to TRUE.
     *      - 'levels' (integer) Number to level to be deflated. If 0 then
     *           all levels are deflated. If 1 then the array stays unchanged. Defaults to 0. 
     * 
     * @return array Deflated array
     */
    static public function deflate(&$array, $options = array(), /* internal use */ &$flatArray = array(), $flatKey = null) {  
        $defaults = array(
            'separator' => '.',
            'ignorePlain' => true,
            'levels' => 0,
        );
        $options = array_merge($defaults, $options);
        $options['levels']--;
        $ignorePlain = $options['ignorePlain'];
        if (
            $options['ignorePlain'] !== true
            && $options['ignorePlain'] !== false
            && $options['ignorePlain'] > 0
        ) {
            $options['ignorePlain']--;
        }
        foreach ($array as $key => $value) {
            if ($flatKey !== null) {
                $key = $flatKey . $options['separator'] . $key;
            }
            if (
                $options['levels'] !== 0
                && is_array($value)
                && !empty($value)
            ) {
                if ($ignorePlain && is_int($key)) {
                    $flatArray[$key] = self::deflate($value, $options);
                }
                else {
                    self::deflate($value, $options, $flatArray, $key);
                }
            }
            else {
                $flatArray[$key] = $value;
            }
        }
        // retrun but only at the level of directly called method
        if ($flatKey === null) {
            return $flatArray;
        }
    }
    
    /**
     * Deflates provided get params, e.g.:
     * 
     *      array(
     *          'data' => array(
     *              'id' => 25,
     *              'name' => 'admin',
     *              'Profile' => array(
     *                  'address' => 'Baker street 20/A',
     *              )
     *          )
     *      )
     * 
     * is deflated to 
     * 
     *      array(
     *          'data[id]' => 25,
     *          'data[name]' => 'admin',
     *          'data[Profile][address]' => 'Baker street 20/A',
     *      )
     *      
     * @param array $getParams Array of get params to be deflated
     * 
     * @return array Flat get params array
     */
    static public function deflateGetParams($getParams) {
        $flatGetParams = array();
        foreach ($getParams as $k => $v) {
            if (!is_array($v)) {
                $flatGetParams[$k] = $v;
                continue;
            }
            $v = self::deflate($v, array('separator' => ']['));
            foreach($v as $vk => $vv) {
                $flatKey = $k . '[' . $vk . ']';
                $flatGetParams[$flatKey] = $vv;
            }
        }
        return $flatGetParams;
    }
    
    /**
     * Deflates provided data, e.g.:
     * 
     *      array(
     *          'id' => 1,
     *          'username' => 'Admin',
     *          'password' => '53cr3t',
     *          'run_groups_ids' => array(1, 2),
     *          'UserProfile' => array(
     *              'firstname' => 'Ján',
     *              'lastname' => 'Šimko',
     *              'hobbies' => array('bike', 'flute', 'pc'),
     *          ),
     *          'Eshop' => array(
     *              'EshopUserProfile' => array(
     *                  'address' => 'Some street 01',
     *              ),
     *          ),
     *      )
     * 
     * is deflated to 
     * 
     *      array(
     *          'id' => 1,
     *          'username' => 'Admin',
     *          'password' => '53cr3t',
     *          'run_groups_ids' => array(1, 2),
     *          'UserProfile.firstname' => 'Ján',
     *          'UserProfile.lastname' => 'Šimko',
     *          'UserProfile.hobbies' => array('bike', 'flute', 'pc'),
     *          'Eshop.EshopUserProfile.address' => 'Some street 01',
     *      )
     * 
     * Means only fields and their preceding qualifiers are deflated. Qualifiers 
     * are recognized by first capital letter (A-Z). Max 3 levels are deflated = 
     * 2 qualifiers and one field ('Module.Model.field') 
     * 
     * @param array $data Data to deflate
     * @param array $options Following are available:
     *      - 'separator' (string) Separator to create flat keys. Defaults to '.'.
     * 
     * @return array Deflated data array (see above)
     */
    public static function deflateData($data, $options = array()) {
        $defaults = array(
            'separator' => '.'
        );
        $options = array_merge($defaults, $options);
        $flatData = array();
        foreach($data as $k1 => $v1) {
            if (is_string($k1)) {
                // check if $k1 is qualifier ('MyModel') = if its first letter code of is capital (A-Z)
                $k1FirstLetterCode = ord(substr($k1, 0, 1));
                $k1IsQualifier = $k1FirstLetterCode >= 65 && $k1FirstLetterCode <= 90;
                if ($k1IsQualifier && is_array($v1)) {
                    // check if $k1 is not already deflated
                    $k1Deflated = false;
                    if (strpos($k1, $options['separator']) !== false) {
                        $k1Parts = explode($options['separator'], $k1);
                        $k1PartsCount = count($k1Parts);
                        if ($k1PartsCount >= 3) {
                            $k1Deflated = true;
                        }
                        // there is just 2 parts
                        else { 
                            $k1Part2LetterCode = ord(substr($k1Parts[1], 0, 1));
                            $k1Part2IsQualifier = $k1Part2LetterCode >= 65 && $k1Part2LetterCode <= 90;
                            if ($k1Part2IsQualifier) {
                                $k1Deflated = 1; // there is first deflation level done and still possibility to add field name
                            }
                            else {
                                $k1Deflated = true;
                            }
                        }
                    }
                    if ($k1Deflated !== true) {
                        foreach ($v1 as $k2 => $v2) {
                            if (is_string($k2)) {
                                // check if $k2 is qualifier ('MyModel') = if its first letter code of is [A-Z]
                                $k2FirstLetterCode = ord(substr($k2, 0, 1));
                                $k2IsQualifier = $k2FirstLetterCode >= 65 && $k2FirstLetterCode <= 90;
                                if (!$k1Deflated && $k2IsQualifier && is_array($v2)) {
                                    foreach($v2 as $k3 => $v3) {
                                        if (is_string($k3)) {
                                            $flatData[$k1 . $options['separator'] . $k2 . $options['separator'] . $k3] = $v3;
                                        }
                                        else {
                                            $flatData[$k1][$k2][$k3] = $v3;
                                        }
                                    }
                                }
                                else {
                                    $flatData[$k1 . $options['separator'] . $k2] = $v2;
                                }
                            }
                            else {
                                $flatData[$k1][$k2] = $v2;
                            }
                        }
                    }
                    else {
                        $flatData[$k1] = $v1;
                    }
                }
                else {
                    $flatData[$k1] = $v1;
                }
            }
            else {
                $flatData[$k1] = $v1;
            }
        }
        return $flatData;  
    }
    
    /**
     * Gets value from an array according to specified path like 'keyA.keyB.keyC'
     * 
     * NOTE: In contrary to Sanitize::value($array['unexisting'] the Arr::getPath($array, 'unexisting') 
     * does not create 'unexisting' key in $array.
     * 
     * @param array& $array Passed by reference. Array to get value of specified path from.
     * @param string $path Path specified like 'keyA.keyB.keyC'.
     * @param string $separator Optional. String used to separate path levels. Defaults to  '.'.
     * 
     * @return mixed Value of $array on given $path. If there is no corresponding 
     *      item for given $path then NULL is returned.
     */
    static public function getPath(&$array, $path, $separator = '.') {        
        if (!is_array($array)) {
            //return null;
            throw new Exception('Arr::getPath() needs an array as the first argument');
        }
        $arrayValue = null;
        $subArray = &$array;
        $path = explode($separator, $path);
        foreach($path as $key) {
            if (
                is_array($subArray) 
                && array_key_exists($key, $subArray)
            ) {
                $arrayValue = $subArray[$key];
                $subArray = &$subArray[$key];
            }
            else {
                $arrayValue = null;
                break;
            }
        }
        return $arrayValue;
    }
    
    /**
     * Sets value in an array according to specified path like 'keyA.keyB.keyC'
     * 
     * @param array& $array Passed by reference
     * @param string $path
     * @param mixed $value
     * @param string $separator Optional. String used to separate path levels. Defaults to  '.'.
     * 
     * @return mixed Old value of $array on given $path. If there is no corresponding 
     *      item for given $path then NULL is returned.
     */
    static public function setPath(&$array, $path, $value, $separator = '.') {        
        if (!is_array($array)) {
            //return null;
            throw new Exception('Arr::setPath() needs an array as the first argument');
        }
        $extend = false;
        $arrayValue = null;
        $subArray = &$array;
        $path = explode($separator, $path);
        foreach($path as $i => $key) {
            if (
                is_array($subArray) 
                && array_key_exists($key, $subArray)
            ) {
                $arrayValue = $subArray[$key];
                $subArray = &$subArray[$key];
            }
            else {
                $arrayValue = null;
                $extend = true; // for the case that we are setting a new value
                break;
            }
        }
        
        if ($extend) {
            $pathLength = count($path);
            if (!is_array($subArray)) {
                $subArray = array();
            }
            for ($j = $i; $j < $pathLength; $j++) {
                $subArray[$path[$j]] = array();
                $subArray = &$subArray[$path[$j]];
            }
        }
        $subArray = $value;

        return $arrayValue;
    }
    
    /**
     * Unsets item in array according to specified path like 'keyA.keyB.keyC'
     * 
     * @param array& $array Passed by reference. Array to unset specified path.
     * @param string $path Path specified like 'keyA.keyB.keyC'.
     * @param string $separator Optional. String used to separate path levels. Defaults to  '.'.
     * 
     * An optional signature can be used:
     * 
     * @param array& $array Passed by reference. Array to unset specified path.
     * @param string $path Path specified like 'keyA.keyB.keyC'.
     * @param array $options Following are available:
     *      - 'separator' (string) String used to separate path levels. Defaults to  '.'.
     *      - 'unsetEmptyTail' (boolean) If TRUE then empty items (arrays) on $path 
     *          are recursively unset (removed) till the first non empty item (array).
     *          The top level $array is never unset even if empty. Defaults to  FALSE.
     * 
     * @return mixed Old value of $array on given $path. If there is no corresponding 
     *      item for given $path then NULL is returned.
     */
    static public function unsetPath(&$array, $path, $separator = '.') {
        if (!is_array($array)) {
            //return null;
            throw new Exception('Arr::unsetPath() needs an array as the first argument');
        }
        $options = is_array($separator) ? $separator : array('separator' => $separator);
        $options = array_merge(array(
            'separator' => '.',
            'unsetEmptyTail' => false,
        ), $options);
        $separator = $options['separator'];
        $arrayValue = null;
        $subArray = &$array;
        $path = explode($separator, $path);
        $lastIndex = count($path) - 1;
        $hasEmptyTail = false;
        foreach($path as $i => $key) {
            if (
                is_array($subArray) 
                && array_key_exists($key, $subArray)
            ) {
                if ($i === $lastIndex) {
                    $arrayValue = $subArray[$key];
                    unset($subArray[$key]);
                    $hasEmptyTail = count($subArray) === 0;
                }
                else {
                    $subArray = &$subArray[$key];
                }
            }
            else {
                $arrayValue = null;
                break;
            }
        }
        if (
            $options['unsetEmptyTail']
            && $hasEmptyTail
            && count($path) > 1
        ) {
            array_pop($path);
            self::unsetPath($array, implode($separator, $path), $options);
        }
        return $arrayValue;
    }
    
    /**
     * Checks if specified path exists in provided array
     * 
     * @param array& $array Passed by reference. Array to check specified path in.
     * @param string $path Path specified like 'keyA.keyB.keyC'.
     * @param string $separator Optional. String used to separate path levels. Defaults to  '.'.
     * 
     * @return bool Yes/no
     */
    static public function hasPath(&$array, $path, $separator = '.') {        
        if (!is_array($array)) {
            //return null;
            throw new Exception('Arr::hasPath() needs an array as the first argument');
        }
        $arrayValue = null;
        $subArray = &$array;
        $path = explode($separator, $path);
        foreach($path as $key) {
            if (
                is_array($subArray) 
                && array_key_exists($key, $subArray)
            ) {
                $arrayValue = $subArray[$key];
                $subArray = &$subArray[$key];
            }
            else {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Searches in provided $array for given $value and returns array of paths to
     * all found occurences.
     * 
     * @param array& $array Passed by reference. Array to seach for $value in.
     * @param mixed $value Value to be searched.
     * @param array $options Following are available:
     *      - 'for' (string) Possible values are 'value', 'key'. Defaults to 'value'.
     *      - 'comparison' (string) Type of comparison which is done between provided 
     *      $value and values of array. Possible values are 'strict', 'nonstrict', 'regex'
     *      and 'intersect'. If 'regex' then $value must be a valid regular expression. 
     *      If 'intersect' then $value must be an array. Defaults to 'strict'.
     *      - 'separator' (string) String to be used as separator in returned array paths.
     *      Defaults to '.'.
     *      - 'first' (bool) If TRUE then only first occurence path is returned.
     *      Defaults to FALSE.
     *      - 'length' (integer) Max length of returned paths. If negative then so
     *      many levels are ommited from the end of paths. If empty then path lengths 
     *      are not restricted. Defaults to NULL.
     * 
     * @return array|bool Array of paths (strings) or single path (if 'first' options is TRUE)
     *       on which given value has been found. If nothing found then an empty array or FALSE if 'first' is TRUE.
     * 
     * @throws Exception if first argument is not array
     */
    static public function search(&$array, $value, $options= array()) {
        // do initialization on top level
        if (empty($options['recursion'])) {
            if (!is_array($array)) {
                //return null;
                throw new Exception('Arr::search() needs an array as the first argument');
            }
            $defaults = array(
                'for' => 'value',
                'comparison' => 'strict', //'nonstrict', 'regex', 'intersect'
                'separator' => '.',
                'first' => false,
                'length' => null,
                /* internal use */
                'recursion' => false
            );
            $options = array_merge($defaults, $options);
            $options['for'] = strtolower(substr($options['for'], 0, 1));
            $options['comparison'] = strtolower(substr($options['comparison'], 0, 1));
        }
        $keys = array();
        foreach ($array as $k => $v) {
            $subject = $options['for'] === 'k' ? $k : $v;
            if (
                $options['comparison'] === 's' 
                && $value === $subject
                || 
                $options['comparison'] === 'n' 
                && $value == $subject
                || 
                $options['comparison'] === 'r' 
                && !is_array($subject) 
                && preg_match($value, $subject)
                || 
                $options['comparison'] === 'i' 
                && is_array($subject) 
                && array_intersect_assoc($value, $subject) === $value
            ) {
                $keys[] = $k; 
                if ($options['first']) {
                    break;
                }
            }
            elseif (
                is_array($v)
                && (
                    ($subKeys = self::search($v, $value, array_merge($options, array('recursion' => true))))
                    ||
                    $options['first'] === true && $subKeys !== false
                )
            ) {
                if ($options['first']) {
                    $keys[] = $k . $options['separator'] . $subKeys;
                    break;
                }
                foreach ($subKeys as $subKey) {
                    $keys[] = $k . $options['separator'] . $subKey;
                }
            }
        }
        if (
            empty($options['recursion'])
            && !empty($options['length'])
        ) {
            // to make this work also when 'first' is TRUE
            $keys = (array)$keys;
            foreach($keys as &$key) {
                $key = explode($options['separator'], $key);
                $key = array_slice($key, 0, $options['length']);
                $key = implode($options['separator'], $key);
            }
            unset($key);
            $keys = array_unique($keys);
        }
        if ($options['first']) {
            $keys = reset($keys);
        }
        return $keys;
    }
        
    /**
     * Prepends the given $value to provided $array. 
     * The $key, to place the $value under, can be optionaly provided.
     * 
     * Similar to php array_unshift() but preserves also numerical keys and 
     * not only literal ones as array_unshift() does.
     * 
     * @param array $array Array to prepend the $value to
     * @param mixed $value Prepended value
     * @param mixed $key Optional. Key of prepended value. If omitted (NULL) then 
     *      then a new numerical key is counted as lowest existing key - 1. 
     *      If provide key matchs to an existing key then this key is placed as 
     *      the first and its value is rewritten by provided $value. Defaults to NULL.
     * 
     * @return array Array with prepended value
     */
    static public function prepend($array, $value, $key = null) {
        if (!is_array($array)) {
            throw new Exception('Arr::prepend() needs an array as the first argument');
        }
        if ($key === null) {
            $key = (int)min(array_keys($array)) - 1;
        }
        return array($key => $value) + $array;
    } 
    
    /**
     * Moves the array element with specified $key to the start of provided $array.
     * If specified $key doesn't exist in provided $array then nothing happens and 
     * $array is returned unchanged.
     * 
     * @param array $array Array to move the element having specified $key
     * @param mixed $key Key of element to move to the start of provided $array
     * 
     * @return array Array with element having specified key on the first position
     */
    static public function moveKeyToStart($array, $key) {
        if (!is_array($array)) {
            throw new Exception('Arr::moveKeyToStart() needs an array as the first argument');
        }
        if (!array_key_exists($key, $array)) {
            return $array;
        }
        return array($key => $array[$key]) + $array;
    } 
    
    /**
     * Moves the array element with specified $key to the end of provided $array.
     * If specified $key doesn't exist in provided $array then nothing happens and 
     * $array is returned unchanged.
     * 
     * @param array $array Array to move the element having specified $key
     * @param mixed $key Key of element to move to the end of provided $array
     * 
     * @return array Array with element having specified key on the last position
     */
    static public function moveKeyToEnd($array, $key) {
        if (!is_array($array)) {
            throw new Exception('Arr::moveKeyToEnd() needs an array as the first argument');
        }
        if (!array_key_exists($key, $array)) {
            return $array;
        }
        $arrayEnd = array($key => $array[$key]);
        unset($array[$key]);
        return $array + $arrayEnd;
    } 
    
    /**
     * Sorts array of strings regarding to specified locale (e.g. 'sk_SK')
     * 
     * @param array& $array Passed by reference. Array is changed (sorted) by this method.
     * @param array $options Folliving are available:
     *      - 'locale' (string) Locale to sort the string accordingly. If empty then
     *          an ASCII sorting is applied. Defaults to App::$locale (actual App locale).
     *      - 'associative' (bool) Should be the associative indexes maintained? 
     *          If TRUE then internally asort() is used. Defaults to FALSE.
     *      - 'compare' (function) Function to compare two strings in array.
     *          It gets on input two items of array plus Collator instance,
     *          e.g. `function ($a, $b, $Collator) {return $Collator->compare($a, $b);}`.
     *          The example function copies just the default behaviour (without 
     *          providing 'compare' option). The real use of this option is to 
     *          allow sorting of more than flat/plain arrays of strings, e.g. 
     *          `function ($a, $b, $Collator) {return $Collator->compare($a['name'], $b['name']);}`
     */
    static public function sortStrings(&$array, $options = array()) {
        $defaults = array(
            'locale' => App::$locale,
            'associative' => false,
            'compare' => null,
        );
        $options = array_merge($defaults, $options);
        if (
            !empty($options['locale'])
            && class_exists('Collator')
        ) {
            $Collator = new Collator($options['locale']);    
            if ($options['associative']) {
                if (Validate::callableFunction($options['compare'])) {
                    uasort($array, function($a, $b) use ($options, $Collator) {
                        return $options['compare']($a, $b, $Collator);
                    });
                }
                else {
                    $Collator->asort($array, Collator::SORT_STRING);
                }
            }
            else {
                if (Validate::callableFunction($options['compare'])) {
                    usort($array, function($a, $b) use ($options, $Collator) {
                        return $options['compare']($a, $b, $Collator);
                    });
                }
                else {
                    $Collator->sort($array, Collator::SORT_STRING);
                }
            }
        }
        else {
            if ($options['associative']) {
                if (Validate::callableFunction($options['compare'])) {
                    uasort($array, $options['compare']);
                }
                else {
                    asort($array, SORT_STRING);
                }
            }
            else {
                if (Validate::callableFunction($options['compare'])) {
                    usort($array, $options['compare']);
                }
                else {
                    sort($array, SORT_STRING);
                }
            }
        }
    }
    
    /**
     * @deprecated - use ksort instead
     * 
     * Sorts given $array by key $sortby.
     *
     * @param array& $array Passed by reference. Array to sort
     * @param string $sortby Sort by this key
     * @param string $order  Sort order asc/desc (ascending or descending).
     * @param integer $type Type of sorting to perform
     * @return mixed Sorted array
     */
	static public function sortByKey(&$array, $sortby, $order = 'asc', $type = SORT_NUMERIC) {
		if (!is_array($array)) {
			return null;
		}

		foreach ($array as $key => $val) {
			$sa[$key] = $val[$sortby];
		}

		if ($order == 'asc') {
			asort($sa, $type);
		} else {
			arsort($sa, $type);
		}

		foreach ($sa as $key => $val) {
			$out[] = $array[$key];
		}
		return $out;
	}
    
    /**
     * Copied from unikinhy - check what it is used there for and why there is so
     * many unused variables in method code.
     * 
     * Generate array from xml content 
     * 
     * @param string $contents
     * @param int $get_attributes - get attributes of xml tags too
     * 
     * @return array
     */
    static public function getFromXml($contents, $get_attributes=1) {  

        if(!$contents) return array();  
        if(!function_exists('xml_parser_create')) {  
            return array();  
        }  
        $parser = xml_parser_create();  
        xml_parser_set_option( $parser, XML_OPTION_CASE_FOLDING, 0 );  
        xml_parser_set_option( $parser, XML_OPTION_SKIP_WHITE, 1 );  
        xml_parse_into_struct( $parser, $contents, $xml_values );  
        xml_parser_free( $parser );  

        if(!$xml_values) return;  
        $xml_array = array();  
        $parents = array();  
        $opened_tags = array();  
        $arr = array();  
        $current = &$xml_array;  

        foreach($xml_values as $data) {  
            unset($attributes,$value);  
            extract($data);  
            $result = '';  

            if($get_attributes) {  
                $result = array();  
                if(isset($value)) $result['value'] = $value;  
                if(isset($attributes)) {  
                    foreach($attributes as $attr => $val) {  
                        if($get_attributes == 1) $result['attr'][$attr] = $val;  
                    }  
                }  
            } elseif(isset($value)) {  
                $result = $value;  
            }  

            if($type == "open") {  
                $parent[$level-1] = &$current;  
                if(!is_array($current) or (!in_array($tag, array_keys($current)))) {  
                    $current[$tag] = $result;  
                    $current = &$current[$tag];  
                } else {  
                    if(isset($current[$tag][0])) {  
                        array_push($current[$tag], $result);  
                    } else {  
                        $current[$tag] = array($current[$tag],$result);  
                    }  
                    $last = count($current[$tag]) - 1;  
                    $current = &$current[$tag][$last];  
                }  
            } elseif ($type == "complete") {  
                if(!isset($current[$tag])) {  
                    $current[$tag] = $result;  
                } else {  
                    if((is_array($current[$tag]) and $get_attributes == 0) or (isset($current[$tag][0]) and is_array($current[$tag][0]) and $get_attributes == 1)) {  
                        array_push($current[$tag],$result);  
                    } else {  
                        $current[$tag] = array($current[$tag],$result);  
                    }  
                }  
            } elseif ($type == 'close') {  
                $current = &$parent[$level-1];  
            }  
        }  
        return($xml_array);  
    } 
    
    /**
     * Converts array associative keys to camelized version, 
     * e.g. the lower_case_and_underscored_key is converted to lowerCaseAndUnderscoredKey
     * or lower-case-and-dasherized-key to lowerCaseAndDasherizedKey.
     * 
     * @param array& $array Passed by reference. Array to camelize key in.
     * @param array $options Following are available:
     *      - 'first' (boolean) If FALSE then starting char of the first word
     *          is lowercased. If TRUE then starting char of the first word is 
     *          uppercased like starting chars of all other words. Defaults to FALSE.
     *      - 'separator' (string|array) Words separator or array of possible separators. 
     *          Defaults to '_'. NOTE: All spaces are processed as implicit separators.
     *      - 'depth' (int) Number of nested levels to be proccessed recursively.
     *          Defaults to NULL, means all levels are processed.
     * 
     * @return array 
     */
    static public function camelizeKeys(&$array, $options = array()) {
        $defaults = array(
            'first' => false,
            'separator' => '_',
            'depth' => null,
        );
        $options = array_merge($defaults, $options);    
        if (!empty($options['depth'])) {
            $options['depth']--;
        }
        $array2 = array();
        foreach ($array as $key => $value) {
            if (!Validate::intNumber($key)) {
                $key = Str::camelize($key, $options);
            }
            if (
                $options['depth'] !== 0
                && is_array($value)
            ) {
                $value = self::camelizeKeys($value, $options);
            }
            $array2[$key] = $value;
        }
        
        return $array2;
    }
    
    /**
     * Builds a tree array from nested list array. 
     * 
     * Nested list is 2D array and each of its items has defined 'level' of nesting. 
     * Top 'level' has value 1. Items are ordered as they occure in tree, 
     * and nesting should be correct. If not, the method corrects it. 
     * The example structure (highlighted by indents, the array indexes are depicted
     * just to give possibility to match items in converted tree array here bellow):
     * 
     *      array(
     *          0 => array('level' => 1, ...),
     *          1 => array('level' => 1, ...),
     *              2 => array('level' => 2, ...),
     *              3 => array('level' => 2, ...),
     *          4 => array('level' => 1, ...),
     *              5 => array('level' => 2, ...),
     *                  6 => array('level' => 3, ...),
     *              7 => array('level' => 2, ...),
     *          ...,
     *      )
     *      
     * Tree is 3D array where same levels under the common parent are gathered into 
     * one node. It has the same structure as array returned by Model::findTree().
     * The above example will be converted to:
     * 
     *      array(
     *          // if there is no conflict with key in list then '-' is used as the top level key
     *          // in case of conflict an unique key generated by uniqid() is used
     *          '-' => array(                       
     *              0 => array('level' => 1, ...),
     *              1 => array('level' => 1, ...),
     *              4 => array('level' => 1, ...),
     *          ),
     *          1 => array(
     *              2 => array('level' => 2, ...),
     *              3 => array('level' => 2, ...),
     *          ),
     *          4 => array(
     *              5 => array('level' => 2, ...),
     *              7 => array('level' => 2, ...),
     *          ),
     *          5 => array(
     *              6 => array('level' => 3, ...),
     *          ),
     *      )
     *  
     * 
     * @param array& $list Passed by reference. List array with here above described structure
     * 
     * @return array Tree array described here above. All invalid list items are omitted in 
     *      this resulting array. If the all array is invalid (no level item is defined)
     *      then an empty array is returned.
     */
    static public function buildTree(&$list) {
        // get top level unique index
        $topIndex = '-';
        while (array_key_exists($topIndex, $list)) {
            $topIndex = uniqid();
        }
        // initilaize variables
        $level = 1;
        $previousIndex = $levelIndex = $topIndex;
        $levelsIndexesStack = array($level => $levelIndex);
        $tree = array();
        $firstItem = true;
        foreach ($list as $i => $item) {
            // if the item is not an array or it has no valid level defined then ignore it
            if (
                !is_array($item) 
                || empty($item['level'])
                || !Validate::intNumber($item['level'])
            ) {
                continue;
            }
            // if we are on first item then force level value to 1 regardless to its
            // real value
            if ($firstItem === true) {
                $firstItem = false;
            }
            // if the item has higher level than actual one (it is going deeper down the tree) 
            // then resolve the new actual level
            elseif ($item['level'] > $level) {
                $level++;
                $levelIndex = $previousIndex;
                $levelsIndexesStack[$level] = $levelIndex;
            }
            // if the item has lower level than actual one (it is going up the tree) 
            // then retrieve the level index
            elseif ($item['level'] < $level) {
                $level = $item['level'] < 1 ? 1 : $item['level'];
                $levelIndex = $levelsIndexesStack[$level];
            }
            // levels here are autocorrected so set the correct resuting level to item 
            // before placing it to tree
            $item['level'] = $level;
            $tree[$levelIndex][$i] = $item;
            $previousIndex = $i;
        }
        return $tree;
    }
    
    /**
     * Merges 2 tree array into one tree, e.g.:
     * 
     *      array ( 
     *          [24] => array ( 
     *              [37] => array('name' => Fruit), 
     *          ), 
     *          [37] => array ( 
     *              [39] => array('name' => Apple), 
     *              [35] => array('name' => Banana), 
     *          ), 
     *      ) 
     * 
     * and
     * 
     *      array ( 
     *          [??] => array (                             // this key is lost after merge
     *              [25] => array('name' => Vegetable), 
     *          ), 
     *          [25] => array ( 
     *              [68] => array('name' => Cauliflower), 
     *              [72] => array('name' => Pumpkin), 
     *          ), 
     *      ) 
     * 
     * are merged into:
     * 
     *      array ( 
     *          [24] => array ( 
     *              [37] => array('name' => Fruit), 
     *              [25] => array('name' => Vegetable), 
     *          ), 
     *          [37] => array ( 
     *              [39] => array('name' => Apple), 
     *              [35] => array('name' => Banana), 
     *          ), 
     *          [25] => array ( 
     *              [68] => array('name' => Cauliflower), 
     *              [72] => array('name' => Pumpkin), 
     *          ), 
     *      ) 
     * 
     * @param array $tree1 Tree array, @see Model::findTree()
     * @param array $tree2 Tree array, @see Model::findTree()
     * 
     * @return array
     */
    static public function mergeTree($tree1, $tree2) {
        if (!empty($tree2)) {
            if (empty($tree1)) {
                $tree1 = $tree2;
            }
            else {
                $tree1 = (array)$tree1;
                $tree2 = (array)$tree2;
                $tree2Keys = array_keys($tree2);
                $firstTree2ItemKey = reset($tree2Keys);
                $firstTree2Item = $tree2[$firstTree2ItemKey];
                if (
                    !empty($firstTree2Item) 
                    && is_array($firstTree2Item)
                ) {
                    $tree1Keys = array_keys($tree1);
                    $firstTree1ItemKey = reset($tree1Keys);
                    $firstTree1Item = &$tree1[$firstTree1ItemKey];
                    if (is_array($firstTree1Item)) {
                        unset($tree2[$firstTree2ItemKey]);
                        $firstTree1Item = $firstTree1Item + $firstTree2Item;
                    }
                }
                $tree1 = $tree1 + $tree2;
            }
        }
        return $tree1;
    }
    
    /**
     * Converts provided array (can be associative and multidimensional) into specified 
     * format (programming language) array literal (or to some corresponding literal 
     * as in JS it is in fact an object)
     * 
     * @param array $array Array to be converted
     * @param string $format Optional. Format (programming language) to create literal for. 
     *      Possible values are 'php' and 'js'. Defaults to 'php'.
     * 
     * @return string Array literal in specified format.
     * @throws Exception on invalid $format specification
     */
    static public function getLiteral($array, $format = 'php') {
        $format = strtolower($format);
        if ($format === 'php') {
            $separator = '=>';
        }
        elseif ($format === 'js') {
            $separator = ':';
        }
        else {
            throw new Exception (__e(__FILE__, 'Invalid array literal format %s', $format));
        }
        $literal = array();
        $escChr = chr(27);
        foreach ($array as $k => $v) {
            // escape apostrophs in key
            // - hide the escaped apostrophs
            $k = str_replace("\'", $escChr, $k);
            // - escape unescaped apostrophs
            $k = str_replace("'", "\'", $k);
            // - put back hidden escaped apostrophs
            $k = str_replace($escChr, "\'", $k);
            
            // treat value
            if (!is_array($v)) {
                // escape apostrophs in value
                // - hide the escaped apostrophs
                $v = str_replace("\'", $escChr, $v);
                // - escape unescaped apostrophs
                $v = str_replace("'", "\'", $v);
                // - put back hidden escaped apostrophs
                $v = str_replace($escChr, "\'", $v);
                // add literal item
                $literal[] = '\'' . $k . '\'' . $separator . '\'' . $v . '\'';
            }
            else {
                // if value is array then recurse
                $v = self::getLiteral($v, $format);
                // add literal item
                $literal[] = '\'' . $k . '\'' . $separator . $v;
            }
        }
        if ($format === 'php') {
            $literal = 'array(' . implode(',', $literal) . ')';
        }
        elseif ($format === 'js') {
            $literal = '{' . implode(',', $literal) . '}';
        }
        return $literal;
    }
    
    /**
     * Changes numeric array index to alphabetic index (used e.g. in excel table)
     * E.g. 0 => A, 1 => B, 26 => AA, 27 => AB, 14557 => UMX or if $startFromZero 
     * is FALSE 1 => A, 2 => B, 27 => AA, 28 => AB, 14558 => UMX
     * 
     * @param int $index
     * @param bool $startFromZero Optional. If TRUE the 0 is returned as A. If 
     *      FALSE then 1 is returned as A. Defaults to TRUE.
     * 
     * @return string
     */
    static public function convertNumericToAlphabeticIndex($index, $startFromZero = true) {
        // if start from zero then increment initial index (do it on starting level but not on recursed levels)
        if ($startFromZero) {
            $index++;
        }
        $numeric = ($index-1) % 26;
        $letter = chr(65 + $numeric);
        $num2 = intval(($index-1) / 26);
        if ($num2 > 0) {
            $letter = self::convertNumericToAlphabeticIndex($num2, false) . $letter;
        }
        return $letter;
    }
    
    /** 
     * Returns array of unique values of provided $array. 
     * In contrary to array_unique() this method works correctly also with array 
     * of arrays, e.g.:
     * 
     *      array(
     *          1,
     *          array(
     *              'name' => 'Peter',
     *              'age' => 23,
     *          ),
     *          2,
     *          array(
     *              'name' => 'Tomas',
     *              'age' => 23,
     *          ),
     *          array(
     *              'age' => 23,
     *              'name' => 'Peter',
     *          ),
     *          2,
     *          array(
     *              'name' => 'Peter',
     *              'age' => '23',
     *          ),
     *          'x' => 3,
     *          array(
     *              'name' => 'Peter',
     *              'age' => 23,
     *          ),
     *          3,
     *      )     
     * 
     * will with $strictComparison = FALSE result to
     * 
     *      array(
     *          1,
     *          array(
     *              'name' => 'Peter',
     *              'age' => 23,
     *          ),
     *          2,
     *          array(
     *              'name' => 'Tomas',
     *              'age' => 23,
     *          ),
     *          'x' => 3,
     *      )   
     *   
     * and with $strictComparison = TRUE will result to
     * 
     *      array(
     *          1,
     *          array(
     *              'name' => 'Peter',
     *              'age' => 23,
     *          ),
     *          2,
     *          array(
     *              'name' => 'Tomas',
     *              'age' => 23,
     *          ),
     *          array(
     *              'age' => 23,
     *              'name' => 'Peter',
     *          ),
     *          array(
     *              'name' => 'Peter',
     *              'age' => '23',
     *          ),
     *          'x' => 3,
     *      )     
     * 
     * @param array $array 
     * @param bool $strictComparison Optional. If TRUE then values are compared strictly 
     *      (for array comparison see http://php.net/manual/en/language.operators.array.php)
     *      Defaults to FALSE.
     * 
     * @return array 
     */  
    static public function getUniqueValues($array, $strictComparison = false) {
        $outputArray = array();  
        foreach($array as $key => $item) {  
            if (!in_array($item, $outputArray, $strictComparison)) {  
                $outputArray[$key] = $item;  
            }  
        }  
        return $outputArray;  
    }     
    
    /**
     * Returns cartesian product of provided arrays. If input array is:
     * 
     *      Array (
     *          [arm] => Array
     *              (
     *                  [0] => A
     *                  [1] => B
     *                  [2] => C
     *              )
     *          [gender] => Array
     *              (
     *                  [0] => Female
     *                  [1] => Male
     *              )
     *          [location] => Array
     *              (
     *                  [0] => Vancouver
     *                  [1] => Calgary
     *              )
     *      )     
     * 
     * then output cartesian product is:
     * 
     *      Array (
     *          [0] => Array
     *              (
     *                  [arm] => A
     *                  [gender] => Female
     *                  [location] => Vancouver
     *              )
     *
     *          [1] => Array
     *              (
     *                  [arm] => A
     *                  [gender] => Female
     *                  [location] => Calgary
     *              )
     *
     *          [2] => Array
     *              (
     *                  [arm] => A
     *                  [gender] => Male
     *                  [location] => Vancouver
     *              )
     *          ... etc
     *      )
     * 
     * See http://stackoverflow.com/questions/6311779/finding-cartesian-product-with-php-associative-arrays
     * 
     * @param array $arrays Array of arrays to do the cartesian product for
     * 
     * @return array
     */
    static public function getCartesianProduct($arrays) {
        $result = array();

        foreach ($arrays as $key => $values) {
            // If a sub-array is empty, it doesn't affect the cartesian product
            if (empty($values)) {
                continue;
            }

            // Seeding the product array with the values from the first sub-array
            if (empty($result)) {
                foreach($values as $value) {
                    $result[] = array($key => $value);
                }
            }
            else {
                // Second and subsequent input sub-arrays work like this:
                //   1. In each existing array inside $product, add an item with
                //      key == $key and value == first item in input sub-array
                //   2. Then, for each remaining item in current input sub-array,
                //      add a copy of each existing array inside $product with
                //      key == $key and value == first item of input sub-array

                // Store all items to be added to $product here; adding them
                // inside the foreach will result in an infinite loop
                $append = array();

                foreach($result as &$product) {
                    // Do step 1 above. array_shift is not the most efficient, but
                    // it allows us to iterate over the rest of the items with a
                    // simple foreach, making the code short and easy to read.
                    $product[$key] = array_shift($values);

                    // $product is by reference (that's why the key we added above
                    // will appear in the end result), so make a copy of it here
                    $copy = $product;

                    // Do step 2 above.
                    foreach($values as $item) {
                        $copy[$key] = $item;
                        $append[] = $copy;
                    }

                    // Undo the side effecst of array_shift
                    array_unshift($values, $product[$key]);
                }

                // Out of the foreach, we can add to $results now
                $result = array_merge($result, $append);
            }
        }

        return $result;
    }
    
    /**
     * Encodes array into json. This method differs from json_encode() in the way
     * how it treats js anonymous function definitions. It preserves them so that 
     * they are recognized by js as valid function definitions. E.g. if you have 
     * following array:
     * 
     *      $a = array(
     *          'myValue' => true,
     *          'myFunction' => 'function(){console.log("hello")}',
     *      );   
     * 
     * then Arr::encodeToJson($a) will produce:
     * 
     *      {"myValue":true,"myFunction":function(){console.log("hello")}}
     * 
     * while json_encode($a, JSON_FORCE_OBJECT) will produce:
     * 
     *      {"myValue":true,"myFunction":"function(){console.log(\"hello\")}"}
     * 
     * ATTENTION: Even plain (nonassociative) array is encoded as js object {}
     * and not as js array []. It means that option JSON_FORCE_OBJECT is forced.
     * 
     * @param array $array
     * @param int $options See json_encode() options
     * 
     * @return string Json
     */
    static public function encodeToJson($array, $options = 0) {
        $options = $options | JSON_FORCE_OBJECT;
        $empty = true;
        $json = '{}';
        $slice = array();
        foreach ($array as $k => $v) {
            if (is_array($v)) {
                $subJson = self::encodeToJson($v, $options);
            }
            elseif (
                is_string($v)
                && ($t = trim($v))
                && preg_match('/^function\s*\(/', $t)
            ) {
                $subJson = $t;
            }
            if (!empty($subJson)) {
                $sliceJson = '';
                if (!empty($slice)) {
                    $sliceJson = json_encode($slice, $options);
                    $sliceJson = substr($sliceJson, 1, -1) . ',';
                }
                $json = substr($json, 0, -1);
                if (!$empty) {
                    $json .= ',';
                }
                $json .= $sliceJson . '"' . $k . '":' . $subJson . '}';
                $slice = array();
                $subJson = '';
                $empty = false;
            }
            else {
                $slice[$k] = $v;
            }
        }
        if (!empty($slice)) {
            $sliceJson = json_encode($slice, $options);
            $sliceJson = substr($sliceJson, 1);
            $json = substr($json, 0, -1);
            if (!$empty) {
                 $json .= ',';
            }
            $json .= $sliceJson;
        }
        
        return $json;
    }  
}
