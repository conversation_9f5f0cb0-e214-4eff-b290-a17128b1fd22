<?php
class File {
    
    /**
     * CONSTANTS
     */
    
    /**
     * File source type constant returned by method Model::getSourceType()
     * 
     * This constant is returned for upload file sources - array containing
     * items from $_FILES array.
     */
    const UPLOAD_SOURCE = 1;
    
    /**
     * File source type constant returned by method Model::getSourceType()
     * 
     * This constant is returned for HTTP file sources - an array containing
     * url string. E.g. array('http://my.file.source/image.png').
     */
    const DOWNLOAD_SOURCE = 2;
    
    /**
     * File source type constant returned by method Model::getSourceType()
     * 
     * This constant is returned for FTP file sources - an array containing FTP
     * connection string like 'scheme://user:password@host:port/path?query#fragment'.
     * E.g. array('ftp://myuser:<EMAIL>:21/my/path/to/fiel.txt')
     */
    const FTP_SOURCE = 3;
        
    /**
     * File source type constant returned by method Model::getSourceType()
     * 
     * This constant is returned for copy file sources - an array containing local
     * app root relative path of existing file. E.g. array('userfiles/import/image.png')
     */
    const COPY_SOURCE = 4;
        
    /**
     * File source type constant returned by method Model::getSourceType()
     * 
     * This constant is returned if provided value does not represent any of possible file sources.
     * E.g. string containing actual name of attached file 'image_1.png'
     */
    const NOT_SOURCE = 0;
    
    
    /**
     * Definition of mime types for different file extensions 
     * 
     * @var array 
     */
    static protected $mimeTypesByExtensions = array(
        'aif' => 'audio/aiff',
        'aifc' => 'audio/aiff',
        'aiff' => 'audio/aiff',
        'avi' => 'video/avi',
        'bm' => 'image/bmp',
        'bmp' => 'image/bmp',
        'bz' => 'application/x-bzip',
        'bz2' => 'application/x-bzip2',
        'css' => 'text/css',
        'doc' => 'application/msword',
        'gif' => 'image/gif',
        'gtar' => 'application/x-gtar',
        'gz' => 'application/x-gzip',
        'gzip' => 'application/x-gzip',
        'htm' => 'text/html',
        'html' => 'text/html',
        'java' => 'text/plain',
        'jpe' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'jpg' => 'image/jpeg',
        'js' => 'application/javascript',
        'midi' => 'audio/midi',
        'mp2' => 'audio/mpeg',
        'mp3' => 'audio/mpeg3',
        'mpe' => 'video/mpeg',
        'mpeg' => 'video/mpeg',
        'mpg' => 'audio/mpeg',
        'pdf' => 'application/pdf',
        'png' => 'image/png',
        'pps' => 'application/vnd.ms-powerpoint',
        'ppt' => 'application/vnd.ms-powerpoint',
        'rt' => 'text/richtext',
        'rtf' => 'application/rtf',
        'svf' => 'image/vnd.dwg',
        'tar' => 'application/x-tar',
        'text' => 'application/plain',
        'text' => 'text/plain',
        'txt' => 'text/plain',
        'tgz' => 'application/x-compressed',
        'tif' => 'image/tiff',
        'tiff' => 'image/tiff',
        'wav' => 'audio/wav',
        'xls' => 'application/vnd.ms-excel',
        'xml' => 'application/xml',
//        'xml' => 'text/xml',
        'zip' => 'application/x-compressed',
//        'zip' => 'application/zip',
    );
    
    /**
     * Definition of extensions for different file mime types
     * 
     * ATTENTION: Use method File::getExtensionByMimeType() to do this conversion.
     * 
     * @var array 
     */
    static protected $extensionsByMimeTypes = array(
        'video/3gpp2'                                                               => '3g2',
        'video/3gp'                                                                 => '3gp',
        'video/3gpp'                                                                => '3gp',
        'application/x-compressed'                                                  => '7zip',
        'audio/x-acc'                                                               => 'aac',
        'audio/ac3'                                                                 => 'ac3',
        'application/postscript'                                                    => 'ai',
        'audio/x-aiff'                                                              => 'aif',
        'audio/aiff'                                                                => 'aif',
        'audio/x-au'                                                                => 'au',
        'video/x-msvideo'                                                           => 'avi',
        'video/msvideo'                                                             => 'avi',
        'video/avi'                                                                 => 'avi',
        'application/x-troff-msvideo'                                               => 'avi',
        'application/macbinary'                                                     => 'bin',
        'application/mac-binary'                                                    => 'bin',
        'application/x-binary'                                                      => 'bin',
        'application/x-macbinary'                                                   => 'bin',
        'image/bmp'                                                                 => 'bmp',
        'image/x-bmp'                                                               => 'bmp',
        'image/x-bitmap'                                                            => 'bmp',
        'image/x-xbitmap'                                                           => 'bmp',
        'image/x-win-bitmap'                                                        => 'bmp',
        'image/x-windows-bmp'                                                       => 'bmp',
        'image/ms-bmp'                                                              => 'bmp',
        'image/x-ms-bmp'                                                            => 'bmp',
        'application/bmp'                                                           => 'bmp',
        'application/x-bmp'                                                         => 'bmp',
        'application/x-win-bitmap'                                                  => 'bmp',
        'application/cdr'                                                           => 'cdr',
        'application/coreldraw'                                                     => 'cdr',
        'application/x-cdr'                                                         => 'cdr',
        'application/x-coreldraw'                                                   => 'cdr',
        'image/cdr'                                                                 => 'cdr',
        'image/x-cdr'                                                               => 'cdr',
        'zz-application/zz-winassoc-cdr'                                            => 'cdr',
        'application/mac-compactpro'                                                => 'cpt',
        'application/pkix-crl'                                                      => 'crl',
        'application/pkcs-crl'                                                      => 'crl',
        'application/x-x509-ca-cert'                                                => 'crt',
        'application/pkix-cert'                                                     => 'crt',
        'text/css'                                                                  => 'css',
        'text/x-comma-separated-values'                                             => 'csv',
        'text/comma-separated-values'                                               => 'csv',
        'application/vnd.msexcel'                                                   => 'csv',
        'application/x-director'                                                    => 'dcr',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'   => 'docx',
        'application/x-dvi'                                                         => 'dvi',
        'message/rfc822'                                                            => 'eml',
        'application/x-msdownload'                                                  => 'exe',
        'video/x-f4v'                                                               => 'f4v',
        'audio/x-flac'                                                              => 'flac',
        'video/x-flv'                                                               => 'flv',
        'image/gif'                                                                 => 'gif',
        'application/gpg-keys'                                                      => 'gpg',
        'application/x-gtar'                                                        => 'gtar',
        'application/x-gzip'                                                        => 'gzip',
        'application/mac-binhex40'                                                  => 'hqx',
        'application/mac-binhex'                                                    => 'hqx',
        'application/x-binhex40'                                                    => 'hqx',
        'application/x-mac-binhex40'                                                => 'hqx',
        'text/html'                                                                 => 'html',
        'image/x-icon'                                                              => 'ico',
        'image/x-ico'                                                               => 'ico',
        'image/vnd.microsoft.icon'                                                  => 'ico',
        'text/calendar'                                                             => 'ics',
        'application/java-archive'                                                  => 'jar',
        'application/x-java-application'                                            => 'jar',
        'application/x-jar'                                                         => 'jar',
        'image/jp2'                                                                 => 'jp2',
        'video/mj2'                                                                 => 'jp2',
        'image/jpx'                                                                 => 'jp2',
        'image/jpm'                                                                 => 'jp2',
        'image/jpeg'                                                                => 'jpg',
        'image/pjpeg'                                                               => 'jpg',
        'application/x-javascript'                                                  => 'js',
        'application/json'                                                          => 'json',
        'text/json'                                                                 => 'json',
        'application/vnd.google-earth.kml+xml'                                      => 'kml',
        'application/vnd.google-earth.kmz'                                          => 'kmz',
        'text/x-log'                                                                => 'log',
        'audio/x-m4a'                                                               => 'm4a',
        'application/vnd.mpegurl'                                                   => 'm4u',
        'audio/midi'                                                                => 'mid',
        'application/vnd.mif'                                                       => 'mif',
        'video/quicktime'                                                           => 'mov',
        'video/x-sgi-movie'                                                         => 'movie',
        'audio/mpeg'                                                                => 'mp3',
        'audio/mpg'                                                                 => 'mp3',
        'audio/mpeg3'                                                               => 'mp3',
        'audio/mp3'                                                                 => 'mp3',
        'video/mp4'                                                                 => 'mp4',
        'video/mpeg'                                                                => 'mpeg',
        'application/oda'                                                           => 'oda',
        'audio/ogg'                                                                 => 'ogg',
        'video/ogg'                                                                 => 'ogg',
        'application/ogg'                                                           => 'ogg',
        'application/x-pkcs10'                                                      => 'p10',
        'application/pkcs10'                                                        => 'p10',
        'application/x-pkcs12'                                                      => 'p12',
        'application/x-pkcs7-signature'                                             => 'p7a',
        'application/pkcs7-mime'                                                    => 'p7c',
        'application/x-pkcs7-mime'                                                  => 'p7c',
        'application/x-pkcs7-certreqresp'                                           => 'p7r',
        'application/pkcs7-signature'                                               => 'p7s',
        'application/pdf'                                                           => 'pdf',
        'application/octet-stream'                                                  => 'pdf',
        'application/x-x509-user-cert'                                              => 'pem',
        'application/x-pem-file'                                                    => 'pem',
        'application/pgp'                                                           => 'pgp',
        'application/x-httpd-php'                                                   => 'php',
        'application/php'                                                           => 'php',
        'application/x-php'                                                         => 'php',
        'text/php'                                                                  => 'php',
        'text/x-php'                                                                => 'php',
        'application/x-httpd-php-source'                                            => 'php',
        'image/png'                                                                 => 'png',
        'image/x-png'                                                               => 'png',
        'application/powerpoint'                                                    => 'ppt',
        'application/vnd.ms-powerpoint'                                             => 'ppt',
        'application/vnd.ms-office'                                                 => 'ppt',
        'application/msword'                                                        => 'doc',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'pptx',
        'application/x-photoshop'                                                   => 'psd',
        'image/vnd.adobe.photoshop'                                                 => 'psd',
        'audio/x-realaudio'                                                         => 'ra',
        'audio/x-pn-realaudio'                                                      => 'ram',
        'application/x-rar'                                                         => 'rar',
        'application/rar'                                                           => 'rar',
        'application/x-rar-compressed'                                              => 'rar',
        'audio/x-pn-realaudio-plugin'                                               => 'rpm',
        'application/x-pkcs7'                                                       => 'rsa',
        'text/rtf'                                                                  => 'rtf',
        'text/richtext'                                                             => 'rtx',
        'video/vnd.rn-realvideo'                                                    => 'rv',
        'application/x-stuffit'                                                     => 'sit',
        'application/smil'                                                          => 'smil',
        'text/srt'                                                                  => 'srt',
        'image/svg+xml'                                                             => 'svg',
        'application/x-shockwave-flash'                                             => 'swf',
        'application/x-tar'                                                         => 'tar',
        'application/x-gzip-compressed'                                             => 'tgz',
        'image/tiff'                                                                => 'tiff',
        'text/plain'                                                                => 'txt',
        'text/x-vcard'                                                              => 'vcf',
        'application/videolan'                                                      => 'vlc',
        'text/vtt'                                                                  => 'vtt',
        'audio/x-wav'                                                               => 'wav',
        'audio/wave'                                                                => 'wav',
        'audio/wav'                                                                 => 'wav',
        'application/wbxml'                                                         => 'wbxml',
        'video/webm'                                                                => 'webm',
        'audio/x-ms-wma'                                                            => 'wma',
        'application/wmlc'                                                          => 'wmlc',
        'video/x-ms-wmv'                                                            => 'wmv',
        'video/x-ms-asf'                                                            => 'wmv',
        'application/xhtml+xml'                                                     => 'xhtml',
        'application/excel'                                                         => 'xl',
        'application/msexcel'                                                       => 'xls',
        'application/x-msexcel'                                                     => 'xls',
        'application/x-ms-excel'                                                    => 'xls',
        'application/x-excel'                                                       => 'xls',
        'application/x-dos_ms_excel'                                                => 'xls',
        'application/xls'                                                           => 'xls',
        'application/x-xls'                                                         => 'xls',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'         => 'xlsx',
        'application/vnd.ms-excel'                                                  => 'xlsx',
        'application/xml'                                                           => 'xml',
        'text/xml'                                                                  => 'xml',
        'text/xsl'                                                                  => 'xsl',
        'application/xspf+xml'                                                      => 'xspf',
        'application/x-compress'                                                    => 'z',
        'application/x-zip'                                                         => 'zip',
        'application/zip'                                                           => 'zip',
        'application/x-zip-compressed'                                              => 'zip',
        'application/s-compressed'                                                  => 'zip',
        'multipart/x-zip'                                                           => 'zip',
        'text/x-scriptzsh'                                                          => 'zsh',
    );
    
    /**
     * Replacement function for PHP's native phpinfo() which does not work consistently
     * across all versions (In PHP < 5.2.0 it does not return `filename`).
     * 
     * @param string $path The path to be parsed. 
     * @param int $options If present, specifies a specific element to be returned; 
     *      one of PATHINFO_DIRNAME, PATHINFO_BASENAME, PATHINFO_EXTENSION or 
     *      PATHINFO_FILENAME. If options is not specified, returns all available elements.
     * 
     * @return array Pathinfo array with keys 'dirname', 'basename', 'filename' 
     *      and 'extension'. The 'extension' can be NULL if there is no extension
     *      (e.g. /path/noextension) or empty string if there is empty extension
     *      (e.g. /path/emptyextension.) E.g.: for path '/www/htdocs/inc/lib.inc.php'
     *      the output is: array('dirname' => '/www/htdocs/inc', 'basename' => 'lib.inc.php', 
     *      'filename' => 'lib.inc', 'extension' => 'php')
     */
    static public function getPathinfo($path, $options = null) {
        if ($options === null) {
            // some system constants may not be defined in which case they are passed as strings
            $pathinfo = pathinfo($path);
            // normalize extension
            if (!isset($pathinfo['extension'])) {
                $pathinfo['extension'] = null;
            }
        } 
        else {
            $pathinfo = pathinfo($path, $options);
        }
        // filename is not available (PHP < 5.2.0)
        if (
            is_array($pathinfo) 
            && !isset($pathinfo['filename']) 
        ) {
            // user wanted all info 
            if ($options === null) {
                $pathinfo['filename'] = substr($pathinfo['basename'], 0, strlen($pathinfo['basename']) - strlen($pathinfo['extension']) - 1);
            }
            // user wanted filename only (but that constant is not defined)
            elseif ($options == 'PATHINFO_FILENAME') {
                $pathinfo = substr($pathinfo['basename'], 0, strlen($pathinfo['basename']) - strlen($pathinfo['extension']) - 1);
            }
        }
        return $pathinfo;
    }
    
    /**
     * Returns timestamp of specified $file
     * 
     * @param string $file App root relative path to file
     * 
     * @return int File modification timestamp 
     */
    static public function getTimestamp($file) {
        $file = self::normalizePath($file);
        return filemtime(ROOT . DS . $file);
    }
    
    /**
     * Returns timestamped URL path of specified $file
     * 
     * @param string $file App root relative path to file. Both '/' and '\'  can
     *      be used as directory separators.
     * @param array $options Options of App::getUrl() plus following:
     *      - 'avoidOnLocalhost' (bool) If TRUE then timestamp is not added on localhost. 
     *          Defaults to TRUE.
     * 
     * @return string File URL path with added timestamp
     */
    static public function getTimestampedUrlPath($file, $options = array()) {
        $options = array_merge(array(
            'avoidOnLocalhost' => true
        ), $options);
        $options['locator'] = '/' . self::normalizeDS(self::normalizePath($file), '/');
        if (!$options['avoidOnLocalhost'] || !ON_LOCALHOST) {
            $options['get'] = (array)Sanitize::value($options['get']);
            $options['get']['ts'] = self::getTimestamp($file);
        }
        return App::getUrl($options);
    }
    
    /**
     * Normalizes directory separators in given path to ensure 
     * Linux / Windows compatibility. E.g. instead of:
     * 
     *     require ROOT . DS . 'my' . DS . 'very' . DS . 'deep' . DS . 'folder' . DS . 'script.php';
     * 
     * is enough to write:
     * 
     *      require ROOT . File::normalizeDS('/my/very/deep/folder/script.php');
     * 
     * @param string $path
     * @param string $ds Optional. Normalized form of directory separator. Defautls to DS.
     * 
     * @return string Normalized path
     */
    static public function normalizeDS($path, $ds = DS) {
        return str_replace(array('/', '\\'), $ds, $path);
    }
    
    /**
     * Normalizes provided path like:
     * - does not allow to go out from ROOT folder (for security)
     * - resolves directory separators diferences between windows and unix paths
     * - trims leading and trailing directory separator
     * - forces provided extension if required
     * 
     * @param string $path
     * @param string $extension Optional. An extension to be forced to path.
     *      Defaults to NULL (no extension is forced).
     * @return string Normalized path
     */
    static public function normalizePath($path, $extension = null) {
        // do not allow to go out from ROOT folder
        $path = str_replace('..', '', $path); 
        // make it windows compatibile
        $path = str_replace(array('/', '\\'), DS, $path); 
        // be sure the there is no leading slash
        $path = trim($path, DS); 
        // force php script if required
        if (
            $extension
            && (
                strlen($path) < ($len = strlen($extension) + 1)
                || substr($path, -$len) != '.' . $extension
            )
        ) {
            $path .= '.' . $extension;
        }
        return $path;
    }
    
    /**
     * Normalizes provided extension like:
     * - it is lowercased
     * - jpeg and jpe are normalized to jpg
     * 
     * @param string $extension
     * @return string
     */
    static public function normalizeExtension($extension) {
        if (!empty($extension)) {
            $extension = strtolower($extension);
            if (
                $extension === 'jpeg'
                || $extension === 'jpe'
            ) {
                $extension = 'jpg';
            }
        }
        return $extension;
    }
    
    /**
     * Convenience method to retrieve file upload data form $_FILES for specified field name.
     * This is usefull especially in the case when the data are nested and the field uses dot syntax
     * to separate nested levels
     * 
     * @param string $field Field name to retrieve upload data from $_FILES array for.
     *      Dot syntax can by used to separate nested levels.
     * 
     * @return array|bool Array containing upload data on success. FALSE if provided 
     *      field is not found in $_FILES or if it does not contain all nested levels
     *      (means that items in upload data array are still arrays) 
     */
    static public function getUploadData($field) {
        if (strpos($field, '.') !== false) {
            $fieldParts = explode('.', $field);
            $mainQualifier = array_shift($fieldParts);
            if (empty($_FILES[$mainQualifier])) {
                return false;
            }
            $subQualifiers = implode('.', $fieldParts);
            $data = array(
                'name' => Arr::getPath($_FILES, $mainQualifier . '.name.' . $subQualifiers),
                'type' => Arr::getPath($_FILES, $mainQualifier . '.type.' . $subQualifiers),
                'tmp_name' => Arr::getPath($_FILES, $mainQualifier . '.tmp_name.' . $subQualifiers),
                'error' => Arr::getPath($_FILES, $mainQualifier . '.error.' . $subQualifiers),
                'size' => Arr::getPath($_FILES, $mainQualifier . '.size.' . $subQualifiers)
            );
        }
        elseif (empty($_FILES[$field])) {
            return false;
        }
        else {
            $data = $_FILES[$field];
        }
        if (
            !isset($data['name'])
            || !isset($data['type'])
            || !isset($data['tmp_name'])
            || !isset($data['error'])
            || !isset($data['size'])
            || is_array($data['error'])
                
        ) {
            return false;
        }
        return $data;
    }
                
    /**
     * Returns app root relative part of provided path. Path does not contain 
     * leading and trailing slashes.
     * 
     * Method does not checks for path existence. 
     * 
     * @param string $path Path to get the relative part from
     * @param string $root Optional. Root to get relative part of $path to.
     *      Defaults to ROOT (app root).
     * 
     * @return string|bool Relative part of provided path (it can be also empty string). 
     *      FALSE if provided path is not under root.
     */
    static public function getRelativePath($path, $root = ROOT) {
        $rootWithoutDS = rtrim($root, DS);
        $rootWithDS = $rootWithoutDS . DS;
        $rootLength = strlen($rootWithDS);
        if ($path === $rootWithoutDS) {
            return '';
        }
        elseif (substr($path, 0, $rootLength) === $rootWithDS) {
            return trim(substr($path, $rootLength), DS);
        }
        return false;
    }
    
    /**
     * Returns absolut path for provided $root relative path. Path does not contain 
     * trailing slash.
     * 
     * Method does not checks for path existence. 
     * 
     * @param string $path A $root relative path to get absolute path for
     * @param string $root Optional. Root to get relative part of $path to.
     *      Defaults to ROOT (app root).
     * 
     * @return string Absolut path 
     */
    static public function getAbsolutePath($path, $root = ROOT) {
        return $root . DS . trim($path, DS);
    }
    
    /**
     * Ensures existence of directory under app root.
     * 
     * Means that if the directory does not exist on given path 
     * then it is created
     * 
     * @param string $path Path to directory whose existence is required under app root.
     *      This path can be relative to app root or absolute. This must be defined
     *      by $absolute param. In each case, even if absolute, the path must target
     *      under app root. Default is relative. 
     * 
     * 
     * @param bool $absolute Optional. If TRUE then the given path is absolute, otherwise
     *      it is relative to app root. Defaults to FALSE.
     * 
     * @return bool TRUE on success, FALSE on failure. Fails also if trying to ensure
     *      existence of a folder out of app root.
     * 
     * @throws Exception If the $path is out of app root 
     */
    static public function ensurePath($path, $absolute = false) {
        // as normalize removes leading directory separator (WINDOWS does not use it but LINUX does)
        // so keep notice of leading DS if it was in provided path
        $leadingDS = '';
        if (
            $absolute
            && substr($path, 0, 1) === DS
        ) {
            $leadingDS = DS;
        }
        $path = $leadingDS . self::normalizePath($path);
        // check if the directory is placed under app root if the given path is absolute
        if (
            $absolute
            && ($path = self::getRelativePath($path)) === false
        ) {
            throw new Exception("Provided path is out of app root");
        }
        // remove leading and trailing slashes and explode
        $path = trim($path, DS);
        $pathParts = explode(DS, $path);
        $partialPath = ROOT;
        foreach ($pathParts as $part) {
            $partialPath .= DS . $part;
            if (!file_exists($partialPath)) {
                if (!mkdir($partialPath)) {
                    return false;
                }      
            }
            // check for possible conflict with existing file
            elseif (is_file($partialPath)) {
                return false;
            }
        }
        return true;
    }     
    
    /**
     * Finds an unique filename version (by numeric suffix) in provided directory or directories
     *
     * @param string $filename Filename to be made unique (by numeric suffix)
     * @param string|array $dir App root relative path to directory or an array of such paths.
     *      If directory path is provided as key of array then an extension can be provided as value.
     *      In such a case the unique filename will be searched for given extension under corresponding directory.
     * @param array $options Optional. Following options can be used:
     *      - 'tries' (int) Number of tries. If 0 then number of tries is not limited
     *          and the method search till the first free name is found. Defaults to 0.
     *      - 'reserve' (bool) If TRUE then found filename will be reserved by creating a fake
     *          empty file with found unique filename under all provided directories. Defaults to FALSE.
     *      - 'extension' (string) File extension to be used to find unique filename. 
     *          If $dir is array and there are a defined extensions for directories then 
     *          these extensions are used instead of this option. Defaults to NULL 
     *          (extension from provided $filename will be used).
     *      - 'withExtension' (bool) If TRUE then the unique filename is returned together with extension.
     *          Defaults to FALSE - unique filename is returned without extension.
     *          NOTE: This option can be used only if a single extension is used
     *          for all provided dirs. Othervise an exception will be raised.
     * 
     * @return string|bool An unique filename version (without extension) for provided dir(s). 
     *      Filename contains extension if option 'withExtensions' is TRUE.
     *      FALSE if number of tries were exceeded
     * 
     * @throws Exception if one of directories does not exist
     */
    static public function getUniqueName($filename, $dir, $options = array()) {
        $defaults = array(
            'tries' => 0,
            'reserve' => false,
            'extension' => null,
            'withExtension' => false,
        );
        $options = array_merge($defaults, $options);
        
        // get default extension
		$file = self::getPathinfo($filename);
        if (!isset($file['extension']) || $options['extension']) {
            $file['extension'] = $options['extension'];
        }
        $extension = null;
        if ($file['extension']) {
            $extension = $file['extension'];
        }
        
        $tries = $options['tries'];
		$filename = $uniqueFilename = $file['filename'];
        if (is_array($dir)) {
            // normalize dir array: set absolute paths to directories, set extensions and
            // add '.' before them
            $temp = array();
            $exts = array();
            foreach ($dir as $k => $v) {
                if (($useKey = is_string($k))) {
                    $d = $k;
                }
                else {
                    $d = $v;
                }
                $d = ROOT . DS . trim($d, DS);
                if (!is_dir($d)) {
                    throw new Exception("Invalid directory '{$d}'");
                }
                // get extension
                if ($useKey) {
                    $e = $v ? $v : $extension;
                }
                else {
                    $e = $extension;
                }
                if ($e) {
                    $temp[$d] = '.' . $e;
                }
                else {
                    $temp[$d] = '';
                }
                $exts[$e] = true;
            }
            $dir = $temp;
            // check if possible to return name with extension
            if (
                $options['withExtension']
                && count($exts) > 1
            ) {
                throw new Exception("Option 'withExtension' can be used only in case of single used extension");
            }
            // find unique filename
            $count = 1;
            for(;;) {
                $exists = false;
                foreach ($dir as $d => $e) {
                    if (($exists = file_exists($d . DS . $uniqueFilename . $e))) {
                        if ($tries != 0 && $count > $tries) {
                            return false;
                        }
                        break;
                    }
                }
                if (!$exists) {
                    break;
                }
                $uniqueFilename = $filename . '_' . $count;                
                $count++;
            }
            // reserve filename under dirs
            if ($options['reserve']) {
                foreach ($dir as $d => $e) {
                    file_put_contents($d . DS . $uniqueFilename . $e, '');
                }
            }
            // add extension if required
            if ($options['withExtension']) {
                $uniqueFilename = $uniqueFilename . $e;
            }
        }
        else {
            $dir = self::getAbsolutePath($dir);
            if (!is_dir($dir)) {
                throw new Exception("Invalid directory '{$dir}'");
            }
            // find unique filename
            if ($extension) {
                $extension = '.' . $extension;
            }
            for ($count = 1; file_exists($dir . DS . $uniqueFilename . $extension); $count++) {
                if ($tries != 0 && $count > $tries) {
                    return false;
                }
                $uniqueFilename = $filename . '_' . $count;
            }
            // reserve filename
            if ($options['reserve']) {
                file_put_contents($dir . DS . $uniqueFilename . $extension, '');
            }
            // add extension if required
            if ($options['withExtension']) {
                $uniqueFilename = $uniqueFilename . $extension;
            }
        }
        
	return $uniqueFilename;
    }
        
    /**
     * Uploads file to given directory under app root.
     * 
     * @param array $fileSource Uploaded file data, which is an array like:
     *      array('name' => 'myfile', 'type' => 'image/png', 'tmp_name' => '/tmp/phpm8vpsT', 'error' => 0, 'size' => 65377) 
     * @param string $dir App root relative destination directory to upload the file into
     * @param array $options Array of following options:
     *      - 'name' (string) Name to save the file under. If not provided (NULL)
     *          then the name of uploaded file is used. Defaults to NULL.
     *      - 'unique' (bool) If TRUE then the unique filename is searched under
     *          provided $dir, to avoid overwriting of existing file. If FALSE
     *          then the file is saved under its name, even in case of name colision
     *          and the existing file is overwritten by the new one. Defaults to TRUE.
     * 
     * @return string|bool|int On success returns filename (without path) under which 
     *      the upload was saved. If file source is invalid then constant File::NOT_SOURCE 
     *      (int, 0) is returned. If no file was uploaded then returns FALSE.
     * 
     * @throws Exception On upload errors (exception code is set to error code),
     *      on incorrect tmp_name and on failed saving the uploaded file to $dir
     *      (no exception code for two last)
     */
    static public function upload($fileSource, $dir, $options = array()) {
        $defaults = array(
            'name' => null,
            'unique' => true,
        );
        $options = array_merge($defaults, $options);
        // normalize dir
        $dir = self::normalizePath($dir);
        // check for file source validity
        if (!Validate::uploadData($fileSource)) {
            return self::NOT_SOURCE;
        }
        // check if the field was uploaded
        if ($fileSource['error'] == UPLOAD_ERR_NO_FILE) {
            return false;
        }
        // check for upload errors
        switch ($fileSource['error']) {
            case UPLOAD_ERR_INI_SIZE:
                throw new Exception("The uploaded file exceeds the upload_max_filesize directive in php.ini", UPLOAD_ERR_INI_SIZE);
                break;

            case UPLOAD_ERR_FORM_SIZE:
                throw new Exception("The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form", UPLOAD_ERR_FORM_SIZE);
                break;

            case UPLOAD_ERR_PARTIAL:
                throw new Exception("The uploaded file was only partially uploaded", UPLOAD_ERR_PARTIAL);
                break;

            case UPLOAD_ERR_NO_TMP_DIR:
                throw new Exception("Missing a temporary folder", UPLOAD_ERR_NO_TMP_DIR);
                break;

            case UPLOAD_ERR_CANT_WRITE:
                throw new Exception("Failed to write file to disk", UPLOAD_ERR_CANT_WRITE);
                break;

            case UPLOAD_ERR_EXTENSION:
                throw new Exception("A PHP extension stopped the file upload", UPLOAD_ERR_EXTENSION);
                break;                
        }
        // check whether the file was really uploaded via HTTP POST
        if(!is_uploaded_file($fileSource['tmp_name'])) {
            throw new Exception("Temporary name '{$fileSource['tmp_name']}' of uploaded file is incorrect");
        }
        // use source file name if no explicit name is provided
        if (empty($options['name'])) {
            $options['name'] = self::getTransferFilename($fileSource, array(
                'sourceType' => self::UPLOAD_SOURCE,
            ));
        }
        // if explicit name is provided then check if it has extension and if not 
        // then inherit it from source file (if any)
        elseif (
            !self::getPathinfo($options['name'], PATHINFO_EXTENSION)
            && ($extension = self::getPathinfo($fileSource['name'], PATHINFO_EXTENSION))
        ) {
            $options['name'] = rtrim($options['name'], '.') . '.' . $extension;
        }
        // sanitize name
        $options['name'] = Sanitize::filename($options['name']);
        // destination directory
        if (!self::ensurePath($dir)) {
            throw new Exception("Invalid destination directory {$dir}");
        }
        // get unique filename if required
        if ($options['unique']) {
            $result = self::getUniqueName($options['name'], $dir, array(
                'withExtension' => true,
                'reserve' => true,
            ));
            if (!$result) {
                throw new Exception("Search of unique filename for '{$options['name']}' in '{$dir}' has failed");
            }
            $options['name'] = $result;
        }
        // move uploaded file to destination directory
        $dir = self::getAbsolutePath($dir);
        if (
            move_uploaded_file($fileSource['tmp_name'], $dir . DS . $options['name'])
        ) {
            return $options['name'];
        }
        else {
            throw new Exception("File upload of '{$fileSource['name']}' to '{$dir}' has failed");
        }
    }
    
    /**
     * Downloads file to given directory under app root.
     * 
     * @param array $fileSource Download file source which must be provided as array like:
     *      array('http://my-url.com/of/file.jpg') 
     * @param string $dir App root relative destination directory to download the file into
     * @param array $options Array of following options:
     *      - 'name' (string) Name to save the file under. If not provided (NULL)
     *          then the name of downloaded file is used. Defaults to NULL.
     *      - 'unique' (bool) If TRUE then the unique filename is searched under
     *          provided $dir, to avoid overwriting of existing file. If FALSE
     *          then the file is saved under its name, even in case of name colision
     *          and the existing file is overwritten by the new one. Defaults to TRUE.
     * 
     * @return string|int|bool On success returns filename (without path) under which the download was saved.
     *      If file source is invalid then constant File::NOT_SOURCE (int, 0) is returned. 
     * 
     * @throws Exception On invalid destination folder or on failed saving the downloaded 
     *      file to $dir.
     */
    static public function download($fileSource, $dir, $options = array()) {
        $defaults = array(
            'name' => null,
            'unique' => true,
        );
        $options = array_merge($defaults, $options);
        // check for file source validity
        if (
            !is_array($fileSource)
            || !Validate::url(reset($fileSource), array('scheme' => 'http'))
        ) {
            return self::NOT_SOURCE;
        }
        // get file url
        $fileUrl = reset($fileSource);
        // use source file name if no explicit name is provided
        if (empty($options['name'])) {
            $options['name'] = self::getTransferFilename($fileSource, array(
                'sourceType' => self::DOWNLOAD_SOURCE,
            ));
        }
        // if explicit name is provided then check if it has extension and if not 
        // then inherit it from source file (if any)
        elseif (
            !self::getPathinfo($options['name'], PATHINFO_EXTENSION)
            && ($transferName = self::getTransferFilename($fileSource, array(
                'sourceType' => self::DOWNLOAD_SOURCE,
            )))
            && ($extension = self::getPathinfo($transferName, PATHINFO_EXTENSION))
        ) {
            $options['name'] = rtrim($options['name'], '.') . '.' . $extension;
        }
        // if there is no extension in the name (even after all above effort)
        // then we will try to resolve it by file mime type - 2 attemps follows:
        $hasTmpFile = false;
        if (!self::getPathinfo($options['name'], PATHINFO_EXTENSION)) {
            $hasTmpFile = true;
            // destination directory
            $tmpDir = self::getRelativePath(TMP . DS . 'files');
            $tmpName = self::download($fileSource, $tmpDir, array(
                // set .tmp extension to avoid infinite cycling in the same problem
                'name' => uniqid('file_download_') . '.tmp', 
                'unique' => true,
            ));
            $tmpFile = self::getAbsolutePath($tmpDir) . DS . $tmpName;
            $mimeType = mime_content_type($tmpFile);
            if (($extension = self::getExtensionByMimeType($mimeType))) {
                $options['name'] = rtrim($options['name'], '.') . '.' . $extension;
            }
        }
        if (!self::getPathinfo($options['name'], PATHINFO_EXTENSION)) {
            try {
                if (
                    App::request($fileUrl, array(
                        'returnResponse' => false,
                        'includeHeader' => false,
                        'includeBody' => false,
                        'followLocation' => false,
                        'getInfo' => CURLINFO_CONTENT_TYPE,
                    ), $mimeType)
                    && ($extension = self::getExtensionByMimeType($mimeType))
                ) {
                    $options['name'] = rtrim($options['name'], '.') . '.' . $extension;
                }
                else {
                    // remove the above downloaded tmp file
                    @unlink($tmpFile);
                    throw new Exception(sprintf('Resolution of extension for file downloaded from %s has failed', $fileUrl));
                }
            } 
            catch (Throwable $e) {
                // remove the above downloaded tmp file
                @unlink($tmpFile);
                throw new Exception(
                    sprintf('Resolution of extension for file downloaded from %s has failed because of exception: %s', $fileUrl, $e->getMessage()), 
                    null, 
                    $e
                );
            }
        }
        // sanitize name
        $options['name'] = Sanitize::filename($options['name']);
        // destination directory
        if (!self::ensurePath($dir)) {
            throw new Exception("Invalid destination directory {$dir}");
        }
        // get unique filename if required
        if ($options['unique']) {
            $result = self::getUniqueName($options['name'], $dir, array(
                'withExtension' => true,
                'reserve' => true,
            ));
            if (!$result) {
                throw new Exception("Search of unique filename for '{$options['name']}' in '{$dir}' has failed");
            }
            $options['name'] = $result;
        }
        // download the file
        $destinationFile = self::getAbsolutePath($dir) . DS . $options['name'];
        if (!$hasTmpFile) {  
            try {
//                if (!@copy($fileUrl, $destinationFile)) {
                if (!App::request($fileUrl, array(
                    'outputFile' => self::getRelativePath($destinationFile),
                ))) {
                    throw new Exception("File download of '{$fileUrl}' to '{$destinationFile}' has failed");
                }
            } 
            catch (Throwable $e) {
                throw new Exception("File download of '{$fileUrl}' to '{$destinationFile}' has failed", null, $e);
            }
        }
        elseif (!@rename($tmpFile, $destinationFile)) {
            throw new Exception("File download of '{$fileUrl}' to '{$destinationFile}' has failed because of failure of tmp file rename");
        }
        return $options['name'];
    }
    
    /**
     * Gets file from FTP and stores it to given directory under app root.
     * 
     * @param array $fileSource Ftp file source which must be provided as array like:
     *      array('ftp://myuser:<EMAIL>:21/my/path/to/fiel.txt') 
     * @param string $dir App root relative destination directory to save the file into
     * @param array $options Array of following options:
     *      - 'name' (string) Name to save the file under. If not provided (NULL)
     *          then the name of FTP file is used. Defaults to NULL.
     *      - 'unique' (bool) If TRUE then the unique filename is searched under
     *          provided $dir, to avoid overwriting of existing file. If FALSE
     *          then the file is saved under its name, even in case of name colision
     *          and the existing file is overwritten by the new one. Defaults to TRUE.
     * 
     * @return string|int|bool On success returns filename (without path) under which the download was saved.
     *      If file source is invalid then constant File::NOT_SOURCE (int, 0) is returned. 
     *      If no file was downloaded then returns FALSE.
     * 
     * @throws Exception On invalid destination folder or on failed saving the downloaded 
     *      file to $dir.
     */
    static public function getFromFtp($fileSource, $dir, $options = array()) {
        $defaults = array(
            'name' => null,
            'unique' => true,
        );
        $options = array_merge($defaults, $options);
        // check for file source validity
        if (
            !is_array($fileSource)
            || !Validate::url(reset($fileSource), array('scheme' => 'ftp'))
        ) {
            return self::NOT_SOURCE;
        }
        // get file url
        $fileUrl = reset($fileSource);
        $parsedFileUrl = parse_url($fileUrl);
        // use source file name if no explicit name is provided
        if (empty($options['name'])) {
            $options['name'] = self::getTransferFilename($fileSource, array(
                'sourceType' => self::FTP_SOURCE,
            ));
        }
        // if explicit name is provided then check if it has extension and if not 
        // then inherit it from source file (if any)
        elseif (
            !self::getPathinfo($options['name'], PATHINFO_EXTENSION)
            && ($extension = self::getPathinfo($fileUrl, PATHINFO_EXTENSION))
        ) {
            $options['name'] = rtrim($options['name'], '.') . '.' . $extension;
        }
        // sanitize name
        $options['name'] = Sanitize::filename($options['name']);
        // destination directory
        if (!self::ensurePath($dir)) {
            throw new Exception("Invalid destination directory {$dir}");
        }
        // get unique filename if required
        if ($options['unique']) {
            $result = self::getUniqueName($options['name'], $dir, array(
                'withExtension' => true,
                'reserve' => true,
            ));
            if (!$result) {
                throw new Exception("Search of unique filename for '{$options['name']}' in '{$dir}' has failed");
            }
            $options['name'] = $result;
        }
        // get the file from FTP
        $parsedFileUrl = parse_url($fileUrl);
        App::loadLib('App', 'Ftp');
        try {
            $Ftp = new Ftp($parsedFileUrl['host'], array(
                'username' => Sanitize::value($parsedFileUrl['user']),
                'password' => Sanitize::value($parsedFileUrl['pass']),
                'port' => Sanitize::value($parsedFileUrl['port']),
            ));
            $Ftp->get($parsedFileUrl['path'], $dir, array(
                'name' => $options['name'], 
                'overwrite' => true // there is reserved file name, so overwrite must be TRUE
            ));
        }
        catch (Throwable $e) {
            return false;
        }
        
        return $options['name'];
    }
    
    /**
     * Copies file to given directory under app root.
     * 
     * @param array $fileSource Copy file source which must be provided as array like:
     *      array('/app/root/relative/path/of/file.jpg'). The path must be app root relative.
     * @param string $dir App root relative destination directory to copy the file into
     * @param array $options Array of following options:
     *      - 'name' (string) Name to save the file under. If not provided (NULL)
     *          then the name of copied file is used. Defaults to NULL.
     *      - 'unique' (bool) If TRUE then the unique filename is searched under
     *          provided $dir, to avoid overwriting of existing file. If FALSE
     *          then the file is saved under its name, even in case of name colision
     *          and the existing file is overwritten by the new one. Defaults to TRUE.
     * 
     * @return string|int|bool On success returns filename (without path) under which the copy was saved.
     *      If file source is invalid then constant File::NOT_SOURCE (int, 0) is returned. 
     *      If no file was copied then returns FALSE.
     * 
     * @throws Exception On invalid destination folder or on failed saving the copied 
     *      file to $dir.
     */
    static public function copy($fileSource, $dir, $options = array()) {
        $defaults = array(
            'name' => null,
            'unique' => true,
        );
        $options = array_merge($defaults, $options);
        // check for file source validity
        if (!is_array($fileSource)) {
            return self::NOT_SOURCE;
        }
        if (!Validate::path(reset($fileSource), array('file' => true, 'exists' => true))) {
            return false;
        }
        // get absolute file path
        $filePath = self::getAbsolutePath(reset($fileSource));
        // use source file name if no explicit name is provided
        if (empty($options['name'])) {
            $options['name'] = self::getTransferFilename($fileSource, array(
                'sourceType' => self::COPY_SOURCE,
            ));
        }
        // if explicit name is provided then check if it has extension and if not 
        // then inherit it from source file (if any)
        elseif (
            !self::getPathinfo($options['name'], PATHINFO_EXTENSION)
            && ($extension = self::getPathinfo($filePath, PATHINFO_EXTENSION))
        ) {
            $options['name'] = rtrim($options['name'], '.') . '.' . $extension;
        }
        // sanitize name
        $options['name'] = Sanitize::filename($options['name']);
        // destination directory
        if (!self::ensurePath($dir)) {
            throw new Exception("Invalid destination directory {$dir}");
        }
        // get unique filename if required
        if ($options['unique']) {
            $result = self::getUniqueName($options['name'], $dir, array(
                'withExtension' => true,
                'reserve' => true,
            ));
            if (!$result) {
                throw new Exception("Search of unique filename for '{$options['name']}' in '{$dir}' has failed");
            }
            $options['name'] = $result;
        }
        // copy the file to destination file
        $destinationFile = self::getAbsolutePath($dir) . DS . $options['name'];
        if (@copy($filePath, $destinationFile)) {
            return $options['name'];
        }
        else {
            throw new Exception("File copy of '{$filePath}' to '{$destinationFile}' has failed");
        }
    }
    
    /**
     * Gets the file source type for provided file source
     * 
     * NOTE: This method checks existence of provided source only in case of copy source. 
     * In case of upload and download source the existence of source is not checked
     * and the source type is resolved only according $fileSource structure.
     * 
     * @param array $fileSource File source specification. Valid possibilities
     *      are uploaded file data, or array containing single URL to download file 
     *      or array containing app root relative path of local file under app root for copy.
     * @param bool $exceptionOnInvalid Optional. If TRUE then an exception is thrown
     *      instead of returning FALSE if provided file source does not meet requirements
     *      for any of available source types. Defaults to FALSE.
     * 
     * @return int Returns:
     *      - File::UPLOAD_SOURCE constant if file source contains data of uploaded file (array). 
     *      - File::DOWNLOAD_SOURCE constant if file source contains URL for file download (string).
     *      - File::COPY_SOURCE constant if file source contains path to local file (string).
     *      - File::NOT_SOURCE if provided file source does not meet requirements for any of available source types.
     *          If $exceptionOnInvalid is TRUE then an exception is thrown instead of returning FALSE.
     * 
     * @throws Exception if provided file source does not meet requirements for any
     *      of available source types and input $exceptionOnInvalid is TRUE. 
     */
    static public function getSourceType($fileSource, $exceptionOnInvalid = false) {
        if (!is_array($fileSource)) {
            if ($exceptionOnInvalid) {
                throw new Exception ("Invalid file source type");
            }
            return self::NOT_SOURCE;
        }
        if (Validate::uploadData($fileSource)) {
            return self::UPLOAD_SOURCE;
        }
        elseif (Validate::downloadData($fileSource)) {
            return self::DOWNLOAD_SOURCE;
        }
        elseif (Validate::ftpData($fileSource)) {
            return self::FTP_SOURCE;
        }
        // check for existence here to avoid mix copy case with case when there is 
        // just saved filename in fileSource (which is get as fileField value)
        elseif (Validate::copyData($fileSource)) {
            return self::COPY_SOURCE;
        }
        elseif ($exceptionOnInvalid) {
            throw new Exception ("Invalid file source type");
        }
        else {
            return self::NOT_SOURCE;
        }
    }  
        
    /**
     * Wrapper method of App::upload/download/copyFile for sake of implicit use 
     * of convenient transfer method
     * 
     * @param array $fileSource File source specification. Valid possibilities
     *      are uploaded file data, or array containing single HTTP URL to download file 
     *      or array containing single FTP URL to get file from FTP or array containing
     *      app root relative path of local file under app root for copy.
     * @param string $dir App root relative destination directory to transfer the file into
     * @param array $options Options of methods App::upload/download/getFromFtp/copy can be used
     *      and also following:
     *      - 'sourceType' (string|bool) Here should be passed the result of File::getSourceType()
     *          called somewhere on higher level. The reason is just optimization (to not
     *          look for source type on each level, if we already know that). Defaults to NULL.
     *      - 'exceptionOnInvalidSourceType' (bool) If TRUE then an exception is thrown
     *          instead of returning FALSE if provided file source does not meet requirements
     *          for any of available source types or if explicit 'sourceType' is invalid. 
     *          Defaults to FALSE.
     * 
     * @return string|int|bool On success returns filename (without path) under which the file was saved.
     *      If file source is invalid then constant File::NOT_SOURCE (int, 0) is returned. 
     *      If no file was transfered then returns FALSE.
     * 
     * @throws Exception On invalid implicit source type (decided from file source)
     *      or invalid explicit 'sourceType' option (both previous only if option 
     *      'exceptionOnInvalidSourceType' is TRUE) or on transfer errors or invalid source type
     */
    static public function transfer($fileSource, $dir, $options = array()) {
        $defaults = array(
            'sourceType' => null,
            'exceptionOnInvalidSourceType' => false
        );
        $options = array_merge($defaults, $options);
        if ($options['sourceType'] === null) {
            $options['sourceType'] = self::getSourceType(
                $fileSource, 
                $options['exceptionOnInvalidSourceType']
            );
        }
        if ($options['sourceType'] == self::UPLOAD_SOURCE) {
            $fileName = self::upload(
                $fileSource, 
                $dir, 
                $options
            );
        }
        elseif ($options['sourceType'] == self::DOWNLOAD_SOURCE) {
            $fileName = self::download(
                $fileSource, 
                $dir, 
                $options
            );
        }
        elseif ($options['sourceType'] == self::FTP_SOURCE) {
            $fileName = self::getFromFtp(
                $fileSource, 
                $dir, 
                $options
            );
        }
        elseif ($options['sourceType'] == self::COPY_SOURCE) {
            $fileName = self::copy(
                $fileSource, 
                $dir, 
                $options
            );
        }
        elseif ($options['exceptionOnInvalidSourceType']) {
            throw new Exception ("Invalid file source type");
        }
        else {
            return self::NOT_SOURCE;
        }
        return $fileName; // this can be FALSE too
    }
    
    /**
     * Resolves the original filename (more precisely basename = filename + extension) of 
     * transfered file according to provided file source
     * 
     * @param array $fileSource File source specification. Valid possibilities
     *      are uploaded file data, or array containing single URL to download file 
     *      or array containing app root relative path of local file under app root for copy.
     * @param array $options Following options can be passed:
     *      - 'sourceType' (string|bool) Here should be passed the result of File::getSourceType()
     *          called somewhere on higher level. The reason is just optimization (to not
     *          look for source type on each level, if we already know that). Defaults to NULL.
     *      - 'exceptionOnInvalidSourceType' (bool) If TRUE then an exception is thrown
     *          instead of returning FALSE if provided file source does not meet requirements
     *          for any of available source types or if explicit 'sourceType' is invalid. 
     *          Defaults to FALSE.
     * 
     * @return string|bool On success returns original filename of transfered file.
     *      FALSE On invalid implicit source type (decided from file source)
     *      or invalid explicit 'sourceType' option (but only if the option 'exceptionOnInvalidSourceType' is FALSE).
     * 
     * @throws Exception On invalid implicit source type (decided from file source)
     *      or invalid explicit 'sourceType' option (both previous only if option 
     *      'exceptionOnInvalidSourceType' is TRUE) or on transfer errors or invalid source type
     */
    static public function getTransferFilename($fileSource, $options) {
        $defaults = array(
            'sourceType' => null,
            'exceptionOnInvalidSourceType' => false
        );
        $options = array_merge($defaults, $options);
        if ($options['sourceType'] === null) {
            $options['sourceType'] = self::getSourceType(
                $fileSource, 
                $options['exceptionOnInvalidSourceType']
            );
        }
        if ($options['sourceType'] == self::UPLOAD_SOURCE) {
            $fileName = $fileSource['name'];
        }
        elseif ($options['sourceType'] == self::DOWNLOAD_SOURCE) {
            $fileName = parse_url(reset($fileSource));
            $fileName = pathinfo($fileName['path'], PATHINFO_BASENAME); 
        }
        elseif ($options['sourceType'] == self::FTP_SOURCE) {
            $fileName = parse_url(reset($fileSource));
            $fileName = pathinfo($fileName['path'], PATHINFO_BASENAME); 
        }
        elseif ($options['sourceType'] == self::COPY_SOURCE) {
            $fileName = pathinfo(reset($fileSource), PATHINFO_BASENAME);
        }
        elseif ($options['exceptionOnInvalidSourceType']) {
            throw new Exception ("Invalid file source type");
        }
        else {
            return false;
        }
        return $fileName;
    }
    
    /**
     * Returns filename which is unique for each of transforms paths
     * 
     * @param string $name Filename to create an unique version for
     * @param array $transforms Array of file transforms arrays
     * @param array $options Optional. Following options can be used:
     *      - 'tries' (int) Number of tries. If 0 then number of tries is not limited
     *          and the method search till the first free name is found. Defaults to 0.
     *      - 'reserve' (bool) If TRUE then found filename will be reserved by creating a fake
     *          empty file with found unique filename under all provided directories. Defaults to FALSE.
     *      - 'extension' (string) File extension to be used to find unique filename. 
     *          If $dir is array and there are a defined extensions for directories then 
     *          these extensions are used instead of this option. Defaults to NULL 
     *          (extension from provided $filename will be used).
     *      - 'withExtension' (bool) If TRUE then the unique filename is returned together with extension.
     *          Defaults to FALSE - unique filename is returned without extension.
     *          NOTE: This option can be used only if a single extension is used
     *          for all provided dirs. Othervise an exception will be raised.
     * 
     * @return string|bool An unique filename version (without extension) for provided transforms. 
     *      Filename contains extension if option 'withExtensions' is TRUE.
     *      FALSE if number of tries were exceeded
     * 
     * @throws Exception if one transforms paths does not exist
     */
    static public function getTransformsUniqueName($name, $transforms, $options = array()) {
        $paths = array();
        foreach ($transforms as $transform) {
            $paths[$transform['path']] = Sanitize::value($transform['extension']);
        }
        return self::getUniqueName($name, $paths, $options);
    }
    
    /**
     * Checks if file is transformable (e.g. 'png', 'gif', 'jpg', 'jpeg')
     * or not (e.g. 'pdf', 'doc', 'xls', ...).
     * 
     * @param string $file App root relative filename path of file
     * @param array $options Following are available:
     *      - 'exists' (bool) If TRUE then the existence of $file is verified.
     *          Defaults to FALSE (the check is not done).
     * 
     * @return bool
     */
    static public function isTransformable($file, $options = array()) {
        $defaults = array(
            'exists' => false,
        );
        $options = array_merge($defaults, $options);
        return Validate::path($file, array(
            'file' => true,
            'exists' => $options['exists'],
            'extension' => array('png', 'gif', 'jpg', 'jpeg'),
        ));
    }
    
    /**
     * Creates file transform(s) according to provided transform options and options 
     * 
     * @param string $file App root relative filename path of file to make variants for
     * @param array $transform Array of following file transform options:
     *      - 'path' (string) App root relative path to target dir where the file transform
     *          should be saved
     *      - 'extension' (string) Target file extension, e.g. 'jpg', 'png', 'gif'.
     *          if not defined then an $file extension is used
     *      - 'quality' (int) Quality of transformed file. Integer between 1 and 100.
     *          Defaults to NULL, means full quality is preserved.
     *      - 'crop', 'fit', 'flip', 'resize', 'rotate', 'pngGifToJpg' ... (array) Array of args
     *          for required method of Image_Transform class. See class definition
     *          in app/vendors/imagetransform/Image/Transform.php for all
     *          possible methods and their args. Method 'pngGifToJpg' shoud be used 
     *          to convert transparent png/gif into jpg and replace transparent pixels 
     *          by specified color (if transformed file is not png then nothing happens).
     *          Methods are processed in order they are listed in transform options.
     * @param array $options Optional. Following options can be used:
     *      - 'multiple' (bool) If TRUE then $transform is an array containing
     *          multiple transform options to create many transforms of one file
     *          at once. Defaults to FALSE.
     *      - 'name' (string) If defined then this name is used for all created file transforms.
     *          Defaults to NULL - name of provided file is used.
     *      - 'unique' (bool) If TRUE then an unique version of name is searched for in
     *          target path. Defaults to TRUE.
     *      - 'extension' (string) Target file extension. This is used as default in 
     *          case that there is no 'extension' provided in $transform options.
     *          Defaults to NULL.
     *      - 'withExtension' (bool) If TRUE then filename of transformed file(s) is 
     *          returned with extension. Defaults to FALSE - filename is returned 
     *          without extension. NOTE: This option can be used only if a single 
     *          extension is used for all provided transforms. Otherwise an exception will be raised.
     * 
     * @return string Filename used for variants files (without extension!). Filename
     *      has extension only if option 'withExtension' is set TRUE.
     */
    static public function transform($file, $transform = array(), $options = array()) {
        $transformDefaults = array(
            'path' => null,
            'extension' => null, 
            'quality' => null,  
        );
        $defaults = array(
            'name' => null,
            'unique' => true,
            'multiple' => false,
            'extension' => null,
            'withExtension' => false,
        );
        $options = array_merge($defaults, $options);
        // validate source file
        if (!self::isTransformable($file, array('exists' => true))) {
            // tiff images are not supported
            throw new Exception_File_NonTransformable(__e(__FILE__, 'Only existing jpg/png/gif image file can be transformed'));
        }
        // get file parts
        $fileParts = self::getPathinfo($file);
        // normalize and validate transform options
        if (!$options['multiple']) {
            $transform = array($transform);
        }        
        foreach ($transform as &$variant) {
            $variant = array_merge($transformDefaults, $variant);
            // get extension
            if (!$variant['extension']) {
                if ($options['extension']) {
                    $variant['extension'] = $options['extension'];
                }
                elseif (!empty($fileParts['extension'])) {
                    $variant['extension'] = $fileParts['extension'];
                }
                else {
                    throw new Exception(__e(__FILE__, 'Undefined transform extension'));
                }
            }
            // normalize extension
            $variant['extension'] = self::normalizeExtension($variant['extension']);
            // validate extension
            if (
                $variant['extension'] !== 'png'
                && $variant['extension'] !== 'gif'
                && $variant['extension'] !== 'jpg'
            ) {
                throw new Exception(__e(__FILE__, 'Invalid extension %s for image variants. Use one of jpg/png/gif', $variant['extension']));
            }
            // validate quality
            if (
                !empty($variant['quality'])
                && (
                    !Validate::intNumber($variant['quality'])
                    || $variant['quality'] < 0 
                    || $variant['quality'] > 100
                )
            ) {
                throw new Exception(__e(__FILE__, 'Invalid quality %s for image variants. Use an integer between 1 and 100', $variant['quality']));
            }
            // normailze quality
            if (!empty($variant['quality'])) {
                $variant['quality'] = (int)$variant['quality'];
                if ($variant['extension'] !== 'png') {
                    $variant['quality'] = (int)round(9 * $variant['quality'] / 100, 0);
                }
            }
            // get path
            if (!$variant['path']) {            
                if ($fileParts['dirname']) {
                    $variant['path'] = $fileParts['dirname'];
                }
                else {
                    throw new Exception(__e(__FILE__, 'Undefined transform path'));
                }
            }
            // validate and ensure path
            if (
                !Validate::path($variant['path'], array('dir' => true))
                || !self::ensurePath($variant['path'])
            ) {
                throw new Exception(__e(__FILE__, 'Invalid transform path %s', $variant['path']));
            }
        }
        unset($variant); // unset reference
        
        // load Image_Transform class
        if (!class_exists('Image_Transform')) {
            App::loadVendor('App', 'imagetransform/Image/Transform');
        }
        // get target name
        if (!$options['name']) {
            $options['name'] = $fileParts['basename'];
        }
        // get unique target name
        if ($options['unique']) {
            $options['reserve'] = true;
            $options['name'] = self::getTransformsUniqueName($options['name'], $transform, $options);
        }
        elseif (!$options['withExtension']) {
            $nameParts = self::getPathinfo($options['name']);
            $options['name'] = $nameParts['filename'];
        }
        // get filename (without extension)
        if ($options['withExtension']) {            
            $nameParts = self::getPathinfo($options['name']);
            $filename = $nameParts['filename'];
        }
        else {
            $filename = $options['name'];
        }
        // create variants
        $variantFiles = array();
        foreach ($transform as $variant) {
            $Image = Image_Transform::factory(App::getPropertyImageEngine());
            if (PEAR::isError($Image)) {
                throw new Exception($Image->getMessage());
            }
            $Image->load(self::getAbsolutePath($file));
            $transformOptions = array_diff_key($variant, $transformDefaults);
            $transformed = false;
            foreach ($transformOptions as $method => $args) {
                if (!method_exists($Image, $method)) {
                    continue;
                }
                $transformed = true;
                $args = (array)$args;
                call_user_func_array(array($Image, $method), $args);
            }
            // save variant
            $variantFile = $variant['path'] . DS . $filename . '.' . $variant['extension'];
            $variantFile = self::getAbsolutePath($variantFile);
            $variantFiles[] = $variantFile;
            // - if any transform applied then save transformed variant
            if ($transformed) {
                $result = $Image->save($variantFile, $variant['extension'], $variant['quality']);
            }
            // - if no transform applied then just copy the file to target file
            else {
                $result = @copy(self::getAbsolutePath($file), $variantFile);
            }
            if (PEAR::isError($result) || !$result) {
                foreach ($variantFiles as $variantFile) {
                    @unlink($variantFile);
                }
                if (PEAR::isError($result)) {
                    $message = $result->getMessage();
                }
                else {
                    $message = __e(__FILE__, 'Unable to write the file: %s', $file);
                }
                throw new Exception($message);
            }
        }
            
        return $options['name'];
    }
    
    /**
     * Converts file encoding.
     *
     * @param string $fromEncoding
     * @param string $toEncoding
     * @param string $file
     * @return bool 
     */
    static public function setEncoding($file, $fromEncoding, $toEncoding) {
        // create temp file name
        $pathInfo = self::getPathinfo($file);
        $tmpFile = $pathInfo['dirname'] . DS . uniqid("{$pathInfo['filename']}_tmp") . '.' . $pathInfo['extension'];
        // open files
        $ifh = fopen($file, 'r');
        $ofh = fopen($tmpFile, 'w');
        if (!$ifh || !$ofh) {
            return false;
        }
        // convert (make it line by line to be memory safe and so allow to convert text files of any size)
        while (($line = fgets($ifh)) !== false) {
            $line = iconv($fromEncoding, $toEncoding, $line);
            if (fwrite($ofh, $line) === false) {
                fclose($ifh);
                fclose($ofh);
                unlink($tmpFile);
                return false;
            }
        }
        fclose($ifh);
        fclose($ofh);
        // rename temp file
        return rename($tmpFile, $file);
    }
        
    /**
     * Detects file encoding
     * 
     * NOTE: Does not work quite properly. Does not succeed to detect CP1250!!!
     *
     * @param string $file
     * @param array $encodingList See mb_detect_encoding() 
     * @param bool $strict See mb_detect_encoding() 
     * @param int $linesCount Max number of file lines to be examined for encoding detection.
     *      If not set then all lines (whole file) is examined.  
     * @return mixed  
     */
    static public function getEncoding($file, $encodingList = null, $strict = false, $linesCount = -1) {
        $fh = fopen($file, 'r');
        if (!$fh) {
            return false;
        }
        if (empty($encodingList)) {
            $encodingList = mb_detect_order();
        }
        // convert (make it line by line to be memory safe and so allow to convert text files of any size)
        $fileEncoding = false;
        $lineEncodings = array();
        //debug($encodingList,  '$encodingList'); //debug
        while (
            ($line = fgets($fh)) !== false
            && $linesCount != 0 
        ) {
            if (!$line)  {
                continue;
            }
            //debug($line); //debug
            $lineEncoding = mb_detect_encoding($line, $encodingList, $strict);
            //debug($lineEncoding); //debug
            if (isset($lineEncodings[$lineEncoding])) {
                $lineEncodings[$lineEncoding]++;
            }
            else {
                $lineEncodings[$lineEncoding] = 1;
            }
            $linesCount -= 1;
        }
        //debug($lineEncodings, '$lineEncodings'); //debug
        fclose($fh);
        arsort($lineEncodings);
        reset($lineEncodings);
        $fileEncoding = key($lineEncodings);
        return $fileEncoding;
    }
    
    /**
     * Method for parsing .po file to simple array.
     * 
     * For .po files format see https://www.gnu.org/software/gettext/manual/html_node/PO-Files.html
     * 
     * NOTE: Use msgcat to merge more .po files into one and avoid duplicities. In list of 
     * msgcat input files introduce at first newer file(s): 
     * msgcat --use-first inputfile01.po inputfile02.po -o outputfile.po
     * NOTE: Use msguniq to remove duplicit messages definitions in .po file:
     * msguniq --use-first myfile.po -o myfile.po
     * For more utilities see http://www.gnu.org/software/gettext/manual/gettext.html#Manipulating
     * 
     * @param string $file App root relative path to .po file
     * @param array $options Following are available:
     *      - 'translatedOnly' If TRUE then only translated texts (means msgstr 
     *          is not empty string and does not equal to msgid) are included in 
     *          output array. Defaults to TRUE.
     * 
     * @return array
     */
    static public function parsePo($file, $options = array()) {
        $options = array_merge(array(
            'translatedOnly' => true,
        ), $options);
        $file = self::getAbsolutePath($file);
        if (!is_readable($file)) {
            throw new Exception(Str::fill('Invalid .po file %s', $file));
        }
        $handle = fopen($file, 'r');
        $translations = array();
        $part = null; // msgid | msgstr
        $msgid = '';
        $msgstr = '';
        $fuzzy = false;
        while ($line = fgets($handle)) {
            if (
                // separating whitespace between two items in catalogue
                trim($line) === '' 
            ) {
                // store previous msgid and msgstr 
                // but only if non-empty, translated and non-fuzzy
                if (
                    // include only translated (if required) and nonfuzzy pairs 
                    $msgid !== ''
                    && (
                        $msgstr !== ''
                        || !$options['translatedOnly']
                    )
                    && (
                        $msgstr !== $msgid
                        || !$options['translatedOnly']
                    )
                    && empty($fuzzy)
                ) {
                    // quotes are in po file automatically escaped so convert them to unescaped quotes
                    $msgid = str_replace('\"', '"', $msgid);
                    $msgstr = str_replace('\"', '"', $msgstr);
                    // put msgid and msgstr to translations
                    $translations[$msgid] = $msgstr;
                }
                // prepare for reading next msgid and msgstr pair
                $msgid = '';
                $msgstr = '';
                $fuzzy = false;
            }
            elseif (
                substr($line, 0, 2) === '#,' 
                && strpos($line, 'fuzzy') !== false
            ) {
                $fuzzy = true;
            }
            elseif (substr($line, 0, 5) == 'msgid') {
                // coming to msgid means that there can be some  str to store
                $part = 'msgid';
                // get string
                $string = explode('"', $line);
                array_pop($string);
                array_shift($string);
                $string = implode('"', $string);
                $msgid = $string;
            }
            elseif (substr($line, 0, 6) == 'msgstr') {
                $part = 'msgstr';
                // get string
                $string = explode('"', $line);
                array_pop($string);
                array_shift($string);
                $string = implode('"', $string);
                $msgstr = $string;
            }
            elseif (
                substr($line, 0, 1) === '"'
            ) {
                // get string
                $string = explode('"', $line);
                array_pop($string);
                array_shift($string);
                $string = implode('"', $string);
                if ($part === 'msgid') {
                    $msgid .= $string;
                }
                elseif ($part === 'msgstr') {
                    $msgstr .= $string;
                }
            }
        }
        // treat last msgid and msgstr
        if (
            // include only translated (if required) and nonfuzzy pairs 
            $msgid !== ''
            && (
                $msgstr !== ''
                || !$options['translatedOnly']
            )
            && (
                $msgstr !== $msgid
                || !$options['translatedOnly']
            )
            && empty($fuzzy)
        ) {
            // quotes are in po file automatically escaped so convert them to unescaped quotes
            $msgid = str_replace('\"', '"', $msgid);
            $msgstr = str_replace('\"', '"', $msgstr);
            // put msgid and msgstr to translations
            $translations[$msgid] = $msgstr;
        }
        return $translations;
    }
    
    /**
     * Compiles .po file into either php or js code
     * 
     * NOTE: Use msgcat to merge more .po files into one and avoid duplicities. In list of 
     * msgcat input files introduce at first newer file(s): 
     * msgcat --use-first inputfile01.po inputfile02.po -o outputfile.po
     * NOTE: Use msguniq to remove duplicit messages definitions in .po file:
     * msguniq --use-first myfile.po -o myfile.po
     * For more utilities see http://www.gnu.org/software/gettext/manual/gettext.html#Manipulating
     * 
     * @param string $file App root relative path to .po file
     * @param string $format Output format, possible values are 'php' or 'js'.
     * 
     * @return string The .po file translations compiled to either php or js code
     * @throws Exception on invalid $file or $format specification
     */
    static public function compilePo($file, $format) {
        $translations = self::parsePo($file);
        return Arr::getLiteral($translations, $format);
    }
    
    /**
     * Zips provided file or directory
     * 
     * @param string $file App root relative path to file or directory to zip
     * @param array $options Following are available:
     *      - 'path' (string) Path to create zipped file on. Defauts to NULL, means 
     *          that the zipped file is created on the same path as the $file.
     *      - 'name' (string) Name to create zipped file with. Defauts to NULL, means 
     *          that the zipped file is created with the same name as the $file.
     *          The extension '.zip' is added to this name.
     * 
     * @return string App root relative filename path of zipped file
     * 
     * @throws Exception on failure
     */
    static public function zip($file, $options = array()) {
        $defaults = array(
            'path' => null,
            'name' => null,
        );
        $options = array_merge($defaults, $options);
        $file = trim($file, DS);
        $fileParts = self::getPathinfo($file);
        if (!$options['path']) {
            $options['path'] = $fileParts['dirname'];
        }
        if (!$options['name']) {
            $options['name'] = $fileParts['basename'];
        }
        $zipFile = $options['path'] . DS . $options['name'] . '.zip';
        if (!class_exists('ZipArchive')) {
            throw new Exception('ZipArchive class is not available');
        }
        $Zip = new ZipArchive();
        if (
            $Zip->open(ROOT . DS . $zipFile, ZIPARCHIVE::CREATE| ZipArchive::OVERWRITE) !== true
        ) {
            throw new Exception(__e(__FILE__, 'Failed to create zip archive %s', $zipFile));
        }
        if (!is_dir(ROOT . DS . $file)) {
            if (
                !$Zip->addFile(ROOT . DS . $file, $fileParts['basename'])
            ) {
                throw new Exception(__e(__FILE__, 'Failed to add file %s to zip archive %s', $file, $zipFile));
            }
        }
        else {
            // create recursive directory iterator
            $items = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator(ROOT . DS . $file),
                RecursiveIteratorIterator::LEAVES_ONLY
            );
            $relativePathStart = strlen(dirname(ROOT . DS . $file)) + 1;
            foreach ($items as $item) {
                // skip directories (they would be added automatically)
                if (!$item->isDir()) {
                    $filePath = $item->getRealPath();
                    $relativePath = substr($filePath, $relativePathStart);
                    if (
                        !$Zip->addFile($filePath, $relativePath)
                    ) {
                        throw new Exception(__e(__FILE__, 'Failed to add file %s to zip archive %s', self::getRelativePath($filePath), $zipFile));
                    }
                }
            }        
        }
        if (!$Zip->close()) {
            throw new Exception(__e(__FILE__, 'Failed to close zip archive %s', $zipFile));
        }
        
        return $zipFile;
    }
    
    /**
     * Unzips provided file
     * 
     * ATTENTION: Method will overwrite existing files with same name as extracted files!
     * 
     * NOTE: In case of .zip files we do not know the filename(s) of extracted file(s).
     * In case of .gz/.gzip there can only one file included in archive and the extracted
     * file is named according the archive omitting the .gz/.gzip extension
     * 
     * @param string $archive App root relative path to zip or gzip archive file. In case 
     *      of gzip archive the file must have extension .gz or .gzip
     * @param array $options Following are available:
     *      - 'path' (string) Path to extract zip $archive into. Defauts to NULL, means 
     *          that the zip $archive is extracted on the same path as it is placed.
     *      - 'delete' (bool) If TRUE then the $archive is deleted after succesfull 
     *          extraction. Defauts to FALSE.
     * 
     * @return string App root relative extract path
     * 
     * @throws Exception on failure
     */
    static public function unzip($archive, $options = array()) {
        $defaults = array(
            'path' => null,
            'delete' => false,
        );
        $options = array_merge($defaults, $options);
        $archive = ltrim($archive, DS);
        $fileParts = self::getPathinfo($archive);
        if (!$options['path']) {
            $options['path'] = $fileParts['dirname'];
        }
        $options['path'] = trim($options['path'], DS);
        // process .gz, .gzip files
        if (strtolower(substr($fileParts['extension'], 0, 2)) === 'gz') {
            $sfp = gzopen(ROOT . DS . $archive, "rb");
            $tfp = fopen(ROOT . DS . $options['path'] . DS . $fileParts['filename'], "w");
            while (!gzeof($sfp)) {
                $string = gzread($sfp, 4096);
                fwrite($tfp, $string, strlen($string));
            }
            gzclose($sfp);
            fclose($tfp);
        }
        // process .zip files
        else {
            if (!class_exists('ZipArchive')) {
                throw new Exception('ZipArchive class is not available');
            }
            $Zip = new ZipArchive();
            if (
                $Zip->open(ROOT . DS . $archive) !== true
                || !$Zip->extractTo(ROOT . DS . $options['path'])
                || !$Zip->close()
            ) {
                throw new Exception(__e(__FILE__, 'Failed to extract archive %s to %s', $archive, $options['path']));
            }
        }
        
        if ($options['delete']) {
            unlink(ROOT . DS . $archive);
        }
        
        return $options['path'];
    }        
    
    /**
     * Removes given tmp directory with all included files and subdirectories recursively.
     * A recursive version of classic php rmdir function.
     * 
     * ATTENTION: If $dir does not points to app tmp (TMP) or it points to TMP itself 
     * ('', '/') then nothing happens because it is allowed only delete directories under TMP.
     * 
     * @param string $dir An app root relative path pointing to under TMP directory
     */
    static public function removeTmpDir($dir) { 
        // check if $dir points to TMP
        // but do not allow to remove TMP dir
        $dir = self::normalizePath($dir);
        $dir = self::getAbsolutePath($dir);
        if (
            strpos($dir, TMP) !== 0
            || $dir === TMP
        ) {
            return;
        }
        if (is_dir($dir)) { 
            $objects = scandir($dir); 
            foreach ($objects as $object) { 
                if ($object != "." && $object != "..") { 
                    $object = $dir . DS . $object;
                    if (filetype($object) == "dir") {
                        self::removeTmpDir($object);
                    } else {
                        unlink($object); 
                    }
                } 
            } 
            rmdir($dir); 
        } 
    }
    
    /**
     * Creates pdf file/data from provided html
     * 
     * NOTE: This method uses mPdf package (see https://mpdf.github.io/ and http://www.mpdf1.com/mpdf/index.php )
     * 
     *      The mPdf engine works quite fine, but you cannot await that it is implemented 
     *      with the same complexity as some browser rendering engine. Following can help 
     *      you and save your time:
     *          - do not relly on inheritance
     *          - keep the html and css as simple as possible
     *          - use CSS 2.1, nothing more
     *          - be careful with order of arguments in shorthand properties, e.g. 
     *              "border: 1px solid #000" works but "border: 1px #000 solid" DOES NOT WORK
     *          - (run) grid.less can be used quite well with following: set border-box 
     *              sizing (copy it from beginning of main.less) and set .grid-row 
     *              and .grid-col margin-left/right to 0.
     *      
     *      Check also following limitations of mpdf library (https://mpdf.github.io/about-mpdf/limitations.html):
     *          - The script is (a lot) slower than the original FPDF and html2fpdf. 
     *              Some of this is due to the inclusion of unicode font files (when used), 
     *              but there is also an increase in processing time.
     *          - Tables: Block elements (e.g. DIV or P) are not supported inside tables. 
     *              The content is displayed, but any CSS properties which apply to block 
     *              elements are ignored (e.g. borders, padding, margins etc).
     *          - Block and in-line elements: All HTML elements are hard-coded to 
     *              be treated as block or in-line elements (e.g. equivalent to CSS display:block or display:in-line). 
     *              This cannot be changed using CSS. See HTML tags.
     *          - Special features: Several of the "special" features of mPDF are 
     *              incompatible with each other e.g columns, fixed-position block elements, 
     *              page-break-avoid:inside, Keep-with-table and rotated tables.
     *          - Other: Millimeters are the only accepted dimensions for defining page 
     *              size and margins within mPDF (CSS stylesheets accept all usual units).
     *              Block-level tags (DIV,P etc.) within table cells are only partially 
     *              supported (only the 'inline' type CSS styles are applied). Blocks which 
     *              are defined as position:absolute, fixed or float have only limited support (introduced v4.0).
     * 
     * For mPdf methods reference see https://mpdf.github.io/ > Reference
     * 
     * @example Example of sending pdf file as email attachment:
     * 
     *              App::sendEmail('Hello, I send you a file', '<EMAIL>', array(
     *                  'attachments' => array(array(
     *                      'data' => File::createPdf('Hello world', array('output' => 'string')),
     *                      'filename' => 'my-file.pdf',
     *                      'contentType' => 'application/pdf',
     *                  )),
     *              ))  
     * 
     * @param string $html Html to create pdf from
     * @param array $options Following are available:
     *      - 'name' (string) File name of created pdf file. If the 'output' option
     *          is set to file then it can contain app root relative path, e.g. '/userfiles/contacts/c_159.pdf'.
     *          Defaults to 'doc.pdf'.
     *      - 'output' (string) Output type. Possible values are 'inline', 'download', 
     *          'string' or 'file'. The 'inline' opens pdf directly in browser.
     *          The 'download' opens download dialog in browser. The 'string' output 
     *          can be used e.g. to create email attachments on fly (see example). 
     *          Defaults to 'download'.
     *      - 'format' (string) Page format. Possible values are A0-A10, B0-B10, 
     *          C0-C10, 4A0, 2A0, RA0-RA4, SRA0-SRA4, Letter, Legal, Executive, 
     *          Folio, Demy, Royal, A, B, Ledger, Tabloid. Use array({width}, {height}) 
     *          to set custom format. Both width and height are in millimeters, e.g.
     *          array(210, 297) to get page 210mm wide x 297mm high. Defaults to 'A4'.
     *      - 'orientation' (string) Page orientation. Possible values are 'portrait'
     *          and 'landscape'. Defaults to 'portrait'.
     *      - 'css' (string) Cascading styles string, e.g. 'body {font-size: 15pt;...}.my-class{...}...'.
     *          Or you can grab a content of a css file like file_get_contents(ROOT . DS . 'css' . DS . 'my-pdf.css');
     *      - 'marginTop' (int) Page top margin in millimeters. Defaults to 16.
     *      - 'marginRight' (int) Page right margin in millimeters. If you are creating 
     *          a DOUBLE-SIDED document, the margin values specified will be used for ODD pages; 
     *          left and right margins will be mirrored for EVEN pages. Defaults to 15.
     *      - 'marginBottom' (int) Page bottom margin in millimeters. Defaults to 16.
     *      - 'marginLeft' (int) Page left margin in millimeters. If you are creating 
     *          a DOUBLE-SIDED document, the margin values specified will be used for ODD pages; 
     *          left and right margins will be mirrored for EVEN pages. Defaults to 15.
     *      - 'marginHeader' (int) Page top margin in millimeters. Defaults to 9.
     *      - 'marginHeader' (int) Page right margin in millimeters. Defaults to 9.
     * 
     *      ATTENTION: All other items present in the options are used to set configuration 
     *      properties of mPdf instance. For all available properties see https://mpdf.github.io/reference/mpdf-variables/overview.html
     *      or see directly in mpdf/config.php and mpdf/config_fonts.php
     * 
     * @return void|string Returns pdf data if the 'output' option is set to 'string'.
     */
    static public function createPdf($html, $options = array()) {
        $defaults = array(
            'name' => 'doc.pdf', 
            'output' => 'download',
            'format' => 'A4',
            'orientation' => 'portrait',
            'css' => '',
            'marginTop' => 16,
            'marginRight' => 15,
            'marginBottom' => 16,
            'marginLeft' => 15,
            'marginHeader' => 9,
            'marginHeader' => 9,
            'defaultFontSize' => 0,
            'defaultFont' => '',
        );
        $options = array_merge($defaults, $options);
        // normalize output and orientation
        $options['output'] = substr(strtoupper($options['output']), 0, 1);
        $options['orientation'] = substr(strtoupper($options['orientation']), 0, 1);
        
        App::loadVendor('App', 'mpdf/mpdf');
        $mpdf = new mPDF(
            'utf-8',                    // $mode
            $options['format'], 
            $options['defaultFontSize'],
            $options['defaultFont'],
            $options['marginLeft'],
            $options['marginRight'],
            $options['marginTop'],
            $options['marginBottom'],
            $options['marginHeader'],
            $options['marginFooter'],
            $options['orientation']
        );
        $mpdf->showImageErrors = true;
        $configs = array_diff_key($options, $defaults);
        foreach ($configs as $configName => $configValue) {
            $mpdf->{$configName} = $configValue;
        }
        //$mpdf->allow_charset_conversion=true;  // Set by default to TRUE
        //$mpdf->charset_in='windows-1250';
        if (!empty($options['css'])) {
            $mpdf->WriteHTML($options['css'], 1);
        }
        $mpdf->WriteHTML($html, 0); // 0 - allows to parse also attached css in <style> tags
        //$mpdf->WriteHTML($html);
        if ($options['output'] === 'F') {
            $options['name'] = ROOT . DS . self::normalizePath($options['name'], 'pdf');
        }
        $pdfString = $mpdf->Output($options['name'], $options['output']);
        if ($options['output'] === 'S') {
            return $pdfString;
        }
    }   
    
    /**
     * Outputs the file to output buffer (browser)
     * 
     * NOTE: This method turns off App layout and debug
     * 
     * @param string $file App root relative path to file
     * @param array $options Following are available:
     *      - 'filename' (string) Explicit name of file when downloaded. Extension
     *      is ignored and inherited from $file (if any). If not provided then the 
     *      same as filename of $file. Defaults to NULL.
     *      - 'mimeType' (string) Mime type of file send to output, e.g. 'text/plain' for .txt,
     *      'application/pdf' for .pdf, 'application/vnd.ms-excel' for .xls.  It is 
     *      guessed from $file extension, if not found then defaults to 'text/plain'.
     *      See http://en.wikipedia.org/wiki/Internet_media_type 
     *      or http://www.sitepoint.com/web-foundations/mime-types-complete-list/
     *      for details and complete list.
     *      - 'encoding' (string) Encoding of file send to output. If not provided
     *      then it is not set in headers. Defaults to NULL.
     *      - 'disposition' (string) Disposition of file on the client side. Possible
     *      values are 'download' (file is saved by browser) or 'inline' (file is displayed by browser).
     *      Defaults to 'download'.
     * 
     * @throws Exception on failure of input/output opening or if headers has been already sent
     */
    static public function output($file, $options = array()) {
        $defaults = array(
            'filename' => null,
            'mimeType' => null,
            'encoding' => null,
            'disposition' => 'download',
        );
        $options = array_merge($defaults, $options);
        // normalize
        $file = ltrim($file, DS);
        $fileParts = self::getPathinfo($file);
        if (!$options['filename']) {
            $options['filename'] = $fileParts['filename'];
        }
        $filenameParts = self::getPathinfo($options['filename']);
        $options['filename'] = $filenameParts['filename'];
        if (!empty($fileParts['extension'])) {
            $options['filename'] .= '.' . $fileParts['extension'];
        }
        elseif (!empty($filenameParts['extension'])) {
            $options['filename'] .= '.' . $filenameParts['extension'];
        }
        $options['disposition'] = strtolower($options['disposition']);
        if (empty($options['mimeType'])) {
            $options['mimeType'] = self::getMimeTypeByExtension($fileParts['extension']);
        }
        
        // check headers
        if(headers_sent()) {
            throw new Exception(__e(__FILE__, 'Some data has already been output to browser, can\'t send %s file', $options['filename']));
        }
        // open input and output
        if (!($fh = @fopen(ROOT . DS . $file, 'r'))) {
            throw new Exception(__e(__FILE__, 'Opening of file %s has failed', $file));
        }
        $buffersData = App::closeOpenedOutputBuffers();
        if (!($oh = @fopen('php://output', 'w'))) {
            App::reopenClosedOutputBuffers($buffersData);
            throw new Exception(__e(__FILE__, 'Opening of php ouput buffer has failed'));
        }
        
        // set headers (copied from mpdf/mpdf.php - function Output())
        $contentType = 'Content-Type: ' . $options['mimeType'];
        if (!empty($options['encoding'])) {
            $contentType .= '; charset=' . strtolower($options['encoding']);
        }
        if ($options['disposition'] === 'inline') {
            // display in browser
            header($contentType);
//            if (empty($_SERVER['HTTP_ACCEPT_ENCODING'])) {
//                // don't use length if server using compression
//                header('Content-Length: '.strlen($this->buffer));
//            }
            header('Content-disposition: inline; filename="' . $options['filename'] . '"');
            header('Cache-Control: public, must-revalidate, max-age=0'); 
//            header('Pragma: public');
//            header('Expires: Sat, 26 Jul 1997 05:00:00 GMT'); 
//            header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        }
        else {
			// show download window
			header('Content-Description: File Transfer');
//			header('Content-Transfer-Encoding: binary');
//			header('Content-Type: application/force-download');
//			header('Content-Type: application/octet-stream', false);
//			header('Content-Type: application/download', false);
            header($contentType, false);
//			if (empty($_SERVER['HTTP_ACCEPT_ENCODING'])) {
//				// don't use length if server using compression
//				header('Content-Length: '.strlen($this->buffer));
//			}
			header('Content-disposition: attachment; filename="' . $options['filename'] . '"');
			header('Cache-Control: public, must-revalidate, max-age=0');
//			header('Pragma: public');
//			header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
//			header('Last-Modified: '.gmdate('D, d M Y H:i:s').' GMT');
        }
        
        // write the file to output
        while ($kb = fread($fh, 1024)) {
            fwrite($oh, $kb, 1024);
        }
        
        // close input and output
        fclose($fh);
        fclose($oh);
        App::reopenClosedOutputBuffers($buffersData);
        
        // turn off debug to avoid generation of js code into output in case of download
        App::setDebug(false);
        App::setLayout(false);
    }
    
    /**
     * Returns mime type for provided file path or extension. The file path must 
     * contain extension.
     * 
     * @param string $fileOrExtension File path containing extension or just the 
     *      extension itself
     * 
     * @return string Mime type string
     */
    static public function getMimeTypeByExtension($fileOrExtension) {
        if (($pos = strrpos($fileOrExtension, '.')) !== false) {
            $extension = substr($fileOrExtension, $pos + 1);
        }
        else {
            $extension = $fileOrExtension;
        }
        $extension = strtolower($extension);
        if (isset(self::$mimeTypesByExtensions[$extension])) {
            return self::$mimeTypesByExtensions[$extension];
        }
        return 'text/plain';
    }
    
    /**
     * Returns extension for provided mime type.
     * 
     * @param string $mimeType Mime type returned for file by php function mime_content_type()
     *      or for URL by App::request($url, array(
     *          'returnResponse' => false,
     *          'includeHeader' => false,
     *          'includeBody' => false,
     *          'followLocation' => false,
     *          'getInfo' => CURLINFO_CONTENT_TYPE,
     *      ), $contentType).
     * 
     * @return string|NULL Extension string
     * 
     * @todo: If list of mime types in File::$extensionsByMimeTypes does not contain searched mime type
     * then use solution from http://php.net/manual/en/function.mime-content-type.php#107798
     * and load list of all mimetipes from http://svn.apache.org/repos/asf/httpd/httpd/trunk/docs/conf/mime.types
     */
    static public function getExtensionByMimeType($mimeType) {
        if (empty(self::$extensionsByMimeTypes)) {
            self::$extensionsByMimeTypes = array_flip(self::$mimeTypesByExtensions);
        }
        if (!empty(self::$extensionsByMimeTypes[$mimeType])) {
            return self::$extensionsByMimeTypes[$mimeType];
        }
        return null;
    }
    
    /**
     * Returns mime type of specified $file
     * 
     * @see http://stackoverflow.com/questions/1232769/how-to-get-the-content-type-of-a-file-in-php
     * 
     * @param string $file Path to file. If option 'absolutePath' is TRUE then 
     *      absolute path otherwise App root relative path.
     * @param array $options Following are available:
     *      - 'byExtension' (bool|NULL) If TRUE then mime type is resolved from 
     *          file extension. If FALSE then mime type is resolved from file content.
     *          Defaults to NULL, means that if file is readable then mime type is
     *          resolved from its content otherwise from its extension.
     *      - 'absolutePath' (bool) If TRUE then $file is considered to be an absolute
     *          file path. Defaults to FALSE, means $file is considered to be App
     *          root relative path. 
     * @param bool& $byExtension Optional. Passed by reference. Auxiliary output
     *      keeping info if the mime type was resolved by content (if FALSE) or
     *      by extension (if TRUE) in case that options 'byExtension' is NULL.
     * 
     * @return string|NULL Mime type of provided $file or NULL if cannot be resolved.
     */
    static public function getMimeType($file, $options = array(), &$byExtension = null) {
        $defaults = array(
            'byExtension' => null,
            'absolutePath' => false,
        );
        $options = array_merge($defaults, $options);
        if (empty($options['absolutePath'])) {
            $file = ROOT . DS . self::normalizePath($file);
        }
        else {
            $file = self::normalizeDS($file);
        }
        $readable = is_readable($file);
        if (
            !empty($options['byExtension'])
            || 
            $options['byExtension'] === null
            && !$readable
        ) {
            $byExtension = true;
            return self::getMimeTypeByExtension($file);
        }
        if (
            !$readable 
            && empty($options['byExtension']) 
            && $options['byExtension'] !== null
        ) {
            return null;
        }
        $byExtension = false;
        if (function_exists('finfo_file')) {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $type = finfo_file($finfo, $file);
            finfo_close($finfo);
        } 
        else {
//            require_once 'upgradephp/ext/mime.php';
            $type = mime_content_type($file);
        }
//        if (
//            !$type 
//            || in_array($type, array('application/octet-stream', 'text/plain'))
//        ) {
//            $secondOpinion = exec('file -b --mime-type ' . escapeshellarg($file), $output, $returnCode);
//            if ($returnCode === 0 && $secondOpinion) {
//                $type = $secondOpinion;
//            }
//        }
//        if (
//            !$type 
//            || in_array($type, array('application/octet-stream', 'text/plain'))
//        ) {
////            require_once 'upgradephp/ext/mime.php';
//            $exifImageType = exif_imagetype($file);
//            if ($exifImageType !== false) {
//                $type = image_type_to_mime_type($exifImageType);
//            }
//        }

        return $type;
    }
    
    /**
     * Searches for position of first occurence of specified string in provided 
     * file from specified offset
     * 
     * ATTENTION: If file contains UTF8 chars then returned offset does not correspond
     * to number of characters preceeding the string in file
     * 
     * @param string|resource $file App root relative file path to haystack file 
     *      or a file handle resource returned by fopen($myFile, 'rb'). If provided 
     *      by file path then file is opened and closed internally. If provided as 
     *      file handle then it is up to user to close it.
     * @param string $string String needle to be searched for in haystack file.
     *      It can be also provided as regex delimited by slashes (/). Found match
     *      is then returned by 'match' option.
     * @param array $options Following are available:
     *      - 'offset' (int) If specified, search will start this number of bytes
     *          counted from the beginning of the file. Defaults to 0.
     *      - 'sliceSize' (int) Size of slice the file is read by. If nonregex search
     *          is done then set to lenght of provided string if 'sliceSize' is lower
     *          than string length. If regex search is done then used as provided. 
     *          Increase this if searching in large files, e.g. 4096, 100000. Internally
     *          two following slices are examined at once. Defaults to 1024.
     *      - 'return' (string) Possible values are 'start' and 'after'. If 'start'
     *          then found string start position is returned. If 'after' then the 
     *          first position after the found string is returned. Defaults to 'start'.
     *      - 'match' (array) Aux output. In case of regex search pass here a variable
     *          by reference ('match' => &$match) to get the regex match array populated
     *          by method preg_match(). If nothing found then NULL.
     * 
     * @return int|bool Returns the position of first found string occurence relative 
     *      to beginning of file (regardless to provided offset). Position starts from 0.
     *      Returns FALSE if the string was not found.
     * 
     * @throws Exception on invalid $file specification
     */
    static public function searchString($file, $string, $options = array()) {
        $defaults = array(
            'offset' => 0,
            'sliceSize' => 1024,
            'return' => 'start', // 'after'
            'match' => null,
        );
        $options = array_merge($defaults, $options);
        $options['offset'] = (int)$options['offset'];
        $options['return'] = strtolower($options['return']);
        $options['sliceSize'] = (int)$options['sliceSize'];
        if (is_string($file)) {
            $fileHandle = fopen(ROOT . DS . self::normalizePath($file), 'rb');
        }
        elseif (is_resource($file)) {
            if (($resourceType = get_resource_type($file)) !== 'stream') {
                throw new Exception(__e(__FILE__, 'Invalid file resource "%s" provided. Provide file resource.', $resourceType));
            }
            $fileHandle = $file;
        }
        else {
            throw new Exception(__e(__FILE__, 'Invalid file "%s" provided', $file));
        }
        if (empty($fileHandle)) {
            throw new Exception(__e(__FILE__, 'Invalid file handle'));
        }
        $regexSearch = Validate::regex($string);
        $sliceSize = $options['sliceSize'];
        if (
            !$regexSearch 
            && $sliceSize < ($stringLength = strlen($string))
        ) {
            $sliceSize = $stringLength;
        }
        $match = null;
        $slice = '';
        $found = false;
        $i = 0;
        fseek($fileHandle, $options['offset']);
        while (!feof($fileHandle)) {
            if (++$i > 2) {
                $slice = substr($slice, $sliceSize);
            }
            $slice .= fread($fileHandle, $sliceSize);
            if ($regexSearch) {
                if (preg_match($string, $slice, $match)) {
                    $string = $match[0];
                    $position = strpos($slice, $string);
                    $options['match'] = $match;
                    $found = true;
                    break;
                }
            }
            elseif (($position = strpos($slice, $string)) !== false) {
                $found = true;
                break;
            }
        }
        if (empty($found)) {
            $return = false;
        }
        elseif ($options['return'] === 'after') {
            $return = ftell($fileHandle) - strlen($slice) + $position + strlen($string);
        }
        else {
            $return = ftell($fileHandle) - strlen($slice) + $position;
        }
        if (is_string($file)) {
            fclose($fileHandle);
        }
        return $return;
    }
}
class Exception_File_NonTransformable extends Exception {}

