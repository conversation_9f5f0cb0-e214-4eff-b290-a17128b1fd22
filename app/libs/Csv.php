<?php
/**
 * Csv utility class
 * 
 * This class can be used in two ways:
 * 
 * 1] Using static interface Csv::export(), Csv::import(), Csv::output(), Csv::get()
 *      This approach is supposed to import/export small datasets at once.
 *      Means that the imported or exported data are in such a measure that they
 *      will not break php memory limits
 * 
 * 2] Creating instance of class and work with csv data row by row. 
 *      This approach is supposed for large datasets which cannot be read all into 
 *      memory at once, but instead they should/must be processes row by row.
 */
class Csv {
    
    //
    // CONFIG PROPERTIES set by options of construct method
    //
    
    /**
     * Mode of csv file access/open.
     * Possible values are r, r+, w, w+, a, a+, x, x+, c, c+.
     * See http://php.net/manual/en/function.fopen.php for details.
     * 
     * @var string 
     */
    protected $mode = 'r';
    
    /**
     * Csv file
     * 
     * @var string
     */
    protected $file = null;
    
    /**
     * Handle to csv file
     *
     * @var resource 
     */
    protected $fileHandle = null;
    
    /**
     * Csv file row items delimiter
     * Defaults to comma (,).
     * 
     * @var string 
     */
    protected $delimiter = ',';
    
    /**
     * Csv file row items enclosure
     * Defaults to quotes (").
     * 
     * @var string 
     */
    protected $enclosure = '"';
    
    /**
     * Has the actual csv file line with headers?
     *
     * @var bool 
     */
    protected $hasHeaders = false;
    
    /**
     * Actual csv row offset in csv file. Each file csv row is counted into offset
     * Even empty lines and info line which are even not really a csv rows.
     * Starts from 0.
     *
     * @var int 
     */
    protected $rowOffset = null;
    
    /**
     * Actual record offset in csv file. Only valid record csv rows are taken into 
     * account. Info line, header row and empty lines (not empty record rows!) are skipped.
     * Starts from 0. If -2 or -1 then the actual position in file is on info or header row.
     * 
     * @var int 
     */
    protected $recordOffset = null;
    
    /**
     * Csv file encoding, e.g. 'UTF-8', 'CP1250', ...
     * Defaults to NULL.
     * 
     * @var string 
     */
    protected $encoding = null;
    
    /**
     * Encoding used by process which is working with
     * the csv file. In case of csv file reading the output values are converted 
     * to this encoding. In case of csv file writing the input values are converted 
     * from this encoding. This is done only if both Csv::$encoding and 
     * Csv::$processingEncoding are speciefied. If not then values are 
     * left as they are. Defaults to 'UTF-8'.
     * 
     * @var string 
     */
    protected $processingEncoding = 'UTF-8';
    
    /**
     * Decimal point used in csv float numbers. Defaults to '.'.
     * 
     * @var string 
     */
    protected $decimalPoint = '.';
    
    /**
     * Thousand separator used in csv float numbers. Defaults to ','.
     * 
     * @var string 
     */
    protected $thousandsSeparator = ',';
    
    /**
     * Fields of output records. This applyes only for method Csv::readRecord(). 
     * For more info see phpDoc of Csv::__construct().  
     *
     * @var array 
     */
    protected $recordFields = null;
    
    /**
     * If TRUE then record fields of columns (defined by header name) which does 
     * not exist are ignored. This option is considered only if 'hasHeaders' is TRUE.
     * 
     * @var bool
     */
    protected $ignoreUnexistingColumnsRecordFields = false;
    
    /**
     * Anonymous function to preprocess each csv record column value.
     * For more info see phpDoc of Csv::__construct().
     * 
     * @var function
     */
    protected $preprocessRecordFieldValue = null;
    
    
    //
    // INTERNAL PROPERTIES
    // 

    /**
     * Is the provided file writable according to actual provided mode?
     * TRUE for modes r+, w, w+, a, a+, x, x+, c, c+. FALSE for mode r.
     * If NULL then the mode of csv file was not checked yet.
     * 
     * @var bool 
     */
    protected $isWritable = null;
    
    /**
     * Is the provided file readable according to actual provided mode?
     * TRUE for r, r+, w+, a+, x+, c+. FALSE for mode w, a, x, c.
     * If NULL then the mode of csv file was not checked yet.
     * 
     * @var bool 
     */
    protected $isReadable = null;
    
    /**
     * Count of csv rows in file. Each file csv row is counted here.
     * Even empty lines and info line which are even not really a csv rows.
     * 
     * @var int 
     */
    protected $rowsCount = null;
    
    /**
     * Count of csv records in file. Info line, header row and empty lines 
     * (not empty record rows!) are not counted here.
     * 
     * @var int 
     */
    protected $recordsCount = null;
    
    /**
     * List of headers in actual csv file stored in array like:
     * 
     *      array(
     *          '{convertedColumnHeader}' => {columnIndex},
     *          '{convertedColumnHeader}' => {columnIndex},
     *          ...
     *      )
     * 
     * If both Csv::$encoding and Csv::$processingEncoding are set then the headers are
     * converted to output encoding. They are set only in case the $options['hasHeaders'] is TRUE.
     *
     * @var array 
     */
    protected $headers = null;
    
    /**
     * Has the actual csv file a line with defined info (e.g. _csv_info_=normalized)
     * Even empty info line (_csv_info_=) is taken into account.
     * 
     * @var bool
     */
    protected $hasInfo = false;
        
    /**
     * Info of csv file. Must be defined on very first line of file like:
     * 
     *      _csv_info_={info01}|{info02}:{value02}
     * 
     * These infos are loaded into this property in array like:
     * 
     *      array(
     *          '{info01}' => true,
     *          '{info02}' => '{value02}',
     *      )
     * 
     * @var array
     */
    protected $info = null;
                   
    /**
     * Creates new Csv instance
     * 
     * @param string $file App root relative path to csv file
     * @param array $options Following options are available:
     *      - 'mode' (string) Mode of csv file access/open. Possible values are 
     *          r, r+, w, w+, a, a+, x, x+, c, c+. See http://php.net/manual/en/function.fopen.php for details.
     *          Defaults to 'r'.
     *      - 'delimiter' (string) Csv delimiter, e.g. ',' or ';'. Use chr(9) for TAB. 
     *          Defaults to ','.
     *      - 'enclosure' (string) Csv enclosure. Defaults to '"'.
     *      - 'hasHeaders' (bool) If TRUE the csv file is considered to have 
     *          headers and these headers are read on creation of new instance.
     *          Then they can be used to match columns in 'recordFields' definition
     *          (see below). Defaults to FALSE.
     *      - 'rowOffset' (int) Row offset to initialize the csv file to. Defaults to 0.
     *      - 'recordOffset' (int) Record offset to initialize the csv file to. 
     *          If not defined or NULL then rowOffset is taken into account.
     *          If defined then rowOffset is igniored. Defaults to NULL.
     *      - 'encoding' (string) Csv file encoding, e.g. 'UTF-8', 'CP1250'. Defaults to NULL.
     *      - 'processingEncoding' (string) Encoding used by process which is working with
     *          the csv file. In case of csv file reading the output values are converted 
     *          to this encoding. In case of csv file writing the input values are converted 
     *          from this encoding. This is done only if both Csv::$encoding and 
     *          Csv::$processingEncoding are speciefied. If not then values are 
     *          left as they are. Defaults to 'UTF-8'.
     *      - 'decimalPoint' (string) Decimal point used in csv float numbers. Defaults to '.'.
     *      - 'thousandsSeparator' (string) Thousand separator used in csv float numbers. Defaults to ','.
     *      - 'recordFields' (array) Fields of output records. This applyes only for method Csv::readRecord(). 
     *          To define recordFields provide associative array containing pairs:
     *              - {fieldName} => {columnIndexOrColumnHeaderName}
     *              - or {fieldName} => {function} where csv record, existing part of 
     *                  output record and actual class instance are used as anonymous function inputs.
     *                  For use of actual class instance see 'recordFields' > 'convert'.
     *              - or {fieldName} => array('column' => {columnIndexOrColumnHeaderName}, {conversionOperations}).
     *                  The 'column' is mandatory if 'hasHeaders' is FALSE. Otherwise if there is
     *                  no 'column' defined then it is internally set to {fieldName} and
     *                  it means that header name is expected to be the same as field name.
     *                  Available conversion operations are:
     *                      - 'convertEncoding' (bool) TRUE/FALSE
     *                      - 'convertType' (string) Possible values are: 'bool', 'boolean', 'int', 'integer', 'float'
     *                      - 'trim' (bool|string) TRUE/FALSE or string to trim, e.g. '_'. If the
     *                          value is at first exploded then 'trim' applies on each item separatelly. 
     *                      - 'explode' (string) Separator string. Str::explode() is used.
     *                      - 'convert' (string|array|function) If string the considered to be
     *                          input for Str::fill() where csv record is used for inserts.
     *                          If array the considered to be conversion array of pair {oldValue} => {newValue}.
     *                          If provided as anonymous function then csv value of specified column,
     *                          csv record, existing part of output record and actual class instance
     *                          are used as anonymous function inputs.
     *                          Input with actual class instance is usefull in the case that
     *                          MyCsv options are defined under OtherClass and returned by some OtherClass::method().
     *                          If the conversion function uses $this then it is referencing to 
     *                          OtherClass instance but in the most of cases it should reference to 
     *                          MyCsv instance. That is why the MyCsv instance is passed as last input.
     *                          Use ${ClassName} (e.g. $MyCsv) as the name of variable for this input 
     *                          as $this variable cannot be used as function arg.
     *                  Operations are proccessed in order they are defined. Defaults to NULL, 
     *                  which means that record is returned as read from .csv file. E.g.:
     * 
     *                      array(
     *                          // the field 'id' will be imported from column 0
     *                          'id' => 0 
     * 
     *                          // the field 'name' will be imported from column 1
     *                          // and the encoding will be converted from file encoding to processingEncoding
     *                          'name' => array('column' => 1, 'convertEncoding' => true, 'trim' => true) 
     * 
     *                          // the field 'price' will be imported from column 2
     *                          // and typecasted to float (before typecasting decimalPoint and 
     *                          // thousandsSeparator are normalized to php comprehensive form)
     *                          'price' => array('column' => 2, 'convertType' => 'float')
     * 
     *                          // the field 'authors' will be imported from column with name 'AUTHORS:' in headers.
     *                          // This can be used only in case that csv file has headers.
     *                          // The encoding will be converted from encoding to processingEncoding
     *                          // and value is exploded by ';' 
     *                          'authors' => array('column' => 'AUTHORS:', 'convertEncoding' => true, 'explode' => ';') ,
     *      
     *                          // in general
     *                          {csvColumnHeaderOrIndex} => {outputFieldNameOrConfigArray},
     *                      )
     * 
     *      - 'ignoreUnexistingColumnsRecordFields' (bool). If TRUE then record fields
     *          of columns (defined by header name) which does not exist are ignored. 
     *          This option is considered only if 'hasHeaders' is TRUE. Default to FALSE.
     *      - 'preprocessRecordFieldValue' (function) Anonymous function to preprocess each
     *          csv record column value. If provided then it is applied before any other 
     *          column conversions defined in 'recordFields' except the case "{fieldName} => {function}"
     *          in which it does not applies. It must return preprocessed / normalized 
     *          column value. The anonymous function args are column value, record field name, 
     *          csv record, existing part of output record and actual class instance.
     *          For use of actual class instance see 'recordFields' > 'convert'.
     *          Use this options to make some generic all-columns-values conversion / preprocessing.
     *          Defaults to NULL.
     * 
     * NOTE: If both 'encoding' and 'processingEncoding' options are defined then 
     *      all csv rows values (including headers) are converted to output encoding.
     *      If recordFields are specified then only values of fields with 'convertEncoding' => TRUE
     *      are converted.
     */
    public function __construct($file, $options = array()) {
        $this->file = ROOT . DS . ltrim($file, DS);
        $this->open($options);
    }
    
    /**
     * Opens csv file
     * 
     * @param type $options
     * 
     * @throws Exception on
     *      - not empty file handle
     *      - file is neither readable nor writable
     *      - fopen fails
     *      - fopen fails
     *      - it is indicated that file has headers but there is not enought lines even for having headers
     */
    protected function open($options = array()) {
        $defaults = array(
            'mode' => $this->mode,
            'delimiter' => $this->delimiter,
            'enclosure' => $this->enclosure,         
            'hasHeaders' => $this->hasHeaders,
            'rowOffset' => $this->rowOffset,
            'recordOffset' => $this->recordOffset,
            'encoding' => $this->encoding,
            'processingEncoding' => $this->processingEncoding,
            'decimalPoint' => $this->decimalPoint,         
            'thousandsSeparator' => $this->thousandsSeparator,         
            'recordFields' => $this->recordFields,
            'ignoreUnexistingColumnsRecordFields' => $this->ignoreUnexistingColumnsRecordFields,
            'preprocessRecordFieldValue' => $this->preprocessRecordFieldValue,
        );
        $options = array_merge($defaults, $options);
        
        if ($this->fileHandle) {
            throw new Exception("The csv file {$this->file} is already opened.");
        }
        
        // set simple properties
        $this->delimiter = $options['delimiter'];
        $this->enclosure = $options['enclosure'];
        $this->encoding = $options['encoding'];
        $this->processingEncoding = $options['processingEncoding'];
        $this->decimalPoint = $options['decimalPoint'];
        $this->thousandsSeparator = $options['thousandsSeparator'];
                
        // validate mode
        $options['mode'] = strtolower($options['mode']);
        $allowedModes = array('r', 'r+', 'w', 'w+', 'a', 'a+', 'x', 'x+', 'c', 'c+');
        $writableModes = array('r+', 'w', 'w+', 'a', 'a+', 'x', 'x+', 'c', 'c+');
        $readableModes = array('r', 'r+', 'w+', 'a+', 'x+', 'c+');
        if (!in_array($options['mode'],  $allowedModes)) {
            throw new Exception("Invalid file open mode {$options['mode']}");
        }
        $this->mode = $options['mode'];
        
        // set isWritable and isReadable
        $this->isReadable = in_array($this->mode, $readableModes);
        $this->isWritable = in_array($this->mode, $writableModes);
        
        // validate file
        if (
            $this->isReadable
            && (!is_file($this->file) || !is_readable($this->file))
        ) {
            throw new Exception("File {$this->file} does not exist or is not readable");
        }
        if (
            $this->isWritable
            && (!is_file($this->file) || !is_writable($this->file))
        ) {
            throw new Exception("File {$this->file} does not exist or is not writable");
        }
        
        // open the file
        if (!($this->fileHandle = fopen($this->file, $this->mode))) {
            throw new Exception("Function fopen() has failed to open the file {$this->file} in mode {$this->mode}");
        }
        
        // load file info (must be done before the file is opened)
        $this->loadInfo();
        
        // load headers if indicated that file has them
        if ($options['hasHeaders']) {
            $this->loadHeaders();
        }
        
        // set record fields
        $this->ignoreUnexistingColumnsRecordFields = $options['ignoreUnexistingColumnsRecordFields'];
        if ($options['recordFields']) {
            $this->setRecordFields($options['recordFields']);
        } 
        
        if (
            $options['preprocessRecordFieldValue']
            && !Validate::callableFunction($options['preprocessRecordFieldValue'])
        ) {
            throw new Exception("Option 'preprocessRecordFieldValue' must be an annonymous function or callable");
        }
        $this->preprocessRecordFieldValue = $options['preprocessRecordFieldValue'];
        
        // reset the file position
        $this->reset();
        
        // set the specified rowOffset 
        if ($options['recordOffset'] !== null) {
            $this->setRecordOffset($options['recordOffset']);
        }
        elseif ($options['rowOffset']) {
            $this->setRowOffset($options['rowOffset']);
        }
    }
        
    /**
     * Loads the info line (if any) of the csv file
     * 
     * @throws Exception
     */
    protected function loadInfo() {
        if (!$this->fileHandle) {
            throw new Exception("Csv file {$this->file} has not been opened yet");
        }
        if (!$this->isReadable) {
            throw new Exception("Csv file {$this->file} is not opened for reading");
        }
        
        // keep actual file position
        $actualPosition = ftell($this->fileHandle);
        // load info
        rewind($this->fileHandle);
        $line = trim(fgets($this->fileHandle));
        $line = trim($line, $this->enclosure);
        $lineParts = explode('=', $line);
        if (array_shift($lineParts) === '_csv_info_') {
            $this->hasInfo = true;
            $this->info = array();
            if ($lineParts) {
                $infoParts = explode('|', array_shift($lineParts));
                foreach($infoParts as $infoPart) {
                    $infoPart = explode(':', $infoPart);
                    if (count($infoPart) > 1) {
                        $this->info[$infoPart[0]] = $infoPart[1];
                    }
                    else {
                        $this->info[$infoPart[0]] = true;
                    }
                }
            }
        }
        else {
            $this->hasInfo = false;
            $this->info = array();
        }
        // go back to actual file position
        fseek($this->fileHandle, $actualPosition, SEEK_SET);
    }
    
    /**
     * Loads csv file headers
     * Should be launched only after the Csv::loadInfo() has been launched, because 
     * this method checks if the file has info line
     * 
     * @throws Exception
     */
    protected function loadHeaders() {
        if (!$this->fileHandle) {
            throw new Exception("Csv file {$this->file} has not been opened yet");
        }
        if (!$this->isReadable) {
            throw new Exception("Csv file {$this->file} is not opened for reading");
        }
        
        // keep actual file position
        $actualPosition = ftell($this->fileHandle);
        // load headers
        rewind($this->fileHandle);
        if ($this->hasInfo) {
            if (
                ($row = fgetcsv($this->fileHandle, 0, $this->delimiter, $this->enclosure)) === false
            ) {
                throw new Exception ("Csv file {$this->file} has no headers");
            }
        }
        if (
            ($row = fgetcsv($this->fileHandle, 0, $this->delimiter, $this->enclosure)) === false
        ) {
            throw new Exception ("Csv file {$this->file} has no headers");
        }
        
        if ($this->encoding && $this->processingEncoding) {
            foreach ($row as &$value) {
                $value = (string)iconv($this->encoding, $this->processingEncoding, $value);
            }
            unset($value);
        }
        
        $this->headers = array_flip($row);
        $this->hasHeaders = true;
        
        // go back to actual file position
        fseek($this->fileHandle, $actualPosition, SEEK_SET);
    }
    
    /**
     * Sets and normalizes recordFields
     * 
     * @param array $fields Array of record fields. See Csv::$outputFieds property
     *      for description of array structure.
     * 
     * @throws Exception
     */
    protected function setRecordFields($fields) {
        $fields = (array)$fields;
        $recordFields = array();
        foreach ($fields as $targetField => $source) {
            // if source is provided as closure then let it be as it is
            if (Validate::callableFunction($source)) {
                $recordFields[$targetField] = $source;
                continue;
            }
            // normalize column given by header
            if (!is_array($source)) {
                $source = array('column' => $source);
            }
            elseif (!isset($source['column'])) {
                if (!$this->hasHeaders) {
                    throw new Exception(sprintf("Mising 'column' definition for field %s", $targetField));
                }
                $source['column'] = $targetField;
            }
            if (is_string($source['column'])) {
                if (!$this->hasHeaders) {
                    throw new Exception("Output fields cannot be defined by headers");
                }
                // replace the column header by column index
                if (isset($this->headers[$source['column']])) {                    
                    $source['column'] = $this->headers[$source['column']];
                }
                // ignore records fields of unexisting columns (defined by header name) if allowed
                elseif($this->ignoreUnexistingColumnsRecordFields) {
                    continue;
                }
                else {
                    throw new Exception(sprintf("Unexisting header '%s' used in specification of record fields", $source['column']));
                }
            }
            // normalize source operations
            if (empty($source['convertType'])) {
                unset($source['convertType']);
            }
            else {
                $source['convertType'] = strtolower($source['convertType']);
                if (
                    !in_array(
                        $source['convertType'], 
                        array('bool', 'boolean', 'integer', 'int', 'float')
                    )
                ) {
                    throw new Exception(sprintf("Invalid type '%s' for field value type conversion. Use one of 'bool', 'boolean', 'int', 'integer', 'float'", $source['convertType']));
                }
            }
            if (!isset($source['convertEncoding'])) {
                unset($source['convertEncoding']);
            }
            elseif (
                $source['convertEncoding']
                && (
                    empty($this->encoding)
                    || empty($this->processingEncoding)
                )
            ) {
                throw new Exception("Encoding of record fields cannot be converted as there is undefined either csv file encoding or output encoding");
            }
            if (
                !isset($source['explode'])
            ) {
                unset($source['explode']);
            }
            if (empty($source['convert'])) {
                unset($source['convert']);
            }
            if (!isset($source['trim'])) {
                unset($source['trim']);
            }
            $recordFields[$targetField] = $source;
        }
        $this->recordFields = $recordFields;
    }
    
    /**
     * Gets output record containing only record fields extracted from provided record
     * 
     * @param array $record
     * 
     * @return array Output record containing only record fields. If record fields are
     *      empty then empty array is returned.
     */
    protected function getOutputRecord($record) {
        $outputRecord = array();
        foreach ($this->recordFields as $targetField => $source) {
            if (Validate::callableFunction($source)) {
                $outputRecord[$targetField] = $source($record, $outputRecord, $this);
                continue;
            }
            $column = $source['column'];
            if (!isset($record[$column])) {
                $outputRecord[$targetField] = null;
                continue;
            }
            $fieldValue = $record[$column];
            if (Validate::callableFunction($this->preprocessRecordFieldValue)) {
                $fieldValue = ($this->preprocessRecordFieldValue)($fieldValue, $targetField, $record, $outputRecord, $this);
            }
            // apply operations in order they occure in source definition
            foreach ($source as $operation => $arg) {
                if ($operation === 'column') {
                    continue;
                }
                elseif (
                    $operation === 'convertEncoding'
                    && !empty($arg)
                ) {
                    $fieldValue = $this->convertEncoding($fieldValue);
                }
                elseif (
                    $operation === 'convertType'
                    && !empty($arg)
                ) {
                    $fieldValue = $this->convertType($fieldValue, $arg);
                }
                elseif (
                    $operation === 'explode' 
                    && $arg !== false
                ) {
                    $fieldValue = Str::explode($arg, $fieldValue);
                }
                elseif (
                    $operation === 'trim' 
                    && $arg !== false
                ) {
                    if ($arg === true) {
                        if (is_array($fieldValue)) {
                            $fieldValue = array_map('trim', $fieldValue);
                        }
                        else {
                            $fieldValue = trim($fieldValue);
                        }
                    }
                    else {
                        if (is_array($fieldValue)) {
                            foreach ($fieldValue as &$v) {
                                $v = trim($v, $arg);
                            }
                            unset($v);
                        }
                        else {
                            $fieldValue = trim($fieldValue, $arg);
                        }
                    }
                }
                elseif (
                    $operation === 'convert' 
                    && $arg !== false
                ) {
                    if (Validate::callableFunction($arg)) {
                        $fieldValue = $arg($fieldValue, $record, $outputRecord, $this);
                    }
                    elseif (Validate::callableFunction($arg)) {
                        $fieldValue = call_user_func_array($arg, array($fieldValue, $record, $outputRecord, $this));
                    }
                    elseif (is_array($arg)) {
                        if (isset($arg[$fieldValue])) {
                            $fieldValue = $arg[$fieldValue];
                        }
                    }
                    else {
                        $fieldValue = Str::fill($arg, $record, $fieldValue);
                    }
                }
            }
            $outputRecord[$targetField] = $fieldValue;
        }
        return $outputRecord;
    }
    
    /**
     * Converts value encoding from (input csv) encoding to output encoding
     * 
     * @param string $value
     * @return string
     * 
     * @throws Exception on undefined encoding (csv) or undefined output encoding
     */
    public function convertEncoding($value) {
        if (
            empty($this->encoding)
            || empty($this->processingEncoding)
        ) {
            throw new Exception("Encoding cannot be converted as there is undefined either csv file encoding or output encoding");
        }
        return (string)iconv($this->encoding, $this->processingEncoding, $value);
    }
    
    /**
     * Converts provided $value to specified type
     * 
     * @param mixed $value
     * @param string $type Use one of 'bool', 'boolean', 'int', 'integer', 'float'
     * 
     * @return mixed Typecasted value
     * 
     * @throws Exception on invalid type specification
     */
    public function convertType($value, $type) {
        switch ($type) {
            case 'bool':
            case 'boolean':
                $value = (bool)$value;
                break;
            case 'int':
            case 'integer':
                $value = (int)$value;
                break;
            case 'float':
                $value = (float)$this->normalizeNumber($value);
                break;
            default:
                throw new Exception(sprintf("Invalid type '%s' for value conversion. Use one of 'bool', 'boolean', 'int', 'integer', 'float'", $type));
        }
        return $value;
    }
    
    /**
     * Normalizes provided number string to php valid form, e.g. '1,000.23' to '1000.23'
     * or '45,78' to '45.78'. Normalization is made according to values of 
     * Csv::$thousandsSeparator and ::$decimalPoint prorepties.
     * 
     * @param string $number Number string to be normalized.
     * 
     * @return string Normalized number string
     */
    public function normalizeNumber($number) {
        $number = trim($number);
        if ($this->thousandsSeparator !== '') {
            $number = str_replace($this->thousandsSeparator, '', $number);
        }
        if ($this->decimalPoint !=='.') {
            $number = str_replace($this->decimalPoint, '.', $number);
        }
        // allways perform this fallback
        $number = str_replace(' ', '', $number);
        $number = str_replace(',', '.', $number);
        return $number;
    }
    
    /**
     * Gets Csw::$headers
     * 
     * @return array
     */
    public function getPropertyHeaders() {
        return array_keys($this->headers);
    }
    
    /**
     * Gets Csw::$info
     * 
     * @return array
     */
    public function getPropertyInfo() {
        return $this->info;
    }
    
    /**
     * Is/has been the csv file normalized by method Csv::normalize().
     * 
     * @return true
     */
    public function isNormalized() {
        return !empty($this->info['normalized']);
    }
        
    public function getPropertyRowOffset() {
        return $this->rowOffset;
    }
    
    public function setRowOffset($offset) {
        if (!$this->fileHandle) {
            throw new Exception("Csv file {$this->file} has not been opened yet");
        }
        if (!$this->isReadable) {
            throw new Exception("It is not possible to set row offset as csv file {$this->file} is not opened for reading");
        }
        
        if ($this->rowOffset == $offset) {
            return true;
        }
        if ($this->rowOffset > $offset) {
            $this->reset();
        }
        $offsetDiff = $offset - $this->rowOffset;
        // go to required row offset
        $row = null;
        while (
            $offsetDiff > 0 
            && (($row = fgetcsv($this->fileHandle, 0, $this->delimiter, $this->enclosure)) !== false)
        ) {
            $offsetDiff--;
            $this->rowOffset++;
            // actualize also record offset (skip empty rows)
            if ($row !== array(null)) {
                $this->recordOffset++;
            }
        }
        if (
            $this->rowOffset != $offset
            && $row === false
        ) {
            return false;
        }
        return true;
    }
    
    public function getRowsCount() {
        if ($this->rowsCount === null) {
            // keep actual file position
            $actualPosition = ftell($this->fileHandle);
            // go till the end of file and count all rows
            $rowsCount = $this->rowOffset;
            while (
                (($row = fgetcsv($this->fileHandle, 0, $this->delimiter, $this->enclosure)) !== false)
            ) {
                $rowsCount++;
            }
            $this->rowsCount = $rowsCount;
            // go back to actual file position
            fseek($this->fileHandle, $actualPosition, SEEK_SET);
        }
        return $this->rowsCount;
    }
    
    /**
     * Reads the next csv row from file. Info line, header row and empty lines including.
     * 
     * @return null|array Next row array. NULL if the end of csv file is reached.
     */
    public function readRow() {
        if (!$this->fileHandle) {
            throw new Exception("Csv file {$this->file} has not been opened yet");
        }
        if (!$this->isReadable) {
            throw new Exception("Csv file {$this->file} is not opened for reading");
        }
        // read the row
        if (
            ($row = fgetcsv($this->fileHandle, 0, $this->delimiter, $this->enclosure)) === false
        ) {
            return null;
        }
        // actualize row offset
        $this->rowOffset++;
        // actualize also record offset (skip empty rows)
        if ($row !== array(null)) {
            $this->recordOffset++;
        }
        
        if ($this->encoding && $this->processingEncoding) {
            foreach ($row as &$value) {
                $value = (string)iconv($this->encoding, $this->processingEncoding, $value);
            }
            unset($value);
        }
        
        return $row;
    }
    
    /**
     * Writes a next row to csv file
     * 
     * @param type $row
     * @param type $options
     * @throws Exception
     */
    public function writeRow($row) {
        
        if ($this->mode != 'w') {
            throw new Exception('...');
        }
        
        if (!$this->fileHandle) {
            throw new Exception('...');
        }
        
        //... Update rows count
        //@todo
    }
    
    /**
     * Gets offset of record which will be read as next
     * 
     * @return int
     */
    public function getPropertyRecordOffset() {
        return $this->recordOffset;
    }
    
    public function setRecordOffset($offset) {
        if (!$this->fileHandle) {
            throw new Exception("Csv file {$this->file} has not been opened yet");
        }
        if (!$this->isReadable) {
            throw new Exception("It is not possible to set record offset as csv file {$this->file} is not opened for reading");
        }
        
        if ($this->recordOffset == $offset) {
            return true;
        }
        if ($this->recordOffset > $offset) {
            $this->reset();
        }
        $offsetDiff = $offset - $this->recordOffset;
        // go to required record offset
        $row = null;
        while (
            $offsetDiff > 0 
            && (($row = fgetcsv($this->fileHandle, 0, $this->delimiter, $this->enclosure)) !== false)
        ) {
            $this->rowOffset++;
            // actualize also record offset (skip empty rows)
            if ($row !== array(null)) {
                $offsetDiff--;
                $this->recordOffset++;
            }
        }
        if (
            $this->recordOffset != $offset
            && $row === false
        ) {
            return false;
        }
        return true;
    }
    
    public function getRecordsCount() {
        if ($this->recordsCount === null) {
            // keep actual file position
            $actualPosition = ftell($this->fileHandle);
            // go till the end of file and count all records
            $recordsCount = $this->recordOffset;
            while (
                (($row = fgetcsv($this->fileHandle, 0, $this->delimiter, $this->enclosure)) !== false)
            ) {
                if ($row !== array(null)) {
                    $recordsCount++;
                }
            }
            $this->recordsCount = $recordsCount;
            // go back to actual file position
            fseek($this->fileHandle, $actualPosition, SEEK_SET);
        }
        return $this->recordsCount;
    }
    
    /**
     * Reads the next csv record from file. Info line, header row and empty lines are omitted.
     * 
     * @return null|array Next record array. NULL if the end of csv file is reached.
     */
    public function readRecord() {
        if (!$this->fileHandle) {
            throw new Exception("Csv file {$this->file} has not been opened yet");
        }
        if (!$this->isReadable) {
            throw new Exception("Csv file {$this->file} is not opened for reading");
        }
        // find the offset of the next record
        $nextRecordOffset = ($this->recordOffset < 0) ? 1 : $this->recordOffset + 1;
        // read the record
        while (
            $this->recordOffset < $nextRecordOffset
            && ($row = fgetcsv($this->fileHandle, 0, $this->delimiter, $this->enclosure)) !== false
        ) {
            // actualize row offset
            $this->rowOffset++;
            // actualize record offset (skip empty rows)
            if ($row !== array(null)) {
                $this->recordOffset++;
            }
        }
        
        if ($row === false) {
            return null;
        }
        
        if ($this->recordFields) {
            $row = $this->getOutputRecord($row);
        }
        elseif ($this->encoding && $this->processingEncoding) {
            foreach ($row as &$value) {
                $value = (string)iconv($this->encoding, $this->processingEncoding, $value);
            }
            unset($value);
        }
        
        return $row;
    }
       
    /**
     * Resets the file pointer
     */
    public function reset() {
        if (!$this->fileHandle) {
            throw new Exception("Csv file {$this->file} has not been opened yet");
        }
        
        rewind($this->fileHandle);
        $this->rowOffset = 0;
        $this->recordOffset = 0;
        if ($this->hasInfo) {
            $this->recordOffset--;
        }
        if ($this->hasHeaders) {
            $this->recordOffset--;
        }
    }
    
    /**
     * Normalizes a csv file, means encloses all unenclosed items, like this:
     * 
     * input file:
     * a,"b",""c""
     * 
     * output file:
     * "a","b","""c"""
     * 
     * It takes round 10 sec to normalize each MB of file size.
     * 
     * NOTE:
     * This method should be used before fgetcsv() for files which are not UTF-8 encoded.
     * Normalization seems to solve the problem caused by fact, that function fgetcsv()
     * takes into account the locale setting of server: Locale setting is taken
     * into account by this function. If App::$lang is e.g. en_US.UTF-8, 
     * files in one-byte encoding are read wrong by this function.
     * 
     * @return bool 
     */
    public function normalize() {
        if ($this->isNormalized()) {
            return true;
        }
        if (empty($this->encoding)) {
            throw new Exception("Provide csv file encoding to allow normalization");
        }
        // create temp file name for normalized 
        $pathInfo = File::getPathinfo($this->file);
        $tmpFile = $pathInfo['dirname'] . DS . uniqid("{$pathInfo['filename']}_tmp") . '.' . $pathInfo['extension'];
        // open the temp normalized file
        $ofh = fopen($tmpFile, 'w');
        if (!$ofh) {
            return false;
        }
        // set the info that file is normalized (on the first line of file)
        fwrite($ofh, "{$this->enclosure}_csv_info_=normalized{$this->enclosure}\n");
        // put the original file poiter to the file begining
        rewind($this->fileHandle);
        // normalize
        $crChar = chr(13);
        $lfChar = chr(10);
        $enclosed = false;
        while (($line = fgets($this->fileHandle)) !== false) {
            $line = iconv($this->encoding, 'UTF-8', $line);
            $length = mb_strlen($line, 'UTF-8');
            $normalLine = '';
            $prevPrevChar = null;
            $prevChar = null;
            $char = null;
            $nextChar = null;
            $nextNextChar = null;
            for ($i = 0; $i < $length; $i++) {
                // set chars
                $prevPrevChar = $prevChar;
                $prevChar = $char;
                if ($i == 0) {
                    $char = mb_substr($line, $i, 1, 'UTF-8'); // time consuming, iconv_substr() is even slower
                }
                else {
                    $char = $nextChar;
                }
                if ($i == 0) {
                    $nextChar = mb_substr($line, $i + 1, 1, 'UTF-8');
                }
                else {
                    $nextChar = $nextNextChar;
                }
                if ($i < $length - 2) {
                    $nextNextChar = mb_substr($line, $i + 2, 1, 'UTF-8');
                }
                else {
                    $nextNextChar = null;
                }
                
                // check for start of enclosure
                $enclosureJustStarted = false;
                if (!$enclosed) {
                    if (
                        $char == $this->enclosure // if an enclosure
                        && ($nextChar != $this->enclosure || $nextNextChar == $this->enclosure) // but not an eacape for next enclosure char (MS style)
                        && ($prevChar == $this->delimiter || $prevChar === null) // and after delimiter or at the begining of line
                    ) {
                        $enclosed = true;
                        $enclosureJustStarted = true;
                    }
                }

                // enclose at the beginning of line or after delimiter
                if (
                    !$enclosed
                    && $char != $this->delimiter && $char != $crChar && $char != $lfChar
                    && ($prevChar === null || $prevChar == $this->delimiter)
                ) {
                    $normalLine .= $this->enclosure;
                    //$normalLine .= '{L}'; //debug
                }
                
                $normalLine .= $char;
                
                // close enclosure at the end of line or before delimiter
                if (
                    !$enclosed
                    && $char != $this->delimiter && $char != $crChar && $char != $lfChar
                    && ($nextChar === $this->delimiter || $nextChar == $crChar || $nextChar == $lfChar)
                ) {
                    $normalLine .= $this->enclosure;
                    //$normalLine .= '{R}'; //debug
                }
                
                // check for end of enclosure (but only if the enclosure has not been opened in this turn)
                if ($enclosed && !$enclosureJustStarted) {
                    if (
                        $char == $this->enclosure // if an enclosure
                        && ($prevChar != $this->enclosure || $prevPrevChar == $this->enclosure) && $prevChar != '\\' // but not escaped (MS style and official style)
                        && ($nextChar == $this->delimiter || $nextChar == $crChar || $nextChar == $lfChar) // and directly before delimiter or at the end of line
                    ) {
                        $enclosed = false;
                    }
                }
            }
            // write the normalized line to temp file 
            $normalLine = iconv('UTF-8', $this->encoding, $normalLine);
            if (fwrite($ofh, $normalLine) === false) {
                fclose($ofh);
                unlink($tmpFile);
                return false;
            }
        }
        fclose($ofh);
        // close the current file
        $file = $this->file;
        $rowOffset = $this->rowOffset;
        $recordOffset = $this->recordOffset;
        $this->close();
        // rename temp file
        rename($tmpFile, $this->file);
        // open the normalized file and put it to the same state as the original 
        // file has been
        $this->file = $file;
        $this->open(array(
            'mode' => $this->mode,
            'delimiter' => $this->delimiter,
            'enclosure' => $this->enclosure,         
            'hasHeaders' => $this->hasHeaders,
            'rowOffset' => $rowOffset + 1,
            'recordOffset' => $recordOffset,
            'encoding' => $this->encoding,
            'processingEncoding' => $this->processingEncoding,
            'decimalPoint' => $this->decimalPoint,         
            'thousandsSeparator' => $this->thousandsSeparator,         
            'recordFields' => $this->recordFields,
        ));
        return true;
    }    
    
    /**
     * Allows to close the csv file explicitly.
     * 
     * File is implicitly closed by class destructor.
     * 
     * @return bool
     */
    public function close() {
        if (!$this->fileHandle) {
             return;
        }
        $result  = fclose($this->fileHandle);
        $this->fileHandle = null;
        $this->rowOffset = null;
        $this->recordOffset = null;
        return $result;
    }
    
    public function __destruct() {
        $this->close();
    }
    
    
    //
    // STATIC INTERFACE
    //
    
    /**
     * Echoes an array to output buffer as a csv string
     * 
     * @param array $data
     * @param string $delimiter
     * @param string $enclosure
     */
    static public function output($data, $delimiter = ',', $enclosure = '"') {
        // fix formating, $data must be an array of arrays (rows)
        // If it's just a single row then fix it.
        if (!is_array(array_shift(array_values($data)))) {
            $data = array($data);
        }
        $buffersData = App::closeOpenedOutputBuffers();
        $outstream = fopen('php://output', 'w'); 
        foreach($data as $row) {
            fputcsv($outstream, $row, $delimiter, $enclosure);
        }
        fclose($outstream);
        App::reopenClosedOutputBuffers($buffersData);
    }
    
    /**
     * Transforms an array to csv string
     * 
     * @param array $data
     * @param string $delimiter
     * @param string $enclosure
     */
    static public function get($data, $delimiter = ',', $enclosure = '"') {
        ob_start();
        self::output($data, $delimiter, $enclosure);
        $csv = ob_get_contents();
        ob_end_clean();
        return $csv;
    }
    
	/**
	 * Exports a data array into csv file. Names of columns are taken from keys
     * of first item in $data array.
	 *
	 * @param string $file Path to file relative to ROOT, e.g. /userfiles/cvs/my_file.csv
	 * @param string $data Array like e.g.:
     * 
     *      array(
     *          array('name' => '...', 'surname' => '...', ...),
     *          array('name' => '...', 'surname' => '...', ...),
     *          ...
     *      )
     * 
     * @param array $options 
     * 
	 * @return bool FALSE if creation of csv file has failed. TRUE on success.
	 */
	public static function export($file, $data, $options = array()) {
        $defaults = array(
            'headers' => true,
            'delimiter' => ',',
            'enclosure' => '"',
        );
		$options = array_merge($defaults, $options);
		
		// open the file
        $file = DS . ltrim($file, DS);
		if (!($fh = @fopen(ROOT . $file, 'w'))) {
            return false;
        }
            
        // Iterate through and format data
        $firstRow = true;
        $headers = array();
        $rows = array();
        foreach ($data as $rawRow) {
            if (!is_array($rawRow)) {
                continue;
            }
            $row = array();
            foreach ($rawRow as $field => $value) {
                if ($firstRow) {
                    $headers[] = $field;
                }
                $row[] = (string)$value;
            }
            $rows[] = $row;
            $firstRow = false;
        }

        if ($options['headers']) {
            fputcsv($fh, $headers, $options['delimiter'], $options['enclosure']);
        }

        foreach ($rows as $row) {
            fputcsv($fh, $row, $options['delimiter'], $options['enclosure']);
        }

        // close the file
        fclose($fh);

        return true;
	}
    
    public static function import($file, $options) {
        $defaults = array(
            'headers' => true,
            'delimiter' => ',',
            'enclosure' => '"',
            'offset' => 0, // number of record to be skipped (header is not included)
            'limit' => null,
            'fields' => null,
        );
		$options = array_merge($defaults, $options);
        //...
    }
    
    /**
     * Returns CSV record string for provided record array. Generated CSV record 
     * strings must be concatenated by new line character (PHP_EOL, "\n"). 
     * 
     * @param array $record
     * @param string $delimiter Defaults to ','.
     * @param string $enclosure Defaults to '"'.
     * 
     * @return string
     */
    public static function getRecordString($record, $delimiter = ',', $enclosure = '"') {
        if ($enclosure === '"') {
            $escape = $enclosure;
        }
        else {
            $escape = '\\';
        }
        foreach($record as &$field) {
            $field = str_replace($enclosure, $escape . $enclosure, $field);
        }
        unset($field);
        return $enclosure . implode($enclosure . $delimiter . $enclosure, $record) . $enclosure;
    }    

}

