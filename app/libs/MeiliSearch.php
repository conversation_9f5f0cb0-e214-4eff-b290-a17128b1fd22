<?php

/**
 * Simplifying facade class for more general MeiliSearch vendor class
 * 
 * ATTENTION: Read phpDoc of EshopProductMeiliSearch class.
 */
class MeiliSearch {
        
    /**
     * @var MeiliSearch\Client 
     */
    protected $Client;
    
    /**
     * @var MeiliSearch\Endpoints\Indexes
     */
    protected $Index;
    
    /**
     * List of pairs {apiUrlBase} => {version}
     * 
     * @var array
     */
    static protected $versions = array();
    
    /**
     * @var string 
     */
    public $version;
        
    /**
     * @param string $indexName Unique index name for specified 'indexHost' option.
     *          Mostly a model name can be provided here, e.g. 'EshopProduct'.
     * @param array $options Following are avilable:
     *      - 'apiUrlBase' (string) Defauts to 'http://localhost:7700/'.
     *      - 'apiKey' (string) See https://docs.meilisearch.com/learn/security/master_api_keys.html
     *          Defaults to NULL, it means MeiliSearch must be launched in unprotected mode without master key.
     *      - 'indexHost' (string) Name of HTTP host the searh index belongs to.
     *          Defaults (if not provided or empty) to $_SERVER['HTTP_HOST']. 
     *          It can be rewriten for debugging.
     */
    public function __construct($indexName, $options = array()) {
        $options = array_merge(array(
            'apiUrlBase' => null,
            'apiKey' => null,
        ), $options);
        if (!$options['apiUrlBase']) {
            $options['apiUrlBase'] = 'http://localhost:7700/';
        }
        
        $this->Client = new MeiliSearch\Client($options['apiUrlBase'], $options['apiKey']);
        
        $this->Index = $this->Client->index($this->getIndexUid($indexName, $options));
        
        if (empty(self::$versions[$options['apiUrlBase']])) {
            $version = $this->Client->version();
            self::$versions[$options['apiUrlBase']] = $version['pkgVersion'];
        }
        $this->version = self::$versions[$options['apiUrlBase']];
    }
    
    /**
     * Returns an unique id of search index for provided index host and name
     * 
     * @param string $indexName Unique index name for specified 'indexHost' option.
     *          Mostly a model name can be provided here, e.g. 'EshopProduct'.
     * @param array $options Following are available:
     *      - 'indexHost' (string) Name of HTTP host the searh index belongs to.
     *          Defaults (if not provided or empty) to $_SERVER['HTTP_HOST']. 
     *          It can be rewriten for debugging.
     * 
     * @return string
     */
    protected function getIndexUid($indexName, $options = array()) {
        $options = array_merge(array(
            'indexHost' => null,
        ), $options);
        if (!$options['indexHost']) {
            $options['indexHost'] = $_SERVER['HTTP_HOST'];
        }
        // get host name without 'www.' (to use the same index for www.example.com
        // and example.com)
        $host = preg_replace('/^www\./i', '', $options['indexHost']);
        return Str::slugize($host) . '-' . Str::slugize($indexName);
    }
    
    public function getKeys() {
        return $this->Client->getKeys();
    }
    
    public function getStats() {
        return $this->Client->stats();
    }
    
    /**
     * @param array $options Settings options, see following docs:
     *          - https://docs.meilisearch.com/learn/configuration/settings.html
     *          - https://docs.meilisearch.com/reference/api/settings.html
     */
    public function updateSettings($options) {
        if ($this->version < '0.27') {
            if (array_key_exists('typoTolerance', $options)) {
                unset($options['typoTolerance']);
                if (ON_LOCALHOST) {
                    App::log(
                        'meilisearch', 
                        __e(__FILE__, 'WARNING: Option \'typoTolerance\' is not available in version %s', $this->version)
                    );
                }
            }
        }
        $this->Index->updateSettings($options);
    }
    
    public function resetSettings() {
        $this->Index->resetSettings();
    }
    
    /**
     * @param array $documents
     * @param string $primaryKey Name if primary key field, e.g. 'id'.
     */
    public function addOrReplaceDocuments($documents, $primaryKey) {
        $this->Index->addDocuments($documents, $primaryKey);
    }
    
    /**
     * @param array $documents
     * @param string $primaryKey Name if primary key field, e.g. 'id'.
     */
    public function addOrUpdateDocuments($documents, $primaryKey) {
        $this->Index->updateDocuments($documents, $primaryKey);
    }
    
    /**
     * @param int|array $ids Single document id or an array of such ids.
     */
    public function deleteDocuments($ids) {
        $this->Index->deleteDocuments((array)$ids);
    }
    
    public function deleteAllDocuments() {
        $this->Index->deleteAllDocuments();
    }
    
    /**
     * @param string $keywords
     * @param array $options For available see https://docs.meilisearch.com/reference/api/search.html
     * 
     * @return MeiliSearch\Search\SearchResult
     */
    public function search($keywords, $options = array()) {
        return $this->Index->search($keywords, $options);
    }
    
    /**
     * Converts provided string to a fuzzy string allowing some search typo resistance.
     * E.g. string "Harry's whiskey" is converted to "hary's whiskey harri's vhiskei hari's vhiskei".
     * Only fuzzy variations which differs from normalized form of original string are included.
     * 
     * @param string $string
     * 
     * @return string
     */
    public static function getFuzzyString($string) {
        // normalize string
        $string = Sanitize::nonAscii($string);
        $string = mb_strtolower($string);
        $string = preg_replace('/[\s\n]+/', ' ', $string);
        // remove double characters
        $noDoubleCharsString = '';
        $stringLength = mb_strlen($string);
        $previousChar = '';
        for ($i = 0; $i < $stringLength; $i++) {
            $char = mb_substr($string, $i, 1);
            if ($char !== $previousChar) {
                $noDoubleCharsString .= $char;
            }
            $previousChar = $char;
        }
        // replace w, y by v, i
        $wyToViString = str_replace(
            array('w', 'y'),
            array('v', 'i'),
            $string
        );
        // replace w, y by v, i in no double characters string
        $wyToViNoDoubleCharsString = str_replace(
            array('w', 'y'),
            array('v', 'i'),
            $noDoubleCharsString
        );
        $fuzzyString = '';
        if ($string !== $noDoubleCharsString) {
            $fuzzyString .= ' ' . $noDoubleCharsString;
        }
        $fuzzyString = trim($fuzzyString);
        if ($string !== $wyToViString) {
            $fuzzyString .= ' ' . $wyToViString;
        }
        $fuzzyString = trim($fuzzyString);
        if (
            $string !== $noDoubleCharsString
            && $noDoubleCharsString !== $wyToViNoDoubleCharsString
        ) {
            $fuzzyString .= ' ' . $wyToViNoDoubleCharsString;
        }
        $fuzzyString = trim($fuzzyString);
        return $fuzzyString;
    }   
}
