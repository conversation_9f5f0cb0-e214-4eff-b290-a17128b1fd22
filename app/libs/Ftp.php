<?php
/**
 * OO wrapper for php FTP functionality
 * 
 * Functionality here was copied/transfered from PEAR package Net_FTP 1.3.7 
 * (see http://pear.php.net/package/Net_FTP/download/1.3.7)
 * 
 * 
 * Basic usage:
 * 
 * App::loadLib('App', 'Ftp');
 * // create new instance and connect (connection handle is stored in cache to optimize
 * // work with the same FTP account on diferent places of project
 * $Ftp = new Ftp('ftp.host.my', array(
 *     'username' => 'myusername',
 *     'password' => 'mypassword',
 * ));
 * $Ftp->put('/app/tmp/my_local_test_file.txt', '/');
 * $Ftp->putContents('My testing contents', '/my_remote_test.txt');
 * $Ftp->get('/my_remote_test.txt', '/app/tmp');
 * $contents = $Ftp->getContents('/my_remote_test.txt');
 * // after explicit closure the the connection is removed also from cache
 * // If you woulf like to keep it in cache do not call this method and all
 * // created FTP connection handles will be closed in shutdown function
 * $Ftp->close(); 
 */
class Ftp {
    
    /**
     * FTP host
     * 
     * @var string 
     */
    protected $host = null;
        
    /**
     * FTP port
     * Defaults to 21.
     * 
     * @var int 
     */
    protected $port = 21;
    
    /**
     * FTP timeout for all subsequent network operations
     * Defaults to 90.
     * 
     * @var int 
     */
    protected $timeout = 90;
    
    /**
     * FTP login username. 
     * Defaults to NULL.
     * 
     * @var string 
     */
    protected $username = null;
    
    /**
     * FTP login password. 
     * Defaults to NULL.
     * 
     * @var string 
     */
    protected $password = null;
    
    /**
     * Is FTP connection make over encrypted SSL protocol. 
     * Defaults to FALSE.
     * 
     * @var bool
     */
    protected $ssl = false;
    
    /**
     * FTP connection transfer mode. Possible values are 'default', 'active', 'passive'.
     * Defaults to 'default'.
     * 
     * NOTE: This is different from 'mode' option of Ftp::get()/put()/getContents()/putContents() methods. 
     * 
     * @var string
     */
    protected $mode = 'default';
    
    //
    // INTERNAL PROPERTIES
    //
    
    /**
     * Cache of created ftp connections
     *
     * @var array 
     */
    static protected $connections = array();
    
    /**
     * Has been the Ftp::closeAll() registered as shudown handler already?
     * 
     * @var bool 
     */
    static protected $shutDownHandlerRegistered = false;
    
    
    /**
     * Sets ftp connection handle for current instance
     * 
     * @param resource $connection
     */
    protected function setConnection($connection) {
        $username = $this->username ? $this->username : '_anonymous';
        $ssl = $this->ssl ? 'ssl' : 'plain';
        self::$connections[$this->host][$username][$ssl] = $connection;
    }
    
    /**
     * Gets ftp connection handle for current instance
     * 
     * @return resource
     */
    protected function getConnection() {
        $username = $this->username ? $this->username : '_anonymous';
        $ssl = $this->ssl ? 'ssl' : 'plain';
        if (empty(self::$connections[$this->host][$username][$ssl])) {
            return null;
        }
        return self::$connections[$this->host][$username][$ssl];
    }
    
    /**
     * Cloase all existing FTP connections.
     * Set as shutdown handler in construct
     */
    static public function closeAll() {
        foreach (self::$connections as $host => $users) {
            foreach ($users as $user => $encryptions) {
                foreach ($encryptions as $encryption => $connection) {
                    if ($connection) {
                        @ftp_close($connection);
                    }
                    unset(self::$connections[$host][$user][$encryption]);
                }
            }
        }
    }
    
    /**
     * Opens a FTP connection.
     * 
     * @param string $host
     * @param array $options Following options are available:
     *      - 'port' (int) Defaults to 21.
     *      - 'timeout' (int) Defaults to 90.
     *      - 'username' (string) Username of FTP login. Login is done only if both
     *          'username' and 'password' are set. Defaults to NULL.
     *      - 'password' (string) Password of FTP login. Login is done only if both
     *          'username' and 'password' are set. Defaults to NULL.
     *      - 'ssl' (bool) If TRUE then SSL connection is opened. Defaults to FALSE.
     *      - 'mode' (string) FTP connection transfer mode. Possible values are 
     *          'default', 'active', 'passive'. This is different from 'mode' option
     *          of Ftp::get()/put()/getContents()/putContents() methods. Defaults to 'default'.
     *      - 'connect' (bool) If TRUE then FTP connection is opened on new instance creation. 
     *          Defaults to TRUE.
     * 
     * @todo SSL makes a problems, e.g. with ftp_put(): Illegal PORT command
     * 
     * @throws Exception on failure
     */
    public function __construct($host, $options = array()) {
        $defaults = array(
            'port' => $this->port,
            'timeout' => $this->timeout,
            'username' => $this->username,
            'password' => $this->password,
            'ssl' => $this->ssl, 
            'mode' => $this->mode, 
            'connect' => true,
        );
        $options = array_merge($defaults, $options);
        
        // set properties
        $this->host = $host;
        $this->port = $options['port'];
        $this->timeout = $options['timeout'];
        $this->username = $options['username'];
        $this->password = $options['password'];
        $this->ssl = $options['ssl'];
        $this->mode = $options['mode'];
        
        // register shutdown handler if not yet
        if (!self::$shutDownHandlerRegistered) {
            register_shutdown_function(array('Ftp', 'closeAll'));
            self::$shutDownHandlerRegistered = true;
        }
        
        // make the connection if required
        if ($options['connect']) {
            $this->connect($host, $options);
        }
    }
    
    /**
     * Opens the FTP connection.
     * FTP connections are cached in static property to optimize work over 
     * the same FTP account.
     * 
     * @throws Exception on failure
     */
    public function connect() {
        if (!$this->getConnection()) {
            if (!$this->ssl) {
                $connection = @ftp_connect($this->host, $this->port, $this->timeout);
            }
            else {
                $connection = @ftp_ssl_connect($this->host, null, $this->timeout);
            }
            if (!$connection) {
                throw new Exception('Connection to host failed');
            }
            if ($this->username !== null && $this->password !== null) {
                if (!@ftp_login($connection, $this->username, $this->password)) {
                    throw new Exception('Login failed');
                }
            }
            if ($this->mode === 'passive') {
                if (!@ftp_pasv($connection, true)) {
                    throw new Exception('Passive mode unavailable');
                }
            }
            elseif ($this->mode === 'active') {
                if (!@ftp_pasv($connection, false)) {
                    throw new Exception('Active mode unavailable');
                }
            }
            // store connection in the cache
            $this->setConnection($connection);
        }
    }
    
    /**
     * Closes the FTP connection
     * 
     * @throws Exception on failure
     */
    public function close() {
        if (!($connection = $this->getConnection())) {
            return;
        }
        if (!@ftp_close($connection)) {
            throw new Exception('Ftp close has failed');
        }
        // set the connection handle to null and remove it from cache
        $this->setConnection(null);
    }
    
    /**
     * Resolves the given remote path in to absolute path by prepending it by 
     * the current remote directory.
     *
     * @param string $path The remote path relative to actual ftp directory
     *
     * @return string The absolute remote path
     */
    public function getAbsoluteRemotePath($path) {
        return rtrim(@ftp_pwd($this->getConnection()), '/') . '/' . trim($path, '/');
    }
    
    /**
     * Resolves file transfer mode according to its extension
     * 
     * @param string $file File to be transferred
     * 
     * @return int FTP_ASCII or FTP_BINARY. If the file extension is not recognized
     *      the FTP_BINARY is returned as default.
     */
    public function getTransferMode($file) {
        $fileInfo =  File::getPathinfo($file);
        $extension = strtolower($fileInfo['extension']);
        switch ($extension) {
            case 'txt':
            case 'csv':
            case 'log':
                return FTP_ASCII;
                break;
            default:
                return FTP_BINARY;
                break;
        }
    }
    
    /**
     * This function will download a file from the ftp server.
     * 
     * @param string $remoteFile Remote file path relative to actual ftp directory.
     * @param string $localDir  App root relative path of local dir to store the
     *      remote file into.
     * @param array $options Following options are available:
     *      - 'name' (string) Explicit name of local file. Defaults to NULL - means
     *          that the remote filename will be used.
     *      - 'overwrite' (bool) If TRUE then local file will be overwritten if it exists.
     *          Defaults to FALSE.
     *      - 'mode' (int) FTP file transfer mode, either FTP_ASCII or FTP_BINARY.
     *          Defaults to NULL - means it is set by Ftp::getTransferMode()
     *      - 'nonBlocking' (bool) If TRUE then non blocking functionality is used 
     *          preferable if it is implemented. Defaults to FALSE.
     * 
     * @throws Exception on failure
     */
    function get($remoteFile, $localDir, $options = array()) {
        $defaults = array(
            'name' => null,
            'overwrite' => false,
            'mode' => null,
            'nonBlocking' => false,
        );
        $options = array_merge($defaults, $options);
        if (!isset($options['mode'])) {
            $options['mode'] = $this->getTransferMode($remoteFile);
        }
        // use remote file name if no explicit local name is provided
        if (!$options['name']) {
            $remoteFileParts = File::getPathinfo($remoteFile);
            $options['name'] = $remoteFileParts['basename'];
        }
        // sanitize name
        $options['name'] = Sanitize::filename($options['name']);
        $localFile = rtrim($localDir, DS) . DS . $options['name'];
        
        // set absolut path to files
        $remoteFile = $this->getAbsoluteRemotePath($remoteFile);
        $localFile = File::getAbsolutePath($localFile);

        if (@file_exists($localFile) && !$options['overwrite']) {
            throw new Exception(sprintf("Local file '%s' exists and may not be overwriten.", $localFile));
        }
        if (
            @file_exists($localFile) 
            && !@is_writeable($localFile) 
            && $options['overwrite']
        ) {
            throw new Exception(sprintf("Local file '%s' is not writeable. Can not overwrite.", $localFile));
        }

        $connection = $this->getConnection();
        if ($options['nonBlocking'] && @function_exists('ftp_nb_get')) {
            $res = @ftp_nb_get($connection, $localFile, $remoteFile, $options['mode']);
            while ($res == FTP_MOREDATA) {
                $res = @ftp_nb_continue($connection);
            }
        } 
        else {
            $res = @ftp_get($connection, $localFile, $remoteFile, $options['mode']);
        }
        
        if (!$res) {
            throw new Exception(sprintf("File '%s' could not be downloaded to '%s'.", $remoteFile, $localFile));
        }
    }
    
    /**
     * This function will upload a file to the ftp server.
     * 
     * @param string $localFile App root relative path of local file to be uploaded
     *      to ftp server.
     * @param string $remoteDir Remote dir to store the local file. It must be relative 
     *      to actual ftp directory. Use '/' to upload the file to actual ftp directory.
     * @param array $options Following options are available:
     *      - 'name' (string) Explicit name of remote file. Defaults to NULL - means
     *          that the local filename will be used.
     *      - 'overwrite' (bool) If TRUE then remote file will be overwritten if it exists.
     *          Defaults to FALSE.
     *      - 'mode' (int) FTP file transfer mode, either FTP_ASCII or FTP_BINARY.
     *          Defaults to NULL - means it is set by Ftp::getTransferMode()
     *      - 'nonBlocking' (bool) If TRUE then non blocking functionality is used 
     *          preferable if it is implemented. Defaults to FALSE.
     *      - 'remoteServerDirSeparator' (string) directory separator on remote server. If remote FTP server is
     *          based on Microsoft technology, separator can be \
     *          Defaults to /
     * 
     * @throws Exception on failure
     */
    function put($localFile, $remoteDir, $options = array()) {
        $defaults = array(
            'name' => null,
            'overwrite' => false,
            'mode' => null,
            'nonBlocking' => false,
            'remoteServerDirSeparator' => '/',
        );
        $options = array_merge($defaults, $options);
        if (!isset($options['mode'])) {
            $options['mode'] = $this->getTransferMode($localFile);
        }
        // use local file name if no explicit remote name is provided
        if (!$options['name']) {
            $localFileParts = File::getPathinfo($localFile);
            $options['name'] = $localFileParts['basename'];
        }
        // sanitize name
        $options['name'] = Sanitize::filename($options['name']);
        $remoteFile = rtrim($remoteDir, $options['remoteServerDirSeparator']) . $options['remoteServerDirSeparator'] . $options['name'];
        // set absolut path to files
        $localFile = File::getAbsolutePath($localFile);
        $remoteFile = $this->getAbsoluteRemotePath($remoteFile);

        if (!@file_exists($localFile)) {
            throw new Exception(sprintf("Local file '%s' does not exist.", $localFile));
        }
        $connection = $this->getConnection();
        if (
            (@ftp_size($connection, $remoteFile) != -1) 
            && !$options['overwrite']
        ) {
            throw new Exception(sprintf("Remote file '%s' exists and may not be overwriten.", $remoteFile));
        }

        if (function_exists('ftp_alloc')) {
            ftp_alloc($connection, filesize($localFile));
        }
        if ($options['nonBlocking'] && function_exists('ftp_nb_put')) {
            $res = @ftp_nb_put($connection, $remoteFile, $localFile, $options['mode']);
            while ($res == FTP_MOREDATA) {
                $res = @ftp_nb_continue($connection);
            }
        } 
        else {
            $res = @ftp_put($connection, $remoteFile, $localFile, $options['mode']);
        }
        if (!$res) {
            throw new Exception(sprintf("File '%s' could not be uploaded to '%s'.", $localFile, $remoteFile));
        } 
    }   
    
    /**
     * This function will get contents of remote ftp file.
     * 
     * @param string $remoteFile Remote file to get the contens of. Its path 
     *      must be relative to actual ftp directory.
     * @param array $options Following options are available:
     *      - 'mode' (int) FTP file transfer mode, either FTP_ASCII or FTP_BINARY.
     *          Defaults to NULL - means it is set by Ftp::getTransferMode()
     *      - 'nonBlocking' (bool) If TRUE then non blocking functionality is used 
     *          preferable if it is implemented. Defaults to FALSE.
     * 
     * @throws Exception on failure
     */
    function getContents($remoteFile, $options = array()) {
        $defaults = array(
            'mode' => null,
            'nonBlocking' => false,
        );
        $options = array_merge($defaults, $options);
        
        // get local tmp file
        $tmpFilename = File::getUniqueName(uniqid('ftpTmp'), File::getRelativePath(TMP));
        $localFile = TMP . DS . $tmpFilename;
        $options['name'] = $tmpFilename;
        
        // download the remote file into the local tmp file
        $this->get($remoteFile, File::getRelativePath(TMP), $options);
        
        // read the local tmp file contents
        $contents = file_get_contents($localFile);
        
        unlink($localFile);
        
        if ($contents === false) {
            throw new Exception(sprintf("Reading of local tmp file '%s' has failed.", $localFile));
        }
        
        return $contents;
    }
    
    /**
     * This function will put given contents to remote ftp file.
     * 
     * @param string $contents Contents to be used to update remote file.
     * @param string $remoteFile Remote file to store the contens into. Its path 
     *      must be relative to actual ftp directory.
     * @param array $options Following options are available:
     *      - 'overwrite' (bool) If TRUE then remote file will be overwritten if it exists.
     *          Defaults to FALSE.
     *      - 'mode' (int) FTP file transfer mode, either FTP_ASCII or FTP_BINARY.
     *          Defaults to NULL - means it is set by Ftp::getTransferMode()
     *      - 'nonBlocking' (bool) If TRUE then non blocking functionality is used 
     *          preferable if it is implemented. Defaults to FALSE.
     * 
     * @throws Exception on failure
     */
    function putContents($contents, $remoteFile, $options = array()) {
        $defaults = array(
            'overwrite' => false,
            'mode' => null,
            'nonBlocking' => false,
        );
        $options = array_merge($defaults, $options);
        if (!isset($options['mode'])) {
            $options['mode'] = $this->getTransferMode($remoteFile);
        }
        
        // save the string into local tmp file
        $tmpFilename = File::getUniqueName(uniqid('ftpTmp'), File::getRelativePath(TMP));
        $localFile = TMP . DS . $tmpFilename;
        $localFileRelative = File::getRelativePath($localFile);
        if (file_put_contents($localFile, $contents) === false) {
            throw new Exception(sprintf("Creation of local tmp file '%s' has failed.", $localFile));
        }
        
        // find out the name of remote file and remote dir
        $remoteFileParts = File::getPathinfo($remoteFile);
        $options['name'] = Sanitize::filename($remoteFileParts['basename']);
        $remoteDir = $remoteFileParts['dirname'];
        
        // upload the tmp file to ftp server remote file
        try {
            $this->put($localFileRelative, $remoteDir, $options);
        }
        catch (Throwable $e) {
            unlink($localFile);
            throw $e;
        }
        
        // delete local tmp file
        unlink($localFile);
    }
}