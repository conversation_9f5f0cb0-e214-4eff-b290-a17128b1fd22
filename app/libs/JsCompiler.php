<?php
class JsCompiler {
    
    /**
     * Js to be compiled provided either as literal js code or as url link
     * to the js script. It should be provided in form like:
     * 
     *      array(
     *          '/app/js/my.js',
     *          'http://maps.googleapis.com/maps/api/js?v=3&sensor=false'
     *          array('code' => 'alert("Hello world")'),
     *          array('url' => '/app/js/your.js'),
     *      )
     * 
     * 
     * @var array
     */
    public $js = array();
    
    public function __construct($options = array()) {
        $defaults = array(
            'js' => array(),
            'level' => 'simple', // 'whitespace', 'simple', 'advanced'
            'externs' => array(),
        );
        $options = array_merge($defaults, $options);
        
        $this->setPropertyJs($options['js']);
    }
    
    public function compile() {
        
    }
}
