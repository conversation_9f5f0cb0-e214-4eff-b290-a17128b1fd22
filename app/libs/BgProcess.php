<?php
class BgProcess {
    
    
    protected $table = 'bg_processes';
    
    protected $schema = array(
        // process properties
        'id' => array('type' => 'int', 'index' => 'primary', 'autoIncrement' => true),
        'processId' => array('type' => 'varchar', 'index' => 'unique'),
        'url' => array('type' => 'text', 'default' => null),
        'method' => array('type' => 'enum', 'values' => array('get', 'post'), 'default' => null),
        'params' => array('type' => 'text', 'default' => null, 'comment' => 'Json encoded array of params to be passed to process url'),
        'launches' => array('type' => 'int', 'default' => 0, 'comment' => 'Number of process (re)launches'),
        'summary' => array('type' => 'text', 'default' => null, 'comment' => 'Actual or final process state can be summarized here'),
        // process html output
        'output' => array('type' => 'text', 'default' => null, 'Process html output'),
        // process times
        'startMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'Microtime when the process was created'),
        'startDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime when the process was created'),
        'touchMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'A kind of live-man control - microtime when the process was touched last time'),
        'touchDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'A kind of live-man control - datetime when the process was touched last time'),
        'pauseMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'Microtime when the process was paused'),
        'pauseDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'Microtime when the process was paused'),
        'endMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'Microtime when the process was finished'),
        'endDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'Microtime when the process was finished'),
        'loopMicrotimeReserve' => array('type' => 'int', 'default' => 5000),
        'loopStartMicrotime' => array('type' => 'double', 'default' => null),
        'loopMaxMicrotime' => array('type' => 'double', 'default' => null),
        // process flags
        'isLaunched' => array('type' => 'bool', 'default' => 0),
        'isPaused' => array('type' => 'bool', 'default' => 0),
        'isCanceled' => array('type' => 'bool', 'default' => 0),
        'isFinished' => array('type' => 'bool', 'default' => 0),
        'isDeleted' => array('type' => 'bool', 'default' => 0),
        // process progress
        'progress' => array('type' => 'text', 'default' => null, 'comment' => 'Json encoded array of process progress params. Content is up to user. Should be relatively small'),
        'progressUpdateMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'Microtime of last data update'),
        'progressUpdateDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime of last data update'),
        // process data
        'data' => array('type' => 'longtext', 'default' => null, 'comment' => 'Json encoded array of process data. Content is up to user. Can be huge.'),
        'dataUpdateMicrotime' => array('type' => 'double', 'default' => null, 'comment' => 'Microtime of last data update'),
        'dataUpdateDatetime' => array('type' => 'datetime', 'default' => null, 'comment' => 'Datetime of last data update'),
    );
    
    protected $id = null;
    
    protected $processId = null;
    
    protected $process = array();
    
    protected function getMicrotime() {
        return round(1000 * (microtime(true)), 2);
    }
    
    protected function getDatetime() {
        return date('Y-m-d H:i:s');
    }
        
    /**
     * Creates a DB record for a new background process.
     * 
     * @param string $processId 
     * @param array $options Process options
     * 
     * @return BgProcess Instance of new created background process
     */
    static public function create($processId, $options = array()) {
        return new self($processId, true, $options);
    }
    
    /**
     * Connects to DB record of an existing background process.
     * 
     * @param string $processId
     * 
     * @return BgProcess Instance of connected background process
     */
    static public function connect($processId) {
        return new self($processId);
    }
    
    /**
     * 
     * @param string $processId
     * @param bool $create
     * @param array $options
     * @throws Exception if trying create process with id of alread existing process
     */
    protected function __construct($processId, $create = false, $options = array()) {
        $this->processId = $processId;
        if ($create) {
            // create the process file
            $microtime = $this->getMicrotime();
            $datetime = $this->getDatetime();
            $defaults = array(
                'url' => null,
                'method' => 'get',
                'params' => array(),
                'loopMicrotimeReserve'=> 5000,
                'loopStartMicrotime' => 0,
                'loopMaxMicrotime' => 0,
                'launches' => 0,
                'startMicrotime' => $microtime,
                'startDatetime' => $datetime,
                'isFinished' => false,
                'isCanceled' => false,
                'isDeleted' => false,
            );
            $this->process = array_merge($defaults, $options);
            $this->process['processId'] = $processId;
            if (!$this->save()) {
                throw new Exception("Creation of process '{$processId}' has failed");
            }
            $this->id = $this->getPropertyId();
        }
        $this->load();
    }
    
    /**
     * Loads process data from database
     * 
     * @return array Loaded process data
     * @throws Exception on invalid process id (unexisting process)
     */
    public function load() {
        $fields = array_keys($this->schema);
        $fields = array_flip($fields);
        unset($fields['data']);
        unset($fields['progress']);
        $fields = array_flip($fields);
        $process = DB::select($this->table, array(
            'fields' => $fields,
            'conditions' => array('processId' => $this->processId),
            'first' => true,
        ));
        if (!$process) {
            throw new Exception("Process with id '{$this->processId}' does not exist");
        }
        // normalize
        $process['params'] = json_decode($process['params'], true);
        $process['isLaunched'] = (bool)$process['isLaunched'];
        $process['isPaused'] = (bool)$process['isPaused'];
        $process['isCanceled'] = (bool)$process['isCanceled'];
        $process['isFinished'] = (bool)$process['isFinished'];
        $process['isDeleted'] = (bool)$process['isDeleted'];
        $this->process = $process;
        $this->id = $process['id'];
        
        return $this->process;
    }
    
    public function get($property = null) {
        if (is_string($property)) {
            return @$this->process[$property];
        }
        return $this->process;
    }
    
    /**
     * 
     * @param array $data Optional. Process data to be saved together with process.s
     * @return bool
     */
    public function save($options = array()) {        
        $this->process = array_merge($this->process, $options);
        $microtime = $this->getMicrotime();
        $datetime = $this->getDatetime();
        $this->process['touchMicrotime'] = $microtime;
        $this->process['touchDatetime'] = $datetime;
        $process = $this->process; 
        $process['params'] = json_encode($process['params']);
        $process['processId'] = $this->processId;
        $fields = array_keys($this->schema);
        $fields = array_flip($fields);
        unset($fields['id']);
        $fields = array_flip($fields);
        // save
        if (empty($this->id)) {
            $result = DB::insert(
                $this->table, 
                $process,
                array(
                    'allowFields' => $fields,
                )
            );
        }
        else {
            $result = DB::update(
                $this->table, 
                $process, 
                array(
                    'conditions' => array('id' => $this->id),
                    'allowFields' => $fields,
                )
            );
        }
        return $result !== false;
    }
    
    /**
     * 
     * 
     * @return bool
     */
    public function touch() {
        $result = DB::update(
            $this->table, 
            array(
                'touchMicrotime' => $this->getMicrotime(),
                'touchDatetime' => $this->getDatetime(),
            ), 
            array('conditions' => array('id' => $this->id))
        );
        return $result;
    }
        
    /**
     * Sets the pused process properties
     * 
     * @param array $options Optional.
     * @return bool success
     */
    public function pause($options = array()) {
        $this->process['isPaused'] = true;
        $this->process['pauseMicrotime'] = $this->getMicrotime();
        $this->process['pauseDatetime'] = $this->getDatetime();
        return $this->save($options);
    }
    
    /**
     * Sets the ending process properties
     * 
     * @param array $options Optional.
     * @return bool success
     */
    public function finish($options = array()) {
        $this->process['isFinished'] = true;
        $this->process['endMicrotime'] = $this->getMicrotime();
        $this->process['endDatetime'] = $this->getDatetime();
        return $this->save($options);
    }

    /**
     * Sets the ending process properties
     * 
     * @param array $options Optional.
     * @return bool success
     */
    public function delete($options = array()) {
        $this->process['isDeleted'] = true;
        $this->process['data'] = null;
        $this->process['dataUpdateMicrotime'] = null;
        $this->process['dataUpdateDatetime'] = null;
        return $this->save($options);
    }

    /**
     * 
     * @param array $data
     * @return bool
     * @throws Exception if data cannot be json encoded
     */
    public function saveData($data) {
        $data = json_encode($data);
        if ($data === false) {
            throw new Exception("Data encoding to json format has failed");
        }
        $microtime = $this->getMicrotime();
        $datetime = $this->getDatetime();
        $result = DB::update(
            $this->table, 
            array(
                'data' => $data,
                'dataUpdateMicrotime' => $microtime,
                'dataUpdateDatetime' => $datetime,
                'touchMicrotime' => $microtime,
                'touchDatetime' => $datetime,
            ), 
            array('conditions' => array('id' => $this->id))
        );
        // keep the process actual
        $this->process['dataUpdateMicrotime'] = $microtime;
        $this->process['dataUpdateDatetime'] = $datetime;
        $this->process['touchMicrotime'] = $microtime;
        $this->process['touchMicrotime'] = $datetime;
        return $result;
    }
    
    /**
     * 
     * @return array Process data
     * @throws Exception on invalid process
     */
    public function loadData() {
        $process = DB::select($this->table, array(
            'conditions' => array('id' => $this->id),
            'fields' => array('data'),
            'first' => true,
        ));
        return json_decode(@$process['data'], true);        
    }
    
    /**
     * 
     * @param array $progress
     * @return bool
     * @throws Exception if progress cannot be json encoded
     */
    public function saveProgress($progress) {
        $progress = json_encode($progress);
        if ($progress === false) {
            throw new Exception("Data encoding to json format has failed");
        }
        $microtime = $this->getMicrotime();
        $datetime = $this->getDatetime();
        $result = DB::update(
            $this->table, 
            array(
                'progress' => $progress,
                'progressUpdateMicrotime' => $microtime,
                'progressUpdateDatetime' => $datetime,
                'touchMicrotime' => $microtime,
                'touchDatetime' => $datetime,
                'loopMicrotimeReserve' => $this->process['loopMicrotimeReserve'],
                'loopStartMicrotime' => $this->process['loopStartMicrotime'],
                'loopMaxMicrotime' => $this->process['loopMaxMicrotime'],
            ), 
            array('conditions' => array('id' => $this->id))
        );
        // keep the process actual
        $this->process['progressUpdateMicrotime'] = $microtime;
        $this->process['progressUpdateDatetime'] = $datetime;
        $this->process['touchMicrotime'] = $microtime;
        $this->process['touchMicrotime'] = $datetime;
        return $result;
    }
    
    /**
     * 
     * @return array Process progress
     * @throws Exception on invalid process
     */
    public function loadProgress() {
        $process = DB::select($this->table, array(
            'conditions' => array('id' => $this->id),
            'fields' => array('progress'),
            'first' => true,
        ));
        return json_decode(@$process['progress'], true);        
    }
    
    /**
     * Makes http request of given URL address and let it to run in background.
     * It does not wait for response from given URL.
     * It is up to the URL to treat max_execution_time limitations (redirection loops)
     *
     * @param array $options Options:
     *      - 'url' (string) URL string, e.g. 'http://example.com/slow.php?file=my_file.dat'
     *          It may contain also params which are always send as GET params 
     *          but these are not checked for proper ulr encoding.
     *      - 'method' (string) Http request method 'get' or 'post', Defaults to 'get'.
     *          (actually, for the moment the post is not implemented)
     *      - 'params' (array) Array of params to be send by request either as 
     *          GET or POST params (according to the method) and they are 
     *          properly urlencoded. Defaults to array()
     * 
     * @return bool TRUE if given $url was succesfully launched. 
     * 
     * @throws Exception on undefined process url
     */
    public function launch($options = array()) {
        $this->process = array_merge($this->process, $options);
        if (empty($this->process['url'])) {
            throw new Exception("Process '{$this->processId}' cannot be launched as no 'url' is defined");
        }
        // save the process before launching
        $this->process['isPaused'] = false;
        $this->process['isLaunched'] = true;
        $this->process['launches']++;
        $this->save();
        
//// using cURL (for the moment just get requests)   
        // prepare params string
        $params = $this->process['params'];
        // - add process id as launched process param
        $params['processId'] = $this->processId;
        foreach ($params as $key => &$val) {
            if (is_array($val)) {
                $val = implode(',', $val);
            }
            $val = $key . '=' . urlencode($val);
        }
        unset($val);
        $params = implode('&', $params);
        
        // parse url
        $urlParts = explode('?', $this->process['url']);
        $url = $urlParts[0];
                
        // find out get params
        $getParams = @$urlParts[1];
        if (!empty($params)) {
            if (!empty($getParams)) {
                $getParams = rtrim($getParams, '&') . '&';
            }
            $getParams .= $params;
        }
        // attach them to url
        if ($getParams) {
            $url = $url . '?' . $getParams;
        }
        
        // make request
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        //curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
        //curl_setopt($ch, CURLOPT_POSTFIELDS, $post_string); // use this to implement post params
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'curl');
        curl_setopt($ch, CURLOPT_TIMEOUT, 1); // delay 1s on each call  
        //curl_setopt($ch, CURLOPT_TIMEOUT_MS, 1); // delay 1ms on each call (this doesnt work on my localhost, works only from PHP 5.2, and cURL 1.7...)
        curl_exec($ch);
        curl_close($ch);   
        
        return true;
    }
    
    /**
     * Relaunches the url of existing process.
     * 
     * NOTE:
     * Very probably exit(); should be called after this method if the process 
     * is succesfully relaunched
     *  
     * @return bool TRUE if the process on success otherwise FALSE
     */
    public function relaunch() {
        return $this->launch(array('loopStartMicrotime' => 0));
    }
    
    public function isCanceled() {
        $process = DB::select($this->table, array(
            'fields' => array('isCanceled'), 
            'conditions' => array('id' => $this->id),
            'first' => true
        ));
        $this->process['isCanceled'] = (bool)@$process['isCanceled'];
        return $this->process['isCanceled'];
    }   
    
    public function isPaused() {
        $process = DB::select($this->table, array(
            'fields' => array('isPaused'), 
            'conditions' => array('id' => $this->id),
            'first' => true
        ));
        $this->process['isPaused'] = (bool)@$process['isPaused'];
        return $this->process['isPaused'];
    }   
    
    //
    // LOOPING METHODS
    // 
    // Bg processes are very oftenly used for long processes running in loops.
    // This implies time checking and redirection technique which must be used 
    // to overcome php setting max_ececution_time.
    // 
    // Following methods serves to facilitate this
    
    /**
     * Resets the loop timer. 
     * This method should be used before each loop which uses method BgProcess::checkLoopTime()
     */
    public function resetLoopTime() {
        $this->process['loopStartMicrotime'] = null;
    }
    
    /** 
     * Checks if there is enought time to do another loop or not.
     * 
     * This method should be placed at the begining of loop like this:
     *      
     *      $Process = BgProcess::connect('my_process_id');
     *      $data = $Process->loadData();
     *      $Process->resetLoopTime();
     *      while(($row = fgets()) !== false) {
     *          if ($Process->isCanceled()) {
     *              ...
     *              $Process->finish();
     *              return;
     *          }
     *          if (!$Process->checkLoopTime()) {
     *              ...
     *              fclose($fh);
     *              $Process->save($data);
     *              $Process->relaunch();
     *              exit;
     *          }
     * 
     * 
     *      }
     *
     * @return bool Returns TRUE if another loop can be launched, otherwise FALSE
     */
    public function checkLoopTime() {
        // get the current microtime
        $microtime = $this->getMicrotime();
        // if there is no starting time defined, then it is the first loop of 
        // (re)launched  process so let it go. Store just the 'loopStartMicrotime' for next turn
        if (empty($this->process['loopStartMicrotime'])) {
            $this->process['loopStartMicrotime'] = $microtime;
            return true;
        }
        // ok the first turn is done, check the loop time
        else {
            // count the loop time
            $loopTime = $microtime - $this->process['loopStartMicrotime'];
            // actualize loopMaxMicrotime
            if ($loopTime > $this->process['loopMaxMicrotime']) {
                $this->process['loopMaxMicrotime'] = $loopTime; 
            }
            // set the loopStartMicrotime (this allows this method to make 2 in 1)
            $this->process['loopStartMicrotime'] = $microtime;
            // check if there is enough time to do another loop
            if (Utility::getFreeMicrotime() < ($this->process['loopMaxMicrotime'] + $this->process['loopMicrotimeReserve'])) {
                return false;
            }    
            return true;
        }
    }    
     
    //
    // OUTPUT BUFFERING
    // 
    // Background processes seems to get stalled if there is error (warning, notice, ...)
    // message (more preciselly sequence of two such messages according to testing)
    // That is why to ensure continuous processing of bg process:
    //  - the error reporting must be turned off (redirection to log file does not seems to be enought)
    //  or
    //  - the output buffering must be turned on (ob_start())
    //
    
    /**
     * Starts output buffering
     */
    public function bufferOutput() {
        ob_start();
        register_shutdown_function(array($this, 'saveOutput'));
    }
    
    /**
     * Save the buffered output to {processId}.output file
     * It is not necessary to call this function. 
     * The output is automatically saved at the and of process.
     */
    public function saveOutput() {
        $output = ob_get_contents();
        ob_end_clean();
        $this->process['output'] = $output;
        $this->save();
    }
    
    //
    // CONTROLL METHODS
    // 
    // This methods are used to control background process from out of process
    //
    
    public function createCanceledSignal() {
        $result = DB::update(
            $this->table, 
            array('isCanceled' => true), 
            array('conditions' => array('id' => $this->id))
        );
        return $result;
    }
    
    public function deleteCanceledSignal() {
        $result = DB::update(
            $this->table, 
            array('isCanceled' => false), 
            array('conditions' => array('id' => $this->id))
        );
        return $result;
    }
    
    public function createPausedSignal() {
        $result = DB::update(
            $this->table, 
            array('isPaused' => true), 
            array('conditions' => array('id' => $this->id))
        );
        return $result;
    }
    
    public function deletePausedSignal() {
        $result = DB::update(
            $this->table, 
            array('isPaused' => false), 
            array('conditions' => array('id' => $this->id))
        );
        return $result;
    }
       
}