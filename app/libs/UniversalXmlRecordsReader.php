<?php
App::loadLib('App', 'XmlRecordsReader');

/**
 * Allows to read any XML records without rewriting method readRecord()
 */
class UniversalXmlRecordsReader extends XmlRecordsReader {
    
    protected $recordTagName = 'item';
    
    protected $decimalPoint = '.';
    
    protected $thousandsSeparator = '';
    
    /**
     * See https://stackoverflow.com/a/15849257/1245149
     * 
     * @param SimpleXMLElement $Parent
     * @return array
     */
    protected function convertSimpleXmlElementToArray(SimpleXMLElement $Parent) {
        $array = array();
        foreach ($Parent as $name => $Element) {
            ($node = &$array[$name])
                && (1 === count($node) ? $node = array($node) : 1)
                && $node = &$node[];
            $node = $Element->count() ? $this->convertSimpleXmlElementToArray($Element) : trim($Element);
        }
        return $array;
    }
    
    /**
     * Returns associative array corresponding to XML structure of record
     * 
     * @return array
     */
    public function readRecord() {
        $XmlRecord = parent::readRecord();
        if (!$XmlRecord) {
            return null;
        }
        return $this->convertSimpleXmlElementToArray($XmlRecord);
    }
}