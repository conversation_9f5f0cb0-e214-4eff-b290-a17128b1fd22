syntax: glob
*.project
*.orig
*.rej
*.swp
*.kate-swp
*.mo
*.sublime-project
*.sublime-workspace         
*.css.map
*.js.map
*~
*.xml
app/config/database.php
tmp/*
FTF10.exe
nbproject/*
.idea/*
node_modules/*
node_modules
userfiles/*
userfiles_locale/*
userfiles_remote/*
app/modules/Core/vendors/phpthumb/cache/*
app/vendors/mpdf/ttfontdata/*
css/sass/.sass-cache/*
app/updates/*_locale.sql
tests/css
tests/js
tests/img
tests/*.html
sitemap.xml
image-sitemap.xml
app/locale/compiled/*.js
app/locale/compiled/*.php
app/modules/*/locale/compiled/*.js
app/modules/*/locale/compiled/*.php
app/css/*.map
app/css/libs/*.map
app/modules/*/css/*.map
app/modules/*/css/libs/*.map
_book/*
_book
composer.phar
vendor/*

.devcontainer/app/bash_history
.devcontainer/build.log
.devcontainer/.env
.vscode/settings.json
export/*
Project DB.session.sql

.DS_Store